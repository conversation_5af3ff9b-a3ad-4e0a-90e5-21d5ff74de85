crematorium-entity-storage-component-on-examine-details-is-burning = The {$owner} is [color=red]active[/color]!
crematorium-entity-storage-component-on-examine-details-has-contents =  The content light is [color=green]on[/color], there's something in here.
crematorium-entity-storage-component-on-examine-details-empty = The content light is off, there's nothing in here.
crematorium-entity-storage-component-is-cooking-safety-message = Safety first, not while it's active!
crematorium-entity-storage-component-suicide-message = You cremate yourself!
crematorium-entity-storage-component-suicide-message-others = {$victim} is cremating {$victim}!

# CremateVerb
cremate-verb-get-data-text = Cremate