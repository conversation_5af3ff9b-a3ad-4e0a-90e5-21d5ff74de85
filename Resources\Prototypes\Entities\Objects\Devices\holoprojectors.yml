- type: entity
  parent: BaseItem
  id: Holoprojector
  name: "голографічний проектор"
  description: "Зручний голографічний проектор, який відображає знак прибиральника."
  components:
  - type: Item
    storedRotation: -90
  - type: HolosignProjector
  - type: UseDelay
  - type: ContainerContainer
    containers:
      cell_slot: !type:ContainerSlot {}
  - type: PowerCellSlot
    cellSlotId: cell_slot
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellMedium
  - type: Sprite
    sprite: Objects/Devices/Holoprojectors/custodial.rsi
    state: icon
  - type: Tag
    tags:
      - HolosignProjector
  - type: MeleeWeapon
    wideAnimationRotation: 90
    attackRate: 0.8
    damage:
      types:
        Blunt: 6.5
    bluntStaminaDamageFactor: 2
    heavyRateModifier: 0.9
    maxTargets: 1
    angle: 20
    soundHit:
      collection: MetalThud
  - type: DamageOtherOnHit

- type: entity
  parent: Holoprojector
  id: HoloprojectorEmpty
  suffix: Empty
  components:
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default

- type: entity
  parent: Holoprojector
  id: HoloprojectorBorg
  suffix: borg
  components:
  - type: HolosignProjector
    chargeUse: 240
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellMicroreactor
        disableEject: true
        swap: false

- type: entity
  parent: Holoprojector
  id: HolofanProjector
  name: "голофазний проектор"
  description: "Не дайте пасажирам-самогубцям вбити всіх під час надзвичайних ситуацій в атмосфері."
  components:
  - type: HolosignProjector
    signProto: HoloFan
    chargeUse: 120
  - type: Sprite
    sprite: Objects/Devices/Holoprojectors/atmos.rsi
    state: icon
  - type: Tag
    tags:
      - HolofanProjector
  - type: StaticPrice
    price: 80
  - type: ReverseEngineering # Nyano
    difficulty: 3
    recipes:
      - HolofanProjector

- type: entity
  parent: HolofanProjector
  id: HolofanProjectorEmpty
  suffix: Empty
  components:
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default

- type: entity
  parent: Holoprojector
  id: HoloprojectorField
  name: "проектор силового поля"
  description: "Створює непрохідне силове поле, яке нічого не пропускає. Близьке сусідство може викликати, а може і не викликати рак."
  components:
    - type: HolosignProjector
      signProto: HolosignForcefield
      chargeUse: 120
    - type: Sprite
      sprite: Objects/Devices/Holoprojectors/field.rsi
      state: icon
    - type: Tag
      tags:
        - HolofanProjector
    - type: StaticPrice
      price: 130
    - type: ReverseEngineering # Nyano
      difficulty: 3
      recipes:
        - HoloprojectorField

- type: entity
  parent: HoloprojectorField
  id: HoloprojectorFieldEmpty
  suffix: Empty
  components:
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default

- type: entity
  parent: Holoprojector
  id: HoloprojectorSecurity
  name: "голобар'єрний проектор"
  description: "Створює міцний, але крихкий голографічний бар'єр."
  components:
    - type: HolosignProjector
      signProto: HolosignSecurity
      chargeUse: 120
    - type: Sprite
      sprite: Objects/Devices/Holoprojectors/security.rsi
      state: icon
    - type: Tag
      tags:
        - HolofanProjector
    - type: StaticPrice
      price: 50

- type: entity
  parent: HoloprojectorSecurity
  id: HoloprojectorSecurityEmpty
  suffix: Empty
  components:
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
