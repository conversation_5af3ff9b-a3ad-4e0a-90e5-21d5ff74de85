- type: entity
  id: BaseGenericTabletopPiece
  parent: BaseTabletopPiece
  abstract: true
  components:
  - type: Sprite
    noRot: true
    sprite: Objects/Fun/Tabletop/generic_pieces.rsi

- type: entity
  id: RedTabletopPiece
  name: "червона фішка"
  parent: BaseGenericTabletopPiece
  components:
  - type: Sprite
    state: red

- type: entity
  id: GreenTabletopPiece
  name: "зелена фішка"
  parent: BaseGenericTabletopPiece
  components:
  - type: Sprite
    state: green

- type: entity
  id: YellowTabletopPiece
  name: "жовта фішка"
  parent: BaseGenericTabletopPiece
  components:
  - type: Sprite
    state: yellow

- type: entity
  id: BlueTabletopPiece
  name: "синя фішка"
  parent: BaseGenericTabletopPiece
  components:
  - type: Sprite
    state: blue

- type: entity
  id: WhiteTabletopPiece
  name: "біла фішка"
  parent: BaseGenericTabletopPiece
  components:
  - type: Sprite
    state: white

- type: entity
  id: BlackTabletopPiece
  name: "чорна фішка"
  parent: BaseGenericTabletopPiece
  components:
  - type: Sprite
    state: black
