﻿# Popups

network-configurator-device-saved = Successfully saved network device {$device} with address {$address}!
network-configurator-device-failed = Failed to save network device {$device}! No address assigned!
network-configurator-too-many-devices = Too many devices stored on this device!
network-configurator-update-ok = Device storage updated.
network-configurator-device-already-saved = network device: {$device} is already saved.
network-configurator-device-access-denied = Access denied!
network-configurator-link-mode-started = Started linking device: {$device}
network-configurator-link-mode-stopped = Stopped linking.
network-configurator-mode-link = Link
network-configurator-mode-list = List
network-configurator-switched-mode = Switched mode to: {$mode}

# Verbs
network-configurator-save-device = Save device
network-configurator-configure = Configure
network-configurator-switch-mode = Switch mode
network-configurator-link-defaults = Link defaults
network-configurator-start-link = Start link
network-configurator-link = Link

# ui
network-configurator-title-saved-devices = Saved Devices
network-configurator-title-device-configuration = Device Configuration
network-configurator-ui-clear-button = Clear
network-configurator-ui-count-label = {$count} Devices

# tooltips
network-configurator-tooltip-set = Sets targets device list
network-configurator-tooltip-add = Adds to targets device list
network-configurator-tooltip-edit = Edit targets device list
network-configurator-tooltip-clear = Clear targets device list
network-configurator-tooltip-copy = Copy targets device list to held tool
network-configurator-tooltip-show = Show a holographic visualization of targets device list

# examine
network-configurator-examine-mode-link = [color=red]Link[/color]
network-configurator-examine-mode-list = [color=green]List[/color]
network-configurator-examine-current-mode = Current mode: {$mode}
network-configurator-examine-switch-modes = Press {$key} to switch modes

# item status
network-configurator-item-status-label = Mode: {$mode}
    Switch: {$keybinding}
