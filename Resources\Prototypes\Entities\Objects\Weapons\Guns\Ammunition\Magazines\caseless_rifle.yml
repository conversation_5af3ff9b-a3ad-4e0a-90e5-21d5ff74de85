- type: entity
  id: BaseMagazineCaselessRifle
  name: "магазин (.25 безоболонковий)"
  parent: BaseItem
  abstract: true
  components:
  - type: Tag
    tags:
      - MagazineCaselessRifle
  - type: BallisticAmmoProvider
    mayTransfer: true
    whitelist:
      tags:
        - CartridgeCaselessRifle
    proto: CartridgeCaselessRifle
    capacity: 30
  - type: Item
    size: Small
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Magazine/CaselessRifle/caseless_rifle_mag.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 2
    zeroVisible: false
  - type: Appearance

- type: entity
  id: BaseMagazineCaselessRifleShort
  name: "безкаліберна гвинтівка з коротким магазин<PERSON><PERSON> (.25 безкаліберна)"
  parent: BaseMagazineCaselessRifle
  abstract: true
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifle
    capacity: 10
  - type: Item
    size: Tiny
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Magazine/CaselessRifle/caseless_rifle_mag_short.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: BaseMagazinePistolCaselessRifle
  name: "магазин до пістолета (калібру .25 без набоїв)"
  parent: BaseMagazineCaselessRifle
  abstract: true
  components:
  - type: Tag
    tags:
      - MagazinePistolCaselessRifle
  - type: BallisticAmmoProvider
    whitelist:
      tags:
        - CartridgeCaselessRifle
    proto: CartridgeCaselessRifle
    capacity: 10
  - type: Item
    size: Small
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Magazine/CaselessRifle/caseless_pistol_mag.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 6
    zeroVisible: false
  - type: Appearance

- type: entity
  id: MagazineCaselessRifle10x24
  name: "коробчатий магазин (.25 без гільзи)"
  parent: BaseMagazineCaselessRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifle
    capacity: 99
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Magazine/CaselessRifle/10x24.rsi

- type: entity
  id: MagazinePistolCaselessRifle
  name: "магазин до пістолета (калібру .25 без набоїв)"
  parent: BaseMagazinePistolCaselessRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifle
  - type: Sprite
    slayers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 6
    zeroVisible: false
  - type: Appearance

- type: entity
  id: MagazinePistolCaselessRiflePractice
  name: "магазин до пістолета (калібру .25) для стрільби без набоїв"
  parent: BaseMagazinePistolCaselessRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRiflePractice
  - type: Sprite
    layers:
    - state: practice
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 6
    zeroVisible: false
  - type: Appearance

- type: entity
  id: MagazinePistolCaselessRifleRubber
  name: "пістолетний магазин (.25 безгільзовий гумовий)"
  parent: BaseMagazinePistolCaselessRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifleRubber
  - type: Sprite
    layers:
    - state: rubber
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 6
    zeroVisible: false
  - type: Appearance

- type: entity
  id: MagazinePistolCaselessRifleIncendiary
  name: "пістолетний магазин (.25 безгільзовий запальний)"
  parent: BaseMagazinePistolCaselessRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifleIncendiary

- type: entity
  id: MagazinePistolCaselessRifleUranium
  name: "пістолетний магазин (.25 безгільзовий, урановий)"
  parent: BaseMagazinePistolCaselessRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifleUranium

- type: entity
  id: MagazinePistolCaselessRifleShrapnel
  name: "пістолетний магазин (.25 безгільзовий, шрапнельний)"
  parent: BaseMagazinePistolCaselessRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifleShrapnel

- type: entity
  id: MagazineCaselessRifle
  name: "магазин (.25 безоболонковий)"
  parent: BaseMagazineCaselessRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifle
  - type: Sprite
    layers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineCaselessRiflePractice
  name: "магазин (.25 тренувальний безоболонковий)"
  parent: BaseMagazineCaselessRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRiflePractice
  - type: Sprite
    layers:
    - state: practice
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineCaselessRifleRubber
  name: "магазин (.25 безгільзовий гумовий)"
  parent: BaseMagazineCaselessRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifleRubber
  - type: Sprite
    layers:
    - state: rubber
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineCaselessRifleIncendiary
  name: "магазин (.25 безгільзовий запальний)"
  parent: MagazineCaselessRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifleIncendiary

- type: entity
  id: MagazineCaselessRifleUranium
  name: "магазин (.25 безгільзовий, урановий)"
  parent: MagazineCaselessRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifleUranium

- type: entity
  id: MagazineCaselessRifleShrapnel
  name: "магазин (.25 безгільзовий, шрапнельний)"
  parent: MagazineCaselessRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifleShrapnel

- type: entity
  id: MagazineCaselessRifleShort
  name: "короткий магазин (.25 без гільзи)"
  parent: BaseMagazineCaselessRifleShort
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifle
    capacity: 20
  - type: Sprite
    layers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineCaselessRifleShortPractice
  name: "короткий журнал (.25 безсудової практики)"
  parent: BaseMagazineCaselessRifleShort
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRiflePractice
  - type: Sprite
    layers:
    - state: practice
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineCaselessRifleShortRubber
  name: "короткий магазин (.25 безгільзовий гумовий)"
  parent: BaseMagazineCaselessRifleShort
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifleRubber
  - type: Sprite
    layers:
    - state: rubber
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineCaselessRifleShortIncendiary
  name: "короткий магазин (.25 безгільзовий запальний)"
  parent: BaseMagazineCaselessRifleShort
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifleIncendiary

- type: entity
  id: MagazineCaselessRifleShortUranium
  name: "короткий магазин (.25 безгільзовий, урановий)"
  parent: BaseMagazineCaselessRifleShort
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifleUranium

- type: entity
  id: MagazineCaselessRifleShortShrapnel
  name: "короткий магазин (.25 безгільзовий, шрапнельний)"
  parent: BaseMagazineCaselessRifleShort
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifleShrapnel
