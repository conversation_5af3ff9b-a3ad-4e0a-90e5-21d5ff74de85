- type: entity
  id: TableBase
  parent: BaseStructure
  name: "таблиця"
  description: "Квадратний шматок металу, що стоїть на чотирьох металевих ніжках."
  abstract: true
  components:
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: PlaceableSurface
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.45"
        density: 55
        mask: # tables should collide with other tables
        - TableMask
        layer:
        - TableLayer
        - BulletImpassable
  - type: SpriteFade
  - type: Sprite
  - type: Icon
    state: full
  - type: IconSmooth
    key: state
    base: state_
  - type: Climbable
  - type: Bonkable
    bonkDamage:
      types:
        Blunt: 4
  - type: Clickable
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepHull
  - type: RequireProjectileTarget
  - type: ThrownItemImmune

- type: entity
  id: CounterBase
  parent: TableBase
  name: "лічильник"
  abstract: true
  components:
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.45"
        density: 55
        mask:
        - TableMask
        layer:
        - TableLayer
