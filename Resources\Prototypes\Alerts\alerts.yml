- type: alertOrder
  # Defines ordering in alert tab, higher up = higher in tab.
  # List below can contain alert type or category, if both are present the id will take precedence.
  # If item is not in list it will go at the bottom (ties broken by alert type enum value)
  id: BaseAlertOrder
  order:
    - category: Health
    - category: Mood
    - category: Stamina
    - alertType: CosmicEntropy # ImpStation
    - category: Counter #PIRATE
    - alertType: MageMana #PIRATE
    - alertType: SuitPower
    - alertType: ModsuitPower # Goobstation - Modsuits
    - category: Internals
    - alertType: Fire
    - alertType: Handcuffed
    - alertType: Ensnared
    - category: Buckled
    - alertType: Pulling
    - category: Piloting
    - alertType: Corporeal
    - alertType: Stun
    - alertType: KnockedDown # goobstation
    - category: Breathing # Vox gang not calling this oxygen
    - category: Pressure
    - alertType: Bleed
    - category: Temperature
    - category: Hunger
    - category: Thirst
    - alertType: Magboots
    - alertType: Pacified
    - alertType: Offer
    - alertType: Deflecting

- type: entity
  id: AlertSpriteView
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    layers:
    - map: [ "enum.AlertVisualLayers.Base" ]

- type: alert
  id: LowOxygen
  category: Breathing
  icons:
  - sprite: /Textures/Interface/Alerts/breathing.rsi
    state: not_enough_oxy
  name: alerts-low-oxygen-name
  description: alerts-low-oxygen-desc

- type: alert
  id: LowNitrogen
  category: Breathing
  icons:
    - sprite: /Textures/Interface/Alerts/breathing.rsi
      state: not_enough_nitro
  name: alerts-low-nitrogen-name
  description: alerts-low-nitrogen-desc

- type: alert
  id: LowPlasma
  category: Breathing
  icons:
    - sprite: /Textures/Interface/Alerts/breathing.rsi
      state: not_enough_tox
  name: alerts-low-plasma-name
  description: alerts-low-plasma-desc

- type: alert
  id: HighOxygen
  category: Breathing
  icons:
    - sprite: /Textures/Interface/Alerts/breathing.rsi
      state: too_much_oxy
  name: alerts-high-oxygen-name
  description: alerts-high-oxygen-desc

- type: alert
  id: Toxins
  category: Toxins
  icons:
  - sprite: /Textures/Interface/Alerts/breathing.rsi
    state: too_much_tox
  name: alerts-high-toxin-name
  description: alerts-high-toxin-desc

- type: alert
  id: LowPressure
  category: Pressure
  icons:
  - sprite: /Textures/Interface/Alerts/pressure.rsi
    state: lowpressure1
  - sprite: /Textures/Interface/Alerts/pressure.rsi
    state: lowpressure2
  maxSeverity: 2
  name: alerts-low-pressure-name
  description: alerts-low-pressure-desc

- type: alert
  id: PlasmaCounter
  category: Counter
  icons:
  - sprite: /Textures/Interface/Alerts/plasma_counter.rsi
    state: power_display0
  - sprite: /Textures/Interface/Alerts/plasma_counter.rsi
    state: power_display1
  - sprite: /Textures/Interface/Alerts/plasma_counter.rsi
    state: power_display2
  - sprite: /Textures/Interface/Alerts/plasma_counter.rsi
    state: power_display3
  - sprite: /Textures/Interface/Alerts/plasma_counter.rsi
    state: power_display4
  - sprite: /Textures/Interface/Alerts/plasma_counter.rsi
    state: power_display5
  - sprite: /Textures/Interface/Alerts/plasma_counter.rsi
    state: power_display6
  - sprite: /Textures/Interface/Alerts/plasma_counter.rsi
    state: power_display7
  - sprite: /Textures/Interface/Alerts/plasma_counter.rsi
    state: power_display8
  - sprite: /Textures/Interface/Alerts/plasma_counter.rsi
    state: power_display9
  - sprite: /Textures/Interface/Alerts/plasma_counter.rsi
    state: power_display10
  minSeverity: 0
  maxSeverity: 11
  name: alerts-plasma-name
  description: alerts-plasma-desc

- type: alert
  id: HighPressure
  category: Pressure
  icons:
  - sprite: /Textures/Interface/Alerts/pressure.rsi
    state: highpressure1
  - sprite: /Textures/Interface/Alerts/pressure.rsi
    state: highpressure2
  maxSeverity: 2
  name: alerts-high-pressure-name
  description: alerts-high-pressure-desc

- type: alert
  id: Fire
  icons: [ /Textures/Interface/Alerts/Fire/fire.png ]
  onClick: !type:ResistFire  { }
  name: alerts-on-fire-name
  description: alerts-on-fire-desc


- type: alert
  id: Cold
  category: Temperature
  icons:
  - sprite: /Textures/Interface/Alerts/temperature.rsi
    state: cold1
  - sprite: /Textures/Interface/Alerts/temperature.rsi
    state: cold2
  - sprite: /Textures/Interface/Alerts/temperature.rsi
    state: cold3
  maxSeverity: 3
  name: alerts-too-cold-name
  description: alerts-too-cold-desc

- type: alert
  id: Hot
  category: Temperature
  icons:
  - sprite: /Textures/Interface/Alerts/temperature.rsi
    state: hot1
  - sprite: /Textures/Interface/Alerts/temperature.rsi
    state: hot2
  - sprite: /Textures/Interface/Alerts/temperature.rsi
    state: hot3
  maxSeverity: 3
  name: alerts-too-hot-name
  description: alerts-too-hot-desc

- type: alert
  id: Weightless
  icons: [ /Textures/Interface/Alerts/Weightless/weightless.png ]
  name: alerts-weightless-name
  description: alerts-weightless-desc

- type: alert
  id: Stun
  icons:
  - sprite: /Textures/Objects/Weapons/Melee/stunbaton.rsi #Should probably draw a proper icon
    state: stunbaton_off
  name: alerts-stunned-name
  description: alerts-stunned-desc

- type: alert
  id: Handcuffed
  onClick: !type:RemoveCuffs  { }
  icons: [ /Textures/Interface/Alerts/Handcuffed/Handcuffed.png ]
  name: alerts-handcuffed-name
  description: alerts-handcuffed-desc

- type: alert
  id: Ensnared
  onClick: !type:RemoveEnsnare  { }
  icons:
  - sprite: /Textures/Interface/Alerts/ensnared.rsi
    state: ensnared
  name: alerts-ensnared-name
  description: alerts-ensnared-desc

- type: alert
  id: Buckled
  category: Buckled
  onClick: !type:Unbuckle  { }
  icons: [ /Textures/Interface/Alerts/Buckle/buckled.png ]
  name: alerts-buckled-name
  description: alerts-buckled-desc

- type: alert
  id: Offer
  onClick: !type:AcceptOffer  { }
  icons:
  - sprite: /Textures/Interface/Alerts/offer_item.rsi
    state: offer_item
  name: alerts-offer-name
  description: alerts-offer-desc

- type: alert
  id: HumanCrit
  category: Health
  icons:
  - sprite: /Textures/Interface/Alerts/human_critical.rsi
    state: critical
  name: alerts-crit-name
  description: alerts-crit-desc

- type: alert
  id: HumanDead
  category: Health
  icons:
  - sprite: /Textures/Interface/Alerts/human_dead.rsi
    state: dead
  name: alerts-dead-name
  description: alerts-dead-desc

- type: alert
  id: HumanHealth
  category: Health
  onClick: !type:CheckHealth { }
  icons:
  - sprite: /Textures/Interface/Alerts/human_alive.rsi
    state: health0
  - sprite: /Textures/Interface/Alerts/human_alive.rsi
    state: health1
  - sprite: /Textures/Interface/Alerts/human_alive.rsi
    state: health2
  - sprite: /Textures/Interface/Alerts/human_alive.rsi
    state: health3
  - sprite: /Textures/Interface/Alerts/human_alive.rsi
    state: health4
  name: alerts-health-name
  description: alerts-health-desc
  minSeverity: 0
  maxSeverity: 4

- type: alert
  id: AlienHealth
  category: Health
  icons:
  - sprite: /Textures/Interface/Alien/screen_alien.rsi
    state: health0
  - sprite: /Textures/Interface/Alien/screen_alien.rsi
    state: health1
  - sprite: /Textures/Interface/Alien/screen_alien.rsi
    state: health2
  - sprite: /Textures/Interface/Alien/screen_alien.rsi
    state: health3
  - sprite: /Textures/Interface/Alien/screen_alien.rsi
    state: health4
  name: alerts-health-name
  description: alerts-health-desc
  minSeverity: 0
  maxSeverity: 4

- type: alert
  id: AlienCrit
  category: Health
  icons:
  - sprite: /Textures/Interface/Alien/screen_alien.rsi
    state: health6
  name: alerts-crit-name
  description: alerts-crit-desc

- type: alert
  id: AlienDead
  category: Health
  icons:
  - sprite: /Textures/Interface/Alien/screen_alien.rsi
    state: health7
  name: alerts-dead-name
  description: alerts-dead-desc

- type: alert
  id: BorgHealth
  category: Health
  icons:
  - sprite: /Textures/Interface/Alerts/borg_alive.rsi
    state: health0
  - sprite: /Textures/Interface/Alerts/borg_alive.rsi
    state: health1
  - sprite: /Textures/Interface/Alerts/borg_alive.rsi
    state: health2
  - sprite: /Textures/Interface/Alerts/borg_alive.rsi
    state: health3
  - sprite: /Textures/Interface/Alerts/borg_alive.rsi
    state: health4
  name: alerts-health-name
  description: alerts-health-desc
  minSeverity: 0
  maxSeverity: 4

- type: alert
  id: BorgCrit
  category: Health
  icons:
  - sprite: /Textures/Interface/Alerts/borg_critical.rsi
    state: critical
  name: alerts-crit-name
  description: alerts-crit-desc

- type: alert
  id: BorgDead
  category: Health
  icons:
  - sprite: /Textures/Interface/Alerts/borg_dead.rsi
    state: dead
  name: alerts-dead-name
  description: alerts-dead-desc

- type: alert
  id: BorgBattery
  category: Battery
  icons:
  - sprite: /Textures/Interface/Alerts/battery.rsi
    state: battery0
  - sprite: /Textures/Interface/Alerts/battery.rsi
    state: battery1
  - sprite: /Textures/Interface/Alerts/battery.rsi
    state: battery2
  - sprite: /Textures/Interface/Alerts/battery.rsi
    state: battery3
  - sprite: /Textures/Interface/Alerts/battery.rsi
    state: battery4
  - sprite: /Textures/Interface/Alerts/battery.rsi
    state: battery5
  - sprite: /Textures/Interface/Alerts/battery.rsi
    state: battery6
  - sprite: /Textures/Interface/Alerts/battery.rsi
    state: battery7
  - sprite: /Textures/Interface/Alerts/battery.rsi
    state: battery8
  - sprite: /Textures/Interface/Alerts/battery.rsi
    state: battery9
  - sprite: /Textures/Interface/Alerts/battery.rsi
    state: battery10
  name: alerts-battery-name
  description: alerts-battery-desc
  minSeverity: 0
  maxSeverity: 10

- type: alert
  id: BorgBatteryNone
  category: Battery
  icons:
  - sprite: /Textures/Interface/Alerts/battery.rsi
    state: battery-none
  name: alerts-no-battery-name
  description: alerts-no-battery-desc

- type: alert
  id: Internals
  category: Internals
  onClick: !type:ToggleInternals {}
  icons:
  - sprite: /Textures/Interface/Alerts/internals.rsi
    state: internal0
  - sprite: /Textures/Interface/Alerts/internals.rsi
    state: internal1
  - sprite: /Textures/Interface/Alerts/internals.rsi
    state: internal2
  name: alerts-internals-name
  description: alerts-internals-desc
  minSeverity: 0
  maxSeverity: 2

- type: alert
  id: PilotingShuttle
  category: Piloting
  onClick: !type:StopPiloting { }
  icons: [ /Textures/Interface/Alerts/piloting.png ]
  name: alerts-piloting-name
  description: alerts-piloting-desc

- type: alert
  id: Peckish
  category: Hunger
  icons:
  - sprite: /Textures/Interface/Alerts/hunger.rsi
    state: peckish
  name: alerts-hunger-name
  description: alerts-hunger-desc

- type: alert
  id: Stamina
  category: Stamina
  icons:
  - sprite: /Textures/Interface/Alerts/stamina.rsi
    state: stamina0
  - sprite: /Textures/Interface/Alerts/stamina.rsi
    state: stamina1
  - sprite: /Textures/Interface/Alerts/stamina.rsi
    state: stamina2
  - sprite: /Textures/Interface/Alerts/stamina.rsi
    state: stamina3
  - sprite: /Textures/Interface/Alerts/stamina.rsi
    state: stamina4
  - sprite: /Textures/Interface/Alerts/stamina.rsi
    state: stamina5
  - sprite: /Textures/Interface/Alerts/stamina.rsi
    state: stamina6
  name: alerts-stamina-name
  description: alerts-stamina-desc
  minSeverity: 0
  maxSeverity: 6

- type: alert
  id: Starving
  category: Hunger
  icons:
  - sprite: /Textures/Interface/Alerts/hunger.rsi
    state: starving
  name: alerts-starving-name
  description: alerts-starving-desc

- type: alert
  id: Thirsty
  category: Thirst
  icons:
  - sprite: /Textures/Interface/Alerts/thirst.rsi
    state: thirsty
  name: alerts-thirsty-name
  description: alerts-thirsty-desc

- type: alert
  id: Parched
  category: Thirst
  icons:
  - sprite: /Textures/Interface/Alerts/thirst.rsi
    state: parched
  name: alerts-parched-name
  description: alerts-parched-desc

- type: alert
  id: Muted
  icons: [ /Textures/Interface/Alerts/Abilities/silenced.png ]
  name: alerts-muted-name
  description: alerts-muted-desc

- type: alert
  id: VowOfSilence
  icons: [ /Textures/Interface/Alerts/Abilities/silenced.png ]
  name: alerts-vow-silence-name
  onClick: !type:BreakVow  { }
  description: alerts-vow-silence-desc

- type: alert
  id: VowBroken
  icons: [ /Textures/Interface/Actions/scream.png ]
  name: alerts-vow-broken-name
  onClick: !type:RetakeVow  { }
  description: alerts-vow-broken-desc

# Goobstation start
# Changed pulling to accomodate grabbing severity
- type: alert
  id: Pulled
  icons:
  - sprite: /Textures/_White/Interface/Alerts/pull.rsi
    state: pulled
  - sprite: /Textures/_White/Interface/Alerts/pull.rsi
    state: grabbed-soft
  - sprite: /Textures/_White/Interface/Alerts/pull.rsi
    state: grabbed-hard
  - sprite: /Textures/_White/Interface/Alerts/pull.rsi
    state: grabbed-choke
  onClick: !type:StopBeingPulled { }
  name: alerts-pulled-name
  description: alerts-pulled-desc
  minSeverity: 0
  maxSeverity: 3

- type: alert
  id: Pulling
  icons:
  - sprite: /Textures/_White/Interface/Alerts/pull.rsi
    state: pulling
  - sprite: /Textures/_White/Interface/Alerts/pull.rsi
    state: grab-soft
  - sprite: /Textures/_White/Interface/Alerts/pull.rsi
    state: grab-hard
  - sprite: /Textures/_White/Interface/Alerts/pull.rsi
    state: grab-choke
  onClick: !type:StopPulling  { }
  name: alerts-pulling-name
  description: alerts-pulling-desc
  minSeverity: 0
  maxSeverity: 3

# Goobstation end

- type: alert
  id: Bleed
  icons:
  - sprite: /Textures/Interface/Alerts/bleed.rsi
    state: bleed0
  - sprite: /Textures/Interface/Alerts/bleed.rsi
    state: bleed1
  - sprite: /Textures/Interface/Alerts/bleed.rsi
    state: bleed2
  - sprite: /Textures/Interface/Alerts/bleed.rsi
    state: bleed3
  - sprite: /Textures/Interface/Alerts/bleed.rsi
    state: bleed4
  - sprite: /Textures/Interface/Alerts/bleed.rsi
    state: bleed5
  - sprite: /Textures/Interface/Alerts/bleed.rsi
    state: bleed6
  - sprite: /Textures/Interface/Alerts/bleed.rsi
    state: bleed7
  - sprite: /Textures/Interface/Alerts/bleed.rsi
    state: bleed8
  - sprite: /Textures/Interface/Alerts/bleed.rsi
    state: bleed9
  - sprite: /Textures/Interface/Alerts/bleed.rsi
    state: bleed10
  name: alerts-bleed-name
  description: alerts-bleed-desc
  minSeverity: 0
  maxSeverity: 10

- type: alert
  id: Pacified
  icons:
  - sprite: /Textures/Interface/Alerts/pacified.rsi
    state: icon
  name: alerts-pacified-name
  description: alerts-pacified-desc

- type: alert
  id: Walking
  icons:
  - sprite: /Textures/Interface/Alerts/walking.rsi
    state: walking0
  - sprite: /Textures/Interface/Alerts/walking.rsi
    state: walking1
  name: alerts-walking-name
  description: alerts-walking-desc
  minSeverity: 0
  maxSeverity: 1

- type: alert
  id: Debug1
  icons:
  - sprite: /Textures/Interface/Alerts/human_alive.rsi
    state: health0
  name: "Debug1"
  description: "Налагодження"

- type: alert
  id: Debug2
  icons:
  - sprite: /Textures/Interface/Alerts/human_alive.rsi
    state: health1
  name: "Налагодження2"
  description: "Налагодження"

- type: alert
  id: Debug3
  icons:
  - sprite: /Textures/Interface/Alerts/human_alive.rsi
    state: health2
  name: "Debug3"
  description: "Налагодження"

- type: alert
  id: Debug4
  icons:
  - sprite: /Textures/Interface/Alerts/human_alive.rsi
    state: health3
  name: "Debug4"
  description: "Налагодження"

- type: alert
  id: Debug5
  icons:
  - sprite: /Textures/Interface/Alerts/human_alive.rsi
    state: health4
  name: "Debug5"
  description: "Налагодження"

- type: alert
  id: Debug6
  icons:
  - sprite: /Textures/Interface/Alerts/human_critical.rsi
    state: critical
  name: "Debug6"
  description: "Налагодження"

# Moods
- type: alert
  id: Insane
  category: Mood
  onClick: !type:ShowMoodEffects { }
  icons:
    - sprite: /Textures/Interface/Alerts/mood.rsi
      state: mood_insane
  name: alerts-mood-insane-name
  description: alerts-mood-insane-desc

- type: alert
  id: Horrible
  category: Mood
  onClick: !type:ShowMoodEffects { }
  icons:
    - sprite: /Textures/Interface/Alerts/mood.rsi
      state: mood1
  name: alerts-mood-horrible-name
  description: alerts-mood-horrible-desc

- type: alert
  id: Terrible
  category: Mood
  onClick: !type:ShowMoodEffects { }
  icons:
    - sprite: /Textures/Interface/Alerts/mood.rsi
      state: mood2
  name: alerts-mood-terrible-name
  description: alerts-mood-terrible-desc

- type: alert
  id: Bad
  category: Mood
  onClick: !type:ShowMoodEffects { }
  icons:
    - sprite: /Textures/Interface/Alerts/mood.rsi
      state: mood3
  name: alerts-mood-bad-name
  description: alerts-mood-bad-desc

- type: alert
  id: Meh
  category: Mood
  onClick: !type:ShowMoodEffects { }
  icons:
    - sprite: /Textures/Interface/Alerts/mood.rsi
      state: mood4
  name: alerts-mood-meh-name
  description: alerts-mood-meh-desc

- type: alert
  id: Neutral
  category: Mood
  onClick: !type:ShowMoodEffects { }
  icons:
    - sprite: /Textures/Interface/Alerts/mood.rsi
      state: mood5
  name: alerts-mood-neutral-name
  description: alerts-mood-neutral-desc

- type: alert
  id: Good
  category: Mood
  onClick: !type:ShowMoodEffects { }
  icons:
    - sprite: /Textures/Interface/Alerts/mood.rsi
      state: mood6
  name: alerts-mood-good-name
  description: alerts-mood-good-desc

- type: alert
  id: Great
  category: Mood
  onClick: !type:ShowMoodEffects { }
  icons:
    - sprite: /Textures/Interface/Alerts/mood.rsi
      state: mood7
  name: alerts-mood-great-name
  description: alerts-mood-great-desc

- type: alert
  id: Exceptional
  category: Mood
  onClick: !type:ShowMoodEffects { }
  icons:
    - sprite: /Textures/Interface/Alerts/mood.rsi
      state: mood8
  name: alerts-mood-exceptional-name
  description: alerts-mood-exceptional-desc

- type: alert
  id: Perfect
  category: Mood
  onClick: !type:ShowMoodEffects { }
  icons:
    - sprite: /Textures/Interface/Alerts/mood.rsi
      state: mood9
  name: alerts-mood-perfect-name
  description: alerts-mood-perfect-desc

- type: alert
  id: MoodDead
  category: Mood
  onClick: !type:ShowMoodEffects { }
  icons:
    - sprite: /Textures/Interface/Alerts/mood.rsi
      state: mood_happiness_bad
  name: alerts-mood-dead-name
  description: alerts-mood-dead-desc

- type: alert
  id: Deflecting
  icons:
  - sprite: /Textures/Interface/Alerts/deflecting.rsi
    state: deflecting0
  name: alerts-deflecting-name
  description: alerts-deflecting-desc
