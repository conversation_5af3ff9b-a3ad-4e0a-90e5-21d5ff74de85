# For empty check out chemistry bottles

# With cut-out

- type: entity
  parent: DrinkBaseCup
  id: DrinkVisualizerTestCut
  name: "контейнер для розчину з вирізом"
  description: "Ізольований глечик з нержавіючої сталі. Найкращий друг кожного вранці."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: JuiceWatermelon
          Quantity: 30
  - type: Sprite
    sprite: Objects/Consumable/Drinks/pitcher.rsi
    layers:
      - state: icon
      - state: fill-6
        map: ["enum.SolutionContainerLayers.Fill"]
        # REMEMBER IF YOU'RE SPAWNING WITH LIQUID ALREADY IN IT YOU WANT THIS TRUE
        visible: true
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 6
    fillBaseName: fill-

# Without (For food, non cut-out stuff)

- type: entity
  parent: DrinkBaseCup
  id: DrinkVisualizerTestNot
  name: "контейнер для розчину vis cut-not"
  description: "Ізольований глечик з нержавіючої сталі. Найкращий друг кожного вранці."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: JuiceWatermelon
          Quantity: 30
  - type: Sprite
    sprite: Objects/Consumable/Drinks/pitcher.rsi
    layers:
      - state: icon-6
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: true
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 6
    fillBaseName: icon-
    changeColor: false
    emptySpriteName: icon
