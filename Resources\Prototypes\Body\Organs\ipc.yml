- type: entity
  id: BaseIPCOrgan
  parent: BaseItem
  abstract: true
  components:
  - type: Sprite
    netsync: false
    sprite: Mobs/Species/IPC/organs.rsi
  - type: Organ
  # - type: Food
  # - type: Extractable
  #   grindableSolutionName: organ
  - type: SolutionContainerManager
    solutions:
      organ:
        reagents:
        - ReagentId: Oil
          Quantity: 10

- type: entity
  id: OrganIPCEyes
  parent: BaseIPCOrgan
  name: "роботичні очі"
  description: "01001001 00100000 01110011 01100101 01100101 00100000 01111001 01101111 01110101 00100001"
  components:
  - type: Sprite
    layers:
      - state: eyeball-l
      - state: eyeball-r
  - type: Organ
    slotId: eyes
  - type: Eyes
  - type: Tag
    tags:
    - Organ
    - Eyes

- type: entity
  id: OrganIPCTongue
  parent: BaseIPCOrgan
  name: "голосовий модулятор"
  description: "Голосовий модулятор, що використовується для відтворення мови."
  components:
  - type: Sprite
    state: tongue
  - type: Organ
  - type: Tag
    tags:
    - Organ

- type: entity
  id: OrganIPCEars
  parent: BaseIPCOrgan
  name: "звукові рецептори"
  description:
  components:
  - type: Sprite
    state: ears
  - type: Organ
  - type: Ears

- type: entity
  id: OrganIPCPump
  parent: BaseIPCOrgan
  name: "мікронасос"
  description: "Мікронасос, що використовується для циркуляції охолоджуючої рідини."
  components:
  - type: Sprite
    state: heart-on
  - type: Organ
    slotId: heart
  - type: Heart
  - type: Tag
    tags:
    - Organ
    - Heart
  # The heart 'metabolizes' medicines and poisons that aren't filtered out by other organs.
  # This is done because these chemicals need to have some effect even if they aren't being filtered out of your body.
  # You're technically 'immune to poison' without a heart, but.. uhh, you'll have bigger problems on your hands.

  # This is fine?
  # - type: Metabolizer
  #   maxReagents: 2
  #   metabolizerTypes: [Human]
  #   groups:
  #   - id: Medicine
  #   - id: Poison
  #   - id: Narcotic
