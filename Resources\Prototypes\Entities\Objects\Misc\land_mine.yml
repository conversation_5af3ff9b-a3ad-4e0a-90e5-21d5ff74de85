﻿- type: entity
  id: BaseLandMine
  abstract: true
  components:
  - type: Clickable
  - type: InteractionOutline
  - type: Anchorable
  - type: Pullable
  - type: MovedByPressure
  - type: Physics
    bodyType: Static
    fixedRotation: true
  - type: Fixtures
    fixtures:
      slips:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.2,-0.2,0.2,0.2"
        hard: false
        layer:
          - LowImpassable
  - type: Sprite
    drawdepth: Items
    sprite: Objects/Misc/landmine.rsi
    state: landmine
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:TriggerBehavior
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: LandMine
    sound:
        path: /Audio/Effects/beep_landmine.ogg
        params:
            maxDistance: 10
  - type: StepTrigger
    triggerGroups:
      types:
      - Landmine
    requiredTriggeredSpeed: 0
    stepOn: true

- type: entity
  name: "вдарити міну"
  parent: BaseLandMine
  id: LandMineKick
  components:
  - type: GhostKickUserOnTrigger
  - type: DeleteOnTrigger

- type: entity
  name: "модульна міна"
  description: "Цей поганець може приховувати в собі безліч небезпек. Або велосипедний клаксон."
  parent: BaseLandMine
  id: LandMineModular
  components:
  - type: PayloadCase
  - type: Construction
    graph: ModularMineGraph
    node: emptyCase

- type: entity
  name: "вибухонебезпечна міна"
  parent: BaseLandMine
  id: LandMineExplosive
  components:
  - type: ExplodeOnTrigger
  - type: Explosive
    explosionType: Default
    maxIntensity: 10
    intensitySlope: 3
    totalIntensity: 120 # about a ~4 tile radius
    canCreateVacuum: false
