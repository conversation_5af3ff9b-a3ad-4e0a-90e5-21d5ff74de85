- type: entity
  parent: [ BaseMachinePowered, ConstructibleMachine ]
  id: TelecomServer
  name: "телекомунікаційний сервер"
  description: "При увімкненому живленні та заповненому ключами шифрування він забезпечує зв'язок з радіогарнітурою."
  components:
  - type: Sprite
    sprite: Structures/Machines/telecomms.rsi
    snapCardinals: true
    layers:
    - state: icon
    - state: unlit
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: GenericVisualizer
    visuals:
      enum.PowerDeviceVisuals.Powered:
        enum.PowerDeviceVisualLayers.Powered:
          True: { visible: true }
          False: { visible: false }
  - type: Appearance
  - type: WiresVisuals
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.4,0.4,0.4"
        density: 190
        mask:
        - MachineMask
        layer:
        - MachineLayer
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:ChangeConstructionNodeBehavior
        node: machineFrame
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Machine
    board: TelecomServerCircuitboard
  - type: WiresPanel
  - type: Transform
    anchored: true
  - type: Pullable
  - type: EncryptionKeyHolder
    keysExtractionMethod: Prying
    keySlots: 10
  - type: TelecomServer
  - type: ContainerContainer
    containers:
      key_slots: !type:Container
      machine_board: !type:Container
      machine_parts: !type:Container

- type: entity
  parent: TelecomServer
  id: TelecomServerFilled
  suffix: Filled All
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyCommon
      - EncryptionKeyCargo
      - EncryptionKeyEngineering
      - EncryptionKeyMedical
      - EncryptionKeyScience
      - EncryptionKeySecurity
      - EncryptionKeyService
      - EncryptionKeyCommand
      - EncryptionKeyJustice #DeltaV - Justice dept

- type: entity
  parent: TelecomServer
  id: TelecomServerFilledCommon
  suffix: Common
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyCommon

- type: entity
  parent: TelecomServer
  id: TelecomServerFilledCargo
  suffix: Cargo
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyCargo

- type: entity
  parent: TelecomServer
  id: TelecomServerFilledEngineering
  suffix: Engineering
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyEngineering

- type: entity
  parent: TelecomServer
  id: TelecomServerFilledMedical
  suffix: Medical
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyMedical

- type: entity
  parent: TelecomServer
  id: TelecomServerFilledScience
  suffix: Science
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyScience

- type: entity
  parent: TelecomServer
  id: TelecomServerFilledSecurity
  suffix: Security
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeySecurity
      - EncryptionKeyJustice #DeltaV - Justice dept

- type: entity
  parent: TelecomServer
  id: TelecomServerFilledService
  suffix: Service
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyService

- type: entity
  parent: TelecomServer
  id: TelecomServerFilledCommand
  suffix: Command
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyCommand
