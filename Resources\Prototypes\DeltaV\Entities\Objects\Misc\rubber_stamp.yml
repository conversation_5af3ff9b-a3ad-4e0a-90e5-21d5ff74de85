- type: entity
  name: "штамп психолога"
  parent: RubberStampBase
  id: RubberStampPsychologist
  suffix: DO NOT MAP
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-psychologist
    stampedColor: "#00ccff"
    stampState: "paper_stamp-psychologist"
  - type: Sprite
    sprite: DeltaV/Objects/Misc/stamps.rsi
    state: stamp-psychologist

- type: entity
  name: "печатка адвоката"
  parent: RubberStampBase
  id: RubberStampLawyer
  suffix: DO NOT MAP
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-lawyer
    stampedColor: "#696969"
    stampState: "paper_stamp-lawyer"
  - type: Sprite
    sprite: DeltaV/Objects/Misc/stamps.rsi
    state: stamp-lawyer

- type: entity
  name: "печатка нотаріуса"
  parent: RubberStampBase
  id: RubberStampNotary
  description: "Старовинна печатка для скріплення важливих документів, виготовлена з полірованої бронзи."
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-notary
    stampedColor: "#a81f3d"
    stampState: "paper_stamp-notary"
  - type: Sprite
    sprite: DeltaV/Objects/Misc/stamps.rsi
    state: stamp-notary
  - type: StealTarget
    stealGroup: RubberStampNotary

- type: entity
  name: "печатка магістрата"
  parent: RubberStampBase
  id: RubberStampChiefJustice
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-chiefjustice
    stampedColor: "#6b2833"
    stampState: "paper_stamp-notary"
  - type: Sprite
    sprite: DeltaV/Objects/Misc/stamps.rsi
    state: stamp-cj

- type: entity
  name: "адміністративний асистент гумова печатка"
  parent: RubberStampBase
  id: RubberStampAdminAssistant
  suffix: DO NOT MAP
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-admin-assistant
    stampedColor: "#4191f2"
    stampState: "paper_stamp-admin-assistant"
  - type: Sprite
    sprite: DeltaV/Objects/Misc/stamps.rsi
    state: stamp-admin-assistant

- type: entity
  name: "Гумовий штамп представника"
  parent: RubberStampBase
  id: RubberStampCorporateLiaison
  suffix: DO NOT MAP
  components:
  - type: Stamp
    stampState: "paper_stamp-corporate"
  - type: CorporateStamp # handles colour, name
  - type: Sprite
    sprite: DeltaV/Objects/Misc/stamps.rsi
    state: stamp-liaison
