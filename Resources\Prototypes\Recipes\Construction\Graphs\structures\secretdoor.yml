- type: constructionGraph
  id: SecretDoor
  start: start
  graph:
  - node: start
    edges:
    - to: assembly
      completed:
      - !type:SetAnchor
        value: false
      steps:
      - material: Steel
        amount: 4
        doAfter: 4
      - material: MetalRod
        amount: 4
        doAfter: 4

  - node: assembly
    entity: BaseSecretDoorAssembly
    actions:
    - !type:SnapToGrid {}
    - !type:SetAnchor {}
    edges:
    - to: wired
      conditions:
      - !type:EntityAnchored {}
      steps:
      - material: Cable
        amount: 4
        doAfter: 2.5
    - to: start
      conditions:
      - !type:EntityAnchored
        anchored: false
      completed:
      - !type:SpawnPrototype
        prototype: SheetSteel1
        amount: 4
      - !type:DeleteEntity {}
      steps:
      - tool: Welding
        doAfter: 3

  - node: wired
    entity: BaseSecretDoorAssembly
    edges:
    - to: electronics
      steps:
      - component: PowerCell
        name: power cell
        store: battery-container
        icon:
          sprite: Objects/Power/power_cells.rsi
          state: small
        doAfter: 1
    - to: assembly
      completed:
      - !type:GivePrototype
        prototype: CableApcStack1
        amount: 4
      steps:
      - tool: Cutting
        doAfter: 2

  - node: electronics
    entity: BaseSecretDoorAssembly
    edges:
    - to: solidSecretDoor
      steps:
      - tool: Screwing
        doAfter: 2

  - node: solidSecretDoor
    entity: SolidSecretDoor
    edges:
    - to: wired
      conditions:
      - !type:EntityAnchored {}
      - !type:DoorWelded {}
      completed:
      - !type:EmptyAllContainers
        pickup: true
        emptyAtUser: true
      steps:
      - tool: Prying
        doAfter: 5
