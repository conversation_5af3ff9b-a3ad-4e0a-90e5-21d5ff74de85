using Content.Shared._NC.Radio;
using JetBrains.Annotations;
using Robust.Client.GameObjects;

namespace Content.Client._NC.Radio.UI;


[UsedImplicitly]
public sealed class HandheldRadioBoundUserInterface : BoundUserInterface
{
    [ViewVariables]
    private HandheldRadioMenu? _menu;

    public HandheldRadioBoundUserInterface(EntityUid owner, Enum uiKey) : base(owner, uiKey)
    {

    }

    protected override void Open()
    {
        base.Open();

        _menu = new();

        _menu.OnMicPressed += enabled =>
        {
            SendMessage(new ToggleHandheldRadioMicMessage(enabled));
        };
        _menu.OnSpeakerPressed += enabled =>
        {
            SendMessage(new ToggleHandheldRadioSpeakerMessage(enabled));
        };
        _menu.OnFrequencyChanged += frequency =>
        {
            if (int.TryParse(frequency.Trim(), out var intFreq) && intFreq > 0)
                SendMessage(new SelectHandheldRadioFrequencyMessage(intFreq));
            else
                SendMessage(new SelectHandheldRadioFrequencyMessage(-1)); // Query the current frequency
        };

        _menu.OnClose += Close;
        _menu.OpenCentered();
    }

    protected override void Dispose(bool disposing)
    {
        base.Dispose(disposing);
        if (!disposing)
            return;
        _menu?.Close();
    }

    protected override void UpdateState(BoundUserInterfaceState state)
    {
        base.UpdateState(state);

        if (state is not HandheldRadioBoundUIState msg)
            return;

        _menu?.Update(msg);
    }
}
