
### UI

# Verb name for climbing
comp-climbable-verb-climb = Забратися

### Повідомлення про взаємодію

# Відображається, коли ваш персонаж піднімається на $climbable
comp-climbable-user-climbs = Ви стрибаєте на { THE($climbable) }!

# Показується іншим, коли $user залазить на $climbable
comp-climbable-user-climbs-other = { CAPITALIZE(THE($user)) } стрибає на { THE($climbable) }!

# Shown to you when your character forces someone to climb on $climbable
comp-climbable-user-climbs-force = Ви змушуєте { THE($moved-user) } піднятися на { THE($climbable) }!

# Показується іншим, коли хтось змушує іншого $moved-user залізти на $climbable
comp-climbable-user-climbs-force-other = { CAPITALIZE(THE($user)) } піднімає { THE($moved-user) } на { THE($climbable) }!

# Показується вам, коли ваш персонаж знаходиться далеко від того, щоб бути підйомним
comp-climbable-cant-reach = Туди не дотягнешся!

# З'являється, коли ваш персонаж з певних причин не може взаємодіяти з підйомним об'єктом
comp-climbable-cant-interact = Ви не можете цього зробити!

# Показується вам, коли ваш персонаж не може піднятися власними діями
comp-climbable-cant-climb = Ви не здатні лазити!

# З'являється, коли ваш персонаж намагається змусити когось іншого, хто не може залізти на перешкоду, залізти на неї
comp-climbable-target-cant-climb = { CAPITALIZE(THE($moved-user)) } не може туди забратися!
