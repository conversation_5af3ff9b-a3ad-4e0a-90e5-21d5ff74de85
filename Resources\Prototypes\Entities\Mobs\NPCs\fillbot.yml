- type: entity
  parent: MobSiliconBase
  id: MobFillBot
  name: "філлбот"
  description: "Він підбирає речі і кладе їх кудись іще."
  components:
  - type: Fillbot
  - type: Sprite
    sprite: Mobs/Silicon/Bots/fillbot.rsi
    state: fillbot
  - type: Construction
    graph: FillBot
    node: bot
  - type: SentienceTarget
    flavorKind: station-event-random-sentience-flavor-mechanical
  - type: MovementSpeedModifier
    baseWalkSpeed: 2
    baseSprintSpeed: 3
  - type: NoSlip
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
  - type: Hands
    showInHands: false
  - type: ComplexInteraction
  - type: Body
    prototype: SiliconHand
    requiredLegs: 0
  - type: HTN
    rootTask:
      task: FillbotCompound
  - type: DeviceLinkSource
    ports:
      - FillItems
  - type: InteractionPopup
    interactSuccessString: petting-success-fillbot
    interactFailureString: petting-failure-fillbot
    interactSuccessSound:
      path: /Audio/Ambience/Objects/periodic_beep.ogg
