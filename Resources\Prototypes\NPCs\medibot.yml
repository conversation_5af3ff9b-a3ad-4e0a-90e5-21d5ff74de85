- type: htnCompound
  id: MedibotCompound
  branches:
    - tasks:
        - !type:HTNCompoundTask
          task: InjectNearbyCompound
    - tasks:
        - !type:HTNCompoundTask
          task: IdleCompound

- type: htnCompound
  id: InjectNearbyCompound
  branches:
    - tasks:
        # TODO: Kill this shit
        - !type:HTNPrimitiveTask
          operator: !type:PickNearbyInjectableOperator
            targetKey: InjectTarget
            targetMoveKey: TargetCoordinates

        - !type:HTNPrimitiveTask
          operator: !type:SpeakOperator
            speech: medibot-start-inject
            hidden: true

        - !type:HTNPrimitiveTask
          operator: !type:MoveToOperator
            pathfindInPlanning: false

        - !type:HTNPrimitiveTask
          operator: !type:SetFloatOperator
            targetKey: IdleTime
            amount: 3

        - !type:HTNPrimitiveTask
          operator: !type:WaitOperator
            key: IdleTime
          preconditions:
            - !type:KeyExistsPrecondition
              key: IdleTime

        # TODO: Kill this
        - !type:HTNPrimitiveTask
          operator: !type:MedibotInjectOperator
            targetKey: InjectTarget
