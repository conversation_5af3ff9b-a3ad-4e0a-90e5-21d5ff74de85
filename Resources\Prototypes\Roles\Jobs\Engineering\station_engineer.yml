- type: job
  id: StationEngineer
  name: job-name-engineer
  description: job-description-engineer
  playTimeTracker: JobStationEngineer
  requirements:
    - !type:CharacterDepartmentTimeRequirement
      department: Engineering
      min: 14400 #4 hrs
    - !type:CharacterEmployerRequirement
      employers:
      - HephaestusIndustries
      - ZavodskoiInterstellar
      - EinsteinEngines
      - NanoTrasen
  startingGear: StationEngineerGear
  icon: "JobIconStationEngineer"
  supervisors: job-supervisors-ce
  access:
  - Maintenance
  - Engineering
  - External
  extendedAccess:
  - Atmospherics

- type: startingGear
  id: StationEngineerGear
  subGear:
  - StationEngineerPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitEngineering
    back: ClothingBackpackEngineeringFilled
    shoes: ClothingShoesBootsWork
    id: EngineerPDA
    eyes: ClothingEyesGlassesMeson
    belt: ClothingBeltUtilityEngineering
    ears: ClothingHeadsetEngineering
  innerClothingSkirt: ClothingUniformJumpskirtEngineering
  satchel: ClothingBackpackSatchelEngineeringFilled
  duffelbag: ClothingBackpackDuffelEngineeringFilled

- type: startingGear
  id: StationEngineerPlasmamanGear
  parent: BasePlasmamanGear
  equipment:
    jumpsuit: ClothingUniformEnvirosuitEngineering
    head: ClothingHeadEnvirohelmEngineering
    gloves: ClothingHandsGlovesEnviroglovesEngineering
