# When adding new food also add to random spawner located in Resources\Prototypes\Entities\Markers\Spawners\Random\Food_Drinks\food_snack.yml
# Base

- type: entity
  parent: FoodBase
  id: FoodSnackBase
  abstract: true
  components:
  - type: Food
  - type: Tag
    tags:
      - FoodSnack
  - type: Sprite
    sprite: Objects/Consumable/Food/snacks.rsi
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 30 # Room for extra condiments
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
  - type: Item
    sprite: Objects/Consumable/Food/snacks.rsi
    heldPrefix: packet
    size: Tiny
# Snacks

# "Snacks" means food in a packet. Down the line this stuff can have multiple
# reagents and shit for healing or different effects.

- type: entity
  name: "боритос"
  parent: FoodSnackBase
  id: FoodSnackBoritos
  description: "Хрусткі, солоні чіпси з тортильї. З них, напевно, можна зробити начос."
  components:
  - type: FlavorProfile
    flavors:
      - boritos
  - type: Sprite
    state: boritos
  - type: Item
  - type: Food
    trash: 
    - FoodPacketBoritosTrash

- type: entity
  name: "C&D"
  parent: FoodSnackBase
  id: FoodSnackCnDs
  description: "З юридичної точки зору, ми не можемо сказати, що вони не розтануть у вас в руках."
  components:
  - type: FlavorProfile
    flavors:
      - chocolate
  - type: Sprite
    state: cnds
  - type: Item
  - type: Food
    trash: 
    - FoodPacketCnDsTrash

- type: entity
  name: "сирні гудки"
  parent: FoodSnackBase
  id: FoodSnackCheesie
  description: "Сирні закуски розміром з укус, від яких аж дзвенить у роті."
  components:
  - type: FlavorProfile
    flavors:
      - cheesy
      - funny
  - type: Sprite
    state: cheesiehonkers
  - type: Item
  - type: Food
    trash: 
    - FoodPacketCheesieTrash

- type: entity
  name: "чіпси"
  parent: FoodSnackBase
  id: FoodSnackChips
  description: "Командир Райкер - \"Чіпси\"."
  components:
  - type: FlavorProfile
    flavors:
      - oily
      - salty
      - potatoes
  - type: Sprite
    state: chips
  - type: Item
  - type: Food
    trash: 
    - FoodPacketChipsTrash

- type: entity
  name: "шоколадка"
  parent: BaseItem
  id: FoodSnackChocolate
  description: "На смак як картон."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/snacks.rsi
    state: chocolatebar
  - type: Item
    heldPrefix: chocolatebar
    size: Tiny
  - type: Tag
    tags:
      - FoodSnack
  - type: SpawnItemsOnUse
    items:
      - id: FoodPacketChocolateTrash
      - id: FoodSnackChocolateBar
    sound:
      path: /Audio/Effects/unwrap.ogg

- type: entity
  name: "шоколадка"
  parent: FoodSnackBase
  id: FoodSnackChocolateBar
  description: "На смак як картон."
  components:
  - type: FlavorProfile
    flavors:
      - chocolate
  - type: Sprite
    state: chocolatebar-open
  - type: Item
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 30
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Theobromine
          Quantity: 3
        - ReagentId: CocoaPowder
          Quantity: 1

- type: entity
  name: "енергетичний батончик"
  parent: FoodSnackBase
  id: FoodSnackEnergy
  description: "Енергетичний батончик з великою кількістю пуншу."
  components:
  - type: Sprite
    state: energybar
  - type: Item
  - type: SpawnItemsOnUse
    items:
      - id: FoodPacketEnergyTrash
      - id: FoodSnackEnergyBar
    sound:
      path: /Audio/Effects/unwrap.ogg

- type: entity
  name: "енергетичний батончик"
  parent: FoodSnackBase
  id: FoodSnackEnergyBar
  description: "Енергетичний батончик з великою кількістю пуншу."
  components:
  - type: FlavorProfile
    flavors:
      - nutty
      - oats
      - fiber
      - sweet
  - type: Sprite
    state: energybar-open
  - type: Item

- type: entity
  name: "фісташки цукерочки"
  parent: FoodSnackBase
  id: FoodSnackPistachios
  description: "Фісташки від \"Солодкого\". Напевно, від них не буде хвороб. Напевно."
  components:
  - type: FlavorProfile
    flavors:
      - salty
      - nutty
  - type: Sprite
    state: pistachio
  - type: Item
  - type: Food
    trash: 
    - FoodPacketPistachioTrash
  - type: Tag
    tags:
    - Fruit # Seed of a fruit, you can yell at me

- type: entity
  name: "попкорн"
  parent: FoodSnackBase
  id: FoodSnackPopcorn
  description: "Вирощений на невідомій планеті, невідомим фермером, застрелений якимось придурком на космічній станції."
  components:
  - type: FlavorProfile
    flavors:
      - corn
      - salt
      - butter
  - type: Sprite
    state: popcorn
  - type: Item
    heldPrefix: popcorn
  - type: Food
    trash: 
    - FoodPacketPopcornTrash

- type: entity
  name: "4без родзинок"
  parent: FoodSnackBase
  id: FoodSnackRaisins
  description: "Найкращі родзинки у всесвіті. Не знаю, чому."
  components:
  - type: FlavorProfile
    flavors:
      - raisins
  - type: Sprite
    state: raisins
  - type: Item
  - type: Food
    trash: 
    - FoodPacketRaisinsTrash
  - type: Tag
    tags:
    - Fruit

- type: entity
  name: "насіння соняшнику бобівські семки"
  parent: FoodSnackBase
  id: FoodSnackSemki
  description: "З гордістю виробляється харчовою корпорацією Bob Bobson. Ідеально підходить для того, щоб плювати на людей."
  components:
  - type: FlavorProfile
    flavors:
      - nutty
  - type: Sprite
    state: semki
  - type: Item
  - type: Food
    trash: 
    - FoodPacketSemkiTrash

- type: entity
  name: "sus jerky"
  parent: FoodSnackBase
  id: FoodSnackSus
  description: "Щось у цьому пакеті змушує вас почуватися неймовірно неспокійно. Хоча в'ялене м'ясо смачне."
  components:
  - type: FlavorProfile
    flavors:
      - susjerky
  - type: Sprite
    state: susjerky
  - type: Item
  - type: Food
    trash: 
    - FoodPacketSusTrash
  - type: Tag
    tags:
    - Meat

- type: entity
  name: "синді-тістечка"
  parent: FoodSnackBase
  id: FoodSnackSyndi
  description: "Надзвичайно вологий закусочний торт, який смакує так само добре, як і після ядерної бомбардування."
  components:
  - type: FlavorProfile
    flavors:
      - syndicakes
  - type: Sprite
    state: syndicakes
  - type: Item
  - type: Food
    trash: 
    - FoodPacketSyndiTrash

- type: entity
  parent: FoodSnackBase
  id: DrinkRamen
  name: "чашка рамен"
  description: "Дешева їжа зі смаком, що нагадує шкільні роки."
  components:
  - type: RefillableSolution
    solution: food
  - type: InjectableSolution
    solution: food
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50 #big cup
        reagents:
        - ReagentId: DryRamen
          Quantity: 30
        - ReagentId: Soysauce
          Quantity: 5
  - type: Sprite
    state: ramen
  - type: Food
    trash: 
    - FoodPacketCupRamenTrash

- type: entity
  parent: DrinkRamen
  id: DrinkHellRamen
  name: "пекельний рамен"
  description: "Супер пікантний смак!"
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50
        reagents:
        - ReagentId: DryRamen
          Quantity: 30
        - ReagentId: CapsaicinOil
          Quantity: 5

- type: entity
  name: "чау-мейн"
  parent: FoodSnackBase
  id: FoodSnackChowMein
  description: "Закуска з солоної смаженої локшини. Схоже, вони забули овочі."
  components:
  - type: FlavorProfile
    flavors:
      - cheapnoodles
      - salty
      - oily
  - type: Sprite
    state: chinese1
  - type: Item
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 30 # Room for extra condiments
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Soysauce
          Quantity: 2
  - type: Food
    trash: 
    - FoodPacketChowMeinTrash

- type: entity
  name: "локшина дан дан дан"
  parent: FoodSnackBase
  id: FoodSnackDanDanNoodles
  description: "Гостра закуска з сичуаньської локшини. Зверху збирається патьоки олії чилі."
  components:
  - type: FlavorProfile
    flavors:
      - cheapnoodles
      - oily
      - spicy
  - type: Sprite
    state: chinese2
  - type: Item
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 30 # Room for extra condiments
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: CapsaicinOil
          Quantity: 2
        - ReagentId: Soysauce
          Quantity: 2
  - type: Food
    trash: 
    - FoodPacketDanDanTrash

- type: entity
  name: "печиво з передбаченнями"
  parent: FoodSnackBase
  id: FoodSnackCookieFortune
  description: "Нудний картонний смаколик з цілим багатством всередині. Сюрприз! Ти теж нудний."
  components:
  - type: FlavorProfile
    flavors:
      - fortunecookie
  - type: Sprite
    state: cookie_fortune
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 1
        reagents:
        - ReagentId: Nutriment
          Quantity: 1
  - type: Item
    sprite: Objects/Consumable/Food/snacks.rsi
    heldPrefix: packet
    size: Tiny
  - type: Food
    trash: 
    - FoodCookieFortune

- type: entity
  id: FoodSnackNutribrick
  parent: BaseItem
  name: "нутрібрік"
  description: "Ретельно синтезована цеглина, розроблена з максимальним співвідношенням поживних речовин до об'єму. На смак як лайно."
  components:
  - type: Item
    size: Small
  - type: Tag
    tags:
      - FoodSnack
  - type: Sprite
    sprite: Objects/Consumable/Food/snacks.rsi
    state: nutribrick
  - type: SpawnItemsOnUse
    items:
      - id: FoodPacketMRETrash
      - id: FoodSnackNutribrickOpen
    sound:
      path: /Audio/Effects/unwrap.ogg

- type: entity
  id: FoodSnackNutribrickOpen
  parent: FoodSnackBase
  name: "нутрібрік"
  description: "Ретельно синтезована цеглина, розроблена з максимальним співвідношенням поживних речовин до об'єму. На смак як лайно."
  components:
  - type: FlavorProfile
    flavors:
      - nutribrick
  - type: Item
    size: Small
  - type: Tag
    tags:
      - ReptilianFood
  - type: Sprite
    state: nutribrick-open
  - type: Food
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 25
        reagents:
        - ReagentId: Nutriment
          Quantity: 20

- type: entity
  id: FoodSnackMREBrownie
  parent: BaseItem
  name: "тістечко"
  description: "Точно вимішаний брауні, створений, щоб витримувати тупі травми та суворі умови. На смак як лайно."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/snacks.rsi
    state: mre-brownie
  - type: Tag
    tags:
      - FoodSnack
  - type: SpawnItemsOnUse
    items:
      - id: FoodPacketMRETrash
      - id: FoodSnackMREBrownieOpen
    sound:
      path: /Audio/Effects/unwrap.ogg


- type: entity
  id: FoodSnackMREBrownieOpen
  parent: FoodSnackBase
  name: "тістечко"
  suffix: MRE
  description: "Точно вимішаний брауні, створений, щоб витримувати тупі травми та суворі умови. На смак як лайно."
  components:
  - type: FlavorProfile
    flavors:
      - mrebrownie
  - type: Sprite
    state: mre-brownie-open
  - type: Food
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Theobromine
          Quantity: 3

# Trash

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseItem
  id: FoodPacketTrash
  description: "Це нісенітниця."
  abstract: true
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/snacks.rsi
  - type: Item
    sprite: Objects/Consumable/Food/snacks.rsi
    heldPrefix: packet
    size: Tiny
  - type: Tag
    tags:
    - Trash
  - type: PhysicalComposition
    materialComposition:
      Steel: 100
  - type: SpaceGarbage
  - type: StaticPrice
    price: 0

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketTrash
  id: FoodPacketBoritosTrash
  name: "пакетик боритос"
  components:
  - type: Sprite
    state: boritos-trash

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketTrash
  id: FoodPacketCnDsTrash
  name: "Сумка C&Ds"
  components:
  - type: Sprite
    state: cnds-trash

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketTrash
  id: FoodPacketCheesieTrash
  name: "сирні гудки"
  components:
  - type: Sprite
    state: cheesiehonkers-trash

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketTrash
  id: FoodPacketChipsTrash
  name: "чіпси"
  components:
  - type: Sprite
    state: chips-trash

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketTrash
  id: FoodPacketChocolateTrash
  name: "шоколадна обгортка"
  components:
  - type: Sprite
    state: chocolatebar-trash

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketTrash
  id: FoodPacketEnergyTrash
  name: "обгортка енергетичного батончика"
  components:
  - type: Sprite
    state: energybar-trash

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketTrash
  id: FoodPacketPistachioTrash
  name: "пачка фісташок"
  components:
  - type: Sprite
    state: pistachio-trash

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketTrash
  id: FoodPacketPopcornTrash
  name: "коробка для попкорну"
  components:
  - type: Sprite
    state: popcorn-trash

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketTrash
  id: FoodPacketRaisinsTrash
  name: "4без родзинок"
  components:
  - type: Sprite
    state: raisins-trash

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketTrash
  id: FoodPacketSemkiTrash
  name: "пакет \"Семки"
  components:
  - type: Sprite
    state: semki-trash

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketTrash
  id: FoodPacketSusTrash
  name: "sus jerky"
  components:
  - type: Sprite
    state: susjerky-trash

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketTrash
  id: FoodPacketSyndiTrash
  name: "коробка з синді-тістечками"
  components:
  - type: Sprite
    state: syndicakes-trash

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketTrash
  id: FoodPacketCupRamenTrash
  name: "порожня чашка рамен"
  components:
  - type: Sprite
    state: ramen

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketTrash
  id: FoodPacketChowMeinTrash
  name: "порожня коробка з-під чау-чау"
  components:
  - type: Sprite
    state: chinese1

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketTrash
  id: FoodPacketDanDanTrash
  name: "порожне відро Дан-Дан"
  components:
  - type: Sprite
    state: chinese2

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketTrash
  id: FoodCookieFortune
  name: "печиво фортуна"
  description: "Доля говорить: Кінець близький... і це твоя провина."
  components:
  - type: Sprite
    state: fortune
  - type: Item
  - type: RandomMetadata
    descriptionSegments: [CookieFortuneDescriptions]

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketTrash
  id: FoodPacketMRETrash
  name: "Обгортка MRE"
  description: "Універсальна обгортка для різноманітних військових харчових продуктів."
  components:
  - type: Food
    requiresSpecialDigestion: true
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 30
        reagents:
        - ReagentId: Fiber
          Quantity: 40
  - type: Tag
    tags:
    - ClothMade
    - Trash
  - type: Sprite
    state: mre-wrapper

# Snacks
- type: entity
  parent: FoodSnackBase
  id: FoodSnackProteinbar
  name: "протеїновий батончик"
  description: "Протеїнові батончики бренду SwoleMAX гарантовано додадуть вам впевненості у власних силах."
  components:
  - type: FlavorProfile
    flavors:
      - chalky
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_food.rsi
    state: proteinbar
  - type: Item
  - type: Food
    trash:
    - FoodPacketProteinbarTrash
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 4
        - ReagentId: Protein
          Quantity: 4
        - ReagentId: CocoaPowder
          Quantity: 1
        - ReagentId: Theobromine
          Quantity: 1

# Sol Vend / Mars Mart
- type: entity
  parent: BaseItem
  id: FoodSnackLunacakeWrapped
  name: "загорнутий лунакейк"
  description: "Тепер на 20% менше судових позовів для отримання реголіту!"
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_food.rsi
    state: lunacake_wrapped
  - type: Item
    heldPrefix: lunacake_wrapped
    size: Tiny
  - type: Tag
    tags:
      - FoodSnack
  - type: SpawnItemsOnUse
    items:
      - id: FoodPacketLunacakeTrash
      - id: FoodSnackLunacake
    sound:
      path: /Audio/Effects/unwrap.ogg

- type: entity
  parent: FoodSnackBase
  id: FoodSnackLunacake
  name: "лунакейк"
  description: "Тепер на 20% менше судових позовів для отримання реголіту!"
  components:
  - type: FlavorProfile
    flavors:
      - vanilla
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_food.rsi
    state: lunacake
  - type: Item

- type: entity
  parent: BaseItem
  id: FoodSnackMochicakeWrapped
  name: "загорнутий мочікейк"
  description: "Коннічіва! Багатьом приносять удачу рисові коржі в майбутньому!"
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_food.rsi
    state: mochicake_wrapped
  - type: Item
    heldPrefix: mochicake_wrapped
    size: Tiny
  - type: Tag
    tags:
      - FoodSnack
  - type: SpawnItemsOnUse
    items:
      - id: FoodPacketMochicakeTrash
      - id: FoodSnackMochicake
    sound:
      path: /Audio/Effects/unwrap.ogg

- type: entity
  parent: FoodSnackBase
  id: FoodSnackMochicake
  name: "мочікейк"
  description: "Коннічіва! Багатьом приносять удачу рисові коржі в майбутньому!"
  components:
  - type: FlavorProfile
    flavors:
      - rice
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_food.rsi
    state: lunacake
  - type: Item

- type: entity
  parent: BaseItem
  id: FoodSnackMooncakeWrapped
  name: "загорнутий мункейк"
  description: "Відкрий для себе темну сторону! Може містити незначну кількість відновленого какао."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_food.rsi
    state: mooncake_wrapped
  - type: Item
    heldPrefix: mooncake_wrapped
    size: Tiny
  - type: Tag
    tags:
      - FoodSnack
  - type: SpawnItemsOnUse
    items:
      - id: FoodPacketMooncakeTrash
      - id: FoodSnackMooncake
    sound:
      path: /Audio/Effects/unwrap.ogg

- type: entity
  parent: FoodSnackBase
  id: FoodSnackMooncake
  name: "мункейк"
  description: "Відкрий для себе темну сторону! Може містити незначну кількість відновленого какао."
  components:
  - type: FlavorProfile
    flavors:
      - chocolate
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_food.rsi
    state: lunacake
  - type: Item

- type: entity
  parent: FoodSnackBase
  id: FoodSnackTidegobs
  name: "приливні качки"
  description: "Містить понад 9000% рекомендованої добової норми солі."
  components:
  - type: FlavorProfile
    flavors:
      - salty
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_food.rsi
    state: tidegobs
  - type: Item
  - type: Food
    trash:
    - FoodPacketTidegobsTrash

- type: entity
  parent: FoodSnackBase
  id: FoodSnackSaturnos
  name: "сатурн-ос"
  description: "Денний раціон солі, пінопласту і, можливо, тирси."
  components:
  - type: FlavorProfile
    flavors:
      - salty
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_food.rsi
    state: saturno
  - type: Item
  - type: Food
    trash:
    - FoodPacketSaturnosTrash

- type: entity
  parent: FoodSnackBase
  id: FoodSnackJoveGello
  name: "джов Гелло"
  description: "Від Джооов! Це якийсь гель."
  components:
  - type: FlavorProfile
    flavors:
      - sweet
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_food.rsi
    state: jupiter
  - type: Item
  - type: Food
    trash:
    - FoodPacketJoveGelloTrash

- type: entity
  parent: FoodSnackBase
  id: FoodSnackPlutoniumrods
  name: "плутонієві стрижні"
  description: "Безглузді несмачні поживні палички, які допоможуть вам протриматися цілий день. Тепер ще менше провокують висип!"
  components:
  - type: FlavorProfile
    flavors:
      - chalky
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_food.rsi
    state: pluto
  - type: Item
  - type: Food
    trash:
    - FoodPacketPlutoniumrodsTrash

- type: entity
  parent: FoodSnackBase
  id: FoodSnackMarsFrouka
  name: "фроука"
  description: "Відсвяткуйте день заснування з паруючою самопідігрітою мискою солодких яєць і картоплі!"
  components:
  - type: FlavorProfile
    flavors:
      - egg
      - potatoes
      - mustard
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_food.rsi
    state: mars
  - type: Item
  - type: Food
    trash:
    - FoodPacketMarsFroukaTrash

- type: entity
  parent: FoodSnackBase
  id: FoodSnackVenusianhotcakes
  name: "венеріанські гарячі пиріжки"
  description: "Гаряче - це гарячі пиріжки, вічна класика, яка тепер нарешті придатна для споживання людиною!"
  components:
  - type: FlavorProfile
    flavors:
      - spicy
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_food.rsi
    state: venus
  - type: Item
  - type: Food
    trash: 
    - FoodPacketVenusTrash

- type: entity
  parent: FoodSnackBase
  id: FoodSnackOortrocks
  name: "хмарні скелі Оорта"
  description: "Поп-рок, присвячений найважливішому промисловому сектору в Солі, нова формула гарантує менше травм ротової порожнини, спричинених осколками."
  components:
  - type: FlavorProfile
    flavors:
      - fizzy
      - sweet
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_food.rsi
    state: oort
  - type: Item
  - type: Food
    trash: 
    - FoodPacketOortrocksTrash

# Weeb Vend
- type: entity
  parent: FoodSnackBase
  id: FoodSnackRedalertnuts
  name: "горішки \"Червона тривога!\" "
  description: "Пакетик пряних горішків \"Червона тривога!\". Дуже смакує до пива!"
  components:
  - type: FlavorProfile
    flavors:
    - spicy
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_food.rsi
    state: weebonuts
  - type: Item
  - type: Food
    trash: 
    - FoodPacketRedalertnutsTrash

- type: entity
  parent: FoodSnackBase
  id: FoodSnackRicecake
  name: "рисовий пиріг"
  description: "Стародавня земна закуска, виготовлена зі спресованого рису."
  components:
  - type: FlavorProfile
    flavors:
      - rice
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_food.rsi
    state: ricecake
  - type: Item

- type: entity
  parent: FoodSnackBase
  id: FoodSnackPokeysticks
  name: "палички покі"
  description: "В'язка бісквітних паличок в шоколаді."
  components:
  - type: FlavorProfile
    flavors:
      - sweet
      - chocolate
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_food.rsi
    state: pokeys
  - type: Item

- type: entity
  parent: FoodSnackBase
  id: FoodSnackChocobanana
  name: "чокобанана"
  description: "Банан, вкритий шоколадом і посипкою. На паличці"
  components:
  - type: FlavorProfile
    flavors:
      - sweet
      - chocolate
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_food.rsi
    state: chocobanana
  - type: Item
  - type: Food
    trash:
    - FoodPacketStickTrash

- type: entity
  parent: FoodSnackBase
  id: FoodSnackDango
  name: "данго"
  description: "Харчові фарбовані рисові пельмені на паличці."
  components:
  - type: FlavorProfile
    flavors:
      - rice
      - sweet
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_food.rsi
    state: dango
  - type: Item
  - type: Food
    trash:
    - FoodPacketStickTrash

# Old food
- type: entity
  parent: FoodSnackBase
  id: FoodSnackAncientBurger
  name: "космічний бургер"
  suffix: ancient
  description: "Напевно, колись це виглядало дуже смачно."
  components:
  - type: FlavorProfile
    flavors:
      - terrible
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_food.rsi
    state: ancient_burger
  - type: Item
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
  - type: RandomFillSolution
    solution: food
    weightedRandomId: RandomFillSpaceshroom

- type: entity
  parent: FoodSnackAncientBurger
  id: FoodSnackAncientPizza
  name: "космічна піца"
  description: "Напевно, колись це виглядало дуже смачно."
  components:
  - type: Sprite
    state: ancient_pizza

- type: entity
  parent: FoodSnackAncientBurger
  id: FoodSnackAncientFries
  name: "картопля фрі"
  description: "Здається, сіль зберегла їх, все ще несвіжими і грубими."
  components:
  - type: Sprite
    state: ancient_fries

- type: entity
  parent: FoodSnackAncientBurger
  id: FoodSnackAncientHotdog
  name: "космічний пес"
  description: "Напевно, він лише трохи менш безпечний для вживання в їжу, ніж тоді, коли його вперше створили..."
  components:
  - type: Sprite
    state: ancient_hotdog

- type: entity
  parent: FoodSnackAncientBurger
  id: FoodSnackAncientTaco
  name: "космічний тако"
  description: "Цікаво, що оболонка стала м'якою, а вміст - несвіжим."
  components:
  - type: Sprite
    state: ancient_taco

# trash
- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketTrash
  id: FoodPacketLunacakeTrash
  name: "обгортка для луна-кексів"
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/bay_trash.rsi
    state: cakewrap
  - type: Item

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketLunacakeTrash
  id: FoodPacketMochicakeTrash
  name: "обгортка для печива"
  components:
  - type: Sprite
    state: mochicakewrap
  - type: Item

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketLunacakeTrash
  id: FoodPacketMooncakeTrash
  name: "обгортка для місячного кексу"
  components:
  - type: Sprite
    state: mooncakewrap
  - type: Item

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketLunacakeTrash
  id: FoodPacketTidegobsTrash
  name: "сміття tidegobs"
  components:
  - type: Sprite
    state: tidegobs
  - type: Item

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketLunacakeTrash
  id: FoodPacketSaturnosTrash
  name: "сатурн-ос сміття"
  components:
  - type: Sprite
    state: saturno
  - type: Item

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketLunacakeTrash
  id: FoodPacketJoveGelloTrash
  name: "jove gello trash"
  components:
  - type: Sprite
    state: jupiter
  - type: Item

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketLunacakeTrash
  id: FoodPacketPlutoniumrodsTrash
  name: "відходи плутонієвих стрижнів"
  components:
  - type: Sprite
    state: pluto
  - type: Item

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketLunacakeTrash
  id: FoodPacketMarsFroukaTrash
  name: "марс фрука сміття"
  components:
  - type: Sprite
    state: mars
  - type: Item

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketLunacakeTrash
  id: FoodPacketVenusTrash
  name: "венера гарячі пиріжки сміття"
  components:
  - type: Sprite
    state: venus
  - type: Item

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketLunacakeTrash
  id: FoodPacketOortrocksTrash
  name: "оорт каміння сміття"
  components:
  - type: Sprite
    state: oort
  - type: Item

# weebo vend
- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketLunacakeTrash
  id: FoodPacketRedalertnutsTrash
  name: "пачка червоної тривоги"
  components:
  - type: Sprite
    state: weebonuts
  - type: Item

- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketLunacakeTrash
  id: FoodPacketStickTrash
  name: "палиця"
  components:
  - type: Sprite
    state: stick
  - type: Item

#
- type: entity
  categories: [ HideSpawnMenu ]
  parent: FoodPacketLunacakeTrash
  id: FoodPacketProteinbarTrash
  name: "обгортка протеїнового батончика"
  components:
  - type: Sprite
    state: proteinbar
  - type: Item
