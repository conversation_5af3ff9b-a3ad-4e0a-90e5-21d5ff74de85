- type: entity
  id: AirAlarm
  name: "повітряна сигналізація"
  description: "Повітряна тривога. Сигналізація... повітря?"
  placement:
    mode: SnapgridCenter
    snap:
    - Wallmount
  components:
  - type: StationAiWhitelist
  - type: WallMount
  - type: ApcPowerReceiver
  - type: Electrified
    enabled: false
    usesApcPower: true
  - type: ExtensionCableReceiver
  - type: DeviceNetwork
    deviceNetId: AtmosDevices
    receiveFrequencyId: AtmosMonitor
    transmitFrequencyId: AtmosMonitor
    prefix: device-address-prefix-air-alarm
    sendBroadcastAttemptEvent: true
  - type: WiredNetworkConnection
  # for output status ports
  - type: WirelessNetworkConnection
    range: 200
  - type: DeviceList
  - type: DeviceNetworkRequiresPower
  - type: DeviceLinkSource
    ports:
    - AirDanger
    - AirWarning
    - AirNormal
    lastSignals:
      AirDanger: false
      AirWarning: false
      AirNormal: false
  - type: AtmosAlarmable
    syncWith:
    - AirAlarm
    - AirSensor
    - GasVent
    - GasScrubber
  - type: AtmosAlarmableVisuals
    layerMap: "airAlarmBase"
    alarmStates:
      Normal: alarm0
      Warning: alarm2
      Danger: alarm1
    setOnDepowered:
      airAlarmBase: alarmp
  - type: Tag
    tags:
      - AirAlarm
  - type: AtmosDevice
  - type: AirAlarm
  - type: AtmosAlertsDevice
    group: AirAlarm
  - type: Clickable
  - type: InteractionOutline
  - type: UserInterface
    interfaces:
      enum.SharedAirAlarmInterfaceKey.Key:
        type: AirAlarmBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: WiresPanel
  - type: Wires
    boardName: wires-board-name-airalarm
    layoutId: AirAlarm
  - type: AccessReader
    access: [["Atmospherics"]]
  - type: ContainerFill
    containers:
      board: [ AirAlarmElectronics ]
  - type: ContainerContainer
    containers:
      board: !type:Container
  - type: Appearance
  - type: WiresVisuals
  - type: Sprite
    sprite: Structures/Wallmounts/air_monitors.rsi
    layers:
    - state: alarm0
      map: ["airAlarmBase"] # TODO: fire alarm enum
    - state: alarmx
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: Transform
    anchored: true
  - type: Construction
    graph: AirAlarm
    node: air_alarm
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 200
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
          - !type:PlaySoundBehavior
            sound:
              collection: MetalGlassBreak
              params:
                volume: -4

- type: entity
  id: AirAlarmAssembly
  name: "повітряна сигналізація в зборі"
  description: "Повітряна тривога. Не схоже, що найближчим часом буде повітряна тривога."
  placement:
    mode: SnapgridCenter
    snap:
    - Wallmount
  components:
  - type: WallMount
  - type: Clickable
  - type: InteractionOutline
  - type: Sprite
    sprite: Structures/Wallmounts/air_monitors.rsi
    layers:
    - state: alarm_b1
      map: [ "enum.ConstructionVisuals.Layer" ]
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ConstructionVisuals.Key:
        enum.ConstructionVisuals.Layer:
          assembly: { state: alarm_b1 }
          electronics: { state: alarm_b1 }
  - type: Construction
    graph: AirAlarm
    node: assembly
  - type: Transform
    anchored: true
