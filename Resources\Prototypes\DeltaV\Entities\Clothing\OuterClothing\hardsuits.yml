# Standard Combat Hardsuits
- type: entity
  parent: [ClothingOuterHardsuitBaseMedium, BaseIntegratedMagboot]
  id: ClothingOuterHardsuitCombatStandard
  name: "бойовий комбінезон"
  description: "Спеціально розроблений бойовий костюм, призначений для захисту від усіх видів ворожих бойових дій в умовах низького тиску."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/OuterClothing/Hardsuits/Combat/standard.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/OuterClothing/Hardsuits/Combat/standard.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.50
    lowPressureMultiplier: 1000
  - type: ExplosionResistance
    damageCoefficient: 0.50
  - type: Armor # Good armour resistance across the board, comparable to the standard security hardsuit.
    modifiers:
      coefficients:
        Blunt: 0.60
        Slash: 0.60
        Piercing: 0.60
        Radiation: 0.75
        Caustic: 0.75
        Heat: 0.75
  - type: ClothingSpeedModifier
    walkModifier: 0.75
    sprintModifier: 0.75
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitCombatStandard
  - type: AllowSuitStorage

- type: entity
  parent: ClothingOuterHardsuitCombatStandard
  id: ClothingOuterHardsuitCombatOfficer
  name: "захисний бойовий комбінезон"
  description: "Спеціально розроблений бойовий костюм, призначений для захисту від усіх видів ворожих бойовиків в умовах низького тиску. На цьому костюмі є позначки безпеки станції."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/OuterClothing/Hardsuits/Combat/officer.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/OuterClothing/Hardsuits/Combat/officer.rsi
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitCombatOfficer

# Medical Combat Hardsuits
- type: entity
  parent: [ClothingOuterHardsuitBaseMedium, BaseIntegratedMagboot, BaseIntegratedManeuveringThrusters]
  id: ClothingOuterHardsuitCombatMedical
  name: "медичний бойовий комбінезон"
  description: "Спеціально розроблений бойовий костюм, що дозволяє користувачеві бути більш мобільним для кращої підтримки дружніх підрозділів у зонах активних бойових дій."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/OuterClothing/Hardsuits/Combat/medical.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/OuterClothing/Hardsuits/Combat/medical.rsi
  - type: PressureProtection # Less protective from high pressure than a standard hardsuit due to less plating.
    highPressureMultiplier: 0.60
    lowPressureMultiplier: 1000
  - type: ExplosionResistance
    damageCoefficient: 0.60
  - type: Armor # Slightly less armour than the standard hardsuit, but far higher mobility.
    modifiers:
      coefficients:
        Blunt: 0.65
        Slash: 0.65
        Piercing: 0.65
        Radiation: 0.80
        Caustic: 0.80
        Heat: 0.80
  - type: ClothingSpeedModifier
    walkModifier: 0.85
    sprintModifier: 0.85
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitCombatMedical
  - type: AllowSuitStorage

- type: entity
  parent: ClothingOuterHardsuitCombatMedical
  id: ClothingOuterHardsuitCombatCorpsman
  name: "бойовий комбінезон бріг-медика"
  description: "Спеціально розроблений бойовий костюм, що дозволяє користувачеві бути більш мобільним для кращої підтримки дружніх підрозділів у зонах активних бойових дій. На цьому костюмі є позначки безпеки станції."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/OuterClothing/Hardsuits/Combat/corpsman.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/OuterClothing/Hardsuits/Combat/corpsman.rsi
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitCombatCorpsman

# Riot Combat Hardsuits
- type: entity
  parent: [ClothingOuterHardsuitBaseHeavy, BaseIntegratedMagboot]
  id: ClothingOuterHardsuitCombatRiot
  name: "костюм для боротьби з масовими заворушеннями"
  description: "Спеціально розроблений бойовий костюм, призначений для боротьби з натовпом проти озброєних бойовиків в умовах низького тиску."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/OuterClothing/Hardsuits/Combat/riot.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/OuterClothing/Hardsuits/Combat/riot.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.45
    lowPressureMultiplier: 1000
  - type: ExplosionResistance
    damageCoefficient: 0.40
  - type: Armor # More protective than a standard security hardsuit, but far slower.
    modifiers:
      coefficients:
        Blunt: 0.50
        Slash: 0.50
        Piercing: 0.40
        Radiation: 0.70
        Caustic: 0.70
        Heat: 0.40
  - type: ClothingSpeedModifier
    walkModifier: 0.60
    sprintModifier: 0.60
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitCombatRiot
  - type: AllowSuitStorage

- type: entity
  parent: ClothingOuterHardsuitCombatRiot
  id: ClothingOuterHardsuitCombatWarden
  name: "костюм наглядача для боротьби з бунтом"
  description: "Спеціально розроблений бойовий костюм, призначений для контролю натовпу проти озброєних бойовиків в умовах низького тиску. На цьому костюмі є знаки безпеки станції та звання наглядача."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/OuterClothing/Hardsuits/Combat/warden.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/OuterClothing/Hardsuits/Combat/warden.rsi
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitCombatWarden

# Advanced Combat Hardsuits
- type: entity
  parent: [ClothingOuterHardsuitBaseHeavy, BaseIntegratedMagboot, BaseIntegratedManeuveringThrusters]
  id: ClothingOuterHardsuitCombatAdvanced
  name: "вдосконалений бойовий комбінезон"
  description: "Спеціально розроблений бойовий костюм другого покоління, що забезпечує неперевершений захист від усіх видів кінетичних сил в умовах низького тиску."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/OuterClothing/Hardsuits/Combat/advanced.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/OuterClothing/Hardsuits/Combat/advanced.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.40
    lowPressureMultiplier: 1000
  - type: ExplosionResistance
    damageCoefficient: 0.40
  - type: Armor # About as protective as a riot hardsuit but with far less movement penalty.
    modifiers:
      coefficients:
        Blunt: 0.50
        Slash: 0.50
        Piercing: 0.50
        Radiation: 0.75
        Caustic: 0.75
        Heat: 0.75
  - type: ClothingSpeedModifier
    walkModifier: 0.80
    sprintModifier: 0.80
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitCombatAdvanced
  - type: AllowSuitStorage

- type: entity
  parent: ClothingOuterHardsuitCombatAdvanced
  id: ClothingOuterHardsuitCombatHoS
  name: "просунутий бойовий комбінезон керівника служби безпеки"
  description: "Спеціально розроблений бойовий костюм другого покоління, що забезпечує неперевершений захист від усіх видів кінетичних сил в умовах низького тиску. Має захисні знаки станції та звання командира."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/OuterClothing/Hardsuits/Combat/hos.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/OuterClothing/Hardsuits/Combat/hos.rsi
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitCombatHoS