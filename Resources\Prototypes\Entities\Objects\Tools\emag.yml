- type: entity
  parent: BaseItem
  id: EmagUnlimited
  suffix: Unlimited
  name: "криптографічний секвенсор"
  description: "Червона друкована плата з індикатором заряду."
  components:
  - type: Emag
    blacklist: # DeltaV - Split Emag into 2 items, this one cannot emag doors
      components:
      - Airlock
  - type: ExtendDescription
    descriptionList:
      - description: "emag-extenddescription-command"
        fontSize: 12
        color: "#1155ff"
        requireDetailRange: true
        requirements:
        - !type:CharacterDepartmentRequirement
          departments:
            - Command
      - description: "emag-extenddescription-engineering"
        fontSize: 12
        color: "#ffaf00"
        requireDetailRange: true
        requirements:
        - !type:CharacterDepartmentRequirement
          departments:
            - Engineering
      - description: "emag-extenddescription-security"
        fontSize: 12
        color: "#ff0000"
        requireDetailRange: true
        requirements:
        - !type:CharacterDepartmentRequirement
          departments:
            - Security
      - description: "emag-extenddescription-epistemics"
        fontSize: 12
        color: "#aa00aa"
        requireDetailRange: true
        requirements:
        - !type:CharacterDepartmentRequirement
          departments:
            - Epistemics
      - description: "emag-extenddescription-syndicate"
        fontSize: 12
        color: "#880000"
        requireDetailRange: false
        requirements:
        - !type:CharacterAntagonistRequirement
          antagonists:
            - Traitor
            - TraitorSleeper
            - SpaceNinja
            - Nukeops
            - NukeopsMedic
            - NukeopsCommander
      - description: "mindshield-extenddescription-emag"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: true
        requirements:
        - !type:CharacterMindshieldRequirement
  - type: Sprite
    sprite: Objects/Tools/emag.rsi
    state: icon
  - type: Item
    sprite: Objects/Tools/emag.rsi
    storedRotation: -90
  - type: DamageOtherOnHit # An emag has sharp edges
    damage:
      types:
        Slash: 5
  - type: EmbeddableProjectile
    sound: /Audio/Weapons/star_hit.ogg
  - type: EmbedPassiveDamage

- type: entity
  parent: EmagUnlimited
  id: Emag
  suffix: Limited
  components:
  - type: LimitedCharges
  - type: AutoRecharge
