- type: entity
  parent: SeatBase
  id: ChairBarber
  name: "перукарське крісло"
  components:
  - type: Transform
    anchored: true
  - type: Physics
    bodyType: Static
  - type: Anchorable
  - type: Rotatable
  - type: Sprite
    sprite: Structures/Specific/barberchair.rsi
    state: barberchair

- type: entity
  parent: BaseSign
  id: BarberSignPole
  name: "перукарська палиця"
  description: "Гіпнотичний."
  components:
  - type: Sprite
    sprite: Structures/Specific/barbershop.rsi
    state: pole
    snapCardinals: false

- type: entity
  parent: BarberSignPole
  id: BarberSignThesnip
  name: "постріл"
  description: "Будемо сподіватися, що це не медична практика..."
  components:
  - type: Sprite
    sprite: Structures/Specific/barbershop.rsi
    state: thesnip
    snapCardinals: false
