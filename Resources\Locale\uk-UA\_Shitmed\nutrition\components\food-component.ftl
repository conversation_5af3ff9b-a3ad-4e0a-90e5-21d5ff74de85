## System

food-system-eat-broadcasted = {CAPITALIZE(THE($user))} намагається з'їсти {THE($food)}!
food-system-eat-broadcasted-self = Ви починаєте намагатися з'їсти {THE($food)}!
food-system-force-feed-broadcasted = {CAPITALIZE(THE($user))} намагається нагодувати {THE($target)} {THE($food)}!
food-system-force-feed-broadcasted-success = {CAPITALIZE(THE($user))} змусив {THE($target)} з'їсти {THE($food)}!
food-system-eat-broadcasted-success = {CAPITALIZE(THE($user))} з'їв {THE($food)}!
