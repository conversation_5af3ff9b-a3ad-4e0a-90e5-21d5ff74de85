- type: entity
  parent: ClothingHeadsetService
  id: ClothingHeadsetHoP
  name: "хмелева гарнітура"
  description: "Гарнітура, якою користується керівник відділу кадрів."
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyService
      - EncryptionKeyCommand
      - EncryptionKeyCommon

- type: entity
  parent: ClothingHeadsetAlt
  id: ClothingHeadsetAltService
  name: "навушник керівника персоналу"
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyService
      - EncryptionKeyCommand
      - EncryptionKeyCommon
  - type: Sprite
    sprite: DeltaV/Clothing/Ears/Headsets/service.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Ears/Headsets/service.rsi
    
- type: entity
  parent: ClothingHeadsetAltSyndicate
  id: ClothingHeadsetAltSyndicateListening
  name: "криваво-червона навушникова гарнітура з перехопленням"
  description: "Удосконалений модульний переговорний пристрій для перехоплення синдикатів, який одягається на голову."
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeySyndie
      - EncryptionKeyStationMaster
      - EncryptionKeyBinarySyndicate
  - type: Sprite
    sprite: DeltaV/Clothing/Ears/Headsets/syndicate_listening.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Ears/Headsets/syndicate_listening.rsi

- type: entity
  parent: ClothingHeadset
  id: ClothingHeadsetJustice
  name: "гарнітура для правосуддя"
  description: "Цим користується міністерство юстиції."
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyJustice
      - EncryptionKeySecurity
      - EncryptionKeyCommon
  - type: Sprite
    sprite: DeltaV/Clothing/Ears/Headsets/justice.rsi
    state: icon
  - type: Clothing
    sprite: DeltaV/Clothing/Ears/Headsets/justice.rsi

- type: entity
  parent: ClothingHeadset
  id: ClothingHeadsetAltJustice
  name: "гарнітура голови суду"
  description: "Гарнітура, якою користується голова суду."
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyJustice
      - EncryptionKeySecurity
      - EncryptionKeyCommon
      - EncryptionKeyCommand
  - type: Sprite
    sprite: DeltaV/Clothing/Ears/Headsets/justice.rsi
    state: icon_alt
  - type: Clothing
    sprite: DeltaV/Clothing/Ears/Headsets/justice.rsi
    
- type: entity
  parent: ClothingHeadset
  id: ClothingHeadsetPrison
  name: "тюремний гарнітур"
  description: "Гарнітура використовується всіма, хто потребує безпосереднього контакту з ув'язненими"
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyPrison
      - EncryptionKeyCommon
  - type: Sprite
    sprite: DeltaV/Clothing/Ears/Headsets/prisoner.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Ears/Headsets/prisoner.rsi

- type: entity
  parent: ClothingHeadset
  id: ClothingHeadsetPrisonGuard
  name: "охоронна гарнітура"
  description: "Гарнітура, якою користуються тюремні охоронці."
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyPrison
      - EncryptionKeyCommon
      - EncryptionKeySecurity
  - type: Sprite
    sprite: Clothing/Ears/Headsets/security.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/security.rsi
    
- type: entity
  parent: ClothingHeadset
  id: ClothingHeadsetAdminAssist
  name: "гарнітура адміністративного асистента"
  description: "Гарнітура, якою користується адміністративний асистент."
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyCommand
      - EncryptionKeyCommon
  - type: Sprite
    sprite: DeltaV/Clothing/Ears/Headsets/adminassistant.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Ears/Headsets/adminassistant.rsi

- type: entity
  parent: ClothingHeadsetAdminAssist
  id: ClothingHeadsetAltAdminAssist
  name: "адміністративний асистент навушної гарнітури"
  components:
  - type: Sprite
    state: icon_alt
  - type: Clothing
    equippedPrefix: alt
