﻿- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatChameleon
  name: "берет"
  description: "Берет - улюблений головний убір художників."
  suffix: Chameleon
  components:
    - type: Tag
      tags: [] # ignore "WhitelistChameleon" tag
    - type: Sprite
      sprite: Clothing/Head/Hats/beret.rsi
    - type: Clothing
      sprite: Clothing/Head/Hats/beret.rsi
    - type: ChameleonClothing
      slot: [HEAD]
      default: ClothingHeadHatBeret
    - type: UserInterface
      interfaces:
        enum.ChameleonUiKey.Key:
          type: ChameleonBoundUserInterface
