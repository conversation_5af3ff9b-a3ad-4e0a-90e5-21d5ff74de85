- type: entity
  parent: [Clothing, ContentsExplosionResistanceBase]
  id: ClothingBackpack
  name: "рюкзак"
  description: "Ви одягаєте його на спину і кладете в нього речі."
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/backpack.rsi
    state: icon
  - type: Item
    size: Huge
  - type: Clothing
    quickEquip: false
    slots:
    - back
  - type: Storage
    grid:
    - 0,0,6,3
    maxItemSize: Huge
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
        ents: []
  - type: UserInterface
    interfaces:
      enum.StorageUiKey.Key:
        type: StorageBoundUserInterface
  # to prevent bag open/honk spam
  - type: UseDelay
    delay: 0.5
  - type: ExplosionResistance
    damageCoefficient: 0.9
  - type: AllowsSleepInside # DeltaV - enable sleeping inside bags
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 35
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackClown
  name: "смішко фон Хонкертон"
  description: "Це рюкзак, зроблений компанією Honk! Co."
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/clown.rsi
  - type: Storage
    storageOpenSound:
      collection: BikeHorn

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackIan
  name: "Рюкзак Яна"
  description: "Іноді він його носить."
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/ian.rsi
  - type: Storage
    storageOpenSound:
      collection: IanBark

- type: entity
  parent: ClothingBackpackIan
  id: ClothingBackpackIanFilled
  name: "Рюкзак Яна"
  description: "Іноді він його носить."
  components:
  - type: StorageFill
    contents:
      - id: Flash

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackSecurity
  name: "рюкзак служби безпеки"
  description: "Це дуже міцний рюкзак."
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/security.rsi

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackBrigmedic
  name: "рюкзак бріг-медика" # DeltaV - rename brig medic to corpsman
  description: "Це дуже стерильний рюкзак."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Back/Backpacks/brigmedic.rsi # DeltaV - resprite

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackEngineering
  name: "інженерний рюкзак"
  description: "Це міцний рюкзак для щоденної рутини вокзального життя."
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/engineering.rsi

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackAtmospherics
  name: "атмосферний рюкзак"
  description: "Це рюкзак з вогнетривких волокон. Пахне плазмою."
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/atmospherics.rsi

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackMedical
  name: "медичний рюкзак"
  description: "Це рюкзак, спеціально розроблений для використання в стерильному середовищі."
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/medical.rsi

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackCaptain
  name: "капітанський рюкзак"
  description: "Це спеціальний рюкзак, виготовлений ексклюзивно для офіцерів \"Нанотрейзену\"."
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/captain.rsi

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackMime
  name: "мімічний рюкзак"
  description: "Безшумний рюкзак, створений для тих, хто працює мовчки. Silence Co."
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/mime.rsi

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackChemistry
  name: "рюкзак для хімії"
  description: "Рюкзак, спеціально розроблений для відштовхування плям і небезпечних рідин."
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/chemistry.rsi

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackHydroponics
  name: "рюкзак для гідропоніки"
  description: "Це рюкзак з повністю натуральних волокон."
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/hydroponics.rsi

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackScience
  name: "рюкзак з епістемології" # DeltaV - Epistemics Department replacing Science
  description: "Рюкзак, спеціально розроблений для відштовхування плям і небезпечних рідин."
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/science.rsi

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackRobotics
  name: "рюкзак з робототехніки"
  description: "Рюкзак, спеціально розроблений для відштовхування плям і небезпечних рідин."
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/robotics.rsi

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackVirology
  name: "вірусологічний рюкзак"
  description: "Рюкзак з гіпоалергенних волокон. Створений, щоб допомогти запобігти поширенню хвороб. Пахне мавпочкою."
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/virology.rsi

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackGenetics
  name: "генетичний рюкзак"
  description: "Надміцний рюкзак, розроблений на випадок, якщо на вас хтось накинеться."
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/genetics.rsi

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackCargo
  name: "логістичний рюкзак" # DeltaV - Logistics Department replacing Cargo
  description: "Міцний рюкзак для крадіжок з логістики." # DeltaV - Logistics Department replacing Cargo
  components:
    - type: Sprite
      sprite: Clothing/Back/Backpacks/cargo.rsi

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackSalvage
  name: "речовий мішок утилізатора"
  description: "Міцний рюкзак для зберігання здобичі."
  components:
    - type: Sprite
      sprite: Clothing/Back/Backpacks/salvage.rsi

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackMerc
  name: "мішок з грошима"
  description: "Рюкзак, який побував у багатьох небезпечних місцях, надійний бойовий рюкзак."
  components:
    - type: Sprite
      sprite: Clothing/Back/Backpacks/merc.rsi

#ERT
- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackERTLeader
  name: "Рюкзак лідера групи швидкого реагування"
  description: "Місткий рюкзак з великою кількістю кишень, який носить командир ГШР"
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/ertleader.rsi
  - type: Storage
    grid:
    - 0,0,10,3

- type: entity
  parent: ClothingBackpackERTLeader
  id: ClothingBackpackERTSecurity
  name: "Рюкзак безпеки ГШР"
  description: "Місткий рюкзак з великою кількістю кишень, який носять співробітники служби безпеки ГШР"
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/ertsec.rsi

- type: entity
  parent: ClothingBackpackERTLeader
  id: ClothingBackpackERTMedical
  name: "Медичний рюкзак ГШР"
  description: "Місткий рюкзак з великою кількістю кишень, який носять медики ГШР"
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/ertmed.rsi

- type: entity
  parent: ClothingBackpackERTLeader
  id: ClothingBackpackERTEngineer
  name: "Рюкзак інженера ГШР"
  description: "Місткий рюкзак з великою кількістю кишень, який носять інженери ГШР"
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/erteng.rsi

- type: entity
  parent: ClothingBackpackERTLeader
  id: ClothingBackpackERTJanitor
  name: "Рюкзак прибиральника ГШР"
  description: "Місткий рюкзак з великою кількістю кишень, який носять прибиральники ГШР"
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/ertjanitor.rsi

- type: entity
  parent: ClothingBackpackERTLeader
  id: ClothingBackpackERTClown
  name: "Рюкзак клоуна ГШР"
  description: "Місткий рюкзак з великою кількістю кишень, який носять клоуни ГШР"
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/ertclown.rsi

- type: entity
  parent: ClothingBackpackERTLeader
  id: ClothingBackpackERTChaplain
  name: "Рюкзак капелана ERT"
  description: "Місткий рюкзак з великою кількістю кишень, який носять капелани групи швидкого реагування."
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/ertchaplain.rsi

- type: entity
  parent: ClothingBackpackERTSecurity
  id: ClothingBackpackDeathSquad
  name: "рюкзак ескадрону смерті"
  description: "Містить набір найстрашніших агентів CentComm."
  components:
    - type: Storage
      grid:
      - 0,0,7,6

#Syndicate
- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackSyndicate
  name: "синдикатський рюкзак"
  description:
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/syndicate.rsi
  - type: ExplosionResistance
    damageCoefficient: 0.1

#Special
- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackHolding
  name: "мішок для утримання"
  description: "Рюкзак, який відкривається в локальну кишеню блюзового простору."
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/holding.rsi
    state: holding
    layers:
    - state: holding
    - state: holding-unlit
      shader: unshaded
  - type: Clothing
    equippedPrefix: holding
  - type: Item
    size: Ginormous
  - type: Storage
    maxItemSize: Huge
    grid:
    - 0,0,19,9

- type: entity
  parent: ClothingBackpackClown
  id: ClothingBackpackCluwne
  name: "джиглз фон джонкертон"
  suffix: Unremoveable
  description: "Це рюкзак від компанії Jonk! Co."
  components:
  - type: Sprite
    sprite: Clothing/Back/Backpacks/cluwne.rsi
  - type: Unremoveable
    deleteOnDrop: false

# Debug
- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackDebug
  name: "вакпак"
  description: "Що це в біса таке?"
  suffix: Debug
  components:
  - type: Storage
    grid:
    - 0,0,3,3
    - 5,0,7,2
    - 0,5,7,5
    - 6,4,7,5
    - 9,2,10,3
    - 9,5,9,5

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackDebug2
  name: "великий рюкзак"
  description: "Що це в біса таке?"
  suffix: Debug
  components:
  - type: Storage
    grid:
    - 0,0,39,24

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackDebug3
  name: "гейський придурок"
  description: "Що це в біса таке?"
  suffix: Debug
  components:
  - type: Storage
    grid:
    - 0,0,0,3
    - 0,0,2,0
    - 0,3,2,3
    - 2,1,2,1
    - 4,0,4,2
    - 6,0,6,2
    - 5,1,5,1
    - 5,3,5,3
    - 9,0,9,1
    - 8,2,8,3
    - 10,2,10,3

- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackDebug4
  name: "офсетний рюкзак"
  description: "Що це в біса таке?"
  suffix: Debug
  components:
  - type: Storage
    grid:
    - 5,5,11,8
