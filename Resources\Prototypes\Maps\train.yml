- type: gameMap
  id: Train
  mapName: Потяг
  mapPath: /Maps/train.yml
  minPlayers: 7
  stations:
    Train:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: 'Train "Sentipode" {0}-{1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: 'ED'
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/emergency_omega.yml # TODO - add railway station
        - type: StationJobs
          availableJobs:
            #service
            Captain: [ 1, 1 ]
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 1, 1 ]
            Botanist: [ 2, 2 ]
            Chef: [ 1, 1 ]
            Janitor: [ 2, 2 ]
            Chaplain: [ 1, 1 ]
            Li<PERSON>rian: [ 1, 1 ]
            ServiceWorker: [ 2, 3 ]
            Detective: [ 1, 1 ]
            #engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 2, 2 ]
            StationEngineer: [ 4, 4 ]
            TechnicalAssistant: [ 3, 3 ]
            SeniorEngineer: [ 1, 1 ]
            #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            Chemist: [ 2, 2 ]
            MedicalDoctor: [ 2, 2 ]
            MedicalIntern: [ 2, 2 ]
            Paramedic: [ 1, 1 ]
            SeniorPhysician: [ 1, 1 ]
            #science
            ResearchDirector: [ 1, 1 ]
            Scientist: [ 4, 4 ]
            ResearchAssistant: [ 2, 2 ]
            SeniorResearcher: [ 1, 1 ]
            #security
            HeadOfSecurity: [ 1, 1 ]
            Warden: [ 1, 1 ]
            SecurityOfficer: [ 6, 6 ]
            SecurityCadet: [ 3, 3 ]
            Lawyer: [ 1, 2 ]
            SeniorOfficer: [ 1, 1 ]
            #supply
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ 3, 3 ]
            CargoTechnician: [ 3, 3 ]
            #civilian
            Passenger: [ -1, -1 ]
            Clown: [ 1, 1 ]
            Mime: [ 1, 1 ]
            Musician: [ 1, 1 ]
            Borg: [ 2, 2 ]
            Reporter: [ 1, 1 ]
            #silicon
            StationAi: [ 1, 1 ]
