- type: species # Chitinids have had their visuals moved to be a set of moth markings so less is lost in the process of disabling them.
  id: Chitinid
  name: species-name-chitinid
  roundStart: false # THESE ARE CURRENTLY A SERVER PERFORMANCE DISASTER. IF YOU ENABLE THEM AND THEN COMPLAIN ABOUT SERVER PERFORMANCE, I WILL BAN YOU FROM THE DISCORD.
  prototype: MobChitinid
  sprites: MobChitinidSprites
  defaultSkinTone: "#ffda93"
  markingLimits: MobChitinidMarkingLimits
  dollPrototype: MobChitinidDummy
  skinColoration: Hues
  maleFirstNames: NamesChitinidFirstMale
  femaleFirstNames: NamesChitinidFirstFemale
  naming: First

- type: speciesBaseSprites
  id: MobChitinidSprites
  sprites:
    Head: MobChitinidHead
    Snout: MobHumanoidAnyMarking
    Chest: MobChitinidTorso
    Underwear: MobHumanoidAnyMarking
    Undershirt: MobHumanoidAnyMarking
    HeadTop: MobHumanoidAnyMarking
    HeadSide: MobHumanoidAnyMarking
    Tail: MobHumanoidAnyMarking
    Eyes: MobChitinidEyes
    LArm: MobChitinidLArm
    RArm: MobChitinidRArm
    LHand: MobChitinidLHand
    RHand: MobChitinidRHand
    LLeg: MobChitinidLLeg
    RLeg: MobChitinidRLeg
    LFoot: MobChitinidLFoot
    RFoot: MobChitinidRFoot

- type: humanoidBaseSprite
  id: MobChitinidEyes
  baseSprite:
    sprite: DeltaV/Mobs/Species/Chitinid/parts.rsi
    state: eyes

- type: markingPoints
  id: MobChitinidMarkingLimits
  onlyWhitelisted: true
  points:
    Hair:
      points: 0
      required: false
    FacialHair:
      points: 0
      required: false
    Tail:
      points: 1
      required: true
      defaultMarkings: [ ChitinidWingsDefault ]
    Snout:
      points: 1
      required: false
    HeadTop:
      points: 6
      required: true
      defaultMarkings: [ ChitinidAntennasDefault ]
    HeadSide:
      points: 6
      required: false
    Head:
      points: 6
      required: false
    Chest:
      points: 6
      required: false
    LeftLeg:
      points: 6
      required: false
    RightLeg:
      points: 6
      required: false
    LeftArm:
      points: 6
      required: false
    RightArm:
      points: 6
      required: false

- type: humanoidBaseSprite
  id: MobChitinidHead
  baseSprite:
    sprite: DeltaV/Mobs/Species/Chitinid/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobChitinidHeadMale
  baseSprite:
    sprite: DeltaV/Mobs/Species/Chitinid/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobChitinidHeadFemale
  baseSprite:
    sprite: DeltaV/Mobs/Species/Chitinid/parts.rsi
    state: head_f

- type: humanoidBaseSprite
  id: MobChitinidTorso
  baseSprite:
    sprite: DeltaV/Mobs/Species/Chitinid/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobChitinidTorsoMale
  baseSprite:
    sprite: DeltaV/Mobs/Species/Chitinid/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobChitinidTorsoFemale
  baseSprite:
    sprite: DeltaV/Mobs/Species/Chitinid/parts.rsi
    state: torso_f

- type: humanoidBaseSprite
  id: MobChitinidLLeg
  baseSprite:
    sprite: DeltaV/Mobs/Species/Chitinid/parts.rsi
    state: l_leg

- type: humanoidBaseSprite
  id: MobChitinidLHand
  baseSprite:
    sprite: DeltaV/Mobs/Species/Chitinid/parts.rsi
    state: l_hand

- type: humanoidBaseSprite
  id: MobChitinidLArm
  baseSprite:
    sprite: DeltaV/Mobs/Species/Chitinid/parts.rsi
    state: l_arm

- type: humanoidBaseSprite
  id: MobChitinidLFoot
  baseSprite:
    sprite: DeltaV/Mobs/Species/Chitinid/parts.rsi
    state: l_foot

- type: humanoidBaseSprite
  id: MobChitinidRLeg
  baseSprite:
    sprite: DeltaV/Mobs/Species/Chitinid/parts.rsi
    state: r_leg

- type: humanoidBaseSprite
  id: MobChitinidRHand
  baseSprite:
    sprite: DeltaV/Mobs/Species/Chitinid/parts.rsi
    state: r_hand

- type: humanoidBaseSprite
  id: MobChitinidRArm
  baseSprite:
    sprite: DeltaV/Mobs/Species/Chitinid/parts.rsi
    state: r_arm

- type: humanoidBaseSprite
  id: MobChitinidRFoot
  baseSprite:
    sprite: DeltaV/Mobs/Species/Chitinid/parts.rsi
    state: r_foot
