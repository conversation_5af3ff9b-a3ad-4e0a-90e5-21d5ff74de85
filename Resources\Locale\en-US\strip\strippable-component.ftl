strippable-component-not-holding-anything = You aren't holding anything!
strippable-component-cannot-drop = You can't drop that!
strippable-component-item-slot-occupied-message = {$owner} already has something there!
strippable-component-cannot-equip-message = {$owner} cannot equip that there!
strippable-component-cannot-put-message = {$owner} cannot put that there!
strippable-component-item-slot-free-message = {$owner} has nothing there!
strippable-component-cannot-unequip-message = {$owner} cannot unequip that!
strippable-component-cannot-drop-message = {$owner} cannot drop that!
strippable-component-alert-owner = {$user} is removing your {$item}!
strippable-component-alert-owner-hidden = You feel someone fumbling in your {$slot}!
strippable-component-alert-owner-insert = {$user} is putting {$item} on you!

# generic warning for when a user interacts with your equipped items.
strippable-component-alert-owner-interact = {$user} is fumbling around with your {$item}!

# StripVerb
strip-verb-get-data-text = Strip

## UI

strippable-bound-user-interface-stripping-menu-title = {$ownerName}'s inventory
strippable-bound-user-interface-stripping-menu-ensnare-button = Remove Leg Restraints

# Stealth
thieving-component-user = Someone
thieving-component-item = something