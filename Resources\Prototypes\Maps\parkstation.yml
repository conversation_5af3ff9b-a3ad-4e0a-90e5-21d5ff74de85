- type: gameMap
  id: ParkStation
  mapName: Паркстанція
  mapPath: /Maps/parkstation.yml
  minPlayers: 3
  stations:
    parkstation:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: '{0} Arborum {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: 'TG'
        - type: StationJobs
          overflowJobs:
            - Passenger
          availableJobs:
            #service
            Captain: [ 1, 1]
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 1, 1 ]
            Botanist: [ 2, 3 ]
            Chef: [ 1, 1 ]
            Janitor: [ 2, 3 ]
            Chaplain: [ 1, 1 ]
            Librarian: [ 1, 1 ]
            ServiceWorker: [ 2, 3 ]
            Magistrate: [ 1, 1 ]
            #engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 1, 3 ]
            StationEngineer: [ 3, 6 ]
            TechnicalAssistant: [ 3, 3 ]
            #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            MedicalDoctor: [ 3, 6 ]
            MedicalIntern: [ 2, 2 ]
            Paramedic: [ 1, 1 ]
            MedicalBorg: [ 2, 3 ]
            #science
            ResearchDirector: [ 1, 1 ]
            Scientist: [ 4, 4 ]
            ResearchAssistant: [ 2, 2 ]
            Roboticist: [ 1, 2 ]
            ForensicMantis: [ 1, 1 ]
            #security
            HeadOfSecurity: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1 ] #PIRATE
            Warden: [ 1, 1 ]
            SecurityOfficer: [ 2, 4 ]
            Brigmedic:  [ 1, 1 ]
            Detective: [ 1, 1 ]
            SecurityCadet: [ 3, 5 ]
            Psychologist: [ 1, 1 ]
            #supply
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ 2, 4 ]
            CargoTechnician: [ 2, 4 ]
            MailCarrier: [ 1, 2 ]
            #civilian
            Passenger: [ -1, -1 ]
            Clown: [ 1, 1 ]
            Mime: [ 1, 1 ]
            Musician: [1, 2]
            Borg: [1, 2]
            Boxer: [ 1, 2 ]
           #justice
            Clerk: [ 1, 1 ]
            Lawyer: [ 2, 2 ]
            Prosecutor: [ 2, 2 ]
            #other
            Reporter: [ 1, 1 ]
