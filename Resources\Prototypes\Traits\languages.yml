- type: trait
  id: SignLanguage
  category: TraitsSpeechLanguages
  points: -1
  # slots: 1
  # itemGroupSlots: 0
  requirements:
    # - !type:CharacterItemGroupRequirement
    #   group: TraitsLanguagesBasic
    - !type:CharacterTraitRequirement
      inverted: true
      traits:
        - Muted
  functions:
    - !type:TraitModifyLanguages
      languagesSpoken:
        - Sign
      languagesUnderstood:
        - Sign

- type: trait
  id: SolCommon
  category: TraitsSpeechLanguages
  points: 0
  # slots: 1
  # itemGroupSlots: 0
  requirements:
    # - !type:CharacterItemGroupRequirement
    #   group: TraitsLanguagesBasic
    - !type:CharacterSpeciesRequirement
      inverted: true
      species:
        - Human
    - !type:CharacterNationalityRequirement
      inverted: true
      nationalities:
        - Solarian
  functions:
    - !type:TraitModifyLanguages
      languagesSpoken:
        - SolCommon
      languagesUnderstood:
        - SolCommon

- type: trait
  id: Tradeband
  category: TraitsSpeechLanguages
  points: 0
  # slots: 1
  # itemGroupSlots: 0
  requirements:
    # - !type:CharacterItemGroupRequirement
    #   group: TraitsLanguagesBasic
    - !type:CharacterSpeciesRequirement
      inverted: true
      species:
        - Harpy
  functions:
    - !type:TraitModifyLanguages
      languagesSpoken:
        - Tradeband
      languagesUnderstood:
        - Tradeband

- type: trait
  id: Freespeak
  category: TraitsSpeechLanguages
  points: 0
  # slots: 1
  # itemGroupSlots: 0
  # requirements:
  #   - !type:CharacterItemGroupRequirement
  #     group: TraitsLanguagesBasic
  functions:
    - !type:TraitModifyLanguages
      languagesSpoken:
        - Freespeak
      languagesUnderstood:
        - Freespeak

- type: trait
  id: Elyran
  category: TraitsSpeechLanguages
  points: 0
  # slots: 1
  # itemGroupSlots: 0
  requirements:
    # - !type:CharacterItemGroupRequirement
    #   group: TraitsLanguagesBasic
    - !type:CharacterNationalityRequirement
      inverted: true
      nationalities:
        - Elyran
  functions:
    - !type:TraitModifyLanguages
      languagesSpoken:
        - Elyran
      languagesUnderstood:
        - Elyran

- type: trait
  id: ValyrianStandard
  category: TraitsSpeechLanguages
  points: -1
  # slots: 1
  # itemGroupSlots: 0
  requirements:
    # - !type:CharacterItemGroupRequirement
    #   group: TraitsLanguagesBasic
    - !type:CharacterNationalityRequirement
      inverted: true
      nationalities:
        - Valyrian
    - !type:CharacterSpeciesRequirement
      inverted: true
      species:
        - Harpy
  functions:
    - !type:TraitModifyLanguages
      languagesSpoken:
        - ValyrianStandard
      languagesUnderstood:
        - ValyrianStandard

- type: trait
  id: NovuNederic
  category: TraitsSpeechLanguages
  points: 0
  # slots: 1
  # itemGroupSlots: 0
  requirements:
    # - !type:CharacterItemGroupRequirement
    #   group: TraitsLanguagesBasic
    - !type:CharacterNationalityRequirement
      inverted: true
      nationalities:
        - Nederlandic
  functions:
    - !type:TraitModifyLanguages
      languagesSpoken:
        - NovuNederic
      languagesUnderstood:
        - NovuNederic

- type: trait
  id: Azaziba
  category: TraitsSpeechLanguages
  points: -1
  # slots: 1
  # itemGroupSlots: 0
  requirements:
    - !type:CharacterSpeciesRequirement
      species:
        - Reptilian
    # - !type:CharacterItemGroupRequirement
    #   group: TraitsLanguagesBasic
  functions:
    - !type:TraitModifyLanguages
      languagesSpoken:
        - Azaziba
      languagesUnderstood:
        - Azaziba
