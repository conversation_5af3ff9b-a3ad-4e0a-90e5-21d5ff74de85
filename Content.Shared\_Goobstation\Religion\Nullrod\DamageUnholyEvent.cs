// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 SolsticeOfTheWinter <<EMAIL>>
// SPDX-FileCopyrightText: 2025 TheBorzoiMustConsume <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

namespace Content.Shared._Goobstation.Religion.Nullrod;

[ByRefEvent]
public sealed class DamageUnholyEvent(EntityUid target, EntityUid? origin = null) : EntityEventArgs
{
    public readonly EntityUid Target = target;

    public bool ShouldTakeHoly = false;

    public EntityUid? Origin = origin;
}
