using Content.Client.UserInterface.Controls;
using Content.Shared.Damage;
using Content.Shared.IdentityManagement;
using Content.Shared.Medical;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;
using System.Text;


namespace Content.Client.Eye.PenLight.UI
{
    [GenerateTypedNameReferences]
    public sealed partial class PenLightWindow : FancyWindow
    {
        private readonly IEntityManager _entityManager;
        private const int LightHeight = 150;
        private const int LightWidth = 900;

        public PenLightWindow()
        {
            RobustXamlLoader.Load(this);

            var dependencies = IoCManager.Instance!;
            _entityManager = dependencies.Resolve<IEntityManager>();
        }
        public void Diagnose(PenLightUserMessage msg)
        {
            var target = _entityManager.GetEntity(msg.TargetEntity);

            if (target == null || !_entityManager.TryGetComponent<DamageableComponent>(target, out var damageable))
            {
                NoPatientDataText.Visible = true;
                ExamDataLabel.Text = string.Empty;
                return;
            }

            NoPatientDataText.Visible = false;


            string entityName = Loc.GetString("pen-light-window-entity-unknown-text");
            if (_entityManager.HasComponent<MetaDataComponent>(target.Value))
                entityName = Identity.Name(target.Value, _entityManager);

            var sb = new StringBuilder();
            sb.AppendLine(Loc.GetString("pen-light-window-entity-eyes-text", ("entityName", entityName)));

            // Check if Blind and return early if true
            if (msg.Blind == true)
            {
                sb.AppendLine(Loc.GetString("pen-light-exam-blind-text"));
                ExamDataLabel.Text = sb.ToString();
                SetHeight = LightHeight;
                SetWidth = LightWidth;
                return;
            }
            // EyeDamage
            if (msg.EyeDamage == true)
                sb.AppendLine(Loc.GetString("pen-light-exam-eyedamage-text"));

            // Drunk
            if (msg.Drunk == true)
                sb.AppendLine(Loc.GetString("pen-light-exam-drunk-text"));

            // Hallucinating
            if (msg.SeeingRainbows == true)
                sb.AppendLine(Loc.GetString("pen-light-exam-hallucinating-text"));

            // Healthy
            if (msg.Healthy == true)
                sb.AppendLine(Loc.GetString("pen-light-exam-healthy-text"));

            ExamDataLabel.Text = sb.ToString();

            SetHeight = LightHeight;
            SetWidth = LightWidth;
        }
    }
}