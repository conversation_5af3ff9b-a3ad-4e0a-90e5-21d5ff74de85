﻿- type: entity
  id: MachineFlatpacker
  parent: [ BaseMachinePowered, ConstructibleMachine, BaseMaterialSiloUtilizer ]
  name: "флатпакер 1001"
  description: "Промислова машина, що використовується для прискорення будівництва машин на станції."
  components:
  - type: Sprite
    sprite: Structures/Machines/flatpacker.rsi
    snapCardinals: true
    layers:
    - state: base
    - state: screen
      map: ["enum.PowerDeviceVisualLayers.Powered"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
    - state: packing
      map: ["anim"]
      visible: false
    - state: inserting
      visible: false
      map: ["enum.MaterialStorageVisualLayers.Inserting"]
  - type: GenericVisualizer
    visuals:
      enum.PowerDeviceVisuals.Powered:
        enum.PowerDeviceVisualLayers.Powered:
          True: { visible: true }
          False: { visible: false }
      enum.FlatpackCreatorVisuals.Packing:
        anim:
          True: { visible: true }
          False: { visible: false }
  - type: FlatpackCreator
    baseMachineCost:
      Steel: 750
    baseComputerCost:
      Steel: 500
      Glass: 250
  - type: Machine
    board: FlatpackerMachineCircuitboard
  - type: MaterialStorage
    whitelist:
      tags:
      - Sheet
      - RawMaterial
      - Ingot
  - type: AmbientSound
    enabled: false
    volume: 5
    range: 3
    sound:
      path: /Audio/Items/rped.ogg
  - type: WiresPanel
  - type: WiresVisuals
  - type: Appearance
  - type: ActivatableUI
    key: enum.FlatpackCreatorUIKey.Key
  - type: ActivatableUIRequiresPower
  - type: UserInterface
    interfaces:
      enum.FlatpackCreatorUIKey.Key:
        type: FlatpackCreatorBoundUserInterface
  - type: ItemSlots
    slots:
      board_slot:
        name: flatpacker-item-slot-name
        whitelist:
          components:
          - MachineBoard
          - ComputerBoard
  - type: ContainerContainer
    containers:
      machine_board: !type:Container
      machine_parts: !type:Container
      board_slot: !type:ContainerSlot
  - type: Construction
    containers:
    - machine_parts
    - machine_board
    - board_slot
  - type: StaticPrice
    price: 2000

- type: entity
  id: FlatpackerNoBoardEffect
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Structures/Machines/autolathe.rsi
    state: icon
    color: "#222222"
