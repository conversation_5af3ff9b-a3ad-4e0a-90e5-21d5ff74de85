- type: job
  id: Chef
  name: job-name-chef
  description: job-description-chef
  playTimeTracker: JobChef
  requirements:
    - !type:CharacterDepartmentTimeRequirement
      department: Civilian
      min: 3600 #DeltaV 1 hour
    - !type:CharacterEmployerRequirement
      employers:
      - NanoTrasen
      - OrionExpress
      - IdrisIncorporated
  startingGear: ChefGear
  icon: "JobIconChef"
  supervisors: job-supervisors-hop
  access:
  - Service
  - Maintenance
  - Kitchen
  extendedAccess:
  - Hydroponics
  - Bar #Nyano - Summary: After this line, Professional Che is a component to be added. Very important.
  special:
  - !type:AddComponentSpecial
    components:
    - type: ProfessionalChef #Nyano - End Summary.
    - type: GrantCqc # Goobstation - Martial Arts
      isBlocked: true # Goobstation - Martial Arts

- type: startingGear
  id: ChefGear
  subGear:
  - ChefPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitChef
    head: ClothingHeadHatChef
    back: ClothingBackpackFilled
    mask: ClothingMaskItalianMoustache
    shoes: ClothingShoesColorBlack
    id: ChefPDA
    ears: ClothingHeadsetService
    outerClothing: ClothingOuterApronChef
    belt: ClothingBeltChefFilled
  innerClothingSkirt: ClothingUniformJumpskirtChef
  satchel: ClothingBackpackSatchelFilled
  duffelbag: ClothingBackpackDuffelFilled

- type: startingGear
  id: ChefPlasmamanGear
  parent: BasePlasmamanGear
  equipment:
    jumpsuit: ClothingUniformEnvirosuitChef
    head: ClothingHeadEnvirohelmColorWhite
    gloves: ClothingHandsGlovesEnviroglovesWhite
