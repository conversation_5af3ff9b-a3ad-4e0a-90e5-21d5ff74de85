#- type: characterItemGroup
#  id: LoadoutBotanistBackpacks
#  maxItems: 1
#  items:
#
#- type: characterItemGroup
#  id: LoadoutBotanistBelt
#  maxItems: 1
#  items:
#
#- type: characterItemGroup
#  id: LoadoutBotanistEars
#  maxItems: 1
#  items:
#
#- type: characterItemGroup
#  id: LoadoutBotanistEquipment
#  maxItems: 1
#  items:
#
#- type: characterItemGroup
#  id: LoadoutBotanistEyes
#  maxItems: 1
#  items:
#
#- type: characterItemGroup
#  id: LoadoutBotanistGloves
#  maxItems: 1
#  items:
#
#- type: characterItemGroup
#  id: LoadoutBotanistHead
#  maxItems: 1
#  items:
#
#- type: characterItemGroup
#  id: LoadoutBotanistId
#  maxItems: 1
#  items:
#
#- type: characterItemGroup
#  id: LoadoutBotanistNeck
#  maxItems: 1
#  items:
#
#- type: characterItemGroup
#  id: LoadoutBotanistMask
#  maxItems: 1
#  items:
#
- type: characterItemGroup
  id: LoadoutBotanistOuter
  maxItems: 1
  items:
  - type: loadout
    id: LoadoutServiceBotanistWinterCoat
#
#- type: characterItemGroup
#  id: LoadoutBotanistShoes
#  maxItems: 1
#  items:
#
- type: characterItemGroup
  id: LoadoutBotanistUniforms
  maxItems: 1
  items:
    - type: loadout
      id: LoadoutServiceBotanistUniformOveralls
    - type: loadout
      id: LoadoutServiceJumpsuitHydroponicsNt
    - type: loadout
      id: LoadoutServiceJumpsuitHydroponicsIdris
    - type: loadout
      id: LoadoutServiceJumpsuitHydroponicsOrion
    - type: loadout
      id: LoadoutServiceJumpskirtHydroponicsEOC
    - type: loadout
      id: LoadoutServiceJumpsuitHydroponicsEOC
