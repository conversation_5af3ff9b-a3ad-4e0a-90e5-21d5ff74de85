- type: constructionGraph
  id: GasBinary
  start: start
  graph:
  - node: start
    edges:
    - to: pressurepump
      steps:
      - material: Steel
        amount: 2
        doAfter: 1

    - to: volumepump
      steps:
      - material: Steel
        amount: 2
        doAfter: 1

    - to: passivegate
      steps:
      - material: Steel
        amount: 2
        doAfter: 1

    - to: valve
      steps:
      - material: Steel
        amount: 2
        doAfter: 1

    - to: signalvalve
      steps:
      - material: Steel
        amount: 2
        doAfter: 1

    - to: port
      steps:
      - material: Steel
        amount: 2
        doAfter: 1

    - to: dualportventpump
      steps:
      - material: Steel
        amount: 2
        doAfter: 1

    - to: radiator
      steps:
      - material: Steel
        amount: 2
        doAfter: 1

  - node: pressurepump
    entity: GasPressurePump
    edges:
    - to: start
      conditions:
      - !type:EntityAnchored
        anchored: false
      completed:
      - !type:SpawnPrototype
        prototype: SheetSteel1
        amount: 2
      - !type:DeleteEntity
      steps:
      - tool: Welding
        doAfter: 1

  - node: volumepump
    entity: GasVolumePump
    edges:
    - to: start
      conditions:
      - !type:EntityAnchored
        anchored: false
      completed:
      - !type:SpawnPrototype
        prototype: SheetSteel1
        amount: 2
      - !type:DeleteEntity
      steps:
      - tool: Welding
        doAfter: 1

  - node: passivegate
    entity: GasPassiveGate
    edges:
    - to: start
      conditions:
      - !type:EntityAnchored
        anchored: false
      completed:
      - !type:SpawnPrototype
        prototype: SheetSteel1
        amount: 2
      - !type:DeleteEntity
      steps:
      - tool: Welding
        doAfter: 1

  - node: valve
    entity: GasValve
    edges:
    - to: start
      conditions:
      - !type:EntityAnchored
        anchored: false
      completed:
      - !type:SpawnPrototype
        prototype: SheetSteel1
        amount: 2
      - !type:DeleteEntity
      steps:
      - tool: Welding
        doAfter: 1

  - node: signalvalve
    entity: SignalControlledValve
    edges:
    - to: start
      conditions:
      - !type:EntityAnchored
        anchored: false
      completed:
      - !type:SpawnPrototype
        prototype: SheetSteel1
        amount: 2
      - !type:DeleteEntity
      steps:
      - tool: Welding
        doAfter: 1

  - node: port
    entity: GasPort
    edges:
    - to: start
      conditions:
      - !type:EntityAnchored
        anchored: false
      completed:
      - !type:SpawnPrototype
        prototype: SheetSteel1
        amount: 2
      - !type:DeleteEntity
      steps:
      - tool: Welding
        doAfter: 1

  - node: dualportventpump
    entity: GasDualPortVentPump
    edges:
    - to: start
      conditions:
      - !type:EntityAnchored
        anchored: false
      completed:
      - !type:SpawnPrototype
        prototype: SheetSteel1
        amount: 2
      - !type:DeleteEntity
      steps:
      - tool: Screwing
        doAfter: 1
      - tool: Welding
        doAfter: 1

  - node: radiator
    entity: HeatExchanger
    edges:
    - to: start
      conditions:
      - !type:EntityAnchored
        anchored: false
      completed:
      - !type:SpawnPrototype
        prototype: SheetSteel1
        amount: 2
      - !type:DeleteEntity
      steps:
      - tool: Screwing
        doAfter: 1
      - tool: Welding
        doAfter: 1
