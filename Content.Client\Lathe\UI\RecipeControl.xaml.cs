using Content.Shared.Research.Prototypes;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;

namespace Content.Client.Lathe.UI;

[GenerateTypedNameReferences]
public sealed partial class RecipeControl : Control
{
    public Action<string>? OnButtonPressed;
    public Func<string> TooltipTextSupplier;

    public RecipeControl(LatheSystem latheSystem, LatheRecipePrototype recipe, Func<string> tooltipTextSupplier, bool canProduce, Control displayControl)
    {
        RobustXamlLoader.Load(this);

        RecipeName.Text = latheSystem.GetRecipeName(recipe);
        RecipeDisplayContainer.AddChild(displayControl);
        Button.Disabled = !canProduce;
        TooltipTextSupplier = tooltipTextSupplier;
        Button.TooltipSupplier = SupplyTooltip;

        Button.OnPressed += (_) =>
        {
            OnButtonPressed?.Invoke(recipe.ID);
        };
    }

    private Control? SupplyTooltip(Control sender)
    {
        return new RecipeTooltip(TooltipTextSupplier());
    }
}
