# Frontier Mail
- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMailLarge
  id: MailNFInstrumentLarge
  suffix: instrument, large
  components:
  - type: Mail
    contents:
    - id: TromboneInstrument
      orGroup: Instrument
    - id: FrenchHornInstrument
      orGroup: Instrument
    - id: SaxophoneInstrument
      orGroup: Instrument
    - id: EuphoniumInstrument
      orGroup: Instrument
    - id: AcousticGuitarInstrument
      orGroup: Instrument
    - id: ElectricGuitarInstrument
      orGroup: Instrument
    - id: BassGuitarInstrument
      orGroup: Instrument
    - id: RockGuitarInstrument
      orGroup: Instrument
    - id: BanjoInstrument
      orGroup: Instrument
    - id: ViolinInstrument
      orGroup: Instrument
    - id: CelloInstrument
      orGroup: Instrument
    - id: ViolaInstrument
      orGroup: Instrument
    - id: BagpipeInstrument # Fury.
      orGroup: Instrument
    - id: SynthesizerInstrument
      orGroup: Instrument
    - id: AccordionInstrument
      orGroup: Instrument
    - id: GlockenspielInstrument
      orGroup: Instrument
    - id: XylophoneInstrument
      orGroup: Instrument
    # Uncommon
    - id: Rickenbacker4003Instrument
      orGroup: Instrument
      prob: 0.25
    # Rare
    - id: Rickenbacker4001Instrument
      orGroup: Instrument
      prob: 0.1

# Base Nyano Mail

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailBotanistChemicalBottles
  suffix: botanist chemicals
  components:
  - type: Mail
    contents:
    - id: RobustHarvestChemistryBottle
      orGroup: Chemical
      prob: 0.6
    - id: WeedSpray
      orGroup: Chemical
      prob: 0.4

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailBotanistMutagen
  suffix: mutagen
  components:
  - type: Mail
    isFragile: true
    isPriority: true
    contents:
    - id: UnstableMutagenChemistryBottle

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailBotanistSeeds
  suffix: seeds
  components:
  - type: Mail
    contents:
    - id: AloeSeeds
      orGroup: Seeds
    - id: AmbrosiaVulgarisSeeds
      orGroup: Seeds
    - id: AppleSeeds
      orGroup: Seeds
    - id: BananaSeeds
      orGroup: Seeds
    - id: CarrotSeeds
      orGroup: Seeds
    - id: ChanterelleSeeds
      orGroup: Seeds
    - id: ChiliSeeds
      orGroup: Seeds
    - id: CornSeeds
      orGroup: Seeds
    - id: EggplantSeeds
      orGroup: Seeds
    - id: GalaxythistleSeeds
      orGroup: Seeds
    - id: LemonSeeds
      orGroup: Seeds
    - id: LingzhiSeeds
      orGroup: Seeds
    - id: OatSeeds
      orGroup: Seeds
    - id: OnionSeeds
      orGroup: Seeds
    - id: PoppySeeds
      orGroup: Seeds
    - id: PotatoSeeds
      orGroup: Seeds
    - id: SugarcaneSeeds
      orGroup: Seeds
    - id: TomatoSeeds
      orGroup: Seeds
    - id: TowercapSeeds
      orGroup: Seeds
    - id: WheatSeeds
      orGroup: Seeds

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailClownGildedBikeHorn
  suffix: honk
  components:
  - type: Mail
    isFragile: true
    contents:
    - id: BikeHornInstrument

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailClownHonkSupplement
  suffix: honk
  components:
  - type: Mail
    isFragile: true
    contents:
    - id: BikeHorn
    - id: FoodPieBananaCream
    - id: FoodBanana

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailHoPBureaucracy
  suffix: hop paper
  components:
  - type: Mail
    contents:
    - id: Paper
      maxAmount: 3

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailHoPSupplement
  suffix: hop supplement
  components:
  - type: Mail
    contents:
    - id: ClearPDA
    - id: ClothingHeadsetGrey
    - id: Paper

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailMimeArtsCrafts
  suffix: arts and crafts
  components:
  - type: Mail
    contents:
    - id: CrayonBox
    - id: Paper
      maxAmount: 3
    - id: CrayonMagic
    - prob: .2

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailMimeBlankBook
  suffix: blank book
  components:
  - type: Mail
    contents:
      - id: BookRandom

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailMimeBottleOfNothing
  suffix: bottle of nothing
  components:
  - type: Mail
    contents:
      - id: DrinkBottleOfNothingFull

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailPassengerMoney
  suffix: passenger money
  components:
  - type: Mail
    contents:
    - id: SpaceCash100
      orGroup: Cash
      prob: 0.1
      maxAmount: 10
    - id: SpaceCash500
      orGroup: Cash
      prob: 0.3
      maxAmount: 5
    - id: SpaceCash1000
      orGroup: Cash
      prob: 0.6
      maxAmount: 3
