- type: entity
  abstract: true
  id: ReagentDispenserBase
  parent: SmallConstructibleMachine
  placement:
    mode: SnapgridCenter
  components:
  - type: Transform
    anchored: true
    noRot: true
  - type: Clickable
  - type: InteractionOutline
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.3,0.4,0.3"
        density: 190
        mask:
        - TabletopMachineMask
        layer:
        - TabletopMachineLayer
  - type: ApcPowerReceiver
  - type: ExtensionCableReceiver
  - type: ActivatableUI
    key: enum.ReagentDispenserUiKey.Key
  - type: ActivatableUIRequiresPower
  - type: UserInterface
    interfaces:
      enum.ReagentDispenserUiKey.Key:
        type: ReagentDispenserBoundUserInterface
  - type: Anchorable
  - type: Pullable
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 400
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:Do<PERSON><PERSON>Behavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
  - type: ReagentDispenser
    storageWhitelist:
      tags:
      - Bottle
    beakerSlot:
      whitelistFailPopup: reagent-dispenser-component-cannot-put-entity-message
      whitelist:
        components:
        - FitsInDispenser
  - type: ItemSlots
  - type: ContainerContainer
    containers:
      machine_board: !type:Container
      machine_parts: !type:Container
      beakerSlot: !type:ContainerSlot
  - type: StaticPrice
    price: 1000
  - type: WiresPanel
