using Content.Client.UserInterface.Controls;
using Content.Shared._Shitmed.Autodoc;
using Content.Shared._Shitmed.Autodoc.Components;
using Content.Shared._Shitmed.Autodoc.Systems;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Timing;

namespace Content.Client._Shitmed.Autodoc;

[GenerateTypedNameReferences]
public sealed partial class AutodocProgramWindow : FancyWindow
{
    [Dependency] private readonly IEntityManager _entMan = default!;
    private SharedAutodocSystem _autodoc = default!;

    public event Action? OnToggleSafety;
    public event Action? OnRemoveProgram;
    public event Action<IAutodocStep, int>? OnAddStep;
    public event Action<int>? OnRemoveStep;
    public event Action? OnStart;

    private EntityUid _owner;
    private AutodocProgram _program;
    private int _steps;
    private bool _safety = true;

    private int? _selected;
    private AddStepWindow? _addStep;

    public AutodocProgramWindow(EntityUid owner, AutodocProgram program)
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        _autodoc = _entMan.System<SharedAutodocSystem>();

        _owner = owner;
        _program = program;

        OnClose += () => _addStep?.Close();

        SafetyButton.OnPressed += _ =>
        {
            OnToggleSafety?.Invoke();
            program.SkipFailed ^= true;
            UpdateSafety();
        };
        UpdateSafety();

        RemoveButton.OnPressed += _ =>
        {
            OnRemoveProgram?.Invoke();
            Close();
        };

        AddStepButton.OnPressed += _ =>
        {
            if (_addStep is {} window)
            {
                window.MoveToFront();
                return;
            }

            _addStep = new AddStepWindow();
            _addStep.OnAddStep += step =>
            {
                // if nothing is selected add it to the end
                // if something is selected, insert just before it
                var index = _selected ?? program.Steps.Count;
                OnAddStep?.Invoke(step, index);
                _selected = null;
                RemoveButton.Disabled = true;
                program.Steps.Insert(index, step);
                UpdateSteps();
            };
            _addStep.OnClose += () => _addStep = null;
            _addStep.OpenCentered();
        };

        RemoveStepButton.OnPressed += _ =>
        {
            if (_selected is not {} index)
                return;

            _selected = null;
            RemoveStepButton.Disabled = true;
            OnRemoveStep?.Invoke(index);

            // Steps.RemoveChild throws for no fucking reason so rebuild it
            program.Steps.RemoveAt(index);
            UpdateSteps();
        };

        StartButton.OnPressed += _ =>
        {
            OnStart?.Invoke();
            Close();
        };

        Steps.OnItemSelected += args =>
        {
            _selected = args.ItemIndex;
            RemoveStepButton.Disabled = false;
        };
        Steps.OnItemDeselected += _ =>
        {
            _selected = null;
            RemoveStepButton.Disabled = true;
        };

        UpdateSteps();
        UpdateSafety();
    }

    private void UpdateSafety()
    {
        var safety = !_program.SkipFailed;
        if (safety == _safety)
            return;

        _safety = safety;

        SafetyButton.Text = Loc.GetString("autodoc-safety-" + (safety ? "enabled" : "disabled"));
        if (safety)
            SafetyButton.RemoveStyleClass("Danger");
        else
            SafetyButton.AddStyleClass("Danger");
    }

    private void UpdateSteps()
    {
        var count = _program.Steps.Count;
        if (_steps == count)
            return;

        _steps = count;

        Steps.Clear();

        for (int i = 0; i < count; i++)
        {
            Steps.AddItem(_program.Steps[i].Title);
        }

        if (!_entMan.TryGetComponent<AutodocComponent>(_owner, out var comp))
            return;

        AddStepButton.Disabled = count >= comp.MaxProgramSteps;
    }

    private void UpdateStart()
    {
        if (!_entMan.TryGetComponent<AutodocComponent>(_owner, out var comp))
            return;

        StartButton.Disabled = _autodoc.GetPatient((_owner, comp)) == null;
    }

    protected override void FrameUpdate(FrameEventArgs args)
    {
        base.FrameUpdate(args);

        UpdateSteps();
        UpdateSafety();
        UpdateStart();
    }
}
