#TODO: Someone should probably move the ore vein prototypes into their own file, or otherwise split this up in some way. This should not be 1.5k lines long.
#TODO: Please god someone that's NOT me do the Lavaland changes to this file
#TODO: I genuinely cannot do this myself

#Asteroid rock
- type: entity
  id: AsteroidRock
  parent: BaseWall
  name: "астероїдний камінь"
  description: "Кам'янистий астероїд."
  components:
  - type: Transform
    noRot: true
  - type: IconSmooth
    key: walls
    mode: NoSprite
  - type: SmoothEdge
  - type: Icon
    sprite: Structures/Walls/rock.rsi
    state: rock_asteroid
  - type: Sprite
    sprite: Structures/Walls/rock.rsi
    layers:
      - state: rock_asteroid
      - map: [ "enum.EdgeLayer.South" ]
        state: rock_asteroid_south
      - map: [ "enum.EdgeLayer.East" ]
        state: rock_asteroid_east
      - map: [ "enum.EdgeLayer.North" ]
        state: rock_asteroid_north
      - map: [ "enum.EdgeLayer.West" ]
        state: rock_asteroid_west
  - type: SoundOnGather # Goobstation
  - type: Gatherable    # Goobstation
    toolWhitelist:
      tags:
        - Pickaxe
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: Rock
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 120
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          path: /Audio/Effects/break_stone.ogg
          params:
            volume: -6

# Ore veins
- type: entity
  id: AsteroidRockCoal
  parent: AsteroidRock
  description: "Рудна жила, багата на вугілля."
  suffix: Coal
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreCoal
    - type: Sprite
      layers:
        - state: rock_asteroid
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_asteroid_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_asteroid_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_asteroid_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_asteroid_west
        - state: rock_coal

- type: entity
  id: AsteroidRockGold
  parent: AsteroidRock
  description: "Рудна жила, багата на золото."
  suffix: Gold
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreGold
    - type: Sprite
      layers:
        - state: rock_asteroid
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_asteroid_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_asteroid_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_asteroid_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_asteroid_west
        - state: rock_gold

- type: entity
  id: AsteroidRockDiamond
  parent: AsteroidRock
  description: "Рудна жила, багата на алмази."
  suffix: Diamond
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreDiamond
    - type: Sprite
      layers:
        - state: rock_asteroid
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_asteroid_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_asteroid_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_asteroid_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_asteroid_west
        - state: rock_diamond

- type: entity
  id: AsteroidRockPlasma
  parent: AsteroidRock
  description: "Рудна жила, багата на плазму."
  suffix: Plasma
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OrePlasma
    - type: Sprite
      layers:
        - state: rock_asteroid
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_asteroid_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_asteroid_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_asteroid_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_asteroid_west
        - state: rock_phoron

- type: entity
  id: AsteroidRockQuartz
  parent: AsteroidRock
  description: "Рудна жила, багата на кварц."
  suffix: Quartz
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSpaceQuartz
    - type: Sprite
      layers:
        - state: rock_asteroid
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_asteroid_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_asteroid_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_asteroid_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_asteroid_west
        - state: rock_quartz

- type: entity
  id: AsteroidRockQuartzCrab
  parent: AsteroidRock
  description: "Рудна жила, багата на кварц."
  suffix: Quartz Crab
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreQuartzCrab
    - type: Sprite
      layers:
        - state: rock_asteroid
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_asteroid_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_asteroid_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_asteroid_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_asteroid_west
        - state: rock_quartz

- type: entity
  id: AsteroidRockSilver
  parent: AsteroidRock
  description: "Рудна жила, багата на срібло."
  suffix: Silver
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSilver
    - type: Sprite
      layers:
        - state: rock_asteroid
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_asteroid_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_asteroid_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_asteroid_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_asteroid_west
        - state: rock_silver

- type: entity
  id: AsteroidRockSilverCrab
  parent: AsteroidRockSilver
  suffix: Silver Crab
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSilverCrab

# Yes I know it drops steel but we may get smelting at some point
- type: entity
  id: AsteroidRockTin
  parent: AsteroidRock
  description: "Рудна жила, багата на залізо."
  suffix: Iron
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSteel
    - type: Sprite
      layers:
        - state: rock_asteroid
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_asteroid_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_asteroid_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_asteroid_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_asteroid_west
        - state: rock_tin

- type: entity
  id: AsteroidRockTinCrab
  parent: AsteroidRockTin
  suffix: Iron Crab
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreIronCrab

- type: entity
  id: AsteroidRockUranium
  parent: AsteroidRock
  description: "Рудна жила, багата на уран."
  suffix: Uranium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreUranium
    - type: Sprite
      layers:
        - state: rock_asteroid
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_asteroid_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_asteroid_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_asteroid_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_asteroid_west
        - state: rock_uranium

- type: entity
  id: AsteroidRockUraniumCrab
  parent: AsteroidRockUranium
  suffix: Uranium Crab
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreUraniumCrab

- type: entity
  id: AsteroidRockBananium
  parent: AsteroidRock
  description: "Рудна жила, багата на бананіум."
  suffix: Bananium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreBananium
    - type: Sprite
      layers:
        - state: rock_asteroid
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_asteroid_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_asteroid_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_asteroid_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_asteroid_west
        - state: rock_bananium

- type: entity
  id: AsteroidRockArtifactFragment
  parent: AsteroidRock
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Artifact Fragment
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreArtifactFragment
    - type: Sprite
      layers:
        - state: rock_asteroid
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_asteroid_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_asteroid_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_asteroid_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_asteroid_west
        - state: rock_artifact_fragment

- type: entity
  id: AsteroidRockBluespace
  parent: AsteroidRock
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Bluespace
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreBluespace
    - type: Sprite
      layers:
        - state: rock_asteroid
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_asteroid_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_asteroid_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_asteroid_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_asteroid_west
        - state: rock_bluespace

- type: entity
  id: AsteroidRockNormality
  parent: AsteroidRock
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Normality
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreNormality
    - type: Sprite
      layers:
        - state: rock_asteroid
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_asteroid_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_asteroid_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_asteroid_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_asteroid_west
        - state: rock_bluespace

- type: entity
  id: AsteroidRockMining
  parent: AsteroidRock
  name: "астероїдний камінь"
  suffix: higher ore yield .33
  description: "Астероїд."
  components:
  - type: OreVein
    oreChance: 0.33
    oreRarityPrototypeId: RandomOreDistributionStandard

- type: entity
  id: IronRock
  parent: AsteroidRock
  name: "айронрок"
  suffix: Low Ore Yield
  description: "Кам'янистий астероїд."
  components:
  - type: IconSmooth
    key: walls
    mode: NoSprite
  - type: SmoothEdge
  - type: Sprite
    sprite: Structures/Walls/rock.rsi
    layers:
      - state: ironrock
      - map: [ "enum.EdgeLayer.South" ]
        state: ironrock_south
      - map: [ "enum.EdgeLayer.East" ]
        state: ironrock_east
      - map: [ "enum.EdgeLayer.North" ]
        state: ironrock_north
      - map: [ "enum.EdgeLayer.West" ]
        state: ironrock_west

- type: entity
  id: AsteroidRockSalt
  parent: AsteroidRock
  description: "Рудна жила, багата на сіль."
  suffix: Salt
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSalt
    - type: Sprite
      layers:
        - state: rock_asteroid
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_asteroid_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_asteroid_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_asteroid_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_asteroid_west
        - state: rock_salt

- type: entity
  id: IronRockMining
  parent: IronRock
  name: "айронрок"
  suffix: higher ore yield
  description: "Астероїд."
  components:
  - type: OreVein
    oreChance: 0.33
    oreRarityPrototypeId: RandomOreDistributionStandard

- type: entity
  id: AsteroidRockAluminium
  parent: AsteroidRock
  description: "Рудна жила, багата на боксити."
  suffix: Aluminium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreAluminium
    - type: Sprite
      layers:
        - state: rock_asteroid
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_asteroid_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_asteroid_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_asteroid_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_asteroid_west
        - state: rock_bauxite

- type: entity
  id: AsteroidRockLead
  parent: AsteroidRock
  description: "Рудна жила, багата на каситерит."
  suffix: Lead
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreLead
    - type: Sprite
      layers:
        - state: rock_asteroid
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_asteroid_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_asteroid_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_asteroid_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_asteroid_west
        - state: rock_lead

- type: entity
  id: AsteroidRockCopper
  parent: AsteroidRock
  description: "Рудна жила, багата на самородну мідь."
  suffix: Copper
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreCopper
    - type: Sprite
      layers:
        - state: rock_asteroid
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_asteroid_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_asteroid_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_asteroid_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_asteroid_west
        - state: rock_copper

- type: entity
  id: AsteroidRockTungsten
  parent: AsteroidRock
  description: "Рудна жила, багата на вольфраміт."
  suffix: Tungsten
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreTungsten
    - type: Sprite
      layers:
        - state: rock_asteroid
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_asteroid_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_asteroid_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_asteroid_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_asteroid_west
        - state: rock_copper


# Rocks and ore veins
- type: entity
  id: WallRock
  parent: BaseWall
  name: "скеля"
  suffix: planetmap
  components:
    - type: Transform
      noRot: true
    - type: SoundOnGather
    - type: Gatherable
      toolWhitelist:
        tags:
          - Pickaxe
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: Metallic
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 150
          behaviors:
            - !type:DoActsBehavior
              acts: ["Destruction"]
    - type: IconSmooth
      key: walls
      mode: NoSprite
    - type: Icon
      sprite: Structures/Walls/rock.rsi
      state: rock
    - type: SmoothEdge
    - type: Sprite
      sprite: Structures/Walls/rock.rsi
      layers:
        - state: rock
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_west

# Ore veins
- type: entity
  id: WallRockCoal
  parent: WallRock
  description: "Рудна жила, багата на вугілля."
  suffix: Coal
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreCoal
    - type: Sprite
      layers:
        - state: rock
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_west
        - state: rock_coal

- type: entity
  id: WallRockGold
  parent: WallRock
  description: "Рудна жила, багата на золото."
  suffix: Gold
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreGold
    - type: Sprite
      layers:
        - state: rock
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_west
        - state: rock_gold

- type: entity
  id: WallRockDiamond
  parent: WallRock
  description: "Рудна жила, багата на алмази."
  suffix: Diamond
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreDiamond
    - type: Sprite
      layers:
        - state: rock
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_west
        - state: rock_diamond

- type: entity
  id: WallRockPlasma
  parent: WallRock
  description: "Рудна жила, багата на плазму."
  suffix: Plasma
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OrePlasma
    - type: Sprite
      layers:
        - state: rock
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_west
        - state: rock_phoron

- type: entity
  id: WallRockQuartz
  parent: WallRock
  description: "Рудна жила, багата на кварц."
  suffix: Quartz
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSpaceQuartz
    - type: Sprite
      layers:
        - state: rock
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_west
        - state: rock_quartz

- type: entity
  id: WallRockSilver
  parent: WallRock
  description: "Рудна жила, багата на срібло."
  suffix: Silver
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSilver
    - type: Sprite
      layers:
        - state: rock
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_west
        - state: rock_silver

# Yes I know it drops steel but we may get smelting at some point
- type: entity
  id: WallRockTin
  parent: WallRock
  description: "Рудна жила, багата на залізо."
  suffix: Iron
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSteel
    - type: Sprite
      layers:
        - state: rock
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_west
        - state: rock_tin

- type: entity
  id: WallRockUranium
  parent: WallRock
  description: "Рудна жила, багата на уран."
  suffix: Uranium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreUranium
    - type: Sprite
      layers:
        - state: rock
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_west
        - state: rock_uranium


- type: entity
  id: WallRockBananium
  parent: WallRock
  description: "Рудна жила, багата на бананіум."
  suffix: Bananium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreBananium
    - type: Sprite
      layers:
        - state: rock
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_west
        - state: rock_bananium

- type: entity
  id: WallRockArtifactFragment
  parent: WallRock
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Artifact Fragment
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreArtifactFragment
    - type: Sprite
      layers:
        - state: rock
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_west
        - state: rock_artifact_fragment

- type: entity
  id: WallRockSalt
  parent: WallRock
  description: "Рудна жила, багата на сіль."
  suffix: Salt
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSalt
    - type: Sprite
      layers:
        - state: rock
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_west
        - state: rock_salt

- type: entity
  id: WallRockBluespace
  parent: WallRock
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Bluespace
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreBluespace
    - type: Sprite
      layers:
        - state: rock
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_west
        - state: rock_bluespace

- type: entity
  id: WallRockNormality
  parent: WallRock
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Normality
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreNormality
    - type: Sprite
      layers:
        - state: rock
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_west
        - state: rock_bluespace

- type: entity
  id: WallRockAluminium
  parent: WallRock
  description: "Рудна жила, багата на боксити."
  suffix: Aluminium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreAluminium
    - type: Sprite
      layers:
        - state: rock
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_west
        - state: rock_bauxite

- type: entity
  id: WallRockLead
  parent: WallRock
  description: "Рудна жила, багата на каситерит."
  suffix: Lead
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreLead
    - type: Sprite
      layers:
        - state: rock
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_west
        - state: rock_lead

- type: entity
  id: WallRockCopper
  parent: WallRock
  description: "Рудна жила, багата на самородну мідь."
  suffix: Copper
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreCopper
    - type: Sprite
      layers:
        - state: rock
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_west
        - state: rock_copper

- type: entity
  id: WallRockTungsten
  parent: WallRock
  description: "Рудна жила, багата на вольфраміт."
  suffix: Tungsten
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreTungsten
    - type: Sprite
      layers:
        - state: rock
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_west
        - state: rock_copper

# Basalt variants
- type: entity
  id: WallRockBasalt
  name: "базальт"
  parent: WallRock
  components:
    - type: Icon
      sprite: Structures/Walls/rock.rsi
      state: rock_wall
    - type: Sprite
      sprite: Structures/Walls/rock.rsi
      layers:
        - state: rock_wall
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_wall_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_wall_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_wall_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_wall_west

- type: entity
  id: WallRockBasaltCoal
  parent: WallRockBasalt
  description: "Рудна жила, багата на вугілля."
  suffix: Coal
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreCoal
    - type: Sprite
      layers:
        - state: rock_wall
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_wall_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_wall_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_wall_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_wall_west
        - state: rock_coal

- type: entity
  id: WallRockBasaltGold
  parent: WallRockBasalt
  description: "Рудна жила, багата на золото."
  suffix: Gold
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreGold
    - type: Sprite
      layers:
        - state: rock_wall
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_wall_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_wall_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_wall_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_wall_west
        - state: rock_gold

- type: entity
  id: WallRockBasaltDiamond
  parent: WallRockBasalt
  description: "Рудна жила, багата на алмази."
  suffix: Diamond
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreDiamond
    - type: Sprite
      layers:
        - state: rock_wall
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_wall_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_wall_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_wall_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_wall_west
        - state: rock_diamond

- type: entity
  id: WallRockBasaltPlasma
  parent: WallRockBasalt
  description: "Рудна жила, багата на плазму."
  suffix: Plasma
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OrePlasma
    - type: Sprite
      layers:
        - state: rock_wall
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_wall_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_wall_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_wall_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_wall_west
        - state: rock_phoron

- type: entity
  id: WallRockBasaltQuartz
  parent: WallRockBasalt
  description: "Рудна жила, багата на кварц."
  suffix: Quartz
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSpaceQuartz
    - type: Sprite
      layers:
        - state: rock_wall
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_wall_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_wall_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_wall_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_wall_west
        - state: rock_quartz

- type: entity
  id: WallRockBasaltSilver
  parent: WallRockBasalt
  description: "Рудна жила, багата на срібло."
  suffix: Silver
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSilver
    - type: Sprite
      layers:
        - state: rock_wall
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_wall_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_wall_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_wall_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_wall_west
        - state: rock_silver

- type: entity
  id: WallRockBasaltTin
  parent: WallRockBasalt
  description: "Рудна жила, багата на залізо."
  suffix: Iron
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSteel
    - type: Sprite
      layers:
        - state: rock_wall
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_wall_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_wall_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_wall_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_wall_west
        - state: rock_tin

- type: entity
  id: WallRockBasaltUranium
  parent: WallRockBasalt
  description: "Рудна жила, багата на уран."
  suffix: Uranium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreUranium
    - type: Sprite
      layers:
        - state: rock_wall
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_wall_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_wall_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_wall_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_wall_west
        - state: rock_uranium


- type: entity
  id: WallRockBasaltBananium
  parent: WallRockBasalt
  description: "Рудна жила, багата на бананіум."
  suffix: Bananium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreBananium
    - type: Sprite
      layers:
        - state: rock_wall
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_wall_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_wall_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_wall_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_wall_west
        - state: rock_bananium

- type: entity
  id: WallRockBasaltArtifactFragment
  parent: WallRockBasalt
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Artifact Fragment
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreArtifactFragment
    - type: Sprite
      layers:
        - state: rock_wall
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_wall_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_wall_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_wall_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_wall_west
        - state: rock_artifact_fragment

- type: entity
  id: WallRockBasaltSalt
  parent: WallRockBasalt
  description: "Рудна жила, багата на сіль."
  suffix: Salt
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSalt
    - type: Sprite
      layers:
        - state: rock_wall
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_wall_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_wall_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_wall_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_wall_west
        - state: rock_salt

- type: entity
  id: WallRockBasaltBluespace
  parent: WallRockBluespace
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Bluespace
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreBluespace
    - type: Sprite
      layers:
        - state: rock_wall
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_wall_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_wall_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_wall_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_wall_west
        - state: rock_bluespace

- type: entity
  id: WallRockBasaltNormality
  parent: WallRockNormality
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Normality
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreNormality
    - type: Sprite
      layers:
        - state: rock_wall
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_wall_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_wall_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_wall_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_wall_west
        - state: rock_bluespace

- type: entity
  id: WallRockBasaltAluminium
  parent: WallRockBasalt
  description: "Рудна жила, багата на боксити."
  suffix: Aluminium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreAluminium
    - type: Sprite
      layers:
        - state: rock_wall
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_wall_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_wall_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_wall_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_wall_west
        - state: rock_bauxite

- type: entity
  id: WallRockBasaltLead
  parent: WallRockBasalt
  description: "Рудна жила, багата на каситерит."
  suffix: Lead
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreLead
    - type: Sprite
      layers:
        - state: rock_wall
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_wall_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_wall_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_wall_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_wall_west
        - state: rock_lead

- type: entity
  id: WallRockBasaltCopper
  parent: WallRockBasalt
  description: "Рудна жила, багата на самородну мідь."
  suffix: Copper
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreCopper
    - type: Sprite
      layers:
        - state: rock_wall
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_wall_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_wall_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_wall_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_wall_west
        - state: rock_copper

- type: entity
  id: WallRockBasaltTungsten
  parent: WallRockBasalt
  description: "Рудна жила, багата на вольфраміт."
  suffix: Tungsten
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreTungsten
    - type: Sprite
      layers:
        - state: rock_wall
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_wall_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_wall_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_wall_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_wall_west
        - state: rock_copper

# Snow variants
- type: entity
  id: WallRockSnow
  name: "сніговий замет"
  parent: WallRock
  components:
    - type: Icon
      sprite: Structures/Walls/rock.rsi
      state: rock_snow
    - type: Sprite
      sprite: Structures/Walls/rock.rsi
      layers:
        - state: rock_snow
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_snow_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_snow_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_snow_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_snow_west

- type: entity
  id: WallRockSnowCoal
  parent: WallRockSnow
  description: "Рудна жила, багата на вугілля."
  suffix: Coal
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreCoal
    - type: Sprite
      layers:
        - state: rock_snow
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_snow_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_snow_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_snow_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_snow_west
        - state: rock_coal

- type: entity
  id: WallRockSnowGold
  parent: WallRockSnow
  description: "Рудна жила, багата на золото."
  suffix: Gold
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreGold
    - type: Sprite
      layers:
        - state: rock_snow
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_snow_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_snow_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_snow_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_snow_west
        - state: rock_gold

- type: entity
  id: WallRockSnowDiamond
  parent: WallRockSnow
  description: "Рудна жила, багата на алмази."
  suffix: Diamond
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreDiamond
    - type: Sprite
      layers:
        - state: rock_snow
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_snow_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_snow_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_snow_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_snow_west
        - state: rock_diamond

- type: entity
  id: WallRockSnowPlasma
  parent: WallRockSnow
  description: "Рудна жила, багата на плазму."
  suffix: Plasma
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OrePlasma
    - type: Sprite
      layers:
        - state: rock_snow
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_snow_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_snow_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_snow_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_snow_west
        - state: rock_phoron

- type: entity
  id: WallRockSnowQuartz
  parent: WallRockSnow
  description: "Рудна жила, багата на кварц."
  suffix: Quartz
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSpaceQuartz
    - type: Sprite
      layers:
        - state: rock_snow
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_snow_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_snow_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_snow_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_snow_west
        - state: rock_quartz

- type: entity
  id: WallRockSnowSilver
  parent: WallRockSnow
  description: "Рудна жила, багата на срібло."
  suffix: Silver
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSilver
    - type: Sprite
      layers:
        - state: rock_snow
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_snow_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_snow_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_snow_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_snow_west
        - state: rock_silver

- type: entity
  id: WallRockSnowTin
  parent: WallRockSnow
  description: "Рудна жила, багата на залізо."
  suffix: Iron
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSteel
    - type: Sprite
      layers:
        - state: rock_snow
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_snow_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_snow_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_snow_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_snow_west
        - state: rock_tin

- type: entity
  id: WallRockSnowUranium
  parent: WallRockSnow
  description: "Рудна жила, багата на уран."
  suffix: Uranium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreUranium
    - type: Sprite
      layers:
        - state: rock_snow
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_snow_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_snow_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_snow_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_snow_west
        - state: rock_uranium


- type: entity
  id: WallRockSnowBananium
  parent: WallRockSnow
  description: "Рудна жила, багата на бананіум."
  suffix: Bananium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreBananium
    - type: Sprite
      layers:
        - state: rock_snow
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_snow_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_snow_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_snow_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_snow_west
        - state: rock_bananium

- type: entity
  id: WallRockSnowArtifactFragment
  parent: WallRockSnow
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Artifact Fragment
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreArtifactFragment
    - type: Sprite
      layers:
        - state: rock_snow
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_snow_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_snow_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_snow_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_snow_west
        - state: rock_artifact_fragment

- type: entity
  id: WallRockSnowSalt
  parent: WallRockSnow
  description: "Рудна жила, багата на сіль."
  suffix: Salt
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSalt
    - type: Sprite
      layers:
        - state: rock_snow
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_snow_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_snow_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_snow_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_snow_west
        - state: rock_salt

- type: entity
  id: WallRockSnowBluespace
  parent: WallRockSnow
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Bluespace
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreBluespace
    - type: Sprite
      layers:
        - state: rock_snow
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_snow_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_snow_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_snow_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_snow_west
        - state: rock_bluespace

- type: entity
  id: WallRockSnowNormality
  parent: WallRockSnow
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Normality
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreNormality
    - type: Sprite
      layers:
        - state: rock_snow
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_snow_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_snow_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_snow_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_snow_west
        - state: rock_bluespace

- type: entity
  id: WallRockSnowAluminium
  parent: WallRockSnow
  description: "Рудна жила, багата на боксити."
  suffix: Aluminium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreAluminium
    - type: Sprite
      layers:
        - state: rock_snow
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_snow_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_snow_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_snow_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_snow_west
        - state: rock_bauxite

- type: entity
  id: WallRockSnowLead
  parent: WallRockSnow
  description: "Рудна жила, багата на каситерит."
  suffix: Lead
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreLead
    - type: Sprite
      layers:
        - state: rock_snow
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_snow_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_snow_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_snow_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_snow_west
        - state: rock_lead

- type: entity
  id: WallRockSnowCopper
  parent: WallRockSnow
  description: "Рудна жила, багата на самородну мідь."
  suffix: Copper
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreCopper
    - type: Sprite
      layers:
        - state: rock_snow
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_snow_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_snow_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_snow_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_snow_west
        - state: rock_copper

- type: entity
  id: WallRockSnowTungsten
  parent: WallRockSnow
  description: "Рудна жила, багата на вольфраміт."
  suffix: Tungsten
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreTungsten
    - type: Sprite
      layers:
        - state: rock_snow
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_snow_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_snow_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_snow_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_snow_west
        - state: rock_copper

# Sand variants
- type: entity
  id: WallRockSand
  name: "пісковик"
  parent: WallRock
  components:
    - type: Icon
      sprite: Structures/Walls/rock.rsi
      state: rock_sand
    - type: Sprite
      sprite: Structures/Walls/rock.rsi
      layers:
        - state: rock_sand
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_sand_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_sand_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_sand_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_sand_west

- type: entity
  id: WallRockSandCoal
  parent: WallRockSand
  description: "Рудна жила, багата на вугілля."
  suffix: Coal
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreCoal
    - type: Sprite
      layers:
        - state: rock_sand
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_sand_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_sand_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_sand_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_sand_west
        - state: rock_coal

- type: entity
  id: WallRockSandGold
  parent: WallRockSand
  description: "Рудна жила, багата на золото."
  suffix: Gold
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreGold
    - type: Sprite
      layers:
        - state: rock_sand
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_sand_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_sand_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_sand_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_sand_west
        - state: rock_gold

- type: entity
  id: WallRockSandDiamond
  parent: WallRockSand
  description: "Рудна жила, багата на алмази."
  suffix: Diamond
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreDiamond
    - type: Sprite
      layers:
        - state: rock_sand
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_sand_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_sand_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_sand_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_sand_west
        - state: rock_diamond

- type: entity
  id: WallRockSandPlasma
  parent: WallRockSand
  description: "Рудна жила, багата на плазму."
  suffix: Plasma
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OrePlasma
    - type: Sprite
      layers:
        - state: rock_sand
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_sand_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_sand_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_sand_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_sand_west
        - state: rock_phoron

- type: entity
  id: WallRockSandQuartz
  parent: WallRockSand
  description: "Рудна жила, багата на кварц."
  suffix: Quartz
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSpaceQuartz
    - type: Sprite
      layers:
        - state: rock_sand
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_sand_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_sand_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_sand_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_sand_west
        - state: rock_quartz

- type: entity
  id: WallRockSandSilver
  parent: WallRockSand
  description: "Рудна жила, багата на срібло."
  suffix: Silver
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSilver
    - type: Sprite
      layers:
        - state: rock_sand
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_sand_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_sand_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_sand_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_sand_west
        - state: rock_silver

- type: entity
  id: WallRockSandTin
  parent: WallRockSand
  description: "Рудна жила, багата на залізо."
  suffix: Iron
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSteel
    - type: Sprite
      layers:
        - state: rock_sand
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_sand_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_sand_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_sand_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_sand_west
        - state: rock_tin

- type: entity
  id: WallRockSandUranium
  parent: WallRockSand
  description: "Рудна жила, багата на уран."
  suffix: Uranium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreUranium
    - type: Sprite
      layers:
        - state: rock_sand
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_sand_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_sand_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_sand_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_sand_west
        - state: rock_uranium

- type: entity
  id: WallRockSandBananium
  parent: WallRockSand
  description: "Рудна жила, багата на бананіум."
  suffix: Bananium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreBananium
    - type: Sprite
      layers:
        - state: rock_sand
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_sand_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_sand_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_sand_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_sand_west
        - state: rock_bananium

- type: entity
  id: WallRockSandArtifactFragment
  parent: WallRockSand
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Artifact Fragment
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreArtifactFragment
    - type: Sprite
      layers:
        - state: rock_sand
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_sand_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_sand_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_sand_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_sand_west
        - state: rock_artifact_fragment

- type: entity
  id: WallRockSandSalt
  parent: WallRockSand
  description: "Рудна жила, багата на сіль."
  suffix: Salt
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSalt
    - type: Sprite
      layers:
        - state: rock_sand
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_sand_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_sand_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_sand_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_sand_west
        - state: rock_salt

- type: entity
  id: WallRockSandBluespace
  parent: WallRockSand
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Bluespace
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreBluespace
    - type: Sprite
      layers:
        - state: rock_sand
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_sand_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_sand_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_sand_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_sand_west
        - state: rock_bluespace

- type: entity
  id: WallRockSandNormality
  parent: WallRockSand
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Normality
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreNormality
    - type: Sprite
      layers:
        - state: rock_sand
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_sand_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_sand_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_sand_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_sand_west
        - state: rock_bluespace

- type: entity
  id: WallRockSandAluminium
  parent: WallRockSand
  description: "Рудна жила, багата на боксити."
  suffix: Aluminium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreAluminium
    - type: Sprite
      layers:
        - state: rock_sand
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_sand_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_sand_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_sand_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_sand_west
        - state: rock_bauxite

- type: entity
  id: WallRockSandLead
  parent: WallRockSand
  description: "Рудна жила, багата на каситерит."
  suffix: Lead
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreLead
    - type: Sprite
      layers:
        - state: rock_sand
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_sand_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_sand_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_sand_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_sand_west
        - state: rock_lead

- type: entity
  id: WallRockSandCopper
  parent: WallRockSand
  description: "Рудна жила, багата на самородну мідь."
  suffix: Copper
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreCopper
    - type: Sprite
      layers:
        - state: rock_sand
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_sand_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_sand_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_sand_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_sand_west
        - state: rock_copper

- type: entity
  id: WallRockSandTungsten
  parent: WallRockSand
  description: "Рудна жила, багата на вольфраміт."
  suffix: Tungsten
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreTungsten
    - type: Sprite
      layers:
        - state: rock_sand
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_sand_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_sand_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_sand_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_sand_west
        - state: rock_copper

# Chromite variants
- type: entity
  id: WallRockChromite
  name: "хроміт"
  parent: WallRock
  components:
    - type: Icon
      sprite: Structures/Walls/rock.rsi
      state: rock_chromite
    - type: Sprite
      sprite: Structures/Walls/rock.rsi
      layers:
        - state: rock_chromite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_chromite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_chromite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_chromite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_chromite_west

- type: entity
  id: WallRockChromiteCoal
  parent: WallRockChromite
  description: "Рудна жила, багата на вугілля."
  suffix: Coal
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreCoal
    - type: Sprite
      layers:
        - state: rock_chromite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_chromite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_chromite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_chromite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_chromite_west
        - state: rock_coal

- type: entity
  id: WallRockChromiteGold
  parent: WallRockChromite
  description: "Рудна жила, багата на золото."
  suffix: Gold
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreGold
    - type: Sprite
      layers:
        - state: rock_chromite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_chromite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_chromite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_chromite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_chromite_west
        - state: rock_gold

- type: entity
  id: WallRockChromiteDiamond
  parent: WallRockChromite
  description: "Рудна жила, багата на алмази."
  suffix: Diamond
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreDiamond
    - type: Sprite
      layers:
        - state: rock_chromite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_chromite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_chromite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_chromite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_chromite_west
        - state: rock_diamond

- type: entity
  id: WallRockChromitePlasma
  parent: WallRockChromite
  description: "Рудна жила, багата на плазму."
  suffix: Plasma
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OrePlasma
    - type: Sprite
      layers:
        - state: rock_chromite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_chromite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_chromite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_chromite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_chromite_west
        - state: rock_phoron

- type: entity
  id: WallRockChromiteQuartz
  parent: WallRockChromite
  description: "Рудна жила, багата на кварц."
  suffix: Quartz
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSpaceQuartz
    - type: Sprite
      layers:
        - state: rock_chromite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_chromite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_chromite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_chromite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_chromite_west
        - state: rock_quartz

- type: entity
  id: WallRockChromiteSilver
  parent: WallRockChromite
  description: "Рудна жила, багата на срібло."
  suffix: Silver
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSilver
    - type: Sprite
      layers:
        - state: rock_chromite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_chromite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_chromite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_chromite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_chromite_west
        - state: rock_silver

- type: entity
  id: WallRockChromiteTin
  parent: WallRockChromite
  description: "Рудна жила, багата на залізо."
  suffix: Iron
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSteel
    - type: Sprite
      layers:
        - state: rock_chromite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_chromite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_chromite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_chromite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_chromite_west
        - state: rock_tin

- type: entity
  id: WallRockChromiteUranium
  parent: WallRockChromite
  description: "Рудна жила, багата на уран."
  suffix: Uranium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreUranium
    - type: Sprite
      layers:
        - state: rock_chromite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_chromite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_chromite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_chromite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_chromite_west
        - state: rock_uranium


- type: entity
  id: WallRockChromiteBananium
  parent: WallRockChromite
  description: "Рудна жила, багата на бананіум."
  suffix: Bananium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreBananium
    - type: Sprite
      layers:
        - state: rock_chromite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_chromite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_chromite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_chromite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_chromite_west
        - state: rock_bananium

- type: entity
  id: WallRockChromiteArtifactFragment
  parent: WallRockChromite
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Artifact Fragment
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreArtifactFragment
    - type: Sprite
      layers:
        - state: rock_chromite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_chromite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_chromite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_chromite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_chromite_west
        - state: rock_artifact_fragment

- type: entity
  id: WallRockChromiteSalt
  parent: WallRockChromite
  description: "Рудна жила, багата на сіль."
  suffix: Salt
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSalt
    - type: Sprite
      layers:
        - state: rock_chromite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_chromite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_chromite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_chromite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_chromite_west
        - state: rock_salt

- type: entity
  id: WallRockChromiteBluespace
  parent: WallRockChromite
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Bluespace
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreBluespace
    - type: Sprite
      layers:
        - state: rock_chromite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_chromite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_chromite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_chromite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_chromite_west
        - state: rock_bluespace

- type: entity
  id: WallRockChromiteNormality
  parent: WallRockChromite
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Normality
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreNormality
    - type: Sprite
      layers:
        - state: rock_chromite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_chromite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_chromite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_chromite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_chromite_west
        - state: rock_bluespace

- type: entity
  id: WallRockChromiteAluminium
  parent: WallRockChromite
  description: "Рудна жила, багата на боксити."
  suffix: Aluminium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreAluminium
    - type: Sprite
      layers:
        - state: rock_chromite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_chromite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_chromite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_chromite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_chromite_west
        - state: rock_bauxite

- type: entity
  id: WallRockChromiteLead
  parent: WallRockChromite
  description: "Рудна жила, багата на каситерит."
  suffix: Lead
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreLead
    - type: Sprite
      layers:
        - state: rock_chromite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_chromite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_chromite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_chromite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_chromite_west
        - state: rock_lead

- type: entity
  id: WallRockChromiteCopper
  parent: WallRockChromite
  description: "Рудна жила, багата на самородну мідь."
  suffix: Copper
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreCopper
    - type: Sprite
      layers:
        - state: rock_chromite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_chromite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_chromite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_chromite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_chromite_west
        - state: rock_copper

- type: entity
  id: WallRockChromiteTungsten
  parent: WallRockChromite
  description: "Рудна жила, багата на вольфраміт."
  suffix: Tungsten
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreTungsten
    - type: Sprite
      layers:
        - state: rock_chromite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_chromite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_chromite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_chromite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_chromite_west
        - state: rock_copper

# Andesite variants
- type: entity
  id: WallRockAndesite
  name: "андезит"
  parent: WallRock
  components:
    - type: Icon
      sprite: Structures/Walls/rock.rsi
      state: rock_andesite
    - type: Sprite
      sprite: Structures/Walls/rock.rsi
      layers:
        - state: rock_andesite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_andesite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_andesite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_andesite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_andesite_west

- type: entity
  id: WallRockAndesiteCoal
  parent: WallRockAndesite
  description: "Рудна жила, багата на вугілля."
  suffix: Coal
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreCoal
    - type: Sprite
      layers:
        - state: rock_andesite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_andesite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_andesite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_andesite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_andesite_west
        - state: rock_coal

- type: entity
  id: WallRockAndesiteGold
  parent: WallRockAndesite
  description: "Рудна жила, багата на золото."
  suffix: Gold
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreGold
    - type: Sprite
      layers:
        - state: rock_andesite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_andesite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_andesite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_andesite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_andesite_west
        - state: rock_gold

- type: entity
  id: WallRockAndesiteDiamond
  parent: WallRockAndesite
  description: "Рудна жила, багата на алмази."
  suffix: Diamond
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreDiamond
    - type: Sprite
      layers:
        - state: rock_andesite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_andesite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_andesite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_andesite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_andesite_west
        - state: rock_diamond

- type: entity
  id: WallRockAndesitePlasma
  parent: WallRockAndesite
  description: "Рудна жила, багата на плазму."
  suffix: Plasma
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OrePlasma
    - type: Sprite
      layers:
        - state: rock_andesite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_andesite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_andesite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_andesite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_andesite_west
        - state: rock_phoron

- type: entity
  id: WallRockAndesiteQuartz
  parent: WallRockAndesite
  description: "Рудна жила, багата на кварц."
  suffix: Quartz
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSpaceQuartz
    - type: Sprite
      layers:
        - state: rock_andesite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_andesite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_andesite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_andesite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_andesite_west
        - state: rock_quartz

- type: entity
  id: WallRockAndesiteSilver
  parent: WallRockAndesite
  description: "Рудна жила, багата на срібло."
  suffix: Silver
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSilver
    - type: Sprite
      layers:
        - state: rock_andesite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_andesite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_andesite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_andesite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_andesite_west
        - state: rock_silver

- type: entity
  id: WallRockAndesiteTin
  parent: WallRockAndesite
  description: "Рудна жила, багата на залізо."
  suffix: Iron
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSteel
    - type: Sprite
      layers:
        - state: rock_andesite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_andesite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_andesite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_andesite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_andesite_west
        - state: rock_tin

- type: entity
  id: WallRockAndesiteUranium
  parent: WallRockAndesite
  description: "Рудна жила, багата на уран."
  suffix: Uranium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreUranium
    - type: Sprite
      layers:
        - state: rock_andesite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_andesite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_andesite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_andesite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_andesite_west
        - state: rock_uranium


- type: entity
  id: WallRockAndesiteBananium
  parent: WallRockAndesite
  description: "Рудна жила, багата на бананіум."
  suffix: Bananium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreBananium
    - type: Sprite
      layers:
        - state: rock_andesite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_andesite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_andesite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_andesite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_andesite_west
        - state: rock_bananium

- type: entity
  id: WallRockAndesiteArtifactFragment
  parent: WallRockAndesite
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Artifact Fragment
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreArtifactFragment
    - type: Sprite
      layers:
        - state: rock_andesite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_andesite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_andesite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_andesite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_andesite_west
        - state: rock_artifact_fragment

- type: entity
  id: WallRockAndesiteSalt
  parent: WallRockAndesite
  description: "Рудна жила, багата на сіль."
  suffix: Salt
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreSalt
    - type: Sprite
      layers:
        - state: rock_andesite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_andesite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_andesite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_andesite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_andesite_west
        - state: rock_salt

- type: entity
  id: WallRockAndesiteBluespace
  parent: WallRockAndesite
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Bluespace
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreBluespace
    - type: Sprite
      layers:
        - state: rock_andesite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_andesite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_andesite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_andesite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_andesite_west
        - state: rock_bluespace

- type: entity
  id: WallRockAndesiteNormality
  parent: WallRockAndesite
  description: "Кам'яна стіна. Що це з неї стирчить?"
  suffix: Normality
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreNormality
    - type: Sprite
      layers:
        - state: rock_andesite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_andesite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_andesite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_andesite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_andesite_west
        - state: rock_bluespace

- type: entity
  id: WallRockAndesiteAluminium
  parent: WallRockAndesite
  description: "Рудна жила, багата на боксити."
  suffix: Aluminium
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreAluminium
    - type: Sprite
      layers:
        - state: rock_andesite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_andesite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_andesite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_andesite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_andesite_west
        - state: rock_bauxite

- type: entity
  id: WallRockAndesiteLead
  parent: WallRockAndesite
  description: "Рудна жила, багата на каситерит."
  suffix: Lead
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreLead
    - type: Sprite
      layers:
        - state: rock_andesite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_andesite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_andesite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_andesite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_andesite_west
        - state: rock_lead

- type: entity
  id: WallRockAndesiteCopper
  parent: WallRockAndesite
  description: "Рудна жила, багата на самородну мідь."
  suffix: Copper
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreCopper
    - type: Sprite
      layers:
        - state: rock_andesite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_andesite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_andesite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_andesite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_andesite_west
        - state: rock_copper

- type: entity
  id: WallRockAndesiteTungsten
  parent: WallRockAndesite
  description: "Рудна жила, багата на вольфраміт."
  suffix: Tungsten
  components:
    - type: OreVein
      oreChance: 1.0
      currentOre: OreTungsten
    - type: Sprite
      layers:
        - state: rock_andesite
        - map: [ "enum.EdgeLayer.South" ]
          state: rock_andesite_south
        - map: [ "enum.EdgeLayer.East" ]
          state: rock_andesite_east
        - map: [ "enum.EdgeLayer.North" ]
          state: rock_andesite_north
        - map: [ "enum.EdgeLayer.West" ]
          state: rock_andesite_west
        - state: rock_copper
