using Robust.Shared.GameStates;
using Robust.Shared.Serialization;

namespace Content.Shared._Pirate.DNDMage;

[RegisterComponent, NetworkedComponent, AutoGenerateComponentState]
public sealed partial class DNDMageComponent : Component
{
    /// <summary>
    /// Поточний рівень магічної сили ДНД мага
    /// </summary>
    [DataField("magicPower"), AutoNetworkedField]
    public int MagicPower = 10;

    /// <summary>
    /// Максимальний рівень магічної сили
    /// </summary>
    [DataField("maxMagicPower"), AutoNetworkedField]
    public int MaxMagicPower = 20;

    /// <summary>
    /// Швидкість відновлення магічної сили
    /// </summary>
    [DataField("magicRegenRate"), AutoNetworkedField]
    public float MagicRegenRate = 0.5f;

    /// <summary>
    /// Рівень майстерності мага
    /// </summary>
    [DataField("skillLevel"), AutoNetworkedField]
    public int SkillLevel = 1;

    /// <summary>
    /// Чи активний зараз магічний щит
    /// </summary>
    [DataField("shieldActive"), AutoNetworkedField]
    public bool ShieldActive = false;
}
