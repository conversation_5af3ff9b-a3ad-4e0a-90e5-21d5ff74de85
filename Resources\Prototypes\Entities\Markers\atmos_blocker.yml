- type: entity
  name: "<PERSON>ікс Атмосу: Вакуум"
  id: AtmosFixBlockerMarker
  description: "Вакуум, T20C"
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - sprite: Markers/atmos.rsi # {
          state: base
          shader: unshaded
        - sprite: Markers/atmos.rsi
          shader: unshaded # }
          state: vacuum
    - type: AtmosFixMarker
      mode: 1

- type: entity
  name: "Фікс Атмосу: Маркер Кисню"
  id: AtmosFixOxygenMarker
  description: "Кисень @ тиск шахтного газу, T20C"
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - sprite: Markers/atmos.rsi # {
          state: base
          shader: unshaded
        - sprite: Markers/atmos.rsi
          shader: unshaded # }
          state: oxygen
    - type: AtmosFixMarker
      mode: 2

- type: entity
  name: "Фікс Атмосу: Маркер Азоту"
  id: AtmosFixNitrogenMarker
  description: "Азот @ тиск шахтного газу, T20C"
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - sprite: Markers/atmos.rsi # {
          state: base
          shader: unshaded
        - sprite: Markers/atmos.rsi
          shader: unshaded # }
          state: nitrogen
    - type: AtmosFixMarker
      mode: 3

- type: entity
  name: "Фікс Атмосу: Маркер Плазми"
  id: AtmosFixPlasmaMarker
  description: "Плазма @ тиск шахтного газу, T20C"
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - sprite: Markers/atmos.rsi # {
          state: base
          shader: unshaded
        - sprite: Markers/atmos.rsi
          shader: unshaded # }
          state: plasma
    - type: AtmosFixMarker
      mode: 4

- type: entity
  name: "Фікс Атмосу: Маркер Миттєвого Плазмового Вогню"
  id: AtmosFixInstantPlasmaFireMarker
  description: "МИТТЄВИЙ ПЛАЗМОВИЙ ВОГОНЬ"
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - sprite: Markers/atmos.rsi # {
          state: base-hot
          shader: unshaded
        - sprite: Markers/atmos.rsi
          shader: unshaded # }
          state: fire
    - type: AtmosFixMarker
      mode: 5

- type: entity
  name: "Фікс Атмосу: Маркер для Морозильної Камери "
  id: AtmosFixFreezerMarker
  description: "Змініть температуру повітря на 235°K, щоб морозильна камера мала достатньо місця для налаштування."
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - sprite: Markers/atmos.rsi # {
          state: base
          shader: unshaded
        - sprite: Markers/atmos.rsi
          shader: unshaded # }
          state: freeze
    - type: AtmosFixMarker
      mode: 6

- type: entity
  parent: AtmosFixNitrogenMarker
  id: AtmosFixVoxMarker
  suffix: Vox Atmosphere
  description: "Азот при 101 кПа, 20°C"
  components:
  - type: AtmosFixMarker
    mode: 7

- type: entity
  id: AtmosFixCoolant
  suffix: Coolant
  description: "Охолоджувач Суперматерії, еквівалентний балону з рідким азотом"
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - sprite: Markers/atmos.rsi # {
          state: base
          shader: unshaded
        - sprite: Markers/atmos.rsi
          shader: unshaded # }
          state: nitrogen
    - type: AtmosFixMarker
      gasMix:
        volume: 2500
        moles:
          - 0 # oxygen
          - 9350.0 # nitrogen
        temperature: 72
