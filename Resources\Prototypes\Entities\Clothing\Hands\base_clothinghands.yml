- type: entity
  abstract: true
  parent: Clothing
  id: ClothingHandsBase
  components:
  - type: Sprite
    state: icon
  - type: Clothing
    slots: [gloves]
  - type: Food
    requiresSpecialDigestion: true
  - type: Item
    size: Small
    storedRotation: -90
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Fiber
          Quantity: 10
  - type: Tag
    tags:
    - ClothMade
    - WhitelistChameleon
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 5
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  abstract: true
  parent: ClothingHandsBase
  id: ClothingHandsButcherable
  components:
  - type: Butcherable
    butcheringType: Knife
    spawned:
    - id: MaterialCloth1
      amount: 1

# gloves that cover the fingertips and have synthetic fibers
- type: entity
  abstract: true
  parent: ClothingHandsButcherable
  id: ClothingHandsGlovesSyntheticBase
  components:
  - type: Fiber
    fiberMaterial: fibers-synthetic
  - type: FingerprintMask

- type: entity
  parent: ClothingHandsGlovesSyntheticBase
  abstract: true
  id: ClothingHandsGlovesEnviroglovesBase
  components:
  - type: Armor
    modifiers:
      coefficients:
        Caustic: 0.95
  - type: IgniteFromGasImmunity
    parts:
    - LeftHand
    - RightHand

- type: entity
  abstract: true
  parent: BaseItem
  id: RingBase
  components:
    - type: Clothing
      slots: [ gloves, neck ]
    - type: Item
      size: Tiny
    - type: Tag
      tags:
      - Ring
