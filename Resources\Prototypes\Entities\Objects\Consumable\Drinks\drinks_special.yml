- type: entity
  parent: BaseItem
  id: DrinkShaker
  name: "шейкер"
  description: "Вірний помічник бармена у змішуванні напоїв."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 100
  - type: MixableSolution
    solution: drink
  - type: Drink
  - type: Shakeable
  - type: FitsInDispenser
    solution: drink
  - type: DrawableSolution
    solution: drink
  - type: RefillableSolution
    solution: drink
  - type: DrainableSolution
    solution: drink
  - type: SolutionTransfer
    canChangeTransferAmount: true
  - type: Spillable
    solution: drink
  - type: Sprite
    sprite: Objects/Consumable/Drinks/shaker.rsi
    state: icon
  - type: UserInterface
    interfaces:
      enum.TransferAmountUiKey.Key:
        type: TransferAmountBoundUserInterface
  - type: PhysicalComposition
    materialComposition:
      Steel: 50
  - type: EmitSoundOnPickup
    sound: /Audio/SimpleStation14/Items/Handling/drinkglass_pickup.ogg
  - type: EmitSoundOnDrop
    sound: /Audio/SimpleStation14/Items/Handling/drinkglass_drop.ogg
  - type: EmitSoundOnLand
    sound: /Audio/SimpleStation14/Items/Handling/drinkglass_drop.ogg
  - type: ReactionMixer
    mixOnInteract: false
    reactionTypes:
    - Shake

- type: entity
  parent: DrinkGlassBase
  id: DrinkShotGlass
  name: "чарка"
  description: "Ідеально підходить для того, щоб сердито грюкнути нею об стіл."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 5
  - type: SolutionTransfer
    transferAmount: 10
    minTransferAmount: 10
  - type: Drink
  - type: Sprite
    sprite: Objects/Consumable/Drinks/shotglass.rsi
    layers:
      - state: icon
        map: [ "enum.SolutionContainerLayers.Base" ]
      - state: fill1
        map: [ "enum.SolutionContainerLayers.Fill" ]
        visible: false
      - state: icon-front
        map: [ "enum.SolutionContainerLayers.Overlay" ]
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 2
    fillBaseName: fill

- type: entity
  parent: DrinkGlassBase
  id: DrinkJar
  name: "банка"
  description: "Чашка хіпстера"
  components:
  - type: Drink
  - type: Sprite
    sprite: Objects/Consumable/Drinks/jar.rsi
    layers:
      - state: icon
        map: [ "enum.SolutionContainerLayers.Base" ]
      - state: fill1
        map: [ "enum.SolutionContainerLayers.Fill" ]
        visible: false
      - state: icon-front
        map: [ "enum.SolutionContainerLayers.Overlay" ]
  - type: Appearance
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 50
  - type: SolutionContainerVisuals
    maxFillLevels: 9
    fillBaseName: fill
    metamorphic: true
    metamorphicDefaultSprite:
      sprite: Objects/Consumable/Drinks/jar.rsi
      state: icon
  - type: TransformableContainer

- type: entity
  parent: DrinkGlassBase
  id: DrinkJarWhat
  name: "банку з чимось"
  description: "Насправді не можна сказати, що це таке."
  components:
  - type: Drink
  - type: Sprite
    sprite: Objects/Consumable/Drinks/jar_what.rsi

- type: entity
  id: BartenderMixer
  abstract: true
  components:
  - type: DrainableSolution
    solution: drink
  - type: Drink
  - type: DrawableSolution
    solution: drink
  - type: RefillableSolution
    solution: drink
  - type: SolutionTransfer
    canChangeTransferAmount: true
  - type: Spillable
    solution: drink
  - type: UserInterface
    interfaces:
      enum.TransferAmountUiKey.Key:
        type: TransferAmountBoundUserInterface

- type: entity
  parent: [BaseItem, BartenderMixer]
  id: DrinkJigger
  name: "джиггер"
  description: "Як шейкер, але менший. Використовується для контролю кількості інгредієнтів."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 20
  - type: MixableSolution
    solution: drink
  - type: FitsInDispenser
    solution: drink
  - type: Sprite
    sprite: Objects/Consumable/Drinks/jigger.rsi
    state: icon
  - type: PhysicalComposition
    materialComposition:
      Steel: 20

- type: entity
  parent: [BaseItem, BartenderMixer]
  id: DrinkIceBucket
  name: "відро з льодом"
  description: "Спеціальне відерце з освіжаючим льодом. Заборонено використовувати для однойменного челенджу!"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Ice
          Quantity: 200
  - type: Sprite
    sprite: Objects/Consumable/Drinks/icebucket.rsi
    state: icon
  - type: PhysicalComposition
    materialComposition:
      Steel: 75
