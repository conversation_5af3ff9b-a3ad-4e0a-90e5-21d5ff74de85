- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackChameleon
  name: "рюкзак"
  description: "Ви одягаєте його на спину і кладете в нього речі."
  suffix: Chameleon
  components:
    - type: Tag
      tags: [] # ignore "WhitelistChameleon" tag
    - type: Sprite
      sprite: Clothing/Back/Backpacks/backpack.rsi
    - type: ChameleonClothing
      slot: [back]
      default: ClothingBackpack
    - type: UserInterface
      interfaces:
        enum.StorageUiKey.Key:
          type: StorageBoundUserInterface
        enum.ChameleonUiKey.Key:
          type: ChameleonBoundUserInterface

- type: entity
  parent: Clothing
  id: ClothingBackpackWaterTank
  name: "резервуар для води в рюкзаку"
  description: "Вміщує велику кількість рідини. Підходить для розпилювачів в руках."
  components:
  - type: Tag
    tags:
    - NozzleBackTank
    - WhitelistChameleon
  - type: Sprite
    sprite: Clothing/Back/Backpacks/waterbackpack.rsi
    state: icon
  - type: Item
    size: Ginormous
  - type: Clothing
    slots: BACK
    sprite: Clothing/Back/Backpacks/waterbackpack.rsi
  - type: SolutionAmmoProvider
    solutionId: tank
    proto: BulletWaterShot
  - type: SolutionContainerManager
    solutions:
      tank:
        maxVol: 1000 #much water
  - type: SolutionTransfer
    transferAmount: 50
    maxTransferAmount: 100
    minTransferAmount: 10
    canChangeTransferAmount: true
  - type: UserInterface
    interfaces:
      enum.TransferAmountUiKey.Key:
        type: TransferAmountBoundUserInterface
  - type: DrawableSolution
    solution: tank
  - type: RefillableSolution
    solution: tank
  - type: DrainableSolution
    solution: tank
  - type: ExaminableSolution
    solution: tank

- type: entity
  parent: Clothing
  id: ClothingBackpackEtherealTeleporter
  name: "ефірний телепорт"
  description: "Цей рюкзак, створений під час експериментів кількох дослідницьких центрів над Shadowkin, дозволяє користувачеві перестрибнути прірву між \"нормальним\" виміром і Темрявою."
  components:
    - type: Tag
      tags:
        - WhitelistChameleon
    - type: Sprite
      sprite: Clothing/Back/etherealteleporter.rsi
      state: icon
    - type: Item
      size: Ginormous
    - type: Clothing
      slots: BACK
      sprite: Clothing/Back/etherealteleporter.rsi
    # TODO: Uncomment this when ClothingGrantPsionicPower is fixed and back working.
    # - type: ClothingGrantPsionicPower
    #   power: DarkSwapPower
    # - type: Psionic
