- type: entity
  id: ProjectileFireball
  name: "вогняна куля"
  description: "Тобі краще зважитися."
  parent: BulletRocket
  categories: [ HideSpawnMenu ]
  components:
  - type: PointLight
    color: "#E25822"
    radius: 2.0
    energy: 5.0
  - type: Projectile
    damage:
      types:
        Heat: 10
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/magic.rsi
    layers:
    - state: fireball
      shader: unshaded
  - type: Explosive
    explosionType: Default
    maxIntensity: 3.5 # Goob edit
    intensitySlope: 3 # Goob edit
    totalIntensity: 28 # Gobo edit
    maxTileBreak: 0
  - type: IgnitionSource
    temperature: 400
    ignited: true
  - type: IgniteOnCollide
    fireStacks: 0.35
  - type: Ammo # Goobstation
    muzzleFlash: null
  - type: FlashOnTrigger # Goobstation
    duration: 4
    range: 1.8
  - type: Tag # Goobstation
    tags:
      - FlashIgnoreResistances

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseBulletTrigger
  id: ProjectileDragonsBreath
  name: "дихання дракона"
  description: "Намагайтеся не підсмажитися."
  components:
  - type: PointLight
    color: "#E25822"
    radius: 3.0
    energy: 5.0
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/magic.rsi
    layers:
    - state: fireball
      shader: unshaded
  - type: IgnitionSource
    temperature: 1000
    ignited: true
  - type: RepeatingTrigger
    delay: 0.5 # line of fire as well as if it hits something
  - type: ExplodeOnTrigger
  - type: Explosive
    explosionType: Default
    totalIntensity: 5 # low intensity, the point is to burn attackers not to break open walls, dragons can just eat them
    intensitySlope: 1
    maxIntensity: 3
    canCreateVacuum: false
    deleteAfterExplosion: false
    repeatable: true
  - type: TimedDespawn
    lifetime: 5

- type: entity
  id: ProjectileAnomalyFireball
  name: "вогняна куля"
  description: "Згусток полум'я, що висить у повітрі."
  parent: ProjectileFireball
  categories: [ HideSpawnMenu ]
  components:
  - type: TimedDespawn
    lifetime: 30
  - type: Explosive
    explosionType: Default
    maxIntensity: 100
    intensitySlope: 0.1
    totalIntensity: 0.3
    maxTileBreak: 0

- type: entity
  id: ProjectilePolyboltBase
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/magic.rsi
    layers:
    - state: magicm_green # Goob edit
      shader: unshaded
  - type: Projectile
    damage:
      types:
        Poison: 0 # Goob edit
  - type: Reflective # Goobstation
    reflective: None
  - type: Ammo # Goobstation
    muzzleFlash: null
  - type: Trail # Goobstation
    lerpTime: 0.05
    frequency: 0.02
    lifetime: 1
    alphaLerpAmount: 0.4
    shader: unshaded
    sprite:
      sprite: /Textures/Objects/Weapons/Guns/Projectiles/magic.rsi
      state: magicm_green

- type: entity
  id: ProjectilePolyboltCarp
  parent: ProjectilePolyboltBase
  name: "короповий поліболт"
  description: "Ні, я не хочу бути рибою!"
  categories: [ HideSpawnMenu ]
  components:
  - type: PolymorphOnCollide
    polymorph: WizardForcedCarp
    whitelist:
      components:
      - Body

- type: entity
  id: ProjectilePolyboltMonkey
  parent: ProjectilePolyboltBase
  name: "мавпячий поліболт"
  description: "Ні, я не хочу бути мавпою!"
  categories: [ HideSpawnMenu ]
  components:
  - type: PolymorphOnCollide
    polymorph: WizardForcedMonkey
    whitelist:
      components:
      - Body

- type: entity
  id: ProjectilePolyboltDoor
  parent: ProjectilePolyboltBase
  name: "дверний засув"
  description: "Ні, я не хочу бути дверима!"
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/magic.rsi
    layers:
    - state: spell
      color: brown
  - type: PolymorphOnCollide
    polymorph: WizardWallDoor
    whitelist:
      components:
      - Airlock
      - Firelock
      tags:
      - Wall

- type: entity
  id: ProjectileHealingBolt
  name: "цілющий болт"
  description: "Я НАКАЗУЮ ТОБІ ЖИТИ!"
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/magic.rsi
    layers:
    - state: magicm_yellow # Goob edit
      shader: unshaded
  - type: Projectile
    damage:
      types:
        Heat: 0 # Goob edit
  - type: RejuvenateOnProjectileHit # Goobstation
    undeadList:
      components:
      - Zombie
      tags:
      - Undead
    damage:
      types:
        Heat: 600
  - type: Reflective # Goobstation
    reflective: None
  - type: Ammo # Goobstation
    muzzleFlash: null
  - type: Trail # Goobstation
    lerpTime: 0.05
    frequency: 0.02
    lifetime: 1
    alphaLerpAmount: 0.4
    shader: unshaded
    sprite:
      sprite: /Textures/Objects/Weapons/Guns/Projectiles/magic.rsi
      state: magicm_yellow

- type: entity
  id: BulletInstakillMagic
  name: "магічний свинцевий циліндр"
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  description: "Виглядає знайомо."
  components:
  - type: Projectile
    damage:
      types:
        Piercing: 300
    ignoreResistances: true

- type: entity
  id: ProjectilePolyboltCluwne
  parent: ProjectilePolyboltBase
  name: "застібка-фіксатор"
  description: "кноХ КноХ!"
  categories: [ HideSpawnMenu ]
  components:
  - type: PolymorphOnCollide
    polymorph: WizardForcedCluwne
    whitelist:
      components:
      - Body

- type: entity
  id: ProjectileIcicle
  parent: BaseBullet
  name: "Бурулька"
  description: "Бррррр."
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Structures/Specific/Anomalies/ice_anom.rsi
  - type: Projectile
    damage:
      types:
        Piercing: 20
        Cold: 20
        Structural: 50

- type: entity
  parent: ProjectilePolyboltBase
  id: ProjectilePolyboltBread
  name: "хлібний поліболт"
  description: "Ні, я не хочу бути хлібом!"
  categories: [ HideSpawnMenu ]
  components:
  - type: PolymorphOnCollide
    polymorph: BreadMorph
    whitelist:
      components:
      - Body
#PIRATE START UNTIL END OF FILE
- type: entity
  id: ProjectilePolyboltMouseTimed
  parent: ProjectilePolyboltBase
  name: "миша"
  description: "Ні, я не хочу бути мишою!"
  components:
  - type: PolymorphOnCollide
    polymorph: WizardMouse
    whitelist:
      components:
      - Body

- type: entity
  id: ProjectilePolyboltCluwneTimed
  parent: ProjectilePolyboltBase
  name: "клувня"
  description: "Ні, я не хочу бути клувнею!"
  components:
  - type: PolymorphOnCollide
    polymorph: ArtifactCluwne
    whitelist:
      components:
      - Body

- type: entity
  id: ProjectilePolyboltBreadTimed
  parent: ProjectilePolyboltBase
  name: "хліб"
  description: "Ні, я не хочу бути хлібом!"
  components:
  - type: PolymorphOnCollide
    polymorph: WizardBread
    whitelist:
      components:
      - Body

- type: entity
  id: ProjectilePolyboltChickenTimed
  parent: ProjectilePolyboltBase
  name: "курка"
  description: "Ні, я не хочу бути мавпою!"
  components:
  - type: PolymorphOnCollide
    polymorph: WizardChicken
    whitelist:
      components:
      - Body

- type: entity
  id: ProjectilePolyboltMonkeyTimed
  parent: ProjectilePolyboltBase
  name: "монке"
  description: "Ні, я не хочу бути мавпою!"
  components:
  - type: PolymorphOnCollide
    polymorph: WizardMonkey
    whitelist:
      components:
      - Body

- type: entity
  id: ProjectilePolyboltGoatTimed
  parent: ProjectilePolyboltBase
  name: "коза"
  description: "Ні, я не хочу бути мавпою!"
  components:
  - type: PolymorphOnCollide
    polymorph: WizardGoat
    whitelist:
      components:
      - Body

- type: entity
  id: ProjectilePolyboltCowTimed
  parent: ProjectilePolyboltBase
  name: "корова"
  description: "Ні, я не хочу бути мавпою!"
  components:
  - type: PolymorphOnCollide
    polymorph: WizardCow
    whitelist:
      components:
      - Body

- type: entity
  id: ProjectilePolyboltPigTimed
  parent: ProjectilePolyboltBase
  name: "свиня"
  description: "Ні, я не хочу бути мавпою!"
  components:
  - type: PolymorphOnCollide
    polymorph: WizardPig
    whitelist:
      components:
      - Body

- type: entity
  id: ProjectilePolyboltHamsterTimed
  parent: ProjectilePolyboltBase
  name: "омак"
  description: "Ні, я не хочу бути мавпою!"
  components:
  - type: PolymorphOnCollide
    polymorph: WizardHamster
    whitelist:
      components:
      - Body

- type: entity
  id: ProjectilePolyboltWizardCatTimed
  parent: ProjectilePolyboltBase
  name: "кіт"
  description: "Ні, я не хочу бути мавпою!"
  components:
  - type: PolymorphOnCollide
    polymorph: WizardCat
    whitelist:
      components:
      - Body

- type: entity
  id: ProjectileIceBurst
  name: "лід проломився"
  description: "Бррррр."
  parent: BaseBullet
  components:
  - type: Projectile
    damage:
      types:
        Cold: 20
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/magic.rsi
    layers:
    - state: ice
      shader: unshaded
  - type: StaminaDamageOnHit
    damage: 25

- type:  entity
  parent: BaseBullet
  id: ProjectileMagicTesla
  name: "магічна куля тесла"
  description: "2 баджільйони вольт"
  components:
  - type: TimedDespawn
    lifetime: 5
  - type: Sprite
    sprite: Structures/Power/Generation/Tesla/energy_miniball.rsi
    layers:
    - state: tesla_projectile
      shader: unshaded
  - type: StaminaDamageOnCollide
    damage: 15
  - type: EmbeddableProjectile
  - type: Projectile
    deleteOnCollide: true
    soundHit:
      path: /Audio/Weapons/Guns/Hits/bullet_hit.ogg
  - type: LightningArcShooter
    arcDepth: 1
    maxLightningArc: 3
    shootMinInterval: 2.5
    shootMaxInterval: 4.5
    shootRange: 5
    lightningPrototype: Lightning
  - type: Electrified
    requirePower: false

- type: entity
  name: "болт зачарованого меча"
  id: SpellswordBolt
  parent: BaseBullet
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles_tg.rsi
    layers:
    - state: omnilaser_greyscale
      shader: unshaded
      color: orangered
  - type: Projectile
     #   soundHit:  Waiting on serv3
    impactEffect: BulletImpactEffectOrangeDisabler
    damage:
      types:
        Heat: 15

- type: entity
  id: ProjectileLocker
  name: "проклята шафка"
  description: "Проклята магічна шафка! Зможеш встояти?"
  parent: ClosetSteelBase
  categories: [ HideSpawnMenu ]
  components:
  - type: ResistLocker
    resistTime: 60 # Goob edit
  - type: Ammo # Goobstation
    muzzleFlash: null
  - type: FadingTimedDespawn # Goobstation
    lifetime: 120
    fadeOutTime: 5
  - type: StoreOnCollide
    lockOnCollide: true
    disableWhenFirstOpened: true
    disableOnSleep: true # Goobstation
    whitelist:
      components:
        - Body
  - type: LockVisuals
    stateLocked: cursed_door
    stateUnlocked: decursed_door
  - type: Lock
    breakOnEmag: false
  - type: Projectile
    deleteOnCollide: false
    onlyCollideWhenShot: true
    damage:
      types:
        Brute: 0
  - type: Sprite
    noRot: true
    sprite: Structures/Storage/closet.rsi
    layers:
    - state: cursed
      map: [ "enum.StorageVisualLayers.Base" ]
    - state: decursed_door
      map: [ "enum.StorageVisualLayers.Door" ]
    - state: cursed_door
      map: [ "enum.LockVisualLayers.Lock" ]
    - state: welded
      visible: false
      map: [ "enum.WeldableLayers.BaseWelded" ]
  #TODO: Will have to eventually make a custom visualizer for cursed lockers
  - type: EntityStorageVisuals
    stateBaseClosed: decursed
    stateDoorOpen: decursed_open
    stateDoorClosed: decursed_door
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.48,0.25,0.48"
        density: 75
        mask:
        - MachineMask
        layer:
        - MachineLayer
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.15,-0.45,0.15,0.15"
        hard: false
        mask:
        - Impassable
        - BulletImpassable
      fly-by: &flybyfixture
        shape: !type:PhysShapeCircle
          radius: 0.6
        layer:
        - Impassable
        - MidImpassable
        - HighImpassable
        - LowImpassable
        hard: false
