﻿using Content.Shared.Cargo;
using Content.Shared.Cargo.Prototypes;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Cargo.UI
{
    [GenerateTypedNameReferences]
    public sealed partial class CargoProductRow : PanelContainer
    {
        public CargoProductPrototype? Product { get; set; }

        public CargoProductRow()
        {
            RobustXamlLoader.Load(this);
        }
    }
}
