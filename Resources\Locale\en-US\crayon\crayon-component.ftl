
## Entity

crayon-drawing-label = Drawing: [color={$color}]{$state}[/color] ({$charges}/{$capacity})
crayon-interact-not-enough-left-text = Not enough left.
crayon-interact-used-up-text = The {$owner} got used up.
crayon-interact-invalid-location = Can't reach there!

## UI
crayon-window-title = Crayon
crayon-window-placeholder = Search, or queue a comma-separated list of names
crayon-category-1-brushes = Brushes
crayon-category-2-alphanum = Numbers and letters
crayon-category-3-symbols = Symbols
crayon-category-4-info = Signs
crayon-category-5-graffiti = Graffiti
crayon-category-random = Random
