- type: entity
  id: SyndicateMonitoringServer
  parent: BaseMachinePowered
  name: "підозрілий сервер моніторингу екіпажу"
  description: "Отримує та передає стан усіх активних датчиків костюма."
  components:
    - type: Sprite
      sprite: Structures/Machines/server.rsi
      layers:
        - state: server
        - state: variant-crew
    - type: Construction
      graph: Machine
      node: machine
      containers:
        - machine_board
        - machine_parts
    - type: Machine
      board: CrewMonitoringServerMachineCircuitboard
    - type: ContainerContainer
      containers:
        machine_board: !type:Container
        machine_parts: !type:Container
    - type: CrewMonitoringServer
    - type: SingletonDeviceNetServer
    - type: DeviceNetwork
      deviceNetId: Wireless
      transmitFrequencyId: CrewMonitor
      receiveFrequencyId: SuitSensor
      autoConnect: false
    - type: WirelessNetworkConnection
      range: 10000 # Mega range for Listening Post
    - type: ApcPowerReceiver
      powerLoad: 200
    - type: ExtensionCableReceiver
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 600
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
        - trigger:
            !type:DamageTrigger
            damage: 300
          behaviors:
            - !type:DoActsBehavior
              acts: ["Destruction"]
            - !type:PlaySoundBehavior
              sound:
                collection: MetalBreak
            - !type:SpawnEntitiesBehavior
              spawn:
                SheetSteel1:
                  min: 1
                  max: 2
    - type: AmbientSound
      volume: -1
      range: 2
      sound:
        path: /Audio/Ambience/Objects/server_fans.ogg
