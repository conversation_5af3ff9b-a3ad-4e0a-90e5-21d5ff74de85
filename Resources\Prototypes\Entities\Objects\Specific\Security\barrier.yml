- type: entity
  name: "розкладний бар'єр"
  id: DeployableBarrier
  description: "бар'єр, що розгортається. Проведіть посвідченням особи, щоб заблокувати/розблокувати його."
  parent: BaseStructure
  components:
  - type: Transform
    anchored: false
    noRot: true
  - type: Sprite
    sprite: Objects/Specific/Security/barrier.rsi
    layers:
    - state: idle
    - state: locked
      map: ["enum.LockVisualLayers.Lock"]
  - type: Appearance
  - type: LockVisuals
  - type: InteractionOutline
  - type: Physics
    bodyType: Dynamic
    canCollide: false
  - type: Fixtures
    fixtures:
      base:
        shape:
          !type:PhysShapeCircle
          radius: 0.45
        density: 75
        mask:
        - MachineMask
      barrier:
        shape:
          !type:PhysShapeCircle
          radius: 0.45
        layer:
        - WallLayer
  - type: DeployableBarrier
    fixture: barrier
  - type: AccessReader
    access: [["Security"]]
  - type: Lock
    locked: false
    lockOnClick: true # toggle lock just by clicking on barrier
    lockTime: 5
    unlockTime: 5
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 600
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel:
            min: 5
            max: 5
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: PointLight
    enabled: false
    radius: 3
    color: red
  - type: StaticPrice
    price: 200
