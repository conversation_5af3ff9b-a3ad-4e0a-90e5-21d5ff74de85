- type: job
  id: Detective
  name: job-name-detective
  description: job-description-detective
  playTimeTracker: JobDetective
  requirements:
  - !type:CharacterDepartmentTimeRequirement
    department: Security
    min: 36000 # DeltaV - 10 hours
  - !type:CharacterTraitRequirement
    inverted: true
    traits:
      - Foreigner
      - ForeignerLight
      - Muted
      - Blindness
      - Pacifist
      - BrittleBoneDisease
  - !type:CharacterEmployerRequirement
      employers:
      - ZavodskoiInterstellar
      - PMCG
      - NanoTrasen
  startingGear: DetectiveGear
  icon: "JobIconDetective"
  supervisors: job-supervisors-hos
  canBeAntag: false
  access:
  - Security
  #- Brig # Delta V: Removed
  - Maintenance
  - Service
  - Detective
  - External
  - Cryogenics
  special:
  - !type:AddImplantSpecial
    implants: [ MindShieldImplant ]
  - !type:AddComponentSpecial # goobstation
    components:
      - type: SecurityStaff
  afterLoadoutSpecial:
  - !type:ModifyEnvirosuitSpecial
    charges: 6
  - !type:ModifyEnvirohelmSpecial
    powerCell: PowerCellHigh

- type: startingGear
  id: DetectiveGear
  subGear:
  - Detective<PERSON>lasmamanGear

  equipment:
    jumpsuit: ClothingUniformJumpsuitDetective
    back: ClothingBackpackSecurity
    shoes: ClothingShoesBootsCombatFilled
    eyes: ClothingEyesGlassesSunglasses
    head: ClothingHeadHatFedoraBrown
    outerClothing: ClothingOuterVestDetective
    id: DetectivePDA
    ears: ClothingHeadsetAltSecurityRegular # Goobstation
    belt: ClothingBeltHolsterFilled
  innerClothingSkirt: ClothingUniformJumpskirtDetective
  satchel: ClothingBackpackSatchelSecurity
  duffelbag: ClothingBackpackDuffelSecurity

- type: startingGear
  id: DetectivePlasmamanGear
  parent: BasePlasmamanSecurityGear
  equipment:
    jumpsuit: ClothingUniformEnvirosuitDetective
    head: ClothingHeadEnvirohelmDetective
    gloves: ClothingHandsGlovesEnviroglovesWhite
