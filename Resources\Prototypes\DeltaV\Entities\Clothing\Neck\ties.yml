- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckTieWhite
  name: "біла краватка"
  description: "Біла краватка кольору слонової кістки."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Neck/Ties/whitetie.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Neck/Ties/whitetie.rsi
    renderLayer: innerNeck

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckTieBlack
  name: "чорна краватка"
  description: "Вугільно-чорна краватка."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Neck/Ties/blacktie.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Neck/Ties/blacktie.rsi

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckTieBrown
  name: "коричнева краватка"
  description: "Шоколадно-коричнева краватка."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Neck/Ties/browntie.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Neck/Ties/browntie.rsi

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckTieBlue
  name: "синя краватка"
  description: "Блакитно-синя краватка."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Neck/Ties/bluetie.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Neck/Ties/bluetie.rsi

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckTieGreen
  name: "зелена краватка"
  description: "Зелена краватка."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Neck/Ties/greentie.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Neck/Ties/greentie.rsi

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckTieChem
  name: "краватка з хімії"
  description: "Помаранчева краватка."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Neck/Ties/chemtie.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Neck/Ties/chemtie.rsi
