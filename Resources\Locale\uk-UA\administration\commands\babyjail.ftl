cmd-babyjail-desc = Вмикає дитячу в'язницю, яка дозволяє суворіше обмежувати доступ до сервера.
cmd-babyjail-help = Використання: babyjail
babyjail-command-enabled = Дитяча в'язниця була включена.
babyjail-command-disabled = Дитяча в'язниця була виведена з ладу.

cmd-babyjail_show_reason-desc = Дозволяє вказати, чи показувати клієнтам, що підключаються, причину, через яку дитяча в'язниця заблокувала їхнє приєднання.
cmd-babyjail_show_reason-help = Використання: babyjail_show_reason
babyjail-command-show-reason-enabled = Тепер дитяча в'язниця показуватиме користувачам, яким вона блокує підключення, причину.
babyjail-command-show-reason-disabled = Дитяча в'язниця більше не показуватиме користувачам, яким вона блокує підключення, причину.

cmd-babyjail_max_account_age-desc = Отримує або встановлює максимальний вік акаунта у хвилинах, який може бути дозволено акаунту для підключення з увімкненою дитячою в'язницею.
cmd-babyjail_max_account_age-help = Використання: babyjail_max_account_age <hours
babyjail-command-max-account-age-is = Максимальний вік акаунта для дитячої в'язниці становить {$hours} годин.
babyjail-command-max-account-age-set = Встановіть максимальний вік акаунта для дитячої в'язниці в {$hours} годин.

cmd-babyjail_max_overall_hours-desc = Отримує або встановлює максимальний загальний ігровий час у хвилинах, який може бути дозволений акаунту для підключення з увімкненою дитячою в'язницею.
cmd-babyjail_max_overall_hours-help = Використання: babyjail_max_overall_hours <годин
babyjail-command-max-overall-hours-is = Максимальний загальний ігровий час для дитячої в'язниці становить {$hours} годин.
babyjail-command-max-overall-hours-set = Встановіть максимальний загальний час гри для дитячої в'язниці в {$hours} годин.
