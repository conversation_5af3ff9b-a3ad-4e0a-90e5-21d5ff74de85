- type: entity
  parent: Poweredlight
  id: JanitorServiceLight
  name: "сигнальне світло для прибиральників"
  description: "Настінний знак прибиральника. Якщо лампочка блимає, необхідна послуга у прибиральників."
  components:
  - type: Sprite
    sprite: Structures/Wallmounts/service_light.rsi
    drawdepth: WallMountedItems
    layers:
    - map: ["enum.PoweredLightLayers.Base"]
      state: base
    - map: ["enum.PoweredLightLayers.Glow"]
      state: glow
      shader: unshaded
    state: base
  - type: PointLight
    radius: 1.5
    energy: 0.45
    softness: 3
    offset: "0, 0"
    enabled: false
  - type: PoweredLight
    bulb: Bulb
    on: false
    hasLampOnSpawn: ServiceLightBulb
    damage:
      types:
        Heat: 5
