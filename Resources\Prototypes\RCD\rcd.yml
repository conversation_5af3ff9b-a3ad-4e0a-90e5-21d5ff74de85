# Operations
- type: rcd
  id: Invalid   # Hidden prototype - do not add to RCDs
  mode: Invalid
  
- type: rcd
  id: Deconstruct
  name: rcd-component-deconstruct
  category: Main
  sprite: /Textures/Interface/Radial/RCD/deconstruct.png
  mode: Deconstruct
  prototype: EffectRCDDeconstructPreview
  rotation: Camera

- type: rcd
  id: DeconstructLattice   # Hidden prototype - do not add to RCDs  
  name: rcd-component-deconstruct
  mode: Deconstruct
  cost: 2
  delay: 0
  rotation: Camera
  fx: EffectRCDConstruct0
    
- type: rcd
  id: DeconstructTile      # Hidden prototype - do not add to RCDs
  name: rcd-component-deconstruct
  mode: Deconstruct
  cost: 4
  delay: 4
  rotation: Camera
  fx: EffectRCDDeconstruct4

# Flooring 
- type: rcd
  id: Plating
  name: rcd-component-plating
  category: WallsAndFlooring
  sprite: /Textures/Interface/Radial/RCD/plating.png
  mode: ConstructTile
  prototype: Plating
  cost: 1
  delay: 1
  collisionMask: InteractImpassable
  rules:
    - CanBuildOnEmptyTile
  fx: EffectRCDConstruct1
  
- type: rcd
  id: FloorSteel
  name: rcd-component-floor-steel
  category: WallsAndFlooring
  sprite: /Textures/Interface/Radial/RCD/metal_tile.png
  mode: ConstructTile
  prototype: FloorSteel
  cost: 1
  delay: 1
  collisionMask: InteractImpassable
  rules:
    - CanBuildOnEmptyTile
  fx: EffectRCDConstruct1

- type: rcd
  id: Catwalk
  category: WallsAndFlooring
  sprite: /Textures/Interface/Radial/RCD/catwalk.png
  mode: ConstructObject
  prototype: Catwalk
  cost: 1
  delay: 1
  collisionMask: InteractImpassable
  rules:
    - MustBuildOnSubfloor
    - IsCatwalk
  rotation: Fixed
  fx: EffectRCDConstruct1

# Walls
- type: rcd
  id: WallSolid
  category: WallsAndFlooring
  sprite: /Textures/Interface/Radial/RCD/solid_wall.png
  mode: ConstructObject
  prototype: WallSolid 
  cost: 4
  delay: 2
  collisionMask: FullTileMask
  rotation: Fixed
  fx: EffectRCDConstruct2

- type: rcd
  id: Grille
  category: WindowsAndGrilles
  sprite: /Textures/Interface/Radial/RCD/grille.png
  mode: ConstructObject
  prototype: Grille
  cost: 4
  delay: 2
  collisionMask: FullTileMask
  rotation: Fixed
  fx: EffectRCDConstruct2

# Windows
- type: rcd
  id: Window
  category: WindowsAndGrilles
  sprite: /Textures/Interface/Radial/RCD/window.png
  mode: ConstructObject
  prototype: Window
  cost: 3
  delay: 2
  collisionMask: FullTileMask
  rules:
    - IsWindow
  rotation: Fixed
  fx: EffectRCDConstruct2
  
- type: rcd
  id: WindowDirectional
  category: WindowsAndGrilles
  sprite: /Textures/Interface/Radial/RCD/directional.png
  mode: ConstructObject
  prototype: WindowDirectional
  cost: 2
  delay: 1
  collisionMask: FullTileMask
  collisionBounds: "-0.23,-0.49,0.23,-0.36"
  rules:
    - IsWindow
  rotation: User
  fx: EffectRCDConstruct1
  
- type: rcd
  id: ReinforcedWindow
  category: WindowsAndGrilles
  sprite: /Textures/Interface/Radial/RCD/window_reinforced.png
  mode: ConstructObject
  prototype: ReinforcedWindow
  cost: 4
  delay: 3
  collisionMask: FullTileMask
  rules:
    - IsWindow
  rotation: User
  fx: EffectRCDConstruct3
    
- type: rcd
  id: WindowReinforcedDirectional
  category: WindowsAndGrilles
  sprite: /Textures/Interface/Radial/RCD/directional_reinforced.png
  mode: ConstructObject
  prototype: WindowReinforcedDirectional
  cost: 3
  delay: 2
  collisionMask: FullTileMask
  collisionBounds: "-0.23,-0.49,0.23,-0.36"
  rules:
    - IsWindow
  rotation: User
  fx: EffectRCDConstruct2

# Airlocks
- type: rcd
  id: Airlock
  category: Airlocks
  sprite: /Textures/Interface/Radial/RCD/airlock.png
  mode: ConstructObject
  prototype: Airlock
  cost: 4
  delay: 4
  collisionMask: FullTileMask
  rotation: Camera
  fx: EffectRCDConstruct4
  
- type: rcd
  id: AirlockGlass
  category: Airlocks
  sprite: /Textures/Interface/Radial/RCD/glass_airlock.png
  mode: ConstructObject
  prototype: AirlockGlass
  cost: 4
  delay: 4
  collisionMask: FullTileMask
  rotation: Camera
  fx: EffectRCDConstruct4
  
- type: rcd
  id: Firelock
  category: Airlocks
  sprite: /Textures/Interface/Radial/RCD/firelock.png
  mode: ConstructObject
  prototype: Firelock
  cost: 4
  delay: 3
  collisionMask: FullTileMask
  rotation: Camera
  fx: EffectRCDConstruct3

# Lighting
- type: rcd
  id: TubeLight
  category: Lighting
  sprite: /Textures/Interface/Radial/RCD/tube_light.png
  mode: ConstructObject
  prototype: Poweredlight
  cost: 2
  delay: 1
  collisionMask: TabletopMachineMask
  collisionBounds: "-0.23,-0.49,0.23,-0.36"
  rotation: User
  fx: EffectRCDConstruct1
  
- type: rcd
  id: BulbLight
  category: Lighting
  sprite: /Textures/Interface/Radial/RCD/bulb_light.png
  mode: ConstructObject
  prototype: PoweredSmallLight
  cost: 2
  delay: 1
  collisionMask: TabletopMachineMask
  collisionBounds: "-0.23,-0.49,0.23,-0.36"
  rotation: User
  fx: EffectRCDConstruct1

# Electrical
- type: rcd
  id: LVCable
  category: Electrical
  sprite: /Textures/Interface/Radial/RCD/lv_coil.png
  mode: ConstructObject
  prototype: CableApcExtension
  cost: 1
  delay: 0
  collisionMask: InteractImpassable
  rules:
  - MustBuildOnSubfloor
  rotation: Fixed
  fx: EffectRCDConstruct0
    
- type: rcd
  id: MVCable
  category: Electrical
  sprite: /Textures/Interface/Radial/RCD/mv_coil.png
  mode: ConstructObject
  prototype: CableMV
  cost: 1
  delay: 0
  collisionMask: InteractImpassable
  rules:
  - MustBuildOnSubfloor
  rotation: Fixed
  fx: EffectRCDConstruct0
    
- type: rcd
  id: HVCable
  category: Electrical
  sprite: /Textures/Interface/Radial/RCD/hv_coil.png
  mode: ConstructObject
  prototype: CableHV
  cost: 1
  delay: 0
  collisionMask: InteractImpassable
  rules:
  - MustBuildOnSubfloor
  rotation: Fixed
  fx: EffectRCDConstruct0
    
- type: rcd
  id: CableTerminal
  category: Electrical
  sprite: /Textures/Interface/Radial/RCD/cable_terminal.png
  mode: ConstructObject
  prototype: CableTerminal
  cost: 1
  delay: 0
  collisionMask: InteractImpassable
  rules:
  - MustBuildOnSubfloor
  rotation: User
  fx: EffectRCDConstruct0