﻿- type: entity
  id: CargoTelepad
  parent: [ BaseMachinePowered, ConstructibleMachine ]
  name: "вантажний пульт"
  description: "Принесіть піцу і приступайте до роботи."
  components:
  - type: InteractionOutline
  - type: Physics
    bodyType: Static
    canCollide: false
  - type: Transform
    anchored: true
    noRot: true
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.00"
        density: 190
        mask:
          - MachineMask
  - type: Sprite
    sprite: Structures/cargo_telepad.rsi
    drawdepth: HighFloorObjects
    layers:
    - state: offline
      map: [ "enum.CargoTelepadLayers.Base" ]
    - state: idle
      map: [ "enum.CargoTelepadLayers.Beam" ]
      shader: unshaded
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: BasicDevice
  - type: WirelessNetworkConnection
    range: 200
  - type: DeviceLinkSink
    ports:
      - OrderReceiver
  - type: ApcPowerReceiver
    powerLoad: 1000 # TODO if we keep this make it spike power draw when teleporting
  - type: CargoTelepad
  - type: Machine
    board: CargoTelepadMachineCircuitboard
  - type: Appearance
  - type: CollideOnAnchor
  - type: NameIdentifier
    group: CargoTelepads
