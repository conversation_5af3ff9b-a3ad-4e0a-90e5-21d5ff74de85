shuttle-pilot-start = Piloting ship
shuttle-pilot-end = Stopped piloting

shuttle-console-in-ftl = Currently in FTL
shuttle-console-mass = Too large to FTL
shuttle-console-prevent = You are unable to pilot this ship

# NAV

shuttle-console-display-label = Display

shuttle-console-position = Position:
shuttle-console-orientation = Orientation:
shuttle-console-linear-velocity = Linear velocity:
shuttle-console-angular-velocity = Angular velocity:

shuttle-console-unknown = Unknown
shuttle-console-iff-label = {$name} ({$distance}m)
shuttle-console-exclusion = Exclusion area

shuttle-console-nav-settings = Settings
shuttle-console-iff-toggle = Show IFF
shuttle-console-dock-toggle = Show docks

# MAP

shuttle-console-ftl-label = FTL Status
shuttle-console-ftl-state-Available = Available
shuttle-console-ftl-state-Starting = Starting
shuttle-console-ftl-state-Travelling = Travelling
shuttle-console-ftl-state-Arriving = Arriving
shuttle-console-ftl-state-Cooldown = Cooldown

shuttle-console-map-settings = Settings
shuttle-console-ftl-button = FTL
shuttle-console-map-rebuild = Scan for objects
shuttle-console-map-beacons = Show beacons

shuttle-console-no-signal = No signal

shuttle-console-map-objects = Sector objects

# DOCK
shuttle-console-docked = Docked objects

shuttle-console-view = View
shuttle-console-undock = Undock
shuttle-console-dock = Dock
shuttle-console-docks-label = Docks

shuttle-console-undock-fail = Undocking failed
shuttle-console-dock-fail = Docking failed
