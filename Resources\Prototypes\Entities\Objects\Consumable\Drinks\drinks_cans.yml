# When adding new drinks also add to random spawner located in Resources\Prototypes\Entities\Markers\Spawners\Random\Food_Drinks\drinks_soda.yml
- type: entity
  parent: BaseItem
  id: DrinkCanBaseFull
  abstract: true
  components:
  - type: Drink
  - type: Openable
  - type: Shakeable
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Cola
          Quantity: 30
        maxVol: 30
  - type: MixableSolution
    solution: drink
  - type: SolutionTransfer
    canChangeTransferAmount: true
    maxTransferAmount: 15
  - type: UserInterface
    interfaces:
      enum.TransferAmountUiKey.Key:
        type: TransferAmountBoundUserInterface
  - type: Sprite
    state: icon
    layers:
      - state: icon
        map: ["enum.OpenableVisuals.Layer"]
  - type: FitsInDispenser
    solution: drink
  - type: DrawableSolution
    solution: drink
  - type: RefillableSolution
    solution: drink
  - type: DrainableSolution
    solution: drink
  - type: PressurizedSolution
    solution: drink
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.OpenableVisuals.Opened:
        enum.OpenableVisuals.Layer:
          True: {state: "icon_open"}
          False: {state: "icon"}
  - type: Spillable
    solution: drink
  - type: MeleeWeapon
    soundNoDamage:
      path: "/Audio/Effects/Fluids/splat.ogg"
    damage:
      types:
        Blunt: 0
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 3
    soundHit:
      path: /Audio/SimpleStation14/Items/Handling/drinkglass_drop.ogg
  - type: Tool
    qualities:
    - Rolling
    speedModifier: 0.25 # its small so takes longer to roll the entire dough flat
  - type: SpaceGarbage
  - type: TrashOnSolutionEmpty
    solution: drink
  - type: PhysicalComposition
    materialComposition:
      Steel: 50 #reduce, reuse, recycle
  - type: Tag
    tags:
    - DrinkCan
  - type: EmitSoundOnPickup
    sound: /Audio/SimpleStation14/Items/Handling/drinkglass_pickup.ogg
  - type: EmitSoundOnDrop
    sound: /Audio/SimpleStation14/Items/Handling/drinkglass_drop.ogg
  - type: EmitSoundOnLand
    sound: /Audio/SimpleStation14/Items/Handling/drinkglass_drop.ogg

- type: entity
  parent: DrinkCanBaseFull
  id: DrinkColaCan
  name: "космічна кола"
  description: "Освіжаючий напій."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: Cola
          Quantity: 30
  - type: Tag
    tags:
    - Cola
    - DrinkCan
    - Recyclable
  - type: Sprite
    sprite: Objects/Consumable/Drinks/cola.rsi
  - type: Item
    sprite: Objects/Consumable/Drinks/cola.rsi

# created when taking apart an ied
- type: entity
  parent: DrinkColaCan
  id: DrinkColaCanEmpty
  suffix: empty
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
  - type: Openable
    opened: true
  - type: Sprite
    sprite: Objects/Consumable/Drinks/cola.rsi
    layers:
    - state: icon_open
  - type: Item
    sprite: Objects/Consumable/Drinks/cola.rsi
  - type: Tag
    tags:
    - Cola
    - DrinkCan
    - Recyclable
    - Trash

- type: entity
  parent: DrinkCanBaseFull
  id: DrinkIcedTeaCan
  name: "банка чаю з льодом"
  description: "Освіжаючу банку холодного чаю."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: IcedTea
          Quantity: 30
  - type: Sprite
    sprite: Objects/Consumable/Drinks/ice_tea_can.rsi
  - type: Item
    sprite: Objects/Consumable/Drinks/ice_tea_can.rsi

- type: entity
  parent: DrinkCanBaseFull
  id: DrinkLemonLimeCan
  name: "лимонно-лаймова банка"
  description: "Ви хотіли апельсин. А отримав лимонно-лаймовий."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: LemonLime
          Quantity: 30
  - type: Sprite
    sprite: Objects/Consumable/Drinks/lemon-lime.rsi
  - type: Item
    sprite: Objects/Consumable/Drinks/lemon-lime.rsi

- type: entity
  parent: DrinkCanBaseFull
  id: DrinkGrapeCan
  name: "банка виноградної газованої води"
  description: "Підсолоджений напій з виноградним смаком та насиченим фіолетовим кольором."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: GrapeSoda
          Quantity: 30
  - type: Sprite
    sprite: Objects/Consumable/Drinks/purple_can.rsi
  - type: Item
    sprite: Objects/Consumable/Drinks/purple_can.rsi

- type: entity
  parent: DrinkCanBaseFull
  id: DrinkRootBeerCan
  name: "банка з-під кореневого пива"
  description: "Трохи смачного кореневого пива тепер у портативній бляшанці!"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: RootBeer
          Quantity: 30
  - type: Sprite
    sprite: Objects/Consumable/Drinks/rootbeer.rsi
  - type: Item
    sprite: Objects/Consumable/Drinks/rootbeer.rsi
  - type: Tag
    tags:
    - Beer

- type: entity
  parent: DrinkCanBaseFull
  id: DrinkSodaWaterCan
  name: "банка газованої води"
  description: "Газована вода. Чому б не зробити віскі з содовою?"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: SodaWater
          Quantity: 30
  - type: Drink
  - type: Sprite
    sprite: Objects/Consumable/Drinks/sodawater.rsi

- type: entity
  parent: DrinkCanBaseFull
  id: DrinkSpaceMountainWindCan
  name: "вітер космічної гори може"
  description: "Проникає крізь тебе, як космічний вітер."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: SpaceMountainWind
          Quantity: 30
  - type: Sprite
    sprite: Objects/Consumable/Drinks/space_mountain_wind.rsi
  - type: Item
    sprite: Objects/Consumable/Drinks/space_mountain_wind.rsi

- type: entity
  parent: DrinkCanBaseFull
  id: DrinkSpaceUpCan
  name: "пробіл у банці"
  description: "Смак у роті, наче пробоїна в оболонці."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: SpaceUp
          Quantity: 30
  - type: Sprite
    sprite: Objects/Consumable/Drinks/space-up.rsi
  - type: Item
    sprite: Objects/Consumable/Drinks/space-up.rsi

- type: entity
  parent: DrinkCanBaseFull
  id: DrinkSolDryCan
  name: "соляна суха"
  description: "Солодка імбирна газована вода з космосу!"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: SolDry
          Quantity: 30
  - type: Sprite
    sprite: Objects/Consumable/Drinks/sol_dry.rsi
  - type: Item
    sprite: Objects/Consumable/Drinks/sol_dry.rsi

- type: entity
  parent: DrinkCanBaseFull
  id: DrinkStarkistCan
  name: "старкістська банка"
  description: "Смак зірки в рідкому вигляді. І трохи тунця...?"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: Starkist
          Quantity: 30
  - type: Sprite
    sprite: Objects/Consumable/Drinks/starkist.rsi
  - type: Item
    sprite: Objects/Consumable/Drinks/starkist.rsi

- type: entity
  parent: DrinkCanBaseFull
  id: DrinkTonicWaterCan
  name: "банка тонізуючої води"
  description: "Хінін має кумедний смак, але принаймні він вбереже вас від космічної малярії."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: TonicWater
          Quantity: 30
  - type: Drink
  - type: Sprite
    sprite: Objects/Consumable/Drinks/tonic.rsi

- type: entity
  parent: DrinkCanBaseFull
  id: DrinkFourteenLokoCan
  name: "Чотирнадцять банок \"Локо"
  description: "MBO повідомило членів екіпажу, що вживання \"Чотирнадцяти Локо\" може призвести до судом, сліпоти, сп'яніння або навіть смерті. Будь ласка, пийте відповідально."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: FourteenLoko
          Quantity: 30
  - type: Sprite
    sprite: Objects/Consumable/Drinks/fourteen_loko.rsi
  - type: Item
    sprite: Objects/Consumable/Drinks/fourteen_loko.rsi

- type: entity
  parent: DrinkCanBaseFull
  id: DrinkChangelingStingCan
  name: "банка для жала перевертня"
  description: "Робиш маленький ковток і відчуваєш печіння..."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: ChangelingSting
          Quantity: 30
  - type: Drink
  - type: Sprite
    sprite: Objects/Consumable/Drinks/changelingsting.rsi
  - type: Item
    sprite: Objects/Consumable/Drinks/changelingsting.rsi

- type: entity
  parent: DrinkCanBaseFull
  id: DrinkDrGibbCan
  name: "Доктор Гібб може"
  description: "Смачна суміш із 42 різних смаків."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: DrGibb
          Quantity: 30
  - type: Drink
  - type: Sprite
    sprite: Objects/Consumable/Drinks/dr_gibb.rsi
  - type: Item
    sprite: Objects/Consumable/Drinks/dr_gibb.rsi

- type: entity
  parent: DrinkCanBaseFull
  id: DrinkNukieCan
  name: "криваво-червона банка для пива"
  description: "Домашній напій, виготовлений з божевільних умів Синдикату. Лікарі не рекомендують."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: Stimulants
          Quantity: 5
        - ReagentId: NuclearCola
          Quantity: 20
        - ReagentId: Ice
          Quantity: 5
  - type: Tag
    tags:
    - DrinkCan
    - Recyclable
  - type: Sprite
    sprite: Objects/Consumable/Drinks/robustnukie.rsi
  - type: Item
    sprite: Objects/Consumable/Drinks/robustnukie.rsi

- type: entity
  parent: DrinkCanBaseFull
  id: DrinkEnergyDrinkCan
  name: "енергетичний напій red bool"
  description: "Бляшанка Red Bool, в якій стільки кофеїну, що можна вбити коня."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: EnergyDrink
          Quantity: 30
  - type: Drink
  - type: Sprite
    sprite: Objects/Consumable/Drinks/energy_drink.rsi
  - type: Item
    sprite: Objects/Consumable/Drinks/energy_drink.rsi

- type: entity
  id: DrinkCanPack
  parent: BaseStorageItem
  name: "6 упаковок"
  components:
  - type: Sprite
    sprite: Objects/Misc/6pack.rsi
    layers:
      - state: plastic-thingy
      - state: 6pack1
        map: ["6pack1"]
        visible: false
      - state: 6pack2
        map: ["6pack2"]
        visible: false
      - state: 6pack3
        map: ["6pack3"]
        visible: false
      - state: 6pack4
        map: ["6pack4"]
        visible: false
      - state: 6pack5
        map: ["6pack5"]
        visible: false
      - state: 6pack6
        map: ["6pack6"]
        visible: false
  - type: Item
    size: Normal
  - type: Storage
    grid:
    - 0,0,2,3
    whitelist:
      tags:
        - Cola
    hideStackVisualsWhenClosed: false
  - type: StorageFill
    contents:
      - id: DrinkColaCan
        amount: 6
  - type: ItemCounter
    count:
      tags: [Cola]
    composite: true
    layerStates:
    - 6pack1
    - 6pack2
    - 6pack3
    - 6pack4
    - 6pack5
    - 6pack6
  - type: Appearance

- type: entity
  parent: DrinkCanBaseFull
  id: DrinkShamblersJuiceCan
  name: "банка з-під соку shamblers"
  description: "~Налий мені соку Шамблер!"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: ShamblersJuice
          Quantity: 30
  - type: Drink
  - type: Sprite
    sprite: Objects/Consumable/Drinks/shamblersjuice.rsi
  - type: Item
    sprite: Objects/Consumable/Drinks/shamblersjuice.rsi

- type: entity
  parent: DrinkCanBaseFull
  id: DrinkPwrGameCan
  name: "pwr game can"
  description: "Єдиний напій з PWR, якого жадають справжні геймери. Коли геймер говорить про геймерпаливо, він має на увазі саме його."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: PwrGame
          Quantity: 30
  - type: Drink
  - type: Sprite
    sprite: Objects/Consumable/Drinks/pwrgame.rsi
  - type: Item
    sprite: Objects/Consumable/Drinks/pwrgame.rsi

- type: entity
  parent: DrinkCanBaseFull
  id: DrinkBeerCan
  name: "пивна банка"
  description: "Маленька радість, великий смак, ніяких турбот!"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: Beer
          Quantity: 30
  - type: Drink
  - type: Sprite
    sprite: Objects/Consumable/Drinks/beer_can.rsi
  - type: Item
    sprite: Objects/Consumable/Drinks/beer_can.rsi
  - type: Tag
    tags:
    - Beer

- type: entity
  parent: DrinkCanBaseFull
  id: DrinkWineCan
  name: "винна банка"
  description: "Ваш шлях до того, щоб забути про всі турботи та розважитися!"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: Wine
          Quantity: 30
  - type: Drink
  - type: Sprite
    sprite: Objects/Consumable/Drinks/wine_can.rsi
  - type: Item
    sprite: Objects/Consumable/Drinks/wine_can.rsi
  - type: Tag
    tags:
    - Wine
