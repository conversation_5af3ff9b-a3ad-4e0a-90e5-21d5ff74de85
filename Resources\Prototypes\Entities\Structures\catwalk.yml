# TODO move this to tiles once tile smoothing is supported
- type: entity
  id: Catwalk
  name: "подіум"
  description: "Як решітка. Тільки виглядає краще."
  placement:
    mode: SnapgridCenter
  components:
  - type: Clickable
  - type: Sprite
    sprite: Structures/catwalk.rsi
    drawdepth: FloorTiles
  - type: Icon
    sprite: Structures/catwalk.rsi
    state: catwalk_preview
  - type: Transform
    anchored: true
  - type: IconSmooth
    key: catwalk
    base: catwalk_
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepCatwalk
      params:
        volume: 8
  - type: Tag
    tags:
      - Catwalk
  - type: Construction
    graph: Catwalk
    node: Catwalk
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 500
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          PartRodMetal: # takes two to construct, so drop less than that
            min: 0
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: RCDDeconstructable
    cost: 2
    delay: 2
    fx: EffectRCDDeconstruct2  