using System.Linq;
using System.Numerics;
using Content.Client.UserInterface.Controls;
using Content.Shared.DeltaV.QuickPhrase;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;

namespace Content.Client.DeltaV.AACTablet.UI;

[GenerateTypedNameReferences]
public sealed partial class AACWindow : FancyWindow
{
    [Dependency] private readonly IPrototypeManager _prototype = default!;
    public event Action<ProtoId<QuickPhrasePrototype>>? PhraseButtonPressed;

    private const float SpaceWidth = 10f;
    private const float ParentWidth = 540f;
    private const int ColumnCount = 4;

    private const int ButtonWidth =
        (int)((ParentWidth - SpaceWidth * 2) / ColumnCount - SpaceWidth * ((ColumnCount - 1f) / ColumnCount));

    public AACWindow()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
        PopulateGui();
    }

    private void PopulateGui()
    {
        var phrases = _prototype.EnumeratePrototypes<QuickPhrasePrototype>().ToList();

        // take ALL phrases and turn them into tabs and groups, so the buttons are sorted and tabbed
        var sortedTabs = phrases
            .GroupBy(p => p.Tab)
            .OrderBy(g => g.Key)
            .ToDictionary(
                g => g.Key,
                g => g.GroupBy(p => p.Group)
                    .OrderBy(gg => gg.Key)
                    .ToDictionary(
                        gg => gg.Key,
                        gg => gg.OrderBy(p => Loc.GetString(p.Text)).ToList()
                    )
            );

        var tabContainer = CreateTabContainer(sortedTabs);
        WindowBody.AddChild(tabContainer);
    }

    private TabContainer CreateTabContainer(Dictionary<string, Dictionary<string, List<QuickPhrasePrototype>>> sortedTabs)
    {
        var tabContainer = new TabContainer();

        foreach (var tab in sortedTabs)
        {
            var tabName = Loc.GetString(tab.Key);
            var boxContainer = CreateBoxContainerForTab(tab.Value);
            tabContainer.AddChild(boxContainer);
            tabContainer.SetTabTitle(tabContainer.ChildCount - 1, tabName);
        }

        return tabContainer;
    }

    private BoxContainer CreateBoxContainerForTab(Dictionary<string, List<QuickPhrasePrototype>> groups)
    {
        var boxContainer = new BoxContainer
        {
            HorizontalExpand = true,
            Orientation = BoxContainer.LayoutOrientation.Vertical
        };

        foreach (var group in groups)
        {
            var header = CreateHeaderForGroup(group.Key);
            var buttonContainer = CreateButtonContainerForGroup(group.Value);
            boxContainer.AddChild(header);
            boxContainer.AddChild(buttonContainer);
        }

        return boxContainer;
    }

    private static Label CreateHeaderForGroup(string groupName)
    {
        var header = new Label
        {
            HorizontalExpand = true,
            Text = groupName,
            Margin = new Thickness(10, 10, 10, 0),
            StyleClasses = { "LabelBig" }
        };

        return header;
    }

    private GridContainer CreateButtonContainerForGroup(List<QuickPhrasePrototype> phrases)
    {
        var buttonContainer = CreateButtonContainer();
        foreach (var phrase in phrases)
        {
            var text = Loc.GetString(phrase.Text);
            var button = CreatePhraseButton(text, phrase.StyleClass);
            button.OnPressed += _ => OnPhraseButtonPressed(new ProtoId<QuickPhrasePrototype>(phrase.ID));
            buttonContainer.AddChild(button);
        }
        return buttonContainer;
    }

    private static GridContainer CreateButtonContainer()
    {
        var buttonContainer = new GridContainer
        {
            Margin = new Thickness(10),
            Columns = 4
        };

        return buttonContainer;
    }

    private static Button CreatePhraseButton(string text, string styleClass)
    {
        var phraseButton = new Button
        {
            Access = AccessLevel.Public,
            MaxSize = new Vector2(ButtonWidth, ButtonWidth),
            ClipText = false,
            HorizontalExpand = true,
            StyleClasses = { styleClass }
        };

        var buttonLabel = new RichTextLabel
        {
            Margin = new Thickness(0, 5),
            StyleClasses = { "WhiteText" }
        };

        buttonLabel.SetMessage(text);
        phraseButton.AddChild(buttonLabel);
        return phraseButton;
    }

    private void OnPhraseButtonPressed(ProtoId<QuickPhrasePrototype> phraseId)
    {
        PhraseButtonPressed?.Invoke(phraseId);
    }
}
