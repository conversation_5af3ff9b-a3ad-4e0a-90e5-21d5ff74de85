- type: entity
  parent: SimpleSpaceMobBase
  id: MobIfritFamiliar
  name: "<PERSON><PERSON>р<PERSON>т"
  description: "Слуга містагога."
  components:
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/Guardians/guardians.rsi
    layers:
      - state: magic_base
        map: [ "enum.DamageStateVisualLayers.Base" ]
      - state: magic_flare
        map: [ "enum.DamageStateVisualLayers.BaseUnshaded" ]
        color: "#40a7d7"
        shader: unshaded
  - type: RandomSprite
    available:
      - enum.DamageStateVisualLayers.BaseUnshaded:
          magic_flare: Sixteen
  - type: Fixtures
    fixtures:
      fix1:
          shape:
            !type:PhysShapeCircle
            radius: 0.35
          density: 225
          mask:
            - FlyingMobMask
          layer:
            - FlyingMobLayer
  - type: Bloodstream
    bloodReagent: Phlogiston
    bloodMaxVolume: 0
  - type: MeleeWeapon
    altDisarm: false # No shoving
    angle: 0
    damage:
      types:
        Heat: 10
    animation: WeaponArcPunch
  - type: MobState
  - type: MobThresholds
    thresholds:
      0: Alive
      120: Dead
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 120
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Access
    tags:
    - Chapel
    - Research
  - type: MindContainer
    showExamineInfo: true
  - type: Familiar
  - type: Dispellable
  - type: Damageable
    damageContainer: CorporealSpirit
    damageModifierSet: CorporealSpirit
  - type: PassiveDamage # Slight passive regen. Assuming one damage type, comes out to about 4 damage a minute.
    allowedStates:
    - Alive
    damageCap: 120
    damage:
      types:
        Cold: -0.07
      groups:
        Brute: -0.07
  - type: Speech
    speechSounds: Bass
  - type: Puller
    needsHands: false
  - type: Tag
    tags:
      - CannotSuicide
      - DoorBumpOpener
  - type: Body
    prototype: Human # For two hands.
  - type: InnateTool
    tools:
      - id: WelderIfrit
      - id: WelderIfrit
  - type: Hands
  - type: ZombieImmune # No zombie servant
  - type: RandomMetadata
    nameSegments: [names_golem]
  - type: Psionic
    removable: false
    roller: false
    assayFeedback:
      - ifrit-feedback
    psychognomicDescriptors:
      - p-descriptor-bound
      - p-descriptor-cyclic
  - type: InnatePsionicPowers
    powersToAdd:
      - PyrokinesisPower
      - TelepathyPower
  - type: Grammar
    attributes:
      proper: true
  - type: MovementIgnoreGravity
  - type: NoSlip
  - type: EyeProtection
  - type: NpcFactionMember
    factions:
    - PsionicInterloper
    - NanoTrasen
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    understands:
    - TauCetiBasic
  - type: GhostTakeoverAvailable
  - type: GhostRole
    name: ghost-role-information-ifrit-name
    description: ghost-role-information-ifrit-description
    rules: ghost-role-information-ifrit-rules
    makeSentient: true
    allowMovement: true
    allowSpeech: true
    requirements:
    - !type:CharacterDepartmentTimeRequirement
      department: Epistemics
      min: 14400 # 4 hours

- type: entity
  parent: WelderExperimental
  id: WelderIfrit
  name: "рука іфриту"
  description: "Зварювальна рука іфриту."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Objects/Tools/ifrit_hand.rsi
  - type: Item
    sprite: Nyanotrasen/Objects/Tools/ifrit_hand.rsi
  - type: SolutionContainerManager
    solutions:
      Welder:
        reagents:
        - ReagentId: Phlogiston
          Quantity: 1000
        maxVol: 1000
  - type: Welder
    fuelReagent: Phlogiston
    tankSafe: true
#    welderOnSounds:
#      collection: WelderIfritHandOn
#    welderOffSounds:
#      collection: WelderIfritHandOff
#  - type: Tool
#    useSound:
#      collection: WelderIfritHand
#  - type: MeleeWeapon
#    soundHit:
#      collection: WelderIfritHand
# TODO some way to give the MeleeWeapon a separate soundHit for when it's on,
# similar to EnergySword. will have to go in ToolSystem.Welder.cs
# for now, just make it sound like it's on fire no matter what because it
# sounds cooler than the default fizzling noise.
  - type: MeleeWeapon
    soundHit:
      path: /Audio/Items/welder.ogg
  - type: SolutionRegeneration
    solution: Welder
    generated:
      reagents:
        - ReagentId: Phlogiston
          Quantity: 1
  - type: PointLight
    enabled: false
    radius: 2
    energy: 1.5
    color: orange
