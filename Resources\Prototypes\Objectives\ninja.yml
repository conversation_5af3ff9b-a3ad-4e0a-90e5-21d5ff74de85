- type: entity
  abstract: true
  parent: BaseObjective
  id: BaseNinjaObjective
  components:
  - type: Objective
    # difficulty isn't used all since objectives are picked
    difficulty: 1.5
    issuer: spiderclan
  - type: RoleRequirement
    roles:
      mindRoles:
      - NinjaRole

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseNinjaObjective
  id: DoorjackObjective
  components:
  - type: Objective
    icon:
      sprite: Objects/Tools/emag.rsi
      state: icon
  - type: NumberObjective
    min: 15
    max: 40
    title: objective-condition-doorjack-title
    description: objective-condition-doorjack-description
  - type: DoorjackCondition

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseNinjaObjective
  id: StealResearchObjective
  description: "Ваші рукавички можуть бути використані для злому дослідницького сервера і викрадення його дорогоцінних даних. Якщо епістемологія занепала, вам доведеться взятися за роботу." # DeltaV - Epistemics Department replacing Science
  components:
  - type: Objective
    icon:
      sprite: Structures/Machines/server.rsi
      state: server
  - type: NumberObjective
    min: 9
    max: 13
    title: objective-condition-steal-research-title
  - type: StealResearchCondition

- type: entity
  categories: [ HideSpawnMenu ]
  parent: [BaseNinjaObjective, BaseCodeObjective]
  id: SpiderChargeObjective
  description: "Ця бомба може бути підірвана в певному місці. Зверніть увагу, що в іншому місці бомба не спрацює!"
  components:
  - type: Objective
    icon:
      sprite: Objects/Weapons/Bombs/spidercharge.rsi
      state: icon

- type: entity
  categories: [ HideSpawnMenu ]
  parent: [BaseNinjaObjective, BaseSurviveObjective]
  id: NinjaSurviveObjective
  name: "Виживіть"
  description: "Ти не був би хорошим ніндзя, якщо помер, чи не так?"
  components:
  - type: Objective
    icon:
      sprite: Clothing/Mask/ninja.rsi
      state: icon

- type: entity
  categories: [ HideSpawnMenu ]
  parent: [BaseNinjaObjective, BaseCodeObjective]
  id: TerrorObjective
  name: "Викличте загрозу"
  description: "Використовуйте рукавички на комунікаційній консолі, щоб викликати ще одну загрозу на станцію."
  components:
  - type: Objective
    icon:
      sprite: Objects/Fun/Instruments/otherinstruments.rsi
      state: red_phone

- type: entity
  categories: [ HideSpawnMenu ]
  parent: [BaseNinjaObjective, BaseCodeObjective]
  id: MassArrestObjective
  name: "Оголосити всіх у розшук"
  description: "Використовуйте рукавички, щоб зламати консоль обліку судимостей, оголосивши всю станцію в розшук!"
  components:
  - type: Objective
    icon:
      sprite: Objects/Weapons/Melee/stunbaton.rsi
      state: stunbaton_on
