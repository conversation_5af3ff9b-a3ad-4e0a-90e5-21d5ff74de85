autodoc-program-step-surgery = perform surgery on {$part}: {$name}
autodoc-program-step-grab-item = grab item: '{$name}'
autodoc-program-step-grab-any = grab any: {$name}
autodoc-item-organ = Organ
autodoc-item-part = Body Part
autodoc-program-step-store-item = store item
autodoc-program-step-set-label = set label: '{$label}'
autodoc-program-step-wait = wait {$length} seconds

autodoc-program-completed = PROGRAM COMPLETED
autodoc-error = ERROR: {$error}
autodoc-fatal-error = FATAL ERROR: {$error}
autodoc-waiting = PROGRAM WAITING

autodoc-error-missing-patient = MISSING PATIENT
autodoc-error-body-part = BODY PART NOT DETECTED
autodoc-error-surgery-impossible = SELECTED SURGERY IMPOSSIBLE
autodoc-error-item-unavailable = ITEM UNAVAILABLE
autodoc-error-surgery-failed = SURGERY FAILED
autodoc-error-hand-full = ITEM MANIPULATOR FULL
autodoc-error-storage-full = ITEM TRAYS FULL
autodoc-error-patient-unsedated = PATIENT REQUIRES SEDATION

autodoc-title = AUTODOC MK.XIV
autodoc-create-program = NEW PROGRAM
autodoc-program-title = PROGRAM TITLE
autodoc-program-title-placeholder = Program {$number}
autodoc-abort-program = ABORT PROGRAM

autodoc-view-program-title = VIEW PROGRAM
autodoc-safety-enabled = SAFETY ON
autodoc-safety-disabled = SAFETY OFF
autodoc-remove-program = REMOVE PROGRAM
autodoc-add-step = ADD STEP ДОБАВЬ
autodoc-remove-step = REMOVE STEP
autodoc-start-program = START PROGRAM

autodoc-add-step-surgery = PERFORM SURGERY
autodoc-add-step-grab-item = GRAB ITEM
autodoc-add-step-grab-item-prompt = Item name
autodoc-add-step-grab-item-placeholder = dwarf heart
autodoc-add-step-grab-organ = GRAB ORGAN
autodoc-add-step-grab-part = GRAB BODY PART
autodoc-add-step-store-item = STORE ITEM
autodoc-add-step-set-label = SET LABEL
autodoc-add-step-set-label-prompt = Label
autodoc-add-step-wait = WAIT
autodoc-add-step-wait-prompt = Seconds to wait

autodoc-body-part-Other = Other
autodoc-body-part-Torso = Torso
autodoc-body-part-Head = Head
autodoc-body-part-Arm = Arm
autodoc-body-part-Hand = Hand
autodoc-body-part-Leg = Leg
autodoc-body-part-Foot = Foot
autodoc-body-part-Tail = Tail

autodoc-body-symmetry-ignored = Any
autodoc-body-symmetry-None = None
autodoc-body-symmetry-Left = Left
autodoc-body-symmetry-Right = Right

autodoc-submit = Submit
