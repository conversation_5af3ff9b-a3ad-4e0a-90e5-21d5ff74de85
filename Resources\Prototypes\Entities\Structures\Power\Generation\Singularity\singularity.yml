- type: entity
  id: Singularity
  name: "гравітаційна сингулярність"
  description: "Заворожуючий вихор темряви, що засмоктує все навколо. Якщо він рухається до вас, тікайте."
  components:
  - type: SupermatterImmune
  - type: Clickable
  - type: AmbientSound
    volume: -4
    range: 14
    sound:
      path: /Audio/Effects/singularity.ogg
  - type: Physics
    bodyType: Dynamic
    bodyStatus: InAir
  - type: CanMoveInAir
  - type: EventHorizon # To make the singularity consume things.
    radius: 0.5
    canBreachContainment: false
    colliderFixtureId: EventHorizonCollider
    consumerFixtureId: EventHorizonConsumer
  - type: GravityWell # To make the singularity attract things.
    baseRadialAcceleration: 10
    maxRange: 4
  - type: Fixtures
    fixtures:
      EventHorizonCollider:
        shape:
          !type:PhysShapeCircle
            radius: 0.35
        hard: true
        restitution: 0.8
        density: 99999
        mask:
        - SingularityLayer
        layer:
        - SingularityLayer
      EventHorizonConsumer:
        shape:
          !type:PhysShapeCircle
            radius: 0.35
        hard: false
        mask:
        - SingularityLayer
        layer:
        - SingularityLayer
  - type: Singularity
    energy: 180
    level: 1
    radsPerLevel: 2
    energyLoss: 1
  - type: RandomWalk # To make the singularity move around.
    maxSpeed: 2.5
    minSpeed: 1.875
  - type: SingularityDistortion
    falloffPower: 2.529822
    intensity: 3645
  - type: RadiationSource
    slope: 0.2 # its emit really far away
    intensity: 2
  - type: PointLight
    enabled: true
    radius: 10
  - type: Appearance
  - type: GuideHelp
    guides: [ Singularity, Power ] # uhhh.. I would hoped they'd  have read the manual before ever getting in viewing distance...
  - type: WarpPoint
    follow: true
    location: singularity
  - type: Sprite
    sprite: Structures/Power/Generation/Singularity/singularity_1.rsi
    shader: unshaded
    layers:
    - map: [ "VisualLevel" ]
      state: singularity_1
  - type: GenericVisualizer
    visuals:
      enum.SingularityAppearanceKeys.Singularity:
       VisualLevel:
          1:
            sprite: Structures/Power/Generation/Singularity/singularity_1.rsi
            state: singularity_1
            scale: 1.0,1.0
          2:
            sprite: Structures/Power/Generation/Singularity/singularity_2.rsi
            state: singularity_2
            scale: 1.0,1.0
          3:
            sprite: Structures/Power/Generation/Singularity/singularity_3.rsi
            state: singularity_3
            scale: 1.0,1.0
          4:
            sprite: Structures/Power/Generation/Singularity/singularity_4.rsi
            state: singularity_4
            scale: 1.0,1.0
          5:
            sprite: Structures/Power/Generation/Singularity/singularity_5.rsi
            state: singularity_5
            scale: 1.5,1.5
          6:
            sprite: Structures/Power/Generation/Singularity/singularity_6.rsi
            state: singularity_6
            scale: .9,.9
