- type: entity
  id: BaseSpeedLoaderSpecial
  name: "швидкісний навантажувач (.38 special)"
  parent: BaseItem
  abstract: true
  components:
  - type: Tag
    tags:
      - SpeedLoaderSpecial
  - type: SpeedLoader
  - type: BallisticAmmoProvider
    whitelist:
      tags:
        - CartridgeSpecial
    capacity: 6
  - type: Sprite
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
        ents: []

- type: entity
  id: SpeedLoaderSpecial
  name: "швидкісний навантажувач (.38 special)"
  parent: BaseSpeedLoaderSpecial
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeSpecial
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Ammunition/SpeedLoaders/Special/special_speed_loader.rsi
    layers:
      - state: base
        map: [ "enum.GunVisualLayers.Base" ]
      - state: base-6
        map: [ "enum.GunVisualLayers.Mag" ]
  - type: MagazineVisuals
    magState: base
    steps: 7
    zeroVisible: false
  - type: Appearance

- type: entity
  id: SpeedLoaderSpecialPractice
  name: "швидкісний навантажувач (.38 special practice)"
  parent: BaseSpeedLoaderSpecial
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeSpecialPractice
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Ammunition/SpeedLoaders/Special/special_speed_loader.rsi
    layers:
      - state: base
        map: [ "enum.GunVisualLayers.Base" ]
      - state: practice-6
        map: [ "enum.GunVisualLayers.Mag" ]
  - type: MagazineVisuals
    magState: practice
    steps: 7
    zeroVisible: false
  - type: Appearance

- type: entity
  id: SpeedLoaderSpecialRubber
  name: "швидкозаряджач (.38 спеціальний гумовий)"
  parent: BaseSpeedLoaderSpecial
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeSpecialRubber
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Ammunition/SpeedLoaders/Special/special_speed_loader.rsi
    layers:
      - state: base
        map: [ "enum.GunVisualLayers.Base" ]
      - state: rubber-6
        map: [ "enum.GunVisualLayers.Mag" ]
  - type: MagazineVisuals
    magState: rubber
    steps: 7
    zeroVisible: false
  - type: Appearance

- type: entity
  id: SpeedLoaderSpecialUranium
  name: "швидкісний навантажувач (.38 special uranium)"
  parent: BaseSpeedLoaderSpecial
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeSpecialUranium
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Ammunition/SpeedLoaders/Special/special_speed_loader.rsi
    layers:
      - state: base
        map: [ "enum.GunVisualLayers.Base" ]
      - state: uranium-6
        map: [ "enum.GunVisualLayers.Mag" ]
  - type: MagazineVisuals
    magState: uranium
    steps: 7
    zeroVisible: false
  - type: Appearance

- type: entity
  id: SpeedLoaderSpecialMindbreaker
  name: "швидкісний навантажувач (.38 special mindbreaker)"
  parent: BaseSpeedLoaderSpecial
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeSpecialMindbreaker
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Ammunition/SpeedLoaders/Special/special_speed_loader.rsi
    layers:
      - state: base
        map: [ "enum.GunVisualLayers.Base" ]
      - state: mindbreaker-6
        map: [ "enum.GunVisualLayers.Mag" ]
  - type: MagazineVisuals
    magState: mindbreaker
    steps: 7
    zeroVisible: false
  - type: Appearance

- type: entity
  id: SpeedLoaderSpecialHoly
  name: "швидкісний навантажувач (.38 special holy)"
  parent: BaseSpeedLoaderSpecial
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeSpecialHoly
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Ammunition/SpeedLoaders/Special/special_speed_loader.rsi
    layers:
      - state: base
        map: [ "enum.GunVisualLayers.Base" ]
      - state: holy-6
        map: [ "enum.GunVisualLayers.Mag" ]
  - type: MagazineVisuals
    magState: holy
    steps: 7
    zeroVisible: false
  - type: Appearance
