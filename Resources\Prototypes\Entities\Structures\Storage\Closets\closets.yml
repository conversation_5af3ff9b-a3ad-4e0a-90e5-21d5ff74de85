﻿# Tool closet
- type: entity
  id: ClosetTool
  name: "шафа для інструментів"
  parent: ClosetSteelBase
  description: "Це місце для зберігання інструментів."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: eng
    stateDoorOpen: eng_open
    stateDoorClosed: eng_tool_door

# Radiation suit closet
- type: entity
  id: ClosetRadiationSuit
  name: "шафа для протирадіаційних костюмів"
  parent: ClosetSteelBase
  description: "Комфортніше, ніж радіаційне отруєння."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: eng
    stateDoorOpen: eng_open
    stateDoorClosed: eng_rad_door

# Emergency closet
- type: entity
  id: ClosetEmergency
  name: "авар<PERSON>йна шафа"
  parent: ClosetSteelBase
  description: "Це приміщення для зберігання аварійних дихальних масок та балонів з киснем."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: emergency
    stateDoorOpen: emergency_open
    stateDoorClosed: emergency_door

# Emergency N2 closet
- type: entity
  id: ClosetEmergencyN2
  name: "аварійна азотна шафа"
  parent: ClosetSteelBase
  description: "Там повно обладнання для порятунку життя. Якщо припустити, що ви дихаєте азотом."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: n2
    stateDoorOpen: n2_open
    stateDoorClosed: n2_door

# Fire safety closet
- type: entity
  id: ClosetFire
  name: "протипожежна шафа"
  parent: ClosetSteelBase
  description: "Це склад для зберігання протипожежного приладдя."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: fire
    stateDoorOpen: fire_open
    stateDoorClosed: fire_door

# EOD closet
- type: entity
  id: ClosetBomb
  name: "шафа для вибухозахисного костюму"
  parent: ClosetSteelBase
  description: "Це шафа для зберігання вибухозахисних костюмів."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: bomb
    stateDoorOpen: bomb_open
    stateDoorClosed: bomb_door

# Janitorial bomb suit closet
- type: entity
  id: ClosetJanitorBomb
  name: "шафа для вибухозахисного костюму прибиральника"
  parent: ClosetSteelBase
  description: "Це шафа для зберігання прибиральницьких вибухозахисних костюмів."
  suffix: DO NOT MAP
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: janitor_bomb
    stateDoorOpen: janitor_bomb_open
    stateDoorClosed: janitor_bomb_door

# Biohazard

# Base level 3 bio hazard closet
- type: entity
  id: ClosetL3
  parent: ClosetSteelBase
  name: "шафа для біологічної небезпеки 3-го рівня"
  description: "Це сховище для зберігання спорядження 3-го рівня біологічної небезпеки."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: bio
    stateDoorOpen: bio_open
    stateDoorClosed: bio_door

# Virology variant
- type: entity
  id: ClosetL3Virology
  parent: ClosetL3
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: bio_viro
    stateDoorOpen: bio_viro_open
    stateDoorClosed: bio_viro_door

# Security variant
- type: entity
  id: ClosetL3Security
  parent: ClosetL3
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: bio_sec
    stateDoorOpen: bio_sec_open
    stateDoorClosed: bio_sec_door

# Janitor variant
- type: entity
  id: ClosetL3Janitor
  parent: ClosetL3
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: bio_jan
    stateDoorOpen: bio_jan_open
    stateDoorClosed: bio_jan_door

# Maintenance closet
- type: entity
  id: ClosetMaintenance
  name: "технічна шафа"
  parent: ClosetSteelBase
  description: "Це склад."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateDoorOpen: generic_open
    stateDoorClosed: generic_door

# Syndicate
- type: entity
  id: LockerSyndicate
  name: "збройова шафа"
  parent: ClosetSteelBase
  description: "Це склад."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: syndicate
    stateDoorOpen: syndicate_open
    stateDoorClosed: syndicate_door

# Bluespace closet
- type: entity
  id: ClosetBluespace
  name: "підозріла шафа"
  suffix: Bluespace
  parent: ClosetMaintenance
  description: "Це ж склад... так?"
  components:
    - type: BluespaceLocker
      pickLinksFromSameMap: true
      minBluespaceLinks: 1
      behaviorProperties:
        bluespaceEffectOnTeleportSource: true
      autoLinksBidirectional: true
      autoLinksUseProperties: true
      autoLinkProperties:
        bluespaceEffectOnTeleportSource: true

# Unstable bluespace closet
- type: entity
  id: ClosetBluespaceUnstable
  name: "підозріла шафа"
  suffix: Bluespace unstable
  parent: ClosetMaintenance
  description: "Це ж склад... так?"
  components:
    - type: BluespaceLocker
      pickLinksFromSameMap: true
      minBluespaceLinks: 1
      behaviorProperties:
        clearLinksDebluespaces: true
        transportEntities: false
        bluespaceEffectOnTeleportSource: true
        clearLinksEvery: 2
      autoLinksBidirectional: true
      autoLinksUseProperties: true
      usesSinceLinkClear: -1 # hacky
      autoLinkProperties:
        invalidateOneWayLinks: true
        transportEntities: false
        bluespaceEffectOnTeleportSource: true
        destroyAfterUses: 2
        destroyType: DeleteComponent
