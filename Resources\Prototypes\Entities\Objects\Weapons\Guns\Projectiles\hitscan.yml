# Used to animate the hitscan effects because effectsystem doesn't support it
- type: entity
  id: HitscanEffect
  categories: [ HideSpawnMenu ]
  components:
  - type: TimedDespawn
    lifetime: 2.0
  - type: Sprite
    drawdepth: Effects
    layers:
    - shader: unshaded
      map: ["enum.EffectLayers.Unshaded"]
  - type: EffectVisuals
  - type: Tag
    tags:
      - HideContextMenu
  - type: AnimationPlayer

- type: hitscan
  id: RedLaser
  damage:
    types:
      Heat: 14
  muzzleFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: muzzle_laser
  travelFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: beam
  impactFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: impact_laser

- type: hitscan
  id: RedLaserPractice
  damage:
    types:
      Heat: 1
  muzzleFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: muzzle_laser
  travelFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: beam
  impactFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: impact_laser

- type: hitscan
  id: RedMediumLaser
  damage:
    types:
      Heat: 17
  muzzleFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: muzzle_laser
  travelFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: beam
  impactFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: impact_laser

- type: hitscan
  id: RedLightLaser
  damage:
    types:
      Heat: 7
  muzzleFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: muzzle_laser
  travelFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: beam
  impactFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: impact_laser

- type: hitscan
  id: XrayLaser
  damage:
    types:
      Heat: 10
      Radiation: 10
  muzzleFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: muzzle_xray
  travelFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: xray
  impactFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: impact_xray

- type: hitscan
  id: RedHeavyLaser
  damage:
    types:
      Heat: 28
  muzzleFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: muzzle_beam_heavy
  travelFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: beam_heavy
  impactFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: impact_beam_heavy

- type: hitscan
  id: Pulse
  damage:
    types:
      Heat: 35
  muzzleFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: muzzle_blue
  travelFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: beam_blue
  impactFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: impact_blue

- type: hitscan
  id: RedShuttleLaser
  maxLength: 60
  damage:
    types:
      Heat: 45
      Structural: 10
  muzzleFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: muzzle_beam_heavy2
  travelFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: beam_heavy2
  impactFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: impact_beam_heavy2

# Glimmer wisp laser
- type: hitscan
  id: WispLash
  damage:
    types:
      Cold: 8
      Shock: 8
  muzzleFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: muzzle_omni
  travelFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: beam_omni
  impactFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: impact_omni
