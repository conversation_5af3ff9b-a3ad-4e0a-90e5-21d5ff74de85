- type: gameMap
  id: Europa
  mapName: 'Європа'
  mapPath: /Maps/europa.yml
  minPlayers: 0
  maxPlayers: 30
  stations:
    Europa:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: '{0} Europa Outpost {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: 'B'
        - type: StationJobs
          overflowJobs:
            - Passenger
          availableJobs:
          #service
            Captain: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1]
            NanotrasenRepresentative: [ 1, 1 ]
            Magistrate: [ 1, 1 ]
            AdministrativeAssistant: [ 1, 1 ]
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 1, 1 ]
            Botanist: [ 2, 2 ]
            Boxer: [ 2, 2 ]
            Chef: [ 1 , 1 ]
            Clown: [ 1, 1 ]
            Lawyer: [ 1, 1 ]
            Musician: [ 1, 1 ]
            Janitor: [ 1, 2 ]
            Mime: [ 1, 1 ]
          #engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 1, 2 ]
            StationEngineer: [ 2, 2 ]
            TechnicalAssistant: [ 2, 2 ]
          #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            Chemist: [ 1, 1 ]
            MedicalDoctor: [ 2, 3 ]
            MedicalIntern: [ 2, 2 ]
            Paramedic: [ 1, 1 ]
            Psychologist: [ 1, 1 ]
          #science
            ResearchDirector: [ 1, 1 ]
            Chaplain: [ 1, 1 ]
            ForensicMantis: [ 1, 1 ]
            Scientist: [ 2, 3 ]
            ResearchAssistant: [ 2, 2 ]
            Borg: [ 1, 1 ]
          #security
            HeadOfSecurity: [ 1, 1 ]
            Warden: [ 1, 1 ]
            Detective: [ 1, 1 ]
            SecurityOfficer: [ 2, 2 ]
            SecurityCadet: [ 1, 1 ]
            Prisoner: [ 1, 2 ]
          #supply
            Quartermaster: [ 1, 1 ]
            MailCarrier: [ 1, 2 ]
            SalvageSpecialist: [ 2, 2 ]
            CargoTechnician: [ 2, 3 ]
          #civilian
            Passenger: [ -1, -1 ]
          # Silicon
            StationAi: [ 1, 1 ]
