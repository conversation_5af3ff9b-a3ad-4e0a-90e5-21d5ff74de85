﻿- type: entity
  id: Airlock
  parent: BaseStructure
  name: "повітряний шлюз"
  description: "Він відкривається, закривається і, можливо, розчавить вас."
  components:
  - type: StationAiWhitelist
  - type: MeleeSound
    soundGroups:
      Brute:
        path:
          "/Audio/Weapons/smash.ogg"
  - type: InteractionOutline
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Standard/basic.rsi
    snapCardinals: true
    layers:
    - state: closed
      map: ["enum.DoorVisualLayers.Base"]
    - state: closed_unlit
      shader: unshaded
      map: ["enum.DoorVisualLayers.BaseUnlit"]
    - state: welded
      map: ["enum.WeldableLayers.BaseWelded"]
    - state: bolted_unlit
      shader: unshaded
      map: ["enum.DoorVisualLayers.BaseBolted"]
    - state: emergency_unlit
      map: ["enum.DoorVisualLayers.BaseEmergencyAccess"]
      shader: unshaded
    - state: panel_open
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
    - state: electrified
      sprite: Interface/Misc/ai_hud.rsi
      shader: unshaded
      visible: false
      map: ["enum.ElectrifiedLayers.HUD"]
  - type: AnimationPlayer
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.49,-0.49,0.49,0.49" # don't want this colliding with walls or they won't close
        density: 100
        mask:
        - FullTileMask
        layer:
        - AirlockLayer
  - type: LayerChangeOnWeld
    unWeldedLayer: AirlockLayer
    weldedLayer: WallLayer
  - type: ContainerFill
    containers:
      board: [ DoorElectronics ]
  - type: AccessReader
    containerAccessProvider: board
  - type: Door
    crushDamage:
      types:
        Blunt: 15
    openSound:
      path: /Audio/Machines/airlock_open.ogg
    closeSound:
      path: /Audio/Machines/airlock_close.ogg
    denySound:
      path: /Audio/Machines/airlock_deny.ogg
  - type: ContainerContainer
    containers:
      board: !type:Container
  - type: Weldable
    fuel: 5
    time: 3
  - type: Airlock
  - type: NavMapDoor
  - type: DoorBolt
  - type: Appearance
  - type: WiresVisuals
  - type: ElectrocutionHUDVisuals
  - type: ApcPowerReceiver
    powerLoad: 20
  - type: ExtensionCableReceiver
  - type: Electrified
    enabled: false
    usesApcPower: true
  - type: WiresPanel
  - type: WiresPanelSecurity
  - type: Wires
    boardName: wires-board-name-airlock
    layoutId: Airlock
  - type: DoorSignalControl
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: BasicDevice
  - type: WirelessNetworkConnection
    range: 200
  - type: DeviceLinkSink
    ports:
      - Open
      - Close
      - Toggle
      - AutoClose
      - DoorBolt
  - type: DeviceLinkSource
    ports:
      - DoorStatus
    lastSignals:
      DoorStatus: false
  - type: SoundOnOverload
  - type: SpawnOnOverload
  - type: UserInterface
    interfaces:
      enum.AiUi.Key:
        type: StationAiBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: Airtight
    noAirWhenFullyAirBlocked: false
  - type: RadiationBlocker
    resistance: 5
  - type: Occluder
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: StrongMetallic
  - type: RCDDeconstructable
    cost: 6
    delay: 8
    fx: EffectRCDDeconstruct8
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 500
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      # TODO this should go to the broken node first
      - !type:PlaySoundBehavior
        sound:
          collection: MetalSlam
  - type: Construction
    graph: Airlock
    node: airlock
    containers:
    - board
  - type: PlacementReplacement
    key: walls
  - type: PaintableAirlock
    group: Standard
    department: Civilian
  - type: StaticPrice
    price: 150
  - type: LightningTarget
    priority: 1
  - type: Tag
    tags:
      - Airlock
      # This tag is used to nagivate the Airlock construction graph. It's needed because the construction graph is shared between Airlock, AirlockGlass, and HighSecDoor
  - type: PryUnpowered
  - type: BlockWeather
  - type: InteractionVerbs
    allowedVerbs:
    - KnockOn
  - type: TwistedConstructionTarget
    replacementProto: CultDoor
    doAfterDelay: 10
  placement:
    mode: SnapgridCenter

- type: entity
  id: AirlockRCDResistant
  parent: Airlock
  abstract: true
  components:
  - type: RCDDeconstructable
    deconstructable: false

- type: entity
  id: AirlockGlass
  parent: Airlock
  name: "скляний шлюз"
  components:
  - type: MeleeSound
    soundGroups:
      Brute:
        collection: GlassSmack
  - type: Door
    occludes: false
  - type: Occluder
    enabled: false
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Glass/glass.rsi
  - type: AnimationPlayer
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.49,-0.49,0.49,0.49" # don't want this colliding with walls or they won't close
        density: 100
        mask:
        - FullTileMask
        layer:     #removed opaque from the layer, allowing lasers to pass through glass airlocks
        - GlassAirlockLayer
  - type: LayerChangeOnWeld
    unWeldedLayer: GlassAirlockLayer
    weldedLayer: GlassLayer
  - type: Construction
    graph: Airlock
    node: glassAirlock
  - type: PaintableAirlock
    group: Glass
  - type: RadiationBlocker
    resistance: 1
  - type: Tag
    tags:
      - GlassAirlock
      # This tag is used to nagivate the Airlock construction graph. It's needed because the construction graph is shared between Airlock, AirlockGlass, and HighSecDoor
