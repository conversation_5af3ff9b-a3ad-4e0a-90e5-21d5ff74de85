# Base

- type: entity
  id: SpawnPointJobBase
  parent: MarkerBase
  abstract: true
  suffix: Job Spawn
  components:
  - type: SpawnPoint
    spawn_type: Job
  - type: Sprite
    sprite: Markers/jobs.rsi

- type: entity
  name: "точка спавну спостерігача"
  id: SpawnPointObserver
  parent: MarkerBase
  components:
  - type: Sprite
    sprite: Markers/jobs.rsi
    layers:
      - state: green
      - sprite: Mobs/Ghosts/ghost_human.rsi
        state: icon
    state: observer
  - type: SpawnPoint
    spawn_type: Observer

- type: entity
  name: "точка спавну зайшовших пізніше"
  id: SpawnPointLatejoin
  parent: SpawnPointJobBase
  components:
  - type: Sprite
    state: green
  - type: SpawnPoint
    spawn_type: LateJoin

# Logistics

- type: entity
  id: SpawnPointQuartermaster
  parent: SpawnPointJobBase
  name: "голова Карго" # DeltaV - Logistics Department replacing Cargo
  components:
  - type: SpawnPoint
    job_id: Quartermaster
  - type: Sprite
    layers:
      - state: green
      - state: qm


- type: entity
  id: SpawnPointCargoTechnician
  parent: SpawnPointJobBase
  name: "вантажник"
  components:
  - type: SpawnPoint
    job_id: CargoTechnician
  - type: Sprite
    layers:
      - state: green
      - state: cargo_tech

- type: entity
  id: SpawnPointSalvageSpecialist
  parent: SpawnPointJobBase
  name: "шахтар"
  components:
  - type: SpawnPoint
    job_id: SalvageSpecialist
  - type: Sprite
    layers:
      - state: green
      - sprite: DeltaV/Markers/jobs.rsi # DeltaV - Salvage specialist look change
        state: salvagespecialist

# Civilian

- type: entity
  id: SpawnPointPassenger
  parent: SpawnPointJobBase
  name: "пасажир"
  components:
  - type: SpawnPoint
    job_id: Passenger
  - type: Sprite
    layers:
      - state: green
      - state: passenger

- type: entity
  id: SpawnPointTechnicalAssistant
  parent: SpawnPointJobBase
  name: "технічний асистент"
  components:
  - type: SpawnPoint
    job_id: TechnicalAssistant
  - type: Sprite
    layers:
      - state: green
      - state: technicalassistant

- type: entity
  id: SpawnPointMedicalIntern
  parent: SpawnPointJobBase
  name: "лікар-інтерн"
  components:
  - type: SpawnPoint
    job_id: MedicalIntern
  - type: Sprite
    layers:
      - state: green
      - state: medicalintern

- type: entity
  id: SpawnPointSecurityCadet
  parent: SpawnPointJobBase
  name: "курсант служби безпеки"
  components:
  - type: SpawnPoint
    job_id: SecurityCadet
  - type: Sprite
    layers:
      - state: green
      - state: security_cadet

- type: entity
  id: SpawnPointResearchAssistant
  parent: SpawnPointJobBase
  name: "науковий співробітник"
  components:
  - type: SpawnPoint
    job_id: ResearchAssistant
  - type: Sprite
    layers:
      - state: green
      - state: researchassistant

- type: entity
  id: SpawnPointServiceWorker
  parent: SpawnPointJobBase
  name: "сервісний працівник"
  components:
  - type: SpawnPoint
    job_id: ServiceWorker
  - type: Sprite
    layers:
      - state: green
      - state: serviceworker

- type: entity
  id: SpawnPointBartender
  parent: SpawnPointJobBase
  name: "бармен"
  components:
  - type: SpawnPoint
    job_id: Bartender
  - type: Sprite
    layers:
      - state: green
      - state: bartender

- type: entity
  id: SpawnPointChef
  parent: SpawnPointJobBase
  name: "шеф-кухар"
  components:
  - type: SpawnPoint
    job_id: Chef
  - type: Sprite
    layers:
      - state: green
      - state: chef

- type: entity
  id: SpawnPointBotanist
  parent: SpawnPointJobBase
  name: "ботанік"
  components:
  - type: SpawnPoint
    job_id: Botanist
  - type: Sprite
    layers:
      - state: green
      - state: botanist

- type: entity
  id: SpawnPointClown
  parent: SpawnPointJobBase
  name: "клоун"
  components:
  - type: SpawnPoint
    job_id: Clown
  - type: Sprite
    layers:
      - state: green
      - state: clown

- type: entity
  id: SpawnPointMime
  parent: SpawnPointJobBase
  name: "мім"
  components:
  - type: SpawnPoint
    job_id: Mime
  - type: Sprite
    layers:
      - state: green
      - state: mime

- type: entity
  id: SpawnPointChaplain
  parent: SpawnPointJobBase
  name: "капелан"
  components:
  - type: SpawnPoint
    job_id: Chaplain
  - type: Sprite
    layers:
      - state: green
      - state: chaplain

- type: entity
  id: SpawnPointLibrarian
  parent: SpawnPointJobBase
  name: "бібліотекар"
  components:
  - type: SpawnPoint
    job_id: Librarian
  - type: Sprite
    layers:
      - state: green
      - state: librarian

- type: entity
  id: SpawnPointLawyer
  parent: SpawnPointJobBase
  name: "адвокат"
  components:
  - type: SpawnPoint
    job_id: Lawyer
  - type: Sprite
    layers:
      - state: green
      - state: lawyer

- type: entity
  id: SpawnPointJanitor
  parent: SpawnPointJobBase
  name: "прибиральник"
  components:
  - type: SpawnPoint
    job_id: Janitor
  - type: Sprite
    layers:
      - state: green
      - state: janitor

- type: entity
  id: SpawnPointMusician
  parent: SpawnPointJobBase
  name: "музикант"
  components:
  - type: SpawnPoint
    job_id: Musician
  - type: Sprite
    layers:
      - state: green
      - state: musician

- type: entity
  id: SpawnPointBoxer
  parent: SpawnPointJobBase
  name: "боксер"
  components:
  - type: SpawnPoint
    job_id: Boxer
  - type: Sprite
    layers:
      - state: green
      - state: boxer

- type: entity
  id: SpawnPointBorg
  parent: SpawnPointJobBase
  name: "кіборг"
  components:
  - type: SpawnPoint
    job_id: Borg
  - type: Sprite
    layers:
    - state: green
    - sprite: Mobs/Silicon/chassis.rsi
      state: robot
    - sprite: Mobs/Silicon/chassis.rsi
      state: robot_e

# Command

- type: entity
  id: SpawnPointCaptain
  parent: SpawnPointJobBase
  name: "капітан"
  components:
  - type: SpawnPoint
    job_id: Captain
  - type: Sprite
    layers:
      - state: green
      - state: captain

- type: entity
  id: SpawnPointHeadOfPersonnel
  parent: SpawnPointJobBase
  name: "голова Персоналу"
  components:
  - type: SpawnPoint
    job_id: HeadOfPersonnel
  - type: Sprite
    layers:
      - state: green
      - state: hop

# Engineering

- type: entity
  id: SpawnPointChiefEngineer
  parent: SpawnPointJobBase
  name: "головний Інженер"
  components:
  - type: SpawnPoint
    job_id: ChiefEngineer
  - type: Sprite
    layers:
      - state: green
      - state: ce

- type: entity
  id: SpawnPointSeniorEngineer
  parent: SpawnPointJobBase
  name: "старший інженер"
  components:
  - type: SpawnPoint
    job_id: SeniorEngineer
  - type: Sprite
    layers:
      - state: green
      - state: seniorengineer

- type: entity
  id: SpawnPointStationEngineer
  parent: SpawnPointJobBase
  name: "інженер"
  components:
  - type: SpawnPoint
    job_id: StationEngineer
  - type: Sprite
    layers:
      - state: green
      - state: engineer

- type: entity
  id: SpawnPointAtmos
  parent: SpawnPointJobBase
  name: "атмосферний технік"
  components:
  - type: SpawnPoint
    job_id: AtmosphericTechnician
  - type: Sprite
    layers:
      - state: green
      - state: atmospherics

# Medical

- type: entity
  id: SpawnPointChiefMedicalOfficer
  parent: SpawnPointJobBase
  name: "головний Лікар"
  components:
  - type: SpawnPoint
    job_id: ChiefMedicalOfficer
  - type: Sprite
    layers:
      - state: green
      - state: cmo

- type: entity
  id: SpawnPointSeniorPhysician
  parent: SpawnPointJobBase
  name: "старший лікар"
  components:
  - type: SpawnPoint
    job_id: SeniorPhysician
  - type: Sprite
    layers:
      - state: green
      - state: seniorphysician

- type: entity
  id: SpawnPointMedicalDoctor
  parent: SpawnPointJobBase
  name: "лікар"
  components:
  - type: SpawnPoint
    job_id: MedicalDoctor
  - type: Sprite
    layers:
      - state: green
      - state: doctor

- type: entity
  id: SpawnPointParamedic
  parent: SpawnPointJobBase
  name: "парамедик"
  components:
  - type: SpawnPoint
    job_id: Paramedic
  - type: Sprite
    layers:
      - state: green
      - state: paramedic

- type: entity
  id: SpawnPointChemist
  parent: SpawnPointJobBase
  name: "хімік"
  components:
  - type: SpawnPoint
    job_id: Chemist
  - type: Sprite
    layers:
      - state: green
      - state: chemist

# Science

- type: entity
  id: SpawnPointResearchDirector
  parent: SpawnPointJobBase
  name: "науковий Директор" # DeltaV - Epistemics Department replacing Science
  components:
  - type: SpawnPoint
    job_id: ResearchDirector
  - type: Sprite
    layers:
      - state: green
      - sprite: DeltaV/Markers/jobs.rsi # DeltaV - Epistemics new labcoats
        state: mystagogue

- type: entity
  id: SpawnPointSeniorResearcher
  parent: SpawnPointJobBase
  name: "старший вчений"
  components:
  - type: SpawnPoint
    job_id: SeniorResearcher
  - type: Sprite
    layers:
      - state: green
      - state: seniorresearcher

- type: entity
  id: SpawnPointScientist
  parent: SpawnPointJobBase
  name: "вчений"
  components:
  - type: SpawnPoint
    job_id: Scientist
  - type: Sprite
    layers:
      - state: green
      - sprite: DeltaV/Markers/jobs.rsi # DeltaV - Epistemics new labcoats
        state: scientist

- type: entity
  id: SpawnPointRoboticist
  parent: SpawnPointJobBase
  name: "робототехнік"
  components:
  - type: SpawnPoint
    job_id: Roboticist
  - type: Sprite
    layers:
    - state: green
    - state: roboticist

# Security

- type: entity
  id: SpawnPointHeadOfSecurity
  parent: SpawnPointJobBase
  name: "голова Безпеки"
  components:
  - type: SpawnPoint
    job_id: HeadOfSecurity
  - type: Sprite
    layers:
      - state: green
      - state: hos

- type: entity
  id: SpawnPointWarden
  parent: SpawnPointJobBase
  name: "варден"
  components:
  - type: SpawnPoint
    job_id: Warden
  - type: Sprite
    layers:
      - state: green
      - state: warden

- type: entity
  id: SpawnPointSeniorOfficer
  parent: SpawnPointJobBase
  name: "старший офіцер"
  components:
  - type: SpawnPoint
    job_id: SeniorOfficer
  - type: Sprite
    layers:
      - state: green
      - state: seniorofficer

- type: entity
  id: SpawnPointSecurityOfficer
  parent: SpawnPointJobBase
  name: "офіцер"
  components:
  - type: SpawnPoint
    job_id: SecurityOfficer
  - type: Sprite
    layers:
      - state: green
      - state: security_officer

- type: entity
  id: SpawnPointDetective
  parent: SpawnPointJobBase
  name: "детектив"
  components:
  - type: SpawnPoint
    job_id: Detective
  - type: Sprite
    layers:
      - state: green
      - state: detective

- type: entity
  id: SpawnPointBrigmedic
  parent: SpawnPointJobBase
  name: "брігмедик" # DeltaV - rename brigmedic to corpsman
  components:
  - type: SpawnPoint #Delta V
    job_id: Brigmedic
  - type: Sprite
    layers:
      - state: green
      - state: brigmedic

# SPECIAL
# ERT
- type: entity
  id: SpawnPointERTLeader
  parent: SpawnPointJobBase
  name: "лідер ГШР"
  components:
  - type: SpawnPoint
    job_id: ERTLeader
  - type: Sprite
    layers:
      - state: green
      - state: ertleader

- type: entity
  id: SpawnPointERTChaplain
  parent: SpawnPointJobBase
  name: "капелан ERT"
  components:
  - type: SpawnPoint
    job_id: ERTChaplain
  - type: Sprite
    layers:
      - state: green
      - state: chaplain

- type: entity
  id: SpawnPointERTEngineer
  parent: SpawnPointJobBase
  name: "інжинер ГШР"
  components:
  - type: SpawnPoint
    job_id: ERTEngineer
  - type: Sprite
    layers:
      - state: green
      - state: ertengineer

- type: entity
  id: SpawnPointERTMedical
  parent: SpawnPointJobBase
  name: "медик ГШР"
  components:
  - type: SpawnPoint
    job_id: ERTMedical
  - type: Sprite
    layers:
      - state: green
      - state: ertmedical

- type: entity
  id: SpawnPointERTSecurity
  parent: SpawnPointJobBase
  name: "офіцер ГШР"
  components:
  - type: SpawnPoint
    job_id: ERTSecurity
  - type: Sprite
    layers:
      - state: green
      - state: ertsecurity

- type: entity
  id: SpawnPointERTJanitor
  parent: SpawnPointJobBase
  name: "прибиральник ГШР"
  components:
  - type: SpawnPoint
    job_id: ERTJanitor
  - type: Sprite
    layers:
      - state: green
      - state: ertjanitor

# STATION SPECIFIC

- type: entity
  id: SpawnPointReporter
  parent: SpawnPointJobBase
  name: "репортер"
  components:
  - type: SpawnPoint
    job_id: Reporter
  - type: Sprite
    layers:
      - state: green
      - state: reporter

- type: entity
  id: SpawnPointPsychologist
  parent: SpawnPointJobBase
  name: "психолог"
  components:
  - type: SpawnPoint
    job_id: Psychologist
  - type: Sprite
    layers:
      - state: green
      - state: psychologist

- type: entity
  id: SpawnPointZookeeper
  parent: SpawnPointJobBase
  name: "доглядач зоопарку"
  components:
  - type: SpawnPoint
    job_id: Zookeeper
  - type: Sprite
    layers:
      - state: green
      - state: zookeeper
