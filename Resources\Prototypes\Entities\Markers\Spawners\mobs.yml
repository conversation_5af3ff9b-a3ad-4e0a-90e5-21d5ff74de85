# Base

- type: entity
  name: "Спавнер Миші"
  id: SpawnMobMouse
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - sprite: Mobs/Animals/mouse.rsi
        state: icon-2
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobMouse
      - MobMouse1
      - MobMouse2

- type: entity
  name: "Спавнер Таргана"
  id: SpawnMobCockroach
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - sprite: Mobs/Animals/cockroach.rsi
        state: cockroach_icon
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobCockroach

- type: entity
  name: "Спавнер Коргі ГП"
  id: SpawnMobCorgi
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobCorgiIan
      - MobCorgiIanOld
      - MobCorgiLisa
      - MobCorgiIanPup

- type: entity
  name: "Спавнер Опосума Морті"
  id: SpawnMobPossumMorty
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobPossumMortyOld

- type: entity
  name: "Спавнер Єнота Мортіша"
  id: SpawnMobRaccoonMorticia
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobRaccoonMorticia

- type: entity
  name: "Спавнер Дрона"
  id: SpawnMobDrone
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - sprite: Mobs/Silicon/drone.rsi
        state: shell

- type: entity
  name: "Спавнер Лисиці Рено"
  id: SpawnMobFoxRenault
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobFoxRenault

- type: entity
  name: "Спавнер Рантайму"
  id: SpawnMobCatRuntime
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobCatRuntime

- type: entity
  name: "Спавнер Виняткуна"
  id: SpawnMobCatException
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobCatException

- type: entity
  name: "Спавнер Тропіко"
  id: SpawnMobCrabAtmos
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobCrabAtmos

- type: entity
  name: "Спавнер Флоппи"
  id: SpawnMobCatFloppa
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobCatFloppa

- type: entity
  name: "Спавнер Бінгуса"
  id: SpawnMobCatBingus
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobBingus

- type: entity
  name: "Спавнер Космічного Кота"
  id: SpawnMobCatSpace
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobCatSpace

- type: entity
  name: "Спавнер Кошеняти"
  id: SpawnMobCatKitten
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobCatKitten

- type: entity
  name: "Спавнер Кота"
  id: SpawnMobCat
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobCatRuntime
      - MobCatException
      - MobCatFloppa
      - MobBingus

- type: entity
  name: "Спавнер Звичайного Кота"
  id: SpawnMobCatGeneric
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: RandomSpawner
    prototypes:
      - MobCat
      - MobCatCalico
      - MobCatCaracal
      - MobCatKitten
    rarePrototypes:
      - MobCatSpace
    rareChance: 0.05

- type: entity
  name: "Спавнер Бандіто"
  id: SpawnMobBandito
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobBandito

- type: entity
  name: "Спавнер МакГріфф"
  id: SpawnMobMcGriff
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobMcGriff

- type: entity
  name: "Спавнер Лінивого Бюрократа"
  id: SpawnMobSlothPaperwork
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobPaperwork

- type: entity
  name: "Спавнер Вальтера"
  id: SpawnMobWalter
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobWalter

- type: entity
  name: "Спавнер Космічного Ведмедя"
  id: SpawnMobBear
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: bear
      sprite: Mobs/Animals/bear.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobBearSpace

- type: entity
  name: "Спавнер Космічного Коропа"
  id: SpawnMobCarp
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: icon
      sprite: Mobs/Aliens/Carps/space.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobCarp

- type: entity
  name: "Спавнер Магікарпа"
  id: SpawnMobCarpMagic
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: icon
      sprite: Mobs/Aliens/Carps/magic.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobCarpMagic

- type: entity
  name: "Спавнер Голокарпа"
  id: SpawnMobCarpHolo
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: icon
      sprite: Mobs/Aliens/Carps/holo.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobCarpHolo

- type: entity
  name: "Спавнер Космічної Акульяни"
  id: SpawnMobShark
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: icon
      sprite: Mobs/Aliens/Carps/sharkminnow.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobShark

- type: entity
  name: "Спавнер Хом'яка Гамлета"
  id: SpawnMobHamsterHamlet
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: icon-0
        sprite: Mobs/Animals/hamster.rsi
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - CrateNPCHamlet

- type: entity
  name: "Спавнер Александра"
  id: SpawnMobAlexander
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobAlexander

- type: entity
  name: "Спавнер Шиви"
  id: SpawnMobShiva
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: shiva
        sprite: Mobs/Pets/shiva.rsi
  - type: ConditionalSpawner
    prototypes:
      - MobSpiderShiva

- type: entity
  name: "Спавнер Віллоу"
  id: SpawnMobKangarooWillow
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: kangaroo
      sprite: Mobs/Animals/kangaroo.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobKangarooWillow

- type: entity
  name: "Спавнер Космічних Кенгуру"
  id: SpawnMobKangaroo
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: kangaroo-space
      sprite: Mobs/Animals/kangaroo.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobKangarooSpace

- type: entity
  name: "Спавнер Боксерського Кенгуру"
  id: SpawnMobBoxingKangaroo
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: kangaroo
      sprite: Mobs/Animals/kangaroo.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobBoxingKangaroo

- type: entity
  name: "Спавнер Космічного Павука"
  id: SpawnMobSpaceSpider
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: spacespider
      sprite: Mobs/Animals/spacespider.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobSpiderSpace

- type: entity
  name: "Спавнер Космічної Кобри"
  id: SpawnMobSpaceCobra
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: spacecobra
      sprite: Mobs/Animals/spacecobra.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobCobraSpace

- type: entity
  name: "Спавнер Синього Слайма"
  id: SpawnMobAdultSlimesBlue
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobAdultSlimesBlue

- type: entity
  name: "Спавнер Злого Синього Слайма"
  id: SpawnMobAdultSlimesBlueAngry
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobAdultSlimesBlueAngry

- type: entity
  name: "Спавнер Зеленого Слайма"
  id: SpawnMobAdultSlimesGreen
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobAdultSlimesGreen

- type: entity
  name: "Спавнер Злого Зеленого Слайма"
  id: SpawnMobAdultSlimesGreenAngry
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobAdultSlimesGreenAngry

- type: entity
  name: "Спавнер Жовтого Слайма"
  id: SpawnMobAdultSlimesYellow
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobAdultSlimesYellow

- type: entity
  name: "Спавнер Злого Жовтого Слайма"
  id: SpawnMobAdultSlimesYellowAngry
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobAdultSlimesYellowAngry

- type: entity
  name: "Спавнер Смайл"
  id: SpawnMobSmile
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: ai
  - type: ConditionalSpawner
    prototypes:
      - MobSlimesPet

- type: entity
  name: "Спавнер Пун Пуна"
  id: SpawnMobMonkeyPunpun
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: monkey
      sprite: Mobs/Animals/monkey.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobMonkeyPunpun

- type: entity
  name: "спавнер Біхонкера"
  id: SpawnMobBehonker
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: base
      sprite: Objects/Weapons/Guns/Battery/behonker_eye.rsi
  - type: ConditionalSpawner
    prototypes:
      - MobBehonkerIce
      - MobBehonkerPyro
      - MobBehonkerElectrical
      - MobBehonkerGrav

- type: entity
  name: "Спавнер Мавпи"
  id: SpawnMobMonkey
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: monkey
      sprite: Mobs/Animals/monkey.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobMonkey

- type: entity
  name: "Спавнер Пурпурної Змії"
  id: SpawnMobPurpleSnake
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: purple_snake
      sprite: Mobs/Aliens/Xenos/purple_snake.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobPurpleSnake

- type: entity
  name: "Спавнер Малої Пурпурної Змії"
  id: SpawnMobSmallPurpleSnake
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: small_purple_snake
      sprite: Mobs/Aliens/Xenos/purple_snake.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobSmallPurpleSnake

- type: entity
  name: "Спавнер Слимака"
  id: SpawnMobSlug
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: slug
      sprite: Mobs/Animals/slug.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobSlug

- type: entity
  name: "Спавнер Ящірки"
  id: SpawnMobLizard
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: lizard
      sprite: Mobs/Animals/lizard.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobLizard

- type: entity
  name: "Спавнер Краба"
  id: SpawnMobCrab
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: crab
      sprite: Mobs/Animals/crab.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobCrab

- type: entity
  name: "Спавнер Кози"
  id: SpawnMobGoat
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: goat
      sprite: Mobs/Animals/goat.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobGoat

- type: entity
  name: "Спавнер Жаби"
  id: SpawnMobFrog
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: frog
      sprite: Mobs/Animals/frog.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobFrog

- type: entity
  name: "Спавнер Бджоли"
  id: SpawnMobBee
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: 0
      sprite: Mobs/Animals/bee.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobBee

- type: entity
  name: "Спавнер Папуги"
  id: SpawnMobParrot
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: parrot
      sprite: Mobs/Animals/parrot.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobParrot

- type: entity
  name: "Спавнер Метелика"
  id: SpawnMobButterfly
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: butterfly
      sprite: Mobs/Animals/butterfly.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobButterfly

- type: entity
  name: "Спавнер Корови"
  id: SpawnMobCow
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: cow
      sprite: Mobs/Animals/cow.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobCow

- type: entity
  name: "Спавнер Горили"
  id: SpawnMobGorilla
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: crawling
      sprite: Mobs/Animals/gorilla.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobGorilla

- type: entity
  name: "Спавнер Пінгвіна"
  id: SpawnMobPenguin
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: penguin
      sprite: Mobs/Animals/penguin.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobPenguin


- type: entity
  name: "Спавнер Пекельного Відроддя"
  id: SpawnMobHellspawn
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: hellspawn
      sprite: Markers/mobs.rsi
  - type: ConditionalSpawner
    prototypes:
      - MobHellspawn

- type: entity
  name: "спавнер рудного краба"
  id: SpawnMobOreCrab
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: quartz_crab
      sprite: Mobs/Elemental/orecrab.rsi
  - type: RandomSpawner
    prototypes:
      - MobSpawnCrabUranium
      - MobSpawnCrabIron
      - MobSpawnCrabQuartz
      - MobSpawnCrabSilver

- type: entity
  name: "спавнер людини, що світиться"
  id: SpawnMobLuminousPerson
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: luminous_person
      sprite: Mobs/Elemental/living_light/luminous_person.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobLuminousPerson

- type: entity
  name: "спавнер об'єктів, що світяться"
  id: SpawnMobLuminousObject
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: luminous_object
      sprite: Mobs/Elemental/living_light/luminous_object.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobLuminousObject

- type: entity
  name: "спавнер ентіті, що світяться"
  id: SpawnMobLuminousEntity
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: luminous_entity
      sprite: Mobs/Elemental/living_light/luminous_entity.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobLuminousEntity

- type: entity
  name: "нерест павука-клоуна"
  id: SpawnClownSpider
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: clown
      sprite: Mobs/Animals/clownspider.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobClownSpider

- type: entity
  name: "Спавнер Піббла"
  id: SpawnMobPibble
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - sprite: Mobs/Pets/pitbull.rsi
        state: pibble
  - type: ConditionalSpawner
    prototypes:
      - MobPibble
