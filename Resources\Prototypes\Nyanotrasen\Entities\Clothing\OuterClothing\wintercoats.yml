- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterCoatHyenhSweater
  name: "зручний светр"
  description: "Справді, зручно."
  components:
    - type: Sprite
      sprite: Nyanotrasen/Clothing/OuterClothing/WinterCoats/hyenh.rsi
    - type: Clothing
      sprite: Nyanotrasen/Clothing/OuterClothing/WinterCoats/hyenh.rsi
    - type: TemperatureProtection
      coefficient: 0.1

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterWinterCoatLong
  name: "довге зимове пальто"
  description: "У цьому стильному пальті будуть в теплі навіть ваші ноги."
  components:
    - type: Sprite
      sprite: Nyanotrasen/Clothing/OuterClothing/WinterCoats/long_winter_coat.rsi
    - type: Clothing
      sprite: Nyanotrasen/Clothing/OuterClothing/WinterCoats/long_winter_coat.rsi

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterWinterCoatPlaid
  name: "зимове пальто в клітинку"
  description: "Він може бути виготовлений з натуральної вовни."
  components:
    - type: Sprite
      sprite: Nyanotrasen/Clothing/OuterClothing/WinterCoats/plaid_winter_coat.rsi
    - type: Clothing
      sprite: Nyanotrasen/Clothing/OuterClothing/WinterCoats/plaid_winter_coat.rsi

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterWinterCoatMail
  name: "зимове пальто листоноші"
  description: "Він захистить від холоду, але не від собак."
  components:
    - type: ToggleableClothing #DeltaV - fixing the fact that it has no hood
      clothingPrototype: ClothingHeadHatHoodWinterMailCarrier
      slot: head
    - type: ContainerContainer
      containers:
        toggleable-clothing: !type:Container {}
        storagebase: !type:Container
          ents: []
    - type: Sprite
      sprite: Nyanotrasen/Clothing/OuterClothing/WinterCoats/mail_winter_coat.rsi
    - type: Clothing
      sprite: Nyanotrasen/Clothing/OuterClothing/WinterCoats/mail_winter_coat.rsi

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterWinterCoatMantis
  name: "зимове пальто мантіса"
  description: "Розкривайте холодні справи зі стилем."
  components:
    - type: Sprite
      sprite: Nyanotrasen/Clothing/OuterClothing/WinterCoats/mantis_winter_coat.rsi
    - type: Clothing
      sprite: Nyanotrasen/Clothing/OuterClothing/WinterCoats/mantis_winter_coat.rsi

- type: entity
  parent: ClothingOuterWinterRD
  id: ClothingOuterWinterMystagogue
  name: "зимове пальто містагога"
