# No idea why it's in sci but we ball.
- type: job
  id: StationAi
  name: job-name-station-ai
  description: job-description-station-ai
  playTimeTracker: JobStationAi
  requirements:
  - !type:CharacterPlaytimeRequirement
    tracker: JobBorg
    min: 18000  # 5 hrs
  canBeAntag: false
  icon: JobIconStationAi
  supervisors: job-supervisors-rd
  jobEntity: StationAiBrain
  nameDataset: NamesAI
  spawnLoadout: false
  applyTraits: false
  canHavePassport: false

- type: job
  id: Borg
  name: job-name-borg
  description: job-description-borg
  playTimeTracker: JobBorg
  requirements:
    - !type:CharacterOverallTimeRequirement
      min: 72000 #Pirate 20 hrs
  canBeAntag: false
  icon: JobIconBorg
  supervisors: job-supervisors-rd
  jobEntity: PlayerBorgBattery
  canHavePassport: false
