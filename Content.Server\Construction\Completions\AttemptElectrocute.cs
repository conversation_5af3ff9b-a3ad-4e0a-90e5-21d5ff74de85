using Content.Server.Electrocution;
using Content.Shared.Electrocution;
using Content.Shared.Construction;

namespace Content.Server.Construction.Completions;

[DataDefinition]
public sealed partial class AttemptElectrocute : IGraphAction
{
    public void PerformAction(EntityUid uid, EntityUid? userUid, IEntityManager entityManager)
    {
        if (userUid == null)
            return;

        if (!entityManager.TryGetComponent<ElectrifiedComponent>(uid, out var electrified))
            return;

        var currentValue = electrified.Enabled;
        electrified.Enabled = true;

        entityManager.EntitySysManager.GetEntitySystem<ElectrocutionSystem>().TryDoElectrifiedAct(uid, userUid.Value, electrified: electrified);

        electrified.Enabled = currentValue;
    }
}
