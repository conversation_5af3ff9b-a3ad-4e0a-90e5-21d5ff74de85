- type: entity
  name: "спавнер випадкового вівтарю"
  id: AltarSpawner
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
          state: nanotrasen
    - type: RandomSpawner
      prototypes:
        - AltarNanotrasen
        - AltarChaos
        - AltarDruid
        - AltarToolbox
        - AltarSpaceChristian
        - AltarSatana
        - AltarTechnology
        - AltarCosmic #imp edit (cosmic cult)
      offset: 0.0

- type: entity
  name: "спавнер випадкового конверт-вівтарю"
  id: ConvertAltarSpawner
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
          state: convertaltar
    - type: RandomSpawner
      prototypes:
        - AltarConvert
        - AltarConvertFestival
        - AltarConvertMaint
        - AltarConvertBlue
        - AltarConvertBurden
        - AltarConvertOrange
        - AltarConvertRed
        - AltarConvertWhite
        - AltarConvertYellow
      offset: 0.0

- type: entity
  name: "спавнер випадковго вівтарю культу"
  id: CultAltarSpawner
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Structures/Furniture/Altars/Cults/fangs.rsi
          state: full
    - type: RandomSpawner
      prototypes:
        - AltarHeaven
        - AltarFangs
      offset: 0.0
