- type: entity
  parent: BaseStructureDynamic
  id: CrateGeneric
  categories: [ HideSpawnMenu ]
  name: "ящик"
  description: "Великий контейнер для речей."
  components:
  - type: Transform
    noRot: true
  - type: Icon
    sprite: Structures/Storage/Crates/generic.rsi
    state: icon
  - type: Sprite
    noRot: true
    sprite: Structures/Storage/Crates/generic.rsi
    layers:
    - state: base
      map: ["enum.StorageVisualLayers.Base"]
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: InteractionOutline
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.4,0.4,0.29"
        density: 50
        mask:
        - CrateMask #this is so they can go under plastic flaps
        layer:
        - MachineLayer
  - type: EntityStorage
  - type: PlaceableSurface
    isPlaceable: false # defaults to closed.
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
  - type: Appearance
  - type: EntityStorageVisuals
    stateDoorOpen: open
    stateDoorClosed: closed
  - type: GenericVisualizer
    visuals:
      enum.PaperLabelVisuals.HasLabel:
        enum.PaperLabelVisuals.Layer:
          True: { visible: true }
          False: { visible: false }
      enum.PaperLabelVisuals.LabelType:
        enum.PaperLabelVisuals.Layer:
          Paper: { state: paper }
          Bounty: { state: bounty }
          CaptainsPaper: { state: captains_paper }
          Invoice: { state: invoice }
  - type: PaperLabel
    labelSlot:
      insertVerbText: Attach Label
      ejectVerbText: Remove Label
      whitelist:
        components:
        - Paper
  - type: ContainerContainer
    containers:
      entity_storage: !type:Container
      paper_label: !type:ContainerSlot
  - type: ItemSlots
  - type: StaticPrice
    price: 50
  - type: Construction
    graph: CrateGenericSteel
    node: crategenericsteel
    containers:
      - entity_storage
  - type: RequireProjectileTarget

- type: entity
  parent: CrateGeneric
  id: CrateBaseWeldable
  categories: [ HideSpawnMenu ]
  components:
  - type: Weldable
  - type: ResistLocker

- type: entity
  parent: CrateBaseWeldable
  id: CrateBaseSecure
  suffix: Secure
  components:
  - type: Lock
  - type: LockVisuals
  - type: AccessReader
  - type: Icon
    sprite: Structures/Storage/Crates/secure.rsi
    state: icon
  - type: Sprite
    sprite: Structures/Storage/Crates/secure.rsi
    layers:
    - state: base
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
    - state: locked
      map: ["enum.LockVisualLayers.Lock"]
      shader: unshaded
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: StructuralMetallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 75
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
  - type: Construction
    graph: CrateSecure
    node: cratesecure
    containers:
    - entity_storage
  - type: Reflect
    reflects:
    - Energy
    reflectProb: 0.2
    spread: 90

- type: entity
  parent: CrateBaseSecure
  id: CrateBaseSecureReinforced
  suffix: Secure, Reinforced
  components:
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: StructuralMetallicStrong