﻿<controls:FancyWindow xmlns="https://spacestation14.io"
               xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
               xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
               Title="{Loc 'war-declarator-ui-header'}">
    <BoxContainer Orientation="Vertical" SeparationOverride="4" MinWidth="440">
        <TextEdit Name="MessageEdit"
                  HorizontalExpand="True"
                  MinHeight="200"
                  Access="Public" />
        <Button Name="WarButton"
                Text="{Loc 'war-declarator-ui-try-war-button'}"
                StyleClasses="Danger"
                Access="Public"/>
        <BoxContainer Orientation="Vertical" HorizontalExpand="True">
            <Label Name="StatusLabel"
                   Align="Center"
                   Access="Public"/>
            <Label Name="InfoLabel"
                   Align="Center"
                   Access="Public"/>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
