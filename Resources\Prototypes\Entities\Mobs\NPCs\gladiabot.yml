- type: entity
  parent: MobSiliconBase
  id: MobGladiaBot
  name: "гладіатор"
  description: "На славу!"
  components:
  - type: Sprite
    sprite: Mobs/Silicon/Bots/gladiabot.rsi
    state: GladiabotFFA
  - type: Inventory
    templateId: gladiabot
  - type: InventorySlots
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
  - type: Construction
    graph: GladiaBot
    node: bot
  - type: Stripping
  - type: Strippable
  - type: SentienceTarget
    flavorKind: station-event-random-sentience-flavor-mechanical
  - type: UseDelay
    delay: 1
  - type: NpcFactionMember
    factions:
    - GladiabotFFA
  - type: NpcFactionSelector
    selectableFactions:
    - GladiabotFFA
    - GladiabotRed
    - GladiabotBlue
    - GladiabotGreen
    - GladiabotYellow
  - type: EmagReplaceFactions
    additive: true
    factions:
    - Syndicate
  - type: NpcFactionSpriteStateSetter
  - type: CombatMode
  - type: MeleeWeapon
    altDisarm: false
    soundHit:
        path: /Audio/Weapons/bladeslice.ogg
    angle: 0
    animation: WeaponArcPunch
    damage:
      types:
        Slash: 3
  - type: MobThresholds
    thresholds:
      0: Alive
      40: Dead
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 30
      behaviors:
      - !type:TriggerBehavior
    - trigger:
        !type:DamageTrigger
        damage: 40
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          ProximitySensor:
            min: 1
            max: 1
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialCloth1:
            min: 1
            max: 1
      - !type:EmptyContainersBehaviour
        containers:
        - head
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: MovementSpeedModifier
    baseWalkSpeed: 2
    baseSprintSpeed: 3
  - type: HTN
    rootTask:
      task: GladiabotCompound
    blackboard:
      AttackDelayDeviation: !type:Single
        0.3
  - type: InteractionPopup
    interactSuccessString: petting-success-gladiabot
    interactFailureString: petting-failure-gladiabot
    interactSuccessSound:
      path: /Audio/Ambience/Objects/periodic_beep.ogg
