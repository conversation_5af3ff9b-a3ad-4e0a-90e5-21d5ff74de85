<BoxContainer xmlns="https://spacestation14.io"
         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         xmlns:s="clr-namespace:Content.Client.Stylesheets"
         xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
         xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
         Orientation="Vertical" HorizontalExpand ="True" Margin="0 0 0 3">

    <!-- Network selection button -->
    <Button Name="FocusButton" HorizontalExpand="True" VerticalExpand="True" Margin="0 0 6 8" StyleClasses="OpenLeft" Access="Public">
        <BoxContainer HorizontalExpand="True" VerticalExpand="True" Orientation="Vertical">
            <BoxContainer HorizontalExpand="True" VerticalExpand="True" Orientation="Horizontal" SetHeight="32">
                <PanelContainer Name="NetworkColorStripe" HorizontalAlignment="Left" SetWidth="8" VerticalExpand="True" Margin="-8 -2 0 0">
                    <PanelContainer.PanelOverride>
                        <gfx:StyleBoxFlat BackgroundColor="#d7d7d7"/>
                    </PanelContainer.PanelOverride>
                </PanelContainer>
                <Label Name="NetworkNameLabel" Text="???" HorizontalExpand="True" HorizontalAlignment="Center"/>
            </BoxContainer>

            <!-- Panel that appears on selecting the device -->
        
            <PanelContainer HorizontalExpand="True" Margin="-8 0 -14 -4" Access="Public">
                <PanelContainer.PanelOverride>
                    <gfx:StyleBoxFlat BackgroundColor="#25252a"/>
                </PanelContainer.PanelOverride>
                <BoxContainer Name="MainDataContainer" HorizontalExpand="True" VerticalExpand="True" Orientation="Vertical">
                    <Control>
                        <BoxContainer HorizontalExpand="True" VerticalExpand="True" Orientation="Vertical">
                            <BoxContainer HorizontalExpand="True" Orientation="Horizontal">
                                <Label Name="TemperatureHeaderLabel" Text="{Loc 'atmos-alerts-window-temperature-label'}" HorizontalAlignment="Center" HorizontalExpand="True" FontColorOverride="#a9a9a9" Margin="0 2 0 0" SetHeight="24"></Label>
                                <Label Name="PressureHeaderLabel" Text="{Loc 'atmos-alerts-window-pressure-label'}" HorizontalAlignment="Center" HorizontalExpand="True" FontColorOverride="#a9a9a9" Margin="0 2 0 0" SetHeight="24"></Label>
                                <Label Name="TotalMolHeaderLabel" Text="{Loc 'atmos-alerts-window-total-mol-label'}" HorizontalAlignment="Center" HorizontalExpand="True" FontColorOverride="#a9a9a9" Margin="0 2 0 0" SetHeight="24"></Label>
                            </BoxContainer>
                            <PanelContainer HorizontalExpand="True">
                                <PanelContainer.PanelOverride>
                                    <gfx:StyleBoxFlat BackgroundColor="#202023"/>
                                </PanelContainer.PanelOverride>
                                <BoxContainer HorizontalExpand="True" Orientation="Horizontal">
                                    <Label Name="TemperatureLabel" Text="???" HorizontalAlignment="Center" HorizontalExpand="True" FontColorOverride="#a9a9a9" Margin="0 2 0 0" SetHeight="24"></Label>
                                    <Label Name="PressureLabel" Text="???" HorizontalAlignment="Center" HorizontalExpand="True" FontColorOverride="#a9a9a9" Margin="0 2 0 0" SetHeight="24"></Label>
                                    <Label Name="TotalMolLabel" Text="???" HorizontalAlignment="Center" HorizontalExpand="True" FontColorOverride="#a9a9a9" Margin="0 2 0 0" SetHeight="24"></Label>
                                </BoxContainer>
                            </PanelContainer>
                            <BoxContainer HorizontalExpand="True" Orientation="Horizontal" Margin="8 0">
                                <TextureRect Name="ArrowTexture" VerticalAlignment="Center" SetSize="12 12" Stretch="KeepAspectCentered" Margin="3 0" TexturePath="/Textures/Interface/Nano/triangle_right.png"></TextureRect>
                                <Label Name="GasesHeaderLabel" Text="{Loc 'atmos-monitoring-window-label-gases'}" HorizontalAlignment="Left" HorizontalExpand="True" FontColorOverride="#a9a9a9" Margin="4 0 0 0" SetHeight="24"></Label>
                            </BoxContainer>
                    
                        </BoxContainer>
                    </Control>
            
                    <!-- Atmosphere status -->
                    <Control Name="FocusContainer" ReservesSpace="False"  Visible="False">
                        <!-- Main container for displaying atmospheric data -->
                        <BoxContainer HorizontalExpand="True" VerticalExpand="True" Orientation="Vertical">
                            <PanelContainer HorizontalExpand="True">
                                <PanelContainer.PanelOverride>
                                    <gfx:StyleBoxFlat BackgroundColor="#202023"/>
                                </PanelContainer.PanelOverride>

                                <!-- Gas entries added via C# code -->
                                <GridContainer Name="GasGridContainer" HorizontalExpand="True" Columns = "4"></GridContainer>
                            </PanelContainer>
                        </BoxContainer>
                    </Control>
                </BoxContainer>

                <!-- If the alarm is inactive, this is label is displayed instead -->
                <Label Name="NoDataLabel" Text="{Loc 'atmos-alerts-window-no-data-available'}" HorizontalAlignment="Center" Margin="0 15" FontColorOverride="#a9a9a9" ReservesSpace="False" Visible="False"></Label>
                
            </PanelContainer>
        </BoxContainer>
    </Button>
</BoxContainer>
