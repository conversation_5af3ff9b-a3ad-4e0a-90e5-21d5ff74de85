- type: entity
  name: "ку<PERSON><PERSON><PERSON><PERSON><PERSON>"
  parent: BaseItem
  id: Wirecutter
  description: "Воно ріже дріт."
  components:
  - type: EmitSoundOnPickup
    sound:
      path: /Audio/SimpleStation14/Items/Handling/wirecutter_pickup.ogg
      params:
        volume: -2
  - type: EmitSoundOnDrop
    sound:
      path: /Audio/SimpleStation14/Items/Handling/wirecutter_drop.ogg
      params:
        volume: -2
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Items/wirecutter_drop.ogg
      params:
        volume: -2
  - type: Tag
    tags:
    - PlantSampleTaker
    - Wirecutter
  - type: Sprite
    sprite: Objects/Tools/wirecutters.rsi
    layers:
    - state: cutters
      map: [ "enum.DamageStateVisualLayers.Base" ]
    - state: cutters-cutty-thingy
  - type: MeleeWeapon
    wideAnimationRotation: -90
    attackRate: 1.1
    range: 1.6
    damage:
      types:
        Blunt: 6.5
    heavyRateModifier: 1.4
    heavyDamageBaseModifier: 1.2
    maxTargets: 4
    soundHit:
      path: "/Audio/Items/wirecutter.ogg"
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 4
    soundHit:
      collection: MetalThud
  - type: Tool
    qualities:
      - Cutting
    useSound:
      path: /Audio/Items/wirecutter.ogg
  - type: RandomSprite
    available:
      - enum.DamageStateVisualLayers.Base:
          cutters: Rainbow
  - type: Item
    sprite: Objects/Tools/wirecutters.rsi
    storedRotation: -90
  - type: ToolTileCompatible
  - type: PhysicalComposition
    materialComposition:
      Steel: 100
  - type: StaticPrice
    price: 30
  # Shitmed Change
  - type: Retractor
    speed: 0.35
  - type: Hemostat
    speed: 0.6
  - type: SurgeryTool
    startSound:
      path: /Audio/Items/wirecutter.ogg
      params:
        variation: 0.125
    endSound:
      path: /Audio/Items/wirecutter.ogg
      params:
        variation: 0.125
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 18
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  name: "викрутка"
  parent: BaseItem
  id: Screwdriver
  description: "Крутний момент промислового класу в невеликій викрутці."
  components:
  - type: EmitSoundOnPickup
    sound:
      path: /Audio/SimpleStation14/Items/Handling/screwdriver_pickup.ogg
      params:
        volume: -2
  - type: EmitSoundOnDrop
    sound:
      path: /Audio/SimpleStation14/Items/Handling/screwdriver_drop.ogg
      params:
        volume: -2
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Items/screwdriver_drop.ogg
      params:
        volume: -2
  - type: Tag
    tags:
    - Screwdriver
  - type: Sprite
    sprite: Objects/Tools/screwdriver.rsi
    layers:
      - state: screwdriver
        map: [ "enum.DamageStateVisualLayers.Base" ]
      - state: screwdriver-screwybits
  - type: Item
    sprite: Objects/Tools/screwdriver.rsi
    storedRotation: -90
  - type: MeleeWeapon
    wideAnimationRotation: -90
    attackRate: .8
    damage:
      types:
        Piercing: 6
    heavyRateModifier: 1.75
    heavyDamageBaseModifier: 1.5
    maxTargets: 1
    angle: 20
    soundHit:
      path: "/Audio/Weapons/bladeslice.ogg"
  - type: DamageOtherOnHit
    staminaCost: 5
  - type: ThrowingAngle
    angle: 270
  - type: EmbeddableProjectile
    sound: /Audio/Weapons/star_hit.ogg
    removalTime: 1
  - type: EmbedPassiveDamage
  - type: Tool
    qualities:
      - Screwing
    useSound:
      collection: Screwdriver
  - type: RandomSprite
    available:
      - enum.DamageStateVisualLayers.Base:
          screwdriver: Rainbow
  - type: PhysicalComposition
    materialComposition:
      Steel: 100
  - type: StaticPrice
    price: 30
  # Shitmed Change
  - type: Retractor
    speed: 0.45
  - type: Tending
    speed: 0.65
  - type: SurgeryTool
    startSound:
      collection: Screwdriver
    endSound:
      path: /Audio/Medical/Surgery/retractor2.ogg
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 12
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  name: "гайковий ключ"
  parent: BaseItem
  id: Wrench
  description: "Найпоширеніший інструмент для збирання та розбирання. Пам'ятайте: правою рукою туго, лівою вільно."
  components:
  - type: EmitSoundOnPickup
    sound:
      path: /Audio/SimpleStation14/Items/Handling/wrench_pickup.ogg
      params:
        volume: -2
  - type: EmitSoundOnDrop
    sound:
      path: /Audio/SimpleStation14/Items/Handling/wrench_drop.ogg
      params:
        volume: -2
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Items/wrench_drop.ogg
      params:
        volume: -2
  - type: Tag
    tags:
    - Wrench
  - type: Sprite
    sprite: Objects/Tools/wrench.rsi
    state: icon
  - type: Item
    sprite: Objects/Tools/wrench.rsi
    storedSprite:
      sprite: Objects/Tools/wrench.rsi
      state: storage
  - type: MeleeWeapon
    wideAnimationRotation: 135
    attackRate: 1.25
    range: 1.6
    damage:
      types:
        Blunt: 6.5
    bluntStaminaDamageFactor: 1.5
    heavyRateModifier: 1.5
    heavyDamageBaseModifier: 1.5
    angle: 100
    soundHit:
      collection: MetalThud
  - type: DamageOtherOnHit
  - type: Tool
    qualities:
      - Anchoring
    useSound:
      path: /Audio/Items/ratchet.ogg
  - type: PhysicalComposition
    materialComposition:
      Steel: 100
  - type: StaticPrice
    price: 22
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 17
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  name: "лом"
  parent: BaseItem
  id: Crowbar
  description: "Багатофункціональний інструмент для відчинення дверей та боротьби з міжпросторовими загарбниками."
  components:
  - type: EmitSoundOnPickup
    sound:
      path: /Audio/SimpleStation14/Items/Handling/crowbar_pickup.ogg
      params:
        volume: -2
  - type: EmitSoundOnDrop
    sound:
      path: /Audio/SimpleStation14/Items/Handling/crowbar_drop.ogg
      params:
        volume: -2
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Items/crowbar_drop.ogg
      params:
        volume: -2
  - type: Tag
    tags:
    - Crowbar
  - type: Sprite
    sprite: Objects/Tools/crowbar.rsi
    state: icon
  - type: Item
    sprite: Objects/Tools/crowbar.rsi
    size: Small
    storedSprite:
      sprite: Objects/Tools/crowbar.rsi
      state: storage
  - type: MeleeWeapon
    wideAnimationRotation: -135
    attackRate: .8
    damage:
      types:
        Blunt: 6
    bluntStaminaDamageFactor: 2
    heavyStaminaCost: 2.5
    soundHit:
      collection: MetalThud
  - type: DamageOtherOnHit
    staminaCost: 6
  - type: Tool
    qualities:
      - Prying
    useSound:
      path: /Audio/Items/crowbar.ogg
  - type: ToolTileCompatible
  - type: PhysicalComposition
    materialComposition:
      Steel: 100
  - type: StaticPrice
    price: 22
  - type: Prying
  # Shitmed Change
  - type: Tweezers
    speed: 0.55
  - type: SurgeryTool
    startSound: /Audio/Items/crowbar.ogg
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 28
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  parent: Crowbar
  id: CrowbarRed
  name: "аварійний лом"
  components:
  - type: Tag
    tags:
    - Crowbar
    - CrowbarRed
  - type: Sprite
    state: red-icon
  - type: Item
    heldPrefix: red
    storedSprite:
      sprite: Objects/Tools/crowbar.rsi
      state: red-storage

- type: entity
  name: "мультитул"
  parent: BaseItem
  id: Multitool
  description: "Просунутий інструмент для копіювання, зберігання та надсилання електричних імпульсів і сигналів через дроти та машини"
  components:
  - type: EmitSoundOnPickup
    sound: /Audio/SimpleStation14/Items/Handling/multitool_pickup.ogg
  - type: EmitSoundOnDrop
    sound: /Audio/SimpleStation14/Items/Handling/multitool_drop.ogg
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Items/multitool_drop.ogg
  - type: Sprite
    sprite: Objects/Tools/multitool.rsi
    layers:
    - state: icon
    - state: green-unlit
      shader: unshaded
  - type: MeleeWeapon
    attackRate: 1.3
    damage:
      types:
        Shock: 3
    heavyRateModifier: 3
    heavyRangeModifier: 1.25
    heavyDamageBaseModifier: 3
    maxTargets: 1
    angle: 10
  - type: DamageOtherOnHit
  - type: Item
    size: Small
  - type: Clothing
    sprite: Objects/Tools/multitool.rsi
    quickEquip: false
    slots:
    - Belt
  - type: Tool
    qualities:
      - Pulsing
  - type: NetworkConfigurator
  - type: ActivatableUI
    key: enum.NetworkConfiguratorUiKey.List
    inHandsOnly: true
  - type: UserInterface
    interfaces:
      enum.NetworkConfiguratorUiKey.List:
        type: NetworkConfiguratorBoundUserInterface
      enum.NetworkConfiguratorUiKey.Configure:
        type: NetworkConfiguratorBoundUserInterface
      enum.NetworkConfiguratorUiKey.Link:
        type: NetworkConfiguratorBoundUserInterface
  - type: Tag
    tags:
      - Multitool
      - DoorElectronicsConfigurator
  - type: PhysicalComposition
    materialComposition:
      Aluminum: 50
      Plastic: 50
      Circuitry: 25
  - type: StaticPrice
    price: 56
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 11
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  name: "конфігуратор мережі"
  parent: BaseItem
  id: NetworkConfigurator
  description: "Інструмент для зв'язування пристроїв між собою. Має два режими: режим списку для масового зв'язування пристроїв і режим зв'язування для розширеного зв'язування пристроїв."
  components:
    - type: EmitSoundOnLand
      sound:
        path: /Audio/Items/multitool_drop.ogg
    - type: Sprite
      sprite: Objects/Tools/network_configurator.rsi
      layers:
        - state: icon
        - state: mode-link
          map: ["enum.NetworkConfiguratorLayers.ModeLight"]
          shader: unshaded
    - type: Item
      size: Small
    - type: Clothing
      sprite: Objects/Tools/network_configurator.rsi
      quickEquip: false
      slots:
        - Belt
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.NetworkConfiguratorVisuals.Mode:
          enum.NetworkConfiguratorLayers.ModeLight:
            True: { state: mode-link }
            False: { state: mode-list }
    - type: NetworkConfigurator
    - type: ActivatableUI
      key: enum.NetworkConfiguratorUiKey.List
      inHandsOnly: true
    - type: Tag
      tags:
        - DoorElectronicsConfigurator
    - type: UserInterface
      interfaces:
        enum.NetworkConfiguratorUiKey.List:
          type: NetworkConfiguratorBoundUserInterface
        enum.NetworkConfiguratorUiKey.Configure:
          type: NetworkConfiguratorBoundUserInterface
        enum.NetworkConfiguratorUiKey.Link:
          type: NetworkConfiguratorBoundUserInterface
    - type: StaticPrice
      price: 56
    - type: GuideHelp
      guides:
        - NetworkConfigurator
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.25,-0.25,0.25,0.25"
          density: 12
          mask:
          - ItemMask
          restitution: 0.3
          friction: 0.2

#Power tools
#Later on these should switch probably switch damage when changing the tool behavior.
- type: entity
  name: "електродриль"
  parent: BaseItem
  id: PowerDrill
  description: "Проста ручна дриль з електроприводом."
  components:
  - type: Tag
    tags:
    - Powerdrill
  - type: Sprite
    sprite: Objects/Tools/drill.rsi
    state: drill_screw
  - type: Item
    sprite: Objects/Tools/drill.rsi
    size: Small
  - type: Tool
    qualities:
      - Screwing
    speedModifier: 1.5
    useSound: /Audio/Items/drill_use.ogg
  - type: MultipleTool
    statusShowBehavior: true
    entries:
      - behavior: Screwing
        sprite:
          sprite: Objects/Tools/drill.rsi
          state: drill_screw
        useSound:
          path: /Audio/Items/drill_use.ogg
        changeSound:
          path: /Audio/Items/change_drill.ogg
      - behavior: Anchoring
        sprite:
          sprite: Objects/Tools/drill.rsi
          state: drill_bolt
        useSound:
          path: /Audio/Items/drill_use.ogg
        changeSound:
          path: /Audio/Items/change_drill.ogg
  - type: PhysicalComposition
    materialComposition:
      Steel: 300
      Plastic: 50
      Circuitry: 50
#  - type: DynamicPrice
#    price: 100
  - type: StaticPrice
    price: 100
  - type: MeleeWeapon
    wideAnimationRotation: -90
    attackRate: 1.1
    range: 1.4
    damage:
      types:
        Piercing: 8
    heavyRateModifier: 1.1
    heavyDamageBaseModifier: 1.2
    maxTargets: 1
    angle: 20
    soundHit:
      path: "/Audio/Items/drill_hit.ogg"
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 8
    staminaCost: 7.5
    soundHit:
      collection: MetalThud
  - type: ReverseEngineering # Nyano
    difficulty: 2
    recipes:
      - PowerDrill
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 45
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  id: RCD
  parent: BaseItem
  name: "УЗО"
  description: "Пристрій для швидкого будівництва можна використовувати для швидкого встановлення та демонтажу різних станційних конструкцій і пристосувань. Для роботи потрібна стиснута речовина."
  components:
  - type: RCD
    availablePrototypes:
    - WallSolid
    - FloorSteel
    - Plating
    - Catwalk
    - Grille
    - Window
    - WindowDirectional
    - WindowReinforcedDirectional
    - ReinforcedWindow
    - Airlock
    - AirlockGlass
    - Firelock
    - TubeLight
    - BulbLight
    - LVCable
    - MVCable
    - HVCable
    - CableTerminal
    - Deconstruct
  - type: LimitedCharges
    maxCharges: 30
    charges: 30
  - type: UseDelay
  - type: Sprite
    sprite: Objects/Tools/rcd.rsi
    state: icon
  - type: Item
    size: Normal
  - type: Clothing
    sprite: Objects/Tools/rcd.rsi
    quickEquip: false
    slots:
    - Belt
  - type: PhysicalComposition
    materialComposition:
      Steel: 400
      Plastic: 100
      Circuitry: 50
  - type: StaticPrice
    price: 100
  - type: UserInterface
    interfaces:
      enum.RcdUiKey.Key:
        type: RCDMenuBoundUserInterface
  - type: ActivatableUI
    key: enum.RcdUiKey.Key
  - type: ReverseEngineering # Nyano
    difficulty: 3
    recipes:
      - RCD
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 65
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  id: RPD
  parent: RCD
  name: "РПД"
  description: "Пристрій, що використовується для швидкого прокладання труб."
  components:
  - type: Tag
    tags:
      - RPD
  - type: RCD
    isRpd: true
    availablePrototypes:
    - PipeFourway
    - PipeStraight
    - PipeBend
    - PipeTJunction
    - OutletInjector
    - ManualValve
    - VolumetricPump
    - PressurePump
    - VentScrubber
    - PressureValve
    - DualPortVent
    - VentGas
    - VentPassive
    - MixerGas
    - Radiator
    - SignalValve
    - CanisterPort
    - FilterGas
    - Deconstruct
  - type: LimitedCharges
    maxCharges: 50
    charges: 50
  - type: Sprite
    sprite: Objects/Tools/rpd.rsi
  - type: PhysicalComposition
    materialComposition:
      Steel: 500
      Plastic: 150
      Circuitry: 50
  - type: StaticPrice
    price: 100
  - type: ReverseEngineering # Nyano
    difficulty: 3
    recipes:
      - RPD
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 65
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  id: RCDEmpty
  parent: RCD
  suffix: Empty
  components:
  - type: LimitedCharges
    charges: 0
  - type: RCD
    availablePrototypes:
    - WallSolid
    - FloorSteel
    - Plating
    - Catwalk
    - Grille
    - Window
    - WindowDirectional
    - WindowReinforcedDirectional
    - ReinforcedWindow
    - Airlock
    - AirlockGlass
    - Firelock

- type: entity
  id: RPDEmpty
  parent: RPD
  suffix: Empty
  components:
  - type: LimitedCharges
    charges: 0
  - type: Tag
    tags:
      - RPD
  - type: RCD
    isRpd: true
    availablePrototypes:
    - PipeFourway
    - PipeStraight
    - PipeBend
    - PipeTJunction
    - OutletInjector
    - ManualValve
    - VolumetricPump
    - PressurePump
    - VentScrubber
    - PressureValve
    - DualPortVent
    - VentGas
    - VentPassive
    - MixerGas
    - Radiator
    - SignalValve
    - CanisterPort
    - FilterGas
    - Deconstruct

- type: entity
  id: RCDRecharging
  parent: RCD
  name: "експериментальний УЗО"
  description: "Пристрій для швидкого будівництва, що пасивно генерує власну стиснуту матерію, на основі блюзового простору."
  suffix: AutoRecharge
  components:
  - type: LimitedCharges
    maxCharges: 20
    charges: 20
  - type: AutoRecharge
    rechargeDuration: 10

- type: entity
  id: RCDExperimental
  parent: RCD
  suffix: Admeme
  name: "експериментальний УЗО"
  description: "Пристрій для швидкого будівництва, що пасивно генерує власну стиснуту матерію, на основі блюзового простору."
  components:
  - type: AutoRecharge
    rechargeDuration: 1

- type: entity
  id: RPDRecharging
  parent: RPD
  name: "експериментальний RPD"
  description: "Пристрій швидкого прокладання труб, вдосконалений блюспейсом, який пасивно генерує власну стиснену матерію."
  suffix: AutoRecharge
  components:
  - type: LimitedCharges
    maxCharges: 25
    charges: 25
  - type: AutoRecharge
    rechargeDuration: 10

- type: entity
  id: RPDExperimental
  parent: RPD
  suffix: Admeme
  name: "експериментальний RPD"
  description: "Пристрій швидкого прокладання труб, вдосконалений блюспейсом, який пасивно генерує власну стиснену матерію."
  components:
  - type: AutoRecharge
    rechargeDuration: 1

- type: entity
  name: "стиснута речовина"
  parent: BaseItem
  id: RCDAmmo
  description: "Картридж із сировиною, спресованою за технологією bluespace. Використовується в пристроях швидкого будівництва."
  components:
  - type: RCDAmmo
  - type: Sprite
    sprite: Objects/Tools/rcd.rsi
    state: ammo
  - type: Item
    sprite: Objects/Tools/rcd.rsi
    heldPrefix: ammo
  - type: PhysicalComposition
    materialComposition:
      Steel: 300
      Plastic: 100
  - type: StaticPrice
    price: 60
#  - type: DynamicPrice
#    price: 60
  - type: ReverseEngineering # Nyano
    difficulty: 3
    recipes:
      - RCDAmmo

- type: entity
  name: "omnitool"
  parent: BaseItem
  id: Omnitool
  description: "Найкращий друг дрона."
  components:
  - type: Sprite
    sprite: Objects/Tools/omnitool.rsi
    state: omnitool-screwing
  - type: Item
    sprite: Objects/Tools/omnitool.rsi
    size: Normal
  - type: Tag
    tags:
    - Multitool
  - type: Prying
    enabled: false
  - type: Tool
    qualities:
      - Screwing
    speedModifier: 1.2 # Kept for future adjustments. Currently 1.2x for balance
    useSound: /Audio/Items/drill_use.ogg
  - type: ToolTileCompatible
  - type: MultipleTool
    statusShowBehavior: true
    entries:
      - behavior: Screwing
        sprite:
          sprite: Objects/Tools/omnitool.rsi
          state: omnitool-screwing
        useSound:
          path: /Audio/Items/drill_use.ogg
        changeSound:
          path: /Audio/Items/change_drill.ogg
      - behavior: Prying
        sprite:
          sprite: Objects/Tools/omnitool.rsi
          state: omnitool-prying
        useSound:
          path: /Audio/Items/jaws_pry.ogg
        changeSound:
          path: /Audio/Items/change_drill.ogg
      - behavior: Anchoring
        sprite:
          sprite: Objects/Tools/omnitool.rsi
          state: omnitool-wrenching
        useSound:
          path: /Audio/Items/ratchet.ogg
        changeSound:
          path: /Audio/Items/change_drill.ogg
      - behavior: Cutting
        sprite:
          sprite: Objects/Tools/omnitool.rsi
          state: omnitool-snipping
        useSound:
          path: /Audio/Items/jaws_cut.ogg
        changeSound:
          path: /Audio/Items/change_drill.ogg
      - behavior: Pulsing
        sprite:
          sprite: Objects/Tools/omnitool.rsi
          state: omnitool-pulsing
        changeSound:
          path: /Audio/Items/change_drill.ogg

#Other
- type: entity
  name: "лопата"
  parent: BaseItem
  id: Shovel
  description: "Великий інструмент для копання та переміщення ґрунту."
  components:
  - type: Sprite
    sprite: Objects/Tools/shovel.rsi
    state: icon
  - type: MeleeWeapon
    wideAnimationRotation: 45
    attackRate: 1.25
    range: 1.75
    damage:
      types:
        Blunt: 8
    bluntStaminaDamageFactor: 2
    heavyRateModifier: 1.75
    heavyDamageBaseModifier: 1.5
    heavyStaminaCost: 2.5
    angle: 120
    soundHit:
      collection: MetalThud
  - type: DamageOtherOnHit
    staminaCost: 8.5
  - type: ThrowingAngle
    angle: 45
  - type: Item
    size: Normal
    sprite: Objects/Tools/shovel.rsi
  - type: PhysicalComposition
    materialComposition:
      Steel: 100
      Wood: 50
  - type: StaticPrice
    price: 25
  # Delta V: Adds tool quality for digging
  - type: Tool
    qualities:
      - Digging
    useSound:
      path: /Audio/Nyanotrasen/Items/shovel_dig.ogg
  - type: EarthDigging
  - type: Shovel

- type: entity
  parent: BaseItem
  id: RollingPin
  name: "качалка"
  description: "Інструмент, який використовується для формування та розрівнювання тіста."
  components:
  - type: Sprite
    sprite: Objects/Tools/rolling_pin.rsi
    state: icon
  - type: Item
    sprite: Objects/Tools/rolling_pin.rsi
    size: Small
  - type: Clothing
    sprite: Objects/Tools/rolling_pin.rsi
    quickEquip: false
    slots:
    - Belt
  - type: MeleeWeapon
    wideAnimationRotation: -135
    attackRate: 1.1
    damage:
      types:
        Blunt: 7
    bluntStaminaDamageFactor: 2.0
    heavyRateModifier: 2.5
    heavyDamageBaseModifier: 2
    maxTargets: 2
    angle: 20
    soundHit:
      collection: MetalThud
  - type: DamageOtherOnHit
  - type: Tool
    qualities:
    - Rolling
  - type: PhysicalComposition
    materialComposition:
      Wood: 100
  - type: Tag
    tags:
    - RollingPin
