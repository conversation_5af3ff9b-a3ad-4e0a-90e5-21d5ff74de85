## General stuff

ui-options-title = Параметри Гри
ui-options-tab-graphics = Графіка
ui-options-tab-controls = Керування
ui-options-tab-audio = Аудіо
ui-options-tab-network = Мережа
ui-options-tab-misc = Загальне

ui-options-apply = Застосувати
ui-options-reset-all = Скинути все
ui-options-default = За замовчуванням

# Misc/General menu

ui-options-discordrich = Увімкнути Discord Rich Presence
ui-options-general-ui-style = Стиль UI
ui-options-general-discord = Discord
ui-options-general-cursor = Курсор
ui-options-general-speech = Розмови
ui-options-general-storage = Сховище
ui-options-general-accessibility = Доступність

## Audio menu

ui-options-master-volume = Основна гучність:
ui-options-midi-volume = MIDI (Інструменти) Гучність:
ui-options-ambient-music-volume = Гучність атмосферної музики:
ui-options-ambience-volume = Гучність оточення:
ui-options-lobby-volume = Гучність лобі та закінчення раунду:
ui-options-interface-volume = Гучність інтерфейсу:
ui-options-ambience-max-sounds = К-сть одночасних звуків оточення:
ui-options-lobby-music = Музика лобі та закінчення раунду
ui-options-restart-sounds = Звуки Перезапуску Раунду
ui-options-event-music = Музика Івентів
ui-options-admin-sounds = Звук Звернень Адміністраторів
ui-options-volume-label = Гучність
ui-options-volume-percent = { TOSTRING($volume, "P0") }

## Графічне меню

ui-options-show-held-item = Показувати утримуваний елемент поруч з курсором?
ui-options-show-combat-mode-indicators = Показувати індикатори бойового режиму при наведенні курсору?
ui-options-opaque-storage-window = Увімкнути непрозоре вікно зберігання
ui-options-show-looc-on-head = Показувати чат LOOC над головою персонажа?
ui-options-fancy-speech = Показувати імена в спливаючій підказці?
ui-options-fancy-name-background = Додати фон до назв спливаючої підказки?
ui-options-enable-color-name = Додати кольори до імен персонажів
ui-options-colorblind-friendly = Дружній режим для дальтоніків
ui-options-reduced-motion = Зменшити рух візуальних ефектів
ui-options-screen-shake-intensity = Інтенсивність тремтіння екрана
ui-options-screen-shake-percent = { TOSTRING($intensity, "P0") }
ui-options-vsync = Вертикальна синхронізація
ui-options-fullscreen = Повноекранний
ui-options-lighting-label = Якість освітлення:
ui-options-lighting-very-low = Дуже Низька
ui-options-lighting-low = Низька
ui-options-lighting-medium = Середня
ui-options-lighting-high = Висока
ui-options-scale-label = Масштаб UI:
ui-options-scale-auto = Автоматичний ({ TOSTRING($scale, "P0") })
ui-options-scale-75 = 75%
ui-options-scale-100 = 100%
ui-options-scale-125 = 125%
ui-options-scale-150 = 150%
ui-options-scale-175 = 175%
ui-options-scale-200 = 200%
ui-options-hud-theme = Тема HUD:
ui-options-hud-theme-default = За замовчуванням
ui-options-hud-theme-plasmafire = Плазмовий Вогонь
ui-options-hud-theme-slimecore = Слаймкор
ui-options-hud-theme-clockwork = Заводний механізм
ui-options-hud-theme-retro = Ретро
ui-options-hud-theme-minimalist = Мінімалістичний
ui-options-hud-theme-ashen = Попеляста
ui-options-vp-stretch = Розтягнути зображення для відповідності вікну гри
ui-options-vp-scale = Фіксований масштаб вікна: x{ $scale }
ui-options-vp-integer-scaling = Використовувати цілочисельне масштабування (може спричинити появу чорних смуг/обрізання)
ui-options-vp-integer-scaling-tooltip = Якщо ця опція увімкнена, вікно перегляду буде масштабуватися,
                                        використовуючи ціле значення за певної роздільної здатності.
                                        Хоча це і призводить до чітких текстур, це також часто означає,
                                        що чорні полоси будуть у верхній/нижній частині екрана або
                                        що частина вікна не буде видна.
ui-options-vp-low-res = Зображення з низькою роздільною здатністю
ui-options-parallax-low-quality = Паралакс Низької Якості (фон)
ui-options-fps-counter = Показати лічильник FPS
ui-options-vp-width = Ширина вікна: { $width }
ui-options-hud-layout = Схема HUD:

## Controls menu

ui-options-binds-reset-all = Скинути ВСІ сполучення клавіш
ui-options-binds-explanation = Нажміть, щоб змінити кнопку, ПКМ - щоб очистити
ui-options-unbound = Вільний
ui-options-bind-reset = Скинути
ui-options-key-prompt = Натисніть клавішу...

ui-options-header-movement = Переміщення
ui-options-header-camera = Камера
ui-options-header-interaction-basic = Базова Взаємодія
ui-options-header-interaction-adv = Розширена Взаємодія
ui-options-header-ui = Інтерфейс Користувача
ui-options-header-misc = Різне
ui-options-header-hotbar = Гаряча Панель
ui-options-header-shuttle = Шатл
ui-options-header-map-editor = Редактор Карт
ui-options-header-dev = Розробка
ui-options-header-general = Загальні

ui-options-hotkey-keymap = Використовувати клавіші QWERTY (США)
ui-options-hotkey-toggle-walk = Перемкнути Ходьбу

ui-options-function-move-up = Рухатися Вгору
ui-options-function-move-left = Рухатися Ліворуч
ui-options-function-move-down = Рухатися Вниз
ui-options-function-move-right = Рухатися Праворуч
ui-options-function-walk = Ходьба

ui-options-function-camera-rotate-left = Повернути Ліворуч
ui-options-function-camera-rotate-right = Повернути Праворуч
ui-options-function-camera-reset = Скинути
ui-options-function-zoom-in = Збільшити Масштаб
ui-options-function-zoom-out = Зменшити Масштаб
ui-options-function-reset-zoom = Скинути Масштаб

ui-options-function-use = Використовувати
ui-options-function-use-secondary = Використовувати (вторинний)
ui-options-function-alt-use = Альтернативне використання
ui-options-function-wide-attack = Розмашиста атака
ui-options-function-activate-item-in-hand = Використовувати предмет у руці
ui-options-function-alt-activate-item-in-hand = Альтернативне використання предмета в руці
ui-options-function-activate-item-in-world = Використовувати предмет у світі
ui-options-function-alt-activate-item-in-world = Альтернативно використовувати предмет у світі
ui-options-function-drop = Покласти предмет
ui-options-function-examine-entity = Оглянути
ui-options-function-swap-hands = Поміняти руку
ui-options-function-move-stored-item = Перемістити збережений елемент
ui-options-function-rotate-stored-item = Обернути збережений елемент
ui-options-static-storage-ui = Інтерфейс статичного сховища

ui-options-function-smart-equip-backpack = Розумне екіпірування в рюкзак
ui-options-function-smart-equip-belt = Розумне екіпірування на пояс
ui-options-function-open-backpack = Відкрити наплічник
ui-options-function-open-belt = Відкрити пасок
ui-options-function-throw-item-in-hand = Кинути предмет
ui-options-function-try-pull-object = Тягнути предмет
ui-options-function-move-pulled-object = Тягнути предмет убік
ui-options-function-release-pulled-object = Припинити тягнути предмет
ui-options-function-point = Вказати на щось

ui-options-function-focus-chat-input-window = Писати в чат
ui-options-function-focus-local-chat-window = Писати в чат (IC)
ui-options-function-focus-emote = Писати в чат (Емоції)
ui-options-function-focus-whisper-chat-window = Писати в чат (Шепіт)
ui-options-function-focus-radio-window = Писати в чат (Радіо)
ui-options-function-focus-looc-window = Писати в чат (LOOC)
ui-options-function-focus-ooc-window = Писати в чат (OOC)
ui-options-function-focus-admin-chat-window = Писати в чат (Адмін)
ui-options-function-focus-dead-chat-window = Писати в чат (Мертві)
ui-options-function-focus-console-chat-window = Писати в чат (Консоль)
ui-options-function-cycle-chat-channel-forward = Переключити канал (Вперед)
ui-options-function-cycle-chat-channel-backward = Переключити канал (Назад)
ui-options-function-open-character-menu = Відкрити меню персонажа
ui-options-function-open-context-menu = Відкрити контекстне меню
ui-options-function-open-crafting-menu = Відкрити меню крафта
ui-options-function-open-inventory-menu = Відкрити інвентар
ui-options-function-open-a-help = Відкрити адмін допомогу
ui-options-function-open-abilities-menu = Відкрити меню дій
ui-options-function-open-entity-spawn-window = Відкрити меню створення сутності
ui-options-function-open-sandbox-window = Відкрити меню пісочниці
ui-options-function-open-tile-spawn-window = Відкрити меню створення тайлів
ui-options-function-open-decal-spawn-window = Відкрити меню створення декалів
ui-options-function-open-admin-menu = Відкрити меню адміністратора
ui-options-function-open-guidebook = Відкрити путівник
ui-options-function-window-close-all = Закрити всі вікна
ui-options-function-window-close-recent = Закрити поточне вікно
ui-options-function-show-escape-menu = Перемкнути меню гри
ui-options-function-escape-context = Закрити останнє вікно або перемкнути меню гри

ui-options-function-take-screenshot = Зробити знімок екрану
ui-options-function-take-screenshot-no-ui = Зробити знімок екрана (без інтерфейсу)
ui-options-function-toggle-fullscreen = Перемкнути повноекранний режим

ui-options-function-editor-place-object = Розмістити об'єкт
ui-options-function-editor-cancel-place = Скасувати розміщення
ui-options-function-editor-grid-place = Розмістити в сітці
ui-options-function-editor-line-place = Розмістити в лінію
ui-options-function-editor-rotate-object = Повернути
ui-options-function-editor-flip-object = Розвернути
ui-options-function-editor-copy-object = Копіювати

ui-options-function-show-debug-console = Відкрити консоль
ui-options-function-show-debug-monitors = Показати дебаг-інформацію
ui-options-function-inspect-entity = Оглянути сутність
ui-options-function-hide-ui = Приховати UI

ui-options-function-hotbar1 = 1 слот hotbar
ui-options-function-hotbar2 = 2 слот hotbar
ui-options-function-hotbar3 = 3 слот hotbar
ui-options-function-hotbar4 = 4 слот hotbar
ui-options-function-hotbar5 = 5 слот hotbar
ui-options-function-hotbar6 = 6 слот hotbar
ui-options-function-hotbar7 = 7 слот hotbar
ui-options-function-hotbar8 = 8 слот hotbar
ui-options-function-hotbar9 = 9 слот hotbar
ui-options-function-hotbar0 = 0 слот hotbar
ui-options-function-loadout1 = 1 сторінка hotbar
ui-options-function-loadout2 = 2 сторінка hotbar
ui-options-function-loadout3 = 3 сторінка hotbar
ui-options-function-loadout4 = 4 сторінка hotbar
ui-options-function-loadout5 = 5 сторінка hotbar
ui-options-function-loadout6 = 6 сторінка hotbar
ui-options-function-loadout7 = 7 сторінка hotbar
ui-options-function-loadout8 = 8 сторінка hotbar
ui-options-function-loadout9 = 9 сторінка hotbar
ui-options-function-loadout0 = 0 сторінка hotbar

ui-options-function-shuttle-strafe-up = Стрейф вгору
ui-options-function-shuttle-strafe-right = Стрейф вправо
ui-options-function-shuttle-strafe-left = Стрейф вліво
ui-options-function-shuttle-strafe-down = Стрейф вниз
ui-options-function-shuttle-rotate-left = Поворот ліворуч
ui-options-function-shuttle-rotate-right = Повернути праворуч
ui-options-function-shuttle-brake = Гальмування

## Network menu

ui-options-net-predict = Припущення на стороні клієнта

ui-options-net-interp-ratio = Мережеве згладжування
ui-options-net-interp-ratio-tooltip = Збільшення цього параметра, як правило, зробить гру більш стійкою
                                      до втрати пакетів сервер->клієнт, проте при цьому це так само
                                      додає трохи більшу затримку та вимагає від клієнта передбачати
                                      більше майбутніх тактів.

ui-options-net-predict-tick-bias = Похибка тиків вгадування
ui-options-net-predict-tick-bias-tooltip = Збільшення цього параметра, як правило, зробить гру більш стійкою
                                           до втрати пакетів клієнт->сервер, проте при цьому трохи зростає
                                           затримка і клієнту потрібно передбачати більше майбутніх тактів.

ui-options-net-pvs-spawn = Ліміт появи PVS сутностей
ui-options-net-pvs-spawn-tooltip = Обмежує частоту, з якою сервер надсилатиме щойно створені сутності
                                   клієнту. Зниження цього параметра може допомогти зменшити "заїкання"
                                   викликані створенням сутності, але може призвести до їх різкої появи.

ui-options-net-pvs-entry = Ліміт PVS сутностей
ui-options-net-pvs-entry-tooltip = Обмежує частоту, з якою сервер надсилатиме нові видимі сутності
                                   клієнту. Зниження цього параметра може допомогти зменшити "заїкання"
                                   викликані створенням сутності, але може призвести до їх різкої появи.

ui-options-net-pvs-leave = Частота видалення PVS
ui-options-net-pvs-leave-tooltip = Обмежує частоту, з якою клієнт видалятиме об’єкти, які не видно.
                                   Зниження цього параметра може допомогти зменшити заїкання під час ходьби,
                                   але іноді може призвести до неправильних передбачень та інших проблем.

## Toggle window console command
cmd-options-desc = Відкриває меню опцій, опціонально з конкретно обраною вкладкою
cmd-options-help = Використання: options [tab]

ui-options-show-offer-mode-indicators = Показувати індикатори режиму пропозиції курсором
ui-options-function-offer-item = Запропонуйте щось
ui-options-announcer-volume = Гучність диктора:
ui-options-function-toggle-standing = Перемикач стояння
ui-options-show-ooc-patron-color = Показати колір OOC Patreon
ui-options-chat-window-opacity = Непрозорість вікна чату
ui-options-chat-window-opacity-percent = { TOSTRING($opacity, "P0") }
ui-options-function-save-item-location = Зберегти розташування елемента
ui-options-announcer-disable-multiple-sounds = Вимкнути звуки диктора, що накладаються один на одного
ui-options-announcer-disable-multiple-sounds-tooltip = Деякі оголошення будуть звучати неправильно, це налаштування не рекомендується

ui-options-hotkey-default-walk = Ходити за замовчуванням
ui-options-no-filters = Вимкнути расові візуальні фільтри
ui-options-function-look-up = Подивіться вгору/Цільтеся
ui-options-function-auto-get-up = Автоматичне вставання після падіння
ui-options-function-hold-look-up = Утримуйте клавішу, щоб прицілитися

ui-options-vp-vertical-fit = Вертикальне кріплення оглядового вікна
ui-options-vp-vertical-fit-tooltip = Якщо увімкнено, головне вікно перегляду повністю ігноруватиме горизонтальну вісь під час
                                     під час підгонки під ваш екран. Якщо ваш екран менший за область перегляду, це
                                     призведе до обрізання області перегляду по горизонтальній осі.
ui-options-function-toggle-crawling-under = Перемикач повзання під меблями
ui-options-function-toggle-round-end-summary-window = Перемикання круглого кінцевого вікна підсумків
ui-options-header-targeting = Прицілювання
ui-options-function-target-head = Ціль - голова
ui-options-function-target-torso = Ціль - торс
ui-options-function-target-left-arm = Ціль - ліва рука
ui-options-function-target-right-arm = Ціль - права рука
ui-options-function-target-left-leg = Ціль - ліва нога
ui-options-function-target-right-leg = Ціль - права нога

ui-options-chatstack = Автоматично об'єднувати однакові повідомлення чату
ui-options-chatstack-off = Вимкнено
ui-options-chatstack-single = Тільки останнє повідомлення
ui-options-chatstack-double = Останні два повідомлення
ui-options-chatstack-triple = Останні три повідомлення
ui-options-function-mapping-enable-pick = Виберіть об'єкт/плитку
ui-options-function-mapping-enable-decal-pick = Виберіть наклейку
ui-options-function-mapping-enable-delete = Видалити об'єкт
ui-options-general-other = Інше
ui-options-modern-progress-bar = Сучасний індикатор виконання
ui-options-mood-visual-effects = Увімкніть візуальні ефекти системи настрою

ui-options-function-smart-equip-back = Автоматично екіпірувати в слот спини
ui-options-binds-search = Пошук
ui-options-log-in-chat = Записувати спливаючі вікна в чат