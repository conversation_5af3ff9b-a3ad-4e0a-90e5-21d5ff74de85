﻿- type: entity
  name: "газоаналізатор"
  parent: BaseItem
  id: GasAnalyzer
  description: "Ручний екологічний сканер, який повідомляє про поточний рівень загазованості."
  components:
  - type: Sprite
    sprite: Objects/Specific/Atmos/gasanalyzer.rsi
    layers:
      - state: icon
        map: ["analyzer"]
  - type: GasAnalyzer
  - type: ActivatableUI
    inHandsOnly: true
    singleUser: true
    requireActiveHand: false
    key: enum.GasAnalyzerUiKey.Key
  - type: UserInterface
    interfaces:
      enum.GasAnalyzerUiKey.Key:
        type: GasAnalyzerBoundUserInterface
  - type: Appearance
  - type: GenericVisualizer
    visuals:
        enum.GasAnalyzerVisuals.Enabled:
          enabled:
            True: { state: working }
            False: { state: icon }
  - type: StaticPrice
    price: 80
  - type: PhysicalComposition
    materialComposition:
      Steel: 400
      Glass: 100
