- type: entity
  id: BaseMaterialDoor
  parent: BaseStructure
  name: "двері"
  abstract: true
  description: "Двері, куди вони ведуть?"
  components:
  - type: InteractionOutline
  - type: Sprite
    sprite: Structures/Doors/MineralDoors/metal_door.rsi
    layers:
    - state: closed
      map: ["enum.DoorVisualLayers.Base"]
  - type: AnimationPlayer
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.49,-0.49,0.49,0.49"
        density: 100
        mask:
        - FullTileMask
        layer:
        - AirlockLayer
  - type: Door
    bumpOpen: false
    clickOpen: true
    canCrush: false
    closeTimeOne: 0.2
    closeTimeTwo: 0.6
    openTimeOne: 0.6
    openTimeTwo: 0.2
    openingAnimationTime: 1.2
    closingAnimationTime: 1.2
    openSound:
      path: /Audio/Effects/stonedoor_openclose.ogg
    closeSound:
      path: /Audio/Effects/stonedoor_openclose.ogg
  - type: Appearance
  - type: Airtight
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: RCDDeconstructable
    cost: 6
    delay: 6
    fx: EffectRCDDeconstruct6
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Occluder
  - type: BlockWeather
  - type: InteractionVerbs
    allowedVerbs:
    - KnockOn

- type: entity
  parent: BaseMaterialDoor
  id: BaseMaterialDoorNavMap
  abstract: true
  components:
  - type: NavMapDoor

- type: entity
  id: MetalDoor
  name: "металеві двері"
  parent: BaseMaterialDoorNavMap
  components:
  - type: Construction
    graph: DoorGraph
    node: metalDoor

- type: entity
  id: WoodDoor
  name: "дерев'яні двері"
  parent: BaseMaterialDoorNavMap
  description: "Двері, куди вони ведуть?"
  components:
  - type: Sprite
    sprite: Structures/Doors/MineralDoors/wood_door.rsi
    layers:
    - state: closed
      map: ["enum.DoorVisualLayers.Base"]
  - type: Door
    bumpOpen: false
    clickOpen: true
    closeTimeOne: 0.2
    closeTimeTwo: 0.6
    openTimeOne: 0.6
    openTimeTwo: 0.2
    openSound:
      path: /Audio/Effects/door_open.ogg
    closeSound:
      path: /Audio/Effects/door_close.ogg
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Wood
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Construction
    graph: DoorGraph
    node: woodDoor

- type: entity
  id: PaperDoor
  name: "паперові двері"
  parent: BaseMaterialDoorNavMap
  description: "Двері, куди вони ведуть?"
  components:
  - type: Sprite
    sprite: Structures/Doors/MineralDoors/paper_door.rsi
    layers:
    - state: closed
      map: ["enum.DoorVisualLayers.Base"]
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Construction
    graph: DoorGraph
    node: paperDoor

- type: entity
  id: PlasmaDoor
  name: "плазмові двері"
  parent: BaseMaterialDoorNavMap
  description: "Двері, куди вони ведуть?"
  components:
  - type: Sprite
    sprite: Structures/Doors/MineralDoors/plasma_door.rsi
    layers:
    - state: closed
      map: ["enum.DoorVisualLayers.Base"]
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: StrongMetallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Construction
    graph: DoorGraph
    node: plasmaDoor

- type: entity
  id: GoldDoor
  name: "золоті двері"
  parent: BaseMaterialDoorNavMap
  description: "Двері, куди вони ведуть?"
  components:
  - type: Sprite
    sprite: Structures/Doors/MineralDoors/gold_door.rsi
    layers:
    - state: closed
      map: ["enum.DoorVisualLayers.Base"]
  - type: Construction
    graph: DoorGraph
    node: goldDoor

- type: entity
  id: SilverDoor
  name: "срібні двері"
  parent: BaseMaterialDoorNavMap
  description: "Двері, куди вони ведуть?"
  components:
  - type: Sprite
    sprite: Structures/Doors/MineralDoors/silver_door.rsi
    layers:
    - state: closed
      map: ["enum.DoorVisualLayers.Base"]
  - type: Construction
    graph: DoorGraph
    node: silverDoor

- type: entity
  id: BananiumDoor
  name: "бананіумні двері"
  parent: BaseMaterialDoorNavMap
  description: "Двері, куди вони ведуть?"
  components:
  - type: Sprite
    sprite: Structures/Doors/MineralDoors/bananium_door.rsi
    layers:
    - state: closed
      map: ["enum.DoorVisualLayers.Base"]
  - type: Construction
    graph: DoorGraph
    node: bananiumDoor
  - type: Door
    openSound:
      path: /Audio/Items/bikehorn.ogg
    closeSound:
      path: /Audio/Items/bikehorn.ogg

- type: entity
  id: WebDoor
  name: "павутинні двері"
  parent: BaseMaterialDoorNavMap
  description: "Ручні дверцята з павутиння, зазвичай розміщені безпосередньо перед ямою або пасткою."
  components:
  - type: Sprite
    sprite: Structures/Doors/web_door.rsi
    layers:
    - state: closed
      map: ["enum.DoorVisualLayers.Base"]
  - type: Construction
    graph: WebStructures
    node: door
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWebSilk:
            min: 3
            max: 5
  - type: Damageable
    damageModifierSet: Web
  - type: Door
    closeSound:
      path: /Audio/Effects/rustle1.ogg
    openSound:
      path: /Audio/Effects/rustle2.ogg
