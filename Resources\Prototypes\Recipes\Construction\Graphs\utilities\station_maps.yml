- type: constructionGraph
  id: StationMap
  start: start
  graph:
  - node: start
    edges:
    - to: assembly
      steps:
      - material: Steel
        amount: 2
        doAfter: 2.0

  - node: assembly
    entity: StationMapAssembly
    actions:
    - !type:AppearanceChange
    edges:
    - to: wired
      steps:
      - material: Cable
        amount: 2
        doAfter: 1
    - to: start
      completed:
      - !type:GivePrototype
        prototype: SheetSteel1
        amount: 2
      - !type:DeleteEntity {}
      steps:
      - tool: Welding
        doAfter: 2

  - node: wired
    entity: StationMapAssembly
    actions:
    - !type:AppearanceChange
    edges:
    - to: electronics
      steps:
      - tag: StationMapElectronics
        store: board
        name: "station map electronics"
        icon:
          sprite: "Objects/Misc/module.rsi"
          state: "door_electronics" # /tg/ uses the same sprite, right?
        doAfter: 1
    - to: assembly
      completed:
      - !type:GivePrototype
        prototype: CableApcStack1
        amount: 2
      steps:
      - tool: Cutting
        doAfter: 1

  - node: electronics
    actions:
    - !type:AppearanceChange
    edges:
    - to: station_map
      steps:
      - tool: Screwing
        doAfter: 2

  - node: station_map
    entity: StationMap
    edges:
    - to: wired
      conditions:
      - !type:ContainerNotEmpty
        container: board
      completed:
      - !type:EmptyAllContainers
        pickup: true
        emptyAtUser: true
      steps:
      - tool: Prying
        doAfter: 1
