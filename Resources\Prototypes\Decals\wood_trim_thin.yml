﻿- type: decal
  id: WoodTrimThin
  abstract: true

- type: decal
  id: WoodTrimThinBox
  parent: WoodTrimThin
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/wood_trim.rsi
    state: thin_box

- type: decal
  id: WoodTrimThinCornerNe
  parent: WoodTrimThin
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/wood_trim.rsi
    state: thin_corner_ne

- type: decal
  id: WoodTrimThinCornerSe
  parent: WoodTrimThin
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/wood_trim.rsi
    state: thin_corner_se

- type: decal
  id: WoodTrimThinCornerNw
  parent: WoodTrimThin
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/wood_trim.rsi
    state: thin_corner_nw

- type: decal
  id: WoodTrimThinCornerSw
  parent: WoodTrimThin
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/wood_trim.rsi
    state: thin_corner_sw

- type: decal
  id: WoodTrimThinInnerNe
  parent: WoodTrimThin
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/wood_trim.rsi
    state: thin_inner_ne

- type: decal
  id: WoodTrimThinInnerSe
  parent: WoodTrimThin
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/wood_trim.rsi
    state: thin_inner_se

- type: decal
  id: WoodTrimThinInnerNw
  parent: WoodTrimThin
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/wood_trim.rsi
    state: thin_inner_nw

- type: decal
  id: WoodTrimThinInnerSw
  parent: WoodTrimThin
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/wood_trim.rsi
    state: thin_inner_sw

- type: decal
  id: WoodTrimThinEndN
  parent: WoodTrimThin
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/wood_trim.rsi
    state: thin_end_n

- type: decal
  id: WoodTrimThinEndE
  parent: WoodTrimThin
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/wood_trim.rsi
    state: thin_end_e

- type: decal
  id: WoodTrimThinEndS
  parent: WoodTrimThin
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/wood_trim.rsi
    state: thin_end_s

- type: decal
  id: WoodTrimThinEndW
  parent: WoodTrimThin
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/wood_trim.rsi
    state: thin_end_w

- type: decal
  id: WoodTrimThinLineN
  parent: WoodTrimThin
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/wood_trim.rsi
    state: thin_line_n

- type: decal
  id: WoodTrimThinLineE
  parent: WoodTrimThin
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/wood_trim.rsi
    state: thin_line_e

- type: decal
  id: WoodTrimThinLineS
  parent: WoodTrimThin
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/wood_trim.rsi
    state: thin_line_s

- type: decal
  id: WoodTrimThinLineW
  parent: WoodTrimThin
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/wood_trim.rsi
    state: thin_line_w

