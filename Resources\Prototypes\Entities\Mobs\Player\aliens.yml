﻿- type: entity
  name: "Чужа личинка"
  parent: SimpleSpaceMobBase
  id: MobAlienLarva
  description: "Маленький і нешкідливий прибулець"
  components:
    - type: ShowInfectedIcons
    - type: Body
      prototype: Xeno
    - type: GhostRole
      makeSentient: true
      allowSpeech: true
      allowMovement: true
      name: ghost-role-information-alien-larva-name
      description: ghost-role-information-alien-larva-description
    - type: GhostTakeoverAvailable
    - type: Speech
      speechSounds: Alien
      speechVerb: SmallMob
    - type: Vocal
      sounds:
        Male: UnisexChitinid
        Female: UnisexChitinid
        Unsexed: UnisexChitinid
    - type: LizardAccent
    - type: Sprite
      drawdepth: SmallMobs
      sprite: Mobs/Aliens/Xenos/alien.rsi
      layers:
        - map: ["enum.DamageStateVisualLayers.Base", "movement"]
          state: larva0
    - type: SpriteMovement
      movementLayers:
        movement:
          state: larva0
      noMovementLayers:
        movement:
          state: larva0
    - type: NpcFactionMember
      factions:
        - Xeno
    - type: HTN
      rootTask:
        task: XenoCompound
    - type: Physics
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeCircle
            radius: 0.2
          density: 100
          mask:
            - SmallMobMask
          layer:
            - SmallMobLayer
    - type: MobState
    - type: Deathgasp
    - type: MobStateActions
      actions:
        Critical:
          - ActionCritSuccumb
          - ActionCritFakeDeath
          - ActionCritLastWords
    - type: MobThresholds
      thresholds:
        0: Alive
        25: Dead
      stateAlertDict:
        Alive: AlienHealth
        Critical: AlienCrit
        Dead: AlienDead
    - type: MovementSpeedModifier
      baseWalkSpeed : 5
      baseSprintSpeed : 5
    - type: DamageStateVisuals
      states:
        Alive:
          Base: larva0
        Critical:
          Base: larva0_stun
        Dead:
          Base: larva0_dead
    - type: Tag
      tags:
        - Trash
        - VimPilot
        - Meat
    - type: CombatMode
      combatToggleAction: ActionCombatModeToggleAlien
    - type: MeleeWeapon
      soundHit:
        path: /Audio/Weapons/Xeno/alien_claw_flesh1.ogg
      angle: 0
      animation: WeaponArcBite
      damage:
        types:
          Blunt: 2
    - type: Bloodstream
      bloodMaxVolume: 50
      bloodReagent: FluorosulfuricAcid
    - type: MobPrice
      price: 50
    - type: BadFood
    - type: NonSpreaderZombie
    - type: PassiveDamage
      allowedStates:
      - Alive
      - Critical
      - Dead
    - type: Alien
      caste: larva
      weedHeal:
        groups:
          Burn: -5
          Toxin: -5
          Airloss: -10
          Brute: -5
    - type: PlasmaVessel
      maxPlasma: 30
    - type: TimedPolymorph
      polymorphPrototype: AlienEvolutionGrowStageTwo
      polymorphTime: 50


- type: entity
  parent: MobAlienLarva # Larva that is inside of it's owner
  id: MobAlienLarvaInside
  components:
  - type: TimedPolymorph
    enabled: false
  - type: InsideAlienLarva
    evolutionCooldown: 5
  - type: GhostRole
    makeSentient: true
    allowSpeech: true
    allowMovement: true
    name: ghost-role-information-alien-larva-name
    description: ghost-role-information-alien-larva-inside-description

- type: entity
  parent: MobAlienLarva
  id: MobAlienLarvaGrowStageTwo
  components:
    - type: Sprite
      drawdepth: SmallMobs
      sprite: Mobs/Aliens/Xenos/alien.rsi
      layers:
        - map: [ "enum.DamageStateVisualLayers.Base", "movement" ]
          state: larva1
    - type: SpriteMovement
      movementLayers:
        movement:
          state: larva1
      noMovementLayers:
        movement:
          state: larva1
    - type: DamageStateVisuals
      states:
        Alive:
          Base: larva1
        Critical:
          Base: larva1_stun
        Dead:
          Base: larva1_dead
    - type: TimedPolymorph
      polymorphPrototype: AlienEvolutionGrowStageThree
      polymorphTime: 50

- type: entity
  parent: MobAlienLarva
  id: MobAlienLarvaGrowStageThree
  components:
    - type: Sprite
      drawdepth: SmallMobs
      sprite: Mobs/Aliens/Xenos/alien.rsi
      layers:
        - map: [ "enum.DamageStateVisualLayers.Base", "movement" ]
          state: larva2
    - type: SpriteMovement
      movementLayers:
        movement:
          state: larva2
      noMovementLayers:
        movement:
          state: larva2
    - type: DamageStateVisuals
      states:
        Alive:
          Base: larva2
        Critical:
          Base: larva2_stun
        Dead:
          Base: larva2_dead
    - type: TimedPolymorph
      enabled: false
    - type: AlienEvolution
      evolutionCooldown: 50

- type: entity
  name: "Прибулець дрон"
  abstract: true
  id: MobAlienBase
  parent: SimpleSpaceMobBase
  description: "Вони приходять переважно вночі. Здебільшого."
  components:
    - type: ShowInfectedIcons
    - type: PlasmaTransfer
    - type: Insulated
    - type: CombatMode
      combatToggleAction: ActionCombatModeToggleAlien
    - type: InputMover
    - type: MobMover
    - type: MovementSpeedModifier
      baseWalkSpeed: 2
      baseSprintSpeed: 5
    - type: HTN
      rootTask:
        task: XenoCompound
      blackboard:
        NavClimb: !type:Bool
          true
        NavInteract: !type:Bool
          true
        NavPry: !type:Bool
          true
        NavSmash: !type:Bool
          true
    - type: Tool
      qualities:
        - Prying
    - type: Prying
      pryPowered: !type:Bool
        true
      force: !type:Bool
        true
      useSound:
        path: /Audio/Items/crowbar.ogg
    - type: Reactive
      groups:
        Flammable: [Touch]
        Extinguish: [Touch]
    - type: NpcFactionMember
      factions:
        - Xeno
    - type: Hands
    - type: Sprite
      drawdepth: Mobs
      offset: 0, 0.2
      sprite: Mobs/Aliens/Xenos/alien.rsi
      layers:
        - map: ["enum.DamageStateVisualLayers.Base"]
          state: aliend
        - map: [ "pocket1" ]
        - map: [ "pocket2" ]
        - map: [ "enum.HumanoidVisualLayers.Handcuffs" ]
          color: "#ffffff"
          sprite: Mobs/Aliens/Xenos/alien.rsi
          state: aliencuff
          visible: false
        - map: [ "clownedon" ] # Dynamically generated
          sprite: "Effects/creampie.rsi"
          state: "creampie_human"
          visible: false
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeCircle
            radius: 0.25
          density: 1000
          mask:
            - MobMask
          layer:
            - MobLayer
    - type: MobState
      allowedStates:
        - Alive
        - Dead
        - Critical
    - type: MobThresholds
      thresholds:
        0: Alive
        180: Critical
        280: Dead
      stateAlertDict:
        Alive: AlienHealth
        Critical: AlienCrit
        Dead: AlienDead
    - type: SlowOnDamage
      speedModifierThresholds:
        25: 0.5
    - type: Stamina
      critThreshold: 200
    - type: Bloodstream
      bloodReagent: FluorosulfuricAcid
      bloodMaxVolume: 650
    - type: MeleeWeapon
      altDisarm: true
      angle: 0
      soundHit:
        collection: AlienClaw
      animation: WeaponArcBite
      damage:
        groups:
          Brute: 20
        types:
          Structural: 60
    - type: DamageStateVisuals
      rotate: true
      states:
        Alive:
          Base: aliend
        Critical:
          Base: aliend_unconscious
        Dead:
          Base: aliend_dead
    - type: Puller
    - type: Butcherable
      butcheringType: knife
      spawned:
        - id: FoodMeatXeno
          amount: 5
    - type: GhostRole
      allowMovement: true
      allowSpeech: true
      makeSentient: true
      name: mob-alien-name
      description: ghost-role-mob-alien-description
      rules: ghost-role-information-xeno-rules
    - type: GhostTakeoverAvailable
    - type: TypingIndicator
      proto: alien
    - type: Temperature
      heatDamageThreshold: 360
      coldDamageThreshold: -150
      currentTemperature: 310.15
    - type: Tag
      tags:
        - CannotSuicide
        - DoorBumpOpener
        - FootstepSound
    - type: NoSlip
    - type: Perishable #Ummmm the acid kills a lot of the bacteria or something
      molsPerSecondPerUnitMass: 0.0005
    - type: PassiveDamage
      allowedStates:
      - Alive
      - Critical
      - Dead
    - type: Alien
      caste: drone
      weedHeal:
        groups:
          Burn: -5
          Toxin: -5
          Airloss: -10
          Brute: -5
    - type: Body
      prototype: Xeno
    - type: Inventory
      templateId: alien
    - type: Cuffable
    - type: Ensnareable
      sprite: Objects/Misc/ensnare.rsi
      state: icon
    - type: InventorySlots
    - type: StatusEffects
      allowed:
        - Stun
        - KnockedDown
        - SlowedDown
        - Stutter
        - Electrocution
        - ForcedSleep
        - TemporaryBlindness
        - Pacified
        - StaminaModifier
    - type: Devourer
      shouldStoreDevoured: true
      foodPreference: All
      chemical: UncookedAnimalProteins
      healRate: 5.0
      whitelist:
        components:
          - MobState
      consumes:
        - Alive
        - Critical
        - Dead
    - type: VomitAction
      thirstAdded: 5.0
      hungerAdded: 5.0
    - type: PlasmaVessel
    - type: Alerts
    - type: Speech
      speechSounds: Alien
      speechVerb: SmallMob
    - type: Vocal
      sounds:
        Male: UnisexChitinid
        Female: UnisexChitinid
        Unsexed: UnisexChitinid
    - type: LizardAccent

- type: entity
  name: "Прибулець дрон"
  id: MobAlienDrone
  parent: MobAlienBase
  components:
    - type: AcidMaker
      action: ActionMakeAcid
      entityProduced: AlienAcid
    - type: ResinSpinner
    - type: PlasmaVessel
      plasmaPerSecond: 5
      plasmaModified: 40
    - type: PraetorianEvolution

- type: entity
  name: "Інопланетний вартовий"
  id: MobAlienSentinel
  parent: MobAlienBase
  components:
  - type: Alien
    caste: sentinel
  - type: AlienStalk
  - type: AcidMaker
    action: ActionMakeAcid
    entityProduced: AlienAcid
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/Xenos/alien.rsi
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: aliens
    - map: [ "pocket1" ]
    - map: [ "pocket2" ]
    - map: [ "enum.HumanoidVisualLayers.Handcuffs" ]
      color: "#ffffff"
      sprite: Mobs/Aliens/Xenos/alien.rsi
      state: aliencuff
      visible: false
    - map: [ "clownedon" ] # Dynamically generated
      sprite: "Effects/creampie.rsi"
      state: "creampie_human"
      visible: false
  - type: MobThresholds
    thresholds:
      0: Alive
      250: Critical
      350: Dead
  - type: MeleeWeapon
    altDisarm: true
    angle: 0
    soundHit:
      collection: AlienClaw
    animation: WeaponArcBite
    damage:
      groups:
        Brute: 25
      types:
        Structural: 60
  - type: DamageStateVisuals
    rotate: true
    states:
      Alive:
        Base: aliens
      Critical:
        Base: aliens_unconscious
      Dead:
        Base: aliens_dead
  - type: PlasmaVessel
    plasmaPerSecond: 5
    plasmaModified: 40
  - type: RechargeBasicEntityAmmo
    rechargeCooldown: 0.75
  - type: BasicEntityAmmoProvider
    proto: BulletAcidSentinel
    capacity: 1
    count: 1
  - type: Gun
    fireRate: 0.25
    useKey: false
    selectedMode: FullAuto
    availableModes:
    - FullAuto
    soundGunshot: /Audio/Weapons/Xeno/alien_spitacid.ogg
  - type: MovementSpeedModifier
    baseWalkSpeed: 4
    baseSprintSpeed: 5

- type: entity
  name: "Чужий преторіанський"
  id: MobAlienPraetorian
  parent: MobAlienBase
  components:
  - type: Alien
    caste: praetorian
  - type: AcidMaker
    action: ActionMakeAcid
    entityProduced: AlienAcid
    plasmaCost: 100
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/Xenos/alienqueen.rsi
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: alienp
    - map: [ "pocket1" ]
    - map: [ "pocket2" ]
    - map: [ "enum.HumanoidVisualLayers.Handcuffs" ]
      color: "#ffffff"
      sprite: Mobs/Aliens/Xenos/alienqueen.rsi
      state: aliencuff_p
      visible: false
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.4
        density: 1000
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: MobThresholds
    thresholds:
      0: Alive
      250: Critical
      350: Dead
  - type: MeleeWeapon
    altDisarm: true
    angle: 0
    soundHit:
      collection: AlienClaw
    animation: WeaponArcBite
    damage:
      groups:
        Brute: 30
      types:
        Structural: 80
  - type: DamageStateVisuals
    rotate: true
    states:
      Alive:
        Base: alienp
      Critical:
        Base: alienp_unconscious
      Dead:
        Base: alienp_dead
  - type: PlasmaVessel
    plasmaPerSecond: 5
    plasmaModified: 40
  - type: RechargeBasicEntityAmmo
    rechargeCooldown: 0.75
  - type: BasicEntityAmmoProvider
    proto: BulletAcidSentinel
    capacity: 1
    count: 1
  - type: Gun
    fireRate: 0.3
    useKey: false
    selectedMode: FullAuto
    availableModes:
    - FullAuto
    soundGunshot: /Audio/Weapons/Xeno/alien_spitacid.ogg
  - type: QueenEvolution
  - type: ResinSpinner
  - type: TailLash

- type: entity
  name: "Мисливець на прибульців"
  id: MobAlienHunter
  parent: MobAlienBase
  components:
  - type: Alien
    caste: hunter
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/Xenos/alien.rsi
    layers:
    - map: [ "enum.JumpLayers.Jumping" ]
      sprite: Mobs/Aliens/Xenos/alien_hunter_jump.rsi
      state: icon
      visible: false
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: alienh
    - map: [ "pocket1" ]
    - map: [ "pocket2" ]
    - map: [ "enum.HumanoidVisualLayers.Handcuffs" ]
      color: "#ffffff"
      sprite: Mobs/Aliens/Xenos/alien.rsi
      state: aliencuff
      visible: false
    - map: [ "clownedon" ] # Dynamically generated
      sprite: "Effects/creampie.rsi"
      state: "creampie_human"
      visible: false
  - type: GenericVisualizer
    visuals:
      enum.JumpVisuals.Jumping:
        enum.JumpLayers.Jumping:
          True: { visible: true }
          False: { visible: false }
        enum.DamageStateVisualLayers.Base:
          True: { visible: false}
          False: { visible: true}
  - type: MobThresholds
    thresholds:
      0: Alive
      205: Critical
      305: Dead
  - type: MeleeWeapon
    altDisarm: true
    angle: 0
    soundHit:
      collection: AlienClaw
    animation: WeaponArcBite
    damage:
      groups:
        Brute: 20
      types:
        Structural: 60
  - type: DamageStateVisuals
    rotate: true
    states:
      Alive:
        Base: alienh
      Critical:
        Base: alienh_unconscious
      Dead:
        Base: alienh_dead
  - type: PlasmaVessel
    maxPlasma: 150
    plasmaPerSecond: 5
    plasmaModified: 40
  - type: Devourer
    shouldStoreDevoured: true
    foodPreference: All
    chemical: UncookedAnimalProteins
    healRate: 5.0
    whitelist:
      components:
        - MobState
    consumes:
    - Alive
    - Critical
    - Dead
  - type: MovementSpeedModifier
    baseWalkSpeed: 4
    baseSprintSpeed: 8
  - type: JumpVisuals
    baseState: alienh
  - type: AlienJump
    jumpSprite: Mobs/Aliens/Xenos/alien_hunter_jump.rsi

- type: entity
  name: "Інопланетна королева"
  id: MobAlienQueen
  parent: MobAlienBase
  components:
  - type: Alien
    caste: queen
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/Xenos/alienqueen.rsi
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: alienq
    - map: [ "pocket1" ]
    - map: [ "pocket2" ]
    - map: [ "enum.HumanoidVisualLayers.Handcuffs" ]
      color: "#ffffff"
      sprite: Mobs/Aliens/Xenos/alienqueen.rsi
      state: aliencuff_q
      visible: false
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.6
        density: 1000
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: MobThresholds
    thresholds:
      0: Alive
      640: Critical
      740: Dead
  - type: MeleeWeapon
    altDisarm: true
    angle: 0
    soundHit:
      collection: AlienClaw
    animation: WeaponArcBite
    damage:
      groups:
        Brute: 30
      types:
        Structural: 300
  - type: DamageStateVisuals
    rotate: true
    states:
      Alive:
        Base: alienq
      Critical:
        Base: alienq_unconscious
      Dead:
        Base: alienq_dead
  - type: PlasmaVessel
    maxPlasma: 500
    plasmaPerSecond: 5
    plasmaModified: 40
  - type: ResinSpinner
  - type: MovementSpeedModifier
    baseWalkSpeed: 1
    baseSprintSpeed: 2
  - type: AcidMaker
    plasmaCost: 50
    entityProduced: AlienAcid
    action: ActionMakeAcid
  - type: AlienQueen
    action: ActionAlienRoyalLarva
  - type: Speech
    speechSounds: Alien
    speechVerb: LargeMob
  - type: TailLash

- type: entity
  name: "Хтива інопланетна покоївка"
  description: "Не просіть його відкладати в вас яйця."
  id: MobAlienMaid
  parent: MobAlienBase
  components:
  - type: Alien
    caste: maid
  - type: PlasmaVessel
    plasmaPerSecond: 5
    plasmaModified: 40
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/Xenos/alien.rsi
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: maid
    - map: [ "pocket1" ]
    - map: [ "pocket2" ]
    - map: [ "enum.HumanoidVisualLayers.Handcuffs" ]
      color: "#ffffff"
      sprite: Mobs/Aliens/Xenos/alien.rsi
      state: aliencuff
      visible: false
    - map: [ "clownedon" ] # Dynamically generated
      sprite: "Effects/creampie.rsi"
      state: "creampie_human"
      visible: false
  - type: MobThresholds
    thresholds:
      0: Alive
      208: Dead
  - type: MeleeWeapon
    altDisarm: true
    angle: 0
    soundHit:
      collection: AlienClaw
    animation: WeaponArcBite
    damage:
      groups:
        Brute: 25
      types:
        Structural: 60
  - type: DamageStateVisuals
    rotate: true
    states:
      Alive:
        Base: maid
      Dead:
        Base: maid_dead
  - type: OwOAccent
