﻿using Content.Shared.Administration.Events;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Console;

/*
 * TODO: Remove me once a more mature gateway process is established. This code is only being issued as a stopgap to help with potential tiding in the immediate future.
 */

namespace Content.Client.Administration.UI.Tabs.BabyJailTab;

[GenerateTypedNameReferences]
public sealed partial class BabyJailTab : Control
{
    [Dependency] private readonly IConsoleHost _console = default!;

    private string _maxAccountAge;
    private string _maxOverallHours;

    public BabyJailTab()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        MaxAccountAge.OnTextEntered += args => SendMaxAccountAge(args.Text);
        MaxAccountAge.OnFocusExit += args => SendMaxAccountAge(args.Text);
        _maxAccountAge = MaxAccountAge.Text;

        MaxOverallHours.OnTextEntered += args => SendMaxOverallHours(args.Text);
        MaxOverallHours.OnFocusExit += args => SendMaxOverallHours(args.Text);
        _maxOverallHours = MaxOverallHours.Text;
    }

    private void SendMaxAccountAge(string text)
    {
        if (string.IsNullOrWhiteSpace(text) ||
            text == _maxAccountAge ||
            !int.TryParse(text, out var hours))
        {
            return;
        }

        _console.ExecuteCommand($"babyjail_max_account_age {hours}");
    }

    private void SendMaxOverallHours(string text)
    {
        if (string.IsNullOrWhiteSpace(text) ||
            text == _maxOverallHours ||
            !int.TryParse(text, out var hours))
        {
            return;
        }

        _console.ExecuteCommand($"babyjail_max_overall_hours {hours}");
    }

    public void UpdateStatus(BabyJailStatus status)
    {
        EnabledButton.Pressed = status.Enabled;
        EnabledButton.Text = Loc.GetString(status.Enabled
            ? "admin-ui-baby-jail-enabled"
            : "admin-ui-baby-jail-disabled"
        );
        EnabledButton.ModulateSelfOverride = status.Enabled ? Color.Red : null;
        ShowReasonButton.Pressed = status.ShowReason;

        MaxAccountAge.Text = status.MaxAccountAgeHours.ToString();
        _maxAccountAge = MaxAccountAge.Text;

        MaxOverallHours.Text = status.MaxOverallHours.ToString();
        _maxOverallHours = MaxOverallHours.Text;
    }
}
