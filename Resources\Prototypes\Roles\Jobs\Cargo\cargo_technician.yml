- type: job
  id: CargoTechnician
  name: job-name-cargotech
  description: job-description-cargotech
  playTimeTracker: JobCargoTechnician
  startingGear: CargoTechGear
  icon: "JobIconCargoTechnician"
  supervisors: job-supervisors-qm
  access:
  - Cargo
  - Maintenance
  extendedAccess:
  - Salvage
  - Orders # DeltaV - Orders, see Resources/Prototypes/DeltaV/Access/cargo.yml
  requirements:
    - !type:CharacterEmployerRequirement
      employers:
      - OrionExpress
      - NanoTrasen

- type: startingGear
  id: CargoTechGear
  subGear:
  - CargoTechPlasmamanGear
  equipment:
    head: ClothingHeadHatCargosoft
    jumpsuit: ClothingUniformJumpsuitCargo
    back: ClothingBackpackCargoFilled
    shoes: ClothingShoesColorBlack
    id: CargoPDA
    ears: ClothingHeadsetCargo
    pocket1: AppraisalTool
  innerClothingSkirt: ClothingUniformJumpskirtCargo
  satchel: ClothingBackpackSatchelCargoFilled
  duffelbag: ClothingBackpackDuffelCargoFilled

- type: startingGear
  id: CargoTechPlasmamanGear
  parent: BasePlasmamanGear
  equipment:
    jumpsuit: ClothingUniformEnvirosuitCargo
    head: ClothingHeadEnvirohelmCargo
    gloves: ClothingHandsGlovesEnviroglovesCargo
