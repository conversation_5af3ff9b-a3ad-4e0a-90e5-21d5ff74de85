- type: entity
  abstract: true
  parent: BaseItem
  id: GasTankBase
  components:
  - type: Sprite
    sprite: Objects/Tanks/generic.rsi
    state: icon
  - type: Item
    size: Normal
    sprite: Objects/Tanks/generic.rsi
  - type: Clothing
    quickEquip: false
    sprite: Objects/Tanks/generic.rsi
    slots:
    - Back
    - suitStorage
  - type: ActivatableUI
    key: enum.SharedGasTankUiKey.Key
  - type: UserInterface
    interfaces:
      enum.SharedGasTankUiKey.Key:
        type: GasTankBoundUserInterface
  - type: GasTank
    outputPressure: 21.3
    air:
      # If gas tank volume is changed, adjust MinimumTritiumOxyburnEnergy in Atmospherics.cs by the same proportions
      volume: 5
      temperature: 293.15
    tankLowPressure: 30.0
  - type: Explosive
    explosionType: Default
    maxIntensity: 20
  - type: MeleeWeapon
    wideAnimationRotation: 45
    attackRate: 1.25
    range: 1.75
    damage:
      types:
        Blunt: 8
    bluntStaminaDamageFactor: 2.5
    heavyRateModifier: 1.5
    heavyDamageBaseModifier: 1.5
    heavyStaminaCost: 2.5
    maxTargets: 3
    angle: 100
    soundHit:
      path: /Audio/Weapons/smash.ogg
  - type: DamageOtherOnHit
    staminaCost: 8
  - type: PhysicalComposition
    materialComposition:
      Steel: 185
  - type: StaticPrice
    price: 20

- type: entity
  abstract: true
  parent: GasTankBase
  id: GasTankRoundBase
  components:
  - type: Tool
    qualities:
    - Rolling
    speedModifier: 0.6 # fairly unwieldly but nice round surface
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 105
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  parent: GasTankRoundBase
  id: OxygenTank
  name: "кисневий балон"
  description: "Стандартний циліндричний балон для кисню."
  components:
  - type: Sprite
    sprite: Objects/Tanks/oxygen.rsi
  - type: Item
    sprite: Objects/Tanks/oxygen.rsi
  - type: Clothing
    sprite: Objects/Tanks/oxygen.rsi

- type: entity
  parent: GasTankRoundBase
  id: NitrogenTank
  name: "азотний бак"
  description: "Стандартний циліндричний газовий балон для азоту."
  components:
  - type: Sprite
    sprite: Objects/Tanks/red.rsi
  - type: Item
    sprite: Objects/Tanks/red.rsi
  - type: Clothing
    sprite: Objects/Tanks/red.rsi

- type: entity
  parent: GasTankRoundBase
  id: EmergencyOxygenTank
  name: "аварійний кисневий балон"
  description: "Легко переносний балон для надзвичайних ситуацій. Містить дуже мало кисню, призначений лише для виживання."
  components:
  - type: Sprite
    sprite: Objects/Tanks/emergency.rsi
  - type: Item
    size: Small
    sprite: Objects/Tanks/emergency.rsi
  - type: GasTank
    air:
      volume: 0.66
      temperature: 293.15
  - type: Clothing
    sprite: Objects/Tanks/emergency.rsi
    slots:
    - Pocket
    - Belt
    - suitStorage
  - type: MeleeWeapon
    damage:
      types:
        Blunt: 5
  - type: DamageOtherOnHit
    staminaCost: 3.5
  - type: PhysicalComposition
    materialComposition:
      Steel: 100
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 45
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  parent: EmergencyOxygenTank
  id: EmergencyNitrogenTank
  name: "аварійний резервуар з азотом"
  description: "Легко переносний бак для надзвичайних ситуацій. Містить дуже мало азоту, призначений лише для виживання."
  components:
  - type: Sprite
    sprite: Objects/Tanks/emergency_red.rsi
  - type: Item
    sprite: Objects/Tanks/emergency_red.rsi
  - type: Clothing
    sprite: Objects/Tanks/emergency_red.rsi

- type: entity
  parent: EmergencyOxygenTank
  id: EmergencyPlasmaTank
  name: "аварійний резервуар для плазми"
  description: "Легко переносний балон для надзвичайних ситуацій. Містить дуже мало плазми, призначений лише для виживання."
  components:
  - type: Sprite
    sprite: Objects/Tanks/emergency_red.rsi # TODO: emergency plasma tank sprite
  - type: Item
    sprite: Objects/Tanks/emergency_red.rsi
  - type: Clothing
    sprite: Objects/Tanks/emergency_red.rsi

- type: entity
  parent: EmergencyOxygenTank
  id: ExtendedEmergencyOxygenTank
  name: "аварійний кисневий балон збільшеної ємності"
  description: "Аварійний бак збільшеної місткості. Технічно розрахований на тривале використання."
  components:
  - type: Sprite
    sprite: Objects/Tanks/emergency_extended.rsi
  - type: Item
    sprite: Objects/Tanks/emergency_extended.rsi
  - type: GasTank
    air:
      volume: 1.5
      temperature: 293.15
  - type: Clothing
    sprite: Objects/Tanks/emergency_extended.rsi
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 65
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  parent: ExtendedEmergencyOxygenTank
  id: ExtendedEmergencyNitrogenTank
  name: "аварійний резервуар для азоту збільшеної місткості"
  description: "Аварійний бак збільшеної місткості. Технічно розрахований на тривале використання."
  components:
  - type: Sprite
    sprite: Objects/Tanks/emergency_extended_red.rsi
  - type: Item
    sprite: Objects/Tanks/emergency_extended_red.rsi
  - type: Clothing
    sprite: Objects/Tanks/emergency_extended_red.rsi

- type: entity
  parent: ExtendedEmergencyOxygenTank
  id: ExtendedEmergencyPlasmaTank
  name: "аварійний плазмовий бак збільшеної місткості"
  description: "Аварійний бак збільшеної місткості. Технічно розрахований на тривале використання."
  components:
  - type: Sprite
    sprite: Objects/Tanks/emergency_extended_red.rsi # TODO: extended-capacity emergency plasma tank sprite
  - type: Item
    sprite: Objects/Tanks/emergency_extended_red.rsi
  - type: Clothing
    sprite: Objects/Tanks/emergency_extended_red.rsi

- type: entity
  parent: ExtendedEmergencyOxygenTank
  id: DoubleEmergencyOxygenTank
  name: "подвійний аварійний кисневий балон"
  description: "Високоякісний двобалонний аварійний контейнер для життєзабезпечення. Містить достатню кількість кисню, як на свій невеликий розмір."
  components:
  - type: Sprite
    sprite: Objects/Tanks/emergency_double.rsi
  - type: Item
    sprite: Objects/Tanks/emergency_double.rsi
  - type: GasTank
    air:
      volume: 2.5
      temperature: 293.15
  - type: Clothing
    sprite: Objects/Tanks/emergency_double.rsi
  - type: MeleeWeapon
    attackRate: 0.9
    damage:
      types:
        Blunt: 7.5
  - type: DamageOtherOnHit
    staminaCost: 5
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 90
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  parent: DoubleEmergencyOxygenTank
  id: DoubleEmergencyNitrogenTank
  name: "подвійний аварійний резервуар для азоту"
  description: "Високоякісний двобаковий контейнер для аварійної життєзабезпечення. Вміщує пристойну кількість азоту, як на свій невеликий розмір."
  components:
  - type: Sprite
    sprite: Objects/Tanks/emergency_double_red.rsi
  - type: Item
    sprite: Objects/Tanks/emergency_double_red.rsi
  - type: Clothing
    sprite: Objects/Tanks/emergency_double_red.rsi

- type: entity
  parent: DoubleEmergencyOxygenTank
  id: DoubleEmergencyPlasmaTank
  name: "внутрішній бак для плазми"
  description: "Резервуар з плазмою, призначений для внутрішнього використання для Plasmamen."
  components:
  - type: Sprite
    sprite: Objects/Tanks/plasmaman.rsi
  - type: Item
    sprite: Objects/Tanks/plasmaman.rsi
  - type: Clothing
    sprite: Objects/Tanks/plasmaman.rsi

- type: entity
  parent: EmergencyOxygenTank
  id: EmergencyFunnyOxygenTank
  name: "кумедний аварійний кисневий балон"
  description: "Легко переносний балон для надзвичайних ситуацій. Містить дуже мало кисню з додаванням веселячого газу, призначений лише для виживання."
  components:
  - type: Sprite
    sprite: Objects/Tanks/emergency_clown.rsi
  - type: Item
    sprite: Objects/Tanks/emergency_clown.rsi
  - type: Clothing
    sprite: Objects/Tanks/emergency_clown.rsi

- type: entity
  parent: GasTankRoundBase
  id: AirTank
  name: "повітряний балон"
  description: "З чимось змішаний?"
  components:
  - type: GasTank
    outputPressure: 101.3

- type: entity
  parent: GasTankRoundBase
  id: NitrousOxideTank
  name: "бак для закису азоту"
  description: "Містить суміш повітря та закису азоту. Переконайтеся, що ви не заправляєте його чистим N2O."
  components:
  - type: Sprite
    sprite: Objects/Tanks/anesthetic.rsi
  - type: Item
    sprite: Objects/Tanks/anesthetic.rsi
  - type: GasTank
    outputPressure: 30.4
  - type: Clothing
    sprite: Objects/Tanks/anesthetic.rsi

- type: entity
  # it's a square so no rolling dough
  parent: GasTankBase
  id: PlasmaTank
  name: "плазмовий бак"
  description: "Містить небезпечну плазму. Не вдихати, якщо ви не плазмоман. Надзвичайно легкозаймистий."
  components:
  - type: Sprite
    sprite: Objects/Tanks/plasma.rsi
  - type: Item
    sprite: Objects/Tanks/plasma.rsi
  - type: GasTank
    outputPressure: 101.3
  - type: Clothing
    sprite: Objects/Tanks/plasma.rsi
    slots:
    - Belt
    - suitStorage
