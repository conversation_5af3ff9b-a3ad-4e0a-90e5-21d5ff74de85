- type: entity
  parent: Airlock
  id: AirlockFreezer
  suffix: Freezer
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Standard/freezer.rsi
  - type: Wires
    layoutId: AirlockService

- type: entity
  parent: Airlock
  id: AirlockEngineering
  suffix: Engineering
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Standard/engineering.rsi
  - type: PaintableAirlock
    department: Engineering
  - type: Wires
    layoutId: AirlockEngineering

- type: entity
  parent: AirlockEngineering
  id: AirlockAtmospherics
  suffix: Atmospherics
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Standard/atmospherics.rsi

- type: entity
  parent: Airlock
  id: AirlockCargo
  suffix: Logistics # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Standard/cargo.rsi
  - type: PaintableAirlock
    department: Logistics
  - type: Wires
    layoutId: AirlockCargo

- type: entity
  parent: Airlock
  id: AirlockMedical
  suffix: Medical
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Standard/medical.rsi
  - type: PaintableAirlock
    department: Medical
  - type: Wires
    layoutId: AirlockMedical

- type: entity
  parent: AirlockMedical
  id: AirlockVirology
  suffix: Virology
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Standard/virology.rsi

- type: entity
  parent: AirlockMedical
  id: AirlockChemistry
  suffix: Chemistry

- type: entity
  parent: Airlock
  id: AirlockScience
  suffix: Epistemics # DeltaV - Epistemics Department replacing Science
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Standard/science.rsi
  - type: PaintableAirlock
    department: Epistemics
  - type: Wires
    layoutId: AirlockScience

- type: entity
  parent: Airlock
  id: AirlockCommand
  suffix: Command
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Standard/command.rsi
  - type: WiresPanelSecurity
    securityLevel: medSecurity
  - type: PaintableAirlock
    department: Command
  - type: Wires
    layoutId: AirlockCommand
  - type: PsionicInsulation #Psifoil shielding in the airlock

- type: entity
  parent: Airlock
  id: AirlockSecurity
  suffix: Security
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Standard/security.rsi
  - type: PaintableAirlock
    department: Security
  - type: Wires
    layoutId: AirlockSecurity
  - type: PsionicInsulation #Psifoil shielding in the airlock mechanisms

- type: entity
  parent: Airlock
  id: AirlockMaint
  name: "доступ до технічного обслуговування"
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Standard/maint.rsi

- type: entity
  parent: AirlockSecurity # if you get syndie door somehow it counts as sec
  id: AirlockSyndicate
  suffix: Syndicate
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Standard/syndicate.rsi

- type: entity
  parent: AirlockCargo
  id: AirlockMining
  suffix: Mining(Salvage)
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Standard/mining.rsi
  - type: Wires
    layoutId: AirlockCargo

- type: entity
  parent: AirlockCommand # if you get centcom door somehow it counts as command, also inherit panel
  id: AirlockCentralCommand
  suffix: Central Command
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Standard/centcomm.rsi

- type: entity
  parent: Airlock
  id: AirlockHatch
  name: "герметичний люк"
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Standard/hatch.rsi

- type: entity
  parent: Airlock
  id: AirlockHatchMaintenance
  name: "люк для обслуговування"
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Standard/hatch_maint.rsi

# Glass
- type: entity
  parent: AirlockGlass
  id: AirlockEngineeringGlass
  suffix: Engineering
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Glass/engineering.rsi
  - type: PaintableAirlock
    department: Engineering
  - type: Wires
    layoutId: AirlockEngineering

- type: entity
  parent: AirlockGlass
  id: AirlockMaintGlass
  suffix: Maintenance
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Glass/maint.rsi

- type: entity
  parent: AirlockEngineeringGlass
  id: AirlockAtmosphericsGlass
  suffix: Atmospherics
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Glass/atmospherics.rsi

- type: entity
  parent: AirlockGlass
  id: AirlockCargoGlass
  suffix: Logistics # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Glass/cargo.rsi
  - type: PaintableAirlock
    department: Logistics
  - type: Wires
    layoutId: AirlockCargo

- type: entity
  parent: AirlockGlass
  id: AirlockMedicalGlass
  suffix: Medical
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Glass/medical.rsi
  - type: PaintableAirlock
    department: Medical
  - type: Wires
    layoutId: AirlockMedical

- type: entity
  parent: AirlockMedicalGlass
  id: AirlockChemistryGlass
  suffix: Chemistry

- type: entity
  parent: AirlockMedicalGlass
  id: AirlockVirologyGlass
  suffix: Virology
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Glass/virology.rsi

- type: entity
  parent: AirlockGlass
  id: AirlockScienceGlass
  suffix: Epistemics # DeltaV - Epistemics Department replacing Science
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Glass/science.rsi
  - type: PaintableAirlock
    department: Epistemics
  - type: Wires
    layoutId: AirlockScience

- type: entity
  parent: AirlockGlass
  id: AirlockCommandGlass
  suffix: Command
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Glass/command.rsi
  - type: PaintableAirlock
    department: Command
  - type: WiresPanelSecurity
    securityLevel: medSecurity
  - type: Wires
    layoutId: AirlockCommand
  - type: PsionicInsulation #Psifoil shielding in the airlock mechanisms

- type: entity
  parent: AirlockGlass
  id: AirlockSecurityGlass
  suffix: Security
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Glass/security.rsi
  - type: PaintableAirlock
    department: Security
  - type: Wires
    layoutId: AirlockSecurity

- type: entity
  parent: AirlockSecurityGlass # see standard
  id: AirlockSyndicateGlass
  suffix: Syndicate
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Glass/syndicate.rsi

- type: entity
  parent: AirlockCargoGlass
  id: AirlockMiningGlass
  suffix: Mining(Salvage)
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Glass/mining.rsi

- type: entity
  parent: AirlockCommandGlass # see standard
  id: AirlockCentralCommandGlass
  suffix: Central Command
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Glass/centcomm.rsi
