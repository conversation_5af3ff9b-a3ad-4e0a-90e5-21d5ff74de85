mail-recipient-mismatch = Recipient name or job does not match.
mail-invalid-access = Recipient name and job match, but access isn't as expected.
mail-locked = The anti-tamper lock hasn't been removed. Tap the recipient's ID.
mail-desc-far = A parcel of mail. You can't make out who it's addressed to from this distance.
mail-desc-close = A parcel of mail addressed to {CAPITALIZE($name)}, {$job}.
mail-desc-fragile = It has a [color=red]red fragile label[/color].
mail-desc-priority = The anti-tamper lock's [color=yellow]yellow priority tape[/color] is active. Better deliver it on time!
mail-desc-priority-inactive = The anti-tamper lock's [color=#886600]yellow priority tape[/color] is inactive.
mail-unlocked = Anti-tamper system unlocked.
mail-unlocked-by-emag = Anti-tamper system *BZZT*.
mail-unlocked-reward = Anti-tamper system unlocked. {$bounty} spesos have been added to logistics' account.
mail-penalty-lock = ANTI-TAMPER LOCK BROKEN. LOGISTICS BANK ACCOUNT PENALIZED BY {$credits} SPESOS.
mail-penalty-fragile = INTEGRITY COMPROMISED. LOGISTICS BANK ACCOUNT PENALIZED BY {$credits} SPESOS.
mail-penalty-expired = DELIVERY PAST DUE. LOGISTICS BANK ACCOUNT PENALIZED BY {$credits} SPESOS.
mail-item-name-unaddressed = mail
mail-item-name-addressed = mail ({$recipient})

mail-large-item-name-unaddressed = package
mail-large-item-name-addressed = package ({$recipient})
mail-large-desc-far = A large package.
mail-large-desc-close = A large package addressed to {CAPITALIZE($name)}, {$job}.


