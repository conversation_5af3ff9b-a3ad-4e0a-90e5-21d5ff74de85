<controls:BoxContainer Visible="False"
       HorizontalExpand="True"
       xmlns:controls="https://spacestation14.io"
       xmlns:controls1="clr-namespace:Content.Client.UserInterface.Controls"
       xmlns:ui="clr-namespace:Content.Client.Shuttles.UI">
            <ui:ShuttleMapControl Name="MapRadar"
                             MouseFilter="Stop"
                             Margin="5 4 10 5"
                             HorizontalExpand="True"
                             VerticalExpand="True"
                             VerticalAlignment="Stretch"/>
            <controls:BoxContainer Name="RightDisplayMap"
                          VerticalAlignment="Top"
                          HorizontalAlignment="Right"
                          MinWidth="256"
                          MaxWidth="256"
                          Margin="5 0 5 5"
                          Orientation="Vertical"
                          VerticalExpand="True">
                <controls1:StripeBack
                    MinSize="48 48">
                    <controls:Label Name="MapDisplayLabel" Text="{controls:Loc 'shuttle-console-ftl-label'}"
                                    VerticalExpand="True"
                                    HorizontalAlignment="Center"/>
                </controls1:StripeBack>
                <controls:Label Name="MapFTLState"
                                Text="{controls:Loc 'shuttle-console-ftl-state-Available'}"
                                VerticalAlignment="Stretch"
                                HorizontalAlignment="Center"/>
                <controls:ProgressBar Name="FTLBar" HorizontalExpand="True"
                                      Margin="5"
                                      MinValue="0.0"
                                      MaxValue="1.0"
                                      Value="1.0"/>
                <controls:BoxContainer Orientation="Vertical">
                <!-- Normal buttons -->
                <controls1:StripeBack MinSize="48 48">
                    <controls:Label Name="SettingsLabel" Text="{controls:Loc 'shuttle-console-map-settings'}"
                           HorizontalAlignment="Center"/>
                </controls1:StripeBack>
                <controls:Button Name="MapBeaconsButton"
                                 Text="{controls:Loc 'shuttle-console-map-beacons'}"
                                 TextAlign="Center"
                                 ToggleMode="True"
                                 Pressed="True"/>
                <controls:Button Name="MapFTLButton"
                                 ToggleMode="True"
                                 Text="{controls:Loc 'shuttle-console-ftl-button'}"
                                 TextAlign="Center"/>
                <controls:Button Name="MapRebuildButton"
                        Text="{controls:Loc 'shuttle-console-map-rebuild'}"
                        TextAlign="Center"/>
                <!-- Map objects -->
                <controls1:StripeBack MinSize="48 48">
                    <controls:Label Name="HyperspaceLabel" Text="{controls:Loc 'shuttle-console-map-objects'}"
                           HorizontalAlignment="Center"/>
                </controls1:StripeBack>
                <controls:ScrollContainer VerticalExpand="True" HScrollEnabled="False"
                                 ReturnMeasure="True">
                    <controls:BoxContainer Name="HyperspaceDestinations"
                                  Orientation="Vertical"
                                  VerticalExpand="True"/>
                </controls:ScrollContainer>
            </controls:BoxContainer>
        </controls:BoxContainer>
</controls:BoxContainer>
