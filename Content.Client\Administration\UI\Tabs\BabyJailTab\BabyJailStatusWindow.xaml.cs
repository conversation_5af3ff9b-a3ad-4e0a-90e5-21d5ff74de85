﻿using Content.Client.Message;
using Content.Client.UserInterface.Controls;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Administration.UI.Tabs.BabyJailTab;

/*
 * TODO: Remove me once a more mature gateway process is established. This code is only being issued as a stopgap to help with potential tiding in the immediate future.
 */

[GenerateTypedNameReferences]
public sealed partial class BabyJailStatusWindow : FancyWindow
{
    public BabyJailStatusWindow()
    {
        RobustXamlLoader.Load(this);
        MessageLabel.SetMarkup(Loc.GetString("admin-ui-baby-jail-is-enabled"));
    }
}
