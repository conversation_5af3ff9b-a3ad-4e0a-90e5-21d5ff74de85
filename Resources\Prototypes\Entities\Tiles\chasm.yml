﻿- type: entity
  id: FloorChasmEntity
  name: "прірва"
  description: "Навіть дна не видно."
  placement:
    mode: SnapgridCenter
    snap:
    - Wall
  components:
  - type: Chasm
  - type: StepTrigger
    requiredTriggeredSpeed: 0
    intersectRatio: 0.4
    blacklist:
      tags:
      - Catwalk
    triggerGroups:
      types:
      - Chasm
  - type: Transform
    anchored: true
  - type: Clickable
  - type: Sprite
    sprite: Tiles/Planet/Chasms/basalt_chasm.rsi
    drawdepth: BelowFloor
    layers:
    - state: chasm
  - type: Icon
    sprite: Tiles/Planet/Chasms/basalt_chasm.rsi
    state: full
  - type: IconSmooth
    key: chasm
    base: chasm
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.5,-0.5,0.5,0.5"
        layer:
        - WallLayer
        mask:
        - ItemMask
        density: 1000
        hard: false
  - type: Tag
    tags:
    - HideContextMenu

- type: entity
  parent: FloorChasmEntity
  id: FloorChromiteChasm
  suffix: Chromite
  components:
  - type: Sprite
    sprite: Tiles/Planet/Chasms/chromite_chasm.rsi
  - type: Icon
    sprite: Tiles/Planet/Chasms/chromite_chasm.rsi

- type: entity
  parent: FloorChasmEntity
  id: FloorDesertChasm
  suffix: Desert
  components:
  - type: Sprite
    sprite: Tiles/Planet/Chasms/desert_chasm.rsi
  - type: Icon
    sprite: Tiles/Planet/Chasms/desert_chasm.rsi

- type: entity
  parent: FloorChasmEntity
  id: FloorSnowChasm
  suffix: Snow
  components:
  - type: Sprite
    sprite: Tiles/Planet/Chasms/snow_chasm.rsi
  - type: Icon
    sprite: Tiles/Planet/Chasms/snow_chasm.rsi
