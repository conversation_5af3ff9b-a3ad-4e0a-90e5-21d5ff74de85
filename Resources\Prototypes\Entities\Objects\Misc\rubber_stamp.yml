- type: entity
  name: "звич<PERSON>йна гумова печатка"
  parent: BaseItem
  id: RubberStampBase
  description: "Гумова печатка для штампування важливих документів."
  abstract: true
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-default
    stampState: "paper_stamp-generic"
    stampedColor: "#a23e3e"
    sound:
      path: /Audio/Items/Stamp/thick_stamp_sub.ogg
      params:
        volume: -2
        maxDistance: 5
  - type: Sprite
    sprite: Objects/Misc/stamps.rsi
    state: stamp-mime
  - type: Item
    size: Tiny
  - type: StealTarget
    stealGroup: Stamp

- type: entity
  name: "альтернативна гумова печатка"
  parent: RubberStampBase
  id: RubberStampBaseAlt
  abstract: true
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-default
    stampState: "paper_stamp-generic"
    sound:
      path: /Audio/Items/Stamp/automatic_stamp.ogg
      params:
        volume: -2
        maxDistance: 5

- type: entity
  name: "печатка капітана"
  parent: RubberStampBase
  id: RubberStampCaptain
  suffix: DO NOT MAP
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-captain
    stampedColor: "#3681bb"
    stampState: "paper_stamp-cap"
  - type: Sprite
    state: stamp-cap

- type: entity
  name: "Гумовий штамп CentCom"
  parent: RubberStampBase
  id: RubberStampCentcom
  suffix: DO NOT MAP
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-centcom
    stampedColor: "#006600"
    stampState: "paper_stamp-centcom"
  - type: Sprite
    state: stamp-centcom

- type: entity
  name: "печатка капелана"
  parent: RubberStampBase
  id: RubberStampChaplain
  suffix: DO NOT MAP
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-chaplain
    stampedColor: "#d70601"
    stampState: "paper_stamp-chaplain"
  - type: Sprite
    state: stamp-chaplain

- type: entity
  name: "гумовий штамп клоуна"
  parent: RubberStampBase
  id: RubberStampClown
  suffix: DO NOT MAP
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-clown
    stampedColor: "#ff33cc"
    stampState: "paper_stamp-clown"
  - type: Sprite
    state: stamp-clown
  - type: Tag
    tags:
      - ClownRubberStamp

- type: entity
  name: "печатка головного інженера"
  parent: RubberStampBase
  id: RubberStampCE
  suffix: DO NOT MAP
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-ce
    stampedColor: "#c69b17"
    stampState: "paper_stamp-ce"
  - type: Sprite
    state: stamp-ce

- type: entity
  name: "печатка головного лікаря"
  parent: RubberStampBase
  id: RubberStampCMO
  suffix: DO NOT MAP
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-cmo
    stampedColor: "#33ccff"
    stampState: "paper_stamp-cmo"
  - type: Sprite
    state: stamp-cmo

- type: entity
  name: "печатка начальника відділу кадрів"
  parent: RubberStampBase
  id: RubberStampHop
  suffix: DO NOT MAP
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-hop
    stampedColor: "#6ec0ea"
    stampState: "paper_stamp-hop"
  - type: Sprite
    state: stamp-hop

- type: entity
  name: "печатка керівника служби безпеки"
  parent: RubberStampBase
  id: RubberStampHos
  suffix: DO NOT MAP
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-hos
    stampedColor: "#cc0000"
    stampState: "paper_stamp-hos"
  - type: Sprite
    state: stamp-hos

- type: entity
  name: "гумовий штамп міма"
  parent: RubberStampBase
  id: RubberStampMime
  suffix: DO NOT MAP
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-mime
    stampedColor: "#777777"
    stampState: "paper_stamp-mime"
    sound: null
  - type: Sprite
    state: stamp-mime

- type: entity
  name: "печатка офіцера з логістики" # DeltaV - Logistics Department replacing Cargo
  parent: RubberStampBase
  id: RubberStampQm
  suffix: DO NOT MAP
  components:
    - type: Stamp
      stampedName: stamp-component-stamped-name-qm
      stampedColor: "#a23e3e"
      stampState: "paper_stamp-qm"
    - type: Sprite
      state: stamp-qm

- type: entity
  name: "штамп містифікатора" # DeltaV - Epistemics Department replacing Science
  parent: RubberStampBase
  id: RubberStampRd
  suffix: DO NOT MAP
  components:
    - type: Stamp
      stampedName: stamp-component-stamped-name-rd
      stampedColor: "#1f66a0"
      stampState: "paper_stamp-rd"
    - type: Sprite
      state: stamp-rd

- type: entity
  name: "штамп трейдера"
  parent: RubberStampBase
  id: RubberStampTrader
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-trader
    stampedColor: "#000000"
    stampState: "paper_stamp-trader"
  - type: Sprite
    state: stamp-trader

- type: entity
  name: "синдикатська гумова печатка"
  parent: RubberStampBase
  id: RubberStampSyndicate
  suffix: DO NOT MAP
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-syndicate
    stampedColor: "#850000"
    stampState: "paper_stamp-syndicate"
  - type: Sprite
    state: stamp-syndicate

- type: entity
  name: "печатка наглядача"
  parent: RubberStampBase
  id: RubberStampWarden
  suffix: DO NOT MAP
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-warden
    stampedColor: "#5b0000"
    stampState: "paper_stamp-warden"
  - type: Sprite
    state: stamp-warden

- type: entity
  name: "ЗАТВЕРДЖЕНА гумова печатка"
  parent: RubberStampBaseAlt
  id: RubberStampApproved
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-approved
    stampedColor: "#00be00"
    stampState: "paper_stamp-ok"
  - type: Sprite
    state: stamp-ok

- type: entity
  name: "ВІДМОВЛЕНА гумова печатка"
  parent: RubberStampBaseAlt
  id: RubberStampDenied
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-denied
    stampedColor: "#a23e3e"
    stampState: "paper_stamp-deny"
  - type: Sprite
    state: stamp-deny

- type: entity
  name: "печатка детектива"
  parent: RubberStampBase
  id: RubberStampDetective
  suffix: DO NOT MAP
  components:
  - type: Stamp
    stampedName: stamp-component-stamped-name-detective
    stampedColor: "#FC0A0A"
    stampState: "paper_stamp-detective"
  - type: Sprite
    state: stamp-detective
