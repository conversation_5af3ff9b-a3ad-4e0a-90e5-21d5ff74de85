### Secret stash component. Stuff like potted plants, comfy chair cushions, etc...

comp-secret-stash-secret-part-name = { THE($item) }
comp-secret-stash-action-hide-success = You hide { THE($item) } in { $this }
comp-secret-stash-action-hide-container-not-empty = There's already something in here?!
comp-secret-stash-action-hide-item-too-big = { THE($item) } is too big to fit in {$stash}!
comp-secret-stash-action-get-item-found-something = There was something inside {$stash}!
comp-secret-stash-on-examine-found-hidden-item = There is something hidden inside.

secret-stash-part-plant = the plant
secret-stash-part-toilet = the toilet cistern
