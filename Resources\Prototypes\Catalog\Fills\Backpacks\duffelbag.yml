- type: entity
  parent: ClothingBackpackDuffelMedical
  id: ClothingBackpackDuffelSurgeryFilled
  name: "хірургічна сумка-мішок"
  description: "Велика сумка для додаткових медичних засобів - ця, здається, призначена для зберігання хірургічних інструментів."
  components:
  - type: StorageFill
    contents:
      - id: Hemostat
      - id: SawElectric # Shitmed Change
      - id: Drill
      - id: Cautery
      - id: Retractor
      - id: Scalpel
      - id: BoneGel # Shitmed Change

- type: entity
  id: ClothingBackpackDuffelCBURNFilled
  parent: ClothingBackpackDuffelCBURN
  suffix: Filled
  components:
  - type: StorageFill
    contents:
    - id: BoxSurvivalEngineering
    - id: WeaponShotgunDoubleBarreled
    - id: BoxShotgunIncendiary
      amount: 2
    - id: GrenadeFlashBang
      amount: 2
    - id: PillAmbuzolPlus
    - id: PillAmbuzol
      amount: 4

- type: entity
  parent: ClothingBackpackDuffelSyndicateMedicalBundle
  id: ClothingBackpackDuffelSyndicateFilledMedical
  name: "синдикатська хірургічна сумка-мішок"
  description: "Велика сумка з повним набором хірургічних інструментів."
  components:
  - type: StorageFill
    contents:
      - id: Hemostat
      - id: SawAdvanced
      - id: Drill
      - id: Cautery
      - id: Retractor
      - id: ScalpelAdvanced
      - id: ClothingHandsGlovesNitrile
      - id: EmergencyRollerBedSpawnFolded
      - id: BoneGel # Shitmed Change

- type: entity
  parent: ClothingBackpackDuffelSyndicateBundle
  id: ClothingBackpackDuffelSyndicateFilledShotgun
  name: "комплект зі зброєю Бульдог"
  description: "Худий і злий: Містить популярну рушницю \"Бульдог\", барабан з картеччю на 12 г та 3 барабани з картеччю на 12 г." #, and a pair of Thermal Imaging Goggles.
  components:
  - type: StorageFill
    contents:
      - id: WeaponShotgunBulldog
      - id: MagazineShotgun
      - id: MagazineShotgun
      - id: MagazineShotgunBeanbag
#     - id: ThermalImagingGoggles

- type: entity
  parent: ClothingBackpackDuffelSyndicateBundle
  id: ClothingBackpackDuffelSyndicateFilledSMG
  name: "комплект з C-20r"
  description: "Старий, як світ: класичний пістолет-кулемет C-20r у комплекті з трьома магазинами." #, and a Suppressor.
  components:
  - type: StorageFill
    contents:
      - id: WeaponSubMachineGunC20rEmpty
#     - id: SMGSuppressor

- type: entity
  parent: ClothingBackpackDuffelSyndicateBundle
  id: ClothingBackpackDuffelSyndicateFilledRevolver
  name: "комплект з Python"
  description: "З повністю зарядженим Magnum Python, оснащеним двома спідлоудерами, ви будете голосно і гордо заявляти про себе."
  components:
  - type: StorageFill
    contents:
      - id: WeaponRevolverPythonAP
      - id: SpeedLoaderMagnumAP
        amount: 2

- type: entity
  parent: ClothingBackpackDuffelSyndicateBundle
  id: ClothingBackpackDuffelSyndicateFilledLMG
  name: "комплект з L6 SAW"
  description: "Більше дакка: культовий кулемет L6 у комплекті з 2 коробчастими магазинами."
  components:
  - type: StorageFill
    contents:
      - id: WeaponLightMachineGunL6
      - id: MagazineLightRifleBox

- type: entity
  parent: ClothingBackpackDuffelSyndicateBundle
  id: ClothingBackpackDuffelSyndicateFilledGrenadeLauncher
  name: "комплект з гранатометом China-Lake"
  description: "Старий гранатомет China-Lake у комплекті з 11 пострілами різого типу."
  components:
  - type: StorageFill
    contents:
      - id: WeaponLauncherChinaLake
      - id: GrenadeBlast
        amount: 4
      - id: GrenadeFrag
        amount: 4

- type: entity
  parent: ClothingBackpackDuffelSyndicateBundle
  id: ClothingBackpackDuffelSyndicateFilledCarbine
  name: "комплект з М-90г"
  description: "Універсальна бойова гвинтівка з підствольним гранатометом, в комплекті з 3 магазинами та 6 гранатами різної потужності."
  components:
  - type: StorageFill
    contents:
      - id: WeaponRifleM90GrenadeLauncher
      - id: MagazineRifle
        amount: 2
      - id: GrenadeBlast
        amount: 2
      - id: GrenadeFlash
        amount: 2
      - id: GrenadeFrag
        amount: 2

- type: entity
  parent: ClothingBackpackDuffelSyndicateAmmo
  id: ClothingBackpackDuffelSyndicateAmmoFilled
  name: "комплект боєприпасів"
  description: "Перезарядка! Містить 4 магазини для C-20r, 4 барабани для Bulldog, 2 коробки з набоями для L6 SAW і 3 магазини для Burner."
  components:
  - type: StorageFill
    contents:
      - id: MagazinePistolSubMachineGun
        amount: 4
      - id: MagazineRifle # goobstation - m90がアップリンクに戻る
        amount: 3
      - id: MagazineShotgun
        amount: 4
      - id: MagazineLightRifleBox
        amount: 2
      - id: MagazineHighCaliber # Goobstation - Burner Heavy Assault Rifle
        amount: 1
      - id: MagazineHighCaliberExplosive # Goobstation - Burner Heavy Assault Rifle


- type: entity
  parent: ClothingBackpackDuffelSyndicateAmmo
  id: ClothingBackpackDuffelSyndicateAmmoFilledRubber
  name: "комплект боєприпасів"
  description: "Перезарядка! Містить 4 магазини для C-20r, 4 барабани для Bulldog і 2 коробки боєприпасів для L6 SAW."
  components:
  - type: StorageFill
    contents:
      - id: MagazinePistolSubMachineGunRubber
        amount: 4
      - id: MagazineShotgunBeanbag
        amount: 4
      - id: MagazineLightRifleBoxRubber
        amount: 2

- type: entity
  parent: ClothingBackpackDuffelSyndicateAmmo
  id: ClothingBackpackDuffelSyndicateAmmoFilledIncendiary
  name: "комплект боєприпасів"
  description: "Перезарядка! Містить 4 магазини для C-20r, 4 барабани для Bulldog і 2 коробки боєприпасів для L6 SAW."
  components:
  - type: StorageFill
    contents:
      - id: MagazinePistolSubMachineGunIncendiary
        amount: 4
      - id: MagazineShotgunIncendiary
        amount: 4
      - id: MagazineLightRifleBoxIncendiary
        amount: 2

- type: entity
  parent: ClothingBackpackDuffelSyndicateAmmo
  id: ClothingBackpackDuffelSyndicateAmmoFilledShrapnel
  name: "комплект боєприпасів"
  description: "Перезарядка! Містить 4 магазини для C-20r, 4 барабани для Bulldog і 2 коробки боєприпасів для L6 SAW."
  components:
  - type: StorageFill
    contents:
      - id: MagazinePistolSubMachineGunShrapnel
        amount: 4
      - id: MagazineShotgunBirdshot
        amount: 4
      - id: MagazineLightRifleBoxShrapnel
        amount: 2

- type: entity
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelSyndicateCostumeCentcom
  name: "комплект з офіційним костюмом ЦК"
  description: "Містить повний комплект офіційної уніформи CentCom, гарнітуру та буфер обміну. Ключі шифрування та ідентифікатор доступу не входять до комплекту."
  suffix: DO NOT MAP
  components:
  - type: Tag
    tags: [] # ignore "WhitelistChameleon" tag
  - type: StorageFill
    contents:
      - id: ClothingOuterArmorBasic
      - id: ClothingHeadHatCentcom
      - id: ClothingEyesGlassesSunglasses
      - id: ClothingUniformJumpsuitCentcomOfficial
      - id: ClothingShoesBootsJack
      - id: ClothingHandsGlovesColorBlack
      - id: ClothingHeadsetAltCentComFake
      - id: Paper
      - id: Pen
      - id: CentcomPDAFake

- type: entity
  parent: ClothingBackpackDuffelClown
  id: ClothingBackpackDuffelSyndicateCostumeClown
  suffix: syndicate
  components:
  - type: Tag
    tags: [] # ignore "WhitelistChameleon" tag
  - type: StorageFill
    contents:
      - id: ClothingUniformJumpsuitClown
      - id: ClothingShoesClown
      - id: ClothingMaskClown
      - id: BikeHorn
      - id: ClownPDA
      - id: ClothingHeadsetService

- type: entity
  parent: ClothingBackpackDuffelSyndicateBundle
  id: ClothingBackpackDuffelSyndicateCarpSuit
  name: "комплект з костюмом коропа"
  description: "Містить костюм коропа та друзів, з якими можна погратися."
  components:
  - type: StorageFill
    contents:
    - id: ClothingOuterSuitCarp
    - id: PlushieCarp
      amount: 6

- type: entity
  parent: ClothingBackpackDuffelSyndicateBundle
  id: ClothingBackpackDuffelSyndicatePyjamaBundle
  name: "комплект з піжамою Синдикату"
  description: "Містить 3 пари піжам Синдикату та 3 плюшевих іграшки для ідеальної ночівлі."
  components:
  - type: Storage
    grid:
    - 0,0,8,4
  - type: StorageFill
    contents:
      - id: ClothingUniformJumpsuitPyjamaSyndicateRed
      - id: ClothingUniformJumpsuitPyjamaSyndicateBlack
      - id: ClothingUniformJumpsuitPyjamaSyndicatePink
      - id: ClothingHeadPyjamaSyndicateRed
      - id: ClothingHeadPyjamaSyndicateBlack
      - id: ClothingHeadPyjamaSyndicatePink
      - id: ClothingShoesSlippers
        amount: 3
      - id: BedsheetSyndie
        amount: 3
      - id: PlushieCarp
      - id: PlushieNuke
      - id: PlushieLizard
      - id: PlushieSharkBlue

- type: entity
  parent: ClothingBackpackDuffelSyndicateBundle
  id: ClothingBackpackDuffelSyndicateC4tBundle
  name: "комплект з С-4 Синдикату"
  description: "Містить багато зарядів С-4."
  components:
  - type: StorageFill
    contents:
      - id: C4
        amount: 8

- type: entity
  parent: ClothingBackpackChameleon
  id: ClothingBackpackChameleonFill
  suffix: Fill, Chameleon
  components:
    - type: StorageFill
      contents:
        - id: ClothingUniformJumpsuitChameleon
        - id: ClothingOuterChameleon
        - id: ClothingNeckChameleon
        - id: ClothingMaskGasChameleon
        - id: ClothingHeadHatChameleon
        - id: ClothingHandsChameleon
        - id: ClothingEyesChameleon
        - id: ClothingHeadsetChameleon
        - id: ClothingShoesChameleon
        - id: BarberScissors

- type: entity
  parent: ClothingBackpackDuffelSyndicateBundle
  id: ClothingBackpackDuffelSyndicateEVABundle
  name: "комплект EVA Синдикату"
  description: "Містить затверджений Синдикатом костюм EVA."
  components:
  - type: StorageFill
    contents:
      - id: ClothingHeadHelmetSyndicate
      - id: ClothingOuterHardsuitSyndicate
      - id: ClothingMaskGasSyndicate
      - id: ClothingHandsGlovesColorYellowBudget
      - id: DoubleEmergencyOxygenTankFilled
      - id: DoubleEmergencyNitrogenTankFilled
      - id: DoubleEmergencyPlasmaTankFilled

- type: entity
  parent: ClothingBackpackDuffelSyndicateBundle
  id: ClothingBackpackDuffelSyndicateHardsuitBundle
  name: "комплект хардсьюту Синдикату"
  description: "Містить фірмовий криваво-червоний костюм Синдикату."
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterHardsuitSyndie
      - id: ClothingMaskGasSyndicate
      - id: ClothingHandsGlovesCombat
      - id: DoubleEmergencyOxygenTankFilled
      - id: DoubleEmergencyNitrogenTankFilled
      - id: DoubleEmergencyPlasmaTankFilled

- type: entity
  parent: ClothingBackpackDuffelSyndicateBundle
  id: ClothingBackpackDuffelSyndicateHardsuitCommanderBundle
  name: "комплект скафандра синдикату командира"
  description: "Містить фірмовий криваво-червоний костюм Синдикату."
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterHardsuitSyndieCommander
      - id: ClothingMaskGasSyndicate
      - id: ClothingHandsGlovesCombat
      - id: DoubleEmergencyOxygenTankFilled
      - id: DoubleEmergencyNitrogenTankFilled
      - id: DoubleEmergencyPlasmaTankFilled

- type: entity
  parent: ClothingBackpackDuffelSyndicateBundle
  id: ClothingBackpackDuffelSyndicateHardsuitMedicBundle
  name: "комплект скафандра синдикату медика"
  description: "Містить фірмовий криваво-червоний костюм Синдикату."
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterHardsuitSyndieMedic
      - id: ClothingMaskGasSyndicate
      - id: ClothingHandsGlovesCombat
      - id: DoubleEmergencyOxygenTankFilled
      - id: DoubleEmergencyNitrogenTankFilled
      - id: DoubleEmergencyPlasmaTankFilled

- type: entity
  parent: ClothingBackpackDuffelSyndicateBundle
  id: ClothingBackpackDuffelSyndicateEliteHardsuitBundle
  name: "комплект елітного хардсьюту Синдикату"
  description: "Містить елітний хардкостюм Синдикату, до якого додається ще кілька речей."
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterHardsuitSyndieElite
      - id: ClothingMaskGasSyndicate
      - id: ClothingHandsGlovesCombat
      - id: DoubleEmergencyOxygenTankFilled
      - id: DoubleEmergencyNitrogenTankFilled
      - id: DoubleEmergencyPlasmaTankFilled

- type: entity
  parent: ClothingBackpackDuffelSyndicateBundle
  id: ClothingBackpackDuffelSyndicateHardsuitExtrasBundle
  name: "комплект додаткового спорядження для хардсьюту Синдикату"
  description: "Містить речі, які ви неодмінно захочете мати при покупці захисного костюма."
  components:
  - type: StorageFill
    contents:
      - id: ClothingMaskGasSyndicate
      - id: ClothingHandsGlovesCombat
      - id: DoubleEmergencyOxygenTankFilled
      - id: DoubleEmergencyNitrogenTankFilled
      - id: DoubleEmergencyPlasmaTankFilled

- type: entity
  parent: ClothingBackpackDuffelSyndicateBundle
  id: ClothingBackpackDuffelZombieBundle
  name: "комплект зомбі"
  description: "Комплект \"все в одному\" для вивільнення нежиті на станції."
  components:
  - type: StorageFill
    contents:
      - id: SyringeRomerol
      - id: WeaponRevolverPython
      - id: MagazineBoxMagnumIncendiary
      - id: PillAmbuzolPlus
      - id: PillAmbuzol
        amount: 3

- type: entity
  parent: ClothingBackpackDuffelSyndicateBundle
  id: ClothingBackpackDuffelSyndicateOperative
  name: "сумка-мішок оперативника"
  components:
  - type: StorageFill
    contents:
      - id: BoxSurvivalSyndicate
      - id: WeaponPistolViper
      - id: PinpointerSyndicateNuclear
      - id: DeathAcidifierImplanter


- type: entity
  parent: ClothingBackpackDuffelSyndicateMedicalBundle
  id: ClothingBackpackDuffelSyndicateOperativeMedic
  name: "сумка-мішок медика-оперативника"
  description: "Велика сумка для зберігання додаткового медичного приладдя."
  components:
  - type: StorageFill
    contents:
      - id: SyndiHypo
      - id: BoxSurvivalSyndicate
      - id: SawAdvanced
      - id: Cautery
      - id: CombatKnife
      - id: WeaponPistolViper
      - id: PinpointerSyndicateNuclear
      - id: HandheldHealthAnalyzer
      - id: CombatMedipen
      - id: DeathAcidifierImplanter
      - id: OmnimedToolSyndie # Shitmed Change

- type: entity
  parent: ClothingBackpackDuffelSyndicateMedicalBundle
  id: ClothingBackpackDuffelSyndicateMedicalBundleFilled
  name: "комплект медицини"
  description: "Все, що вам потрібно, - це повернути товаришів у бій."
  components:
  - type: StorageFill
    contents:
      - id: DefibrillatorSyndicate
      - id: MedkitCombatFilled
        amount: 4
      - id: Tourniquet
        amount: 4
      - id: CombatMedipen
        amount: 4
      - id: PunctAutoInjector
        amount: 4
      - id: PyraAutoInjector
        amount: 4
      - id: AirlossAutoInjector
        amount: 4

- type: entity
  parent: ClothingBackpackDuffelSyndicateBundle
  id: ClothingBackpackDuffelSyndicateDecoyKitFilled
  name: "комплект приманок"
  description: "Містить відволікаючі пристрої, як слухові, так і візуальні. Скоро - і з запахами."
  components:
  - type: StorageFill
    contents:
      - id: BalloonOperative
      - id: BalloonAgent
      - id: BalloonElite
      - id: BalloonJuggernaut
      - id: BalloonCommander
      - id: SnapPopBox
      - id: GrenadeDummy
        amount: 2
      - id: SyndieTrickyBomb
        amount: 2

- type: entity
  parent: ClothingBackpackDuffelSyndicateBundle
  id: ClothingBackpackDuffelSyndicateFilledFPA90
  name: "комплект FPA-90"
  description: "Дешевий пістолет-кулемет з інтегрованим глушником. Магазини продаються окремо."
  components:
  - type: StorageFill
    contents:
      - id: WeaponSubMachineGunFPA90Empty
