- type: entity
  id: TelescopicBaton
  parent: BaseItem
  name: "телескопічний кийок"
  description: "Компактна та нешкідлива зброя для особистого захисту. Досить міцний, щоб вибити ноги з-під нападників."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Melee/telebaton.rsi
    layers:
    - state: icon
      map: [ "enum.TelescopicBatonVisuals.Layer" ]
  - type: Item
    size: Small
  - type: ItemToggle
    soundActivate:
      path: /Audio/Weapons/telescopicon.ogg
      params:
        volume: -2
    soundDeactivate:
      path: /Audio/Weapons/telescopicoff.ogg
      params:
        volume: -2
  - type: ComponentToggler
    components:
    - type: DisarmMalus
      malus: 0.6
  - type: ItemToggleMeleeWeapon
    activatedDamage:
      types:
        Blunt: 12
  - type: ItemToggleDamageOtherOnHit
  - type: ItemToggleSize
    activatedSize: Normal
  - type: UseDelay
    delay: 2
  - type: TelescopicBaton
  - type: KnockdownOnHit
    duration: 1.5
    dropHeldItemsBehavior: NoDrop
  - type: MeleeWeapon
    attackRate: 1.25
    bluntStaminaDamageFactor: 1.5
    heavyDamageBaseModifier: 1.2
    heavyStaminaCost: 2.5
    maxTargets: 1
    angle: 40
    damage:
      types:
        Blunt: 1
  - type: DamageOtherOnHit
    staminaCost: 6
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.TelescopicBatonVisuals.State:
        enum.TelescopicBatonVisuals.Layer:
          True: { state: icon }
          False: { state: icon-off }

- type: entity
  parent: TelescopicBaton
  id: TelescopicBatonAdmeme
  name: "міцний телескопічний кийок"
  description: "Компактна та ШКІДЛИВА зброя особистого захисту. Достатньо міцна, щоб зламати ноги нападникам, позбавивши їх можливості ходити."
  suffix: admeme, DO NOT MAP
  components:
  - type: TelescopicBaton
    attackTimeframe: 300 # one minute after activation
  - type: KnockdownOnHit
    duration: 60 #
  - type: MeleeWeapon
    attackRate: .83
  - type: ItemToggleMeleeWeapon
    activatedDamage:
      types:
        Blunt: 20
