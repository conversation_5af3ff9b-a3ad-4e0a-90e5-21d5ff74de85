<controls:FancyWindow xmlns="https://spacestation14.io"
        xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
        SetSize="500 500">
    <BoxContainer Orientation="Vertical">
	    <ScrollContainer SetHeight="256" HorizontalExpand="True">
    	    <ItemList Name="Destinations"/> <!-- Populated from comp.Destinations -->
	    </ScrollContainer>
        <controls:StripeBack MinSize="48 48">
            <Label Text="{Loc 'shuttle-console-ftl-label'}" VerticalExpand="True" HorizontalAlignment="Center"/>
        </controls:StripeBack>
	    <Label Name="MapFTLState" Text="{Loc 'shuttle-console-ftl-state-Available'}" VerticalAlignment="Stretch" HorizontalAlignment="Center"/>
    	<ProgressBar Name="FTLBar" HorizontalExpand="True" Margin="5" MinValue="0.0" MaxValue="1.0" Value="1.0" SetHeight="32"/>
	    <controls:StripeBack HorizontalExpand="True">
    	    <Button Name="FTLButton" Text="{Loc 'docking-console-ftl'}" Disabled="False" SetSize="128 48" Margin="5" HorizontalAlignment="Left"/>
            <Button Name="ShuttleCallButton" Text="{Loc 'docking-console-call'}" Disabled="False" SetSize="128 48" Margin="5" HorizontalAlignment="Right"/>
		</controls:StripeBack>
        <Label Name="MapFTLMessage" Text="{Loc 'docking-console-ftl-message-Unknown'}" VerticalAlignment="Bottom" HorizontalAlignment="Center"/>
	</BoxContainer>
</controls:FancyWindow>
