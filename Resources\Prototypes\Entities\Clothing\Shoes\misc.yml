- type: entity
  parent: ClothingShoesBase
  id: ClothingShoesFlippers
  name: "ласти"
  description: "Пара гумових ласт, які покращують здатність плавати при носінні."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Misc/flippers.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Misc/flippers.rsi

- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesLeather
  name: "шкіряне взуття"
  description: "Дуже стильні черевики, виготовлені з тонкої шкіри."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Misc/leather.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Misc/leather.rsi

- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesSlippers
  name: "капці"
  description: "Пухнасті!"
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Misc/slippers.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Misc/slippers.rsi

- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoeSlippersDuck
  name: "качині капці"
  # description: You wish these made quacking sounds as you walked.
  description: "Затишний, але переслідуваний привидами качок, яких ви годували хлібом у дитинстві."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Misc/duck-slippers.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Misc/duck-slippers.rsi
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepDuck
      params:
        variation: 0.07
  - type: WaddleWhenWorn
    tumbleIntensity: 10 # smaller than clown shoes
  - type: Construction
    graph: ClothingShoeSlippersDuck
    node: shoes

- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesTourist
  name: "туристичне взуття"
  description: "Ці дешеві босоніжки виглядають не дуже зручно."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Misc/tourist.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Misc/tourist.rsi

- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesDameDane
  name: "взуття якудза"
  description: "Нарешті..."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Misc/damedaneshoes.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Misc/damedaneshoes.rsi

- type: entity
  parent: ClothingShoesBase
  id: ClothingShoesSnakeskinBoots
  name: "чоботи зі зміїної шкіри"
  description: "Чоботи з висококласної зміїної шкіри, яким позаздрять всі навколо."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Misc/snakeskin.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Misc/snakeskin.rsi
  - type: NoSlip

- type: entity
  parent: [ClothingShoesBase, PowerCellSlotSmallItem, BaseToggleClothing]
  id: ClothingShoesBootsSpeed
  name: "швидкісні черевики"
  description: "Високотехнологічні черевики, сплетені з квантових волокон, здатні перетворювати електрику на чисту швидкість!"
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Boots/speedboots.rsi
    layers:
    - state: icon
      map: [ "enum.ToggleVisuals.Layer" ]
  - type: Clothing
    sprite: Clothing/Shoes/Boots/speedboots.rsi
  - type: ToggleClothing
    action: ActionToggleSpeedBoots
  - type: ClothingSpeedModifier
    walkModifier: 1.5
    sprintModifier: 1.5
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ToggleVisuals.Toggled:
        enum.ToggleVisuals.Layer:
          True: {state: icon-on}
          False: {state: icon}
  - type: StaticPrice
    price: 500
  - type: PowerCellDraw
    drawRate: 4
  - type: ToggleCellDraw
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
  - type: Tag
    tags: []

- type: entity
  id: ActionToggleSpeedBoots
  name: "Перемкнути Швидкісні Черевики"
  description: "Увімкнення та вимкнення швидкості черевиків."
  components:
  - type: InstantAction
    itemIconStyle: BigItem
    event: !type:ToggleActionEvent

- type: entity
  parent: ClothingShoesBase
  id: ClothingShoesBootsMoon
  name: "місячні чоботи"
  description: "Спеціальні антигравітаційні черевики, розроблені зі спеціальною сумішшю гелю місячної породи. Постачаються з Нідерландів."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Boots/moonboots.rsi
    layers:
    - state: icon
  - type: Clothing
    sprite: Clothing/Shoes/Boots/moonboots.rsi
  - type: AntiGravityClothing
  - type: StaticPrice
    price: 75
  - type: Tag
    tags: [ ]

- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesHighHeels
  name: "високі підбори"
  description: "Ці підбори допоможуть вам промайнути на станцію, наче по подіуму."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Misc/high_heels.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Misc/high_heels.rsi
  - type: HideLayerClothing
    force: true
    slots:
    - LFoot
    - RFoot
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepHighHeels

- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesHighHeelsLong
  name: "довгі високі підбори"
  description: "Вбити будинок черевиками!"
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Misc/long_high_heels.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Misc/long_high_heels.rsi
  - type: HideLayerClothing
    force: true
    slots:
    - LFoot
    - RFoot
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepHighHeels

- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesFlats
  name: "квартири"
  description: "Модні та комфортні квартири."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Misc/flats.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Misc/flats.rsi
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepHighHeels

- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesSkater
  name: "черевики для скейтерів"
  description: "Пара широких черевиків на товстій підошві. Призначені для катання на ковзанах."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Misc/skater.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Misc/skater.rsi

- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesSandals
  name: "босоніжки"
  description: "Пара простих, але стильних босоніжок."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Sandals/sandals.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Sandals/sandals.rsi

- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesSandalsFlipFlops
  name: "шльопанці"
  description: "Пара шльопанців. Для тих, хто не боїться показати трохи щиколотки."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Sandals/flipflops.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Sandals/flipflops.rsi

- type: entity
  parent: ClothingShoesSandalsFlipFlops
  id: ClothingShoesSandalsFlipFlopsAlt
  description: "Пара шльопанців. Колір ремінців світліший, ніж у звичайних шльопанців."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Sandals/flipflops_alt.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Sandals/flipflops_alt.rsi
