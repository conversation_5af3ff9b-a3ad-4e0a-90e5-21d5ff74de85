- type: gameMap
  id: Edge
  mapName: Едж
  mapPath: /Maps/edge.yml
  minPlayers: 10
  stations:
    Edge:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/DeltaV/NTES_Delta.yml
        - type: StationNameSetup
          mapNameTemplate: '{0} Edge Station {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: 'DV'
        - type: StationJobs
          overflowJobs:
            - Passenger
          availableJobs:
          #service
            Captain: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1]
            NanotrasenRepresentative: [ 1, 1 ]
            Magistrate: [ 1, 1 ]
            AdministrativeAssistant: [ 1, 1 ]
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 1, 2 ]
            Botanist: [ 2, 3 ]
            Boxer: [ 2, 3 ]
            Chef: [ 2, 3 ]
            Clown: [ 1, 1 ]
            Lawyer: [ 1, 1 ]
            Reporter: [ 0, 1 ]
            Musician: [ 1, 1 ]
            Janitor: [ 1, 3 ]
            Mime: [ 1, 1 ]
          #engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 1, 2 ]
            StationEngineer: [ 3, 5 ]
            TechnicalAssistant: [ 2, 5 ]
          #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            Chemist: [ 1, 2 ]
            Paramedic: [ 1, 2 ]
            Psychologist: [ 1, 1 ]
            MedicalDoctor: [ 3, 5 ]
            MedicalIntern: [ 2, 4 ]
          #science
            ResearchDirector: [ 1, 1 ]
            Chaplain: [ 1, 1 ]
            ForensicMantis: [ 1, 1 ]
            Scientist: [ 2, 4 ]
            ResearchAssistant: [ 2, 4 ]
            Borg: [ 1, 2 ]
          #security
            HeadOfSecurity: [ 1, 1 ]
            Warden: [ 1, 1 ]
            Detective: [ 1, 1 ]
            SecurityOfficer: [ 2, 4 ]
            SecurityCadet: [ 1, 4 ]
          #supply
            Quartermaster: [ 1, 1 ]
            MailCarrier: [ 1, 2 ]
            SalvageSpecialist: [ 2, 3 ]
            CargoTechnician: [ 2, 4 ]
          #civilian
            Passenger: [ -1, -1 ]
