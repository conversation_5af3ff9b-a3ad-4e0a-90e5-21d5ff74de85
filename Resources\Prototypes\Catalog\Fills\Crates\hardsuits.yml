# Engineering
- type: entity
  id: CrateEngineeringFotiaHardsuit
  parent: CrateEngineering
  name: "ящик з костюмом fotia"
  description: "Містить один бронежилет HpI-19t \"Fotia\". Потрібен інженерний доступ, щоб відкрити."
  components:
  - type: StorageFill
    contents:
    - id: ClothingOuterHardsuitAtmos

- type: entity
  id: CrateEngineeringLampsiHardsuit
  parent: CrateEngineering
  name: "ящик з важким костюмом lampsi"
  description: "Містить один хардсьют HpI-19r \"Lampsi\". Костюм не пофарбований. Потрібен інженерний доступ, щоб відкрити."
  components:
  - type: StorageFill
    contents:
    - id: ClothingOuterHardsuitEngineeringUnpainted

# Logistics
- type: entity
  id: CrateLogisticsKritiHardsuit
  parent: CrateGenericSteel
  name: "ящик з важким костюмом kriti"
  description: "Містить один хардсьют HpI-20s \"Kriti\"."
  components:
  - type: StorageFill
    contents:
    - id: ClothingOuterHardsuitSpatio

- type: entity
  id: CrateLogisticsLavrionHardsuit
  parent: CrateGenericSteel
  name: "ящик з важким костюмом lavrion"
  description: "Містить один хардсьют HpI-20a \"Lavrion\"."
  components:
  - type: StorageFill
    contents:
    - id: ClothingOuterHardsuitSalvage

# Security
- type: entity
  id: CrateSecurityShanlinTacsuit
  parent: CrateSecgear
  name: "ящик для костюмів shanlin"
  description: "Містить один костюм CSA-51a \"Shanlin\". Для відкриття потрібен доступ до системи безпеки."
  components:
  - type: StorageFill
    contents:
    - id: ClothingOuterHardsuitShanlinUnpainted

- type: entity
  id: CrateSecurityShiweiTacsuit
  parent: CrateSecgear
  name: "ящик костюма шивей"
  description: "Містить один костюм CSA-54UA \"Shiwei\". Для відкриття потрібен доступ до системи безпеки."
  components:
  - type: StorageFill
    contents:
    - id: ClothingOuterHardsuitShiweiUnpainted

- type: entity
  id: CrateSecurityGuanYuTacsuit
  parent: CrateSecgear
  name: "ящик для костюма гуань-ю"
  description: "Містить один костюм CSA-80UA \"Гуань-Ю\". Для відкриття потрібен доступ до системи безпеки."
  components:
  - type: StorageFill
    contents:
    - id: ClothingOuterHardsuitJuggernautReverseEngineered

- type: entity
  id: CrateSecurityBaghaturTacsuit
  parent: CrateSecgear
  name: "ящик з тактичним костюмом baghatur"
  description: "Містить один костюм FPA-83s \"Багатур\". Для відкриття потрібен доступ служби безпеки."
  components:
  - type: StorageFill
    contents:
    - id: ClothingOuterHardsuitCombatStandard

- type: entity
  id: CrateSecuritySuldeTacsuit
  parent: CrateSecgear
  name: "ящик з тактичним костюмом sulde"
  description: "Містить один костюм FPA-93 - \"Sulde Mk.II\". Для відкриття потрібен доступ служби безпеки."
  components:
  - type: StorageFill
    contents:
    - id: ClothingOuterHardsuitCombatRiot

- type: entity
  id: CrateSecurityTsagaanTacsuit
  parent: CrateSecgear
  name: "ящик з тактичним костюмом tsagaan"
  description: "Містить один костюм FPA-86 - \"Tsagaan Mk.II\". Для відкриття потрібен доступ служби безпеки."
  components:
  - type: StorageFill
    contents:
    - id: ClothingOuterHardsuitCombatMedical
