﻿- type: entity
  id: ActionFlashRune
  name: "Руна Спалаху"
  description: "Викликає руну, яка спалахне при використанні."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    useDelay: 10
    itemIconStyle: BigAction
    icon:
      sprite: Objects/Magic/magicactions.rsi
      state: spell_default
    event: !type:InstantSpawnSpellEvent
      prototype: FlashRune

- type: entity
  id: ActionExplosionRune
  name: "Руна Вибуху"
  description: "Викликає руну, яка вибухає при використанні."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    useDelay: 20
    itemIconStyle: BigAction
    icon:
      sprite: Objects/Magic/magicactions.rsi
      state: spell_default
    event: !type:InstantSpawnSpellEvent
      prototype: ExplosionRune

- type: entity
  id: ActionIgniteRune
  name: "Руна Запалення"
  description: "Викликає руну, яка запалюється при використанні."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    useDelay: 15
    itemIconStyle: BigAction
    icon:
      sprite: Objects/Magic/magicactions.rsi
      state: spell_default
    event: !type:InstantSpawnSpellEvent
      prototype: IgniteRune

- type: entity
  id: ActionStunRune
  name: "Руна Оглушення"
  description: "Викликає руну, яка оглушує при використанні."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    useDelay: 10
    itemIconStyle: BigAction
    icon:
      sprite: Objects/Magic/magicactions.rsi
      state: spell_default
    event: !type:InstantSpawnSpellEvent
      prototype: StunRune
