- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuit
  name: "плазмовий екокостюм"
  description: "Спеціальний захисний костюм, який дозволяє плазмовим формам життя безпечно існувати в середовищі, насиченому киснем."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/plain.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/plain.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitAtmos
  name: "атмосферний екокостюм"
  description: "Герметичний костюм, призначений для використання плазмоменами, які працюють в якості атмосферних техніків."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/atmos.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/atmos.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitCargo
  name: "вантажний технологічний екокостюм"
  description: "Екокостюм, який використовують вантажні техніки Plasmamen."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/cargo.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/cargo.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitCaptain
  name: "капітанський скафандр"
  description: "Це блакитний екокостюм із золотими знаками, що позначають звання \"капітан\"."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/captain.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/captain.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitChiefEngineer
  name: "екокостюм головного інженера"
  description: "Герметичний костюм, призначений для використання плазмаменами, достатньо божевільними, щоб отримати звання \"головного інженера\"."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/ce.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/ce.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitChaplain
  name: "костюм капелана"
  description: "Екокостюм, спеціально розроблений для найблагочестивіших плазмаменів."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/chaplain.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/chaplain.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitChef
  name: "костюм шеф-кухаря"
  description: "Білий енвайронментальний костюм Plasmaman, призначений для кулінарних практик. Здавалося б, навіщо представнику виду, якому не потрібно їсти, ставати шеф-кухарем?"
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/chef.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/chef.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitChemist
  name: "хімічний екокостюм"
  description: "Екокостюм Plasmaman, розроблений для хіміків."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/chemist.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/chemist.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitClown
  name: "клоунський екокостюм"
  description: "ГУДОК!"
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/clown.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/clown.rsi
  - type: Tag
    tags:
    - ClownSuit

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitCMO
  name: "костюм головного лікаря"
  description: "Це захисний костюм, який носять ті, хто має досвід роботи на посаді \"головного лікаря\"."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/cmo.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/cmo.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitEngineering
  name: "інженерний екокостюм"
  description: "Герметичний костюм, призначений для використання плазмаменами, які працюють інженерами, звичайні фіолетові смуги замінені на інженерні помаранчеві."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/engineering.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/engineering.rsi

- type: entity
  parent: ClothingUniformEnvirosuitEnviroslacksColorOrange
  id: ClothingUniformEnvirosuitDetective
  name: "екокостюм детектива"
  description: "Улюблений проект особливо розкішного Плазмамена, цей нестандартний костюм був модифікований компанією Nanotrasen для своїх детективів."

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitHoP
  name: "костюм керівника відділу кадрів"
  description: "Це костюм, який носить людина, що працює на посаді \"Начальник відділу кадрів\"."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/hop.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/hop.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitHoS
  name: "костюм керівника служби безпеки"
  description: "Захисний костюм Plasmaman, створений для тих небагатьох, хто прагне зайняти посаду керівника служби безпеки."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/hos.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/hos.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitHydroponics
  name: "гідропонічний екокостюм"
  description: "Зелено-синій екокостюм, призначений для захисту плазмалеми від незначних травм, пов'язаних з рослинами."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/hydroponics.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/hydroponics.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitJanitor
  name: "екокостюм двірника"
  description: "Сіро-фіолетовий екокостюм, призначений для прибиральників Plasmamen."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/janitor.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/janitor.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitAncientVoid
  name: "Екокостюм NTSRA"
  description: "Виготовлений з модифікованого вакцинального костюма NTSRA, цей не придатний для космосу костюм став першим розробленим NanoTrasen енвайронментальним костюмом для Plasmamen."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/ancientvoid.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/ancientvoid.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitMedicalDoctor
  name: "аерокостюм лікаря"
  description: "Костюм, розроблений для лікарів станції, які більше працюють у плазмі."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/medical.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/medical.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitGenetics
  name: "генетичний екокостюм"
  description: "Екокостюм Plasmaman, розроблений для генетиків."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/genetics.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/genetics.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitMime
  name: "екокостюм міма"
  description: "Вона не дуже барвиста."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/mime.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/mime.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitParamedic
  name: "екокостюм парамедика"
  description: "Костюм, розроблений для парамедиків станції \"Плазмамен\"."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/paramedic.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/paramedic.rsi

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitPrisoner
  name: "екокостюм для в'язнів"
  description: "Помаранчевий екокостюм, що ідентифікує та захищає злочинця Плазмамена."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ff8300"
    - state: plaintop-icon
      color: "#ff8300"
    - state: accentprisoner-icon
      color: "#404040"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ff8300"
      - state: plaintop-inhand-left
        color: "#ff8300"
      - state: accentprisoner-inhand-left
        color: "#404040"
      right:
      - state: inhand-right
        color: "#ff8300"
      - state: plaintop-inhand-right
        color: "#ff8300"
      - state: accentprisoner-inhand-right
        color: "#404040"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ff8300"
      - state: plaintop-equipped-INNERCLOTHING
        color: "#ff8300"
      - state: accentprisoner-equipped-INNERCLOTHING
        color: "#404040"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#404040"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"
  - type: SuitSensor
    controlsLocked: true
    randomMode: false
    mode: SensorCords
  - type: Tag
    tags:
    - ClothMade
    - WhitelistChameleon
    - PlasmamanSafe
    - PrisonUniform

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitResearchDirector
  name: "екокостюм наукового директора"
  description: "Це костюм, який носять ті, хто володіє ноу-хау, щоб досягти позиції \"Містагога\"."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/rd.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/rd.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitRoboticist
  name: "екокостюм робототехніка"
  description: "Екокостюм Plasmaman, розроблений для робототехніків."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/roboticist.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/roboticist.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitSalvage
  name: "рятувальний екокостюм"
  description: "Герметичний костюм кольору хакі, розроблений для рятувальних операцій Plasmamen."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/salvage.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/salvage.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitScientist
  name: "науковий екокостюм"
  description: "Екокостюм Plasmaman, розроблений для науковців."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/scientist.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/scientist.rsi

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitSec
  name: "захисний екокостюм"
  description: "Захисний костюм Plasmaman, розроблений для співробітників служби безпеки."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#8f3132"
    - state: accent2-icon
      color: "#2e2e2e"
    - state: accenthighlight-icon
      color: "#313e5a"
    - state: clip-icon
      color: "#730000"
    - state: clip_right-icon
      color: "#313e5a"
    - state: pants-icon
      color: "#2e2e2e"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#8f3132"
      - state: accent2-inhand-left
        color: "#2e2e2e"
      - state: accenthighlight-inhand-left
        color: "#313e5a"
      - state: clip-inhand-left
        color: "#730000"
      - state: clip_right-inhand-left
        color: "#313e5a"
      - state: pants-inhand-left
        color: "#2e2e2e"
      right:
      - state: inhand-right
        color: "#8f3132"
      - state: accent2-inhand-right
        color: "#2e2e2e"
      - state: accenthighlight-inhand-right
        color: "#313e5a"
      - state: clip-inhand-right
        color: "#730000"
      - state: clip_right-inhand-right
        color: "#313e5a"
      - state: pants-inhand-right
        color: "#2e2e2e"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#8f3132"
      - state: accent2-equipped-INNERCLOTHING
        color: "#2e2e2e"
      - state: accenthighlight-equipped-INNERCLOTHING
        color: "#313e5a"
      - state: clip-equipped-INNERCLOTHING
        color: "#730000"
      - state: clip_right-equipped-INNERCLOTHING
        color: "#313e5a"
      - state: pants-equipped-INNERCLOTHING
        color: "#2e2e2e"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#732829"
      - state: shoesdark-equipped-INNERCLOTHING
        color: "#2e2e2e"
      - state: soles-equipped-INNERCLOTHING
        color: "#7a7a7a"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitSecBlue
  name: "синій захисний екокостюм"
  description: "Прохолодна блакитна енвайронментальна сорочка поверх вугільних штанів, для спокійного та зібраного офіцера Plasmaman."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#b9c1d9"
    - state: accent-icon
      color: "#2e2e2e"
    - state: accent3_chestonly-icon
      color: "#36476b"
    - state: accenthighlight-icon
      color: "#313e5a"
    - state: clip-icon
      color: "#860000"
    - state: clip_right-icon
      color: "#313e5a"
    - state: pants-icon
      color: "#232938"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#b9c1d9"
      - state: accent-inhand-left
        color: "#2e2e2e"
      - state: accent3_chestonly-inhand-left
        color: "#36476b"
      - state: accenthighlight-inhand-left
        color: "#313e5a"
      - state: clip-inhand-left
        color: "#860000"
      - state: clip_right-inhand-left
        color: "#313e5a"
      - state: pants-inhand-left
        color: "#232938"
      right:
      - state: inhand-right
        color: "#b9c1d9"
      - state: accent-inhand-right
        color: "#2e2e2e"
      - state: accent3_chestonly-inhand-right
        color: "#36476b"
      - state: accenthighlight-inhand-right
        color: "#313e5a"
      - state: clip-inhand-right
        color: "#860000"
      - state: clip_right-inhand-right
        color: "#313e5a"
      - state: pants-inhand-right
        color: "#232938"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#b9c1d9"
      - state: accentalt_noback-equipped-INNERCLOTHING
        color: "#2e2e2e"
      - state: accent3_chestonly-equipped-INNERCLOTHING
        color: "#36476b"
      - state: accenthighlight-equipped-INNERCLOTHING
        color: "#313e5a"
      - state: clip-equipped-INNERCLOTHING
        color: "#860000"
      - state: clip_right-equipped-INNERCLOTHING
        color: "#313e5a"
      - state: pants-equipped-INNERCLOTHING
        color: "#232938"
      - state: backaccent-equipped-INNERCLOTHING
        color: "#36476b"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#36476b"
      - state: shoesdark-equipped-INNERCLOTHING
        color: "#2e2e2e"
      - state: soles-equipped-INNERCLOTHING
        color: "#7a7a7a"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitSecGrey
  name: "сірий захисний екокостюм"
  description: "Світло-сірі екологічні куртки з яскраво-червоними вставками для відданих та оперативних співробітників служби безпеки."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#7e7e7e"
    - state: plaintop-icon
      color: "#7e7e7e"
    - state: accent-icon
      color: "#333333"
    - state: accenthighlight-icon
      color: "#313e5a"
    - state: tie-icon
      color: "#a61d1d"
    - state: clip-icon
      color: "#860000"
    - state: clip_right-icon
      color: "#313e5a"
    - state: pants-icon
      color: "#333333"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#7e7e7e"
      - state: plaintop-inhand-left
        color: "#7e7e7e"
      - state: accent-inhand-left
        color: "#333333"
      - state: accenthighlight-inhand-left
        color: "#313e5a"
      - state: tie-inhand-left
        color: "#a61d1d"
      - state: clip-inhand-left
        color: "#860000"
      - state: clip_right-inhand-left
        color: "#313e5a"
      - state: pants-inhand-left
        color: "#333333"
      right:
      - state: inhand-right
        color: "#7e7e7e"
      - state: plaintop-inhand-right
        color: "#7e7e7e"
      - state: accent-inhand-right
        color: "#333333"
      - state: accenthighlight-inhand-right
        color: "#313e5a"
      - state: tie-inhand-right
        color: "#a61d1d"
      - state: clip-inhand-right
        color: "#860000"
      - state: clip_right-inhand-right
        color: "#313e5a"
      - state: pants-inhand-right
        color: "#333333"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#7e7e7e"
      - state: plaintop-equipped-INNERCLOTHING
        color: "#7e7e7e"
      - state: accentalt-equipped-INNERCLOTHING
        color: "#333333"
      - state: accenthighlight-equipped-INNERCLOTHING
        color: "#313e5a"
      - state: tie-equipped-INNERCLOTHING
        color: "#a61d1d"
      - state: clip-equipped-INNERCLOTHING
        color: "#860000"
      - state: clip_right-equipped-INNERCLOTHING
        color: "#313e5a"
      - state: cuffs-equipped-INNERCLOTHING
        color: "#a11a1a"
      - state: cuffs_upper-equipped-INNERCLOTHING
        color: "#c92323"
      - state: pants-equipped-INNERCLOTHING
        color: "#333333"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#a61d1d"
      - state: shoesdark-equipped-INNERCLOTHING
        color: "#333333"
      - state: soles-equipped-INNERCLOTHING
        color: "#7a7a7a"

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitVirology
  name: "вірусологічний екокостюм"
  description: "Костюм, який носять найбезпечніші люди на станції, ті, хто має повний імунітет до монстрів, яких вони створюють."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/virology.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/virology.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitWarden
  name: "костюм наглядача"
  description: "Захисний костюм Plasmaman, розроблений для наглядача, з білими смужками, щоб відрізняти його від інших членів охорони."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/warden.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/warden.rsi

- type: entity
  parent: UnsensoredClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitOperative
  name: "оперативний екокостюм"
  description: "Зловісний на вигляд екокостюм для найелітніших кістлявих оперативників."
  components:
    - type: Sprite
      sprite: Clothing/Uniforms/Envirosuits/tacticool.rsi
    - type: Clothing
      sprite: Clothing/Uniforms/Envirosuits/tacticool.rsi
    - type: LimitedCharges
      maxCharges: 6
      charges: 6

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitTacticool
  name: "екокостюм tacticool"
  description: "Зловісний на вигляд екокостюм для найміцніших оперативників."
  components:
    - type: Sprite
      sprite: Clothing/Uniforms/Envirosuits/tacticool.rsi
    - type: Clothing
      sprite: Clothing/Uniforms/Envirosuits/tacticool.rsi
    # Too cool for sensors to be on
    - type: SuitSensor
      randomMode: false
      mode: SensorOff

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitCentcomAgent
  name: "Аерокостюм агента CentCom"
  description: "Екокостюм, пошитий для юридичної команди CentCom. Пахне горілою кавою."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/centcom_agent.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/centcom_agent.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitCentcomOfficial
  name: "Захисний костюм чиновника CentCom"
  description: "Це костюм, який носять співробітники CentCom."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/centcom_official.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/centcom_official.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitCentcomOfficer
  name: "Аерокостюм офіцера CentCom"
  description: "Це - захисний костюм, який носять офіцери Центрального командування."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/centcom_officer.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/centcom_officer.rsi

- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitBlueshield
  name: "захисний костюм Блюшильда"
  description: "Екокостюм, розроблений для плазмоменів, які виконують роль Блакитного Щита."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/blueshield_officer.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/blueshield_officer.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.95
        Heat: 0.95

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitCourier
  name: "костюм кур'єра - екокостюм"
  description: "Екокостюм, пошитий для кур'єра."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#4a281f"
    - state: accent2-icon
      color: "#c2911e"
    - state: clip-icon
      color: "#c2911e"
    - state: clip_right-icon
      color: "#c2911e"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#4a281f"
      - state: accent2-inhand-left
        color: "#c2911e"
      - state: clip-inhand-left
        color: "#c2911e"
      - state: clip_right-inhand-left
        color: "#c2911e"
      right:
      - state: inhand-right
        color: "#4a281f"
      - state: accent2-inhand-right
        color: "#c2911e"
      - state: clip-inhand-right
        color: "#c2911e"
      - state: clip_right-inhand-right
        color: "#c2911e"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#4a281f"
      - state: accent2-equipped-INNERCLOTHING
        color: "#c2911e"
      - state: clip-equipped-INNERCLOTHING
        color: "#c2911e"
      - state: clip_right-equipped-INNERCLOTHING
        color: "#c2911e"
      - state: loweraccent2-equipped-INNERCLOTHING
        color: "#c2911e"
      - state: soles-equipped-INNERCLOTHING
        color: "#c2911e"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitMailCarrier
  name: "екокостюм поштового перевізника"
  description: "Екокостюм, скроєний для листоноші. Кольоровий візерунок доводить пітбулів до сказу."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#394dc6"
    - state: accent-icon
      color: "#d82927"
    - state: accent2_chestonly-icon
      color: "#dcdcdc"
    - state: clip-icon
      color: "#dcdcdc"
    - state: clip_right-icon
      color: "#c2c2c2"
    - state: belt-icon
      color: "#cfcfcf"
    - state: beltbuckle_small-icon
      color: "#f0990c"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#394dc6"
      - state: accent-inhand-left
        color: "#d82927"
      - state: accent2_chestonly-inhand-left
        color: "#dcdcdc"
      - state: clip-inhand-left
        color: "#dcdcdc"
      - state: clip_right-inhand-left
        color: "#c2c2c2"
      - state: belt-inhand-left
        color: "#cfcfcf"
      - state: beltbuckle_small-inhand-left
        color: "#f0990c"
      right:
      - state: inhand-right
        color: "#394dc6"
      - state: accent-inhand-right
        color: "#d82927"
      - state: accent2_chestonly-inhand-right
        color: "#dcdcdc"
      - state: clip-inhand-right
        color: "#dcdcdc"
      - state: clip_right-inhand-right
        color: "#c2c2c2"
      - state: belt-inhand-right
        color: "#cfcfcf"
      - state: beltbuckle_small-inhand-right
        color: "#f0990c"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#394dc6"
      - state: accentalt_noback-equipped-INNERCLOTHING
        color: "#d82927"
      - state: accent2_chestonly-equipped-INNERCLOTHING
        color: "#dcdcdc"
      - state: clip-equipped-INNERCLOTHING
        color: "#dcdcdc"
      - state: clip_right-equipped-INNERCLOTHING
        color: "#c2c2c2"
      - state: belt-equipped-INNERCLOTHING
        color: "#cfcfcf"
      - state: beltbuckle_small-equipped-INNERCLOTHING
        color: "#f0990c"
      - state: backaccent-equipped-INNERCLOTHING
        color: "#dcdcdc"
      - state: loweraccent2-equipped-INNERCLOTHING
        color: "#dcdcdc"
      - state: soles-equipped-INNERCLOTHING
        color: "#dcdcdc"
  - type: ClothingAddFaction
    faction: Mailman

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitMusician
  name: "костюм музиканта"
  description: "Костюм, в якому можна грати музику."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#3c335b"
    - state: plaintop-icon
      color: "#3c335b"
    - state: accent2-icon
      color: "#f3f5f4"
    - state: pin-icon
      color: "#db2525"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#3c335b"
      - state: accent2-inhand-left
        color: "#f3f5f4"
      - state: pin-inhand-left
        color: "#db2525"
      right:
      - state: inhand-right
        color: "#3c335b"
      - state: accent2-inhand-right
        color: "#f3f5f4"
      - state: pin-inhand-right
        color: "#db2525"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#3c335b"
      - state: accent2-equipped-INNERCLOTHING
        color: "#f3f5f4"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#f3f5f4"
      - state: pin-equipped-INNERCLOTHING
        color: "#db2525"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitReporter
  name: "репортерський екокостюм"
  description: "Костюм для плазмомена, орієнтованого на новини."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#112334"
    - state: accent2-icon
      color: "#79121b"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#112334"
      - state: accent2-inhand-left
        color: "#79121b"
      right:
      - state: inhand-right
        color: "#112334"
      - state: accent2-inhand-right
        color: "#79121b"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#112334"
      - state: accent2-equipped-INNERCLOTHING
        color: "#79121b"
      - state: loweraccent2-equipped-INNERCLOTHING
        color: "#79121b"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitGladiator
  name: "гладіаторський комбінезон"
  description: "Створено для кровожерливих плазмоїдів."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#dab13b"
    - state: accent-icon
      color: "#a349a4"
    - state: corneraccent-icon
      color: "#a349a4"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#dab13b"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: corneraccent-inhand-left
        color: "#a349a4"
      right:
      - state: inhand-right
        color: "#dab13b"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: corneraccent-inhand-right
        color: "#a349a4"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#dab13b"
      - state: accent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: loweraccent2-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: corneraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: soles-equipped-INNERCLOTHING
        color: "#a349a4"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitMantis
  name: "екокостюм мантіса"
  description: "Полювання на псіоніків у безпеці цього екокостюма."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#46566d"
    - state: pants-icon
      color: "#7d2322"
    - state: belt-icon
      color: "#51321a"
    - state: beltbuckle-icon
      color: "#dcbb60"
    - state: accent-icon
      color: "#d4af48"
    - state: buttons-icon
      color: "#997d30"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#46566d"
      - state: pants-inhand-left
        color: "#7d2322"
      - state: belt-inhand-left
        color: "#51321a"
      - state: beltbuckle-inhand-left
        color: "#dcbb60"
      - state: accent-inhand-left
        color: "#d4af48"
      - state: buttons-inhand-left
        color: "#997d30"
      right:
      - state: inhand-right
        color: "#46566d"
      - state: pants-inhand-right
        color: "#7d2322"
      - state: belt-inhand-right
        color: "#51321a"
      - state: beltbuckle-inhand-right
        color: "#dcbb60"
      - state: accent-inhand-right
        color: "#d4af48"
      - state: buttons-inhand-right
        color: "#997d30"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#46566d"
      - state: pants-equipped-INNERCLOTHING
        color: "#7d2322"
      - state: belt-equipped-INNERCLOTHING
        color: "#51321a"
      - state: beltbuckle-equipped-INNERCLOTHING
        color: "#dcbb60"
      - state: accentalt-equipped-INNERCLOTHING
        color: "#d4af48"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#d4af48"
      - state: buttons-equipped-INNERCLOTHING
        color: "#997d30"
      - state: soles-equipped-INNERCLOTHING
        color: "#d4af48"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitSafari
  name: "сафарі-костюм"
  description: "Ідеально підходить для екскурсії в джунглі."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#d3b986"
    - state: accent-icon
      color: "#a349a4"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#d3b986"
      - state: accent-inhand-left
        color: "#a349a4"
      right:
      - state: inhand-right
        color: "#d3b986"
      - state: accent-inhand-right
        color: "#a349a4"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#d3b986"
      - state: accent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitMartialGi
  name: "екокостюм gi"
  description: "Струмливий екокостюм, створений спеціально для бойових мистецтв, який не обмежує вашу рухливість."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ffffff"
    - state: accent-icon
      color: "#3b3b3b"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffffff"
      - state: accent-inhand-left
        color: "#3b3b3b"
      right:
      - state: inhand-right
        color: "#ffffff"
      - state: accent-inhand-right
        color: "#3b3b3b"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ffffff"
      - state: accent-equipped-INNERCLOTHING
        color: "#3b3b3b"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#3b3b3b"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitQM
  name: "костюм офіцера логіста"
  description: "Герметичний костюм, розроблений для використання плазмаменами, які достатньо божевільні, щоб отримати звання \"Офіцер логістики\"."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#bb934b"
    - state: accent-icon
      color: "#ffc000"
    - state: accent3_chestonly-icon
      color: "#d08200"
    - state: clip-icon
      color: "#c0c0c0"
    - state: clip_right-icon
      color: "#a7a7a7"
    - state: pants-icon
      color: "#8a8a8a"
    - state: belt-icon
      color: "#6f6f6f"
    - state: beltbuckle-icon
      color: "#bfbfbf"
    - state: buttons-icon
      color: "#535353"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#bb934b"
      - state: accent-inhand-left
        color: "#ffc000"
      - state: accent3_chestonly-inhand-left
        color: "#d08200"
      - state: clip-inhand-left
        color: "#c0c0c0"
      - state: clip_right-inhand-left
        color: "#a7a7a7"
      - state: pants-inhand-left
        color: "#8a8a8a"
      - state: belt-inhand-left
        color: "#6f6f6f"
      - state: beltbuckle-inhand-left
        color: "#bfbfbf"
      - state: buttons-inhand-left
        color: "#535353"
      right:
      - state: inhand-right
        color: "#bb934b"
      - state: accent-inhand-right
        color: "#ffc000"
      - state: accent3_chestonly-inhand-right
        color: "#d08200"
      - state: clip-inhand-right
        color: "#c0c0c0"
      - state: clip_right-inhand-right
        color: "#a7a7a7"
      - state: pants-inhand-right
        color: "#8a8a8a"
      - state: belt-inhand-right
        color: "#6f6f6f"
      - state: beltbuckle-inhand-right
        color: "#bfbfbf"
      - state: buttons-inhand-right
        color: "#535353"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#bb934b"
      - state: accentalt-equipped-INNERCLOTHING
        color: "#ffc000"
      - state: accent3_chestonly-equipped-INNERCLOTHING
        color: "#d08200"
      - state: clip-equipped-INNERCLOTHING
        color: "#c0c0c0"
      - state: clip_right-equipped-INNERCLOTHING
        color: "#a7a7a7"
      - state: cuffs-equipped-INNERCLOTHING
        color: "#d08200"
      - state: cuffs_upper-equipped-INNERCLOTHING
        color: "#6e6e6e"
      - state: pants-equipped-INNERCLOTHING
        color: "#8a8a8a"
      - state: belt-equipped-INNERCLOTHING
        color: "#6f6f6f"
      - state: beltbuckle-equipped-INNERCLOTHING
        color: "#bfbfbf"
      - state: buttons-equipped-INNERCLOTHING
        color: "#535353"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#ffc000"
      - state: shoesdark-equipped-INNERCLOTHING
        color: "#828282"
      - state: soles-equipped-INNERCLOTHING
        color: "#ffc000"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitBoxing
  name: "боксерський аерокостюм"
  description: "Використовується боксерами Plasmamen."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#eeeeee"
    - state: accent3-icon
      color: "#a81818"
    - state: pants-icon
      color: "#a81818"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#eeeeee"
      - state: accent3-inhand-left
        color: "#a81818"
      - state: pants-inhand-left
        color: "#a81818"
      right:
      - state: inhand-right
        color: "#eeeeee"
      - state: accent3-inhand-right
        color: "#a81818"
      - state: pants-inhand-right
        color: "#a81818"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#eeeeee"
      - state: accent3-equipped-INNERCLOTHING
        color: "#a81818"
      - state: pants-equipped-INNERCLOTHING
        color: "#a81818"
      - state: loweraccent2-equipped-INNERCLOTHING
        color: "#eeeeee"
      - state: soles-equipped-INNERCLOTHING
        color: "#eeeeee"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitAdminAssistant
  name: "костюм адміністративного асистента"
  description: "Костюм, який носить адміністративна асистентка. Пахне горілою кавою."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ffffff"
    - state: plaintop-icon
      color: "#ffffff"
    - state: pants-icon
      color: "#313131"
    - state: belt-icon
      color: "#4d4d4d"
    - state: accent-icon
      color: "#315266"
    - state: tie-icon
      color: "#315266"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffffff"
      - state: plaintop-inhand-left
        color: "#ffffff"
      - state: pants-inhand-left
        color: "#313131"
      - state: belt-inhand-left
        color: "#4d4d4d"
      - state: accent-inhand-left
        color: "#315266"
      - state: tie-inhand-left
        color: "#315266"
      right:
      - state: inhand-right
        color: "#ffffff"
      - state: plaintop-inhand-right
        color: "#ffffff"
      - state: pants-inhand-right
        color: "#313131"
      - state: belt-inhand-right
        color: "#4d4d4d"
      - state: accent-inhand-right
        color: "#315266"
      - state: tie-inhand-right
        color: "#315266"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ffffff"
      - state: plaintop-equipped-INNERCLOTHING
        color: "#ffffff"
      - state: pants-equipped-INNERCLOTHING
        color: "#313131"
      - state: belt-equipped-INNERCLOTHING
        color: "#4d4d4d"
      - state: accent-equipped-INNERCLOTHING
        color: "#315266"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#315266"
      - state: tie-equipped-INNERCLOTHING
        color: "#315266"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitBlackPink
  name: "чорно-рожевий екокостюм"
  description: "Чорно-рожевий екокостюм у вашому регіоні!"
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#292929"
    - state: plaintop-icon
      color: "#292929"
    - state: accent-icon
      color: "#f4a1b7"
    - state: heart-icon
      color: "#f4a1b7"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#292929"
      - state: plaintop-inhand-left
        color: "#292929"
      - state: accent-inhand-left
        color: "#f4a1b7"
      - state: heart-inhand-left
        color: "#f4a1b7"
      right:
      - state: inhand-right
        color: "#292929"
      - state: plaintop-inhand-right
        color: "#292929"
      - state: accent-inhand-right
        color: "#f4a1b7"
      - state: heart-inhand-right
        color: "#f4a1b7"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#292929"
      - state: plaintop-equipped-INNERCLOTHING
        color: "#292929"
      - state: accent-equipped-INNERCLOTHING
        color: "#f4a1b7"
      - state: heart-equipped-INNERCLOTHING
        color: "#f4a1b7"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#f4a1b7"
      - state: soles-equipped-INNERCLOTHING
        color: "#f4a1b7"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitBlackPinkAlt
  name: "чорно-рожевий екокостюм"
  suffix: Alternative
  description: "Чорно-рожевий екокостюм у вашому регіоні!"
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#f4a1b7"
    - state: plaintop-icon
      color: "#f4a1b7"
    - state: accent-icon
      color: "#292929"
    - state: heart-icon
      color: "#292929"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#f4a1b7"
      - state: plaintop-inhand-left
        color: "#f4a1b7"
      - state: accent-inhand-left
        color: "#292929"
      - state: heart-inhand-left
        color: "#292929"
      right:
      - state: inhand-right
        color: "#f4a1b7"
      - state: plaintop-inhand-right
        color: "#f4a1b7"
      - state: accent-inhand-right
        color: "#292929"
      - state: heart-inhand-right
        color: "#292929"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#f4a1b7"
      - state: plaintop-equipped-INNERCLOTHING
        color: "#f4a1b7"
      - state: accent-equipped-INNERCLOTHING
        color: "#292929"
      - state: heart-equipped-INNERCLOTHING
        color: "#292929"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#292929"
      - state: soles-equipped-INNERCLOTHING
        color: "#292929"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitTrans
  name: "транс-екокостюм"
  description: "Фірмовий екокостюм трансильванських плазмаменів."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ffffff"
    - state: plaintop-icon
      color: "#ffffff"
    - state: accent-icon
      color: "#5dd2ff"
    - state: heart-icon
      color: "#ffb0c0"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffffff"
      - state: plaintop-inhand-left
        color: "#ffffff"
      - state: accent-inhand-left
        color: "#5dd2ff"
      - state: heart-inhand-left
        color: "#ffb0c0"
      right:
      - state: inhand-right
        color: "#ffffff"
      - state: plaintop-inhand-right
        color: "#ffffff"
      - state: accent-inhand-right
        color: "#5dd2ff"
      - state: heart-inhand-right
        color: "#ffb0c0"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ffffff"
      - state: plaintop-equipped-INNERCLOTHING
        color: "#ffffff"
      - state: accentalt-equipped-INNERCLOTHING
        color: "#5dd2ff"
      - state: heart-equipped-INNERCLOTHING
        color: "#ffb0c0"
      - state: cuffs-equipped-INNERCLOTHING
        color: "#ffb0c0"
      - state: loweraccent2_top-equipped-INNERCLOTHING
        color: "#5dd2ff"
      - state: loweraccent2_bottom-equipped-INNERCLOTHING
        color: "#ffb0c0"
      - state: soles-equipped-INNERCLOTHING
        color: "#5dd2ff"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitPrisonGuard
  name: "костюм тюремного охоронця"
  description: "Зручний, міцний, івіброкостюм, створений для забезпечення комфорту та безпеки персоналу в'язниці Плазмамен."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#d76b00"
    - state: accent3-icon
      color: "#363636"
    - state: accenthighlight-icon
      color: "#313e5a"
    - state: clip-icon
      color: "#860000"
    - state: clip_right-icon
      color: "#313e5a"
    - state: pants-icon
      color: "#363636"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#d76b00"
      - state: accent3-inhand-left
        color: "#363636"
      - state: accenthighlight-inhand-left
        color: "#313e5a"
      - state: clip-inhand-left
        color: "#860000"
      - state: clip_right-inhand-left
        color: "#313e5a"
      - state: pants-inhand-left
        color: "#363636"
      right:
      - state: inhand-right
        color: "#d76b00"
      - state: accent3-inhand-right
        color: "#363636"
      - state: accenthighlight-inhand-right
        color: "#313e5a"
      - state: clip-inhand-right
        color: "#860000"
      - state: clip_right-inhand-right
        color: "#313e5a"
      - state: pants-inhand-right
        color: "#363636"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#d76b00"
      - state: accent3-equipped-INNERCLOTHING
        color: "#363636"
      - state: accenthighlight-equipped-INNERCLOTHING
        color: "#313e5a"
      - state: clip-equipped-INNERCLOTHING
        color: "#860000"
      - state: clip_right-equipped-INNERCLOTHING
        color: "#313e5a"
      - state: pants-equipped-INNERCLOTHING
        color: "#363636"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#d76b00"
      - state: shoesdark-equipped-INNERCLOTHING
        color: "#363636"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitBrigmedic
  name: "костюм санітара"
  description: "Костюм для санітарів Plasmamen."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#486782"
    - state: accent-icon
      color: "#333333"
    - state: accent2_chestonly-icon
      color: "#3b3b3b"
    - state: accenthighlight-icon
      color: "#2f74b8"
    - state: buttons-icon
      color: "#b0bdca"
    - state: clip-icon
      color: "#860000"
    - state: clip_right-icon
      color: "#313e5a"
    - state: pants-icon
      color: "#3b3b3b"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#486782"
      - state: accent-inhand-left
        color: "#333333"
      - state: accent2_chestonly-inhand-left
        color: "#3b3b3b"
      - state: accenthighlight-inhand-left
        color: "#2f74b8"
      - state: buttons-inhand-left
        color: "#b0bdca"
      - state: clip-inhand-left
        color: "#860000"
      - state: clip_right-inhand-left
        color: "#313e5a"
      - state: pants-inhand-left
        color: "#3b3b3b"
      right:
      - state: inhand-right
        color: "#486782"
      - state: accent-inhand-right
        color: "#333333"
      - state: accent2_chestonly-inhand-right
        color: "#3b3b3b"
      - state: accenthighlight-inhand-right
        color: "#2f74b8"
      - state: buttons-inhand-right
        color: "#b0bdca"
      - state: clip-inhand-right
        color: "#860000"
      - state: clip_right-inhand-right
        color: "#313e5a"
      - state: pants-inhand-right
        color: "#3b3b3b"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#486782"
      - state: accentalt-equipped-INNERCLOTHING
        color: "#333333"
      - state: accent2_chestonly-equipped-INNERCLOTHING
        color: "#3b3b3b"
      - state: accenthighlight-equipped-INNERCLOTHING
        color: "#2f74b8"
      - state: buttons-equipped-INNERCLOTHING
        color: "#b0bdca"
      - state: clip-equipped-INNERCLOTHING
        color: "#860000"
      - state: clip_right-equipped-INNERCLOTHING
        color: "#313e5a"
      - state: cuffs-equipped-INNERCLOTHING
        color: "#bfcddb"
      - state: pants-equipped-INNERCLOTHING
        color: "#3b3b3b"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#9ca7b3"
      - state: shoesdark-equipped-INNERCLOTHING
        color: "#3b3b3b"
      - state: soles-equipped-INNERCLOTHING
        color: "#bfcddb"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitNanotrasenRepresentative
  name: "скафандр корпоративного представника"
  description: "Чорний скафандр, який носять офіційні особи."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#292929"
    - state: accent3_chestonly-icon
      color: "#266199"
    - state: accent2-icon
      color: "#ffce5b"
    - state: buttons-icon
      color: "#f3f5f4"
    - state: belt-icon
      color: "#87511b"
    - state: beltbuckle-icon
      color: "#969696"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#292929"
      - state: accent3_chestonly-inhand-left
        color: "#266199"
      - state: accent2-inhand-left
        color: "#ffce5b"
      - state: buttons-inhand-left
        color: "#f3f5f4"
      - state: belt-inhand-left
        color: "#87511b"
      - state: beltbuckle-inhand-left
        color: "#969696"
      right:
      - state: inhand-right
        color: "#292929"
      - state: accent3_chestonly-inhand-right
        color: "#266199"
      - state: accent2-inhand-right
        color: "#ffce5b"
      - state: buttons-inhand-right
        color: "#f3f5f4"
      - state: belt-inhand-right
        color: "#87511b"
      - state: beltbuckle-inhand-right
        color: "#969696"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#292929"
      - state: accent3_chestonly-equipped-INNERCLOTHING
        color: "#266199"
      - state: accent2-equipped-INNERCLOTHING
        color: "#ffce5b"
      - state: buttons-equipped-INNERCLOTHING
        color: "#f3f5f4"
      - state: cuffs-equipped-INNERCLOTHING
        color: "#ffce5b"
      - state: cuffs_upper-equipped-INNERCLOTHING
        color: "#0057a8"
      - state: belt-equipped-INNERCLOTHING
        color: "#87511b"
      - state: beltbuckle-equipped-INNERCLOTHING
        color: "#969696"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#266199"
      - state: soles-equipped-INNERCLOTHING
        color: "#ffce5b"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitMagistrate
  name: "судовий позов до магістрату"
  description: "Костюм, який вершить правосуддя."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ebebeb"
    - state: plaintop-icon
      color: "#ebebeb"
    - state: tie-icon
      color: "#333333"
    - state: tieclip-icon
      color: "#e6b952"
    - state: accent-icon
      color: "#ffce5b"
    - state: pants-icon
      color: "#292929"
    - state: belt-icon
      color: "#634737"
    - state: beltbuckle-icon
      color: "#ffce5b"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ebebeb"
      - state: plaintop-inhand-left
        color: "#ebebeb"
      - state: tie-inhand-left
        color: "#333333"
      - state: tieclip-inhand-left
        color: "#e6b952"
      - state: accent-inhand-left
        color: "#ffce5b"
      - state: pants-inhand-left
        color: "#292929"
      - state: belt-inhand-left
        color: "#634737"
      - state: beltbuckle-inhand-left
        color: "#ffce5b"
      right:
      - state: inhand-right
        color: "#ebebeb"
      - state: plaintop-inhand-right
        color: "#ebebeb"
      - state: tie-inhand-right
        color: "#333333"
      - state: tieclip-inhand-right
        color: "#e6b952"
      - state: accent-inhand-right
        color: "#ffce5b"
      - state: pants-inhand-right
        color: "#292929"
      - state: belt-inhand-right
        color: "#634737"
      - state: beltbuckle-inhand-right
        color: "#ffce5b"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ebebeb"
      - state: plaintop-equipped-INNERCLOTHING
        color: "#ebebeb"
      - state: tie-equipped-INNERCLOTHING
        color: "#333333"
      - state: tieclip-equipped-INNERCLOTHING
        color: "#e6b952"
      - state: accentalt-equipped-INNERCLOTHING
        color: "#ffce5b"
      - state: cuffs-equipped-INNERCLOTHING
        color: "#634737"
      - state: pants-equipped-INNERCLOTHING
        color: "#292929"
      - state: belt-equipped-INNERCLOTHING
        color: "#634737"
      - state: beltbuckle-equipped-INNERCLOTHING
        color: "#ffce5b"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#ffce5b"
      - state: soles-equipped-INNERCLOTHING
        color: "#634737"


# The Tortured Enviroslacks Department (Skubman's Version)
- type: entity
  parent: ClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitEnviroslacks
  name: "enviroslack"
  description: "Домашній проект особливо шикарного Плазмамена. Професіонал!"
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/enviroslacks.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/enviroslacks.rsi

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitEnviroslacksNegative
  name: "негативні екологічні наслідки"
  description: "Улюблений проект особливо шикарного Плазмамена, цей варіант має інвертовані кольори. Ошатний!"
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#3f3f3f"
    - state: plaintop-icon
      color: "#3f3f3f"
    - state: tie-icon
      color: "#a349a4"
    - state: accent-icon
      color: "#a349a4"
    - state: pants-icon
      color: "#f2f2f2"
    - state: belt-icon
      color: "#737373"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#3f3f3f"
      - state: plaintop-inhand-left
        color: "#3f3f3f"
      - state: tie-inhand-left
        color: "#a349a4"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: pants-inhand-left
        color: "#f2f2f2"
      - state: belt-inhand-left
        color: "#737373"
      right:
      - state: inhand-right
        color: "#3f3f3f"
      - state: plaintop-inhand-right
        color: "#3f3f3f"
      - state: tie-inhand-right
        color: "#a349a4"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: pants-inhand-right
        color: "#f2f2f2"
      - state: belt-inhand-right
        color: "#737373"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#3f3f3f"
      - state: plaintop-equipped-INNERCLOTHING
        color: "#3f3f3f"
      - state: tie-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: accentalt-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: pants-equipped-INNERCLOTHING
        color: "#f2f2f2"
      - state: belt-equipped-INNERCLOTHING
        color: "#737373"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitEnviroslacksColorRed
  name: "червоні екошорти"
  description: "Улюблений проект особливо шикарного Плазмамена, цей варіант має червоні акценти. Вишукано!"
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ffffff"
    - state: plaintop-icon
      color: "#ffffff"
    - state: tie-icon
      color: "#99211f"
    - state: accent-icon
      color: "#99211f"
    - state: pants-icon
      color: "#292929"
    - state: belt-icon
      color: "#737373"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffffff"
      - state: plaintop-inhand-left
        color: "#ffffff"
      - state: tie-inhand-left
        color: "#99211f"
      - state: accent-inhand-left
        color: "#99211f"
      - state: pants-inhand-left
        color: "#292929"
      - state: belt-inhand-left
        color: "#737373"
      right:
      - state: inhand-right
        color: "#ffffff"
      - state: plaintop-inhand-right
        color: "#ffffff"
      - state: tie-inhand-right
        color: "#99211f"
      - state: accent-inhand-right
        color: "#99211f"
      - state: pants-inhand-right
        color: "#292929"
      - state: belt-inhand-right
        color: "#737373"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ffffff"
      - state: plaintop-equipped-INNERCLOTHING
        color: "#ffffff"
      - state: tie-equipped-INNERCLOTHING
        color: "#99211f"
      - state: accentalt-equipped-INNERCLOTHING
        color: "#99211f"
      - state: pants-equipped-INNERCLOTHING
        color: "#292929"
      - state: belt-equipped-INNERCLOTHING
        color: "#737373"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#99211f"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitEnviroslacksColorOrange
  name: "помаранчеві екошорти"
  description: "Улюблений проект особливо розкішного Плазмамена, цей варіант має помаранчеві акценти. Пікантно!"
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ffffff"
    - state: plaintop-icon
      color: "#ffffff"
    - state: tie-icon
      color: "#c2680f"
    - state: accent-icon
      color: "#c2680f"
    - state: pants-icon
      color: "#292929"
    - state: belt-icon
      color: "#737373"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffffff"
      - state: plaintop-inhand-left
        color: "#ffffff"
      - state: tie-inhand-left
        color: "#c2680f"
      - state: accent-inhand-left
        color: "#c2680f"
      - state: pants-inhand-left
        color: "#292929"
      - state: belt-inhand-left
        color: "#737373"
      right:
      - state: inhand-right
        color: "#ffffff"
      - state: plaintop-inhand-right
        color: "#ffffff"
      - state: tie-inhand-right
        color: "#c2680f"
      - state: accent-inhand-right
        color: "#c2680f"
      - state: pants-inhand-right
        color: "#292929"
      - state: belt-inhand-right
        color: "#737373"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ffffff"
      - state: plaintop-equipped-INNERCLOTHING
        color: "#ffffff"
      - state: tie-equipped-INNERCLOTHING
        color: "#c2680f"
      - state: accentalt-equipped-INNERCLOTHING
        color: "#c2680f"
      - state: pants-equipped-INNERCLOTHING
        color: "#292929"
      - state: belt-equipped-INNERCLOTHING
        color: "#737373"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#c2680f"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitEnviroslacksColorGreen
  name: "зелені екошорти"
  description: "Улюблений проект особливо розкішного Плазмамена, цей варіант має зелені акценти. Зелений!"
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ffffff"
    - state: plaintop-icon
      color: "#ffffff"
    - state: tie-icon
      color: "#5b991f"
    - state: accent-icon
      color: "#5b991f"
    - state: pants-icon
      color: "#292929"
    - state: belt-icon
      color: "#737373"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffffff"
      - state: plaintop-inhand-left
        color: "#ffffff"
      - state: tie-inhand-left
        color: "#5b991f"
      - state: accent-inhand-left
        color: "#5b991f"
      - state: pants-inhand-left
        color: "#292929"
      - state: belt-inhand-left
        color: "#737373"
      right:
      - state: inhand-right
        color: "#ffffff"
      - state: plaintop-inhand-right
        color: "#ffffff"
      - state: tie-inhand-right
        color: "#5b991f"
      - state: accent-inhand-right
        color: "#5b991f"
      - state: pants-inhand-right
        color: "#292929"
      - state: belt-inhand-right
        color: "#737373"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ffffff"
      - state: plaintop-equipped-INNERCLOTHING
        color: "#ffffff"
      - state: tie-equipped-INNERCLOTHING
        color: "#5b991f"
      - state: accentalt-equipped-INNERCLOTHING
        color: "#5b991f"
      - state: pants-equipped-INNERCLOTHING
        color: "#292929"
      - state: belt-equipped-INNERCLOTHING
        color: "#737373"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#5b991f"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitEnviroslacksColorBlue
  name: "сині екошорти"
  description: "Улюблений проект особливо шикарного Плазмамена, цей варіант має сині акценти. Круто!"
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ffffff"
    - state: plaintop-icon
      color: "#ffffff"
    - state: tie-icon
      color: "#2b5c99"
    - state: accent-icon
      color: "#2b5c99"
    - state: pants-icon
      color: "#292929"
    - state: belt-icon
      color: "#737373"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffffff"
      - state: plaintop-inhand-left
        color: "#ffffff"
      - state: tie-inhand-left
        color: "#2b5c99"
      - state: accent-inhand-left
        color: "#2b5c99"
      - state: pants-inhand-left
        color: "#292929"
      - state: belt-inhand-left
        color: "#737373"
      right:
      - state: inhand-right
        color: "#ffffff"
      - state: plaintop-inhand-right
        color: "#ffffff"
      - state: tie-inhand-right
        color: "#2b5c99"
      - state: accent-inhand-right
        color: "#2b5c99"
      - state: pants-inhand-right
        color: "#292929"
      - state: belt-inhand-right
        color: "#737373"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ffffff"
      - state: plaintop-equipped-INNERCLOTHING
        color: "#ffffff"
      - state: tie-equipped-INNERCLOTHING
        color: "#2b5c99"
      - state: accentalt-equipped-INNERCLOTHING
        color: "#2b5c99"
      - state: pants-equipped-INNERCLOTHING
        color: "#292929"
      - state: belt-equipped-INNERCLOTHING
        color: "#737373"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#2b5c99"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitEnviroslacksColorBrown
  name: "коричневі екошорти"
  description: "Улюблений проект особливо шикарного Плазмамена, цей варіант має коричневі штани. Нагадує запилені офіси."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ffffff"
    - state: plaintop-icon
      color: "#ffffff"
    - state: tie-icon
      color: "#a349a4"
    - state: accent-icon
      color: "#a349a4"
    - state: pants-icon
      color: "#583f20"
    - state: belt-icon
      color: "#363636"
    - state: beltbuckle_small-icon
      color: "#3e3e3e"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffffff"
      - state: plaintop-inhand-left
        color: "#ffffff"
      - state: tie-inhand-left
        color: "#a349a4"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: pants-inhand-left
        color: "#583f20"
      - state: belt-inhand-left
        color: "#363636"
      - state: beltbuckle_small-inhand-left
        color: "#3e3e3e"
      right:
      - state: inhand-right
        color: "#ffffff"
      - state: plaintop-inhand-right
        color: "#ffffff"
      - state: tie-inhand-right
        color: "#a349a4"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: pants-inhand-right
        color: "#583f20"
      - state: belt-inhand-right
        color: "#363636"
      - state: beltbuckle_small-inhand-right
        color: "#3e3e3e"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ffffff"
      - state: plaintop-equipped-INNERCLOTHING
        color: "#ffffff"
      - state: tie-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: accentalt-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: pants-equipped-INNERCLOTHING
        color: "#583f20"
      - state: belt-equipped-INNERCLOTHING
        color: "#363636"
      - state: beltbuckle_small-equipped-INNERCLOTHING
        color: "#3e3e3e"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitEnviroslacksMNK
  name: "MNK envirolaces"
  description: "Культові енвайронментальні штани з фірмовою монохромною естетикою MNK. Класика!"
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ffffff"
    - state: plaintop-icon
      color: "#ffffff"
    - state: tie-icon
      color: "#363636"
    - state: accent-icon
      color: "#363636"
    - state: pants-icon
      color: "#292929"
    - state: belt-icon
      color: "#737373"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffffff"
      - state: plaintop-inhand-left
        color: "#ffffff"
      - state: tie-inhand-left
        color: "#363636"
      - state: accent-inhand-left
        color: "#363636"
      - state: pants-inhand-left
        color: "#292929"
      - state: belt-inhand-left
        color: "#737373"
      right:
      - state: inhand-right
        color: "#ffffff"
      - state: plaintop-inhand-right
        color: "#ffffff"
      - state: tie-inhand-right
        color: "#363636"
      - state: accent-inhand-right
        color: "#363636"
      - state: pants-inhand-right
        color: "#292929"
      - state: belt-inhand-right
        color: "#737373"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ffffff"
      - state: plaintop-equipped-INNERCLOTHING
        color: "#ffffff"
      - state: tie-equipped-INNERCLOTHING
        color: "#363636"
      - state: accentalt-equipped-INNERCLOTHING
        color: "#363636"
      - state: pants-equipped-INNERCLOTHING
        color: "#292929"
      - state: belt-equipped-INNERCLOTHING
        color: "#737373"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#d6d6d6"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitEnviroslacksMNKAlt
  name: "MNK envirolaces"
  description: "Культові нью-йоркські чорні штани з фірмовою монохромною естетикою MNK. Нуар!"
  suffix: Alternative
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#3b3b3b"
    - state: plaintop-icon
      color: "#3b3b3b"
    - state: tie-icon
      color: "#d6d6d6"
    - state: accent-icon
      color: "#d6d6d6"
    - state: pants-icon
      color: "#f2f2f2"
    - state: belt-icon
      color: "#737373"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#3b3b3b"
      - state: plaintop-inhand-left
        color: "#3b3b3b"
      - state: tie-inhand-left
        color: "#d6d6d6"
      - state: accent-inhand-left
        color: "#d6d6d6"
      - state: pants-inhand-left
        color: "#f2f2f2"
      - state: belt-inhand-left
        color: "#737373"
      right:
      - state: inhand-right
        color: "#3b3b3b"
      - state: plaintop-inhand-right
        color: "#3b3b3b"
      - state: tie-inhand-right
        color: "#d6d6d6"
      - state: accent-inhand-right
        color: "#d6d6d6"
      - state: pants-inhand-right
        color: "#f2f2f2"
      - state: belt-inhand-right
        color: "#737373"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#3b3b3b"
      - state: plaintop-equipped-INNERCLOTHING
        color: "#3b3b3b"
      - state: tie-equipped-INNERCLOTHING
        color: "#d6d6d6"
      - state: accentalt-equipped-INNERCLOTHING
        color: "#d6d6d6"
      - state: pants-equipped-INNERCLOTHING
        color: "#f2f2f2"
      - state: belt-equipped-INNERCLOTHING
        color: "#737373"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#424242"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitEnviroslacksPsychologist
  name: "психолог енвірослакс"
  description: "Улюблений проект особливо шикарного Плазмамена, цей варіант був зроблений для психолога. Запаморочливо!"
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ffffff"
    - state: plaintop-icon
      color: "#ffffff"
    - state: tie-icon
      color: "#5ba0cf"
    - state: accent-icon
      color: "#5ba0cf"
    - state: pants-icon
      color: "#292929"
    - state: belt-icon
      color: "#737373"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffffff"
      - state: plaintop-inhand-left
        color: "#ffffff"
      - state: tie-inhand-left
        color: "#5ba0cf"
      - state: accent-inhand-left
        color: "#5ba0cf"
      - state: pants-inhand-left
        color: "#292929"
      - state: belt-inhand-left
        color: "#737373"
      right:
      - state: inhand-right
        color: "#ffffff"
      - state: plaintop-inhand-right
        color: "#ffffff"
      - state: tie-inhand-right
        color: "#5ba0cf"
      - state: accent-inhand-right
        color: "#5ba0cf"
      - state: pants-inhand-right
        color: "#292929"
      - state: belt-inhand-right
        color: "#737373"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ffffff"
      - state: plaintop-equipped-INNERCLOTHING
        color: "#ffffff"
      - state: tie-equipped-INNERCLOTHING
        color: "#5ba0cf"
      - state: accentalt-equipped-INNERCLOTHING
        color: "#5ba0cf"
      - state: pants-equipped-INNERCLOTHING
        color: "#292929"
      - state: belt-equipped-INNERCLOTHING
        color: "#737373"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#5ba0cf"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

# Color envirosuits
- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitColorWhite
  name: "білий екокостюм"
  description: "Універсальний білий комбінезон без розпізнавальних знаків."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ffffff"
    - state: accent-icon
      color: "#a349a4"
    - state: corneraccent-icon
      color: "#a349a4"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffffff"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: corneraccent-inhand-left
        color: "#a349a4"
      right:
      - state: inhand-right
        color: "#ffffff"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: corneraccent-inhand-right
        color: "#a349a4"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ffffff"
      - state: accent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: corneraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitColorGrey
  name: "сірий екокостюм"
  description: "Зі смаком підібраний сірий екокостюм, що нагадує про старі добрі часи."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#b3b3b3"
    - state: accent-icon
      color: "#a349a4"
    - state: corneraccent-icon
      color: "#a349a4"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#b3b3b3"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: corneraccent-inhand-left
        color: "#a349a4"
      right:
      - state: inhand-right
        color: "#b3b3b3"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: corneraccent-inhand-right
        color: "#a349a4"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#b3b3b3"
      - state: accent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: corneraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitColorBlack
  name: "чорний екокостюм"
  description: "Звичайний чорний комбінезон без розпізнавальних знаків."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#3f3f3f"
    - state: accent-icon
      color: "#a349a4"
    - state: corneraccent-icon
      color: "#a349a4"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#3f3f3f"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: corneraccent-inhand-left
        color: "#a349a4"
      right:
      - state: inhand-right
        color: "#3f3f3f"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: corneraccent-inhand-right
        color: "#a349a4"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#3f3f3f"
      - state: accent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: corneraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitColorRed
  name: "червоний екокостюм"
  description: "Темно-зелений екокостюм."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#d1423f"
    - state: accent-icon
      color: "#a349a4"
    - state: corneraccent-icon
      color: "#a349a4"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#d1423f"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: corneraccent-inhand-left
        color: "#a349a4"
      right:
      - state: inhand-right
        color: "#d1423f"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: corneraccent-inhand-right
        color: "#a349a4"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#d1423f"
      - state: accent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: corneraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitColorGreen
  name: "зелений екокостюм"
  description: "Загальний зелений екокостюм без маркування звання."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#9ed63a"
    - state: accent-icon
      color: "#a349a4"
    - state: corneraccent-icon
      color: "#a349a4"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#9ed63a"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: corneraccent-inhand-left
        color: "#a349a4"
      right:
      - state: inhand-right
        color: "#9ed63a"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: corneraccent-inhand-right
        color: "#a349a4"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#9ed63a"
      - state: accent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: corneraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitColorDarkGreen
  name: "темно-зелений екокостюм"
  description: "Типовий темно-зелений екокостюм без розпізнавальних знаків."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#79CC26"
    - state: accent-icon
      color: "#a349a4"
    - state: corneraccent-icon
      color: "#a349a4"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#79CC26"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: corneraccent-inhand-left
        color: "#a349a4"
      right:
      - state: inhand-right
        color: "#79CC26"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: corneraccent-inhand-right
        color: "#a349a4"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#79CC26"
      - state: accent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: corneraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitColorBlue
  name: "синій екокостюм"
  description: "Універсальний синій екокостюм без розпізнавальних знаків."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#52aecc"
    - state: accent-icon
      color: "#a349a4"
    - state: corneraccent-icon
      color: "#a349a4"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#52aecc"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: corneraccent-inhand-left
        color: "#a349a4"
      right:
      - state: inhand-right
        color: "#52aecc"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: corneraccent-inhand-right
        color: "#a349a4"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#52aecc"
      - state: accent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: corneraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitColorDarkBlue
  name: "темно-синій екокостюм"
  description: "Універсальний темно-синій екокостюм без розпізнавальних знаків."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#3285ba"
    - state: accent-icon
      color: "#a349a4"
    - state: corneraccent-icon
      color: "#a349a4"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#3285ba"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: corneraccent-inhand-left
        color: "#a349a4"
      right:
      - state: inhand-right
        color: "#3285ba"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: corneraccent-inhand-right
        color: "#a349a4"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#3285ba"
      - state: accent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: corneraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitColorTeal
  name: "екокостюм чирка"
  description: "Типовий екокостюм чирка без маркування звання."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#77f3b7"
    - state: accent-icon
      color: "#a349a4"
    - state: corneraccent-icon
      color: "#a349a4"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#77f3b7"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: corneraccent-inhand-left
        color: "#a349a4"
      right:
      - state: inhand-right
        color: "#77f3b7"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: corneraccent-inhand-right
        color: "#a349a4"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#77f3b7"
      - state: accent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: corneraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitColorMaroon
  name: "бордовий екокостюм"
  description: "Звичайний бордовий екокостюм без розпізнавальних знаків."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#cc295f"
    - state: accent-icon
      color: "#a349a4"
    - state: corneraccent-icon
      color: "#a349a4"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#cc295f"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: corneraccent-inhand-left
        color: "#a349a4"
      right:
      - state: inhand-right
        color: "#cc295f"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: corneraccent-inhand-right
        color: "#a349a4"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#cc295f"
      - state: accent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: corneraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitColorPink
  name: "рожевий екокостюм"
  description: >-
    "Plasmamen can't slay" and other jokes you can tell yourself.
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ff8cff"
    - state: accent-icon
      color: "#8b3e8c"
    - state: corneraccent-icon
      color: "#8b3e8c"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ff8cff"
      - state: accent-inhand-left
        color: "#8b3e8c"
      - state: corneraccent-inhand-left
        color: "#8b3e8c"
      right:
      - state: inhand-right
        color: "#ff8cff"
      - state: accent-inhand-right
        color: "#8b3e8c"
      - state: corneraccent-inhand-right
        color: "#8b3e8c"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ff8cff"
      - state: accent-equipped-INNERCLOTHING
        color: "#8b3e8c"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#8b3e8c"
      - state: corneraccent-equipped-INNERCLOTHING
        color: "#8b3e8c"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitColorYellow
  name: "жовтий екокостюм"
  description: "Універсальний жовтий екокостюм без розпізнавальних знаків."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ffe14d"
    - state: accent-icon
      color: "#a349a4"
    - state: corneraccent-icon
      color: "#a349a4"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffe14d"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: corneraccent-inhand-left
        color: "#a349a4"
      right:
      - state: inhand-right
        color: "#ffe14d"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: corneraccent-inhand-right
        color: "#a349a4"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ffe14d"
      - state: accent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: corneraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitColorPurple
  name: "фіолетовий екокостюм"
  description: "Універсальний фіолетовий екокостюм без розпізнавальних знаків."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#9f70cc"
    - state: accent-icon
      color: "#843b85"
    - state: corneraccent-icon
      color: "#843b85"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#9f70cc"
      - state: accent-inhand-left
        color: "#843b85"
      - state: corneraccent-inhand-left
        color: "#843b85"
      right:
      - state: inhand-right
        color: "#9f70cc"
      - state: accent-inhand-right
        color: "#843b85"
      - state: corneraccent-inhand-right
        color: "#843b85"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#9f70cc"
      - state: accent-equipped-INNERCLOTHING
        color: "#843b85"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#843b85"
      - state: corneraccent-equipped-INNERCLOTHING
        color: "#843b85"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitColorOrange
  name: "помаранчевий екокостюм"
  description: "Не одягайте це поруч з параноїдальними офіцерами безпеки."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ff8c19"
    - state: accent-icon
      color: "#a349a4"
    - state: corneraccent-icon
      color: "#a349a4"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ff8c19"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: corneraccent-inhand-left
        color: "#a349a4"
      right:
      - state: inhand-right
        color: "#ff8c19"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: corneraccent-inhand-right
        color: "#a349a4"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ff8c19"
      - state: accent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: corneraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitColorLightBrown
  name: "світло-коричневий екокостюм"
  description: "Універсальний світло-коричневий екокостюм без розпізнавальних знаків."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#a17229"
    - state: accent-icon
      color: "#a349a4"
    - state: corneraccent-icon
      color: "#a349a4"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#a17229"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: corneraccent-inhand-left
        color: "#a349a4"
      right:
      - state: inhand-right
        color: "#a17229"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: corneraccent-inhand-right
        color: "#a349a4"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#a17229"
      - state: accent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: corneraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"

- type: entity
  parent: ClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitColorBrown
  name: "коричневий екокостюм"
  description: "Звичайний коричневий екокостюм без розпізнавальних знаків."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#543e1b"
    - state: accent-icon
      color: "#a349a4"
    - state: corneraccent-icon
      color: "#a349a4"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#543e1b"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: corneraccent-inhand-left
        color: "#a349a4"
      right:
      - state: inhand-right
        color: "#543e1b"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: corneraccent-inhand-right
        color: "#a349a4"
  - type: Clothing
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#543e1b"
      - state: accent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: corneraccent-equipped-INNERCLOTHING
        color: "#a349a4"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa"
