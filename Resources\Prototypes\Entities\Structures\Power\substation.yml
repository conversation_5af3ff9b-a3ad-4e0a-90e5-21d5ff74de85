# Base substation
- type: entity
  abstract: true
  id: BaseSubstation
  parent: [ BaseMachine, ConstructibleMachine ]
  name: "підстанція"
  description: "Зменшує напругу електроенергії, що подається в нього."
  placement:
    mode: SnapgridCenter
  components:
  - type: Sprite # TODO: add sprite for maintenance panel open
    sprite: Structures/Power/substation.rsi
    snapCardinals: true
    layers:
    - state: substation
    - state: screen
      shader: unshaded
    - state: full
      shader: unshaded
  - type: UpgradeBattery
    maxChargeMultiplier: 2
    baseMaxCharge: 2500000
  - type: UpgradePowerSupplyRamping
    scaling: Linear
    supplyRampingMultiplier: 1
  - type: Battery
    startingCharge: 0
  - type: ExaminableBattery
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#3db83b"
    castShadows: false
  - type: NodeContainer
    examinable: true
    nodes:
      input:
        !type:CableDeviceNode
        nodeGroupID: HVPower
      output:
        !type:CableDeviceNode
        nodeGroupID: MVPower
  - type: PowerMonitoringDevice
    group: Substation
    sourceNode: input
    loadNode: output
    collectionName: substation
    sprite: Structures/Power/substation.rsi
    state: substation_static
  - type: BatteryCharger
    voltage: High
  - type: BatteryDischarger
    voltage: Medium
  - type: PowerNetworkBattery
    maxSupply: 150000
    maxChargeRate: 5000
    supplyRampTolerance: 5000
    supplyRampRate: 1000
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: StrongMetallic
  - type: PacifismDangerousAttack
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
      - !type:ExplodeBehavior
      - !type:SpawnEntitiesBehavior
        spawn:
          MachineFrameDestroyed:
            min: 1
            max: 1
      - !type:SpawnGasBehavior
        gasMixture:
          volume: 1000
          moles:
            - 0 # oxygen
            - 0 # nitrogen
            - 340.5701689 # carbon dioxide
          temperature: 373.15
  - type: Explosive
    explosionType: Default
    maxIntensity: 100
    intensitySlope: 2
    totalIntensity: 200
  - type: WiresPanel
  - type: Machine
    board: SubstationMachineCircuitboard
  - type: StationInfiniteBatteryTarget
  - type: AmbientOnPowered
  - type: AmbientSound
    volume: -5
    range: 3
    sound:
      path: /Audio/Ambience/Objects/buzzing.ogg
  - type: Electrified
    onHandInteract: false
    onInteractUsing: false
    onBump: false
    requirePower: true
    highVoltageNode: input
    mediumVoltageNode: output

# Compact Wall Substation Base
- type: entity
  id: BaseSubstationWall
  categories: [ HideSpawnMenu ]
  name: "настінна підстанція"
  description: "Підстанція, розроблена для компактних шатлів і просторів."
  placement:
    mode: SnapgridCenter
  components:
  - type: AmbientOnPowered
  - type: AmbientSound
    volume: -5
    range: 2
    sound:
      path: /Audio/Ambience/Objects/buzzing.ogg
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#3db83b"
    netsync: false
    castShadows: false
  - type: Clickable
  - type: AccessReader
    access: [["Engineering"]]
  - type: ContainerFill
    containers:
      board: [ WallmountSubstationElectronics ]
  - type: ContainerContainer
    containers:
      board: !type:Container
  - type: InteractionOutline
  - type: Physics
    bodyType: Static
    canCollide: false
  - type: Fixtures
  - type: Transform
    anchored: true
  - type: Sprite # TODO: add sprite for maintenance panel open
    drawdepth: WallMountedItems
    sprite: Structures/Power/substation.rsi
    layers:
    - state: substation_wall
    - state: screen
      shader: unshaded
  - type: Battery
    maxCharge: 2000000
    startingCharge: 0
  - type: ExaminableBattery
  - type: NodeContainer
    examinable: true
    nodes:
      input:
        !type:CableDeviceNode
        nodeGroupID: HVPower
      output:
        !type:CableDeviceNode
        nodeGroupID: MVPower
  - type: PowerMonitoringDevice
    group: Substation
    sourceNode: input
    loadNode: output
    sprite: Structures/Power/substation.rsi
    state: substation_wall_static
  - type: BatteryCharger
    voltage: High
  - type: BatteryDischarger
    voltage: Medium
  - type: PowerNetworkBattery
    maxSupply: 150000
    maxChargeRate: 5000
    supplyRampTolerance: 5000
    supplyRampRate: 1000
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: PacifismDangerousAttack
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:ExplodeBehavior
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
  - type: Construction
    graph: WallmountSubstation
    node: substation
  - type: Explosive
    explosionType: Default
    maxIntensity: 50
    intensitySlope: 2
    totalIntensity: 100
  - type: StationInfiniteBatteryTarget
  - type: WallMount

# Substations in use

- type: entity
  parent: BaseSubstation
  id: SubstationBasic
  suffix: Basic, 2.5MJ
  components:
  - type: Battery
    maxCharge: 2500000
    startingCharge: 2500000

- type: entity
  parent: SubstationBasic
  id: SubstationBasicEmpty
  suffix: Empty
  components:
  - type: Battery
    startingCharge: 0

- type: entity
  parent: BaseSubstationWall
  id: SubstationWallBasic
  suffix: Basic, 2MJ
  components:
  - type: Battery
    maxCharge: 2000000
    startingCharge: 2000000
  - type: ContainerFill
    containers:
      board: [ WallmountSubstationElectronics ]
      capacitor: [ CapacitorStockPart ]
      powercell: [ PowerCellSmall ]
  - type: ContainerContainer
    containers:
      board: !type:Container
      capacitor: !type:Container
      powercell: !type:Container

# Construction Frame
- type: entity
  id: BaseSubstationWallFrame
  categories: [ HideSpawnMenu ]
  name: "настінний каркас підстанції"
  description: "Каркас підстанції для будівництва"
  placement:
    mode: SnapgridCenter
  components:
  - type: Clickable
  - type: InteractionOutline
  - type: Physics
    bodyType: Static
    canCollide: false
  - type: Fixtures
  - type: Transform
    anchored: true
  - type: Sprite
    drawdepth: WallMountedItems
    sprite: Structures/Power/substation.rsi
    layers:
    - state: substation_wall
  - type: Construction
    graph: WallmountSubstation
    node: frame
  - type: WallMount
