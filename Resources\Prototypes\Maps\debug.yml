- type: gameMap
  id: Empty
  mapName: Empty
  mapPath: /Maps/Test/empty.yml
  minPlayers: 0
  stations:
    Empty:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: "Empty"
        - type: StationJobs
          overflowJobs:
            - Passenger
          availableJobs:
            Passenger: [ -1, -1 ]

- type: gameMap
  id: Dev
  mapName: Дев
  mapPath: /Maps/Test/dev_map.yml
  minPlayers: 0
  stations:
    Dev:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: "Dev"
        - type: StationJobs
          overflowJobs:
          - Captain
          availableJobs:
            Captain: [ -1, -1 ]
        # Goobstation blob-config-start DEBUG
        - type: StationBlobConfig
          stageBegin: 20
          stageCritical: 50
          stageTheEnd: 100
        # Goobstation blob-config-end

- type: gameMap
  id: TestTeg
  mapName: Тест ТЕГ
  mapPath: /Maps/Test/test_teg.yml
  minPlayers: 0
  stations:
    TEG:
      stationProto: TestStation
      components:
        - type: StationNameSetup
          mapNameTemplate: "TEG"
        - type: StationJobs
          overflowJobs:
            - ChiefEngineer
          availableJobs:
            ChiefEngineer: [ -1, -1 ]
