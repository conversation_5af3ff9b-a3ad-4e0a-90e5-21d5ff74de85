
## Messages shown to Pacified players when they try to do violence:

# With projectiles:
pacified-cannot-throw = I can't bring myself to throw { THE($projectile) }, that could hurt someone!
# With embedding projectiles:
pacified-cannot-throw-embed = No way I could throw { THE($projectile) }, that could get lodged inside someone!
# With liquid-spilling projectiles:
pacified-cannot-throw-spill = I can't possibly throw { THE($projectile) }, that could spill nasty stuff on someone!
# With bolas and snares:
pacified-cannot-throw-snare = I can't throw { THE($projectile) }, what if someone trips?!

pacified-cannot-harm-directly = I can't bring myself to hurt { THE($entity) }!
pacified-cannot-harm-indirect = I can't damage { THE($entity) }, it could hurt someone!
pacified-cannot-fire-gun = I can't fire { THE($entity) }, it could hurt someone!
