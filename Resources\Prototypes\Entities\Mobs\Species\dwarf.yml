- type: entity
  save: false
  name: "Уріст МакХендс Гном"
  parent: BaseMobSpeciesOrganic
  id: BaseMobDwarf
  abstract: true
  components:
  - type: Hunger
  - type: Thirst
  - type: Carriable # Carrying system from nyanotrasen.
  - type: Icon
    sprite: Mobs/Species/Slime/parts.rsi # It was like this beforehand, no idea why.
    state: full
  - type: Respirator
    damage:
      types:
        Asphyxiation: 2
    damageRecovery:
      types:
        Asphyxiation: -1.0
  - type: Sprite
    noRot: true
    drawdepth: Mobs
  - type: ScaleData # Goobstation
    scale: 1.15, 0.8
  - type: Body
    prototype: Dwarf
    requiredLegs: 2
  - type: Butcherable
    butcheringType: Spike
    spawned:
    - id: FoodMeatHuman
      amount: 5
  - type: Fixtures
    fixtures: # TODO: This needs a second fixture just for mob collisions.
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        # they r smaller
        density: 120
        restitution: 0.0
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Vocal
    sounds:
      Male: UnisexDwarf
      Female: FemaleDwarf
      Unsexed: UnisexDwarf
  - type: ReplacementAccent
    accent: dwarf
  - type: Speech
    speechSounds: Bass
  - type: HumanoidAppearance
    species: Human
    hideLayersOnEquip:
    - Hair
    - Snout
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    - SolCommon
    understands:
    - TauCetiBasic
    - SolCommon
  - type: LightweightDrunk
    boozeStrengthMultiplier: 0.5
  - type: Stamina
    critThreshold: 115
    decay: 3.3 # 3 base decay multiplied by 1.15 = 3.3
  - type: FootPrints
    leftBarePrint: "footprint-left-bare-dwarf"
    rightBarePrint: "footprint-right-bare-dwarf"

- type: entity
  parent: BaseSpeciesDummy
  id: MobDwarfDummy
  categories: [ HideSpawnMenu ]
