using System.Linq;
using Content.Shared.Materials;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Timing;

namespace Content.Client.Materials.UI;

/// <summary>
/// This widget is one row in the lathe eject menu.
/// </summary>
[GenerateTypedNameReferences]
public sealed partial class MaterialStorageControl : ScrollContainer
{
    [Dependency] private readonly IEntityManager _entityManager = default!;

    private readonly MaterialSiloSystem _materialSilo;

    private EntityUid? _owner;

    private Dictionary<string, int> _currentMaterials = new();

    public MaterialStorageControl()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        _materialSilo = _entityManager.System<MaterialSiloSystem>();
    }

    public void SetOwner(EntityUid owner)
    {
        _owner = owner;
    }

    protected override void FrameUpdate(FrameEventArgs args)
    {
        base.FrameUpdate(args);

        if (_owner == null)
            return;

        if (_entityManager.Deleted(_owner) || !_entityManager.TryGetComponent<MaterialStorageComponent>(_owner, out var materialStorage))
        {
            _owner = null;
            return;
        }

        var canEject = materialStorage.CanEjectStoredMaterials;

        Dictionary<string, int> mats;
        if (_entityManager.TryGetComponent<MaterialSiloUtilizerComponent>(_owner, out var utilizer) && utilizer.Silo.HasValue)
        {
            var silo = _materialSilo.GetSiloStorage(_owner.Value);
            mats = silo != null
                ? silo.Value.Comp.Storage.Select(pair => (pair.Key.Id, pair.Value)).ToDictionary()
                : materialStorage.Storage.Select(pair => (pair.Key.Id, pair.Value)).ToDictionary();
            ConnectToSiloLabel.Visible = silo != null;
        }
        else
        {
            mats = materialStorage.Storage.Select(pair => (pair.Key.Id, pair.Value)).ToDictionary();
            ConnectToSiloLabel.Visible = false;
        }

        if (_currentMaterials.Equals(mats))
            return;

        var missing = new List<string>();
        var extra = new List<string>();
        foreach (var (mat, amount) in mats)
        {
            if (!_currentMaterials.ContainsKey(mat) ||
                _currentMaterials[mat] == 0 && _currentMaterials[mat] != amount)
                missing.Add(mat);
        }
        foreach (var (mat, amount) in _currentMaterials)
        {
            if (!mats.ContainsKey(mat) || amount == 0)
                extra.Add(mat);
        }

        var children = new List<MaterialDisplay>();
        children.AddRange(MaterialList.Children.OfType<MaterialDisplay>());

        foreach (var display in children)
        {
            var mat = display.Material;

            if (extra.Contains(mat))
            {
                MaterialList.RemoveChild(display);
                continue;
            }

            if (!mats.TryGetValue(mat, out var newAmount))
                continue;
            display.UpdateVolume(newAmount);
        }

        foreach (var mat in missing)
        {
            var volume = mats[mat];
            MaterialList.AddChild(new MaterialDisplay(_owner.Value, mat, volume, canEject));
        }

        _currentMaterials = mats;
        NoMatsLabel.Visible = ChildCount == 1;
    }
}
