﻿- type: entity
  id: DisposalMachineFrame
  name: "рама машини високого тиску"
  description: "Рама машини, виготовлена таким чином, щоб витримувати тиск, який використовується в системі утилізації станції."
  placement:
    mode: SnapgridCenter
  components:
    - type: Transform
      anchored: true
      noRot: true
    - type: Physics
      bodyType: Static
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.25,-0.4,0.25,0.4"
          density: 190
          mask:
          - MachineMask
          layer:
          - MachineLayer
    - type: Clickable
    - type: InteractionOutline
    - type: Anchorable
    - type: Pullable
    - type: Construction
      graph: DisposalMachine
      node: frame
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: Metallic
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 100
          behaviors:
            - !type:PlaySoundBehavior
              sound:
                collection: MetalBreak
            - !type:ChangeConstructionNodeBehavior
              node: start
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
    - type: Sprite
      sprite: Structures/Piping/high_pressure_machine_frame.rsi
      layers:
      - state: frame
        map: [ "enum.ConstructionVisuals.Layer" ]
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.ConstructionVisuals.Key:
          enum.ConstructionVisuals.Layer:
            frame_cable: { state: frame_cables }
            frame_electronics: { state: frame_electronics }
            frame_unit: { state: frame_unit }
            frame_mailing: { state: frame_unit } # not a typo, there is no frame_mailing state.
            frame_inlet: { state: frame_inlet }
            frame_outlet: { state: frame_outlet }
