<controls:FancyWindow xmlns="https://spacestation14.io"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls" Title="{Loc 'autodoc-view-program-title'}" SetSize="600 500">
    <BoxContainer Orientation="Horizontal">
        <BoxContainer Orientation="Vertical" Margin="5 5 5 5">
            <Label Name="ProgramTitle"/>
            <!-- Set to Program.Title -->
            <Button Name="SafetyButton" Text="{Loc 'autodoc-safety-enabled'}"/>
            <Button Name="RemoveButton" StyleClasses="Danger" Text="{Loc 'autodoc-remove-program'}"/>
            <Button Name="AddStepButton" Text="{Loc 'autodoc-add-step'}"/>
            <Button Name="RemoveStepButton" StyleClasses="Danger" Text="{Loc 'autodoc-remove-step'}" Disabled="True"/>
            <Button Name="StartButton" StyleClasses="Danger" Text="{Loc 'autodoc-start-program'}"/>
        </BoxContainer>
        <ScrollContainer HorizontalExpand="True" VerticalExpand="True">
            <ItemList Name="Steps"/>
            <!-- Set to Program.Steps -->
        </ScrollContainer>
    </BoxContainer>
</controls:FancyWindow>
