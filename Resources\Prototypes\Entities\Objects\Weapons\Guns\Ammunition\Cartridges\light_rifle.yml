- type: entity
  id: BaseCartridgeLightRifle
  name: "патрон (гвинтівка .30)"
  parent: BaseCartridge
  abstract: true
  components:
  - type: Tag
    tags:
    - Cartridge
    - CartridgeLightRifle
  - type: CartridgeAmmo
    proto: BulletLightRifle
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Casings/ammo_casing.rsi
    layers:
    - state: base
      map: ["enum.AmmoVisualLayers.Base"]
  - type: Appearance
  - type: SpentAmmoVisuals

- type: entity
  id: CartridgeLightRifle
  name: "патрон (гвинтівка .30)"
  parent: BaseCartridgeLightRifle
  components:
  - type: CartridgeAmmo
    proto: BulletLightRifle

- type: entity
  id: CartridgeLightRiflePractice
  name: "патрон (практична стрільба з гвинтівки калібру .30)"
  parent: BaseCartridgeLightRifle
  components:
  - type: CartridgeAmmo
    proto: BulletLightRiflePractice
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#dbdbdb"

- type: entity
  id: CartridgeLightRifleRubber
  name: "набій (.30 гвинтівковий гумовий)"
  parent: BaseCartridgeLightRifle
  components:
  - type: CartridgeAmmo
    proto: BulletLightRifleRubber
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#43c4f7"

- type: entity
  id: CartridgeLightRifleIncendiary
  name: "набій (.30 гвинтівковий запальний)"
  parent: BaseCartridgeLightRifle
  components:
  - type: CartridgeAmmo
    proto: BulletLightRifleIncendiary
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#ff6e52"

- type: entity
  id: CartridgeLightRifleUranium
  name: "патрон (.30 гвинтівочний уран)"
  parent: BaseCartridgeLightRifle
  components:
  - type: CartridgeAmmo
    proto: BulletLightRifleUranium
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#65fe08"

- type: entity
  id: CartridgeLightRifleShrapnel
  name: "патрон (.30 гвинтівковий, шрапнельний)"
  parent: BaseCartridgeLightRifle
  components:
  - type: CartridgeAmmo
    proto: BulletLightRifleShrapnelSpread
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#FF00FF"
