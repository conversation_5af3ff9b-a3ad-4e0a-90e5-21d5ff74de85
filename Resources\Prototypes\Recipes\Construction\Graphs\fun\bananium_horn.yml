﻿- type: constructionGraph
  id: BananiumHorn
  start: start
  graph:
    - node: start
      edges:
        - to: bananiumHorn
          steps:
            - tag: Pipe
              name: Pipe
              icon:
                sprite: Structures/Piping/Atmospherics/pipe.rsi
                state: pipeStraight
              doAfter: 2
            - material: Bananium
              amount: 4
              doAfter: 1
            - tag: BikeHorn
              name: Bike Horn
              icon:
                sprite: Objects/Fun/bikehorn.rsi
                state: icon
              doAfter: 1
    - node: bananiumHorn
      entity: BananiumHorn
