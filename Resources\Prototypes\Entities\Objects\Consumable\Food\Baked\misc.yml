# When adding new food also add to the appropriate random spawner located in Resources\Prototypes\Entities\Markers\Spawners\Random\Food_Drinks\
# Base

- type: entity
  parent: FoodInjectableBase
  id: FoodBakedBase
  abstract: true
  components:
  - type: Food
  - type: Sprite
    sprite: Objects/Consumable/Food/Baked/misc.rsi
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 6
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
  - type: Item
    size: Tiny

# Muffins/Buns

- type: entity
  name: "кексик"
  parent: FoodBakedBase
  id: FoodBakedMuffin
  description: "Смачний і пористий маленький пиріг."
  components:
  - type: Sprite
    state: muffin

- type: entity
  name: "ягідний кекс"
  parent: FoodBakedBase
  id: FoodBakedMuffinBerry
  description: "Смачний і пористий пиріг з ягодами."
  components:
  - type: Sprite
    state: muffin-berry
  - type: Tag
    tags:
    - Fruit

- type: entity
  name: "вишневий кекс"
  parent: FoodBakedBase
  id: FoodBakedMuffinCherry
  description: "Солодкий кекс з вишневими шматочками."
  components:
  - type: Sprite
    state: muffin-cherry
  - type: Tag
    tags:
    - Fruit

- type: entity
  name: "чорничний кекс"
  parent: FoodBakedBase
  id: FoodBakedMuffinBluecherry
  description: "Вишеньки всередині смачного кексу."
  components:
  - type: Sprite
    state: muffin-bluecherry
  - type: Tag
    tags:
    - Fruit

- type: entity
  name: "медова булочка" #TODO honey
  parent: FoodBakedBase
  id: FoodBakedBunHoney
  description: "Булочка з липкого тіста, глазурована медовою глазур'ю."
  components:
  - type: Sprite
    state: bun-honey

- type: entity
  name: "булочка хоткросс"
  parent: FoodBakedBase
  id: FoodBakedBunHotX
  description: "Булочка з липкого тіста, вкрита глазур'ю з виразним білим хрестом."
  components:
  - type: Sprite
    state: bun-hotcross

- type: entity
  name: "м'ясна булочка"
  parent: FoodBakedBase
  id: FoodBakedBunMeat
  description: "Має потенціал не бути собакою."
  components:
  - type: Sprite
    state: bun-meat
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 2

# Cookies

- type: entity
  name: "печиво"
  parent: FoodBakedBase
  id: FoodBakedCookie
  description: "ПЕЧИВО!!!"
  components:
  - type: Sprite
    state: COOKIE!!!

- type: entity
  name: "вівсяне печиво"
  parent: FoodBakedBase
  id: FoodBakedCookieOatmeal
  description: "Найкраще з печива та вівса."
  components:
  - type: Sprite
    state: cookie-oatmeal
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 8
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Vitamin
          Quantity: 1

- type: entity
  name: "печиво з родзинками"
  parent: FoodBakedBase
  id: FoodBakedCookieRaisin
  description: "Навіщо класти родзинки в печиво?"
  components:
  - type: Sprite
    state: cookie-raisin
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 8
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Vitamin
          Quantity: 1
  - type: Tag
    tags:
    - Fruit

- type: entity
  name: "цукрове печиво"
  parent: FoodBakedBase
  id: FoodBakedCookieSugar
  description: "Точнісінько так, як готувала твоя мама."
  components:
  - type: Sprite
    state: cookie-sugar

# Nuggets

- type: entity
  name: "курячий нагетс"
  parent: FoodBakedBase
  id: FoodBakedNugget
  description: "Курячий\" самородок, нечітко сформований в об'єкт." # Should change depending on name.
  components:
  - type: Tag
    tags:
      - Nugget
  - type: Sprite
    sprite: Objects/Consumable/Food/Baked/nuggets.rsi
    layers:
      - state: tendie
        map: [ "enum.DamageStateVisualLayers.Base" ]
  - type: RandomSprite
    available:
      - enum.DamageStateVisualLayers.Base:
          tendie: ""
          lizard: ""
          star: ""
          corgi: ""
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 8
        reagents:
        - ReagentId: Nutriment
          Quantity: 2
        - ReagentId: Protein
          Quantity: 2
        - ReagentId: Vitamin
          Quantity: 1
  - type: FlavorProfile # DeltaV - custom flavor, see Resources/Locale/en-US/deltav/flavors/flavor_profiles.ftl
    flavors:
     - nuggie

# Waffles/Pancakes

# Once StackVisuals is updated you should be able to mix/match stacks of pancakes.

- type: entity
  name: "млинець"
  parent: FoodBakedBase
  id: FoodBakedPancake
  description: "Пухнастий млинець. М'якший, вищий родич вафель."
  components:
  - type: Stack
    stackType: Pancake
    count: 1
    composite: true
    layerStates:
    - pancakes1
    - pancakes2
    - pancakes3
    - pancakes4
    - pancakes5
    - pancakes6
    - pancakes7
    - pancakes8
    - pancakes9
  - type: Sprite
    state: pancakes1
    layers:
    - state: pancakes1
      map: ["pancakes1"]
      visible: false
    - state: pancakes2
      map: ["pancakes2"]
      visible: false
    - state: pancakes3
      map: ["pancakes3"]
      visible: false
    - state: pancakes4
      map: ["pancakes4"]
      visible: false
    - state: pancakes5
      map: ["pancakes5"]
      visible: false
    - state: pancakes6
      map: ["pancakes6"]
      visible: false
    - state: pancakes7
      map: ["pancakes7"]
      visible: false
    - state: pancakes8
      map: ["pancakes8"]
      visible: false
    - state: pancakes9
      map: ["pancakes9"]
      visible: false
  - type: Appearance
  - type: Tag
    tags:
    - Pancake

- type: entity
  name: "чорничний млинець"
  parent: FoodBakedBase
  id: FoodBakedPancakeBb
  description: "Пишний і смачний чорничний млинець."
  components:
  - type: Stack
    stackType: Pancake
    count: 1
    composite: true
    layerStates:
    - pancakesbb1
    - pancakesbb2
    - pancakesbb3
  - type: Sprite
    state: pancakesbb1
    layers:
    - state: pancakesbb1
      map: ["pancakesbb1"]
      visible: false
    - state: pancakesbb2
      map: ["pancakesbb2"]
      visible: false
    - state: pancakesbb3
      map: ["pancakesbb3"]
      visible: false
  - type: Appearance
  - type: Tag
    tags:
    - Pancake
    - Fruit

- type: entity
  name: "млинець з шоколадною крихтою"
  parent: FoodBakedBase
  id: FoodBakedPancakeCc
  description: "Пишний і смачний млинець з шоколадною крихтою."
  components:
  - type: Stack
    stackType: Pancake
    count: 1
    composite: true
    layerStates:
    - pancakescc1
    - pancakescc2
    - pancakescc3
  - type: Sprite
    state: pancakescc1
    layers:
    - state: pancakescc1
      map: ["pancakescc1"]
      visible: false
    - state: pancakescc2
      map: ["pancakescc2"]
      visible: false
    - state: pancakescc3
      map: ["pancakescc3"]
      visible: false
  - type: Appearance
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 6
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Theobromine
          Quantity: 1
  - type: Tag
    tags:
    - Pancake

- type: entity
  name: "вафлі"
  parent: FoodBakedBase
  id: FoodBakedWaffle
  description: "Вафлі."
  components:
  - type: Sprite
    layers:
      - state: tray
      - state: waffles
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Nutriment
          Quantity: 8
        - ReagentId: Vitamin
          Quantity: 1

- type: entity
  name: "соєві вафлі"
  parent: FoodBakedWaffle
  id: FoodBakedWaffleSoy
  description: "Ви відчуваєте себе здоровішою і - більш жіночною?"
  components:
  - type: Sprite
    layers:
      - state: tray
      - state: waffles-soy
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Vitamin
          Quantity: 1

- type: entity
  name: "соковиті вафлі"
  parent: FoodBakedWaffle
  id: FoodBakedWaffleSoylent
  description: "Не з людей. Чесно." # Definitely people
  components:
  - type: Sprite
    layers:
      - state: tray
      - state: waffles-soylent
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Vitamin
          Quantity: 1

- type: entity
  name: "вафлі рофле"
  parent: FoodBakedWaffle
  id: FoodBakedWaffleRoffle
  description: "Вафлі від Roffle. Co."
  components:
  - type: Sprite
    layers:
      - state: tray
      - state: waffles-roffle
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Nutriment
          Quantity: 8
        - ReagentId: Vitamin
          Quantity: 2

# Misc

- type: entity
  name: "маковий крендель"
  parent: FoodBakedBase
  id: FoodBakedPretzel
  description: "Все переплутано!"
  components:
  - type: Sprite
    state: pretzel

- type: entity
  name: "каннолі"
  parent: FoodBakedBase
  id: FoodBakedCannoli
  description: "Сицилійські ласощі, які перетворюють вас на мудреця."
  components:
  - type: Sprite
    state: cannoli
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Vitamin
          Quantity: 1

- type: entity
  name: "вареники"
  parent: FoodBakedBase
  id: FoodBakedDumplings
  description: "Середньостатистичний рецепт м'яса в тісті."
  components:
  - type: Sprite
    state: dumplings
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Nutriment
          Quantity: 8
        - ReagentId: Protein
          Quantity: 2

- type: entity
  name: "chèvre chaud"
  parent: FoodBakedBase
  id: FoodBakedChevreChaud
  description: "Диск злегка розтопленого шевру виклали зверху на кростіні і підсмажили по всьому периметру."
  components:
  - type: FlavorProfile
    flavors:
      - bread
      - nutty
      - creamy
      - smokey
  - type: Sprite
    state: chevrechaud
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 5
        reagents:
        - ReagentId: Nutriment
          Quantity: 2
        - ReagentId: Vitamin
          Quantity: 0.5

- type: entity
  name: "тістечка"
  parent: FoodBakedBase
  id: FoodBakedBrownieBatch
  description: "Сковорода з тістечками."
  components:
  - type: FlavorProfile
    flavors:
      - sweet
      - chocolate
  - type: Sprite
    sprite: Objects/Consumable/Food/Baked/brownie.rsi
    state: brownie-batch
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 78
        reagents:
        - ReagentId: Nutriment
          Quantity: 30
        - ReagentId: Theobromine
          Quantity: 18
  - type: SliceableFood
    count: 6
    slice: FoodBakedBrownie

- type: entity
  name: "тістечко"
  parent: FoodBakedBase
  id: FoodBakedBrownie
  suffix: Fresh
  description: "Свіжоспечений брауні."
  components:
  - type: FlavorProfile
    flavors:
      - sweet
      - chocolate
  - type: Sprite
    sprite: Objects/Consumable/Food/snacks.rsi
    state: mre-brownie-open
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 13
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Theobromine
          Quantity: 3
  - type: Tag
    tags:
    - Slice

- type: entity
  name: "особливі тістечка"
  parent: FoodBakedBase
  id: FoodBakedCannabisBrownieBatch
  description: "Каструля з \"особливими\" брауні."
  components:
  - type: FlavorProfile
    flavors:
      - sweet
      - magical
      - chocolate
  - type: Sprite
    sprite: Objects/Consumable/Food/Baked/brownie.rsi
    state: brownie-batch
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 228
        reagents:
        - ReagentId: Nutriment
          Quantity: 30
        - ReagentId: Theobromine
          Quantity: 18
        - ReagentId: THC
          Quantity: 150
  - type: SliceableFood
    count: 6
    slice: FoodBakedCannabisBrownie

- type: entity
  name: "особливе тістечко"
  parent: FoodBakedBase
  id: FoodBakedCannabisBrownie
  description: "Особливий\" брауні."
  components:
  - type: FlavorProfile
    flavors:
      - sweet
      - magical
      - chocolate
  - type: Sprite
    sprite: Objects/Consumable/Food/snacks.rsi
    state: mre-brownie-open
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 38
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Theobromine
          Quantity: 3
        - ReagentId: THC
          Quantity: 25
  - type: Tag
    tags:
    - Slice

- type: entity
  name: "кільця цибулі"
  parent: FoodBakedBase
  id: FoodOnionRings
  description: "Ви можете з'їсти його або зробити пропозицію коханій людині."
  components:
    - type: FlavorProfile
      flavors:
        - onion
        - oily
    - type: Sprite
      state: onionrings
    - type: SolutionContainerManager
      solutions:
        food:
          maxVol: 4
          reagents:
          - ReagentId: Nutriment
            Quantity: 1
          - ReagentId: Allicin
            Quantity: 1
          - ReagentId: Vitamin
            Quantity: 1

- type: entity
  name: "круасан"
  parent: FoodBakedBase
  id: FoodBakedCroissant
  description: "Масляниста, пластівчаста смакота."
  components:
  - type: FlavorProfile
    flavors:
      - bread
      - butter
  - type: Sprite
    state: croissant
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 7
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
        - ReagentId: Butter
          Quantity: 2
        - ReagentId: Vitamin
          Quantity: 1
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 0 # so the damage stats icon doesn't immediately give away the syndie ones