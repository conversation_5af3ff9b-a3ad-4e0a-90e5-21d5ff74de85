- type: entity
  id: Memorial
  parent: BaseStructure
  name: "меморіал"
  description: "Вшанування чогось."
  components:
  - type: Sprite
    sprite: Structures/Furniture/Memorials/generic_memorial.rsi
    state: memorial
  - type: Transform
    noRot: true
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.20"
        density: 85
        mask:
        - FullTileMask
        layer:
        - WallLayer
  - type: InteractionOutline

# At 7:30 AM on March 8th, 2017, <PERSON><PERSON><PERSON><PERSON>, a SS13 contributor, died in a fatal car crash on impact.
# Rest in peace, man. You did good work.
# When a contributor for SS13 dies, all codebases feel it and suffer.
# We may disagree on whether farts should be a thing, or what color to paint the bikeshed,
# but we are all contributors together.
# Goodbye, man. We'll miss you.
# This memorial has been designed for him and any future coders to perish.
- type: entity
  id: SS13Memorial
  parent: BaseStructure
  name: "Моги<PERSON>а невідомого працівника"
  description: "Тут спочиває невідомий працівник\nНевідомий ні по імені, ні за званням\nЧиї діяння не будуть забуті\n"
  components:
    - type: Sprite
      sprite: Structures/Furniture/Memorials/ss13_memorial.rsi
      state: memorial
      drawdepth: Mobs
      noRot: true
      offset: "0.0,0.5"
