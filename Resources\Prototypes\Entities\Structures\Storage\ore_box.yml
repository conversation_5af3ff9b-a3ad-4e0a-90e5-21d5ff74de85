﻿- type: entity
  id: OreBox
  name: "рудний ящик"
  description: "Великий контейнер для зберігання необробленої руди."
  parent: BaseStructureDynamic
  components:
  - type: StaticPrice
    price: 500
  - type: Anchorable
  - type: InteractionOutline
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: Wood
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:EmptyAllContainersBehaviour
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank1:
            min: 3
            max: 5
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Climbable

  # orebox-specific
  - type: Sprite
    noRot: true
    sprite: /Textures/Structures/Storage/orebox.rsi
    layers:
    - state: orebox
    - state: orebox-top
      map: [ top ]
      visible: true
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.StorageVisuals.Open:
        top:
          True: { visible: false }
          False: { visible: true }
  - type: Storage
    grid:
    - 0,0,19,9 # Lavaland Change
    maxItemSize: Normal
    storageOpenSound: /Audio/Effects/closetopen.ogg
    storageCloseSound: /Audio/Effects/closetclose.ogg
    quickInsert: true
    areaInsert: true
    whitelist:
      tags:
      - ArtifactFragment # Lavaland Change
      - Ore
  - type: UserInterface
    interfaces:
      enum.StorageUiKey.Key:
        type: StorageBoundUserInterface
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
        ents: [ ]
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.3
        # very not dense to make it easy to pull
        density: 20
        mask:
        - MachineMask
        layer:
        - MachineLayer
  # Lavaland Change Start
  - type: OreBag
  - type: MagnetPickup
  - type: ItemToggle
  # Lavaland Change End
