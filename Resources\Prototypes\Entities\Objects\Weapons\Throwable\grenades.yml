- type: entity
  abstract: true
  parent: BaseItem
  id: GrenadeBase
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/grenade.rsi
    layers:
    - state: icon
      map: ["enum.TriggerVisualLayers.Base"]
  - type: Item
    size: Small
  - type: Clothing
    quickEquip: false
    slots:
    - Belt
  - type: OnUseTimerTrigger
    delay: 3.5
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 10
      behaviors:
      - !type:TriggerBehavior
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Appearance
  - type: AnimationPlayer
  - type: GenericVisualizer
    visuals:
      enum.Trigger.TriggerVisuals.VisualState:
        enum.ConstructionVisuals.Layer:
          Primed: { state: primed }
          Unprimed: { state: icon }

- type: entity
  name: "розривна граната"
  description: "Граната, що створює невеликий, але руйнівний вибух."
  parent: GrenadeBase
  id: ExGrenade
  components:
  - type: ExplodeOnTrigger
  - type: Explosive
    explosionType: Default
    maxIntensity: 10
    intensitySlope: 3
    totalIntensity: 120 # about a ~4 tile radius
    canCreateVacuum: false
  - type: OnUseTimerTrigger
    beepSound:
      path: "/Audio/Effects/beep1.ogg"
      params:
        volume: 5
    initialBeepDelay: 0
    beepInterval: 2 # 2 beeps total (at 0 and 2)
  - type: SpawnOnTrigger # Pirate shock wave
    proto: ExplosionEffectGrenadeShockWave

- type: entity
  name: "світлошумова граната"
  description: "Флешбенг!! *піііііііііііііііііііііііі*"
  parent: GrenadeBase
  id: GrenadeFlashBang
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/flashbang.rsi
  - type: FlashOnTrigger
    range: 7
  - type: SoundOnTrigger
    sound:
      path: "/Audio/Effects/flash_bang.ogg"
  - type: DeleteOnTrigger
  - type: SpawnOnTrigger
    proto: GrenadeFlashEffect
  - type: Appearance
  - type: TimerTriggerVisuals
    primingSound:
      path: /Audio/Effects/countdown.ogg
  - type: GuideHelp
    guides:
    - Security
    - Antagonists
  - type: Flashbang # Goobstation

- type: entity
  id: GrenadeFlashEffect
  categories: [ HideSpawnMenu ]
  components:
    - type: PointLight
      enabled: true
      radius: 5
      energy: 8
      netsync: false
    - type: LightFade
      duration: 0.5
    - type: TimedDespawn
      lifetime: 0.5

#The explosive values for these are pretty god damn mediocre, but SS14's explosion system is hard to understand - this is a good enough approximation of how it was in SS13.
#Ideally, there should be a weak radius around the bomb outside of its gibbing / spacing range capable of dealing fair damage to players / structures.
- type: entity
  name: "синдикатська міні-бомба"
  description: "Вироблена синдикатом вибухівка, що використовується для руйнування та створення хаосу."
  parent: GrenadeBase
  id: SyndieMiniBomb
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/syndgrenade.rsi
  - type: OnUseTimerTrigger
    delay: 5
  - type: ExplodeOnTrigger
  - type: Explosive
    explosionType: Minibomb
    totalIntensity: 200
    intensitySlope: 30 #Will destroy the tile under it reliably, space 1-2 more to rods. Only does any significant damage in a 5-tile cross.
    maxIntensity: 60
  - type: Appearance
  - type: TimerTriggerVisuals
    primingSound:
      path: /Audio/Effects/minibombcountdown.ogg
      params:
        volume: 12
  - type: SpawnOnTrigger # Pirate shock wave
    proto: ExplosionEffectGrenadeShockWave

- type: entity
  name: "Самознищення"
  description: "Вмирайте на своїх власних умовах!"
  parent: GrenadeBase
  id: SelfDestructSeq
  categories: [ HideSpawnMenu ]
  components:
  - type: ExplodeOnTrigger
  - type: Explosive
    explosionType: Minibomb
    totalIntensity: 400
    intensitySlope: 30
    maxIntensity: 125
    canCreateVacuum: true
  - type: OnUseTimerTrigger
    delay: 4.5
    beepSound:
      path: /Audio/Effects/Grenades/SelfDestruct/SDS_Charge2.ogg
      params:
        volume: 30
    initialBeepDelay: 0
    beepInterval: 16


- type: entity
  name: "граната з надматерії"
  description: "Граната, яка імітує розшарування двигуна надматерії, стягуючи речі в купу і вибухаючи через деякий час."
  parent: GrenadeBase
  id: SupermatterGrenade
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/supermattergrenade.rsi
  - type: OnUseTimerTrigger
    delay: 3
    beepInterval: 0.46
    beepSound:
      path: /Audio/Effects/Grenades/Supermatter/smbeep.ogg
      params:
        volume: -5
  - type: TimerTriggerVisuals
    primingSound:
      path: /Audio/Effects/Grenades/Supermatter/smbeep.ogg
      params:
        volume: -5
  - type: Explosive
    explosionType: Default
    totalIntensity: 200
    intensitySlope: 30
    maxIntensity: 120
  - type: SoundOnTrigger
    removeOnTrigger: true
    sound:
      path: /Audio/Effects/Grenades/Supermatter/supermatter_start.ogg
      volume: 5
  - type: AnchorOnTrigger
    removeOnTrigger: true
  - type: TwoStageTrigger
    triggerDelay: 10.45
    components:
      - type: AmbientSound
        enabled: true
        volume: -5
        range: 14
        sound:
          path: /Audio/Effects/Grenades/Supermatter/supermatter_loop.ogg
      - type: GravityWell
        maxRange: 8
        baseRadialAcceleration: 145
        baseTangentialAcceleration: 5
        gravPulsePeriod: 0.01
      - type: SingularityDistortion
        intensity: 150
        falloffPower: 1.5
      - type: PointLight
        enabled: true
        color: "#ffaa44"
        energy: 8
        radius: 6
        softness: 1
        offset: "0, 0"
      - type: SoundOnTrigger
        sound:
          path: /Audio/Effects/Grenades/Supermatter/supermatter_end.ogg
          params:
            volume: 5
      - type: ExplodeOnTrigger

- type: entity
  name: "світлодірова граната"
  description: "Граната, яка на деякий час відбиває все навколо."
  parent: SupermatterGrenade
  id: WhiteholeGrenade
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/whiteholegrenade.rsi
  - type: OnUseTimerTrigger
    delay: 3
    beepInterval: 0.69
  - type: SoundOnTrigger
    removeOnTrigger: true
    sound:
      path: /Audio/Effects/Grenades/Supermatter/whitehole_start.ogg
      volume: 5
  - type: TwoStageTrigger
    triggerDelay: 11.14
    components:
      - type: AmbientSound
        enabled: true
        volume: -5
        range: 14
        sound:
          path: /Audio/Effects/Grenades/Supermatter/whitehole_loop.ogg
      - type: GravityWell
        maxRange: 10
        baseRadialAcceleration: -180
        baseTangentialAcceleration: -5
        gravPulsePeriod: 0.01
      - type: SingularityDistortion
        intensity: -200
        falloffPower: 1.5
      - type: PointLight
        enabled: true
        color: "#ffffff"
        energy: 8
        radius: 6
        softness: 1
        offset: "0, 0"
      - type: SoundOnTrigger
        sound:
          path: /Audio/Effects/Grenades/Supermatter/supermatter_end.ogg
          params:
            volume: 15
      - type: DeleteOnTrigger

- type: entity
  name: "ядерний варіант"
  description: "Будь ласка, не кидайте його, подумайте про дітей."
  parent: GrenadeBase
  id: NuclearGrenade
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/nukenade.rsi
  - type: OnUseTimerTrigger
    delay: 5
  - type: ExplodeOnTrigger
  - type: Explosive
    explosionType: Default
    totalIntensity: 20000 # ~15 tile radius.
    intensitySlope: 5
    maxIntensity: 50
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:TriggerBehavior
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Appearance
  - type: TimerTriggerVisuals
    primingSound:
      path: /Audio/Effects/countdown.ogg

- type: entity
  name: "модульна граната"
  description: "Корпус від гранати. Потрібен спусковий гачок і заряд."
  parent: BaseItem
  id: ModularGrenade
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/modular.rsi
    layers:
    - state: empty
      map: [ "enum.ConstructionVisuals.Layer" ]
  - type: Item
    size: Small
  - type: PayloadCase
  - type: Construction
    graph: ModularGrenadeGraph
    node: emptyCase
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:TriggerBehavior
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ConstructionVisuals.Key:
        enum.ConstructionVisuals.Layer:
          emptyCase: { state: empty }
          wiredCase: { state: wired }
          caseWithTrigger: { state: no-payload }
          grenade: { state: complete }
      enum.Trigger.TriggerVisuals.VisualState:
        enum.ConstructionVisuals.Layer:
          Primed: { state: primed }
          Unprimed: { state: complete }
  - type: StaticPrice
    price: 25

- type: entity
  name: "ЕМР-граната"
  description: "Граната, призначена для виведення з ладу електронних систем."
  parent: GrenadeBase
  id: EmpGrenade
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/empgrenade.rsi
  - type: EmpOnTrigger
    range: 4
    energyConsumption: 2700000
  - type: DeleteOnTrigger
  - type: Appearance
  - type: TimerTriggerVisuals
    primingSound:
      path: /Audio/Effects/countdown.ogg

- type: entity
  name: "свята ручна граната"
  description: "Господи, благослови цю ручну гранату Твою, щоб нею Ти міг рознести ворогів Твоїх на дрібні шматочки, по милості Твоїй."
  parent: GrenadeBase
  id: HolyHandGrenade
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/holyhandgrenade.rsi
  - type: ExplodeOnTrigger
  - type: Explosive
    explosionType: Default # same as macrobomb
    totalIntensity: 3500
    intensitySlope: 15
    maxIntensity: 70
    canCreateVacuum: true
  - type: OnUseTimerTrigger
    delay: 3 # by canon
  - type: PointLight
    radius: 7
    energy: 3
    netsync: false
  - type: TimerTriggerVisuals
    primingSound:
      path: /Audio/Effects/hallelujah.ogg
  - type: SpawnOnTrigger # Pirate shock wave
    proto: ExplosionEffectGrenadeShockWave

- type: entity
  parent: GrenadeBase
  id: SmokeGrenade
  name: "димова шашка"
  description: "Тактична граната, яка при використанні випускає велику, довготривалу хмару диму."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/smoke.rsi
  - type: SmokeOnTrigger
    duration: 30
    spreadAmount: 50
  - type: SoundOnTrigger
    sound: /Audio/Items/smoke_grenade_smoke.ogg
  - type: DeleteOnTrigger
  - type: TimerTriggerVisuals
    primingSound:
      path: /Audio/Items/smoke_grenade_prime.ogg

- type: entity
  parent: SmokeGrenade
  id: CleanerGrenade
  name: "чисткогранат"
  description: "Спеціальна граната для прибиральників, що випускає велику хмару піни для прибирання приміщень"
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/janitor.rsi
  - type: SmokeOnTrigger
    duration: 15
    spreadAmount: 50
    smokePrototype: Foam
    solution:
      reagents:
      - ReagentId: SpaceCleaner
        Quantity: 30

- type: entity
  parent: SmokeGrenade
  id: CleanerGrenadeNuke
  name: "чисте рішення"
  description: "Спеціальна чистяча граната, розроблена особливо завзятим прибиральником. Її піна може поширитися по всій кімнаті, залишаючи її бездоганно чистою і з легким запахом лимона."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/janitornuke.rsi
  - type: SmokeOnTrigger
    duration: 20
    spreadAmount: 200
    smokePrototype: Foam
    solution:
      reagents:
      - ReagentId: SpaceCleaner
        Quantity: 30

- type: entity
  parent: SmokeGrenade
  id: TearGasGrenade
  name: "граната зі сльозогінним газом"
  description: "Граната зі сльозогінним газом для боротьби з масовими заворушеннями. Викликає роздратування, біль і викликає сльози на очах."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/tear_gas.rsi
  - type: SmokeOnTrigger
    duration: 10
    spreadAmount: 30
    solution:
      reagents:
      - ReagentId: TearGas
        Quantity: 50

# Non-explosive "dummy" grenades to use as a distraction.

- type: entity
  name: "граната-обманка"
  description: "Гранате без бабаху."
  parent: GrenadeBase
  id: GrenadeDummy
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/grenade.rsi
  - type: DeleteOnTrigger
  - type: SpawnOnTrigger
    proto: GrenadeFlashEffect
  - type: SoundOnTrigger
    sound:
      path: /Audio/Effects/Emotes/parp1.ogg
  - type: Appearance
  - type: TimerTriggerVisuals
    primingSound:
      path: /Audio/Effects/countdown.ogg

- type: entity
  name: "відволікаюча бомба синдикату"
  description: "Вибухівка, виготовлена синдикатом, стала чудовим відволікаючим маневром."
  parent: GrenadeDummy
  id: SyndieTrickyBomb
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/syndgrenade.rsi
  - type: SoundOnTrigger
    sound:
      path: /Audio/Effects/Emotes/parp1.ogg
  - type: Appearance
  - type: TimerTriggerVisuals
    primingSound:
      path: /Audio/Effects/minibombcountdown.ogg
      params:
        volume: 12
