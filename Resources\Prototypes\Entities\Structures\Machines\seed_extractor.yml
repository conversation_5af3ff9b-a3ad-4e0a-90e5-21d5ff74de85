﻿- type: entity
  id: SeedExtractor
  name: "витяжка насіння"
  parent: [ BaseMachinePowered, ConstructibleMachine ]
  description: "Витягує насіння з продуктів."
  components:
  - type: Sprite
    sprite: Structures/Machines/seed_extractor.rsi
    snapCardinals: true
    layers:
    - state: seedextractor-off
    - state: seedextractor-unlit
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.4,0.25,0.4"
        density: 190
        mask:
        - MachineMask
        layer:
        - MachineLayer
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.PowerDeviceVisuals.Powered:
        enum.PowerDeviceVisualLayers.Powered:
          True: { visible: true }
          False: { visible: false }
  - type: SeedExtractor
  - type: Machine
    board: SeedExtractorMachineCircuitboard
  - type: UpgradePowerDraw
    powerDrawMultiplier: 0.75
    scaling: Exponential
