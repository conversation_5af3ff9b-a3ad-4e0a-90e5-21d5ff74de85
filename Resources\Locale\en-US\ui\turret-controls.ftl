# Headings
turret-controls-window-title = Automonous Defense Control System
turret-controls-window-turret-status-label = Linked devices [{$count}]
turret-controls-window-armament-controls-label = Armament setting
turret-controls-window-targeting-controls-label = Authorized personnel

# Status reports
turret-controls-window-no-turrets = <! No linked devices !>
turret-controls-window-turret-status = » {$device} - Status: {$status}
turret-controls-window-turret-disabled = ***OFFLINE***
turret-controls-window-turret-retracted = INACTIVE
turret-controls-window-turret-retracting = DEACTIVATING
turret-controls-window-turret-deployed = SEARCHING...
turret-controls-window-turret-deploying = ACTIVATING
turret-controls-window-turret-firing = ENGAGING TARGET
turret-controls-window-turret-error = ERROR [404]

# Buttons
turret-controls-window-safe = Inactive
turret-controls-window-stun = Stun
turret-controls-window-lethal = Lethal
turret-controls-window-ignore = Ignore
turret-controls-window-target = Target
turret-controls-window-access-group-label = {$prefix} {$label}
turret-controls-window-all-checkbox = All

# Flavor
turret-controls-window-footer = Unauthorized personnel should ensure defenses are inactive before proceeding

# Warnings
turret-controls-access-denied = Access denied