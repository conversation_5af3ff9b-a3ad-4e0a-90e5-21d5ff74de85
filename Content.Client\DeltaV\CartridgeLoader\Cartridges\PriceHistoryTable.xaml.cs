using System.Linq;
using Robust.Client.AutoGenerated;
using Robust.Client.Graphics;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.DeltaV.CartridgeLoader.Cartridges;

[GenerateTypedNameReferences]
public sealed partial class PriceHistoryTable : BoxContainer
{
    public PriceHistoryTable()
    {
        RobustXamlLoader.Load(this);

        // Create the stylebox here so we can use the colors from StockTradingUi
        var styleBox = new StyleBoxFlat
        {
            BackgroundColor = StockTradingUiFragment.PriceBackgroundColor,
            ContentMarginLeftOverride = 6,
            ContentMarginRightOverride = 6,
            ContentMarginTopOverride = 4,
            ContentMarginBottomOverride = 4,
            BorderColor = StockTradingUiFragment.BorderColor,
            BorderThickness = new Thickness(1),
        };

        HistoryPanel.PanelOverride = styleBox;
    }

    public void Update(List<float> priceHistory)
    {
        PriceGrid.RemoveAllChildren();

        // Take last 5 prices
        var lastFivePrices = priceHistory.TakeLast(5).ToList();

        for (var i = 0; i < lastFivePrices.Count; i++)
        {
            var price = lastFivePrices[i];
            var previousPrice = i > 0 ? lastFivePrices[i - 1] : price;
            var priceChange = ((price - previousPrice) / previousPrice) * 100;

            var entryContainer = new BoxContainer
            {
                Orientation = LayoutOrientation.Vertical,
                MinWidth = 80,
                HorizontalAlignment = HAlignment.Center,
            };

            var priceLabel = new Label
            {
                Text = $"${price:F2}",
                HorizontalAlignment = HAlignment.Center,
            };

            var changeLabel = new Label
            {
                Text = $"{(priceChange >= 0 ? "+" : "")}{priceChange:F2}%",
                HorizontalAlignment = HAlignment.Center,
                StyleClasses = { "LabelSubText" },
                Modulate = priceChange switch
                {
                    > 0 => StockTradingUiFragment.PositiveColor,
                    < 0 => StockTradingUiFragment.NegativeColor,
                    _ => StockTradingUiFragment.NeutralColor,
                }
            };

            entryContainer.AddChild(priceLabel);
            entryContainer.AddChild(changeLabel);
            PriceGrid.AddChild(entryContainer);
        }
    }
}
