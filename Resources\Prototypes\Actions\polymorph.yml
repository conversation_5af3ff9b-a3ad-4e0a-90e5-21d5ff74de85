- type: entity
  id: ActionRevertPolymorph
  name: "Повернути назад"
  description: "Поверніться до початкової форми."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    event: !type:RevertPolymorphActionEvent

- type: entity
  id: ActionPolymorph
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    event: !type:PolymorphActionEvent
    itemIconStyle: NoItem

- type: entity
  id: ActionPolymorphWizardSpider
  name: "Поліморф павука"
  description: "Поліморфує тебе на павука."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    useDelay: 60
    event: !type:PolymorphActionEvent
      protoId: WizardSpider
    itemIconStyle: NoItem
    icon:
      sprite: Mobs/Animals/spider.rsi
      state: tarantula

- type: entity
  id: ActionPolymorphWizardRod
  name: "Форма стрижня"
  description: "БРЯЗКІТ!"
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    raiseOnUser: true # Goobstation
    checkCanInteract: false # Goobstation
    useDelay: 25 # Goob edit
    event: !type:PolymorphSpellEvent # Goob edit
      speech: action-speech-spell-rod-form # Goobstation
      protoId: WizardRod
    itemIconStyle: NoItem
    icon:
      sprite: _Goobstation/Wizard/actions.rsi # Goob edit
      state: immrod # Goob edit
  - type: Magic # Goobstation
    requiresClothes: true
    requiresSpeech: true
    school: Transmutation
  - type: ActionUpgrade
    effectedLevels:
      2: ActionPolymorphWizardRodII
      3: ActionPolymorphWizardRodIII
      4: ActionPolymorphWizardRodIV
      5: ActionPolymorphWizardRodV

- type: entity
  id: ActionPolymorphJaunt
  name: "Безтілесна прогулянка"
  description: "Розчинитися в ефірній площині для швидкої втечі!"
  components:
  - type: Magic
    requiresClothes: true # Goobstation
    school: Translocation # Goobstation
  - type: Sprite # Goobstation - for apprentices
    sprite: _Goobstation/Wizard/actions.rsi
    state: jaunt
  - type: InstantAction
    useDelay: 30
    raiseOnUser: true # Goobstation
    checkCanInteract: false # Goobstation
    event: !type:PolymorphSpellEvent # Goob edit
      protoId: Jaunt
      makeWizard: false # Goobstation
    itemIconStyle: NoItem
    icon:
      sprite: _Goobstation/Wizard/actions.rsi # Goob edit
      state: jaunt
    # TODO: Effect ECS (from cardboard box)
  - type: ActionUpgrade
    effectedLevels:
      2: ActionPolymorphJauntII
      3: ActionPolymorphJauntIII

- type: entity
  id: ActionPolymorphJauntII
  parent: ActionPolymorphJaunt
  name: "Ефірний похід II"
  description: "Розчинитися в ефірній площині, щоб ще швидше втекти!"
  components:
  - type: InstantAction
    useDelay: 22 # Goob edit
    raiseOnUser: true # Goobstation
    checkCanInteract: false # Goobstation
    event: !type:PolymorphSpellEvent # Goob edit
      protoId: Jaunt
      makeWizard: false # Goobstation
    itemIconStyle: NoItem
    icon:
      sprite: _Goobstation/Wizard/actions.rsi # Goob edit
      state: jaunt

- type: entity
  id: ActionPolymorphJauntIII
  parent: ActionPolymorphJaunt
  name: "Ефірний похід III"
  description: "Ти вже взагалі відчутний?"
  components:
  - type: InstantAction
    useDelay: 14 # Goob edit
    raiseOnUser: true # Goobstation
    checkCanInteract: false # Goobstation
    event: !type:PolymorphSpellEvent # Goob edit
      protoId: Jaunt
      makeWizard: false # Goobstation
    itemIconStyle: NoItem
    icon:
      sprite: _Goobstation/Wizard/actions.rsi # Goob edit
      state: jaunt
