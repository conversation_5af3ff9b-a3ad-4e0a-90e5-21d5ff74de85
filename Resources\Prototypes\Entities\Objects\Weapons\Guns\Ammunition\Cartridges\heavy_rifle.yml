- type: entity
  id: BaseCartridgeHeavyRifle
  name: "патрон (гвинтівка .20)"
  parent: BaseCartridge
  abstract: true
  components:
  - type: Tag
    tags:
      - Cartridge
      - CartridgeHeavyRifle
  - type: CartridgeAmmo
    proto: BulletHeavyRifle
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Casings/ammo_casing.rsi
    layers:
    - state: base
      map: ["enum.AmmoVisualLayers.Base"]
  - type: Appearance
  - type: SpentAmmoVisuals
  - type: StaticPrice
    price: 10

- type: entity
  id: CartridgeMinigun
  name: "патрон (гвинтівка .10)"
  parent: BaseCartridgeHeavyRifle
  components:
  - type: CartridgeAmmo
    proto: BulletMinigun
    deleteOnSpawn: true
