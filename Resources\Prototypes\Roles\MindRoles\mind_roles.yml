- type: entity
  id: BaseMindRole
  name: "Роль Розуму"
  description: "Сутність Ролі Розуму"
  abstract: true
  components:
  - type: MindRole

- type: entity
  parent: BaseMindRole
  id: BaseMindRoleAntag
  abstract: true
  components:
  - type: MindRole
    antag: true

#Observer
- type: entity
  parent: BaseMindRole
  id: MindRoleObserver
  name: "Роль Спостерігача"
  components:
  - type: ObserverRole

#Ghost Roles
- type: entity
  parent: BaseMindRole
  id: MindRoleGhostRoleNeutral
  name: "Роль Привида"
  components:
  - type: GhostRoleMarkerRole

- type: entity
  parent: BaseMindRole
  id: MindRoleGhostRoleFamiliar
  name: "Роль Привида (Фамільяр)"
  components:
  - type: MindRole
    roleType: Familiar
  - type: GhostRoleMarkerRole

- type: entity
  parent: BaseMindRole
  id: MindRoleGhostRoleFreeAgent
  name: "Роль Привида (Вільний Агент)"
  components:
  - type: MindRole
    roleType: FreeAgent
  - type: GhostRoleMarkerRole

- type: entity
  parent: BaseMindRole
  id: MindRoleGhostRoleSilicon
  name: "Роль Привида (Кремній)"
  components:
  - type: MindRole
    roleType: Silicon
  - type: GhostRoleMarkerRole

- type: entity
  parent: BaseMindRole
  id: MindRoleGhostRoleSiliconAntagonist
  name: "Роль Привида (Антагоніст Кремнію)"
  components:
  - type: MindRole
    roleType: SiliconAntagonist
  - type: GhostRoleMarkerRole

- type: entity
  parent: BaseMindRole
  id: MindRoleGhostRoleSoloAntagonist
  name: "Роль Привида (Соло Антагоніст)"
  components:
  - type: MindRole
    roleType: SoloAntagonist
  - type: GhostRoleMarkerRole

- type: entity
  parent: BaseMindRole
  id: MindRoleGhostRoleTeamAntagonist
  name: "Роль Привида (Командний Антагоніст)"
  components:
  - type: MindRole
    roleType: TeamAntagonist
  - type: GhostRoleMarkerRole



# The Job MindRole holds the mob's Job prototype
- type: entity
  parent: BaseMindRole
  id: MindRoleJob
  name: "Роль Роботи"
#  description:
  # MindRoleComponent.JobPrototype is filled by SharedJobSystem

# Silicon
- type: entity
  parent: BaseMindRole
  id: MindRoleSiliconBrain
  name: "Роль Мозку Борга"
  components:
  - type: MindRole
    roleType: Silicon
  - type: SiliconBrainRole

- type: entity
  parent: BaseMindRoleAntag
  id: MindRoleSubvertedSilicon
  name: "Роль Підкореного Кремнію"
  description:
  components:
  - type: MindRole
    antagPrototype: SubvertedSilicon
    roleType: SiliconAntagonist
  - type: SubvertedSiliconRole

# Dragon
- type: entity
  parent: BaseMindRoleAntag
  id: MindRoleDragon
  name: "Роль Дракона"
#  description:
  components:
  - type: MindRole
    antagPrototype: Dragon
    roleType: TeamAntagonist
    exclusiveAntag: true
  - type: DragonRole
  - type: RoleBriefing
    briefing: dragon-role-briefing

# Ninja
- type: entity
  parent: BaseMindRoleAntag
  id: MindRoleNinja
  name: "Роль Космічного Ніндзя"
#  description: mind-role-ninja-description
  components:
  - type: MindRole
    antagPrototype: SpaceNinja
    roleType: SoloAntagonist
    exclusiveAntag: true
  - type: NinjaRole

# Nukies
- type: entity
  parent: BaseMindRoleAntag
  id: MindRoleNukeops
  name: "Роль Оперативника Ядерного Удару"
#  description: mind-role-nukeops-description
  components:
  - type: MindRole
    roleType: TeamAntagonist
    exclusiveAntag: true
    antagPrototype: Nukeops
  - type: NukeopsRole

- type: entity
  parent: MindRoleNukeops
  id: MindRoleNukeopsMedic
  name: "Роль Медика Ядерного Удару"
#  description: mind-role-nukeops-medic-description
  components:
  - type: MindRole
    antagPrototype: NukeopsMedic

- type: entity
  parent: MindRoleNukeops
  id: MindRoleNukeopsCommander
  name: "Роль Командира Ядерного Удару"
#  description: mind-role-nukeops-commander-description
  components:
  - type: MindRole
    antagPrototype: NukeopsCommander

# Revolutionaries
- type: entity
  parent: BaseMindRoleAntag
  id: MindRoleHeadRevolutionary
  name: "Роль Голови Революціонерів"
#  description: mind-role-head-revolutionary-description
  components:
  - type: MindRole
    antagPrototype: HeadRev
    exclusiveAntag: true
    roleType: TeamAntagonist
  - type: RevolutionaryRole

- type: entity
  parent: MindRoleHeadRevolutionary
  id: MindRoleRevolutionary
  name: "Роль Революціонера"
#  description: mind-role-revolutionary-description
  components:
  - type: MindRole
    antagPrototype: Rev

# Thief
- type: entity
  parent: BaseMindRoleAntag
  id: MindRoleThief
  name: "Роль Злодія"
#  description: mind-role-thief-description
  components:
  - type: MindRole
    antagPrototype: Thief
    roleType: SoloAntagonist
  - type: ThiefRole

# Traitors
- type: entity
  parent: BaseMindRoleAntag
  id: MindRoleTraitor
  name: "Роль Зрадника"
#  description: mind-role-traitor-description
  components:
  - type: MindRole
    antagPrototype: Traitor
    exclusiveAntag: true
    roleType: SoloAntagonist
  - type: TraitorRole

- type: entity
  parent: MindRoleTraitor
  id: MindRoleTraitorSleeper
  name: "Роль Сплячого Агента"
#  description: mind-role-traitor-sleeper-description
  components:
  - type: MindRole
    antagPrototype: TraitorSleeper

- type: entity
  parent: MindRoleTraitor
  id: MindRoleTraitorReinforcement
  name: "Роль Підкріплення Синдикату"
  #  description: mind-role-syndicate-reinforcement-description
  components:
    - type: MindRole
      roleType: TeamAntagonist

# Zombie Squad
- type: entity
  parent: BaseMindRoleAntag
  id: MindRoleInitialInfected
  name: "Роль Початкового Інфікованого"
#  description: mind-role-initial-infected-description
  components:
  - type: MindRole
    antagPrototype: InitialInfected
    exclusiveAntag: true
    roleType: TeamAntagonist
  - type: InitialInfectedRole

- type: entity
  parent: BaseMindRoleAntag
  id: MindRoleZombie
  name: "Роль Зомбі"
#  description: mind-role-zombie-description
  components:
  - type: MindRole
    antagPrototype: Zombie
    exclusiveAntag: true
    roleType: TeamAntagonist
  - type: ZombieRole

# goob edit - antags
- type: entity
  parent: BaseMindRoleAntag
  id: MindRoleChangeling
  name: "Роль Перевертня"
  components:
  - type: MindRole
    antagPrototype: Changeling
    exclusiveAntag: true
    roleType: SoloAntagonist
  - type: ChangelingRole

# shadowling
- type: entity
  parent: BaseMindRoleAntag
  id: MindRoleShadowling
  name: "Роль Тіньовика"
  components:
  - type: MindRole
    antagPrototype: Shadowling
    exclusiveAntag: true
    roleType: TeamAntagonist
  - type: ShadowlingRole

- type: entity
  parent: MindRoleShadowling
  id: MindRoleThrall
  name: "Роль Поневоленого"
  components:
  - type: MindRole
    antagPrototype: Thrall

#PIRATE START UNTIL END OF FILE
- type: entity
  parent: BaseMindRoleAntag
  id: MindRoleHeretic
  name: "Роль Єретика"
  components:
  - type: MindRole
    antagPrototype: Heretic
    exclusiveAntag: true
  - type: HereticRole
  
- type: entity
  parent: BaseMindRoleAntag
  id: MindRoleVampire
  name: "Роль Вампіра"
  components:
  - type: MindRole
    antagPrototype: Vampire
  - type: VampireRole