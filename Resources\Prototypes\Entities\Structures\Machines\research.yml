﻿- type: entity
  id: ResearchAndDevelopmentServer
  parent: [ BaseMachinePowered, ConstructibleMachine ]
  name: "<PERSON>&<PERSON> сервер"
  description: "Містить колективні знання науковців станції. Знищення його відкине їх назад у кам'яний вік. Ти ж не хочеш цього?"
  components:
  - type: Sprite
    sprite: Structures/Machines/server.rsi
    layers:
      - state: server
      - state: variant-research
      - state: server_o
        map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: ResearchServer
  - type: TechnologyDatabase
    supportedDisciplines:
    - Industrial
    - Arsenal
    - Experimental
    - CivilianServices
  - type: ApcPowerReceiver
    powerLoad: 200
  - type: ExtensionCableReceiver
  - type: WiresPanel
  - type: WiresVisuals
  - type: Machine
    board: ResearchAndDevelopmentServerMachineCircuitboard
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 600
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 2
  - type: Appearance
  - type: AmbientSound
    volume: -9
    range: 5
    sound:
      path: /Audio/Ambience/Objects/server_fans.ogg
  - type: GuideHelp
    guides:
    - Science

- type: entity
  id: BaseResearchAndDevelopmentPointSource
  parent: BaseMachinePowered
  name: "базове точкове джерело досліджень і розробок"
  # We should make this abstract once there are actual point sources.
  components:
  - type: Sprite
    sprite: Structures/Machines/rndpointsource.rsi
    layers:
    - state: rndpointsource-off
    - state: rndpointsource
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
  - type: ResearchClient
  - type: ResearchPointSource
    pointspersecond: 25
    active: true
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#3db83b"
  - type: ActivatableUI
    key: enum.ResearchClientUiKey.Key
  - type: ActivatableUIRequiresPower
  - type: UserInterface
    interfaces:
      enum.ResearchClientUiKey.Key:
        type: ResearchClientBoundUserInterface
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.PowerDeviceVisuals.Powered:
        enum.PowerDeviceVisualLayers.Powered:
          True: {visible: true}
          False: {visible: false}
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 400
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 1
  - type: GuideHelp
    guides:
    - Science
