- type: entity
  abstract: true
  parent: BaseMagazineBox
  id: BaseMagazineBoxLightRifle
  name: "коробочка з набоями (гвинтівка калібру .30)"
  components:
  - type: BallisticAmmoProvider
    mayTransfer: true
    whitelist:
      tags:
        - CartridgeLightRifle
    proto: CartridgeLightRifle
    capacity: 60
  - type: Item
    size: Small
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Boxes/light_rifle.rsi
  - type: MagazineVisuals
    magState: mag
    steps: 2
    zeroVisible: false
  - type: Appearance

# Boxes
- type: entity
  parent: BaseMagazineBoxLightRifle
  id: MagazineBoxLightRifleBig
  name: "велика коробка боєприпасів (.30 гвинтівковий)"
  components:
  - type: BallisticAmmoProvider
    capacity: 200
    proto: CartridgeLightRifle
  - type: Sprite
    layers:
    - state: base-b
      map: ["enum.GunVisualLayers.Base"]
    - state: magb-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: magb
    steps: 2
    zeroVisible: false
  - type: Appearance

- type: entity
  parent: MagazineBoxLightRifleBig
  id: MagazineBoxLightRifleBigPractice
  name: "велика коробка боєприпасів (.30 гвинтівковий, тренувальний)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRiflePractice

- type: entity
  parent: MagazineBoxLightRifleBig
  id: MagazineBoxLightRifleBigRubber
  name: "велика коробка боєприпасів (.30 гвинтівковий гумовий)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifleRubber

- type: entity
  parent: MagazineBoxLightRifleBig
  id: MagazineBoxLightRifleBigIncendiary
  name: "велика коробка боєприпасів (.30 гвинтівковий запальний)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifleIncendiary

- type: entity
  parent: MagazineBoxLightRifleBig
  id: MagazineBoxLightRifleBigUranium
  name: "велика коробка боєприпасів (.30 гвинтівковий, урановий)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifleUranium

- type: entity
  parent: MagazineBoxLightRifleBig
  id: ********************************
  name: "велика коробка боєприпасів (.30 гвинтівковий, шрапнельний)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifleShrapnel

- type: entity
  parent: BaseMagazineBoxLightRifle
  id: MagazineBoxLightRifle
  name: "коробочка з набоями (гвинтівка калібру .30)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifle
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  parent: BaseMagazineBoxLightRifle
  id: MagazineBoxLightRiflePractice
  name: "коробочка з набоями (стрільба з гвинтівки калібру .30)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRiflePractice
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: practice

- type: entity
  parent: BaseMagazineBoxLightRifle
  id: MagazineBoxLightRifleRubber
  name: "коробка боєприпасів (.30 гвинтівковий гумовий)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifleRubber
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: rubber

- type: entity
  id: MagazineBoxLightRifleIncendiary
  parent: BaseMagazineBoxLightRifle
  name: "коробка боєприпасів (.30 гвинтівковий запальний)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifleIncendiary
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: incendiary

- type: entity
  parent: BaseMagazineBoxLightRifle
  id: MagazineBoxLightRifleUranium
  name: "коробочка з набоями (уран для гвинтівок калібру .30)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifleUranium
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: uranium

- type: entity
  parent: MagazineBoxLightRifle
  id: MagazineBoxLightRifleShrapnel
  name: "коробка боєприпасів (.30 гвинтівковий, шрапнельний)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifleShrapnel
