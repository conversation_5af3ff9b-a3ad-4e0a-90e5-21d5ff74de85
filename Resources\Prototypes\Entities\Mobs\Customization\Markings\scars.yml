- type: marking
  id: ScarEyeRight
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Rodentia, Lamia, Tajaran] # Delta V - Felinid, <PERSON><PERSON>, <PERSON><PERSON><PERSON>; Einstein Engines - Tajaran
  followSkinColor: true
  sprites:
  - sprite: Mobs/Customization/scars.rsi
    state: scar_eye_right

- type: marking
  id: ScarEyeLeft
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [Human, Dwar<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Arachne, Rodentia, Lamia, Tajaran] # Delta V - Felinid, <PERSON>i, <PERSON><PERSON><PERSON>; Einstein Engines - Tajaran
  followSkinColor: true
  sprites:
  - sprite: Mobs/Customization/scars.rsi
    state: scar_eye_left

- type: marking
  id: ScarTopSurgeryShort
  bodyPart: Chest
  markingCategory: Chest
  speciesRestriction: [Human, Dwarf, Felinid, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Tajaran] # Einstein Engines - Tajaran
  sexRestriction: [Male]
  followSkinColor: true
  sprites:
  - sprite: Mobs/Customization/scars.rsi
    state: scar_top_surgery_short

- type: marking
  id: ScarTopSurgeryLong
  bodyPart: Chest
  markingCategory: Chest
  speciesRestriction: [Human, Dwarf, Felinid,  Oni, Arachne, Lamia, Tajaran] # Einstein Engines - Tajaran
  sexRestriction: [Male]
  followSkinColor: true
  sprites:
  - sprite: Mobs/Customization/scars.rsi
    state: scar_top_surgery_long

- type: marking
  id: ScarChest
  bodyPart: Chest
  markingCategory: Chest
  speciesRestriction: [Human, Dwarf, Felinid, Oni, Arachne, Lamia, Tajaran] # Einstein Engines - Tajaran
  followSkinColor: true
  sprites:
  - sprite: Mobs/Customization/scars.rsi
    state: scar_chest
