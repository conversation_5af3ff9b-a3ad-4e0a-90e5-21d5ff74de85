﻿using Content.Shared.Administration.Events;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Console;

namespace Content.Client.Administration.UI.Tabs.PanicBunkerTab;

[GenerateTypedNameReferences]
public sealed partial class PanicBunkerTab : Control
{
    [Dependency] private readonly IConsoleHost _console = default!;

    private string _minAccountAge;
    private string _minOverallHours;

    public PanicBunkerTab()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        DisableAutomaticallyButton.ToolTip = Loc.GetString("admin-ui-panic-bunker-disable-automatically-tooltip");

        MinAccountAge.OnTextEntered += args => SendMinAccountAge(args.Text);
        MinAccountAge.OnFocusExit += args => SendMinAccountAge(args.Text);
        _minAccountAge = MinAccountAge.Text;

        MinOverallHours.OnTextEntered += args => SendMinOverallHours(args.Text);
        MinOverallHours.OnFocusExit += args => SendMinOverallHours(args.Text);
        _minOverallHours = MinOverallHours.Text;
    }

    private void SendMinAccountAge(string text)
    {
        if (string.IsNullOrWhiteSpace(text) ||
            text == _minAccountAge ||
            !int.TryParse(text, out var minutes))
        {
            return;
        }

        _console.ExecuteCommand($"panicbunker_min_account_age {minutes}");
    }

    private void SendMinOverallHours(string text)
    {
        if (string.IsNullOrWhiteSpace(text) ||
            text == _minOverallHours ||
            !int.TryParse(text, out var hours))
        {
            return;
        }

        _console.ExecuteCommand($"panicbunker_min_overall_hours {hours}");
    }

    public void UpdateStatus(PanicBunkerStatus status)
    {
        EnabledButton.Pressed = status.Enabled;
        EnabledButton.Text = Loc.GetString(status.Enabled
            ? "admin-ui-panic-bunker-enabled"
            : "admin-ui-panic-bunker-disabled"
        );
        EnabledButton.ModulateSelfOverride = status.Enabled ? Color.Red : null;

        DisableAutomaticallyButton.Pressed = status.DisableWithAdmins;
        EnableAutomaticallyButton.Pressed = status.EnableWithoutAdmins;
        CountDeadminnedButton.Pressed = status.CountDeadminnedAdmins;
        ShowReasonButton.Pressed = status.ShowReason;

        MinAccountAge.Text = status.MinAccountAgeHours.ToString();
        _minAccountAge = MinAccountAge.Text;

        MinOverallHours.Text = status.MinOverallHours.ToString();
        _minOverallHours = MinOverallHours.Text;
    }
}
