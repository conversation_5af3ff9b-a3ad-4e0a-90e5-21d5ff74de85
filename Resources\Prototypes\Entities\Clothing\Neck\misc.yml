- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckHeadphones
  name: "навушники"
  description: "Якісні навушники від Drunk Masters, з хорошою звукоізоляцією."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Misc/headphones.rsi
    layers:
    - state: icon
      map: [ "enum.ToggleVisuals.Layer" ]
  - type: Clothing
    equippedPrefix: off
    sprite: Clothing/Neck/Misc/headphones.rsi
  - type: ToggleableLightVisuals
    spriteLayer: enum.ToggleVisuals.Layer
    clothingVisuals:
      neck:
      - state: on-equipped-NECK
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ToggleVisuals.Toggled:
        enum.ToggleVisuals.Layer:
          True: {state: icon-on}
          False: {state: icon}
  - type: ItemToggle
    predictable: false
    soundActivate:
      path: /Audio/Items/flashlight_on.ogg
    soundDeactivate:
      path: /Audio/Items/flashlight_off.ogg

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckStethoscope
  name: "стетоскоп"
  description: "Застарілий медичний апарат для прослуховування звуків людського тіла. Воно також створює враження, що ви знаєте, що робите."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Misc/stethoscope.rsi
  - type: Clothing
    sprite: Clothing/Neck/Misc/stethoscope.rsi
  - type: Stethoscope

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckBling
  name: "блискітка"
  description: "Чорт, як добре бути гангстером."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Misc/bling.rsi
  - type: Clothing
    sprite: Clothing/Neck/Misc/bling.rsi

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckLawyerbadge
  name: "значок адвоката"
  description: "Значок, який показує, що власник є \"легітимним\" адвокатом, який склав іспит Нанотрейзен на право займатися адвокатською діяльністю."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Misc/lawyerbadge.rsi
  - type: Clothing
    sprite: Clothing/Neck/Misc/lawyerbadge.rsi
  - type: TypingIndicatorClothing
    proto: lawyer

- type: entity
  id: ActionStethoscope
  name: "Слухати за допомогою стетоскопа"
  categories: [ HideSpawnMenu ]
  components:
  - type: EntityTargetAction
    icon:
      sprite: Clothing/Neck/Misc/stethoscope.rsi
      state: icon
    event: !type:StethoscopeActionEvent
    checkCanInteract: false
    priority: -1

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckBellCollar
  name: "нашийник-дзвіночок"
  description: "Спосіб повідомити оточуючим про свою присутність або просто дратувати всіх навколо!"
  components:
  - type: Sprite
    sprite: Clothing/Neck/Misc/bellcollar.rsi
  - type: Clothing
    sprite: Clothing/Neck/Misc/bellcollar.rsi
  - type: EmitsSoundOnMove
    soundCollection:
      collection: FootstepJester

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckDogtags
  name: "мітки"
  description: "Пара вигравіруваних металевих ідентифікаційних бирок."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Misc/dogtags.rsi
  - type: Clothing
    sprite: Clothing/Neck/Misc/dogtags.rsi
