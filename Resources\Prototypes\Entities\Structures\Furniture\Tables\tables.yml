- type: entity
  id: TableFrame
  # BaseStructure and not BaseTable, since these shouldn't be climbable/placeable.
  parent: BaseStructure
  name: "каркас столу"
  description: "Шматки металу, з яких складається каркас столу."
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/frame.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/frame.rsi
    state: full
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.40,-0.30,0.40,0.45"
        density: 80
        mask:
        - TableMask
        layer:
        - TableLayer
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: IconSmooth
    key: state
    base: state_
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 25
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 5
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
          params:
            volume: -6
      - !type:SpawnEntitiesBehavior
        spawn:
          PartRodMetal1:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: Table
    node: TableFrame

- type: entity
  id: CounterWoodFrame
  parent: BaseStructure
  name: "дерев'яна рама прилавка"
  description: "Шматки дерева, з яких складається каркас столу."
  components:
  - type: SpriteFade
  - type: Sprite
    sprite: Structures/Furniture/Tables/frame.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/frame.rsi
    state: full
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.45"
        density: 55
        mask:
        - TableMask
        layer:
        - TableLayer
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Wood
  - type: IconSmooth
    key: state
    base: state_
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 15
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank1:
            min: 0
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: Table
    node: CounterWoodFrame

- type: entity
  id: CounterMetalFrame
  parent: CounterWoodFrame
  name: "металевий каркас прилавка"
  description: "Шматки металу, з яких складається каркас столу."
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/frame.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/frame.rsi
    state: full
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: IconSmooth
    key: state
    base: state_
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 125
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 15
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
          params:
            volume: -6
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 0
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: Table
    node: CounterMetalFrame

- type: entity
  id: Table
  parent: TableBase
  name: "сталевий стіл"
  description: "Квадратний шматок металу, що стоїть на чотирьох металевих ніжках."
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/generic.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/generic.rsi
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 125
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 15
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
          params:
            volume: -6
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: Table
    node: Table
  - type: Tag # goob edit
    tags:
    - Table

- type: entity
  id: TableReinforced
  parent: TableBase
  name: "посилений сталевий стіл"
  description: "Квадратний шматок металу, що стоїть на чотирьох металевих ніжках. Дуже міцний."
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/reinforced.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/reinforced.rsi
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 30
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
          params:
            volume: -6
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 2
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: Table
    node: TableReinforced
  - type: Climbable
    # Reinforced tables are extra tough
  - type: Bonkable
    bonkDamage:
      types:
        Blunt: 8
    bonkTime: 3

- type: entity
  id: TableGlass
  parent: TableBase
  name: "скляний стіл"
  description: "Квадратний шматок скла, що стоїть на чотирьох металевих ніжках."
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/glass.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/glass.rsi
  - type: GlassTable
    climberDamage:
      types:
        Slash: 15
    tableDamage:
      types:
        Blunt: 25
    tableMassLimit: 60
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 15
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
        - !type:PlaySoundBehavior
          sound:
            collection: GlassBreak
    - trigger:
        !type:DamageTrigger
        damage: 5
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlass:
            min: 1
            max: 1
      - !type:ChangeConstructionNodeBehavior
        node: TableFrame
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: Table
    node: TableGlass
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepTile

- type: entity
  id: TableReinforcedGlass
  parent: TableBase
  name: "стіл з армованого скла"
  description: "Квадратний шматок скла, що стоїть на чотирьох металевих ніжках. Дуже міцний."
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/r_glass.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/r_glass.rsi
  - type: GlassTable
    climberDamage:
      types:
        Slash: 25
    tableDamage:
      types:
        Blunt: 40
    tableMassLimit: 120
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors: #excess damage (nuke?). avoid computational cost of spawning entities.
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
    - trigger:
        !type:DamageTrigger
        damage: 20
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:ChangeConstructionNodeBehavior
        node: TableFrame
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlass:
            min: 1
            max: 1
          PartRodMetal1:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: Table
    node: TableReinforcedGlass
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepTile

- type: entity
  id: TablePlasmaGlass
  parent: TableBase
  name: "стіл із плазмового скла"
  description: "Квадратний шматок плазмового скла, що стоїть на чотирьох металевих ніжках. Гарно!"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/plasma.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/plasma.rsi
  - type: GlassTable
    climberDamage:
      types:
        Slash: 30
    tableDamage:
      types:
        Blunt: 100
    tableMassLimit: 240
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 250
      behaviors: #excess damage (nuke?). avoid computational cost of spawning entities.
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
    - trigger:
        !type:DamageTrigger
        damage: 30
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:ChangeConstructionNodeBehavior
        node: TableFrame
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassPlasma:
            min: 1
            max: 1
          PartRodMetal1:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: Table
    node: TablePlasmaGlass
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepTile

- type: entity
  id: TableBrass
  parent: TableBase
  name: "латунний стіл"
  description: "Блискучий, стійкий до корозії латунний стіл. Стимпанк!"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/brass.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/brass.rsi
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 125
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 40
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
          params:
            volume: -6
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetBrass1:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: Table
    node: TableBrass

- type: entity
  id: TableWood
  parent: TableBase
  name: "дерев'яний стіл"
  description: "Не підносьте до нього вогонь. Подейкують, що він легко горить."
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/wood.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/wood.rsi
  - type: Damageable
    damageModifierSet: Wood
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors: #excess damage (nuke?). avoid computational cost of spawning entities.
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
    - trigger:
        !type:DamageTrigger
        damage: 15
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: Table
    node: TableWood
  - type: Tag
    tags:
    - Wooden
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepWood

- type: entity
  id: TableCarpet
  parent: TableBase
  name: "покерний стіл"
  description: "Квадратний шматок дерева, що стоїть на чотирьох ніжках, вкритий тканиною. (Чого ви очікували?)"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/carpet.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/carpet.rsi
  - type: Damageable
    damageModifierSet: Wood
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 15
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank:
            min: 1
            max: 1
          MaterialCloth1:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: Table
    node: TableCarpet
  - type: Tag
    tags:
    - Wooden
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepCarpet

- type: entity
  id: TableStone
  parent: TableBase
  name: "кам'яний стіл"
  description: "Буквально найміцніша річ, яку ви коли-небудь бачили."
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/stone.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/stone.rsi
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 40
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          path: /Audio/Effects/picaxe2.ogg
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepFloor

- type: entity
  id: TableWeb
  parent: TableBase
  name: "павутинний стіл"
  description: "Необхідний для будь-якої серйозної веб-розробки."
  components:
  - type: Damageable
    damageModifierSet: Web
  - type: Sprite
    sprite: Structures/Furniture/Tables/web.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/web.rsi
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 20
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: MeleeSound
    soundGroups:
      Brute:
        path:
          "/Audio/Weapons/slash.ogg"
  - type: Construction
    graph: WebStructures
    node: table
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepCarpet

# Fancy tables

- type: entity
  id: TableFancyBase
  abstract: true
  parent: TableBase
  name: "вишуканий стіл"
  description: "Дорогий і багатий."
  components:
  - type: Damageable
    damageModifierSet: Wood
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 25
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank:
            min: 1
            max: 1
          MaterialCloth1:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Tag
    tags:
    - Wooden
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepCarpet

- type: entity
  id: TableFancyBlue
  parent: TableFancyBase
  suffix: Blue
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/Fancy/blue.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/Fancy/blue.rsi
  - type: Construction
    graph: Table
    node: TableFancyBlue

- type: entity
  id: TableFancyCyan
  parent: TableFancyBase
  suffix: Cyan
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/Fancy/cyan.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/Fancy/cyan.rsi
  - type: Construction
    graph: Table
    node: TableFancyCyan

- type: entity
  id: TableFancyBlack
  parent: TableFancyBase
  suffix: Black
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/Fancy/black.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/Fancy/black.rsi
  - type: Construction
    graph: Table
    node: TableFancyBlack

- type: entity
  id: TableFancyRed
  parent: TableFancyBase
  suffix: Red
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/Fancy/red.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/Fancy/red.rsi
  - type: Construction
    graph: Table
    node: TableFancyRed

- type: entity
  id: TableFancyPurple
  parent: TableFancyBase
  suffix: Purple
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/Fancy/purple.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/Fancy/purple.rsi
  - type: Construction
    graph: Table
    node: TableFancyPurple

- type: entity
  id: TableFancyPink
  parent: TableFancyBase
  suffix: Pink
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/Fancy/pink.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/Fancy/pink.rsi
  - type: Construction
    graph: Table
    node: TableFancyPink

- type: entity
  id: TableFancyGreen
  parent: TableFancyBase
  suffix: Green
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/Fancy/green.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/Fancy/green.rsi
  - type: Construction
    graph: Table
    node: TableFancyGreen

- type: entity
  id: TableFancyOrange
  parent: TableFancyBase
  suffix: Orange
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/Fancy/orange.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/Fancy/orange.rsi
  - type: Construction
    graph: Table
    node: TableFancyOrange

- type: entity
  id: TableFancyWhite
  parent: TableFancyBase
  suffix: White
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/Fancy/white.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/Fancy/white.rsi
  - type: Construction
    graph: Table
    node: TableFancyWhite

#Debug

- type: entity
  id: TableDebug
  parent: TableBase
  name: "сталевий стіл"
  description: "ВДЯГНІТЬ НА НИХ ГАМІВНІ СОРОЧКИ!!!"
  suffix: DEBUG
  components:
  - type: Tag
    tags:
      - Debug
  - type: Sprite
    sprite: Structures/Furniture/Tables/debug.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/debug.rsi
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 1
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]

# counters

- type: entity
  id: TableCounterWood
  parent: CounterBase
  name: "дерев'яний прилавок"
  description: "Не підносьте до нього вогонь. Подейкують, що він легко горить."
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/counterwood.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/counterwood.rsi
    state: full
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Wood
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 15
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank1:
            min: 0
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: Table
    node: CounterWood
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepWood

- type: entity
  id: TableCounterMetal
  parent: CounterBase
  name: "металевий прилавок"
  description: "Виглядає як гарне місце, щоб поставити випивку."
  components:
  - type: Sprite
    sprite: Structures/Furniture/Tables/countermetal.rsi
  - type: Icon
    sprite: Structures/Furniture/Tables/countermetal.rsi
    state: full
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 125
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 25
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
          params:
            volume: -6
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 0
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: Table
    node: CounterMetal
