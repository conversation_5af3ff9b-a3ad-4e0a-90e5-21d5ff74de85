﻿- type: entity
  parent: BaseStructureDynamic
  id: AlienEggGrowing
  name: "Я<PERSON><PERSON>е"
  description: "Велике строкате яйце"
  components:
    - type: Transform
      noRot: true
    - type: Icon
      sprite: Mobs/Aliens/Xenos/alien.rsi
      state: egg_growing
    - type: Sprite
      noRot: true
      sprite: Mobs/Aliens/Xenos/alien.rsi
      state: egg_growing
    - type: InteractionOutline
    - type: Physics
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.4,-0.4,0.4,0.4"
          density: 1000
          mask:
          - ItemMask
          layer:
          - SlipLayer
          hard: false
    - type: PlaceableSurface
      isPlaceable: false # defaults to closed.
    - type: Damageable
      damageContainer: StructuralInorganic
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 50
          behaviors:
            - !type:DoActsBehavior
              acts: ["Destruction"]
            - !type:PlaySoundBehavior
              sound:
                collection: blood
    - type: Appearance
    - type: ItemSlots
    - type: StaticPrice
      price: 1000
    - type: TimedPolymorph
      polymorphTime: 240
      polymorphPrototype: AlienEggGrowth

- type: entity
  parent: BaseStructureDynamic
  id: AlienEgg
  name: "Я<PERSON><PERSON><PERSON>"
  description: "Велике строкате яйце"
  components:
    - type: Transform
      noRot: true
    - type: Icon
      sprite: Mobs/Aliens/Xenos/alien.rsi
      state: egg
    - type: Sprite
      noRot: true
      sprite: Mobs/Aliens/Xenos/alien.rsi
      state: egg
    - type: InteractionOutline
    - type: Physics
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.4,-0.4,0.4,0.4"
          density: 1000
          mask:
          - ItemMask
          layer:
          - SlipLayer
          hard: false
    - type: PlaceableSurface
      isPlaceable: false
    - type: Damageable
      damageContainer: StructuralInorganic
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 50
          behaviors:
            - !type:DoActsBehavior
              acts: ["Destruction"]
            - !type:PlaySoundBehavior
              sound:
                collection: blood
    - type: Appearance
    - type: ItemSlots
    - type: StaticPrice
      price: 1000
    - type: AlienEggHatch
      polymorphPrototype: AlienEggHatch

- type: entity
  parent: BaseStructureDynamic
  id: AlienEggHatching
  name: "Яйце"
  description: "Велике строкате яйце"
  components:
    - type: Transform
      noRot: true
    - type: Icon
      sprite: Mobs/Aliens/Xenos/alien.rsi
      state: egg_opening
    - type: Sprite
      noRot: true
      sprite: Mobs/Aliens/Xenos/alien.rsi
      state: egg_opening
    - type: TimedDespawn
      lifetime: 1.5
    - type: SpawnOnDespawn
      prototype: AlienEggOpened

- type: entity
  parent: BaseStructureDynamic
  id: AlienEggOpened
  name: "Яйце"
  description: "Велике строкате яйце"
  components:
    - type: Transform
      noRot: true
    - type: Icon
      sprite: Mobs/Aliens/Xenos/alien.rsi
      state: egg_hatched
    - type: Sprite
      noRot: true
      sprite: Mobs/Aliens/Xenos/alien.rsi
      state: egg_hatched
    - type: InteractionOutline
    - type: Physics
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.4,-0.4,0.4,0.4"
          density: 1000
          mask:
          - ItemMask
          layer:
          - SlipLayer
          hard: false
    - type: PlaceableSurface
      isPlaceable: false
    - type: Damageable
      damageContainer: StructuralInorganic
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 50
          behaviors:
            - !type:DoActsBehavior
              acts: ["Destruction"]
            - !type:PlaySoundBehavior
              sound:
                collection: blood
    - type: Appearance
    - type: ItemSlots
    - type: StaticPrice
      price: 1000
    - type: ConditionalSpawner
      prototypes:
      - Facehugger

- type: entity
  id: AlienEggSpawner
  name: "спавнер яєць чужих"
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: red
    - sprite: Mobs/Aliens/Xenos/alien.rsi
      state: egg
  - type: RandomSpawner
    prototypes:
    - AlienEgg
    chance: 1

- type: entity
  id: AlienEggSpawnerRandom
  name: "спавнер яєць чужих"
  suffix: 20%
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: red
    - sprite: Mobs/Aliens/Xenos/alien.rsi
      state: egg
  - type: RandomSpawner
    prototypes:
    - AlienEgg
    chance: 0.2
