- type: entity
  id: BaseIntercom
  name: "інтерком"
  description: "Переговорний пристрій. Для випадків, коли станція просто хоче щось дізнатися."
  abstract: true
  components:
  - type: StationAiWhitelist
  - type: WallMount
  - type: ApcPowerReceiver
  - type: Electrified
    enabled: false
    usesApcPower: true
  - type: TelecomExempt
  - type: EncryptionKeyHolder
    keySlots: 3
    keysExtractionMethod: Prying
  - type: RadioMicrophone
    powerRequired: true
    unobstructedRequired: true
    listenRange: 2
    toggleOnInteract: false
  - type: RadioSpeaker
    toggleOnInteract: false
  - type: Intercom
  - type: Speech
    speechVerb: Robotic
  - type: VoiceOverride # This is for the wire that makes an electricity zapping noise.
    speechVerbOverride: Electricity
    enabled: false
  - type: ExtensionCableReceiver
  - type: Clickable
  - type: InteractionOutline
  - type: Appearance
  - type: WiresVisuals
  - type: WiresPanelSecurity
  - type: ContainerFill
    containers:
      board: [ IntercomElectronics ]
  - type: ContainerContainer
    containers:
      board: !type:Container
      key_slots: !type:Container
  - type: Sprite
    noRot: false
    drawdepth: SmallObjects
    sprite: Structures/Wallmounts/intercom.rsi
    layers:
    - state: base
    - state: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
      shader: unshaded
    - state: broadcasting
      map: ["enum.RadioDeviceVisualLayers.Broadcasting"]
      shader: unshaded
      visible: false
    - state: speaker
      map: ["enum.RadioDeviceVisualLayers.Speaker"]
      shader: unshaded
      visible: false
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
      visible: false
  - type: Transform
    noRot: false
    anchored: true
  - type: WiresPanel
  - type: Wires
    boardName: wires-board-name-intercom
    layoutId: Intercom
  - type: ActivatableUIRequiresPower
  - type: ActivatableUI
    key: enum.IntercomUiKey.Key
    singleUser: true
  - type: UserInterface
    interfaces:
      enum.IntercomUiKey.Key:
        type: IntercomBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: Construction
    graph: Intercom
    node: intercom
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 200
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
          - !type:PlaySoundBehavior
            sound:
              collection: MetalGlassBreak
              params:
                volume: -4
  - type: GenericVisualizer
    visuals:
      enum.WiresVisualLayers.MaintenancePanel:
        enum.WiresVisualLayers.MaintenancePanel:
          True: { visible: true }
          False: { visible: false }
      enum.PowerDeviceVisuals.Powered:
        enum.PowerDeviceVisualLayers.Powered:
          True: { visible: true }
          False: { visible: false }
      enum.RadioDeviceVisuals.Broadcasting:
        enum.RadioDeviceVisualLayers.Broadcasting:
          True: { visible: true }
          False: { visible: false }
      enum.RadioDeviceVisuals.Speaker:
        enum.RadioDeviceVisualLayers.Speaker:
          True: { visible: true }
          False: { visible: false }
  placement:
    mode: SnapgridCenter
    snap:
    - Wallmount

- type: entity
  id: IntercomAssembly
  name: "збірка інтеркому"
  description: "Інтерком. Не думаю, що це зараз дуже корисно."
  components:
  - type: WallMount
  - type: Clickable
  - type: InteractionOutline
  - type: Sprite
    drawdepth: SmallObjects
    sprite: Structures/Wallmounts/intercom.rsi
    layers:
    - state: build
    - state: panel
      visible: false
      map: [ "wires" ]
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ConstructionVisuals.Layer:
        wires:
          0: { visible: false }
          1: { visible: true }
  - type: Construction
    graph: Intercom
    node: assembly
  - type: Transform
    anchored: true
  placement:
    mode: SnapgridCenter
    snap:
    - Wallmount

# this weird inheritance BS exists for construction shitcode
- type: entity
  id: IntercomConstructed
  parent: BaseIntercom
  suffix: Empty, Panel Open
  components:
  - type: Sprite
    layers:
    - state: base
    - state: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
      shader: unshaded
    - state: broadcasting
      map: ["enum.RadioDeviceVisualLayers.Broadcasting"]
      shader: unshaded
      visible: false
    - state: speaker
      map: ["enum.RadioDeviceVisualLayers.Speaker"]
      shader: unshaded
      visible: false
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
      visible: true
  - type: WiresPanel
    open: true

- type: entity
  id: Intercom
  parent: IntercomConstructed
  suffix: ""
  components:
  - type: Sprite
    layers:
    - state: base
    - state: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
      shader: unshaded
    - state: broadcasting
      map: ["enum.RadioDeviceVisualLayers.Broadcasting"]
      shader: unshaded
      visible: false
    - state: speaker
      map: ["enum.RadioDeviceVisualLayers.Speaker"]
      shader: unshaded
      visible: false
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: WiresPanel
    open: false

- type: entity
  id: IntercomCommon
  parent: Intercom
  suffix: Common
  components:
  - type: ContainerFill
    containers:
      board:
      - IntercomElectronics
      key_slots:
      - EncryptionKeyCommon

- type: entity
  id: IntercomCommand
  parent: Intercom
  suffix: Command
  components:
  - type: ContainerFill
    containers:
      board:
      - IntercomElectronics
      key_slots:
      - EncryptionKeyCommon
      - EncryptionKeyCommand

- type: entity
  id: IntercomEngineering
  parent: Intercom
  suffix: Engineering
  components:
  - type: ContainerFill
    containers:
      board:
      - IntercomElectronics
      key_slots:
      - EncryptionKeyCommon
      - EncryptionKeyEngineering

- type: entity
  id: IntercomMedical
  parent: Intercom
  suffix: Medical
  components:
  - type: ContainerFill
    containers:
      board:
      - IntercomElectronics
      key_slots:
      - EncryptionKeyCommon
      - EncryptionKeyMedical

- type: entity
  id: IntercomScience
  parent: Intercom
  suffix: Epistemics # DeltaV - Epistemics Department replacing Science
  components:
  - type: ContainerFill
    containers:
      board:
      - IntercomElectronics
      key_slots:
      - EncryptionKeyCommon
      - EncryptionKeyScience

- type: entity
  id: IntercomSecurity
  parent: Intercom
  suffix: Security
  description: "Інтерком. Він укріплений металом від шоломів охоронців, що робить його дуже складним для відкривання."
  components:
  - type: WiresPanel
    openDelay: 5
  - type: WiresPanelSecurity
    examine: wires-panel-component-on-examine-security-level2
    wiresAccessible: false
  - type: Construction
    node: intercomReinforced
  - type: ContainerFill
    containers:
      board:
      - IntercomElectronics
      key_slots:
      - EncryptionKeyCommon
      - EncryptionKeySecurity

- type: entity
  id: IntercomService
  parent: Intercom
  suffix: Service
  components:
  - type: ContainerFill
    containers:
      board:
      - IntercomElectronics
      key_slots:
      - EncryptionKeyCommon
      - EncryptionKeyService

- type: entity
  id: IntercomSupply
  parent: Intercom
  suffix: Supply
  components:
  - type: ContainerFill
    containers:
      board:
      - IntercomElectronics
      key_slots:
      - EncryptionKeyCommon
      - EncryptionKeyCargo

- type: entity
  id: IntercomAll
  parent: Intercom
  suffix: All
  components:
  - type: ContainerFill
    containers:
      board:
      - IntercomElectronics
      key_slots:
      - EncryptionKeyCommon
      - EncryptionKeyStationMaster
