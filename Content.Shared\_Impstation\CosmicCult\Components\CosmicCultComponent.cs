using Robust.Shared.GameStates;
using Content.Shared.StatusIcon;
using Robust.Shared.Prototypes;
using Robust.Shared.Audio;
using Content.Shared.Damage;
using Content.Shared._Impstation.CosmicCult.Prototypes;
using Content.Shared.Damage.Prototypes;
using Content.Shared.Alert;
using Robust.Shared.Serialization;

namespace Content.Shared._Impstation.CosmicCult.Components;

/// <summary>
/// Added to entities to tag that they are a cosmic cultist. Holds nearly all cultist-relevant data! Removal of this component is used to call for a deconversion
/// </summary>
[RegisterComponent, NetworkedComponent]
[AutoGenerateComponentState]
public sealed partial class CosmicCultComponent : Component
{
    #region Housekeeping

    /// <summary>
    /// The status icon prototype displayed for cosmic cultists.
    /// </summary>
    [DataField, ViewVariables(VVAccess.ReadWrite)]
    public ProtoId<FactionIconPrototype> StatusIcon { get; set; } = "CosmicCultIcon";

    [DataField] public bool IsConstruct = false;

    #endregion

    #region Ability Data
    [DataField]
    [AutoNetworkedField]
    public HashSet<ProtoId<InfluencePrototype>> UnlockedInfluences =
    [
        "InfluenceAberrantLapse",
        "InfluenceShuntSubjectivity",
        "InfluenceEschewMetabolism",
    ];

    [DataField]
    [AutoNetworkedField]
    public HashSet<EntProtoId> CosmicCultActions =
    [
        "ActionCosmicSiphon",
        "ActionCosmicGlare",
    ];
    public HashSet<EntityUid?> ActionEntities = [];

    [DataField]
    [AutoNetworkedField]
    public HashSet<ProtoId<InfluencePrototype>> OwnedInfluences = [];

    /// <summary>
    /// The duration of the doAfter for Siphon Entropy
    /// </summary>
    [DataField, AutoNetworkedField] public TimeSpan CosmicSiphonDelay = TimeSpan.FromSeconds(2);

    /// <summary>
    /// The duration of the doAfter for Shunt Subjectivity
    /// </summary>
    [DataField, AutoNetworkedField] public TimeSpan CosmicBlankDelay = TimeSpan.FromSeconds(0.6f);

    /// <summary>
    /// The duration of Shunt Subjectivity's trip to the cosmic void
    /// </summary>
    [DataField, AutoNetworkedField] public TimeSpan CosmicBlankDuration = TimeSpan.FromSeconds(22);

    /// <summary>
    /// The duration of Vacuous Imposition's shield.
    /// </summary>
    [DataField, AutoNetworkedField] public TimeSpan CosmicImpositionDuration = TimeSpan.FromSeconds(5.8);
    /// <summary>
    /// The duration of Null Glare's flash/disorientation.
    /// </summary>
    [DataField, AutoNetworkedField] public float CosmicGlareDuration = 8 * 1000f;

    /// <summary>
    /// The range of Null Glare.
    /// </summary>
    [DataField, AutoNetworkedField] public int CosmicGlareRange = 8;

    /// <summary>
    /// The movement speed penalty inflicted by Null Glare.
    /// </summary>
    [DataField, AutoNetworkedField] public float CosmicGlarePenalty = 0.3f;

    /// <summary>
    /// The stun duration inflicted by Null Glare.
    /// </summary>
    [DataField, AutoNetworkedField] public TimeSpan CosmicGlareStun = TimeSpan.FromSeconds(0);

    /// <summary>
    /// The amount of Entropy generated by Siphon Entropy
    /// </summary>
    [DataField, AutoNetworkedField] public int CosmicSiphonQuantity = 1;
    #endregion
    #region Misc Data
    /// <summary>
    /// The amount of Entropy the user is allowed to spend at The Monument.
    /// </summary>
    [DataField, AutoNetworkedField] public int EntropyBudget = 0;

    /// <summary>
    /// The amount of Entropy the user is currently holding on to.
    /// </summary>
    [DataField, AutoNetworkedField] public int EntropyStored = 0;

    /// <summary>
    /// The maximum amount of Entropy the user can have at once.
    /// </summary>
    [DataField, AutoNetworkedField] public int EntropyStoredCap = 14;

    /// <summary>
    /// Wether or not this cultist has been empowered by a Malign Rift.
    /// </summary>
    [DataField, AutoNetworkedField] public bool CosmicEmpowered = false;

    /// <summary>
    /// Wether or not this cultist needs to respirate.
    /// </summary>
    [DataField, AutoNetworkedField] public bool Respiration = true;

    /// <summary>
    /// A string for storing what damage container this cultist had upon conversion.
    /// </summary>
    [DataField, AutoNetworkedField] public ProtoId<DamageContainerPrototype> StoredDamageContainer = "Biological";

    /// <summary>
    /// The alert for displaying the cultist's currently held Entropy.
    /// </summary>
    [DataField]
    public ProtoId<AlertPrototype> EntropyAlert = "CosmicEntropy";
    #endregion


    #region VFX & SFX
    [DataField] public EntProtoId SpawnWisp = "MobCosmicWisp";
    [DataField] public EntProtoId LapseVFX = "CosmicLapseAbilityVFX";
    [DataField] public EntProtoId BlankVFX = "CosmicBlankAbilityVFX";
    [DataField] public EntProtoId GlareVFX = "CosmicGlareAbilityVFX";
    [DataField] public EntProtoId AbsorbVFX = "CosmicGenericVFX";
    [DataField] public EntProtoId ImpositionVFX = "CosmicImpositionAbilityVFX";
    [DataField] public SoundSpecifier BlankSFX = new SoundPathSpecifier("/Audio/_Impstation/CosmicCult/ability_blank.ogg");
    [DataField] public SoundSpecifier IngressSFX = new SoundPathSpecifier("/Audio/_Impstation/CosmicCult/ability_ingress.ogg");
    [DataField] public SoundSpecifier GlareSFX = new SoundPathSpecifier("/Audio/_Impstation/CosmicCult/ability_glare.ogg");
    [DataField] public SoundSpecifier NovaCastSFX = new SoundPathSpecifier("/Audio/_Impstation/CosmicCult/ability_nova_cast.ogg");
    [DataField] public SoundSpecifier ImpositionSFX = new SoundPathSpecifier("/Audio/_Impstation/CosmicCult/ability_imposition.ogg");

    #endregion
}

[NetSerializable, Serializable]
public enum CultAlertVisualLayers : byte
{
    Counter,
}
