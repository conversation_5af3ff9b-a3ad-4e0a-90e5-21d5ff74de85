- type: entity
  parent: Clothing
  id: N14ClothingBackpackDuffel
  name: "спортивна сумка"
  description: "Велика сумка для зберігання додаткових речей."
  components:
  - type: Sprite
    sprite: Clothing/Back/Duffels/duffel.rsi
    state: icon
  - type: Item
    # size: 9999
  - type: Clothing
    quickEquip: false
    slots:
    - back
  - type: Storage
    maxItemSize: Huge
    grid:
    - 0,0,7,4
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
        ents: []
  - type: ClothingSpeedModifier
    walkModifier: 1
    sprintModifier: 0.9
  - type: UserInterface
    interfaces:
      enum.StorageUiKey.Key:
        type: StorageBoundUserInterface

- type: entity
  parent: N14ClothingBackpackDuffel
  id: N14ClothingBackpackDuffelSalvage
  name: "речовий мішок утилізатора"
  description: "Велика спортивна сумка для зберігання екзотичних скарбів."
  components:
    - type: Sprite
      sprite: Clothing/Back/Duffels/salvage.rsi

- type: entity
  parent: N14ClothingBackpackDuffel
  id: N14ClothingBackpackDuffelMilitary
  name: "військовий речовий мішок"
  description: "Велика сумка для перевезення важкого спорядження."
  components:
  - type: Sprite
    sprite: Clothing/Back/Duffels/syndicate.rsi

- type: entity
  parent: N14ClothingBackpackDuffelMilitary
  id: N14ClothingBackpackDuffelMilitaryMedical
  name: "військовий медичний речовий мішок"
  components:
  - type: Sprite
    sprite: Clothing/Back/Duffels/syndicate.rsi
    state: icon-med
  - type: Item
    heldPrefix: med
  - type: Clothing
    equippedPrefix: med
