- type: entity
  abstract: true
  parent: BaseItem
  id: EncryptionKey
  name: "ключ шифрування"
  description: "Невеликий шифрувальний чіп для гарнітури."
  components:
  - type: EncryptionKey
  - type: Item
    sprite: Objects/Devices/encryption_keys.rsi
  - type: Sprite
    sprite: Objects/Devices/encryption_keys.rsi

- type: entity
  parent: EncryptionKey
  id: EncryptionKeyCommon
  name: "загальний ключ шифрування"
  description: "Ключ шифрування, яким може користуватися будь-хто."
  components:
  - type: EncryptionKey
    channels:
    - Common
    defaultChannel: Common
  - type: Sprite
    layers:
    - state: crypt_gray
    - state: common_label

- type: entity
  parent: EncryptionKey
  id: EncryptionKeyCargo
  name: "логістичний ключ шифрування" # DeltaV - Logistics Department replacing Cargo
  description: "Ключ шифрування, який використовують працівники відділу постачання."
  components:
  - type: EncryptionKey
    channels:
    - Supply
    defaultChannel: Supply
  - type: Sprite
    layers:
    - state: crypt_gray
    - state: cargo_label

- type: entity
  parent: EncryptionKey
  id: EncryptionKeyCentCom
  name: "ключ шифрування центральної команди"
  description: "Ключ шифрування, яким користується начальство капітана."
  components:
  - type: EncryptionKey
    channels:
    - CentCom
    defaultChannel: CentCom
  - type: Sprite
    layers:
    - state: crypt_blue
    - state: nano_label

- type: entity
  parent: EncryptionKey
  id: EncryptionKeyStationMaster
  name: "головний ключ шифрування станції"
  description: "Ключ шифрування, яким користується начальство станції."
  components:
  - type: EncryptionKey
    channels:
    - Common
    - Command
    - Engineering
    - Justice # Delta V- adds Justice department
    - Medical
    - Science
    - Security
    - Service
    - Supply
    - Prison #DeltaV
    defaultChannel: Command
  - type: Sprite
    layers:
    - state: crypt_gold
    - state: cap_label

- type: entity
  parent: EncryptionKey
  id: EncryptionKeyCommand
  name: "ключ шифрування команди"
  description: "Ключ шифрування, який використовують боси екіпажу."
  components:
  - type: EncryptionKey
    channels:
    - Command
    defaultChannel: Command
  - type: Sprite
    layers:
    - state: crypt_silver
    - state: com_label

- type: entity
  parent: EncryptionKey
  id: EncryptionKeyEngineering
  name: "інженерний ключ шифрування"
  description: "Ключ шифрування, який використовують інженери."
  components:
  - type: EncryptionKey
    channels:
    - Engineering
    defaultChannel: Engineering
  - type: Sprite
    layers:
    - state: crypt_gray
    - state: eng_label

- type: entity
  parent: EncryptionKey
  id: EncryptionKeyMedical
  name: "медичний ключ шифрування"
  description: "Ключ шифрування, який використовують ті, хто рятує життя."
  components:
  - type: EncryptionKey
    channels:
    - Medical
    defaultChannel: Medical
  - type: Sprite
    layers:
    - state: crypt_gray
    - state: med_label

- type: entity
  parent: EncryptionKey
  id: EncryptionKeyMedicalScience
  name: "ключ шифрування med-epi" # DeltaV - Epistemics Department replacing Science
  description: "Ключ шифрування, яким користується той, хто ще не вирішив, на чий бік стати."
  components:
  - type: EncryptionKey
    channels:
    - Medical
    - Science
    defaultChannel: Science
  - type: Sprite
    layers:
    - state: crypt_gray
    - state: medsci_label

- type: entity
  parent: EncryptionKey
  id: EncryptionKeyScience
  name: "ключ шифрування епістеми" # DeltaV - Epistemics Department replacing Science
  description: "Ключ шифрування, яким користуються вчені. Може, він плазмостійкий?"
  components:
  - type: EncryptionKey
    channels:
    - Science
    defaultChannel: Science
  - type: Sprite
    layers:
    - state: crypt_gray
    - state: sci_label

- type: entity
  parent: EncryptionKey
  id: EncryptionKeyRobo
  name: "ключ шифрування робототехніки"
  description: "Ключ шифрування, який використовують інженери робототехніки. Можливо, на ньому є LAH-6000?"
  components:
  - type: EncryptionKey
    channels:
    - Binary  #PIRATE
    - Science
    defaultChannel: Binary #PIRATE
  - type: Sprite
    layers:
    - state: crypt_gray
    - state: robotics_label

- type: entity
  parent: EncryptionKey
  id: EncryptionKeySecurity
  name: "ключ шифрування безпеки"
  description: "Ключ шифрування, який використовується службою безпеки."
  components:
  - type: EncryptionKey
    channels:
    - Security
    - Prison #deltaV
    defaultChannel: Security
  - type: Sprite
    layers:
    - state: crypt_gray
    - state: sec_label

- type: entity
  parent: EncryptionKey
  id: EncryptionKeyService
  name: "сервісний ключ шифрування"
  description: "Ключ шифрування, що використовується обслуговуючим персоналом, завдання якого полягає в тому, щоб станція була повною, щасливою і чистою."
  components:
  - type: EncryptionKey
    channels:
    - Service
    defaultChannel: Service
  - type: Sprite
    layers:
    - state: crypt_gray
    - state: service_label

- type: entity
  parent: EncryptionKey
  id: EncryptionKeySyndie
  name: "криваво-червоний ключ шифрування"
  description: "Ключ шифрування, яким користується... зачекайте... Хто власник цього чіпа?"
  components:
  - type: EncryptionKey
    channels:
    - Syndicate
    defaultChannel: Syndicate
  - type: Sprite
    layers:
    - state: crypt_red
    - state: synd_label
  - type: ReverseEngineering # Nyano
    difficulty: 5
    recipes:
      - EncryptionKeySyndie

- type: entity
  parent: EncryptionKey
  id: EncryptionKeyBinary
  name: "ключ бінарного транслятора"
  description: "Ключ шифрування, який перетворює двійкові сигнали, що використовуються кремнієм."
  components:
  - type: EncryptionKey
    channels:
    - Binary
    defaultChannel: Binary
  - type: Sprite
    layers:
    - state: crypt_silver
    - state: ai_label

- type: entity
  parent: EncryptionKey
  id: EncryptionKeyBinarySyndicate
  name: "ключ бінарного транслятора"
  description: "Ключ шифрування синдикату, який транслює двійкові сигнали, що використовуються кремнієм."
  components:
  - type: EncryptionKey
    channels:
    - Binary
    defaultChannel: Binary
  - type: Sprite
    layers:
    - state: crypt_red
    - state: ai_label

- type: entity
  parent: EncryptionKey
  id: EncryptionKeyFreelance
  name: "ключ шифрування фрілансера"
  description: "Ключ шифрування, що використовується фрілансерами, які можуть мати або не мати афілійованості. Схоже, що він зношений."
  components:
    - type: EncryptionKey
      channels:
        - Freelance
      defaultChannel: Freelance
    - type: Sprite
      layers:
        - state: crypt_rusted
        - state: pirate_label
