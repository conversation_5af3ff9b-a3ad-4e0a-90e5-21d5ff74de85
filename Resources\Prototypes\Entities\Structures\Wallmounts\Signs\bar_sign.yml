﻿- type: entity
  id: BaseBarSign
  parent: BaseStructure
  name: "барна вивіска"
  abstract: true
  components:
  - type: MeleeSound
    soundGroups:
      Brute:
        collection: GlassSmash
  - type: WallMount
    arc: 360
  - type: Sprite
    drawdepth: Objects
    sprite: Structures/Wallmounts/barsign.rsi
    state: empty
  - type: ApcPowerReceiver
  - type: ExtensionCableReceiver
  - type: BarSign
  - type: InteractionOutline
  - type: AccessReader
    access: [["Bar"]]
  - type: ActivatableUIRequiresPower
  - type: ActivatableUIRequiresAccess
  - type: ActivatableUI
    key: enum.BarSignUiKey.Key
  - type: UserInterface
    interfaces:
      enum.BarSignUiKey.Key:
        type: BarSignBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: Appearance
  - type: Damageable
    damageContainer: StructuralInorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
  - type: StationAiWhitelist
  - type: WiresPanel
  - type: Wires
    boardName: wires-board-name-barsign
    layoutId: BarSign

- type: entity
  parent: BaseBarSign
  id: BarSign
  name: "барна вивіска"
  suffix: Random

# Missing appearance components & various other sprite issues.
#- type: entity
#  id: LargeBarSign
#  name: large bar sign
#  categories: [ HideSpawnMenu ]
#  components:
#  - type: Clickable
#  - type: InteractionOutline
#  - type: Sprite
#    drawdepth: WallTops
#    sprite: Structures/Wallmounts/sylphs.rsi
#    state: sylph
#  - type: ApcPowerReceiver
#  - type: ExtensionCableReceiver
#  - type: BarSign

- type: entity
  parent: BaseBarSign
  id: BarSignComboCafe
  name: "Кафе Комбо"
  description: "Відомий по всій системі своїми абсолютно нетворчими поєднаннями напоїв."
  components:
  - type: BarSign
    current: ComboCafe

- type: entity
  parent: BaseBarSign
  id: BarSignEmergencyRumParty
  name: "Екстрена Ромова Вечірка"
  description: "Нещодавно переліцензоване після тривалого закриття."
  components:
  - type: BarSign
    current: EmergencyRumParty

- type: entity
  parent: BaseBarSign
  id: BarSignLV426
  name: "LV426"
  description: "Пияцтво з модними масками для обличчя явно важливіше, ніж відвідування медпункту."
  components:
  - type: BarSign
    current: LV426

- type: entity
  parent: BaseBarSign
  id: BarSignMaidCafe
  name: "Кафе Покоївка"
  description: "Ласкаво просимо назад, хазяїне!"
  components:
  - type: BarSign
    current: MaidCafe

- type: entity
  parent: BaseBarSign
  id: BarSignMalteseFalcon
  name: "Мальтійський Сокіл"
  description: "Прокрути ще раз, Семе."
  components:
  - type: BarSign
    current: MalteseFalcon

- type: entity
  parent: BaseBarSign
  id: BarSignOfficerBeersky
  name: "Офіцер Біпскі"
  description: "Чоловіче, з'їж хуй, ці напої чудові."
  components:
  - type: BarSign
    current: OfficerBeersky

- type: entity
  parent: BaseBarSign
  id: BarSignRobustaCafe
  name: "Кафе Робаста"
  description: "5 років беззаперечно утримує рекорд \"Найсмертоносніших бійок\"."
  components:
  - type: BarSign
    current: RobustaCafe

- type: entity
  parent: BaseBarSign
  id: BarSignTheAleNath
  name: "Ель Натх"
  description: "Гаразд, приятелю. Здається, тобі вже достатньо. Час ловити таксі."
  components:
  - type: BarSign
    current: TheAleNath

- type: entity
  parent: BaseBarSign
  id: BarSignTheBirdCage
  name: "Пташина Клітка"
  description: "Кар-Кар!"
  components:
  - type: BarSign
    current: TheBirdCage

- type: entity
  parent: BaseBarSign
  id: BarSignTheCoderbus
  name: "Баркод-ер"
  description: "Дуже суперечливий бар, відомий своїм широким асортиментом напоїв, що постійно змінюються."
  components:
  - type: BarSign
    current: TheCoderbus

- type: entity
  parent: BaseBarSign
  id: BarSignTheDrunkCarp
  name: "П'яний Короп"
  description: "Не пийте і не купайтеся."
  components:
  - type: BarSign
    current: TheDrunkCarp

- type: entity
  parent: BaseBarSign
  id: BarSignEngineChange
  name: "Заміна Двигуна"
  description: "Все ще чекаю."
  components:
  - type: BarSign
    current: EngineChange

- type: entity
  parent: BaseBarSign
  id: BarSignTheHarmbaton
  name: "Хармбатон"
  description: "Чудова їдальня як для співробітників служби безпеки, так і для пасажирів."
  components:
  - type: BarSign
    current: Harmbaton

- type: entity
  parent: BaseBarSign
  id: BarSignTheLightbulb
  name: "Лампочка"
  description: "Кафе, популярне серед молі та мофів. Одного разу воно було закрите на тиждень після того, як барменка використала нафталін, щоб захистити свій запасний одяг."
  components:
  - type: BarSign
    current: TheLightbulb

- type: entity
  parent: BaseBarSign
  id: BarSignTheLooseGoose
  name: "Розслабонь"
  description: "Пийте, поки не знудить і/або не порушите закони реальності!"
  components:
  - type: BarSign
    current: Goose

- type: entity
  parent: BaseBarSign
  id: BarSignTheNet
  name: "Мережа"
  description: "Здається, що ти просто занурюєшся в неї на кілька годин."
  components:
  - type: BarSign
    current: TheNet

- type: entity
  parent: BaseBarSign
  id: BarSignTheOuterSpess
  name: "Зовнішній Косс"
  description: "Цей бар насправді не знаходиться у відкритому космосі."
  components:
  - type: BarSign
    current: TheOuterSpess

- type: entity
  parent: BaseBarSign
  id: BarSignTheSingulo
  name: "Сінгула"
  description: "Куди ходять люди, які не хочуть, щоб їх називали на ім'я."
  components:
  - type: BarSign
    current: TheSingulo

- type: entity
  parent: BaseBarSign
  id: BarSignTheSun
  name: "Сонце"
  description: "Парадоксально яскраво для такого тінистого бару."
  components:
  - type: BarSign
    current: TheSun

- type: entity
  parent: BaseBarSign
  id: BarSignWiggleRoom
  name: "Wiggle Room"
  description: "У MoMMI з'явилися ходи."
  components:
  - type: BarSign
    current: WiggleRoom

- type: entity
  parent: BaseBarSign
  id: BarSignZocalo
  name: "Зокало"
  description: "Раніше мешкав ен Еспанья."
  components:
  - type: BarSign
    current: Zocalo

- type: entity
  parent: BaseBarSign
  id: BarSignEmprah
  name: "3а Імператра"
  description: "Його полюбляють фанатики, єретики і просто несповна розуму меценати."
  components:
  - type: BarSign
    current: Emprah

- type: entity
  parent: BaseBarSign
  id: BarSignSpacebucks
  name: "Космобакси"
  description: "Від них нікуди не дітися, навіть у космосі, і навіть після того, як ми почали називати їх \"спесо\"."
  components:
  - type: BarSign
    current: Spacebucks

- type: entity
  parent: BaseBarSign
  id: BarSignMaltroach
  name: "Малтроуч"
  description: "Піск!"
  components:
  - type: BarSign
    current: Maltroach
