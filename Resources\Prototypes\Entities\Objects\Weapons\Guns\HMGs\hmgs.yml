- type: entity
  name: "Базова зброя - великокаліберний кулемет"
  parent: BaseItem
  id: BaseWeaponHeavyMachineGun
  description: "Розпилюйте і моліться"
  abstract: true
  components:
  - type: Sprite
  - type: Wieldable
    unwieldOnUse: false
  - type: Item
    size: Ginormous
  - type: Gun
    fireRate: 20
    selectedMode: FullAuto
    availableModes:
    - FullAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/lmg.ogg
    soundEmpty:
      path: /Audio/Weapons/Guns/Empty/lmg_empty.ogg
  - type: StaticPrice
    price: 500
  - type: Execution
  # No chamber because HMG may want its own
  - type: MeleeWeapon
    attackRate: 1.5
    damage:
      types:
        Blunt: 16
    bluntStaminaDamageFactor: 1.5
    swapKeys: true
    disableHeavy: true
    animation: WeaponArcThrust
    wideAnimationRotation: 180
    soundHit:
      collection: MetalThud
  - type: DamageOtherOnHit
    staminaCost: 16
  # PIRATE START
  - type: CanHoldAccessories
    whiteListTags:
      - CombatKnife
  - type: CanTakeAim
  # PIRATE END

- type: entity
  name: "мін<PERSON>ган"
  id: WeaponMinigun
  parent: BaseWeaponHeavyMachineGun
  description: "Взззззз! Rahrahrahrahrah! Врррррр! Використовує гвинтівкові патрони 10-го калібру."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/HMGs/minigun.rsi
    layers:
    - state: icon
      map: ["enum.GunVisualLayers.Base"]
  - type: Item
    sprite: Objects/Weapons/Guns/HMGs/minigun.rsi
  - type: Gun
    minAngle: 80
    maxAngle: 200
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/minigun.ogg
  - type: GunWieldBonus
    minAngle: -75
    maxAngle: -175
  - type: ChamberMagazineAmmoProvider
    soundRack:
      path: /Audio/Weapons/Guns/Cock/lmg_cock.ogg
  - type: AmmoCounter
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        startingItem: MagazineBoxRifleMinigun
        insertSound: /Audio/Weapons/Guns/MagIn/batrifle_magin.ogg
        ejectSound: /Audio/Weapons/Guns/MagOut/batrifle_magout.ogg
        priority: 2
        whitelist:
          tags:
            - MagazineBoxRifleMinigun
      gun_chamber:
        name: Chamber
        startingItem: CartridgeMinigun
        priority: 1
        whitelist:
          tags:
            - CartridgeMinigun
  - type: ContainerContainer
    containers:
      gun_magazine: !type:ContainerSlot
      gun_chamber: !type:ContainerSlot
  - type: ClothingSpeedModifier
    walkModifier: 0.35
    sprintModifier: 0.35
  - type: HeldSpeedModifier
  - type: MagazineVisuals
    magState: mag
    steps: 4
    zeroVisible: true
