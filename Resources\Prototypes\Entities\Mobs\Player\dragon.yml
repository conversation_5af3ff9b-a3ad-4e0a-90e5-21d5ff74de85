- type: entity
  parent: [ SimpleSpaceMobBase, FlyingMobBase ]
  id: BaseMobDragon
  suffix: ""
  name: "космічний дракон"
  description: "Літаючий левіафан, віддалено пов'язаний з космічними коропами."
  abstract: true
  components:
  - type: Body # Shitmed
    prototype: SpaceDragon
  - type: Bloodstream
    bloodMaxVolume: 650
  - type: GhostRole
    allowMovement: true
    allowSpeech: true
    makeSentient: true
    name: ghost-role-information-space-dragon-name
    description: ghost-role-information-space-dragon-description
    rules: ghost-role-information-space-dragon-rules
    mindRoles:
    - MindRoleGhostRoleTeamAntagonist
    raffle:
      settings: default
    requirements: # Goobstation - ghost roles requirements
    - !type:CharacterOverallTimeRequirement
      time: 18000 #5 hours
  - type: GhostTakeoverAvailable
  - type: HTN
    rootTask:
      task: XenoCompound
    blackboard:
      NavInteract: !type:Bool
        true
      NavPry: !type:Bool
        true
      NavSmash: !type:Bool
        true
  - type: NpcFactionMember
    factions:
    - Dragon
  - type: Speech
    speechVerb: LargeMob
  - type: CombatMode
  - type: MobMover
  - type: InputMover
  - type: MovementSpeedModifier
    baseWalkSpeed: 3
    baseSprintSpeed: 5
    weightlessModifier: 1.5
  - type: RandomSprite
    available:
    - enum.DamageStateVisualLayers.Base:
        alive: Rainbow
  - type: Sprite
    sprite: Mobs/Aliens/Carps/dragon.rsi
    noRot: true
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: alive
    - map: [ "enum.DamageStateVisualLayers.BaseUnshaded" ]
      state: alive-unshaded
      shader: unshaded
  - type: Appearance
  - type: DamageStateVisuals
    states:
      Alive:
        Base: alive
        BaseUnshaded: alive-unshaded
      Critical:
        Base: crit
      Dead:
        Base: dead
        BaseUnshaded: dead-unshaded
  - type: Physics
    bodyType: KinematicController
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.40
        density: 100
        mask:
        - FlyingMobMask
        layer:
        - FlyingMobLayer
  - type: MobState
  - type: MobStateActions
    actions:
      Critical:
      - ActionCritSuccumb
      - ActionCritLastWords
  - type: MobThresholds
    thresholds:
      0: Alive
      450: Critical
      500: Dead
  - type: SlowOnDamage
    speedModifierThresholds:
      250: 0.7
      400: 0.5
  # disable taking damage from fire, since its a fire breathing dragon
  - type: Flammable
    damage:
      types: {}
  - type: Temperature
    heatDamageThreshold: 800
  - type: Metabolizer
    solutionOnBody: false
    updateInterval: 0.25
    metabolizerTypes: [ Dragon ]
    groups:
    - id: Medicine
    - id: Poison
  - type: Butcherable
    spawned:
    - id: FoodMeatDragon
      amount: 2
  - type: InteractionPopup
    successChance: 0.25 # It's no goose, but you better smell like carp.
    interactSuccessString: petting-success-dragon
    interactFailureString: petting-failure-dragon
    interactFailureSound:
      path: /Audio/Animals/space_dragon_roar.ogg
    soundPerceivedByOthers: false # A 75% chance for a loud roar would get old fast.
  - type: MeleeWeapon
    altDisarm: false
    soundHit:
      path: /Audio/Weapons/Xeno/alien_claw_flesh3.ogg
    damage:
      types:
        Piercing: 15
        Slash: 15
  - type: Devourer
    foodPreference: Humanoid
    shouldStoreDevoured: true
    chemical: Ichor
    healRate: 15.0
    whitelist:
      components:
      - MobState
      - Door
      tags:
      - Wall
  - type: Tag
    tags:
    - CannotSuicide
    - DoorBumpOpener
  - type: NightVision
    isActive: true
    color: "#808080"
    activateSound: null
    deactivateSound: null
  - type: Puller
    needsHands: false

- type: entity
  parent: BaseMobDragon
  id: MobDragon
  suffix: No role or objectives
  components:
  - type: Dragon
    spawnRiftAction: ActionSpawnRift
  - type: ActionGun
    action: ActionDragonsBreath
    gunProto: DragonsBreathGun
  - type: GuideHelp
    guides:
    - MinorAntagonists

- type: entity
  categories: [ HideSpawnMenu ]
  id: DragonsBreathGun
  name: "лЕГЕНА ДРАКОНА"
  description: "Для дихання дракона"
  components:
  - type: RechargeBasicEntityAmmo
    rechargeCooldown: 5
    rechargeSound:
      path: /Audio/Animals/space_dragon_roar.ogg
  - type: BasicEntityAmmoProvider
    proto: ProjectileDragonsBreath
    capacity: 1
    count: 1
  - type: Gun
    soundGunshot:
      path: /Audio/Animals/space_dragon_roar.ogg
    soundEmpty: null
    projectileSpeed: 5

- type: entity
  parent: BaseMobDragon
  id: MobDragonDungeon
  suffix: Dungeon
  components:
  - type: Absorbable
  - type: GhostRole
    description: ghost-role-information-space-dragon-dungeon-description
    raffle:
      settings: default
  - type: SlowOnDamage
    speedModifierThresholds:
      100: 0.7
  - type: MobThresholds
    thresholds:
      0: Alive
      200: Dead
  # less meat spawned since it's a lot easier to kill
  - type: Butcherable
    spawned:
    - id: FoodMeatDragon
      amount: 1
    - id: DragonEgg #Frontier
      amount: 1 #Frontier
      prob: 0.3 #Frontier
  - type: MeleeWeapon
    damage:
      groups:
        Brute: 12

- type: entity
  id: ActionSpawnRift
  name: "Викликати Carp Rift"
  description: "Викликає короповий розлом, який періодично буде нереститися коропами."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon:
      sprite: Interface/Actions/carp_rift.rsi
      state: icon
    event: !type:DragonSpawnRiftActionEvent
    useDelay: 1
    priority: 3

- type: entity
  id: ActionDevour
  name: "[color=red]Поглинути[/color]"
  description: "Спробуйте зламати конструкцію щелепами або проковтнути істоту."
  categories: [ HideSpawnMenu ]
  components:
  - type: EntityTargetAction
    icon: { sprite : Interface/Actions/devour.rsi, state: icon }
    iconOn: { sprite : Interface/Actions/devour.rsi, state: icon-on }
    event: !type:DevourActionEvent
    priority: 1

- type: entity
  categories: [ HideSpawnMenu ]
  id: ActionDragonsBreath
  name: "[color=помаранчевий]Подих дракона[/color]"
  description: "Вивергайте полум'я на всіх, хто наважиться напасти на вас!"
  components:
  - type: WorldTargetAction
    # TODO: actual sprite
    icon: { sprite : Objects/Weapons/Guns/Projectiles/magic.rsi, state: fireball }
    event: !type:ActionGunShootEvent
    priority: 2
    checkCanAccess: false
    range: 0
