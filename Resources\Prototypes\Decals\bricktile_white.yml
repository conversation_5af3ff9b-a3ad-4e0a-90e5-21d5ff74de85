﻿- type: decal
  id: BrickTileWhite
  parent: BrickTile
  abstract: true

- type: decal
  id: BrickTileWhiteBox
  parent: BrickTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: white_box

- type: decal
  id: BrickTileWhiteCornerNe
  parent: BrickTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: white_corner_ne

- type: decal
  id: BrickTileWhiteCornerSe
  parent: BrickTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: white_corner_se

- type: decal
  id: BrickTileWhiteCornerNw
  parent: BrickTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: white_corner_nw

- type: decal
  id: BrickTileWhiteCornerSw
  parent: BrickTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: white_corner_sw

- type: decal
  id: BrickTileWhiteInnerNe
  parent: BrickTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: white_inner_ne

- type: decal
  id: BrickTileWhiteInnerSe
  parent: BrickTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: white_inner_se

- type: decal
  id: BrickTileWhiteInnerNw
  parent: BrickTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: white_inner_nw

- type: decal
  id: BrickTileWhiteInnerSw
  parent: BrickTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: white_inner_sw

- type: decal
  id: BrickTileWhiteEndN
  parent: BrickTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: white_end_n

- type: decal
  id: BrickTileWhiteEndE
  parent: BrickTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: white_end_e

- type: decal
  id: BrickTileWhiteEndS
  parent: BrickTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: white_end_s

- type: decal
  id: BrickTileWhiteEndW
  parent: BrickTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: white_end_w

- type: decal
  id: BrickTileWhiteLineN
  parent: BrickTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: white_line_n

- type: decal
  id: BrickTileWhiteLineE
  parent: BrickTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: white_line_e

- type: decal
  id: BrickTileWhiteLineS
  parent: BrickTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: white_line_s

- type: decal
  id: BrickTileWhiteLineW
  parent: BrickTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: white_line_w

