- type: entity
  id: Bookshelf
  parent: BaseStructure
  name: "книжкова полиця"
  description: "Здебільшого заповнена книжками."
  components:
  - type: Sprite
    sprite: Structures/Furniture/bookshelf.rsi
    snapCardinals: true
    layers:
      - state: base
      - state: book-0
      - map: ["enum.StorageFillLayers.Fill"]
  - type: Appearance
  - type: StorageFillVisualizer
    maxFillLevels: 21
    fillBaseName: book
  - type: Damageable
    damageModifierSet: Wood
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 30
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroyHeavy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank:
            min: 2
            max: 3
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Tag
    tags:
    - Structure
    - Wooden
  - type: Transform
    anchored: true
    noRot: true
  - type: Physics
    bodyType: Static
  - type: Anchorable
  - type: Pullable
  - type: Occluder
  - type: Storage
    grid:
    - 0,0,5,1 # DeltaV, make it 2 rows of 6 instead of a giant block
    - 0,3,5,4
    #maxItemSize: Normal # DeltaV: removed whitelist and max item size
  - type: UserInterface
    interfaces:
      enum.StorageUiKey.Key:
        type: StorageBoundUserInterface
  - type: InteractionOutline
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
  - type: Construction
    graph: Bookshelf
    node: bookshelf
  - type: BlockWeather
