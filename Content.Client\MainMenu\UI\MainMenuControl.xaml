﻿<Control xmlns="https://spacestation14.io"
         xmlns:pllax="clr-namespace:Content.Client.Parallax"
         xmlns:clog="clr-namespace:Content.Client.Changelog">
    <pllax:ParallaxControl />
    <BoxContainer Name="VBox"
                  Orientation="Vertical"
                  HorizontalAlignment="Center"
                  VerticalAlignment="Center"
                  HorizontalExpand="True"
                  VerticalExpand="True"
                  StyleIdentifier="mainMenuVBox"
                  SeparationOverride="3">
        <TextureRect Name="Logo"
                     Stretch="KeepCentered"/>
        <GridContainer Columns="2">
            <Label Text="{Loc 'main-menu-username-label'}" />
            <LineEdit Name="UsernameBox"
                      Access="Public"
                      PlaceHolder="{Loc 'main-menu-username-text'}"
                      HorizontalExpand="True" />
            <Label Text="{Loc 'main-menu-address-label'}"/>
            <LineEdit Name="AddressBox"
                      Access="Public"
                      Text="localhost"
                      PlaceHolder="server address:port"
                      HorizontalExpand="True" />
        </GridContainer>
        <Button Name="DirectConnectButton"
                Access="Public"
                Text="{Loc 'main-menu-direct-connect-button'}"
                TextAlign="Center"
                StyleIdentifier="mainMenu"/>
        <Button Name="GoToLobbyButton"
                Access="Public"
                Text="{Loc 'main-menu-go-lobby-button'}"
                TextAlign="Center"
                StyleIdentifier="mainMenu"/>
        <Button Name="OptionsButton"
                Access="Public"
                Text="{Loc 'main-menu-options-button'}"
                TextAlign="Center"
                StyleIdentifier="mainMenu"/>
        <Button Name="QuitButton"
                Access="Public"
                Text="{Loc 'main-menu-quit-button'}"
                TextAlign="Center"
                StyleIdentifier="mainMenu"/>
        <clog:ChangelogButton
            Name="ChangelogButton"
            Access="Public"/>
    </BoxContainer>
</Control>
