<BoxContainer xmlns="https://spacestation14.io"
         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
         xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
         Orientation="Vertical" HorizontalExpand ="True">

    <BoxContainer HorizontalExpand="True" Orientation="Horizontal" Margin="0 1">
        <Label Name="GasLabel" Text="{Loc 'supermatter-console-window-label-gas'}" HorizontalExpand="True" HorizontalAlignment="Left" />
        <PanelContainer Name="GasBarBorder" SetWidth="150" SetHeight="24">
            <ProgressBar Name="GasBar" MinValue="0" MaxValue="100" HorizontalExpand="True" VerticalExpand="True" Margin="1">
                <Label Name="GasBarLabel" HorizontalAlignment="Right" Margin="0 0 4 0" />
            </ProgressBar>
        </PanelContainer>
    </BoxContainer>

</BoxContainer>
