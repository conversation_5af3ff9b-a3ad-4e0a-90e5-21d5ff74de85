- type: entity
  parent: BaseItem
  id: BaseVendingMachineRestock
  abstract: true
  name: "коробка для поповнення торгового автомата"
  description: "Коробка для поповнення торгових автоматів фірмовими смаколиками."
  components:
  - type: VendingMachineRestock
  - type: Sprite
    sprite: Objects/Specific/Service/vending_machine_restock.rsi
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
  - type: MeleeWeapon
    damage:
      types:
        Blunt: 5
    soundHit:
      path: /Audio/Weapons/genhit2.ogg
    soundSwing:
      path: /Audio/Weapons/punchmiss.ogg
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 11
    staminaCost: 15
  - type: Item
    size: Normal
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 40
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 20
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:DumpRestockInventory
      - !type:DoActsBehavior
        acts: [ "Destruction" ]

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockBooze
  name: "Коробка для поповнення запасів Booze-O-Mat"
  description: "Покладіть на свій килимок Booze-O-Mat, щоб розпочати вечірку! Не продається пасажирам, які не досягли встановленого законом віку."
  components:
  - type: VendingMachineRestock
    canRestock:
    - BoozeOMatInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_booze

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockChang
  name: "Коробка пана Чанга для поповнення запасів"
  description: "Коробка, обклеєна білими етикетками зі сміливими червоними китайськими ієрогліфами, готова до завантаження в найближчий торговий автомат пана Чанга."
  components:
  - type: VendingMachineRestock
    canRestock:
    - ChangInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_chinese

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockChefvend
  name: "Коробка для поповнення запасів ChefVend"
  description: "Поповніть ChefVend. Тільки не розбивайте більше яєць."
  components:
  - type: VendingMachineRestock
    canRestock:
    - ChefvendInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_chef

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockCondimentStation
  name: "ящик для поповнення запасів станції приправ"
  description: "Поповни станцію для приправ. Холодний соус."
  components:
  - type: VendingMachineRestock
    canRestock:
    - CondimentInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_condiment

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockClothes
  name: "ящик для поповнення гардеробу"
  description: "Настав час зробити крок назустріч моді! Помістіть у слот для поповнення запасів будь-якого продавця одягу, щоб почати."
  components:
  - type: VendingMachineRestock
    canRestock:
    - ClothesMateInventory
    - AtmosDrobeInventory
    - BarDrobeInventory
    - CargoDrobeInventory
    - ChefDrobeInventory
    - ChemDrobeInventory
    - DetDrobeInventory
    - EngiDrobeInventory
    - GeneDrobeInventory
    - HyDrobeInventory
    - JaniDrobeInventory
    - LawDrobeInventory
    - MediDrobeInventory
    - RoboDrobeInventory
    - SciDrobeInventory
    - SecDrobeInventory
    - ViroDrobeInventory
    - WinterDrobeInventory
    - CuraDrobeInventory
    - MagiVendInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_clothes

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockCostumes
  name: "Коробка для поповнення запасів AutoDrobe"
  description: "Безліч співробітників NanoTrasen грають у барвистому театрі в трагікомедії. Ви теж можете до них приєднатися! Завантажте це в найближчий торговий автомат AutoDrobe."
  components:
  - type: VendingMachineRestock
    canRestock:
    - AutoDrobeInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_costume

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockDinnerware
  name: "Коробка для поповнення запасів Plasteel Chef"
  description: "На цій кухні ніколи не буває сирих продуктів! Щоб почати, киньте в слот для поповнення запасів на пластиковому шеф-кухарі."
  components:
  - type: VendingMachineRestock
    canRestock:
    - DinnerwareInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_dinner

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockDiscountDans
  name: "Знижка на коробку для поповнення запасів Дена"
  description: "Коробка, повна солі та крохмалю. Навіщо страждати якістю, коли можна мати кількість? Знижка від Дена!"
  components:
  - type: VendingMachineRestock
    canRestock:
    - DiscountDansInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_discount

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockDonut
  name: "Міцна коробка для поповнення запасів пончиків"
  description: "Коробка з тороїдальними пачками смаженого тіста для поповнення торгового автомата. Використовувати тільки за вказівкою Robust Industries, LLC."
  components:
  - type: VendingMachineRestock
    canRestock:
    - DonutInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_donuts

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockEngineering
  name: "Коробка для поповнення запасів EngiVend"
  description: "Тільки для використання сертифікованими фахівцями."
  components:
  - type: VendingMachineRestock
    canRestock:
    - EngiVendInventory
    - YouToolInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_engi

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockGames
  name: "Коробка для поповнення запасів Good Clean Fun"
  description: "Час кидати за ініціативою, дракони-кістки! Заряджайтеся в автоматі Good Clean Fun!"
  components:
  - type: VendingMachineRestock
    canRestock:
    - GoodCleanFunInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_games

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockGetmoreChocolateCorp
  name: "Коробка для поповнення запасу шоколаду GetMore"
  description: "Коробка з найкращим ерзац-какао. Тільки для використання в офіційних торгових автоматах Getmore Chocolate."
  components:
  - type: VendingMachineRestock
    canRestock:
    - GetmoreChocolateCorpInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_snack

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockHotDrinks
  name: "Найкраща коробка для поповнення запасів Solar"
  description: "Підсмажений! Для використання в торгових автоматах Solar's Best Hot Drinks або інших торгових автоматах партнерів."
  components:
  - type: VendingMachineRestock
    canRestock:
    - HotDrinksMachineInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_joe

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockMedical
  name: "Коробка для поповнення запасів NanoMed"
  description: "Вставте в NanoMed або NanoMedPlus у вашому відділенні для дозування. Поводьтеся з ним обережно."
  components:
  - type: VendingMachineRestock
    canRestock:
    - NanoMedInventory
    - NanoMedPlusInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_medical

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockNutriMax
  name: "Коробка для поповнення запасів NutriMax"
  description: "Ми зробимо ваші пальці зеленими за допомогою наших інструментів. Приступаємо до збору врожаю! Завантажуємо в торговий автомат NutriMax."
  components:
  - type: VendingMachineRestock
    canRestock:
    - NutriMaxInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_plant

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockPTech
  name: "Коробка для поповнення запасів PTech"
  description: "Вся бюрократія, з якою ви можете впоратися, і навіть більше! Завантажте в торговий автомат PTech, щоб почати."
  components:
  - type: VendingMachineRestock
    canRestock:
    - PTechInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_ptech

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockRobustSoftdrinks
  name: "коробка для поповнення запасів напоїв"
  description: "Холодний, незграбний контейнер з холодними циліндрами, що стикаються. Використовувати тільки за вказівкою Robust Industries, LLC."
  components:
  - type: VendingMachineRestock
    canRestock:
    - RobustSoftdrinksInventory
    - BodaInventory
    - PwrGameInventory
    - ShamblersJuiceInventory
    - StarkistInventory
    - SpaceUpInventory
    - SodaInventory
    - DrGibbInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_cola

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockSecTech
  name: "Коробка для поповнення запасів SecTech"
  description: "Комуністи, стережіться: прибуло підкріплення! На етикетці написано: ТІЛЬКИ ДЛЯ ПЕРСОНАЛУ БЕЗПЕКИ."
  components:
  - type: VendingMachineRestock
    canRestock:
    - SecTechInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_sec

- type: entity
  abstract: true # DeltaV: Salvage vendor doesn't have stock anymore
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockSalvageEquipment
  name: "Коробка для поповнення запасів утилізатора"
  description: "Вдарте об землю, поки космічний короп не вхопив вас за зад! Для початку вріжся в продавця утильсировини."
  components:
  - type: VendingMachineRestock
    canRestock:
    - SalvageEquipmentInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_salvage

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockSeeds
  name: "Коробка для поповнення запасів MegaSeed"
  description: "На етикетці вказано, що це насіння - сімейна цінність, яка передалася від наших предків. Покладіть його в MegaSeed Servitor!"
  components:
  - type: VendingMachineRestock
    canRestock:
    - MegaSeedServitorInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_plant

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockSmokes
  name: "Коробка для поповнення запасів ShadyCigs"
  description: "Важко щось розгледіти під усіма цими попередженнями, але там згадується про завантаження його в торговий автомат."
  components:
  - type: VendingMachineRestock
    canRestock:
    - CigaretteMachineInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_smoke

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockTankDispenser
  name: "ящик для видачі балонів"
  description: "Здатний замінити цистерни видачі газу. Поводьтеся з ним обережно."
  components:
  - type: VendingMachineRestock
    canRestock:
    - TankDispenserEVAInventory
    - TankDispenserEngineeringInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_tanks

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockVendomat
  name: "Коробка для поповнення запасу вендомату"
  description: "Коробка, повна деталей для різних механізмів. Для початку завантажте її у вендомат."
  components:
  - type: VendingMachineRestock
    canRestock:
    - VendomatInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_parts

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockRobotics
  name: "Коробка для поповнення запасів Robotech Deluxe"
  description: "Коробка, повна інструментів для створення боргів. Завантажте її в Robotech Deluxe, щоб почати."
  components:
  - type: VendingMachineRestock
    canRestock:
    - RoboticsInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_robotics

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockHappyHonk
  name: "Коробка для поповнення запасів Happy Honk"
  description: "покладіть цю коробку, повну веселощів, у слот для поповнення запасів на Happy Honk Dispenser, щоб почати."
  components:
  - type: VendingMachineRestock
    canRestock:
    - HappyHonkDispenserInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_honk

- type: entity
  parent: BaseVendingMachineRestock
  id: VendingMachineRestockChemVend
  name: "Коробка для поповнення запасів ChemVend"
  description: "Ящик, наповнений хімікатами та вкритий небезпечними на вигляд діамантами NFPA. Для початку завантажте її в ChemVend."
  components:
  - type: VendingMachineRestock
    canRestock:
    - ChemVendInventory
  - type: Sprite
    layers:
    - state: base
    - state: green_bit
      shader: unshaded
    - state: refill_medical
