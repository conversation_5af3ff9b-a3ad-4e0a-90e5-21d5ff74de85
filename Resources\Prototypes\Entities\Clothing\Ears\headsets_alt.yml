- type: entity
  abstract: true
  parent: ClothingHeadset
  id: ClothingHeadsetAlt
  name: "гарнітура"
  description: "Оновлений модульний інтерком, який одягається на голову. Приймає ключі шифрування."
  components:
  - type: Sprite
    state: icon_alt
  - type: Clothing
    equippedPrefix: alt
  - type: FlashSoundSuppression # Goobstation

- type: entity
  parent: ClothingHeadsetAlt
  id: ClothingHeadsetAltCargo
  name: "навушник для логіста" # DeltaV - Logistics Department replacing Cargo
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyCargo
      - EncryptionKeyCommand
      - EncryptionKeyCommon
  - type: Sprite
    sprite: Clothing/Ears/Headsets/cargo.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/cargo.rsi

- type: entity
  parent: ClothingHeadsetAlt
  id: ClothingHeadsetAltCentCom
  name: "Накладна гарнітура CentCom"
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyCentCom
      - EncryptionKeyStationMaster
  - type: Sprite
    sprite: Clothing/Ears/Headsets/centcom.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/centcom.rsi

- type: entity
  parent: ClothingHeadsetAltCentCom
  id: ClothingHeadsetAltCentComFake
  suffix: Fake
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyCommon

- type: entity
  parent: ClothingHeadsetAlt
  id: ClothingHeadsetAltCommand
  name: "командна навушникова гарнітура"
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyStationMaster
  - type: Sprite
    sprite: Clothing/Ears/Headsets/command.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/command.rsi

- type: entity
  parent: ClothingHeadsetAlt
  id: ClothingHeadsetAltEngineering
  name: "навушник головного інженера"
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyEngineering
      - EncryptionKeyCommand
      - EncryptionKeyCommon
  - type: Sprite
    sprite: Clothing/Ears/Headsets/engineering.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/engineering.rsi

- type: entity
  parent: ClothingHeadsetAlt
  id: ClothingHeadsetAltMedical
  name: "навушник головного лікаря"
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyMedical
      - EncryptionKeyCommand
      - EncryptionKeyCommon
  - type: Sprite
    sprite: Clothing/Ears/Headsets/medical.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/medical.rsi
  - type: StealTarget
    stealGroup: ClothingHeadsetAltMedical

- type: entity
  parent: ClothingHeadsetAlt
  id: ClothingHeadsetAltSecurity
  name: "навушник керівника служби безпеки"
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeySecurity
      - EncryptionKeyCommand
      - EncryptionKeyCommon
  - type: Sprite
    sprite: Clothing/Ears/Headsets/security.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/security.rsi

- type: entity
  parent: ClothingHeadsetAlt
  id: ClothingHeadsetAltScience
  name: "навушник-гарнітура для містифікатора" # DeltaV - Epistemics Department replacing Science
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyScience
      - EncryptionKeyCommand
      - EncryptionKeyCommon
  - type: Sprite
    sprite: Clothing/Ears/Headsets/science.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/science.rsi

- type: entity
  parent: ClothingHeadsetAlt
  id: ClothingHeadsetAltSyndicate
  name: "криваво-червона накладна гарнітура"
  description: "Оновлений модульний синдикатний інтерком, який одягається на голову і приймає ключі шифрування (є 5 слотів для ключів)."
  components:
  - type: Headset
  - type: EncryptionKeyHolder
    keySlots: 5
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeySyndie
  - type: Sprite
    sprite: Clothing/Ears/Headsets/syndicate.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/syndicate.rsi

- type: entity
  parent: ClothingHeadsetAlt
  id: ClothingHeadsetAltFreelancer
  name: "навушник для фрілансера"
  components:
    - type: Headset
    - type: EncryptionKeyHolder
      keySlots: 5
    - type: ContainerFill
      containers:
        key_slots:
          - EncryptionKeyFreelance
    - type: Sprite
      sprite: Clothing/Ears/Headsets/freelance.rsi
    - type: Clothing
      sprite: Clothing/Ears/Headsets/freelance.rsi

# Goobstation start
- type: entity
  parent: ClothingHeadsetAlt
  id: ClothingHeadsetAltSecurityRegular
  name: "гарнітура служби безпеки"
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeySecurity
      - EncryptionKeyCommon
  - type: Sprite
    sprite: Clothing/Ears/Headsets/security.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/security.rsi
# Goobstation end
