- type: entity
  id: BaseLathe
  parent: [ BaseMachinePowered, ConstructibleMachine ]
  abstract: true
  name: "токарний верстат"
  components:
  - type: Appearance
  - type: WiresVisuals
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.4,0.4,0.4"
        density: 190
        mask:
        - MachineMask
        layer:
          - MachineLayer
  - type: Lathe
  - type: MaterialStorage
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:ChangeConstructionNodeBehavior
        node: machineFrame
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: WiresPanel
  - type: ActivatableUI
    key: enum.LatheUiKey.Key
  - type: ActivatableUIRequiresPower
  - type: UserInterface
    interfaces:
      enum.LatheUiKey.Key:
        type: LatheBoundUserInterface
      enum.ResearchClientUiKey.Key:
        type: ResearchClientBoundUserInterface
  - type: Transform
    anchored: true
  - type: Pullable
  - type: StaticPrice
    price: 800
  - type: ResearchClient
  - type: TechnologyDatabase
  - type: Speech # Goobstation - Lathe message on recipes update
    speechSounds: Lathe

# a lathe that can be sped up with space lube / slowed down with glue
- type: entity
  abstract: true
  parent: BaseLathe
  id: BaseLatheLube
  components:
  - type: ReagentSpeed
    solution: lube
    modifiers:
      SpaceLube: 0.25
      SpaceGlue: 5
  - type: SolutionContainerManager
    solutions:
      lube:
        maxVol: 250
  - type: Spillable
    solution: lube
  - type: RefillableSolution
    solution: lube
  - type: ExaminableSolution
    solution: lube

- type: entity
  abstract: true
  id: BaseHyperlathe
  components:
  - type: Lathe
    materialUseMultiplier: 0.5
    timeMultiplier: 1.5
  - type: LatheHeatProducing
  - type: ReagentSpeed
    modifiers:
      SpaceLube: 0.8 # being faster means less heat so lube needs to be nerfed
      SpaceGlue: 5 # no change from normal lathe, overheat!!!

- type: entity
  id: Autolathe
  parent: [BaseLatheLube, BaseMaterialSiloUtilizer]
  name: "автолат"
  description: "Виробляє базові предмети, використовуючи метал та скло."
  components:
  - type: Sprite
    sprite: Structures/Machines/autolathe.rsi
    snapCardinals: true
    layers:
    - state: icon
      map: ["enum.LatheVisualLayers.IsRunning"]
    - state: unlit
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
    - state: inserting
      map: ["enum.MaterialStorageVisualLayers.Inserting"]
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: Machine
    board: AutolatheMachineCircuitboard
  - type: MaterialStorage
    whitelist:
      tags:
      - Sheet
      - RawMaterial
      - Ingot
      - Wooden
      - ClothMade
      - Gauze
      - Metal
  - type: Lathe
    idleState: icon
    runningState: building
    staticRecipes:
    - Wirecutter
    - Igniter
    - Signaller
    - Screwdriver
    - Welder
    - Wrench
    - Crowbar
    - Multitool
    - NetworkConfigurator
    - AccessConfigurator
    - SprayPainter
    - FlashlightLantern
    - CableStack
    - CableMVStack
    - CableHVStack
    - HandheldGPSBasic
    - TRayScanner
    - AirTank
    - GasAnalyzer
    - UtilityBelt
    - Fulton
    - FultonBeacon
    - Pickaxe
    - ModularReceiver
    - AppraisalTool
    - SheetRGlass
    - Beaker
    - Syringe
    - DisposableSyringe
    - HandLabeler
    - LightTube
    - LedLightTube
    - SodiumLightTube
    - ExteriorLightTube
    - LightBulb
    - LedLightBulb
    - Bucket
    - DrinkMug
    - DrinkMugMetal
    - DrinkGlass
    - DrinkShotGlass
    - DrinkGlassCoupeShaped
    - CustomDrinkJug
    - FoodPlate
    - FoodPlateSmall
    - FoodPlatePlastic
    - FoodPlateSmallPlastic
    - FoodBowlBig
    - FoodPlateTin
    - FoodKebabSkewer
    - SprayBottle
    - MopItem
    - Holoprojector
    - Mousetrap
    - LightReplacer
    - TrashBag
    - PowerCellSmall
    - PowerCellMedium
    - RollerBedSpawnFolded
    - CheapRollerBedSpawnFolded
    - EmergencyRollerBedSpawnFolded
    - MicroManipulatorStockPart
    - MatterBinStockPart
    - CapacitorStockPart
    - ConveyorBeltAssembly
    - IntercomElectronics
    - FirelockElectronics
    - DoorElectronics
    - AirAlarmElectronics
    - StationMapElectronics
    - FireAlarmElectronics
    - MailingUnitElectronics
    - SignalTimerElectronics
    - APCElectronics
    - SMESMachineCircuitboard
    - SubstationMachineCircuitboard
    - WallmountSubstationElectronics
    - CellRechargerCircuitboard
    - BorgChargerCircuitboard
    - WeaponCapacitorRechargerCircuitboard
    - HydroponicsTrayMachineCircuitboard
    - HandheldStationMap
    - ClothingHeadHatWelding
    - ProgramDiskUnburnt # Arcadis - Modular Computer System
    - FauxTileAstroGrass
    - FauxTileMowedAstroGrass
    - FauxTileJungleAstroGrass
    - FauxTileAstroIce
    - FauxTileAstroSnow
    - CanilunztTranslator
    - BubblishTranslator
    - NekomimeticTranslator
    - DraconicTranslator
    - SolCommonTranslator
    - NovuNedericTranslator
    - RootSpeakTranslator
    - BasicGalaticCommonTranslatorImplanter
    - MofficTranslator
    - N14MaterialCircuitry
    - VehicleWheelchairFolded # Goobstation
  - type: EmagLatheRecipes
    emagStaticRecipes:
    - BoxLethalshot
    - BoxShotgunLumen
    - BoxShotgunFlare
    - BoxShotgunSlug
    - MagazineBoxLightRifle
    - MagazineBoxMagnum
    - MagazineBoxPistol
    - MagazineBoxRifle
    - MagazineLightRifle
    - MagazineLightRifleEmpty
    - MagazinePistol
    - MagazinePistolEmpty
    - MagazinePistolSubMachineGun
    - MagazinePistolSubMachineGunEmpty
    - MagazinePistolSubMachineGunTopMounted
    - MagazinePistolSubMachineGunTopMountedEmpty
    - MagazineRifle
    - MagazineRifleEmpty
    - MagazineShotgun
    - MagazineShotgunEmpty
    - MagazineShotgunSlug
    - RiotShield
    - SpeedLoaderMagnum
    - SpeedLoaderMagnumEmpty
    - MagazineLightRifleMarkOneEmpty
    - MagazineLightRifleMarkOne
    - GrenadeFlashBang # Goobstation
    # .35 Caseless Pistol
    - MagazinePistolCaselessRiflePractice # EE
    - MagazinePistolCaselessRifle # EE
    - MagazinePistolCaselessRifleRubber # EE
    # .35 Caseless (Civilian) Rifle
    - MagazineCaselessRifleShortPractice # EE
    - MagazineCaselessRifleShort # EE
    - MagazineCaselessRifleShortRubber # EE
    # .35 Caseless (Military) Rifle
    - MagazineCaselessRiflePractice # EE
    - MagazineCaselessRifle # EE
    - MagazineCaselessRifleRubber # EE
    # .35 Caseless (Box Mag) Rifle
    - MagazineBoxCaselessRiflePractice # EE
    - MagazineBoxCaselessRifle # EE
    - MagazineBoxCaselessRifleRubber # EE
    - MagazineMagnumEmpty # Goobstation
    - MagazineMagnum # Goobstation
    - MagazineMagnumLeverRifleEmpty # Goobstation
    - MagazineMagnumLeverRifle # Goobstation
    - CartridgeEpinephrine # Goobstation
    - CartridgeBicaridine # Goobstation
    - CartridgeDermaline # Goobstation
    - CartridgeEphedrine # Goobstation
    - CartridgeAtropine # Goobstation
    - CartridgePuncturase # Goobstation
    - SecHypo # Goobstation
    - ParamedHypo # Goobstation - ParamedHypo

- type: entity
  id: AutolatheHyperConvection
  parent: [Autolathe, BaseHyperlathe]
  name: "гіперконвекційний автолат"
  description: "Високоекспериментальний автолат, який використовує потужність надзвичайного тепла для повільного та більш економічно ефективного створення об'єктів."
  components:
  - type: Sprite
    sprite: Structures/Machines/autolathe_hypercon.rsi
  - type: Machine
    board: AutolatheHyperConvectionMachineCircuitboard

- type: entity
  id: Protolathe
  parent: [BaseLatheLube, BaseMaterialSiloUtilizer]
  name: "протолат"
  description: "Перетворює сировину на вдосконалені предмети."
  components:
  - type: Sprite
    sprite: Structures/Machines/protolathe.rsi
    snapCardinals: true
    layers:
    - state: icon
      map: ["enum.LatheVisualLayers.IsRunning"]
    - state: unlit
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
    - state: inserting
      map: ["enum.MaterialStorageVisualLayers.Inserting"]
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: Machine
    board: ProtolatheMachineCircuitboard
  - type: MaterialStorage
    whitelist:
      tags:
      - Sheet
      - RawMaterial
      - Ingot
      - Wooden
      - ClothMade
      - Gauze
      - Metal
  - type: Lathe
    idleState: icon
    runningState: building
    staticRecipes:
    - LargeBeaker
    - Dropper
    - ClothingEyesGlassesChemical
    - N14MaterialCircuitry
    dynamicRecipes:
    - AntiMindControlDevice
    - PowerDrill
    - MiningDrill
    - MiningDrillDiamond
    - AnomalyScanner
    - AnomalyLocator
    - AnomalyLocatorWide
    - HandheldCrewMonitor
    - Scalpel
    - Retractor
    - Cautery
    - Drill
    - WeaponParticleDecelerator
    - HoloprojectorField
    - Saw
    - Hemostat
    - CryostasisBeaker
    - SyringeCryostasis
    - Syringe
    - DisposableSyringe
    - Implanter
    - PillCanister
    - ChemistryEmptyBottle01
    - AdvancedCapacitorStockPart
    - AdvancedMatterBinStockPart
    - NanoManipulatorStockPart
    - SuperCapacitorStockPart
    - SuperMatterBinStockPart
    - PicoManipulatorStockPart
    - BluespaceCapacitorStockPart
    - BluespaceManipulatorStockPart
    - BluespaceMatterBinStockPart
    - AdvMopItem
    - WeaponSprayNozzle
    - ClothingBackpackWaterTank
    - MegaSprayBottle
    - TimerTrigger
    - ChemicalPayload
    - FlashPayload
    - Signaller
    - SignallerAdvanced
    - SignalTrigger
    - VoiceTrigger
    - Igniter
    - HandHeldMassScanner
    - PowerCellMicrortg
    - PowerCellMicroreactor
    - PowerCellHigh
    - WeaponPistolCHIMP
    - ClothingMaskWeldingGas
    - WeaponGauntletGorilla
    - SynthesizerInstrument
    - RPED
    - ClothingShoesBootsMag
    - ClothingShoesBootsMoon
    - ClothingShoesBootsSpeed
    - ClothingShoesBootsMagAdv
    - NodeScanner
    - HolofanProjector
    - BluespaceBeaker
    - SyringeBluespace
  #  - WeaponForceGun
    - WeaponLaserSvalinn
    - WeaponCivilianDisabler
    - WeaponProtoKineticAccelerator
  #  - WeaponTetherGun
  #  - WeaponGrapplingGun
    - ClothingBackpackHolding
    - ClothingBackpackSatchelHolding
    - ClothingBackpackDuffelHolding
    - WelderExperimental
    - JawsOfLife
    - CoreSilver # Nyanotrasen - Silver Golem core
    - OreBagOfHolding
    - DeviceQuantumSpinInverter
    - PKAUpgradeDamage # Lavaland Change
    - PKAUpgradeRange # Lavaland Change
    - PKAUpgradeFireRate # Lavaland Change
    - XenoTranslator
    - AdvancedGalaticCommonTranslatorImplanter
    - BubblishTranslatorImplanter
    - NekomimeticTranslatorImplanter
    - DraconicTranslatorImplanter
    - CanilunztTranslatorImplanter
    - SolCommonTranslatorImplanter
    - RootSpeakTranslatorImplanter
    - AnimalTranslator
    - MofficTranslatorImplanter
    - ClothingEyesNightVisionGoggles
    - ClothingEyesNightVisionDiagnosticGoggles
    - ClothingEyesThermalVisionGoggles
    - RCDAmmo #DeltaV
    - RCD #EE
    - RPD
    - WeaponPlasmaCutter # Goobstation
    - WeaponPlasmaRifle # Goobstation
    # Shitmed Change
    - EnergyScalpel
    - EnergyCautery
    - AdvancedRetractor
    - VehicleWheelchairFolded # Goobstation
  - type: EmagLatheRecipes
    emagDynamicRecipes:
    - BoxBeanbag
    - BoxShotgunIncendiary
    - BoxShotgunUranium
    - ExplosivePayload
    - GrenadeBlast
    - GrenadeEMP
    - GrenadeFlash
    - HoloprojectorSecurity
    - MagazineBoxLightRifleIncendiary
    - MagazineBoxLightRifleUranium
    - MagazineBoxMagnumIncendiary
    - MagazineBoxMagnumUranium
    - MagazineBoxPistolIncendiary
    - MagazineBoxPistolUranium
    - MagazineBoxRifleIncendiary
    - MagazineBoxRifleUranium
    - MagazineGrenadeEmpty
    - MagazineLightRifleIncendiary
    - MagazineLightRifleUranium
    - MagazinePistolIncendiary
    - MagazinePistolUranium
    - MagazineRifleIncendiary
    - MagazineRifleUranium
    - MagazineShotgunBeanbag
    - MagazineShotgunIncendiary
    - MagazineShotgunIncendiary
    - PortableRecharger
    - PowerCageHigh
    - PowerCageMedium
    - PowerCageSmall
    - ShellTranquilizer
    - SpeedLoaderMagnumIncendiary
    - SpeedLoaderMagnumUranium
    - TelescopicShield
    - Truncheon
    - WeaponAdvancedLaser
    - WeaponLaserCannon
    - WeaponLaserCarbine
    - WeaponXrayCannon
    - RapidSyringeGun # Goobstation
    - WeaponEnergyGun       # DeltaV - Energy Gun
    - WeaponEnergyGunMini   # DeltaV - Miniature Energy Gun
    - WeaponEnergyGunPistol # DeltaV - PDW-9 Energy Pistol
    - WeaponGunLaserCarbineAutomatic # DeltaV - IK-60 Laser Carbine

- type: entity
  id: ProtolatheHyperConvection
  parent: [Protolathe, BaseHyperlathe]
  name: "гіперконвекційний протолат"
  description: "Високоекспериментальний протолат, який використовує потужність надзвичайного тепла для повільного та більш економічно ефективного створення об'єктів."
  components:
  - type: Sprite
    sprite: Structures/Machines/protolathe_hypercon.rsi
  - type: Machine
    board: ProtolatheHyperConvectionMachineCircuitboard

- type: entity
  id: CircuitImprinter
  parent: [BaseLatheLube, BaseMaterialSiloUtilizer]
  name: "друкар плат"
  description: "Друкує плати для механізмів."
  components:
  - type: Sprite
    sprite: Structures/Machines/circuit_imprinter.rsi
    snapCardinals: true
    layers:
    - state: icon
      map: ["enum.LatheVisualLayers.IsRunning"]
    - state: unlit
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: Machine
    board: CircuitImprinterMachineCircuitboard
  - type: Lathe
    producingSound: /Audio/Machines/circuitprinter.ogg
    idleState: icon
    runningState: building
    staticRecipes:
    - ProtolatheMachineCircuitboard
    - AutolatheMachineCircuitboard
    - BookPressMachineCircuitboard
    - CircuitImprinterMachineCircuitboard
    - OreProcessorMachineCircuitboard
    - MaterialReclaimerMachineCircuitboard
    - ElectrolysisUnitMachineCircuitboard
    - CentrifugeMachineCircuitboard
    - ChemDispenserMachineCircuitboard
    - ChemMasterMachineCircuitboard
    - CondenserMachineCircuitBoard
    - HotplateMachineCircuitboard
    - PrinterDocMachineCircuitboard  # Corvax-Printer
    - UniformPrinterMachineCircuitboard
    - FloorGreenCircuit
    - FloorBlueCircuit
    - MicrowaveMachineCircuitboard
    - ReagentGrinderMachineCircuitboard
    - ElectricGrillMachineCircuitboard
    - BoozeDispenserMachineCircuitboard
    - SodaDispenserMachineCircuitboard
    - DeepFryerMachineCircuitboard #Nyano - Summary: adds deep fryer circuit board
    - SpaceHeaterMachineCircuitBoard
    - StationAnchorCircuitboard
    - SeedExtractorMachineCircuitboard
    - ReagentGrinderIndustrialMachineCircuitboard
    - StasisBedMachineCircuitboard
    - CryoPodMachineCircuitboard
    - N14MaterialCircuitry
    dynamicRecipes:
    - ThermomachineFreezerMachineCircuitBoard
    - DroneElectronics
    - HellfireFreezerMachineCircuitBoard
    - PortableScrubberMachineCircuitBoard
    - CloningPodMachineCircuitboard
    - MedicalScannerMachineCircuitboard
    - VaccinatorMachineCircuitboard
    - DiagnoserMachineCircuitboard
    - BiomassReclaimerMachineCircuitboard
    - BiofabricatorMachineCircuitboard
    - SurveillanceCameraRouterCircuitboard
    - SurveillanceCameraMonitorCircuitboard
    - SurveillanceWirelessCameraMonitorCircuitboard
    - SurveillanceCameraWirelessRouterCircuitboard
    - ComputerTelevisionCircuitboard
    - JukeboxCircuitBoard
    - SurveillanceWirelessCameraMovableCircuitboard
    - SurveillanceWirelessCameraAnchoredCircuitboard
    - SolarControlComputerCircuitboard
    - SolarTrackerElectronics
    - TurboItemRechargerCircuitboard
    - PowerComputerCircuitboard
    - AlertsComputerCircuitboard
    - AutolatheHyperConvectionMachineCircuitboard
    - ProtolatheHyperConvectionMachineCircuitboard
    - CircuitImprinterHyperConvectionMachineCircuitboard
    - FatExtractorMachineCircuitboard
    - FlatpackerMachineCircuitboard
    - SheetifierMachineCircuitboard
    - ShuttleConsoleCircuitboard
    - RadarConsoleCircuitboard
    - TechDiskComputerCircuitboard
    - DawInstrumentMachineCircuitboard
    - CloningConsoleComputerCircuitboard
    - OreProcessorIndustrialMachineCircuitboard
    - CargoTelepadMachineCircuitboard
    - RipleyCentralElectronics
    - RipleyPeripheralsElectronics
    - HonkerCentralElectronics
    - HonkerPeripheralsElectronics
    - HonkerTargetingElectronics
    - HamtrCentralElectronics
    - HamtrPeripheralsElectronics
    - PortableGeneratorPacmanMachineCircuitboard
    - PortableGeneratorSuperPacmanMachineCircuitboard
    - PortableGeneratorJrPacmanMachineCircuitboard
    - WallmountGeneratorElectronics
    - WallmountGeneratorAPUElectronics
    - WallmountSubstationElectronics
    - PowerCageRechargerCircuitboard
    - EmitterCircuitboard
    - ThrusterMachineCircuitboard
    - GyroscopeMachineCircuitboard
    - MiniGravityGeneratorCircuitboard
    - ShuttleGunKineticCircuitboard
    - GasRecyclerMachineCircuitboard
    - AnalysisComputerCircuitboard
    - ExosuitFabricatorMachineCircuitboard
    - AnomalyVesselCircuitboard
    - AnomalyVesselExperimentalCircuitboard
    - AnomalySynchronizerCircuitboard
    - APECircuitboard
    - ArtifactAnalyzerMachineCircuitboard
    - ArtifactCrusherMachineCircuitboard
    - TelecomServerCircuitboard
    - MassMediaCircuitboard
    - ReverseEngineeringMachineCircuitboard
    - CrewMonitoringComputerCircuitboard
    - DoorElectronics
    - FireAlarmElectronics
    - FirelockElectronics
    - IntercomElectronics
    - MailingUnitElectronics
    - SalvageMagnetMachineCircuitboard
    - StationMapElectronics
    - MetempsychoticMachineCircuitboard
    - SalvageExpeditionsComputerCircuitboard
    - JukeboxCircuitBoard
    - AutodocCircuitboard # Shitmed Change
#    - OperatingTableCircuitboard # Shitmed Change
    - MaterialSiloCircuitboard
    - GygaxCentralElectronics
    - GygaxPeripheralsElectronics
    - GygaxTargetingElectronics
    - DurandCentralElectronics
    - DurandPeripheralsElectronics
    - DurandTargetingElectronics
    - ClarkeCentralElectronics
    - ClarkePeripheralsElectronics
    - SMESAdvancedMachineCircuitboard
    - BaseComputerModularCircuitBoard # Arcadis - Modular Computer System
    - DiskBurnerMachineCircuitboard # Arcadis - Modular Computer System
  - type: EmagLatheRecipes
    emagDynamicRecipes:
    - ShuttleGunDusterCircuitboard
    - ShuttleGunFriendshipCircuitboard
    - ShuttleGunPerforatorCircuitboard
    - ShuttleGunSvalinnMachineGunCircuitboard
  - type: MaterialStorage
    whitelist:
      tags:
      - Sheet
      - RawMaterial
      - Ingot
      - Wooden
      - ClothMade
      - Gauze
      - Metal
  - type: RequireProjectileTarget

- type: entity
  id: CircuitImprinterHyperConvection
  parent: [CircuitImprinter, BaseHyperlathe]
  name: "гіперконвекційний друкар плат"
  description: "Високоекспериментальний друкар плат, який використовує потужність надзвичайного тепла для повільного та більш економічно ефективного створення об'єктів."
  components:
  - type: Sprite
    sprite: Structures/Machines/circuit_imprinter_hypercon.rsi
  - type: Machine
    board: CircuitImprinterHyperConvectionMachineCircuitboard

- type: entity
  id: ExosuitFabricator
  parent: [BaseLatheLube, BaseMaterialSiloUtilizer]
  name: "фабрикатор екзокостюмів"
  description: "Створює деталі для робототехніки та інших механічних потреб"
  components:
  - type: Sprite
    sprite: Structures/Machines/exosuit_fabricator.rsi
    layers:
    - state: fab-idle
      map: ["enum.LatheVisualLayers.IsRunning"]
    - state: fab-load
      map: ["enum.MaterialStorageVisualLayers.Inserting"]
    - state: fab-o
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: Machine
    board: ExosuitFabricatorMachineCircuitboard
  - type: Lathe
    idleState: fab-idle
    runningState: fab-active
    staticRecipes:
    - MMI
    - PositronicBrain
    - SciFlash
    - BorgModuleCable
    - BorgModuleFireExtinguisher
    - BorgModuleRadiationDetection
    - BorgModuleTool
    - BorgModuleAppraisal
    - BorgModuleConstruction
    - BorgModuleService
    - BorgModuleTreatment
    - BorgModuleSurgery
    - BorgModuleCleaning
    - CyborgEndoskeleton
    - LeftArmBorg
    - RightArmBorg
    - LeftLegBorg
    - RightLegBorg
    - LightHeadBorg
    - TorsoBorg
    - LeftArmBorgEngineer
    - RightArmBorgEngineer
    - LeftLegBorgEngineer
    - RightLegBorgEngineer
    - HeadBorgEngineer
    - TorsoBorgEngineer
    - LeftLegBorgJanitor
    - RightLegBorgJanitor
    - HeadBorgJanitor
    - TorsoBorgJanitor
    - LeftArmBorgMedical
    - RightArmBorgMedical
    - LeftLegBorgMedical
    - RightLegBorgMedical
    - HeadBorgMedical
    - TorsoBorgMedical
    - LeftArmBorgMining
    - RightArmBorgMining
    - LeftLegBorgMining
    - RightLegBorgMining
    - HeadBorgMining
    - TorsoBorgMining
    - LeftArmBorgService
    - RightArmBorgService
    - LeftLegBorgService
    - RightLegBorgService
    - HeadBorgService
    - TorsoBorgService
    - MechAirTank # Goobstation
    - MechThruster # Goobstation
    - PowerCageMedium # Goobstation - Powercell to exosuit fab
    - PowerCageSmall # Goobstation - Powercell to exosuit fab
    dynamicRecipes:
    - ProximitySensor
    - BorgModuleLightReplacer
    - BorgModuleAdvancedCleaning
    - BorgModuleMining
  #  - BorgModuleGrapplingGun
    - BorgModuleAdvancedTool
    - BorgModuleGPS
    - BorgModuleRCD
    - BorgModuleJetpack
    - BorgModulePka
    - BorgModuleArtifact
    - BorgModuleAnomaly
    - BorgModuleGardening
    - BorgModuleHarvesting
    - BorgModuleMusique
    - BorgModuleClowning
    - BorgModuleDiagnosis
    - BorgModuleDefibrillator
    - BorgModuleAdvancedTreatment
    - BorgModuleAdvancedSurgery
    - JawsOfLifeLeftArm
    - JawsOfLifeRightArm
    - SpeedLeftLeg
    - SpeedRightLeg
    - BasicCyberneticEyes
    - RipleyHarness
    - RipleyLArm
    - RipleyRArm
    - RipleyLLeg
    - RipleyRLeg
    - RipleyMKIIHarness
    - RipleyUpgradeKit
    - MechEquipmentGrabber
    - HonkerHarness
    - HonkerLArm
    - HonkerRArm
    - HonkerLLeg
    - HonkerRLeg
    - MechEquipmentHorn
    - MechEquipmentGrabberSmall
    - HamtrHarness
    - HamtrLArm
    - HamtrRArm
    - HamtrLLeg
    - HamtrRLeg
    - VimHarness
    # Begin Nyano additions
    - JetpackBlue
    - JetpackMini
  # End Nyano additions
    - ClarkeHarness
    - ClarkeHead
    - ClarkeLArm
    - ClarkeRArm
    - ClarkeTreads
    - DurandHarness
    - DurandArmor
    - DurandHead
    - DurandLArm
    - DurandLLeg
    - DurandRArm
    - DurandRLeg
    - GygaxHarness
    - GygaxArmor
    - GygaxHead
    - GygaxLArm
    - GygaxLLeg
    - GygaxRArm
    - GygaxRLeg
    - MechEquipmentDrill
    - MechEquipmentDrillDiamond
    - MechEquipmentKineticAccelerator
    - MechEquipmentHonkerBananaMortar
    - MechEquipmentHonkerMousetrapMortar
    # Goobstation - Modsuits
    - ModsuitChestplate
    - ModsuitBoots
    - ModsuitHelmet
    - ModsuitGauntlets
    - ModsuitShell
    - ModsuitPlatingExternal
    - PowerCageHigh # Goobstation - Powercell to exosuit fab
    - ReverseMMI # IPCMMI
  - type: EmagLatheRecipes
    emagDynamicRecipes:
    - WeaponMechCombatImmolationGun
    - WeaponMechCombatSolarisLaser
    - WeaponMechCombatFiredartLaser
    - WeaponMechCombatUltraRifle
    - WeaponMechCombatShotgun
    - WeaponMechCombatShotgunIncendiary
    - WeaponMechCombatDisabler
    - WeaponMechCombatFlashbangLauncher
    - WeaponMechCombatMissileRack8
  - type: MaterialStorage
    whitelist:
      tags:
      - Sheet
      - RawMaterial
      - Ingot
      - Wooden
      - ClothMade
      - Gauze
      - Metal
  - type: GuideHelp
    guides:
    - Robotics

- type: entity
  id: Biofabricator
  parent: [BaseLathe, BaseMaterialSiloUtilizer]
  name: "біофабрикатор"
  description: "Виробляє куби тварин, використовуючи біомасу."
  components:
  - type: Sprite
    sprite: Structures/Machines/biofabricator.rsi
    snapCardinals: true
    layers:
    - state: icon
      map: ["enum.LatheVisualLayers.IsRunning"]
      color: "#ffaa99"
    - state: unlit
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
      color: "#ffaaaa"
    - state: inserting
      map: ["enum.MaterialStorageVisualLayers.Inserting"]
      color: "#ffaaaa"
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: Machine
    board: BiofabricatorMachineCircuitboard
  - type: MaterialStorage
    ignoreColor: true
    whitelist:
      tags:
      - Sheet
      - RawMaterial
      - Ingot
      - Wooden
      - ClothMade
      - Gauze
      - Metal
  - type: Lathe
    idleState: icon
    runningState: building
    staticRecipes:
    - MonkeyCube
    - KoboldCube
    - CowCube
    - GoatCube
    - MothroachCube
    - MouseCube
    - CockroachCube
  - type: EmagLatheRecipes
    emagStaticRecipes:
    - AbominationCube
    - SpaceCarpCube
    - SpaceTickCube

- type: entity
  id: SecurityTechFab
  parent: [BaseLatheLube, BaseMaterialSiloUtilizer]
  name: "техфаб служби безпеки"
  description: "Друкує обладнання для використання службою безпеки."
  components:
  - type: Sprite
    sprite: Structures/Machines/techfab.rsi
    layers:
    - state: icon
      map: ["enum.LatheVisualLayers.IsRunning"]
    - state: sec
    - state: unlit
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
    - state: inserting
      map: ["enum.MaterialStorageVisualLayers.Inserting"]
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: Machine
    board: SecurityTechFabCircuitboard
  - type: Lathe
    idleState: icon
    runningState: icon
    staticRecipes:
      - BoxLethalshot
      - BoxShotgunLumen
      - BoxShotgunFlare
      - BoxShotgunPractice
      - BoxShotgunSlug
      - ClothingEyesHudSecurity
      - CombatKnife
      - Flash
      - ForensicPad
      - Handcuffs
      - ShellShotgun
      - ShellShotgunSlug
      - ShellShotgunLumen
      - ShellShotgunFlare
      - ShellTranquilizer
      - MagazineBoxLightRifle
      - MagazineBoxLightRiflePractice
      - MagazineBoxMagnum
      - MagazineBoxMagnumPractice
      - MagazineBoxPistol
      - MagazineBoxPistolPractice
      - MagazineBoxRifle
      - MagazineBoxRiflePractice
      - MagazineBoxLightRifle
      - MagazineLightRifle
      - MagazineLightRifleEmpty
      - MagazinePistol
      - MagazinePistolEmpty
      - MagazinePistolSubMachineGun
      - MagazinePistolSubMachineGunEmpty
      - MagazinePistolSubMachineGunTopMounted
      - MagazinePistolSubMachineGunTopMountedEmpty
      # .35 Caseless Pistol
      - MagazinePistolCaselessRiflePractice # EE
      - MagazinePistolCaselessRifle # EE
      - MagazinePistolCaselessRifleRubber # EE
      # .35 Caseless (Civilian) Rifle
      - MagazineCaselessRifleShortPractice # EE
      - MagazineCaselessRifleShort # EE
      - MagazineCaselessRifleShortRubber # EE
      # .35 Caseless (Military) Rifle
      - MagazineCaselessRiflePractice # EE
      - MagazineCaselessRifle # EE
      - MagazineCaselessRifleRubber # EE
      # .35 Caseless (Box Mag) Rifle
      - MagazineBoxCaselessRiflePractice # EE
      - MagazineBoxCaselessRifle # EE
      - MagazineBoxCaselessRifleRubber # EE
      - MagazineMagnum
      - MagazineMagnumEmpty
      - MagazineRifle
      - MagazineRifleEmpty
      - MagazineShotgun
      - MagazineShotgunEmpty
      - MagazineShotgunSlug
      - RiotShield
      - SpeedLoaderMagnum
      - SpeedLoaderMagnumEmpty
      - Stunbaton
      - TargetClown
      - ClothingOuterArmorPlateCarrier
      - ClothingOuterArmorDuraVest
      - TargetHuman
      - TargetSyndicate
      - WeaponDisablerPractice
      - WeaponFlareGunSecurity
      - WeaponLaserCarbinePractice
      - Zipties
      - GrenadeFlashBang # Goobstation
      - ShockCollar
      # DeltaV - .38 special ammo - Add various .38 special ammo to security techfab
      - MagazineBoxSpecial
      - MagazineBoxSpecialPractice
      - SpeedLoaderSpecial
      - MagazinePistolSpecial
      - SyringeGun #Goobstation
      - HandheldGPSBasic
      - HandheldStationMap
      - MagazineNovaliteC1Empty
      - MagazineNovaliteC1
      - MagazineLightRifleLowCapacityEmpty
      - MagazineLightRifleLowCapacity
      - SpeedLoaderRifleHeavy
      - SpeedLoaderRifleHeavyEmpty
      - WeaponRifleNovaliteC1
      - WeaponRifleGestio
      - MagazineMagnumLeverRifle # Goobstation
      - MagazineMagnumLeverRifleEmpty # Goobstation
      - ClothingEyesGlassesSecurity
      - TelescopicShield
      - HoloprojectorSecurity
      - BolaEnergy
      - ClothingHeadHelmetBasic
      - MagazineShotgunBeanbag
      - ShellTranquilizer
      - CartridgeEpinephrine # Goobstation
      - CartridgeBicaridine # Goobstation
      - CartridgeDermaline # Goobstation
      - CartridgeAtropine # Goobstation
      - CartridgeSaline # Goobstation
      - SecHypo # Goobstation
      - BoxBeanbag
      - CartridgePistolRubber
      - CartridgeMagnumRubber
      - CartridgeLightRifleRubber
      - CartridgeRifleRubber
      - MagazineRifleRubber
      - MagazinePistolRubber
      - MagazinePistolSubMachineGunRubber
      - MagazineMagnumRubber
      - MagazineLightRifleRubber
      - SpeedLoaderMagnumRubber
      - MagazineBoxPistolRubber
      - MagazineBoxMagnumRubber
      - MagazineBoxLightRifleRubber
      - MagazineBoxRifleRubber
      - CartridgeSpecialRubber
      - MagazineBoxSpecialRubber
      - MagazineBoxLightRifleRubber # Frontier
      - MagazineBoxMagnumRubber # Frontier
      - MagazineBoxPistolRubber # Frontier
      - MagazineBoxRifleRubber # Frontier
      - SpeedLoaderRifleHeavyRubber
      - BoxShotgunBirdshot
      - BoxShotgun00Buckshot
      - BoxShotgun0000Buckshot
      - MagazineUniversalMagnumEmpty
      - MagazineUniversalMagnum
      - MagazineUniversalMagnumRubber
      - MagazineLightRifleMarkOneEmpty
      - MagazineLightRifleMarkOne
      - CassetteTape # DeltaV
      - TapeRecorder # DeltaV
    dynamicRecipes:
      - Truncheon
      - Terminus
      - BoxShotgunIncendiary
      - BoxShotgunUranium
      - EncryptionKeySyndie
      - CartridgeLightRifleIncendiary
      - CartridgeMagnumIncendiary
      - CartridgePistolIncendiary
      - CartridgeRifleIncendiary
      - CartridgeLightRifleUranium
      - CartridgeMagnumUranium
      - CartridgePistolUranium
      - CartridgeRifleUranium
      - ExplosivePayload
      - FlashPayload
      - GrenadeBlast
      - GrenadeEMP
      - GrenadeFlash
      - MagazineBoxLightRifleIncendiary
      - MagazineBoxLightRifleUranium
      - MagazineBoxMagnumIncendiary
      - MagazineBoxMagnumUranium
      - MagazineBoxPistolIncendiary
      - MagazineBoxPistolUranium
      - MagazineBoxRifleIncendiary
      - MagazineBoxRifleUranium
      - ShellSoulbreaker
      - MagazineGrenadeEmpty
      - MagazineLightRifleIncendiary
      - MagazineLightRifleUranium
      - MagazinePistolIncendiary
      - MagazinePistolUranium
      - MagazinePistolSubMachineGunIncendiary
      - MagazinePistolSubMachineGunUranium
      - MagazineMagnumIncendiary
      - MagazineMagnumUranium
      - MagazineRifleIncendiary
      - MagazineRifleUranium
      - MagazineShotgunIncendiary
      - PortableRecharger
      - PowerCageHigh
      - PowerCageMedium
      - PowerCageSmall
      - ShuttleGunDusterCircuitboard
      - ShuttleGunFriendshipCircuitboard
      - ShuttleGunPerforatorCircuitboard
      - ShuttleGunSvalinnMachineGunCircuitboard
      - Signaller
      - SignalTrigger
      - SpeedLoaderMagnumIncendiary
      - SpeedLoaderMagnumUranium
      - SpeedLoaderRifleHeavyIncendiary
      - SpeedLoaderRifleHeavyUranium
      - TimerTrigger
      - VoiceTrigger
      - WeaponAdvancedLaser
      - WeaponDisabler
      - WeaponDisablerSMG
      - WeaponLaserCannon
      - WeaponLaserCarbine
      - ClothingEyesNightVisionSecurityGoggles
      - ClothingHeadHelmetInsulated # Nyanotrasen - Insulative headgear
      - ClothingHeadCage # Nyanotrasen - Insulative headgear
      - ShockCollar # Nyanotrasen - Shock Collar
      - WeaponXrayCannon
      - WeaponEnergyGun
      - WeaponEnergyGunMini
      - WeaponEnergyGunPistol
      - WeaponGunLaserCarbineAutomatic
      - CartridgeSpecialIncendiary
      - CartridgeSpecialUranium
      - CartridgeSpecialHoly
      - CartridgeSpecialMindbreaker
      - MagazineBoxSpecialIncendiary
      - MagazineBoxSpecialUranium
      - MagazineBoxSpecialMindbreaker
      - SecurityCyberneticEyes
      - MedicalCyberneticEyes
      - ClothingShoesBootsMagCom #Pirate
      - ClothingOuterHardsuitCombatStandard
      - ClothingOuterHardsuitCombatMedical
      - ClothingOuterHardsuitCombatRiot
      - ClothingOuterHardsuitCombatAdvanced
      - ClothingOuterHardsuitShanlinUnpainted
      - ClothingOuterHardsuitShiweiUnpainted
      - ClothingOuterHardsuitJuggernautReverseEngineered
      - WeaponMechCombatImmolationGun
      - WeaponMechCombatSolarisLaser
      - WeaponMechCombatFiredartLaser
      - WeaponMechCombatUltraRifle
      - WeaponMechCombatShotgun
      - WeaponMechCombatShotgunIncendiary
      - WeaponMechCombatDisabler
      - WeaponMechCombatFlashbangLauncher
      - WeaponMechCombatMissileRack8
      - EnergySword
      - EnergySwordDouble
      - EnergyCutlass
      - WeaponEnergyTurretAIMachineCircuitboard
      - WeaponEnergyTurretStationMachineCircuitboard
      - WeaponEnergyTurretAIControlPanelElectronics
      - WeaponEnergyTurretStationControlPanelElectronics
      - WeaponSubMachineGunFPA90
      - WeaponSubMachineGunBRDIR25
      - WeaponPistolMk58
      - WeaponPistolN1984
      - WeaponPistolViper
      - WeaponPistolCobra
      - MagazineRifleShrapnel
      - MagazinePistolShrapnel
      - MagazinePistolSubMachineGunShrapnel
      - MagazineMagnumShrapnel
      - MagazineLightRifleShrapnel
      - MagazineCaselessRifleShrapnel
      - MagazineCaselessRifleIncendiary
      - MagazineCaselessRifleUranium
      - MagazineBoxPistolShrapnel
      - MagazineBoxMagnumShrapnel
      - MagazineBoxLightRifleShrapnel
      - MagazineBoxRifleShrapnel
      - MagazineBoxCaselessRifleShrapnel
      - MagazineBoxCaselessRifleIncendiary
      - MagazineBoxCaselessRifleUranium
      - MagazineUniversalMagnumUranium
      - MagazineUniversalMagnumIncendiary
      - MagazineUniversalMagnumShrapnel
      - WeaponPistolUniversal
  - type: MaterialStorage
    whitelist:
      tags:
      - Sheet
      - RawMaterial
      - Ingot
      - Wooden
      - ClothMade
      - Gauze
      - Metal

- type: entity
  id: AmmoTechFab
  parent: [BaseLatheLube, BaseMaterialSiloUtilizer]
  name: "техфаб боєприпасів"
  description: "Друкує мінімально необхідну кількість набоїв, яка може знадобитися будь-якій бюджетній армії чи зброярні. Нічого особливого."
  components:
    - type: Sprite
      sprite: Structures/Machines/techfab.rsi
      layers:
        - state: icon
          map: ["enum.LatheVisualLayers.IsRunning"]
        - state: ammo
        - state: unlit
          shader: unshaded
          map: ["enum.PowerDeviceVisualLayers.Powered"]
        - state: inserting
          map: ["enum.MaterialStorageVisualLayers.Inserting"]
        - state: panel
          map: ["enum.WiresVisualLayers.MaintenancePanel"]
    - type: Machine
      board: AmmoTechFabCircuitboard
    - type: Lathe
      idleState: icon
      runningState: icon
      staticRecipes:
        - BoxLethalshot
        - BoxShotgunLumen
        - BoxShotgunFlare
        - BoxShotgunSlug
        - MagazineBoxLightRifle
        - MagazineBoxMagnum
        - MagazineBoxPistol
        - MagazineBoxRifle
        - MagazineLightRifle
        - MagazineLightRifleEmpty
        - MagazinePistol
        - MagazinePistolEmpty
        - MagazineRifle
        - MagazineRifleEmpty
        - MagazineNovaliteC1
        - MagazineNovaliteC1Empty
        - MagazineShotgun
        - MagazineShotgunEmpty
        - MagazineShotgunSlug
        - ShellTranquilizer
        - SpeedLoaderMagnum
        - SpeedLoaderMagnumEmpty
        - SpeedLoaderRifleHeavy
        - SpeedLoaderRifleHeavyEmpty
        - CartridgeSpecial
        - MagazineBoxSpecial
        # .35 Caseless Pistol
        - MagazinePistolCaselessRiflePractice # EE
        - MagazinePistolCaselessRifle # EE
        - MagazinePistolCaselessRifleRubber # EE
        # .35 Caseless (Civilian) Rifle
        - MagazineCaselessRifleShortPractice # EE
        - MagazineCaselessRifleShort # EE
        - MagazineCaselessRifleShortRubber # EE
        # .35 Caseless (Military) Rifle
        - MagazineCaselessRiflePractice # EE
        - MagazineCaselessRifle # EE
        - MagazineCaselessRifleRubber # EE
        # .35 Caseless (Box Mag) Rifle
        - MagazineBoxCaselessRiflePractice # EE
        - MagazineBoxCaselessRifle # EE
        - MagazineBoxCaselessRifleRubber # EE
        - MagazineLightRifleMarkOneEmpty
        - MagazineLightRifleMarkOne
    - type: MaterialStorage
      whitelist:
        tags:
          - Sheet
          - RawMaterial
          - Ingot
          - Wooden
          - ClothMade
          - Gauze
          - Metal

- type: entity
  id: MedicalTechFab
  parent: [BaseLatheLube, BaseMaterialSiloUtilizer]
  name: "медичний техфаб"
  description: "Друкує обладнання для використання медблоком."
  components:
  - type: Sprite
    sprite: Structures/Machines/techfab.rsi
    layers:
    - state: icon
      map: ["enum.LatheVisualLayers.IsRunning"]
    - state: med
    - state: unlit
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
    - state: inserting
      map: ["enum.MaterialStorageVisualLayers.Inserting"]
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: Lathe
    idleState: icon
    runningState: icon
    staticRecipes:
    - Brutepack
    - Ointment
    - Gauze
    - HandLabeler
    - Defibrillator
    - HandheldHealthAnalyzer
    - ClothingHandsGlovesLatex
    - ClothingHandsGlovesNitrile
    - ClothingMaskSterile
    - DiseaseSwab
    - Beaker
    - LargeBeaker
    - Dropper
    - Jug
    - Syringe
    - DisposableSyringe
    - Implanter
    - PillCanister
    - BodyBag
    - ChemistryEmptyBottle01
    - RollerBedSpawnFolded
    - CheapRollerBedSpawnFolded
    - EmergencyRollerBedSpawnFolded
    - Medkit
    - MedkitBurn
    - MedkitToxin
    - MedkitO2
    - MedkitBrute
    - MedkitAdvanced
    - MedkitRadiation
    - MedkitCombat
    - Scalpel
    - Retractor
    - Cautery
    - Drill
    - Saw
    - Hemostat
    - ClothingEyesGlassesChemical
    - SyringeGun #Goobstation
    - BoneGel # Shitmed Change
    - AACTablet # DeltaV
    - VehicleWheelchairFolded # Goobstation - Vehicles
    - SyringeCase # Morphine
    - ConicalFlask #PIRATE
    - LargeConicalFlask #PIRATE
    - CartridgeAtropine # Goobstation - ParamedHypo
    - CartridgeEpinephrine # Goobstation - ParamedHypo
    - CartridgeBicaridine # Goobstation - ParamedHypo
    - CartridgeDermaline # Goobstation - ParamedHypo
    - CartridgeSaline # Goobstation - ParamedHypo
    - ParamedHypo # Goobstation - ParamedHypo
    dynamicRecipes:
    - ChemicalPayload
    - CryostasisBeaker
    - BluespaceBeaker
    - SyringeBluespace
    - ClothingEyesHudMedical # Nyano
    - ChemicalPayload # Nyano
    - SyringeCryostasis
    - ClothingEyesNightVisionMedicalGoggles
    - EnvirosuitExtinguisherRefill
    # Shitmed Change
    - EnergyScalpel
    - EnergyCautery
    - AdvancedRetractor
    - OmnimedTool
    - MedicalCyberneticEyes
    - RapidSyringeGun # Goobstation
  - type: EmagLatheRecipes
    emagStaticRecipes:
    - CartridgeEphedrine
    - CartridgePuncturase
    - CartridgeTirizene
  - type: Machine
    board: MedicalTechFabCircuitboard
  - type: StealTarget
    stealGroup: MedicalTechFabCircuitboard

- type: entity
  parent: [BaseLathe, BaseMaterialSiloUtilizer]
  id: UniformPrinter
  name: "принтер уніформ"
  description: "Друкує нову або запасну уніформу."
  components:
  - type: Transform
    noRot: false
  - type: Sprite
    sprite: Structures/Machines/uniform_printer.rsi
    snapCardinals: false
    layers:
    - state: icon
      map: ["enum.LatheVisualLayers.IsRunning"]
  - type: Machine
    board: UniformPrinterMachineCircuitboard
  - type: Lathe
    producingSound: /Audio/Machines/uniformprinter.ogg
    idleState: icon
    runningState: building
    staticRecipes:
    - ClothingUniformJumpsuitColorGrey
    - ClothingUniformJumpskirtColorGrey
    - ClothingUniformJumpsuitBartender
    - ClothingUniformJumpskirtBartender
    - ClothingHeadHatCapcap
    - ClothingHeadHatCaptain
    - ClothingUniformJumpsuitCaptain
    - ClothingUniformJumpskirtCaptain
    - ClothingUniformJumpsuitCapFormal
    - ClothingUniformJumpskirtCapFormalDress
    - ClothingUniformJumpsuitCargo
    - ClothingUniformJumpskirtCargo
    - ClothingUniformJumpsuitSalvageSpecialist
    - ClothingHeadHatBeretEngineering
    - ClothingUniformJumpsuitChiefEngineer
    - ClothingUniformJumpskirtChiefEngineer
    - ClothingUniformJumpsuitChiefEngineerTurtle
    - ClothingUniformJumpskirtChiefEngineerTurtle
    - ClothingUniformJumpsuitChaplain
    - ClothingUniformJumpskirtChaplain
    - ClothingUniformJumpsuitChef
    - ClothingUniformJumpskirtChef
    - ClothingUniformJumpsuitChemistry
    - ClothingUniformJumpskirtChemistry
    - ClothingUniformJumpsuitClown
    - ClothingHeadHatBeretCmo
    - ClothingUniformJumpsuitCMO
    - ClothingUniformJumpskirtCMO
    - ClothingUniformJumpsuitCMOTurtle
    - ClothingUniformJumpskirtCMOTurtle
    - ClothingUniformJumpsuitDetective
    - ClothingUniformJumpskirtDetective
    - ClothingUniformJumpsuitEngineering
    - ClothingUniformJumpskirtEngineering
    - ClothingUniformJumpsuitSeniorEngineer
    - ClothingUniformJumpskirtSeniorEngineer
    - ClothingHeadHatHopcap
    - ClothingUniformJumpsuitHoP
    - ClothingUniformJumpskirtHoP
    - ClothingHeadHatBeretHoS
    - ClothingHeadHatHoshat
    - ClothingUniformJumpsuitHoS
    - ClothingUniformJumpskirtHoS
    - ClothingUniformJumpsuitHoSBlue # DeltaV - alternate sec uniforms
    - ClothingUniformJumpskirtHoSBlue # DeltaV - alternate sec uniforms
    - ClothingUniformJumpsuitHoSGrey # DeltaV - alternate sec uniforms
    - ClothingUniformJumpskirtHoSGrey # DeltaV - alternate sec uniforms
    - ClothingUniformJumpsuitHosFormal
    - ClothingUniformJumpskirtHosFormal
    - ClothingUniformJumpsuitHoSAlt
    - ClothingUniformJumpskirtHoSAlt
    - ClothingUniformJumpsuitHoSBlue
    - ClothingUniformJumpsuitHoSGrey
    - ClothingUniformJumpsuitHoSParadeMale
    - ClothingUniformJumpskirtHoSParadeMale
    - ClothingUniformJumpsuitHydroponics
    - ClothingUniformJumpskirtHydroponics
    - ClothingUniformJumpsuitJanitor
    - ClothingUniformJumpskirtJanitor
    - ClothingUniformJumpsuitLawyerBlack
    - ClothingUniformJumpsuitLibrarian
    - ClothingUniformJumpskirtColorLightBrown
    - ClothingUniformCourier # DeltaV - Courier Uniform
    - ClothingUniformSkirtCourier # DeltaV - Courier Uniform
    - ClothingUniformJumpsuitMantis # Nyanotrasen - Forensic Mantis
    - ClothingUniformSkirtMantis # Nyanotrasen - Forensic Mantis
    - ClothingHeadHatBeretSeniorPhysician
    - ClothingUniformJumpsuitMedicalDoctor
    - ClothingUniformJumpskirtMedicalDoctor
    - ClothingUniformJumpsuitSeniorPhysician
    - ClothingUniformJumpskirtSeniorPhysician
    - ClothingUniformJumpsuitMime
    - ClothingUniformJumpskirtMime
    - ClothingUniformJumpsuitMusician
    - ClothingUniformJumpsuitParamedic
    - ClothingUniformJumpskirtParamedic
    - ClothingUniformJumpsuitSeniorOfficer
    - ClothingUniformJumpskirtSeniorOfficer
    - ClothingUniformJumpsuitPrisoner
    - ClothingUniformJumpskirtPrisoner
    - ClothingHeadHatQMsoft
    - ClothingHeadHatBeretQM
    - ClothingUniformJumpsuitQM
    - ClothingUniformJumpskirtQM
    - ClothingUniformJumpsuitQMTurtleneck
    - ClothingUniformJumpskirtQMTurtleneck
    - ClothingUniformJumpsuitQMFormal
    - ClothingHeadHatBeretRND
    - ClothingUniformJumpsuitResearchDirector
    - ClothingUniformJumpskirtResearchDirector
    - ClothingUniformJumpsuitScientist
    - ClothingUniformJumpskirtScientist
    - ClothingUniformJumpsuitSeniorResearcher
    - ClothingUniformJumpskirtSeniorResearcher
    - ClothingHeadHatBeretSecurity
    - ClothingUniformJumpsuitSec
    - ClothingUniformJumpskirtSec
    - ClothingUniformJumpsuitSecBlue # DeltaV - alternate sec uniforms
    - ClothingUniformJumpskirtSecBlue # DeltaV - alternate sec uniforms
    - ClothingUniformJumpsuitSecGrey # DeltaV - alternate sec uniforms
    - ClothingUniformJumpskirtSecGrey # DeltaV - alternate sec uniforms
    - ClothingHeadHatBeretBrigmedic
    - ClothingUniformJumpsuitBrigmedic
    - ClothingUniformJumpskirtBrigmedic
    - ClothingHeadHatBeretWarden
    - ClothingHeadHatWarden
    - ClothingUniformJumpsuitWarden
    - ClothingUniformJumpskirtWarden
    - ClothingUniformJumpsuitWardenBlue # DeltaV - alternate sec uniforms
    - ClothingUniformJumpskirtWardenBlue # DeltaV - alternate sec uniforms
    - ClothingUniformJumpsuitWardenGrey # DeltaV - alternate sec uniforms
    - ClothingUniformJumpskirtWardenGrey # DeltaV - alternate sec uniforms
    - ClothingHeadHatParamedicsoft
    # Winter outfits
    - ClothingOuterWinterCap
    - ClothingOuterWinterCE
    - ClothingOuterWinterCMO
    - ClothingOuterWinterHoP
    - ClothingOuterWinterHoSUnarmored
    - ClothingOuterWinterWardenUnarmored
    - ClothingOuterWinterQM
    - ClothingOuterWinterRD
    - ClothingNeckMantleCap
    - ClothingNeckMantleCE
    - ClothingNeckMantleCMO
    - ClothingNeckMantleHOP
    - ClothingNeckMantleHOS
    - ClothingNeckMantleRD
    - ClothingNeckMantleQM
    - ClothingOuterStasecSweater # DeltaV - added stasec sweater to uniform printer.
    - ClothingOuterWinterMusician
    - ClothingOuterWinterClown
    - ClothingOuterWinterMime
    - ClothingOuterWinterCoat
    - ClothingOuterWinterJani
    - ClothingOuterWinterBar
    - ClothingOuterWinterChef
    - ClothingOuterWinterHydro
    - ClothingOuterWinterAtmos
    - ClothingOuterWinterEngi
    - ClothingOuterWinterCargo
    - ClothingOuterWinterMiner
    - ClothingOuterWinterMed
    - ClothingOuterWinterPara
    - ClothingOuterWinterChem
    - ClothingOuterWinterGen
    - ClothingOuterWinterViro
    - ClothingOuterWinterSci
    - ClothingOuterWinterRobo
    - ClothingOuterWinterSec
    # Ties
    - ClothingNeckTieRed
    - ClothingNeckTieDet
    - ClothingNeckTieSci
    # Scarfs - All scarfs avaible in winterdrobe
    - ClothingNeckScarfStripedGreen
    - ClothingNeckScarfStripedBlue
    - ClothingNeckScarfStripedRed
    - ClothingNeckScarfStripedBrown
    - ClothingNeckScarfStripedLightBlue
    - ClothingNeckScarfStripedOrange
    - ClothingNeckScarfStripedBlack
    - ClothingNeckScarfStripedPurple
    # Carpets
    - Carpet
    - CarpetBlack
    - CarpetPink
    - CarpetBlue
    - CarpetGreen
    - CarpetOrange
    - CarpetPurple
    - CarpetCyan
    - CarpetWhite
  - type: EmagLatheRecipes
    emagStaticRecipes:
    - ClothingHeadHatCentcomcap
    - ClothingHeadHatCentcom
    - ClothingUniformJumpsuitCentcomAgent
    - ClothingUniformJumpsuitCentcomFormal
    - ClothingUniformJumpskirtCentcomFormalDress
    - ClothingUniformJumpsuitCentcomOfficer
    - ClothingUniformJumpsuitCentcomOfficial
    - ClothingHeadHatSyndieMAA
    - ClothingHeadHatSyndie
    - ClothingUniformJumpsuitOperative
    - ClothingUniformJumpskirtOperative
    - ClothingUniformJumpsuitSyndieFormal
    - ClothingUniformJumpskirtSyndieFormalDress
    - ClothingHeadPyjamaSyndicateBlack
    - ClothingUniformJumpsuitPyjamaSyndicateBlack
    - ClothingHeadPyjamaSyndicatePink
    - ClothingUniformJumpsuitPyjamaSyndicatePink
    - ClothingHeadPyjamaSyndicateRed
    - ClothingUniformJumpsuitPyjamaSyndicateRed
    - ClothingOuterWinterCentcom
    - ClothingOuterWinterSyndie
    - ClothingOuterWinterSyndieCap
  - type: MaterialStorage
    whitelist:
      tags:
      - Sheet
      - RawMaterial
      - Ingot
      - Wooden
      - ClothMade
      - Gauze
      - Metal

- type: entity
  parent: [BaseLathe, BaseMaterialSiloUtilizer]
  id: OreProcessor
  name: "переробник руди"
  description: "Виробляє листи та злитки, використовуючи руди."
  components:
  - type: Sprite
    sprite: Structures/Machines/ore_processor.rsi
    layers:
    - state: icon
      map: ["enum.LatheVisualLayers.IsRunning"]
    - state: unlit
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
    - state: inserting
      map: ["enum.MaterialStorageVisualLayers.Inserting"]
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: Machine
    board: OreProcessorMachineCircuitboard
  - type: MaterialStorage
    ignoreColor: true
    whitelist:
      tags:
      - Ore
  - type: Lathe
    idleState: icon
    runningState: building
    defaultProductionAmount: 30 #Imp QOL
    staticRecipes:
    - BluespaceCrystal
    - NormalityCrystal
    - SheetSteel
    - SheetGlass1
    - SheetRGlass
    - SheetPlasma1
    - SheetPGlass1
    - SheetRPGlass1
    - SheetUranium1
    - IngotGold1
    - IngotSilver1
    - MaterialBananium1
    - N14IngotLead1
    - IngotCopper1
    - N14IngotAluminium1
    - IngotTungsten1
    # Additional recipes
    - SheetPlasteel1
    - SheetUGlass1
    - SheetRUGlass1
    - MaterialDiamond
  - type: MaterialStorageMagnetPickup # Delta V - Summary: Adds magnet pull from Frontier
    magnetEnabled: True
    range: 0.30 # Delta V - End Magnet Pull
  - type: MiningPoints # DeltaV - Source of mining points for miners
    transferSound:
      path: /Audio/Effects/Cargo/ping.ogg
  - type: MiningPointsLathe # DeltaV

- type: entity
  parent: OreProcessor
  id: OreProcessorIndustrial
  name: "промисловий переробник руди"
  description: "Переробник руди, спеціально розроблений для масового виробництва металів у промислових цілях."
  components:
  - type: Sprite
    sprite: Structures/Machines/ore_processor_industrial.rsi
  - type: Machine
    board: OreProcessorIndustrialMachineCircuitboard
  - type: Lathe
    materialUseMultiplier: 0.75
    timeMultiplier: 0.5

- type: entity
  parent: [BaseLathe, BaseMaterialSiloUtilizer]
  id: Sheetifier
  name: "листороб-2000"
  description: "Та ще листова машина."
  components:
  - type: Sprite
    sprite: Structures/Machines/sheetifier.rsi
    layers:
    - state: base_machine
      map: ["enum.LatheVisualLayers.IsRunning"]
    - state: buttons_on
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
  - type: Machine
    board: SheetifierMachineCircuitboard
  - type: MaterialStorage
    dropOnDeconstruct: false #should drop ores instead of ingots/sheets
    ignoreColor: true
    canEjectStoredMaterials: false
    whitelist:
      tags:
      - Raw
      - Wooden
  - type: Lathe
    idleState: base_machine
    runningState: base_machine_processing
    staticRecipes:
    - MaterialSheetMeat
    - SheetPaper

- type: entity
  parent: [BaseLathe, BaseMaterialSiloUtilizer]
  id: PrizeCounter
  name: "прилавок з призами"
  description: "Заберіть свій приз та виграйте іграшки та милі плюшеві іграшки!"
  components:
  - type: AmbientOnPowered
  - type: AmbientSound
    volume: -9
    range: 3
    enabled: false
    sound:
      path: /Audio/Ambience/Objects/vending_machine_hum.ogg
  - type: LitOnPowered
  - type: ApcPowerReceiver
    powerLoad: 200
  # - type: Advertise
  #   pack: CuddlyCritterAds # TODO Change this
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#4b93ad"
  - type: Transform
    noRot: false
  - type: Sprite
    sprite: Structures/Machines/prizecounter.rsi
    snapCardinals: true
    layers:
    - state: icon
      map: ["enum.LatheVisualLayers.IsRunning"]
    - state: unlit
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: Lathe
    idleState: icon
    runningState: icon
    staticRecipes:
    - PrizeBall
    - PlushieMothRandom
    - PlushieShadowkin
    - PlushieMothMusician
    - PlushieMothBartender
    - PlushieBee
    - PlushieHampter
    - PlushieRouny
    - PlushieLamp
    - PlushieArachind
    - PlushieLizard
    - PlushieSpaceLizard
    - PlushieSharkBlue
    - PlushieSharkPink
    - PlushieSharkGrey
    - PlushieCarp
    - PlushieMagicarp
    - PlushieHolocarp
    - PlushieSlime
    - PlushieSnake
    - ToyMouse
    - ToyRubberDuck
    - PlushieVox
    - PlushieAtmosian
    - PlushiePenguin
    - PlushieHuman
    - PlushieArachne
    - PlushieGnome
    - PlushieLoveable
    - PlushieDeer
    - PlushieIpc
    - PlushieGrey
    - PlushieRedFox
    - PlushiePurpleFox
    - PlushiePinkFox
    - PlushieOrangeFox
    - PlushieMarbleFox
    - PlushieCrimsonFox
    - PlushieCoffeeFox
    - PlushieBlueFox
    - PlushieBlackFox
    - PlushieVulp
    - PlushieCorgi
    - PlushieGirlyCorgi
    - PlushieRobotCorgi
    - PlushieCatBlack
    - PlushieCatGrey
    - PlushieCatOrange
    - PlushieCatSiames
    - PlushieCatTabby
    - PlushieCatTuxedo
    - PlushieCatWhite
    - ToyAi
    - ToyIan
    - BalloonNT
    - BalloonCorgi
    - CrayonBox
    - PetRockCarrier
    - PlushieXeno
    - FoamCrossbow
    - RevolverCapGun
    - PonderingOrb
    - ToyAmongPequeno
    - FoamCutlass
    - WhoopieCushion
    - ToyHammer
    - PlasticBanana
    - WeaponWaterPistol
    - WeaponWaterBlaster
    - NewtonCradle
    - SnapPopBox
    - MrDips
    - MrChips
    - CrazyGlue
    - PlushieRatvar
    - PlushieNar
    - NFPlushieBotanist
    - NFPlushieCmo
    - NFPlushieConstruction
    - NFPlushieEngineer
    # imp edit start
    - PlushieGray
    - PlushieUrsaMinor
    - PlushieBug
    - PlushieEvilBug
    - PlushieSnail
    - PlushieGoblin
    - PlushieGollylad
    - PlushieImp
    - PlushieCaptain
    - PlushieGiantBear
    # imp edit end
  - type: EmagLatheRecipes
    emagStaticRecipes:
    - PlushieGhost
    - PlushieRGBee
    - PlushieRainbowCarp
    - PlushieJester
    - PlushieSlips
    - PlushieTrystan
    - PlushieAbductor
    - PlushieAbductorAgent
    - PlushieNuke
    - ToyNuke
    - FoamBlade
    - BalloonSyn
    - SingularityToy
    - TeslaToy
    - ToySword
    - BwoinkHammer
    - ThronglerToy
  - type: MaterialStorage
    whitelist:
      tags:
        - PrizeTicket
