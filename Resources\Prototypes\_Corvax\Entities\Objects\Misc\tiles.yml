- type: entity
  name: tiles-wood-parquet
  parent: FloorTileItemBase
  id: FloorTileItemWoodParquet
  components:
  - type: Sprite
    sprite: _Corvax/Objects/Tiles/wood.rsi
    state: wood_parquet
  - type: Item
    sprite: _Corvax/Objects/Tiles/wood.rsi
    heldPrefix: wood
  - type: FloorTile
    outputs:
      - Plating
      - FloorWoodParquet
  - type: Stack
    stackType: FloorTileItemWoodParquet

- type: entity
  name: tiles-wood-black
  parent: FloorTileItemBase
  id: FloorTileItemWoodBlack
  components:
  - type: Sprite
    sprite: _Corvax/Objects/Tiles/wood.rsi
    state: wood_black
  - type: Item
    sprite: _Corvax/Objects/Tiles/wood.rsi
    heldPrefix: wood-black
  - type: FloorTile
    outputs:
      - Plating
      - FloorWoodBlack
  - type: Stack
    stackType: FloorTileItemWoodBlack

- type: entity
  name: tiles-wood-dark
  parent: FloorTileItemBase
  id: FloorTileItemWoodDark
  components:
  - type: Sprite
    sprite: _Corvax/Objects/Tiles/wood.rsi
    state: wood_dark
  - type: Item
    sprite: _Corvax/Objects/Tiles/wood.rsi
    heldPrefix: wood-dark
  - type: FloorTile
    outputs:
      - Plating
      - FloorWoodDark
  - type: Stack
    stackType: FloorTileItemWoodDark

- type: entity
  name: tiles-wood-light
  parent: FloorTileItemBase
  id: FloorTileItemWoodLight
  components:
  - type: Sprite
    sprite: _Corvax/Objects/Tiles/wood.rsi
    state: wood_light
  - type: Item
    sprite: _Corvax/Objects/Tiles/wood.rsi
    heldPrefix: wood-light
  - type: FloorTile
    outputs:
      - Plating
      - FloorWoodLight
  - type: Stack
    stackType: FloorTileItemWoodLight

- type: entity
  name: tiles-wood-red
  parent: FloorTileItemBase
  id: FloorTileItemWoodRed
  components:
  - type: Sprite
    sprite: _Corvax/Objects/Tiles/wood.rsi
    state: wood_red
  - type: Item
    sprite: _Corvax/Objects/Tiles/wood.rsi
    heldPrefix: wood-red
  - type: FloorTile
    outputs:
      - Plating
      - FloorWoodRed
  - type: Stack
    stackType: FloorTileItemWoodRed

- type: entity
  name: "велика чорна дерев'яна підлога"
  parent: FloorTileItemBase
  id: FloorTileItemWoodLargeBlack
  components:
  - type: Sprite
    sprite: _Corvax/Objects/Tiles/wood.rsi
    state: wood_large_black
  - type: Item
    sprite: _Corvax/Objects/Tiles/wood.rsi
    heldPrefix: wood-black
  - type: FloorTile
    outputs:
      - Plating
      - FloorWoodLargeBlack
  - type: Stack
    stackType: FloorTileItemWoodLargeBlack

- type: entity
  name: "велика темна дерев'яна підлога"
  parent: FloorTileItemBase
  id: FloorTileItemWoodLargeDark
  components:
  - type: Sprite
    sprite: _Corvax/Objects/Tiles/wood.rsi
    state: wood_large_dark
  - type: Item
    sprite: _Corvax/Objects/Tiles/wood.rsi
    heldPrefix: wood-dark
  - type: FloorTile
    outputs:
      - Plating
      - FloorWoodLargeDark
  - type: Stack
    stackType: FloorTileItemWoodLargeDark

- type: entity
  name: "велика світла дерев'яна підлога"
  parent: FloorTileItemBase
  id: FloorTileItemWoodLargeLight
  components:
  - type: Sprite
    sprite: _Corvax/Objects/Tiles/wood.rsi
    state: wood_large_light
  - type: Item
    sprite: _Corvax/Objects/Tiles/wood.rsi
    heldPrefix: wood-light
  - type: FloorTile
    outputs:
      - Plating
      - FloorWoodLargeLight
  - type: Stack
    stackType: FloorTileItemWoodLargeLight

- type: entity
  name: "велика червона дерев'яна підлога"
  parent: FloorTileItemBase
  id: FloorTileItemWoodLargeRed
  components:
  - type: Sprite
    sprite: _Corvax/Objects/Tiles/wood.rsi
    state: wood_large_red
  - type: Item
    sprite: _Corvax/Objects/Tiles/wood.rsi
    heldPrefix: wood-red
  - type: FloorTile
    outputs:
      - Plating
      - FloorWoodLargeRed
  - type: Stack
    stackType: FloorTileItemWoodLargeRed

- type: entity
  name: "паркетна чорна дерев'яна підлога"
  parent: FloorTileItemBase
  id: FloorTileItemWoodParquetBlack
  components:
  - type: Sprite
    sprite: _Corvax/Objects/Tiles/wood.rsi
    state: wood_parquet_black
  - type: Item
    sprite: _Corvax/Objects/Tiles/wood.rsi
    heldPrefix: wood-black
  - type: FloorTile
    outputs:
      - Plating
      - FloorWoodParquetBlack
  - type: Stack
    stackType: FloorTileItemWoodParquetBlack

- type: entity
  name: "паркетна темна дерев'яна підлога"
  parent: FloorTileItemBase
  id: FloorTileItemWoodParquetDark
  components:
  - type: Sprite
    sprite: _Corvax/Objects/Tiles/wood.rsi
    state: wood_parquet_dark
  - type: Item
    sprite: _Corvax/Objects/Tiles/wood.rsi
    heldPrefix: wood-dark
  - type: FloorTile
    outputs:
      - Plating
      - FloorWoodParquetDark
  - type: Stack
    stackType: FloorTileItemWoodParquetDark

- type: entity
  name: "паркетна світла дерев'яна підлога"
  parent: FloorTileItemBase
  id: FloorTileItemWoodParquetLight
  components:
  - type: Sprite
    sprite: _Corvax/Objects/Tiles/wood.rsi
    state: wood_parquet_light
  - type: Item
    sprite: _Corvax/Objects/Tiles/wood.rsi
    heldPrefix: wood-light
  - type: FloorTile
    outputs:
      - Plating
      - FloorWoodParquetLight
  - type: Stack
    stackType: FloorTileItemWoodParquetLight

- type: entity
  name: "паркетна червона дерев'яна підлога"
  parent: FloorTileItemBase
  id: FloorTileItemWoodParquetRed
  components:
  - type: Sprite
    sprite: _Corvax/Objects/Tiles/wood.rsi
    state: wood_parquet_red
  - type: Item
    sprite: _Corvax/Objects/Tiles/wood.rsi
    heldPrefix: wood-red
  - type: FloorTile
    outputs:
      - Plating
      - FloorWoodParquetRed
  - type: Stack
    stackType: FloorTileItemWoodParquetRed

- type: entity
  name: "шахова дерев'яна підлога"
  parent: FloorTileItemBase
  id: FloorTileItemWoodChess
  components:
  - type: Sprite
    sprite: _Corvax/Objects/Tiles/wood.rsi
    state: wood_chess
  - type: Item
    sprite: _Corvax/Objects/Tiles/wood.rsi
    heldPrefix: wood
  - type: FloorTile
    outputs:
      - Plating
      - FloorWoodChess
  - type: Stack
    stackType: FloorTileItemWoodChess

- type: entity
  name: "шахова чорна дерев'яна підлога"
  parent: FloorTileItemBase
  id: FloorTileItemWoodChessBlack
  components:
  - type: Sprite
    sprite: _Corvax/Objects/Tiles/wood.rsi
    state: wood_chess_black
  - type: Item
    sprite: _Corvax/Objects/Tiles/wood.rsi
    heldPrefix: wood-black
  - type: FloorTile
    outputs:
      - Plating
      - FloorWoodChessBlack
  - type: Stack
    stackType: FloorTileItemWoodChessBlack

- type: entity
  name: "шахова темна дерев'яна підлога"
  parent: FloorTileItemBase
  id: FloorTileItemWoodChessDark
  components:
  - type: Sprite
    sprite: _Corvax/Objects/Tiles/wood.rsi
    state: wood_chess_dark
  - type: Item
    sprite: _Corvax/Objects/Tiles/wood.rsi
    heldPrefix: wood-dark
  - type: FloorTile
    outputs:
      - Plating
      - FloorWoodChessDark
  - type: Stack
    stackType: FloorTileItemWoodChessDark

- type: entity
  name: "шахова світла дерев'яна підлога"
  parent: FloorTileItemBase
  id: FloorTileItemWoodChessLight
  components:
  - type: Sprite
    sprite: _Corvax/Objects/Tiles/wood.rsi
    state: wood_chess_light
  - type: Item
    sprite: _Corvax/Objects/Tiles/wood.rsi
    heldPrefix: wood-light
  - type: FloorTile
    outputs:
      - Plating
      - FloorWoodChessLight
  - type: Stack
    stackType: FloorTileItemWoodChessLight

- type: entity
  name: "шахова червона дерев'яна підлога"
  parent: FloorTileItemBase
  id: FloorTileItemWoodChessRed
  components:
  - type: Sprite
    sprite: _Corvax/Objects/Tiles/wood.rsi
    state: wood_chess_red
  - type: Item
    sprite: _Corvax/Objects/Tiles/wood.rsi
    heldPrefix: wood-red
  - type: FloorTile
    outputs:
      - Plating
      - FloorWoodChessRed
  - type: Stack
    stackType: FloorTileItemWoodChessRed
