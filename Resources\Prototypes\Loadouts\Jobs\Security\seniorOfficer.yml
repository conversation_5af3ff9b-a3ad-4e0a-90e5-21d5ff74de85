# Senior Officer
# Backpacks

# Belt
- type: loadout
  id: LoadoutClothingBeltJudo
  category: JobsSecuritySeniorOfficer
  cost: 2
  exclusive: true
  requirements:
  - !type:CharacterItemGroupRequirement
    group: LoadoutSecurityBelt
  - !type:CharacterJobRequirement
    jobs:
    - SeniorOfficer
  items:
  - ClothingBeltCorporateJudoFilled
# Ears

# Equipment
- type: loadout
  id: LoadoutSecurityDisablerSMG
  category: JobsSecuritySeniorOfficer
  cost: 2
  canBeHeirloom: true
  guideEntry: SecurityWeapons
  requirements:
  - !type:CharacterItemGroupRequirement
    group: LoadoutSecurityWeapons
  - !type:CharacterJobRequirement
    jobs:
      - SeniorOfficer
      - HeadOfSecurity
      - BlueshieldOfficer
  items:
  - WeaponDisablerSMG

- type: loadout
  id: LoadoutSecurityPistolUniversal
  category: JobsSecuritySeniorOfficer
  cost: 6
  canBeHeirloom: true
  guideEntry: SecurityWeapons
  requirements:
  - !type:CharacterItemGroupRequirement
    group: LoadoutSecurityWeapons
  - !type:CharacterJobRequirement
    jobs:
    - SeniorOfficer
    - HeadOfSecurity
    - BlueshieldOfficer
  - !type:CharacterAgeRequirement
    min: 21
  items:
  - WeaponPistolUniversalSecurity

- type: loadout
  id: LoadoutSecurityPistolUniversalNonLethal
  category: JobsSecuritySeniorOfficer
  cost: 6
  canBeHeirloom: true
  guideEntry: SecurityWeapons
  requirements:
  - !type:CharacterItemGroupRequirement
    group: LoadoutSecurityWeapons
  - !type:CharacterJobRequirement
    jobs:
    - SeniorOfficer
    - HeadOfSecurity
    - BlueshieldOfficer
  - !type:CharacterAgeRequirement
    min: 21
  items:
  - WeaponPistolUniversalSecurityNonLethal

- type: loadout
  id: LoadoutMagazineUniversalMagnum
  category: JobsSecuritySeniorOfficer
  cost: 4
  canBeHeirloom: true
  guideEntry: SecurityWeapons
  requirements:
  - !type:CharacterItemGroupRequirement
    group: LoadoutSecurityEquipment
  - !type:CharacterJobRequirement
    jobs:
    - SeniorOfficer
    - HeadOfSecurity
    - BlueshieldOfficer
  - !type:CharacterAgeRequirement
    min: 21
  items:
  - MagazineUniversalMagnum

- type: loadout
  id: LoadoutMagazineUniversalMagnumSpare
  category: JobsSecuritySeniorOfficer
  cost: 4
  canBeHeirloom: true
  guideEntry: SecurityWeapons
  requirements:
  - !type:CharacterItemGroupRequirement
    group: LoadoutSecurityEquipment
  - !type:CharacterJobRequirement
    jobs:
    - SeniorOfficer
    - HeadOfSecurity
    - BlueshieldOfficer
  - !type:CharacterAgeRequirement
    min: 21
  items:
  - MagazineUniversalMagnum

- type: loadout
  id: LoadoutMagazineUniversalMagnumRubber
  category: JobsSecuritySeniorOfficer
  cost: 4
  canBeHeirloom: true
  guideEntry: SecurityWeapons
  requirements:
  - !type:CharacterItemGroupRequirement
    group: LoadoutSecurityEquipment
  - !type:CharacterJobRequirement
    jobs:
    - SeniorOfficer
    - HeadOfSecurity
    - BlueshieldOfficer
  - !type:CharacterAgeRequirement
    min: 21
  items:
  - MagazineUniversalMagnumRubber

- type: loadout
  id: LoadoutMagazineUniversalMagnumRubberSpare
  category: JobsSecuritySeniorOfficer
  cost: 4
  canBeHeirloom: true
  guideEntry: SecurityWeapons
  requirements:
  - !type:CharacterItemGroupRequirement
    group: LoadoutSecurityEquipment
  - !type:CharacterJobRequirement
    jobs:
    - SeniorOfficer
    - HeadOfSecurity
    - BlueshieldOfficer
  - !type:CharacterAgeRequirement
    min: 21
  items:
  - MagazineUniversalMagnumRubber

# Eyes

- type: loadout
  id: LoadoutClothingEyesPatchTintedSenior
  category: JobsSecuritySeniorOfficer
  cost: 0
  exclusive: true
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutSecurityEyes
    - !type:CharacterDepartmentRequirement
      departments:
        - Security
  items:
    - ClothingEyesEyepatchHudTintedSecurity

# Gloves

# Head

# Id

# Neck

# Mask

# Outer

# Shoes

# Uniforms
- type: loadout
  id: LoadoutSecurityUniformJumpskirtSenior
  category: JobsSecuritySeniorOfficer
  cost: 0
  exclusive: true
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutSeniorOfficerUniforms
    - !type:CharacterJobRequirement
      jobs:
        - SeniorOfficer
  items:
    - ClothingUniformJumpskirtSeniorOfficer

- type: loadout
  id: LoadoutSecurityUniformJumpsuitSenior
  category: JobsSecuritySeniorOfficer
  cost: 0
  exclusive: true
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutSeniorOfficerUniforms
    - !type:CharacterJobRequirement
      jobs:
        - SeniorOfficer
  items:
    - ClothingUniformJumpsuitSeniorOfficer

- type: loadout
  id: LoadoutUniformJumpsuitSecFormal
  category: JobsSecuritySeniorOfficer
  cost: 0
  exclusive: true
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutSeniorOfficerUniforms
    - !type:CharacterJobRequirement
      jobs:
        - SeniorOfficer
  items:
  - ClothingUniformJumpsuitSecFormal
