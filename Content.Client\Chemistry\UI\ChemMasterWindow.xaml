<controls:FancyWindow xmlns="https://spacestation14.io"
                xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
                MinSize="800 00"
                Title="{Loc 'chem-master-bound-user-interface-title'}">
    <BoxContainer Orientation="Horizontal" VerticalExpand="true" MinSize="0 200">
        <TabContainer Name="Tabs" Margin="5 5 7 5" HorizontalExpand="True">
            <BoxContainer Orientation="Vertical" HorizontalExpand="True" Margin="5" SeparationOverride="10">
                <!-- Buffer -->
                <BoxContainer Orientation="Horizontal">
                    <Label Text="{Loc 'chem-master-window-buffer-text'}" />
                    <Control HorizontalExpand="True" />
                    <Button MinSize="80 0" Name="BufferTransferButton" Access="Public" Text="{Loc 'chem-master-window-transfer-button'}" ToggleMode="True" StyleClasses="OpenRight" />
                    <OptionButton Name="SortMethod" Access="Public" ToolTip="{Loc 'chem-master-window-sort-method-tooltip'}" StyleClasses="OpenBoth" />
                    <Button MinSize="80 0" Name="BufferDiscardButton" Access="Public" Text="{Loc 'chem-master-window-discard-button'}" ToggleMode="True" StyleClasses="OpenLeft" />
                </BoxContainer>

                <!-- Buffer info -->
                <PanelContainer VerticalExpand="True" HorizontalExpand="True" MinSize="0 300">
                    <PanelContainer.PanelOverride>
                        <gfx:StyleBoxFlat BackgroundColor="#1B1B1E" />
                    </PanelContainer.PanelOverride>

                    <ScrollContainer HorizontalExpand="True" MinSize="0 300">
                        <!-- Buffer reagent list -->
                        <BoxContainer Name="BufferInfo" Orientation="Vertical" Margin="4" HorizontalExpand="True">
                            <Label Text="{Loc 'chem-master-window-buffer-empty-text'}" />
                        </BoxContainer>
                    </ScrollContainer>
                </PanelContainer>
            </BoxContainer>

            <BoxContainer Orientation="Vertical" HorizontalExpand="True" Margin="5" SeparationOverride="10">
                <!-- Pill Buffer -->
                <BoxContainer Orientation="Horizontal">
                    <Label Text="{Loc 'chem-master-window-pill-buffer-text'}" />
                    <Control HorizontalExpand="True" />
                    <Button MinSize="80 0" Name="PillBufferTransferButton" Access="Public" Text="{Loc 'chem-master-window-transfer-button'}" ToggleMode="True" StyleClasses="OpenRight" />
                    <OptionButton Name="PillSortMethod" Access="Public" ToolTip="{Loc 'chem-master-window-sort-method-tooltip'}" StyleClasses="OpenBoth" />
                    <Button MinSize="80 0" Name="PillBufferDiscardButton" Access="Public" Text="{Loc 'chem-master-window-discard-button'}" ToggleMode="True" StyleClasses="OpenLeft" />
                </BoxContainer>

                <!-- Pill Buffer info -->
                <PanelContainer VerticalExpand="True" HorizontalExpand="True" MinSize="0 300">
                    <PanelContainer.PanelOverride>
                        <gfx:StyleBoxFlat BackgroundColor="#1B1B1E" />
                    </PanelContainer.PanelOverride>

                    <ScrollContainer HorizontalExpand="True" MinSize="0 300">
                        <!-- Pill Buffer reagent list -->
                        <BoxContainer Name="PillBufferInfo" Orientation="Vertical" Margin="4" HorizontalExpand="True">
                            <Label Text="{Loc 'chem-master-window-pill-buffer-empty-text'}" />
                        </BoxContainer>
                    </ScrollContainer>
                </PanelContainer>

                <!-- Padding -->
                <Control MinSize="0 10" />

                <!-- Packaging -->
                <BoxContainer Orientation="Horizontal">
                    <Label Text="{Loc 'chem-master-window-packaging-text'}" />
                    <Control HorizontalExpand="True"/>
                    <Label Text="{Loc 'chem-master-window-buffer-label'}" />
                    <Label Name="BufferCurrentVolume" StyleClasses="LabelSecondaryColor" />
                </BoxContainer>

                <!-- Wrap the packaging info-->
                <PanelContainer>
                    <PanelContainer.PanelOverride>
                        <gfx:StyleBoxFlat BackgroundColor="#1B1B1E" />
                    </PanelContainer.PanelOverride>

                    <!-- Packaging Info -->
                    <BoxContainer Orientation="Vertical" Margin="4" HorizontalExpand="True" VerticalExpand="True" SeparationOverride="5">
                        <!-- Label for output -->
                        <BoxContainer Orientation="Horizontal">
                            <Label Text="{Loc 'chem-master-current-text-label'}" />
                            <Control HorizontalExpand="True" MinSize="50 0"/>
                            <LineEdit Name="LabelLineEdit" SetWidth="455"/>
                        </BoxContainer>

                        <!-- Pills Type Buttons -->
                        <BoxContainer Orientation="Horizontal">
                            <Label Text="{Loc 'chem-master-window-pill-type-label'}"/>
                            <Control HorizontalExpand="True" MinSize="50 0"/>
                            <GridContainer Name="Grid" Columns="10">
                                <!-- Pills type buttons are generated in the code -->
                            </GridContainer>
                        </BoxContainer>

                        <BoxContainer Orientation="Horizontal">
                            <Label Text="{Loc 'chem-master-window-pills-label'}" />
                            <Control HorizontalExpand="True" MinSize="50 0" />
                            <Label Text="{Loc 'chem-master-window-pills-number-label'}" Margin="5 0 0 0" StyleClasses="LabelSecondaryColor" />
                            <SpinBox MinSize="100 0" Name="PillNumber" Access="Public" Value="0" />
                            <Label Text="{Loc 'chem-master-window-dose-label'}" Margin="5 0 0 0" StyleClasses="LabelSecondaryColor" />
                            <SpinBox MinSize="100 0" Name="PillDosage" Access="Public" Value="1" />
                            <Button MinSize="80 0" Name="CreatePillButton" Access="Public" Text="{Loc 'chem-master-window-create-button'}" />
                        </BoxContainer>

                        <BoxContainer Orientation="Horizontal">
                            <Label Text="{Loc 'chem-master-window-bottles-label'}" />
                            <Control HorizontalExpand="True" MinSize="50 0" />
                            <Label Text="{Loc 'chem-master-window-dose-label'}" Margin="5 0 0 0" StyleClasses="LabelSecondaryColor" />
                            <SpinBox MinSize="100 0" Name="BottleDosage" Access="Public" Value="0" />
                            <Button MinSize="80 0" Name="CreateBottleButton" Access="Public" Text="{Loc 'chem-master-window-create-button'}" />
                        </BoxContainer>
                    </BoxContainer>
                </PanelContainer>
            </BoxContainer>
        </TabContainer>
        <BoxContainer Orientation="Vertical" Margin="5 5 7 5" MinSize="300 0">
            <!-- Input container info -->
            <BoxContainer Orientation="Horizontal">
                <Label Text="{Loc 'chem-master-window-container-label'}" />
                <Control HorizontalExpand="True" />
                <Button MinSize="80 0" Name="InputEjectButton" Access="Public" Text="{Loc 'chem-master-window-eject-button'}" StyleClasses="OpenLeft" />
            </BoxContainer>
            <!-- Padding -->
            <Control MinSize="0 5" />

            <PanelContainer VerticalExpand="True" HorizontalExpand="True" MinSize="0 300">
                <PanelContainer.PanelOverride>
                    <gfx:StyleBoxFlat BackgroundColor="#1B1B1E" />
                </PanelContainer.PanelOverride>

                <BoxContainer>
                    <ScrollContainer HorizontalExpand="True" MinSize="0 20">
                        <!-- Initially empty, when server sends state data this will have container contents and fill volume.-->
                        <BoxContainer Name="ContainerInfoContainer" Orientation="Vertical" Margin="4" HorizontalExpand="True">
                            <Label Text="{Loc 'chem-master-window-no-container-loaded-text'}" />
                        </BoxContainer>
                    </ScrollContainer>
                </BoxContainer>
            </PanelContainer>
            <RichTextLabel Name="AmountLabel" Text="{Loc 'chem-master-window-transferring-default-label'}"  Margin="0 0 7 0" />
            <LineEdit MinSize="140 0" Name="AmountLineEdit" Access="Public" PlaceHolder="{Loc 'chem-master-window-amount-placeholder'}" />
            <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
                <Button Name="SetAmountButton" Text="{Loc 'chem-master-window-set-amount-label'}" HorizontalExpand="True" StyleClasses="OpenRight"/>
                <Button Name="SaveAsFrequentButton" Text="{Loc 'chem-master-window-save-as-frequent-label'}" HorizontalExpand="True" StyleClasses="OpenLeft"/>
            </BoxContainer>
            <RichTextLabel Text="Both of the above buttons use the textbox" />
            <Control MinSize="0 5" />
            <GridContainer
                Name="AmountButtons"
                VerticalExpand="True"
                HorizontalExpand="True"
                Margin="2 2 2 2"
                Columns="4" />
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
