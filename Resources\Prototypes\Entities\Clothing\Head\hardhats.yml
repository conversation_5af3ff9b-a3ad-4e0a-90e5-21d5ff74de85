- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatHardhatBase
  abstract: true
  components:
  - type: Sprite
    layers:
    - state: icon
    - state: light-icon
      shader: unshaded
      visible: false
      map: [ "light" ]
  - type: Clothing
    slots: #PIRATE
    - HEAD #PIRATE
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
    equippedPrefix: off
  - type: PointLight
    enabled: false
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
    radius: 7
    netsync: false
  - type: Appearance
  - type: HandheldLight
    addPrefix: false
    blinkingBehaviourId: blinking
    radiatingBehaviourId: radiating
  - type: LightBehaviour
    behaviours:
      - !type:FadeBehaviour
        id: radiating
        interpolate: Linear
        maxDuration: 2.0
        startValue: 3.0
        endValue: 2.0
        isLooped: true
        reverseWhenFinished: true
      - !type:PulseBehaviour
        id: blinking
        interpolate: Nearest
        maxDuration: 1.0
        minValue: 0.1
        maxValue: 2.0
        isLooped: true
  - type: ToggleableLightVisuals
    spriteLayer: light
    inhandVisuals:
      left:
      - state: on-inhand-left
      right:
      - state: on-inhand-right
    clothingVisuals:
      head:
      - state: on-equipped-HELMET
  - type: PowerCellSlot
    cellSlotId: cell_slot
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellMedium
  - type: Item
    heldPrefix: off
  - type: ContainerContainer
    containers:
      cell_slot: !type:ContainerSlot
  - type: Tag
    tags:
    - WhitelistChameleon
    - HardHat

- type: entity
  parent: ClothingHeadHatHardhatBase
  id: ClothingHeadHatHardhatBlue
  name: "синя каска"
  description: "Каска, пофарбована в синій колір, використовується в небезпечних умовах праці для захисту голови. Поставляється з вбудованим ліхтариком."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hardhats/blue.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardhats/blue.rsi

- type: entity
  parent: ClothingHeadHatHardhatBase
  id: ClothingHeadHatHardhatOrange
  name: "помаранчева каска"
  description: "Каска, пофарбована в помаранчевий колір, використовується в небезпечних умовах праці для захисту голови. Поставляється з вбудованим ліхтариком."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hardhats/orange.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardhats/orange.rsi

- type: entity
  parent: ClothingHeadHatHardhatBase
  id: ClothingHeadHatHardhatRed
  name: "червона каска"
  description: "Каска, пофарбована в червоний колір, використовується в небезпечних умовах праці для захисту голови. Поставляється з вбудованим ліхтариком."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hardhats/red.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardhats/red.rsi

- type: entity
  parent: ClothingHeadHatHardhatBase
  id: ClothingHeadHatHardhatWhite
  name: "біла каска"
  description: "Каска, пофарбована в білий колір, використовується в небезпечних умовах праці для захисту голови. Поставляється з вбудованим ліхтариком."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hardhats/white.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardhats/white.rsi

- type: entity
  parent: ClothingHeadHatHardhatBase
  id: ClothingHeadHatHardhatYellow
  name: "жовта каска"
  description: "Каска, пофарбована в жовтий колір, використовується в небезпечних умовах праці для захисту голови. Поставляється з вбудованим ліхтариком."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hardhats/yellow.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardhats/yellow.rsi

- type: entity
  parent: ClothingHeadHatHardhatBase
  id: ClothingHeadHatHardhatYellowDark
  name: "темно-жовта каска"
  description: "Каска, пофарбована в темно-жовтий колір, використовується в небезпечних умовах праці для захисту голови. Поставляється з вбудованим ліхтариком."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hardhats/dark_yellow.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardhats/dark_yellow.rsi

- type: entity
  parent: ClothingHeadHatHardhatBase
  id: ClothingHeadHatHardhatArmored
  name: "броньована каска"
  description: "Броньована каска. Забезпечує найкраще з двох світів - захист та зручність - ідеально підходить для інженера на передовій."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hardhats/armored.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardhats/armored.rsi
  - type: Armor #Copied from the sec helmet, as it's hard to give these sane values without locational damage existing.
    modifiers:
      coefficients:
        Blunt: 0.8
        Slash: 0.8
        Piercing: 0.9
        Heat: 0.8
