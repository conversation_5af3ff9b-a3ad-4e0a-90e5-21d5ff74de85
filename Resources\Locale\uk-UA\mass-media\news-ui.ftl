news-read-ui-next-text = ▶
news-read-ui-prev-text = ◀
news-read-ui-next-tooltip = Далі
news-read-ui-prev-tooltip = Попередня
news-read-ui-default-title = Новини станції
news-read-ui-not-found-text = Не знайдено жодної статті
news-read-ui-time-prefix-text = Час публікації:
news-reader-ui-mute-tooltip = Вимкнути звук сповіщень
news-read-ui-notification-off = ̶♫̶
news-read-ui-notification-on = ♫
news-read-ui-no-author = Анонім
news-read-ui-author-prefix = Автор:
news-write-ui-default-title = Управління новинами
news-write-ui-articles-label = Статті:
news-write-ui-delete-text = Видалити
news-write-ui-publish-text = Опублікувати
news-write-ui-create-text = Створити
news-write-ui-cancel-text = Скасувати
news-write-ui-preview-text = Попередній перегляд
news-write-ui-article-count-0 = 0 Статей
news-write-ui-article-count-text = {$count} Статей
news-write-ui-footer-text = Авторська Система Новини#Менеджер™
news-write-ui-new-article = Нова Стаття
news-write-ui-article-name-label = Назва:
news-write-no-access-popup = Немає доступу
news-writer-text-length-exceeded = Текст перевищує максимальну довжину
news-write-ui-richtext-tooltip = Статті новин підтримують розширений текст
    Підтримуються наступні теги розгорнутого тексту:
    {"[color=Gray][bullet/]heading \\[size=1-3\\]"}
    {"[bullet/]bold"}
    {"[bullet/]italic"}
    {"[bullet/]bolditalic"}
    {"[bullet/]color"}
    {"[bullet/]bullet[/color]"}

news-pda-notification-header = Нова стаття в новинах
