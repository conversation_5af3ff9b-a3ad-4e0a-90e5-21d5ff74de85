- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatHoodBioGeneral
  name: "біо капюшон"
  suffix: Generic
  description: "Капюшон, який захищає голову та обличчя від біологічних забруднень."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Bio/general.rsi
  - type: Clothing
    slots: #PIRATE
    - HEAD #PIRATE
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
    sprite: Clothing/Head/Hoods/Bio/general.rsi
  - type: BreathMask
  - type: IngestionBlocker
  - type: Tag
    tags:
    - WhitelistChameleon
  - type: HideLayerClothing
    slots:
    - Hair
    - Snout

- type: entity
  parent: ClothingHeadHatHoodBioGeneral
  id: ClothingHeadHatHoodBioCmo
  name: "біо капюшон"
  suffix: CMO
  description: "Удосконалений капюшон для головного лікаря, який захищає голову та обличчя від біологічних забруднень."
  components:
  - type: IdentityBlocker
  - type: Sprite
    sprite: Clothing/Head/Hoods/Bio/cmo.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Bio/cmo.rsi

- type: entity
  parent: ClothingHeadHatHoodBioGeneral
  id: ClothingHeadHatHoodBioJanitor
  name: "біо капюшон"
  suffix: Janitor
  description: "Капюшон, який захищає голову та обличчя від біологічних забруднень."
  components:
  - type: IdentityBlocker
  - type: Sprite
    sprite: Clothing/Head/Hoods/Bio/janitor.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Bio/janitor.rsi

- type: entity
  parent: ClothingHeadHatHoodBioGeneral
  id: ClothingHeadHatHoodBioScientist
  name: "біо капюшон"
  suffix: Epistemics # DeltaV - Epistemics Department replacing Science
  description: "Капюшон, який захищає голову та обличчя від біологічних забруднень."
  components:
  - type: IdentityBlocker
  - type: Sprite
    sprite: Clothing/Head/Hoods/Bio/scientist.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Bio/scientist.rsi

- type: entity
  parent: ClothingHeadHatHoodBioGeneral
  id: ClothingHeadHatHoodBioSecurity
  name: "біо капюшон"
  suffix: Security
  description: "Капюшон, який захищає голову та обличчя від біологічних забруднень."
  components:
  - type: IdentityBlocker
  - type: Sprite
    sprite: Clothing/Head/Hoods/Bio/security.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Bio/security.rsi

- type: entity
  parent: ClothingHeadHatHoodBioGeneral
  id: ClothingHeadHatHoodBioVirology
  name: "біо капюшон"
  suffix: Virology
  description: "Капюшон, який захищає голову та обличчя від біологічних забруднень."
  components:
  - type: IdentityBlocker
  - type: Sprite
    sprite: Clothing/Head/Hoods/Bio/virology.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Bio/virology.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatHoodChaplainHood
  categories: [ HideSpawnMenu ]
  name: "капелюх капелана"
  description: "Максимальна побожність у цій зоряній системі."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/chaplain.rsi
  - type: Clothing
    slots: #PIRATE
    - HEAD #PIRATE
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
    sprite: Clothing/Head/Hoods/chaplain.rsi
  - type: Tag
    tags:
    - HamsterWearable
    - WhitelistChameleon
  - type: HideLayerClothing
    slots:
    - Hair

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatHoodCulthood
  name: "капюшон культу"
  description: "Не буває культу без культових капюшонів."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/cult.rsi
  - type: Clothing
    slots: #PIRATE
    - HEAD #PIRATE
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
    sprite: Clothing/Head/Hoods/cult.rsi
  - type: Tag
    tags:
    - WhitelistChameleon
  - type: HideLayerClothing
    slots:
    - Hair

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatHoodNunHood
  name: "монастирський капюшон"
  description: "Максимальна побожність у цій зоряній системі."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/nun.rsi
  - type: Clothing
    slots: #PIRATE
    - HEAD #PIRATE
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
    sprite: Clothing/Head/Hoods/nun.rsi
  - type: Tag
    tags:
    - HamsterWearable
    - WhitelistChameleon
  - type: HideLayerClothing
    slots:
    - Hair

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatHoodRad
  name: "радіаційний капюшон"
  description: "Капюшон захисного костюма, призначений для захисту від високої радіоактивності."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/rad.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/rad.rsi
  - type: Armor
    modifiers:
      coefficients:
        Heat: 0.95
        Radiation: 0.65
  - type: BreathMask
  - type: HideLayerClothing
    slots:
    - Hair
    - Snout

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatHoodGoliathCloak
  name: "капюшон плаща голіафа"
  description: "Капюшон плаща Голіафа, виготовлений зі шкури витривалої фауни з далекої планети."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/goliathcloak.rsi
  - type: Clothing
    slots: #PIRATE
    - HEAD #PIRATE
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
    sprite: Clothing/Head/Hoods/goliathcloak.rsi
  - type: Tag
    tags:
    - WhitelistChameleon
  - type: HideLayerClothing
    slots:
    - Hair

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatHoodIan
  categories: [ HideSpawnMenu ]
  name: "капюшон Іана"
  description: "Капюшон для завершення образу \"хорошого хлопчика\"."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/iansuit.rsi
  - type: Clothing
    slots: #PIRATE
    - HEAD #PIRATE
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
    sprite: Clothing/Head/Hoods/iansuit.rsi
  - type: HideLayerClothing
    slots:
    - Hair

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatHoodCarp
  categories: [ HideSpawnMenu ]
  name: "капюшон коропа"
  description: "Кострубатий капюшон, прикрашений пластиковими зубами космічного коропа."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/carpsuit.rsi
  - type: Clothing
    slots: #PIRATE
    - HEAD #PIRATE
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
    sprite: Clothing/Head/Hoods/carpsuit.rsi
  - type: HideLayerClothing
    slots:
    - Hair

- type: entity
  parent: ClothingHeadHatHoodCarp
  id: ClothingHeadHelmetHardsuitCarp
  categories: [ HideSpawnMenu ]
  components:
  - type: PressureProtection
    highPressureMultiplier: 0.6
    lowPressureMultiplier: 1000
  - type: TemperatureProtection
    coefficient: 0.2
  - type: BreathMask
  # this is on the hood so you only fool the fish if you wear the whole set
  # wear carp suit and security helmet, they'll know you are fake
  - type: FactionClothing
    faction: Dragon

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatHoodMoth
  name: "маска молі"
  description: "Маска у вигляді голови молі зазвичай виготовляється з легких матеріалів. Вона імітує форму голови молі з великими очима і довгими вусиками. Такі маски часто використовують у косплеї, або під час зйомок фільмів та відео."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/moth.rsi
  - type: Clothing
    slots: #PIRATE
    - HEAD #PIRATE
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
    sprite: Clothing/Head/Hoods/moth.rsi
  - type: IdentityBlocker
  - type: Tag
    tags:
    - WhitelistChameleon
  - type: HideLayerClothing
    slots:
    - Hair
    - Snout

#Winter Coat Hoods
- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterDefault
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто за замовчуванням"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hooddefault.rsi
  - type: Clothing
    slots: #PIRATE
    - HEAD #PIRATE
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
    sprite: Clothing/Head/Hoods/Coat/hooddefault.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterBartender
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто бармена"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodbartender.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodbartender.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterCaptain
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто капітана"
  description: "Дорогий капюшон, щоб голова капітана була в теплі."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodcaptain.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodcaptain.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterCargo
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто логістики" # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodcargo.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodcargo.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterCE
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто головного інженера"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodce.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodce.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterCentcom
  categories: [ HideSpawnMenu ]
  name: "капюшон для зимового пальто ЦентКому"
  description: "Капюшон для збереження голови центрального командира в теплі."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodcentcom.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodcentcom.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterChem
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто хіміка"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodchemist.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodchemist.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterCMO
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто головного лікаря"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodcmo.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodcmo.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterEngineer
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто інженера"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodengi.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodengi.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterHOP
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто голови персоналу"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodhop.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodhop.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterHOS
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто голови безпеки"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodhos.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodhos.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterHydro
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто гідропоніки"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodhydro.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodhydro.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterJani
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто прибиральника"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodjani.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodjani.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterMed
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто лікаря"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodmed.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodmed.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterMime
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто міма"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodmime.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodmime.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterMiner
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто шахтаря"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodminer.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodminer.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterPara
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто парамедика"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodpara.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodpara.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterQM
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто вантажника" # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodqm.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodqm.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterRD
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто наукового директора" # DeltaV - Epistemics Department replacing Science
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodrd.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodrd.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterRobo
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто робототехніка"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodrobo.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodrobo.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterSci
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто науковця"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodsci.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodsci.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterSec
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто охоронця"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodsec.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodsec.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterSyndie
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто синдикату"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodsyndicate.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodsyndicate.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterWarden
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто вардена"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodsec.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodsec.rsi

- type: entity
  parent: ClothingHeadHatHoodWinterBase
  id: ClothingHeadHatHoodWinterWeb
  categories: [ HideSpawnMenu ]
  name: "капюшон зимового пальто з павутини"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/Coat/hoodweb.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/Coat/hoodweb.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatHoodRedHood
  name: "Червона Шапочка"
  description: "Капюшон червоного плаща, зроблений так, щоб уберегти подорожнього від різкого світла. Іноді він виблискує у променях сонця."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hoods/redhood.rsi
  - type: Clothing
    sprite: Clothing/Head/Hoods/redhood.rsi
  - type: Tag
    tags:
    - WhitelistChameleon
  - type: HideLayerClothing
    slots:
    - Hair