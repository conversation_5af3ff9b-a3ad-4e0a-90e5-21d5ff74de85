## Damage command loc.

damage-command-description = Додати або видалити пошкодження об'єкту. 
damage-command-help = Використання: {$command} <type/group> <amount> [ignoreResistances] [uid]

damage-command-arg-type = <тип або група ушкоджень>
damage-command-arg-quantity = [кількість]
damage-command-arg-target = [ціль euid]

damage-command-error-type = {$arg} не є дійсною групою чи типом пошкодження.
damage-command-error-euid = {$arg} не є дійсним ідентифікаційним кодом юридичної особи.
damage-command-error-quantity = {$arg} не є достовірною кількістю.
damage-command-error-bool = {$arg} не є дійсною величиною.
damage-command-error-player = До сеансу не приєднано жодного об'єкта. Ви повинні вказати цільовий номер
damage-command-error-args = Неправильна кількість аргументів 