salvage-system-announcement-losing = The magnet is no longer able to hold the salvagable debris. Estimated time until loss: {$timeLeft} seconds.
salvage-system-announcement-spawn-debris-disintegrated = <PERSON><PERSON><PERSON> disintegrated during orbital transfer.
salvage-system-announcement-spawn-no-debris-available = No debris could be recovered by the salvage magnet.
salvage-system-announcement-arrived = A piece of salvagable debris has been pulled in. Estimated hold time: {$timeLeft} seconds.
salvage-asteroid-name = Asteroid

salvage-magnet-window-title = Salvage magnet
salvage-expedition-window-progression = Progression

salvage-magnet-resources = {$resource ->
    [OreIron] Iron
    [OreCoal] Coal
    [OreQuartz] Quartz
    [OreSalt] Salt
    [OreGold] Gold
    [OreDiamond] Diamond
    [OreSilver] Silver
    [OrePlasma] Plasma
    [OreUranium] Uranium
    [OreArtifactFragment] Artifact fragments
    [OreBluespace] Bluespace crystals
    [OreNormality] Normality crystals
    *[other] {$resource}
}

salvage-magnet-resources-count = {$count ->
    [1] (Poor)
    [2] (Moderate)
    [3] (Moderate)
    [4] (Rich)
    [5] (Rich)
    *[other] (Extraordinary)
}

# Asteroids
dungeon-config-proto-BlobAsteroid = Asteroid clump
dungeon-config-proto-ClusterAsteroid = Asteroid cluster
dungeon-config-proto-SpindlyAsteroid = Asteroid spiral
dungeon-config-proto-SwissCheeseAsteroid = Asteroid fragments

# Wrecks
salvage-map-proto-Small1 = Engineering Storage
salvage-map-proto-Small2 = Gaming Nook
salvage-map-proto-Small3 = Laundromat
salvage-map-proto-Small4 = Bar Salvage
salvage-map-proto-SmallShip1 = Pill
salvage-map-proto-SmallAISurveyDrone = AI Survey Drone
salvage-map-proto-SmallCargo = Mining Pod
salvage-map-proto-SmallChapel = Chapel
salvage-map-proto-SmallChef = Restaurant
salvage-map-proto-SmallParty = Birthday Party
salvage-map-proto-SmallSyndicate = Ruined Syndicate Ship
salvage-map-proto-SmallTesla = Tesla Containment

salvage-map-proto-SmallA1 = Asteroid Plasmafire
salvage-map-proto-Medium1 = Plasma-Trapped Cache
salvage-map-proto-MediumVault1 = Vault
salvage-map-proto-MediumOrchestra = Silent Orchestra
salvage-map-proto-MediumLibraryWreck = Abandoned Library
salvage-map-proto-MediumCargoWreck = Cargo Department Wreck
salvage-map-proto-MediumPirateWreck = Pirate Barge Fragment
salvage-map-proto-MediumHaulingShuttleWreck = Ruined Hauling Ship
salvage-map-proto-TickColony = Space Tick colony
salvage-map-proto-CargoDock = Asteroid Cargo Dock
salvage-map-proto-SpaceWaffleHome = Waffle Home
salvage-map-proto-MediumShuttleWreck = Ruined Emergency Shuttle
salvage-map-proto-MediumPetHospital = Pet and Bear Hospital
salvage-map-proto-MediumCrashedShuttle = Crashed Shuttle
salvage-map-proto-Meatball = Meatball
salvage-map-proto-VeganMeatball = Vegan Meatball

salvage-map-proto-StationStation = Station station
salvage-map-proto-AsteroidBase = Asteroid Base
salvage-map-proto-RuinCargoBase = Ruined Cargo Storage
salvage-map-proto-SecurityChunk = Security Department Chunk
salvage-map-proto-EngineeringChunk = Engineering Department Chunk
salvage-map-proto-OutpostArm = Overrun Outpost Arm