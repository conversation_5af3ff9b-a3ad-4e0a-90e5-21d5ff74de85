# Ripley

- type: entity
  id: RipleyCentralElectronics
  parent: BaseElectronics
  name: "центральний модуль управління Ripley"
  description: "Електричний центр управління для ріппі-меха."
  components:
    - type: Item
      storedRotation: 0
    - type: Sprite
      sprite: Objects/Misc/module.rsi
      state: mainboard
    - type: Tag
      tags:
      - RipleyCentralControlModule
    - type: GuideHelp
      guides:
      - Robotics

- type: entity
  id: RipleyPeripheralsElectronics
  parent: BaseElectronics
  name: "модуль управління периферійними пристроями Ripley"
  description: "Електрична периферія керує роботою механізму."
  components:
    - type: Sprite
      sprite: Objects/Misc/module.rsi
      state: id_mod
    - type: Tag
      tags:
      - RipleyPeripheralsControlModule
    - type: GuideHelp
      guides:
      - Robotics

# H.O.N.K.

- type: entity
  id: HonkerCentralElectronics
  parent: BaseElectronics
  name: "модуль центрального управління H.O.N.K"
  description: "Електричний центр управління механізмом H.O.N.K."
  components:
    - type: Item
      storedRotation: 0
    - type: Sprite
      sprite: Objects/Misc/module.rsi
      state: mainboard
    - type: Tag
      tags:
      - HonkerCentralControlModule
    - type: GuideHelp
      guides:
      - Robotics

- type: entity
  id: HonkerPeripheralsElectronics
  parent: BaseElectronics
  name: "модуль керування периферією H.O.N.K"
  description: "Керування електричною периферією для механізму H.O.N.K."
  components:
    - type: Sprite
      sprite: Objects/Misc/module.rsi
      state: id_mod
    - type: Tag
      tags:
      - HonkerPeripheralsControlModule
    - type: GuideHelp
      guides:
      - Robotics

- type: entity
  id: HonkerTargetingElectronics
  parent: BaseElectronics
  name: "модуль управління та наведення зброї H.O.N.K"
  description: "Електричне керування прицілюванням для механізму H.O.N.K."
  components:
    - type: Sprite
      sprite: Objects/Misc/module.rsi
      state: mcontroller # Goobstation
    - type: Tag
      tags:
      - HonkerTargetingControlModule
    - type: GuideHelp
      guides:
      - Robotics

# HAMTR

- type: entity
  id: HamtrCentralElectronics
  parent: BaseElectronics
  name: "центральний модуль управління HAMTR"
  description: "Електричний центр управління механізмом HAMTR."
  components:
    - type: Item
      storedRotation: 0
    - type: Sprite
      sprite: Objects/Misc/module.rsi
      state: mainboard
    - type: Tag
      tags:
      - HamtrCentralControlModule
    - type: GuideHelp
      guides:
      - Robotics

- type: entity
  id: HamtrPeripheralsElectronics
  parent: BaseElectronics
  name: "модуль керування периферійними пристроями HAMTR"
  description: "Електрична периферія для управління механізмом HAMTR."
  components:
    - type: Sprite
      sprite: Objects/Misc/module.rsi
      state: id_mod
    - type: Tag
      tags:
      - HamtrPeripheralsControlModule
    - type: GuideHelp
      guides:
      - Robotics
