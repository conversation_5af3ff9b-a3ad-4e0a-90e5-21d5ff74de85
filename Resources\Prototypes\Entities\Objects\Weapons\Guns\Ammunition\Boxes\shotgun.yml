- type: entity
  parent: BaseItem
  id: BaseAmmoProvider
  abstract: true
  components:
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
  - type: Item
    size: Normal
  - type: Sprite
    sprite: Objects/Storage/boxes.rsi

- type: entity
  parent: BaseAmmoProvider
  id: AmmoProviderShotgunShell
  abstract: true
  components:
    - type: BallisticAmmoProvider
      mayTransfer: true
      whitelist:
        tags:
        - ShellShotgun
      capacity: 16

# Shotgun Shells
- type: entity
  name: "дозатор набоїв для рушниці-мішка"
  parent: AmmoProviderShotgunShell
  id: BoxBeanbag
  description: "Коробка-розподільник, наповнена набоями, призначеними для рушниць для масових заворушень."
  components:
    - type: BallisticAmmoProvider
      proto: ShellShotgunBeanbag
    - type: Sprite
      layers:
        - state: boxwide
        - state: shellbeanbag

- type: entity
  name: "дозатор патронів для дробовика"
  parent: AmmoProviderShotgunShell
  id: BoxLethalshot
  description: "Коробка-розподільник зі смертоносними кулями, призначеними для рушниць для масових заворушень."
  components:
    - type: BallisticAmmoProvider
      proto: ShellShotgun
    - type: Sprite
      layers:
        - state: boxwide
        - state: shelllethal

- type: entity
  name: "дозатор патронів для дробовика"
  parent: AmmoProviderShotgunShell
  id: BoxShotgunSlug
  description: "Коробка з набоями, призначеними для рушниць, призначених для масових заворушень."
  components:
    - type: BallisticAmmoProvider
      proto: ShellShotgunSlug
    - type: Sprite
      layers:
        - state: boxwide
        - state: shellslug

- type: entity
  name: "диспенсер патронів люменбласт для дробовика"
  parent: AmmoProviderShotgunShell
  id: BoxShotgunLumen
  description: "Коробка-диспенсер, повна патронів люмен, призначених для дробовиків для придушення заворушень."
  components:
    - type: BallisticAmmoProvider
      proto: ShellShotgunLumen
    - type: Sprite
      layers:
        - state: boxwide
        - state: shelllumen

- type: entity
  name: "диспенсер запальних дробових набоїв"
  parent: AmmoProviderShotgunShell
  id: BoxShotgunIncendiary
  description: "Диспенсерна коробка, повна запальних набоїв, розроблених для дробовиків."
  components:
    - type: BallisticAmmoProvider
      proto: ShellShotgunIncendiary
    - type: Sprite
      layers:
        - state: boxwide
        - state: shellincendiary

- type: entity
  name: "дозатор уранових патронів для дробовика"
  parent: AmmoProviderShotgunShell
  id: BoxShotgunUranium
  description: "Коробка-дозатор з урановими патронами, призначеними для рушниць для масових заворушень."
  components:
    - type: BallisticAmmoProvider
      proto: ShellShotgunUranium
    - type: Sprite
      layers:
        - state: boxwide
        - state: shelluranium

- type: entity
  name: "дозатор тренувальних патронів для дробовика"
  parent: AmmoProviderShotgunShell
  id: BoxShotgunPractice
  description: "Коробка-розподільник з тренувальними патронами, призначеними для штурмових рушниць."
  components:
    - type: BallisticAmmoProvider
      proto: ShellShotgunPractice
    - type: Sprite
      layers:
        - state: boxwide
        - state: shellpractice

- type: entity
  name: "дозатор картриджів з транквілізаторами"
  parent: AmmoProviderShotgunShell
  id: BoxShellTranquilizer
  description: "Коробка з патронами з транквілізатором, призначеними для рушниць для масових заворушень."
  components:
    - type: BallisticAmmoProvider
      proto: ShellTranquilizer
    - type: Sprite
      layers:
        - state: boxwide
        - state: shellslug

- type: entity
  name: "Коробка .50 Дробу"
  parent: AmmoProviderShotgunShell
  id: BoxShotgunBirdshot
  description: "Коробка-дозатор, повна .50 дробу. Століттями використовувалася колоністами для полювання на дрібну дичину в інопланетних світах."
  components:
    - type: BallisticAmmoProvider
      proto: ShellShotgunBirdshot
    - type: Sprite
      layers:
        - state: boxwide
        - state: shelllethal

- type: entity
  name: "Коробка .50 00-Картечі"
  parent: AmmoProviderShotgunShell
  id: BoxShotgun00Buckshot
  description: "Коробка-дозатор, повна .50 \"двонульової\" картечі. Століттями використовувалася колоністами для полювання на дичину середнього розміру. Знання того, що означає \"Картеч\", було втрачено з часом."
  components:
    - type: BallisticAmmoProvider
      proto: ShellShotgun00Buckshot
    - type: Sprite
      layers:
        - state: boxwide
        - state: shelllethal

- type: entity
  name: "Коробка .50 0000-Картечі"
  parent: AmmoProviderShotgunShell
  id: BoxShotgun0000Buckshot
  description: "Коробка-дозатор, повна .50 \"чотиринульової\" картечі. Століттями використовувалася колоністами для полювання на інопланетну мегафауну. Вона так само легко прорве скафандр, як і товсті шкури."
  components:
    - type: BallisticAmmoProvider
      proto: ShellShotgun0000Buckshot
    - type: Sprite
      layers:
        - state: boxwide
        - state: shelllethal

- type: entity
  name: "дозатор патронів для рушниць з сигнальними ракетами"
  parent: AmmoProviderShotgunShell
  id: BoxShotgunFlare
  description: "Коробка-розподільник, наповнена сигнальними патронами, призначеними для рушниць для масових заворушень."
  components:
    - type: BallisticAmmoProvider
      proto: ShellShotgunFlare
    - type: Sprite
      layers:
        - state: boxwide
        - state: shellflare
