- type: entity
  abstract: true
  parent: BaseStructureDynamic
  id: BaseHardBomb
  description: "Просто продовжуй говорити, і ніхто не вибухне."
  name: "важка бомба"
  components:
    - type: Appearance
    - type: WiresVisuals
    - type: InteractionOutline
    - type: UserInterface
      interfaces:
        enum.WiresUiKey.Key:
          type: WiresBoundUserInterface
    - type: Wires
      layoutId: Defusable
      alwaysRandomize: true
    - type: Defusable
    - type: Rotatable
    - type: Explosive
      explosionType: Default
      totalIntensity: 20.0
      intensitySlope: 5
      maxIntensity: 4
    - type: ExplodeOnTrigger
    # If you nerf the syndicate bomb in any major way, this should probably drop down to at least 100s (not 90s to compensate for slower movement speed & less lag in SS14)
    # Unless, of course, you want the 90 seconds regardless. I can't stop you.
    - type: OnUseTimerTrigger
      delay: 180
      delayOptions: [180, 240, 300, 600, 900]
      initialBeepDelay: 0
      beepSound: /Audio/Machines/timer.ogg
    - type: Anchorable
      delay: 5
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.3,-0.3,0.3,0.3"
          density: 190
          mask:
            - MachineMask
          layer:
            - MachineLayer
    - type: WiresPanel
    - type: GuideHelp
      openOnActivation: true
      guides:
        - Defusal

- type: entity
  parent: BaseHardBomb
  id: TrainingBomb
  name: "навчальна бомба"
  description: "Бомба для чайників, інструкція не входить до комплекту."
  components:
    - type: Wires
      layoutId: Defusable
      alwaysRandomize: true
    - type: Sprite
      sprite: Structures/Machines/bomb.rsi
      layers:
        - state: training-bomb
        - state: training-bomb-active
          visible: false
          map: [ "primed" ]
        - state: training-bomb-wires
          visible: false
          map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
    - type: GenericVisualizer
      visuals:
        enum.DefusableVisuals.Active:
          primed:
            True: { visible: true }
            False: { visible: false }
        enum.WiresVisualLayers.MaintenancePanel:
          enum.WiresVisualLayers.MaintenancePanel:
            True: { visible: true }
            False: { visible: false }
    - type: PointLight
      color: "#0063C7"
      radius: 1.1
      softness: 1
    - type: Explosive
      explosionType: Default
      totalIntensity: 5.0
      intensitySlope: 5
      maxIntensity: 4
      canCreateVacuum: false
    - type: Defusable
      disposable: false

- type: entity
  parent: BaseHardBomb
  id: SyndicateBomb
  name: "бомба синдикату"
  description: "Бомба для оперативників та агентів Синдикату. Справжня, ніяких тренувань, приступайте до справи!"
  components:
    - type: Sprite
      sprite: Structures/Machines/bomb.rsi
      layers:
        - state: syndicate-bomb
        - state: syndicate-bomb-active
          visible: false
          map: [ "primed" ]
        - state: syndicate-bomb-wires
          visible: false
          map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
    - type: GenericVisualizer
      visuals:
        enum.DefusableVisuals.Active:
          primed:
            True: { visible: true }
            False: { visible: false }
        enum.WiresVisualLayers.MaintenancePanel:
          enum.WiresVisualLayers.MaintenancePanel:
            True: { visible: true }
            False: { visible: false }
    - type: PointLight
      color: "#C7001B"
      radius: 1.1
      softness: 1
    - type: Explosive
      explosionType: HardBomb
      totalIntensity: 4000.0
      intensitySlope: 3
      maxIntensity: 400

- type: entity
  parent: SyndicateBomb
  id: SyndicateBombFake
  suffix: fake
  components:
    - type: Explosive
      explosionType: Default
      totalIntensity: 5.0
      intensitySlope: 5
      maxIntensity: 4
      canCreateVacuum: false

- type: entity
  parent: SyndicateBomb
  id: DebugHardBomb
  name: "дебаг-бомба"
  suffix: DEBUG
  description: "Чорт зараз вибухне"
  components:
    - type: Defusable
      disposable: true
    - type: OnUseTimerTrigger
      delay: 10
      delayOptions: [10, 20, 30, 60, 90, 120, 150, 180, 210, 240, 270, 300]
