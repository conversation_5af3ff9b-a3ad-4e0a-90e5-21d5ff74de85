- type: gameMap
  id: Cog
  mapName: 'Коґ'
  mapPath: /Maps/cog.yml
  minPlayers: 10
  maxPlayers: 70
  stations:
    Cog:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: '{0} Cog {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: 'R4' # R4407/Goon. GS isn't as cool sounding.
        - type: StationJobs
          availableJobs:
            #service
            Captain: [ 1, 1 ]
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 2, 2 ]
            Botanist: [ 3, 3 ]
            Chef: [ 2, 2 ]
            Janitor: [ 3, 3 ]
            Chaplain: [ 1, 1 ]
            Reporter: [ 2, 2 ]
            Librarian: [ 1, 1 ]
            ServiceWorker: [ 3, 3 ]
            Zookeeper: [ 1, 1 ]
            #engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 3, 3 ]
            StationEngineer: [ 4, 4 ]
            TechnicalAssistant: [ 3, 3 ]
            #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            Chemist: [ 3, 3 ]
            MedicalDoctor: [ 4, 4 ]
            MedicalIntern: [ 3, 3 ]
            Psychologist: [ 1, 1 ]
            Paramedic: [ 1, 1 ]
            #science
            ResearchDirector: [ 1, 1 ]
            Scientist: [ 5, 5 ]
            ResearchAssistant: [ 3, 3 ]
            #security
            BlueshieldOfficer: [ 1, 1 ]
            HeadOfSecurity: [ 1, 1 ]
            Warden: [ 1, 1 ]
            SecurityOfficer: [ 4, 4 ]
            Detective: [ 1, 1 ]
            SecurityCadet: [ 4, 4 ]
            Lawyer: [ 2, 2 ]
            #supply
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ 3, 3 ]
            CargoTechnician: [ 4, 4 ]
            #civilian
            Passenger: [ -1, -1 ]
            Clown: [ 1, 1 ]
            Mime: [ 1, 1 ]
            Musician: [ 1, 1 ]
            Boxer: [ 2, 2]
            #silicon
            Borg: [ 3, 3 ]
