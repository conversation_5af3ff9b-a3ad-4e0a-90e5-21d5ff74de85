- type: trait
  id: CPRTraining
  category: Mental
  points: -3
  functions:
    - !type:TraitAddComponent
      components:
        - type: CPRTraining
  requirements:
    - !type:CharacterJobRequirement
      inverted: true
      jobs:
        - MedicalDoctor
        - Chemist
        - MedicalIntern
        - Paramedic
        - ChiefMedicalOfficer
        - Brigmedic
        - SeniorPhysician
        - BlueshieldOfficer

- type: trait
  id: SelfAware
  category: Mental
  points: -2
  functions:
    - !type:TraitAddComponent
      components:
        - type: SelfAware
          analyzableTypes:
            - Blunt
            - Slash
            - Piercing
            - Heat
            - Shock
            - Cold
            - Caustic
          detectableGroups:
            - Airloss
            - Toxin

- type: trait
  id: HeavyweightDrunk
  category: Physical
  points: -1
  requirements:
    - !type:CharacterJobRequirement
      inverted: true
      jobs:
        - Borg
        - MedicalBorg
    - !type:CharacterTraitRequirement
      inverted: true
      traits:
        - LightweightDrunk
        - LiquorLifeline
    - !type:CharacterSpeciesRequirement
      inverted: true
      species:
        - Dwarf
        - IPC
  functions:
    - !type:TraitReplaceComponent
      components:
        - type: LightweightDrunk
          boozeStrengthMultiplier: 0.5

- type: trait
  id: LiquorLifeline
  category: Physical
  points: -6
  requirements:
    - !type:CharacterJobRequirement
      inverted: true
      jobs:
        - Borg
        - MedicalBorg
    - !type:CharacterTraitRequirement
      inverted: true
      traits:
        - LightweightDrunk
        - HeavyweightDrunk
    - !type:CharacterSpeciesRequirement
      inverted: true
      species:
        - Dwarf
        - IPC
  functions:
    - !type:TraitReplaceComponent
      components:
        - type: LiquorLifeline
        - type: LightweightDrunk
          boozeStrengthMultiplier: 0.5

- type: trait
  id: Thieving
  category: Physical
  points: -3
  functions:
    - !type:TraitReplaceComponent
      components:
        - type: Thieving
          ignoreStripHidden: true
          stealth: Subtle
          stripTimeReduction: 0
          stripTimeMultiplier: 0.667
  requirements:
    - !type:CharacterSpeciesRequirement
      inverted: true
      species:
        - Felinid

- type: trait
  id: Voracious
  category: Physical
  points: -1
  functions:
    - !type:TraitReplaceComponent
      components:
        - type: ConsumeDelayModifier
          foodDelayMultiplier: 0.5
          drinkDelayMultiplier: 0.5
  requirements:
    - !type:CharacterSpeciesRequirement
      inverted: true
      species:
        - Vulpkanin
        - IPC

- type: trait
  id: ParkourTraining
  category: Physical
  points: -5
  requirements:
    - !type:CharacterTraitRequirement
      inverted: true
      traits:
        - Sluggish
        - SnailPaced
        - BadKnees
        - WheelchairBound
    - !type:CharacterSpeciesRequirement
      inverted: true
      species:
        - Diona
  functions:
    - !type:TraitReplaceComponent
      components:
        - type: ClimbDelayModifier
          climbDelayMultiplier: 0.5
        - type: SlippableModifier
          paralyzeTimeMultiplier: 0.7
        - type: SpeedModifiedByContactModifier
          walkModifierEffectiveness: 0.5
          sprintModifierEffectiveness: 0.5

- type: trait
  id: LightStep
  category: Auditory
  points: -2
  functions:
    - !type:TraitReplaceComponent
      components:
        - type: FootstepVolumeModifier
          sprintVolumeModifier: -10
          walkVolumeModifier: -10
  requirements:
    - !type:CharacterSpeciesRequirement
      inverted: true
      species:
        - Felinid
        - Tajaran

- type: trait
  id: Singer
  category: Auditory
  points: -1
  requirements:
    - !type:CharacterSpeciesRequirement
      inverted: true
      species:
        - Harpy
    - !type:CharacterTraitRequirement
      inverted: true
      traits:
        - Muted
  functions:
    - !type:TraitAddComponent
      components:
        - type: Singer
          proto: NormalSinger

- type: trait
  id: LyreBird
  category: Auditory
  points: -3
  requirements:
    - !type:CharacterSpeciesRequirement
      species:
        - Harpy
        - IPC
    - !type:CharacterTraitRequirement
      inverted: true
      traits:
        - Muted
  functions:
    - !type:TraitReplaceComponent
      components:
        - type: Singer
          proto: HarpySingerAll

- type: trait
  id: TrapAvoider
  category: Physical
  points: -3
  functions:
    - !type:TraitAddComponent
      components:
        - type: StepTriggerImmune
          whitelist:
            types:
              - Shard
              - Landmine
              - Mousetrap
              - SlipEntity
  requirements:
    - !type:CharacterSpeciesRequirement
      inverted: true
      species:
        - Felinid
        - Tajaran
        - Harpy

- type: trait
  id: AnomalousPositronics
  category: Psionics
  points: -4
  functions:
    - !type:TraitRemoveComponent
      components:
        - type: PsionicInsulation
  requirements:
    - !type:CharacterSpeciesRequirement
      species:
        - IPC

# - type: trait
#   id: AnimalFriend
#   category: Mental
#   points: -4
#   functions:
#     - !type:TraitModifyFactions
#       addFactions:
#         - AnimalFriend

- type: trait
  id: SurgeryTraining
  category: Mental
  points: -3
  functions:
    - !type:TraitAddComponent
      components:
        - type: SurgerySpeedModifier
          speedModifier: 1.6
  requirements:
    - !type:CharacterJobRequirement
      inverted: true
      jobs:
        - MedicalDoctor
        - Chemist
        - MedicalIntern
        - Paramedic
        - ChiefMedicalOfficer
        - Brigmedic
        - SeniorPhysician

- type: trait
  id: ExperiencedSurgeon
  category: Mental
  points: -3
  functions:
    - !type:TraitReplaceComponent
      components:
      - type: SurgerySpeedModifier
        speedModifier: 2.3
  requirements:
    - !type:CharacterJobRequirement
      jobs:
      - MedicalDoctor
      - Chemist
      - Paramedic
      - Brigmedic
      - SeniorPhysician

