- type: entity
  name: "кирка"
  parent: BaseItem
  id: Pickaxe
  description: "Зазубрена до досконалості, для вбивання в каміння."
  components:
  - type: Tag
    tags:
    - Pickaxe
    - ItemPickaxe
  - type: Sprite
    sprite: Objects/Weapons/Melee/pickaxe.rsi
    state: pickaxe
  - type: MeleeWeapon
    attackRate: 1.2
    range: 1.6
    wideAnimationRotation: -135
    soundHit:
      path: "/Audio/Weapons/smash.ogg"
      params:
        volume: -3
    damage:
      types:
        Blunt: 6
        Piercing: 3
    bluntStaminaDamageFactor: 2.0
    heavyDamageBaseModifier: 1.75
    maxTargets: 5
    angle: 80
  - type: DamageOtherOnHit
    staminaCost: 5
  - type: Wieldable
  - type: IncreaseDamageOnWield
    damage:
      groups:
        Brute: 5
      types:
        Structural: 30
  - type: Item
    size: Normal
    shape:
    - 0,0,2,0
    - 1,1,1,2
    sprite: Objects/Weapons/Melee/pickaxe.rsi
    storedRotation: -45
  - type: UseDelay

- type: entity
  name: "гірничий бур"
  parent: BaseItem
  id: MiningDrill
  description: "Потужний інструмент для швидкого буріння гірських порід."
  components:
  - type: Item
    storedRotation: -90
  - type: Tag
    tags:
    - Pickaxe
  - type: Sprite
    sprite: Objects/Tools/handdrill.rsi
    state: handdrill
  - type: MeleeWeapon
    autoAttack: true
    angle: 0
    wideAnimationRotation: -90
    soundHit:
      path: "/Audio/Items/drill_hit.ogg"
    attackRate: 0.5
    range: 1.5
    heavyStaminaCost: 0.7
    damage:
      types:
        Structural: 15
        Piercing: 6
  - type: DamageOtherOnHit
    staminaCost: 9
    damage:
      types:
        Structural: 25
        Piercing: 9
  - type: ThrowingAngle
    angle: 270

- type: entity
  name: "гірничий бур з алмазним наконечником"
  parent: MiningDrill
  id: MiningDrillDiamond
  description: "Значно ефективніший гірничий бур з алмазним наконечником."
  components:
  - type: Sprite
    sprite: Objects/Tools/handdrilldiamond.rsi
    state: handdrill
  - type: MeleeWeapon
    autoAttack: true
    angle: 0
    wideAnimationRotation: -90
    soundHit:
      path: "/Audio/Items/drill_hit.ogg"
    heavyStaminaCost: 0.55 # More efficient so less stamina needed to use
    damage:
      types:
        Piercing: 12
        Structural: 50
  - type: DamageOtherOnHit
    damage:
      types:
        Structural: 40
        Piercing: 18

- type: entity
  abstract: true
  parent: BaseItem
  id: BaseWeaponCrusher # Crusher? But I...
  name: "дробарка"
  description: "Ранній проект протокінетичного прискорювача."
  components:
  - type: Sharp
  - type: UnpoweredFlashlight
  - type: PointLight
    color: "#ffeead"
    enabled: false
    radius: 4
  - type: PressureDamageChange # Lavaland Change: Pressure damage change for kinetic weapons

- type: entity
  parent: BaseWeaponCrusher
  id: WeaponCrusher
  components:
  - type: Tag
    tags:
      - Pickaxe
  - type: Sprite
    sprite: Objects/Weapons/Melee/crusher.rsi
    state: icon
  - type: AmmoCounter
  - type: UseDelayOnShoot
  - type: UseDelay
    delay: 0.9
  - type: Gun
    soundGunshot: /Audio/Weapons/plasma_cutter.ogg
    fireRate: 1
    useKey: false
  - type: RechargeBasicEntityAmmo
    rechargeCooldown: 0.5
    rechargeSound:
      path: /Audio/Weapons/Guns/MagIn/kinetic_reload.ogg
  - type: BasicEntityAmmoProvider
    proto: BulletCharge
    capacity: 1
    count: 1
  - type: MeleeWeapon
    attackRate: 1.5 # Lavaland/Eris Change
    range: 1.65
    wideAnimationRotation: -135
    damage:
      types:
        Blunt: 13 # Lavaland Change: no damage when unwielded
        Slash: 7
    bluntStaminaDamageFactor: 2.0
    heavyRateModifier: 1.5
    heavyDamageBaseModifier: 1.2
    heavyStaminaCost: 2.5
    angle: 120
    soundHit:
      collection: MetalThud
  - type: DamageOtherOnHit
    staminaCost: 8
  - type: Wieldable
  - type: GunRequiresWield
  - type: Item
    size: Ginormous
  - type: DisarmMalus
  - type: Tool
    qualities:
      - Prying
  - type: Prying
  # Lavaland Change Start
  - type: MeleeRequiresWield
    fumbleOnAttempt: true
  - type: DamageBoostOnMarker
    boost:
      types: # Totals to 70 damage when hitting marked targets
        Blunt: 34
        Slash: 16
    backstabBoost: # And 30 extra for a backstab. Adding up to 100
      types:
        Blunt: 19
        Slash: 11

- type: entity
  parent: BaseWeaponCrusher
  id: WeaponCrusherDagger
  name: "кинджал дробарки"
  description: "Зменшена версія протокінетичної дробарки. Використовує кінетичну енергію для вібрації леза на високих швидкостях."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Melee/crusher_dagger.rsi
    state: icon
  - type: MeleeWeapon
    autoAttack: true
    wideAnimationRotation: -135
    attackRate: 0.8
    range: 1.4
    damage:
      types:
        Slash: 9
    heavyRateModifier: 1.4
    heavyDamageBaseModifier: 1.2
    maxTargets: 2
    angle: 20
  - type: DamageOtherOnHit
    staminaCost: 5
  - type: EmbeddableProjectile
  - type: EmbedPassiveDamage
  - type: ThrowingAngle
    angle: 225
  - type: Tag
    tags:
    - Knife

# Like a crusher... but better
- type: entity
  parent: WeaponCrusher
  id: WeaponCrusherGlaive
  name: "дробарко меч"
  description: "Ранній проект прото-кінетичного прискорювача у формі рукавиці."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Melee/crusher_glaive.rsi
  - type: UseDelay
    delay: 1.9
  - type: LeechOnMarker
    leech:
      groups:
        Brute: -21
  - type: MeleeWeapon
  - type: EmbeddableProjectile
  - type: EmbedPassiveDamage
  - type: ThrowingAngle
    angle: 225
  - type: Tag
    tags:
      - Pickaxe
