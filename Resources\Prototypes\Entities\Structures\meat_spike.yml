﻿- type: entity
  id: KitchenSpike
  parent: BaseStructure
  name: "м'ясний шпик"
  description: "Колос для збирання м'яса тварин."
  components:
  - type: InteractionOutline
  - type: Sprite
    # temp to make clickmask work
    sprite: Structures/meat_spike.rsi
    state: spike
    layers:
    - state: spike
      map: ["base"]
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
          params:
            volume: -4
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 1
  - type: Transform
    noRot: true
  - type: KitchenSpike
  - type: Anchorable
  - type: Pullable
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.KitchenSpikeVisuals.Status:
        base:
          Empty: { state: spike }
          Bloody: { state: spikebloody }
  - type: Construction
    graph: MeatSpike
    node: MeatSpike
  - type: GuideHelp
    guides:
    - Chef
