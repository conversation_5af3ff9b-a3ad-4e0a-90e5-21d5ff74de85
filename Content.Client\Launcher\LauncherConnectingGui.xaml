<Control xmlns="https://spacestation14.io"
         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
         xmlns:parallax="clr-namespace:Content.Client.Parallax"
         xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls">
    <parallax:ParallaxControl />
    <Control HorizontalAlignment="Center" VerticalAlignment="Center">
        <PanelContainer StyleClasses="AngleRect" />
        <BoxContainer Orientation="Vertical" MinSize="300 200">
            <BoxContainer Orientation="Horizontal">
                <Label Margin="8 0 0 0" Text="{Loc 'connecting-title'}"
                       StyleClasses="LabelHeading" VAlign="Center" />
                <Button Name="ExitButton" Text="{Loc 'connecting-exit'}"
                        HorizontalAlignment="Right" HorizontalExpand="True" />
            </BoxContainer>
            <controls:HighDivider />
            <BoxContainer Orientation="Vertical" VerticalExpand="True" Margin="4 4 4 0">
                <Control VerticalExpand="True" Margin="0 0 0 8">
                    <BoxContainer Orientation="Vertical" Name="ConnectingStatus">
                        <Label Text="{Loc 'connecting-in-progress'}" Align="Center" />
                        <!-- Who the fuck named these cont- oh wait I did -->
                        <Label Name="ConnectStatus" StyleClasses="LabelSubText" Align="Center" />
                    </BoxContainer>
                    <BoxContainer Orientation="Vertical" Name="ConnectFail" Visible="False">
                        <RichTextLabel Name="ConnectFailReason" VerticalAlignment="Stretch"/>
                        <Button Name="RetryButton" Text="{Loc 'connecting-retry'}"
                                HorizontalAlignment="Center"
                                VerticalExpand="True" VerticalAlignment="Bottom" />
                    </BoxContainer>
                    <BoxContainer Orientation="Vertical" Name="Disconnected">
                        <Label Text="{Loc 'connecting-disconnected'}" Align="Center" />
                        <Label Name="DisconnectReason" Align="Center" />
                        <Button Name="ReconnectButton" Text="{Loc 'connecting-reconnect'}"
                                HorizontalAlignment="Center"
                                VerticalExpand="True" VerticalAlignment="Bottom" />
                        <Button Name="RedialButton" Text="{Loc 'connecting-redial'}"
                                Disabled="True"
                                HorizontalAlignment="Center"
                                VerticalExpand="True" VerticalAlignment="Bottom" />
                    </BoxContainer>
                </Control>
                <Label Name="ConnectingAddress" StyleClasses="LabelSubText" HorizontalAlignment="Center" />
            </BoxContainer>
            <PanelContainer>
                <PanelContainer.PanelOverride>
                    <gfx:StyleBoxFlat BackgroundColor="#444" ContentMarginTopOverride="2" />
                </PanelContainer.PanelOverride>
            </PanelContainer>

            <BoxContainer Orientation="Horizontal" Margin="12 0 4 0" VerticalAlignment="Bottom">
                <Label Text="{Loc 'connecting-tip'}" StyleClasses="LabelSubText" />
                <Label Text="{Loc 'connecting-version'}" StyleClasses="LabelSubText"
                       HorizontalAlignment="Right" HorizontalExpand="True" />
            </BoxContainer>
        </BoxContainer>
    </Control>
    <!-- Bottom window for tips -->
    <PanelContainer Name="LoginTips" StyleClasses="AngleRect" Margin="0 10" MaxWidth="600" VerticalExpand="True" VerticalAlignment="Bottom">
        <BoxContainer Orientation="Vertical" VerticalExpand="True">
            <controls:StripeBack>
                <BoxContainer Orientation="Horizontal" HorizontalAlignment="Center">
                    <Label Name="LoginTipTitle" Text="Tip" StyleClasses="LabelHeading" Align="Center"/>
                </BoxContainer>
            </controls:StripeBack>
            <BoxContainer Orientation="Vertical" Margin="5 5 5 5" >
                <RichTextLabel Name="LoginTip" VerticalExpand="True" />
            </BoxContainer>
        </BoxContainer>
    </PanelContainer>
</Control>
