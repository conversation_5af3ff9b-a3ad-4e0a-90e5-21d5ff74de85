supermatter-examine-integrity =
    Its integrity is [color=yellow]{$integrity}%[/color].
supermatter-healing =
    Crystalline hyperstructure returning to safe operating parameters. Integrity: {$integrity}%.
supermatter-warning =
    Warning! Crystal hyperstructure integrity faltering! Integrity: {$integrity}%.
supermatter-emergency =
    DANGER! Crystal hyperstructure integrity reaching critical levels! Integrity: {$integrity}%.
supermatter-delam-explosion =
    CRYSTAL DELAMINATION IMMINENT! The crystal has reached critical integrity failure! Emergency causality destabilization field has been engaged.
supermatter-delam-overmass =
    CRYSTAL DELAMINATION IMMINENT! Crystal hyperstructure integrity has reached critical mass failure! Singularity formation imminent!
supermatter-delam-tesla =
    CRYSTAL DELAMINATION IMMINENT! Crystal hyperstructure integrity has reached critical power surge failure! Energy ball formation imminent!
supermatter-delam-cascade =
    CRYSTAL DELAMINATION IMMINENT! Harmonic frequency limits exceeded, casualty destabilization field could not be engaged!
supermatter-delam-cancel =
    Crystalline hyperstructure returning to safe operating parameters. Failsafe has been disengaged. Integrity: {$integrity}%.
supermatter-delam-player =
    You feel reality distort for a moment...
supermatter-seconds-before-delam =
    Estimated time before delamination: {$seconds} seconds.
supermatter-seconds-before-delam-countdown =
    {$seconds} seconds remain before causality stabilization.
supermatter-seconds-before-delam-imminent =
    {$seconds}...

supermatter-tamper-begin =
    You begin carefully cutting a piece off the supermatter crystal...
supermatter-tamper-end =
    You feel the power of a thousand suns laying on your palms. Or is it all the radiation?
supermatter-announcement-cc-tamper =
    Our automatic casualty system has detected that the supermatter crystal structural integrity was compromised by an external force.
    Engineering department, report to the supermatter engine immediately.

supermatter-collide =
    {CAPITALIZE(THE($target))} slams into the {$sm}!
supermatter-collide-mob =
    {CAPITALIZE(THE($target))}'s body flashes into dust!
