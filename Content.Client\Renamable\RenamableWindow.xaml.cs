using Robust.Client.UserInterface.CustomControls;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Renamable;


[GenerateTypedNameReferences]
public sealed partial class RenamableWindow : DefaultWindow
{
    public event Action<string>? OnNameChanged;

    /// <summary>
    /// Is the user currently entering text into the control?
    /// </summary>
    private bool _focused;
    // TODO LineEdit Make this a bool on the LineEdit control

    private string _name = string.Empty;

    public RenamableWindow()
    {
        RobustXamlLoader.Load(this);

        NameLineEdit.OnTextEntered += args =>
        {
            _name = args.Text;
            OnNameChanged?.Invoke(_name);
        };

        NameLineEdit.OnFocusEnter += _ => _focused = true;
        NameLineEdit.OnFocusExit += _ =>
        {
            _focused = false;
            NameLineEdit.Text = _name;
        };
    }

    public void SetCurrentName(string label)
    {
        if (label == _name)
            return;

        _name = label;
        if (!_focused)
            NameLineEdit.Text = label;
    }
}
