# When adding new drinks also add to random spawner located in Resources\Prototypes\Entities\Markers\Spawners\Random\Food_Drinks\drinks_bottles.yml
- type: entity
  parent: DrinkBase
  id: DrinkBottlePlasticBaseFull
  abstract: true
  suffix: Full
  components:
  - type: Tag
    tags:
    - DrinkBottle
  - type: Openable
    sound:
      collection: bottleOpenSounds
    closeable: true
    closeSound:
      collection: bottleCloseSounds
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 100
  - type: Sprite
    state: icon
    sprite: Objects/Consumable/Drinks/generic_jug.rsi # fallback to generic plastic jug
  - type: Item
    size: Normal
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 5
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:SpillBehavior { }
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: TrashOnSolutionEmpty
    solution: drink
  - type: PhysicalComposition
    materialComposition:
      Plastic: 100
  - type: PressurizedSolution
    solution: drink
  - type: Shakeable

- type: entity
  parent: DrinkBottlePlasticBaseFull
  id: DrinkBottleGlassBaseFull
  abstract: true
  components:
  - type: Sprite
    state: icon
  - type: MeleeWeapon
    bluntStaminaDamageFactor: 2.0
    damage:
      types:
        Blunt: 7.5
    heavyRangeModifier: 1.2
    heavyStaminaCost: 5
    maxTargets: 1
    angle: 25
  - type: DamageOnLand
    damage:
      types:
        Blunt: 5
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 5
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 5
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:SpillBehavior { }
      - !type:SpawnEntitiesBehavior
        spawn:
          BrokenBottle:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Tool
    qualities:
    - Rolling
    speedModifier: 0.75 # not as good as a rolling pin but does the job
  - type: PhysicalComposition
    materialComposition:
      Glass: 100

- type: entity
  id: DrinkBottleVisualsOpenable
  abstract: true
  components:
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.OpenableVisuals.Opened:
        enum.OpenableVisuals.Layer:
          True: {state: "icon_open"}
          False: {state: "icon"}
  - type: Sprite
    layers:
      - state: icon
        map: ["enum.OpenableVisuals.Layer"]

- type: entity
  id: DrinkBottleVisualsAll
  abstract: true
  components:
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.OpenableVisuals.Opened:
        enum.OpenableVisuals.Layer:
          True: {state: "icon_open"}
          False: {state: "icon_empty"}
  - type: Sprite
    sprite: Objects/Consumable/Drinks/alco-bottle.rsi
    layers:
      - state: icon
        map: ["enum.OpenableVisuals.Layer"]
      - state: fill-1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
  - type: SolutionContainerVisuals
    maxFillLevels: 5
    fillBaseName: fill-

- type: entity
  parent: [DrinkBottleVisualsAll, DrinkBottleGlassBaseFull]
  id: DrinkAbsintheBottleFull
  name: "Jailbreaker Verte"
  description: "Один ковток - і ви точно знаєте, що добре проведете час."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Absinthe
          Quantity: 100
  - type: Sprite
    sprite: Objects/Consumable/Drinks/absinthebottle.rsi
  - type: Sealable

- type: entity
  parent: [DrinkBottleVisualsAll, DrinkBottleGlassBaseFull]
  id: DrinkBlueCuracaoBottleFull
  name: "Міс Блакитний Кюрасао"
  description: "Фруктовий, виключно блакитний напій. Не дозволяє імбіберу використовувати п'яту магію."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: BlueCuracao
          Quantity: 100
  - type: Sprite
    sprite: Objects/Consumable/Drinks/alco-bottle.rsi
    layers:
      - state: icon_blue
        map: ["enum.OpenableVisuals.Layer"]
      - state: fill-1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
  - type: SolutionContainerVisuals
    maxFillLevels: 6
    fillBaseName: fill-

- type: entity
  parent: [DrinkBottleVisualsOpenable, DrinkBottleGlassBaseFull]
  id: DrinkBottleOfNothingFull
  name: "пляшка нічого"
  description: "Пляшка, наповнена порожнечею."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Nothing
          Quantity: 100
  - type: Sprite
    sprite: Objects/Consumable/Drinks/bottleofnothing.rsi
  - type: Sealable

- type: entity
  parent: [DrinkBottleVisualsOpenable, DrinkBottleGlassBaseFull]
  id: DrinkChampagneBottleFull
  name: "пляшка шампанського"
  description: "Тільки люди, позбавлені уяви, можуть не знайти виправдання для шампанського."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Champagne
          Quantity: 100
  - type: Sprite
    sprite: Objects/Consumable/Drinks/champagnebottle.rsi
  - type: Openable
    closeable: false # Champagne corks are fat. Not worth the effort.

- type: entity
  parent: [DrinkBottleVisualsAll, DrinkBottleGlassBaseFull]
  id: DrinkCognacBottleFull
  name: "пляшка коньяку"
  description: "Солодкий і міцний алкогольний напій, виготовлений після численних перегонок і років витримки. Цього разу ви можете не кричати \"SHITCURITY\"."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Cognac
          Quantity: 100
  - type: Label
    currentLabel: cognac
  - type: Sprite
    sprite: Objects/Consumable/Drinks/cognacbottle.rsi
  - type: Sealable

- type: entity
  parent: [DrinkBottleVisualsAll, DrinkBottlePlasticBaseFull]
  id: DrinkColaBottleFull
  name: "космічна пляшка коли"
  description: "Кола. У космосі."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Cola
          Quantity: 100
  - type: Label
    currentLabel: cola
  - type: Sprite
    sprite: Objects/Consumable/Drinks/colabottle.rsi
  - type: Sealable

- type: entity
  parent: [DrinkBottleVisualsAll, DrinkBottleGlassBaseFull]
  id: DrinkGrenadineBottleFull
  name: "пляшка сиропу з шипшини з гренадіном"
  description: "Солодкий і гострий, барний сироп, який використовується для додавання кольору або аромату напоям."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Grenadine
          Quantity: 100
  # intended use is to spike drinks so starts open
  - type: Openable
    opened: true
  - type: Sprite
    sprite: Objects/Consumable/Drinks/grenadinebottle.rsi
  - type: SolutionContainerVisuals
    maxFillLevels: 6

- type: entity
  parent: [DrinkBottleVisualsAll, DrinkBottleGlassBaseFull]
  id: DrinkGinBottleFull
  name: "Джин Griffeater"
  description: "Пляшка високоякісного джину, виробленого на космічній станції \"Новий Лондон\"."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Gin
          Quantity: 100
  - type: Label
    currentLabel: gin
  - type: Sprite
    sprite: Objects/Consumable/Drinks/ginbottle.rsi
  - type: Sealable

- type: entity
  parent: [DrinkBottleVisualsAll, DrinkBottleGlassBaseFull]
  id: DrinkGildlagerBottleFull
  name: "пляшка з позолотою"
  description: "100-градусний шнапс з корицею, створений для дівчат-підлітків-алкоголіків на весняних канікулах."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Gildlager
          Quantity: 100
  - type: Sprite
    sprite: Objects/Consumable/Drinks/gildlagerbottle.rsi
  - type: Sealable

- type: entity
  parent: [DrinkBottleVisualsOpenable, DrinkBottleGlassBaseFull]
  id: DrinkCoffeeLiqueurBottleFull
  name: "пляшка кавового лікеру"
  description: "Чудовий смак кави без жодної користі."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: CoffeeLiqueur
          Quantity: 100
  - type: Label
    currentLabel: coffee liqueur
  - type: Sprite
    sprite: Objects/Consumable/Drinks/coffeeliqueurbottle.rsi
  - type: Sealable

- type: entity
  parent: [DrinkBottleVisualsAll, DrinkBottleGlassBaseFull]
  id: DrinkMelonLiquorBottleFull
  name: "смарагдовий динний лікер"
  description: "Пляшка динного лікеру \"Смарагдова диня\" міцністю 46 градусів. Солодкий і легкий."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: MelonLiquor
          Quantity: 100
  - type: Sprite
    sprite: Objects/Consumable/Drinks/alco-bottle.rsi
    layers:
      - state: icon_green
        map: ["enum.OpenableVisuals.Layer"]
      - state: fill-1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
  - type: SolutionContainerVisuals
    maxFillLevels: 6
    fillBaseName: fill-

- type: entity
  parent: [DrinkBottleVisualsAll, DrinkBottleGlassBaseFull]
  id: DrinkPatronBottleFull
  name: "пляшка Wrapp Artiste Patron"
  description: "Срібляста текіла, яку подають у космічних нічних клубах по всій галактиці."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Patron
          Quantity: 100
  - type: Sprite
    sprite: Objects/Consumable/Drinks/patronbottle.rsi

- type: entity
  parent: [DrinkBottleVisualsOpenable, DrinkBottleGlassBaseFull]
  id: DrinkPoisonWinebottleFull
  name: "оксамитова пляшка чаклуна"
  description: "Яка чудова упаковка для безсумнівно високоякісного вина! Вінтаж повинен бути дивовижним!"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: PoisonWine
          Quantity: 100
  - type: Sprite
    sprite: Objects/Consumable/Drinks/pwinebottle.rsi
  - type: Sealable
  - type: Tag
    tags:
    - Wine
    - DrinkBottle

- type: entity
  parent: [DrinkBottleVisualsAll, DrinkBottleGlassBaseFull]
  id: DrinkRumBottleFull
  name: "кубинський ром з прянощами капітана Піта"
  description: "Це не просто ром, о ні. Це практично Гріфф у пляшці."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Rum
          Quantity: 100
  - type: Label
    currentLabel: rum
  - type: Sprite
    sprite: Objects/Consumable/Drinks/rumbottle.rsi
  - type: Sealable

- type: entity
  parent: [DrinkBottleVisualsAll, DrinkBottlePlasticBaseFull]
  id: DrinkSpaceMountainWindBottleFull
  name: "вітряна пляшка з космічної гори"
  description: "Проникає крізь тебе, як космічний вітер."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: SpaceMountainWind
          Quantity: 100
  - type: Drink
  - type: Label
    currentLabel: space mountain wind
  - type: Sprite
    sprite: Objects/Consumable/Drinks/space_mountain_wind_bottle.rsi
  - type: Sealable

- type: entity
  parent: [DrinkBottleVisualsAll, DrinkBottlePlasticBaseFull]
  id: DrinkSpaceUpBottleFull
  name: "пляшка з проміжним простором"
  description: "Смак у роті, наче пробоїна в оболонці."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: SpaceUp
          Quantity: 100
  - type: Drink
  - type: Label
    currentLabel: space-up
  - type: Sprite
    sprite: Objects/Consumable/Drinks/space-up_bottle.rsi
  - type: Sealable

- type: entity
  parent: [DrinkBottleVisualsAll, DrinkBottleGlassBaseFull]
  id: DrinkTequilaBottleFull
  name: "пляшка текіли гарантованої якості caccavo"
  description: "Виготовлений з високоякісних нафтових дистилятів, чистого талідоміду та інших високоякісних інгредієнтів!"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Tequila
          Quantity: 100
  - type: Label
    currentLabel: tequila
  - type: Sprite
    sprite: Objects/Consumable/Drinks/tequillabottle.rsi
  - type: Sealable

- type: entity
  parent: [DrinkBottleVisualsAll, DrinkBottleGlassBaseFull]
  id: DrinkVermouthBottleFull
  name: "пляшка вермуту \"Золоте око"
  description: "Солодка, солодка сухість!"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Vermouth
          Quantity: 100
  - type: Label
    currentLabel: vermouth
  - type: Sprite
    sprite: Objects/Consumable/Drinks/vermouthbottle.rsi
  - type: Sealable

- type: entity
  parent: [DrinkBottleVisualsAll, DrinkBottleGlassBaseFull]
  id: DrinkVodkaBottleFull
  name: "пляшка горілки"
  description: "Горілка. Основний вибір напою та пального для росіян у всьому світі."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Vodka
          Quantity: 100
  - type: Label
    currentLabel: vodka
  - type: Sprite
    sprite: Objects/Consumable/Drinks/vodkabottle.rsi
  - type: Sealable

- type: entity
  parent: [DrinkBottleVisualsAll, DrinkBottleGlassBaseFull]
  id: DrinkWhiskeyBottleFull
  name: "особливий заповідник дядька Гіта"
  description: "Односолодовий віскі преміум-класу, дбайливо витриманий у тунелях ядерного сховища. ТУНЕЛЬНИЙ ВІСКІ РУЛИТЬ."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Whiskey
          Quantity: 100
  - type: Label
    currentLabel: whiskey
  - type: Sprite
    sprite: Objects/Consumable/Drinks/whiskeybottle.rsi
  - type: Sealable

- type: entity
  parent: [DrinkBottleVisualsOpenable, DrinkBottleGlassBaseFull]
  id: DrinkWineBottleFull
  name: "двобородий бородань спеціальна винна пляшка"
  description: "Слабка аура тривоги та болю оточує пляшку."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Wine
          Quantity: 100
  - type: Label
    currentLabel: wine
  - type: Sprite
    sprite: Objects/Consumable/Drinks/winebottle.rsi
  - type: Sealable
  - type: Tag
    tags:
    - Wine
    - DrinkBottle

# Small Bottles

- type: entity
  parent: [DrinkBottleVisualsAll, DrinkBottleGlassBaseFull]
  id: DrinkBeerBottleFull
  name: "пиво"  # beer it is. coffee. beer? coff-ee? be-er? c-o... b-e
  description: "Алкогольний напій, виготовлений із солоджених зерен, хмелю, дріжджів і води."
  components:
  - type: Item
    size: Small
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 50
        reagents:
        - ReagentId: Beer
          Quantity: 50
  - type: Sprite
    sprite: Objects/Consumable/Drinks/beer.rsi
  - type: Openable
    closeable: false
  - type: Tag
    tags:
    - Beer
    - DrinkBottle

- type: entity
  parent: [DrinkBottleVisualsAll, DrinkBottleGlassBaseFull]
  id: DrinkBeerGrowler # Needs to be renamed DrinkBeerBottleFull
  name: "Пивний рик"  # beer it is. coffee. beer? coff-ee? be-er? c-o... b-e
  description: "Алкогольний напій, виготовлений із солоду, хмелю, дріжджів та води. Пляшка XL гроулер."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 150
        reagents:
        - ReagentId: Beer
          Quantity: 150
  - type: Label
    currentLabel: beer
  - type: Sprite
    sprite: Objects/Consumable/Drinks/beer.rsi
  - type: Openable
    closeable: false
  - type: Tag
    tags:
    - Beer
    - DrinkBottle

- type: entity
  parent: [DrinkBottleVisualsAll, DrinkBottlePlasticBaseFull]
  id: DrinkAleBottleFull
  name: "Магматичний ель"
  description: "Напій на вибір справжнього дорфа."
  components:
  - type: Item
    size: Small
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 50
        reagents:
        - ReagentId: Ale
          Quantity: 50
  - type: Sprite
    sprite: Objects/Consumable/Drinks/alebottle.rsi
  - type: Openable
    closeable: false

- type: entity
  parent: [DrinkBottleVisualsAll, DrinkBottlePlasticBaseFull]
  id: DrinkAleBottleFullGrowler
  name: "Магма-Ель Гроулер"
  description: "Напій, який обирає справжній дорф. Величезна пляшка для гроулерів."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 150
        reagents:
        - ReagentId: Ale
          Quantity: 150
  - type: Label
    currentLabel: ale
  - type: Sprite
    sprite: Objects/Consumable/Drinks/alebottle.rsi
  - type: Openable
    closeable: false

- type: entity
  parent: [DrinkBottleVisualsOpenable, DrinkBottlePlasticBaseFull]
  id: DrinkWaterBottleFull
  name: "пляшка з водою"
  description: "Проста чиста вода невідомого походження. Ви думаєте, що, можливо, ви не хочете цього знати."
  components:
  - type: Item
    size: Small
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: Water
          Quantity: 30
  - type: Drink
  - type: Sprite
    sprite: Objects/Consumable/Drinks/waterbottle.rsi
    layers:
      - state: icon
        map: ["enum.OpenableVisuals.Layer"]
      - state: icon-1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 5
    fillBaseName: icon-
    inHandsMaxFillLevels: 2
    inHandsFillBaseName: -fill-
  - type: Sealable

- type: entity
  parent: DrinkWaterBottleFull
  id: DrinkSodaWaterBottleFull
  name: "пляшка газованої води"
  description: "Як вода, але сердита!"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 150
        reagents:
        - ReagentId: SodaWater
          Quantity: 150
  - type: Label
    currentLabel: soda water

- type: entity
  parent: DrinkWaterBottleFull
  id: DrinkTonicWaterBottleFull
  name: "пляшка тонізуючої води"
  description: "Як газована вода, але, можливо, зліша? Часто солодша."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 150
        reagents:
        - ReagentId: TonicWater
          Quantity: 150
  - type: Label
    currentLabel: tonic water

# Cartons, TODO: this needs to be moved elsewhere eventually, since cartons shouldnt smash into glass shards

- type: entity
  parent: [DrinkBottleVisualsOpenable, DrinkBottlePlasticBaseFull]
  id: DrinkJuiceLimeCartonXL
  name: "сік лайма XL"
  description: "Солодко-кисла смакота."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 150
        reagents:
        - ReagentId: JuiceLime
          Quantity: 150
  - type: Drink
  - type: Label
    currentLabel: lime juice
  - type: Sprite
    sprite: Objects/Consumable/Drinks/limejuice.rsi

- type: entity
  parent: [DrinkBottleVisualsOpenable, DrinkBottlePlasticBaseFull]
  id: DrinkJuiceOrangeCartonXL
  name: "апельсиновий сік XL"
  description: "Повно вітамінів і смакоти!"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 150
        reagents:
        - ReagentId: JuiceOrange
          Quantity: 150
  - type: Drink
  - type: Label
    currentLabel: orange juice
  - type: Sprite
    sprite: Objects/Consumable/Drinks/orangejuice.rsi

- type: entity
  parent: [DrinkBottleVisualsOpenable, DrinkBottlePlasticBaseFull]
  id: DrinkCreamCartonXL
  name: "Молочний крем XL"
  description: "Це вершки. Зроблені з молока. Що ще ти думав там знайти?"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 150
        reagents:
        - ReagentId: Cream
          Quantity: 150
  - type: Drink
  - type: Label
    currentLabel: cream
  - type: Sprite
    sprite: Objects/Consumable/Drinks/cream.rsi

#boring jugs some more sprites are made

- type: entity
  parent: DrinkBottlePlasticBaseFull
  id: DrinkSugarJug
  name: "колба з цукром"
  suffix: For Drinks, Full
  description: "деякі люди додають це в каву..."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 300
        reagents:
        - ReagentId: Sugar
          Quantity: 300
  - type: Drink

- type: entity
  parent: DrinkBottlePlasticBaseFull
  id: DrinkLemonLimeJug
  name: "колба з лимон лаймом"
  description: "подвійне цитрусове відчуття."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 300
        reagents:
        - ReagentId: LemonLime
          Quantity: 300
  - type: Drink
  - type: Label
    currentLabel: lemon-lime

- type: entity
  parent: DrinkBottlePlasticBaseFull
  id: DrinkMeadJug
  name: "колба медовухи"
  description: "зберігати медовуху в пластиковій колбі має бути злочином."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 150
        reagents:
        - ReagentId: Mead
          Quantity: 150
  - type: Drink
  - type: Label
    currentLabel: mead

- type: entity
  parent: DrinkBottlePlasticBaseFull
  id: DrinkIceJug
  name: "колба льоду"
  description: "уперта вода. Досить прохолодно."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 300
        reagents:
        - ReagentId: Ice
          Quantity: 300
  - type: Drink
  - type: Label
    currentLabel: ice

- type: entity
  parent: DrinkBottlePlasticBaseFull
  id: DrinkCoconutWaterJug
  name: "колба кокосової води"
  description: "Цінною є саме внутрішня частина кокосового горіха."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 300
        reagents:
        - ReagentId: CoconutWater
          Quantity: 300
  - type: Drink
  - type: Label
    currentLabel: coconut water

- type: entity
  parent: DrinkBottlePlasticBaseFull
  id: DrinkCoffeeJug
  name: "колба кави"
  description: "сік для пробудження, підігрітий."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 300
        reagents:
        - ReagentId: Coffee
          Quantity: 300
  - type: Drink
  - type: Label
    currentLabel: coffee

- type: entity
  parent: DrinkBottlePlasticBaseFull
  id: DrinkTeaJug
  name: "колба чаю"
  description: "улюблений напій бріошів та хіпстерів."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 300
        reagents:
        - ReagentId: Tea
          Quantity: 300
  - type: Drink
  - type: Label
    currentLabel: tea

- type: entity
  parent: DrinkBottlePlasticBaseFull
  id: DrinkGreenTeaJug
  name: "колба зеленого чаю"
  description: "це як чай... але зелений! Чудовий засіб для заспокоєння шлунку."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 300
        reagents:
        - ReagentId: GreenTea
          Quantity: 300
  - type: Drink
  - type: Label
    currentLabel: green tea

- type: entity
  parent: DrinkBottlePlasticBaseFull
  id: DrinkIcedTeaJug
  name: "колба холодного чаю"
  description: "для випадків, коли звичайний чай занадто гарячий для вас"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 300
        reagents:
        - ReagentId: IcedTea
          Quantity: 300
  - type: Drink

- type: entity
  parent: DrinkBottlePlasticBaseFull
  id: DrinkDrGibbJug
  name: "колба Доктор Гібб"
  description: "так, я теж не знаю..."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 300
        reagents:
        - ReagentId: DrGibb
          Quantity: 300
  - type: Drink
  - type: Label
    currentLabel: dr gibb

- type: entity
  parent: DrinkBottlePlasticBaseFull
  id: DrinkRootBeerJug
  name: "колба пива"
  description: "цей напій змушує австралійців хихикати"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 300
        reagents:
        - ReagentId: RootBeer
          Quantity: 300
  - type: Drink
  - type: Label
    currentLabel: root beer

- type: entity
  parent: DrinkBottlePlasticBaseFull
  id: DrinkWaterMelonJuiceJug
  name: "колба кавунового соку"
  description: "Може містити залишки насіння"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 300
        reagents:
        - ReagentId: JuiceWatermelon
          Quantity: 300
  - type: Drink
  - type: Label
    currentLabel: watermelon juice

- type: entity
  parent: DrinkBottlePlasticBaseFull
  id: DrinkEnergyDrinkJug
  name: "колба ред булу"
  description: "Колба Red Bool, в якій стільки кофеїну, що можна було б вбити цілу станцію."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 100
        reagents:
        - ReagentId: EnergyDrink
          Quantity: 100
  - type: Drink
  - type: Label
    currentLabel: red bool

- type: entity
  parent: DrinkBottlePlasticBaseFull
  id: CustomDrinkJug
  name: "колба напоїв"
  description: "Колба для зберігання напоїв на замовлення."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 150
  - type: Drink
