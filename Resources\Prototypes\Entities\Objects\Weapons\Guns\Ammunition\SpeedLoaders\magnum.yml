- type: entity
  id: BaseSpeedLoaderMagnum
  name: "швидкісний зарядник (.45 magnum)"
  parent: BaseItem
  abstract: true
  components:
  - type: Tag
    tags:
      - SpeedLoaderMagnum
  - type: SpeedLoader
  - type: BallisticAmmoProvider
    whitelist:
      tags:
        - CartridgeMagnum
    capacity: 6
  - type: Sprite
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
        ents: []

- type: entity
  id: SpeedLoaderMagnum
  name: "швидкісний зарядник (.45 magnum)"
  parent: BaseSpeedLoaderMagnum
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnum
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/SpeedLoaders/Magnum/magnum_speed_loader.rsi
    layers:
      - state: base
        map: [ "enum.GunVisualLayers.Base" ]
      - state: base-6
        map: [ "enum.GunVisualLayers.Mag" ]
  - type: MagazineVisuals
    magState: base
    steps: 7
    zeroVisible: false
  - type: Appearance

- type: entity
  id: SpeedLoaderMagnumEmpty
  name: "швидкозаряджач (.45 магнум будь-який)"
  parent: SpeedLoaderMagnum
  components:
  - type: BallisticAmmoProvider
    proto: null
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/SpeedLoaders/Magnum/magnum_speed_loader.rsi
    layers:
      - state: base
        map: [ "enum.GunVisualLayers.Base" ]

- type: entity
  id: SpeedLoaderMagnumIncendiary
  name: "швидкозаряджач (.45 магнум запальний)"
  parent: SpeedLoaderMagnum
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumIncendiary

- type: entity
  id: SpeedLoaderMagnumPractice
  name: "швидкісний зарядник (.45 magnum practice)"
  parent: BaseSpeedLoaderMagnum
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumPractice
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/SpeedLoaders/Magnum/magnum_speed_loader.rsi
    layers:
      - state: base
        map: [ "enum.GunVisualLayers.Base" ]
      - state: practice-6
        map: [ "enum.GunVisualLayers.Mag" ]
  - type: MagazineVisuals
    magState: practice
    steps: 7
    zeroVisible: false
  - type: Appearance

- type: entity
  id: SpeedLoaderMagnumRubber
  name: "швидкозаряджач (.45 магнум гумовий)"
  parent: BaseSpeedLoaderMagnum
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumRubber
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/SpeedLoaders/Magnum/magnum_speed_loader.rsi
    layers:
      - state: base
        map: [ "enum.GunVisualLayers.Base" ]
      - state: rubber-6
        map: [ "enum.GunVisualLayers.Mag" ]
  - type: MagazineVisuals
    magState: rubber
    steps: 7
    zeroVisible: false
  - type: Appearance

- type: entity
  id: SpeedLoaderMagnumAP
  name: "швидкісний зарядник (.45 magnum бронебійний)"
  parent: BaseSpeedLoaderMagnum
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumAP
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/SpeedLoaders/Magnum/magnum_speed_loader.rsi
    layers:
      - state: base
        map: [ "enum.GunVisualLayers.Base" ]
      - state: piercing-6
        map: [ "enum.GunVisualLayers.Mag" ]
  - type: MagazineVisuals
    magState: piercing
    steps: 7
    zeroVisible: false
  - type: Appearance

- type: entity
  id: SpeedLoaderMagnumUranium
  name: "швидкісний зарядник (.45 magnum uranium)"
  parent: BaseSpeedLoaderMagnum
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumUranium
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/SpeedLoaders/Magnum/magnum_speed_loader.rsi
    layers:
      - state: base
        map: [ "enum.GunVisualLayers.Base" ]
      - state: uranium-6
        map: [ "enum.GunVisualLayers.Mag" ]
  - type: MagazineVisuals
    magState: uranium
    steps: 7
    zeroVisible: false
  - type: Appearance

- type: entity
  id: SpeedLoaderMagnumShrapnel
  name: "спідлоадер (.45 магнум, шрапнельний)"
  parent: SpeedLoaderMagnum
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumShrapnel
