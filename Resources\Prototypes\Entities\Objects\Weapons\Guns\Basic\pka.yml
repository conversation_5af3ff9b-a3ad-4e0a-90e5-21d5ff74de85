- type: entity
  name: "протокінетичний прискорювач"
  id: WeaponProtoKineticAccelerator
  parent: WeaponProtoKineticAcceleratorBase
  description: "Стріляє кінетичними болтами з малою шкодою на невеликій відстані."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Basic/kinetic_accelerator.rsi
    layers:
    - state: icon
      map: [ "enum.WeaponVisualLayers.Base" ]
    - state: bayonet
      map: [ "enum.WeaponVisualLayers.Bayonet" ]
      visible: false
    - state: flight
      map: [ "enum.WeaponVisualLayers.FlightOff" ]
      visible: false
    - state: flight-on
      map: [ "enum.WeaponVisualLayers.FlightOn" ]
      visible: false
    - state: animation-icon
      visible: false
      map: [ "empty-icon" ]
  - type: ExtendDescription
    descriptionList:
      - description: "gun-legality-salvage"
        fontSize: 12
        color: "#ff4f00"
        requireDetailRange: false
  # todo: add itemcomponent with inhandVisuals states using unused texture and animation assets in kinetic_accelerator.rsi
  # todo: add clothingcomponent with clothingVisuals states using unused texture and animations assets in kinetic_accelerator.rsi
  - type: StaticPrice
    price: 270
  # - type: Construction #Frontier # Sawn PKA is temporarily disabled because its not setup for PKA customization.
  #   graph: PKASawn #Frontier
  #   node: start #Frontier
  #   deconstructionTarget: null #Frontier
  - type: Wieldable # Frontier
