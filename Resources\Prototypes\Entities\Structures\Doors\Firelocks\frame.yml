﻿- type: entity
  id: FirelockFrame
  parent: BaseStructureDynamic
  name: "рама вогневого шлюзу"
  description: "Це рама вогневого шлюзу"
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Standard/firelock.rsi
    layers:
    - state: frame1
      map: [ "enum.ConstructionVisuals.Layer" ]
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ConstructionVisuals.Key:
        enum.ConstructionVisuals.Layer:
          frame1: { state: frame1 }
          frame2: { state: frame2 }
          frame3: { state: frame3 }
          frame4: { state: frame4 }
  - type: Construction
    graph: Firelock
    node: frame1
  - type: InteractionOutline
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: RCDDeconstructable
    cost: 4
    delay: 6
    fx: EffectRCDDeconstruct6    
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 500
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Physics
    bodyType: Dynamic
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.45"
        density: 55
        mask:
        - Impassable
        - HighImpassable
        layer:
        - HighImpassable
        - MidImpassable
  - type: Transform
    noRot: true
