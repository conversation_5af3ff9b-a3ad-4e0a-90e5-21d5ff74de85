- type: entity
  id: Girder
  parent: BaseStructureDynamic
  name: "балка"
  description: "Великий конструктивний вузол з металу."
  components:
  - type: Transform
    anchored: true
    noRot: false
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.45"
        density: 60
        mask:
        - MachineMask
        layer:
        - GlassAirlockLayer
  - type: InteractionOutline
  - type: Construction
    graph: Girder
    node: girder
  - type: Sprite
    sprite: Structures/Walls/solid.rsi
    state: wall_girder
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors: #excess damage, don't spawn entities.
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
  - type: StaticPrice
    price: 10

- type: entity
  id: ReinforcedGirder
  parent: Girder
  name: "посилена балка"
  description: "Великий конструктивний вузол, виготовлений з металу та сталі."
  components:
    - type: Sprite
      sprite: Structures/Walls/solid.rsi
      state: reinforced_wall_girder
    - type: Construction
      graph: Girder
      node: reinforcedGirder
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 200
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
            - !type:PlaySoundBehavior
              sound:
                collection: MetalBreak
        - trigger:
            !type:DamageTrigger
            damage: 50
          behaviors:
            - !type:SpawnEntitiesBehavior
              spawn:
                SheetSteel1:
                  min: 1
                  max: 1
                SheetPlasteel1:
                  min: 1
                  max: 1
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
            - !type:PlaySoundBehavior
              sound:
                collection: MetalBreak
    - type: StaticPrice
      price: 66

- type: entity
  id: ClockworkGirder
  parent: ReinforcedGirder
  name: "настінне обладнання"
  description: "Велика шестерня з монтажними кронштейнами для додаткового покриття."
  components:
    - type: Sprite
      sprite: Structures/Walls/clock.rsi
      state: wall_gear
    - type: Construction
      graph: ClockworkGirder
      node: clockGirder
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 200
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
            - !type:PlaySoundBehavior
              sound:
                collection: MetalBreak
        - trigger:
            !type:DamageTrigger
            damage: 50
          behaviors:
            - !type:SpawnEntitiesBehavior
              spawn:
                SheetBrass1:
                  min: 1
                  max: 2
                PartRodMetal1:
                  min: 1
                  max: 2
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
            - !type:PlaySoundBehavior
              sound:
                collection: MetalBreak