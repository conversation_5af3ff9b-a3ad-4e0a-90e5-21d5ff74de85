- type: entity
  name: "косм<PERSON>чний кліщ"
  id: MobTick
  parent: SimpleSpaceMobBase
  description: "Це космічний кліщ, остерігайтеся його неприємних укусів. ЦентКом нагадує, що 90 відсотків ампутацій ніг у логістів відбуваються саме через укуси космічних кліщів." # DeltaV - Logistics Department replacing Cargo
  components:
  - type: VentCrawler # goobstation - Ventcrawl
  - type: InputMover
  - type: MobMover
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
  - type: NpcFactionMember
    factions:
    - SimpleHostile
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: alive
      sprite: Mobs/Aliens/Xenos/spacetick.rsi
      scale: 0.8, 0.8
  - type: Physics
  - type: MovementSpeedModifier
    baseWalkSpeed : 4
    baseSprintSpeed : 6
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.20
        density: 20
        mask:
        - SmallMobMask
        layer:
        - SmallMobLayer
  - type: MobState
    allowedStates:
      - Alive
      - Dead
  - type: MobThresholds
    thresholds:
      0: Alive
      15: Dead
  - type: Stamina
    critThreshold: 15
  - type: MovementAlwaysTouching
  - type: DamageStateVisuals
    states:
      Alive:
        Base: alive
      Dead:
        Base: dead
  - type: Butcherable
    spawned:
    - id: FoodMeatXeno
      amount: 1
  - type: Bloodstream
    bloodMaxVolume: 50
  - type: CombatMode
  - type: MeleeWeapon
    soundHit:
        path: /Audio/Effects/bite.ogg
    angle: 0
    animation: WeaponArcBite
    damage:
      groups:
        Brute: 2
  - type: SolutionContainerManager
    solutions:
      melee:
        reagents:
        - ReagentId: Toxin
          Quantity: 5
  - type: MeleeChemicalInjector
    solution: melee
  - type: ReplacementAccent
    accent: genericAggressive
  - type: Speech
    speechVerb: SmallMob
  - type: NonSpreaderZombie

- type: entity
  id: MobTickSalvage
  parent: MobTick
  suffix: "Salvage Ruleset"
  components:
  - type: SalvageMobRestrictions

