- type: entity
  id: PartDiona
  parent: [BaseItem, BasePart]
  name: "частина тіла діони"
  abstract: true
  components:
  - type: Sprite
    sprite: Mobs/Species/Diona/parts.rsi

- type: entity
  id: TorsoDiona
  name: "торс діони"
  parent: [PartDiona, BaseTorso]
  components:
  - type: Sprite
    state: "torso_m"

- type: entity
  id: HeadDiona
  name: "голова діони"
  parent: [PartDiona, BaseHead]
  components:
  - type: Sprite
    state: "head_m"

- type: entity
  id: LeftArmDiona
  name: "ліва рука діони"
  parent: [PartDiona, BaseLeftArm]
  components:
  - type: Sprite
    state: "l_arm"

- type: entity
  id: RightArmDiona
  name: "права рука діони"
  parent: [PartDiona, BaseRightArm]
  components:
  - type: Sprite
    state: "r_arm"

- type: entity
  id: LeftHandDiona
  name: "ліва долоня діони"
  parent: [PartDiona, BaseLeftHand]
  components:
  - type: Sprite
    state: "l_hand"

- type: entity
  id: RightHandDiona
  name: "права долоня діони"
  parent: [PartDiona, BaseRightHand]
  components:
  - type: Sprite
    state: "r_hand"

- type: entity
  id: LeftLegDiona
  name: "ліва нога діони"
  parent: [PartDiona, BaseLeftLeg]
  components:
  - type: Sprite
    state: "l_leg"

- type: entity
  id: RightLegDiona
  name: "права нога діони"
  parent: [PartDiona, BaseRightLeg]
  components:
  - type: Sprite
    state: "r_leg"
  - type: BodyPart
    partType: Leg
    symmetry: Right

- type: entity
  id: LeftFootDiona
  name: "ліва стопа діони"
  parent: [PartDiona, BaseLeftFoot]
  components:
  - type: Sprite
    state: "l_foot"
  - type: BodyPart
    partType: Foot
    symmetry: Left

- type: entity
  id: RightFootDiona
  name: "права стопа діони"
  parent: [PartDiona, BaseRightFoot]
  components:
  - type: Sprite
    state: "r_foot"
  - type: BodyPart
    partType: Foot
    symmetry: Right
