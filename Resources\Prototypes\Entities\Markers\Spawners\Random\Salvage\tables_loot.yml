# Scrap: Worthless items that can be recycled into materials

- type: entityTable
  id: SalvageScrapLowValue
  table: !type:GroupSelector
    children:
    - id: ScrapAirlock2
    - id: ScrapBucket
    - id: ScrapCamera
    - id: ScrapCanister1
    - id: ScrapCanister2
    - id: ScrapFaxMachine
    - id: ScrapFireExtinguisher
    - id: ScrapFirelock3
    - id: ScrapIntercom
    - id: ScrapMedkit
    - !type:GroupSelector
      children:
      - id: ScrapPAI
      - id: ScrapPAIGold
        weight: 0.1
    - id: ScrapTube
    - id: ShardGlass
      amount: !type:RangeNumberSelector
        range: 1, 3
    - id: SheetSteel1
      weight: 0.5
      amount: !type:RangeNumberSelector
        range: 1, 3
    - id: SheetPlastic1
      weight: 0.5
      amount: !type:RangeNumberSelector
        range: 1, 3
    - id: PartRodMetal1
      weight: 0.5
      amount: !type:RangeNumberSelector
        range: 1, 3

- type: entityTable
  id: SalvageScrapHighValue
  table: !type:GroupSelector
    children:
    - id: ScrapSteel
      weight: 2
    - id: ScrapGlass
      weight: 2
    # - id: ScrapPlastic TODO: high-value plastic scrap.
    #  weight: 2
    - id: SheetSteel10
    - id: SheetGlass10
    - id: SheetPlastic10
    - id: ScrapGeneratorFuelTank
      weight: 0.5
    - id: PartRodMetal10
      weight: 0.33
    - id: MaterialWoodPlank10
      weight: 0.2
    - id: SheetPlasma10
      weight: 0.2
    - id: ArtifactFragment1
      weight: 0.05
    - id: SheetPlasteel10
      weight: 0.05

- type: entityTable
  id: SalvageScrapLarge
  table: !type:GroupSelector
    children:
    - !type:NestedSelector
      tableId: RandomGeneratorTable
      weight: 2
    - id: ScrapAirlock1
    - id: ScrapCloset
    - id: ScrapFirelock1
    - id: ScrapFirelock2
    - id: ScrapJetpack
      weight: 0.5
    - id: ScrapMopBucket
      weight: 0.5

# Treasure: High-value scatterables that don't do a ton.

- type: entityTable
  id: SalvageTreasureCommon
  table: !type:GroupSelector
    children:
    - id: SheetPlasma1
      amount: !type:RangeNumberSelector
        range: 3, 5
    - id: ResearchDisk5000
    - id: DrinkGoldenCup
    - id: TreasureSampleTube
    - !type:NestedSelector
      tableId: TreasureCoinPile
      rolls: !type:RangeNumberSelector
        range: 1, 2

- type: entityTable
  id: SalvageTreasureUncommon
  table: !type:GroupSelector
    children:
    - id: IngotSilver1
      amount: !type:RangeNumberSelector
        range: 1, 5
    - id: IngotGold1
      amount: !type:RangeNumberSelector
        range: 3, 5
    - id: ResearchDisk10000
    - id: TreasureDatadiskEncrypted
    - !type:GroupSelector
      children:
      - id: TreasureCDDrive
      - id: TreasureHardDiskDrive
      - id: TreasureFlopDiskDrive
    - !type:NestedSelector
      tableId: TreasureCoinPile
      rolls: !type:RangeNumberSelector
        range: 4, 7
    - id: SpaceCash5000
    - id: WristwatchGold
    - !type:NestedSelector
      tableId: RingTableCommon

- type: entityTable
  id: SalvageTreasureRare
  table: !type:GroupSelector
    children:
    - id: MaterialDiamond1
    - id: TreasureCPUSupercharged
    - id: TechnologyDiskRare
      weight: 0.5
    - id: ResearchDisk20000
      weight: 0.5
    - !type:NestedSelector
      tableId: TreasureCoinPileRare
      rolls: !type:RangeNumberSelector
        range: 2, 5
    - !type:AllSelector
      children:
      - id: BriefcaseBrown
      - !type:GroupSelector
        rolls: !type:RangeNumberSelector
          range: 3, 5
        children:
        - id: SpaceCash10000
          weight: 60
        - id: SpaceCash20000
          weight: 25
        - id: SpaceCash30000
          weight: 14
        - id: SpaceCash50000
          weight: 1
    - !type:NestedSelector
      tableId: RingTableRare

- type: entityTable
  id: SalvageTreasureLegendary
  table: !type:GroupSelector
    children:
    - id: ClothingMaskGoldenCursed
    - id: ClothingHeadHatFancyCrown
    - id: ResearchDisk50000
    - id: GoldenBikeHorn
    - id: ClothingHeadHatCatEars
    - id: TreasureCoinDiamond
      amount: !type:RangeNumberSelector
        range: 2, 5
    - !type:NestedSelector
      tableId: RingTableRare
      rolls: !type:RangeNumberSelector
        range: 2, 3
    - id: SpaceCash100000

# Equipment: Tools and things used by salvagers. Quote unquote "Gamer Loot"

- type: entityTable
  id: SalvageEquipmentCommon
  table: !type:GroupSelector
    children:
    - id: Flare
    - id: Crowbar
    - id: Pickaxe
    - id: ClothingMaskGas
    - !type:GroupSelector
      children:
      - id: Wirecutter
      - id: Screwdriver
      - id: Wrench
      - id: Welder
        weight: 0.5
    - id: Shovel
    - id: FlashlightLantern
    - id: FireExtinguisher
    - id: SurvivalKnife

- type: entityTable
  id: SalvageEquipmentUncommon
  table: !type:GroupSelector
    children:
    - id: OreBag
    - id: HandheldGPSBasic
    - id: PowerCellHighPrinted
    - id: RadioHandheld
    - id: ClothingBeltUtility
    - id: WeaponProtoKineticAccelerator
      weight: 0.5
    - id: OxygenTankFilled
    - id: WelderIndustrial
 #   - id: WeaponGrapplingGun
    - !type:GroupSelector
      children:
      - id: ClothingHeadHatWelding
      - id: ClothingHeadHatWeldingMaskFlame
        weight: 0.25
      - id: ClothingHeadHatWeldingMaskFlameBlue
        weight: 0.25
      - id: ClothingHeadHatWeldingMaskPainted
        weight: 0.1

- type: entityTable
  id: SalvageEquipmentRare
  table: !type:GroupSelector
    children:
#    - id: BlueprintDoubleEmergencyTank
    - id: FultonBeacon
    - id: Fulton
      amount: !type:RangeNumberSelector
        range: 1, 3
    #- id: HandHeldMassScanner - Removed by Lavaland
    - id: WeaponCrusherDagger
    - id: MiningDrill
    - id: ClothingEyesGlassesMeson
    - id: ClothingBeltSalvageWebbing
    - id: SeismicCharge
#    - id: MineralScanner
      weight: 0.5
    - id: WeaponCrusher
      weight: 0.5
    - !type:GroupSelector
      children:
      - id: JetpackBlue
      - id: JetpackBlack

- type: entityTable
  id: SalvageEquipmentLegendary
  table: !type:GroupSelector
    children:
#    - id: AdvancedMineralScanner
#    - id: BlueprintFulton
#    - id: BlueprintSeismicCharge
    - id: WeaponCrusherGlaive
    - id: ClothingOuterHardsuitSalvage
    - id: OmnizineChemistryBottle
    - !type:GroupSelector
      children:
      - id: JetpackBlueFilled
      - id: JetpackBlackFilled

- type: entityTable
  id: RandomGeneratorTable
  table: !type:GroupSelector
    children:
    - id: ScrapGeneratorPlasmaLeaking
    - id: ScrapGeneratorUraniumLeaking
    - id: ScrapGeneratorPlasma
      weight: 0.5
    - id: ScrapGeneratorUranium
      weight: 0.5
    - id: ScrapGeneratorFrame
      weight: 0.25
