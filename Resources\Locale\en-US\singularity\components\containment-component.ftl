﻿comp-containment-turned-on = The containment field generator boots up.
comp-containment-turned-off = The containment field generator shuts down.

comp-containment-on = It's switched [color=green]on[/color], ready to generate a connection.
comp-containment-off = It's switched [color=red]off[/color].

comp-containment-connected = The containment field generator shoots out a light as it establishes a connection!
comp-containment-disconnected = The containment field generator light fades away, severing the connection.

comp-containment-anchor-warning = You cannot unanchor the containment field generator while it's on or connected!
comp-containment-toggle-warning = You cannot turn the generator off while it's connected.

comp-field-vaporized = The {$entity} hits the field and vaporizes into nothing!
