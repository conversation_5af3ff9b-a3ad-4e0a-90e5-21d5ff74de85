- type: entity
  name: "Базова зброяЛегкий кулемет"
  parent: BaseItem
  id: BaseWeaponLightMachineGun
  description: "Корінцевий зубастий і пагонистий."
  abstract: true
  components:
  - type: Sprite
  - type: Item
    size: Huge
  - type: Clothing
    sprite: Objects/Weapons/Guns/LMGs/l6.rsi
    quickEquip: false
    slots:
    - Back
  - type: Wieldable
    unwieldOnUse: false
  - type: GunRequiresWield
  - type: GunWieldBonus
    minAngle: -20
    maxAngle: -20
  - type: Gun
    minAngle: 24
    maxAngle: 45
    angleIncrease: 4
    angleDecay: 16
    fireRate: 8
    selectedMode: FullAuto
    availableModes:
      - FullAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/lmg.ogg
    soundEmpty:
      path: /Audio/Weapons/Guns/Empty/lmg_empty.ogg
  - type: ChamberMagazineAmmoProvider
    soundRack:
      path: /Audio/Weapons/Guns/Cock/lmg_cock.ogg
  - type: AmmoCounter
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        startingItem: MagazineLightRifleBox
        insertSound: /Audio/Weapons/Guns/MagIn/batrifle_magin.ogg
        ejectSound: /Audio/Weapons/Guns/MagOut/batrifle_magout.ogg
        priority: 2
        whitelist:
          tags:
            - MagazineLightRifleBox
      gun_chamber:
        name: Chamber
        startingItem: CartridgeLightRifle
        priority: 1
        whitelist:
          tags:
            - CartridgeLightRifle
  - type: ContainerContainer
    containers:
      gun_magazine: !type:ContainerSlot
      gun_chamber: !type:ContainerSlot
  - type: StaticPrice
    price: 500
  - type: UseDelay
    delay: 1
  - type: Execution
  - type: MeleeWeapon
    attackRate: 1.4
    damage:
      types:
        Blunt: 11
    bluntStaminaDamageFactor: 1.3333
    swapKeys: true
    disableHeavy: true
    animation: WeaponArcThrust
    wideAnimationRotation: 180
    soundHit:
      collection: MetalThud
  - type: IncreaseDamageOnWield
    damage:
      types:
        Blunt: 3
  - type: DamageOtherOnHit
    staminaCost: 12
  # PIRATE START
  - type: CanHoldAccessories
    whiteListTags:
      - CombatKnife
  - type: CanTakeAim
  # PIRATE END

- type: entity
  name: "L6 SAW"
  id: WeaponLightMachineGunL6
  parent: BaseWeaponLightMachineGun
  description: "Досить традиційно виконаний LMG з приємною лакованою дерев'яною пістолетною рукояткою. Використовує гвинтівкові набої калібру .30."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/LMGs/l6.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-3
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 4
    zeroVisible: true
  - type: Appearance

- type: entity
  name: "L6C ROW"
  id: WeaponLightMachineGunL6C
  parent: BaseItem
  description: "L6 SAW для використання кіборгами. Створює гвинтівкові боєприпаси калібру .30 \"на льоту\" із вбудованої установки, яка повільно самозаряджається."
  components:
    - type: Gun
      minAngle: 4
      maxAngle: 25
      angleIncrease: 4
      angleDecay: 16
      fireRate: 8
      selectedMode: FullAuto
      availableModes:
        - FullAuto
      soundGunshot:
        path: /Audio/Weapons/Guns/Gunshots/lmg.ogg
      soundEmpty:
        path: /Audio/Weapons/Guns/Empty/lmg_empty.ogg
    - type: Sprite
      sprite: Objects/Weapons/Guns/LMGs/l6.rsi
      layers:
        - state: base
          map: [ "enum.GunVisualLayers.Base" ]
        - state: mag-3
          map: [ "enum.GunVisualLayers.Mag" ]
    - type: Item
      size: Huge
    - type: ContainerContainer
      containers:
        ballistic-ammo: !type:Container
    - type: ProjectileBatteryAmmoProvider
      proto: CartridgeLightRifle
      fireCost: 100
    - type: Battery
      maxCharge: 10000
      startingCharge: 10000
    - type: BatterySelfRecharger
      autoRecharge: true
      autoRechargeRate: 25
    - type: AmmoCounter
