- type: entity
  id: Fireplace
  parent: BaseStructure
  name: "камін"
  description: "Місце, де є вогонь. Затишно!"
  components:
  - type: Sprite
    sprite: Structures/Decoration/fireplace.rsi
    layers:
      - state: fireplace
      - state: fireplacefestive
        map: ["christmas"]
        visible: false
      - state: fireplace_fire4
        shader: unshaded
      - state: fireplace_glow
        shader: unshaded
  - type: Appearance
  - type: HolidayVisuals
    holidays:
      festive:
      - FestiveSeason
  - type: GenericVisualizer
    visuals:
      enum.HolidayVisuals.Holiday:
        christmas:
          festive: { visible: true }
  - type: AmbientSound
    volume: -6
    range: 5
    sound:
      path: /Audio/Ambience/Objects/fireplace.ogg
  - type: PointLight
    radius: 3
    energy: 3
    color: "#FF6F00"
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 200
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
  - type: AlwaysHot
  - type: Construction # Frontier
    graph: FiresGraph # Frontier
    node: FireplaceNode # Frontier 
