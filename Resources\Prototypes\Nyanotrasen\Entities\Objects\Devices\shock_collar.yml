- type: entity
  parent: ClothingNeckBase
  id: ShockCollar
  name: "шоковий нашийник"
  description: "Шокуюче." # DeltaV: sprite is fine
  components:
  - type: Sprite
    sprite: Nyanotrasen/Clothing/Neck/Misc/shock.rsi
  - type: Clothing
    sprite: Nyanotrasen/Clothing/Neck/Misc/shock.rsi
    equipDelay: 1
    unequipDelay: 10 # It's a collar meant to be used on prisoners (or not), so it probably has some sort of safety.
  - type: ShockCollar
  - type: UseDelay
    delay: 3 # DeltaV: prevent clocks instakilling people
  - type: TriggerOnSignal
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: BasicDevice
  - type: WirelessNetworkConnection
    range: 200
  - type: DeviceLinkSink
    ports:
      - Trigger
