﻿using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Administration.UI.BanList;

[GenerateTypedNameReferences]
public sealed partial class BanListIdsPopup : Popup
{
    public BanListIdsPopup(string? id, string? ip, string? hwid, string? guid)
    {
        RobustXamlLoader.Load(this);

        ID.Text = id;
        IP.Text = ip;
        HWId.Text = hwid;
        GUID.Text = guid;

        UserInterfaceManager.ModalRoot.AddChild(this);
    }
}
