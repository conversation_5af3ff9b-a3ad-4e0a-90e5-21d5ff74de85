- type: entity
  id: BinglePit
  name: "яма Бінгла"
  description: "Виглядає голодною"
  placement:
    mode: SnapgridCenter
  components:
  - type: BinglePit
  - type: StepTrigger
    requiredTriggeredSpeed: 0
    intersectRatio: 0.4
    triggerGroups:
      types:
      - Chasm
  - type: Transform
    anchored: true
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.5,-0.5,0.5,0.5"
        layer:
        - WallLayer
        mask:
        - ItemMask
        density: 1000
        hard: false
  - type: Sprite
    drawdepth: FloorTiles
    layers:
    - sprite: _Goobstation/Mobs/Bingle/bingle.rsi
      state: pit
  - type: InteractionOutline
  - type: Clickable
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 600
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: EmitSoundOnSpawn
    sound:
      path: /Audio/Weapons/Guns/Gunshots/rocket_launcher.ogg
  - type: RequireProjectileTarget
  - type: SolutionContainerManager
    solutions:
      drainBuffer:
        maxVol: 1000
  - type: Drain # remove liquids around pit
    unitsPerSecond: 10
    unitsDestroyedPerSecond: 10
  - type: PassiveDamage #passive Healing
    damage:
      types:
        Heat: -1
        Shock: -1
        Structural: -1
      groups:
        Brute: -1
  - type: AntiRottingContainer
