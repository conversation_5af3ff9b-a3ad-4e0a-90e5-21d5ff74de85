- type: entity
  id: BaseThruster
  parent: BaseStructureDynamic
  name: "двигун"
  description: "<PERSON>в<PERSON><PERSON><PERSON><PERSON>, який дозволяє шатлу рухатися."
  abstract: true
  components:
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.45,-0.45,0.45,0.45"
          density: 60
          mask:
          - MachineMask
          layer:
          - MachineLayer
    - type: AmbientSound
      enabled: false
      range: 4
      volume: -4
      sound:
        path: /Audio/Effects/shuttle_thruster.ogg
    - type: Transform
      anchored: true
    - type: Physics
      bodyType: Static
    - type: Rotatable
      rotateWhileAnchored: true
    - type: Thruster
      damage:
        types:
          Heat: 40
    - type: InteractionOutline
    - type: Sprite
    - type: Appearance
    - type: ThrusterVisuals
    - type: ApcPowerReceiver
      powerLoad: 1500
    - type: ExtensionCableReceiver
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: Electronic
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 100  # Considering we need a lot of thrusters didn't want to make an individual one too tanky
        behaviors:
          - !type:DoActsBehavior
            acts: ["Destruction"]
          - !type:PlaySoundBehavior
            sound:
              collection: MetalBreak
    - type: StaticPrice
      price: 300
  placement:
    mode: SnapgridCenter

- type: entity
  id: Thruster
  name: "двигун"
  parent: [ BaseThruster, ConstructibleMachine ]
  components:
  - type: Machine
    board: ThrusterMachineCircuitboard
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
        - !type:DoActsBehavior
          acts: ["Destruction"]
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
        - !type:DoActsBehavior
          acts: ["Destruction"]
        - !type:PlaySoundBehavior
          sound:
            collection: MetalBreak
        - !type:ChangeConstructionNodeBehavior
          node: machineFrame
  - type: Sprite
    sprite: Structures/Shuttles/thruster.rsi
    layers:
    - state: base
      map: ["enum.ThrusterVisualLayers.Base"]
    - state: thrust
      map: ["enum.ThrusterVisualLayers.ThrustOn"]
      shader: unshaded
      visible: false
    - state: thrust_burn_unshaded
      map: ["enum.ThrusterVisualLayers.ThrustingUnshaded"]
      shader: unshaded
      visible: false
      offset: 0, 1

- type: entity
  id: ThrusterUnanchored
  parent: Thruster
  suffix: Unanchored
  components:
    - type: Transform
      anchored: false
    - type: Physics
      bodyType: Dynamic

- type: entity
  id: DebugThruster
  parent: BaseThruster
  suffix: DEBUG
  components:
  - type: Thruster
    requireSpace: false
  - type: ApcPowerReceiver
    needsPower: false
    powerLoad: 0
  - type: Sprite
    sprite: Structures/Shuttles/thruster.rsi
    layers:
    - state: base
      map: ["enum.ThrusterVisualLayers.Base"]
    - state: thrust
      map: ["enum.ThrusterVisualLayers.ThrustOn"]
      shader: unshaded
      visible: false
    - state: thrust_burn_unshaded
      map: ["enum.ThrusterVisualLayers.ThrustingUnshaded"]
      shader: unshaded
      visible: false
      offset: 0, 1

- type: entity
  id: Gyroscope
  parent: [ BaseThruster, ConstructibleMachine ]
  name: "гіроскоп"
  description: "Збільшує потенційний кутовий поворот шатла."
  components:
  - type: Thruster
    thrusterType: Angular
    requireSpace: false
    baseThrust: 2000
    thrust: 2000
    machinePartThrust: Manipulator
  - type: Sprite
    # Listen I'm not the biggest fan of the sprite but it was the most appropriate thing I could find.
    sprite: Structures/Shuttles/gyroscope.rsi
    snapCardinals: true
    layers:
    - state: base
      map: ["enum.ThrusterVisualLayers.Base"]
    - state: thrust
      map: ["enum.ThrusterVisualLayers.ThrustOn"]
      shader: unshaded
      visible: false
    - state: thrust_burn
      map: [ "enum.ThrusterVisualLayers.Thrusting" ]
      visible: false
    - state: thrust_burn_unshaded
      map: ["enum.ThrusterVisualLayers.ThrustingUnshaded"]
      shader: unshaded
      visible: false
  - type: PointLight
    radius: 1.3
    energy: 0.8
    enabled: false
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
    offset: "0, 0.1" # shine from the top, not bottom of the computer
    color: "#4246b3"
  - type: Machine
    board: GyroscopeMachineCircuitboard
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
        - !type:DoActsBehavior
          acts: ["Destruction"]
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
        - !type:DoActsBehavior
          acts: ["Destruction"]
        - !type:PlaySoundBehavior
          sound:
            collection: MetalBreak
        - !type:ChangeConstructionNodeBehavior
          node: machineFrame
  - type: UpgradePowerDraw
    powerDrawMultiplier: 0.75
    scaling: Exponential
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Electronic
  - type: StaticPrice
    price: 2000

- type: entity
  id: GyroscopeUnanchored
  parent: Gyroscope
  suffix: Unanchored
  components:
    - type: Transform
      anchored: false
    - type: Physics
      bodyType: Dynamic

- type: entity
  id: DebugGyroscope
  parent: BaseThruster
  suffix: DEBUG
  components:
  - type: Thruster
    thrusterType: Angular
    requireSpace: false
    baseThrust: 100
    thrust: 100
  - type: ApcPowerReceiver
    needsPower: false
    powerLoad: 0
  - type: Sprite
    sprite: Structures/Shuttles/gyroscope.rsi
    snapCardinals: true
    layers:
    - state: base
      map: ["enum.ThrusterVisualLayers.Base"]
    - state: thrust
      map: ["enum.ThrusterVisualLayers.ThrustOn"]
      shader: unshaded
      visible: false
    - state: thrust_burn_unshaded
      map: ["enum.ThrusterVisualLayers.ThrustingUnshaded"]
      shader: unshaded
      visible: false
