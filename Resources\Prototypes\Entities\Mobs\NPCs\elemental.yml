- type: entity
  abstract: true
  id: MobElementalBase
  components:
  - type: LagCompensation
  - type: Reactive
    groups:
      Acidic: [Touch]
  - type: Clickable
  - type: Damageable
    damageContainer: Inorganic
  - type: InteractionOutline
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 50
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: MovementSpeedModifier
    baseWalkSpeed : 2
    baseSprintSpeed : 3
  - type: Sprite
    noRot: true
    drawdepth: Mobs
  - type: NpcFactionMember
    factions:
    - SimpleNeutral
  - type: MovedByPressure
  - type: Physics
    bodyType: KinematicController # Same for all inheritors
  - type: StatusEffects
    allowed:
    - Stun
    - KnockedDown
    - SlowedDown
    - Stutter
    - Electrocution
  - type: Pullable
  - type: Tag
    tags:
    - DoorBumpOpener
  - type: MobState
    allowedStates:
      - Alive
      - Dead
  - type: MobThresholds
    thresholds:
      0: Alive
      120: Dead
  - type: Stamina
    critThreshold: 120
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:TriggerBehavior
    - trigger:
        !type:DamageTrigger
        damage: 120
      behaviors:
      - !type:DoActs<PERSON>ehavior
        acts: [ "Destruction" ]
  - type: Input
    context: "human"
  - type: InputMover
  - type: MobMover
  - type: ZombieImmune
  - type: ClothingRequiredStepTriggerImmune
    slots: All
  - type: Dispellable
  - type: Fauna # Lavaland Change

- type: entity
  abstract: true
  id: MobOreCrab
  name: "рудний краб"
  parent: [ MobElementalBase, MobCombat ]
  components:
  - type: Sprite
    sprite: Mobs/Elemental/orecrab.rsi
    state: uranium_crab
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
  - type: MeleeWeapon
    hidden: true
    soundHit:
      path: /Audio/Weapons/Xeno/alien_claw_flesh3.ogg
    damage:
      types:
        Blunt: 6
  - type: MovementSpeedModifier
    baseWalkSpeed : 2
    baseSprintSpeed : 2.5
  - type: NpcFactionMember
    factions:
      - SimpleHostile
  - type: Damageable
    damageContainer: StructuralInorganic
  - type: Psionic
    removable: false
    roller: false
    assayFeedback:
      - orecrab-feedback
  - type: InnatePsionicPowers
    powersToAdd:
      - TelepathyPower

- type: entity
  parent: MobOreCrab
  id: MobQuartzCrab
  description: "Рудний краб з кварцу."
  components:
  - type: Sprite
    state: quartz_crab
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 40
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          SpaceQuartz1:
            min: 2
            max: 4
      - !type:DoActsBehavior
        acts: [ "Destruction" ]

- type: entity
  parent: MobOreCrab
  id: MobIronCrab
  description: "Рудний краб із заліза."
  components:
  - type: Sprite
    state: iron_crab
  - type: MeleeWeapon
    damage:
      types:
        Blunt: 5
  - type: MovementSpeedModifier
    baseWalkSpeed : 1.5
    baseSprintSpeed : 2
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 80
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          SteelOre1:
            min: 2
            max: 4
      - !type:DoActsBehavior
        acts: [ "Destruction" ]

- type: entity
  parent: MobOreCrab
  id: MobUraniumCrab
  description: "Рудний краб з урану."
  components:
  - type: FactionException
  - type: NPCRetaliation
    attackMemoryLength: 10
  - type: NpcFactionMember
    factions:
      - SimpleNeutral
  - type: RadiationSource
    intensity: 2
    slope: 0.3
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 80
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          UraniumOre1:
            min: 1
            max: 3
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: PointLight
    radius: 2
    energy: 3
    color: "#06DF24"

- type: entity
  parent: MobOreCrab
  id: MobSilverCrab
  name: "рудний краб"
  description: "Рудний краб зі срібла."
  components:
  - type: Sprite
    state: silver_crab
  - type: MeleeWeapon
    damage:
      types:
        Blunt: 5
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 70
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          SilverOre1:
            min: 1
            max: 3
      - !type:DoActsBehavior
        acts: [ "Destruction" ]

- type: entity
  name: "Реагентний слиз"
  id: ReagentSlime
  suffix: Water
  parent: [ MobAdultSlimes, MobCombat ]
  description: "Він складається з рідини, і вона хоче розчинити вас у собі."
  components:
  - type: GhostRole
    prob: 0
    description: ghost-role-information-angry-slimes-description
    rules: ghost-role-information-angry-slimes-rules
    mindRoles:
    - MindRoleGhostRoleTeamAntagonist
    raffle:
      settings: short
  - type: NpcFactionMember
    factions:
    - SimpleHostile
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/elemental.rsi
    layers:
      - map: [ "enum.DamageStateVisualLayers.Base" ]
        state: alive
        color: "#75b1f0"
  - type: PointLight
    radius: 2.0
    energy: 3.5
    color: "#75b1f0" # Edited through the LiquidAnomalySystem
  - type: MobState
    allowedStates:
    - Alive
    - Dead
  - type: MobThresholds
    thresholds:
      0: Alive
      50: Dead
  - type: SlowOnDamage
    speedModifierThresholds:
      20: 0.4
  - type: Bloodstream
    bloodReagent: Water
    chemicalMaxVolume: 50
  - type: StatusEffects
    allowed:
    - SlowedDown
    - Electrocution
  - type: MeleeWeapon
    soundHit:
      collection: AlienClaw
    animation: WeaponArcBite
    damage:
      types:
        Slash: 3
  - type: MeleeChemicalInjector
    solution: bloodstream
    transferAmount: 2
  - type: DamageStateVisuals
    rotate: true
    states:
      Alive:
        Base: alive
      Dead:
        Base: dead
  - type: Tag
    tags:
      - FootstepSound
      - CannotSuicide
      - DoorBumpOpener
  - type: NoSlip
  - type: ZombieImmune
  - type: ExaminableSolution
    solution: bloodstream
  - type: InjectableSolution
    solution: bloodstream
  - type: DrainableSolution
    solution: bloodstream
  - type: Psionic
    roller: false
    removable: false
    assayFeedback:
      - reagent-slime-feedback
  - type: InnatePsionicPowers
    powersToAdd:
      - TelepathyPower
  - type: Fauna # Lavaland Change

- type: entity
  name: "Спавн Реагентного Слизу"
  id: ReagentSlimeSpawner
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Mobs/Aliens/elemental.rsi
          state: alive
    - type: RandomSpawner
      prototypes:
        - ReagentSlime
        - ReagentSlimeBeer
        - ReagentSlimePax
        - ReagentSlimeNocturine
        - ReagentSlimeTHC
        - ReagentSlimeBicaridine
        - ReagentSlimeToxin
        - ReagentSlimeNapalm
        - ReagentSlimeOmnizine
        - ReagentSlimeMuteToxin
        - ReagentSlimeNorepinephricAcid
        - ReagentSlimeEphedrine
        - ReagentSlimeRobustHarvest
        - ReagentSlimeIchor
        - ReagentSlimeBleach
        - ReagentSlimeSoap
        - ReagentSlimeSpacelube
        - ReagentSlimeBuzzachloricbees
        - ReagentSlimeWehjuice
        - ReagentCognizine
        - ReagentSlimeNecrosol
        - ReagentSlimeSpaceDrugs
        - ReagentSlimeUnstableMutagen
        - ReagentSlimeLead
        - ReagentSlimechlorinetriflouride
        - ReagentSlimePotassium
        - ReagentSlimeLotophagoiOil
      chance: 1

- type: entity
  id: ReagentSlimeBeer
  parent: ReagentSlime
  suffix: Beer
  components:
  - type: Bloodstream
    bloodReagent: Beer
  - type: PointLight
    color: "#cfa85f"
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/elemental.rsi
    layers:
      - map: [ "enum.DamageStateVisualLayers.Base" ]
        state: alive
        color: "#cfa85f"

- type: entity
  id: ReagentSlimePax
  parent: ReagentSlime
  suffix: Pax
  components:
  - type: Bloodstream
    bloodReagent: Pax
  - type: PointLight
    color: "#AAAAAA"
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/elemental.rsi
    layers:
      - map: [ "enum.DamageStateVisualLayers.Base" ]
        state: alive
        color: "#AAAAAA"
  - type: MeleeChemicalInjector
    solution: bloodstream
    transferAmount: 1

- type: entity
  id: ReagentSlimeNocturine
  parent: ReagentSlime
  suffix: Nocturine
  components:
  - type: Bloodstream
    bloodReagent: Nocturine
  - type: PointLight
    color: "#128e80"
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/elemental.rsi
    layers:
      - map: [ "enum.DamageStateVisualLayers.Base" ]
        state: alive
        color: "#128e80"
  - type: MeleeChemicalInjector
    solution: bloodstream
    transferAmount: 3

- type: entity
  id: ReagentSlimeTHC
  parent: ReagentSlime
  suffix: THC
  components:
  - type: Bloodstream
    bloodReagent: THC
  - type: PointLight
    color: "#808080"
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/elemental.rsi
    layers:
      - map: [ "enum.DamageStateVisualLayers.Base" ]
        state: alive
        color: "#808080"

- type: entity
  id: ReagentSlimeBicaridine
  parent: ReagentSlime
  suffix: Bicaridine
  components:
  - type: Bloodstream
    bloodReagent: Bicaridine
  - type: PointLight
    color: "#ffaa00"
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/elemental.rsi
    layers:
      - map: [ "enum.DamageStateVisualLayers.Base" ]
        state: alive
        color: "#ffaa00"

- type: entity
  id: ReagentSlimeToxin
  parent: ReagentSlime
  suffix: Toxin
  components:
  - type: Bloodstream
    bloodReagent: Toxin
  - type: PointLight
    color: "#cf3600"
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/elemental.rsi
    layers:
      - map: [ "enum.DamageStateVisualLayers.Base" ]
        state: alive
        color: "#cf3600"

- type: entity
  id: ReagentSlimeNapalm
  parent: ReagentSlime
  suffix: Napalm
  components:
  - type: Bloodstream
    bloodReagent: Napalm
  - type: PointLight
    color: "#FA00AF"
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/elemental.rsi
    layers:
      - map: [ "enum.DamageStateVisualLayers.Base" ]
        state: alive
        color: "#FA00AF"

- type: entity
  id: ReagentSlimeOmnizine
  parent: ReagentSlime
  suffix: Omnizine
  components:
  - type: Bloodstream
    bloodReagent: Omnizine
  - type: PointLight
    color: "#fcf7f9"
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/elemental.rsi
    layers:
      - map: [ "enum.DamageStateVisualLayers.Base" ]
        state: alive
        color: "#fcf7f9"

- type: entity
  id: ReagentSlimeMuteToxin
  parent: ReagentSlime
  suffix: Mute Toxin
  components:
  - type: Bloodstream
    bloodReagent: MuteToxin
  - type: PointLight
    color: "#0f0f0f"
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/elemental.rsi
    layers:
      - map: [ "enum.DamageStateVisualLayers.Base" ]
        state: alive
        color: "#0f0f0f"

- type: entity
  id: ReagentSlimeNorepinephricAcid
  parent: ReagentSlime
  suffix: Norepinephric Acid
  components:
  - type: Bloodstream
    bloodReagent: NorepinephricAcid
  - type: PointLight
    color: "#96a8b5"
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/elemental.rsi
    layers:
      - map: [ "enum.DamageStateVisualLayers.Base" ]
        state: alive
        color: "#96a8b5"

- type: entity
  id: ReagentSlimeEphedrine
  parent: ReagentSlime
  suffix: Ephedrine
  components:
  - type: Bloodstream
    bloodReagent: Ephedrine
  - type: PointLight
    color: "#D2FFFA"
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/elemental.rsi
    layers:
      - map: [ "enum.DamageStateVisualLayers.Base" ]
        state: alive
        color: "#D2FFFA"

- type: entity
  id: ReagentSlimeRobustHarvest
  parent: ReagentSlime
  suffix: Robust Harvest
  components:
  - type: Bloodstream
    bloodReagent: RobustHarvest
  - type: PointLight
    color: "#3e901c"
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/elemental.rsi
    layers:
      - map: [ "enum.DamageStateVisualLayers.Base" ]
        state: alive
        color: "#3e901c"

- type: entity
  id: ReagentSlimeLotophagoiOil
  parent: ReagentSlime
  suffix: Lotophagoi Oil
  components:
  - type: Bloodstream
    bloodReagent: LotophagoiOil
  - type: PointLight
    color: "#FFBF00"
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/elemental.rsi
    layers:
      - map: [ "enum.DamageStateVisualLayers.Base" ]
        state: alive
        color: "#3e901c"
  - type: GhostRole
    prob: 1 #it's significantly more psionic than the others
    description: ghost-role-information-angry-slimes-description
