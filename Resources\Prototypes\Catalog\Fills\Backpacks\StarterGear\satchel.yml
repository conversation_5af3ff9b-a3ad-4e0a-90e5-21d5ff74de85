- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelNinjaTools
  components:
  - type: StorageFill
    contents:
      - id: Crowbar
      - id: Screwdriver
      - id: Wrench
      - id: Wirecutter
      - id: Welder
      - id: Multitool
      - id: FoodSnackDango
      - id: FoodSnackDango
      - id: DrinkSakeBottleFull


- type: entity
  parent: ClothingBackpackSatchelClown
  id: ClothingBackpackSatchelClownFilled
  components:
  - type: StorageFill
    contents:
      - id: BoxHug
      - id: RubberStampClown

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelBrigmedic
  id: ClothingBackpackSatchelBrigmedicFilled
  components:
  - type: StorageFill
    contents:
      - id: EmergencyRollerBedSpawnFolded
      - id: BodyBagFolded
      - id: Portafib
      - id: BruteAutoInjector
        amount: 2
      - id: BurnAutoInjector
        amount: 2

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelSecurity
  id: ClothingBackpackSatchelSecurityFilled
  components:
  - type: StorageFill
    contents:
      - id: MagazinePistol

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelSecurity
  id: ClothingBackpackSatchelSecurityFilledDetective
  components:
  - type: StorageFill
    contents:
      - id: ForensicPad
      - id: ForensicScanner

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelMedical
  id: ClothingBackpackSatchelMedicalFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelMedical
  id: ClothingBackpackSatchelParamedicFilled
  components:
  - type: StorageFill
    contents:
      - id: EmergencyRollerBedSpawnFolded

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelCaptain
  id: ClothingBackpackSatchelCaptainFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      #- name: StationCharter
      #- name: TelescopicBaton

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelEngineering
  id: ClothingBackpackSatchelChiefEngineerFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      #- id: TelescopicBaton

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelScience
  id: ClothingBackpackSatchelResearchDirectorFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      #- id: TelescopicBaton

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelHOPFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      #- id: TelescopicBaton

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelMedical
  id: ClothingBackpackSatchelCMOFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      #- id: TelescopicBaton

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelCargo
  id: ClothingBackpackSatchelQuartermasterFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      #- id: TelescopicBaton

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelSecurity
  id: ClothingBackpackSatchelHOSFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      - id: MagazinePistol

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelEngineering
  id: ClothingBackpackSatchelEngineeringFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelAtmospherics
  id: ClothingBackpackSatchelAtmosphericsFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelScience
  id: ClothingBackpackSatchelScienceFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelRobotics
  id: ClothingBackpackSatchelRoboticsFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelHydroponics
  id: ClothingBackpackSatchelHydroponicsFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelChemistry
  id: ClothingBackpackSatchelChemistryFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelChaplainFilled
  components:
  - type: StorageFill
    contents:
      - id: Bible
      - id: RubberStampChaplain

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelMusicianFilled
  components:
    - type: StorageFill
      contents:
        - id: AcousticGuitarInstrument
        - id: SaxophoneInstrument

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelLibrarianFilled
  components:
    - type: StorageFill
      contents:
        - id: BookRandom

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelDetectiveFilled
  components:
    - type: StorageFill
      contents:
      - id: BoxForensicPad
      - id: Lighter
      - id: CigPackBlack
      - id: HandLabeler

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelCargo
  id: ClothingBackpackSatchelCargoFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelSalvage
  id: ClothingBackpackSatchelSalvageFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelMime
  id: ClothingBackpackSatchelMimeFilled
  components:
  - type: StorageFill
    contents:
      - id: RubberStampMime

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSatchelHolding
  id: ClothingBackpackSatchelHoldingAdmin
  components:
  - type: StorageFill
    contents:
    - id: GasAnalyzer
    - id: trayScanner
  - type: Unremoveable
