﻿- type: entity
  id: ExtinguisherCabinet
  name: "шафа для вогнегасника"
  description: "Невелика настінна шафа, призначена для зберігання вогнегасника."
  components:
    - type: WallMount
      arc: 360
    - type: Transform
      anchored: true
    - type: Clickable
    - type: InteractionOutline
    - type: Sprite
      sprite: Structures/Wallmounts/extinguisher_cabinet.rsi
      snapCardinals: true
      layers:
      - state: frame
      - state: extinguisher
        map: ["enum.ItemCabinetVisualLayers.ContainsItem"]
        visible: true
      - state: closed
        map: ["enum.ItemCabinetVisualLayers.Door"]
    - type: ItemCabinet
      cabinetSlot:
        ejectOnInteract: true
        whitelist:
          components:
          - FireExtinguisher
      doorSound:
        path: /Audio/Machines/machine_switch.ogg
      openState: open
      closedState: closed
    - type: Appearance
    - type: ItemSlots
    - type: ContainerContainer
      containers:
        ItemCabinet: !type:ContainerSlot
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: Metallic
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 80
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
        - trigger:
            !type:DamageTrigger
            damage: 40
          behaviors:
            - !type:EmptyAllContainersBehaviour
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
            - !type:PlaySoundBehavior
              sound:
                collection: MetalGlassBreak
                params:
                  volume: -4
  placement:
    mode: SnapgridCenter

- type: entity
  id: ExtinguisherCabinetOpen
  parent: ExtinguisherCabinet
  suffix: Open
  components:
  - type: ItemCabinet
    opened: true
    doorSound:
      path: /Audio/Machines/machine_switch.ogg
    openState: open
    closedState: closed

- type: entity
  id: ExtinguisherCabinetFilled
  parent: ExtinguisherCabinet
  suffix: Filled
  components:
  - type: ItemCabinet
    cabinetSlot:
      ejectOnInteract: true
      startingItem: FireExtinguisher
      whitelist:
        components:
        - FireExtinguisher
    doorSound:
      path: /Audio/Machines/machine_switch.ogg
    openState: open
    closedState: closed

- type: entity
  id: ExtinguisherCabinetFilledOpen
  parent: ExtinguisherCabinetFilled
  suffix: Filled, Open
  components:
  - type: ItemCabinet
    opened: true
    doorSound:
      path: /Audio/Machines/machine_switch.ogg
    openState: open
    closedState: closed
