#Web vest
- type: entity
  parent: [ClothingOuterStorageBase, AllowSuitStorageClothing]
  id: ClothingOuterVestWeb
  name: "розвантажувальний жилет"
  description: "Синтетичний бронежилет. На цей додали розгрузку та балістичні пластини."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Vests/webvest.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Vests/webvest.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.6 #ballistic plates = better protection
        Slash: 0.6
        Piercing: 0.3
        Heat: 0.9
  - type: ExplosionResistance
    damageCoefficient: 0.9

#Mercenary web vest
- type: entity
  parent: ClothingOuterVestWeb #web vest so it should have some pockets for ammo
  id: ClothingOuterVestWebMerc
  name: "бронежилет найманця"
  description: "Високоякісний бронежилет, виготовлений з міцного синтетичного матеріалу. Він напрочуд гнучкий та легкий, незважаючи на грізну броню."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Vests/mercwebvest.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Vests/mercwebvest.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.7 #slightly better overall protection but slightly worse than bulletproof armor against bullets seems sensible
        Slash: 0.7
        Piercing: 0.5
        Heat: 0.9
  - type: ExplosionResistance
    damageCoefficient: 0.9

#Detective's vest
- type: entity
  parent: ClothingOuterArmorBasic
  id: ClothingOuterVestDetective
  name: "бронежилет детектива"
  description: "Бронежилет запеклого приватного детектива."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Vests/detvest.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Vests/detvest.rsi
  - type: Armor #Based on /tg/ but slightly compensated to fit the fact that armor stacks in SS14.
    modifiers:
      coefficients:
        Blunt: 0.70
        Slash: 0.70
        Piercing: 0.70 #Can save you, but bullets will still hurt. Will take about 10 shots from a Viper before critting, as opposed to 7 while unarmored and 16~ with a bulletproof vest.
        Heat: 0.80
  - type: ExplosionResistance
    damageCoefficient: 0.90

#Hazard vest
- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterVestHazard
  name: "сигнальний жилет"
  description: "Жилет підвищеної видимості, що використовується в робочих зонах."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Vests/hazard.rsi
    layers:
    - state: icon
    - state: icon-unshaded
      shader: unshaded
  - type: Clothing
    sprite: Clothing/OuterClothing/Vests/hazard.rsi
    clothingVisuals:
      outerClothing:
      - state: equipped-OUTERCLOTHING
      - state: equipped-OUTERCLOTHING-unshaded
        shader: unshaded
  - type: Tag
    tags:
    - HiViz

#(Bartender) vest
- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterVest
  name: "жилет"
  description: "Товстий жилет з гумовою, водостійкою оболонкою."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Vests/vest.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Vests/vest.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterVestNt
  name: "жилет Нанотрейзен"
  description: "Товстий жилет з гумовою, водонепроникною оболонкою синього кольору."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Vests/vest_nt.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Vests/vest_nt.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterVestIdris
  name: "жилет Idris Incorporated"
  description: "Товстий жилет з гумовою, водонепроникною оболонкою кольору чаплі."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Vests/vest_idris.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Vests/vest_idris.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterVestOrion
  name: "жилет Orion Express"
  description: "Товстий жилет з гумовою, водонепроникною оболонкою коричневого кольору."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Vests/vest_orion.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Vests/vest_orion.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterVestColorable
  name: "жилет"
  description: "Ви зацікавлені в тому, щоб виглядати добре, і цей жилет - ідеальний вибір для досягнення цієї мети!"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Vests/vest_colorable.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Vests/vest_colorable.rsi

- type: entity
  parent: ClothingOuterFoldableBase
  id: ClothingOuterVestThick
  name: "товстий жилет"
  description: "Жилет зі штучної шкіри."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Vests/vest_thick.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Vests/vest_thick.rsi
#PIRATE
#Blueshield vest
- type: entity
  parent: ClothingOuterArmorBasic
  id: ClothingOuterVestBlueshield
  name: "бронежилет синього щита"
  description: "Це жилет, а не желе!.."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Vests/blueshield.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Vests/blueshield.rsi