- type: entity
  id: PartHarpy
  parent: BaseItem
  name: "частина тіла гарпії"
  abstract: true
  components:
  - type: Damageable
    damageContainer: OrganicPart # Shitmed Change
  - type: BodyPart
  - type: ContainerContainer
    containers:
      bodypart: !type:Container
        ents: []
  - type: StaticPrice #DynamicPrice
    price: 100
  - type: Tag
    tags:
      - Trash
  # Shitmed Change Start
  - type: Gibbable
  - type: SurgeryTool
    startSound:
      path: /Audio/_Shitmed/Medical/Surgery/organ1.ogg
    endSound:
      path: /Audio/_Shitmed/Medical/Surgery/organ2.ogg
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTypeTrigger
        damageType: Blunt
        damage: 110
      behaviors:
      - !type:GibPartBehavior { }
    - trigger:
        !type:DamageTypeTrigger
        damageType: Slash
        damage: 150
      behaviors:
      - !type:GibPartBehavior { }
    - trigger:
        !type:DamageTypeTrigger
        damageType: Heat
        damage: 200
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawnInContainer: true
        spawn:
          Ash:
            min: 1
            max: 1
      - !type:<PERSON><PERSON>ody<PERSON>ehavior { }
      - !type:PlaySoundBehavior
        sound:
          collection: MeatLaserImpact
  # Shitmed Change End

- type: entity
  id: TorsoHarpy
  name: "торс гарпії"
  parent: PartHarpy
  components:
  - type: Sprite
    netsync: false
    sprite: Mobs/Species/Harpy/parts.rsi
    state: "torso_m"
  - type: Icon
    sprite: Mobs/Species/Harpy/parts.rsi
    state: "torso_m"
  - type: BodyPart
    partType: Torso
  # Shitmed Change Start
    toolName: "a torso"
    containerName: "torso_slot"
  - type: ContainerContainer
    containers:
      torso_slot: !type:ContainerSlot {}
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTypeTrigger
        damageType: Blunt
        damage: 400
      behaviors:
      - !type:GibPartBehavior { }
    - trigger:
        !type:DamageTypeTrigger
        damageType: Slash
        damage: 400
      behaviors:
      - !type:GibPartBehavior { }
    - trigger:
        !type:DamageTypeTrigger
        damageType: Heat
        damage: 400
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawnInContainer: true
        spawn:
          Ash:
            min: 1
            max: 1
      - !type:BurnBodyBehavior { }
      - !type:PlaySoundBehavior
        sound:
          collection: MeatLaserImpact
  # Shitmed Change End

- type: entity
  id: HeadHarpy
  name: "голова гарпії"
  parent: PartHarpy
  components:
  - type: Sprite
    netsync: false
    sprite: Mobs/Species/Harpy/parts.rsi
    state: "head_m"
  - type: Icon
    sprite: Mobs/Species/Harpy/parts.rsi
    state: "head_m"
  - type: BodyPart
    partType: Head
    vital: true
    toolName: "a head" # Shitmed Change
  - type: Input
    context: "ghost"
  - type: InputMover
  - type: GhostOnMove
  - type: Tag
    tags:
      - Head

- type: entity
  id: LeftArmHarpy
  name: "ліва рука гарпії"
  parent: PartHarpy
  components:
  - type: Sprite
    netsync: false
    sprite: Mobs/Species/Harpy/parts.rsi
    state: "l_arm"
  - type: Icon
    sprite: Mobs/Species/Harpy/parts.rsi
    state: "l_arm"
  - type: BodyPart
    partType: Arm
    symmetry: Left
    toolName: "a left arm" # Shitmed Change

- type: entity
  id: RightArmHarpy
  name: "права рука гарпії"
  parent: PartHarpy
  components:
  - type: Sprite
    netsync: false
    sprite: Mobs/Species/Harpy/parts.rsi
    state: "r_arm"
  - type: Icon
    sprite: Mobs/Species/Harpy/parts.rsi
    state: "r_arm"
  - type: BodyPart
    partType: Arm
    symmetry: Right
    toolName: "a right arm" # Shitmed Change
    onAdd:
    - type: Flight
      speedModifier: 1.3 # For harpies, it gives an effective ~60% (60.16%) increase over sprinting (as sprinting has a 10% increase for harpies) with base harpy weightless modifier
      isLayerAnimated: true
      layer: "/Textures/Mobs/Customization/Harpy/harpy_wings.rsi"
      animationKey: "Flap"

- type: entity
  id: LeftHandHarpy
  name: "ліва долоня гарпії"
  parent: PartHarpy
  components:
  - type: Sprite
    netsync: false
    sprite: Mobs/Species/Harpy/parts.rsi
    state: "l_hand"
  - type: Icon
    sprite: Mobs/Species/Harpy/parts.rsi
    state: "l_hand"
  - type: BodyPart
    partType: Hand
    symmetry: Left
    toolName: "a left hand" # Shitmed Change

- type: entity
  id: RightHandHarpy
  name: "права долоня гарпії"
  parent: PartHarpy
  components:
  - type: Sprite
    netsync: false
    sprite: Mobs/Species/Harpy/parts.rsi
    state: "r_hand"
  - type: Icon
    sprite: Mobs/Species/Harpy/parts.rsi
    state: "r_hand"
  - type: BodyPart
    partType: Hand
    symmetry: Right
    toolName: "a right hand" # Shitmed Change

- type: entity
  id: LeftLegHarpy
  name: "ліва нога гарпії"
  parent: PartHarpy
  components:
  - type: Sprite
    netsync: false
    sprite: Mobs/Species/Harpy/parts.rsi
    state: "l_leg"
  - type: Icon
    sprite: Mobs/Species/Harpy/parts.rsi
    state: "l_leg"
  - type: BodyPart
    partType: Leg
    symmetry: Left
    toolName: "a left leg" # Shitmed Change
  - type: MovementBodyPart

- type: entity
  id: RightLegHarpy
  name: "права нога гарпії"
  parent: PartHarpy
  components:
  - type: Sprite
    netsync: false
    sprite: Mobs/Species/Harpy/parts.rsi
    state: "r_leg"
  - type: Icon
    sprite: Mobs/Species/Harpy/parts.rsi
    state: "r_leg"
  - type: BodyPart
    partType: Leg
    symmetry: Right
    toolName: "a right leg" # Shitmed Change
  - type: MovementBodyPart

- type: entity
  id: LeftFootHarpy
  name: "ліва стопа гарпії"
  parent: PartHarpy
  components:
  - type: Sprite
    netsync: false
    sprite: Mobs/Species/Harpy/parts.rsi
    state: "l_foot"
  - type: Icon
    sprite: Mobs/Species/Harpy/parts.rsi
    state: "l_foot"
  - type: BodyPart
    partType: Foot
    symmetry: Left
    toolName: "a left foot" # Shitmed Change

- type: entity
  id: RightFootHarpy
  name: "права стопа гарпії"
  parent: PartHarpy
  components:
  - type: Sprite
    netsync: false
    sprite: Mobs/Species/Harpy/parts.rsi
    state: "r_foot"
  - type: Icon
    sprite: Mobs/Species/Harpy/parts.rsi
    state: "r_foot"
  - type: BodyPart
    partType: Foot
    symmetry: Right
    toolName: "a right foot" # Shitmed Change
