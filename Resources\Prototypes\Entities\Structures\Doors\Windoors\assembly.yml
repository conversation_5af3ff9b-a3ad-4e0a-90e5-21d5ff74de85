- type: entity
  id: WindoorAssembly
  name: "збірка вікно-дверей"
  description: "Він відкривається, закривається, і ви можете бачити крізь нього!"
  parent: BaseStructure
  components:
  - type: InteractionOutline
  - type: Sprite
    sprite: Structures/Doors/Windoors/windoor.rsi
    layers:
    - state: assembly
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.49,-0.49,0.49,-0.2"
        density: 40
        mask:
        - Impassable
        - HighImpassable
  - type: Anchorable
  - type: Pullable
  - type: Rotatable
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: RCDDeconstructable
    cost: 6
    delay: 8
    fx: EffectRCDDeconstruct8
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 3
  - type: Construction
    graph: Windoor
    node: assembly
  placement:
    mode: SnapgridCenter

- type: entity
  id: WindoorAssemblySecure
  name: "збірка захищених вікно-дверей"
  description: "Вона відкривається, закривається, і крізь неї можна бачити! Ця виглядає міцною."
  parent: WindoorAssembly
  components:
  - type: Sprite
    sprite: Structures/Doors/Windoors/windoor.rsi
    layers:
    - state: secure_underlay
    - state: assembly
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 600
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetPlasteel1:
            min: 1
            max: 2
  - type: Construction
    graph: Windoor
    node: assemblySecure

- type: entity
  id: WindoorAssemblyClockwork
  name: "збірка часових вікно-дверей"
  description: "Вона відкривається, закривається, і крізь неї можна бачити! Ця виглядає міцною."
  parent: WindoorAssembly
  components:
  - type: Sprite
    sprite: Structures/Doors/Windoors/clockwork_windoor.rsi
    layers:
    - state: open
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 450
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetBrass1:
            min: 1
            max: 2
  - type: Construction
    graph: Windoor
    node: assemblyClockwork

#plasma windoor assemblies
- type: entity
  id: WindoorAssemblyPlasma
  name: "збірка плазмових вікно-дверей"
  description: "Вона відкривається, закривається, і крізь неї можна бачити! Ця виглядає фіолетовою, ні, рожевою. Зачекай..."
  parent: WindoorAssembly
  components:
  - type: Sprite
    sprite: Structures/Doors/Windoors/plasma.rsi
    layers:
    - state: assembly
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 360
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 2
          SheetPGlass1:
            min: 1
            max: 3
  - type: Construction
    graph: Windoor
    node: pglass

- type: entity
  id: WindoorAssemblySecurePlasma
  name: "збірка захищених плазмових вікно-дверей"
  description: "Вона відкривається, закривається, і ви можете бачити крізь неї! Ця виглядає міцною і тонкою, ні, фіолетовою. Зачекай..."
  parent: WindoorAssembly
  components:
  - type: Sprite
    sprite: Structures/Doors/Windoors/plasma.rsi
    layers:
    - state: secure_underlay
    - state: assembly
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 1200
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetPlasteel1:
            min: 1
            max: 2
          SheetRPGlass1:
            min: 1
            max: 3
  - type: Construction
    graph: Windoor
    node: pglassSecure

#uranium windoor assemblies
- type: entity
  id: WindoorAssemblyUranium
  name: "збірка уранових вікно-дверей"
  description: "Вона відкривається, закривається, і крізь неї можна бачити! Ця виглядає хворобливо зеленою..."
  parent: WindoorAssembly
  components:
  - type: Sprite
    sprite: Structures/Doors/Windoors/uranium.rsi
    layers:
    - state: assembly
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 2
          SheetUGlass1:
            min: 1
            max: 3
  - type: Construction
    graph: Windoor
    node: uglass

- type: entity
  id: WindoorAssemblySecureUranium
  name: "збірка захищених уранових вікно-дверей"
  description: "Вона відкривається, закривається, і крізь неї можна бачити! Ця виглядає міцною і радіоактивно-вапняно-зеленою!"
  parent: WindoorAssembly
  components:
  - type: Sprite
    sprite: Structures/Doors/Windoors/uranium.rsi
    layers:
    - state: secure_underlay
    - state: assembly
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 1200
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetPlasteel1:
            min: 1
            max: 2
          SheetRUGlass1:
            min: 1
            max: 3
  - type: Construction
    graph: Windoor
    node: uglassSecure

