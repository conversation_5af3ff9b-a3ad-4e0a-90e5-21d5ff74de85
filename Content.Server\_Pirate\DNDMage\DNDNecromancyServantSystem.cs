using Content.Server.Administration.Systems;
using Content.Server.Chat.Systems;
using Content.Server.Ghost.Roles;
using Content.Server.Mind;
using Content.Shared._Pirate.DNDMage;
using Content.Shared.Damage;
using Content.Shared.Follower;
using Content.Server.Ghost.Roles.Components;
using Content.Shared.Mind;
using Content.Shared.Mind.Components;
using Content.Shared.Mobs;
using Content.Shared.Mobs.Components;
using Content.Shared.Mobs.Systems;
using Content.Shared.Popups;
using Robust.Shared.Audio.Systems;
using Robust.Shared.GameObjects;
using Robust.Shared.Player;
using Robust.Shared.Timing;
using Robust.Shared.Prototypes;
using Content.Shared.NPC.Systems;
using Content.Shared.NPC.Components;
using Content.Server.NPC.HTN;
using Content.Server.NPC.Systems;
using Content.Shared.Humanoid;
using Content.Server.Humanoid;
using Content.Shared.Inventory;
using Content.Shared.Movement.Systems;
using Content.Shared.Movement.Components;
using System.Linq;

namespace Content.Server._Pirate.DNDMage;

/// <summary>
/// Система для управління слугами некроманта
/// </summary>
public sealed class DNDNecromancyServantSystem : EntitySystem
{
    [Dependency] private readonly IGameTiming _timing = default!;
    [Dependency] private readonly MobStateSystem _mobState = default!;
    [Dependency] private readonly DamageableSystem _damageable = default!;
    [Dependency] private readonly RejuvenateSystem _rejuvenate = default!;
    [Dependency] private readonly SharedPopupSystem _popup = default!;
    [Dependency] private readonly ChatSystem _chat = default!;
    [Dependency] private readonly MindSystem _mind = default!;
    [Dependency] private readonly GhostRoleSystem _ghostRole = default!;
    [Dependency] private readonly FollowerSystem _follower = default!;
    [Dependency] private readonly SharedAudioSystem _audio = default!;
    [Dependency] private readonly SharedTransformSystem _transform = default!;
    [Dependency] private readonly NpcFactionSystem _faction = default!;
    [Dependency] private readonly HTNSystem _htn = default!;
    [Dependency] private readonly NPCSystem _npc = default!;
    [Dependency] private readonly HumanoidAppearanceSystem _humanoid = default!;
    [Dependency] private readonly InventorySystem _inventory = default!;
    [Dependency] private readonly MovementSpeedModifierSystem _movement = default!;
    [Dependency] private readonly IPrototypeManager _prototypeManager = default!;

    public override void Initialize()
    {
        base.Initialize();

        SubscribeLocalEvent<DNDNecromancyServantComponent, ComponentInit>(OnServantInit);
        SubscribeLocalEvent<DNDNecromancyServantComponent, MobStateChangedEvent>(OnServantMobStateChanged);
        SubscribeLocalEvent<DNDNecromancyServantComponent, DamageChangedEvent>(OnServantDamaged);

        // Команди для слуг
        SubscribeNetworkEvent<ServantCommandEvent>(OnServantCommand);
    }

    public override void Update(float frameTime)
    {
        base.Update(frameTime);

        // Перевіряємо всіх слуг некроманта
        var query = EntityQueryEnumerator<DNDNecromancyServantComponent>();
        while (query.MoveNext(out var uid, out var servant))
        {
            UpdateServant(uid, servant, frameTime);
            UpdateLoyalty(uid, servant, frameTime);
        }
    }

    private void OnServantInit(EntityUid uid, DNDNecromancyServantComponent component, ComponentInit args)
    {
        component.ResurrectionTime = _timing.CurTime;

        // Визначаємо тип слуги
        component.Type = DetermineServantType(uid);

        // Налаштовуємо слугу залежно від типу
        SetupServantByType(uid, component);

        // Додаємо візуальні ефекти
        SetupServantVisuals(uid, component);

        // Налаштовуємо AI поведінку
        SetupServantAI(uid, component);

        // Створюємо ghost role для воскрешеного слуги
        CreateGhostRoleForServant(uid, component);

        // Показуємо повідомлення про воскресіння
        _popup.PopupEntity("💀 Ви були воскрешені темною магією! 💀", uid, uid);

        if (component.Master != EntityUid.Invalid)
        {
            _popup.PopupEntity($"💀 {Name(uid)} тепер служить вам! 💀", component.Master, component.Master);

            // Слуга починає слідувати за майстром
            if (component.ShouldFollowMaster)
            {
                _follower.StartFollowingEntity(uid, component.Master);
            }
        }
    }

    private void OnServantMobStateChanged(EntityUid uid, DNDNecromancyServantComponent component, MobStateChangedEvent args)
    {
        // Якщо слуга помер, видаляємо компонент
        if (args.NewMobState == MobState.Dead)
        {
            if (component.Master != EntityUid.Invalid)
            {
                _popup.PopupEntity($"💀 Ваш слуга {Name(uid)} повернувся до смерті! 💀", component.Master, component.Master);
            }

            RemComp<DNDNecromancyServantComponent>(uid);
        }
    }

    private void OnServantDamaged(EntityUid uid, DNDNecromancyServantComponent component, DamageChangedEvent args)
    {
        // Ділимо пошкодження з майстром (як у Guardian системі)
        if (args.DamageDelta == null || component.Master == EntityUid.Invalid || component.DamageShare == 0)
            return;

        if (!Exists(component.Master))
            return;

        // Передаємо частину пошкодження майстру
        _damageable.TryChangeDamage(
            component.Master,
            args.DamageDelta * component.DamageShare,
            origin: args.Origin,
            interruptsDoAfters: false);

        _popup.PopupEntity("💀 Ваш слуга отримує пошкодження! 💀", component.Master, component.Master);
    }

    private void UpdateServant(EntityUid uid, DNDNecromancyServantComponent component, float frameTime)
    {
        // Перевіряємо, чи не закінчився час служіння
        var currentTime = _timing.CurTime;
        var timeSinceResurrection = (currentTime - component.ResurrectionTime).TotalSeconds;

        if (timeSinceResurrection >= component.ServantDuration)
        {
            // Час служіння закінчився, слуга помирає
            KillServant(uid, component, "💀 Темна магія залишає ваше тіло... 💀");
            return;
        }

        // Перевіряємо, чи майстер все ще існує
        if (component.Master == EntityUid.Invalid || !Exists(component.Master))
        {
            KillServant(uid, component, "💀 Ваш майстер зник, ви повертаєтеся до смерті... 💀");
            return;
        }

        // Перевіряємо відстань до майстра
        if (component.ShouldFollowMaster && component.Master != EntityUid.Invalid)
        {
            var masterPos = _transform.GetMapCoordinates(component.Master);
            var servantPos = _transform.GetMapCoordinates(uid);

            if (masterPos.MapId == servantPos.MapId)
            {
                var distance = (masterPos.Position - servantPos.Position).Length();

                if (distance > component.MaxDistanceFromMaster)
                {
                    // Слуга занадто далеко від майстра
                    _popup.PopupEntity("💀 Ви занадто далеко від свого майстра! 💀", uid, uid);

                    // Телепортуємо слуги до майстра, якщо він дуже далеко
                    if (distance > component.MaxDistanceFromMaster * 2)
                    {
                        _transform.SetCoordinates(uid, _transform.GetMoverCoordinates(component.Master));
                        _popup.PopupEntity("💀 Темна магія повертає вас до майстра! 💀", uid, uid);
                    }
                }
            }
        }
    }

    private void KillServant(EntityUid uid, DNDNecromancyServantComponent component, string message)
    {
        _popup.PopupEntity(message, uid, uid);

        if (component.Master != EntityUid.Invalid && Exists(component.Master))
        {
            _popup.PopupEntity($"💀 Ваш слуга {Name(uid)} повернувся до смерті! 💀", component.Master, component.Master);
        }

        // Завдаємо смертельне пошкодження
        var lethalDamage = new DamageSpecifier();
        lethalDamage.DamageDict.Add("Blunt", 1000);
        _damageable.TryChangeDamage(uid, lethalDamage);

        RemComp<DNDNecromancyServantComponent>(uid);
    }

    private void CreateGhostRoleForServant(EntityUid uid, DNDNecromancyServantComponent component)
    {
        // Створюємо ghost role компонент
        var ghostRole = EnsureComp<GhostRoleComponent>(uid);
        var ghostTakeover = EnsureComp<GhostTakeoverAvailableComponent>(uid);

        ghostRole.RoleName = "Слуга Некроманта";
        ghostRole.RoleDescription = $"Ви були воскрешені темною магією некроманта {(component.Master != EntityUid.Invalid ? Name(component.Master) : "невідомого мага")}. " +
                                   "Ваше завдання - служити своєму майстру, захищати його та виконувати його накази. " +
                                   $"Ваше служіння триватиме {component.ServantDuration} секунд.";

        ghostRole.RoleRules = "1. Ви повинні слухатися свого майстра-некроманта\n" +
                             "2. Захищайте свого майстра від ворогів\n" +
                             "3. Виконуйте накази майстра\n" +
                             "4. Не відходьте занадто далеко від майстра\n" +
                             "5. Ваше служіння обмежене в часі";

        // Ці властивості встановлюються автоматично системою ghost role

        // Реєструємо ghost role
        _ghostRole.RegisterGhostRole((uid, ghostRole));
    }

    /// <summary>
    /// Воскрешає труп та робить його слугою некроманта
    /// </summary>
    public bool TryResurrectAsServant(EntityUid corpse, EntityUid master, float duration = 300f)
    {
        // Перевіряємо, чи це мертва істота
        if (!TryComp<MobStateComponent>(corpse, out var mobState) || !_mobState.IsDead(corpse, mobState))
            return false;

        // Перевіряємо, чи це не маг
        if (HasComp<DNDMageComponent>(corpse))
            return false;

        // Повністю лікуємо труп
        _rejuvenate.PerformRejuvenate(corpse);

        // Додаємо компонент слуги некроманта
        var servant = EnsureComp<DNDNecromancyServantComponent>(corpse);
        servant.Master = master;
        servant.ServantDuration = duration;
        servant.ResurrectionTime = _timing.CurTime;

        // Налаштовуємо faction для слуги
        SetupServantFaction(corpse, master);

        // Звук воскресіння
        _audio.PlayPvs("/Audio/Magic/summon_magic.ogg", corpse);

        return true;
    }

    /// <summary>
    /// Отримує всіх слуг конкретного майстра
    /// </summary>
    public List<EntityUid> GetServantsOfMaster(EntityUid master)
    {
        var servants = new List<EntityUid>();

        var query = EntityQueryEnumerator<DNDNecromancyServantComponent>();
        while (query.MoveNext(out var uid, out var servant))
        {
            if (servant.Master == master)
            {
                servants.Add(uid);
            }
        }

        return servants;
    }

    /// <summary>
    /// Перевіряє, чи є сутність слугою некроманта
    /// </summary>
    public bool IsNecromancyServant(EntityUid uid, out EntityUid? master)
    {
        master = null;

        if (TryComp<DNDNecromancyServantComponent>(uid, out var servant))
        {
            master = servant.Master;
            return true;
        }

        return false;
    }

    /// <summary>
    /// Налаштовує faction для слуги некроманта
    /// </summary>
    private void SetupServantFaction(EntityUid servant, EntityUid master)
    {
        // Робимо слугу дружнім до майстра
        _faction.IgnoreEntity(servant, master);
        _faction.IgnoreEntity(master, servant);

        // Додаємо слугу до спеціальної faction некромантських слуг
        var servantFactionComp = EnsureComp<NpcFactionMemberComponent>(servant);
        _faction.RemoveFaction((servant, servantFactionComp), "NanoTrasen", false);
        _faction.AddFaction((servant, servantFactionComp), "DNDNecromancyServant", false);

        // Якщо майстер є DND магом, додаємо його до відповідної faction
        if (HasComp<DNDMageComponent>(master))
        {
            var masterFactionComp = EnsureComp<NpcFactionMemberComponent>(master);
            _faction.AddFaction((master, masterFactionComp), "DNDMage", false);
        }
    }

    /// <summary>
    /// Визначає тип слуги на основі оригінального трупа
    /// </summary>
    private ServantType DetermineServantType(EntityUid uid)
    {
        // Перевіряємо, чи має слуга зброю
        if (_inventory.TryGetSlotEntity(uid, "gloves", out _) ||
            _inventory.TryGetSlotEntity(uid, "belt", out _))
        {
            return ServantType.Warrior;
        }

        // Перевіряємо, чи це був маг (хоча магів не можна воскрешати)
        if (HasComp<DNDMageComponent>(uid))
        {
            return ServantType.Mage;
        }

        // Перевіряємо, чи це була охорона або солдат
        if (TryComp<HumanoidAppearanceComponent>(uid, out _))
        {
            // Логіка визначення типу на основі зовнішності
            // Можна розширити пізніше
        }

        // За замовчуванням - базовий тип
        return ServantType.Basic;
    }

    /// <summary>
    /// Налаштовує слугу залежно від його типу
    /// </summary>
    private void SetupServantByType(EntityUid uid, DNDNecromancyServantComponent component)
    {
        switch (component.Type)
        {
            case ServantType.Warrior:
                component.DamageShare = 0.15f; // Менше ділиться пошкодження
                component.LoyaltyDecayRate = 0.3f; // Повільніше втрачає лояльність
                break;

            case ServantType.Guardian:
                component.DamageShare = 0.35f; // Більше ділиться пошкодження
                component.MaxDistanceFromMaster = 3f; // Ближче до майстра
                component.LoyaltyDecayRate = 0.2f; // Дуже повільно втрачає лояльність
                break;

            case ServantType.Scout:
                component.MaxDistanceFromMaster = 15f; // Може бути далі
                component.LoyaltyDecayRate = 0.7f; // Швидше втрачає лояльність
                // Додаємо швидкість
                if (TryComp<MovementSpeedModifierComponent>(uid, out var movement))
                {
                    _movement.ChangeBaseSpeed(uid, movement.BaseWalkSpeed * 1.3f, movement.BaseSprintSpeed * 1.3f, movement.Acceleration);
                }
                break;

            case ServantType.Mage:
                component.DamageShare = 0.4f; // Багато ділиться пошкодження
                component.ServantDuration *= 0.7f; // Менше часу служіння
                component.LoyaltyDecayRate = 1.0f; // Швидко втрачає лояльність
                break;

            case ServantType.Basic:
            default:
                // Стандартні налаштування
                break;
        }
    }

    /// <summary>
    /// Налаштовує візуальні ефекти для слуги
    /// </summary>
    private void SetupServantVisuals(EntityUid uid, DNDNecromancyServantComponent component)
    {
        var visual = EnsureComp<DNDNecromancyServantVisualComponent>(uid);

        switch (component.Type)
        {
            case ServantType.Warrior:
                visual.EyeGlowColor = Color.Red;
                visual.AuraColor = Color.DarkRed;
                visual.VisualType = ServantVisualType.Warrior;
                break;

            case ServantType.Guardian:
                visual.EyeGlowColor = Color.Blue;
                visual.AuraColor = Color.DarkBlue;
                visual.VisualType = ServantVisualType.Guardian;
                break;

            case ServantType.Scout:
                visual.EyeGlowColor = Color.Green;
                visual.AuraColor = Color.DarkGreen;
                visual.VisualType = ServantVisualType.Scout;
                break;

            case ServantType.Mage:
                visual.EyeGlowColor = Color.Purple;
                visual.AuraColor = Color.DarkMagenta;
                visual.VisualType = ServantVisualType.Mage;
                break;

            case ServantType.Basic:
            default:
                visual.EyeGlowColor = Color.Yellow;
                visual.AuraColor = Color.Purple;
                visual.VisualType = ServantVisualType.Basic;
                break;
        }

        Dirty(uid, visual);
    }

    /// <summary>
    /// Налаштовує AI поведінку для слуги
    /// </summary>
    private void SetupServantAI(EntityUid uid, DNDNecromancyServantComponent component)
    {
        // Додаємо HTN компонент для AI поведінки (він наслідується від NPCComponent)
        var htn = EnsureComp<HTNComponent>(uid);

        // Створюємо базове завдання для HTN
        htn.RootTask = new HTNCompoundTask()
        {
            Task = "NecromancyServantCompound"
        };

        if (component.Master != EntityUid.Invalid)
        {
            htn.Blackboard.SetValue("NecromancyMaster", component.Master);
        }
        htn.Blackboard.SetValue("FollowRange", component.MaxDistanceFromMaster);
        htn.Blackboard.SetValue("DefendRange", 5f);
        htn.Blackboard.SetValue("AttackRange", 2f);

        // Встановлюємо часи для WaitOperator
        htn.Blackboard.SetValue("HesitateTime", 3.0f);
        htn.Blackboard.SetValue("StayTime", 2.0f);
        htn.Blackboard.SetValue("PatrolWaitTime", 2.0f);
        htn.Blackboard.SetValue("IdleTime", 1.0f);

        Dirty(uid, htn);
    }

    /// <summary>
    /// Оновлює лояльність слуги
    /// </summary>
    private void UpdateLoyalty(EntityUid uid, DNDNecromancyServantComponent component, float frameTime)
    {
        // Зменшуємо лояльність з часом
        component.Loyalty -= component.LoyaltyDecayRate * frameTime;

        // Перевіряємо, чи слуга все ще лояльний
        if (component.Loyalty <= 0)
        {
            // Слуга втратив лояльність і стає ворожим
            BecomeHostile(uid, component);
        }
        else if (component.Loyalty <= 25)
        {
            // Попередження про низьку лояльність
            if (component.Master != EntityUid.Invalid && _timing.CurTime.TotalSeconds % 10 < frameTime)
            {
                _popup.PopupEntity($"💀 {Name(uid)} виглядає неспокійно... 💀", component.Master, component.Master);
            }
        }
    }

    /// <summary>
    /// Робить слугу ворожим до майстра
    /// </summary>
    private void BecomeHostile(EntityUid uid, DNDNecromancyServantComponent component)
    {
        _popup.PopupEntity("💀 Ви більше не підкоряєтеся темній магії! 💀", uid, uid);

        if (component.Master != EntityUid.Invalid)
        {
            _popup.PopupEntity($"💀 {Name(uid)} повстав проти вас! 💀", component.Master, component.Master);

            // Робимо слугу ворожим до майстра
            if (TryComp<NpcFactionMemberComponent>(uid, out var servantFaction))
            {
                // Переводимо слугу до faction повстанців
                _faction.RemoveFaction((uid, servantFaction), "DNDNecromancyServant", false);
                _faction.AddFaction((uid, servantFaction), "DNDNecromancyServantRebel", false);
            }
        }

        // Видаляємо компонент слуги
        RemComp<DNDNecromancyServantComponent>(uid);
    }

    /// <summary>
    /// Обробляє команди для слуг
    /// </summary>
    private void OnServantCommand(ServantCommandEvent ev, EntitySessionEventArgs args)
    {
        // Конвертуємо NetEntity в EntityUid
        if (!TryGetEntity(ev.Target, out var targetUid))
            return;

        if (!TryComp<DNDNecromancyServantComponent>(targetUid, out var servant))
            return;

        // Перевіряємо, чи гравець є майстром слуги
        if (servant.Master != args.SenderSession.AttachedEntity)
            return;

        servant.CurrentCommand = ev.Command;

        // Оновлюємо AI blackboard
        if (TryComp<HTNComponent>(targetUid, out var htn))
        {
            htn.Blackboard.SetValue("CurrentCommand", ev.Command);
        }

        _popup.PopupEntity($"Ви отримали наказ: {GetCommandDescription(ev.Command)}", targetUid.Value, targetUid.Value);
    }

    /// <summary>
    /// Отримує опис команди
    /// </summary>
    private static string GetCommandDescription(ServantCommand command) => command switch
    {
        ServantCommand.Follow => "Слідувати за майстром",
        ServantCommand.Guard => "Охороняти позицію",
        ServantCommand.Attack => "Атакувати ворогів",
        ServantCommand.Patrol => "Патрулювати область",
        ServantCommand.Stay => "Залишатися на місці",
        _ => "Невідома команда"
    };
}
