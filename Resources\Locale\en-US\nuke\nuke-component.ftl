nuke-component-cant-anchor-floor = The anchoring bolts fail to lock into the floor!
nuke-component-announcement-sender = Nuclear Fission Explosive
nuke-component-announcement-armed = Attention! The station's self-destruct mechanism has been engaged {$location}. {$time} seconds until detonation. If this was made in error, the mechanism may still be disarmed.
nuke-component-announcement-unarmed = The station's self-destruct was deactivated! Have a nice day!
nuke-component-announcement-send-codes = Attention! Self-destruction codes have been sent to designated fax machines.
nuke-component-doafter-warning = You start fiddling with wires and knobs in order to disarm the nuke.. This may take a while.

# Nuke UI
nuke-user-interface-title = Nuclear Fission Explosive
nuke-user-interface-arm-button = ARM
nuke-user-interface-disarm-button = DISARM
nuke-user-interface-anchor-button = ANCHOR
nuke-user-interface-eject-button = EJECT

## Upper status
nuke-user-interface-first-status-device-locked = DEVICE LOCKED
nuke-user-interface-first-status-input-code = INPUT CODE
nuke-user-interface-first-status-input-time = INPUT TIME
nuke-user-interface-first-status-device-ready = DEVICE READY
nuke-user-interface-first-status-device-armed = DEVICE ARMED
nuke-user-interface-first-status-device-cooldown = DEACTIVATED
nuke-user-interface-status-error = ERROR

## Lower status
nuke-user-interface-second-status-await-disk = AWAIT DISK
nuke-user-interface-second-status-time = TIME: {$time}
nuke-user-interface-second-status-current-code = CODE: {$code}
nuke-user-interface-second-status-cooldown-time = WAIT: {$time}

## Nuke labels
nuke-label-nanotrasen = NT-{$serial}

# do you even need this one? It's more funnier to say that
# the Syndicate stole a NT nuke
nuke-label-syndicate = SYN-{$serial}

# Codes
nuke-codes-message = [color=red]TOP SECRET![/color]
nuke-codes-list = {$name} code: {$code}
nuke-codes-fax-paper-name = nuclear authentication codes

# Nuke disk slot
nuke-slot-component-slot-name-disk = Disk

## Examine
nuke-examine-armed = Hey uh, why's that [color=red]red light[/color] blinking?
nuke-examine-exploding = Yeah... I think it's too late buddy.
