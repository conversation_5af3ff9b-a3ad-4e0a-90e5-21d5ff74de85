- type: entity
  parent: DrinkBottleGlassBaseFull
  id: BaseOilJar
  abstract: true
  components:
  - type: Sprite
    sprite: Nyanotrasen/Objects/Consumable/Drinks/oil_jar.rsi
    state: icon
    layers:
      - state: icon
        map: ["drinkCanIcon"]
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.OpenableVisuals.Opened:
        drinkCanIcon:
          True: {state: "icon_open"}
          False: {state: "icon"}

- type: entity
  parent: BaseOilJar
  id: OilJarGhee
  name: "банка топленого масла"
  description: "Велика банка з тонованого скла з простою етикеткою вершкового масла."
  components:
  - type: Sprite
    layers:
      - state: icon
        map: ["drinkCanIcon"]
      - state: butter
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 100
        reagents:
        - ReagentId: OilGhee
          Quantity: 100

- type: entity
  parent: BaseOilJar
  id: OilJarCorn
  name: "банка кукурудзяної олії"
  description: "Велика банка з тонованого скла з простою етикеткою у вигляді кукурудзяного стебла."
  components:
  - type: Sprite
    layers:
      - state: icon
        map: ["drinkCanIcon"]
      - state: corn
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 100
        reagents:
        - ReagentId: Cornoil
          Quantity: 100

- type: entity
  parent: BaseOilJar
  id: OilJarOlive
  name: "банка оливкової олії"
  description: "Велика банка з тонованого скла з простою етикеткою оливок."
  components:
  - type: Sprite
    layers:
      - state: icon
        map: ["drinkCanIcon"]
      - state: olives
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 100
        reagents:
        - ReagentId: OilOlive
          Quantity: 100
