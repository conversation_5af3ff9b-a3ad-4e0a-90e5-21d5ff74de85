- type: entity
  parent: BaseStorageItem
  name: "пачка паперу для закруток"
  id: PackPaperRolling
  description: "Пачка тонких шматочків паперу, з яких виготовляють тонкі курильні суміші."
  components:
  - type: Storage
    grid:
    - 0,0,3,1
    whitelist:
      tags:
      - RollingPaper
      - CigFilter
  - type: StorageFill
    contents:
      - id: PaperRolling
        amount: 4
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigarettes/paper.rsi
    state: cigpapers
  - type: Item
    sprite: Objects/Consumable/Smokeables/Cigarettes/paper.rsi

- type: entity
  parent: PackPaperRolling
  name: "пачка паперу для закруток з фільтрами"
  id: PackPaperRollingFilters
  description: "Пачка фільтрів і тонких шматочків паперу, з яких виготовляють тонкі курильні суміші."
  components:
  - type: Storage
    whitelist:
      tags:
      - RollingPaper
      - CigFilter
  - type: StorageFill
    contents:
      - id: PaperRolling
        amount: 2
      - id: CigaretteFilter
        amount: 2

- type: entity
  id: PaperRolling
  name: "рулонний папір"
  description: "Тонкий шматок паперу, з якого виготовляють тонкі курильні суміші."
  suffix: Full
  parent: BaseItem
  components:
  - type: Stack
    stackType: PaperRolling
    count: 5
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigarettes/paper.rsi
    state: cigpaper
  - type: Item
    sprite: Objects/Consumable/Smokeables/Cigarettes/paper.rsi
    size: Tiny
  - type: Tag
    tags:
    - RollingPaper
    - Trash
  - type: SpaceGarbage

- type: entity
  id: PaperRolling1
  suffix: Single
  parent: PaperRolling
  components:
    - type: Stack
      count: 1

- type: entity
  id: CigaretteFilter
  name: "сигаретний фільтр"
  description: "Смужка цупкого паперу, що використовується як фільтр для сигарет ручної роботи."
  suffix: Full
  parent: BaseItem
  components:
  - type: Stack
    stackType: CigaretteFilter
    count: 5
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigarettes/paper.rsi
    state: cigfilter
  - type: Item
    sprite: Objects/Consumable/Smokeables/Cigarettes/paper.rsi
    size: Tiny
  - type: Tag
    tags:
    - CigFilter
    - Trash

- type: entity
  id: CigaretteFilter1
  suffix: Single
  parent: CigaretteFilter
  components:
    - type: Stack
      count: 1
