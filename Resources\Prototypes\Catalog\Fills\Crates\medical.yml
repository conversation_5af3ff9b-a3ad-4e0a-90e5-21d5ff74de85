- type: entity
  id: CrateMedicalSupplies
  parent: CrateMedical
  name: "ящик медикаментів"
  description: "Базові медикаменти."
  components:
  - type: StorageFill
    contents:
      - id: MedkitFilled
        amount: 2
      - id: Gauze
        amount: 2
      - id: Bloodpack
        amount: 2
      - id: BoxLatexGloves
      - id: BoxSterileMask
      - id: BoxMouthSwab

- type: entity
  id: CrateChemistrySupplies
  parent: CrateMedical
  name: "ящик хімікатів"
  description: "Базові хімікати."
  components:
  - type: StorageFill
    contents:
      - id: BoxSyringe
      - id: BoxBeaker
      - id: BoxPillCanister
      - id: Dropper
        amount: 2
      - id: BoxBottle
        amount: 2

- type: entity
  id: CrateChemistryVials
  parent: CrateMedical
  name: "ящик для флаконів"
  description: "Ящик, наповнений коробкою з флаконами."
  components:
  - type: StorageFill
    contents:
      - id: BoxVial

- type: entity
  id: CrateMindShieldImplants
  parent: CrateMedical
  name: "імпланти MindShield"
  description: "Ящик з 3 імплантатами MindShield."
  components:
  - type: StorageFill
    contents:
      - id: MindShieldImplanter
        amount: 3

- type: entity
  id: CrateMedicalSurgery
  parent: CrateSurgery
  name: "ящик хірургічних інструментів"
  description: "Хірургічні інструменти."
  components:
  - type: StorageFill
    contents:
      - id: Scalpel
      - id: Retractor
      - id: Cautery
      - id: Drill
      - id: SawElectric # Shitmed Change
      - id: Hemostat
      # Shitmed Change
      - id: BoneGel
      - id: BoxLatexGloves
      - id: BoxSterileMask

- type: entity
  id: CrateMedicalScrubs
  parent: CrateMedical
  name: "ящик медичних скрабів"
  description: "Медичний одяг."
  components:
  - type: StorageFill
    contents:
      - id: UniformScrubsColorGreen
      - id: UniformScrubsColorPurple
      - id: UniformScrubsColorBlue
#DeltaV adds cyan through white scrubs
      - id: UniformScrubsColorCyan
      - id: UniformScrubsColorBlack
      - id: UniformScrubsColorPink
      - id: UniformScrubsColorRainbow
      - id: UniformScrubsColorWhite
      - id: ClothingHeadHatSurgcapBlue
      - id: ClothingHeadHatSurgcapPurple
      - id: ClothingHeadHatSurgcapGreen
#DeltaV adds cyan through white surgcaps
      - id: ClothingHeadHatSurgcapCyan
      - id: ClothingHeadHatSurgcapBlack
      - id: ClothingHeadHatSurgcapPink
      - id: ClothingHeadHatSurgcapRainbow
      - id: ClothingHeadHatSurgcapWhite
      - id: ClothingMaskSterile
#DeltaV nerfs amount from 3 to 1(more items)
        amount: 1

- type: entity
  id: CrateEmergencyBurnKit
  parent: CrateMedical
  name: "аварійний набір від опіків"
  description: "Ящик з набором для лікування опіків."
  components:
  - type: StorageFill
    contents:
    - id: MedkitBurnFilled

- type: entity
  id: CrateEmergencyToxinKit
  parent: CrateMedical
  name: "аварійний набір від токсинів"
  description: "Ящик з набором для обробки токсинів."
  components:
  - type: StorageFill
    contents:
    - id: MedkitToxinFilled

- type: entity
  id: CrateEmergencyO2Kit
  parent: CrateMedical
  name: "аварійний кисневий набір"
  description: "Ящик з набором для лікування гіпоксії."
  components:
  - type: StorageFill
    contents:
    - id: MedkitOxygenFilled

- type: entity
  id: CrateEmergencyBruteKit
  parent: CrateMedical
  name: "аварійний набір від травм"
  description: "Ящик з набором для лікування травм"
  components:
  - type: StorageFill
    contents:
    - id: MedkitBruteFilled

- type: entity
  id: CrateEmergencyAdvancedKit
  parent: CrateMedical
  name: "аварійний розширений набір"
  description: "Ящик, наповнений просунутим набором для лікування травм і опіків."
  components:
  - type: StorageFill
    contents:
    - id: MedkitAdvancedFilled

- type: entity
  id: CrateEmergencyRadiationKit
  parent: CrateMedical
  name: "аварійний радіаційний набір"
  description: "Ящик, наповнений просунутим набором для лікування радіації."
  components:
  - type: StorageFill
    contents:
    - id: MedkitRadiationFilled

- type: entity
  id: CrateBodyBags
  parent: CrateMedical
  name: "ящик з мішками для трупів"
  description: "Містить десять мішків для трупів."
  components:
  - type: StorageFill
    contents:
      - id: BoxBodyBag
        amount: 2

- type: entity
  id: CrateVirologyBiosuit
  parent: CrateMedicalSecure
  name: "ящик з вірусологічним біокостюмом"
  description: "Містить 2 костюми біозахисту, щоб жодна хвороба не відволікала вас від лікування екіпажу. Для відкриття потрібен медичний доступ."
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterBioVirology
        amount: 2
      - id: ClothingHeadHatHoodBioVirology
        amount: 2
      - id: ClothingMaskSterile
        amount: 2
