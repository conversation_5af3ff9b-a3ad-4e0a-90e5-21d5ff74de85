#SOFTSUITS
#Basic EVA
- type: entity
  id: SuitStorageEVA
  parent: SuitStorageBase
  suffix: EVA
  components:
  - type: StorageFill
    contents:
        - id: OxygenTankFilled
        - id: ClothingOuterHardsuitEVA
        - id: ClothingHeadHelmetEVA
        - id: ClothingMaskBreath

#Basic EVA (Big Ass Helmet)
- type: entity
  id: SuitStorageEVAAlternate
  parent: SuitStorageBase
  suffix: EVA, Large Helmet
  components:
  - type: StorageFill
    contents:
        - id: OxygenTankFilled
        - id: ClothingOuterHardsuitEVA
        - id: ClothingHeadHelmetEVALarge
        - id: ClothingMaskBreath

#Emergency EVA
- type: entity
  id: SuitStorageEVAEmergency
  parent: SuitStorageBase
  suffix: Emergency EVA
  components:
  - type: StorageFill
    contents:
        - id: OxygenTankFilled
        - id: ClothingOuterSuitEmergency
        - id: ClothingMaskBreath

#Prisoner EVA
- type: entity
  id: SuitStorageEVAPrisoner
  parent: SuitStorageBase
  suffix: Prisoner EVA
  components:
  - type: StorageFill
    contents:
        - id: OxygenTankFilled
        - id: ClothingOuterHardsuitEVAPrisoner
        - id: ClothingHeadHelmetEVALarge
        - id: ClothingMaskBreath

#Syndicate EVA
- type: entity
  id: SuitStorageEVASyndicate
  parent: SuitStorageBase
  suffix: Syndicate EVA
  components:
  - type: StorageFill
    contents:
        - id: OxygenTankFilled
        - id: ClothingOuterHardsuitSyndicate
        - id: ClothingHeadHelmetSyndicate
        - id: ClothingMaskGasSyndicate

#Pirate EVA
- type: entity
  id: SuitStorageEVAPirate
  parent: SuitStorageBase
  suffix: Pirate EVA
  components:
  - type: StorageFill
    contents:
        - id: OxygenTankFilled
        - id: ClothingOuterHardsuitPirateEVA
        - id: ClothingMaskGas

#NTSRA Voidsuit
- type: entity
  id: SuitStorageNTSRA
  parent: SuitStorageBase
  suffix: Ancient EVA
  components:
  - type: StorageFill
    contents:
        - id: JetpackVoidFilled
        - id: ClothingOuterHardsuitAncientEVA
        - id: ClothingHeadHelmetAncient
        - id: ClothingMaskBreath

#HARDSUITS
#Basic hardsuit
- type: entity
  id: SuitStorageBasic
  parent: SuitStorageBase
  suffix: Basic Hardsuit
  components:
  - type: StorageFill
    contents:
        - id: OxygenTankFilled
        - id: ClothingOuterHardsuitBasic
        - id: ClothingMaskBreath

#Engineering hardsuit
- type: entity
  id: SuitStorageEngi
  parent: SuitStorageBase
  suffix: Station Engineer
  components:
  - type: StorageFill
    contents:
        - id: OxygenTankFilled
        - id: ClothingShoesBootsMagEng
        - id: ClothingOuterHardsuitEngineering
        - id: ClothingMaskBreath
  - type: AccessReader
    access: [["Engineering"]]

#Atmospherics hardsuit
- type: entity
  id: SuitStorageAtmos
  parent: SuitStorageBase
  suffix: Atmospheric Technician
  components:
  - type: StorageFill
    contents:
        - id: OxygenTankFilled
        - id: ClothingOuterHardsuitAtmos
        - id: ClothingMaskBreath
  - type: AccessReader
    access: [["Atmospherics"]]

#Security hardsuit
- type: entity
  id: SuitStorageSec
  parent: SuitStorageBase
  suffix: Security
  components:
  - type: StorageFill
    contents:
        - id: OxygenTankFilled
          amount: 2 #DeltaV - Double the tanks
        - id: ClothingOuterHardsuitCombatOfficer # DeltaV - ClothingOuterHardsuitSecurity replaced in favour of security combat hardsuit.
          amount: 2 #DeltaV - Double the suits
        - id: ClothingMaskBreath
          amount: 2 #Delta-V Double the masks
        - id: ClothingShoesBootsMagSec
          amount: 2
  - type: AccessReader
    access: [["Security"]]

#CE's hardsuit
- type: entity
  id: SuitStorageCE
  parent: SuitStorageBase
  suffix: Chief Engineer
  components:
  - type: StorageFill
    contents:
        - id: OxygenTankFilled
        - id: JetpackVoidFilled
        - id: ClothingShoesBootsMagAdv
        - id: ClothingOuterHardsuitEngineeringWhite
        - id: ClothingMaskBreath
  - type: AccessReader
    access: [["ChiefEngineer"]]

#CMO's hardsuit
- type: entity
  id: SuitStorageCMO
  parent: SuitStorageBase
  suffix: Chief Medical Officer
  components:
  - type: StorageFill
    contents:
        - id: OxygenTankFilled
        - id: ClothingOuterHardsuitMedical
        - id: ClothingMaskBreathMedical
  - type: AccessReader
    access: [ [ "ChiefMedicalOfficer" ] ]

#RD's hardsuit
- type: entity
  id: SuitStorageRD
  parent: SuitStorageBase
  suffix: Mystagogue # DeltaV - Epistemics Department replacing Science
  components:
  - type: StorageFill
    contents:
        - id: OxygenTankFilled
        - id: ClothingOuterHardsuitRd
        - id: ClothingMaskBreath
  - type: AccessReader
    access: [ [ "ResearchDirector" ] ]

#HOS's hardsuit
- type: entity
  id: SuitStorageHOS
  parent: SuitStorageBase
  suffix: Head of Security
  components:
  - type: StorageFill
    contents:
        - id: OxygenTankFilled
        - id: JetpackSecurityFilled
        - id: ClothingOuterHardsuitCombatHoS # DeltaV - ClothingOuterHardsuitSecurityRed replaced in favour of head of security's advanced combat hardsuit.
        - id: ClothingMaskGasSwat
        - id: ClothingShoesBootsMagSec
  - type: AccessReader
    access: [["HeadOfSecurity"]]

#Warden's hardsuit
- type: entity
  id: SuitStorageWarden
  parent: SuitStorageBase
  suffix: Warden
  components:
  - type: StorageFill
    contents:
        - id: OxygenTankFilled
        - id: ClothingOuterHardsuitCombatWarden # DeltaV - ClothingOuterHardsuitWarden replaced in favour of warden's riot combat hardsuit.
        - id: ClothingMaskBreath
        - id: ClothingShoesBootsMagSec
  - type: AccessReader
    access: [["Armory"]]

#Captain's hardsuit
- type: entity
  id: SuitStorageCaptain
  parent: SuitStorageBase
  suffix: Captain
  components:
  - type: StorageFill
    contents:
        - id: OxygenTankFilled
        - id: ClothingOuterHardsuitCap
        - id: ClothingMaskGasCaptain
        - id: ClothingMaskGasCaptainCombat
  - type: AccessReader
    access: [["Captain"]]

#Salvage hardsuit
- type: entity
  id: SuitStorageSalv
  parent: SuitStorageBase
  suffix: Salvage
  components:
  - type: StorageFill
    contents:
      - id: OxygenTankFilled
      - id: ClothingShoesBootsMag
      - id: ClothingOuterHardsuitSpatio
        orGroup: suit # Pirate salvage suits variety
        prob: 0.8
      - id: ClothingOuterHardsuitPredaceous
        orGroup: suit
        prob: 0.15
      - id: ClothingOuterHardsuitSynla
        orGroup: suit
        prob: 0.15 # Pirate salvage suits variety
      - id: ClothingMaskGasExplorer
  - type: AccessReader
    access: [["Salvage"]]

# DeltaV - Adding Paramedic Suit Storage Unit
#Paramedic hardsuit
- type: entity
  id: SuitStorageParamedic
  parent: SuitStorageBase
  suffix: Paramedic
  components:
  - type: StorageFill
    contents:
        - id: NitrogenTankFilled
        - id: OxygenTankFilled
        - id: ClothingOuterHardsuitVoidParamed
        - id: ClothingMaskBreathMedical
  - type: AccessReader
    access: [ [ "Paramedic" ] ]

# DeltaV - Adding Corpsman Suit Storage Unit
- type: entity
  id: SuitStorageCorpsman
  parent: SuitStorageBase
  suffix: Corpsman
  components:
  - type: StorageFill
    contents:
      - id: NitrogenTankFilled
      - id: OxygenTankFilled
      - id: ClothingOuterHardsuitCombatCorpsman
      - id: ClothingMaskBreath
  - type: AccessReader
    access: [ [ "Corpsman" ] ]

# DeltaV - End Changes

#Blood-red hardsuit
- type: entity
  id: SuitStorageSyndie
  parent: SuitStorageBase
  suffix: Syndicate Hardsuit
  components:
  - type: StorageFill
    contents:
        - id: OxygenTankFilled
        - id: ClothingOuterHardsuitSyndie
        - id: ClothingShoesBootsMagSyndie
        - id: ClothingMaskGasSyndicate

#Pirate Captain's hardsuit
- type: entity
  id: SuitStoragePirateCap
  parent: SuitStorageBase
  suffix: Pirate Captain
  components:
  - type: StorageFill
    contents:
        - id: OxygenTankFilled
        - id: ClothingOuterHardsuitPirateCap
        - id: ClothingMaskGas

#Wizard
- type: entity
  id: SuitStorageWizard
  parent: SuitStorageBase
  suffix: Wizard
  components:
  - type: StorageFill
    contents:
        - id: OxygenTankFilled
        - id: ClothingOuterHardsuitWizard
        - id: ClothingMaskBreath

# Cybersun Armoring Corporation
- type: entity
  id: SuitStorageShanlin
  parent: SuitStorageBase
  suffix: CSA-51a
  components:
    - type: StorageFill
      contents:
          - id: OxygenTankFilled
          - id: ClothingOuterHardsuitShanlinUnpainted
          - id: ClothingMaskBreath

- type: entity
  id: SuitStorageShiwei
  parent: SuitStorageBase
  suffix: CSA-54UA
  components:
    - type: StorageFill
      contents:
          - id: OxygenTankFilled
          - id: ClothingOuterHardsuitShiweiUnpainted
          - id: ClothingMaskBreath
