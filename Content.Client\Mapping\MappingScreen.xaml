﻿<mapping:MappingScreen
    xmlns="https://spacestation14.io"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
    xmlns:menuBar="clr-namespace:Content.Client.UserInterface.Systems.MenuBar.Widgets"
    xmlns:widgets="clr-namespace:Content.Client.UserInterface.Systems.Chat.Widgets"
    xmlns:hotbar="clr-namespace:Content.Client.UserInterface.Systems.Hotbar.Widgets"
    xmlns:graphics="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
    xmlns:mapping="clr-namespace:Content.Client.Mapping"
    VerticalExpand="False"
    VerticalAlignment="Bottom"
    HorizontalAlignment="Center">
    <menuBar:GameTopMenuBar Visible="False" />
    <SplitContainer Name="LeftContainer" HorizontalExpand="True"
                    VerticalExpand="True" SplitWidth="0"
                    StretchDirection="TopLeft" ResizeMode="NotResizable">
        <BoxContainer Orientation="Vertical" VerticalExpand="True" Name="SpawnContainer" MinWidth="200" SetWidth="450">
            <PanelContainer VerticalExpand="True" HorizontalExpand="True">
                <PanelContainer.PanelOverride>
                    <graphics:StyleBoxFlat BackgroundColor="#222222AA" />
                </PanelContainer.PanelOverride>
                <mapping:MappingPrototypeList Name="Entities" Access="Public" VerticalExpand="True" />
            </PanelContainer>
            <controls:HLine Color="#404040" Thickness="2"/>
            <BoxContainer Orientation="Horizontal">
                <Button Name="EntityReplaceButton" Access="Public" ToggleMode="True"
                        SetHeight="48"
                        StyleClasses="ButtonSquare" Text="{Loc 'mapping-replace'}" HorizontalExpand="True" />
                <OptionButton Name="EntityPlacementMode" Access="Public"
                              SetHeight="48"
                              StyleClasses="ButtonSquare" TooltipDelay="0"
                              ToolTip="{Loc 'entity-spawn-window-override-menu-tooltip'}"
                              HorizontalExpand="True" />
                <Button Name="EraseEntityButton" Access="Public" HorizontalExpand="True"
                        SetHeight="48"
                        ToggleMode="True" Text="{Loc 'mapping-erase-entity'}" StyleClasses="ButtonSquare"
                        TooltipDelay="0" />
            </BoxContainer>
        </BoxContainer>
        <SplitContainer Name="RightContainer" HorizontalExpand="True"
                        VerticalExpand="True" SplitWidth="0"
                        StretchDirection="TopLeft" ResizeMode="NotResizable">
            <LayoutContainer Name="ViewportContainer" HorizontalExpand="True" VerticalExpand="True">
                <controls:MainViewport Name="MainViewport"/>
                <hotbar:HotbarGui Name="Hotbar" />
                <PanelContainer Name="Actions" VerticalExpand="True" HorizontalExpand="True"
                                MaxHeight="48">
                    <PanelContainer.PanelOverride>
                        <graphics:StyleBoxFlat BackgroundColor="#222222AA" />
                    </PanelContainer.PanelOverride>
                    <BoxContainer Orientation="Horizontal" Margin="15 10">
                        <mapping:MappingActionsButton Name="Add" Access="Public"
                                                      Disabled="True" ToolTip=""
                                                      Visible="False" />
                        <mapping:MappingActionsButton Name="Fill" Access="Public"
                                                      ToolTip="" Visible="False" />
                        <mapping:MappingActionsButton Name="Grab" Access="Public"
                                                      ToolTip="" Visible="False" />
                        <mapping:MappingActionsButton Name="Move" Access="Public"
                                                      ToolTip="" Visible="False" />
                        <mapping:MappingActionsButton Name="Pick" Access="Public" />
                        <mapping:MappingActionsButton Name="PickDecal" Access="Public" />
                        <mapping:MappingActionsButton Name="Flip" Access="Public" ToggleMode="False"
                                                      ToolTip="{Loc 'mapping-flip-tooltip'}" />
                        <mapping:MappingActionsButton Name="Visibility" Access="Public" ToggleMode="False"
                                                      ToolTip="{Loc 'mapping-visibility-tooltip'}" />
                        <mapping:MappingActionsButton Name="FixGridAtmos" Access="Public"
                                                      ToolTip="{Loc 'mapping-fixgridatmos-tooltip'}" />
                        <mapping:MappingActionsButton Name="RemoveGrid" Access="Public"
                                                      ToolTip="{Loc 'mapping-remove-grid-tooltip'}" />
                        <mapping:MappingActionsButton Name="MoveGrid" Access="Public"
                                                      ToolTip="{Loc 'mapping-move-grid-tooltip'}" />
                        <mapping:MappingActionsButton Name="GridVV" Access="Public"
                                                      ToolTip="{Loc 'mapping-grid-vv-tooltip'}" />
                        <mapping:MappingActionsButton Name="PipesColor" Access="Public"
                                                      ToolTip="{Loc 'mapping-pipes-color-tooltip'}" />
                        <BoxContainer HorizontalExpand="True"/>
                        <mapping:MappingActionsButton Name="ChatButton" Access="Public"
                                                      ToolTip="{Loc 'mapping-chat-button-tooltip'}" />
                    </BoxContainer>
                </PanelContainer>
                <widgets:ChatBox Name="Chat" Access="Public" Visible="False" ReservesSpace="False" Margin="3 51 0 0" />
            </LayoutContainer>
            <BoxContainer Orientation="Vertical" VerticalExpand="True" MinWidth="200" SetWidth="500">
                <PanelContainer VerticalExpand="True" HorizontalExpand="True">
                    <PanelContainer.PanelOverride>
                        <graphics:StyleBoxFlat BackgroundColor="#222222AA" />
                    </PanelContainer.PanelOverride>
                    <mapping:MappingPrototypeList Name="Tiles" Access="Public" VerticalExpand="True" Margin="1 5" />
                </PanelContainer>
                <controls:HLine Color="#404040" Thickness="2"/>
                <PanelContainer VerticalExpand="True" HorizontalExpand="True">
                    <PanelContainer.PanelOverride>
                        <graphics:StyleBoxFlat BackgroundColor="#222222AA" />
                    </PanelContainer.PanelOverride>
                    <mapping:MappingPrototypeList Name="Decals" Access="Public" VerticalExpand="True" Margin="1 5"
                                                  Gallery="True"/>
                </PanelContainer>
                <controls:HLine Color="#404040" Thickness="2"/>
                <BoxContainer Orientation="Horizontal">
                    <PanelContainer VerticalExpand="True" HorizontalExpand="True">
                        <PanelContainer.PanelOverride>
                            <graphics:StyleBoxFlat BackgroundColor="#222222AA" />
                        </PanelContainer.PanelOverride>
                        <BoxContainer Orientation="Vertical" HorizontalExpand="True" Margin="3 3">
                            <ColorSelectorSliders Name="DecalColorPicker" IsAlphaVisible="True" />
                            <Button Name="DecalPickerOpen" Text="{Loc 'decal-placer-window-palette'}" />
                        </BoxContainer>
                    </PanelContainer>
                    <PanelContainer VerticalExpand="True" HorizontalExpand="True">
                        <PanelContainer.PanelOverride>
                            <graphics:StyleBoxFlat BackgroundColor="#222222AA" />
                        </PanelContainer.PanelOverride>
                        <BoxContainer Orientation="Vertical" HorizontalExpand="True" Margin="3 3">
                            <CheckBox Name="DecalEnableAuto" Margin="0 0 0 10"
                                      Text="{Loc 'decal-placer-window-enable-auto'}" />
                            <CheckBox Name="DecalEnableColor"
                                      Text="{Loc 'decal-placer-window-use-color'}" />
                            <CheckBox Name="DecalEnableSnap"
                                      Text="{Loc 'decal-placer-window-enable-snap'}" />
                            <CheckBox Name="DecalEnableCleanable"
                                      Text="{Loc 'decal-placer-window-enable-cleanable'}" />
                            <BoxContainer Name="DecalSpinBoxContainer" Orientation="Horizontal">
                                <Label Text="{Loc 'decal-placer-window-rotation'}" Margin="0 0 0 1" />
                            </BoxContainer>
                            <BoxContainer Orientation="Horizontal">
                                <Label Text="{Loc 'decal-placer-window-zindex'}" Margin="0 0 0 1" />
                                <SpinBox Name="DecalZIndexSpinBox" HorizontalExpand="True" />
                            </BoxContainer>
                        </BoxContainer>
                    </PanelContainer>
                </BoxContainer>
                <controls:HLine Color="#404040" Thickness="2"/>
                <BoxContainer Orientation="Horizontal">
                    <Button Name="EraseTileButton" Access="Public" HorizontalExpand="True"
                            SetHeight="48"
                            ToggleMode="True" Text="{Loc 'mapping-erase-tile'}" StyleClasses="ButtonSquare" />
                    <Button Name="EraseDecalButton" Access="Public" HorizontalExpand="True"
                            SetHeight="48"
                            ToggleMode="True" Text="{Loc 'mapping-erase-decal'}" StyleClasses="ButtonSquare" />
                </BoxContainer>
            </BoxContainer>
        </SplitContainer>
    </SplitContainer>
</mapping:MappingScreen>
