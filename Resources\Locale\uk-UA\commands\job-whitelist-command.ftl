cmd-jobwhitelist-job-does-not-exist = Робота {$job} не існує.
cmd-jobwhitelist-player-not-found = Гравець {$player} не знайдений.
cmd-jobwhitelist-hint-player = [гравець]
cmd-jobwhitelist-hint-job = [робота]
cmd-jobwhitelistadd-desc = Дозволяє гравцеві виконувати роботу з білого списку.
cmd-jobwhitelistadd-help = Використання: jobwhitelistadd <ім'я користувача> <посада>
cmd-jobwhitelistadd-already-whitelisted = {$player} вже внесено до білого списку для гри як {$jobId} .({$jobName}).
cmd-jobwhitelistadd-added = Додано {$player} до білого списку {$jobId} ({$jobName}).
cmd-jobwhitelistget-desc = Отримує всі роботи, за які гравця було занесено до білого списку.
cmd-jobwhitelistget-help = Використання: jobwhitelistadd <ім'я користувача>
cmd-jobwhitelistget-whitelisted-none = Гравця {$player} не внесено до білого списку на жодній роботі.
cmd-jobwhitelistget-whitelisted-for = "Гравець {$player} внесений до білого списку за:
{$jobs}"

cmd-jobwhitelistremove-desc = Видаляє можливість гравця виконувати роботу з білого списку.
cmd-jobwhitelistremove-help = Використання: jobwhitelistadd <ім'я користувача> <посада>
cmd-jobwhitelistremove-was-not-whitelisted = {$player} не було внесено до білого списку, щоб грати як {$jobId} ({$jobName}).
cmd-jobwhitelistremove-removed = Вилучено {$player} з білого списку для {$jobId} ({$jobName}).
