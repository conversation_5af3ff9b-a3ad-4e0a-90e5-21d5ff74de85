- type: job
  id: MedicalDoctor
  name: job-name-doctor
  description: job-description-doctor
  playTimeTracker: JobMedicalDoctor
  requirements:
    - !type:CharacterDepartmentTimeRequirement
      department: Medical
      min: 14400 #4 hrs
    - !type:CharacterEmployerRequirement
      employers:
      - ZengHuPharmaceuticals
      - Interdyne
      - NanoTrasen
  startingGear: DoctorGear
  icon: "JobIconMedicalDoctor"
  supervisors: job-supervisors-cmo
  access:
  - Medical
  - Maintenance
  extendedAccess:
  - Chemistry
  - Paramedic # DeltaV - Add Paramedic access
  special:
  - !type:AddComponentSpecial
    components:
    - type: CPRTraining
    - type: SurgerySpeedModifier
      speedModifier: 1.75

- type: startingGear
  id: DoctorGear
  subGear:
  - DoctorPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitMedicalDoctor
    back: ClothingBackpackMedicalFilled
    shoes: ClothingShoesColorWhite
    id: MedicalPDA
    ears: ClothingHeadsetMedical
  innerClothingSkirt: ClothingUniformJumpskirtMedicalDoctor
  satchel: ClothingBackpackSatchelMedicalFilled
  duffelbag: ClothingBackpackDuffelMedicalFilled

- type: startingGear
  id: DoctorPlasmamanGear
  parent: BasePlasmamanGear
  equipment:
    jumpsuit: ClothingUniformEnvirosuitMedicalDoctor
    head: ClothingHeadEnvirohelmMedicalDoctor
    gloves: ClothingHandsGlovesEnviroglovesWhite
