- type: entity
  parent: DrinkBase
  id: DrinkJuiceBoxBaseFull
  abstract: true
  components:
  - type: Openable
    sound:
      collection: bottleOpenSounds #Could use a new sound someday ¯\_(ツ)_/¯
  - type: Sealable
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 20
  - type: Item
    size: Small
  - type: MeleeWeapon
    soundNoDamage:
      path: "/Audio/Effects/Fluids/splat.ogg"
    damage:
      types:
        Blunt: 0
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 10
      behaviors:
      - !type:SpillBehavior { }
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
        #In future maybe add generic plastic scrap trash/debris
  - type: TrashOnSolutionEmpty
    solution: drink
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.OpenableVisuals.Opened:
        enum.OpenableVisuals.Layer:
          True: {state: "icon_open"}
          False: {state: "icon"}
  - type: Sprite
    layers:
      - state: icon
        map: ["enum.OpenableVisuals.Layer"]

- type: entity
  parent: DrinkJuiceBoxBaseFull
  id: DrinkJuiceOrangeJuicebox
  name: "коробка апельсинового соку"
  description: "Чудове джерело вітамінів. Будьте здорові!"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: JuiceOrange
          Quantity: 20
  - type: Sprite
    sprite: DeltaV/Objects/Consumable/Drinks/juiceboxorange.rsi

- type: entity
  parent: DrinkJuiceBoxBaseFull
  id: DrinkJuicePineappleJuicebox
  name: "коробка ананасового соку"
  description: "Всіма улюблений сік."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: JuicePineapple
          Quantity: 20
  - type: Sprite
    sprite: DeltaV/Objects/Consumable/Drinks/juiceboxpineapple.rsi

- type: entity
  parent: DrinkJuiceBoxBaseFull
  id: DrinkJuiceAppleJuicebox
  name: "коробка яблучного соку"
  description: "Солодкий яблучний сік. Не запізнитися до школи!"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: JuiceApple
          Quantity: 20
  - type: Sprite
    sprite: DeltaV/Objects/Consumable/Drinks/juiceboxapple.rsi

- type: entity
  parent: DrinkJuiceBoxBaseFull
  id: DrinkJuiceGrapeJuicebox
  name: "коробка виноградного соку"
  description: "Смачний виноградний сік у веселій маленькій тарі. Безалкогольний!"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: JuiceGrape
          Quantity: 20
  - type: Sprite
    sprite: DeltaV/Objects/Consumable/Drinks/juiceboxgrape.rsi

- type: entity
  parent: DrinkJuiceBoxBaseFull
  id: DrinkChocolateJuicebox
  name: "коробка шоколадного молочного соку"
  description: "Смачний шоколадний сік та молоко у маленькій коробочці. Містить теобромін."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: HotCocoa
          Quantity: 10
        - ReagentId: Milk # The milk of chocolate milk
          Quantity: 10
  - type: Sprite
    sprite: DeltaV/Objects/Consumable/Drinks/juiceboxchocolate.rsi
