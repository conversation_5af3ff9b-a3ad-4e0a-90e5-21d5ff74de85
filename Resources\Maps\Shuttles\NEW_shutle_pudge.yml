meta:
  format: 6
  postmapinit: false
tilemap:
  0: Space
  30: FloorDark
  77: FloorRGlass
  85: FloorShuttleRed
  119: FloorWood
  121: <PERSON><PERSON><PERSON>
  122: Plating
entities:
- proto: ""
  entities:
  - uid: 1
    components:
    - type: MetaData
      name: <PERSON><PERSON><PERSON>uddle CS108
    - type: Transform
      rot: 4.2101240158081055 rad
      pos: -273.50327,-465.2048
      parent: invalid
    - type: MapGrid
      chunks:
        -1,-1:
          ind: -1,-1
          tiles: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAegAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAegAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAegAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAegAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAegAAAAAAeQAAAAAAeQAAAAAAeQAAAAAAeQAAAAAAeQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAeQAAAAAAeQAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAeQAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAAAAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAHgAAAAABHgAAAAAAHgAAAAACHgAAAAACHgAAAAADHgAAAAAAegAAAAAAeQAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAHgAAAAABHgAAAAACHgAAAAABHgAAAAAAHgAAAAADegAAAAAAHgAAAAACHgAAAAAAegAAAAAAAAAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAHgAAAAADegAAAAAAHgAAAAADegAAAAAAeQAAAAAAeQAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAeQAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAA
          version: 6
        0,-1:
          ind: 0,-1
          tiles: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAeQAAAAAAAAAAAAAAeQAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAeQAAAAAAAAAAAAAAHgAAAAADHgAAAAADdwAAAAABdwAAAAABegAAAAAAHgAAAAABegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAHgAAAAAAVQAAAAAAVQAAAAAAegAAAAAAAAAAAAAAHgAAAAABegAAAAAAegAAAAAAegAAAAAAegAAAAAAHgAAAAAAHgAAAAACTQAAAAADTQAAAAAATQAAAAADHgAAAAACHgAAAAACVQAAAAAAVQAAAAAAegAAAAAAAAAAAAAAegAAAAAAegAAAAAAHgAAAAABHgAAAAADHgAAAAADHgAAAAACegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAHgAAAAABVQAAAAAAVQAAAAAAegAAAAAAAAAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAeQAAAAAAAAAAAAAAeQAAAAAAegAAAAAAegAAAAAAegAAAAAAegAAAAAAeQAAAAAAAAAAAAAA
          version: 6
        -1,0:
          ind: -1,0
          tiles: AAAAAAAAegAAAAAAeQAAAAAAeQAAAAAAeQAAAAAAeQAAAAAAeQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAegAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAegAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAegAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAegAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
          version: 6
        -2,-1:
          ind: -2,-1
          tiles: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAeQAAAAAAeQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAeQAAAAAAegAAAAAAegAAAAAAegAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAegAAAAAAegAAAAAAegAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAeQAAAAAAeQAAAAAAegAAAAAAegAAAAAAegAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAeQAAAAAAeQAAAAAA
          version: 6
    - type: Broadphase
    - type: Physics
      bodyStatus: InAir
      angularDamping: 0.05
      linearDamping: 0.05
      fixedRotation: False
      bodyType: Dynamic
    - type: Fixtures
      fixtures: {}
    - type: OccluderTree
    - type: Shuttle
    - type: SpreaderGrid
      updateAccumulator: 0.23416786
    - type: Gravity
      gravityShakeSound: !type:SoundPathSpecifier
        path: /Audio/Effects/alert.ogg
    - type: DecalGrid
      chunkCollection:
        version: 2
        nodes:
        - node:
            angle: -1.5707963267948966 rad
            color: '#FFFFFFFF'
            id: Arrows
          decals:
            1: -7,-3
        - node:
            color: '#DE3A3A66'
            id: BrickTileWhiteCornerNe
          decals:
            23: 5,-2
        - node:
            color: '#DE3A3A74'
            id: BrickTileWhiteEndS
          decals:
            74: 5,-4
        - node:
            cleanable: True
            color: '#863333FF'
            id: BrickTileWhiteInnerNe
          decals:
            45: 10,-5
        - node:
            cleanable: True
            color: '#DE3A3A74'
            id: BrickTileWhiteInnerNe
          decals:
            67: 5,-3
        - node:
            color: '#DE3A3A66'
            id: BrickTileWhiteInnerNw
          decals:
            13: 11,-3
        - node:
            color: '#DE3A3A74'
            id: BrickTileWhiteInnerSe
          decals:
            68: 5,-3
        - node:
            color: '#DE3A3A66'
            id: BrickTileWhiteInnerSw
          decals:
            12: 11,-3
        - node:
            color: '#DE3A3A74'
            id: BrickTileWhiteInnerSw
          decals:
            76: 5,-2
        - node:
            color: '#DE3A3A66'
            id: BrickTileWhiteLineN
          decals:
            17: 10,-3
            24: 4,-2
            25: 3,-2
            27: -2,-2
            28: -4,-2
        - node:
            color: '#DE3A3A74'
            id: BrickTileWhiteLineN
          decals:
            69: 6,-3
            81: 2,-2
        - node:
            color: '#DE3A3A66'
            id: BrickTileWhiteLineS
          decals:
            16: 10,-3
        - node:
            color: '#DE3A3A74'
            id: BrickTileWhiteLineS
          decals:
            70: 6,-3
            77: 4,-2
            78: 3,-2
            80: 2,-2
        - node:
            color: '#DE3A3A66'
            id: BrickTileWhiteLineW
          decals:
            20: 11,-4
            21: 11,-2
        - node:
            color: '#DE3A3A74'
            id: BrickTileWhiteLineW
          decals:
            75: 5,-3
        - node:
            color: '#FFFFFFFF'
            id: Caution
          decals:
            0: -7,-3
        - node:
            color: '#FFFFFFFF'
            id: DirtHeavy
          decals:
            36: 0,-3
        - node:
            color: '#FFFFFFFF'
            id: DirtLight
          decals:
            31: -2,-3
            38: 3,-2
            39: 4,-2
            40: -1,-3
            41: 11,-4
        - node:
            color: '#FFFFFFFF'
            id: DirtMedium
          decals:
            32: -2,-4
            33: -3,-4
            34: -6,-3
            35: -4,-4
            42: 11,-3
            44: -4,-3
        - node:
            color: '#DE3A3A66'
            id: HalfTileOverlayGreyscale180
          decals:
            5: 0,-4
            6: -1,-4
            7: -2,-4
            8: -3,-4
            9: -4,-4
            10: -5,-4
            11: -6,-4
        - node:
            color: '#FFFFFFFF'
            id: WarnCornerSmallNE
          decals:
            4: -2,-3
        - node:
            color: '#FFFFFFFF'
            id: WarnLineE
          decals:
            29: -2,-2
        - node:
            color: '#FFFFFFFF'
            id: WarnLineS
          decals:
            82: 2,-2
        - node:
            color: '#FFFFFFFF'
            id: WarnLineW
          decals:
            2: 0,-3
            3: -1,-3
    - type: GridAtmosphere
      version: 2
      data:
        tiles:
          -1,-1:
            0: 3295
            1: 256
            8: 61984
          0,-1:
            0: 7967
            8: 57568
          -4,-2:
            2: 15360
            3: 546
            8: 49152
          -4,-1:
            0: 16
            2: 12320
            4: 2184
            8: 50501
          -4,-3:
            3: 8704
          -3,-2:
            2: 1792
            0: 4096
            8: 57344
          -3,-1:
            5: 321
            6: 530
            0: 4096
            4: 1060
            7: 128
            8: 59400
          -2,-2:
            2: 4096
            8: 57344
          -2,-1:
            0: 1276
            2: 4096
            8: 60163
          1,-1:
            0: 994
            8: 31773
          2,-1:
            0: 2296
            2: 8192
            8: 50951
          2,-2:
            2: 8192
            8: 49152
          3,-2:
            2: 16384
            8: 12288
          3,-1:
            0: 819
            2: 16384
            8: 13380
          -4,0:
            3: 8738
            2: 12
          -4,1:
            3: 2
          -3,0:
            2: 7
          -6,-1:
            2: 2048
          -5,-1:
            2: 49409
            0: 192
            8: 3630
          -1,-2:
            8: 61440
          0,-2:
            8: 61440
          1,-2:
            8: 28672
        uniqueMixes:
        - volume: 2500
          temperature: 293.15
          moles:
          - 21.824879
          - 82.10312
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
        - volume: 2500
          temperature: 293.1495
          moles:
          - 9.8897505
          - 37.204296
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
        - volume: 2500
          immutable: True
          moles:
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
        - volume: 2500
          moles:
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
        - volume: 2500
          temperature: 293.15
          moles:
          - 21.690569
          - 81.597855
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
        - volume: 2500
          temperature: 293.15
          moles:
          - 21.690567
          - 81.597855
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
        - volume: 2500
          temperature: 293.15
          moles:
          - 21.690569
          - 81.59786
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
        - volume: 2500
          temperature: 293.15
          moles:
          - 21.69061
          - 81.598015
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
        - volume: 2500
          temperature: 293.15
          moles:
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
          - 0
        chunkSize: 4
    - type: GasTileOverlay
    - type: RadiationGridResistance
    - type: GridPathfinding
    - type: NavMap
  - uid: 147
    components:
    - type: MetaData
    - type: Transform
      parent: 146
    - type: Solution
      solution:
        maxVol: 40
        name: smokable
        reagents:
        - ReagentId: Nicotine
          Quantity: 10
        - ReagentId: Omnizine
          Quantity: 30
    - type: ContainedSolution
      containerName: smokable
      container: 146
  - uid: 150
    components:
    - type: MetaData
    - type: Transform
      parent: 149
    - type: Solution
      solution:
        maxVol: 10
        name: food
        reagents:
        - ReagentId: Fiber
          Quantity: 10
    - type: ContainedSolution
      containerName: food
      container: 149
  - uid: 153
    components:
    - type: MetaData
    - type: Transform
      parent: 152
    - type: Solution
      solution:
        maxVol: 10
        name: food
        reagents:
        - ReagentId: Fiber
          Quantity: 10
    - type: ContainedSolution
      containerName: food
      container: 152
  - uid: 155
    components:
    - type: MetaData
    - type: Transform
      parent: 154
    - type: Solution
      solution:
        maxVol: 10
        name: food
        reagents:
        - ReagentId: Fiber
          Quantity: 10
    - type: ContainedSolution
      containerName: food
      container: 154
  - uid: 157
    components:
    - type: MetaData
    - type: Transform
      parent: 156
    - type: Solution
      solution:
        maxVol: 30
        name: food
        reagents:
        - ReagentId: Fiber
          Quantity: 30
    - type: ContainedSolution
      containerName: food
      container: 156
  - uid: 159
    components:
    - type: MetaData
    - type: Transform
      parent: 158
    - type: Solution
      solution:
        maxVol: 30
        name: food
        reagents:
        - ReagentId: Fiber
          Quantity: 30
    - type: ContainedSolution
      containerName: food
      container: 158
  - uid: 161
    components:
    - type: MetaData
    - type: Transform
      parent: 160
    - type: Solution
      solution:
        maxVol: 30
        name: food
        reagents:
        - ReagentId: Fiber
          Quantity: 30
    - type: ContainedSolution
      containerName: food
      container: 160
  - uid: 168
    components:
    - type: MetaData
    - type: Transform
      parent: 167
    - type: Solution
      solution:
        maxVol: 30
        name: food
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
    - type: ContainedSolution
      containerName: food
      container: 167
  - uid: 170
    components:
    - type: MetaData
    - type: Transform
      parent: 169
    - type: Solution
      solution:
        maxVol: 20
        name: food
        reagents:
        - ReagentId: Nutriment
          Quantity: 15
    - type: ContainedSolution
      containerName: food
      container: 169
  - uid: 218
    components:
    - type: MetaData
    - type: Transform
      parent: 217
    - type: Solution
      solution:
        maxVol: 10
        name: gold
        reagents:
        - ReagentId: Gold
          Quantity: 10
    - type: ContainedSolution
      containerName: gold
      container: 217
  - uid: 220
    components:
    - type: MetaData
    - type: Transform
      parent: 219
    - type: Solution
      solution:
        maxVol: 10
        name: gold
        reagents:
        - ReagentId: Gold
          Quantity: 10
    - type: ContainedSolution
      containerName: gold
      container: 219
  - uid: 226
    components:
    - type: MetaData
    - type: Transform
      parent: 225
    - type: Solution
      solution:
        maxVol: 6
        name: hvcable
        reagents:
        - ReagentId: Iron
          Quantity: 3
        - ReagentId: Copper
          Quantity: 2
        - ReagentId: Carbon
          Quantity: 1
    - type: ContainedSolution
      containerName: hvcable
      container: 225
  - uid: 263
    components:
    - type: MetaData
    - type: Transform
      parent: 262
    - type: Solution
      solution:
        maxVol: 5
        name: battery
        reagents: []
    - type: ContainedSolution
      containerName: battery
      container: 262
  - uid: 265
    components:
    - type: MetaData
    - type: Transform
      parent: 264
    - type: Solution
      solution:
        maxVol: 5
        name: battery
        reagents: []
    - type: ContainedSolution
      containerName: battery
      container: 264
  - uid: 267
    components:
    - type: MetaData
    - type: Transform
      parent: 266
    - type: Solution
      solution:
        maxVol: 5
        name: battery
        reagents: []
    - type: ContainedSolution
      containerName: battery
      container: 266
  - uid: 269
    components:
    - type: MetaData
    - type: Transform
      parent: 268
    - type: Solution
      solution:
        maxVol: 5
        name: battery
        reagents: []
    - type: ContainedSolution
      containerName: battery
      container: 268
  - uid: 271
    components:
    - type: MetaData
    - type: Transform
      parent: 270
    - type: Solution
      solution:
        maxVol: 6
        name: hvcable
        reagents:
        - ReagentId: Iron
          Quantity: 3
        - ReagentId: Copper
          Quantity: 2
        - ReagentId: Carbon
          Quantity: 1
    - type: ContainedSolution
      containerName: hvcable
      container: 270
  - uid: 286
    components:
    - type: MetaData
    - type: Transform
      parent: 285
    - type: Solution
      solution:
        maxVol: 5
        name: battery
        reagents: []
    - type: ContainedSolution
      containerName: battery
      container: 285
  - uid: 288
    components:
    - type: MetaData
    - type: Transform
      parent: 287
    - type: Solution
      solution:
        maxVol: 5
        name: mvcable
        reagents:
        - ReagentId: Iron
          Quantity: 3
        - ReagentId: Copper
          Quantity: 2
    - type: ContainedSolution
      containerName: mvcable
      container: 287
  - uid: 290
    components:
    - type: MetaData
    - type: Transform
      parent: 289
    - type: Solution
      solution:
        maxVol: 6
        name: hvcable
        reagents:
        - ReagentId: Iron
          Quantity: 3
        - ReagentId: Copper
          Quantity: 2
        - ReagentId: Carbon
          Quantity: 1
    - type: ContainedSolution
      containerName: hvcable
      container: 289
  - uid: 394
    components:
    - type: MetaData
    - type: Transform
      parent: 393
    - type: Solution
      solution:
        maxVol: 5
        name: mvcable
        reagents:
        - ReagentId: Iron
          Quantity: 3
        - ReagentId: Copper
          Quantity: 2
    - type: ContainedSolution
      containerName: mvcable
      container: 393
- proto: AirCanister
  entities:
  - uid: 2
    components:
    - type: Transform
      pos: -17.5,-2.5
      parent: 1
    - type: AtmosDevice
      joinedGrid: 1
- proto: AirlockExternalGlassShuttleSyndicateLocked
  entities:
  - uid: 3
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: 0.5,-0.5
      parent: 1
    - type: ContainerContainer
      containers:
        board: !type:Container
          showEnts: False
          occludes: True
          ents:
          - 4
    - type: DeviceNetwork
      address: 2057-ED29
      receiveFrequency: 1280
- proto: AirlockExternalShuttleSyndicateLocked
  entities:
  - uid: 5
    components:
    - type: Transform
      pos: -11.5,-4.5
      parent: 1
    - type: ContainerContainer
      containers:
        board: !type:Container
          showEnts: False
          occludes: True
          ents:
          - 6
    - type: DeviceNetwork
      address: 7251-D403
      receiveFrequency: 1280
  - uid: 7
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: -11.5,-0.5
      parent: 1
    - type: ContainerContainer
      containers:
        board: !type:Container
          showEnts: False
          occludes: True
          ents:
          - 8
    - type: DeviceNetwork
      address: 6CA2-3E9E
      receiveFrequency: 1280
- proto: AirlockSyndicateGlassLocked
  entities:
  - uid: 9
    components:
    - type: Transform
      pos: -2.5,-3.5
      parent: 1
    - type: ContainerContainer
      containers:
        board: !type:Container
          showEnts: False
          occludes: True
          ents:
          - 10
    - type: DeviceNetwork
      address: 238F-FA4E
      receiveFrequency: 1280
  - uid: 11
    components:
    - type: Transform
      pos: -7.5,-2.5
      parent: 1
    - type: ContainerContainer
      containers:
        board: !type:Container
          showEnts: False
          occludes: True
          ents:
          - 12
    - type: DeviceNetwork
      address: 7C6A-8265
      receiveFrequency: 1280
  - uid: 13
    components:
    - type: Transform
      pos: -15.5,-2.5
      parent: 1
    - type: ContainerContainer
      containers:
        board: !type:Container
          showEnts: False
          occludes: True
          ents:
          - 14
    - type: DeviceNetwork
      address: 783D-AC80
      receiveFrequency: 1280
- proto: AirlockSyndicateLocked
  entities:
  - uid: 15
    components:
    - type: Transform
      pos: 10.5,-2.5
      parent: 1
    - type: ContainerContainer
      containers:
        board: !type:Container
          showEnts: False
          occludes: True
          ents:
          - 16
    - type: DeviceNetwork
      address: 2271-A8FC
      receiveFrequency: 1280
  - uid: 19
    components:
    - type: Transform
      pos: 1.5,-3.5
      parent: 1
    - type: ContainerContainer
      containers:
        board: !type:Container
          showEnts: False
          occludes: True
          ents:
          - 20
    - type: DeviceNetwork
      address: 6DBD-4B3F
      receiveFrequency: 1280
  - uid: 405
    components:
    - type: Transform
      pos: 6.5,-2.5
      parent: 1
- proto: APCBasic
  entities:
  - uid: 21
    components:
    - type: Transform
      pos: 5.5,-0.5
      parent: 1
    - type: Apc
      lastUiUpdate: 291.9321656
      lastChargeStateTime: -462.4153573
    - type: PowerNetworkBattery
      loadingNetworkDemand: 245
      currentReceiving: 244.92285
      currentSupply: 245
      supplyRampPosition: 0.07714844
  - uid: 22
    components:
    - type: Transform
      pos: -4.5,-1.5
      parent: 1
    - type: Apc
      lastUiUpdate: 291.9321656
      lastChargeStateTime: -462.4153573
    - type: PowerNetworkBattery
      loadingNetworkDemand: 120
      currentReceiving: 120.00047
      currentSupply: 120
      supplyRampPosition: -0.00048065186
  - uid: 23
    components:
    - type: Transform
      pos: 9.5,-1.5
      parent: 1
    - type: Apc
      lastUiUpdate: 291.9321656
      lastChargeStateTime: -462.4153573
    - type: PowerNetworkBattery
      loadingNetworkDemand: 345
      currentReceiving: 345.00137
      currentSupply: 345
- proto: AtmosDeviceFanTiny
  entities:
  - uid: 24
    components:
    - type: Transform
      pos: -11.5,-0.5
      parent: 1
  - uid: 25
    components:
    - type: Transform
      pos: -11.5,-4.5
      parent: 1
  - uid: 26
    components:
    - type: Transform
      pos: 0.5,-0.5
      parent: 1
- proto: Bed
  entities:
  - uid: 27
    components:
    - type: Transform
      pos: 3.5,-3.5
      parent: 1
- proto: BedsheetSyndie
  entities:
  - uid: 28
    components:
    - type: Transform
      rot: -6.283185005187988 rad
      pos: 3.499939,-3.5000153
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
- proto: CableApcExtension
  entities:
  - uid: 29
    components:
    - type: Transform
      pos: 9.5,-1.5
      parent: 1
  - uid: 30
    components:
    - type: Transform
      pos: 9.5,-2.5
      parent: 1
  - uid: 31
    components:
    - type: Transform
      pos: 10.5,-2.5
      parent: 1
  - uid: 32
    components:
    - type: Transform
      pos: 11.5,-2.5
      parent: 1
  - uid: 33
    components:
    - type: Transform
      pos: 12.5,-2.5
      parent: 1
  - uid: 34
    components:
    - type: Transform
      pos: 8.5,-2.5
      parent: 1
  - uid: 35
    components:
    - type: Transform
      pos: 5.5,-0.5
      parent: 1
  - uid: 36
    components:
    - type: Transform
      pos: 5.5,-1.5
      parent: 1
  - uid: 37
    components:
    - type: Transform
      pos: 5.5,-2.5
      parent: 1
  - uid: 38
    components:
    - type: Transform
      pos: 5.5,-3.5
      parent: 1
  - uid: 39
    components:
    - type: Transform
      pos: 4.5,-1.5
      parent: 1
  - uid: 40
    components:
    - type: Transform
      pos: 3.5,-1.5
      parent: 1
  - uid: 41
    components:
    - type: Transform
      pos: 2.5,-1.5
      parent: 1
  - uid: 42
    components:
    - type: Transform
      pos: 1.5,-1.5
      parent: 1
  - uid: 43
    components:
    - type: Transform
      pos: 0.5,-1.5
      parent: 1
  - uid: 44
    components:
    - type: Transform
      pos: -0.5,-1.5
      parent: 1
  - uid: 45
    components:
    - type: Transform
      pos: -0.5,-2.5
      parent: 1
  - uid: 46
    components:
    - type: Transform
      pos: -0.5,-3.5
      parent: 1
  - uid: 47
    components:
    - type: Transform
      pos: 0.5,-3.5
      parent: 1
  - uid: 48
    components:
    - type: Transform
      pos: 1.5,-3.5
      parent: 1
  - uid: 49
    components:
    - type: Transform
      pos: 2.5,-3.5
      parent: 1
  - uid: 50
    components:
    - type: Transform
      pos: 3.5,-3.5
      parent: 1
  - uid: 51
    components:
    - type: Transform
      pos: -4.5,-1.5
      parent: 1
  - uid: 52
    components:
    - type: Transform
      pos: -4.5,-2.5
      parent: 1
  - uid: 53
    components:
    - type: Transform
      pos: -4.5,-3.5
      parent: 1
  - uid: 54
    components:
    - type: Transform
      pos: -3.5,-3.5
      parent: 1
  - uid: 55
    components:
    - type: Transform
      pos: -5.5,-2.5
      parent: 1
  - uid: 56
    components:
    - type: Transform
      pos: -6.5,-2.5
      parent: 1
  - uid: 57
    components:
    - type: Transform
      pos: -7.5,-2.5
      parent: 1
  - uid: 58
    components:
    - type: Transform
      pos: -8.5,-2.5
      parent: 1
  - uid: 59
    components:
    - type: Transform
      pos: -9.5,-2.5
      parent: 1
  - uid: 60
    components:
    - type: Transform
      pos: -11.5,-2.5
      parent: 1
  - uid: 61
    components:
    - type: Transform
      pos: -11.5,-1.5
      parent: 1
  - uid: 62
    components:
    - type: Transform
      pos: -12.5,-1.5
      parent: 1
  - uid: 63
    components:
    - type: Transform
      pos: -11.5,-3.5
      parent: 1
  - uid: 64
    components:
    - type: Transform
      pos: -12.5,-3.5
      parent: 1
  - uid: 65
    components:
    - type: Transform
      pos: -9.5,-1.5
      parent: 1
  - uid: 66
    components:
    - type: Transform
      pos: -9.5,-3.5
      parent: 1
  - uid: 67
    components:
    - type: Transform
      pos: -10.5,-3.5
      parent: 1
  - uid: 68
    components:
    - type: Transform
      pos: -10.5,-1.5
      parent: 1
  - uid: 69
    components:
    - type: Transform
      pos: -12.5,-4.5
      parent: 1
  - uid: 70
    components:
    - type: Transform
      pos: -12.5,-5.5
      parent: 1
  - uid: 71
    components:
    - type: Transform
      pos: -13.5,-5.5
      parent: 1
  - uid: 72
    components:
    - type: Transform
      pos: -14.5,-5.5
      parent: 1
  - uid: 73
    components:
    - type: Transform
      pos: -14.5,-4.5
      parent: 1
  - uid: 74
    components:
    - type: Transform
      pos: -15.5,-4.5
      parent: 1
  - uid: 296
    components:
    - type: Transform
      pos: -16.5,-4.5
      parent: 1
  - uid: 297
    components:
    - type: Transform
      pos: -17.5,-4.5
      parent: 1
  - uid: 304
    components:
    - type: Transform
      pos: -17.5,-3.5
      parent: 1
  - uid: 305
    components:
    - type: Transform
      pos: -12.5,-0.5
      parent: 1
  - uid: 306
    components:
    - type: Transform
      pos: -12.5,0.5
      parent: 1
  - uid: 307
    components:
    - type: Transform
      pos: -13.5,0.5
      parent: 1
  - uid: 308
    components:
    - type: Transform
      pos: -14.5,0.5
      parent: 1
  - uid: 309
    components:
    - type: Transform
      pos: -14.5,-0.5
      parent: 1
  - uid: 310
    components:
    - type: Transform
      pos: -15.5,-0.5
      parent: 1
  - uid: 311
    components:
    - type: Transform
      pos: -16.5,-0.5
      parent: 1
  - uid: 312
    components:
    - type: Transform
      pos: -17.5,-0.5
      parent: 1
  - uid: 401
    components:
    - type: Transform
      pos: -17.5,-1.5
      parent: 1
  - uid: 403
    components:
    - type: Transform
      pos: 7.5,-2.5
      parent: 1
  - uid: 404
    components:
    - type: Transform
      pos: 6.5,-2.5
      parent: 1
- proto: CableHV
  entities:
  - uid: 75
    components:
    - type: Transform
      pos: -14.5,-9.5
      parent: 1
  - uid: 76
    components:
    - type: Transform
      pos: -14.5,-8.5
      parent: 1
  - uid: 77
    components:
    - type: Transform
      pos: -14.5,-7.5
      parent: 1
  - uid: 78
    components:
    - type: Transform
      pos: -14.5,-6.5
      parent: 1
  - uid: 79
    components:
    - type: Transform
      pos: -14.5,-5.5
      parent: 1
  - uid: 80
    components:
    - type: Transform
      pos: -14.5,4.5
      parent: 1
  - uid: 81
    components:
    - type: Transform
      pos: -14.5,3.5
      parent: 1
  - uid: 82
    components:
    - type: Transform
      pos: -14.5,2.5
      parent: 1
  - uid: 83
    components:
    - type: Transform
      pos: -14.5,1.5
      parent: 1
  - uid: 84
    components:
    - type: Transform
      pos: -14.5,0.5
      parent: 1
  - uid: 85
    components:
    - type: Transform
      pos: -13.5,0.5
      parent: 1
  - uid: 86
    components:
    - type: Transform
      pos: -12.5,0.5
      parent: 1
  - uid: 87
    components:
    - type: Transform
      pos: -11.5,0.5
      parent: 1
  - uid: 88
    components:
    - type: Transform
      pos: -11.5,-0.5
      parent: 1
  - uid: 89
    components:
    - type: Transform
      pos: -11.5,-1.5
      parent: 1
  - uid: 90
    components:
    - type: Transform
      pos: -11.5,-2.5
      parent: 1
  - uid: 91
    components:
    - type: Transform
      pos: -11.5,-3.5
      parent: 1
  - uid: 92
    components:
    - type: Transform
      pos: -11.5,-4.5
      parent: 1
  - uid: 93
    components:
    - type: Transform
      pos: -11.5,-5.5
      parent: 1
  - uid: 94
    components:
    - type: Transform
      pos: -12.5,-5.5
      parent: 1
  - uid: 95
    components:
    - type: Transform
      pos: -13.5,-5.5
      parent: 1
  - uid: 96
    components:
    - type: Transform
      pos: -10.5,-2.5
      parent: 1
  - uid: 97
    components:
    - type: Transform
      pos: -12.5,-2.5
      parent: 1
  - uid: 98
    components:
    - type: Transform
      pos: -5.5,-1.5
      parent: 1
  - uid: 99
    components:
    - type: Transform
      pos: -5.5,-2.5
      parent: 1
  - uid: 100
    components:
    - type: Transform
      pos: -6.5,-2.5
      parent: 1
  - uid: 101
    components:
    - type: Transform
      pos: -7.5,-2.5
      parent: 1
  - uid: 102
    components:
    - type: Transform
      pos: -8.5,-2.5
      parent: 1
  - uid: 103
    components:
    - type: Transform
      pos: -9.5,-2.5
      parent: 1
- proto: CableHVStack1
  entities:
  - uid: 225
    components:
    - type: Transform
      parent: 222
    - type: Stack
      count: 5
    - type: SolutionContainerManager
      solutions: null
      containers:
      - hvcable
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
    - type: ContainerContainer
      containers:
        solution@hvcable: !type:ContainerSlot
          ent: 226
  - uid: 270
    components:
    - type: Transform
      parent: 259
    - type: Stack
      count: 10
    - type: SolutionContainerManager
      solutions: null
      containers:
      - hvcable
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
    - type: ContainerContainer
      containers:
        solution@hvcable: !type:ContainerSlot
          ent: 271
  - uid: 289
    components:
    - type: Transform
      parent: 282
    - type: Stack
      count: 5
    - type: SolutionContainerManager
      solutions: null
      containers:
      - hvcable
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
    - type: ContainerContainer
      containers:
        solution@hvcable: !type:ContainerSlot
          ent: 290
- proto: CableMV
  entities:
  - uid: 104
    components:
    - type: Transform
      pos: -5.5,-1.5
      parent: 1
  - uid: 105
    components:
    - type: Transform
      pos: -5.5,-2.5
      parent: 1
  - uid: 106
    components:
    - type: Transform
      pos: -4.5,-2.5
      parent: 1
  - uid: 107
    components:
    - type: Transform
      pos: -4.5,-1.5
      parent: 1
  - uid: 108
    components:
    - type: Transform
      pos: -4.5,-3.5
      parent: 1
  - uid: 109
    components:
    - type: Transform
      pos: -3.5,-3.5
      parent: 1
  - uid: 110
    components:
    - type: Transform
      pos: -2.5,-3.5
      parent: 1
  - uid: 111
    components:
    - type: Transform
      pos: -1.5,-3.5
      parent: 1
  - uid: 112
    components:
    - type: Transform
      pos: -0.5,-3.5
      parent: 1
  - uid: 113
    components:
    - type: Transform
      pos: -0.5,-2.5
      parent: 1
  - uid: 114
    components:
    - type: Transform
      pos: -0.5,-1.5
      parent: 1
  - uid: 115
    components:
    - type: Transform
      pos: 0.5,-1.5
      parent: 1
  - uid: 116
    components:
    - type: Transform
      pos: 1.5,-1.5
      parent: 1
  - uid: 117
    components:
    - type: Transform
      pos: 2.5,-1.5
      parent: 1
  - uid: 118
    components:
    - type: Transform
      pos: 3.5,-1.5
      parent: 1
  - uid: 119
    components:
    - type: Transform
      pos: 4.5,-1.5
      parent: 1
  - uid: 120
    components:
    - type: Transform
      pos: 5.5,-1.5
      parent: 1
  - uid: 121
    components:
    - type: Transform
      pos: 5.5,-0.5
      parent: 1
  - uid: 122
    components:
    - type: Transform
      pos: 5.5,-2.5
      parent: 1
  - uid: 123
    components:
    - type: Transform
      pos: 6.5,-2.5
      parent: 1
  - uid: 124
    components:
    - type: Transform
      pos: 7.5,-2.5
      parent: 1
  - uid: 125
    components:
    - type: Transform
      pos: 8.5,-2.5
      parent: 1
  - uid: 126
    components:
    - type: Transform
      pos: 9.5,-2.5
      parent: 1
  - uid: 127
    components:
    - type: Transform
      pos: 9.5,-1.5
      parent: 1
- proto: CableMVStack1
  entities:
  - uid: 287
    components:
    - type: Transform
      parent: 282
    - type: Stack
      count: 5
    - type: SolutionContainerManager
      solutions: null
      containers:
      - mvcable
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
    - type: ContainerContainer
      containers:
        solution@mvcable: !type:ContainerSlot
          ent: 288
  - uid: 393
    components:
    - type: Transform
      parent: 389
    - type: Stack
      count: 5
    - type: SolutionContainerManager
      solutions: null
      containers:
      - mvcable
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
    - type: ContainerContainer
      containers:
        solution@mvcable: !type:ContainerSlot
          ent: 394
- proto: CableTerminal
  entities:
  - uid: 128
    components:
    - type: Transform
      rot: 1.5707963267948966 rad
      pos: -11.5,-2.5
      parent: 1
- proto: CapacitorStockPart
  entities:
  - uid: 224
    components:
    - type: Transform
      parent: 222
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
  - uid: 261
    components:
    - type: Transform
      parent: 259
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
  - uid: 284
    components:
    - type: Transform
      parent: 282
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
  - uid: 391
    components:
    - type: Transform
      parent: 389
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
  - uid: 392
    components:
    - type: Transform
      parent: 389
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
- proto: Carpet
  entities:
  - uid: 129
    components:
    - type: Transform
      pos: 3.5,-3.5
      parent: 1
- proto: Catwalk
  entities:
  - uid: 130
    components:
    - type: Transform
      pos: -0.5,-1.5
      parent: 1
  - uid: 131
    components:
    - type: Transform
      pos: 0.5,-1.5
      parent: 1
  - uid: 132
    components:
    - type: Transform
      pos: 1.5,-1.5
      parent: 1
  - uid: 133
    components:
    - type: Transform
      pos: -9.5,-3.5
      parent: 1
  - uid: 134
    components:
    - type: Transform
      pos: -10.5,-3.5
      parent: 1
  - uid: 135
    components:
    - type: Transform
      pos: -11.5,-3.5
      parent: 1
  - uid: 136
    components:
    - type: Transform
      pos: -12.5,-3.5
      parent: 1
  - uid: 137
    components:
    - type: Transform
      pos: -12.5,-1.5
      parent: 1
  - uid: 138
    components:
    - type: Transform
      pos: -11.5,-1.5
      parent: 1
  - uid: 139
    components:
    - type: Transform
      pos: -10.5,-1.5
      parent: 1
  - uid: 140
    components:
    - type: Transform
      pos: -9.5,-1.5
      parent: 1
  - uid: 141
    components:
    - type: Transform
      pos: -9.5,-2.5
      parent: 1
  - uid: 142
    components:
    - type: Transform
      pos: -8.5,-2.5
      parent: 1
- proto: ChairOfficeDark
  entities:
  - uid: 143
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: -0.5,-2.5000153
      parent: 1
- proto: ChairPilotSeat
  entities:
  - uid: 144
    components:
    - type: Transform
      rot: 1.5707963267948966 rad
      pos: 12.5,-2.5
      parent: 1
  - uid: 145
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: 12.5,-3.5
      parent: 1
- proto: CigaretteSyndicate
  entities:
  - uid: 146
    components:
    - type: Transform
      rot: -6.283185005187988 rad
      pos: -1.6491089,-1.7179413
      parent: 1
    - type: SolutionContainerManager
      solutions: null
      containers:
      - smokable
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
    - type: ContainerContainer
      containers:
        solution@smokable: !type:ContainerSlot
          ent: 147
- proto: ClothingHandsGlovesColorRed
  entities:
  - uid: 149
    components:
    - type: Transform
      pos: 5.474929,-3.3550735
      parent: 1
    - type: SolutionContainerManager
      solutions: null
      containers:
      - food
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: ContainerContainer
      containers:
        solution@food: !type:ContainerSlot
          ent: 150
- proto: ClothingHandsTacticalMaidGloves
  entities:
  - uid: 152
    components:
    - type: Transform
      parent: 151
    - type: SolutionContainerManager
      solutions: null
      containers:
      - food
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
    - type: InsideEntityStorage
    - type: ContainerContainer
      containers:
        solution@food: !type:ContainerSlot
          ent: 153
- proto: ClothingHeadHatTacticalMaidHeadband
  entities:
  - uid: 154
    components:
    - type: Transform
      parent: 151
    - type: SolutionContainerManager
      solutions: null
      containers:
      - food
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
    - type: InsideEntityStorage
    - type: ContainerContainer
      containers:
        solution@food: !type:ContainerSlot
          ent: 155
- proto: ClothingUniformJumpskirtTacticalMaid
  entities:
  - uid: 156
    components:
    - type: Transform
      parent: 151
    - type: SuitSensor
      nextUpdate: -462.4153573
      mode: SensorBinary
    - type: DeviceNetwork
      address: 2615-429B
      transmitFrequency: 1262
    - type: SolutionContainerManager
      solutions: null
      containers:
      - food
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
    - type: InsideEntityStorage
    - type: ContainerContainer
      containers:
        solution@food: !type:ContainerSlot
          ent: 157
- proto: ClothingUniformJumpskirtTacticool
  entities:
  - uid: 158
    components:
    - type: Transform
      parent: 151
    - type: SuitSensor
      nextUpdate: -462.4153573
    - type: DeviceNetwork
      address: 61C2-6913
      transmitFrequency: 1262
    - type: SolutionContainerManager
      solutions: null
      containers:
      - food
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
    - type: InsideEntityStorage
    - type: ContainerContainer
      containers:
        solution@food: !type:ContainerSlot
          ent: 159
- proto: ClothingUniformJumpsuitTacticool
  entities:
  - uid: 160
    components:
    - type: Transform
      parent: 151
    - type: SuitSensor
      nextUpdate: -462.4153573
    - type: DeviceNetwork
      address: 4B25-4DDB
      transmitFrequency: 1262
    - type: SolutionContainerManager
      solutions: null
      containers:
      - food
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
    - type: InsideEntityStorage
    - type: ContainerContainer
      containers:
        solution@food: !type:ContainerSlot
          ent: 161
- proto: ComputerIFFSyndicate
  entities:
  - uid: 162
    components:
    - type: Transform
      rot: 1.5707963267948966 rad
      pos: 11.5,-3.5
      parent: 1
- proto: ComputerShuttleSyndie
  entities:
  - uid: 402
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: 13.5,-2.5
      parent: 1
- proto: ComputerSolarControl
  entities:
  - uid: 164
    components:
    - type: Transform
      pos: 11.5,-1.5
      parent: 1
    - type: ContainerContainer
      containers:
        board: !type:Container
          showEnts: False
          occludes: True
          ents:
          - 165
- proto: CyberPen
  entities:
  - uid: 166
    components:
    - type: Transform
      rot: -6.283185005187988 rad
      pos: 13.046143,-1.4593353
      parent: 1
    - type: MeleeWeapon
      nextAttack: -462.4153573
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
- proto: DoorElectronics
  entities:
  - uid: 4
    components:
    - type: Transform
      parent: 3
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
  - uid: 6
    components:
    - type: Transform
      parent: 5
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
  - uid: 8
    components:
    - type: Transform
      parent: 7
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
  - uid: 10
    components:
    - type: Transform
      parent: 9
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
  - uid: 12
    components:
    - type: Transform
      parent: 11
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
  - uid: 14
    components:
    - type: Transform
      parent: 13
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
  - uid: 16
    components:
    - type: Transform
      parent: 15
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
  - uid: 20
    components:
    - type: Transform
      parent: 19
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
  - uid: 397
    components:
    - type: Transform
      parent: 396
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
  - uid: 399
    components:
    - type: Transform
      parent: 398
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
- proto: FaxMachineSyndie
  entities:
  - uid: 148
    components:
    - type: Transform
      pos: 13.5,-3.5
      parent: 1
- proto: FoodSnackSyndi
  entities:
  - uid: 167
    components:
    - type: Transform
      pos: 5.3655396,-3.401947
      parent: 1
    - type: SolutionContainerManager
      solutions: null
      containers:
      - food
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: ContainerContainer
      containers:
        solution@food: !type:ContainerSlot
          ent: 168
- proto: FoodTinMRE
  entities:
  - uid: 169
    components:
    - type: Transform
      rot: -6.283185005187988 rad
      pos: -1.3052979,-1.7335052
      parent: 1
    - type: SolutionContainerManager
      solutions: null
      containers:
      - food
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
    - type: ContainerContainer
      containers:
        solution@food: !type:ContainerSlot
          ent: 170
- proto: GasPipeStraight
  entities:
  - uid: 171
    components:
    - type: Transform
      rot: 1.5707963267948966 rad
      pos: -15.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 172
    components:
    - type: Transform
      rot: 1.5707963267948966 rad
      pos: -13.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 173
    components:
    - type: Transform
      rot: 1.5707963267948966 rad
      pos: -12.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 174
    components:
    - type: Transform
      rot: 1.5707963267948966 rad
      pos: -11.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 175
    components:
    - type: Transform
      rot: 1.5707963267948966 rad
      pos: -10.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 176
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: -8.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 177
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: -7.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 178
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: -6.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 179
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: -5.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 180
    components:
    - type: Transform
      rot: 1.5707963267948966 rad
      pos: -3.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 181
    components:
    - type: Transform
      rot: 1.5707963267948966 rad
      pos: -2.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 182
    components:
    - type: Transform
      rot: 1.5707963267948966 rad
      pos: -1.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 183
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: 0.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 184
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: 1.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 185
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: 2.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 186
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: 3.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 187
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: 4.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 188
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: 5.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 189
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: 6.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 190
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: 7.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 191
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: 8.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 192
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: 9.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 193
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: 10.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 194
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: 11.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
- proto: GasPipeTJunction
  entities:
  - uid: 195
    components:
    - type: Transform
      pos: -9.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 196
    components:
    - type: Transform
      pos: -4.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 197
    components:
    - type: Transform
      pos: -0.5,-2.5
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
- proto: GasPort
  entities:
  - uid: 198
    components:
    - type: Transform
      rot: 1.5707963267948966 rad
      pos: -16.5,-2.5
      parent: 1
    - type: AtmosDevice
      joinedGrid: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
- proto: GasPressurePump
  entities:
  - uid: 199
    components:
    - type: Transform
      rot: 1.5707963267948966 rad
      pos: -14.5,-2.5
      parent: 1
    - type: AtmosDevice
      joinedGrid: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
- proto: GasVentPump
  entities:
  - uid: 200
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: -9.5,-3.5
      parent: 1
    - type: DeviceNetwork
      address: VNT-54EA-FFCD
      transmitFrequency: 1621
      receiveFrequency: 1621
    - type: AtmosDevice
      joinedGrid: 1
    - type: AtmosMonitor
      gasThresholds:
        Oxygen:
          lowerWarnAround:
            threshold: 1.5
            enabled: True
          upperWarnAround:
            threshold: 0
            enabled: False
          lowerBound:
            threshold: 0.1
            enabled: True
          upperBound:
            threshold: 0
            enabled: False
          ignore: False
        Nitrogen:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0
            enabled: False
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0
            enabled: False
          ignore: True
        CarbonDioxide:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0.5
            enabled: True
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.0025
            enabled: True
          ignore: False
        Plasma:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0.5
            enabled: True
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.00125
            enabled: True
          ignore: False
        Tritium:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0
            enabled: False
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.0001
            enabled: True
          ignore: False
        WaterVapor:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0.5
            enabled: True
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 1.5
            enabled: True
          ignore: False
        Ammonia:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0.5
            enabled: True
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.05
            enabled: True
          ignore: False
        NitrousOxide:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0.5
            enabled: True
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.01
            enabled: True
          ignore: False
        Frezon:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0
            enabled: False
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.0001
            enabled: True
          ignore: False
      pressureThreshold:
        lowerWarnAround:
          threshold: 2.5
          enabled: True
        upperWarnAround:
          threshold: 0.7
          enabled: True
        lowerBound:
          threshold: 20
          enabled: True
        upperBound:
          threshold: 550
          enabled: True
        ignore: False
      temperatureThreshold:
        lowerWarnAround:
          threshold: 1.1
          enabled: True
        upperWarnAround:
          threshold: 0.8
          enabled: True
        lowerBound:
          threshold: 193.15
          enabled: True
        upperBound:
          threshold: 393.15
          enabled: True
        ignore: False
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 201
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: -4.5,-3.5
      parent: 1
    - type: DeviceNetwork
      address: VNT-3135-2B68
      transmitFrequency: 1621
      receiveFrequency: 1621
    - type: AtmosDevice
      joinedGrid: 1
    - type: AtmosMonitor
      gasThresholds:
        Oxygen:
          lowerWarnAround:
            threshold: 1.5
            enabled: True
          upperWarnAround:
            threshold: 0
            enabled: False
          lowerBound:
            threshold: 0.1
            enabled: True
          upperBound:
            threshold: 0
            enabled: False
          ignore: False
        Nitrogen:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0
            enabled: False
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0
            enabled: False
          ignore: True
        CarbonDioxide:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0.5
            enabled: True
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.0025
            enabled: True
          ignore: False
        Plasma:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0.5
            enabled: True
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.00125
            enabled: True
          ignore: False
        Tritium:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0
            enabled: False
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.0001
            enabled: True
          ignore: False
        WaterVapor:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0.5
            enabled: True
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 1.5
            enabled: True
          ignore: False
        Ammonia:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0.5
            enabled: True
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.05
            enabled: True
          ignore: False
        NitrousOxide:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0.5
            enabled: True
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.01
            enabled: True
          ignore: False
        Frezon:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0
            enabled: False
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.0001
            enabled: True
          ignore: False
      pressureThreshold:
        lowerWarnAround:
          threshold: 2.5
          enabled: True
        upperWarnAround:
          threshold: 0.7
          enabled: True
        lowerBound:
          threshold: 20
          enabled: True
        upperBound:
          threshold: 550
          enabled: True
        ignore: False
      temperatureThreshold:
        lowerWarnAround:
          threshold: 1.1
          enabled: True
        upperWarnAround:
          threshold: 0.8
          enabled: True
        lowerBound:
          threshold: 193.15
          enabled: True
        upperBound:
          threshold: 393.15
          enabled: True
        ignore: False
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 202
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: 12.5,-2.5
      parent: 1
    - type: DeviceNetwork
      address: VNT-0785-1CD7
      transmitFrequency: 1621
      receiveFrequency: 1621
    - type: AtmosDevice
      joinedGrid: 1
    - type: AtmosMonitor
      gasThresholds:
        Oxygen:
          lowerWarnAround:
            threshold: 1.5
            enabled: True
          upperWarnAround:
            threshold: 0
            enabled: False
          lowerBound:
            threshold: 0.1
            enabled: True
          upperBound:
            threshold: 0
            enabled: False
          ignore: False
        Nitrogen:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0
            enabled: False
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0
            enabled: False
          ignore: True
        CarbonDioxide:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0.5
            enabled: True
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.0025
            enabled: True
          ignore: False
        Plasma:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0.5
            enabled: True
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.00125
            enabled: True
          ignore: False
        Tritium:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0
            enabled: False
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.0001
            enabled: True
          ignore: False
        WaterVapor:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0.5
            enabled: True
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 1.5
            enabled: True
          ignore: False
        Ammonia:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0.5
            enabled: True
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.05
            enabled: True
          ignore: False
        NitrousOxide:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0.5
            enabled: True
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.01
            enabled: True
          ignore: False
        Frezon:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0
            enabled: False
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.0001
            enabled: True
          ignore: False
      pressureThreshold:
        lowerWarnAround:
          threshold: 2.5
          enabled: True
        upperWarnAround:
          threshold: 0.7
          enabled: True
        lowerBound:
          threshold: 20
          enabled: True
        upperBound:
          threshold: 550
          enabled: True
        ignore: False
      temperatureThreshold:
        lowerWarnAround:
          threshold: 1.1
          enabled: True
        upperWarnAround:
          threshold: 0.8
          enabled: True
        lowerBound:
          threshold: 193.15
          enabled: True
        upperBound:
          threshold: 393.15
          enabled: True
        ignore: False
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
  - uid: 203
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: -0.5,-3.5
      parent: 1
    - type: DeviceNetwork
      address: VNT-7766-D9D8
      transmitFrequency: 1621
      receiveFrequency: 1621
    - type: AtmosDevice
      joinedGrid: 1
    - type: AtmosMonitor
      gasThresholds:
        Oxygen:
          lowerWarnAround:
            threshold: 1.5
            enabled: True
          upperWarnAround:
            threshold: 0
            enabled: False
          lowerBound:
            threshold: 0.1
            enabled: True
          upperBound:
            threshold: 0
            enabled: False
          ignore: False
        Nitrogen:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0
            enabled: False
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0
            enabled: False
          ignore: True
        CarbonDioxide:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0.5
            enabled: True
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.0025
            enabled: True
          ignore: False
        Plasma:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0.5
            enabled: True
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.00125
            enabled: True
          ignore: False
        Tritium:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0
            enabled: False
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.0001
            enabled: True
          ignore: False
        WaterVapor:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0.5
            enabled: True
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 1.5
            enabled: True
          ignore: False
        Ammonia:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0.5
            enabled: True
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.05
            enabled: True
          ignore: False
        NitrousOxide:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0.5
            enabled: True
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.01
            enabled: True
          ignore: False
        Frezon:
          lowerWarnAround:
            threshold: 0
            enabled: False
          upperWarnAround:
            threshold: 0
            enabled: False
          lowerBound:
            threshold: 0
            enabled: False
          upperBound:
            threshold: 0.0001
            enabled: True
          ignore: False
      pressureThreshold:
        lowerWarnAround:
          threshold: 2.5
          enabled: True
        upperWarnAround:
          threshold: 0.7
          enabled: True
        lowerBound:
          threshold: 20
          enabled: True
        upperBound:
          threshold: 550
          enabled: True
        ignore: False
      temperatureThreshold:
        lowerWarnAround:
          threshold: 1.1
          enabled: True
        upperWarnAround:
          threshold: 0.8
          enabled: True
        lowerBound:
          threshold: 193.15
          enabled: True
        upperBound:
          threshold: 393.15
          enabled: True
        ignore: False
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
- proto: GravityGeneratorMini
  entities:
  - uid: 18
    components:
    - type: Transform
      pos: -12.5,-1.5
      parent: 1
- proto: Grille
  entities:
  - uid: 204
    components:
    - type: Transform
      pos: 12.5,-0.5
      parent: 1
  - uid: 205
    components:
    - type: Transform
      pos: 14.5,-2.5
      parent: 1
  - uid: 206
    components:
    - type: Transform
      pos: 12.5,-4.5
      parent: 1
  - uid: 207
    components:
    - type: Transform
      pos: 3.5,-4.5
      parent: 1
  - uid: 208
    components:
    - type: Transform
      pos: -0.5,-4.5
      parent: 1
  - uid: 209
    components:
    - type: Transform
      pos: 3.5,-0.5
      parent: 1
  - uid: 210
    components:
    - type: Transform
      pos: 2.5,-0.5
      parent: 1
  - uid: 211
    components:
    - type: Transform
      pos: 4.5,-0.5
      parent: 1
  - uid: 212
    components:
    - type: Transform
      pos: -4.5,-4.5
      parent: 1
  - uid: 213
    components:
    - type: Transform
      pos: -10.5,-4.5
      parent: 1
  - uid: 214
    components:
    - type: Transform
      pos: -12.5,-4.5
      parent: 1
  - uid: 215
    components:
    - type: Transform
      pos: -12.5,-0.5
      parent: 1
  - uid: 216
    components:
    - type: Transform
      pos: -10.5,-0.5
      parent: 1
- proto: Gyroscope
  entities:
  - uid: 17
    components:
    - type: Transform
      pos: -12.5,-3.5
      parent: 1
    - type: Thruster
      nextFire: -2474.4221115
- proto: IngotGold1
  entities:
  - uid: 217
    components:
    - type: Transform
      rot: -6.283185005187988 rad
      pos: -1.5758057,-1.4302673
      parent: 1
    - type: SolutionContainerManager
      solutions: null
      containers:
      - gold
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
    - type: ContainerContainer
      containers:
        solution@gold: !type:ContainerSlot
          ent: 218
  - uid: 219
    components:
    - type: Transform
      rot: -6.283185005187988 rad
      pos: -1.3883057,-1.5396271
      parent: 1
    - type: SolutionContainerManager
      solutions: null
      containers:
      - gold
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
    - type: ContainerContainer
      containers:
        solution@gold: !type:ContainerSlot
          ent: 220
- proto: LightBulb
  entities:
  - uid: 235
    components:
    - type: Transform
      parent: 234
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
  - uid: 237
    components:
    - type: Transform
      parent: 236
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
  - uid: 239
    components:
    - type: Transform
      parent: 238
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
  - uid: 241
    components:
    - type: Transform
      parent: 240
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
  - uid: 243
    components:
    - type: Transform
      parent: 242
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
- proto: LockerSyndicatePersonal
  entities:
  - uid: 151
    components:
    - type: Transform
      pos: -3.500061,-1.5
      parent: 1
    - type: EntityStorage
      air:
        volume: 200
        immutable: False
        temperature: 293.1434
        moles:
        - 0.8599783
        - 3.235156
        - 0
        - 0
        - 0
        - 0
        - 0
        - 0
        - 0
        - 0
        - 0
        - 0
    - type: ContainerContainer
      containers:
        entity_storage: !type:Container
          showEnts: False
          occludes: True
          ents:
          - 160
          - 156
          - 152
          - 158
          - 154
        paper_label: !type:ContainerSlot
          showEnts: False
          occludes: True
          ent: null
- proto: PlushieNuke
  entities:
  - uid: 221
    components:
    - type: Transform
      rot: -6.283185005187988 rad
      pos: 3.0700073,-3.5929108
      parent: 1
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: UseDelay
      delayEndTime: -462.4153573
      delayStartTime: -462.4153573
    - type: MeleeWeapon
      nextAttack: -462.4153573
    - type: Physics
      canCollide: False
- proto: PortableGeneratorPacman
  entities:
  - uid: 222
    components:
    - type: Transform
      pos: -12.5,-2.5
      parent: 1
    - type: UseDelay
      delayEndTime: -462.4153573
      delayStartTime: -462.4153573
    - type: DeviceNetwork
      address: 04D3-9425
      receiveFrequency: 1280
    - type: ContainerContainer
      containers:
        machine_board: !type:Container
          showEnts: False
          occludes: True
          ents:
          - 223
        machine_parts: !type:Container
          showEnts: False
          occludes: True
          ents:
          - 224
          - 225
- proto: PortableGeneratorPacmanMachineCircuitboard
  entities:
  - uid: 223
    components:
    - type: Transform
      parent: 222
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
- proto: PosterContrabandC20r
  entities:
  - uid: 227
    components:
    - type: Transform
      pos: 3.5,-2.5
      parent: 1
- proto: PosterContrabandCybersun600
  entities:
  - uid: 228
    components:
    - type: Transform
      pos: 10.5,-3.5
      parent: 1
- proto: PosterContrabandEnlistGorlex
  entities:
  - uid: 229
    components:
    - type: Transform
      pos: 10.5,-1.5
      parent: 1
- proto: PosterContrabandFreeSyndicateEncryptionKey
  entities:
  - uid: 230
    components:
    - type: Transform
      pos: 1.5,-2.5
      parent: 1
- proto: PosterContrabandMoth
  entities:
  - uid: 231
    components:
    - type: Transform
      pos: -6.5,-3.5
      parent: 1
- proto: PosterContrabandSyndicatePistol
  entities:
  - uid: 232
    components:
    - type: Transform
      pos: -2.5,-1.5
      parent: 1
- proto: PosterContrabandSyndicateRecruitment
  entities:
  - uid: 233
    components:
    - type: Transform
      pos: 6.5,-3.5
      parent: 1
- proto: PowerCellSmall
  entities:
  - uid: 262
    components:
    - type: Transform
      parent: 259
    - type: SolutionContainerManager
      solutions: null
      containers:
      - battery
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
    - type: ContainerContainer
      containers:
        solution@battery: !type:ContainerSlot
          ent: 263
  - uid: 264
    components:
    - type: Transform
      parent: 259
    - type: SolutionContainerManager
      solutions: null
      containers:
      - battery
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
    - type: ContainerContainer
      containers:
        solution@battery: !type:ContainerSlot
          ent: 265
  - uid: 266
    components:
    - type: Transform
      parent: 259
    - type: SolutionContainerManager
      solutions: null
      containers:
      - battery
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
    - type: ContainerContainer
      containers:
        solution@battery: !type:ContainerSlot
          ent: 267
  - uid: 268
    components:
    - type: Transform
      parent: 259
    - type: SolutionContainerManager
      solutions: null
      containers:
      - battery
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
    - type: ContainerContainer
      containers:
        solution@battery: !type:ContainerSlot
          ent: 269
  - uid: 285
    components:
    - type: Transform
      parent: 282
    - type: SolutionContainerManager
      solutions: null
      containers:
      - battery
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
    - type: ContainerContainer
      containers:
        solution@battery: !type:ContainerSlot
          ent: 286
- proto: PoweredSmallLight
  entities:
  - uid: 234
    components:
    - type: Transform
      pos: -1.5,-1.5
      parent: 1
    - type: PointLight
      color: '#FFD1A3FF'
      enabled: False
    - type: ContainerContainer
      containers:
        light_bulb: !type:ContainerSlot
          showEnts: False
          occludes: True
          ent: 235
    - type: ApcPowerReceiver
      powerLoad: 60
    - type: DeviceNetwork
      address: 1403-98E8
      receiveFrequency: 1173
  - uid: 236
    components:
    - type: Transform
      pos: 5.5,-1.5
      parent: 1
    - type: PointLight
      color: '#FFD1A3FF'
      enabled: False
    - type: ContainerContainer
      containers:
        light_bulb: !type:ContainerSlot
          showEnts: False
          occludes: True
          ent: 237
    - type: ApcPowerReceiver
      powerLoad: 60
    - type: DeviceNetwork
      address: 36D6-0A5C
      receiveFrequency: 1173
  - uid: 238
    components:
    - type: Transform
      pos: 3.5,-3.5
      parent: 1
    - type: PointLight
      color: '#FFD1A3FF'
      enabled: False
    - type: ContainerContainer
      containers:
        light_bulb: !type:ContainerSlot
          showEnts: False
          occludes: True
          ent: 239
    - type: ApcPowerReceiver
      powerLoad: 60
    - type: DeviceNetwork
      address: 3E59-FBA5
      receiveFrequency: 1173
  - uid: 240
    components:
    - type: Transform
      pos: 8.5,-2.5
      parent: 1
    - type: PointLight
      color: '#FFD1A3FF'
      enabled: False
    - type: ContainerContainer
      containers:
        light_bulb: !type:ContainerSlot
          showEnts: False
          occludes: True
          ent: 241
    - type: ApcPowerReceiver
      powerLoad: 60
    - type: DeviceNetwork
      address: 7EFC-C1C4
      receiveFrequency: 1173
  - uid: 242
    components:
    - type: Transform
      pos: 13.5,-1.5
      parent: 1
    - type: PointLight
      color: '#FFD1A3FF'
      enabled: False
    - type: ContainerContainer
      containers:
        light_bulb: !type:ContainerSlot
          showEnts: False
          occludes: True
          ent: 243
    - type: ApcPowerReceiver
      powerLoad: 60
    - type: DeviceNetwork
      address: 1719-54E7
      receiveFrequency: 1173
- proto: ReinforcedPlasmaWindow
  entities:
  - uid: 244
    components:
    - type: Transform
      pos: -12.5,-4.5
      parent: 1
  - uid: 245
    components:
    - type: Transform
      pos: -10.5,-4.5
      parent: 1
  - uid: 246
    components:
    - type: Transform
      pos: -10.5,-0.5
      parent: 1
  - uid: 247
    components:
    - type: Transform
      pos: -12.5,-0.5
      parent: 1
  - uid: 248
    components:
    - type: Transform
      pos: -4.5,-4.5
      parent: 1
  - uid: 249
    components:
    - type: Transform
      pos: -0.5,-4.5
      parent: 1
  - uid: 250
    components:
    - type: Transform
      pos: 3.5,-4.5
      parent: 1
  - uid: 251
    components:
    - type: Transform
      pos: 2.5,-0.5
      parent: 1
  - uid: 252
    components:
    - type: Transform
      pos: 3.5,-0.5
      parent: 1
  - uid: 253
    components:
    - type: Transform
      pos: 4.5,-0.5
      parent: 1
  - uid: 254
    components:
    - type: Transform
      pos: 12.5,-4.5
      parent: 1
  - uid: 255
    components:
    - type: Transform
      pos: 14.5,-2.5
      parent: 1
  - uid: 256
    components:
    - type: Transform
      pos: 12.5,-0.5
      parent: 1
- proto: SignEngine
  entities:
  - uid: 257
    components:
    - type: Transform
      pos: -6.5,-1.5
      parent: 1
- proto: SignSecureSmallRed
  entities:
  - uid: 258
    components:
    - type: Transform
      pos: -2.5,-2.5
      parent: 1
- proto: SMESBasic
  entities:
  - uid: 259
    components:
    - type: MetaData
      name: Syndicate SMES
    - type: Transform
      pos: -10.5,-2.5
      parent: 1
    - type: PowerNetworkBattery
      loadingNetworkDemand: 705.0028
      currentReceiving: 720.00287
      currentSupply: 705.0028
    - type: DeviceNetwork
      address: SMS-674A-9E28
      transmitFrequency: 1621
      receiveFrequency: 1621
    - type: ContainerContainer
      containers:
        machine_board: !type:Container
          showEnts: False
          occludes: True
          ents:
          - 260
        machine_parts: !type:Container
          showEnts: False
          occludes: True
          ents:
          - 261
          - 262
          - 264
          - 266
          - 268
          - 270
- proto: SMESMachineCircuitboard
  entities:
  - uid: 260
    components:
    - type: Transform
      parent: 259
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
- proto: SolarControlComputerCircuitboard
  entities:
  - uid: 165
    components:
    - type: Transform
      parent: 164
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
- proto: SolarPanel
  entities:
  - uid: 272
    components:
    - type: Transform
      rot: -4.2101240158081055 rad
      pos: -14.5,0.5
      parent: 1
  - uid: 273
    components:
    - type: Transform
      rot: -4.2101240158081055 rad
      pos: -14.5,1.5
      parent: 1
  - uid: 274
    components:
    - type: Transform
      rot: -4.2101240158081055 rad
      pos: -14.5,2.5
      parent: 1
  - uid: 275
    components:
    - type: Transform
      rot: -4.2101240158081055 rad
      pos: -14.5,3.5
      parent: 1
  - uid: 276
    components:
    - type: Transform
      rot: -4.2101240158081055 rad
      pos: -14.5,4.5
      parent: 1
  - uid: 277
    components:
    - type: Transform
      rot: -4.2101240158081055 rad
      pos: -14.5,-5.5
      parent: 1
    - type: PowerSupplier
      supplyRampPosition: 144.00058
      supplyRate: 319
  - uid: 278
    components:
    - type: Transform
      rot: -4.2101240158081055 rad
      pos: -14.5,-6.5
      parent: 1
    - type: PowerSupplier
      supplyRampPosition: 144.00058
      supplyRate: 319
  - uid: 279
    components:
    - type: Transform
      rot: -4.2101240158081055 rad
      pos: -14.5,-7.5
      parent: 1
    - type: PowerSupplier
      supplyRampPosition: 144.00058
      supplyRate: 319
  - uid: 280
    components:
    - type: Transform
      rot: -4.2101240158081055 rad
      pos: -14.5,-8.5
      parent: 1
    - type: PowerSupplier
      supplyRampPosition: 144.00058
      supplyRate: 319
  - uid: 281
    components:
    - type: Transform
      rot: -4.2101240158081055 rad
      pos: -14.5,-9.5
      parent: 1
    - type: PowerSupplier
      supplyRampPosition: 144.00058
      supplyRate: 319
- proto: SubstationBasic
  entities:
  - uid: 282
    components:
    - type: MetaData
      name: Syndicate Substation
    - type: Transform
      pos: -5.5,-1.5
      parent: 1
    - type: PowerNetworkBattery
      loadingNetworkDemand: 709.9247
      currentReceiving: 705.0028
      currentSupply: 709.9247
      supplyRampPosition: 4.921875
    - type: ContainerContainer
      containers:
        machine_board: !type:Container
          showEnts: False
          occludes: True
          ents:
          - 283
        machine_parts: !type:Container
          showEnts: False
          occludes: True
          ents:
          - 284
          - 285
          - 287
          - 289
- proto: SubstationMachineCircuitboard
  entities:
  - uid: 283
    components:
    - type: Transform
      parent: 282
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
- proto: SuitStorageEVASyndicate
  entities:
  - uid: 406
    components:
    - type: Transform
      pos: -5.5,-3.5
      parent: 1
- proto: TablePlasmaGlass
  entities:
  - uid: 298
    components:
    - type: Transform
      pos: 5.5,-3.5
      parent: 1
- proto: TableReinforced
  entities:
  - uid: 299
    components:
    - type: Transform
      pos: -1.5,-2.5
      parent: 1
  - uid: 300
    components:
    - type: Transform
      pos: -1.5,-1.5
      parent: 1
  - uid: 301
    components:
    - type: Transform
      pos: 13.5,-1.5
      parent: 1
  - uid: 302
    components:
    - type: Transform
      pos: 12.5,-1.5
      parent: 1
  - uid: 303
    components:
    - type: Transform
      pos: 13.5,-3.5
      parent: 1
- proto: Thruster
  entities:
  - uid: 163
    components:
    - type: Transform
      rot: 1.5707963267948966 rad
      pos: -19.5,-3.5
      parent: 1
    - type: Thruster
      nextFire: -607.0314407
  - uid: 291
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: -7.5,-4.5
      parent: 1
    - type: Thruster
      nextFire: -610.0060786
  - uid: 292
    components:
    - type: Transform
      pos: -7.5,-0.5
      parent: 1
    - type: Thruster
      nextFire: -611.6297433
  - uid: 293
    components:
    - type: Transform
      rot: 1.5707963267948966 rad
      pos: -19.5,-1.5
      parent: 1
    - type: Thruster
      nextFire: -699.7473469
  - uid: 294
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: 7.5,-4.5
      parent: 1
    - type: Thruster
      nextFire: -682.6458064
  - uid: 295
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: 7.5,-0.5
      parent: 1
    - type: Thruster
      nextFire: -683.2808197
- proto: WallPlastitanium
  entities:
  - uid: 313
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: -0.5,-0.5
      parent: 1
  - uid: 314
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: 1.5,-0.5
      parent: 1
  - uid: 315
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: 5.5,-0.5
      parent: 1
  - uid: 316
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: 6.5,-0.5
      parent: 1
  - uid: 317
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: 6.5,-1.5
      parent: 1
  - uid: 318
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: 6.5,-4.5
      parent: 1
  - uid: 319
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: 6.5,-3.5
      parent: 1
  - uid: 320
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: 7.5,-3.5
      parent: 1
  - uid: 321
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: 8.5,-3.5
      parent: 1
  - uid: 322
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: 9.5,-3.5
      parent: 1
  - uid: 323
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: 7.5,-1.5
      parent: 1
  - uid: 324
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: 8.5,-1.5
      parent: 1
  - uid: 325
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: 9.5,-1.5
      parent: 1
  - uid: 326
    components:
    - type: Transform
      pos: 11.5,-0.5
      parent: 1
  - uid: 327
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: 10.5,-1.5
      parent: 1
  - uid: 328
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: 10.5,-0.5
      parent: 1
  - uid: 329
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: 10.5,-3.5
      parent: 1
  - uid: 330
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: 10.5,-4.5
      parent: 1
  - uid: 331
    components:
    - type: Transform
      pos: 11.5,-4.5
      parent: 1
  - uid: 332
    components:
    - type: Transform
      pos: 13.5,-4.5
      parent: 1
  - uid: 333
    components:
    - type: Transform
      pos: 14.5,-3.5
      parent: 1
  - uid: 334
    components:
    - type: Transform
      pos: 14.5,-1.5
      parent: 1
  - uid: 335
    components:
    - type: Transform
      pos: 13.5,-0.5
      parent: 1
  - uid: 336
    components:
    - type: Transform
      pos: 5.5,-4.5
      parent: 1
  - uid: 337
    components:
    - type: Transform
      pos: 4.5,-4.5
      parent: 1
  - uid: 338
    components:
    - type: Transform
      pos: 2.5,-4.5
      parent: 1
  - uid: 339
    components:
    - type: Transform
      pos: 1.5,-4.5
      parent: 1
  - uid: 340
    components:
    - type: Transform
      pos: 0.5,-4.5
      parent: 1
  - uid: 341
    components:
    - type: Transform
      pos: -1.5,-4.5
      parent: 1
  - uid: 342
    components:
    - type: Transform
      pos: -2.5,-4.5
      parent: 1
  - uid: 343
    components:
    - type: Transform
      pos: -3.5,-4.5
      parent: 1
  - uid: 344
    components:
    - type: Transform
      pos: -1.5,-0.5
      parent: 1
  - uid: 345
    components:
    - type: Transform
      pos: -2.5,-0.5
      parent: 1
  - uid: 346
    components:
    - type: Transform
      pos: -3.5,-0.5
      parent: 1
  - uid: 347
    components:
    - type: Transform
      pos: 1.5,-2.5
      parent: 1
  - uid: 348
    components:
    - type: Transform
      pos: 2.5,-2.5
      parent: 1
  - uid: 349
    components:
    - type: Transform
      pos: 3.5,-2.5
      parent: 1
  - uid: 350
    components:
    - type: Transform
      pos: 4.5,-2.5
      parent: 1
  - uid: 351
    components:
    - type: Transform
      pos: 4.5,-3.5
      parent: 1
  - uid: 352
    components:
    - type: Transform
      pos: -2.5,-2.5
      parent: 1
  - uid: 353
    components:
    - type: Transform
      pos: -2.5,-1.5
      parent: 1
  - uid: 354
    components:
    - type: Transform
      pos: -4.5,-0.5
      parent: 1
  - uid: 355
    components:
    - type: Transform
      pos: -4.5,-1.5
      parent: 1
  - uid: 356
    components:
    - type: Transform
      pos: -5.5,-0.5
      parent: 1
  - uid: 357
    components:
    - type: Transform
      pos: -6.5,-0.5
      parent: 1
  - uid: 358
    components:
    - type: Transform
      pos: -6.5,-1.5
      parent: 1
  - uid: 359
    components:
    - type: Transform
      pos: -7.5,-1.5
      parent: 1
  - uid: 360
    components:
    - type: Transform
      pos: -5.5,-4.5
      parent: 1
  - uid: 361
    components:
    - type: Transform
      pos: -6.5,-4.5
      parent: 1
  - uid: 362
    components:
    - type: Transform
      pos: -6.5,-3.5
      parent: 1
  - uid: 363
    components:
    - type: Transform
      pos: -7.5,-3.5
      parent: 1
  - uid: 364
    components:
    - type: Transform
      pos: -8.5,-3.5
      parent: 1
  - uid: 365
    components:
    - type: Transform
      pos: -8.5,-4.5
      parent: 1
  - uid: 366
    components:
    - type: Transform
      pos: -8.5,-1.5
      parent: 1
  - uid: 367
    components:
    - type: Transform
      pos: -8.5,-0.5
      parent: 1
  - uid: 368
    components:
    - type: Transform
      pos: -9.5,-4.5
      parent: 1
  - uid: 369
    components:
    - type: Transform
      pos: -9.5,-0.5
      parent: 1
  - uid: 370
    components:
    - type: Transform
      pos: -13.5,-4.5
      parent: 1
  - uid: 371
    components:
    - type: Transform
      pos: -13.5,-3.5
      parent: 1
  - uid: 372
    components:
    - type: Transform
      pos: -13.5,-2.5
      parent: 1
  - uid: 373
    components:
    - type: Transform
      pos: -13.5,-1.5
      parent: 1
  - uid: 374
    components:
    - type: Transform
      pos: -13.5,-0.5
      parent: 1
  - uid: 375
    components:
    - type: Transform
      pos: -15.5,-3.5
      parent: 1
  - uid: 376
    components:
    - type: Transform
      pos: -16.5,-3.5
      parent: 1
  - uid: 377
    components:
    - type: Transform
      pos: -17.5,-3.5
      parent: 1
  - uid: 378
    components:
    - type: Transform
      pos: -18.5,-3.5
      parent: 1
  - uid: 379
    components:
    - type: Transform
      pos: -18.5,-2.5
      parent: 1
  - uid: 380
    components:
    - type: Transform
      pos: -18.5,-1.5
      parent: 1
  - uid: 381
    components:
    - type: Transform
      pos: -17.5,-1.5
      parent: 1
  - uid: 382
    components:
    - type: Transform
      pos: -16.5,-1.5
      parent: 1
  - uid: 383
    components:
    - type: Transform
      pos: -15.5,-1.5
      parent: 1
- proto: WallPlastitaniumDiagonal
  entities:
  - uid: 384
    components:
    - type: Transform
      pos: 9.5,-0.5
      parent: 1
  - uid: 385
    components:
    - type: Transform
      rot: 1.5707963267948966 rad
      pos: 9.5,-4.5
      parent: 1
  - uid: 386
    components:
    - type: Transform
      rot: -1.5707963267948966 rad
      pos: 14.5,-0.5
      parent: 1
  - uid: 387
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: 14.5,-4.5
      parent: 1
- proto: WarningAir
  entities:
  - uid: 388
    components:
    - type: Transform
      pos: -15.5,-1.5
      parent: 1
- proto: WeaponCapacitorRecharger
  entities:
  - uid: 389
    components:
    - type: Transform
      pos: 13.5,-1.5
      parent: 1
    - type: ContainerContainer
      containers:
        charger_slot: !type:ContainerSlot
          showEnts: False
          occludes: True
          ent: null
        machine_board: !type:Container
          showEnts: False
          occludes: True
          ents:
          - 390
        machine_parts: !type:Container
          showEnts: False
          occludes: True
          ents:
          - 391
          - 392
          - 393
    - type: ApcPowerReceiver
      powerLoad: 0
- proto: WeaponCapacitorRechargerCircuitboard
  entities:
  - uid: 390
    components:
    - type: Transform
      parent: 389
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
- proto: WeaponTurretSyndicateBroken
  entities:
  - uid: 395
    components:
    - type: Transform
      pos: -1.5,-2.5
      parent: 1
- proto: WindoorSecure
  entities:
  - uid: 396
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: -3.5,-2.5
      parent: 1
    - type: DeviceNetwork
      address: 1194-AA17
      receiveFrequency: 1280
    - type: ContainerContainer
      containers:
        board: !type:Container
          showEnts: False
          occludes: True
          ents:
          - 397
  - uid: 398
    components:
    - type: Transform
      rot: 3.141592653589793 rad
      pos: -5.5,-2.5
      parent: 1
    - type: DeviceNetwork
      address: 1841-7A5E
      receiveFrequency: 1280
    - type: ContainerContainer
      containers:
        board: !type:Container
          showEnts: False
          occludes: True
          ents:
          - 399
- proto: Wrench
  entities:
  - uid: 400
    components:
    - type: Transform
      rot: -6.283185005187988 rad
      pos: -16.536316,-2.5396729
      parent: 1
    - type: MeleeWeapon
      nextAttack: -462.4153573
    - type: EmitSoundOnCollide
      nextSound: -462.4153573
    - type: Physics
      canCollide: False
...
