﻿<controls:FancyWindow xmlns="https://spacestation14.io"
                      xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
                      xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                      Title="{Loc 'mech-menu-title'}"
                      MinSize="350 440"
                      SetSize="350 440">
    <BoxContainer Orientation="Vertical" HorizontalExpand="True" VerticalExpand="True">
        <BoxContainer Margin="10 10 10 10" Orientation="Horizontal" HorizontalExpand="True">
            <BoxContainer Orientation="Vertical" HorizontalExpand="True">
                <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
                    <ProgressBar Name="IntegrityDisplayBar"
                                 HorizontalExpand="True"
                                 SetHeight="25"
                                 MaxValue="1"
                                 Value="0">
                        <Label Name="IntegrityDisplay"
                               HorizontalAlignment="Left"
                               Margin="5 0 0 0"
                               VerticalAlignment="Center" />
                    </ProgressBar>
                </BoxContainer>
                <BoxContainer Orientation="Horizontal" HorizontalExpand="True" Margin="0 5 0 0">
                    <ProgressBar Name="EnergyDisplayBar"
                                 HorizontalExpand="True"
                                 SetHeight="25"
                                 MaxValue="1"
                                 Value="0">
                        <Label Name="EnergyDisplay"
                               Margin="5 0 0 0"
                               HorizontalAlignment="Left"
                               VerticalAlignment="Center"></Label>
                    </ProgressBar>
                </BoxContainer>
                <Label Name="SlotDisplay"
                       HorizontalAlignment="Left"
                       Access="Public"
                       HorizontalExpand="True" />
            </BoxContainer>
            <SpriteView Name="MechView"
                        Margin="10 0 0 0"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Right"
                        OverrideDirection="South"
                        SetSize="64 64"
                        MaxSize="64 64"
                        Scale="2 2">
            </SpriteView>
        </BoxContainer>
        <BoxContainer VerticalExpand="True" Margin="10 0 10 10" Orientation="Vertical">
            <PanelContainer VerticalExpand="True" MinSize="0 200">
                <PanelContainer.PanelOverride>
                    <gfx:StyleBoxFlat BackgroundColor="#1B1B1E" />
                </PanelContainer.PanelOverride>
                <ScrollContainer
                    HScrollEnabled="False"
                    HorizontalExpand="True"
                    MinSize="100 256"
                    SizeFlagsStretchRatio="2"
                    VerticalExpand="True">
                    <BoxContainer
                        Name="EquipmentControlContainer"
                        MinSize="100 256"
                        Orientation="Vertical"
                        SizeFlagsStretchRatio="2"
                        VerticalExpand="True">
                    </BoxContainer>
                </ScrollContainer>
            </PanelContainer>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
