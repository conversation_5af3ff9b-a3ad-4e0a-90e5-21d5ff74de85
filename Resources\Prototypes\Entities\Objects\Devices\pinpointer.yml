- type: entity
  name: "пінпоінтер"
  description: "Портативний пристрій стеження. Хоча зазвичай цей пристрій має набагато більше можливостей, він налаштований на фіксацію певних сигналів. Тримайте вертикально, щоб зберегти точність."
  parent: BaseItem
  id: PinpointerBase
  abstract: true
  components:
  - type: Transform
    noRot: True
  - type: Sprite
    noRot: True
    sprite: Objects/Devices/pinpointer.rsi
    layers:
    - state: pinpointer
      map: ["enum.PinpointerLayers.Base"]
    - state: pinonnull
      map: ["enum.PinpointerLayers.Screen"]
      shader: unshaded
      visible: false
  - type: Icon
    sprite: Objects/Devices/pinpointer.rsi
    state: pinpointer
  - type: Item
    sprite: Objects/Devices/pinpointer.rsi
  - type: Pinpointer
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.PinpointerVisuals.IsActive:
        enum.PinpointerLayers.Screen:
          True: { visible: True }
          False: { visible: False }
      enum.PinpointerVisuals.TargetDistance:
        enum.PinpointerLayers.Screen:
          Unknown: { state: pinonnull }
          Reached: { state: pinondirect }
          Close: { state: pinonclose }
          Medium: { state: pinonmedium }
          Far: { state: pinonfar }
  - type: AnimationPlayer

- type: entity
  name: "пінпоінтер"
  id: PinpointerNuclear
  parent: PinpointerBase
  components:
  - type: Pinpointer
    component: NukeDisk
    targetName: nuclear authentication disk

- type: entity
  name: "синдикатський пінпоінтер"
  description: "Створений спеціально для ядерних оперативних місій, придбайте цей диск!"
  id: PinpointerSyndicateNuclear
  parent: PinpointerBase
  components:
  - type: Sprite
    layers:
    - state: pinpointer-syndicate
      map: ["enum.PinpointerLayers.Base"]
    - state: pinonnull
      map: ["enum.PinpointerLayers.Screen"]
      shader: unshaded
      visible: false
  - type: Icon
    state: pinpointer-syndicate
  - type: Pinpointer
    component: NukeDisk
    targetName: nuclear authentication disk

- type: entity
  name: "універсальна пінпоінтер"
  description: "Портативний пристрій для відстеження, який фіксує будь-яку фізичну особу у вимкненому стані. Тримайте вертикально, щоб зберегти точність."
  id: PinpointerUniversal
  parent: PinpointerBase
  components:
  - type: Sprite
    layers:
    - state: pinpointer-way
      map: ["enum.PinpointerLayers.Base"]
    - state: pinonnull
      map: ["enum.PinpointerLayers.Screen"]
      shader: unshaded
      visible: false
  - type: Icon
    state: pinpointer-way
  - type: Pinpointer
    updateTargetName: true
    canRetarget: true

- type: entity
  parent: PinpointerBase
  name: "пінпойнтер станції"
  description: "Портативний пристрій для відстеження, який веде в напрямку будь-якої найближчої станції."
  id: PinpointerStation
  suffix: Station
  components:
  - type: Sprite
    layers:
    - state: pinpointer-station
      map: ["enum.PinpointerLayers.Base"]
    - state: pinonnull
      map: ["enum.PinpointerLayers.Screen"]
      shader: unshaded
      visible: false
  - type: Icon
    state: pinpointer-station
  - type: Pinpointer
    component: ResearchServer
    targetName: the station
