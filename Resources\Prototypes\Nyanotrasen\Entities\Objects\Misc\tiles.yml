- type: entity
  name: "темна трав'яна плитка"
  parent: FloorTileItemBase
  id: FloorTileItemGrassDark
  components:
  - type: Sprite
    sprite: /Textures/Nyanotrasen/Objects/Tiles/tiles.rsi
    state: grassdark
  - type: Item
    heldPrefix: grassdark
  - type: FloorTile
    outputs:
      - FloorGrassDark
  - type: Stack
    stackType: FloorTileGrassDark

- type: entity
  name: "світла трав'яна плитка"
  parent: FloorTileItemBase
  id: FloorTileItemGrassLight
  components:
  - type: Sprite
    sprite: /Textures/Nyanotrasen/Objects/Tiles/tiles.rsi
    state: grasslight
  - type: Item
    heldPrefix: grasslight
  - type: FloorTile
    outputs:
      - FloorGrassLight
  - type: Stack
    stackType: FloorTileGrassLight

- type: entity
  name: "земляна плитка"
  parent: FloorTileItemBase
  id: FloorTileItemDirt
  components:
  - type: Sprite
    sprite: /Textures/Nyanotrasen/Objects/Tiles/tiles.rsi
    state: dirt
  - type: Item
    heldPrefix: dirt
  - type: FloorTile
    outputs:
      - FloorDirt
  - type: Stack
    stackType: FloorTileDirt

- type: entity
  name: "керамічна плитка"
  parent: FloorTileItemBase
  id: FloorTileItemBedrock
  components:
  - type: Sprite
    sprite: /Textures/Nyanotrasen/Objects/Tiles/tiles.rsi
    state: bedrock
  - type: Item
    heldPrefix: bedrock
  - type: FloorTile
    outputs:
      - FloorBedrock
  - type: Stack
    stackType: FloorTileBedrock
