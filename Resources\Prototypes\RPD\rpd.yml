- type: rcd
  id: PipeFourway
  category: Piping
  sprite: /Textures/Interface/Radial/RPD/fourway.png
  mode: ConstructObject
  prototype: GasPipeFourway
  cost: 1
  delay: 0
  rotation: User
  fx: EffectRCDConstruct0

- type: rcd
  id: PipeStraight
  category: Piping
  sprite: /Textures/Interface/Radial/RPD/straight.png
  mode: ConstructObject
  prototype: GasPipeStraight
  cost: 1
  delay: 0
  rotation: User
  fx: EffectRCDConstruct0

- type: rcd
  id: PipeBend
  category: Piping
  sprite: /Textures/Interface/Radial/RPD/bend.png
  mode: ConstructObject
  prototype: GasPipeBend
  cost: 1
  delay: 0
  rotation: User
  fx: EffectRCDConstruct0

- type: rcd
  id: PipeTJunction
  category: Piping
  sprite: /Textures/Interface/Radial/RPD/tjunction.png
  mode: ConstructObject
  prototype: GasPipeTJunction
  cost: 1
  delay: 0
  collisionMask: Impassable
  rotation: User
  fx: EffectRCDConstruct0

- type: rcd
  id: PressurePump
  category: PumpsValves
  sprite: /Textures/Interface/Radial/RPD/pump_pressure.png
  mode: ConstructObject
  prototype: GasPressurePump
  cost: 1
  delay: 0
  collisionMask: Impassable
  rotation: User
  fx: EffectRCDConstruct0

- type: rcd
  id: VolumetricPump
  category: PumpsValves
  sprite: /Textures/Interface/Radial/RPD/pump_volume.png
  mode: ConstructObject
  prototype: GasVolumePump
  cost: 1
  delay: 0
  collisionMask: Impassable
  rotation: User
  fx: EffectRCDConstruct0

- type: rcd
  id: ManualValve
  category: PumpsValves
  sprite: /Textures/Interface/Radial/RPD/pump_manual_valve.png
  mode: ConstructObject
  prototype: GasValve
  cost: 1
  delay: 0
  collisionMask: Impassable
  rotation: User
  fx: EffectRCDConstruct0

- type: rcd
  id: SignalValve
  category: PumpsValves
  sprite: /Textures/Interface/Radial/RPD/pump_signal_valve.png
  mode: ConstructObject
  prototype: SignalControlledValve
  cost: 1
  delay: 0
  collisionMask: Impassable
  rotation: User
  fx: EffectRCDConstruct0

- type: rcd
  id: PressureValve
  category: PumpsValves
  sprite: /Textures/Interface/Radial/RPD/pneumatic_valve.png
  mode: ConstructObject
  prototype: PressureControlledValve
  cost: 1
  delay: 0
  collisionMask: Impassable
  rotation: User
  fx: EffectRCDConstruct0

- type: rcd
  id: OutletInjector
  category: Vents
  sprite: /Textures/Interface/Radial/RPD/injector.png
  mode: ConstructObject
  prototype: GasOutletInjector
  cost: 1
  delay: 0
  collisionMask: Impassable
  rotation: User
  fx: EffectRCDConstruct0

- type: rcd
  id: VentScrubber
  category: Vents
  sprite: /Textures/Interface/Radial/RPD/scrub_off.png
  mode: ConstructObject
  prototype: GasVentScrubber
  cost: 1
  delay: 0
  collisionMask: Impassable
  rotation: User
  fx: EffectRCDConstruct0

- type: rcd
  id: VentGas
  category: Vents
  sprite: /Textures/Interface/Radial/RPD/vent_off.png
  mode: ConstructObject
  prototype: GasVentPump
  cost: 1
  delay: 0
  collisionMask: Impassable
  rotation: User
  fx: EffectRCDConstruct0

- type: rcd
  id: DualPortVent
  category: Vents
  sprite: /Textures/Interface/Radial/RPD/dual_port.png
  mode: ConstructObject
  prototype: GasDualPortVentPump
  cost: 1
  delay: 0
  collisionMask: Impassable
  rotation: User
  fx: EffectRCDConstruct0

- type: rcd
  id: VentPassive
  category: Vents
  sprite: /Textures/Interface/Radial/RPD/vent_passive.png
  mode: ConstructObject
  prototype: GasPassiveVent
  cost: 1
  delay: 0
  collisionMask: Impassable
  rotation: User
  fx: EffectRCDConstruct0

- type: rcd
  id: Radiator
  category: AtmosphericUtility
  sprite: /Textures/Interface/Radial/RPD/radiator.png
  mode: ConstructObject
  prototype: HeatExchanger
  cost: 1
  delay: 0
  collisionMask: Impassable
  rotation: User
  fx: EffectRCDConstruct0

- type: rcd
  id: MixerGas
  category: AtmosphericUtility
  sprite: /Textures/Interface/Radial/RPD/gas_mixer.png
  mode: ConstructObject
  prototype: GasMixer
  mirrorPrototype: GasMixerFlipped
  cost: 1
  delay: 0
  collisionMask: Impassable
  rotation: User
  fx: EffectRCDConstruct0

- type: rcd
  id: FilterGas
  category: AtmosphericUtility
  sprite: /Textures/Interface/Radial/RPD/gas_filter.png
  mode: ConstructObject
  prototype: GasFilter
  mirrorPrototype: GasFilterFlipped
  cost: 1
  delay: 0
  collisionMask: Impassable
  rotation: User
  fx: EffectRCDConstruct0

- type: rcd
  id: CanisterPort
  category: AtmosphericUtility
  sprite: /Textures/Interface/Radial/RPD/port.png
  mode: ConstructObject
  prototype: GasPort
  cost: 1
  delay: 0
  collisionMask: Impassable
  rotation: User
  fx: EffectRCDConstruct0
