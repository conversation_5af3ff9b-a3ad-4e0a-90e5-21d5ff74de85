# TODO: Add descriptions (many)
# TODO BODY: Part damage
- type: entity
  id: PartMoth
  parent: [BaseItem, BasePart]
  name: "частина тіла молі"
  abstract: true
  components:
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 3
      - ReagentId: Blood
        Quantity: 10

- type: entity
  id: TorsoMoth
  name: "тулуб молі"
  parent: [PartMoth, BaseTorso]
  components:
  - type: Sprite
    sprite: Mobs/Species/Moth/parts.rsi
    state: "torso_m"
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 10
      - ReagentId: Blood
        Quantity: 20


- type: entity
  id: HeadMoth
  name: "голова молі"
  parent: [PartMoth, BaseHead]
  components:
  - type: Sprite
    sprite: Mobs/Species/Moth/parts.rsi
    state: "head_m"
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 5
      - ReagentId: Blood
        Quantity: 10

- type: entity
  id: LeftArmMoth
  name: "ліва рука молі"
  parent: [PartMoth, BaseLeftArm]
  components:
  - type: Sprite
    sprite: Mobs/Species/Moth/parts.rsi
    state: "l_arm"

- type: entity
  id: RightArmMoth
  name: "права рука молі"
  parent: [PartMoth, BaseRightArm]
  components:
  - type: Sprite
    sprite: Mobs/Species/Moth/parts.rsi
    state: "r_arm"

- type: entity
  id: LeftHandMoth
  name: "ліва долоня молі"
  parent: [PartMoth, BaseLeftHand]
  components:
  - type: Sprite
    sprite: Mobs/Species/Moth/parts.rsi
    state: "l_hand"

- type: entity
  id: RightHandMoth
  name: "права долоня молі"
  parent: [PartMoth, BaseRightHand]
  components:
  - type: Sprite
    sprite: Mobs/Species/Moth/parts.rsi
    state: "r_hand"

- type: entity
  id: LeftLegMoth
  name: "ліва ніжка молі"
  parent: [PartMoth, BaseLeftLeg]
  components:
  - type: Sprite
    sprite: Mobs/Species/Moth/parts.rsi
    state: "l_leg"

- type: entity
  id: RightLegMoth
  name: "права ніжка молі"
  parent: [PartMoth, BaseRightLeg]
  components:
  - type: Sprite
    sprite: Mobs/Species/Moth/parts.rsi
    state: "r_leg"

- type: entity
  id: LeftFootMoth
  name: "ліва стопа молі"
  parent: [PartMoth, BaseLeftFoot]
  components:
  - type: Sprite
    sprite: Mobs/Species/Moth/parts.rsi
    state: "l_foot"

- type: entity
  id: RightFootMoth
  name: "права стопа молі"
  parent: [PartMoth, BaseRightFoot]
  components:
  - type: Sprite
    sprite: Mobs/Species/Moth/parts.rsi
    state: "r_foot"
