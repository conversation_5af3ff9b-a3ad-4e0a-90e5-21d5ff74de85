- type: entity
  name: "п<PERSON><PERSON><PERSON><PERSON>"
  parent: BaseItem
  id: Paper
  description: "Аркуш білого паперу."
  components:
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    layers:
    - state: paper
    - state: paper_words
      map: ["enum.PaperVisualLayers.Writing"]
      visible: false
    - state: paper_stamp-generic
      map: ["enum.PaperVisualLayers.Stamp"]
      visible: false
  - type: Paper
  - type: PaperLabelType
  - type: ActivatableUI
    key: enum.PaperUiKey.Key
    requiresComplex: false
  - type: UserInterface
    interfaces:
      enum.PaperUiKey.Key:
        type: PaperBoundUserInterface
  - type: Item
    size: Tiny
  - type: Tag
    tags:
    - Document
    - Trash
    - Paper
  - type: Appearance
  - type: FaxableObject
  - type: PaperVisuals
  - type: Flammable
    fireSpread: true
    canResistFire: false
    alwaysCombustible: true
    canExtinguish: false # Mwahaha! Let the world burn because of one piece of paper!
    damage:
      types:
        Heat: 1
  - type: FireVisuals
    sprite: Effects/fire.rsi
    normalState: fire
  - type: Damageable
    damageModifierSet: Wood
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 15
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
            Ash:
              min: 1
              max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Food
    solution: food
    delay: 7
    forceFeedDelay: 7
  - type: BadFood
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 1
        reagents:
        - ReagentId: Fiber
          Quantity: 1
  - type: EmitSoundOnPickup
    sound: /Audio/SimpleStation14/Items/Handling/paper_pickup.ogg
  - type: EmitSoundOnDrop
    sound: /Audio/SimpleStation14/Items/Handling/paper_drop.ogg
  - type: EmitSoundOnLand
    sound: /Audio/SimpleStation14/Items/Handling/paper_drop.ogg
# Corvax-Printer-Start
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      SheetPrinter: 100
# Corvax-Printer-End

- type: entity
  name: "макулатура"
  parent: Paper
  id: PaperScrap
  description: "Зім'ятий аркуш білого паперу."
  components:
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    layers:
    - state: scrap

- type: entity
  name: "офісний папір"
  parent: Paper
  id: PaperOffice
  description: "Звичайний аркуш офісного паперу."
  components:
  - type: PaperVisuals
    backgroundImagePath: "/Textures/Interface/Paper/paper_background_default.svg.96dpi.png"
    contentImagePath: "/Textures/Interface/Paper/paper_content_lined.svg.96dpi.png"
    backgroundPatchMargin: 16.0, 16.0, 16.0, 16.0
    contentMargin: 16.0, 16.0, 16.0, 16.0

- type: entity
  name: "роздруківка аналізатора артефактів"
  parent: Paper
  id: PaperArtifactAnalyzer
  description: "Показання пристрою, про який забули вчасно"
  components:
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    layers:
    - state: paper_dotmatrix
    - state: paper_dotmatrix_words
      map: ["enum.PaperVisualLayers.Writing"]
      visible: false
    - state: paper_stamp-generic
      map: ["enum.PaperVisualLayers.Stamp"]
      visible: false
  - type: GuideHelp
    guides:
    - ArtifactReports
  - type: PaperVisuals
    headerImagePath: "/Textures/Interface/Paper/paper_heading_artifact_analyzer.svg.96dpi.png"
    headerMargin: 0.0, 0.0, 0.0, 16.0
    backgroundImagePath: "/Textures/Interface/Paper/paper_background_dotmatrix.svg.96dpi.png"
    backgroundImageTile: true
    backgroundPatchMargin: 37.0, 0.0, 37.0, 0.0
    contentImagePath: "/Textures/Interface/Paper/paper_content_dotmatrix.svg.96dpi.png"
    contentImageNumLines: 2
    contentMargin: 16.0, 16.0, 16.0, 0.0
    # Make this a wide dot-matrix printer
    maxWritableArea: 400.0, 0.0

- type: entity
  name: "думки капітана"
  parent: Paper
  id: PaperCaptainsThoughts
  description: "Сторінка щоденника капітана. Розкішного лавандового кольору."
  components:
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    layers:
    - state: paper
      color: "#e6e6fa"
    - state: paper_words
      map: ["enum.PaperVisualLayers.Writing"]
      color: "#e6e6fa"
      visible: false
    - state: paper_stamp-generic
      map: ["enum.PaperVisualLayers.Stamp"]
      visible: false
  - type: PaperLabelType
    paperType: CaptainsPaper
  - type: PaperVisuals
    headerImagePath: "/Textures/Interface/Paper/paper_heading_captains_thoughts.svg.96dpi.png"
    backgroundImagePath: "/Textures/Interface/Paper/paper_background_default.svg.96dpi.png"
    backgroundModulate: "#e6e6fa"
    backgroundPatchMargin: 16.0, 16.0, 16.0, 16.0
    contentMargin: 32.0, 16.0, 32.0, 0.0

- type: entity
  name: "примітка"
  description: "Аркуш білого паперу."
  id: PaperWrittenPunpunNote
  parent: PaperCaptainsThoughts
  suffix: Punpun Note
  components:
    - type: Paper
      content: I, Punpun, invoke my right to have all of my clones on the NT family vacation to the meat packaging plant one out of every 15 shifts.

- type: entity
  name: "вантажна накладна"
  parent: Paper
  id: PaperCargoInvoice
  description: "Єдина одиниця бюрократії."
  components:
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    layers:
    - state: paper
      color: "#9ef5ff"
    - state: paper_words
      map: ["enum.PaperVisualLayers.Writing"]
      color: "#9ef5ff"
      visible: false
    - state: paper_stamp-generic
      map: ["enum.PaperVisualLayers.Stamp"]
      visible: false
  - type: PaperLabelType
    paperType: Invoice
  - type: PaperVisuals
    backgroundImagePath: "/Textures/Interface/Paper/paper_background_default.svg.96dpi.png"
    contentImagePath: "/Textures/Interface/Paper/paper_content_lined.svg.96dpi.png"
    backgroundModulate: "#9ef5ff"
    contentImageModulate: "#9ef5ff"
    backgroundPatchMargin: 16.0, 16.0, 16.0, 16.0
    contentMargin: 16.0, 16.0, 16.0, 16.0
    headerImagePath: "/Textures/Interface/Paper/paper_heading_cargo_invoice.svg.96dpi.png"
    headerMargin: 0.0, 12.0, 0.0, 0.0

- type: entity
  id: PaperCargoBountyManifest
  parent: PaperCargoInvoice
  name: "маніфест про винагороду"
  description: "Паперова етикетка з позначкою, що ящик містить винагороду. Продаж ящика з такою етикеткою принесе винагороду."
  components:
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    layers:
    - state: paper
      color: "#f7e574"
    - state: paper_words
      map: ["enum.PaperVisualLayers.Writing"]
      color: "#f7e574"
      visible: false
    - state: paper_stamp-generic
      map: ["enum.PaperVisualLayers.Stamp"]
      visible: false
  - type: PaperLabelType
    paperType: Bounty
  - type: PaperVisuals
    backgroundImagePath: "/Textures/Interface/Paper/paper_background_default.svg.96dpi.png"
    contentImagePath: "/Textures/Interface/Paper/paper_content_lined.svg.96dpi.png"
    backgroundModulate: "#f7e574"
    contentImageModulate: "#f7e574"
    backgroundPatchMargin: 16.0, 16.0, 16.0, 16.0
    contentMargin: 16.0, 16.0, 16.0, 16.0
    headerImagePath: "/Textures/Interface/Paper/paper_heading_cargo_invoice.svg.96dpi.png"
    headerMargin: 0.0, 12.0, 0.0, 0.0
  - type: CargoBountyLabel
  - type: StaticPrice
    price: 0
  - type: GuideHelp
    guides:
    - CargoBounties

- type: entity
  name: "таблиця символів"
  parent: Paper
  id: PaperCNCSheet # legally gray zone of using "D&D" and "DND"
  description: "Аркуш для ваших персонажів Коропів і Крипт."
  components:
  - type: Paper
    contentSize: 10000
    escapeFormatting: false
    content: book-cnc-sheet
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    layers:
      - state: paper
        color: "#cccccc"
      - state: paper_words
        map: ["enum.PaperVisualLayers.Writing"]
        color: "#cccccc" #aaaaaaaaaaaaaaaaaaaaaaa
        visible: false
      - state: paper_stamp-generic
        map: ["enum.PaperVisualLayers.Stamp"]
        visible: false
  - type: PaperVisuals
    backgroundImagePath: "/Textures/Interface/Paper/paper_background_default.svg.96dpi.png"
    contentImagePath: "/Textures/Interface/Paper/paper_content_lined.svg.96dpi.png"
    backgroundModulate: "#cccccc"
    contentImageModulate: "#cccccc"
    backgroundPatchMargin: 16.0, 16.0, 16.0, 16.0
    contentMargin: 16.0, 16.0, 16.0, 16.0

- type: entity
  parent: Paper
  id: PaperWritten
  categories: [ HideSpawnMenu ]
  components:
  - type: Paper
  - type: Sprite
    layers:
    # Changing it here is fine - if the PaperStatus key is actually added,
    #  something happened, so that ought to override this either way.
    - state: paper_words
  - type: ActivatableUI
    key: enum.PaperUiKey.Key
  - type: UserInterface
    interfaces:
      enum.PaperUiKey.Key:
        type: PaperBoundUserInterface

- type: entity
  parent: Paper
  id: NukeCodePaper
  name: "ядерні коди автентифікації"
  components:
  - type: NukeCodePaper
    allNukesAvailable: true
  - type: Paper

- type: entity
  parent: NukeCodePaper
  id: NukeCodePaperStation
  suffix: Station Only
  components:
  - type: NukeCodePaper

- type: entity
  parent: Paper
  id: StationGoalPaper
  name: "мета станції"
  description: "Схоже, у вас багато роботи."
  components:
    - type: Paper
      stampState: paper_stamp-centcom
      stampedBy:
      - stampedName: stamp-component-stamped-name-centcom
        stampedColor: "#bb3232"


- type: entity
  name: "ручка"
  parent: BaseItem
  id: Pen
  description: "Ручка з темним чорнилом."
  components:
  - type: Tag
    tags:
    - Write
    - Pen
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    state: pen
  - type: Item
    sprite: Objects/Misc/bureaucracy.rsi
    heldPrefix: pen
    size: Tiny
  - type: PhysicalComposition
    materialComposition:
      Steel: 25
  # Shitmed Change
  - type: Tending
    speed: 0.55
  - type: SurgeryTool
    startSound:
      path: /Audio/_Shitmed/Medical/Surgery/retractor1.ogg
    endSound:
      path: /Audio/_Shitmed/Medical/Surgery/hemostat1.ogg
  - type: EmbeddableProjectile
    offset: 0.3,0.0
    removalTime: 0.0
  - type: ThrowingAngle
    angle: 315
  - type: DamageOtherOnHit
    damage:
      types:
        Piercing: 3

- type: entity
  id: BaseAdvancedPen
  # Goobstation - Return pen embeddability
  parent: Pen
  abstract: true
  components:
  - type: Tag
    tags:
    - Write
    - WriteIgnoreStamps
    - Pickaxe
    - Pen
  - type: MeleeWeapon
    wideAnimationRotation: -45
    damage:
      types:
        Piercing: 15
    soundHit:
      path: /Audio/Weapons/bladeslice.ogg
  - type: Tool
    qualities:
    - Screwing
    useSound:
      collection: Screwdriver
  - type: DamageOtherOnHit
    damage:
      types:
        Piercing: 20 # Goobstation - ow

- type: entity
  parent: Pen
  id: PenEmbeddable
  abstract: true
  components:
  - type: LandAtCursor
  - type: DamageOtherOnHit
    damage:
      types:
        Piercing: 5

#TODO: I want the luxury pen to write a cool font like Merriweather in the future.

- type: entity
  name: "розкішна ручка"
  parent: Pen
  id: LuxuryPen
  description: "Модна і дорога ручка, на яку ви заслуговуєте лише в тому випадку, якщо маєте достатню кваліфікацію для роботи з великими обсягами паперової документації."
  components:
  - type: Sprite
    state: luxury_pen
  - type: Item
    heldPrefix: luxury_pen

- type: entity
  name: "Ручка Cybersun"
  parent: PenEmbeddable
  id: CyberPen
  description: "Високотехнологічна ручка від юридичного відділу Cybersun, здатна заломлювати тверде світло під неможливими кутами через алмазний наконечник, щоб писати. Настільки потужна, що навіть здатна переписувати офіційні документи з печаткою, якщо виникне така потреба."
  components:
  - type: Tag
    tags:
    - Write
    - WriteIgnoreStamps
    - Pickaxe
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    state: overpriced_pen
  - type: MeleeWeapon
    wideAnimationRotation: -45
    damage:
      types:
        Piercing: 15
    soundHit:
      path: /Audio/Weapons/bladeslice.ogg
  - type: DamageOtherOnHit
    damage:
      types:
        Piercing: 15
  - type: EmbeddableProjectile
    removalTime: 2
  - type: EmbedPassiveDamage
  - type: Tool
    qualities:
      - Screwing
    useSound:
      collection: Screwdriver
  - type: Item
    sprite: Objects/Misc/bureaucracy.rsi
    heldPrefix: overpriced_pen
    size: Tiny

- type: entity
  name: "капітанська авторучка"
  parent: PenEmbeddable
  id: PenCap
  description: "Розкішна авторучка для капітана станції."
  components:
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    state: pen_cap
  - type: DamageOtherOnHit
    damage:
      types:
        Piercing: 8

- type: entity
  name: "Ручка CentCom"
  parent: CyberPen
  id: PenCentcom
  description: "Намагаючись не відставати від \"могутності\" бюрократії кібер-солдата, NT випустила репліку кібер-ручки у своєму фірмовому стилі."
  components:
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    state: pen_centcom
  - type: Item
    sprite: Objects/Misc/bureaucracy.rsi
    heldPrefix: pen_centcom

- type: entity
  name: "пір'яна ручка Хмеля"
  parent: PenEmbeddable
  id: PenHop
  description: "Розкішна авторучка для хмелю станції."
  components:
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    state: pen_hop

- type: entity
  id: BoxFolderBase
  parent: BoxBase
  name: "папка"
  description: "Папка з надсекретними документами."
  components:
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    layers:
    - state: folder-colormap
    - state: folder-base
# RandomSpriteColor requires netsync which is currently incompatible with ItemMapper
#  - type: RandomSpriteColor
#    sprite: Objects/Misc/bureaucracy.rsi
#    state: folder-colormap
#    colors:
#      red: "#cc2323"
#      blue: "#355d99"
#      yellow: "#b38e3c"
#      white: "#e6e6e6"
#      grey: "#999999"
#      black: "#3f3f3f"
#      green: "#43bc38"
  - type: Item
    sprite: Objects/Misc/bureaucracy.rsi
    size: Small
    shape: null
  - type: Storage
    maxItemSize: Small
    grid:
    - 0,0,4,3
    whitelist:
      tags:
        - Document
  - type: ItemMapper
    mapLayers:
      folder-overlay-paper:
        whitelist:
          tags:
          - Document
  - type: Appearance
  - type: Tag
    tags:
    - Folder
  - type: StorageFill
    contents:
      - id: Paper
        prob: 0.5
      - id: PaperOffice
        prob: 0.4
      - id: Paper
        prob: 0.3
      - id: PaperOffice
        prob: 0.2
      - id: Paper
        prob: 0.2

- type: entity
  id: BoxFolderRed
  parent: BoxFolderBase
  suffix: Red
  components:
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    layers:
    - state: folder-colormap
      color: "#cc2323"
    - state: folder-base

- type: entity
  id: BoxFolderBlue
  parent: BoxFolderBase
  suffix: Blue
  components:
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    layers:
    - state: folder-colormap
      color: "#355d99"
    - state: folder-base

- type: entity
  id: BoxFolderYellow
  parent: BoxFolderBase
  suffix: Yellow
  components:
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    layers:
    - state: folder-colormap
      color: "#b38e3c"
    - state: folder-base

- type: entity
  id: BoxFolderWhite
  parent: BoxFolderBase
  suffix: White
  components:
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    layers:
    - state: folder-white
    - state: folder-base

- type: entity
  id: BoxFolderGrey
  parent: BoxFolderBase
  suffix: Grey
  components:
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    layers:
    - state: folder-colormap
      color: "#999999"
    - state: folder-base

- type: entity
  id: BoxFolderBlack
  parent: BoxFolderBase
  suffix: Black
  components:
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    layers:
    - state: folder-colormap
      color: "#3f3f3f"
    - state: folder-base

- type: entity
  id: BoxFolderGreen
  parent: BoxFolderBase
  suffix: Green
  components:
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    layers:
    - state: folder-colormap
      color: "#43bc38"
    - state: folder-base

- type: entity
  id: BoxFolderCentCom
  name: "Папка CentCom"
  parent: BoxFolderBase
  suffix: DO NOT MAP
  description: "Жалюгідна маленька купка секретів CentCom!"
  components:
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    layers:
    - state: folder-centcom
    - state: folder-base

- type: entity
  id: BoxFolderClipboard
  parent: BoxFolderBase
  name: "буфер обміну"
  description: "Зброя вибору для тих, хто знаходиться на передовій лінії бюрократії."
  components:
  - type: Sprite
    sprite: Objects/Misc/clipboard.rsi
    layers:
    - state: clipboard
    - state: clipboard_paper
      map: ["clipboard_paper"]
      visible: false
    - state: clipboard_pen
      map: ["clipboard_pen"]
      visible: false
    - state: clipboard_over
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
        ents: []
      pen_slot: !type:ContainerSlot {}
  - type: ItemSlots
    slots:
      pen_slot:
        name: clipboard-slot-component-slot-name-pen
        whitelist:
          tags:
            - Write
        insertOnInteract: false
  - type: Item
    sprite: Objects/Misc/clipboard.rsi
    size: Small
  - type: Clothing
    slots: [belt]
    quickEquip: false
    sprite: Objects/Misc/clipboard.rsi
  - type: Storage
    grid:
    - 0,0,5,3
    whitelist:
      tags:
        - Document
  - type: ItemMapper
    mapLayers:
      clipboard_paper:
        whitelist:
          tags:
          - Document
      clipboard_pen:
        whitelist:
          tags:
          - Write
  - type: MeleeWeapon
    wideAnimationRotation: 180
    damage:
      types:
        Blunt: 6

- type: entity
  id: BoxFolderCentComClipboard
  parent: BoxFolderClipboard
  name: "Буфер обміну CentCom"
  description: "Розкішний блокнот, оббитий зеленим оксамитом. Його часто носять із собою чиновники Центру, але рідко коли ним користуються."
  components:
  - type: Sprite
    sprite: Objects/Misc/cc-clipboard.rsi
    layers:
    - state: clipboard
    - state: clipboard_paper
      map: ["clipboard_paper"]
      visible: false
    - state: clipboard_pen
      map: ["clipboard_pen"]
      visible: false
    - state: clipboard_over
  - type: Item
    sprite: Objects/Misc/cc-clipboard.rsi
    size: Small
  - type: Clothing
    slots: [belt]
    quickEquip: false
    sprite: Objects/Misc/cc-clipboard.rsi

- type: entity
  id: BoxFolderQmClipboard
  parent: BoxFolderBase
  name: "електроплата замовлень"
  description: "Громіздкий електричний буфер обміну, заповнений замовленнями на доставку та фінансовими деталями. Маючи стільки компрометуючих документів, ви повинні зберігати його в безпеці."
  components:
  - type: Sprite
    sprite: Objects/Misc/qm_clipboard.rsi
    layers:
    - state: qm_clipboard
    - state: qm_clipboard_paper
      map: ["qm_clipboard_paper"]
      visible: false
    - state: qm_clipboard_pen
      map: ["qm_clipboard_pen"]
      visible: false
    - state: qm_clipboard_over
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
        ents: []
      pen_slot: !type:ContainerSlot {}
  - type: ItemSlots
    slots:
      pen_slot:
        name: clipboard-slot-component-slot-name-pen
        whitelist:
          tags:
            - Write
        insertOnInteract: true
  - type: Item
    sprite: Objects/Misc/qm_clipboard.rsi
    size: Normal
  - type: Clothing
    slots: [belt]
    quickEquip: false
    sprite: Objects/Misc/qm_clipboard.rsi
  - type: Storage
    grid:
    - 0,0,4,3
    quickInsert: true
    whitelist:
      tags:
        - Document
  - type: StorageFill
    contents: [] #to override base folder fill
  - type: ItemMapper
    mapLayers:
      qm_clipboard_paper:
        whitelist:
          tags:
          - Document
      qm_clipboard_pen:
        whitelist:
          tags:
          - Write
  - type: CargoOrderConsole
  - type: BankClient
  - type: ActivatableUI
    verbText: qm-clipboard-computer-verb-text
    key: enum.CargoConsoleUiKey.Orders
  - type: UserInterface
    interfaces:
      enum.CargoConsoleUiKey.Orders:
        type: CargoOrderConsoleBoundUserInterface
      enum.StorageUiKey.Key:
        type: StorageBoundUserInterface
  - type: MeleeWeapon
    wideAnimationRotation: 180
    damage:
      types:
        Blunt: 10
  - type: StealTarget
    stealGroup: BoxFolderQmClipboard
