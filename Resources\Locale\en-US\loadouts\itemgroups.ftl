# This list is sorted Mixed Alphabetically with Generic always being placed first, Departments alphabetically, Items Groups Alphabetically, and Jobs Alphabetically after all Department items
# Generic - Clothing
character-item-group-LoadoutBackpacks = Civilian Backpacks
character-item-group-LoadoutEyes = Civilian Eyewear
character-item-group-LoadoutGloves = Civilian Gloves
character-item-group-LoadoutHead = Civilian Headgear
character-item-group-LoadoutMasks = Civilian Masks
character-item-group-LoadoutNeck = Civilian Neckwear
character-item-group-LoadoutOuter = Civilian Outerwear
character-item-group-LoadoutBelt = Civilian Belts
character-item-group-LoadoutShoes = Civilian Shoes
character-item-group-LoadoutUniformsCivilian = Civilian Uniforms

# Generic - Items
character-item-group-LoadoutAirTank = Emergency Air Tanks
character-item-group-LoadoutLighters = Lighters
character-item-group-LoadoutInstrumentsAny = Musical Instruments (Non-Musician)
character-item-group-LoadoutSmokes = Smokeables
character-item-group-LoadoutBoxKits = Survival Kits
character-item-group-LoadoutWritables = Writing Tools
character-item-group-LoadoutPets = Pets
character-item-group-LoadoutCards = Playing Cards
character-item-group-LoadoutPlushie = Plushies

# Job Specific Template
character-item-group-LoadoutJOBBackpacks = JOB Backpacks
character-item-group-LoadoutJOBBelt = JOB Belt
character-item-group-LoadoutJOBEars = JOB Ears
character-item-group-LoadoutJOBEquipment = JOB Equipment
character-item-group-LoadoutJOBEyes = JOB Eyewear
character-item-group-LoadoutJOBloves = JOB Gloves
character-item-group-LoadoutJOBHead = JOB Headgear
character-item-group-LoadoutJOBId = JOB Id
character-item-group-LoadoutJOBNeck = JOB Neckwear
character-item-group-LoadoutJOBMask = JOB Masks
character-item-group-LoadoutJOBOuter = JOB Outerwear
character-item-group-LoadoutJOBShoes = JOB Shoes
character-item-group-LoadoutJOBUniforms = JOB Uniforms

# Command
character-item-group-LoadoutCommandBackpacks = Command Backpacks
character-item-group-LoadoutCommandBelt = Command Belt
character-item-group-LoadoutCommandEars = Command Ears
character-item-group-LoadoutCommandEquipment = Command Equipment
character-item-group-LoadoutCommandEyes = Command Eyewear
character-item-group-LoadoutCommandloves = Command Gloves
character-item-group-LoadoutCommandHead = Command Headgear
character-item-group-LoadoutCommandId = Command Id
character-item-group-LoadoutCommandNeck = Command Neckwear
character-item-group-LoadoutCommandMask = Command Masks
character-item-group-LoadoutCommandOuter = Command Outerwear
character-item-group-LoadoutCommandShoes = Command Shoes
character-item-group-LoadoutCommandUniforms = Command Uniforms

# Command - Captain
character-item-group-LoadoutCaptainBackpacks = Captain Backpacks
character-item-group-LoadoutCaptainBelt = Captain's Belt
character-item-group-LoadoutCaptainEars = Captain Ears
character-item-group-LoadoutCaptainEquipment = Captain Equipment
character-item-group-LoadoutCaptainTrinkets = Captain's Trinkets
character-item-group-LoadoutCaptainWeapon = Captain's Personal Weapon
character-item-group-LoadoutCaptainEyes = Captain's Eyewear
character-item-group-LoadoutCaptainGloves = Captain's Gloves
character-item-group-LoadoutCaptainHead = Captain's Headgear
character-item-group-LoadoutCaptainId = Captain's Id
character-item-group-LoadoutCaptainNeck = Captain's Neckwear
character-item-group-LoadoutCaptainMask = Captain's Masks
character-item-group-LoadoutCaptainOuter = Captain's Outerwear
character-item-group-LoadoutCaptainShoes = Captain's Shoes
character-item-group-LoadoutCaptainUniforms = Captain's Uniforms

# Command - Head Of Personnel
character-item-group-LoadoutHeadOfPersonnelBackpacks = Head of Personnel Backpacks
character-item-group-LoadoutHeadOfPersonnelBelt = Head of Personnel Belt
character-item-group-LoadoutHeadOfPersonnelEars = Head of Personnel Ears
character-item-group-LoadoutHeadOfPersonnelEquipment = Head of Personnel Equipment
character-item-group-LoadoutHeadOfPersonnelTrinkets = Head of Personnel Trinkets
character-item-group-LoadoutHeadOfPersonnelEyes = Head of Personnel Eyewear
character-item-group-LoadoutHeadOfPersonnelGloves = Head of Personnel Gloves
character-item-group-LoadoutHeadOfPersonnelHead = Head of Personnel Headgear
character-item-group-LoadoutHeadOfPersonnelId = Head of Personnel Id
character-item-group-LoadoutHeadOfPersonnelNeck = Head of Personnel Neckwear
character-item-group-LoadoutHeadOfPersonnelOuter = Head of Personnel Outerwear
character-item-group-LoadoutHeadOfPersonnelShoes = Head of Personnel Shoes
character-item-group-LoadoutHeadOfPersonnelUniforms = Head of Personnel Uniforms

# Dignitary - BlueshieldOfficer
character-item-group-LoadoutBlueshieldOfficerBackpacks = Blueshield Officer Backpacks
character-item-group-LoadoutBlueshieldOfficerVests = Blueshield Officer Vests
character-item-group-LoadoutBlueshieldOfficerUniforms = Blueshield Officer Uniforms
character-item-group-LoadoutBlueshieldOfficerPrimary = Blueshield Officer Primary Weapon

# Dignitary - Magistrate
character-item-group-LoadoutMagistrateHead = Magistrate Headgear
character-item-group-LoadoutMagistrateNeck = Magistrate Neckwear
character-item-group-LoadoutMagistrateOuter = Magistrate Outerwear
character-item-group-LoadoutMagistrateUniforms = Magistrate Uniforms

# Dignitary - Corporate Liaison
character-item-group-LoadoutNanorepBackpacks = Corporate Liaison Backpacks
character-item-group-LoadoutNanorepUniforms = Corporate Liaison Uniforms
character-item-group-LoadoutNanotrasenRepresentativeWeapon = Corporate Liaison Weapons

# Engineering
character-item-group-LoadoutEngineeringBackpacks = Engineering Backpacks
character-item-group-LoadoutEngineeringBelt = Engineering Belt
character-item-group-LoadoutEngineeringEars = Engineering Ears
character-item-group-LoadoutEngineeringEquipment = Engineering Equipment
character-item-group-LoadoutEngineeringEyes = Engineering Eyewear
character-item-group-LoadoutEngineeringGloves = Engineering Gloves
character-item-group-LoadoutEngineeringHead = Engineering Headgear
character-item-group-LoadoutEngineeringId = Engineering Id
character-item-group-LoadoutEngineeringNeck = Engineering Neckwear
character-item-group-LoadoutEngineeringMask = Engineering Masks
character-item-group-LoadoutEngineeringOuter = Engineering Outerwear
character-item-group-LoadoutEngineeringShoes = Engineering Shoes
character-item-group-LoadoutEngineeringUniforms = Engineering Uniforms

# Engineering - Atmospheric Technician
character-item-group-LoadoutAtmosphericTechnicianBackpacks = Atmospheric Technician Backpacks
character-item-group-LoadoutAtmosphericTechnicianBelt = Atmospheric Technician Belt
character-item-group-LoadoutAtmosphericTechnicianEars = Atmospheric Technician Ears
character-item-group-LoadoutAtmosphericTechnicianEquipment = Atmospheric Technician Equipment
character-item-group-LoadoutAtmosphericTechnicianEyes = Atmospheric Technician Eyewear
character-item-group-LoadoutAtmosphericTechniciangloves = Atmospheric Technician Gloves
character-item-group-LoadoutAtmosphericTechnicianHead = Atmospheric Technician Headgear
character-item-group-LoadoutAtmosphericTechnicianId = Atmospheric Technician Id
character-item-group-LoadoutAtmosphericTechnicianNeck = Atmospheric Technician Neckwear
character-item-group-LoadoutAtmosphericTechnicianMask = Atmospheric Technician Masks
character-item-group-LoadoutAtmosphericTechnicianOuter = Atmospheric Technician Outerwear
character-item-group-LoadoutAtmosphericTechnicianShoes = Atmospheric Technician Shoes
character-item-group-LoadoutAtmosphericTechnicianUniforms = Atmospheric Technician Uniforms

# Engineering - Chief Engineer
character-item-group-LoadoutChiefEngineerBackpacks = Chief Engineer Backpacks
character-item-group-LoadoutChiefEngineerBelt = Chief Engineer Belt
character-item-group-LoadoutChiefEngineerEars = Chief Engineer Ears
character-item-group-LoadoutChiefEngineerEquipment = Chief Engineer Equipment
character-item-group-LoadoutChiefEngineerEyes = Chief Engineer Eyewear
character-item-group-LoadoutChiefEngineerGloves = Chief Engineer Gloves
character-item-group-LoadoutChiefEngineerHead = Chief Engineer Headgear
character-item-group-LoadoutChiefEngineerId = Chief Engineer Id
character-item-group-LoadoutChiefEngineerNeck = Chief Engineer Neckwear
character-item-group-LoadoutChiefEngineerMask = Chief Engineer Masks
character-item-group-LoadoutChiefEngineerOuter = Chief Engineer Outerwear
character-item-group-LoadoutChiefEngineerShoes = Chief Engineer Shoes
character-item-group-LoadoutChiefEngineerUniforms = Chief Engineer Uniforms

# Engineering - Senior Engineer
character-item-group-LoadoutSeniorEngineerBackpacks = Senior Engineer Backpacks
character-item-group-LoadoutSeniorEngineerBelt = Senior Engineer Belt
character-item-group-LoadoutSeniorEngineerEars = Senior Engineer Ears
character-item-group-LoadoutSeniorEngineerEquipment = Senior Engineer Equipment
character-item-group-LoadoutSeniorEngineerEyes = Senior Engineer Eyewear
character-item-group-LoadoutSeniorEngineerGloves = Senior Engineer Gloves
character-item-group-LoadoutSeniorEngineerHead = Senior Engineer Headgear
character-item-group-LoadoutSeniorEngineerId = Senior Engineer Id
character-item-group-LoadoutSeniorEngineerNeck = Senior Engineer Neckwear
character-item-group-LoadoutSeniorEngineerMask = Senior Engineer Masks
character-item-group-LoadoutSeniorEngineerOuter = Senior Engineer Outerwear
character-item-group-LoadoutSeniorEngineerShoes = Senior Engineer Shoes
character-item-group-LoadoutSeniorEngineerUniforms = Senior Engineer Uniforms

# Engineering - Station Engineer
character-item-group-LoadoutStationEngineerBackpacks = Station Engineer Backpacks
character-item-group-LoadoutStationEngineerBelt = Station Engineer Belt
character-item-group-LoadoutStationEngineerEars = Station Engineer Ears
character-item-group-LoadoutStationEngineerEquipment = Station Engineer Equipment
character-item-group-LoadoutStationEngineerEyes = Station Engineer Eyewear
character-item-group-LoadoutStationEngineerGloves = Station Engineer Gloves
character-item-group-LoadoutStationEngineerHead = Station Engineer Headgear
character-item-group-LoadoutStationEngineerId = Station Engineer Id
character-item-group-LoadoutStationEngineerNeck = Station Engineer Neckwear
character-item-group-LoadoutStationEngineerMask = Station Engineer Masks
character-item-group-LoadoutStationEngineerOuter = Station Engineer Outerwear
character-item-group-LoadoutStationEngineerShoes = Station Engineer Shoes
character-item-group-LoadoutStationEngineerUniforms = Station Engineer Uniforms

# Engineering - Technical Assistant
character-item-group-LoadoutTechnicalAssistantBackpacks = Technical Assistant Backpacks
character-item-group-LoadoutTechnicalAssistantBelt = Technical Assistant Belt
character-item-group-LoadoutTechnicalAssistantEars = Technical Assistant Ears
character-item-group-LoadoutTechnicalAssistantEquipment = Technical Assistant Equipment
character-item-group-LoadoutTechnicalAssistantEyes = Technical Assistant Eyewear
character-item-group-LoadoutTechnicalAssistantGloves = Technical Assistant Gloves
character-item-group-LoadoutTechnicalAssistantHead = Technical Assistant Headgear
character-item-group-LoadoutTechnicalAssistantId = Technical Assistant Id
character-item-group-LoadoutTechnicalAssistantNeck = Technical Assistant Neckwear
character-item-group-LoadoutTechnicalAssistantMask = Technical Assistant Masks
character-item-group-LoadoutTechnicalAssistantOuter = Technical Assistant Outerwear
character-item-group-LoadoutTechnicalAssistantShoes = Technical Assistant Shoes
character-item-group-LoadoutTechnicalAssistantUniforms = Technical Assistant Uniforms

# Epistemics
character-item-group-LoadoutEpistemicsBackpacks = Epistemics Backpacks
character-item-group-LoadoutEpistemicsBelt = Epistemics Belt
character-item-group-LoadoutEpistemicsEars = Epistemics Ears
character-item-group-LoadoutEpistemicsEquipment = Epistemics Equipment
character-item-group-LoadoutEpistemicsEyes = Epistemics Eyewear
character-item-group-LoadoutEpistemicsGloves = Epistemics Gloves
character-item-group-LoadoutEpistemicsHead = Epistemics Headgear
character-item-group-LoadoutEpistemicsId = Epistemics Id
character-item-group-LoadoutEpistemicsNeck = Epistemics Neckwear
character-item-group-LoadoutEpistemicsMask = Epistemics Masks
character-item-group-LoadoutEpistemicsOuter = Epistemics Outerwear
character-item-group-LoadoutEpistemicsShoes = Epistemics Shoes
character-item-group-LoadoutEpistemicsUniforms = Epistemics Uniforms

# Epistemics - Acolyte
character-item-group-LoadoutAcolyteBackpacks = Acolyte Backpacks
character-item-group-LoadoutAcolyteBelt = Acolyte Belt
character-item-group-LoadoutAcolyteEars = Acolyte Ears
character-item-group-LoadoutAcolyteEquipment = Acolyte Equipment
character-item-group-LoadoutAcolyteEyes = Acolyte Eyewear
character-item-group-LoadoutAcolyteGloves = Acolyte Gloves
character-item-group-LoadoutAcolyteHead = Acolyte Headgear
character-item-group-LoadoutAcolyteId = Acolyte Id
character-item-group-LoadoutAcolyteNeck = Acolyte Neckwear
character-item-group-LoadoutAcolyteMask = Acolyte Masks
character-item-group-LoadoutAcolyteOuter = Acolyte Outerwear
character-item-group-LoadoutAcolyteShoes = Acolyte Shoes
character-item-group-LoadoutAcolyteUniforms = Acolyte Uniforms

# Epistemics - Cataloger
character-item-group-LoadoutCatalogerBackpacks = Cataloger Backpacks
character-item-group-LoadoutCatalogerBelt = Cataloger Belt
character-item-group-LoadoutCatalogerEars = Cataloger Ears
character-item-group-LoadoutCatalogerEquipment = Cataloger Equipment
character-item-group-LoadoutCatalogerEyes = Cataloger Eyewear
character-item-group-LoadoutCatalogerGloves = Cataloger Gloves
character-item-group-LoadoutCatalogerHead = Cataloger Headgear
character-item-group-LoadoutCatalogerId = Cataloger Id
character-item-group-LoadoutCatalogerNeck = Cataloger Neckwear
character-item-group-LoadoutCatalogerMask = Cataloger Masks
character-item-group-LoadoutCatalogerOuter = Cataloger Outerwear
character-item-group-LoadoutCatalogerShoes = Cataloger Shoes
character-item-group-LoadoutCatalogerUniforms = Cataloger Uniforms

# Epistemics - Chaplain
character-item-group-LoadoutChaplainBackpacks = Chaplain Backpacks
character-item-group-LoadoutChaplainBelt = Chaplain Belt
character-item-group-LoadoutChaplainEars = Chaplain Ears
character-item-group-LoadoutChaplainEquipment = Chaplain Equipment
character-item-group-LoadoutChaplainEyes = Chaplain Eyewear
character-item-group-LoadoutChaplainGloves = Chaplain Gloves
character-item-group-LoadoutChaplainHead = Chaplain Headgear
character-item-group-LoadoutChaplainId = Chaplain Id
character-item-group-LoadoutChaplainNeck = Chaplain Neckwear
character-item-group-LoadoutChaplainMask = Chaplain Masks
character-item-group-LoadoutChaplainOuter = Chaplain Outerwear
character-item-group-LoadoutChaplainShoes = Chaplain Shoes
character-item-group-LoadoutChaplainUniforms = Chaplain Uniforms

# Epistemics - Golemancer
character-item-group-LoadoutGolemancerBackpacks = Golemancer Backpacks
character-item-group-LoadoutGolemancerBelt = Golemancer Belt
character-item-group-LoadoutGolemancerEars = Golemancer Ears
character-item-group-LoadoutGolemancerEquipment = Golemancer Equipment
character-item-group-LoadoutGolemancerEyes = Golemancer Eyewear
character-item-group-LoadoutGolemancerGloves = Golemancer Gloves
character-item-group-LoadoutGolemancerHead = Golemancer Headgear
character-item-group-LoadoutGolemancerId = Golemancer Id
character-item-group-LoadoutGolemancerNeck = Golemancer Neckwear
character-item-group-LoadoutGolemancerMask = Golemancer Masks
character-item-group-LoadoutGolemancerOuter = Golemancer Outerwear
character-item-group-LoadoutGolemancerShoes = Golemancer Shoes
character-item-group-LoadoutGolemancerUniforms = Golemancer Uniforms

# Epistemics - Mystagogue
character-item-group-LoadoutMystagogueBackpacks = Mystagogue Backpacks
character-item-group-LoadoutMystagogueBelt = Mystagogue Belt
character-item-group-LoadoutMystagogueEars = Mystagogue Ears
character-item-group-LoadoutMystagogueEquipment = Mystagogue Equipment
character-item-group-LoadoutMystagogueEyes = Mystagogue Eyewear
character-item-group-LoadoutMystagogueGloves = Mystagogue Gloves
character-item-group-LoadoutMystagogueHead = Mystagogue Headgear
character-item-group-LoadoutMystagogueId = Mystagogue Id
character-item-group-LoadoutMystagogueNeck = Mystagogue Neckwear
character-item-group-LoadoutMystagogueMask = Mystagogue Masks
character-item-group-LoadoutMystagogueOuter = Mystagogue Outerwear
character-item-group-LoadoutMystagogueShoes = Mystagogue Shoes
character-item-group-LoadoutMystagogueUniforms = Mystagogue Uniforms

# Epistemics - Mystic
character-item-group-LoadoutMysticBackpacks = Mystic Backpacks
character-item-group-LoadoutMysticBelt = Mystic Belt
character-item-group-LoadoutMysticEars = Mystic Ears
character-item-group-LoadoutMysticEquipment = Mystic Equipment
character-item-group-LoadoutMysticEyes = Mystic Eyewear
character-item-group-LoadoutMysticGloves = Mystic Gloves
character-item-group-LoadoutMysticHead = Mystic Headgear
character-item-group-LoadoutMysticId = Mystic Id
character-item-group-LoadoutMysticNeck = Mystic Neckwear
character-item-group-LoadoutMysticMask = Mystic Masks
character-item-group-LoadoutMysticOuter = Mystic Outerwear
character-item-group-LoadoutMysticShoes = Mystic Shoes
character-item-group-LoadoutMysticUniforms = Mystic Uniforms

# Epistemics - Noviciate
character-item-group-LoadoutNoviciateBackpacks = Noviciate Backpacks
character-item-group-LoadoutNoviciateBelt = Noviciate Belt
character-item-group-LoadoutNoviciateEars = Noviciate Ears
character-item-group-LoadoutNoviciateEquipment = Noviciate Equipment
character-item-group-LoadoutNoviciateEyes = Noviciate Eyewear
character-item-group-LoadoutNoviciateGloves = Noviciate Gloves
character-item-group-LoadoutNoviciateHead = Noviciate Headgear
character-item-group-LoadoutNoviciateId = Noviciate Id
character-item-group-LoadoutNoviciateNeck = Noviciate Neckwear
character-item-group-LoadoutNoviciateMask = Noviciate Masks
character-item-group-LoadoutNoviciateOuter = Noviciate Outerwear
character-item-group-LoadoutNoviciateShoes = Noviciate Shoes
character-item-group-LoadoutNoviciateUniforms = Noviciate Uniforms

# Epistemics - Psionic Mantis
character-item-group-LoadoutPsionicMantisBackpacks = Psionic Mantis Backpacks
character-item-group-LoadoutPsionicMantisBelt = Psionic Mantis Belt
character-item-group-LoadoutPsionicMantisEars = Psionic Mantis Ears
character-item-group-LoadoutPsionicMantisEquipment = Psionic Mantis Equipment
character-item-group-LoadoutPsionicMantisEyes = Psionic Mantis Eyewear
character-item-group-LoadoutPsionicMantisGloves = Psionic Mantis Gloves
character-item-group-LoadoutPsionicMantisHead = Psionic Mantis Headgear
character-item-group-LoadoutPsionicMantisId = Psionic Mantis Id
character-item-group-LoadoutPsionicMantisNeck = Psionic Mantis Neckwear
character-item-group-LoadoutPsionicMantisMask = Psionic Mantis Masks
character-item-group-LoadoutPsionicMantisOuter = Psionic Mantis Outerwear
character-item-group-LoadoutPsionicMantisShoes = Psionic Mantis Shoes
character-item-group-LoadoutPsionicMantisUniforms = Psionic Mantis Uniforms

# Logistics
character-item-group-LoadoutLogisticsBackpacks = Logistics Backpacks
character-item-group-LoadoutLogisticsBelt = Logistics Belt
character-item-group-LoadoutLogisticsEars = Logistics Ears
character-item-group-LoadoutLogisticsEquipment = Logistics Equipment
character-item-group-LoadoutLogisticsEyes = Logistics Eyewear
character-item-group-LoadoutLogisticsGloves = Logistics Gloves
character-item-group-LoadoutLogisticsHead = Logistics Headgear
character-item-group-LoadoutLogisticsId = Logistics Id
character-item-group-LoadoutLogisticsNeck = Logistics Neckwear
character-item-group-LoadoutLogisticsMask = Logistics Masks
character-item-group-LoadoutLogisticsOuter = Logistics Outerwear
character-item-group-LoadoutLogisticsShoes = Logistics Shoes
character-item-group-LoadoutLogisticsUniforms = Logistics Uniforms

# Logistics - Cargo Technician
character-item-group-LoadoutCargoTechnicianBackpacks = Cargo Technician Backpacks
character-item-group-LoadoutCargoTechnicianBelt = Cargo Technician Belt
character-item-group-LoadoutCargoTechnicianEars = Cargo Technician Ears
character-item-group-LoadoutCargoTechnicianEquipment = Cargo Technician Equipment
character-item-group-LoadoutCargoTechnicianEyes = Cargo Technician Eyewear
character-item-group-LoadoutCargoTechnicianGloves = Cargo Technician Gloves
character-item-group-LoadoutCargoTechnicianHead = Cargo Technician Headgear
character-item-group-LoadoutCargoTechnicianId = Cargo Technician Id
character-item-group-LoadoutCargoTechnicianNeck = Cargo Technician Neckwear
character-item-group-LoadoutCargoTechnicianMask = Cargo Technician Masks
character-item-group-LoadoutCargoTechnicianOuter = Cargo Technician Outerwear
character-item-group-LoadoutCargoTechnicianShoes = Cargo Technician Shoes
character-item-group-LoadoutCargoTechnicianUniforms = Cargo Technician Uniforms

# Logistics - Courier
character-item-group-LoadoutCourierBackpacks = Courier Backpacks
character-item-group-LoadoutCourierBelt = Courier Belt
character-item-group-LoadoutCourierEars = Courier Ears
character-item-group-LoadoutCourierEquipment = Courier Equipment
character-item-group-LoadoutCourierEyes = Courier Eyewear
character-item-group-LoadoutCourierGloves = Courier Gloves
character-item-group-LoadoutCourierHead = Courier Headgear
character-item-group-LoadoutCourierId = Courier Id
character-item-group-LoadoutCourierNeck = Courier Neckwear
character-item-group-LoadoutCourierMask = Courier Masks
character-item-group-LoadoutCourierOuter = Courier Outerwear
character-item-group-LoadoutCourierShoes = Courier Shoes
character-item-group-LoadoutCourierUniforms = Courier Uniforms

# Logistics - Logistics Officer
character-item-group-LoadoutLogisticsOfficerBackpacks = Logistics Officer Backpacks
character-item-group-LoadoutLogisticsOfficerBelt = Logistics Officer Belt
character-item-group-LoadoutLogisticsOfficerEars = Logistics Officer Ears
character-item-group-LoadoutLogisticsOfficerEquipment = Logistics Officer Equipment
character-item-group-LoadoutLogisticsOfficerEyes = Logistics Officer Eyewear
character-item-group-LoadoutLogisticsOfficerGloves = Logistics Officer Gloves
character-item-group-LoadoutLogisticsOfficerHead = Logistics Officer Headgear
character-item-group-LoadoutLogisticsOfficerId = Logistics Officer Id
character-item-group-LoadoutLogisticsOfficerNeck = Logistics Officer Neckwear
character-item-group-LoadoutLogisticsOfficerMask = Logistics Officer Masks
character-item-group-LoadoutLogisticsOfficerOuter = Logistics Officer Outerwear
character-item-group-LoadoutLogisticsOfficerShoes = Logistics Officer Shoes
character-item-group-LoadoutLogisticsOfficerUniforms = Logistics Officer Uniforms

# Logistics - Salvage Specialist
character-item-group-LoadoutSalvageSpecialistBackpacks = Salvage Specialist Backpacks
character-item-group-LoadoutSalvageSpecialistBelt = Salvage Specialist Belt
character-item-group-LoadoutSalvageSpecialistEars = Salvage Specialist Ears
character-item-group-LoadoutSalvageSpecialistEquipment = Salvage Specialist Equipment
character-item-group-LoadoutSalvageSpecialistEyes = Salvage Specialist Eyewear
character-item-group-LoadoutSalvageSpecialistGloves = Salvage Specialist Gloves
character-item-group-LoadoutSalvageSpecialistHead = Salvage Specialist Headgear
character-item-group-LoadoutSalvageSpecialistId = Salvage Specialist Id
character-item-group-LoadoutSalvageSpecialistNeck = Salvage Specialist Neckwear
character-item-group-LoadoutSalvageSpecialistMask = Salvage Specialist Masks
character-item-group-LoadoutSalvageSpecialistOuter = Salvage Specialist Outerwear
character-item-group-LoadoutSalvageSpecialistShoes = Salvage Specialist Shoes
character-item-group-LoadoutSalvageSpecialistUniforms = Salvage Specialist Uniforms
character-item-group-LoadoutSalvageSpecialistWeapons = Salvage Specialist Weapons

# Medical
character-item-group-LoadoutMedicalBackpacks = Medical Backpacks
character-item-group-LoadoutMedicalBelt = Medical Belt
character-item-group-LoadoutMedicalEars = Medical Ears
character-item-group-LoadoutMedicalEquipment = Medical Equipment
character-item-group-LoadoutMedicalEyes = Medical Eyewear
character-item-group-LoadoutMedicalGloves = Medical Gloves
character-item-group-LoadoutMedicalHead = Medical Headgear
character-item-group-LoadoutMedicalId = Medical Id
character-item-group-LoadoutMedicalNeck = Medical Neckwear
character-item-group-LoadoutMedicalMask = Medical Masks
character-item-group-LoadoutMedicalOuter = Medical Outerwear
character-item-group-LoadoutMedicalShoes = Medical Shoes
character-item-group-LoadoutMedicalUniforms = Medical Uniforms

# Medical - Chemist
character-item-group-LoadoutChemistBackpacks = Chemist Backpacks
character-item-group-LoadoutChemistBelt = Chemist Belt
character-item-group-LoadoutChemistEars = Chemist Ears
character-item-group-LoadoutChemistEquipment = Chemist Equipment
character-item-group-LoadoutChemistEyes = Chemist Eyewear
character-item-group-LoadoutChemistGloves = Chemist Gloves
character-item-group-LoadoutChemistHead = Chemist Headgear
character-item-group-LoadoutChemistId = Chemist Id
character-item-group-LoadoutChemistNeck = Chemist Neckwear
character-item-group-LoadoutChemistMask = Chemist Masks
character-item-group-LoadoutChemistOuter = Chemist Outerwear
character-item-group-LoadoutChemistShoes = Chemist Shoes
character-item-group-LoadoutChemistUniforms = Chemist Uniforms

# Medical - Chief Medical Officer
character-item-group-LoadoutChiefMedicalOfficerBackpacks = Chief Medical Officer Backpacks
character-item-group-LoadoutChiefMedicalOfficerBelt = Chief Medical Officer Belt
character-item-group-LoadoutChiefMedicalOfficerEars = Chief Medical Officer Ears
character-item-group-LoadoutChiefMedicalOfficerEquipment = Chief Medical Officer Equipment
character-item-group-LoadoutChiefMedicalOfficerEyes = Chief Medical Officer Eyewear
character-item-group-LoadoutChiefMedicalOfficerGloves = Chief Medical Officer Gloves
character-item-group-LoadoutChiefMedicalOfficerHead = Chief Medical Officer Headgear
character-item-group-LoadoutChiefMedicalOfficerId = Chief Medical Officer Id
character-item-group-LoadoutChiefMedicalOfficerNeck = Chief Medical Officer Neckwear
character-item-group-LoadoutChiefMedicalOfficerMask = Chief Medical Officer Masks
character-item-group-LoadoutChiefMedicalOfficerOuter = Chief Medical Officer Outerwear
character-item-group-LoadoutChiefMedicalOfficerShoes = Chief Medical Officer Shoes
character-item-group-LoadoutChiefMedicalOfficerUniforms = Chief Medical Officer Uniforms

# Medical - Medical Doctor
character-item-group-LoadoutMedicalDoctorBackpacks = Medical Doctor Backpacks
character-item-group-LoadoutMedicalDoctorBelt = Medical Doctor Belt
character-item-group-LoadoutMedicalDoctorEars = Medical Doctor Ears
character-item-group-LoadoutMedicalDoctorEquipment = Medical Doctor Equipment
character-item-group-LoadoutMedicalDoctorEyes = Medical Doctor Eyewear
character-item-group-LoadoutMedicalDoctorGloves = Medical Doctor Gloves
character-item-group-LoadoutMedicalDoctorHead = Medical Doctor Headgear
character-item-group-LoadoutMedicalDoctorId = Medical Doctor Id
character-item-group-LoadoutMedicalDoctorNeck = Medical Doctor Neckwear
character-item-group-LoadoutMedicalDoctorMask = Medical Doctor Masks
character-item-group-LoadoutMedicalDoctorOuter = Medical Doctor Outerwear
character-item-group-LoadoutMedicalDoctorShoes = Medical Doctor Shoes
character-item-group-LoadoutMedicalDoctorUniforms = Medical Doctor Uniforms

# Medical - Medical Intern
character-item-group-LoadoutMedicalInternBackpacks = Medical Intern Backpacks
character-item-group-LoadoutMedicalInternBelt = Medical Intern Belt
character-item-group-LoadoutMedicalInternEars = Medical Intern Ears
character-item-group-LoadoutMedicalInternEquipment = Medical Intern Equipment
character-item-group-LoadoutMedicalInternEyes = Medical Intern Eyewear
character-item-group-LoadoutMedicalInternGloves = Medical Intern Gloves
character-item-group-LoadoutMedicalInternHead = Medical Intern Headgear
character-item-group-LoadoutMedicalInternId = Medical Intern Id
character-item-group-LoadoutMedicalInternNeck = Medical Intern Neckwear
character-item-group-LoadoutMedicalInternMask = Medical Intern Masks
character-item-group-LoadoutMedicalInternOuter = Medical Intern Outerwear
character-item-group-LoadoutMedicalInternShoes = Medical Intern Shoes
character-item-group-LoadoutMedicalInternUniforms = Medical Intern Uniforms

# Medical - Paramedic
character-item-group-LoadoutParamedicBackpacks = Paramedic Backpacks
character-item-group-LoadoutParamedicBelt = Paramedic Belt
character-item-group-LoadoutParamedicEars = Paramedic Ears
character-item-group-LoadoutParamedicEquipment = Paramedic Equipment
character-item-group-LoadoutParamedicEyes = Paramedic Eyewear
character-item-group-LoadoutParamedicGloves = Paramedic Gloves
character-item-group-LoadoutParamedicHead = Paramedic Headgear
character-item-group-LoadoutParamedicId = Paramedic Id
character-item-group-LoadoutParamedicNeck = Paramedic Neckwear
character-item-group-LoadoutParamedicMask = Paramedic Masks
character-item-group-LoadoutParamedicOuter = Paramedic Outerwear
character-item-group-LoadoutParamedicShoes = Paramedic Shoes
character-item-group-LoadoutParamedicUniforms = Paramedic Uniforms

# Medical - Psychologist
character-item-group-LoadoutPsychologistBackpacks = Psychologist Backpacks
character-item-group-LoadoutPsychologistBelt = Psychologist Belt
character-item-group-LoadoutPsychologistEars = Psychologist Ears
character-item-group-LoadoutPsychologistEquipment = Psychologist Equipment
character-item-group-LoadoutPsychologistEyes = Psychologist Eyewear
character-item-group-LoadoutPsychologistGloves = Psychologist Gloves
character-item-group-LoadoutPsychologistHead = Psychologist Headgear
character-item-group-LoadoutPsychologistId = Psychologist Id
character-item-group-LoadoutPsychologistNeck = Psychologist Neckwear
character-item-group-LoadoutPsychologistMask = Psychologist Masks
character-item-group-LoadoutPsychologistOuter = Psychologist Outerwear
character-item-group-LoadoutPsychologistShoes = Psychologist Shoes
character-item-group-LoadoutPsychologistUniforms = Psychologist Uniforms

# Medical - Senior Physician
character-item-group-LoadoutSeniorPhysicianBackpacks = Senior Physician Backpacks
character-item-group-LoadoutSeniorPhysicianBelt = Senior Physician Belt
character-item-group-LoadoutSeniorPhysicianEars = Senior Physician Ears
character-item-group-LoadoutSeniorPhysicianEquipment = Senior Physician Equipment
character-item-group-LoadoutSeniorPhysicianEyes = Senior Physician Eyewear
character-item-group-LoadoutSeniorPhysicianGloves = Senior Physician Gloves
character-item-group-LoadoutSeniorPhysicianHead = Senior Physician Headgear
character-item-group-LoadoutSeniorPhysicianId = Senior Physician Id
character-item-group-LoadoutSeniorPhysicianNeck = Senior Physician Neckwear
character-item-group-LoadoutSeniorPhysicianMask = Senior Physician Masks
character-item-group-LoadoutSeniorPhysicianOuter = Senior Physician Outerwear
character-item-group-LoadoutSeniorPhysicianShoes = Senior Physician Shoes
character-item-group-LoadoutSeniorPhysicianUniforms = Senior Physician Uniforms

# Security
character-item-group-LoadoutSecurityBackpacks = Security Backpacks
character-item-group-LoadoutSecurityBelt = Security Belt
character-item-group-LoadoutSecurityEars = Security Ears
character-item-group-LoadoutSecurityEquipment = Security Equipment
character-item-group-LoadoutSecurityWeapons = Security Duty Weapon
character-item-group-LoadoutSecurityEyes = Security Eyewear
character-item-group-LoadoutSecurityGloves = Security Gloves
character-item-group-LoadoutSecurityHead = Security Headgear
character-item-group-LoadoutSecurityId = Security Id
character-item-group-LoadoutSecurityNeck = Security Neckwear
character-item-group-LoadoutSecurityMask = Security Masks
character-item-group-LoadoutSecurityOuter = Security Outerwear
character-item-group-LoadoutSecurityShoes = Security Shoes
character-item-group-LoadoutSecurityUniforms = Security Uniforms

# Security - Cadet
character-item-group-LoadoutCadetBackpacks = Cadet Backpacks
character-item-group-LoadoutCadetBelt = Cadet Belt
character-item-group-LoadoutCadetEars = Cadet Ears
character-item-group-LoadoutCadetEquipment = Cadet Equipment
character-item-group-LoadoutCadetEyes = Cadet Eyewear
character-item-group-LoadoutCadetGloves = Cadet Gloves
character-item-group-LoadoutCadetHead = Cadet Headgear
character-item-group-LoadoutCadetId = Cadet Id
character-item-group-LoadoutCadetNeck = Cadet Neckwear
character-item-group-LoadoutCadetMask = Cadet Masks
character-item-group-LoadoutCadetOuter = Cadet Outerwear
character-item-group-LoadoutCadetShoes = Cadet Shoes
character-item-group-LoadoutCadetUniforms = Cadet Uniforms

# Security - Corpsman
character-item-group-LoadoutCorpsmanBackpacks = Corpsman Backpacks
character-item-group-LoadoutCorpsmanBelt = Corpsman Belt
character-item-group-LoadoutCorpsmanEars = Corpsman Ears
character-item-group-LoadoutCorpsmanEquipment = Corpsman Equipment
character-item-group-LoadoutCorpsmanEyes = Corpsman Eyewear
character-item-group-LoadoutCorpsmanGloves = Corpsman Gloves
character-item-group-LoadoutCorpsmanHead = Corpsman Headgear
character-item-group-LoadoutCorpsmanId = Corpsman Id
character-item-group-LoadoutCorpsmanNeck = Corpsman Neckwear
character-item-group-LoadoutCorpsmanMask = Corpsman Masks
character-item-group-LoadoutCorpsmanOuter = Corpsman Outerwear
character-item-group-LoadoutCorpsmanShoes = Corpsman Shoes
character-item-group-LoadoutCorpsmanUniforms = Corpsman Uniforms

# Security - Detective
character-item-group-LoadoutDetectiveBackpacks = Detective Backpacks
character-item-group-LoadoutDetectiveBelt = Detective Belt
character-item-group-LoadoutDetectiveEars = Detective Ears
character-item-group-LoadoutDetectiveEquipment = Detective Equipment
character-item-group-LoadoutDetectiveEyes = Detective Eyewear
character-item-group-LoadoutDetectiveGloves = Detective Gloves
character-item-group-LoadoutDetectiveHead = Detective Headgear
character-item-group-LoadoutDetectiveId = Detective Id
character-item-group-LoadoutDetectiveNeck = Detective Neckwear
character-item-group-LoadoutDetectiveMask = Detective Masks
character-item-group-LoadoutDetectiveOuter = Detective Outerwear
character-item-group-LoadoutDetectiveShoes = Detective Shoes
character-item-group-LoadoutDetectiveUniforms = Detective Uniforms

# Security - Head Of Security
character-item-group-LoadoutHeadOfSecurityBackpacks = Head Of Security Backpacks
character-item-group-LoadoutHeadOfSecurityBelt = Head Of Security Belt
character-item-group-LoadoutHeadOfSecurityEars = Head Of Security Ears
character-item-group-LoadoutHeadOfSecurityEquipment = Head Of Security Equipment
character-item-group-LoadoutHeadOfSecurityWeapons = Head of Security's Antique Weapon Collection
character-item-group-LoadoutHeadOfSecurityEyes = Head Of Security Eyewear
character-item-group-LoadoutHeadOfSecurityGloves = Head Of Security Gloves
character-item-group-LoadoutHeadOfSecurityHead = Head Of Security Headgear
character-item-group-LoadoutHeadOfSecurityId = Head Of Security Id
character-item-group-LoadoutHeadOfSecurityNeck = Head Of Security Neckwear
character-item-group-LoadoutHeadOfSecurityMask = Head Of Security Masks
character-item-group-LoadoutHeadOfSecurityOuter = Head Of Security Outerwear
character-item-group-LoadoutHeadOfSecurityShoes = Head Of Security Shoes
character-item-group-LoadoutHeadOfSecurityUniforms = Head Of Security Uniforms
character-item-group-LoadoutHeadOfSecurityTrinkets = Head Of Security Trinkets

# Security - Security Officer
character-item-group-LoadoutSecurityOfficerBackpacks = Security Officer Backpacks
character-item-group-LoadoutSecurityOfficerBelt = Security Officer Belt
character-item-group-LoadoutSecurityOfficerEars = Security Officer Ears
character-item-group-LoadoutSecurityOfficerEquipment = Security Officer Equipment
character-item-group-LoadoutSecurityOfficerEyes = Security Officer Eyewear
character-item-group-LoadoutSecurityOfficerGloves = Security Officer Gloves
character-item-group-LoadoutSecurityOfficerHead = Security Officer Headgear
character-item-group-LoadoutSecurityOfficerId = Security Officer Id
character-item-group-LoadoutSecurityOfficerNeck = Security Officer Neckwear
character-item-group-LoadoutSecurityOfficerMask = Security Officer Masks
character-item-group-LoadoutSecurityOfficerOuter = Security Officer Outerwear
character-item-group-LoadoutSecurityOfficerShoes = Security Officer Shoes
character-item-group-LoadoutSecurityOfficerUniforms = Security Officer Uniforms

# Security - Senior Officer
character-item-group-LoadoutSeniorOfficerBackpacks = Senior Officer Backpacks
character-item-group-LoadoutSeniorOfficerBelt = Senior Officer Belt
character-item-group-LoadoutSeniorOfficerEars = Senior Officer Ears
character-item-group-LoadoutSeniorOfficerEquipment = Senior Officer Equipment
character-item-group-LoadoutSeniorOfficerEyes = Senior Officer Eyewear
character-item-group-LoadoutSeniorOfficerGloves = Senior Officer Gloves
character-item-group-LoadoutSeniorOfficerHead = Senior Officer Headgear
character-item-group-LoadoutSeniorOfficerId = Senior Officer Id
character-item-group-LoadoutSeniorOfficerNeck = Senior Officer Neckwear
character-item-group-LoadoutSeniorOfficerMask = Senior Officer Masks
character-item-group-LoadoutSeniorOfficerOuter = Senior Officer Outerwear
character-item-group-LoadoutSeniorOfficerShoes = Senior Officer Shoes
character-item-group-LoadoutSeniorOfficerUniforms = Senior Officer Uniforms

# Security - Warden
character-item-group-LoadoutWardenBackpacks = Warden Backpacks
character-item-group-LoadoutWardenBelt = Warden Belt
character-item-group-LoadoutWardenEars = Warden Ears
character-item-group-LoadoutWardenEquipment = Warden Equipment
character-item-group-LoadoutWardenEyes = Warden Eyewear
character-item-group-LoadoutWardenGloves = Warden Gloves
character-item-group-LoadoutWardenHead = Warden Headgear
character-item-group-LoadoutWardenId = Warden Id
character-item-group-LoadoutWardenNeck = Warden Neckwear
character-item-group-LoadoutWardenMask = Warden Masks
character-item-group-LoadoutWardenOuter = Warden Outerwear
character-item-group-LoadoutWardenShoes = Warden Shoes
character-item-group-LoadoutWardenUniforms = Warden Uniforms

# Service
character-item-group-LoadoutServiceBackpacks = Service Backpacks
character-item-group-LoadoutServiceBelt = Service Belt
character-item-group-LoadoutServiceEars = Service Ears
character-item-group-LoadoutServiceEquipment = Service Equipment
character-item-group-LoadoutServiceEyes = Service Eyewear
character-item-group-LoadoutServiceGloves = Service Gloves
character-item-group-LoadoutServiceHead = Service Headgear
character-item-group-LoadoutServiceId = Service Id
character-item-group-LoadoutServiceNeck = Service Neckwear
character-item-group-LoadoutServiceMask = Service Masks
character-item-group-LoadoutServiceOuter = Service Outerwear
character-item-group-LoadoutServiceShoes = Service Shoes
character-item-group-LoadoutServiceUniforms = Service Uniforms

# Service - Bartender
character-item-group-LoadoutBartenderBackpacks = Bartender Backpacks
character-item-group-LoadoutBartenderBelt = Bartender Belt
character-item-group-LoadoutBartenderEars = Bartender Ears
character-item-group-LoadoutBartenderEquipment = Bartender Equipment
character-item-group-LoadoutBartenderAmmo = Bartender Ammo
character-item-group-LoadoutBartenderWeapon = Bartender Weapon
character-item-group-LoadoutBartenderEyes = Bartender Eyewear
character-item-group-LoadoutBartenderGloves = Bartender Gloves
character-item-group-LoadoutBartenderHead = Bartender Headgear
character-item-group-LoadoutBartenderId = Bartender Id
character-item-group-LoadoutBartenderNeck = Bartender Neckwear
character-item-group-LoadoutBartenderMask = Bartender Masks
character-item-group-LoadoutBartenderOuter = Bartender Outerwear
character-item-group-LoadoutBartenderShoes = Bartender Shoes
character-item-group-LoadoutBartenderUniforms = Bartender Uniforms

# Service - Botanist
character-item-group-LoadoutBotanistBackpacks = Botanist Backpacks
character-item-group-LoadoutBotanistBelt = Botanist Belt
character-item-group-LoadoutBotanistEars = Botanist Ears
character-item-group-LoadoutBotanistEquipment = Botanist Equipment
character-item-group-LoadoutBotanistEyes = Botanist Eyewear
character-item-group-LoadoutBotanistGloves = Botanist Gloves
character-item-group-LoadoutBotanistHead = Botanist Headgear
character-item-group-LoadoutBotanistId = Botanist Id
character-item-group-LoadoutBotanistNeck = Botanist Neckwear
character-item-group-LoadoutBotanistMask = Botanist Masks
character-item-group-LoadoutBotanistOuter = Botanist Outerwear
character-item-group-LoadoutBotanistShoes = Botanist Shoes
character-item-group-LoadoutBotanistUniforms = Botanist Uniforms

# Service - Chef
character-item-group-LoadoutChefBackpacks = Chef Backpacks
character-item-group-LoadoutChefBelt = Chef Belt
character-item-group-LoadoutChefEars = Chef Ears
character-item-group-LoadoutChefEquipment = Chef Equipment
character-item-group-LoadoutChefEyes = Chef Eyewear
character-item-group-LoadoutChefGloves = Chef Gloves
character-item-group-LoadoutChefHead = Chef Headgear
character-item-group-LoadoutChefId = Chef Id
character-item-group-LoadoutChefNeck = Chef Neckwear
character-item-group-LoadoutChefMask = Chef Masks
character-item-group-LoadoutChefOuter = Chef Outerwear
character-item-group-LoadoutChefShoes = Chef Shoes
character-item-group-LoadoutChefUniforms = Chef Uniforms

# Service - Clown
character-item-group-LoadoutClownBackpacks = Clown Backpacks
character-item-group-LoadoutClownBelt = Clown Belt
character-item-group-LoadoutClownEars = Clown Ears
character-item-group-LoadoutClownEquipment = Clown Equipment
character-item-group-LoadoutClownEyes = Clown Eyewear
character-item-group-LoadoutClownGloves = Clown Gloves
character-item-group-LoadoutClownHead = Clown Headgear
character-item-group-LoadoutClownId = Clown Id
character-item-group-LoadoutClownNeck = Clown Neckwear
character-item-group-LoadoutClownMask = Clown Masks
character-item-group-LoadoutClownOuter = Clown Outerwear
character-item-group-LoadoutClownShoes = Clown Shoes
character-item-group-LoadoutClownUniforms = Clown Uniforms

# Service - Janitor
character-item-group-LoadoutJanitorBackpacks = Janitor Backpacks
character-item-group-LoadoutJanitorBelt = Janitor Belt
character-item-group-LoadoutJanitorEars = Janitor Ears
character-item-group-LoadoutJanitorEquipment = Janitor Equipment
character-item-group-LoadoutJanitorEyes = Janitor Eyewear
character-item-group-LoadoutJanitorGloves = Janitor Gloves
character-item-group-LoadoutJanitorHead = Janitor Headgear
character-item-group-LoadoutJanitorId = Janitor Id
character-item-group-LoadoutJanitorNeck = Janitor Neckwear
character-item-group-LoadoutJanitorMask = Janitor Masks
character-item-group-LoadoutJanitorOuter = Janitor Outerwear
character-item-group-LoadoutJanitorShoes = Janitor Shoes
character-item-group-LoadoutJanitorUniforms = Janitor Uniforms

# Service - Lawyer
character-item-group-LoadoutLawyerBackpacks = Lawyer Backpacks
character-item-group-LoadoutLawyerBelt = Lawyer Belt
character-item-group-LoadoutLawyerEars = Lawyer Ears
character-item-group-LoadoutLawyerEquipment = Lawyer Equipment
character-item-group-LoadoutLawyerEyes = Lawyer Eyewear
character-item-group-LoadoutLawyerGloves = Lawyer Gloves
character-item-group-LoadoutLawyerHead = Lawyer Headgear
character-item-group-LoadoutLawyerId = Lawyer Id
character-item-group-LoadoutLawyerNeck = Lawyer Neckwear
character-item-group-LoadoutLawyerMask = Lawyer Masks
character-item-group-LoadoutLawyerOuter = Lawyer Outerwear
character-item-group-LoadoutLawyerShoes = Lawyer Shoes
character-item-group-LoadoutLawyerUniforms = Lawyer Uniforms

# Service - Mime
character-item-group-LoadoutMimeBackpacks = Mime Backpacks
character-item-group-LoadoutMimeBelt = Mime Belt
character-item-group-LoadoutMimeEars = Mime Ears
character-item-group-LoadoutMimeEquipment = Mime Equipment
character-item-group-LoadoutMimeEyes = Mime Eyewear
character-item-group-LoadoutMimeGloves = Mime Gloves
character-item-group-LoadoutMimeHead = Mime Headgear
character-item-group-LoadoutMimeId = Mime Id
character-item-group-LoadoutMimeNeck = Mime Neckwear
character-item-group-LoadoutMimeMask = Mime Masks
character-item-group-LoadoutMimeOuter = Mime Outerwear
character-item-group-LoadoutMimeShoes = Mime Shoes
character-item-group-LoadoutMimeUniforms = Mime Uniforms

# Service - Musician
character-item-group-LoadoutMusicianBackpacks = Musician Backpacks
character-item-group-LoadoutMusicianBelt = Musician Belt
character-item-group-LoadoutMusicianEars = Musician Ears
character-item-group-LoadoutMusicianEquipment = Musician Equipment
character-item-group-LoadoutMusicianEyes = Musician Eyewear
character-item-group-LoadoutMusicianGloves = Musician Gloves
character-item-group-LoadoutMusicianHead = Musician Headgear
character-item-group-LoadoutMusicianId = Musician Id
character-item-group-LoadoutMusicianNeck = Musician Neckwear
character-item-group-LoadoutMusicianMask = Musician Masks
character-item-group-LoadoutMusicianOuter = Musician Outerwear
character-item-group-LoadoutMusicianShoes = Musician Shoes
character-item-group-LoadoutMusicianUniforms = Musician Uniforms

# Service - Reporter
character-item-group-LoadoutReporterBackpacks = Reporter Backpacks
character-item-group-LoadoutReporterBelt = Reporter Belt
character-item-group-LoadoutReporterEars = Reporter Ears
character-item-group-LoadoutReporterEquipment = Reporter Equipment
character-item-group-LoadoutReporterEyes = Reporter Eyewear
character-item-group-LoadoutReporterGloves = Reporter Gloves
character-item-group-LoadoutReporterHead = Reporter Headgear
character-item-group-LoadoutReporterId = Reporter Id
character-item-group-LoadoutReporterNeck = Reporter Neckwear
character-item-group-LoadoutReporterMask = Reporter Masks
character-item-group-LoadoutReporterOuter = Reporter Outerwear
character-item-group-LoadoutReporterShoes = Reporter Shoes
character-item-group-LoadoutReporterUniforms = Reporter Uniforms

# Misc - Prisoner
character-item-group-LoadoutPrisonerBackpacks = Prisoner Backpacks
character-item-group-LoadoutPrisonerBelt = Prisoner Belt
character-item-group-LoadoutPrisonerEars = Prisoner Ears
character-item-group-LoadoutPrisonerEquipment = Prisoner Equipment
character-item-group-LoadoutPrisonerEyes = Prisoner Eyewear
character-item-group-LoadoutPrisonerloves = Prisoner Gloves
character-item-group-LoadoutPrisonerHead = Prisoner Headgear
character-item-group-LoadoutPrisonerId = Prisoner Id
character-item-group-LoadoutPrisonerNeck = Prisoner Neckwear
character-item-group-LoadoutPrisonerMask = Prisoner Masks
character-item-group-LoadoutPrisonerOuter = Prisoner Outerwear
character-item-group-LoadoutPrisonerShoes = Prisoner Shoes
character-item-group-LoadoutPrisonerUniforms = Prisoner Uniforms

# Traits - Languages
character-item-group-TraitsLanguagesBasic = Basic Languages
character-item-group-TraitsAccents = Accents
