using Content.Client.Stylesheets;
using Content.Client.UserInterface.Controls;
using Content.Shared.Chemistry;
using Content.Shared.Chemistry.Reagent;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Client.Utility;
using Robust.Shared.Prototypes;
using Robust.Shared.Utility;
using System.Linq;
using System.Numerics;
using Content.Shared.FixedPoint;
using Robust.Client.Graphics;
using static Robust.Client.UserInterface.Controls.BoxContainer;

namespace Content.Client.Chemistry.UI
{
    /// <summary>
    /// Client-side UI used to control a <see cref="SharedChemMasterComponent"/>
    /// </summary>
    [GenerateTypedNameReferences]
    public sealed partial class ChemMasterWindow : FancyWindow
    {
        [Dependency] private readonly IPrototypeManager _prototypeManager = default!;
        public event Action<BaseButton.ButtonEventArgs, ReagentButton, int, bool>? OnReagentButtonPressed;
        public event Action<int>? OnAmountButtonPressed;
        public event Action<int>? OnSortMethodChanged;
        public event Action<int>? OnTransferAmountChanged;
        public event Action<List<int>>? OnUpdateAmounts;

        public readonly Button[] PillTypeButtons;

        private List<int> _amounts = new();

        private const string TransferringAmountColor = "#ffffff";
        private ReagentSortMethod _currentSortMethod = ReagentSortMethod.Alphabetical;
        private ChemMasterBoundUserInterfaceState? _lastState;
        private int _transferAmount = 50;

        private const string PillsRsiPath = "/Textures/Objects/Specific/Chemistry/pills.rsi";

        /// <summary>
        /// Create and initialize the chem master UI client-side. Creates the basic layout,
        /// actual data isn't filled in until the server sends data about the chem master.
        /// </summary>
        public ChemMasterWindow()
        {
            RobustXamlLoader.Load(this);
            IoCManager.InjectDependencies(this);

            AmountLabel.HorizontalAlignment = HAlignment.Center;
            AmountLineEdit.OnTextEntered += SetAmount;

            SetAmountButton.OnPressed += _ => SetAmountText(AmountLineEdit.Text);
            SaveAsFrequentButton.OnPressed += HandleSaveAsFrequentPressed;

            // Pill type selection buttons, in total there are 20 pills.
            // Pill rsi file should have states named as pill1, pill2, and so on.
            var resourcePath = new ResPath(PillsRsiPath);
            var pillTypeGroup = new ButtonGroup();
            PillTypeButtons = new Button[20];
            for (uint i = 0; i < PillTypeButtons.Length; i++)
            {
                // For every button decide which stylebase to have
                // Every row has 10 buttons
                String styleBase = StyleBase.ButtonOpenBoth;
                uint modulo = i % 10;
                if (i > 0 && modulo == 0)
                    styleBase = StyleBase.ButtonOpenRight;
                else if (i > 0 && modulo == 9)
                    styleBase = StyleBase.ButtonOpenLeft;
                else if (i == 0)
                    styleBase = StyleBase.ButtonOpenRight;

                // Generate buttons
                PillTypeButtons[i] = new Button
                {
                    Access = AccessLevel.Public,
                    StyleClasses = { styleBase },
                    MaxSize = new Vector2(42, 28),
                    Group = pillTypeGroup
                };

                // Generate buttons textures
                var specifier = new SpriteSpecifier.Rsi(resourcePath, "pill" + (i + 1));
                TextureRect pillTypeTexture = new TextureRect
                {
                    Texture = specifier.Frame0(),
                    TextureScale = new Vector2(1.75f, 1.75f),
                    Stretch = TextureRect.StretchMode.KeepCentered,
                };

                PillTypeButtons[i].AddChild(pillTypeTexture);
                Grid.AddChild(PillTypeButtons[i]);
            }

            PillDosage.InitDefaultButtons();
            PillNumber.InitDefaultButtons();
            BottleDosage.InitDefaultButtons();

            // Ensure label length is within the character limit.
            LabelLineEdit.IsValid = s => s.Length <= SharedChemMaster.LabelMaxLength;

            Tabs.SetTabTitle(0, Loc.GetString("chem-master-window-input-tab"));
            Tabs.SetTabTitle(1, Loc.GetString("chem-master-window-output-tab"));

            SortMethod.AddItem(
                Loc.GetString("chem-master-window-sort-method-Alphabetical-text"),
                (int) ReagentSortMethod.Alphabetical);

            SortMethod.AddItem(
                Loc.GetString("chem-master-window-sort-method-Amount-text"),
                (int) ReagentSortMethod.Amount);

            SortMethod.AddItem(
                Loc.GetString("chem-master-window-sort-method-Time-text"),
                (int) ReagentSortMethod.Time);

            SortMethod.OnItemSelected += HandleChildPressed;

            PillSortMethod.AddItem(
                Loc.GetString(
                    "chem-master-window-sort-method-Alphabetical-text"),
                (int) ReagentSortMethod.Alphabetical);
            PillSortMethod.AddItem(Loc.GetString(
                    "chem-master-window-sort-method-Amount-text"),
                (int) ReagentSortMethod.Amount);
            PillSortMethod.AddItem(
                Loc.GetString("chem-master-window-sort-method-Time-text"),
                (int) ReagentSortMethod.Time);

            PillSortMethod.OnItemSelected += HandleChildPressed;

            BufferTransferButton.OnPressed += HandleDiscardTransferPress;
            BufferDiscardButton.OnPressed += HandleDiscardTransferPress;

            CreateAmountButtons();

            OnAmountButtonPressed += amount => SetAmountText(amount.ToString());
        }

        private void CreateAmountButtons()
        {
            AmountButtons.DisposeAllChildren();

            for (int i = 0; i < _amounts.Count; i++)
            {
                var styleClass = StyleBase.ButtonOpenBoth;
                var amount = _amounts[i];
                var columns = AmountButtons.Columns;

                if (i == 0 || i % columns == 0)
                    styleClass = StyleBase.ButtonOpenRight;

                if ((i + 1) % columns == 0)
                    styleClass = StyleBase.ButtonOpenLeft;

                var button = new Button()
                {
                    Text = amount.ToString(),
                    MinSize = new(10, 10),
                    StyleClasses = { styleClass },
                    HorizontalExpand = true
                };

                button.OnPressed += _ => OnAmountButtonPressed?.Invoke(amount);
                AmountButtons.AddChild(button);
            }
        }

        private void HandleSaveAsFrequentPressed(BaseButton.ButtonEventArgs args)
        {
            if (!int.TryParse(AmountLineEdit.Text, out var amount)
                || _amounts.Any(a => amount == a))
                return;

            _amounts.Add(amount);
            _amounts.Sort();
            CreateAmountButtons();
        }

        private void HandleDiscardTransferPress(BaseButton.ButtonEventArgs args)
        {
            var buttons = BufferInfo.Children
                .Where(c => c is Button)
                .Cast<Button>();

            foreach (var button in buttons)
            {
                var text = BufferTransferButton.Pressed ? "transfer" : "discard";
                button.Text = Loc.GetString($"chem-master-window-{text}-button-text");
            }
        }

        private void HandleSortMethodChange(int newSortMethod)
        {
            if (newSortMethod == (int) _currentSortMethod)
                return;

            _currentSortMethod = (ReagentSortMethod) newSortMethod;
            SortMethod.SelectId(newSortMethod);
            PillSortMethod.SelectId(newSortMethod);

            SortUpdated();
        }

        private void HandleChildPressed(OptionButton.ItemSelectedEventArgs args)
        {
            HandleSortMethodChange(args.Id);
            OnSortMethodChanged?.Invoke(args.Id);
        }

        private void SortUpdated()
        {
            if (_lastState == null)
                return;

            UpdatePanelInfo(_lastState);
        }

        private bool ValidateAmount(string newText, bool invokeEvent = true)
        {
            if (string.IsNullOrWhiteSpace(newText) || !int.TryParse(newText, out int amount))
            {
                AmountLineEdit.SetText(string.Empty);
                return false;
            }

            _transferAmount = amount;

            if (invokeEvent)
                OnTransferAmountChanged?.Invoke(amount);

            return true;
        }

        private void SetAmount(LineEdit.LineEditEventArgs args) =>
            SetAmountText(args.Text);

        private void SetAmountText(string newText, bool invokeEvent = true)
        {
            if (newText == _transferAmount.ToString() || !ValidateAmount(newText, invokeEvent))
                return;

            var localizedAmount = Loc.GetString(
                "chem-master-window-transferring-label",
                ("quantity", newText),
                ("color", TransferringAmountColor));

            AmountLabel.Text = localizedAmount;
            AmountLineEdit.SetText(string.Empty);
        }

        private ReagentButton MakeReagentButton(string text, ReagentId id, bool isBuffer)
        {
            var reagentTransferButton = new ReagentButton(text, id, isBuffer);
            reagentTransferButton.OnPressed += args
                => OnReagentButtonPressed?.Invoke(args, reagentTransferButton, _transferAmount, Tabs.CurrentTab == 1);
            return reagentTransferButton;
        }
        /// <summary>
        /// Conditionally generates a set of reagent buttons based on the supplied boolean argument.
        /// This was moved outside of BuildReagentRow to facilitate conditional logic, stops indentation depth getting out of hand as well.
        /// </summary>
        private ReagentButton? CreateReagentTransferButton(ReagentId reagent, bool isBuffer, bool addReagentButtons)
        {
            if (!addReagentButtons)
                return null; // Return an empty list if reagentTransferButton creation is disabled.

            var text = BufferTransferButton.Pressed ? "transfer" : "discard";

            var reagentTransferButton = MakeReagentButton(
                Loc.GetString($"chem-master-window-{text}-button"),
                reagent,
                isBuffer
            );

            return reagentTransferButton;
        }

        /// <summary>
        /// Update the UI state when new state data is received from the server.
        /// </summary>
        /// <param name="state">State data sent by the server.</param>
        public void UpdateState(BoundUserInterfaceState state)
        {
            var castState = (ChemMasterBoundUserInterfaceState)state;

            if (castState.UpdateLabel)
                LabelLine = GenerateLabel(castState);

            _lastState = castState;

            // Ensure the Panel Info is updated, including UI elements for Buffer Volume, Output Container and so on
            UpdatePanelInfo(castState);
            HandleSortMethodChange(castState.SortMethod);
            SetAmountText(castState.TransferringAmount.ToString(), false);

            if (_amounts != castState.Amounts)
            {
                _amounts = castState.Amounts;
                _amounts.Sort();
                CreateAmountButtons();
            }

            BufferCurrentVolume.Text = $" {castState.PillBufferCurrentVolume?.Int() ?? 0}u";

            InputEjectButton.Disabled = castState.ContainerInfo is null;
            CreateBottleButton.Disabled = castState.PillBufferReagents.Count == 0;
            CreatePillButton.Disabled = castState.PillBufferReagents.Count == 0;

            UpdateDosageFields(castState);
        }

        private FixedPoint2 CurrentStateBufferVolume(ChemMasterBoundUserInterfaceState state) =>
            (Tabs.CurrentTab == 0 ? state.BufferCurrentVolume : state.PillBufferCurrentVolume) ?? 0;

        //assign default values for pill and bottle fields.
        private void UpdateDosageFields(ChemMasterBoundUserInterfaceState castState)
        {
            var bufferVolume = castState.PillBufferCurrentVolume?.Int() ?? 0;
            PillDosage.Value = (int) Math.Min(bufferVolume, castState.PillDosageLimit);

            PillTypeButtons[castState.SelectedPillType].Pressed = true;
            PillNumber.IsValid = x => x >= 0;
            PillDosage.IsValid = x => x > 0 && x <= castState.PillDosageLimit;
            BottleDosage.IsValid = x => x >= 0;

            // Avoid division by zero
            if (PillDosage.Value > 0)
                PillNumber.Value = bufferVolume / PillDosage.Value;
            else
                PillNumber.Value = 0;

            BottleDosage.Value = bufferVolume;
        }
        /// <summary>
        /// Generate a product label based on reagents in the buffer.
        /// </summary>
        /// <param name="state">State data sent by the server.</param>
        private string GenerateLabel(ChemMasterBoundUserInterfaceState state)
        {
            if (CurrentStateBufferVolume(state) == 0)
                return "";

            var buffer = Tabs.CurrentTab == 0 ? state.BufferReagents : state.PillBufferReagents;
            var reagent = buffer.OrderBy(r => r.Quantity).First().Reagent;

            _prototypeManager.TryIndex(reagent.Prototype, out ReagentPrototype? proto);
            return proto?.LocalizedName ?? "";
        }

        /// <summary>
        /// Update the container, buffer, and packaging panels.
        /// </summary>
        /// <param name="state">State data for the dispenser.</param>
        private void UpdatePanelInfo(ChemMasterBoundUserInterfaceState state)
        {
            BufferTransferButton.Pressed = state.Mode == ChemMasterMode.Transfer;
            BufferDiscardButton.Pressed = state.Mode == ChemMasterMode.Discard;

            PillBufferTransferButton.Pressed = state.Mode == ChemMasterMode.Transfer;
            PillBufferDiscardButton.Pressed = state.Mode == ChemMasterMode.Discard;

            BuildContainerUI(ContainerInfoContainer, state.ContainerInfo, true);
            BuildBufferInfo(state);
            BuildPillBufferInfo(state);
        }

        private void BuildBufferInfo(ChemMasterBoundUserInterfaceState state)
        {
            BufferInfo.Children.Clear();

            if (!state.BufferReagents.Any())
            {
                BufferInfo.Children.Add(new Label { Text = Loc.GetString("chem-master-window-buffer-empty-text") });
                return;
            }

            var bufferHBox = new BoxContainer
            {
                Orientation = LayoutOrientation.Horizontal
            };

            BufferInfo.AddChild(bufferHBox);

            var bufferLabel = new Label { Text = $"{Loc.GetString("chem-master-window-buffer-label")} " };
            bufferHBox.AddChild(bufferLabel);
            var bufferVol = new Label
            {
                Text = $"{state.BufferCurrentVolume}u",
                StyleClasses = { StyleNano.StyleClassLabelSecondaryColor }
            };
            bufferHBox.AddChild(bufferVol);

            var bufferReagents = state.BufferReagents.OrderBy(x => x.Reagent.Prototype);

            if (_currentSortMethod == ReagentSortMethod.Amount)
                bufferReagents = bufferReagents.OrderByDescending(x => x.Quantity);

            HandleBuffer(_currentSortMethod == ReagentSortMethod.Time ? state.BufferReagents : bufferReagents, false);
        }

        private void BuildPillBufferInfo(ChemMasterBoundUserInterfaceState state)
        {
            PillBufferInfo.Children.Clear();

            if (!state.PillBufferReagents.Any())
            {
                PillBufferInfo.Children.Add(new Label { Text = Loc.GetString("chem-master-window-buffer-empty-text") });
                return;
            }

            var bufferHBox = new BoxContainer
            {
                Orientation = LayoutOrientation.Horizontal
            };
            PillBufferInfo.AddChild(bufferHBox);

            var bufferLabel = new Label { Text = $"{Loc.GetString("chem-master-window-buffer-label")} " };
            bufferHBox.AddChild(bufferLabel);
            var bufferVol = new Label
            {
                Text = $"{state.PillBufferCurrentVolume}u",
                StyleClasses = { StyleNano.StyleClassLabelSecondaryColor }
            };
            bufferHBox.AddChild(bufferVol);

            var bufferReagents = state.PillBufferReagents.OrderBy(x => x.Reagent.Prototype);

            if (_currentSortMethod == ReagentSortMethod.Amount)
                bufferReagents = bufferReagents.OrderByDescending(x => x.Quantity);

            HandleBuffer(_currentSortMethod == ReagentSortMethod.Time ? state.PillBufferReagents : bufferReagents, true);
        }

        private void HandleBuffer(IEnumerable<ReagentQuantity> reagents, bool pillBuffer)
        {
            var rowCount = 0;
            foreach (var (reagentId, quantity) in reagents)
            {
                _prototypeManager.TryIndex(reagentId.Prototype, out ReagentPrototype? proto);

                var name = proto?.LocalizedName ?? Loc.GetString("chem-master-window-unknown-reagent-text");
                var reagentColor = proto?.SubstanceColor ?? default(Color);

                if (pillBuffer)
                    PillBufferInfo.Children.Add(
                        BuildReagentRow(reagentColor, rowCount++, name, reagentId, quantity, true, true));
                else
                    BufferInfo.Children.Add(
                        BuildReagentRow(reagentColor, rowCount++, name, reagentId, quantity, true, true));
            }
        }

        private void BuildContainerUI(Control control, ContainerInfo? info, bool addReagentButtons)
        {
            control.Children.Clear();

            if (info is null)
            {
                control.Children.Add(new Label
                {
                    Text = Loc.GetString("chem-master-window-no-container-loaded-text")
                });
                return;
            }

            // Name of the container and its fill status (Ex: 44/100u)
            control.Children.Add(new BoxContainer
            {
                Orientation = LayoutOrientation.Horizontal,
                Children =
                {
                    new Label { Text = $"{info.DisplayName}: " },
                    new Label
                    {
                        Text = $"{info.CurrentVolume}/{info.MaxVolume}",
                        StyleClasses = { StyleNano.StyleClassLabelSecondaryColor }
                    }
                }
            });
            // Initialises rowCount to allow for striped rows
            var rowCount = 0;

            // Handle entities if they are not null
            if (info.Entities != null)
            {
                foreach (var (id, quantity) in info.Entities.Select(x => (x.Id, x.Quantity)))
                {
                    control.Children.Add(BuildReagentRow(default(Color), rowCount++, id, default(ReagentId), quantity, false, addReagentButtons));
                }
            }

            // Handle reagents if they are not null
            if (info.Reagents != null)
            {
                foreach (var reagent in info.Reagents)
                {
                    _prototypeManager.TryIndex(reagent.Reagent.Prototype, out ReagentPrototype? proto);
                    var name = proto?.LocalizedName ?? Loc.GetString("chem-master-window-unknown-reagent-text");
                    var reagentColor = proto?.SubstanceColor ?? default(Color);

                    control.Children.Add(BuildReagentRow(reagentColor, rowCount++, name, reagent.Reagent, reagent.Quantity, false, addReagentButtons));
                }
            }
        }
        /// <summary>
        /// Take reagent/entity data and present rows, labels, and buttons appropriately. todo sprites?
        /// </summary>
        private Control BuildReagentRow(Color reagentColor, int rowCount, string name, ReagentId reagent, FixedPoint2 quantity, bool isBuffer, bool addReagentButtons)
        {
            //Colors rows and sets fallback for reagentcolor to the same as background, this will hide colorPanel for entities hopefully
            var rowColor1 = Color.FromHex("#1B1B1E");
            var rowColor2 = Color.FromHex("#202025");
            var currentRowColor = (rowCount % 2 == 1) ? rowColor1 : rowColor2;
            if ((reagentColor == default(Color))|(!addReagentButtons))
            {
                reagentColor = currentRowColor;
            }
            //this calls the separated button builder, and stores the return to render after labels
            var reagentButtonConstructor = CreateReagentTransferButton(reagent, isBuffer, addReagentButtons);

            // Create the row layout with the color panel
            var rowContainer = new BoxContainer
            {
                Orientation = LayoutOrientation.Horizontal,
                Children =
                {
                    new Label { Text = $"{name}: " },
                    new Label
                    {
                        Text = $"{quantity}u",
                        StyleClasses = { StyleNano.StyleClassLabelSecondaryColor }
                    },

                    // Padding
                    new Control { HorizontalExpand = true },
                    // Colored panels for reagents
                    new PanelContainer
                    {
                        Name = "colorPanel",
                        VerticalExpand = true,
                        MinWidth = 4,
                        PanelOverride = new StyleBoxFlat
                        {
                            BackgroundColor = reagentColor
                        },
                        Margin = new Thickness(0, 1)
                    }
                }
            };

            if (reagentButtonConstructor != null)
                rowContainer.AddChild(reagentButtonConstructor);

            //Apply panencontainer to allow for striped rows
            return new PanelContainer
            {
                PanelOverride = new StyleBoxFlat(currentRowColor),
                Children = { rowContainer }
            };
        }

        public string LabelLine
        {
            get => LabelLineEdit.Text;
            set => LabelLineEdit.Text = value;
        }
    }

    public sealed class ReagentButton : Button
    {
        public bool IsBuffer = true;
        public ReagentId Id { get; set; }
        public ReagentButton(string text, ReagentId id, bool isBuffer)
        {
            AddStyleClass(StyleBase.ButtonOpenLeft);
            Text = text;
            Id = id;
            IsBuffer = isBuffer;
        }
    }

    public enum ReagentSortMethod
    {
        Time,
        Alphabetical,
        Amount
    }
}
