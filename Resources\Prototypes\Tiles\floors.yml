- type: tile
  id: FloorSteel
  name: tiles-steel-floor
  sprite: /Textures/Tiles/steel.png
  variants: 8
  placementVariants:
  - 1.2
  - 1.0
  - 1.0
  - 1.0  
  - 1.0
  - 0.3
  - 1.0
  - 0.3
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemSteel
  heatCapacity: 10000

- type: tile
  id: FloorSteelCheckerLight
  name: tiles-steel-floor-checker-light
  sprite: /Textures/Tiles/cafeteria.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemSteelCheckerLight
  heatCapacity: 10000

- type: tile
  id: FloorSteelCheckerDark
  name: tiles-steel-floor-checker-dark
  sprite: /Textures/Tiles/checker_dark.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemSteelCheckerDark
  heatCapacity: 10000

- type: tile
  id: FloorSteelMini
  name: tiles-steel-floor-mini
  sprite: /Textures/Tiles/steel_mini.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemSteel
  heatCapacity: 10000

- type: tile
  id: FloorSteelPavement
  name: tiles-steel-floor-pavement
  sprite: /Textures/Tiles/steel_pavement.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemSteel
  heatCapacity: 10000

- type: tile
  id: FloorSteelDiagonal
  name: tiles-steel-floor-diagonal
  sprite: /Textures/Tiles/steel_diagonal.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemSteel
  heatCapacity: 10000

- type: tile
  id: FloorSteelOffset
  name: tiles-steel-floor-offset
  sprite: /Textures/Tiles/steel_offset.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemSteel
  heatCapacity: 10000

- type: tile
  id: FloorSteelMono
  name: tiles-steel-floor-mono
  sprite: /Textures/Tiles/steel_mono.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemSteel
  heatCapacity: 10000

- type: tile
  id: FloorSteelPavementVertical
  name: tiles-steel-floor-pavement-vertical
  sprite: /Textures/Tiles/steel_pavement_vertical.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemSteel
  heatCapacity: 10000

- type: tile
  id: FloorSteelHerringbone
  name: tiles-steel-floor-herringbone
  sprite: /Textures/Tiles/steel_herringbone.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemSteel
  heatCapacity: 10000

- type: tile
  id: FloorSteelDiagonalMini
  name: tiles-steel-floor-diagonal-mini
  sprite: /Textures/Tiles/steel_diagonal_mini.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemSteel
  heatCapacity: 10000

- type: tile
  id: FloorBrassFilled
  name: tiles-brass-floor-filled
  sprite: /Textures/Tiles/Misc/clockwork/clockwork_floor_filled.png
  baseTurf: PlatingBrass
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepHull
  itemDrop: FloorTileItemBrassFilled
  heatCapacity: 10000

- type: tile
  id: FloorBrassReebe
  name: tiles-brass-floor-reebe
  sprite: /Textures/Tiles/Misc/clockwork/reebe.png
  baseTurf: PlatingBrass
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepHull
  itemDrop: FloorTileItemBrassReebe
  heatCapacity: 10000

- type: tile
  id: FloorPlastic
  name: tiles-plastic-floor
  sprite: /Textures/Tiles/plastic.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemSteel
  heatCapacity: 10000

- type: tile
  id: FloorWood
  name: tiles-wood
  sprite: /Textures/Tiles/wood.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepWood
  barestepSounds:
    collection: BarestepWood
  itemDrop: FloorTileItemWood
  heatCapacity: 10000

- type: tile
  id: FloorWhite
  name: tiles-white-floor
  sprite: /Textures/Tiles/white.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemWhite
  heatCapacity: 10000

- type: tile
  id: FloorWhiteMini
  name: tiles-white-floor-mini
  sprite: /Textures/Tiles/white_mini.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemWhite
  heatCapacity: 10000

- type: tile
  id: FloorWhitePavement
  name: tiles-white-floor-pavement
  sprite: /Textures/Tiles/white_pavement.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemWhite
  heatCapacity: 10000

- type: tile
  id: FloorWhiteDiagonal
  name: tiles-white-floor-diagonal
  sprite: /Textures/Tiles/white_diagonal.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemWhite
  heatCapacity: 10000

- type: tile
  id: FloorWhiteOffset
  name: tiles-white-floor-offset
  sprite: /Textures/Tiles/white_offset.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemWhite
  heatCapacity: 10000

- type: tile
  id: FloorWhiteMono
  name: tiles-white-floor-mono
  sprite: /Textures/Tiles/white_mono.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemWhite
  heatCapacity: 10000

- type: tile
  id: FloorWhitePavementVertical
  name: tiles-white-floor-pavement-vertical
  sprite: /Textures/Tiles/white_pavement_vertical.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemWhite
  heatCapacity: 10000

- type: tile
  id: FloorWhiteHerringbone
  name: tiles-white-floor-herringbone
  sprite: /Textures/Tiles/white_herringbone.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemWhite
  heatCapacity: 10000

- type: tile
  id: FloorWhiteDiagonalMini
  name: tiles-white-floor-diagonal-mini
  sprite: /Textures/Tiles/white_diagonal_mini.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemWhite
  heatCapacity: 10000

- type: tile
  id: FloorWhitePlastic
  name: tiles-plastic-white-floor
  sprite: /Textures/Tiles/white_plastic.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemWhite
  heatCapacity: 10000

- type: tile
  id: FloorDark
  name: tiles-dark-floor
  sprite: /Textures/Tiles/dark.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemDark
  heatCapacity: 10000

- type: tile
  id: FloorDarkMini
  name: tiles-dark-floor-mini
  sprite: /Textures/Tiles/dark_mini.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemDark
  heatCapacity: 10000

- type: tile
  id: FloorDarkPavement
  name: tiles-dark-floor-pavement
  sprite: /Textures/Tiles/dark_pavement.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemDark
  heatCapacity: 10000

- type: tile
  id: FloorDarkDiagonal
  name: tiles-dark-floor-diagonal
  sprite: /Textures/Tiles/dark_diagonal.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemDark
  heatCapacity: 10000

- type: tile
  id: FloorDarkOffset
  name: tiles-dark-floor-offset
  sprite: /Textures/Tiles/dark_offset.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemDark
  heatCapacity: 10000

- type: tile
  id: FloorDarkMono
  name: tiles-dark-floor-mono
  sprite: /Textures/Tiles/dark_mono.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemDark
  heatCapacity: 10000

- type: tile
  id: FloorDarkPavementVertical
  name: tiles-dark-floor-pavement-vertical
  sprite: /Textures/Tiles/dark_pavement_vertical.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemDark
  heatCapacity: 10000

- type: tile
  id: FloorDarkHerringbone
  name: tiles-dark-floor-herringbone
  sprite: /Textures/Tiles/dark_herringbone.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemDark
  heatCapacity: 10000

- type: tile
  id: FloorDarkDiagonalMini
  name: tiles-dark-floor-diagonal-mini
  sprite: /Textures/Tiles/dark_diagonal_mini.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemDark
  heatCapacity: 10000

- type: tile
  id: FloorDarkPlastic
  name: tiles-plastic-dark-floor
  sprite: /Textures/Tiles/dark_plastic.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemDark
  heatCapacity: 10000

- type: tile
  id: FloorTechMaint
  name: tiles-techmaint-floor
  sprite: /Textures/Tiles/tech_maint.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepHull
  itemDrop: FloorTileItemTechmaint
  heatCapacity: 10000

- type: tile
  id: FloorReinforced
  name: tiles-reinforced-floor
  sprite: /Textures/Tiles/reinforced.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepHull
  itemDrop: FloorTileItemReinforced
  heatCapacity: 10000
  reinforced: true

- type: tile
  id: FloorMono
  name: tiles-mono-floor
  sprite: /Textures/Tiles/mono.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemMono
  heatCapacity: 10000

- type: tile
  id: FloorLino
  name: tiles-linoleum-floor
  sprite: /Textures/Tiles/lino.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemLino
  heatCapacity: 10000

- type: tile
  id: FloorSteelDirty
  name: tiles-dirty-steel-floor
  sprite: /Textures/Tiles/steel_dirty.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepPlating
  itemDrop: FloorTileItemDirty
  heatCapacity: 10000

- type: tile
  id: FloorElevatorShaft
  name: tiles-elevator-shaft
  sprite: /Textures/Tiles/elevator_shaft.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepHull
  itemDrop: FloorTileItemElevatorShaft
  heatCapacity: 10000

- type: tile
  id: FloorMetalDiamond
  name: tiles-diamond-plate-floor
  sprite: /Textures/Tiles/metaldiamond.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepHull
  itemDrop: FloorTileItemMetalDiamond
  heatCapacity: 10000

- type: tile
  id: FloorRockVault
  name: tiles-rock-floor
  sprite: /Textures/Tiles/rock_vault.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepAsteroid
  itemDrop: FloorTileItemRockVault
  heatCapacity: 10000

- type: tile
  id: FloorBlue
  name: tiles-blue-tile
  sprite: /Textures/Tiles/blue.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemBlue
  heatCapacity: 10000

- type: tile
  id: FloorSteelLime
  name: tiles-lime-floor
  sprite: /Textures/Tiles/lime.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemLime
  heatCapacity: 10000

- type: tile
  id: FloorMining
  name: tiles-mining-tile
  sprite: /Textures/Tiles/mining_floor.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemMining
  heatCapacity: 10000

- type: tile
  id: FloorMiningDark
  name: tiles-mining-dark-tile
  sprite: /Textures/Tiles/mining_floor_dark.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemMiningDark
  heatCapacity: 10000

- type: tile
  id: FloorMiningLight
  name: tiles-mining-light-tile
  sprite: /Textures/Tiles/mining_floor_light.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemMiningLight
  heatCapacity: 10000

# Departamental
- type: tile
  id: FloorFreezer
  name: tiles-freezer
  sprite: /Textures/Tiles/freezer.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepHull
  itemDrop: FloorTileItemFreezer
  heatCapacity: 10000

- type: tile
  id: FloorShowroom
  name: tiles-showroom-floor
  sprite: /Textures/Tiles/showroom.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemShowroom
  heatCapacity: 10000

- type: tile
  id: FloorHydro
  name: tiles-hydro-floor
  sprite: /Textures/Tiles/hydro.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemHydro
  heatCapacity: 10000

- type: tile
  id: FloorBar
  name: tiles-bar-floor
  sprite: /Textures/Tiles/bar.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemBar
  heatCapacity: 10000

- type: tile
  id: FloorClown
  name: tiles-clown-floor
  sprite: /Textures/Tiles/clown.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemClown
  heatCapacity: 10000

- type: tile
  id: FloorMime
  name: tiles-mime-floor
  sprite: /Textures/Tiles/mime.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemMime
  heatCapacity: 10000

- type: tile
  id: FloorKitchen
  name: tiles-kitchen-floor
  sprite: /Textures/Tiles/kitchen.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemKitchen
  heatCapacity: 10000

- type: tile
  id: FloorLaundry
  name: tiles-laundry-floor
  sprite: /Textures/Tiles/laundry.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemLaundry
  heatCapacity: 10000

- type: tile
  id: FloorSteelDamaged
  name: tiles-steel-floor
  sprite: /Textures/Tiles/steel_damaged.png
  variants: 5
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemSteel #This should probably be made null when it becomes possible to make it such, in SS13 prying destroyed tiles wouldn't give you anything.
  heatCapacity: 10000

- type: tile
  id: FloorSteelBurnt
  name: tiles-steel-floor
  sprite: /Textures/Tiles/steel_burnt.png
  variants: 2
  placementVariants:
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemSteel #Same case as FloorSteelDamaged, make it null when possible
  heatCapacity: 10000

# Concrete
- type: tile
  id: FloorConcrete
  name: tiles-concrete-tile
  sprite: /Textures/Tiles/Planet/Concrete/concrete.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemConcrete
  heatCapacity: 10000
  weather: true

- type: tile
  id: FloorConcreteMono
  name: tiles-concrete-slab
  sprite: /Textures/Tiles/Planet/Concrete/concrete_mono.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemConcrete
  heatCapacity: 10000
  weather: true

- type: tile
  id: FloorConcreteSmooth
  name: tiles-concrete-smooth
  sprite: /Textures/Tiles/Planet/Concrete/concrete_smooth.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemConcrete
  heatCapacity: 10000
  weather: true

- type: tile
  id: FloorGrayConcrete
  name: tiles-gray-concrete-tile
  sprite: /Textures/Tiles/Planet/Concrete/grayconcrete.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemGrayConcrete
  heatCapacity: 10000
  weather: true

- type: tile
  id: FloorGrayConcreteMono
  name: tiles-gray-concrete-slab
  sprite: /Textures/Tiles/Planet/Concrete/grayconcrete_mono.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemGrayConcrete
  heatCapacity: 10000
  weather: true

- type: tile
  id: FloorGrayConcreteSmooth
  name: tiles-gray-concrete-smooth
  sprite: /Textures/Tiles/Planet/Concrete/grayconcrete_smooth.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemGrayConcrete
  heatCapacity: 10000
  weather: true

- type: tile
  id: FloorOldConcrete
  name: tiles-old-concrete-tile
  sprite: /Textures/Tiles/Planet/Concrete/oldconcrete.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemOldConcrete
  heatCapacity: 10000
  weather: true

- type: tile
  id: FloorOldConcreteMono
  name: tiles-old-concrete-slab
  sprite: /Textures/Tiles/Planet/Concrete/oldconcrete_mono.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemOldConcrete
  heatCapacity: 10000
  weather: true

- type: tile
  id: FloorOldConcreteSmooth
  name: tiles-old-concrete-smooth
  sprite: /Textures/Tiles/Planet/Concrete/oldconcrete_smooth.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemOldConcrete
  heatCapacity: 10000
  weather: true

# Carpets (non smoothing)
- type: tile
  id: FloorArcadeBlue
  name: tiles-blue-arcade-floor
  sprite: /Textures/Tiles/arcadeblue.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepCarpet
  barestepSounds:
    collection: BarestepCarpet
  friction: 0.25
  itemDrop: FloorTileItemArcadeBlue
  heatCapacity: 10000

- type: tile
  id: FloorArcadeBlue2
  name: tiles-blue-arcade-floor
  sprite: /Textures/Tiles/arcadeblue2.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepCarpet
  barestepSounds:
    collection: BarestepCarpet
  friction: 0.25
  itemDrop: FloorTileItemArcadeBlue2
  heatCapacity: 10000

- type: tile
  id: FloorArcadeRed
  name: tiles-red-arcade-floor
  sprite: /Textures/Tiles/arcadered.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepCarpet
  barestepSounds:
    collection: BarestepCarpet
  friction: 0.25
  itemDrop: FloorTileItemArcadeRed
  heatCapacity: 10000

- type: tile
  id: FloorEighties
  name: tiles-eighties-floor
  sprite: /Textures/Tiles/eighties.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepCarpet
  barestepSounds:
    collection: BarestepCarpet
  friction: 0.25
  itemDrop: FloorTileItemEighties
  heatCapacity: 10000

- type: tile
  id: FloorCarpetClown
  name: tiles-clown-carpet-floor
  sprite: /Textures/Tiles/carpetclown.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepCarpet
  barestepSounds:
    collection: BarestepCarpet
  friction: 0.25
  itemDrop: FloorTileItemCarpetClown
  heatCapacity: 10000

- type: tile
  id: FloorCarpetOffice
  name: tiles-office-carpet-floor
  sprite: /Textures/Tiles/carpetoffice.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepCarpet
  barestepSounds:
    collection: BarestepCarpet
  friction: 0.25
  itemDrop: FloorTileItemCarpetOffice
  heatCapacity: 10000

- type: tile
  id: FloorBoxing
  name: tiles-boxing-ring-floor
  sprite: /Textures/Tiles/boxing.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  friction: 0.25
  itemDrop: FloorTileItemBoxing
  heatCapacity: 10000

- type: tile
  id: FloorGym
  name: tiles-gym-floor
  sprite: /Textures/Tiles/gym.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  friction: 0.25
  itemDrop: FloorTileItemGym
  heatCapacity: 10000

# Shuttle
- type: tile
  id: FloorShuttleWhite
  name: tiles-white-shuttle-floor
  sprite: /Textures/Tiles/shuttlewhite.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemShuttleWhite
  heatCapacity: 10000

- type: tile
  id: FloorShuttleGrey
  name: tiles-grey-shuttle-floor
  sprite: /Textures/Tiles/shuttlegrey.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemShuttleGrey
  heatCapacity: 10000

- type: tile
  id: FloorShuttleBlack
  name: tiles-black-shuttle-floor
  sprite: /Textures/Tiles/shuttleblack.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemShuttleBlack
  heatCapacity: 10000

- type: tile
  id: FloorShuttleBlue
  name: tiles-blue-shuttle-floor
  sprite: /Textures/Tiles/shuttleblue.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemShuttleBlue
  heatCapacity: 10000

- type: tile
  id: FloorShuttleOrange
  name: tiles-orange-shuttle-floor
  sprite: /Textures/Tiles/shuttleorange.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemShuttleOrange
  heatCapacity: 10000

- type: tile
  id: FloorShuttlePurple
  name: tiles-purple-shuttle-floor
  sprite: /Textures/Tiles/shuttlepurple.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemShuttlePurple
  heatCapacity: 10000

- type: tile
  id: FloorShuttleRed
  name: tiles-red-shuttle-floor
  sprite: /Textures/Tiles/shuttlered.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemShuttleRed
  heatCapacity: 10000

# Materials
- type: tile
  id: FloorGold
  name: tiles-gold-tile
  sprite: /Textures/Tiles/gold.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemGold
  heatCapacity: 10000

- type: tile
  id: FloorSilver
  name: tiles-silver-tile
  sprite: /Textures/Tiles/silver.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: FloorTileItemSilver
  heatCapacity: 10000

- type: tile
  id: FloorGlass
  name: tiles-glass-floor
  sprite: /Textures/DeltaV/Tiles/glass.png # DeltaV - Replace glass texture so it's easier to see
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: SheetGlass1
  heatCapacity: 10000

- type: tile
  id: FloorRGlass
  name: tiles-reinforced-glass-floor
  sprite: /Textures/Tiles/rglass.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepTile
  itemDrop: SheetRGlass1
  heatCapacity: 10000

# Circuits
- type: tile
  id: FloorGreenCircuit
  name: tiles-green-circuit-floor
  sprite: /Textures/Tiles/green_circuit.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepHull
  itemDrop: FloorTileItemGCircuit
  heatCapacity: 10000

- type: tile
  id: FloorBlueCircuit
  name: tiles-blue-circuit-floor
  sprite: /Textures/Tiles/blue_circuit.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepHull
  itemDrop: FloorTileItemBCircuit
  heatCapacity: 10000

- type: tile
  id: FloorRedCircuit
  name: "підлога з червоною схемою"
  sprite: /Textures/Tiles/red_circuit.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepHull
  itemDrop: FloorTileItemRCircuit
  heatCapacity: 10000
  
# Terrain
- type: tile
  id: FloorAsphalt
  name: tiles-asphalt
  sprite: /Textures/Tiles/Planet/Concrete/asphalt.png
  variants: 10
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: FloorDirt
  isSubfloor: false
  footstepSounds:
    collection: FootstepTile
  heatCapacity: 10000
  weather: true

- type: tile
  id: FloorGrass
  name: tiles-planet-grass-floor
  sprite: /Textures/Tiles/grass.png
  baseTurf: FloorDirt
  isSubfloor: false
  canShovel: true #DV
  footstepSounds:
    collection: FootstepGrass
  itemDrop: FloorTileItemGrass
  heatCapacity: 10000
  weather: true

- type: tile
  id: FloorGrassJungle
  name: tiles-jungle-grass-floor
  sprite: /Textures/Tiles/grassjungle.png
  baseTurf: FloorDirt
  isSubfloor: false
  canShovel: true # Delta V
  footstepSounds:
    collection: FootstepGrass
  itemDrop: FloorTileItemGrassJungle
  heatCapacity: 10000
  weather: true

- type: tile
  id: FloorGrassDark
  name: tiles-dark-grass-floor
  sprite: /Textures/Tiles/grassdark.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: FloorDirt
  isSubfloor: false
  canShovel: true # Delta V
  footstepSounds:
    collection: FootstepGrass
  itemDrop: FloorTileItemGrassDark # Delta V
  heatCapacity: 10000
  weather: true

- type: tile
  id: FloorGrassLight
  name: tiles-light-grass-floor
  sprite: /Textures/Tiles/grasslight.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: FloorDirt
  isSubfloor: false
  canShovel: true # Delta V
  footstepSounds:
    collection: FootstepGrass
  itemDrop: FloorTileItemGrassLight
  heatCapacity: 10000
  weather: true

- type: tile
  id: FloorDirt
  name: tiles-dirt-floor
  sprite: /Textures/Tiles/dirt.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: true
  canShovel: true # Delta V
  footstepSounds:
    collection: FootstepAsteroid
  itemDrop: FloorTileItemDirt # Delta V
  heatCapacity: 10000
  weather: True

# Asteroid

- type: tile
  id: FloorAsteroidSand
  name: tiles-asteroid-sand
  sprite: /Textures/Tiles/Asteroid/asteroid.png
  variants: 13
  placementVariants:
  - 0.8
  - 0.0166 #Should be roughly 20%.... I think??? I don't know dude, I'm just a YAML monkey.
  - 0.0166
  - 0.0166
  - 0.0166
  - 0.0166
  - 0.0166
  - 0.0166
  - 0.0166
  - 0.0166
  - 0.0166
  - 0.0116
  - 0.0116
  baseTurf: Space
  isSubfloor: true
  footstepSounds:
    collection: FootstepAsteroid
  heatCapacity: 10000
  weather: true

- type: tile
  id: FloorAsteroidSandDug
  name: tiles-asteroid-sand
  sprite: /Textures/Tiles/Asteroid/asteroid_dug.png
  baseTurf: Space
  isSubfloor: true
  footstepSounds:
    collection: FootstepAsteroid
  heatCapacity: 10000
  weather: true

- type: tile
  id: FloorAsteroidSandRed
  name: tiles-asteroid-sand
  sprite: /Textures/Tiles/Asteroid/asteroid_red.png
  variants: 13
  placementVariants:
  - 0.8
  - 0.0166
  - 0.0166
  - 0.0166
  - 0.0166
  - 0.0166
  - 0.0166
  - 0.0166
  - 0.0166
  - 0.0166
  - 0.0166
  - 0.0116
  - 0.0116
  baseTurf: Space
  isSubfloor: true
  footstepSounds:
    collection: FootstepAsteroid
  heatCapacity: 10000
  weather: true

- type: tile
  id: FloorAsteroidTile
  name: tiles-asteroid-tile
  sprite: /Textures/Tiles/Asteroid/asteroid_tile.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepAsteroid
  heatCapacity: 10000
  weather: true

- type: tile
  id: FloorAsteroidIronsand
  name: tiles-asteroid-ironsand
  sprite: /Textures/Tiles/Asteroid/ironsand.png
  variants: 15
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Space
  isSubfloor: true
  footstepSounds:
    collection: FootstepAsteroid
  heatCapacity: 10000
  weather: true

- type: tile
  id: FloorAsteroidSandUnvariantized
  name: tiles-asteroid-sand
  sprite: /Textures/Tiles/Asteroid/asteroid0.png
  baseTurf: Space
  isSubfloor: true
  footstepSounds:
    collection: FootstepAsteroid
  heatCapacity: 10000
  weather: true

- type: tile
  id: FloorAsteroidIronsandUnvariantized
  name: tiles-asteroid-ironsand
  sprite: /Textures/Tiles/Asteroid/ironsand0.png
  baseTurf: Space
  isSubfloor: true
  footstepSounds:
    collection: FootstepAsteroid
  heatCapacity: 10000
  weather: true

# Caves
- type: tile
  id: FloorCave
  name: tiles-cave
  sprite: /Textures/Tiles/cave.png
  variants: 7
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Space
  isSubfloor: true
  footstepSounds:
    collection: FootstepAsteroid
  heatCapacity: 10000

- type: tile
  id: FloorCaveDrought
  name: tiles-cave-drought
  sprite: /Textures/Tiles/cavedrought.png
  variants: 8
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Space
  isSubfloor: true
  footstepSounds:
    collection: FootstepAsteroid
  heatCapacity: 10000

- type: tile
  id: FloorFlesh
  name: tiles-flesh-floor
  sprite: /Textures/Tiles/meat.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepBlood
  itemDrop: FloorTileItemFlesh
  friction: 0.05 #slippy
  heatCapacity: 10000

- type: tile
  id: FloorTechMaint2
  name: tiles-techmaint2-floor
  sprite: /Textures/Tiles/steel_maint.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepHull
  itemDrop: FloorTileItemSteelMaint
  heatCapacity: 10000

- type: tile
  id: FloorTechMaint3
  name: tiles-techmaint3-floor
  sprite: /Textures/Tiles/grating_maint.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepHull
  itemDrop: FloorTileItemGratingMaint
  heatCapacity: 10000

- type: tile
  id: FloorWoodTile
  name: tiles-wood2
  sprite: /Textures/Tiles/wood_tile.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepWood
  barestepSounds:
    collection: BarestepWood
  itemDrop: FloorTileItemWoodPattern
  heatCapacity: 10000

- type: tile
  id: FloorBrokenWood
  name: tiles-wood3
  sprite: /Textures/Tiles/wood_broken.png
  variants: 7
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepWood
  barestepSounds:
    collection: BarestepWood
  itemDrop: MaterialWoodPlank1
  heatCapacity: 10000

- type: tile
  id: FloorWebTile
  name: tiles-web
  sprite: /Textures/Tiles/Misc/Web/web_tile.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepCarpet
  barestepSounds:
    collection: BarestepCarpet
  itemDrop: FloorTileItemWeb
  heatCapacity: 10000

- type: tile
  id: FloorChromite
  name: tiles-chromite
  sprite: /Textures/Tiles/chromite.png
  variants: 7
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Space
  isSubfloor: true
  footstepSounds:
    collection: FootstepAsteroid
  heatCapacity: 10000

#Hull tiles
- type: tile
  id: FloorHull
  name: tiles-hull
  sprite: /Textures/Tiles/hull.png
  baseTurf: Plating
  isSubfloor: false
  footstepSounds:
    collection: FootstepHull
  itemDrop: FloorTileItemSteel #probably should not be normally obtainable, but the game shits itself and dies when you try to put null here
  heatCapacity: 10000

- type: tile
  id: FloorHullReinforced
  name: tiles-hull-reinforced
  sprite: /Textures/Tiles/hull_reinforced.png
  baseTurf: Plating
  isSubfloor: false
  footstepSounds:
    collection: FootstepHull
  itemDrop: FloorTileItemSteel
  heatCapacity: 100000 #/tg/ has this set as "INFINITY." I don't know if that exists here so I've just added an extra 0
  indestructible: true
  reinforced: true

- type: tile
  id: FloorReinforcedHardened
  name: tiles-super-reinforced-floor
  sprite: /Textures/Tiles/super_reinforced.png
  baseTurf: Plating
  isSubfloor: false
  footstepSounds:
    collection: FootstepHull
  itemDrop: FloorTileItemReinforced #same case as FloorHull
  reinforced: true

# Faux sci tiles

# Grass
- type: tile
  id: FloorAstroGrass
  name: tiles-astro-grass
  sprite: /Textures/Tiles/Planet/Grass/grass.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  edgeSpritePriority: 1
  edgeSprites:
    SouthEast: /Textures/Tiles/Planet/Grass/single_edge.png
    NorthEast: /Textures/Tiles/Planet/Grass/single_edge.png
    NorthWest: /Textures/Tiles/Planet/Grass/single_edge.png
    SouthWest: /Textures/Tiles/Planet/Grass/single_edge.png
    South: /Textures/Tiles/Planet/Grass/double_edge.png
    East: /Textures/Tiles/Planet/Grass/double_edge.png
    North: /Textures/Tiles/Planet/Grass/double_edge.png
    West: /Textures/Tiles/Planet/Grass/double_edge.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Cutting ]
  footstepSounds:
    collection: FootstepGrass
  itemDrop: FloorTileItemAstroGrass
  heatCapacity: 10000

- type: tile
  id: FloorMowedAstroGrass
  name: tiles-mowed-astro-grass
  parent: FloorGrass
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Cutting ]
  itemDrop: FloorTileItemMowedAstroGrass

- type: tile
  id: FloorJungleAstroGrass
  name: tiles-jungle-astro-grass
  parent: FloorGrassJungle
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Cutting ]
  itemDrop: FloorTileItemJungleAstroGrass

# Ice
- type: tile
  id: FloorAstroIce
  name: tiles-astro-ice
  sprite: /Textures/Tiles/Planet/Snow/ice.png
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  friction: 0.05
  heatCapacity: 10000
  mobFriction: 0.5
  mobFrictionNoInput: 0.05
  mobAcceleration: 2
  itemDrop: FloorTileItemAstroIce

- type: tile
  id: FloorAstroSnow
  name: tiles-astro-snow
  parent: FloorSnow
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  itemDrop: FloorTileItemAstroSnow

- type: tile
  id: FloorWoodLarge
  name: tiles-wood-large
  sprite: /Textures/Tiles/wood_large.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepWood
  barestepSounds:
    collection: BarestepWood
  itemDrop: FloorTileItemWoodLarge
  heatCapacity: 10000
  
- type: tile
  id: FloorSteelWorn
  name: tiles-steel-floor-worn
  sprite: /Textures/Tiles/steel_worn.png
  variants: 4
  placementVariants:
  - 1.0
  - 1.0
  - 1.0
  - 1.0  
  baseTurf: Plating
  isSubfloor: false
  deconstructTools: [ Prying ]
  footstepSounds:
    collection: FootstepFloor
  itemDrop: FloorTileItemSteel
  heatCapacity: 10000
