- type: entity
  save: false
  name: "Уріст МакКукі"
  parent: BaseMobSpeciesOrganic
  id: BaseMobGingerbread
  abstract: true
  components:
  - type: HumanoidAppearance
    species: Gingerbread
  - type: Icon
    sprite: Mobs/Species/Gingerbread/parts.rsi
    state: full
  - type: Body
    prototype: Gingerbread
    requiredLegs: 2
  - type: Damageable
    damageContainer: Biological
    damageModifierSet: Gingerbread
  - type: DamageVisuals
    damageOverlayGroups:
      Brute:
        sprite: Mobs/Effects/brute_damage.rsi
        color: "#896e55"
  - type: Butcherable
    butcheringType: Spike
    spawned:
      - id: FoodBakedCookie #should be replaced with gingerbread sheets or something... provided you're willing to make a full spriteset of those.
        amount: 5
  - type: Bloodstream
    bloodReagent: Sugar
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 100 #fairly light
        restitution: 0.0
        mask:
          - MobMask
        layer:
          - MobLayer

- type: entity
  parent: BaseSpeciesDummy
  id: MobGingerbreadDummy
  categories: [ HideSpawnMenu ]
  components:
  - type: HumanoidAppearance
    species: Gingerbread
