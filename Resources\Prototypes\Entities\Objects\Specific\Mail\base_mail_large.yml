# Large packages.
- type: entity
  parent: BaseMail
  abstract: true
  id: BaseMailLarge
  name: mail-large-item-name-unaddressed
  components:
  - type: Item
    size: Ginormous
  - type: Sprite
    scale: 0.8, 0.8
    sprite: Objects/Specific/Mail/mail_large.rsi
    layers:
    - state: icon
      map: ["enum.MailVisualLayers.Icon"]
    - state: fragile
      map: ["enum.MailVisualLayers.FragileStamp"]
      visible: false
    - map: ["enum.MailVisualLayers.JobStamp"]
      scale: 0.8, 0.8
      offset: 0.235, -0.01
    - state: locked
      map: ["enum.MailVisualLayers.Lock"]
    - state: priority
      map: ["enum.MailVisualLayers.PriorityTape"]
      visible: false
      shader: unshaded
    - state: broken
      map: ["enum.MailVisualLayers.Breakage"]
      visible: false
  - type: GenericVisualizer
    visuals:
      enum.MailVisuals.IsTrash:
        enum.MailVisualLayers.Icon:
          True:
            state: trash
          False:
            state: icon
      enum.MailVisuals.IsLocked:
        enum.MailVisualLayers.Lock:
          True:
            visible: true
          False:
            visible: false
      enum.MailVisuals.IsFragile:
        enum.MailVisualLayers.FragileStamp:
          True:
            visible: true
          False:
            visible: false
      enum.MailVisuals.IsPriority:
        enum.MailVisualLayers.PriorityTape:
          True:
            visible: true
          False:
            visible: false
      enum.MailVisuals.IsPriorityInactive:
        enum.MailVisualLayers.PriorityTape:
          True:
            shader: shaded
            state: priority_inactive
          False:
            shader: unshaded
            state: priority
      enum.MailVisuals.IsBroken:
        enum.MailVisualLayers.Breakage:
          True:
            visible: true
          False:
            visible: false
  - type: MultiHandedItem
  - type: Mail
    isLarge: true

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMailLarge
  id: MailLargeAdminFun
  suffix: adminfun
