- type: entity
  parent: WallSolid #basically non-Reinforced Wall (shuttle variant)
  id: WallShuttleInterior
  name: "стіна шатла"
  suffix: Interior
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Walls/shuttleinterior.rsi
  - type: Icon
    sprite: Nyanotrasen/Structures/Walls/shuttleinterior.rsi
  - type: Construction
    graph: Girder
    node: shuttleInteriorWall
  - type: IconSmooth
    key: walls
    base: state
  - type: Reflect
    reflectProb: 1

- type: entity
  parent: BaseWall
  id: WallPaper
  name: "паперова стіна"
  description: "Допомагає стримувати повторювані крики майстрів бойових мистецтв."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Walls/paper.rsi
  - type: Icon
    sprite: Nyanotrasen/Structures/Walls/paper.rsi
  - type: Construction
    graph: Girder
    node: paperWall
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300 # #excess damage (nuke?). avoid computational cost of spawning entities.
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:ChangeConstructionNodeBehavior
        node: girder
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: IconSmooth
    key: walls
    base: paperwall

# TODO: construction graph, material, better sounds
- type: entity
  parent: BaseWall
  id: WallDrywall
  name: "гіпсокартон"
  description: "Виглядає досить міцно."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Walls/drywall.rsi
  - type: Icon
    sprite: Nyanotrasen/Structures/Walls/drywall.rsi
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Wood
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: IconSmooth
    key: walls
    base: drywall
  - type: Tag
    tags:
      - Wall
