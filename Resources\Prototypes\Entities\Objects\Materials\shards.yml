- type: entity
  abstract: true
  parent: BaseItem
  id: ShardBase
  description: "Це уламок якогось невідомого матеріалу."
  components:
  - type: Sharp
  - type: Execution
    doAfterDuration: 4.0
  - type: Sprite
    layers:
      - sprite: Objects/Materials/Shards/shard.rsi
        state: shard1
        map: [ "enum.DamageStateVisualLayers.Base" ]
  - type: RandomSprite
    available:
      - enum.DamageStateVisualLayers.Base:
          shard1: ""
          shard2: ""
          shard3: ""
  - type: MeleeWeapon
    attackRate: 1
    damage:
      types:
        Slash: 3.5
  - type: Item
    sprite: Objects/Materials/Shards/shard.rsi
    size: Tiny
  - type: CollisionWake
    enabled: false
  - type: Fixtures
    fixtures:
      slips:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.2,-0.2,0.2,0.2"
        hard: false
        layer:
        - LowImpassable
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.2,-0.2,0.2,0.2"
        density: 30
        mask:
        - ItemMask
  - type: DamageOtherOnHit
    meleeDamageMultiplier: 2
  - type: EmbeddableProjectile
    sound: /Audio/Weapons/bladeslice.ogg
    removalTime: 2.5
    autoRemoveDuration: 30
  - type: EmbedPassiveDamage
  - type: Tag
    tags:
    - Trash
  - type: SpaceGarbage
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Glass
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: StepTrigger
    triggerGroups:
      types:
      - Shard
    intersectRatio: 0.2
  - type: ClothingRequiredStepTrigger
  - type: Slippery
    slipSound:
      path: /Audio/Effects/glass_step.ogg
    launchForwardsMultiplier: 0
  - type: TriggerOnStepTrigger
  - type: DamageUserOnTrigger
    damage:
      types:
        Piercing: 5
  - type: DeleteOnTrigger
  - type: StaticPrice
    price: 0
  # Shitmed Change
  - type: Scalpel
    speed: 0.45
  - type: SurgeryTool
    startSound:
      path: /Audio/_Shitmed/Medical/Surgery/scalpel1.ogg
    endSound:
      path: /Audio/_Shitmed/Medical/Surgery/scalpel2.ogg

- type: entity
  parent: ShardBase
  id: ShardGlass
  name: "уламок скла"
  description: "Маленький шматочок скла."
  components:
  - type: Sprite
    color: "#bbeeff"
  - type: ToolRefinable
    refineResult:
    - id: SheetGlass1
  - type: DamageUserOnTrigger
    damage:
      types:
        Piercing: 4.5
  - type: Tag
    tags:
      - GlassShard
      - Trash
  - type: Extractable
    grindableSolutionName: shardglass
  - type: SolutionContainerManager
    solutions:
      shardglass:
        reagents:
        - ReagentId: Silicon
          Quantity: 5 #Half of the value of regular glass. TECHNICALLY, welding a single shard of glass gives you the full thing back with just 1 sheet, but that is stupid so I am making it half.
  - type: Construction
    graph: ShivConstruct
    node: start

- type: entity
  parent: ShardBase
  id: ShardGlassReinforced
  name: "уламок армованого скла"
  description: "Невеликий шматок армованого скла."
  components:
  - type: Sprite
    color: "#96cdef"
  - type: MeleeWeapon
    damage:
      types:
        Slash: 4.5
  - type: ToolRefinable
    refineResult:
    - id: SheetGlass1
    - id: PartRodMetal1
  - type: DamageUserOnTrigger
    damage:
      types:
        Piercing: 5.5
  - type: Tag
    tags:
      - ReinforcedGlassShard
      - Trash
  - type: Extractable
    grindableSolutionName: shardrglass
  - type: SolutionContainerManager
    solutions:
      shardrglass:
        reagents:
        - ReagentId: Silicon
          Quantity: 5 #I don't care enough to divide all of the reinforced glass materials by 2 because reinforced glass shards are due for removal anyways.
  - type: Construction
    graph: ReinforcedShivConstruct
    node: start

- type: entity
  parent: ShardBase
  id: ShardGlassPlasma
  name: "уламок плазмового скла"
  description: "Невеликий шматочок плазмового скла."
  components:
  - type: Sprite
    color: "#FF72E7"
  - type: MeleeWeapon
    damage:
      types:
        Slash: 5.5
  - type: ToolRefinable
    refineResult:
      - id: SheetGlass1
      - id: SheetPlasma1
  - type: EmbedPassiveDamage
    damage:
      types:
        Slash: 0.15
  - type: DamageUserOnTrigger
    damage:
      types:
        Piercing: 6.5
  - type: Tag
    tags:
      - PlasmaGlassShard
      - Trash
  - type: Extractable
    grindableSolutionName: shardpglass
  - type: SolutionContainerManager
    solutions:
      shardpglass:
        reagents:
        - ReagentId: Silicon
          Quantity: 5
        - ReagentId: Plasma
          Quantity: 5
  - type: Construction
    graph: PlasmaShivConstruct
    node: start

- type: entity
  parent: ShardBase
  id: ShardGlassUranium
  name: "уламок уранового скла"
  description: "Маленький шматочок уранового скла."
  components:
  - type: Sprite
    color: "#8eff7a"
  - type: MeleeWeapon
    damage:
      types:
        Slash: 4.5
        Radiation: 2
  - type: ToolRefinable
    refineResult:
    - id: SheetGlass1
    - id: SheetUranium1
  - type: DamageUserOnTrigger
    damage:
      types:
        Piercing: 5
        Radiation: 2.5
  - type: Tag
    tags:
      - UraniumGlassShard
      - Trash
  - type: Extractable
    grindableSolutionName: sharduglass
  - type: SolutionContainerManager
    solutions:
      sharduglass:
        reagents:
        - ReagentId: Silicon
          Quantity: 5
        - ReagentId: Uranium
          Quantity: 5
  - type: Construction
    graph: UraniumShivConstruct
    node: start

- type: entity
  parent: ShardBase
  id: ShardGlassClockwork
  name: "уламок годинникового скла"
  description: "Невеликий шматок скла, інкрустований латунню."
  components:
  - type: Sprite
    color: "#e0aa36"
  - type: ToolRefinable
    refineResult:
    - id: SheetGlass1
    - id: SheetBrass1
  - type: DamageUserOnTrigger
    damage:
      types:
        Piercing: 5
  - type: Tag
    tags:
      - ClockworkGlassShard
      - Trash
  - type: Extractable
    grindableSolutionName: shardcglass
  - type: SolutionContainerManager
    solutions:
      shardcglass:
        reagents:
        - ReagentId: Silicon
          Quantity: 5
        - ReagentId: Zinc
          Quantity: 1.65
        - ReagentId: Copper
          Quantity: 3.35
