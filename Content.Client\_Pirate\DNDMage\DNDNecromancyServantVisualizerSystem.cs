using Content.Shared._Pirate.DNDMage;
using Robust.Client.GameObjects;
using Robust.Shared.GameObjects;
using System.Numerics;

namespace Content.Client._Pirate.DNDMage;

/// <summary>
/// Система для відображення візуальних ефектів слуг некроманта
/// </summary>
public sealed class DNDNecromancyServantVisualizerSystem : EntitySystem
{
    public override void Initialize()
    {
        base.Initialize();

        SubscribeLocalEvent<DNDNecromancyServantVisualComponent, ComponentInit>(OnInit);
        SubscribeLocalEvent<DNDNecromancyServantVisualComponent, ComponentShutdown>(OnShutdown);
    }

    private void OnInit(EntityUid uid, DNDNecromancyServantVisualComponent component, ComponentInit args)
    {
        if (!TryComp<SpriteComponent>(uid, out var sprite))
            return;

        // Додаємо світіння очей
        AddEyeGlow(uid, sprite, component);

        // Додаємо темну ауру
        AddDarkAura(uid, sprite, component);

        // Додаємо частинки некромантії
        if (component.ShowNecromancyParticles)
        {
            AddNecromancyParticles(uid, component);
        }
    }

    private void OnShutdown(EntityUid uid, DNDNecromancyServantVisualComponent component, ComponentShutdown args)
    {
        // Видаляємо всі ефекти при знищенні компонента
        if (TryComp<SpriteComponent>(uid, out var sprite))
        {
            RemoveVisualEffects(uid, sprite);
        }
    }

    /// <summary>
    /// Додає світіння очей слуги
    /// </summary>
    private void AddEyeGlow(EntityUid uid, SpriteComponent sprite, DNDNecromancyServantVisualComponent component)
    {
        if (!component.ShowDarkAura)
            return;

        // Додаємо шар для світіння очей
        var eyeLayer = sprite.LayerMapReserveBlank("necromancy_eye_glow");
        sprite.LayerSetTexture(eyeLayer, "/Textures/_Pirate/Effects/necromancy_eye_glow.png");
        sprite.LayerSetColor(eyeLayer, component.EyeGlowColor.WithAlpha(component.EyeGlowIntensity));
        sprite.LayerSetShader(eyeLayer, "unshaded");
    }

    /// <summary>
    /// Додає темну ауру навколо слуги
    /// </summary>
    private void AddDarkAura(EntityUid uid, SpriteComponent sprite, DNDNecromancyServantVisualComponent component)
    {
        if (!component.ShowDarkAura)
            return;

        // Додаємо шар для темної аури
        var auraLayer = sprite.LayerMapReserveBlank("necromancy_aura");
        sprite.LayerSetTexture(auraLayer, "/Textures/_Pirate/Effects/necromancy_aura.png");
        sprite.LayerSetColor(auraLayer, component.AuraColor.WithAlpha(component.AuraIntensity));
        sprite.LayerSetScale(auraLayer, new Vector2(component.AuraRadius, component.AuraRadius));
        sprite.LayerSetShader(auraLayer, "unshaded");
    }

    /// <summary>
    /// Додає частинки некромантії
    /// </summary>
    private void AddNecromancyParticles(EntityUid uid, DNDNecromancyServantVisualComponent component)
    {
        // Тут можна додати систему частинок, якщо вона доступна
        // Наразі залишаємо заглушку для майбутньої реалізації
    }

    /// <summary>
    /// Видаляє всі візуальні ефекти
    /// </summary>
    private void RemoveVisualEffects(EntityUid uid, SpriteComponent sprite)
    {
        if (sprite.LayerMapTryGet("necromancy_eye_glow", out var eyeLayer))
        {
            sprite.RemoveLayer(eyeLayer);
        }

        if (sprite.LayerMapTryGet("necromancy_aura", out var auraLayer))
        {
            sprite.RemoveLayer(auraLayer);
        }
    }
}
