# Felinid Ears

- type: marking
  id: FelinidEarsBasic
  bodyPart: HeadTop
  markingCategory: HeadTop
  speciesRestriction: [<PERSON><PERSON><PERSON>, Human] # Einstein Engines - Humans get felinid markings
  sprites:
  - sprite: Nyanotrasen/Mobs/Customization/felinid_ears.rsi
    state: basic_outer
  - sprite: Nyanotrasen/Mobs/Customization/felinid_ears.rsi
    state: basic_inner

- type: marking
  id: FelinidEarsCurled
  bodyPart: HeadTop
  markingCategory: HeadTop
  speciesRestriction: [<PERSON>linid, Human] # Einstein Engines - Humans get felinid markings
  sprites:
  - sprite: Nyanotrasen/Mobs/Customization/felinid_ears.rsi
    state: curled_outer
  - sprite: Nyanotrasen/Mobs/Customization/felinid_ears.rsi
    state: curled_inner

- type: marking
  id: FelinidEarsDroopy
  bodyPart: HeadTop
  markingCategory: HeadTop
  speciesRestriction: [Felinid, Human] # Einstein Engines - Humans get felinid markings
  sprites:
  - sprite: Nyanotrasen/Mobs/Customization/felinid_ears.rsi
    state: droopy_outer
  - sprite: Nyanotrasen/Mobs/Customization/felinid_ears.rsi
    state: droopy_inner

- type: marking
  id: FelinidEarsFuzzy
  bodyPart: HeadTop
  markingCategory: HeadTop
  speciesRestriction: [Felinid, Human] # Einstein Engines - Humans get felinid markings
  sprites:
  - sprite: Nyanotrasen/Mobs/Customization/felinid_ears.rsi
    state: basic_outer
  - sprite: Nyanotrasen/Mobs/Customization/felinid_ears.rsi
    state: fuzzy_inner

- type: marking
  id: FelinidEarsStubby
  bodyPart: HeadTop
  markingCategory: HeadTop
  speciesRestriction: [Felinid, Human] # Einstein Engines - Humans get felinid markings
  sprites:
  - sprite: Nyanotrasen/Mobs/Customization/felinid_ears.rsi
    state: stubby_outer
  - sprite: Nyanotrasen/Mobs/Customization/felinid_ears.rsi
    state: stubby_inner

- type: marking
  id: FelinidEarsTall
  bodyPart: HeadTop
  markingCategory: HeadTop
  speciesRestriction: [Felinid, Human] # Einstein Engines - Humans get felinid markings
  sprites:
  - sprite: Nyanotrasen/Mobs/Customization/felinid_ears.rsi
    state: tall_outer
  - sprite: Nyanotrasen/Mobs/Customization/felinid_ears.rsi
    state: tall_inner
  - sprite: Nyanotrasen/Mobs/Customization/felinid_ears.rsi
    state: tall_fuzz

- type: marking
  id: FelinidEarsTorn
  bodyPart: HeadTop
  markingCategory: HeadTop
  speciesRestriction: [Felinid, Human] # Einstein Engines - Humans get felinid markings
  sprites:
  - sprite: Nyanotrasen/Mobs/Customization/felinid_ears.rsi
    state: torn_outer
  - sprite: Nyanotrasen/Mobs/Customization/felinid_ears.rsi
    state: torn_inner

- type: marking
  id: FelinidEarsWide
  bodyPart: HeadTop
  markingCategory: HeadTop
  speciesRestriction: [Felinid, Human, Tajaran] # Einstein Engines - Humans get felinid markings, Tajaran directly use felinid tails
  sprites:
  - sprite: Nyanotrasen/Mobs/Customization/felinid_ears.rsi
    state: wide_outer
  - sprite: Nyanotrasen/Mobs/Customization/felinid_ears.rsi
    state: wide_inner

# Felinid Tails

- type: marking
  id: FelinidTailBasic
  bodyPart: Tail
  markingCategory: Tail
  speciesRestriction: [Felinid, Human, Tajaran] # Einstein Engines - Humans get felinid markings
  sprites:
  - sprite: Nyanotrasen/Mobs/Customization/felinid_tails.rsi
    state: basic_tail_tip
  - sprite: Nyanotrasen/Mobs/Customization/felinid_tails.rsi
    state: basic_tail_stripes_even
  - sprite: Nyanotrasen/Mobs/Customization/felinid_tails.rsi
    state: basic_tail_stripes_odd

- type: marking
  id: FelinidTailBasicWithBow
  bodyPart: Tail
  markingCategory: Tail
  speciesRestriction: [Felinid, Human, Tajaran] # Einstein Engines - Humans get felinid markings, Tajaran directly use felinid tails
  sprites:
  - sprite: Nyanotrasen/Mobs/Customization/felinid_tails.rsi
    state: basic_tail_tip
  - sprite: Nyanotrasen/Mobs/Customization/felinid_tails.rsi
    state: basic_tail_stripes_even
  - sprite: Nyanotrasen/Mobs/Customization/felinid_tails.rsi
    state: basic_tail_stripes_odd
  - sprite: Nyanotrasen/Mobs/Customization/felinid_tails.rsi
    state: basic_bow

- type: marking
  id: FelinidTailBasicWithBell
  bodyPart: Tail
  markingCategory: Tail
  speciesRestriction: [Felinid, Human, Tajaran] # Einstein Engines - Humans get felinid markings, Tajaran directly use felinid tails
  sprites:
  - sprite: Nyanotrasen/Mobs/Customization/felinid_tails.rsi
    state: basic_tail_tip
  - sprite: Nyanotrasen/Mobs/Customization/felinid_tails.rsi
    state: basic_tail_stripes_even
  - sprite: Nyanotrasen/Mobs/Customization/felinid_tails.rsi
    state: basic_tail_stripes_odd
  - sprite: Nyanotrasen/Mobs/Customization/felinid_tails.rsi
    state: basic_bell

- type: marking
  id: FelinidTailBasicWithBowAndBell
  bodyPart: Tail
  markingCategory: Tail
  speciesRestriction: [Felinid, Human, Tajaran] # Einstein Engines - Humans get felinid markings, Tajaran directly use felinid tails
  sprites:
  - sprite: Nyanotrasen/Mobs/Customization/felinid_tails.rsi
    state: basic_tail_tip
  - sprite: Nyanotrasen/Mobs/Customization/felinid_tails.rsi
    state: basic_tail_stripes_even
  - sprite: Nyanotrasen/Mobs/Customization/felinid_tails.rsi
    state: basic_tail_stripes_odd
  - sprite: Nyanotrasen/Mobs/Customization/felinid_tails.rsi
    state: basic_bow
  - sprite: Nyanotrasen/Mobs/Customization/felinid_tails.rsi
    state: basic_bell
