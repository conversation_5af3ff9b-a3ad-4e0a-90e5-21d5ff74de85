swap-teleporter-popup-link-create = Quantum link established!
swap-teleporter-popup-link-fail-already = Quantum link failed! Link already present on device.
swap-teleporter-popup-link-fail-already-other = Quantum link failed! Link already present on secondary device.
swap-teleporter-popup-link-destroyed = Quantum link destroyed!
swap-teleporter-popup-teleport-cancel-time = It's still recharging!
swap-teleporter-popup-teleport-cancel-link = It's not linked with another device!
swap-teleporter-popup-teleport-other = {CAPITALIZE(THE($entity))} activates, and you find yourself somewhere else.

swap-teleporter-verb-destroy-link = Destroy Quantum Link

swap-teleporter-examine-link-present = [color=forestgreen]It is linked to another device.[/color] Alt-Click to break the quantum link.
swap-teleporter-examine-link-absent = [color=yellow]It is not currently linked.[/color] Use on another device to establish a quantum link.
swap-teleporter-examine-time-remaining = Time left to recharge: [color=purple]{$second} second{$second ->
    [one].
    *[other]s.
}[/color]

