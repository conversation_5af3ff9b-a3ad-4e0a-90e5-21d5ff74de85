- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatFedoraBlack
  name: "чорний капелюх"
  description: "Чорний фетровий капелюх. Виглядає стильно."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/blackfedora.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/blackfedora.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatFedoraChoc
  name: "коричневий капелюх"
  description: "Коричневий капелюх. Виглядає шикарно."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/brownfedora.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/brownfedora.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatFedoraWhite
  name: "бі<PERSON>ий капелюх"
  description: "<PERSON><PERSON><PERSON>ий капелюх. Виглядає шикарно."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/whitefedora.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/whitefedora.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatFlatBlack
  name: "чорний плоский ковпачок"
  description: "Чорний кашкет, для водіння таксі та доставки газет."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/flatblack.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/flatblack.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatFlatBrown
  name: "коричневий плоский ковпачок"
  description: "Коричневий кашкет, для водіння таксі та доставки газет."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/flatbrown.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/flatbrown.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatFlatSyndie
  name: "чорний плоский ковпачок"
  suffix: Weapon
  description: "Чорний капелюшок, для надсилання повідомлення."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/flatblack.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/flatblack.rsi
  - type: MeleeWeapon
    attackRate: 1.5
    damage:
      types:
        Slash: 8

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatSurgcapBlack
  name: "хірургічна шапочка"
  description: "Чорна шапочка, яку носять хірурги під час операцій. Не дає їхньому волоссю лоскотати внутрішні органи."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/surgcap_black.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/surgcap_black.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatSurgcapCyan
  name: "хірургічна шапочка"
  description: "Шапочка блакитного кольору, яку хірурги одягають під час операцій. Не дає їхньому волоссю лоскотати ваші внутрішні органи."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/surgcap_cyan.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/surgcap_cyan.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatSurgcapCybersun
  name: "хірургічна шапочка"
  description: "Хірургічна шапочка, яку носять члени біотехнологічного підрозділу \"КіберСонця\"."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/surgcap_cybersun.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/surgcap_cybersun.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatSurgcapPink
  name: "хірургічна шапочка"
  description: "Рожева шапочка, яку носять хірурги під час операцій. Не дає їхньому волоссю лоскотати внутрішні органи."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/surgcap_pink.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/surgcap_pink.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatSurgcapRainbow
  name: "хірургічна шапочка"
  description: "Райдужна шапочка, яку хірурги одягають під час операцій. Не дає їх волоссю лоскотати внутрішні органи."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/surgcap_rainbow.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/surgcap_rainbow.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatSurgcapWhite
  name: "хірургічна шапочка"
  description: "Чорна шапочка, яку носять хірурги під час операцій. Не дає їхньому волоссю лоскотати внутрішні органи."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/surgcap_white.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/surgcap_white.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadCourier
  name: "кепка кур'єра"
  description: "Спеціальна доставка."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Soft/couriersoft.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Soft/couriersoft.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadCourierFlipped
  name: "кур'єр зсунув кепку"
  description: "Спеціальна доставка."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Soft/couriersoft.rsi
    state: flipped-icon
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Soft/couriersoft.rsi
    equippedPrefix: flipped

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatDirCap
  name: "режисерський кашкет"
  description: "Зелений кашкет з емблемою директорського звання, що належить високопоставленому члену Центрального командування. Хто б це не був, він серйозно налаштований."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/dircap.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/dircap.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeretLogi
  name: "логістичний берет"
  description: "Для інтелігентного та старанного бухгалтера. Точно не революційний символ."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/beret_lo.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/beret_lo.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeretCorpsman
  name: "берет брігмедика"
  description: "Для тих, хто стежить за стилем, хто рятує життя в бою. А також для медичних працівників."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/beret_corpsman.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/beret_corpsman.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatCJToque
  name: "токе головного судді"
  description: "Стандартний суддівський капелюх. Перуки все одно старомодні."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/cj_toque.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/cj_toque.rsi

