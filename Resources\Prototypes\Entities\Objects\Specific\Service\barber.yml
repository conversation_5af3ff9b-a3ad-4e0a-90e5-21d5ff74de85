- type: entity
  id: BarberScissors
  name: "перукарські ножиці"
  description: "здатний перекроїти зачіску будь-якої стрижки на ваш смак."
  parent: BaseItem
  components:
  - type: Sprite
    sprite: Objects/Tools/scissors.rsi
    state: icon
  - type: MagicMirror
    addSlotTime: 20
    removeSlotTime: 20
    selectSlotTime: 20
    changeSlotTime: 20
  - type: ActivatableUI
    key: enum.MagicMirrorUiKey.Key
    inHandsOnly: true
    requireActiveHand: true
  - type: UserInterface
    interfaces:
      enum.MagicMirrorUiKey.Key:
        type: MagicMirrorBoundUserInterface
  - type: MeleeWeapon
    wideAnimationRotation: -135
    attackRate: 1
    damage:
      types:
        Piercing: 6
    soundHit:
      path: "/Audio/Weapons/bladeslice.ogg"
  - type: DamageOtherOnHit
  - type: EmbeddableProjectile
    sound: /Audio/Weapons/star_hit.ogg
  - type: EmbedPassiveDamage
  - type: ThrowingAngle
    angle: 225
