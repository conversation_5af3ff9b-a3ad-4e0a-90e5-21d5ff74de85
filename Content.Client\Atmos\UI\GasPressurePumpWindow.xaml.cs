using System;
using System.Collections.Generic;
using System.Globalization;
using Content.Client.Atmos.EntitySystems;
using Content.Shared.Atmos;
using Content.Shared.Atmos.Prototypes;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Localization;

namespace Content.Client.Atmos.UI
{
    /// <summary>
    /// Client-side UI used to control a gas pressure pump.
    /// </summary>
    [GenerateTypedNameReferences]
    public sealed partial class GasPressurePumpWindow : DefaultWindow
    {
        public bool PumpStatus = true;

        public event Action? ToggleStatusButtonPressed;
        public event Action<string>? PumpOutputPressureChanged;

        public GasPressurePumpWindow()
        {
            RobustXamlLoader.Load(this);

            ToggleStatusButton.OnPressed += _ => SetPumpStatus(!PumpStatus);
            ToggleStatusButton.OnPressed += _ => ToggleStatusButtonPressed?.Invoke();

            PumpPressureOutputInput.OnTextChanged += _ => SetOutputPressureButton.Disabled = false;
            SetOutputPressureButton.OnPressed += _ =>
            {
                PumpOutputPressureChanged?.Invoke(PumpPressureOutputInput.Text ??= "");
                SetOutputPressureButton.Disabled = true;
            };

            SetMaxPressureButton.OnPressed += _ =>
            {
                PumpPressureOutputInput.Text = Atmospherics.MaxOutputPressure.ToString(CultureInfo.CurrentCulture);
                SetOutputPressureButton.Disabled = false;
            };
        }

        public void SetOutputPressure(float pressure)
        {
            PumpPressureOutputInput.Text = pressure.ToString(CultureInfo.CurrentCulture);
        }

        public void SetPumpStatus(bool enabled)
        {
            PumpStatus = enabled;
            if (enabled)
            {
                ToggleStatusButton.Text = Loc.GetString("comp-gas-pump-ui-status-enabled");
            }
            else
            {
                ToggleStatusButton.Text = Loc.GetString("comp-gas-pump-ui-status-disabled");
            }
        }
    }
}
