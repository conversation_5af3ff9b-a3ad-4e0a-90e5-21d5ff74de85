- type: entity
  id: PartShadowkin
  parent: BaseItem
  name: "частина тіла Шедовкіна"
  abstract: true
  components:
    - type: Sprite
      netsync: false
      sprite: Mobs/Species/Shadowkin/parts.rsi
    - type: Icon
      sprite: Mobs/Species/Shadowkin/parts.rsi
    - type: Damageable
      damageContainer: OrganicPart # Shitmed
    - type: BodyPart
    - type: ContainerContainer
      containers:
        bodypart: !type:Container
          ents: []

- type: entity
  id: TorsoShadowkin
  name: "тулуб Шедовкіна"
  parent: PartShadowkin
  components:
    - type: Sprite
      state: "torso_m"
    - type: Icon
      state: "torso_m"
    - type: BodyPart
      partType: Torso

- type: entity
  id: HeadShadowkin
  name: "голова Шедовкіна"
  parent: PartShadowkin
  components:
    - type: Sprite
      state: "head_m"
    - type: Icon
      state: "head_m"
    - type: BodyPart
      partType: Head
    - type: Input
      context: "ghost"
    - type: MovementSpeedModifier
      baseWalkSpeed: 0
      baseSprintSpeed: 0
    - type: InputMover
    - type: GhostOnMove

- type: entity
  id: LeftArmShadowkin
  name: "ліва рука Шедовкіна"
  parent: PartShadowkin
  components:
    - type: Sprite
      state: "l_arm"
    - type: Icon
      state: "l_arm"
    - type: BodyPart
      partType: Arm
      symmetry: Left

- type: entity
  id: RightArmShadowkin
  name: "права рука Шедовкіна"
  parent: PartShadowkin
  components:
    - type: Sprite
      state: "r_arm"
    - type: Icon
      state: "r_arm"
    - type: BodyPart
      partType: Arm
      symmetry: Right

- type: entity
  id: LeftHandShadowkin
  name: "ліва кисть Шедовкіна"
  parent: PartShadowkin
  components:
    - type: Sprite
      state: "l_hand"
    - type: Icon
      state: "l_hand"
    - type: BodyPart
      partType: Hand
      symmetry: Left

- type: entity
  id: RightHandShadowkin
  name: "права кисть Шедовкіна"
  parent: PartShadowkin
  components:
    - type: Sprite
      state: "r_hand"
    - type: Icon
      state: "r_hand"
    - type: BodyPart
      partType: Hand
      symmetry: Right

- type: entity
  id: LeftLegShadowkin
  name: "ліва нога Шедовкіна"
  parent: PartShadowkin
  components:
    - type: Sprite
      state: "l_leg"
    - type: Icon
      state: "l_leg"
    - type: BodyPart
      partType: Leg
      symmetry: Left
    - type: MovementBodyPart

- type: entity
  id: RightLegShadowkin
  name: "права нога Шедовкіна"
  parent: PartShadowkin
  components:
    - type: Sprite
      state: "r_leg"
    - type: Icon
      state: "r_leg"
    - type: BodyPart
      partType: Leg
      symmetry: Right
    - type: MovementBodyPart

- type: entity
  id: LeftFootShadowkin
  name: "ліва ступня Шедовкіна"
  parent: PartShadowkin
  components:
    - type: Sprite
      state: "l_foot"
    - type: Icon
      state: "l_foot"
    - type: BodyPart
      partType: Foot
      symmetry: Left

- type: entity
  id: RightFootShadowkin
  name: "права ступня Шедовкіна"
  parent: PartShadowkin
  components:
    - type: Sprite
      state: "r_foot"
    - type: Icon
      state: "r_foot"
    - type: BodyPart
      partType: Foot
      symmetry: Right
