- type: entity
  abstract: true
  id: BaseAnomaly
  name: "аномалія"
  description: "Неможливий об'єкт. Чи варто стояти так близько до нього?"
  components:
  - type: Anomaly
    pulseSound:
      collection: RadiationPulse
      params:
        volume: 5
    anomalyContactDamage:
      types:
        Radiation: 10
  - type: AmbientSound
    range: 5
    volume: -5
    sound:
      path: /Audio/Ambience/anomaly_drone.ogg
  - type: Transform
    anchored: false
  - type: Physics
    bodyType: Static
    bodyStatus: InAir
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 50
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Sprite
    noRot: true
    drawdepth: Effects #it needs to draw over stuff.
    sprite: Structures/Specific/anomaly.rsi
  - type: InteractionOutline
  - type: Clickable
  - type: Damageable
  - type: Appearance
  - type: AnimationPlayer
  - type: GuideHelp
    guides:
    - AnomalousResearch
  - type: EmitSoundOnSpawn
    sound:
      path: /Audio/Effects/teleport_arrival.ogg
  - type: Psionic
    removable: false
    roller: false
  - type: InnatePsionicPowers
    powersToAdd:
      - TelepathyPower
  - type: GlimmerSource
    active: false
  - type: SecretDataAnomaly
    randomStartSecretMin: 0
    randomStartSecretMax: 2

- type: entity
  id: AnomalyPyroclastic
  parent: BaseAnomaly
  suffix: Pyroclastic
  components:
  - type: AmbientSound
    volume: 5
    range: 5
    sound:
      path: /Audio/Ambience/Objects/fireplace.ogg
  - type: Anomaly
    corePrototype: AnomalyCorePyroclastic
    coreInertPrototype: AnomalyCorePyroclasticInert
  - type: Sprite
    sprite: Structures/Specific/Anomalies/pyro_anom.rsi
    layers:
    - state: anom
      map: ["enum.AnomalyVisualLayers.Base"]
    - state: pulse
      map: ["enum.AnomalyVisualLayers.Animated"]
      visible: false
  - type: PointLight
    radius: 6.0
    energy: 7.5
    color: "#E25822"
    castShadows: false
  - type: PyroclasticAnomaly
  - type: TempAffectingAnomaly
    tempChangePerSecond: 25
    hotspotExposeTemperature: 1000
  - type: GasProducerAnomaly
    releasedGas: 3
    releaseOnMaxSeverity: true
    spawnRadius: 4
    tileCount: 5
    tempChange: 420
  - type: ProjectileAnomaly
    projectilePrototype: ProjectileAnomalyFireball
    projectileSpeed: 0.5
    minProjectiles: 3
    maxProjectiles: 6
  - type: IgnitionSource
    temperature: 800
    ignited: true
  - type: IgniteOnCollide
    fixtureId: fix1
    fireStacks: 1
  - type: Psionic
    removable: false
    roller: false
    assayFeedback:
      - anomaly-pyroclastic-feedback

- type: entity
  id: AnomalyGravity
  parent: BaseAnomaly
  suffix: Gravity
  components:
  - type: Anomaly
    corePrototype: AnomalyCoreGravity
    coreInertPrototype: AnomalyCoreGravityInert
  - type: Sprite
    layers:
    - state: anom2
      map: ["enum.AnomalyVisualLayers.Base"]
    - state: anom2-pulse
      map: ["enum.AnomalyVisualLayers.Animated"]
      visible: false
  - type: PointLight
    radius: 5.0
    energy: 20
    color: "#1e070e"
    castShadows: false
  - type: GravityAnomaly
  - type: GravityWell
  - type: RadiationSource
  - type: Physics
    bodyType: Dynamic
    bodyStatus: InAir
  - type: CanMoveInAir
  - type: RandomWalk
  - type: SingularityDistortion
    intensity: 1000
    falloffPower: 2.7
  - type: Psionic
    removable: false
    roller: false
    assayFeedback:
      - anomaly-gravity-feedback

- type: entity
  id: AnomalyElectricity
  parent: BaseAnomaly
  suffix: Electricity
  components:
  - type: Anomaly
    corePrototype: AnomalyCoreElectricity
    coreInertPrototype: AnomalyCoreElectricityInert
  - type: Sprite
    layers:
    - state: anom3
      map: ["enum.AnomalyVisualLayers.Base"]
    - state: anom3-pulse
      map: ["enum.AnomalyVisualLayers.Animated"]
      visible: false
  - type: PointLight
    radius: 2.0
    energy: 5.0
    color: "#ffffaa"
    castShadows: false
  - type: ElectricityAnomaly
  - type: Electrified
  - type: Psionic
    removable: false
    roller: false
    assayFeedback:
      - anomaly-electricity-feedback

- type: entity
  id: AnomalyFlesh
  parent: BaseAnomaly
  suffix: Flesh
  components:
  - type: Anomaly
    corePrototype: AnomalyCoreFlesh
    coreInertPrototype: AnomalyCoreFleshInert
    minPulseLength: 180
    maxPulseLength: 300
  - type: Sprite
    layers:
    - state: anom5
      map: ["enum.AnomalyVisualLayers.Base"]
    - state: anom5-pulse
      map: ["enum.AnomalyVisualLayers.Animated"]
      visible: false
  - type: PointLight
    radius: 2.0
    energy: 7.5
    color: "#cb5b7e"
    castShadows: false
  - type: TileSpawnAnomaly
    entries:
    - settings:
        spawnOnPulse: true
        spawnOnStabilityChanged: true
        minAmount: 3
        maxAmount: 7
        maxRange: 4
      floor: FloorFlesh
    - settings:
        spawnOnSuperCritical: true
        minAmount: 10
        maxAmount: 30
        maxRange: 10
      floor: FloorFlesh
  - type: EntitySpawnAnomaly
    entries:
    - settings:
        spawnOnPulse: true
        minAmount: 1
        maxAmount: 4
        minRange: 1.5
        maxRange: 2.5
      spawns:
      - FleshBlocker
    - settings:
        spawnOnPulse: true
        maxAmount: 3
        minRange: 3
        maxRange: 4.5
      spawns:
      - MobFleshJared
      - MobFleshGolem
      - MobFleshClamp
      - MobFleshLover
    - settings:
        spawnOnSuperCritical: true
        minAmount: 10
        maxAmount: 15
        minRange: 5
        maxRange: 15
      spawns:
      - FleshBlocker
    - settings:
        spawnOnSuperCritical: true
        minAmount: 5
        maxAmount: 10
        maxRange: 8
      spawns:
      - MobFleshJared
      - MobFleshGolem
      - MobFleshClamp
      - MobFleshLover
    - settings:
        spawnOnSuperCritical: true
        minAmount: 5
        maxAmount: 8
        maxRange: 10
      spawns:
      - FleshKudzu
    - settings:
        spawnOnShutdown: true
        maxAmount: 2
        maxRange: 1
      spawns:
      - MobFleshJared
      - MobFleshGolem
      - MobFleshClamp
      - MobFleshLover
      - FleshKudzu
  - type: Psionic
    removable: false
    roller: false
    assayFeedback:
      - anomaly-flesh-feedback

- type: entity
  id: AnomalyBluespace
  parent: BaseAnomaly
  suffix: Bluespace
  components:
  - type: Sprite
    layers:
    - state: anom4
      map: ["enum.AnomalyVisualLayers.Base"]
    - state: anom4-pulse
      map: ["enum.AnomalyVisualLayers.Animated"]
      visible: false
  - type: PointLight
    radius: 2.0
    energy: 7.5
    color: "#00ccff"
    castShadows: false
  - type: BluespaceAnomaly
  - type: Portal
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 50
        mask:
        - MobMask
        layer:
        - MobLayer
      portalFixture:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.48,0.25,0.48"
        mask:
        - FullTileMask
        layer:
        - WallLayer
        hard: false
  - type: Anomaly
    corePrototype: AnomalyCoreBluespace
    coreInertPrototype: AnomalyCoreBluespaceInert
    pulseSound:
      collection: RadiationPulse
      params:
        volume: 5
    anomalyContactDamage:
      types:
        Radiation: 10
  - type: Psionic
    removable: false
    roller: false
    assayFeedback:
      - anomaly-bluespace-feedback

- type: entity
  id: AnomalyIce
  parent: BaseAnomaly
  suffix: Ice
  components:
  - type: Sprite
    sprite: Structures/Specific/Anomalies/ice_anom.rsi
    layers:
    - state: anom
      map: ["enum.AnomalyVisualLayers.Base"]
    - state: pulse
      map: ["enum.AnomalyVisualLayers.Animated"]
      visible: false
  - type: PointLight
    radius: 2.0
    energy: 2.5
    color: "#befaff"
    castShadows: false
  - type: Anomaly
    corePrototype: AnomalyCoreIce
    coreInertPrototype: AnomalyCoreIceInert
    anomalyContactDamage:
      types:
        Cold: 10
  - type: ExplosionAnomaly
    supercriticalExplosion: Cryo
    explosionTotalIntensity: 300
    explosionDropoff: 2
    explosionMaxTileIntensity: 20
  - type: ProjectileAnomaly
    projectilePrototype: ProjectileIcicle
  - type: EntitySpawnAnomaly
    entries:
    - settings:
        spawnOnStabilityChanged: true
        minAmount: 5
        maxAmount: 15
        maxRange: 4
      spawns:
      - IceCrust
  - type: TempAffectingAnomaly
    tempChangePerSecond: -25
    hotspotExposeTemperature: -1000
  - type: GasProducerAnomaly
    releasedGas: 8 # Frezon. Please replace if there is a better way to specify this
    releaseOnMaxSeverity: true
    spawnRadius: 0
  - type: Psionic
    removable: false
    roller: false
    assayFeedback:
      - anomaly-ice-feedback

- type: entity
  id: AnomalyRockBase
  parent: BaseAnomaly
  abstract: true
  suffix: Rock
  components:
  - type: Anomaly
    corePrototype: AnomalyCoreRock
    coreInertPrototype: AnomalyCoreRockInert
    minPulseLength: 180
    maxPulseLength: 300
  - type: Sprite
    layers:
    - state: anom6
      map: ["enum.AnomalyVisualLayers.Base"]
    - state: anom6-pulse
      map: ["enum.AnomalyVisualLayers.Animated"]
      visible: false
  - type: PointLight
    radius: 2.0
    energy: 7.5
    color: "#5ca8cb"
    castShadows: false
  - type: TileSpawnAnomaly
    entries:
    - settings:
        spawnOnPulse: true
        minAmount: 15
        maxAmount: 20
        maxRange: 7.5
      floor: FloorAsteroidTile
    - settings:
        spawnOnSuperCritical: true
        minAmount: 30
        maxAmount: 50
        maxRange: 12
      floor: FloorAsteroidTile
  - type: Psionic
    removable: false
    roller: false
    assayFeedback:
      - anomaly-rock-feedback

- type: entity
  id: AnomalyRockUranium
  parent: AnomalyRockBase
  suffix: Rock, Uranium
  components:
  - type: Sprite
    color: "#52ff39"
  - type: PointLight
    radius: 2.0
    energy: 7.5
    color: "#52ff39"
  - type: EntitySpawnAnomaly
    entries:
    - settings:
        spawnOnPulse: true
        minAmount: 8
        maxAmount: 15
        minRange: 4.5
        maxRange: 7.5
      spawns:
      - WallSpawnAsteroid
      - WallSpawnAsteroid
      - WallSpawnAsteroidUranium
      - WallSpawnAsteroidUraniumCrab
    - settings:
        spawnOnPulse: true
        maxAmount: 3
        minRange: 2.5
        maxRange: 4.5
      spawns:
      - CrystalGreen
    - settings:
        spawnOnSuperCritical: true
        minAmount: 30
        maxAmount: 40
        minRange: 5
        maxRange: 15
      spawns:
      - CrystalGreen
      - WallSpawnAsteroid
      - WallSpawnAsteroid
      - WallSpawnAsteroidUraniumCrab
    - settings:
        spawnOnSuperCritical: true
        minAmount: 6
        maxAmount: 10
        maxRange: 5
      spawns:
      - MobSpawnCrabUranium

- type: entity
  id: AnomalyRockQuartz
  parent: AnomalyRockBase
  suffix: Rock, Quartz
  components:
  - type: Sprite
    color: "#fb4747"
  - type: PointLight
    radius: 2.0
    energy: 7.5
    color: "#fb4747"
  - type: EntitySpawnAnomaly
    entries:
    - settings:
        spawnOnPulse: true
        minAmount: 8
        maxAmount: 15
        minRange: 4.5
        maxRange: 7.5
      spawns:
      - WallSpawnAsteroid
      - WallSpawnAsteroid
      - WallSpawnAsteroidQuartz
      - WallSpawnAsteroidQuartzCrab
    - settings:
        spawnOnPulse: true
        maxAmount: 3
        minRange: 2.5
        maxRange: 4.5
      spawns:
      - CrystalGrey
    - settings:
        spawnOnSuperCritical: true
        minAmount: 30
        maxAmount: 40
        minRange: 5
        maxRange: 15
      spawns:
      - CrystalGrey
      - WallSpawnAsteroid
      - WallSpawnAsteroid
      - WallSpawnAsteroidQuartzCrab
    - settings:
        spawnOnSuperCritical: true
        minAmount: 6
        maxAmount: 10
        maxRange: 5
      spawns:
      - MobSpawnCrabQuartz

- type: entity
  id: AnomalyRockSilver
  parent: AnomalyRockBase
  suffix: Rock, Silver
  components:
  - type: Sprite
    color: "#47f8ff"
  - type: PointLight
    radius: 2.0
    energy: 7.5
    color: "#47f8ff"
  - type: EntitySpawnAnomaly
    entries:
    - settings:
        spawnOnPulse: true
        minAmount: 8
        maxAmount: 15
        minRange: 4.5
        maxRange: 7.5
      spawns:
      - WallSpawnAsteroid
      - WallSpawnAsteroid
      - WallSpawnAsteroidSilver
      - WallSpawnAsteroidSilverCrab
    - settings:
        spawnOnPulse: true
        maxAmount: 3
        minRange: 2.5
        maxRange: 4.5
      spawns:
      - CrystalCyan
    - settings:
        spawnOnSuperCritical: true
        minAmount: 30
        maxAmount: 40
        minRange: 5
        maxRange: 15
      spawns:
      - CrystalCyan
      - WallSpawnAsteroid
      - WallSpawnAsteroid
      - WallSpawnAsteroidSilverCrab
    - settings:
        spawnOnSuperCritical: true
        minAmount: 6
        maxAmount: 10
        maxRange: 5
      spawns:
      - MobSpawnCrabSilver

- type: entity
  id: AnomalyRockIron
  parent: AnomalyRockBase
  suffix: Rock, Iron
  components:
  - type: Sprite
    color: "#ff8227"
  - type: PointLight
    radius: 2.0
    energy: 7.5
    color: "#ff8227"
  - type: EntitySpawnAnomaly
    entries:
    - settings:
        spawnOnPulse: true
        minAmount: 8
        maxAmount: 15
        minRange: 4.5
        maxRange: 7.5
      spawns:
      - WallSpawnAsteroid
      - WallSpawnAsteroid
      - WallSpawnAsteroidIron
      - WallSpawnAsteroidIronCrab
    - settings:
        spawnOnPulse: true
        maxAmount: 3
        minRange: 2.5
        maxRange: 4.5
      spawns:
      - CrystalOrange
    - settings:
        spawnOnSuperCritical: true
        minAmount: 30
        maxAmount: 40
        minRange: 5
        maxRange: 15
      spawns:
      - CrystalOrange
      - WallSpawnAsteroid
      - WallSpawnAsteroid
      - WallSpawnAsteroidIronCrab
    - settings:
        spawnOnSuperCritical: true
        minAmount: 6
        maxAmount: 10
        maxRange: 5
      spawns:
      - MobSpawnCrabIron

- type: entity
  id: AnomalyFlora
  parent: BaseAnomaly
  suffix: Flora
  components:
  - type: Sprite
    drawdepth: Mobs
    sprite: Structures/Specific/Anomalies/flora_anom.rsi
    layers:
    - state: anom
      map: ["enum.AnomalyVisualLayers.Base"]
    - state: pulse
      map: ["enum.AnomalyVisualLayers.Animated"]
      visible: false
  - type: PointLight
    radius: 8.0
    energy: 8.5
    color: "#6270bb"
  - type: Anomaly
    animationTime: 6
    offset: 0, 0
    corePrototype: AnomalyCoreFlora
    coreInertPrototype: AnomalyCoreFloraInert
    minPulseLength: 60
    maxPulseLength: 120
    anomalyContactDamage:
      types:
        Slash: 0
  - type: TileSpawnAnomaly
    entries:
    - settings:
        spawnOnPulse: true
        minAmount: 3
        maxAmount: 7
        maxRange: 5
      floor: FloorAstroGrass
    - settings:
        spawnOnSuperCritical: true
        minAmount: 10
        maxAmount: 30
        maxRange: 15
      floor: FloorAstroGrass
  - type: EntitySpawnAnomaly
    entries:
    - settings:
        spawnOnPulse: true
        minAmount: 2
        maxAmount: 5
        maxRange: 2
      spawns:
      - KudzuFlowerFriendly
    - settings:
        spawnOnSuperCritical: true
        minAmount: 5
        maxAmount: 10
        maxRange: 6
      spawns:
      - KudzuFlowerAngry
  - type: Psionic
    removable: false
    roller: false
    assayFeedback:
      - anomaly-flora-feedback

- type: entity
  id: AnomalyFloraBulb
  name: "сяюча дивна ягода"
  parent: BaseStructure
  description: "Це красива дивна ягода, що світиться. Здається, всередині неї щось росте..."
  suffix: Flora Anomaly
  components:
  - type: Transform
    anchored: true
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.2
  - type: InteractionOutline
  - type: Damageable
    damageContainer: Biological
    damageModifierSet: Diona
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 1
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
          - !type:SpawnEntitiesBehavior
            spawn:
              EffectAnomalyFloraBulb:
                min: 1
                max: 1
  - type: PointLight
    radius: 2.0
    energy: 4.5
    color: "#6270bb"
  - type: Sprite
    noRot: true
    sprite: Structures/Specific/Anomalies/flora_anom.rsi
    state: bulb

- type: entity
  id: AnomalyLiquid
  parent: BaseAnomaly
  suffix: Liquid
  components:
  - type: Sprite
    sprite: Structures/Specific/Anomalies/liquid_anom.rsi
    layers:
    - state: anom
      map: ["enum.AnomalyVisualLayers.Base"]
    - state: pulse
      map: ["enum.AnomalyVisualLayers.Animated"]
      visible: false
  - type: RandomSprite
    selected: # Initialized layer values. Edited through the ReagentProducerAnomalySystem
      enum.AnomalyVisualLayers.Base:
        anom: "#ffffff"
      enum.AnomalyVisualLayers.Animated:
        pulse: "#ffffff"
  - type: PointLight
    radius: 4.0
    energy: 3.5
    color: "#bbbbbb"
  - type: BadFood
  - type: Anomaly
    corePrototype: AnomalyCoreLiquid
    coreInertPrototype: AnomalyCoreLiquidInert
    minPulseLength: 60
    maxPulseLength: 120
    anomalyContactDamage:
      types:
        Slash: 1
  - type: EntitySpawnAnomaly
    entries:
    - settings:
        spawnOnSuperCritical: true
        minAmount: 3
        maxAmount: 8
        maxRange: 2
      spawns:
      - ReagentSlimeSpawner
  - type: SolutionContainerManager
    solutions:
      anomaly:
        maxVol: 1500
  - type: PuddleCreateAnomaly
    solution: anomaly
  - type: InjectionAnomaly
    solution: anomaly
    superCriticalInjectRadius: 10
  - type: ReagentProducerAnomaly
    solution: anomaly
    needRecolor: true
    dangerousChemicals:
    - UnstableMutagen
    - Mold
    - PolytrinicAcid
    - FerrochromicAcid
    - FluorosulfuricAcid
    - SulfuricAcid
    - HeartbreakerToxin
    - VentCrud
    - UncookedAnimalProteins
    - Thermite
    - Napalm
    - Phlogiston
    - ChlorineTrifluoride
    - FoamingAgent
    - BuzzochloricBees
    - RobustHarvest
    # Goobstation - Start
    - Triclor
    - Cyanide
    - Genecide
    usefulChemicals:
    - Cryptobiolin
    - Dylovene
    - Arithrazine
    - Bicaridine
    - Cryoxadone
    - Dermaline
    - Dexalin
    - DexalinPlus
    - Epinephrine
    - Leporazine
    - Ambuzol
    - Tricordrazine
    - Artifexium
    - Ethylredoxrazine
    # Goobstation - Start
    - Probital
    - Tirimol
    - Multiver
    funChemicals:
    - Desoxyephedrine
    - Ephedrine
    - THC
    - SpaceDrugs
    - Nocturine
    - MuteToxin
    - NorepinephricAcid
    - Pax
    - Ipecac
    - Cognizine
    - Beer
    - SpaceGlue
    - SpaceLube
    - CogChamp
    - Honk
    - Carpetium
    - JuiceThatMakesYouWeh
  - type: Drink
    solution: anomaly
  - type: DrainableSolution
    solution: anomaly
  - type: DrawableSolution
    solution: anomaly
  - type: ExaminableSolution
    solution: anomaly
  - type: RefillableSolution
    solution: anomaly
  - type: InjectableSolution
    solution: beaker
  - type: Psionic
    removable: false
    roller: false
    assayFeedback:
      - anomaly-liquid-feedback

- type: entity
  id: AnomalyShadow
  parent: BaseAnomaly
  suffix: Shadow
  components:
  - type: Sprite
    sprite: Structures/Specific/Anomalies/shadow_anom.rsi
    layers:
    - state: anom
      map: ["enum.AnomalyVisualLayers.Base"]
    - state: pulse
      map: ["enum.AnomalyVisualLayers.Animated"]
      visible: false
  - type: PointLight
    radius: 1.5
    energy: 12.5
    color: "#793a80"
  - type: AmbientSound
    range: 5
    volume: -5
    sound:
      path: /Audio/Ambience/anomaly_scary.ogg
  - type: Anomaly
    corePrototype: AnomalyCoreShadow
    coreInertPrototype: AnomalyCoreShadowInert
    minPulseLength: 60
    maxPulseLength: 120
    anomalyContactDamage:
      types:
        Cold: 10
    animationTime: 4
    offset: "-0.1,0.1"
  - type: EntitySpawnAnomaly
    entries:
    - settings:
        spawnOnPulse: true
        spawnOnSuperCritical: true
        minAmount: 10
        maxAmount: 20
        maxRange: 4
      spawns:
      - ShadowKudzuWeak
    - settings:
        spawnOnSuperCritical: true
        minAmount: 30
        maxAmount: 40
        maxRange: 50
      spawns:
      - ShadowKudzu
  - type: Portal
    arrivalSound: /Audio/Items/hiss.ogg
    departureSound: /Audio/Items/hiss.ogg
  - type: Tag
    tags:
      - SpookyFog
  - type: Psionic
    removable: false
    roller: false
    assayFeedback:
      - anomaly-shadow-feedback
