using Content.Client.Resources;
using Robust.Client.AutoGenerated;
using Robust.Client.Graphics;
using Robust.Client.ResourceManagement;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Utility;

namespace Content.Client.Replay.UI.Loading;

[GenerateTypedNameReferences]
public sealed partial class LoadingScreenControl : Control
{
    public static SpriteSpecifier Sprite = new SpriteSpecifier.Rsi(new ("/Textures/Mobs/Silicon/Bots/mommi.rsi"), "wiggle");

    public LoadingScreenControl(IResourceCache resCache)
    {
        RobustXamlLoader.Load(this);

        LayoutContainer.SetAnchorPreset(this, LayoutContainer.LayoutPreset.Wide);
        Header.FontOverride = resCache.GetFont("/Fonts/NotoSansDisplay/NotoSansDisplay-Bold.ttf", 24);
        Subtext.FontOverride = resCache.GetFont("/Fonts/NotoSansDisplay/NotoSansDisplay-Bold.ttf", 12);

        SpriteLeft.SetFromSpriteSpecifier(Sprite);
        SpriteRight.SetFromSpriteSpecifier(Sprite);
        SpriteLeft.HorizontalAlignment = HAlignment.Stretch;
        SpriteLeft.VerticalAlignment = VAlignment.Stretch;
        SpriteLeft.DisplayRect.Stretch = TextureRect.StretchMode.KeepAspectCentered;
        SpriteRight.DisplayRect.Stretch = TextureRect.StretchMode.KeepAspectCentered;

        Background.PanelOverride = new StyleBoxFlat()
        {
            BackgroundColor = Color.FromHex("#303033"),
            BorderColor = Color.FromHex("#5a5a5a"),
            BorderThickness = new Thickness(4)
        };
    }
}
