#debug
- type: storeCategory
  id: Debug
  name: store-category-debug

- type: storeCategory
  id: Debug2
  name: store-category-debug2

#WIZARD
- type: storeCategory
  id: SpellbookOffensive
  name: store-caregory-spellbook-offensive
  priority: 0

- type: storeCategory
  id: SpellbookDefensive
  name: store-caregory-spellbook-defensive
  priority: 1

- type: storeCategory
  id: SpellbookUtility
  name: store-caregory-spellbook-utility
  priority: 2

- type: storeCategory
  id: SpellbookEquipment
  name: store-caregory-spellbook-equipment
  priority: 3

- type: storeCategory
  id: SpellbookEvents
  name: store-caregory-spellbook-events
  priority: 4

# Goobstation
- type: storeCategory
  id: SpellbookSummons
  name: store-caregory-spellbook-summons
  priority: 5

# Goobstation
- type: storeCategory
  id: SpellbookSpecial
  name: store-caregory-spellbook-special
  priority: 6

# Goobstation
- type: storeCategory
  id: SpellbookBundles
  name: store-caregory-spellbook-bundles
  priority: 7

#uplink categoires
- type: storeCategory
  id: UplinkWeaponry
  name: store-category-weapons
  priority: 0

- type: storeCategory
  id: UplinkAmmo
  name: store-category-ammo
  priority: 1

- type: storeCategory
  id: UplinkExplosives
  name: store-category-explosives
  priority: 2

- type: storeCategory
  id: UplinkWearables
  name: store-category-wearables
  priority: 3

- type: storeCategory
  id: UplinkChemicals
  name: store-category-chemicals
  priority: 4

- type: storeCategory
  id: UplinkDeception
  name: store-category-deception
  priority: 5

- type: storeCategory
  id: UplinkDisruption
  name: store-category-disruption
  priority: 6

- type: storeCategory
  id: UplinkImplants
  name: store-category-implants
  priority: 7

- type: storeCategory
  id: UplinkAllies
  name: store-category-allies
  priority: 8

- type: storeCategory
  id: UplinkJob
  name: store-category-job
  priority: 9

- type: storeCategory
  id: UplinkPointless
  name: store-category-pointless
  priority: 10

- type: storeCategory
  id: UplinkSales
  name: "Продажі"
  priority: 11

#revenant
- type: storeCategory
  id: RevenantAbilities
  name: store-category-abilities

- type: storeCategory
  id: DiscountedItems
  name: store-discounted-items
  priority: 200
  
#PIRATE START UNTIL END OF FILE
- type: storeCategory
  id: MageAbilities
  name: store-category-abilities
  priority: 0

- type: storeCategory
  id: MageUtility
  name: store-category-utility
  priority: 1