- type: entity
  id: BaseSecretDoor
  parent: BaseStructure
  name: "суцільна стіна" # No meta
  suffix: secret door
  abstract: true
  description: "Утримує повітря всередині та грейтайдів зовні."
  components:
  - type: Sprite
    sprite: Structures/Doors/secret_door.rsi
    layers:
    - state: closed
      map: ["enum.DoorVisualLayers.Base"]
  - type: AnimationPlayer
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.49,-0.49,0.49,0.49"
        density: 100
        mask:
        - FullTileMask
        layer:
        - WallLayer
  - type: Door
    bumpOpen: false
    clickOpen: true
    canCrush: false
    closeTimeOne: 0.2
    closeTimeTwo: 0.6
    openTimeOne: 0.6
    openTimeTwo: 0.2
    openingAnimationTime: 1.2
    closingAnimationTime: 1.2
  - type: Appearance
  - type: Weldable
    time: 2
  - type: Airtight
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: RCDDeconstructable
    cost: 6
    delay: 8
    fx: EffectRCDDeconstruct8
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: IconSmooth
    key: walls
    mode: NoSprite
  - type: Occluder
  - type: ContainerFill
    containers:
      battery-container: [ PowerCellMedium ]
  - type: Tag
    tags:
    - Structure
    - Wall
  - type: ContainerContainer
    containers:
      battery-container: !type:Container
  - type: BlockWeather

- type: entity
  id: BaseSecretDoorAssembly
  name: "потайні двері в зборі"
  description: "Він відкривається, закривається і, можливо, розчавить вас."
  components:
  - type: Clickable
  - type: InteractionOutline
  - type: Sprite
    sprite: Structures/Doors/secret_door.rsi
    state: assembly
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.45"
        density: 110
        mask:
        - FullTileMask
        layer:
        - HumanoidBlockLayer
  - type: Anchorable
    delay: 2
  - type: Pullable
  - type: Transform
    anchored: true
    noRot: false
  - type: Rotatable
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: RCDDeconstructable
    cost: 6
    delay: 8
    fx: EffectRCDDeconstruct8
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Construction
    graph: SecretDoor
    node: assembly
  placement:
    mode: SnapgridCenter

- type: entity
  id: SolidSecretDoor
  name: "суцільна стіна"
  parent: BaseSecretDoor
  components:
  - type: Construction
    graph: SecretDoor
    node: solidSecretDoor
    containers:
    - battery-container
