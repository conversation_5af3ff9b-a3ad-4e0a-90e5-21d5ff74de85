ent-ClothingHeadHelmetHardsuitBasic = basic vacsuit helmet
    .desc = A common vacsuit helmet that provides little more than basic protection from vacuum.
ent-ClothingHeadHelmetHardsuitAtmos = HpI-19t helmet
    .desc = The Fotia's standard helmet, it features the same heat protection as the suit, along with some integrated protective gear for the wearer's head.
ent-ClothingHeadHelmetHardsuitEngineering = HpI-19r helmet
    .desc = The Lampsi's standard helmet, it features the same radiation protection as the suit, along with some integrated protective gear for the wearer's head.
ent-ClothingHeadHelmetHardsuitEngineeringUnpainted = HpI-19r helmet
    .desc = { ent-ClothingHeadHelmetHardsuitEngineering.desc }
ent-ClothingHeadHelmetHardsuitSpatio = HpI-20s helmet
    .desc = A lightweight helmet designed for the Kriti hardsuit, it allows for better mobility, along with some protection against radiation.
ent-ClothingHeadHelmetHardsuitSalvage = HpI-20a helmet
    .desc = A bulky helmet designed for the Lavrion hardsuit, it has reinforced plating to protect the wearer's head in wildlife encounters.
ent-ClothingHeadHelmetHardsuitVoidParamed = ZhP-24m helmet
    .desc = A lightweight helmet designed for use with the Sukunabikona hardsuit. Parts of the helmet are made of hard transparent plastics, allowing for better visibility.
ent-ClothingHeadHelmetHardsuitMaxim = mysterious helmet
    .desc = An old looking helmet, it seems rather sturdy and lightweight. It also has an ominous red and black color scheme.
ent-ClothingHeadHelmetHardsuitSecurity = FPA-83s helmet
    .desc = A bulky helmet deployed with the Baghatur tacsuit. Protects its wearer against ballistics and explosive ordinance, at the cost of some mobility.
ent-ClothingHeadHelmetHardsuitBrigmedic = FPA-84m helmet
    .desc = A bulky helmet deployed with the Tsagaan tacsuit. Protects its wearer against ballistics and explosive ordinance, at the cost of some mobility.
ent-ClothingHeadHelmetHardsuitCombatStandard = FPA-85 helmet
    .desc = A bulky helmet deployed with the Baghatur Mk.II tacsuit.
    Protects its wearer against ballistics and explosive ordinance, at the cost of some mobility.
ent-ClothingHeadHelmetHardsuitCombatOfficer = FPA-85s helmet
    .desc = { ent-ClothingHeadHelmetHardsuitCombatStandard.desc }
ent-ClothingHeadHelmetHardsuitCombatMedical = FPA-86 helmet
    .desc = A bulky helmet deployed with the Tsagaan Mk.II tacsuit.
    Protects its wearer against ballistics and explosive ordinance, at the cost of some mobility.
ent-ClothingHeadHelmetHardsuitCombatCorpsman = FPA-86m helmet
    .desc = { ent-ClothingHeadHelmetHardsuitCombatMedical.desc }
ent-ClothingHeadHelmetHardsuitWarden = FPA-92s helmet
    .desc = A modified riot-control helmet for use with the Sulde tacsuit. Offers better overall protection than standard tacsuits at the expense of mobility.
ent-ClothingHeadHelmetHardsuitCombatRiot = FPA-93 helmet
    .desc = A modified riot-control helmet for use with the Sulde Mk.II tacsuit.
    Offers better overall protection than other models at the expense of mobility.
ent-ClothingHeadHelmetHardsuitCombatWarden = FPA-93s helmet
    .desc = { ent-ClothingHeadHelmetHardsuitCombatRiot.desc }
ent-ClothingHeadHelmetHardsuitCap = NT-42c helmet
    .desc = A special helmet for the Tengri tacsuit, despite its lightweight appearance, it provides good all-around protection to its wearer.
ent-ClothingHeadHelmetHardsuitEngineeringWhite = HpI-24c helmet
    .desc = A high-grade helmet used with the Daedalus hardsuit, it has a lighter construction than its standard counterparts, along with more radiation protection.
ent-ClothingHeadHelmetHardsuitMedical = ZhP-25m helmet
    .desc = An extraordinarily lightweight helmet designed for use with the Okuninushi hardsuit.
    It is primarily made from transparent hard-plastics, providing complete freedom of vision.
ent-ClothingHeadHelmetHardsuitRd = NT-45e helmet
    .desc = A heavily armored helmet worn over the Sophia hardsuit. It boasts the same near-immunity to explosions, heat and radiation as the suit, but heavily restricts the wearer's mobility.
ent-ClothingHeadHelmetHardsuitMystagogue = NT-45e helmet
    .desc = { ent-ClothingHeadHelmetHardsuitRd.desc }
ent-ClothingHeadHelmetHardsuitSecurityRed = FPA-98s helmet
    .desc = A high quality helmet for the Dayicin tacsuit. It offers better overall protection than standard tacsuits without impacting mobility as much.
ent-ClothingHeadHelmetHardsuitCombatAdvanced = FPA-99 helmet
    .desc = A high quality helmet for the Dayicin Mk.II tacsuit.
    Features a lightweight construction, offering the same protection as a standard tacsuit without impacting mobility as much.
ent-ClothingHeadHelmetHardsuitCombatHoS = FPA-99s helmet
    .desc = { ent-ClothingHeadHelmetHardsuitCombatAdvanced.desc }
ent-ClothingHeadHelmetHardsuitLuxury = HpI-20c helmet
    .desc = A modified helmet for the Minos hardsuit, fashioned after the Logistics Officer's colors. It's been modified for greater mobility at the expense of physical trauma protection.
ent-ClothingHeadHelmetHardsuitSyndie = CSA-51a helmet
    .desc = An armored helmet deployed over a Shanlin tacsuit. This one has been painted blood red.
ent-ClothingHeadHelmetHardsuitShanlinUnpainted = CSA-51a helmet
    .desc = An armored helmet deployed over a Shanlin tacsuit. This one is unpainted bare metal.
ent-ClothingHeadHelmetHardsuitSyndieReverseEngineered = CSA-51a helmet
    .desc = An armored helmet deployed over a Shanlin tacsuit. This one has been painted blue.
ent-ClothingHeadHelmetHardsuitSyndieMedic = CSA-51m helmet
    .desc = An armored helmet deployed over a Zhongyao tacsuit. features optic integrations for nearly every medical hud on the market.
    Designed to enable the survival of combat medics in the most dangerous of environments.
ent-ClothingHeadHelmetHardsuitSyndieElite = CSA-54UA helmet
    .desc = An elite version of the Shanlin tacsuit's helmet, featuring improved armor and fireproofing.
ent-ClothingHeadHelmetHardsuitShiweiUnpainted = CSA-54UA helmet
    .desc = An elite version of the Shanlin tacsuit's helmet, featuring improved armor and fireproofing.
    It is unpainted bare spaceship alloy.
ent-ClothingHeadHelmetHardsuitSyndieCommander = CSA-54c helmet
    .desc = A bulked up version of the Shanlin tacsuit's helmet, purpose-built for commanders of special operation squads. This one has been painted blood-red.
ent-ClothingHeadHelmetHardsuitCybersun = CSA-80UA helmet
    .desc = An incredibly sturdy looking helmet designed for the Guan Yu tacsuit.
ent-ClothingHeadHelmetHardsuitJuggernautReverseEngineered = CSA-80UA helmet
    .desc = An incredibly sturdy looking helmet designed for the Guan Yu tacsuit. This one has been painted blue.
ent-ClothingHeadHelmetHardsuitCybersunStealth = CSA-91x helmet
    .desc = A moderately protective sealed helmet designed for the èguǐ tacsuit. It features "Cloaking" metamaterials.
ent-ClothingHeadHelmetHardsuitWizard = WZD-84 helmet
    .desc = A bizarre, gem-encrusted helmet from unknown origins. It provides some protection to its wearer without restricting their movements.
ent-ClothingHeadHelmetHardsuitLing = organic space helmet
    .desc = A spaceworthy biomass of pressure and temperature resistant tissue.
ent-ClothingHeadHelmetHardsuitPirateEVA = pirate helmet
    .desc = A pirate vacsuit helmet, very heavy but provides good protection.
    .suffix = Pirate
ent-ClothingHeadHelmetHardsuitPirateCap = pirate captain's tacsuit helmet
    .desc = A special hardsuit helmet, made for the captain of a pirate ship.
    .suffix = Pirate
ent-ClothingHeadHelmetHardsuitERTCentcomm = NT-444c helmet
    .desc = A special tacsuit helmet worn by Central Command Officers.
ent-ClothingHeadHelmetHardsuitERTLeader = NT-444l helmet
    .desc = A special tacsuit helmet worn by leaders of an emergency response team.
ent-ClothingHeadHelmetHardsuitERTEngineer = NT-444e helmet
    .desc = A special hardsuit helmet worn by members of an engineering emergency response team.
ent-ClothingHeadHelmetHardsuitERTMedical = NT-444m helmet
    .desc = A special hardsuit helmet worn by members of a medical emergency response team.
ent-ClothingHeadHelmetHardsuitERTSecurity = NT-444s helmet
    .desc = A special tacsuit helmet worn by members of a engineering emergency response team.
ent-ClothingHeadHelmetHardsuitERTJanitor = NT-444j helmet
    .desc = A special hardsuit helmet worn by members of a janitorial emergency response team.
ent-ClothingHeadHelmetCBURN = NT-444-CBRN helmet
    .desc = A pressure resistant and fireproof hood worn by special cleanup units.
ent-ClothingHeadHelmetHardsuitDeathsquad = NT-662ua helmet
    .desc = A highly advanced, top of the line helmet used in special operations.
ent-ClothingHeadHelmetHardsuitClown = clown vacsuit helmet
    .desc = A colorful clown vacsuit helmet. On closer inspection, it appears to be a normal helmet painted with crayons, and a clown mask glued on top.
ent-ClothingHeadHelmetHardsuitMime = mime vacsuit helmet
    .desc = A mime vacsuit helmet. On closer inspection, it appears to be a normal helmet painted with crayons, and a mime mask glued on top.
ent-ClothingHeadHelmetHardsuitSanta = DNK-31 helmet
    .desc = A festive-looking hardsuit helmet that provides the jolly gift-giver protection from low-pressure environments.
