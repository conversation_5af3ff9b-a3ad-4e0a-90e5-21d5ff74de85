- type: entity
  id: Incorporeal
  save: false
  abstract: true
  description: "Натовпи без фізичних тіл"
  components:
  - type: Sprite
    noRot: true
    overrideContainerOcclusion: true # Always show up regardless of where they're contained.
    drawdepth: Ghosts
  - type: FTLSmashImmune
  - type: CargoSellBlacklist
  - type: MovementSpeedModifier
    baseSprintSpeed: 12
    baseWalkSpeed: 8
  - type: MovementIgnoreGravity
  - type: Physics
    bodyType: KinematicController
    bodyStatus: InAir
  - type: CanMoveInAir
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 15
        layer:
        - GhostImpassable

- type: entity
  parent:
  - Incorporeal
  - BaseMob
  id: MobObserver
  name: "спостерігач"
  description: "Бу!"
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Mobs/Ghosts/ghost_human.rsi
    color: "#fff8"
    layers:
    - state: animated
      shader: unshaded
  - type: ContentEye
    maxZoom: 1.44,1.44
  - type: Eye
    drawFov: false
    visMask:
      - TelegnosticProjection
      - PsionicInvisibility
      - Ghost
      - Normal
      - Ethereal
  - type: Input
    context: "ghost"
  - type: Examiner
    skipChecks: true
  - type: Ghost
  - type: GhostHearing
  - type: ShowElectrocutionHUD
  - type: IntrinsicRadioReceiver
  - type: ActiveRadio
    receiveAllChannels: true
    globalReceive: true
  - type: Tag
    tags:
    - BypassInteractionRangeChecks
  - type: UniversalLanguageSpeaker # Ghosts should understand any language.
  - type: PointLight
    radius: 6
    enabled: false
  - type: UserInterface
    interfaces:
      enum.RevivalContractUiKey.Key: # Goobstation - Devil
        type: RevivalContractBoundUserInterface
        requireInputValidation: false


- type: entity
  id: ActionGhostBoo
  name: "Бу!"
  description: "Налякайте членів екіпажу нудьгою!"
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: Interface/Actions/scream.png
    checkCanInteract: false
    event: !type:BooActionEvent
    startDelay: true
    useDelay: 120

- type: entity
  id: ActionToggleLighting
  name: "Перемкнути Освітлення"
  description: "Перемкнути рендеринг світла для кращого огляду темних зон."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: Interface/VerbIcons/light.svg.192dpi.png
    clientExclusive: true
    checkCanInteract: false
    raiseOnUser: true # Goobstation
    event: !type:ToggleLightingActionEvent

- type: entity
  id: ActionToggleFov
  name: "Toggle FoV"
  description: "Перемикає поле зору, щоб бачити те, що бачать гравці."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: Interface/VerbIcons/vv.svg.192dpi.png
    clientExclusive: true
    checkCanInteract: false
    raiseOnUser: true # Goobstation
    event: !type:ToggleFoVActionEvent

- type: entity
  id: ActionToggleGhosts
  name: "Перемикання привидів"
  description: "Перемикання видимості інших привидів."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: { sprite: Mobs/Ghosts/ghost_human.rsi, state: icon }
    clientExclusive: true
    checkCanInteract: false
    raiseOnUser: true # Goobstation
    event: !type:ToggleGhostsActionEvent

- type: entity
  id: ActionToggleGhostHearing
  name: "Ввімкнути слух привидів"
  description: "Перемикайтеся між прослуховуванням усіх повідомлень і прослуховуванням лише радіо та повідомлень поблизу."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    checkCanInteract: false
    icon:
      sprite: Clothing/Ears/Headsets/base.rsi
      state: icon
    iconOn: Interface/Actions/ghostHearingToggled.png
    event: !type:ToggleGhostHearingActionEvent

- type: entity
  parent:
  - Incorporeal
  - BaseMob
  id: MobTelegnosisObserver
  name: "псіонічна проєкція"
  description: "Душа, що танцює крізь світло снів"
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Mobs/Ghosts/telegnostic_observer.rsi #Feel free to improve this!
    layers:
    - state: animated
      shader: unshaded
  - type: Eye
    drawFov: false
    visMask:
      - TelegnosticProjection
      - PsionicInvisibility
      - Normal
  - type: Examiner
  - type: MovementSpeedModifier
    baseSprintSpeed: 8
    baseWalkSpeed: 5
  - type: Psionic
    removable: false
    roller: false
    psychognomicDescriptors:
      - p-descriptor-liminal # because you arnt currently in your body
  - type: InnatePsionicPowers
    powersToAdd:
      - XenoglossyPower #Remove if a better way to have the projection understand langs is implemented, ideal is to have projection inherit user langs
      - TelepathyPower
      - PsychognomyPower
  - type: TelegnosticProjection
  - type: Stealth
    lastVisibility: 0.66
