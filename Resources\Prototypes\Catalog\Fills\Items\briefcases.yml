- type: entity
  id: BriefcaseBrownFilled
  parent: BriefcaseBrown
  suffix: Filled, Paper
  components:
  - type: StorageFill
    contents:
      - id: Paper
        amount: 3

- type: entity
  id: BriefcaseSyndieSniperBundleFilled
  parent: BriefcaseSyndie
  suffix: Syndicate, Sniper Bundle
  components:
  - type: Item
    size: Ginormous
  - type: Storage
    maxItemSize: Huge
    grid:
    - 0,0,6,3
  - type: StorageFill
    contents:
      - id: WeaponSniperHristov
      - id: MagazineBoxAntiMateriel
      - id: ClothingNeckTieRed
      - id: ClothingHandsGlovesLatex
      - id: ClothingUniformJumpsuitLawyerBlack
      - id: BarberScissors

- type: entity
  id: BriefcaseSyndieLobbyingBundleFilled
  parent: BriefcaseSyndie
  suffix: Syndicate, Spesos
  components:
  - type: StorageFill
    contents:
      - id: ClothingEyesGlassesSunglasses
      - id: SpaceCash30000
        amount: 1
      - id: EncryptionKeySyndie
      - id: RubberStampTrader
      - id: PhoneInstrumentSyndicate
      - id: ClothingUniformJumpsuitTacticool
      - id: ClothingOuterCoatJensen
      - id: ClothingHandsGlovesCombat
      - id: ClothingMaskNeckGaiter
      - id: SyndieHandyFlag

- type: entity
  id: BriefcaseSyndieLobbyingBundlePlasmamanFilled
  parent: BriefcaseSyndie
  suffix: Syndicate, Spesos, Plasmaman
  components:
  - type: StorageFill
    contents:
      - id: ClothingEyesGlassesSunglasses
      - id: SpaceCash30000
        amount: 1
      - id: EncryptionKeySyndie
      - id: RubberStampTrader
      - id: PhoneInstrumentSyndicate
      - id: ClothingUniformEnvirosuitTacticool
      - id: ClothingHeadEnvirohelmTacticool
      - id: ClothingOuterCoatJensenFilled
      - id: ClothingHandsGlovesCombat

- type: entity # No more space in briefcase due to envirohelm so put the syndicate flag in the jensen coat
  id: ClothingOuterCoatJensenFilled
  parent: ClothingOuterCoatJensen
  suffix: Syndicate Flag Inside
  components:
  - type: StorageFill
    contents:
      - id: SyndieHandyFlag

- type: entity
  id: BriefcaseThiefBribingBundleFilled
  parent: BriefcaseSyndie
  suffix: Thief, Spesos
  components:
  - type: StorageFill
    contents:
      - id: ClothingEyesGlassesSunglasses
      - id: SpaceCash20000
        amount: 1
      - id: ClothingUniformJumpsuitTacticool
      - id: ClothingOuterCoatJensen
      - id: ClothingHandsGlovesCombat
      - id: ClothingMaskNeckGaiter
      - id: ToyFigurineThief

- type: entity
  id: BriefcaseThiefBribingBundlePlasmamanFilled
  parent: BriefcaseSyndie
  suffix: Thief, Spesos, Plasmaman
  components:
  - type: StorageFill
    contents:
      - id: ClothingEyesGlassesSunglasses
      - id: SpaceCash20000
        amount: 1
      - id: ClothingUniformEnvirosuitTacticool
      - id: ClothingHeadEnvirohelmTacticool
      - id: ClothingOuterCoatJensen
      - id: ClothingHandsGlovesCombat
      - id: ToyFigurineThief
