- type: entity
  abstract: true
  parent: BaseObjective
  id: BaseDragonObjective
  components:
  - type: Objective
    # difficulty isn't used at all since objective are fixed
    difficulty: 1.5
    issuer: dragon
  - type: RoleRequirement
    roles:
      mindRoles:
      - DragonRole

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseDragonObjective
  id: CarpRiftsObjective
  components:
  - type: Objective
    icon:
      sprite: Structures/Specific/carp_rift.rsi
      state: icon_blue
  - type: NumberObjective
    # dragon can only spawn 3 rifts so keep objective the same
    min: 3
    max: 3
    title: objective-carp-rifts-title
    description: objective-carp-rifts-description
  - type: CarpRiftsCondition

- type: entity
  categories: [ HideSpawnMenu ]
  parent: [BaseDragonObjective, BaseSurviveObjective]
  id: DragonSurviveObjective
  name: "Виживіть"
  description: "Ви повинні залишатися живими, щоб зберегти контроль."
  components:
  - type: Objective
    icon:
      sprite: Mobs/Aliens/Carps/dragon.rsi
      state: alive
