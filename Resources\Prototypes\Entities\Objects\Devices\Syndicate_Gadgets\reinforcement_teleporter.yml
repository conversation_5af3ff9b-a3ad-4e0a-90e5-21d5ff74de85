

- type: entity
  parent: BaseItem
  id: ReinforcementRadioSyndicate
  name: "радіо підкріплення синдикату"
  description: "Викликати агента синдикату сумнівної якості, миттєво! Надається лише базове обладнання."
  components:
  - type: Sprite
    sprite: Objects/Devices/communication.rsi
    layers:
    - state: radio
  - type: GhostRole
    name: ghost-role-information-syndicate-reinforcement-name
    description: ghost-role-information-syndicate-reinforcement-description
    rules: ghost-role-information-syndicate-reinforcement-rules
    mindRoles:
    - MindRoleGhostRoleTeamAntagonist
    raffle:
      settings: default
  - type: GhostRoleMobSpawner
    prototype: MobHumanSyndicateAgent
  - type: EmitSoundOnUse
    sound: /Audio/Effects/Emotes/parp1.ogg
  - type: UseDelay
    delay: 300

- type: entity
  parent: ReinforcementRadioSyndicate
  id: ReinforcementRadioSyndicateNukeops # Reinforcement radio exclusive to nukeops uplink
  suffix: NukeOps
  components:
  - type: GhostRole
    name: ghost-role-information-nukeop-reinforcement-name
    description: ghost-role-information-nukeop-reinforcement-description
    rules: ghost-role-information-nukeop-reinforcement-rules
    mindRoles:
    - MindRoleGhostRoleTeamAntagonist
    raffle:
      settings: default
  - type: GhostRoleMobSpawner
    prototype: MobHumanSyndicateAgentNukeops

- type: entity
  parent: ReinforcementRadioSyndicate
  id: ReinforcementRadioSyndicateAncestor
  name: "синдикатське радіо підкріплення генетичних предків"
  description: "Викликає спеціально навченого предка на ваш вибір, щоб допомогти вам."
  components:
  - type: GhostRole
    name: ghost-role-information-syndicate-monkey-reinforcement-name
    description: ghost-role-information-syndicate-monkey-reinforcement-description
    rules: ghost-role-information-syndicate-reinforcement-rules
    mindRoles:
    - MindRoleGhostRoleTeamAntagonist
    raffle:
      settings: default
  - type: GhostRoleMobSpawner
    prototype: MobMonkeySyndicateAgent
    selectablePrototypes: ["SyndicateMonkey", "SyndicateKobold"]

- type: entity
  parent: ReinforcementRadioSyndicateAncestor
  id: ReinforcementRadioSyndicateAncestorNukeops # Reinforcement radio exclusive to nukeops uplink
  suffix: NukeOps
  components:
  - type: GhostRoleMobSpawner
    prototype: MobMonkeySyndicateAgentNukeops
    selectablePrototypes: ["SyndicateMonkeyNukeops", "SyndicateKoboldNukeops"]

- type: entity
  parent: ReinforcementRadioSyndicate
  id: ReinforcementRadioSyndicateSyndiCat
  name: "радіо підкріплення синдикату"
  description: "Викликає на допомогу відданого дресированого кота з мікробом."
  components:
  - type: GhostRole
    name: ghost-role-information-SyndiCat-name
    description: ghost-role-information-SyndiCat-description
    rules: ghost-role-information-syndicate-reinforcement-rules
    mindRoles:
    - MindRoleGhostRoleTeamAntagonist
    raffle:
      settings: default
  - type: GhostRoleMobSpawner
    prototype: MobCatSyndy
  - type: EmitSoundOnUse
    sound: /Audio/Animals/cat_meow.ogg

- type: entity
  parent: ReinforcementRadioSyndicate
  id: ReinforcementRadioSyndicateCyborgAssault # Reinforcement radio exclusive to nukeops uplink
  name: "синдикат штурм кіборгів радіо підкріплення"
  description: "Негайно викличте добре озброєного штурмового кіборга!"
  suffix: NukeOps
  components:
    - type: GhostRole
      name: ghost-role-information-syndicate-cyborg-assault-name
      description: ghost-role-information-syndicate-cyborg-description
      rules: ghost-role-information-silicon-rules
      mindRoles:
      - MindRoleGhostRoleSilicon
      raffle:
        settings: default
    - type: GhostRoleMobSpawner
      prototype: PlayerBorgSyndicateAssaultBattery
