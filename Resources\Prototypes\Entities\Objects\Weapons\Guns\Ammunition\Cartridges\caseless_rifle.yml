- type: entity
  id: BaseCartridgeCaselessRifle
  name: "патрон (гвинтівка .25)"
  parent: BaseCartridge
  abstract: true
  components:
  - type: Tag
    tags:
      - Cartridge
      - CartridgeCaselessRifle
  - type: CartridgeAmmo
    deleteOnSpawn: true
  - type: Sprite
    noRot: false
    sprite: Objects/Weapons/Guns/Ammunition/Casings/ammo_casing.rsi
    layers:
    - state: base
      map: ["enum.AmmoVisualLayers.Base"]
  - type: Appearance
  - type: SpentAmmoVisuals
  - type: StaticPrice
    price: 10

- type: entity
  id: CartridgeCaselessRifle
  name: "патрон (.25 безоболонковий)"
  parent: BaseCartridgeCaselessRifle
  components:
  - type: CartridgeAmmo
    proto: BulletCaselessRifle

- type: entity
  id: CartridgeCaselessRiflePractice
  name: "набій (практична набій калібру .25)"
  parent: BaseCartridgeCaselessRifle
  components:
  - type: CartridgeAmmo
    proto: BulletCaselessRiflePractice
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#dbdbdb"

- type: entity
  id: CartridgeCaselessRifleRubber
  name: "набій (.25 безгільзовий гумовий)"
  parent: BaseCartridgeCaselessRifle
  components:
  - type: CartridgeAmmo
    proto: BulletCaselessRifleRubber
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#43c4f7"

- type: entity
  id: CartridgeCaselessRifleIncendiary
  name: "набій (.25 безгільзовий запальний)"
  parent: BaseCartridgeCaselessRifle
  components:
  - type: CartridgeAmmo
    proto: BulletCaselessRifleIncendiary
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#ff6e52"

- type: entity
  id: CartridgeCaselessRifleUranium
  name: "патрон (.25 безгільзовий, урановий)"
  parent: BaseCartridgeCaselessRifle
  components:
  - type: CartridgeAmmo
    proto: BulletCaselessRifleUranium
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#65fe08"

- type: entity
  id: CartridgeCaselessRifleShrapnel
  name: "патрон (.25 безгільзовий, шрапнельний)"
  parent: BaseCartridgeCaselessRifle
  components:
  - type: CartridgeAmmo
    proto: BulletCaselessRifleShrapnelSpread
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#FF00FF"
