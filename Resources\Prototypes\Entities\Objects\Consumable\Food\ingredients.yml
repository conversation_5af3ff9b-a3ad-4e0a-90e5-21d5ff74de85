# Lots of misc stuff in here, hard to parent it.

# Powder (For when you throw stuff like flour and it explodes)

# Reagent Containers

- type: entity
  abstract: true
  parent: BaseItem
  id: ReagentContainerBase
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/ingredients.rsi
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50
  - type: SolutionTransfer
    # This is potentially badly-handled due to 'drink opening',
    #  but it lets the flour be tampered with, refilled, etc.
  - type: DrawableSolution
    solution: food
  - type: RefillableSolution
    solution: food
  - type: DrainableSolution
    solution: food
  - type: Drink
    solution: food
    useSound:
      collection: eating
  - type: Damageable
    damageContainer: Inorganic
  - type: Spillable
    solution: food
  - type: TrashOnSolutionEmpty
    solution: food
  - type: Tag
    tags:
      - Ingredient

- type: entity
  abstract: true
  parent: ReagentContainerBase
  id: ReagentPacketBase
  components:
  - type: Openable
    sound:
      collection: packetOpenSounds
  # packet can be broken open
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 2
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: desecration
      - !type:SpillBehavior
        solution: food
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  # packet contents can be splashed when open
  - type: MeleeWeapon
    soundNoDamage:
      path: "/Audio/Effects/Fluids/splat.ogg"
    damage:
      types:
        Blunt: 0

- type: entity
  abstract: true
  id: ItemHeftyBase
  components:
  - type: DamageOnLand
    damage:
      types:
        Blunt: 1
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 1

- type: entity
  parent: [ReagentPacketBase, ItemHeftyBase]
  id: ReagentContainerFlour
  name: "мішок борошна"
  description: "Великий мішок борошна. Добре для випічки!"
  components:
  - type: Sprite
    state: flour-big
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50
        reagents:
        - ReagentId: Flour
          Quantity: 50

- type: entity
  parent: [ReagentPacketBase, ItemHeftyBase]
  id: ReagentContainerFlourSmall
  name: "пачка борошна"
  description: "Пачка борошна. Добре підходить для випічки!"
  components:
  - type: Sprite
    state: flour-small
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Flour
          Quantity: 20

- type: entity
  parent: ReagentPacketBase
  id: ReagentContainerCornmeal
  name: "мішок кукурудзяного борошна"
  description: "Великий мішок кукурудзяного борошна. Згодиться для приготування їжі!"
  components:
  - type: Sprite
    state: cornmeal-big
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Cornmeal
          Quantity: 50

- type: entity
  parent: ReagentPacketBase
  id: ReagentContainerCornmealSmall
  name: "пачка кукурудзяного борошна"
  description: "Пачка кукурудзяного борошна. Згодиться для приготування їжі!"
  components:
  - type: Sprite
    state: cornmeal
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Cornmeal
          Quantity: 20

- type: entity
  parent: ReagentPacketBase
  id: ReagentContainerRice
  name: "мішок з рисом"
  description: "Великий мішок рису. Гарний для приготування їжі!"
  components:
  - type: Sprite
    state: rice-big
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Rice
          Quantity: 50

- type: entity
  parent: ReagentPacketBase
  id: ReagentContainerRiceSmall
  name: "пачка рису"
  description: "Пачка рису. Чудово підходить для приготування їжі!"
  components:
  - type: Sprite
    state: rice-small
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Rice
          Quantity: 20

- type: entity
  parent: ReagentPacketBase
  id: ReagentContainerSugar
  name: "пакетик цукру"
  description: "Великий мішок смачного космічного цукру."
  components:
  - type: Sprite
    state: sugar-big
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Sugar
          Quantity: 50

- type: entity
  parent: ReagentPacketBase
  id: ReagentContainerSugarSmall
  name: "пачка цукру"
  description: "Пачка смачного космічного цукру."
  components:
  - type: Sprite
    state: sugar-small
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Sugar
          Quantity: 20

# Misc

- type: entity
  parent: ReagentPacketBase
  id: ReagentContainerOliveoil
  name: "оливкова олія"
  description: "Оливкова олія. З космічних оливок, мабуть."
  components:
  - type: Sprite
    state: oliveoil
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: OilOlive
          Quantity: 20

- type: entity
  parent: ReagentPacketBase
  id: ReagentContainerMayo
  name: "майонез"
  description: "Пляшка майонезу."
  components:
  - type: Sprite
    state: mayo
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50 # you always need more mayo
        reagents:
        - ReagentId: Mayo
          Quantity: 50
  - type: Tag
    tags:
      - Mayo

# - type: entity
#   parent: ReagentPacketBase
#   id: ReagentContainerAllspice
#   name: all-spice
#   description:
#   components:
#   - type: Sprite
#     state: spice
#   - type: SolutionContainerManager
#     maxVol: 10
#     contents:
#       reagents:
#       - ReagentId: Allspice
#         Quantity: 10

# Baking

- type: entity
  abstract: true
  parent: FoodBase
  id: FoodBakingBase
  description: "Використовується в різних рецептах."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/ingredients.rsi
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 18
        reagents:
        - ReagentId: Nutriment
          Quantity: 15
  - type: Tag
    tags:
      - Ingredient

- type: entity
  name: "тісто"
  parent: FoodBakingBase
  id: FoodDough
  description: "Шматок тіста."
  components:
  - type: FlavorProfile
    flavors:
      - dough
  - type: Sprite
    state: dough
  - type: SliceableFood
    count: 3
    slice: FoodDoughSlice
  - type: Construction
    graph: Pizza
    node: start

- type: entity
  name: "шматочок тіста"
  parent: FoodBakingBase
  id: FoodDoughSlice
  description: "Шматочок тіста. Можна приготувати булочку."
  components:
  - type: FlavorProfile
    flavors:
      - dough
  - type: Sprite
    state: dough-slice
  - type: Tag
    tags:
    - Slice

- type: entity
  name: "тісто з кукурудзяного борошна"
  parent: FoodBakingBase
  id: FoodDoughCornmeal
  description: "Шматок тіста з кукурудзяного борошна."
  components:
  - type: FlavorProfile
    flavors:
      - chalky
      - dough
  - type: Sprite
    state: cornmealdough
  - type: SliceableFood
    count: 3
    slice: FoodDoughCornmealSlice

- type: entity
  name: "шматочок тіста з кукурудзяного борошна"
  parent: FoodBakingBase
  id: FoodDoughCornmealSlice
  description: "Шматочок тіста з кукурудзяного борошна."
  components:
  - type: FlavorProfile
    flavors:
      - chalky
      - dough
  - type: Sprite
    state: cornmealdough-slice
  - type: Tag
    tags:
    - Slice

- type: entity
  name: "тісто для тортильї"
  parent: FoodBakingBase
  id: FoodDoughTortilla
  description: "Шматок тіста для тортильї."
  components:
  - type: FlavorProfile
    flavors:
      - chalky
      - dough
  - type: Sprite
    state: tortilladough
  - type: SliceableFood
    count: 3
    slice: FoodDoughTortillaSlice

- type: entity
  name: "шматочок тіста тортильї"
  parent: FoodBakingBase
  id: FoodDoughTortillaSlice
  description: "Шматочок тіста для тортильї."
  components:
  - type: FlavorProfile
    flavors:
      - chalky
      - dough
  - type: Sprite
    state: tortilladough-slice
  - type: Construction
    graph: Tortilla
    node: start
  - type: Tag
    tags:
    - Slice

- type: entity
  name: "розплющене тісто для тортильї"
  parent: FoodBakingBase
  id: FoodDoughTortillaFlat
  description: "Розплющений шматочок тіста для тортильї, приготуйте так, щоб вийшла тортилья для тако."
  components:
  - type: FlavorProfile
    flavors:
      - chalky
      - dough
  - type: Sprite
    state: tortilladough-flat
  - type: Construction
    graph: Tortilla
    node: flat

- type: entity
  name: "сира тістова основа"
  parent: FoodBakingBase
  id: FoodDoughPastryBaseRaw
  description: "Перед вживанням необхідно приготувати."
  components:
  - type: Sprite
    state: dough-pastry

- type: entity
  name: "тістова основа"
  parent: FoodBakingBase
  id: FoodDoughPastryBase
  description: "Основа для будь-якої поважаючої себе випічки."
  components:
  - type: Sprite
    state: dough-pastry-baked

- type: entity
  name: "тісто для пирога"
  parent: FoodBakingBase
  id: FoodDoughPie
  description: "Приготуйте його, щоб вийшов пиріг."
  components:
  - type: Sprite
    state: dough-pie

- type: entity
  name: "плоске тісто"
  parent: FoodBakingBase
  id: FoodDoughFlat
  description: "Розплющуємо тісто."
  components:
  - type: Sprite
    state: dough-flat
  - type: Construction
    graph: Pizza
    node: flat
  - type: SliceableFood
    count: 3
    slice: FoodCroissantRaw

- type: entity
  name: "хліб для піци"
  parent: FoodBakingBase
  id: FoodDoughPizzaBaked
  description: "Додайте інгредієнти для приготування піци."
  components:
  - type: Sprite
    state: pizzabread

- type: entity
  name: "тісто для торта"
  parent: FoodBakingBase
  id: FoodCakeBatter
  description: "Випікаємо, щоб вийшов пиріг."
  components:
  - type: FlavorProfile
    flavors:
      - sweetdough
  - type: Sprite
    state: cakebatter

- type: entity
  name: "шматочок вершкового масла"
  parent: FoodBakingBase
  id: FoodButter
  description: "Паличка смачної, золотистої, жирної смакоти."
  components:
  - type: Sprite
    state: butter
  - type: Slippery
  - type: StepTrigger
    intersectRatio: 0.2
  - type: CollisionWake
    enabled: false
  - type: Physics
    bodyType: Dynamic
  - type: Fixtures
    fixtures:
      slips:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.3,-0.2,0.3,0.2"
        layer:
        - SlipLayer
        hard: false
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.3,-0.2,0.3,0.2"
        density: 10
        mask:
        - ItemMask
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 18
        reagents:
        - ReagentId: Butter
          Quantity: 15
  - type: SliceableFood
    count: 3
    slice: FoodButterSlice

- type: entity
  name: "шматочок вершкового масла"
  parent: FoodBakingBase
  id: FoodButterSlice
  description: "Шматочок смачної, золотистої, жирної смакоти."
  components:
  - type: Sprite
    state: butter-slice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 6
        reagents:
        - ReagentId: Butter
          Quantity: 5
  - type: Tag
    tags:
    - Slice

- type: entity
  name: "паличка конопляної олії"
  parent: FoodBakingBase
  id: FoodCannabisButter
  description: "Додайте його до вашої улюбленої випічки, щоб завжди мати під рукою."
  components:
  - type: Sprite
    state: butter
    color: "#82C36E"
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 100
        reagents:
        - ReagentId: Butter
          Quantity: 10
        - ReagentId: THC
          Quantity: 82
  - type: Extractable
    grindableSolutionName: food

- type: entity
  name: "сирне колесо"
  parent: FoodBakingBase
  id: FoodCheese
  description: "Велике колесо смачного Чеддеру."
  components:
  - type: FlavorProfile
    flavors:
      - cheesy
  - type: Sprite
    state: cheesewheel
  - type: SliceableFood
    count: 3
    slice: FoodCheeseSlice

- type: entity
  name: "сирний клин"
  parent: FoodBakingBase
  id: FoodCheeseSlice
  description: "Скибочка смачного Чеддеру. Сирне колесо, з якого його відрізали, не могло далеко піти."
  components:
  - type: FlavorProfile
    flavors:
      - cheesy
  - type: Sprite
    state: cheesewedge
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 6
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
  - type: Tag
    tags:
    - Slice
  - type: FoodSequenceElement
    entries:
      Taco: CheeseTaco
      Burger: CheeseBurger

- type: entity
  name: "шеврова колода"
  parent: FoodBakingBase
  id: FoodChevre
  description: "М'яке поліно вершкового шевру."
  components:
  - type: FlavorProfile
    flavors:
      - nutty
      - creamy
  - type: Sprite
    state: chevrelog
  - type: SliceableFood
    count: 3
    slice: FoodChevreSlice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 6
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
        - ReagentId: Vitamin
          Quantity: 0.6

- type: entity
  name: "шевровий диск"
  parent: FoodBakingBase
  id: FoodChevreSlice
  description: "Невеликий диск вершкового шевру. Ідеальна прикраса для французьких гарнірів."
  components:
  - type: FlavorProfile
    flavors:
      - nutty
      - creamy
  - type: Sprite
    state: chevredisk
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 2
        reagents:
        - ReagentId: Nutriment
          Quantity: 1
        - ReagentId: Vitamin
          Quantity: 0.2
  - type: Tag
    tags:
    - Slice

- type: entity
  name: "тофу"
  parent: FoodBakingBase
  id: FoodTofu
  description: "Суцільний білий блок з тонким ароматом."
  components:
  - type: FlavorProfile
    flavors:
      - tofu
  - type: Sprite
    state: tofu
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 6
        reagents:
        - ReagentId: Protein
          Quantity: 9
        - ReagentId: Nutriment
          Quantity: 6
  - type: SliceableFood
    count: 3
    slice: FoodTofuSlice

- type: entity
  name: "шматочок тофу"
  parent: FoodBakingBase
  id: FoodTofuSlice
  description: "Шматочок тофу. Інгредієнт різних вегетаріанських страв."
  components:
  - type: FlavorProfile
    flavors:
      - tofu
  - type: Sprite
    state: tofu-slice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 6
        reagents:
        - ReagentId: Protein
          Quantity: 3
        - ReagentId: Nutriment
          Quantity: 2
  - type: Tag
    tags:
    - Slice

- type: entity
  name: "обгоріле місиво"
  parent: FoodBakingBase
  id: FoodBadRecipe
  description: "За це когось треба звільнити з посади кухаря."
  components:
  - type: FlavorProfile
    flavors:
      - terrible
  - type: Sprite
    state: badrecipe
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 5
        reagents:
        - ReagentId: Nutriment
          Quantity: 2
        - ReagentId: GastroToxin
          Quantity: 3
  - type: Tag
    tags:
    - Trash

- type: entity
  name: "какао-боби"
  parent: FoodProduceBase
  id: FoodCocoaBeans
  description: "Шоколаду ніколи не буває забагато!"
  components:
  - type: FlavorProfile
    flavors:
      - chocolate
  - type: Sprite
    sprite: Objects/Specific/Hydroponics/cocoa.rsi
    state: produce-beans
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 14
        reagents:
        - ReagentId: Nutriment
          Quantity: 2
        - ReagentId: Vitamin
          Quantity: 1
        - ReagentId: CocoaPowder
          Quantity: 2
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: CocoaPowder
        Quantity: 2
  - type: Tag
    tags:
      - Ingredient

- type: entity
  parent: BaseFoodCondimentPacket
  id: FoodCondimentPacketProtein
  name: "упаковка протеїнового порошку"
  description: "Містить 10 унцій порошкового протеїну. Змішати з 20 унціями води"
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Protein
          Quantity: 10
  - type: Icon
    state: packet-greygoo
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 2
    fillBaseName: packet-solid-

- type: entity
  name: "сирий круасан"
  parent: FoodBakingBase
  id: FoodCroissantRaw
  description: "Масляниста, пластівчаста смакота, яка чекає, щоб статися."
  components:
  - type: FlavorProfile
    flavors:
      - dough
  - type: Sprite
    state: croissant-raw
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 4
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
