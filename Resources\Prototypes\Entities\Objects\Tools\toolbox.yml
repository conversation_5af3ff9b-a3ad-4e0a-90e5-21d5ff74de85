- type: entity
  id: ToolboxBase
  parent: BaseStorageItem
  abstract: true
  components:
  - type: Sprite
    layers:
      - map: [ base ]
        state: icon
  - type: EmitSoundOnPickup
    sound: /Audio/SimpleStation14/Items/Handling/toolbox_pickup.ogg
  - type: EmitSoundOnDrop
    sound: /Audio/SimpleStation14/Items/Handling/toolbox_drop.ogg
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Items/toolbox_drop.ogg
  - type: Storage
    maxItemSize: Normal
    grid:
    - 0,0,6,3
  - type: Item
    size: Ginormous
  - type: MeleeWeapon
    attackRate: 1.1
    range: 1.5
    damage:
      types:
        Blunt: 9
    bluntStaminaDamageFactor: 2.0
    heavyRangeModifier: 1.2
    heavyDamageBaseModifier: 1.5
    heavyRateModifier: 2
    heavyStaminaCost: 2.5
    angle: 110
    soundHit:
      path: "/Audio/Weapons/smash.ogg"
  - type: DamageOtherOnHit
    meleeDamageMultiplier: 1.5
    staminaCost: 10
  - type: Tag
    tags:
    - Toolbox
  - type: GenericVisualizer
    visuals:
      enum.StorageVisuals.Open:
        base:
          True: { state: icon-open }
          False: { state: icon }
  - type: Appearance
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 38
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  name: "аварійний набір інструментів"
  parent: ToolboxBase
  id: ToolboxEmergency
  description: "Яскраво-червоний ящик з аварійними інструментами."
  components:
  - type: Sprite
    sprite: Objects/Tools/Toolboxes/toolbox_red.rsi
    state: icon
  - type: Item
    sprite: Objects/Tools/Toolboxes/toolbox_red.rsi

- type: entity
  name: "механічний ящик для інструментів"
  parent: ToolboxBase
  id: ToolboxMechanical
  description: "Синій ящик, наповнений механічними інструментами."
  components:
  - type: Sprite
    sprite: Objects/Tools/Toolboxes/toolbox_blue.rsi
    state: icon
  - type: Item
    sprite: Objects/Tools/Toolboxes/toolbox_blue.rsi

- type: entity
  name: "електричний ящик для інструментів"
  parent: ToolboxBase
  id: ToolboxElectrical
  description: "Ящик для інструментів, в якому зазвичай зберігаються електричні прилади."
  components:
  - type: Sprite
    sprite: Objects/Tools/Toolboxes/toolbox_yellow.rsi
    state: icon
  - type: Item
    sprite: Objects/Tools/Toolboxes/toolbox_yellow.rsi

- type: entity
  name: "електричний ящик для інструментів"
  suffix: Syndicate, Turret
  parent: ToolboxElectrical
  id: ToolboxElectricalTurret
  description: "Ящик для інструментів, в якому зазвичай зберігаються електричні прилади."
  components:
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 1
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
          - !type:SpawnEntitiesBehavior
            spawn:
              WeaponTurretSyndicateDisposable:
                min: 1
                max: 1
  - type: StaticPrice
    price: 1350

- type: entity
  name: "художній інструментарій"
  parent: ToolboxBase
  id: ToolboxArtistic
  description: "Ящик для інструментів, в якому зазвичай зберігаються художні приналежності."
  components:
  - type: Sprite
    sprite: Objects/Tools/Toolboxes/toolbox_green.rsi
    state: icon
  - type: Item
    sprite: Objects/Tools/Toolboxes/toolbox_green.rsi

- type: entity
  name: "підозрілий набір інструментів"
  parent: ToolboxBase
  id: ToolboxSyndicate
  description: "Зловісний на вигляд ящик, наповнений інструментами елітних синдикатів."
  components:
  - type: Sprite
    sprite: Objects/Tools/Toolboxes/toolbox_syn.rsi
    state: icon
  - type: Item
    sprite: Objects/Tools/Toolboxes/toolbox_syn.rsi
  - type: Storage
    maxItemSize: Huge
    grid:
    - 0,0,7,3
  - type: MeleeWeapon
    damage:
      types:
        Blunt: 11.5
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 15

- type: entity
  name: "золотий ящик для інструментів"
  parent: ToolboxBase
  id: ToolboxGolden
  description: "Суцільнозолотий ящик для інструментів. Репер би за таке вбив."
  components:
  - type: Sprite
    sprite: Objects/Tools/Toolboxes/toolbox_gold.rsi
    state: icon
  - type: Item
    sprite: Objects/Tools/Toolboxes/toolbox_gold.rsi
  - type: MeleeWeapon
    damage:
      types:
        Blunt: 12
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 16

- type: entity
  id: ToolboxThief
  name: "злодій невизначений набір інструментів"
  description: "Саме тут лежать припаси твого улюбленого злодія. Спробуй згадати, які саме."
  parent: BaseItem
  components:
  - type: Sprite
    sprite: Objects/Tools/Toolboxes/toolbox_thief.rsi
    state: icon
  - type: ThiefUndeterminedBackpack
    possibleSets:
    # - TO DO Thief pinpointer needed
    - ChemistrySet
    - ToolsSet
    - ChameleonSet # - TO DO Chameleon stump PR needed
    - SyndieSet
    - SleeperSet
    - CommunicatorSet
    - CommunicatorSetPlasmaman
    - SmugglerSet
  - type: ActivatableUI
    key: enum.ThiefBackpackUIKey.Key
  - type: UserInterface
    interfaces:
      enum.ThiefBackpackUIKey.Key:
        type: ThiefBackpackBoundUserInterface
