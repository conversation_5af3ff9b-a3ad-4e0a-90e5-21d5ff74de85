- type: entity
  parent: ClothingHandsBase
  id: ClothingHandsGlovesBoxingRed
  name: "червоні боксерські рукавички"
  description: "Червоні рукавички для спортивного боксу."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/Boxing/boxingred.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/Boxing/boxingred.rsi
  - type: StaminaDamageOnHit
    damage: 8 #Stam damage values seem a bit higher than regular damage because of the decay, etc
    # This needs to be moved to boxinggloves
    #knockdownSound: /Audio/Weapons/boxingbell.ogg
  - type: MeleeWeapon
    attackRate: 1.5
    damage:
      types:
        Blunt: 0.4
    soundHit:
      collection: BoxingHit
    animation: WeaponArcFist
    mustBeEquippedToUse: true
  - type: Fiber
    fiberMaterial: fibers-leather
    fiberColor: fibers-red
  - type: FingerprintMask
  - type: Tag
    tags:
    - Kangaroo
    - WhitelistChameleon

- type: entity
  parent: ClothingHandsGlovesBoxingRed
  id: ClothingHandsGlovesBoxingBlue
  name: "сині боксерські рукавички"
  description: "Сині рукавички для спортивного боксу."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/Boxing/boxingblue.rsi
    state: icon
  - type: Clothing
    sprite: Clothing/Hands/Gloves/Boxing/boxingblue.rsi
  - type: Fiber
    fiberMaterial: fibers-leather
    fiberColor: fibers-blue
  - type: FingerprintMask

- type: entity
  parent: ClothingHandsGlovesBoxingRed
  id: ClothingHandsGlovesBoxingGreen
  name: "зелені боксерські рукавички"
  description: "Зелені рукавички для спортивного боксу."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/Boxing/boxinggreen.rsi
    state: icon
  - type: Clothing
    sprite: Clothing/Hands/Gloves/Boxing/boxinggreen.rsi
  - type: Fiber
    fiberMaterial: fibers-leather
    fiberColor: fibers-green
  - type: FingerprintMask

- type: entity
  parent: ClothingHandsGlovesBoxingRed
  id: ClothingHandsGlovesBoxingYellow
  name: "жовті боксерські рукавички"
  description: "Жовті рукавички для змагального боксу."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/Boxing/boxingyellow.rsi
    state: icon
  - type: Clothing
    sprite: Clothing/Hands/Gloves/Boxing/boxingyellow.rsi
  - type: Fiber
    fiberMaterial: fibers-leather
    fiberColor: fibers-yellow
  - type: FingerprintMask

- type: entity
  parent: ClothingHandsGlovesBoxingBlue
  id: ClothingHandsGlovesBoxingRigged
  suffix: Rigged
  components:
  - type: StaminaDamageOnHit
    damage: 25
  - type: MeleeWeapon
    attackRate: 1.4
    damage:
      types:
        Blunt: 8
    bluntStaminaDamageFactor: 0.0 # so blunt doesn't deal stamina damage at all
    mustBeEquippedToUse: true

- type: entity
  parent: ClothingHandsBase
  id: ClothingHandsGlovesCaptain
  name: "капітанські рукавички"
  description: "Королівські сині рукавички, з красивою золотою обробкою. Шикарні."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/captain.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/captain.rsi
  - type: GloveHeatResistance
    heatResistance: 1400
  - type: Insulated
  - type: Fiber
    fiberMaterial: fibers-durathread
    fiberColor: fibers-regal-blue
  - type: FingerprintMask

- type: entity
  parent: ClothingHandsBase
  id: ClothingHandsGlovesHop
  name: "захисні від порізів паперу рукавички"
  description: "Ідеально підходить для роботи з документами та вирішення питань з бюрократією."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/hop.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/hop.rsi
  - type: Fiber
    fiberMaterial: fibers-durathread
    fiberColor: fibers-black
  - type: Armor
    modifiers:
      coefficients:
        Slash: 0.95
  - type: FingerprintMask

#### Medical
- type: entity
  parent: ClothingHandsButcherable
  id: ClothingHandsGlovesLatex
  name: "латексні рукавички"
  description: "Тонкі стерильні латексні рукавички. Базовий ЗІЗ для будь-якого лікаря."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/latex.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/latex.rsi
  - type: Fiber
    fiberMaterial: fibers-latex
  - type: FingerprintMask

- type: entity
  parent: ClothingHandsButcherable
  id: ClothingHandsGlovesNitrile
  name: "нітрилові рукавички"
  description: "Якісні нітрилові рукавички. Дорогі медичні ЗІЗ."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/nitrile.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/nitrile.rsi
  - type: Fiber
    fiberMaterial: fibers-nitrile
  - type: FingerprintMask
####
- type: entity
  parent: ClothingHandsButcherable
  id: ClothingHandsGlovesLeather
  name: "шкіряні рукавички ботаніка"
  description: "Ці шкіряні рукавички захищають від колючок, шипів, колючок, шпильок та інших шкідливих предметів рослинного походження. Вони також досить теплі."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/leather.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/leather.rsi
  - type: GloveHeatResistance
    heatResistance: 1400
  - type: Fiber
    fiberMaterial: fibers-leather
    fiberColor: fibers-brown
  - type: FingerprintMask

- type: entity
  parent: ClothingHandsBase
  id: ClothingHandsGlovesPowerglove
  name: "робочі рукавички"
  description: "Тепер я граюся з енергією! Зачекай... вони вимкнені." #Use "Now I'm playin' with power! BAM!" for when they're turned on
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/powerglove.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/powerglove.rsi
  - type: Fiber
    fiberMaterial: fibers-nanomachines
  - type: FingerprintMask

- type: entity
  parent: ClothingHandsButcherable
  id: ClothingHandsGlovesRobohands
  name: "рукавички робототехніка"
  description: "Біп-буп-борп!"
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/robohands.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/robohands.rsi
  - type: Fiber
    fiberMaterial: fibers-leather
    fiberColor: fibers-black
  - type: FingerprintMask

- type: entity
  parent: [ClothingHandsBase, BaseToggleClothing]
  id: ClothingHandsGlovesSpaceNinja
  name: "рукавички космічних ніндзя"
  description: "Ці чорні рукавички з нанопокриттям ізолюють від електрики та забезпечують вогнестійкість."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/spaceninja.rsi
    layers:
    - state: icon
      map: [ "enum.ToggleVisuals.Layer" ]
  - type: Clothing
    sprite: Clothing/Hands/Gloves/spaceninja.rsi
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ToggleVisuals.Toggled:
        enum.ToggleVisuals.Layer:
          True: {state: icon-green}
          False: {state: icon}
  - type: GloveHeatResistance
    heatResistance: 1400
  - type: Insulated
  - type: Fiber
    fiberMaterial: fibers-nanomachines
  - type: FingerprintMask
  - type: Thieving
    stripTimeReduction: 1
  - type: ToggleClothing
    action: ActionToggleNinjaGloves
  - type: Unremoveable
  - type: NinjaGloves
    abilities:
    - components:
      - type: BatteryDrainer
      - type: StunProvider
        noPowerPopup: ninja-no-power
        whitelist:
          components:
          - Stamina
      - type: EmagProvider
        whitelist:
          components:
          - Airlock
    - objective: StealResearchObjective
      components:
      - type: ResearchStealer
    - objective: TerrorObjective
      components:
      - type: CommsHacker
        threats: NinjaThreats
    - objective: MassArrestObjective
      components:
      - type: CriminalRecordsHacker

- type: entity
  parent: ClothingHandsGlovesColorBlack
  id: ClothingHandsGlovesCombat
  name: "бойові рукавички"
  description: "Ці тактичні рукавички вогнетривкі та ударостійкі."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/combat.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/combat.rsi
  - type: GloveHeatResistance
    heatResistance: 1400
  - type: Insulated
  - type: Fiber
    fiberMaterial: fibers-insulative
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.95
        Slash: 0.95
        Piercing: 0.95

# can't parent combat gloves since they are butcherable
- type: entity
  parent: ClothingHandsGlovesSyntheticBase
  id: ClothingHandsTacticalMaidGloves
  name: "тактичні рукавички покоївки"
  description: "Тактичні рукавички покоївки, кожна поважаюча себе покоївка повинна вміти непомітно ліквідувати свої цілі."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/tacticalmaidgloves.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/tacticalmaidgloves.rsi
  - type: Fiber
    fiberColor: fibers-black
  - type: GloveHeatResistance
    heatResistance: 1400
  - type: Insulated
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.95
        Slash: 0.95
        Piercing: 0.95

- type: entity
  parent: ClothingHandsGlovesCombat
  id: ClothingHandsMercGlovesCombat
  name: "бойові рукавиці найманців"
  description: "Якісні бойові рукавички для захисту рук від механічних пошкоджень під час бою."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/mercbattle.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/mercbattle.rsi
  - type: Butcherable
    butcheringType: Knife
    spawned:
    - id: ClothingHandsGlovesMercFingerless
  - type: Fiber
    fiberMaterial: fibers-insulative
    fiberColor: fibers-olive
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.95
        Slash: 0.95
        Piercing: 0.95

- type: entity
  parent: ClothingHandsButcherable
  id: ClothingHandsGlovesFingerless
  name: "рукавички без пальців"
  description: "Прості чорні рукавички без пальців для працьовитих людей."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/fingerless.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/fingerless.rsi
  - type: Fiber
    fiberMaterial: fibers-synthetic
    fiberColor: fibers-black

- type: entity
  parent: ClothingHandsButcherable
  id: ClothingHandsGlovesFingerlessWhite
  name: "рукавички без пальців"
  description: "Прості рукавички без пальців."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/fingerlesswhite.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/fingerlesswhite.rsi
  - type: Fiber
    fiberMaterial: fibers-synthetic
    fiberColor: fibers-dyed

- type: entity
  parent: ClothingHandsBase
  id: ClothingHandsGlovesFingerlessInsulated
  name: "ізольовані рукавички без пальців"
  description: "Утеплені рукавички стійкі до ударів, або, принаймні, були такими раніше."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/fingerlessinsuls.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/fingerlessinsuls.rsi
  - type: Fiber
    fiberMaterial: fibers-insulative
    fiberColor: fibers-yellow

- type: entity
  parent: ClothingHandsButcherable
  id: ClothingHandsGlovesMercFingerless
  name: "найманські рукавички без пальців"
  description: "Рукавички, які, можливо, не захистять вас від опіків пальців, але зроблять вас прохолоднішими."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/mercfingerless.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/mercfingerless.rsi
  - type: Fiber
    fiberMaterial: fibers-insulative
    fiberColor: fibers-olive

- type: entity
  # Intentionally named after regular gloves, they're meant to be sneaky.
  # This means they can also be butchered if you need to look un-sus before a very thorough search...
  parent: ClothingHandsGlovesColorBlack
  id: ThievingGloves
  suffix: Thieving
  components:
  - type: Tag
    tags: [] # ignore "WhitelistChameleon" tag
  - type: Thieving
    stripTimeReduction: 1.5

- type: entity
  parent: ClothingHandsGlovesColorWhite
  id: ClothingHandsGlovesCluwne
  name: "руки клувні"
  suffix: Unremoveable
  description: "Проклята пара рук клувні."
  components:
  - type: Unremoveable

- type: entity
  parent: ClothingHandsButcherable
  id: ClothingHandsGlovesNorthStar
  name: "рукавички Полярної зірки"
  description: "Ці рукавички дозволяють наносити удари неймовірно швидко."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/northstar.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/northstar.rsi
  - type: MeleeWeapon
    autoAttack: true
    attackRate: 0.25
    heavyStaminaCost: 2
    heavyDamageBaseModifier: 1.05
    maxTargets: 1
    damage:
      types:
       Blunt: 8
    soundHit:
      collection: Punch
    animation: WeaponArcFist
    mustBeEquippedToUse: true
  - type: Fiber
    fiberMaterial: fibers-leather
    fiberColor: fibers-blue
  - type: FingerprintMask
  - type: MeleeSpeech
  - type: ActivatableUI
    key: enum.MeleeSpeechUiKey.Key
    verbOnly: true
  - type: Actions
  - type: UserInterface
    interfaces:
      enum.MeleeSpeechUiKey.Key:
        type: MeleeSpeechBoundUserInterface
  - type: StaticPrice
    price: 0

- type: entity
  parent: ClothingHandsBase
  id: ClothingHandsGlovesForensic
  name: "криміналістичні рукавички"
  description: "Не залишайте волокон і відбитків пальців. Якщо ви працюєте без них, ви жахливий детектив."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/forensic.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/forensic.rsi
  - type: FingerprintMask
  - type: GuideHelp
    guides:
    - Forensics

# TODO Make lubed items not slip in hands
- type: entity
  parent: ClothingHandsBase
  id: ClothingHandsGlovesJanitor
  name: "гумові рукавички"
  description: "Високоякісні гумові рукавички, скрипучі, щоб зробити прибирання!"
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/janitor.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/janitor.rsi
  - type: Fiber
    fiberMaterial: fibers-rubber
    fiberColor: fibers-yellow
  - type: FingerprintMask

- type: entity
  parent: ClothingHandsGlovesSyntheticBase
  id: ClothingHandsGlovesEvening
  name: "вечірні рукавички"
  description: "Рукавички, що сягають за лікоть. Подача елегантності та подача вигляду!"
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/evening_gloves.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/evening_gloves.rsi

- type: entity
  parent: ClothingHandsGlovesSyntheticBase
  id: ClothingHandsTacticalMaidGlovesLoadout
  name: "тактичні рукавички покоївки"
  description: "Тактичні рукавички покоївки, кожна поважаюча себе покоївка повинна вміти непомітно усувати плями, які залишають пасажири."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/tacticalmaidgloves.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/tacticalmaidgloves.rsi
  - type: Fiber
    fiberColor: fibers-black
