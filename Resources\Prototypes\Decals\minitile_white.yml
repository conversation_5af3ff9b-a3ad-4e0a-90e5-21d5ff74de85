﻿- type: decal
  id: MiniTileWhite
  parent: MiniTile
  abstract: true

- type: decal
  id: MiniTileWhiteBox
  parent: MiniTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: white_box

- type: decal
  id: MiniTileWhiteCornerNe
  parent: MiniTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: white_corner_ne

- type: decal
  id: MiniTileWhiteCornerSe
  parent: MiniTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: white_corner_se

- type: decal
  id: MiniTileWhiteCornerNw
  parent: MiniTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: white_corner_nw

- type: decal
  id: MiniTileWhiteCornerSw
  parent: MiniTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: white_corner_sw

- type: decal
  id: MiniTileWhiteInnerNe
  parent: MiniTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: white_inner_ne

- type: decal
  id: MiniTileWhiteInnerSe
  parent: MiniTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: white_inner_se

- type: decal
  id: MiniTileWhiteInnerNw
  parent: MiniTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: white_inner_nw

- type: decal
  id: MiniTileWhiteInnerSw
  parent: MiniTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: white_inner_sw

- type: decal
  id: MiniTileWhiteEndN
  parent: MiniTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: white_end_n

- type: decal
  id: MiniTileWhiteEndE
  parent: MiniTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: white_end_e

- type: decal
  id: MiniTileWhiteEndS
  parent: MiniTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: white_end_s

- type: decal
  id: MiniTileWhiteEndW
  parent: MiniTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: white_end_w

- type: decal
  id: MiniTileWhiteLineN
  parent: MiniTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: white_line_n

- type: decal
  id: MiniTileWhiteLineE
  parent: MiniTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: white_line_e

- type: decal
  id: MiniTileWhiteLineS
  parent: MiniTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: white_line_s

- type: decal
  id: MiniTileWhiteLineW
  parent: MiniTileWhite
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: white_line_w

