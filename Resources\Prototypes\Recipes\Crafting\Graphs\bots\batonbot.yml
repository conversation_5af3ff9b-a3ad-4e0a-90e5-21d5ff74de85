- type: constructionGraph
  id: BatonBot
  start: start
  graph:
  - node: start
    edges:
    - to: bot
      steps:
      - tag: ProximitySensor
        icon:
          sprite: Objects/Misc/proximity_sensor.rsi
          state: icon
        name: proximity sensor
        doAfter: 2
      - tag: Stunbaton
        icon:
          sprite: Objects/Weapons/Melee/stunbaton.rsi
          state: stunbaton_on
        name: stunbaton
        doAfter: 2
      - tag: ClothingHeadHelmetBasic
        icon:
          sprite: DeltaV/Clothing/Head/Helmets/security.rsi
          state: icon
        name: Security Helmet
        doAfter: 2
  - node: bot
    entity: MobBatonBot
