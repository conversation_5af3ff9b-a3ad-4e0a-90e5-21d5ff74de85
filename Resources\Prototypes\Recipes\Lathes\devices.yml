- type: latheRecipe
  id: TimerTrigger
  result: TimerTrigger
  category: Parts
  completetime: 2
  materials:
    Steel: 300
    Plastic: 200

- type: latheRecipe
  id: SignalTrigger
  result: SignalTrigger
  category: Parts
  completetime: 2
  materials:
    Steel: 300
    Plastic: 200

- type: latheRecipe
  id: VoiceTrigger
  result: VoiceTrigger
  category: Parts
  completetime: 2
  materials:
    Steel: 300
    Plastic: 200

- type: latheRecipe
  id: Igniter
  result: Igniter
  category: Parts
  completetime: 2
  materials:
    Steel: 300
    Plastic: 100
    Glass: 100

- type: latheRecipe
  id: ChemicalPayload
  result: ChemicalPayload
  category: Weapons
  completetime: 2
  materials:
    Steel: 200
    Plastic: 300

- type: latheRecipe
  id: FlashPayload
  result: FlashPayload
  category: Weapons
  completetime: 2
  materials:
    Steel: 50
    Plastic: 100
    Glass: 50
    #one fourth of what making a flash would cost

- type: latheRecipe
  id: ExplosivePayload
  result: ExplosivePayload
  category: Weapons
  completetime: 4
  materials:
    Steel: 100
    Plastic: 100
    Glass: 50
    Plasma: 50
    Silver: 50

- type: latheRecipe
  id: Signaller
  result: RemoteSignaller
  category: Parts
  completetime: 2
  materials:
    Steel: 100
    Plastic: 200
    Glass: 100

- type: latheRecipe
  id: SignallerAdvanced
  result: RemoteSignallerAdvanced
  category: Parts
  completetime: 2
  materials:
    Steel: 100
    Plastic: 200
    Plasma: 100

- type: latheRecipe
  id: AnomalyLocator
  result: AnomalyLocatorEmpty
  category: Tools
  completetime: 3
  materials:
    Steel: 400
    Glass: 100

- type: latheRecipe
  id: AnomalyLocatorWide
  result: AnomalyLocatorWideEmpty
  category: Tools
  completetime: 3
  materials:
    Steel: 400
    Glass: 100

- type: latheRecipe
  id: AnomalyScanner
  result: AnomalyScanner
  category: Tools
  completetime: 2
  materials:
    Plastic: 200
    Glass: 150

- type: latheRecipe
  id: WeaponPistolCHIMP
  result: WeaponPistolCHIMP
  category: Tools
  completetime: 5
  materials:
    Steel: 500
    Glass: 400

- type: latheRecipe
  id: WeaponGauntletGorilla
  result: WeaponGauntletGorilla
  category: Tools
  completetime: 5
  materials:
    Steel: 1500
    Plastic: 300
    Glass: 500
    Plasma: 500
    Silver: 250

- type: latheRecipe
  id: ClothingBackpackHolding
  result: ClothingBackpackHolding
  completetime: 5
  materials:
    Steel: 2000
    Silver: 750
    Plasma: 1250 #Higher Plasma due to it needing less bluespace
    Uranium: 150
    Bluespace: 300 #DeltaV: Bluespace Exists

- type: latheRecipe
  id: ClothingBackpackSatchelHolding
  result: ClothingBackpackSatchelHolding
  completetime: 5
  materials:
    Steel: 2000
    Silver: 750
    Plasma: 1250 #Higher Plasma due to it needing less bluespace
    Uranium: 150
    Bluespace: 300 #DeltaV: Bluespace Exists

- type: latheRecipe
  id: ClothingBackpackDuffelHolding
  result: ClothingBackpackDuffelHolding
  completetime: 5
  materials:
    Steel: 2000
    Silver: 750
    Plasma: 1250 #Higher Plasma due to it needing less bluespace
    Uranium: 150
    Bluespace: 300 #DeltaV: Bluespace Exists

- type: latheRecipe
  id: OreBagOfHolding
  result: OreBagOfHolding
  completetime: 5
  materials:
    Steel: 2000
    Silver: 750
    Plasma: 1000 #DeltaV: Bluespace Exists so less plasma used, no uranium
    Bluespace: 200 #DeltaV: Bluespace Exists

- type: latheRecipe
  id: ClothingMaskWeldingGas
  result: ClothingMaskWeldingGas
  completetime: 3
  materials:
    Steel: 600
    Glass: 200

# - type: latheRecipe
#   id: WeaponForceGun
#   result: WeaponForceGun
#   category: Tools
#   completetime: 5
#   materials:
#     Steel: 500
#     Glass: 400
#    Silver: 200

- type: latheRecipe
  id: DeviceQuantumSpinInverter
  result: DeviceQuantumSpinInverter
  completetime: 5
  materials:
    Steel: 700
    Glass: 100
    Uranium: 100

- type: latheRecipe
  id: WeaponProtoKineticAccelerator
  result: WeaponProtoKineticAccelerator
  category: Weapons
  completetime: 5
  materials:
    Steel: 1000
    Glass: 500
    Silver: 100

# - type: latheRecipe
#   id: WeaponTetherGun
#   result: WeaponTetherGun
#   category: Tools
#   completetime: 5
#   materials:
#     Steel: 500
#     Glass: 400
#     Silver: 100

# - type: latheRecipe
#   id: WeaponGrapplingGun
#   result: WeaponGrapplingGun
#   category: Tools
#   completetime: 5
#   materials:
#     Steel: 500
#     Glass: 400
#     Gold: 100

- type: latheRecipe
  id: ClothingEyesNightVisionGoggles
  result: ClothingEyesNightVisionGoggles
  completetime: 2
  materials:
    Steel: 200
    Glass: 100
    Silver: 100
    Gold: 100

- type: latheRecipe
  id: ClothingEyesNightVisionDiagnosticGoggles
  result: ClothingEyesNightVisionDiagnosticGoggles
  completetime: 2
  materials:
    Steel: 200
    Glass: 100
    Silver: 100
    Gold: 100

- type: latheRecipe
  id: ClothingEyesThermalVisionGoggles
  result: ClothingEyesThermalVisionGoggles
  completetime: 2
  materials:
    Steel: 200
    Glass: 100
    Silver: 100
    Gold: 100
