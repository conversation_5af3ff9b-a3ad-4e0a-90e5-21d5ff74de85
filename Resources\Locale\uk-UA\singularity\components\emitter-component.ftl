### EmitterComponent

# Shows when attempting to turn the emitter on or off without proper access
comp-emitter-access-locked = {$target} заблоковано!

# Shows when turning the emitter on/off
comp-emitter-turned-on = {$target} вмикається.

comp-emitter-turned-off = {$target} вимикається.

# Shows if the user attempts to activate the emitter while it's un-anchored.
comp-emitter-not-anchored = {$target} не пригвинчений до підлоги!

# Upgrades
emitter-component-upgrade-fire-rate = скорострільність

emitter-component-current-type = Поточний вибраний тип: {$type}.
emitter-component-type-set = Встановлено тип: {$type}
