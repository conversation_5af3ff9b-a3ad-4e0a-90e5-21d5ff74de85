- type: shader
  id: CircleMask
  kind: source
  path: "/Textures/Shaders/circle_mask.swsl"
  params:
    CircleRadius: 15.0
    CircleMinDist: 0.0
    CirclePow: 0.5
    CircleMax: 4.0
    CircleMult: 0.5

- type: shader
  id: GradientCircleMask
  kind: source
  path: "/Textures/Shaders/gradient_circle_mask.swsl"

- type: shader
  id: WorldGradientCircle
  kind: source
  path: "/Textures/Shaders/world_gradient_circle.swsl"

- type: shader
  id: ColoredScreenBorder
  kind: source
  path: "/Textures/Shaders/colored_screen_border.swsl"

- type: shader
  id: FlashedEffect
  kind: source
  path: "/Textures/Shaders/flashed_effect.swsl"

- type: shader
  id: Singularity
  kind: source
  path: "/Textures/Shaders/singularity.swsl"

- type: shader
  id: Radiation
  kind: source
  path: "/Textures/Shaders/radiation.swsl"
  params:
    positionInput: 0,0
    life: 0

- type: shader
  id: Rainbow
  kind: source
  path: "/Textures/Shaders/rainbow.swsl"

- type: shader
  id: CameraStatic
  kind: source
  path: "/Textures/Shaders/camera_static.swsl"

- type: shader
  id: Drunk
  kind: source
  path: "/Textures/Shaders/drunk.swsl"

- type: shader
  id: Drowsiness
  kind: source
  path: "/Textures/Shaders/radial_blur.swsl"

- type: shader
  id: Texture
  kind: source
  path: "/Textures/Shaders/texture.swsl"
  params:
    positionInput: 0, 0
    pixelSize: 32, 32
    alphaCutoff: 0
    removeTransparency: false

- type: shader
  id: BlurryVisionX
  kind: source
  path: "/Textures/Shaders/blurryx.swsl"

- type: shader
  id: BlurryVisionY
  kind: source
  path: "/Textures/Shaders/blurryy.swsl"

# cloaking distortion effect
- type: shader
  id: Stealth
  kind: source
  path: "/Textures/Shaders/stealth.swsl"

- type: shader
  id: PaperStamp
  kind: source
  path: "/Textures/Shaders/paperstamp.swsl"

# Simple horizontal cut
- type: shader
  id: HorizontalCut
  kind: source
  path: "/Textures/Shaders/hcut.swsl"

# Stylized simulation of blindness
- type: shader
  id: Cataracts
  kind: source
  path: "/Textures/Shaders/cataracts.swsl"

- type: shader
  id: SaturationScale
  kind: source
  path: "/Textures/Shaders/saturationscale.swsl"

  # Flight shaders

- type: shader
  id: Flap
  kind: source
  path: "/Textures/Shaders/flap.swsl"

  # Shadowkin shaders

- type: shader
  id: ColorTint
  kind: source
  path: "/Textures/Shaders/color_tint.swsl"

- type: shader
  id: Ethereal
  kind: source
  path: "/Textures/Shaders/ethereal.swsl"

- type: shader
  id: NightVision
  kind: source
  path: "/Textures/Shaders/nightvision.swsl"

- type: shader  
  id: Hologram
  kind: source
  path: "/Textures/Shaders/hologram.swsl"