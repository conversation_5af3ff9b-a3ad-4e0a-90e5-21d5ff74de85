- type: entity
  id: DecorFloorBase
  abstract: true
  placement:
    mode: SnapgridCenter
  components:
  - type: Sprite
    sprite: Structures/Decoration/cave_decor.rsi
    netsync: false
    noRot: true
    drawdepth: FloorObjects
  - type: Damageable
    damageModifierSet: Wood
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Transform
    anchored: true
  - type: Physics
    bodyType: Static
    canCollide: false
  - type: Clickable
  - type: InteractionOutline
# No fixture on this base, inherit from further down for fixture

# Cave Decor
- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard1
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_drought_ns-1

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard2
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_drought_ns-2

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard3
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_drought_ns-3

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard4
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_drought_ns-4

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard5
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_drought_ns-5

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard6
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_drought_ns-6

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard7
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_drought_we-1

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard8
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_drought_we-2

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard9
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_drought_we-3

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard10
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_drought_we-4

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard11
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_drought_we-5

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard12
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_drought_we-6

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard13
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_mammoth_ns-1

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard14
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_mammoth_ns-2

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard15
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_mammoth_ns-3

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard16
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_mammoth_ns-4

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard17
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_mammoth_ns-5

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard18
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_mammoth_ns-6

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard19
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_mammoth_we-1

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard20
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_mammoth_we-2

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard21
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_mammoth_we-3

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard22
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_mammoth_we-4

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard23
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_mammoth_we-5

- type: entity
  parent: DecorFloorBase
  id: DecorFloorBoard24
  name: "дошка підлоги"
  description: "Не допускайте потрапляння бруду на ноги."
  components:
  - type: Sprite
    state: boards_mammoth_we-6

- type: entity
  parent: DecorFloorBase
  id: DecorStalagmite1
  name: "сталагміт"
  description: "Гострі камені! Кліщі йдуть вгору, кліщі приходять..."
  components:
  - type: Sprite
    state: stalagmite
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
            radius: 0.20
        density: 1000
        mask:
        - MachineMask
        layer:
        - MachineLayer

- type: entity
  parent: DecorStalagmite1
  id: DecorStalagmite2
  components:
  - type: Sprite
    state: stalagmite1

- type: entity
  parent: DecorStalagmite1
  id: DecorStalagmite3
  components:
  - type: Sprite
    state: stalagmite2

- type: entity
  parent: DecorStalagmite1
  id: DecorStalagmite4
  components:
  - type: Sprite
    state: stalagmite3

- type: entity
  parent: DecorStalagmite1
  id: DecorStalagmite5
  components:
  - type: Sprite
    state: stalagmite4

- type: entity
  parent: DecorStalagmite1
  id: DecorStalagmite6
  components:
  - type: Sprite
    state: stalagmite5

- type: entity
  parent: DecorStalagmite1
  id: DecorMinecart
  name: "мінно-вибуховий пристрій"
  description: "Здається, вона впала..."
  components:
  - type: Sprite
    state: minecart_fallen

- type: entity
  parent: DecorFloorBase
  id: DecorSignLeftMine
  name: "знак"
  description: "Знак міни, що вказує ліворуч"
  components:
  - type: Sprite
    state: sign_left

- type: entity
  parent: DecorFloorBase
  id: DecorSignRightMine
  name: "знак"
  description: "Знак, що вказує направо."
  components:
  - type: Sprite
    state: sign_right

# World Decor
- type: entity
  parent: DecorFloorBase
  id: DecorFloorWorldBase
  abstract: true
  components:
  - type: Sprite
    sprite: Structures/Decoration/world.rsi

- type: entity
  parent: DecorFloorWorldBase
  id: DecorFloorPaper
  name: "розкиданий папір"
  description: "Купа паперів"
  suffix: 8 states
  components:
  - type: Sprite
    sprite: Structures/Decoration/world.rsi
    state: scattered_papers
    # add destruction drop for materials

- type: entity
  parent: DecorFloorWorldBase
  id: DecorFloorPaper1
  suffix: 4 states
  name: "розкиданий папір"
  description: "Купа паперів"
  components:
  - type: Sprite
    state: papers_1

- type: entity
  parent: DecorFloorPaper1
  id: DecorFloorPaper2
  components:
  - type: Sprite
    state: papers_2

- type: entity
  parent: DecorFloorPaper1
  id: DecorFloorPaper3
  components:
  - type: Sprite
    state: papers_3

- type: entity
  parent: DecorFloorWorldBase
  id: DecorFloorScrapwood
  name: "обрізки деревини"
  description: "обрізки деревини"
  suffix: 6 states
  components:
  - type: Sprite
    state: woodscrap

- type: entity
  parent: DecorFloorWorldBase
  id: DecorFloorBrickrubble
  name: "цегляний щебінь"
  description: "цегляний щебінь"
  suffix: "6 states"
  components:
  - type: Sprite
    state: brickrubble

- type: entity
  parent: DecorFloorWorldBase
  id: DecorFloorCardboard
  name: "картонні коробки"
  description: "картонні коробки для брухту"
  suffix: "6 states"
  components:
  - type: Sprite
    state: cardboard
    # add destruction drop for materials

- type: entity
  parent: DecorFloorWorldBase
  id: DecorFloorPallet
  name: "піддон"
  description: "дерев'яний піддон."
  suffix: "2 states"
  components:
  - type: Sprite
    state: pallet
        # add destruction drop for materials

- type: entity
  parent: DecorFloorWorldBase
  id: DecorFloorPalletStack
  name: "штабель палет"
  description: "штабель дерев'яних палет"
  suffix: "2 states"
  components:
  - type: Sprite
    state: pallet_stack
        # add destruction drop for materials
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
            radius: 0.20
        density: 1000
        mask:
        - MachineMask
        layer:
        - MachineLayer

- type: entity
  parent: DecorFloorPalletStack
  id: DecorFloorBrickStack
  name: "цегляний штабель"
  description: "акуратний штабель цегли"
  components:
  - type: Sprite
    state: brickpile

- type: entity
  parent: DecorFloorWorldBase
  id: DecorFloorBookstack1
  name: "книжковий стелаж"
  description: "стос книг"
  components:
  - type: Sprite
    state: bookstack_1
        # add destruction drop for materials

- type: entity
  parent: DecorFloorBookstack1
  id: DecorFloorBookstack2
  components:
  - type: Sprite
    state: bookstack_2

- type: entity
  parent: DecorFloorBookstack1
  id: DecorFloorBookstack3
  components:
  - type: Sprite
    state: bookstack_3

- type: entity
  parent: DecorFloorWorldBase
  id: DecorFloorBookPile1
  name: "книжковий стос"
  description: "стос книг"
  components:
  - type: Sprite
    state: bookpile_1
        # add destruction drop for materials

- type: entity
  parent: DecorFloorBookPile1
  id: DecorFloorBookPile2
  components:
  - type: Sprite
    state: bookpile_2

- type: entity
  parent: DecorFloorBookPile1
  id: DecorFloorBookPile3
  components:
  - type: Sprite
    state: bookpile_3

- type: entity
  parent: DecorFloorBookPile1
  id: DecorFloorBookPile4
  components:
  - type: Sprite
    state: bookpile_4

- type: entity
  parent: DecorFloorBookPile1
  id: DecorFloorBookPile5
  components:
  - type: Sprite
    state: bookpile_5

- type: entity
  parent: DecorFloorBookPile1
  id: DecorFloorBookPile6
  components:
  - type: Sprite
    state: bookpile_6

- type: entity
  parent: DecorFloorWorldBase
  id: DecorFloorFood1
  name: "продукти харчування"
  description: "деякі старі продукти"
  components:
  - type: Sprite
    state: foodstuff_1

- type: entity
  parent: DecorFloorFood1
  id: DecorFloorFood2
  components:
  - type: Sprite
    state: foodstuff_2

- type: entity
  parent: DecorFloorFood1
  id: DecorFloorFood3
  components:
  - type: Sprite
    state: foodstuff_3

- type: entity
  parent: DecorFloorFood1
  id: DecorFloorFood4
  components:
  - type: Sprite
    state: foodstuff_4

- type: entity
  parent: DecorFloorFood1
  id: DecorFloorFood5
  components:
  - type: Sprite
    state: foodstuff_5

- type: entity
  parent: DecorFloorFood1
  id: DecorFloorFood6
  components:
  - type: Sprite
    state: foodstuff_6

- type: entity
  parent: DecorFloorWorldBase
  id: DecorFloorTrashbags1
  name: "мішки для сміття"
  description: "кілька старих мішків для сміття"
  components:
  - type: Sprite
    state: trashbags_1
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
            radius: 0.20
        density: 1000
        mask:
        - MachineMask
        layer:
        - MachineLayer

- type: entity
  parent: DecorFloorTrashbags1
  id: DecorFloorTrashbags2
  components:
  - type: Sprite
    state: trashbags_2

- type: entity
  parent: DecorFloorTrashbags1
  id: DecorFloorTrashbags3
  components:
  - type: Sprite
    state: trashbags_3

- type: entity
  parent: DecorFloorTrashbags1
  id: DecorFloorTrashbags4
  components:
  - type: Sprite
    state: trashbags_4

- type: entity
  parent: DecorFloorTrashbags1
  id: DecorFloorTrashbags5
  components:
  - type: Sprite
    state: trashbags_5

- type: entity
  parent: DecorFloorTrashbags1
  id: DecorFloorTrashbags6
  components:
  - type: Sprite
    state: trashbags_6

- type: entity
  parent: DecorFloorWorldBase
  id: DecorFloorGlass1
  name: "скляні пляшки"
  description: "старі уламки скла"
  components:
  - type: Sprite
    state: glass_1
    # add glass shard destruction

- type: entity
  parent: DecorFloorGlass1
  id: DecorFloorGlass2
  components:
  - type: Sprite
    state: glass_2

- type: entity
  parent: DecorFloorGlass1
  id: DecorFloorGlass3
  components:
  - type: Sprite
    state: glass_3

- type: entity
  parent: DecorFloorGlass1
  id: DecorFloorGlass4
  components:
  - type: Sprite
    state: glass_4

- type: entity
  parent: DecorFloorGlass1
  id: DecorFloorGlass5
  components:
  - type: Sprite
    state: glass_5

- type: entity
  parent: DecorFloorGlass1
  id: DecorFloorGlass6
  components:
  - type: Sprite
    state: glass_6

- type: entity
  parent: DecorFloorWorldBase
  id: DecorSignMines
  name: "міни"
  description: "небезпека мін і смерті..."
  components:
  - type: Sprite
    state: mine_sign

- type: entity
  parent: DecorFloorWorldBase
  id: DecorFloorSkeleton
  name: "скелет"
  description: "виглядає трохи гірше на вигляд"
  components:
  - type: Sprite
    state: skeleton

- type: entity
  parent: DecorFloorWorldBase
  id: DecorBarrels
  name: "бочки"
  description: "купа старих іржавих бочок."
  components:
  - type: Sprite
    layers:
    - state: barrels1
      map: [ "body" ]
  - type: RandomSprite
    available:
      - body:
          barrels1: ""
          barrels2: ""
          barrels3: ""
          barrels4: ""
          barrels5: ""
          barrels6: ""
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
            radius: 0.3
        density: 1000
        mask:
        - MachineMask
        layer:
        - MachineLayer

- type: entity
  parent: DecorFloorSkeleton
  id: DecorFloorSkeletonOver
  suffix: draws over objects
  components:
  - type: Sprite
    drawdepth: Mobs
