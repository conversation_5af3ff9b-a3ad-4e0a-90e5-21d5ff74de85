- type: entity
  id: SpawnPointGhostIfrit
  name: "точка народження гост-ролі"
  suffix: Ifrit
  parent: MarkerBase
  categories: [ HideSpawnMenu ]
  components:
  - type: GhostRoleMobSpawner
    prototype: MobIfritFamiliar
  - type: GhostRole
    name: Mystagogue's Ifrit
    description: Obey the mystagogue. Defend the oracle.
    rules: You are a servant of the mystagogue. Obey them directly.
  - type: Sprite
    sprite: Markers/jobs.rsi
    layers:
      - state: green
      - sprite: Mobs/Animals/bat.rsi
        state: bat

- type: entity
  id: SpawnPointGhostFugitive
  name: "точка народження гост-ролі"
  parent: MarkerBase
  categories: [ HideSpawnMenu ]
  components:
  # - type: GhostRoleMobSpawner
  #   prototype: MobHumanFugitive # Todo
  - type: GhostRoleAntagSpawner
  - type: GhostRole
    name: Fugitive
    description: You're an escaped prisoner. Make it out alive.
    rules: |
      You are the lightest of antags.
      Murderboning = ban and whitelist removal.
  - type: Sprite
    sprite: Markers/jobs.rsi
    layers:
      - state: green
      - state: prisoner

- type: entity
  id: SpawnPointLocationMidRoundAntag
  name: "можлива точка спавну"
  suffix: MidRoundAntag
  parent: MarkerBase
  components:
  - type: Sprite
    sprite: Markers/jobs.rsi
    layers:
      - state: green
      - state: prisoner
  # - type: MidRoundAntagSpawnLocation # When MidRoundAntag?

#- type: entity
#  id: SpawnPointGhostVampSpider
#  name: ghost role spawn point
#  suffix: Vampire spider
#  parent: MarkerBase
#  categories: [ HideSpawnMenu ]
#  components:
#  - type: GhostRoleMobSpawner
#    prototype: MobGiantSpiderVampireAngry
#  - type: GhostRole
#    makeSentient: true
#    name: ghost-role-information-giant-spider-vampire-name
#    description: ghost-role-information-giant-spider-vampire-description
#    rules: No antagonist restrictions. Just don't talk in emote; you have telepathic chat.
#  - type: Sprite
#    sprite: Markers/jobs.rsi
#    layers:
#      - state: green
#      - sprite: Mobs/Animals/bat.rsi
#        state: bat
