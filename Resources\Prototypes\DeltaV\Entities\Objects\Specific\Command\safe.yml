﻿- type: entity
  parent: FireAxeCabinet
  id: SpareIdCabinet
  name: "шафа запасних ідентифікаторів"
  description: "Є невелика етикетка з написом \"Тільки для екстреного використання\"."
  components:
    - type: Sprite
      sprite: DeltaV/Structures/Wallmounts/idcard_cabinet.rsi
      layers:
        - state: cabinet
        - state: card
          map: ["enum.ItemCabinetVisualLayers.ContainsItem"]
          visible: true
        - state: glass
          map: ["enum.ItemCabinetVisualLayers.Door"]
    - type: ItemCabinet
      cabinetSlot:
        ejectOnInteract: true
        whitelist:
          components:
            - IdCard
    - type: AccessReader
      access: [["DV-SpareSafe"]]
    - type: SpareIDSafe

- type: entity
  id: SpareIdCabinetFilled
  parent: [SpareIdCabinet,FireAxeCabinetFilled]
  suffix: Filled
  components:
    - type: ItemCabinet
      cabinetSlot:
        startingItem: CaptainIDCard
        ejectOnInteract: true
        whitelist:
          components:
            - IdCard
