﻿- type: entity
  name: "ліхтар"
  parent: BaseItem
  id: Lantern
  description: "Святе світло вказує шлях."
  components:
    - type: HandheldLight
      addPrefix: true
      blinkingBehaviourId: blinking
      radiatingBehaviourId: radiating
    - type: LightBehaviour
      behaviours:
        - !type:FadeBehaviour
          id: radiating
          maxDuration: 2.0
          startValue: 3.0
          endValue: 2.0
          isLooped: true
          reverseWhenFinished: true
        - !type:PulseBehaviour
          id: blinking
          interpolate: Nearest
          maxDuration: 1.0
          minValue: 0.1
          maxValue: 2.0
          isLooped: true
    - type: Sprite
      sprite: Objects/Tools/lantern.rsi
      layers:
        - state: lantern
        - state: lantern-on
          shader: unshaded
          visible: false
          map: [ "light" ]
    - type: Item
      sprite: Objects/Tools/lantern.rsi
      heldPrefix: off
    - type: PointLight
      enabled: false
      radius: 3
      energy: 2.5
      color: "#FFC458"
      netsync: false
    - type: Appearance
    - type: ToggleableLightVisuals
    - type: PowerCellSlot
      cellSlotId: cell_slot
    - type: ItemSlots
      slots:
        cell_slot:
          name: power-cell-slot-component-slot-name-default
          startingItem: PowerCellMedium
    - type: ContainerContainer
      containers:
        cell_slot: !type:ContainerSlot {}
    - type: StealTarget
      stealGroup: LAMP
    - type: Clothing
      sprite: Objects/Tools/lantern.rsi
      equippedPrefix: off
      quickEquip: false
      slots:
        - Belt
    - type: Tag
      tags:
      - Flashlight

- type: entity
  parent: Lantern
  id: LanternFlash
  suffix: Flash
  components:
    - type: Sprite
      sprite: Objects/Tools/lantern.rsi
      layers:
      - state: lantern
        map: [ "enum.FlashVisuals.BaseLayer" ]
      - state: lantern-on
        shader: unshaded
        visible: false
        map: [ "light" ]
      - state: flashing
        map: [ "enum.FlashVisuals.LightLayer" ]
        visible: false
    - type: PointLight
      radius: 5
      energy: 10
    - type: Flash
    - type: LimitedCharges
      maxCharges: 15
      charges: 15
    - type: MeleeWeapon
      damage:
        types:
          Blunt: 0 # melee weapon to allow flashing individual targets
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.FlashVisuals.Burnt:
          enum.FlashVisuals.BaseLayer:
            True: {state: burnt}
        enum.FlashVisuals.Flashing:
          enum.FlashVisuals.LightLayer:
            True: {visible: true}
            False: {visible: false}
