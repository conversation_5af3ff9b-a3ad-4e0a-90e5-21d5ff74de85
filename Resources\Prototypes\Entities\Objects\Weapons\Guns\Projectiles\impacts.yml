- type: entity
  id: BulletImpactEffect
  categories: [ HideSpawnMenu ]
  components:
  - type: TimedDespawn
    lifetime: 0.25
  - type: Sprite
    drawdepth: Effects
    layers:
    - shader: unshaded
      map: ["enum.EffectLayers.Unshaded"]
      sprite: Objects/Weapons/Guns/Projectiles/projectiles_tg.rsi
      state: impact_bullet
  - type: EffectVisuals
  - type: Tag
    tags:
      - HideContextMenu

- type: entity
  id: BulletImpactEffectDisabler
  categories: [ HideSpawnMenu ]
  components:
    - type: TimedDespawn
      lifetime: 0.2
    - type: Sprite
      drawdepth: Effects
      layers:
        - shader: unshaded
          map: ["enum.EffectLayers.Unshaded"]
          sprite: Objects/Weapons/Guns/Projectiles/projectiles_tg.rsi
          state: impact_laser_blue
    - type: EffectVisuals
    - type: Tag
      tags:
        - HideContextMenu

- type: entity
  id: BulletImpactEffectOrangeDisabler
  categories: [ HideSpawnMenu ]
  components:
  - type: TimedDespawn
    lifetime: 0.2
  - type: Sprite
    drawdepth: Effects
    layers:
    - shader: unshaded
      map: ["enum.EffectLayers.Unshaded"]
      sprite: Objects/Weapons/Guns/Projectiles/projectiles_tg.rsi
      state: impact_laser_greyscale
      color: orangered
  - type: EffectVisuals
  - type: Tag
    tags:
    - HideContextMenu

- type: entity
  id: BulletImpactEffectKinetic
  categories: [ HideSpawnMenu ]
  components:
  - type: TimedDespawn
    lifetime: 0.2
  - type: Sprite
    drawdepth: Effects
    layers:
    - shader: unshaded
      map: ["enum.EffectLayers.Unshaded"]
      sprite: Effects/arcs.rsi
      state: punch
  - type: EffectVisuals
  - type: Tag
    tags:
      - HideContextMenu
