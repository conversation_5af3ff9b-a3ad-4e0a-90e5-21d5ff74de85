#bureaucracy
- type: construction
  id: FilingCabinet
  name: "картотека"
  description: "Шафа для всіх ваших картотечних потреб."
  graph: FilingCabinet
  startNode: start
  targetNode: filingCabinet
  category: construction-category-storage
  icon:
    sprite: Structures/Storage/cabinets.rsi
    state: filingcabinet
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  id: TallCabinet
  name: "висока шафа"
  description: "Шафа для всіх ваших картотечних потреб."
  graph: FilingCabinet
  startNode: start
  targetNode: tallCabinet
  category: construction-category-storage
  icon:
    sprite: Structures/Storage/cabinets.rsi
    state: tallcabinet
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  id: ChestDrawer
  name: "шухляда комода"
  description: "Маленька шухляда для всіх ваших картотечних потреб, тепер на колесах!"
  graph: FilingCabinet
  startNode: start
  targetNode: chestDrawer
  category: construction-category-storage
  icon:
    sprite: Structures/Storage/cabinets.rsi
    state: chestdrawer
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

# ItemCabinets
- type: construction
  id: ShowCase
  name: "вітрина"
  description: "Міцна вітрина для дорогого експоната."
  graph: GlassBox
  startNode: start
  targetNode: glassBox
  category: construction-category-storage
  icon:
    sprite: Structures/Storage/glassbox.rsi
    state: icon
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

# Shelfs
# Normals
- type: construction
  id: ShelfWood
  name: "дерев'яна полиця"
  description: "Зручне місце, щоб розмістити, ну, що завгодно."
  graph: Shelf
  startNode: start
  targetNode: ShelfWood
  icon:
    sprite: Structures/Storage/Shelfs/wood.rsi
    state: base
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true
  conditions:
    - !type:WallmountCondition

- type: construction
  id: ShelfMetal
  name: "металева полиця"
  description: "Міцне місце, де можна розмістити, ну, що завгодно."
  graph: Shelf
  startNode: start
  targetNode: ShelfMetal
  icon:
    sprite: Structures/Storage/Shelfs/metal.rsi
    state: base
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true
  conditions:
    - !type:WallmountCondition

- type: construction
  id: ShelfGlass
  name: "скляна полиця"
  description: "Як звичайна полиця! Але тендітна і без стінок!"
  graph: Shelf
  startNode: start
  targetNode: ShelfGlass
  icon:
    sprite: Structures/Storage/Shelfs/glass.rsi
    state: base
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true
  conditions:
    - !type:WallmountCondition

# Reinforced
- type: construction
  id: ShelfRWood
  name: "міцна дерев'яна полиця"
  description: "Ідеальне місце для зберігання всіх ваших вінтажних платівок."
  graph: Shelf
  startNode: start
  targetNode: ShelfRWood
  icon:
    sprite: Structures/Storage/Shelfs/wood.rsi
    state: rbase
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true
  conditions:
    - !type:WallmountCondition

- type: construction
  id: ShelfRMetal
  name: "міцна металева полиця"
  description: "Гарна і міцна, вона надійно захистить ваші комп'ютерні цінності."
  graph: Shelf
  startNode: start
  targetNode: ShelfRMetal
  icon:
    sprite: Structures/Storage/Shelfs/metal.rsi
    state: rbase
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true
  conditions:
    - !type:WallmountCondition

- type: construction
  id: ShelfRGlass
  name: "міцна скляна полиця"
  description: "Прозорий, пристойної міцності, блискучий пластиковий корпус. Що тут може не сподобатися?"
  graph: Shelf
  startNode: start
  targetNode: ShelfRGlass
  icon:
    sprite: Structures/Storage/Shelfs/glass.rsi
    state: rbase
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true
  conditions:
    - !type:WallmountCondition

# Departmental
- type: construction
  id: ShelfBar
  name: "барна стійка"
  description: "Зручне місце для всієї вашої зайвої випивки, спеціально розроблене, щоб вмістити більше пляшок!"
  graph: Shelf
  startNode: start
  targetNode: ShelfBar
  icon:
    sprite: Structures/Storage/Shelfs/Departments/Service/bar.rsi
    state: base
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true
  conditions:
    - !type:WallmountCondition

- type: construction
  id: ShelfKitchen
  name: "полиця для приготування їжі"
  description: "Тримає ваші ножі, спеції та все інше!"
  graph: Shelf
  startNode: start
  targetNode: ShelfKitchen
  icon:
    sprite: Structures/Storage/Shelfs/Departments/Service/kitchen.rsi
    state: base
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true
  conditions:
    - !type:WallmountCondition

- type: construction
  id: ShelfChemistry
  name: "хімічний полигон"
  description: "Ідеально підходить для зберігання найважливіших хімікатів у безпеці, і не потрапляє до незграбних рук клоунів!"
  graph: Shelf
  startNode: start
  targetNode: ShelfChemistry
  icon:
    sprite: Structures/Storage/Shelfs/Departments/Medical/chemistry.rsi
    state: base
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true
  conditions:
    - !type:WallmountCondition
