- type: entity
  save: false
  abstract: true
  id: MobSiliconBase
  components:
  - type: Renamable
  - type: UserInterface
    interfaces:
      enum.SharedRenamableInterfaceKey.Key:
        type: RenamableBoundUserInterface
  - type: LagCompensation
  - type: Reactive
    groups:
      Acidic: [Touch]
  - type: Clickable
  - type: Damageable
    damageContainer: Inorganic
  - type: InteractionOutline
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 50
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: MovementSpeedModifier
    baseWalkSpeed : 3
    baseSprintSpeed : 4
  - type: Sprite
    noRot: true
    drawdepth: Mobs
  - type: NpcFactionMember
    factions:
    - SimpleNeutral
  - type: Access
    tags:
    - BasicSilicon
  - type: IntrinsicRadioReceiver
  - type: IntrinsicRadioTransmitter
    channels:
    - Binary
  - type: ActiveRadio
    channels:
    - Binary
    - Common
  - type: HealthExaminable
    examinableTypes:
    - Blunt
    - Slash
    - Piercing
    - Heat
    - Shock
    locPrefix: silicon
  - type: MovedByPressure
  - type: Physics
    bodyType: KinematicController # Same for all inheritors
  - type: StatusEffects
    allowed:
    - Stun
    - KnockedDown
    - SlowedDown
    - Stutter
    - Electrocution
  - type: NameIdentifier
    group: GenericNumber
  - type: Repairable
    doAfterDelay: 8
    fuelCost: 15
  - type: Pullable
  - type: Tag
    tags:
    - DoorBumpOpener
    - SiliconMob
  - type: MobState
    allowedStates:
      - Alive
      - Dead
  - type: MobThresholds
    thresholds:
      0: Alive
      120: Dead
    stateAlertDict:
      Alive: BorgHealth
    showOverlays: false
  - type: Stamina
    critThreshold: 120
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:TriggerBehavior
    - trigger:
        !type:DamageTrigger
        damage: 120
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Input
    context: "human"
  - type: InputMover
  - type: MobMover
  - type: Body
    prototype: Bot
  - type: GuideHelp
    guides:
    - Robotics
  - type: Speech
    speechVerb: Robotic
  - type: TypingIndicator
    proto: robot
  - type: ZombieImmune
  - type: StepTriggerImmune
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    - RobotTalk
    understands:
    - TauCetiBasic
    - RobotTalk
  - type: PsionicInsulation

- type: entity
  parent: MobSiliconBase
  id: MobHonkBot
  name: "гудкобот"
  description: "Жахливо."
  components:
  - type: SpamEmitSound
    minInterval: 2
    maxInterval: 12
    sound:
      collection: BikeHorn
  - type: Sprite
    sprite: Mobs/Silicon/Bots/honkbot.rsi
    state: honkbot
  - type: HTN
    rootTask:
      task: HonkbotCompound
  - type: Slippery
    launchForwardsMultiplier: 2
  - type: Speech
    speechVerb: Cluwne
  - type: StepTrigger
    triggerGroups:
      types:
      - SlipEntity
    intersectRatio: 0.2
  - type: Fixtures
    fixtures:
      slips:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        hard: false
        layer:
          - SlipLayer
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 20
        mask:
        - MobMask
        layer: # Goobstation
        - MobLayer
  - type: Construction
    graph: HonkBot
    node: bot
  - type: GhostRole
    makeSentient: true
    name: ghost-role-information-honkbot-name
    description: ghost-role-information-honkbot-description
    rules: ghost-role-information-freeagent-rules
    mindRoles:
    - MindRoleGhostRoleFreeAgent
    raffle:
      settings: default
  - type: GhostTakeoverAvailable
  - type: InteractionPopup
    interactSuccessString: petting-success-honkbot
    interactFailureString: petting-failure-honkbot
    interactSuccessSound:
      path: /Audio/Items/bikehorn.ogg

- type: entity
  parent: MobHonkBot
  id: MobJonkBot
  name: "сміхобот"
  description: "Жахливо."
  components:
  - type: SpamEmitSound
    sound:
      collection: CluwneHorn
  - type: Sprite
    state: jonkbot
  - type: Construction
    graph: JonkBot
    node: bot
  - type: GhostRole
    makeSentient: true
    name: ghost-role-information-jonkbot-name
    description: ghost-role-information-jonkbot-description
    rules: ghost-role-information-freeagent-rules
    mindRoles:
    - MindRoleGhostRoleFreeAgent
    raffle:
      settings: default
  - type: InteractionPopup
    interactSuccessSound:
      path: /Audio/Items/brokenbikehorn.ogg
  - type: Vocal
    sounds:
      Unsexed: Cluwne
  - type: ExplodeOnTrigger
  - type: Explosive
    explosionType: Default
    maxIntensity: 20
    totalIntensity: 10
    intensitySlope: 5
    canCreateVacuum: false

- type: entity
  parent: MobSiliconBase
  id: MobCleanBot
  name: "чистко-бот"
  description: "Повзуча автоматизація тепер загрожує космічним прибиральникам."
  components:
  - type: Sprite
    sprite: Mobs/Silicon/Bots/cleanbot.rsi
    state: cleanbot
  - type: Construction
    graph: CleanBot
    node: bot
  - type: SentienceTarget
    flavorKind: station-event-random-sentience-flavor-mechanical
  - type: Absorbent
    pickupAmount: 10
  - type: UseDelay
    delay: 2
  - type: SolutionRegeneration
    solution: absorbed
    generated:
      reagents:
        - ReagentId: Water
          Quantity: 10
  - type: SolutionPurge
    solution: absorbed
    preserve:
      - Water
    quantity: 10
  - type: SolutionContainerManager
    solutions:
      absorbed:
        maxVol: 50
  - type: MovementSpeedModifier
    baseWalkSpeed: 2
    baseSprintSpeed: 3
  - type: NoSlip
  - type: HTN
    rootTask:
      task: CleanbotCompound
  - type: DrainableSolution
    solution: drainBuffer
  - type: InteractionPopup
    interactSuccessString: petting-success-cleanbot
    interactFailureString: petting-failure-cleanbot
    interactSuccessSound:
      path: /Audio/Ambience/Objects/periodic_beep.ogg

- type: entity
  parent: MobSiliconBase
  id: MobMedibot
  name: "медико-бот"
  description: "Це не замінить лікаря, але краще, ніж нічого."
  components:
  - type: Medibot
    treatments:
      Alive:
        reagent: Tricordrazine
        quantity: 30
        minDamage: 0
        maxDamage: 50
      Critical:
        reagent: Inaprovaline
        quantity: 15
  - type: EmaggableMedibot
    replacements:
      # when you are fine, medibot will help you go sleep
      Alive:
        reagent: ChloralHydrate
        quantity: 15
      # when you are crit, medibot will help you have fun
      Critical:
        reagent: SpaceDrugs
        quantity: 25
  - type: Sprite
    sprite: Mobs/Silicon/Bots/medibot.rsi
    state: medibot
  - type: HTN
    rootTask:
      task: MedibotCompound
  - type: Construction
    graph: MediBot
    node: bot
  - type: NoSlip
  - type: SentienceTarget
    flavorKind: station-event-random-sentience-flavor-mechanical
  - type: Anchorable
  - type: InteractionPopup
    interactSuccessString: petting-success-medibot
    interactFailureString: petting-failure-medibot
    interactSuccessSound:
      path: /Audio/Ambience/Objects/periodic_beep.ogg
  - type: ShowHealthBars
    damageContainers:
    - Biological
  - type: ShowHealthIcons
    damageContainers:
    - Biological

- type: entity
  parent: MobSiliconBase
  id: MobMimeBot
  name: "мімбот"
  description: "Чому б не помахати мімеботу рукою?"
  components:
  - type: InventorySlots
  - type: Strippable
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
  - type: Sprite
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: mimebot
      sprite: Mobs/Silicon/Bots/mimebot.rsi
  - type: MimePowers
  - type: Construction
    graph: MimeBot
    node: bot
  - type: GhostRole
    makeSentient: true
    name: ghost-role-information-mimebot-name
    description: ghost-role-information-mimebot-description
    rules: ghost-role-information-freeagent-rules
    mindRoles:
    - MindRoleGhostRoleFreeAgent
    raffle:
      settings: default
  - type: GhostTakeoverAvailable
  - type: InteractionPopup
    interactSuccessString: petting-success-mimebot
    interactFailureString: petting-failure-mimebot
  - type: Inventory
    templateId: head

- type: entity
  parent: MobSiliconBase
  id: MobSupplyBot
  name: "supplybot"
  description: "Доставляє вантаж!"
  components:
  - type: Sprite
    sprite: Mobs/Silicon/Bots/supplybot.rsi
    layers:
    - state: supplybot
  - type: GhostRole
    makeSentient: true
    name: ghost-role-information-supplybot-name
    description: ghost-role-information-supplybot-description
    raffle:
      settings: default
  - type: GhostTakeoverAvailable
  - type: Construction
    graph: SupplyBot
    node: bot
  - type: Access
    tags:
    - Cargo
    - Maintenance
    - Salvage
  - type: Dumpable
  - type: Storage
    maxItemSize: Huge
    grid:
    - 0,0,9,3
  - type: UserInterface
    interfaces:
      enum.StorageUiKey.Key:
        type: StorageBoundUserInterface
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
        ents: []
  - type: UnpoweredFlashlight
  - type: PointLight
    enabled: false
    radius: 3.5
    softness: 2
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepBorg
  - type: Tag
    tags:
    - DoorBumpOpener
    - FootstepSound
  - type: ActiveRadio
    channels:
    - Binary
    - Common
    - Supply
