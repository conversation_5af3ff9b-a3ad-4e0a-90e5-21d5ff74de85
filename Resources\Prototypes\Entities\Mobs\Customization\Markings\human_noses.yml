- type: marking
  id: HumanNoseSchnozz
  bodyPart: Snout
  markingCategory: Snout
  followSkinColor: true
  forcedColoring: true
  speciesRestriction: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>]
  sprites:
  - sprite: Mobs/Customization/human_noses.rsi
    state: schnozz

- type: marking
  id: HumanNoseNubby
  bodyPart: Snout
  markingCategory: Snout
  followSkinColor: true
  forcedColoring: true
  speciesRestriction: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>]
  sprites:
  - sprite: Mobs/Customization/human_noses.rsi
    state: nubby

- type: marking
  id: HumanNoseDroop
  bodyPart: Snout
  markingCategory: Snout
  followSkinColor: true
  forcedColoring: true
  speciesRestriction: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>]
  sprites:
  - sprite: Mobs/Customization/human_noses.rsi
    state: droop

- type: marking
  id: HumanNoseBlob
  bodyPart: Snout
  markingCategory: Snout
  followSkinColor: true
  forcedColoring: true
  speciesRestriction: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>]
  sprites:
  - sprite: Mobs/Customization/human_noses.rsi
    state: blob

- type: marking
  id: HumanNoseUppie
  bodyPart: Snout
  markingCategory: Snout
  followSkinColor: true
  forcedColoring: true
  speciesRestriction: [Human, Dwarf, Felinid, Oni, Harpy, Arachne, Lamia]
  sprites:
  - sprite: Mobs/Customization/human_noses.rsi
    state: uppie
