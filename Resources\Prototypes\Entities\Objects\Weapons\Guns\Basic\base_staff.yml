- type: entity
  id: WeaponStaffBase
  abstract: true
  parent: [BaseItem] # Goob edit
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Basic/staves.rsi
  - type: Item
    heldPrefix: staff
    size: Huge # Goob edit
  - type: Gun
    soundEmpty: null # Goobstation
    soundMode: null # Goobstation
    clumsyProof: true # Goobstation
    fireRate: 1.5 # Goob edit
    selectedMode: SemiAuto
    availableModes:
    - SemiAuto
  - type: AmmoCounter
  # All staves recharge. Wands are not.
  - type: RechargeBasicEntityAmmo
    rechargeCooldown: 30
  - type: Tag
    tags:
    - WizardStaff
  - type: Clothing # Goobstation
    quickEquip: false
    slots:
      - Back
      - suitStorage
    clothingVisuals:
      back:
      - state: equipped-BACKPACK
      suitstorage:
      - state: equipped-BACKPACK
  - type: StaticPrice # Goobstation
    price: 30000
