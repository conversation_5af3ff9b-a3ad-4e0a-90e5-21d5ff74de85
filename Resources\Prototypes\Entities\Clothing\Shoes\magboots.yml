- type: entity
  id: BaseIntegratedMagboot
  name: "вбудовані магнітні черевики"
  description: "Набір магнітних замків, вбудованих у костюм."
  abstract: true
  components:
    - type: ToggleClothing
      action: ActionToggleMagboots
    - type: Magboots
      slot: outerClothing
    - type: ItemToggle
      onUse: false # can't really wear it like that

- type: entity
  id: BaseIntegratedManeuveringThrusters
  name: "вбудовані маневрові двигуни"
  description: "Набір маневрових двигунів, вбудованих у костюм."
  abstract: true
  components:
    - type: GasTank
      outputPressure: 42.6
      air:
        # 10 minutes of thrust
        volume: 3.75
        temperature: 293.15
        moles:
          - 0.769267145 # oxygen
          - 0.769267145 # nitrogen
      isInternals: false # So you don't suck on your integrated thrusters
    - type: ActivatableUI
      key: enum.SharedGasTankUiKey.Key
      verbOnly: true
    - type: UserInterface
      interfaces:
        enum.SharedGasTankUiKey.Key:
          type: GasTankBoundUserInterface
    - type: Jetpack
      moleUsage: 0.00085
    - type: CanMoveInAir
    - type: InputMover
      toParent: true
    - type: MovementSpeedModifier
      weightlessAcceleration: 1
      weightlessFriction: 0.3
      weightlessModifier: 1.2

- type: entity
  parent: [ClothingShoesBase, BaseToggleClothing]
  id: ClothingShoesBootsMagBase
  name: "магнітні чоботи"
  description: "Магнітні черевики, часто використовуються під час позашляхової активності, щоб гарантувати, що користувач залишається надійно прикріпленим до транспортного засобу."
  abstract: true
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Boots/magboots-science.rsi
    layers:
    - state: icon
      map: [ "enum.ToggleVisuals.Layer" ]
  - type: Clothing
    sprite: Clothing/Shoes/Boots/magboots-science.rsi
  - type: ToggleClothing
    action: ActionToggleMagboots
  - type: ComponentToggler
    components:
    - type: NoSlip
  - type: Magboots
    activeWalkModifier: 0.85
    activeSprintModifier: 0.8
    changeClothingVisuals: true
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ToggleVisuals.Toggled:
        enum.ToggleVisuals.Layer:
          True: {state: icon-on}
          False: {state: icon}
  - type: StaticPrice
    price: 200
  - type: Tag
    tags:
    - WhitelistChameleon

- type: entity
  parent: [ClothingShoesBootsMagBase]
  id: ClothingShoesBootsMag

- type: entity
  parent: ClothingShoesBootsMag
  id: ClothingShoesBootsMagAdv
  name: "просунуті магнітні чоботи"
  description: "Найсучасніші магнітні черевики, які не гальмують свого власника."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Boots/magboots-advanced.rsi
    state: icon
  - type: Clothing
    sprite: Clothing/Shoes/Boots/magboots-advanced.rsi
  - type: Magboots
    activeWalkModifier: 1
    activeSprintModifier: 1
  - type: Tag
    tags:
    - WhitelistChameleon
    - HighRiskItem
  - type: StaticPrice
    price: 750
  - type: StealTarget
    stealGroup: ClothingShoesBootsMagAdv
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 13
    staminaCost: 15

- type: entity
  parent: ClothingShoesBootsMag
  id: ClothingShoesBootsMagBlinding
  name: "магнітні чоботи сліпучої швидкості"
  description: "Вони виглядатимуть привабливо на такому ловеласі, як ти."
  components:
  - type: Magboots
    activeWalkModifier: 1.10
    activeSprintModifier: 1.10
  - type: StaticPrice
    price: 3000

- type: entity
  parent: [ClothingShoesBootsMagBase, BaseJetpack]
  id: ClothingShoesBootsMagSyndie
  name: "криваво-червоні магнітні чоботи"
  description: "Магнітні черевики, що мають потужну магнітну тягу та інтегровані штовхачі, спроектовані за останнім словом техніки."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Boots/magboots-syndicate.rsi
    state: icon
  - type: Clothing
    sprite: Clothing/Shoes/Boots/magboots-syndicate.rsi
  - type: Magboots
    activeWalkModifier: 0.95
    activeSprintModifier: 0.9
  - type: GasTank
    outputPressure: 42.6
    air:
      # 2 minutes of thrust
      volume: 0.75
      temperature: 293.15
      moles:
        - 0.153853429 # oxygen
        - 0.153853429 # nitrogen
  - type: ActivatableUI
    key: enum.SharedGasTankUiKey.Key
  - type: UserInterface
    interfaces:
      enum.SharedGasTankUiKey.Key:
        type: GasTankBoundUserInterface
  - type: Explosive
    explosionType: Default
    maxIntensity: 20
  - type: Jetpack
    moleUsage: 0.00085
  - type: CanMoveInAir
  - type: InputMover
    toParent: true
  - type: MovementSpeedModifier
    weightlessAcceleration: 1
    weightlessFriction: 0.3
    weightlessModifier: 1.2
  - type: Tag
    tags:
      - WhitelistChameleon
  - type: Item
    sprite: null
    size: Normal

- type: entity
  parent: ClothingShoesBootsMag
  id: ClothingShoesBootsMagEng
  name: "інженерні магчеревики"
  description: "Дорогі магчеревики, виготовлені з легших матеріалів для швидкого реагування інженерів."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Boots/magboots.rsi
    state: icon
  - type: Clothing
    sprite: Clothing/Shoes/Boots/magboots.rsi
  - type: Magboots
    activeWalkModifier: 0.95
    activeSprintModifier: 0.9
  - type: Tag
    tags:
    - WhitelistChameleon
  - type: StaticPrice
    price: 500
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 13
    staminaCost: 15

- type: entity
  parent: ClothingShoesBootsMag
  id: ClothingShoesBootsMagSec
  name: "бойові магчеревики"
  description: "Надміцні магчеревики з додатковим захистом і зручним ремінцем для додаткової зброї."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Boots/magboots-combat.rsi
    state: icon
  - type: Clothing
    sprite: Clothing/Shoes/Boots/magboots-combat.rsi
  - type: Magboots
    activeWalkModifier: 0.82
    activeSprintModifier: 0.77
  - type: Tag
    tags:
    - WhitelistChameleon
  - type: StaticPrice
    price: 500
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 13
    staminaCost: 15
  - type: ClothingSlowOnDamageModifier
    modifier: 0.8
  - type: ClothingModifyStunTime # Goobstation
    modifier: 0.8
  - type: Matchbox
  - type: ItemSlots
    slots:
      item:
        name: clothing-boots-sidearm
        whitelist:
          tags:
          - Knife
          - Sidearm
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.95
        Slash: 0.95
        Piercing: 0.95
        Heat: 0.95
  - type: ContainerContainer
    containers:
      item: !type:ContainerSlot

- type: entity
  id: ActionToggleMagboots
  name: "Перемикач Magboots"
  description: "Вмикає та вимикає магбути."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    itemIconStyle: BigItem
    event: !type:ToggleActionEvent
