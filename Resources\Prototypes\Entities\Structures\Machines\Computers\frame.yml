- type: entity
  id: BaseStructureComputer
  parent: BaseStructure
  abstract: true
  components:
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.35,0.25,0.35"
        density: 190
        mask:
        - MachineMask
        layer:
        - MachineLayer
  - type: InteractionOutline
  - type: Rotatable
  - type: Anchorable
  - type: Construction
    graph: Computer
    node: frameUnsecured
  - type: Sprite
    drawdepth: Objects
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Electronic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:ChangeConstructionNodeBehavior
        node: monitorBroken
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: InteractionVerbs
    allowedVerbs:
    - KnockOn

- type: entity
  id: ComputerFrame
  parent: BaseStructureComputer
  name: "комп'ютерна рама"
  description: "Комп'ютер, що будується."
  components:
  - type: Sprite
    sprite: Structures/Machines/parts.rsi
    layers:
    - state: 0
      map: [ "enum.ConstructionVisuals.Layer" ]
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ConstructionVisuals.Key:
        enum.ConstructionVisuals.Layer:
          frameUnsecured: { state: 0 }
          boardUnsecured: { state: 1 }
          missingWires: { state: 2 }
          monitorMissing: { state: 3 }
          monitorUnsecured: { state: 4 }

- type: entity
  parent: BaseStructureComputer
  id: ComputerBroken
  name: "зламана консоль"
  description: "Цей комп'ютер бачив кращі часи."
  components:
  - type: Construction
    graph: Computer
    node: monitorBroken
  - type: Sprite
    sprite: Structures/Machines/computers.rsi
    drawdepth: Objects
    state: broken
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
          params:
            volume: -6
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
