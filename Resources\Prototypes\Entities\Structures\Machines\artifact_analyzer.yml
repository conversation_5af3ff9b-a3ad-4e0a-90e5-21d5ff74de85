- type: entity
  id: MachineArtifactAnalyzer
  parent: [ BaseMachinePowered, SmallConstructibleMachine ]
  name: "аналізатор артефактів"
  description: "Платформа, здатна виконувати аналіз різних типів артефактів."
  components:
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/artifact_analyzer.rsi
    drawdepth: HighFloorObjects
    layers:
    - state: icon
    - state: unshaded
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
    - state: panel
      map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
  - type: Physics
    bodyType: Static
    canCollide: true
  - type: AmbientSound
    enabled: false
    sound:
      path: /Audio/Machines/scan_loop.ogg
    range: 5
    volume: -8
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.35,-0.35,0.35,0.35"
        density: 190
        mask:
        - MachineMask
        layer:
        - Impassable
        - MidImpassable
        - LowImpassable
        - BulletImpassable
        hard: False
  - type: Transform
    anchored: true
    noRot: false
  - type: ApcPowerReceiver
    powerLoad: 12000
    needsPower: false #only turns on when scanning
  - type: ArtifactAnalyzer
  - type: TraversalDistorter
  - type: ItemPlacer
    whitelist:
      components:
      - Artifact
  - type: WiresPanel
  - type: WiresVisuals
  - type: DeviceNetwork
    deviceNetId: Wired
    receiveFrequencyId: BasicDevice
  - type: DeviceList
  - type: DeviceLinkSink
    ports:
      - ArtifactAnalyzerReceiver
  - type: Machine
    board: ArtifactAnalyzerMachineCircuitboard
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#b53ca1"
  - type: GuideHelp
    guides:
    - Xenoarchaeology
  - type: LitOnPowered
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.PowerDeviceVisuals.Powered:
        enum.PowerDeviceVisualLayers.Powered:
          True: { visible: true }
          False: { visible: false }

- type: entity
  id: MachineTraversalDistorter
  parent: [ BaseMachinePowered, ConstructibleMachine ]
  name: "дисторсійний викривлювач траверси"
  description: "Машина, здатна спотворювати обхід вузлів артефактів."
  components:
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/traversal_distorter.rsi
    drawdepth: FloorObjects
    layers:
    - state: icon
    - state: unshaded
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
  - type: Physics
    bodyType: Static
    canCollide: true
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.35,-0.35,0.35,0.35"
        density: 190
        mask:
        - MachineMask
        layer:
        - Impassable
        - MidImpassable
        - LowImpassable
        hard: False
  - type: Transform
    noRot: false
  - type: UpgradePowerDraw
    powerDrawMultiplier: 0.80
    scaling: Exponential
  - type: TraversalDistorter
  - type: ItemPlacer
    # don't limit the number of artifacts that can be biased
    maxEntities: 0
    whitelist:
      components:
      - Artifact
  - type: PlaceableSurface
    placeCentered: true
  - type: Machine
    board: TraversalDistorterMachineCircuitboard
  - type: Appearance
  - type: GuideHelp
    guides:
    - TraversalDistorter
  - type: GenericVisualizer
    visuals:
      enum.PowerDeviceVisuals.Powered:
        enum.PowerDeviceVisualLayers.Powered:
          True: { visible: true }
          False: { visible: false }

- type: entity
  id: MachineArtifactCrusher
  parent: [ ConstructibleMachine, BaseMachinePowered ]
  name: "дробарка для артефактів"
  description: "Краще не дозволяти пальцям застрявати..."
  components:
  - type: ArtifactCrusher
    crushingWhitelist:
      components:
      - Artifact
    crushingDamage:
      types:
        Blunt: 10
  - type: Machine
    board: ArtifactCrusherMachineCircuitboard
  - type: WiresPanel
  - type: Sprite
    sprite: Structures/Machines/artifact_crusher.rsi
    offset: 0,0.5
    noRot: true
    layers:
    - state: base
      map: ["enum.StorageVisualLayers.Base"]
    - state: door-closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: piston
      map: ["pistonlayer"]
    - state: glass
    - state: lights
      map: ["enum.PowerDeviceVisualLayers.Powered"]
      shader: unshaded
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.45
        density: 50
        mask:
        - HighImpassable
        layer:
        - HighImpassable
  - type: EntityStorage
    capacity: 1
    whitelist:
      components:
      - Artifact
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ArtifactCrusherVisuals.Crushing:
        pistonlayer:
          True: {state: piston-push}
          False: {state: piston}
      enum.PowerDeviceVisuals.Powered:
        enum.PowerDeviceVisualLayers.Powered:
          True: { visible: true }
          False: { visible: false }
  - type: EntityStorageVisuals
    stateDoorClosed: door-closed
    openDrawDepth: 0
    closedDrawDepth: 4
  - type: Construction
    containers:
    - machine_board
    - machine_parts
    - entity_storage
    - output_container
  - type: ContainerContainer
    containers:
      machine_board: !type:Container
      machine_parts: !type:Container
      entity_storage: !type:Container
      output_container: !type:Container
