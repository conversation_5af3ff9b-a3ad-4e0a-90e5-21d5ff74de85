- type: entity
  name: "імпровізована пневматична гармата"
  parent: BaseStorageItem
  id: WeaponImprovisedPneumaticCannon
  description: "Імпровізована з труби, кількох застібок-блискавок і пневматичної гармати. Не приймає танки без достатньої кількості бензину."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Cannons/pneumatic_cannon.rsi
    layers:
    - state: icon
    - state: tank
      map: [ "tank" ]
      visible: false
  - type: Item
    size: Huge
  - type: Clothing
    quickEquip: false
    slots:
    - Back
  - type: Gun
    fireRate: 2
    selectedMode: SemiAuto
    availableModes:
    - SemiAuto
    soundGunshot:
      path: /Audio/Effects/thunk.ogg
    soundEmpty:
      path: /Audio/Items/hiss.ogg
    clumsyProof: true
  - type: ContainerAmmoProvider
    container: storagebase
  - type: PneumaticCannon
  - type: Storage
    maxItemSize: Normal
    grid:
    - 0,0,1,1
  - type: Appearance
  - type: ItemMapper
    containerWhitelist: [gas_tank]
    mapLayers:
      tank:
        whitelist:
          components:
          - GasTank
  - type: Construction
    graph: PneumaticCannon
    node: cannon
  - type: ItemSlots
    slots:
      gas_tank:
        name: pneumatic-cannon-component-itemslot-name
        whitelist:
          components:
          - GasTank
        insertSound:
          path: /Audio/Weapons/click.ogg
          params:
            volume: -3
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
        ents: []
      gas_tank: !type:ContainerSlot
  - type: MeleeWeapon
    attackRate: 1.33
    damage:
      types:
        Blunt: 9
    swapKeys: true
    disableHeavy: true
    animation: WeaponArcThrust
    wideAnimationRotation: 180
    soundHit:
      collection: MetalThud
  - type: DamageOtherOnHit
    staminaCost: 8

- type: entity
  name: "пирогова гармата"
  parent: BaseStorageItem
  id: LauncherCreamPie
  description: "Завантажте кремовий пиріг для оптимального результату."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Cannons/pie_cannon.rsi
    layers:
    - state: piecannon
  - type: Storage
    maxItemSize: Normal
    grid:
    - 0,0,7,3
    whitelist:
      components:
      - CreamPie
  - type: Gun
    fireRate: 1
    selectedMode: SemiAuto
    availableModes:
    - SemiAuto
    - FullAuto
    soundGunshot:
      path: /Audio/Effects/thunk.ogg
    soundEmpty:
      path: /Audio/Items/hiss.ogg
    clumsyProof: true
  - type: ContainerAmmoProvider
    container: storagebase
  - type: Item
    size: Normal
  - type: Clothing
    sprite: Objects/Weapons/Guns/Cannons/pie_cannon.rsi
    quickEquip: false
    slots:
    - Back
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
        ents: []
  - type: Execution

# shoots bullets instead of throwing them, no other changes
- type: entity
  parent: WeaponImprovisedPneumaticCannon
  id: WeaponImprovisedPneumaticCannonGun
  suffix: Gun
  components:
  - type: PneumaticCannon
    throwItems: false

# doesn't need gas, extra capacity
- type: entity
  parent: WeaponImprovisedPneumaticCannonGun
  id: WeaponImprovisedPneumaticCannonAdmeme
  suffix: Admeme
  components:
  - type: Item
    size: Ginormous
  - type: Storage
    maxItemSize: Ginormous
    grid:
    - 0,0,19,10
    whitelist:
      tags: [] #dodging a test fail like the IRS
  - type: PneumaticCannon
    gasUsage: 0
    throwItems: false
  - type: Gun
    fireRate: 10
    selectedMode: FullAuto
    availableModes:
    - SemiAuto
    - FullAuto
    soundGunshot:
      path: /Audio/Effects/thunk.ogg
    soundEmpty:
      path: /Audio/Items/hiss.ogg
