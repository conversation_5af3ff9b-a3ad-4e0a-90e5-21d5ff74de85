- type: constructionGraph
  id: ShivConstruct
  start: start
  graph:
  - node: start
    entity: ShardGlass
    edges:
    - to: icon
      steps:
      - material: Cloth
        amount: 1
        doAfter: 1
  - node: icon
    entity: Shiv

- type: constructionGraph
  id: Shiv
  start: start
  graph:
  - node: start
    edges:
    - to: icon
      steps:
      - tag: GlassShard
        name: Glass Shard
        icon:
          sprite: Objects/Materials/Shards/shard.rsi
          state: shard1
        doAfter: 1
      - material: Cloth
        amount: 1
        doAfter: 1
  - node: icon
    entity: Shiv

- type: constructionGraph
  id: ReinforcedShivConstruct
  start: start
  graph:
  - node: start
    entity: ShardGlassReinforced
    edges:
    - to: icon
      steps:
      - material: Cloth
        amount: 1
        doAfter: 1
  - node: icon
    entity: ReinforcedShiv

- type: constructionGraph
  id: ReinforcedShiv
  start: start
  graph:
  - node: start
    edges:
    - to: icon
      steps:
      - tag: ReinforcedGlassShard
        name: Reinforced Glass Shard
        icon:
          sprite: Objects/Materials/Shards/shard.rsi
          state: shard1
        doAfter: 1
      - material: Cloth
        amount: 1
        doAfter: 1
  - node: icon
    entity: ReinforcedShiv

- type: constructionGraph
  id: PlasmaShivConstruct
  start: start
  graph:
  - node: start
    entity: ShardGlassPlasma
    edges:
    - to: icon
      steps:
      - material: Cloth
        amount: 1
        doAfter: 1
  - node: icon
    entity: PlasmaShiv

- type: constructionGraph
  id: PlasmaShiv
  start: start
  graph:
  - node: start
    edges:
    - to: icon
      steps:
      - tag: PlasmaGlassShard
        name: Plasma Glass Shard
        icon:
          sprite: Objects/Materials/Shards/shard.rsi
          state: shard1
        doAfter: 1
      - material: Cloth
        amount: 1
        doAfter: 1
  - node: icon
    entity: PlasmaShiv

- type: constructionGraph
  id: UraniumShivConstruct
  start: start
  graph:
  - node: start
    entity: ShardGlassUranium
    edges:
    - to: icon
      steps:
      - material: Cloth
        amount: 1
        doAfter: 1
  - node: icon
    entity: UraniumShiv

- type: constructionGraph
  id: UraniumShiv
  start: start
  graph:
  - node: start
    edges:
    - to: icon
      steps:
      - tag: UraniumGlassShard
        name: Uranium Glass Shard
        icon:
          sprite: Objects/Materials/Shards/shard.rsi
          state: shard1
        doAfter: 1
      - material: Cloth
        amount: 1
        doAfter: 1
  - node: icon
    entity: UraniumShiv
