# Base
- type: entity
  id: WardrobeBase
  parent: ClosetSteelBase
  abstract: true
  description: "Це стандартизоване НаноТрейзен сховище для зберігання одягу."

# Colored

# Blue wardrobe
- type: entity
  id: WardrobeBlue
  parent: WardrobeBase
  name: "синій гардероб"
  description: "Гардероб, наповнений стильним синім одягом."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: blue_door

# Pink wardrobe
- type: entity
  id: WardrobePink
  parent: WardrobeBase
  name: "рожевий гардероб"
  description: "Шафа, наповнена казковим рожевим одягом."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: pink_door

# Black wardrobe
- type: entity
  id: WardrobeBlack
  parent: WardrobeBase
  name: "чорний гардероб"
  description: "Шафа, наповнена стильним чорним одягом."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: black_door

# Green wardrobe
- type: entity
  id: WardrobeGreen
  parent: WardrobeBase
  name: "зелений гардероб"
  description: "Гардероб, наповнений стильним зеленим одягом."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: green_door

# Prison wardrobe
- type: entity
  id: WardrobePrison
  parent: WardrobeBase
  name: "гардероб ув'язнених"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: orange_door

# Yellow wardrobe
- type: entity
  id: WardrobeYellow
  parent: WardrobeBase
  name: "жовтий гардероб"
  description: "Гардероб, наповнений стильним жовтим одягом."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: yellow_door

# White wardrobe
- type: entity
  id: WardrobeWhite
  parent: WardrobeBase
  name: "біла шафа"
  description: "Шафа, наповнена стильним білим одягом."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: white_door

# Grey wardrobe
- type: entity
  id: WardrobeGrey
  parent: WardrobeBase
  name: "сірий гардероб"
  description: "Шафа, переповнена сірим одягом."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: grey_door

# Mixed wardrobe
- type: entity
  id: WardrobeMixed
  parent: WardrobeBase
  name: "змішаний гардероб"
  description: "Гардероб, наповнений різнобарвним одягом."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: mixed_door

# Jobs

- type: entity
  id: WardrobeSecurity
  parent: WardrobeBase
  name: "гардероб служби безпеки"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: red_door

- type: entity
  id: WardrobeAtmospherics
  parent: WardrobeBase
  name: "атмосферний гардероб"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: atmos_wardrobe_door

- type: entity
  id: ClosetJanitor
  parent: WardrobeMixed
  name: "шафа для зберігання цінностей"
  description: "Це приміщення для зберігання одягу та інвентарю двірників."

- type: entity
  id: WardrobeFormal
  parent: WardrobeBlack
  name: "урочистий гардероб"
  description: "Це приміщення для зберігання урочистого одягу."

- type: entity
  id: ClosetChef
  parent: WardrobeBlack
  name: "шафа шеф-кухаря"
  description: "Це склад для зберігання одягу для харчової промисловості та мишоловок."

- type: entity
  id: WardrobeChapel
  parent: WardrobeBlack
  name: "гардероб капелана"
  description: "Це сховище для релігійного вбрання, схваленого НаноТреайзен."

- type: entity
  id: ClosetLegal
  parent: WardrobeBlue
  name: "юридичний гардероб"
  description: "Це сховище для зберігання одягу та предметів, необхідних для роботи в суді."

- type: entity
  id: WardrobeCargo
  parent: WardrobePrison
  name: "вантажницький гардероб" # DeltaV - Logistics Department replacing Cargo

- type: entity
  id: WardrobeSalvage
  parent: WardrobeMixed
  name: "шахтарський гардероб"
  description: "Це сховище для зберігання одягу та предметів, необхідних для роботи шахтерам."

- type: entity
  id: WardrobeEngineering
  parent: WardrobeYellow
  name: "інженерний гардероб"

- type: entity
  id: WardrobeMedicalDoctor
  parent: WardrobeWhite
  name: "гардероб лікаря"

- type: entity
  id: WardrobeRobotics
  parent: WardrobeBlack
  name: "робототехнічний гардероб"

- type: entity
  id: WardrobeChemistry
  parent: WardrobeWhite
  name: "хімічна шафа"

- type: entity
  id: WardrobeGenetics
  parent: WardrobeWhite
  name: "генетичний гардероб"

- type: entity
  id: WardrobeVirology
  parent: WardrobeWhite
  name: "вірусологічний гардероб"

- type: entity
  id: WardrobeScience
  parent: WardrobeWhite
  name: "гардероб вчених" # DeltaV - Epistemics Department replacing Science

- type: entity
  id: WardrobeBotanist
  parent: WardrobeGreen
  name: "гардероб гідропоніки"
