# Goob edit - energy bola

- type: entity
  name: "імпровізована бола"
  abstract: true
  id: BaseBola
  parent: BaseItem
  description: "Високошвидкісний інструмент для заплутування."
  components:
  - type: Item
    size: Normal
  - type: Sprite
    sprite: Objects/Weapons/Throwable/bola.rsi
    state: icon
  - type: EmitSoundOnThrow
    sound: /Audio/Weapons/bolathrow.ogg
  - type: EmitSoundOnLand
    sound: /Audio/Effects/snap.ogg
  - type: DamageOnLand
    damage:
      types:
        Blunt: 5
  - type: Ensnaring
    freeTime: 2.0
    breakoutTime: 3.5
    walkSpeed: 0.7
    sprintSpeed: 0.7
    staminaDamage: 55
    canThrowTrigger: true
    canMoveBreakout: true

- type: entity
  name: "імпровізована бола"
  parent: [BaseBola]
  id: Bola
  description: "Проста зброя для того, щоб підставити комусь підніжку на відстані."
  components:
  - type: Construction
    graph: Bola
    node: bola
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 75
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 15
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:DoActsBehavior
        acts: [ "Destruction" ]

- type: entity
  name: "енергетична бола"
  id: BolaEnergy
  parent: BaseBola
  description: "Просунутий інструмент для заплутування злочинців з жорстким світлом. Інакше відомий як дорогий шматок мотузки."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Throwable/energy_bola.rsi
  - type: DamageOnLand
    damage:
      types:
      Heat: 5
  - type: Ensnaring
    destroyOnRemove: true
    freeTime: 2.0
    breakoutTime: 3.5 #all bola should generally be fast to remove
    walkSpeed: 0.7 #makeshift bola shouldn't slow too much
    sprintSpeed: 0.7
    staminaDamage: 55 # Sudden weight increase sapping stamina
    canThrowTrigger: true
    canMoveBreakout: true
  - type: LandAtCursor
