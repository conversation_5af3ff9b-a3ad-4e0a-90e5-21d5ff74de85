- type: entity
  abstract: true
  parent: BaseStructure
  id: BaseMachine
  components:
  - type: InteractionOutline
  - type: Anchorable
    delay: 2
  - type: Physics
    bodyType: Static
  - type: Transform
    noRot: true
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.45"
        density: 190
        mask:
        - MachineMask
        layer:
        - MachineLayer
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
  - type: InteractionVerbs
    allowedVerbs:
    - KnockOn

- type: entity
  abstract: true
  parent: BaseMachine
  id: BaseMachinePowered
  components:
  - type: ApcPowerReceiver
    powerLoad: 1000
  - type: ExtensionCableReceiver
  - type: LightningTarget
    priority: 1
  - type: LanguageSpeaker # Yes, on BASE MACHINE. This is all I need to make sure that in 99% of cases, machines use the language system.
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    understands:
    - TauCetiBasic

- type: entity
  abstract: true
  id: ConstructibleMachine
  components:
  - type: Machine
  - type: ContainerContainer
    containers:
      machine_board: !type:Container
      machine_parts: !type:Container
  - type: Construction
    graph: Machine
    node: machine
    containers:
    - machine_parts
    - machine_board
  - type: LightningTarget
    priority: 1

- type: entity
  abstract: true
  parent: ConstructibleMachine
  id: SmallConstructibleMachine
  components:
    - type: RequireProjectileTarget
