# TODO: Add descriptions (many)
# TODO BODY: Part damage
- type: entity
  id: PartPlasmaman
  parent: [BaseItem, BasePart]
  name: "частина тіла плазмомена"
  abstract: true
  components:
  - type: Sprite
    sprite: Mobs/Species/Plasmaman/parts.rsi
  - type: Butcherable
    butcheringType: Knife
    butcherDelay: 4.0
    spawned:
    - id: SheetPlasma1
      amount: 1
  - type: IgniteFromGasPart
    gas: Oxygen

- type: entity
  id: TorsoPlasmaman
  name: "торс плазмомена"
  parent: [PartPlasmaman, BaseTorso]
  components:
  - type: Sprite
    state: torso_m
  - type: Butcherable
    butcherDelay: 6.0
    spawned:
    - id: SheetPlasma1
      amount: 3
  - type: IgniteFromGasPart
    fireStacks: 0.06

- type: entity
  id: HeadPlasmaman
  name: "голова плазмомена"
  parent: [PartPlasmaman, BaseHead]
  components:
  - type: Sprite
    state: head_m
  - type: Butcherable
    butcherDelay: 5.0
  - type: IgniteFromGasPart
    fireStacks: 0.02

- type: entity
  id: LeftArmPlasmaman
  name: "ліва рука плазмомена"
  parent: [PartPlasmaman, BaseLeftArm]
  components:
  - type: Sprite
    state: l_arm
  - type: IgniteFromGasPart
    fireStacks: 0.02

- type: entity
  id: RightArmPlasmaman
  name: "права рука плазмомена"
  parent: [PartPlasmaman, BaseRightArm]
  components:
  - type: Sprite
    state: r_arm
  - type: IgniteFromGasPart
    fireStacks: 0.02

- type: entity
  id: LeftHandPlasmaman
  name: "ліва рука плазмомена"
  parent: [PartPlasmaman, BaseLeftHand]
  components:
  - type: Sprite
    state: l_hand
  - type: Butcherable
    butcherDelay: 3.0
  - type: IgniteFromGasPart
    fireStacks: 0.01

- type: entity
  id: RightHandPlasmaman
  name: "права рука плазмомена"
  parent: [PartPlasmaman, BaseRightHand]
  components:
  - type: Sprite
    state: r_hand
  - type: Butcherable
    butcherDelay: 3.0
  - type: IgniteFromGasPart
    fireStacks: 0.01

- type: entity
  id: LeftLegPlasmaman
  name: "ліва нога плазмомена"
  parent: [PartPlasmaman, BaseLeftLeg]
  components:
  - type: Sprite
    state: l_leg
  - type: IgniteFromGasPart
    fireStacks: 0.02

- type: entity
  id: RightLegPlasmaman
  name: "права нога плазмомена"
  parent: [PartPlasmaman, BaseRightLeg]
  components:
  - type: Sprite
    state: r_leg
  - type: IgniteFromGasPart
    fireStacks: 0.02

- type: entity
  id: LeftFootPlasmaman
  name: "ліва нога плазмомена"
  parent: [PartPlasmaman, BaseLeftFoot]
  components:
  - type: Sprite
    state: l_foot
  - type: Butcherable
    butcherDelay: 3.0
  - type: IgniteFromGasPart
    fireStacks: 0.01

- type: entity
  id: RightFootPlasmaman
  name: "права нога плазмомена"
  parent: [PartPlasmaman, BaseRightFoot]
  components:
  - type: Sprite
    state: r_foot
  - type: Butcherable
    butcherDelay: 3.0
  - type: IgniteFromGasPart
    fireStacks: 0.01
