- type: job
  id: Paramedic
  name: job-name-paramedic
  description: job-description-paramedic
  playTimeTracker: JobParamedic
  requirements:
    # - !type:RoleTimeRequirement # DeltaV - No Medical Doctor time requirement
    #   role: JobMedicalDoctor
    #   time: 14400 #4 hrs
    - !type:CharacterDepartmentTimeRequirement # DeltaV - Medical dept time requirement
      department: Medical
      min: 28800 # DeltaV - 8 hours
    - !type:CharacterEmployerRequirement
      employers:
      - Interdyne
      - NanoTrasen
      - ZengHuPharmaceuticals
    # - !type:OverallPlaytimeRequirement # DeltaV - No playtime requirement
    #   time: 54000 # 15 hrs
  startingGear: ParamedicGear
  icon: "JobIconParamedic"
  supervisors: job-supervisors-cmo
  access:
  - Medical
  - Maintenance
  - External
  - Paramedic # DeltaV - Add Paramedic access
  extendedAccess:
  - Chemistry
  special:
  - !type:AddComponentSpecial
    components:
    - type: CPRTraining
    - type: SurgerySpeedModifier
      speedModifier: 1.75

- type: startingGear
  id: ParamedicGear
  subGear:
  - ParamedicPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitParamedic
    back: ClothingBackpackParamedicFilledDV
    shoes: ClothingShoesColorBlue
    id: ParamedicPDA
    ears: ClothingHeadsetMedical
    belt: ClothingBeltMedicalEMTFilled
    pocket1: HandheldGPSBasic
    pocket2: HandheldCrewMonitor
  innerClothingSkirt: ClothingUniformJumpskirtParamedic
  satchel: ClothingBackpackSatchelParamedicFilledDV
  duffelbag: ClothingBackpackDuffelParamedicFilledDV

- type: startingGear
  id: ParamedicPlasmamanGear
  parent: BasePlasmamanGear
  equipment:
    jumpsuit: ClothingUniformEnvirosuitParamedic
    head: ClothingHeadEnvirohelmParamedic
    gloves: ClothingHandsGlovesEnviroglovesNitrile
    shoes: ClothingShoesColorBlue
