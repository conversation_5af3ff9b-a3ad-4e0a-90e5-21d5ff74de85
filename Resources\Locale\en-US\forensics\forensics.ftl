forensic-scanner-interface-title = Forensic scanner
forensic-scanner-interface-fingerprints = Fingerprints
forensic-scanner-interface-fibers = Fibers
forensic-scanner-interface-dnas = DNAs
forensic-scanner-interface-residues = Residues
forensic-scanner-interface-no-data = No scan data available
forensic-scanner-interface-print = Print
forensic-scanner-interface-clear = Clear
forensic-scanner-report-title = Forensics Report: {$entity}
forensic-pad-unused = It hasn't been used.
forensic-pad-sample = It has a sample: {$sample}
forensic-pad-gloves = {CAPITALIZE($target)} is wearing gloves.
forensic-pad-start-scan-target = {CAPITALIZE($user)} is trying to take a sample of your fingerprints.
forensic-pad-start-scan-user = You start taking a sample of {CAPITALIZE($target)}'s fingerprints.
forensic-pad-already-used = This pad has already been used.
forensic-scanner-match-fiber = Match in fiber found!
forensic-scanner-match-fingerprint = Match in fingerprint found!
forensic-scanner-match-none = No matches found!
forensic-scanner-printer-not-ready = Printer is not ready yet.
forensic-scanner-verb-text = Scan
forensic-scanner-verb-message = Perform a forensic scan



forensic-pad-fingerprint-name = {$entity}'s fingerprints
forensic-pad-gloves-name = fibers from {$entity}

forensics-cleaning = You begin cleaning the evidence off of {THE($target)}...