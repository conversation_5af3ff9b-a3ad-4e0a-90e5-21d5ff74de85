- type: entity
  id: SmallboxItem
  parent: BaseStorageItem
  abstract: true
  components:
  - type: Storage
    grid:
    - 0,0,6,1
  - type: Item
    size: Small

- type: entity
  name: "сірник"
  parent: BaseItem
  id: Matchstick
  description: "Простий сірниковий стрижень, що використовується для підпалювання тонких курильних сумішей."
  components:
  - type: Tag
    tags:
    - Matchstick
    - Trash
  - type: SpaceGarbage
  - type: Sprite
    sprite: Objects/Tools/matches.rsi
    layers:
      - state: match_unlit
  - type: Item
    sprite: Objects/Tools/matches.rsi
    heldPrefix: unlit
    size: Tiny
  - type: Matchstick
    duration: 10
    igniteSound:
      path: /Audio/Items/match_strike.ogg
  - type: PointLight
    enabled: false
    radius: 1.1
    color: darkorange
  - type: Appearance
  - type: BurnStateVisuals
    unlitIcon: match_unlit
    litIcon: match_lit
    burntIcon: match_burnt
  # Shitmed Change
  - type: Cautery
    speed: 0.2
  - type: SurgeryTool
    startSound:
      path: /Audio/Weapons/Guns/Hits/energy_meat1.ogg
    endSound:
      path: /Audio/Weapons/Guns/Hits/energy_meat1.ogg

- type: entity
  parent: Matchstick
  id: MatchstickSpent
  suffix: spent
  components:
  - type: Sprite
    layers:
      - state: match_burnt
  - type: Matchstick
    state: Burnt

- type: entity
  name: "сірникова коробка"
  parent: [ SmallboxItem, BaseBagOpenClose ]
  id: Matchbox
  description: "Невеличка коробочка сірників Almost But Not Quite Plasma Premium."
  components:
  - type: EmitSoundOnPickup
    sound: /Audio/SimpleStation14/Items/Handling/matchbox_pickup.ogg
  - type: EmitSoundOnDrop
    sound: /Audio/SimpleStation14/Items/Handling/matchbox_drop.ogg
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Items/matchbox_drop.ogg
  - type: Matchbox
  - type: Sprite
    sprite: Objects/Tools/matches.rsi
    layers:
    - state: matchbox
    - state: matchbox-open
      map: ["openLayer"]
    - state: matchbox1
      map: ["matchbox1"]
      visible: false
    - state: matchbox2
      map: ["matchbox2"]
      visible: false
    - state: matchbox3
      map: ["matchbox3"]
      visible: false
  - type: Item
    sprite: Objects/Tools/matches.rsi
    heldPrefix: matchbox
    size: Small
  - type: Storage
    grid:
    - 0,0,2,1
  - type: StorageFill
    contents:
      - id: Matchstick
        amount: 6
  - type: ItemCounter
    count:
      tags: [Matchstick]
    composite: true
    layerStates:
    - matchbox1
    - matchbox2
    - matchbox3
  - type: Appearance
  - type: Tag
    tags:
    - Trash
  - type: SpaceGarbage
