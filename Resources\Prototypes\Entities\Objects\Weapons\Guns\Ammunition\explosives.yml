# Rockets

- type: entity
  id: CartridgeRocket
  name: "Граната ПГ-7ВЛ"
  parent: BaseItem
  description: "Боєголовка калібру 1,5, призначена для пускової установки РПГ-7. Має трубчасту форму."
  components:
  - type: Tag
    tags:
      - CartridgeRocket
  - type: Item
    size: Small
  - type: CartridgeAmmo
    proto: BulletRocket
    deleteOnSpawn: true
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Explosives/explosives.rsi
    state: rpg
  - type: StaticPrice
    price: 20

- type: entity
  id: CartridgeRocketSlow
  name: "Граната ПГ-7ВЛ \"Ракета-равлик\""
  parent: BaseItem
  description: "Боєголовка калібру 1,5, розроблена для пускової установки РПГ-7. Надзвичайно повільна."
  components:
    - type: Tag
      tags:
        - CartridgeRocket
    - type: Item
      size: Small
    - type: CartridgeAmmo
      proto: BulletWeakRocket
      deleteOnSpawn: true
    - type: Sprite
      sprite: Objects/Weapons/Guns/Ammunition/Explosives/explosives.rsi
      state: frag
    - type: StaticPrice
      price: 20

# Grenades

- type: entity
  id: BaseGrenade
  name: "базова граната"
  parent: BaseItem
  abstract: true
  components:
  - type: Tag
    tags:
    - Grenade
  - type: Item
    size: Small
  - type: Sprite

- type: entity
  id: GrenadeBaton
  name: "світлошумова граната"
  parent: BaseGrenade
  components:
  - type: CartridgeAmmo
    proto: BulletGrenadeBaton
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Explosives/explosives.rsi
    layers:
    - state: baton
      map: ["enum.AmmoVisualLayers.Base"]
  - type: Appearance
  - type: SpentAmmoVisuals
    state: baton
    suffix: false

- type: entity
  id: GrenadeBlast
  name: "40мм вибухова граната"
  parent: BaseGrenade
  components:
  - type: CartridgeAmmo
    proto: BulletGrenadeBlast
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Explosives/explosives.rsi
    layers:
    - state: blast
      map: ["enum.AmmoVisualLayers.Base"]
  - type: Appearance
  - type: SpentAmmoVisuals
    state: blast
    suffix: false

- type: entity
  id: GrenadeFlash
  name: "40мм світлошумова граната"
  parent: BaseGrenade
  components:
  - type: CartridgeAmmo
    proto: BulletGrenadeFlash
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Explosives/explosives.rsi
    layers:
    - state: flash
      map: ["enum.AmmoVisualLayers.Base"]
  - type: Appearance
  - type: SpentAmmoVisuals
    state: flash
    suffix: false

- type: entity
  id: GrenadeFrag
  name: "40мм осколкова граната"
  parent: BaseGrenade
  components:
  - type: CartridgeAmmo
    proto: BulletGrenadeFrag
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Explosives/explosives.rsi
    layers:
    - state: frag
      map: ["enum.AmmoVisualLayers.Base"]
  - type: Appearance
  - type: SpentAmmoVisuals
    state: frag
    suffix: false

- type: entity
  id: GrenadeEMP
  name: "40мм ЕМІ граната"
  parent: BaseGrenade
  components:
  - type: CartridgeAmmo
    proto: BulletGrenadeEMP
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Explosives/explosives.rsi
    layers:
    - state: emp
      map: ["enum.AmmoVisualLayers.Base"]
  - type: Appearance
  - type: SpentAmmoVisuals
    state: frag
    suffix: false

- type: entity
  id: GrenadeBeanbagShotgun
  name: "40мм мішечок з дробом"
  parent: BaseGrenade
  components:
  - type: CartridgeAmmo
    proto: BulletGrenadeSpreadBeanbag
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Explosives/explosives.rsi
    layers:
    - state: frag
      map: ["enum.AmmoVisualLayers.Base"]
  - type: Appearance
  - type: SpentAmmoVisuals
    state: frag
    suffix: false

- type: entity
  id: GrenadeBirdshot
  name: "40мм дріб"
  description: "Для полювання на інопланетних птахів незвичайного розміру."
  parent: BaseGrenade
  components:
  - type: CartridgeAmmo
    proto: BulletGrenadeSpreadBirdshot
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Explosives/explosives.rsi
    layers:
    - state: frag
      map: ["enum.AmmoVisualLayers.Base"]
  - type: Appearance
  - type: SpentAmmoVisuals
    state: frag
    suffix: false

- type: entity
  id: GrenadeSlug
  name: "40мм слаг"
  description: "Для полювання на інопланетну дичину розміром з гору."
  parent: BaseGrenade
  components:
  - type: CartridgeAmmo
    proto: BulletGrenadeSlug
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Explosives/explosives.rsi
    layers:
    - state: frag
      map: ["enum.AmmoVisualLayers.Base"]
  - type: Appearance
  - type: SpentAmmoVisuals
    state: frag
    suffix: false

- type: entity
  id: Grenade00Buckshot
  name: "40мм 00-Картеч"
  description: "Для випадків, коли ви полюєте на інопланетних тварин розміром з будинок."
  parent: BaseGrenade
  components:
  - type: CartridgeAmmo
    proto: BulletGrenadeSpread00Buckshot
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Explosives/explosives.rsi
    layers:
    - state: frag
      map: ["enum.AmmoVisualLayers.Base"]
  - type: Appearance
  - type: SpentAmmoVisuals
    state: frag
    suffix: false

- type: entity
  id: Grenade0000Buckshot
  name: "40мм 0000-Картеч"
  parent: BaseGrenade
  description: "Для випадків, коли ви полюєте на інопланетних тварин розміром з десантний корабель."
  components:
  - type: CartridgeAmmo
    proto: BulletGrenadeSpread0000Buckshot
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Explosives/explosives.rsi
    layers:
    - state: frag
      map: ["enum.AmmoVisualLayers.Base"]
  - type: Appearance
  - type: SpentAmmoVisuals
    state: frag
    suffix: false

# Cannon Balls

- type: entity
  id: BaseCannonBall
  name: "основа гарматного ядра"
  parent: BaseItem
  abstract: true
  components:
  - type: Tag
    tags:
    - CannonBall
  - type: Item
    size: Small
  - type: Sprite

- type: entity
  id: CannonBall
  name: "гарматне ядро"
  suffix: Pirate
  parent: BaseCannonBall
  components:
  - type: CartridgeAmmo
    proto: BulletCannonBall
    deleteOnSpawn: true
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Explosives/explosives.rsi
    state: ball

- type: entity
  id: CannonBallGrapeshot
  name: "фотографія"
  suffix: Pirate
  parent: BaseCannonBall
  components:
  - type: CartridgeAmmo
    proto: PelletGrapeshotSpread
    deleteOnSpawn: true
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Explosives/explosives.rsi
    state: grapeshot

- type: entity
  id: CannonBallGlassshot
  name: "скляний постріл"
  suffix: Pirate
  parent: BaseCannonBall
  components:
  - type: CartridgeAmmo
    proto: PelletGlassSpread
    deleteOnSpawn: true
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Explosives/explosives.rsi
    state: glassshot
