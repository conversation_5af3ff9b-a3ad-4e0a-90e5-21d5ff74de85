- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBunny
  name: "кролячі вушка"
  description: "Милі кролячі вушка."
  components:
  - type: Sprite
    sprite: Clothing/Head/Misc/bunny.rsi
  - type: Clothing #PIRATE
    slots: #PIRATE
    - HEAD #PIRATE
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
    sprite: Clothing/Head/Misc/bunny.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatCake
  name: "капелюшок для торта"
  description: "Ти одягаєш торт собі на голову. Геніально."
  components:
  - type: Sprite
    sprite: Clothing/Head/Misc/cake.rsi
  - type: Clothing
    sprite: Clothing/Head/Misc/cake.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatChickenhead
  name: "куряча голова"
  description: "Це куряча голова. Бок, бок, бок!"
  components:
  - type: Sprite
    sprite: Clothing/Head/Misc/chickenhead.rsi
  - type: Clothing
    sprite: Clothing/Head/Misc/chickenhead.rsi
  - type: IngestionBlocker
  - type: IdentityBlocker

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatFlowerWreath
  name: "квітковий вінок"
  description: "Вінок з різнокольорових квітів. Можна носити як на голові, так і на шиї."
  components:
  - type: Sprite
    sprite: Clothing/Head/Misc/flower-wreath.rsi
  - type: Clothing
    sprite: Clothing/Head/Misc/flower-wreath.rsi
    slots:
    - HEAD
    - neck
  - type: Construction
    graph: flowerwreath
    node: flowerwreath

- type: entity
  parent: ClothingHeadLightBase
  id: ClothingHeadHatPumpkin
  name: "гарбузовий капелюх"
  description: "Ліхтар Джека! Вважається, що він відганяє злих духів."
  components:
  - type: Sprite
    sprite: Clothing/Head/Misc/pumpkin.rsi
  - type: Clothing
    sprite: Clothing/Head/Misc/pumpkin.rsi
  - type: IngestionBlocker
  - type: IdentityBlocker
  - type: PointLight
    enabled: false
    radius: 3
    energy: 1
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
    color: "#cc6600"
    netsync: false
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatPwig
  name: "перука"
  description: "Чесно кажучи, вони виглядають безглуздо."
  components:
  - type: Sprite
    sprite: Clothing/Head/Misc/pwig.rsi
  - type: Clothing
    sprite: Clothing/Head/Misc/pwig.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadMirror
  name: "дзеркало заднього виду"
  description: "Сумніваюся, що навіть операційний директор знає, як ним користуватися."
  components:
  - type: Sprite
    sprite: Clothing/Head/Misc/head_mirror.rsi
  - type: Clothing
    sprite: Clothing/Head/Misc/head_mirror.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatRichard
  name: "річард"
  description: "Тобі подобається робити людям боляче?"
  components:
  - type: Sprite
    sprite: Clothing/Head/Misc/richard.rsi
  - type: Clothing
    sprite: Clothing/Head/Misc/richard.rsi
  - type: IngestionBlocker
  - type: IdentityBlocker

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatSkub
  name: "скаб-шапка"
  description: "Найкраще поєднується зі скаб-костюмом."
  components:
  - type: Sprite
    sprite: Clothing/Head/Misc/skubhead.rsi
  - type: Clothing
    sprite: Clothing/Head/Misc/skubhead.rsi
  - type: IngestionBlocker
  - type: IdentityBlocker

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatShrineMaidenWig
  name: "перука святої дівиці"
  description: "На бирці написано: \"Всі виручені кошти йдуть до храму Хакурей\""
  components:
  - type: Sprite
    sprite: Clothing/Head/Misc/shrine-maidens-wig.rsi
  - type: Clothing
    sprite: Clothing/Head/Misc/shrine-maidens-wig.rsi
  - type: Tag
    tags:
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatCone
  name: "дорожній конус"
  description: "Цей конус намагається вас про щось попередити!"
  components:
  - type: Sprite
    sprite: Clothing/Head/Misc/cone.rsi
  - type: Clothing
    sprite: Clothing/Head/Misc/cone.rsi
  - type: PhysicalComposition #you can't just pass up some free plastic!
    materialComposition:
      Plastic: 100

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatFancyCrown
  name: "вигадлива корона"
  description: "Смердить дохлим щуром. Дозволяє тобі говорити, як щур!"
  components:
  - type: Sprite
    sprite: Clothing/Head/Misc/fancycrown.rsi
  - type: Clothing
    sprite: Clothing/Head/Misc/fancycrown.rsi
  - type: MobPrice
    price: 3000
  - type: AddAccentClothing
    accent: MobsterAccent

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatCatEars
  name: "котячі вуха"
  description: "НЯВ!"
  suffix: DO NOT MAP
  components:
  - type: Tag
    tags: [] # ignore "WhitelistChameleon" tag
  - type: Sprite
    sprite: Clothing/Head/Hats/catears.rsi
  - type: Clothing #PIRATE
    slots: #PIRATE
    - HEAD #PIRATE
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
    sprite: Clothing/Head/Hats/catears.rsi
  - type: AddAccentClothing
    accent: OwOAccent

- type: entity
  parent: [ClothingHeadHatCatEars, BaseToggleClothing]
  id: ClothingHeadHatCatEarsValid
  suffix: Valid, DO NOT MAP
  components:
  - type: ToggleClothing
    action: ActionBecomeValid
    disableOnUnequip: true
  - type: ComponentToggler
    parent: true
    components:
    - type: KillSign
  - type: Tag
    tags: [] # ignore "WhitelistChameleon" tag
  - type: Sprite
    sprite: Clothing/Head/Hats/catears.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/catears.rsi
  - type: AddAccentClothing
    accent: OwOAccent

- type: entity
  categories: [ HideSpawnMenu ]
  id: ActionBecomeValid
  name: "Стати дійсним"
  description: "*Що це?"
  components:
  - type: InstantAction
    event: !type:ToggleActionEvent

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatDogEars
  name: "собачі вушка"
  description: "Тільки для хороших хлопчиків."
  suffix: DO NOT MAP
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/dogears.rsi
  - type: Clothing #PIRATE
    slots: #PIRATE
    - HEAD #PIRATE
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
    sprite: Clothing/Head/Hats/dogears.rsi
  - type: AddAccentClothing
    accent: BarkAccent

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatSquid
  name: "кальмарчик"
  description: "Налякайте своїх друзів цією жахлпивою маскою."
  components:
  - type: Sprite
    sprite: Clothing/Head/Misc/squiddy.rsi
  - type: Clothing
    sprite: Clothing/Head/Misc/squiddy.rsi
  - type: IngestionBlocker
  - type: IdentityBlocker

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatRedRacoon
  name: "шапка червоного єнота"
  description: "Пухнаста шапка рудого єнота!"
  components:
  - type: Sprite
    sprite: Clothing/Head/Misc/red_racoon.rsi
  - type: Clothing
    sprite: Clothing/Head/Misc/red_racoon.rsi

- type: entity
  parent: Clothing
  id: WaterDropletHat
  name: "крапля води"
  description: "Робить 8-оких друзів у 8 разів чарівнішими!"
  components:
  - type: Sprite
    sprite: Clothing/Head/Misc/waterdroplet.rsi
    state: icon
  - type: Item
    sprite: Clothing/Head/Misc/waterdroplet.rsi
    size: Tiny
    storedRotation: -90
  - type: Clothing
    slots:
    - HEAD
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
  - type: Food
    solution: drink
    useSound: /Audio/Items/drink.ogg
    eatMessage: drink-component-try-use-drink-success-slurp
    delay: 0.5
    forceFeedDelay: 1.5
  - type: FlavorProfile
    flavors:
      - water
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 2
        reagents:
        - ReagentId: Water
          Quantity: 2
  - type: DamageOnHighSpeedImpact
    minimumSpeed: 0.1
    damage:
      types:
        Blunt: 1
  - type: Damageable
    damageContainer: Biological
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 1
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: desecration
      - !type:SpillBehavior
        solution: drink
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Tag
    tags:
      - WhitelistChameleon
  - type: StaticPrice
    price: 1

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatHeadBandBasic
  name: "базова пов'язка на голову"
  description: "Ідеально підходить для майстрів бойових мистецтв, спітнілих операторів і тунельних гангстерів."
  components:
  - type: Sprite
    sprite: Clothing/Head/Misc/headband.rsi
  - type: Clothing
    sprite: Clothing/Head/Misc/headband.rsi
