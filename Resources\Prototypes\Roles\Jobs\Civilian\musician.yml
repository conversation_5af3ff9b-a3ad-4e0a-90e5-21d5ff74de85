- type: job
  id: Musician
  name: job-name-musician
  description: job-description-musician
  playTimeTracker: JobMusician
  startingGear: MusicianGear
  icon: "JobIconMusician"
  supervisors: job-supervisors-hire
  access:
  - Maintenance # TODO Remove maint access for all gimmick jobs once access work is completed
  - Theatre
  - Musician # DeltaV - Add Musician access
  special:
  - !type:GiveItemOnHolidaySpecial
    holiday: MikuDay
    prototype: BoxPerformer
  requirements:
    - !type:CharacterOverallTimeRequirement
      min: 7200 # DeltaV - 2 hours
    - !type:CharacterEmployerRequirement
      inverted: true
      employers:
      - Unemployed

- type: startingGear
  id: MusicianGear
  subGear:
  - MusicianPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitMusician
    back: ClothingBackpackMusicianFilled
    eyes: ClothingEyesGlassesSunglasses
    shoes: ClothingShoesBootsLaceup
    id: MusicianPDA
    ears: ClothingHeadsetService
  satchel: ClothingBackpackSatchelMusicianFilled
  duffelbag: ClothingBackpackDuffelMusicianFilled

- type: startingGear
  id: MusicianPlasmamanGear
  parent: BasePlasmamanGear
  equipment:
    jumpsuit: ClothingUniformEnvirosuitMusician
    head: ClothingHeadEnvirohelmMusician
    gloves: ClothingHandsGlovesEnviroglovesWhite
