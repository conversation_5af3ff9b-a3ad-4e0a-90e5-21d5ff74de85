- type: entity
  parent: BaseItem
  id: BaseBoardEntity # Board item
  name: "дошка"
  abstract: true
  description: "Чиста дошка."
  components:
    - type: TabletopGame
      size: 256, 256
      setup:
        !type:TabletopEmptySetup
        boardPrototype: Crowbar

- type: entity
  id: BaseTabletopPiece # Board piece
  parent: BaseItem
  abstract: true
  components:
  - type: TabletopDraggable
  - type: Sprite
    noRot: true
  - type: Appearance
  - type: Tag
    tags:
      - TabletopPiece

- type: entity
  id: BaseBoardTabletop
  name: "плінтус"
  abstract: true
  categories: [ HideSpawnMenu ]
  components:
  - type: Tag
    tags:
      - TabletopBoard
  - type: Sprite
    noRot: false
    drawdepth: FloorTiles
