﻿- type: constructionGraph
  id: PlushieGhostRevenant
  start: start
  graph:
    - node: start
      edges:
        - to: plushie
          steps:
            - tag: PlushieGhost
              name: ghost soft toy
              icon:
                sprite: Mobs/Ghosts/ghost_human.rsi
                state: icon
            - tag: Ectoplasm
              name: ectoplasm
              icon:
                sprite: DeltaV/Mobs/Ghosts/revenant.rsi # DeltaV - Custom revenant sprite
                state: ectoplasm
              doAfter: 10
    - node: plushie
      entity: PlushieGhostRevenant

- type: constructionGraph
  id: ClothingOuterSuitIan
  start: start
  graph:
  - node: start
    edges:
    - to: suit
      steps:
      - tag: HideCorgi
        name: corgi hide
        icon:
          sprite: Objects/Materials/materials.rsi
          state: corgihide
        doAfter: 5
  - node: suit
    entity: ClothingOuterSuitIan
