- type: entity
  parent: BaseItem
  id: DefaultStationBeacon
  name: "станційний маяк"
  description: "Невеликий пристрій, який передає інформацію на мапи станцій. Може бути налаштований."
  placement:
    mode: SnapgridCenter
  suffix: General
  components:
  - type: Sprite
    sprite: Objects/Devices/station_beacon.rsi
    drawdepth: BelowFloor
    layers:
    - state: blink
      map: ["base"]
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.NavMapBeaconVisuals.Enabled:
        base:
          True: {state: blink}
          False: {state: icon}
  - type: ConfigurableNavMapBeacon
  - type: NavMapBeacon
    defaultText: station-beacon-general
    color: "#D4D4D496"
  #- type: WarpPoint # Delta V - Removes in favor of Warp Point Markers
  - type: WizardTeleportWarpPoint # Goobstation
  - type: ActivatableUI
    key: enum.NavMapBeaconUiKey.Key
    singleUser: true
  - type: UserInterface
    interfaces:
      enum.NavMapBeaconUiKey.Key:
        type: NavMapBeaconBoundUserInterface
  - type: Item
    size: Small
  - type: SubFloorHide
  - type: Anchorable
  - type: Construction
    graph: StationBeaconPart
    node: complete
  - type: CollideOnAnchor
  - type: Physics
    canCollide: false
    bodyType: static
  - type: Transform
    anchored: true
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger: # for nukes
        !type:DamageTrigger
        damage: 200
      behaviors:
        - !type:DoActsBehavior
          acts: ["Destruction"]
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
        - !type:PlaySoundBehavior
          sound:
            collection: MetalBreak
            params:
              volume: -8
        - !type:SpawnEntitiesBehavior
          spawn:
            SheetSteel1:
              min: 1
              max: 1
          offset: 0
        - !type:DoActsBehavior
          acts: ["Breakage"]
  - type: StaticPrice
    price: 25

- type: entity
  parent: DefaultStationBeacon
  id: DefaultStationBeaconUnanchored
  suffix: General, Unanchored
  placement:
    mode: PlaceFree
  components:
  - type: Sprite
    sprite: Objects/Devices/station_beacon.rsi
    layers:
    - state: icon
      map: ["base"]
  - type: Physics
    canCollide: true
    bodyType: dynamic
  - type: Transform
    anchored: false

- type: entity
  parent: BaseItem
  id: StationBeaconPart
  name: "флетпак станцйного маяку"
  description: "Флетпак, що використовується для побудови станційного радіомаяка."
  components:
  - type: Item
    size: Small
    sprite: Objects/Devices/station_beacon.rsi
  - type: Sprite
    sprite: Objects/Devices/station_beacon.rsi
    state: assembly
  - type: Construction
    graph: StationBeaconPart
    node: start
    defaultTarget: complete

# Prototypes for various default beacon configurations.
- type: entity
  parent: DefaultStationBeacon
  id: DefaultStationBeaconCommand
  suffix: Command
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-command
    color: "#FFFF00"

- type: entity
  parent: DefaultStationBeaconCommand
  id: DefaultStationBeaconBridge
  suffix: Bridge
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-bridge

- type: entity
  parent: DefaultStationBeaconCommand
  id: DefaultStationBeaconVault
  suffix: Vault
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-vault

- type: entity
  parent: DefaultStationBeaconCommand
  id: DefaultStationBeaconCaptainsQuarters
  suffix: Captain's Quarters
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-captain

- type: entity
  parent: DefaultStationBeaconCommand
  id: DefaultStationBeaconHOPOffice
  suffix: HOP's Office
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-hop

- type: entity
  parent: DefaultStationBeacon
  id: DefaultStationBeaconSecurity
  suffix: Security
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-security
    color: "#DE3A3A"

- type: entity
  parent: DefaultStationBeaconSecurity
  id: DefaultStationBeaconBrig
  suffix: Brig
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-brig

- type: entity
  parent: DefaultStationBeaconSecurity
  id: DefaultStationBeaconWardensOffice
  suffix: Warden's Office
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-warden

- type: entity
  parent: DefaultStationBeaconSecurity
  id: DefaultStationBeaconHOSRoom
  suffix: HOS’s Room
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-hos

- type: entity
  parent: DefaultStationBeaconSecurity
  id: DefaultStationBeaconArmory
  suffix: Armory
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-armory

- type: entity
  parent: DefaultStationBeaconSecurity
  id: DefaultStationBeaconPermaBrig
  suffix: Perma Brig
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-perma-brig

- type: entity
  parent: DefaultStationBeaconSecurity
  id: DefaultStationBeaconDetectiveRoom
  suffix: Detective's Room
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-detective

- type: entity
  parent: DefaultStationBeaconSecurity
  id: DefaultStationBeaconCourtroom
  suffix: Courtroom
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-courtroom

- type: entity
  parent: DefaultStationBeaconService #Delta V - Lawer is Service until Justice Dept is a thing
  id: DefaultStationBeaconLawOffice
  suffix: Law Office
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-law

- type: entity
  parent: DefaultStationBeaconSecurity
  id: DefaultStationBeaconSecurityCheckpoint
  suffix: Sec Checkpoint
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-security-checkpoint

- type: entity
  parent: DefaultStationBeacon
  id: DefaultStationBeaconMedical
  suffix: Medical
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-medical
    color: "#52B4E9"

- type: entity
  parent: DefaultStationBeaconMedical
  id: DefaultStationBeaconMedbay
  suffix: Medbay
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-medbay

- type: entity
  parent: DefaultStationBeaconMedical
  id: DefaultStationBeaconChemistry
  suffix: Chemistry
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-chemistry

- type: entity
  parent: DefaultStationBeaconMedical
  id: DefaultStationBeaconCryonics
  suffix: Cryonics
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-cryonics

- type: entity
  parent: DefaultStationBeaconMedical
  id: DefaultStationBeaconCMORoom
  suffix: CMO's room
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-cmo

- type: entity
  parent: DefaultStationBeaconMedical
  id: DefaultStationBeaconMorgue
  suffix: Morgue
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-morgue

- type: entity
  parent: DefaultStationBeaconMedical
  id: DefaultStationBeaconSurgery
  suffix: Surgery
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-surgery

- type: entity
  parent: DefaultStationBeacon
  id: DefaultStationBeaconScience
  suffix: Epistemics #Delta V - Renamed
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-epistemics #Delta V - Renamed
    color: "#D381C9"

- type: entity
  parent: DefaultStationBeaconScience
  id: DefaultStationBeaconRND
  suffix: Research and Development
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-research-and-development

- type: entity
  parent: DefaultStationBeaconScience
  id: DefaultStationBeaconServerRoom
  suffix: Research Server Room
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-research-server

- type: entity
  parent: DefaultStationBeaconScience
  id: DefaultStationBeaconRDRoom
  suffix: MG's Room
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-mystagogue  #Delta V - Renamed

- type: entity
  parent: DefaultStationBeaconScience
  id: DefaultStationBeaconRobotics
  suffix: Robotics
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-robotics

- type: entity
  parent: DefaultStationBeaconScience
  id: DefaultStationBeaconArtifactLab
  suffix: Artifact Lab
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-artifact-lab

- type: entity
  parent: DefaultStationBeaconScience
  id: DefaultStationBeaconAnomalyGenerator
  suffix: Anomaly Generator
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-anomaly-gen

- type: entity
  parent: DefaultStationBeacon
  id: DefaultStationBeaconSupply
  suffix: Logistics #Delta V - Renamed
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-logistics #Delta V - Renamed
    color: "#A46106"

- type: entity
  parent: DefaultStationBeaconSupply
  id: DefaultStationBeaconCargoReception
  suffix: Logistics Reception #Delta V - Renamed
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-logistics-reception #Delta V - Renamed

- type: entity
  parent: DefaultStationBeaconSupply
  id: DefaultStationBeaconCargoBay
  suffix: Cargo Bay
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-cargo-bay

- type: entity
  parent: DefaultStationBeaconSupply
  id: DefaultStationBeaconQMRoom
  suffix: LO #Delta V - Renamed
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-lo #Delta V - Renamed

- type: entity
  parent: DefaultStationBeaconSupply
  id: DefaultStationBeaconSalvage
  suffix: Salvage
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-salvage

- type: entity
  parent: DefaultStationBeacon
  id: DefaultStationBeaconEngineering
  suffix: Engineering
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-engineering
    color: "#EFB341"

- type: entity
  parent: DefaultStationBeaconEngineering
  id: DefaultStationBeaconCERoom
  suffix: CE's Room
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-ce

- type: entity
  parent: DefaultStationBeaconEngineering
  id: DefaultStationBeaconAME
  suffix: AME
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-ame

- type: entity
  parent: DefaultStationBeaconEngineering
  id: DefaultStationBeaconSolars
  suffix: Solars
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-solars

- type: entity
  parent: DefaultStationBeaconEngineering
  id: DefaultStationBeaconGravGen
  suffix: Grav Gen
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-gravgen

- type: entity
  parent: DefaultStationBeaconEngineering
  id: DefaultStationBeaconAnchor
  suffix: Anchor
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-anchor

- type: entity
  parent: DefaultStationBeaconEngineering
  id: DefaultStationBeaconSingularity
  suffix: PA Control
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-pa

- type: entity
  parent: DefaultStationBeaconEngineering
  id: DefaultStationBeaconPowerBank
  suffix: SMES Power Bank
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-smes

- type: entity
  parent: DefaultStationBeaconEngineering
  id: DefaultStationBeaconTelecoms
  suffix: Telecoms
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-telecoms

- type: entity
  parent: DefaultStationBeaconEngineering
  id: DefaultStationBeaconAtmospherics
  suffix: Atmospherics
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-atmos

- type: entity
  parent: DefaultStationBeaconEngineering
  id: DefaultStationBeaconTEG
  suffix: TEG
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-teg

- type: entity
  parent: DefaultStationBeaconEngineering
  id: DefaultStationBeaconTechVault
  suffix: Tech Vault
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-tech-vault

- type: entity
  parent: DefaultStationBeacon
  id: DefaultStationBeaconService
  suffix: Service
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-service
    color: "#9FED58"

- type: entity
  parent: DefaultStationBeaconService
  id: DefaultStationBeaconKitchen
  suffix: Kitchen
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-kitchen

- type: entity
  parent: DefaultStationBeaconService
  id: DefaultStationBeaconBar
  suffix: Bar
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-bar

- type: entity
  parent: DefaultStationBeaconService
  id: DefaultStationBeaconBotany
  suffix: Botany
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-botany

- type: entity
  parent: DefaultStationBeaconService
  id: DefaultStationBeaconJanitorsCloset
  suffix: Janitor's Closet
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-janitor-closet #Delta V - Add for closet/outpost

- type: entity
  parent: DefaultStationBeacon
  id: DefaultStationBeaconAI
  suffix: AI
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-ai
    color: "#2ed2fd"

- type: entity
  parent: DefaultStationBeaconAI
  id: DefaultStationBeaconAISatellite
  suffix: AI Satellite
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-ai-sat

- type: entity
  parent: DefaultStationBeaconAI
  id: DefaultStationBeaconAICore
  suffix: AI Core
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-ai-core

- type: entity
  parent: DefaultStationBeacon
  id: DefaultStationBeaconArrivals
  suffix: Arrivals
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-arrivals

- type: entity
  parent: DefaultStationBeacon
  id: DefaultStationBeaconEvac
  suffix: Evac
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-evac

- type: entity
  parent: DefaultStationBeacon
  id: DefaultStationBeaconEVAStorage
  suffix: EVA Storage
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-eva-storage

- type: entity
  parent: DefaultStationBeaconScience #Delta V - Chapel in Epi
  id: DefaultStationBeaconChapel
  suffix: Chapel
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-chapel

- type: entity
  parent: DefaultStationBeacon
  id: DefaultStationBeaconLibrary
  suffix: Library
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-library

- type: entity
  parent: DefaultStationBeacon
  id: DefaultStationBeaconTheater
  suffix: Theater
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-theater

- type: entity
  parent: DefaultStationBeacon
  id: DefaultStationBeaconDorms
  suffix: Dorms
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-dorms

- type: entity
  parent: DefaultStationBeacon
  id: DefaultStationBeaconToolRoom
  suffix: Tool Room
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-tools

- type: entity
  parent: DefaultStationBeacon
  id: DefaultStationBeaconDisposals
  suffix: Disposals
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-disposals

- type: entity
  parent: DefaultStationBeacon
  id: DefaultStationBeaconCryosleep
  suffix: Cryosleep
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-cryosleep

- type: entity
  parent: DefaultStationBeacon
  id: DefaultStationBeaconEscapePod
  suffix: Escape Pod
  components:
  - type: NavMapBeacon
    defaultText: station-beacon-escape-pod
