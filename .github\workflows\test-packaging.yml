﻿name: Test Packaging

on:
  push:
    branches: [ master, staging, trying ]
    paths:
      - '**.cs'
      - '**.csproj'
      - '**.sln'
      - '**.git**'
      - '**.yml'
      # no docs on which one of these is supposed to work, so
      # why not just do both
      - 'RobustToolbox'
      - 'RobustToolbox/**'
  merge_group:
  pull_request:
    types: [ opened, reopened, synchronize, ready_for_review ]
    branches: [ master ]
    paths:
      - '**.cs'
      - '**.csproj'
      - '**.sln'
      - '**.git**'
      - '**.yml'
      - 'RobustToolbox'
      - 'RobustToolbox/**'

jobs:
  build:
    name: Test Packaging
    if: github.actor != 'PJBot' && github.event.pull_request.draft == false && github.actor != 'DeltaV-Bot' && github.actor != 'SimpleStation14'
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Master
        uses: actions/checkout@v3.6.0

      - name: Setup Submodule
        run: |
          git submodule update --init --recursive

      - name: Pull engine updates
        uses: space-wizards/submodule-dependency@v0.1.5

      - name: Update Engine Submodules
        run: |
          cd RobustToolbox/
          git submodule update --init --recursive

      - name: Setup .NET Core
        uses: actions/setup-dotnet@v3.2.0
        with:
          dotnet-version: 9.0.x

      - name: Install dependencies
        run: dotnet restore

      - name: Build Packaging
        run: dotnet build Content.Packaging --configuration Release --no-restore /m

      - name: Package server
        run: dotnet run --project Content.Packaging server --platform win-x64 --platform linux-x64 --platform osx-x64 --platform linux-arm64

      - name: Package client
        run: dotnet run --project Content.Packaging client --no-wipe-release

      - name: Update Build Info
        run: Tools/gen_build_info.py

      - name: Shuffle files around
        run: |
          mkdir "release/${{ github.sha }}"
          mv release/*.zip "release/${{ github.sha }}"
