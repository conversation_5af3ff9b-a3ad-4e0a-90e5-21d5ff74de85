- type: entity
  name: "лазерний хижак"
  id: MobLaserRaptor
  parent: SimpleMobBase
  description: "З часів вікінгів."
  components:
    - type: Body # Shitmed Change
      prototype: LaserRaptor
    - type: NpcFactionMember
      factions:
        - SimpleHostile
    - type: HTN
      rootTask:
        task: SimpleRangedHostileCompound
    - type: Sprite
      drawdepth: Mobs
      sprite: Mobs/Aliens/laser_raptor.rsi
      layers:
        - map: [ "enum.DamageStateVisualLayers.Base" ]
          state: laser_raptor
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeCircle
            radius: 0.30
          density: 80
          mask:
            - MobMask
          layer:
            - MobLayer
    - type: DamageStateVisuals
      states:
        Alive:
          Base: laser_raptor
        Dead:
          Base: laser_raptor_dead
    - type: Butcherable
      spawned:
        - id: FoodMeat
          amount: 2
    - type: MobThresholds
      thresholds:
        0: Alive
        100: Dead
    - type: MovementSpeedModifier
      baseWalkSpeed: 2
      baseSprintSpeed: 5
    - type: Tag
      tags:
        - FootstepSound
    - type: CombatMode
    - type: InteractionPopup
      successChance: 0.3
      interactSuccessString: petting-success-reptile
      interactFailureString: petting-failure-generic
      interactSuccessSound:
        path: /Audio/Animals/lizard_happy.ogg
    - type: Fauna # Lavaland Change
