﻿- type: constructionGraph
  id: LightFixture
  start: start
  graph:
    - node: start
      edges:
        - to: bulbLight
          steps:
            - material: Steel
              amount: 1
              doAfter: 2.0
        - to: tubeLight
          steps:
            - material: Steel
              amount: 2
              doAfter: 2.0
        - to: groundLight
          steps:
            - material: Steel
              amount: 5
              doAfter: 2.0
        - to: strobeLight
          steps:
            - material: Steel
              amount: 1
              doAfter: 2.0
    - node: tubeLight
      entity: PoweredlightEmpty
      edges:
        - to: start
          conditions:
            - !type:ContainerEmpty
              container: "light_bulb"
          steps:
            - tool: Screwing
              doAfter: 2.0
          completed:
            - !type:SpawnPrototype
              prototype: SheetSteel1
              amount: 2
            - !type:DeleteEntity {}
    - node: bulbLight
      entity: PoweredSmallLightEmpty
      edges:
        - to: start
          conditions:
            - !type:ContainerEmpty
              container: "light_bulb"
          steps:
            - tool: Screwing
              doAfter: 2.0
          completed:
            - !type:SpawnPrototype
              prototype: SheetSteel1
              amount: 1
            - !type:DeleteEntity {}
    - node: groundLight
      entity: PoweredLightPostSmallEmpty
      edges:
        - to: start
          conditions:
            - !type:ContainerEmpty
              container: "light_bulb"
          steps:
            - tool: Screwing
              doAfter: 4.0
          completed:
            - !type:SpawnPrototype
              prototype: SheetSteel1
              amount: 5
            - !type:DeleteEntity {}
    - node: strobeLight
      entity: PoweredStrobeLightEmpty
      edges:
        - to: start
          conditions:
            - !type:ContainerEmpty
              container: "light_bulb"
          steps:
            - tool: Screwing
              doAfter: 2.0
          completed:
            - !type:SpawnPrototype
              prototype: SheetSteel1
              amount: 1
            - !type:DeleteEntity {}