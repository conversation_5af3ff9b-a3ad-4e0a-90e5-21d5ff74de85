- type: entity
  parent: WeaponWandBase
  id: WeaponWandPolymorphBase
  abstract: true
  components:
  - type: Sprite
    layers:
    - state: poly
    - state: poly-effect
      map: ["effect"]
  - type: Gun
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/Magic/staff_animation.ogg
  - type: BasicEntityAmmoProvider
    proto: ProjectilePolyboltCarp
    capacity: 5

- type: entity
  name: "паличка коропового поліморфу"
  parent: WeaponWandPolymorphBase
  id: WeaponWandPolymorphCarp
  description: "Для тих, кому потрібно швидко приготувати коропове філе, а клоун виглядає соковито."
  components:
  - type: BasicEntityAmmoProvider
    proto: ProjectilePolyboltCarp
    capacity: 5
    count: 5

- type: entity
  name: "паличка мавпячого поліморфу"
  parent: WeaponWandPolymorphBase
  id: WeaponWandPolymorphMonkey
  description: "На випадок, коли вам потрібен друг-мавпа."
  components:
  - type: BasicEntityAmmoProvider
    proto: ProjectilePolyboltMonkey
    capacity: 5
    count: 5

- type: entity
  name: "паличка з вогняною кулею"
  parent: WeaponWandBase
  id: WeaponWandFireball
  description: "Величезні вогняні кулі!"
  components:
  - type: Sprite
    layers:
    - state: fire
    - state: fire-effect
      map: ["effect"]
  - type: Gun
    fireRate: 0.25
    soundGunshot:
      path: /Audio/Magic/fireball.ogg
  - type: BasicEntityAmmoProvider
    proto: ProjectileFireball
    capacity: 5
    count: 5

- type: entity
  name: "чарівна паличка миттєвої смерті"
  parent: WeaponWandBase
  id: WeaponWandDeath
  description: "Над створенням цієї краси працювали лише найкращі та найяскравіші з команди Space Wizards R&D."
  components:
  - type: Sprite
    layers:
    - state: death
    - state: death-effect
      map: ["effect"]
  - type: Gun
    soundGunshot:
      path: /Audio/_Goobstation/Wizard/wandodeath.ogg # Goob edit
  - type: BasicEntityAmmoProvider
    proto: ProjectileDeathBolt # Goob edit
    capacity: 3
    count: 3

- type: entity
  name: "паличка для входу"
  parent: WeaponWandBase
  id: WeaponWandPolymorphDoor
  description: "Для тих випадків, коли вам потрібен маршрут для втечі."
  components:
  - type: Sprite
    layers:
    - state: door
    - state: door-effect
      map: ["effect"]
  - type: Gun
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/Magic/staff_door.ogg
  - type: BasicEntityAmmoProvider
    proto: ProjectilePolyboltDoor
    capacity: 10
    count: 10

- type: entity
  name: "чарівна паличка"
  parent: WeaponWandPolymorphBase
  id: WeaponWandCluwne
  description: "Погіршити їхнє становище, перетворивши на клоунаду."
  components:
  - type: BasicEntityAmmoProvider
    proto: ProjectilePolyboltCluwne
    capacity: 3
    count: 3

- type: entity
  parent: WeaponWandPolymorphBase
  id: WeaponWandPolymorphBread
  name: "чарівна хлібна паличка"
  description: "Перетвори всіх своїх друзів на хліб! Свого боса! Твоїх ворогів! Свого пса! Зробіть з усього хліб!"
  components:
  - type: BasicEntityAmmoProvider
    proto: ProjectilePolyboltBread
    capacity: 10
    count: 10
#GOOB
- type: entity
  name: "персонал камери схову" # Goob edit
  description: "Артефакт, який випускає капсулюючі болти, щоб вивести з ладу твого ворога." # Goob edit
  parent: WeaponStaffBase # Goob edit
  id: WeaponWandLocker
  components:
  - type: Sprite
    sprite: _Goobstation/Wizard/Objects/Weapons/Staves/locker.rsi # Goobstation
    layers:
    - state: locker
      map: ["base"]
    - state: locker-effect
      map: ["effect"]
  - type: Gun
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/Magic/staff_animation.ogg
  - type: BasicEntityAmmoProvider
    proto: ProjectileLocker
    capacity: 6 # Goob edit
    count: 6 # Goob edit
  - type: Item
    inhandVisuals:
      left:
      - state: locker-inhand-left
      right:
      - state: locker-inhand-right
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.AmmoVisuals.HasAmmo:
        effect:
          True: { visible: False }
          False: { visible: True }
        base:
          True: { visible: True }
          False: { visible: False }
  - type: RechargeBasicEntityAmmo # Goobstation
    rechargeCooldown: 4
