# Command
- type: entity
  parent: DoorElectronics
  id: DoorElectronicsCaptain
  suffix: Captain, Locked
  components:
  - type: AccessReader
    access: [["Captain"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsHeadOfPersonnel
  suffix: HeadOfPersonnel, Locked
  components:
  - type: AccessReader
    access: [["HeadOfPersonnel"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsCommand
  suffix: Command, Locked
  components:
  - type: AccessReader
    access: [["Command"]]

# Service
- type: entity
  parent: DoorElectronics
  id: DoorElectronicsBar
  suffix: Bar, Locked
  components:
  - type: AccessReader
    access: [["Bar"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsBarKitchen
  suffix: Bar, Locked
  components:
  - type: AccessReader
    access: [["Bar"], ["Kitchen"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsHydroponics
  suffix: Hydroponics, Locked
  components:
  - type: AccessReader
    access: [["Hydroponics"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsChapel
  suffix: Chapel, Locked
  components:
  - type: AccessReader
    access: [["Chapel"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsTheatre
  suffix: Theatre, Locked
  components:
  - type: AccessReader
    access: [["Theatre"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsKitchen
  suffix: Kitchen, Locked
  components:
  - type: AccessReader
    access: [["Kitchen"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsKitchenHydroponics
  suffix: Kitchen/Hydroponics, Locked
  components:
  - type: AccessReader
    access: [["Kitchen"], ["Hydroponics"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsJanitor
  suffix: Janitor, Locked
  components:
  - type: AccessReader
    access: [["Janitor"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsLawyer
  suffix: Lawyer, Locked
  components:
  - type: AccessReader
    access: [["Lawyer"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsService
  suffix: Service, Locked
  components:
  - type: AccessReader
    access: [["Service"]]

# Cargo
- type: entity
  parent: DoorElectronics
  id: DoorElectronicsQuartermaster
  suffix: Quartermaster, Locked
  components:
  - type: AccessReader
    access: [["Quartermaster"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsSalvage
  suffix: Salvage, Locked
  components:
  - type: AccessReader
    access: [["Salvage"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsCargo
  suffix: Cargo, Locked
  components:
  - type: AccessReader
    access: [["Cargo"]]

# Engineering
- type: entity
  parent: DoorElectronics
  id: DoorElectronicsChiefEngineer
  suffix: ChiefEngineer, Locked
  components:
  - type: AccessReader
    access: [["ChiefEngineer"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsAtmospherics
  suffix: Atmospherics, Locked
  components:
  - type: AccessReader
    access: [["Atmospherics"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsEngineering
  suffix: Engineering, Locked
  components:
  - type: AccessReader
    access: [["Engineering"]]

# Science
- type: entity
  parent: DoorElectronics
  id: DoorElectronicsMorgue
  suffix: Morgue, Locked
  components:
  - type: AccessReader
    access: [["Medical"], ["Detective"], ["Chapel"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsResearchDirector
  suffix: ResearchDirector, Locked
  components:
  - type: AccessReader
    access: [["ResearchDirector"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsMedicalResearch
  suffix: Medical/Science, Locked
  components:
  - type: AccessReader
    access: [["Research"], ["Medical"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsResearch
  suffix: Research, Locked
  components:
  - type: AccessReader
    access: [["Research"]]

# Security
- type: entity
  parent: DoorElectronics
  id: DoorElectronicsHeadOfSecurity
  suffix: HeadOfSecurity, Locked
  components:
  - type: AccessReader
    access: [["HeadOfSecurity"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsArmory
  suffix: Armory, Locked
  components:
  - type: AccessReader
    access: [["Armory"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsDetective
  suffix: Detective, Locked
  components:
  - type: AccessReader
    access: [["Detective"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsSecurity
  suffix: Security, Locked
  components:
  - type: AccessReader
    access: [["Security"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsSecurityLawyer
  suffix: Security/Lawyer, Locked
  components:
  - type: AccessReader
    access: [["Security"], ["Lawyer"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsBrig
  suffix: Brig, Locked
  components:
  - type: AccessReader
    access: [["Brig"]]

# Medical
- type: entity
  parent: DoorElectronics
  id: DoorElectronicsChiefMedicalOfficer
  suffix: ChiefMedicalOfficer, Locked
  components:
  - type: AccessReader
    access: [["ChiefMedicalOfficer"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsChemistry
  suffix: Chemistry, Locked
  components:
  - type: AccessReader
    access: [["Chemistry"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsMedical
  suffix: Medical, Locked
  components:
  - type: AccessReader
    access: [["Medical"]]

# Syndicate
- type: entity
  parent: DoorElectronics
  id: DoorElectronicsNukeop
  suffix: Nukeop, Locked
  components:
  - type: AccessReader
    access: [["NuclearOperative"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsSyndicateAgent
  suffix: SyndicateAgent, Locked
  components:
  - type: AccessReader
    access: [["SyndicateAgent"]]

# Misc
- type: entity
  parent: DoorElectronics
  id: DoorElectronicsCentralCommand
  suffix: CentralCommand, Locked
  components:
  - type: AccessReader
    access: [["CentralCommand"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsExternal
  suffix: External, Locked
  components:
  - type: AccessReader
    access: [["External"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsMaintenance
  suffix: Maintenance, Locked
  components:
  - type: AccessReader
    access: [["Maintenance"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsVault
  suffix: Vault, Locked
  components:
  - type: AccessReader
    access: [["Security"], ["Command"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsChiefJustice
  suffix: ChiefJustice, Locked
  components:
  - type: AccessReader
    access: [["ChiefJustice"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsClerk
  suffix: Clerk, Locked
  components:
  - type: AccessReader
    access: [["Clerk"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsJustice
  suffix: Justice, Locked
  components:
  - type: AccessReader
    access: [["Justice"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsProsecutor
  suffix: Prosecutor, Locked
  components:
  - type: AccessReader
    access: [["Prosecutor"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsMantis
  suffix: Mantis, Locked
  components:
  - type: AccessReader
    access: [["Mantis"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsCorpsman
  suffix: Corpsman, Locked
  components:
  - type: AccessReader
    access: [["Corpsman"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsBoxer
  suffix: Boxer, Locked
  components:
  - type: AccessReader
    access: [["Boxer"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsClown
  suffix: Clown, Locked
  components:
  - type: AccessReader
    access: [["Clown"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsMime
  suffix: Mime, Locked
  components:
  - type: AccessReader
    access: [["Mime"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsMusician
  suffix: Musician, Locked
  components:
  - type: AccessReader
    access: [["Musician"]]


- type: entity
  parent: DoorElectronics
  id: DoorElectronicsReporter
  suffix: Reporter, Locked
  components:
  - type: AccessReader
    access: [["Reporter"]]


- type: entity
  parent: DoorElectronics
  id: DoorElectronicsLibrary
  suffix: Library, Locked
  components:
  - type: AccessReader
    access: [["Library"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsZookeeper
  suffix: Zookeeper, Locked
  components:
  - type: AccessReader
    access: [["Zookeeper"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsPsychologist
  suffix: Psychologist, Locked
  components:
  - type: AccessReader
    access: [["Psychologist"]]

- type: entity
  parent: DoorElectronics
  id: DoorElectronicsMail
  suffix: Mail, Locked
  components:
  - type: AccessReader
    access: [["Mail"]]

# Sol Alliance
- type: entity
  parent: DoorElectronics
  id: DoorElectronicsSAN
  suffix: SAN, Locked
  components:
  - type: AccessReader
    access: [["SAN"]]
