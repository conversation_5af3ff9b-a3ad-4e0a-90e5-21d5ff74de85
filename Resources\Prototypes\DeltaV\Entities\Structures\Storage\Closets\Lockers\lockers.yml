- type: entity
  id: LockerChiefJustice
  parent: LockerBaseSecure
  name: "шафка головного судді"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: cj 
    stateDoorOpen: cj_open
    stateDoorClosed: cj_door
  - type: AccessReader
    access: [["ChiefJustic<PERSON>"]]
    
- type: entity
  id: LockerClerk
  parent: LockerBaseSecure
  name: "шафка клерка"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: clerk
    stateDoorOpen: clerk_open
    stateDoorClosed: clerk_door
  - type: AccessReader
    access: [["Clerk"]]
