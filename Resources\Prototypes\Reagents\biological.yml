- type: reagent
  id: Blood
  name: reagent-name-blood
  group: Biological
  desc: reagent-desc-blood
  flavor: metallic
  color: "#800000"
  metamorphicSprite:
    sprite: Objects/Consumable/Drinks/bloodglass.rsi
    state: icon_empty
  metamorphicMaxFillLevels: 5
  metamorphicFillBaseName: fill-
  metamorphicChangeColor: false
  recognizable: true
  physicalDesc: reagent-physical-desc-ferrous
  slippery: false
  metabolisms:
    Drink:
      effects:
      - !type:SatiateThirst
        factor: 0.5
        conditions:
          - !type:OrganType
            type: Human
            shouldHave: false
      - !type:SatiateHunger
        factor: 0.5
        conditions:
        - !type:OrganType
          type: Vampiric
      - !type:AdjustReagent
        conditions:
        - !type:OrganType
          type: Vampiric
        reagent: Water
        amount: 0.3
      - !type:AdjustReagent
        conditions:
        - !type:OrganType
          type: Vampiric
        reagent: Protein
        amount: 0.25 # See below
      - !type:AdjustReagent
        conditions:
        - !type:OrganType
          type: Vampiric
        reagent: Omnizine
        amount: 0.3
      - !type:ChemAddMoodlet
        moodPrototype: DrankBlood
        conditions:
          - !type:OrganType
            type: Human
      - !type:ChemAddMoodlet
        moodPrototype: DrankBloodVampiric
        conditions:
          - !type:OrganType
            type: Vampiric
    Food:
      effects:
        - !type:AdjustReagent
          reagent: UncookedAnimalProteins
          amount: 0.125 # 0.25 proteins for 1u of blood - restores 0.75 hunger, adds 0.25 blood per unit
          conditions:
          - !type:OrganType
            type: Vampiric
            shouldHave: false
    Medicine:
      effects:
      - !type:HealthChange
        conditions:
        - !type:OrganType
          type: Bloodsucker
        damage:
          groups:
            Brute: -3
            Burn: -1.25
  plantMetabolism:
    - !type:PlantAdjustWater
      amount: 0.5
  footstepSound:
    collection: FootstepBlood
    params:
      volume: 6

- type: reagent
  parent: Blood
  id: InsectBlood
  name: reagent-name-insect-blood
  group: Biological
  desc: reagent-desc-insect-blood
  flavor: horrible
  color: "#808A51"
  recognizable: true
  physicalDesc: reagent-physical-desc-slimy
  slippery: false
  metabolisms:
    Drink:
      effects:
        - !type:ChemAddMoodlet
          moodPrototype: DrankInsectBlood

- type: reagent
  id: Slime
  name: reagent-name-slime
  group: Biological
  desc: reagent-desc-slime
  flavor: slimy
  color: "#2cf274"
  recognizable: true
  physicalDesc: reagent-physical-desc-viscous
  slippery: false
  viscosity: 0.25
  tileReactions:
    - !type:SpillTileReaction
  metabolisms:
    Food:
      # Delicious!
      effects:
      - !type:SatiateHunger
        factor: 1
  footstepSound:
    collection: FootstepBlood
    params:
      volume: 6

- type: reagent
  id: Sap
  name: reagent-name-sap
  group: Biological
  desc: reagent-desc-sap
  flavor: sweet
  color: "#cd7314"
  recognizable: true
  physicalDesc: reagent-physical-desc-sticky
  slippery: false
  viscosity: 0.10
  tileReactions:
    - !type:SpillTileReaction
  metabolisms:
    Food:
      # Sweet!
      effects:
      - !type:SatiateHunger
        factor: 0.5
      - !type:SatiateThirst
        factor: 0.5
  footstepSound:
    collection: FootstepBlood
    params:
      volume: 6

- type: reagent
  parent: Blood
  id: CopperBlood
  name: reagent-name-hemocyanin-blood
  group: Biological
  desc: reagent-desc-hemocyanin-blood
  flavor: metallic
  color: "#162581"
  recognizable: true
  physicalDesc: reagent-physical-desc-metallic
  slippery: false
  metabolisms:
    Drink:
      effects:
        - !type:ChemAddMoodlet
          moodPrototype: DrankBlood
          conditions:
            - !type:OrganType
              type: Human

- type: reagent
  parent: Blood
  id: AmmoniaBlood
  name: reagent-name-ammonia-blood
  group: Biological
  desc: reagent-desc-ammonia-blood
  flavor: bitter
  color: "#7a8bf2"
  recognizable: true
  physicalDesc: reagent-physical-desc-pungent
  slippery: false
  metabolisms:
    Drink:
      effects:
        - !type:ChemAddMoodlet
          moodPrototype: DrankBlood
          conditions:
            - !type:OrganType
              type: Human

- type: reagent
  id: ZombieBlood
  name: reagent-name-zombie-blood
  group: Biological
  desc: reagent-desc-zombie-blood
  physicalDesc: reagent-physical-desc-necrotic
  flavor: bitter
  color: "#2b0700"
  slippery: false
  metabolisms:
    Drink:
      # Disgusting!
      effects:
      - !type:SatiateThirst
        factor: -0.5
    Poison:
      effects:
      - !type:HealthChange
        damage:
          types:
            Poison: 4
      - !type:ChemVomit
        probability: 0.25
      - !type:CauseZombieInfection
      - !type:ChemAddMoodlet
        moodPrototype: DrankZombieBlood

- type: reagent
  id: Ichor
  name: reagent-name-ichor
  group: Biological
  desc: reagent-desc-ichor
  physicalDesc: reagent-physical-desc-roaring
  flavor: metallic
  color: "#f4692e"
  recognizable: true
  metabolisms:
    Drink:
      effects:
      - !type:SatiateThirst
        factor: 1.5
    # Dragon doesn't require airloss healing, so omnizine is still best for humans.
    Medicine:
      effects:
      - !type:ModifyBloodLevel
        amount: 3
      - !type:HealthChange
        damage:
          groups:
            Burn: -5
            Brute: -5
            Toxin: -2
          types:
            Bloodloss: -5
  # Just in case you REALLY want to water your plants
  plantMetabolism:
  - !type:PlantAdjustWater
    amount: 0.5
  footstepSound:
    collection: FootstepBlood
    params:
      volume: 6

- type: reagent
  id: Fat
  name: reagent-name-fat
  group: Biological
  desc: reagent-desc-fat
  flavor: terrible
  color: "#d8d8b0"
  physicalDesc: reagent-physical-desc-exotic-smelling
  slippery: false
  footstepSound:
    collection: FootstepBlood
    params:
      volume: 6

- type: reagent
  id: Vomit
  name: reagent-name-vomit
  group: Biological
  desc: reagent-desc-vomit
  flavor: terrible
  color: "#87ab08"
  physicalDesc: reagent-physical-desc-pungent
  slippery: true
  metabolisms:
    Drink:
      effects:
      - !type:SatiateThirst
        factor: 0.5
      - !type:AdjustReagent
        reagent: Nutriment
        amount: 0.1
      - !type:ChemAddMoodlet
        moodPrototype: DrankVomit
  footstepSound:
    collection: FootstepBlood
    params:
      volume: 6

- type: reagent
  id: GreyMatter
  name: reagent-name-grey-matter
  group: Biological
  desc: reagent-desc-grey-matter
  physicalDesc: reagent-physical-desc-neural
  flavor: mindful
  color: "#C584B8"
  slippery: false
  metabolisms:
    Drink:
      effects:
      - !type:SatiateHunger
        factor: 1.5
    Poison:
      effects:
      - !type:HealthChange
        damage:
          types:
            Cellular: 2

- type: reagent
  parent: Blood
  id: BlackBlood
  name: reagent-name-black-blood
  group: Biological
  desc: reagent-desc-black-blood
  flavor: bitter
  color: "#1c1624"
  recognizable: true
  physicalDesc: reagent-physical-desc-inky
  slippery: false
