<controls:FancyWindow xmlns="https://spacestation14.io"
                xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                xmlns:networkConfigurator="clr-namespace:Content.Client.NetworkConfigurator"
                Title="Network Configurator" MinSize="620 400" RectClipContent="True">
    <BoxContainer Orientation="Vertical" VerticalExpand="True">
        <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
            <Label Margin="12 0" Text="{Loc signal-port-selector-help}"/>
        </BoxContainer>
        <Control VerticalExpand="True" HorizontalExpand="True" Margin="12 6 12 0">
            <PanelContainer Name="MainPanel" HorizontalExpand="False" VerticalExpand="True" />
            <ScrollContainer HorizontalExpand="True" VerticalExpand="True" HScrollEnabled="True">
                <BoxContainer Orientation="Horizontal" HorizontalExpand="True" VerticalExpand="True" Margin="6">
                    <BoxContainer Orientation="Vertical" HorizontalExpand="True" SizeFlagsStretchRatio="0.3">
                        <RichTextLabel Name="HeaderLeft"/>
                        <BoxContainer Name="ButtonContainerLeft" Orientation="Vertical" VerticalExpand="True" HorizontalExpand="True"/>
                    </BoxContainer>
                    <BoxContainer Name="MiddleContainer" Orientation="Vertical" HorizontalExpand="True" VerticalExpand="True" SizeFlagsStretchRatio="0.2"/>
                    <BoxContainer Orientation="Vertical" HorizontalExpand="True" SizeFlagsStretchRatio="0.3">
                        <RichTextLabel Name="HeaderRight"/>
                        <BoxContainer Name="ButtonContainerRight" Orientation="Vertical" VerticalExpand="True" HorizontalExpand="True"/>
                    </BoxContainer>
                </BoxContainer>
            </ScrollContainer>
        </Control>
        <BoxContainer Orientation="Horizontal" HorizontalExpand="True" Margin="12 4">
            <BoxContainer Orientation="Horizontal">
                <Button Name="ButtonClear" SetHeight="32" StyleClasses="OpenRight" Text="{Loc signal-port-selector-menu-clear}"/>
                <Button Name="ButtonLinkDefault" SetHeight="32" StyleClasses="OpenLeft" Text="{Loc signal-port-selector-menu-link-defaults}"/>
            </BoxContainer>
            <Control HorizontalExpand="True"/>
            <Control>
                <Button Name="ButtonOk" MinHeight="26" StyleClasses="ButtonColorGreen" Text="{Loc signal-port-selector-menu-done}"></Button>
            </Control>
        </BoxContainer>
        <Control SetHeight="28" Margin="1 0 2 1">
            <PanelContainer Name="FooterPanel"></PanelContainer>
            <BoxContainer Name="ContentFooter" HorizontalExpand="True" SetHeight="28">
                <Label Text="Net#Link ™" VerticalAlignment="Center" Margin="6 0" StyleClasses="PdaContentFooterText"/>
                <Label Name="AddressLabel" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="6 0" StyleClasses="PdaContentFooterText"/>
                <Control HorizontalExpand="True"/>
                <Label Name="FromAddressLabel" Margin="6 0" StyleClasses="PdaContentFooterText"/>
                <Label SetWidth="25" StyleClasses="PdaContentFooterText" Align="Center" Text="➝"/><!--Turn this into an arrow texture-->
                <Label Name="ToAddressLabel" Margin="6 0" StyleClasses="PdaContentFooterText"/>
            </BoxContainer>
        </Control>
    </BoxContainer>
</controls:FancyWindow>
