# Commands
## Delay shuttle round end
emergency-shuttle-command-round-desc = Зупиняє таймер, що закінчує раунд, коли аварійний шатл покидає гіперпростір.
emergency-shuttle-command-round-yes = Раунд відкладено.
emergency-shuttle-command-round-no = Неможливо відкласти закінчення раунду.

## Dock emergency shuttle
emergency-shuttle-command-dock-desc = Викликає аварійний шатл і пристиковує його до станції... якщо це можливо.

## Launch emergency shuttle
emergency-shuttle-command-launch-desc = Ранній запуск аварійного шатлу, якщо це можливо.

# Emergency shuttle
emergency-shuttle-left = Аварійний Шатл покинув станцію. Приблизний час до прибуття на ЦК: {$transitTime} секунд.
emergency-shuttle-launch-time = Аварійний Шатл стартує через {$consoleAccumulator} секунд.
emergency-shuttle-docked = Аварійний Шатл пристикувався до станції зі сторони: {$direction}. Він відчалює через {$time} секунд.
emergency-shuttle-good-luck = Аварійний Шатл не може знайти станцію. Хай щастить.
emergency-shuttle-nearby = Аварійний Шатл не може знайти вільний стикувальний порт. Він був переміщений у напрямку {$direction} від станції.

# Emergency shuttle console popup / announcement
emergency-shuttle-console-no-early-launches = Достроковий запуск вимкнено
emergency-shuttle-console-auth-left = Необхідно {$remaining} авторизацій, для дострокового запуску шатлу.
emergency-shuttle-console-auth-revoked = Авторизацію для дострокового запуску скасовано, необхідно авторизацій: {$remaining}.
emergency-shuttle-console-denied = У доступі відмовлено.

# UI
emergency-shuttle-console-window-title = Консоль Аварійного Шатлу
emergency-shuttle-ui-engines = ДВИГУНИ:
emergency-shuttle-ui-idle = У режимі очікування
emergency-shuttle-ui-repeal-all = Анулювати всі
emergency-shuttle-ui-early-authorize = Авторизація Дострокового Запуску
emergency-shuttle-ui-authorize = АВТОРИЗУВАТИ
emergency-shuttle-ui-repeal = СКАСУВАТИ
emergency-shuttle-ui-authorizations = Авторизацій
emergency-shuttle-ui-remaining = Залишилося: {$remaining}
