- type: entity
  parent: BaseItem
  id: BaseArrow
  abstract: true
  components:
  - type: Item
    size: Small
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/arrows.rsi
  - type: Fixtures
    fixtures:
      fix1:
        shape: !type:PhysShapeCircle
          radius: 0.2
        density: 5
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.1,-0.1,0.1,0.1"
        hard: false
        mask:
        - Impassable
        - BulletImpassable
  - type: EmbeddableProjectile
    sound: /Audio/Weapons/star_hit.ogg
    embedOnThrow: false
  - type: EmbedPassiveDamage
  - type: ThrowingAngle
    angle: 0
  - type: Ammo
    muzzleFlash: null
  - type: Tag
    tags:
    - Arrow
  - type: Projectile
    deleteOnCollide: false
    onlyCollideWhenShot: true
    damage:
      types:
        Piercing: 25
  - type: SolutionContainerManager
    solutions:
      ammo:
        maxVol: 2
  - type: RefillableSolution
    solution: ammo
  - type: InjectableSolution
    solution: ammo
  - type: SolutionInjectOnEmbed
    transferAmount: 2
    solution: ammo
  - type: SolutionTransfer
    maxTransferAmount: 2
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 1
    fillBaseName: solution


- type: entity
  parent: BaseArrow
  id: ArrowRegular
  name: "стрілка"
  description: "Ви можете відчути силу степу всередині себе."
  components:
  - type: Sprite
    layers:
    - state: tail
      color: red
    - state: rod
      color: brown
    - state: tip
    - state: solution1
      map: ["enum.SolutionContainerLayers.Fill"]
      visible: false
  - type: Projectile
    damage:
      types:
        Piercing: 35

- type: entity
  parent: BaseArrow
  id: ArrowImprovised
  name: "стріла з осколків скла"
  description: "Улюблений снаряд сірої сорочки."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/arrows.rsi
    layers:
    - state: tail
      color: white
    - state: rod
      color: darkgray
    - state: tip
      color: lightblue
    - state: solution1
      map: ["enum.SolutionContainerLayers.Fill"]
      visible: false
  - type: Projectile
    damage:
      types:
        Piercing: 25
  - type: Construction
    graph: ImprovisedArrow
    node: ImprovisedArrow
