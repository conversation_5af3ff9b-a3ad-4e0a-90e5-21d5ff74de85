- type: entity
  name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"
  parent: BaseWeaponRifle
  id: WeaponRifleVulcan
  description: "Одна з перших вогнепальних гвинтівок, модифікованих для використання в космосі, ця гвинтівка була спеціально розроблена для пробивання отворів у каргоніанських \"бойових механізмах\". Використовує гвинтівкові набої калібру .30."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Rifles/vulcan.rsi
    layers:
      - state: base
        map: ["enum.GunVisualLayers.Base"]
      - state: mag-0
        map: ["enum.GunVisualLayers.Mag"]
  - type: Gun
    fireRate: 2
    selectedMode: SemiAuto
    availableModes:
    - SemiAuto
  - type: ChamberMagazineAmmoProvider
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        startingItem: MagazineLightRifle
        insertSound: /Audio/Weapons/Guns/MagIn/ltrifle_magin.ogg
        ejectSound: /Audio/Weapons/Guns/MagOut/ltrifle_magout.ogg
        priority: 2
        whitelist:
          tags:
            - MagazineLightRifle
      gun_chamber:
        name: Chamber
        startingItem: CartridgeLightRifle
        priority: 1
        whitelist:
          tags:
            - CartridgeLightRifle
  - type: ContainerContainer
    containers:
      gun_magazine: !type:ContainerSlot
      gun_chamber: !type:ContainerSlot
  - type: MagazineVisuals
    magState: mag
    steps: 1
    zeroVisible: true
  - type: Appearance
  - type: Clothing
    sprite: DeltaV/Objects/Weapons/Guns/Rifles/vulcan.rsi

- type: entity
  name: "CAWS-25 Галка"
  parent: BaseWeaponRifle
  id: WeaponRifleJackdaw
  description: "Початок кінця сповіщає пісня галки. Використовує безоболонкові набої калібру .25."
  suffix: Deathsquad
  components:
  - type: Item
    size: Large
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Rifles/jackdaw.rsi
    layers:
      - state: base
        map: ["enum.GunVisualLayers.Base"]
      - state: mag-0
        map: ["enum.GunVisualLayers.Mag"]
  - type: Gun
    minAngle: 2
    maxAngle: 6
    angleIncrease: 2
    angleDecay: 8
    fireRate: 8
    selectedMode: FullAuto
    availableModes:
    - SemiAuto
    - FullAuto
    soundGunshot:
      path: /Audio/DeltaV/Weapons/Guns/Gunshots/jackdaw.ogg
  - type: ChamberMagazineAmmoProvider
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        startingItem: MagazineCaselessRifle
        insertSound: /Audio/Weapons/Guns/MagIn/batrifle_magin.ogg
        ejectSound: /Audio/Weapons/Guns/MagOut/batrifle_magout.ogg
        priority: 2
        whitelist:
          tags:
            - MagazineCaselessRifle
      gun_chamber:
        name: Chamber
        startingItem: CartridgeCaselessRifle
        priority: 1
        whitelist:
          tags:
            - CartridgeCaselessRifle
  - type: ContainerContainer
    containers:
      gun_magazine: !type:ContainerSlot
      gun_chamber: !type:ContainerSlot
  - type: MagazineVisuals
    magState: mag
    steps: 1
    zeroVisible: true
  - type: Appearance
  - type: Clothing
    sprite: DeltaV/Objects/Weapons/Guns/Rifles/jackdaw.rsi

- type: entity
  name: "Тенебра"
  parent: BaseWeaponRifle
  id: WeaponRifleTenebra
  description: "Серійна та мінімалістична гвинтівка в корпусі буллпап. Використовує гвинтівкові набої калібру .20."
  suffix: Mercenary
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Rifles/tenebra.rsi
    layers:
      - state: base
        map: ["enum.GunVisualLayers.Base"]
      - state: mag-0
        map: ["enum.GunVisualLayers.Mag"]
  - type: Gun
    minAngle: 3
    maxAngle: 12
    angleIncrease: 3
    angleDecay: 12
    fireRate: 5
    selectedMode: FullAuto
    availableModes:
    - SemiAuto
    - FullAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/ltrifle.ogg
  - type: ChamberMagazineAmmoProvider
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        startingItem: MagazineRifle
        insertSound: /Audio/Weapons/Guns/MagIn/ltrifle_magin.ogg
        ejectSound: /Audio/Weapons/Guns/MagOut/ltrifle_magout.ogg
        priority: 2
        whitelist:
          tags:
            - MagazineRifle
      gun_chamber:
        name: Chamber
        startingItem: CartridgeRifle
        priority: 1
        whitelist:
          tags:
            - CartridgeRifle
  - type: ContainerContainer
    containers:
      gun_magazine: !type:ContainerSlot
      gun_chamber: !type:ContainerSlot
  - type: MagazineVisuals
    magState: mag
    steps: 1
    zeroVisible: true
  - type: Appearance
  - type: Clothing
    sprite: DeltaV/Objects/Weapons/Guns/Rifles/tenebra.rsi

- type: entity
  name: "мушкет"
  id: WeaponRifleMusket
  parent: Musket
  suffix: "craftable"
  description: "Старий кремінний мушкет. Використовує мушкетні патрони."
  components:
  - type: BallisticAmmoProvider
    whitelist:
      tags:
      - CartridgeMusket
    capacity: 1
    proto: CartridgeMusket
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Rifles/musket.rsi
    state: base
  - type: Gun #Smoothbore
    fireRate: .125
    resetOnHandSelected: false
    minAngle: 22
    maxAngle: 43
  - type: Wieldable
  - type: GunWieldBonus
    minAngle: -20
    maxAngle: -40
  - type: Construction
    graph: WeaponRifleMusketGraph
    node: musket
  - type: UseDelay
    delay: 0 #Straight up doesn't work for guns
