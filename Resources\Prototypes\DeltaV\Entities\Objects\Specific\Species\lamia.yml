# Delta-V - This file is licensed under AGPLv3
# Copyright (c) 2024 Delta-V Contributors
# See AGPLv3.txt for details.

- type: entity
  id: LamiaInitialSegment
  save: false
  categories: [ HideSpawnMenu ]
  components:
  - type: Damageable
  - type: StandingState
  - type: Clickable
  - type: InteractionOutline
  - type: PsionicInsulation #Not a brain, target the lamia instead
  - type: Physics
    bodyType: KinematicController
  - type: Fixtures
    fixtures: # TODO: This needs a second fixture just for mob collisions.
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.25
        density: 80
        restitution: 0.0
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Transform
    anchored: false
  - type: Tag
    tags:
    - HideContextMenu
  - type: RequireProjectileTarget
    active: True

- type: entity
  id: LamiaSegment
  save: false
  parent: LamiaInitialSegment
  name: "сегмент ламії"
  categories: [ HideSpawnMenu ]
  description: "Хвостовий сегмент, сподіваємось, прикріплений до ламії."
  components:
  - type: Sprite
  - type: Tag
    tags:
    - HideContextMenu
    - DoorBumpOpener
