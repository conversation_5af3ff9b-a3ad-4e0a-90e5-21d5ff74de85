<controls:FancyWindow xmlns="https://spacestation14.io"
                      xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                      Title="{Loc 'handheld-radio-menu-title'}"
                      MinSize="200 150"
                      SetSize="200 150">
    <BoxContainer Orientation="Vertical"
                  HorizontalExpand="True"
                  VerticalExpand="True"
                  Margin="5 0 5 0">
        <Control MinHeight="10"/>
        <BoxContainer Orientation="Vertical" SeparationOverride="4" MinWidth="150">
            <Label Name="CurrentTextFrequency" Text="{Loc 'handheld-radio-current-text-frequency'}" />
            <LineEdit Name="FrequencyLineEdit" Text="1330"/>
        </BoxContainer>
        <BoxContainer Orientation="Horizontal" HorizontalExpand="True" HorizontalAlignment="Right" Margin="5 0 5 5">
            <Button Name="MicButton" ToggleMode="True" Text="{Loc 'handheld-radio-button-text-mic'}" StyleClasses="OpenRight" MinWidth="70"/>
            <Button Name="SpeakerButton" ToggleMode="True" Text="{Loc 'handheld-radio-button-text-speaker'}" StyleClasses="OpenLeft" MinWidth="70"/>
        </BoxContainer>
        <BoxContainer Orientation="Vertical">
            <PanelContainer StyleClasses="LowDivider" />
            <BoxContainer Orientation="Horizontal" Margin="10 2 5 0" VerticalAlignment="Bottom">
                <Label Text="{Loc 'handheld-radio-flavor-text-left'}" StyleClasses="WindowFooterText"
                       HorizontalAlignment="Left" HorizontalExpand="True"  Margin="0 0 5 0" />
                <TextureRect StyleClasses="NTLogoDark" Stretch="KeepAspectCentered"
                             VerticalAlignment="Center" HorizontalAlignment="Right" SetSize="19 19"/>
            </BoxContainer>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
