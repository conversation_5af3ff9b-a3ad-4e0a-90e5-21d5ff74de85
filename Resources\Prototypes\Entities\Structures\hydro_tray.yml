- type: entity
  name: "лоток для гідропоніки"
  parent: [ hydroponicsSoil, SmallConstructibleMachine]
  id: hydroponicsTray
  description: "Космічна ферма міжзоряного рівня, що дозволяє швидко вирощувати та селекціонувати сільськогосподарські культури. Тільки... не забувайте про космічні бур'яни."
  components:
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.1"
        density: 190
        hard: true
        mask:
        - MachineMask
        layer:
        - BulletImpassable
  - type: Anchorable
  - type: Pullable
  - type: Sprite
    layers:
      - sprite: Structures/Hydroponics/containers.rsi
        state: hydrotray3
      - sprite: Structures/Hydroponics/overlays.rsi
        state: lowhealth3
        map: [ "health_alert" ]
        visible: false
      - sprite: Structures/Hydroponics/overlays.rsi
        state: lowwater3
        map: [ "water_alert" ]
        visible: false
      - sprite: Structures/Hydroponics/overlays.rsi
        state: lownutri3
        map: [ "nutri_alert" ]
        visible: false
      - sprite: Structures/Hydroponics/overlays.rsi
        state: alert3
        map: [ "undefined_alert" ]
        visible: false
      - sprite: Structures/Hydroponics/overlays.rsi
        state: harvest3
        map: [ "harvest_alert" ]
        visible: false
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.PlantHolderVisuals.HealthLight:
        health_alert:
          True: { visible: true }
          False: { visible: false }
      enum.PlantHolderVisuals.WaterLight:
        water_alert:
          True: { visible: true }
          False: { visible: false }
      enum.PlantHolderVisuals.NutritionLight:
        nutri_alert:
          True: { visible: true }
          False: { visible: false }
      enum.PlantHolderVisuals.AlertLight:
        undefined_alert:
          True: { visible: true }
          False: { visible: false }
      enum.PlantHolderVisuals.HarvestLight:
        harvest_alert:
          True: { visible: true }
          False: { visible: false }
  - type: PlantHolder
    drawWarnings: true
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:ChangeConstructionNodeBehavior
        node: machineFrame
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Machine
    board: HydroponicsTrayMachineCircuitboard
  - type: WiresPanel
  - type: AmbientSound
    volume: -9
    range: 5
    sound:
      path: /Audio/Ambience/Objects/flowing_water_open.ogg
  - type: GuideHelp
    guides:
    - Botany
  - type: Tag
    tags:
      - NoPaint

- type: entity
  parent: hydroponicsTray
  id: HydroponicsTrayEmpty
  suffix: Empty
  components:
  - type: PlantHolder
    waterLevel: 0
    nutritionLevel: 0
    # for the lights to update immediately
    updateSpriteAfterUpdate: true
