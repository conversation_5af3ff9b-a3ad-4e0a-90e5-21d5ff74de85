- type: entity
  parent: BaseComputerAiAccess
  id: ComputerAlert
  name: "консоль атмосферних попереджень"
  description: "Використовується для доступу до автоматизованої системи оповіщення про станцію."
  components:
  - type: Computer
    board: AlertsComputerCircuitboard
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: alert-0
    - map: ["computerLayerKeys"]
      state: atmos_key
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: GenericVisualizer
    visuals:
      enum.ComputerVisuals.Powered:
        computerLayerScreen:
          True: { visible: true, shader: unshaded }
          False: { visible: false }
        computerLayerKeys:
          True: { visible: true, shader: unshaded }
          False: { visible: true, shader: shaded }
      enum.AtmosAlertsComputerVisuals.ComputerLayerScreen:
        computerLayerScreen:
          0: { state: alert-0 }
          1: { state: alert-0 }
          2: { state: alert-1 }
          3: { state: alert-2 }
          4: { state: alert-2 }
      enum.WiresVisuals.MaintenancePanelState:
        enum.WiresVisualLayers.MaintenancePanel:
          True: { visible: false }
          False: { visible: true }
  - type: AtmosAlertsComputer
  - type: ActivatableUI
    singleUser: true
    key: enum.AtmosAlertsComputerUiKey.Key
  - type: UserInterface
    interfaces:
      enum.AtmosAlertsComputerUiKey.Key:
        type: AtmosAlertsComputerBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface

- type: entity
  parent: BaseComputerAiAccess
  id: ComputerAtmosMonitoring
  name: "монітор атмосферної мережі"
  description: "Використовується для моніторингу атмосферних мереж станції."
  components:
  - type: Computer
    board: AtmosMonitoringComputerCircuitboard
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: tank
    - map: ["computerLayerKeys"]
      state: atmos_key
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: AtmosMonitoringConsole
    navMapTileColor: "#1a1a1a"
    navMapWallColor: "#404040"
  - type: ActivatableUI
    singleUser: true
    key: enum.AtmosMonitoringConsoleUiKey.Key
  - type: UserInterface
    interfaces:
      enum.AtmosMonitoringConsoleUiKey.Key:
        type: AtmosMonitoringConsoleBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface

- type: entity
  parent: BaseComputer
  id: ComputerEmergencyShuttle
  name: "консоль аварійного шатлу"
  description: "Забезпечує дозвіл на достроковий запуск шатла."
  components:
  - type: AccessReader
    access:
    - [ Command ]
  - type: EmergencyShuttleConsole
  - type: ActivatableUI
    key: enum.EmergencyConsoleUiKey.Key
  - type: UserInterface
    interfaces:
        enum.EmergencyConsoleUiKey.Key:
          type: EmergencyConsoleBoundUserInterface
        enum.WiresUiKey.Key:
          type: WiresBoundUserInterface
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#43ccb5"
  - type: Rotatable
    rotateWhileAnchored: true

- type: entity
  parent: BaseComputer
  id: BaseComputerShuttle
  name: "консоль шатлу"
  description: "Був пілотом шатла."
  abstract: true
  components:
  - type: ShuttleConsole
  - type: ActivatableUI
    key: enum.ShuttleConsoleUiKey.Key
  - type: UserInterface
    interfaces:
        enum.ShuttleConsoleUiKey.Key:
          type: ShuttleConsoleBoundUserInterface
        enum.WiresUiKey.Key:
          type: WiresBoundUserInterface
  - type: RadarConsole
  - type: WorldLoader
    radius: 256
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#43ccb5"
  - type: Rotatable
    rotateWhileAnchored: true
  - type: ItemSlots
    slots:
      disk_slot:
        name: Disk
        insertSound:
          path: /Audio/Machines/terminal_insert_disc.ogg
        ejectSound:
          path: /Audio/Machines/terminal_insert_disc.ogg
        whitelist:
          components:
          - ShuttleDestinationCoordinates
  - type: ContainerContainer
    containers:
      board: !type:Container
        ents: []
      disk_slot: !type:ContainerSlot {}

- type: entity
  parent: BaseComputerShuttle
  id: ComputerShuttle
  name: "консоль шатлу"
  description: "Був пілотом шатла."
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: shuttle
    - map: ["computerLayerKeys"]
      state: generic_keys
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: Computer
    board: ShuttleConsoleCircuitboard

- type: entity
  parent: BaseComputerShuttle
  id: ComputerShuttleSyndie
  name: "консоль синдикатського шатлу"
  description: "Використовувався для пілотування шатлу синдикату."
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: syndishuttle
    - map: ["computerLayerKeys"]
      state: syndie_key
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: Tag
    tags:
      - Syndicate
  - type: RadarConsole
    maxRange: 384
  - type: WorldLoader
    radius: 1536
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#c94242"
  - type: Computer
    board: SyndicateShuttleConsoleCircuitboard
  - type: ShuttleConsole
    ftlToPlanets: true

- type: entity
  parent: BaseComputerShuttle
  id: ComputerShuttleSyndieDropPod
  name: "консоль капсули синдикату"
  description: "Використовується для запуску капсули. Це поїздка в один кінець, переконайтеся, що ви взяли з собою обід."
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: syndishuttle
    - map: ["computerLayerKeys"]
      state: syndie_key
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: Tag
    tags:
      - Syndicate
  - type: RadarConsole
    maxRange: 384
  - type: WorldLoader
    radius: 1536
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#c94242"
  - type: Computer
    board: SyndicateShuttleConsoleCircuitboard
  - type: ShuttleConsole
    ftlToPlanets: true
    ignoreExclusionZones: true # It can intentionally be crashed into the station, crushing whatever's underneath the target location.
    oneWayTrip: true

- type: entity
  parent: BaseComputerShuttle
  id: ComputerShuttleCargo
  name: "консоль вантажного шатлу"
  description: "Використовувався для пілотування вантажного шатлу."
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: shuttle
    - map: ["computerLayerKeys"]
      state: generic_keys
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: DroneConsole
    components:
      - type: CargoShuttle
  - type: RadarConsole
    maxRange: 256
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#c94242"
  - type: Computer
    board: CargoShuttleConsoleCircuitboard
  - type: StealTarget
    stealGroup: CargoShuttleConsoleCircuitboard
  - type: ShuttleConsole
    ftlToPlanets: true

- type: entity
  parent: BaseComputerShuttle
  id: ComputerShuttleSalvage
  name: "консоль утилізаторського шатлу"
  description: "Використовується для пілотування шатлу утилізаторів."
  components:
    - type: Sprite
      layers:
        - map: ["computerLayerBody"]
          state: computer
        - map: ["computerLayerKeyboard"]
          state: generic_keyboard
        - map: ["computerLayerScreen"]
          state: shuttle
        - map: ["computerLayerKeys"]
          state: generic_keys
    - type: DroneConsole
      components:
        - type: SalvageShuttle
    - type: RadarConsole
      maxRange: 256
    - type: PointLight
      radius: 1.5
      energy: 1.6
      color: "#c94242"
    - type: Computer
      board: SalvageShuttleConsoleCircuitboard
    - type: StealTarget
      stealGroup: SalvageShuttleConsoleCircuitboard
    - type: ShuttleConsole
      ftlToPlanets: true

- type: entity
  parent: BaseComputerAiAccess
  id: ComputerIFF
  name: "консоль IFF"
  description: "Дозволяє контролювати IFF-характеристики цього судна."
  components:
  - type: IFFConsole
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      sprite: Structures/Shuttles/iff.rsi
      state: helm
    - map: ["computerLayerKeys"]
      state: generic_keys
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: ActivatableUI
    key: enum.IFFConsoleUiKey.Key
  - type: UserInterface
    interfaces:
        enum.IFFConsoleUiKey.Key:
          type: IFFConsoleBoundUserInterface
        enum.WiresUiKey.Key:
          type: WiresBoundUserInterface
  - type: Computer
    board: ComputerIFFCircuitboard

- type: entity
  parent: ComputerIFF
  id: ComputerIFFSyndicate
  name: "консоль IFF"
  suffix: Syndicate
  description: "Дозволяє контролювати IFF і стелс-характеристики цього судна."
  components:
  - type: IFFConsole
    allowedFlags:
      - Hide
      - HideLabel
  - type: ActivatableUI
    key: enum.IFFConsoleUiKey.Key
  - type: UserInterface
    interfaces:
        enum.IFFConsoleUiKey.Key:
          type: IFFConsoleBoundUserInterface
        enum.WiresUiKey.Key:
          type: WiresBoundUserInterface
  - type: Computer
    board: ComputerIFFSyndicateCircuitboard

- type: entity
  parent: BaseComputerAiAccess
  id: ComputerPowerMonitoring
  name: "консоль для моніторингу енергоспоживання"
  description: "Він контролює рівень потужності на всій станції."
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: power_monitor
    - map: ["computerLayerKeys"]
      state: power_key
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#c9c042"
  - type: Computer
    board: PowerComputerCircuitboard
  - type: PowerMonitoringConsole
  - type: PowerMonitoringCableNetworks
  - type: NodeContainer
    examinable: true
    nodes:
      hv:
        !type:CableDeviceNode
        nodeGroupID: HVPower
  - type: ActivatableUI
    singleUser: true
    key: enum.PowerMonitoringConsoleUiKey.Key
  - type: UserInterface
    interfaces:
      enum.PowerMonitoringConsoleUiKey.Key:
        type: PowerMonitoringConsoleBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface

- type: entity
  parent: BaseComputerAiAccess
  id: ComputerMedicalRecords
  name: "консоль медичної документації"
  description: "Це може бути використано для перевірки медичної документації."
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: medcomp
    - map: ["computerLayerKeys"]
      state: med_key
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#1f8c28"
  - type: Computer
    board: MedicalRecordsComputerCircuitboard

- type: entity
  parent: BaseComputerAiAccess
  id: ComputerCriminalRecords
  name: "консольна база даних судимостей"
  description: "Це може бути використано для перевірки судимостей. Змінювати їх може лише служба безпеки."
  components:
  - type: CriminalRecordsConsole
  - type: UserInterface
    interfaces:
      enum.CriminalRecordsConsoleKey.Key:
        type: CriminalRecordsConsoleBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: ActivatableUI
    key: enum.CriminalRecordsConsoleKey.Key
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: explosive
    - map: ["computerLayerKeys"]
      state: security_key
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#1f8c28"
  - type: Computer
    board: CriminalRecordsComputerCircuitboard
  - type: AccessReader
    access: [["Security"]]
  - type: GuideHelp
    guides:
    - CriminalRecords

- type: entity
  parent: BaseComputerAiAccess
  id: ComputerPsionicsRecords
  name: "консоль регістру псионіків"
  description: "Це може бути використано для перевірки записів реєстру псионіки. Тільки епістеміки можуть їх змінювати."
  components:
  - type: PsionicsRecordsConsole
  - type: UserInterface
    interfaces:
      enum.PsionicsRecordsConsoleKey.Key:
        type: PsionicsRecordsConsoleBoundUserInterface
  - type: ActivatableUI
    key: enum.PsionicsRecordsConsoleKey.Key
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: registry
    - map: ["computerLayerKeys"]
      state: rd_key
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#1f8c28"
  - type: Computer
    board: PsionicsRecordsComputerCircuitboard
  - type: AccessReader
    access:
      - [ Chapel ]
      - [ Library ]
      - [ Mantis ]
      - [ ResearchDirector ]
  # - type: GuideHelp
    # guides: # TODO: Add a guide for Psionics Registry

- type: entity
  parent: BaseComputerAiAccess
  id: ComputerStationRecords
  name: "консоль для записів станції"
  description: "Це може бути використано для перевірки записів станції."
  components:
  - type: GeneralStationRecordConsole
  - type: UserInterface
    interfaces:
        enum.GeneralStationRecordConsoleKey.Key:
          type: GeneralStationRecordConsoleBoundUserInterface
        enum.WiresUiKey.Key:
          type: WiresBoundUserInterface
  - type: ActivatableUI
    key: enum.GeneralStationRecordConsoleKey.Key
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#1f8c28"
  - type: Computer
    board: StationRecordsComputerCircuitboard
  - type: GuideHelp
    guides:
    - Forensics

- type: entity
  parent: BaseComputerAiAccess
  id: ComputerCrewMonitoring
  name: "консоль моніторингу екіпажу"
  description: "Використовується для моніторингу активних датчиків здоров'я, вбудованих у більшість уніформи екіпажу."
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: crew
    - map: ["computerLayerKeys"]
      state: med_key
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#006400"
  - type: Computer
    board: CrewMonitoringComputerCircuitboard
  - type: ActivatableUI
    key: enum.CrewMonitoringUIKey.Key
  - type: UserInterface
    interfaces:
        enum.CrewMonitoringUIKey.Key:
          type: CrewMonitoringBoundUserInterface
        enum.WiresUiKey.Key:
          type: WiresBoundUserInterface
  - type: CrewMonitoringConsole
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: CrewMonitor
  - type: WirelessNetworkConnection
    range: 1200

- type: entity
  parent: BaseComputerAiAccess
  id: ComputerResearchAndDevelopment
  name: "консоль для досліджень і розробок"
  description: "Комп'ютер, що використовується для взаємодії з інструментами R&D."
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: rdcomp
    - map: ["computerLayerKeys"]
      state: rd_key
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: ResearchClient
  - type: ResearchConsole
  - type: ActiveRadio
    channels:
    - Science
  - type: TechnologyDatabase
  - type: ActivatableUI
    key: enum.ResearchConsoleUiKey.Key
  - type: UserInterface
    interfaces:
      enum.ResearchConsoleUiKey.Key:
        type: FancyResearchConsoleBoundUserInterface # R&D Console rework
      enum.ResearchClientUiKey.Key:
        type: ResearchClientBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: ApcPowerReceiver
    powerLoad: 1000
  - type: Computer
    board: ResearchComputerCircuitboard
  - type: AccessReader
    access: [["Research"]]
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#b53ca1"
  - type: GuideHelp
    guides:
    - Science

- type: entity
  parent: BaseComputerAiAccess
  id: ComputerAnalysisConsole
  name: "консоль аналізу"
  description: "Комп'ютер, що використовується для взаємодії з аналізатором артефактів."
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: artifact
    - map: ["computerLayerKeys"]
      state: tech_key
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: ResearchClient
  - type: AnalysisConsole
    reportEntityId: PaperArtifactAnalyzer
  - type: DeviceList
  - type: DeviceNetwork
    deviceNetId: Wired
  - type: DeviceLinkSource
    range: 5
    ports:
      - ArtifactAnalyzerSender
  - type: ActivatableUI
    key: enum.ArtifactAnalzyerUiKey.Key
  - type: UserInterface
    interfaces:
      enum.ArtifactAnalzyerUiKey.Key:
        type: AnalysisConsoleBoundUserInterface
      enum.ResearchClientUiKey.Key:
        type: ResearchClientBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: ApcPowerReceiver
    powerLoad: 1000
  - type: Computer
    board: AnalysisComputerCircuitboard
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#b53ca1"
  - type: GuideHelp
    guides:
    - Xenoarchaeology

- type: entity
  parent: BaseComputerAiAccess
  id: ComputerId
  name: "консоль ID карток"
  description: "Термінал для програмування ідентифікаційних карток співробітників Nanotrasen для доступу до частин станції."
  components:
  - type: IdCardConsole
    privilegedIdSlot:
      name: id-card-console-privileged-id
      ejectSound: /Audio/Machines/id_swipe.ogg
      insertSound: /Audio/Weapons/Guns/MagIn/batrifle_magin.ogg
      ejectOnBreak: true
      swap: false
      whitelist:
        components:
        - IdCard
    targetIdSlot:
      name: id-card-console-target-id
      ejectSound: /Audio/Machines/id_swipe.ogg
      insertSound: /Audio/Weapons/Guns/MagIn/batrifle_magin.ogg
      ejectOnBreak: true
      swap: false
      whitelist:
        components:
        - IdCard
  - type: ActivatableUI
    key: enum.IdCardConsoleUiKey.Key
  - type: UserInterface
    interfaces:
      enum.IdCardConsoleUiKey.Key:
        type: IdCardConsoleBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: CrewManifestViewer
    ownerKey: enum.IdCardConsoleUiKey.Key
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: id
    - map: ["computerLayerKeys"]
      state: id_key
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: Computer
    board: IDComputerCircuitboard
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#3c5eb5"
  - type: Tag
    tags:
    - EmagImmune
  - type: ItemSlots
  - type: ContainerContainer
    containers:
      board: !type:Container
      IdCardConsole-privilegedId: !type:ContainerSlot
      IdCardConsole-targetId: !type:ContainerSlot

- type: entity
  parent: BaseComputerAiAccess
  id: computerBodyScanner
  name: "консоль сканера тіла"
  description: "Сканер тіла."
  components:
  - type: ApcPowerReceiver
    powerLoad: 500
  - type: Computer
    board: BodyScannerComputerCircuitboard
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#1f8c28"

- type: entity
  parent: BaseComputerAiAccess
  id: ComputerComms
  name: "консоль комунікації"
  description: "Комп'ютер, за допомогою якого з клавіатури робилися загальностанційні оголошення, встановлювався відповідний рівень тривоги і викликався аварійний шаттл."
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: comm
    - map: ["computerLayerKeys"]
      state: generic_keys
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: AccessReader
    access: [[ "Command" ]]
  - type: CommunicationsConsole
    title: comms-console-announcement-title-station
  - type: DeviceNetwork
    transmitFrequencyId: ShuttleTimer
  - type: ActivatableUI
    key: enum.CommunicationsConsoleUiKey.Key
  - type: UserInterface
    interfaces:
      enum.CommunicationsConsoleUiKey.Key:
        type: CommunicationsConsoleBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: Computer
    board: CommsComputerCircuitboard
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#3c5eb5"
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: StrongMetallic

- type: entity
  parent: ComputerComms
  id: SyndicateComputerComms
  name: "консоль зв'язку синдикату"
  description: "Комп'ютер, здатний віддалено зламати комунікаційні системи станції. Використання його для оголошення сповістить станцію про вашу присутність."
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: comm_syndie
    - map: ["computerLayerKeys"]
      state: syndie_key
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: AccessReader
    access: [[ "NuclearOperative" ]]
  - type: CommunicationsConsole
    title: comms-console-announcement-title-nukie
    color: "#ff0000"
    canShuttle: false
    global: true #announce to everyone they're about to fuck shit up
    sound: /Audio/Announcements/war.ogg
  - type: Computer
    board: SyndicateCommsComputerCircuitboard
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#f71713"

- type: entity
  parent: BaseComputerAiAccess
  id: ComputerSolarControl
  name: "консоль управління сонячною енергією"
  description: "Контролер для масивів сонячних панелей."
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: solar_screen
    - map: ["computerLayerKeys"]
      state: generic_keys
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: SolarControlConsole
  - type: ActivatableUI
    key: enum.SolarControlConsoleUiKey.Key
  - type: UserInterface
    interfaces:
      enum.SolarControlConsoleUiKey.Key:
        type: SolarControlConsoleBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: Computer
    board: SolarControlComputerCircuitboard
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#e6e227"
  - type: GuideHelp
    guides:
    - Power

- type: entity
  parent: BaseComputerAiAccess
  id: ComputerRadar
  name: "консоль сканування тіл"
  description: "Комп'ютер для виявлення тіл, що знаходяться поблизу, з відображенням їх за положенням і масою."
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: solar_screen
    - map: ["computerLayerKeys"]
      state: generic_keys
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: RadarConsole
  - type: ActivatableUI
    key: enum.RadarConsoleUiKey.Key
  - type: UserInterface
    interfaces:
        enum.RadarConsoleUiKey.Key:
          type: RadarConsoleBoundUserInterface
        enum.WiresUiKey.Key:
          type: WiresBoundUserInterface
  - type: Computer
    board: RadarConsoleCircuitboard
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#e6e227"

- type: entity
  id: ComputerCargoShuttle
  parent: BaseComputerAiAccess
  name: "консоль вантажного шатлу ЦК"
  description: "Раніше через нього можна було замовити вантажний шатл з ЦК."
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: supply
    - map: ["computerLayerKeys"]
      state: tech_key
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: CargoShuttleConsole
  - type: ActivatableUI
    key: enum.CargoConsoleUiKey.Shuttle
  - type: UserInterface
    interfaces:
      enum.CargoConsoleUiKey.Shuttle:
        type: CargoShuttleConsoleBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: Computer
    board: CargoShuttleComputerCircuitboard
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#b89f25"
  - type: AccessReader
    access: [["Cargo"]]
  - type: GuideHelp
    guides:
    - Cargo

- type: entity
  id: ComputerCargoOrders
  parent: BaseComputerAiAccess
  name: "консоль запитів на вантаж"
  description: "Використовується для замовлення матеріалів та затвердження запитів."
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: request
    - map: ["computerLayerKeys"]
      state: tech_key
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: CargoOrderConsole
  - type: BankClient
  - type: ActiveRadio
    channels:
    - Supply
  - type: ActivatableUI
    key: enum.CargoConsoleUiKey.Orders
  - type: UserInterface
    interfaces:
      enum.CargoConsoleUiKey.Orders:
        type: CargoOrderConsoleBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: Computer
    board: CargoRequestComputerCircuitboard
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#b89f25"
  - type: AccessReader
    access: [["Cargo"]]
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: BasicDevice
  - type: WirelessNetworkConnection
    range: 200
  - type: DeviceLinkSource
    range: 200
    ports:
      - OrderSender
  - type: GuideHelp
    guides:
    - Cargo

- type: entity
  id: ComputerCargoBounty
  parent: BaseComputerAiAccess
  name: "консоль завдань на вантаж"
  description: "Використовується для керування поточними активними завданнями з ЦК."
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: bounty
    - map: ["computerLayerKeys"]
      state: tech_key
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: CargoBountyConsole
  - type: ActivatableUI
    key: enum.CargoConsoleUiKey.Bounty
  - type: UserInterface
    interfaces:
      enum.CargoConsoleUiKey.Bounty:
        type: CargoBountyConsoleBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: Computer
    board: CargoBountyComputerCircuitboard
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#b89f25"
  - type: AccessReader
    access: [["Quartermaster"]]
  - type: GuideHelp
    guides:
    - CargoBounties

- type: entity
  parent: BaseComputerAiAccess
  id: ComputerCloningConsole
  name: "консоль консолі клонування"
  description: "Центральний елемент системи клонування, найбільше досягнення медицини. Він має багато портів і дротів."
  components:
  - type: CloningConsole
  - type: DeviceList
  - type: DeviceNetwork
    deviceNetId: Wired
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: dna
    - map: ["computerLayerKeys"]
      state: generic_keys
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: ApcPowerReceiver
    powerLoad: 3100 #We want this to fail first so I transferred most of the scanner and pod's power here. (3500 in total)
  - type: Computer
    board: CloningConsoleComputerCircuitboard
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#1f8c28"
  - type: DeviceLinkSource
    range: 4
    ports:
      - MedicalScannerSender
      - CloningPodSender
  - type: ActivatableUI
    key: enum.CloningConsoleUiKey.Key
  - type: UserInterface
    interfaces:
        enum.CloningConsoleUiKey.Key:
          type: CloningConsoleBoundUserInterface
        enum.WiresUiKey.Key:
          type: WiresBoundUserInterface
  - type: Speech
    speechVerb: Robotic
    speechSounds: Pai
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: StrongMetallic
  - type: GuideHelp
    guides:
    - Cloning

- type: entity
  id: ComputerSalvageExpedition
  parent: BaseComputerAiAccess
  name: "консоль утилізаторських експедицій"
  description: "Використовуйте для утилізаторських експедицій, якщо ви достатньо витривалі."
  components:
    - type: Sprite
      layers:
        - map: ["computerLayerBody"]
          state: computer
        - map: ["computerLayerKeyboard"]
          state: generic_keyboard
        - map: [ "computerLayerScreen" ]
          state: mining
        - map: ["computerLayerKeys"]
          state: tech_key
        - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
          state: generic_panel_open
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.ComputerVisuals.Powered:
          computerLayerScreen:
            True: { visible: true, shader: unshaded }
            False: { visible: false }
          computerLayerKeys:
            True: { visible: true, shader: unshaded }
            False: { visible: true }
        enum.WiresVisuals.MaintenancePanelState:
          enum.WiresVisualLayers.MaintenancePanel:
            True: { visible: false }
            False: { visible: true }
    - type: SalvageExpeditionConsole
    - type: ActivatableUI
      key: enum.SalvageConsoleUiKey.Expedition
    - type: ActivatableUIRequiresPower
    - type: UserInterface
      interfaces:
        enum.SalvageConsoleUiKey.Expedition:
          type: SalvageExpeditionConsoleBoundUserInterface
        enum.WiresUiKey.Key:
          type: WiresBoundUserInterface
    - type: Computer
      board: SalvageExpeditionsComputerCircuitboard
    - type: PointLight
      radius: 1.5
      energy: 1.6
      color: "#b89f25"
    - type: AccessReader
      access: [["Salvage"]]
    - type: StealTarget
      stealGroup: SalvageExpeditionsComputerCircuitboard

- type: entity
  parent: BaseComputer
  id: ComputerSurveillanceCameraMonitor
  name: "монітор камер спостереження"
  description: "Монітор камери спостереження. Ти стежиш за ними. Можливо."
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: cameras
    - map: ["computerLayerKeys"]
      state: tech_key
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: Computer
    board: SurveillanceCameraMonitorCircuitboard
  - type: DeviceNetwork
    deviceNetId: Wired
    receiveFrequencyId: SurveillanceCamera
    transmitFrequencyId: SurveillanceCamera
  - type: WiredNetworkConnection
  - type: DeviceNetworkRequiresPower
  - type: SurveillanceCameraMonitor
  - type: ActivatableUI
    key: enum.SurveillanceCameraMonitorUiKey.Key
  - type: ActivatableUIRequiresVision
  - type: Transform
    anchored: true
  - type: UserInterface
    interfaces:
        enum.SurveillanceCameraMonitorUiKey.Key:
          type: SurveillanceCameraMonitorBoundUserInterface
        enum.WiresUiKey.Key:
          type: WiresBoundUserInterface

- type: entity
  parent: BaseComputer
  id: ComputerSurveillanceWirelessCameraMonitor
  name: "монітор бездротових камер спостереження"
  description: "Монітор бездротових камер спостереження. Ти стежиш за ними. Можливо."
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: cameras
    - map: ["computerLayerKeys"]
      state: tech_key
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: Computer
    board: SurveillanceWirelessCameraMonitorCircuitboard
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: SurveillanceCamera
    transmitFrequencyId: SurveillanceCamera
  - type: WirelessNetworkConnection
    range: 200
  - type: DeviceNetworkRequiresPower
  - type: Speech
    speechVerb: Robotic
  - type: SurveillanceCameraSpeaker
  - type: SurveillanceCameraMonitor
  - type: ActivatableUI
    key: enum.SurveillanceCameraMonitorUiKey.Key
  - type: ActivatableUIRequiresVision
  - type: UserInterface
    interfaces:
        enum.SurveillanceCameraMonitorUiKey.Key:
          type: SurveillanceCameraMonitorBoundUserInterface
        enum.WiresUiKey.Key:
          type: WiresBoundUserInterface

- type: entity
  id: ComputerPalletConsole
  parent: BaseComputerAiAccess
  name: "консоль продажу вантажів"
  description: "Використовується для продажу товарів, завантажених на вантажні піддони"
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: request
    - map: ["computerLayerKeys"]
      state: tech_key
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: Anchorable
    flags:
    - Anchorable
  - type: CargoPalletConsole
  - type: ActivatableUI
    key: enum.CargoPalletConsoleUiKey.Sale
  - type: UserInterface
    interfaces:
      enum.CargoPalletConsoleUiKey.Sale:
        type: CargoPalletConsoleBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: Computer
    board: CargoRequestComputerCircuitboard
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#b89f25"
  - type: GuideHelp
    guides:
    - Cargo

- type: entity
  parent: BaseComputerAiAccess
  id: ComputerMassMedia
  name: "консоль менеджменту новин"
  description: "Напишіть своє послання світу!"
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: service
    - map: ["computerLayerKeys"]
      state: service_keys
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: Computer
    board: ComputerMassMediaCircuitboard
  - type: DeviceNetworkRequiresPower
  - type: NewsWriter
  - type: AccessReader
    access: [[ "Command" ]]
  - type: ActivatableUI
    key: enum.NewsWriterUiKey.Key
  - type: ActivatableUIRequiresVision
  - type: Transform
    anchored: true
  - type: UserInterface
    interfaces:
        enum.NewsWriterUiKey.Key:
          type: NewsWriterBoundUserInterface
        enum.WiresUiKey.Key:
          type: WiresBoundUserInterface

- type: entity
  parent: BaseComputer
  id: ComputerSensorMonitoring
  name: "консоль моніторингу датчиків"
  description: "Гнучка консоль для моніторингу всіх видів датчиків."
  # Putting this as "DO NOT MAP" until the performance issues are fixed.
  # And it's more fleshed out.
  suffix: TESTING, DO NOT MAP
  components:
    - type: Sprite
      layers:
        - map: ["computerLayerBody"]
          state: computer
        - map: ["computerLayerKeyboard"]
          state: generic_keyboard
        - map: ["computerLayerScreen"]
          state: sensors
        - map: ["computerLayerKeys"]
          state: atmos_key
        - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
          state: generic_panel_open
    - type: PointLight
      radius: 1.5
      energy: 1.6
      color: "#43ccb5"
    - type: Computer
      board: SensorConsoleCircuitboard
    - type: SensorMonitoringConsole
    - type: ActivatableUI
      key: enum.SensorMonitoringConsoleUiKey.Key
    - type: UserInterface
      interfaces:
        enum.SensorMonitoringConsoleUiKey.Key:
          type: SensorMonitoringConsoleBoundUserInterface
        enum.WiresUiKey.Key:
          type: WiresBoundUserInterface
    - type: DeviceNetwork
      deviceNetId: AtmosDevices
      receiveFrequencyId: AtmosMonitor
      transmitFrequencyId: AtmosMonitor
      prefix: device-address-prefix-sensor-monitor
      sendBroadcastAttemptEvent: true
      examinableAddress: true
    - type: WiredNetworkConnection
    - type: DeviceList
    - type: AtmosDevice

- type: entity
  parent: BaseComputerAiAccess
  id: ComputerRoboticsControl
  name: "консоль управління робототехнікою"
  description: "Використовується для дистанційного моніторингу, виведення з ладу та знищення кіборгів станції."
  components:
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: robot
    - map: ["computerLayerKeys"]
      state: rd_key
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: RoboticsConsole
  - type: ActiveRadio
    channels:
    - Science
  - type: ActivatableUI
    key: enum.RoboticsConsoleUiKey.Key
  - type: UserInterface
    interfaces:
      enum.RoboticsConsoleUiKey.Key:
        type: RoboticsConsoleBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: ApcPowerReceiver
    powerLoad: 1000
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: RoboticsConsole
    transmitFrequencyId: CyborgControl
  - type: Computer
    board: RoboticsConsoleCircuitboard
  - type: AccessReader # only used for dangerous things
    access: [["ResearchDirector"]]
  - type: Lock
    unlockOnClick: false

- type: entity
  id: StationAiUploadComputer
  parent: BaseComputer
  name: "Консоль завантаження ШІ"
  description: "Використовується для оновлення законів станції ШІ."
  components:
  - type: Computer
    board: StationAiUploadCircuitboard
  - type: Sprite
    layers:
    - map: [ "computerLayerBody" ]
      state: computer
    - map: [ "computerLayerKeyboard" ]
      state: generic_keyboard
    - map: [ "computerLayerScreen" ]
      state: aiupload
    - map: [ "computerLayerKeys" ]
      state: generic_keys
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: ApcPowerReceiver
    powerLoad: 1000
  - type: AccessReader
    access: [ [ "ResearchDirector" ] ]
  - type: Lock
    unlockOnClick: false
  - type: SiliconLawUpdater
    components:
    - type: StationAiHeld
  - type: ItemSlotsLock
    slots:
    - circuit_holder
  - type: ItemSlotRequiresPower
  - type: ItemSlots
    slots:
      circuit_holder:
        name: circuit-holder
        insertSuccessPopup: silicon-laws-updated
        whitelist:
          components:
          - SiliconLawProvider
          - Item
  - type: ContainerContainer
    containers:
      circuit_holder: !type:ContainerSlot
      board: !type:Container
