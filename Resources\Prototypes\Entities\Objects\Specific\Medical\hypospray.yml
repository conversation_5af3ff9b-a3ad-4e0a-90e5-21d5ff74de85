- type: entity
  name: "гіпоспрей"
  parent: BaseItem
  description: "Стерильний ін'єктор для швидкого введення ліків пацієнтам."
  id: Hypospray
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/hypospray.rsi
    state: hypo
  - type: Item
    sprite: Objects/Specific/Medical/hypospray.rsi
  - type: SolutionContainerManager
    solutions:
      hypospray:
        maxVol: 30
  - type: RefillableSolution
    solution: hypospray
  - type: ExaminableSolution
    solution: hypospray
  - type: Hypospray
    onlyAffectsMobs: false
  - type: UseDelay
    delay: 0.5
  - type: StaticPrice
    price: 750
  - type: Tag
    tags:
    - HighRiskItem
  - type: StealTarget
    stealGroup: Hypospray

- type: entity
  name: "гіпоспрей gorlex"
  parent: BaseItem
  description: "Використовуючи перероблений дизайн від NT, компанія Cybersun виготовила їх в обмеженій кількості для оперативників Gorlex Marauder."
  id: SyndiHypo
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/syndihypo.rsi
    state: hypo
  - type: Item
    sprite: Objects/Specific/Medical/syndihypo.rsi
  - type: SolutionContainerManager
    solutions:
      hypospray:
        maxVol: 20
  - type: RefillableSolution
    solution: hypospray
  - type: ExaminableSolution
    solution: hypospray
  - type: Hypospray
    onlyAffectsMobs: false
  - type: UseDelay
    delay: 0.5

- type: entity
  name: "borgypo"
  parent: BaseItem
  description: "Стерильний ін'єктор для швидкого введення ліків пацієнтам. Дешевша та більш спеціалізована версія для медичних боргів."
  id: BorgHypo
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/hypospray.rsi
    state: borghypo
  - type: Item
    sprite: Objects/Specific/Medical/hypospray.rsi
  - type: SolutionContainerManager
    solutions:
      hypospray:
        maxVol: 10
  - type: RefillableSolution
    solution: hypospray
  - type: ExaminableSolution
    solution: hypospray
  - type: Hypospray
    onlyAffectsMobs: false
  - type: UseDelay
    delay: 0.5

- type: entity
  name: "експериментальний гіпоспрей"
  suffix: Admeme
  parent: SyndiHypo
  description: "Максимальне застосування технології bluespace та швидкого введення хімікатів."
  id: AdminHypo
  components:
    - type: SolutionContainerManager
      solutions:
        hypospray:
          maxVol: 3000
    - type: UseDelay
      delay: 0.0

- type: entity
  name: "хімічний медипен"
  parent: BaseItem
  description: "Стерильний ін'єктор для швидкого введення ліків пацієнтам. Його не можна перезаправляти."
  id: ChemicalMedipen
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/medipen.rsi
    layers:
    - state: firstaid
      map: ["enum.SolutionContainerLayers.Fill"]
  - type: Item
    sprite: Objects/Specific/Medical/medipen.rsi
    size: Tiny
  - type: SolutionContainerManager
    solutions:
      pen:
        maxVol: 15
  - type: ExaminableSolution
    solution: pen
  - type: Hypospray
    solutionName: pen
    transferAmount: 15
    onlyAffectsMobs: false
    injectOnly: true
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 1
    changeColor: false
    emptySpriteName: firstaid_empty
  - type: Tag
    tags:
    - Trash
    - Medipen
  - type: PhysicalComposition
    materialComposition:
      Plastic: 50
  - type: SpaceGarbage
  - type: StaticPrice
    price: 75 # These are limited supply items.
  - type: TrashOnSolutionEmpty
    solution: pen

- type: entity
  name: "екстрений медпен першої допомоги"
  parent: ChemicalMedipen
  id: EmergencyMedipen
  description: "Швидкий і безпечний спосіб стабілізації пацієнтів у критичному стані для персоналу без поглиблених медичних знань. Будьте обережні, оскільки легко викликати передозування адреналіном і транексамовою кислотою."
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/medipen.rsi
    layers:
    - state: medipen
      map: ["enum.SolutionContainerLayers.Fill"]
  - type: SolutionContainerVisuals
    maxFillLevels: 1
    changeColor: false
    emptySpriteName: medipen_empty
  - type: SolutionContainerManager
    solutions:
      pen:
        maxVol: 15
        reagents:
        - ReagentId: Epinephrine
          Quantity: 12
        - ReagentId: TranexamicAcid
          Quantity: 3
  - type: Tag
    tags: []

- type: entity
  name: "автоінжектор від отрути"
  parent: ChemicalMedipen
  id: AntiPoisonMedipen
  description: "Швидка доза антиотрути. Містить ультраваскулін та адреналін."
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/medipen.rsi
    layers:
    - state: penacid
      map: ["enum.SolutionContainerLayers.Fill"]
  - type: SolutionContainerVisuals
    maxFillLevels: 1
    changeColor: false
    emptySpriteName: penacid_empty
  - type: SolutionContainerManager
    solutions:
      pen:
        maxVol: 15
        reagents:
        - ReagentId: Ultravasculine
          Quantity: 10
        - ReagentId: Epinephrine
          Quantity: 5
  - type: Tag
    tags: []

- type: entity
  name: "автоінжектор від пошкоджень"
  parent: ChemicalMedipen
  id: BruteAutoInjector
  description: "Швидка доза бікаридину та транексамової кислоти, призначена для бойового застосування"
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/medipen.rsi
    layers:
    - state: bicpen
      map: ["enum.SolutionContainerLayers.Fill"]
  - type: SolutionContainerVisuals
    maxFillLevels: 1
    changeColor: false
    emptySpriteName: bicpen_empty
  - type: Hypospray
    solutionName: pen
    transferAmount: 20
    onlyAffectsMobs: false
    injectOnly: true
  - type: SolutionContainerManager
    solutions:
      pen:
        maxVol: 20
        reagents:
        - ReagentId: Bicaridine
          Quantity: 15
        - ReagentId: TranexamicAcid
          Quantity: 5
  - type: Tag
    tags: []

- type: entity
  name: "автоінжектор від опіків"
  parent: ChemicalMedipen
  id: BurnAutoInjector
  description: "Швидка доза дерматину та лепоразину, призначена для бойового застосування"
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/medipen.rsi
    layers:
    - state: dermpen
      map: ["enum.SolutionContainerLayers.Fill"]
  - type: SolutionContainerVisuals
    maxFillLevels: 1
    changeColor: false
    emptySpriteName: dermpen_empty
  - type: Hypospray
    solutionName: pen
    transferAmount: 20
    onlyAffectsMobs: false
    injectOnly: true
  - type: SolutionContainerManager
    solutions:
      pen:
        maxVol: 20
        reagents:
        - ReagentId: Dermaline
          Quantity: 10
        - ReagentId: Leporazine
          Quantity: 10
  - type: Tag
    tags: []

- type: entity
  name: "автоінжектор від радіації"
  parent: ChemicalMedipen
  id: RadAutoInjector
  description: "Швидка антирадіаційна доза. Містить аритразин і бікаридин."
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/medipen.rsi
    layers:
    - state: arithpen
      map: ["enum.SolutionContainerLayers.Fill"]
  - type: SolutionContainerVisuals
    maxFillLevels: 1
    changeColor: false
    emptySpriteName: arithpen_empty
  - type: Hypospray
    solutionName: pen
    transferAmount: 20
    onlyAffectsMobs: false
    injectOnly: true
  - type: SolutionContainerManager
    solutions:
      pen:
        maxVol: 20
        reagents:
        - ReagentId: Arithrazine
          Quantity: 15
        - ReagentId: Bicaridine
          Quantity: 5
  - type: Tag
    tags: []

- type: entity
  name: "автоінжектор для проколів"
  parent: ChemicalMedipen
  id: PunctAutoInjector
  description: "Швидка доза пунктурази та транексамової кислоти, призначена для бойового застосування."
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/medipen.rsi
    layers:
    - state: punctpen
      map: ["enum.SolutionContainerLayers.Fill"]
  - type: SolutionContainerVisuals
    maxFillLevels: 1
    changeColor: false
    emptySpriteName: punctpen_empty
  - type: Hypospray
    solutionName: pen
    transferAmount: 15
    onlyAffectsMobs: false
    injectOnly: true
  - type: SolutionContainerManager
    solutions:
      pen:
        maxVol: 15
        reagents:
        - ReagentId: Puncturase
          Quantity: 10
        - ReagentId: TranexamicAcid
          Quantity: 5
  - type: Tag
    tags: []

- type: entity
  name: "автоінжектор піразину"
  parent: ChemicalMedipen
  id: PyraAutoInjector
  description: "Швидка доза піразину та дерматину, призначена для бойового застосування."
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/medipen.rsi
    layers:
    - state: pyrapen
      map: ["enum.SolutionContainerLayers.Fill"]
  - type: SolutionContainerVisuals
    maxFillLevels: 1
    changeColor: false
    emptySpriteName: pyrapen_empty
  - type: Hypospray
    solutionName: pen
    transferAmount: 20
    onlyAffectsMobs: false
    injectOnly: true
  - type: SolutionContainerManager
    solutions:
      pen:
        maxVol: 20
        reagents:
        - ReagentId: Pyrazine
          Quantity: 10
        - ReagentId: Dermaline
          Quantity: 10
  - type: Tag
    tags: []

- type: entity
  name: "автоінжектор при кисневій недостатності"
  parent: ChemicalMedipen
  id: AirlossAutoInjector
  description: "Швидка доза фізіологічного розчину і дексаліну плюс, призначена для того, щоб швидко підняти людину."
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/medipen.rsi
    layers:
    - state: dexpen
      map: ["enum.SolutionContainerLayers.Fill"]
  - type: SolutionContainerVisuals
    maxFillLevels: 1
    changeColor: false
    emptySpriteName: dexpen_empty
  - type: Hypospray
    solutionName: pen
    transferAmount: 40
    onlyAffectsMobs: false
    injectOnly: true
  - type: SolutionContainerManager
    solutions:
      pen:
        maxVol: 40
        reagents:
        - ReagentId: Saline
          Quantity: 20
        - ReagentId: DexalinPlus
          Quantity: 20
  - type: Tag
    tags: []

- type: entity
  name: "космічний медпен"
  parent: ChemicalMedipen
  id: SpaceMedipen
  description: "Містить суміш хімічних речовин, які захищають вас від смертоносного впливу космосу."
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/medipen.rsi
    layers:
    - state: hypovolemic
      map: [ "enum.SolutionContainerLayers.Fill" ]
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 1
    changeColor: false
    emptySpriteName: hypovolemic_empty
  - type: Hypospray
    solutionName: pen
    transferAmount: 30
    onlyAffectsMobs: false
    injectOnly: true
  - type: SolutionContainerManager
    solutions:
      pen:
        maxVol: 30
        reagents:
          - ReagentId: Leporazine
            Quantity: 10
          - ReagentId: Barozine
            Quantity: 20
  - type: Tag
    tags: []

- type: entity
  name: "ін'єктор для стимуляторів"
  parent: ChemicalMedipen
  id: Stimpack
  description: "Містить достатню кількість стимуляторів для того, щоб ви відчули ефект хімічної речовини протягом 30 секунд. Використовуйте його, коли ви впевнені, що готові кинути."
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/medipen.rsi
    layers:
    - state: stimpen
      map: ["enum.SolutionContainerLayers.Fill"]
  - type: SolutionContainerManager
    solutions:
      pen:
        maxVol: 30
        reagents:
        - ReagentId: Stimulants
          Quantity: 30
  - type: SolutionContainerVisuals
    maxFillLevels: 1
    changeColor: false
    emptySpriteName: stimpen_empty
  - type: Hypospray
    solutionName: pen
    transferAmount: 30
    onlyAffectsMobs: false
    injectOnly: true
  - type: StaticPrice
    price: 500
  - type: Tag
    tags: []

- type: entity
  name: "мікроін'єктор стимулятора"
  parent: ChemicalMedipen
  id: StimpackMini
  description: "Мікроін'єктор стимуляторів, який дає вам близько п'ятнадцяти секунд дії хімічної речовини."
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/medipen.rsi
    layers:
    - state: microstimpen
      map: ["enum.SolutionContainerLayers.Fill"]
  - type: SolutionContainerManager
    solutions:
      pen:
        maxVol: 15
        reagents:
        - ReagentId: Stimulants
          Quantity: 15
  - type: SolutionContainerVisuals
    maxFillLevels: 1
    changeColor: false
    emptySpriteName: microstimpen_empty
  - type: StaticPrice
    price: 100
  - type: Tag
    tags: []

- type: entity
  name: "бойовий медипен"
  parent: ChemicalMedipen
  id: CombatMedipen
  description: "Одноразовий медипен, що містить хімічні речовини, які регенерують більшість видів пошкоджень."
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/medipen.rsi
    layers:
    - state: morphen
      map: ["enum.SolutionContainerLayers.Fill"]
  - type: SolutionContainerVisuals
    maxFillLevels: 1
    changeColor: false
    emptySpriteName: morphen_empty
  - type: SolutionContainerManager
    solutions:
      pen:
        maxVol: 30
        reagents:
        - ReagentId: Omnizine
          Quantity: 25
        - ReagentId: TranexamicAcid
          Quantity: 5
  - type: Hypospray
    solutionName: pen
    transferAmount: 30
    onlyAffectsMobs: false
    injectOnly: true
  - type: StaticPrice
    price: 500
  - type: Tag
    tags: []

- type: entity
  name: "ручка"
  suffix: Hypopen
  parent: Pen # It is just like normal pen, isn't it?
  description: "Ручка з темним чорнилом."
  id: Hypopen
  components:
  - type: SolutionContainerManager
    solutions:
      hypospray:
        maxVol: 10
  - type: RefillableSolution
    solution: hypospray
  - type: ExaminableSolution
    solution: hypospray
    heldOnly: true # Allow examination only when held in hand.
  - type: Hypospray
    onlyAffectsMobs: false
  - type: UseDelay
    delay: 0.5
  - type: StaticPrice # A new shitcurity meta
    price: 75

- type: entity
  parent: BaseItem
  id: HypopenBox
  name: "гіпо-відкрита коробка"
  description: "Невеличка коробочка з гіпопеном. Упаковка розпадається при відкритті, не залишаючи після себе жодних доказів."
  components:
  - type: Item
    size: Tiny
  - type: Sprite
    sprite: Objects/Storage/penbox.rsi
    state: hypopen
  - type: SpawnItemsOnUse
    items:
    - id: Hypopen
    sound:
      path: /Audio/Effects/unwrap.ogg
