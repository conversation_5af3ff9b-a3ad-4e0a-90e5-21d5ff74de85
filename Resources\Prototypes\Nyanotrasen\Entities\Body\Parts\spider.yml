- type: entity
  id: PartSpider
  parent: BaseItem
  name: "частина тіла павука"
  abstract: true
  components:
  - type: Damageable
    damageContainer: OrganicPart
  - type: BodyPart
  - type: ContainerContainer
    containers:
      bodypart: !type:Container
        ents: []
  - type: StaticPrice
    price: 100
  - type: Tag
    tags:
      - Trash
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 3
      - ReagentId: DemonsBlood
        Quantity: 10

- type: entity
  id: ThoraxSpider
  name: "грудна клітка павука" #for arachne, actual spiders should get a cephalothorax that combines with head.
  parent: PartSpider
  components:
  - type: Sprite
    sprite: Mobs/Species/Moth/parts.rsi # placeholder sprite
    state: "torso_m"
  - type: Icon
    sprite: Mobs/Species/Moth/parts.rsi
    state: "torso_m"
  - type: BodyPart #"Other" type
    slotId: thorax
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 10
      - ReagentId: DemonsBlood
        Quantity: 20

- type: entity
  id: RightLegSpider
  name: "права павукова нога"
  parent: PartSpider
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/meat.rsi # placeholder sprite
    state: spiderleg
  - type: Icon
    sprite: Objects/Consumable/Food/meat.rsi
    state: spiderleg
  - type: BodyPart
    partType: Leg
    symmetry: Right
  - type: MovementBodyPart #should actual spiders get a seperate part from arachne?
    walkSpeed : 2.7
    sprintSpeed : 5

- type: entity
  id: LeftLegSpider
  name: "ліва нога павука"
  parent: PartSpider
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/meat.rsi # placeholder sprite
    state: spiderleg
  - type: Icon
    sprite: Objects/Consumable/Food/meat.rsi
    state: spiderleg
  - type: BodyPart
    partType: Leg
    symmetry: Left
  - type: MovementBodyPart #should actual spiders get a seperate part from arachne?
    walkSpeed : 2.7
    sprintSpeed : 5
