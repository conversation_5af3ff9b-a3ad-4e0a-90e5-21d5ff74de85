using Content.Client.Atmos.EntitySystems;
using Content.Client.Stylesheets;
using Content.Shared._EE.Supermatter.Components;
using Content.Shared._EE.Supermatter.Monitor;
using Content.Shared.Atmos;
using Content.Shared.Atmos.Prototypes;
using Robust.Client.AutoGenerated;
using Robust.Client.Graphics;
using Robust.Client.ResourceManagement;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using System.Linq;

namespace Content.Client._EE.Supermatter.Consoles;

[GenerateTypedNameReferences]
public sealed partial class SupermatterEntryContainer : BoxContainer
{
    public NetEntity NetEntity;

    private readonly IEntityManager _entManager;
    private readonly IResourceCache _cache;

    private Dictionary<SupermatterStatusType, string> _statusStrings = new()
    {
        [SupermatterStatusType.Inactive] = "supermatter-console-window-inactive-status",
        [SupermatterStatusType.Normal] = "supermatter-console-window-normal-status",
        [SupermatterStatusType.Caution] = "supermatter-console-window-caution-status",
        [SupermatterStatusType.Warning] = "supermatter-console-window-warning-status",
        [SupermatterStatusType.Danger] = "supermatter-console-window-danger-status",
        [SupermatterStatusType.Emergency] = "supermatter-console-window-emergency-status",
        [SupermatterStatusType.Delaminating] = "supermatter-console-window-delaminating-status"
    };

    public SupermatterEntryContainer(NetEntity uid)
    {
        RobustXamlLoader.Load(this);

        _entManager = IoCManager.Resolve<IEntityManager>();
        _cache = IoCManager.Resolve<IResourceCache>();

        NetEntity = uid;

        // Load fonts
        var headerFont = new VectorFont(_cache.GetResource<FontResource>("/Fonts/NotoSans/NotoSans-Bold.ttf"), 11);
        var normalFont = new VectorFont(_cache.GetResource<FontResource>("/Fonts/NotoSansDisplay/NotoSansDisplay-Regular.ttf"), 11);
        var monoFont = new VectorFont(_cache.GetResource<FontResource>("/EngineFonts/NotoSans/NotoSansMono-Regular.ttf"), 10);

        // Set fonts
        SupermatterNameLabel.FontOverride = headerFont;

        SupermatterStatusLabel.FontOverride = normalFont;

        IntegrityLabel.FontOverride = normalFont;
        PowerLabel.FontOverride = normalFont;
        RadiationLabel.FontOverride = normalFont;
        MolesLabel.FontOverride = normalFont;
        TemperatureLabel.FontOverride = normalFont;
        TemperatureLimitLabel.FontOverride = normalFont;
        WasteLabel.FontOverride = normalFont;
        AbsorptionLabel.FontOverride = normalFont;

        IntegrityBarLabel.FontOverride = monoFont;
        PowerBarLabel.FontOverride = monoFont;
        RadiationBarLabel.FontOverride = monoFont;
        MolesBarLabel.FontOverride = monoFont;
        TemperatureBarLabel.FontOverride = monoFont;
        TemperatureLimitBarLabel.FontOverride = monoFont;
        WasteBarLabel.FontOverride = monoFont;
        AbsorptionBarLabel.FontOverride = monoFont;
    }

    public void UpdateEntry(SupermatterConsoleEntry entry, bool isFocus, SupermatterFocusData? focusData = null)
    {
        NetEntity = entry.NetEntity;

        // Load fonts
        var normalFont = new VectorFont(_cache.GetResource<FontResource>("/Fonts/NotoSansDisplay/NotoSansDisplay-Regular.ttf"), 11);

        // Update supermatter name
        SupermatterNameLabel.Text = Loc.GetString("supermatter-console-window-label-sm", ("name", entry.EntityName));

        // Update supermatter status
        SupermatterStatusLabel.Text = Loc.GetString(GetStatusLabel(entry.EntityStatus));
        SupermatterStatusLabel.FontColorOverride = GetStatusColor(entry.EntityStatus);

        // Focus updates
        FocusContainer.Visible = isFocus;

        if (isFocus)
        {
            if (focusData != null)
            {
                var red = StyleNano.DangerousRedFore;
                var orange = StyleNano.ConcerningOrangeFore;
                var green = StyleNano.GoodGreenFore;
                var turqoise = Color.FromHex("#009893");

                // TODO: please don't define this dictionary every update you animal
                var engineDictionary = new Dictionary<string, (Label label, ProgressBar bar, PanelContainer border, float value, float leftSize, float rightSize, Color leftColor, Color middleColor, Color rightColor)>()
                {
                    { "integrity",   (IntegrityBarLabel,   IntegrityBar,   IntegrityBarBorder,   focusData.Value.Integrity,       0.9f, 0.1f, red,      orange, green) },
                    { "power",       (PowerBarLabel,       PowerBar,       PowerBarBorder,       focusData.Value.Power,           0.9f, 0.1f, green,    orange, red  ) },
                    { "radiation",   (RadiationBarLabel,   RadiationBar,   RadiationBarBorder,   focusData.Value.Radiation,       0.1f, 0.9f, green,    orange, red  ) },
                    { "moles",       (MolesBarLabel,       MolesBar,       MolesBarBorder,       focusData.Value.AbsorbedMoles,   0.5f, 0.5f, green,    orange, red  ) },
                    { "temperature", (TemperatureBarLabel, TemperatureBar, TemperatureBarBorder, focusData.Value.Temperature,     0.5f, 0.5f, turqoise, green,  red  ) },
                    { "waste",       (WasteBarLabel,       WasteBar,       WasteBarBorder,       focusData.Value.WasteMultiplier, 0.5f, 0.5f, green,    orange, red  ) }
                };

                // Special cases
                var powerBar = engineDictionary["power"];
                var powerPrefix = powerBar.value switch { >= 1000 => "G", >= 1 => "M", _ => "" };
                var powerMultiplier = powerBar.value switch { >= 1000 => 0.001, >= 1 => 1, _ => 1000 };
                powerBar.label.Text = Loc.GetString("supermatter-console-window-label-power-bar", ("power", (powerBar.value * powerMultiplier).ToString("0.000")), ("prefix", powerPrefix));

                var temperatureLimit = focusData.Value.TemperatureLimit;
                TemperatureBar.MaxValue = temperatureLimit;
                TemperatureLimitBarLabel.Text = Loc.GetString("supermatter-console-window-label-temperature-bar", ("temperature", temperatureLimit.ToString("0.00")));

                var absorptionRatio = focusData.Value.AbsorptionRatio;
                AbsorptionBarLabel.Text = Loc.GetString("supermatter-console-window-label-absorption-bar", ("absorption", absorptionRatio.ToString("0")));

                // Update engine bars
                foreach (var bar in engineDictionary)
                {
                    var current = bar.Value;
                    UpdateEngineBar(current.bar, current.border, current.value, current.leftSize, current.rightSize, current.leftColor, current.middleColor, current.rightColor);

                    if (bar.Key == "power")
                        continue;

                    current.label.Text = Loc.GetString("supermatter-console-window-label-" + bar.Key + "-bar", (bar.Key, current.value.ToString("0.00")));
                }

                // Update gas bars
                var atmosphereSystem = _entManager.System<AtmosphereSystem>();
                var gases = atmosphereSystem.Gases.OrderByDescending(gas => GetStoredGas(gas, focusData));
                var index = 0;

                foreach (var gas in gases)
                {
                    var name = gas.Name;
                    var color = Color.FromHex("#" + gas.Color);
                    var value = GetStoredGas(gas, focusData) / focusData.Value.AbsorbedMoles * 100;

                    UpdateGasBar(index, GasTable, name, color, value);
                    index++;
                }
            }
        }
    }

    private string GetStatusLabel(SupermatterStatusType status)
    {
        switch (status)
        {
            case SupermatterStatusType.Inactive:
                return "supermatter-console-window-inactive-status";

            case SupermatterStatusType.Normal:
                return "supermatter-console-window-normal-status";

            case SupermatterStatusType.Caution:
                return "supermatter-console-window-caution-status";

            case SupermatterStatusType.Warning:
                return "supermatter-console-window-warning-status";

            case SupermatterStatusType.Danger:
                return "supermatter-console-window-danger-status";

            case SupermatterStatusType.Emergency:
                return "supermatter-console-window-emergency-status";

            case SupermatterStatusType.Delaminating:
                return "supermatter-console-window-delaminating-status";
        }

        return "supermatter-console-window-error-status";
    }

    private Color GetStatusColor(SupermatterStatusType status)
    {
        switch (status)
        {
            case SupermatterStatusType.Inactive:
                return Color.DarkGray;

            case SupermatterStatusType.Normal:
                return StyleNano.GoodGreenFore;

            case SupermatterStatusType.Caution:
            case SupermatterStatusType.Warning:
                return StyleNano.ConcerningOrangeFore;

            case SupermatterStatusType.Danger:
            case SupermatterStatusType.Emergency:
            case SupermatterStatusType.Delaminating:
                return StyleNano.DangerousRedFore;
        }

        return StyleNano.DisabledFore;
    }

    private float GetStoredGas(GasPrototype gas, SupermatterFocusData? focusData)
    {
        var id = int.Parse(gas.ID);

        if (focusData == null)
            return 0;

        return focusData.Value.GasStorage[(Gas)id];
    }

    private void UpdateEngineBar(ProgressBar bar, PanelContainer border, float value, float leftSize, float rightSize, Color leftColor, Color middleColor, Color rightColor)
    {
        var clamped = Math.Clamp(value, bar.MinValue, bar.MaxValue);

        var normalized = clamped / bar.MaxValue;
        var leftHsv = Color.ToHsv(leftColor);
        var middleHsv = Color.ToHsv(middleColor);
        var rightHsv = Color.ToHsv(rightColor);

        // Ensure leftSize and rightSize add up to 1.0 or the transition won't be smooth
        var minColor = new Vector4(0, 0, 0, 0);
        var maxColor = new Vector4(1, 1, 1, 1);
        Color finalColor;

        if (normalized <= leftSize)
        {
            normalized /= leftSize; // Adjust range to 0.0 to 1.0
            var calcColor = Vector4.Lerp(leftHsv, middleHsv, normalized);
            var clampedColor = Vector4.Clamp(calcColor, minColor, maxColor);
            finalColor = Color.FromHsv(clampedColor);
        }

        else
        {
            normalized = (normalized - leftSize) / rightSize; // Adjust range to 0.0 to 1.0
            var calcColor = Vector4.Lerp(middleHsv, rightHsv, normalized);
            var clampedColor = Vector4.Clamp(calcColor, minColor, maxColor);
            finalColor = Color.FromHsv(clampedColor);
        }

        // Check if null first to avoid repeatedly creating this.
        bar.ForegroundStyleBoxOverride ??= new StyleBoxFlat();
        border.PanelOverride ??= new StyleBoxFlat();

        var barOverride = (StyleBoxFlat)bar.ForegroundStyleBoxOverride;
        barOverride.BackgroundColor = finalColor;

        var panelOverride = (StyleBoxFlat)border.PanelOverride;
        panelOverride.BackgroundColor = finalColor;

        bar.Value = clamped;
    }

    private void UpdateGasBar(int index, Control table, string name, Color color, float value)
    {
        // Make new UI entry if required
        if (index >= table.ChildCount)
        {
            var newEntryContainer = new SupermatterGasBarContainer();

            // Add the entry to the current table
            table.AddChild(newEntryContainer);
        }

        // Update values and UI elements
        var tableChild = table.GetChild(index);

        if (tableChild is not SupermatterGasBarContainer)
        {
            table.RemoveChild(tableChild);
            UpdateGasBar(index, table, name, color, value);

            return;
        }

        var entryContainer = (SupermatterGasBarContainer)tableChild;

        entryContainer.UpdateEntry(name, color, value);
    }
}
