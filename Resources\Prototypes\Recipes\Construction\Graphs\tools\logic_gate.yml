﻿- type: constructionGraph
  id: LogicGate
  start: start
  graph:
  - node: start
    edges:
    - to: logic_gate
      steps:
      - material: Steel
        amount: 3
        doAfter: 1
      - material: Cable
        amount: 2
        doAfter: 1
    - to: edge_detector
      steps:
      - material: Steel
        amount: 3
        doAfter: 1
      - material: Cable
        amount: 2
        doAfter: 1
    - to: power_sensor
      steps:
      - material: Steel
        amount: 3
        doAfter: 1
      - material: Cable
        amount: 2
        doAfter: 1
      - tag: Multitool
        icon:
          sprite: Objects/Tools/multitool.rsi
          state: icon
        name: a multitool
  - node: logic_gate
    entity: LogicGateOr
  - node: edge_detector
    entity: EdgeDetector
  - node: power_sensor
    entity: PowerSensor
