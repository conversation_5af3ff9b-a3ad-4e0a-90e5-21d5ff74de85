### for technical and/or system messages

## General

shell-server-cannot = Server cannot do this.
shell-command-success = Command successful
shell-invalid-command = Invalid command.
shell-invalid-command-specific = Invalid {$commandName} command.
shell-cannot-run-command-from-server = You cannot run this command from the server.
shell-only-players-can-run-this-command = Only players can run this command.
shell-must-be-attached-to-entity = You must be attached to an entity to run this command.

## Arguments

shell-need-exactly-one-argument = Need exactly one argument.
shell-wrong-arguments-number-need-specific = Need {$properAmount} arguments, there were {$currentAmount}.
shell-argument-must-be-number = Argument must be a number.
shell-argument-must-be-boolean = Argument must be a boolean.
shell-wrong-arguments-number = Wrong number of arguments.
shell-need-between-arguments = Need {$lower} to {$upper} arguments!
shell-need-minimum-arguments = Need at least {$minimum} arguments!
shell-need-minimum-one-argument = Need at least one argument!

shell-argument-uid = EntityUid

## Guards

shell-entity-is-not-mob = Target entity is not a mob!
shell-invalid-entity-id = Invalid entity ID.
shell-invalid-grid-id = Invalid grid ID.
shell-invalid-map-id = Invalid map ID.
shell-invalid-entity-uid = {$uid} is not a valid entity uid
shell-invalid-bool = Invalid boolean.
shell-entity-uid-must-be-number = EntityUid must be a number.
shell-could-not-find-entity = Could not find entity {$entity}
shell-could-not-find-entity-with-uid = Could not find entity with uid {$uid}
shell-entity-with-uid-lacks-component = Entity with uid {$uid} doesn't have {INDEFINITE($componentName)} {$componentName} component
shell-invalid-color-hex = Invalid color hex!
shell-target-player-does-not-exist = Target player does not exist!
shell-target-entity-does-not-have-message = Target entity does not have {INDEFINITE($missing)} {$missing}!
shell-timespan-minutes-must-be-correct = {$span} is not a valid minutes timespan.
shell-argument-must-be-prototype = Argument {$index} must be a {LOC($prototypeName)}!
shell-argument-number-must-be-between = Argument {$index} must be a number between {$lower} and {$upper}!
shell-argument-station-id-invalid = Argument {$index} must be a valid station id!
shell-argument-map-id-invalid = Argument {$index} must be a valid map id!
shell-argument-number-invalid = Argument {$index} must be a valid number!

# Hints
shell-argument-username-hint = <username>
shell-argument-username-optional-hint = [username]
