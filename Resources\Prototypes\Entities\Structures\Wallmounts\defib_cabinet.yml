﻿- type: entity
  id: DefibrillatorCabinet
  name: "шафа для дефібрилятора"
  description: "Невелика настінна шафа, призначена для розміщення дефібрилятора."
  components:
    - type: WallMount
      arc: 175
    - type: Transform
      anchored: true
    - type: Clickable
    - type: InteractionOutline
    - type: Sprite
      sprite: Structures/Wallmounts/defib_cabinet.rsi
      noRot: false
      layers:
      - state: frame
      - state: fill
        map: ["enum.ItemCabinetVisualLayers.ContainsItem"]
        visible: true
      - state: closed
        map: ["enum.ItemCabinetVisualLayers.Door"]
    - type: ItemCabinet
      cabinetSlot:
        ejectOnInteract: true
        whitelist:
          components:
          - Defibrillator
      doorSound:
        path: /Audio/Machines/machine_switch.ogg
      openState: open
      closedState: closed
    - type: Appearance
    - type: ItemSlots
    - type: ContainerContainer
      containers:
        ItemCabinet: !type:ContainerSlot
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: Metallic
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 80
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
        - trigger:
            !type:DamageTrigger
            damage: 40
          behaviors:
            - !type:EmptyAllContainersBehaviour
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
            - !type:PlaySoundBehavior
              sound:
                collection: MetalGlassBreak
  placement:
    mode: SnapgridCenter

- type: entity
  id: DefibrillatorCabinetOpen
  parent: DefibrillatorCabinet
  suffix: Open
  components:
  - type: ItemCabinet
    opened: true
    doorSound:
      path: /Audio/Machines/machine_switch.ogg
    openState: open
    closedState: closed

- type: entity
  id: DefibrillatorCabinetFilled
  parent: DefibrillatorCabinet
  suffix: Filled
  components:
  - type: ItemCabinet
    cabinetSlot:
      ejectOnInteract: true
      startingItem: Defibrillator
      whitelist:
        components:
        - Defibrillator
    doorSound:
      path: /Audio/Machines/machine_switch.ogg
    openState: open
    closedState: closed

- type: entity
  id: DefibrillatorCabinetFilledOpen
  parent: DefibrillatorCabinetFilled
  suffix: Filled, Open
  components:
  - type: ItemCabinet
    opened: true
    doorSound:
      path: /Audio/Machines/machine_switch.ogg
    openState: open
    closedState: closed
