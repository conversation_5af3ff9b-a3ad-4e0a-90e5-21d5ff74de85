- type: job
  id: Prisoner
  name: job-name-prisoner
  description: job-description-prisoner
  playTimeTracker: JobPrisoner
  startingGear: PrisonerGear
  alwaysUseSpawner: true
  canBeAntag: false
#  whitelistRequired: true
  icon: "JobIconPrisoner"
  supervisors: job-supervisors-security
  requirements:
    - !type:CharacterDepartmentTimeRequirement
      department: Security
      min: 21600
    - !type:CharacterWhitelistRequirement # PIRATE - Whitelist requirement
  special:
  - !type:AddComponentSpecial
    components:
      - type: Pacified
  afterLoadoutSpecial:
  - !type:ModifyEnvirosuitSpecial
    charges: 3 # Poor prisoners with not a lot of self-extinguishes.
  - !type:ModifyEnvirohelmSpecial
    powerCell: PowerCellPotato

- type: startingGear
  id: PrisonerGear
  subGear:
  - PrisonerPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitPrisoner
    shoes: ClothingShoesColorBlack
    id: PrisonerPDA
    ears: ClothingHeadsetPrison #deltaV
  innerClothingSkirt: ClothingUniformJumpsuitPrisoner

- type: startingGear
  id: PrisonerPlasmamanGear
  parent: BasePlasmamanGear
  equipment:
    jumpsuit: ClothingUniformEnvirosuitPrisoner
    head: ClothingHeadEnvirohelmPrisoner
    gloves: ClothingHandsGlovesEnviroglovesBlack
