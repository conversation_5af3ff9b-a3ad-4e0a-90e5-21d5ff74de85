cmd-bql_select-desc = Відображення результатів BQL-запиту у вікні на стороні клієнта
cmd-bql_select-help = Використання: bql_select <bql-запит>.
    У вікні, що відкриється, ви можете телепортуватися до отриманих сутностей або переглянути їхні змінні.

cmd-bql_select-err-server-shell = Неможливо виконати з оболонки сервера
cmd-bql_select-err-rest = Попередження: невикористана частина після BQL-запиту: "{ $rest }"

ui-bql-results-title = Результати BQL
ui-bql-results-vv = В.В
ui-bql-results-tp = ТП
ui-bql-results-vv-tooltip = Перегляд змінних сутності
ui-bql-results-tp-tooltip = Телепортуйтеся до сутності
ui-bql-results-status = { $count } одиниць
