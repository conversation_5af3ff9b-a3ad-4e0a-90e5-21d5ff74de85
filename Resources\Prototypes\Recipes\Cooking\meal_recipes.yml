- type: microwaveMealRecipe
  id: RecipeBun
  name: "рецепт булочки"
  result: FoodBreadBun
  time: 5
  solids:
    FoodDoughSlice: 1 # one third of a standard bread dough recipe

#Burgers

- type: microwaveMealRecipe
  id: RecipeAppendixBurger
  name: "рецепт бургера з апендиксом"
  result: FoodBurgerAppendix
  time: 10
  solids:
    FoodBreadBun: 1
    OrganHumanAppendix: 1

- type: microwaveMealRecipe
  id: RecipeBaconBurger
  name: "рецепт бургера з беконом"
  result: FoodBurgerBacon
  time: 10
  solids:
    FoodBreadBun: 1
    FoodMeatBacon: 1
    FoodCheeseSlice: 2

- type: microwaveMealRecipe
  id: RecipeBaseballBurger
  name: "рецепт бейсбольного бургера"
  result: FoodBurgerBaseball
  time: 10
  solids:
    FoodBreadBun: 1
    BaseBallBat: 1

- type: microwaveMealRecipe
  id: RecipeBearger
  name: "рецепт ведмебургера"
  result: FoodBurgerBear
  time: 10
  solids:
    FoodBreadBun: 1
    FoodMeatBear: 1

- type: microwaveMealRecipe
  id: RecipeBigBiteBurger
  name: "рецепт бургера з великим шматком"
  result: FoodBurgerBig
  time: 15
  solids:
    FoodBreadBun: 1
    FoodMeat: 2
    FoodCheeseSlice: 1
    FoodTomato: 1
    FoodOnionSlice: 2

- type: microwaveMealRecipe #Added to metamorph recipes
  id: RecipeBrainBurger
  name: "рецепт бургера з мозком"
  result: FoodBurgerBrain
  time: 10
  solids:
    FoodBreadBun: 1
    OrganHumanBrain: 1

- type: microwaveMealRecipe
  id: RecipeCatBurger
  name: "рецепт котячого бургера"
  result: FoodBurgerCat
  time: 10
  solids:
    FoodBreadBun: 1
    FoodMeat: 1
    ClothingHeadHatCatEars: 1

- type: microwaveMealRecipe #Added to metamorph recipes
  id: RecipeCheeseburger
  name: "рецепт чизбургера"
  result: FoodBurgerCheese
  time: 10
  solids:
    FoodBreadBun: 1
    FoodMeat: 1
    FoodCheeseSlice: 1

- type: microwaveMealRecipe #Added to metamorph recipes
  id: RecipeChickenSandwich
  name: "рецепт курячого сендвіча"
  result: FoodBurgerChicken
  time: 10
  reagents:
    Mayo: 5
  solids:
    FoodBreadBun: 1
    FoodMeatChicken: 1

- type: microwaveMealRecipe
  id: RecipeClownBurger
  name: "рецепт клоунбургера"
  result: FoodBurgerClown
  time: 10
  solids:
    FoodBreadBun: 1
    ClothingMaskClown: 1

- type: microwaveMealRecipe
  id: RecipeCorgiBurger
  name: "рецепт коргі-бургера"
  result: FoodBurgerCorgi
  time: 10
  solids:
    FoodBreadBun: 1
    FoodMeatCorgi: 1

- type: microwaveMealRecipe #Added to metamorph recipes
  id: RecipeCrabBurger
  name: "рецепт крабового бургера"
  result: FoodBurgerCrab
  time: 10
  solids:
    FoodBreadBun: 1
    FoodMeatCrab: 2

- type: microwaveMealRecipe
  id: RecipeCrazyHamburger
  name: "рецепт божевільного гамбургера"
  result: FoodBurgerCrazy
  time: 15
  reagents:
    OilOlive: 15
  solids:
    FoodBreadBun: 1
    FoodMeat: 2
    FoodCheeseSlice: 2
    FoodChiliPepper: 1
    FoodCabbage: 1
    CrayonGreen: 1
    Flare: 1

- type: microwaveMealRecipe #Added to metamorph recipes
  id: RecipeDuckBurger
  name: "рецепт качиного сендвіча"
  result: FoodBurgerDuck
  time: 10
  solids:
    FoodBreadBun: 1
    FoodMeatDuck: 1
    FoodCheeseSlice: 1

- type: microwaveMealRecipe
  id: RecipeEmpoweredBurger
  name: "рецепт посиленого бургера"
  result: FoodBurgerEmpowered
  time: 10
  solids:
    FoodBreadBun: 1
    SheetPlasma1: 2

- type: microwaveMealRecipe
  id: RecipeCarpBurger
  name: "рецепт бургера з філе коропа"
  result: FoodBurgerCarp
  time: 10
  solids:
    FoodBreadBun: 1
    FoodMeatFish: 1
    FoodCheeseSlice: 1

- type: microwaveMealRecipe
  id: RecipeFiveBurger
  name: "рецепт бургера з п'ятьма перцями"
  result: FoodBurgerFive
  time: 10
  solids:
    FoodBreadBun: 1
    FoodMeat: 1
    FoodChiliPepper: 3

- type: microwaveMealRecipe
  id: RecipeGhostBurger
  name: "рецепт примарного бургера"
  result: FoodBurgerGhost
  time: 10
  solids:
    FoodBreadBun: 1
    Ectoplasm: 1

- type: microwaveMealRecipe
  id: RecipeHumanBurger
  name: "рецепт людського бургера"
  result: FoodBurgerHuman
  time: 10
  solids:
    FoodBreadBun: 1
    FoodMeatHuman: 1

- type: microwaveMealRecipe
  id: RecipeJellyBurger
  name: "рецепт желейного бургера"
  result: FoodBurgerJelly
  time: 10
  solids:
    FoodBreadBun: 1
    FoodJellyAmanita: 1

- type: microwaveMealRecipe
  id: RecipeBurgerMcguffin
  name: "рецепт МакГафіна"
  result: FoodBurgerMcguffin
  time: 10
  solids:
    FoodBreadBun: 1
    FoodCheeseSlice: 1
    FoodEgg: 2

- type: microwaveMealRecipe
  id: RecipeBurgerMcrib
  name: "рецепт сендвіча з реберець барбекю"
  result: FoodBurgerMcrib
  time: 10
  solids:
    FoodBreadBun: 1
    FoodMealRibs: 1
    FoodOnionSlice: 1

- type: microwaveMealRecipe
  id: RecipeMimeBurger
  name: "рецепт мім-бургера"
  result: FoodBurgerMime
  time: 10
  solids:
    FoodBreadBun: 1
    ClothingMaskMime: 1

- type: microwaveMealRecipe
  id: RecipePlainBurger
  name: "рецепт простого бургера"
  result: FoodBurgerPlain
  time: 10
  solids:
    FoodBreadBun: 1
    FoodMeat: 1

- type: microwaveMealRecipe
  id: RecipeRatBurger
  name: "рецепт щурячого бургера"
  result: FoodBurgerRat
  time: 10
  solids:
    FoodBreadBun: 1
    FoodMeatRat: 1

- type: microwaveMealRecipe
  id: RecipeRobotBurger
  name: "рецепт робобургера"
  result: FoodBurgerRobot
  time: 10
  solids:
    FoodBreadBun: 1
    CapacitorStockPart: 2
    # i would add steel to this recipe but the microwave explodes

- type: microwaveMealRecipe
  id: RecipeSoylentBurger
  name: "рецепт сойлент-бургера"
  result: FoodBurgerSoy
  time: 10
  solids:
    FoodBreadBun: 1
    FoodCheeseSlice: 2
    FoodSoybeans: 2 #replace with soylent green when those become craftable

- type: microwaveMealRecipe
  id: RecipeSpellBurger
  name: "рецепт спелл-бургера"
  result: FoodBurgerSpell
  time: 10
  solids:
    FoodBreadBun: 1
    ClothingHeadHatWizard: 1

- type: microwaveMealRecipe
  id: RecipeSuperBiteBurger
  name: "рецепт супер-бургера"
  result: FoodBurgerSuper
  time: 25
  reagents:
    TableSalt: 5
  solids:
    FoodBreadBun: 1
    FoodMeat: 2
    FoodCheeseSlice: 2
    FoodTomato: 2
    FoodEgg: 2

- type: microwaveMealRecipe
  id: RecipeTofuBurger
  name: "рецепт тофу-бургера"
  result: FoodBurgerTofu
  time: 10
  solids:
    FoodBreadBun: 1
    FoodTofuSlice: 1

- type: microwaveMealRecipe
  id: RecipeXenoburger
  name: "рецепт ксенобургера"
  result: FoodBurgerXeno
  time: 10
  solids:
    FoodBreadBun: 1
    FoodMeatXeno: 1

- type: microwaveMealRecipe
  id: RecipeMothRoachburger
  name: "рецепт тарганоміль-бургера"
  result: FoodBurgerMothRoach
  solids:
    FoodBreadBun: 1
    MobMothroach: 1

#Breads & Sandwiches

- type: microwaveMealRecipe
  id: RecipeBananaBread
  name: "рецепт бананового хліба"
  result: FoodBreadBanana
  time: 15
  solids:
    FoodDough: 1
    FoodBanana: 1

- type: microwaveMealRecipe
  id: RecipeCornbread
  name: "рецепт кукурудзяного хліба"
  result: FoodBreadCorn
  time: 10
  solids:
    FoodDoughCornmeal: 1

- type: microwaveMealRecipe
  id: RecipeCreamCheeseBread
  name: "рецепт хліба з вершковим сиром"
  result: FoodBreadCreamcheese
  time: 15
  reagents:
    Milk: 5
  solids:
    FoodDough: 1
    FoodCheeseSlice: 2

- type: microwaveMealRecipe
  id: RecipeMeatBread
  name: "рецепт м'ясного хліба"
  result: FoodBreadMeat
  time: 15
  solids:
    FoodDough: 1
    FoodMeatCutlet: 2
    FoodCheeseSlice: 2

- type: microwaveMealRecipe
  id: RecipeMimanaBread
  name: "рецепт мімана-хліба"
  result: FoodBreadMimana
  time: 15
  reagents:
    Nothing: 5
  solids:
    FoodDough: 1
    FoodMimana: 1

- type: microwaveMealRecipe
  id: RecipeBread
  name: "рецепт хліба"
  result: FoodBreadPlain
  time: 10
  solids:
    FoodDough: 1

- type: microwaveMealRecipe
  id: RecipeSausageBread
  name: "рецепт ковбасного хліба"
  result: FoodBreadSausage
  time: 15
  solids:
    FoodDough: 1
    FoodMeat: 1 #replace with sausage

- type: microwaveMealRecipe
  id: RecipeSpiderMeatBread
  name: "рецепт хліба з павучого м'яса"
  result: FoodBreadMeatSpider
  time: 15
  solids:
    FoodDough: 1
    FoodMeatSpiderCutlet: 2
    FoodCheeseSlice: 2

- type: microwaveMealRecipe
  id: RecipeTofuBread
  name: "рецепт тофу-хліба"
  result: FoodBreadTofu
  time: 15
  solids:
    FoodDough: 1
    FoodTofu: 1

- type: microwaveMealRecipe
  id: RecipeXenoMeatBread
  name: "рецепт хліба з ксеном'яса"
  result: FoodBreadMeatXeno
  time: 15
  solids:
    FoodDough: 1
    FoodMeatXenoCutlet: 2
    FoodCheeseSlice: 2

#Slices Only

- type: microwaveMealRecipe
  id: RecipeBaguette
  name: "рецепт багета"
  result: FoodBreadBaguette
  time: 15
  reagents:
    TableSalt: 5
    Blackpepper: 5
  solids:
    FoodDough: 1

- type: microwaveMealRecipe
  id: RecipeBaguetteSword
  name: "рецепт багетного меча"
  result: WeaponBaguette
  secretRecipe: true
  time: 15
  reagents:
    TableSalt: 5
    Blackpepper: 5
  solids:
    FoodDough: 1
    PartRodMetal1: 1

- type: microwaveMealRecipe
  id: RecipeButteredToast
  name: "рецепт тосту з маслом"
  result: FoodBreadButteredToast
  time: 5
  solids:
    FoodBreadPlainSlice: 1
    FoodButterSlice: 1

- type: microwaveMealRecipe
  id: RecipeFrenchToast
  name: "рецепт французького тосту"
  result: FoodBreadFrenchToast
  time: 5
  reagents:
    Milk: 5
    Egg: 12
  solids:
    FoodBreadPlainSlice: 1

- type: microwaveMealRecipe
  id: RecipeGarlicBread
  name: "рецепт часникового хліба"
  result: FoodBreadGarlicSlice
  time: 5
  solids:
    FoodBreadPlainSlice: 1
    FoodGarlic: 1
    FoodButterSlice: 1

- type: microwaveMealRecipe
  id: RecipeJellyToast
  name: "рецепт тосту з желе"
  result: FoodBreadJellySlice
  time: 5
  solids:
    FoodBreadPlainSlice: 1
    FoodJellyAmanita: 1 #replace with jelly

- type: microwaveMealRecipe
  id: RecipeMoldyBreadSlice
  name: "рецепт цвілого шматка хліба"
  result: FoodBreadMoldySlice
  time: 5
  solids:
    FoodBreadPlainSlice: 1
    FoodFlyAmanita: 1

- type: microwaveMealRecipe
  id: RecipeTwoBreadSlice
  name: "рецепт двох шматків хліба"
  result: FoodBreadTwoSlice
  time: 5
  reagents:
    Wine: 5
  solids:
    FoodBreadPlainSlice: 2

- type: microwaveMealRecipe
  id: RecipeOnionRings
  name: "рецепт цибулевих кілець"
  result: FoodOnionRings
  time: 15
  solids:
    FoodOnionSlice: 1

#Pizzas TODO: contruction graph based pizza
- type: microwaveMealRecipe
  id: RecipeMargheritaPizza
  name: "рецепт піци Маргарита"
  result: FoodPizzaMargherita
  time: 30
  solids:
    FoodDoughFlat: 1
    FoodCheeseSlice: 1
    FoodTomato: 4

- type: microwaveMealRecipe
  id: RecipeMushroomPizza
  name: "рецепт грибної піци"
  result: FoodPizzaMushroom
  time: 30
  solids:
    FoodDoughFlat: 1
    FoodMushroom: 5

- type: microwaveMealRecipe
  id: RecipeMeatPizza
  name: "рецепт м'ясної піци"
  result: FoodPizzaMeat
  time: 30
  solids:
    FoodDoughFlat: 1
    FoodMeat: 3
    FoodCheeseSlice: 1
    FoodTomato: 1

- type: microwaveMealRecipe
  id: RecipeVegetablePizza
  name: "рецепт овочевої піци"
  result: FoodPizzaVegetable
  time: 30
  solids:
    FoodDoughFlat: 1
    FoodEggplant: 1
    FoodCarrot: 1
    FoodCorn: 1
    FoodTomato: 1

- type: microwaveMealRecipe
  id: RecipeHawaiianPizza
  name: "рецепт гавайської піци"
  result: FoodPizzaPineapple
  time: 30
  solids:
    FoodDoughFlat: 1
    FoodMeatChickenCutlet: 3
    FoodPineappleSlice: 5

- type: microwaveMealRecipe
  id: RecipeDankPizza
  name: "рецепт данк-піци"
  result: FoodPizzaDank
  time: 30
  solids:
    FoodDoughFlat: 1
    FoodAmbrosiaVulgaris: 3
    FoodCheeseSlice: 1
    FoodTomato: 1

- type: microwaveMealRecipe
  id: RecipeDonkpocketPizza
  name: "рецепт донк-покет піци"
  result: FoodPizzaDonkpocket
  time: 30
  solids:
    FoodDoughFlat: 1
    FoodDonkpocketWarm: 3
    FoodCheeseSlice: 1
    FoodTomato: 1

#Italian
- type: microwaveMealRecipe
  id: RecipeBoiledSpaghetti
  name: "рецепт варених спагеті"
  result: FoodNoodlesBoiled
  time: 15
  reagents:
    Flour: 15
    Egg: 6
  solids:
    FoodButter: 1

- type: microwaveMealRecipe
  id: RecipePastaTomato
  name: "рецепт пасти з томатами"
  result: FoodNoodles
  time: 10
  solids:
    FoodNoodlesBoiled: 1
    FoodTomato: 2

- type: microwaveMealRecipe
  id: RecipeMeatballSpaghetti
  name: "рецепт спагеті з фрикадельками"
  result: FoodNoodlesMeatball
  time: 10
  solids:
    FoodNoodlesBoiled: 1
    FoodMeatMeatball: 2

- type: microwaveMealRecipe
  id: RecipeButterNoodles
  name: "рецепт локшини з маслом"
  result: FoodNoodlesButter
  time: 10
  solids:
    FoodNoodlesBoiled: 1
    FoodButter: 1

- type: microwaveMealRecipe
  id: RecipeChowMein
  name: "рецепт чау-мейн"
  result: FoodNoodlesChowmein
  time: 10
  solids:
    FoodNoodlesBoiled: 1
    FoodEggplant: 1
    FoodCarrot: 1
    FoodCorn: 1
    FoodEgg: 1

- type: microwaveMealRecipe
  id: RecipeOatmeal
  name: "рецепт вівсянки"
  result: FoodOatmeal
  time: 15
  reagents:
    Oats: 15
    Water: 10
  solids:
    FoodBowlBig: 1

- type: microwaveMealRecipe
  id: RecipeBoiledRice
  name: "рецепт вареного рису"
  result: FoodRiceBoiled
  time: 15
  reagents:
    Rice: 15
    Water: 10
  solids:
    FoodBowlBig: 1

- type: microwaveMealRecipe
  id: RecipeRicePudding
  name: "рецепт рисового пудингу"
  result: FoodRicePudding
  time: 15
  reagents:
    Rice: 15
    Milk: 10
    Sugar: 5
  solids:
    FoodBowlBig: 1

- type: microwaveMealRecipe
  id: RecipeRicePork
  name: "рецепт рису зі свининою"
  result: FoodRicePork
  time: 15
  solids:
    FoodRiceBoiled: 1
    FoodMeatCutlet: 3

- type: microwaveMealRecipe
  id: RecipeRiceGumbo
  name: "рецепт гамбо з чорнооким горохом"
  result: FoodRiceGumbo
  time: 15
  solids:
    FoodRiceBoiled: 1
    FoodMeatCutlet: 3
    FoodChiliPepper: 2

- type: microwaveMealRecipe
  id: RecipeEggRice
  name: "рецепт смаженого рису з яйцем"
  result: FoodRiceEgg
  time: 15
  solids:
    FoodRiceBoiled: 1
    FoodEgg: 1
    FoodCarrot: 1

- type: microwaveMealRecipe
  id: RecipeCopypasta
  name: "рецепт копіпасти"
  result: FoodNoodlesCopy
  time: 10
  solids:
    FoodNoodles: 2

#Soups & Stew
- type: microwaveMealRecipe
  id: RecipeBisque
  name: "рецепт біску"
  result: FoodSoupBisque
  time: 10
  reagents:
    Water: 10
  solids:
    FoodBowlBig: 1
    FoodTomato: 1
    FoodMushroom: 1
    FoodMeatFish: 1

- type: microwaveMealRecipe
  id: RecipeMeatballSoup
  name: "рецепт супу з фрикадельками"
  result: FoodSoupMeatball
  time: 10
  reagents:
    Water: 10
  solids:
    FoodBowlBig: 1
    FoodMeatMeatball: 1
    FoodCarrot: 1
    FoodPotato: 1

- type: microwaveMealRecipe
  id: RecipeNettleSoup
  name: "рецепт супу з кропиви"
  result: FoodSoupNettle
  time: 10
  reagents:
    Water: 10
    Egg: 6
  solids:
    FoodBowlBig: 1
    Nettle: 1
    FoodPotato: 1

- type: microwaveMealRecipe
  id: RecipeEyeballSoup
  name: "рецепт супу з очними яблуками"
  result: FoodSoupEyeball
  time: 10
  reagents:
    Water: 10
  solids:
    FoodBowlBig: 1
    OrganHumanEyes: 1
    FoodCarrot: 1
    FoodPotato: 1

- type: microwaveMealRecipe
  id: RecipeAmanitaJelly
  name: "рецепт желе з мухоморів"
  result: FoodJellyAmanita
  time: 10
  reagents:
    Water: 5
    Vodka: 5
  solids:
    FoodFlyAmanita: 3

- type: microwaveMealRecipe
  id: RecipeOnionSoup
  name: "рецепт цибулевого супу"
  result: FoodSoupOnion
  time: 10
  reagents:
    Water: 10
  solids:
    FoodBowlBig: 1
    FoodOnionSlice: 5
    FoodCheeseSlice: 1

- type: microwaveMealRecipe
  id: RecipeMushroomSoup
  name: "рецепт грибного супу"
  result: FoodSoupMushroom
  time: 10
  reagents:
    Water: 5
    Milk: 5
  solids:
    FoodBowlBig: 1
    FoodMushroom: 2

- type: microwaveMealRecipe
  id: RecipeStewSoup
  name: "рецепт рагу"
  result: FoodSoupStew
  time: 10
  reagents:
    Water: 10
  solids:
    FoodBowlBig: 1
    FoodMeatCutlet: 3
    FoodTomato: 1
    FoodPotato: 1
    FoodCarrot: 1
    FoodEggplant: 1
    FoodMushroom: 1

- type: microwaveMealRecipe
  id: RecipeTomatoSoup
  name: "рецепт томатного супу"
  result: FoodSoupTomato
  time: 10
  reagents:
    Water: 10
  solids:
    FoodBowlBig: 1
    FoodTomato: 2

- type: microwaveMealRecipe
  id: RecipeTomatoBloodSoup
  name: "рецепт томатно-кров'яного супу"
  result: FoodSoupTomatoBlood
  time: 10
  reagents:
    Blood: 10
  solids:
    FoodBowlBig: 1
    FoodBloodTomato: 2

- type: microwaveMealRecipe
  id: RecipeWingFangChuSoup
  name: "рецепт супу з крил, іклів та Чу"
  result: FoodSoupWingFangChu
  time: 10
  reagents:
    Soysauce: 5
  solids:
    FoodBowlBig: 1
    FoodMeatXenoCutlet: 2

- type: microwaveMealRecipe
  id: RecipeWingFangChuSoupSpider
  name: "рецепт супу з крил, іклів та Чу"
  result: FoodSoupWingFangChu
  time: 10
  reagents:
    Soysauce: 5
  solids:
    FoodBowlBig: 1
    FoodMeatSpider: 2

- type: microwaveMealRecipe
  id: RecipeVegetableSoup
  name: "рецепт овочевого супу"
  result: FoodSoupVegetable
  time: 10
  reagents:
    Water: 5
  solids:
    FoodBowlBig: 1
    FoodCorn: 1
    FoodCarrot: 1
    FoodPotato: 1
    FoodEggplant: 1

- type: microwaveMealRecipe
  id: RecipeClownTearsSoup
  name: "рецепт супу зі сліз клоуна"
  result: FoodSoupClown
  time: 10
  reagents:
    Water: 10
  solids:
    FoodBowlBig: 1
    FoodBanana: 1
    ShardGlass: 1
    #idk probably replace shard with someting bananium when #14663 merged

- type: microwaveMealRecipe
  id: RecipeMonkeysDelightSoup
  name: "рецепт супу з мавпячої насолоди"
  result: FoodSoupMonkey
  time: 10
  reagents:
    Flour: 5
    TableSalt: 1
    Blackpepper: 1
  solids:
    FoodBowlBig: 1
    FoodBanana: 1
    MonkeyCube: 1

- type: microwaveMealRecipe
  id: RecipeBungoSoup
  name: "рецепт супу бунго"
  result: FoodSoupBungo
  time: 10
  reagents:
    Water: 5
  solids:
    FoodBowlBig: 1
    FoodBungo: 2
    FoodChiliPepper: 1

- type: microwaveMealRecipe
  id: RecipeBoiledSnail
  name: "рецепт вареного равлика"
  result: FoodMeatSnailCooked
  time: 5
  reagents:
    Water: 10
  solids:
    FoodMeatSnail: 1

- type: microwaveMealRecipe
  id: RecipeEscargotSoup
  name: "рецепт ескарго"
  result: FoodSoupEscargot
  time: 10
  reagents:
    Water: 5
  solids:
    FoodBowlBig: 1
    FoodOnionSlice: 1
    FoodButter: 1
    FoodMeatSnailCooked: 1

#Pies

- type: microwaveMealRecipe
  id: RecipeAmanitaPie
  name: "рецепт пирога з мухоморами"
  result: FoodPieAmanita
  time: 15
  solids:
    FoodDoughPie: 1
    FoodFlyAmanita: 1
    FoodPlateTin: 1

- type: microwaveMealRecipe
  id: RecipeApplePie
  name: "рецепт яблучного пирога"
  result: FoodPieApple
  time: 15
  solids:
    FoodDoughPie: 1
    FoodApple: 3
    FoodPlateTin: 1

- type: microwaveMealRecipe
  id: RecipeBaklava
  name: "рецепт пахлави"
  result: FoodPieBaklava
  time: 15
  solids:
    FoodDoughPie: 1
    FoodSnackPistachios: 1 #i'd rather use a botany crop but we don't have nuts yet
    FoodPlateTin: 1

- type: microwaveMealRecipe
  id: RecipeBananaCreamPie
  name: "рецепт бананового кремового пирога"
  result: FoodPieBananaCream
  time: 15
  solids:
    FoodDoughPie: 1
    FoodBanana: 3
    FoodPlateTin: 1

- type: microwaveMealRecipe
  id: RecipeBerryClafoutis
  name: "рецепт ягідного клафуті"
  result: FoodPieClafoutis
  time: 15
  solids:
    FoodDoughPie: 1
    FoodBerries: 3
    FoodPlateTin: 1

- type: microwaveMealRecipe
  id: RecipeCherryPie
  name: "рецепт вишневого пирога"
  result: FoodPieCherry
  time: 15
  solids:
    FoodDoughPie: 1
    FoodCherry: 5
    FoodPlateTin: 1

- type: microwaveMealRecipe
  id: RecipeFrostyPie
  name: "рецепт морозного пирога"
  result: FoodPieFrosty
  time: 15
  solids:
    FoodDoughPie: 1
    FoodChillyPepper: 3
    FoodPlateTin: 1

- type: microwaveMealRecipe
  id: RecipeMeatPie
  name: "рецепт м'ясного пирога"
  result: FoodPieMeat
  time: 15
  solids:
    FoodDoughPie: 1
    FoodMeat: 3
    FoodPlateTin: 1

#- type: microwaveMealRecipe
#  id: RecipePlumpPie
#  name: plump pie recipe
#  result: FoodPiePlump
#  time: 15
#  solids:
#    FoodDoughPie: 1
#    FoodPlumpHelmet: 3 #a big part of me wants to veto this because the item description is a dick joke but i'll write the placeholder anyway
#    FoodPlateTin: 1

- type: microwaveMealRecipe
  id: RecipeXenoPie
  name: "рецепт ксенопирога"
  result: FoodPieXeno
  time: 15
  solids:
    FoodDoughPie: 1
    FoodMeatXeno: 3
    FoodPlateTin: 1

#Tarts

- type: microwaveMealRecipe
  id: RecipeCocoTart
  name: "рецепт шоколадного лавового тарта"
  result: FoodTartCoco
  time: 15
  reagents:
    Sugar: 5
    Milk: 5
  solids:
    FoodDoughPie: 1
    FoodSnackChocolateBar: 3
    FoodPlateTin: 1

- type: microwaveMealRecipe
  id: RecipeGappleTart
  name: "рецепт тарта з золотими яблуками та штрейзелем"
  result: FoodTartGapple
  time: 15
  reagents:
    Gold: 10
    Sugar: 5
    Milk: 5
  solids:
    FoodDoughPie: 1
    FoodApple: 2 #in absence of the real gapple i'm substituting one apple with 10u gold (one ingot)
    FoodPlateTin: 1

- type: microwaveMealRecipe
  id: RecipeGrapeTart
  name: "рецепт виноградного тарта"
  result: FoodTartGrape
  time: 15
  reagents:
    Sugar: 5
    Milk: 5
  solids:
    FoodDoughPie: 1
    FoodGrape: 3
    FoodPlateTin: 1

- type: microwaveMealRecipe
  id: RecipeMimeTart
  name: "рецепт тарта міма"
  result: FoodTartMime
  time: 15
  reagents:
    Sugar: 5
    Milk: 5
  solids:
    FoodDoughPie: 1
    FoodMimana: 3
    FoodPlateTin: 1

#Other

- type: microwaveMealRecipe
  id: RecipeCubanCarp
  name: "рецепт кубинського коропа"
  result: FoodMealCubancarp
  time: 15
  solids:
    FoodDough: 1
    FoodCheeseSlice: 2
    FoodChiliPepper: 1
    FoodMeatFish: 2

- type: microwaveMealRecipe
  id: RecipeSashimi
  name: "рецепт сашимі"
  result: FoodMealSashimi
  time: 15
  reagents:
    TableSalt: 1
  solids:
    FoodMeatFish: 2

- type: microwaveMealRecipe
  id: RecipeMisoColaSoup
  name: "рецепт солодко-солоного супу місокола"
  result: DisgustingSweptSoup
  time: 15
  reagents:
    Cola: 5
  solids:
    FoodSoupMiso: 1

- type: microwaveMealRecipe
  id: RecipeLoadedBakedPotato
  name: "рецепт запеченої картоплі з начинкою"
  result: FoodMealPotatoLoaded
  time: 15
  solids:
    FoodPotato: 1
    FoodCheeseSlice: 1

- type: microwaveMealRecipe
  id: RecipeFries
  name: "рецепт космічної картоплі фрі"
  result: FoodMealFries
  time: 15
  reagents:
    TableSalt: 5
  solids:
    FoodPotato: 1

- type: microwaveMealRecipe
  id: RecipeCheesyFries
  name: "рецепт сирної картоплі фрі"
  result: FoodMealFriesCheesy
  time: 15
  reagents:
    TableSalt: 5
  solids:
    FoodPotato: 1
    FoodCheeseSlice: 1

- type: microwaveMealRecipe
  id: RecipeCarrotFries
  name: "рецепт морквяної картоплі фрі"
  result: FoodMealFriesCarrot
  time: 15
  reagents:
    TableSalt: 5
  solids:
    FoodCarrot: 1

- type: microwaveMealRecipe
  id: RecipePopcorn
  name: "рецепт попкорну"
  result: FoodSnackPopcorn
  time: 20
  solids:
    FoodCorn: 1

- type: microwaveMealRecipe
  id: RecipePancake
  name: "рецепт млинців"
  result: FoodBakedPancake
  time: 5
  reagents:
    Flour: 5
    Milk: 5
    Egg: 6

- type: microwaveMealRecipe
  id: RecipeBlueberryPancake
  name: "рецепт чорничних млинців"
  result: FoodBakedPancakeBb
  time: 5
  reagents:
    Flour: 5
    Milk: 5
    Egg: 6
  solids:
    FoodBerries: 2

- type: microwaveMealRecipe
  id: RecipeWaffles
  name: "рецепт вафель"
  result: FoodBakedWaffle
  time: 10
  reagents:
    Flour: 5
    Milk: 5
    Egg: 6
    SodaWater: 5

- type: microwaveMealRecipe
  id: RecipeWaffleSoy
  name: "рецепт соєвих вафель"
  result: FoodBakedWaffleSoy
  time: 10
  reagents:
    Flour: 5
    MilkSoy: 5
    Egg: 6
    SodaWater: 5

- type: microwaveMealRecipe
  id: RecipeCookie
  name: "рецепт печива"
  result: FoodBakedCookie
  time: 5
  reagents:
    Flour: 5
    Sugar: 5
  solids:
    FoodButterSlice: 1
    FoodSnackChocolateBar: 1

- type: microwaveMealRecipe
  id: RecipeSugarCookie
  name: "рецепт цукрового печива"
  result: FoodBakedCookieSugar
  time: 5
  reagents:
    Flour: 5
    Sugar: 10
  solids:
    FoodButterSlice: 1

- type: microwaveMealRecipe
  id: RecipeRaisinCookie
  name: "рецепт печива з родзинками"
  result: FoodBakedCookieRaisin
  time: 5
  reagents:
    Flour: 5
    Sugar: 5
  solids:
    FoodSnackRaisins: 1

- type: microwaveMealRecipe
  id: RecipeCookieOatmeal
  name: "рецепт вівсяного печива"
  result: FoodBakedCookieOatmeal
  time: 5
  reagents:
    Oats: 5
    Sugar: 5
  solids:
    FoodButterSlice: 1

- type: microwaveMealRecipe
  id: RecipeChocolateChipPancake
  name: "рецепт млинців з шоколадною крихтою"
  result: FoodBakedPancakeCc
  time: 5
  reagents:
    Flour: 5
    Milk: 5
    Egg: 6
  solids:
    FoodSnackChocolateBar: 1

- type: microwaveMealRecipe
  id: RecipeAppleCake
  name: "рецепт яблучного пирога"
  result: FoodCakeApple
  time: 5
  solids:
    FoodCakePlain: 1
    FoodApple: 3

- type: microwaveMealRecipe
  id: RecipeCarrotCake
  name: "рецепт морквяного пирога"
  result: FoodCakeCarrot
  time: 5
  solids:
    FoodCakePlain: 1
    FoodCarrot: 3

- type: microwaveMealRecipe
  id: RecipeLemonCake
  name: "рецепт лимонного пирога"
  result: FoodCakeLemon
  time: 5
  solids:
    FoodCakePlain: 1
    FoodLemon: 3

- type: microwaveMealRecipe
  id: RecipeLemoonCake
  name: "рецепт лимунового пирога"
  result: FoodCakeLemoon
  time: 5
  solids:
    FoodCakePlain: 1
    FoodLemoon: 2
    FoodBerries: 1 #dark colouring

- type: microwaveMealRecipe
  id: RecipeOrangeCake
  name: "рецепт апельсинового пирога"
  result: FoodCakeOrange
  time: 5
  solids:
    FoodCakePlain: 1
    FoodOrange: 3

- type: microwaveMealRecipe
  id: RecipeBlueberryCake
  name: "рецепт чорничного пирога"
  result: FoodCakeBlueberry
  time: 5
  solids:
    FoodCakePlain: 1
    FoodBerries: 3

- type: microwaveMealRecipe
  id: RecipeLimeCake
  name: "рецепт лаймового пирога"
  result: FoodCakeLime
  time: 5
  solids:
    FoodCakePlain: 1
    FoodLime: 3

- type: microwaveMealRecipe
  id: RecipeCheeseCake
  name: "рецепт сирного пирога"
  result: FoodCakeCheese
  time: 5
  reagents:
    Cream: 10
  solids:
    FoodCakePlain: 1
    FoodCheeseSlice: 3

- type: microwaveMealRecipe
  id: RecipePumpkinCake
  name: "рецепт гарбузового пирога"
  result: FoodCakePumpkin
  time: 5
  solids:
    FoodCakePlain: 1
    FoodPumpkin: 1

- type: microwaveMealRecipe
  id: RecipeClownCake
  name: "рецепт клоунського пирога"
  result: FoodCakeClown
  time: 5
  solids:
    ClothingMaskClown: 1
    FoodCakePlain: 1

- type: microwaveMealRecipe
  id: RecipeCake
  name: "рецепт пирога"
  result: FoodCakePlain
  time: 15
  solids:
    FoodCakeBatter: 1

- type: microwaveMealRecipe
  id: RecipeBirthdayCake
  name: "рецепт торта на день народження"
  result: FoodCakeBirthday
  time: 5
  reagents:
    Cream: 5
  solids:
    FoodCakePlain: 1

- type: microwaveMealRecipe
  id: RecipeChocolateCake
  name: "рецепт шоколадного пирога"
  result: FoodCakeChocolate
  time: 5
  solids:
    FoodCakePlain: 1
    FoodSnackChocolateBar: 2

- type: microwaveMealRecipe
  id: RecipeBrainCake
  name: "рецепт пирога з мізками"
  result: FoodCakeBrain
  time: 15
  solids:
    FoodCakePlain: 1
    OrganHumanBrain: 1

- type: microwaveMealRecipe
  id: RecipeSlimeCake
  name: "рецепт пирога зі слизу"
  result: FoodCakeSlime
  time: 5
  reagents:
    Slime: 15
  solids:
    FoodCakePlain: 1

- type: microwaveMealRecipe
  id: RecipeCatCake
  name: "рецепт котячого пирога"
  result: MobCatCake
  time: 15
  reagents:
    Milk: 15
    Cognizine: 5
  solids:
    FoodCakePlain: 1
    FoodSnackRaisins: 1
    OrganAnimalHeart: 1

- type: microwaveMealRecipe
  id: RecipeBreadDog
  name: "рецепт хлібного пса"
  result: MobBreadDog
  time: 15
  reagents:
    Cognizine: 5
  solids:
    FoodBreadSausage: 1
    OrganAnimalHeart: 1
    FoodSpaceshroomCooked: 1

- type: microwaveMealRecipe
  id: RecipeDumplings
  name: "рецепт дамплінгів"
  result: FoodBakedDumplings
  time: 15
  reagents:
    Water: 10
    UncookedAnimalProteins: 6
  solids:
    FoodDoughSlice: 3

- type: microwaveMealRecipe
  id: RecipeBrownies
  name: "рецепт брауні"
  result: FoodBakedBrownieBatch
  time: 25
  reagents:
    Flour: 15
    Sugar: 30
  solids:
    FoodButter: 2
    FoodSnackChocolateBar: 2
    FoodEgg: 3

- type: microwaveMealRecipe
  id: RecipeHarpyWings
  name: "рецепт крилець гарпії"
  result: FoodHarpyWings
  time: 15
  solids:
    LeftLegHarpy: 1
    RightLegHarpy: 1
 
#Donks i guess
- type: microwaveMealRecipe
  id: RecipeWarmDonkpocket
  name: "рецепт теплого донк-пакета"
  result: FoodDonkpocketWarm
  time: 5
  solids:
    FoodDonkpocket: 1

- type: microwaveMealRecipe
  id: RecipeWarmDankpocket
  name: "рецепт теплого данк-пакета"
  result: FoodDonkpocketDankWarm
  time: 5
  solids:
    FoodDonkpocketDank: 1

- type: microwaveMealRecipe
  id: RecipeWarmDonkpocketSpicy
  name: "рецепт теплого гострого донк-пакета"
  result: FoodDonkpocketSpicyWarm
  time: 5
  solids:
    FoodDonkpocketSpicy: 1

- type: microwaveMealRecipe
  id: RecipeWarmDonkpocketTeriyaki
  name: "рецепт теплого теріякі-пакета"
  result: FoodDonkpocketTeriyakiWarm
  time: 5
  solids:
    FoodDonkpocketTeriyaki: 1

- type: microwaveMealRecipe
  id: RecipeWarmDonkpocketPizza
  name: "рецепт теплого піца-пакета"
  result: FoodDonkpocketPizzaWarm
  time: 5
  solids:
    FoodDonkpocketPizza: 1

- type: microwaveMealRecipe
  id: RecipeDonkpocketHonk
  name: "рецепт теплого хонк-пакета"
  result: FoodDonkpocketHonkWarm
  time: 5
  solids:
    FoodDonkpocketHonk: 1

- type: microwaveMealRecipe
  id: RecipeDonkpocketBerry
  name: "рецепт теплого ягідного пакета"
  result: FoodDonkpocketBerryWarm
  time: 5
  solids:
    FoodDonkpocketBerry: 1

- type: microwaveMealRecipe
  id: RecipeDonkpocketStonk
  name: "рецепт теплого стонк-пакета"
  result: FoodDonkpocketStonkWarm
  time: 5
  solids:
    FoodDonkpocketStonk: 1

- type: microwaveMealRecipe
  id: RecipeDonkpocketCarp
  name: "рецепт теплого карп-пакета"
  result: FoodDonkpocketCarpWarm
  time: 5
  solids:
    FoodDonkpocketCarp: 1


- type: microwaveMealRecipe
  id: RecipeHotChili
  name: "рецепт гострого чилі"
  result: FoodSoupChiliHot
  time: 20
  solids:
    FoodBowlBig: 1
    FoodChiliPepper: 1
    FoodMeatCutlet: 1
    FoodOnionSlice: 1
    FoodTomato: 1

- type: microwaveMealRecipe
  id: RecipeColdChili
  name: "рецепт холодного чилі"
  result: FoodSoupChiliCold
  time: 5
  reagents:
    Nitrogen: 5
  solids:
    FoodSoupChiliHot: 1

- type: microwaveMealRecipe
  id: RecipeClownTears
  name: "рецепт сліз клоуна"
  result: FoodSoupClown
  time: 15
  solids:
    FoodBowlBig: 1
    FoodOnionSlice: 1
    FoodTomato: 1
    BikeHorn: 1

- type: microwaveMealRecipe
  id: RecipeChiliClown
  name: "рецепт чилі кон карнавал"
  result: FoodSoupChiliClown
  time: 30
  solids:
    FoodBowlBig: 1
    FoodChiliPepper: 1
    FoodMeatCutlet: 1
    FoodOnionSlice: 1
    FoodTomato: 1
    ClothingShoesClown: 1

- type: microwaveMealRecipe
  id: RecipeQueso
  name: "рецепт кесо"
  result: FoodMealQueso
  time: 15
  #todo Add blackpepper
  #reagents:
    #blackpepper: 5
  solids:
    FoodChiliPepper: 1
    FoodCheeseSlice: 2

- type: microwaveMealRecipe
  id: RecipeRibs
  name: "рецепт реберець барбекю"
  result: FoodMealRibs
  time: 15
  reagents:
    BbqSauce: 5
  solids:
    FoodMeat: 2
    FoodKebabSkewer: 1

- type: microwaveMealRecipe
  id: RecipeEnchiladas
  name: "рецепт енчіладас"
  result: FoodMealEnchiladas
  time: 20
  solids:
    FoodChiliPepper: 2
    FoodMeatCutlet: 1
    FoodCorn: 1

# SALADS: These should be moved out of the microwave as soon as possible
- type: microwaveMealRecipe
  id: RecipeHerbSalad
  name: "рецепт трав'яного салату"
  result: FoodSaladHerb
  time: 5
  solids:
    FoodBowlBig: 1
    FoodAmbrosiaVulgaris: 3
    FoodApple: 1

- type: microwaveMealRecipe
  id: RecipeValidSalad
  name: "рецепт валід салату"
  result: FoodSaladValid
  time: 5
  solids:
    FoodBowlBig: 1
    FoodAmbrosiaVulgaris: 3
    FoodPotato: 1
    FoodMeatMeatball: 1

- type: microwaveMealRecipe
  id: RecipeColeslaw
  name: "рецепт салату коулсло"
  result: FoodSaladColeslaw
  time: 5
  reagents:
    Vinaigrette: 5
  solids:
    FoodBowlBig: 1
    FoodOnionRed: 1
    FoodCabbage: 1

- type: microwaveMealRecipe
  id: RecipeCaesarSalad
  name: "рецепт салату Цезар"
  result: FoodSaladCaesar
  time: 5
  reagents:
    OilOlive: 5
  solids:
    FoodBowlBig: 1
    FoodOnionRedSlice: 1
    FoodBreadPlainSlice: 1
    FoodCheeseSlice: 1
    FoodCabbage: 1

- type: microwaveMealRecipe
  id: RecipeCitrusSalad
  name: "рецепт цитрусового салату"
  result: FoodSaladCitrus
  time: 5
  solids:
    FoodBowlBig: 1
    FoodOrange: 1
    FoodLemon: 1
    FoodLime: 1

- type: microwaveMealRecipe
  id: RecipeKimchiSalad
  name: "рецепт салату кімчі"
  result: FoodSaladKimchi
  time: 5
  reagents:
    Vinegar: 5
  solids:
    FoodBowlBig: 1
    FoodCarrot: 1
    FoodCabbage: 1
    FoodGarlic: 1

- type: microwaveMealRecipe
  id: RecipeFruitSalad
  name: "рецепт фруктового салату"
  result: FoodSaladFruit
  time: 5
  solids:
    FoodBowlBig: 1
    FoodOrange: 1
    FoodApple: 1
    FoodGrape: 1
    FoodWatermelonSlice: 2

- type: microwaveMealRecipe
  id: RecipeJungleSalad
  name: "рецепт салату джунглі"
  result: FoodSaladJungle
  time: 5
  solids:
    FoodBowlBig: 1
    FoodBanana: 1
    FoodApple: 1
    FoodGrape: 1
    FoodWatermelonSlice: 2

- type: microwaveMealRecipe
  id: RecipeWatermelonFruitBowlSalad
  name: "рецепт фруктової миски з кавуна"
  result: FoodSaladWatermelonFruitBowl
  time: 5
  solids:
    FoodWatermelon: 1
    FoodApple: 1
    FoodBanana: 1
    FoodLemon: 1
    FoodOrange: 1
    FoodAmbrosiaVulgaris: 1

# NOT ACTUAL FOOD

- type: microwaveMealRecipe
  id: RecipeDriedTobacco
  name: "рецепт сушеного листя тютюну"
  result: LeavesTobaccoDried
  time: 10
  solids:
    LeavesTobacco: 1

- type: microwaveMealRecipe
  id: RecipeDriedCannabis
  name: "рецепт сушеного листя канабісу"
  result: LeavesCannabisDried
  time: 10
  solids:
    LeavesCannabis: 1

- type: microwaveMealRecipe
  id: RecipeDriedCannabisRainbow
  name: "рецепт сушеного листя райдужної коноплі"
  result: LeavesCannabisRainbowDried
  time: 10
  solids:
    LeavesCannabisRainbow: 1

- type: microwaveMealRecipe
  id: RecipeTrashBakedBananaPeel
  name: "рецепт печеної шкірки банана"
  result: TrashBakedBananaPeel
  time: 5
  solids:
    TrashBananaPeel: 1

# Suppermatter
- type: microwaveMealRecipe
  id: RecipeSuppermatter
  name: "рецепт суперматерії"
  result: FoodCakeSuppermatter
  time: 30
  solids:
    FoodCakeBatter: 2
  reagents:
    Sugar: 30
    Nitrogen: 10
    Plasma: 10

- type: microwaveMealRecipe
  id: RecipeFoodBakedChevreChaud
  name: "рецепт печеного шевр шо"
  result: FoodBakedChevreChaud
  time: 5
  solids:
    FoodChevreSlice: 1
    FoodBreadBaguetteSlice: 1

- type: microwaveMealRecipe
  id: RecipeCookedSpaceshroom
  name: "рецепт приготованих космічних грибів"
  result: FoodSpaceshroomCooked
  time: 5
  solids:
    FoodSpaceshroom: 1

- type: microwaveMealRecipe
  id: RecipeCannabisButter
  name: "рецепт масла з канабісу"
  result: FoodCannabisButter
  time: 15
  solids:
    FoodButter: 1
    LeavesCannabis: 6

- type: microwaveMealRecipe
  id: RecipeCannabisBrownies
  name: "рецепт брауні з канабісом"
  result: FoodBakedCannabisBrownieBatch
  time: 25
  reagents:
    Flour: 15
    Sugar: 30
  solids:
    FoodCannabisButter: 2
    FoodSnackChocolateBar: 2
    FoodEgg: 3

- type: microwaveMealRecipe
  id: RecipeCornInButter
  name: "рецепт кукурудзи в маслі"
  result: FoodMealCornInButter
  time: 10
  solids:
    FoodCorn: 1
    FoodPlate: 1
    FoodButter: 1

- type: microwaveMealRecipe
  id: RecipePeaSoup
  name: "рецепт горохового супу"
  result: FoodSoupPea
  time: 10
  solids:
    FoodPeaPod: 2
    FoodBowlBig: 1
  reagents:
    Water: 10

- type: microwaveMealRecipe
  id: RecipeTacoShell
  name: "рецепт тако-шелл"
  result: FoodTacoShell
  time: 5
  solids:
    FoodDoughTortillaFlat: 1 # one third of a standard bread dough recipe

- type: microwaveMealRecipe
  id: RecipeTacoBeef
  name: "рецепт яловичого тако"
  result: FoodTacoBeef
  time: 10
  solids:
    FoodTacoShell: 1
    FoodMeatCutlet: 1
    FoodCheeseSlice: 1

- type: microwaveMealRecipe
  id: RecipeTacoChicken
  name: "рецепт курячого тако"
  result: FoodTacoChicken
  time: 10
  solids:
    FoodTacoShell: 1
    FoodMeatChickenCutlet: 1
    FoodCheeseSlice: 1

- type: microwaveMealRecipe
  id: RecipeTacoFish
  name: "рецепт рибного тако"
  result: FoodTacoFish
  time: 10
  solids:
    FoodTacoShell: 1
    FoodMeatFish: 1
    FoodOnionSlice: 2
    FoodTomato: 1
    FoodCabbage: 1

- type: microwaveMealRecipe
  id: RecipeTacoRat
  name: "рецепт щурячого тако"
  result: FoodTacoRat
  time: 10
  solids:
    FoodTacoShell: 1
    FoodCheeseSlice: 1
    FoodMeatRat: 1

- type: microwaveMealRecipe
  id: RecipeTacoBeefSupreme
  name: "рецепт яловичого тако супрім"
  result: FoodTacoBeefSupreme
  time: 10
  solids:
    FoodTacoShell: 1
    FoodCheeseSlice: 1
    FoodMeatCutlet: 1
    FoodTomato: 1
    FoodCabbage: 1
    FoodOnionSlice: 2

- type: microwaveMealRecipe
  id: RecipeTacoChickenSupreme
  name: "рецепт яловичого тако супрім"
  result: FoodTacoChickenSupreme
  time: 10
  solids:
    FoodTacoShell: 1
    FoodCheeseSlice: 1
    FoodMeatChickenCutlet: 1
    FoodTomato: 1
    FoodCabbage: 1
    FoodOnionSlice: 2

- type: microwaveMealRecipe
  id: RecipeCroissant
  name: "рецепт круасанів"
  result: FoodBakedCroissant
  time: 5
  solids:
    FoodCroissantRaw: 1
    FoodButterSlice: 1

- type: microwaveMealRecipe
  id: RecipeThrowingCroissant
  name: "рецепт кидання круасанів"
  result: WeaponCroissant
  secretRecipe: true
  time: 5
  solids:
    FoodCroissantRaw: 1
    FoodButterSlice: 1
    ShardGlass: 1