- type: job
  id: ChiefJustice
  name: job-name-chief-justice
  description: job-description-chief-justice
  playTimeTracker: JobChiefJustice
  weight: 20
  startingGear: CJGear
  icon: "JobIconChiefJustice"
  requireAdminNotify: true
  supervisors: job-supervisors-captain
  canBeAntag: false
  requirements:
    - !type:CharacterPlaytimeRequirement
      tracker: JobClerk
      min: 36000 # 10 hours
    - !type:CharacterPlaytimeRequirement
      tracker: JobLawyer
      min: 36000 # 10 hours
    - !type:CharacterPlaytimeRequirement
      tracker: JobProsecutor
      min: 36000 # 10 hours
    - !type:CharacterOverallTimeRequirement
      min: 90000 # 25 hours
    - !type:CharacterWhitelistRequirement # whitelist requirement because I don't want any dingus judges
    - !type:CharacterEmployerRequirement
      employers:
      - IdrisIncorporated
      - NanoTrasen
  access:
  - Command
  - ChiefJustice
  - Justice
  - Lawyer
  - Prosecutor
  - Clerk
  - Security
  - Maintenance
  - External
  special:
  - !type:AddImplantSpecial
    implants: [ MindShieldImplant ]
  - !type:AddComponentSpecial
    components:
      - type: CommandStaff

- type: startingGear
  id: CJGear
  subGear:
  - PassengerPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitChiefJustice
    back: ClothingBackpackFilled # TODO- make Justice department bags
    shoes: ClothingShoesLeather
    head: ClothingHeadHatCJToque
    outerClothing: ClothingOuterChiefJustice
    id: ChiefJusticePDA
    ears: ClothingHeadsetAltJustice
    gloves: ClothingHandsGlovesColorWhite
    # Todo - pocket1: Gavel
  innerClothingSkirt: ClothingUniformJumpskirtChiefJustice
  satchel: ClothingBackpackSatchelFilled # TODO- make Justice departmebt bags
  duffelbag: ClothingBackpackDuffelFilled


