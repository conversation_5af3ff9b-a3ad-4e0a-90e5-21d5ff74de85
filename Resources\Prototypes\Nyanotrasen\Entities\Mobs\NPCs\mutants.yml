- type: entity
  name: "помідор-вбивця"
  id: MobTomatoKiller
  parent: SimpleSpaceMobBase
  description: "Це дійсно дозволить вам залучити кількох веганів до наступної онлайн-дискусії."
  components:
  - type: InputMover
  - type: MobMover
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
  - type: NpcFactionMember
    factions:
      - SimpleHostile
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: produce
      sprite: Nyanotrasen/Mobs/Mutants/killer_tomato.rsi
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.5
        density: 63
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: MobState
  - type: MobThresholds
    thresholds:
      0: Alive
      60: Dead
  - type: Appearance
  - type: DamageStateVisuals
    states:
      Alive:
        Base: produce
      Critical:
        Base: produce_dead
      Dead:
        Base: produce_dead
  - type: Butcherable
    spawned:
    - id: FoodMeatTomato
      amount: 3
    - id: KillerTomatoSeeds
      amount: 1
  - type: Bloodstream
    bloodReagent: DemonsBlood
    bloodMaxVolume: 100
  - type: CombatMode
  - type: Temperature
    heatDamageThreshold: 500
    coldDamageThreshold: 200
  - type: MeleeWeapon
    hidden: true
    animation: WeaponArcBite
    soundHit:
      path: /Audio/Effects/bite.ogg
    damage:
      types:
        Piercing: 3
        Slash: 5
  - type: ReplacementAccent
    accent: genericAggressive
  - type: Produce
  - type: NoSlip

- type: entity
  name: "онейрофаг"
  parent: MobGiantSpider
  id: MobGiantSpiderVampire
  description: "Павук-\"пожирач снів\", який, за чутками, є одним з потенційних генетичних джерел павука-русака."
  components:
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base", "movement"]
      state: viper
      sprite: Mobs/Animals/spider.rsi
  - type: SpriteMovement
    movementLayers:
      movement:
        state: viper-moving
    noMovementLayers:
      movement:
        state: viper
  - type: Appearance
  - type: DamageStateVisuals
    states:
      Alive:
        Base: viper
      Critical:
        Base: viper_dead
      Dead:
        Base: viper_dead
  - type: ReplacementAccent
    accent: xeno
  - type: InteractionPopup
    successChance: 0.5
    interactSuccessString: petting-success-tarantula
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/snake_hiss.ogg
  - type: Puller
    needsHands: false
  - type: Cocooner
    cocoonDelay: 8
  - type: SolutionContainerManager
    solutions:
      melee:
        reagents:
        - ReagentId: Nocturine
          Quantity: 20
  - type: MeleeChemicalInjector
    solution: melee
    transferAmount: 3.5
  - type: SolutionRegeneration
    solution: melee
    generated:
      reagents:
        - ReagentId: Nocturine
          Quantity: 0.15
  - type: BloodSucker
    unitsToSucc: 35
    injectWhenSucc: true
    injectReagent: Cryptobiolin
    unitsToInject: 10
    webRequired: true
  - type: Bloodstream
    bloodReagent: DemonsBlood
  - type: Body
    prototype: VampiricAnimalLarge
  - type: Psionic
    removable: false
  - type: InnatePsionicPowers
    powersToAdd:
      - MetapsionicPower
      - PsionicInvisibilityPower
  - type: AntiPsionicWeapon
    punish: false
    modifiers:
      coefficients:
        Piercing: 2.25
  - type: Damageable
    damageContainer: HalfSpirit
    damageModifierSet: HalfSpirit
  - type: StatusEffects
    allowed:
      - Stun
      - KnockedDown
      - SlowedDown
      - Stutter
      - SeeingRainbows
      - Electrocution
      - Drunk
      - SlurredSpeech
      - PressureImmunity
      - Muted
      - ForcedSleep
      - TemporaryBlindness
      - Pacified
      - PsionicsDisabled
      - PsionicallyInsulated
  - type: Tag
    tags:
      - Oneirophage
  - type: MovementAlwaysTouching
  - type: PsionicInvisibleContacts
    whitelist:
      tags:
        - ArachneWeb

- type: entity
  name: "онейрофаг"
  parent: MobGiantSpiderVampire
  id: MobGiantSpiderVampireAngry
  suffix: Angry
  components:
    - type: NpcFactionMember
      factions:
        - SimpleHostile
    - type: InputMover
    - type: MobMover
    - type: HTN
      rootTask:
        task: SimpleHostileCompound
    - type: GhostRole
      makeSentient: true
      name: ghost-role-information-giant-spider-vampire-name
      description: ghost-role-information-giant-spider-vampire-description
      rules: No antagonist restrictions. Just don't talk in emote; you have telepathic chat.
    - type: GhostTakeoverAvailable
