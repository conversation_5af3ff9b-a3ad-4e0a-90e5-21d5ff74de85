<controls:FancyWindow
        xmlns="https://spacestation14.io"
        xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
        Title="{Loc 'autodoc-title'}">
    <BoxContainer Orientation="Vertical">
        <ScrollContainer MinWidth="600" MinHeight="400" VerticalExpand="True">
            <BoxContainer Name="Programs" Orientation="Vertical"/> <!-- Populated at runtime -->
        </ScrollContainer>
        <BoxContainer Orientation="Horizontal" Margin="5 5 5 5" Align="Center">
            <Button Name="CreateProgramButton" StyleClasses="OpenRight" Text="{Loc 'autodoc-create-program'}"/>
            <Button Name="AbortButton" StyleClasses="OpenLeft" Text="{Loc 'autodoc-abort-program'}" Disabled="True"/>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
