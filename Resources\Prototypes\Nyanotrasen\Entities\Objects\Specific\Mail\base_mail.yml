- type: entity
  parent: BaseItem
  abstract: true
  id: BaseMail
  name: mail-item-name-unaddressed
  components:
  - type: Item
#    size: Normal # Frontier
    storedRotation: -90
  - type: Mail
  - type: AccessReader
  - type: Sprite
    scale: 0.7, 0.7 # Frontier
    sprite: Objects/Specific/Mail/mail.rsi
    layers:
    - state: icon
      map: ["enum.MailVisualLayers.Icon"]
    - state: postmark
    - state: fragile
      map: ["enum.MailVisualLayers.FragileStamp"]
      visible: false
    - map: ["enum.MailVisualLayers.JobStamp"]
      scale: 0.8, 0.8 # Frontier 0.5<0.8
      offset: 0.225, 0.165 # Frontier (0.275, 0.2)<(0.225, 0.165)
    - state: locked
      map: ["enum.MailVisualLayers.Lock"]
    - state: priority
      map: ["enum.MailVisualLayers.PriorityTape"]
      visible: false
      shader: unshaded
    - state: broken
      map: ["enum.MailVisualLayers.Breakage"]
      visible: false
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.MailVisuals.IsTrash:
        enum.MailVisualLayers.Icon:
          True:
            state: trash
          False:
            state: icon
      enum.MailVisuals.IsLocked:
        enum.MailVisualLayers.Lock:
          True:
            visible: true
          False:
            visible: false
      enum.MailVisuals.IsFragile:
        enum.MailVisualLayers.FragileStamp:
          True:
            visible: true
          False:
            visible: false
      enum.MailVisuals.IsPriority:
        enum.MailVisualLayers.PriorityTape:
          True:
            visible: true
          False:
            visible: false
      enum.MailVisuals.IsPriorityInactive:
        enum.MailVisualLayers.PriorityTape:
          True:
            shader: shaded
            state: priority_inactive
          False:
            shader: unshaded
            state: priority
      enum.MailVisuals.IsBroken:
        enum.MailVisualLayers.Breakage:
          True:
            visible: true
          False:
            visible: false
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 5
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Breakage" ]
    - trigger:
        !type:DamageTrigger
        damage: 40
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Speech
  - type: DamageOnLand
    damage:
      types:
        Blunt: 10
  - type: CargoSellBlacklist # Frontier
  - type: Food # Frontier - Moth food
    requiresSpecialDigestion: true
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 1
        reagents:
        - ReagentId: Nothing
          Quantity: 1
  - type: LanguageSpeaker
  - type: LanguageKnowledge
    speaks: [TauCetiBasic] # So that them foreigners can't understand what the broken mail is saying
    understands: []

# This empty parcel is allowed to exist and evade the tests for the admin
# mailto command.
- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailAdminFun
  suffix: adminfun
