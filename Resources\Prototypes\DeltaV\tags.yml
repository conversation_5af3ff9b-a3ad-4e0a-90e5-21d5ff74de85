## This is for Nyano and Delta V tags
- type: Tag
  id: AllowLamiaHardsuit

- type: Tag
  id: Bayonet #Craftable Musket

- type: Tag
  id: BeltSlotNotBelt #not a 'belt'

- type: Tag
  id: BulletBB

- type: Tag
  id: CartridgeSpecial # For the .38 special ammo and revolver

- type: Tag
  id: CartridgeMusket #For the Craftable Musket

- type: Tag
  id: Directional

- type: Tag
  id: DockShipyard

- type: Tag
  id: ForensicBeltEquip

- type: Tag
  id: Gavel

- type: Tag
  id: GasPipeHalf #Craftable Musket

- type: Tag
  id: HandLabeler

- type: Tag
  id: HidesHarpyWings

- type: Tag
  id: MagazinePistolSpecial # For the .38 special ammo and pistol

- type: Tag
  id: ModularBreech #Craftable Musket

- type: Tag
  id: ModularTrigger #Craftable Musket

- type: Tag
  id: SecDogWearable # allows <PERSON><PERSON> to wear meson goggles (and more later?)

- type: Tag
  id: SpeedLoaderSpecial

- type: Tag
  id: PreventLabel

- type: Tag
  id: BionicSyrinxImplant

- type: Tag
  id: PaperSlip

- type: Tag
  id: HudMedicalSecurity #Craftable Corpsman Glasses

- type: Tag
  id: Skirt
