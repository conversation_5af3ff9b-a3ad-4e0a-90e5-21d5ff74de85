signal-port-name-autoclose = Autoclose
signal-port-description-autoclose = Toggles whether the device should automatically close.

signal-port-name-toggle = Toggle
signal-port-description-toggle = Toggles the state of a device.

signal-port-name-on-receiver = On
signal-port-description-on-receiver = Turns a device on.

signal-port-name-off-receiver = Off
signal-port-description-off-receiver = Turns a device off.

signal-port-name-forward = Forward
signal-port-description-forward = Makes a device (e.g. conveyer) operate in the normal direction.

signal-port-name-reverse = Reverse
signal-port-description-reverse = Makes a device (e.g. conveyer) operate in the reverse direction.

signal-port-name-open = Open
signal-port-description-open = Opens a device.

signal-port-name-close = Close
signal-port-description-close = Closes a device.

signal-port-name-doorbolt = Door bolt
signal-port-description-doorbolt = Bolts door when HIGH.

signal-port-name-trigger = Trigger
signal-port-description-trigger = Triggers some mechanism on the device.

signal-port-name-order-sender = Order sender
signal-port-description-order-sender = Cargo console order sender

signal-port-name-order-receiver = Order receiver
signal-port-description-order-receiver = Cargo console order receiver

signal-port-name-pressurize = Pressurize
signal-port-description-pressurize = Causes the device to starts releasing air until some target pressure is reached.

signal-port-name-depressurize = Depressurize
signal-port-description-depressurize = Causes the device to starts siphoning air until some target pressure is reached.

signal-port-name-pod-sender = Cloning pod
signal-port-description-pod-sender = Cloning pod signal sender

signal-port-name-pod-receiver = Cloning pod
signal-port-description-pod-receiver = Cloning pod signal receiver

signal-port-name-med-scanner-sender = Medical scanner
signal-port-description-med-scanner-sender = Medical scanner signal sender

signal-port-name-med-scanner-receiver = Medical scanner
signal-port-description-med-scanner-receiver = Medical scanner signal receiver

signal-port-name-hold-open = Hold
signal-port-description-hold-open = Turns off automatic closing.

signal-port-name-artifact-analyzer-sender = Console
signal-port-description-artifact-analyzer-sender = Analysis console signal sender

signal-port-name-artifact-analyzer-receiver = Pad
signal-port-description-artifact-analyzer-receiver = Artifact analyzer signal receiver

signal-port-name-set-particle-delta = Set particle type: delta
signal-port-description-set-particle-delta = Sets the type of particle this device emits to delta.

signal-port-name-set-particle-epsilon = Set particle type: epsilon
signal-port-description-set-particle-epsilon = Sets the type of particle this device emits to epsilon.

signal-port-name-set-particle-zeta = Set particle type: zeta
signal-port-description-set-particle-zeta = Sets the type of particle this device emits to zeta.

signal-port-name-set-particle-sigma = Set particle type: sigma
signal-port-description-set-particle-sigma = Sets the type of particle this device emits to sigma.

signal-port-name-logic-input-a = Input A
signal-port-description-logic-input-a = First input of a logic gate.

signal-port-name-logic-input-b = Input B
signal-port-description-logic-input-b = Second input of a logic gate.

signal-port-name-logic-input = Input
signal-port-description-logic-input = Input to the edge detector, cannot be a pulse signal.

signal-port-name-material-silo-utilizer = Silo utilizer
signal-port-description-material-silo-utilizer = Utilizer for station material silo

signal-port-name-fill-any-item = Fillbot Target
signal-port-description-fill-any-item = Target port for Fillbot insertion.
