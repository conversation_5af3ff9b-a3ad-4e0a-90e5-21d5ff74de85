handcuff-component-target-self = You start restraining yourself.
handcuff-component-cuffs-broken-error = The restraints are broken!
handcuff-component-target-has-no-hands-error = {$targetName} has no hands!
handcuff-component-target-has-no-free-hands-error = {$targetName} has no free hands!
handcuff-component-too-far-away-error = You are too far away to use the restraints!
handcuff-component-target-flying-error = You cannot reach {$targetName}'s hands!
handcuff-component-start-cuffing-observer = {$user} starts restraining {$target}!
handcuff-component-start-cuffing-target-message = You start restraining {$targetName}.
handcuff-component-start-cuffing-by-other-message = {$otherName} starts restraining you!
handcuff-component-cuff-observer-success-message = {$user} restrains {$target}.
handcuff-component-cuff-other-success-message = You successfully restrain {$otherName}.
handcuff-component-cuff-by-other-success-message = You have been restrained by {$otherName}!
handcuff-component-cuff-self-success-message = You restrain yourself.
handcuff-component-cuff-interrupt-message = You were interrupted while restraining {$targetName}!
handcuff-component-cuff-interrupt-other-message = You interrupt {$otherName} while they are restraining you!
handcuff-component-cuff-interrupt-self-message = You were interrupted while restraining yourself.
handcuff-component-cuff-interrupt-buckled-message = You can't buckle while restrained!
handcuff-component-cuff-interrupt-unbuckled-message = You can't unbuckle while restrained!
