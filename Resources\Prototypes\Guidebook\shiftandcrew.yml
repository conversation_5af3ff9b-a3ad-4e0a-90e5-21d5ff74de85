- type: guideEntry
  id: Jobs
  name: guide-entry-jobs
  text: "/ServerInfo/Guidebook/Jobs.xml"
  children:
  - Bartender
  - Chef
  - Botany
  - Engineering
  - Science
  - Security
  - Janitorial
  - Cargo
  - Salvage
  - Medical

- type: guideEntry
  id: Survival
  name: guide-entry-survival
  text: "/ServerInfo/Guidebook/Survival.xml"

- type: guideEntry
  id: Janitorial
  name: guide-entry-janitorial
  text: "/ServerInfo/Guidebook/Service/Janitorial.xml"

- type: guideEntry
  id: Bartender
  name: guide-entry-bartender
  text: "/ServerInfo/Guidebook/Service/Bartender.xml"
  filterEnabled: True

- type: guideEntry
  id: Chef
  name: guide-entry-chef
  text: "/ServerInfo/Guidebook/Service/Chef.xml"
  children:
  - Food Recipes

- type: guideEntry
  id: Food Recipes
  name: guide-entry-foodrecipes
  text: "/ServerInfo/Guidebook/Service/FoodRecipes.xml"
  filterEnabled: true
