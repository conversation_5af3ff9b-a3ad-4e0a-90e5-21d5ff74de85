<Control xmlns="https://spacestation14.io"
         xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
         xmlns:customControls="clr-namespace:Content.Client.Administration.UI.CustomControls;assembly=Content.Client"
         Margin="5"
         MinWidth="450"
         VerticalExpand="True"
         HorizontalExpand="True">
    <BoxContainer Orientation="Vertical"
            VerticalExpand="True"
            HorizontalExpand="True">
        <BoxContainer
            Name="InfoContainer"
            Orientation="Vertical"
            VerticalExpand="True"
            HorizontalExpand="True"
            Margin="5"
            SizeFlagsStretchRatio="3">
            <BoxContainer Orientation="Horizontal"
                    HorizontalExpand="True">
                <BoxContainer Orientation="Horizontal"
                        HorizontalExpand="True"
                        SizeFlagsStretchRatio="1">
                    <TextureRect Name="DisciplineTexture"
                            TextureScale="7 7"
                            VerticalAlignment="Center"
                            Margin="4"/>
                    <TextureRect Name="TechnologyTexture"
                                 TextureScale="2 2"
                                 VerticalAlignment="Center"
                                 Margin="4"/>
                </BoxContainer>
                <Control HorizontalExpand="True"
                         SizeFlagsStretchRatio="2"/>
            </BoxContainer>
            <Label Name="TechnologyNameLabel"
                    StyleClasses="LabelKeyText"
                    HorizontalExpand="True"/>
            <RichTextLabel Name="TechnologyCostLabel"
                    HorizontalExpand="True"/>
            <Button Name="ResearchButton"
                    Text="{Loc 'research-console-menu-server-research-button'}"
                    HorizontalAlignment="Left"
                    Margin="0 4"/>
        </BoxContainer>
        <Control SizeFlagsStretchRatio="1"
                 HorizontalExpand="True"/>
        <BoxContainer
            Orientation="Vertical"
            VerticalExpand="True"
            Margin="5"
            SizeFlagsStretchRatio="5">
            <RichTextLabel Name="NoPrereqLabel" HorizontalAlignment="Center" Text="{Loc 'research-console-no-tech-requirements'}" Visible="True"/>
            <BoxContainer Name="PrereqsContainer"
                          Orientation="Vertical"
                          VerticalExpand="True"
                          Margin="5"
                          SizeFlagsStretchRatio="1"
                          Visible="False">
                <Label Text="{Loc 'research-console-required-text'}"
                        HorizontalAlignment="Center"/>
                <customControls:HSeparator StyleClasses="LowDivider"
                        Margin="0 0 0 10"/>
                <PanelContainer VerticalExpand="True">
                    <PanelContainer.PanelOverride>
                        <gfx:StyleBoxFlat BackgroundColor="#1B1B1E"/>
                    </PanelContainer.PanelOverride>
                    <ScrollContainer
                        HScrollEnabled="False"
                        HorizontalExpand="True"
                        VerticalExpand="True">
                        <BoxContainer
                            Name="RequiredTechContainer"
                            Orientation="Vertical"
                            VerticalExpand="True">
                        </BoxContainer>
                    </ScrollContainer>
                </PanelContainer>
            </BoxContainer>
            <BoxContainer
                Name="RecipesContainer"
                Orientation="Vertical"
                VerticalExpand="True"
                SizeFlagsStretchRatio="2"
                Margin="5">
                <Label Text="{Loc 'research-console-unlocks-text'}"
                        HorizontalAlignment="Center"/>
                <customControls:HSeparator StyleClasses="LowDivider"
                        Margin="0 0 0 10"/>
                <PanelContainer VerticalExpand="True">
                    <PanelContainer.PanelOverride>
                        <gfx:StyleBoxFlat BackgroundColor="#1B1B1E"/>
                    </PanelContainer.PanelOverride>
                    <ScrollContainer
                        HScrollEnabled="False"
                        HorizontalExpand="True"
                        VerticalExpand="True">
                        <BoxContainer
                            Name="UnlocksContainer"
                            Orientation="Vertical"
                            VerticalExpand="True">
                        </BoxContainer>
                    </ScrollContainer>
                </PanelContainer>
            </BoxContainer>
        </BoxContainer>
    </BoxContainer>
</Control>
