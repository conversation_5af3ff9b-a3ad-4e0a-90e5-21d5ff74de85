card-examined = This is the {$target}.
cards-verb-shuffle = Shuffle
card-verb-shuffle-success = Cards shuffled
cards-verb-draw = Draw card
cards-verb-flip = Flip cards
card-verb-join = Join cards
card-verb-organize-success = Cards flipped face { $facedown ->
    [true]   down
    *[false] up
}
cards-verb-organize-up = Flip cards face up
cards-verb-organize-down = Flip cards face down
cards-verb-pickcard = Pick a card
card-stack-examine = { $count ->
    [one] There is {$count} card in this stack.
    *[other] There are {$count} cards in this stack.
}
cards-stackquantitychange-added = Card was added (Total cards: {$quantity})
cards-stackquantitychange-removed = Card was removed (Total cards: {$quantity})
cards-stackquantitychange-joined = Stack was merged (Total cards: {$quantity})
cards-stackquantitychange-split = Stack was split (Total cards: {$quantity})
cards-stackquantitychange-unknown = Stack count changed (Total cards: {$quantity})
cards-verb-convert-to-deck = Convert to deck
cards-verb-split = Split in half

card-base-name = card
card-deck-name = deck of cards

card-sc-2-clubs = 2 of clubs
card-sc-3-clubs = 3 of clubs
card-sc-4-clubs = 4 of clubs
card-sc-5-clubs = 5 of clubs
card-sc-6-clubs = 6 of clubs
card-sc-7-clubs = 7 of clubs
card-sc-8-clubs = 8 of clubs
card-sc-9-clubs = 9 of clubs
card-sc-10-clubs = 10 of clubs
card-sc-ace-clubs = ace of clubs
card-sc-jack-clubs = jack of clubs
card-sc-king-clubs = king of clubs
card-sc-queen-clubs = queen of clubs

card-sc-2-diamonds = 2 of diamonds
card-sc-3-diamonds = 3 of diamonds
card-sc-4-diamonds = 4 of diamonds
card-sc-5-diamonds = 5 of diamonds
card-sc-6-diamonds = 6 of diamonds
card-sc-7-diamonds = 7 of diamonds
card-sc-8-diamonds = 8 of diamonds
card-sc-9-diamonds = 9 of diamonds
card-sc-10-diamonds = 10 of diamonds
card-sc-ace-diamonds = ace of diamonds
card-sc-jack-diamonds = jack of diamonds
card-sc-king-diamonds = king of diamonds
card-sc-queen-diamonds = queen of diamonds

card-sc-2-hearts = 2 of hearts
card-sc-3-hearts = 3 of hearts
card-sc-4-hearts = 4 of hearts
card-sc-5-hearts = 5 of hearts
card-sc-6-hearts = 6 of hearts
card-sc-7-hearts = 7 of hearts
card-sc-8-hearts = 8 of hearts
card-sc-9-hearts = 9 of hearts
card-sc-10-hearts = 10 of hearts
card-sc-ace-hearts = ace of hearts
card-sc-jack-hearts = jack of hearts
card-sc-king-hearts = king of hearts
card-sc-queen-hearts = queen of hearts

card-sc-2-spades = 2 of spades
card-sc-3-spades = 3 of spades
card-sc-4-spades = 4 of spades
card-sc-5-spades = 5 of spades
card-sc-6-spades = 6 of spades
card-sc-7-spades = 7 of spades
card-sc-8-spades = 8 of spades
card-sc-9-spades = 9 of spades
card-sc-10-spades = 10 of spades
card-sc-ace-spades = ace of spades
card-sc-jack-spades = jack of spades
card-sc-king-spades = king of spades
card-sc-queen-spades = queen of spades

card-sc-joker = joker

container-sealed = A holographic security seal is on it. Opening it will have the seal dissipate.
container-unsealed = The seal attached to it dissipates.

