# Base

- type: entity
  parent: [FoodInjectableBase, ItemHeftyBase]
  id: FoodEggBase
  description: "Яйце!"
  abstract: true
  components:
  - type: Tag
    tags:
      - Egg
      - Meat
  - type: Food
    trash: 
    - Eggshells
  - type: Sprite
    sprite: Objects/Consumable/Food/egg.rsi
  - type: Item
    sprite: Objects/Consumable/Food/egg.rsi
    size: Tiny
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 6
        reagents:
        - ReagentId: Egg
          Quantity: 6
  - type: DrawableSolution
    solution: food
  - type: SolutionSpiker
    sourceSolution: food
    ignoreEmpty: true
    popup: spike-solution-egg
  # egg fragile
  - type: DamageOnHighSpeedImpact
    minimumSpeed: 0.1
    damage:
      types:
        Blunt: 1
  - type: Damageable
    damageContainer: Biological
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 1
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: desecration
      - !type:SpillBehavior
        solution: food
      - !type:SpawnEntitiesBehavior
        spawn:
          Eggshells:
            min: 1
            max: 1
          # Wow double-yolk you're so lucky!
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  # all below are for egg cooking/exploding
  - type: AtmosExposed
  - type: Temperature
    currentTemperature: 290
  - type: InternalTemperature
    # ~1mm shell and ~1cm of albumen
    thickness: 0.011
    area: 0.04
    # conductivity of egg shell based on a paper from Romanoff and Romanoff (1949)
    conductivity: 0.456

# Splat
- type: entity
  name: "яєчна шкаралупа"
  parent: BaseItem
  id: Eggshells
  description: "Ти пройдешся по ним, друже."
  components:
  - type: Food
  - type: Sprite
    sprite: Objects/Consumable/Food/egg.rsi
    state: eggshells
  - type: Item
    size: Tiny
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 2
        reagents:
        - ReagentId: Egg
          Quantity: 1
  - type: Tag
    tags:
    - Egg
    - Trash
  - type: SpaceGarbage

# Egg

- type: entity
  parent: FoodEggBase
  id: FoodEgg
  name: "яйце"
  components:
  - type: Sprite
    layers:
      - state: icon
        map: [ "enum.DamageStateVisualLayers.Base" ]
  - type: RandomSprite
    available:
      - enum.DamageStateVisualLayers.Base:
          icon: ""
          white: ""
  - type: Construction
    graph: Egg
    node: start

- type: entity
  parent: FoodEggBase
  id: FoodEggBoiled
  name: "варене яйце"
  description: "Смачне варене яйце."
  components:
  - type: Sprite
    layers:
    - state: icon
      map: [ "enum.DamageStateVisualLayers.Base" ]
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 6
        reagents:
        - ReagentId: EggCooked
          Quantity: 6
  - type: Temperature
    # preserve temperature from the boiling step
    currentTemperature: 344
  - type: Construction
    graph: Egg
    node: boiled
