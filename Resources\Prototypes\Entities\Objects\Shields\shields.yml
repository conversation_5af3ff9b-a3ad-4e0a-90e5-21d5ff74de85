- type: entity
  name: "базовий щит"
  parent: BaseItem
  id: BaseShield
  description: "Щит!"
  abstract: true
  components:
    - type: Sprite
      sprite: Objects/Weapons/Melee/shields.rsi
      state: riot-icon
    - type: Item
      sprite: Objects/Weapons/Melee/shields.rsi
      size: Ginormous
      heldPrefix: riot
    - type: Blocking
      passiveBlockModifier:
        coefficients:
          Blunt: 0.9
          Slash: 0.9
          Piercing: 0.9
          Heat: 0.9
      activeBlockModifier:
        coefficients:
          Blunt: 0.8
          Slash: 0.8
          Piercing: 0.8
          Heat: 0.8
        flatReductions:
          Blunt: 1
          Slash: 1
          Piercing: 1
          Heat: 1
    - type: Damageable
      damageContainer: Shield
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 80
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
        - trigger:
            !type:DamageTrigger
            damage: 40 #This is probably enough damage before it breaks
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
            - !type:PlaySoundBehavior
              sound:
                collection: MetalBreak
            - !type:SpawnEntitiesBehavior
              spawn:
                SheetSteel:
                  min: 2
                  max: 2
                SheetGlass:
                  min: 2
                  max: 2
    - type: StaticPrice
      price: 50

#Security Shields

- type: entity
  name: "протиударний щит"
  parent: BaseShield
  id: RiotShield
  description: "Великий баштовий щит. Добре підходить для контролю натовпу."
  components:
    - type: StaticPrice
      price: 90

- type: entity
  name: "лазерний протиударний щит"
  parent: BaseShield
  id: RiotLaserShield
  description: "Захисний щит, створений для того, щоб протистояти лазерам, але не більше того."
  components:
    - type: Sprite
      state: riot_laser-icon
    - type: Item
      heldPrefix: riot_laser
    - type: Blocking
      passiveBlockModifier:
        coefficients:
          Heat: 0.8
      activeBlockModifier:
        coefficients:
          Heat: 0.7
        flatReductions:
          Heat: 2

- type: entity
  name: "щит від кулі під час бунтарсва"
  parent: BaseShield
  id: RiotBulletShield
  description: "Балістичний щит, створений для того, щоб витримувати кулі, але не більше того."
  components:
    - type: Sprite
      state: riot_bullet-icon
    - type: Item
      heldPrefix: riot_bullet
    - type: Blocking
      passiveBlockModifier:
        coefficients:
          Blunt: 0.8
          Piercing: 0.8
      activeBlockModifier:
        coefficients:
          Blunt: 0.7
          Piercing: 0.7
        flatReductions:
          Blunt: 1.5
          Piercing: 1.5

#Craftable shields

- type: entity
  name: "дерев'яний баклер"
  parent: BaseShield
  id: WoodenBuckler
  description: "Гарно вирізьблений дерев'яний щит!"
  components:
    - type: Sprite
      state: buckler-icon
    - type: Item
      heldPrefix: buckler
    - type: Blocking
      passiveBlockModifier:
        coefficients:
          Blunt: 0.95
          Slash: 0.95
          Piercing: 0.95
          Heat: 2
      activeBlockModifier:
        coefficients:
          Blunt: 0.85
          Slash: 0.85
          Piercing: 0.85
          Heat: 2
        flatReductions:
          Blunt: 1
          Slash: 1
          Piercing: 1
    - type: Construction
      graph: WoodenBuckler
      node: woodenBuckler
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 60
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
        - trigger:
            !type:DamageTrigger
            damage: 30 #Weaker shield
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
            - !type:PlaySoundBehavior
              sound:
                collection: WoodDestroy
            - !type:SpawnEntitiesBehavior
              spawn:
                MaterialWoodPlank:
                  min: 5
                  max: 5
    - type: StaticPrice
      price: 150

- type: entity
  name: "імпровізований щит"
  parent: BaseShield
  id: MakeshiftShield
  description: "Сирий і розвалюється. Навіщо ви це робите?"
  components:
    - type: Sprite
      state: makeshift-icon
    - type: Item
      heldPrefix: metal
    - type: Blocking
      passiveBlockModifier:
        coefficients:
          Blunt: 0.95
          Slash: 0.95
          Piercing: 0.95
          Heat: 0.9
      activeBlockModifier:
        coefficients:
          Blunt: 0.85
          Slash: 0.85
          Piercing: 0.85
          Heat: 0.8
        flatReductions:
          Blunt: 0.5
          Slash: 0.5
          Piercing: 0.5
          Heat: 1
    - type: Construction
      graph: MakeshiftShield
      node: makeshiftShield
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 40
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
        - trigger:
            !type:DamageTrigger
            damage: 20 #Very weak shield
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
            - !type:PlaySoundBehavior
              sound:
                collection: MetalBreak
            - !type:SpawnEntitiesBehavior
              spawn:
                SheetSteel:
                  min: 1
                  max: 2

- type: entity
  name: "павутинний щит"
  parent: BaseShield
  id: WebShield
  description: "Він достатньо товстий, щоб витримати кілька ударів, але, мабуть, не нагрівається."
  components:
    - type: Sprite
      sprite: Objects/Weapons/Melee/web-shield.rsi
      state: icon
    - type: Item
      sprite: Objects/Weapons/Melee/web-shield.rsi
      heldPrefix: icon
    - type: Blocking
      passiveBlockModifier:
        coefficients:
          Blunt: 0.95
          Slash: 0.95
          Piercing: 0.95
      activeBlockModifier:
        coefficients:
          Blunt: 0.85
          Slash: 0.85
          Piercing: 0.85
        flatReductions:
          Blunt: 0.5
          Slash: 0.5
          Piercing: 0.5
    - type: Construction
      graph: WebObjects
      node: shield
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 40
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
        - trigger:
            !type:DamageTrigger
            damage: 20
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
            - !type:SpawnEntitiesBehavior
              spawn:
                MaterialWebSilk:
                  min: 1
                  max: 1
            - !type:PlaySoundBehavior
              sound:
                collection: WoodDestroy

#Magic/Cult Shields (give these to wizard for now)

- type: entity
  name: "щит з годинниковим механізмом"
  parent: BaseShield
  id: ClockworkShield
  description: "Ratvar oyrffrf lbh jvgu uvf cebgrpgvba."
  components:
    - type: Sprite
      state: ratvarian-icon
    - type: Item
      heldPrefix: ratvarian
    - type: Blocking
      passiveBlockModifier:
        coefficients:
          Blunt: 0.8
          Slash: 0.8
          Piercing: 0.8
          Heat: 1.5
      activeBlockModifier:
        coefficients:
          Blunt: 0.7
          Slash: 0.7
          Piercing: 0.7
          Heat: 1.5
        flatReductions:
          Blunt: 1
          Slash: 1
          Piercing: 1
      #Have it break into brass when clock cult is in

- type: entity
  name: "енергетичний щит"
  parent: BaseItem
  id: EnergyShield
  description: "Екзотичний енергетичний щит у складеному вигляді може поміститися навіть у кишені."
  components:
    - type: ItemToggle
      soundActivate:
        path: /Audio/Weapons/ebladeon.ogg
      soundDeactivate:
        path: /Audio/Weapons/ebladeoff.ogg
    - type: ItemToggleActiveSound
      activeSound:
        path: /Audio/Weapons/ebladehum.ogg
    - type: ItemToggleSize
      activatedSize: Huge
    - type: ComponentToggler
      components:
      - type: DisarmMalus
        malus: 0.6
    - type: Sprite
      sprite: Objects/Weapons/Melee/e_shield.rsi
      layers:
        - state: eshield-icon
        - state: eshield-on
          color: "#FFFFFF"
          visible: false
          shader: unshaded
          map: [ "shield" ]
    - type: Item
      size: Small
      sprite: Objects/Weapons/Melee/e_shield.rsi
      heldPrefix: eshield
    - type: UseDelay
      delay: 0.5
    - type: ToggleableLightVisuals
      spriteLayer: shield
      inhandVisuals:
        left:
          - state: inhand-left-shield
            shader: unshaded
        right:
          - state: inhand-right-shield
            shader: unshaded
    - type: PointLight
      netsync: false
      enabled: false
      radius: 1.5
      energy: 2
      color: blue
    - type: ItemTogglePointLight
    - type: Reflect
      reflectProb: 0.95
      innate: true
      reflects:
        - Energy
    - type: Blocking
      passiveBlockModifier:
        coefficients:
          Blunt: 1.0
          Slash: 0.9
          Piercing: 0.85
          Heat: 0.6
      activeBlockModifier:
        coefficients:
          Blunt: 1.2
          Slash: 0.85
          Piercing: 0.5
          Heat: 0.4
        flatReductions:
          Heat: 1
          Piercing: 1
    - type: Appearance
    - type: Damageable
      damageContainer: Shield
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 180
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
        - trigger:
            !type:DamageTrigger
            damage: 100
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
            - !type:PlaySoundBehavior
              sound:
                collection: GlassBreak
            - !type:SpawnEntitiesBehavior
              spawn:
                BrokenEnergyShield:
                  min: 1
                  max: 1
    - type: StaticPrice
      price: 350

- type: entity
  name: "зламаний енергетичний щит"
  parent: BaseItem
  id: BrokenEnergyShield
  description: "Щось всередині вигоріло, воно більше не функціонує."
  components:
    - type: Sprite
      sprite: Objects/Weapons/Melee/e_shield.rsi
      state: eshield-icon
    - type: Item
      sprite: Objects/Weapons/Melee/e_shield.rsi
      size: Small
      heldPrefix: eshield

- type: entity
  name: "телескопічний щит"
  parent: BaseShield
  id: TelescopicShield
  description: "Удосконалений щит для захисту від заворушень, виготовлений з легких матеріалів, який складається для зручності зберігання."
  components:
    - type: ItemToggle
      soundActivate:
        path: /Audio/Weapons/telescopicon.ogg
        params:
          volume: -5
      soundDeactivate:
        path: /Audio/Weapons/telescopicoff.ogg
        params:
          volume: -5
    - type: ComponentToggler
      components:
      - type: DisarmMalus
        malus: 0.6
    - type: ItemToggleSize
      activatedSize: Huge
    - type: Sprite
      sprite: Objects/Weapons/Melee/teleriot_shield.rsi
      layers:
        - state: teleriot-icon
        - state: teleriot-on
          visible: false
          map: [ "shield" ]
    - type: Item
      size: Small
      sprite: Objects/Weapons/Melee/teleriot_shield.rsi
      heldPrefix: teleriot
    - type: UseDelay
      delay: 0.5
    - type: ToggleableLightVisuals
      spriteLayer: shield
      inhandVisuals:
        left:
          - state: inhand-left-shield
        right:
          - state: inhand-right-shield
    - type: Appearance
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 100
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
        - trigger:
            !type:DamageTrigger
            damage: 70
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
            - !type:PlaySoundBehavior
              sound:
                collection: MetalGlassBreak
            - !type:SpawnEntitiesBehavior
              spawn:
                SheetSteel:
                  min: 1
                  max: 1
                SheetGlass:
                  min: 2
                  max: 2
    - type: StaticPrice
      price: 250
