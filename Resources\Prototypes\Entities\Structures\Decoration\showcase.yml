- type: entity
  id: BaseShowcaseRobot
  abstract: true
  parent: BaseStructure
  name: "вітрина охоронних роботів"
  description: "Нефункціональна копія старого робота-охоронця."
  components:
    - type: Anchorable
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: Metallic
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 150
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
        - trigger:
            !type:DamageTrigger
            damage: 75
          behaviors:
            - !type:PlaySoundBehavior
              sound:
                collection: MetalBreak
            - !type:SpawnEntitiesBehavior
              spawn:
                PartRodMetal1:
                  min: 5
                  max: 10
            - !type:DoActsBehavior
              acts: [ "Destruction" ]

- type: entity
  id: ShowcaseRobot
  parent: BaseShowcaseRobot
  name: "вітрина охоронних роботів"
  description: "Нефункціональна копія старого робота-охоронця."
  components:
  - type: Sprite
    sprite: Structures/Decoration/showcase.rsi
    state: showcase_1

- type: entity
  id: ShowcaseRobotWhite
  parent: BaseShowcaseRobot
  name: "вітрина з білим роботом"
  description: "Нефункціональна копія старого робота."
  components:
  - type: Sprite
    sprite: Structures/Decoration/showcase.rsi
    state: showcase_2

- type: entity
  id: ShowcaseRobotAntique
  parent: BaseShowcaseRobot
  name: "вітрина вантажних роботів"
  description: "Нефункціональна копія старого вантажного робота."
  components:
  - type: Sprite
    sprite: Structures/Decoration/showcase.rsi
    state: showcase_3

- type: entity
  id: ShowcaseRobotMarauder
  parent: BaseShowcaseRobot
  name: "мародерська вітрина"
  description: "Нефункціональна копія мародера, пофарбована в зелений колір."
  components:
  - type: Sprite
    sprite: Structures/Decoration/showcase.rsi
    state: showcase_4
