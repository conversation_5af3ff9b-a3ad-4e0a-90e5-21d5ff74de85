- type: entity
  name: "предмет"
  id: BaseItem
  abstract: true
  components:
  - type: Item
    size: Small
  - type: Clickable
  - type: InteractionOutline
  - type: MovedByPressure
  - type: EmitSoundOnCollide
    sound:
      collection: WeakHit
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Effects/drop.ogg
  - type: DamageOnHighSpeedImpact
    damage:
      types:
        Blunt: 5
    soundHit:
      collection: MetalThud
  - type: CollisionWake
  - type: Physics
    bodyType: Dynamic
    fixedRotation: false
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 20
        mask:
        - ItemMask
        restitution: 0.3  # fite me
        friction: 0.2
  - type: Sprite
    drawdepth: Items
    noRot: false
  - type: Pullable
  - type: DamageExaminable

- type: entity
  name: "предмет зберігання"
  id: BaseStorageItem
  parent: BaseItem
  abstract: true
  components:
  - type: Storage
  - type: UserInterface
    interfaces:
      enum.StorageUiKey.Key:
        type: StorageBoundUserInterface
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
        ents: []

- type: entity
  id: BaseBagOpenClose
  abstract: true
  components:
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.SharedBagOpenVisuals.BagState:
        openLayer:
          Open: { visible: true }
          Closed: { visible: false }


# PowerCellSlot parents
- type: entity
  id: PowerCellSlotSmallItem
  abstract: true
  components:
    - type: ContainerContainer
      containers:
        cell_slot: !type:ContainerSlot { }
    - type: PowerCellSlot
      cellSlotId: cell_slot
    - type: ItemSlots
      slots:
        cell_slot:
          name: power-cell-slot-component-slot-name-default
          startingItem: PowerCellSmall

- type: entity
  id: PowerCellSlotMediumItem
  abstract: true
  components:
    - type: ContainerContainer
      containers:
        cell_slot: !type:ContainerSlot { }
    - type: PowerCellSlot
      cellSlotId: cell_slot
    - type: ItemSlots
      slots:
        cell_slot:
          name: power-cell-slot-component-slot-name-default
          startingItem: PowerCellMedium

- type: entity
  id: PowerCellSlotHighItem
  abstract: true
  components:
    - type: ContainerContainer
      containers:
        cell_slot: !type:ContainerSlot { }
    - type: PowerCellSlot
      cellSlotId: cell_slot
    - type: ItemSlots
      slots:
        cell_slot:
          name: power-cell-slot-component-slot-name-default
          startingItem: PowerCellHigh
