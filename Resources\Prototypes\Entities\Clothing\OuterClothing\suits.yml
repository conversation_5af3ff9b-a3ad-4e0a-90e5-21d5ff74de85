- type: entity
  parent: ClothingOuterBaseLarge
  id: ClothingOuterSuitBomb
  name: "вибухозахисний костюм"
  description: "Важкий костюм, розроблений для того, щоб витримувати тиск бомби та осколків, які вона може спричинити."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Suits/bombsuit.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Suits/bombsuit.rsi
  - type: ClothingSpeedModifier
    walkModifier: 0.8
    sprintModifier: 0.8
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Heat: 0.75
  - type: ExplosionResistance
    damageCoefficient: 0.15
  - type: GroupExamine
  - type: Tag
    tags:
    - Hardsuit
    - WhitelistChameleon
    - FullBodyOuter
    - HidesHarpyWings
  - type: ClothingRequiredStepTriggerImmune
    slots: WITHOUT_POCKET

- type: entity
  parent: ClothingOuterSuitBomb
  id: ClothingOuterSuitJanitorBomb
  name: "вибухозахисний костюм прибиральника"
  description: "Важкий костюм, призначений для протистояння вибухам, що утворюються в результаті реакцій між хімічними речовинами."
  suffix: DO NOT MAP
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Suits/janitor_bombsuit.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Suits/janitor_bombsuit.rsi
  - type: ClothingSpeedModifier
    walkModifier: 0.8
    sprintModifier: 0.8
  - type: ExplosionResistance
    damageCoefficient: 0.15
  - type: GroupExamine
  - type: Tag
    tags:
    - Hardsuit
    - WhitelistChameleon
    - FullBodyOuter

- type: entity
  parent: [ClothingOuterBaseLarge, AllowSuitStorageClothing]
  id: ClothingOuterSuitFire
  name: "протипожежний костюм"
  description: "Костюм, який допомагає захиститися від небезпечних температур."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Suits/fire.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Suits/fire.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.04
  - type: TemperatureProtection
    coefficient: 0.005
  - type: FireProtection
    reduction: 0.65 # doesnt have a full seal so not as good
  - type: Armor
    modifiers:
      coefficients:
        Slash: 0.9
        Heat: 0.8
        Cold: 0.8
  - type: ClothingSpeedModifier
    walkModifier: 0.8
    sprintModifier: 0.7
  - type: HeldSpeedModifier
  - type: GroupExamine
  - type: ClothingRequiredStepTriggerImmune
    slots: WITHOUT_POCKET
  - type: Tag
    tags:
      - WhitelistChameleon
      - HidesHarpyWings

- type: entity
  parent: [ClothingOuterBaseLarge, AllowSuitStorageClothing]
  id: ClothingOuterSuitAtmosFire
  name: "протипожежний костюм атмосферного техніка"
  description: "Дорогий протипожежний костюм, який захищає навіть від найсмертоносніших пожеж на станції. Призначений для захисту, навіть якщо його власник підпалений."
  components:
    - type: Sprite
      sprite: Clothing/OuterClothing/Suits/atmos_firesuit.rsi
    - type: Clothing
      sprite: Clothing/OuterClothing/Suits/atmos_firesuit.rsi
    - type: PressureProtection
      highPressureMultiplier: 0.02
      lowPressureMultiplier: 1000
    - type: TemperatureProtection
      coefficient: 0.001
    - type: FireProtection
      reduction: 0.8 # atmos firesuit offers best protection, hardsuits are a little vulnerable
    - type: Armor
      modifiers:
        coefficients:
          Slash: 0.9
          Heat: 0.8
          Cold: 0.8
    - type: ClothingSpeedModifier
      walkModifier: 0.8
      sprintModifier: 0.8
    - type: HeldSpeedModifier
    - type: GroupExamine
    - type: ClothingRequiredStepTriggerImmune
      slots: WITHOUT_POCKET
    - type: Tag
      tags:
        - FullBodyOuter
        - WhitelistChameleon
        - HidesHarpyWings

- type: entity
  parent: [ClothingOuterBaseLarge, GeigerCounterClothing, AllowSuitStorageClothing]
  id: ClothingOuterSuitRad
  name: "протирадіаційний костюм"
  description: "Костюм, який захищає від радіації. На етикетці написано: \"Зроблено зі свинцю. Будь ласка, не використовуйте ізоляцію\""
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Suits/rad.rsi
  - type: Armor
    modifiers:
      coefficients:
        Heat: 0.90
        Radiation: 0.001
  - type: Clothing
    sprite: Clothing/OuterClothing/Suits/rad.rsi
  - type: GroupExamine
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodRad
    slot: head
  - type: ContainerContainer
    containers:
      toggleable-clothing: !type:Container {}
  - type: ClothingRequiredStepTriggerImmune
    slots: WITHOUT_POCKET
  - type: Tag
    tags:
      - FullBodyOuter
      - WhitelistChameleon
      - HidesHarpyWings

- type: entity
  parent: [ClothingOuterBaseLarge, AllowSuitStorageClothing, BaseToggleClothing]
  id: ClothingOuterSuitSpaceNinja
  name: "космічний костюм ніндзя"
  description: "Цей чорний технологічно просунутий, кібернетично вдосконалений костюм надає безліч здібностей, таких як невидимість або телепортація."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Suits/spaceninja.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Suits/spaceninja.rsi
  # hardsuit stuff
  - type: PressureProtection
    highPressureMultiplier: 0.6
    lowPressureMultiplier: 1000
  - type: TemperatureProtection
    coefficient: 0.01
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.8
        Slash: 0.8
        Piercing: 0.8
        Heat: 0.8
  # phase cloak
  - type: ToggleClothing
    action: ActionTogglePhaseCloak
  - type: ComponentToggler
    parent: true
    components:
    # Goobstation - Proper invisibility
    - type: Stealth
      maxVisibility: 0.1
      hadOutline: true
    - type: StealthOnMove
      invisibilityPenalty: 3
      maxInvisibilityPenalty: 1.6
      passiveVisibilityRate: -1 # cloak very fast
      movementVisibilityRate: 0.15 # only some cloak if moving
  - type: PowerCellDraw
    drawRate: 1.8 # 200 seconds on the default cell
  - type: ToggleCellDraw
  # throwing star ability (Broken)
  # - type: ItemCreator
  #  action: ActionCreateThrowingStar
  #  charge: 14.4
  #  spawnedPrototype: ThrowingStarNinja
  #  noPowerPopup: ninja-no-power
  # core ninja suit stuff
  - type: NinjaSuit
  - type: UseDelay
    delay: 5 # disable time
  - type: PowerCellSlot
    cellSlotId: cell_slot
    # throwing in a recharger would bypass glove charging mechanic
    fitsInCharger: false
  - type: ContainerContainer
    containers:
      cell_slot: !type:ContainerSlot
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellSmall
        disableEject: true
  - type: Unremoveable

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterSuitChicken
  name: "костюм курки"
  description: "Бок-бок-бок!"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Suits/chicken.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Suits/chicken.rsi
  - type: ClothingRequiredStepTriggerImmune
    slots: WITHOUT_POCKET
  - type: Tag
    tags:
      - FullBodyOuter

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterSuitShrineMaiden
  name: "дівоче вбрання святині"
  description: "Відразу хочеться піти і розібратися з якимось неприємним йокай."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Suits/shrine-maiden.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Suits/shrine-maiden.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterSuitMonkey
  name: "костюм мавпи"
  description: "Костюм, схожий на примата."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Suits/monkey.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Suits/monkey.rsi
  - type: ContainerContainer
    containers:
      toggleable-clothing: !type:Container {}
  - type: ClothingRequiredStepTriggerImmune
    slots: WITHOUT_POCKET
  - type: Tag
    tags:
      - FullBodyOuter

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterSuitIan
  name: "костюм Іана"
  description: "Зробіть себе схожим на Іана!"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Suits/iansuit.rsi
  - type: Item
    size: Normal
  - type: Clothing
    sprite: Clothing/OuterClothing/Suits/iansuit.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodIan
  - type: ContainerContainer
    containers:
      toggleable-clothing: !type:Container {}
  - type: Construction
    graph: ClothingOuterSuitIan
    node: suit
  - type: ClothingRequiredStepTriggerImmune
    slots: WITHOUT_POCKET

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterSuitCarp
  name: "костюм космокоропа"
  description: "Спеціальний костюм, який робить вас схожим на космічного коропа, якщо у вас поганий зір."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Suits/carpsuit.rsi
  - type: Item
    size: Normal
  - type: Clothing
    sprite: Clothing/OuterClothing/Suits/carpsuit.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodCarp
  - type: ContainerContainer
    containers:
      toggleable-clothing: !type:Container {}

- type: entity
  parent: ClothingOuterSuitCarp
  id: ClothingOuterHardsuitCarp
  suffix: Hardsuit, DO NOT MAP
  components:
  - type: PressureProtection
    highPressureMultiplier: 0.6
    lowPressureMultiplier: 1000
  - type: TemperatureProtection
    coefficient: 0.01
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHelmetHardsuitCarp

- type: entity # From Upstream
  parent: ClothingOuterBase
  id: ClothingOuterSuitWitchRobes
  name: "відьомські шати"
  description: "Магія - це сила заклинання, ЗЕ!"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Suits/witchrobe.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Suits/witchrobe.rsi
