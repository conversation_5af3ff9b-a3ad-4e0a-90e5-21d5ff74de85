- type: species
  id: Skeleton
  name: species-name-skeleton
  roundStart: false
  prototype: MobSkeletonPerson
  sprites: MobSkeletonSprites
  defaultSkinTone: "#fff9e2"
  markingLimits: MobHumanMarkingLimits
  maleFirstNames: skeletonNamesFirst
  femaleFirstNames: skeletonNamesFirst
  dollPrototype: MobSkeletonPersonDummy
  skinColoration: TintedHues

- type: speciesBaseSprites
  id: MobSkeletonSprites
  sprites:
    Head: MobSkeletonHead
    Chest: MobSkeletonTorso
    LArm: MobSkeletonLArm
    RArm: MobSkeletonRArm
    LHand: MobSkeletonLHand
    RHand: MobSkeletonRHand
    LLeg: MobSkeletonLLeg
    RLeg: MobSkeletonRLeg
    LFoot: MobSkeletonLFoot
    RFoot: MobSkeletonRFoot

- type: humanoidBaseSprite
  id: MobSkeletonHead
  baseSprite:
    sprite: Mobs/Species/Skeleton/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobSkeletonHeadMale
  baseSprite:
    sprite: Mobs/Species/Skeleton/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobSkeletonHeadFemale
  baseSprite:
    sprite: Mobs/Species/Skeleton/parts.rsi
    state: head_f

- type: humanoidBaseSprite
  id: MobSkeletonTorso
  baseSprite:
    sprite: Mobs/Species/Skeleton/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobSkeletonTorsoMale
  baseSprite:
    sprite: Mobs/Species/Skeleton/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobSkeletonTorsoFemale
  baseSprite:
    sprite: Mobs/Species/Skeleton/parts.rsi
    state: torso_f

- type: humanoidBaseSprite
  id: MobSkeletonLLeg
  baseSprite:
    sprite: Mobs/Species/Skeleton/parts.rsi
    state: l_leg

- type: humanoidBaseSprite
  id: MobSkeletonLArm
  baseSprite:
    sprite: Mobs/Species/Skeleton/parts.rsi
    state: l_arm

- type: humanoidBaseSprite
  id: MobSkeletonLHand
  baseSprite:
    sprite: Mobs/Species/Skeleton/parts.rsi
    state: l_hand

- type: humanoidBaseSprite
  id: MobSkeletonLFoot
  baseSprite:
    sprite: Mobs/Species/Skeleton/parts.rsi
    state: l_foot

- type: humanoidBaseSprite
  id: MobSkeletonRLeg
  baseSprite:
    sprite: Mobs/Species/Skeleton/parts.rsi
    state: r_leg

- type: humanoidBaseSprite
  id: MobSkeletonRArm
  baseSprite:
    sprite: Mobs/Species/Skeleton/parts.rsi
    state: r_arm

- type: humanoidBaseSprite
  id: MobSkeletonRHand
  baseSprite:
    sprite: Mobs/Species/Skeleton/parts.rsi
    state: r_hand

- type: humanoidBaseSprite
  id: MobSkeletonRFoot
  baseSprite:
    sprite: Mobs/Species/Skeleton/parts.rsi
    state: r_foot
