﻿- type: decal
  id: Markup
  parent: Overlays
  abstract: true

- type: decal
  id: MarkupSquare
  parent: Markup
  tags: ["overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/markups.rsi
    state: square

- type: decal
  id: MarkupRectangle1x2
  parent: Markup
  tags: ["overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/markups.rsi
    state: rectangle1x2

- type: decal
  id: MarkupRectangle1x2Center
  parent: Markup
  tags: ["overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/markups.rsi
    state: rectangle1x2center

- type: decal
  id: MarkupSquareQuater
  parent: Markup
  tags: ["overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/markups.rsi
    state: squareQuater

- type: decal
  id: MarkupSquareQuaterCenter
  parent: Markup
  tags: ["overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/markups.rsi
    state: squareQuaterCenter
