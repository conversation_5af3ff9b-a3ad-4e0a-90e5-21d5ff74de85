#Small lights
- type: entity
  parent: SmallLight
  id: LightSmallAlwayson
  name: "маленька лампа"
  suffix: Always on
  description: "Постійно ввімкнене світло."
  components:
  - type: Sprite
    sprite: Structures/Wallmounts/lightbulbcaged.rsi
    state: base
    drawdepth: Overdoors
    offset: 0, 1 # 0.75 is better but breaks for east west placement
  - type: PointLight
    energy: 1.0
    radius: 6
    softness: 1.1
    enabled: true
  - type: WallMount

- type: entity
  parent: PoweredSmallLightEmpty
  id: LightSmallEmpty
  name: "маленька лампа"
  description: "Освітлювальний прилад. Споживає енергію і виробляє світло, коли оснащений лампочкою."
  suffix: Empty
  components:
  - type: Sprite
    sprite: Structures/Wallmounts/lightbulbcaged.rsi
    state: empty
    offset: 0, 1
  - type: WallMount

- type: entity
  parent: PoweredSmallLight
  id: LightSmall
  suffix: ""
  components:
  - type: Sprite
    sprite: Structures/Wallmounts/lightbulbcaged.rsi
    state: base
    offset: 0, 1
  - type: WallMount
