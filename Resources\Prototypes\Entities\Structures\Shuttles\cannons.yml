- type: entity
  id: ShuttleGunBase
  name: "гівняний пістолет"
  abstract: true
  placement:
    mode: SnapgridCenter
  components:
  - type: Appearance
  - type: Clickable
  - type: InteractionOutline
  - type: Anchorable
  - type: Pullable
  - type: Rotatable
  - type: Physics
    bodyType: Static
  - type: ContainerContainer
  - type: Gun
    selectedMode: SemiAuto
    availableModes:
      - SemiAuto
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.45"
        density: 450
        mask:
        - MachineMask
        layer:
        - MachineLayer
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Electronic
  - type: Transform
    anchored: true
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: BasicDevice
  - type: WirelessNetworkConnection
    range: 200
  - type: DeviceLinkSink
    ports:
    - Trigger
    - Toggle
    - On
    - Off
  - type: AutoShootGun
  - type: GunSignalControl
  - type: StaticPrice
    price: 1500

# ---- Laser weapon branch ----
# naming: LSE (Laser) + conventional power + suffix (c for PowerCage, e for wired energy) + Name
# example: LSE-100e "Clown destroyer" (powered by the wiring, very weak)

- type: entity
  id: ShuttleGunSvalinnMachineGun
  parent: [ ShuttleGunBase, ConstructibleMachine]
  name: "LSE-400c \"Свалінський кулемет\""
  description: "Базова стаціонарна лазерна установка. Ефективний проти живих цілей та електроніки. Використовує для стрільби звичайні елементи живлення і має надзвичайно високу скорострільність"
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Shuttles/laser.rsi
    layers:
    - state: lse-400c
    - state: mag-unshaded-9
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: ContainerContainer
    containers:
      machine_board: !type:Container
      machine_parts: !type:Container
      gun_magazine: !type:ContainerSlot
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
        - !type:ChangeConstructionNodeBehavior
          node: machineFrame
        - !type:DoActsBehavior
          acts: ["Destruction"]
  - type: Gun
    fireRate: 5
    useKey: false
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/ship_svalinn.ogg
      params:
        variation: 0.05
  - type: MagazineVisuals
    magState: mag
    steps: 10
    zeroVisible: true
  - type: Machine
    board: ShuttleGunSvalinnMachineGunCircuitboard
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        insertSound: /Audio/Weapons/Guns/MagIn/batrifle_magin.ogg
        ejectSound: /Audio/Weapons/Guns/MagOut/batrifle_magout.ogg
        whitelist:
          tags:
            - PowerCell
            - PowerCellSmall
  - type: MagazineAmmoProvider

- type: entity
  id: ShuttleGunPerforator
  parent: [ ShuttleGunBase, ConstructibleMachine]
  name: "LSE-1200c \"Перфоратор\""
  description: "Вдосконалений стаціонарний лазерний апарат. Знищує електроніку та надзвичайно небезпечний для здоров'я! Використовує силову клітку для стрільби."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Shuttles/laser.rsi
    layers:
    - state: lse-1200c
    - state: mag-unshaded-9
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: ContainerContainer
    containers:
      machine_board: !type:Container
      machine_parts: !type:Container
      gun_magazine: !type:ContainerSlot
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
        - !type:ChangeConstructionNodeBehavior
          node: machineFrame
        - !type:DoActsBehavior
          acts: ["Destruction"]
  - type: Gun
    fireRate: 1
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/ship_perforator.ogg
      params:
        variation: 0.05
  - type: MagazineVisuals
    magState: mag
    steps: 10
    zeroVisible: true
  - type: Machine
    board: ShuttleGunPerforatorCircuitboard
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        insertSound: /Audio/Weapons/Guns/MagIn/batrifle_magin.ogg
        ejectSound: /Audio/Weapons/Guns/MagOut/batrifle_magout.ogg
        whitelist:
          tags:
            - PowerCage
  - type: MagazineAmmoProvider

# ---- Launchers ----
# naming: EXP (Explosion) + conventional power + suffix (g for Grenade, c for RPG Cartridge) + Name
# example: EXP-100c "Poppy"

- type: entity
  id: ShuttleGunFriendship
  parent: [ShuttleGunBase, ConstructibleMachine]
  name: "EXP-320g \"Дружба\""
  description: "Невеликий стаціонарний гранатомет, що вміщує 2 гранати."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Shuttles/launcher.rsi
    layers:
    - state: exp-320g
    - state: mag-7
      map: ["enum.GunVisualLayers.Mag"]
  - type: ContainerContainer
    containers:
      machine_board: !type:Container
      machine_parts: !type:Container
      ballistic-ammo: !type:Container
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
        - !type:ChangeConstructionNodeBehavior
          node: machineFrame
        - !type:DoActsBehavior
          acts: ["Destruction"]
  - type: Gun
    projectileSpeed: 80
    fireRate: 1
    angleDecay: 45
    minAngle: 0
    maxAngle: 15
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/ship_friendship.ogg
      params:
        pitch: 0.8
    soundEmpty:
      path: /Audio/Weapons/Guns/Empty/empty.ogg
  - type: BallisticAmmoProvider
    whitelist:
      tags:
        - Grenade
    capacity: 2
    soundInsert:
      path: /Audio/Weapons/Guns/MagIn/batrifle_magin.ogg
  - type: Machine
    board: ShuttleGunFriendshipCircuitboard
  - type: MagazineVisuals
    magState: mag
    steps: 8
    zeroVisible: false

- type: entity
  id: ShuttleGunDuster
  parent: [ShuttleGunBase, ConstructibleMachine]
  name: "EXP-2100g \"Duster\""
  description: "Потужний стаціонарний гранатомет. Для використання потрібен патрон."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Shuttles/launcher.rsi
    layers:
    - state: exp-2100g
    - state: mag-7
      map: ["enum.GunVisualLayers.Mag"]
  - type: ContainerContainer
    containers:
      machine_board: !type:Container
      machine_parts: !type:Container
      gun_magazine: !type:ContainerSlot
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 350
      behaviors:
        - !type:ChangeConstructionNodeBehavior
          node: machineFrame
        - !type:DoActsBehavior
          acts: ["Destruction"]
  - type: Gun
    projectileSpeed: 40
    fireRate: 0.3
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/ship_duster.ogg
      params:
        variation: 0.05
        pitch: 0.8
    soundEmpty:
      path: /Audio/Weapons/Guns/Empty/empty.ogg
  - type: Machine
    board: ShuttleGunDusterCircuitboard
  - type: MagazineAmmoProvider
  - type: MagazineVisuals
    magState: mag
    steps: 8
    zeroVisible: false
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        priority: 2
        whitelist:
          tags:
          - MagazineGrenade
        insertSound:
          path: /Audio/Weapons/Guns/MagIn/kinetic_reload.ogg
          params:
            pitch: 2
        ejectSound: /Audio/Weapons/Guns/MagOut/smg_magout.ogg

# ---- Other weapon ----

- type: entity
  id: ShuttleGunPirateCannon
  parent: ShuttleGunBase
  name: "гармата піратського корабля"
  description: "Бабах!"
  components:
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
        - !type:DoActsBehavior
          acts: ["Destruction"]
  - type: Sprite
    sprite: Objects/Weapons/Guns/Shuttles/pirate_cannon.rsi
    layers:
    - state: base
  - type: Gun
    fireRate: 1
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/mateba.ogg
  - type: BallisticAmmoProvider
    whitelist:
      tags:
        - CannonBall
    capacity: 1
    proto: CannonBall
    soundInsert:
      path: /Audio/Weapons/Guns/Gunshots/grenade_launcher.ogg

- type: entity
  id: ShuttleGunKinetic
  parent: [ ShuttleGunBase, ConstructibleMachine]
  name: "PTK-800 \"Дематеріалізатор матерії\""
  description: "Рятівна стаціонарна шахтна вежа. Поступово накопичує заряди самостійно, надзвичайно ефективна для розкопок астероїдів."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Shuttles/kinetic.rsi
    layers:
    - state: ptk-800
    - state: mag-7
  - type: ContainerContainer
    containers:
      machine_board: !type:Container
      machine_parts: !type:Container
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
        - !type:ChangeConstructionNodeBehavior
          node: machineFrame
        - !type:DoActsBehavior
          acts: ["Destruction"]
  - type: Gun
    projectileSpeed: 20
    fireRate: 2
    selectedMode: SemiAuto
    angleDecay: 45
    minAngle: 5
    maxAngle: 15
    availableModes:
    - SemiAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/kinetic_accel.ogg
      params:
        variation: 0.12
  - type: RechargeBasicEntityAmmo
    rechargeCooldown: 2
    rechargeSound:
      path: /Audio/Weapons/Guns/Bolt/lmg_bolt_closed.ogg
      params:
        pitch: 1.2
        variation: 0.08
  - type: BasicEntityAmmoProvider
    proto: BulletKineticShuttle
    capacity: 5
    count: 5
  - type: Machine
    board: ShuttleGunKineticCircuitboard
