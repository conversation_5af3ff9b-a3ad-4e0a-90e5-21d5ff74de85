- type: entity
  id: BaseMobAsteroid
  parent:
  - BaseMob
  - MobDamageable
  - MobAtmosExposed
  - MobCombat
  abstract: true
  components:
  - type: Reactive
    groups:
      Flammable: [Touch]
      Extinguish: [Touch]
      Acidic: [Touch, Ingestion]
  - type: Body
    prototype: Animal
  - type: Climbing
  - type: NameIdentifier
    group: GenericNumber
  - type: StatusEffects
    allowed:
    - SlowedDown
    - Stutter
    - Stun
    - Electrocution
    - TemporaryBlindness
    - RadiationProtection
    - Drowsiness
  - type: StandingState
  - type: Tag
    tags:
    - DoorBumpOpener

- type: entity
  id: MobGoliath
  parent: BaseMobAsteroid
  name: "голіаф"
  description: "Масивний звір, який використовує довгі щупальця, щоб схопити свою здобич, погрожуючи їй, не рекомендується за жодних обставин."
  components:
  - type: Absorbable
  - type: Sprite
    sprite: Mobs/Aliens/Asteroid/goliath.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: goliath
  - type: DamageStateVisuals
    states:
      Alive:
        Base: goliath
      Dead:
        Base: goliath_dead
  - type: MovementSpeedModifier
    baseWalkSpeed : 2.50
    baseSprintSpeed : 2.50
  - type: MobThresholds
    thresholds:
      0: Alive
      300: Dead
  - type: MeleeWeapon
    soundHit:
      path: "/Audio/Weapons/smash.ogg"
    angle: 0
    attackRate: 1.3333
    animation: WeaponArcPunch
    damage:
      types:
        Slash: 15
        Piercing: 10
  - type: NpcFactionMember
    factions:
    - SimpleHostile
  - type: HTN
    rootTask:
      task: GoliathCompound
    blackboard:
      VisionRadius: !type:Single
        6
      AggroVisionRadius: !type:Single
        10
  - type: NPCUseActionOnTarget
    actionId: ActionGoliathTentacle
  - type: Tag
    tags:
    - CannotSuicide
    - Goliath
    - FootstepSound
  - type: NoSlip
  - type: Butcherable
    spawned:
    - id: FoodMeatGoliath
      amount: 3
    - id: MaterialGoliathHide1
  - type: Fauna # Lavaland Change

- type: entity
  id: ActionGoliathTentacle
  name: "[color=red]Tentacle Slam[/color]"
  description: "Використовуйте щупальця, щоб схопити та оглушити цільового гравця!"
  components:
  - type: EntityWorldTargetAction
    raiseOnUser: true
    icon:
      sprite: Mobs/Aliens/Asteroid/goliath.rsi
      state: goliath_tentacle_spawn
    iconOn:
      sprite: Mobs/Aliens/Asteroid/goliath.rsi
      state: goliath_tentacle_wiggle
    sound:
      path: "/Audio/Weapons/slash.ogg"
    event: !type:GoliathSummonTentacleAction
    useDelay: 8
    range: 10

- type: entity
  id: GoliathTentacle
  name: "щупальце"
  components:
  - type: Transform
    anchored: True
  - type: Physics
    bodyType: Static
    canCollide: true
  - type: InteractionOutline
  - type: Sprite
    sprite: Mobs/Aliens/Asteroid/goliath.rsi
    layers:
    - state: goliath_tentacle_wiggle
  - type: StunOnContact
    blacklist:
      tags:
      - Goliath
  - type: Fixtures
    fixtures:
      fix:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.45"
        mask:
        - Impassable
        layer:
        - Impassable
        hard: false
  - type: TimedDespawn #do this shit by hand because of fucking course.
    lifetime: 0.4
  - type: SpawnOnDespawn
    prototype: EffectGoliathTentacleRetract

- type: entity
  id: BaseEffectGoliathTentacleSpawn
  categories: [ HideSpawnMenu ]
  name: "щупальце"
  abstract: true
  components:
  - type: Transform
    anchored: True
  - type: Physics
    bodyType: Static
    canCollide: false
  - type: Sprite
    sprite: Mobs/Aliens/Asteroid/goliath.rsi
  - type: InteractionOutline
  - type: TimedDespawn
    lifetime: 0.7

- type: entity
  id: EffectGoliathTentacleSpawn
  parent: BaseEffectGoliathTentacleSpawn
  categories: [ HideSpawnMenu ]
  name: "щупальце"
  components:
    - type: Sprite
      state: goliath_tentacle_spawn
    - type: SpawnOnDespawn
      prototype: GoliathTentacle

- type: entity
  id: EffectGoliathTentacleRetract
  parent: BaseEffectGoliathTentacleSpawn
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    state: goliath_tentacle_retract
  - type: EffectVisuals
  - type: AnimationPlayer
