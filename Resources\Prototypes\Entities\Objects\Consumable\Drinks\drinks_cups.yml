# Empty drink containers; different from bottles in that these are intended to be spawned empty
# When adding new drinks also add to random spawner located in Resources\Prototypes\Entities\Markers\Spawners\Random\Food_Drinks\drinks_soda.yml
- type: entity
  parent: BaseItem
  id: DrinkBaseCup
  name: "основна чашка"
  abstract: true
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 20
  - type: MixableSolution
    solution: drink
  - type: FitsInDispenser
    solution: drink
  - type: DrawableSolution
    solution: drink
  - type: RefillableSolution
    solution: drink
  - type: DrainableSolution
    solution: drink
  - type: SolutionTransfer
    canChangeTransferAmount: true
    maxTransferAmount: 10
  - type: UserInterface
    interfaces:
      enum.TransferAmountUiKey.Key:
        type: TransferAmountBoundUserInterface
  - type: Drink
  - type: Sprite
    state: icon
  - type: Spillable
    solution: drink
  - type: MeleeWeapon
    soundNoDamage:
      path: "/Audio/Effects/Fluids/splat.ogg"
    damage:
      types:
        Blunt: 0
  - type: EmitSoundOnPickup
    sound: /Audio/SimpleStation14/Items/Handling/drinkglass_pickup.ogg
  - type: EmitSoundOnDrop
    sound: /Audio/SimpleStation14/Items/Handling/drinkglass_drop.ogg
  - type: EmitSoundOnLand
    sound: /Audio/SimpleStation14/Items/Handling/drinkglass_drop.ogg

- type: entity
  parent: DrinkBaseCup
  id: DrinkGoldenCup
  name: "золотий кубок"
  description: "Золотий кубок."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Drinks/golden_cup.rsi

- type: entity
  parent: DrinkBaseCup
  id: DrinkBaseMug
  abstract: true
  name: "кружка"
  description: "Кухоль."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Drinks/mug.rsi
    layers:
      - state: icon-0
      - map: ["enum.SolutionContainerLayers.Fill"]
        state: icon-3
        visible: false
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 3
    fillBaseName: icon-
  - type: PhysicalComposition
    materialComposition:
      Glass: 25

- type: entity
  parent: DrinkBaseMug
  id: DrinkMug
  name: "кружка"
  description: "Звичайна біла кружка."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Drinks/mug.rsi

- type: entity
  parent: DrinkBaseMug
  id: DrinkMugBlack
  name: "чорна кружка"
  description: "Гладка чорна кружка."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Drinks/mug_black.rsi

- type: entity
  parent: DrinkBaseMug
  id: DrinkMugBlue
  name: "синя кружка"
  description: "Синьо-чорний кухоль."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Drinks/mug_blue.rsi

- type: entity
  parent: DrinkBaseMug
  id: DrinkMugGreen
  name: "зелена кружка"
  description: "Блідо-зелена та рожева кружка."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Drinks/mug_green.rsi

- type: entity
  parent: DrinkBaseCup #this one doesn't have the proper layers in the rsi
  id: DrinkMugDog
  name: "кумедна кружка з собакою"
  description: "Він схожий на мультяшного бігля."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Drinks/mug_dog.rsi
    state: icon-3

- type: entity
  parent: DrinkBaseMug
  id: DrinkMugHeart
  name: "кружка з сердечком"
  description: "На білому горнятку помітно червоне серце."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Drinks/mug_heart.rsi

- type: entity
  parent: DrinkBaseMug
  id: DrinkMugMetal
  name: "металевий кухоль"
  description: "Металевий кухоль. Ти не знаєш, з якого металу."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Drinks/mug_metal.rsi
  - type: PhysicalComposition
    materialComposition:
      Steel: 25

- type: entity
  parent: DrinkBaseMug
  id: DrinkMugMoebius
  name: "кружка мебіуса"
  description: "Горнятко з логотипом Moebius Laboratories. Навіть ваша ранкова кава не застрахована від корпоративної реклами."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Drinks/mug_moebius.rsi

- type: entity
  parent: DrinkBaseMug
  id: DrinkMugOne
  name: "#кружка № 1"
  description: "На білому горнятку помітно видніється цифра \"1\"."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Drinks/mug_one.rsi

- type: entity
  parent: DrinkBaseMug
  id: DrinkMugRainbow
  name: "райдужна кружка"
  description: "Райдужна кружка. Кольори засліплюють майже так само, як зварювальник."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Drinks/mug_rainbow.rsi

- type: entity
  parent: DrinkBaseMug
  id: DrinkMugRed
  name: "червона кружка"
  description: "Червоно-чорний кухоль."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Drinks/mug_red.rsi

- type: entity
  parent: DrinkBaseCup
  id: DrinkHotCoco
  name: "гарячий шоколад"
  description: "Підігрітий напій, що складається з розтопленого шоколаду та підігрітого молока."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: HotCocoa
          Quantity: 20
  - type: Icon
    sprite: Objects/Consumable/Drinks/hot_coco.rsi
    state: icon-vend
  - type: Sprite
    sprite: Objects/Consumable/Drinks/hot_coco.rsi
    layers:
      - state: icon-0
      - map: ["enum.SolutionContainerLayers.Fill"]
        state: icon-4
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 4
    fillBaseName: icon-
  - type: TrashOnSolutionEmpty
    solution: drink

- type: entity
  parent: DrinkBaseCup
  id: DrinkHotCoffee
  name: "кава"
  description: "Кава - це заварений напій, приготований зі смаженого насіння кавової рослини, яке зазвичай називають кавовими зернами."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Coffee
          Quantity: 20
  - type: Icon
    sprite: Objects/Consumable/Drinks/hot_coffee.rsi
    state: icon-vend
  - type: Sprite
    sprite: Objects/Consumable/Drinks/hot_coffee.rsi
    layers:
      - state: icon-0
      - map: ["enum.SolutionContainerLayers.Fill"]
        state: icon-4
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 4
    fillBaseName: icon-
  - type: TrashOnSolutionEmpty
    solution: drink

- type: entity
  parent: DrinkBaseCup
  id: DrinkCafeLatte
  name: "кафе латте"
  description: "Приємний, міцний і смачний напій, поки ви читаєте."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: CafeLatte
          Quantity: 20
  - type: Sprite
    sprite: Objects/Consumable/Drinks/cafe_latte.rsi
    layers:
      - state: icon_empty
      - state: fill-1
        map: ["enum.SolutionContainerLayers.Fill"]
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 1
    fillBaseName: fill-
    changeColor: false
  - type: TrashOnSolutionEmpty
    solution: drink

- type: entity
  parent: DrinkBaseCup
  id: DrinkTeacup
  name: "чашка"
  description: "Звичайна біла порцелянова чашка."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Tea
          Quantity: 20
  - type: Icon
    sprite: Objects/Consumable/Drinks/teacup.rsi
    state: icon-vend-tea
  - type: Sprite
    sprite: Objects/Consumable/Drinks/teacup.rsi
    layers:
      - state: icon-0
      - map: ["enum.SolutionContainerLayers.Fill"]
        state: icon-4
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 4
    fillBaseName: icon-
  - type: TrashOnSolutionEmpty
    solution: drink

- type: entity
  parent: DrinkBaseCup
  id: DrinkGreenTea
  name: "зелений чай"
  description: "Звичайна біла порцелянова чашка."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: GreenTea
          Quantity: 20
  - type: Icon
    sprite: Objects/Consumable/Drinks/teacup.rsi
    state: icon-vend-green-tea
  - type: Sprite
    sprite: Objects/Consumable/Drinks/teacup.rsi
    layers:
      - state: icon-0
      - map: ["enum.SolutionContainerLayers.Fill"]
        state: icon-4
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 4
    fillBaseName: icon-
  - type: TrashOnSolutionEmpty
    solution: drink

- type: entity
  parent: DrinkBaseCup
  id: DrinkLean
  name: "святковий стакан" # Delta V : Re-adds Lean
  description: "Часто в поєднанні з солодкою доджею, пиття цього напою є основним елементом трейлового О.Г." # Delta V : Re-Re-adds Lean
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Lean # Delta V : Re-adds Lean
          Quantity: 20
  - type: Sprite
    sprite: Objects/Consumable/Drinks/lean.rsi
    state: icon
  - type: Item
    sprite: Objects/Consumable/Drinks/lean.rsi
  - type: TrashOnSolutionEmpty
    solution: drink

- type: entity
  parent: DrinkBaseCup
  id: DrinkWaterCup
  name: "чашка для води"
  description: "Паперовий стаканчик для води."
  components:
  - type: Drink
  - type: Sprite
    sprite: Objects/Consumable/Drinks/water_cup.rsi
    layers:
      - state: icon-0
      - map: ["enum.SolutionContainerLayers.Fill"]
        state: icon-1
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 1
    fillBaseName: icon-
  - type: Tag
    tags:
    - Trash
    - WhitelistChameleon
  - type: TrashOnSolutionEmpty
    solution: drink
  - type: Clothing
    slots:
    - HEAD
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
    quickEquip: false
    sprite: Clothing/Head/Hats/party_water_cup.rsi
