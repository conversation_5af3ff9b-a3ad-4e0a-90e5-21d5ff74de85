using Content.Server.Administration;
using Content.Server._Pirate.DNDMage;
using Content.Shared.Administration;
using Content.Shared._Pirate.DNDMage;
using Content.Shared.Damage;
using Content.Shared.Mobs;
using Content.Shared.Mobs.Components;
using Robust.Shared.Console;
using Robust.Shared.GameObjects;

namespace Content.Server._Pirate.DNDMage.Commands;

[AdminCommand(AdminFlags.Fun)]
public sealed class DNDNecromancyCommand : IConsoleCommand
{
    public string Command => "dndnecromancy";
    public string Description => "Тестує систему некромантії DND";
    public string Help => "dndnecromancy <action> [args]\n" +
                          "Actions:\n" +
                          "  resurrect <corpseId> <mageId> [duration] - Воскрешає труп як слугу\n" +
                          "  kill <corpseId> - Вбиває сутність\n" +
                          "  makemage <entityId> - Робить сутність магом\n" +
                          "  servants <mageId> - Показує слуг мага\n" +
                          "  info <servantId> - Показує інформацію про слугу";

    public void Execute(IConsoleShell shell, string argStr, string[] args)
    {
        if (args.Length < 1)
        {
            shell.WriteLine(Help);
            return;
        }

        var entManager = IoCManager.Resolve<IEntityManager>();
        var necromancySystem = entManager.System<DNDNecromancyServantSystem>();
        var damageableSystem = entManager.System<DamageableSystem>();

        switch (args[0].ToLower())
        {
            case "resurrect":
                if (args.Length < 3)
                {
                    shell.WriteLine("Використання: dndnecromancy resurrect <corpseId> <mageId> [duration]");
                    return;
                }

                if (!NetEntity.TryParse(args[1], out var corpseNetId) || 
                    !entManager.TryGetEntity(corpseNetId, out var corpseId))
                {
                    shell.WriteLine($"Неправильний ID трупа: {args[1]}");
                    return;
                }

                if (!NetEntity.TryParse(args[2], out var mageNetId) || 
                    !entManager.TryGetEntity(mageNetId, out var mageId))
                {
                    shell.WriteLine($"Неправильний ID мага: {args[2]}");
                    return;
                }

                var duration = 300f;
                if (args.Length > 3 && !float.TryParse(args[3], out duration))
                {
                    shell.WriteLine($"Неправильна тривалість: {args[3]}");
                    return;
                }

                // Додаємо компонент мага, якщо його немає
                if (!entManager.HasComponent<DNDMageComponent>(mageId.Value))
                {
                    entManager.AddComponent<DNDMageComponent>(mageId.Value);
                    shell.WriteLine($"Додано компонент мага до {mageId}");
                }

                var success = necromancySystem.TryResurrectAsServant(corpseId.Value, mageId.Value, duration);
                if (success)
                {
                    shell.WriteLine($"Успішно воскрешено {corpseId} як слугу {mageId} на {duration} секунд");
                }
                else
                {
                    shell.WriteLine($"Не вдалося воскресити {corpseId}");
                }
                break;

            case "kill":
                if (args.Length < 2)
                {
                    shell.WriteLine("Використання: dndnecromancy kill <entityId>");
                    return;
                }

                if (!NetEntity.TryParse(args[1], out var killNetId) || 
                    !entManager.TryGetEntity(killNetId, out var killId))
                {
                    shell.WriteLine($"Неправильний ID сутності: {args[1]}");
                    return;
                }

                var lethalDamage = new DamageSpecifier();
                lethalDamage.DamageDict.Add("Blunt", 1000);
                damageableSystem.TryChangeDamage(killId.Value, lethalDamage);
                shell.WriteLine($"Завдано смертельне пошкодження {killId}");
                break;

            case "makemage":
                if (args.Length < 2)
                {
                    shell.WriteLine("Використання: dndnecromancy makemage <entityId>");
                    return;
                }

                if (!NetEntity.TryParse(args[1], out var mageNetId2) || 
                    !entManager.TryGetEntity(mageNetId2, out var mageId2))
                {
                    shell.WriteLine($"Неправильний ID сутності: {args[1]}");
                    return;
                }

                if (entManager.HasComponent<DNDMageComponent>(mageId2.Value))
                {
                    shell.WriteLine($"{mageId2} вже є магом");
                }
                else
                {
                    entManager.AddComponent<DNDMageComponent>(mageId2.Value);
                    shell.WriteLine($"Зроблено {mageId2} магом");
                }
                break;

            case "servants":
                if (args.Length < 2)
                {
                    shell.WriteLine("Використання: dndnecromancy servants <mageId>");
                    return;
                }

                if (!NetEntity.TryParse(args[1], out var servantMageNetId) || 
                    !entManager.TryGetEntity(servantMageNetId, out var servantMageId))
                {
                    shell.WriteLine($"Неправильний ID мага: {args[1]}");
                    return;
                }

                var servants = necromancySystem.GetServantsOfMaster(servantMageId.Value);
                shell.WriteLine($"Слуги мага {servantMageId} ({servants.Count}):");
                foreach (var servant in servants)
                {
                    var name = entManager.TryGetComponent<MetaDataComponent>(servant, out var meta) ? meta.EntityName : "Невідомий";
                    shell.WriteLine($"  - {servant} ({name})");
                }
                break;

            case "info":
                if (args.Length < 2)
                {
                    shell.WriteLine("Використання: dndnecromancy info <servantId>");
                    return;
                }

                if (!NetEntity.TryParse(args[1], out var infoNetId) || 
                    !entManager.TryGetEntity(infoNetId, out var infoId))
                {
                    shell.WriteLine($"Неправильний ID слуги: {args[1]}");
                    return;
                }

                if (!entManager.TryGetComponent<DNDNecromancyServantComponent>(infoId.Value, out var servantComp))
                {
                    shell.WriteLine($"{infoId} не є слугою некроманта");
                    return;
                }

                var masterName = servantComp.Master != EntityUid.Invalid && 
                                entManager.TryGetComponent<MetaDataComponent>(servantComp.Master, out var masterMeta) 
                                ? masterMeta.EntityName : "Невідомий";

                shell.WriteLine($"Інформація про слугу {infoId}:");
                shell.WriteLine($"  Майстер: {servantComp.Master} ({masterName})");
                shell.WriteLine($"  Тривалість: {servantComp.ServantDuration:F1}с");
                shell.WriteLine($"  Тип: {servantComp.Type}");
                shell.WriteLine($"  Лояльність: {servantComp.Loyalty:F1}/{servantComp.MaxLoyalty:F1}");
                shell.WriteLine($"  Команда: {servantComp.CurrentCommand}");
                shell.WriteLine($"  Активний: {servantComp.IsActive}");
                shell.WriteLine($"  Слідує за майстром: {servantComp.ShouldFollowMaster}");
                shell.WriteLine($"  Може атакувати: {servantComp.CanAttackEnemies}");
                shell.WriteLine($"  Захищає майстра: {servantComp.ShouldProtectMaster}");
                break;

            default:
                shell.WriteLine($"Невідома дія: {args[0]}");
                shell.WriteLine(Help);
                break;
        }
    }
}
