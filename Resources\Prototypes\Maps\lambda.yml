- type: gameMap
  id: Lambda
  mapName: 'Лямбда'
  mapPath: /Maps/lambda.yml
  minPlayers: 15
  fallback: true
  stations:
    Lambda:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: '{0} Lambda Station {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: 'TG'
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/emergency_raven.yml
        - type: StationCargoShuttle
          path: /Maps/Shuttles/cargo_lambda.yml
        - type: StationJobs
          availableJobs:
          #command
            Captain: [ 1, 1 ]
            NanotrasenRepresentative: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1 ]
            # Magistrate: [ 1, 1 ] # EE: Disabled
            # AdministrativeAssistant: [ 1, 1 ] # EE: Disabled
          #service
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 2, 2 ]
            Botanist: [ 2 , 3 ]
            Chef: [ 1, 2 ]
            Janitor: [ 2, 3 ]
          #engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 2, 4 ]
            StationEngineer: [ 4, 6 ]
            TechnicalAssistant: [ 2, 4 ]
            SeniorEngineer: [ 1, 1 ]
          #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            Chemist: [ 1, 2 ]
            MedicalDoctor: [ 4, 5 ]
            MedicalIntern: [ 3, 4 ]
            Paramedic: [ 1, 2 ]
            SeniorPhysician: [ 1, 1 ]
          #science
            ResearchDirector: [ 1, 1 ]
            Scientist: [ 4, 6 ]
            Roboticist: [ 2, 3 ]
            ForensicMantis: [ 1, 1 ]
            Chaplain: [ 1, 1 ]
            Librarian: [ 1, 1 ]
            ResearchAssistant: [ 3, 4 ]
            SeniorResearcher: [ 1, 1 ]
          #security
            HeadOfSecurity: [ 1, 1 ]
            Warden: [ 1, 1 ]
            Prisoner: [ 2, 3 ]
            # PrisonGuard: [ 2, 3 ] # EE: Disabled
            SecurityOfficer: [ 5, 6 ]
            SecurityCadet: [ 3, 4 ]
            Detective: [ 1, 1 ]
            SeniorOfficer: [ 1, 1 ]
          #supply
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ 3, 4 ]
            CargoTechnician: [ 4, 6 ]
          #civilian
            Passenger: [ -1, -1 ]
            Clown: [ 2, 2 ]
            Mime: [ 1, 1 ]
            Musician: [ 1, 1 ]
          #Silicon
            Borg: [ 2, 3 ]
            MedicalBorg: [ 1, 2 ]
            StationAi: [ 1, 1 ]
        # blob-config-start HUGE
        - type: StationBlobConfig
          stageBegin: 40
          stageCritical: 450
          stageTheEnd: 900
        # blob-config-end
