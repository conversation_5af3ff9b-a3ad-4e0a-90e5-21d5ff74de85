abductors-ui-beacons = Beacons
abductors-ui-teleport = Teleport
abductors-ui-attract = Attract

abductors-ui-experiment = Experiment
abductors-ui-complete-experiment = Complete the experiment

abductors-ui-gizmo-transferred = Target information transferred

abductors-ui-armor-control = Armor Control
abductors-ui-combat-mode = Combat Mode
abductors-ui-stealth-mode = Stealth Mode
abductors-ui-lock-armor = Lock Armor
abductors-ui-unlock-armor = Unlock Armor
abductors-ui-vest-linked = Vest linked

abductors-title = Abductors
abductors-description = Abductors have targeted the station. Avoid getting kidnapped by them!

abductor-lone-ghost-role-name = Lone Abductor
abductor-lone-ghost-role-desc = Kidnap people, and stuff them with experimental organs of dubious origin, all by yourself.

abductor-scientist-ghost-role-name = Abductor Scientist
abductor-scientist-ghost-role-desc = Teleport people your partner kidnapped onto your ship and stuff them with experimental organs of dubious origin.

abductor-agent-ghost-role-name = Abductor Agent
abductor-agent-ghost-role-desc = Kidnap people for your partner to stuff them with experimental organs of dubious origin.

abductors-ghost-role-rules = You are an [color=red][bold]Abductor[/bold][/color].
                            Your intentions are to abduct people from the station and replace their organs with various experimental devices,
                            after which you return them back. You are not allowed to destroy the station or intentionally kill people.
                            It is in your interest to return the test subjects alive and healthy for the purity of the experiment.

                            You don't remember any of your previous life, and you don't remember anything you learned as a ghost.
                            You are allowed to remember knowledge about the game in general, such as how to cook, how to use objects, etc.
                            You are absolutely [color=red]NOT[/color] allowed to remember, say, the name, appearance, etc. of your previous character.

abductor-round-end-agent-name = abductor

objective-issuer-abductors = [color=#FD0098]Mothership[/color]

objective-condition-abduct-title = Abduct {$count} person.
objective-condition-abduct-description = (use the Gizmo on a subdued victim, then use the Gizmo on the abductor console and select the attract action), then replace their heart with one of the glands, put them in the experimenter, and press complete experiment.

abductor-role-greeting = I am a professional combat scientist of a high-tech race. My task is to abduct humans, conduct experiments on them, and return them alive for the purity of the experiment. It is not in my interest to destroy the station, kill, or assist the crew.

roles-antag-abductor-objective = Kidnap station crew and perform your experiments on them!

abductor-victim-role-greeting = You have seen things you shouldn't have. The world must know the truth.
abductor-victim-role-name = Abductee
abductor-victim-role-desc = You have seen things you shouldn't have. The world must know the truth.

objective-issuer-voices = [color=#FD0098]The Voices[/color]
abductor-ui-pad-found = pad: [color=green]connected[/color]
abductor-ui-pad-not-found = pad: [color=red]not found[/color]
abductor-ui-target-none = target: [color=red]NONE[/color]
abductor-ui-target-found = target: [color=green]{$target}[/color]
abductor-ui-experimentator-connected = experimentator: [color=green]connected[/color]
abductor-ui-experimentator-not-found = experimentator: [color=red]not found[/color]
abductor-ui-victim-none = victim: [color=red]NONE[/color]
abductor-ui-victim-found = victim: [color=green]{$victim}[/color]
abductor-ui-armor-plug-in = [color=red][font size=16]You need to plug in abductor armor![/font][/color]
