- type: constructionGraph
  id: Table
  start: start
  graph:
    - node: start
      actions:
        - !type:DeleteEntity {}
      edges:
        - to: TableFrame
          completed:
            - !type:SnapToGrid
              southRotation: true
          steps:
            - material: MetalRod
              amount: 2
              doAfter: 1
        - to: CounterWoodFrame
          completed:
            - !type:SnapToGrid
              southRotation: true
          steps:
            - material: WoodPlank
              amount: 1
              doAfter: 1
        - to: CounterMetalFrame
          completed:
            - !type:SnapToGrid
              southRotation: true
          steps:
            - material: Steel
              amount: 1
              doAfter: 1


    - node: TableFrame
      entity: TableFrame
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
                prototype: PartRodMetal
                amount: 2
          steps:
            - tool: Anchoring
              doAfter: 1

        - to: Table
          steps:
            - material: Steel
              amount: 1
              doAfter: 1

        - to: TableReinforced
          steps:
            - material: Plasteel
              amount: 1
              doAfter: 1

        - to: TableGlass
          steps:
            - material: Glass
              amount: 1
              doAfter: 1

        - to: TableReinforcedGlass
          steps:
            - material: ReinforcedGlass
              amount: 1
              doAfter: 1

        - to: TablePlasmaGlass
          steps:
            - material: PlasmaGlass
              amount: 1
              doAfter: 1

        - to: TableUraniumGlass # Pirate edit uranium table
          steps:
            - material: UraniumGlass
              amount: 1
              doAfter: 1

        - to: TableBrass
          steps:
            - material: Brass
              amount: 1
              doAfter: 1

        - to: TableWood
          steps:
            - material: WoodPlank
              amount: 1
              doAfter: 1


    - node: CounterWoodFrame
      entity: CounterWoodFrame
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: MaterialWoodPlank1
              amount: 1
          steps:
            - tool: Anchoring
              doAfter: 1
        - to: CounterWood
          steps:
            - material: WoodPlank
              amount: 1
              doAfter: 1

    - node: CounterMetalFrame
      entity: CounterMetalFrame
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: SheetSteel1
              amount: 1
          steps:
          - tool: Anchoring
            doAfter: 1
        - to: CounterMetal
          steps:
            - material: Steel
              amount: 1
              doAfter: 1


    - node: Table
      entity: Table
      edges:
        - to: TableFrame
          completed:
            - !type:SpawnPrototype
              prototype: SheetSteel1
              amount: 1
          steps:
            - tool: Anchoring
              doAfter: 1

    - node: TableReinforced
      entity: TableReinforced
      edges:
        - to: TableFrame
          completed:
            - !type:SpawnPrototype
              prototype: SheetPlasteel1
              amount: 1
          steps:
            - tool: Anchoring
              doAfter: 1

    - node: TableGlass
      entity: TableGlass
      edges:
        - to: TableFrame
          completed:
            - !type:SpawnPrototype
              prototype: SheetGlass1
              amount: 1
          steps:
            - tool: Anchoring
              doAfter: 1

    - node: TableReinforcedGlass
      entity: TableReinforcedGlass
      edges:
        - to: TableFrame
          completed:
            - !type:SpawnPrototype
              prototype: SheetRGlass1
              amount: 1
          steps:
            - tool: Anchoring
              doAfter: 1

    - node: TablePlasmaGlass
      entity: TablePlasmaGlass
      edges:
        - to: TableFrame
          completed:
            - !type:SpawnPrototype
              prototype: SheetPGlass1
              amount: 1
          steps:
            - tool: Anchoring
              doAfter: 1

    - node: TableUraniumGlass # Pirate edit uranium table
      entity: TableUraniumGlass
      edges:
        - to: TableFrame
          completed:
            - !type:SpawnPrototype
              prototype: SheetUGlass1
              amount: 1
          steps:
            - tool: Anchoring
              doAfter: 1

    - node: TableBrass
      entity: TableBrass
      edges:
        - to: TableFrame
          completed:
            - !type:SpawnPrototype
              prototype: SheetBrass1
              amount: 1
          steps:
            - tool: Prying
              doAfter: 1

    - node: TableWood
      entity: TableWood
      edges:
        - to: TableFrame
          completed:
            - !type:SpawnPrototype
              prototype: MaterialWoodPlank1
              amount: 1
          steps:
            - tool: Prying
              doAfter: 1

        - to: TableCarpet
          steps:
            - material: Cloth
              amount: 1
              doAfter: 1
              
        - to: TableFancyBlack
          steps: 
            - material: FloorCarpetBlack
              amount: 1
              doAfter: 1
              
        - to: TableFancyBlue
          steps: 
            - material: FloorCarpetBlue
              amount: 1
              doAfter: 1
              
        - to: TableFancyCyan
          steps: 
            - material: FloorCarpetCyan
              amount: 1
              doAfter: 1
              
        - to: TableFancyGreen
          steps: 
            - material: FloorCarpetGreen
              amount: 1
              doAfter: 1
              
        - to: TableFancyOrange
          steps: 
            - material: FloorCarpetOrange
              amount: 1
              doAfter: 1
              
        - to: TableFancyPurple
          steps: 
            - material: FloorCarpetPurple
              amount: 1
              doAfter: 1
                
        - to: TableFancyPink
          steps: 
            - material: FloorCarpetPink
              amount: 1
              doAfter: 1
              
        - to: TableFancyRed
          steps: 
            - material: FloorCarpetRed
              amount: 1
              doAfter: 1
              
        - to: TableFancyWhite
          steps: 
            - material: FloorCarpetWhite
              amount: 1
              doAfter: 1

        - to: TableWoodReinforced #Nyano - See Resources/Prototypes/Nyanotrasen/Entities/Furniture/Tables/tables.yml
          steps:
            - material: WoodPlank
              amount: 2
              doAfter: 1

    - node: TableCarpet
      entity: TableCarpet
      edges:
        - to: TableWood
          completed:
            - !type:SpawnPrototype
              prototype: MaterialCloth1
              amount: 1
          steps:
            - tool: Prying
              doAfter: 1
              
    - node: TableFancyBlack
      entity: TableFancyBlack
      edges:
        - to: TableWood
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemBlack
              amount: 1
          steps:
            - tool: Prying
              doAfter: 1
              
    - node: TableFancyBlue
      entity: TableFancyBlue
      edges:
        - to: TableWood
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemBlue
              amount: 1
          steps:
            - tool: Prying
              doAfter: 1
              
    - node: TableFancyCyan
      entity: TableFancyCyan
      edges:
        - to: TableWood
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemCyan
              amount: 1
          steps:
            - tool: Prying
              doAfter: 1

    - node: TableFancyGreen
      entity: TableFancyGreen
      edges:
        - to: TableWood
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemGreen
              amount: 1
          steps:
            - tool: Prying
              doAfter: 1

    - node: TableFancyOrange
      entity: TableFancyOrange
      edges:
        - to: TableWood
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemOrange
              amount: 1
          steps:
            - tool: Prying
              doAfter: 1

    - node: TableFancyPurple
      entity: TableFancyPurple
      edges:
        - to: TableWood
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemPurple
              amount: 1
          steps:
            - tool: Prying
              doAfter: 1

    - node: TableFancyPink
      entity: TableFancyPink
      edges:
        - to: TableWood
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemPink
              amount: 1
          steps:
            - tool: Prying
              doAfter: 1

    - node: TableFancyRed
      entity: TableFancyRed
      edges:
        - to: TableWood
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemRed
              amount: 1
          steps:
            - tool: Prying
              doAfter: 1

    - node: TableFancyWhite
      entity: TableFancyWhite
      edges:
        - to: TableWood
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemWhite
              amount: 1
          steps:
            - tool: Prying
              doAfter: 1

    - node: TableWoodReinforced #Nyano - See Resources/Prototypes/Nyanotrasen/Entities/Furniture/Tables/tables.yml
      entity: TableWoodReinforced
      edges:
        - to: TableWood
          completed:
            - !type:SpawnPrototype
              prototype: MaterialWoodPlank1
              amount: 2
          steps:
            - tool: Prying
              doAfter: 1

    - node: CounterMetal
      entity: TableCounterMetal
      edges:
        - to: CounterMetalFrame
          completed:
            - !type:SpawnPrototype
              prototype: SheetSteel1
              amount: 1
          steps:
            - tool: Anchoring
              doAfter: 1

    - node: CounterWood
      entity: TableCounterWood
      edges:
        - to: CounterWoodFrame
          completed:
            - !type:SpawnPrototype
              prototype: MaterialWoodPlank1
              amount: 1
          steps:
            - tool: Prying
              doAfter: 1
