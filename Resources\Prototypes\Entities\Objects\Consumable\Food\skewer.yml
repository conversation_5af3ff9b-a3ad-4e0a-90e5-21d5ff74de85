# When adding new food also add to random spawner located in Resources\Prototypes\Entities\Markers\Spawners\Random\Food_Drinks\food_single.yml
# Kebabs

- type: entity
  name: "шампур"
  parent: BaseItem
  id: FoodKebabSkewer
  description: "Тонкий металевий стрижень, на який нанизували практично все, що завгодно, і готували."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/skewer.rsi
    state: 
    layers:
    - state: skewer
    - map: ["foodSequenceLayers"]
  - type: LandAtCursor
  - type: Fixtures
    fixtures:
      fix1: 
        shape: !type:PolygonShape
          vertices:
            - -0.40,-0.20
            - -0.30,-0.30
            - 0.50,0.10
            - 0.40,0.20
        density: 20
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2
  - type: DamageOtherOnHit
    damage:
      types:
        Piercing: 6
  - type: ThrowingAngle
    angle: 245
  - type: EmbeddableProjectile
    offset: -0.15,0.0
  - type: MeleeWeapon
    wideAnimationRotation: -120
    damage:
      types:
        Piercing: 8
    angle: 0
    animation: WeaponArcThrust
    soundHit:
      path: /Audio/Weapons/bladeslice.ogg
  - type: Tag
    tags:
    - Trash
    - Skewer
  - type: Food
    trash: 
    - FoodKebabSkewer
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 2

- type: entity
  name: "подвійний щурячий шашлик"
  parent: FoodKebabSkewer
  id: FoodMeatRatdoubleKebab
  description: "Подвійна порція не дуже смачного щурячого м'яса на паличці."
  components:
  - type: Sprite
    layers:
      - state: skewer
      - state: skewer-rat
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 12
        - ReagentId: Vitamin
          Quantity: 6

- type: entity
  name: "шашлик фієста"
  parent: FoodKebabSkewer
  id: FoodMeatFiestaKebab
  description: "Завжди вечірка на круїзному лайнері десь у світі, чи не так?"
  components:
  - type: Sprite
    layers:
      - state: skewer
      - state: skewer-pepper
      - state: skewer-corn
      - state: skewer-mushroom
      - state: skewer-tomato

- type: entity
  name: "зміїний шашлик"
  parent: FoodKebabSkewer
  id: FoodMeatSnakeKebab
  description: "Зміїне м'ясо на паличці. Трохи жорсткувате."
  components:
  - type: Sprite
    layers:
      - state: skewer
      - state: skewer-snake
  - type: SolutionContainerManager
    solutions:
      food:
        canReact: false # Dont want cause reactions inside skewers after merging ingredients
        maxVol: 0
  - type: FoodSequenceStartPoint
    key: Skewer
    maxLayers: 4
    startPosition: -0.27, -0.19
    inverseLayers: true 
    offset: 0.2, 0.1
    nameGeneration: food-sequence-skewer-gen
    contentSeparator: ", "
    allowHorizontalFlip: false