using System.Linq;
using System.Numerics;
using Content.Shared.Roles;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;
using Robust.Shared.Utility;
using Robust.Client.Utility;
using Robust.Shared.Player;


namespace Content.Client.ReadyManifest;


[GenerateTypedNameReferences]
public sealed partial class ReadyManifestUi : DefaultWindow
{
    private readonly IEntitySystemManager _entitySystem;
    private readonly IPrototypeManager _prototypeManager;
    private readonly ISharedPlayerManager _playerManager;
    private readonly Dictionary<string, BoxContainer> _jobCategories;

    public ReadyManifestUi()
    {
        RobustXamlLoader.Load(this);
        _jobCategories = new Dictionary<string, BoxContainer>();
        _prototypeManager = IoCManager.Resolve<IPrototypeManager>();
        _playerManager = IoCManager.Resolve<ISharedPlayerManager>();
        _entitySystem = IoCManager.Resolve<IEntitySystemManager>();
    }

    // Currently rebuilds the UI every time it's updated, should probably split department lookup and job counts into separate functions
    public void RebuildUI(Dictionary<ProtoId<JobPrototype>, int> jobCounts)
    {
        ReadyManifestListing.DisposeAllChildren();

        _jobCategories.Clear();
        var departments = new List<DepartmentPrototype>();

        foreach (var department in _prototypeManager.EnumeratePrototypes<DepartmentPrototype>())
        {
            if (department.EditorHidden)
                continue;

            departments.Add(department);
        }

        departments.Sort(DepartmentUIComparer.Instance);

        foreach (var department in departments)
        {
            var departmentName = Loc.GetString($"department-{department.ID}");

            if (!_jobCategories.TryGetValue(department.ID, out var category))
            {
                category = new BoxContainer
                {
                    Orientation = BoxContainer.LayoutOrientation.Vertical,
                    HorizontalExpand = true,
                    Name = department.ID,
                    ToolTip = Loc.GetString("humanoid-profile-editor-jobs-amount-in-department-tooltip",
                        ("departmentName", departmentName))
                };

                category.AddChild(new Label()
                {
                    StyleClasses = { "LabelBig" },
                    Text = Loc.GetString($"department-{department.ID}")
                });

                _jobCategories[department.ID] = category;
                ReadyManifestListing.AddChild(category);
            }

            var jobs = department.Roles.Select(jobId => _prototypeManager.Index(jobId))
                    .Where(job => job.SetPreference)
                    .ToArray();

            Array.Sort(jobs, JobUIComparer.Instance);

            foreach (var job in jobs)
            {

                var gridContainer = new GridContainer()
                {
                    HorizontalExpand = true,
                    Columns = 2
                };

                var jobContainer = new BoxContainer()
                {
                    Orientation = BoxContainer.LayoutOrientation.Horizontal,
                };

                var title = new RichTextLabel()
                {
                    HorizontalExpand = true
                };
                title.SetMessage(job.LocalizedName + ":");

                var icon = new TextureRect
                {
                    TextureScale = new Vector2(2, 2),
                    VerticalAlignment = VAlignment.Center,
                    Margin = new Thickness(0, 0, 4, 0)
                };

                var readyCount = new RichTextLabel()
                {
                    HorizontalExpand = true
                };

                if (jobCounts.ContainsKey(job.ID))
                {
                    var jobCount = jobCounts[job.ID];
                    var color = jobCount > 0 ? Color.White : Color.Red;
                    readyCount.SetMessage(jobCount.ToString(), null, color);
                }
                else
                {
                    readyCount.SetMessage("0", null, Color.Red);
                }

                var jobIcon = _prototypeManager.Index(job.Icon);
                icon.Texture = jobIcon.Icon.Frame0();
                jobContainer.AddChild(icon);
                jobContainer.AddChild(title);
                gridContainer.AddChild(jobContainer);
                gridContainer.AddChild(readyCount);
                category.AddChild(gridContainer);
            }
        }
    }
}