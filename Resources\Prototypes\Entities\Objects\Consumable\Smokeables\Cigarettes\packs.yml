- type: entity
  id: CigPackBase
  parent: [ BaseStorageItem, BaseBagOpenClose ]
  name: "пачка сигарет"
  abstract: true
  components:
  - type: Sprite
    layers:
    - state: closed
    - state: open
      map: ["openLayer"]
      visible: false
    - state: cig1
      map: ["cig1"]
      sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/visualizer.rsi
      visible: false
    - state: cig2
      map: ["cig2"]
      sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/visualizer.rsi
      visible: false
    - state: cig3
      map: ["cig3"]
      sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/visualizer.rsi
      visible: false
    - state: cig4
      map: ["cig4"]
      sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/visualizer.rsi
      visible: false
    - state: cig5
      map: ["cig5"]
      sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/visualizer.rsi
      visible: false
    - state: cig6
      map: ["cig6"]
      sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/visualizer.rsi
      visible: false
  - type: Tag
    tags:
    - CigPack
    - Trash
  - type: PhysicalComposition
    materialComposition:
      Steel: 50
  - type: SpaceGarbage
  - type: Item
    size: Tiny
    shape: # Yes, this is cursed, but it breaks otherwise, dont question it.
    - 0,0,0,1
  - type: Storage
    grid:
    - 0,0,4,1
  - type: StorageFill
    contents:
    - id: Cigarette
      amount: 10
  - type: ItemCounter
    count:
      tags: [Cigarette]
    composite: true
    layerStates:
    - cig1
    - cig2
    - cig3
    - cig4
    - cig5
    - cig6
  - type: Appearance

- type: entity
  id: CigPackMixedBase
  parent: [ BaseStorageItem, BaseBagOpenClose ]
  name: "розмочена пачка сигарет"
  abstract: true
  components:
  - type: Sprite
    layers:
    - state: closed
    - state: open
      map: ["openLayer"]
      visible: false
    - state: cig1
      map: ["cig1"]
      sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/visualizer.rsi
      visible: false
    - state: cig2
      map: ["cig2"]
      sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/visualizer.rsi
      visible: false
    - state: cig3
      map: ["cig3"]
      sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/visualizer.rsi
      visible: false
    - state: cig4
      map: ["cig4"]
      sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/visualizer.rsi
      visible: false
    - state: cig5
      map: ["cig5"]
      sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/visualizer.rsi
      visible: false
    - state: cig6
      map: ["cig6"]
      sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/visualizer.rsi
      visible: false
  - type: Tag
    tags:
    - CigPack
    - Trash
  - type: PhysicalComposition
    materialComposition:
      Steel: 50
  - type: SpaceGarbage
  - type: Item
    size: Tiny
    shape: # Yes, this is cursed, but it breaks otherwise, dont question it.
    - 0,0,0,1
  - type: Storage
    grid:
    - 0,0,4,1
  - type: StorageFill
    contents:
    - id: CigaretteRandom
      amount: 10
  - type: ItemCounter
    count:
      tags: [Cigarette]
    composite: true
    layerStates:
    - cig1
    - cig2
    - cig3
    - cig4
    - cig5
    - cig6
  - type: Appearance

- type: entity
  id: CigPackGreen
  parent: CigPackBase
  name: "пачка Spessman's Smokes"
  description: "Етикетка на упаковці говорить: \"Хіба повільна смерть не змінила б ситуацію?"
  components:
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/green.rsi
  - type: Item
    sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/green.rsi

- type: entity
  id: CigPackRed
  parent: CigPackBase
  name: "пачка DromedaryCo"
  description: "Найпопулярніший бренд Space Cigarettes, спонсорів космічної олімпіади."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/red.rsi
  - type: Item
    sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/red.rsi

- type: entity
  id: CigPackBlue
  parent: CigPackBase
  name: "пачка AcmeCo"
  description: "Для тих, хто так чи інакше хоче встановити рекорд з найбільшої кількості ракових пухлин."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/blue.rsi
  - type: Item
    sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/blue.rsi

- type: entity
  id: CigPackBlack
  parent: CigPackBase
  name: "пачка Nomads"
  description: "Nomads - дуже сильний, для тих випадків, коли життя стає ще складнішим."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/black.rsi
  - type: Item
    sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/black.rsi

- type: entity
  id: CigPackSyndicate
  parent: CigPackBase
  name: "пачка Interdyne herbals"
  description: "Лікувальні сигарети преміум-класу від корпорації Interdyne. Не схвалені Головним хірургом Терра-Губернаторства."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/syndicate.rsi
  - type: Item
    sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/syndicate.rsi
  - type: StorageFill
    contents:
    - id: CigaretteSyndicate
      amount: 5

- type: entity
  id: CigPackMixedMedical
  parent: CigPackMixedBase
  name: "пачка Dan's Soaked"
  suffix: Medical
  description: "Ден працював з компанією Хімія НТ над утилізацією надлишкових хімікатів, НАСОЛОДЖУЙТЕСЯ."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/mixed.rsi
  - type: Item
    sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/mixed.rsi
  - type: StorageFill
    contents:
    - id: CigaretteIron
    - id: CigaretteTricordrazine
    - id: CigaretteDylovene
    - id: CigaretteDermaline
    - id: CigaretteArithrazine
    - id: CigaretteBicaridine
    - id: CigaretteIpecac
    - id: CigaretteOmnizine
      prob: 0.25
    - id: CigaretteDexalin
      prob: 0.25
    - id: CigarettePax
      prob: 0.10


- type: entity
  id: CigPackMixed
  parent: CigPackMixedBase
  name: "пачка Dan's Soaked"
  suffix: Mixed
  description: "Ден працював з компанією Хімія НТ над утилізацією надлишкових хімікатів, НАСОЛОДЖУЙТЕСЯ."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/mixed.rsi
  - type: Item
    sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/mixed.rsi
  - type: StorageFill
    contents:
    - id: CigaretteBbqSauce
      prob: 0.70
    - id: CigaretteBlackPepper
      prob: 0.70
    - id: CigaretteCapsaicinOil
      prob: 0.70
    - id: CigaretteBread
      prob: 0.70
    - id: CigaretteMilk
      prob: 0.70
    - id: CigaretteBanana
      prob: 0.10
    - id: CigaretteTHC
      prob: 0.70
    - id: CigaretteTricordrazine
      prob: 0.25
    - id: CigaretteSpaceDrugs
      prob: 0.50
    - id: CigaretteLicoxide
      prob: 0.10

- type: entity
  id: CigPackMixedNasty
  parent: CigPackMixedBase
  name: "пачка Dan's Soaked"
  suffix: Nasty
  description: "Ден працював з компанією Хімія НТ над утилізацією надлишкових хімікатів, НАСОЛОДЖУЙТЕСЯ."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/mixed.rsi
  - type: Item
    sprite: Objects/Consumable/Smokeables/Cigarettes/Packs/mixed.rsi
  - type: StorageFill
    contents:
    - id: CigaretteSpaceDrugs
      amount: 2
    - id: CigaretteWeldingFuel
      amount: 2
    - id: CigaretteMold
      amount: 2
    - id: CigaretteTHC
    - id: CigaretteLicoxide
      prob: 0.50
    - id: CigarettePax
      prob: 0.10
    - id: CigaretteMuteToxin
      prob: 0.05
