﻿- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitChameleon
  name: "чорний комбінезон"
  description: "Універсальний чорний комбінезон без розпізнавальних знаків."
  suffix: Chameleon
  components:
    - type: Tag
      tags: [] # ignore "WhitelistChameleon" tag
    - type: Sprite
      sprite: Clothing/Uniforms/Jumpsuit/color.rsi
      layers:
      - state: icon
        color: "#3f3f3f"
      - state: trinkets-icon
    - type: Item
      inhandVisuals:
        left:
        - state: inhand-left
          color: "#3f3f3f"
        - state: trinkets-inhand-left
        right:
        - state: inhand-right
          color: "#3f3f3f"
        - state: trinkets-inhand-right
    - type: Clothing
      sprite: Clothing/Shoes/color.rsi
      clothingVisuals:
        shoes:
        - state: equipped-FEET
          color: "#1d1d1d"
        - state: soles-equipped-FEET
    - type: SuitSensor
      randomMode: false
      mode: SensorOff
    - type: ChameleonClothing
      slot: [innerclothing]
      default: ClothingUniformJumpsuitColorBlack
    - type: UserInterface
      interfaces:
        enum.ChameleonUiKey.Key:
          type: ChameleonBoundUserInterface
