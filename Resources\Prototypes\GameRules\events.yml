- type: entity
  id: BaseStationEvent
  parent: BaseGameRule
  abstract: true
  categories: [ HideSpawnMenu ]
  components:
  - type: GameRule
    delay:
      min:  10
      max:  20

- type: entity
  id: BaseStationEventShortDelay
  parent: BaseGameRule
  abstract: true
  categories: [ HideSpawnMenu ]
  components:
  - type: GameRule
    delay:
      min:  10
      max:  20

- type: entity
  id: BaseStationEventLongDelay
  parent: BaseGameRule
  abstract: true
  categories: [ HideSpawnMenu ]
  components:
  - type: GameRule
    delay:
      min:  40
      max:  60

- type: entity
  id: AnomalySpawn
  parent: BaseStationEventLongDelay
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    weight: 8
    duration: 35
  - type: AnomalySpawnRule

- type: entity
  id: BluespaceArtifact
  parent: BaseStationEventLongDelay
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    weight: 8
    duration: 35
  - type: BluespaceArtifactRule

- type: entity
  id: BluespaceLocker
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    weight: 2
    reoccurrenceDelay: 5
    earliestStart: 1
    duration: 1
  - type: BluespaceLockerRule

- type: entity
  id: BreakerFlip
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    weight: 7
    duration: 1
    minimumPlayers: 15
  - type: BreakerFlipRule

- type: entity
  id: BureaucraticError
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    startAnnouncement: true
    minimumPlayers: 25
    weight: 3
    duration: 1
  - type: BureaucraticErrorRule

- type: entity
  id: ClericalError
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    startAnnouncement: true
    minimumPlayers: 15
    weight: 5
    duration: 1
  - type: ClericalErrorRule

#- type: entity #REMOVED BY PIRATES, NICHOLAS REQUESTED
#  parent: BaseGameRule
#  id: ClosetSkeleton
#  categories: [ HideSpawnMenu ]
#  components:
#  - type: StationEvent
#    weight: 5
#    duration: 1
#    minimumPlayers: 10
#  - type: RandomEntityStorageSpawnRule
#    prototype: MobSkeletonCloset

- type: entity
  parent: BaseGameRule
  id: DragonSpawn
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    weight: 6.5
    earliestStart: 40
    reoccurrenceDelay: 20
    minimumPlayers: 20
    duration: null
  - type: SpaceSpawnRule
    spawnDistance: 0
  - type: AntagSpawner
    prototype: MobDragon
  - type: DragonRule
  - type: AntagObjectives
    objectives:
    - CarpRiftsObjective
    - DragonSurviveObjective
  - type: AntagSelection
    agentName: dragon-round-end-agent-name
    definitions:
    - spawnerPrototype: SpawnPointGhostDragon
      min: 1
      max: 1
      pickPlayer: false
      mindRoles:
      - MindRoleDragon

- type: entity
  parent: BaseGameRule
  id: NinjaSpawn
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    weight: 6
    duration: 1
    earliestStart: 30
    reoccurrenceDelay: 20
    minimumPlayers: 25
  - type: SpaceSpawnRule
  - type: AntagLoadProfileRule
  - type: AntagObjectives
    objectives:
    - StealResearchObjective
    - DoorjackObjective
    - SpiderChargeObjective
    - TerrorObjective
    - MassArrestObjective
    - NinjaSurviveObjective
  - type: AntagSelection
    agentName: ninja-round-end-agent-name
    definitions:
    - spawnerPrototype: SpawnPointGhostSpaceNinja
      min: 1
      max: 1
      pickPlayer: false
      startingGear: SpaceNinjaGear
      briefing:
        text: ninja-role-greeting
        color: Green
        sound: /Audio/Misc/ninja_greeting.ogg
      components:
      - type: SpaceNinja
      - type: NpcFactionMember
        factions:
        - Syndicate
      - type: AutoImplant
        implants:
        - DeathAcidifierImplant
      - type: RandomMetadata
        nameSegments:
        - names_ninja_title
        - names_ninja
      mindRoles:
      - MindRoleNinja

- type: entity
  parent: BaseGameRule
  id: RevenantSpawn
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    weight: 7.5
    duration: 1
    earliestStart: 45
    minimumPlayers: 20
  - type: RandomSpawnRule
    prototype: MobRevenant

# disabled until event is rewritten to be more interesting
#- type: entity
#  id: FalseAlarm
#  parent: BaseGameRule
#  categories: [ HideSpawnMenu ]
#  components:
#  - type: StationEvent
#    weight: 15
#    duration: 1
#  - type: FalseAlarmRule

- type: entity
  id: GasLeak
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    startAnnouncement: true
    endAnnouncement: true
    weight: 8
    startDelay: 20
  - type: GasLeakRule

- type: entity
  id: KudzuGrowth
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    earliestStart: 15
    minimumPlayers: 15
    weight: 7
    startDelay: 50
    duration: 240
  - type: KudzuGrowthRule

- type: entity
  id: MeteorSwarm
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    earliestStart: 30
    weight: 5
    minimumPlayers: 10 #Enough to hopefully have at least one engineering guy
    startAnnouncement: true
    endAnnouncement: true
    duration: null #ending is handled by MeteorSwarmRule
    startDelay: 30
  - type: MeteorSwarmRule

- type: entity
  id: MouseMigration
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    startAnnouncement: true
    startDelay: 10
    earliestStart: 15
    weight: 3
    duration: 50
  - type: VentCrittersRule
    entries:
    - id: MobMouse
      prob: 0.02
    - id: MobMouse1
      prob: 0.02
    - id: MobMouse2
      prob: 0.02
    - id: MobMouseCancer
      prob: 0.001
    specialEntries:
    - id: SpawnPointGhostRatKing
      prob: 0.001

- type: entity
  id: CockroachMigration
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    startAnnouncement: true
    startDelay: 10
    weight: 3
    duration: 50
  - type: VentCrittersRule
    entries:
    - id: MobCockroach
      prob: 0.03
    - id: MobMothroach
      prob: 0.008
    - id: MobSquackroach
      prob: 0.001

- type: entity
  id: SnailMigrationLowPop
  parent: BaseGameRule
  components:
  - type: StationEvent
    startAnnouncement: true
    startDelay: 10
    weight: 3
    duration: 50
  - type: VentCrittersRule
    entries:
    - id: MobSnail
      prob: 0.02
    - id: MobSnailSpeed
      prob: 0.002
    - id: MobSnailMoth
      prob: 0.002

- type: entity
  id: SnailMigration
  parent: BaseStationEventShortDelay
  components:
  - type: StationEvent
    startAnnouncement: true
    earliestStart: 15
    weight: 3
    duration: 50
    minimumPlayers: 15
  - type: VentCrittersRule
    entries:
    - id: MobSnail
      prob: 0.02
    - id: MobSnailSpeed
      prob: 0.002
    - id: MobSnailMoth
      prob: 0.002
    - id: MobSnailInstantDeath
      prob: 0.00001 #  ~ 1:2000 snails

- type: entity
  id: PowerGridCheck
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    weight: 5
    startAnnouncement: true
    endAnnouncement: true
    startDelay: 24
    duration: 60
    maxDuration: 120
  - type: PowerGridCheckRule

- type: entity
  id: RandomSentience
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    weight: 6
    duration: 1
    maxOccurrences: 1 # this event has diminishing returns on interesting-ness, so we cap it
    startAnnouncement: true
  - type: RandomSentienceRule
    minSentiences: 2
    maxSentiences: 5

- type: entity
  parent: BaseGameRule
  id: SolarFlare
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    weight: 8
    startAnnouncement: true
    endAnnouncement: true
    duration: 120
    maxDuration: 240
  - type: SolarFlareRule
    onlyJamHeadsets: true
    affectedChannels:
    - Common
    extraChannels:
    - Command
    - Engineering
    - Medical
    - Science
    - Security
    - Service
    - Supply
    extraCount: 2
    lightBreakChancePerSecond: 0.0003
    doorToggleChancePerSecond: 0.001

- type: entity
  id: VentClog
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    startAnnouncement: true
    earliestStart: 15
    minimumPlayers: 15
    weight: 5
    startDelay: 50
    duration: 60
  - type: VentClogRule

- type: entity
  id: SlimesSpawn
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    startAnnouncement: true
    startDelay: 10
    earliestStart: 20
    minimumPlayers: 15
    weight: 3
    duration: 60
  - type: VentCrittersRule
    entries:
    - id: MobAdultSlimesBlueAngry
      prob: 0.02
    - id: MobAdultSlimesGreenAngry
      prob: 0.02
    - id: MobAdultSlimesYellowAngry
      prob: 0.02
    ## PIRATE HALLOWEEN
    - id: MobHaloweenSlimes
      prob: 0.02
    - id: MobHaloweenSlimesAngry
      prob: 0.02

- type: entity
  id: SpiderSpawn
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    startAnnouncement: true
    startDelay: 10
    earliestStart: 20
    minimumPlayers: 15
    weight: 3
    duration: 60
  - type: VentCrittersRule
    entries:
    - id: MobGiantSpiderAngry
      prob: 0.05

- type: entity
  id: SpiderClownSpawn
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    startAnnouncement: false
    startDelay: 10
    earliestStart: 20
    minimumPlayers: 20
    weight: 1.5
    duration: 60
  - type: VentCrittersRule
    entries:
    - id: MobClownSpider
      prob: 0.05

- type: entity
  id: ZombieOutbreak
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    earliestStart: 90
    minimumPlayers: 40
    weight: 1 # Zombies was happening basically every single survival round, so now it's super rare
    duration: 1
  - type: ZombieRule
  - type: AntagSelection
    definitions:
    - prefRoles: [ InitialInfected ]
      max: 3
      playerRatio: 10
      blacklist:
        components:
        - ZombieImmune
        - AntagImmune
      briefing:
        text: zombie-patientzero-role-greeting
        color: Plum
        sound: "/Audio/Ambience/Antag/zombie_start.ogg"
      components:
      - type: PendingZombie #less time to prepare than normal
        minInitialInfectedGrace: 300
        maxInitialInfectedGrace: 450
      - type: ZombifyOnDeath
      - type: IncurableZombie
      - type: InitialInfected
      mindRoles:
      - MindRoleInitialInfected

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseNukeopsRule
  id: LoneOpsSpawn
  components:
  - type: StationEvent
    earliestStart: 35
    weight: 5.5
    minimumPlayers: 20
    duration: 1
  - type: RuleGrids
  - type: LoadMapRule
    preloadedGrid: ShuttleStriker
  - type: NukeopsRule
    roundEndBehavior: Nothing
  - type: AntagSelection
    definitions:
    - spawnerPrototype: SpawnPointLoneNukeOperative
      min: 1
      max: 1
      pickPlayer: false
      startingGear: SyndicateLoneOperativeGearFull
      components:
      - type: NukeOperative
      - type: RandomMetadata
        nameSegments:
        - SyndicateNamesPrefix
        - SyndicateNamesNormal
      - type: NpcFactionMember
        factions:
        - Syndicate
      mindRoles:
      - MindRoleNukeops

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseTraitorRule
  id: SleeperAgents
  components:
  - type: StationEvent
    earliestStart: 25
    weight: 8
    minimumPlayers: 15
    reoccurrenceDelay: 30
    startAnnouncement: false
# - type: AlertLevelInterceptionRule - Disable setting to blue with sleeper agents. Uncomment to enable.
  - type: AntagSelection
    definitions:
    - prefRoles: [ TraitorSleeper ]
      fallbackRoles: [ Traitor ]
      min: 1
      max: 2
      playerRatio: 10
      blacklist:
        components:
        - AntagImmune
      mindRoles:
      - MindRoleTraitorSleeper

- type: entity
  id: MassHallucinations
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    weight: 7
    duration: 150
    maxDuration: 300
    reoccurrenceDelay: 30
  - type: MassHallucinationsRule
    minTimeBetweenIncidents: 0.1
    maxTimeBetweenIncidents: 300
    maxSoundDistance: 7
    sounds:
      collection: Paracusia

- type: entity
  id: ImmovableRodSpawn
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    startAnnouncement: false
    weight: 2
    duration: 1
    earliestStart: 45
    minimumPlayers: 20
  - type: ImmovableRodRule

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseGameRule
  id: IonStorm
  components:
  - type: StationEvent
    weight: 10
    reoccurrenceDelay: 20
    duration: 1
  - type: IonStormRule

- type: entity
  id: MimicVendorRule
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
    - type: StationEvent
      earliestStart: 0
      minimumPlayers: 20
      maxOccurrences: 1 # this event has diminishing returns on interesting-ness, so we cap it
      weight: 5
    - type: MobReplacementRule

- type: entity
  id: AirlockVirusRule
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
    - type: StationEvent
      earliestStart: 15
      minimumPlayers: 5
      maxOccurrences: 1
      weight: 3
    - type: AirlockVirusRule

- type: entity
  id: CarpSpawn
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: StationEvent
    startAnnouncement: true
    startDelay: 10
    earliestStart: 20
    minimumPlayers: 15
    weight: 3
    duration: 60
  - type: VentCrittersRule
    entries:
    - id: MobCarpGoldfish
      prob: 0.02
    - id: MobCarp
      prob: 0.005

- type: entity
  parent: BaseGameRule
  id: SmugglerStashVariationPass
  components:
  - type: StationEvent
    earliestStart: 0
    duration: 1
    minimumPlayers: 1
    maxOccurrences: 2
    weight: 10
  - type: RandomSpawnRule
    prototype: RandomSatchelSpawner
