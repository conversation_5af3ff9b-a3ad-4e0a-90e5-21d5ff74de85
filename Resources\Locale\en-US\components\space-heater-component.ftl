﻿comp-space-heater-ui-thermostat = Thermostat:
comp-space-heater-ui-mode = Mode
comp-space-heater-ui-status-disabled = Off
comp-space-heater-ui-status-enabled = On
comp-space-heater-ui-increase-temperature-range = +
comp-space-heater-ui-decrease-temperature-range = -

comp-space-heater-mode-Auto = Auto
comp-space-heater-mode-Heat = Heat
comp-space-heater-mode-Cool = Cool

comp-space-heater-ui-power-consumption = Power level:
comp-space-heater-ui-Low-power-consumption = Low
comp-space-heater-ui-Medium-power-consumption = Medium
comp-space-heater-ui-High-power-consumption = High

comp-space-heater-device-name = space heater
comp-space-heater-unanchored = The {$device} is not anchored.
