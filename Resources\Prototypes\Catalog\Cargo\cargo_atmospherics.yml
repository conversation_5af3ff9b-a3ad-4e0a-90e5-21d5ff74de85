- type: cargoProduct
  id: AtmosphericsAir
  icon:
    sprite: Structures/Storage/canister.rsi
    state: grey
  product: AirCanister
  cost: 1100
  category: cargoproduct-category-name-atmospherics
  group: market

- type: cargoProduct
  id: AtmosphericsOxygen
  icon:
    sprite: Structures/Storage/canister.rsi
    state: blue
  product: OxygenCanister
  cost: 1100
  category: cargoproduct-category-name-atmospherics
  group: market

- type: cargoProduct
  id: AtmosphericsLiquidOxygen
  icon:
    sprite: Structures/Storage/canister.rsi
    state: blue
  product: LiquidOxygenCanister
  cost: 2500
  category: cargoproduct-category-name-atmospherics
  group: market

- type: cargoProduct
  id: AtmosphericsNitrogen
  icon:
    sprite: Structures/Storage/canister.rsi
    state: red
  product: NitrogenCanister
  cost: 1100
  category: cargoproduct-category-name-atmospherics
  group: market

- type: cargoProduct
  id: AtmosphericsLiquidNitrogen
  icon:
    sprite: Structures/Storage/canister.rsi
    state: red
  product: LiquidNitrogenCanister
  cost: 2500
  category: cargoproduct-category-name-atmospherics
  group: market

- type: cargoProduct
  id: AtmosphericsCarbonDioxide
  icon:
    sprite: Structures/Storage/canister.rsi
    state: black
  product: CarbonDioxideCanister
  cost: 2200 # Until someone fixes it co2 can be used to oneshot people so it's more expensive
  category: cargoproduct-category-name-atmospherics
  group: market

- type: cargoProduct
  id: AtmosphericsLiquidCarbonDioxide
  icon:
    sprite: Structures/Storage/canister.rsi
    state: black
  product: LiquidCarbonDioxideCanister
  cost: 4000
  category: cargoproduct-category-name-atmospherics
  group: market

- type: cargoProduct
  id: AtmosphericsStorage
  icon:
    sprite: Structures/Storage/canister.rsi
    state: yellow
  product: StorageCanister
  cost: 1010 # No gases in it so it's cheaper
  category: cargoproduct-category-name-atmospherics
  group: market

#- type: cargoProduct
#  name: "water vapor canister"
#  id: AtmosphericsWaterVapor
#  description: "Water vapor canister"
#  icon:
#    sprite: Structures/Storage/canister.rsi
#    state: water_vapor
#  product: WaterVaporCanister
#  cost: 2600
#  category: cargoproduct-category-name-atmospherics
#  group: market

- type: cargoProduct
  id: AtmosphericsPlasma
  icon:
    sprite: Structures/Storage/canister.rsi
    state: orange
  product: PlasmaCanister
  cost: 4000
  category: cargoproduct-category-name-atmospherics
  group: market

#- type: cargoProduct
#  name: "tritium canister"
#  id: AtmosphericsTritium
#  description: "Tritium canister"
#  icon:
#    sprite: Structures/Storage/canister.rsi
#    state: green
#  product: TritiumCanister
#  cost: 15500
#  category: cargoproduct-category-name-atmospherics
#  group: market
