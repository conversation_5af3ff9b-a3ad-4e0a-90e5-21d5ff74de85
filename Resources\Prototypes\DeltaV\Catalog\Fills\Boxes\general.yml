- type: entity
  name: "Кор<PERSON>бка з ключами шифрування в'язнів"
  parent: BoxCardboard
  id: BoxEncryptionKeyPrisoner
  description: "Коробка запасних ключів шифрування."
  components:
  - type: StorageFill
    contents:
      - id: EncryptionKeyPrison
        amount: 4
  - type: Sprite
    layers:
      - state: box
      - state: encryptokey
  - type: Storage
    whitelist:
      components:
      - EncryptionKey

- type: entity
  name: "коробка ключів шифрування правосуддя"
  parent: BoxEncryptionKeyPassenger
  id: BoxEncryptionKeyJustice
  description: "Коробка запасних ключів шифрування."
  components:
  - type: StorageFill
    contents:
      - id: EncryptionKeyJustice
        amount: 4

#- type: entity
#  name: syndicate radio implanter box
#  parent: BoxCardboard
#  id: BoxSyndicateRadioImplanter
#  description: Contains cranial radio implants favored by Syndicate agents.
#  components:
#  - type: Sprite
#    layers:
#    - state: box_of_doom
#    - state: implant
#  - type: StorageFill
#    contents:
#    - id: SyndicateRadioImplanter
#      amount: 2
