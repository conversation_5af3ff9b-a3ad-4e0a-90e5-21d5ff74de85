- type: artifactEffect
  id: EffectBadFeeling
  targetDepth: 0
  effectHint: artifact-effect-hint-mental
  components:
  - type: TelepathicArtifact
    messages:
    - badfeeling-artifact-1
    - badfeeling-artifact-2
    - badfeeling-artifact-3
    - badfeeling-artifact-4
    - badfeeling-artifact-5
    - badfeeling-artifact-6
    - badfeeling-artifact-7
    - badfeeling-artifact-8
    - badfeeling-artifact-9
    - badfeeling-artifact-10
    - badfeeling-artifact-11
    - badfeeling-artifact-12
    - badfeeling-artifact-13
    - badfeeling-artifact-14
    - badfeeling-artifact-15
    drastic:
    - badfeeling-artifact-drastic-1
    - badfeeling-artifact-drastic-2
    - badfeeling-artifact-drastic-3
    - badfeeling-artifact-drastic-4
    - badfeeling-artifact-drastic-5
    - badfeeling-artifact-drastic-6

- type: artifactEffect
  id: EffectGoodFeeling
  targetDepth: 0
  effectHint: artifact-effect-hint-mental
  components:
  - type: TelepathicArtifact
    messages:
    - goodfeeling-artifact-1
    - goodfeeling-artifact-2
    - goodfeeling-artifact-3
    - goodfeeling-artifact-4
    - goodfeeling-artifact-5
    - goodfeeling-artifact-6
    - goodfeeling-artifact-7
    - goodfeeling-artifact-8
    - goodfeeling-artifact-9
    - goodfeeling-artifact-10
    - goodfeeling-artifact-11
    - goodfeeling-artifact-12
    - goodfeeling-artifact-13
    - goodfeeling-artifact-14
    drastic:
    - goodfeeling-artifact-drastic-1
    - goodfeeling-artifact-drastic-2
    - goodfeeling-artifact-drastic-3
    - goodfeeling-artifact-drastic-4
    - goodfeeling-artifact-drastic-5
    - goodfeeling-artifact-drastic-6

- type: artifactEffect
  id: EffectJunkSpawn
  targetDepth: 0
  effectHint: artifact-effect-hint-creation
  components:
  - type: SpawnArtifact
    maxSpawns: 10
    spawns:
    - id: FoodPacketSyndiTrash
      prob: 0.1
      orGroup: Trash
    - id: FoodPacketSemkiTrash
      prob: 0.1
      orGroup: Trash
    - id: FoodPacketBoritosTrash
      prob: 0.1
      orGroup: Trash
    - id: FoodPacketCheesieTrash
      prob: 0.1
      orGroup: Trash
    - id: FoodPacketChipsTrash
      prob: 0.1
      orGroup: Trash
    - id: FoodPacketChocolateTrash
      prob: 0.1
      orGroup: Trash
    - id: FoodPacketEnergyTrash
      prob: 0.1
      orGroup: Trash
    - id: FoodPacketPopcornTrash
      prob: 0.1
      orGroup: Trash
    - id: FoodPacketRaisinsTrash
      prob: 0.1
      orGroup: Trash
    - id: ToySpawner
      prob: 0.1
      orGroup: Trash

- type: artifactEffect
  id: EffectLightFlicker
  targetDepth: 0
  effectHint: artifact-effect-hint-electrical-interference
  components:
  - type: LightFlickerArtifact

- type: artifactEffect
  id: EffectPointLight
  targetDepth: 0
  components:
  - type: PointLight
    radius: 8
    energy: 10
    color: "#daa3fd"
  - type: TriggerArtifact
  - type: FlashOnTrigger
    range: 8

- type: artifactEffect #bornana
  id: EffectBananaSpawn
  targetDepth: 0
  effectHint: artifact-effect-hint-creation
  components:
  - type: SpawnArtifact
    maxSpawns: 20
    spawns:
    - id: FoodBanana
      amount: 3
      maxAmount: 6
  - type: ChemicalPuddleArtifact
    chemicalSolution:
      maxVol: 100
      canReact: false
    possibleChemicals:
    - Potassium

- type: artifactEffect
  id: EffectFloraSpawn
  targetDepth: 1
  effectHint: artifact-effect-hint-creation
  components:
  - type: SpawnArtifact
    maxSpawns: 3
    spawns:
    - id: RandomFloraTree

- type: artifactEffect
  id: EffectThrow
  targetDepth: 0
  effectHint: artifact-effect-hint-environment
  components:
    - type: ThrowArtifact

- type: artifactEffect
  id: EffectChemicalPuddle
  targetDepth: 0
  effectHint: artifact-effect-hint-biochemical
  components:
  - type: ChemicalPuddleArtifact
    chemicalSolution:
      maxVol: 500
      canReact: false
    possibleChemicals:
    - Aluminium
    - Carbon
    - Chlorine
    - Copper
    - Ethanol
    - Fluorine
    - Sugar
    - Hydrogen
    - Iodine
    - Iron
    - Lithium
    - Mercury
    - Nitrogen
    - Oxygen
    - Phosphorus
    - Potassium
    - Radium
    - Silicon
    - Sodium
    - Water
    - Sulfur

- type: artifactEffect
  id: EffectCold
  targetDepth: 1
  effectHint: artifact-effect-hint-consumption
  components:
  - type: TemperatureArtifact
    targetTemp: 50

- type: artifactEffect
  id: EffectHeat
  targetDepth: 1
  effectHint: artifact-effect-hint-release
  components:
    - type: TemperatureArtifact
      targetTemp: 500

- type: artifactEffect
  id: EffectFoamMild
  targetDepth: 1
  effectHint: artifact-effect-hint-biochemical
  components:
  - type: FoamArtifact
    reagents:
    - Oxygen
    - Plasma
    - Blood
    - SpaceCleaner
    - Nutriment
    - SpaceLube
    - Ethanol
    - Mercury
    - VentCrud
    - WeldingFuel
    - JuiceThatMakesYouWeh

- type: artifactEffect
  id: EffectInstrumentSpawn
  targetDepth: 1
  effectHint: artifact-effect-hint-creation
  components:
  - type: SpawnArtifact
    maxSpawns: 5
    spawns:
    - id: RandomInstruments

- type: artifactEffect
  id: EffectMonkeySpawn
  targetDepth: 1
  effectHint: artifact-effect-hint-creation
  components:
  - type: SpawnArtifact
    spawns:
    - id: MobMonkey
      orGroup: monkey
      prob: 0.95
    - id: MobGorilla #harambe
      orGroup: monkey
      prob: 0.05

- type: artifactEffect
  id: EffectChargeBatteries
  targetDepth: 1
  effectHint: artifact-effect-hint-release
  components:
  - type: ChargeBatteryArtifact
  - type: TelepathicArtifact
    messages:
    - charge-artifact-popup

- type: artifactEffect
  id: EffectRadiate
  targetDepth: 1
  effectHint: artifact-effect-hint-release
  components:
  - type: RadiationSource
    intensity: 2

- type: artifactEffect
  id: EffectKnock
  targetDepth: 1
  effectHint: artifact-effect-hint-electrical-interference
  components:
    - type: KnockArtifact

- type: artifactEffect
  id: EffectMagnet
  targetDepth: 1
  effectHint: artifact-effect-hint-magnet
  components:
  - type: GravityWell
    maxRange: 3
    baseRadialAcceleration: 1
    baseTangentialAcceleration: 3

- type: artifactEffect
  id: EffectAntiMagnet
  targetDepth: 1
  effectHint: artifact-effect-hint-magnet
  components:
  - type: GravityWell
    maxRange: 3
    baseRadialAcceleration: -1
    baseTangentialAcceleration: -3

- type: artifactEffect
  id: EffectInvisibility
  targetDepth: 2
  effectHint: artifact-effect-hint-visual
  components:
  - type: Stealth
    hadOutline: true
    minVisibility: -1 # Goobstation - Proper invisibility
  - type: StealthOnMove
    passiveVisibilityRate: -0.10
    movementVisibilityRate: 0.10

- type: artifactEffect
  id: EffectExplosionScary
  targetDepth: 2
  effectHint: artifact-effect-hint-environment
  components:
  - type: TriggerArtifact
  - type: ExplodeOnTrigger
  - type: Explosive
    deleteAfterExplosion: false
    explosionType: Radioactive
    totalIntensity: 300
    intensitySlope: 2
    maxIntensity: 1.5
    canCreateVacuum: false

- type: artifactEffect
  id: EffectRareMaterialSpawn
  targetDepth: 2
  effectHint: artifact-effect-hint-creation
  components:
  - type: SpawnArtifact
    spawns:
    - id: SilverOre1
      prob: 0.3
      maxAmount: 3
    - id: PlasmaOre1
      prob: 0.3
      maxAmount: 3
    - id: GoldOre1
      prob: 0.3
      maxAmount: 3
    - id: UraniumOre1
      prob: 0.3
      maxAmount: 3

- type: artifactEffect
  id: EffectAngryCarpSpawn
  targetDepth: 2
  effectHint: artifact-effect-hint-creation
  components:
  - type: SpawnArtifact
    maxSpawns: 5
    spawns:
    - id: MobCarpHolo
      orGroup: carp
    - id: MobCarpMagic
      orGroup: carp

- type: artifactEffect
  id: EffectFaunaSpawn
  targetDepth: 2
  effectHint: artifact-effect-hint-creation
  components:
  - type: SpawnArtifact
    maxSpawns: 5
    spawns:
    - id: MobAdultSlimesYellowAngry
      orGroup: fauna
    - id: MobAngryBee
      orGroup: fauna
    - id: MobBearSpace
      orGroup: fauna
    - id: MobBee
      orGroup: fauna
      maxAmount: 5
    - id: MobCat
      orGroup: fauna
      maxAmount: 2
    - id: MobCatKitten
      orGroup: fauna
      maxAmount: 2
    - id: MobCorgiPuppy
      orGroup: fauna
      maxAmount: 2
    - id: MobFox
      orGroup: fauna
      maxAmount: 1
    - id: MobGoat
      orGroup: fauna
      maxAmount: 3
    - id: MobKangaroo
      orGroup: fauna
      maxAmount: 1
    - id: MobKangarooSpace
      orGroup: fauna
    - id: MobMothroach
      orGroup: fauna
      maxAmount: 2
    - id: MobMonkeySyndicateAgent #so lucky
      orGroup: fauna
      maxAmount: 1
      prob: 0.03
    - id: MobMouse
      orGroup: fauna
    - id: MobParrot
      orGroup: fauna
      maxAmount: 1
    - id: MobPenguin
      orGroup: fauna
      maxAmount: 2
    - id: MobPig
      orGroup: fauna
      maxAmount: 1
    - id: MobPurpleSnake
      orGroup: fauna
    - id: MobSpiderSpace
      orGroup: fauna
    - id: MobTick
      orGroup: fauna
    - id: MobXenoRavager
      orGroup: fauna

- type: artifactEffect
  id: EffectCashSpawn
  targetDepth: 2
  effectHint: artifact-effect-hint-creation
  components:
  - type: SpawnArtifact
    maxSpawns: 10
    spawns:
    - id: SpaceCash10
      maxAmount: 5
      prob: 0.75
    - id: SpaceCash100
      maxAmount: 2
      prob: 0.5
    - id: SpaceCash500
      prob: 0.25
    - id: SpaceCash1000
      prob: 0.1

- type: artifactEffect
  id: EffectShatterWindows
  targetDepth: 2
  effectHint: artifact-effect-hint-environment
  components:
  - type: DamageNearbyArtifact
    damageChance: 0.75
    whitelist:
      tags:
      - Window
    damage:
      types:
        Structural: 200

- type: artifactEffect
  id: EffectGas
  targetDepth: 2
  effectHint: artifact-effect-hint-environment
  components:
  - type: GasArtifact
    possibleGas:
    - CarbonDioxide
    - Plasma
    - Tritium
    - Ammonia
    - NitrousOxide
    - Frezon

- type: artifactEffect
  id: EffectBlink
  targetDepth: 2
  effectHint: artifact-effect-hint-displacement
  components:
  - type: RandomTeleportArtifact

- type: artifactEffect
  id: EffectFoamGood
  targetDepth: 2
  effectHint: artifact-effect-hint-biochemical
  components:
  - type: FoamArtifact
    reagents:
    - Dermaline
    - Arithrazine
    - Bicaridine
    - Inaprovaline
    - Kelotane
    - Dexalin
    - Omnizine

- type: artifactEffect
  id: EffectChemicalPuddleRare
  targetDepth: 2
  effectHint: artifact-effect-hint-biochemical
  components:
  - type: ChemicalPuddleArtifact
    chemicalSolution:
      maxVol: 500
      canReact: false
    possibleChemicals:
    - Dermaline
    - Arithrazine
    - Bicaridine
    - Inaprovaline
    - Kelotane
    - Dexalin
    - Omnizine
    - Napalm
    - Toxin
    - Epinephrine
    - Cognizine
    - Ultravasculine
    - Desoxyephedrine
    - Pax
    - Siderlac
    # Goobstation - Start
    - Ibuprofen
    - Tirimol

- type: artifactEffect
  id: EffectEmp
  targetDepth: 2
  effectHint: artifact-effect-hint-electrical-interference
  components:
  - type: EmpArtifact

- type: artifactEffect
  id: EffectPolyMonkey
  targetDepth: 2
  effectHint: artifact-effect-hint-polymorph
  components:
  - type: PolyOthersArtifact

- type: artifactEffect
  id: EffectPolyLizard
  targetDepth: 2
  effectHint: artifact-effect-hint-polymorph
  components:
  - type: PolyOthersArtifact
    polymorphPrototypeName: ArtifactLizard

- type: artifactEffect
  id: EffectPolyLuminous
  targetDepth: 3
  effectHint: artifact-effect-hint-polymorph
  components:
  - type: PolyOthersArtifact
    polymorphPrototypeName: ArtifactLuminous

- type: artifactEffect
  id: EffectHealAll
  targetDepth: 3
  effectHint: artifact-effect-hint-environment
  components:
  - type: DamageNearbyArtifact
    damageChance: 1
    radius: 8
    whitelist:
      components:
      - MobState
    damage:
      groups:
        Brute: -300
        Burn: -300

- type: artifactEffect
  id: EffectRadiateStrong
  targetDepth: 3
  effectHint: artifact-effect-hint-release
  components:
  - type: RadiationSource
    intensity: 6

- type: artifactEffect
  id: EffectMaterialSpawn
  targetDepth: 3
  effectHint: artifact-effect-hint-creation
  components:
  - type: SpawnArtifact
    maxSpawns: 5
    spawns:
    - id: SheetSteel
      orGroup: materials
    - id: SheetGlass
      orGroup: materials
    - id: SheetPlastic
      orGroup: materials

- type: artifactEffect
  id: EffectShuffle
  targetDepth: 3
  effectHint: artifact-effect-hint-displacement
  components:
  - type: ShuffleArtifact
  - type: TelepathicArtifact
    range: 7.5
    messages:
    - shuffle-artifact-popup

- type: artifactEffect
  id: EffectT4PartsSpawn
  targetDepth: 3
  effectHint: artifact-effect-hint-creation
  components:
  - type: SpawnArtifact
    maxSpawns: 10
    spawns:
    - id: BluespaceCapacitorStockPart
      prob: 0.5
      maxAmount: 3
    - id: BluespaceManipulatorStockPart
      prob: 0.5
      maxAmount: 3
    - id: BluespaceMatterBinStockPart
      prob: 0.5
      maxAmount: 3

- type: artifactEffect
  id: EffectFoamDangerous
  targetDepth: 3
  effectHint: artifact-effect-hint-biochemical
  components:
  - type: FoamArtifact
    minFoamAmount: 20
    maxFoamAmount: 30
    reagents:
    - Tritium
    - Plasma
    - SulfuricAcid
    - SpaceDrugs
    - Nocturine
    - MuteToxin
    - Napalm
    - CarpoToxin
    - ChloralHydrate
    - Mold
    - Amatoxin
    # Goobstation - Start
    - Triclor
    - Cyanide

- type: artifactEffect
  id: EffectIgnite
  targetDepth: 3
  effectHint: artifact-effect-hint-release
  components:
  - type: IgniteArtifact
    range: 7
    minFireStack: 3
    maxFireStack: 6

- type: artifactEffect
  id: EffectMitosis
  targetDepth: 3
  effectHint: artifact-effect-hint-creation
  components:
  - type: SpawnArtifact
    maxSpawns: 1
    spawns:
    - id: RandomArtifactSpawner

- type: artifactEffect
  id: EffectAnomaly
  targetDepth: 3
  effectHint: artifact-effect-hint-creation
  components:
  - type: SpawnArtifact
    maxSpawns: 1
    spawns:
    - id: RandomAnomalySpawner

- type: artifactEffect
  id: EffectBoom
  targetDepth: 3
  effectHint: artifact-effect-hint-environment
  components:
  - type: TriggerArtifact
  - type: ExplodeOnTrigger
  - type: Explosive
    deleteAfterExplosion: false
    explosionType: Default
    totalIntensity: 500
    intensitySlope: 2.5
    maxIntensity: 50

- type: artifactEffect
  id: EffectPortal
  targetDepth: 3
  effectHint: artifact-effect-hint-displacement
  components:
  - type: PortalArtifact

- type: artifactEffect
  id: EffectSingulo
  targetDepth: 10
  effectHint: artifact-effect-hint-destruction
  components:
  - type: SpawnArtifact
    maxSpawns: 1
    spawns:
    - id: Singularity

- type: artifactEffect
  id: EffectTesla
  targetDepth: 10
  effectHint: artifact-effect-hint-destruction
  components:
  - type: SpawnArtifact
    maxSpawns: 1
    spawns:
    - id: TeslaEnergyBall
