- type: entity
  name: "книга"
  parent: BaseItem
  id: BookBase
  description: "Книга у твердій обкладинці."
  components:
  - type: Sprite
    sprite: Objects/Misc/books.rsi
    layers:
    - state: paper
    - state: cover_base
      color: "#332d27"
      map: [ "cover" ]
    - state: decor_wingette
      color: "#453f3a"
      map: [ "decor" ]
    - state: icon_text
      map: [ "icon" ]
    - state: overlay_null
      map: [ "overlay" ]
  - type: Paper
    contentSize: 12000
  - type: ActivatableUI
    key: enum.PaperUiKey.Key
  - type: UserInterface
    interfaces:
      enum.PaperUiKey.Key:
        type: PaperBoundUserInterface
  - type: Tag
    tags:
      - Book
  - type: PaperVisuals
    backgroundImagePath: "/Textures/Interface/Paper/paper_background_book.svg.96dpi.png"
    backgroundPatchMargin: 23.0, 16.0, 14.0, 15.0
    contentMargin: 20.0, 20.0, 20.0, 20.0
  - type: EmitSoundOnPickup
    sound: /Audio/SimpleStation14/Items/Handling/book_pickup.ogg
  - type: EmitSoundOnDrop
    sound: /Audio/SimpleStation14/Items/Handling/book_drop.ogg
  - type: EmitSoundOnLand
    sound: /Audio/SimpleStation14/Items/Handling/book_drop.ogg

- type: entity
  id: BookSpaceEncyclopedia
  parent: BaseItem
  name: "космічна енциклопедія"
  description: "Енциклопедія, що містить усі знання. Автор цієї енциклопедії невідомий."
  components:
  - type: Sprite
    sprite: Objects/Misc/books.rsi
    layers:
    - state: paper
    - state: cover_base
      color: "#0a2a6b"
    - state: decor_wingette
      color: "#082561"
    - state: icon_text
      color: gold
    - state: icon_planet
      color: "#42b6f5"
  - type: Tag
    tags:
    - Book
  - type: GuideHelp
    openOnActivation: true
    guides:
    - SS14

- type: entity
  id: BookTheBookOfControl
  parent: BaseItem
  name: "книга контролю"
  description: "Важливо, щоб стати міцним."
  components:
  - type: Sprite
    sprite: Objects/Misc/books.rsi
    layers:
    - state: paper
    - state: cover_base
      color: black
    - state: decor_wingette
      color: "#bbbbbb"
    - state: icon_glow
      color: red
    - state: icon_corner
      color: red
  - type: Tag
    tags:
    - Book
  - type: GuideHelp
    openOnActivation: true
    guides:
    - Controls

- type: entity
  id: BookBartendersManual
  parent: BaseItem
  name: "посібник бармена"
  description: "Цей посібник забруднений пивом."
  components:
  - type: Sprite
    sprite: Objects/Misc/books.rsi
    layers:
    - state: paper
    - state: cover_base
      color: "#004848"
    - state: decor_wingette
      color: "#006666"
    - state: icon_bar
  - type: Tag
    tags:
    - Book
  - type: GuideHelp
    openOnActivation: true
    guides:
    - Bartender

- type: entity
  id: BookHowToCookForFortySpaceman
  parent: BaseItem
  name: "Як приготувати їжу для сорока космонавтів"
  description: "Книга про кулінарію, написана космічним шеф-кухарем."
  components:
  - type: Sprite
    sprite: Objects/Misc/books.rsi
    layers:
    - state: paper
    - state: cover_base
      color: "#e22541"
    - state: decor_wingette
    - state: icon_apple
  - type: Tag
    tags:
    - Book
  - type: GuideHelp
    openOnActivation: true
    guides:
    - Chef

- type: entity
  id: BookLeafLoversSecret
  parent: BaseItem
  name: "секрет любителя листя"
  description: "Має сильний запах бур'янів. Це мотивує вас підгодовувати і сіяти."
  components:
  - type: Sprite
    sprite: Objects/Misc/books.rsi
    layers:
    - state: paper
    - state: cover_base
      color: "#0e5a24"
    - state: decor_wingette
      color: "#2fa151"
    - state: icon_cabbage
    - state: icon_corner
      color: gold
  - type: Tag
    tags:
    - Book
  - type: GuideHelp
    openOnActivation: true
    guides:
    - Botany

- type: entity
  id: BookEngineersHandbook
  parent: BaseItem
  name: "довідник інженера"
  description: "Посібник з інженерії, написаний НаноТрейзен."
  components:
  - type: Sprite
    sprite: Objects/Misc/books.rsi
    layers:
    - state: paper
    - state: cover_base
      color: "#6c4718"
    - state: decor_wingette
      color: "#b5913c"
    - state: icon_wrench
    - state: icon_corner
      color: gold
  - type: Tag
    tags:
    - Book
  - type: GuideHelp
    openOnActivation: true
    guides:
    - Engineering

- type: entity
  id: BookScientistsGuidebook
  parent: BaseItem
  name: "путівник науковця"
  description: "Путівник про науку, написаний НаноТрейзен."
  components:
  - type: Sprite
    sprite: Objects/Misc/books.rsi
    layers:
    - state: paper
    - state: cover_base
      color: "#542485"
    - state: decor_wingette_circle
      color: "#be69f0"
    - state: icon_dna
  - type: Tag
    tags:
    - Book
  - type: GuideHelp
    openOnActivation: true
    guides:
    - Science

- type: entity
  id: BookSecurity
  parent: BaseItem
  name: "безпека 101"
  description: "Книга про безпеку, написана НаноТрейзен. Книга заплямована кров'ю. Схоже, її використовували більше як зброю, ніж як матеріал для читання."
  components:
  - type: Sprite
    sprite: Objects/Misc/books.rsi
    layers:
    - state: paper
    - state: cover_base
      color: "#ab1515"
    - state: decor_wingette
      color: "#e05334"
    - state: icon_stunbaton
  - type: Tag
    tags:
    - Book
  - type: MeleeWeapon
    wideAnimationRotation: 180
    damage:
      types:
        Blunt: 6
  - type: GuideHelp
    openOnActivation: true
    guides:
    - Security

- type: entity
  id: BookHowToKeepStationClean
  parent: BaseItem
  name: "як підтримувати станцію в чистоті"
  description: "Ця книга дуже чиста."
  components:
  - type: Sprite
    sprite: Objects/Misc/books.rsi
    layers:
    - state: paper
    - state: cover_base
      color: "#550c82"
    - state: decor_wingette
    - state: icon_bucket
  - type: Tag
    tags:
    - Book
  - type: GuideHelp
    openOnActivation: true
    guides:
    - Janitorial

- type: entity
  id: BookHowToRockAndStone
  parent: BaseItem
  name: "слава породі та каменюці для чайників"
  description: "Дуже детальний посібник для дослідників, написаний Карлом, легендарним космічним шахтарем, який, на жаль, пропав безвісти. Він мотивує вас каменувати і каменувати."
  components:
  - type: Sprite
    sprite: Objects/Misc/books.rsi
    layers:
    - state: paper
    - state: cover_base
      color: "#52320b"
    - state: decor_wingette
      color: "#e69a3e"
    - state: icon_glow
    - state: icon_diamond
    - state: icon_text
      color: "#fcdf74"
  - type: Tag
    tags:
    - Book
  - type: GuideHelp
    openOnActivation: true
    guides:
    - Salvage

- type: entity
  id: BookMedicalReferenceBook
  parent: BaseItem
  name: "медичний довідник"
  description: "Довідник з медицини, написаний старим лікарем. Почерк ледь розбірливий."
  components:
  - type: Sprite
    sprite: Objects/Misc/books.rsi
    layers:
    - state: paper
    - state: cover_base
      color: "#cccccc"
    - state: decor_wingette
      color: "#f7f7f7"
    - state: icon_medical
      color: "#58abcc"
  - type: Tag
    tags:
    - Book
  - type: GuideHelp
    openOnActivation: true
    guides:
    - Medical

- type: entity
  id: BookHowToSurvive
  parent: BaseItem
  name: "як вижити"
  description: "За іронією долі, автор цієї книги помер."
  components:
  - type: Sprite
    sprite: Objects/Misc/books.rsi
    layers:
    - state: paper
    - state: cover_old
      color: "#6c4718"
    - state: decor_wingette
      color: "#b5913c"
    - state: icon_glow
      color: red
    - state: icon_wrench
    - state: overlay_blood
  - type: Tag
    tags:
    - Book
  - type: GuideHelp
    openOnActivation: true
    guides:
    - Survival

- type: entity
  id: BookChemicalCompendium
  parent: BaseItem
  name: "хепендіум"
  description: "Вичерпний посібник, написаний якимось старим професорським скелетом про хімічний синтез."
  components:
  - type: Sprite
    sprite: Objects/Misc/books.rsi
    layers:
    - state: paper
    - state: cover_base
      color: "#2a7b74"
    - state: decor_wingette
      color: "#2a7b74"
    - state: icon_chemical
  - type: Tag
    tags:
    - Book
  - type: GuideHelp
    openOnActivation: true
    guides:
    - Chemicals

- type: entity
  parent: BookBase
  id: BookRandom
  suffix: random visual
  description: "Кожна книга унікальна! Що заховано в цій?"
  components:
  - type: RandomMetadata
    nameSegments:
    - book_hint_appearance
    - book_type
  - type: RandomSprite
    available:
      - cover:
          cover_base: Sixteen
          cover_old: Sixteen
          cover_strong: Sixteen
        decor:
          decor_wingette: Sixteen
          decor_wingette_circle: Sixteen
          decor_bottom: Sixteen
          decor_middle: Sixteen
          decor_spine: Sixteen
          decor_diagonal: Sixteen
          decor_vertical_middle: Sixteen
          icon_corner: Sixteen
          icon_mount: ""
        icon:
          icon_biohazard: Sixteen
          icon_borg: ""
          icon_banana: ""
          icon_glow: Sixteen
          icon_hacking: ""
          icon_law: Sixteen
          icon_magnifier: ""
          icon_nuclear: ""
          icon_time: Sixteen
          icon_aurora: Sixteen
          icon_briefcase: ""
          icon_eye: ""
          icon_letter_N: ""
          icon_letter_P: ""
          icon_lightning: ""
          icon_planet: ""
          icon_possum: ""
          icon_question: Sixteen
          icon_scmmd: ""
          icon_stars: Sixteen
          icon_stars2: Sixteen
          icon_temple: Sixteen
          icon_tree: ""
          icon_pentagramm: Sixteen
          icon_fish: ""
          icon_origami: ""
          icon_skull: ""
          icon_text: ""
          icon_text2: ""
          icon_text3: ""
        overlay:
          overlay_blood: ""
          overlay_dirt: Sixteen
          detail_bookmark: Sixteen
          detail_rivets: Sixteen
          overlay_null: ""

- type: entity
  parent: BookRandom
  id: BookRandomStory
  suffix: random visual, random story
  components:
  - type: PaperRandomStory
    storySegments:
    - "This is a "
    - book_genre
    - " about a "
    - book_character_trait
    - " "
    - book_character
    - " and "
    - book_character_trait
    - " "
    - book_character
    - ". Due to "
    - book_event
    - ", they "
    - book_action_trait
    - " "
    - book_action
    - " "
    - book_character
    - " "
    - book_location
    - ". \n\n"
    - book_story_element
    - " is "
    - book_story_element_trait
    - "."
    storySeparator: ""

- type: entity
  parent: BookBase
  id: BookAtmosDistro
  name: "Путівник Ньютона по атмосфері: Дистрибутив"
  description: "На полях - нескінченні нерозбірливі нотатки. Більша частина тексту вкрита написаними від руки знаками питання."
  components:
  - type: Sprite
    sprite: Objects/Misc/books.rsi
    layers:
    - state: paper
    - state: cover_base
      color: "#366ed6"
    - state: decor_wingette
      color: "#2739b0"
    - state: icon_atmos
    - state: icon_corner
      color: gold
  - type: Paper
    content: book-text-atmos-distro

- type: entity
  parent: BookBase
  id: BookAtmosWaste
  name: "Путівник Ньютона по атмосфері: Відходи"
  description: "На полях - нескінченні нерозбірливі нотатки. Більша частина тексту вкрита написаними від руки знаками питання."
  components:
  - type: Sprite
    sprite: Objects/Misc/books.rsi
    layers:
    - state: paper
    - state: cover_base
      color: "#db233f"
    - state: decor_wingette
      color: "#ab0730"
    - state: icon_atmos
    - state: icon_corner
      color: gold
  - type: Paper
    content: book-text-atmos-waste

- type: entity
  parent: BookBase
  id: BookAtmosAirAlarms
  name: "Путівник Ньютона по атмосфері: Повітряна сигналізація"
  description: "На полях - нескінченні нерозбірливі нотатки. Більша частина тексту вкрита написаними від руки знаками питання."
  components:
  - type: Sprite
    sprite: Objects/Misc/books.rsi
    layers:
    - state: paper
    - state: cover_base
      color: "#bfb328"
    - state: decor_wingette
      color: "#9c7c14"
    - state: icon_atmos
    - state: icon_corner
      color: gold
  - type: Paper
    content: book-text-atmos-alarms

- type: entity
  parent: BookBase
  id: BookAtmosVentsMore
  name: "Путівник Ньютона по атмосфері: Вентиляційні отвори та інше"
  description: "На полях - нескінченні нерозбірливі нотатки. Більша частина тексту вкрита написаними від руки знаками питання."
  components:
  - type: Sprite
    sprite: Objects/Misc/books.rsi
    layers:
    - state: paper
    - state: cover_base
      color: "#3ec78e"
    - state: decor_wingette
      color: "#28a15a"
    - state: icon_atmos
    - state: icon_corner
      color: gold
  - type: Paper
    content: book-text-atmos-vents

- type: entity
  id: BookPsionicsGuidebook
  parent: BaseItem
  name: "путівник по псіоніці"
  description: "Посібник з псіоніки, написаний Гюнтером П. Гундером (Gunther P. Gunderaan)."
  components:
  - type: Sprite
    sprite: Objects/Misc/books.rsi
    layers:
    - state: paper
    - state: cover_base
      color: "#542485"
    - state: decor_wingette_circle
      color: "#be69f0"
    - state: icon_dna
  - type: Tag
    tags:
    - Book
  - type: GuideHelp
    openOnActivation: true
    guides:
    - Psionics
