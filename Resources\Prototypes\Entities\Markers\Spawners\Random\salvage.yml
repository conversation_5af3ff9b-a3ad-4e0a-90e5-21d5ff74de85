# NOTE! All mobs that come out of this should have Salvage rulesets.
# These rulesets exist because Salvage mobs kept harassing the station and going out of control.

- type: entity
  name: "спавнер ящиків з матеріалами утилізаторів"
  id: SalvageMaterialCrateSpawner
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Structures/Storage/Crates/generic.rsi
          state: icon
    - type: RandomSpawner
      rarePrototypes:
        - SalvageHumanCorpse
        - CrateMaterialPlasteel
        - CrateMaterialWood
        - CrateMaterialPlastic
        - CrateSalvageEquipment
        - CrateMaterialSteel
        - CrateMaterialGlass
        - CrateServiceJanitorialSupplies
        - CrateHydroponicsSeedsMedicinal
        - CrateEmergencyInternals
        - CrateFoodMRE
        - CrateMaterialTextiles
        - CrateMedicalSupplies
        - CrateEngineeringCableBulk
        - CrateMaterialCardboard
        - CrateServiceBooks
        - CrateServiceSmokeables
        - CrateTrashCartFilled
        - ClosetMaintenanceFilledRandom
        - ClosetEmergencyFilledRandom
      rareChance: 0.4
      prototypes:
        - CrateSalvageAssortedGoodies
      chance: 0.9
      offset: 0.0

- type: entity
  name: "спавнер каністр утилізаторів"
  id: SalvageCanisterSpawner
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Structures/Storage/canister.rsi
          state: blue
    - type: RandomSpawner
      rarePrototypes:
        - PlasmaCanister
      rareChance: 0.03
      prototypes:
        - AirCanister
        - NitrogenCanister
        - OxygenCanister
        - CarbonDioxideCanister
        - WaterVaporCanister
      chance: 0.9
      offset: 0.0

- type: entity
  name: "Спасатель деталей машин T2 Sprinter"
  id: SalvagePartsT2Spawner
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Objects/Misc/stock_parts.rsi
          state: advanced_matter_bin
    - type: RandomSpawner
      prototypes:
        - AdvancedCapacitorStockPart
        - NanoManipulatorStockPart
        - AdvancedMatterBinStockPart
      offset: 0.0

- type: entity
  parent: MarkerBase
  id: SalvagePartsT3T4Spawner
  name: "верстатна частина ярусу 3/4"
  components:
  - type: Sprite
    layers:
    - sprite: Objects/Misc/stock_parts.rsi
      state: super_matter_bin
  - type: RandomSpawner
    rarePrototypes:
    - BluespaceCapacitorStockPart
    - BluespaceManipulatorStockPart
    - BluespaceMatterBinStockPart
    rareChance: 0.05
    prototypes:
    - SuperCapacitorStockPart
    - PicoManipulatorStockPart
    - SuperMatterBinStockPart
    chance: 0.95
    offset: 0.0

- type: entity
  parent: MarkerBase
  id: SalvagePartsT3Spawner
  name: "частина машини рівня 3"
  suffix: Spawner
  components:
  - type: Sprite
    layers:
    - sprite: Objects/Misc/stock_parts.rsi
      state: super_matter_bin
  - type: RandomSpawner
    prototypes:
    - SuperCapacitorStockPart
    - PicoManipulatorStockPart
    - SuperMatterBinStockPart
    offset: 0.0

- type: entity
  parent: MarkerBase
  id: SalvagePartsT4Spawner
  name: "частина машини рівня 4"
  suffix: Spawner
  components:
  - type: Sprite
    layers:
    - sprite: Objects/Misc/stock_parts.rsi
      state: bluespace_matter_bin
  - type: RandomSpawner
    prototypes:
    - BluespaceCapacitorStockPart
    - PicoManipulatorStockPart
    - BluespaceMatterBinStockPart
    offset: 0.0

- type: entity
  name: "спавнер мобів утилізаторів"
  id: SalvageMobSpawner
  parent: MarkerBase
  suffix: 25
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Mobs/Aliens/Carps/space.rsi
          state: icon
    - type: RandomSpawner
      rarePrototypes:
      - MobSharkSalvage
      rareChance: 0.2
      prototypes:
        - MobCarpSalvage
        - MobCarpSalvage
        - MobCarpSalvage
        - MobCarpSalvage
        - MobCarpSalvage
        - MobTickSalvage
        - MobTickSalvage
        - MobTickSalvage
        - PlushieCarp
        - DehydratedSpaceCarp
      chance: 0.25
      offset: 0.2

- type: entity #I made this in case someone decided to make a terrifying space tick swarm for salvage. that someone may be me -EG404
  name: "спавнер космічного кліща утилізаторів"
  id: SpaceTickSpawner
  parent: MarkerBase
  suffix: 100
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Mobs/Aliens/Xenos/spacetick.rsi
          state: icon
    - type: ConditionalSpawner
      prototypes:
        - MobTickSalvage

- type: entity
  name: "спавнер космічних ведмедів утилізаторів"
  id: SpawnMobBearSalvage
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: bear
      sprite: Mobs/Animals/bear.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobBearSpaceSalvage

- type: entity
  id: SalvageMobSpawner75
  parent: SalvageMobSpawner
  suffix: 75
  components:
  - type: RandomSpawner
    rarePrototypes:
    - MobSharkSalvage
    rareChance: 0.2
    prototypes:
    - MobCarpSalvage
    - MobCarpSalvage
    - MobCarpSalvage
    - MobCarpSalvage
    - MobCarpSalvage
    - MobTickSalvage
    - MobTickSalvage
    - MobTickSalvage
    - PlushieCarp
    - DehydratedSpaceCarp
    chance: 0.75
    offset: 0.2

- type: entity
  name: "спавнер космічного кенгуру утилізаторів"
  id: SpawnMobKangarooSalvage
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: kangaroo-space
      sprite: Mobs/Animals/kangaroo.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobKangarooSpaceSalvage

- type: entity
  name: "спавнер космічного павука утилізаторів"
  id: SpawnMobSpiderSalvage
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: green
    - state: spacespider
      sprite: Mobs/Animals/spacespider.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobSpiderSpaceSalvage

- type: entity
  name: "спавнер космічної кобри утилізаторів"
  id: SpawnMobCobraSalvage
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - state: spacecobra
        sprite: Mobs/Animals/spacecobra.rsi
  - type: ConditionalSpawner
    prototypes:
    - MobCobraSpaceSalvage

- type: entity
  name: "спавнер плоті утилізаторів"
  id: SalvageFleshSpawner
  parent: SalvageMobSpawner
  suffix: 100
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Mobs/Aliens/flesh.rsi
          state: jared
    - type: RandomSpawner
      rarePrototypes:
      - MobSharkSalvage
      rareChance: 0
      prototypes:
      - MobFleshJaredSalvage
      - MobFleshGolemSalvage
      - MobFleshGolemSalvage
      - MobFleshClampSalvage
      - MobFleshClampSalvage
      - MobFleshLoverSalvage
      - MobFleshLoverSalvage
      chance: 1
      offset: 0.2

- type: entity
  name: "спавнер луту утилізаторів"
  id: SalvageLootSpawner
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Structures/Storage/Crates/generic.rsi
          state: icon
    - type: RandomSpawner
      rarePrototypes:
        - SalvagePartsT2Spawner
        - SalvagePartsT3Spawner
        - SalvagePartsT3T4Spawner
        - SalvagePartsT4Spawner
      rareChance: 0.4
      prototypes:
        - CrateSalvageAssortedGoodies
        - WeaponCrusher
        - WeaponCrusherDagger
        - WeaponCrusherGlaive
      chance: 0.9
      offset: 0.2
