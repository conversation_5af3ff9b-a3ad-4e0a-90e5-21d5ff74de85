- type: entity
  parent: [ BaseMachinePowered, ConstructibleMachine ]
  id: SalvageMagnet
  name: "магніт для порятунку"
  description: "Тягне врятовані речі."
  components:
  - type: Sprite
    sprite: Structures/Machines/salvage.rsi
    layers:
    - state: salvage-magnet
    - state: salvage-magnet-ready
      visible: false
      map: [ "ready" ]
    - state: salvage-magnet-ready-blinking
      visible: false
      map: [ "readyBlinking" ]
    - state: salvage-magnet-unready
      visible: false
      map: [ "unready" ]
    - state: salvage-magnet-unready-blinking
      visible: false
      map: [ "unreadyBlinking" ]
    - state: salvage-magnet-o4
      map: ["chargeState"]
      shader: unshaded
  - type: ActivatableUI
    key: enum.SalvageMagnetUiKey.Key
  - type: ActivatableUIRequiresPower
  - type: UserInterface
    interfaces:
      enum.SalvageMagnetUiKey.Key:
        type: SalvageMagnetBoundUserInterface
  - type: Transform
    noRot: false
  - type: Appearance
  - type: Rotatable
  - type: IntrinsicRadioReceiver
  - type: ActiveRadio
    channels:
    - Supply
  - type: SalvageMagnet
  - type: ApcPowerReceiver
    powerLoad: 2500 # TODO change this to a HV power draw that really hits the grid hard WHEN active
  - type: Machine
    board: SalvageMagnetMachineCircuitboard

# For Knightship
- type: entity
  parent: SalvageMagnet
  id: SalvageLocator
  name: "локатор порятунку"
  description: "Знаходить порятунок."
  components:
  - type: SalvageMagnet
  - type: ApcPowerReceiver
    powerLoad: 1000

- type: weightedRandomEntity
  id: RandomAsteroidPool
  weights:
    AsteroidSalvageSmall: 0 #Delta V: remove chance for small chunks
    AsteroidSalvageMedium: 7
    AsteroidSalvageLarge: 5
    AsteroidSalvageHuge: 3
