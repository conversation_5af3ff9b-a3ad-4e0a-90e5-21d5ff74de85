action-name-spin-web = Spin Web
action-desc-spin-web = Використовуйте спінеретки, щоб створити павутину на поточному тайлі. Змушує вас відчувати голод і спрагу.
action-name-spin-web-space = Ви не можете прясти павутину в космосі!
action-name-spin-web-blocked = Тут немає місця для павутини.
spin-web-action-hungry = Ти занадто голодний, щоб плести павутину!
spin-web-action-thirsty = Ти занадто спраглий, щоб плести павутину!
spin-web-start-second-person = Ти починаєш плести павутину.
spin-web-start-third-person = {CAPITALIZE(THE($spider))} починає прясти павутину!
cocoon-start-second-person = Ви починаєте коконувати {THE($target)}.
cocoon-start-third-person = {CAPITALIZE(THE($spider))} починає закутувати {THE($target)} в кокон.
spun-web-second-person = Ти плетеш павутину.
spun-web-third-person = {CAPITALIZE(THE($spider))} пряде павутину!
cocoon = Кокон

uncocoon-start-second-person = Ви починаєте відпускати {THE($target)}.
uncocoon-start-third-person = {CAPITALIZE(THE($spider))} починає відпускати {THE($target)}.
uncocoon = Вивільнити