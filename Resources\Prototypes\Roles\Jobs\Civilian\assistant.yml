- type: job
  id: Passenger
  name: job-name-passenger
  description: job-description-passenger
  playTimeTracker: JobPassenger
  startingGear: PassengerGear
  icon: "JobIconPassenger"
  supervisors: job-supervisors-everyone
  access:
  - Maintenance

- type: startingGear
  id: PassengerGear
  subGear:
  - PassengerPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitColor<PERSON>rey
    back: ClothingBackpackFilled
    shoes: ClothingShoesColorBlack
    id: PassengerPDA
    ears: ClothingHeadsetGrey
  innerClothingSkirt: ClothingUniformJumpskirtColorGrey
  satchel: ClothingBackpackSatchelFilled
  duffelbag: ClothingBackpackDuffelFilled

- type: startingGear
  id: PassengerPlasmamanGear
  parent: BasePlasmamanGear
  equipment:
    jumpsuit: ClothingUniformEnvirosuitColorGrey
    head: ClothingHeadEnvirohelmColorGrey
    gloves: ClothingHandsGlovesEnviroglovesColorDarkGrey
