- type: entity
  name: "спавнер медиботів"
  id: SpawnMobMedibot
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - sprite: Mobs/Silicon/Bots/medibot.rsi
        state: medibot
  - type: ConditionalSpawner
    prototypes:
      - MobMedibot

- type: entity
  name: "спавнер клінботів"
  id: SpawnMobCleanBot
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - sprite: Mobs/Silicon/Bots/cleanbot.rsi
        state: cleanbot
  - type: ConditionalSpawner
    prototypes:
      - MobCleanBot

- type: entity
  name: "спайдер disablerbot"
  id: SpawnMobDisablerBot
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - sprite: Mobs/Silicon/Bots/disablerbot.rsi
        state: disablerbot
  - type: ConditionalSpawner
    prototypes:
      - MobDisablerBot

- type: entity
  name: "батонбот-спойлер"
  id: SpawnMobBatonBot
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - sprite: Mobs/Silicon/Bots/batonbot.rsi
        state: batonbot
  - type: ConditionalSpawner
    prototypes:
      - MobBatonBot

- type: entity
  name: "точка появи рослинобота"
  id: SpawnMobPlantbot
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: green
      - sprite: Mobs/Silicon/Bots/plantbot.rsi
        state: plantbot
  - type: ConditionalSpawner
    prototypes:
      - MobPlantbot
