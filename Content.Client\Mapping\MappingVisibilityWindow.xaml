<DefaultWindow
    xmlns="https://spacestation14.io"
    xmlns:mapping="clr-namespace:Content.Client.Mapping"
    Title="{Loc 'mapping-visibility-window-title'}"
    Resizable="False"
    MinWidth="250">
    <BoxContainer Orientation="Vertical">
        <mapping:MappingVisibilityGroupButton Name="Light" Text="{Loc 'mapping-visibility-light'}" Access="Public"/>
        <mapping:MappingVisibilityButton Name="Fov" Text="{Loc 'mapping-visibility-fov'}" Access="Public"/>
        <mapping:MappingVisibilityButton Name="Shadows" Text="{Loc 'mapping-visibility-shadows'}" Access="Public"/>
        <mapping:MappingVisibilityGroupButton Name="Entities" Text="{Loc 'mapping-visibility-entities'}" Access="Public"/>
        <mapping:MappingVisibilityButton Name="Markers" Text="{Loc 'mapping-visibility-markers'}" Access="Public"/>
        <mapping:MappingVisibilityButton Name="Walls" Text="{Loc 'mapping-visibility-walls'}" Access="Public"/>
        <mapping:MappingVisibilityButton Name="Airlocks" Text="{Loc 'mapping-visibility-airlocks'}" Access="Public"/>
        <mapping:MappingVisibilityGroupButton Name="Decals" Text="{Loc 'mapping-visibility-decals'}" Access="Public"/>
        <mapping:MappingVisibilityGroupButton Name="SubFloor" Text="{Loc 'mapping-visibility-subfloor'}" Access="Public"/>
        <mapping:MappingVisibilityButton Name="Cables" Text="{Loc 'mapping-visibility-cables'}" Access="Public"/>
        <mapping:MappingVisibilityButton Name="Disposal" Text="{Loc 'mapping-visibility-disposal'}" Access="Public"/>
        <mapping:MappingVisibilityButton Name="Atmos" Text="{Loc 'mapping-visibility-atmos'}" Access="Public"/>
    </BoxContainer>
</DefaultWindow>
