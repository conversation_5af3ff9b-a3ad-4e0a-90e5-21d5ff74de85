- type: entity
  id: SignalTimer
  name: "таймер сигналу"
  description: "Настінний таймер для надсилання синхронізованих сигналів речам."
  placement:
    mode: SnapgridCenter
    snap:
    - Wallmount
  components:
  - type: StationAiWhitelist
  - type: Transform
    anchored: true
  - type: WallMount
    arc: 360
  - type: Clickable
  - type: InteractionOutline
  - type: Sprite
    drawdepth: SmallObjects
    sprite: Structures/Wallmounts/switch.rsi
    state: on
  - type: Appearance
  - type: Rotatable
  - type: SignalTimer
    canEditLabel: false
  - type: DeviceLinkSource
    ports:
      - Start
      - Timer
  - type: DeviceLinkSink
    ports:
      - Trigger
  - type: ActivatableUI
    key: enum.SignalTimerUiKey.Key
  - type: UserInterface
    interfaces:
      enum.SignalTimerUiKey.Key:
        type: SignalTimerBoundUserInterface
  - type: ApcPowerReceiver
    powerLoad: 100
  - type: Electrified
    enabled: false
    usesApcPower: true
  - type: ExtensionCableReceiver
  - type: ActivatableUIRequiresPower
  - type: Construction
    graph: Timer
    node: signal

- type: entity
  id: ScreenTimer
  parent: SignalTimer
  name: "таймер на екрані"
  description: "Настінний таймер для надсилання часових сигналів предметам. Цей має екран для відображення тексту."
  components:
  - type: SignalTimer
    canEditLabel: true
  - type: TextScreenVisuals
    color: FloralWhite
    textOffset: 0,8
    timerOffset: 0,8
    textLength: 5
    rows: 1
  - type: Sprite
    drawdepth: WallMountedItems
    sprite: Structures/Wallmounts/signalscreen.rsi
    state: signalscreen
    noRot: true
  - type: Construction
    graph: Timer
    node: screen

- type: entity
  id: BrigTimer
  parent: ScreenTimer
  name: "таймер гауптвахти"
  description: "Настінний таймер для надсилання часових сигналів предметам. Цей таймер має екран для відображення тексту і вимагає доступу до системи безпеки для використання."
  components:
  - type: AccessReader
    access: [["Security"]]
  - type: Construction
    graph: Timer
    node: brig

# Construction Frame

- type: entity
  categories: [ HideSpawnMenu ]
  id: TimerFrame
  name: "рамка таймера"
  description: "Будівельна рамка для таймера."
  placement:
    mode: SnapgridCenter
  components:
  - type: Clickable
  - type: InteractionOutline
  - type: Transform
    anchored: true
  - type: Sprite
    drawdepth: WallMountedItems
    sprite: Structures/Wallmounts/signalscreen.rsi
    state: signalscreen
  - type: Construction
    graph: Timer
    node: frame
  - type: WallMount
