- type: entity
  abstract: true
  parent: BaseItem
  id: Crayon
  name: "крейда"
  description: "Кольорова крейда. Виглядає смачно. Мммм..."
  components:
  - type: Sprite
    sprite: Objects/Fun/crayons.rsi
  - type: Item
    sprite: Objects/Fun/crayons.rsi
    size: Tiny
  - type: Tag
    tags:
    - Write
    - Crayon
    - Trash
  - type: SpaceGarbage
  - type: UserInterface
    interfaces:
      enum.CrayonUiKey.Key:
        type: CrayonBoundUserInterface
  - type: Crayon
    capacity: 25
  - type: Food
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
        - ReagentId: MindbreakerToxin
          Quantity: 2
  - type: StaticPrice
    price: 5

- type: entity
  parent: Crayon
  id: CrayonWhite
  name: "біла крейда"
  components:
  - type: Sprite
    state: white
  - type: Item
    heldPrefix: white
  - type: Crayon
    color: white
  - type: Tag
    tags:
    - Write
    - Crayon
    - CrayonWhite
    - Recyclable
    - Trash

- type: entity
  parent: Crayon
  id: CrayonMime
  name: "мімічна крейда"
  components:
  - type: Sprite
    state: mime
  - type: Item
    heldPrefix: mime
  - type: Crayon
    color: white
  - type: Tag
    tags:
    - Write
    - Crayon
    - CrayonWhite
    - Recyclable
    - Trash

- type: entity
  parent: Crayon
  id: CrayonRainbow
  name: "райдужна крейда"
  components:
  - type: Sprite
    state: rainbow
  - type: Item
    heldPrefix: rainbow
  - type: Crayon
    color: Red
    selectableColor: true
    capacity: 30
  - type: Tag
    tags:
    - Write
    - Crayon
    - Recyclable
    - Trash

- type: entity
  parent: Crayon
  id: CrayonBlack
  name: "чорна крейда"
  components:
  - type: Sprite
    state: black
  - type: Item
    heldPrefix: black
  - type: Crayon
    color: black
  - type: Tag
    tags:
    - Write
    - Crayon
    - CrayonBlack
    - Recyclable
    - Trash

- type: entity
  parent: Crayon
  id: CrayonRed
  name: "червона крейда"
  components:
  - type: Sprite
    state: red
  - type: Item
    heldPrefix: red
  - type: Crayon
    color: red
  - type: Tag
    tags:
    - Write
    - Crayon
    - CrayonRed
    - Recyclable
    - Trash

- type: entity
  parent: Crayon
  id: CrayonOrange
  name: "помаранчева крейда"
  components:
  - type: Sprite
    state: orange
  - type: Item
    heldPrefix: orange
  - type: Crayon
    color: orange
  - type: Tag
    tags:
    - Write
    - Crayon
    - CrayonOrange
    - Recyclable
    - Trash

- type: entity
  parent: Crayon
  id: CrayonYellow
  name: "жовта крейда"
  components:
  - type: Sprite
    state: yellow
  - type: Item
    heldPrefix: yellow
  - type: Crayon
    color: yellow
  - type: Tag
    tags:
    - Write
    - Crayon
    - CrayonYellow
    - Recyclable
    - Trash

- type: entity
  parent: Crayon
  id: CrayonGreen
  name: "зелена крейда"
  components:
  - type: Sprite
    state: green
  - type: Item
    heldPrefix: green
  - type: Crayon
    color: green
  - type: Tag
    tags:
    - Write
    - Crayon
    - CrayonGreen
    - Recyclable
    - Trash

- type: entity
  parent: Crayon
  id: CrayonBlue
  name: "синя крейда"
  components:
  - type: Sprite
    state: blue
  - type: Item
    heldPrefix: blue
  - type: Crayon
    color: lightblue
  - type: Tag
    tags:
    - Write
    - Crayon
    - CrayonBlue
    - Recyclable
    - Trash

- type: entity
  parent: Crayon
  id: CrayonPurple
  name: "фіолетова крейда"
  components:
  - type: Sprite
    state: purple
  - type: Item
    heldPrefix: purple
  - type: Crayon
    color: purple
  - type: Tag
    tags:
    - Write
    - Crayon
    - CrayonPurple
    - Recyclable
    - Trash

- type: entity
  parent: BoxCardboard
  id: CrayonBox
  name: "коробка для олівців"
  description: "Це коробка з олівцями."
  components:
  - type: Sprite
    sprite: Objects/Fun/crayons.rsi
    state: box
  - type: Storage
    grid:
    - 0,0,7,0
    maxItemSize: Tiny
  - type: Item
    sprite: Objects/Fun/crayons.rsi
    size: Small
    heldPrefix: box
  - type: StorageFill
    contents:
    - id: CrayonRed
    - id: CrayonOrange
    - id: CrayonYellow
    - id: CrayonGreen
    - id: CrayonBlue
    - id: CrayonPurple
    - id: CrayonBlack
    - id: CrayonWhite
  - type: ItemMapper
    mapLayers:
      black_box:
        whitelist:
          tags:
          - CrayonBlack
      blue_box:
        whitelist:
          tags:
          - CrayonBlue
      green_box:
        whitelist:
          tags:
          - CrayonGreen
      orange_box:
        whitelist:
          tags:
          - CrayonOrange
      purple_box:
        whitelist:
          tags:
          - CrayonPurple
      red_box:
        whitelist:
          tags:
          - CrayonRed
      yellow_box:
        whitelist:
          tags:
          - CrayonYellow
      white_box:
        whitelist:
          tags:
          - CrayonWhite
  - type: Appearance
