# - type: entity
#   parent: VendingMachine
#   id: VendingMachineMailDrobe # DeltaV - Replaced with the CourierDrobe
#   name: MailDrobe
#   description: Neither solar flares nor meteors nor plasma fire nor void of space stays these couriers from the swift completion of their appointed rounds.
#   components:
#   - type: VendingMachine
#     pack: MailDrobeInventory
#     offState: off
#     brokenState: broken
#     normalState: normal-unshaded
#     # ejectState: eject-unshaded  No sprite, see chefvend/dinnerware/BODA/etc for examples
#   - type: Advertise
#     pack: MailDrobeAds
#   - type: Sprite
#     sprite: Nyanotrasen/Structures/Machines/VendingMachines/maildrobe.rsi
#     layers:
#     - state: "off"
#       map: ["enum.VendingMachineVisualLayers.Base"]
#     - state: "off"
#       map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
#       shader: unshaded
#     - state: panel
#       map: ["enum.WiresVisualLayers.MaintenancePanel"]
#   - type: AccessReader
#     access: [["Mail"]]
#   - type: PointLight
#     radius: 1.5
#     energy: 1.6
#     color: "#4b93ad"

- type: entity
  parent: VendingMachine
  id: VendingMachineBoxingDrobe
  name: "Боксерський халат"
  description: "Завжди не вистачає на складі."
  components:
  - type: VendingMachine
    pack: BoxingDrobeInventory
  - type: Sprite
    sprite: Nyanotrasen/Structures/Machines/VendingMachines/boxingdrobe.rsi
    layers:
    - state: off
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: normal-unshaded
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Boxer"]]

- type: entity
  parent: VendingMachine
  id: VendingMachineRepDrobe
  name: "RepDrobe"
  description: "Машина для всіх ваших звітних потреб, доки вам потрібен одяг."
  components:
  - type: VendingMachine
    pack: RepDrobeInventory
  - type: Advertise
    pack: DetDrobeAds
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/detdrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: WiresVisuals
  - type: AccessReader
    access: [["Reporter"]]

- type: entity
  parent: VendingMachine
  id: VendingMachineMNKDrobe
  name: "MNK Drobe"
  description: "Якісний одяг наданий MoNoKrome."
  components:
  - type: VendingMachine
    pack: MNKDrobeInventory
    offState: off
    brokenState: broken
  - type: Sprite
    sprite: Nyanotrasen/Structures/Machines/VendingMachines/mnkdrobe.rsi
    layers:
    - state: off
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]

- type: entity
  parent: VendingMachineRoboDrobe
  id: VendingMachineAutomatrobe
  name: "Автомат"
  description: "Автоматично добре."
  components:
  - type: VendingMachine
    pack: AutomatrobeInventory
