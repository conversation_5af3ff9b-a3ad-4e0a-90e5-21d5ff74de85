name: Discord Changelog

on:
  workflow_dispatch:
  schedule:
  - cron: '0 6 * * *'

jobs:
  publish_changelog:
    runs-on: ubuntu-latest
    steps:

    - name: checkout
      uses: actions/checkout@v3
      with:
        ref: master

    - name: Publish changelog
      run: Tools/actions_changelogs_since_last_run.py
      env:
        CHANGELOG_DIR: ${{ vars.CHANGELOG_DIR }}
        CHANGELOG_WEBHOOK: ${{ secrets.CHANGELOG_WEBHOOK }}
        GITHUB_TOKEN: ${{ secrets.BOT_TOKEN }}
    continue-on-error: true
