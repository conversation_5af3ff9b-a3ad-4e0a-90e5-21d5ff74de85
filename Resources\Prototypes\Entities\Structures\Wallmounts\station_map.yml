- type: entity
  id: StationMapBroken
  name: "мапа станції"
  description: "Віртуальна карта околиць станції."
  suffix: Wall broken
  placement:
    mode: SnapgridCenter
  components:
    - type: InteractionOutline
    - type: Clickable
    - type: Transform
      anchored: true
    - type: Sprite
      sprite: Structures/Machines/station_map.rsi
      drawdepth: WallMountedItems
      layers:
        - state: station_map_broken
    - type: Damageable
      damageContainer: Inorganic
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 100
          behaviors:
            - !type:PlaySoundBehavior
              sound:
                collection: GlassBreak
            - !type:DoActsBehavior
              acts: [ "Destruction" ]

- type: entity
  id: StationMap
  name: "мапа станції"
  parent: BaseComputer
  description: "Віртуальна карта околиць станції."
  suffix: Wall
  placement:
    mode: SnapgridCenter
  components:
    - type: StationMap
    - type: Transform
      anchored: true
    - type: Sprite
      sprite: Structures/Machines/station_map.rsi
      layers:
      - map: ["computerLayerBody"]
        state: station_map0
      - map: ["computerLayerScreen"]
        state: unshaded
    - type: Icon
      sprite: Structures/Machines/station_map.rsi
      state: station_map0
    - type: ContainerFill
      containers:
        board: [ StationMapCircuitboard ]
    - type: ContainerContainer
      containers:
        board: !type:Container
    - type: ApcPowerReceiver
      powerLoad: 200
    - type: WallMount
      arc: 360
    - type: ExtensionCableReceiver
    - type: Construction
      graph: StationMap
      node: station_map
    - type: ActivatableUIRequiresPower
    - type: ActivatableUI
      key: enum.StationMapUiKey.Key
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 100
          behaviors:
            - !type:PlaySoundBehavior
              sound:
                collection: GlassBreak
            - !type:SpawnEntitiesBehavior
              spawn:
                StationMapBroken:
                  min: 1
                  max: 1
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
    - type: UserInterface
      interfaces:
        enum.StationMapUiKey.Key:
          type: StationMapBoundUserInterface

- type: entity
  id: StationMapAssembly
  name: "збірка мапи станції"
  description: "Збірка дисплею мапи станції."
  components:
  - type: WallMount
  - type: Clickable
  - type: InteractionOutline
  - type: Sprite
    sprite: Structures/Machines/station_map.rsi
    layers:
    - state: station_map_frame1
      map: [ "enum.ConstructionVisuals.Layer" ]
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ConstructionVisuals.Key:
        enum.ConstructionVisuals.Layer:
          assembly: { state: station_map_frame0 }
          wired: { state: station_map_frame1 }
          electronics: { state: station_map_frame2 }
  - type: Construction
    graph: StationMap
    node: assembly
  - type: Transform
    anchored: true
  placement:
    mode: SnapgridCenter
    snap:
    - Wallmount
