{"metadata": [{"src": [{"files": ["**.c<PERSON><PERSON>j"], "exclude": ["**/bin/**", "**/obj/**", "_site/**", "**.xaml"], "src": "../Content.Client"}], "disableGitFeatures": false, "disableDefaultFilter": false, "dest": "api/Content.Client"}, {"src": [{"files": ["**.c<PERSON><PERSON>j"], "exclude": ["**/bin/**", "**/obj/**", "_site/**", "**.xaml"], "src": "../Content.Server"}], "disableGitFeatures": false, "disableDefaultFilter": false, "dest": "api/Content.Server"}, {"src": [{"files": ["**.c<PERSON><PERSON>j"], "exclude": ["**/bin/**", "**/obj/**", "_site/**", "**.xaml"], "src": "../Content.Shared"}], "disableGitFeatures": false, "disableDefaultFilter": false, "dest": "api/Content.Shared"}, {"src": [{"files": ["**.c<PERSON><PERSON>j"], "exclude": ["**/bin/**", "**/obj/**", "_site/**", "**.xaml"], "src": "../Content.Tests"}], "disableGitFeatures": false, "disableDefaultFilter": false, "dest": "api/Content.Tests"}], "build": {"content": [{"files": ["api/**/**.yml"]}, {"files": ["articles/**.md", "articles/**/toc.yml", "toc.yml", "*.md"]}], "resource": [{"files": ["images/**", "favicon.ico", "icon.svg"]}], "overwrite": [{"files": ["apidoc/**.md"], "exclude": ["obj/**", "_site/**"]}], "dest": "_content-site", "globalMetadataFiles": [], "fileMetadataFiles": [], "template": ["default", "templates/darkfx"], "postProcessors": [], "markdownEngineName": "markdig", "noLangKeyword": false, "keepFileLink": false, "cleanupCacheHistory": false, "disableGitFeatures": false}}