- type: gameMap
  id: Glacier
  mapName: Льодовик
  mapPath: /Maps/glacier.yml
  minPlayers: 0
  stations:
    Glacier:
      stationProto: StandardNanotrasenStation
      components:
      - type: StationNameSetup
        mapNameTemplate: '{0} Glacier Station {1}'
        nameGenerator:
          !type:NanotrasenNameGenerator
          prefixCreator: 'NY'
      # uncomment when planets arent fucked sideways
      #- type: StationSurface
      #  mapPath: /Maps/Nonstations/glacier_surface.yml
      #  biome: Snow
      - type: StationJobs
        overflowJobs:
          - Passenger
        availableJobs:
          Passenger: [ -1, -1 ]
        # Command
          Captain: [ 1, 1 ]
          BlueshieldOfficer: [ 1, 1]
          NanotrasenRepresentative: [ 1, 1 ]
          Magistrate: [ 1, 1 ]
          AdministrativeAssistant: [ 1, 1 ]
          HeadOfPersonnel: [ 1, 1 ]
          ChiefEngineer: [ 1, 1 ]
          ChiefMedicalOfficer: [ 1, 1 ]
          ResearchDirector: [ 1, 1 ]
          HeadOfSecurity: [ 1, 1 ]
          Quartermaster: [ 1, 1 ]
        #service
          Bartender: [ 1, 2 ]
          Botanist: [ 2, 3 ]
          Chef: [ 1, 2 ]
          Clown: [ 1, 1 ]
          Reporter: [ 2, 2 ]
          Musician: [ 1, 2 ]
          ServiceWorker: [ 2, 3 ]
          Janitor: [ 2, 2 ]
          Mime: [ 1, 1 ]
        #engineering
          SeniorEngineer: [ 1, 1 ]
          AtmosphericTechnician: [ 1, 2 ]
          StationEngineer: [ 2, 3 ]
          TechnicalAssistant: [ 2, 2 ]
        #medical
          SeniorPhysician: [ 1, 1 ]
          Chemist: [ 1, 2 ]
          Paramedic: [ 1, 2 ]
          Psychologist: [ 1, 1 ]
          MedicalDoctor: [ 2, 3 ]
          MedicalIntern: [ 2, 2 ]
        #science
          SeniorResearcher: [ 1, 1 ]
          Chaplain: [ 1, 1 ]
          ForensicMantis: [ 1, 1 ]
          Scientist: [ 2, 3 ]
          Roboticist: [ 1, 2 ]
          ResearchAssistant: [ 2, 2 ]
          Librarian: [ 1, 1 ]
        #security
          SeniorOfficer: [ 1, 1 ]
          Warden: [ 1, 1 ]
          Detective: [ 1, 1 ]
          SecurityOfficer: [ 3, 4 ]
          SecurityCadet: [ 1, 2 ]
          Brigmedic: [ 1, 1 ]
          Prisoner: [ 2, 3 ]
        #logistics
          MailCarrier: [ 2, 3 ]
          SalvageSpecialist: [ 2, 3 ]
          CargoTechnician: [ 2, 3 ]
          #justice
          Clerk: [ 1, 1 ]
          Lawyer: [ 2, 2 ]
          Prosecutor: [ 2, 2 ]
        # Silicon
          StationAi: [ 1, 1 ]
          Borg: [ 2, 3 ]
      # blob-config-start MEDIUM
      - type: StationBlobConfig
        stageBegin: 30
        stageCritical: 375
        stageTheEnd: 750
      # blob-config-end
