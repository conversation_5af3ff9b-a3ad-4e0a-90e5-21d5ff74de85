﻿- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterChameleon
  name: "жилет"
  description: "Товстий жилет з гумовою, водостійкою оболонкою."
  suffix: Chameleon
  components:
    - type: Tag
      tags: [] # ignore "WhitelistChameleon" tag
    - type: Sprite
      sprite: Clothing/OuterClothing/Vests/vest.rsi
    - type: Clothing
      sprite: Clothing/OuterClothing/Vests/vest.rsi
    - type: ChameleonClothing
      slot: [outerClothing]
      default: ClothingOuterVest
    - type: UserInterface
      interfaces:
        enum.ChameleonUiKey.Key:
          type: ChameleonBoundUserInterface
