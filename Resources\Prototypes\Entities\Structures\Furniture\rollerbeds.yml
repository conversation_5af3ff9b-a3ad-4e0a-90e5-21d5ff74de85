- type: entity
  id: RollerBed
  parent: [BaseItem, BaseFoldable]
  name: "ліжко на роликах"
  description: "Використовували для перенесення пацієнтів, не пошкоджуючи їх."
  components:
    - type: Transform
      noRot: true
    - type: Item
      size: Normal
    - type: Sprite
      sprite: Structures/Furniture/rollerbeds.rsi
      noRot: true
      layers:
      - state: rollerbed
        map: ["unfoldedLayer"]
      - state: rollerbed_folded
        map: ["foldedLayer"]
        visible: false
      - state: rollerbed_buckled
        map: ["buckledLayer"]
        visible: false
    - type: MovedByPressure
    - type: DamageOnHighSpeedImpact
      soundHit:
        collection: MetalThud
    - type: InteractionOutline
    - type: Physics
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeCircle
            radius: 0.35
          density: 80
          mask:
          - MobMask
    - type: Damageable
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 50
          behaviors:
            - !type:DoActsBehavior
              acts: ["Destruction"]
            - !type:SpawnEntitiesBehavior
              spawn:
                SheetSteel1:
                  min: 1
                  max: 1
    - type: Pullable
    - type: Strap
      position: Down
      rotation: -90
      buckleOffset: "0,0.15"
      buckleOnInteractHand: False
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.StrapVisuals.State:
          buckledLayer:
            True: {visible: true}
            False: {visible: false}
        enum.FoldedVisuals.State: # Copypasta from BaseFoldable b/c collections don't merge when overriding component prototypes.
          foldedLayer:
            True: {visible: true}
            False: {visible: false}
          unfoldedLayer:
            True: {visible: false}
            False: {visible: true}
    - type: StaticPrice
      price: 120

- type: entity
  parent: RollerBed
  id: RollerBedSpawnFolded
  suffix: folded
  components:
  - type: Foldable
    folded: true
  - type: Strap
    enabled: False

- type: entity
  id: CheapRollerBed
  name: "ліжко на роликах"
  parent: RollerBed
  description: "Пошарпане ліжко на колесах. Використовували для перевезення хворих."
  components:
  - type: Sprite
    layers:
    - state: cheap_rollerbed
      map: ["unfoldedLayer"]
    - state: cheap_rollerbed_folded
      map: ["foldedLayer"]
      visible: false
    - state: cheap_rollerbed_buckled
      map: ["buckledLayer"]
      visible: false
  - type: Appearance

- type: entity
  parent: CheapRollerBed
  id: CheapRollerBedSpawnFolded
  suffix: folded
  components:
  - type: Foldable
    folded: true
  - type: Strap
    enabled: False

- type: entity
  id: EmergencyRollerBed
  name: "ліжко на роликах"
  parent: RollerBed
  description: "Міцне на вигляд підкатне ліжко, що використовується в екстрених випадках."
  components:
  - type: Sprite
    layers:
    - state: emergency_rollerbed
      map: ["unfoldedLayer"]
    - state: emergency_rollerbed_folded
      map: ["foldedLayer"]
      visible: false
    - state: emergency_rollerbed_buckled
      map: ["buckledLayer"]
      visible: false
  - type: Appearance

- type: entity
  parent: EmergencyRollerBed
  id: EmergencyRollerBedSpawnFolded
  suffix: folded
  components:
  - type: Foldable
    folded: true
  - type: Strap
    enabled: False
