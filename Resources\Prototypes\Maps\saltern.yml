- type: gameMap
  id: Saltern
  mapName: Салтерн
  mapPath: /Maps/saltern.yml
  minPlayers: 0
  fallback: true
  stations:
    Saltern:
      stationProto: StandardNanotrasenStationNoShuttles
      components:
        - type: StationNameSetup
          mapNameTemplate: '{0} Saltern {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: '14'
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/DeltaV/NTES_Delta.yml
        - type: StationJobs
          overflowJobs:
            - Passenger
          availableJobs:
          #command
            Captain: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1]
            NanotrasenRepresentative: [ 1, 1 ]
            # Magistrate: [ 1, 1 ] # EE: Disabled
            # AdministrativeAssistant: [ 1, 1 ] # EE: Disabled
          #service
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 1, 2 ]
            Botanist: [ 1, 3 ]
            Chef: [ 1, 2 ]
            Janitor: [ 1, 2 ]
            ServiceWorker: [ 2, 4 ]
          #engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 2, 2 ]
            StationEngineer: [ 4, 6 ]
            TechnicalAssistant: [ 4, 4 ]
          #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            Chemist: [ 2, 2 ]
            MedicalDoctor: [ 3, 5 ]
            MedicalIntern: [ 4, 6 ]
            Paramedic: [ 1, 2 ]
          #science
            ResearchDirector: [ 1, 1 ]
            Scientist: [ 4, 6 ]
            ResearchAssistant: [ 2, 4 ]
            Chaplain: [ 1, 1 ]
            ForensicMantis: [ 1, 1 ]
            Librarian: [ 1, 1 ]
          #security
            HeadOfSecurity: [ 1, 1 ]
            Warden: [ 1, 1 ]
            Detective: [ 1, 1 ]
            SecurityOfficer: [ 4, 6 ]
            SecurityCadet: [ 4, 6 ]
          #supply
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ 1, 3 ]
            CargoTechnician: [ 2, 4 ]
          #civilian
            Clown: [ 1, 1 ]
            Mime: [ 1, 1 ]
            Musician: [ 1, 2 ]
            Passenger: [ -1, -1 ]
          #silicon
            StationAi: [ 1, 1 ]
            Borg: [ 2, 2 ]
        # Goobstation blob-config-start SMALL+
        - type: StationBlobConfig
          stageBegin: 25
          stageCritical: 350
          stageTheEnd: 700
        # Goobstation blob-config-end
