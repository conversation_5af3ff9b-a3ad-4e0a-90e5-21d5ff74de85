signal-port-name-autoclose = Автозакриття
signal-port-description-autoclose = Дозволяє ввімкнути автоматичне закриття пристрою.

signal-port-name-toggle = Перемкнути
signal-port-description-toggle = Перемикає стан пристрою.

signal-port-name-on-receiver = Увімкнути
signal-port-description-on-receiver = Вмикає пристрій.

signal-port-name-off-receiver = Вимкнути
signal-port-description-off-receiver = Вимикає пристрій.

signal-port-name-forward = Вперед
signal-port-description-forward = Змушує пристрій (наприклад, конвеєр) працювати в нормальному напрямку.

signal-port-name-reverse = Назад
signal-port-description-reverse = Змушує пристрій (наприклад, конвеєр) працювати у зворотному напрямку.

signal-port-name-open = Відкрити
signal-port-description-open = Відкриває пристрій.

signal-port-name-close = Закрити
signal-port-description-close = Закриває пристрій.

signal-port-name-doorbolt = Болти двері
signal-port-description-doorbolt = Двері замикаються на засувку, коли вони високі.

signal-port-name-trigger = Запуск
signal-port-description-trigger = Запускає якийсь механізм на пристрої.

signal-port-name-order-sender = Відправник замовлення
signal-port-description-order-sender = Відправник замовлення вантажної консолі

signal-port-name-order-receiver = Одержувач замовлення
signal-port-description-order-receiver = Приймач замовлень на вантажну консоль

signal-port-name-pressurize = Герметизація
signal-port-description-pressurize = Призводить до того, що пристрій починає випускати повітря, поки не буде досягнуто певного цільового тиску.

signal-port-name-depressurize = Розгерметизація
signal-port-description-depressurize = Призводить до того, що пристрій починає відкачувати повітря, доки не буде досягнуто певного цільового тиску.

signal-port-name-pod-sender = Капсула клонування
signal-port-description-pod-sender = Відправник сигналу капсули клонування

signal-port-name-pod-receiver = Капсула клонування
signal-port-description-pod-receiver = Приймач сигналу капсули клонування

signal-port-name-med-scanner-sender = Медичний сканер
signal-port-description-med-scanner-sender = Передавач сигналу медичного сканера

signal-port-name-med-scanner-receiver = Медичний сканер
signal-port-description-med-scanner-receiver = Приймач сигналу медичного сканера

signal-port-name-hold-open = Зачекати
signal-port-description-hold-open = Вимикає автоматичне закриття.

signal-port-name-artifact-analyzer-sender = Консоль
signal-port-description-artifact-analyzer-sender = Відправник сигналу аналітичної консолі

signal-port-name-artifact-analyzer-receiver = Сканер
signal-port-description-artifact-analyzer-receiver = Приймач сигналу аналізатора артефактів

signal-port-name-set-particle-delta = Встановити тип частинок: дельта
signal-port-description-set-particle-delta = Встановлює тип частинок, які випромінює пристрій, на дельта.

signal-port-name-set-particle-epsilon = Тип встановленої частинки: епсилон
signal-port-description-set-particle-epsilon = Встановлює тип частинок, які випромінює цей пристрій, на епсилон.

signal-port-name-set-particle-zeta = Встановити тип частинок: zeta
signal-port-description-set-particle-zeta = Задає тип частинок, які пристрій випромінює на дзета.

signal-port-name-logic-input-a = Вхід A
signal-port-description-logic-input-a = Перший вхід логічного вентилю.

signal-port-name-logic-input-b = Вхід B
signal-port-description-logic-input-b = Другий вхід логічного вентилю.

signal-port-name-logic-input = Вхід
signal-port-description-logic-input = Вхідний сигнал на детектор краю, не може бути імпульсним.

signal-port-name-set-particle-sigma = Встановити тип частинок: сигма
signal-port-description-set-particle-sigma = Встановлює тип частинок, які випромінює цей пристрій, на сигму.
signal-port-name-material-silo-utilizer = Утилізатор силосу
signal-port-description-material-silo-utilizer = Утилізатор для силосу станційного матеріалу
signal-port-name-fill-any-item = Ціль Філлбота
signal-port-description-fill-any-item = Цільовий порт для вставки Філлбота.