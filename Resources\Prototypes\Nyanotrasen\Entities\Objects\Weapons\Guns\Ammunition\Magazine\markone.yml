- type: entity
  id: MagazineLightRifleMarkOneEmpty
  name: "пачка до гвинтівки Mark 1 (порожня) (.30 rifle any)"
  suffix: empty
  parent: MagazineLightRifle
  components:
  - type: Tag
    tags:
    - MagazineMarkOne
  - type: BallisticAmmoProvider
    proto: null
    capacity: 8
  - type: Item
    size: Tiny
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/SpeedLoaders/LightRifle/light_rifle_speed_loader.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 6
    zeroVisible: false

- type: entity
  id: MagazineLightRifleMarkOne
  name: "пачка до гвинтівки Mark 1 (.30 rifle)"
  parent: BaseMagazineLightRifle
  components:
  - type: Tag
    tags:
    - MagazineMarkOne
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifle
    capacity: 8
  - type: Item
    size: Tiny
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/SpeedLoaders/LightRifle/light_rifle_speed_loader.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 6
    zeroVisible: false
