- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterApron
  name: "фартух"
  description: "Модний фартух для стильної людини."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/apron.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/apron.rsi

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterApronBar
  suffix: Bartender
  name: "фартух"
  description: "Темніший фартух призначений для барменів."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/apronbar.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/apronbar.rsi

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterApronBotanist
  name: "фартух"
  suffix: Botanical
  description: "Щільний синій фартух, що ідеально захищає вашу ніжну плоть від пролитої рідини, землі та колючок."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/apronbotanist.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/apronbotanist.rsi

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterApronChef
  name: "фартух"
  suffix: Chef
  description: "Фартух-куртка, що використовується шеф-кухарем високого класу."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/apronchef.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/apronchef.rsi

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterJacketChef
  name: "куртка шеф-кухаря"
  description: "Фартух-куртка, що використовується шеф-кухарем високого класу."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/chef.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/chef.rsi

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterJacketChefNt
  name: "куртка шеф-кухаря Нанотрейзен"
  description: "Фартух-куртка, що використовується висококласним шеф-кухарем корпорації Nanotrasen."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/chef_nt.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/chef_nt.rsi

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterJacketChefIdris
  name: "куртка ідріса шеф-кухаря"
  description: "Фартух-куртка, що використовується висококласним шеф-кухарем Idris Incorporated."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/chef_idris.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/chef_idris.rsi

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterJacketChefOrion
  name: "куртка шеф-кухаря \"Оріон"
  description: "Фартух-куртка, яку використовує висококласний експрес-шеф ресторану \"Оріон\"."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/chef_orion.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/chef_orion.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterHoodieBlack
  name: "чорний балахон"
  description: "Боже мій, це ж чорний балахон!"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/black_hoodie.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/black_hoodie.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterHoodieGrey
  name: "сіра куртка з капюшоном"
  description: "Сіра куртка з капюшоном."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/grey_hoodie.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/grey_hoodie.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterCardborg
  name: "костюм кардборга"
  description: "Звичайна картонна коробка з вирізаними з боків отворами."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/cardborg.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/cardborg.rsi
  - type: TypingIndicatorClothing
    proto: robot
  - type: Tag
    tags:
    - WhitelistChameleon

- type: entity
  parent: ClothingOuterBaseToggleable
  id: ClothingOuterHoodieChaplain
  name: "капюшон капелана"
  description: "Чорний і строгий капеланський балахон."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/chaplain_hoodie.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/chaplain_hoodie.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodChaplainHood

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterPonchoClassic
  name: "класичне пончо"
  description: "Тепле і зручне класичне пончо."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/classicponcho.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/classicponcho.rsi
  - type: AddAccentClothing
    accent: SpanishAccent

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterRobesCult
  name: "одяг культиста"
  description: "Не існує культу без класичних червоних/малинових культових шат."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/cultrobes.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/cultrobes.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterRobesJudge
  name: "суддівські мантії"
  description: "Ця мантія викликає довіру."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/judge.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/judge.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterPoncho
  name: "пончо"
  description: "Тепле та зручне пончо."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/poncho.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/poncho.rsi
  - type: AddAccentClothing
    accent: SpanishAccent

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterSanta
  name: "костюм Санти"
  description: "Хо-хо-хо!"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/santa.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/santa.rsi

- type: entity
  abstract: true
  parent: [ClothingOuterBase, AllowSuitStorageClothing] # Goob edit
  id: ClothingOuterWizardBase
  suffix: Wizard Clothes # Goobstation
  components:
  - type: WizardClothes
  - type: Tag # Goobstation
    tags:
      - WhitelistChameleon
      - SyringeArmor

# Is this wizard wearing a fanny pack???
- type: entity
  parent: ClothingOuterBase # Goob edit
  id: ClothingOuterWizardViolet
  name: "фіолетові мантії чарівника"
  description: "Химерний фіолетовий халат, інкрустований дорогоцінним камінням, що випромінює магічну енергію."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/violetwizard.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/violetwizard.rsi

- type: entity
  parent: ClothingOuterBase # Goob edit
  id: ClothingOuterWizard
  name: "мантії чарівників"
  description: "Химерна, інкрустована дорогоцінними каменями синя мантія, що випромінює магічну енергію."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/wizard.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/wizard.rsi

- type: entity
  parent: ClothingOuterBase # Goob edit
  id: ClothingOuterWizardRed
  name: "червоні мантії чарівника"
  description: "Дивний на вигляд, червоний, капелюх, який, безсумнівно, належить справжньому магічному користувачеві." # Goob edit
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/redwizard.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/redwizard.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterSkub
  name: "скаб-костюм"
  description: "На зовнішньому боці цього циліндричного костюма грубо написано \"скаб\"."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/skubbody.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/skubbody.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterPlagueSuit
  name: "костюм чумного лікаря"
  description: "Поганий знак."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/plaguedoctorsuit.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/plaguedoctorsuit.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterNunRobe
  name: "чернеча ряса"
  description: "Максимальна побожність у цій зоряній системі."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/nunrobe.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/nunrobe.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterGhostSheet
  name: "примарне покривало"
  description: "Моторошно!!!"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/ghostsheet.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/ghostsheet.rsi
  - type: Construction
    graph: GhostSheet
    node: ghost_sheet
  - type: IdentityBlocker

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterHospitalGown
  name: "лікарняний халат"
  description: "Виготовляється з вовни забитих ягнят.  Жорстокість робить її м'якшою."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/hospitalgown.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/hospitalgown.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterFlannelRed
  name: "червона фланелева сорочка"
  description: "Старомодний піджак з червоної фланелі для космічної осені."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/flannel_jacket.rsi
    layers:
    - state: icon
      color: "#670a09"
    - state: icon-lines
      color: "#000000"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#670a09"
      - state: inhand-left-lines
        color: "#000000"
      right:
      - state: inhand-right
        color: "#670a09"
      - state: inhand-right-lines
        color: "#000000"
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/flannel_jacket.rsi
    clothingVisuals:
      outerClothing:
      - state: equipped-OUTERCLOTHING
        color: "#670a09"
      - state: equipped-OUTERCLOTHING-lines
        color: "#000000"
  - type: TemperatureProtection
    coefficient: 0.3

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterFlannelBlue
  name: "синя фланелева сорочка"
  description: "Старомодний синій фланелевий жакет для космічної осені."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/flannel_jacket.rsi
    layers:
    - state: icon
      color: "#3232a6"
    - state: icon-lines
      color: "#000000"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#3232a6"
      - state: inhand-left-lines
        color: "#000000"
      right:
      - state: inhand-right
        color: "#3232a6"
      - state: inhand-right-lines
        color: "#000000"
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/flannel_jacket.rsi
    clothingVisuals:
      outerClothing:
      - state: equipped-OUTERCLOTHING
        color: "#3232a6"
      - state: equipped-OUTERCLOTHING-lines
        color: "#000000"
  - type: TemperatureProtection
    coefficient: 0.3

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterFlannelGreen
  name: "зелена фланелева сорочка"
  description: "Старомодний зелений фланелевий жакет для космічної осені."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/flannel_jacket.rsi
    layers:
    - state: icon
      color: "#164d0f"
    - state: icon-lines
      color: "#000000"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#164d0f"
      - state: inhand-left-lines
        color: "#000000"
      right:
      - state: inhand-right
        color: "#164d0f"
      - state: inhand-right-lines
        color: "#000000"
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/flannel_jacket.rsi
    clothingVisuals:
      outerClothing:
      - state: equipped-OUTERCLOTHING
        color: "#164d0f"
      - state: equipped-OUTERCLOTHING-lines
        color: "#000000"
  - type: TemperatureProtection
    coefficient: 0.3

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterRedRacoon
  name: "костюм рудого єнота"
  description: "Пухнастий костюм рудого єнота!"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/red_racoon.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/red_racoon.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterUnathiRobe
  name: "грубосплетені халати"
  description: "Традиційний одяг унатів."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/unathirobe.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/unathirobe.rsi

# - type: entity # TODO: There no use for it now... but pehaps we could turn it into a Anti-Psionic Clothing like headcage?
#   parent: ClothingOuterBase
#   id: ClothingOuterShadowkinRestraints
#   name: shadowkin restraints
#   description: One of the first creations after finding Shadowkin, these were used to contain the Shadowkin during research so they didn't teleport away.
#   components:
#   - type: Sprite
#     sprite: Clothing/OuterClothing/Misc/shadowkinrestraints.rsi
#   - type: Clothing
#     sprite: Clothing/OuterClothing/Misc/shadowkinrestraints.rsi
#     equipDelay: 0.5
#     unequipDelay: 10
#   - type: GuideHelp
#     guides:
#     - Shadowkin

- type: entity
  parent: ClothingOuterStorageFoldableBase
  id: ClothingOuterSuitJacket
  name: "піджак"
  description: "Коротенька сукняна куртка."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/suit_jacket.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/suit_jacket.rsi

- type: entity
  parent: [ClothingOuterStorageFoldableBaseOpened, ClothingOuterSuitJacket]
  id: ClothingOuterSuitJacketOpened
  name: "піджак"

- type: entity
  parent: ClothingOuterFoldableBase
  id: ClothingOuterCardigan
  name: "кардиган"
  description: "Лише трохи гірше, ніж ковдра."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/cardigan.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/cardigan.rsi

- type: entity
  parent: [ClothingOuterFoldableBaseOpened, ClothingOuterCardigan]
  id: ClothingOuterCardiganOpened
  name: "кардиган"

- type: entity
  parent: ClothingOuterFoldableBase
  id: ClothingOuterCardiganSweater
  name: "светр кардиган"
  description: "Наполовину тепліший, ніж светр, і наполовину модніший, ніж кардиган. Для бітників, що живуть у кав'ярнях, як ви, це не має значення."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/cardigan_sweater.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/cardigan_sweater.rsi

- type: entity
  parent: [ClothingOuterFoldableBaseOpened, ClothingOuterCardiganSweater]
  id: ClothingOuterCardiganSweaterOpened
  name: "светр кардиган"

- type: entity
  parent: ClothingOuterFoldableBase
  id: ClothingOuterCardiganArgyle
  name: "кардиган в клітинку"
  description: "Вас ніколи не кинуть, якщо ви ніколи не вступали у стосунки. З цим вам більше ніколи не доведеться хвилюватися."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/cardigan_argyle.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/cardigan_argyle.rsi

- type: entity
  parent: [ClothingOuterFoldableBaseOpened, ClothingOuterCardiganArgyle]
  id: ClothingOuterCardiganArgyleOpened
  name: "кардиган в клітинку"

- type: entity
  parent: ClothingOuterFoldableBase
  id: ClothingOuterCardiganSlim
  name: "тонкий кардиган"
  description: "Затишний гладкий кардиган в класичному стилі."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/cardigan_slim.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/cardigan_slim.rsi

- type: entity
  parent: [ClothingOuterFoldableBaseOpened, ClothingOuterCardiganSlim]
  id: ClothingOuterCardiganSlimOpened
  name: "тонкий кардиган"

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterCowboyDuster
  name: "ковбойський пилосос"
  description: "Пильовик, який часто можна побачити на ковбоях з кінця 1800-х років."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/cowboy_duster.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/cowboy_duster.rsi

- type: entity
  parent: ClothingOuterStorageFoldableBase
  id: ClothingOuterBlazer
  name: "піджак"
  description: "Чарівна куртка для тих, хто хоче грати в бейсбол, як аристократ."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/blazer.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/blazer.rsi

- type: entity
  parent: [ClothingOuterFoldableBaseOpened, ClothingOuterBlazer]
  id: ClothingOuterBlazerOpened
  name: "піджак"

- type: entity
  parent: ClothingOuterStorageFoldableBase
  id: ClothingOuterBlazerLong
  name: "довгий піджак"
  description: "Чарівний довгий жакет, який майже замінює пальто."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/blazer_long.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/blazer_long.rsi

- type: entity
  parent: [ClothingOuterFoldableBaseOpened, ClothingOuterBlazerLong]
  id: ClothingOuterBlazerLongOpened
  name: "довгий піджак"

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterBlazerFormal
  name: "формальний піджак"
  description: "Приталений блейзер підійде як для повсякденного, так і для офіційного вбрання."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/blazer_formal.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/blazer_formal.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterBlazerLooseSleeve
  name: "піджак з вільним рукавом"
  description: "Один з тих хіпі-формальних піджаків."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/blazer_loose_sleeve.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/blazer_loose_sleeve.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterBlazerFlatCollar
  name: "піджак з плоским коміром"
  description: "Для тих дорослих, які не хотіли."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/blazer_flat_collar.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/blazer_flat_collar.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterBlazerPosh
  name: "шикарний піджак"
  description: "Це трохи вигадливо, як і новий сервісний працівник у Солтерні, який надто сильно намагається справити враження на керівників."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Misc/blazer_posh.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Misc/blazer_posh.rsi
