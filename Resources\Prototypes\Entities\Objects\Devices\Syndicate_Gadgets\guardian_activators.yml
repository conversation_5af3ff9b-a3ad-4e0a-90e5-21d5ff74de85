- type: entity
  name: "ін'єктор голопаразитів"
  id: HoloparasiteInjector
  parent: BaseItem
  description: "Складний витвір ручної техніки, що дозволяє користувачеві приймати у себе охоронця-голопаразита."
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/hypospray.rsi
    state: combat_hypo
  - type: GuardianCreator
    guardianProto: MobHoloparasiteGuardian

- type: entity
  name: "інжектор холоклоуна"
  parent: HoloparasiteInjector
  id: HoloClownInjector
  description: "Складний витвір мистецтва з ручної техніки, що дозволяє користувачеві приймати опікуна-голоклоуна."
  components:
  - type: GuardianCreator
    guardianProto: MobHoloClownGuardian

- type: entity
  name: "чарівна лампа"
  id: MagicalLamp
  parent: [ArabianLamp] # Goob edit
  description: "Федерація чарівників мусила якось скоротити витрати після скандалу з товарами для джинів."
  components:
  - type: GuardianCreator
    guardianProto: MobIfritGuardian

- type: entity
  name: "ящик для голопаразитів"
  parent: BoxCardboard
  id: BoxHoloparasite
  description: "Коробка з ін'єктором для голопаразитів"
  components:
  - type: StorageFill
    contents:
      - id: HoloparasiteInjector
      - id: HoloparasiteInfo
      - id: ClothingHeadHatBizarreSoft
  - type: Sprite
    layers:
      - state: box
      - state: holo

- type: entity
  name: "голографічна коробка"
  parent: BoxCardboard
  id: BoxHoloclown
  description: "Коробка з інжектором холостого ходу"
  components:
  - type: StorageFill
    contents:
      - id: HoloClownInjector
      - id: ToyFigurineHoloClown
      - id: ToyHammer
  - type: Sprite
    layers:
      - state: box_hug
      - state: holo
  - type: Tag
    tags:
      - BoxHug
