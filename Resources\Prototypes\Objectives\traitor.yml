- type: entity
  abstract: true
  parent: BaseObjective
  id: BaseTraitorObjective
  components:
  - type: Objective
    issuer: syndicate
  - type: RoleRequirement
    roles:
      mindRoles:
      - TraitorRole

- type: entity
  abstract: true
  parent: [BaseTraitorObjective, BaseSocialObjective]
  id: BaseTraitorSocialObjective
  components:
  - type: Objective
    icon:
      sprite: Objects/Misc/bureaucracy.rsi
      state: folder-white
  - type: MultipleTraitorsRequirement

- type: entity
  abstract: true
  parent: [BaseTraitorObjective, BaseStealObjective]
  id: BaseTraitorStealObjective
  components:
  - type: StealCondition
    verifyMapExistence: false
  - type: Objective
    difficulty: 2.75
  - type: ObjectiveLimit
    limit: 2 # there is usually only 1 of each steal objective, have 2 max for drama

- type: entity # Head of Security steal objective.
  categories: [HideSpawnMenu]
  parent: BaseTraitorStealObjective
  id: HoSAntiqueWeaponStealObjective
  components:
  - type: Objective
    difficulty: 3 # HoS will mostly be using the gun to stop you from stealing it
  - type: NotJobRequirement
    job: HeadOfSecurity
  - type: StealCondition
    verifyMapExistence: true
    stealGroup: HoSAntiqueWeapon
    owner: job-name-hos

# state

- type: entity
  categories: [HideSpawnMenu]
  parent: [BaseTraitorObjective, BaseLivingObjective]
  id: EscapeShuttleObjective
  name: "Втекти до центру живим і не затриманим."
  description: "Один з наших агентів під прикриттям підведе підсумки, коли ви прибудете. Не з'являйтеся в наручниках."
  components:
  - type: Objective
    difficulty: 1.3
    icon:
      sprite: Structures/Furniture/chairs.rsi
      state: shuttle
  - type: EscapeShuttleCondition

- type: entity
  categories: [HideSpawnMenu]
  parent: BaseTraitorObjective
  id: DieObjective
  name: "Померти славною смертю"
  description: "Здохни."
  components:
  - type: Objective
    difficulty: 0.5
    icon:
      sprite: Mobs/Ghosts/ghost_human.rsi
      state: icon
  - type: ObjectiveBlacklistRequirement
    blacklist:
      components:
      - EscapeShuttleCondition
      - StealCondition
  - type: DieCondition

- type: entity
  categories: [HideSpawnMenu]
  parent: [BaseTraitorObjective, BaseLivingObjective]
  id: HijackShuttleObjective
  name: "Викрасти аварійний шатл"
  description: "Вийдіть з шатла вільно і без лояльного екіпажу Нанотрасен на борту. Використовуйте БУДЬ-ЯКІ доступні вам методи. На шатлі можуть залишитися живими агенти Синдикату, вороги Нанотрасену та заручники в наручниках. Ігноруйте допомогу від будь-кого, окрім агента підтримки."
  components:
  - type: Objective
    difficulty: 5 # insane, default config max difficulty
    icon:
      sprite: Objects/Tools/emag.rsi
      state: icon
  - type: HijackShuttleCondition

# kill

- type: entity
  categories: [HideSpawnMenu]
  parent: [BaseTraitorObjective, BaseKillObjective]
  id: KillRandomPersonObjective
  description: "Зробіть це як захочете, тільки переконайтеся, щоб вони не потрапили до ЦК."
  components:
  - type: Objective
    difficulty: 1.75
    unique: false
  - type: TargetObjective
    title: objective-condition-kill-person-title
  - type: PickRandomPerson

- type: entity
  categories: [HideSpawnMenu]
  parent: [BaseTraitorObjective, BaseKillObjective]
  id: KillRandomHeadObjective
  description: "Нам потрібно, щоб ця голова відділу зникла, і ви, мабуть, знаєте чому. Удачі, агенте."
  components:
  - type: Objective
    # technically its still possible for KillRandomPersonObjective to roll a head but this is guaranteed, so higher difficulty
    difficulty: 3.0
    # killing 1 head is enough
    unique: true
  - type: TargetObjective
    title: objective-condition-kill-head-title
  - type: PickRandomHead
  - type: KillPersonCondition
    # don't count missing evac as killing as heads are higher profile, so you really need to do the dirty work
    # if ce flies a shittle to centcom you better find a way onto it
    requireDead: true

# social

- type: entity
  categories: [HideSpawnMenu]
  parent: [BaseTraitorSocialObjective, BaseKeepAliveObjective]
  id: RandomTraitorAliveObjective
  description: "Ідентифікуйте себе на свій страх і ризик. Він потрібен нам живим."
  components:
  - type: Objective
    difficulty: 1.75
  - type: TargetObjective
    title: objective-condition-other-traitor-alive-title
  - type: RandomTraitorAlive

- type: entity
  categories: [HideSpawnMenu]
  parent: [BaseTraitorSocialObjective, BaseHelpProgressObjective]
  id: RandomTraitorProgressObjective
  description: "Ідентифікуйте себе на свій страх і ризик. Нам потрібно, щоб він досяг успіху."
  components:
  - type: Objective
    difficulty: 2.5
  - type: TargetObjective
    title: objective-condition-other-traitor-progress-title
  - type: RandomTraitorProgress

# steal

## cmo

- type: entity
  abstract: true
  parent: BaseTraitorStealObjective
  id: BaseCMOStealObjective
  components:
  - type: NotJobRequirement
    job: ChiefMedicalOfficer
  - type: StealCondition
    owner: job-name-cmo

- type: entity
  categories: [HideSpawnMenu]
  parent: BaseCMOStealObjective
  id: CMOHyposprayStealObjective
  components:
  - type: StealCondition
    stealGroup: Hypospray
    verifyMapExistence: true

# This is going back in Loadouts. Not worth fucking over Paramedics.
#- type: entity
#  categories: [ HideSpawnMenu ]
#  parent: BaseCMOStealObjective
#  id: CMOCrewMonitorStealObjective
#  components:
#  - type: StealCondition
#    stealGroup: HandheldCrewMonitor
#    verifyMapExistence: true

## rd

- type: entity
  abstract: true
  parent: BaseTraitorStealObjective
  id: BaseRDStealObjective
  components:
  - type: NotJobRequirement
    job: ResearchDirector
  - type: StealCondition
    owner: job-name-rd

- type: entity
  categories: [HideSpawnMenu]
  parent: BaseRDStealObjective
  id: RDHardsuitStealObjective
  components:
  - type: StealCondition
    stealGroup: ClothingOuterHardsuitRd
    verifyMapExistence: true
  - type: Objective
    # This item must be worn or stored in a slowing duffelbag, very hard to hide.
    difficulty: 3

- type: entity
  categories: [HideSpawnMenu]
  parent: BaseRDStealObjective
  id: HandTeleporterStealObjective
  components:
  - type: StealCondition
    stealGroup: HandTeleporter
    verifyMapExistence: true

## ce

- type: entity
  categories: [HideSpawnMenu]
  parent: BaseTraitorStealObjective
  id: MagbootsStealObjective
  components:
  - type: NotJobRequirement
    job: ChiefEngineer
  - type: StealCondition
    stealGroup: ClothingShoesBootsMagAdv
    verifyMapExistence: true
    owner: job-name-ce

## qm

- type: entity
  categories: [HideSpawnMenu]
  parent: BaseTraitorStealObjective
  id: ClipboardStealObjective
  components:
  - type: NotJobRequirement
    job: Quartermaster
  - type: StealCondition
    stealGroup: BoxFolderQmClipboard
    verifyMapExistence: true
    owner: job-name-qm

## hop

- type: entity
  categories: [HideSpawnMenu]
  parent: BaseTraitorStealObjective
  id: CorgiMeatStealObjective
  components:
  - type: NotJobRequirement
    job: HeadOfPersonnel
  - type: ObjectiveLimit
    limit: 3 # ian only has 2 slices, 3 obj for drama
  - type: StealCondition
    stealGroup: FoodMeatCorgi
    owner: objective-condition-steal-Ian

## cap

- type: entity
  abstract: true
  parent: BaseTraitorStealObjective
  id: BaseCaptainObjective
  components:
  - type: Objective
    # sorry ce but your jordans are not as high security as the caps gear
    difficulty: 3.5
  - type: NotJobRequirement
    job: Captain

- type: entity
  categories: [HideSpawnMenu]
  parent: BaseCaptainObjective
  id: CaptainIDStealObjective
  components:
  - type: StealCondition
    stealGroup: CaptainIDCard
    verifyMapExistence: true

- type: entity
  categories: [HideSpawnMenu]
  parent: BaseCaptainObjective
  id: CaptainJetpackStealObjective
  components:
  - type: StealCondition
    stealGroup: JetpackCaptainFilled
    verifyMapExistence: true

- type: entity
  categories: [HideSpawnMenu]
  parent: BaseCaptainObjective
  id: CaptainGunStealObjective
  components:
  - type: StealCondition
    stealGroup: WeaponCaptain
    owner: job-name-captain
    verifyMapExistence: true

- type: entity
  categories: [HideSpawnMenu]
  parent: BaseCaptainObjective
  id: NukeDiskStealObjective
  components:
  - type: Objective
    # high difficulty since the hardest item both to steal, and to not get caught down the road,
    # since anyone with a pinpointer can track you down and kill you
    # it's close to being a stealth loneop
    difficulty: 4
  - type: NotCommandRequirement
  - type: StealCondition
    stealGroup: NukeDisk
    verifyMapExistence: true
    owner: objective-condition-steal-station

- type: entity
  categories: [HideSpawnMenu]
  parent: BaseTraitorStealObjective
  id: StealSupermatterSliverObjective
  components:
  - type: Objective
    difficulty: 3.5
  - type: StealCondition
    stealGroup: SupermatterSliver
    objectiveNoOwnerText: objective-condition-steal-smsliver-title
    descriptionText: objective-condition-steal-smsliver-description

## ntrep

- type: entity
  abstract: true
  parent: BaseTraitorStealObjective
  id: BaseNTRepObjective
  components:
  - type: Objective
    difficulty: 2.5
  - type: NotJobRequirement
    job: NanotrasenRepresentative

- type: entity
  categories: [HideSpawnMenu]
  parent: BaseNTRepObjective
  id: NTRepGunStealObjective
  components:
  - type: StealCondition
    stealGroup: NTRepWeapon
    owner: job-name-ntrep
    verifyMapExistence: true
