# changestockprice command
cmd-changestocksprice-desc = Changes a company's stock price to the specified number.
cmd-changestocksprice-help = changestockprice <Company index> <New price> [Station UID]
cmd-changestocksprice-invalid-company = Failed to execute command! Invalid company index or the new price exceeds the allowed limit.
cmd-changestocksprice-invalid-station = No stock market found for specified station
cmd-changestocksprice-no-stations = No stations with stock markets found

# addstockscompany command
cmd-addstockscompany-desc = Adds a new company to the stocks market.
cmd-addstockscompany-help = addstockscompany <Display name> <Base price> [Station UID]
cmd-addstockscompany-failure = Failed to add company to the stock market.
cmd-addstockscompany-invalid-station = No stock market found for specified station
cmd-addstockscompany-no-stations = No stations with stock markets found
