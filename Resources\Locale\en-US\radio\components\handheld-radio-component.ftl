handheld-radio-component-on-use = The radio is now {$radioState}.
handheld-radio-component-on-examine = Frequency: [color={$color}]{$frequency}[/color]
handheld-radio-component-on-state = on
handheld-radio-component-off-state = off
handheld-radio-component-channel-set = Channel set to {$channel}
handheld-radio-component-channel-examine = Channel: [color={$color}]{$channel}[/color]

# Nuclear-14-Start
handheld-radio-menu-title = Handheld radio
handheld-radio-current-text-frequency = Broadcast frequency
handheld-radio-button-text-mic = Mic.
handheld-radio-button-text-speaker = Spkr.
handheld-radio-flavor-text-left = HandiComms, 1000-3000 kHz
# Nuclear-14-End