- type: entity
  name: "енергетичний меч"
  parent: BaseItem
  id: EnergySword
  description: "Дуже гучний і небезпечний меч з променем з чистої, концентрованої плазми. Розсікає неброньовані цілі як масло."
  components:
  - type: EnergySword
  - type: ItemToggle
    soundActivate:
      path: /Audio/Weapons/ebladeon.ogg
    soundDeactivate:
      path: /Audio/Weapons/ebladeoff.ogg
  - type: ItemToggleActiveSound
    activeSound:
      path: /Audio/Weapons/ebladehum.ogg
  - type: ComponentToggler
    components:
    - type: Sharp
    - type: DisarmMalus
      malus: 0.6
  - type: ItemToggleHot
  - type: ItemToggleSize
    activatedSize: Huge
  - type: ItemTogglePointLight
  - type: ItemToggleMeleeWeapon
    activatedSoundOnHit:
      path: /Audio/Weapons/eblade1.ogg
      params:
        variation: 0.250
    activatedSoundOnHitNoDamage:
      path: /Audio/Weapons/eblade1.ogg
      params:
        variation: 0.250
        volume: -10
    activatedSoundOnSwing:
      path: /Audio/Weapons/eblademiss.ogg
      params:
        variation: 0.125
    activatedDamage:
        types:
            Slash: 12
            Heat: 15 # Should be significantly stronger than other variants, no clue why it's so weak rn
            Structural: 45 # its a damn laser sword, let people star wars their way through walls
  - type: ItemToggleDamageOtherOnHit
    activatedStaminaCost: 6
  - type: ItemToggleThrowingAngle
    activatedAngle: 225
    deleteOnDeactivate: true
  - type: Sprite
    sprite: Objects/Weapons/Melee/e_sword.rsi
    layers:
      - state: e_sword
      - state: e_sword_blade
        color: "#FFFFFF"
        visible: false
        shader: unshaded
        map: [ "blade" ]
  - type: MeleeWeapon
    wideAnimationRotation: -135
    attackRate: .8
    range: 1.6
    damage:
      types:
        Blunt: 2.5
    clickPartDamageMultiplier: 3
    heavyPartDamageMultiplier: 2.5
  - type: DamageOtherOnHit
  - type: Item
    size: Small
    sprite: Objects/Weapons/Melee/e_sword-inhands.rsi
  - type: UseDelay
    delay: 1.0
  - type: PointLight
    enabled: false
    radius: 2
    energy: 2
    color: white
    netsync: false
  - type: Appearance
  - type: ToggleableLightVisuals
    spriteLayer: blade
    inhandVisuals:
      left:
      - state: inhand-left-blade
        shader: unshaded
      right:
      - state: inhand-right-blade
        shader: unshaded
  - type: Reflect
  - type: IgnitionSource
    temperature: 700
  # Shitmed Change
  - type: Scalpel
    speed: 0.75
  - type: Cautery
    speed: 0.2
  - type: SurgeryTool
    startSound:
      path: /Audio/Weapons/ebladehum.ogg
    endSound:
      path: /Audio/Weapons/eblade1.ogg
      params:
        variation: 0.250

- type: entity
  name: "антикварний енергетичний меч"
  parent: EnergySword
  id: EnergySwordHoS
  description: "Елегантна зброя, гідна принца, на звичайному срібному руків'ї якої викарбувано: \"Моя любов\"."
  components:
    - type: EnergySword
      activatedColor: "#00CCFF"
      colorOptions:
       - "#00CCFF"
    - type: StealTarget
      stealGroup: HoSAntiqueWeapon

- type: entity
  name: "ручка"
  parent: EnergySword
  id: EnergyDagger
  suffix: E-Dagger
  description: "Ручка з темним чорнилом."
  components:
  - type: ItemToggle
    soundActivate:
      path: /Audio/Weapons/ebladeon.ogg
      params:
        volume: -6
    soundDeactivate:
      path: /Audio/Weapons/ebladeoff.ogg
      params:
        volume: -6
  - type: ItemToggleMeleeWeapon
    activatedSoundOnSwing:
      path: /Audio/Weapons/eblademiss.ogg
      params:
        volume: -6
        variation: 0.250
    activatedDamage:
        types:
            Slash: 4
            Heat: 8
    deactivatedSecret: true
  - type: ItemToggleActiveSound
    activeSound:
      path: /Audio/Weapons/ebladehum.ogg
      params:
        volume: -6
  - type: ComponentToggler
    components:
    - type: Sharp
    - type: DisarmMalus
      malus: 0.4
  - type: Sprite
    sprite: Objects/Weapons/Melee/e_dagger.rsi
    layers:
      - state: e_sword
      - state: e_sword_blade
        color: "#FFFFFF"
        visible: false
        shader: unshaded
        map: [ "blade" ]
  - type: MeleeWeapon
    wideAnimationRotation: -135
    attackRate: 1
    hidden: true
    damage:
      types:
        Blunt: 1
  - type: DamageOtherOnHit
    damage:
      types:
        Piercing: 3
    staminaCost: 3.5
  - type: EmbeddableProjectile
    offset: 0.3,0.0
    removalTime: 0.0
    embedOnThrow: true
  - type: EmbedPassiveDamage
    damage:
      types:
        Blunt: 0
  - type: Item
    size: Tiny
    sprite: Objects/Weapons/Melee/e_dagger.rsi
  - type: UseDelay
    delay: 1.0
  - type: PointLight
    enabled: false
    radius: 1.5
    energy: 1.5
    color: white
    netsync: false
  - type: Appearance
  - type: ToggleableLightVisuals
    spriteLayer: blade
    inhandVisuals:
      left:
      - state: inhand-left-blade
        shader: unshaded
      right:
      - state: inhand-right-blade
        shader: unshaded
  - type: Tag
    tags:
    - Write
    - NoPaint
  - type: DisarmMalus
    malus: 0

- type: entity
  parent: BaseItem
  id: EnergyDaggerBox
  name: "коробка для електронних кинджалів"
  suffix: E-Dagger
  description: "Невеличка коробочка з електронним кинджалом. Упаковка розпадається при відкритті, не залишаючи жодних доказів."
  components:
  - type: Item
    size: Tiny
  - type: Sprite
    sprite: Objects/Storage/penbox.rsi
    state: e_dagger
  - type: SpawnItemsOnUse
    items:
    - id: EnergyDagger
    sound:
      path: /Audio/Effects/unwrap.ogg

- type: entity
  name: "енергетичний меч"
  parent: EnergySword
  id: EnergyCutlass
  description: "Екзотична енергетична зброя, брутальне лезо, що потріскує грубо приборканою плазмою." #DeltaV - nicer description.
  components:
  - type: ItemToggleMeleeWeapon
    activatedDamage:
        types:
            Slash: 10
            Heat: 12
    deactivatedSecret: true
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Melee/e_cutlass.rsi # DeltaV
    layers:
      - state: e_cutlass
      - state: e_cutlass_blade
        color: "#e95151"
        visible: false
        shader: unshaded
        map: [ "blade" ]
  - type: MeleeWeapon # DeltaV - reduced attack rate of e-cutlass; slower, more brutal swings
    attackRate: 1.5
    soundHit:
      path: /Audio/Weapons/eblade1.ogg
    damage:
      types:
        Blunt: 6
  - type: Item
    size: Small
    sprite: DeltaV/Objects/Weapons/Melee/e_cutlass.rsi #DeltaV
  - type: ToggleableLightVisuals
    spriteLayer: blade
    inhandVisuals:
      left:
      - state: inhand-left-blade
        shader: unshaded
      right:
      - state: inhand-right-blade
        shader: unshaded

- type: entity
  name: "енергетичний меч"
  parent: EnergyCutlass
  id: EnergyCutlassSecurity
  description: "Витончена енергетична зброя, її лезо дзвенить летючою плазмою, майстерно сконцентрованою для того, щоб прорізати майже будь-який матеріал. Серійний номер на руків'ї вказує на те, що ця зброя належить офіцеру служби безпеки NT." # how do I make the text blue
  suffix: Security Loadouts
  components:
  - type: MeleeWeapon
    attackRate: 1.25
  - type: ItemToggleMeleeWeapon
    activatedDamage:
        types:
            Slash: 5
            Heat: 12
            Structural: 20 # "expertly contained to cut through nearly any material" yet it had no structural damage
  - type: GuideHelp
    guides: [ SecurityWeapons ]

- type: entity
  name: "двосторонній енергетичний меч"
  parent: EnergySword
  id: EnergySwordDouble
  description: "Стажер Командування Синдикату вважав, що мати лише одне лезо на енергетичному мечі - недостатньо круто. Його можна зберігати в кишенях."
  components:
  - type: ItemToggle
    onUse: false # wielding events control it instead
    soundActivate:
      path: /Audio/Weapons/ebladeon.ogg
      params:
        volume: 3
    soundDeactivate:
      path: /Audio/Weapons/ebladeoff.ogg
      params:
        volume: 3
  - type: ItemToggleMeleeWeapon
    activatedSoundOnSwing:
      path: /Audio/Weapons/eblademiss.ogg
      params:
        volume: 3
        variation: 0.250
    activatedDamage:
        types:
            Slash: 12
            Heat: 15
            Structural: 30
  - type: ItemToggleActiveSound
    activeSound:
      path: /Audio/Weapons/ebladehum.ogg
      params:
        volume: 3
  - type: ComponentToggler
    components:
    - type: Sharp
    - type: DisarmMalus
      malus: 0.7
  - type: Wieldable
    wieldSound: null # esword light sound instead
  - type: MeleeWeapon
    wideAnimationRotation: -135
    attackRate: .6666
    angle: 100
    damage:
      types:
        Blunt: 4.5
  - type: Sprite
    sprite: Objects/Weapons/Melee/e_sword_double.rsi
    layers:
      - state: e_sword_double
      - state: e_sword_double_blade
        color: "#FFFFFF"
        visible: false
        shader: unshaded
        map: [ "blade" ]
  - type: Item
    size: Small
    sprite: Objects/Weapons/Melee/e_sword_double-inhands.rsi
  - type: Reflect
    reflectProb: .80
    minReflectProb: .65
    spread: 75
    reflects:
      - Energy #DeltaV: 80% Energy Reflection but no ballistics.
  - type: UseDelay
    delay: 1
  - type: ToggleableLightVisuals
    spriteLayer: blade
    inhandVisuals:
      left:
      - state: inhand-left-blade
        shader: unshaded
      right:
      - state: inhand-right-blade
        shader: unshaded
