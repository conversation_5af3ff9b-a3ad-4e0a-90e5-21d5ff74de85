﻿- type: entity
  id: CloningPod
  parent: BaseMachinePowered
  name: "капсула для клонування"
  description: "Клонуюча капсула. Надійність 50%."
  components:
  - type: CloningPod
  - type: DeviceList
  - type: DeviceNetwork
    deviceNetId: Wired
    receiveFrequencyId: BasicDevice
  - type: DeviceLinkSink
    ports:
      - CloningPodReceiver
  - type: Sprite
    sprite: Structures/Machines/cloning.rsi
    snapCardinals: true
    layers:
      - state: pod_0
        map: ["base"]
  - type: Physics
    bodyType: Static
  - type: Construction
    graph: Machine
    node: machine
    containers:
     - machine_board
     - machine_parts
     - clonepod-bodyContainer
  - type: EmptyOnMachineDeconstruct
    containers:
      - clonepod-bodyContainer
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: StrongMetallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
      - !type:ChangeConstructionNodeBehavior
        node: machineFrame
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Machine
    board: CloningPodMachineCircuitboard
  - type: MaterialStorage
    materialWhiteList:
    - Biomass
  - type: WiresPanel
  - type: ApcPowerReceiver
    powerLoad: 200 #Receives most of its power from the console
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.CloningPodVisuals.Status:
        base:
          Cloning: { state: pod_1 }
          NoMind: { state: pod_e }
          Gore: { state: pod_g }
          Idle: { state: pod_0 }
  - type: Climbable
  - type: StaticPrice
    price: 1000
  - type: ContainerContainer
    containers:
      machine_board: !type:Container
      machine_parts: !type:Container
      clonepod-bodyContainer: !type:ContainerSlot
  - type: GuideHelp
    guides:
    - Cloning
