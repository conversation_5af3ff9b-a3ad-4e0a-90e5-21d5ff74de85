﻿borg-player-not-allowed = The brain doesn't fit!
borg-player-not-allowed-eject = The brain was expelled from the chassis!

borg-panel-not-open = The cyborg's panel isn't open...

borg-mind-added = {CAPITALIZE($name)} powered on!
borg-mind-removed = {CAPITALIZE($name)} shut off!

borg-module-too-many = There's not enough room for another module...
borg-module-whitelist-deny = This module doesn't fit in this type of cyborg...

borg-construction-guide-string = The cyborg limbs and torso must be attached to the endoskeleton.

borg-ui-menu-title = Cyborg Interface
borg-ui-charge-label = Charge: {$charge}%
borg-ui-no-brain = No brain present
borg-ui-remove-battery = Remove
borg-ui-modules-label = Modules:
borg-ui-module-counter = {$actual}/{$max}

# Transponder
borg-transponder-disabled-popup = A brain shoots out the top of {$name}!
borg-transponder-disabling-popup = Your transponder begins to lock you out of the chassis!
borg-transponder-destroying-popup = The self destruct of {$name} starts beeping!
borg-transponder-emagged-disabled-popup = Your transponder's lights go out!
borg-transponder-emagged-destroyed-popup = Your transponder's fuse blows!

## Borg type selection UI.
borg-select-type-menu-title = Select Chassis Type
borg-select-type-menu-bottom-text = Chassis selection is irreversible
borg-select-type-menu-available = Available types
borg-select-type-menu-information = Information
borg-select-type-menu-select-type = Select type to view information
borg-select-type-menu-confirm = Confirm selection
borg-select-type-menu-guidebook = Guidebook

## Borg type information

borg-type-generic-name = Generic
borg-type-generic-desc = Jack of all trades, master of none. Do various random station tasks, or maybe help out the science department that built you.
borg-type-generic-transponder = generic cyborg

borg-type-engineering-name = Engineering
borg-type-engineering-desc = Assist the engineering team in station construction, repairing damage, or fixing electrical and atmospheric issues.
borg-type-engineering-transponder = engineering cyborg

borg-type-mining-name = Salvage
borg-type-mining-desc = Join salvage and help them mine for materials, scavenge wrecks, and fight off hostile wildlife.
borg-type-mining-transponder = salvage cyborg

borg-type-janitor-name = Janitor
borg-type-janitor-desc = Keep the station nice and tidy, clean up spills, collect and properly dispose of trash left around by lazy crewmembers.
borg-type-janitor-transponder = janitor cyborg

borg-type-medical-name = Medical
borg-type-medical-desc = Provide medical attention to crew who need it, either in medbay or in hazardous areas conventional paramedics cannot reach.
borg-type-medical-transponder = medical cyborg

borg-type-service-name = Service
borg-type-service-desc = Help out with a wide range of crew services, ranging from serving snacks and drinks to botany to entertainment.
borg-type-service-transponder = service cyborg


