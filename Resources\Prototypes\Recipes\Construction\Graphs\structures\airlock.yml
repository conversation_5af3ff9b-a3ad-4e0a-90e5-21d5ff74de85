- type: constructionGraph
  id: Airlock
  start: start
  graph:
  - node: start
    edges:
    - to: assembly
      completed:
      - !type:SetAnchor
        value: false
      steps:
      - material: Steel
        amount: 4
        doAfter: 2

  - node: assembly
    entity: AirlockAssembly
    actions:
    - !type:SnapToGrid {}
    - !type:SetAnchor {}
    edges:
    - to: wired
      conditions:
      - !type:EntityAnchored {}
      steps:
      - material: Cable
        amount: 5
        doAfter: 2.5
    - to: start
      conditions:
      - !type:EntityAnchored
        anchored: false
      completed:
      - !type:SpawnPrototype
        prototype: SheetSteel1
        amount: 4
      - !type:DeleteEntity {}
      steps:
      - tool: Welding
        doAfter: 3

  - node: wired
    entity: AirlockAssembly
    edges:
    - to: electronics
      conditions:
      - !type:EntityAnchored {}
      steps:
      - component: DoorElectronics
        store: board
        name: "door electronics circuit board"
        icon:
          sprite: "Objects/Misc/module.rsi"
          state: "door_electronics"
        doAfter: 3
    - to: assembly
      completed:
      - !type:SpawnPrototype
        prototype: CableApcStack1
        amount: 5
      steps:
      - tool: Cutting
        doAfter: 2.5

  - node: electronics
    edges:
    - to: airlock
      conditions:
      - !type:EntityAnchored {}
      steps:
      - tool: Screwing
        doAfter: 2.5
    - to: glassElectronics
      conditions:
      - !type:EntityAnchored {}
      steps:
      - material: ReinforcedGlass
        amount: 1
        doAfter: 2
    - to: wired
      conditions:
      - !type:EntityAnchored {}
      completed:
      - !type:EmptyAllContainers
        pickup: true
        emptyAtUser: true
      steps:
      - tool: Prying
        doAfter: 5

  - node: glassElectronics
    entity: AirlockAssembly
    edges:
    - to: glassAirlock
      conditions:
      - !type:EntityAnchored {}
      steps:
      - material: ReinforcedGlass
        amount: 1
        doAfter: 2
      - tool: Screwing
        doAfter: 2.5
    - to: wired
      conditions:
      - !type:EntityAnchored {}
      completed:
      - !type:EmptyAllContainers
        pickup: true
        emptyAtUser: true
      - !type:SpawnPrototype
        prototype: SheetRGlass1
        amount: 1
      steps:
      - tool: Prying
        doAfter: 5

## Glass airlock
  - node: glassAirlock
    entity: AirlockGlass
    doNotReplaceInheritingEntities: true
    actions:
    - !type:SetWiresPanelSecurity
      wiresAccessible: true
    edges:
    - to: glassElectronics
      conditions:
      - !type:EntityAnchored {}
      - !type:DoorWelded {}
      - !type:DoorBolted
        value: false
      - !type:WirePanel {}
      - !type:AllWiresCut
      completed:
      - !type:SpawnPrototype
        prototype: SheetRGlass1
        amount: 1
      steps:
      - tool: Prying
        doAfter: 2

    - to: medSecurityUnfinished
      conditions:
      - !type:WirePanel {}
      steps:
      - material: Steel
        amount: 2
        doAfter: 2

    - to: highSecurityUnfinished
      conditions:
      - !type:WirePanel {}
      steps:
      - material: Plasteel
        amount: 2
        doAfter: 2

## Standard airlock
  - node: airlock
    entity: Airlock
    doNotReplaceInheritingEntities: true
    actions:
    - !type:SetWiresPanelSecurity
      wiresAccessible: true
    edges:
    - to: wired
      conditions:
      - !type:EntityAnchored {}
      - !type:DoorWelded {}
      - !type:DoorBolted
        value: false
      - !type:WirePanel {}
      - !type:AllWiresCut
      completed:
      - !type:EmptyAllContainers
        pickup: true
        emptyAtUser: true
      steps:
      - tool: Prying
        doAfter: 5

    - to: medSecurityUnfinished
      conditions:
      - !type:WirePanel {}
      steps:
      - material: Steel
        amount: 2
        doAfter: 2

    - to: highSecurityUnfinished
      conditions:
      - !type:WirePanel {}
      steps:
      - material: Plasteel
        amount: 2
        doAfter: 2

## High security door
  - node: highSecDoor
    actions:
    - !type:SetWiresPanelSecurity
      wiresAccessible: true
    edges:
    - to: medSecurityUnfinished
      conditions:
      - !type:WirePanel {}
      steps:
      - material: Steel
        amount: 2
        doAfter: 2

    - to: highSecurityUnfinished
      conditions:
      - !type:WirePanel {}
      steps:
      - material: Plasteel
        amount: 2
        doAfter: 2

## Medium security level door: a layer of steel plating protects the internal wiring
  - node: medSecurityUnfinished
    actions:
    - !type:SetWiresPanelSecurity
      examine: wires-panel-component-on-examine-security-level1
      wiresAccessible: false
    edges:
    - to: glassAirlock
      completed:
      - !type:GivePrototype
        prototype: SheetSteel1
        amount: 2
      conditions:
      - !type:WirePanel {}
      - !type:HasTag
        tag: GlassAirlock
      steps:
      - tool: Prying
        doAfter: 4

    - to: airlock
      completed:
      - !type:GivePrototype
        prototype: SheetSteel1
        amount: 2
      conditions:
      - !type:WirePanel {}
      - !type:HasTag
        tag: Airlock
      steps:
      - tool: Prying
        doAfter: 4

    - to: highSecDoor
      completed:
      - !type:GivePrototype
        prototype: SheetSteel1
        amount: 2
      conditions:
      - !type:WirePanel {}
      - !type:HasTag
        tag: HighSecDoor
      steps:
      - tool: Prying
        doAfter: 4

    - to: medSecurity
      conditions:
      - !type:WirePanel {}
      steps:
      - tool: Welding
        doAfter: 3

  - node: medSecurity
    actions:
    - !type:SetWiresPanelSecurity
      examine: wires-panel-component-on-examine-security-level2
      wiresAccessible: false
    edges:
    - to: medSecurityUnfinished
      conditions:
      - !type:WirePanel {}
      steps:
      - tool: Welding
        doAfter: 10

## High security level door: a layer of plasteel plating protects the internal wiring
  - node: highSecurityUnfinished
    actions:
    - !type:SetWiresPanelSecurity
      examine: wires-panel-component-on-examine-security-level3
      wiresAccessible: false
    edges:
    - to: glassAirlock
      completed:
      - !type:GivePrototype
        prototype: SheetPlasteel1
        amount: 2
      conditions:
      - !type:WirePanel {}
      - !type:HasTag
        tag: GlassAirlock
      steps:
      - tool: Prying
        doAfter: 4

    - to: airlock
      completed:
      - !type:GivePrototype
        prototype: SheetPlasteel1
        amount: 2
      conditions:
      - !type:WirePanel {}
      - !type:HasTag
        tag: Airlock
      steps:
      - tool: Prying
        doAfter: 4

    - to: highSecDoor
      completed:
      - !type:GivePrototype
        prototype: SheetPlasteel1
        amount: 2
      conditions:
      - !type:WirePanel {}
      - !type:HasTag
        tag: HighSecDoor
      steps:
      - tool: Prying
        doAfter: 4

    - to: highSecurity
      conditions:
      - !type:WirePanel {}
      steps:
      - tool: Welding
        doAfter: 5

  - node: highSecurity
    actions:
    - !type:SetWiresPanelSecurity
      examine: wires-panel-component-on-examine-security-level4
      wiresAccessible: false
    edges:
    - to: highSecurityUnfinished
      conditions:
      - !type:WirePanel {}
      steps:
      - tool: Welding
        doAfter: 15

    - to: maxSecurity
      conditions:
      - !type:WirePanel {}
      steps:
      - material: MetalRod
        amount: 2
        doAfter: 1

## Max security level door: an electric grill is added
  - node: maxSecurity
    actions:
    - !type:SetWiresPanelSecurity
      examine: wires-panel-component-on-examine-security-level5
      wiresAccessible: false
    edges:
    - to: highSecurity
      completed:
      - !type:AttemptElectrocute
      - !type:GivePrototype
        prototype: PartRodMetal1
        amount: 2
      conditions:
      - !type:WirePanel {}
      steps:
      - tool: Cutting
        doAfter: 0.5

    - to: superMaxSecurityUnfinished
      conditions:
      - !type:WirePanel {}
      steps:
      - material: Plasteel
        amount: 2
        doAfter: 2

## Super max security level door: an additional layer of plasteel is added
  - node: superMaxSecurityUnfinished
    actions:
    - !type:SetWiresPanelSecurity
      examine: wires-panel-component-on-examine-security-level6
      wiresAccessible: false
    edges:
    - to: maxSecurity
      completed:
      - !type:GivePrototype
        prototype: SheetPlasteel1
        amount: 2
      conditions:
      - !type:WirePanel {}
      steps:
      - tool: Prying
        doAfter: 4

    - to: superMaxSecurity
      conditions:
      - !type:WirePanel {}
      steps:
      - tool: Welding
        doAfter: 5

  - node: superMaxSecurity
    actions:
    - !type:SetWiresPanelSecurity
      examine: wires-panel-component-on-examine-security-level7
      wiresAccessible: false
    edges:
    - to: superMaxSecurityUnfinished
      conditions:
      - !type:WirePanel {}
      steps:
      - tool: Welding
        doAfter: 15

