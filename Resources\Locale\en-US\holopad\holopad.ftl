﻿# Window headers
holopad-window-title = {CAPITALIZE($title)}
holopad-window-subtitle = [color=white][bold]Holographic communication system[/bold][/color]
holopad-window-options = [color=darkgray][font size=10][italic]Please select an option from the list below[/italic][/font][/color]

# Call status
holopad-window-no-calls-in-progress = No holo-calls in progress
holopad-window-incoming-call = Incoming holo-call from:
holopad-window-relay-label = Originating at:
holopad-window-outgoing-call = Attempting to establish a connection...
holopad-window-call-in-progress = Holo-call in progress
holopad-window-call-ending = Disconnecting...
holopad-window-call-rejected = Unable to establish a connection
holopad-window-ai-request = Your presence is requested by:
holopad-window-emergency-broadcast-in-progress = [color=#cf2f2f][bold]Emergency broadcast in progress[/bold][/color]
holopad-window-controls-locked-out = Control of this device has been locked to:
holopad-window-controls-unlock-countdown = It will automatically unlock in: {$countdown}

# Buttons
holopad-window-answer-call = Answer call
holopad-window-end-call = End call
holopad-window-request-station-ai = Request station AI
holopad-window-activate-projector = Activate projector
holopad-window-emergency-broadcast = Emergency broadcast
holopad-window-emergency-broadcast-with-countdown = Emergency broadcast ({$countdown})
holopad-window-access-denied = Access denied

# Contact list
holopad-window-select-contact-from-list = Select a contact to initiate a holo-call
holopad-window-fetching-contacts-list = No holopads are currently contactable
holopad-window-contact-label = {CAPITALIZE($label)}
holopad-window-filter-line-placeholder = Search for a contact

# Flavor
holopad-window-flavor-left = ⚠ Do not enter while projector is active
holopad-window-flavor-right = v3.0.9

# Holograms
holopad-hologram-name = hologram of {THE($name)}

# Holopad actions
holopad-activate-projector-verb = Activate holopad projector
holopad-ai-is-unable-to-reach-holopad = You are unable to interface with the source of the call, it is too far from your core.

# Mapping prototypes
# General
holopad-general-tools = General - Tools
holopad-general-cryosleep = General - Cryosleep
holopad-general-theater = General - Theater
holopad-general-disposals = General - Disposals
holopad-general-eva = General - EVA Storage
holopad-general-lounge = General - Lounge
holopad-general-arcade = General - Arcade
holopad-general-evac = General - Evac
holopad-general-arrivals = General - Arrivals

# Command
holopad-command-bridge = Command - Bridge
holopad-command-vault = Command - Vault
holopad-command-bridge-hallway = Command - Bridge Hallway
holopad-command-meeting-room = Command - Meeting Room
holopad-command-lounge = Command - Lounge
holopad-command-captain = Command - Captain
holopad-command-hop = Command - HoP
holopad-command-cmo = Command - CMO
holopad-command-qm = Command - QM
holopad-command-ce = Command - CE
holopad-command-rd = Command - RD
holopad-command-hos = Command - HoS

# Science
holopad-science-anomaly = Science - Anomaly
holopad-science-artifact = Science - Artifact
holopad-science-robotics = Science - Robotics
holopad-science-rnd = Science - R&D
holopad-science-front = Science - Front
holopad-science-breakroom = Science - Breakroom

# Medical
holopad-medical-medbay = Medical - Medbay
holopad-medical-chemistry = Medical - Chemistry
holopad-medical-cryopods = Medical - Cryopods
holopad-medical-morgue = Medical - Morgue
holopad-medical-surgery = Medical - Surgery
holopad-medical-paramedic = Medical - Paramedic
holopad-medical-virology = Medical - Virology
holopad-medical-front = Medical - Front
holopad-medical-breakroom = Medical - Breakroom

# Cargo
holopad-cargo-front = Cargo - Front
holopad-cargo-bay = Cargo - Cargo Bay
holopad-cargo-salvage-bay = Cargo - Salvage Bay
holopad-cargo-breakroom  = Cargo - Breakroom
holopad-cargo-ats = Cargo - ATS
holopad-cargo-shuttle = Cargo - Shuttle

# Engineering
holopad-engineering-atmos-front = Atmos - Front
holopad-engineering-atmos-main = Atmos - Main
holopad-engineering-atmos-teg = Atmos - TEG
holopad-engineering-storage = Engineering - Storage
holopad-engineering-breakroom = Engineering - Breakroom
holopad-engineering-front = Engineering - Front
holopad-engineering-telecoms = Engineering - Telecoms
holopad-engineering-tech-vault = Engineering - Tech Vault
holopad-engineering-ame = Engineering - AME
holopad-engineering-power = Engineering - Power

# Security
holopad-security-front = Security - Front
holopad-security-brig = Security - Brig
holopad-security-warden = Security - Warden
holopad-security-interrogation = Security - Interrogation
holopad-security-breakroom = Security - Breakroom
holopad-security-detective = Security - Detective
holopad-security-perma = Security - Perma
holopad-security-courtroom = Security - Courtroom
holopad-security-lawyer = Security - Lawyer
holopad-security-armory = Security - Armory
holopad-security-locker-room = Security - Locker Room
holopad-security-brig-med = Security - Brig Med

# Service
holopad-service-janitor = Service - Janitor
holopad-service-bar = Service - Bar
holopad-service-kitchen = Service - Kitchen
holopad-service-botany = Service - Botany
holopad-service-chapel = Service - Chapel
holopad-service-library = Service - Library
holopad-service-newsroom = Service - Newsroom
holopad-service-zookeeper = Service - Zookeeper
holopad-service-boxer = Service - Boxer
holopad-service-clown = Service - Clown
holopad-service-musician = Service - Musician
holopad-service-mime = Service - Mime
holopad-service-clown-mime = Service - Clown/Mime
holopad-service-gameroom = Service - Game Room

# AI
holopad-ai-core = AI - Core
holopad-ai-main = AI - Main
holopad-ai-upload = AI - Upload
holopad-ai-backup-power = AI - Backup Power
holopad-ai-entrance = AI - Entrance
holopad-ai-chute = AI - Chute

# Long Range
holopad-station-bridge = Station - Bridge
holopad-station-cargo-bay = Station - Cargo Bay

# CentComm
holopad-centcomm-evac = CentComm - Evacuation Shuttle
