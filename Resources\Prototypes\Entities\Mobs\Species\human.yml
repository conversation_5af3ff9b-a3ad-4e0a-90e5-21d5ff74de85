# Anything human specific (e.g. UI, input) goes under MobHuman
- type: entity
  parent: BaseMobSpeciesOrganic
  id: BaseMobHuman
  name: "Уріст МакХендс"
  abstract: true
  components:
  - type: Hunger
  - type: Icon # It will not have an icon in the adminspawn menu without this. Body parts seem fine for whatever reason.
    sprite: Mobs/Species/Human/parts.rsi
    state: full
  - type: Thirst
  - type: Carriable # Carrying system from nyanotrasen.
  - type: Butcherable
    butcheringType: Spike
    spawned:
    - id: FoodMeatHuman
      amount: 5
  - type: HumanoidAppearance
    species: Human
    hideLayersOnEquip:
    - Hair
    - Snout
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    - SolCommon
    understands:
    - TauCetiBasic
    - SolCommon
  - type: FootPrints

- type: entity
  parent: BaseSpeciesDummy
  id: MobHumanDummy
  categories: [ HideSpawnMenu ]
