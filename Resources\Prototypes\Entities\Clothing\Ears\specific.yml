﻿- type: entity
  parent: ClothingHeadsetGrey
  id: ClothingHeadsetChameleon
  name: "пасажирська гарнітура"
  description: "Оновлений модульний інтерком, який одягається на голову. Приймає ключі шифрування."
  suffix: Chameleon
  components:
    - type: Tag
      tags: [] # ignore "WhitelistChameleon" tag
    - type: Sprite
      sprite: Clothing/Ears/Headsets/base.rsi
    - type: Clothing
      sprite: Clothing/Ears/Headsets/base.rsi
    - type: ChameleonClothing
      slot: [ears]
      default: ClothingHeadsetGrey
    - type: UserInterface
      interfaces:
        enum.ChameleonUiKey.Key:
          type: ChameleonBoundUserInterface
