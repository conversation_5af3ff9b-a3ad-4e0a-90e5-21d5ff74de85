- type: entity
  parent: BaseItem
  id: ClusterBang
  name: "кластерний вибух"
  description: "Може використовуватися тільки з сигнальними ракетами. Вибухає кілька разів."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/clusterbang.rsi
    state: base-0
  - type: Appearance
  - type: ClusterGrenadeVisuals
    state: base
  - type: ClusterGrenade
  - type: OnUseTimerTrigger
    delay: 3.5
  - type: ContainerContainer
    containers:
      cluster-payload: !type:Container

- type: entity
  parent: GrenadeBase
  id: ClusterBangFull
  name: "ClusterBang"
  description: "Запускає три світлошумові гранати після закінчення таймера."
  suffix: Full
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/clusterbang.rsi
    layers:
    - state: icon
      map: ["enum.TriggerVisualLayers.Base"]
  - type: ClusterGrenade
    fillPrototype: GrenadeFlashBang
    distance: 7
    velocity: 7
  - type: TimerTriggerVisuals
    primingSound:
      path: /Audio/Effects/countdown.ogg
  - type: GenericVisualizer
    visuals:
      enum.Trigger.TriggerVisuals.VisualState:
        enum.ConstructionVisuals.Layer:
          Primed: { state: primed }
          Unprimed: { state: icon }
  - type: EmitSoundOnTrigger
    sound:
      path: "/Audio/Machines/door_lock_off.ogg"
  - type: ContainerContainer
    containers:
      cluster-payload: !type:Container

- type: entity
  parent: GrenadeBase
  id: ClusterGrenade
  name: "кластерна граната"
  description: "Навіщо використовувати одну гранату, коли можна використати одразу три!"
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/clusterbomb.rsi
    layers:
    - state: icon
      map: ["enum.TriggerVisualLayers.Base"]
  - type: ClusterGrenade
    fillPrototype: ExGrenade
    velocity: 3.5
    distance: 5
  - type: OnUseTimerTrigger
    beepSound:
      path: "/Audio/Effects/beep1.ogg"
      params:
        volume: 5
    initialBeepDelay: 0
    beepInterval: 0.5
  - type: EmitSoundOnTrigger
    sound:
      path: "/Audio/Machines/door_lock_off.ogg"
  - type: ContainerContainer
    containers:
      cluster-payload: !type:Container

- type: entity
  parent: BaseItem
  id: ClusterBananaPeel
  name: "кластерна бананова шкірка"
  description: "Після кидка розпадається на 6 вибухових бананових шкірок, веселощі гарантовані!"
  components:
  - type: Sprite
    sprite: Objects/Specific/Hydroponics/banana.rsi
    state: produce
  - type: Appearance
  - type: ClusterGrenade
    fillPrototype: TrashBananaPeelExplosive
    maxGrenadesCount: 6
    baseTriggerDelay: 20
  - type: DamageOnLand
    damage:
      types:
        Blunt: 10
  - type: LandAtCursor
  - type: Damageable
    damageContainer: Inorganic
  - type: EmitSoundOnTrigger
    sound:
      path: "/Audio/Items/bikehorn.ogg"
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 10
      behaviors:
      - !type:TriggerBehavior
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: ContainerContainer
    containers:
      cluster-payload: !type:Container

- type: entity
  parent: GrenadeBase
  id: GrenadeStinger
  name: "світлошумова граната"
  description: "Тут немає на що дивитися, будь ласка, розійдіться."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/stingergrenade.rsi
    layers:
    - state: icon
      map: ["enum.TriggerVisualLayers.Base"]
  - type: ClusterGrenade
    fillPrototype: PelletClusterRubber
    maxGrenadesCount: 30
    grenadeType: enum.GrenadeType.Shoot
  - type: FlashOnTrigger
    range: 7
  - type: EmitSoundOnTrigger
    sound:
      path: "/Audio/Effects/flash_bang.ogg"
  - type: SpawnOnTrigger
    proto: GrenadeFlashEffect
  - type: TimerTriggerVisuals
    primingSound:
      path: /Audio/Effects/countdown.ogg
  - type: ContainerContainer
    containers:
      cluster-payload: !type:Container

- type: entity
  parent: GrenadeBase
  id: GrenadeIncendiary
  name: "запальна граната"
  description: "Гарантовано підніме настрій."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/pyrogrenade.rsi
    layers:
    - state: icon
      map: ["enum.TriggerVisualLayers.Base"]
  - type: ClusterGrenade
    fillPrototype: PelletClusterIncendiary
    maxGrenadesCount: 30
    grenadeType: enum.GrenadeType.Shoot
  - type: OnUseTimerTrigger
    beepSound:
      path: "/Audio/Effects/beep1.ogg"
      params:
        volume: 5
    initialBeepDelay: 0
    beepInterval: 2
  - type: EmitSoundOnTrigger
    sound:
      path: "/Audio/Weapons/Guns/Gunshots/batrifle.ogg"
  - type: ContainerContainer
    containers:
      cluster-payload: !type:Container

- type: entity
  parent: GrenadeBase
  id: GrenadeShrapnel
  name: "осколкова граната"
  description: "Випускає смертоносний аерозоль осколків, що спричиняє сильну кровотечу."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/shrapnelgrenade.rsi
    layers:
    - state: icon
      map: ["enum.TriggerVisualLayers.Base"]
  - type: ClusterGrenade
    fillPrototype: PelletClusterLethal
    maxGrenadesCount: 30
    grenadeType: enum.GrenadeType.Shoot
  - type: OnUseTimerTrigger
    beepSound:
      path: "/Audio/Effects/beep1.ogg"
      params:
        volume: 5
    initialBeepDelay: 0
    beepInterval: 2
  - type: EmitSoundOnTrigger
    sound:
      path: "/Audio/Weapons/Guns/Gunshots/batrifle.ogg"
  - type: ContainerContainer
    containers:
      cluster-payload: !type:Container

- type: entity
  parent: SoapSyndie
  id: SlipocalypseClusterSoap
  name: "slipocalypse clustersoap"
  description: "Приземлившись на підлогу, розкидає невеликі шматочки синдикативного мила по площі."
  components:
  - type: Sprite
    sprite: Objects/Specific/Janitorial/soap.rsi
    layers:
    - state: syndie-4
  - type: Appearance
  - type: ClusterGrenade
    fillPrototype: SoapletSyndie
    maxGrenadesCount: 30
    grenadeTriggerIntervalMax: 0
    grenadeTriggerIntervalMin: 0
    baseTriggerDelay: 60
    randomSpread: true
    velocity: 3
  - type: DamageOnLand
    damage:
      types:
        Blunt: 10
  - type: LandAtCursor
  - type: EmitSoundOnTrigger
    sound:
      path: "/Audio/Effects/flash_bang.ogg"
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 10
      behaviors:
      - !type:TriggerBehavior
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: ContainerContainer
    containers:
      cluster-payload: !type:Container

- type: entity
  parent: GrenadeShrapnel
  id: GrenadeFoamDart
  name: "пінопластова світлошумова граната"
  description: "Випускає неприємний аерозоль з пінопластових дротиків, які викликають сильне роз'їдання."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Grenades/foamdart.rsi
    layers:
    - state: icon
      map: ["Base"]
    - state: primed
      map: ["enum.TriggerVisualLayers.Base"]
  - type: ClusterGrenade
    fillPrototype: BulletFoam
    maxGrenadesCount: 30
    grenadeType: enum.GrenadeType.Throw
    velocity: 70
