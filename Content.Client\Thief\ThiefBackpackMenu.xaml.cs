using Content.Client.UserInterface.Controls;
using Content.Shared.Thief;
using Robust.Client.AutoGenerated;
using Robust.Client.GameObjects;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Thief;

[GenerateTypedNameReferences]
public sealed partial class ThiefBackpackMenu : FancyWindow
{
    [Dependency] private readonly IEntitySystemManager _sysMan = default!;
    private readonly SpriteSystem _spriteSystem;

    public event Action? OnApprove;
    public event Action<int>? OnSetChange;

    public ThiefBackpackMenu()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
        _spriteSystem = _sysMan.GetEntitySystem<SpriteSystem>();

        ApproveButton.OnPressed += args =>
        {
            OnApprove?.Invoke();
        };
    }

    public void UpdateState(ThiefBackpackBoundUserInterfaceState state)
    {
        SetsGrid.DisposeAllChildren();
        var selectedNumber = 0;
        foreach (var (set, info) in state.Sets)
        {
            var child = new ThiefBackpackSet(info, _spriteSystem);

            child.SetButton.OnButtonDown += (args) =>
            {
                OnSetChange?.Invoke(set);
            };

            SetsGrid.AddChild(child);

            if (info.Selected)
                selectedNumber++;
        }

        SelectedSets.Text = Loc.GetString("thief-backpack-window-selected", ("selectedCount", selectedNumber), ("maxCount", state.MaxSelectedSets));
        ApproveButton.Disabled = selectedNumber != state.MaxSelectedSets;
    }
}
