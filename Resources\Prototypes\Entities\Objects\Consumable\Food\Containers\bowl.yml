# Bowls

- type: entity
  name: "чаша"
  parent: ReagentContainerBase
  id: FoodBowlBig
  description: "Проста миска, яку використовують для супів і салатів."
  components:
  - type: Item
    storedRotation: -90
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50 # enough to make cheese in.
  - type: Sprite
    sprite: Objects/Consumable/Food/bowl.rsi
    layers:
    - state: bowl
    - map: ["enum.SolutionContainerLayers.Fill"]
      state: fill-1
      visible: false
  - type: MixableSolution
    solution: food
  - type: DamageOnLand
    damage:
      types:
        Blunt: 5
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 5
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 5
    fillBaseName: fill-
  - type: SolutionTransfer
    canChangeTransferAmount: true
  - type: UserInterface
    interfaces:
      enum.TransferAmountUiKey.Key:
        type: TransferAmountBoundUserInterface
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 5
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:SpillBehavior { }
      - !type:SpawnEntitiesBehavior
        spawn:
          FoodBowlBigTrash:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Tag
    tags:
    - Trash
    - FoodBowlBig

- type: entity
  name: "розбита чаша"
  parent: BaseItem
  id: FoodBowlBigTrash
  description: "Проста миска, розбита і марна."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/bowl.rsi
    state: bowl-trash
  - type: Tag
    tags:
    - Trash
  - type: PhysicalComposition
    materialComposition:
      Glass: 50
  - type: SpaceGarbage
  - type: StaticPrice
    price: 0

- type: entity
  name: "чаша"
  parent: FoodBowlBig
  id: FoodBowlFancy
  description: "Вишукана миска, яку використовують для СПЕЦІАЛЬНИХ супів і салатів."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/bowl.rsi
    layers:
    - state: bowl2
    - map: ["enum.SolutionContainerLayers.Fill"]
      state: fill-1
      visible: false
