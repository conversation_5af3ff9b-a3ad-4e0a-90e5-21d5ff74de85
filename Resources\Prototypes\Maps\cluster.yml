- type: gameMap
  id: Cluster
  mapName: Кластер
  mapPath: /Maps/cluster.yml
  minPlayers: 0
  stations:
    Cluster:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/emergency_transit.yml
        - type: StationNameSetup
          mapNameTemplate: '{0} Cluster Station {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: '14'
        - type: StationJobs
          overflowJobs:
            - Passenger
          availableJobs:
            #service
            Captain: [ 1, 1 ]
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 1, 1 ]
            Botanist: [ 2, 2 ]
            Chef: [ 1, 1 ]
            Janitor: [ 1, 1 ]
            Chaplain: [ 1, 1 ]
            Librarian: [ 1, 1 ]
            Lawyer: [ 1, 1 ]
            ServiceWorker: [ 1, 1 ]
            #engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 2, 2 ]
            StationEngineer: [ 3, 3 ]
            TechnicalAssistant: [ 2, 2 ]
            #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            Chemist: [ 1, 1 ]
            MedicalDoctor: [ 2, 2 ]
            MedicalIntern: [ 2, 2 ]
            #science
            ResearchDirector: [ 1, 1 ]
            Scientist: [ 3, 3 ]
            ResearchAssistant: [ 2, 2 ]
            ForensicMantis: [ 1, 1 ]
            Borg: [ 1, 1 ]
            Roboticist: [ 2, 2 ]
            #security
            HeadOfSecurity: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1 ]
            Warden: [ 1, 1 ]
            Detective: [ 1, 1 ]
            SecurityOfficer: [ 3, 3 ]
            SecurityCadet: [ 2, 2 ]
            #supply
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ 2, 2 ]
            CargoTechnician: [ 1, 1 ]
            #civillian
            Passenger: [ -1, -1 ]
            Clown: [ 1, 1 ]
            Mime: [ 1, 1 ]
            Musician: [ 1, 1 ]
            #silicon
            StationAi: [ 1, 1 ]

        # Goobstation blob-config-start SMALL+
        - type: StationBlobConfig
          stageBegin: 25
          stageCritical: 350
          stageTheEnd: 700
        # Goobstation blob-config-end
