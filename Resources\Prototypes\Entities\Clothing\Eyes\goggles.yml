# Night Vision Goggles

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesNightVisionGoggles
  name: "окуляри нічного бачення"
  description: "Удосконалений дисплей, який відображає ідентифікаційні дані та бачення в повній темряві."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Goggles/nightvision.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Goggles/nightvision.rsi
  - type: NightVision
    isEquipment: true
  - type: IdentityBlocker
    coverage: EYES

- type: entity
  parent: [ClothingEyesNightVisionGoggles, ShowSecurityIcons]
  id: ClothingEyesNightVisionSecurityGoggles
  name: "окуляри нічного бачення"
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Goggles/security_nightvision.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Goggles/security_nightvision.rsi

- type: entity
  parent: [ClothingEyesNightVisionGoggles, ClothingEyesHudMedical]
  id: ClothingEyesNightVisionMedicalGoggles
  name: "медичні окуляри нічного бачення"
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Goggles/medical_nightvision.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Goggles/medical_nightvision.rsi

- type: entity
  parent: [ClothingEyesNightVisionGoggles, ClothingEyesHudDiagnostic]
  id: ClothingEyesNightVisionDiagnosticGoggles
  name: "діагностичні окуляри нічного бачення"
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Goggles/diagnostic_nightvision.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Goggles/diagnostic_nightvision.rsi

- type: entity
  parent: ClothingEyesNightVisionGoggles
  id: ClothingEyesNightVisionGogglesSyndie
  suffix: "Chameleon"
  components:
  - type: ChameleonClothing
    slot: [eyes]
    default: ClothingEyesNightVisionGoggles
  - type: UserInterface
    interfaces:
      enum.ChameleonUiKey.Key:
        type: ChameleonBoundUserInterface

- type: entity
  parent: [ClothingEyesNightVisionGogglesSyndie, ShowSecurityIcons]
  id: ClothingEyesNightVisionGogglesNukie
  suffix: "Chameleon, NukeOps"
  components:
  - type: ShowSyndicateIcons

# Thermal Vision Goggles

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesThermalVisionGoggles
  name: "тепловізійні окуляри"
  description: "Термоси у формі келихів."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Goggles/thermal.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Goggles/thermal.rsi
  - type: ThermalVision
    isEquipment: true
    pulseTime: 2
    toggleAction: PulseThermalVision
  - type: IdentityBlocker
    coverage: EYES

- type: entity
  parent: ClothingEyesThermalVisionGoggles
  id: ClothingEyesThermalVisionMonocle
  name: "термонокль"
  description: "Ніколи ще бачення крізь стіни не було таким ніжним."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Goggles/monocle_thermal.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Goggles/monocle_thermal.rsi

- type: entity
  parent: ClothingEyesThermalVisionGoggles
  id: ClothingEyesThermalVisionGogglesSyndie
  suffix: "Chameleon"
  components:
  - type: ChameleonClothing
    slot: [eyes]
    default: ClothingEyesThermalVisionGoggles
  - type: UserInterface
    interfaces:
      enum.ChameleonUiKey.Key:
        type: ChameleonBoundUserInterface

- type: entity
  parent: [ClothingEyesThermalVisionGogglesSyndie, ShowSecurityIcons]
  id: ClothingEyesThermalVisionGogglesNukie
  suffix: "Chameleon, NukeOps"
  components:
  - type: ShowSyndicateIcons
