- type: entity
  id: MobObserverTelegnostic
  name: "телегностична проекція"
  description: "Зловісно. Спрайт-заповнювач."
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    overrideContainerOcclusion: true # Ghosts always show up regardless of where they're contained.
    noRot: true
    drawdepth: Ghosts
    sprite: Objects/Consumable/Food/bowl.rsi
    state: eyeball
    color: "#90EE90"
    layers:
      - state: eyeball
        shader: unshaded
  - type: Psionic
    removable: false
    roller: false
  - type: MindContainer
  - type: Clickable
  - type: InteractionOutline
  - type: Physics
    bodyType: KinematicController
    fixedRotation: true
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 13
        mask:
        - GhostImpassable
  - type: MovementSpeedModifier
    baseSprintSpeed: 8
    baseWalkSpeed: 5
  - type: MovementIgnoreGravity
  #- type: PsionicallyInvisible
  - type: InputMover
  - type: Appearance
  - type: Eye
    drawFov: false
    visMask:
      - Normal
      - TelegnosticProjection
      - PsionicInvisibility
  - type: Input
    context: "ghost"
  - type: Examiner
  - type: TelegnosticProjection
  - type: Stealth
    lastVisibility: 0.66
  - type: Visibility
    layer: 4
  - type: NoNormalInteraction
