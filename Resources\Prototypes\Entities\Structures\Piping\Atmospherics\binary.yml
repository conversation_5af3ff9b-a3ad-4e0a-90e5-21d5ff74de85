﻿- type: entity
  parent: GasPipeBase
  abstract: true
  id: GasBinaryBase
  placement:
    mode: SnapgridCenter
  components:
  - type: AtmosDevice
  - type: Tag
    tags:
      - Unstackable
  - type: SubFloorHide
    blockInteractions: false
    blockAmbience: false
  - type: NodeContainer
    nodes:
      inlet:
        !type:PipeNode
        nodeGroupID: Pipe
        pipeDirection: North
      outlet:
        !type:PipeNode
        nodeGroupID: Pipe
        pipeDirection: South

- type: entity
  parent: GasBinaryBase
  id: GasPressurePump
  name: "газовий насос"
  description: "Насос, який переміщує газ під дією тиску."
  placement:
    mode: SnapgridCenter
  components:
  - type: Sprite
    sprite: Structures/Piping/Atmospherics/pump.rsi
    layers:
    - sprite: Structures/Piping/Atmospherics/pipe.rsi
      state: pipeStraight
      map: [ "enum.PipeVisualLayers.Pipe" ]
    - state: pumpPressure
      map: [ "enum.SubfloorLayers.FirstLayer", "enabled" ]
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.PumpVisuals.Enabled:
        enabled:
          True: { state: pumpPressureOn }
          False: { state: pumpPressure }
  - type: PipeColorVisuals
  - type: GasPressurePump
    enabled: false
  - type: UserInterface
    interfaces:
        enum.GasPressurePumpUiKey.Key:
          type: GasPressurePumpBoundUserInterface
  - type: Construction
    graph: GasBinary
    node: pressurepump
  - type: AmbientSound
    enabled: false
    volume: -9
    range: 5
    sound:
      path: /Audio/Ambience/Objects/gas_pump.ogg
  - type: AtmosMonitoringConsoleDevice
    navMapBlip: GasFlowRegulator
  - type: StationAiWhitelist
  - type: VentCrawlerTube # Goobstation - ventcrawl
    containerId: VentCrawlerTube
  - type: VentCrawlerTransit # Goobstation - ventcrawl
  - type: ContainerContainer # Goobstation - ventcrawl
    containers:
      VentCrawlerTube: !type:Container

- type: entity
  parent: GasBinaryBase
  id: GasVolumePump
  name: "об'ємний газовий насос"
  description: "Насос, який переміщує газ за об'ємом."
  placement:
    mode: SnapgridCenter
  components:
    - type: Sprite
      sprite: Structures/Piping/Atmospherics/pump.rsi
      layers:
        - sprite: Structures/Piping/Atmospherics/pipe.rsi
          state: pipeStraight
          map: [ "enum.PipeVisualLayers.Pipe" ]
        - state: pumpVolume
          map: [ "enum.SubfloorLayers.FirstLayer", "enabled" ]
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.GasVolumePumpVisuals.State:
          enabled:
            On: { state: pumpVolumeOn }
            Off: { state: pumpVolume }
            Blocked: { state: pumpVolumeBlocked }
    - type: PipeColorVisuals
    - type: GasVolumePump
      enabled: false
    - type: UserInterface
      interfaces:
        enum.GasVolumePumpUiKey.Key:
          type: GasVolumePumpBoundUserInterface
    - type: Construction
      graph: GasBinary
      node: volumepump
    - type: AmbientSound
      enabled: false
      volume: -9
      range: 5
      sound:
        path: /Audio/Ambience/Objects/gas_pump.ogg
    - type: DeviceNetwork
      deviceNetId: AtmosDevices
      receiveFrequencyId: AtmosMonitor
      transmitFrequencyId: AtmosMonitor
      sendBroadcastAttemptEvent: true
      examinableAddress: true
      prefix: device-address-prefix-volume-pump
    - type: WiredNetworkConnection
    - type: AtmosMonitoringConsoleDevice
      navMapBlip: GasFlowRegulator
    - type: VentCrawlerTube # Goobstation - ventcrawl
      containerId: VentCrawlerTube
    - type: VentCrawlerTransit # Goobstation - ventcrawl
    - type: ContainerContainer # Goobstation - ventcrawl
      containers:
        VentCrawlerTube: !type:Container

- type: entity
  parent: GasBinaryBase
  id: GasPassiveGate
  name: "пасивні ворота"
  description: "Односторонній повітряний клапан, який не потребує живлення."
  placement:
    mode: SnapgridCenter
  components:
    - type: Sprite
      sprite: Structures/Piping/Atmospherics/pump.rsi
      layers:
        - sprite: Structures/Piping/Atmospherics/pipe.rsi
          state: pipeStraight
          map: [ "enum.PipeVisualLayers.Pipe" ]
        - state: pumpPassiveGate
          map: [ "enum.SubfloorLayers.FirstLayer" ]
    - type: Appearance
    - type: PipeColorVisuals
    - type: GasPassiveGate
    - type: Construction
      graph: GasBinary
      node: passivegate
    - type: AmbientSound
      enabled: true
      volume: -9
      range: 5
      sound:
        path: /Audio/Ambience/Objects/gas_hiss.ogg
    - type: AtmosMonitoringConsoleDevice
      navMapBlip: GasValve
    - type: VentCrawlerTube # Goobstation - ventcrawl
      containerId: VentCrawlerTube
    - type: VentCrawlerTransit # Goobstation - ventcrawl
    - type: ContainerContainer # Goobstation - ventcrawl
      containers:
        VentCrawlerTube: !type:Container

- type: entity
  parent: GasBinaryBase
  id: GasValve
  name: "ручний клапан"
  description: "Труба з клапаном, за допомогою якого можна перекрити потік газу через неї."
  placement:
    mode: SnapgridCenter
  components:
    # TODO ATMOS: Give unique sprite.
    - type: Sprite
      sprite: Structures/Piping/Atmospherics/pump.rsi
      layers:
        - sprite: Structures/Piping/Atmospherics/pipe.rsi
          state: pipeStraight
          map: [ "enum.PipeVisualLayers.Pipe" ]
        - state: pumpManualValve
          map: [ "enum.SubfloorLayers.FirstLayer", "enabled" ]
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.FilterVisuals.Enabled:
          enabled:
            True: { state: pumpManualValveOn }
            False: { state: pumpManualValve }
    - type: PipeColorVisuals
    - type: GasValve
    - type: NodeContainer
      nodes:
        inlet:
          !type:PipeNode
          nodeGroupID: Pipe
          pipeDirection: North
          volume: 100
        outlet:
          !type:PipeNode
          nodeGroupID: Pipe
          pipeDirection: South
          volume: 100
    - type: Construction
      graph: GasBinary
      node: valve
    - type: AmbientSound
      enabled: true
      volume: -9
      range: 5
      sound:
        path: /Audio/Ambience/Objects/gas_hiss.ogg
    - type: AtmosMonitoringConsoleDevice
      navMapBlip: GasValve
    - type: VentCrawlerTube # Goobstation - ventcrawl
      containerId: VentCrawlerTube
    - type: VentCrawlerTransit # Goobstation - ventcrawl
    - type: ContainerContainer # Goobstation - ventcrawl
      containers:
        VentCrawlerTube: !type:Container

- type: entity
  parent: GasBinaryBase
  id: SignalControlledValve
  name: "сигнальний клапан"
  description: "Клапан керується за допомогою сигнальних входів."
  placement:
    mode: SnapgridCenter
  components:
  - type: StationAiWhitelist
  - type: Sprite
    sprite: Structures/Piping/Atmospherics/pump.rsi
    layers:
    - sprite: Structures/Piping/Atmospherics/pipe.rsi
      state: pipeStraight
      map: [ "enum.PipeVisualLayers.Pipe" ]
    - state: pumpSignalValve
      map: [ "enum.SubfloorLayers.FirstLayer", "enabled" ]
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.FilterVisuals.Enabled:
        enabled:
          True: { state: pumpSignalValveOn }
          False: { state: pumpSignalValve }
  - type: PipeColorVisuals
  - type: GasValve
  - type: SignalControlledValve
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: BasicDevice
  - type: WirelessNetworkConnection
    range: 200
  - type: DeviceLinkSink
    ports:
      - Open
      - Close
      - Toggle
  - type: NodeContainer
    nodes:
      inlet:
        !type:PipeNode
        nodeGroupID: Pipe
        pipeDirection: North
        volume: 100
      outlet:
        !type:PipeNode
        nodeGroupID: Pipe
        pipeDirection: South
        volume: 100
  - type: Construction
    graph: GasBinary
    node: signalvalve
  - type: AmbientSound
    enabled: true
    volume: -9
    range: 5
    sound:
      path: /Audio/Ambience/Objects/gas_hiss.ogg
  - type: AtmosMonitoringConsoleDevice
    navMapBlip: GasValve
  - type: VentCrawlerTube # Goobstation - ventcrawl
    containerId: VentCrawlerTube
  - type: VentCrawlerTransit # Goobstation - ventcrawl
  - type: ContainerContainer # Goobstation - ventcrawl
    containers:
      VentCrawlerTube: !type:Container

- type: entity
  parent: GasBinaryBase
  id: GasPort
  name: "порт роз'єму"
  description: "Для підключення портативних пристроїв, пов'язаних з контролем атмосфери."
  placement:
    mode: SnapgridCenter
  components:
    - type: Sprite
      sprite: Structures/Piping/Atmospherics/gascanisterport.rsi
      layers:
        - sprite: Structures/Piping/Atmospherics/pipe.rsi
          state: pipeHalf
          map: [ "enum.PipeVisualLayers.Pipe" ]
        - state: gasCanisterPort
          map: [ "enum.SubfloorLayers.FirstLayer" ]
    - type: Appearance
    - type: PipeColorVisuals
    - type: GasPort
    - type: NodeContainer
      nodes:
        connected:
          !type:PortPipeNode
          nodeGroupID: Pipe
          pipeDirection: South
    - type: Construction
      graph: GasBinary
      node: port
    - type: AtmosMonitoringConsoleDevice
      navMapBlip: GasVentOpening
    - type: VentCrawlerTube # Goobstation - ventcrawl
      containerId: VentCrawlerTube
    - type: VentCrawlerJunction # Goobstation - ventcrawl
      degrees:
        - 0
    - type: ContainerContainer # Goobstation - ventcrawl
      containers:
        VentCrawlerTube: !type:Container

- type: entity
  parent: GasVentPump
  id: GasDualPortVentPump
  name: "двопортовий вентиляційний отвір"
  description: "Має клапан і прикріплений до нього насос. Має два порти, один - вхідний для випуску повітря, інший - вихідний при сифонізації."
  placement:
    mode: SnapgridCenter
  components:
    - type: Sprite
      drawdepth: FloorObjects
      sprite: Structures/Piping/Atmospherics/vent.rsi
      layers:
        - sprite: Structures/Piping/Atmospherics/pipe.rsi
          state: pipeStraight
          map: [ "enum.PipeVisualLayers.Pipe" ]
        - state: vent_off
          map: [ "enabled", "enum.SubfloorLayers.FirstLayer" ]
    - type: GenericVisualizer
      visuals:
        enum.VentPumpVisuals.State:
          enabled:
            Off: { state: vent_off }
            In: { state: vent_in }
            Out: { state: vent_out }
            Welded: { state: vent_welded }
    - type: PipeColorVisuals
    - type: GasVentPump
      inlet: inlet
      outlet: outlet
      canLink: true
    - type: DeviceNetwork
      deviceNetId: Wireless
      receiveFrequencyId: BasicDevice
    - type: WirelessNetworkConnection
      range: 200
    - type: DeviceLinkSink
      ports:
        - Pressurize
        - Depressurize
    - type: Construction
      graph: GasBinary
      node: dualportventpump
    - type: NodeContainer
      nodes:
        inlet:
          !type:PipeNode
          nodeGroupID: Pipe
          pipeDirection: North
        outlet:
          !type:PipeNode
          nodeGroupID: Pipe
          pipeDirection: South
    - type: AmbientSound
      enabled: true
    - type: AtmosMonitoringConsoleDevice
      navMapBlip: GasVentOpening

- type: entity
  parent: [ BaseMachine, ConstructibleMachine ]
  id: GasRecycler
  name: "рециркулятор газу"
  description: "Переробляє вуглекислий газ та закис азоту. Нагрівач і компресор не входять до комплекту."
  placement:
    mode: SnapgridCenter
  components:
  - type: Sprite
    sprite: Structures/Machines/gasrecycler.rsi
    layers:
    - sprite: Structures/Piping/Atmospherics/pipe.rsi
      state: pipeStraight
      map: [ "enum.PipeVisualLayers.Pipe" ]
    - state: running
    - state: unlit
      shader: unshaded
  - type: GenericVisualizer
    visuals:
      enum.PumpVisuals.Enabled:
        enabled:
          True: { state: running }
          False: { state: unlit }
  - type: Appearance
  - type: PipeColorVisuals
  - type: Rotatable
  - type: GasRecycler
  # - type: PipeRestrictOverlap # Allow pipe stacking for MRP
  - type: NodeContainer
    nodes:
      inlet:
        !type:PipeNode
        nodeGroupID: Pipe
        pipeDirection: North
      outlet:
        !type:PipeNode
        nodeGroupID: Pipe
        pipeDirection: South
  - type: AtmosDevice
  - type: AtmosPipeColor
  - type: AmbientSound
    enabled: true
    volume: -9
    range: 5
    sound:
      path: /Audio/Ambience/Objects/gas_pump.ogg
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
      - !type:ChangeConstructionNodeBehavior
        node: machineFrame
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Machine
    board: GasRecyclerMachineCircuitboard
  - type: AtmosMonitoringConsoleDevice
    navMapBlip: GasValve

- type: entity
  parent: GasBinaryBase
  id: HeatExchanger
  name: "радіатор"
  description: "Передає тепло між трубою та її оточенням."
  placement:
    mode: SnapgridCenter
  components:
  - type: Rotatable
  - type: Transform
    noRot: false
  - type: Sprite
    sprite: Structures/Piping/Atmospherics/heatexchanger.rsi
    layers:
    - sprite: Structures/Piping/Atmospherics/pipe.rsi
      state: pipeStraight
      map: [ "enum.PipeVisualLayers.Pipe" ]
    - state: heStraight
      map: [ "enum.SubfloorLayers.FirstLayer" ]
  - type: SubFloorHide
    visibleLayers:
    - enum.SubfloorLayers.FirstLayer
  - type: Appearance
  - type: PipeColorVisuals
  - type: AtmosDevice
  - type: HeatExchanger
  - type: NodeContainer
    nodes:
      inlet:
        !type:PipeNode
        nodeGroupID: Pipe
        pipeDirection: North
      outlet:
        !type:PipeNode
        nodeGroupID: Pipe
        pipeDirection: South
  - type: Construction
    graph: GasBinary
    node: radiator
  - type: AtmosMonitoringConsoleDevice
    navMapBlip: Thermoregulator
