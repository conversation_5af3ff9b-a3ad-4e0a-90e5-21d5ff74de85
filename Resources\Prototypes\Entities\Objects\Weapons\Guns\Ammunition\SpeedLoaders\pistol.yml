- type: entity
  id: BaseSpeedLoaderPistol
  name: "швидкісний зарядник (.35 auto)"
  parent: BaseItem
  abstract: true
  components:
  - type: Tag
    tags:
      - SpeedLoaderPistol
  - type: SpeedLoader
  - type: BallisticAmmoProvider
    whitelist:
      tags:
        - CartridgePistol
    capacity: 6
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/SpeedLoaders/Pistol/pistol_speed_loader.rsi
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
        ents: []

- type: entity
  id: SpeedLoaderPistol
  name: "швидкісний зарядник (.35 auto)"
  parent: BaseSpeedLoaderPistol
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistol
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: base-6
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: base
    steps: 7
    zeroVisible: false
  - type: Appearance

- type: entity
  id: SpeedLoaderPistolPractice
  name: "швидкісний зарядник (.35 автопрактика)"
  parent: BaseSpeedLoaderPistol
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistolPractice
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: practice-6
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: practice
    steps: 7
    zeroVisible: false
  - type: Appearance

- type: entity
  id: SpeedLoaderPistolRubber
  name: "швидкозаряджач (.35 авто гумовий)"
  parent: BaseSpeedLoaderPistol
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistolRubber
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: rubber-6
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: rubber
    steps: 7
    zeroVisible: false
  - type: Appearance
