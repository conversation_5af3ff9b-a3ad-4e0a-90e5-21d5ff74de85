- type: entity
  id: WashingMachine
  parent: BaseStructureDynamic
  name: "пральна машина"
  description: "<PERSON><PERSON><PERSON><PERSON><PERSON>, яка пере одяг за допомогою сталевого барабана, що обертається, у блискучій рамі."
  components:
  - type: Sprite
    noRot: true
    sprite: Nyanotrasen/Structures/Machines/washer.rsi
    layers:
    # GenericVisualizer doesn't have a way to deal with multiple conditions at
    # once. So have all the layers ready to go, but don't show them unless
    # they're relevant.
    #
    # If we actually get more complex interactions with washing machines later,
    # we can take a look at making this cleaner.
    - map: ["enum.WashingMachineVisualLayers.Normal"]
      state: "normal-base"
    - map: ["enum.WashingMachineVisualLayers.Broken"]
      state: "broken-base"
      visible: false
    - map: ["enum.WashingMachineVisualLayers.Contents"]
      state: "stuff"
      visible: false
    - map: ["enum.WashingMachineVisualLayers.NormalDoor"]
      state: "normal-closed"
    - map: ["enum.WashingMachineVisualLayers.BrokenDoor"]
      state: "broken-closed"
      visible: false
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.WashingMachineVisualState.Broken:
        enum.WashingMachineVisualLayers.Normal:
          True: { visible: false }
          False: { visible: true }
        enum.WashingMachineVisualLayers.NormalDoor:
          True: { visible: false }
          False: { visible: true }
        enum.WashingMachineVisualLayers.Broken:
          True: { visible: true }
          False: { visible: false }
        enum.WashingMachineVisualLayers.BrokenDoor:
          True: { visible: true }
          False: { visible: false }
      enum.StorageVisuals.HasContents:
        enum.WashingMachineVisualLayers.Contents:
          True: { visible: true }
          False: { visible: false }
      enum.StorageVisuals.Open:
        enum.WashingMachineVisualLayers.NormalDoor:
          True: { state: "normal-open" }
          False: { state: "normal-closed" }
        enum.WashingMachineVisualLayers.BrokenDoor:
          True: { state: "broken-open" }
          False: { state: "broken-closed" }
  - type: WashingMachine
  - type: InteractionOutline
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.35,-0.25,0.35,0.49"
        density: 600
        mask:
        - MachineMask
        layer:
        - MachineLayer
  - type: Climbable
    delay: 1.6
  - type: Storage
    maxItemSize: Normal
    grid:
      - 0,0,5,5
    storageOpenSound:
      path: /Audio/Nyanotrasen/Machines/washer_open.ogg
    storageCloseSound:
      path: /Audio/Nyanotrasen/Machines/washer_close.ogg
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
        ents: []
  - type: UserInterface
    interfaces:
      enum.StorageUiKey.Key:
        type: StorageBoundUserInterface
  - type: UseDelay
    delay: 0.5
  - type: Repairable
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: ["Destruction"]
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:EmptyAllContainersBehaviour
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlass:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: ["Breakage"]

- type: entity
  id: WashingMachineBroken
  parent: WashingMachine
  name: "пральна машина"
  suffix: broken
  description: "Розбите на шматки скло та сталь, яке найближчим часом нічого не відмиє. Виглядає запиленим."
  components:
  - type: Sprite
    layers:
    - map: ["enum.WashingMachineVisualLayers.Broken"]
      state: "broken-base"
    - map: ["enum.WashingMachineVisualLayers.Contents"]
      state: "stuff"
      visible: false
    - map: ["enum.WashingMachineVisualLayers.BrokenDoor"]
      state: "broken-closed"
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.StorageVisuals.HasContents:
        enum.WashingMachineVisualLayers.Contents:
          True: { visible: true }
          False: { visible: false }
      enum.StorageVisuals.Open:
        enum.WashingMachineVisualLayers.BrokenDoor:
          True: { state: "broken-open" }
          False: { state: "broken-closed" }
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: ["Destruction"]

- type: entity
  id: WashingMachineFilledClothes
  parent: WashingMachine
  name: "пральна машина"
  suffix: random clothes
  components:
  - type: StorageFill
    contents:
    - id: Soap
      prob: 0.3
    - id: ClothingOuterWinterCoatPlaid
      prob: 0.5
    - id: ClothingUniformMNKTracksuitBlack
      prob: 0.3
    - id: ClothingCostumeNaota
      prob: 0.2
    - id: ClothingHeadBandSkull
      prob: 0.2
    - id: ClothingNeckScarfStripedBlue
      prob: 0.3

