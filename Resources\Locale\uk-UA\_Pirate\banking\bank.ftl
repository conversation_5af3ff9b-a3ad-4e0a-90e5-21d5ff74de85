bank-program-ui-no-account = [color=red]Обліковий запис не привʼязаний.[/color]
bank-program-name = Віртуальний гаманець
bank-program-ui-link-account = Привʼязати облік. запис
bank-program-ui-account-number = Номер облік. запису
bank-program-ui-link-confirm = Привʼязати
bank-program-ui-link-cancel = Відмінити
bank-program-ui-account-number-text = Облік. запис №{ $account }
bank-program-ui-account-owner-text = Власник облік. запису: { $owner }
bank-program-ui-link-error = [color=red]Помилка привʼязки облік. запису.[/color]
bank-program-ui-link-success = [color=green]Облік. запис успішно привʼязано.[/color]
bank-program-ui-link-program = Облік. запис буде привʼязано до застосунку.
bank-program-ui-link-id-card = Облік. запис буде привʼязано до ID картки.
bank-program-ui-link-no-id-card = [color=red]ID картка відсутня.[/color]
bank-program-ui-link-id-card-linked = [color=red]до ID картки вже привʼязано облік. запис: { $account }[/color]
