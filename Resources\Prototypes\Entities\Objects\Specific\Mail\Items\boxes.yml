# Mail-only boxes. If/when something uses these outside of the mail, move the entry into Catalog/Fills.

- type: entity
  name: "пачка пробників ароматизованого мила"
  parent: BoxCardboard
  id: BoxSoapsAssorted
  description: "Коробка з різним ароматизованим милом. О, лаванда."
  components:
  - type: StorageFill
    contents:
    - id: SoapNT
      amount: 1
    - id: Soap
      amount: 1
    - id: SoapHomemade
      amount: 1
    - id: SoapDeluxe
      amount: 1
  - type: Storage
    maxItemSize: Normal
    grid:
    - 0,0,3,1
    whitelist:
      tags:
      - Soap
  - type: Sprite
    layers:
    - state: box

- type: entity
  name: "пачка пробників ароматизованого мила"
  parent: BoxCardboard
  id: BoxSoapsAssortedOmega
  description: "Коробка з різним ароматизованим милом. О, блюзовий простір."
  components:
  - type: StorageFill
    contents:
    - id: SoapNT
      amount: 1
    - id: Soap
      amount: 1
    - id: SoapOmega
      amount: 1
    - id: SoapDeluxe
      amount: 1
  - type: Storage
    maxItemSize: Normal
    grid:
    - 0,0,3,1
    whitelist:
      tags:
      - Soap
  - type: Sprite
    layers:
    - state: box

- type: entity
  name: "Набір \"Побудуй-друга"
  suffix: Human
  parent: BoxHug
  id: BoxBuildABuddyHuman
  description: "Конструктор \"Генрі-людина\" Build-a-Buddy. Потрібно зібрати."
  components:
  - type: StorageFill
    contents:
    - id: HeadHuman
      amount: 1
    - id: TorsoHuman
      amount: 1
    - id: LeftArmHuman
      amount: 1
    - id: RightArmHuman
      amount: 1
    - id: LeftHandHuman
      amount: 1
    - id: RightHandHuman
      amount: 1
    - id: LeftLegHuman
      amount: 1
    - id: RightLegHuman
      amount: 1
    - id: LeftFootHuman
      amount: 1
    - id: RightFootHuman
      amount: 1
  - type: Storage
    grid:
    - 0,0,4,3
    whitelist:
      components:
      - BodyPart

# DeltaV - Goblins Aren't Real
#- type: entity
#  name: Build-a-Buddy kit
#  suffix: Goblin
#  parent: BoxBuildABuddyHuman
#  id: BoxBuildABuddyGoblin
#  description: "\"Greta the Goblin\" Build-a-Buddy kit. Some assembly required."
#  components:
#  - type: StorageFill
#    contents:
#      - id: HeadGoblin
#        amount: 1
#      - id: TorsoGoblin
#        amount: 1
#      - id: LeftArmGoblin
#        amount: 1
#      - id: RightArmGoblin
#        amount: 1
#      - id: LeftHandGoblin
#        amount: 1
#      - id: RightHandGoblin
#        amount: 1
#      - id: LeftLegGoblin
#        amount: 1
#      - id: RightLegGoblin
#        amount: 1
#      - id: LeftFootGoblin
#        amount: 1
#      - id: RightFootGoblin
#        amount: 1

- type: entity
  name: "Набір \"Побудуй-друга"
  suffix: Reptilian
  parent: BoxBuildABuddyHuman
  id: BoxBuildABuddyReptilian
  description: "Конструктор \"Рептилія Ренді\" Build-a-Buddy. Потрібно зібрати."
  components:
  - type: StorageFill
    contents:
    - id: HeadReptilian
      amount: 1
    - id: TorsoReptilian
      amount: 1
    - id: LeftArmReptilian
      amount: 1
    - id: RightArmReptilian
      amount: 1
    - id: LeftHandReptilian
      amount: 1
    - id: RightHandReptilian
      amount: 1
    - id: LeftLegReptilian
      amount: 1
    - id: RightLegReptilian
      amount: 1
    - id: LeftFootReptilian
      amount: 1
    - id: RightFootReptilian
      amount: 1

- type: entity
  name: "Набір \"Побудуй-друга"
  suffix: Slime
  parent: BoxBuildABuddyHuman
  id: BoxBuildABuddySlime
  description: "Конструктор \"Стівен Слимак\" Build-a-Buddy. Потрібно зібрати."
  components:
  - type: StorageFill
    contents:
    - id: HeadSlime
      amount: 1
    - id: TorsoSlime
      amount: 1
    - id: LeftArmSlime
      amount: 1
    - id: RightArmSlime
      amount: 1
    - id: LeftHandSlime
      amount: 1
    - id: RightHandSlime
      amount: 1
    - id: LeftLegSlime
      amount: 1
    - id: RightLegSlime
      amount: 1
    - id: LeftFootSlime
      amount: 1
    - id: RightFootSlime
      amount: 1

- type: entity
  name: "Набір \"Побудуй-друга"
  suffix: Vulpkanin
  parent: BoxBuildABuddyHuman
  id: BoxBuildABuddyVulpkanin
  description: "Конструктор \"Валерій Вульпканін\" Build-a-Buddy. Потрібно зібрати."
  components:
  - type: StorageFill
    contents:
    - id: HeadVulpkanin
      amount: 1
    - id: TorsoVulpkanin
      amount: 1
    - id: LeftArmVulpkanin
      amount: 1
    - id: RightArmVulpkanin
      amount: 1
    - id: LeftHandVulpkanin
      amount: 1
    - id: RightHandVulpkanin
      amount: 1
    - id: LeftLegVulpkanin
      amount: 1
    - id: RightLegVulpkanin
      amount: 1
    - id: LeftFootVulpkanin
      amount: 1
    - id: RightFootVulpkanin
      amount: 1
