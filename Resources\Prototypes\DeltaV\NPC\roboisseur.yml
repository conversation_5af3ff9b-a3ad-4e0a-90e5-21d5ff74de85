- type: entity
  parent: BaseStructure
  id: Roboisseur
  name: "Пан Батлертрон"
  description: "Він замовляє їжу, щоб доставити її екзотичним клієнтам по всьому космосу. Працює на новітніх технологіях блюспейс доставки їжі."
  components:
  - type: Anchorable
  - type: Pullable
  - type: Sprite
    noRot: true
    drawdepth: Mobs
    sprite: DeltaV/Structures/Machines/roboisseur.rsi
    layers:
    - state: roboisseur-1
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 1000
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Roboisseur
  - type: Speech
    speechSounds: Pai
  - type: Appearance
  - type: Grammar
    attributes:
      gender: male
      proper: true
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.15,-0.15,0.15,0.15"
        density: 60
        mask:
        - MachineMask
        layer:
        - MidImpassable
        - LowImpassable
