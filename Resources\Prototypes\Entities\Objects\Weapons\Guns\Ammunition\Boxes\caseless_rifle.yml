- type: entity
  abstract: true
  parent: BaseMagazineBox
  id: BaseMagazineBoxCaselessRifle
  name: "коробочка з набоями (.25 без гільз)"
  components:
  - type: BallisticAmmoProvider
    mayTransfer: true
    whitelist:
      tags:
        - CartridgeCaselessRifle
    proto: CartridgeCaselessRifle
    capacity: 60
  - type: Item
    size: Small
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Boxes/caseless_rifle.rsi
  - type: MagazineVisuals
    magState: mag
    steps: 2
    zeroVisible: false
  - type: Appearance

# Boxes
- type: entity
  parent: BaseMagazineBoxCaselessRifle
  id: MagazineBoxCaselessRifle10x24
  name: "коробочка з набоями (.25 без гільз)"
  components:
  - type: BallisticAmmoProvider
    capacity: 200
    proto: CartridgeCaselessRifle
  - type: Sprite
    layers:
    - state: base-10x24
      map: ["enum.GunVisualLayers.Base"]
    - state: mag10-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag10
    steps: 2
    zeroVisible: false
  - type: Appearance

- type: entity
  parent: BaseMagazineBoxCaselessRifle
  id: MagazineBoxCaselessRifleBig
  name: "коробочка з набоями (.25 без гільз)"
  components:
  - type: BallisticAmmoProvider
    capacity: 200
    proto: CartridgeCaselessRifle
  - type: Sprite
    layers:
    - state: base-b
      map: ["enum.GunVisualLayers.Base"]
    - state: magb-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: magb
    steps: 2
    zeroVisible: false
  - type: Appearance

- type: entity
  parent: BaseMagazineBoxCaselessRifle
  id: MagazineBoxCaselessRifleBigRubber
  name: "коробка боєприпасів (.25 безгільзовий гумовий)"
  components:
  - type: BallisticAmmoProvider
    capacity: 200
    proto: CartridgeCaselessRifleRubber
  - type: Sprite
    layers:
    - state: base-b
      map: ["enum.GunVisualLayers.Base"]
    - state: magb-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: rubber-b
  - type: MagazineVisuals
    magState: magb
    steps: 2
    zeroVisible: false
  - type: Appearance

- type: entity
  parent: BaseMagazineBoxCaselessRifle
  id: MagazineBoxCaselessRifle
  name: "коробочка з набоями (.25 без гільз)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifle
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  parent: BaseMagazineBoxCaselessRifle
  id: MagazineBoxCaselessRiflePractice
  name: "коробочка з набоями (.25 безоболонковий набій)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRiflePractice
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: practice

- type: entity
  parent: BaseMagazineBoxCaselessRifle
  id: MagazineBoxCaselessRifleRubber
  name: "коробка боєприпасів (.25 безгільзовий гумовий)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifleRubber
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: rubber

- type: entity
  parent: MagazineBoxCaselessRifle
  id: MagazineBoxCaselessRifleIncendiary
  name: "коробка боєприпасів (.25 безгільзовий запальний)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifleIncendiary

- type: entity
  parent: MagazineBoxCaselessRifle
  id: MagazineBoxCaselessRifleUranium
  name: "коробка боєприпасів (.25 безгільзовий, урановий)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifleUranium

- type: entity
  parent: MagazineBoxCaselessRifle
  id: MagazineBoxCaselessRifleShrapnel
  name: "коробка боєприпасів (.25 безгільзовий, шрапнельний)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifleShrapnel
