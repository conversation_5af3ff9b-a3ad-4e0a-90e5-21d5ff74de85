﻿- type: entity
  id: BlastDoor
  parent: BaseShutter
  name: "вибухові двері"
  description: "На цьому написано \"Вибуховий пристрій\"."
  components:
  - type: AccessReader
  - type: Sprite
    sprite: Structures/Doors/Shutters/blastdoor.rsi
    layers:
    - state: closed
      map: ["enum.DoorVisualLayers.Base"]
  - type: Door
    closeTimeOne: 0.4
    closeTimeTwo: 0.4
    openTimeOne: 0.4
    openTimeTwo: 0.4
    openingAnimationTime: 1.0
    closingAnimationTime: 1.0
    canPry: false
    crushDamage:
      types:
        Blunt: 25 # yowch
  - type: Occluder
  - type: Appearance
  - type: RadiationBlocker
    resistance: 10
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: StrongMetallic
  - type: ContainerFill
    containers:
      board: [ DoorElectronics ]
  - type: Construction
    graph: BlastDoor
    node: blastdoor
    containers:
    - board
  - type: StaticPrice
    price: 280

- type: entity
  id: BlastDoorOpen
  parent: BlastDoor
  suffix: Open
  components:
  - type: Door
    state: Open
  - type: Occluder
    enabled: false
  - type: Physics
    canCollide: false
  - type: Airtight
    airBlocked: false
  - type: RadiationBlocker
    enabled: false

- type: entity
  id: BlastDoorFrame
  parent: BaseStructureDynamic
  name: "рама противибухових дверей"
  description: "На цьому написано \"Вибуховий пристрій\"."
  components:
  - type: Sprite
    sprite: Structures/Doors/Shutters/blastdoor.rsi
    state: assembly
  - type: InteractionOutline
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.45"
        density: 55
        mask:
        - Impassable
        - HighImpassable
        layer:
        - HighImpassable
  - type: Transform
    noRot: true
  - type: Construction
    graph: BlastDoor
    node: frame1
  placement:
    mode: SnapgridCenter
