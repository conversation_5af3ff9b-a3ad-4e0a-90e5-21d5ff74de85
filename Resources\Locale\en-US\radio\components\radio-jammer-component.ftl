radio-jammer-component-on-use = The jammer is now {$state}.
radio-jammer-component-on-state = on
radio-jammer-component-off-state = off

radio-jammer-component-examine-on-state = The light is currently [color=darkgreen]on[/color].
radio-jammer-component-examine-off-state = The light is currently [color=darkred]off[/color].

radio-jammer-component-setting-high = High
radio-jammer-component-setting-medium = Medium
radio-jammer-component-setting-low = Low

radio-jammer-component-set-message-high = The jammer is now operating at high power.
radio-jammer-component-set-message-medium = The jammer is now operating at medium power.
radio-jammer-component-set-message-low = The jammer is now operating at low power.

radio-jammer-component-switch-setting = The power level switch is set to "[color=yellow]{$powerLevel}[/color]".
