using Content.Client.UserInterface.Controls;
using Content.Shared.Cargo;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Cargo.UI;

[GenerateTypedNameReferences]
public sealed partial class CargoPalletMenu : FancyWindow
{
    public Action? SellRequested;
    public Action? AppraiseRequested;

    public CargoPalletMenu()
    {
        RobustXamlLoader.Load(this);
        SellButton.OnPressed += OnSellPressed;
        AppraiseButton.OnPressed += OnAppraisePressed;
    }

    public void SetAppraisal(int amount)
    {
        AppraisalLabel.Text = Loc.GetString("cargo-console-menu-points-amount", ("amount", amount.ToString()));
    }

    public void SetCount(int count)
    {
        CountLabel.Text = count.ToString();
    }
    public void SetEnabled(bool enabled)
    {
        AppraiseButton.Disabled = !enabled;
        SellButton.Disabled = !enabled;
    }

    private void OnSellPressed(BaseButton.ButtonEventArgs obj)
    {
        SellRequested?.Invoke();
    }

    private void OnAppraisePressed(BaseButton.ButtonEventArgs obj)
    {
        AppraiseRequested?.Invoke();
    }
}
