- type: entity
  id: GravityGenerator
  parent: BaseMachinePowered
  name: "гравітаційний генератор"
  description: "Це те, що утримує вас на підлозі."
  placement:
    mode: AlignTileAny
  components:
  - type: StationAiWhitelist
  - type: AmbientSound
    enabled: false
    volume: -6
    range: 7
    sound:
      path: /Audio/Ambience/Objects/gravity_gen_hum.ogg
  - type: Sprite
    sprite: Structures/Machines/gravity_generator.rsi
    layers:
      - state: on
        map: ["enum.GravityGeneratorVisualLayers.Base"]
      - sprite: Structures/Machines/gravity_generator_core.rsi
        state: activated
        shader: unshaded
        map: ["enum.GravityGeneratorVisualLayers.Core"]
  - type: Transform
    anchored: true
  - type: ApcPowerReceiver
    powerLoad: 2500
  - type: ExtensionCableReceiver
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-1.5,-1.5,1.5,1.5"
        density: 50
        mask:
        - LargeMobMask
        layer:
        - WallLayer
  - type: Repairable
    fuelCost: 10
    doAfterDelay: 5
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:DoActsBehavior
        acts: ["Breakage"]
  - type: PowerCharge
    windowTitle: gravity-generator-window-title
    idlePower: 50
    activePower: 2500
  - type: GravityGenerator
    lightRadiusMin: 0.75
    lightRadiusMax: 2.5
    spriteMap:
      broken: "broken"
      unpowered: "off"
      off: "off"
      on: "on"
  - type: ActivatableUI
    key: enum.PowerChargeUiKey.Key
  - type: ActivatableUIRequiresPower
  - type: UserInterface
    interfaces:
      enum.PowerChargeUiKey.Key:
        type: PowerChargeBoundUserInterface
  - type: Appearance
  - type: PointLight
    radius: 2.5
    energy: 0.5
    # Gravity generator is a large machine, not casting shadows is fine within the radius set above.
    castShadows: false
    color: "#a8ffd9"
  - type: StaticPrice
    price: 5000
  - type: Tag # Goobstation
    tags:
    - Structure
    - IgnoreImmovableRod

- type: entity
  id: GravityGeneratorMini
  parent: [ GravityGenerator, ConstructibleMachine ]
  name: "міні-гравітаційний генератор"
  description: "Це те, що утримує вас на підлозі, тепер у смішному розмірі."
  components:
  - type: StationAiWhitelist
  - type: Sprite
    sprite: Structures/Machines/gravity_generator_mini.rsi
    snapCardinals: true
    layers:
      - state: on
        map: ["enum.GravityGeneratorVisualLayers.Base"]
      - sprite: Structures/Machines/gravity_generator_core.rsi
        state: activated
        shader: unshaded
        map: ["enum.GravityGeneratorVisualLayers.Core"]
        scale: "0.4,0.4"
        offset: "0,0.2"
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.4,0.4,0.4"
        density: 3125
        mask:
        - LargeMobMask
        layer:
        - WallLayer
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 500
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          MachineFrameDestroyed:
            min: 1
            max: 1
  - type: WiresPanel
  - type: Machine
    board: MiniGravityGeneratorCircuitboard
  - type: ApcPowerReceiver
    powerLoad: 500
  - type: PowerCharge
    idlePower: 15
    activePower: 500
  - type: GravityGenerator
    lightRadiusMin: 0.75
    lightRadiusMax: 2.5
  - type: StaticPrice
    price: 2000
  - type: Tag # Goobstation
    tags:
    - Structure
