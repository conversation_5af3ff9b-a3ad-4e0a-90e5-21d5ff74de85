# End points - the eventual goal is that almost all foods and drinks metabolize into either these, or reagents that build up (e.g. theobromine, caffeine,... )
- type: reagent
  id: Nutriment #Anything that isn't better suited to vitamin, protein, or sugar. we aren't doing a super in-depth simulator.
  name: reagent-name-nutriment
  group: Foods
  desc: reagent-desc-nutriment
  physicalDesc: reagent-physical-desc-opaque
  flavor: nutriment
  color: "#24591F"
  metabolisms:
    Food:
      effects:
      - !type:SatiateHunger
  plantMetabolism:
  - !type:PlantAdjustNutrition
    amount: 1.5
  - !type:PlantAdjustHealth
    amount: 0.75
  pricePerUnit: 2

- type: reagent
  id: Vitamin #Anything "healthy"
  name: reagent-name-vitamin
  group: Foods
  desc: reagent-desc-vitamin
  physicalDesc: reagent-physical-desc-chalky
  flavor: vitamin
  color: "#D3D3D3"
  metabolisms:
    Food: #This makes it not compete with medicines, a large bonus for something that can heal
      effects:
      - !type:HealthChange
        probability: 0.5
        damage:
          groups:
            Brute: -0.5
            Burn: -0.5
      # Helps you stop bleeding to an extent.
      - !type:ModifyBleedAmount
        amount: -0.25
      - !type:SatiateHunger #Numbers are balanced with this in mind + it helps limit how much healing you can get from food
  # Lets plants benefit too
  plantMetabolism:
  - !type:PlantAdjustNutrition
    amount: 0.5
  - !type:PlantAdjustHealth
    amount: 1.5
  pricePerUnit: 2.5

- type: reagent
  id: Protein #Meat and beans
  name: reagent-name-protein
  group: Foods
  desc: reagent-desc-protein
  physicalDesc: reagent-physical-desc-clumpy
  flavor: protein
  color: "#FFFFE5"
  metabolisms:
    Food:
      effects:
      - !type:HealthChange
        probability: 0.5
        damage:
          groups:
            Brute: -0.4
      - !type:ModifyBloodLevel
        amount: 1 # weaker than iron but pretty good all things considered
      - !type:SatiateHunger
  pricePerUnit: 3

- type: reagent
  id: Sugar #Candy and grains
  name: reagent-name-sugar
  group: Foods
  desc: reagent-desc-sugar
  physicalDesc: reagent-physical-desc-sweet
  flavor: sweet
  color: white
  meltingPoint: 146.0
  metabolisms:
    Food:
      effects:
      - !type:SatiateHunger
        conditions:
        - !type:ReagentThreshold #Only satiates when eaten with nutriment
          reagent: Nutriment
          min: 0.1
        factor: 1
      - !type:ChemAddMoodlet
        moodPrototype: SweetenerEffect
  plantMetabolism:
  - !type:PlantAdjustNutrition
    amount: 0.1
  - !type:PlantAdjustWeeds
    amount: 2
  - !type:PlantAdjustPests
    amount: 2
  metamorphicSprite:
    sprite: Objects/Consumable/Drinks/sugarglass.rsi
    state: icon_empty
  metamorphicMaxFillLevels: 7
  metamorphicFillBaseName: fill-
  metamorphicChangeColor: false

- type: reagent
  id: PumpkinFlesh #Just so pumpkins spill orange stuff when smashed
  parent: Nutriment
  name: reagent-name-pumpkin-flesh
  desc: reagent-desc-pumpkin-flesh
  flavor: pumpkin
  color: "#fc9300"
