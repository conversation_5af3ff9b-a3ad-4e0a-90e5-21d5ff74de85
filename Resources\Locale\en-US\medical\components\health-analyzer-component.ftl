health-analyzer-window-no-patient-data-text = No patient data.
health-analyzer-window-entity-unknown-text = Unknown
health-analyzer-window-entity-unknown-species-text = Non-Humanoid
health-analyzer-window-entity-unknown-value-text = N/A

health-analyzer-window-entity-alive-text = Alive
health-analyzer-window-entity-dead-text = Dead
health-analyzer-window-entity-critical-text = Critical

health-analyzer-window-entity-temperature-text = Temperature:
health-analyzer-window-entity-blood-level-text = Blood Level:
health-analyzer-window-entity-status-text = Status:
health-analyzer-window-entity-damage-total-text = Total Damage:

health-analyzer-window-damage-group-text = {$damageGroup}: {$amount}
health-analyzer-window-damage-type-text = {$damageType}: {$amount}
health-analyzer-window-damage-type-duplicate-text = {$damageType}: {$amount} (duplicate)

health-analyzer-window-damage-group-Brute = Brute
health-analyzer-window-damage-type-Blunt = Blunt
health-analyzer-window-damage-type-Slash = Slash
health-analyzer-window-damage-type-Piercing = Piercing

health-analyzer-window-damage-group-Burn = Burn
health-analyzer-window-damage-type-Heat = Heat
health-analyzer-window-damage-type-Shock = Shock
health-analyzer-window-damage-type-Cold = Cold
health-analyzer-window-damage-type-Caustic = Caustic

health-analyzer-window-damage-group-Airloss = Airloss
health-analyzer-window-damage-type-Asphyxiation = Asphyxiation
health-analyzer-window-damage-type-Bloodloss = Bloodloss

health-analyzer-window-damage-group-Toxin = Toxin
health-analyzer-window-damage-type-Poison = Poison
health-analyzer-window-damage-type-Radiation = Radiation

health-analyzer-window-damage-group-Genetic = Genetic
health-analyzer-window-damage-type-Cellular = Cellular

health-analyzer-window-malnutrition = Severely malnourished

health-analyzer-window-entity-unrevivable-text = Unique body composition detected! Patient can not be resuscitated by normal means!
health-analyzer-window-entity-bleeding-text = Patient is bleeding!

health-analyzer-window-scan-mode-text = Scan Mode:
health-analyzer-window-scan-mode-active = Active
health-analyzer-window-scan-mode-inactive = Inactive

health-analyzer-popup-scan-target = {CAPITALIZE(THE($user))} is trying to scan you!
health-analyzer-window-return-button-text = < Return
