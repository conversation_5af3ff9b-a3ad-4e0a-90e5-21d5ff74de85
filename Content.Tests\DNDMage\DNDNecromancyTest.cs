using System;
using Content.Server._Pirate.DNDMage;
using Content.Shared._Pirate.DNDMage;
using Content.Shared.Damage;
using Content.Shared.Mobs;
using Content.Shared.Mobs.Components;
using Content.Shared.Mobs.Systems;
using NUnit.Framework;
using Robust.Shared.GameObjects;
using Robust.Shared.Map;
using Robust.UnitTesting;

namespace Content.Tests.DNDMage;

[TestFixture]
public sealed class DNDNecromancyTest
{
    [Test]
    public void TestNecromancyServantComponent()
    {
        // Створюємо компонент слуги некроманта
        var component = new DNDNecromancyServantComponent();

        // Перевіряємо початкові значення
        Assert.That(component.Master, Is.EqualTo(EntityUid.Invalid));
        Assert.That(component.ServantDuration, Is.EqualTo(300f));
        Assert.That(component.ShouldFollowMaster, Is.True);
        Assert.That(component.MaxDistanceFromMaster, Is.EqualTo(15f));
        Assert.That(component.CanAttackEnemies, Is.True);
        Assert.That(component.AttackRadius, Is.EqualTo(5f));
        Assert.That(component.ShouldProtectMaster, Is.True);
        Assert.That(component.DamageShare, Is.EqualTo(0.25f));
        Assert.That(component.IsActive, Is.True);
        Assert.That(component.Type, Is.EqualTo(ServantType.Basic));
        Assert.That(component.Loyalty, Is.EqualTo(100f));
        Assert.That(component.MaxLoyalty, Is.EqualTo(100f));
        Assert.That(component.LoyaltyDecayRate, Is.EqualTo(0.5f));
        Assert.That(component.CurrentCommand, Is.EqualTo(ServantCommand.Follow));

        // Тестуємо зміну значень
        var master = new EntityUid(123);
        component.Master = master;
        component.ServantDuration = 60f;
        component.Type = ServantType.Warrior;

        Assert.That(component.Master, Is.EqualTo(master));
        Assert.That(component.ServantDuration, Is.EqualTo(60f));
        Assert.That(component.Type, Is.EqualTo(ServantType.Warrior));
    }

    [Test]
    public void TestServantTypes()
    {
        // Тестуємо всі типи слуг
        Assert.That(Enum.GetValues(typeof(ServantType)).Length, Is.EqualTo(5));
        Assert.That(Enum.IsDefined(typeof(ServantType), ServantType.Basic), Is.True);
        Assert.That(Enum.IsDefined(typeof(ServantType), ServantType.Warrior), Is.True);
        Assert.That(Enum.IsDefined(typeof(ServantType), ServantType.Guardian), Is.True);
        Assert.That(Enum.IsDefined(typeof(ServantType), ServantType.Scout), Is.True);
        Assert.That(Enum.IsDefined(typeof(ServantType), ServantType.Mage), Is.True);
    }

    [Test]
    public void TestServantCommands()
    {
        // Тестуємо всі команди слуг
        Assert.That(Enum.GetValues(typeof(ServantCommand)).Length, Is.EqualTo(5));
        Assert.That(Enum.IsDefined(typeof(ServantCommand), ServantCommand.Follow), Is.True);
        Assert.That(Enum.IsDefined(typeof(ServantCommand), ServantCommand.Guard), Is.True);
        Assert.That(Enum.IsDefined(typeof(ServantCommand), ServantCommand.Attack), Is.True);
        Assert.That(Enum.IsDefined(typeof(ServantCommand), ServantCommand.Patrol), Is.True);
        Assert.That(Enum.IsDefined(typeof(ServantCommand), ServantCommand.Stay), Is.True);
    }
}
