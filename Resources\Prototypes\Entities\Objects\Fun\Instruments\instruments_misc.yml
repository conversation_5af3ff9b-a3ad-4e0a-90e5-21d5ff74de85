﻿- type: entity
  parent: BaseHandheldInstrument
  id: MusicalLungInstrument
  name: "музичні легені"
  description: "Духовна і магічна легеня колишньої оперної співачки. Хоча, чесно кажучи, все це роблять голосові зв'язки."
  components:
  - type: Sprite
    sprite: Mobs/Species/Human/organs.rsi
    state: lung-l
  - type: Instrument
    program: 121
  - type: Item
    size: Small

- type: entity
  parent: BaseHandheldInstrument
  id: SeashellInstrument
  name: "мушля"
  description: "Для прокладання берегової лінії биття."
  components:
  - type: Sprite
    sprite: Objects/Fun/Instruments/otherinstruments.rsi
    state: seashell
  - type: Instrument
    program: 122
  - type: Item
    size: Normal

- type: entity
  parent: BaseHandheldInstrument
  id: BirdToyInstrument
  name: "пташиний свист"
  description: "Чудовий маленький свисток у формі пташки. Чудово співає."
  components:
  - type: Sprite
    sprite: Objects/Fun/Instruments/otherinstruments.rsi
    state: birdtoy
  - type: Instrument
    program: 123
  - type: Item
    size: Small
    storedRotation: 90

# Entity is renamed to hijack its id
# ID is redefined in Resources/Prototypes/_Harmony/Entities/Objects/Devices/red_phone.yml
# child entites: PhoneInstrumentSyndicate, BananaPhoneInstrument
- type: entity
  parent: BaseHandheldInstrument
  id: PhoneInstrumentUpstream # Harmony
  name: "червоний телефон"
  description: "Якщо щось піде не так..."
  components:
  - type: Sprite
    sprite: Objects/Fun/Instruments/otherinstruments.rsi
    state: red_phone
  - type: EmitSoundOnLand
    sound:
       path: /Audio/Items/ring.ogg
  - type: Instrument
    program: 124
  - type: Item
    size: Small
  - type: Prayable
    sentMessage: prayer-popup-notify-centcom-sent
    notificationPrefix: prayer-chat-notify-centcom
    verb: prayer-verbs-call
    verbImage: null

- type: entity
  parent: PhoneInstrumentUpstream # Harmony, due to hijacked 'PhoneInstrument'
  id: PhoneInstrumentSyndicate
  name: "криваво-червоний телефон"
  description: "Щоб злі люди дзвонили своїм друзям."
  components:
  - type: Sprite
    sprite: Objects/Fun/Instruments/blood_red_phone.rsi
    state: icon
  - type: Prayable
    sentMessage: prayer-popup-notify-syndicate-sent
    notificationPrefix: prayer-chat-notify-syndicate

- type: entity
  parent: BaseHandheldInstrument
  id: HelicopterInstrument
  name: "іграшковий вертоліт"
  description: "Ch-ka-ch-ka-ch-ka-ch-ka-ch-ka-ch-ka..."
  components:
  - type: Sprite
    sprite: Objects/Fun/Instruments/otherinstruments.rsi
    state: helicopter
  - type: Instrument
    program: 125
  - type: Item
    size: Small
    storedRotation: -90

- type: entity
  parent: BaseHandheldInstrument
  id: CannedApplauseInstrument
  name: "консервовані оплески"
  description: "Здається, хтось уже все використав..."
  components:
  - type: Sprite
    sprite: Objects/Fun/Instruments/otherinstruments.rsi
    state: cannedapplause
  - type: Instrument
    program: 126
  - type: Item
    size: Tiny

- type: entity
  parent: BaseHandheldInstrument
  id: GunpetInstrument
  name: "килим"
  description: "Навіщо вам це вивчати? Хіба це не зрозуміло?"
  components:
  - type: Instrument
    program: 127
  - type: Sprite
    sprite: Objects/Fun/Instruments/gunpet.rsi
    state: icon
  - type: Item
    size: Small
    sprite: Objects/Fun/Instruments/gunpet.rsi
    storedRotation: -90
  - type: Tag
    tags:
    - Sidearm
    - BrassInstrument

- type: entity
  parent: BaseHandheldInstrument
  id: BikeHornInstrument
  name: "позолочений велосипедний клаксон"
  description: "Вишукано декорований велосипедний клаксон, здатний сигналити різними нотами."
  components:
  - type: Instrument
    program: 1
    bank: 1
  - type: Sprite
    sprite: Objects/Fun/Instruments/bike_horn.rsi
    state: icon
  - type: Tag
    tags:
    - BrassInstrument #Go figure.
  - type: Item
    sprite: Objects/Fun/Instruments/bike_horn.rsi
    size: Small
    storedRotation: -90
  - type: Clothing
    sprite: Objects/Fun/Instruments/bike_horn.rsi
    slots: [Belt]
    quickEquip: false

- type: entity
  parent: PhoneInstrumentUpstream # Harmony, due to hijacked 'PhoneInstrument'
  id: BananaPhoneInstrument
  name: "банановий телефон"
  description: "Пряма лінія до Honkmother. Здається, завжди переходить на голосову пошту."
  components:
  - type: Sprite
    sprite: Objects/Fun/Instruments/otherinstruments.rsi
    state: banana_phone
  - type: EmitSoundOnLand
    sound:
       path: /Audio/Items/bikehorn.ogg
  - type: Slippery
    paralyzeTime: 4
    launchForwardsMultiplier: 1.5
  - type: StepTrigger
  - type: CollisionWake
    enabled: false
  - type: Fixtures
    fixtures:
      slips:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.3,0.4,0.3"
        hard: false
        layer:
        - SlipLayer
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.3,0.4,0.3"
        density: 5
        mask:
        - ItemMask
  - type: Prayable
    sentMessage: prayer-popup-notify-honkmother-sent
    notificationPrefix: prayer-chat-notify-honkmother
    verb: prayer-verbs-call
    verbImage: null
