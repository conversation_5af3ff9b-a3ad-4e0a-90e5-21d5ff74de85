﻿using Content.Shared.Ghost;
using Robust.Client.GameObjects;
using Robust.Shared.Console;

namespace Content.Client.Ghost;

public sealed class GhostToggleSelfVisibility : IConsoleCommand
{
    public string Command => "toggleselfghost";
    public string Description => "Перемикає видимість власного привида";
    public string Help => "toggleselfghost";
    public void Execute(IConsoleShell shell, string argStr, string[] args)
    {
        var attachedEntity = shell.Player?.AttachedEntity;
        if (!attachedEntity.HasValue)
            return;

        var entityManager = IoCManager.Resolve<IEntityManager>();
        if (!entityManager.HasComponent<GhostComponent>(attachedEntity))
        {
            shell.WriteError("Entity must be a ghost.");
            return;
        }

        if (!entityManager.TryGetComponent(attachedEntity, out SpriteComponent? spriteComponent))
            return;

        spriteComponent.Visible = !spriteComponent.Visible;
    }
}
