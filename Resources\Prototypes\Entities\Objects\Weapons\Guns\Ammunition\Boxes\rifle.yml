- type: entity
  abstract: true
  parent: BaseMagazineBox
  id: BaseMagazineBoxRifle
  components:
  - type: BallisticAmmoProvider
    mayTransfer: true
    whitelist:
      tags:
        - CartridgeRifle
    proto: CartridgeRifle
    capacity: 50
  - type: Item
    size: Small
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Boxes/rifle.rsi
  - type: MagazineVisuals
    magState: mag
    steps: 2
    zeroVisible: false
  - type: Appearance

# Boxes
- type: entity
  parent: BaseMagazineBoxRifle
  id: MagazineBoxRifleBig
  name: "коробочка з набоями (гвинтівка калібру .20)"
  components:
  - type: BallisticAmmoProvider
    capacity: 200
    proto: CartridgeRifle
  - type: Sprite
    layers:
    - state: base-b
      map: ["enum.GunVisualLayers.Base"]
    - state: magb-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: magb
    steps: 2
    zeroVisible: false
  - type: Appearance

- type: entity
  parent: BaseMagazineBoxRifle
  id: MagazineBoxRifleBigRubber
  name: "коробка боєприпасів (.20 гвинтівковий гумовий)"
  components:
  - type: BallisticAmmoProvider
    capacity: 200
    proto: CartridgeRifleRubber
  - type: Sprite
    layers:
    - state: base-b
      map: ["enum.GunVisualLayers.Base"]
    - state: magb-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: practice-b
  - type: MagazineVisuals
    magState: magb
    steps: 2
    zeroVisible: false
  - type: Appearance

- type: entity
  parent: BaseMagazineBoxRifle
  id: MagazineBoxRifle
  name: "коробочка з набоями (гвинтівка калібру .20)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeRifle
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  parent: BaseMagazineBoxRifle
  id: MagazineBoxRiflePractice
  name: "коробочка з набоями (стрільба з гвинтівки калібру .20)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeRiflePractice
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: practice

- type: entity
  parent: BaseMagazineBoxRifle
  id: MagazineBoxRifleRubber
  name: "коробка боєприпасів (.20 гвинтівковий гумовий)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeRifleRubber
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: rubber

- type: entity
  id: MagazineBoxRifleIncendiary
  parent: BaseMagazineBoxRifle
  name: "коробка боєприпасів (.20 гвинтівковий запальний)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeRifleIncendiary
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: incendiary

- type: entity
  id: MagazineBoxRifleUranium
  parent: BaseMagazineBoxRifle
  name: "коробочка з набоями (.20 гвинтівочний уран)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeRifleUranium
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: uranium

- type: entity
  parent: BaseMagazineBoxRifle
  id: MagazineBoxRifleShrapnel
  name: "коробка боєприпасів (.20 гвинтівковий, шрапнельний)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeRifleShrapnel
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
