- type: entity
  id: ClosetWall
  parent: BaseWallCloset
  name: "настінна шафа"
  description: "Це камера зберігання"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: generic_door

- type: entity
  id: ClosetWallEmergency
  name: "аварійна стінна шафа"
  parent: BaseWallCloset
  description: "Це приміщення для зберігання аварійних дихальних масок та балонів з киснем."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: emergency
    stateDoorOpen: emergency_open
    stateDoorClosed: emergency_door

- type: entity
  id: ClosetWallRadiation
  name: "радіаційна настінна шафа"
  parent: BaseWallCloset
  description: "Це сховище для радіаційного обладнання"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: rad
    stateDoorOpen: rad_open
    stateDoorClosed: rad_door

- type: entity
  id: ClosetWallFire
  name: "протипожежна стінна шафа"
  parent: BaseWallCloset
  description: "Це склад для зберігання протипожежного приладдя."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: fire
    stateDoorOpen: fire_open
    stateDoorClosed: fire_door

- type: entity
  id: ClosetWallBlue
  parent: BaseWallCloset
  name: "синя настінна шафа"
  description: "Гардероб, наповнений стильним синім одягом."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: blue_door

- type: entity
  id: ClosetWallPink
  parent: BaseWallCloset
  name: "рожева настінна шафа"
  description: "Шафа, наповнена казковим рожевим одягом."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: pink_door

- type: entity
  id: ClosetWallBlack
  parent: BaseWallCloset
  name: "чорна настінна шафа"
  description: "Шафа, наповнена стильним чорним одягом."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: black_door

- type: entity
  id: ClosetWallGreen
  parent: BaseWallCloset
  name: "зелена настінна шафа"
  description: "Гардероб, наповнений стильним зеленим одягом."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: green_door

- type: entity
  id: ClosetWallOrange
  parent: BaseWallCloset
  name: "тюремна настінна шафа"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: orange_door

- type: entity
  id: ClosetWallYellow
  parent: BaseWallCloset
  name: "жовта настінна шафа"
  description: "Гардероб, наповнений стильним жовтим одягом."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: yellow_door

- type: entity
  id: ClosetWallWhite
  parent: BaseWallCloset
  name: "біла настінна шафа"
  description: "Шафа, наповнена стильним білим одягом."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: white_door

- type: entity
  id: ClosetWallGrey
  parent: BaseWallCloset
  name: "сіра настінна шафа"
  description: "Шафа, переповнена сірим одягом."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: gray_door

- type: entity
  id: ClosetWallMixed
  parent: BaseWallCloset
  name: "змішана стінна шафа"
  description: "Гардероб, наповнений різнобарвним одягом."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: mixed_door

- type: entity
  id: ClosetWallAtmospherics
  parent: BaseWallCloset
  name: "настінна шафа атмосферника"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: atmos_door

- type: entity
  id: LockerWallMedical
  parent: BaseWallLocker
  name: "медична настінна шафка"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: med
    stateDoorOpen: med_open
    stateDoorClosed: med_door
  - type: AccessReader
    access: [["Medical"]]