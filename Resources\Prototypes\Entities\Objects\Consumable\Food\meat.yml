# Base

- type: entity
  parent: FoodInjectableBase
  id: FoodMeatBase
  abstract: true
  components:
  - type: FlavorProfile
    flavors:
      - meaty
  - type: Sprite
    sprite: Objects/Consumable/Food/meat.rsi
  - type: Extractable
    grindableSolutionName: food
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: UncookedAnimalProteins
          Quantity: 1
        - ReagentId: Fat
          Quantity: 5
  - type: Item
    size: Tiny
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        # less mass so it can cook faster, a single strip of bacon isnt 5kg
        density: 1
        mask:
        - ItemMask
        restitution: 0.3  # fite me
        friction: 0.2
  # let air cook and freeze meat for cooking and preservation
  - type: AtmosExposed
  - type: Temperature
    currentTemperature: 290
  # required for cooking to work
  - type: InternalTemperature
    thickness: 0.02
    area: 0.02 # arbitrary number that sounds right for a slab of meat
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      Meaterial: 300

- type: entity
  parent: FoodMeatBase
  id: FoodMeatRawBase
  abstract: true
  components:
  - type: Tag
    tags:
    - Raw
    - Meat
  - type: Perishable
    # raw meat rots in 5 minutes, get it into the freezer fast
    rotAfter: 300
    # don't want meat giving off ammonia only bodies
    molsPerSecondPerUnitMass: 0
  - type: RotInto
    entity: FoodMeatRotten
    # once it would say bloated, turn into the rotten prototype
    stage: 1

# bruh
- type: Tag
  id: Raw

- type: Tag
  id: Cooked

- type: Tag
  id: Cutlet

# Raw

- type: entity
  name: "сире м'ясо"
  parent: FoodMeatRawBase
  id: FoodMeat
  description: "Шматок сирого м'яса."
  components:
  - type: Sprite
    state: plain
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 9
        - ReagentId: Fat
          Quantity: 9
  - type: SliceableFood
    count: 3
    slice: FoodMeatCutlet
  - type: InternalTemperature
    conductivity: 0.43
  - type: Construction
    graph: MeatSteak
    node: start
    defaultTarget: meat steak
  - type: Tag
    tags:
    - Meat

- type: entity
  name: "сире людське м'ясо"
  parent: FoodMeatRawBase
  id: FoodMeatHuman
  description: "Гидота."
  components:
  - type: Sprite
    state: plain
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 9
  - type: SliceableFood
    count: 3
    slice: FoodMeatCutlet

- type: entity
  name: "сире філе коропа"
  parent: FoodMeatBase
  # MeatFish?...
  id: FoodMeatFish
  description: "Ваші останні слова \"Ого, екзотика!\" не варті того. А сам смак? Можливо."
  components:
  - type: FlavorProfile
    flavors:
      - fishy
  - type: Tag
    tags:
    - Raw
    - Meat
  - type: Sprite
    state: fish
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: CarpoToxin
          Quantity: 5
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: CarpoToxin
        Quantity: 5

- type: entity
  name: "сирий бекон"
  # bacon is cured so not raw = cant rot
  parent: FoodMeatBase
  id: FoodMeatBacon
  description: "Сирий шматок бекону."
  components:
  - type: Tag
    tags:
    - Raw
    - Meat
  - type: Sprite
    state: bacon
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 2
        - ReagentId: Fat
          Quantity: 9
  - type: InternalTemperature
    conductivity: 0.44
    thickness: 0.004 # bacon is thin so faster to cook than a steak
    area: 0.0075 # ~5x15cm
  - type: Construction
    graph: Bacon
    node: start
    defaultTarget: bacon

- type: entity
  name: "сире ведмеже м'ясо"
  parent: FoodMeatRawBase
  id: FoodMeatBear
  description: "Дуже мужній шматок сирого ведмежого м'яса."
  components:
  - type: Sprite
    state: bear
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 9
        - ReagentId: Fat
          Quantity: 9
  - type: SliceableFood
    count: 3
    slice: FoodMeatBearCutlet
  - type: Construction
    graph: BearSteak
    node: start
    defaultTarget: filet migrawr

- type: entity
  name: "сире м'ясо пінгвіна"
  parent: FoodMeatRawBase
  id: FoodMeatPenguin
  description: "Шматок сирого м'яса пінгвіна. Можна використовувати як замінник риби в рецептах."
  components:
  - type: Sprite
    state: bird
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 9
        - ReagentId: Fat
          Quantity: 9
  - type: SliceableFood
    count: 3
    slice: FoodMeatPenguinCutlet
  - type: Construction
    graph: PenguinSteak
    node: start
    defaultTarget: cooked penguin

- type: entity
  name: "сире куряче м'ясо"
  parent: FoodMeatRawBase
  id: FoodMeatChicken
  description: "Шматок сирої курки. Не забудьте вимити руки!"
  components:
  - type: Sprite
    state: bird
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 9
        - ReagentId: Fat
          Quantity: 9
  - type: SliceableFood
    count: 3
    slice: FoodMeatChickenCutlet
  - type: InternalTemperature
    conductivity: 0.41
  - type: Construction
    graph: ChickenSteak
    node: start
    defaultTarget: cooked chicken

- type: entity
  name: "сире качине м'ясо"
  parent: FoodMeatRawBase
  id: FoodMeatDuck
  description: "Шматок сирої качки. Не забудьте вимити руки!"
  components:
  - type: Sprite
    state: bird
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 9
        - ReagentId: Fat
          Quantity: 9
  - type: SliceableFood
    count: 3
    slice: FoodMeatDuckCutlet
  - type: Construction
    graph: DuckSteak
    node: start
    defaultTarget: cooked duck

- type: entity
  name: "м'ясо коргі вищого ґатунку"
  # can't rot since that would be very bad for syndies
  parent: FoodMeatBase
  id: FoodMeatCorgi
  description: "Заплямований дар злого злочину. М'ясо може бути смачним, але якою ціною?"
  components:
  - type: Tag
    tags:
    - Raw
    - HighRiskItem
    - Meat
  - type: Sprite
    state: corgi
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Bicaridine
          Quantity: 20
  - type: StaticPrice
    price: 750
  - type: StealTarget
    stealGroup: FoodMeatCorgi

- type: entity
  name: "сире крабове м'ясо"
  parent: FoodMeatRawBase
  id: FoodMeatCrab
  description: "Купа сирого крабового м'яса."
  components:
  - type: FlavorProfile
    flavors:
      - crabby
  - type: Sprite
    state: crab
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 1
        - ReagentId: Fat
          Quantity: 2
  - type: Construction
    graph: CrabSteak
    node: start
    defaultTarget: cooked crab

- type: entity
  name: "сире м'ясо голіафа"
  parent: FoodMeatRawBase
  id: FoodMeatGoliath
  description: "Шматок м'яса Голіафа. Зараз воно не дуже їстівне, але чудово готується в лаві."
  components:
  - type: Sprite
    state: goliath
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 5
        - ReagentId: Fat
          Quantity: 3
  - type: InternalTemperature
    thickness: 0.1 # very big, do cook it in lava
  - type: Construction
    graph: GoliathSteak
    node: start
    defaultTarget: goliath steak

- type: entity
  name: "драконяча плоть"
  parent: FoodMeatBase
  id: FoodMeatDragon
  description: "Щільне м'ясо верхівкового хижака космічної ери просякнуте міфічним іхором. За іронією долі, його найкраще їсти сирим."
  components:
  - type: Tag
    tags:
    - Raw
    - Meat
  - type: Sprite
    layers:
    - state: dragon
    - state: dragon_veins
      shader: unshaded
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Ichor
          Quantity: 10
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Ichor
        Quantity: 10

- type: entity
  name: "сире щуряче м'ясо"
  parent: FoodMeatRawBase
  id: FoodMeatRat
  description: "Першокласне м'ясо з технічного обслуговування!"
  components:
  - type: Sprite
    state: plain
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 3
        - ReagentId: Fat
          Quantity: 3
  - type: SliceableFood
    count: 3
    slice: FoodMeatCutlet

- type: entity
  name: "сире м'ясо ящірок"
  parent: FoodMeatRawBase
  id: FoodMeatLizard
  description: "Смачна шкода від динозаврів."
  components:
  - type: Sprite
    state: lizard
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 9
        - ReagentId: Fat
          Quantity: 9
  - type: SliceableFood
    count: 3
    slice: FoodMeatLizardCutlet
  - type: Construction
    graph: LizardSteak
    node: start
    defaultTarget: lizard steak

- type: entity
  name: "сире рослинне м'ясо"
  parent: FoodMeatBase
  id: FoodMeatPlant
  description: "Всі радощі здорового харчування з усіма принадами канібалізму."
  components:
  - type: Sprite
    state: plant

- type: entity
  name: "гниле м'ясо"
  parent: FoodMeatBase
  id: FoodMeatRotten
  description: "На півдорозі до того, щоб стати добривом для вашого саду."
  components:
  - type: Tag
    tags:
    - Raw
    - Meat
  - type: Sprite
    state: rotten
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 5
        - ReagentId: GastroToxin
          Quantity: 4
        - ReagentId: Fat
          Quantity: 4

- type: entity
  name: "сире м'ясо павуків"
  parent: FoodMeatRawBase
  id: FoodMeatSpider
  description: "Шматок павукового м'яса. Це так по-кафкіанськи."
  components:
  - type: Sprite
    state: spider
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 9
        - ReagentId: Fat
          Quantity: 9
  - type: SliceableFood
    count: 3
    slice: FoodMeatSpiderCutlet

- type: entity
  name: "сира павукова ніжка"
  parent: FoodMeatBase
  id: FoodMeatSpiderLeg
  description: "Ніжка гігантського павука, що все ще смикається... ви ж не хочете це їсти, чи не так?"
  components:
  - type: Sprite
    state: spiderleg
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 10
        - ReagentId: Fat
          Quantity: 3

- type: entity
  name: "грудочка м'ясної пшениці"
  parent: FoodMeatBase
  id: FoodMeatWheat
  description: "Це не схоже на м'ясо, але ваші стандарти не такі вже й високі."
  components:
  - type: FlavorProfile
    flavors:
      - falsemeat
  - type: Sprite
    state: clump
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 1

- type: entity
  name: "сире зміїне м'ясо"
  parent: FoodMeatBase
  id: FoodMeatSnake
  description: "Довгий шматок зміїного м'яса, сподіваюся, не отруйного."
  components:
  - type: Tag
    tags:
    - Raw
  - type: Sprite
    state: snake
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 10
        - ReagentId: Toxin
          Quantity: 2

- type: entity
  name: "сире м'ясо ксеносів"
  # not raw since acid kills bacteria or something, same as xeno
  parent: FoodMeatBase
  id: FoodMeatXeno
  description: "Шматок м'яса ксеноса, з якого капає кислота."
  components:
  - type: FlavorProfile
    flavors:
      - meaty
      - acid
  - type: Tag
    tags:
    - Raw
    - Meat
  - type: Sprite
    state: xeno
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: SulfuricAcid
          Quantity: 20
  - type: SliceableFood
    count: 3
    slice: FoodMeatXenoCutlet

- type: entity
  name: "сире рум'яне м'ясо"
  # not raw since rouny best
  parent: FoodMeatBase
  id: FoodMeatRouny
  description: "Шматок м'яса безневинного рудого друга."
  components:
  - type: FlavorProfile
    flavors:
    - meaty
    - acid
    - lostfriendship
  - type: Tag
    tags:
    - Raw
    - Meat
  - type: Sprite
    state: rouny
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: SulfuricAcid
          Quantity: 20
  - type: Construction
    graph: RounySteak
    node: start
    defaultTarget: rouny steak

- type: entity
  name: "м'ясо з томатів-убивць"
  parent: FoodMeatBase
  id: FoodMeatTomato
  description: "Шматочок від величезного помідора."
  components:
  - type: Sprite
    state: tomato
  - type: SliceableFood
    count: 3
    slice: FoodMeatTomatoCutlet
  - type: StaticPrice
    price: 100

- type: entity
  name: "салямі"
  parent: FoodMeatBase
  id: FoodMeatSalami
  description: "Великий шматок салямі. Краще не питати, що туди входило."
  components:
  - type: Tag
    tags:
    - Raw
    - Meat
  - type: Sprite
    sprite: Objects/Consumable/Food/salami.rsi
    state: salami
  - type: SliceableFood
    slice: FoodMeatSalamiSlice
  - type: MeleeWeapon
    wideAnimationRotation: 100
    damage:
      types:
        Blunt: 6
  - type: Wieldable
  - type: IncreaseDamageOnWield
    damage:
      types:
        Blunt: 2

- type: entity
  name: "м'ясний клоун"
  parent: FoodMeatBase
  id: FoodMeatClown
  description: "Смачний, круглий шматок м'яса клоуна. Який жах."
  components:
  - type: FlavorProfile
    flavors:
      - meaty
      - funny
  - type: Tag
    tags:
    - Raw
    - Meat
  - type: Sprite
    state: clown
  - type: SliceableFood
    slice: FoodMeatSalamiSlice

- type: entity
  name: "фрикаделька"
  parent: FoodMeatBase
  id: FoodMeatMeatball
  description: "Сирий шматок м'яса. М'ясна кулька."
  components:
  - type: Tag
    tags:
    - Raw
    - Meat
  - type: Sprite
    state: meatball

- type: entity
  name: "кулька зі слизу"
  parent: FoodMeatBase
  id: FoodMeatSlime
  description: "Желеподібна форма зі слизового желе."
  components:
  - type: FlavorProfile
    flavors:
      - slimy
  - type: Tag
    tags:
    - Raw
    - Meat
  - type: Sprite
    state: slime

- type: entity
  name: "сире м'ясо равликів"
  parent: FoodMeatRawBase
  id: FoodMeatSnail
  description: "Посолити."
  components:
  - type: Sprite
    state: snail
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 3
        - ReagentId: Fat
          Quantity: 3
        - ReagentId: Water
          Quantity: 4 #It makes saline if you add salt!

# Cooked

- type: entity
  parent: FoodBase
  id: MaterialSmileExtract
  name: "екстракт посміхунчика"
  description: "Це справжня панацея. Але якою ціною?"
  components:
  - type: Extractable
    grindableSolutionName: food
  - type: FlavorProfile
    flavors:
      - sweet
  - type: Sprite
    sprite: Mobs/Aliens/slimes.rsi
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: rainbow_slime_extract
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Omnizine
          Quantity: 30
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Iron
          Quantity: 10
  - type: StaticPrice
    price: 3000 #It has so much Omnizin in it
  - type: Tag
    tags:
    - Meat

- type: entity
  name: "стейк"
  parent: FoodMeatBase
  id: FoodMeatCooked
  description: "Приготований шматок м'яса. Пахне первісним."
  components:
  - type: Tag
    tags:
    - Cooked
    - Meat
    - Steak
  - type: Sprite
    layers:
    - state: plain-cooked
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Protein
          Quantity: 5
  - type: SliceableFood
    count: 3
    slice: FoodMeatCutletCooked
  - type: Construction
    graph: MeatSteak
    node: meat steak
  - type: FoodSequenceElement
    entries:
      Burger: MeatSteak
      Taco: MeatSteak

- type: entity
  name: "бекон"
  parent: FoodMeatBase
  id: FoodMeatBaconCooked
  description: "Смачний шматочок смаженого бекону."
  components:
  - type: Tag
    tags:
    - Cooked
    - Meat
  - type: Sprite
    layers:
    - state: bacon-cooked
      map: [ "enum.DamageStateVisualLayers.Base" ]
  - type: RandomSprite
    available:
      - enum.DamageStateVisualLayers.Base:
          bacon-cooked: ""
          bacon2-cooked: ""
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Protein
          Quantity: 5
  - type: Construction
    graph: Bacon
    node: bacon
  - type: FoodSequenceElement
    entries:
      Burger: MeatBecon
      Taco: MeatBecon

- type: entity
  name: "вареного ведмедя"
  parent: FoodMeatBase
  id: FoodMeatBearCooked
  description: "Добре просмажений шматок ведмежого м'яса. Жорсткий, але смачний з правильними боками."
  components:
  - type: Tag
    tags:
    - Cooked
    - Meat
    - Steak
  - type: Sprite
    layers:
    - state: product-cooked
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Protein
          Quantity: 5
  - type: SliceableFood
    count: 3
    slice: FoodMeatBearCutletCooked
  - type: Construction
    graph: BearSteak
    node: filet migrawr
  - type: FoodSequenceElement
    entries:
      Burger: MeatBearBurger
      Taco: MeatBear

- type: entity
  name: "філе пінгвіна"
  parent: FoodMeatBase
  id: FoodMeatPenguinCooked
  description: "Приготоване філе пінгвіна. Можна використовувати як замінник риби в рецептах."
  components:
  - type: Tag
    tags:
    - Cooked
    - Meat
  - type: Sprite
    layers:
    - state: bird-cooked
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Protein
          Quantity: 5
  - type: SliceableFood
    count: 3
    slice: FoodMeatPenguinCutletCooked
  - type: Construction
    graph: PenguinSteak
    node: cooked penguin
  - type: FoodSequenceElement
    entries:
      Burger: MeatPenguinBurger
      Taco: MeatPenguin

- type: entity
  name: "варена курка"
  parent: FoodMeatBase
  id: FoodMeatChickenCooked
  description: "Приготований шматок курки. Найкраще використовувати в інших рецептах."
  components:
  - type: Tag
    tags:
    - Cooked
    - Meat
  - type: Sprite
    layers:
    - state: bird-cooked
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Protein
          Quantity: 5
  - type: SliceableFood
    count: 3
    slice: FoodMeatChickenCutletCooked
  - type: Construction
    graph: ChickenSteak
    node: cooked chicken
  - type: FoodSequenceElement
    entries:
      Burger: MeatChicken
      Taco: MeatChicken

- type: entity
  name: "смажена курочка"
  parent: FoodMeatBase
  id: FoodMeatChickenFried
  description: "Соковитий шматок курячого м'яса, просмажений до ідеалу."
  components:
  - type: Tag
    tags:
    - Cooked
    - Meat
  - type: Sprite
    layers:
    - state: chicken-fried
      map: [ "enum.DamageStateVisualLayers.Base" ]
  - type: RandomSprite
    available:
      - enum.DamageStateVisualLayers.Base:
          chicken-fried: ""
          chicken2-fried: ""
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Protein
          Quantity: 5
  - type: FoodSequenceElement
    entries:
      Burger: MeatChicken
      Taco: MeatChicken

- type: entity
  name: "варена качка"
  parent: FoodMeatBase
  id: FoodMeatDuckCooked
  description: "Приготований шматок качки. Найкраще використовувати в інших рецептах."
  components:
  - type: Tag
    tags:
    - Cooked
    - Meat
  - type: Sprite
    layers:
    - state: bird-cooked
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Protein
          Quantity: 5
  - type: SliceableFood
    count: 3
    slice: FoodMeatDuckCutletCooked
  - type: Construction
    graph: DuckSteak
    node: cooked duck
  - type: FoodSequenceElement
    entries:
      Burger: MeatDuck
      Taco: MeatDuck

- type: entity
  name: "варений краб"
  parent: FoodMeatBase
  id: FoodMeatCrabCooked
  description: "Смачно приготоване крабове м'ясо."
  components:
  - type: FlavorProfile
    flavors:
      - crabby
  - type: Tag
    tags:
    - Cooked
    - Meat
  - type: Sprite
    layers:
    - state: crab-cooked
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Protein
          Quantity: 5
  - type: Construction
    graph: CrabSteak
    node: cooked crab
  - type: FoodSequenceElement
    entries:
      Burger: MeatCrabBurger
      Taco: MeatCrab

- type: entity
  name: "стейк голіафа"
  parent: FoodMeatBase
  id: FoodMeatGoliathCooked
  description: "Смачний стейк, приготований на лаві."
  components:
  - type: Tag
    tags:
    - Cooked
    - Meat
    - Steak
  - type: Sprite
    layers:
    - state: goliath-cooked
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Protein
          Quantity: 5
  - type: Construction
    graph: GoliathSteak
    node: goliath steak
  - type: FoodSequenceElement
    entries:
      Burger: MeatGoliathBurger
      Taco: MeatGoliath

- type: entity
  name: "рум'яний стейк"
  parent: FoodMeatBase
  id: FoodMeatRounyCooked
  description: "Дехто вбиває, щоб вижити. Ти ж, навпаки, вбиваєш заради задоволення."
  components:
  - type: FlavorProfile
    flavors:
    - meaty
    - lostfriendship
  - type: Tag
    tags:
    - Cooked
    - Meat
    - Steak
  - type: Sprite
    layers:
    - state: rouny-cooked
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Protein
          Quantity: 10
  - type: Construction
    graph: RounySteak
    node: rouny steak
  - type: FoodSequenceElement
    entries:
      Burger: MeatXeno
      Taco: MeatXeno

- type: entity
  name: "стейк ящірки"
  parent: FoodMeatBase
  id: FoodMeatLizardCooked
  description: "Приготоване, жорстке м'ясо ящірки."
  components:
  - type: Tag
    tags:
    - Cooked
    - Meat
    - Steak
  - type: Sprite
    layers:
    - state: lizard-cooked
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Protein
          Quantity: 5
  - type: SliceableFood
    count: 3
    slice: FoodMeatLizardCutletCooked
  - type: Construction
    graph: LizardSteak
    node: lizard steak
  - type: FoodSequenceElement
    entries:
      Burger: MeatLizardBurger
      Taco: MeatLizard

- type: entity
  name: "варена ніжка павука"
  parent: FoodMeatBase
  id: FoodMeatSpiderlegCooked
  description: "Гігантська павуча лапка, яка все ще смикається після варіння. Гидота!"
  components:
  - type: Tag
    tags:
    - Cooked
    - Meat
  - type: Sprite
    layers:
    - state: spiderleg-cooked
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Protein
          Quantity: 5
  - type: FoodSequenceElement
    entries:
      Burger: MeatSpiderBurger
      Taco: MeatSpider

- type: entity
  name: "фрикаделька"
  parent: FoodMeatBase
  id: FoodMeatMeatballCooked
  description: "Варена фрикаделька. Ідеально підходить для додавання до інших страв... окрім фруктових."
  components:
  - type: Tag
    tags:
    - Cooked
    - Meat
  - type: Sprite
    state: meatball-cooked
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Protein
          Quantity: 5
  - type: FoodSequenceElement
    entries:
      Burger: MeatBall
      Taco: MeatBall

- type: entity
  name: "варений равлик"
  parent: FoodMeatBase
  id: FoodMeatSnailCooked
  description: "Посолити."
  components:
  - type: Tag
    tags:
    - Cooked
    - Meat
  - type: Sprite
    layers:
    - state: snail-cooked
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
        - ReagentId: Protein
          Quantity: 3
        - ReagentId: Water
          Quantity: 4 # makes saline if you add salt!
  - type: FoodSequenceElement
    entries:
      Burger: MeatSnail
      Taco: MeatSnail

# Cutlets

# Raw

- type: entity
  name: "сира котлета"
  parent: FoodMeatBase
  id: FoodMeatCutlet
  description: "Котлета з сирого м'яса."
  components:
  - type: Tag
    tags:
    - Raw
    - Cutlet
    - Meat
  - type: Sprite
    state: cutlet
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 3
        - ReagentId: Fat
          Quantity: 2
  - type: Construction
    graph: Cutlet
    node: start
    defaultTarget: cutlet

- type: entity
  name: "сира ведмежа котлета"
  parent: FoodMeatBase
  id: FoodMeatBearCutlet
  description: "Дуже мужня котлета з сирого ведмежого м'яса."
  components:
  - type: Tag
    tags:
    - Raw
    - Cutlet
    - Meat
  - type: Sprite
    layers:
    - state: cutlet
    - state: cutlet-alpha
      color: brown
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 3
        - ReagentId: Fat
          Quantity: 2
  - type: Construction
    graph: BearCutlet
    node: start
    defaultTarget: bear cutlet

- type: entity
  name: "сира пінгвіняча котлета"
  parent: FoodMeatBase
  id: FoodMeatPenguinCutlet
  description: "Котлета з сирого м'яса пінгвіна. Можна використовувати як замінник риби в рецептах."
  components:
  - type: Tag
    tags:
    - Raw
    - Cutlet
    - Meat
  - type: Sprite
    state: cutlet
    color: white
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 3
        - ReagentId: Fat
          Quantity: 2
  - type: Construction
    graph: PenguinCutlet
    node: start
    defaultTarget: penguin cutlet

- type: entity
  name: "сира куряча котлета"
  parent: FoodMeatBase
  id: FoodMeatChickenCutlet
  description: "Котлета з сирого курячого м'яса. Не забудьте вимити руки!"
  components:
  - type: Tag
    tags:
    - Raw
    - Cutlet
    - Meat
  - type: Sprite
    state: cutlet
    color: white
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 3
        - ReagentId: Fat
          Quantity: 2
  - type: Construction
    graph: ChickenCutlet
    node: start
    defaultTarget: chicken cutlet

- type: entity
  name: "сира качина котлета"
  parent: FoodMeatBase
  id: FoodMeatDuckCutlet
  description: "Котлета з сирої качки. Не забудьте вимити руки!"
  components:
  - type: Tag
    tags:
    - Raw
    - Cutlet
    - Meat
  - type: Sprite
    state: cutlet
    color: white
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 3
        - ReagentId: Fat
          Quantity: 2
  - type: Construction
    graph: DuckCutlet
    node: start
    defaultTarget: duck cutlet

- type: entity
  name: "сира котлета з ящірки"
  parent: FoodMeatBase
  id: FoodMeatLizardCutlet
  description: "Смачна котлета з динозавра."
  components:
  - type: Tag
    tags:
    - Raw
    - Cutlet
    - Meat
  - type: Sprite
    layers:
    - state: cutlet
      color: green
    - state: cutlet-alpha
      color: pink
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 3
        - ReagentId: Fat
          Quantity: 2
  - type: Construction
    graph: LizardCutlet
    node: start
    defaultTarget: lizard cutlet

- type: entity
  name: "сира павукова котлета"
  parent: FoodMeatBase
  id: FoodMeatSpiderCutlet
  description: "Котлета з сирого павукового м'яса. Так по-кафкіанськи."
  components:
  - type: Tag
    tags:
    - Raw
    - Cutlet
    - Meat
  - type: Sprite
    state: spidercutlet
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 3
        - ReagentId: Fat
          Quantity: 2
  - type: Construction
    graph: SpiderCutlet
    node: start
    defaultTarget: spider cutlet

- type: entity
  name: "сира ксенокотлета"
  parent: FoodMeatBase
  id: FoodMeatXenoCutlet
  description: "Шматок сирого м'яса ксеноса, з якого капає кислота."
  components:
  - type: FlavorProfile
    flavors:
      - meaty
      - acid
  - type: Tag
    tags:
    - Raw
    - Cutlet
    - Meat
  - type: Sprite
    state: xenocutlet
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: SulfuricAcid
          Quantity: 20
  - type: Construction
    graph: XenoCutlet
    node: start
    defaultTarget: xeno cutlet

- type: entity
  name: "сира котлета з помідорами-вбивцями"
  parent: FoodMeatBase
  id: FoodMeatTomatoCutlet
  description: "Котлету з пластинки помідора."
  components:
  - type: Tag
    tags:
    - Cutlet
    - Meat
  - type: Sprite
    state: salami-slice
    color: red
  - type: StaticPrice
    price: 30

- type: entity
  name: "скибочка салямі"
  parent: FoodMeatBase
  id: FoodMeatSalamiSlice
  description: "Шматочок в'яленої салямі."
  components:
  - type: Tag
    tags:
    - Raw
    - Cutlet
    - Meat
  - type: Sprite
    state: salami-slice
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 1
        - ReagentId: Protein
          Quantity: 1

# Cooked

- type: entity
  name: "котлета"
  parent: FoodMeatBase
  id: FoodMeatCutletCooked
  description: "Приготована м'ясна котлета. Треба трохи приправити."
  components:
  - type: Tag
    tags:
    - Cooked
    - Cutlet
    - Meat
  - type: Sprite
    state: cutlet-cooked
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 2
        - ReagentId: Protein
          Quantity: 2
  - type: Construction
    graph: Cutlet
    node: cutlet
  - type: FoodSequenceElement
    entries:
      Burger: MeatCutlet
      Taco: MeatCutlet

- type: entity
  name: "ведмежа котлета"
  parent: FoodMeatBase
  id: FoodMeatBearCutletCooked
  description: "Дуже чоловіча котлета з вареного ведмежого м'яса."
  components:
  - type: Tag
    tags:
    - Cooked
    - Cutlet
    - Meat
  - type: Sprite
    layers:
    - state: cutlet-cooked
    - state: cutlet-alpha
      color: brown
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 2
        - ReagentId: Protein
          Quantity: 2
  - type: Construction
    graph: BearCutlet
    node: bear cutlet
  - type: FoodSequenceElement
    entries:
      Burger: BearCutletBurger
      Taco: BearCutlet

- type: entity
  name: "котлета з пінгвіна"
  parent: FoodMeatBase
  id: FoodMeatPenguinCutletCooked
  description: "Котлета з вареного м'яса пінгвіна."
  components:
  - type: Tag
    tags:
    - Cooked
    - Cutlet
    - Meat
  - type: Sprite
    state: cutlet-cooked
    color: white
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 2
        - ReagentId: Protein
          Quantity: 2
  - type: Construction
    graph: PenguinCutlet
    node: penguin cutlet
  - type: FoodSequenceElement
    entries:
      Burger: PenguinCutletBurger
      Taco: PenguinCutlet

- type: entity
  name: "куряча котлета"
  parent: FoodMeatBase
  id: FoodMeatChickenCutletCooked
  description: "Котлета з вареного курячого філе. Не забудьте вимити руки!"
  components:
  - type: Tag
    tags:
    - Cooked
    - Cutlet
    - Meat
  - type: Sprite
    state: cutlet-cooked
    color: white
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 2
        - ReagentId: Protein
          Quantity: 2
  - type: Construction
    graph: ChickenCutlet
    node: chicken cutlet
  - type: FoodSequenceElement
    entries:
      Burger: ChickenCutlet
      Taco: ChickenCutlet

- type: entity
  name: "качина котлета"
  parent: FoodMeatBase
  id: FoodMeatDuckCutletCooked
  description: "Котлета з вареної качки. Не забудьте вимити руки!"
  components:
  - type: Tag
    tags:
    - Cooked
    - Cutlet
    - Meat
  - type: Sprite
    state: cutlet-cooked
    color: white
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 2
        - ReagentId: Protein
          Quantity: 2
  - type: Construction
    graph: DuckCutlet
    node: duck cutlet
  - type: FoodSequenceElement
    entries:
      Burger: DuckCutlet
      Taco: DuckCutlet

- type: entity
  name: "котлета з ящірки"
  parent: FoodMeatBase
  id: FoodMeatLizardCutletCooked
  description: "Смачно приготована котлета \"Діно\"."
  components:
  - type: Tag
    tags:
    - Cooked
    - Cutlet
    - Meat
  - type: Sprite
    layers:
    - state: cutlet-cooked
      color: green
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 2
        - ReagentId: Protein
          Quantity: 2
  - type: Construction
    graph: LizardCutlet
    node: lizard cutlet
  - type: FoodSequenceElement
    entries:
      Burger: LizardCutletBurger
      Taco: LizardCutlet

- type: entity
  name: "павукова котлета"
  parent: FoodMeatBase
  id: FoodMeatSpiderCutletCooked
  description: "Котлета з вареного павукового м'яса. Нарешті їстівна."
  components:
  - type: Tag
    tags:
    - Cooked
    - Cutlet
    - Meat
  - type: Sprite
    state: spidercutlet-cooked
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 1
        - ReagentId: Protein
          Quantity: 1
  - type: Construction
    graph: SpiderCutlet
    node: spider cutlet
  - type: FoodSequenceElement
    entries:
      Burger: SpiderCutletBurger
      Taco: SpiderCutlet

- type: entity
  name: "ксенокотлета"
  parent: FoodMeatBase
  id: FoodMeatXenoCutletCooked
  description: "Котлета з вареного ксеноса, що стікає... смакотою?"
  components:
  - type: Tag
    tags:
    - Cooked
    - Cutlet
    - Meat
  - type: Sprite
    state: xenocutlet-cooked
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 1
        - ReagentId: Protein
          Quantity: 1
  - type: Construction
    graph: XenoCutlet
    node: xeno cutlet
  - type: FoodSequenceElement
    entries:
      Burger: XenoCutlet
      Taco: XenoCutlet