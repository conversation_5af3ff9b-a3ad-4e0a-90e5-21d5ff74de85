- type: entity
  id: BaseMagazineGrenade
  name: "гранатометний постріл"
  parent: BaseItem
  abstract: true
  components:
  - type: Tag
    tags:
    - MagazineGrenade
  - type: BallisticAmmoProvider
    mayTransfer: true
    whitelist:
      tags:
        - Grenade
    capacity: 5
    soundRack:
      path: /Audio/Weapons/Guns/Bolt/lmg_bolt_closed.ogg
      params:
        variation: 0.05
    soundInsert:
      path: /Audio/Weapons/Guns/MagIn/rifle_load.ogg
      params:
        variation: 0.05
  - type: Item
    size: Large
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Magazine/Grenade/grenade_cartridge.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-4
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 5
    zeroVisible: false
  - type: Appearance

- type: entity
  id: MagazineGrenadeEmpty
  name: "гранатометний постріл"
  parent: BaseMagazineGrenade
  components:
  - type: BallisticAmmoProvider
    
- type: entity
  id: MagazineGrenadeFrag
  name: "осколковий гранатометний патрон"
  parent: BaseMagazineGrenade
  components:
  - type: BallisticAmmoProvider
    proto: GrenadeFrag

- type: entity
  id: MagazineGrenadeEMP
  name: "Патрон до ЕМР-гранати"
  parent: BaseMagazineGrenade
  components:
  - type: BallisticAmmoProvider
    proto: GrenadeEMP

- type: entity
  id: MagazineGrenadeFlash
  name: "патрон для світлошумової гранати"
  parent: BaseMagazineGrenade
  components:
  - type: BallisticAmmoProvider
    proto: GrenadeFlash

- type: entity
  id: MagazineGrenadeBlast
  name: "патрон до бойової гранати"
  parent: BaseMagazineGrenade
  components:
  - type: BallisticAmmoProvider
    proto: GrenadeBlast

- type: entity
  id: MagazineGrenadeBaton
  name: "патрон до кийкової гранати"
  parent: BaseMagazineGrenade
  components:
  - type: BallisticAmmoProvider
    proto: GrenadeBaton