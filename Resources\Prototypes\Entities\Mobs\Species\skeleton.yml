- type: entity
  save: false
  name: "Уріст МакСкеллі"
  parent:
  - MobFlammable
  - BaseMobSpecies
  id: BaseMobSkeletonPerson
  abstract: true
  components:
  - type: HumanoidAppearance
    species: Skeleton
  - type: Carriable # Carrying system from nyanotrasen.
  - type: Icon
    sprite: Mobs/Species/Skeleton/parts.rsi
    state: full
  - type: Body
    prototype: Skeleton
    requiredLegs: 2
    gibSound: /Audio/Effects/bone_rattle.ogg
  - type: Damageable
    damageContainer: Biological
    damageModifierSet: Skeleton
  - type: DamageVisuals
    damageOverlayGroups:
      Brute:
        sprite: Mobs/Effects/brute_damage.rsi
        color: "#555555AA"
      Burn:
        sprite: Mobs/Effects/burn_damage.rsi
  - type: MobState
  - type: MobThresholds
    thresholds:
      0: Alive
      100: Critical
      150: Dead
  - type: TransferMindOnGib
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:GibBehavior
        gibContents: Skip
  - type: SlowOnDamage #modified speeds because they're so weak
    speedModifierThresholds:
      60: 0.9
      80: 0.7
  - type: Speech
    speechVerb: Skeleton
  - type: ZombieImmune
  - type: Vocal
    sounds:
      Male: Skeleton
      Female: Skeleton
      Unsexed: Skeleton
  - type: SkeletonAccent
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 130
        restitution: 0.0
        mask:
          - MobMask
        layer:
          - MobLayer
  - type: Reactive
    groups:
      Flammable: [ Touch ]
      Extinguish: [ Touch ]
    reactions:
    - reagents: [ Water, SpaceCleaner ]
      methods: [ Touch ]
      effects:
      - !type:WashCreamPieReaction
    - reagents: [ Milk, MilkGoat, MilkSoy, MilkSpoiled ]
      # add new types of milk to reagents as they appear, oat milk isn't on the list
      # because turns out oat milk has 1/30th the amount of calcium in it compared to the rest
      # even if it's a meme - I did research
      methods: [ Touch ]
      effects: # TODO: when magic is around - make a milk transformation to a skeleton monster
      - !type:HealthChange
        scaleByQuantity: true
        damage:
          groups:
            Burn: -2 # healing obviously up to discussion
            Brute: -1.5 # these groups are the only 2 possible ways to damage a skeleton
      - !type:PopupMessage
        type: Local
        visualType: Large
        messages: [ "skeleton-healed-by-milk-popup" ]
        probability: 0.25
    - reagents: [ MilkOat ]
      methods: [ Touch ]
      effects:
        - !type:PopupMessage
          type: Local
          visualType: Large
          messages: [ "skeleton-sprayed-by-oat-milk-popup" ]
          probability: 0.5
  - type: FireVisuals
    alternateState: Standing
  - type: TypingIndicator
    proto: skeleton
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    - Calcic
    understands:
    - TauCetiBasic
    - Calcic
  - type: FootPrints
  - type: LayingDown
  - type: Tag # Goobstation
    tags:
    - CanPilot
    - FootstepSound
    - DoorBumpOpener
    - AnomalyHost
    - Undead

- type: entity
  parent: BaseSpeciesDummy
  id: MobSkeletonPersonDummy
  categories: [ HideSpawnMenu ]
  components:
    - type: HumanoidAppearance
      species: Skeleton
