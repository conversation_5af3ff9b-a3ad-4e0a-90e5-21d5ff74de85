﻿- type: constructionGraph
  id: SolarPanel
  start: start
  graph:
    - node: start
      edges:
        - to: solarassembly
          steps:
            - tag: SolarAssemblyFlatpack
              name: Solar Assembly Parts
              icon:
                sprite: Objects/Devices/flatpack.rsi
                state: solar-assembly-part
              doAfter: 1

    - node: solarassembly
      entity: SolarAssembly
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: SolarAssemblyFlatpack
              amount: 1
            - !type:DeleteEntity {}
          steps:
            - tool: Screwing
              doAfter: 2
        - to: solarpanelplasma
          conditions:
            - !type:EntityAnchored
          steps:
            - material: PlasmaGlass
              amount: 2
              doAfter: 0.5
          completed:
            - !type:SnapToGrid
        - to: solarpaneluranium
          conditions:
            - !type:EntityAnchored
          steps:
            - material: UraniumGlass
              amount: 2
              doAfter: 0.5
          completed:
            - !type:SnapToGrid
        - to: solarpanel
          conditions:
            - !type:EntityAnchored
          steps:
            - material: Glass
              amount: 2
              doAfter: 0.5
          completed:
            - !type:SnapToGrid
        - to: solartracker
          conditions:
            - !type:EntityAnchored
          steps:
            - tag: SolarTrackerElectronics
              name: Solar Tracker Electronics
              icon:
                sprite: Objects/Misc/module.rsi
                state: engineering
              doAfter: 1
            - material: Glass
              amount: 2
              doAfter: 2
          completed:
            - !type:SnapToGrid

    - node: solarpanelplasma
      entity: SolarPanelPlasma
      edges:
        - to: solarassembly
          completed:
            - !type:SnapToGrid
              southRotation: true
            - !type:SpawnPrototype
              prototype: SheetPGlass1
              amount: 2
          conditions:
            - !type:EntityAnchored
              anchored: true
          steps:
            - tool: Prying
              doAfter: 0.5

    - node: solarpaneluranium
      entity: SolarPanelUranium
      edges:
        - to: solarassembly
          completed:
            - !type:SnapToGrid
              southRotation: true
            - !type:SpawnPrototype
              prototype: SheetUGlass1
              amount: 2
          conditions:
            - !type:EntityAnchored
              anchored: true
          steps:
            - tool: Prying
              doAfter: 0.5

    - node: solarpanel
      entity: SolarPanel
      edges:
        - to: solarassembly
          completed:
            - !type:SnapToGrid
              southRotation: true
            - !type:SpawnPrototype
              prototype: SheetGlass1
              amount: 2
          conditions:
            - !type:EntityAnchored
              anchored: true
          steps:
            - tool: Prying
              doAfter: 0.5

    - node: solarpanel_broken
      entity: SolarPanelBroken
      edges:
        - to: solarassembly
          completed:
            - !type:SnapToGrid
              southRotation: true
            - !type:SpawnPrototype
              prototype: ShardGlass
              amount: 2
          conditions:
            - !type:EntityAnchored
              anchored: true
          steps:
            - tool: Prying
              doAfter: 0.5

    - node: solarpanelplasma_broken
      entity: SolarPanelPlasmaBroken
      edges:
      - to: solarassembly
        completed:
        - !type:SnapToGrid
          southRotation: true
        - !type:SpawnPrototype
          prototype: ShardGlassPlasma
          amount: 2
        conditions:
        - !type:EntityAnchored
          anchored: true
        steps:
        - tool: Prying
          doAfter: 0.5

    - node: solarpaneluranium_broken
      entity: SolarPanelUraniumBroken
      edges:
      - to: solarassembly
        completed:
        - !type:SnapToGrid
          southRotation: true
        - !type:SpawnPrototype
          prototype: ShardGlassUranium
          amount: 2
        conditions:
        - !type:EntityAnchored
          anchored: true
        steps:
        - tool: Prying
          doAfter: 0.5

    - node: solartracker
      entity: SolarTracker
      edges:
        - to: solarassembly
          completed:
            - !type:SnapToGrid
              southRotation: true
            - !type:SpawnPrototype
              prototype: SheetGlass1
              amount: 2
            - !type:SpawnPrototype
              prototype: SolarTrackerElectronics
              amount: 1
          conditions:
            - !type:EntityAnchored
              anchored: true
          steps:
            - tool: Prying
              doAfter: 1
