- type: entity
  parent: BaseMachinePowered
  id: FaxMachineBase
  name: "факс дальнього зв'язку"
  description: "Технології Bluespace про застосування бюрократії."
  components:
  - type: Sprite
    sprite: Structures/Machines/fax_machine.rsi
    drawdepth: SmallObjects
    layers:
    - state: icon
      map: [ "enum.FaxMachineVisuals.VisualState" ]
  - type: Icon
    sprite: Structures/Machines/fax_machine.rsi
    state: icon
  - type: Appearance
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 25
        mask:
        - TabletopMachineMask
        layer:
        - TabletopMachineLayer
  - type: ActivatableUI
    key: enum.FaxUiKey.Key
  - type: ActivatableUIRequiresPower
  - type: UserInterface
    interfaces:
      enum.FaxUiKey.Key:
        type: FaxBoundUi
  - type: ApcPowerReceiver
    powerLoad: 250
  - type: Faxecute
    damage:
      types:
        Blunt: 100
  - type: FaxMachine
    receiveStationGoal: true #PIRATE StationGoal
    paperSlot:
      insertSound: /Audio/Machines/scanning.ogg
      ejectSound: /Audio/Machines/tray_eject.ogg
      whitelist:
        components:
          - FaxableObject
      blacklist:
        tags:
          - PaperSlip
  - type: GenericVisualizer
    visuals:
      enum.PowerDeviceVisuals.Powered:
        enum.FaxMachineVisuals.VisualState:
          True: { state: idle }
          False: { state: icon }
      enum.FaxMachineVisuals.VisualState:
        enum.FaxMachineVisuals.VisualState:
          Printing: { state: printing }
          Normal: {state: idle}
  - type: ItemSlots
  - type: ContainerContainer
    containers:
      Paper: !type:ContainerSlot
  - type: DeviceNetworkRequiresPower
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: Fax
    transmitFrequencyId: Fax
  - type: RequireProjectileTarget

# Special
- type: entity
  parent: FaxMachineBase
  id: FaxMachineCentcom
  name: "факс великої дальності CentCom"
  suffix: CentCom
  components:
  - type: Sprite
    layers:
      - state: icon
        map: [ "base" ]
        color: "#bfe3ff"
  - type: FaxMachine
    name: "Central Command"
    notifyAdmins: true

- type: entity
  parent: FaxMachineBase
  id: FaxMachineSyndie
  name: "синдикатський факс дальнього зв'язку"
  suffix: Syndicate
  components:
  - type: Sprite
    layers:
      - state: icon
        map: [ "base" ]
        color: "#a3a3a3"
  - type: FaxMachine
    name: "ERR*?*%!"
    responsePings: false
    notifyAdmins: true
  - type: Emagged

- type: entity
  parent: FaxMachineBase
  id: FaxMachineCaptain
  name: "капітанський факс дальнього зв'язку"
  suffix: NukeCodes
  components:
    - type: FaxMachine
      name: "Captain's Office"
      receiveNukeCodes: true
      receiveStationGoal: true
    - type: StealTarget
      stealGroup: FaxMachineCaptain
