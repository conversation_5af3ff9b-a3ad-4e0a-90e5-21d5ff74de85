- type: entity
  name: "основний"
  id: MobSpaceBasic
  parent: SimpleSpaceMobBase
  abstract: true
  description: "Виглядає дружелюбно. Чому б тобі не обійняти його?"
  components:
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
  - type: NpcFactionMember
    factions:
    - SimpleHostile
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Animals/bear.rsi
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: bear
    - map: [ "enum.DamageStateVisualLayers.BaseUnshaded" ]
      state: glow
      shader: unshaded
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.40
        density: 100
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: MobThresholds
    thresholds:
      0: Alive
      100: Dead
  - type: Stamina
    critThreshold: 150
  - type: MovementAlwaysTouching
  - type: Bloodstream
    bloodMaxVolume: 300
    bloodReagent: Cryoxadone
  - type: CombatMode
  - type: Temperature
    heatDamageThreshold: 500
    coldDamageThreshold: 0
  - type: MeleeWeapon
    soundHit:
        path: /Audio/Weapons/Xeno/alien_claw_flesh3.ogg
    angle: 0
    animation: WeaponArcClaw
    damage:
      types:
        Blunt: 5
        Slash: 10
  - type: Tag
    tags:
    - DoorBumpOpener
    - FootstepSound
  - type: Puller
    needsHands: false
  - type: PointLight
    radius: 1.2
    energy: 2
    color: "#4faffb"
  - type: GhostTakeoverAvailable
  - type: Speech
    speechVerb: LargeMob
  - type: Fauna # Lavaland Change

- type: entity
  name: "космічний ведмідь"
  id: MobBearSpace
  parent: MobSpaceBasic
  description: "Виглядає дружелюбно. Чому б тобі не обійняти його?"
  components:
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Animals/bear.rsi
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: bear
    - map: [ "enum.DamageStateVisualLayers.BaseUnshaded" ]
      state: glow
      shader: unshaded
  - type: DamageStateVisuals
    states:
      Alive:
        Base: bear
        BaseUnshaded: glow
      Critical:
        Base: bear_dead
      Dead:
        Base: bear_dead
  - type: Butcherable
    spawned:
    - id: FoodMeatBear
      amount: 3
    - id: MaterialHideBear
      amount: 1
      prob: 0.3
  - type: ReplacementAccent
    accent: genericAggressive
  - type: Vocal
    sounds:
      Unsexed: Bear
  - type: InteractionPopup
    successChance: 0.25
    interactSuccessString: petting-success-bear
    interactFailureString: petting-failure-bear
    interactSuccessSound:
      path: /Audio/Animals/sloth_squeak.ogg

- type: entity
  id: MobBearSpaceSalvage
  parent: MobBearSpace
  suffix: "Salvage Ruleset"
  components:
  - type: SalvageMobRestrictions

- type: entity
  name: "космічний кенгуру"
  id: MobKangarooSpace
  parent: MobSpaceBasic
  description: "Виглядає дружелюбно. Чому б тобі не обійняти його?"
  components:
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Animals/kangaroo.rsi
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: kangaroo-space
    - map: [ "enum.DamageStateVisualLayers.BaseUnshaded" ]
      state: glow
      shader: unshaded
  - type: MeleeWeapon
    soundHit:
        path: /Audio/Weapons/Xeno/alien_claw_flesh3.ogg
    angle: 0
    animation: WeaponArcClaw
    damage:
      types:
        Blunt: 9
        Slash: 2
  - type: MovementSpeedModifier
    baseWalkSpeed: 3.5
    baseSprintSpeed: 4.5
  - type: DamageStateVisuals
    states:
      Alive:
        Base: kangaroo-space
        BaseUnshaded: glow
      Critical:
        Base: kangaroo-space-crit
      Dead:
        Base: kangaroo-space-dead
  - type: Stamina
    critThreshold: 180
  - type: Inventory
    speciesId: kangaroo
    templateId: spacekangaroo
  - type: Vocal
    sounds:
      Unsexed: Kangaroo
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepBounce
  - type: LanguageKnowledge
    speaks:
    - Kangaroo
    understands:
    - Kangaroo
  - type: InventorySlots
  - type: Strippable
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface

- type: entity
  id: MobKangarooSpaceSalvage
  parent: MobKangarooSpace
  suffix: "Salvage Ruleset"
  components:
  - type: SalvageMobRestrictions

- type: entity
  name: "космічний павук"
  id: MobSpiderSpace
  parent: MobSpaceBasic
  description: "Він так світиться, що виглядає небезпечно."
  components:
  - type: VentCrawler # goobstation - Ventcrawl
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Animals/spacespider.rsi
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: spacespider
    - map: [ "enum.DamageStateVisualLayers.BaseUnshaded" ]
      state: glow
      shader: unshaded
  - type: MobThresholds
    thresholds:
      0: Alive
      90: Dead
  - type: Stamina
    critThreshold: 150
  - type: DamageStateVisuals
    states:
      Alive:
        Base: spacespider
        BaseUnshaded: glow
      Dead:
        Base: spacespider_dead
  - type: Butcherable
    spawned:
    - id: FoodMeatSpider
      amount: 2
    - id: EggSpider
      amount: 1
      prob: 0.5
  - type: Bloodstream
    bloodMaxVolume: 250
    bloodReagent: Cryoxadone
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 120
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: MeleeWeapon
    soundHit:
      path: /Audio/Effects/bite.ogg
    angle: 0
    animation: WeaponArcBite
    damage:
      types:
        Piercing: 6
        Poison: 4
  - type: SolutionContainerManager
    solutions:
      melee:
        reagents:
        - ReagentId: ChloralHydrate
          Quantity: 80
  - type: MeleeChemicalInjector
    solution: melee
    transferAmount: 4
  - type: LanguageKnowledge
    speaks:
    - Xeno
    understands:
    - Xeno
  - type: InteractionPopup
    successChance: 0.20
    interactSuccessString: petting-success-tarantula
    interactFailureString: petting-failure-generic
  - type: PointLight
    radius: 1.2
    energy: 2
    color: "#4faffb"
  - type: NoSlip
  - type: IgnoreSpiderWeb
  - type: Speech
    speechVerb: Arachnid
    speechSounds: Arachnid
    allowedEmotes: ['Click', 'Chitter']
  - type: Vocal
    sounds:
      Male: UnisexArachnid
      Female: UnisexArachnid
      Unsexed: UnisexArachnid
  - type: TypingIndicator
    proto: spider
  - type: BloodSucker
    webRequired: true
  - type: Cocooner

- type: entity
  id: MobSpiderSpaceSalvage
  parent: MobSpiderSpace
  suffix: "Salvage Ruleset"
  components:
  - type: SalvageMobRestrictions

- type: entity
  name: "космічна кобра"
  id: MobCobraSpace
  parent: MobSpaceBasic
  description: "Довгі ікла та сяючий капюшон, а звабливий погляд так і просить підійти ближче."
  components:
    - type: Body # Shitmed Change
      prototype: SpaceCobra
    - type: Sprite
      drawdepth: Mobs
      sprite: Mobs/Animals/spacecobra.rsi
      layers:
        - map: [ "enum.DamageStateVisualLayers.Base" ]
          state: spacecobra
        - map: [ "enum.DamageStateVisualLayers.BaseUnshaded" ]
          state: glow
          shader: unshaded
    - type: FootstepModifier
      footstepSoundCollection:
        collection: FootstepSnake
    - type: MobThresholds
      thresholds:
        0: Alive
        100: Dead
    - type: Stamina
      critThreshold: 150
    - type: DamageStateVisuals
      states:
        Alive:
          Base: spacecobra
          BaseUnshaded: glow
        Dead:
          Base: dead_spacecobra
    - type: Butcherable
      spawned:
        - id: FoodMeatSnake
          amount: 2
        - id: UraniumOre1
          amount: 1
        - id: ClothingShoesSnakeskinBoots
          amount: 1
          prob: 0.3
    - type: Bloodstream
      bloodMaxVolume: 200
      bloodReagent: Cryoxadone
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeCircle
            radius: 0.40
          density: 120
          mask:
          - MobMask
          layer:
          - MobLayer
    - type: MeleeWeapon
      hidden: true
      soundHit:
        path: /Audio/Effects/bite.ogg
      angle: 0
      animation: WeaponArcBite
      damage:
        types:
          Piercing: 6
          Poison: 4
    - type: SolutionContainerManager
      solutions:
        melee:
          reagents:
          - ReagentId: NorepinephricAcid
            Quantity: 90
    - type: MeleeChemicalInjector
      solution: melee
      transferAmount: 6
    - type: LanguageKnowledge
      speaks:
      - Xeno
      understands:
      - Xeno
    - type: InteractionPopup
      successChance: 0.2
      interactSuccessString: petting-success-snake
      interactFailureString: petting-failure-generic
    - type: PointLight
      radius: 1.1
      energy: 1.5
      color: "#4faffb"

- type: entity
  id: MobCobraSpaceSalvage
  parent: MobCobraSpace
  suffix: "Salvage Ruleset"
  components:
    - type: SalvageMobRestrictions

- type: entity
  parent: SimpleSpaceMobBase
  id: MobSnail
  name: "равлик"
  description: "Огидно, якщо ти не француз."
  components:
  - type: Body
    prototype: Mouse
  - type: GhostRole
    makeSentient: true
    allowSpeech: false
    allowMovement: true
    name: ghost-role-information-snail-name
    description: ghost-role-information-snail-description
    rules: ghost-role-information-freeagent-rules
    mindRoles:
    - MindRoleGhostRoleFreeAgent
  - type: GhostTakeoverAvailable
  - type: Sprite
    drawdepth: SmallMobs
    sprite: Mobs/Animals/snail.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: snail
  - type: Item
    size: Tiny
  - type: NpcFactionMember
    factions:
      - Mouse
  - type: HTN
    rootTask:
      task: MouseCompound
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.2
        density: 100
        mask:
        - SmallMobMask
        layer:
        - SmallMobLayer
  - type: MobState
  - type: Deathgasp
  - type: MobStateActions
    actions:
      Critical:
      - ActionCritSuccumb
      - ActionCritFakeDeath
      - ActionCritLastWords
  - type: MobThresholds
    thresholds:
      0: Alive
      10: Critical
      20: Dead
  - type: MovementSpeedModifier
    baseWalkSpeed : 2
    baseSprintSpeed : 3
  - type: DamageStateVisuals
    states:
      Alive:
        Base: snail
      Critical:
        Base: dead
      Dead:
        Base: dead
  - type: Food
  - type: Thirst
    startingThirst: 25  # spawn with Okay thirst state
    thresholds:
      OverHydrated: 35
      Okay: 25
      Thirsty: 15
      Parched: 10
      Dead: 0
    baseDecayRate: 0.04
  - type: Hunger
    currentHunger: 25   # spawn with Okay hunger state
    thresholds:
      Overfed: 35
      Okay: 25
      Peckish: 15
      Starving: 10
      Dead: 0
    baseDecayRate: 0.1
  - type: Extractable
    grindableSolutionName: food
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 3
  - type: Butcherable
    spawned:
    - id: FoodMeatSnail
      amount: 1
  - type: Tag
    tags:
    - Trash
    - VimPilot
    - ChefPilot
    - Meat
  - type: CombatMode
    combatToggleAction: ActionCombatModeToggleOff
  - type: Bloodstream
    bloodMaxVolume: 30
    bloodReagent: Cryoxadone
  - type: CanEscapeInventory
  - type: MobPrice
    price: 50
  - type: BadFood
  - type: NonSpreaderZombie
  - type: FireVisuals
    sprite: Mobs/Effects/onfire.rsi
    normalState: Mouse_burning
  - type: Temperature
    heatDamageThreshold: 500
    coldDamageThreshold: 0
  - type: Reactive
    reactions:
    - reagents: [TableSalt, Saline]
      methods: [Touch, Ingestion, Injection]
      effects:
      - !type:HealthChange
        scaleByQuantity: true
        damage:
          types:
            Caustic: 1
      - !type:PopupMessage
        type: Local
        visualType: Large
        messages: [ "snail-hurt-by-salt-popup" ]
        probability: 0.66

- type: entity
  parent: MobSnail
  id: MobSnailInstantDeath
  suffix: Smite
  components:
  - type: MobStateActions
    actions:
      Alive:
      - ActionSmite
      Critical:
      - ActionCritSuccumb
      - ActionCritFakeDeath
      - ActionCritLastWords
  - type: Godmode
  - type: MovementAlwaysTouching

- type: entity
  parent: MobSnail
  id: MobSnailSpeed
  suffix: Speed
  components:
  - type: GhostRole
    name: ghost-role-information-snailspeed-name
    description: ghost-role-information-snailspeed-description
    rules: ghost-role-information-freeagent-rules
  - type: Sprite
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: spacesnail
  - type: DamageStateVisuals
    states:
      Alive:
        Base: spacesnail
      Critical:
        Base: spacesnaildead
      Dead:
        Base: spacesnaildead
  - type: MovementSpeedModifier
    baseWalkSpeed : 5 #he go fast, also they cant slip so its probably fine.
    baseSprintSpeed : 7
#  - type: ActiveJetpack # I think this will need a custom component to not make tests angry.
  - type: MovementAlwaysTouching

- type: entity
  parent: MobSnail
  id: MobSnailMoth
  name: "Слимак"
  components:
  - type: Body
    prototype: Mothroach
  - type: GhostRole
    name: ghost-role-information-snoth-name
    description: ghost-role-information-snoth-description
    rules: ghost-role-information-freeagent-rules
  - type: Sprite
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: snoth
  - type: DamageStateVisuals
    states:
      Alive:
        Base: snoth
      Critical:
        Base: snothdead
      Dead:
        Base: snothdead
