- type: gameMap
  id: Omega
  mapName: Омега
  mapPath: /Maps/omega.yml
  minPlayers: 0
  stations:
    Omega:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: '{0} Omega Station {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: 'TG'
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/emergency_omega.yml
        - type: StationJobs
          overflowJobs:
            - Passenger
          availableJobs:
            #service
            Captain: [ 1, 1 ]
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 1, 1 ]
            Botanist: [ 2, 2 ]
            Chef: [ 1, 1 ]
            Janitor: [ 1, 2 ]
            Chaplain: [ 1, 1 ]
            Librarian: [ 1, 1 ]
            ServiceWorker: [ 2, 2 ]
            #engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 2, 2 ]
            StationEngineer: [ 3, 3 ]
            TechnicalAssistant: [ 2, 2 ]
            #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            Chemist: [ 2, 2 ]
            MedicalDoctor: [ 3, 3 ]
            Paramedic: [ 1, 1 ]
            MedicalIntern: [ 2, 2 ]
            #science
            ResearchDirector: [ 1, 1 ]
            Roboticist: [ 2, 2 ]
            Scientist: [ 4, 4 ]
            ResearchAssistant: [ 2, 2 ]
            #security
            HeadOfSecurity: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1 ]
            Warden: [ 1, 1 ]
            SecurityOfficer: [ 4, 4 ]
            Detective: [ 1, 1 ]
            SecurityCadet: [ 2, 2 ]
            Lawyer: [ 1, 1 ]
            #supply
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ 2, 2 ]
            CargoTechnician: [ 2, 2 ]
            #civilian
            Passenger: [ -1, -1 ]
            Clown: [ 1, 1 ]
            Mime: [ 1, 1 ]
            Musician: [ 1, 1 ]
            #silicon
            Borg: [ 2, 2 ]

        # Goobstation blob-config-start SMALL+
        - type: StationBlobConfig
          stageBegin: 25
          stageCritical: 350
          stageTheEnd: 700
        # Goobstation blob-config-end
