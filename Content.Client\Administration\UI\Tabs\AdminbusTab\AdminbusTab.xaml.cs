﻿using Content.Client.Administration.Managers;
using Content.Client.UserInterface.Systems.DecalPlacer;
using Content.Shared.Administration;
using Robust.Client.AutoGenerated;
using Robust.Client.Upload.Commands;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controllers.Implementations;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Administration.UI.Tabs.AdminbusTab
{
    [GenerateTypedNameReferences]
    public sealed partial class AdminbusTab : Control
    {
        private readonly EntitySpawningUIController _entitySpawningController;
        private readonly TileSpawningUIController _tileSpawningController;
        private readonly DecalPlacerUIController _decalPlacerController;

        public AdminbusTab()
        {
            RobustXamlLoader.Load(this);
            IoCManager.InjectDependencies(this);

            _entitySpawningController = UserInterfaceManager.GetUIController<EntitySpawningUIController>();
            _tileSpawningController = UserInterfaceManager.GetUIController<TileSpawningUIController>();
            _decalPlacerController = UserInterfaceManager.GetUIController<DecalPlacerUIController>();

            var adminManager = IoCManager.Resolve<IClientAdminManager>();
            adminManager.AdminStatusUpdated += OnStatusUpdate;

            // For the SpawnEntitiesButton and SpawnTilesButton we need to do the press manually
            // TODO: This will probably need some command check at some point
            SpawnEntitiesButton.OnPressed += SpawnEntitiesButtonOnPressed;
            SpawnTilesButton.OnPressed += SpawnTilesButtonOnOnPressed;
            SpawnDecalsButton.OnPressed += SpawnDecalsButtonOnPressed;
            LoadGamePrototypeButton.OnPressed += LoadGamePrototypeButtonOnPressed;
            LoadGamePrototypeButton.Disabled = !adminManager.CanCommand("loadprototype");
            LoadBlueprintsButton.Disabled = !adminManager.CanCommand("loadgrid");
        }

        private void OnStatusUpdate()
        {
            var adminManager = IoCManager.Resolve<IClientAdminManager>();
            LoadGamePrototypeButton.Disabled = !adminManager.CanCommand("loadprototype");
            LoadBlueprintsButton.Disabled = !adminManager.CanCommand("loadgrid");
        }

        private void LoadGamePrototypeButtonOnPressed(BaseButton.ButtonEventArgs obj)
        {
            LoadPrototypeCommand.LoadPrototype();
        }

        private void SpawnEntitiesButtonOnPressed(BaseButton.ButtonEventArgs obj)
        {
            _entitySpawningController.ToggleWindow();
        }

        private void SpawnTilesButtonOnOnPressed(BaseButton.ButtonEventArgs obj)
        {
            _tileSpawningController.ToggleWindow();
        }

        private void SpawnDecalsButtonOnPressed(BaseButton.ButtonEventArgs obj)
        {
            _decalPlacerController.ToggleWindow();
        }
    }
}
