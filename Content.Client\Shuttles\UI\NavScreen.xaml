<controls:BoxContainer Visible="False"
              HorizontalExpand="True"
              xmlns:controls="https://spacestation14.io"
              xmlns:ui="clr-namespace:Content.Client.Shuttles.UI"
              xmlns:controls1="clr-namespace:Content.Client.UserInterface.Controls">
                <ui:ShuttleNavControl Name="NavRadar"
                                 MouseFilter="Stop"
                                 VerticalAlignment="Stretch"
                                 VerticalExpand="True"
                                 HorizontalExpand="True"
                                 Margin="5 4 10 5"/>
                <!-- Nav controls -->
                <controls:BoxContainer Name="RightDisplayNav"
                      VerticalAlignment="Top"
                      HorizontalAlignment="Right"
                      VerticalExpand="True"
                      MinWidth="256"
                      MaxWidth="256"
                      Margin="5 0 5 5"
                      Orientation="Vertical">
                    <controls1:StripeBack
                        MinSize="48 48">
                        <controls:Label Name="NavDisplayLabel" Text="{controls:Loc 'shuttle-console-display-label'}"
                               VerticalExpand="True"
                               HorizontalAlignment="Center"/>
                    </controls1:StripeBack>
                    <controls:GridContainer Columns="2"
                                   HorizontalAlignment="Stretch"
                                   VerticalAlignment="Top"
                                   HorizontalExpand="True"
                                   Margin="3"
                                   Name="ReadonlyDisplay">
                        <controls:Label Text="{controls:Loc 'shuttle-console-position'}"/>
                        <controls:Label Name="GridPosition"
                               Text="0.0, 0.0"
                               HorizontalExpand="True"
                               Align="Right"/>
                        <controls:Label Text="{controls:Loc 'shuttle-console-orientation'}"/>
                        <controls:Label Name="GridOrientation"
                               Text="0.0"
                               HorizontalExpand="True"
                               Align="Right"/>
                        <controls:Label Text="{controls:Loc 'shuttle-console-linear-velocity'}"/>
                        <controls:Label Name="GridLinearVelocity"
                               Text="0.0, 0.0"
                               HorizontalExpand="True"
                               Align="Right"/>
                        <controls:Label Text="{controls:Loc 'shuttle-console-angular-velocity'}"/>
                        <controls:Label Name="GridAngularVelocity"
                               Text="0.0"
                               HorizontalExpand="True"
                               Align="Right"/>
                    </controls:GridContainer>
                    <controls1:StripeBack
                        MinSize="48 48">
                        <controls:Label Name="NavSettingsLabel" Text="{controls:Loc 'shuttle-console-nav-settings'}"
                               VerticalExpand="True"
                               HorizontalAlignment="Center"/>
                    </controls1:StripeBack>
                    <controls:Button Name="IFFToggle"
                            Text="{controls:Loc 'shuttle-console-iff-toggle'}"
                            TextAlign="Center"
                            ToggleMode="True"/>
                    <controls:Button Name="DockToggle"
                            Text="{controls:Loc 'shuttle-console-dock-toggle'}"
                            TextAlign="Center"
                            ToggleMode="True"/>
                    <!-- Frontier - Inertia dampener controls-->
                    <controls:BoxContainer Name="DampenerModeButtons"
                                  Orientation="Horizontal"
                                  HorizontalAlignment="Stretch"
                                  Margin="5">
                        <controls:Button Name="DampenerOff"
                                         Text="{controls:Loc 'shuttle-console-inertia-dampener-off'}"
                                         TextAlign="Center"
                                         ToggleMode="True"
                                         MinWidth="82"
                                         MaxWidth="82"/>
                        <controls:Button Name="DampenerOn"
                                         Text="{controls:Loc 'shuttle-console-inertia-dampener-dampened'}"
                                         TextAlign="Center"
                                         ToggleMode="True"
                                         MinWidth="82"
                                         MaxWidth="82"/>
                        <controls:Button Name="AnchorOn"
                                         Text="{controls:Loc 'shuttle-console-inertia-dampener-anchored'}"
                                         TextAlign="Center"
                                         ToggleMode="True"
                                         MinWidth="82"
                                         MaxWidth="82"/>
                    </controls:BoxContainer>
                    <!--end Inertia dampener controls-->
                    </controls:BoxContainer>
                </controls:BoxContainer>
