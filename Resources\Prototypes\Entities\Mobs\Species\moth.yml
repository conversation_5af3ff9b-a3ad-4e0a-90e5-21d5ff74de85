- type: entity
  save: false
  name: "Уріст МакФлафф"
  parent: BaseMobSpeciesOrganic
  id: BaseMobMoth
  abstract: true
  components:
  - type: HumanoidAppearance
    species: Moth
  - type: Hunger
  - type: Thirst
  - type: Carriable # Carrying system from nyanotrasen.
  - type: Icon
    sprite: Mobs/Species/Moth/parts.rsi
    state: full
  - type: Body
    prototype: Moth
    requiredLegs: 2
  - type: Damageable
    damageContainer: Biological
    damageModifierSet: Moth
  - type: ZombieAccentOverride
    accent: zombieMoth
  - type: Speech
    speechVerb: Moth
    allowedEmotes: ['Chitter', 'Squeak']
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    - Moffic
    understands:
    - TauCetiBasic
    - Moffic
  - type: TypingIndicator
    proto: moth
  - type: Butcherable
    butcheringType: Spike
    spawned:
    - id: FoodMeat
      amount: 5
  - type: Bloodstream
    bloodReagent: InsectBlood
  - type: DamageVisuals
    damageOverlayGroups:
      Brute:
        sprite: Mobs/Effects/brute_damage.rsi
        color: "#808A51"
  - type: MothAccent
  - type: Vocal
    sounds:
      Male: UnisexMoth
      Female: UnisexMoth
      Unsexed: UnisexMoth
  - type: MovementSpeedModifier
    weightlessAcceleration: 2.5 # Move around more easily in space.
    weightlessModifier: 1.1
    weightlessFriction: 2 # makes you turn more sharply while weightless
  - type: Flammable
    damage:
      types:
        Heat: 4 # DV - Reduced by .5
  - type: Temperature # Moths hate the heat and thrive in the cold.
    heatDamageThreshold: 320
    coldDamageThreshold: 230
    currentTemperature: 310.15
    specificHeat: 46
    coldDamage:
      types:
        Cold : 0.05 #per second, scales with temperature & other constants
    heatDamage:
      types:
        Heat : 3 #per second, scales with temperature & other constants
  - type: TemperatureSpeed
    thresholds:
      289: 0.85
      275: 0.7
      250: 0.55
  - type: TemperatureProtection # moff fluff acts as insulation
    coefficient: 0.5 # less than vulp fur but still significant
  - type: Sprite # sprite again because we want different layer ordering
    noRot: true
    drawdepth: Mobs
    layers:
      - map: [ "enum.HumanoidVisualLayers.Chest" ]
      - map: [ "enum.HumanoidVisualLayers.Snout" ]
      - map: [ "enum.HumanoidVisualLayers.Head" ]
      - map: [ "enum.HumanoidVisualLayers.Eyes" ]
      - map: [ "enum.HumanoidVisualLayers.Face" ]
      - map: [ "enum.HumanoidVisualLayers.RArm" ]
      - map: [ "enum.HumanoidVisualLayers.LArm" ]
      - map: [ "enum.HumanoidVisualLayers.RLeg" ]
      - map: [ "enum.HumanoidVisualLayers.LLeg" ]
#     - shader: StencilClear
#      sprite: Mobs/Species/Human/parts.rsi
#      state: l_leg
      - map: [ "enum.HumanoidVisualLayers.Underwear" ]
      - map: [ "enum.HumanoidVisualLayers.Undershirt" ]
      - map: [ "jumpsuit" ]
      - map: [ "enum.HumanoidVisualLayers.LHand" ]
      - map: [ "enum.HumanoidVisualLayers.RHand" ]
      - map: [ "enum.HumanoidVisualLayers.LFoot" ]
      - map: [ "enum.HumanoidVisualLayers.RFoot" ]
      - map: [ "enum.HumanoidVisualLayers.Handcuffs" ]
        color: "#ffffff"
        sprite: Objects/Misc/handcuffs.rsi
        state: body-overlay-2
        visible: false
      - map: [ "gloves" ]
      - map: [ "shoes" ]
      - map: [ "ears" ]
      - map: [ "innerBelt" ]
      - map: [ "innerNeck" ]
      - map: [ "outerClothing" ]
      - map: [ "eyes" ]
      - map: [ "belt" ]
      - map: [ "id" ]
      - map: [ "enum.HumanoidVisualLayers.Tail" ] #in the utopian future we should probably have a wings enum inserted here so everyhting doesn't break
      - map: [ "neck" ]
      - map: [ "back" ]
      - map: [ "enum.HumanoidVisualLayers.FacialHair" ]
      - map: [ "enum.HumanoidVisualLayers.Hair" ]
      - map: [ "enum.HumanoidVisualLayers.HeadSide" ]
      - map: [ "enum.HumanoidVisualLayers.HeadTop" ]
      - map: [ "mask" ]
      - map: [ "head" ]
      - map: [ "neck2" ] #PIRATE
      - map: [ "neck1" ] #PIRATE
      - map: [ "head2" ] #PIRATE
      - map: [ "head1" ] #PIRATE
      - map: [ "pocket1" ]
      - map: [ "pocket2" ]
      - map: [ "clownedon" ] # Dynamically generated
        sprite: "Effects/creampie.rsi"
        state: "creampie_moth"
        visible: false
  - type: FootPrints
  - type: CanMoveInAir

- type: entity
  parent: BaseSpeciesDummy
  id: MobMothDummy
  categories: [ HideSpawnMenu ]
  components:
  - type: HumanoidAppearance
    species: Moth
  - type: Sprite
    layers:
      - map: [ "enum.HumanoidVisualLayers.Chest" ]
      - map: [ "enum.HumanoidVisualLayers.Snout" ]
      - map: [ "enum.HumanoidVisualLayers.Head" ]
      - map: [ "enum.HumanoidVisualLayers.Eyes" ]
      - map: [ "enum.HumanoidVisualLayers.Face" ]
      - map: [ "enum.HumanoidVisualLayers.RArm" ]
      - map: [ "enum.HumanoidVisualLayers.LArm" ]
      - map: [ "enum.HumanoidVisualLayers.RLeg" ]
      - map: [ "enum.HumanoidVisualLayers.LLeg" ]
#     - shader: StencilClear
#      sprite: Mobs/Species/Human/parts.rsi
#      state: l_leg
      - map: [ "enum.HumanoidVisualLayers.Underwear" ]
      - map: [ "enum.HumanoidVisualLayers.Undershirt" ]
      - map: [ "jumpsuit" ]
      - map: [ "enum.HumanoidVisualLayers.LHand" ]
      - map: [ "enum.HumanoidVisualLayers.RHand" ]
      - map: [ "enum.HumanoidVisualLayers.LFoot" ]
      - map: [ "enum.HumanoidVisualLayers.RFoot" ]
      - map: [ "enum.HumanoidVisualLayers.Handcuffs" ]
        color: "#ffffff"
        sprite: Objects/Misc/handcuffs.rsi
        state: body-overlay-2
        visible: false
      - map: [ "gloves" ]
      - map: [ "shoes" ]
      - map: [ "ears" ]
      - map: [ "innerBelt" ]
      - map: [ "innerNeck" ]
      - map: [ "outerClothing" ]
      - map: [ "eyes" ]
      - map: [ "belt" ]
      - map: [ "id" ]
      - map: [ "enum.HumanoidVisualLayers.Tail" ] #in the utopian future we should probably have a wings enum inserted here so everyhting doesn't break
      - map: [ "neck" ]
      - map: [ "back" ]
      - map: [ "enum.HumanoidVisualLayers.FacialHair" ]
      - map: [ "enum.HumanoidVisualLayers.Hair" ]
      - map: [ "enum.HumanoidVisualLayers.HeadSide" ]
      - map: [ "enum.HumanoidVisualLayers.HeadTop" ]
      - map: [ "mask" ]
      - map: [ "head" ]
      - map: [ "pocket1" ]
      - map: [ "pocket2" ]
      - map: [ "clownedon" ] # Dynamically generated
        sprite: "Effects/creampie.rsi"
        state: "creampie_moth"
        visible: false
