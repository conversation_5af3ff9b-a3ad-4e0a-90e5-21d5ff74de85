- type: entity
  id: ConveyorBelt
  name: "конвеєрна стрічка"
  description: "Конвеєрна стрічка, яку зазвичай використовують для швидкого транспортування великої кількості предметів в інше місце."
  placement:
    mode: SnapgridCenter
  components:
  - type: Rotatable
    rotateWhileAnchored: true
  - type: Clickable
  - type: InteractionOutline
  - type: Transform
    anchored: true
  - type: Sprite
    sprite: Structures/conveyor.rsi
    state: conveyor_started_cw
    drawdepth: HighFloorObjects
  - type: ApcPowerReceiver
  - type: ExtensionCableReceiver
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      conveyor:
        shape: !type:PolygonShape
          vertices:
          - -0.55,-0.55
          - 0.55,-0.55
          - 0.55,0.55
          - -0.55,0.55
        layer:
        - Impassable
        - MidImpassable
        - LowImpassable
        - DoorPassable
        hard: False
  - type: Conveyor
  - type: DeviceLinkSink
    ports:
      - Reverse
      - Forward
      - Off
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ConveyorVisuals.State:
        enum.ConveyorState.Off:
          Off: { state: conveyor_stopped_cw }
          Forward: { state: conveyor_started_cw }
          Reverse: { state: conveyor_started_cw_r }
  - type: Construction
    graph: ConveyorGraph
    node: entity
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      # if ConveyorBeltAssembly becomes craftable, maybe spawn some of the ingredients?
      - !type:DoActsBehavior
        acts: [ "Destruction" ]

- type: entity
  id: ConveyorBeltAssembly
  parent: BaseItem
  name: "конвеєрна стрічка"
  suffix: assembly
  description: "Конвеєрна стрічка в зборі. Використовується для створення конвеєрної стрічки."
  components:
  - type: Tag
    tags:
    - ConveyorAssembly
  - type: Sprite
    sprite: Structures/conveyor.rsi
    state: conveyor_loose
  - type: Construction
    graph: ConveyorGraph
    node: item
  - type: StaticPrice
    price: 40

