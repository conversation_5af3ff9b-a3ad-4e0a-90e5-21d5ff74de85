- type: entity
  name: "зручна лавка"
  id: BenchComfy
  parent: SeatBase
  description: "Лавка з надзвичайно зручною спинкою."
  components:
  - type: Transform
    anchored: true
  - type: Rotatable
  - type: Sprite
    sprite: Structures/Furniture/Bench/comf_bench.rsi
    layers:
    - state: bench_solo_base
      color: "#767e82"
    - state: bench_solo_cover
  - type: Physics
    bodyType: Static
  - type: StaticPrice
    price: 15

- type: entity
  id: BenchColorfulComfy
  suffix: Solo. Colorful
  parent: BenchComfy
  description: "Лавка з надзвичайно зручною спинкою."
  components:
  - type: Sprite
    layers:
    - state: bench_solo_base
      color: "#767e82"
    - state: bench_solo_cover
      map: [ "enum.DamageStateVisualLayers.Base" ]
  - type: RandomSprite
    available:
      - enum.DamageStateVisualLayers.Base:
          bench_solo_cover: Rainbow

- type: entity
  id: BenchRedComfy
  suffix: Solo. Red
  parent: BenchComfy
  components:
  - type: Sprite
    layers:
    - state: bench_solo_base
      color: "#767e82"
    - state: bench_solo_cover
      color: "#8c4141"
  - type: Construction
    graph: Seat
    node: redComfBench

- type: entity
  id: BenchBlueComfy
  suffix: Solo. Blue
  parent: BenchComfy
  components:
  - type: Sprite
    layers:
    - state: bench_solo_base
      color: "#767e82"
    - state: bench_solo_cover
      color: "#334e6d"
  - type: Construction
    graph: Seat
    node: blueComfBench
