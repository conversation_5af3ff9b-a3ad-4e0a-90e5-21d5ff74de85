﻿- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesChameleon # no flash immunity, sorry
  name: "сонцезахисні окуляри"
  description: "Корисно як для безпеки, так і для карго."
  suffix: Chameleon
  components:
    - type: Tag
      tags: [] # ignore "WhitelistChameleon" tag
    - type: Sprite
      sprite: Clothing/Eyes/Glasses/sunglasses.rsi
    - type: Clothing
      sprite: Clothing/Eyes/Glasses/sunglasses.rsi
    - type: ChameleonClothing
      slot: [eyes]
      default: ClothingEyesGlassesSunglasses
    - type: UserInterface
      interfaces:
        enum.ChameleonUiKey.Key:
          type: ChameleonBoundUserInterface

