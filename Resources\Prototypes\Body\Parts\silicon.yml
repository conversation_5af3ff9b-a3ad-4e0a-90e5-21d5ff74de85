- type: entity
  id: PartSilicon
  parent: BaseItem
  abstract: true
  components:
  - type: Sprite
    sprite: Objects/Specific/Robotics/cyborg_parts.rsi
  - type: Icon
    sprite: Objects/Specific/Robotics/cyborg_parts.rsi
  - type: Damageable
    damageContainer: Inorganic
  - type: BodyPart
  - type: ContainerContainer
    containers:
      bodypart: !type:Container
        ents: []
  - type: StaticPrice
    price: 5
  - type: Tag
    tags:
      - Trash
  - type: PhysicalComposition
    materialComposition:
      Steel: 25
  - type: GuideHelp
    guides:
      - Cyborgs
  # Shitmed Change Start
  - type: SurgeryTool
    startSound:
      path: /Audio/_Shitmed/Medical/Surgery/organ1.ogg
    endSound:
      path: /Audio/_Shitmed/Medical/Surgery/organ2.ogg
  - type: Gibbable
  # Shitmed Change End

- type: entity
  id: BaseBorgLeftArm
  parent: PartSilicon
  name: "кіборг ліва рука"
  components:
  - type: BodyPart
    partType: Arm # Shitmed Change
    symmetry: Left
  - type: Sprite
    state: borg_l_arm
  - type: Icon
    state: borg_l_arm
    toolName: "a left arm" # Shitmed Change
    children: # Shitmed Change
      left hand:
        id: "left hand"
        type: Hand
  - type: Tag
    tags:
    - Trash
    - BorgArm
    - BorgLArm

- type: entity
  id: BaseBorgRightArm
  parent: PartSilicon
  name: "кіборг права рука"
  components:
  - type: BodyPart
    partType: Arm # Shitmed Change
    symmetry: Right
  - type: Sprite
    state: borg_r_arm
  - type: Icon
    state: borg_r_arm
    toolName: "a right arm" # Shitmed Change
    children: # Shitmed Change
      right hand:
        id: "right hand"
        type: Hand
  - type: Tag
    tags:
    - Trash
    - BorgArm
    - BorgRArm

- type: entity
  id: BaseBorgLegLeft
  parent: PartSilicon
  name: "ліва нога кіборга"
  components:
  - type: BodyPart
    partType: Leg
    symmetry: Left
  - type: Sprite
    state: borg_l_leg
  - type: Icon
    state: borg_l_leg
    toolName: "a left leg" # Shitmed Change
    children: # Shitmed Change
      left foot:
        id: "left foot"
        type: Foot
  - type: Tag
    tags:
    - Trash
    - BorgLeg
    - BorgLLeg
  - type: MovementBodyPart

- type: entity
  id: BaseBorgLegRight
  parent: PartSilicon
  name: "права нога кіборга"
  components:
  - type: BodyPart
    partType: Leg
    symmetry: Right
  - type: Sprite
    state: borg_r_leg
  - type: Icon
    state: borg_r_leg
    toolName: "a right leg" # Shitmed Change
    children: # Shitmed Change
      right foot:
        id: "right foot"
        type: Foot
  - type: Tag
    tags:
    - Trash
    - BorgLeg
    - BorgRLeg
  - type: MovementBodyPart

- type: entity
  id: BaseLightHeadBorg
  parent: PartSilicon
  name: "голова кіборга"
  components:
  - type: BodyPart
    partType: Head
  - type: Sprite
    state: borg_head
  - type: Icon
    state: borg_head
    toolName: "a head" # Shitmed Change
  - type: Tag
    tags:
    - Trash
    - BorgHead

- type: entity
  id: BaseBorgTorso
  parent: PartSilicon
  name: "торс кіборга"
  components:
  - type: BodyPart
    partType: Torso
  - type: Sprite
    state: borg_chest
  - type: Icon
    state: borg_chest
    toolName: "a torso" # Shitmed Change
  - type: Tag
    tags:
    - Trash
    - BorgTorso
