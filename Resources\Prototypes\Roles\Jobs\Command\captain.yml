- type: job
  id: Captain
  name: job-name-captain
  description: job-description-captain
  playTimeTracker: JobCaptain
  requirements:
    - !type:CharacterTraitRequirement
      inverted: true
      traits:
        - Foreigner
        - ForeignerLight
        - Pacifist
        - Muted
    - !type:CharacterDepartmentTimeRequirement
      department: Logistics # DeltaV - Logistics Department replacing Cargo
      min: 18000 # DeltaV - 5 hours
    - !type:CharacterDepartmentTimeRequirement
      department: Engineering
      min: 18000 # DeltaV - 5 hours
    - !type:CharacterDepartmentTimeRequirement
      department: Medical
      min: 18000 # DeltaV - 5 hours
    - !type:CharacterDepartmentTimeRequirement
      department: Security
      min: 18000 # DeltaV - 5 hours
    - !type:CharacterDepartmentTimeRequirement # DeltaV - Epistemics dept time requirement
      department: Epistemics # DeltaV - Epistemics Department replacing Science
      min: 18000 # 5 hours
    - !type:CharacterDepartmentTimeRequirement
      department: Command
      min: 72000 #Pirate - 20 hours
    - !type:CharacterOverallTimeRequirement # DeltaV - Playtime requirement
      min: 72000 #Pirate 20 hours
    - !type:CharacterWhitelistRequirement
    - !type:CharacterEmployerRequirement
      employers:
      - NanoTrasen
  weight: 20
  startingGear: CaptainGear
  icon: "JobIconCaptain"
  requireAdminNotify: true
  joinNotifyCrew: true
  supervisors: job-supervisors-centcom
  canBeAntag: false
  accessGroups:
  - AllAccess
  special:
  - !type:AddImplantSpecial
    implants: [ MindShieldImplant ]
  - !type:AddComponentSpecial
    components:
      - type: CommandStaff
      - type: Condemned # Goobstation - Nanotrasen owns your soul pal.
        soulOwnedNotDevil: true
  afterLoadoutSpecial:
  - !type:ModifyEnvirosuitSpecial
    charges: 8
  - !type:ModifyEnvirohelmSpecial
    powerCell: PowerCellHigh

- type: startingGear
  id: CaptainGear
  subGear:
  - CaptainPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitCaptain
    back: ClothingBackpackCaptainFilled
    shoes: ClothingShoesBootsLaceup
    id: CaptainPDA
    ears: ClothingHeadsetAltCommand
  innerClothingSkirt: ClothingUniformJumpskirtCaptain
  satchel: ClothingBackpackSatchelCaptainFilled
  duffelbag: ClothingBackpackDuffelCaptainFilled

- type: startingGear
  id: CaptainPlasmamanGear
  parent: BasePlasmamanGear
  equipment:
    jumpsuit: ClothingUniformEnvirosuitCaptain
    head: ClothingHeadEnvirohelmCaptain
    gloves: ClothingHandsGlovesCaptain
