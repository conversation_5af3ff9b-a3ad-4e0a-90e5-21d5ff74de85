using Content.Shared.Nyanotrasen.Kitchen.UI;
using Robust.Client.AutoGenerated;
using Robust.Client.GameObjects;
using Robust.Client.Graphics;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Nyanotrasen.Kitchen.UI
{
    [GenerateTypedNameReferences]
    [Access(typeof(DeepFryerBoundUserInterface))]
    public sealed partial class DeepFryerWindow : DefaultWindow
    {
        [Dependency] private readonly IEntityManager _entityManager = default!;

        private static readonly Color WarningColor = Color.FromHsv(new Vector4(0.0f, 1.0f, 0.8f, 1.0f));

        public DeepFryerWindow()
        {
            RobustXamlLoader.Load(this);
            IoCManager.InjectDependencies(this);
        }

        public void UpdateState(DeepFryerBoundUserInterfaceState state)
        {
            OilLevel.Value = (float) state.OilLevel;
            OilPurity.Value = (float) state.OilPurity;

            if (state.OilPurity < state.FryingOilThreshold)
            {
                if (OilPurity.ForegroundStyleBoxOverride == null)
                {
                    OilPurity.ForegroundStyleBoxOverride = new StyleBoxFlat();

                    var oilPurityStyle = (StyleBoxFlat) OilPurity.ForegroundStyleBoxOverride;
                    oilPurityStyle.BackgroundColor = WarningColor;
                }
            }
            else
            {
                OilPurity.ForegroundStyleBoxOverride = null;
            }

            ItemList.Clear();

            foreach (var netEntity in state.ContainedEntities)
            {
                var entity = _entityManager.GetEntity(netEntity);
                if (_entityManager.Deleted(entity))
                    continue;

                // Duplicated from MicrowaveBoundUserInterface.cs: keep an eye on that file for when it changes.
                Texture? texture;
                if (_entityManager.TryGetComponent(entity, out IconComponent? iconComponent))
                {
                    texture = _entityManager.System<SpriteSystem>().GetIcon(iconComponent);
                }
                else if (_entityManager.TryGetComponent(entity, out SpriteComponent? spriteComponent))
                {
                    texture = spriteComponent.Icon?.Default;
                }
                else
                {
                    continue;
                }

                ItemList.AddItem(_entityManager.GetComponent<MetaDataComponent>(entity).EntityName, texture);
            }
        }
    }
}
