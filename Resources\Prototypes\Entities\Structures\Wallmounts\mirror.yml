﻿- type: entity
  id: Mirror
  name: "дзеркало"
  description: 'Mirror mirror on the wall , who''s the most robust of them all?'
  components:
  - type: WallMount
  - type: Sprite
    sprite: Structures/Wallmounts/mirror.rsi
    state: mirror
  - type: InteractionOutline
  - type: Clickable
  - type: Transform
    anchored: true
  - type: MagicMirror #instant and silent
    changeHairSound: null
    addSlotTime: 0
    removeSlotTime: 0
    selectSlotTime: 0
    changeSlotTime: 0
  - type: ActivatableUI
    key: enum.MagicMirrorUiKey.Key
  - type: UserInterface
    interfaces:
      enum.MagicMirrorUiKey.Key:
        type: MagicMirrorBoundUserInterface
