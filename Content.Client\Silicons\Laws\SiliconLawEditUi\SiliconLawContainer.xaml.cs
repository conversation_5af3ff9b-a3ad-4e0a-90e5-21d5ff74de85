﻿using Content.Shared.Silicons.Laws;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Utility;

namespace Content.Client.Silicons.Laws.SiliconLawEditUi;

[GenerateTypedNameReferences]
public sealed partial class SiliconLawContainer : BoxContainer
{
    public const string StyleClassSiliconLawPositionLabel = "SiliconLawPositionLabel";

    public static readonly string CorruptedString =
        Loc.GetString("ion-storm-law-scrambled-number", ("length", 5));

    private SiliconLaw? _law;

    public event Action<SiliconLaw>? MoveLawUp;
    public event Action<SiliconLaw>? MoveLawDown;
    public event Action<SiliconLaw>? DeleteAction;


    public SiliconLawContainer()
    {
        RobustXamlLoader.Load(this);

        MoveUp.OnPressed += _ => MoveLawUp?.Invoke(_law!);
        MoveDown.OnPressed += _ => MoveLawDown?.Invoke(_law!);
        Corrupted.OnPressed += _ =>
        {
            if (Corrupted.Pressed)
            {
                _law!.LawIdentifierOverride = CorruptedString;
            }
            else
            {
                _law!.LawIdentifierOverride = null;
            }
        };

        LawContent.OnTextChanged += _ => _law!.LawString = Rope.Collapse(LawContent.TextRope).Trim();
        LawContent.Placeholder = new Rope.Leaf(Loc.GetString("silicon-law-ui-placeholder"));
        Delete.OnPressed += _ => DeleteAction?.Invoke(_law!);
    }

    public void SetLaw(SiliconLaw law)
    {
        _law = law;
        LawContent.TextRope = new Rope.Leaf(Loc.GetString(law.LawString));
        PositionText.Text = law.Order.ToString();
        if (!string.IsNullOrEmpty(law.LawIdentifierOverride))
        {
            Corrupted.Pressed = true;
        }
        else
        {
            Corrupted.Pressed = false;
        }
    }
}
