cmd-panicbunker-desc = Вмикає бункер паніки, який дозволяє суворіше обмежувати доступ до сервера.
cmd-panicbunker-help = Використання: панічний бункер
panicbunker-command-enabled = Увімкнено панічний бункер.
panicbunker-command-disabled = Панічний бункер відключено.

cmd-panicbunker_disable_with_admins-desc = Дозволяє ввімкнути чи вимкнути аварійний бункер при підключенні адміністратора.
cmd-panicbunker_disable_with_admins-help = Використання: panicbunker_disable_with_admins
panicbunker-command-disable-with-admins-enabled = Панічний бункер автоматично вимкнеться, якщо адміністратори будуть на зв'язку.
panicbunker-command-disable-with-admins-disabled = Панічний бункер не вимкнеться автоматично, якщо адміністратори в мережі.

cmd-panicbunker_enable_without_admins-desc = Дозволяє ввімкнути чи вимкнути аварійний бункер при відключенні останнього адміністратора.
cmd-panicbunker_enable_without_admins-help = Використання: panicbunker_enable_without_admins
panicbunker-command-enable-without-admins-enabled = Панічний бункер увімкнеться автоматично без участі адміністратора.
panicbunker-command-enable-without-admins-disabled = Панічний бункер не ввімкнеться автоматично, якщо адміністратори не будуть онлайн.

cmd-panicbunker_count_deadminned_admins-desc = Дозволяє вмикати чи вимикати врахування замаскованих адміністраторів при автоматичному вмиканні та вимиканні бункера паніки.
cmd-panicbunker_count_deadminned_admins-help = Використання: panicbunker_count_deadminned_admins
panicbunker-command-count-deadminned-admins-enabled = У бункері паніки буде підраховано кількість замаскованих адміністраторів, коли його буде автоматично ввімкнено та вимкнено.
panicbunker-command-count-deadminned-admins-disabled = Бункер паніки не буде рахувати замаскованих адміністраторів, якщо його буде автоматично ввімкнено та вимкнено.

cmd-panicbunker_show_reason-desc = Дозволяє вказати, чи показувати клієнтам, що підключаються, причину, через яку бункер паніки заблокував їх приєднання.
cmd-panicbunker_show_reason-help = Використання: panicbunker_show_reason
panicbunker-command-show-reason-enabled = Тепер бункер паніки показуватиме користувачам, яким він блокує підключення, причину.
panicbunker-command-show-reason-disabled = Бункер тривоги більше не показуватиме користувачам, яким він блокує підключення, причину, з якої вони заблоковані.

cmd-panicbunker_min_account_age-desc = Отримує або встановлює мінімальний вік акаунта в годинах, який має бути дозволений для підключення з увімкненим аварійним бункером.
cmd-panicbunker_min_account_age-help = Використання: panicbunker_min_account_age <hours
panicbunker-command-min-account-age-is = Мінімальний вік акаунта в бункері становить {$hours} годин.
panicbunker-command-min-account-age-set = Встановіть мінімальний вік акаунта для панічного бункера у {$hours} годин.

cmd-panicbunker_min_overall_hours-desc = Отримує або встановлює мінімальний загальний ігровий час у годинах, який повинен мати акаунт для підключення з увімкненим аварійним бункером.
cmd-panicbunker_min_overall_hours-help = Використання: panicbunker_min_overall_hours <hours
panicbunker-command-min-overall-hours-is = Мінімальний загальний час гри в бункері становить {$hours} годин.
panicbunker-command-min-overall-hours-set = Встановіть мінімальний загальний час гри для бункера в {$hours} годин.
