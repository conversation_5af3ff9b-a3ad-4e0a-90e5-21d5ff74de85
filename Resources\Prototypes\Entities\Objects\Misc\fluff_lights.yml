- type: entity
  name: "ліхтар"
  parent: BaseItem
  id: BaseLamp
  abstract: true
  components:
  - type: HandheldLight
    addPrefix: true
    blinkingBehaviourId: blinking
    radiatingBehaviourId: radiating
  - type: LightBehaviour
    behaviours:
      - !type:FadeBehaviour
        id: radiating
        maxDuration: 2.0
        startValue: 3.0
        endValue: 2.0
        isLooped: true
        reverseWhenFinished: true
      - !type:PulseBehaviour
        id: blinking
        interpolate: Nearest
        maxDuration: 1.0
        minValue: 0.1
        maxValue: 2.0
        isLooped: true
  - type: PowerCellSlot
    cellSlotId: cell_slot
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellSmall
  - type: ContainerContainer
    containers:
      cell_slot: !type:ContainerSlot
  - type: Sprite
    sprite: Objects/Misc/Lights/lights.rsi
  - type: Item
    sprite: Objects/Misc/Lights/lights.rsi
    size: Normal
    heldPrefix: off
  - type: EtherealLight
  - type: PointLight
    enabled: false
    radius: 3
    energy: 2
    netsync: false
  - type: ToggleableLightVisuals
  - type: Appearance
  - type: Physics
    canCollide: false
  - type: StealTarget
    stealGroup: LAMP

- type: entity
  name: "лампа"
  id: Lamp
  parent: BaseLamp
  description: "Пристрій, що випромінює світло."
  components:
  - type: Sprite
    sprite: Objects/Misc/Lights/lamp.rsi
    layers:
      - state: lamp
        map: [ "base" ]
      - state: lamp-on
        shader: unshaded
        visible: false
        map: [ "light" ]
  - type: Item
    sprite: Objects/Misc/Lights/lamp.rsi
  - type: HolidayVisuals
    holidays:
      festive:
      - FestiveSeason
  - type: GenericVisualizer
    visuals:
      enum.HolidayVisuals.Holiday:
        base:
          festive: { state: christmaslamp }
        light:
          festive: { state: christmaslamp-on }

- type: entity
  name: "бананова лампа"
  id: LampBanana
  parent: BaseLamp
  description: "Світловипромінювальний пристрій у формі банана."
  components:
  - type: Sprite
    layers:
      - state: bananalamp
      - state: bananalamp_on
        shader: unshaded
        visible: false
        map: [ "light" ]

- type: entity
  name: "настільна лампа"
  id: LampGold
  parent: BaseLamp
  description: "Пристрій, що випромінює світло, ще й чудово виглядатиме на робочому столі."
  components:
  - type: Sprite
    sprite: Objects/Misc/Lights/lampgreen.rsi
    layers:
      - state: lampgreen
        map: [ "base" ]
      - state: lampgreen-on
        shader: unshaded
        visible: false
        map: [ "light" ]
  - type: Item
    sprite: Objects/Misc/Lights/lampgreen.rsi
  - type: HolidayVisuals
    holidays:
      festive:
      - FestiveSeason
  - type: GenericVisualizer
    visuals:
      enum.HolidayVisuals.Holiday:
        base:
          festive:
            sprite: Objects/Misc/Lights/lamp.rsi
            state: christmaslamp
        light:
          festive:
            sprite: Objects/Misc/Lights/lamp.rsi
            state: christmaslamp-on

- type: entity
  name: "лампа для допиту"
  id: LampInterrogator
  parent: BaseLamp
  description: "Надзвичайно яскрава лампа для поганого поліцейського"
  components:
  - type: Sprite
    sprite: Objects/Misc/Lights/lampint.rsi
    layers:
      - state: lamp-int
        map: [ "enum.FlashVisuals.BaseLayer" ]
      - state: lamp-int-on
        shader: unshaded
        visible: false
        map: [ "light" ]
      - state: flashing
        map: [ "enum.FlashVisuals.LightLayer" ]
        visible: false
  - type: Item
    sprite: Objects/Misc/Lights/lampint.rsi
  - type: StaticPrice
    price: 2500
  - type: PointLight
    netsync: false
    enabled: false
    radius: 2
    energy: 0.5
    color: "#FFFFEE"
  - type: Flash
  - type: LimitedCharges
    maxCharges: 3
    charges: 3
  - type: AutoRecharge
    rechargeDuration: 30
  - type: MeleeWeapon
    wideAnimationRotation: 180
    damage:
      types:
        Blunt: 0 # melee weapon to allow flashing individual targets
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellHigh
  - type: GenericVisualizer
    visuals:
      enum.FlashVisuals.Burnt:
        enum.FlashVisuals.BaseLayer:
          True: {state: burnt}
      enum.FlashVisuals.Flashing:
        enum.FlashVisuals.LightLayer:
          True: {visible: true}
          False: {visible: false}

- type: entity
  name: "прожектор"
  id: Floodlight
  parent: BaseLamp
  description: "Стовп, на якому встановлені потужні ліхтарі."
  components:
  - type: Item
    size: Normal
  - type: Sprite
    layers:
      - state: floodlight
      - state: floodlight_on
        shader: unshaded
        visible: false
        map: [ "light" ]
  - type: Physics
    canCollide: true
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.2, -0.5, 0.2, 0.5"
        density: 50
        mask:
        - HighImpassable
  - type: PointLight
    enabled: false
    radius: 8
    energy: 5
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellMedium
  - type: Anchorable
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 10
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          FloodlightBroken:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]

- type: entity
  name: "розбитий прожектор"
  id: FloodlightBroken
  parent: BaseItem
  description: "Стовп з потужними ліхтарями на ньому. Він зламаний."
  components:
  - type: Sprite
    sprite: Objects/Misc/Lights/lights.rsi
    state: floodlight_broken
  - type: Anchorable
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 40
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 20
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.2, -0.5, 0.2, 0.5"
        density: 50
        mask:
        - HighImpassable
