- type: constructionGraph
  id: Intercom
  start: start
  graph:
  - node: start
    edges:
    - to: assembly
      steps:
      - material: Steel
        amount: 2
        doAfter: 2.0

  - node: assembly
    entity: IntercomAssembly
    edges:
    - to: wired
      steps:
      - material: Cable
        amount: 2
        doAfter: 1
      completed:
      - !type:VisualizerDataInt
        key: "enum.ConstructionVisuals.Layer"
        data: 1
    - to: start
      completed:
      - !type:GivePrototype
        prototype: SheetSteel1
        amount: 2
      - !type:DeleteEntity {}
      steps:
      - tool: Welding
        doAfter: 2

  - node: wired
    entity: IntercomAssembly
    edges:
    - to: electronics
      steps:
      - tag: IntercomElectronics
        store: board
        name: "intercom electronics"
        icon:
          sprite: "Objects/Misc/module.rsi"
          state: "id_mod"
        doAfter: 1
    - to: assembly
      completed:
      - !type:GivePrototype
        prototype: CableApcStack1
        amount: 2
      - !type:VisualizerDataInt
        key: "enum.ConstructionVisuals.Layer"
        data: 0
      steps:
      - tool: Cutting
        doAfter: 1

  - node: electronics
    edges:
    - to: intercom
      steps:
      - tool: Screwing
        doAfter: 2

  - node: intercom
    entity: IntercomConstructed
    doNotReplaceInheritingEntities: true
    actions:
    - !type:SetWiresPanelSecurity
      wiresAccessible: true
    edges:
    - to: wired
      conditions:
      - !type:AllWiresCut {}
      - !type:WirePanel {}
      - !type:ContainerNotEmpty
        container: board
      completed:
      - !type:EmptyAllContainers
        pickup: true
        emptyAtUser: true
      steps:
      - tool: Prying
        doAfter: 1
    - to: intercomReinforced
      conditions:
      - !type:WirePanel
      steps:
      - material: Steel
        amount: 1
      - tool: Welding
        doAfter: 1

  - node: intercomReinforced
    actions:
    - !type:SetWiresPanelSecurity
      examine: wires-panel-component-on-examine-security-level2
      wiresAccessible: false
    edges:
    - to: intercom
      conditions:
      - !type:WirePanel
      completed:
      - !type:GivePrototype
        prototype: SheetSteel1
      steps:
      - tool: Welding
        doAfter: 5
