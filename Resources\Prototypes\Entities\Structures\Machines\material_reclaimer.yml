- type: entity
  parent: [ BaseMachinePowered, ConstructibleMachine ]
  id: MaterialReclaimer
  name: "переробник матеріалів"
  description: "Неможливо повернути нематеріальні речі, такі як мотивація."
  components:
  - type: Sprite
    sprite: Structures/Machines/material_reclaimer.rsi
    snapCardinals: true
    layers:
    - state: icon
      map: ["enum.LatheVisualLayers.IsRunning"]
    - state: gear-active
      map: ["enum.DamageStateVisualLayers.Base"]
    - state: unlit
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
    - state: fill-6
      map: ["enum.SolutionContainerLayers.Fill"]
      visible: false
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 6
    fillBaseName: fill-
  - type: WiresVisuals
  - type: GenericVisualizer
    visuals:
      enum.PowerDeviceVisuals.Powered:
        enum.DamageStateVisualLayers.Base:
          True: { state: gear-active}
          False: { state: gear-idle }
        enum.PowerDeviceVisualLayers.Powered:
          True: { visible: true }
          False: { visible: false }
  - type: LitOnPowered
  - type: PointLight
    radius: 1.5
    energy: 1.6
    enabled: false
    color: "#da824d"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
    offset: "0, 0.4"
    castShadows: false
  - type: PowerSwitch
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
      - !type:ChangeConstructionNodeBehavior
        node: machineFrame
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Machine
    board: MaterialReclaimerMachineCircuitboard
  - type: WiresPanel
  - type: MaterialReclaimer
    whitelist:
      components:
      - PhysicalComposition
      - SpaceGarbage
      tags:
      - Trash
      - Recyclable
    blacklist:
      components:
      - Material
      - Pda
      - IdCard
      - Brain
      tags:
      - HighRiskItem
    soundCooldown: 0
    sound:
      path: /Audio/Ambience/Objects/crushing.ogg
      params:
        volume: 5
        maxDistance: 5
        loop: true
  - type: MaterialStorage
    insertOnInteract: false
  - type: ContainerContainer
    containers:
      active-material-reclaimer-container: !type:Container
      machine_board: !type:Container
      machine_parts: !type:Container
  - type: SolutionContainerManager
    solutions:
      output:
        maxVol: 100
  - type: DrainableSolution
    solution: output
  - type: ExaminableSolution
    solution: output
  - type: StaticPrice
    price: 500
  - type: MaterialReclaimerMagnetPickup # Delta V - Summary: Adds magnet pull from Frontier
    magnetEnabled: True
    range: 0.30 # Delta V - End Magnet Pull
