﻿<controls:FancyWindow
    xmlns="https://spacestation14.io"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
    Title="{Loc silicon-law-ui-title}"
    MinSize="560 400"
>
    <!-->
    this shit does not layout properly unless I put the horizontal boxcontainer inside of a vertical one
    ????
    <!-->
    <BoxContainer Orientation="Vertical">
        <BoxContainer Orientation="Horizontal" Align="End">
            <Button Name="NewLawButton" Text="{Loc silicon-law-ui-new-law}" MaxSize="256 64" StyleClasses="OpenRight"></Button>
            <Button Name="Save" Text="{Loc silicon-law-ui-save}" MaxSize="256 64" Access="Public" StyleClasses="OpenLeft"></Button>
        </BoxContainer>
    </BoxContainer>
    <BoxContainer Orientation="Vertical" Margin="4 60 0 0">
        <ScrollContainer VerticalExpand="True" HorizontalExpand="True" HScrollEnabled="False">
            <BoxContainer Orientation="Vertical" Name="LawContainer" Access="Public" VerticalExpand="True" />
        </ScrollContainer>
    </BoxContainer>
</controls:FancyWindow>
