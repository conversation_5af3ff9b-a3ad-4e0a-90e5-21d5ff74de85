- type: entity
  parent: BaseComputer
  id: ComputerShipyard
  name: "консоль верфі"
  description: "Використовується для покупки та продажу шатлів"
  components:
  - type: ShipyardConsole
  - type: AccessReader
    access: [[ Captain ]]
  - type: ActivatableUI
    key: enum.ShipyardConsoleUiKey.Key
  - type: UserInterface
    interfaces:
      enum.ShipyardConsoleUiKey.Key:
        type: ShipyardConsoleBoundUserInterface
  - type: Computer
    board: ShipyardComputerCircuitboard
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#b89f25"
  - type: Sprite
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: request
    - map: ["computerLayerKeys"]
      state: tech_key
