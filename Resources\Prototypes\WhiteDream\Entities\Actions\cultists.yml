﻿- type: entity
  id: ActionBloodCultStun
  name: "Оглушення"
  description: "Дозволяє вашій руці оглушити і приглушити жертву при контакті."
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/actions.rsi
    state: stun
  - type: InstantAction
    itemIconStyle: BigAction
    charges: 1
    useDelay: 3
    temporary: true
    icon:
      sprite: WhiteDream/BloodCult/actions.rsi
      state: stun
    event: !type:SummonEquipmentEvent
      prototypes:
        hand1: StunAura
      force: false
  - type: BaseCultSpell

- type: entity
  id: ActionBloodCultTeleport
  name: "Телепортуйся"
  description: "Дозволяє вашій руці телепортувати себе або іншого культиста до руни телепорту при контакті."
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/actions.rsi
    state: teleport
  - type: EntityTargetAction
    checkCanAccess: false
    range: 3
    itemIconStyle: BigAction
    charges: 1
    temporary: true
    interactOnMiss: false
    whitelist:
      components:
      - BloodCultist
    icon:
      sprite: WhiteDream/BloodCult/actions.rsi
      state: teleport
    event: !type:BloodCultTeleportEvent
      speech: "Sas'so c'arta forbici"
  - type: BaseCultSpell

- type: entity
  id: ActionBloodCultEmp
  name: "Електромагнітний імпульс"
  description: "Випромінює великий електромагнітний імпульс."
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/actions.rsi
    state: create_emp
  - type: InstantAction
    itemIconStyle: BigAction
    charges: 1
    temporary: true
    icon:
      sprite: WhiteDream/BloodCult/actions.rsi
      state: create_emp
    event: !type:BloodCultEmpEvent
      speech: "Ta'gh fara'qha fel d'amar det!"
  - type: BaseCultSpell

- type: entity
  id: ActionBloodCultShadowShackles
  name: "Кайдани тіней"
  description: "Дозволяє вашій руці одягати наручники на жертву при контакті, а в разі успіху - вимикати звук."
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/actions.rsi
    state: shackles
  - type: InstantAction
    itemIconStyle: BigAction
    charges: 4
    useDelay: 3
    temporary: true
    icon:
      sprite: WhiteDream/BloodCult/actions.rsi
      state: shackles
    event: !type:SummonEquipmentEvent
      prototypes:
        hand1: ShadowShacklesAura
      force: false
  - type: BaseCultSpell

- type: entity
  id: ActionBloodCultTwistedConstruction
  name: "Кручена конструкція"
  description: "Дозволяє вашій руці пошкоджувати певні металеві предмети."
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/actions.rsi
    state: transmute
  - type: EntityTargetAction
    checkCanAccess: false
    range: 3
    itemIconStyle: BigAction
    charges: 1
    temporary: true
    canTargetSelf: false
    interactOnMiss: false
    whitelist:
      components:
      - TwistedConstructionTarget
    icon:
      sprite: WhiteDream/BloodCult/actions.rsi
      state: transmute
    event: !type:BloodCultTwistedConstructionEvent
      speech: "Ethra p'ni dedol!"
  - type: BaseCultSpell

- type: entity
  id: ActionBloodCultSummonCombatEquipment
  name: "Викликати бойове спорядження"
  description: "Дозволяє викликати бойове культове спорядження, зокрема культову броню, культову болу та культовий меч."
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/actions.rsi
    state: summon_combat_equipment
  - type: InstantAction
    itemIconStyle: BigAction
    charges: 1
    temporary: true
    icon:
      sprite: WhiteDream/BloodCult/actions.rsi
      state: summon_combat_equipment
    event: !type:SummonEquipmentEvent
      speech: "Wur d'dai leev'mai k'sagan!"
      prototypes:
        outerClothing: ClothingOuterCultArmor
        hand1: EldritchLongsword
        hand2: CultBola
  - type: BaseCultSpell

- type: entity
  id: ActionBloodCultSummonRitualDagger
  name: "Ритуальний кинджал виклику"
  description: "Дозволяє викликати ритуальний кинджал, якщо ви загубили подарований вам кинджал."
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/actions.rsi
    state: summon_dagger
  - type: InstantAction
    itemIconStyle: BigAction
    charges: 1
    temporary: true
    icon:
      sprite: WhiteDream/BloodCult/actions.rsi
      state: summon_dagger
    event: !type:SummonEquipmentEvent
      speech: "Wur d'dai leev'mai k'sagan!"
      prototypes:
        hand: RitualDagger
  - type: BaseCultSpell

- type: entity
  id: ActionBloodCultBloodRites
  name: "Криваві обряди"
  description: "Дозволяє вашій руці поглинати кров, щоб використовувати її для складних обрядів або зцілити культиста при контакті. Використовуйте заклинання в руці для проведення складних обрядів"
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/actions.rsi
    state: blood_rites
  - type: InstantAction
    itemIconStyle: BigAction
    charges: 1
    useDelay: 3
    temporary: true
    icon:
      sprite: WhiteDream/BloodCult/actions.rsi
      state: blood_rites
    event: !type:SummonEquipmentEvent
      speech: "Fel'th Dol Ab'orod!"
      prototypes:
        hand1: BloodRitesAura
      force: false
  - type: BaseCultSpell

- type: entity
  id: ActionSummonCultFloor
  name: "Викликати культовий поверх"
  description: "Це заклинання будує культову підлогу."
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/actions.rsi
    state: create_cult_floor
  - type: WorldTargetAction
    useDelay: 10
    range: 5
    itemIconStyle: BigAction
    checkCanAccess: false
    icon:
      sprite: WhiteDream/BloodCult/actions.rsi
      state: create_cult_floor
    event: !type:PlaceTileEntityEvent
      tileId: CultFloor
      audio:  !type:SoundPathSpecifier
        path: /Audio/WhiteDream/BloodCult/curse.ogg
  - type: BaseCultSpell

- type: entity
  id: ActionLesserConstruction
  name: "Мале будівництво"
  description: "Це заклинання будує культову стіну."
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/actions.rsi
    state: lesser_construct
  - type: WorldTargetAction
    useDelay: 20
    range: 5
    itemIconStyle: BigAction
    checkCanAccess: false
    icon:
      sprite: WhiteDream/BloodCult/actions.rsi
      state: lesser_construct
    event: !type:PlaceTileEntityEvent
      entity: WallCult
      audio:  !type:SoundPathSpecifier
        path: /Audio/WhiteDream/BloodCult/curse.ogg
  - type: BaseCultSpell

- type: entity
  id: ActionSummonCultDoor
  name: "Викликати Культові Двері"
  description: "Це заклинання створює культові двері."
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/actions.rsi
    state: lesser_construct
  - type: WorldTargetAction
    useDelay: 30
    range: 5
    itemIconStyle: BigAction
    checkCanAccess: false
    icon:
      sprite: WhiteDream/BloodCult/actions.rsi
      state: lesser_construct
    event: !type:PlaceTileEntityEvent
      entity: CultDoor
      audio:  !type:SoundPathSpecifier
        path: /Audio/WhiteDream/BloodCult/curse.ogg
  - type: BaseCultSpell

- type: entity
  id: ActionSummonSoulStone
  name: "Викликати Соулшард"
  description: "Це заклинання проникає в царство Нар'сі, викликаючи один з легендарних фрагментів крізь час і простір."
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/actions.rsi
    state: create_soul_stone
  - type: WorldTargetAction
    useDelay: 30
    range: 5
    itemIconStyle: BigAction
    checkCanAccess: false
    icon:
      sprite: WhiteDream/BloodCult/actions.rsi
      state: create_soul_stone
    event: !type:PlaceTileEntityEvent
      entity: SoulShardGhost
      audio:  !type:SoundPathSpecifier
        path: /Audio/WhiteDream/BloodCult/curse.ogg
  - type: BaseCultSpell

- type: entity
  id: ActionSummonSoulStoneHoly
  name: "Викликати Святий Соулшард"
  description: "Це заклинання проникає в царство Нар'сі, викликаючи один з легендарних фрагментів крізь час і простір."
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/actions.rsi
    state: create_soul_stone
  - type: WorldTargetAction
    useDelay: 30
    range: 5
    itemIconStyle: BigAction
    checkCanAccess: false
    icon:
      sprite: WhiteDream/BloodCult/actions.rsi
      state: create_soul_stone
    event: !type:PlaceTileEntityEvent
      entity: SoulShardHolyGhost
      audio:  !type:SoundPathSpecifier
        path: /Audio/WhiteDream/BloodCult/curse.ogg
  - type: BaseCultSpell

- type: entity
  id: ActionForceWallCult
  name: "Щит"
  description: "Це заклинання створює тимчасове силове поле, щоб захистити себе і союзників від вогню."
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/actions.rsi
    state: summon_force_wall
  - type: InstantAction
    useDelay: 40
    itemIconStyle: BigAction
    sound: !type:SoundPathSpecifier
      path: /Audio/Magic/forcewall.ogg
    icon:
      sprite: WhiteDream/BloodCult/actions.rsi
      state: summon_force_wall
    event: !type:InstantSpawnSpellEvent
      prototype: WallForceCult
      posData: !type:TargetInFront
  - type: BaseCultSpell

- type: entity
  id: ActionPhaseShift
  name: "Зсув фаз"
  description: "Це заклинання дозволяє проходити крізь стіни."
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/actions.rsi
    state: phase_shift
  - type: InstantAction
    itemIconStyle: BigAction
    useDelay: 30
    icon:
      sprite: WhiteDream/BloodCult/actions.rsi
      state: phase_shift
    event: !type:PhaseShiftEvent
  - type: BaseCultSpell

- type: entity
  id: ActionGauntletEcho
  name: "Рукавичка Ехо"
  description: "Спрямовує енергію у вашу рукавичку - вистрілює своєю сутністю вперед у повільній, але руйнівній атаці"
  categories: [ HideSpawnMenu ]
  components:
  - type: WorldTargetAction
    useDelay: 30
    itemIconStyle: BigAction
    checkCanAccess: false
    raiseOnUser: true
    range: 15
    sound: !type:SoundPathSpecifier
      path: /Audio/WhiteDream/BloodCult/resonator_blast.ogg
    icon:
      sprite: WhiteDream/BloodCult/actions.rsi
      state: gauntlet_echo
    event: !type:ProjectileSpellEvent
      prototype: ProjectileGauntlet
  - type: BaseCultSpell
