- type: constructionGraph
  id: Spear
  start: start
  graph:
    - node: start
      edges:
        - to: spear
          steps:
            - material: MetalRod
              amount: 2
              doAfter: 2
            - material: Cable
              amount: 3
              doAfter: 1
            - tag: GlassShard
              name: Glass Shard
              icon:
                sprite: Objects/Materials/Shards/shard.rsi
                state: shard1
              doAfter: 1
    - node: spear
      entity: Spear

- type: constructionGraph
  id: SpearReinforced
  start: start
  graph:
    - node: start
      edges:
        - to: spear
          steps:
            - material: MetalRod
              amount: 2
              doAfter: 2
            - material: Cable
              amount: 3
              doAfter: 1
            - tag: ReinforcedGlassShard
              name: Reinforced Glass Shard
              icon:
                sprite: Objects/Materials/Shards/shard.rsi
                state: shard1
              doAfter: 1
    - node: spear
      entity: SpearReinforced

- type: constructionGraph
  id: SpearPlasma
  start: start
  graph:
    - node: start
      edges:
        - to: spear
          steps:
            - material: MetalRod
              amount: 2
              doAfter: 2
            - material: Cable
              amount: 3
              doAfter: 1
            - tag: PlasmaGlassShard
              name: Plasma Glass Shard
              icon:
                sprite: Objects/Materials/Shards/shard.rsi
                state: shard1
              doAfter: 1
    - node: spear
      entity: SpearPlasma

- type: constructionGraph
  id: SpearUranium
  start: start
  graph:
    - node: start
      edges:
        - to: spear
          steps:
            - material: MetalRod
              amount: 2
              doAfter: 2
            - material: Cable
              amount: 3
              doAfter: 1
            - tag: UraniumGlassShard
              name: Uranium Glass Shard
              icon:
                sprite: Objects/Materials/Shards/shard.rsi
                state: shard1
              doAfter: 1
    - node: spear
      entity: SpearUranium

- type: constructionGraph
  id: SpearBone
  start: start
  graph:
    - node: start
      edges:
        - to: spear
          steps:
            - material: Bones
              amount: 4
              doAfter: 2
            - material: WebSilk
              amount: 1
              doAfter: 1
    - node: spear
      entity: SpearBone
