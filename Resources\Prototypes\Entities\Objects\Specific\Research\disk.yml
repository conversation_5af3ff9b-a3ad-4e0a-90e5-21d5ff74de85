- type: entity
  parent: BaseItem
  id: ResearchDisk
  name: "диск з дослідницькими точками (1000)"
  description: "Диск для R&D сервера, що містить 1000 балів."
  components:
  - type: Sprite
    sprite: Objects/Specific/Research/researchdisk.rsi
    state: icon
  - type: ResearchDisk
  - type: GuideHelp
    guides:
    - Science
  - type: EmitSoundOnPickup
    sound: /Audio/SimpleStation14/Items/Handling/disk_pickup.ogg
  - type: EmitSoundOnDrop
    sound: /Audio/SimpleStation14/Items/Handling/disk_drop.ogg
  - type: EmitSoundOnLand
    sound: /Audio/SimpleStation14/Items/Handling/disk_drop.ogg

- type: entity
  parent: ResearchDisk
  id: ResearchDisk5000
  name: "диск з дослідницькою точкою (5000)"
  description: "Диск для R&D сервера, що містить 5000 балів."
  components:
  - type: ResearchDisk
    points: 5000

- type: entity
  parent: ResearchDisk
  id: ResearchDisk10000
  name: "диск дослідницької точки (10000)"
  description: "Диск для R&D сервера, що містить 10000 балів."
  components:
  - type: ResearchDisk
    points: 10000

- type: entity
  parent: ResearchDisk
  id: ResearchDisk20000
  name: "диск дослідницької точки (20000)"
  description: "Диск для R&D сервера, що містить 20000 балів."
  components:
  - type: ResearchDisk
    points: 20000

- type: entity
  parent: ResearchDisk
  id: ResearchDisk50000
  name: "диск дослідницьких балів (50000)"
  description: "Диск для сервера досліджень і розробок, що містить 50000 балів."
  components:
  - type: ResearchDisk
    points: 50000

- type: entity
  parent: ResearchDisk
  id: ResearchDiskDebug
  name: "диск дослідницької точки"
  suffix: DEBUG, DO NOT MAP
  description: "Диск для R&D сервера з усіма пунктами, які можуть вам коли-небудь знадобитися."
  components:
  - type: ResearchDisk
    unlockAllTech: true

- type: entity
  parent: BaseItem
  id: TechnologyDisk
  name: "технологічний диск"
  description: "Диск для сервера відділу вчених, що містить технологію для досліду."
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    layers:
    - state: datadisk_base
      map: ["enum.DamageStateVisualLayers.Base"]
    - state: datadisk_label
  - type: RandomSprite
    available:
    - enum.DamageStateVisualLayers.Base:
        datadisk_base: Sixteen
  - type: TechnologyDisk
  - type: StaticPrice
    price: 100
  - type: StealTarget
    stealGroup: TechnologyDisk

- type: entity
  parent: TechnologyDisk
  id: TechnologyDiskRare
  suffix: rare.
  components:
  - type: TechnologyDisk
    tierWeightPrototype: RareTechDiskTierWeights
  - type: EmitSoundOnPickup
    sound: /Audio/SimpleStation14/Items/Handling/disk_pickup.ogg
  - type: EmitSoundOnDrop
    sound: /Audio/SimpleStation14/Items/Handling/disk_drop.ogg
  - type: EmitSoundOnLand
    sound: /Audio/SimpleStation14/Items/Handling/disk_drop.ogg
