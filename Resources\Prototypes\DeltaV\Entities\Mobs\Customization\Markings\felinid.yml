- type: marking
  id: FelinidFluffyTailRings
  bodyPart: Tail
  markingCategory: Tail
  speciesRestriction: [<PERSON><PERSON><PERSON>, Human, Tajaran] # Einstein Engines - Tajaran
  sprites:
  - sprite: DeltaV/Mobs/Customization/Felinid/felinid_tails.rsi
    state: Felinid_fluffy_tail_full
  - sprite: DeltaV/Mobs/Customization/Felinid/felinid_tails.rsi
    state: felinid_fluffy_tail_rings

- type: marking
  id: FelinidFluffyTail
  bodyPart: Tail
  markingCategory: Tail
  speciesRestriction: [<PERSON>linid, Human, Tajaran] # Einstein Engines - Tajaran
  sprites:
  - sprite: DeltaV/Mobs/Customization/Felinid/felinid_tails.rsi
    state: Felinid_fluffy_tail_full

- type: marking
  id: FelinidAlternativeTail
  bodyPart: Tail
  markingCategory: Tail
  speciesRestriction: [Felinid, Human, Tajaran] # Einstein Engines - Tajaran
  sprites:
    - sprite: DeltaV/Mobs/Customization/Felinid/alternative_tail.rsi
      state: m_waggingtail_cat_FRONT

- type: marking
  id: FelinidTiger
  bodyPart: Tail
  markingCategory: Tail
  speciesRestriction: [<PERSON><PERSON><PERSON>, <PERSON>, Tajaran] # Einstein Engines - Tajaran
  sprites:
    - sprite: DeltaV/Mobs/Customization/Felinid/tiger_tail.rsi
      state: m_tail_tiger_primary
    - sprite: DeltaV/Mobs/Customization/Felinid/tiger_tail.rsi
      state: m_tail_tiger_secondary
    - sprite: DeltaV/Mobs/Customization/Felinid/tiger_tail.rsi
      state: m_tail_tiger_tertiary
