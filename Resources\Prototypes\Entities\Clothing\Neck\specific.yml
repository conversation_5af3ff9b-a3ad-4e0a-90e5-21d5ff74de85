﻿- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckChameleon
  name: "смугастий червоний шарф"
  description: "Стильний смугастий червоний шарф. Ідеальний зимовий аксесуар для тих, хто стежить за модою, і тих, хто просто не може терпіти холодний вітерець на шиї."
  suffix: Chameleon
  components:
    - type: Tag
      tags: [] # ignore "WhitelistChameleon" tag
    - type: Sprite
      sprite: Clothing/Neck/Scarfs/red.rsi
    - type: Clothing
      sprite: Clothing/Neck/Scarfs/red.rsi
    - type: ChameleonClothing
      slot: [neck]
      default: ClothingNeckScarfStripedRed
    - type: UserInterface
      interfaces:
        enum.ChameleonUiKey.Key:
          type: ChameleonBoundUserInterface
