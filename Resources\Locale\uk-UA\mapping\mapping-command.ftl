cmd-mapping-desc = Створює або завантажує мапу і телепортує вас на неї.
cmd-mapping-help = Використання: відображення [MapID] [Шлях] [MapID] [Шлях]
cmd-mapping-server = Тільки гравці можуть використовувати цю команду.
cmd-mapping-error = При створенні нової мапи сталася помилка.
cmd-mapping-success-load = Створено неініціалізовану карту з файлу {$path} з ідентифікатором {$mapId}.
cmd-mapping-success = Створено неініціалізовану карту з ідентифікатором {$mapId}.
cmd-mapping-warning = ПОПЕРЕДЖЕННЯ: На сервері використовується налагоджувальна збірка. Ви ризикуєте втратити свої зміни.


# дублює текст з команд завантаження/збереження мапи.
# I CBF зробити цей PR залежним від того.
cmd-mapping-failure-integer = {$arg} не є дійсним інтегром.
cmd-mapping-failure-float = {$arg} не є дійсним флоутом.
cmd-mapping-failure-bool = {$arg} не є допустимим bool.
cmd-mapping-nullspace = Ви не можете завантажити в карту 0.
cmd-hint-mapping-id = [MapID]
cmd-hint-mapping-path = [Шлях]
cmd-mapping-exists = Карта {$mapId} вже існує.
