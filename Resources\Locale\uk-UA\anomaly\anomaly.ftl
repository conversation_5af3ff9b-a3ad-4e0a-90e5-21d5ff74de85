anomaly-component-contact-damage = Аномалія обпікає вашу шкіру!

anomaly-vessel-component-anomaly-assigned = Аномалія призначена контейнеру.
anomaly-vessel-component-not-assigned = Цей контейнер не віднесений до жодної аномалії. Спробуйте скористатися сканером.
anomaly-vessel-component-assigned = Цей контейнер наразі віднесена до аномалії.
anomaly-vessel-component-upgrade-output = точковий вихід

anomaly-particles-delta = Дельта-частинки
anomaly-particles-epsilon = Епсилон-частинки
anomaly-particles-zeta = Зета-частинки
anomaly-particles-omega = Омега-частинки

anomaly-scanner-component-scan-complete = Сканування завершено!

anomaly-scanner-ui-title = сканер аномалій
anomaly-scanner-no-anomaly = Наразі жодна аномалія не сканується.
anomaly-scanner-severity-percentage = Поточна серйозність: [color=gray]{$percent}[/color]
anomaly-scanner-stability-low = Поточний стан аномалії: [color=gold]Згасає[/color]
anomaly-scanner-stability-medium = Поточний стан аномалії: [color=forestgreen]Стабільний[/color]
anomaly-scanner-stability-high = Поточний стан аномалії: [color=crimson]Зростає[/color]
anomaly-scanner-point-output = Вихід точки: [color=gray]{$point}[/color]
anomaly-scanner-particle-readout = Аналіз Реакції Частинок:
anomaly-scanner-particle-danger = - [color=crimson]Тип небезпеки:[/color] {$type}
anomaly-scanner-particle-unstable = - [color=plum]Тип нестабільності:[/color] {$type}
anomaly-scanner-particle-containment = - [color=goldenrod]Тип утримання:[/color] {$type}
anomaly-scanner-pulse-timer = Час до наступного імпульсу: [color=gray]{$time}[/color]

anomaly-gorilla-core-slot-name = Ядро аномалії
anomaly-gorilla-charge-none = Воно не має [bold]аномального ядра[/bold] всередині.
anomaly-gorilla-charge-limit = Воно має [color={$count ->
    [3]зелений
    [2]жовтий
    [1]помаранчевий
    [0]червоний
    *[other]фіолетовий
}]{$count} {$count ->
    [one]заряд
    *[other]заряди
}[/color] залишилося.
anomaly-gorilla-charge-infinite = Воно має [color=gold]нескінченні заряди[/color]. [italic]Поки що...[/italic]

anomaly-sync-connected = Аномалію успішно приєднано
anomaly-sync-disconnected = Зʼєднання з аномалією втрачено!
anomaly-sync-no-anomaly = Нема аномалії в радіусі.
anomaly-sync-examine-connected = Його [color=darkgreen]приєднано[/color] до аномалії.
anomaly-sync-examine-not-connected = Його [color=darkred]не приєднано[/color] до аномалії.
anomaly-sync-connect-verb-text = Приєднати аномалію
anomaly-sync-connect-verb-message = Приєднати найближчу аномалію до {THE($machine)}.

anomaly-generator-ui-title = Генератор Аномалій
anomaly-generator-fuel-display = Паливо:
anomaly-generator-cooldown = Охолодження: [color=gray]{$time}[/color]
anomaly-generator-no-cooldown = Охолодження: [color=gray]Повний[/color]
anomaly-generator-yes-fire = Статус: [color=forestgreen]Готовий[/color]
anomaly-generator-no-fire = Статус: [color=crimson]Не готовий[/color]
anomaly-generator-generate = Створити Аномалію
anomaly-generator-charges = {$charges ->
    [one] {$charges} заряд
    *[other] {$charges} зарядів
}
anomaly-generator-announcement = Виникла аномалія!

anomaly-command-pulse = Пульсує цільову аномалію
anomaly-command-supercritical = Робить цільову аномалію надкритичною

# Flavor text on the footer
anomaly-generator-flavor-left = Аномалія може породжуватися всередині оператора.
anomaly-generator-flavor-right = v1.1

anomaly-particles-sigma = Сигма-частинки
anomaly-scanner-severity-percentage-unknown = Поточний ступінь тяжкості: [color=red]ПОМИЛКА[/color]
anomaly-scanner-stability-unknown = Поточний стан аномалії: [color=red]ПОМИЛКА[/color]
anomaly-scanner-point-output-unknown = Точковий вихід: [color=red]ERROR[/color]
anomaly-scanner-particle-transformation = - [color=#6b75fa]Тип перетворення:[/color] {$type}
anomaly-scanner-particle-danger-unknown = - [color=crimson]Тип небезпеки:[/color] [color=red]ПОМИЛКА[/color]
anomaly-scanner-particle-unstable-unknown = - [color=plum]Нестабільний тип:[/color] [color=red]ПОМИЛКА[/color]
anomaly-scanner-particle-containment-unknown = - [color=goldenrod]Тип вмісту:[/color] [color=red]ПОМИЛКА[/color]
anomaly-scanner-particle-transformation-unknown = - [color=#6b75fa]Тип перетворення:[/color] [color=red]ПОМИЛКА[/color]
anomaly-behavior-unknown = [color=red]ПОМИЛКА. Неможливо прочитати.[/color]
anomaly-behavior-title = аналіз відхилень у поведінці:
anomaly-behavior-point = [color=gold]Аномалія приносить {$mod}% очок[/color]
anomaly-behavior-safe = [color=forestgreen]Аномалія надзвичайно стабільна. Вкрай рідкісні пульсації.[/color]
anomaly-behavior-slow = [color=forestgreen]Частота пульсацій набагато менша[/color]
anomaly-behavior-light = [color=forestgreen]Потужність пульсації значно зменшується[/color]
anomaly-behavior-balanced = Відхилень у поведінці не виявлено.
anomaly-behavior-delayed-force = Частота пульсацій значно зменшується, але їхня потужність збільшується.
anomaly-behavior-rapid = Частота пульсації набагато вища, але її сила ослаблена.
anomaly-behavior-reflect = Виявлено захисне покриття.
anomaly-behavior-nonsensivity = Виявлено слабку реакцію на частинки.
anomaly-behavior-sensivity = Виявлено посилену реакцію на частинки.
anomaly-behavior-secret = Виявлено перешкоди. Не вдається прочитати деякі дані
anomaly-behavior-inconstancy = [color=crimson]Виявлено мінливість. Типи частинок можуть змінюватися з часом[/color]
anomaly-behavior-fast = [color=crimson]Частота пульсації сильно збільшена[/color]
anomaly-behavior-strenght = [color=crimson]Потужність пульсації значно збільшена[/color]
anomaly-behavior-moving = [color=crimson]Виявлено нестабільність координат[/color]
anomaly-scanner-scan-copied = Скановані дані аномалії скопійовано!
