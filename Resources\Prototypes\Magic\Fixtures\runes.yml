﻿- type: entity
  id: BaseRune
  name: "руна"
  abstract: true
  placement:
    mode: SnapgridCenter
  components:
    - type: Clickable
    - type: Sprite
      sprite: Structures/Magic/Cult/rune.rsi
      layers:
        - state: cult2
          color: '#FF00FF'

- type: entity
  parent: BaseRune
  id: CollideRune
  name: "руна зіткнення"
  abstract: true
  components:
    - type: Fixtures
      fixtures:
        rune:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.4,-0.4,0.4,0.4"
          hard: false
          mask:
            - ItemMask
          layer:
            - SlipLayer
    - type: Physics

- type: entity
  parent: CollideRune
  id: ActivateRune
  name: "руна активації"
  abstract: true
  components:
    - type: TriggerOnActivate

- type: entity
  parent: CollideRune
  id: CollideTimerRune
  name: "руна часового зіткнення"
  abstract: true
  components:
    - type: TriggerOnTimedCollide
      threshold: 5

- type: entity
  parent: CollideRune
  id: ExplosionRune
  name: "руна вибуху"
  components:
    - type: TriggerOnCollide
      fixtureID: rune
    - type: ExplodeOnTrigger
    - type: Explosive
      explosionType: Cryo
      totalIntensity: 20.0
      intensitySlope: 5
      maxIntensity: 4
    - type: Sprite
      sprite: Structures/Magic/Cult/trap.rsi
      layers:
        - state: trap
          color: '#FF770055'

- type: entity
  parent: CollideRune
  id: StunRune
  name: "руна оглушення"
  components:
    - type: TriggerOnCollide
      fixtureID: rune
    - type: DeleteOnTrigger
    - type: StunOnCollide
      stunAmount: 5
      knockdownAmount: 3
    - type: Sprite
      sprite: Structures/Magic/Cult/trap.rsi
      layers:
        - state: trap
          color: '#FFFF0055'

- type: entity
  parent: CollideRune
  id: IgniteRune
  name: "руна запалення"
  components:
    - type: TriggerOnCollide
      fixtureID: ignition
    - type: Fixtures
      fixtures:
        ignition:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.4,-0.4,0.4,0.4"
          hard: false
          mask:
          - ItemMask
          layer:
          - SlipLayer
    - type: DeleteOnTrigger
    - type: IgniteOnCollide
      fireStacks: 5
    - type: Sprite
      sprite: Structures/Magic/Cult/trap.rsi
      layers:
        - state: trap
          color: '#FF000055'

- type: entity
  parent: CollideTimerRune
  id: ExplosionTimedRune
  name: "руна часового вибуху"
  components:
    - type: ExplodeOnTrigger
    - type: Explosive
      explosionType: Cryo
      totalIntensity: 20.0
      intensitySlope: 5
      maxIntensity: 4

- type: entity
  parent: ActivateRune
  id: ExplosionActivateRune
  name: "руна активованого вибуху"
  components:
    - type: ExplodeOnTrigger
    - type: Explosive
      explosionType: Cryo
      totalIntensity: 20.0
      intensitySlope: 5
      maxIntensity: 4

- type: entity
  parent: ActivateRune
  id: FlashRune
  name: "руна спалаху"
  components:
    - type: FlashOnTrigger
    - type: DeleteOnTrigger

- type: entity
  parent: CollideTimerRune
  id: FlashRuneTimer
  name: "руна часового спалаху"
  components:
    - type: FlashOnTrigger
