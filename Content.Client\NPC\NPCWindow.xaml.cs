using Content.Client.NPC.HTN;
using Content.Client.UserInterface.Controls;
using Content.Shared.NPC;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.NPC;

[GenerateTypedNameReferences]
public sealed partial class NPCWindow : FancyWindow
{
    public NPCWindow()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
        var sysManager = IoCManager.Resolve<IEntitySystemManager>();
        var htn = sysManager.GetEntitySystem<HTNSystem>();
        var path = sysManager.GetEntitySystem<PathfindingSystem>();

        NPCThonk.Pressed = htn.EnableOverlay;
        PathCrumbs.Pressed = (path.Modes & PathfindingDebugMode.Breadcrumbs) != 0x0;
        PathPolys.Pressed = (path.Modes & PathfindingDebugMode.Polys) != 0x0;
        PathNeighbors.Pressed = (path.Modes & PathfindingDebugMode.PolyNeighbors) != 0x0;
        PathRouteCosts.Pressed = (path.Modes & PathfindingDebugMode.RouteCosts) != 0x0;
        PathRoutes.Pressed = (path.Modes & PathfindingDebugMode.Routes) != 0x0;

        NPCThonk.OnToggled += args => htn.EnableOverlay = args.Pressed;
        PathCrumbs.OnToggled += args => path.Modes ^= PathfindingDebugMode.Breadcrumbs;
        PathPolys.OnToggled += args => path.Modes ^= PathfindingDebugMode.Polys;
        PathNeighbors.OnToggled += args => path.Modes ^= PathfindingDebugMode.PolyNeighbors;
        PathRouteCosts.OnToggled += args => path.Modes ^= PathfindingDebugMode.RouteCosts;
        PathRoutes.OnToggled += args => path.Modes ^= PathfindingDebugMode.Routes;
    }
}
