﻿- type: decal
  id: Markings
  abstract: true

- type: decal
  id: Arrows
  parent: Markings
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: arrows

- type: decal
  id: ArrowsGreyscale
  parent: Markings
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: arrows_greyscale

- type: decal
  id: Bots
  parent: Markings
  abstract: true

- type: decal
  id: Bot
  parent: Bots
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: bot

- type: decal
  id: BotGreyscale
  parent: Bots
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: bot_greyscale

- type: decal
  id: BotLeft
  parent: Bots
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: bot_left

- type: decal
  id: BotLeftGreyscale
  parent: Bots
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: bot_left_greyscale

- type: decal
  id: BotRight
  parent: Bots
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: bot_right

- type: decal
  id: BotRightGreyscale
  parent: Bots
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: bot_right_greyscale

- type: decal
  id: Box
  parent: Markings
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: box

- type: decal
  id: BoxGreyscale
  parent: Markings
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: box_greyscale

- type: decal
  id: Caution
  parent: Markings
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: caution

- type: decal
  id: CautionGreyscale
  parent: Markings
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: caution_greyscale

- type: decal
  id: Delivery
  parent: Markings
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: delivery

- type: decal
  id: DeliveryGreyscale
  parent: Markings
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: delivery_greyscale

- type: decal
  id: LoadingArea
  parent: Markings
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: loading_area

- type: decal
  id: LoadingAreaGreyscale
  parent: Markings
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: loading_area_greyscale

- type: decal
  id: StandClear
  parent: Markings
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: standclear

- type: decal
  id: StandClearGreyscale
  parent: Markings
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: standclear_greyscale

- type: decal
  id: Warn
  parent: Markings
  abstract: true

- type: decal
  id: WarnBox
  parent: Warn
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_box

- type: decal
  id: WarnBoxGreyscale
  parent: Warn
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: warn_box_greyscale

# TODO: Remove from here to...

- type: decal
  id: WarnEnd
  showMenu: false
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_end

- type: decal
  id: WarnEndGreyscale
  showMenu: false
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_end_greyscale

# TODO: Down here

- type: decal
  id: WarnFull
  parent: Warn
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_full

- type: decal
  id: WarnFullGreyscale
  parent: Warn
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: warn_full_greyscale

# TODO: remove from here down to...

- type: decal
  id: WarningLine
  showMenu: false
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warningline

- type: decal
  id: WarningLineGreyscale
  showMenu: false
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warningline_greyscale

- type: decal
  id: WarningLineCorner
  showMenu: false
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warninglinecorner

- type: decal
  id: WarningLineCornerGreyscale
  showMenu: false
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warninglinecorner_greyscale

- type: decal
  id: WarningLineCornerFlipped
  showMenu: false
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warninglinecorner_flipped

- type: decal
  id: WarningLineCornerFlippedGreyscale
  showMenu: false
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warninglinecorner_flipped_greyscale

- type: decal
  id: WarnCorner
  showMenu: false
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_corner

- type: decal
  id: WarnCornerGreyscale
  showMenu: false
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_corner_greyscale

- type: decal
  id: WarnCornerFlipped
  showMenu: false
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_corner_flipped

- type: decal
  id: WarnCornerFlippedGreyscale
  showMenu: false
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_corner_flipped_greyscale

# TODO: here

- type: decal
  id: WarnCornerGreyscaleNE
  parent: Warn
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: warn_corner_greyscale_ne

- type: decal
  id: WarnCornerGreyscaleNW
  parent: Warn
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: warn_corner_greyscale_nw

- type: decal
  id: WarnCornerGreyscaleSE
  parent: Warn
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: warn_corner_greyscale_se

- type: decal
  id: WarnCornerGreyscaleSW
  parent: Warn
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: warn_corner_greyscale_sw

- type: decal
  id: WarnCornerNE
  parent: Warn
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_corner_ne

- type: decal
  id: WarnCornerNW
  parent: Warn
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_corner_nw

- type: decal
  id: WarnCornerSE
  parent: Warn
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_corner_se

- type: decal
  id: WarnCornerSW
  parent: Warn
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_corner_sw

- type: decal
  id: WarnCornerSmallGreyscaleNE
  parent: Warn
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: warn_corner_small_greyscale_ne

- type: decal
  id: WarnCornerSmallGreyscaleNW
  parent: Warn
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: warn_corner_small_greyscale_nw

- type: decal
  id: WarnCornerSmallGreyscaleSE
  parent: Warn
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: warn_corner_small_greyscale_se

- type: decal
  id: WarnCornerSmallGreyscaleSW
  parent: Warn
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: warn_corner_small_greyscale_sw

- type: decal
  id: WarnCornerSmallNE
  parent: Warn
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_corner_small_ne

- type: decal
  id: WarnCornerSmallNW
  parent: Warn
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_corner_small_nw

- type: decal
  id: WarnCornerSmallSE
  parent: Warn
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_corner_small_se

- type: decal
  id: WarnCornerSmallSW
  parent: Warn
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_corner_small_sw

- type: decal
  id: WarnEndE
  parent: Warn
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_end_e

- type: decal
  id: WarnEndN
  parent: Warn
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_end_n

- type: decal
  id: WarnEndS
  parent: Warn
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_end_s

- type: decal
  id: WarnEndW
  parent: Warn
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_end_w

- type: decal
  id: WarnEndGreyscaleE
  parent: Warn
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: warn_end_greyscale_e

- type: decal
  id: WarnEndGreyscaleN
  parent: Warn
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: warn_end_greyscale_n

- type: decal
  id: WarnEndGreyscaleS
  parent: Warn
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: warn_end_greyscale_s

- type: decal
  id: WarnEndGreyscaleW
  parent: Warn
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: warn_end_greyscale_w

- type: decal
  id: WarnLineE
  parent: Warn
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_line_e

- type: decal
  id: WarnLineW
  parent: Warn
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_line_n

- type: decal
  id: WarnLineN
  parent: Warn
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_line_s

- type: decal
  id: WarnLineS
  parent: Warn
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: warn_line_w

- type: decal
  id: WarnLineGreyscaleE
  parent: Warn
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: warn_line_greyscale_e

- type: decal
  id: WarnLineGreyscaleN
  parent: Warn
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: warn_line_greyscale_n

- type: decal
  id: WarnLineGreyscaleS
  parent: Warn
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: warn_line_greyscale_s

- type: decal
  id: WarnLineGreyscaleW
  parent: Warn
  tags: ["station", "markings"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/markings.rsi
    state: warn_line_greyscale_w

- type: decal
  id: HatchSmall
  parent: Markings
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: hatch_small

- type: decal
  id: VentSmall
  parent: Markings
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/markings.rsi
    state: vent_small

