# TODO: Add descriptions (many)
# TODO BODY: Part damage
- type: entity
  id: PartReptilian
  parent: [BaseItem, BasePart]
  name: "частина тіла рептилії"
  abstract: true
  components:
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 3
      - ReagentId: Blood
        Quantity: 10

- type: entity
  id: TorsoReptilian
  name: "тулуб рептилії"
  parent: [PartReptilian, BaseTorso]
  components:
  - type: Sprite
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: "torso_m"
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 10
      - ReagentId: Blood
        Quantity: 20

- type: entity
  id: HeadReptilian
  name: "голова рептилії"
  parent: [PartReptilian, BaseHead]
  components:
  - type: Sprite
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: "head_m"
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 5
      - ReagentId: Blood
        Quantity: 10

- type: entity
  id: LeftArmReptilian
  name: "ліва рука рептилії"
  parent: [PartReptilian, BaseLeftArm]
  components:
  - type: Sprite
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: "l_arm"

- type: entity
  id: RightArmReptilian
  name: "права рука рептилії"
  parent: [PartReptilian, BaseRightArm]
  components:
  - type: Sprite
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: "r_arm"

- type: entity
  id: LeftHandReptilian
  name: "ліва долоня рептилії"
  parent: [PartReptilian, BaseLeftHand]
  components:
  - type: Sprite
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: "l_hand"

- type: entity
  id: RightHandReptilian
  name: "права долоня рептилії"
  parent: [PartReptilian, BaseRightHand]
  components:
  - type: Sprite
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: "r_hand"

- type: entity
  id: LeftLegReptilian
  name: "ліва нога рептилії"
  parent: [PartReptilian, BaseLeftLeg]
  components:
  - type: Sprite
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: "l_leg"

- type: entity
  id: RightLegReptilian
  name: "права нога рептилії"
  parent: [PartReptilian, BaseRightLeg]
  components:
  - type: Sprite
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: "r_leg"

- type: entity
  id: LeftFootReptilian
  name: "ліва стопа рептилії"
  parent: [PartReptilian, BaseLeftFoot]
  components:
  - type: Sprite
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: "l_foot"

- type: entity
  id: RightFootReptilian
  name: "права стопа рептилії"
  parent: [PartReptilian, BaseRightFoot]
  components:
  - type: Sprite
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: "r_foot"
