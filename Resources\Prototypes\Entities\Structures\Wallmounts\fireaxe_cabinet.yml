- type: entity
  id: FireAxeCabinet
  name: "шафа для пожежних сокир"
  description: "На сокирі є невеличка етикетка з написом \"Тільки для екстреного використання\", а також детальна інформація про безпечне використання сокири. Начебто."
  components:
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Glass
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 300
        behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 200 #20ish crowbar hits
        behaviors:
        - !type:EmptyAllContainersBehaviour
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
        - !type:PlaySoundBehavior
          sound:
            collection: MetalGlassBreak
  - type: MeleeSound
    soundGroups:
      Brute:
        collection: GlassSmash
  - type: WallMount
  - type: Clickable
  - type: InteractionOutline
  - type: Sprite
    sprite: Structures/Wallmounts/fireaxe_cabinet.rsi
    layers:
    - state: cabinet
    - state: fireaxe
      map: ["enum.ItemCabinetVisualLayers.ContainsItem"]
      visible: true
    - state: glass
      map: ["enum.ItemCabinetVisualLayers.Door"]
  - type: ItemCabinet
    cabinetSlot:
      ejectOnInteract: true
      whitelist:
        tags:
        - FireAxe
    doorSound:
      path: /Audio/Machines/machine_switch.ogg
    openState: glass-up
    closedState: glass
  - type: Appearance
  - type: Lock
  - type: AccessReader
    access: [["Atmospherics"], ["Command"]]
  - type: ItemSlots
  - type: ContainerContainer
    containers:
      ItemCabinet: !type:ContainerSlot
  placement:
    mode: SnapgridCenter

- type: entity
  id: FireAxeCabinetOpen
  parent: FireAxeCabinet
  suffix: Open
  components:
  - type: ItemCabinet
    opened: true
    doorSound:
      path: /Audio/Machines/machine_switch.ogg
    openState: glass-up
    closedState: glass

- type: entity
  id: FireAxeCabinetFilled
  parent: FireAxeCabinet
  suffix: Filled
  components:
  - type: ItemCabinet
    cabinetSlot:
      startingItem: FireAxe
      ejectOnInteract: true
      whitelist:
        tags:
        - FireAxe
    doorSound:
      path: /Audio/Machines/machine_switch.ogg
    openState: glass-up
    closedState: glass

- type: entity
  id: FireAxeCabinetFilledOpen
  parent: FireAxeCabinetFilled
  suffix: Filled, Open
  components:
  - type: ItemCabinet
    opened: true
    doorSound:
      path: /Audio/Machines/machine_switch.ogg
    openState: glass-up
    closedState: glass
