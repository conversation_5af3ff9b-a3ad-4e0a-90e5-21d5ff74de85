# Really you should write your own
- type: htnCompound
  id: SimpleHostileCompound
  branches:
    - tasks:
        - !type:HTNCompoundTask
          task: MeleeCombatCompound
    - tasks:
        - !type:HTNCompoundTask
          task: IdleCompound

- type: htnCompound
  id: RatServantTargetAttackCompound
  branches:
    - tasks:
        - !type:HTNCompoundTask
          task: RatServantCombatCompound
    - tasks:
        - !type:HTNCompoundTask
          task: IdleCompound

- type: htnCompound
  id: MouseCompound
  branches:
    - tasks:
        - !type:HTNCompoundTask
          task: FoodCompound
    - tasks:
        - !type:HTNCompoundTask
          task: IdleCompound

- type: htnCompound
  id: GlockroachCompound
  branches:
    - tasks:
        - !type:HTNCompoundTask
          task: InnateRangedCombatCompound
    - tasks:
        - !type:HTNCompoundTask
          task: FoodCompound
    - tasks:
        - !type:HTNCompoundTask
          task: IdleCompound

- type: htnCompound
  id: RuminantCompound
  branches:
  - tasks:
    - !type:HTNCompoundTask
      task: FoodCompound
  - tasks:
    - !type:HTNCompoundTask
      task: IdleCompound

- type: htnCompound
  id: RuminantHostileCompound
  branches:
  - tasks:
    - !type:HTNCompoundTask
      task: MeleeCombatCompound
  - tasks:
    - !type:HTNCompoundTask
      task: FoodCompound
  - tasks:
    - !type:HTNCompoundTask
      task: IdleCompound

- type: htnCompound
  id: DragonCarpCompound
  branches:
    - tasks:
        - !type:HTNCompoundTask
          task: MeleeCombatCompound
    - tasks:
        - !type:HTNCompoundTask
          task: FollowCompound
    - tasks:
        - !type:HTNCompoundTask
          task: IdleCompound
