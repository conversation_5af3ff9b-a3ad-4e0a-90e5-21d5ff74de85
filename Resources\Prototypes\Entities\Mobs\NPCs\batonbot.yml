- type: entity
  parent: MobSiliconBase
  id: MobBatonBot
  name: "батонбот"
  description: "Захищає станцію від ворожих диких тварин."
  components:
  - type: Sprite
    sprite: Mobs/Silicon/Bots/batonbot.rsi
    state: batonbot
  - type: Construction
    graph: BatonBot
    node: bot
  - type: SentienceTarget
    flavorKind: station-event-random-sentience-flavor-mechanical
  - type: UseDelay
    delay: 1
  - type: NpcFactionMember
    factions:
    - NanoTrasen
  - type: CombatMode
  - type: EmagReplaceFactions
    factions:
    - Syndicate
  - type: Stunbaton
    energyPerUse: 0
  - type: MeleeWeapon
    altDisarm: false
    soundHit:
        path: /Audio/Weapons/egloves.ogg
    angle: 0
    animation: WeaponArcPunch
    damage:
      types:
        Shock: 4
  - type: StaminaDamageOnHit
    damage: 20
    sound: /Audio/Weapons/egloves.ogg
  - type: MobThresholds
    thresholds:
      0: Alive
      80: Dead
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 70
      behaviors:
      - !type:TriggerBehavior
    - trigger:
        !type:DamageTrigger
        damage: 80
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          ProximitySensor:
            min: 1
            max: 1
      - !type:SpawnEntitiesBehavior
        spawn:
          Stunbaton:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: MovementSpeedModifier
    baseWalkSpeed: 2
    baseSprintSpeed: 3
  - type: HTN
    rootTask:
      task: BatonbotCompound
    blackboard:
      AttackDelayDeviation: !type:Single
        0.3
  - type: InteractionPopup
    interactSuccessString: petting-success-batonbot
    interactFailureString: petting-failure-batonbot
    interactSuccessSound:
      path: /Audio/Ambience/Objects/periodic_beep.ogg
