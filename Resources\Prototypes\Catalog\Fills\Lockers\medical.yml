- type: entity
  id: LockerMedicineFilled
  suffix: Filled
  parent: LockerMedicine
  components:
  - type: StorageFill
    contents:
      - id: BoxSyringe
      - id: EpinephrineChemistryBottle
        amount: 1
      - id: Brutepack
        amount: 2
      - id: Ointment
        amount: 2
      - id: Bloodpack
        amount: 2
      - id: Gauze

- type: entity
  id: LockerWallMedicalFilled
  name: "настінна шафка для медикаментів"
  suffix: Filled
  parent: LockerWallMedical
  components:
  - type: StorageFill
    contents:
      - id: BoxSyringe
      - id: EpinephrineChemistryBottle
        amount: 1
      - id: Brutepack
        amount: 2
      - id: Ointment
        amount: 2
      - id: Bloodpack
        amount: 2
      - id: Gauze


- type: entity
  id: LockerMedicalFilled
  suffix: Filled
  parent: LockerMedical
  components:
  - type: StorageFill
    contents:
      - id: HandheldHealthAnalyzer
        prob: 0.6
      - id: ClothingHeadMirror
        prob: 0.1
      - id: ClothingHandsGlovesLatex
      - id: ClothingHeadsetMedical
      - id: ClothingEyesHudMedical
      - id: ClothingBeltMedical
#Yea. Those probs are fucked. Nothing I can do about orGroup behavior. Everything below this line is DeltaV.
#If W<PERSON>den ever adds new Probs, throw their probs out, but keep new items if added
      - id: ClothingHeadHatSurgcapGreen
        prob: 0.1
        orGroup: Surgcaps
      - id: ClothingHeadHatSurgcapPurple
        prob: 0.11
        orGroup: Surgcaps
      - id: ClothingHeadHatSurgcapBlue
        prob: 0.12
        orGroup: Surgcaps
      - id: ClothingHeadHatSurgcapCyan
        prob: 0.13
        orGroup: Surgcaps
      - id: ClothingHeadHatSurgcapBlack
        prob: 0.15
        orGroup: Surgcaps
      - id: ClothingHeadHatSurgcapPink
        prob: 0.16
        orGroup: Surgcaps
      - id: ClothingHeadHatSurgcapRainbow
        prob: 0.18
        orGroup: Surgcaps
      - id: ClothingHeadHatSurgcapWhite
        prob: 0.19
        orGroup: Surgcaps
      - id: ClothingHeadHatSurgcapCybersun
        prob: 0.02
        orGroup: Surgcaps
      - id: UniformScrubsColorBlue
        prob: 0.1
        orGroup: Surgshrubs
      - id: UniformScrubsColorGreen
        prob: 0.11
        orGroup: Surgshrubs
      - id: UniformScrubsColorPurple
        prob: 0.12
        orGroup: Surgshrubs
      - id: UniformScrubsColorCyan
        prob: 0.13
        orGroup: Surgshrubs
      - id: UniformScrubsColorBlack
        prob: 0.15
        orGroup: Surgshrubs
      - id: UniformScrubsColorPink
        prob: 0.16
        orGroup: Surgshrubs
      - id: UniformScrubsColorRainbow
        prob: 0.18
        orGroup: Surgshrubs
      - id: UniformScrubsColorWhite
        prob: 0.19
        orGroup: Surgshrubs
      - id: UniformScrubsColorCybersun
        prob: 0.02
        orGroup: Surgshrubs
      - id: NitrousOxideTankFilled
        prob: 0.3
      - id: ClothingMaskSterile
      - id: LunchboxMedicalFilledRandom # Delta-V Lunchboxes!
        prob: 0.3
      - id: Eftpos #Pirate banking

- type: entity
  parent: LockerWallMedical
  id: LockerWallMedicalDoctorFilled
  name: "настінна шафка лікаря"
  suffix: Filled
  components:
  - type: StorageFill
    contents:
      - id: HandheldHealthAnalyzer
        prob: 0.6
      - id: ClothingHandsGlovesLatex
      - id: ClothingHeadsetMedical
      - id: ClothingEyesHudMedical
      - id: ClothingBeltMedical
      - id: ClothingHeadHatSurgcapGreen
        prob: 0.1
        orGroup: Surgcaps
      - id: ClothingHeadHatSurgcapPurple
        prob: 0.05
        orGroup: Surgcaps
      - id: ClothingHeadHatSurgcapBlue
        prob: 0.90
        orGroup: Surgcaps
      - id: UniformScrubsColorBlue
        prob: 0.5
        orGroup: Surgshrubs
      - id: UniformScrubsColorGreen
        prob: 0.1
        orGroup: Surgshrubs
      - id: UniformScrubsColorPurple
        prob: 0.05
        orGroup: Surgshrubs
      - id: ClothingMaskSterile
      - id: LunchboxMedicalFilledRandom # Delta-V Lunchboxes!
        prob: 0.3
      - id: Eftpos #Pirate banking

- type: entity
  id: LockerChemistryFilled
  suffix: Filled
  parent: LockerChemistry
  components:
  - type: StorageFill
    contents:
      - id: BoxSyringe
      - id: BoxBeaker
      - id: BoxBeaker
        prob: 0.3
      - id: BoxPillCanister
      - id: BoxBottle
      - id: BoxVial
      - id: ChemBag
      - id: ClothingHandsGlovesLatex
      - id: ClothingHeadsetMedical
      - id: ClothingMaskSterile
      - id: HandLabeler
        prob: 0.5
      - id: Eftpos #Pirate banking

- type: entity
  id: LockerParamedicFilled #Delta V - Paramedic locker without suit
  suffix: Filled
  parent: LockerParamedic
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterCoatParamedicWB
      - id: ClothingHeadHatParamedicsoft
      - id: ClothingUniformJumpsuitParamedic
      - id: ClothingUniformJumpskirtParamedic
      - id: ClothingEyesHudMedical
      - id: ClothingHandsGlovesLatex
      - id: ClothingHeadsetMedical
      - id: ClothingMaskSterile
      - id: ClothingShoesBootsWinterParamedic #Delta V: Add departmental winter boots
      - id: MedkitFilled
        prob: 0.3
      - id: LunchboxMedicalFilledRandom # Delta-V Lunchboxes!
        prob: 0.3
      - id: EmergencyNitrousOxideTankFilled
      - id: Eftpos #Pirate banking

- type: entity
  id: LockerParamedicFilledHardsuit #Delta V - Paramedic locker with suit
  suffix: Filled, Hardsuit
  parent: LockerParamedic
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterHardsuitVoidParamed
      - id: ClothingOuterCoatParamedicWB
      - id: ClothingHeadHatParamedicsoft
      - id: ClothingOuterWinterPara
      - id: ClothingUniformJumpsuitParamedic
      - id: ClothingUniformJumpskirtParamedic
      - id: ClothingEyesHudMedical
      - id: ClothingHandsGlovesLatex
      - id: ClothingHeadsetMedical
      - id: ClothingMaskSterile
      - id: ClothingShoesBootsWinterParamedic #Delta V: Add departmental winter boots
      - id: HandheldGPSBasic
      - id: MedkitFilled
        prob: 0.3
      - id: LunchboxMedicalFilledRandom # Delta-V Lunchboxes!
        prob: 0.3
      - id: EmergencyNitrousOxideTankFilled
      - id: Eftpos #Pirate banking
