surgery-tool-turn-on = Turn it on first!
surgery-tool-reload = Reload it first!
surgery-tool-match-light = Light it first!
surgery-tool-match-replace = Get a new match!

surgery-tool-examinable-verb-text = Surgery Tool
surgery-tool-examinable-verb-message = Examine the uses of this tool in surgeries.
surgery-tool-header = This can be used in surgeries as:
surgery-tool-unlimited = - {$tool} at [color={$color}]{$speed}x[/color] speed
surgery-tool-used = - {$tool} at [color={$color}]{$speed}x[/color] speed, [color=red]then gets used up[/color]
