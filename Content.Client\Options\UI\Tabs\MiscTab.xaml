<tabs:MiscTab xmlns="https://spacestation14.io"
                  xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                  xmlns:tabs="clr-namespace:Content.Client.Options.UI.Tabs"
                  xmlns:xNamespace="http://schemas.microsoft.com/winfx/2006/xaml"
                  xmlns:s="clr-namespace:Content.Client.Stylesheets">
    <BoxContainer Orientation="Vertical">
        <ScrollContainer VerticalExpand="True" HorizontalExpand="True">
            <BoxContainer Orientation="Vertical" Margin="8 8 8 8" VerticalExpand="True">
                <Label Text="{Loc 'ui-options-general-ui-style'}"
                       FontColorOverride="{xNamespace:Static s:StyleNano.NanoGold}"
                       StyleClasses="LabelKeyText"/>
                <BoxContainer Orientation="Horizontal">
                    <Label Text="{Loc 'ui-options-hud-theme'}" />
                    <Control MinSize="4 0" />
                    <OptionButton Name="HudThemeOption" />
                </BoxContainer>
                <BoxContainer Orientation="Horizontal">
                    <Label Text="{Loc 'ui-options-hud-layout'}" />
                    <Control MinSize="4 0" />
                    <OptionButton Name="HudLayoutOption" />
                </BoxContainer>
                <Label Text="{Loc 'ui-options-general-accessibility'}"
                       FontColorOverride="{xNamespace:Static s:StyleNano.NanoGold}"
                       StyleClasses="LabelKeyText"/>
                <CheckBox Name="ReducedMotionCheckBox" Text="{Loc 'ui-options-reduced-motion'}" />
                <CheckBox Name="EnableColorNameCheckBox" Text="{Loc 'ui-options-enable-color-name'}" />
                <CheckBox Name="ColorblindFriendlyCheckBox" Text="{Loc 'ui-options-colorblind-friendly'}" />
                <CheckBox Name="DisableFiltersCheckBox" Text="{Loc 'ui-options-no-filters'}" />
                <BoxContainer Orientation="Horizontal">
                    <Label Text="{Loc 'ui-options-chatstack'}" />
                    <Control MinSize="4 0" />
                    <OptionButton Name="ChatStackOption" />
                </BoxContainer>
                <BoxContainer Orientation="Horizontal">
                    <Label Text="{Loc 'ui-options-chat-window-opacity'}" Margin="8 0" />
                    <Slider Name="ChatWindowOpacitySlider"
                            MinValue="0"
                            MaxValue="1"
                            MinWidth="200" />
                    <Label Name="ChatWindowOpacityLabel" Margin="8 0" />
                </BoxContainer>
                <BoxContainer Orientation="Horizontal">
                    <Label Text="{Loc 'ui-options-screen-shake-intensity'}" Margin="8 0" />
                    <Slider Name="ScreenShakeIntensitySlider"
                            MinValue="0"
                            MaxValue="100"
                            Rounded="True"
                            MinWidth="200" />
                    <Label Name="ScreenShakeIntensityLabel" Margin="8 0" />
                </BoxContainer>
                <Label Text="{Loc 'ui-options-general-discord'}"
                       FontColorOverride="{xNamespace:Static s:StyleNano.NanoGold}"
                       StyleClasses="LabelKeyText"/>
                <CheckBox Name="DiscordRich" Text="{Loc 'ui-options-discordrich'}" />
                <Label Text="{Loc 'ui-options-general-speech'}"
                       FontColorOverride="{xNamespace:Static s:StyleNano.NanoGold}"
                       StyleClasses="LabelKeyText"/>
                <CheckBox Name="ShowOocPatronColor" Text="{Loc 'ui-options-show-ooc-patron-color'}" />
                <CheckBox Name="ShowLoocAboveHeadCheckBox" Text="{Loc 'ui-options-show-looc-on-head'}" />
                <CheckBox Name="FancySpeechBubblesCheckBox" Text="{Loc 'ui-options-fancy-speech'}" />
                <CheckBox Name="FancyNameBackgroundsCheckBox" Text="{Loc 'ui-options-fancy-name-background'}" />
   				<CheckBox Name="LogInChatCheckBox" Text="{Loc 'ui-options-log-in-chat'}" /> <!-- GoobStation log popups in the chat -->
                <Label Text="{Loc 'ui-options-general-cursor'}"
                       FontColorOverride="{xNamespace:Static s:StyleNano.NanoGold}"
                       StyleClasses="LabelKeyText"/>
                <CheckBox Name="ShowHeldItemCheckBox" Text="{Loc 'ui-options-show-held-item'}" />
                <CheckBox Name="ShowCombatModeIndicatorsCheckBox" Text="{Loc 'ui-options-show-combat-mode-indicators'}" />
                <CheckBox Name="ShowOfferModeIndicatorsCheckBox" Text="{Loc 'ui-options-show-offer-mode-indicators'}" />
                <Label Text="{Loc 'ui-options-general-storage'}"
                       FontColorOverride="{xNamespace:Static s:StyleNano.NanoGold}"
                       StyleClasses="LabelKeyText"/>
                <CheckBox Name="OpaqueStorageWindowCheckBox" Text="{Loc 'ui-options-opaque-storage-window'}" />
                <CheckBox Name="StaticStorageUI" Text="{Loc 'ui-options-static-storage-ui'}" />
                <Label Text="{Loc 'ui-options-general-other'}"
                       FontColorOverride="{xNamespace:Static s:StyleNano.NanoGold}"
                       StyleClasses="LabelKeyText"/>
                <CheckBox Name="ModernProgressBar" Text="{Loc 'ui-options-modern-progress-bar'}" />
                <!-- <CheckBox Name="ToggleWalk" Text="{Loc 'ui-options-hotkey-toggle-walk'}" /> -->
            </BoxContainer>
        </ScrollContainer>
        <controls:StripeBack HasBottomEdge="False" HasMargins="False">
            <Button Name="ApplyButton"
                    Text="{Loc 'ui-options-apply'}"
                    TextAlign="Center"
                    HorizontalAlignment="Right" />
        </controls:StripeBack>
    </BoxContainer>
</tabs:MiscTab>
