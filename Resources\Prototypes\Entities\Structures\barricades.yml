#None of these besides the full barricade are constructible yet since I can't understand code, and they need to do stuff like go over airlocks / windows.
#Consider porting the barricade kit from /vg/ if you do make them constructible, along with their HP values. Or don't.

#Base barricade
#A lot of components here are intentionally ommitted in case someone wants to make barricades that are made out of things other than wood.
- type: entity
  id: BaseBarricade
  description: "Барикада з дерев'яних дощок. Виглядає так, ніби може витримати кілька потужних ударів."
  parent: BaseStructure
  name: "дерев'яна барикада"
  abstract: true
  components:
  - type: InteractionOutline
  - type: Sprite
    sprite: Structures/barricades.rsi
    state: barricade_full
    drawdepth: BlastDoors
    noRot: true
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb {}
        mask:
        - FullTileMask
        layer:
        - WallLayer
  - type: Damageable
    damageModifierSet: Wood
    damageContainer: StructuralInorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 60
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank1:
            min: 3
            max: 3
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: AtmosExposed

#Regular Barricade
- type: entity
  id: Barricade
  parent: BaseBarricade
  components:
  - type: Construction
    graph: Barricade
    node: barricadefull
  - type: Tag
    tags:
    - Wooden
  - type: Flammable
    fireSpread: true
    damage:
      types:
        Heat: 3 #per second, scales with number of fire 'stacks'
  - type: Appearance
  - type: FireVisuals
    sprite: Effects/fire.rsi
    normalState: 1

#Structure-Mounted Barricade
- type: entity
  id: BarricadeBlock
  parent: Barricade
  components:
  - type: Sprite
    sprite: Structures/barricades.rsi
    state: barricade

#Directional Barricade
- type: entity
  id: BarricadeDirectional
  parent: BaseBarricade
  placement:
    mode: SnapgridCenter
#A bunch of this is taken straight from dirwindows and there is a chance that I may have fucked something up. Probably not though
  components:
  - type: Sprite
    sprite: Structures/barricades.rsi
    state: barricade_directional
    noRot: false #Results in smoother rotation when turning the camera, the sprite's dirs are just it rotated anyways so there's no reason to not set this.
  - type: Construction
    graph: BarricadeDirectional
    node: barricadefull
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.49,-0.39,0.49,-0.36"
        mask:
        - FullTileMask
        layer:
        - WallLayer
  - type: Tag
    tags:
    - Wooden
  - type: Flammable
    fireSpread: true
    damage:
      types:
        Heat: 3 #per second, scales with number of fire 'stacks'
  - type: Appearance
  - type: FireVisuals
    sprite: Effects/fire.rsi
    normalState: 1
