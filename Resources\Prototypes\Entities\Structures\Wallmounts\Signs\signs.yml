- type: entity
  parent: BaseSign
  id: PaintingMonkey
  name: "мавпячий живопис"
  description: "Повертайся до монастиря."
  components:
  - type: Sprite
    state: monkey_painting

# Directional Station Guide Signs
- type: entity
  parent: BaseSign
  id: BaseSignDirectional
  abstract: true
  components:
    - type: Sprite
      snapCardinals: false

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalBar
  name: "вказівник бару"
  description: "Знак напрямку, що вказує, в який бік знаходиться смужка."
  components:
  - type: Sprite
    state: direction_bar

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalBridge
  name: "вказівник мостику"
  description: "Вказівник напрямку, що вказує, в який бік знаходиться міст."
  components:
  - type: Sprite
    state: direction_bridge

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalBrig
  name: "вказівник брігу"
  description: "Вказівник напрямку, що вказує, в якому напрямку знаходиться бриг."
  components:
  - type: Sprite
    state: direction_brig

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalChapel
  name: "вказівник каплиці"
  description: "Вказівник, що вказує, в якому напрямку знаходиться каплиця."
  components:
  - type: Sprite
    state: direction_chapel

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalChemistry
  name: "вказівник хімії"
  description: "Вказівник, що вказує, в який бік знаходиться хімічна лабораторія."
  components:
  - type: Sprite
    state: direction_chemistry

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalCryo
  name: "вказівник кріо"
  description: "Вказівник, що вказує шлях до кріогенної техніки."
  components:
  - type: Sprite
    state: direction_cryo

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalDorms
  name: "вказівник кают"
  description: "Вказівник, що вказує, в якому напрямку знаходяться гуртожитки."
  components:
  - type: Sprite
    state: direction_dorms

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalEng
  name: "вказівник інженерії"
  description: "Вказівник, що вказує, в якому напрямку знаходиться інженерний відділ."
  components:
  - type: Sprite
    state: direction_eng

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalEvac
  name: "знак евакуації"
  description: "Вказівник напрямку, що вказує, в який бік евакуювати."
  components:
  - type: Sprite
    state: direction_evac

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalExam
  name: "вказівник обстеження"
  description: "Вказівник, що вказує, в якому напрямку знаходиться кабінет(и) обстеження."
  components:
  - type: Sprite
    state: direction_exam

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalFood
  name: "вказівник кухні"
  description: "Вказівник напрямку, що вказує, в який бік знаходиться кухня."
  components:
  - type: Sprite
    state: direction_food

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalGravity
  name: "вказівник гравітації"
  description: "Знак напрямку, що вказує, в якому напрямку знаходиться гравітаційний генератор."
  components:
  - type: Sprite
    state: direction_gravity

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalHop
  name: "вказівник голови персоналу"
  description: "Вказівник, що вказує, в якому напрямку знаходиться кабінет голови персоналу."
  components:
  - type: Sprite
    state: direction_hop

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalHydro
  name: "вказівник ботаніки"
  description: "Знак напрямку, що вказує, в якому напрямку знаходиться гідропоніка."
  components:
  - type: Sprite
    state: direction_hydro

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalIcu
  name: "вказівник інтенсивної терапії"
  description: "Вказівник напрямку, що вказує, в якому напрямку знаходиться відділення інтенсивної терапії."
  components:
  - type: Sprite
    state: direction_icu

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalJanitor
  name: "вказівник прибиральника"
  description: "Вказівник, що вказує, в якому напрямку знаходиться комірчина прибиральника."
  components:
  - type: Sprite
    state: direction_janitor

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalLibrary
  name: "вказівник бібліотеки"
  description: "Вказівник напрямку, що вказує, в якому напрямку знаходиться бібліотека."
  components:
  - type: Sprite
    state: direction_library

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalMed
  name: "вказівник медичного відділу"
  description: "Вказівник, що вказує, в якому напрямку знаходиться медичний відділ."
  components:
  - type: Sprite
    state: direction_med

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalSalvage
  name: "вказівник утилізації"
  description: "Вказівник, що вказує, в якому напрямку знаходиться відділ порятунку."
  components:
  - type: Sprite
    state: direction_salvage

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalSci
  name: "вказівник епістеміки" # DeltaV - Epistemics Department replacing Science
  description: "Вказівник, що вказує, в якому напрямку знаходиться відділ епістеміки." # DeltaV - Epistemics Department replacing Science
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/signs.rsi # DeltaV - Epistemics Department replacing Science
    state: direction_epi # DeltaV - Epistemics Department replacing Science

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalSec
  name: "вказівник служби безпеки"
  description: "Вказівник, що вказує, в який бік знаходиться служба безпеки."
  components:
  - type: Sprite
    state: direction_sec

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalSolar
  name: "вказівник сонячних панелей"
  description: "Знак напрямку, що вказує, в якому напрямку знаходиться сонячні панелі"
  components:
  - type: Sprite
    state: direction_solar

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalSupply
  name: "вказівник постачання"
  description: "Вказівник напрямку, що вказує на деякі запаси."
  components:
  - type: Sprite
    state: direction_supply

- type: entity
  parent: BaseSignDirectional
  id: SignDirectionalWash
  name: "вказівник туалету"
  description: "Вказівник, що вказує шлях до вбиральні."
  components:
  - type: Sprite
    state: direction_wash

# Regular Signs

- type: entity
  parent: BaseSign
  id: SignAi
  name: "знак ШІ"
  description: "Знак, що вказує на наявність штучного інтелекту."
  components:
  - type: Sprite
    state: ai

- type: entity
  parent: BaseSign
  id: SignAiUpload
  name: "знак завантаження ШІ"
  description: "Знак, що вказує на наявність штучного інтелекту."
  components:
    - type: Sprite
      state: ai_upload

- type: entity
  parent: BaseSign
  id: SignArcade
  name: "знак аркади"
  description: "Вивіска, що вказує на зал ігрових автоматів."
  components:
  - type: Sprite
    state: arcade

- type: entity
  parent: BaseSign
  id: SignArmory
  name: "знак арсеналу"
  description: "Табличка, що вказує на арсенал."
  components:
  - type: Sprite
    state: armory

- type: entity
  parent: BaseSign
  id: SignToolStorage
  name: "знак з інструментами"
  description: "Вивіска, що вказує на кімнату для зберігання інструментів."
  components:
  - type: Sprite
    state: ass

- type: entity
  parent: BaseSign
  id: SignAnomaly
  name: "знак ксеноархеологічної лабораторії"
  description: "Вивіска, що вказує на лабораторію ксеноархеології."
  components:
  - type: Sprite
    state: xenoarch

- type: entity
  parent: BaseSign
  id: SignAnomaly2
  name: "знак лабораторії аномалій"
  description: "Вивіска, що вказує на лабораторію аномальних досліджень."
  components:
  - type: Sprite
    state: anomaly

- type: entity
  parent: BaseSign
  id: SignAtmos
  name: "знак атмосу"
  description: "Знак, що вказує на атмосферну зону."
  components:
  - type: Sprite
    state: atmos

- type: entity
  parent: BaseSign
  id: SignBar
  name: "вказівник бару"
  description: "Вивіска із зазначенням бару."
  components:
  - type: Sprite
    state: bar

- type: entity
  parent: BaseSign
  id: SignBath
  name: "вказівник вбиральні"
  description: "Табличка, що вказує на вбиральню."
  components:
  - type: Sprite
    state: bath

- type: entity
  parent: BaseSign
  id: SignKitchen
  name: "кухонна вивіска"
  description: "Серце дому. І хвороби."
  components:
  - type: Sprite
    state: kitchen

- type: entity
  parent: BaseSign
  id: SignTheater
  name: "театральна вивіска"
  description: "Чи була б це взагалі космічна станція без драми?"
  components:
  - type: Sprite
    layers:
    - state: drama1
      map: [ "base" ]
  - type: RandomSprite
    available:
    - base:
        drama1: null
        drama2: null
        drama3: null

- type: entity
  parent: BaseSign
  id: SignBarbershop
  name: "знак перукарні"
  description: "Вивіска, що вказує на перукарню."
  components:
  - type: Sprite
    state: barbershop

- type: entity
  parent: BaseSign
  id: SignBio
  name: "знак біо"
  description: "Вивіска, що вказує на біологічну лабораторію."
  components:
  - type: Sprite
    state: bio

- type: entity
  parent: BaseSign
  id: SignBiohazard
  name: "знак біологічної небезпеки"
  description: "Знак, що вказує на біологічну небезпеку."
  components:
  - type: Sprite
    state: biohazard

- type: entity
  parent: BaseSign
  id: SignBridge
  name: "вказівник мостику"
  description: "Знак, що вказує на міст."
  components:
  - type: Sprite
    state: bridge

- type: entity
  parent: BaseSign
  id: SignCanisters
  name: "вивіска на каністрах"
  description: "Знак, що попереджає глядача про каністри під тиском."
  components:
  - type: Sprite
    state: canisters

- type: entity
  parent: BaseSign
  id: SignCargo
  name: "знак логістики" # DeltaV - Logistics Department replacing Cargo
  description: "Знак, що вказує на логістичну зону." # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Sprite
    state: cargo

- type: entity
  parent: BaseSign
  id: SignCargoDock
  name: "знак вантажного доку"
  description: "Знак, що вказує на вантажний док."
  components:
  - type: Sprite
    state: cargo_dock

- type: entity
  parent: BaseSign
  id: SignChapel
  name: "вказівник каплиці"
  description: "Табличка, що вказує на каплицю."
  components:
  - type: Sprite
    sprite: DeltaV/Structures/Wallmounts/signs.rsi # DeltaV: Epi-themed chapel sprite
    state: chapel

- type: entity
  parent: BaseSign
  id: SignChem
  name: "вказівник хімії"
  description: "Вивіска, що вказує на хімічну лабораторію."
  components:
  - type: Sprite
    state: chem

- type: entity
  parent: BaseSign
  id: SignCloning
  name: "знак клонувальної лабораторії"
  description: "Вивіска, що вказує на лабораторію клонування."
  components:
  - type: Sprite
    state: cloning

- type: entity
  parent: BaseSign
  id: SignConference
  name: "знак конференц-залу"
  description: "Де відбувається робота."
  components:
  - type: Sprite
    state: conference_room

- type: entity
  parent: BaseSign
  id: SignCryo
  name: "знак кріосну"
  description: "Просто так? Збираєшся струсити?"
  components:
  - type: Sprite
    state: cryo


- type: entity
  parent: BaseSign
  id: SignDisposalSpace
  name: "знак утилізації відходів"
  description: "Знак, що вказує на місце утилізації."
  components:
  - type: Sprite
    state: deathsposal

- type: entity
  parent: BaseSign
  id: SignDoors
  name: "дверна знак"
  description: "Вивіска, що вказує на двері."
  components:
  - type: Sprite
    state: doors

- type: entity
  parent: BaseSign
  id: SignRestroom
  name: "вивіска туалету"
  description: "Знак, що вказує, куди ти йдеш... Нагадай, що ти тут робиш?"
  components:
  - type: Sprite
    state: restroom

- type: entity
  parent: BaseSign
  id: SignMaterials
  name: "знак матеріалів"
  description: "Знамення соковитого склепіння зі сталі, скла та пластику, що лежить перед вами."
  components:
  - type: Sprite
    state: mats

- type: entity
  parent: BaseSign
  id: SignEngine
  name: "знак сили"
  description: "Там, де відбувається полова."
  components:
  - type: Sprite
    state: engine

- type: entity
  parent: BaseSign
  id: SignEngineering
  name: "вказівник інженерії"
  description: "Знак, що вказує на інженерну зону."
  components:
  - type: Sprite
    state: eng

- type: entity
  parent: BaseSign
  id: SignEscapePods
  name: "знак з рятувальними капсулами"
  description: "Знак, що вказує на рятувальні капсули."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/signs.rsi # Nyanotrasen - Give pods sign directional sprites
    state: pods
    snapCardinals: false

- type: entity
  parent: BaseSign
  id: SignEVA
  name: "знак EVA"
  description: "Знак, що вказує на зону EVA. За межами цієї зони може знадобитися обладнання EVA."
  components:
  - type: Sprite
    state: eva

- type: entity
  parent: BaseSign
  id: SignElectrical
  name: "знак електрики"
  description: "Знак, що вказує на небезпеку ураження електричним струмом."
  components:
  - type: Sprite
    state: electrical

- type: entity
  parent: BaseSign
  id: SignExamroom
  name: "знак кабінету огляду"
  description: "Вивіска, що вказує на кабінет медичного огляду."
  components:
  - type: Sprite
    state: examroom

- type: entity
  parent: BaseSign
  id: SignFire
  name: "знак з вогнем"
  description: "Знак, що вказує на пожежну небезпеку."
  components:
  - type: Sprite
    state: fire

- type: entity
  parent: BaseSign
  id: SignGravity
  name: "вказівник гравітації"
  description: "Табличка із зазначенням гравітаційного генератора."
  components:
  - type: Sprite
    state: gravi

- type: entity
  parent: BaseSign
  id: SignHead
  name: "знак голови"
  description: "Офіційна вивіска, що вказує на житло керівника відділу, сертифікованого за стандартом Nanotrasen."
  components:
  - type: Sprite
    state: commander

- type: entity
  parent: BaseSign
  id: SignHydro1
  name: "вказівник ботаніки"
  description: "Знак, що вказує на зону гідропоніки."
  components:
  - type: Sprite
    state: hydro

- type: entity
  parent: BaseSign
  id: SignInterrogation
  name: "знак допиту"
  description: "Табличка, що вказує на кімнату для допитів."
  components:
  - type: Sprite
    state: interrogation

- type: entity
  parent: BaseSign
  id: SignJanitor
  name: "вказівник прибиральника"
  description: "Табличка, що позначає територію, на якій працює двірник."
  components:
  - type: Sprite
    state: janitor

- type: entity
  parent: BaseSign
  id: SignLaundromat
  name: "знак пральні"
  description: "Вивіска, що вказує на пральню."
  components:
  - type: Sprite
    state: laundromat

- type: entity
  parent: BaseSign
  id: SignLawyer
  name: "знак закону"
  description: "Ознака, що вказує на наявність (зазвичай відсутнє) верховенства права."
  components:
  - type: Sprite
    state: law

- type: entity
  parent: BaseSign
  id: SignLibrary
  name: "вказівник бібліотеки"
  description: "Вивіска, що вказує на бібліотеку."
  components:
  - type: Sprite
    state: biblio

- type: entity
  parent: BaseSign
  id: SignMail
  name: "знак пошти"
  description: "Знак, що вказує на пошту."
  components:
  - type: Sprite
    state: mail

- type: entity
  parent: BaseSign
  id: SignMedical
  name: "знак медбею"
  description: "Табличка, що вказує на медичний відсік."
  components:
  - type: Sprite
    state: medbay

- type: entity
  parent: BaseSign
  id: SignMorgue
  name: "знак моргу"
  description: "Вивіска, що вказує на морг."
  components:
  - type: Sprite
    state: morgue

- type: entity
  parent: BaseSign
  id: SignNews
  name: "знак новин"
  description: "Табличка із зазначенням місця роботи репортера."
  components:
  - type: Sprite
    state: news

- type: entity
  parent: BaseSign
  id: SignNosmoking
  name: "знак не паління тютюну"
  description: "Знак, що вказує на те, що куріння поблизу заборонено."
  components:
  - type: Sprite
    state: nosmoking

- type: entity
  parent: BaseSign
  id: SignPrison
  name: "знак тюрми"
  description: "Табличка, що вказує на в'язницю."
  components:
  - type: Sprite
    state: prison

- type: entity
  parent: BaseSign
  id: SignPsychology
  name: "знак психології"
  description: "Табличка з позначенням місця, де працює психолог."
  components:
  - type: Sprite
    state: psychology

- type: entity
  parent: BaseSign
  id: SignReception
  name: "знак приймальні"
  description: "Вивіска, що вказує на прийом."
  components:
  - type: Sprite
    state: reception

- type: entity
  parent: BaseSign
  id: SignRND
  name: "знак росліджень і розробок"
  description: "Вивіска, що вказує на лабораторію досліджень і розробок."
  components:
  - type: Sprite
    state: rnd

- type: entity
  parent: BaseSign
  id: SignRobo
  name: "знак робототехніки"
  description: "Вивіска, що вказує на лабораторію робототехніки."
  components:
  - type: Sprite
    state: robo

- type: entity
  parent: BaseSign
  id: SignSalvage
  name: "вказівник утилізації"
  description: "Знак, що вказує на зону порятунку."
  components:
  - type: Sprite
    state: salvage

- type: entity
  parent: BaseSign
  id: SignScience
  name: "знак науковців"
  description: "Вивіска, що вказує на наукову сферу."
  components:
  - type: Sprite
    state: sci

- type: entity
  parent: BaseSign
  id: SignServer
  name: "знак сервера"
  description: "Чув коли-небудь про великі дані? Це воно, дурню. Найбільший."
  components:
  - type: Sprite
    state: data

- type: entity
  parent: BaseSign
  id: SignCans
  name: "вивіска на каністрах"
  description: "Знак, що вказує на сприятливу наявність газових балончиків."
  components:
  - type: Sprite
    state: cans

- type: entity
  parent: BaseSign
  id: SignShipDock
  name: "знак евакуації"
  description: "Знак, що вказує на місце, куди (ймовірно) прибуде евакуаційний шатл."
  components:
  - type: Sprite
    state: dock

- type: entity
  parent: BaseSign
  id: SignSpace
  name: "знак космосу"
  description: "Знак, який попереджає, що попереду лише холодний, порожній простір."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/signs.rsi # Nyanotrasen - Give space sign directional sprites
    state: space
    snapCardinals: false

- type: entity
  parent: BaseSign
  id: SignSurgery
  name: "знак операційної"
  description: "Вивіска, що вказує на операційну."
  components:
  - type: Sprite
    state: surgery

- type: entity
  parent: BaseSign
  id: SignTelecomms
  name: "знак телекомів"
  description: "Вивіска, що вказує на телекомунікаційну кімнату."
  components:
  - type: Sprite
    state: telecoms

- type: entity
  parent: BaseSign
  id: SignToxins
  name: "знак токсикології"
  description: "Вивіска, що вказує на токсикологічну лабораторію."
  components:
  - type: Sprite
    state: toxins

- type: entity
  parent: BaseSign
  id: SignVault
  name: "вивіска сховища"
  description: "Табличка, що вказує на сховище. Хто знає, які таємниці ховаються всередині?"
  components:
  - type: Sprite
    state: vault

- type: entity
  parent: BaseSign
  id: SignVirology
  name: "знак вірусології"
  description: "Вивіска, що вказує на вірусологічну лабораторію."
  components:
  - type: Sprite
    state: virology

- type: entity
  parent: BaseSign
  id: SignCorrosives
  name: "попереджувальний знак про корозію"
  description: "Знак, що вказує на небезпеку корозійних матеріалів."
  components:
  - type: Sprite
    state: corrosives

- type: entity
  parent: BaseSign
  id: SignCryogenics
  name: "попереджувальний знак кріогенної небезпеки"
  description: "Знак, що вказує на небезпеку, пов'язану з кріогенними матеріалами. Візьміть куртку!"
  components:
  - type: Sprite
    state: cryogenics

- type: entity
  parent: BaseSign
  id: SignDanger
  name: "попереджувальний знак про небезпеку"
  description: "Знак, що застерігає від якоїсь небезпеки."
  components:
  - type: Sprite
    state: danger

- type: entity
  parent: BaseSign
  id: SignExplosives
  name: "попереджувальний знак про вибухівку"
  description: "Знак, що вказує на небезпеку вибухонебезпечних матеріалів."
  components:
  - type: Sprite
    state: explosives

- type: entity
  parent: BaseSign
  id: SignFlammable
  name: "попереджувальний знак про пожежонебезпеку"
  description: "Знак, що вказує на небезпеку легкозаймистих матеріалів."
  components:
  - type: Sprite
    state: flammable

- type: entity
  parent: BaseSign
  id: SignLaser
  name: "попереджувальний знак про лазер"
  description: "Знак, що вказує на лазерну небезпеку."
  components:
  - type: Sprite
    state: laser

- type: entity
  parent: BaseSign
  id: SignMagnetics
  name: "попереджувальний знак на магнітах"
  description: "Знак, що вказує на небезпеку магнітних матеріалів."
  components:
  - type: Sprite
    state: magnetics

- type: entity
  parent: BaseSign
  id: SignMemetic
  name: "попереджувальний знак про меметичну небезпеку"
  description: "Знак, що вказує на меметичну небезпеку."
  components:
  - type: Sprite
    state: memetic

- type: entity
  parent: BaseSign
  id: SignSecure
  name: "знак безпеки"
  description: "Знак, який вказує на те, що територія попереду є безпечною зоною."
  components:
  - type: Sprite
    state: secure

- type: entity
  parent: BaseSign
  id: SignSecurearea
  name: "знак безпечної зони"
  description: "Знак, який вказує на те, що територія попереду є безпечною зоною."
  components:
  - type: Sprite
    state: securearea
    snapCardinals: true

- type: entity
  parent: BaseSign
  id: SignShock
  name: "знак шоку"
  description: "Знак, що вказує на небезпеку ураження електричним струмом."
  components:
  - type: Sprite
    state: shock

- type: entity
  parent: BaseSign
  id: SignOptical
  name: "оптичний попереджувальний знак"
  description: "Знак, що вказує на небезпеку оптичного випромінювання."
  components:
  - type: Sprite
    state: optical

- type: entity
  parent: BaseSign
  id: SignOxidants
  name: "попереджувальний знак окислювачів"
  description: "Знак, що вказує на небезпеку окислювача."
  components:
  - type: Sprite
    state: oxidants

- type: entity
  parent: BaseSign
  id: SignRadiation
  name: "знак радіаційної небезпеки"
  description: "Знак, що вказує на небезпеку іонізуючого випромінювання."
  components:
  - type: Sprite
    state: radiation

- type: entity
  parent: BaseSign
  id: SignXenobio
  name: "знак ксенобіо"
  description: "Вивіска, що вказує на лабораторію ксенобіології."
  components:
  - type: Sprite
    state: xenobio

- type: entity
  parent: BaseSign
  id: SignZomlab
  name: "знак зомбі-лабораторії"
  description: "Останки закритого дослідницького проекту \"Нанотрейзен\", який мав на меті використати силу ромеролу. Цікаво, що з цього вийшло..."
  components:
  - type: Sprite
    state: zomlab

- type: entity
  parent: BaseSign
  id: SignSecureMedRed
  name: "червоний знак безпеки"
  description: "Знак, який вказує на те, що територія попереду є безпечною зоною."
  components:
  - type: Sprite
    state: medium_secure_red

- type: entity
  parent: BaseSign
  id: SignSecureSmall
  name: "невеликий захисний знак"
  description: "Знак, який вказує на те, що територія попереду є безпечною зоною."
  components:
  - type: Sprite
    state: small_secure

- type: entity
  parent: BaseSign
  id: SignSecureSmallRed
  name: "невеликий червоний захисний знак"
  description: "Знак, який вказує на те, що територія попереду є безпечною зоною."
  components:
  - type: Sprite
    state: small_secure_red

- type: entity
  parent: BaseSign
  id: SignBlankMed
  name: "пустий знак"
  description: "Порожній знак."
  components:
  - type: Sprite
    state: medium_blank

- type: entity
  parent: BaseSign
  id: SignMagneticsMed
  name: "знак магнітної небезпеки"
  description: "Знак, що вказує на використання магнітів."
  components:
  - type: Sprite
    state: medium_magnetics

- type: entity
  parent: BaseSign
  id: SignDangerMed
  name: "знак небезпеки"
  description: "Знак, що застерігає від певної форми небезпеки."
  components:
  - type: Sprite
    state: medium_danger

- type: entity
  parent: BaseSign
  id: ExplosivesSignMed
  name: "знак вибухонебезпечності"
  description: "Знак, що вказує на вибухонебезпечні матеріали."
  components:
  - type: Sprite
    state: medium_explosives

- type: entity
  parent: BaseSign
  id: SignCryogenicsMed
  name: "знак кріогенної небезпеки"
  description: "Знак, що вказує на кріогенні матеріали."
  components:
  - type: Sprite
    state: medium_cryogenics

- type: entity
  parent: BaseSign
  id: SignElectricalMed
  name: "знак електрики"
  description: "Знак, що вказує на небезпеку ураження електричним струмом."
  components:
  - type: Sprite
    state: medium_electrical

- type: entity
  parent: BaseSign
  id: SignBiohazardMed
  name: "знак біологічної небезпеки"
  description: "Знак, що вказує на біологічну небезпеку."
  components:
  - type: Sprite
    state: medium_biohazard

- type: entity
  parent: BaseSign
  id: SignRadiationMed
  name: "знак радіаційного випромінювання"
  description: "Знак, що вказує на небезпеку іонізуючого випромінювання."
  components:
  - type: Sprite
    state: medium_radiation

- type: entity
  parent: BaseSign
  id: SignFlammableMed
  name: "знак вогненебезпеки" #when flammable component is done, this sign needs it
  description: "Знак, що вказує на легкозаймисті матеріали."
  components:
  - type: Sprite
    state: medium_flammable

- type: entity
  parent: BaseSign
  id: SignLaserMed
  name: "знак лазерної небезпеки"
  description: "Знак, що вказує на лазерну небезпеку."
  components:
  - type: Sprite
    state: medium_laser

- type: entity
  parent: BaseSign
  id: SignSecureMed
  name: "знак безпеки"
  description: "Знак, який вказує на те, що територія попереду є безпечною зоною."
  components:
  - type: Sprite
    state: medium_secure



# Atmos Warnings

- type: entity
  parent: BaseSign
  id: WarningAir
  name: "повітряний попереджувальний знак"
  description: "УВАГА! Трубка для подачі повітря. Перед початком роботи переконайтеся, що потік вимкнений."
  components:
  - type: Sprite
    state: atmos_air

- type: entity
  parent: BaseSign
  id: WarningCO2
  name: "Попереджувальний знак CO2"
  description: "УВАГА! Трубка подачі CO2. Перед роботою переконайтеся, що потік вимкнений."
  components:
  - type: Sprite
    state: atmos_co2

- type: entity
  parent: BaseSign
  id: WarningN2
  name: "Попереджувальний знак N2"
  description: "УВАГА! Проточна трубка N2. Перед роботою переконайтеся, що потік вимкнений."
  components:
  - type: Sprite
    state: atmos_n2

- type: entity
  parent: BaseSign
  id: WarningN2O
  name: "Попереджувальний знак N2O"
  description: "УВАГА! Трубка для подачі N2O. Перед роботою переконайтеся, що потік вимкнений."
  components:
  - type: Sprite
    state: atmos_n2o

- type: entity
  parent: BaseSign
  id: WarningO2
  name: "Попереджувальний знак O2"
  description: "УВАГА! Трубка подачі кисню. Перед роботою переконайтеся, що потік вимкнений."
  components:
  - type: Sprite
    state: atmos_o2

- type: entity
  parent: BaseSign
  id: WarningPlasma
  name: "знак відходів плазми"
  description: "УВАГА! Плазмотрон. Перед роботою переконайтеся, що потік вимкнено."
  components:
  - type: Sprite
    state: atmos_plasma

- type: entity
  parent: BaseSign
  id: WarningTritium
  name: "знак тритієвих відходів"
  description: "ОБЕРЕЖНО! Тритієва проточна трубка. Перед роботою переконайтеся, що потік вимкнений."
  components:
  - type: Sprite
    state: atmos_tritium

- type: entity
  parent: BaseSign
  id: WarningWaste
  name: "знак відходів atmos"
  description: "УВАГА! Трубка для зливу відходів. Перед роботою переконайтеся, що потік вимкнений."
  components:
  - type: Sprite
    state: atmos_waste

- type: entity
  parent: BaseSign
  id: SignSmoking
  name: "знак заборони куріння"
  description: "Попереджувальний знак з написом \"НЕ КУРИТИ"
  components:
  - type: Sprite
    state: nosmoking2

- type: entity
  parent: BaseSign
  id: SignSomethingOld
  name: "стара вивіска"
  description: "Якась технічна інформація, шкода, що вона занадто затерта, щоб її можна було прочитати."
  components:
  - type: Sprite
    state: something-old1

- type: entity
  parent: BaseSign
  id: SignSomethingOld2
  name: "стара вивіска"
  description: "Схоже на планету, що розбивається об якусь станцію над нею. Це трохи лякає."
  components:
  - type: Sprite
    state: something-old2

- type: entity
  parent: BaseSign
  id: SignSecurity
  name: "знак безпеки"
  description: "Знак із зображенням знаків розрізнення."
  components:
  - type: Sprite
    state: security

- type: entity
  parent: BaseSign
  id: SignPlaque
  name: "золота плакетка"
  description: "Престижна золота плакетка."
  components:
  - type: Sprite
    state: goldenplaque

- type: entity
  parent: BaseSign
  id: SignKiddiePlaque
  name: "дитяча табличка"
  description: "Скромна меморіальна дошка."
  components:
  - type: Sprite
    state: kiddieplaque

- type: entity
  parent: BaseSign
  id: SignNanotrasen1
  name: "нанотрейзен знак 1"
  description: "Частина 1."
  components:
  - type: Sprite
    state: nanotrasen_sign1

- type: entity
  parent: BaseSign
  id: SignNanotrasen2
  name: "нанотрейзен знак 2"
  description: "Частина 2."
  components:
  - type: Sprite
    state: nanotrasen_sign2

- type: entity
  parent: BaseSign
  id: SignNanotrasen3
  name: "нанотрейзен знак 3"
  description: "Частина 3."
  components:
  - type: Sprite
    state: nanotrasen_sign3

- type: entity
  parent: BaseSign
  id: SignNanotrasen4
  name: "нанотрейзен знак 4"
  description: "Частина 4."
  components:
  - type: Sprite
    state: nanotrasen_sign4

- type: entity
  parent: BaseSign
  id: SignNanotrasen5
  name: "нанотрейзен знак 5"
  description: "Частина 5."
  components:
  - type: Sprite
    state: nanotrasen_sign5

- type: entity
  parent: BaseSign
  id: SignRedOne
  name: "знак одиниці"
  description: "Табличка з цифрою, на ній зображена одиниця."
  components:
  - type: Sprite
    state: one

- type: entity
  parent: BaseSign
  id: SignRedTwo
  name: "знак двійки"
  description: "Табличка з цифрою, на ній зображена двійка."
  components:
  - type: Sprite
    state: two

- type: entity
  parent: BaseSign
  id: SignRedThree
  name: "знак трійки"
  description: "Табличка з цифрою, на ній зображена трійка."
  components:
  - type: Sprite
    state: three

- type: entity
  parent: BaseSign
  id: SignRedFour
  name: "знак четвірки"
  description: "Табличка з цифрою, на ній зображена четвірка."
  components:
  - type: Sprite
    state: four

- type: entity
  parent: BaseSign
  id: SignRedFive
  name: "знак п'ятірки"
  description: "Табличка з цифрою, на ній зображена п'ятірка."
  components:
  - type: Sprite
    state: five

- type: entity
  parent: BaseSign
  id: SignRedSix
  name: "знак шістки"
  description: "Табличка з цифрою, на ній зображена шістка."
  components:
  - type: Sprite
    state: six

- type: entity
  parent: BaseSign
  id: SignRedSeven
  name: "знак сімки"
  description: "Табличка з цифрою, на ній зображена сімка."
  components:
  - type: Sprite
    state: seven

- type: entity
  parent: BaseSign
  id: SignRedEight
  name: "знак вісімки"
  description: "Табличка з цифрою, на ній зображена вісімка."
  components:
  - type: Sprite
    state: eight

- type: entity
  parent: BaseSign
  id: SignRedNine
  name: "знак дев'ятки"
  description: "Табличка з цифрою, на ній зображена дев'ятка."
  components:
  - type: Sprite
    state: nine

- type: entity
  parent: BaseSign
  id: SignRedZero
  name: "знак нуля"
  description: "Табличка з цифрою, на ній зображена нуль."
  components:
  - type: Sprite
    state: zero

- type: entity
  parent: BaseSign
  id: SignSurvival
  name: "знак виживання"
  description: "Знак. На ньому написано \"Виживання\"."
  components:
  - type: Sprite
    state: survival

- type: entity
  parent: BaseSign
  id: SignNTMine
  name: "знак міни"
  description: "Знак. На ній написано \"Міни\"."
  components:
  - type: Sprite
    state: ntmining
