parse-bool-fail = Unable to parse {$arg} as a bool
parse-float-fail = Unable to parse {$arg} as a float

lockeyes-command-description = Prevents eyes from being rotated any further
lockeyes-command-help = lockeyes <true/false>

rotateeyes-command-description = Rotates every player's current eye to the specified rotation
rotateeyes-command-help = rotateeyes <degrees (default 0)>
rotateeyes-command-count = Set {$count} eye rotations

