<Control xmlns="https://spacestation14.io"
         xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client">
    <PanelContainer MouseFilter="Stop">
        <PanelContainer.PanelOverride>
            <!-- semi-transparent background -->
            <gfx:StyleBoxFlat BackgroundColor="#000000AA" />
        </PanelContainer.PanelOverride>

        <Control HorizontalAlignment="Center" VerticalAlignment="Center" MaxWidth="600">
            <PanelContainer StyleClasses="AngleRect" />

            <BoxContainer Orientation="Vertical" Margin="4">
                <RichTextLabel Name="Description" />

                <!-- Contains actual messages -->
                <ScrollContainer HScrollEnabled="False" Margin="4" VerticalExpand="True" ReturnMeasure="True" MaxHeight="400">
                    <BoxContainer Orientation="Vertical" Name="MessageContainer" Margin="0 2 0 0" />
                </ScrollContainer>

                <Label Name="WaitLabel" />
                <BoxContainer Orientation="Horizontal">
                    <Button Name="DismissButton"
                            Text="{Loc 'admin-notes-message-dismiss'}"
                            Disabled="True"
                            HorizontalExpand="True"
                            StyleClasses="OpenRight" />
                    <Button Name="AcceptButton"
                            Text="{Loc 'admin-notes-message-accept'}"
                            Disabled="True"
                            HorizontalExpand="True"
                            StyleClasses="OpenLeft" />
                </BoxContainer>
            </BoxContainer>
        </Control>
    </PanelContainer>
</Control>
