- type: entity
  id: BulletSpecial
  name: "куля (.38 special)"
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Projectile
    damage:
      types:
        Piercing: 30

- type: entity
  id: BulletSpecialPractice
  name: "куля (.38 special practice)"
  parent: BaseBulletPractice
  categories: [ HideSpawnMenu ]
  components:
  - type: Projectile
    damage:
      types:
        Blunt: 1

- type: entity
  id: BulletSpecialRubber
  name: "куля (.38 спеціальний гумовий)"
  parent: BaseBulletRubber
  categories: [ HideSpawnMenu ]
  components:
  - type: Projectile
    damage:
      types:
        Blunt: 3
  - type: StaminaDamageOnCollide
    damage: 30

- type: entity
  id: BulletSpecialIncendiary
  name: "куля (.38 спеціальний запальний)"
  parent: BaseBulletIncendiary
  categories: [ HideSpawnMenu ]
  components:
  - type: Projectile
    damage:
      types:
        Blunt: 3
        Heat: 27

- type: entity
  id: BulletSpecialUranium
  name: "куля (.38 спеціальний уран)"
  parent: BaseBulletUranium
  categories: [ HideSpawnMenu ]
  components:
  - type: Projectile
    damage:
      types:
        Radiation: 12
        Piercing: 18

- type: entity
  id: BulletSpecialHoly
  name: "куля (.38 special holy)"
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Projectile
    damage:
      types:
        Holy: 6
        Piercing: 25

- type: entity
  id: BulletSpecialMindbreaker
  name: "куля (.38 special mindbreaker)"
  parent: BaseBulletPractice
  categories: [ HideSpawnMenu ]
  components:
  - type: Projectile
    damage:
      types:
        Blunt: 1
  - type: SolutionContainerManager
    solutions:
      ammo:
        maxVol: 9
  - type: RefillableSolution
    solution: ammo
  - type: DrainableSolution
    solution: ammo
  - type: SolutionInjectOnProjectileHit
    transferAmount: 9
    solution: ammo
  - type: InjectableSolution
    solution: ammo
