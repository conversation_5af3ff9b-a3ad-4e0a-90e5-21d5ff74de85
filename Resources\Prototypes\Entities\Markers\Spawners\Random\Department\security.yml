# Security 
- type: entity
  name: "точка появи базового охоронного луту"
  suffix: Gear, Simple
  id: LootSpawnerSecurityBasic
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Objects/Weapons/Melee/flash.rsi
          state: flash
    - type: RandomSpawner
      rarePrototypes:
        - ClothingEyesGlassesSunglasses
        - BoxHandcuff
      rareChance: 0.05
      prototypes:
        - Flash
        - Handcuffs
        - Zipties
        - FlashlightSeclite
        - Tourniquet
      chance: 0.85

- type: entity
  name: "точка появи базового охоронного луту"
  suffix: Gear, Better
  id: LootSpawnerSecurity
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Objects/Weapons/Melee/flash.rsi
          state: flash
    - type: RandomSpawner
      rarePrototypes:
        - ClothingEyesGlassesSunglasses
        - BoxHandcuff
        - BoxFlashbang
        - TearGasGrenade
        - WeaponDisabler
        - Stunbaton
      rareChance: 0.05
      prototypes:
        - Flash
        - Handcuffs
        - Zipties
        - FlashlightSeclite
        - GrenadeFlashBang
        - SecurityWhistle
        - Tourniquet
      chance: 0.85
 
# Armory loot spawner
# Automatics are a complete mess right now, so the AK-MS And WT-550 have been commented out for consistency's sake. I have no idea what role these guns are supposed to fufill.
- type: entity
  name: "точка появи збройового луту"
  suffix: Guns, Armor
  id: LootSpawnerArmory
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Clothing/OuterClothing/Armor/security.rsi
          state: icon
        - sprite: Objects/Weapons/Guns/Shotguns/pump.rsi
          state: icon
    - type: RandomSpawner
      rarePrototypes:
        - ClothingHeadHelmetSwat
        - WeaponShotgunEnforcer
#        - WeaponRifleAk
      rareChance: 0.05
      prototypes:
        - RiotShield
        - RiotBulletShield
        - RiotLaserShield
        - ClothingHeadHelmetBasic
        - ClothingHeadHelmetRiot
        - ClothingOuterArmorBasic
        - ClothingOuterArmorRiot
        - ClothingOuterArmorBulletproof
#        - WeaponSubMachineGunWt550
        - WeaponDisabler
        - WeaponPistolMk58
        - WeaponRifleLecter
        - WeaponSubMachineGunDrozd
        - WeaponLaserCarbine
        - WeaponShotgunKammerer
      chance: 0.95
      offset: 0.0

- type: entity
  name: "точка появи збройового луту"
  suffix: Guns
  id: LootSpawnerArmoryGunsOnly
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Objects/Weapons/Guns/Shotguns/pump.rsi
          state: icon
    - type: RandomSpawner
      rarePrototypes:
        - WeaponShotgunEnforcer
#        - WeaponRifleAk
      rareChance: 0.05
      prototypes:
#        - WeaponSubMachineGunWt550
        - WeaponDisabler
        - WeaponPistolMk58
        - WeaponRifleLecter
        - WeaponSubMachineGunDrozd
        - WeaponLaserCarbine
        - WeaponShotgunKammerer
      chance: 0.95
      offset: 0.0

- type: entity
  name: "точка появи збройового луту"
  suffix: Armor
  id: LootSpawnerArmoryArmorOnly
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Clothing/OuterClothing/Armor/security.rsi
          state: icon
    - type: RandomSpawner
      rarePrototypes:
        - ClothingHeadHelmetSwat
      rareChance: 0.05
      prototypes:
        - RiotShield
        - RiotBulletShield
        - RiotLaserShield
        - ClothingHeadHelmetBasic
        - ClothingHeadHelmetRiot
        - ClothingOuterArmorBasic
        - ClothingOuterArmorRiot
        - ClothingOuterArmorBulletproof
      chance: 0.95
      offset: 0.0