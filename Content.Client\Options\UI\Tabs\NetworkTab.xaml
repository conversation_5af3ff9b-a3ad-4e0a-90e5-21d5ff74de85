<Control xmlns="https://spacestation14.io"
         xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         x:Class="Content.Client.Options.UI.Tabs.NetworkTab">
    <BoxContainer Orientation="Vertical" >
        <BoxContainer Orientation="Vertical" Margin="8 8 8 8" VerticalExpand="True">
            <BoxContainer Orientation="Horizontal" Margin="4 10 4 0">
                <CheckBox Name="NetPredictCheckbox" Text="{Loc 'ui-options-net-predict'}" />
            </BoxContainer>
            <BoxContainer Orientation="Horizontal" Margin="4 10 4 0">
                <Label Text="{Loc 'ui-options-net-interp-ratio'}" />
                <Control MinSize="8 0" />
                <Slider Name="NetInterpRatioSlider"
                            ToolTip="{Loc 'ui-options-net-interp-ratio-tooltip'}"
                            MaxValue="8"
                            HorizontalExpand="True"
                            MinSize="80 0"
                            Rounded="True" />
                <Control MinSize="8 0" />
                <Label Name="NetInterpRatioLabel" MinSize="48 0" Align="Right" />
                <Control MinSize="4 0"/>
            </BoxContainer>
            <BoxContainer Orientation="Horizontal" Margin="4 10 4 0">
                <Label Text="{Loc 'ui-options-net-predict-tick-bias'}" />
                <Control MinSize="8 0" />
                <Slider Name="NetPredictTickBiasSlider"
                            ToolTip="{Loc 'ui-options-net-predict-tick-bias-tooltip'}"
                            MaxValue="6"
                            MinValue="0"
                            HorizontalExpand="True"
                            MinSize="80 0"
                            Rounded="True" />
                <Control MinSize="8 0" />
                <Label Name="NetPredictTickBiasLabel" MinSize="48 0" Align="Right" />
                <Control MinSize="4 0"/>
            </BoxContainer>
            <BoxContainer Orientation="Horizontal" Margin="4 10 4 0">
                <Label Text="{Loc 'ui-options-net-pvs-spawn'}" />
                <Control MinSize="8 0" />
                <Slider Name="NetPvsSpawnSlider"
                            ToolTip="{Loc 'ui-options-net-pvs-spawn-tooltip'}"
                            MaxValue="150"
                            MinValue="20"
                            HorizontalExpand="True"
                            MinSize="80 0"
                            Rounded="True" />
                <Control MinSize="8 0" />
                <Label Name="NetPvsSpawnLabel" MinSize="48 0" Align="Right" />
                <Control MinSize="4 0"/>
            </BoxContainer>
            <BoxContainer Orientation="Horizontal" Margin="4 10 4 0">
                <Label Text="{Loc 'ui-options-net-pvs-entry'}" />
                <Control MinSize="8 0" />
                <Slider Name="NetPvsEntrySlider"
                            ToolTip="{Loc 'ui-options-net-pvs-entry-tooltip'}"
                            MaxValue="500"
                            MinValue="20"
                            HorizontalExpand="True"
                            MinSize="80 0"
                            Rounded="True" />
                <Control MinSize="8 0" />
                <Label Name="NetPvsEntryLabel" MinSize="48 0" Align="Right" />
                <Control MinSize="4 0"/>
            </BoxContainer>
            <BoxContainer Orientation="Horizontal" Margin="4 10 4 10">
                <Label Text="{Loc 'ui-options-net-pvs-leave'}" />
                <Control MinSize="8 0" />
                <Slider Name="NetPvsLeaveSlider"
                            ToolTip="{Loc 'ui-options-net-pvs-leave-tooltip'}"
                            MaxValue="300"
                            MinValue="20"
                            HorizontalExpand="True"
                            MinSize="80 0"
                            Rounded="True" />
                <Control MinSize="8 0" />
                <Label Name="NetPvsLeaveLabel" MinSize="48 0" Align="Right" />
                <Control MinSize="4 0"/>
            </BoxContainer>
        </BoxContainer>
        <controls:StripeBack HasBottomEdge="False" HasMargins="False">
            <BoxContainer Orientation="Horizontal"
                          Align="End"
                          HorizontalExpand="True"
                          VerticalExpand="True">
                <Button Name="ResetButton"
                        Text="{Loc 'ui-options-reset-all'}"
                        StyleClasses="Danger"
                        HorizontalExpand="True"
                        HorizontalAlignment="Right" />
                <Button Name="DefaultButton"
                        Text="{Loc 'ui-options-default'}"
                        TextAlign="Center"
                        HorizontalAlignment="Right" />
                <Control MinSize="2 0" />
                <Button Name="ApplyButton"
                        Text="{Loc 'ui-options-apply'}"
                        TextAlign="Center"
                        HorizontalAlignment="Right" />
            </BoxContainer>
        </controls:StripeBack>
    </BoxContainer>
</Control>
