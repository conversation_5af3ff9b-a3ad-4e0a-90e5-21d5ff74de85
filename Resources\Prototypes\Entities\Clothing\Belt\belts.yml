# Belts that need/have visualizers

- type: entity
  parent: ClothingBeltStorageBase
  id: ClothingBeltUtility
  name: "поясний ремінь"
  description: "Може вміщати різні речі."
  components:
  - type: Sprite
    sprite: Clothing/Belt/utility.rsi
  - type: Clothing
    sprite: Clothing/Belt/utility.rsi
  - type: Storage
    maxItemSize: Normal
    # TODO: Fill this out more.
    whitelist:
      tags:
        - Powerdrill
        - Wirecutter
        - Crowbar
        - Screwdriver
        - Flashlight
        - Wrench
        - GeigerCounter
        - Flare
        - CableCoil
        - CigPack
        - Radio
        - HolofanProjector
        - Multitool
        - AppraisalTool
        - JawsOfLife
        - GPS
        - WeldingMask
        - RPD
      components:
        - SprayPainter
        - NetworkConfigurator
        - RCD
        - RCDAmmo
        - Welder
        - PowerCell
        - Geiger
        - TrayScanner
        - GasAnalyzer
        - HandLabeler
  - type: ItemMapper
    mapLayers:
      drill:
        whitelist:
          tags:
          - Powerdrill
      rpd:
        whitelist:
          tags:
          - RPD
      cutters_red:
        whitelist:
          tags:
          - Wirecutter
      crowbar:
        whitelist:
          tags:
          - Crowbar
      crowbar_red:
        whitelist:
          tags:
          - CrowbarRed
      jaws:
        whitelist:
          tags:
          - JawsOfLife
      screwdriver_nuke:
        whitelist:
          tags:
          - Screwdriver
      wrench:
        whitelist:
          tags:
          - Wrench
      multitool:
        whitelist:
          tags:
            - Multitool
    sprite: Clothing/Belt/belt_overlay.rsi
  - type: Appearance

- type: entity
  parent: ClothingBeltStorageBase
  id: ClothingBeltChiefEngineer
  name: "пояс головного інженера"
  description: "Тримає інструменти, виглядає шикарно."
  components:
  - type: Sprite
    sprite: Clothing/Belt/ce.rsi
  - type: Clothing
    sprite: Clothing/Belt/ce.rsi
  - type: Storage
    grid:
    - 0,0,9,1
    # TODO: Fill this out more.
    whitelist:
      tags:
        - Wirecutter
        - Crowbar
        - Screwdriver
        - Flashlight
        - Wrench
        - GeigerCounter
        - Flare
        - CableCoil
        - Powerdrill
        - JawsOfLife
        - CigPack
        - Radio
        - HolofanProjector
        - Multitool
        - AppraisalTool
        - RPD
      components:
        - SprayPainter
        - NetworkConfigurator
        - RCD
        - RCDAmmo
        - Welder
        - Flash
        - Handcuff
        - PowerCell
        - Geiger
        - TrayScanner
        - GasAnalyzer
  - type: ItemMapper
    mapLayers:
      drill:
        whitelist:
          tags:
          - Powerdrill
      rpd:
        whitelist:
          tags:
          - RPD
      cutters_red:
        whitelist:
          tags:
          - Wirecutter
      crowbar:
        whitelist:
          tags:
          - Crowbar
      crowbar_red:
        whitelist:
          tags:
          - CrowbarRed
      jaws:
        whitelist:
          tags:
          - JawsOfLife
      screwdriver_nuke:
        whitelist:
          tags:
          - Screwdriver
      multitool:
        whitelist:
          tags:
            - Multitool
      wrench:
        whitelist:
          tags:
          - Wrench
    sprite: Clothing/Belt/belt_overlay.rsi
  - type: Appearance

- type: entity
  parent: ClothingBeltStorageBase
  id: ClothingBeltAssault
  name: "штурмовий пояс"
  description: "Тактичний штурмовий пояс."
  components:
  - type: Sprite
    sprite: Clothing/Belt/assault.rsi
  - type: Clothing
    sprite: Clothing/Belt/assault.rsi
  - type: Storage
    whitelist:
      tags:
        - CigPack
        - Taser
      components:
        - Stunbaton
        - FlashOnTrigger
        - SmokeOnTrigger
        - Flash
        - Handcuff
        - BallisticAmmoProvider
        - Ammo
  - type: ItemMapper
    mapLayers:
      flashbang:
        whitelist:
          components:
          - FlashOnTrigger
      stunbaton:
        whitelist:
          components:
          - Stunbaton
      tear_gas_grenade:
        whitelist:
          components:
          - SmokeOnTrigger
    sprite: Clothing/Belt/belt_overlay.rsi
  - type: Appearance

- type: entity
  parent: ClothingBeltStorageBase
  id: ClothingBeltJanitor
  name: "прибиральницький пояс"
  description: "Ремінь, на якому тримається більшість прибиральницького приладдя."
  components:
  - type: Sprite
    sprite: Clothing/Belt/janitor.rsi
  - type: Clothing
    sprite: Clothing/Belt/janitor.rsi
  - type: Storage
    whitelist:
      tags:
        - Wrench
        - Bottle
        - Spray
        - Soap
        - Flashlight
        - CigPack
        - TrashBag
        - WetFloorSign
        - HolosignProjector
        - Plunger
      components:
        - LightReplacer
        - SmokeOnTrigger
    maxItemSize: Large
  - type: ItemMapper
    mapLayers:
      bottle:
        whitelist:
          tags:
          - Bottle
      bottle_spray:
        whitelist:
          tags:
          - Spray
      wrench:
        whitelist:
          tags:
          - Wrench
    sprite: Clothing/Belt/belt_overlay.rsi
  - type: Appearance

- type: entity
  parent: ClothingBeltStorageBase
  id: ClothingBeltMedical
  name: "медичний пояс"
  description: "Може вміщати різне медичне обладнання."
  components:
  - type: Sprite
    sprite: Clothing/Belt/medical.rsi
  - type: Clothing
    sprite: Clothing/Belt/medical.rsi
  - type: Storage
    whitelist:
      tags:
        - Wrench
        - Bottle
        - Spray
        - Brutepack
        - Bloodpack
        - Gauze
        - Ointment
        - CigPack
        - PillCanister
        - Radio
        - DiscreteHealthAnalyzer
        - AutoinjectorCartridge #Goobstation - ParamedHypo
        - Tourniquet #Goobstation - ParamedHypo
      components:
        - SurgeryTool # Shitmed Change
        - Hypospray
        - Injector
        - Pill
        - HandLabeler
  - type: ItemMapper
    mapLayers:
      bottle:
        whitelist:
          tags:
          - Bottle
      hypo:
        whitelist:
          components:
          - Hypospray
      pill:
        whitelist:
          components:
          - Pill
          tags:
          - PillCanister
      bottle_spray:
        whitelist:
          tags:
          - Spray
      # spray_med:
      #   whitelist:
      #     tags:
      #     - SprayMedical
      # wrench_medical:
      #   whitelist:
      #     tags:
      #     - WrenchMedical
      wrench:
        whitelist:
          tags:
          - Wrench
    sprite: Clothing/Belt/belt_overlay.rsi
  # DeltaV - Add medkit slot to medical belts
  - type: ItemSlots
    slots:
      medkit:
        name: clothing-belt-medkit
        whitelist:
          tags:
          - Medkit
        insertOnInteract: false
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
        ents: []
      medkit: !type:ContainerSlot {}
  # End of DeltaV - Add medkit slot to medical belts.
  - type: Appearance

- type: entity
  parent: ClothingBeltMedical
  id: ClothingBeltMedicalEMT
  name: "Пояс EMT"
  description: "Ідеально підходить для зберігання різного обладнання для екстреної медичної допомоги."
  components:
  - type: Sprite
    sprite: Clothing/Belt/emt.rsi
  - type: Clothing
    sprite: Clothing/Belt/emt.rsi

- type: entity
  parent: ClothingBeltStorageBase
  id: ClothingBeltPlant
  name: "ботанічний пояс"
  description: "Ремінь, який використовується для зберігання більшості матеріалів для гідропоніки. Дивно, але не зелений."
  components:
  - type: Sprite
    sprite: Clothing/Belt/plant.rsi
  - type: Clothing
    sprite: Clothing/Belt/plant.rsi
  - type: Storage
    whitelist:
      tags:
        - PlantAnalyzer
        - PlantSampleTaker
        - BotanyShovel
        - BotanyHoe
        - BotanyHatchet
        - PlantSampleTaker
        - PlantBGone
        - Bottle
        - Syringe
        - CigPack
      components:
        - Seed
        - Smokable
        - HandLabeler
  - type: ItemMapper
    mapLayers:
      hatchet:
        whitelist:
          tags:
          - BotanyHatchet
      hydro:
        whitelist:
          tags:
          - PlantAnalyzer
      hoe:
        whitelist:
          tags:
          - BotanyHoe
      secateurs: # We don't have secateurs and this looks similar enough.
        whitelist:
          tags:
          - BotanyShovel
      plantbgone:
        whitelist:
          tags:
          - PlantBGone
      bottle:
        whitelist:
          tags:
          - Bottle
    sprite: Clothing/Belt/belt_overlay.rsi
  - type: Appearance

- type: entity
  parent: ClothingBeltStorageBase
  id: ClothingBeltChef
  name: "пояс шеф-кухаря"
  description: "Ремінь, який використовується для швидкого доступу до кухонних ножів і приправ."
  components:
  - type: Sprite
    sprite: Clothing/Belt/chef.rsi
  - type: Clothing
    sprite: Clothing/Belt/chef.rsi
  - type: Storage
    whitelist:
      tags:
        - KitchenKnife
        - Cleaver
        - RollingPin
        - Coldsauce
        - Enzyme
        - Hotsauce
        - Ketchup
        - BBQsauce
        - SaltShaker
        - PepperShaker
        - CigPack
        - Packet
        - Skewer
        - MonkeyCube
        - Mayo
      components:
        - Mousetrap
        - Smokable
        - Utensil
  - type: ItemMapper
    mapLayers:
      kitchenknife:
        whitelist:
          tags:
          - KitchenKnife
      cleaver:
        whitelist:
          tags:
          - Cleaver
      rollingpin:
        whitelist:
          tags:
          - RollingPin
      coldsauce:
        whitelist:
          tags:
          - Coldsauce
      enzyme:
        whitelist:
          tags:
          - Enzyme
      hotsauce:
        whitelist:
          tags:
          - Hotsauce
      ketchup:
        whitelist:
          tags:
          - Ketchup
      bbqsauce:
        whitelist:
          tags:
          - BBQsauce
      saltshaker:
        whitelist:
          tags:
          - SaltShaker
      peppershaker:
        whitelist:
          tags:
          - PepperShaker
    sprite: Clothing/Belt/belt_overlay.rsi
  - type: Appearance

- type: entity
  parent: ClothingBeltStorageBase
  id: ClothingBeltSecurity
  name: "ремінь безпеки"
  description: "Містить засоби захисту, такі як наручники та спалахи."
  components:
  - type: Sprite
    sprite: Clothing/Belt/security.rsi
  - type: Clothing
    sprite: Clothing/Belt/security.rsi
  - type: ExplosionResistance # Goobstation
    damageCoefficient: 0.5
  - type: Storage
    whitelist:
      tags:
        - CigPack
        - Taser
        - SecBeltEquip
        - Radio
        - Sidearm
        - MagazinePistol
        - MagazineMagnum
        - CombatKnife
        - Truncheon
      components:
        - Stunbaton
        - FlashOnTrigger
        - SmokeOnTrigger
        - Flash
        - Handcuff
        - BallisticAmmoProvider
        - CartridgeAmmo
        - DoorRemote
        - Whistle
        - HolosignProjector
        - BalloonPopper
  - type: ItemMapper
    mapLayers:
      flashbang:
        whitelist:
          components:
          - FlashOnTrigger
      stunbaton:
        whitelist:
          components:
          - Stunbaton
      tear_gas_grenade:
        whitelist:
          components:
          - SmokeOnTrigger
    sprite: Clothing/Belt/belt_overlay.rsi
  - type: Appearance
  - type: EmitsSoundOnMove
    soundCollection:
      collection: FootstepTacticalWebbing
      params:
        volume: -10
    distanceWalking: 2
    distanceSprinting: 3

- type: entity
  parent: [ClothingBeltBase, ClothingSlotBase]
  id: ClothingBeltSheath
  name: "піхви для шаблі"
  description: "Витіюваті піхви, призначені для носіння офіцерського клинка."
  components:
  - type: Sprite
    sprite: Clothing/Belt/sheath.rsi
    state: sheath
  - type: Clothing
    sprite: Clothing/Belt/sheath.rsi
  - type: Item
    size: Large
  - type: ItemSlots
    slots:
      item:
        name: Sabre
        insertVerbText: sheath-insert-verb
        ejectVerbText: sheath-eject-verb
        insertSound: /Audio/SimpleStation14/Items/Handling/sword_sheath.ogg
        ejectSound: /Audio/SimpleStation14/Items/Handling/sword_unsheath.ogg
        whitelist:
          tags:
          - CaptainSabre
  - type: ItemMapper
    mapLayers:
      sheath-sabre:
        whitelist:
          tags:
          - CaptainSabre
  - type: Appearance

# Belts without visualizers

- type: entity
  parent: ClothingBeltAmmoProviderBase
  id: ClothingBeltBandolier
  name: "бандольє"
  description: "Бандольєр для зберігання набоїв до дробовика."
  components:
  - type: Sprite
    sprite: Clothing/Belt/bandolier.rsi
  - type: Clothing
    sprite: Clothing/Belt/bandolier.rsi
  - type: Item
    size: Huge
  - type: BallisticAmmoProvider
    whitelist:
      tags:
        - ShellShotgun
    capacity: 14

- type: entity
  parent: ClothingBeltBase
  id: ClothingBeltChampion
  name: "чемпіонський пояс"
  description: "Довести світові, що ти найсильніший!"
  components:
  - type: Sprite
    sprite: Clothing/Belt/champion.rsi
  - type: Clothing
    sprite: Clothing/Belt/champion.rsi
    quickEquip: true
  - type: Tag
    tags:
    - Kangaroo

- type: entity
  parent: ClothingBeltStorageBase
  id: ClothingBeltHolster
  name: "наплічна кобура"
  description: "Кобура для носіння пістолета та набоїв. Попередження: тільки для крутих."
  components:
  - type: Sprite
    sprite: Clothing/Belt/holster.rsi
  - type: Clothing
    sprite: Clothing/Belt/holster.rsi
  - type: Storage
    grid:
    - 0,0,3,1
  - type: EmitsSoundOnMove
    soundCollection:
      collection: FootstepTacticalWebbing
      params:
        volume: -10
    distanceWalking: 2
    distanceSprinting: 3

- type: entity
  parent: ClothingBeltStorageBase
  id: ClothingBeltSyndieHolster
  name: "синдикатна наплічна кобура"
  description: "Глибока наплічна кобура, здатна вмістити багато видів балістики."
  components:
  - type: Sprite
    sprite: Clothing/Belt/syndieholster.rsi
  - type: Clothing
    sprite: Clothing/Belt/syndieholster.rsi
  - type: Item
    size: Ginormous
  - type: Storage
    maxItemSize: Huge
    grid:
    - 0,0,3,3
    whitelist:
      components:
        - Gun
        - BallisticAmmoProvider
        - CartridgeAmmo
  - type: EmitsSoundOnMove
    soundCollection:
      collection: FootstepTacticalWebbing
      params:
        volume: -10
    distanceWalking: 2
    distanceSprinting: 3

- type: entity
  parent: ClothingBeltSecurity
  id: ClothingBeltSecurityWebbing
  name: "тактичний жилет"
  description: "Унікальний та універсальний нагрудний кронштейн, може вмістити захисне спорядження."
  components:
  - type: Sprite
    sprite: Clothing/Belt/securitywebbing.rsi
  - type: Clothing
    sprite: Clothing/Belt/securitywebbing.rsi
  - type: EmitsSoundOnMove
    soundCollection:
      collection: FootstepTacticalWebbing
      params:
        volume: -10
    distanceWalking: 2
    distanceSprinting: 3

- type: entity
  parent: ClothingBeltSecurityWebbing
  id: ClothingBeltSecurityWebbingFilled
  name: "тактичний жилет"
  description: "Унікальний та універсальний нагрудний кронштейн, може вмістити захисне спорядження."
  components:
  - type: StorageFill
    contents:
      - id: GrenadeFlashBang
      - id: TearGasGrenade
      - id: Stunbaton
      - id: Handcuffs
      - id: Handcuffs

- type: entity
  parent: ClothingBeltStorageBase
  id: ClothingBeltMercWebbing
  name: "найманська павутина"
  description: "Ідеально підходить для зберігання всього, від боєприпасів до зброї та предметів першої необхідності."
  components:
  - type: Sprite
    sprite: Clothing/Belt/mercwebbing.rsi
  - type: Clothing
    sprite: Clothing/Belt/mercwebbing.rsi
  - type: EmitsSoundOnMove
    soundCollection:
      collection: FootstepTacticalWebbing
      params:
        volume: -10
    distanceWalking: 2
    distanceSprinting: 3

- type: entity
  parent: ClothingBeltStorageBase
  id: ClothingBeltSalvageWebbing
  name: "утилізаційна установка"
  description: "Універсальна розвантажувальна система для роботи в космосі."
  components:
  - type: Sprite
    sprite: Clothing/Belt/salvagewebbing.rsi
  - type: Clothing
    sprite: Clothing/Belt/salvagewebbing.rsi
  - type: EmitsSoundOnMove
    soundCollection:
      collection: FootstepTacticalWebbing
      params:
        volume: -10
    distanceWalking: 2
    distanceSprinting: 3

- type: entity
  parent: [ClothingBeltStorageBase, ContentsExplosionResistanceBase]
  id: ClothingBeltMilitaryWebbing
  name: "тактичний розвантажувальний жилет"
  description: "Комплект тактичного спорядження, що носять абордажні групи."
  components:
  - type: Sprite
    sprite: Clothing/Belt/militarywebbing.rsi
  - type: Clothing
    sprite: Clothing/Belt/militarywebbing.rsi
  - type: ExplosionResistance
    damageCoefficient: 0.5
  - type: EmitsSoundOnMove
    soundCollection:
      collection: FootstepTacticalWebbing
      params:
        volume: -10
    distanceWalking: 2
    distanceSprinting: 3

- type: entity
  parent: ClothingBeltMilitaryWebbing
  id: ClothingBeltMilitaryWebbingMed
  name: "медичний нагрудний каркас"
  description: "Набір тактичних ремінців, які носять спецпризначенці Gorlex Marauder."
  components:
  - type: Sprite
    sprite: Clothing/Belt/militarywebbingmed.rsi
  - type: Clothing
    sprite: Clothing/Belt/militarywebbingmed.rsi
  - type: Item
    size: Huge
  - type: ExplosionResistance
    damageCoefficient: 0.1
  - type: EmitsSoundOnMove
    soundCollection:
      collection: FootstepTacticalWebbing
      params:
        volume: -10
    distanceWalking: 2
    distanceSprinting: 3

- type: entity
  parent: ClothingBeltBase
  id: ClothingBeltSuspenders
  name: "підтяжки"
  description: "За те, що тримаєш штани піднятими."
  components:
  - type: Tag
    tags:
    - MimeBelt
  - type: Sprite
    sprite: Clothing/Belt/suspenders.rsi
    state: icon
  - type: Clothing
    sprite: Clothing/Belt/suspenders.rsi
    quickEquip: true

- type: entity
  parent: ClothingBeltStorageBase
  id: ClothingBeltWand
  name: "ремінь для паличок"
  description: "Пояс, призначений для зберігання різноманітних енергетичних жезлів. Справжня поясна сумка екзотичної магії."
  components:
  - type: Sprite
    sprite: Clothing/Belt/wand.rsi
  - type: Clothing
    sprite: Clothing/Belt/wand.rsi
  - type: Storage
    grid:
    - 0,0,15,1
    whitelist:
      tags:
      - WizardWand

- type: entity
  parent: ClothingBeltBase
  id: ClothingBeltGeneric
  name: "ремінь"
  description: "Звичайний ремінь зі шкіри."
  components:
  - type: Sprite
    sprite: Clothing/Belt/generic.rsi
    state: icon
  - type: Clothing
    sprite: Clothing/Belt/generic.rsi
    renderLayer: innerBelt

- type: entity
  parent: ClothingBeltBase
  id: ClothingBeltGenericThin
  name: "тонкий ремінь"
  description: "Він тонший за звичайний ремінь."
  components:
  - type: Sprite
    sprite: Clothing/Belt/generic_thin.rsi
    state: icon
  - type: Clothing
    sprite: Clothing/Belt/generic_thin.rsi
    renderLayer: innerBelt

- type: entity
  parent: ClothingBeltBase
  id: ClothingBeltGenericThick
  name: "товстий ремінь"
  description: "Смілива модна заява."
  components:
  - type: Sprite
    sprite: Clothing/Belt/generic_thick.rsi
    state: icon
  - type: Clothing
    sprite: Clothing/Belt/generic_thick.rsi
    renderLayer: innerBelt

- type: entity
  parent: ClothingBeltBase
  id: ClothingBeltWaist
  name: "поясний ремінь"
  description: "Пояс, який носять на талії, надаючи вбранню більш приталений силует."
  components:
  - type: Sprite
    sprite: Clothing/Belt/waist.rsi
    state: icon
  - type: Clothing
    sprite: Clothing/Belt/waist.rsi
    renderLayer: innerBelt

- type: entity
  parent: ClothingBeltBase
  id: ClothingBeltWaistThin
  name: "тонкий пояс"
  description: "За те, що підкреслила цю вихоплену талію."
  components:
  - type: Sprite
    sprite: Clothing/Belt/waist_thin.rsi
    state: icon
  - type: Clothing
    sprite: Clothing/Belt/waist_thin.rsi
    renderLayer: innerBelt
