﻿### EmitterComponent

# Shows when attempting to turn the emitter on or off without proper access
comp-emitter-access-locked = The {$target} is access locked!

# Shows when turning the emitter on/off
comp-emitter-turned-on = The {$target} turns on.

comp-emitter-turned-off = The {$target} turns off.

# Shows if the user attempts to activate the emitter while it's un-anchored.
comp-emitter-not-anchored = The {$target} isn't anchored to the ground!

# Upgrades
emitter-component-upgrade-fire-rate = fire rate

emitter-component-current-type = The current selected type is: {$type}.
emitter-component-type-set = Type set to: {$type}
