﻿- type: constructionGraph
  id: FloorBananium
  start: start
  graph:
    - node: start
      edges:
        - to: BananiumFloor
          completed:
            - !type:SnapToGrid
              southRotation: true
          steps:
            - material: Bananium
              amount: 2
              doAfter: 5

    - node: BananiumFloor
      entity: FloorBananiumEntity
      edges:
        - to: start
          completed:
          - !type:SpawnPrototype
                prototype: MaterialBananium1
                amount: 2
          - !type:DeleteEntity {}
          steps:
            - tool: Screwing
              doAfter: 5
            - tool: Welding
              doAfter: 5
