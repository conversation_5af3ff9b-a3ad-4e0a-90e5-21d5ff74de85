- type: htnCompound
  id: GoliathCompound
  branches:
    - tasks:
        - !type:HTNCompoundTask
          task: GoliathMeleeCombatPrecondition
    - tasks:
        - !type:HTNCompoundTask
          task: IdleCompound

- type: htnCompound
  id: GoliathMeleeCombatPrecondition
  branches:
    - preconditions:
        - !type:BuckledPrecondition
          isBuckled: true
      tasks:
        - !type:HTNPrimitiveTask
          operator: !type:UnbuckleOperator
            shutdownState: TaskFinished

    - preconditions:
        - !type:InContainerPrecondition
          isInContainer: true
      tasks:
        - !type:HTNCompoundTask
          task: EscapeCompound

    - preconditions:
        - !type:PulledPrecondition
          isPulled: true
      tasks:
        - !type:HTNPrimitiveTask
          operator: !type:UnPullOperator
            shutdownState: TaskFinished

    - tasks:
        - !type:HTNPrimitiveTask
          operator: !type:UtilityOperator
            proto: NearbyMeleeTargets
        - !type:HTNCompoundTask
          task: GoliathAttackTargetCompound

- type: htnCompound
  id: GoliathAttackTargetCompound
  branches:
    - preconditions:
        - !type:KeyExistsPrecondition
          key: Target
      tasks:
        - !type:HTNPrimitiveTask
          operator: !type:MoveToOperator
            shutdownState: PlanFinished
            pathfindInPlanning: true
            removeKeyOnFinish: false
            targetKey: TargetCoordinates
            pathfindKey: TargetPathfind
            rangeKey: MeleeRange
        - !type:HTNPrimitiveTask
          operator: !type:JukeOperator
            jukeType: AdjacentTile
        - !type:HTNPrimitiveTask
          operator: !type:MeleeOperator
            targetKey: Target
          preconditions:
            - !type:KeyExistsPrecondition
              key: Target
            - !type:TargetInRangePrecondition
              targetKey: Target
              rangeKey: MeleeRange
          services:
            - !type:UtilityService
              id: MeleeService
              proto: NearbyMeleeTargets
              key: Target
