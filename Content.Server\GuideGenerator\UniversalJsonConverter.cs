using System.Reflection;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Content.Server.GuideGenerator
{
    // This class is used as a shim to help do polymorphic serialization of objects into JSON
    // (serializing objects that inherit abstract base classes or interfaces) since
    // System.Text.Json (our new JSON solution) doesn't support that while Newtonsoft.Json (our old
    // solution) does.
    public sealed class UniversalJsonConverter<T> : JsonConverter<T>
    {

        // This converter can only convert types that are T or descend from T.
        public override bool CanConvert(Type typeToConvert)
        {
            return typeof(T).IsAssignableFrom(typeToConvert);
        }

        // We don't support deserialization right now. In order to do so, we'd need to bundle a
        // field like "$type" with our objects so they'd be reserialized into the correct base class
        // but that presents a security hazard.
        public override T Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            // Throwing a NotImplementedException here allows the Utf8JsonReader to provide
            // an error message that provides the specific JSON path of the problematic object
            // rather than a generic error message. At least in theory. Haven't tested that.
            throw new NotImplementedException();
        }

        // The bread and butter. Deserialize an object of parameter type T.
        // This method is automatically called when the JSON writer finds an object of a type
        // where we've registered this class as its converter using the [JsonConverter(...)] attribute
        public override void Write(Utf8JsonWriter writer, T obj, JsonSerializerOptions options)
        {
            // If the object is null, don't include it.
            if (obj is null)
            {
                writer.WriteNullValue();
                return;
            }

            // Use reflection to get a list of fields and properties on the object we're serializing.
            // Using obj.GetType() here instead of typeof(T) allows us to get the true base class rather
            // than the abstract ancestor, even if we're parameterized with that abstract class.
            FieldInfo[] fields = obj.GetType().GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            PropertyInfo[] properties = obj.GetType().GetProperties(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);

            // Since the JSON writer will have already written the field name, we need to write the object itself.
            // Since we only use this class to serialize complex objects, we know we'll be writing a JSON object, so open one.
            writer.WriteStartObject();

            // For each field, try to write it into the object.
            foreach (FieldInfo field in fields)
            {
                // If the field has a [JsonIgnore] attribute, skip it
                if (Attribute.GetCustomAttribute(field, typeof(JsonIgnoreAttribute), true) != null) continue;

                // exclude fields that are compiler autogenerated like "__BackingField" fields
                if (Attribute.GetCustomAttribute(field, typeof(System.Runtime.CompilerServices.CompilerGeneratedAttribute), true) != null) continue;

                // If the field has a [JsonPropertyName] attribute, get the property name. Otherwise, use the field name.
                JsonPropertyNameAttribute? attr = (JsonPropertyNameAttribute?) Attribute.GetCustomAttribute(field, typeof(JsonPropertyNameAttribute), true);
                string name = attr == null ? field.Name : attr.Name;

                // Write a new key/value pair into the JSON object itself.
                WriteKV(writer, name, field.GetValue(obj), options);
            }

            // Repeat the same process for each property.
            foreach (PropertyInfo prop in properties)
            {
                // If the field has a [JsonIgnore] attribute, skip it
                if (Attribute.GetCustomAttribute(prop, typeof(JsonIgnoreAttribute), true) != null) continue;

                // If the property has a [JsonPropertyName] attribute, get the property name. Otherwise, use the property name.
                JsonPropertyNameAttribute? attr = (JsonPropertyNameAttribute?) Attribute.GetCustomAttribute(prop, typeof(JsonPropertyNameAttribute), true);
                string name = attr == null ? prop.Name : attr.Name;

                // Write a new key/value pair into the JSON object itself.
                WriteKV(writer, name, prop.GetValue(obj), options);
            }

            // Close the object, we're done!
            writer.WriteEndObject();
        }

        // This is a little utility method to write a key/value pair inside a JSON object.
        // It's used for all the actual writing.
        public void WriteKV(Utf8JsonWriter writer, string key, object? obj, JsonSerializerOptions options)
        {
            // First, write the property name
            writer.WritePropertyName(key);

            // Then, recurse. This ensures that primitive values will be written directly, while
            // more complex values can use any custom converters we've registered (like this one.)
            JsonSerializer.Serialize(writer, obj, options);
        }
    }
}
