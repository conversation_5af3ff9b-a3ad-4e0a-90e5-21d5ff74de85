#HUMANOID FIGURINES

- type: entity
  parent: BaseItem
  id: BaseFigurine
  name: "статуетка"
  description: "Маленька мініатюра."
  abstract: true
  components:
  - type: Sprite
    sprite: Objects/Fun/figurines.rsi
  - type: PhysicalComposition
    materialComposition:
      Plastic: 100
  - type: StaticPrice
    price: 75
  - type: Tag
    tags:
    - Figurine
  - type: StealTarget
    stealGroup: Figurines

- type: entity
  parent: BaseFigurine
  id: ToyFigurineHeadOfPersonnel
  name: "фігурка голови персоналу"
  description: "Фігурка, із славнозвісним зображеннямголови персоналу, що, як завжди, перебуває далеко від свого офісу."
  components:
  - type: Sprite
    state: hop

- type: entity
  parent: BaseFigurine
  id: ToyFigurinePassenger
  name: "пасажирська фігурка"
  description: "Фігурка, що зображає буденне, пересічне пасажирство станції. Не знаходить нічого цікавого."
  components:
  - type: Sprite
    state: passenger

- type: entity
  parent: BaseFigurine
  id: ToyFigurineGreytider
  name: "сіріша фігура"
  description: "Фігурка із зображенням пасажира сумнівного вигляду. Грейтайд по всьому світу!"
  components:
  - type: Sprite
    state: passenger_greytide

- type: entity
  parent: BaseFigurine
  id: ToyFigurineClown
  name: "фігурка клоуна"
  description: "Фігурка, що зображує клоуна. Ви здригаєтеся, думаючи через що вона могла пройти."
  components:
  - type: Sprite
    state: clown

- type: entity
  parent: BaseFigurine
  id: ToyFigurineHoloClown
  name: "фігурка голоклоуна"
  description: "Фігурка, що зображує голографічних клоунів. Ще більш дратівливих, ніж звичайних, і не менш реальних.."
  components:
  - type: Sprite
    state: holoclown


- type: entity
  parent: BaseFigurine
  id: ToyFigurineMime
  name: "фігурка міма"
  description: "Фігурка, що зображує мовчазного виродка, з яким ви добре знайомі."
  components:
  - type: Sprite
    state: mime

- type: entity
  parent: BaseFigurine
  id: ToyFigurineMusician
  name: "фігурка музиканта"
  description: "Фігурка, що зображує музиканта, їх музика вражає."
  components:
  - type: Sprite
    state: musician

- type: entity
  parent: BaseFigurine
  id: ToyFigurineBoxer
  name: "фігура боксера"
  description: "Фігурка, що зображує боксера, як завжди з рукавичками напоготові."
  components:
  - type: Sprite
    state: boxer

- type: entity
  parent: BaseFigurine
  id: ToyFigurineCaptain
  name: "фігурка капітана"
  description: "Фігурка, що зображує стандартне капітанське вбрання певної станції Нанотрейзен."
  components:
  - type: Sprite
    state: captain

- type: entity
  parent: BaseFigurine
  id: ToyFigurineHeadOfSecurity
  name: "фігурка голови служби безпеки"
  description: "Фігурка із не сумнозвісним зображенням голови відділу служби безпеки."
  components:
  - type: Sprite
    state: hos

- type: entity
  parent: BaseFigurine
  id: ToyFigurineWarden
  name: "фігурка наглядача"
  description: "Фігурка, що зображує наглядача, з готовністю в будь-який час посадити когось за ґрати."
  components:
  - type: Sprite
    state: warden

- type: entity
  parent: BaseFigurine
  id: ToyFigurineDetective
  name: "фігура детектива"
  description: "Фігурка, що зображує детектива в культовому пальті."
  components:
  - type: Sprite
    state: detective

- type: entity
  parent: BaseFigurine
  id: ToyFigurineSecurity
  name: "фігурка офіцера"
  description: "Фігурка, що зображує співробітництво служби безпеки з кийком, що точно готове захищати станцію."
  components:
  - type: Sprite
    state: security

- type: entity
  parent: BaseFigurine
  id: ToyFigurineLawyer
  name: "фігура юриста"
  description: "Фігурка, що зображує адвоката у свіжо пошитому костюмі."
  components:
  - type: Sprite
    state: lawyer

- type: entity
  parent: BaseFigurine
  id: ToyFigurineCargoTech
  name: "фігура вантажника"
  description: "Фігурка, що зображує рептилію, працюючу на вантажних вакансіях Карго."
  components:
  - type: Sprite
    state: cargotech

- type: entity
  parent: BaseFigurine
  id: ToyFigurineSalvage
  name: "фігурка шахтарства"
  description: "Фігурка із шахтарським зображенням, що тримає в руках ніж для виживання."
  components:
  - type: Sprite
    state: salvage

- type: entity
  parent: BaseFigurine
  id: ToyFigurineQuartermaster
  name: "фігурка голови Карго" # DeltaV - Logistics Department replacing Cargo
  description: "Фігурка з славнозвісним зображенням голови Карго." # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Sprite
    state: qm

- type: entity
  parent: BaseFigurine
  id: ToyFigurineChiefEngineer
  name: "фігурка голови інженерії"
  description: "Фігурка із славнозвісним зображенням голови інженерного відділу."
  components:
  - type: Sprite
    state: ce

- type: entity
  parent: BaseFigurine
  id: ToyFigurineEngineer
  name: "фігурка інженерії"
  description: "Фігурка, що зображає інженерію станції, з ломом напоготові."
  components:
  - type: Sprite
    state: engineer

- type: entity
  parent: BaseFigurine
  id: ToyFigurineAtmosTech
  name: "фігурка атмосферного техніка"
  description: "Фігурка, що зображає атосферну інженерію, із ще викненим зварювальним апаратом у руках."
  components:
  - type: Sprite
    state: atmos

- type: entity
  parent: BaseFigurine
  id: ToyFigurineResearchDirector
  name: "фігурка наукового директора" # DeltaV - Epistemics Department replacing Science
  description: "Фігурка із славнозвісним зображенням директора наукового відділу." # DeltaV - Epistemics Department replacing Science
  components:
  - type: Sprite
    state: rd

- type: entity
  parent: BaseFigurine
  id: ToyFigurineScientist
  name: "фігурка вчених"
  description: "Фігурка, що зображує вчених в лабораторному халаті."
  components:
  - type: Sprite
    state: scientist

- type: entity
  parent: BaseFigurine
  id: ToyFigurineChiefMedicalOfficer
  name: "фігурка головного лікаря"
  description: "Фігурка із славносвісним зображенням голови медичного відділу."
  components:
  - type: Sprite
    state: cmo

- type: entity
  parent: BaseFigurine
  id: ToyFigurineChemist
  name: "фігурка хіміка"
  description: "Фігурка, що зображає хіміка, що, імовірно, планує виготовити метамфетамін."
  components:
  - type: Sprite
    state: chemist

- type: entity
  parent: BaseFigurine
  id: ToyFigurineParamedic
  name: "фігурка парамедика"
  description: "Фігурка, що зображує парамедика в бронескафандрі порожнечі."
  components:
  - type: Sprite
    state: paramedic

- type: entity
  parent: BaseFigurine
  id: ToyFigurineMedicalDoctor
  name: "фігурка лікаря"
  description: "Фігурка, що зображає лікаря в халаті та зі шприцом."
  components:
  - type: Sprite
    state: medical

- type: entity
  parent: BaseFigurine
  id: ToyFigurineLibrarian
  name: "фігурка бібліотекаря"
  description: "Фігурка, що зображає бібліотекаря, єдиного та неповторного."
  components:
  - type: Sprite
    state: librarian

- type: entity
  parent: BaseFigurine
  id: ToyFigurineChaplain
  name: "фігурка капелана"
  description: "Фігурка, що зображає капелана, що з надією молиться за добрі справи."
  components:
  - type: Sprite
    state: chaplain

- type: entity
  parent: BaseFigurine
  id: ToyFigurineChef
  name: "фігурка шеф-кухаря"
  description: "Фігурка, що зображає шеф-кухаря, повного майстерності кулінарного мистецтва... час від часу."
  components:
  - type: Sprite
    state: chef

- type: entity
  parent: BaseFigurine
  id: ToyFigurineBartender
  name: "фігурка бармена"
  description: "Фігурка, що зображає бармена, стильно виглядаючого в рокерських окулярах окулярам і циліндрі."
  components:
  - type: Sprite
    state: bartender

- type: entity
  parent: BaseFigurine
  id: ToyFigurineBotanist
  name: "фігурка гідропоніки"
  description: "Фігурка, що зображує екіпаж парцюючий в гідропоніці, який точно не дасть кудзу вийти з-під контролю."
  components:
  - type: Sprite
    state: botanist

- type: entity
  parent: BaseFigurine
  id: ToyFigurineJanitor
  name: "фігурка прибиральника"
  description: "Фігурка прибирального персоналу в ґалошах."
  components:
  - type: Sprite
    state: janitor

- type: entity
  parent: BaseFigurine
  id: ToyFigurineNukie
  name: "фігурка оперативника"
  description: "Фігурка, що зображає постать в криваво-червоному комбінезоні, схожому на той, що можуть носити члени ядерної оперативної групи."
  components:
  - type: Sprite
    state: nukie

- type: entity
  parent: BaseFigurine
  id: ToyFigurineNukieElite
  name: "оперативний працівник елітного синдикату"
  description: "Фігурка, що зображає людину в елітному криваво-червоному комбінезоні, схожому на той, що може носити член команди ядерних агентів."
  components:
  - type: Sprite
    state: nukie_elite

- type: entity
  parent: BaseFigurine
  id: ToyFigurineNukieCommander
  name: "фігурка командування оперативної групи"
  description: "Фігурка, що зображає постать у криваво-червоному комбінезоні, схожому на той, що може носити командування ядерної оперативної групи."
  components:
  - type: Sprite
    state: nukie_commander

- type: entity
  parent: BaseFigurine
  id: ToyFigurineFootsoldier
  name: "фігурка піхотинця синдикату"
  description: "Фігурка із зображенням вбрання піхотинця синдикату."
  components:
  - type: Sprite
    state: footsoldier

- type: entity
  parent: BaseFigurine
  id: ToyFigurineWizard
  name: "фігурка чарівника"
  description: "Фігурка, що зображає постать з довгою шовковистою бородою в чарівницькому вбранні. Магли мріють мати такі."
  components:
  - type: Sprite
    state: wizard

- type: entity
  parent: BaseFigurine
  id: ToyFigurineWizardFake
  name: "фігурка псевдочарівника"
  description: "Фігурка, що зображає постать у підробленому костюмі чарівника. Який цирк!"
  components:
  - type: Sprite
    state: wizard_fake

#Nonhuman Figurines

- type: entity
  parent: BaseFigurine
  id: ToyFigurineSpaceDragon
  name: "фігурка космічного дракона"
  description: "Велика фігурка, що зображує космічного дракона, червоні очі які вічно дивляться на вас."
  components:
  - type: Sprite
    state: spacedragon

- type: entity
  parent: BaseFigurine
  id: ToyFigurineQueen
  name: "фігура ксенокоролеви"
  description: "Велика фігурка, що зображує королеву ксеносів, готову до атаки."
  components:
  - type: Sprite
    state: queen

- type: entity
  parent: BaseFigurine
  id: ToyFigurineRatKing
  name: "фігурка щурячого короля"
  description: "Велика фігурка, що зображує щурячого короля, що готується звити собі гніздо."
  components:
  - type: Sprite
    state: ratking

- type: entity
  parent: BaseFigurine
  id: ToyFigurineRatServant
  name: "фігурка щурячого підданства"
  description: "Фігурка, що зображує щура, що живе в ім'я свого короля!"
  components:
  - type: Sprite
    state: ratservant

- type: entity
  parent: BaseFigurine
  id: ToyFigurineMouse
  name: "фігурка миші"
  description: "Фігурка, що зображає мишу, що дременула до найближчого шматка їжі чи калюжі пролитого напою."
  components:
  - type: Sprite
    state: mouse

- type: entity
  parent: BaseFigurine
  id: ToyFigurineSlime
  name: "фігурка слизняка"
  description: "Фігурка, що зображує напівпрозору слизову істоту."
  components:
  - type: Sprite
    state: slime

- type: entity
  parent: BaseFigurine
  id: ToyFigurineHamlet
  name: "фігурка Гамлета"
  description: "Фігурка із зображенням Гамлета, не класти у мікрохвиьову піч."
  components:
  - type: Sprite
    state: hamlet

#TODO: Convert these to the new figurine sprite template and rename their sprite name.
#Yes, these are humanoids, but they need to be updated first.

- type: entity
  parent: BaseFigurine
  id: ToyGriffin
  name: "фігурка Грифона"
  description: "Фігурка, змодельована на честь \"Грифона\", відома в кримінального авторитеті."
  components:
  - type: Sprite
    state: griffinprize

- type: entity
  parent: BaseFigurine
  id: ToyOwlman
  name: "фігурка Сови"
  description: "Фігурка, створена за мотивами \"Сови\", на захисті справедливості."
  components:
  - type: Sprite
    state: owlprize

- type: entity
  parent: BaseFigurine
  id: ToySkeleton
  name: "фігурка скелета"
  description: "Виглядає лякаюче КІСТКИ КІСТКИ!"
  components:
  - type: Sprite
    state: skeletonprize

- type: entity
  parent: BaseFigurine
  id: ToyFigurineThief
  name: "фігурка крадія"
  description: "Ви чуєте скрипи закритих дверей вдалині..."
  components:
  - type: Sprite
    state: thiefcharacter
