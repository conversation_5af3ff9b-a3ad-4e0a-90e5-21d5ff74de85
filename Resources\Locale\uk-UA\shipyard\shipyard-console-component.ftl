## UI
shipyard-console-invalid-vessel = Неможливо придбати судно:
shipyard-commands-purchase-desc = Створює та приєднує FTL до вказаного човника з файлу сітки.
shipyard-console-no-idcard = Немає ID картки
shipyard-console-already-deeded = ID-картка вже має Корабель
shipyard-console-invalid-station = Недійсна станція
shipyard-console-no-bank = Банківський рахунок не знайдено
shipyard-console-no-deed = Судновий акт не знайдено
shipyard-console-sale-reqs = Корабель має бути пришвартований, а весь екіпаж висаджений
shipyard-console-deed-label = Зареєстроване судно:

shipyard-console-leaving = Капітан {$owner} корабля {$vessel} проданий {$player}.
shipyard-console-docking-secret = Незареєстрований корабель входить у сектор.
shipyard-console-leaving-secret = Незареєстрований корабель покинув сектор.
