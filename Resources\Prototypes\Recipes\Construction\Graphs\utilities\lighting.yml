- type: constructionGraph
  id: CyanLight
  start: start
  graph:
    - node: start
      edges:
        - to: icon
          steps:
            - material: Glass
              amount: 1
              doAfter: 1
            - tag: CrystalCyan
              name: cyan crystal shard
              icon:
                sprite: Objects/Materials/Shards/crystal.rsi
                state: shard1
                color: #52ff39
              doAfter: 1
    - node: icon
      entity: LightTubeCrystalCyan

- type: constructionGraph
  id: BlueLight
  start: start
  graph:
    - node: start
      edges:
        - to: icon
          steps:
            - material: Glass
              amount: 2
              doAfter: 1
            - tag: CrystalBlue
              name: blue crystal shard
              icon:
                sprite: Objects/Materials/Shards/crystal.rsi
                state: shard1
              doAfter: 1
    - node: icon
      entity: LightTubeCrystalBlue

- type: constructionGraph
  id: PinkLight
  start: start
  graph:
    - node: start
      edges:
        - to: icon
          steps:
            - material: Glass
              amount: 2
              doAfter: 1
            - tag: CrystalPink
              name: pink crystal shard
              icon:
                sprite: Objects/Materials/Shards/crystal.rsi
                state: shard1
              doAfter: 1
    - node: icon
      entity: LightTubeCrystalPink

- type: constructionGraph
  id: OrangeLight
  start: start
  graph:
    - node: start
      edges:
        - to: icon
          steps:
            - material: Glass
              amount: 2
              doAfter: 1
            - tag: CrystalOrange
              name: orange crystal shard
              icon:
                sprite: Objects/Materials/Shards/crystal.rsi
                state: shard1
              doAfter: 1
    - node: icon
      entity: LightTubeCrystalOrange

- type: constructionGraph
  id: RedLight
  start: start
  graph:
    - node: start
      edges:
        - to: icon
          steps:
            - material: Glass
              amount: 2
              doAfter: 1
            - tag: CrystalRed
              name: red crystal shard
              icon:
                sprite: Objects/Materials/Shards/crystal.rsi
                state: shard1
              doAfter: 1
    - node: icon
      entity: LightTubeCrystalRed

- type: constructionGraph
  id: GreenLight
  start: start
  graph:
    - node: start
      edges:
        - to: icon
          steps:
            - material: Glass
              amount: 2
              doAfter: 1
            - tag: CrystalGreen
              name: green crystal shard
              icon:
                sprite: Objects/Materials/Shards/crystal.rsi
                state: shard1
              doAfter: 1
    - node: icon
      entity: LightTubeCrystalGreen
