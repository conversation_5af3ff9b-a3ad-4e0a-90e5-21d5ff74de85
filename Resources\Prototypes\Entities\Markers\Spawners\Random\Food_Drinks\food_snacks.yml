- type: entity
  id: RandomSnacks
  name: "спавнер випадкового снеку"
  #suffix: ""
  parent: MarkerBase
  placement:
    mode: AlignTileAny
  components:
  - type: Sprite
    layers:
      - state: red
      - sprite: Objects/Consumable/Food/snacks.rsi
        state: cheesiehonkers
  - type: RandomSpawner
  #small item
    prototypes:
      - FoodSnackBoritos
      - FoodSnackCheesie
      - FoodSnackChips
      - FoodSnackChocolate
      - FoodSnackEnergy
      - FoodSnackChocolateBar
      - FoodSnackPopcorn
      - FoodSnackRaisins
      - FoodSnackSus
      - FoodSnackSyndi
      - FoodSnackChowMein
      - FoodSnackDanDanNoodles
      - FoodSnackCookieFortune
      - FoodSnackNutribrick
      - FoodSnackMREBrownie
      - FoodFrozenSandwich
      - FoodFrozenSandwichStrawberry
      - FoodFrozenFreezy
      - FoodFrozenSundae
      - FoodFrozenCornuto
      - FoodFrozenPopsicleOrange
      - FoodFrozenPopsicleBerry
      - FoodFrozenPopsicleJumbo
      - FoodFrozenSnowcone
      - FoodFrozenSnowconeBerry
      - FoodFrozenSnowconeFruit
      - FoodFrozenSnowconeClown
      - FoodFrozenSnowconeMime
      - FoodFrozenSnowconeRainbow
      - FoodSnackPistachios
      - FoodSnackSemki
      - FoodPSB # Delta V
      - FoodLollipop # Delta V
      - FoodGumball # Delta V
    chance: 0.8
    offset: 0.0
