using System.Numerics;

namespace Content.Server.Shuttles.Components
{
    [RegisterComponent]
    public sealed partial class ShuttleComponent : Component
    {
        [ViewVariables]
        public bool Enabled = true;

        [ViewVariables]
        public Vector2[] CenterOfThrust = new Vector2[4];

        /// <summary>
        /// Thrust gets multiplied by this value if it's for braking.
        /// </summary>
        public const float BrakeCoefficient = 1.5f;

        /// <summary>
        /// Maximum velocity assuming unupgraded, tier 1 thrusters
        /// </summary>
        [ViewVariables(VVAccess.ReadWrite)]
        public float BaseMaxLinearVelocity = 20f;

        public const float MaxAngularVelocity = 4f;

        /// <summary>
        /// The cached thrust available for each cardinal direction
        /// </summary>
        [ViewVariables]
        public readonly float[] LinearThrust = new float[4];

        /// <summary>
        /// The thrusters contributing to each direction for impulse.
        /// </summary>
        // No touchy
        public readonly List<EntityUid>[] LinearThrusters = new List<EntityUid>[]
        {
            new(),
            new(),
            new(),
            new(),
        };

        /// <summary>
        /// The thrusters contributing to the angular impulse of the shuttle.
        /// </summary>
        public readonly List<EntityUid> AngularThrusters = new();

        [ViewVariables]
        public float AngularThrust = 0f;

        /// <summary>
        /// A bitmask of all the directions we are considered thrusting.
        /// </summary>
        [ViewVariables]
        public DirectionFlag ThrustDirections = DirectionFlag.None;

        /// <summary>
        /// Damping applied to the shuttle's physics component when not in FTL.
        /// </summary>
        [DataField("linearDamping"), ViewVariables(VVAccess.ReadWrite)]
        public float LinearDamping = 0.05f;

        [DataField("angularDamping"), ViewVariables(VVAccess.ReadWrite)]
        public float AngularDamping = 0.05f;

        /// <summary>
        ///     How far from the shuttle's bounding box will it crush and destroy things?
        /// </summary>
        [DataField]
        public float SmimshDistance = 0.2f;

        /// <summary>
        ///     Whether or not the shuttle calls the DoTheDinosaur function upon FTL'ing. I'm not explaining this, you owe it to yourself to do a code search for it.
        /// </summary>
        [DataField]
        public bool DoTheDinosaur = true;
    }
}
