# gloves
- type: entity
  id: ActionToggleNinjaGloves
  name: "Рукавички ніндзя з перемикачем"
  description: "Вмикає всі дії рукавичок за допомогою лівої кнопки миші. Включає відмикання дверей, викачування енергії, оглушення ворогів і злом певних комп'ютерів."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    priority: -13
    event: !type:ToggleActionEvent {}

# suit
- type: entity
  id: ActionCreateThrowingStar
  name: "Створіть метальну зірку"
  description: "Використовує силу костюма для створення метальної зірки, яка завдає додаткової шкоди витривалості."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    useDelay: 0.5
    icon:
      sprite: Objects/Weapons/Throwable/throwing_star.rsi
      state: icon
    itemIconStyle: NoItem
    priority: -10
    event: !type:CreateItemEvent {}

- type: entity
  id: ActionRecallKatana
  name: "Згадати катану"
  description: "Телепортує пов'язану з цим костюмом Енергетичну катану до її власника, вартість залежить від відстані."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    useDelay: 1
    icon:
      sprite: Objects/Weapons/Melee/energykatana.rsi
      state: icon
    itemIconStyle: NoItem
    priority: -11
    event: !type:RecallKatanaEvent {}

- type: entity
  id: ActionNinjaEmp
  name: "Електромагнітний сплеск"
  description: "Вимкніть електромагнітним імпульсом будь-яку техніку поблизу."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon:
      sprite: Objects/Weapons/Grenades/empgrenade.rsi
      state: icon
    itemIconStyle: BigAction
    priority: -13
    event: !type:NinjaEmpEvent {}

- type: entity
  id: ActionTogglePhaseCloak
  name: "Фазовий плащ"
  description: "Перемикає фазовий плащ вашого костюма. Майте на увазі, що якщо в вас влучать, усі здібності буде вимкнено на 5 секунд, включаючи плащ!"
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    # have to plan (un)cloaking ahead of time
    useDelay: 5
    priority: -9
    event: !type:ToggleActionEvent

# katana
- type: entity
  id: ActionEnergyKatanaDash
  name: "Катання на катані"
  description: "Телепортуйтеся в будь-яке місце, яке ви бачите, якщо ваша Енергетична Катана у вас в руці."
  categories: [ HideSpawnMenu ]
  components:
  - type: WorldTargetAction
    icon:
      sprite: Objects/Magic/magicactions.rsi
      state: blink
    itemIconStyle: NoItem
    sound:
      path: /Audio/Magic/blink.ogg
      params:
        volume: 5
    priority: -12
    event: !type:DashEvent
    checkCanAccess: false
    range: 0
