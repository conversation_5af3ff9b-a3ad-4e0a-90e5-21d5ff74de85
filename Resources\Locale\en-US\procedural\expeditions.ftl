salvage-expedition-structure-examine = This is a [color=#B02E26]destruction[/color] objective
salvage-expedition-structure-remaining = {$count ->
    [one] {$count} structure remaining.
    *[other] {$count} structures remaining.
}

salvage-expedition-type = Mission
salvage-expedition-window-title = Salvage expeditions
salvage-expedition-window-difficulty = Difficulty:
salvage-expedition-window-details = Details:
salvage-expedition-window-hostiles = Hostiles:
salvage-expedition-window-duration = Duration:
salvage-expedition-window-biome = Biome:
salvage-expedition-window-modifiers = Modifiers:

offering-window-claimed = Claimed
offering-window-claim = Claim

salvage-expedition-window-next = Next offer

salvage-expedition-difficulty-Moderate = Moderate
salvage-expedition-difficulty-Hazardous = Hazardous
salvage-expedition-difficulty-Extreme = Extreme

salvage-expedition-difficulty-players = Recommended salvagers:

# Runner
salvage-expedition-not-all-present = Not all salvagers are aboard the shuttle!

salvage-expedition-announcement-countdown-minutes = {$duration} minutes remaining to complete the expedition.
salvage-expedition-announcement-countdown-seconds = {$duration} seconds remaining to complete the expedition.
salvage-expedition-announcement-dungeon = Dungeon is located {$direction}.
salvage-expedition-completed = Expedition is completed.
salvage-expedition-reward-description = Mission completion reward
