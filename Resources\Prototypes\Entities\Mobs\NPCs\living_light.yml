- type: entity
  id: MobLivingLight
  parent: [ BaseMob, MobCombat ]
  abstract: true
  name: "яскрава людоподібна істота"
  description: "Сліпуча фігура чистого світла, що здається нематеріальною."
  components:
  - type: HTN
    rootTask:
      task: SimpleRangedHostileCompound
    blackboard:
      NavClimb: !type:Bool
        true
      NavSmash: !type:Bool
        true
  - type: NpcFactionMember
    factions:
    - SimpleHostile
  - type: MovementIgnoreGravity
  - type: MovementSpeedModifier
    baseWalkSpeed: 3.5
    baseSprintSpeed: 3.5
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Elemental/living_light/luminous_person.rsi
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: luminous_person
    - map: [ "enum.DamageStateVisualLayers.BaseUnshaded" ]
      state: glow
      shader: unshaded
  - type: MobState
  - type: SlowOnDamage
    speedModifierThresholds:
      20: 0.5
  - type: DamageStateVisuals
    states:
      Alive:
        Base: luminous_person
        BaseUnshaded: glow
      Dead:
        Base: dead
        BaseUnshaded: dead_glow
  - type: StatusEffects
    allowed:
    - Stun
    - Corporeal
    - Electrocution
    - StaminaModifier
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 60
        mask:
          - MobMask
        layer:
          - Opaque
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: LivingLight
  - type: PassiveDamage
    allowedStates:
    - Alive
    damageCap: 20
    damage:
      types:
        Heat: -0.2
  - type: NoSlip
  - type: Pullable
  - type: ZombieImmune
  - type: NameIdentifier
    group: GenericNumber
  - type: GhostTakeoverAvailable
  - type: PointLight
    radius: 3.0
    energy: 4.5
    color: "#6270bb"
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepBells
  - type: Speech
    speechVerb: Ghost
  - type: Tag
    tags:
      - FootstepSound
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 25
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
          - !type:PlaySoundBehavior
            sound:
              collection: GlassBreak

- type: entity
  id: MobLuminousPerson
  parent: MobLivingLight
  components:
  - type: MeleeWeapon
    damage:
      types:
        Heat: 10
    animation: WeaponArcFist
  - type: StaminaDamageOnHit
    damage: 16

- type: entity
  id: MobLuminousObject
  parent: MobLivingLight
  name: "мерехтливий об'єкт"
  description: "Невеликий предмет, що світиться і своїм сяйвом викликає опіки на шкірі."
  components:
  - type: Sprite
    sprite: Mobs/Elemental/living_light/luminous_object.rsi
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: luminous_object
    - map: [ "enum.DamageStateVisualLayers.BaseUnshaded" ]
      state: glow
      shader: unshaded
  - type: DamageStateVisuals
    states:
      Alive:
        Base: luminous_object
        BaseUnshaded: glow
      Dead:
        Base: dead
        BaseUnshaded: dead_glow
  - type: MeleeWeapon
    damage:
      types:
        Heat: 6

- type: entity
  id: MobLuminousEntity
  parent: MobLivingLight
  name: "світний об'єкт"
  description: "Сліпуче напівпрозоре утворення, яскраве око здається небезпечним і обпалюючим."
  components:
  - type: Sprite
    sprite: Mobs/Elemental/living_light/luminous_entity.rsi
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: luminous_entity
    - map: [ "enum.DamageStateVisualLayers.BaseUnshaded" ]
      state: glow
      shader: unshaded
  - type: DamageStateVisuals
    states:
      Alive:
        Base: luminous_entity
        BaseUnshaded: glow
      Dead:
        Base: dead
        BaseUnshaded: dead_glow
  - type: MeleeWeapon
    damage:
      types:
        Heat: 5
  - type: HitscanBatteryAmmoProvider
    proto: RedLaser
    fireCost: 140
  - type: Battery
    maxCharge: 1000
    startingCharge: 1000
  - type: BatterySelfRecharger
    autoRecharge: true
    autoRechargeRate: 50
  - type: Gun
    fireRate: 0.3
    useKey: false
    showExamineText: false
    selectedMode: SemiAuto
    availableModes:
      - SemiAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/laser3.ogg
    soundEmpty:
      path: /Audio/Items/Lighters/lighter_off.ogg
