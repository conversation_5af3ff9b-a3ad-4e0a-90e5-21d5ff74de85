- type: entity
  id: BoozeDispenser
  name: "диспенсер алкоголю"
  suffix: Filled
  description: "Дозатор для напоїв з одним гніздом для наповнення контейнера."
  parent: ReagentDispenserBase
  components:
  - type: Rotatable
  - type: Sprite
    sprite: Structures/smalldispensers.rsi
    drawdepth: SmallObjects
    state: booze
  - type: ReagentDispenser
    storageWhitelist:
      tags:
      - DrinkBottle
    pack: BoozeDispenserInventory
  - type: Transform
    noRot: false
  - type: Machine
    board: BoozeDispenserMachineCircuitboard
  - type: GuideHelp
    guides:
    - Bartender
  - type: StealTarget
    stealGroup: BoozeDispenser

- type: entity
  id: BoozeDispenserEmpty
  suffix: Empty
  parent: BoozeDispenser
  components:
  - type: ReagentDispenser
    storageWhitelist:
      tags:
      - DrinkBottle
    pack: EmptyInventory
