- type: job
  id: SeniorOfficer
  name: job-name-senior-officer
  description: job-description-senior-officer
  playTimeTracker: JobSeniorOfficer
  setPreference: true
  requirements:
    - !type:CharacterPlaytimeRequirement
      tracker: JobWarden
      min: 21600 #6 hrs
    - !type:CharacterPlaytimeRequirement
      tracker: JobDetective
      min: 7200 #2 hrs
    - !type:CharacterPlaytimeRequirement
      tracker: JobSecurityOfficer
      min: 21600 #6 hrs
    - !type:CharacterDepartmentTimeRequirement
      department: Security
      min: 216000 # 60 hrs
    - !type:CharacterTraitRequirement
      inverted: true
      traits:
        - Foreigner
        - ForeignerLight
        - Muted
        - Blindness
        - Pacifist
        - BrittleBoneDisease
    - !type:CharacterEmployerRequirement
      employers:
      - ZavodskoiInterstellar
      - PMCG
      - NanoTrasen
      - EastOrionCompany
  startingGear: SeniorOfficerGear
  icon: "JobIconSeniorOfficer"
  supervisors: job-supervisors-hos
  canBeAntag: false
  access:
  - Security
  #- Brig # Delta V: Removed
  - Maintenance
  - Service
  - External
  special:
  - !type:AddImplantSpecial
    implants: [ MindShieldImplant ]
  afterLoadoutSpecial:
  - !type:ModifyEnvirosuitSpecial
    charges: 6
  - !type:ModifyEnvirohelmSpecial
    powerCell: PowerCellHigh

- type: startingGear
  id: SeniorOfficerGear
  subGear:
  - SecurityOfficerPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitSeniorOfficer
    back: ClothingBackpackSecurity
    shoes: ClothingShoesBootsCombatFilled
    eyes: ClothingEyesGlassesSecurity
    head: ClothingHeadHatBeretSecurity
    outerClothing: ClothingOuterArmorPlateCarrier # DeltaV - ClothingOuterArmorBasic replaced in favour of plate carrier
    id: SeniorOfficerPDA
    ears: ClothingHeadsetSecurity
    belt: ClothingBeltSecurityFilled
  innerClothingSkirt: ClothingUniformJumpskirtSeniorOfficer
  satchel: ClothingBackpackSatchelSecurity
  duffelbag: ClothingBackpackDuffelSecurity
