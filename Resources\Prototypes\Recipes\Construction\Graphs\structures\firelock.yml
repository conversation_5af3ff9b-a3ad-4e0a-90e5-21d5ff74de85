﻿- type: constructionGraph
  id: Firelock
  start: start
  graph:
    - node: start
      edges:
        - to: frame1
          completed:
            - !type:SnapToGrid { }
          steps:
            - material: Steel
              amount: 3
              doAfter: 1


    - node: frame1
      entity: FirelockFrame
      actions:
      - !type:AppearanceChange
      edges:
        - to: frame2
          conditions:
            - !type:EntityAnchored
              anchored: true
          steps:
            - material: Cable
              amount: 2
              doAfter: 1
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: SheetSteel1
              amount: 3
            - !type:DeleteEntity {}
          conditions:
            - !type:EntityAnchored
              anchored: true
          steps:
            - tool: Welding
              doAfter: 1

    - node: frame2
      actions:
      - !type:AppearanceChange
      edges:
        - to: frame3
          conditions:
            - !type:EntityAnchored
              anchored: true
          steps:
            - tag: FirelockElectronics
              store: board
              name: Firelock Electronics
              icon:
                sprite: "Objects/Misc/module.rsi"
                state: "mainboard"

        - to: frame1
          completed:
            - !type:SpawnPrototype
              prototype: CableApcStack1
              amount: 2
          conditions:
            - !type:EntityAnchored
              anchored: true
          steps:
            - tool: Cutting
              doAfter: 1.5

    - node: frame3
      actions:
      - !type:AppearanceChange
      edges:
        - to: frame4
          conditions:
            - !type:EntityAnchored
              anchored: true
          steps:
            - tool: Screwing
              doAfter: 0.25
        - to: frame2
          completed:
            - !type:EmptyAllContainers
              pickup: true
              emptyAtUser: true
          conditions:
            - !type:EntityAnchored
              anchored: true
          steps:
            - tool: Prying
              doAfter: 0.25

    - node: frame4
      entity: FirelockFrame
      actions:
      - !type:AppearanceChange
      edges:
        - to: Firelock
          conditions:
            - !type:EntityAnchored
              anchored: true
          steps:
            - tool: Anchoring
              doAfter: 1

        - to: FirelockEdge
          conditions:
            - !type:EntityAnchored
              anchored: true
          steps:
            - tool: Welding
              doAfter: 0.5

        - to: FirelockGlassFrame
          conditions:
            - !type:EntityAnchored
              anchored: true
          steps:
            - material: Glass
              amount: 2
              doAfter: 2

        - to: frame3
          conditions:
            - !type:EntityAnchored
              anchored: true
          steps:
            - tool: Screwing
              doAfter: 0.75

    - node: Firelock
      entity: Firelock
      edges:
        - to: frame4
          conditions:
            - !type:DoorWelded
              welded: true
          steps:
            - tool: Anchoring
              doAfter: 0.25

    - node: FirelockGlassFrame
      edges:
        - to: FirelockGlass
          conditions:
            - !type:EntityAnchored
              anchored: true
          steps:
            - tool: Anchoring
              doAfter: 1
        - to: frame4
          completed:
            - !type:SpawnPrototype
              prototype: SheetGlass1
              amount: 2
          steps:
            - tool: Prying
              doAfter: 1


    - node: FirelockGlass
      entity: FirelockGlass
      edges:
        - to: FirelockGlassFrame
          conditions:
            - !type:EntityAnchored
              anchored: true
          steps:
            - tool: Anchoring
              doAfter: 1

    - node: FirelockEdge
      entity: FirelockEdge
      edges:
        - to: frame4
          conditions:
            - !type:DoorWelded
              welded: true
          steps:
            - tool: Anchoring
              doAfter: 0.25
