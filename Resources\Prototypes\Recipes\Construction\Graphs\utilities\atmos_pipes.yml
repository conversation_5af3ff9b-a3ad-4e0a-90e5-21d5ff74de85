- type: constructionGraph
  id: GasPipe
  start: start
  graph:
  - node: start
    edges:
    - to: half
      steps:
      - material: Steel
        amount: 1
        doAfter: 1

    - to: straight
      steps:
      - material: Steel
        amount: 1
        doAfter: 1

    - to: bend
      steps:
      - material: Steel
        amount: 1
        doAfter: 1

    - to: tjunction
      steps:
      - material: Steel
        amount: 1
        doAfter: 1

    - to: fourway
      steps:
      - material: Steel
        amount: 1
        doAfter: 1

  - node: half
    entity: GasPipeHalf
    edges:
    - to: start
      conditions:
      - !type:EntityAnchored
        anchored: false
      completed:
      - !type:SpawnPrototype
        prototype: SheetSteel1
        amount: 1
      - !type:DeleteEntity
      steps:
      - tool: Welding
        doAfter: 1

  - node: straight
    entity: GasPipeStraight
    edges:
    - to: start
      conditions:
      - !type:EntityAnchored
        anchored: false
      completed:
      - !type:SpawnPrototype
        prototype: SheetSteel1
        amount: 1
      - !type:DeleteEntity
      steps:
      - tool: Welding
        doAfter: 1

  - node: bend
    entity: GasPipeBend
    edges:
    - to: start
      conditions:
      - !type:EntityAnchored
        anchored: false
      completed:
      - !type:SpawnPrototype
        prototype: SheetSteel1
        amount: 1
      - !type:DeleteEntity
      steps:
      - tool: Welding
        doAfter: 1

  - node: tjunction
    entity: GasPipeTJunction
    edges:
    - to: start
      conditions:
      - !type:EntityAnchored
        anchored: false
      completed:
      - !type:SpawnPrototype
        prototype: SheetSteel1
        amount: 1
      - !type:DeleteEntity
      steps:
      - tool: Welding
        doAfter: 1

  - node: fourway
    entity: GasPipeFourway
    edges:
    - to: start
      conditions:
      - !type:EntityAnchored
        anchored: false
      completed:
      - !type:SpawnPrototype
        prototype: SheetSteel1
        amount: 1
      - !type:DeleteEntity
      steps:
      - tool: Welding
        doAfter: 1

  - node: broken
    entity: GasPipeBroken
    edges:
    - to: start
      conditions:
      - !type:EntityAnchored
        anchored: false
      completed:
      - !type:SpawnPrototype
        prototype: SheetSteel1
        amount: 1
      - !type:DeleteEntity
      steps:
      - tool: Welding
        doAfter: 1
