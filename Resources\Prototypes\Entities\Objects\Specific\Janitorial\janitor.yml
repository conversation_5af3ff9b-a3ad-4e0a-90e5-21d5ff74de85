- type: entity
  parent: BaseItem
  name: "швабра"
  id: MopItem
  description: "Шв<PERSON><PERSON><PERSON>а, яку неможливо зупинити, чекає детальне очищення нутрощів."
  components:
  - type: Sprite
    sprite: Objects/Specific/Janitorial/mop.rsi
    state: mop
  - type: MeleeWeapon
    range: 1.85
    damage:
      types:
        Blunt: 2
    bluntStaminaDamageFactor: 3
    heavyRateModifier: 2.5
    heavyRangeModifier: 1.3
    heavyDamageBaseModifier: 1.25
    heavyStaminaCost: 5
    maxTargets: 7
    angle: 160
    soundHit:
      collection: MetalThud
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 6
    staminaCost: 8
  - type: Spillable
    solution: absorbed
  - type: Wieldable
  - type: IncreaseDamageOnWield
    damage:
      types:
        Blunt: 5
  - type: Item
    size: Large
    sprite: Objects/Specific/Janitorial/mop.rsi
  - type: Absorbent
    footprintCleaningRange: 0.45
    maxCleanedFootprints: 25
  - type: SolutionContainerManager
    solutions:
      absorbed:
        maxVol: 100
  - type: UseDelay
    delay: 1
  - type: PhysicalComposition
    materialComposition:
      Plastic: 50
  - type: Tag
    tags:
      - Mop
      - MopBasic
  - type: GuideHelp
    guides:
    - Janitorial

- type: entity
  parent: BaseItem
  name: "вдосконалена швабра"
  id: AdvMopItem
  description: "Моторизована швабра, яка має більший резервуар і швидко замінює реагенти всередині водою. Автоматична протидія клоунам не входить до комплекту."
  components:
    - type: Sprite
      sprite: Objects/Specific/Janitorial/advmop.rsi
      state: advmop
    - type: MeleeWeapon
      range: 1.85
      damage:
        types:
          Blunt: 2
      bluntStaminaDamageFactor: 3
      heavyRateModifier: 2.5
      heavyRangeModifier: 1.3
      heavyDamageBaseModifier: 1.25
      heavyStaminaCost: 5
      maxTargets: 7
      angle: 160
      soundHit:
         collection: MetalThud
    - type: DamageOtherOnHit
      damage:
        types:
          Blunt: 6
      staminaCost: 8
    - type: Spillable
      solution: absorbed
    - type: Wieldable
    - type: IncreaseDamageOnWield
      damage:
        types:
          Blunt: 5
    - type: Item
      size: Large
      sprite: Objects/Specific/Janitorial/advmop.rsi
    - type: Absorbent
      pickupAmount: 100
      footprintCleaningRange: 0.75
      maxCleanedFootprints: 25
    - type: UseDelay
      delay: 1.0
    - type: SolutionRegeneration
      solution: absorbed
      generated:
        reagents:
        - ReagentId: Water
          Quantity: 5
    - type: SolutionPurge
      solution: absorbed
      preserve:
      - Water
      quantity: 10
    - type: SolutionContainerManager
      solutions:
        absorbed:
          maxVol: 100
    - type: Tag
      tags:
        - Mop
        - MopAdv

- type: entity
  name: "знак мокрої підлоги"
  id: WetFloorSign
  parent: ClothingOuterBase
  description: "Обережно! Мокра підлога!"
  components:
  - type: Sprite
    sprite: Objects/Specific/Janitorial/wet_floor_sign.rsi
  - type: Item
    sprite: Objects/Specific/Janitorial/wet_floor_sign.rsi
    size: Normal
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.95
        Slash: 0.95
  - type: PhysicalComposition
    materialComposition:
      Plastic: 50
  - type: Tag
    tags:
      - WetFloorSign
      - WhitelistChameleon


- type: entity
  suffix: Explosive
  parent: WetFloorSign
  id: WetFloorSignMineExplosive
  components:
  - type: StepTrigger
    intersectRatio: 0.2
    requiredTriggeredSpeed: 0
    stepOn: true
  - type: CollisionWake
    enabled: false
  - type: Physics
    bodyType: Dynamic
  - type: Fixtures
    fixtures:
      slips:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.2,-0.2,0.2,0.2"
        hard: false
        layer:
          - LowImpassable
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.2,-0.2,0.2,0.2"
        density: 30
        mask:
        - ItemMask
  - type: LandMine
    sound:
      path: /Audio/Effects/beep_landmine.ogg
      params:
        maxDistance: 10
  - type: ExplodeOnTrigger
  - type: Explosive
    explosionType: HardBomb  # normally Default and max 5 total 60
    maxIntensity: 10 # about a ~67.5 total damage
    totalIntensity: 30 # about a ~3 tile radius
    canCreateVacuum: false
  - type: DeleteOnTrigger
  - type: OnUseTimerTrigger
    useVerbInstead: true
    beepInterval: .25
    beepSound:
      path: /Audio/Items/Janitor/floor_sign_beep.ogg
      params:
        volume: 1
    examinable: false
  - type: Tag
    tags: # ignore "WhitelistChameleon" tag
      - WetFloorSign

- type: entity
  name: "вантуз"
  id: Plunger
  parent: BaseItem
  description: "Вантуз з червоною пластиковою присоскою та дерев'яною ручкою. Використовується для прочищення зливів."
  components:
  - type: Tag
    tags:
       - Plunger
       - WhitelistChameleon
  - type: Sprite
    sprite: Objects/Specific/Janitorial/plunger.rsi
    state: plunger
  - type: Item
    sprite: Objects/Specific/Janitorial/plunger.rsi
    heldPrefix: plunger
  - type: GuideHelp
    guides:
    - Janitorial
  - type: Clothing
    sprite: Objects/Specific/Janitorial/plunger.rsi
    slots:
      - HEAD
      - HEAD1 #PIRATE
      - HEAD2 #PIRATE
    clothingVisuals:
      head:
        - state: equipped-HELMET
          offset: "0, 0.15625"
  - type: Fixtures
    fixtures:
      fix1:
        shape: !type:PhysShapeCircle
          radius: 0.2
        density: 5
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.1,-0.1,0.1,0.1"
        hard: false
        mask:
        - Impassable
        - BulletImpassable
  - type: EmbeddableProjectile
    sound: /Audio/Weapons/pop.ogg
    embedOnThrow: True
  - type: ThrowingAngle
    angle: 0
  - type: LandAtCursor
  - type: Ammo
    muzzleFlash: null
  - type: Projectile
    deleteOnCollide: false
    onlyCollideWhenShot: true
    damage:
      types:
        Blunt: 3
  - type: Plunger

- type: weightedRandomEntity
  id: PlungerLoot
  weights:
    RandomSpawner100: 56
    SpacemenFigureSpawner: 28
    SpawnMobCockroach: 5
    MaintenanceToolSpawner: 5

- type: entity
  parent: BaseItem
  name: "волога ганчірка"
  id: RagItem
  description: "Для прибирання безладу, напевно."
  components:
    - type: Sprite
      sprite: Objects/Specific/Janitorial/rag.rsi
      state: rag
    - type: Spillable
      solution: absorbed
    - type: MeleeWeapon
      soundNoDamage:
        path: "/Audio/Effects/Fluids/splat.ogg"
      damage:
        types:
          Blunt: 0
    - type: Item
      size: Small
      sprite: Objects/Specific/Janitorial/rag.rsi
    - type: Absorbent
      pickupAmount: 15
    - type: Construction
      graph: Rag
      node: rag
    - type: SolutionContainerManager
      solutions:
        absorbed:
          maxVol: 30
    - type: UseDelay
      delay: 1.5
    - type: Tag
      tags:
        - Mop
    - type: CleansForensics
    - type: Fiber
      fiberColor: fibers-white
