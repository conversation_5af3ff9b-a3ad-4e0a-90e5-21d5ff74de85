comp-kitchen-spike-deny-collect = На { CAPITALIZE($this) } вже щось є, спочатку закінчіть збирати м'ясо!
comp-kitchen-spike-deny-butcher = { CAPITALIZE($victim) } не може бути патране на { $this }.
comp-kitchen-spike-deny-butcher-knife = { CAPITALIZE($victim) } не можна патрати на { $this }, потрібно зарізати ножем.
comp-kitchen-spike-deny-not-dead = { CAPITALIZE($victim) } не можна патрати. { CAPITALIZE(SUBJECT($victim)) } ще живий!

comp-kitchen-spike-begin-hook-victim = { $user } почав чіпляти вас на { $this }!
comp-kitchen-spike-begin-hook-self = Ви почали чіпляти себе на { $this }!

comp-kitchen-spike-kill = { CAPITALIZE($user) } вбив { $victim } повісивши { POSS-ADJ($victim) } на гак для м'яса!

comp-kitchen-spike-suicide-other = { CAPITALIZE($victim) } повісив себе на гак для м'яса!
comp-kitchen-spike-suicide-self = Ви повісили себе на гак для м'яса!

comp-kitchen-spike-knife-needed = Для цього вам потрібен ніж.
comp-kitchen-spike-remove-meat = Ви зрізаєте трохи м'яса з { $victim }.
comp-kitchen-spike-remove-meat-last = Ви зрізали останній шматок м'яса з { $victim }!

comp-kitchen-spike-meat-name = { $name } ({ $victim })
