using System.Linq;
using Content.Shared.Access.Components;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.DeltaV.CartridgeLoader.Cartridges;

[GenerateTypedNameReferences]
public sealed partial class EditChatPopup : DefaultWindow
{
    private const int MaxNumberLength = 4;

    // Used to see if the user input is different from the original data
    // to check if the user can submit
    private string _originalNumber = "";
    private string _originalName = "";
    private string _originalJob = "";

    public event Action<uint, string, string?>? OnContactEdited;

    public EditChatPopup()
    {
        RobustXamlLoader.Load(this);

        // margins trolling
        ContentsContainer.Margin = new Thickness(3);

        // Button handlers
        CancelButton.OnPressed += _ => Close();
        ConfirmButton.OnPressed += _ => EditChat();

        // Input validation
        NameInput.OnTextChanged += _ => ValidateInputs();

        NameInput.OnTextChanged += args =>
        {
            if (args.Text.Length > IdCardConsoleComponent.MaxFullNameLength)
                NameInput.Text = args.Text[..IdCardConsoleComponent.MaxFullNameLength];
            ValidateInputs();
        };

        JobInput.OnTextChanged += args =>
        {
            if (args.Text.Length > IdCardConsoleComponent.MaxJobTitleLength)
                JobInput.Text = args.Text[..IdCardConsoleComponent.MaxJobTitleLength];
            ValidateInputs();
        };

        NumberInput.OnTextChanged += args =>
        {
            if (args.Text.Length > MaxNumberLength)
                NumberInput.Text = args.Text[..MaxNumberLength];

            // Filter to digits only
            var newText = string.Concat(NumberInput.Text.Where(char.IsDigit));
            if (newText != NumberInput.Text)
                NumberInput.Text = newText;

            ValidateInputs();
        };
    }

    private void ValidateInputs()
    {
        var isValid = !string.IsNullOrWhiteSpace(NumberInput.Text) &&
                      !string.IsNullOrWhiteSpace(NameInput.Text) &&
                      NumberInput.Text.Length == MaxNumberLength &&
                      uint.TryParse(NumberInput.Text, out _) &&
                      // Only valid if there are any changes
                      (NumberInput.Text != _originalNumber ||
                      NameInput.Text != _originalName ||
                      JobInput.Text != _originalJob);

        ConfirmButton.Disabled = !isValid;
    }

    private void EditChat()
    {
        if (!uint.TryParse(NumberInput.Text, out var number))
            return;

        var name = NameInput.Text.Trim();
        var job = string.IsNullOrWhiteSpace(JobInput.Text) ? null : JobInput.Text.Trim();

        OnContactEdited?.Invoke(number, name, job);
        Close();
    }

    public void ClearInputs()
    {
        NameInput.Text = string.Empty;
        JobInput.Text = string.Empty;
        ValidateInputs();
    }

    public void SetNumberInput(string newNumber)
    {
        NumberInput.Text = newNumber.PadLeft(MaxNumberLength, '0');
        _originalNumber = newNumber;
    }

    public void SetNameInput(string newName)
    {
        NameInput.Text = newName;
        _originalName = newName;
    }

    public void SetJobInput(string newJob)
    {
        JobInput.Text = newJob;
        _originalJob = newJob;
    }
}
