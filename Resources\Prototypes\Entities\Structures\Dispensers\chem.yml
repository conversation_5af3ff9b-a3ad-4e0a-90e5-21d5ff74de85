- type: entity
  id: ChemDispenser
  name: "диспенсер хімікатів"
  suffix: Filled
  parent: ReagentDispenserBase
  description: "Дозатор хімікатів промислового класу."
  components:
  - type: Sprite
    sprite: Structures/dispensers.rsi
    state: industrial-working
    snapCardinals: true
  - type: ReagentDispenser
    storageWhitelist:
      tags:
      - ChemDispensable
    pack: ChemDispenserStandardInventory
  - type: ApcPowerReceiver
  - type: ExtensionCableReceiver
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
      - !type:ChangeConstructionNodeBehavior
        node: machineFrame
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Machine
    board: ChemDispenserMachineCircuitboard
  - type: UpgradePowerDraw
    powerDrawMultiplier: 0.75
    scaling: Exponential
  - type: GuideHelp
    guides:
    - Chemicals
    - Chemist
  - type: StealTarget
    stealGroup: ChemDispenser

- type: entity
  id: ChemDispenserEmpty
  name: "диспенсер хімікатів"
  suffix: Empty
  parent: ChemDispenser
  components:
  - type: ReagentDispenser
    pack: EmptyInventory
