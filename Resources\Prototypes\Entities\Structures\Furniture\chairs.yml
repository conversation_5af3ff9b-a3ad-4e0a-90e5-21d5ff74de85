- type: entity
  name: "крісло"
  id: SeatBase
  abstract: true
  description: "Ти сидиш у цьому. Чи то з волі, чи то з примусу."
  placement:
    mode: PlaceFree
  components:
  - type: Clickable
  - type: InteractionOutline
  - type: Physics
    bodyType: Dynamic
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.2
        density: 100
        mask:
        - TableMask
  - type: Sprite
    sprite: Structures/Furniture/chairs.rsi
    noRot: true
  - type: Strap
    position: Stand
    buckleOffset: "0,-0.05"
  - type: Pullable
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 25
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
  - type: StaticPrice
    price: 25
  - type: RequireProjectileTarget

#Starts unanchored, cannot be rotated while anchored
- type: entity
  name: "крісло"
  id: UnanchoredChairBase
  parent: SeatBase
  abstract: true
  components:
  - type: Anchorable
  - type: Rotatable

#Start anchored, cannot be rotated while anchored
- type: entity
  name: "крісло"
  id: ChairBase
  abstract: true
  parent: UnanchoredChairBase
  placement:
    mode: SnapgridCenter
  components:
  - type: Physics
    bodyType: Static
  - type: Transform
    anchored: true

#Starts unanchored, can be rotated while anchored
- type: entity
  name: "крісло"
  id: OfficeChairBase
  parent: UnanchoredChairBase
  abstract: true
  components:
  - type: Rotatable
    rotateWhileAnchored: true

# Goobstation - rolly chairs that actually roll!
- type: entity
  name: "крісло"
  id: RollingOfficeChairBase
  description: "Пересувайтесь від столу до столу, бо стояти – це для інтернів."
  parent: BaseVehicle
  abstract: true
  components:
  - type: Anchorable
  - type: Rotatable
    rotateWhileAnchored: true
  - type: Sprite
    sprite: Structures/Furniture/chairs.rsi
    noRot: true
  - type: Strap
    position: Stand
    buckleOffset: "0,-0.05"
  - type: Vehicle
    requiredHands: 0
    engineRunning: true
    renderOver: South, SouthEast, SouthWest
  - type: MovementSpeedModifier
    acceleration: 10
    friction: 3.5
    baseSprintSpeed: 2.5
    baseWalkSpeed: 1.5
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.2
        density: 75
        mask:
        - TableMask
  - type: StaticPrice
    price: 50

#Starts anchored, can be rotated while anchored
- type: entity
  name: "табурет"
  id: StoolBase
  parent: OfficeChairBase
  abstract: true
  placement:
    mode: SnapgridCenter
  components:
  - type: Physics
    bodyType: Static
  - type: Transform
    anchored: true

- type: entity
  name: "крісло"
  id: Chair
  parent: ChairBase
  components:
  - type: Sprite
    state: chair
  - type: Construction
    graph: Seat
    node: chair

- type: entity
  name: "крісло"
  id: ChairGreyscale
  parent: Chair
  suffix: White
  components:
  - type: Sprite
    state: chair-greyscale

- type: entity
  name: "табурет"
  id: Stool
  parent: UnanchoredChairBase
  description: "Ти сидиш у цьому. Чи то з волі, чи то з примусу."
  components:
  - type: Sprite
    state: stool
  - type: Construction
    graph: Seat
    node: stool

- type: entity
  name: "барний стілець"
  id: StoolBar
  parent: StoolBase
  components:
  - type: Sprite
    state: bar
  - type: Construction
    graph: Seat
    node: stoolBar

- type: entity
  name: "латунний стілець"
  id: ChairBrass
  parent: StoolBase
  components:
  - type: Sprite
    state: brass_chair
  - type: Construction
    graph: Seat
    node: chairBrass

- type: entity
  name: "білий офісний стілець"
  id: ChairOfficeLight
  parent: OfficeChairBase
  components:
  - type: Sprite
    state: office-white
  - type: Construction
    graph: Seat
    node: chairOffice
  - type: SpawnItemsAtLocationOnUse
    spawnItemsVerbText: disable-wheel-locks
    items:
      - id: ChairOfficeLightRolling

- type: entity
  name: "темне офісне крісло"
  id: ChairOfficeDark
  parent: ChairOfficeLight
  components:
  - type: Sprite
    state: office-dark
  - type: Construction
    graph: Seat
    node: chairOfficeDark
  - type: SpawnItemsAtLocationOnUse
    spawnItemsVerbText: disable-wheel-locks
    items:
      - id: ChairOfficeDarkRolling

 # EE start

- type: entity
  name: "білий офісний стілець"
  suffix: No mapping
  id: ChairOfficeLightRolling
  parent: RollingOfficeChairBase
  components:
  - type: Sprite
    state: office-white
  - type: Construction
    graph: Seat
    node: chairOffice
  - type: SpawnItemsAtLocationOnUse
    spawnItemsVerbText: activate-wheel-locks
    items:
      - id: ChairOfficeLight

- type: entity
  name: "темне офісне крісло"
  suffix: No mapping
  id: ChairOfficeDarkRolling
  parent: ChairOfficeLightRolling
  components:
  - type: Sprite
    state: office-dark
  - type: Construction
    graph: Seat
    node: chairOfficeDark
  - type: SpawnItemsAtLocationOnUse
    spawnItemsVerbText: activate-wheel-locks
    items:
      - id: ChairOfficeDark

 # EE end

- type: entity
  name: "зручне крісло"
  id: ComfyChair
  parent: ChairBase
  description: "Виглядає зручно."
  components:
  - type: Sprite
    state: comfy
  - type: Construction
    graph: Seat
    node: chairComfy

- type: entity
  name: "крісло пілота"
  id: ChairPilotSeat
  parent: ChairBase
  description: "Місце пілота престижного корабля."
  components:
  - type: Sprite
    state: shuttle
  - type: Construction
    graph: Seat
    node: chairPilotSeat

- type: entity
  name: "дерев'яний стілець"
  id: ChairWood
  parent: UnanchoredChairBase
  components:
  - type: Sprite
    state: wooden
  - type: Construction
    graph: Seat
    node: chairWood
  - type: Damageable
    damageModifierSet: Wood
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 25
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank:
            min: 1
            max: 1
  - type: Tag
    tags:
    - Wooden
  - type: StaticPrice
    price: 75

- type: entity
  name: "ритуальний стілець"
  id: ChairRitual
  parent: ChairWood
  description: "Дивно вирізьблений стілець."
  components:
  - type: Sprite
    state: ritual
  - type: Construction
    graph: RitualSeat
    node: chairRitual

- type: entity
  id: ChairMeat
  parent: UnanchoredChairBase
  name: "м'ясний стілець"
  description: "Неприємно спітніла."
  components:
  - type: Sprite
    state: meat
  - type: Construction
    graph: Seat
    node: chairMeat

- type: entity
  name: "проклятий стілець"
  id: ChairCursed
  parent: ChairWood
  description: "Воно дивиться назад."
  components:
  - type: Sprite
    state: cursed
  - type: Strap
    buckleSound:
      collection: MaleScreams
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          path: /Audio/Voice/Human/womanlaugh.ogg
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank:
            min: 1
            max: 1
  - type: Construction
    graph: RitualSeat
    node: chairCursed

- type: entity
  name: "павутинне крісло"
  id: ChairWeb
  description: "Хочеш серйозно зайнятися веб-розробкою? Сідай на цей стілець!"
  parent: UnanchoredChairBase
  components:
  - type: Sprite
    sprite: Structures/Web/chair.rsi
    state: icon
  - type: MeleeSound
    soundGroups:
      Brute:
        path:
          "/Audio/Weapons/slash.ogg"
  - type: Damageable
    damageModifierSet: Web
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWebSilk:
            min: 1
            max: 1
  - type: Construction
    graph: WebStructures
    node: chair

- type: entity
  parent: [SeatBase, BaseFoldable]
  id: ChairFolding
  name: "розкладний стілець"
  description: "Стілець, який легко переносити."
  components:
  - type: Rotatable
  - type: Sprite
    sprite: Structures/Furniture/folding_chair.rsi
    layers:
    - state: folding
      map: ["unfoldedLayer"]
    - state: folding_folded
      map: ["foldedLayer"]
      visible: false
  - type: Item
    size: Huge
  - type: Appearance
  - type: MeleeWeapon
    damage:
      types:
        Blunt: 8
    soundHit:
      collection: MetalThud
  - type: Construction
    graph: Seat
    node: chairFolding

- type: entity
  parent: ChairFolding
  id: ChairFoldingSpawnFolded
  suffix: folded
  components:
  - type: Foldable
    folded: true
  - type: Strap
    enabled: False

- type: entity
  name: "сталева лавка"
  id: SteelBench
  parent: ChairBase
  description: "Довгий стілець, зроблений для метро. Дійсно стандартний дизайн."
  components:
  - type: Sprite
    state: steel-bench
  - type: Construction
    graph: Seat
    node: chairSteelBench

- type: entity
  name: "дерев'яна лавка"
  id: WoodenBench
  parent: ChairBase
  description: "У тебе заноза? Ну, принаймні, це екологічно чисто."
  components:
  - type: Sprite
    state: wooden-bench
  - type: Construction
    graph: Seat
    node: chairWoodBench
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank:
            min: 2
            max: 4
  - type: StaticPrice
    price: 20
