﻿- type: entity
  id: WeaponWaterGunBase
  abstract: true
  parent: BaseItem
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Pistols/water_pistol.rsi
  - type: Clothing
    sprite: Objects/Weapons/Guns/Pistols/water_pistol.rsi
    slots: BELT
  - type: Item
    sprite: Objects/Weapons/Guns/Pistols/water_pistol.rsi
    size: Small
  - type: Gun
    clumsyProof: true
    cameraRecoilScalar: 0 #no recoil
    fireRate: 2
    selectedMode: SemiAuto
    availableModes:
    - SemiAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/water_spray.ogg
  - type: SolutionContainerManager
    solutions:
      chamber:
        maxVol: 50 #10 shots
  - type: SolutionAmmoProvider
    solutionId: chamber
    proto: BulletWaterShot
  - type: SolutionTransfer
    transferAmount: 5
    maxTransferAmount: 50
    minTransferAmount: 5
    canChangeTransferAmount: true
  - type: UserInterface
    interfaces:
      enum.TransferAmountUiKey.Key:
        type: TransferAmountBoundUserInterface
  - type: DrawableSolution
    solution: chamber
  - type: RefillableSolution
    solution: chamber
  - type: DrainableSolution
    solution: chamber
  - type: ExaminableSolution
    solution: chamber
  - type: StaticPrice
    price: 100
  - type: PhysicalComposition
    materialComposition:
      Plastic: 150

- type: entity
  id: WeaponWaterPistol
  parent: WeaponWaterGunBase
  name: "водяний пістолет"
  description: "Найхитріша зброя на водній основі. Ти клянешся, що спусковий гачок нічого не робить."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Pistols/water_pistol.rsi
    layers:
    - state: detail
    - state: icon
      map: [ "enum.DamageStateVisualLayers.Base" ]
  - type: RandomSprite
    available:
      - enum.DamageStateVisualLayers.Base:
          icon: Rainbow

- type: entity
  id: WeaponWaterBlaster
  parent: WeaponWaterGunBase
  name: "водяний бластер"
  description: "З цим поганим хлопцем ти будеш найкрутішою дитиною на літньому барбекю."
  components:
  - type: Gun
    cameraRecoilScalar: 0 #no recoil
    fireRate: 3
    selectedMode: FullAuto
    availableModes:
    - FullAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/water_spray.ogg
  - type: SolutionContainerManager
    solutions:
      chamber:
        maxVol: 100 #20 shots
  - type: Sprite
    sprite: Objects/Weapons/Guns/Pistols/soaker.rsi
    layers:
    - state: detail1
    - state: detail2
      map: ["enum.PowerDeviceVisualLayers.Powered"]
    - state: icon
      map: [ "enum.DamageStateVisualLayers.Base" ]
  - type: Item
    sprite: Objects/Weapons/Guns/Pistols/soaker.rsi
    size: Normal
  - type: RandomSprite
    getAllGroups: true
    available:
      - enum.DamageStateVisualLayers.Base:
          icon: Rainbow
      - enum.PowerDeviceVisualLayers.Powered:
          detail2: Sixteen

- type: entity
  id: WeaponWaterBlasterSuper
  parent: WeaponWaterGunBase
  name: "супер водяний бластер"
  description: "Ні! Ні! Тільки не в очі!"
  components:
  - type: Gun
    cameraRecoilScalar: 0 #no recoil
    fireRate: 8
    selectedMode: FullAuto
    availableModes:
    - FullAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/water_spray.ogg
  - type: SolutionContainerManager
    solutions:
      chamber:
        maxVol: 500 #100 shots
  - type: Sprite
    sprite: Objects/Weapons/Guns/Pistols/soaker.rsi
    layers:
    - state: detail1
    - state: detail2
      map: ["enum.PowerDeviceVisualLayers.Powered"]
    - state: icon
      map: [ "enum.DamageStateVisualLayers.Base" ]
  - type: Item
    sprite: Objects/Weapons/Guns/Pistols/soaker.rsi
    size: Normal
  - type: RandomSprite
    getAllGroups: true
    available:
      - enum.DamageStateVisualLayers.Base:
          icon: Rainbow
      - enum.PowerDeviceVisualLayers.Powered:
          detail2: Sixteen
