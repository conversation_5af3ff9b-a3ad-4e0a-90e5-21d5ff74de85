- type: species
  id: Shadow
  name: "назва-виду-Тінь"
  roundStart: false
  prototype: MobShadow
  sprites: MobShadowSprites
  markingLimits: MobHumanMarkingLimits
  dollPrototype: MobShadowDummy
  skinColoration: HumanToned

- type: speciesBaseSprites
  id: MobShadowSprites
  sprites:
    Head: MobShadowHead
    Face: MobHumanoidAnyMarking
    HeadTop: MobHumanoidAnyMarking
    HeadSide: MobHumanoidAnyMarking
    Hair: MobHumanoidAnyMarking
    FacialHair: MobHumanoidAnyMarking
    Snout: MobHumanoidAnyMarking
    Chest: MobShadowTorso
    Underwear: MobHumanoidAnyMarking
    Undershirt: MobHumanoidAnyMarking
    Eyes: MobShadowEyes
    LArm: MobShadowLArm
    RArm: MobShadowRArm
    LHand: MobShadowLHand
    RHand: MobShadowRHand
    LLeg: MobShadowLLeg
    RLeg: MobShadowRLeg
    Tail: MobHumanoidAnyMarking
    LFoot: Mob<PERSON>hadowLFoot
    RFoot: <PERSON>bS<PERSON>owRFoot

- type: humanoidBaseSprite
  id: MobShadowEyes
  baseSprite:
    sprite: Mobs/Customization/eyes.rsi
    state: eyes

- type: humanoidBaseSprite
  id: MobShadowHead
  baseSprite:
    sprite: _EE/Mobs/Species/Shadow/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobShadowHeadMale
  baseSprite:
    sprite: _EE/Mobs/Species/Shadow/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobShadowHeadFemale
  baseSprite:
    sprite: _EE/Mobs/Species/Shadow/parts.rsi
    state: head_f

- type: humanoidBaseSprite
  id: MobShadowTorso
  baseSprite:
    sprite: _EE/Mobs/Species/Shadow/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobShadowTorsoMale
  baseSprite:
    sprite: _EE/Mobs/Species/Shadow/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobShadowTorsoFemale
  baseSprite:
    sprite: _EE/Mobs/Species/Shadow/parts.rsi
    state: torso_f

- type: humanoidBaseSprite
  id: MobShadowLLeg
  baseSprite:
    sprite: _EE/Mobs/Species/Shadow/parts.rsi
    state: l_leg

- type: humanoidBaseSprite
  id: MobShadowLArm
  baseSprite:
    sprite: _EE/Mobs/Species/Shadow/parts.rsi
    state: l_arm

- type: humanoidBaseSprite
  id: MobShadowLHand
  baseSprite:
    sprite: _EE/Mobs/Species/Shadow/parts.rsi
    state: l_hand

- type: humanoidBaseSprite
  id: MobShadowLFoot
  baseSprite:
    sprite: _EE/Mobs/Species/Shadow/parts.rsi
    state: l_foot

- type: humanoidBaseSprite
  id: MobShadowRLeg
  baseSprite:
    sprite: _EE/Mobs/Species/Shadow/parts.rsi
    state: r_leg

- type: humanoidBaseSprite
  id: MobShadowRArm
  baseSprite:
    sprite: _EE/Mobs/Species/Shadow/parts.rsi
    state: r_arm

- type: humanoidBaseSprite
  id: MobShadowRHand
  baseSprite:
    sprite: _EE/Mobs/Species/Shadow/parts.rsi
    state: r_hand

- type: humanoidBaseSprite
  id: MobShadowRFoot
  baseSprite:
    sprite: _EE/Mobs/Species/Shadow/parts.rsi
    state: r_foot
