- type: htnCompound
  id: GlimmerWispCompound
  branches:
  - tasks:
    - !type:HTNCompoundTask
      task: RangedCombatCompound
  - tasks:
    - !type:HTNCompoundTask
      task: DrainPsionicCompound
  - tasks:
    - !type:HTNCompoundTask
      task: IdleCompound

- type: htnCompound
  id: DrainPsionicCompound
  branches:
  - tasks:
    - !type:HTNPrimitiveTask
      operator: !type:PickDrainTargetOperator
        targetKey: TargetCoordinates
        drainKey: DrainTarget
        rangeKey: IdleRange
    - !type:HTNPrimitiveTask
      operator: !type:MoveToOperator
        pathfindInPlanning: false
    - !type:HTNPrimitiveTask
      operator: !type:DrainOperator
        drainKey: DrainTarget
