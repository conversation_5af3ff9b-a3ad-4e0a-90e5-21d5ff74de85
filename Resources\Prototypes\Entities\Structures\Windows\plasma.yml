- type: entity
  id: PlasmaWindow
  name: "плазмове вікно"
  parent: WindowRCDResistant
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Windows/plasma_window.rsi
  - type: Icon
    sprite: Structures/Windows/plasma_window.rsi
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: RGlass
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 120
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
    - trigger:
        !type:DamageTrigger
        damage: 60
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassPlasma:
            min: 1
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: IconSmooth
    base: pwindow
  - type: Construction
    graph: Window
    node: plasmaWindow
  - type: Appearance
  - type: DamageVisuals
    thresholds: [4, 8, 12]
    damageDivisor: 3.333
    trackAllDamage: true
    damageOverlay:
      sprite: Structures/Windows/cracks.rsi
  - type: StaticPrice
    price: 60
  - type: RadiationBlocker
    resistance: 4
  - type: ExplosionResistance
    damageCoefficient: 0.5

- type: entity
  id: PlasmaWindowDirectional
  parent: WindowDirectionalRCDResistant
  name: "спрямоване плазмове вікно"
  description: "Прозоре і міцне, з фіолетовим відтінком."
  placement:
    mode: SnapgridCenter
    snap:
    - Window
  components:
  - type: Sprite
    sprite: Structures/Windows/directional.rsi
    state: plasma_window
  - type: Icon
    sprite: Structures/Windows/directional.rsi
    state: plasma_window
  - type: Construction
    graph: WindowDirectional
    node: plasmaWindowDirectional
  - type: Appearance
  - type: DamageVisuals
    thresholds: [4, 8, 12]
    damageDivisor: 3.333
    trackAllDamage: true
    damageOverlay:
      sprite: Structures/Windows/cracks_directional.rsi
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassPlasma:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: StaticPrice
    price: 30

- type: entity
  parent: PlasmaWindow
  id: PlasmaWindowDiagonal
  suffix: diagonal
  placement:
    mode: SnapgridCenter
    snap:
    - Window
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Windows/plasma_diagonal.rsi
    state: state0
  - type: IconSmooth
    mode: Diagonal
    key: walls
    base: state
  - type: Icon
    sprite: Structures/Windows/plasma_diagonal.rsi
    state: state0
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PolygonShape
            vertices:
            - "-0.5,-0.5"
            - "0.5,0.5"
            - "0.5,-0.5"
        mask:
        - FullTileMask
        layer:
        - GlassLayer
  - type: Airtight
    noAirWhenFullyAirBlocked: false
    airBlockedDirection:
    - South
    - East
  - type: DamageVisuals
    damageOverlay:
      sprite: Structures/Windows/cracks_diagonal.rsi
  - type: Construction
    graph: WindowDiagonal
    node: plasmaWindowDiagonal
