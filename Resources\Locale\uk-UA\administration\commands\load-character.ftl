loadcharacter-command-description = Застосовує вибраний вами символ до об'єкта
loadcharacter-command-help = Використання: loadcharacter | loadcharacter <entityUid> | loadcharacter <entityUid> <characterName>
loadcharacter-command-mismatch = Виявлено невідповідність виду між персонажем і вибраним об'єктом, це може призвести до несподіваних результатів.
loadcharacter-command-complete = Персонаж завантажено.
loadcharacter-command-fetching = Отримання даних персонажа для {$name}...
loadcharacter-command-fetching-failed = Не вдалося отримати дані персонажа!
loadcharacter-command-failed-fetching = Не вдалося отримати профіль???
loadcharacter-command-hint-select = Виберіть персонажа

spawncharacter-command-description = Створює вибраного/вказаного персонажа
spawncharacter-command-help = Використання: spawncharacter | spawncharacter <ім'я символу>
spawncharacter-command-complete = Персонажа заспавнено.
