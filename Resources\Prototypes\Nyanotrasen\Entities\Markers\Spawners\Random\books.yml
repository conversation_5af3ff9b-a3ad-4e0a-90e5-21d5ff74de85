- type: entity
  parent: MarkerBase
  id: RandomBook
  name: "спавнер випадкової книги"
  placement:
    mode: PlaceFree # want to be able to free place books for stacks, etc.
  components:
  - type: Transform
    anchored: false
  - type: Sprite
    layers:
      - sprite: Objects/Misc/books.rsi
        state: book_icon
  - type: RandomSpawner
    offset: 0
    prototypes:
      # Author Books
      - BookNarsieLegend
      - BookTruth
      - BookWorld
      - BookIanAntarctica
      - BookSlothClownSSS
      - BookSlothClownPranks
      - BookSlothClownMMD
      - BookStruck
      - BookSun
      - BookPossum
      - BookCafe
      - BookFeather
      - BookIanLostWolfPup
      - BookIanRanch
      - BookIanOcean
      - BookIanMountain
      - BookIanCity
      - BookIanArctic
      - BookIanDesert
      - BookNames
      - BookEarth
      - BookTemple
      - BookAurora
      - BookWatched
      - BookMedicalOfficer
      - BookMorgue
      - BookRufus
      - BookMap
      - BookJourney
      - BookInspiration
      - BookJanitorTale
      # Guidebook Books
      - BookSpaceEncyclopedia
      - BookTheBookOfControl
      - BookBartendersManual
      - BookHowToCookForFortySpaceman
      - BookLeafLoversSecret
      - BookEngineersHandbook
      - BookScientistsGuidebook
      - BookSecurity
      - BookHowToKeepStationClean
      - BookHowToRockAndStone
      - BookMedicalReferenceBook
      - BookHowToSurvive
      - BookChemicalCompendium
      # Other Books
      #- BookEscalation
      #- BookEscalationSecurity
      - BookAtmosDistro
      - BookAtmosWaste
      - BookAtmosAirAlarms
      - BookAtmosVentsMore
      - BookRandom # The empty random book because why not
    chance: 0.9
   # rarePrototypes:
   #   - BookChemistryInsane
   #   - BookBotanicalTextbook
   #   - BookGnominomicon
   #   - BookFishing
   #   - BookFishops # DeltaV - fishops book
   #   - BookDetective
   #   - BookSalvageEpistemics1 # Nyanotrasen - Epistemic Lore Book
   # rareChance: 0.25
