- type: entity
  parent: BasePDA
  id: CorpsmanPDA
  name: "кПК брігмедика"
  description: "Червоний і стерильний. Має вбудований аналізатор здоров'я."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Devices/pda.rsi
    layers:
    - map: [ "enum.PdaVisualLayers.Base" ]
    - state: "light_overlay"
      map: [ "enum.PdaVisualLayers.Flashlight" ]
      shader: "unshaded"
      visible: false
    - state: "id_overlay"
      map: [ "enum.PdaVisualLayers.IdLight" ]
      shader: "unshaded"
      visible: false
  - type: CartridgeLoader # DeltaV - Crime Assist + SecWatch
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - CrimeAssistCartridge
      - SecWatchCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge
  - type: Pda
    id: BrigmedicIDCard
    state: pda-corpsman
  - type: PdaBorderColor
    borderColor: "#A32D26"
    accentVColor: "#447987"
  - type: Icon
    sprite: DeltaV/Objects/Devices/pda.rsi
    state: pda-corpsman
  - type: HealthAnalyzer
    scanDelay: 1
    scanningEndSound:
      path: "/Audio/Items/Medical/healthscanner.ogg"

- type: entity
  parent: BasePDA
  id: ChiefJusticePDA
  name: "кПК магістрата"
  description: "Хто б не носив цей КПК, він є законом."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Devices/pda.rsi
    layers:
    - map: [ "enum.PdaVisualLayers.Base" ]
    - state: "light_overlay"
      map: [ "enum.PdaVisualLayers.Flashlight" ]
      shader: "unshaded"
      visible: false
    - state: "id_overlay"
      map: [ "enum.PdaVisualLayers.IdLight" ]
      shader: "unshaded"
      visible: false
  - type: Pda
    id: ChiefJusticeIDCard
    state: pda-chiefjustice
    penSlot:
      startingItem: LuxuryPen
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#470823"
  - type: Icon
    sprite: DeltaV/Objects/Devices/pda.rsi
    state: pda-chiefjustice
  - type: CartridgeLoader
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - CrimeAssistCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge

- type: entity
  parent: BasePDA
  id: ClerkPDA
  name: "кПК клерка"
  description: "На ньому стоїть печатка, яка підтверджує, що він офіційно завірений нотаріусом!"
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Devices/pda.rsi
    layers:
    - map: [ "enum.PdaVisualLayers.Base" ]
    - state: "light_overlay"
      map: [ "enum.PdaVisualLayers.Flashlight" ]
      shader: "unshaded"
      visible: false
    - state: "id_overlay"
      map: [ "enum.PdaVisualLayers.IdLight" ]
      shader: "unshaded"
      visible: false
  - type: Pda
    id: ClerkIDCard
    state: pda-clerk
    penSlot:
      startingItem: LuxuryPen
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#611528"
  - type: Icon
    sprite: DeltaV/Objects/Devices/pda.rsi
    state: pda-clerk
  - type: CartridgeLoader
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - CrimeAssistCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge # DeltaV

- type: entity
  parent: BasePDA
  id: ProsecutorPDA
  name: "кПК прокурора"
  description: "Гострий. Схоже, він може переслідувати вас усіх самостійно."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Devices/pda.rsi
    layers:
    - map: [ "enum.PdaVisualLayers.Base" ]
    - state: "light_overlay"
      map: [ "enum.PdaVisualLayers.Flashlight" ]
      shader: "unshaded"
      visible: false
    - state: "id_overlay"
      map: [ "enum.PdaVisualLayers.IdLight" ]
      shader: "unshaded"
      visible: false
  - type: Pda
    id: ProsecutorIDCard
    state: pda-prosecutor
    penSlot:
      startingItem: LuxuryPen
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#6f6192"
  - type: Icon
    sprite: DeltaV/Objects/Devices/pda.rsi
    state: pda-prosecutor
  - type: CartridgeLoader # DeltaV - Crime Assist
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - CrimeAssistCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge

- type: entity
  parent: SyndiPDA
  id: SyndiListeningPostPDA
  components:
  - type: Pda
    id: SyndicateIDCard
    state: pda-syndi-agent
    penSlot:
      startingItem: CyberPen
      whitelist:
        tags:
        - Write

- type: entity
  parent: BasePDA
  id: AdminAssistantPDA
  name: "адміністративний асистент КПК"
  description: "По краях - каракулі ручкою, і на ній приклеєно кілька липких записок."
  components:
  - type: Sprite
    sprite: Objects/Devices/pda.rsi
    layers:
    - map: [ "enum.PdaVisualLayers.Base" ]
    - state: "light_overlay"
      map: [ "enum.PdaVisualLayers.Flashlight" ]
      shader: "unshaded"
      visible: false
    - state: "id_overlay"
      map: [ "enum.PdaVisualLayers.IdLight" ]
      shader: "unshaded"
      visible: false
  - type: Pda
    id: AdminAssistantIDCard
    state: pda-lawyer
    penSlot:
      startingItem: LuxuryPen
      priority: -1
      whitelist:
        tags:
        - Write
  - type: Icon
    sprite: Objects/Devices/pda.rsi
    state: pda-lawyer
  - type: CartridgeLoader
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge