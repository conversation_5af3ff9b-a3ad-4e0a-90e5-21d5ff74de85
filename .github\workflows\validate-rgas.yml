name: RGA schema validator
on:
  push:
    branches: [ master, staging, trying ]
  merge_group:
  pull_request:
    types: [ opened, reopened, synchronize, ready_for_review ]

jobs:
  yaml-schema-validation:
    name: YAML RGA schema validator
    if: github.actor != 'PJBot' && github.event.pull_request.draft == false && github.actor != 'DeltaV-Bot' && github.actor != 'SimpleStation14'
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3.6.0
    - name: Setup Submodule
      run: git submodule update --init
    - name: Pull engine updates
      uses: space-wizards/submodule-dependency@v0.1.5
    - uses: PaulRitter/yaml-schema-validator@v1
      with:
        schema: RobustToolbox/Schemas/rga.yml
        path_pattern: .*attributions.ya?ml$
        validators_path: RobustToolbox/Schemas/rga_validators.py
        validators_requirements: RobustToolbox/Schemas/rga_requirements.txt
