# Clear to black.
[[layers]]
type = "clear"
color = "#000000"

# Background nebula detail.
[[layers]]
type = "noise"
seed = 7832
innercolor = "#5d1fe1"
outercolor = "#230070"
noise_type = "ridged"
frequency = "4"
octaves = 8
power = "0.25"
threshold = "0.40"

# Mask background nebula.
[[layers]]
type = "noise"
noise_type = "fbm"
innercolor = "#000000"
outercolor = "#000000"
destfactor = "SrcAlpha"
seed = 3551
octaves = 4
power = "0.35"
lacunarity = "1.5"
frequency = "3"
threshold = "0.0"

# Dim background nebula stars.
[[layers]]
type = "points"
seed = 3909
closecolor = "#4B5072"
count = 1500
mask = true
masknoise_type = "fbm"
maskoctaves = 4
maskpersistence = "0.5"
maskpower = "0.35"
masklacunarity = "1.5"
maskfrequency = "3"
maskthreshold = "0.0"
maskseed = 3551

# Bright background nebula stars.
[[layers]]
type = "points"
closecolor = "#7E86BF"
count = 1000
seed = 3472
mask = true
masknoise_type = "fbm"
maskoctaves = 4
maskpersistence = "0.5"
maskpower = "0.35"
masklacunarity = "1.5"
maskfrequency = "3"
maskthreshold = "0.37"
maskseed = 3551

# Bright background nebula stars, dim edge.
[[layers]]
type = "points"
closecolor = "#3D415C"
pointsize = 2
count = 1000
seed = 3472
mask = true
masknoise_type = "fbm"
maskoctaves = 4
maskpersistence = "0.5"
maskpower = "0.35"
masklacunarity = "1.5"
maskfrequency = "3"
maskthreshold = "0.37"
maskseed = 3551

# Couple of odd bright yellow-ish stars.
[[layers]]
type = "points"
closecolor = "#FFD363"
count = 30
seed = 6454

# And their dim edge.
[[layers]]
type = "points"
closecolor = "#43371A"
pointsize = 2
count = 30
seed = 6454
