﻿<ContainerButton
    xmlns="https://spacestation14.io"
    xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls">
    <PanelContainer Name="BackgroundPanel" Access="Public">
        <BoxContainer
            Orientation="Horizontal"
            HorizontalExpand="True"
            SeparationOverride="4">
            <Label Text="{Loc ban-list-header-ids}"
                   SizeFlagsStretchRatio="1"
                   HorizontalExpand="True"/>
            <cc:VSeparator/>
            <Label Text="{Loc ban-list-header-reason}"
                   SizeFlagsStretchRatio="4.5"
                   HorizontalExpand="True"/>
            <cc:VSeparator Name="ReasonSeparator" Access="Public"/>
            <Label Text="{Loc ban-list-header-role}"
                   SizeFlagsStretchRatio="1.5"
                   HorizontalExpand="True"/>
            <cc:VSeparator/>
            <Label Text="{Loc ban-list-header-time}"
                   SizeFlagsStretchRatio="2"
                   HorizontalExpand="True"/>
            <cc:VSeparator/>
            <Label Text="{Loc ban-list-header-expires}"
                   SizeFlagsStretchRatio="4"
                   HorizontalExpand="True"/>
            <cc:VSeparator/>
            <Label Text="{Loc ban-list-header-banning-admin}"
                   SizeFlagsStretchRatio="2"
                   HorizontalExpand="True"/>
        </BoxContainer>
    </PanelContainer>
</ContainerButton>
