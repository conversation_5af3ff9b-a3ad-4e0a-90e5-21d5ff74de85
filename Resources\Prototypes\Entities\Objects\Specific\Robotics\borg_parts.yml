﻿# generic parts
  - type: entity
    id: LeftArmBorg
    parent: BaseBorgLeftArm
    components:
    - type: Sprite
      state: borg_l_arm
    - type: Icon
      state: borg_l_arm
    - type: Tag
      tags:
      - Trash
      - BorgArm
      - BorgGenericLArm

  - type: entity
    id: RightArmBorg
    parent: BaseBorgRightArm
    components:
    - type: Sprite
      state: borg_r_arm
    - type: Icon
      state: borg_r_arm
    - type: Tag
      tags:
      - Trash
      - BorgArm
      - BorgGenericRArm

  - type: entity
    id: LeftLegBorg
    parent: BaseBorgLegLeft
    components:
    - type: Sprite
      state: borg_l_leg
    - type: Icon
      state: borg_l_leg
    - type: Tag
      tags:
      - Trash
      - BorgLeg
      - BorgGenericLLeg

  - type: entity
    id: RightLegBorg
    parent: BaseBorgLegRight
    components:
    - type: Sprite
      state: borg_r_leg
    - type: Icon
      state: borg_r_leg
    - type: Tag
      tags:
      - Trash
      - BorgLeg
      - BorgGenericRLeg

  - type: entity
    id: LightHeadBorg
    parent: BaseLightHeadBorg
    components:
    - type: Sprite
      state: borg_head
    - type: Icon
      state: borg_head
    - type: Tag
      tags:
      - Trash
      - BorgHead
      - BorgGenericHead

  - type: entity
    id: TorsoBorg
    parent: BaseBorgTorso
    components:
    - type: Sprite
      state: borg_chest
    - type: Icon
      state: borg_chest
    - type: Tag
      tags:
      - Trash
      - BorgGenericTorso

  # engineer parts
  - type: entity
    id: LeftArmBorgEngineer
    parent: BaseBorgLeftArm
    name: "інженер-кіборг ліва рука"
    components:
    - type: Sprite
      state: engineer_l_arm
    - type: Icon
      state: engineer_l_arm
    - type: Tag
      tags:
      - Trash
      - BorgArm
      - BorgEngineerLArm

  - type: entity
    id: RightArmBorgEngineer
    parent: BaseBorgRightArm
    name: "права рука інженерного кіборга"
    components:
    - type: Sprite
      state: engineer_r_arm
    - type: Icon
      state: engineer_r_arm
    - type: Tag
      tags:
      - Trash
      - BorgArm
      - BorgEngineerRArm

  - type: entity
    id: LeftLegBorgEngineer
    parent: BaseBorgLegLeft
    name: "інженер-кіборг ліва нога"
    components:
    - type: Sprite
      state: engineer_l_leg
    - type: Icon
      state: engineer_l_leg
    - type: Tag
      tags:
      - Trash
      - BorgLeg
      - BorgEngineerLLeg

  - type: entity
    id: RightLegBorgEngineer
    parent: BaseBorgLegRight
    name: "права нога інженерного кіборга"
    components:
    - type: Sprite
      state: engineer_r_leg
    - type: Icon
      state: engineer_r_leg
    - type: Tag
      tags:
      - Trash
      - BorgLeg
      - BorgEngineerRLeg

  - type: entity
    id: HeadBorgEngineer
    parent: BaseLightHeadBorg
    name: "голова інженерного кіборга"
    components:
    - type: Sprite
      state: engineer_head
    - type: Icon
      state: engineer_head
    - type: Tag
      tags:
      - Trash
      - BorgHead
      - BorgEngineerHead

  - type: entity
    id: TorsoBorgEngineer
    parent: BaseBorgTorso
    name: "торс інженера-кіборга"
    components:
    - type: Sprite
      state: engineer_chest
    - type: Icon
      state: engineer_chest
    - type: Tag
      tags:
      - Trash
      - BorgEngineerTorso

  # janitor parts
  - type: entity
    id: LeftLegBorgJanitor
    parent: BaseBorgLegLeft
    name: "прибиральник-кіборг ліва нога"
    components:
    - type: Sprite
      state: janitor_l_leg
    - type: Icon
      state: janitor_l_leg
    - type: Tag
      tags:
      - Trash
      - BorgLeg
      - BorgJanitorLLeg

  - type: entity
    id: RightLegBorgJanitor
    parent: BaseBorgLegRight
    name: "прибиральник-кіборг права нога"
    components:
    - type: Sprite
      state: janitor_r_leg
    - type: Icon
      state: janitor_r_leg
    - type: Tag
      tags:
      - Trash
      - BorgLeg
      - BorgJanitorRLeg

  - type: entity
    id: HeadBorgJanitor
    parent: BaseLightHeadBorg
    name: "голова прибиральника-кіборга"
    components:
    - type: Sprite
      state: janitor_head
    - type: Icon
      state: janitor_head
    - type: Tag
      tags:
      - Trash
      - BorgHead
      - BorgJanitorHead

  - type: entity
    id: TorsoBorgJanitor
    parent: BaseBorgTorso
    name: "торс прибиральника-кіборга"
    components:
    - type: Sprite
      state: janitor_chest
    - type: Icon
      state: janitor_chest
    - type: Tag
      tags:
      - Trash
      - BorgJanitorTorso

  # medical parts
  - type: entity
    id: LeftArmBorgMedical
    parent: BaseBorgLeftArm
    name: "медичний кіборг ліва рука"
    components:
    - type: Sprite
      state: medical_l_arm
    - type: Icon
      state: medical_l_arm
    - type: Tag
      tags:
      - Trash
      - BorgArm
      - BorgMedicalLArm

  - type: entity
    id: RightArmBorgMedical
    parent: BaseBorgRightArm
    name: "медичний кіборг права рука"
    components:
    - type: Sprite
      state: medical_r_arm
    - type: Icon
      state: medical_r_arm
    - type: Tag
      tags:
      - Trash
      - BorgArm
      - BorgMedicalRArm

  - type: entity
    id: LeftLegBorgMedical
    parent: BaseBorgLegLeft
    name: "медичний кіборг ліва нога"
    components:
    - type: Sprite
      state: medical_l_leg
    - type: Icon
      state: medical_l_leg
    - type: Tag
      tags:
      - Trash
      - BorgLeg
      - BorgMedicalLLeg

  - type: entity
    id: RightLegBorgMedical
    parent: BaseBorgLegRight
    name: "медичний кіборг права нога"
    components:
    - type: Sprite
      state: medical_r_leg
    - type: Icon
      state: medical_r_leg
    - type: Tag
      tags:
      - Trash
      - BorgLeg
      - BorgMedicalRLeg

  - type: entity
    id: HeadBorgMedical
    parent: BaseLightHeadBorg
    name: "голова медичного кіборга"
    components:
    - type: Sprite
      state: medical_head
    - type: Icon
      state: medical_head
    - type: Tag
      tags:
      - Trash
      - BorgHead
      - BorgMedicalHead

  - type: entity
    id: TorsoBorgMedical
    parent: BaseBorgTorso
    name: "торс медичного кіборга"
    components:
    - type: Sprite
      state: medical_chest
    - type: Icon
      state: medical_chest
    - type: Tag
      tags:
      - Trash
      - BorgMedicalTorso

  # mining parts
  - type: entity
    id: LeftArmBorgMining
    parent: BaseBorgLeftArm
    name: "ліва рука шахтарського кіборга"
    components:
    - type: Sprite
      state: mining_l_arm
    - type: Icon
      state: mining_l_arm
    - type: Tag
      tags:
      - Trash
      - BorgArm
      - BorgMiningLArm

  - type: entity
    id: RightArmBorgMining
    parent: BaseBorgRightArm
    name: "права рука шахтарського кіборга"
    components:
    - type: Sprite
      state: mining_r_arm
    - type: Icon
      state: mining_r_arm
    - type: Tag
      tags:
      - Trash
      - BorgArm
      - BorgMiningRArm

  - type: entity
    id: LeftLegBorgMining
    parent: BaseBorgLegLeft
    name: "ліва нога шахтарського кіборга"
    components:
    - type: Sprite
      state: mining_l_leg
    - type: Icon
      state: mining_l_leg
    - type: Tag
      tags:
      - Trash
      - BorgLeg
      - BorgMiningLLeg

  - type: entity
    id: RightLegBorgMining
    parent: BaseBorgLegRight
    name: "права нога шахтарського кіборга"
    components:
    - type: Sprite
      state: mining_r_leg
    - type: Icon
      state: mining_r_leg
    - type: Tag
      tags:
      - Trash
      - BorgLeg
      - BorgMiningRLeg

  - type: entity
    id: HeadBorgMining
    parent: BaseLightHeadBorg
    name: "голова шахтарського кіборга"
    components:
    - type: Sprite
      state: mining_head
    - type: Icon
      state: mining_head
    - type: Tag
      tags:
      - Trash
      - BorgHead
      - BorgMiningHead

  - type: entity
    id: TorsoBorgMining
    parent: BaseBorgTorso
    name: "шахтарський торс кіборга"
    components:
    - type: Sprite
      state: mining_chest
    - type: Icon
      state: mining_chest
    - type: Tag
      tags:
      - Trash
      - BorgMiningTorso

  # service parts
  - type: entity
    id: LeftArmBorgService
    parent: BaseBorgLeftArm
    name: "ліва рука сервісного кіборга"
    components:
    - type: Sprite
      state: service_l_arm
    - type: Icon
      state: service_l_arm
    - type: Tag
      tags:
      - Trash
      - BorgArm
      - BorgServiceLArm

  - type: entity
    id: RightArmBorgService
    parent: BaseBorgRightArm
    name: "права рука сервісного кіборга"
    components:
    - type: Sprite
      state: service_r_arm
    - type: Icon
      state: service_r_arm
    - type: Tag
      tags:
      - Trash
      - BorgArm
      - BorgServiceRArm

  - type: entity
    id: LeftLegBorgService
    parent: BaseBorgLegLeft
    name: "ліва нога сервісного кіборга"
    components:
    - type: Sprite
      state: service_l_leg
    - type: Icon
      state: service_l_leg
    - type: Tag
      tags:
      - Trash
      - BorgLeg
      - BorgServiceLLeg

  - type: entity
    id: RightLegBorgService
    parent: BaseBorgLegRight
    name: "права нога сервісного кіборга"
    components:
    - type: Sprite
      state: service_r_leg
    - type: Icon
      state: service_r_leg
    - type: Tag
      tags:
      - Trash
      - BorgLeg
      - BorgServiceRLeg

  - type: entity
    id: HeadBorgService
    parent: BaseLightHeadBorg
    name: "голова сервісного кіборга"
    components:
    - type: Sprite
      state: service_head
    - type: Icon
      state: service_head
    - type: Tag
      tags:
      - Trash
      - BorgHead
      - BorgServiceHead

  - type: entity
    id: TorsoBorgService
    parent: BaseBorgTorso
    name: "торс сервісного кіборга"
    components:
    - type: Sprite
      state: service_chest
    - type: Icon
      state: service_chest
    - type: Tag
      tags:
      - Trash
      - BorgServiceTorso
