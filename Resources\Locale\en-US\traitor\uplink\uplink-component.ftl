uplink-bound-user-interface-insufficient-funds-popup = Insufficient funds!

uplink-bound-user-interface-tc-balance-popup = TC Balance: [color={$weightedColor}]{$balance}[/color]

uplink-user-interface-title = Uplink

uplink-user-interface-search-label = Search

# Withdraw UI

uplink-user-interface-withdraw-button = Withdraw TC
uplink-user-interface-withdraw-title = Withdraw TC
uplink-user-interface-withdraw-withdraw-button = Withdraw
uplink-user-interface-withdraw-cancel-button = Cancel
