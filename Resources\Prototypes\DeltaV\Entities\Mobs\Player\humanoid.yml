- type: entity # Delta-V part of PirateRadioSpawn
  id: RandomHumanoidSpawnerListener
  name: "Підслуховувач Синдикату"
  components:
    - type: Sprite
      sprite: Mobs/Species/Human/parts.rsi
      state: full
    - type: RandomHumanoidSpawner
      settings: SyndicateListener

- type: randomHumanoidSettings
  id: SyndicateListener
  speciesBlacklist:
    - Shadowkin
    - Monkey # Shitmed Change
    - Kobold # Shitmed Change
  components:
    - type: Loadout
      prototypes: [SyndicateListenerGear]

# Mobsters

- type: entity
  id: RandomHumanoidSpawnerMobster
  name: "мафіозі"
  components:
    - type: Sprite
      netsync: false
      sprite: DeltaV/Markers/jobs.rsi
      state: mobster
    - type: RandomMetadata
      nameSegments:
      - fake_human_first
      - names_last
    - type: RandomHumanoidSpawner
      settings: Mobster

- type: randomHumanoidSettings
  id: Mobster
  randomizeName: false
  speciesBlacklist:
    - Shadowkin
    - Monkey # Shitmed Change
    - Kobold # Shitmed Change
  components:
    - type: GhostRole
      name: Mobster
      description: Break kneecaps, spray lead, keep your mouth shut.
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ MobsterGear ]
    - type: RandomMetadata
      nameSegments:
      - fake_human_first
      - names_last

- type: entity
  id: RandomHumanoidSpawnerMobsterAlt
  name: "мафіозі"
  suffix: alt
  components:
    - type: Sprite
      netsync: false
      sprite: DeltaV/Markers/jobs.rsi
      state: mobster
    - type: RandomMetadata
      nameSegments:
      - fake_human_first
      - names_last
    - type: RandomHumanoidSpawner
      settings: MobsterAlt

- type: randomHumanoidSettings
  id: MobsterAlt
  randomizeName: false
  speciesBlacklist:
    - Shadowkin
    - Monkey # Shitmed Change
    - Kobold # Shitmed Change
  components:
    - type: GhostRole
      name: Mobster
      description: Break kneecaps, spray lead, keep your mouth shut.
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ MobsterGearAlt ]
    - type: RandomMetadata
      nameSegments:
      - fake_human_first
      - names_last
