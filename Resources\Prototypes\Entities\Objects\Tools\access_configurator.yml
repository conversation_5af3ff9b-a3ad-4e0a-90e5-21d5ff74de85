- type: entity
  parent: BaseItem
  id: AccessConfigurator
  name: "конфігуратор доступу"
  description: "Використовується для зміни вимог до рівня доступу для шлюзів та інших пристроїв, що замикаються."
  components:
    - type: EmitSoundOnLand
      sound:
        path: /Audio/Items/multitool_drop.ogg
    - type: Sprite
      sprite: Objects/Tools/access_configurator.rsi
      state: icon
    - type: Item
      size: Small
    - type: Clothing
      sprite: Objects/Tools/access_configurator.rsi
      quickEquip: false
      slots:
        - Belt
    - type: AccessOverrider
      accessLevels:
      - Armory
      - Atmospherics
      - BasicSilicon
      - Borg
      #- Brig #Delta V: Removed Brig Access
      - Boxer #Delta V: Add Boxer Access
      - Detective
      - Captain
      - Cargo
      - Chapel
      - Chemistry
      - ChiefEngineer
      - ChiefMedicalOfficer
      - Clown #Delta V: Add Clown Access
      - Corpsman # DeltaV: Add Corpsman access
      - Command
      - Engineering
      - External
      - HeadOfPersonnel
      - HeadOfSecurity
      - Hydroponics
      - Janitor
      - Kitchen
      - Lawyer
      - Library #Delta V: Add Library Access
      - Maintenance
      - Medical
      - Mime #Delta V: Add Mime Access
      - Musician #Delta V: Add Musician Access
      - Paramedic # Delta V - adds Paramedic access
      - Psychologist #Delta V: Add Psychologist Access
      - Quartermaster
      - Reporter #Delta V: Add Reporter Access
      - Research
      - Robotics #PIRATE
      - ResearchDirector
      - Salvage
      - Security
      - Service
      - StationAi
      - Theatre
      - Zookeeper #Delta V: Add Zookeeper Access
      - ChiefJustice  #Delta V: Add Chief Justice Access
      - Prosecutor  #Delta V: Add Prosecutor Access
      - Justice  #Delta V: Add Justice Access
      - Clerk  #Delta V: Add Clerk Access
      privilegedIdSlot:
        name: id-card-console-privileged-id
        ejectSound: /Audio/Machines/id_swipe.ogg
        insertSound: /Audio/Weapons/Guns/MagIn/batrifle_magin.ogg
        ejectOnBreak: true
        swap: false
        whitelist:
          components:
          - IdCard
      denialSound:
        path: /Audio/Machines/custom_deny.ogg
      doAfter: 0.5
    - type: UserInterface
      interfaces:
        enum.AccessOverriderUiKey.Key:
          type: AccessOverriderBoundUserInterface
    - type: ActivatableUI
      key: enum.AccessOverriderUiKey.Key
      requiresComplex: true
      requireActiveHand: false
      singleUser: true
    - type: ItemSlots
    - type: ContainerContainer
      containers:
        AccessOverrider-privilegedId: !type:ContainerSlot
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.25,-0.25,0.25,0.25"
          density: 12
          mask:
          - ItemMask
          restitution: 0.3
          friction: 0.2
