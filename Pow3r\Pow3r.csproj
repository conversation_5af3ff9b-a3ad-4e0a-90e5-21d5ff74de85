﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>$(TargetFramework)</TargetFramework>
        <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="ImGui.NET" />
      <PackageReference Include="OpenTK" />
      <PackageReference Include="Veldrid" />
      <PackageReference Include="Veldrid.SPIRV" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Content.Server\Content.Server.csproj" />
      <ProjectReference Include="..\RobustToolbox\Robust.Shared.Maths\Robust.Shared.Maths.csproj" />
      <ProjectReference Include="..\RobustToolbox\Robust.UnitTesting\Robust.UnitTesting.csproj" />
    </ItemGroup>

  <Import Project="..\RobustToolbox\MSBuild\Robust.Properties.targets" />
</Project>
