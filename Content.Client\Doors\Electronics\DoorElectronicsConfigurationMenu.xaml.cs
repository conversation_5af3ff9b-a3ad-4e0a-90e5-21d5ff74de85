using System.Linq;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;
using Content.Client.Access.UI;
using Content.Client.Doors.Electronics;
using Content.Shared.Access;
using Content.Shared.Doors.Electronics;
using FancyWindow = Content.Client.UserInterface.Controls.FancyWindow;

namespace Content.Client.Doors.Electronics;

[GenerateTypedNameReferences]
public sealed partial class DoorElectronicsConfigurationMenu : FancyWindow
{
    private readonly AccessLevelControl _buttonsList = new();

    public event Action<List<ProtoId<AccessLevelPrototype>>>? OnAccessChanged;

    public DoorElectronicsConfigurationMenu()
    {
        RobustXamlLoader.Load(this);
        AccessLevelControlContainer.AddChild(_buttonsList);
    }

    public void Reset(IPrototypeManager protoManager, List<ProtoId<AccessLevelPrototype>> accessLevels)
    {
        _buttonsList.Populate(accessLevels, protoManager);

        foreach (var button in _buttonsList.ButtonsList.Values)
        {
            button.OnPressed += _ => OnAccessChanged?.Invoke(_buttonsList.ButtonsList.Where(x => x.Value.Pressed).Select(x => x.Key).ToList());
        }
    }

    public void UpdateState(DoorElectronicsConfigurationState state)
    {
        _buttonsList.UpdateState(state.AccessList);
    }
}
