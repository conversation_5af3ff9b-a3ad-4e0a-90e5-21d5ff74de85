﻿- type: entity
  id: UnfinishedMachineFrame
  name: "рама машини"
  description: "Машина на стадії будівництва. Потрібно більше деталей."
  suffix: Unfinished
  placement:
    mode: SnapgridCenter
  components:
    - type: Transform
      anchored: true
    - type: Physics
      bodyType: Static
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.25,-0.5,0.25,0.5"
          density: 190
          mask:
          - MachineMask
          layer:
          - MachineLayer
    - type: Clickable
    - type: InteractionOutline
    - type: Anchorable
    - type: Rotatable
    - type: Pullable
    - type: Construction
      containers:
        - machine_board
        - machine_parts
      graph: Machine
      node: missingWires
    - type: ContainerContainer
      containers:
        machine_board: !type:Container
        machine_parts: !type:Container
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: Metallic
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors:
        - !type:PlaySoundBehavior
          sound:
            collection: MetalBreak
        - !type:ChangeConstructionNodeBehavior
          node: start
        - !type:DoActsBehavior
          acts: ["Destruction"]
    - type: Sprite
      sprite: Structures/Machines/parts.rsi
      state: box_0
      snapCardinals: true

- type: entity
  id: MachineFrame
  name: "рама машини"
  suffix: Ready
  placement:
    mode: SnapgridCenter
  components:
    - type: Transform
      anchored: true
    - type: Physics
      bodyType: Static
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.25,-0.5,0.25,0.5"
          density: 190
          mask:
          - MachineMask
          layer:
          - MachineLayer
    - type: Clickable
    - type: InteractionOutline
    - type: Anchorable
    - type: Rotatable
    - type: Pullable
    - type: Construction
      graph: Machine
      node: machineFrame
      defaultTarget: machine
      containers:
        - machine_board
        - machine_parts
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: Metallic
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors:
        - !type:PlaySoundBehavior
          sound:
            collection: MetalBreak
        - !type:ChangeConstructionNodeBehavior
          node: missingWires
        - !type:DoActsBehavior
          acts: ["Destruction"]
    - type: MachineFrame
    - type: ContainerContainer
      containers:
        machine_board: !type:Container
        machine_parts: !type:Container
    - type: Sprite
      sprite: Structures/Machines/parts.rsi
      state: box_1
      snapCardinals: true
    - type: Appearance
    - type: ItemMapper
      mapLayers:
        box_2:
          minCount: 1
          whitelist:
            components:
            - MachineBoard

- type: entity
  id: MachineFrameDestroyed
  parent: BaseStructureDynamic
  name: "зруйнована рама машини"
  placement:
    mode: SnapgridCenter
  components:
    - type: Transform
      anchored: true
      noRot: true
    - type: Physics
      bodyType: Static
    - type: Construction
      graph: Machine
      node: destroyedMachineFrame
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: Metallic
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors:
        - !type:SpawnEntitiesBehavior
          spawn:
            SheetSteel1:
              min: 1
              max: 3
        - !type:DoActsBehavior
          acts: ["Destruction"]
    - type: Sprite
      sprite: Structures/Machines/parts.rsi
      state: destroyed
