- type: entity
  id: LightPostSmall
  name: "пост-світло"
  description: "Постійно ввімкнене світло."
  suffix: Always Powered
  parent: BaseStructure
  components:
  - type: Sprite
    sprite: Structures/Lighting/LightPosts/small_light_post.rsi
    snapCardinals: true
    drawdepth: Objects
    noRot: true
    layers:
    - map: ["enum.PoweredLightLayers.Base"]
      state: base
    - map: ["enum.PoweredLightLayers.Glow"]
      state: glow
      shader: unshaded
    state: base
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "0.17,0.25,-0.17,-0.30"
        density: 190
        mask:
        - MachineMask
        layer:
        - MidImpassable
        - LowImpassable
  - type: PointLight
    radius: 10
    energy: 2.5
    softness: 0.9
    color: "#EEEEFF"
  - type: Anchorable
  - type: InteractionOutline
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
        - !type:DoActs<PERSON>ehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 2
            max: 4
      - !type:EmptyAllContainersBehaviour
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak

- type: entity
  id: PoweredLightPostSmallEmpty
  name: "пост-світло"
  description: "Невеликий світловий стовпчик."
  suffix: Empty
  parent: LightPostSmall
  components:
  - type: Sprite
    sprite: Structures/Lighting/LightPosts/small_light_post.rsi
    state: empty
  - type: PointLight
    enabled: false
  - type: PoweredLight
    bulb: Tube
    damage:
      types:
        Heat: 2
  - type: ContainerContainer
    containers:
      light_bulb: !type:ContainerSlot
  - type: ApcPowerReceiver
  - type: ExtensionCableReceiver
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: SmartLight
  - type: WirelessNetworkConnection
    range: 200
  - type: DeviceLinkSink
    ports:
      - On
      - Off
      - Toggle
  - type: Construction
    graph: LightFixture
    node: groundLight
  - type: Appearance
  - type: PoweredLightVisuals
    blinkingSound:
      path: "/Audio/Machines/light_tube_on.ogg"
    spriteStateMap:
      empty: empty
      off: base
      on: base
      broken: broken
      burned: burned

- type: entity
  id: PoweredLightPostSmall
  name: "пост-світло"
  description: "Світильник. Споживає енергію і виробляє світло, якщо оснащений світловою трубкою."
  suffix: ""
  parent: PoweredLightPostSmallEmpty
  components:
  - type: Sprite
    state: base
  - type: PointLight
    enabled: true
  - type: PoweredLight
    hasLampOnSpawn: LightTube
    damage:
      types:
        Heat: 2
  - type: StaticPrice
    price: 25
  - type: AmbientOnPowered
  - type: AmbientSound
    volume: -15
    range: 3
    sound:
      path: /Audio/Ambience/Objects/light_hum.ogg

- type: entity
  id: PoweredLEDLightPostSmall
  name: "пост-світло"
  description: "Світильник. Споживає енергію і виробляє світло, якщо оснащений світловою трубкою."
  suffix: LED
  parent: PoweredLightPostSmallEmpty
  components:
  - type: Sprite
    state: base
  - type: PointLight
    enabled: true
    radius: 15
    energy: 1
    softness: 0.9
    color: "#EEEEFF"
  - type: PoweredLight
    hasLampOnSpawn: LedLightTube
    damage:
      types:
        Heat: 1
  - type: StaticPrice
    price: 25
  - type: AmbientOnPowered
  - type: AmbientSound
    volume: -15
    range: 3
    sound:
      path: /Audio/Ambience/Objects/light_hum.ogg
