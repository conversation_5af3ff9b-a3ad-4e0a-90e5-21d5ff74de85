﻿- type: constructionGraph
  id: Hamtr
  start: start
  graph:
  - node: start
    edges:
    - to: hamtr
      steps:
      - tool: Anchoring
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 1

      - tool: Screwing
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 2

      - material: Cable
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 3

      - tool: Cutting
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 4

      - tag: HamtrCentralControlModule
        name: HAMTR central control module
        icon:
          sprite: "Objects/Misc/module.rsi"
          state: "mainboard"
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 5

      - tool: Screwing
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 6

      - tag: HamtrPeripheralsControlModule
        name: HAMTR peripherals control module
        icon:
          sprite: "Objects/Misc/module.rsi"
          state: id_mod
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 7

      - tool: Screwing
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 12

#i omitted the steps involving inserting machine parts because
#currently mechs don't support upgrading. add them back in once that's squared away.

      - component: PowerCell
        name: power cell
        store: battery-container
        icon:
          sprite: Objects/Power/power_cells.rsi
          state: small
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 13

      - tool: Screwing
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 14

      - material: Steel
        amount: 5
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 15

      - tool: Anchoring
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 16

      - tool: Welding
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 17

      - material: MetalRod
        amount: 10
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 18

      - tool: Welding
        doAfter: 1

  - node: hamtr
    actions:
    - !type:BuildMech
      mechPrototype: MechHamtr
