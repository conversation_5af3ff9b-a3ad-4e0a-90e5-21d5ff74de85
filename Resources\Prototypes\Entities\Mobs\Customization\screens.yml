- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenStatic
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_static: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_static


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenBlue
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_blue: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_blue


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenBreakout
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_breakout: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_breakout


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenEight
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_eight: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_eight


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenGoggles
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_goggles: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_goggles


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenExclaim
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_exclaim: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_exclaim


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenHeart
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_heart: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_heart


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenMonoeye
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_monoeye: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_monoeye


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenNature
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_nature: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_nature


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenOrange
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_orange: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_orange


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenPink
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_pink: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_pink


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenQuestion
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_question: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_question


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenShower
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_shower: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_shower


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenYellow
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_yellow: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_yellow


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenScroll
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_scroll: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_scroll


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenConsole
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_console: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_console


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenRgb
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_rgb: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_rgb


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenGlider
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_glider: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_glider


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenRainbowhoriz
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_rainbowhoriz: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_rainbowhoriz


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenBsod
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_bsod: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_bsod


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenRedtext
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_redtext: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_redtext


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenSinewave
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_sinewave: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_sinewave


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenSquarewave
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_squarewave: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_squarewave


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenEcgwave
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_ecgwave: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_ecgwave


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenEyes
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_eyes: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_eyes


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenEyestall
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_eyestall: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_eyestall


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenEyesangry
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_eyesangry: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_eyesangry


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenLoading
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_loading: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_loading


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenWindowsxp
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_windowsxp: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_windowsxp


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenTetris
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_tetris: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_tetris


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenTv
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_tv: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_tv


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenTextdrop
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_textdrop: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_textdrop


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenStars
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_stars: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_stars


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenRainbowdiag
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_rainbowdiag: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_rainbowdiag


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenBlank
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_blank: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_blank


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenSmile
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_smile: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_smile

- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenFrown
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_frown: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_frown


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenRing
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_ring: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_ring


- type: marking
  speciesRestriction: [ IPC ]
  id: ScreenL
  bodyPart: Face
  markingCategory: Face
  shaders:
    ipc_screen_l: unshaded
  sprites:
    - sprite: Mobs/Customization/ipc_screens.rsi
      state: ipc_screen_l

