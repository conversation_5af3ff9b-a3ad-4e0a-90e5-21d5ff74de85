- type: entity
  id: SpiderWeb
  name: "павутиння"
  description: "Він в'язкий і липкий."
  placement:
    mode: SnapgridCenter
    snap:
      - Wall
  components:
    - type: MeleeSound
      soundGroups:
        Brute:
          path:
            "/Audio/Weapons/slash.ogg"
    - type: Sprite
      sprite: Objects/Misc/spiderweb.rsi
      layers:
        - state: spider_web_1
          map: ["spiderWebLayer"]
      drawdepth: WallMountedItems
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.SpiderWebVisuals.Variant:
          spiderWebLayer:
            1:  {state: spider_web_1}
            2:  {state: spider_web_2}
    - type: Clickable
    - type: Transform
      anchored: true
    - type: Physics
    - type: Fixtures
      fixtures:
        fix1:
          hard: false
          density: 7
          shape:
            !type:PhysShapeAabb
            bounds: "-0.5,-0.5,0.5,0.5"
          layer:
          - MidImpassable
    - type: Damageable
      damageModifierSet: Wood
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 10
        behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
        - !type:SpawnEntitiesBehavior
          spawn:
            MaterialWebSilk:
              min: 0
              max: 1
    - type: Temperature
      heatDamage:
        types:
          Heat: 5
      coldDamage: {}
      coldDamageThreshold: 0
    - type: Flammable
      fireSpread: true
      damage:
        types:
          Heat: 5
    - type: Reactive
      groups:
        Flammable: [Touch]
        Extinguish: [Touch]
    - type: SpiderWebObject
    - type: SpeedModifierContacts
      walkSpeedModifier: 0.5
      sprintSpeedModifier: 0.5
      ignoreWhitelist:
        components:
            - IgnoreSpiderWeb
    - type: Tag
      tags:
        - ArachneWeb

- type: entity
  id: SpiderWebClown
  name: "клоунська павутина"
  description: "Він в'язкий і слизький."
  placement:
    mode: SnapgridCenter
    snap:
      - Wall
  components:
    - type: MeleeSound
      soundGroups:
        Brute:
          path:
            "/Audio/Weapons/slash.ogg"
    - type: Sprite
      sprite: Objects/Misc/spiderweb.rsi
      layers:
        - state: spider_web_clown_1
          map: ["spiderWebLayer"]
      drawdepth: WallMountedItems
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.SpiderWebVisuals.Variant:
          spiderWebLayer:
            1:  {state: spider_web_clown_1}
            2:  {state: spider_web_clown_2}
    - type: Clickable
    - type: Transform
      anchored: true
    - type: Slippery
      paralyzeTime: 2
      launchForwardsMultiplier: 1.5
    - type: StepTrigger
      triggerGroups:
        types:
        - SlipTile
      intersectRatio: 0.2
    - type: Physics
    - type: Fixtures
      fixtures:
        slips:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.4,-0.3,0.4,0.3"
          hard: false
          layer:
          - SlipLayer
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.4,-0.3,0.4,0.3"
          density: 1000
          mask:
          - ItemMask
    - type: Damageable
      damageModifierSet: Wood
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 10
        behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - type: Temperature
      heatDamage:
        types:
          Heat: 5
      coldDamage: {}
      coldDamageThreshold: 0
    - type: Flammable
      fireSpread: true
      damage:
        types:
          Heat: 5
    - type: Reactive
      groups:
        Flammable: [Touch]
        Extinguish: [Touch]
    - type: SpiderWebObject
    - type: FlavorProfile
      flavors:
        - sweet
    - type: Food
      delay: 2
    - type: SolutionContainerManager
      solutions:
        food:
          reagents:
          - ReagentId: Sugar
            Quantity: 2
