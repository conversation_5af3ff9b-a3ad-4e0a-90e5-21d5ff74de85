- type: entity
  id: BasaltOne
  name: "базальт"
  description: "Рок"
  placement:
    mode: SnapgridCenter
  components:
    - type: Clickable
    - type: Sprite
      sprite: /Textures/Tiles/Planet/basalt.rsi
      layers:
        - state: basalt1
          shader: unshaded
      drawdepth: LowFloors
    - type: SyncSprite
    - type: RequiresTile
    - type: Transform
      anchored: true
    - type: Tag
      tags:
        - HideContextMenu

- type: entity
  id: BasaltTwo
  parent: BasaltOne
  placement:
    mode: SnapgridCenter
  components:
    - type: Sprite
      layers:
        - state: basalt2
          shader: unshaded

- type: entity
  id: BasaltThree
  parent: BasaltOne
  placement:
    mode: SnapgridCenter
  components:
    - type: Sprite
      layers:
        - state: basalt3
          shader: unshaded

- type: entity
  id: BasaltFour
  parent: BasaltOne
  placement:
    mode: SnapgridCenter
  components:
    - type: Sprite
      layers:
        - state: basalt4
          shader: unshaded

- type: entity
  id: BasaltFive
  parent: BasaltOne
  placement:
    mode: SnapgridCenter
  components:
    - type: Sprite
      layers:
        - state: basalt5
          shader: unshaded

- type: entity
  id: BasaltRandom
  parent: BasaltOne
  suffix: Random
  components:
    - type: RandomSprite
      available:
        - 0:
            basalt1: ""
            basalt2: ""
            basalt3: ""
            basalt4: ""
            basalt5: ""
