- type: entity
  abstract: true
  parent: BaseItem
  id: IngotBase
  description: "Злиток важкого металу з логотипом Нанотрейзен."
  components:
  - type: Sprite
    sprite: Objects/Materials/ingots.rsi
  - type: Item
    sprite: Objects/Materials/ingots.rsi
    size: Normal
  - type: StaticPrice
    price: 0
  - type: Tag
    tags:
    - Ingot
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 500
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]

- type: entity
  parent: IngotBase
  id: IngotGold
  name: "золотий злиток"
  suffix: Full
  components:
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      Gold: 100
  - type: Stack
    stackType: Gold
    baseLayer: base
    layerStates:
    - gold
    - gold_2
    - gold_3
  - type: Sprite
    state: gold_3
    layers:
    - state: gold_3
      map: ["base"]
  - type: Item
    heldPrefix: gold
  - type: Appearance
  - type: Extractable
    grindableSolutionName: gold
  - type: SolutionContainerManager
    solutions:
      gold:
        reagents:
        - ReagentId: Gold
          Quantity: 10

- type: entity
  parent: IngotGold
  id: IngotGold1
  name: "золотий злиток"
  suffix: Single
  components:
  - type: Sprite
    state: gold
  - type: Stack
    count: 1

- type: entity
  parent: IngotBase
  id: IngotSilver
  name: "срібний злиток"
  suffix: Full
  components:
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      Silver: 100
  - type: Stack
    stackType: Silver
    baseLayer: base
    layerStates:
    - silver
    - silver_2
    - silver_3
  - type: Sprite
    state: silver_3
    layers:
    - state: silver_3
      map: ["base"]
  - type: Item
    heldPrefix: silver
  - type: Appearance
  - type: Extractable
    grindableSolutionName: gold
  - type: SolutionContainerManager
    solutions:
      gold:
        reagents:
        - ReagentId: Silver
          Quantity: 10

- type: entity
  parent: IngotSilver
  id: IngotSilver1
  name: "срібний злиток"
  suffix: Single
  components:
  - type: Sprite
    state: silver
  - type: Stack
    count: 1

- type: entity
  parent: IngotBase
  id: IngotTungsten
  name: "твердосплавний пруток"
  suffix: Full
  components:
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      Tungsten: 100
  - type: Stack
    stackType: Tungsten
    baseLayer: base
    layerStates:
    - tungsten_1
    - tungsten_2
    - tungsten_3
  - type: Sprite
    state: tungsten_3
    layers:
    - state: tungsten_3
      map: ["base"]
  - type: Item
    heldPrefix: tungsten
  - type: Appearance

- type: entity
  parent: IngotTungsten
  id: IngotTungsten1
  name: "твердосплавний пруток"
  suffix: Single
  components:
  - type: Sprite
    state: tungsten_1
  - type: Stack
    count: 1
