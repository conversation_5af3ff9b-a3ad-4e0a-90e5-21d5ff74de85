ammonia-smell = Something smells pungent!

## Perishable

perishable-1 = [color=green]{ CAPITALIZE(POSS-ADJ($target)) } corpse still looks fresh.[/color]
perishable-2 = [color=orangered]{ CAPITALIZE(POSS-ADJ($target)) } corpse looks somewhat fresh.[/color]
perishable-3 = [color=red]{ CAPITALIZE(POSS-ADJ($target)) } corpse doesn't look very fresh.[/color]

perishable-1-nonmob = [color=green]{ CAPITALIZE(SUBJECT($target)) } still looks fresh.[/color]
perishable-2-nonmob = [color=orangered]{ CAPITALIZE(SUBJECT($target)) } looks somewhat fresh.[/color]
perishable-3-nonmob = [color=red]{ CAPITALIZE(SUBJECT($target)) } doesn't look very fresh.[/color]

## Rotting

rotting-rotting = [color=orange]{ CAPITALIZE(POSS-ADJ($target)) } corpse is rotting![/color]
rotting-bloated = [color=orangered]{ CAPITALIZE(POSS-ADJ($target)) } corpse is bloated![/color]
rotting-extremely-bloated = [color=red]{ CAPITALIZE(POSS-ADJ($target)) } corpse is extremely bloated![/color]

rotting-rotting-nonmob = [color=orange]{ CAPITALIZE(SUBJECT($target)) } is rotting![/color]
rotting-bloated-nonmob = [color=orangered]{ CAPITALIZE(SUBJECT($target)) } is bloated![/color]
rotting-extremely-bloated-nonmob = [color=red]{ CAPITALIZE(SUBJECT($target)) } is extremely bloated![/color]
