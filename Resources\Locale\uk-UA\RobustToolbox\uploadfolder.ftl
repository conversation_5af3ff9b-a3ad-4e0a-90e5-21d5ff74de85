uploadfolder-command-description = Рекурсивно завантажує папку з вашої теки UserData до бази даних вмісту сервера.
uploadfolder-command-help = uploadfolder [папку, яку ви хочете завантажити в userdata/UploadFolder]
uploadfolder-command-wrong-args = Неправильна кількість аргументів!
uploadfolder-command-folder-not-found = Folder {$folder} not found!
uploadfolder-command-resource-upload-disabled = Завантаження мережевих ресурсів наразі вимкнено. перевірте CVars сервера.
uploadfolder-command-file-too-big = Розмір файлу {$filename} перевищує поточний ліміт! Він має бути меншим за {$sizeLimit} МБ. пропуск.
uploadfolder-command-success = Завантажено {$fileCount} файлів
