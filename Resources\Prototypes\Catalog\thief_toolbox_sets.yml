- type: thiefBackpackSet
  id: ChameleonSet
  name: thief-backpack-category-chameleon-name
  description: thief-backpack-category-chameleon-description
  sprite:
    sprite: /Textures/Clothing/OuterClothing/Misc/black_hoodie.rsi
    state: icon
  content:
  - ClothingUniformJumpsuitChameleon
  - ClothingOuterChameleon
  - ClothingNeckChameleon
  - ClothingMaskGasChameleon
  - ClothingHeadHatChameleon
  - ClothingEyesChameleon
  - ClothingHeadsetChameleon
  - ClothingShoesChameleon
  - ChameleonProjector

- type: thiefBackpackSet
  id: ToolsSet
  name: thief-backpack-category-tools-name
  description: thief-backpack-category-tools-description
  sprite:
    sprite: Objects/Tools/jaws_of_life.rsi
    state: jaws_pry
  content:
  - WelderIndustrialAdvanced
  - ClothingEyesGlassesMeson
  - ClothingHandsGlovesColorYellow
  - JawsOfLife
  - Multitool
  - C4
  - C4
  - ClothingMaskClown

- type: thiefBackpackSet
  id: ChemistrySet
  name: thief-backpack-category-chemistry-name
  description: thief-backpack-category-chemistry-description
  sprite:
    sprite: Objects/Specific/Medical/implanter.rsi
    state: implanter0
  content:
  - StorageImplanter
  - DnaScramblerImplanter
  - EphedrineChemistryBottle
  - SoapOmega
  - Syringe
  - DrinkVodkaBottleFull

- type: thiefBackpackSet
  id: SyndieSet
  name: thief-backpack-category-syndie-name
  description: thief-backpack-category-syndie-description
  sprite:
    sprite: Objects/Specific/Syndicate/telecrystal.rsi
    state: telecrystal
  content:
  - AgentIDCard
  - Emag
  - Doorjack
  - SyndicatePersonalAI
  - ClothingHeadHatSyndieMAA
  - CigPackSyndicate
  - Telecrystal10 #The thief cannot use them, but it may induce communication with traitors

- type: thiefBackpackSet
  id: SleeperSet
  name: thief-backpack-category-sleeper-name
  description: thief-backpack-category-sleeper-description
  sprite:
    sprite: Objects/Tanks/anesthetic.rsi
    state: icon
  content:
  - ClothingHeadPyjamaSyndicateBlack
  - ClothingUniformJumpsuitPyjamaSyndicateBlack
  - NocturineChemistryBottle
  - NocturineChemistryBottle
  - NocturineChemistryBottle
  - HypopenBox
  - NitrousOxideTankFilled
  - BedsheetSyndie

- type: thiefBackpackSet
  id: CommunicatorSet
  name: thief-backpack-category-communicator-name
  description: thief-backpack-category-communicator-description
  sprite:
    sprite: Objects/Tools/spy_device.rsi
    state: icon
  requirements:
    - !type:CharacterSpeciesRequirement
      inverted: true
      species:
      - Plasmaman
  content:
  - EncryptionKeyStationMaster
  - CyberPen
  - SpyCrewMonitor
  - BriefcaseThiefBribingBundleFilled
  - ClothingMaskGasVoiceChameleon
  #- todo Chameleon Stamp

- type: thiefBackpackSet
  id: CommunicatorSetPlasmaman
  name: thief-backpack-category-communicator-plasmaman-name
  description: thief-backpack-category-communicator-plasmaman-description
  sprite:
    sprite: Objects/Tools/spy_device.rsi
    state: icon
  requirements:
    - !type:CharacterSpeciesRequirement
      species:
      - Plasmaman
  content:
  - EncryptionKeyStationMaster
  - CyberPen
  - SpyCrewMonitor
  - BriefcaseThiefBribingBundlePlasmamanFilled
  - ClothingMaskGasVoiceChameleon

- type: thiefBackpackSet
  id: SmugglerSet
  name: thief-backpack-category-smuggler-name
  description: thief-backpack-category-smuggler-description
  sprite:
    sprite: Clothing/Neck/Cloaks/void.rsi
    state: icon
  content:
  - InvisibleCrate
  - ClothingNeckCloakVoid
  - FultonBeacon
  - Fulton
  - SmokeGrenade
  - SmokeGrenade
  - SmokeGrenade
