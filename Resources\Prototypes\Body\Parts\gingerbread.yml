- type: entity
  id: PartGingerbread
  parent: [BaseItem, BasePart]
  name: "пряникова частина тіла"
  abstract: true
  components:
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Nutriment
        Quantity: 3
      - ReagentId: Sugar
        Quantity: 10

- type: entity
  id: TorsoGingerbread
  name: "пряниковий торс"
  parent: [PartGingerbread, BaseTorso]
  components:
  - type: Sprite
    sprite: Mobs/Species/Gingerbread/parts.rsi
    state: "torso_m"
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Nutriment
        Quantity: 10
      - ReagentId: Sugar
        Quantity: 20

- type: entity
  id: HeadGingerbread
  name: "пряникова голова"
  parent: [PartGingerbread, BaseHead]
  components:
  - type: Sprite
    sprite: Mobs/Species/Gingerbread/parts.rsi
    state: "head_m"
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Nutriment
        Quantity: 5
      - ReagentId: Sugar
        Quantity: 10

- type: entity
  id: LeftArmGingerbread
  name: "ліва пряникова рука"
  parent: [PartGingerbread, BaseLeftArm]
  components:
  - type: Sprite
    sprite: Mobs/Species/Gingerbread/parts.rsi
    state: "l_arm"

- type: entity
  id: RightArmGingerbread
  name: "права пряникова рука"
  parent: [PartGingerbread, BaseRightArm]
  components:
  - type: Sprite
    sprite: Mobs/Species/Gingerbread/parts.rsi
    state: "r_arm"

- type: entity
  id: LeftHandGingerbread
  name: "ліва пряникова долоня"
  parent: [PartGingerbread, BaseLeftHand]
  components:
  - type: Sprite
    sprite: Mobs/Species/Gingerbread/parts.rsi
    state: "l_hand"

- type: entity
  id: RightHandGingerbread
  name: "права пряникова долоня"
  parent: [PartGingerbread, BaseRightHand]
  components:
  - type: Sprite
    sprite: Mobs/Species/Gingerbread/parts.rsi
    state: "r_hand"

- type: entity
  id: LeftLegGingerbread
  name: "ліва пряникова нога"
  parent: [PartGingerbread, BaseLeftLeg]
  components:
  - type: Sprite
    sprite: Mobs/Species/Gingerbread/parts.rsi
    state: "l_leg"

- type: entity
  id: RightLegGingerbread
  name: "права пряникова нога"
  parent: [PartGingerbread, BaseRightLeg]
  components:
  - type: Sprite
    sprite: Mobs/Species/Gingerbread/parts.rsi
    state: "r_leg"

- type: entity
  id: LeftFootGingerbread
  name: "ліва пряникова стопа"
  parent: [PartGingerbread, BaseLeftFoot]
  components:
  - type: Sprite
    sprite: Mobs/Species/Gingerbread/parts.rsi
    state: "l_foot"

- type: entity
  id: RightFootGingerbread
  name: "права пряникова стопа"
  parent: [PartGingerbread, BaseRightFoot]
  components:
  - type: Sprite
    sprite: Mobs/Species/Gingerbread/parts.rsi
    state: "r_foot"
