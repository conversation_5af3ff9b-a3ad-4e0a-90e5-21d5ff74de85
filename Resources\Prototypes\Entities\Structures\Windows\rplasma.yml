- type: entity
  id: ReinforcedPlasmaWindow
  name: "посилене плазмове вікно"
  parent: WindowRCDResistant
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Windows/reinforced_plasma_window.rsi
  - type: Icon
    sprite: Structures/Windows/reinforced_plasma_window.rsi
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: RGlass
  - type: RadiationBlocker
    resistance: 8
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors: #excess damage, don't spawn entities.
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassPlasma:
            min: 1
            max: 2
          PartRodMetal:
            min: 1
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: IconSmooth
    base: rpwindow
  - type: Construction
    graph: Window
    node: reinforcedPlasmaWindow
  - type: Appearance
  - type: DamageVisuals
    thresholds: [4, 8, 12]
    damageDivisor: 6
    trackAllDamage: true
    damageOverlay:
      sprite: Structures/Windows/cracks.rsi
  - type: StaticPrice
    price: 132
  - type: ExplosionResistance
    damageCoefficient: 0.2

- type: entity
  id: PlasmaReinforcedWindowDirectional
  parent: WindowDirectionalRCDResistant
  name: "спрямоване армоване плазмове вікно"
  description: "Вогнестійкий і навіть міцніший, з фіолетовим відтінком."
  placement:
    mode: SnapgridCenter
    snap:
    - Window
  components:
  - type: Sprite
    sprite: Structures/Windows/directional.rsi
    state: plasma_reinforced_window
  - type: Icon
    sprite: Structures/Windows/directional.rsi
    state: plasma_reinforced_window
  - type: Construction
    graph: WindowDirectional
    node: plasmaReinforcedWindowDirectional
  - type: Appearance
  - type: DamageVisuals
    thresholds: [4, 8, 12]
    damageDivisor: 36
    trackAllDamage: true
    damageOverlay:
      sprite: Structures/Windows/cracks_directional.rsi
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 1000
      behaviors: #excess damage, don't spawn entities.
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 600
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassPlasma:
            min: 1
            max: 1
          PartRodMetal:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: StaticPrice
    price: 66

- type: entity
  parent: ReinforcedPlasmaWindow
  id: ReinforcedPlasmaWindowDiagonal
  suffix: diagonal
  placement:
    mode: SnapgridCenter
    snap:
    - Window
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Windows/reinforced_plasma_diagonal.rsi
    state: state0
  - type: IconSmooth
    mode: Diagonal
    key: walls
    base: state
  - type: Icon
    sprite: Structures/Windows/reinforced_plasma_diagonal.rsi
    state: state0
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PolygonShape
            vertices:
            - "-0.5,-0.5"
            - "0.5,0.5"
            - "0.5,-0.5"
        mask:
        - FullTileMask
        layer:
        - GlassLayer
  - type: Airtight
    noAirWhenFullyAirBlocked: false
    airBlockedDirection:
    - South
    - East
  - type: DamageVisuals
    damageOverlay:
      sprite: Structures/Windows/cracks_diagonal.rsi
  - type: Construction
    graph: WindowDiagonal
    node: reinforcedPlasmaWindowDiagonal
