- type: entity
  name: "криміналістичний сканер"
  parent: BaseItem
  id: ForensicScanner
  description: "Портативний пристрій, який може сканувати об'єкти на наявність відбитків пальців і волокон."
  components:
  - type: Sprite
    sprite: Objects/Devices/forensic_scanner.rsi
    state: forensicnew
  - type: Item
    size: Small
  - type: Clothing
    sprite: Objects/Devices/forensic_scanner.rsi
    quickEquip: false
    slots:
    - Belt
  - type: ActivatableUI
    key: enum.ForensicScannerUiKey.Key
    inHandsOnly: true
    requireActiveHand: false
  - type: UserInterface
    interfaces:
      enum.ForensicScannerUiKey.Key:
        type: ForensicScannerBoundUserInterface
  - type: ForensicScanner
  - type: GuideHelp
    guides:
      - Forensics
  - type: StealTarget
    stealGroup: ForensicScanner

- type: entity
  name: "звіт криміналістичного сканера"
  parent: Paper
  id: ForensicReportPaper
  description: "Непрямі докази, в кращому випадку"
  components:
  - type: Sprite
    sprite: Objects/Misc/bureaucracy.rsi
    layers:
    - state: paper_receipt_horizontal
    - state: paper_receipt_horizontal_words
      map: ["enum.PaperVisualLayers.Writing"]
      visible: false
    - state: paper_stamp-generic
      map: ["enum.PaperVisualLayers.Stamp"]
      visible: false
  - type: PaperVisuals
    backgroundImagePath: "/Textures/Interface/Paper/paper_background_perforated.svg.96dpi.png"
    headerImagePath: "/Textures/Interface/Paper/paper_heading_forensic_scanner.svg.96dpi.png"
    headerMargin: 0.0, 0.0, 0.0, 4.0
    backgroundImageTile: true
    backgroundPatchMargin: 6.0, 0.0, 6.0, 0.0
    contentMargin: 12.0, 0.0, 6.0, 0.0
    maxWritableArea: 368.0, 256.0
  - type: GuideHelp
    guides:
    - Forensics
