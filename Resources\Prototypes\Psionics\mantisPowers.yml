- type: psionicPower
  id: MantisSummonBlackBlade
  name: "Викликати Чорний Клинок"
  powerCategories:
    - Mentalic
    - Dangerous
  initializeFunctions:
    - !type:AddPsionicActions
      actions:
        - ActionMantisSummonBlackBlade
  removalFunctions:
    - !type:RemovePsionicActions

- type: entity
  id: ActionMantisSummonBlackBlade
  name: "Викликати Чорний Клинок"
  description: "Прикличте свідоцтво ваших гріхів."
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/Entities/Items/Weapons/cult_blade.rsi
    state: icon
  - type: InstantAction
    useDelay: 20
    checkCanInteract: false
    itemIconStyle: BigAction
    icon:
      sprite: WhiteDream/BloodCult/Entities/Items/Weapons/cult_blade.rsi
      state: icon
    event: !type:SummonEquipmentEvent
      speech: "thrusts their open hand to the side. A black blade forms from nothingness into their waiting hand. Whispering demonic voices echo around the sword..."
      invokeChatType: Emote
      prototypes:
        hand1: MantisBlackBlade
      force: false
  - type: BaseCultSpell

- type: entity
  name: "чорний меч"
  parent: BaseItem
  id: MantisBlackBlade
  description: "Величезний чорний меч, покритий дивними рунами. Він невпинно шепоче своєму власнику."
  components:
  - type: Sharp
  - type: Sprite
    sprite: WhiteDream/BloodCult/Entities/Items/Weapons/cult_blade.rsi
    state: icon
  - type: MeleeWeapon
    wideAnimationRotation: -135
    attackRate: 1.3333
    range: 1.65
    damage:
      types:
        Slash: 10
        Holy: 15
    heavyDamageBaseModifier: 1.2
    heavyStaminaCost: 10
    maxTargets: 3
    angle: 90
    soundHit:
      path: /Audio/Weapons/bladeslice.ogg
  - type: AntiPsionicWeapon
    punish: false
    modifiers:
      coefficients:
        Slash: 1.5
        Holy: 2
  - type: Item
    size: Normal
  - type: Clothing
    slots:
    - back
  - type: DisarmMalus
  - type: CultItem
    allowUseToEveryone: true
  - type: PointLight
    color: "#793a80"
    radius: 1.5
    energy: 12.5
  - type: DeleteOnDrop
  - type: AmbientSound
    range: 5
    volume: -5
    sound:
      path: /Audio/Ambience/anomaly_scary.ogg
