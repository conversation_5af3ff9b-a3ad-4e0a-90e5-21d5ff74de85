- type: entity
  abstract: true
  parent: BaseItem
  id: SheetOtherBase
  description: "Лист матеріалу, який часто використовується на станції в різних сферах."
  components:
  - type: Sprite
    sprite: Objects/Materials/Sheets/other.rsi
  - type: Item
    sprite: Objects/Materials/Sheets/other.rsi
    size: Normal
  - type: Tag
    tags:
    - Sheet
    - NoPaint
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: SolutionContainerManager
    solutions:
      paper:
        canReact: false

- type: entity
  parent: SheetOtherBase
  id: SheetPaper
  name: "папір"
  suffix: Full
  components:
  - type: Stack
    stackType: Paper
    baseLayer: base
    layerStates:
    - paper
    - paper_2
    - paper_3
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      Paper: 100
  - type: Sprite
    state: paper_3
    layers:
    - state: paper_3
      map: ["base"]
  - type: Item
    heldPrefix: paper
  - type: Appearance
  - type: Tag
    tags:
    - Sheet
    - NoPaint
    - MaterialPaper
  - type: Extractable
    grindableSolutionName: paper
  - type: SolutionContainerManager
    solutions:
      paper:
        reagents:
        - ReagentId: Cellulose
          Quantity: 3


- type: entity
  parent: SheetPaper
  id: SheetPaper1
  name: "папір"
  suffix: Single
  components:
  - type: Sprite
    state: paper
  - type: Stack
    count: 1

- type: entity
  parent: SheetOtherBase
  id: SheetPlasma
  name: "плазма"
  suffix: Full
  components:
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      Plasma: 100
  - type: Stack
    stackType: Plasma
    baseLayer: base
    layerStates:
    - plasma
    - plasma_2
    - plasma_3
  - type: Sprite
    state: plasma_3
    layers:
    - state: plasma_3
      map: ["base"]
  - type: Item
    heldPrefix: plasma
  - type: Appearance
  - type: Extractable
    grindableSolutionName: plasma
  - type: SolutionContainerManager
    solutions:
      plasma:
        reagents:
        - ReagentId: Plasma
          Quantity: 10
        canReact: false
  - type: Tag
    tags:
    - Sheet
    - NoPaint
  - type: UserInterface
    interfaces:
      enum.RadialSelectorUiKey.Key:
        type: RadialSelectorMenuBUI
  - type: ActivatableUI
    key: enum.RadialSelectorUiKey.Key
    inHandsOnly: true
    requireActiveHand: false
  - type: ShortConstruction
    entries:
    - prototype: SheetPGlass
    - prototype: SheetRPGlass
    - prototype: SheetRPGlass1

- type: entity
  parent: SheetPlasma
  id: SheetPlasma1
  name: "плазма"
  suffix: Single
  components:
  - type: Sprite
    state: plasma
  - type: Stack
    count: 1

- type: entity
  parent: SheetPlasma
  id: SheetPlasma10
  name: "плазма"
  suffix: 10
  components:
  - type: Sprite
    state: plasma
  - type: Stack
    count: 10

- type: entity
  parent: SheetOtherBase
  id: SheetPlastic
  name: "пластик"
  suffix: Full
  components:
  - type: Tag
    tags:
    - Plastic
    - Sheet
    - NoPaint
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      Plastic: 100
  - type: Stack
    stackType: Plastic
    baseLayer: base
    layerStates:
    - plastic
    - plastic_2
    - plastic_3
  - type: Sprite
    state: plastic_3
    layers:
    - state: plastic_3
      map: ["base"]
  - type: Item
    heldPrefix: plastic
  - type: Appearance
  - type: Extractable
    grindableSolutionName: plastic
  - type: SolutionContainerManager
    solutions:
      plastic:
        reagents:
        - ReagentId: Oil
          Quantity: 5
        - ReagentId: Phosphorus
          Quantity: 5
        canReact: false
  - type: UserInterface
    interfaces:
      enum.RadialSelectorUiKey.Key:
        type: RadialSelectorMenuBUI
  - type: ActivatableUI
    key: enum.RadialSelectorUiKey.Key
    inHandsOnly: true
    requireActiveHand: false
  - type: ShortConstruction
    entries:
    - prototype: CratePlastic

- type: entity
  parent: SheetPlastic
  id: SheetPlastic10
  name: "пластик"
  suffix: 10
  components:
  - type: Sprite
    state: plastic
  - type: Stack
    count: 10

- type: entity
  parent: SheetPlastic
  id: SheetPlastic1
  name: "пластик"
  suffix: Single
  components:
  - type: Sprite
    state: plastic
  - type: Stack
    count: 1

- type: entity
  parent: [SheetOtherBase, FoodBase]
  id: SheetUranium
  name: "уран"
  suffix: Full
  components:
  - type: Material
  - type: Food
    transferAmount: 10
  - type: BadFood
  - type: PhysicalComposition
    materialComposition:
      Uranium: 100
  - type: Stack
    stackType: Uranium
    baseLayer: base
    layerStates:
    - uranium
    - uranium_2
    - uranium_3
  - type: Sprite
    state: uranium_3
    layers:
    - state: uranium_3
      map: ["base"]
  - type: Appearance
  - type: Item
    heldPrefix: uranium
  - type: Extractable
    grindableSolutionName: food
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Uranium
          Quantity: 8
        - ReagentId: Radium
          Quantity: 2
        canReact: false
  - type: UserInterface
    interfaces:
      enum.RadialSelectorUiKey.Key:
        type: RadialSelectorMenuBUI
  - type: ActivatableUI
    key: enum.RadialSelectorUiKey.Key
    inHandsOnly: true
    requireActiveHand: false
  - type: ShortConstruction
    entries:
    - prototype: SheetUGlass
    - prototype: SheetRUGlass
    - prototype: SheetRUGlass1

- type: entity
  parent: SheetUranium
  id: SheetUranium1
  name: "уран"
  suffix: Single
  components:
  - type: Stack
    count: 1

- type: entity
  parent: SheetOtherBase
  id: MaterialSheetMeat
  name: "м'ясний лист"
  suffix: Full
  components:
    - type: Sprite
      sprite: Objects/Materials/Sheets/meaterial.rsi
      layers:
      - state: meat_3
        map: [ "base" ]
    - type: Tag
      tags:
      - Sheet
    - type: Material
    - type: PhysicalComposition
      materialComposition:
        Meaterial: 100
    - type: Stack
      stackType: MeatSheets
      baseLayer: base
      layerStates:
        - meat
        - meat_2
        - meat_3
    - type: Item
      sprite: Objects/Materials/Sheets/meaterial.rsi
      heldPrefix: meat
    - type: Extractable
      grindableSolutionName: meatsheet
    - type: SolutionContainerManager
      solutions:
        meatsheet:
          reagents:
          - ReagentId: Protein
            Quantity: 7
          - ReagentId: Fat
            Quantity: 3
          canReact: false

- type: entity
  parent: MaterialSheetMeat
  id: MaterialSheetMeat1
  suffix: Single
  components:
    - type: Sprite
      state: meat
    - type: Stack
      count: 1
