- type: entity
  name: "ручний монітор екіпажу"
  suffix: DO NOT MAP
  parent: BaseHandheldComputer
  # CMO-only bud, don't add more.
  id: HandheldCrewMonitor
  description: "Ручний монітор екіпажу, що відображає стан датчиків скафандра."
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/handheldcrewmonitor.rsi
    state: scanner
  - type: ActivatableUI
    key: enum.CrewMonitoringUIKey.Key
  - type: UserInterface
    interfaces:
      enum.CrewMonitoringUIKey.Key:
        type: CrewMonitoringBoundUserInterface
  - type: CrewMonitoringConsole
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: CrewMonitor
  - type: WirelessNetworkConnection
    range: 500
  - type: StationLimitedNetwork
  - type: ReverseEngineering # Nyano
    difficulty: 2
    recipes:
      - HandheldCrewMonitor
  - type: StaticPrice
    price: 500
  - type: Tag
    tags:
    - HighRiskItem
  - type: StealTarget
    stealGroup: HandheldCrewMonitor
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 5

- type: entity
  id: HandheldCrewMonitorEmpty
  parent: HandheldCrewMonitor
  suffix: Empty
  components:
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default

- type: entity
  id: SpyCrewMonitor
  name: "шпигунський монітор"
  description: "Шпигунський пристрій, здатний підключатися до серверів моніторингу екіпажу."
  parent: HandheldCrewMonitor
  components:
  - type: Sprite
    sprite: Objects/Tools/spy_device.rsi
    state: icon
  - type: Item
    sprite: Objects/Tools/spy_device.rsi
  - type: PowerCellDraw
    useRate: 10
  - type: StaticPrice
    price: 750

- type: entity
  id: SpyCrewMonitorEmpty
  parent: SpyCrewMonitor
  suffix: Empty
  components:
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default

- type: entity
  id: SyndiCrewMonitor
  name: "спостерігач за екіпажем синдикату"
  description: "Синдикована версія монітора екіпажу, перехоплює інформацію з сервера."
  parent: HandheldCrewMonitor
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/syndihandheldcrewmonitor.rsi
    state: syndiscanner
  - type: Item
    sprite: Objects/Specific/Medical/syndihandheldcrewmonitor.rsi
  - type: PowerCellDraw
    useRate: 40

- type: entity
  id: SyndiCrewMonitorEmpty
  parent: SyndiCrewMonitor
  suffix: Empty
  components:
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
