- type: entity
  name: "електрична дубинка"
  parent: BaseItem
  id: Stunbaton
  description: "Електродубинка для виведення з ладу людей. Активно завдавати нею шкоди вважається поганим тоном."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Melee/stunbaton.rsi
    layers:
    - state: stunbaton_off
      map: [ "enum.ToggleVisuals.Layer" ]
  - type: Tag
    tags:
    - Stunbaton
  - type: Stunbaton
    energyPerUse: 50
  - type: ItemToggle
    predictable: false
    soundActivate:
      collection: sparks
      params:
        variation: 0.250
    soundDeactivate:
      collection: sparks
      params:
        variation: 0.250
    soundFailToActivate:
      path: /Audio/Machines/button.ogg
      params:
        variation: 0.250
  - type: ItemToggleMeleeWeapon
    activatedDamage:
      types:
        Blunt: 0
  - type: ItemToggleDamageOtherOnHit
  - type: MeleeWeapon
    wideAnimationRotation: -135
    damage:
      types:
        Blunt: 7
    bluntStaminaDamageFactor: 3.5
    heavyRateModifier: 1.1
    heavyDamageBaseModifier: 1.5
    heavyStaminaCost: 0.5
    animation: WeaponArcSlash
  - type: DamageOtherOnHit
  - type: StaminaDamageOnHit # Stunmeta
    damage: 15
    overtime: 40
    sound: /Audio/Weapons/egloves.ogg
  - type: StaminaDamageOnCollide
    damage: 15
    overtime: 40
    sound: /Audio/Weapons/egloves.ogg
  - type: Battery
    maxCharge: 1000
    startingCharge: 1000
  - type: Item
    heldPrefix: off
    size: Normal
  - type: Clothing
    sprite: Objects/Weapons/Melee/stunbaton.rsi
    quickEquip: false
    slots:
    - Belt
  - type: DisarmMalus
    malus: 0.225
  - type: Appearance
  - type: GenericVisualizer
    visuals:
     enum.ToggleVisuals.Toggled:
        enum.ToggleVisuals.Layer:
          True: {state: stunbaton_on}
          False: {state: stunbaton_off}
  - type: StaticPrice
    price: 80
  - type: Riggable
  - type: SolutionContainerManager
    solutions:
      battery:
        maxVol: 5
  - type: InjectableSolution
    solution: battery
  - type: DrawableSolution
    solution: battery
  - type: Explosive
    explosionType: Default
    intensitySlope: 1.5
    maxIntensity: 200
  - type: PhysicalComposition
    materialComposition:
      Steel: 100
      Plastic: 100
  - type: GuideHelp
    guides:
    - Security
  - type: MartialArtBlocked # Goobstation - Martial Arts
    form: CorporateJudo # Goobstation - Martial Arts

- type: entity
  name: "кийок"
  parent: BaseItem
  id: Truncheon
  description: "Жорсткий кийок зі сталевим наконечником, призначений для завдання шкоди."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Melee/truncheon.rsi
    state: icon
  - type: MeleeWeapon
    wideAnimationRotation: -135
    attackRate: 1.2
    damage:
      types:
        Blunt: 12
    soundHit:
      collection: MetalThud
    bluntStaminaDamageFactor: 3
    heavyRateModifier: 1.5
    heavyDamageBaseModifier: 1.2
    heavyStaminaCost: 2.5
  - type: DamageOtherOnHit
    staminaCost: 9
  - type: Item
    size: Normal
  - type: Tag
    tags:
    - Truncheon
  - type: Clothing
    sprite: Objects/Weapons/Melee/truncheon.rsi
    quickEquip: false
    slots:
    - Belt
  - type: DisarmMalus
    malus: 0.225
  - type: Appearance
  - type: StaticPrice
    price: 80
  - type: GuideHelp
    guides:
    - Security

- type: entity
  name: "осліплювач"
  parent: BaseItem
  id: Flash
  description: "Надзвичайно яскрава лампочка-спалах з кнопкою, яка викликає у жертви оглушення і втрату зору на мить. Після перегорання марна."
  components:
    - type: Sprite
      sprite: Objects/Weapons/Melee/flash.rsi
      layers:
      - state: flash
        map: [ "enum.FlashVisuals.BaseLayer" ]
      - state: flashing
        map: [ "enum.FlashVisuals.LightLayer" ]
        visible: false
        shader: unshaded
    - type: Flash
    - type: LimitedCharges
      maxCharges: 5
      charges: 5
    - type: MeleeWeapon
      wideAnimationRotation: 180
      damage:
        types:
          Blunt: 0 # melee weapon to allow flashing individual targets
      angle: 10
    - type: Item
      size: Small
      sprite: Objects/Weapons/Melee/flash.rsi
#    - type: DynamicPrice
#      price: 40
    - type: ReverseEngineering # Nyano
      recipes:
        - Flash
    - type: StaticPrice
      price: 40
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.FlashVisuals.Burnt:
          enum.FlashVisuals.BaseLayer:
            True: {state: burnt}
        enum.FlashVisuals.Flashing:
          enum.FlashVisuals.LightLayer:
            True: {visible: true}
            False: {visible: false}
    - type: GuideHelp
      guides:
      - Security

- type: entity
  name: "осліплювач"
  parent: Flash
  suffix: 2 charges
  id: SciFlash
  components:
    - type: LimitedCharges
      maxCharges: 2
      charges: 2

- type: entity
  name: "портативний осліплювач"
  parent: BaseStructure
  id: PortableFlasher
  description: "Надзвичайно яскрава лампочка-спалах із безконтактним тригером, корисна для забезпечення безпеки в зоні."
  components:
    - type: EmitSoundOnTrigger
      sound:
        path: /Audio/Weapons/flash.ogg
    - type: FlashOnTrigger
      range: 3
    - type: TriggerOnProximity
      enabled: true
      cooldown: 5
      shape:
        !type:PhysShapeCircle
          radius: 2
      repeating: true
    - type: Anchorable
    - type: Sprite
      sprite: Objects/Weapons/pflash.rsi
      layers:
        - state: "off"
          map: ["enum.ProximityTriggerVisualLayers.Base"]
    - type: InteractionOutline
    - type: Physics
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.15,-0.3,0.15,0.3"
          mask:
          - MachineMask
          layer:
          - MachineLayer
          density: 380
    - type: Appearance
    - type: AnimationPlayer
    - type: PointLight
      energy: 2.0
      radius: 0
      softness: 0
      enabled: true
    - type: GuideHelp
      guides:
      - Security
