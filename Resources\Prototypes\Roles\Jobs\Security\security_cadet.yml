- type: job
  id: SecurityCadet
  name: job-name-cadet
  description: job-description-cadet
  playTimeTracker: JobSecurityCadet
  requirements:
    - !type:CharacterTraitRequirement
      inverted: true
      traits:
        - Foreigner
        - ForeignerLight
        - Muted
        - Blindness
        - Pacifist
        - BrittleBoneDisease
    - !type:CharacterOverallTimeRequirement
      min: 14400 # DeltaV - 4 hours
    - !type:CharacterEmployerRequirement
      employers:
      - ZavodskoiInterstellar
      - PMCG
      - NanoTrasen
      - EastOrionCompany
  startingGear: SecurityCadetGear
  icon: "JobIconSecurityCadet"
  supervisors: job-supervisors-security
  canBeAntag: false
  access:
  - Security
  #- Brig # Delta V: Removed
  - Maintenance
  - Service
  - External
  - Cryogenics
  special:
  - !type:AddImplantSpecial
    implants: [ MindShieldImplant ]
  - !type:AddComponentSpecial # goobstation
    components:
      - type: SecurityStaff
  afterLoadoutSpecial:
  - !type:ModifyEnvirosuitSpecial
    charges: 6
  - !type:ModifyEnvirohelmSpecial
    powerCell: PowerCellHigh

- type: startingGear
  id: SecurityCadetGear
  subGear:
  - SecurityOfficerPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitColorRed
    back: ClothingBackpackSecurity
    shoes: ClothingShoesBootsCombatFilled
    outerClothing: ClothingOuterArmorDuraVest # DeltaV - ClothingOuterArmorBasic replaced in favour of stab vest. Sucks to suck, cadets
    id: SecurityCadetPDA
    ears: ClothingHeadsetAltSecurityRegular # Goobstation
    belt: ClothingBeltSecurityFilled
    pocket2: BookSecurity
  innerClothingSkirt: ClothingUniformJumpskirtColorRed
  satchel: ClothingBackpackSatchelSecurity
  duffelbag: ClothingBackpackDuffelSecurity
