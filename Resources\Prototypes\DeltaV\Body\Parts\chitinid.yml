# TODO: Add descriptions (many)
# TODO BODY: Part damage
- type: entity
  id: PartChitinidBase
  parent: [BaseItem, BasePart]
  name: "Хітинідна частина тіла"
  abstract: true
  components:
  - type: Sprite
    sprite: DeltaV/Mobs/Species/Chitinid/parts.rsi
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 3
      - ReagentId: Blood
        Quantity: 10

- type: entity
  id: TorsoChitinid
  name: "хітинідний торс"
  parent: [PartChitinidBase, BaseTorso]
  components:
  - type: Sprite
    state: "torso_m"
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 10
      - ReagentId: Blood
        Quantity: 20


- type: entity
  id: HeadChitinid
  name: "хітинідна головка"
  parent: [PartChitinidBase, BaseHead]
  components:
  - type: Sprite
    state: "head_m"
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 5
      - ReagentId: Blood
        Quantity: 10

- type: entity
  id: LeftArmChitinid
  name: "ліва хітинідна рука"
  parent: [PartChitinidBase, BaseLeftArm]
  components:
  - type: Sprite
    state: "l_arm"

- type: entity
  id: RightArmChitinid
  name: "права хітинідна рука"
  parent: [PartChitinidBase, BaseRightArm]
  components:
  - type: Sprite
    state: "r_arm"

- type: entity
  id: LeftHandChitinid
  name: "ліва хітинідна рука"
  parent: [PartChitinidBase, BaseLeftHand]
  components:
  - type: Sprite
    state: "l_hand"

- type: entity
  id: RightHandChitinid
  name: "права хітинідна рука"
  parent: [PartChitinidBase, BaseRightHand]
  components:
  - type: Sprite
    state: "r_hand"

- type: entity
  id: LeftLegChitinid
  name: "ліва хітинідна нога"
  parent: [PartChitinidBase, BaseLeftLeg]
  components:
  - type: Sprite
    state: "l_leg"

- type: entity
  id: RightLegChitinid
  name: "права хітинідна нога"
  parent: [PartChitinidBase, BaseRightLeg]
  components:
  - type: Sprite
    state: "r_leg"

- type: entity
  id: LeftFootChitinid
  name: "ліва хітинідна стопа"
  parent: [PartChitinidBase, BaseLeftFoot]
  components:
  - type: Sprite
    state: "l_foot"

- type: entity
  id: RightFootChitinid
  name: "права хітинідна стопа"
  parent: [PartChitinidBase, BaseRightFoot]
  components:
  - type: Sprite
    state: "r_foot"
