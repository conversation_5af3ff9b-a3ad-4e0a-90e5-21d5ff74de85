mopping-system-target-container-empty = { CAPITALIZE(THE($target)) } is empty!
mopping-system-target-container-empty-water = { CAPITALIZE(THE($target)) } has no water!
mopping-system-puddle-space = { CAPITALIZE(THE($used)) } is full of water
mopping-system-puddle-evaporate = {  CAPITALIZE(THE($target)) } is evaporating
mopping-system-no-water = { CAPITALIZE(THE($used)) } has no water!

mopping-system-full = { CAPITALIZE(THE($used)) } is full!
mopping-system-empty = { CAPITALIZE(THE($used)) } is empty!
