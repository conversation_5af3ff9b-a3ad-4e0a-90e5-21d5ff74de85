- type: artifactTrigger
  id: TriggerInteraction
  targetDepth: 0
  triggerHint: artifact-trigger-hint-physical
  components:
  - type: ArtifactInteractionTrigger

- type: artifactTrigger
  id: TriggerTimer
  targetDepth: 0
  components:
  - type: ArtifactTimerTrigger

- type: artifactTrigger
  id: TriggerExamine
  targetDepth: 0
  triggerHint: artifact-trigger-hint-examine
  components:
  - type: ArtifactExamineTrigger

- type: artifactTrigger
  id: TriggerAnchor
  targetDepth: 0
  triggerHint: artifact-trigger-hint-tool
  blacklist:
    components:
    - Item
  components:
  - type: ArtifactAnchorTrigger

- type: artifactTrigger
  id: TriggerElectricity
  targetDepth: 0
  triggerHint: artifact-trigger-hint-electricity
  blacklist:
    components:
    - Item
  components:
  - type: ArtifactElectricityTrigger
  - type: PowerConsumer
    voltage: Medium
    drawRate: 500
  - type: Electrified
    requirePower: true
    noWindowInTile: true
    highVoltageNode: high
    mediumVoltageNode: medium
    lowVoltageNode: low
  - type: NodeContainer
    nodes:
      medium:
        !type:CableDeviceNode
        nodeGroupID: MVPower
        # sadly, HVPower and Apc cables doesn't work right now

- type: artifactTrigger
  id: TriggerMusic
  targetDepth: 1
  triggerHint: artifact-trigger-hint-music
  components:
  - type: ArtifactMusicTrigger

- type: artifactTrigger
  id: TriggerBruteDamage
  targetDepth: 1
  triggerHint: artifact-trigger-hint-physical
  components:
  - type: ArtifactDamageTrigger
    damageTypes:
    - Blunt
    - Slash
    - Piercing
    damageThreshold: 50

- type: artifactTrigger
  id: TriggerItemLanded
  targetDepth: 1
  triggerHint: artifact-trigger-hint-land
  whitelist:
    components:
    - Item
  components:
  - type: ArtifactLandTrigger

- type: artifactTrigger
  id: TriggerHeat
  targetDepth: 1
  triggerHint: artifact-trigger-hint-heat
  components:
  - type: ArtifactHeatTrigger

- type: artifactTrigger
  id: TriggerWater
  targetDepth: 1
  triggerHint: artifact-trigger-hint-water
  components:
  - type: Reactive
    groups:
      Acidic: [ Touch ]
    reactions:
    - reagents: [ Water ]
      methods: [ Touch ]
      effects:
      - !type:ActivateArtifact

- type: artifactTrigger
  id: TriggerBlood
  targetDepth: 1
  triggerHint: artifact-trigger-hint-blood
  components:
  - type: Reactive
    groups:
      Acidic: [ Touch ]
    reactions:
    - reagents: [ Blood, CopperBlood, InsectBlood, Slime, AmmoniaBlood, ZombieBlood, BlackBlood ]
      methods: [ Touch ]
      effects:
      - !type:ActivateArtifact

- type: artifactTrigger
  id: TriggerMedical
  targetDepth: 2
  triggerHint: artifact-trigger-hint-medical
  components:
  - type: Reactive
    groups:
      Acidic: [ Touch ]
    reactions:
    - reagents: [ Dylovene, Diphenhydramine, Arithrazine, Bicaridine, Dermaline, Dexalin, DexalinPlus, Tricordrazine, Leporazine, Bruizine, Lacerinol, Puncturase, Pyrazine, Insuzine, Kelotane, Hyronalin, Inaprovaline, Epinephrine, Probital, Tirimol, Syriniver, Ebifin, ProcenylLazide, Multiver, Monover, Calomel, AmmoniatedMercury, SilverSulfadiazine, StypticPowder, SalicylicAcid, Oxandrolone, PentenicAcid, Atropine ] # Goobstation - Probital and above
      methods: [ Touch ]
      effects:
      - !type:ActivateArtifact

- type: artifactTrigger
  id: TriggerGas
  targetDepth: 2
  triggerHint: artifact-trigger-hint-regular-gases
  components:
  - type: ArtifactGasTrigger
    possibleGas:
    - Oxygen
    - Nitrogen
    - CarbonDioxide

- type: artifactTrigger
  id: TriggerDeath
  targetDepth: 2
  triggerHint: artifact-trigger-hint-death
  components:
  - type: ArtifactDeathTrigger

- type: artifactTrigger
  id: TriggerMagnet
  targetDepth: 2
  triggerHint: artifact-trigger-hint-magnet
  components:
  - type: ArtifactMagnetTrigger

- type: artifactTrigger
  id: TriggerLowPressure
  targetDepth: 2
  triggerHint: artifact-trigger-hint-pressure
  components:
  - type: ArtifactPressureTrigger
    minPressureThreshold: 50

- type: artifactTrigger
  id: TriggerHighDamage
  targetDepth: 3
  triggerHint: artifact-trigger-hint-physical
  components:
  - type: ArtifactDamageTrigger
    damageThreshold: 500 #make it go boom or w/e

- type: artifactTrigger
  id: TriggerRadiation
  targetDepth: 3
  triggerHint: artifact-trigger-hint-radiation
  components:
  - type: ArtifactMicrowaveTrigger
  - type: ArtifactDamageTrigger
    damageTypes:
    - Radiation
    damageThreshold: 50
  - type: RadiationReceiver

- type: artifactTrigger
  id: TriggerHighPressure
  targetDepth: 3
  triggerHint: artifact-trigger-hint-pressure
  components:
  - type: ArtifactPressureTrigger
    maxPressureThreshold: 385

- type: artifactTrigger
  id: TriggerPlasma
  targetDepth: 3
  triggerHint: artifact-trigger-hint-plasma
  components:
  - type: ArtifactGasTrigger
    possibleGas:
    - Plasma

#don't add in new targetdepth values until you have a few
#or else it will skew heavily towards a few options.
