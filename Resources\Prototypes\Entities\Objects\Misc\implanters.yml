# Implanters

- type: entity
  name: "імплантатор"
  description: "Шприц, призначений виключно для введення та вилучення підшкірних імплантатів."
  id: BaseImplanter
  parent: BaseItem
  abstract: true
  components:
    - type: ItemSlots
    - type: ContainerContainer
      containers:
        implanter_slot: !type:ContainerSlot { }
    - type: Implanter
      whitelist:
        components:
        - Body # no chair microbomb
      blacklist:
        components:
        - Guardian # no holoparasite macrobomb wombo combo
        tags:
        - Unimplantable
      currentMode: Draw
      implanterSlot:
        name: Implant
        locked: True
        priority: 0
        whitelist:
          tags:
            - SubdermalImplant
    - type: Sprite
      sprite: Objects/Specific/Medical/implanter.rsi
      state: implanter0
      layers:
        - state: implanter0
          map: [ "implantOnly" ]
          visible: true
        - state: implanter1
          map: [ "implantFull" ]
          visible: false
    - type: Item
      sprite: Objects/Specific/Medical/implanter.rsi
      heldPrefix: 0
      size: Small
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.ImplanterVisuals.Full:
          implantFull:
            True: {visible: true}
            False: {visible: false}
        enum.ImplanterImplantOnlyVisuals.ImplantOnly:
          implantOnly:
            True: {state: broken}
            False: {state: implanter0}

- type: entity
  id: Implanter
  parent: BaseImplanter
  description: "Одноразовий шприц, призначений виключно для введення та вилучення підшкірних імплантатів."
  components:
    - type: Tag
      tags:
        - Trash

- type: entity
  parent: Implanter
  id: ImplanterAdmeme
  suffix: Admeme
  components:
  - type: Implanter
    # go wild with sentient chairs with macrobombs
    whitelist: null
    blacklist: null

- type: entity
  id: BaseImplantOnlyImplanter
  parent: Implanter
  description: "Одноразовий шприц, призначений виключно для введення підшкірних імплантатів."
  abstract: true
  components:
    - type: Sprite
      sprite: Objects/Specific/Medical/implanter.rsi
      state: implanter0
      layers:
        - state: implanter1
          map: [ "implantFull" ]
          visible: true
        - state: implanter0
          map: [ "implantOnly" ]
    - type: Implanter
      currentMode: Inject
      implantOnly: true

- type: entity
  id: BaseImplantOnlyImplanterSyndi
  parent: BaseImplantOnlyImplanter
  description: "Компактний одноразовий шприц, призначений виключно для ін'єкцій підшкірних імплантатів."
  abstract: true
  components:
    - type: Item
      sprite: Objects/Specific/Medical/syndi_implanter.rsi
    - type: Sprite
      sprite: Objects/Specific/Medical/syndi_implanter.rsi
      state: implanter1
      layers:
        - state: implanter0
          map: [ "implantFull" ]
          visible: true
        - state: implanter1
          map: [ "implantOnly" ]
    - type: GenericVisualizer
      visuals:
        enum.ImplanterVisuals.Full:
          implantFull:
            True: {visible: true}
            False: {visible: false}
        enum.ImplanterImplantOnlyVisuals.ImplantOnly:
          implantOnly:
            True: {state: broken}
            False: {state: implanter1}
    - type: Tag
      tags: []

#Fun implanters

- type: entity
  id: SadTromboneImplanter
  name: "імплантантер сумного тромбона"
  parent: BaseImplantOnlyImplanter
  components:
    - type: Implanter
      implant: SadTromboneImplant

- type: entity
  id: LightImplanter
  name: "світловий імплантантер"
  parent: BaseImplantOnlyImplanter
  components:
    - type: Implanter
      implant: LightImplant

- type: entity
  id: BikeHornImplanter
  name: "імплантантер велосипедного клаксона"
  parent: BaseImplantOnlyImplanter
  components:
    - type: Implanter
      implant: BikeHornImplant

#Security implanters

- type: entity
  id: TrackingImplanter
  name: "відстежуючий імплантантер"
  parent: BaseImplantOnlyImplanter
  components:
    - type: Implanter
      implant: TrackingImplant

#Traitor implanters

- type: entity
  id: StorageImplanter
  name: "імплантантер сховища"
  parent: BaseImplantOnlyImplanterSyndi
  components:
    - type: Implanter
      implant: StorageImplant

- type: entity
  id: FreedomImplanter
  name: "імплантантер свободи"
  parent: BaseImplantOnlyImplanterSyndi
  components:
    - type: Implanter
      implant: FreedomImplant

- type: entity
  id: UplinkImplanter
  name: "імплантантер висхідної лінії зв'язку"
  parent: BaseImplantOnlyImplanterSyndi
  components:
  - type: Implanter
    implant: UplinkImplant

- type: entity
  id: EmpImplanter
  name: "імплантантер ЕМП"
  parent: BaseImplantOnlyImplanterSyndi
  components:
    - type: Implanter
      implant: EmpImplant

- type: entity
  id: ScramImplanter
  name: "скремблерний імплантантер"
  parent: BaseImplantOnlyImplanterSyndi
  components:
    - type: Implanter
      implant: ScramImplant

- type: entity
  id: DnaScramblerImplanter
  name: "імплантантер скремблера ДНК"
  parent: BaseImplantOnlyImplanterSyndi
  components:
    - type: Implanter
      implant: DnaScramblerImplant

#Nuclear Operative/Special implanters

- type: entity
  id: MicroBombImplanter
  name: "імплантантер мікробом"
  parent: BaseImplantOnlyImplanterSyndi
  components:
    - type: Implanter
      implant: MicroBombImplant

- type: entity
  id: MacroBombImplanter
  name: "імплантантер макробомби"
  parent: BaseImplantOnlyImplanterSyndi
  components:
    - type: Implanter
      implant: MacroBombImplant

- type: entity
  id: DeathRattleImplanter
  name: "імплантантер брязкальця смерті"
  parent: BaseImplantOnlyImplanterSyndi
  components:
    - type: Implanter
      implant: DeathRattleImplant

- type: entity
  id: DeathAcidifierImplanter
  name: "імплантантер підкислювача смерті"
  parent: BaseImplantOnlyImplanterSyndi
  components:
  - type: Implanter
    implant: DeathAcidifierImplant

# Security and Command implanters

- type: entity
  id: MindShieldImplanter
  name: "імплантантер розумового щита"
  parent: BaseImplantOnlyImplanter
  components:
    - type: Implanter
      implant: MindShieldImplant

# Example component implant
- type: entity
  id: ComponentImplanter
  suffix: DEBUG
  parent: BaseImplantOnlyImplanter
  components:
    - type: Implanter
      implant: ComponentImplant