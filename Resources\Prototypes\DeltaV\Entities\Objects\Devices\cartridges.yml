- type: entity
  parent: BaseItem
  id: CrimeAssistCartridge
  name: "картридж для допомоги у розслідуванні злочинів"
  description: "Картридж, який допомагає виявляти злочини та бачити відповідне покарання."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Devices/cartridge.rsi
    state: cart-cri
  - type: Icon
    sprite: DeltaV/Objects/Devices/cartridge.rsi
    state: cart-cri
  - type: UIFragment
    ui: !type:CrimeAssistUi
  - type: Cartridge
    programName: crime-assist-program-name
    icon:
      sprite: DeltaV/Icons/cri.rsi
      state: cri

- type: entity
  parent: BaseItem
  id: SecWatchCartridge
  name: "картридж для SecWatch"
  description: "Картридж, який відстежує статус осіб, що перебувають у розшуку."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Devices/cartridge.rsi
    state: cart-cri
  - type: Icon
    sprite: DeltaV/Objects/Devices/cartridge.rsi
    state: cart-cri
  - type: UIFragment
    ui: !type:SecWatchUi
  - type: Cartridge
    programName: sec-watch-program-name
    icon:
      sprite: Objects/Weapons/Melee/stunbaton.rsi
      state: stunbaton_on
  - type: SecWatchCartridge

- type: entity
  parent: BaseItem
  id: MailMetricsCartridge
  name: "картридж поштової метрики"
  description: "Картридж, який відстежує статистику, пов'язану з доставкою пошти."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Devices/cartridge.rsi
    state: cart-mail
  - type: Icon
    sprite: DeltaV/Objects/Devices/cartridge.rsi
    state: cart-mail
  - type: UIFragment
    ui: !type:MailMetricUi
  - type: MailMetricsCartridge
  - type: Cartridge
    programName: mail-metrics-program-name
    icon:
      sprite: Objects/Specific/Mail/mail.rsi
      state: icon

- type: entity
  parent: BaseItem
  id: StockTradingCartridge
  name: "Картридж StockTrading"
  description: "Картридж, який відстежує міжгалактичний фондовий ринок."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Devices/cartridge.rsi
    state: cart-stonk
  - type: Icon
    sprite: DeltaV/Objects/Devices/cartridge.rsi
    state: cart-mail
  - type: UIFragment
    ui: !type:StockTradingUi
  - type: StockTradingCartridge
  - type: Cartridge
    programName: stock-trading-program-name
    icon:
      sprite: DeltaV/Misc/program_icons.rsi
      state: stock_trading
  - type: BankClient
  - type: AccessReader # This is so that we can restrict who can buy stocks
    access: [["Orders"]]

- type: entity
  parent: BaseItem
  id: NanoChatCartridge
  name: "Картридж NanoChat"
  description: "Дозволяє надсилати повідомлення іншим людям!"
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Devices/cartridge.rsi
    state: cart-chat
  - type: UIFragment
    ui: !type:NanoChatUi
  - type: NanoChatCartridge
  - type: Cartridge
    programName: nano-chat-program-name
    icon:
      sprite: DeltaV/Misc/program_icons.rsi
      state: nanochat
  - type: ActiveRadio
    channels:
    - Common
