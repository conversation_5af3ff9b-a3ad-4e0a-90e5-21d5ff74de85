- type: entity
  id: SpawnPointPlayerCharacter
  name: "точка спавну привидів"
  suffix: player character, DO NOT MAP
  parent: MarkerBase
  components:
    - type: GhostRole
      name: Roleplay Ghost Role
      description: Placeholder
      rules: Placeholder
    - type: GhostRoleCharacterSpawner
    - type: Sprite
      sprite: Markers/jobs.rsi
      layers:
        - state: green
        - sprite: Mobs/Species/Human/parts.rsi
          state: full

- type: entity # Part of PirateRadioSpawn
  categories: [ HideSpawnMenu ]
  id: SpawnPointGhostSyndicateListener
  name: "точка спавну привидів"
  suffix: syndicate listener
  parent: MarkerBase
  components:
  - type: GhostRole
    name: ghost-role-information-listeningop-name
    description: ghost-role-information-listeningop-description
    rules: ghost-role-information-listeningop-rules
    requirements: # Worth considering these numbers for the goal of making sure someone willing to MRP takes this.
    - !type:CharacterOverallTimeRequirement
      min: 259200 # 72 hours
    - !type:CharacterDepartmentTimeRequirement
      department: Security
      min: 40000 # 11.1 hours
    - !type:CharacterDepartmentTimeRequirement
      department: Civilian
      min: 40000 # 11.1 hours
    - !type:CharacterDepartmentTimeRequirement
      department: Command
      min: 40000 # 11.1 hours
  - type: GhostRoleMobSpawner
    prototype: MobHumanSyndicateListener
  - type: Sprite
    sprite: Markers/jobs.rsi
    layers:
      - state: green
      - sprite: Structures/Wallmounts/signs.rsi
        state: radiation
  - type: GhostRoleAntagSpawner

- type: entity
  parent: MarkerBase
  id: SpawnPointGhostParadoxAnomaly
  name: "точка спавну парадоксальної аномалії"
  components:
  - type: GhostRole
    name: ghost-role-information-paradox-anomaly-name
    description: ghost-role-information-paradox-anomaly-description
    rules: ghost-role-information-paradox-anomaly-rules
  - type: ParadoxAnomalySpawner
  - type: GhostRoleAntagSpawner
  - type: Sprite
    sprite: Markers/jobs.rsi
    layers:
      - state: green
