- type: entity
  parent: SimpleMobBase
  id: MobPibble
  name: "пітбуль"
  description: "Собака-нянька. Або лабораторний мікс, залежно від того, хто запитує."
  components:
  - type: MeleeWeapon
    hidden: true
    soundHit:
        path: /Audio/Effects/bite.ogg
    angle: 0
    animation: WeaponArcBite
    damage:
      types:
        Slash: 4
        Piercing: 8
        Blunt: 1
        Structural: 4
  - type: MobState
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Pets/pitbull.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: pibble
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 135 #Bigger than Laika
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Appearance
  - type: Inventory
    speciesId: dog
    templateId: pet
  - type: InventorySlots
  - type: Strippable
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
  - type: DamageStateVisuals
    states:
      Alive:
        Base: pibble
      Critical:
        Base: pibble_dead
      Dead:
        Base: pibble_dead
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 3
  - type: ReplacementAccent
    accent: dog
  - type: InteractionPopup
    successChance: 0.7
    interactSuccessString: petting-success-dog
    interactFailureString: petting-failure-pibble
    interactSuccessSound:
      path: /Audio/Animals/small_dog_bark_happy.ogg
  - type: DogVision
  - type: NpcFactionMember
    factions:
    - Pibble
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
    blackboard:
      NavSmash: !type:Bool
        true
  - type: Grammar
    attributes:
      proper: true
  - type: RandomMetadata
    nameSegments: [names_pitbull]
  - type: Speech
    speechVerb: Canine
    speechSounds: Vulpkanin
  - type: LanguageKnowledge
    speaks:
    - Dog
    understands:
    - Dog
    - TauCetiBasic
  - type: ScentTracker

- type: entity
  parent: MobPibble
  id: MobPibbleVent
  name: "вентбулл"
  description: "Щось на кшталт помісі пітбуля... чи, можливо, наступний етап еволюції піблів?"
  components:
  - type: MeleeWeapon
    hidden: true
    soundHit:
        path: /Audio/Effects/bite.ogg
    angle: 0
    animation: WeaponArcBite
    damage:
      types:
        Slash: 2
        Piercing: 5
        Blunt: 0.5
        Structural: 2
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.28
        density: 135
        mask:
        - SmallMobMask
        layer:
        - SmallMobLayer
  - type: Sprite
    scale: 0.8, 0.6
    drawdepth: Mobs
    sprite: Mobs/Pets/ventbull.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: pibble
