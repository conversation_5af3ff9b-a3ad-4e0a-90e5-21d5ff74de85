- type: entity
  parent: [BaseMob, StripableInventoryBase]
  id: BaseBorgChassis
  name: "кіборг"
  description: "Людино-машинний гібрид, який допомагає в роботі станції. Їм подобається, коли їх просять повторювати свої закони знову і знову."
  save: false
  abstract: true
  components:
  - type: Reactive
    groups:
      Acidic: [Touch]
  - type: DamageOnHighSpeedImpact
    damage:
      types:
        Blunt: 5
    soundHit:
      collection: MetalThud
  - type: CombatMode
  - type: NoSlip
  - type: StaticPrice
    price: 1250
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 150
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: MovementSpeedModifier
    baseWalkSpeed : 2.5
    baseSprintSpeed : 4.5
  - type: Sprite
    sprite: Mobs/Silicon/chassis.rsi
  - type: RotationVisuals
    horizontalRotation: 90
  - type: MobState
    allowedStates:
    - Alive
    - Critical
    - Dead
  - type: MobThresholds
    thresholds:
      0: Alive
      100: Critical
      200: Dead
    stateAlertDict:
      Alive: BorgHealth
      Critical: BorgCrit
      Dead: BorgDead
    showOverlays: false
    allowRevives: true
  - type: HealthExaminable
    examinableTypes:
      - Blunt
      - Slash
      - Piercing
      - Heat
      - Shock
    locPrefix: silicon
  - type: UserInterface
    interfaces:
      enum.SiliconLawsUiKey.Key:
        type: SiliconLawBoundUserInterface
      enum.BorgUiKey.Key:
        type: BorgBoundUserInterface
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
      # Only used for NT borgs that can switch type, defined here to avoid copy-pasting the rest of this component.
      enum.BorgSwitchableTypeUiKey.SelectBorgType:
        type: BorgSelectTypeUserInterface
  - type: ActivatableUI
    key: enum.BorgUiKey.Key
  - type: SiliconLawBound
  - type: ActionGrant
    actions:
    - ActionViewLaws
  - type: EmagSiliconLaw
    stunTime: 5
  - type: SiliconLawProvider
    laws: Asimov
  - type: IonStormTarget
  - type: Inventory
    templateId: borg
  - type: Hands
    showInHands: false
    disableExplosionRecursion: true
  - type: ComplexInteraction
  - type: IntrinsicRadioReceiver
  - type: IntrinsicRadioTransmitter
    channels:
    - Binary
    - Common
    - Science
  - type: ActiveRadio
    channels:
    - Binary
    - Common
    - Science
  - type: ZombieImmune
  - type: Repairable
    doAfterDelay: 10
    allowSelfRepair: false
  - type: BorgChassis
  - type: LockingWhitelist
    blacklist:
      components:
      - BorgChassis
      - RoboticsConsole
  - type: WiresPanel
  - type: ActivatableUIRequiresPanel
  - type: NameIdentifier
    group: Silicon
  - type: ContainerContainer
    containers:
      borg_brain: !type:ContainerSlot { }
      cell_slot: !type:ContainerSlot { }
      borg_module: !type:Container { }
      part-container: !type:Container
  - type: PowerCellSlot
    cellSlotId: cell_slot
    fitsInCharger: true
  - type: ItemToggle
    onActivate: false # You should not be able to turn off a borg temporarily.
    activated: false # gets activated when a mind is added
    onUse: false # no item-borg toggling sorry
  - type: ItemTogglePointLight
  - type: AccessToggle
  # TODO: refactor movement to just be based on toggle like speedboots but for the boots themselves
  # TODO: or just have sentient speedboots be fast idk
  - type: PowerCellDraw
    drawRate: 0.6
  # no ToggleCellDraw since dont want to lose access when power is gone
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
  - type: Body
    thermalVisibility: false
  - type: StatusEffects
    allowed:
    - Stun
    - KnockedDown
    - SlowedDown
    - Flashed
  - type: TypingIndicator
    proto: robot
  - type: Speech
    speechVerb: Robotic
    speechSounds: Borg
  - type: Vocal
    sounds:
      Unsexed: UnisexSilicon
  - type: UnblockableSpeech
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepBorg
  - type: Construction
    graph: Cyborg
    node: cyborg
    containers:
    - part-container
    - cell_slot
  - type: Lock
    locked: true
    breakOnEmag: false
    unlockOnClick: false
  - type: ActivatableUIRequiresLock
  - type: LockedWiresPanel
  - type: Damageable
    damageContainer: Silicon
    damageModifierSet: Silicon #Goobstation - Iondamage
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 75
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          path: /Audio/Machines/warning_buzzer.ogg
          params:
            volume: 5
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:EmptyContainersBehaviour
        containers:
        - borg_brain
        - borg_module
        - cell_slot
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: HandheldLight
    toggleOnInteract: false
    wattage: 0.2
    blinkingBehaviourId: blinking
    radiatingBehaviourId: radiating
  - type: LightBehaviour
    behaviours:
    - !type:FadeBehaviour
      id: radiating
      maxDuration: 2.0
      startValue: 3.0
      endValue: 2.0
      isLooped: true
      reverseWhenFinished: true
    - !type:PulseBehaviour
      id: blinking
      interpolate: Nearest
      maxDuration: 1.0
      minValue: 0.1
      maxValue: 2.0
      isLooped: true
  - type: ToggleableLightVisuals
  - type: PointLight
    enabled: false
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
    radius: 4
    netsync: false
  - type: Pullable
  - type: Puller
    needsHands: false
  - type: StandingState
  - type: Tag
    tags:
    - DoorBumpOpener
    - FootstepSound
    - CanPilot
    - SiliconEmotes
    - SiliconMob
  - type: Emoting
  - type: GuideHelp
    guides:
      - Cyborgs
  - type: StepTriggerImmune
  - type: Eye
    visMask:
      - PsionicInvisibility
      - Normal
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    - RobotTalk
    understands:
    - TauCetiBasic
    - RobotTalk
  - type: PsionicInsulation
  - type: TwistedConstructionTarget
    replacementProto: ConstructShell

- type: entity
  abstract: true
  id: BaseBorgTransponder
  components:
  - type: BorgTransponder
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: CyborgControl
    transmitFrequencyId: RoboticsConsole
    savableAddress: false
  - type: OnUseTimerTrigger
    delay: 10
    examinable: false
    beepSound:
      path: /Audio/Effects/Cargo/buzz_two.ogg
      params:
        volume: -4
  # prevent any funnies if someone makes a cyborg item...
  - type: AutomatedTimer
  - type: ExplodeOnTrigger
  # explosion does most of its damage in the center and less at the edges
  - type: Explosive
    explosionType: Minibomb
    deleteAfterExplosion: false # let damage threshold gib the borg
    totalIntensity: 30
    intensitySlope: 20
    maxIntensity: 20
    canCreateVacuum: false # its for killing the borg not the station

- type: entity
  id: BaseBorgChassisNT
  parent: [BaseBorgChassis, BaseBorgTransponder]
  abstract: true
  components:
  - type: NpcFactionMember
    factions:
    - NanoTrasen
  - type: SiliconLawProvider
    laws: Asimov
  - type: Access
    enabled: false
    groups:
    - AllAccessBorg
    tags:
    - Borg
  - type: AccessReader
    access: [["Command"], ["Research"]]
  - type: SlavedBorg # DeltaV: NT borgs are enslaved to the AI by default
    law: ObeyAI
  - type: ShowJobIcons
  - type: ShowMindShieldIcons
  - type: InteractionPopup
    interactSuccessSound:
      path: /Audio/Ambience/Objects/periodic_beep.ogg

- type: entity
  id: BaseBorgChassisSyndicate
  parent: BaseBorgChassis
  abstract: true
  components:
  - type: NpcFactionMember
    factions:
    - Syndicate
  - type: Access
    tags:
    - NuclearOperative
    - SyndicateAgent
  - type: AccessReader
    access: [["SyndicateAgent"], ["NuclearOperative"]]
  - type: SiliconLawProvider
    laws: SyndicateStatic
  - type: IntrinsicRadioTransmitter
    channels:
    - Binary
    - Syndicate
  - type: ActiveRadio
    channels:
    - Syndicate
  - type: ShowSyndicateIcons
  - type: MovementAlwaysTouching
  - type: Speech
    speechSounds: SyndieBorg
    allowedEmotes:
    - Laugh
  - type: Vocal
    sounds:
      Unsexed: UnisexSiliconSyndicate
  - type: PointLight
    color: "#dd200b"
  - type: Damageable 
    damageContainer: Silicon
    damageModifierSet: SiliconProtected 
  - type: ExplosionResistance
    damageCoefficient: 0.5
