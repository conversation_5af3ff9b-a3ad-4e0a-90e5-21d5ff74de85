﻿# Popups

network-configurator-device-saved = Успішно збережено мережевий пристрій {$device} з адресою {$address}!
network-configurator-device-failed = Не вдалося зберегти мережевий пристрій {$device}! Адреса не призначена!
network-configurator-too-many-devices = На цьому пристрої зберігається занадто багато пристроїв!
network-configurator-update-ok = Оновлена пам'ять пристрою.
network-configurator-device-already-saved = мережевий пристрій: {$device} вже збережено.
network-configurator-device-access-denied = Доступ заборонено!
network-configurator-link-mode-started = Розпочато зв’язування пристрою: {$device}
network-configurator-link-mode-stopped = Зв'язування припинено.
network-configurator-mode-link = Посилання
network-configurator-mode-list = Список
network-configurator-switched-mode = Переключено режим на: {$mode}

# Дієслова
network-configurator-save-device = Зберегти пристрій
network-configurator-configure = Налаштувати
network-configurator-switch-mode = Перемкнути режим
network-configurator-link-defaults = Посилання за замовчуванням
network-configurator-start-link = Почати зв'язання
network-configurator-link = Зв'язати

# ui
network-configurator-title-saved-devices = Збережені пристрої
network-configurator-title-device-configuration = Конфігурація пристрою
network-configurator-ui-clear-button = Очистити
network-configurator-ui-count-label = {$count} Пристроїв

# підказки
network-configurator-tooltip-set = Встановлює список пристроїв цілі
network-configurator-tooltip-add = Додає до списку пристроїв цілі
network-configurator-tooltip-edit = Редагування списку пристроїв цілі
network-configurator-tooltip-clear = Очистити список пристроїв цілі
network-configurator-tooltip-copy = Копіювати список пристроїв до утримуваного інструмента
network-configurator-tooltip-show = Показати голографічну візуалізацію списку пристроїв цілі

# дослідити
network-configurator-examine-mode-link = [color=red]Зв'язання[/color]
network-configurator-examine-mode-list = [color=green]Список[/color]
network-configurator-examine-current-mode = Поточний режим: {$mode}
network-configurator-examine-switch-modes = Натисніть {$key} для перемикання режиму

# стан елемента
network-configurator-item-status-label = Поточний режим: {$mode}
    Перемкнути: {$keybinding}
