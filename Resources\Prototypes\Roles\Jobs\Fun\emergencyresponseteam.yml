# Leader
- type: job
  id: ERTLeader
  name: job-name-ertleader
  description: job-description-ertleader
  playTimeTracker: JobERTLeader
  setPreference: false
  startingGear: ERTLeaderGearEVA
  icon: "JobIconNanotrasen"
  supervisors: job-supervisors-centcom
  canBeAntag: false
  accessGroups:
  - AllAccess
  access:
  - CentralCommand

- type: startingGear
  id: ERTLeaderGear
  subGear:
  - ERTPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitERTLeader
    back: ClothingBackpackERTLeaderFilled
    shoes: ClothingShoesBootsCombatFilled
    head: ClothingHeadHelmetERTLeader
    eyes: ClothingEyesGlassesSecurity
    gloves: ClothingHandsGlovesCombat
    outerClothing: ClothingOuterArmorBasicSlim
    id: ERTLeaderPDA
    ears: ClothingHeadsetAltCentCom
    belt: ClothingBeltSecurityFilled
    pocket1: WeaponPistolN1984NonLethal
    pocket2: FlashlightSeclite

- type: startingGear
  id: ERTLeaderGearEVA
  subGear:
  - ERTPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitERTLeader
    back: ClothingBackpackERTLeaderFilled
    shoes: ClothingShoesBootsMagAdv
    mask: ClothingMaskGasERT
    eyes: ClothingEyesGlassesSecurity
    gloves: ClothingHandsGlovesCombat
    outerClothing: ClothingOuterHardsuitERTLeader
    suitstorage: AirTankFilled
    id: ERTLeaderPDA
    ears: ClothingHeadsetAltCentCom
    belt: ClothingBeltSecurityFilled
    pocket1: WeaponPistolN1984NonLethal
    pocket2: FlashlightSeclite

- type: startingGear
  id: ERTLeaderGearEVALecter
  subGear:
  - InhandTankGear
  - ERTPlasmamanGearNoTank
  equipment:
    jumpsuit: ClothingUniformJumpsuitERTLeader
    back: ClothingBackpackERTLeaderFilled
    shoes: ClothingShoesBootsMagAdv
    mask: ClothingMaskGasERT
    eyes: ClothingEyesGlassesSecurity
    gloves: ClothingHandsGlovesCombat
    outerClothing: ClothingOuterHardsuitERTLeader
    suitstorage: WeaponRifleLecter
    id: ERTLeaderPDA
    ears: ClothingHeadsetAltCentCom
    belt: ClothingBeltSecurityFilled
    pocket1: MagazineRifle
    pocket2: MagazineRifle

# Chaplain
- type: job
  id: ERTChaplain
  name: job-name-ertchaplain
  description: job-description-ertchaplain
  playTimeTracker: JobERTChaplain
  setPreference: false
  startingGear: ERTEngineerGearEVA
  icon: "JobIconNanotrasen"
  supervisors: job-supervisors-centcom
  canBeAntag: false
  accessGroups:
  - AllAccess
  access:
  - CentralCommand
  special:
  - !type:AddComponentSpecial
    components:
    - type: BibleUser #Lets them heal with bibles

- type: startingGear
  id: ERTChaplainGear
  subGear:
  - ERTPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitERTChaplain
    back: ClothingBackpackERTChaplainFilled
    shoes: ClothingShoesLeather
    head: ClothingHeadHatFez
    eyes: ClothingEyesGlasses
    neck: ClothingNeckStoleChaplain
    gloves: ClothingHandsGlovesCombat
    outerClothing: ClothingOuterArmorBasicSlim
    id: ERTEngineerPDA
    ears: ClothingHeadsetAltCentCom
    belt: ClothingBeltStorageWaistbag
    pocket1: Flare
    pocket2: DrinkWaterBottleFull

- type: startingGear
  id: ERTChaplainGearEVA
  subGear:
  - ERTPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitERTChaplain
    back: ClothingBackpackERTChaplainFilled
    shoes: ClothingShoesBootsMagAdv
    mask: ClothingMaskGasERT
    eyes: ClothingEyesGlasses
    neck: ClothingNeckStoleChaplain
    gloves: ClothingHandsGlovesCombat
    outerClothing: ClothingOuterHardsuitERTChaplain
    suitstorage: AirTankFilled
    id: ERTChaplainPDA
    ears: ClothingHeadsetAltCentCom
    belt: ClothingBeltStorageWaistbag
    pocket1: Flare
    pocket2: DrinkWaterBottleFull

# Engineer
- type: job
  id: ERTEngineer
  name: job-name-ertengineer
  description: job-description-ertengineer
  playTimeTracker: JobERTEngineer
  setPreference: false
  startingGear: ERTEngineerGearEVA
  icon: "JobIconNanotrasen"
  supervisors: job-supervisors-centcom
  canBeAntag: false
  accessGroups:
  - AllAccess
  access:
  - CentralCommand

- type: startingGear
  id: ERTEngineerGear
  subGear:
  - ERTPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitERTEngineer
    back: ClothingBackpackERTEngineerFilled
    shoes: ClothingShoesBootsWork
    head: ClothingHeadHelmetERTEngineer
    eyes: ClothingEyesGlassesMeson
    gloves: ClothingHandsGlovesCombat
    outerClothing: ClothingOuterArmorBasicSlim
    id: ERTEngineerPDA
    ears: ClothingHeadsetAltCentCom
    belt: ClothingBeltChiefEngineerFilled
    pocket1: Flare
    pocket2: GasAnalyzer

- type: startingGear
  id: ERTEngineerGearEVA
  subGear:
  - ERTPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitERTEngineer
    back: ClothingBackpackERTEngineerFilled
    shoes: ClothingShoesBootsMagEng
    mask: ClothingMaskGasERT
    eyes: ClothingEyesGlassesMeson
    gloves: ClothingHandsGlovesCombat
    outerClothing: ClothingOuterHardsuitERTEngineer
    suitstorage: AirTankFilled
    id: ERTEngineerPDA
    ears: ClothingHeadsetAltCentCom
    belt: ClothingBeltChiefEngineerFilled
    pocket1: Flare
    pocket2: GasAnalyzer

# Security
- type: job
  id: ERTSecurity
  name: job-name-ertsecurity
  description: job-description-ertsecurity
  playTimeTracker: JobERTSecurity
  setPreference: false
  startingGear: ERTEngineerGearEVA
  icon: "JobIconNanotrasen"
  supervisors: job-supervisors-centcom
  canBeAntag: false
  accessGroups:
  - AllAccess
  access:
  - CentralCommand

- type: startingGear
  id: ERTSecurityGear
  subGear:
  - ERTPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitERTSecurity
    back: ClothingBackpackERTSecurityFilled
    shoes: ClothingShoesBootsCombatFilled
    head: ClothingHeadHelmetERTSecurity
    eyes: ClothingEyesGlassesSecurity
    gloves: ClothingHandsGlovesCombat
    outerClothing: ClothingOuterArmorBasicSlim
    id: ERTSecurityPDA
    ears: ClothingHeadsetAltCentCom
    belt: ClothingBeltSecurityFilled
    pocket1: WeaponPistolMk58Nonlethal
    pocket2: FlashlightSeclite

- type: startingGear
  id: ERTSecurityGearEVA
  subGear:
  - ERTPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitERTSecurity
    back: ClothingBackpackERTSecurityFilled
    shoes: ClothingShoesBootsMagSec
    mask: ClothingMaskGasERT
    eyes: ClothingEyesGlassesSecurity
    gloves: ClothingHandsGlovesCombat
    outerClothing: ClothingOuterHardsuitERTSecurity
    suitstorage: AirTankFilled
    id: ERTSecurityPDA
    ears: ClothingHeadsetAltCentCom
    belt: ClothingBeltSecurityFilled
    pocket1: WeaponPistolMk58Nonlethal
    pocket2: FlashlightSeclite

- type: startingGear
  id: ERTSecurityGearEVALecter
  subGear:
  - InhandTankGear
  - ERTPlasmamanGearNoTank
  equipment:
    jumpsuit: ClothingUniformJumpsuitERTSecurity
    back: ClothingBackpackERTSecurityFilled
    shoes: ClothingShoesBootsMagSec
    mask: ClothingMaskGasERT
    eyes: ClothingEyesGlassesSecurity
    gloves: ClothingHandsGlovesCombat
    outerClothing: ClothingOuterHardsuitERTSecurity
    suitstorage: WeaponRifleLecter
    id: ERTSecurityPDA
    ears: ClothingHeadsetAltCentCom
    belt: ClothingBeltSecurityFilled
    pocket1: MagazineRifle
    pocket2: MagazineRifle

# Medical
- type: job
  id: ERTMedical
  name: job-name-ertmedic
  description: job-description-ertmedic
  playTimeTracker: JobERTMedical
  setPreference: false
  startingGear: ERTMedicalGearEVA
  icon: "JobIconNanotrasen"
  supervisors: job-supervisors-centcom
  canBeAntag: false
  accessGroups:
  - AllAccess
  access:
  - CentralCommand

- type: startingGear
  id: ERTMedicalGear
  subGear:
  - ERTPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitERTMedic
    back: ClothingBackpackERTMedicalFilled
    shoes: ClothingShoesBootsCombatFilled
    head: ClothingHeadHelmetERTMedic
    eyes: ClothingEyesHudMedical
    gloves: ClothingHandsGlovesNitrile
    outerClothing: ClothingOuterArmorBasicSlim
    id: ERTMedicPDA
    ears: ClothingHeadsetAltCentCom
    belt: ClothingBeltMedicalFilled
    pocket1: Flare

- type: startingGear
  id: ERTMedicalGearEVA
  subGear:
  - ERTPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitERTMedic
    back: ClothingBackpackERTMedicalFilled
    shoes: ClothingShoesBootsMagSec
    mask: ClothingMaskGasERT
    eyes: ClothingEyesHudMedical
    gloves: ClothingHandsGlovesNitrile
    outerClothing: ClothingOuterHardsuitERTMedical
    suitstorage: AirTankFilled
    id: ERTMedicPDA
    ears: ClothingHeadsetAltCentCom
    belt: ClothingBeltMedicalFilled
    pocket1: Flare

# Janitor
- type: job
  id: ERTJanitor
  name: job-name-ertjanitor
  description: job-description-ertjanitor
  playTimeTracker: JobERTJanitor
  setPreference: false
  startingGear: ERTJanitorGearEVA
  icon: "JobIconNanotrasen"
  supervisors: job-supervisors-centcom
  canBeAntag: false
  accessGroups:
  - AllAccess
  access:
  - CentralCommand

- type: startingGear
  id: ERTJanitorGear
  subGear:
  - ERTPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitERTJanitor
    back: ClothingBackpackERTJanitorFilled
    shoes: ClothingShoesGaloshes
    head: ClothingHeadHelmetERTJanitor
    gloves: ClothingHandsGlovesColorPurple
    outerClothing: ClothingOuterArmorBasicSlim
    id: ERTJanitorPDA
    ears: ClothingHeadsetAltCentCom
    belt: ClothingBeltJanitorFilled
    pocket1: Flare

- type: startingGear
  id: ERTJanitorGearEVA
  subGear:
  - ERTPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitERTJanitor
    back: ClothingBackpackERTJanitorFilled
    shoes: ClothingShoesBootsMagSec
    mask: ClothingMaskGasERT
    gloves: ClothingHandsGlovesColorPurple
    outerClothing: ClothingOuterHardsuitERTJanitor
    suitstorage: AirTankFilled
    id: ERTJanitorPDA
    ears: ClothingHeadsetAltCentCom
    belt: ClothingBeltJanitorFilled
    pocket1: Flare
