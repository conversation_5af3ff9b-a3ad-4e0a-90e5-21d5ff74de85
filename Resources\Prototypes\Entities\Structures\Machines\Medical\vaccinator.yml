- type: entity
  id: Vaccinator
  parent: BaseMachinePowered
  name: "вакцинатор"
  description: "Машина, яка створює вакцини."
  placement:
    mode: SnapgridCenter
  components:
  - type: Sprite
    sprite: Structures/Machines/vaccinator.rsi
    layers:
    - state: icon
      map: ["enum.DiseaseMachineVisualLayers.IsRunning"]
    - state: unlit
      shader: unshaded
      map: ["enum.DiseaseMachineVisualLayers.IsOn"]
  - type: Appearance
  - type: DiseaseMachineVisuals
    idleState: icon
    runningState: running
  - type: Machine
    board: VaccinatorMachineCircuitboard
  - type: ContainerContainer
    containers:
      machine_board: !type:Container
      machine_parts: !type:Container
  - type: RequireProjectileTarget
