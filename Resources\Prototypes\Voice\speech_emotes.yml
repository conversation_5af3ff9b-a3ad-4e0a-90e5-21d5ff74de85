# vocal emotes
- type: emote
  id: Scream
  name: chat-emote-name-scream
  category: Vocal
  icon: Interface/Emotes/scream.png
  whitelist:
    components:
    - Vocal
  chatMessages: ["chat-emote-msg-scream"]
  chatTriggers:
    - screams
    - shrieks
    - screeches
    - yells

- type: emote
  id: Laugh
  name: chat-emote-name-laugh
  category: Vocal
  icon: Interface/Emotes/laugh.png
  whitelist:
    components:
    - Vocal
  chatMessages: ["chat-emote-msg-laugh"]
  chatTriggers:
    - laughs
    - chuckles
    - giggles
    - chortles

- type: emote
  id: Honk
  name: chat-emote-name-honk
  category: Vocal
  icon: Interface/Emotes/honk.png
  chatMessages: ["chat-emote-msg-honk"]
  whitelist:
    tags:
    - SiliconEmotes
    - HarpyEmotes
  chatTriggers:
    - honks

- type: emote
  id: Sigh
  name: chat-emote-name-sigh
  category: Vocal
  icon: Interface/Emotes/sigh.png
  whitelist:
    components:
    - Vocal
  chatMessages: ["chat-emote-msg-sigh"]
  chatTriggers:
    - sighs

- type: emote
  id: Whistle
  name: chat-emote-name-whistle
  category: Vocal
  icon: Interface/Emotes/whistle.png
  whitelist:
    components:
    - Vocal
  chatMessages: ["chat-emote-msg-whistle"]
  chatTriggers:
    - whistles

- type: emote
  id: Crying
  name: chat-emote-name-crying
  category: Vocal
  icon: Interface/Emotes/cry.png
  whitelist:
    components:
    - Vocal
  chatMessages: ["chat-emote-msg-crying"]
  chatTriggers:
    - cries
    - sobs

- type: emote
  id: Squish
  name: chat-emote-name-squish
  category: Vocal
  icon: Interface/Emotes/squish.png
  whitelist:
    tags:
    - HarpyEmotes
  blacklist:
    components:
    - BorgChassis
  chatMessages: ["chat-emote-msg-squish"]
  chatTriggers:
    - squishes

- type: emote
  id: Chitter
  name: chat-emote-name-chitter
  category: Vocal
  icon: Interface/Emotes/chitter.png
  whitelist:
    tags:
    - HarpyEmotes
  blacklist:
    components:
    - BorgChassis
  chatMessages: ["chat-emote-msg-chitter"]
  chatTriggers:
   - chitters

- type: emote
  id: Squeak
  name: chat-emote-name-squeak
  category: Vocal
  icon: Interface/Emotes/squeak.png
  whitelist:
    tags:
    - HarpyEmotes
  blacklist:
    components:
    - BorgChassis
  chatMessages: ["chat-emote-msg-squeak"]
  chatTriggers:
   - squeaks

- type: emote
  id: Click
  name: chat-emote-name-click
  category: Vocal
  icon: Interface/Emotes/click.png
  whitelist:
    tags:
    - HarpyEmotes
  blacklist:
    components:
    - BorgChassis
  chatMessages: ["chat-emote-msg-click"]
  chatTriggers:
   - clicks

# hand emotes
- type: emote
  id: Clap
  name: chat-emote-name-clap
  category: Hands
  icon: Interface/Emotes/clap.png
  whitelist:
    components:
    - Hands
  blacklist:
    components:
    - BorgChassis
  chatMessages: ["chat-emote-msg-clap"]
  chatTriggers:
    - claps

- type: emote
  id: Snap
  name: chat-emote-name-snap
  category: Hands
  icon: Interface/Emotes/snap.png
  whitelist:
    components:
    - Hands
  blacklist:
    components:
    - BorgChassis
  chatMessages: ["chat-emote-msg-snap"] # snaps <{THEIR($ent)}> fingers?
  chatTriggers:
    - snaps
    - snaps fingers
    - snaps his fingers
    - snaps her fingers
    - snaps their fingers
    - snaps its fingers
    - snaps its' fingers

- type: emote
  id: Salute
  name: chat-emote-name-salute
  category: Hands
  icon: Interface/Emotes/salute.png
  whitelist:
    components:
    - Hands
  blacklist:
    components:
    - BorgChassis
  chatMessages: ["chat-emote-msg-salute"]
  chatTriggers:
    - salutes

- type: emote
  id: DefaultDeathgasp
  name: chat-emote-name-deathgasp
  icon: Interface/Emotes/deathgasp.png
  whitelist:
    components:
    - MobState
  blacklist:
    tags:
    - SiliconEmotes
  chatMessages: ["chat-emote-msg-deathgasp"]
  chatTriggers:
  - deathgasp

- type: emote
  id: MonkeyDeathgasp
  name: chat-emote-name-deathgasp
  icon: Interface/Emotes/deathgasp.png
  chatMessages: ["chat-emote-msg-deathgasp-monkey"]

- type: emote
  id: SiliconDeathgasp
  name: chat-emote-name-deathgasp
  icon: Interface/Emotes/deathgasp.png
  whitelist:
    tags:
    - SiliconEmotes
  chatMessages: ["chat-emote-msg-deathgasp-silicon"]
  chatTriggers:
  - sdeathgasp

- type: emote
  id: Buzz
  name: chat-emote-name-buzz
  category: Vocal
  icon: Interface/Emotes/buzz.png
  whitelist:
    tags:
    - SiliconEmotes
    - HarpyEmotes
  chatMessages: ["chat-emote-msg-buzz"]
  chatTriggers:
    - buzzes

- type: emote
  id: Weh
  name: chat-emote-name-weh
  category: Vocal
  icon: Interface/Emotes/weh.png
  chatMessages: [ wehs ]

- type: emote
  id: Chirp
  name: chat-emote-name-chirp
  category: Vocal
  icon: Interface/Emotes/chirp.png
  whitelist:
    requireAll: false
    components:
    - Nymph
    tags:
    - HarpyEmotes
  chatMessages: ["chat-emote-msg-chirp"]
  chatTriggers:
    - chirps

# Machine Emotes
- type: emote
  id: Beep
  name: chat-emote-name-beep
  category: Vocal
  icon: Interface/Emotes/beep.png
  whitelist:
    tags:
    - SiliconEmotes
    - HarpyEmotes
  chatMessages: ["chat-emote-msg-beep"]
  chatTriggers:
    - beeps

- type: emote
  id: Boop
  name: chat-emote-name-boop
  category: Vocal
  whitelist:
    tags:
    - SiliconEmotes
    - HarpyEmotes
  chatMessages: [ boops ]
  chatTriggers:
    - boops

- type: emote
  id: Chime
  name: chat-emote-name-chime
  category: Vocal
  icon: Interface/Emotes/chime.png
  whitelist:
    tags:
    - SiliconEmotes
    - HarpyEmotes
  chatMessages: ["chat-emote-msg-chime"]
  chatTriggers:
    - chimes

- type: emote
  id: Buzz-Two
  name: chat-emote-name-buzztwo
  category: Vocal
  icon: Interface/Emotes/buzztwo.png
  whitelist:
    tags:
    - SiliconEmotes
    - HarpyEmotes
  chatMessages: ["chat-emote-msg-buzzestwo"]
  chatTriggers:
    - buzzes twice

- type: emote
  id: Ping
  name: chat-emote-name-ping
  category: Vocal
  icon: Interface/Emotes/ping.png
  whitelist:
    tags:
    - SiliconEmotes
    - HarpyEmotes
  chatMessages: ["chat-emote-msg-ping"]
  chatTriggers:
    - pings

- type: emote
  id: Whirr
  name: chat-emote-name-whirr
  category: Vocal
  whitelist:
    tags:
    - SiliconEmotes
    - HarpyEmotes
  chatMessages: [ whirrs ]
  chatTriggers:
    - whirrs

- type: emote
  id: Mars
  name: chat-emote-name-mars
  category: Vocal
  whitelist:
    tags:
    - ShadowkinEmotes
    - HarpyEmotes
  blacklist:
    components:
    - BorgChassis
  chatMessages: [ mars ]
  chatTriggers:
    - mars

- type: emote
  id: Wurble
  name: chat-emote-name-wurble
  category: Vocal
  whitelist:
    tags:
    - ShadowkinEmotes
    - HarpyEmotes
  blacklist:
    components:
    - BorgChassis
  chatMessages: [ wurble ]
  chatTriggers:
    - wurble
