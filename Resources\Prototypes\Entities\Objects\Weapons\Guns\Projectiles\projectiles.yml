- type: entity
  id: MuzzleFlashEffect
  categories: [ HideSpawnMenu ]
  components:
  - type: TimedDespawn
    lifetime: 0.4
  - type: Sprite
    drawdepth: Effects
    layers:
    - shader: unshaded
      map: ["enum.EffectLayers.Unshaded"]
      sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
      state: muzzle_bullet
  - type: EffectVisuals
  - type: Tag
    tags:
      - HideContextMenu
  - type: AnimationPlayer

- type: entity
  parent: MuzzleFlashEffect
  id: MuzzleFlashEffectOmnilaser
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    drawdepth: BelowMobs
    offset: 0.15, 0
    layers:
    - shader: unshaded
      map: ["enum.EffectLayers.Unshaded"]
      sprite: Objects/Weapons/Guns/Projectiles/projectiles_tg.rsi
      state: omnilaser_flash

- type: entity
  parent: MuzzleFlashEffect
  id: MuzzleFlashEffectHeavyLaser
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    drawdepth: BelowMobs
    offset: 0.15, 0
    layers:
    - shader: unshaded
      map: ["enum.EffectLayers.Unshaded"]
      sprite: Objects/Weapons/Guns/Projectiles/projectiles_tg.rsi
      state: heavylaser_flash

# One bullet to bring them all into the darkness and bind them
- type: entity
  id: BaseBullet
  name: "BaseBullet"
  description: "Якщо ви бачите це, ви, мабуть, мертві!"
  abstract: true
  components:
  - type: Reflective
  - type: FlyBySound
  - type: Clickable
  - type: Sprite
    noRot: false
    sprite: Objects/Weapons/Guns/Projectiles/projectiles2.rsi
    layers:
    - state: bullet
      shader: unshaded
  - type: Physics
    bodyType: Dynamic
    linearDamping: 0
    angularDamping: 0
  - type: Fixtures
    fixtures:
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.1,-0.1,0.1,0.1"
        hard: false
        mask:
        - Impassable
        - BulletImpassable
      fly-by: &flybyfixture
        shape: !type:PhysShapeCircle
          radius: 1.5
        layer:
        - Impassable
        - MidImpassable
        - HighImpassable
        - LowImpassable
        hard: False
  - type: Projectile
    impactEffect: BulletImpactEffect
    damage:
      types:
        Piercing: 14
    soundHit:
      path: /Audio/Weapons/Guns/Hits/bullet_hit.ogg
  - type: TimedDespawn
    lifetime: 10

- type: entity
  id: BaseBulletTrigger # Trigger-on-collide bullets
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: TriggerOnCollide
    fixtureID: projectile
  - type: Projectile
    damage:
      types:
        Blunt: 7 # more damage than a punch.
  - type: Fixtures
    fixtures:
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.15,-0.45,0.15,0.15"
        hard: false
        mask:
        - Impassable
        - BulletImpassable
      fly-by: *flybyfixture

- type: entity
  id: BaseBulletPractice
  name: "практика з базовими кулями"
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles2.rsi
    layers:
    - state: practice
  - type: Projectile
    damage:
      types:
        Blunt: 1

- type: entity
  id: BaseBulletRubber
  name: "базова куля гумова"
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles2.rsi
    layers:
    - state: rubber
  - type: Projectile
    damage:
      types:
        Blunt: 3
    soundHit:
      path: /Audio/Weapons/Guns/Hits/snap.ogg
  - type: StaminaDamageOnCollide
    damage: 22 # 5 hits to stun sounds reasonable
  - type: Tag
    tags:
    - BulletRubber

- type: entity
  id: BaseBulletIncendiary
  name: "базова куля запальна"
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Projectile
    damage:
      types:
        Blunt: 14
  - type: PointLight
    enabled: true
    color: "#ff4300"
    radius: 2.0
    energy: 7.0
  - type: IgniteOnCollide
    fireStacks: 0.25

- type: entity
  id: BaseBulletAP
  name: "базова куля бронебійний"
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles2.rsi
    layers:
    - state: piercing
  - type: Projectile
    damage:
      types:
        Piercing: 11 # 20% decrease
    ignoreResistances: true

- type: entity
  id: BaseBulletUranium
  name: "базовий уран для куль"
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles2.rsi
    layers:
    - state: uranium
  - type: Projectile
    damage:
      types:
        Radiation: 11

# Energy projectiles
- type: entity
  name: "болт електрошокера"
  id: BulletTaser
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: FlyBySound
    sound:
      collection: EnergyMiss
      params:
        volume: 5
  - type: Sprite
    noRot: true
    sprite: Objects/Weapons/Guns/Projectiles/projectiles2.rsi
    color: "#ffff33"
    layers:
    - state: spark
      shader: unshaded
  - type: Physics
  - type: Fixtures
    fixtures:
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.15,-0.45,0.15,0.15"
        hard: false
        mask:
          - Impassable
          - BulletImpassable
      fly-by: *flybyfixture
  - type: Ammo
  - type: Projectile
    damage:
      types:
        Shock: 5
    soundHit:
      path: "/Audio/Weapons/Guns/Hits/taser_hit.ogg"
    forceSound: true
  - type: StunOnCollide
    stunAmount: 5
    knockdownAmount: 5

- type: entity
  name : disabler bolt
  id: BulletDisabler
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Reflective
    reflective:
    - Energy
  - type: FlyBySound
    sound:
      collection: EnergyMiss
      params:
        volume: 5
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles_tg.rsi
    layers:
    - state: omnilaser
      shader: unshaded
  - type: Physics
  - type: Fixtures
    fixtures:
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.15,-0.3,0.15,0.3"
        hard: false
        mask:
        - Opaque # WWDP allow shooting through windows
      fly-by: *flybyfixture
  - type: Ammo
  - type: StaminaDamageOnCollide
    damage: 30
  - type: Projectile
    impactEffect: BulletImpactEffectDisabler
    damage:
      types:
        Shock: 2
    soundHit:
      collection: WeakHit
    forceSound: true

- type: entity
  name : disabler bolt
  id: BulletCivilianDisabler
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Reflective
    reflective:
    - Energy
  - type: FlyBySound
    sound:
      collection: EnergyMiss
      params:
        volume: 3
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles_tg.rsi
    layers:
    - state: omnilaser_weak
      shader: unshaded
  - type: Physics
  - type: Fixtures
    fixtures:
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.15,-0.3,0.15,0.3"
        hard: false
        mask:
        - Impassable
        - BulletImpassable
      fly-by: *flybyfixture
  - type: Ammo
  - type: StaminaDamageOnCollide
    damage: 20
  - type: Projectile
    impactEffect: BulletImpactEffectDisabler
    damage:
      types:
        Shock: 0.3
    soundHit:
      collection: WeakHit
    forceSound: true

- type: entity
  name : disabler bolt practice
  id: BulletDisablerPractice
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: FlyBySound
    sound:
      collection: EnergyMiss
      params:
        volume: 5
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles_tg.rsi
    layers:
    - state: omnilaser
      shader: unshaded
  - type: Physics
  - type: Fixtures
    fixtures:
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.15,-0.3,0.15,0.3"
        hard: false
        mask:
        - Opaque # WWDP allow shooting through windows
      fly-by: *flybyfixture
  - type: Ammo
  - type: StaminaDamageOnCollide
    damage: 1
  - type: Projectile
    impactEffect: BulletImpactEffectDisabler
    damage:
      types:
        Shock: 1
    soundHit:
      collection: WeakHit
    forceSound: true

- type: entity
  name: "болт випромінювача"
  id: EmitterBolt
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Structures/Power/Generation/Singularity/emitter.rsi
    layers:
      - state: projectile
        shader: unshaded
  - type: Ammo
    muzzleFlash: null
  - type: Physics
  - type: Fixtures
    fixtures:
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.2,-0.2,0.2,0.2"
        hard: false
        mask:
        - Opaque
      fly-by: *flybyfixture
  - type: Reflective # Goobstation - Make emitter bolts reflect as energy
    reflective:
    - Energy
  - type: Projectile
#   soundHit:  Waiting on serv3
    damage:
      types:
        Shock: 14
  # mining laser real
  - type: GatheringProjectile
  - type: Tag
    tags:
    - EmitterBolt
  - type: TimedDespawn
    lifetime: 3

- type: entity
  name: "сторожовий болт"
  id: WatcherBolt
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: FlyBySound
    sound:
      collection: EnergyMiss
      params:
        volume: 5
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles_tg.rsi
    layers:
    - state: omnilaser
      shader: unshaded
  - type: Ammo
    muzzleFlash: null
  - type: Physics
  - type: Fixtures
    fixtures:
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.2,-0.2,0.2,0.2"
        hard: false
        mask:
        - Opaque
      fly-by: *flybyfixture
  - type: StaminaDamageOnCollide
    damage: 40
  - type: Projectile
    #   soundHit:  Waiting on serv3
    impactEffect: BulletImpactEffectDisabler
    damage:
      types:
        Cold: 10
  - type: TimedDespawn
    lifetime: 3

- type: entity
  name: "магнітний болт зі сторожовим замком"
  id: WatcherBoltMagmawing
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles_tg.rsi
    layers:
    - state: omnilaser_greyscale
      shader: unshaded
      color: orangered
  - type: Projectile
    #   soundHit:  Waiting on serv3
    impactEffect: BulletImpactEffectOrangeDisabler
    damage:
      types:
        Heat: 10

- type: entity
  id: BulletKinetic
  name: "кінетичний болт"
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  description: "Не так вже й погано, але ви все одно не хочете, щоб він вас зачепив."
  components:
  - type: Reflective
    reflective:
      - NonEnergy
  - type: Sprite
    noRot: false
    sprite: Objects/Weapons/Guns/Projectiles/magic.rsi
    layers:
    - state: chronobolt
      shader: unshaded
  - type: Projectile
    impactEffect: BulletImpactEffectKinetic
    damage:
      types:
        Blunt: 40 # Lavaland Change - TOTAL PKA VICTORY
        Structural: 30 # Lavaland/Eris change: Yes easy break ins
  # Short lifespan
  - type: TimedDespawn
    lifetime: 0.170 # Lavaland Change

- type: entity
  id: BulletKineticShuttle
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    noRot: false
    sprite: Objects/Weapons/Guns/Projectiles/magic.rsi
    layers:
    - state: chronobolt
      shader: unshaded
  - type: Projectile
    impactEffect: BulletImpactEffectKinetic
    damage:
      types:
        Blunt: 30
        Structural: 35
  - type: Ammo
    muzzleFlash: HitscanEffect
  - type: TimedDespawn
    lifetime: 1.5
  - type: PointLight
    radius: 2.5
    color: white
    energy: 0.5
  - type: GatheringProjectile

- type: entity
  id: BulletCharge
  name: "зарядний болт"
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  description: "Позначає ціль для нанесення додаткової шкоди."
  components:
  - type: Reflective
    reflective:
    - NonEnergy
  - type: Sprite
    noRot: false
    sprite: Objects/Weapons/Guns/Projectiles/magic.rsi
    layers:
      - state: chronobolt
        shader: unshaded
  - type: GatheringProjectile
  - type: DamageMarkerOnCollide
    onlyWorkOnFauna: true # Lavaland Change
    whitelist:
      components:
        - MobState
    damage:
      types:
        Blunt: 20
        Slash: 5
  - type: Projectile
    impactEffect: BulletImpactEffectKinetic
    damage:
      types:
        Blunt: 0
  # Short lifespan
  - type: TimedDespawn
    lifetime: 0.4

- type: entity
  parent: BaseBullet
  id: AnomalousParticleDelta
  name: "дельта-частинки"
  categories: [ HideSpawnMenu ]
  components:
  - type: PointLight
    enabled: true
    color: "#c2385d"
    radius: 2.0
    energy: 7.0
  - type: AnomalousParticle
    particleType: Delta
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/magic.rsi
    layers:
      - state: magicm_red
        shader: unshaded
  - type: Ammo
    muzzleFlash: null
  - type: Physics
  - type: Fixtures
    fixtures:
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.2,-0.2,0.2,0.2"
        hard: false
        mask:
        - Opaque
      fly-by: *flybyfixture
  - type: Projectile
    damage:
      types:
        Heat: 5
  - type: TimedDespawn
    lifetime: 3
  - type: Reflective
    reflective:
    - Energy


- type: entity
  parent: AnomalousParticleDelta
  id: AnomalousParticleDeltaStrong
  categories: [ HideSpawnMenu ]
  components:
  - type: AnomalousParticle
    particleType: Delta
    severityPerSeverityHit: 0.05
    stabilityPerDestabilizingHit: 0.08
    healthPerWeakeningeHit: -0.1
    stabilityPerWeakeningeHit: -0.2

- type: entity
  parent: AnomalousParticleDelta
  id: AnomalousParticleEpsilon
  name: "епсилон-частинки"
  categories: [ HideSpawnMenu ]
  components:
  - type: PointLight
    enabled: true
    color: "#38c296"
    radius: 2.0
    energy: 7.0
  - type: Sprite
    layers:
      - state: magicm_cyan
        shader: unshaded
  - type: AnomalousParticle
    particleType: Epsilon

- type: entity
  parent: AnomalousParticleEpsilon
  id: AnomalousParticleEpsilonStrong
  categories: [ HideSpawnMenu ]
  components:
  - type: AnomalousParticle
    particleType: Epsilon
    severityPerSeverityHit: 0.05
    stabilityPerDestabilizingHit: 0.08
    healthPerWeakeningeHit: -0.1
    stabilityPerWeakeningeHit: -0.2

- type: entity
  parent: AnomalousParticleDelta
  id: AnomalousParticleZeta
  name: "зета-частинки"
  categories: [ HideSpawnMenu ]
  components:
  - type: PointLight
    enabled: true
    color: "#b9c238"
    radius: 2.0
    energy: 7.0
  - type: Sprite
    layers:
      - state: magicm_yellow
        shader: unshaded
  - type: AnomalousParticle
    particleType: Zeta

- type: entity
  parent: AnomalousParticleZeta
  id: AnomalousParticleZetaStrong
  categories: [ HideSpawnMenu ]
  components:
  - type: AnomalousParticle
    particleType: Zeta
    severityPerSeverityHit: 0.05
    stabilityPerDestabilizingHit: 0.08
    healthPerWeakeningeHit: -0.1
    stabilityPerWeakeningeHit: -0.2

- type: entity
  parent: AnomalousParticleDelta
  id: AnomalousParticleOmegaStrong
  name: "омега-частинки"
  categories: [ HideSpawnMenu ]
  components:
  - type: PointLight
    enabled: true
    color: "#38c24f"
    radius: 2.0
    energy: 7.0
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/magic.rsi
    layers:
      - state: magicm_green
        shader: unshaded
  - type: AnomalousParticle
    particleType: Default
    severityOverride: true
    severityPerSeverityHit: 0.1
    stabilityPerDestabilizingHit: 0.05
    healthPerWeakeningeHit: 1
    stabilityPerWeakeningeHit: -0.05
  - type: Projectile
    impactEffect: BulletImpactEffectKinetic
    damage:
      types:
        Heat: 20

- type: entity
  parent: AnomalousParticleDelta
  id: AnomalousParticleSigma
  name: "сигма-частинки"
  categories: [ HideSpawnMenu ]
  components:
  - type: PointLight
    enabled: true
    color: "#8d38c2"
    radius: 2.0
    energy: 7.0
  - type: Sprite
    layers:
      - state: magicm
        shader: unshaded
  - type: AnomalousParticle
    particleType: Sigma

- type: entity
  parent: AnomalousParticleSigma
  id: AnomalousParticleSigmaStrong
  name: "сигма-частинки"
  categories: [ HideSpawnMenu ]
  components:
  - type: AnomalousParticle
    particleType: Sigma

# Launcher projectiles (grenade / rocket)
- type: entity
  id: BulletRocket
  name: "ракета"
  parent: BaseBulletTrigger
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles2.rsi
    layers:
      - state: frag
  - type: ExplodeOnTrigger
  - type: Explosive
    explosionType: Default
    maxIntensity: 40
    intensitySlope: 6
    totalIntensity: 200
    maxTileBreak: 1
  - type: PointLight
    radius: 3.5
    color: orange
    energy: 0.5

- type: entity
  id: BulletWeakRocket
  name: "слабка ракета"
  parent: BaseBulletTrigger
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles2.rsi
    layers:
      - state: frag
  - type: ExplodeOnTrigger
  - type: Explosive
    explosionType: Default
    maxIntensity: 2 # max 30 per tile
    intensitySlope: 1
    totalIntensity: 4 # 60 total damage to distribute over tiles
    maxTileBreak: 1
  - type: PointLight
    radius: 3.5
    color: orange
    energy: 0.5

- type: entity
  id: BulletGrenadeBaton
  name: "світлошумова граната"
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles2.rsi
    layers:
      - state: grenade
  - type: Projectile
    deleteOnCollide: false
    damage:
      types:
        Blunt: 1
    soundHit:
      path: /Audio/Effects/gen_hit.ogg
  - type: StaminaDamageOnCollide
    damage: 80

- type: entity
  id: BulletGrenadeBlast
  name: "бойова граната"
  parent: BaseBulletTrigger
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles2.rsi
    layers:
      - state: grenade
  - type: ExplodeOnTrigger
  - type: Explosive
    explosionType: Default
    totalIntensity: 150 # a ~2 tile radius
    intensitySlope: 5
    maxIntensity: 10

- type: entity
  id: BulletGrenadeFlash
  name: "світлошумова граната"
  parent: BaseBulletTrigger
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles2.rsi
    layers:
      - state: grenade
  - type: FlashOnTrigger
    range: 7
  - type: SpawnOnTrigger
    proto: GrenadeFlashEffect
  - type: ActiveTimerTrigger
    timeRemaining: 0.3
  - type: DeleteOnTrigger

# This is supposed to spawn shrapnel and stuff so uhh... TODO?
- type: entity
  id: BulletGrenadeFrag
  name: "осколкова граната"
  parent: BaseBulletTrigger
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles2.rsi
    layers:
      - state: grenade
  - type: ExplodeOnTrigger
  - type: Explosive
    explosionType: Default
    totalIntensity: 175 # about a ~6 tile radius
    intensitySlope: 1
    maxIntensity: 10

- type: entity
  id: BulletGrenadeEMP
  name: "ЕМІ-ракета"
  parent: BaseBulletTrigger
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles2.rsi
    layers:
      - state: frag
  - type: EmpOnTrigger
    range: 5
    energyConsumption: 50000
    disableDuration: 10
  - type: Ammo
    muzzleFlash: null
  - type: PointLight
    radius: 3.5
    color: blue
    energy: 0.5

- type: entity
  id: BulletGrenadeSpreadBeanbag
  categories: [ HideSpawnMenu ]
  parent: PelletShotgun
  components:
  - type: ProjectileSpread
    proto: PelletShotgunBeanbag
    count: 8
    spread: 25

- type: entity
  id: BulletGrenadeSpreadBirdshot
  categories: [ HideSpawnMenu ]
  parent: PelletShotgun
  components:
  - type: ProjectileSpread
    proto: BulletGrenadeBirdshot
    count: 50
    spread: 25

- type: entity
  id: BulletGrenadeBirdshot
  categories: [ HideSpawnMenu ]
  parent: PelletShotgun
  components:
  - type: Sprite
    scale: 0.5, 0.5
    sprite: Objects/Weapons/Guns/Projectiles/projectiles2.rsi
    state: buckshot
  - type: Projectile
    damage:
      types:
        Piercing: 10

- type: entity
  id: BulletGrenadeSpread00Buckshot
  categories: [ HideSpawnMenu ]
  parent: PelletShotgun
  components:
  - type: ProjectileSpread
    proto: PelletShotgun00Buckshot
    count: 32
    spread: 25

- type: entity
  id: BulletGrenadeSpread0000Buckshot
  categories: [ HideSpawnMenu ]
  parent: PelletShotgun
  components:
  - type: ProjectileSpread
    proto: PelletShotgun0000Buckshot
    count: 24
    spread: 25

- type: entity
  id: BulletGrenadeSlug
  name: "світлошумова граната"
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles2.rsi
    layers:
      - state: grenade
  - type: Projectile
    damage:
      types:
        Blunt: 250 # At this point you're firing a cannon shell at someone.
        Structural: 800 # Will instantly destroy pretty much any structure it hits.
    forceSound: true
    soundHit:
      path: /Audio/DeltaV/Misc/reducedtoatmos.ogg
  - type: StaminaDamageOnCollide
    damage: 150 # JUST IN CASE THEY SOMEHOW SURVIVE IT.

- type: entity
  id: BulletCap
  name: "капсюльна куля"
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    layers:
      - state: capbullet
  - type: Projectile
    deleteOnCollide: true
    damage:
      types:
        Piercing: 0

- type: entity
  id: BulletAcid
  name: "кислотна слина"
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
    - type: Projectile
      damage:
        types:
          Caustic: 5
    - type: Sprite
      sprite: Objects/Weapons/Guns/Projectiles/xeno_toxic.rsi
      layers:
        - state: xeno_toxic
    - type: Ammo
      muzzleFlash: null

- type: entity
  id: BulletAcidSentinel
  name: "кислотна слина"
  parent: BaseBullet
  components:
  - type: Projectile
    damage:
      types:
        Caustic: 33
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/xeno_toxic.rsi
    layers:
    - state: xeno_toxic
  - type: Ammo
    muzzleFlash: null

- type: entity
  id: BulletWaterShot
  name: "вода"
  categories: [ HideSpawnMenu ]
  components:
  - type: Clickable
  - type: Physics
    bodyType: Dynamic
    linearDamping: 0
    angularDamping: 0
  - type: TimedDespawn
    lifetime: 10
  - type: Projectile
    damage:
      types:
        Blunt: 0 #it's just water, bro
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/water_shot.rsi
    layers:
    - state: icon
      map: ["enum.VaporVisualLayers.Base"]
  - type: Ammo
    muzzleFlash: null
  - type: SolutionContainerManager
    solutions:
      vapor:
        maxVol: 50
  - type: Fixtures
    fixtures:
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.10,-0.30,0.10,0.15"
        hard: false
        mask:
        - Impassable
        - BulletImpassable
  - type: Vapor
    active: true
  - type: Appearance
  - type: VaporVisuals

- type: entity
  id: BulletCannonBall
  name: "гарматне ядро"
  parent: BaseBulletTrigger
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles2.rsi
    layers:
      - state: ball
  - type: ExplodeOnTrigger
  - type: Explosive
    explosionType: DemolitionCharge
    maxIntensity: 9
    intensitySlope: 6
    totalIntensity: 60
    maxTileBreak: 0
  - type: PointLight
    radius: 1
    color: orange
    energy: 0.5

# - type: entity
#   id: GrapplingHook
#   name: grappling hook
#   categories: [ HideSpawnMenu ]
#   components:
#     - type: EmbeddableProjectile
#       deleteOnRemove: true
#     - type: Clickable
#     - type: InteractionOutline
#     - type: Sprite
#       noRot: false
#       sprite: Objects/Weapons/Guns/Launchers/grappling_gun.rsi
#       layers:
#         - state: hook
#     - type: Physics
#       bodyType: Dynamic
#       linearDamping: 0
#       angularDamping: 0
#     - type: Projectile
#       deleteOnCollide: false
#       damage:
#         types:
#           Blunt: 0
#     - type: Fixtures
#       fixtures:
#         projectile:
#           shape:
#             !type:PhysShapeAabb
#             bounds: "-0.1,-0.1,0.1,0.1"
#           hard: false
#           mask:
#             - Impassable
#             - HighImpassable
#    - type: GrapplingProjectile

- type: entity
  name : disabler bolt smg
  id: BulletDisablerSmg
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Reflective
    reflective:
    - Energy
  - type: FlyBySound
    sound:
      collection: EnergyMiss
      params:
        volume: 5
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles_tg.rsi
    layers:
    - state: omnilaser
      shader: unshaded
  - type: Physics
  - type: Fixtures
    fixtures:
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.15,-0.3,0.15,0.3"
        hard: false
        mask:
        - Opaque # WWDP allow shooting through windows
      fly-by: *flybyfixture
  - type: Ammo
  - type: StaminaDamageOnCollide
    damage: 20
  - type: Projectile
    impactEffect: BulletImpactEffectDisabler
    damage:
      types:
        Shock: 2
    soundHit:
      collection: WeakHit
    forceSound: true

- type: entity
  name : energy bolt
  id: BulletEnergyTurretBase
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Reflective
    reflective:
    - Energy
  - type: FlyBySound
    sound:
      collection: EnergyMiss
      params:
        volume: 5
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles_tg.rsi
    layers:
    - state: heavylaser
      shader: unshaded
  - type: Physics
  - type: Fixtures
    fixtures:
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.15,-0.3,0.15,0.3"
        hard: false
        mask:
        - Opaque
      fly-by: *flybyfixture
  - type: Ammo

- type: entity
  name : laser bolt
  id: BulletEnergyTurretLaser
  parent: BulletEnergyTurretBase
  categories: [ HideSpawnMenu ]
  components:
  - type: Ammo
    muzzleFlash: MuzzleFlashEffectHeavyLaser
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles_tg.rsi
    layers:
    - state: heavylaser
      shader: unshaded
  - type: Projectile
    impactEffect: BulletImpactEffectOrangeDisabler
    damage:
      types:
        Heat: 28

- type: entity
  name : disabler bolt
  id: BulletEnergyTurretDisabler
  parent: BulletEnergyTurretBase
  categories: [ HideSpawnMenu ]
  components:
  - type: Ammo
    muzzleFlash: MuzzleFlashEffectOmnilaser
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles_tg.rsi
    layers:
    - state: omnilaser
      shader: unshaded
  - type: StaminaDamageOnCollide
    damage: 30
  - type: Projectile
    impactEffect: BulletImpactEffectDisabler
    damage:
      types:
        Shock: 0
    soundHit:
      collection: WeakHit
    forceSound: true

- type: entity
  name: "блискавка з гармати Тесли"
  id: TeslaGunBullet
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: TimedDespawn
    lifetime: 5
  - type: Sprite
    sprite: Structures/Power/Generation/Tesla/energy_miniball.rsi
    layers:
    - state: tesla_projectile
      shader: unshaded
  - type: StaminaDamageOnCollide
    damage: 15
  - type: EmbeddableProjectile
  - type: Projectile
    deleteOnCollide: false
    soundHit:
      path: /Audio/Weapons/Guns/Hits/bullet_hit.ogg
  - type: LightningArcShooter
    arcDepth: 1
    maxLightningArc: 3
    shootMinInterval: 2.5
    shootMaxInterval: 4.5
    shootRange: 5
    lightningPrototype: Lightning
  - type: Electrified
    requirePower: false
  - type: Tag
    tags:
      - HideContextMenu
  - type: Ammo # Goobstation
    muzzleFlash: null

# DeltaV projectiles
# For whatever magical reasons, I cannot put those in the DeltaV folders without having errors.

# Because Hitscan lasers aren't fun to shoot in my opinion, we give the HoS pistol a special lethal laser that moves
- type: entity
  name: "енергетичний болт"
  id: BulletEnergyGunLaser
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Reflective
    reflective:
    - Energy
  - type: FlyBySound
    sound:
      collection: EnergyMiss
      params:
        volume: 5
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles_tg.rsi
    layers:
    - state: omnilaser_greyscale
      shader: unshaded
      color: red
  - type: Ammo
  - type: Physics
  - type: Fixtures
    fixtures:
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.2,-0.2,0.2,0.2"
        hard: false
        mask:
        - Opaque
      fly-by: *flybyfixture
  - type: Projectile
    impactEffect: BulletImpactEffectRedDisabler
    damage:
      types:
        Heat: 20 # Slightly more damage than the 17heat from the Captain's Hitscan lasgun
    soundHit:
      collection: MeatLaserImpact

- type: entity
  name: "лазерний болт"
  id: BulletLaser
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Reflective
    reflective:
    - Energy
  - type: FlyBySound
    sound:
      collection: EnergyMiss
      params:
        volume: 5
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles_tg.rsi
    layers:
    - state: heavylaser
      shader: unshaded
  - type: Physics
  - type: Fixtures
    fixtures:
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.15,-0.3,0.15,0.3"
        hard: false
        mask:
        - Opaque # WWDP allow shooting through windows
      fly-by: *flybyfixture
  - type: Ammo
  - type: Projectile
    impactEffect: BulletImpactEffectOrangeDisabler
    damage:
      types:
        Heat: 10
    soundHit:
      collection: WeakHit
    forceSound: true

- type: entity
  name: "лазерний шквал"
  id: BulletLaserSpread
  parent: BulletLaser
  categories: [ HideSpawnMenu ]
  components:
  - type: ProjectileSpread
    proto: BulletLaser
    count: 6 # 60 heat damage if you hit all your shots, but wide spread
    spread: 25

- type: entity
  name: "лазерний снаряд"
  id: BulletLaserSpreadSlug
  parent: BulletLaserHeavy
  categories: [ HideSpawnMenu ]
  components:
  - type: ProjectileSpread
    proto: BulletLaser
    count: 4 #60 heat damage, 20 blunt damage, and a high energy cost per shot.
    spread: 0

- type: entity
  name : heavy laser bolt
  id: BulletLaserHeavy
  parent: BulletLaser
  categories: [ HideSpawnMenu ]
  components:
  - type: Projectile
    damage:
      types:
        Heat: 15
        Blunt: 5

- type: entity
  name: "вузький лазерний промінь"
  id: BulletLaserHeavySpread
  parent: BulletLaser
  categories: [ HideSpawnMenu ]
  components:
  - type: ProjectileSpread
    proto: BulletLaser
    count: 3 # 45 heat damage if you hit all your shots, but narrower spread
    spread: 10

- type: entity
  name: "лазерний шквал дизейблера"
  id: BulletDisablerSmgSpread
  parent: BulletDisablerSmg
  categories: [ HideSpawnMenu ]
  components:
  - type: ProjectileSpread
    proto: BulletDisablerSmg
    count: 3 # bit stronger than a disabler if you hit your shots you goober, still not a 2 hit stun though
    spread: 9

- type: entity # Goobstation, put here so the X-01 stuff isnt spread everywhere.
  name: "магнум болт"
  id: BulletEnergyGunMagnum
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Reflective
    reflective:
    - Energy
  - type: FlyBySound
    sound:
      collection: EnergyMiss
      params:
        volume: 5
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles_tg.rsi
    layers:
    - state: heavylaser
      shader: unshaded
  - type: Ammo
  - type: Physics
  - type: Fixtures
    fixtures:
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.2,-0.2,0.2,0.2"
        hard: false
        mask:
        - Opaque
  - type: Projectile
    impactEffect: BulletImpactEffectRedDisabler
    damage:
      types:
        Blunt: 3
        Heat: 15
    soundHit:
      collection: MeatLaserImpact

- type: entity #Goobstation, put here so the X-01 stuff isnt spread everywhere.
  name: "іонний заряд"
  id: BulletEnergyGunIon
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Reflective
    reflective:
    - Energy
  - type: FlyBySound
    sound:
      collection: EnergyMiss
      params:
        volume: 5
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles_tg.rsi
    layers:
    - state: omnilaser_greyscale
      shader: unshaded
      color: blue
  - type: Ammo
  - type: Physics
  - type: Fixtures
    fixtures:
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.2,-0.2,0.2,0.2"
        hard: false
        mask:
        - Opaque
  - type: Projectile
    impactEffect: BulletImpactEffectDisabler
    damage:
      types:
        Shock: 20
    soundHit:
      collection: MeatLaserImpact
