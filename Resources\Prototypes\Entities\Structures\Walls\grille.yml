- type: entity
  id: Grille
  parent: BaseStructure
  name: "решітка"
  description: "Хиткий каркас із залізних стрижнів."
  components:
    - type: MeleeSound
      soundGroups:
        Brute:
          path:
            "/Audio/Weapons/grille_hit.ogg"
    - type: RCDDeconstructable
      cost: 6
      delay: 4
      fx: EffectRCDDeconstruct4
    - type: CanBuildWindowOnTop
    - type: Sprite
      drawdepth: Walls
      sprite: Structures/Walls/grille.rsi
      layers:
        - state: grille
        - state: electrified
          sprite: Effects/electricity.rsi
          map: ["enum.ElectrifiedLayers.Sparks"]
          shader: unshaded
          visible: false
    - type: Icon
      sprite: Structures/Walls/grille.rsi
      state: grille
    - type: Construction
      graph: Grille
      node: grille
      deconstructionTarget: start
    - type: Damageable
      damageContainer: StructuralInorganic
      damageModifierSet: PerforatedMetallic
    - type: PowerConsumer
      showInMonitor: false
    - type: Electrified
      requirePower: true
      noWindowInTile: true
      highVoltageNode: high
      mediumVoltageNode: medium
      lowVoltageNode: low
    - type: NodeContainer
      nodes:
        high:
          !type:CableDeviceNode
          nodeGroupID: HVPower
        medium:
          !type:CableDeviceNode
          nodeGroupID: MVPower
        low:
          !type:CableDeviceNode
          nodeGroupID: Apc
    - type: Physics
      bodyType: Static
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.5,-0.5,0.5,0.5"
          layer:
          - GlassLayer
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 130 #excess damage (nuke?). avoid computational cost of spawning entities.
          behaviors:
            - !type:DoActsBehavior
              acts: ["Destruction"]
        - trigger:
            !type:DamageTrigger
            damage: 100
          behaviors:
            - !type:ChangeConstructionNodeBehavior
              node: grilleBroken
            - !type:DoActsBehavior
              acts: ["Breakage"]
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.ElectrifiedVisuals.ShowSparks:
          enum.ElectrifiedLayers.Sparks:
            True: { visible: True }
            False: { visible: False }
    - type: AnimationPlayer

- type: entity
  id: ClockworkGrille
  parent: Grille
  name: "решітка з годинниковим механізмом"
  description: "хиткий каркас із залізних стрижнів, зібраний у традиційній ратварській манері."
  components:
    - type: Sprite
      drawdepth: Walls
      sprite: Structures/Walls/clockwork_grille.rsi
      state: ratvargrille
      layers:
        - state: ratvargrille
    - type: Icon
      sprite: Structures/Walls/clockwork_grille.rsi
      state: ratvargrille
    - type: Construction
      graph: ClockGrille
      node: clockGrille
      deconstructionTarget: start

- type: entity
  id: GrilleBroken
  parent: BaseStructure
  name: "решітка"
  description: "Хиткий каркас із залізних стрижнів. Він бачив кращі часи."
  components:
    - type: Sprite
      drawdepth: Walls
      sprite: Structures/Walls/grille.rsi
      state: grille_broken
    - type: Icon
      sprite: Structures/Walls/grille.rsi
      state: grille_broken
    - type: RCDDeconstructable
      cost: 6
      delay: 4
      fx: EffectRCDDeconstruct4
    - type: Construction
      graph: Grille
      node: grilleBroken
      deconstructionTarget: start
    - type: Fixtures # overwrite BaseStructure parent.
    - type: Physics
      bodyType: Static
      canCollide: false
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: FlimsyMetallic
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 10
          behaviors:
            - !type:ChangeConstructionNodeBehavior
              node: start
            - !type:DoActsBehavior
              acts: ["Destruction"]

- type: entity
  id: ClockworkGrilleBroken
  parent: GrilleBroken
  name: "решітка з годинниковим механізмом"
  description: "Хиткий каркас із залізних стрижнів, зібраний у традиційній ратварській манері. Він бачив кращі часи."
  components:
    - type: Sprite
      drawdepth: Walls
      sprite: Structures/Walls/clockwork_grille.rsi
      state: brokenratvargrille
    - type: Icon
      sprite: Structures/Walls/clockwork_grille.rsi
      state: brokenratvargrille
    - type: Construction
      graph: ClockGrille
      node: clockGrilleBroken

- type: entity
  id: GrilleDiagonal
  parent: Grille
  name: "діагональна решітка"
  components:
    - type: Sprite
      drawdepth: Walls
      sprite: Structures/Walls/grille.rsi
      layers:
        - state: grille_diagonal
        - state: electrified_diagonal
          sprite: Effects/electricity.rsi
          map: ["enum.ElectrifiedLayers.Sparks"]
          shader: unshaded
          visible: false
    - type: Icon
      sprite: Structures/Walls/grille.rsi
      state: grille_diagonal
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PolygonShape
              vertices:
              - "-0.5,-0.5"
              - "0.5,0.5"
              - "0.5,-0.5"
          mask:
          - FullTileMask
          layer:
          - GlassLayer # WWDP fix
    - type: Construction
      graph: GrilleDiagonal
      node: grilleDiagonal

- type: entity
  id: ClockworkGrilleDiagonal
  parent: ClockworkGrille
  name: "діагональна решітка з годинниковим механізмом"
  components:
    - type: Sprite
      drawdepth: Walls
      sprite: Structures/Walls/clockwork_grille.rsi
      layers:
        - state: ratvargrille_diagonal
        - state: electrified_diagonal
          sprite: Effects/electricity.rsi
          map: ["enum.ElectrifiedLayers.Sparks"]
          shader: unshaded
          visible: false
    - type: Icon
      sprite: Structures/Walls/clockwork_grille.rsi
      state: ratvargrille_diagonal
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PolygonShape
              vertices:
              - "-0.5,-0.5"
              - "0.5,0.5"
              - "0.5,-0.5"
          mask:
          - FullTileMask
          layer:
          - GlassLayer # WWDP fix
    - type: Construction
      graph: GrilleDiagonal
      node: clockworkGrilleDiagonal

