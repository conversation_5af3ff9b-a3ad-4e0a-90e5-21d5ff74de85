- type: entity
  name: "телекристал"
  parent: BaseItem
  id: Telecrystal
  suffix: 20 TC
  description: "Здається, він пульсує підозріло привабливими енергіями."
  components:
  - type: Sprite
    sprite: Objects/Specific/Syndicate/telecrystal.rsi
    state: telecrystal
  - type: Item
    sprite: Objects/Specific/Syndicate/telecrystal.rsi
    size: Tiny
  - type: Stack
    count: 20
    stackType: Telecrystal
  - type: StaticPrice
    price: 0
  - type: StackPrice
    price: 200
  - type: Currency
    price:
      Telecrystal: 1

- type: entity
  parent: Telecrystal
  id: Telecrystal1
  suffix: 1 TC
  components:
  - type: Stack
    count: 1

- type: entity
  parent: Telecrystal
  id: Telecrystal5
  suffix: 5 TC
  components:
  - type: Stack
    count: 5

- type: entity
  parent: Telecrystal
  id: Telecrystal10
  suffix: 10 TC
  components:
  - type: Stack
    count: 10

# Uplinks
- type: entity
  parent: [ BaseItem, StorePresetUplink ]
  id: BaseUplinkRadio
  name: "синдикатський канал зв'язку"
  description: "Підозріло виглядає старий радіоприймач..."
  suffix: Empty
  components:
  - type: Sprite
    sprite: Objects/Devices/communication.rsi
    layers:
    - state: radio
  - type: Item
    sprite: Objects/Devices/communication.rsi
    heldPrefix: radio
  - type: UserInterface
    interfaces:
      enum.StoreUiKey.Key:
        type: StoreBoundUserInterface
  - type: ActivatableUI
    key: enum.StoreUiKey.Key
  - type: Store
    balance:
      Telecrystal: 0

- type: entity
  parent: BaseUplinkRadio
  id: BaseUplinkRadio20TC
  suffix: 20 TC
  components:
  - type: Store
    balance:
      Telecrystal: 100

- type: entity
  parent: BaseUplinkRadio
  id: BaseUplinkRadio25TC
  suffix: 25 TC
  components:
  - type: Store
    balance:
      Telecrystal: 125

#this uplink MUST be used for nukeops, as it has the tag for filtering the listing.
- type: entity
  parent: BaseUplinkRadio
  id: BaseUplinkRadio40TC
  suffix: 40 TC, NukeOps
  components:
  - type: Store
    balance:
      Telecrystal: 250 # 5x TC, +cost of a tacsuit they no longer spawn with.
  - type: Tag
    tags:
    - NukeOpsUplink

- type: entity
  parent: BaseUplinkRadio
  id: BaseUplinkRadio60TC
  suffix: 60 TC, LoneOps
  components:
  - type: Store
    balance:
      Telecrystal: 350 # 5x TC, +cost of a tacsuit they no longer spawn with.
  - type: Tag
    tags:
    - NukeOpsUplink

- type: entity
  parent: BaseUplinkRadio
  id: BaseUplinkRadioDebug
  suffix: DEBUG
  components:
  - type: Store
    balance:
      Telecrystal: 99999
