# Contraband
- type: entity
  parent: PosterBase
  id: PosterContrabandSaucerNumberOne
  name: "Тарілка №1!"
  description: "З усіх станцій в юрисдикції Н'яноТрейзен, Тарілка отримала оцінку 1 з 5! Вітаємо!"
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/Vordenburg.rsi
    state: poster_saucer1

- type: entity
  parent: PosterBase
  id: PosterContrabandBreadLies
  name: "Хліб Бреше"
  description: "Вам збрехали. Немає ніякого \"Великого Хліба\". Його не існує. Будь ласка, перестаньте його шукати."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/Huvy.rsi
    state: BreadLies

- type: entity
  parent: PosterBase
  id: PosterContrabandAyaya
  name: "Аяяяяяяяяяяяяяяяяяяяяяяя"
  description: "Ай-яй-яй."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/misc.rsi
    state: poster_ayaya

# Legit
- type: entity
  parent: PosterBase
  id: PosterLegitDejaVu
  name: "Зона Дежавю"
  description: "Ви тут вперше. Якщо ця станція здається вам знайомою, негайно повідомте про це співробітника НаноТрейзен."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/Vordenburg.rsi
    state: poster_dejavu

- type: entity
  parent: PosterBase
  id: PosterLegitDontPanic
  name: "Без Паніки"
  description: "Якщо щось і заспокоювало мене від паніки, так це зелений згусток, що плавав у просторі з парою рук, блискучим червоним язиком і великими круглими зубами."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/Vordenburg.rsi
    state: poster_dontpanic

- type: entity
  parent: PosterBase
  id: PosterLegitBarDrinks
  name: "Барна Афіша"
  description: "Бармен завжди готовий вислухати ваші проблеми і одночасно налити вам випити. Заходьте в гості!"
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/Huvy.rsi
    state: BAR

- type: entity
  parent: PosterBase
  id: PosterLegitBotanyFood
  name: "Кошик з Фруктами"
  description: "Ботаніка завжди наполегливо працює! Завітайте до нас і насолоджуйтеся найкращим, що вони можуть запропонувати!"
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/Huvy.rsi
    state: BotanyFood

- type: entity
  parent: PosterBase
  id: PosterLegitHotDonkExplosion
  name: "Донк!"
  description: "Скуштуйте вибух смаку з будь-яким з наших жирних і шкідливих для здоров'я продуктів №1, щоб насолодитися! ДОНК!"
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/Huvy.rsi
    state: DONK

- type: entity
  parent: PosterBase
  id: PosterLegitEatMeat
  name: "Їж М'ясо!"
  description: "Кому потрібні фрукти чи овочі? ЇЖТЕ М'ЯСО! Нарощуй м'язи і виходь на арену, щоб показати їм свої досягнення!"
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/Huvy.rsi
    state: EATMEAT

- type: entity
  parent: PosterBase
  id: PosterLegitMoreBread
  name: "Більше Хліба!"
  description: "Хліба не вистачає! ЗРОБІТЬ БІЛЬШЕ!"
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/Huvy.rsi
    state: MoreBread

- type: entity
  parent: PosterBase
  id: PosterLegitPieSlice
  name: "Постер Пирога"
  description: "Пироги - це корисно і смачно! Попросіть шеф-кухаря почати їх готувати."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/Huvy.rsi
    state: PIE

- type: entity
  parent: PosterBase
  id: PosterLegitPizzaHope
  name: "Надія - Піца"
  description: "У найтемніші часи є лише одна надія - ПІЦЦА! Попросіть місцевого офіцера з логістики замовити екстрену доставку піци прямо зараз!" # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/Huvy.rsi
    state: PIZZA

- type: entity
  parent: PosterBase
  id: PosterLegitMedicate
  name: "Приймайте Ліки"
  description: "Можливі побічні ефекти можуть включати нудота, розлад шлунку, запалення, набряк обличчя/горла, висип, лихоманка, головний біль, запаморочення, блювання та/або діарея, легкі синці, судоми, нестримна позіхання, загальне відчуття дискомфорту або хвороби, кропив'янка втрата голосу, кровоточивість ясен та/або очних яблук, втрата апетиту, підвищений апетит, раптовий приступ вибухового плачу, рентгенівське бачення, невиразна мова, імпровізована левітація, нетримання сечі, раптовий неконтрольований спалах йодлювання, безсоння, нарколепсія, проказа, спонтанний ріст нових кінцівок/геніталій, нестримний ріст волосся на спині, втрата ваги, збільшення ваги, зморщування, дальтонізм, оніміння прямої кишки, загострена здатність відчувати кольори, невгамовна жага крові, неспокійні нігті, світлочутливість, примус до носіння шкільної форми, цинга, прагнення до життя найманця, несамовита тяга до козячої крові, повстанське бажання розвинути залежність від азартних ігор, свербіж у задньому проході. Зверніться до лікаря за медичною консультацією щодо цих або будь-яких інших побічних ефектів, які ви можете відчути." #credit Chris Zappa
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/Velcroboy.rsi
    state: poster_medicate

- type: entity
  parent: PosterBase
  id: PosterLegitNoTouching
  name: "Не Чіпати"
  description: "Не Чіпати!"
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/Velcroboy.rsi
    state: poster_notouching

- type: entity
  parent: PosterBase
  id: PosterLegitChknDnnr
  name: "Переможець Переможець Куряча вечеря"
  description: "Реклама хрусткої смаженої курки."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/CerebralError.rsi
    state: poster_ChknDnnr

- type: entity
  parent: PosterBase
  id: PosterLegitShoukou
  name: "Плакат Shōkō"
  description: "Кумедні символи, які можна прочитати як Shōkō або Xiaogang, залежно від того, кого ви запитаєте."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/Hyenh.rsi
    state: shoukouposter

- type: entity
  parent: PosterBase
  id: PosterLegitCornzza
  name: "Кукурудзяна Скоринка!"
  description: "НОВА Кукуцца! Кукурудзяна піца зі скоринкою з кукурудзяних качанів, яка змусить вас запитати: \"Навіщо вона існує?\"."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/rosysyntax.rsi
    state: poster_corncobpizza

# Station Maps
- type: entity
  parent: PosterBase
  id: PosterMapArena
  name: "Мапа Arena"
  description: "Карта станції \"Arena\"."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/Velcroboy.rsi
    state: arenamap

- type: entity
  parent: PosterBase
  id: PosterMapGlacier
  name: "Мапа Glacier"
  description: "Карта станції \"Glacier\"."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/Hyenh.rsi
    state: glaciermap

- type: entity
  parent: PosterBase
  id: PosterMapShoukou
  name: "Мапа Shōkō"
  description: "Shōkō no mappu desu."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/Hyenh.rsi
    state: shoukoumap

- type: entity
  parent: PosterBase
  id: PosterMapNorthway
  name: "Мапа Northway"
  description: "Карта космічного корабля \"Нортвей\"."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/Hyenh.rsi
    state: northwaymap

# SAFETY MOTH
# Actual original art credit to AspEv
# Some posters are in the upstream posters.yml

- type: entity
  parent: PosterBase
  id: PosterContrabandSafetyMothSyndie
  name: "Syndie Moth - Ядерна операція"
  description: "Syndie Moth™ радить глядачеві тримати ядерний аутентифікаційний диск незахищеним. \"Мир ніколи не був варіантом!\""
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/safetymoth.rsi
    state: safetymoth_0

- type: entity
  parent: PosterBase
  id: PosterLegitSafetyMothPoisoning
  name: "Безпечний метелик - отруєння"
  description: "Safety Moth™ застерігає глядача не отруїтися пончиками на станції."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/safetymoth.rsi
    state: safetymoth_1

- type: entity
  parent: PosterBase
  id: PosterLegitSafetyMothBoH
  name: "Safety Moth - сумка для утримання"
  description: "Safety Moth™ інформує глядача про небезпеку мішків для зберігання. \"Пам'ятайте! Пакети для зберігання можуть бути гарними, але вони також досить небезпечні! Ніколи не кладіть один в інший!\""
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/safetymoth.rsi
    state: safetymoth_2

- type: entity
  parent: PosterBase
  id: PosterLegitSafetyMothFires
  name: "Метелик безпеки - Пожежі"
  description: "Safety Moth™ сприяє безпечному поводженню з плазмою та дозволяє тримати протипожежне обладнання під рукою."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/safetymoth.rsi
    state: safetymoth_4

- type: entity
  parent: PosterBase
  id: PosterLegitSafetyMothPills
  name: "Безпечна моль - флакони для таблеток"
  description: "Safety Moth™ інформує глядача про те, що залишення таблеток на столі без нагляду може призвести до непередбачуваних наслідків. \"Голодні тварини люблять їсти все підряд! Зберігайте свої пігулки в безпеці в коробочках та пляшечках!\""
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/safetymoth.rsi
    state: safetymoth_9

- type: entity
  parent: PosterBase
  id: PosterLegitSafetyMothGlimmer
  name: "Safety Moth - Заходи безпеки при мерехтінні"
  description: "Safety Moth™ закликає глядачів вдягти ізоляційні засоби і сховатися в шафках, коли мерехтіння досягне критичного рівня. Евакуація може бути кращою стратегією."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Structures/Wallmounts/Posters/safetymoth.rsi
    state: safetymoth_11
