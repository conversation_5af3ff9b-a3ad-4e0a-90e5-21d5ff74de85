# TODO move all of this to tiles once tile smoothing is supported
- type: entity
  id: CarpetBase
  parent: BaseStructure
  name: "килим"
  description: "Чудова доріжка для прогулянок."
  abstract: true
  components:
  - type: Sprite
    drawdepth: FloorTiles
  - type: Icon
    state: full
  - type: IconSmooth
    key: full
    base: carpet_
  - type: Tag
    tags: [ Carpet ]
  - type: Physics
    canCollide: false
  - type: Fixtures
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 10
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
  - type: Butcherable
    butcheringType: Knife
    butcherDelay: 2
    spawned:
    - id: MaterialCloth1
      amount: 1

- type: entity
  id: Carpet
  parent: CarpetBase
  suffix: Red
  components:
  - type: Sprite
    sprite: Structures/Furniture/Carpets/red_carpet.rsi
  - type: Icon
    sprite: Structures/Furniture/Carpets/red_carpet.rsi
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors: #excess damage (nuke?). avoid computational cost of spawning entities.
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 10
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
          - !type:SpawnEntitiesBehavior
            spawn:
              FloorCarpetItemRed:
                min: 1
                max: 1

- type: entity
  id: CarpetBlack
  parent: CarpetBase
  suffix: Black
  components:
  - type: Clickable
  - type: Sprite
    sprite: Structures/Furniture/Carpets/black_carpet.rsi
  - type: Icon
    sprite: Structures/Furniture/Carpets/black_carpet.rsi
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors: #excess damage (nuke?). avoid computational cost of spawning entities.
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 10
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
          - !type:SpawnEntitiesBehavior
            spawn:
              FloorCarpetItemBlack:
                min: 1
                max: 1

- type: entity
  id: CarpetPink
  parent: CarpetBase
  suffix: Pink
  components:
  - type: Clickable
  - type: Sprite
    sprite: Structures/Furniture/Carpets/pink_carpet.rsi
  - type: Icon
    sprite: Structures/Furniture/Carpets/pink_carpet.rsi
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors: #excess damage (nuke?). avoid computational cost of spawning entities.
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 10
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
          - !type:SpawnEntitiesBehavior
            spawn:
              FloorCarpetItemPink:
                min: 1
                max: 1

- type: entity
  id: CarpetBlue
  parent: CarpetBase
  suffix: Blue
  components:
  - type: Clickable
  - type: Sprite
    sprite: Structures/Furniture/Carpets/blue_carpet.rsi
  - type: Icon
    sprite: Structures/Furniture/Carpets/blue_carpet.rsi
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors: #excess damage (nuke?). avoid computational cost of spawning entities.
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 10
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
          - !type:SpawnEntitiesBehavior
            spawn:
              FloorCarpetItemBlue:
                min: 1
                max: 1

- type: entity
  id: CarpetGreen
  parent: CarpetBase
  suffix: Green
  components:
  - type: Clickable
  - type: Sprite
    sprite: Structures/Furniture/Carpets/green_carpet.rsi
  - type: Icon
    sprite: Structures/Furniture/Carpets/green_carpet.rsi
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors: #excess damage (nuke?). avoid computational cost of spawning entities.
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 10
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
          - !type:SpawnEntitiesBehavior
            spawn:
              FloorCarpetItemGreen:
                min: 1
                max: 1

- type: entity
  id: CarpetOrange
  parent: CarpetBase
  suffix: Orange
  components:
  - type: Clickable
  - type: Sprite
    sprite: Structures/Furniture/Carpets/orange_carpet.rsi
  - type: Icon
    sprite: Structures/Furniture/Carpets/orange_carpet.rsi
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors: #excess damage (nuke?). avoid computational cost of spawning entities.
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 10
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
          - !type:SpawnEntitiesBehavior
            spawn:
              FloorCarpetItemOrange:
                min: 1
                max: 1

- type: entity
  id: CarpetSBlue
  parent: CarpetBase
  suffix: Sky Blue
  components:
  - type: Clickable
  - type: Sprite
    sprite: Structures/Furniture/Carpets/skyblue_carpet.rsi
  - type: Icon
    sprite: Structures/Furniture/Carpets/skyblue_carpet.rsi
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors: #excess damage (nuke?). avoid computational cost of spawning entities.
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 10
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
          - !type:SpawnEntitiesBehavior
            spawn:
              FloorCarpetItemSkyBlue:
                min: 1
                max: 1

- type: entity
  id: CarpetPurple
  parent: CarpetBase
  suffix: Purple
  components:
  - type: Clickable
  - type: Sprite
    sprite: Structures/Furniture/Carpets/purple_carpet.rsi
  - type: Icon
    sprite: Structures/Furniture/Carpets/purple_carpet.rsi
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors: #excess damage (nuke?). avoid computational cost of spawning entities.
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 10
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
          - !type:SpawnEntitiesBehavior
            spawn:
              FloorCarpetItemPurple:
                min: 1
                max: 1

- type: entity
  id: CarpetCyan
  parent: CarpetBase
  suffix: Cyan
  components:
  - type: Clickable
  - type: Sprite
    sprite: Structures/Furniture/Carpets/cyan_carpet.rsi
  - type: Icon
    sprite: Structures/Furniture/Carpets/cyan_carpet.rsi
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors: #excess damage (nuke?). avoid computational cost of spawning entities.
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 10
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
          - !type:SpawnEntitiesBehavior
            spawn:
              FloorCarpetItemCyan:
                min: 1
                max: 1

- type: entity
  id: CarpetWhite
  parent: CarpetBase
  suffix: White
  components:
  - type: Clickable
  - type: Sprite
    sprite: Structures/Furniture/Carpets/white_carpet.rsi
  - type: Icon
    sprite: Structures/Furniture/Carpets/white_carpet.rsi
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors: #excess damage (nuke?). avoid computational cost of spawning entities.
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 10
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
          - !type:SpawnEntitiesBehavior
            spawn:
              FloorCarpetItemWhite:
                min: 1
                max: 1

# TODO nuke this once tiles support rotating sprites
- type: entity
  id: CarpetChapel
  parent: BaseStructure
  name: "килим каплиці"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Carpets/chapel_carpet.rsi
    state: chapel
    drawdepth: FloorTiles
  - type: Icon
    sprite: Structures/Furniture/Carpets/chapel_carpet.rsi
    state: chapel
  - type: Tag
    tags: [ Carpet ]
  - type: Physics
    canCollide: false
  - type: Fixtures
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 10
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
