﻿# :TODO: Add the destroyed versions of these as a destruction spawn.

- type: entity
  parent: BaseStructure
  id: StorageTankBase
  name: "резервуар для зберігання"
  description: "Резервуар для зберігання рідин."
  abstract: true
  components:
  - type: Sprite
    noRot: true
  - type: InteractionOutline
  - type: Physics
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTypeTrigger
        damageType: Heat
        damage: 5
      behaviors:
      - !type:SolutionExplosionBehavior
        solution: tank
    - trigger:
        !type:DamageTypeTrigger
        damageType: Piercing
        damage: 5
      behaviors:
      - !type:SolutionExplosionBehavior
        solution: tank
    - trigger:
        !type:DamageTrigger
        damage: 10
      behaviors:
      - !type:SpillBehavior
        solution: tank
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: SolutionContainerManager
    solutions:
      tank:
        maxVol: 1500
  - type: DrainableSolution
    solution: tank
  - type: ReagentTank
  - type: Transform
    noRot: true

# In use
- type: entity
  id: StorageTankWide
  parent: StorageTankBase
  name: "паливний бак"
  description: "Паливний бак. Використовується для зберігання великої кількості пального."
  suffix: Empty
  components:
  - type: Sprite
    sprite: Structures/Storage/tanksx64.rsi
    layers:
      - state: chemical_container
      # - state: chemical_container
        # map: ["enum.SolutionContainerLayers.Fill"]
        # visible: false
  - type: Appearance
  # - type: SolutionContainerVisuals
    # maxFillLevels: 3
    # fillBaseName: fueltank-2-
  - type: ExaminableSolution
    solution: tank
  - type: ReagentTank
    tankType: Fuel
  - type: DamageOnToolInteract
    tools: Welding
    weldingDamage:
      types:
        Heat: 10
  - type: PacifismDangerousAttack
  - type: Explosive
    explosionType: Default
    totalIntensity: 120 # ~ 5 tile radius
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.9,-0.5,0.9,0.2"
        density: 155
        mask:
        - MachineMask
        layer:
        - WallLayer

- type: entity
  parent: StorageTankWide
  id: StorageTankWideFullFuel
  suffix: Full
  components:
  - type: SolutionContainerManager
    solutions:
      tank:
        reagents:
        - ReagentId: WeldingFuel
          Quantity: 2000


- type: entity
  parent: StorageTankWide
  id: StorageTank2
  suffix: Empty
  components:
  - type: Sprite
    sprite: Structures/Storage/tanksx64.rsi
    layers:
      - state: largetank_chemical
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.9,-0.7,-0.1,0.4"
        density: 155
        mask:
        - MachineMask
        layer:
        - WallLayer

- type: entity
  id: StorageTankFullFuel
  parent: StorageTank2
  suffix: Full
  components:
  - type: SolutionContainerManager
    solutions:
      tank:
        reagents:
        - ReagentId: WeldingFuel
          Quantity: 1500

- type: entity
  id: StorageTankHuge
  parent: StorageTankWide
  suffix: Empty
  components:
  - type: Sprite
    sprite: Structures/Storage/tanksx64.rsi
    layers:
      - state: largetank_chemical_huge
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.7,-0.7,0.2,0.6"
        density: 155
        mask:
        - MachineMask
        layer:
        - WallLayer

- type: entity
  id: StorageTankHugeFullFuel
  parent: StorageTankHuge
  suffix: Full
  components:
  - type: SolutionContainerManager
    solutions:
      tank:
        reagents:
        - ReagentId: WeldingFuel
          Quantity: 2000
