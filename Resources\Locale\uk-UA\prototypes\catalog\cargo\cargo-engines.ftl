ent-EngineAmeShielding = { ent-CrateEngineeringAMEShielding }
    .desc = { ent-CrateEngineeringAMEShielding.desc }

ent-EngineAmeJar = { ent-CrateEngineeringAMEJar }
    .desc = { ent-CrateEngineeringAMEJar.desc }

ent-EngineAmeControl = { ent-CrateEngineeringAMEControl }
    .desc = { ent-CrateEngineeringAMEControl.desc }

ent-EngineSingularityGenerator = { ent-CrateEngineeringSingularityGenerator }
    .desc = { ent-CrateEngineeringSingularityGenerator.desc }

ent-EngineSingularityContainment = { ent-CrateEngineeringSingularityContainment }
    .desc = { ent-CrateEngineeringSingularityContainment.desc }

ent-EngineSingularityCollector = { ent-CrateEngineeringSingularityCollector }
    .desc = { ent-CrateEngineeringSingularityCollector.desc }

ent-EngineParticleAccelerator = { ent-CrateEngineeringParticleAccelerator }
    .desc = { ent-CrateEngineeringParticleAccelerator.desc }

ent-EngineSolar = { ent-CrateEngineeringSolar }
    .desc = { ent-CrateEngineeringSolar.desc }
