- type: entity
  id: Morgue
  name: "морг"
  description: "Використовується для зберігання тіл, поки їх не заберуть. Включає високотехнологічну систему оповіщення про хибні спрацьовування!"
  placement:
    mode: SnapgridCenter
  components:
  - type: Pullable
  - type: Anchorable
  - type: Sprite
    sprite: Structures/Storage/morgue.rsi
    layers:
    - state: morgue_closed
      map: ["enum.MorgueVisualLayers.Base"]
    - state: morgue_tray
      offset: 0, -1
      visible: false
      map: ["enum.StorageVisualLayers.Door"]
    - state: morgue_nomob_light
      visible: false
      map: ["enum.MorgueVisualLayers.Light"]
      shader: unshaded
  - type: Clickable
  - type: InteractionOutline
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.5,-0.5,0.5,0.5"
        density: 1000
        mask:
        - MachineMask
        layer:
        - HalfWallLayer
  - type: EntityStorage
    isCollidableWhenOpen: true
    showContents: false
    capacity: 1
    enteringOffset: 0, -1
    closeSound:
      path: /Audio/Items/deconstruct.ogg
    openSound:
      path: /Audio/Items/deconstruct.ogg
  - type: EntityStorageLayingDownOverride
  - type: Morgue
  - type: ContainerContainer
    containers:
      entity_storage: !type:Container
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.StorageVisuals.Open:
        # basic open.close layer:
        enum.MorgueVisualLayers.Base:
          True: { state: morgue_open}
          False: { state: morgue_closed}
        # show/hide morgue tray:
        enum.StorageVisualLayers.Door:
          True: { visible: true}
          False: { visible: false}
      enum.MorgueVisuals.Contents:
        # toggle contents light indicator:
        enum.MorgueVisualLayers.Light:
          Empty: {visible: false}
          HasContents: {visible: true, state: morgue_nomob_light}
          HasMob: {visible: true, state: morgue_nosoul_light}
          HasSoul: {visible: true, state: morgue_soul_light}
  - type: Transform
    anchored: true
  - type: AntiRottingContainer
  - type: StaticPrice
    price: 200

- type: entity
  id: Crematorium
  name: "крематорій"
  description: "Спалювач людей. Добре працює на вечірніх барбекю."
  placement:
    mode: SnapgridCenter
  components:
  - type: Sprite
    sprite: Structures/Storage/morgue.rsi
    layers:
    - state: crema_closed
      map: ["enum.CrematoriumVisualLayers.Base", "enum.StorageVisualLayers.Base"]
    - state: crema_tray
      offset: 0, -1
      map: ["enum.StorageVisualLayers.Door"]
    - state: crema_contents_light
      visible: false
      map: ["enum.CrematoriumVisualLayers.LightContent"]
      shader: unshaded
    - state: crema_active_light
      visible: false
      map: ["enum.CrematoriumVisualLayers.LightBurning"]
      shader: unshaded
  - type: Clickable
  - type: InteractionOutline
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.5,-0.5,0.5,0.5"
        density: 190
        mask:
        - MachineMask
        layer:
        - MachineLayer
  - type: EntityStorage
    isCollidableWhenOpen: true
    showContents: false
    capacity: 1
    enteringOffset: 0, -1
    closeSound:
      path: /Audio/Items/deconstruct.ogg
    openSound:
      path: /Audio/Items/deconstruct.ogg
  - type: EntityStorageLayingDownOverride
  - type: Crematorium
  - type: ContainerContainer
    containers:
      entity_storage: !type:Container
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: crema_closed
    stateBaseOpen: crema_open
    stateDoorOpen: crema_tray
  - type: GenericVisualizer
    visuals:
      enum.CrematoriumVisuals.Burning:
        enum.CrematoriumVisualLayers.LightBurning:
          True: { visible: true }
          False: { visible: false }
      enum.StorageVisuals.HasContents:
        enum.CrematoriumVisualLayers.LightContent:
          True: { visible: true }
          False: { visible: false }
  - type: Transform
    anchored: true
