using Content.Client.Lathe;
using Content.Client.Research;
using Content.Client.Research.UI;
using Content.Shared._Goobstation.Research.Common;
using Content.Client._Goobstation.Research.UI;
using Content.Shared.Research.Prototypes;
using Robust.Client.AutoGenerated;
using Robust.Client.GameObjects;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;

namespace Content.Client._Goobstation.Research.UI;

[GenerateTypedNameReferences]
public sealed partial class FancyTechnologyInfoPanel : Control
{
    [Dependency] private readonly IEntityManager _ent = default!;
    [Dependency] private readonly IPrototypeManager _proto = default!;

    public TechnologyPrototype Prototype;
    public Action<TechnologyPrototype>? BuyAction;
    public FancyTechnologyInfoPanel(TechnologyPrototype proto, bool hasAccess, ResearchAvailability availability, SpriteSystem sprite)
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        var lathe = _ent.System<LatheSystem>();
        var research = _ent.System<ResearchSystem>();
        Prototype = proto;

        TechnologyNameLabel.Text = Loc.GetString(proto.Name);
        DisciplineTexture.Texture = sprite.Frame0(_proto.Index(proto.Discipline).Icon);
        TechnologyTexture.Texture = sprite.Frame0(proto.Icon);

        InitializePrerequisites(proto, research, sprite);

        InitializeRecipeUnlocks(proto, lathe, sprite);

        ResearchButton.ToolTip = !hasAccess
            ? Loc.GetString("research-console-no-access-popup")
            : null;

        ResearchButton.Text = availability == ResearchAvailability.Researched
            ? Loc.GetString("research-console-menu-server-researched-button")
            : ResearchButton.Text;

        Color? color = availability switch
        {
            ResearchAvailability.Researched => Color.LimeGreen,
            ResearchAvailability.PrereqsMet => Color.Crimson,
            ResearchAvailability.Unavailable => Color.Crimson,
            _ => null
        };
        TechnologyCostLabel.SetMessage(
            Loc.GetString("research-console-tech-cost-label", ("cost", proto.Cost)),
            defaultColor: color
        );

        ResearchButton.Disabled = !hasAccess || availability != ResearchAvailability.Available;
        ResearchButton.OnPressed += Bought;
    }

    private void InitializePrerequisites(TechnologyPrototype proto, ResearchSystem research, SpriteSystem sprite)
    {
        NoPrereqLabel.Visible = proto.TechnologyPrerequisites.Count == 0;
        PrereqsContainer.Visible = !NoPrereqLabel.Visible;

        RequiredTechContainer.RemoveAllChildren();
        foreach (var techId in proto.TechnologyPrerequisites)
        {
            var tech = _proto.Index(techId);
            var description = research.GetTechnologyDescription(tech, true, false, true);
            RequiredTechContainer.AddChild(new MiniTechnologyCardControl(tech, _proto, sprite, description));
        }
    }

    private void InitializeRecipeUnlocks(TechnologyPrototype proto, LatheSystem lathe, SpriteSystem sprite)
    {
        UnlocksContainer.RemoveAllChildren();
        foreach (var recipeId in proto.RecipeUnlocks)
        {
            var recipe = _proto.Index(recipeId);
            UnlocksContainer.AddChild(new MiniRecipeCardControl(proto, recipe, _proto, sprite, lathe));
        }
    }

    protected override void ExitedTree()
    {
        base.ExitedTree();

        ResearchButton.OnPressed -= Bought;
    }
    private void Bought(BaseButton.ButtonEventArgs args)
    {
        BuyAction?.Invoke(Prototype);
    }

}
