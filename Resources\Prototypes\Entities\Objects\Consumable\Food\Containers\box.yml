# Donut

# There is a newer, better version of the donutbox from tgstation included in the
# donut.rsi. The reason it isn't implemented is it requires a StackVisuals offsetting
# the layer sprite by a couple pixels everytime a new donut is added. It also requires
# an alpha color which -Y- said he would implement.

- type: entity
  parent: [ BoxCardboard, BaseBagOpenClose ]
  id: FoodBoxDonut
  name: "коробка для пончиків"
  description: "Пончики."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/Baked/donut.rsi
    state: box
    layers:
    - state: box
    - state: box-open
      map: ["openLayer"]
      visible: false
    - state: box1
      map: ["box1"]
      visible: false
    - state: pink-box2
      map: ["pink-box2"]
      visible: false
    - state: box3
      map: ["box3"]
      visible: false
    - state: pink-box4
      map: ["pink-box4"]
      visible: false
    - state: box5
      map: ["box5"]
      visible: false
    - state: pink-box6
      map: ["pink-box6"]
      visible: false
  - type: Storage
    grid:
    - 0,0,5,0
    whitelist:
      tags:
      - Donut
  - type: Item
    sprite: Objects/Consumable/Food/Baked/donut.rsi
    size: Small
    heldPrefix: box
  - type: StorageFill
    contents:
    - id: FoodDonutPink
      amount: 3
    - id: FoodDonutPlain
      amount: 3
  - type: ItemCounter
    count:
      tags: [Donut]
    composite: true
    layerStates:
    - box1
    - pink-box2
    - box3
    - pink-box4
    - box5
    - pink-box6
  - type: Appearance

# Egg

- type: entity
  parent: [ BoxCardboard, BaseBagOpenClose ]
  id: FoodContainerEgg
  name: "коробка для яєць"
  description: "Не кидай їх!"
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/egg.rsi
    state: box-closed
    layers:
    - state: box-closed
    - state: box-open
      map: ["openLayer"]
      visible: false
    - state: box1
      map: ["box1"]
      visible: false
    - state: box2
      map: ["box2"]
      visible: false
    - state: box3
      map: ["box3"]
      visible: false
    - state: box4
      map: ["box4"]
      visible: false
    - state: box5
      map: ["box5"]
      visible: false
    - state: box6
      map: ["box6"]
      visible: false
    - state: box7
      map: ["box7"]
      visible: false
    - state: box8
      map: ["box8"]
      visible: false
    - state: box9
      map: ["box9"]
      visible: false
    - state: box10
      map: ["box10"]
      visible: false
    - state: box11
      map: ["box11"]
      visible: false
    - state: box12
      map: ["box12"]
      visible: false
  - type: Storage
    grid:
    - 0,0,5,1
    whitelist:
      tags:
      - Egg
  - type: Item
    sprite: Objects/Consumable/Food/egg.rsi
    size: Small
  - type: StorageFill
    contents:
    - id: FoodEgg
      amount: 12
  - type: ItemCounter
    count:
      tags: [Egg]
    composite: true
    layerStates:
    - box1
    - box2
    - box3
    - box4
    - box5
    - box6
    - box7
    - box8
    - box9
    - box10
    - box11
    - box12
  - type: Appearance
  # Someday...
  # - type: DamageOnLand
  # - type: DamageOtherOnHit
  # - type: Damageable
  # - type: Destructible
  #   thresholds:
  #   - trigger:
  #       !type:DamageTrigger
  #       damage: 10
  #     behaviors:
  #     - !type:PlaySoundBehavior
  #       collection: desecration
  #     - !type:SpawnEntitiesBehavior
  #       spawn:
  #         EggBoxBroken:
  #           min: 1
  #           max: 1
  #         PuddleEgg:
  #           min: 1
  #           max: 12
  #     - !type:DoActsBehavior
  #       acts: [ "Destruction" ]

- type: entity
  parent: FoodContainerEgg
  id: EggBoxBroken
  suffix: Broken
  components:
  - type: StorageFill
    contents:
    - id: Eggshells
      amount: 12
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 10

# Pizza

# This - is pretty hacky. It works, but you can't have stuff like messy pizza box
# layers after a pizza has been in. Also pizza box stacking, but this is impossible
# Since you could open pizza boxes in the stack.

- type: entity
  parent: BoxCardboard
  id: FoodBoxPizza
  name: "коробка з-під піци"
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/Baked/pizza.rsi
    drawdepth: SmallObjects
    layers:
    - state: box
      map: ["enum.StorageVisualLayers.Base"]
    - state: box-open
      map: ["enum.StorageVisualLayers.Door"]
  # TODO make these entitystorage again + placeablesurface after entity storage ECS gets merged.
  - type: Storage
    grid:
    - 0,0,1,1
    maxItemSize: Normal
  - type: Item
    sprite: Objects/Consumable/Food/Baked/pizza.rsi
    heldPrefix: box
    shape:
    - 0,0,1,1
  - type: Appearance
  - type: EntityStorageVisuals
    stateDoorOpen: box-open
    stateDoorClosed: box
  - type: StaticPrice
    price: 0

- type: entity
  name: "коробка з-під піци"
  parent: FoodBoxPizza
  id: FoodBoxPizzaFilled
  suffix: Filled
  components:
  - type: Sprite
    layers:
    - state: box
    - state: box-open
      map: ["enum.StorageVisualLayers.Door"]
  - type: StorageFill
    contents:
    - id: FoodPizzaArnold
      prob: 0.15
      orGroup: Pizza
    - id: FoodPizzaDank
      prob: 0.10
      orGroup: Pizza
    - id: FoodPizzaSassysage
      prob: 0.15
      orGroup: Pizza
    - id: FoodPizzaMargherita
      prob: 0.15
      orGroup: Pizza
    - id: FoodPizzaMeat
      prob: 0.15
      orGroup: Pizza
    - id: FoodPizzaMushroom
      prob: 0.15
      orGroup: Pizza
    - id: FoodPizzaPineapple
      prob: 0.15
      orGroup: Pizza
    - id: FoodPizzaVegetable
      prob: 0.15
      orGroup: Pizza
    - id: FoodPizzaDonkpocket
      prob: 0.10
      orGroup: Pizza
    - id: FoodMothPizzaFirecracker # Nyanotrasen - Moth Recipes
      prob: 0.05
      orGroup: Pizza
    - id: FoodMothPizzaFiveCheese # Nyanotrasen - Moth Recipes
      prob: 0.05
      orGroup: Pizza
    - id: FoodMothPizzaPesto # Nyanotrasen - Moth Recipes
      prob: 0.05
      orGroup: Pizza
    - id: FoodPizzaCorncob # Nyanotrasen - Corncob Pizza
      prob: 0.01
      orGroup: Pizza

# Nugget

- type: entity
  parent: [ BoxCardboard, BaseBagOpenClose ]
  id: FoodBoxNugget
  name: "курячі нагетси"
  description: "У вас раптом виникло бажання торгувати на міжгалактичному фондовому ринку."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/Baked/nuggets.rsi
    state: box
    layers:
    - state: box
    - state: box-open
      map: ["openLayer"]
      visible: false
    - state: box1
      map: ["box1"]
      visible: false
    - state: box2
      map: ["box2"]
      visible: false
    - state: box3
      map: ["box3"]
      visible: false
    - state: box4
      map: ["box4"]
      visible: false
    - state: box5
      map: ["box5"]
      visible: false
    - state: box6
      map: ["box6"]
      visible: false
  - type: Storage
    grid:
    - 0,0,1,2
  - type: Item
    sprite: Objects/Consumable/Food/Baked/nuggets.rsi
    size: Small
    heldPrefix: box
  - type: StorageFill
    contents:
    - id: FoodBakedNugget
      amount: 6
  - type: ItemCounter
    count:
      tags: [Nugget]
    composite: true
    layerStates:
    - box1
    - box2
    - box3
    - box4
    - box5
    - box6
  - type: Appearance

# Donkpocket

- type: entity
  parent: BoxCardboard
  id: FoodBoxDonkpocket
  name: "коробка з донк-покет"
  description: "Інструкція: Розігріти в мікрохвильовій печі. Продукт охолоне, якщо його не з'їсти протягом семи хвилин."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/Baked/donkpocket.rsi
    state: box
  - type: Storage
    grid:
    - 0,0,3,2
    whitelist:
      tags:
      - DonkPocket
  - type: Item
    sprite: Objects/Consumable/Food/Baked/donkpocket.rsi
    size: Small
  - type: StorageFill
    contents:
    - id: FoodDonkpocket
      amount: 6

- type: entity
  parent: FoodBoxDonkpocket
  id: FoodBoxDonkpocketSpicy
  name: "коробка донк-покетів з гострим смаком"
  components:
  - type: Sprite
    state: spicy-box
  - type: Item
    sprite: Objects/Consumable/Food/Baked/donkpocket.rsi
  - type: StorageFill
    contents:
    - id: FoodDonkpocketSpicy
      amount: 6

- type: entity
  parent: FoodBoxDonkpocket
  id: FoodBoxDonkpocketTeriyaki
  name: "коробка донк-покетів зі смаком теріякі"
  components:
  - type: Sprite
    state: teriyaki-box
  - type: Item
    sprite: Objects/Consumable/Food/Baked/donkpocket.rsi
  - type: StorageFill
    contents:
    - id: FoodDonkpocketTeriyaki
      amount: 6

- type: entity
  parent: FoodBoxDonkpocket
  id: FoodBoxDonkpocketPizza
  name: "коробка донк-покетів зі смаком піци"
  components:
  - type: Sprite
    state: pizza-box
  - type: Item
    sprite: Objects/Consumable/Food/Baked/donkpocket.rsi
  - type: StorageFill
    contents:
    - id: FoodDonkpocketPizza
      amount: 6

- type: entity
  parent: FoodBoxDonkpocket
  id: FoodBoxDonkpocketStonk
  name: "коробка з лімітованою серією стонк-покетів"
  components:
  - type: Sprite
    state: stonk-box
  - type: Item
    sprite: Objects/Consumable/Food/Baked/donkpocket.rsi
  - type: StorageFill
    contents:
    - id: FoodDonkpocketStonk
      amount: 6

- type: entity
  parent: FoodBoxDonkpocket
  id: FoodBoxDonkpocketCarp
  name: "коробка короп-покетів"
  components:
  - type: Sprite
    state: carp-box
  - type: StorageFill
    contents:
    - id: FoodDonkpocketCarp
      amount: 6

- type: entity
  parent: FoodBoxDonkpocket
  id: FoodBoxDonkpocketBerry
  name: "коробка донк-покетів з ягідним смаком"
  components:
  - type: Sprite
    state: berry-box
  - type: Item
    sprite: Objects/Consumable/Food/Baked/donkpocket.rsi
  - type: StorageFill
    contents:
    - id: FoodDonkpocketBerry
      amount: 6

- type: entity
  parent: FoodBoxDonkpocket
  id: FoodBoxDonkpocketHonk
  name: "коробка донк-покетів зі смаком банана"
  components:
  - type: Sprite
    state: banana-box
  - type: Item
    sprite: Objects/Consumable/Food/Baked/donkpocket.rsi
  - type: StorageFill
    contents:
    - id: FoodDonkpocketHonk
      amount: 6

- type: entity
  parent: FoodBoxDonkpocket
  id: FoodBoxDonkpocketDink
  name: "коробка з ящірними донк-покетами"
  description: "Не нуль вуглеводів! Не потрібно розігрівати!"
  components:
  - type: Sprite
    state: dink-box
  - type: Item
    sprite: Objects/Consumable/Food/Baked/donkpocket.rsi
  - type: StorageFill
    contents:
    - id: FoodDonkpocketDink
      amount: 6

- type: entity
  id: HappyHonk
  parent: [ BoxCardboard, BaseBagOpenClose ]
  name: "набір хеппі-хонк їжі"
  suffix: Toy Safe
  description: "Іграшка більш їстівна, ніж сама їжа."
  components:
  - type: Sprite
    sprite: Objects/Storage/Happyhonk/clown.rsi
    state: box
    layers:
    - state: box
    - state: box-open
      map: ["openLayer"]
  - type: Item
    sprite: Objects/Storage/Happyhonk/clown.rsi
    heldPrefix: box
  - type: Storage
    maxItemSize: Normal
    grid:
    - 0,0,3,3
  - type: Tag
    tags:
    - Trash
    - HappyHonk
  - type: StorageFill
    contents:
      - id: ToyMouse
        orGroup: GiftPool
      - id: ToyAi
        orGroup: GiftPool
      - id: ToyNuke
        orGroup: GiftPool
      - id: ToyFigurinePassenger
        orGroup: GiftPool
      - id: ToyGriffin
        orGroup: GiftPool
      - id: ToyHonk
        orGroup: GiftPool
      - id: ToyIan
        orGroup: GiftPool
      - id: ToyMarauder
        orGroup: GiftPool
      - id: ToyMauler
        orGroup: GiftPool
      - id: ToyGygax
        orGroup: GiftPool
      - id: ToyOdysseus
        orGroup: GiftPool
      - id: ToyOwlman
        orGroup: GiftPool
      - id: ToyDeathRipley
        orGroup: GiftPool
      - id: ToyPhazon
        orGroup: GiftPool
      - id: ToyFireRipley
        orGroup: GiftPool
      - id: ToyReticence
        orGroup: GiftPool
      - id: ToyRipley
        orGroup: GiftPool
      - id: ToySeraph
        orGroup: GiftPool
      - id: ToyDurand
        orGroup: GiftPool
      - id: ToySkeleton
        orGroup: GiftPool
      - id: FoamBlade
        orGroup: GiftPool
      - id: ClothingHeadHatBunny
        orGroup: GiftPool
      - id: PersonalAI
        orGroup: GiftPool
      - id: CrayonBox
        orGroup: GiftPool
      - id: ToySword
        orGroup: GiftPool
      - id: RevolverCapGun
        orGroup: GiftPool
      - id: ToyRubberDuck
        orGroup: GiftPool
      - id: BikeHorn
        prob: 0.5
        orGroup: GiftPool
      - id: GoldenBikeHorn
        prob: 0.1
        orGroup: GiftPool

- type: entity
  id: HappyHonkMime
  parent: HappyHonk
  description: "Лімітоване мімічне видання щасливої гудкової їжі."
  suffix: Toy Safe
  components:
  - type: Tag
    tags:
    - Trash
    - MimeHappyHonk
  - type: Sprite
    sprite: Objects/Storage/Happyhonk/mime.rsi
    state: box
  - type: Item
    sprite: Objects/Storage/Happyhonk/mime.rsi
    heldPrefix: box

- type: entity
  id: HappyHonkNukie
  parent: HappyHonk
  name: "міцний нуковий шрот"
  description: "Суха їжа з потенційно вибухонебезпечним сюрпризом."
  suffix: Toy Unsafe
  components:
  - type: Sprite
    sprite: Objects/Storage/Happyhonk/nukie.rsi
    state: box
  - type: Item
    sprite: Objects/Storage/Happyhonk/nukie.rsi
    heldPrefix: box
  - type: StorageFill
    contents:
      - id: C4
        prob: 0.02
        orGroup: GiftPool
      - id: ToyMarauder
        orGroup: GiftPool
      - id: ToyMauler
        orGroup: GiftPool
      - id: ToyNuke
        orGroup: GiftPool
      - id: ToySword
        orGroup: GiftPool
      - id: BalloonSyn
        prob: 0.6
        orGroup: GiftPool
      - id: PlushieNuke
        orGroup: GiftPool

- type: entity
  parent: HappyHonkNukie
  id: HappyHonkNukieSnacks
  suffix: Toy Unsafe, Snacks
  name: "ланчбокс Синдикату"
  components:
  - type: Storage
    grid:
    - 0,0,5,2
  - type: StorageFill
    contents:
    # toy
    - id: C4
      prob: 0.02
      orGroup: GiftPool
    - id: ToyMarauder
      orGroup: GiftPool
    - id: ToyMauler
      orGroup: GiftPool
    - id: ToyNuke
      orGroup: GiftPool
    - id: ToySword
      orGroup: GiftPool
    - id: BalloonSyn
      prob: 0.6
      orGroup: GiftPool
    - id: PlushieNuke
      orGroup: GiftPool
    # drinks - 4 cans, up to 2 blood-red brews
    - id: DrinkNukieCan
      prob: 0.2
      orGroup: Drink1Pool
    - id: DrinkColaCan
      orGroup: Drink1Pool
    - id: DrinkNukieCan
      prob: 0.2
      orGroup: Drink2Pool
    - id: DrinkColaCan
      orGroup: Drink2Pool
    - id: DrinkColaCan
      amount: 2
    # food
    - id: FoodSaladValid
      prob: 0.05
      amount: 4
      orGroup: FoodPool
    - id: FoodSnackSyndi
      amount: 4
      orGroup: FoodPool

- type: entity
  id: HappyHonkCluwne
  parent: HappyHonk
  name: "жалюгідна страва клувні"
  description: "Нічого доброго з цього не вийде."
  components:
  - type: Tag
    tags:
    - Trash
    - CluwneHappyHonk
  - type: Sprite
    sprite: Objects/Storage/Happyhonk/cluwne.rsi
    state: box
  - type: Item
    sprite: Objects/Storage/Happyhonk/cluwne.rsi
    heldPrefix: box
  - type: StorageFill
    contents:
      - id: CluwneHorn

- type: entity
  id: FoodMealHappyHonkClown
  parent: HappyHonk
  suffix: random food spawner meal
  categories: [ HideSpawnMenu ]
  components:
  - type: StorageFill
    contents:
      - id: FoodBurgerCrazy
        orGroup: Burgers
      - id: FoodBurgerCheese
        orGroup: Burgers
      - id: DrinkColaCan
        orGroup: DrinkPool
      - id: DrinkLemonLimeCan
        orGroup: DrinkPool
      - id: DrinkIcedTeaCan
        orGroup: DrinkPool
      - id: ToyMouse
        orGroup: GiftPool
      - id: ToyAi
        orGroup: GiftPool
      - id: ToyNuke
        orGroup: GiftPool
      - id: ToyFigurinePassenger
        orGroup: GiftPool
      - id: ToyGriffin
        orGroup: GiftPool
      - id: ToyHonk
        orGroup: GiftPool
      - id: ToyIan
        orGroup: GiftPool
      - id: ToyMarauder
        orGroup: GiftPool
      - id: ToyMauler
        orGroup: GiftPool
      - id: ToyGygax
        orGroup: GiftPool
      - id: ToyOdysseus
        orGroup: GiftPool
      - id: ToyOwlman
        orGroup: GiftPool
      - id: ToyDeathRipley
        orGroup: GiftPool
      - id: ToyPhazon
        orGroup: GiftPool
      - id: ToyFireRipley
        orGroup: GiftPool
      - id: ToyReticence
        orGroup: GiftPool
      - id: ToyRipley
        orGroup: GiftPool
      - id: ToySeraph
        orGroup: GiftPool
      - id: ToyDurand
        orGroup: GiftPool
      - id: ToySkeleton
        orGroup: GiftPool
      - id: FoamBlade
        orGroup: GiftPool
      - id: ClothingHeadHatBunny
        orGroup: GiftPool
      - id: PersonalAI
        orGroup: GiftPool
      - id: CrayonBox
        orGroup: GiftPool
      - id: ToySword
        orGroup: GiftPool
      - id: RevolverCapGun
        orGroup: GiftPool
      - id: ToyRubberDuck
        orGroup: GiftPool
      - id: BikeHorn
        prob: 0.5
        orGroup: GiftPool
      - id: GoldenBikeHorn
        prob: 0.1
        orGroup: GiftPool
      - id: ToyRenault # DeltaV Toy, see Resources/Prototypes/DeltaV/Entities/Objects/Fun/toys.yml
        orGroup: GiftPool
      - id: ToySiobhan # DeltaV Toy, see Resources/Prototypes/DeltaV/Entities/Objects/Fun/toys.yml
        orGroup: GiftPool
