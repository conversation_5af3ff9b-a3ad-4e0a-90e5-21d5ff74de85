- type: gameMap
  id: Lighthouse
  mapName: Маяк
  mapPath: /Maps/lighthouse.yml
  minPlayers: 10
  stations:
    Lighthouse:
      stationProto: StandardNanotrasenStationNoATS
      components:
      - type: StationEmergencyShuttle
        emergencyShuttlePath: /Maps/Shuttles/DeltaV/NTES_BC20.yml
      - type: StationNameSetup
        mapNameTemplate: '{0} Lighthouse {1}'
        nameGenerator:
          !type:NanotrasenNameGenerator
          prefixCreator: '14'
      - type: StationJobs
        overflowJobs:
        - Passenger
        availableJobs:
        #command
          Captain: [ 1, 1 ]
          BlueshieldOfficer: [ 1, 1]
          NanotrasenRepresentative: [ 1, 1 ]
          # Magistrate: [ 1, 1 ] # EE: Disabled
          # AdministrativeAssistant: [ 1, 1 ] # EE: Disabled
        #service
          HeadOfPersonnel: [ 1, 1 ]
          Bartender: [ 1, 2 ]
          Botanist: [ 2, 2 ]
          # Boxer: [ 2, 2 ] # EE: Disabled
          Chef: [ 1, 2 ]
          Clown: [ 1, 1 ]
          Lawyer: [ 2, 2 ]
          Reporter: [ 0, 2 ]
          Musician: [ 1, 1 ]
          Janitor: [ 1, 2 ]
          Mime: [ 1, 1 ]
        #engineering
          ChiefEngineer: [ 1, 1 ]
          AtmosphericTechnician: [ 1, 2 ]
          StationEngineer: [ 3, 4 ]
          TechnicalAssistant: [ 2, 3 ]
          SeniorEngineer: [ 1, 1 ]
        #medical
          ChiefMedicalOfficer: [ 1, 1 ]
          Chemist: [ 1, 2 ]
          Paramedic: [ 1, 2 ]
          Psychologist: [ 1, 1 ]
          MedicalDoctor: [ 3, 4 ]
          MedicalIntern: [ 2, 3 ]
          SeniorPhysician: [ 1, 1 ]
        #science
          ResearchDirector: [ 1, 1 ]
          Chaplain: [ 1, 1 ]
          ForensicMantis: [ 1, 1 ]
          Scientist: [ 3, 4 ]
          ResearchAssistant: [ 2, 3 ]
          Borg: [ 1, 2 ]
          SeniorResearcher: [ 1, 1 ]
        #security
          HeadOfSecurity: [ 1, 1 ]
          Warden: [ 1, 1 ]
          Detective: [ 1, 1 ]
          SecurityOfficer: [ 2, 4 ]
          SecurityCadet: [ 1, 4 ]
          SeniorOfficer: [ 1, 1 ]
        #supply
          Quartermaster: [ 1, 1 ]
          MailCarrier: [ 2, 2 ]
          SalvageSpecialist: [ 2, 3 ]
          CargoTechnician: [ 2, 4 ]
        #civilian
          Passenger: [ -1, -1 ]
        # Silicon
          StationAi: [ 1, 1 ]
      # Goobstation blob-config-start HUGE
      - type: StationBlobConfig
        stageBegin: 40
        stageCritical: 450
        stageTheEnd: 900
      # Goobstation blob-config-end
