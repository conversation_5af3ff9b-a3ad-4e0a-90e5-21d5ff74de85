- type: entity
  id: HandheldHealthAnalyzerUnpowered
  parent: BaseItem
  name: "аналізатор здоров'я"
  description: "Ручний сканер тіла, здатний розпізнавати життєво важливі ознаки суб'єкта."
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/healthanalyzer.rsi
    state: icon
    layers:
      - state: icon
      - state: analyzer
        shader: unshaded
        visible: true
        map: [ "enum.PowerDeviceVisualLayers.Powered" ]
  - type: Item
    storedRotation: -90
  - type: ActivatableUI
    key: enum.HealthAnalyzerUiKey.Key
  - type: UserInterface
    interfaces:
      enum.HealthAnalyzerUiKey.Key:
        type: HealthAnalyzerBoundUserInterface
  - type: ItemToggle
    onUse: false
  - type: HealthAnalyzer
    scanningEndSound:
      path: "/Audio/Items/Medical/healthscanner.ogg"
  - type: Tag
    tags:
      - DiscreteHealthAnalyzer
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.PowerCellSlotVisuals.Enabled:
        enum.PowerDeviceVisualLayers.Powered:
          True: { visible: true }
          False: { visible: false }
  - type: GuideHelp
    guides:
      - Medical Doctor
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 5

- type: entity
  id: HandheldHealthAnalyzer
  parent: [ HandheldHealthAnalyzerUnpowered, PowerCellSlotSmallItem]
  suffix: Powered
  components:
  - type: PowerCellDraw
    drawRate: 1.2 #Calculated for 5 minutes on a small cell
  - type: ToggleCellDraw
  - type: ActivatableUIRequiresPowerCell

- type: entity
  id: HandheldHealthAnalyzerEmpty
  parent: HandheldHealthAnalyzer
  suffix: Empty
  components:
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
