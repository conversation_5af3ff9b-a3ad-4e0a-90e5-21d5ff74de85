﻿// <auto-generated />
using System;
using System.Net;
using System.Text.Json;
using Content.Server.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using NpgsqlTypes;

#nullable disable

namespace Content.Server.Database.Migrations.Postgres
{
    [DbContext(typeof(PostgresServerDbContext))]
    [Migration("20250115213956_DisplayPronouns")]
    partial class DisplayPronouns
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Content.Server.Database.Admin", b =>
                {
                    b.Property<Guid>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<int?>("AdminRankId")
                        .HasColumnType("integer")
                        .HasColumnName("admin_rank_id");

                    b.Property<string>("Title")
                        .HasColumnType("text")
                        .HasColumnName("title");

                    b.HasKey("UserId")
                        .HasName("PK_admin");

                    b.HasIndex("AdminRankId")
                        .HasDatabaseName("IX_admin_admin_rank_id");

                    b.ToTable("admin", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.AdminFlag", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("admin_flag_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("AdminId")
                        .HasColumnType("uuid")
                        .HasColumnName("admin_id");

                    b.Property<string>("Flag")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("flag");

                    b.Property<bool>("Negative")
                        .HasColumnType("boolean")
                        .HasColumnName("negative");

                    b.HasKey("Id")
                        .HasName("PK_admin_flag");

                    b.HasIndex("AdminId")
                        .HasDatabaseName("IX_admin_flag_admin_id");

                    b.HasIndex("Flag", "AdminId")
                        .IsUnique();

                    b.ToTable("admin_flag", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.AdminLog", b =>
                {
                    b.Property<int>("RoundId")
                        .HasColumnType("integer")
                        .HasColumnName("round_id");

                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("admin_log_id");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("date");

                    b.Property<short>("Impact")
                        .HasColumnType("smallint")
                        .HasColumnName("impact");

                    b.Property<JsonDocument>("Json")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("json");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("message");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.HasKey("RoundId", "Id")
                        .HasName("PK_admin_log");

                    b.HasIndex("Date");

                    b.HasIndex("Message")
                        .HasAnnotation("Npgsql:TsVectorConfig", "english");

                    NpgsqlIndexBuilderExtensions.HasMethod(b.HasIndex("Message"), "GIN");

                    b.HasIndex("Type")
                        .HasDatabaseName("IX_admin_log_type");

                    b.ToTable("admin_log", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.AdminLogPlayer", b =>
                {
                    b.Property<int>("RoundId")
                        .HasColumnType("integer")
                        .HasColumnName("round_id");

                    b.Property<int>("LogId")
                        .HasColumnType("integer")
                        .HasColumnName("log_id");

                    b.Property<Guid>("PlayerUserId")
                        .HasColumnType("uuid")
                        .HasColumnName("player_user_id");

                    b.HasKey("RoundId", "LogId", "PlayerUserId")
                        .HasName("PK_admin_log_player");

                    b.HasIndex("PlayerUserId")
                        .HasDatabaseName("IX_admin_log_player_player_user_id");

                    b.ToTable("admin_log_player", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.AdminMessage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("admin_messages_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean")
                        .HasColumnName("deleted");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by_id");

                    b.Property<bool>("Dismissed")
                        .HasColumnType("boolean")
                        .HasColumnName("dismissed");

                    b.Property<DateTime?>("ExpirationTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("expiration_time");

                    b.Property<DateTime?>("LastEditedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_edited_at");

                    b.Property<Guid?>("LastEditedById")
                        .HasColumnType("uuid")
                        .HasColumnName("last_edited_by_id");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(4096)
                        .HasColumnType("character varying(4096)")
                        .HasColumnName("message");

                    b.Property<Guid?>("PlayerUserId")
                        .HasColumnType("uuid")
                        .HasColumnName("player_user_id");

                    b.Property<TimeSpan>("PlaytimeAtNote")
                        .HasColumnType("interval")
                        .HasColumnName("playtime_at_note");

                    b.Property<int?>("RoundId")
                        .HasColumnType("integer")
                        .HasColumnName("round_id");

                    b.Property<bool>("Seen")
                        .HasColumnType("boolean")
                        .HasColumnName("seen");

                    b.HasKey("Id")
                        .HasName("PK_admin_messages");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DeletedById");

                    b.HasIndex("LastEditedById");

                    b.HasIndex("PlayerUserId")
                        .HasDatabaseName("IX_admin_messages_player_user_id");

                    b.HasIndex("RoundId")
                        .HasDatabaseName("IX_admin_messages_round_id");

                    b.ToTable("admin_messages", null, t =>
                        {
                            t.HasCheckConstraint("NotDismissedAndSeen", "NOT dismissed OR seen");
                        });
                });

            modelBuilder.Entity("Content.Server.Database.AdminNote", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("admin_notes_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean")
                        .HasColumnName("deleted");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("ExpirationTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("expiration_time");

                    b.Property<DateTime?>("LastEditedAt")
                        .IsRequired()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_edited_at");

                    b.Property<Guid?>("LastEditedById")
                        .HasColumnType("uuid")
                        .HasColumnName("last_edited_by_id");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(4096)
                        .HasColumnType("character varying(4096)")
                        .HasColumnName("message");

                    b.Property<Guid?>("PlayerUserId")
                        .HasColumnType("uuid")
                        .HasColumnName("player_user_id");

                    b.Property<TimeSpan>("PlaytimeAtNote")
                        .HasColumnType("interval")
                        .HasColumnName("playtime_at_note");

                    b.Property<int?>("RoundId")
                        .HasColumnType("integer")
                        .HasColumnName("round_id");

                    b.Property<bool>("Secret")
                        .HasColumnType("boolean")
                        .HasColumnName("secret");

                    b.Property<int>("Severity")
                        .HasColumnType("integer")
                        .HasColumnName("severity");

                    b.HasKey("Id")
                        .HasName("PK_admin_notes");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DeletedById");

                    b.HasIndex("LastEditedById");

                    b.HasIndex("PlayerUserId")
                        .HasDatabaseName("IX_admin_notes_player_user_id");

                    b.HasIndex("RoundId")
                        .HasDatabaseName("IX_admin_notes_round_id");

                    b.ToTable("admin_notes", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.AdminRank", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("admin_rank_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("PK_admin_rank");

                    b.ToTable("admin_rank", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.AdminRankFlag", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("admin_rank_flag_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AdminRankId")
                        .HasColumnType("integer")
                        .HasColumnName("admin_rank_id");

                    b.Property<string>("Flag")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("flag");

                    b.HasKey("Id")
                        .HasName("PK_admin_rank_flag");

                    b.HasIndex("AdminRankId");

                    b.HasIndex("Flag", "AdminRankId")
                        .IsUnique();

                    b.ToTable("admin_rank_flag", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.AdminWatchlist", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("admin_watchlists_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean")
                        .HasColumnName("deleted");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("ExpirationTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("expiration_time");

                    b.Property<DateTime?>("LastEditedAt")
                        .IsRequired()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_edited_at");

                    b.Property<Guid?>("LastEditedById")
                        .HasColumnType("uuid")
                        .HasColumnName("last_edited_by_id");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(4096)
                        .HasColumnType("character varying(4096)")
                        .HasColumnName("message");

                    b.Property<Guid?>("PlayerUserId")
                        .HasColumnType("uuid")
                        .HasColumnName("player_user_id");

                    b.Property<TimeSpan>("PlaytimeAtNote")
                        .HasColumnType("interval")
                        .HasColumnName("playtime_at_note");

                    b.Property<int?>("RoundId")
                        .HasColumnType("integer")
                        .HasColumnName("round_id");

                    b.HasKey("Id")
                        .HasName("PK_admin_watchlists");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DeletedById");

                    b.HasIndex("LastEditedById");

                    b.HasIndex("PlayerUserId")
                        .HasDatabaseName("IX_admin_watchlists_player_user_id");

                    b.HasIndex("RoundId")
                        .HasDatabaseName("IX_admin_watchlists_round_id");

                    b.ToTable("admin_watchlists", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Antag", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("antag_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AntagName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("antag_name");

                    b.Property<int>("ProfileId")
                        .HasColumnType("integer")
                        .HasColumnName("profile_id");

                    b.HasKey("Id")
                        .HasName("PK_antag");

                    b.HasIndex("ProfileId", "AntagName")
                        .IsUnique();

                    b.ToTable("antag", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.AssignedUserId", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("assigned_user_id_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("user_name");

                    b.HasKey("Id")
                        .HasName("PK_assigned_user_id");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.HasIndex("UserName")
                        .IsUnique();

                    b.ToTable("assigned_user_id", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.BanTemplate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("ban_template_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("AutoDelete")
                        .HasColumnType("boolean")
                        .HasColumnName("auto_delete");

                    b.Property<int>("ExemptFlags")
                        .HasColumnType("integer")
                        .HasColumnName("exempt_flags");

                    b.Property<bool>("Hidden")
                        .HasColumnType("boolean")
                        .HasColumnName("hidden");

                    b.Property<TimeSpan>("Length")
                        .HasColumnType("interval")
                        .HasColumnName("length");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("reason");

                    b.Property<int>("Severity")
                        .HasColumnType("integer")
                        .HasColumnName("severity");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("title");

                    b.HasKey("Id")
                        .HasName("PK_ban_template");

                    b.ToTable("ban_template", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Blacklist", b =>
                {
                    b.Property<Guid>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("UserId")
                        .HasName("PK_blacklist");

                    b.ToTable("blacklist", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.ConnectionLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("connection_log_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<IPAddress>("Address")
                        .IsRequired()
                        .HasColumnType("inet")
                        .HasColumnName("address");

                    b.Property<byte?>("Denied")
                        .HasColumnType("smallint")
                        .HasColumnName("denied");

                    b.Property<int>("ServerId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("server_id");

                    b.Property<DateTime>("Time")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("time");

                    b.Property<float>("Trust")
                        .HasColumnType("real")
                        .HasColumnName("trust");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("user_name");

                    b.HasKey("Id")
                        .HasName("PK_connection_log");

                    b.HasIndex("ServerId")
                        .HasDatabaseName("IX_connection_log_server_id");

                    b.HasIndex("Time");

                    b.HasIndex("UserId");

                    b.ToTable("connection_log", null, t =>
                        {
                            t.HasCheckConstraint("AddressNotIPv6MappedIPv4", "NOT inet '::ffff:0.0.0.0/96' >>= address");
                        });
                });

            modelBuilder.Entity("Content.Server.Database.ConsentSettings", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("consent_settings_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ConsentFreetext")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("consent_freetext");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("PK_consent_settings");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("consent_settings", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.ConsentToggle", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("consent_toggle_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("ConsentSettingsId")
                        .HasColumnType("integer")
                        .HasColumnName("consent_settings_id");

                    b.Property<string>("ToggleProtoId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("toggle_proto_id");

                    b.Property<string>("ToggleProtoState")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("toggle_proto_state");

                    b.HasKey("Id")
                        .HasName("PK_consent_toggle");

                    b.HasIndex("ConsentSettingsId", "ToggleProtoId")
                        .IsUnique();

                    b.ToTable("consent_toggle", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Job", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("job_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("JobName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("job_name");

                    b.Property<int>("Priority")
                        .HasColumnType("integer")
                        .HasColumnName("priority");

                    b.Property<int>("ProfileId")
                        .HasColumnType("integer")
                        .HasColumnName("profile_id");

                    b.HasKey("Id")
                        .HasName("PK_job");

                    b.HasIndex("ProfileId");

                    b.HasIndex("ProfileId", "JobName")
                        .IsUnique();

                    b.HasIndex(new[] { "ProfileId" }, "IX_job_one_high_priority")
                        .IsUnique()
                        .HasFilter("priority = 3");

                    b.ToTable("job", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Loadout", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("loadout_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CustomColorTint")
                        .HasColumnType("text")
                        .HasColumnName("custom_color_tint");

                    b.Property<string>("CustomDescription")
                        .HasColumnType("text")
                        .HasColumnName("custom_description");

                    b.Property<bool?>("CustomHeirloom")
                        .HasColumnType("boolean")
                        .HasColumnName("custom_heirloom");

                    b.Property<string>("CustomName")
                        .HasColumnType("text")
                        .HasColumnName("custom_name");

                    b.Property<string>("LoadoutName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("loadout_name");

                    b.Property<int>("ProfileId")
                        .HasColumnType("integer")
                        .HasColumnName("profile_id");

                    b.HasKey("Id")
                        .HasName("PK_loadout");

                    b.HasIndex("ProfileId", "LoadoutName")
                        .IsUnique();

                    b.ToTable("loadout", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.PlayTime", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("play_time_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("PlayerId")
                        .HasColumnType("uuid")
                        .HasColumnName("player_id");

                    b.Property<TimeSpan>("TimeSpent")
                        .HasColumnType("interval")
                        .HasColumnName("time_spent");

                    b.Property<string>("Tracker")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("tracker");

                    b.HasKey("Id")
                        .HasName("PK_play_time");

                    b.HasIndex("PlayerId", "Tracker")
                        .IsUnique();

                    b.ToTable("play_time", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Player", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("player_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("FirstSeenTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("first_seen_time");

                    b.Property<DateTime?>("LastReadRules")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_read_rules");

                    b.Property<IPAddress>("LastSeenAddress")
                        .IsRequired()
                        .HasColumnType("inet")
                        .HasColumnName("last_seen_address");

                    b.Property<DateTime>("LastSeenTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_seen_time");

                    b.Property<string>("LastSeenUserName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("last_seen_user_name");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("PK_player");

                    b.HasAlternateKey("UserId")
                        .HasName("ak_player_user_id");

                    b.HasIndex("LastSeenUserName");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("player", null, t =>
                        {
                            t.HasCheckConstraint("LastSeenAddressNotIPv6MappedIPv4", "NOT inet '::ffff:0.0.0.0/96' >>= last_seen_address");
                        });
                });

            modelBuilder.Entity("Content.Server.Database.Preference", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("preference_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AdminOOCColor")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("admin_ooc_color");

                    b.Property<int>("SelectedCharacterSlot")
                        .HasColumnType("integer")
                        .HasColumnName("selected_character_slot");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("PK_preference");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("preference", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Profile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("profile_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("Age")
                        .HasColumnType("integer")
                        .HasColumnName("age");

                    b.Property<string>("Backpack")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("backpack");

                    b.Property<string>("CharacterName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("char_name");

                    b.Property<string>("Clothing")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("clothing");

                    b.Property<string>("CustomSpecieName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("custom_specie_name");

                    b.Property<string>("DisplayPronouns")
                        .HasColumnType("text")
                        .HasColumnName("display_pronouns");

                    b.Property<string>("EyeColor")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("eye_color");

                    b.Property<string>("FacialHairColor")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("facial_hair_color");

                    b.Property<string>("FacialHairName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("facial_hair_name");

                    b.Property<string>("FlavorText")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("flavor_text");

                    b.Property<string>("Gender")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("gender");

                    b.Property<string>("HairColor")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("hair_color");

                    b.Property<string>("HairName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("hair_name");

                    b.Property<float>("Height")
                        .HasColumnType("real")
                        .HasColumnName("height");

                    b.Property<JsonDocument>("Markings")
                        .HasColumnType("jsonb")
                        .HasColumnName("markings");

                    b.Property<int>("PreferenceId")
                        .HasColumnType("integer")
                        .HasColumnName("preference_id");

                    b.Property<int>("PreferenceUnavailable")
                        .HasColumnType("integer")
                        .HasColumnName("pref_unavailable");

                    b.Property<string>("Sex")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("sex");

                    b.Property<string>("SkinColor")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("skin_color");

                    b.Property<int>("Slot")
                        .HasColumnType("integer")
                        .HasColumnName("slot");

                    b.Property<int>("SpawnPriority")
                        .HasColumnType("integer")
                        .HasColumnName("spawn_priority");

                    b.Property<string>("Species")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("species");

                    b.Property<float>("Width")
                        .HasColumnType("real")
                        .HasColumnName("width");

                    b.HasKey("Id")
                        .HasName("PK_profile");

                    b.HasIndex("PreferenceId")
                        .HasDatabaseName("IX_profile_preference_id");

                    b.HasIndex("Slot", "PreferenceId")
                        .IsUnique();

                    b.ToTable("profile", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.RoleWhitelist", b =>
                {
                    b.Property<Guid>("PlayerUserId")
                        .HasColumnType("uuid")
                        .HasColumnName("player_user_id");

                    b.Property<string>("RoleId")
                        .HasColumnType("text")
                        .HasColumnName("role_id");

                    b.HasKey("PlayerUserId", "RoleId")
                        .HasName("PK_role_whitelists");

                    b.ToTable("role_whitelists", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Round", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("round_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("ServerId")
                        .HasColumnType("integer")
                        .HasColumnName("server_id");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("start_date");

                    b.HasKey("Id")
                        .HasName("PK_round");

                    b.HasIndex("ServerId")
                        .HasDatabaseName("IX_round_server_id");

                    b.HasIndex("StartDate");

                    b.ToTable("round", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Server", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("server_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("PK_server");

                    b.ToTable("server", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.ServerBan", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("server_ban_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<NpgsqlInet?>("Address")
                        .HasColumnType("inet")
                        .HasColumnName("address");

                    b.Property<bool>("AutoDelete")
                        .HasColumnType("boolean")
                        .HasColumnName("auto_delete");

                    b.Property<DateTime>("BanTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("ban_time");

                    b.Property<Guid?>("BanningAdmin")
                        .HasColumnType("uuid")
                        .HasColumnName("banning_admin");

                    b.Property<int>("ExemptFlags")
                        .HasColumnType("integer")
                        .HasColumnName("exempt_flags");

                    b.Property<DateTime?>("ExpirationTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("expiration_time");

                    b.Property<bool>("Hidden")
                        .HasColumnType("boolean")
                        .HasColumnName("hidden");

                    b.Property<DateTime?>("LastEditedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_edited_at");

                    b.Property<Guid?>("LastEditedById")
                        .HasColumnType("uuid")
                        .HasColumnName("last_edited_by_id");

                    b.Property<Guid?>("PlayerUserId")
                        .HasColumnType("uuid")
                        .HasColumnName("player_user_id");

                    b.Property<TimeSpan>("PlaytimeAtNote")
                        .HasColumnType("interval")
                        .HasColumnName("playtime_at_note");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("reason");

                    b.Property<int?>("RoundId")
                        .HasColumnType("integer")
                        .HasColumnName("round_id");

                    b.Property<int>("Severity")
                        .HasColumnType("integer")
                        .HasColumnName("severity");

                    b.HasKey("Id")
                        .HasName("PK_server_ban");

                    b.HasIndex("Address");

                    b.HasIndex("BanningAdmin");

                    b.HasIndex("LastEditedById");

                    b.HasIndex("PlayerUserId")
                        .HasDatabaseName("IX_server_ban_player_user_id");

                    b.HasIndex("RoundId")
                        .HasDatabaseName("IX_server_ban_round_id");

                    b.ToTable("server_ban", null, t =>
                        {
                            t.HasCheckConstraint("AddressNotIPv6MappedIPv4", "NOT inet '::ffff:0.0.0.0/96' >>= address");

                            t.HasCheckConstraint("HaveEitherAddressOrUserIdOrHWId", "address IS NOT NULL OR player_user_id IS NOT NULL OR hwid IS NOT NULL");
                        });
                });

            modelBuilder.Entity("Content.Server.Database.ServerBanExemption", b =>
                {
                    b.Property<Guid>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<int>("Flags")
                        .HasColumnType("integer")
                        .HasColumnName("flags");

                    b.HasKey("UserId")
                        .HasName("PK_server_ban_exemption");

                    b.ToTable("server_ban_exemption", null, t =>
                        {
                            t.HasCheckConstraint("FlagsNotZero", "flags != 0");
                        });
                });

            modelBuilder.Entity("Content.Server.Database.ServerBanHit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("server_ban_hit_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("BanId")
                        .HasColumnType("integer")
                        .HasColumnName("ban_id");

                    b.Property<int>("ConnectionId")
                        .HasColumnType("integer")
                        .HasColumnName("connection_id");

                    b.HasKey("Id")
                        .HasName("PK_server_ban_hit");

                    b.HasIndex("BanId")
                        .HasDatabaseName("IX_server_ban_hit_ban_id");

                    b.HasIndex("ConnectionId")
                        .HasDatabaseName("IX_server_ban_hit_connection_id");

                    b.ToTable("server_ban_hit", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.ServerRoleBan", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("server_role_ban_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<NpgsqlInet?>("Address")
                        .HasColumnType("inet")
                        .HasColumnName("address");

                    b.Property<DateTime>("BanTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("ban_time");

                    b.Property<Guid?>("BanningAdmin")
                        .HasColumnType("uuid")
                        .HasColumnName("banning_admin");

                    b.Property<DateTime?>("ExpirationTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("expiration_time");

                    b.Property<bool>("Hidden")
                        .HasColumnType("boolean")
                        .HasColumnName("hidden");

                    b.Property<DateTime?>("LastEditedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_edited_at");

                    b.Property<Guid?>("LastEditedById")
                        .HasColumnType("uuid")
                        .HasColumnName("last_edited_by_id");

                    b.Property<Guid?>("PlayerUserId")
                        .HasColumnType("uuid")
                        .HasColumnName("player_user_id");

                    b.Property<TimeSpan>("PlaytimeAtNote")
                        .HasColumnType("interval")
                        .HasColumnName("playtime_at_note");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("reason");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("role_id");

                    b.Property<int?>("RoundId")
                        .HasColumnType("integer")
                        .HasColumnName("round_id");

                    b.Property<int>("Severity")
                        .HasColumnType("integer")
                        .HasColumnName("severity");

                    b.HasKey("Id")
                        .HasName("PK_server_role_ban");

                    b.HasIndex("Address");

                    b.HasIndex("BanningAdmin");

                    b.HasIndex("LastEditedById");

                    b.HasIndex("PlayerUserId")
                        .HasDatabaseName("IX_server_role_ban_player_user_id");

                    b.HasIndex("RoundId")
                        .HasDatabaseName("IX_server_role_ban_round_id");

                    b.ToTable("server_role_ban", null, t =>
                        {
                            t.HasCheckConstraint("AddressNotIPv6MappedIPv4", "NOT inet '::ffff:0.0.0.0/96' >>= address");

                            t.HasCheckConstraint("HaveEitherAddressOrUserIdOrHWId", "address IS NOT NULL OR player_user_id IS NOT NULL OR hwid IS NOT NULL");
                        });
                });

            modelBuilder.Entity("Content.Server.Database.ServerRoleUnban", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("role_unban_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("BanId")
                        .HasColumnType("integer")
                        .HasColumnName("ban_id");

                    b.Property<DateTime>("UnbanTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("unban_time");

                    b.Property<Guid?>("UnbanningAdmin")
                        .HasColumnType("uuid")
                        .HasColumnName("unbanning_admin");

                    b.HasKey("Id")
                        .HasName("PK_server_role_unban");

                    b.HasIndex("BanId")
                        .IsUnique();

                    b.ToTable("server_role_unban", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.ServerUnban", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("unban_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("BanId")
                        .HasColumnType("integer")
                        .HasColumnName("ban_id");

                    b.Property<DateTime>("UnbanTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("unban_time");

                    b.Property<Guid?>("UnbanningAdmin")
                        .HasColumnType("uuid")
                        .HasColumnName("unbanning_admin");

                    b.HasKey("Id")
                        .HasName("PK_server_unban");

                    b.HasIndex("BanId")
                        .IsUnique();

                    b.ToTable("server_unban", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Trait", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("trait_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("ProfileId")
                        .HasColumnType("integer")
                        .HasColumnName("profile_id");

                    b.Property<string>("TraitName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("trait_name");

                    b.HasKey("Id")
                        .HasName("PK_trait");

                    b.HasIndex("ProfileId", "TraitName")
                        .IsUnique();

                    b.ToTable("trait", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.UploadedResourceLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("uploaded_resource_log_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<byte[]>("Data")
                        .IsRequired()
                        .HasColumnType("bytea")
                        .HasColumnName("data");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("date");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("path");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("PK_uploaded_resource_log");

                    b.ToTable("uploaded_resource_log", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Whitelist", b =>
                {
                    b.Property<Guid>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("UserId")
                        .HasName("PK_whitelist");

                    b.ToTable("whitelist", (string)null);
                });

            modelBuilder.Entity("PlayerRound", b =>
                {
                    b.Property<int>("PlayersId")
                        .HasColumnType("integer")
                        .HasColumnName("players_id");

                    b.Property<int>("RoundsId")
                        .HasColumnType("integer")
                        .HasColumnName("rounds_id");

                    b.HasKey("PlayersId", "RoundsId")
                        .HasName("PK_player_round");

                    b.HasIndex("RoundsId")
                        .HasDatabaseName("IX_player_round_rounds_id");

                    b.ToTable("player_round", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Admin", b =>
                {
                    b.HasOne("Content.Server.Database.AdminRank", "AdminRank")
                        .WithMany("Admins")
                        .HasForeignKey("AdminRankId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_admin_admin_rank_admin_rank_id");

                    b.Navigation("AdminRank");
                });

            modelBuilder.Entity("Content.Server.Database.AdminFlag", b =>
                {
                    b.HasOne("Content.Server.Database.Admin", "Admin")
                        .WithMany("Flags")
                        .HasForeignKey("AdminId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_admin_flag_admin_admin_id");

                    b.Navigation("Admin");
                });

            modelBuilder.Entity("Content.Server.Database.AdminLog", b =>
                {
                    b.HasOne("Content.Server.Database.Round", "Round")
                        .WithMany("AdminLogs")
                        .HasForeignKey("RoundId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_admin_log_round_round_id");

                    b.Navigation("Round");
                });

            modelBuilder.Entity("Content.Server.Database.AdminLogPlayer", b =>
                {
                    b.HasOne("Content.Server.Database.Player", "Player")
                        .WithMany("AdminLogs")
                        .HasForeignKey("PlayerUserId")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_admin_log_player_player_player_user_id");

                    b.HasOne("Content.Server.Database.AdminLog", "Log")
                        .WithMany("Players")
                        .HasForeignKey("RoundId", "LogId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_admin_log_player_admin_log_round_id_log_id");

                    b.Navigation("Log");

                    b.Navigation("Player");
                });

            modelBuilder.Entity("Content.Server.Database.AdminMessage", b =>
                {
                    b.HasOne("Content.Server.Database.Player", "CreatedBy")
                        .WithMany("AdminMessagesCreated")
                        .HasForeignKey("CreatedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_admin_messages_player_created_by_id");

                    b.HasOne("Content.Server.Database.Player", "DeletedBy")
                        .WithMany("AdminMessagesDeleted")
                        .HasForeignKey("DeletedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_admin_messages_player_deleted_by_id");

                    b.HasOne("Content.Server.Database.Player", "LastEditedBy")
                        .WithMany("AdminMessagesLastEdited")
                        .HasForeignKey("LastEditedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_admin_messages_player_last_edited_by_id");

                    b.HasOne("Content.Server.Database.Player", "Player")
                        .WithMany("AdminMessagesReceived")
                        .HasForeignKey("PlayerUserId")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("FK_admin_messages_player_player_user_id");

                    b.HasOne("Content.Server.Database.Round", "Round")
                        .WithMany()
                        .HasForeignKey("RoundId")
                        .HasConstraintName("FK_admin_messages_round_round_id");

                    b.Navigation("CreatedBy");

                    b.Navigation("DeletedBy");

                    b.Navigation("LastEditedBy");

                    b.Navigation("Player");

                    b.Navigation("Round");
                });

            modelBuilder.Entity("Content.Server.Database.AdminNote", b =>
                {
                    b.HasOne("Content.Server.Database.Player", "CreatedBy")
                        .WithMany("AdminNotesCreated")
                        .HasForeignKey("CreatedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_admin_notes_player_created_by_id");

                    b.HasOne("Content.Server.Database.Player", "DeletedBy")
                        .WithMany("AdminNotesDeleted")
                        .HasForeignKey("DeletedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_admin_notes_player_deleted_by_id");

                    b.HasOne("Content.Server.Database.Player", "LastEditedBy")
                        .WithMany("AdminNotesLastEdited")
                        .HasForeignKey("LastEditedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_admin_notes_player_last_edited_by_id");

                    b.HasOne("Content.Server.Database.Player", "Player")
                        .WithMany("AdminNotesReceived")
                        .HasForeignKey("PlayerUserId")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("FK_admin_notes_player_player_user_id");

                    b.HasOne("Content.Server.Database.Round", "Round")
                        .WithMany()
                        .HasForeignKey("RoundId")
                        .HasConstraintName("FK_admin_notes_round_round_id");

                    b.Navigation("CreatedBy");

                    b.Navigation("DeletedBy");

                    b.Navigation("LastEditedBy");

                    b.Navigation("Player");

                    b.Navigation("Round");
                });

            modelBuilder.Entity("Content.Server.Database.AdminRankFlag", b =>
                {
                    b.HasOne("Content.Server.Database.AdminRank", "Rank")
                        .WithMany("Flags")
                        .HasForeignKey("AdminRankId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_admin_rank_flag_admin_rank_admin_rank_id");

                    b.Navigation("Rank");
                });

            modelBuilder.Entity("Content.Server.Database.AdminWatchlist", b =>
                {
                    b.HasOne("Content.Server.Database.Player", "CreatedBy")
                        .WithMany("AdminWatchlistsCreated")
                        .HasForeignKey("CreatedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_admin_watchlists_player_created_by_id");

                    b.HasOne("Content.Server.Database.Player", "DeletedBy")
                        .WithMany("AdminWatchlistsDeleted")
                        .HasForeignKey("DeletedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_admin_watchlists_player_deleted_by_id");

                    b.HasOne("Content.Server.Database.Player", "LastEditedBy")
                        .WithMany("AdminWatchlistsLastEdited")
                        .HasForeignKey("LastEditedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_admin_watchlists_player_last_edited_by_id");

                    b.HasOne("Content.Server.Database.Player", "Player")
                        .WithMany("AdminWatchlistsReceived")
                        .HasForeignKey("PlayerUserId")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("FK_admin_watchlists_player_player_user_id");

                    b.HasOne("Content.Server.Database.Round", "Round")
                        .WithMany()
                        .HasForeignKey("RoundId")
                        .HasConstraintName("FK_admin_watchlists_round_round_id");

                    b.Navigation("CreatedBy");

                    b.Navigation("DeletedBy");

                    b.Navigation("LastEditedBy");

                    b.Navigation("Player");

                    b.Navigation("Round");
                });

            modelBuilder.Entity("Content.Server.Database.Antag", b =>
                {
                    b.HasOne("Content.Server.Database.Profile", "Profile")
                        .WithMany("Antags")
                        .HasForeignKey("ProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_antag_profile_profile_id");

                    b.Navigation("Profile");
                });

            modelBuilder.Entity("Content.Server.Database.ConnectionLog", b =>
                {
                    b.HasOne("Content.Server.Database.Server", "Server")
                        .WithMany("ConnectionLogs")
                        .HasForeignKey("ServerId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .IsRequired()
                        .HasConstraintName("FK_connection_log_server_server_id");

                    b.OwnsOne("Content.Server.Database.TypedHwid", "HWId", b1 =>
                        {
                            b1.Property<int>("ConnectionLogId")
                                .HasColumnType("integer")
                                .HasColumnName("connection_log_id");

                            b1.Property<byte[]>("Hwid")
                                .IsRequired()
                                .HasColumnType("bytea")
                                .HasColumnName("hwid");

                            b1.Property<int>("Type")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasDefaultValue(0)
                                .HasColumnName("hwid_type");

                            b1.HasKey("ConnectionLogId");

                            b1.ToTable("connection_log");

                            b1.WithOwner()
                                .HasForeignKey("ConnectionLogId")
                                .HasConstraintName("FK_connection_log_connection_log_connection_log_id");
                        });

                    b.Navigation("HWId");

                    b.Navigation("Server");
                });

            modelBuilder.Entity("Content.Server.Database.ConsentToggle", b =>
                {
                    b.HasOne("Content.Server.Database.ConsentSettings", "ConsentSettings")
                        .WithMany("ConsentToggles")
                        .HasForeignKey("ConsentSettingsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_consent_toggle_consent_settings_consent_settings_id");

                    b.Navigation("ConsentSettings");
                });

            modelBuilder.Entity("Content.Server.Database.Job", b =>
                {
                    b.HasOne("Content.Server.Database.Profile", "Profile")
                        .WithMany("Jobs")
                        .HasForeignKey("ProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_job_profile_profile_id");

                    b.Navigation("Profile");
                });

            modelBuilder.Entity("Content.Server.Database.Loadout", b =>
                {
                    b.HasOne("Content.Server.Database.Profile", "Profile")
                        .WithMany("Loadouts")
                        .HasForeignKey("ProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_loadout_profile_profile_id");

                    b.Navigation("Profile");
                });

            modelBuilder.Entity("Content.Server.Database.Player", b =>
                {
                    b.OwnsOne("Content.Server.Database.TypedHwid", "LastSeenHWId", b1 =>
                        {
                            b1.Property<int>("PlayerId")
                                .HasColumnType("integer")
                                .HasColumnName("player_id");

                            b1.Property<byte[]>("Hwid")
                                .IsRequired()
                                .HasColumnType("bytea")
                                .HasColumnName("last_seen_hwid");

                            b1.Property<int>("Type")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasDefaultValue(0)
                                .HasColumnName("last_seen_hwid_type");

                            b1.HasKey("PlayerId");

                            b1.ToTable("player");

                            b1.WithOwner()
                                .HasForeignKey("PlayerId")
                                .HasConstraintName("FK_player_player_player_id");
                        });

                    b.Navigation("LastSeenHWId");
                });

            modelBuilder.Entity("Content.Server.Database.Profile", b =>
                {
                    b.HasOne("Content.Server.Database.Preference", "Preference")
                        .WithMany("Profiles")
                        .HasForeignKey("PreferenceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_profile_preference_preference_id");

                    b.Navigation("Preference");
                });

            modelBuilder.Entity("Content.Server.Database.RoleWhitelist", b =>
                {
                    b.HasOne("Content.Server.Database.Player", "Player")
                        .WithMany("JobWhitelists")
                        .HasForeignKey("PlayerUserId")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_role_whitelists_player_player_user_id");

                    b.Navigation("Player");
                });

            modelBuilder.Entity("Content.Server.Database.Round", b =>
                {
                    b.HasOne("Content.Server.Database.Server", "Server")
                        .WithMany("Rounds")
                        .HasForeignKey("ServerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_round_server_server_id");

                    b.Navigation("Server");
                });

            modelBuilder.Entity("Content.Server.Database.ServerBan", b =>
                {
                    b.HasOne("Content.Server.Database.Player", "CreatedBy")
                        .WithMany("AdminServerBansCreated")
                        .HasForeignKey("BanningAdmin")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_server_ban_player_banning_admin");

                    b.HasOne("Content.Server.Database.Player", "LastEditedBy")
                        .WithMany("AdminServerBansLastEdited")
                        .HasForeignKey("LastEditedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_server_ban_player_last_edited_by_id");

                    b.HasOne("Content.Server.Database.Round", "Round")
                        .WithMany()
                        .HasForeignKey("RoundId")
                        .HasConstraintName("FK_server_ban_round_round_id");

                    b.OwnsOne("Content.Server.Database.TypedHwid", "HWId", b1 =>
                        {
                            b1.Property<int>("ServerBanId")
                                .HasColumnType("integer")
                                .HasColumnName("server_ban_id");

                            b1.Property<byte[]>("Hwid")
                                .IsRequired()
                                .HasColumnType("bytea")
                                .HasColumnName("hwid");

                            b1.Property<int>("Type")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasDefaultValue(0)
                                .HasColumnName("hwid_type");

                            b1.HasKey("ServerBanId");

                            b1.ToTable("server_ban");

                            b1.WithOwner()
                                .HasForeignKey("ServerBanId")
                                .HasConstraintName("FK_server_ban_server_ban_server_ban_id");
                        });

                    b.Navigation("CreatedBy");

                    b.Navigation("HWId");

                    b.Navigation("LastEditedBy");

                    b.Navigation("Round");
                });

            modelBuilder.Entity("Content.Server.Database.ServerBanHit", b =>
                {
                    b.HasOne("Content.Server.Database.ServerBan", "Ban")
                        .WithMany("BanHits")
                        .HasForeignKey("BanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_server_ban_hit_server_ban_ban_id");

                    b.HasOne("Content.Server.Database.ConnectionLog", "Connection")
                        .WithMany("BanHits")
                        .HasForeignKey("ConnectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_server_ban_hit_connection_log_connection_id");

                    b.Navigation("Ban");

                    b.Navigation("Connection");
                });

            modelBuilder.Entity("Content.Server.Database.ServerRoleBan", b =>
                {
                    b.HasOne("Content.Server.Database.Player", "CreatedBy")
                        .WithMany("AdminServerRoleBansCreated")
                        .HasForeignKey("BanningAdmin")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_server_role_ban_player_banning_admin");

                    b.HasOne("Content.Server.Database.Player", "LastEditedBy")
                        .WithMany("AdminServerRoleBansLastEdited")
                        .HasForeignKey("LastEditedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_server_role_ban_player_last_edited_by_id");

                    b.HasOne("Content.Server.Database.Round", "Round")
                        .WithMany()
                        .HasForeignKey("RoundId")
                        .HasConstraintName("FK_server_role_ban_round_round_id");

                    b.OwnsOne("Content.Server.Database.TypedHwid", "HWId", b1 =>
                        {
                            b1.Property<int>("ServerRoleBanId")
                                .HasColumnType("integer")
                                .HasColumnName("server_role_ban_id");

                            b1.Property<byte[]>("Hwid")
                                .IsRequired()
                                .HasColumnType("bytea")
                                .HasColumnName("hwid");

                            b1.Property<int>("Type")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasDefaultValue(0)
                                .HasColumnName("hwid_type");

                            b1.HasKey("ServerRoleBanId");

                            b1.ToTable("server_role_ban");

                            b1.WithOwner()
                                .HasForeignKey("ServerRoleBanId")
                                .HasConstraintName("FK_server_role_ban_server_role_ban_server_role_ban_id");
                        });

                    b.Navigation("CreatedBy");

                    b.Navigation("HWId");

                    b.Navigation("LastEditedBy");

                    b.Navigation("Round");
                });

            modelBuilder.Entity("Content.Server.Database.ServerRoleUnban", b =>
                {
                    b.HasOne("Content.Server.Database.ServerRoleBan", "Ban")
                        .WithOne("Unban")
                        .HasForeignKey("Content.Server.Database.ServerRoleUnban", "BanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_server_role_unban_server_role_ban_ban_id");

                    b.Navigation("Ban");
                });

            modelBuilder.Entity("Content.Server.Database.ServerUnban", b =>
                {
                    b.HasOne("Content.Server.Database.ServerBan", "Ban")
                        .WithOne("Unban")
                        .HasForeignKey("Content.Server.Database.ServerUnban", "BanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_server_unban_server_ban_ban_id");

                    b.Navigation("Ban");
                });

            modelBuilder.Entity("Content.Server.Database.Trait", b =>
                {
                    b.HasOne("Content.Server.Database.Profile", "Profile")
                        .WithMany("Traits")
                        .HasForeignKey("ProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_trait_profile_profile_id");

                    b.Navigation("Profile");
                });

            modelBuilder.Entity("PlayerRound", b =>
                {
                    b.HasOne("Content.Server.Database.Player", null)
                        .WithMany()
                        .HasForeignKey("PlayersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_player_round_player_players_id");

                    b.HasOne("Content.Server.Database.Round", null)
                        .WithMany()
                        .HasForeignKey("RoundsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_player_round_round_rounds_id");
                });

            modelBuilder.Entity("Content.Server.Database.Admin", b =>
                {
                    b.Navigation("Flags");
                });

            modelBuilder.Entity("Content.Server.Database.AdminLog", b =>
                {
                    b.Navigation("Players");
                });

            modelBuilder.Entity("Content.Server.Database.AdminRank", b =>
                {
                    b.Navigation("Admins");

                    b.Navigation("Flags");
                });

            modelBuilder.Entity("Content.Server.Database.ConnectionLog", b =>
                {
                    b.Navigation("BanHits");
                });

            modelBuilder.Entity("Content.Server.Database.ConsentSettings", b =>
                {
                    b.Navigation("ConsentToggles");
                });

            modelBuilder.Entity("Content.Server.Database.Player", b =>
                {
                    b.Navigation("AdminLogs");

                    b.Navigation("AdminMessagesCreated");

                    b.Navigation("AdminMessagesDeleted");

                    b.Navigation("AdminMessagesLastEdited");

                    b.Navigation("AdminMessagesReceived");

                    b.Navigation("AdminNotesCreated");

                    b.Navigation("AdminNotesDeleted");

                    b.Navigation("AdminNotesLastEdited");

                    b.Navigation("AdminNotesReceived");

                    b.Navigation("AdminServerBansCreated");

                    b.Navigation("AdminServerBansLastEdited");

                    b.Navigation("AdminServerRoleBansCreated");

                    b.Navigation("AdminServerRoleBansLastEdited");

                    b.Navigation("AdminWatchlistsCreated");

                    b.Navigation("AdminWatchlistsDeleted");

                    b.Navigation("AdminWatchlistsLastEdited");

                    b.Navigation("AdminWatchlistsReceived");

                    b.Navigation("JobWhitelists");
                });

            modelBuilder.Entity("Content.Server.Database.Preference", b =>
                {
                    b.Navigation("Profiles");
                });

            modelBuilder.Entity("Content.Server.Database.Profile", b =>
                {
                    b.Navigation("Antags");

                    b.Navigation("Jobs");

                    b.Navigation("Loadouts");

                    b.Navigation("Traits");
                });

            modelBuilder.Entity("Content.Server.Database.Round", b =>
                {
                    b.Navigation("AdminLogs");
                });

            modelBuilder.Entity("Content.Server.Database.Server", b =>
                {
                    b.Navigation("ConnectionLogs");

                    b.Navigation("Rounds");
                });

            modelBuilder.Entity("Content.Server.Database.ServerBan", b =>
                {
                    b.Navigation("BanHits");

                    b.Navigation("Unban");
                });

            modelBuilder.Entity("Content.Server.Database.ServerRoleBan", b =>
                {
                    b.Navigation("Unban");
                });
#pragma warning restore 612, 618
        }
    }
}
