- type: construction
  name: "павутинна плитка"
  id: TileWeb
  graph: WebObjects
  startNode: start
  targetNode: tile
  category: construction-category-tiles
  description: "Приємна і гладка."
  entityWhitelist:
    tags:
    - SpiderCraft
  icon:
    sprite: Objects/Tiles/web.rsi
    state: icon
  objectType: Item

- type: construction
  name: "зимове пальто з павутини"
  id: ClothingOuterWinterWeb
  graph: WebObjects
  startNode: start
  targetNode: coat
  category: construction-category-clothing
  description: "Напрочуд тепле і міцне."
  entityWhitelist:
    tags:
    - SpiderCraft
  icon:
    sprite: Clothing/OuterClothing/WinterCoats/coatweb.rsi
    state: icon
  objectType: Item

- type: construction
  name: "павутинний комбінезон"
  id: ClothingUniformJumpsuitWeb
  graph: WebObjects
  startNode: start
  targetNode: jumpsuit
  category: construction-category-clothing
  description: "Принаймні, це вже щось."
  entityWhitelist:
    tags:
    - SpiderCraft
  icon:
    sprite: Clothing/Uniforms/Jumpsuit/web.rsi
    state: icon
  objectType: Item

- type: construction
  name: "спідниця з павутини"
  id: ClothingUniformJumpskirtWeb
  graph: WebObjects
  startNode: start
  targetNode: jumpskirt
  category: construction-category-clothing
  description: "Принаймні, це вже щось."
  entityWhitelist:
    tags:
    - SpiderCraft
  icon:
    sprite: Clothing/Uniforms/Jumpskirt/web.rsi
    state: icon
  objectType: Item

- type: construction
  name: "шовкова ткана тканина"
  id: SilkWovenCloth
  graph: WebObjects
  startNode: start
  targetNode: cloth
  category: construction-category-materials
  description: "На дотик, як не дивно, як тканина."
  entityWhitelist:
    tags:
    - SpiderCraft
  icon:
    sprite: Objects/Materials/materials.rsi
    state: cloth_3
  objectType: Item

- type: construction
  name: "павутинний щит"
  id: WebShield
  graph: WebObjects
  startNode: start
  targetNode: shield
  category: construction-category-clothing
  description: "Він достатньо товстий, щоб витримати кілька ударів, але, мабуть, не нагрівається."
  entityWhitelist:
    tags:
    - SpiderCraft
  icon:
    sprite: Objects/Weapons/Melee/web-shield.rsi
    state: icon
  objectType: Item

- type: construction
  name: "зимові черевики з павутини"
  id: ClothingShoesBootsWinterWeb
  graph: WebObjects
  startNode: start
  targetNode: boots
  category: construction-category-clothing
  description: "Щільно сплетене полотно повинно захистити від холоду"
  entityWhitelist:
    tags:
    - SpiderCraft
  icon:
    sprite: Clothing/Shoes/Boots/winterbootsweb.rsi
    state: icon
  objectType: Item
