- type: entity
  id: BaseCartridgePistol
  name: "патрон (.35 auto)"
  parent: BaseCartridge
  abstract: true
  components:
  - type: Tag
    tags:
      - Cartridge
      - CartridgePistol
  - type: CartridgeAmmo
    proto: BulletPistol
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Casings/ammo_casing.rsi
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
  - type: Appearance
  - type: SpentAmmoVisuals

- type: entity
  id: CartridgePistol
  name: "патрон (.35 auto)"
  parent: BaseCartridgePistol
  components:
  - type: CartridgeAmmo
    proto: BulletPistol

- type: entity
  id: CartridgePistolPractice
  name: "патрон (.35 auto practice)"
  parent: BaseCartridgePistol
  components:
  - type: CartridgeAmmo
    proto: BulletPistolPractice
  -  type: Sprite
     layers:
       - state: base
         map: [ "enum.AmmoVisualLayers.Base" ]
       - state: tip
         map: [ "enum.AmmoVisualLayers.Tip" ]
         color: "#dbdbdb"

- type: entity
  id: CartridgePistolRubber
  name: "наб<PERSON>й (.35 авто гумовий)"
  parent: BaseCartridgePistol
  components:
  - type: CartridgeAmmo
    proto: BulletPistolRubber
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#43c4f7"

- type: entity
  id: CartridgePistolIncendiary
  name: "набій (.35 авто запальний)"
  parent: BaseCartridgePistol
  components:
  - type: CartridgeAmmo
    proto: BulletPistolIncendiary
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#ff6e52"

- type: entity
  id: CartridgePistolUranium
  name: "патрон (.35 auto uranium)"
  parent: BaseCartridgePistol
  components:
  - type: CartridgeAmmo
    proto: BulletPistolUranium
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#65fe08"

- type: entity
  id: CartridgePistolShrapnel
  name: "патрон (.35 авто, шрапнельний)"
  parent: BaseCartridgePistol
  components:
  - type: CartridgeAmmo
    proto: BulletPistolShrapnelSpread
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#FF00FF"
