- type: constructionGraph
  id: WindowDirectional
  start: start
  graph:
    - node: start
      edges:

        - to: windowDirectional
          steps:
            - material: Glass
              amount: 1
              doAfter: 2

        - to: windowReinforcedDirectional
          steps:
            - material: ReinforcedGlass
              amount: 1
              doAfter: 3

        - to: plasmaWindowDirectional
          steps:
            - material: PlasmaGlass
              amount: 1
              doAfter: 2

        - to: plasmaReinforcedWindowDirectional
          steps:
            - material: ReinforcedPlasmaGlass
              amount: 1
              doAfter: 3

        - to: uraniumWindowDirectional
          steps:
            - material: UraniumGlass
              amount: 1
              doAfter: 2

        - to: uraniumReinforcedWindowDirectional
          steps:
            - material: ReinforcedUraniumGlass
              amount: 1
              doAfter: 3

        - to: windowClockworkDirectional
          steps:
            - material: ClockworkGlass
              amount: 1
              doAfter: 3

    - node: windowDirectional
      entity: WindowDirectional
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: SheetGlass1
              amount: 1
            - !type:DeleteEntity {}
          steps:
            - tool: Screwing
              doAfter: 1
            - tool: Anchoring
              doAfter: 2

    - node: windowReinforcedDirectional
      entity: WindowReinforcedDirectional
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: SheetRGlass1
              amount: 1
            - !type:DeleteEntity {}
          steps:
            - tool: Screwing
              doAfter: 1
            - tool: Prying
              doAfter: 2
            - tool: Screwing
              doAfter: 1
            - tool: Anchoring
              doAfter: 2

    - node: plasmaWindowDirectional
      entity: PlasmaWindowDirectional
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: SheetPGlass1
              amount: 1
            - !type:DeleteEntity {}
          steps:
            - tool: Screwing
              doAfter: 2
            - tool: Prying
              doAfter: 3
            - tool: Screwing
              doAfter: 2
            - tool: Anchoring
              doAfter: 3

    - node: windowClockworkDirectional
      entity: WindowClockworkDirectional
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: SheetClockworkGlass1
              amount: 1
            - !type:DeleteEntity {}
          steps:
            - tool: Screwing
              doAfter: 2
            - tool: Prying
              doAfter: 3
            - tool: Screwing
              doAfter: 2
            - tool: Anchoring
              doAfter: 3

    - node: plasmaReinforcedWindowDirectional
      entity: PlasmaReinforcedWindowDirectional
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: SheetRPGlass1
              amount: 1
            - !type:DeleteEntity {}
          steps:
            - tool: Screwing
              doAfter: 2
            - tool: Prying
              doAfter: 3
            - tool: Screwing
              doAfter: 2
            - tool: Anchoring
              doAfter: 3
              
    - node: uraniumWindowDirectional
      entity: UraniumWindowDirectional
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: SheetUGlass1
              amount: 1
            - !type:DeleteEntity {}
          steps:
            - tool: Screwing
              doAfter: 2
            - tool: Prying
              doAfter: 3
            - tool: Screwing
              doAfter: 2
            - tool: Anchoring
              doAfter: 3

    - node: uraniumReinforcedWindowDirectional
      entity: UraniumReinforcedWindowDirectional
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: SheetRUGlass1
              amount: 1
            - !type:DeleteEntity {}
          steps:
            - tool: Screwing
              doAfter: 2
            - tool: Prying
              doAfter: 3
            - tool: Screwing
              doAfter: 2
            - tool: Anchoring
              doAfter: 3
