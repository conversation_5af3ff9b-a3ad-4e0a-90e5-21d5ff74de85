# see adverts for sign base
# see street_furniture for floor signs

# 32x32
- type: entity
  parent: SignBase
  id: SignBar2
  name: "вказівник бару"
  description: "Бар! Напивайся тут."
  components:
  - type: Sprite
    sprite: Structures/Wallmounts/signs_32x32.rsi
    state: bar

- type: entity
  parent: SignBar2
  id: SignClinic
  name: "вказівник клініки"
  description: "Вивіска клініки. Сподіваюся, у них є ліки."
  components:
  - type: Sprite
    state: clinic
  - type: PointLight
    radius: 3
    energy: 1
    color: '#00ff00'

- type: entity
  parent: SignBar2
  id: SignOpen1
  name: "знак \"Відкрито"
  description: "Відкрито для бізнесу. Можливо."
  components:
  - type: Sprite
    state: open
  - type: PointLight
    radius: 3
    energy: 1
    color: '#ff0000'

- type: entity
  parent: SignOpen1
  id: SignOpen2
  components:
  - type: Sprite
    state: open_bar

- type: entity
  parent: SignOpen1
  id: SignOpenOn1
  components:
  - type: Sprite
    state: open_on

- type: entity
  parent: SignOpen1
  id: SignOpenOn2
  components:
  - type: Sprite
    state: open_bar_on

- type: entity
  parent: SignBase
  id: SignForRent
  name: "вивіска \"Здається в оренду"
  description: "Вивіска з оголошенням про оренду житла."
  components:
  - type: Sprite
    sprite: Structures/Wallmounts/signs_32x32.rsi
    state: rent

- type: entity
  parent: SignBase
  id: SignNotice
  name: "знак попередження"
  description: "УВАГА!"
  components:
  - type: Sprite
    sprite: Structures/Wallmounts/walldecor.rsi
    state: notice_sign

- type: entity
  parent: SignNotice
  id: SignDanger2
  name: "знак небезпеки"
  description: "Небезпека."
  components:
  - type: Sprite
    state: danger_sign

- type: entity
  parent: SignNotice
  id: WallDecorExitsign
  name: "знак виходу"
  description: "Вивіска з написом \"ВИХІД\". Цікаво, що це означає."
  components:
  - type: Sprite
    state: exit
    noRot: true

# 64x32
- type: entity
  parent: SignBar2
  id: SignBazaarOn
  name: "базарна вивіска"
  description: "Вивіска базару. Як дивно."
  components:
  - type: Sprite
    sprite: Structures/Wallmounts/signs_64x32.rsi
    state: bazaar_on
  - type: PointLight
    radius: 2
    energy: 1
    color: '#ff8000'

- type: entity
  parent: SignBazaarOn
  id: SignHotel
  name: "вивіска готелю"
  description: "Вивіска готелю. Зніміть номер!"
  components:
  - type: Sprite
    state: hotel

- type: entity
  parent: SignBazaarOn
  id: SignPrivateProperty
  name: "знак \"Приватна Власність"
  description: "Знак приватної власності."
  components:
  - type: Sprite
    state: private

- type: entity
  parent: SignBazaarOn
  id: SignOpenBig
  name: "знак \"Відкрито"
  description: "Ми відкриті. Сподіваюсь на це..."
  components:
  - type: Sprite
    state: we_open_open
  - type: PointLight
    radius: 2
    energy: 1
    color: '#ff0000'

- type: entity
  parent: SignBazaarOn
  id: SignWorkersOnly
  name: "знак \"Тільки для Працівників"
  description: "Ніяких перехожих!"
  components:
  - type: Sprite
    state: workers
