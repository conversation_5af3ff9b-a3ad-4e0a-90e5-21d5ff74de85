- type: entity
  name: "летюча миша"
  parent: [ SimpleMobB<PERSON>, FlyingMobBase, MobCombat ]
  id: MobBat
  description: "Деякі культури вважають їх жахливими, інші - хрусткими на зубах."
  components:
  - type: MovementSpeedModifier
    baseWalkSpeed : 3
    baseSprintSpeed : 6
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: bat
      sprite: Mobs/Animals/bat.rsi
  - type: Carriable
  - type: Speech
    speechSounds: Squeak
    speechVerb: SmallMob
    allowedEmotes: ['Squeak']
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.25
        density: 0.8
        mask:
        - FlyingMobMask
        layer:
        - FlyingMobLayer
  - type: DamageStateVisuals
    states:
      Alive:
        Base: bat
      Dead:
        Base: dead
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 1
  - type: InteractionPopup
    successChance: 0.2
    interactSuccessString: petting-success-soft-floofy
    interactFailureString: petting-failure-bat
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/fox_squeak.ogg
  - type: SentienceTarget
    flavorKind: station-event-random-sentience-flavor-organic
  - type: Bloodstream
    bloodMaxVolume: 50
  - type: LanguageKnowledge
    speaks:
    - Mouse
    understands:
    - Mouse
  - type: MeleeWeapon
    soundHit:
        path: /Audio/Effects/bite.ogg
    angle: 0
    animation: WeaponArcBite
    damage:
      types:
        Piercing: 5
  - type: Tag
    tags:
    - VimPilot
  - type: RandomBark
    barkType: mouse
    minTime: 10 # Mice like to squeak, I think. You can always put your pet mouse to sleep if it gets annoying
    maxTime: 160
  - type: NightVision
    isActive: true
    toggleAction: null
    color: "#808080"
    activateSound: null
    deactivateSound: null

- type: entity
  name: "бджола"
  parent: [ SimpleMobBase, FlyingMobBase ]
  id: MobBee
  description: "Приємно мати, але на одному меді цивілізацію не побудуєш."
  components:
  - type: CombatMode
  - type: MovementSpeedModifier
    baseWalkSpeed : 7
    baseSprintSpeed : 7
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: 0
      sprite: Mobs/Animals/bee.rsi
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.1
        density: 0.1
        mask:
        - FlyingMobMask
        layer:
        - FlyingMobLayer
  - type: MobState
  - type: MobThresholds
    thresholds:
      0: Alive
      5: Dead # Goob edit
  - type: Stamina
    critThreshold: 10
  - type: DamageStateVisuals
    states:
      Alive:
        Base: 0
      Dead:
        Base: dead
  - type: Item
    size: Tiny
  - type: Tag
    tags:
    - Bee
    - Trash
  - type: Bloodstream
    bloodMaxVolume: 0.1
  - type: MobPrice
    price: 50
  - type: NPCRetaliation
  - type: FactionException
  - type: NpcFactionMember
    factions:
    - Passive
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
  - type: Extractable # DeltaV - Make it so bees can be ground up
    grindableSolutionName: bee
  - type: SolutionContainerManager
    solutions:
      bee:
        reagents:
        - ReagentId: GroundBee
          Quantity: 5
  - type: ZombieImmune
  - type: RandomBark
    barkType: bee
    barkMultiplier: 1.5

- type: entity
  name: "бджола"
  suffix: Angry
  parent: [ MobBee, MobCombat ]
  id: MobAngryBee
  description: "Яка гарна бджілка. О ні, вона виглядає сердитою і хоче мою піцу."
  components:
  - type: CombatMode
  - type: MeleeWeapon
    angle: 0
    animation: WeaponArcBite
    damage:
      types:
        Poison: 2
        Piercing: 1
  - type: InputMover
  - type: MobMover
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
  - type: NpcFactionMember
    factions:
    - SimpleHostile
  - type: Bloodstream
    bloodMaxVolume: 0.1
  - type: ZombieImmune


- type: entity
  name: "курка"
  parent: SimpleMobBase
  id: MobChicken
  description: "З'явився раніше яйця і є динозавром!"
  components:
  - type: Absorbable
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base", "movement"]
      state: chicken-0
      sprite: Mobs/Animals/chicken.rsi
  - type: SpriteMovement
    movementLayers:
      movement:
        state: chicken-moving-0
    noMovementLayers:
      movement:
        state: chicken-0
  - type: Carriable
    freeHandsRequired: 1
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 8
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Tag
    tags:
    - DoorBumpOpener
    - Chicken
    - VimPilot
  - type: Reproductive
    breedChance: 0.05
    birthPopup: reproductive-laid-egg-popup
    makeOffspringInfant: false
    partnerWhitelist:
      tags:
      - Chicken
    offspring:
    - id: FoodEggChickenFertilized
      maxAmount: 3
  - type: ReproductivePartner
  - type: Appearance
  - type: DamageStateVisuals
    states:
      Alive:
        Base: chicken-0
      Critical:
        Base: dead-0
      Dead:
        Base: dead-0
  - type: Butcherable
    spawned:
    - id: FoodMeatChicken
      amount: 1
  - type: InteractionPopup
    successChance: 0.8
    interactSuccessString: petting-success-bird
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/chicken_cluck_happy.ogg
  - type: Bloodstream
    bloodMaxVolume: 100
  - type: EggLayer
    eggSpawn:
    - id: FoodEgg
  - type: LanguageKnowledge
    speaks:
    - Chicken
    understands:
    - Chicken
  - type: SentienceTarget
    flavorKind: station-event-random-sentience-flavor-organic
  - type: NpcFactionMember
    factions:
    - Passive
  - type: RandomBark
    barkType: chicken

- type: entity
  parent: MobChicken
  id: MobChicken1
  components:
  - type: Absorbable
  - type: Sprite
    layers:
    - map: ["enum.DamageStateVisualLayers.Base", "movement"]
      state: chicken-1
      sprite: Mobs/Animals/chicken.rsi
  - type: SpriteMovement
    movementLayers:
      movement:
        state: chicken-moving-1
    noMovementLayers:
      movement:
        state: chicken-1
  - type: DamageStateVisuals
    states:
      Alive:
        Base: chicken-1
      Critical:
        Base: dead-1
      Dead:
        Base: dead-1

- type: entity
  parent: MobChicken
  id: MobChicken2
  components:
  - type: Absorbable
  - type: Sprite
    layers:
    - map: ["enum.DamageStateVisualLayers.Base", "movement"]
      state: chicken-2
      sprite: Mobs/Animals/chicken.rsi
  - type: SpriteMovement
    movementLayers:
      movement:
        state: chicken-moving-2
    noMovementLayers:
      movement:
        state: chicken-2
  - type: DamageStateVisuals
    states:
      Alive:
        Base: chicken-2
      Critical:
        Base: dead-2
      Dead:
        Base: dead-2

- type: entity
  id: FoodEggChickenFertilized
  parent: FoodEgg
  suffix: Fertilized, Chicken
  components:
  - type: Timer
  - type: TimedSpawner
    prototypes:
    - MobChicken
    - MobChicken1
    - MobChicken2
    intervalSeconds: 20
    minimumEntitiesSpawned: 1
    maximumEntitiesSpawned: 1
  - type: TimedDespawn #delete the egg after the chicken spawns
    lifetime: 21

- type: entity # TODO: figure out how to make these guys gib when stepped on
  name: "тарган"
  parent: SimpleMobBase
  id: MobCockroach
  description: "Ця станція просто кишить жуками."
  components:
  - type: Sprite
    drawdepth: SmallMobs
    sprite: Mobs/Animals/cockroach.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: cockroach
  - type: Item
    size: Tiny
  - type: HTN
    rootTask:
      task: MouseCompound
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.2
        density: 0.0007
        mask:
        - SmallMobMask
        layer:
        - SmallMobLayer
  - type: MobState
  - type: MobThresholds
    thresholds:
      0: Alive
      1: Dead
  - type: MovementSpeedModifier
    baseWalkSpeed : 5
    baseSprintSpeed : 5
  - type: DamageStateVisuals
    states:
      Alive:
        Base: cockroach
      Dead:
        Base: cockroach_dead
  - type: Bloodstream
    bloodReagent: InsectBlood
    bloodMaxVolume: 20
  - type: Food
  - type: Hunger
    baseDecayRate: 0.25
  - type: Extractable
    grindableSolutionName: food
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Slime
          Quantity: 5
  - type: Butcherable
    spawned:
    - id: FoodMeatSlime
  - type: Damageable
    damageContainer: Biological
    damageModifierSet: Cockroach
  - type: Tag
    tags:
    - Trash
    - VimPilot
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTypeTrigger
        damageType: Blunt
        damage: 10
      behaviors:
      - !type:GibBehavior
        gibContents: Skip
        recursive: false
  - type: NonSpreaderZombie
  - type: LanguageKnowledge
    speaks: [Hissing]
    understands: [Hissing]

- type: entity
  parent: MobCockroach
  id: MobCockroachPet
  components:
  - type: HTN
    rootTask:
      task: FollowCompound
    blackboard:
      IdleRange: !type:Single
        1.5
      FollowCloseRange: !type:Single
        1.0
      FollowRange: !type:Single
        2.0
  - type: SentienceTarget
    flavorKind: station-event-random-sentience-flavor-organic

- type: entity
  name: "тарган"
  parent: MobCockroach
  suffix: Admeme
  id: MobGlockroach
  description: "Ця станція просто кишить жу- О ГОСПОДИ, У НЬОГО ЗБРОЯ!!!"
  components:
  - type: Sprite
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: glockroach
  - type: Gun
    fireRate: 2
    useKey: false
    selectedMode: SemiAuto
    availableModes:
      - SemiAuto
    soundGunshot: /Audio/Weapons/Guns/Gunshots/pistol.ogg
  - type: BallisticAmmoProvider
    proto: CartridgeCaselessRifle
    capacity: 500
  - type: CombatMode
  - type: HTN
    rootTask:
      task: GlockroachCompound
  - type: NpcFactionMember
    factions:
    - SimpleHostile
  - type: Butcherable
    spawned:
    - id: FoodMeatSlime
  - type: VentCrawler # goobstation - Ventcrawl

- type: entity
  name: "тарганоміль"
  parent: MobCockroach
  id: MobMothroach
  description: "Це чарівний побічний продукт численних спроб генетичного змішування метеликів з тарганами."
  components:
  - type: GhostRole
    makeSentient: true
    allowSpeech: true
    allowMovement: true
    name: ghost-role-information-mothroach-name
    description: ghost-role-information-mothroach-description
    rules: ghost-role-information-freeagent-rules
    mindRoles:
    - MindRoleGhostRoleFreeAgent
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.2
        density: 0.007
        mask:
        - SmallMobMask
        layer:
        - SmallMobLayer
  - type: GhostTakeoverAvailable
  - type: Speech
    speechVerb: Moth
    speechSounds: Chitter
    allowedEmotes: ['Chitter', 'Squeak']
  - type: FaxableObject
    insertingState: inserting_mothroach
  - type: MothAccent
  - type: Sprite
    sprite: Mobs/Animals/mothroach.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base", "movement"]
      state: mothroach
  - type: SpriteMovement
    movementLayers:
      movement:
        state: mothroach-moving
    noMovementLayers:
      movement:
        state: mothroach
  - type: Item
    size: Normal
  - type: Clothing
    quickEquip: false
    sprite: Mobs/Animals/mothroach.rsi
    equippedPrefix: 0
    slots:
    - HEAD
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
  - type: Appearance
  - type: DamageStateVisuals
    states:
      Alive:
        Base: mothroach
      Critical:
        Base: mothroach_dead
      Dead:
        Base: mothroach_dead
  - type: MobThresholds
    thresholds:
      0: Alive
      40: Critical
      60: Dead
  - type: MovementSpeedModifier
    baseWalkSpeed : 2.5
    baseSprintSpeed : 4
    weightlessAcceleration: 1.5
    weightlessFriction: 1
    weightlessModifier: 1
  - type: Damageable
    damageContainer: Biological
    damageModifierSet: Moth
  - type: Bloodstream
    bloodReagent: InsectBlood
  - type: Respirator
    damage:
      types:
        Asphyxiation: 0.5
    damageRecovery:
      types:
        Asphyxiation: -0.5
  - type: CombatMode
  - type: Butcherable
    spawned:
    - id: FoodMeatRat
      amount: 2
    - id: HideMothroach # Gotta make the plushies
      amount: 1
      prob: 0.5
  - type: Extractable
    grindableSolutionName: food
  - type: LanguageKnowledge
    speaks:
    - Moffic
    understands:
    - Moffic
  - type: ZombieAccentOverride
    accent: zombieMoth
  - type: Vocal
    sounds:
      Male: UnisexMoth
      Female: UnisexMoth
      Unsexed: UnisexMoth
    wilhelmProbability: 0.001
  - type: MobPrice
    price: 150
  - type: Tag
    tags:
    - Trash
    - CannotSuicide
    - DoorBumpOpener # DeltaV - After all, why not? Why shouldn't they be able to open doors?
    - VimPilot
  - type: CanEscapeInventory
  - type: NpcFactionMember
    factions:
    - Mouse
  - type: Body
    prototype: Mothroach
  - type: TypingIndicator
    proto: moth
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTypeTrigger
        damageType: Blunt
        damage: 60
      behaviors:
      - !type:GibBehavior { }
  - type: FireVisuals
    sprite: Mobs/Effects/onfire.rsi
    normalState: Mouse_burning
  - type: SurgeryTarget
  - type: UserInterface
    interfaces:
      enum.SurgeryUIKey.Key:
        type: SurgeryBui

- type: entity
  parent: MobMothroach
  id: MobMothroachPet
  components:
  - type: HTN
    rootTask:
      task: FollowCompound
    blackboard:
      IdleRange: !type:Single
        1.5
      FollowCloseRange: !type:Single
        1.0
      FollowRange: !type:Single
        2.0

- type: entity
  name: "сквак-тарган"
  parent: MobCockroach
  id: MobSquackroach
  description: "Це просто жахлива генетична мішанина, а не екземпляр."
  components:
  - type: Singer
    proto: HarpySinger
  - type: GhostRole
    makeSentient: true
    allowSpeech: true
    allowMovement: true
    name: ghost-role-information-squackroach-name
    description: ghost-role-information-squackroach-description
    rules: ghost-role-information-freeagent-rules
    mindRoles:
    - MindRoleGhostRoleFreeAgent
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.2
        density: 0.007
        mask:
        - SmallMobMask
        layer:
        - SmallMobLayer
  - type: GhostTakeoverAvailable
  - type: Speech
    speechVerb: Harpy
    speechSounds: Harpy
  - type: FaxableObject
    insertingState: inserting_squackroach
  - type: Sprite
    sprite: Mobs/Animals/squackroach.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base", "movement"]
      state: squackroach
    - map: [ "singingLayer" ]
      sprite: Effects/harpysinger.rsi
      state: singing_music_notes
      visible: false
  - type: SpriteMovement
    movementLayers:
      movement:
        state: squackroach-moving
    noMovementLayers:
      movement:
        state: squackroach
  - type: Item
    size: Normal
  - type: Clothing
    quickEquip: false
    sprite: Mobs/Animals/squackroach.rsi
    equippedPrefix: sq
    slots:
    - HEAD
    - NECK
  - type: Appearance
  - type: DamageStateVisuals
    states:
      Alive:
        Base: squackroach
      Critical:
        Base: squackroach_dead
      Dead:
        Base: squackroach_dead
  - type: MobThresholds
    thresholds:
      0: Alive
      40: Critical
      60: Dead
  - type: MovementSpeedModifier
    baseWalkSpeed : 2.5
    baseSprintSpeed : 4
    weightlessAcceleration: 1.5
    weightlessFriction: 1
    weightlessModifier: 1.12
  - type: Damageable
    damageContainer: Biological
    damageModifierSet: Moth
  - type: Bloodstream
    bloodReagent: InsectBlood # it's still somewhat an insect... somehow
  - type: Respirator
  - type: CombatMode
  - type: Butcherable
    spawned:
    - id: FoodMeatRat
      amount: 2
  - type: Extractable
    grindableSolutionName: food
  - type: LanguageKnowledge
    speaks:
    - ValyrianStandard
    understands:
    - ValyrianStandard
  - type: Vocal
    sounds:
      Male: SoundsHarpy
      Female: SoundsHarpy
      Unsexed: SoundsHarpy
  - type: GenericVisualizer
    visuals:
      enum.HarpyVisualLayers.Singing:
        singingLayer:
          False: { visible: false }
          True: { visible: true }
    wilhelmProbability: 0.001
  - type: MobPrice
    price: 150
  - type: Tag
    tags:
    - Trash
    - CannotSuicide
    - DoorBumpOpener
    - HarpyEmotes
    - VimPilot
  - type: CanEscapeInventory
  - type: NpcFactionMember
    factions:
    - Mouse
  - type: Body
    prototype: Squackroach
  - type: StepTriggerImmune
    whitelist:
      types:
      - Shard
      - Landmine
      - Mousetrap
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTypeTrigger
        damageType: Blunt
        damage: 60
      behaviors:
      - !type:GibBehavior { }
  - type: FireVisuals
    sprite: Mobs/Effects/onfire.rsi
    normalState: Mouse_burning
  - type: FootPrints
    leftBarePrint: "footprint-left-bare-lizard"
    rightBarePrint: "footprint-right-bare-lizard"
  - type: SurgeryTarget
  - type: UserInterface
    interfaces:
      enum.SurgeryUIKey.Key:
        type: SurgeryBui
      enum.InstrumentUiKey.Key:
        type: InstrumentBoundUserInterface
        requireInputValidation: false

- type: entity
  parent: MobSquackroach
  id: MobSquackroachPet
  components:
  - type: HTN
    rootTask:
      task: FollowCompound
    blackboard:
      IdleRange: !type:Single
        1.5
      FollowCloseRange: !type:Single
        1.0
      FollowRange: !type:Single
        2.0

# Note that the mallard duck is actually a male drake mallard, with the brown duck being the female variant of the same species, however ss14 lacks sex specific textures
# The white duck is more akin to a pekin or call duck.

- type: entity
  name: "качка крижень" #Quack
  parent: SimpleMobBase
  id: MobDuckMallard
  description: "Чарівна качка крижень, пухнаста і м'яка!"
  components:
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: duck-0
    sprite: Mobs/Animals/duck.rsi
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 5 #They actually are pretty light, I looked it up
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Carriable
    freeHandsRequired: 1
  - type: Tag
    tags:
    - DoorBumpOpener
    - Duck
    - VimPilot
  - type: Reproductive
    breedChance: 0.05
    birthPopup: reproductive-laid-egg-popup
    makeOffspringInfant: false
    partnerWhitelist:
      tags:
      - Duck
    offspring:
    - id: FoodEggDuckFertilized
      maxAmount: 3
  - type: ReproductivePartner
  - type: Appearance
  - type: DamageStateVisuals
    states:
      Alive:
        Base: duck-0
      Dead:
        Base: dead-0
  - type: Butcherable
    spawned:
    - id: FoodMeatDuck
      amount: 1
  - type: InteractionPopup
    successChance: 0.9
    interactSuccessString: petting-success-bird
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/duck_quack_happy.ogg
  - type: Bloodstream
    bloodMaxVolume: 100
  - type: EggLayer
    eggSpawn:
    - id: FoodEgg
  - type: LanguageKnowledge
    speaks:
    - Duck
    understands:
    - Duck
  - type: SentienceTarget
    flavorKind: station-event-random-sentience-flavor-organic
  - type: NpcFactionMember
    factions:
    - Passive
  - type: RandomBark
    barkType: chicken # Duh
    barkMultiplier: 0.7
  - type: SurgeryTarget
  - type: UserInterface
    interfaces:
      enum.SurgeryUIKey.Key:
        type: SurgeryBui

- type: entity
  name: "біла качка" #Quack
  parent: MobDuckMallard
  id: MobDuckWhite
  description: "Чарівна біла качка, пухнаста і м'яка!"
  components:
  - type: Sprite
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: duck-1
  - type: DamageStateVisuals
    states:
      Alive:
        Base: duck-1
      Dead:
        Base: dead-1

- type: entity
  name: "коричнева качка" #Quack
  parent: MobDuckMallard
  id: MobDuckBrown
  description: "Чарівна коричнева качка, пухнаста і м'яка!"
  components:
  - type: Sprite
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: duck-2
  - type: DamageStateVisuals
    states:
      Alive:
        Base: duck-2
      Dead:
        Base: dead-2

- type: entity
  id: FoodEggDuckFertilized
  parent: FoodEgg
  suffix: Fertilized, Duck
  components:
    - type: Timer
    - type: TimedSpawner
      prototypes:
      - MobDuckMallard
      - MobDuckWhite
      - MobDuckBrown
      intervalSeconds: 20
      minimumEntitiesSpawned: 1
      maximumEntitiesSpawned: 1
    - type: TimedDespawn #delete the egg after the chicken spawns
      lifetime: 21

- type: entity
  name: "метелик"
  parent: [ SimpleMobBase, FlyingMobBase ]
  id: MobButterfly
  description: "Всупереч поширеному стереотипу, насправді в головах вони не бувають."
  components:
  - type: MovementSpeedModifier
    baseWalkSpeed : 6
    baseSprintSpeed : 6
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: butterfly
      sprite: Mobs/Animals/butterfly.rsi
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.2
        density: 7.5
        mask:
        - FlyingMobMask
        layer:
        - FlyingMobLayer
  - type: MobState
  - type: MobThresholds
    thresholds:
      0: Alive
      10: Dead
  - type: RandomSprite
    available:
      - enum.DamageStateVisualLayers.Base:
          butterfly: Rainbow
  - type: DamageStateVisuals
    states:
      Alive:
        Base: butterfly
      Dead:
        Base: dead
  - type: Bloodstream
    bloodMaxVolume: 0.1
  - type: MobPrice
    price: 50

- type: entity
  name: "корова"
  parent: SimpleMobBase
  id: MobCow
  description: "Муу. Більше прав коровам..."
  components:
  - type: Absorbable
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: cow
      sprite: Mobs/Animals/cow.rsi
  - type: Tag
    tags:
    - DoorBumpOpener
    - Cow
  - type: Reproductive
    partnerWhitelist:
      tags:
      - Cow
    offspring:
    - id: MobCow
  - type: ReproductivePartner
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.40
        density: 400
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: DamageStateVisuals
    states:
      Alive:
        Base: cow
      Dead:
        Base: dead
  - type: SolutionContainerManager
    solutions:
      udder:
        maxVol: 250
        reagents:
        - ReagentId: Milk
          Quantity: 30
  - type: Udder
    reagentId: Milk
    quantityPerUpdate: 25
    growthDelay: 30
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 5
  - type: Grammar
    attributes:
      gender: female # Here because of UdderComponent
  - type: InteractionPopup
    successChance: 0.7
    interactDelay: 2 # Avoids overlapping SFX due to spam - these SFX are a little longer than the typical 1 second.
    interactSuccessString: petting-success-soft-floofy
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/cow_moo.ogg
  - type: Perishable
    molsPerSecondPerUnitMass: 0.0015
  - type: NpcFactionMember
    factions:
    - Passive
  - type: Body
    prototype: AnimalRuminant
  - type: HTN
    rootTask:
      task: RuminantCompound
  - type: GuideHelp
    guides:
    - Chef
  - type: RandomBark
    barkType: cow
    barkMultiplier: 3


- type: entity
  name: "краб"
  parent: SimpleMobBase
  id: MobCrab
  description: "Існує народна легенда, що він вириває кігтями космонавтів за неприємні зауваження. Будьте ввічливі та толерантні заради власної безпеки."
  components:
  - type: VentCrawler # goobstation - Ventcrawl
  - type: Absorbable
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base", "movement"]
      state: crab
      sprite: Mobs/Animals/crab.rsi
  - type: SpriteMovement
    movementLayers:
      movement:
        state: crab-moving
    noMovementLayers:
      movement:
        state: crab
  - type: Carriable
    freeHandsRequired: 1
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 5
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Speech
    speechVerb: Arachnid
    speechSounds: Arachnid
    allowedEmotes: ['Click', 'Chitter']
  - type: DamageStateVisuals
    states:
      Alive:
        Base: crab
      Dead:
        Base: dead
  - type: Butcherable
    spawned:
    - id: FoodMeatCrab
      amount: 2
  - type: InteractionPopup
    successChance: 0.5
    interactSuccessString: petting-success-crab
    interactFailureString: petting-failure-crab
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Voice/Arachnid/arachnid_chitter.ogg
  - type: LanguageKnowledge
    speaks:
    - Crab
    understands:
    - Crab
  - type: Bloodstream
    bloodMaxVolume: 50
    bloodReagent: CopperBlood
  - type: Tag
    tags:
    - VimPilot
  - type: HTN
    rootTask:
      task: RuminantCompound
  - type: Body
    prototype: AnimalHemocyanin
  - type: RandomBark
    barkType: crab
  - type: SurgeryTarget
  - type: UserInterface
    interfaces:
      enum.SurgeryUIKey.Key:
        type: SurgeryBui

- type: entity
  name: "козел"
  parent: SimpleMobBase
  id: MobGoat
  description: "Її хребет складається з довгих гострих сегментів, не дивно, що вона така сварлива."
  components:
  - type: Absorbable
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: goat
      sprite: Mobs/Animals/goat.rsi
  - type: Carriable
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 150
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Tag
    tags:
    # let moths eat wool directly
    - ClothMade
    - DoorBumpOpener
    - Goat
  - type: Reproductive
    partnerWhitelist:
      tags:
      - Goat
    offspring:
    - id: MobGoat
  - type: ReproductivePartner
  - type: Appearance
  - type: DamageStateVisuals
    states:
      Alive:
        Base: goat
      Critical:
        Base: dead
      Dead:
        Base: dead
  - type: SolutionContainerManager
    solutions:
      udder:
        maxVol: 250
        reagents:
        - ReagentId: MilkGoat
          Quantity: 30
      wool:
        maxVol: 250
  - type: Udder
    reagentId: MilkGoat
    quantityPerUpdate: 25
    growthDelay: 20
  - type: Wooly
  - type: Food
    solution: wool
    requiresSpecialDigestion: true
    # Wooly prevents eating wool deleting the goat so its fine
    requireDead: false
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 4
  - type: Grammar
    attributes:
      gender: female # Here because of UdderComponent
  - type: InteractionPopup
    successChance: 0.2
    interactSuccessString: petting-success-goat
    interactFailureString: petting-failure-goat
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/goat_bah.ogg
  - type: NpcFactionMember
    factions:
    - Passive
  - type: Body
    prototype: AnimalRuminant
  - type: NPCRetaliation
    attackMemoryLength: 5
  - type: FactionException
  - type: HTN
    rootTask:
      task: RuminantHostileCompound
  - type: SurgeryTarget
  - type: UserInterface
    interfaces:
      enum.SurgeryUIKey.Key:
        type: SurgeryBui

# Note that we gotta make this bitch vomit someday when you feed it anthrax or sumthin. Needs to be a small item thief too and aggressive if attacked.
- type: entity
  name: "гусак"
  parent: SimpleMobBase
  id: MobGoose
  description: "Чиї шлунок та розум - загадка, непідвладна людському розумінню."
  components:
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: goose
      sprite: Mobs/Animals/goose.rsi
  - type: Carriable
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 25
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: DamageStateVisuals
    states:
      Alive:
        Base: goose
      Dead:
        Base: dead
  - type: Butcherable
    spawned:
    - id: FoodMeatChicken
      amount: 2
  - type: InteractionPopup # TODO: Make it so there's a separate chance to make certain animals outright hostile towards you.
    successChance: 0.1 # Yeah, good luck with that.
    interactSuccessString: petting-success-goose
    interactFailureString: petting-failure-goose
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/goose_honk.ogg
  - type: Bloodstream
    bloodMaxVolume: 100
  - type: NpcFactionMember
    factions:
    - Passive

- type: entity
  name: "горила"
  parent: [ SimpleMobBase, MobCombat ]
  id: MobGorilla
  description: "Розбивається, реве, виглядає круто. Не підходьте до них близько."
  components:
  - type: Absorbable
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: crawling
      sprite: Mobs/Animals/gorilla.rsi
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.48
        density: 200
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: DamageStateVisuals
    states:
      Alive:
        Base: crawling
      Critical:
        Base: dead
      Dead:
        Base: dead
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 4
  - type: Bloodstream
    bloodMaxVolume: 300
    # if you fuck with the gorilla he will harambe you
  - type: MeleeWeapon
    soundHit:
      collection: Punch
    damage:
      types:
        Blunt: 30 # Goob edit
    animation: WeaponArcFist
  - type: NPCRetaliation
  - type: FactionException
  - type: NpcFactionMember
    factions:
    - Passive
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
  - type: MobThresholds # Goobstation
    thresholds:
      0: Alive
      120: Critical
      220: Dead
  - type: SlowOnDamage # Goobstation
    speedModifierThresholds:
      80: 0.7
      100: 0.5
  - type: Fauna # Lavaland Change


- type: entity
  name: "кенгуру"
  parent: [ SimpleMobBase, MobCombat ]
  id: MobKangaroo
  description: "Великий сумчастий травоїдний ссавець. Має потужні задні ноги, з нігтями, що нагадують довгі кігті."
  components:
  - type: Absorbable
  - type: MovementSpeedModifier
    baseWalkSpeed: 3.5
    baseSprintSpeed: 4.5
  - type: Sprite
    drawdepth: Mobs
    layers:
      - map: ["enum.DamageStateVisualLayers.Base"]
        state: kangaroo
        sprite: Mobs/Animals/kangaroo.rsi
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 130
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Physics
  - type: Inventory
    speciesId: kangaroo
    templateId: kangaroo
  - type: LanguageKnowledge
    speaks:
    - Kangaroo
    understands:
    - Kangaroo
  - type: InventorySlots
  - type: Strippable
  - type: Butcherable
    spawned:
    - id: FoodMeat
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
  - type: DamageStateVisuals
    states:
      Alive:
        Base: kangaroo
      # SKIPPY NO!
      Critical:
        Base: kangaroo-crit
      Dead:
        Base: kangaroo-dead
  - type: Tag
    tags:
    - DoorBumpOpener
    - FootstepSound
  - type: GhostRole
    prob: 0.25
    name: ghost-role-information-kangaroo-name
    description: ghost-role-information-kangaroo-description
  - type: GhostTakeoverAvailable
  - type: Vocal
    sounds:
      Unsexed: Kangaroo
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepBounce
  - type: Puller
    needsHands: false
  - type: StaminaDamageOnHit
    damage: 8 #Stam damage values seem a bit higher than regular damage because of the decay, etc
    # This needs to be moved to boxinggloves
    #knockdownSound: /Audio/Weapons/boxingbell.ogg
  - type: MeleeWeapon
    attackRate: 1.5
    damage:
      types:
        Blunt: 0.4
    soundHit:
      collection: BoxingHit
    animation: WeaponArcFist
  - type: NPCRetaliation
    attackMemoryLength: 10
  - type: FactionException
  - type: NpcFactionMember
    factions:
    - Passive
  - type: HTN
    rootTask:
      task: SimpleHostileCompound

- type: entity
  name: "кенгуру-боксер"
  parent: MobKangaroo
  id: MobBoxingKangaroo
  components:
  - type: Loadout
    prototypes: [ BoxingKangarooGear ]
  - type: NpcFactionMember
    factions:
    - SimpleHostile

- type: entity
  name: "генетичний предок"
  id: MobBaseAncestor
  parent: SimpleMobBase
  description: "Генетичний двоногий предок... Е-е... чогось. Так, на станції точно є щось, що походить від того, що б це не було."
  abstract: true
  components:
  - type: VentCrawler # goobstation - Ventcrawl
    allowInventory: false
  - type: Absorbable
  - type: CombatMode
  # Shitmed Change Start
  - type: SurgeryTarget
  - type: Targeting
  - type: HumanoidAppearance
    species: Monkey
  # Shitmed Change End
  - type: Inventory
    templateId: monkey
    speciesId: monkey
  - type: InventorySlots
  - type: Deathgasp
    prototype: MonkeyDeathgasp
  - type: Cuffable
  - type: RotationVisuals
    defaultRotation: 90
    horizontalRotation: 90
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 80
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Stripping
  - type: Strippable
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
      enum.SurgeryUIKey.Key:
        type: SurgeryBui
  - type: Sprite
    drawdepth: Mobs
    layers:
    # Shitmed Change Start
    - map: [ "enum.HumanoidVisualLayers.Chest" ]
    - map: [ "enum.HumanoidVisualLayers.Head" ]
    - map: [ "enum.HumanoidVisualLayers.Snout" ]
    - map: [ "enum.HumanoidVisualLayers.Eyes" ]
    - map: [ "enum.HumanoidVisualLayers.RArm" ]
    - map: [ "enum.HumanoidVisualLayers.LArm" ]
    - map: [ "enum.HumanoidVisualLayers.RLeg" ]
    - map: [ "enum.HumanoidVisualLayers.LLeg" ]
    - map: ["jumpsuit"]
    - map: ["enum.HumanoidVisualLayers.LFoot"]
    - map: ["enum.HumanoidVisualLayers.RFoot"]
    - map: ["enum.HumanoidVisualLayers.LHand"]
    - map: ["enum.HumanoidVisualLayers.RHand"]
    - map: ["enum.HumanoidVisualLayers.Handcuffs"]
      color: "#ffffff"
      sprite: Objects/Misc/handcuffs.rsi
      state: body-overlay-2
      visible: false
    - map: [ "gloves" ]
    - map: [ "shoes" ]
    - map: [ "ears" ]
    - map: [ "innerBelt" ] # Einstein Engines
    - map: [ "innerNeck" ] # Einstein Engines
    - map: [ "outerClothing" ]
    - map: [ "eyes" ]
    - map: [ "belt" ]
    - map: [ "id" ]
    - map: [ "neck" ]
    - map: [ "back" ]
    - map: [ "enum.HumanoidVisualLayers.FacialHair" ]
    - map: [ "enum.HumanoidVisualLayers.Hair" ]
    - map: [ "enum.HumanoidVisualLayers.HeadSide" ]
    - map: [ "enum.HumanoidVisualLayers.HeadTop" ]
    - map: [ "enum.HumanoidVisualLayers.Tail" ]
    - map: [ "mask" ]
    - map: [ "head" ]
    - map: [ "pocket1" ]
    - map: [ "pocket2" ]
    # Shitmed Change End
  - type: Carriable
  - type: Hands
  - type: ComplexInteraction
  - type: GenericVisualizer
    visuals:
      enum.CreamPiedVisuals.Creamed:
        clownedon:
          True: {visible: true}
          False: {visible: false}
  - type: Body # Shitmed Change
    prototype: Monkey
    requiredLegs: 2
  - type: CreamPied
  - type: FireVisuals
    sprite: Mobs/Effects/onfire.rsi
    normalState: Monkey_burning
  - type: InteractionPopup
    successChance: 0.9
    interactSuccessString: petting-success-monkey
    interactFailureString: petting-failure-monkey
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/ferret_happy.ogg
    interactFailureSound:
      path: /Audio/Items/wirecutter.ogg
  - type: MeleeWeapon
    soundHit:
      path: /Audio/Effects/bite.ogg
    angle: 30
    animation: WeaponArcBite
    damage:
      types:
        Blunt: 3
        Piercing: 3
  - type: Butcherable
    butcheringType: Spike
    spawned:
    - id: FoodMeat
      amount: 3
  - type: Puller
    needsHands: false
  - type: CanHostGuardian
  - type: NPCRetaliation
    attackMemoryLength: 10
  - type: FactionException
  - type: NpcFactionMember
    factions:
      - Passive
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
  - type: IdExaminable
  - type: Tag
    tags:
    - VimPilot
    - DoorBumpOpener
    - AnomalyHost
  - type: Reactive
    groups:
      Flammable: [ Touch ]
      Extinguish: [ Touch ]
    reactions:
    - reagents: [ Water, SpaceCleaner ]
      methods: [ Touch ]
      effects:
      - !type:WashCreamPieReaction
  - type: LayingDown # Goobstation



- type: entity
  name: "мавпа"
  id: MobMonkey
  parent: MobBaseAncestor
  description: "Нова церква неодарвіністів насправді вірить, що КОЖНА тварина походить від мавпи. На смак вони схожі на свинину, а вбивати їх одночасно весело і розслабляюче."
  components:
  - type: Absorbable
  - type: NameIdentifier
    group: Monkey
  - type: Speech
    speechSounds: Monkey
    speechVerb: Monkey
  - type: LanguageKnowledge
    speaks:
    - Monkey
    understands:
    - Monkey
    - Kobold
  - type: SentienceTarget
    flavorKind: station-event-random-sentience-flavor-primate
  - type: AlwaysRevolutionaryConvertible
  - type: GhostRole
    prob: 0.05
    makeSentient: true
    name: ghost-role-information-monkey-name
    description: ghost-role-information-monkey-description
  - type: GhostTakeoverAvailable
  - type: Clumsy
    gunShootFailDamage:
      types:
        Blunt: 5
        Piercing: 4
      groups:
        Burn: 3
    clumsySound:
      path: /Audio/Animals/monkey_scream.ogg

- type: entity
  name: "мавпа"
  id: MobBaseSyndicateMonkey
  parent: MobBaseAncestor
  description: "Нова церква неодарвіністів насправді вірить, що КОЖНА тварина походить від мавпи. На смак вони схожі на свинину, а вбивати їх одночасно весело і розслабляюче."
  suffix: syndicate base
  components:
  - type: Absorbable
  - type: NameIdentifier
    group: Monkey
  - type: SentienceTarget
    flavorKind: station-event-random-sentience-flavor-primate
  - type: Speech
    speechSounds: Monkey
    speechVerb: Monkey
  - type: LanguageKnowledge
    speaks:
    - Monkey
    understands:
    - Monkey
    - Kobold
    - TauCetiBasic
  - type: NpcFactionMember
    factions:
    - Syndicate
  - type: GhostRole
    prob: 0.05
    makeSentient: true
    name: ghost-role-information-monkey-name
    description: ghost-role-information-monkey-description
    rules: ghost-role-information-syndicate-reinforcement-rules
    mindRoles:
    # This is for syndicate monkeys that randomly gain sentience, thus have no summoner to team with
    - MindRoleGhostRoleSoloAntagonist
    raffle:
      settings: default
  - type: GhostTakeoverAvailable
  - type: Loadout
    prototypes: [SyndicateOperativeGearMonkey]

- type: entity
  id: MobMonkeySyndicateAgent
  parent: MobBaseSyndicateMonkey
  suffix: syndicate agent
  components:
    # make the player a traitor once its taken
  - type: VentCrawler # goobstation - Ventcrawl
  - type: Absorbable
  - type: AutoTraitor
    profile: TraitorReinforcement

- type: entity
  id: MobMonkeySyndicateAgentNukeops # Reinforcement exclusive to nukeops uplink
  parent: MobBaseSyndicateMonkey
  suffix: NukeOps
  components:
  - type: VentCrawler # goobstation - Ventcrawl
  - type: Absorbable
  - type: NukeOperative

- type: entity
  name: "кобольд"
  id: MobBaseKobold
  parent: MobBaseAncestor
  description: "Кузени розумної раси людей-ящірок, кобольди зливаються зі своїм природним середовищем існування і є такими ж огидними, як і мавпи; вони готові вирвати вам волосся і зарізати."
  abstract: true
  components:
  - type: Absorbable
  - type: NameIdentifier
    group: Kobold
  - type: LizardAccent
  - type: LanguageKnowledge
    speaks:
    - Kobold
    understands:
    - Kobold
    - Monkey
  - type: Speech
    speechSounds: Lizard
    speechVerb: Reptilian
  - type: Vocal
    sounds:
      Male: MaleReptilian
      Female: FemaleReptilian
      Unsexed: MaleReptilian
  - type: TypingIndicator
    proto: lizard
  - type: InteractionPopup
    successChance: 0.9
    interactSuccessString: petting-success-monkey
    interactFailureString: petting-failure-monkey
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/lizard_happy.ogg
    interactFailureSound:
      path: /Audio/Items/wirecutter.ogg
  - type: MobThresholds
    thresholds:
      0: Alive
      100: Critical # Goob edit
      200: Dead # Goob edit
  - type: MovementSpeedModifier
    baseWalkSpeed: 3.5
    baseSprintSpeed: 5
  - type: MeleeWeapon
    soundHit:
      collection: AlienClaw
    angle: 30
    animation: WeaponArcClaw
    damage:
      types:
        Slash: 5
        Piercing: 4
  - type: Temperature
    heatDamageThreshold: 360
    coldDamageThreshold: 285
    currentTemperature: 310.15
    specificHeat: 42
  - type: Butcherable
    butcheringType: Spike
    spawned:
    - id: FoodMeat
      amount: 2
  - type: AlwaysRevolutionaryConvertible
  - type: GhostTakeoverAvailable
  - type: SentienceTarget
    flavorKind: station-event-random-sentience-flavor-kobold
  - type: GhostRole
    prob: 0.1
    makeSentient: true
    name: ghost-role-information-kobold-name
    description: ghost-role-information-kobold-description
  - type: RandomBark
    barkMultiplier: 0.65
    barkType: kobold
  # Shitmed Change Start
  - type: HumanoidAppearance
    species: Kobold
  - type: Body
    prototype: Kobold
    requiredLegs: 2
  # Shitmed Change End

- type: entity
  name: "кобольд"
  id: MobKobold
  parent: MobBaseKobold
  description: "Кузени розумної раси людей-ящірок, кобольди зливаються зі своїм природним середовищем існування і є такими ж огидними, як і мавпи; вони готові вирвати вам волосся і зарізати."
  components:
  - type: Absorbable
  - type: Clumsy
    gunShootFailDamage:
      types:
        Blunt: 2
        Piercing: 7
      groups:
        Burn: 3
    clumsySound:
      path: /Audio/Voice/Reptilian/reptilian_scream.ogg

- type: entity
  id: MobBaseSyndicateKobold
  parent: MobBaseKobold
  suffix: syndicate base
  components:
  - type: Absorbable
  - type: MobThresholds
    thresholds:
      0: Alive
      100: Critical # Goob edit
      200: Dead
  - type: NpcFactionMember
    factions:
    - Syndicate
  - type: Loadout
    prototypes: [SyndicateOperativeGearMonkey]

- type: entity
  id: MobKoboldSyndicateAgent
  parent: MobBaseSyndicateKobold
  suffix: syndicate agent
  components:
  - type: Absorbable
    # make the player a traitor once its taken
  - type: AutoTraitor
    profile: TraitorReinforcement

- type: entity
  id: MobKoboldSyndicateAgentNukeops # Reinforcement exclusive to nukeops uplink
  parent: MobBaseSyndicateKobold
  suffix: NukeOps
  components:
  - type: Absorbable
  - type: NukeOperative

- type: entity
  name: "мавпа-провідник"
  parent: MobMonkey
  categories: [ HideSpawnMenu ]
  id: MobGuidebookMonkey
  description: "Сподіваємось, корисна мавпочка, єдина мета життя якої - щоб ви на неї клацали. Чи вважається, що мавпа дала вам урок?"
  components:
    - type: GuidebookControlsTest

- type: entity
  name: "миша"
  parent: SimpleMobBase
  id: MobMouse
  description: "Писк!"
  components:
  - type: VentCrawler # goobstation - Ventcrawl
  - type: Body
    prototype: Mouse
  - type: GhostRole
    makeSentient: true
    allowSpeech: true
    allowMovement: true
    name: ghost-role-information-mouse-name
    description: ghost-role-information-mouse-description
    rules: ghost-role-information-freeagent-rules
    mindRoles:
    - MindRoleGhostRoleFreeAgent
  - type: GhostTakeoverAvailable
  - type: Speech
    speechSounds: Squeak
    speechVerb: SmallMob
    allowedEmotes: ['Squeak']
  - type: Sprite
    drawdepth: SmallMobs
    sprite: Mobs/Animals/mouse.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base", "movement"]
      state: mouse-0
  - type: SpriteMovement
    movementLayers:
      movement:
        state: mouse-moving-0
    noMovementLayers:
      movement:
        state: mouse-0
  - type: Item
    size: Tiny
  - type: Clothing
    quickEquip: false
    sprite: Mobs/Animals/mouse.rsi
    equippedPrefix: 0
    slots:
    - HEAD
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
  - type: NpcFactionMember
    factions:
      - Mouse
  - type: HTN
    rootTask:
      task: MouseCompound
  - type: Physics
  - type: FaxableObject
    insertingState: inserting_mouse
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.2
        density: 0.76
        mask:
        - SmallMobMask
        layer:
        - SmallMobLayer
  - type: MobState
  - type: Deathgasp
  - type: MobStateActions
    actions:
      Critical:
      - ActionCritSuccumb
      - ActionCritFakeDeath
      - ActionCritLastWords
  - type: MobThresholds
    thresholds:
      0: Alive
      10: Critical
      20: Dead
  - type: MovementSpeedModifier
    baseWalkSpeed : 5
    baseSprintSpeed : 5
  - type: DamageStateVisuals
    states:
      Alive:
        Base: mouse-0
      Critical:
        Base: dead-0
      Dead:
        Base: splat-0
  - type: Food
  - type: Thirst
    startingThirst: 25  # spawn with Okay thirst state
    thresholds:
      OverHydrated: 35
      Okay: 25
      Thirsty: 15
      Parched: 10
      Dead: 0
    baseDecayRate: 0.04
  - type: Hunger
    currentHunger: 25   # spawn with Okay hunger state
    thresholds:
      Overfed: 35
      Okay: 25
      Peckish: 15
      Starving: 10
      Dead: 0
    baseDecayRate: 0.5 # I'm very hungry! Give me. The cheese.
  - type: Extractable
    grindableSolutionName: food
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 3
  - type: Butcherable
    spawned:
    - id: FoodMeatRat
      amount: 1
  - type: LanguageKnowledge
    speaks:
    - Mouse
    understands:
    - Mouse
  - type: Tag
    tags:
    - Trash
    - VimPilot
    - ChefPilot
    - Mouse
    - Meat
  - type: Respirator
    damage:
      types:
        Asphyxiation: 0.25
    damageRecovery:
      types:
        Asphyxiation: -0.25
  - type: Barotrauma
    damage:
      types:
        Blunt: 0.1
  - type: Vocal
    sounds:
      Male: Mouse
      Female: Mouse
      Unsexed: Mouse
    wilhelmProbability: 0.001
  # TODO: Remove CombatMode when Prototype Composition is added
  - type: CombatMode
    combatToggleAction: ActionCombatModeToggleOff
  - type: Bloodstream
    bloodMaxVolume: 50
  - type: CanEscapeInventory
  - type: MobPrice
    price: 50
  - type: FelinidFood # Nyanotrasen - Felinid, ability to eat mice, see Content.Server/Nyanotrasen/Abilities/Felinid/FelinidSystem.cs
  - type: BadFood
  - type: NonSpreaderZombie
  - type: FireVisuals
    sprite: Mobs/Effects/onfire.rsi
    normalState: Mouse_burning
  - type: StepTriggerImmune
    whitelist:
      types:
      - Landmine
  - type: SurgeryTarget
  - type: UserInterface
    interfaces:
      enum.SurgeryUIKey.Key:
        type: SurgeryBui
  - type: FoodSequenceElement
    entries:
      Taco: RatTaco
      Burger: RatBurger
      Skewer: RatSkewer
  - type: NightVision
    isActive: true
    toggleAction: null
    color: "#808080"
    activateSound: null
    deactivateSound: null

- type: entity
  parent: MobMouse
  id: MobMousePet
  components:
  - type: HTN
    rootTask:
      task: FollowCompound
    blackboard:
      IdleRange: !type:Single
        1.5
      FollowCloseRange: !type:Single
        1.0
      FollowRange: !type:Single
        2.0

- type: entity
  parent: MobMouse
  suffix: Dead
  save: false
  id: MobMouseDead
  name: "миша"
  description: "Писк!"
  components:
  - type: Damageable
    damage:
      types:
        Bloodloss: 6
        Asphyxiation: 4
        Slash: 7
        Blunt: 3

- type: entity
  parent: MobMouse
  id: MobMouseAdmeme
  suffix: Admeme
  components:
  # allow admeme mouse to eat pills
  - type: IgnoreBadFood
  # intended for swarms that eat pills so only temporary
  - type: TimedDespawn
    lifetime: 60

- type: entity
  parent: MobMouse
  id: MobMouse1
  components:
  - type: Sprite
    layers:
    - map: ["enum.DamageStateVisualLayers.Base", "movement"]
      state: mouse-1
  - type: SpriteMovement
    movementLayers:
      movement:
        state: mouse-moving-1
    noMovementLayers:
      movement:
        state: mouse-1
  - type: Clothing
    equippedPrefix: 1
  - type: DamageStateVisuals
    states:
      Alive:
        Base: mouse-1
      Critical:
        Base: dead-1
      Dead:
        Base: splat-1
  - type: Item
    size: Tiny
    heldPrefix: 1

- type: entity
  parent: MobMouse
  id: MobMouse2
  components:
  - type: Sprite
    layers:
    - map: ["enum.DamageStateVisualLayers.Base", "movement"]
      state: mouse-2
  - type: SpriteMovement
    movementLayers:
      movement:
        state: mouse-moving-2
    noMovementLayers:
      movement:
        state: mouse-2
  - type: Clothing
    equippedPrefix: 2
  - type: DamageStateVisuals
    states:
      Alive:
        Base: mouse-2
      Critical:
        Base: dead-2
      Dead:
        Base: splat-2
  - type: Item
    size: Tiny
    heldPrefix: 2

- type: entity
  name: "ракова миша"
  description: "Токсична. Писк!"
  parent: MobMouse
  id: MobMouseCancer
  components:
  - type: Sprite
    color: LightGreen
  - type: PointLight
    color: LightGreen
    radius: 5
    energy: 5
    netsync: false
  - type: RadiationSource
    intensity: 0.3
  - type: Bloodstream
    bloodReagent: UnstableMutagen
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 3
        - ReagentId: Uranium
          Quantity: 10
  - type: Butcherable
    spawned:
    - id: FoodMeatRat
      amount: 1
    - id: SheetUranium1
      amount: 1
  - type: Damageable
    damageContainer: Biological
    damageModifierSet: Zombie

- type: entity
  name: "ящірка" #Weh
  parent: SimpleMobBase
  id: MobLizard
  description: "Нешкідливий дракон."
  components:
  - type: MovementSpeedModifier
    baseWalkSpeed: 2
    baseSprintSpeed: 3
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: lizard
      sprite: Mobs/Animals/lizard.rsi
  - type: Carriable
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.2
        density: 50
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: DamageStateVisuals
    states:
      Alive:
        Base: lizard
      Dead:
        Base: dead
  - type: Butcherable
    spawned:
    - id: FoodMeatLizard
      amount: 1
  - type: InteractionPopup
    successChance: 0.3
    interactSuccessString: petting-success-reptile
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/lizard_happy.ogg
  - type: Bloodstream
    bloodMaxVolume: 150
  - type: Damageable
    damageContainer: Biological
    damageModifierSet: Scale
  - type: Tag
    tags:
    - VimPilot


- type: entity
  name: "слизняк"
  parent: SimpleMobBase
  id: MobSlug
  description: "І вони назвали це ящіркою?"
  components:
  - type: VentCrawler # goobstation - Ventcrawl
  - type: MovementSpeedModifier
    baseWalkSpeed : 2
    baseSprintSpeed : 3
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: slug
      sprite: Mobs/Animals/slug.rsi
  - type: Carriable
    freeHandsRequired: 1
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.2
        density: 5
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: DamageStateVisuals
    states:
      Alive:
        Base: slug
      Dead:
        Base: dead
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 1
  - type: InteractionPopup
    successChance: 0.3
    interactSuccessString: petting-success-generic
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
  - type: Bloodstream
    bloodMaxVolume: 50

- type: entity
  name: "жаба"
  parent: SimpleMobBase
  id: MobFrog
  description: "Хоп, хоп, хоп, хоп. Виглядає вологим."
  components:
  - type: VentCrawler # goobstation - Ventcrawl
  - type: MovementSpeedModifier
    baseWalkSpeed: 4
    baseSprintSpeed: 6
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base", "movement"]
      state: frog
      sprite: Mobs/Animals/frog.rsi
  - type: SpriteMovement
    movementLayers:
      movement:
        state: frog-moving
    noMovementLayers:
      movement:
        state: frog
  - type: Carriable
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.2
        density: 5
        mask:
        - SmallMobMask
        layer:
        - SmallMobLayer
  - type: DamageStateVisuals
    states:
      Alive:
        Base: frog
      Dead:
        Base: dead
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 1
  - type: InteractionPopup
    successChance: 0.6
    interactSuccessString: petting-success-frog
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/frog_ribbit.ogg
  - type: Bloodstream
    bloodMaxVolume: 50
  - type: Tag
    tags:
    - VimPilot
  - type: RandomBark
    barkMultiplier: 1.3

# Would be cool to have some functionality for the parrot to be able to sit on stuff
- type: entity
  name: "папуга"
  parent: [ SimpleMobBase, FlyingMobBase ]
  id: MobParrot
  description: "Проникає у ваш домен, шпигує за вами і чомусь залишається крутим домашнім улюбленцем."
  components:
  - type: MovementSpeedModifier
    baseWalkSpeed : 6
    baseSprintSpeed : 6
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: parrot
      sprite: Mobs/Animals/parrot.rsi
  - type: Carriable
    freeHandsRequired: 1
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.25
        density: 10
        mask:
        - FlyingMobMask
        layer:
        - FlyingMobLayer
  - type: DamageStateVisuals
    states:
      Alive:
        Base: parrot
      Dead:
        Base: dead
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 1
  - type: Speech
    speechSounds: Parrot
    speechVerb: Parrot
  - type: Vocal
    sounds:
      Unsexed: Parrot
  - type: ParrotAccent
  - type: InteractionPopup
    successChance: 0.6
    interactSuccessString: petting-success-bird
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/parrot_raught.ogg
  - type: Bloodstream
    bloodMaxVolume: 50
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    understands:
    - TauCetiBasic

- type: entity
  name: "пінгвін"
  parent: SimpleMobBase
  id: MobPenguin
  description: "Їхнє життя - це постійний біль через їхні внутрішні коліна."
  components:
  - type: Absorbable
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: penguin
      sprite: Mobs/Animals/penguin.rsi
  - type: Carriable
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.25
        density: 100
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: DamageStateVisuals
    states:
      Alive:
        Base: penguin
      Dead:
        Base: penguin_dead
  - type: Butcherable
    spawned:
    - id: FoodMeatPenguin
      amount: 3
  - type: InteractionPopup
    successChance: 0.5
    interactSuccessString: petting-success-bird
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/penguin_squawk.ogg
  - type: Tag
    tags:
    - VimPilot
  - type: NpcFactionMember
    factions:
    - Passive
  - type: Temperature
    atmosTemperatureTransferEfficiency: 0.04
    heatDamageThreshold: 335
    coldDamageThreshold: 230
    currentTemperature: 310.15
    specificHeat: 46
    coldDamage:
      types:
        Cold : 0.05 #per second, scales with temperature & other constants
    heatDamage:
      types:
        Heat : 0.2 #per second, scales with temperature & other constants
  - type: RandomBark
    barkType: penguin
    barkMultiplier: 0.6
  - type: LanguageKnowledge
    speaks: [Penguin]
    understands: [Penguin]

- type: entity
  name: "гранатовий пінгвін"
  parent: [ MobPenguin, MobCombat ]
  id: MobGrenadePenguin
  description: "Маленький пінгвін з гранатою на шиї. Виловлений Синдикатом з крижаних планет-гадючників. Ось так виглядає тепер біозброя?"
  components:
  - type: Absorbable
  - type: MovementSpeedModifier
    baseWalkSpeed: 3.5
    baseSprintSpeed: 5
  - type: InputMover
  - type: MobMover
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
  - type: NpcFactionMember
    factions:
    - Syndicate
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: penguin
      sprite: Mobs/Animals/grenadepenguin.rsi
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.25
        density: 25
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: DamageStateVisuals
    states:
      Alive:
        Base: penguin
      Dead:
        Base: dead
  - type: MeleeWeapon
    angle: 0
    animation: WeaponArcBite
    damage:
      groups:
        Brute: 5
  - type: Item
    size: Normal
  - type: OnUseTimerTrigger
    delay: 10
    beepSound:
      path: /Audio/Weapons/Guns/MagOut/pistol_magout.ogg #funny sfx use
      params:
        volume: -2
    beepInterval: 1
  - type: Explosive
    explosionType: Default
    maxIntensity: 20
    intensitySlope: 20
    totalIntensity: 225
  - type: ExplodeOnTrigger
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:ExplodeBehavior

  # I have included a snake_hiss.ogg sound file so if you want to use that be my guest
- type: entity
  name: "змія"
  parent: SimpleMobBase
  id: MobSnake
  description: "Шипіння! Укуси не отруйні."
  components:
  - type: VentCrawler # goobstation - Ventcrawl
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: snake
      sprite: Mobs/Animals/snake.rsi
  - type: Carriable
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.25
        density: 10
        mask:
        - SmallMobMask
        layer:
        - SmallMobLayer
  - type: DamageStateVisuals
    states:
      Alive:
        Base: snake
      Dead:
        Base: dead
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 1
  - type: InteractionPopup
    successChance: 0.6
    interactSuccessString: petting-success-reptile
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
  - type: Bloodstream
    bloodMaxVolume: 50
  - type: Damageable
    damageContainer: Biological
    damageModifierSet: Scale
  - type: RandomBark
    minTime: 10
    maxTime: 50 # It's a sssnake...
    barkType: hissing
  - type: LanguageKnowledge
    speaks: [Hissing]
    understands: [Hissing]

# Code unique spider prototypes or combine them all into one spider and get a
# random sprite state when you spawn it.
- type: entity
  parent: [ SimpleMobBase, MobCombat ]
  id: MobSpiderBase
  abstract: true
  components:
  - type: VentCrawler # goobstation - Ventcrawl
  - type: Absorbable
  - type: Carriable
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 130
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Butcherable
    spawned:
    - id: FoodMeatSpider
      amount: 2
  - type: CombatMode
  - type: Body
    prototype: AnimalHemocyanin
  - type: MobThresholds
    thresholds:
      0: Alive
      90: Dead
  - type: SolutionContainerManager
    solutions:
      melee:
        maxVol: 30
  - type: SolutionRegeneration
    solution: melee
    generated:
      reagents:
      - ReagentId: Toxin
        Quantity: 1
  - type: MeleeChemicalInjector
    transferAmount: 0.75
    solution: melee
  - type: LanguageKnowledge
    speaks:
    - Xeno
    understands:
    - Xeno
  - type: InteractionPopup
    successChance: 0.5
    interactSuccessString: petting-success-tarantula
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/snake_hiss.ogg
  - type: NoSlip
  - type: Spider
  - type: IgnoreSpiderWeb
  - type: Bloodstream
    bloodMaxVolume: 150
    bloodReagent: CopperBlood
  - type: Speech
    speechVerb: Arachnid
    speechSounds: Arachnid
    allowedEmotes: ['Click', 'Chitter', 'Hiss'] #DeltaV added Hiss
  - type: Vocal
    sounds:
      Male: UnisexArachnid
      Female: UnisexArachnid
      Unsexed: UnisexArachnid
  - type: TypingIndicator
    proto: spider
  - type: Tag
    tags:
    - DoorBumpOpener
    - FootstepSound
  - type: Tool # Open door from xeno.yml.
    speedModifier: 1.5
    qualities:
      - Prying
    useSound:
      path: /Audio/Items/crowbar.ogg
  - type: Prying
    pryPowered: !type:Bool
        true
    force: !type:Bool
      true
    useSound:
      path: /Audio/Items/crowbar.ogg
  - type: PassiveDamage # Slight passive regen. Assuming one damage type, comes out to about 4 damage a minute from base.yml.
    allowedStates:
    - Alive
    damageCap: 89
    damage:
      types:
        Poison: -0.07
      groups:
        Brute: -0.07
        Burn: -0.07
  - type: RandomBark
    barkType: hissing
    barkMultiplier: 0.3
  - type: BloodSucker
    webRequired: true
  - type: Cocooner
  - type: SurgeryTarget
  - type: UserInterface
    interfaces:
      enum.SurgeryUIKey.Key:
        type: SurgeryBui
  - type: Fauna # Lavaland Change

- type: entity
  parent: MobSpiderBase
  id: MobSpiderAngryBase
  abstract: true
  components:
  - type: Absorbable
  - type: NpcFactionMember
    factions:
      - Xeno
  - type: InputMover
  - type: MobMover
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
  - type: GhostRole
    makeSentient: true
    name: ghost-role-information-giant-spider-name
    description: ghost-role-information-giant-spider-description
    rules: ghost-role-information-giant-spider-rules
    mindRoles:
    - MindRoleGhostRoleTeamAntagonist
    raffle:
      settings: short
  - type: GhostTakeoverAvailable

- type: entity
  name: "тарантул"
  parent: MobSpiderBase
  id: MobGiantSpider
  description: "Широко визнано, що це буквально найгірша річ у світі."
  components:
  - type: Absorbable
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base", "movement"]
      state: tarantula
      sprite: Mobs/Animals/spider.rsi
  - type: SpriteMovement
    movementLayers:
      movement:
        state: tarantula-moving
    noMovementLayers:
      movement:
        state: tarantula
  - type: DamageStateVisuals
    states:
      Alive:
        Base: tarantula
      Critical:
        Base: tarantula_dead
      Dead:
        Base: tarantula_dead
  - type: MeleeWeapon
    altDisarm: false
    angle: 0
    animation: WeaponArcBite
    soundHit:
      path: /Audio/Effects/bite.ogg
    damage:
      types:
        Piercing: 6
  - type: Puller
    needsHands: false

- type: entity
  parent:
  - MobSpiderAngryBase
  - MobGiantSpider
  id: MobGiantSpiderAngry

- type: entity
  name: "павук-клоун"
  parent: MobSpiderAngryBase
  id: MobClownSpider
  description: "Поєднує в собі дві найстрашніші речі в світі - павуків і клоунів."
  components:
    - type: Absorbable
    - type: Sprite
      drawdepth: Mobs
      layers:
      - map: ["enum.DamageStateVisualLayers.Base"]
        state: clown
        sprite: Mobs/Animals/clownspider.rsi
    - type: Butcherable
      spawned:
        - id: MaterialBananium1
          amount: 1
    - type: DamageStateVisuals
      states:
        Alive:
          Base: clown
        Dead:
          Base: dead
    - type: MobThresholds
      thresholds:
        0: Alive
        180: Dead
    - type: Spider
      webPrototype: SpiderWebClown
    - type: MeleeWeapon
      altDisarm: false
      angle: 0
      animation: WeaponArcBite
      soundHit:
        path: /Audio/Effects/bite.ogg
      damage:
        types:
          Piercing: 8
    - type: FootstepModifier
      footstepSoundCollection:
        collection: FootstepClownFast
    - type: Speech
      speechVerb: Cluwne
    - type: Bloodstream
      bloodMaxVolume: 150
      bloodReagent: Laughter

- type: entity
  name: "павук-чарівник"
  parent: MobGiantSpider
  id: MobGiantSpiderWizard
  description: "Цей павук виглядає трохи магічно"
  suffix: Wizard
  components:
  - type: Accentless
    removes:
    - type: ReplacementAccent
      accent: xeno  #let this wizard speak

- type: entity
  name: "опосум"
  parent: SimpleMobBase
  id: MobPossum
  description: "\"О, Опосум! Мій Опосум!\" -- Уолт Уітмен, 1865"
  components:
  - type: Absorbable
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Animals/possum.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: possum
  - type: Carriable
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 50
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Inventory
    speciesId: possum
    templateId: pet
  - type: InventorySlots
  - type: Strippable
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
  - type: DamageStateVisuals
    states:
      Alive:
        Base: possum
      Dead:
        Base: possum_dead
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 3
  - type: InteractionPopup
    successChance: 0.2 # Low when undomesticated.
    interactSuccessString: petting-success-possum # Possums don't really make much noise when they're happy. They make clicking noises as a mating call, but that is NOT the same thing!
    interactFailureString: petting-failure-possum
    interactFailureSound:
      path: /Audio/Animals/cat_hiss.ogg # This sound effect is intended for generic hissing. For easy reference it's named after the animal it came from IRL.
  - type: Grammar
    attributes:
      gender: epicene
  - type: Tag
    tags:
    - VimPilot
  - type: LanguageKnowledge
    speaks:
    - Hissing
    understands:
    - Hissing
  - type: RandomBark
    barkType: possum

- type: entity
  name: "опосум"
  parent: MobPossum
  suffix: Old sprite
  id: MobPossumOld
  components:
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Animals/possum_old.rsi
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: possum_old
  - type: DamageStateVisuals
    states:
      Alive:
        Base: possum_old
      Dead:
        Base: possum_dead_old

- type: entity
  name: "єнот"
  parent: SimpleMobBase
  id: MobRaccoon
  description: "Сміттєва панда!"
  components:
  - type: Absorbable
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Animals/raccoon.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: raccoon
  - type: Carriable
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 50
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Inventory
    speciesId: possum #close enough
    templateId: pet
  - type: InventorySlots
  - type: Strippable
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
  - type: DamageStateVisuals
    states:
      Alive:
        Base: raccoon
      Dead:
        Base: raccoon_dead
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 3
  - type: InteractionPopup
    successChance: 0.2 # Low when undomesticated.
    interactSuccessString: petting-success-soft-floofy
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/raccoon_chatter.ogg
  - type: Grammar
    attributes:
      gender: epicene
  - type: Tag
    tags:
    - VimPilot
  - type: LanguageKnowledge
    speaks:
    - Hissing
    understands:
    - Hissing
  - type: RandomBark
    barkType: raccoon

- type: entity
  name: "лисиця"
  parent: SimpleMobBase
  id: MobFox
  description: "Це лисиця."
  components:
  - type: Absorbable
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Animals/fox.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base", "movement"]
      state: fox
  - type: SpriteMovement
    movementLayers:
      movement:
        state: fox-moving
    noMovementLayers:
      movement:
        state: fox
  - type: Carriable
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 16.66
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Inventory
    speciesId: fox
    templateId: pet
  - type: InventorySlots
  - type: Strippable
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
  - type: DamageStateVisuals
    states:
      Alive:
        Base: fox
      Dead:
        Base: fox_dead
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 3
  - type: InteractionPopup
    successChance: 0.5
    interactSuccessString: petting-success-soft-floofy
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      collection: Fox
  - type: Grammar
    attributes:
      gender: epicene
  - type: Bloodstream
    bloodMaxVolume: 100
  - type: MeleeWeapon
    angle: 0
    animation: WeaponArcBite
    soundHit:
      path: /Audio/Effects/bite.ogg
    damage:
      types:
        Piercing: 8
  - type: NpcFactionMember
    factions:
    - Passive
  - type: NPCRetaliation
    attackMemoryLength: 10
  - type: HTN
    rootTask:
      task: RuminantHostileCompound
  - type: Tag
    tags:
    - VimPilot
  - type: LanguageKnowledge
    speaks:
    - Fox
    understands:
    - Fox
  - type: RandomBark
    barkType: fox
    barkMultiplier: 0.5 # Talkative <3
  - type: ScentTracker

- type: entity
  name: "коргі"
  parent: SimpleMobBase
  id: MobCorgi
  description: "Нарешті, космічний коргі!"
  components:
  - type: Absorbable
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Pets/corgi.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: corgi
  - type: Carriable
  - type: Physics
  - type: Speech
    speechVerb: Canine
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 25.5
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Inventory
    speciesId: dog
    templateId: pet
  - type: InventorySlots
  - type: Strippable
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
  - type: DamageStateVisuals
    states:
      Alive:
        Base: corgi
      Dead:
        Base: corgi_dead
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 2
  - type: LanguageKnowledge
    speaks:
    - Dog
    understands:
    - Dog
  - type: InteractionPopup
    interactSuccessString: petting-success-dog
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/small_dog_bark_happy.ogg
  - type: Grammar
    attributes:
      gender: epicene
  - type: SentienceTarget
    flavorKind: station-event-random-sentience-flavor-corgi
  - type: MobPrice
    price: 200
  - type: Tag
    tags:
    - VimPilot
  - type: RandomBark
    barkType: dog
  - type: ScentTracker

- type: entity
  name: "зіпсований коргі"
  parent: [ MobCorgi, MobCombat ]
  id: MobCorgiNarsi
  description: "Іане! Ні!"
  components:
  - type: Absorbable
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Pets/corgi.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: narsian
  - type: DamageStateVisuals
    states:
      Alive:
        Base: narsian
      Dead:
        Base: narsian_dead
  - type: MeleeWeapon
    soundHit:
        path: /Audio/Effects/bite.ogg
    angle: 0
    animation: WeaponArcBite
    damage:
      types:
        Piercing: 5
        Slash: 5
  - type: InputMover
  - type: MobMover
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
  - type: NpcFactionMember
    factions:
    - SimpleHostile
    - PsionicInterloper #Nyano - Summary: makes a psionic interloper.
  - type: InteractionPopup
    successChance: 0 # Override the automatic success that would normally be inherited from MobCorgi.
    interactSuccessString: petting-success-corrupted-corgi # Normally impossible but added an easter egg just in case.
    interactFailureString: petting-failure-corrupted-corgi
  - type: Grammar
    attributes:
      gender: epicene
  - type: Bloodstream
    bloodReagent: DemonsBlood
  - type: Damageable
    damageContainer: CorporealSpirit # Nyanotrasen - Corporeal Spirit allows Holy water to do damage
    damageModifierSet: Infernal
  - type: Temperature
    heatDamageThreshold: 4000 #They come from hell, so..
    coldDamageThreshold: 260
    currentTemperature: 310.15
    coldDamage:
      types:
        Cold : 1 #per second, scales with temperature & other constants
    specificHeat: 42
    heatDamage:
      types:
        Heat : 1 #per second, scales with temperature & other constants

- type: entity
  name: "цуценя коргі"
  parent: MobCorgi
  id: MobCorgiPuppy
  description: "Маленький коргі! Ох..."
  components:
  - type: VentCrawler # goobstation - Ventcrawl
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Pets/corgi.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: puppy
  - type: Inventory
    speciesId: puppy
    templateId: pet
  - type: InventorySlots
  - type: DamageStateVisuals
    states:
      Alive:
        Base: puppy
      Critical:
        Base: puppy_dead
      Dead:
        Base: puppy_dead
  - type: Grammar
    attributes:
      gender: epicene

- type: entity
  name: "кішка"
  parent: SimpleMobBase
  id: MobCat
  description: "Котячий улюбленець, дуже кумедний."
  components:
  - type: Absorbable
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Pets/cat.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: cat
  - type: Carriable
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 9
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Inventory
    speciesId: cat
    templateId: pet
  - type: InventorySlots
  - type: Strippable
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
  - type: DamageStateVisuals
    states:
      Alive:
        Base: cat
      Dead:
        Base: cat_dead
  - type: Speech
    speechSounds: Cat
    speechVerb: SmallMob
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 3
  - type: LanguageKnowledge
    speaks:
    - Cat
    understands:
    - Cat
  - type: InteractionPopup
    successChance: 0.7
    interactSuccessString: petting-success-cat
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/cat_meow.ogg
  - type: MeleeWeapon
    altDisarm: false
    angle: 0
    animation: WeaponArcBite
    soundHit:
      path: /Audio/Effects/bite.ogg
    damage:
      types:
        Piercing: 5
  - type: Grammar
    attributes:
      gender: epicene
  - type: MobPrice
    price: 200
  - type: Tag
    tags:
    - VimPilot
  - type: VentCrawler # goobstation - Ventcrawl
  - type: RandomBark
    barkType: cat
  - type: HTN # DeltaV - make all cats eat mice
    rootTask:
      task: SimpleHostileCompound
  - type: NpcFactionMember # DeltaV - give cats faction so they fight mice
    factions:
    - Cat
# PIRATE Start: Allow goblins to ride cats
  - type: Strap
    # buckleOffset: "0.025, 0.2"
    whitelist:
      components:
      - PseudoItem
    # unbuckleDistanceSquared: 0.09 # Frontier: (30 cm)^2 - seems unnecessary, but for consistency with vehicles
  - type: Vehicle
  - type: ItemSlots
    slots:
      key_slot:
        disableEject: true
        locked: true
  - type: ContainerFill
    containers:
      key_slot:
      - VehicleKeyATV
# PIRATE End: Allow goblins to ride cats

- type: entity
  name: "кішка каліко"
  id: MobCatCalico
  parent: MobCat
  description: "Котячий улюбленець, дуже кумедний."
  components:
  - type: Sprite
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: cat2
  - type: DamageStateVisuals
    states:
      Alive:
        Base: cat2
      Dead:
        Base: cat2_dead

- type: entity
  name: "синдикіт"
  id: MobCatSyndy
  parent: MobCatSpace
  description: "Вибухове кошеня."
  components:
  - type: Absorbable
  - type: Sprite
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: syndicat
  - type: DamageStateVisuals
    states:
      Alive:
        Base: syndicat
      Dead:
        Base: syndicat_dead
  - type: GhostRole
    prob: 1
    name: ghost-role-information-SyndiCat-name
    allowMovement: true
    description: ghost-role-information-SyndiCat-description
    rules: ghost-role-information-SyndiCat-rules
    mindRoles:
    - MindRoleGhostRoleTeamAntagonist
    raffle:
      settings: default
  - type: GhostTakeoverAvailable
  - type: AutoImplant
    implants:
    - MicroBombImplant
  - type: ExplosionResistance
    damageCoefficient: 0.2
  - type: NpcFactionMember
    factions:
    - Syndicate
  - type: MeleeWeapon
    altDisarm: false
    angle: 0
    animation: WeaponArcBite
    soundHit:
      path: /Audio/Effects/bite.ogg
    damage:
      types:
        Slash: 6
        Piercing: 6
        Structural: 15
  - type: LanguageKnowledge
    speaks:
      - Cat
    understands:
    - Cat
    - TauCetiBasic

- type: entity
  name: "космічний кіт"
  id: MobCatSpace
  parent: MobCat
  description: "Котячий улюбленець, підготовлений до найгіршого."
  components:
  - type: Absorbable
  - type: Sprite
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: spacecat
  - type: DamageStateVisuals
    states:
      Alive:
        Base: spacecat
      Dead:
        Base: spacecat_dead
  - type: Temperature
    heatDamageThreshold: 423
    coldDamageThreshold: 0
  - type: Tag
    tags:
      - DoorBumpOpener
  - type: MovementAlwaysTouching
  - type: PressureImmunity
  - type: StepTriggerImmune
  - type: Insulated
  - type: InteractionPopup
    successChance: 0.7
    interactSuccessString: petting-success-space-cat
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/cat_meow.ogg
  - type: Respirator #It just works?
    minSaturation: 5.0

- type: entity
  name: "каракал"
  id: MobCatCaracal
  parent: MobCat
  description: "Смішно."
  components:
  - type: Sprite
    sprite: Mobs/Pets/caracal.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: caracal_flop
  - type: DamageStateVisuals
    states:
      Alive:
        Base: caracal_flop
      Dead:
        Base: caracal_dead
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 30
        mask:
        - MobMask
        layer:
        - MobLayer

- type: entity
  name: "кошеня"
  parent: MobCat
  id: MobCatKitten
  description: "Маленьке і пухнасте."
  components:
  - type: Absorbable
  - type: Sprite
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: kitten
  - type: DamageStateVisuals
    states:
      Alive:
        Base: kitten
      Critical:
        Base: kitten_dead
      Dead:
        Base: kitten_dead
  - type: Carriable
    freeHandsRequired: 1
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 1
  - type: MobPrice
    price: 100
  - type: MeleeWeapon
    damage:
      types:
        Piercing: 3
  - type: MobThresholds
    thresholds:
      0: Alive
      25: Dead
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 2
        mask:
        - MobMask
        layer:
        - MobLayer

- type: entity
  name: "лінивець"
  parent: SimpleMobBase
  id: MobSloth
  description: "Дуже повільна тварина. Для людей з низьким рівнем енергії."
  components:
  - type: Absorbable
  - type: MovementSpeedModifier
    baseWalkSpeed : 1
    baseSprintSpeed : 1
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Pets/sloth.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: sloth
  - type: Carriable
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 15
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Inventory
    speciesId: sloth
    templateId: pet
  - type: InventorySlots
  - type: Strippable
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
  - type: DamageStateVisuals
    states:
      Alive:
        Base: sloth
      Dead:
        Base: sloth_dead
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 3
  - type: InteractionPopup
    successChance: 0.9
    interactSuccessString: petting-success-sloth
    interactFailureString: petting-failure-sloth
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/sloth_squeak.ogg
  - type: Grammar
    attributes:
      gender: epicene
  - type: Tag
    tags:
    - VimPilot
  - type: LanguageKnowledge # WHAT DOES THE SLOTH SAY???????
    speaks:
    - Hissing
    understands:
    - Hissing

- type: entity
  name: "тхір"
  parent: SimpleMobBase
  id: MobFerret
  description: "Просто дурник!"
  components:
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Pets/ferret.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: ferret
  - type: Carriable
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 4
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Inventory
    speciesId: fox #close enough
    templateId: pet
  - type: InventorySlots
  - type: Strippable
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
  - type: DamageStateVisuals
    states:
      Alive:
        Base: ferret
      Dead:
        Base: ferret_dead
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 3
  - type: InteractionPopup
    successChance: 0.8
    interactDelay: 1.5 # Avoids overlapping SFX due to spam - these SFX are a little longer than the typical 1 second.
    interactSuccessString: petting-success-soft-floofy
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/ferret_happy.ogg
  - type: Grammar
    attributes:
      gender: epicene
  - type: Tag
    tags:
    - VimPilot
  - type: LanguageKnowledge
    speaks:
    - Hissing
    understands:
    - Hissing

- type: entity
  name: "хом'як"
  parent: [ SimpleMobBase, MobCombat ]
  id: MobHamster
  description: "Симпатичний, пухнастий, міцний хом'як."
  components:
  - type: VentCrawler # goobstation - Ventcrawl
  - type: GhostRole
    makeSentient: true
    allowSpeech: true
    allowMovement: true
    name: ghost-role-information-hamster-name
    description: ghost-role-information-hamster-description
  - type: GhostTakeoverAvailable
  - type: Speech
    speechVerb: SmallMob
    speechSounds: Squeak
    allowedEmotes: ['Squeak']
  - type: Sprite
    drawdepth: SmallMobs
    sprite: Mobs/Animals/hamster.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base", "movement"]
      state: hamster-0
  - type: SpriteMovement
    movementLayers:
      movement:
        state: hamster-moving-0
    noMovementLayers:
      movement:
        state: hamster-0
  - type: Item
    size: Tiny
  - type: Physics
  - type: FaxableObject
    insertingState: inserting_hamster
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.2
        density: 0.8
        mask:
        - SmallMobMask
        layer:
        - SmallMobLayer
  - type: MobState
  - type: Deathgasp
  - type: MobStateActions
    actions:
      Critical:
      - ActionCritSuccumb
      - ActionCritFakeDeath
      - ActionCritLastWords
  - type: MobThresholds
    thresholds:
      0: Alive
      40: Critical
      60: Dead
  - type: MovementSpeedModifier
    baseWalkSpeed : 4
    baseSprintSpeed : 4
  - type: Inventory
    speciesId: hamster
    templateId: hamster
  - type: InventorySlots
  - type: Strippable
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
  - type: DamageStateVisuals
    states:
      Alive:
        Base: hamster-0
      Critical:
        Base: dead-0
      Dead:
        Base: splat-0
  - type: Food
  - type: Hunger
    baseDecayRate: 0.3
  - type: Extractable
    grindableSolutionName: food
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Blood
          Quantity: 55
        - ReagentId: Fat
          Quantity: 5
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 1
  - type: LanguageKnowledge
    speaks:
    - Mouse
    understands:
    - Mouse
  - type: Tag
    tags:
    - VimPilot
    - ChefPilot
    - Trash
    - Hamster
    - Meat
  - type: Respirator
    damage:
      types:
        Asphyxiation: 0.25
    damageRecovery:
      types:
        Asphyxiation: -0.25
  - type: Barotrauma
    damage:
      types:
        Blunt: 0.1
  - type: CombatMode
  - type: MeleeWeapon
    soundHit:
        path: /Audio/Effects/bite.ogg
    angle: 0
    animation: WeaponArcBite
    damage:
      types:
        Piercing: 2
  - type: InteractionPopup
    successChance: 0.4
    interactSuccessString: petting-success-hamster
    interactFailureString: petting-failure-hamster
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/fox_squeak.ogg
  - type: Bloodstream
    bloodMaxVolume: 60
  - type: CanEscapeInventory
    baseResistTime: 3
  - type: MobPrice
    price: 60
  - type: NonSpreaderZombie
  - type: RandomBark
    barkMultiplier: 0.45
    barkType: mouse # duh
  - type: FireVisuals
    sprite: Mobs/Effects/onfire.rsi
    normalState: Mouse_burning

- type: entity
  parent: MobHamster
  id: MobHamsterPet
  components:
  - type: HTN
    rootTask:
      task: FollowCompound
    blackboard:
      IdleRange: !type:Single
        1.5
      FollowCloseRange: !type:Single
        1.0
      FollowRange: !type:Single
        2.0

- type: entity
  name: "свиня"
  parent: SimpleMobBase
  id: MobPig
  description: "Хрюк."
  components:
  - type: Absorbable
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: pig
      sprite: Mobs/Animals/pig.rsi
  - type: Carriable
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 750
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Inventory
    speciesId: pig
    templateId: pet
  - type: Tag
    tags:
    - DoorBumpOpener
    - Pig
    - VimPilot
  - type: Reproductive
    partnerWhitelist:
      tags:
      - Pig
    offspring:
    - id: MobPig
  - type: ReproductivePartner
  - type: InventorySlots
  - type: Strippable
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
  - type: DamageStateVisuals
    states:
      Alive:
        Base: pig
      Dead:
        Base: dead
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 6
  - type: Grammar
    attributes:
      gender: epicene
  - type: InteractionPopup
    successChance: 0.7
    interactSuccessString: petting-success-pig
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/pig_oink.ogg
  - type: LanguageKnowledge
    speaks:
    - Pig
    understands:
    - Pig
  - type: SentienceTarget
    flavorKind: station-event-random-sentience-flavor-organic
  - type: NpcFactionMember
    factions:
    - Passive
# PIRATE Start: Allow goblins to ride cats
  - type: Strap
    # buckleOffset: "0.025, 0.2"
    whitelist:
      components:
      - PseudoItem
    # unbuckleDistanceSquared: 0.09 # Frontier: (30 cm)^2 - seems unnecessary, but for consistency with vehicles
  - type: Vehicle
  - type: ItemSlots
    slots:
      key_slot:
        disableEject: true
        locked: true
  - type: ContainerFill
    containers:
      key_slot:
      - VehicleKeyATV
# PIRATE End: Allow goblins to ride cats

- type: entity
  name: "німфа діони"
  parent: SimpleMobBase
  id: MobDionaNymph
  description: "Це як кіт, тільки.... гіллястий."
  components:
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: nymph
      sprite: Mobs/Animals/nymph.rsi
  - type: Carriable
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 15
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Inventory
    speciesId: cat
    templateId: pet
  - type: InventorySlots
  - type: Strippable
  - type: Bloodstream
    bloodReagent: Sap
    bloodMaxVolume: 60
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
  - type: DamageStateVisuals
    states:
      Alive:
        Base: nymph
      Critical:
        Base: nymph_sleep
      Dead:
        Base: nymph_dead
  - type: Butcherable
    spawned:
    - id: MaterialWoodPlank1
      amount: 2
  - type: InteractionPopup
    successChance: 0.7
    interactSuccessString: petting-success-nymph
    interactFailureString: petting-failure-nymph
    interactSuccessSound:
      path: /Audio/Animals/nymph_chirp.ogg
  - type: MobThresholds
    thresholds:
      0: Alive
      30: Critical
      60: Dead
  - type: MovementSpeedModifier
    baseWalkSpeed : 2.5
    baseSprintSpeed : 4.5
  - type: Grammar
    attributes:
      gender: epicene
  - type: Speech
    speechVerb: Plant
  - type: Tag
    tags:
    - DoorBumpOpener
    - VimPilot
  - type: Emoting
  - type: BodyEmotes
    soundsId: Nymph
  - type: Reform
    actionPrototype: DionaReformAction
    reformTime: 10
    popupText: diona-reform-attempt
    reformPrototype: MobDionaReformed
  - type: LanguageKnowledge
    speaks:
    - RootSpeak
    understands:
    - TauCetiBasic
    - RootSpeak

- type: entity
  parent: MobDionaNymph
  id: MobDionaNymphAccent # No talky. For non-brain & wild nymphs
  suffix: Accent
  components:
  - type: ReplacementAccent
    accent: nymph
  - type: RandomBark # Using the default barks since they aren't going to talk anyway
