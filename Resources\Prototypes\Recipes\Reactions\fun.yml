- type: reaction
  id: Carpetium
  reactants:
    SpaceDrugs:
      amount: 1
    Fiber:
      amount: 2
  products:
    Carpetium: 3

- type: reaction
  id: BuzzochloricBees
  reactants:
    Saxoite: # do you like jazz (not sorry)
      amount: 1
    Fiber: # bees are fuzzy
      amount: 1
    GroundBee: # you need bee for the bees
      amount: 1
    Chlorine: # the chloric part of buzzochloric
      amount: 1
    UnstableMutagen: # to bring the buzz to life
      amount: 1
  products:
    BuzzochloricBees: 3

- type: reaction
  id: CreateSoap
  impact: Low
  quantized: true
  reactants:
    Fat:
      amount: 15
    TableSalt:
      amount: 10
    Water:
      amount: 10
  effects:
    - !type:CreateEntityReactionEffect
      entity: Soap

- type: reaction
  id: CreateSoapHomemade
  impact: Low
  quantized: true
  reactants:
    Fat:
      amount: 15
    TableSalt:
      amount: 10
    Blood:
      amount: 10
  effects:
    - !type:CreateEntityReactionEffect
      entity: SoapHomemade

- type: reaction
  id: Meatification
  impact: Low
  quantized: true
  reactants:
    Fat:
      amount: 25
    Nutriment:
      amount: 5
    Blood:
      amount: 10
    Carbon:
      amount: 10
  effects:
    - !type:CreateEntityReactionEffect
      entity: FoodMeat

- type: reaction
  id: SpaceGlue
  minTemp: 370
  reactants:
    SpaceLube:
      amount: 1
    Slime:
      amount: 1
  products:
    SpaceGlue: 2

- type: reaction
  id: Licoxide
  reactants:
    Lead:
      amount: 1
    Zinc:
      amount: 1
  products:
    Licoxide: 1

- type: reaction
  id: PlasticSheet # It's here because "haha look you can get smooth beautiful sheets of plastic from small cylindric beaker" (jokes aside: i dont know where should i put it)
  impact: Low
  quantized: true
  minTemp: 374
  reactants:
    Oil:
      amount: 5
    Ash:
      amount: 3
    SulfuricAcid:
      amount: 2
  effects:
    - !type:CreateEntityReactionEffect
      entity: SheetPlastic1

- type: reaction
  id: FlashFreezeIce
  quantized: true
  reactants:
    Fresium:
      amount: 1
    Water:
      amount: 1
  effects:
  - !type:CreateGas
    gas: Frezon
  products:
    Ice: 5

- type: reaction
  id: Fresium
  priority: 20
  maxTemp: 300
  reactants:
    Frezon:
      amount: 3
    Plasma:
      amount: 1
      catalyst: true
    Nitrogen:
      amount: 2
    Cryoxadone:
      amount: 0.22
    TableSalt:
      amount: 0.08
    Water:
      amount: 1.5
  effects:
  - !type:CreateGas
    gas: Nitrogen
  products:
    Fresium: 5

- type: reaction
  id: FiberBreakdown
  source: true
  requiredMixerCategories:
  - Centrifuge
  reactants:
    Fiber:
      amount: 5
    Water:
      amount: 10
      catalyst: true
  products:
    Carbon: 3
    Sugar: 2

- type: reaction
  id: Laughter
  reactants:
    JuiceBanana:
      amount: 1
    Sugar:
      amount: 1
  products:
    Laughter: 2

- type: reaction
  id: CreateCrystals
  quantized: true
  minTemp: 374
  reactants:
    Sugar:
      amount: 15
    Water:
      amount: 15
    Ethanol:
      amount: 5
  effects:
    - !type:CreateEntityReactionEffect
      entity: ShardCrystalRandom

- type: reaction
  id: Gunpowder
  impact: Low
  quantized: true
  minTemp: 374
  reactants:
    Potassium:
      amount: 6
    Sulfur:
      amount: 2
    Charcoal:
      amount: 2
  effects:
    - !type:CreateEntityReactionEffect
      entity: MaterialGunpowder