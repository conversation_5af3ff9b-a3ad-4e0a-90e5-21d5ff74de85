- type: entity
  id: ActionMechCycleEquipment
  name: "Цикл"
  description: "Цикли вибраного на даний момент обладнання"
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    itemIconStyle: NoItem
    icon:
      sprite: Interface/Actions/actions_mecha.rsi
      state: mech_cycle_equip_on
    event: !type:MechToggleEquipmentEvent
    useDelay: 0.5

- type: entity
  id: ActionMechOpenUI
  name: "Панель управління"
  description: "Відкриває панель керування механізмом"
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    itemIconStyle: NoItem
    icon:
      sprite: Interface/Actions/actions_mecha.rsi
      state: mech_view_stats
    event: !type:MechOpenUiEvent
    useDelay: 1

- type: entity
  id: ActionMechEject
  name: "Викида<PERSON>!"
  description: "Катапультує пілота з меча"
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    itemIconStyle: NoItem
    icon:
      sprite: Interface/Actions/actions_mecha.rsi
      state: mech_eject
    event: !type:MechEjectPilotEvent
