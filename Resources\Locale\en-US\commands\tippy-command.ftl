cmd-tippy-desc = Broadcast a message as Tippy the clown.
cmd-tippy-help = tippy <user | all> <message> [entity prototype] [speak time] [slide time] [waddle interval]
cmd-tippy-auto-1 = <user | all>
cmd-tippy-auto-2 = message
cmd-tippy-auto-3 = entity prototype
cmd-tippy-auto-4 = speak time, in seconds
cmd-tippy-auto-5 = slide time, in seconds
cmd-tippy-auto-6 = waddle interval, in seconds
cmd-tippy-error-no-user = User not found.
cmd-tippy-error-no-prototype = Prototype not found: {$proto}

cmd-tip-desc = Spawn a random game tip.
