﻿- type: entity
  id: RandomAnomalySpawner
  name: "випадковий генератор аномалій"
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: red
    - sprite: Structures/Specific/anomaly.rsi
      state: anom1
  - type: RandomSpawner
    prototypes:
    - AnomalyPyroclastic
    - AnomalyGravity
    - AnomalyElectricity
    - AnomalyFlesh
    - AnomalyBluespace
    - AnomalyIce
    - RandomRockAnomalySpawner
    - AnomalyLiquid
    - AnomalyFlora
    - AnomalyShadow
    chance: 1
    offset: 0.15 # not to put it higher. The anomaly sychnronizer looks for anomalies within this radius, and if the radius is higher, the anomaly can be attracted from a neighboring tile.

- type: entity
  id: RandomRockAnomalySpawner
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
    - state: red
    - sprite: Structures/Specific/anomaly.rsi
      state: anom6
  - type: RandomSpawner
    prototypes:
    - AnomalyRockIron
    - AnomalyRockSilver
    - AnomalyRockQuartz
    - AnomalyRockUranium
    chance: 1
    offset: 0.15