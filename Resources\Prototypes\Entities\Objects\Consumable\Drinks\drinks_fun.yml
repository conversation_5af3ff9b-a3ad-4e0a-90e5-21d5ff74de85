- type: entity
  parent: DrinkBase
  id: DrinkSpaceGlue
  name: "тюбик космічного клею"
  description: "Високоефективний клей, призначений для обслуговування надзвичайно складного механічного обладнання. НЕ ПИТИ!"
  components:
  - type: Openable
    sound:
      collection: packetOpenSounds
  - type: Sprite
    sprite: Objects/Consumable/Drinks/glue-tube.rsi
    layers:
      - state: icon
        map: ["enum.OpenableVisuals.Layer"]
      - state: fill1
        map: [ "enum.SolutionContainerLayers.Fill" ]
        visible: false
      - state: icon-front
        map: [ "enum.SolutionContainerLayers.Overlay" ]
  - type: Appearance
  - type: Glue
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: SpaceGlue
          Quantity: 30
  - type: SolutionContainerVisuals
    maxFillLevels: 6
    fillBaseName: fill
  - type: GenericVisualizer
    visuals:
      enum.OpenableVisuals.Opened:
        enum.OpenableVisuals.Layer:
          True: {state: "icon_open"}
          False: {state: "icon"}
  - type: Tag
    tags:
      - DrinkSpaceGlue
  - type: TrashOnSolutionEmpty
    solution: drink

- type: entity
  parent: DrinkBase
  id: DrinkSpaceLube
  name: "тюбик космічного мастила"
  description: "Високоефективне мастило, призначене для обслуговування надзвичайно складного механічного обладнання."
  components:
  - type: Openable
    sound:
      collection: packetOpenSounds
  - type: Sprite
    sprite: Objects/Consumable/Drinks/lube-tube.rsi
    layers:
      - state: icon
        map: ["enum.OpenableVisuals.Layer"]
      - state: fill1
        map: [ "enum.SolutionContainerLayers.Fill" ]
        visible: false
      - state: icon-front
        map: [ "enum.SolutionContainerLayers.Overlay" ]
  - type: Appearance
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: SpaceLube
          Quantity: 30
  - type: SolutionContainerVisuals
    maxFillLevels: 6
    fillBaseName: fill
  - type: GenericVisualizer
    visuals:
      enum.OpenableVisuals.Opened:
        enum.OpenableVisuals.Layer:
          True: {state: "icon_open"}
          False: {state: "icon"}
  - type: Lube
  - type: TrashOnSolutionEmpty
    solution: drink


# Mopwata
- type: weightedRandomFillSolution
  id: RandomFillMopwata
  fills:
  - quantity: 10
    weight: 5
    reagents:
    - Blood
    - CopperBlood
    - Slime
    - Vomit
  - quantity: 10
    weight: 4
    reagents:
    - Coffee
    - Tea
    - Cola
    - RootBeer
    - DrGibb
    - GrapeSoda
    - Starkist
    - SpaceUp
    - SpaceMountainWind
    - LemonLime
    - PwrGame
  - quantity: 10
    weight: 3
    reagents:
    - Whiskey
    - Vodka
    - Beer
    - Ale
    - Rum
    - Tequila
    - Absinthe
  - quantity: 10
    weight: 1
    reagents:
    - SpaceDrugs
    - Mold
    - SpaceLube
    - SpaceGlue
    - SpaceCleaner
    - MilkSpoiled
    - FourteenLoko

- type: entity
  parent: DrinkBottleGlassBaseFull
  id: DrinkMopwataBottleRandom
  name: "смачна мопвата"
  description: "Каламутна коричнева пляшка з вицвілою етикеткою із зображенням швабри. Від неї віє каламутним... вінтажем."
  components:
  - type: Item
    size: Small
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 50
        reagents:
        - ReagentId: Mopwata
          Quantity: 40
  - type: RandomFillSolution
    solution: drink
    weightedRandomId: RandomFillMopwata
  - type: PressurizedSolution
    solution: drink
  - type: Shakeable
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.OpenableVisuals.Opened:
        enum.OpenableVisuals.Layer:
          True: {state: "icon_open"}
          False: {state: "icon_empty"}
  - type: Sprite
    sprite: Objects/Consumable/Drinks/mopwata.rsi
    layers:
      - state: icon
        map: ["enum.OpenableVisuals.Layer"]
      - state: fill-1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
  - type: SolutionContainerVisuals
    maxFillLevels: 5
    fillBaseName: fill-
