- type: entity
  name: "Щурячий Король"
  id: MobRatKing
  parent: [ SimpleMobBase, MobCombat ]
  description: "Він щур. Він тут керулить."
  components:
  - type: VentCrawler # goobstation - Ventcrawl
  - type: CombatMode
  - type: MovementSpeedModifier
    baseWalkSpeed : 3.00
    baseSprintSpeed : 5.00
  - type: InputMover
  - type: MobMover
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
  - type: Reactive
    groups:
      Flammable: [Touch]
      Extinguish: [Touch]
  - type: NpcFactionMember
    factions:
    - SimpleHostile
  - type: Sprite
    drawdepth: SmallMobs
    sprite: Mobs/Animals/regalrat.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: regalrat
    - map: [ "enum.DamageStateVisualLayers.BaseUnshaded" ]
      state: eyes
      shader: unshaded
  - type: Physics
    bodyType: KinematicController
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.3
        density: 230
        mask:
        - SmallMobMask
        layer:
        - SmallMobLayer
  - type: MobState
  - type: MobThresholds
    thresholds:
      0: Alive
      200: Dead
  - type: MeleeWeapon
    soundHit:
        path: /Audio/Weapons/Xeno/alien_claw_flesh1.ogg
    angle: 0
    animation: WeaponArcClaw
    damage:
      types:
        Slash: 12
        Piercing: 8
  - type: Body
    prototype: Rat
    requiredLegs: 1 # TODO: More than 1 leg
  - type: Hunger # probably should be prototyped
    thresholds:
      Overfed: 200
      Okay: 150
      Peckish: 100
      Starving: 50
      Dead: 0
    baseDecayRate: 0.01666666666
  - type: Thirst
    thresholds:
      OverHydrated: 600
      Okay: 450
      Thirsty: 300
      Parched: 150
      Dead: 0
    baseDecayRate: 0.1
  - type: DamageStateVisuals
    rotate: true
    states:
      Alive:
        Base: regalrat
        BaseUnshaded: eyes
      Critical:
        Base: critical
      Dead:
        Base: dead
  - type: GhostRole
    makeSentient: true
    name: ghost-role-information-rat-king-name
    description: ghost-role-information-rat-king-description
    rules: ghost-role-information-freeagent-rules
    mindRoles:
    - MindRoleGhostRoleFreeAgent
    raffle:
      settings: default
    requirements: # Goobstation - ghost roles requirements
    - !type:CharacterOverallTimeRequirement
      time: 18000 #5 hours
  - type: GhostTakeoverAvailable
  - type: Tag
    tags:
      - CannotSuicide
      - FootstepSound
  - type: NoSlip
  - type: RatKing
    hungerPerArmyUse: 25
    hungerPerDomainUse: 50
  - type: Rummager # DeltaV: Make rummaging a generic component
  - type: MobsterAccent
  - type: Speech
    speechVerb: SmallMob
  - type: Butcherable
    spawned:
    - id: ClothingHeadHatFancyCrown #how did that get there?
      amount: 1
  - type: MobPrice
    price: 2500 # rat wealth
  - type: RandomMetadata
    nameSegments:
    - RegalRatNameKingdom
    - RegalRatNameTitle
  - type: GuideHelp
    guides:
    - MinorAntagonists
  - type: Grammar
    attributes:
      gender: male
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    - Mouse
    understands:
    - TauCetiBasic
    - Mouse
  - type: SurgeryTarget
  - type: UserInterface
    interfaces:
      enum.SurgeryUIKey.Key:
        type: SurgeryBui
  - type: NightVision
    isActive: true
    toggleAction: null
    color: "#808080"
    activateSound: null
    deactivateSound: null

- type: entity
  id: MobRatKingBuff
  parent: MobRatKing
  suffix: Buff
  components:
  - type: Sprite
    sprite: Mobs/Animals/buffrat.rsi
    scale: 1.2, 1.2
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: regalrat
    - map: [ "enum.DamageStateVisualLayers.BaseUnshaded"]
      state: eyes
      shader: unshaded
  - type: MobState
  - type: MobThresholds
    thresholds:
      0: Alive
      350: Dead
  - type: MeleeWeapon
    soundHit:
        path: /Audio/Weapons/Xeno/alien_claw_flesh2.ogg
    angle: 0
    attackRate: 0.75
    animation: WeaponArcFist
    damage:
      types:
        Blunt: 66 #oof ouch owie my bones
  - type: SlowOnDamage
    speedModifierThresholds:
      200: 0.7
      250: 0.5
  - type: GuideHelp
    guides:
    - MinorAntagonists
  - type: Fauna # Lavaland Change

- type: entity
  name: "Щурячий Слуга"
  id: MobRatServant
  parent: [ SimpleMobBase, MobCombat ]
  description: "Він маленький щур. Він тут не керулить"
  categories: [ HideSpawnMenu ] #Must be configured to a King or the AI breaks.
  components:
  - type: VentCrawler # goobstation - Ventcrawl
  - type: Carriable
    freeHandsRequired: 1
  - type: CombatMode
  - type: MovementSpeedModifier
    baseWalkSpeed : 3.5
    baseSprintSpeed : 3.5
  - type: InputMover
  - type: MobMover
  - type: HTN
    rootTask:
      task: RatServantCompound
    blackboard:
      IdleRange: !type:Single
        3.5
      FollowCloseRange: !type:Single
        2.0
      FollowRange: !type:Single
        3.0
  - type: Reactive
    groups:
      Flammable: [Touch]
      Extinguish: [Touch]
  - type: NpcFactionMember
    factions:
    - SimpleHostile
  - type: Sprite
    drawdepth: SmallMobs
    sprite: Mobs/Animals/rat.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base", "movement"]
      state: rat
    - map: [ "enum.DamageStateVisualLayers.BaseUnshaded"]
      state: eyes
      shader: unshaded
  - type: SurgeryTarget
  - type: UserInterface
    interfaces:
      enum.SurgeryUIKey.Key:
        type: SurgeryBui
  - type: SpriteMovement
    movementLayers:
      movement:
        state: rat-moving
    noMovementLayers:
      movement:
        state: rat
  - type: Physics
    bodyType: KinematicController
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.2
        density: 30 #Bulky by mouse standards...
        mask:
        - SmallMobMask
        layer:
        - SmallMobLayer
  - type: MobState
  - type: MobThresholds
    thresholds:
      0: Alive
      15: Critical
      30: Dead
  - type: Stamina
    critThreshold: 60
  - type: MeleeWeapon
    soundHit:
        path: /Audio/Weapons/bladeslice.ogg
    angle: 0
    animation: WeaponArcClaw
    damage:
      types:
        Slash: 5
        Piercing: 3
  - type: Body
    prototype: Rat
    requiredLegs: 1 # TODO: More than 1 leg
  - type: Hunger # probably should be prototyped
    thresholds:
      Overfed: 200
      Okay: 150
      Peckish: 100
      Starving: 50
      Dead: 0
    baseDecayRate: 0.01666666666
  - type: Thirst
    thresholds:
      OverHydrated: 600
      Okay: 450
      Thirsty: 300
      Parched: 150
      Dead: 0
    baseDecayRate: 0.1
  - type: DamageStateVisuals
    states:
      Alive:
        Base: rat
        BaseUnshaded: eyes
      Critical:
        Base: dead
      Dead:
        Base: splat
  - type: Butcherable
    spawned:
    - id: FoodMeatRat
      amount: 1
  - type: Vocal
    sounds:
      Male: Mouse
      Female: Mouse
      Unsexed: Mouse
    wilhelmProbability: 0.001
  - type: Tag
    tags:
      - CannotSuicide
      - FootstepSound
  - type: NoSlip
  - type: MobPrice
    price: 500 # rat wealth
  - type: MobsterAccent
    isBoss: false
  - type: Speech
    speechVerb: SmallMob
  - type: GuideHelp
    guides:
    - MinorAntagonists
  - type: FelinidFood # Nyanotrasen - Felinid, ability to eat rat, see Content.Server/Nyanotrasen/Abilities/Felinid/FelinidSystem.cs
  - type: Food
  - type: Item
    size: Tiny # Delta V - Make them eatable and pickable.
  - type: LanguageKnowledge
    speaks:
    - Mouse
    understands:
    - TauCetiBasic
    - Mouse
  - type: FireVisuals
    sprite: Mobs/Effects/onfire.rsi
    normalState: Mouse_burning
  - type: NightVision
    isActive: true
    toggleAction: null
    color: "#808080"
    activateSound: null
    deactivateSound: null

- type: weightedRandomEntity
  id: RatKingLoot
  weights: # DeltaV: Rebalanced for Rodentia rummaging because we don't have Rat King
    RandomSpawner100: 70 #garbage
    FoodCheeseSlice: 15 #food
    RandomSnacks: 10
    RandomFoodSingle: 5

- type: entity
  id: ActionRatKingRaiseArmy
  name: "Підняти Армію"
  description: "Витратьте трохи голоду, щоб викликати щура-союзника, який допоможе вам захистити себе."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    useDelay: 4
    icon:
      sprite: Interface/Actions/actions_rat_king.rsi
      state: ratKingArmy
    event: !type:RatKingRaiseArmyActionEvent

- type: entity
  id: ActionRatKingDomain
  name: "Володіння Щурячого Короля"
  description: "Витратьте трохи голоду, щоб випустити в повітря хмару аміаку."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    useDelay: 6
    icon:
      sprite: Interface/Actions/actions_rat_king.rsi
      state: ratKingDomain
    event: !type:RatKingDomainActionEvent

- type: entity
  id: ActionRatKingOrderStay
  name: "Стояти!"
  description: "Накажіть своїй армії залишатися на місці."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    useDelay: 1
    icon:
      sprite: Interface/Actions/actions_rat_king.rsi
      state: stayOff
    iconOn:
      sprite: Interface/Actions/actions_rat_king.rsi
      state: stay
    event:
      !type:RatKingOrderActionEvent
        type: Stay
    priority: 5

- type: entity
  id: ActionRatKingOrderFollow
  name: "За мною!"
  description: "Накажи своїй армії слідувати за тобою."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    useDelay: 1
    icon:
      sprite: Interface/Actions/actions_rat_king.rsi
      state: followOff
    iconOn:
      sprite: Interface/Actions/actions_rat_king.rsi
      state: follow
    event:
      !type:RatKingOrderActionEvent
      type: Follow
    priority: 6

- type: entity
  id: ActionRatKingOrderCheeseEm
  name: "Шинкуй їх!"
  description: "Накажіть своїй армії атакувати того, на кого ви вкажете."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    useDelay: 1
    icon:
      sprite: Interface/Actions/actions_rat_king.rsi
      state: attackOff
    iconOn:
      sprite: Interface/Actions/actions_rat_king.rsi
      state: attack
    event:
      !type:RatKingOrderActionEvent
      type: CheeseEm
    priority: 7

- type: entity
  id: ActionRatKingOrderLoose
  name: "Разслабонь"
  description: "Накажи своїй армії діяти за власним бажанням."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    useDelay: 1
    icon:
      sprite: Interface/Actions/actions_rat_king.rsi
      state: looseOff
    iconOn:
      sprite: Interface/Actions/actions_rat_king.rsi
      state: loose
    event:
      !type:RatKingOrderActionEvent
      type: Loose
    priority: 8
