- type: loadout
  id: LoadoutUniformAncientJumpsuit
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  canBeHeirloom: true
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterJobRequirement
      jobs:
        - Passenger
  items:
    - ClothingUniformJumpsuitAncient

# Colored jumpsuits
# Someone please alphabetically order these...
- type: loadout
  id: LoadoutUniformJumpsuitColorWhite
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpsuitColorWhite
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      departments:
        - Civilian
        - Medical
        - Epistemics
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtColorWhite
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtColorWhite
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      departments:
        - Civilian
        - Medical
        - Epistemics
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitFlannel
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitFlannel
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitCasualBlue
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitCasualBlue
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtCasualBlue
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtCasualBlue
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitCasualPurple
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitCasualPurple
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtCasualPurple
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtCasualPurple
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitCasualRed
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitCasualRed
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtCasualRed
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtCasualRed
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitTshirtJeans
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitTshirtJeans
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitTshirtJeansGray
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitTshirtJeansGray
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitTshirtJeansPeach
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitTshirtJeansPeach
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitTShirtKhakiPants
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformTShirtKhakiPants
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutUniformJumpsuitJeansGreen
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitJeansGreen
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitJeansRed
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitJeansRed
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitJeansBrown
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitJeansBrown
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitLostTourist
  category: Uniform
  cost: 1
  exclusive: true
  items:
    - ClothingUniformJumpsuitLostTourist
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

# Hawaiian shirts
- type: loadout
  id: LoadoutUniformJumpsuitHawaiBlack
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitHawaiBlack
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitHawaiBlue
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitHawaiBlue
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitHawaiRed
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitHawaiRed
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitHawaiYellow
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitHawaiYellow
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

# AutoDrobe clothes
- type: loadout
  id: LoadoutUniformDressRed
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformDressRed
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitSober
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitSober
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformSkirtTurtle
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformSkirtTurtle
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformGeisha
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - UniformGeisha
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformCostumeArcDress
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingCostumeArcDress
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutUniformCostumeMioSkirt
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingCostumeMioSkirt
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutUniformCostumeNaota
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingCostumeNaota
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitLoungewear
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitLoungewear
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

# Bartender clothes
- type: loadout
  id: LoadoutUniformJumpsuitBartender
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpsuitBartender
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtBartender
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtBartender
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

# Kendo
- type: loadout
  id: LoadoutUniformKendoHakama
  category: Uniform
  cost: 0
  exclusive: true
  canBeHeirloom: true
  items:
    - ClothingUniformKendoHakama
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformMartialGi
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  canBeHeirloom: true
  items:
    - ClothingUniformMartialGi
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

# Kimono

- type: loadout
  id: LoadoutClothingJumpsuitKimono
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  canBeHeirloom: true
  items:
    - ClothingUniformJumpsuitKimono
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutClothingKimonoBlue
  category: Uniform
  cost: 0
  exclusive: true
  canBeHeirloom: true
  items:
    - ClothingKimonoBlue
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutClothingKimonoPink
  category: Uniform
  cost: 0
  exclusive: true
  canBeHeirloom: true
  items:
    - ClothingKimonoPink
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutClothingKimonoPurple
  category: Uniform
  cost: 0
  exclusive: true
  canBeHeirloom: true
  items:
    - ClothingKimonoPurple
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutClothingKimonoSky
  category: Uniform
  cost: 0
  exclusive: true
  canBeHeirloom: true
  items:
    - ClothingKimonoSky
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutClothingKimonoGreen
  category: Uniform
  cost: 0
  exclusive: true
  canBeHeirloom: true
  items:
    - ClothingKimonoGreen
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

# Gakuran
- type: loadout
  id: LoadoutUniformSchoolGakuranBlack
  category: Uniform
  cost: 0
  exclusive: true
  canBeHeirloom: true
  items:
    - ClothingUniformSchoolGakuranBlack
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

# Schoolgirl uniform
- type: loadout
  id: LoadoutUniformSchoolgirlBlack
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - UniformSchoolgirlBlack
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformSchoolgirlBlue
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - UniformSchoolgirlBlue
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutUniformSchoolgirlCyan
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - UniformSchoolgirlCyan
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutUniformSchoolgirlGreen
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - UniformSchoolgirlGreen
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutUniformSchoolgirlOrange
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - UniformSchoolgirlOrange
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutUniformSchoolgirlPink
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - UniformSchoolgirlPink
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutUniformSchoolgirlPurple
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - UniformSchoolgirlPurple
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutUniformSchoolgirlRed
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - UniformSchoolgirlRed
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformSchoolgirlDusk
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - UniformSchoolgirlDusk
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

- type: loadout
  id: LoadoutUniformSchoolgirlBlazerTan
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - UniformSchoolgirlBlazerTan
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Security
        - Command

# MNK Uniforms
- type: loadout
  id: LoadoutClothingMNKOfficeSkirt
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformMNKOfficeSkirt
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutClothingMNKUnderGarment
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformMNKUnderGarment
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutClothingMNKGymBra
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformMNKGymBra
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutClothingMNKDressBlack
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformMNKDressBlack
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutClothingMNKBlackOveralls
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformMNKBlackOveralls
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutClothingMNKBlackShoulder
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformMNKBlackShoulder
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutClothingMNKTracksuitBlack
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformMNKTracksuitBlack
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

# Suits
- type: loadout
  id: LoadoutClothingJumpsuitSuitBlack
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpsuitSuitBlack
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutClothingJumpsuitSuitBlackAlt
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitSuitBlackAlt
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutClothingJumpsuitSuitBlackMob
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitSuitBlackMob
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutClothingJumpsuitSuitBrown
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitSuitBrown
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutClothingJumpsuitSuitBrownAlt
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitSuitBrownAlt
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutClothingJumpsuitSuitBrownMob
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitSuitBrownMob
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutClothingJumpsuitSuitWhite
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpsuitSuitWhite
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutClothingJumpsuitSuitWhiteAlt
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpsuitSuitWhiteAlt
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutClothingJumpsuitSuitWhiteMob
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpsuitSuitWhiteMob
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

# Colorable dresses
- type: loadout
  id: LoadoutUniformJumpskirtDressAsymmetric
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtDressAsymmetric
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressEvening
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtDressEvening
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressMidi
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtDressMidi
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressOpenShoulder
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtDressOpenShoulder
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressTea
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtDressTea
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressSleeveless
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtDressSleeveless
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressLongSleeve
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtDressLongSleeve
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressBlackAndGold
  category: Uniform
  cost: 0
  exclusive: true
  canBeHeirloom: true
  items:
    - ClothingUniformJumpskirtDressBlackAndGold
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressBlackTango
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtDressBlackTango
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressBlackTangoAlt
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtDressBlackTangoAlt
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtSuitBlueBlazer
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtSuitBlueBlazer
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutUniformJumpskirtDressBlueSundress
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtDressBlueSundress
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutUniformJumpskirtDressCheongsamBlue
  category: Uniform
  cost: 0
  exclusive: true
  canBeHeirloom: true
  items:
    - ClothingUniformJumpskirtDressCheongsamBlue
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutUniformJumpskirtDressCheongsamGreen
  category: Uniform
  cost: 0
  exclusive: true
  canBeHeirloom: true
  items:
    - ClothingUniformJumpskirtDressCheongsamGreen
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutUniformJumpskirtDressCheongsamPurple
  category: Uniform
  cost: 0
  exclusive: true
  canBeHeirloom: true
  items:
    - ClothingUniformJumpskirtDressCheongsamPurple
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutUniformJumpskirtDressCheongsamRed
  category: Uniform
  cost: 0
  exclusive: true
  canBeHeirloom: true
  items:
    - ClothingUniformJumpskirtDressCheongsamRed
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressCheongsamWhite
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  canBeHeirloom: true
  items:
    - ClothingUniformJumpskirtDressCheongsamWhite
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutUniformJumpskirtDressClub
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  canBeHeirloom: true
  items:
    - ClothingUniformJumpskirtDressClub
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressCoveter
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtDressCoveter
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressFlamenco
  category: Uniform
  cost: 0
  exclusive: true
  canBeHeirloom: true
  items:
    - ClothingUniformJumpskirtDressFlamenco
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressFire
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtDressFire
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutUniformJumpskirtDressOrange
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtDressOrange
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutUniformJumpskirtDressYellow
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtDressYellow
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutUniformJumpskirtDressFlowergirl
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtDressFlowergirl
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressFluffy
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtDressFluffy
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressFormalRed
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtDressFormalRed
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressGothic
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtDressGothic
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtKimonoColor
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  canBeHeirloom: true
  items:
    - ClothingUniformJumpskirtKimonoColor
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressLacyGown
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtDressLacyGown
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressLong
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtDressLong
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressLongFlared
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtDressLongFlared
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtQipao
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtQipao
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtQipaoSlim
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtQipaoSlim
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressRedSwept
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtDressRedSwept
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtPencilSkirtGymBra
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtPencilSkirtGymBra
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressPuffy
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtDressPuffy
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtSailor
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtSailor
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressSariGreen
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtDressSariGreen
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutUniformJumpskirtDressSariRed
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtDressSariRed
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressShort
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtDressShort
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressSpider
  category: Uniform
  cost: 0
  exclusive: true
  canBeHeirloom: true
  items:
    - ClothingUniformJumpskirtDressSpider
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressStriped
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtDressStriped
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutUniformJumpskirtDressSundress
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtDressSundress
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutUniformJumpskirtDressSundressWhite
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpskirtDressSundressWhite
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressVictorianBlack
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtDressVictorianBlack
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtDressVictorianRed
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtDressVictorianRed
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpskirtWaitress
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtWaitress
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutUniformJumpskirtDressYellowSwoop
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtDressYellowSwoop
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutUniformJumpsuitSuitAmish
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpsuitSuitAmish
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitSuitAristocratic
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitSuitAristocratic
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitSuitAristocraticTuxedo
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitSuitAristocraticTuxedo
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitAscetic
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpsuitAscetic
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitBarber
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitBarber
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitSuitBlueBlazer
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitSuitBlueBlazer
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutUniformJumpsuitDisheveled
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  canBeHeirloom: true
  items:
    - ClothingUniformJumpsuitDisheveled
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitSuitRegal
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitSuitRegal
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitSuitRegalTuxedo
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitSuitRegalTuxedo
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitRippedPunk
  category: Uniform
  cost: 0
  exclusive: true
  canBeHeirloom: true
  items:
    - ClothingUniformJumpsuitRippedPunk
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitSailor
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpsuitSailor
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitSuitStriped
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitSuitStriped
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitTrackpants
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpsuitTrackpants
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitTurtleneckGrey
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpsuitTurtleneckGrey
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitSuitVictorianRedBlack
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitSuitVictorianRedBlack
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitSuitVictorianRedVest
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitSuitVictorianRedVest
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitSuitVictorianVest
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitSuitVictorianVest
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitWaiter
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitWaiter
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutUniformJumpsuitYogaGymBra
  category: Uniform
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingUniformJumpsuitYogaGymBra
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitBlackTurtleneck
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitBlackTurtleneck
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitBlackTurtleneckFlipped
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitBlackTurtleneckFlipped
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

# Nuclear 14 suits
- type: loadout
  id: LoadoutUniformJumpsuitSuitRolledSleeves
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitSuitRolledSleeves
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitBartenderGrey
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitBartenderGrey
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitCheckered
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitCheckered
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitFlannelOveralls
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitFlannelOveralls
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitCowboyBrown
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitCowboyBrown
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitCowboyGrey
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitCowboyGrey
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitCamouflageShirt
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitCamouflageShirt
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitOliveSweater
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitOliveSweater
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutUniformJumpsuitDesertUniform
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitDesertUniform
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitLumberjack
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitLumberjack
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitLightBlueShirt
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitLightBlueShirt
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutUniformJumpsuitGreyShirt
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitGreyShirt
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command
        - Security

- type: loadout
  id: LoadoutUniformJumpsuitFlannelJeans
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitFlannelJeans
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitFlannelBlackJeans
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitFlannelBlackJeans
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitFlannelKhakis
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitFlannelKhakis
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: LoadoutUniformJumpsuitFlannelBlackKhakis
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitFlannelBlackKhakis
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: ClothingUniformJumpsuitTacticool
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpsuitTacticool
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: ClothingUniformJumpskirtTacticool
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtTacticool
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

- type: loadout
  id: ClothingUniformJumpskirtTacticalMaid
  category: Uniform
  cost: 0
  exclusive: true
  items:
    - ClothingUniformJumpskirtTacticalMaid
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterDepartmentRequirement
      inverted: true
      departments:
        - Command

# Biesel Republic Jackets
- type: loadout
  id: ClothingUniformTCAFArmsman
  category: Uniform
  cost: 0
  canBeHeirloom: true
  exclusive: true
  guideEntry: TCFLInfo
  items:
    - ClothingUniformJumpsuitTCAFArmsman
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterNationalityRequirement
      nationalities:
        - Bieselite
    - !type:CharacterLifepathRequirement
      lifepaths:
        - WarVet

- type: loadout
  id: ClothingUniformTCFLLegate
  category: Uniform
  cost: 0
  canBeHeirloom: true
  exclusive: true
  guideEntry: TCFLInfo
  items:
    - ClothingUniformJumpsuitTCFLLegate
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterNationalityRequirement
      nationalities:
        - Bieselite
    - !type:CharacterLifepathRequirement
      lifepaths:
        - WarVet

- type: loadout
  id: ClothingUniformTCFLPilot
  category: Uniform
  cost: 0
  canBeHeirloom: true
  exclusive: true
  guideEntry: TCFLInfo
  items:
    - ClothingUniformJumpsuitTCFLPilot
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterNationalityRequirement
      nationalities:
        - Bieselite
    - !type:CharacterLifepathRequirement
      lifepaths:
        - WarVet

- type: loadout
  id: ClothingUniformTCFLSentinel
  category: Uniform
  cost: 0
  canBeHeirloom: true
  exclusive: true
  guideEntry: TCFLInfo
  items:
    - ClothingUniformJumpsuitTCFLSentinel
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterNationalityRequirement
      nationalities:
        - Bieselite
    - !type:CharacterLifepathRequirement
      lifepaths:
        - WarVet

- type: loadout
  id: ClothingUniformTCFLVolunteer
  category: Uniform
  cost: 0
  canBeHeirloom: true
  exclusive: true
  guideEntry: TCFLInfo
  items:
    - ClothingUniformJumpsuitTCFLVolunteer
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterNationalityRequirement
      nationalities:
        - Bieselite
    - !type:CharacterLifepathRequirement
      lifepaths:
        - WarVet

# Solarian Aliance Uniforms
- type: loadout
  id: ClothingUniformJumpsuitSolArmyService
  category: Uniform
  cost: 0
  canBeHeirloom: true
  exclusive: true
  guideEntry: SANInfo
  items:
    - ClothingUniformJumpsuitSolArmyService
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterNationalityRequirement
      nationalities:
        - Solarian
    - !type:CharacterLifepathRequirement
      lifepaths:
        - WarVet

- type: loadout
  id: ClothingUniformJumpsuitSolFatigueGreen
  category: Uniform
  cost: 0
  canBeHeirloom: true
  exclusive: true
  guideEntry: SANInfo
  items:
    - ClothingUniformJumpsuitSolFatigueGreen
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterNationalityRequirement
      nationalities:
        - Solarian
    - !type:CharacterLifepathRequirement
      lifepaths:
        - WarVet

- type: loadout
  id: ClothingUniformJumpsuitSolFatigueGrey
  category: Uniform
  cost: 0
  canBeHeirloom: true
  exclusive: true
  guideEntry: SANInfo
  items:
    - ClothingUniformJumpsuitSolFatigueGrey
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterNationalityRequirement
      nationalities:
        - Solarian
    - !type:CharacterLifepathRequirement
      lifepaths:
        - WarVet

- type: loadout
  id: ClothingUniformJumpsuitSolFatigueTan
  category: Uniform
  cost: 0
  canBeHeirloom: true
  exclusive: true
  guideEntry: SANInfo
  items:
    - ClothingUniformJumpsuitSolFatigueTan
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterNationalityRequirement
      nationalities:
        - Solarian
    - !type:CharacterLifepathRequirement
      lifepaths:
        - WarVet

- type: loadout
  id: ClothingUniformJumpsuitSolNavyCoveralls
  category: Uniform
  cost: 0
  canBeHeirloom: true
  exclusive: true
  guideEntry: SANInfo
  items:
    - ClothingUniformJumpsuitSolNavyCoveralls
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterNationalityRequirement
      nationalities:
        - Solarian
    - !type:CharacterLifepathRequirement
      lifepaths:
        - WarVet

- type: loadout
  id: ClothingUniformJumpsuitSolNavySailor
  category: Uniform
  cost: 0
  canBeHeirloom: true
  exclusive: true
  guideEntry: SANInfo
  items:
    - ClothingUniformJumpsuitSolNavySailor
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterNationalityRequirement
      nationalities:
        - Solarian
    - !type:CharacterLifepathRequirement
      lifepaths:
        - WarVet

- type: loadout
  id: ClothingUniformJumpsuitSolServiceWhite
  category: Uniform
  cost: 0
  canBeHeirloom: true
  exclusive: true
  guideEntry: SANInfo
  items:
    - ClothingUniformJumpsuitSolServiceWhite
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutUniformsCivilian
    - !type:CharacterNationalityRequirement
      nationalities:
        - Solarian
    - !type:CharacterLifepathRequirement
      lifepaths:
        - WarVet
