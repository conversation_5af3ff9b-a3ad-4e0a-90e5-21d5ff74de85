- type: material
  id: Glass
  stackEntity: SheetGlass1
  name: materials-glass
  icon: { sprite: Objects/Materials/Sheets/glass.rsi, state: glass }
  color: "#a8ccd7"
  price: 0.075

- type: material
  id: ReinforcedGlass
  stackEntity: SheetRGlass1
  name: materials-reinforced-glass
  icon: { sprite: Objects/Materials/Sheets/glass.rsi, state: rglass }
  color: "#549bb0"
  price: 0.16 # 1-0.5 mix of glass and metal.

- type: material
  id: PlasmaGlass
  stackEntity: SheetPGlass1
  name: materials-plasma-glass
  icon: { sprite: Objects/Materials/Sheets/glass.rsi, state: pglass }
  color: "#b35989"
  price: 0.30 # 1-1 mix of plasma and glass.

- type: material
  id: ReinforcedPlasmaGlass
  stackEntity: SheetRPGlass1
  name: materials-reinforced-plasma-glass
  icon: { sprite: Objects/Materials/Sheets/glass.rsi, state: rpglass }
  color: "#8c4069"
  price: 0.40 # 1-1-0.5 mix of plasma, glass, and metal.

- type: material
  id: BrassGlass
  stackEntity: SheetClockworkGlass1
  name: "годинникове скло"
  icon: { sprite: Objects/Materials/Sheets/glass.rsi, state: cglass }
  color: "#9b6f07"
  price: 0.0875 # average of glass and brass.

- type: material
  id: UraniumGlass
  stackEntity: SheetUGlass1
  name: "уранове скло"
  icon: { sprite: Objects/Materials/Sheets/glass.rsi, state: uglass }
  color: "#3cab38"
  price: 0.32 # 1-1 mix of uranium and glass.

- type: material
  id: ReinforcedUraniumGlass
  stackEntity: SheetRUGlass1
  name: "армоване уранове скло"
  icon: { sprite: Objects/Materials/Sheets/glass.rsi, state: ruglass }
  color: "#2d872a"
  price: 0.7 # 2-2-1 mix of uranium, glass, and metal.
