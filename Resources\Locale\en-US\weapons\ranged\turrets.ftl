# Deployable turret component
deployable-turret-component-activating = Deploying...
deployable-turret-component-deactivating = Deactivating...
deployable-turret-component-activate = Activate
deployable-turret-component-deactivate = Deactivate
deployable-turret-component-access-denied = Access denied
deployable-turret-component-no-ammo = Weapon systems depleted
deployable-turret-component-is-broken = The turret is heavily damaged and must be repaired
deployable-turret-component-cant-access-wires = You can't reach the maintenance panel while the turret is active
# Turret notification for station AI
station-ai-turret-is-attacking-warning = {CAPITALIZE($source)} has engaged a hostile target.