﻿- type: entity
  id: AutolatheMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата автолата"
  description: "Друкована плата для токарного верстата"
  components:
    - type: MachineBoard
      prototype: Autolathe
      requirements:
        MatterBin: 3
        Manipulator: 1
      materialRequirements:
        Glass: 1

- type: entity
  parent: BaseMachineCircuitboard
  id: AutolatheHyperConvectionMachineCircuitboard
  name: "машинна плата гіперконвекційного автолата"
  description: "Друкована плата для гіперконвекційного автолата"
  components:
  - type: MachineBoard
    prototype: AutolatheHyperConvection
    requirements:
      MatterBin: 3
    materialRequirements:
      Glass: 1
    tagRequirements:
      Igniter:
        Amount: 1
        DefaultPrototype: Igniter
        ExamineName: Igniter
  - type: ReverseEngineering # Delta
    difficulty: 2
    recipes:
      - AutolatheHyperConvectionMachineCircuitboard

- type: entity
  id: ProtolatheMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата протолата"
  description: "Машинна друкована плата для протолата."
  components:
    - type: MachineBoard
      prototype: Protolathe
      requirements:
        MatterBin: 2
        Manipulator: 2
      tagRequirements:
        GlassBeaker:
          Amount: 2
          DefaultPrototype: Beaker
          ExamineName: Glass Beaker

- type: entity
  parent: BaseMachineCircuitboard
  id: ProtolatheHyperConvectionMachineCircuitboard
  name: "машинна плата гіперконвекційного протолата"
  description: "Машинна друкована плата для гіперконвекційного протолата."
  components:
  - type: MachineBoard
    prototype: ProtolatheHyperConvection
    requirements:
      MatterBin: 2
    tagRequirements:
      GlassBeaker:
        Amount: 2
        DefaultPrototype: Beaker
        ExamineName: Glass Beaker
      Igniter:
        Amount: 1
        DefaultPrototype: Igniter
        ExamineName: Igniter
  - type: ReverseEngineering # Delta
    difficulty: 2
    recipes:
      - ProtolatheHyperConvectionMachineCircuitboard

- type: entity
  id: BiofabricatorMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата біофабрикатора"
  description: "Машинна друкована плата для біофабрикатора."
  components:
    - type: MachineBoard
      prototype: Biofabricator
      requirements:
        MatterBin: 4
      materialRequirements:
        Glass: 1
    - type: ReverseEngineering # Delta
      difficulty: 2
      recipes:
        - BiofabricatorMachineCircuitboard

- type: entity
  id: SecurityTechFabCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата техфабу служби системи"
  description: "Машинна друкована плата для технологічного центру безпеки."
  components:
    - type: Sprite
      state: security
    - type: MachineBoard
      prototype: SecurityTechFab
      requirements:
        MatterBin: 2
        Manipulator: 2
      tagRequirements:
        GlassBeaker:
          Amount: 2
          DefaultPrototype: Beaker
          ExamineName: Glass Beaker

- type: entity
  id: AmmoTechFabCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата боєприпасного автолату"
  description: "Друкована машиною плата для автоматичного станка виробництва боєприпасів"
  components:
    - type: Sprite
      state: security
    - type: MachineBoard
      prototype: AmmoTechFab
      requirements:
        MatterBin: 1
        Manipulator: 1

- type: entity
  id: MedicalTechFabCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата медичного автолату"
  description: "Машинна плата для медичного автолату."
  components:
    - type: Sprite
      state: medical
    - type: MachineBoard
      prototype: MedicalTechFab
      requirements:
        MatterBin: 2
        Manipulator: 2
      tagRequirements:
        GlassBeaker:
          Amount: 2
          DefaultPrototype: Beaker
          ExamineName: Glass Beaker
    - type: StealTarget
      stealGroup: MedicalTechFabCircuitboard

- type: entity
  id: CircuitImprinterMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата імпринтера мікросхем"
  components:
  - type: Sprite
    state: science
  - type: MachineBoard
    prototype: CircuitImprinter
    requirements:
      MatterBin: 1
      Manipulator: 1
    tagRequirements:
      GlassBeaker:
        Amount: 2
        DefaultPrototype: Beaker
        ExamineName: Glass Beaker

- type: entity
  parent: BaseMachineCircuitboard
  id: CircuitImprinterHyperConvectionMachineCircuitboard
  name: "плата гіперконвекційного схемного імпринтера"
  description: "Машинна друкована плата для гіперконвекційного контурного імпринтера."
  components:
  - type: Sprite
    state: science
  - type: MachineBoard
    prototype: CircuitImprinterHyperConvection
    requirements:
      MatterBin: 2
    tagRequirements:
      GlassBeaker:
        Amount: 2
        DefaultPrototype: Beaker
        ExamineName: Glass Beaker
      Igniter:
        Amount: 1
        DefaultPrototype: Igniter
        ExamineName: Igniter

- type: entity
  id: ExosuitFabricatorMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата фабрикатора екзокостюмів"
  components:
  - type: Sprite
    state: science
  - type: MachineBoard
    prototype: ExosuitFabricator
    requirements:
      MatterBin: 1
      Manipulator: 3
    materialRequirements:
      Glass: 5
  - type: ReverseEngineering # Nyano
    difficulty: 2
    recipes:
      - ExosuitFabricatorMachineCircuitboard
  - type: GuideHelp
    guides:
    - Robotics

# yes i know this prototype name is long i'm just following conventions
- type: entity
  id: ResearchAndDevelopmentServerMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата серверу НВ"
  description: "Машинна друкована плата для сервера наукового відділу."
  components:
  - type: Sprite
    state: science
  - type: MachineBoard
    prototype: ResearchAndDevelopmentServer
    materialRequirements:
      Plasma: 5

- type: entity
  id: UniformPrinterMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата принтеру уніформи"
  components:
  - type: MachineBoard
    prototype: UniformPrinter
    requirements:
      MatterBin: 1
      Manipulator: 2

- type: entity
  id: VaccinatorMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата машини для вакцинації"
  components:
  - type: Sprite
    state: medical
  - type: MachineBoard
    prototype: Vaccinator
    requirements:
      MatterBin: 1
      Manipulator: 1
    materialRequirements:
      Cable: 5
    tagRequirements:
        GlassBeaker:
          Amount: 1
          DefaultPrototype: Beaker
          ExamineName: Glass Beaker
  - type: ReverseEngineering # Nyano
    difficulty: 2
    recipes:
      - VaccinatorMachineCircuitboard

- type: entity
  id: DiagnoserMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата діагностичної машини"
  components:
  - type: Sprite
    state: medical
  - type: MachineBoard
    prototype: DiseaseDiagnoser
    materialRequirements:
      Cable: 5
    tagRequirements:
      GlassBeaker:
        Amount: 1
        DefaultPrototype: Beaker
        ExamineName: Glass Beaker
    componentRequirements:
      DiseaseSwab:
        Amount: 1
        DefaultPrototype: DiseaseSwab
        ExamineName: Swab
  - type: ReverseEngineering # Nyano
    difficulty: 2
    recipes:
      - DiagnoserMachineCircuitboard

- type: entity
  id: ArtifactAnalyzerMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата аналізатора артефактів"
  description: "Машинна друкована плата для аналізатора артефактів."
  components:
    - type: Sprite
      state: science
    - type: MachineBoard
      prototype: MachineArtifactAnalyzer
      requirements:
        Manipulator: 3
        Capacitor: 1
      materialRequirements:
        Glass: 5
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - ArtifactAnalyzerMachineCircuitboard

- type: entity
  id: ArtifactCrusherMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата дробарки для артефактів"
  description: "Друкована плата машини для дробарки артефактів."
  components:
  - type: Sprite
    state: science
  - type: MachineBoard
    prototype: MachineArtifactCrusher
    requirements:
      Manipulator: 2
    materialRequirements:
      Glass: 1
      Steel: 5
  - type: ReverseEngineering # Delta
    difficulty: 3
    recipes:
      - ArtifactCrusherMachineCircuitboard

- type: entity
  parent: BaseMachineCircuitboard
  id: AnomalyVesselCircuitboard
  name: "машинна плата аномального судна"
  description: "Машинна друкована плата для аномального судна."
  components:
    - type: Sprite
      state: science
    - type: MachineBoard
      prototype: MachineAnomalyVessel
      requirements:
        Capacitor: 3
      materialRequirements:
        Cable: 1
        PlasmaGlass: 10
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - AnomalyVesselCircuitboard

- type: entity
  parent: BaseMachineCircuitboard
  id: AnomalyVesselExperimentalCircuitboard
  name: "машинна плата експериментального аномального судна"
  description: "Машинна друкована плата для експериментального аномального судна."
  components:
  - type: Sprite
    state: science
  - type: MachineBoard
    prototype: MachineAnomalyVesselExperimental
    requirements:
      Capacitor: 3
    materialRequirements:
      Cable: 5
      PlasmaGlass: 15
      MetalRod: 4
  - type: ReverseEngineering # Nyano
    difficulty: 2
    recipes:
      - AnomalyVesselExperimentalCircuitboard

- type: entity
  parent: BaseMachineCircuitboard
  id: AnomalySynchronizerCircuitboard
  name: "плата машинного синхронізатора аномалії"
  description: "Машинна друкована плата для синхронізатора аномалій."
  components:
    - type: Sprite
      state: science
    - type: MachineBoard
      prototype: MachineAnomalySynchronizer
      requirements:
        Manipulator: 2
        Capacitor: 5
      materialRequirements:
        PlasmaGlass: 5
        Cable: 5
    - type: ReverseEngineering # Delta
      difficulty: 3
      recipes:
        - AnomalySynchronizerCircuitboard

- type: entity
  parent: BaseMachineCircuitboard
  id: APECircuitboard
  name: "Плата машини A.P.E"
  description: "Машинна друкована плата для A.P.E."
  components:
    - type: Sprite
      state: science
    - type: MachineBoard
      prototype: MachineAPE
      requirements:
        Capacitor: 2
      materialRequirements:
        Cable: 1
        Glass: 1
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - APECircuitboard

- type: entity
  id: ThermomachineFreezerMachineCircuitBoard
  parent: BaseMachineCircuitboard
  name: "машинна плата термомашини охолоджувача"
  description: "Схоже, що для зміни типу плати можна скористатися викруткою."
  components:
  - type: Sprite
    state: engineering
  - type: MachineBoard
    prototype: GasThermoMachineFreezer
    requirements:
      MatterBin: 2
      Capacitor: 2
    materialRequirements:
      Cable: 5
  - type: Construction
    deconstructionTarget: null
    graph: ThermomachineBoard
    node: freezer
  - type: ReverseEngineering # Nyano
    difficulty: 2
    recipes:
      - ThermomachineFreezerMachineCircuitBoard

- type: entity
  id: ThermomachineHeaterMachineCircuitBoard
  parent: BaseMachineCircuitboard
  name: "машинна плата термомашини нагрівача"
  description: "Схоже, що для зміни типу плати можна скористатися викруткою."
  components:
  - type: Sprite
    state: engineering
  - type: MachineBoard
    prototype: GasThermoMachineHeater
    requirements:
      MatterBin: 2
      Capacitor: 2
    materialRequirements:
      Cable: 5
  - type: Construction
    graph: ThermomachineBoard
    deconstructionTarget: null
    node: heater

- type: entity
  parent: BaseMachineCircuitboard
  id: HellfireFreezerMachineCircuitBoard
  name: "машинна плата термомашини пекельного охолоджувача"
  description: "Схоже, що для зміни типу плати можна скористатися викруткою."
  components:
  - type: Sprite
    state: engineering
  - type: MachineBoard
    prototype: GasThermoMachineHellfireFreezer
    requirements:
      MatterBin: 2
      Capacitor: 2
    materialRequirements:
      Plasma: 1
  - type: Construction
    deconstructionTarget: null
    graph: ThermomachineBoard
    node: hellfirefreezer
  - type: ReverseEngineering # Delta
    difficulty: 3
    recipes:
      - HellfireFreezerMachineCircuitBoard

- type: entity
  parent: BaseMachineCircuitboard
  id: HellfireHeaterMachineCircuitBoard
  name: "машинна плата термомашини пекельного нагрівача"
  description: "Схоже, що для зміни типу плати можна скористатися викруткою."
  components:
  - type: Sprite
    state: engineering
  - type: MachineBoard
    prototype: GasThermoMachineHellfireHeater
    requirements:
      MatterBin: 2
      Capacitor: 2
    materialRequirements:
      Plasma: 1
  - type: Construction
    graph: ThermomachineBoard
    deconstructionTarget: null
    node: hellfireheater

- type: entity
  id: CondenserMachineCircuitBoard
  parent: BaseMachineCircuitboard
  name: "машинна плата конденсаторної машини"
  description: "Машинна друкована плата для конденсатора."
  components:
  - type: Sprite
    state: engineering
  - type: MachineBoard
    prototype: BaseGasCondenser
    requirements:
      MatterBin: 1
    materialRequirements:
      Glass: 1

- type: entity
  id: PortableScrubberMachineCircuitBoard
  parent: BaseMachineCircuitboard
  name: "друкована плата портативного скрубера"
  description: "Друкована плата для портативного скрубера."
  components:
  - type: Sprite
    state: engineering
  - type: MachineBoard
    prototype: PortableScrubber
    requirements:
      MatterBin: 1
      Manipulator: 2
    materialRequirements:
      Cable: 5
      Glass: 2
  - type: ReverseEngineering # Nyano
    difficulty: 2
    recipes:

- type: entity
  id: SpaceHeaterMachineCircuitBoard
  parent: BaseMachineCircuitboard
  name: "машинна плата обігрівача"
  description: "Машинна друкована плата для обігрівача."
  components:
  - type: Sprite
    state: engineering
  - type: MachineBoard
    prototype: SpaceHeater
    requirements:
      MatterBin: 1
      Capacitor: 2
    materialRequirements:
      Cable: 5

- type: entity
  id: CloningPodMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата клонувального поду"
  description: "Машинна друкована плата для капсули для клонування."
  components:
    - type: Sprite
      state: medical
    - type: MachineBoard
      prototype: CloningPod
      requirements:
        MatterBin: 2
        Manipulator: 2
      materialRequirements:
        Glass: 1
        Cable: 1
    - type: ReverseEngineering # Nyano
      difficulty: 4
      recipes:
        - CloningPodMachineCircuitboard

- type: entity
  id: MetempsychoticMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "метемпсихотична машина машинна плата"
  description: "Машинна друкована плата для капсули для клонування"
  components:
    - type: Sprite
      state: medical
    - type: MachineBoard
      prototype: MetempsychoticMachine
      requirements:
        Capacitor: 2
        Manipulator: 2
      materialRequirements:
        Glass: 1
        Cable: 1
    - type: ReverseEngineering
      difficulty: 3
      recipes:
        - MetempsychoticMachineCircuitboard

- type: entity
  id: MedicalScannerMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата медичного с канеру"
  description: "Машинна друкована плата для медичного сканера."
  components:
    - type: Sprite
      state: medical
    - type: MachineBoard
      prototype: MedicalScanner
      requirements:
        Capacitor: 1
      materialRequirements:
        Glass: 5
        Cable: 1
    - type: ReverseEngineering # Nyano
      difficulty: 3
      recipes:
        - MedicalScannerMachineCircuitboard

- type: entity
  id: CrewMonitoringServerMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата серверу моніторингу екіпажу"
  description: "Машинна друкована плата для сервера моніторингу екіпажу."
  components:
    - type: MachineBoard
      prototype: CrewMonitoringServer
      materialRequirements:
        Steel: 1
        Cable: 2

- type: entity
  id: CryoPodMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата кріоподу"
  description: "Машинна друкована плата для кріоподу."
  components:
    - type: Sprite
      state: medical
    - type: MachineBoard
      prototype: CryoPod
      materialRequirements:
        Glass: 5
        Cable: 1
    - type: ReverseEngineering # Nyano
      difficulty: 3
      recipes:
        - CryoPodMachineCircuitboard

- type: entity
  id: ChemMasterMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "Машинна плата ChemMaster 4000"
  description: "Машинна друкована плата для ChemMaster 4000."
  components:
    - type: Sprite
      state: medical
    - type: MachineBoard
      prototype: ChemMaster
      requirements:
        Capacitor: 1
      materialRequirements:
        Glass: 1
        Cable: 1
      tagRequirements:
        GlassBeaker:
          Amount: 2
          DefaultPrototype: Beaker
          ExamineName: Glass Beaker

- type: entity
  id: ChemDispenserMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата для дозатора хімреагентів"
  description: "Машинна друкована плата для дозатора хімікатів."
  components:
    - type: Sprite
      state: medical
    - type: MachineBoard
      prototype: ChemDispenserEmpty
      requirements:
        Capacitor: 1
      materialRequirements:
        Glass: 1
        Steel: 3
      tagRequirements:
        GlassBeaker:
          Amount: 2
          DefaultPrototype: Beaker
          ExamineName: Glass Beaker

- type: entity
  id: BiomassReclaimerMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата рекуперації біомаси"
  description: "Друкована плата машини для рекуператора біомаси."
  components:
    - type: MachineBoard
      prototype: BiomassReclaimer
      requirements:
        MatterBin: 2
        Manipulator: 1
      tagRequirements:
        Knife:
          Amount: 2
          DefaultPrototype: KitchenKnife
          ExamineName: Knife
      materialRequirements:
        Steel: 5
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - BiomassReclaimerMachineCircuitboard

- type: entity
  id: HydroponicsTrayMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата лотку для гідропоніки"
  description: "Машинна друкована плата для гідропонного лотка."
  components:
    - type: Sprite
      state: service
    - type: MachineBoard
      prototype: HydroponicsTrayEmpty
      materialRequirements:
        # replacing the console screen
        Glass: 5
        Cable: 2
      tagRequirements:
        GlassBeaker:
          Amount: 2
          DefaultPrototype: Beaker
          ExamineName: Glass Beaker
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - HydroponicsTrayMachineCircuitboard

- type: entity
  id: SeedExtractorMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата екстрактору насіння"
  description: "Друкована плата для машинного екстрактора насіння."
  components:
    - type: Sprite
      state: service
    - type: MachineBoard
      prototype: SeedExtractor
      requirements:
        Manipulator: 2
        Capacitor: 1
      materialRequirements:
        # replacing the console screen
        Glass: 1
        Cable: 2
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - SeedExtractorMachineCircuitboard

- type: entity
  id: SMESMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата SMES"
  description: "Машинна друкована плата для надпровідного магнітного накопичувача енергії ."
  components:
    - type: Sprite
      sprite: Objects/Misc/module.rsi
      state: power_mod
    - type: MachineBoard
      prototype: SMESBasicEmpty
      requirements:
        Capacitor: 1
        PowerCell: 4
      materialRequirements:
        CableHV: 10

- type: entity
  id: SMESAdvancedMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата просунутого SMES"
  description: "Машинна плата для просунутого просунутого SMES"
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: power_mod
  - type: MachineBoard
    prototype: SMESAdvancedEmpty
    requirements:
      Capacitor: 2
      PowerCell: 4
    materialRequirements:
      CableHV: 20

- type: entity
  id: CellRechargerCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата заряднику батарейок"
  description: "Машинна друкована плата для зарядного пристрою."
  components:
    - type: Sprite
      sprite: Objects/Misc/module.rsi
      state: charger_APC
    - type: MachineBoard
      prototype: PowerCellRecharger
      requirements:
        Capacitor: 2
      materialRequirements:
        Cable: 5
    - type: PhysicalComposition
      materialComposition:
        Steel: 30
        Plastic: 30
    - type: StaticPrice
      price: 15

- type: entity
  id: PowerCageRechargerCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата заряднику кліток"
  description: "Машинна друкована плата для зарядного пристрою для енергетичної клітки."
  components:
    - type: Sprite
      sprite: Objects/Misc/module.rsi
      state: charger_APC
    - type: MachineBoard
      prototype: PowerCageRecharger
      requirements:
        Capacitor: 4
      materialRequirements:
        Steel: 5
        Cable: 10
    - type: PhysicalComposition
      materialComposition:
        Steel: 30
        Plastic: 30
    - type: StaticPrice
      price: 30

- type: entity
  id: BorgChargerCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата станції підзарядки кіборгів"
  description: "Машинна друкована плата для станції підзарядки робота."
  components:
    - type: Sprite
      sprite: Objects/Misc/module.rsi
      state: charger_APC
    - type: MachineBoard
      prototype: BorgCharger
      requirements:
        Capacitor: 2
      materialRequirements:
        Cable: 5
    - type: PhysicalComposition
      materialComposition:
        Steel: 30
        Plastic: 30
    - type: StaticPrice
      price: 15

- type: entity
  id: WeaponCapacitorRechargerCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата зарядної машини"
  description: "Машинна друкована плата для зарядного пристрою."
  components:
    - type: Sprite
      sprite: Objects/Misc/module.rsi
      state: charger_APC
    - type: MachineBoard
      prototype: WeaponCapacitorRecharger
      requirements:
        Capacitor: 2
      materialRequirements:
        CableMV: 5
    - type: PhysicalComposition
      materialComposition:
        Steel: 30
        Plastic: 30
    - type: StaticPrice
      price: 15

- type: entity
  id: TurboItemRechargerCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата турбозарядної машини"
  description: "Машинна друкована плата для турбопідзарядного пристрою."
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: charger_APC
  - type: MachineBoard
    prototype: TurboItemRecharger
    requirements:
      Capacitor: 2
    materialRequirements:
      CableMV: 5
  - type: PhysicalComposition
    materialComposition:
      Steel: 30
      Plastic: 30
  - type: ReverseEngineering # Delta
    difficulty: 2
    recipes:
      - TurboItemRechargerCircuitboard

- type: entity
  id: SubstationMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата підстанції"
  description: "Машинна друкована плата для підстанції."
  components:
    - type: MachineBoard
      prototype: SubstationBasicEmpty
      requirements:
        Capacitor: 1
        PowerCell: 1
      materialRequirements:
        CableMV: 5
        CableHV: 5
    - type: PhysicalComposition
      materialComposition:
        Glass: 200
      chemicalComposition:
        Silicon: 20
    - type: StaticPrice
      price: 58

- type: entity
  parent: BaseMachineCircuitboard
  id: DawInstrumentMachineCircuitboard
  name: "машинна плата цифрової аудіо робочої станції"
  components:
    - type: MachineBoard
      prototype: DawInstrument
      materialRequirements:
        Glass: 1
        Cable: 1
      tagRequirements:
#      One instrument to bring them all and in the darkness bind them...
       KeyedInstrument:
          Amount: 1
          DefaultPrototype: SynthesizerInstrument
          ExamineName: Keyed Instrument
       StringInstrument:
          Amount: 1
          DefaultPrototype: AcousticGuitarInstrument
          ExamineName: String Instrument
       PercussionInstrument:
          Amount: 1
          DefaultPrototype: GlockenspielInstrument
          ExamineName: Percussion Instrument
       BrassInstrument:
          Amount: 1
          DefaultPrototype: TrumpetInstrument
          ExamineName: Brass Instrument
       WoodwindInstrument:
          Amount: 1
          DefaultPrototype: SaxophoneInstrument
          ExamineName: Woodwind Instrument
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - DawInstrumentMachineCircuitboard

- type: entity
  id: PortableGeneratorPacmanMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "Машинна плата портативного генератора типу P.A.C.M.A.N"
  components:
    - type: Sprite
      state: engineering
    - type: MachineBoard
      prototype: PortableGeneratorPacman
      requirements:
        Capacitor: 1
      materialRequirements:
        CableHV: 5
    - type: PhysicalComposition
      materialComposition:
        Glass: 200
      chemicalComposition:
        Silicon: 20
    - type: StaticPrice
      price: 40
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - PortableGeneratorPacmanMachineCircuitboard

- type: entity
  id: ThrusterMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата рушійної машини"
  components:
  - type: MachineBoard
    prototype: Thruster
    requirements:
      Capacitor: 4
    materialRequirements:
      Steel: 5
  - type: ReverseEngineering # Delta
    difficulty: 3
    recipes:
      - ThrusterMachineCircuitboard

- type: entity
  id: GyroscopeMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата гіроскопічної машини"
  components:
  - type: MachineBoard
    prototype: Gyroscope
    requirements:
      Manipulator: 2
      Capacitor: 1
    materialRequirements:
      Glass: 2
  - type: ReverseEngineering # Delta
    difficulty: 3
    recipes:
      - GyroscopeMachineCircuitboard

- type: entity
  id: PortableGeneratorSuperPacmanMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата портативного генератора типу S.U.P.E.R.P.A.C.M.A.N"
  components:
    - type: Sprite
      state: engineering
    - type: MachineBoard
      prototype: PortableGeneratorSuperPacman
      requirements:
        Capacitor: 2
      materialRequirements:
        CableHV: 10
    - type: PhysicalComposition
      materialComposition:
        Glass: 200
      chemicalComposition:
        Silicon: 20
    - type: StaticPrice
      price: 40
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - PortableGeneratorSuperPacmanMachineCircuitboard

- type: entity
  id: PortableGeneratorJrPacmanMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата для портативного генератора типу J.R.P.A.C.M.A.N"
  components:
    - type: Sprite
      state: engineering
    - type: MachineBoard
      prototype: PortableGeneratorJrPacman
      requirements:
        Capacitor: 1
      materialRequirements:
        Cable: 10
    - type: PhysicalComposition
      materialComposition:
        Glass: 200
      chemicalComposition:
        Silicon: 20
    - type: StaticPrice
      price: 40
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - PortableGeneratorJrPacmanMachineCircuitboard

- type: entity
  id: ReagentGrinderMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата машини для подрібнення реагентів"
  description: "Машинна друкована плата для подрібнювача реактивів."
  components:
    - type: MachineBoard
      prototype: KitchenReagentGrinder
      requirements:
        MatterBin: 2
        Manipulator: 2
      tagRequirements:
        GlassBeaker:
          Amount: 1
          DefaultPrototype: Beaker
          ExamineName: Glass Beaker
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - ReagentGrinderMachineCircuitboard

- type: entity
  id: HotplateMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата конфорки машини"
  description: "Машинна друкована плата для електроплитки."
  components:
    - type: MachineBoard
      prototype: ChemistryHotplate
      requirements:
        Capacitor: 2
      materialRequirements:
        Glass: 1
    - type: ReverseEngineering # Delta
      difficulty: 2
      recipes:
        - HotplateMachineCircuitboard

- type: entity
  parent: BaseMachineCircuitboard
  id: ElectricGrillMachineCircuitboard
  name: "плата електричної гриль-машини"
  description: "Машинна друкована плата для електричного гриля."
  components:
  - type: MachineBoard
    prototype: KitchenElectricGrill
    requirements:
      Capacitor: 4
    materialRequirements:
      Glass: 2
      Cable: 5
  - type: ReverseEngineering # Delta
    difficulty: 2
    recipes:
      - ElectricGrillMachineCircuitboard

- type: entity
  id: StasisBedMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата стазисного ліжка"
  components:
    - type: Sprite
      state: medical
    - type: MachineBoard
      prototype: StasisBed
      requirements:
        Capacitor: 1
        Manipulator: 1
      materialRequirements:
        Cable: 3
        Steel: 2
    - type: ReverseEngineering # Nyano
      difficulty: 3
      recipes:
        - StasisBedMachineCircuitboard

- type: entity
  id: ElectrolysisUnitMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата електролізера"
  description: "Машинна друкована плата для електролізної установки."
  components:
  - type: Sprite
    state: medical
  - type: MachineBoard
    prototype: MachineElectrolysisUnit
    requirements:
      Capacitor: 2
    materialRequirements:
      Cable: 1

- type: entity
  id: CentrifugeMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата центрифуги"
  description: "Машинна друкована плата для центрифуги."
  components:
  - type: Sprite
    state: medical
  - type: MachineBoard
    prototype: MachineCentrifuge
    requirements:
      Manipulator: 1
    materialRequirements:
      Steel: 1

- type: entity
  id: MaterialReclaimerMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата реклеймера матеріалів"
  components:
    - type: Sprite
      state: supply
    - type: MachineBoard
      prototype: MaterialReclaimer
      requirements:
        Manipulator: 2
      materialRequirements:
        Steel: 5
        Plastic: 5
    - type: ReverseEngineering # Delta
      difficulty: 2
      recipes:
        - MaterialReclaimerMachineCircuitboard

- type: entity
  id: OreProcessorMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата рудозбагачувальної машини"
  components:
    - type: Sprite
      state: supply
    - type: MachineBoard
      prototype: OreProcessor
      requirements:
        MatterBin: 1
        Manipulator: 3
      materialRequirements:
        Glass: 1

- type: entity
  parent: BaseMachineCircuitboard
  id: OreProcessorIndustrialMachineCircuitboard
  name: "машинна плата промислової рудозбагачувальної машини"
  components:
  - type: Sprite
    state: supply
  - type: MachineBoard
    prototype: OreProcessorIndustrial
    requirements:
      MatterBin: 1
      Manipulator: 3
    materialRequirements:
      Glass: 1
  - type: ReverseEngineering # Delta
    difficulty: 2
    recipes:
      - OreProcessorIndustrialMachineCircuitboard

- type: entity
  id: SheetifierMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата sheet-meister 2000"
  components:
  - type: MachineBoard
    prototype: Sheetifier
    requirements:
      MatterBin: 1
      Manipulator: 1

- type: entity
  id: MicrowaveMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата мікрохвильовки"
  components:
    - type: Sprite
      state: service
    - type: MachineBoard
      prototype: KitchenMicrowave
      requirements:
        Capacitor: 1
      materialRequirements:
        Glass: 2
        Cable: 2
    - type: Tag
      tags:
      - MicrowaveMachineBoard

- type: entity
  id: SyndicateMicrowaveMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата мікрохвильової печі donk co"
  components:
    - type: Sprite
      state: service
    - type: MachineBoard
      prototype: SyndicateMicrowave
      requirements:
        Capacitor: 1
      materialRequirements:
        Glass: 2
        Cable: 2

- type: entity
  id: FatExtractorMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата екстрактора ліпідів"
  components:
    - type: Sprite
      state: service
    - type: MachineBoard
      prototype: FatExtractor
      requirements:
        Manipulator: 1
      componentRequirements:
        Utensil:
          Amount: 1
          DefaultPrototype: ForkPlastic
          ExamineName: Utensil
    - type: ReverseEngineering # Delta
      difficulty: 2
      recipes:
        - FatExtractorMachineCircuitboard

- type: entity
  parent: BaseMachineCircuitboard
  id: FlatpackerMachineCircuitboard
  name: "машинна плата Флатпекер 1001"
  components:
  - type: MachineBoard
    prototype: MachineFlatpacker
    requirements:
      Manipulator: 2
      MatterBin: 1
    materialRequirements:
      Steel: 1
  - type: ReverseEngineering # Delta
    difficulty: 2
    recipes:
      - FlatpackerMachineCircuitboard

- type: entity
  id: EmitterCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата емітера"
  components:
    - type: Sprite
      state: engineering
    - type: MachineBoard
      prototype: Emitter
      requirements:
        Capacitor: 2
      materialRequirements:
        CableHV: 5
        Glass: 2
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - EmitterCircuitboard

- type: entity
  id: SurveillanceCameraRouterCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата маршрутизатора камери спостереження"
  description: "Машинна друкована плата для маршрутизатора камер спостереження."
  components:
    - type: MachineBoard
      prototype: SurveillanceCameraRouterConstructed
      materialRequirements:
        Cable: 1
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - SurveillanceCameraRouterCircuitboard

- type: entity
  id: SurveillanceCameraWirelessRouterCircuitboard
  parent: BaseMachineCircuitboard
  name: "камера спостереження бездротова плата маршрутизатора"
  description: "Машинна друкована плата для бездротового маршрутизатора камери спостереження."
  components:
    - type: MachineBoard
      prototype: SurveillanceCameraWirelessRouterConstructed
      materialRequirements:
        Cable: 2
        Glass: 1
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - SurveillanceCameraWirelessRouterCircuitboard

- type: entity
  id: SurveillanceWirelessCameraMovableCircuitboard
  parent: BaseMachineCircuitboard
  name: "рухома плата бездротової камери"
  description: "Машинна друкована плата для рухомої бездротової камери."
  components:
    - type: MachineBoard
      prototype: SurveillanceWirelessCameraMovableConstructed
      materialRequirements:
        Glass: 2
        Cable: 2
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - SurveillanceWirelessCameraMovableCircuitboard

- type: entity
  id: SurveillanceWirelessCameraAnchoredCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата бездротової камери"
  description: "Машинна друкована плата для бездротової камери."
  components:
    - type: MachineBoard
      prototype: SurveillanceWirelessCameraAnchoredConstructed
      materialRequirements:
        Cable: 2
        Glass: 1
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - SurveillanceWirelessCameraAnchoredCircuitboard

- type: entity
  id: GasRecyclerMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата рециркуляції газу"
  description: "Друкована плата для утилізатора газу."
  components:
  - type: MachineBoard
    prototype: GasRecycler
    requirements:
      Capacitor: 1
      Manipulator: 1
    materialRequirements:
      Steel: 10
      Plasma: 5
  - type: ReverseEngineering # Nyano
    difficulty: 2
    recipes:
      - GasRecyclerMachineCircuitboard

- type: entity
  id: BoozeDispenserMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата машини для розливу напоїв"
  description: "Машинна друкована плата для диспенсера для напоїв."
  components:
    - type: Sprite
      state: service
    - type: MachineBoard
      prototype: BoozeDispenserEmpty
      materialRequirements:
        Steel: 5
      tagRequirements:
          GlassBeaker:
            Amount: 1
            DefaultPrototype: Beaker
            ExamineName: Glass Beaker

- type: entity
  id: CargoTelepadMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата вантажної телетайпної машини"
  description: "Машинна друкована плата для вантажного телепаду."
  components:
    - type: Sprite
      state: supply
    - type: MachineBoard
      prototype: CargoTelepad
      requirements:
        Capacitor: 2
      materialRequirements:
        Steel: 5
        Bluespace: 2  #DeltaV Bluespace Exists
    - type: ReverseEngineering # Delta
      difficulty: 3
      recipes:
        - CargoTelepadMachineCircuitboard

- type: entity
  id: SodaDispenserMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата дозатора содової машини"
  description: "Машинна друкована плата для дозатора газованої води."
  components:
    - type: Sprite
      state: service
    - type: MachineBoard
      prototype: SodaDispenserEmpty
      materialRequirements:
        Steel: 5
      tagRequirements:
          GlassBeaker:
            Amount: 1
            DefaultPrototype: Beaker
            ExamineName: Glass Beaker

- type: entity
  id: TelecomServerCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата телекомунікаційного сервера"
  description: "Машинна друкована плата для телекомунікаційного сервера."
  components:
    - type: MachineBoard
      prototype: TelecomServer
      materialRequirements:
        Steel: 1
        Cable: 2
    - type: ReverseEngineering # Nyano
      recipes:
        - TelecomServerCircuitboard

- type: entity
  parent: BaseMachineCircuitboard
  id: SalvageMagnetMachineCircuitboard
  name: "машинна плата магніту утилізаторів"
  description: "Машинна друкована плата для рятувального магніту."
  components:
  - type: MachineBoard
    prototype: SalvageMagnet
    requirements:
      Capacitor: 4
    materialRequirements:
      Steel: 5
      CableHV: 5
      Cable: 2
  - type: ReverseEngineering # Nyano
    recipes:
      - SalvageMagnetMachineCircuitboard

- type: entity
  parent: BaseMachineCircuitboard
  id: MiniGravityGeneratorCircuitboard
  name: "машинна плата міні-гравітаційного генератора"
  description: "Машинна друкована плата для міні-гравітаційного генератора."
  components:
  - type: MachineBoard
    prototype: GravityGeneratorMini
    requirements:
      Capacitor: 4
      MatterBin: 3
    materialRequirements:
      Steel: 5
      CableHV: 5
      Uranium: 2
  - type: ReverseEngineering # Delta
    difficulty: 3
    recipes:
      - MiniGravityGeneratorCircuitboard

- type: entity
  id: ShuttleGunSvalinnMachineGunCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата LSE-400c \"Свалінський кулемет"
  description: "Машинна друкована плата для кулемета LSE-400c \"Свалінн\""
  components:
  - type: Sprite
    state: security
  - type: MachineBoard
    prototype: ShuttleGunSvalinnMachineGun
    requirements:
      MatterBin: 2
      Manipulator: 4
    materialRequirements:
      Steel: 5
      CableHV: 5
  - type: ReverseEngineering # Delta
    difficulty: 3
    recipes:
      - ShuttleGunSvalinnMachineGunCircuitboard

- type: entity
  id: ShuttleGunPerforatorCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата LSE-1200c \"Перфоратор"
  description: "Друкована плата для верстата LSE-1200c \"Перфоратор\""
  components:
  - type: Sprite
    state: security
  - type: MachineBoard
    prototype: ShuttleGunPerforator
    requirements:
      MatterBin: 4
      Manipulator: 6
    materialRequirements:
      Steel: 10
      CableHV: 5
  - type: ReverseEngineering # Delta
    difficulty: 3
    recipes:
      - ShuttleGunPerforatorCircuitboard

- type: entity
  id: ShuttleGunFriendshipCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата EXP-320g \"Дружба"
  description: "Машинна друкована плата для ЕКСП-320г \"Дружба\""
  components:
  - type: Sprite
    state: security
  - type: MachineBoard
    prototype: ShuttleGunFriendship
    requirements:
      MatterBin: 3
      Manipulator: 2
    materialRequirements:
      Steel: 7
      CableHV: 5
  - type: ReverseEngineering # Delta
    difficulty: 3
    recipes:
      - ShuttleGunFriendshipCircuitboard

- type: entity
  id: ShuttleGunDusterCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата EXP-2100g \"Duster"
  description: "Друкована плата для машини EXP-2100g \"Duster\""
  components:
  - type: Sprite
    state: security
  - type: MachineBoard
    prototype: ShuttleGunDuster
    requirements:
      MatterBin: 6
      Manipulator: 4
    materialRequirements:
      Steel: 10
      CableHV: 5
      Uranium: 2
  - type: ReverseEngineering # Delta
    difficulty: 4
    recipes:
      -  ShuttleGunDusterCircuitboard

- type: entity
  id: ShuttleGunKineticCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата PTK-800 \"Дематеріалізатор матерії"
  description: "Машинна друкована плата для \"Дематеріалізатора матерії\" PTK-800"
  components:
  - type: Sprite
    state: security
  - type: MachineBoard
    prototype: ShuttleGunKinetic
    requirements:
      MatterBin: 2
      Manipulator: 3
    materialRequirements:
      Steel: 5
      CableHV: 2
  - type: ReverseEngineering # Delta
    difficulty: 2
    recipes:
      -  ShuttleGunKineticCircuitboard

- type: entity
  parent: BaseMachineCircuitboard
  id: StationAnchorCircuitboard
  name: "машинна плата станційного якоря"
  description: "Машинна друкована плата для станційного якоря."
  components:
  - type: MachineBoard
    prototype: StationAnchor
    requirements:
      Capacitor: 4
      MatterBin: 3
    materialRequirements:
      Steel: 10
      Glass: 5
      CableHV: 8
      Uranium: 2

- type: entity
  parent: BaseMachineCircuitboard
  id: ReagentGrinderIndustrialMachineCircuitboard
  name: "плата індустріального подрібнювача реагінтів"
  components:
  - type: MachineBoard
    prototype: ReagentGrinderIndustrial
    requirements:
      MatterBin: 1
      Manipulator: 3
    materialRequirements:
      Glass: 1
  - type: ReverseEngineering # Delta
    difficulty: 2
    recipes:
      -  ReagentGrinderIndustrialMachineCircuitboard

- type: entity
  parent: BaseMachineCircuitboard
  id: JukeboxCircuitBoard
  name: "плата музичного автомата"
  description: "Машинна друкована плата для музичного автомата."
  components:
  - type: MachineBoard
    prototype: Jukebox
    materialRequirements:
      WoodPlank: 5
      Steel: 2
      Glass: 5
      Cable: 2
  - type: ReverseEngineering # Delta
    difficulty: 2
    recipes:
      -  JukeboxCircuitBoard

- type: entity
  id: TraversalDistorterMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "траверсний дисторсійний щит верстата"
  description: "Машинна друкована плата для траверсного дисторсера."
  components:
    - type: Sprite
      state: science
    - type: MachineBoard
      prototype: MachineTraversalDistorter
      requirements:
        Manipulator: 1
        Capacitor: 2
      materialRequirements:
        Steel: 5
        Cable: 1

- type: entity
  id: MaterialSiloCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата матеріального силосу"
  description: "Друкована плата машини силосу для матеріалів."
  components:
    - type: Sprite
      state: supply
    - type: MachineBoard
      prototype: MaterialSilo
      requirements:
        MatterBin: 10
      materialRequirements:
        Steel: 5
        Gold: 5
        Bluespace: 5

- type: entity
  id: BookPressMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата книжкового преса"
  description: "Друкована плата для книжкового преса"
  components:
    - type: MachineBoard
      prototype: BookPress
      requirements:
        MatterBin: 1
        Manipulator: 2
