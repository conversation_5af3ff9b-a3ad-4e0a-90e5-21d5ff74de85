#Base
- type: entity
  id: AirlockAssembly
  name: "збірка шлюзу"
  description: "Він відкривається, закривається і, можливо, розчавить вас."
  components:
  - type: Clickable
  - type: InteractionOutline
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Standard/basic.rsi
    state: "assembly"
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.45"
        density: 110
        mask:
        - FullTileMask
        layer:
        - HumanoidBlockLayer
  - type: Anchorable
    delay: 2
  - type: Pullable
  - type: Transform
    anchored: true
    noRot: true
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: RCDDeconstructable
    cost: 6
    delay: 8
    fx: EffectRCDDeconstruct8
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Construction
    graph: Airlock
    node: assembly
  placement:
    mode: SnapgridCenter