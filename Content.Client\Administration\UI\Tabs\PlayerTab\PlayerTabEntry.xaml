﻿<PanelContainer xmlns="https://spacestation14.io"
                xmlns:customControls="clr-namespace:Content.Client.Administration.UI.CustomControls"
                Name="BackgroundColorPanel">
    <BoxContainer Orientation="Horizontal"
                  HorizontalExpand="True"
                  SeparationOverride="4">
        <Label Name="UsernameLabel"
               SizeFlagsStretchRatio="3"
               HorizontalExpand="True"
               ClipText="True"/>
        <customControls:VSeparator/>
        <Label Name="CharacterLabel"
               SizeFlagsStretchRatio="3"
               HorizontalExpand="True"
               ClipText="True"/>
        <customControls:VSeparator/>
        <Label Name="JobLabel"
               SizeFlagsStretchRatio="3"
               HorizontalExpand="True"
               ClipText="True"/>
        <customControls:VSeparator/>
        <Label Name="AntagonistLabel"
               SizeFlagsStretchRatio="2"
               HorizontalExpand="True"
               ClipText="True"/>
        <customControls:VSeparator/>
        <Label Name="RoleTypeLabel"
               SizeFlagsStretchRatio="2"
               HorizontalExpand="True"
               ClipText="True"/>
        <customControls:VSeparator/>
        <Label Name="OverallPlaytimeLabel"
               SizeFlagsStretchRatio="2"
               HorizontalExpand="True"
               ClipText="True"/>
    </BoxContainer>
</PanelContainer>
