- type: entity
  name: "кишенькова рація"
  description: "Зручна портативна рація."
  parent: BaseItem
  id: RadioHandheld
  components:
  - type: TelecomExempt
  - type: RadioMicrophone
    broadcastChannel: Handheld
    toggleOnInteract: false
    frequency: 1330
  - type: RadioSpeaker
    toggleOnInteract: false
    channels:
    - Handheld
  - type: Speech
    speechVerb: Robotic
  - type: Sprite
    sprite: Objects/Devices/communication.rsi
    layers:
    - state: walkietalkie
    - state: walkietalkie-on
    state: walkietalkie
  - type: Item
    sprite: Objects/Devices/communication.rsi
    heldPrefix: walkietalkie
  - type: Tag
    tags:
    - Radio
  - type: ActivatableUI
    key: enum.HandheldRadioUiKey.Key
  - type: UserInterface
    interfaces:
      enum.HandheldRadioUiKey.Key:
        type: HandheldRadioBoundUserInterface

- type: entity
  name: "радіостанція охорони"
  description: "Зручна охоронна рація."
  parent: RadioHandheld
  id: RadioHandheldSecurity
  components:
  - type: RadioMicrophone
    broadcastChannel: Security
  - type: RadioSpeaker
    channels:
    - Security
  - type: Sprite
    sprite: Objects/Devices/securityhandy.rsi
  - type: Item
    sprite: Objects/Devices/securityhandy.rsi
    heldPrefix: walkietalkie
