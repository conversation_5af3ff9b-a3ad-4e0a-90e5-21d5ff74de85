- type: constructionGraph
  id: Vim
  start: start
  graph:
  - node: start
    edges:
    - to: vim
      steps:
      - tag: VoiceTrigger
        name: a voice trigger
        icon:
          sprite: "Objects/Devices/voice.rsi"
          state: "voice"
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 1
      - component: PowerCell
        name: a power cell
        store: battery-container
        icon:
          sprite: Objects/Power/power_cells.rsi
          state: small
      - tool: Screwing
        doAfter: 1
  - node: vim
    actions:
    - !type:BuildMech
      mechPrototype: MechVim
