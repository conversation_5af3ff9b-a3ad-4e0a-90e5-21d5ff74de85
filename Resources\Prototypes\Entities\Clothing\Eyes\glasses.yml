- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesGlassesGar
  name: "гарні окуляри"
  description: "Вийдіть за межі неможливого і відкиньте розум на узбіччя!"
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/gar.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/gar.rsi
  - type: MeleeWeapon
    attackRate: 1.5
    damage:
      types:
        Blunt: 7

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesGlassesGarOrange
  name: "помаранчеві гарні окуляри "
  description: "За кого ти мене в біса маєш?!"
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/gar.rsi
    state: icon-alt
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/gar.rsi
    equippedPrefix: alt
  - type: Item
    heldPrefix: alt
  - type: MeleeWeapon
    damage:
      types:
        Blunt: 10

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesGlassesGarGiga
  name: "гігагарні окуляри"
  description: "Ми еволюціонуємо, минаючи ту людину, якою були хвилину тому. Потроху ми просуваємося вперед з кожним поворотом. Ось як працює дриль!"
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/gar.rsi
    state: icon-super
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/gar.rsi
    equippedPrefix: super
  - type: Item
    heldPrefix: super
  - type: MeleeWeapon
    damage:
      types:
        Blunt: 10

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesGlassesMeson
  name: "інженерні окуляри" #less confusion
  description: "Окуляри зеленого кольору з використанням запатентованого полімеру, що забезпечує захист від пошкоджень очей усіх типів."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/meson.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/meson.rsi
  - type: EyeProtection
  - type: Tag
    tags:
    - SecDogWearable # DeltaV - let Laika wear meson goggles

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesGlasses
  name: "окуляри"
  description: "Пара ефектних окулярів з лінзами за рецептом."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/glasses.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/glasses.rsi
  - type: VisionCorrection
  - type: Tag
    tags:
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesGlasses3D
  name: "3D окуляри"
  description: "подвійний армагеддон2"
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/3d.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/3d.rsi

- type: entity
  parent: ClothingEyesGlasses
  id: ClothingEyesGlassesHipster
  name: "хіпстерські окуляри"
  description: "Зроблено компанією Uncool. Co."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/hipster.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/hipster.rsi
  - type: Tag
    tags:
    - WhitelistChameleon

- type: entity
  parent: ClothingEyesGlasses
  id: ClothingEyesGlassesMonocle
  name: "монокль"
  description: "Такий ошатний окуляр!"
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/monocle.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/monocle.rsi
  - type: Tag
    tags:
    - WhitelistChameleon

- type: entity
  parent: ClothingEyesGlasses
  id: ClothingEyesGlassesPanto
  name: "окуляри для пантоміми"
  description: "Такий культовий. Такий типовий. Моноблочний стілець світу окулярів."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/panto.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/panto.rsi
  - type: Tag
    tags:
    - WhitelistChameleon

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesGlassesJensen
  name: "окуляри дженсен"
  description: "Пара складаних окулярів у жовтому тонованому корпусі. Ти їх ніколи не просив."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/jensen.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/jensen.rsi
  - type: Tag
    tags:
    - WhitelistChameleon

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesGlassesJamjar
  name: "склянки для джему"
  description: "Також відомі як захисники цноти."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/jamjar.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/jamjar.rsi
  - type: VisionCorrection
    visionBonus: -3.5 #a bit stronger
  - type: Tag
    tags:
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesGlassesOutlawGlasses
  name: "окуляри беззаконника"
  description: "Обов'язкова річ для кожного агента під прикриттям, який себе поважає."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/outlawglasses.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/outlawglasses.rsi
  - type: VisionCorrection
  - type: IdentityBlocker

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesGlassesCheapSunglasses
  name: "сонцезахисні окуляри"
  description: "Пара чорних сонцезахисних окулярів."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/sunglasses.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/sunglasses.rsi
  - type: Tag
    tags:
    - HamsterWearable
    - WhitelistChameleon
    - SecDogWearable # DeltaV - let Laika wear sunglasses
  - type: IdentityBlocker
    coverage: EYES

- type: entity
  parent: ClothingEyesGlassesCheapSunglasses
  id: ClothingEyesGlassesSunglasses
  name: "сонцезахисні окуляри з захистом"
  description: "Пара чорних сонцезахисних окулярів. Ці окуляри спеціально створені для захисту очей від сліпучих спалахів світла."
  components:
  - type: FlashImmunity
  - type: EyeProtection
    protectionTime: 5
  - type: Tag
    tags:
    - Sunglasses

- type: entity
  parent: [ClothingEyesBase, ShowSecurityIcons, BallisticGlasses]
  id: ClothingEyesGlassesSecurity
  name: "окуляри охорони"
  description: "Оновлені сонцезахисні окуляри з захистом від спалахів та захисним екраном HUD."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/secglasses.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/secglasses.rsi
  - type: FlashImmunity
  - type: EyeProtection
    protectionTime: 5
  - type: Tag
    tags:
    - HamsterWearable
    - WhitelistChameleon
    - SecDogWearable # DeltaV - let Laika wear secglasses
  - type: GuideHelp
    guides:
    - Security
  - type: IdentityBlocker
    coverage: EYES

- type: entity
  parent: [ClothingEyesBase, ShowPsionicsIcons]
  id: ClothingEyesGlassesEpistemics
  name: "окуляри епі-псіоніки"
  description: "Оновлені сонцезахисні окуляри, що забезпечують захист від спалахів та епістемологічний псіонічний HUD."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/epiglasses.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/epiglasses.rsi
  - type: FlashImmunity
  - type: EyeProtection
    protectionTime: 5
  - type: Tag
    tags:
    - WhitelistChameleon
  - type: IdentityBlocker
    coverage: EYES

- type: entity
  parent: ClothingEyesGlassesCheapSunglasses
  id: ClothingEyesGlassesCheapSunglassesAviator
  name: "авіатори"
  description: "Пара дизайнерських сонцезахисних окулярів."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/aviators.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/aviators.rsi
  - type: Tag
    tags:
    - WhitelistChameleon

- type: entity
  parent: ClothingEyesGlassesSunglasses
  id: ClothingEyesGlassesSunglassesAviator
  name: "захисні авіатори"
  description: "Пара дизайнерських сонцезахисних окулярів. Ці окуляри спеціально створені для захисту очей від сліпучих спалахів світла."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/aviators.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/aviators.rsi
  - type: Tag
    tags:
    - WhitelistChameleon

- type: entity
  parent: ClothingEyesGlassesSecurity
  id: ClothingEyesGlassesSecurityAviator
  name: "авіація безпеки"
  description: "Модернізовані авіатори, які забезпечують захист від спалахів і захисний HUD."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/secglasses_aviators.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/secglasses_aviators.rsi
  - type: Tag
    tags:
    - WhitelistChameleon

- type: entity
  parent: ClothingEyesGlassesCheapSunglasses
  id: ClothingEyesGlassesCheapSunglassesBig
  name: "великі сонцезахисні окуляри"
  description: "Пара чорних сонцезахисних окулярів з великими лінзами."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/sunglasses_big.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/sunglasses_big.rsi
  - type: Tag
    tags:
    - WhitelistChameleon

- type: entity
  parent: ClothingEyesGlassesSunglasses
  id: ClothingEyesGlassesSunglassesBig
  name: "великі захисні сонцезахисні окуляри"
  description: "Пара чорних сонцезахисних окулярів з великими лінзами. Ці окуляри спеціально створені для захисту очей від сліпучих спалахів світла."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/sunglasses_big.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/sunglasses_big.rsi
  - type: Tag
    tags:
    - WhitelistChameleon

- type: entity
  parent: ClothingEyesGlassesSecurity
  id: ClothingEyesGlassesSecurityBig
  name: "великі захисні окуляри"
  description: "Оновлені великі сонцезахисні окуляри, які забезпечують захист від спалахів та захисний HUD."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/secglasses_big.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/secglasses_big.rsi
  - type: Tag
    tags:
    - WhitelistChameleon

- type: entity
  parent: ClothingEyesGlassesCheapSunglasses
  id: ClothingEyesGlassesCheapSunglassesVisor
  name: "окуляри-візор"
  description: "Пара окулярів-візорів."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/sunglasses_visor.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/sunglasses_visor.rsi
  - type: Tag
    tags:
    - WhitelistChameleon

- type: entity
  parent: ClothingEyesGlassesSunglasses
  id: ClothingEyesGlassesSunglassesVisor
  name: "захисні окуляри-візор"
  description: "Пара окулярів-візорів. Ці окуляри спеціально створені для захисту очей від сліпучих спалахів світла."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/sunglasses_visor.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/sunglasses_visor.rsi
  - type: Tag
    tags:
    - WhitelistChameleon

- type: entity
  parent: ClothingEyesGlassesSecurity
  id: ClothingEyesGlassesSecurityVisor
  name: "захисні окуляри з козирком"
  description: "Оновлені сонцезахисні окуляри з козирком, що забезпечує захист від спалахів та захисний HUD."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/secglasses_visor.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/secglasses_visor.rsi
  - type: Tag
    tags:
    - WhitelistChameleon

- type: entity
  parent: [ClothingEyesBase, BallisticGlasses]
  id: ClothingEyesGlassesMercenary
  name: "окуляри найманця"
  description: "Окуляри для бою, для захисту очей від яскравих сліпучих спалахів."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/mercglasses.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/mercglasses.rsi
  - type: FlashImmunity
  - type: EyeProtection
    protectionTime: 5

#Make a scanner category when these actually function and we get the trayson
- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesGlassesThermal
  name: "оптичний тепловізор"
  description: "Тепловізор у вигляді окулярів"
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/thermal.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/thermal.rsi
  - type: Armor
    modifiers:
      coefficients:
        Heat: 0.95
  - type: GroupExamine

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesGlassesChemical
  name: "окуляри хімічного аналізу"
  description: "Окуляри, які можуть сканувати хімічний склад розчину."
  components:
    - type: Sprite
      sprite: Clothing/Eyes/Glasses/science.rsi
    - type: Clothing
      sprite: Clothing/Eyes/Glasses/science.rsi
    - type: SolutionScanner

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesVisorNinja
  name: "окуляри ніндзя"
  description: "Вдосконалені окуляри, що захищають очі ніндзя від миготливих вогнів."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/ninjavisor.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/ninjavisor.rsi
  - type: FlashImmunity
  - type: NightVision
    isEquipment: true
  - type: Unremoveable

- type: entity #Fake goggles, the latest in anti-valid hunting technology
  parent: ClothingEyesBase
  id: ClothingEyesGlassesThermalBudget
  name: "червоні окуляри"
  description: "Вони не виконують жодної реальної функції, але виглядають круто!"
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/thermal.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/thermal.rsi

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesGlassesChemicalBudget
  name: "фіолетові окуляри"
  description: "Вони не виконують жодної реальної функції, але виглядають круто!"
  components:
    - type: Sprite
      sprite: Clothing/Eyes/Glasses/science.rsi
    - type: Clothing
      sprite: Clothing/Eyes/Glasses/science.rsi

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesGlassesMesonBudget
  name: "зелені окуляри"
  description: "Вони не виконують жодної реальної функції, але виглядають круто!"
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/meson.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/meson.rsi

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesGlassesEthereal
  name: "ефірні окуляри"
  description: "Незвичайна пара окулярів, розроблена під час нелюдських експериментів за участю Тінькіна. Вони призначені для того, щоб дозволити користувачеві вдивлятися в темряву."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Glasses/etherealgoogles.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Glasses/etherealgoogles.rsi
  - type: ShowEthereal
