- type: entity
  abstract: true
  id: ArcadeBase
  description: "Шафа для ігрових автоматів."
  name: "аркада"
  parent: BaseComputer
  components:
  - type: ApcPowerReceiver
    powerLoad: 350
  - type: ExtensionCableReceiver
  - type: PointLight
    radius: 1.8
    energy: 1.6
    color: "#3db83b"
  - type: LitOnPowered
  - type: Sprite
    sprite: Structures/Machines/arcade.rsi
    layers:
    - map: ["computerLayerBody"]
      state: arcade
    - map: ["computerLayerScreen"]
      state: screen_invaders
    - map: ["enum.WiresVisualLayers.MaintenancePanel"]
      state: panel
      visible: false
  - type: Icon
    sprite: Structures/Machines/arcade.rsi
    state: arcade
  - type: WiresPanel
  - type: Wires
    layoutId: Arcade
    boardName: wires-board-name-arcade
  - type: WiresVisuals
  - type: TypingIndicator
    proto: robot
  - type: Speech
    speechVerb: Robotic
    speechSounds: Vending
  - type: Anchorable
  - type: Pullable
  - type: StaticPrice
    price: 300
  - type: SpamEmitSoundRequirePower
  - type: SpamEmitSound
    minInterval: 30
    maxInterval: 90
    sound:
      collection: ArcadeNoise
      params:
        volume: -8
        maxDistance: 10
        variation: 0.05

- type: entity
  id: SpaceVillainArcade
  name: "космічні Лиходії"
  parent: ArcadeBase
  components:
  - type: Sprite
    sprite: Structures/Machines/arcade.rsi
    layers:
    - map: ["computerLayerBody"]
      state: arcade
    - map: ["computerLayerScreen"]
      state: screen_spacevillain
    - map: ["enum.WiresVisualLayers.MaintenancePanel"]
      state: panel
      visible: false
  - type: PointLight
    color: "#e3a136"
  - type: SpaceVillainArcade
    rewardMinAmount: 0
    rewardMaxAmount: 0
    possibleRewards:
    - PrizeBall
    - PrizeTicket10
    - PrizeTicket10
    - PrizeTicket10
    - PrizeTicket10
    - PrizeTicket10
    - PrizeTicket30
    - PrizeTicket30
    - PrizeTicket60
  - type: WiresPanel
  - type: Wires
    layoutId: Arcade
    boardName: wires-board-name-arcade
  - type: ActivatableUI
    key: enum.SpaceVillainArcadeUiKey.Key
  - type: ActivatableUIRequiresPower
  - type: StationAiWhitelist
  - type: UserInterface
    interfaces:
      enum.SpaceVillainArcadeUiKey.Key:
        type: SpaceVillainArcadeBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: Computer
    board: SpaceVillainArcadeComputerCircuitboard
  - type: Advertise
    pack: SpaceVillainAds
    minimumWait: 60 # Arcades are noisy
    maximumWait: 240
  - type: SpeakOnUIClosed
    pack: SpaceVillainGoodbyes

- type: entity
  id: SpaceVillainArcadeFilled
  parent: SpaceVillainArcade
  suffix: Filled
  components:
  - type: SpaceVillainArcade
    rewardMinAmount: 5
    rewardMaxAmount: 8

- type: entity
  id: BlockGameArcade
  description: "Аркадна шафа з дивно знайомою грою."
  name: "НТетріс"
  parent: ArcadeBase
  components:
  - type: Sprite
    sprite: Structures/Machines/arcade.rsi
    layers:
    - map: ["computerLayerBody"]
      state: arcade
    - map: ["computerLayerScreen"]
      state: screen_blockgame
    - map: ["enum.WiresVisualLayers.MaintenancePanel"]
      state: panel
      visible: false
  - type: BlockGameArcade
  - type: ActivatableUI
    key: enum.BlockGameUiKey.Key
  - type: ActivatableUIRequiresPower
  - type: StationAiWhitelist
  - type: UserInterface
    interfaces:
      enum.BlockGameUiKey.Key:
        type: BlockGameBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: Computer
    board: BlockGameArcadeComputerCircuitboard
  - type: Advertise
    pack: BlockGameAds
    minimumWait: 60 # Arcades are noisy
    maximumWait: 240
  - type: SpeakOnUIClosed
    pack: BlockGameGoodbyes
