- type: entity
  id: BasePlasticExplosive
  parent: BaseItem
  abstract: true
  components:
  - type: Item
    size: Small
  - type: Sticky
    stickDelay: 5
    unstickDelay: 5
    stickPopupStart: comp-sticky-start-stick-bomb
    stickPopupSuccess: comp-sticky-success-stick-bomb
    unstickPopupStart: comp-sticky-start-unstick-bomb
    unstickPopupSuccess: comp-sticky-success-unstick-bomb
    blacklist: # can't stick it to other items
      components:
      - Item
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:ExplodeBehavior
  - type: StickyVisualizer
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.Trigger.TriggerVisuals.VisualState:
        base:
          Primed: { state: primed }
          Unprimed: { state: complete }

- type: entity
  name: "композиція C-4"
  description: "Використовується для того, щоб продірявити певні ділянки без зайвих отворів. Улюбленець диверсантів."
  parent: BasePlasticExplosive
  id: C4
  components:
  - type: Sprite
    sprite: Objects/Weapons/Bombs/c4.rsi
    state: icon
    layers:
    - state: icon
      map: ["base"]
  - type: OnUseTimerTrigger
    delay: 10
    delayOptions: [10, 30, 60, 120, 300]
    initialBeepDelay: 0
    beepSound: /Audio/Machines/Nuke/general_beep.ogg
    startOnStick: true
    canToggleStartOnStick: true
  - type: TriggerOnSignal
  - type: DeviceLinkSink
    ports:
      - Trigger
  - type: Explosive # Powerful explosion in a very small radius. Doesn't break underplating.
    explosionType: DemolitionCharge
    totalIntensity: 60
    intensitySlope: 5
    maxIntensity: 30
    canCreateVacuum: false
  - type: ExplodeOnTrigger
  - type: HolidayVisuals
    holidays:
      festive:
      - FestiveSeason
  - type: HolidayRsiSwap
    sprite:
      festive: Objects/Weapons/Bombs/c4gift.rsi

- type: entity
  name: "сейсмічний заряд"
  description: "Фугасна вибухівка призначена для руйнування великих об'ємів гірських порід."
  parent: BasePlasticExplosive
  id: SeismicCharge
  components:
  - type: Sprite
    sprite: Objects/Weapons/Bombs/seismic.rsi
    state: icon
    layers:
    - state: icon
      map: ["base"]
  - type: OnUseTimerTrigger
    delay: 5
    delayOptions: [5, 10, 15, 20]
    initialBeepDelay: 0
    beepSound:
      path: /Audio/Effects/Cargo/buzz_two.ogg
      params:
        volume: -6
    startOnStick: false
    canToggleStartOnStick: true
  - type: TriggerOnSignal
  - type: DeviceLinkSink
    ports:
    - Trigger
  - type: Explosive
    explosionType: Cryo
    totalIntensity: 120
    intensitySlope: 2
    maxIntensity: 30
    canCreateVacuum: false
  - type: ExplodeOnTrigger

- type: entity
  name: "розривний заряд"
  description: "Вибухівка для прориву стін, що дозволяє співробітникам служби безпеки пробивати стіни."
  parent: SeismicCharge
  id: BreachingCharge
  components:
  - type: Sprite
    sprite: Objects/Weapons/Bombs/breaching.rsi
    state: icon
    layers:
    - state: icon
      map: ["base"]
  - type: OnUseTimerTrigger
    delay: 10
    delayOptions: [10, 15, 20, 25]
    initialBeepDelay: 0
    beepSound:
      path: /Audio/Effects/Cargo/buzz_two.ogg
      params:
        volume: -6
    startOnStick: true
    canToggleStartOnStick: true
  - type: Explosive
    explosionType: DemolitionCharge
    totalIntensity: 10
    intensitySlope: 10
    maxIntensity: 10
    canCreateVacuum: false
