using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Input;

namespace Content.Client.Administration.UI.Tabs.ObjectsTab
{
    [GenerateTypedNameReferences]
    public sealed partial class ObjectsTabHeader : Control
    {
        public event Action<Header>? OnHeaderClicked;

        private const string ArrowUp = "↑";
        private const string ArrowDown = "↓";

        public ObjectsTabHeader()
        {
            RobustXamlLoader.Load(this);

            ObjectNameLabel.OnKeyBindDown += ObjectNameClicked;
            EntityIDLabel.OnKeyBindDown += EntityIDClicked;
        }

        public Label GetHeader(Header header)
        {
            return header switch
            {
                Header.ObjectName => ObjectNameLabel,
                Header.EntityID => EntityIDLabel,
                _ => throw new ArgumentOutOfRangeException(nameof(header), header, null)
            };
        }

        public void ResetHeaderText()
        {
            ObjectNameLabel.Text = Loc.GetString("object-tab-object-name");
            EntityIDLabel.Text = Loc.GetString("object-tab-entity-id");
        }

        public void UpdateHeaderSymbols(Header headerClicked, bool ascending)
        {
            ResetHeaderText();
            var arrow = ascending ? ArrowUp : ArrowDown;
            GetHeader(headerClicked).Text += $" {arrow}";
        }

        private void HeaderClicked(GUIBoundKeyEventArgs args, Header header)
        {
            if (args.Function != EngineKeyFunctions.UIClick)
            {
                return;
            }

            OnHeaderClicked?.Invoke(header);
            args.Handle();
        }

        private void ObjectNameClicked(GUIBoundKeyEventArgs args)
        {
            HeaderClicked(args, Header.ObjectName);
        }

        private void EntityIDClicked(GUIBoundKeyEventArgs args)
        {
            HeaderClicked(args, Header.EntityID);
        }

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);

            if (disposing)
            {
                ObjectNameLabel.OnKeyBindDown -= ObjectNameClicked;
                EntityIDLabel.OnKeyBindDown -= EntityIDClicked;
            }
        }

        public enum Header
        {
            ObjectName,
            EntityID
        }
    }
}
