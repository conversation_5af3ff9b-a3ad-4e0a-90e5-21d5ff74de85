- type: entity
  parent: PlushieMoth
  id: PlushieMothRandom
  name: "плюшевий метелик"
  suffix: Random
  description: "Мила маленька людина-метелик, яку можна потримати на долоні."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Objects/Fun/Toys/moff.rsi
    layers:
      - state: moff1
        map: [ "enum.DamageStateVisualLayers.Base" ]
  - type: RandomSprite
    available:
      - enum.DamageStateVisualLayers.Base:
          moff1: ""
      - enum.DamageStateVisualLayers.Base:
          moff2: ""
      - enum.DamageStateVisualLayers.Base:
          moff3: ""
      - enum.DamageStateVisualLayers.Base:
          moff4: ""
      - enum.DamageStateVisualLayers.Base:
          moff5: ""
      - enum.DamageStateVisualLayers.Base:
          moff6: ""
      - enum.DamageStateVisualLayers.Base:
          moff7: ""
      - enum.DamageStateVisualLayers.Base:
          moff8: ""
      - enum.DamageStateVisualLayers.Base:
          moff9: ""
      - enum.DamageStateVisualLayers.Base:
          moff10: ""
      - enum.DamageStateVisualLayers.Base:
          moff11: ""
      - enum.DamageStateVisualLayers.Base:
          moff12: ""
      - enum.DamageStateVisualLayers.Base:
          moff13: ""
      - enum.DamageStateVisualLayers.Base:
          moff14: ""

- type: entity
  parent: PlushieMoth
  id: PlushieMothMusician
  name: "плюшевий метелик-музикант"
  description: "Плюшева іграшка із зображенням чарівної людини-метелика з крихітним синтезатором і крихітними окулярами."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Objects/Fun/Toys/moff.rsi
    state: moffmusician

- type: entity
  parent: PlushieMoth
  id: PlushieMothBartender
  name: "плюшевий метелик-бармер"
  description: "Плюшева іграшка із зображенням чарівної людини-метелика з крихітним тофатом і кевларовою жилеткою. Пахне цигарками."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Objects/Fun/Toys/moff.rsi
    state: moffbartender
