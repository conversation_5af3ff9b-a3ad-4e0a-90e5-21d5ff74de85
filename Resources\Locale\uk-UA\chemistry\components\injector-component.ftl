## UI

injector-draw-text = Набрати
injector-inject-text = Ввести
injector-invalid-injector-toggle-mode = Неправильний режим
injector-volume-label = Кількість: [color=white]{$currentVolume}/{$totalVolume}[/color]
    Режим: [color=white]{$modeString}[/color] ([color=white]{$transferVolume}u[/color])

## Entity

injector-component-drawing-text = Набирання речовини
injector-component-injecting-text = Введення речовини
injector-component-cannot-transfer-message = Ви не можете перемістити речовину в {$target}!
injector-component-cannot-draw-message = Ви не можете набрати речовину з {$target}!
injector-component-cannot-inject-message = Ви не можете ввести речовину в {$target}!
injector-component-inject-success-message = Ви ввели {$amount}u в {$target}!
injector-component-transfer-success-message = Ви перемістили {$amount}u в {$target}.
injector-component-draw-success-message = Ви набрали {$amount}u з {$target}.
injector-component-target-already-full-message = {$target} вже повний!
injector-component-target-is-empty-message = {$target} пустий!
injector-component-cannot-toggle-draw-message = Занадто повний, щоб набрати!
injector-component-cannot-toggle-inject-message = Нічого вводити!

## mob-inject doafter повідомлення

injector-component-drawing-user = Починаєш набирати голкою.
injector-component-injecting-user = Ви почали вводити голкою.
injector-component-drawing-target = {CAPITALIZE(THE($user))} намагається голкою набрати кров вашого тіла!
injector-component-injecting-target = {CAPITALIZE($user)} намагається встромити в вас голку!
injector-component-deny-chitinid = Екзоскелет {CAPITALIZE(THE($target))} занадто товстий, щоб голка могла його пробити.
injector-spent-text = Витрачено
onetime-injector-simple-volume-label = Обсяг: [color=white]{$currentVolume}[/color] Режим: [color=white]{$modeString}[/color]
injector-component-injecting-locked-text = Блокування для ін'єкцій