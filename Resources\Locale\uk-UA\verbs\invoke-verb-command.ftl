﻿### Localization used for the invoke verb command.
# Mostly help + error messages.

invoke-verb-command-description = Викликає дієслово з заданим іменем на сутності, з сутністю гравець
invoke-verb-command-help = invokeverb <playerUid | "self"> <targetUid> <verbName | "interaction" | "activation" | "alternative">

invoke-verb-command-invalid-args = invokeverb отримує 2 аргументи.

invoke-verb-command-invalid-player-uid = Не вдалося розібрати uid гравця, або не було передано "self".
invoke-verb-command-invalid-target-uid = Не вдалося розібрати цільовий uid.

invoke-verb-command-invalid-player-entity = Введений uid гравця не відповідає дійсній сутності.
invoke-verb-command-invalid-target-entity = Цільовий uid не відповідає дійсному об'єкту.

invoke-verb-command-success = Викликав дієслово '{ $verb }' на { $target } з користувачем { $player}.

invoke-verb-command-verb-not-found = Не вдалося знайти дієслово { $verb } у { $target }.
