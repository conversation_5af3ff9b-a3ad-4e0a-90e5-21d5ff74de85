- type: entity
  name: "космічний гриб"
  parent: BaseStructure
  id: Spaceshroom
  suffix: Structure
  description: "Скупчення дикорослих грибів, які люблять рости в темному, вологому середовищі."
  components:
    - type: Sprite
      sprite: Objects/Misc/spaceshroom.rsi
      state: structure
    - type: Transform
      anchored: true
    - type: Physics
      bodyType: Static
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeCircle
            radius: 0.2
    - type: InteractionOutline
    - type: Gatherable
      toolWhitelist:
        components:
          - Hands
      loot:
        All: SpaceshroomGather
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: Wood
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 150
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]

- type: entityLootTable
  id: SpaceshroomGather
  entries:
  - id: FoodSpaceshroom
    amount: 1
    maxAmount: 1

- type: entity
  name: "космічний гриб"
  parent: FoodProduceBase
  id: FoodSpaceshroom
  description: "Дикий гриб. Невідомо, який ефект він може мати..."
  components:
  - type: Produce
  - type: Sprite
    sprite: Objects/Misc/spaceshroom.rsi
    state: object
  - type: FlavorProfile
    flavors:
      - spaceshroom
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: SpaceDrugs
        Quantity: 10
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
        - ReagentId: Vitamin
          Quantity: 2
  - type: RandomFillSolution
    solution: food
    weightedRandomId: RandomFillSpaceshroom
  - type: StaticPrice
    price: 20

- type: weightedRandomFillSolution
  id: RandomFillSpaceshroom
  fills:
  - quantity: 10
    weight: 10
    reagents:
    - SpaceDrugs
  - quantity: 0
    weight: 5
    reagents:
    - Water
  - quantity: 10
    weight: 3
    reagents:
    - Ephedrine
  - quantity: 10
    weight: 1
    reagents:
    - Lexorin
  - quantity: 15
    weight: 1
    reagents:
    - Amatoxin

# Cooked Object
- type: entity
  name: "варений космічний гриб"
  parent: FoodProduceBase
  id: FoodSpaceshroomCooked
  description: "Лісовий гриб, який прожарили наскрізь. Здається, що тепло зняло його хімічну дію."
  components:
  - type: FlavorProfile
    flavors:
      - spaceshroomcooked
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Vitamin
          Quantity: 5
  - type: Sprite
    sprite: Objects/Misc/spaceshroom.rsi
    state: spaceshroom_cooked
  - type: Produce
  - type: StaticPrice
    price: 40
