﻿- type: entity
  parent: BaseItem
  id: NotekeeperCartridge
  name: "картридж для нотатника"
  description: "Програма для ведення нотаток"
  components:
  - type: Sprite
    sprite: Objects/Devices/cartridge.rsi
    state: cart-y
  - type: UIFragment
    ui: !type:NotekeeperUi
  - type: Cartridge
    programName: notekeeper-program-name
    icon:
      sprite:  Objects/Misc/books.rsi
      state: book_icon
  - type: NotekeeperCartridge

- type: entity
  parent: BaseItem
  id: NewsReaderCartridge
  name: "новинний картридж"
  description: "Програма для читання новин"
  components:
  - type: Sprite
    sprite: Objects/Devices/cartridge.rsi
    state: cart-y
  - type: UIFragment
    ui: !type:NewsReaderUi
  - type: Cartridge
    programName: news-read-program-name
    icon:
      sprite: Interface/Misc/program_icons.rsi
      state: news_read
  - type: NewsReaderCartridge

- type: entity
  parent: BaseItem
  id: CrewManifestCartridge
  name: "касета з екіпажним маніфестом"
  description: "Програма для створення списку ваших колег по екіпажу"
  components:
    - type: Sprite
      sprite: Objects/Devices/cartridge.rsi
      state: cart-y
    - type: UIFragment
      ui: !type:CrewManifestUi
    - type: Cartridge
      programName: crew-manifest-program-name
      icon:
        sprite: Interface/Misc/program_icons.rsi
        state: crew_manifest
    - type: CrewManifestCartridge

- type: entity
  parent: BaseItem
  id: NetProbeCartridge
  name: "Картридж NetProbe"
  description: "Програма для отримання адреси та частоти мережевих пристроїв"
  components:
    - type: Sprite
      sprite: Objects/Devices/cartridge.rsi
      state: cart-y
    - type: UIFragment
      ui: !type:NetProbeUi
    - type: Cartridge
      programName: net-probe-program-name
      icon:
        sprite: Structures/Machines/server.rsi
        state: server
    - type: NetProbeCartridge

- type: entity
  parent: BaseItem
  id: LogProbeCartridge
  name: "Картридж LogProbe"
  description: "Програма для отримання логів доступу з пристроїв"
  components:
    - type: Sprite
      sprite: Objects/Devices/cartridge.rsi
      state: cart-log
    - type: Icon
      sprite: Objects/Devices/cartridge.rsi
      state: cart-log
    - type: UIFragment
      ui: !type:LogProbeUi
    - type: Cartridge
      programName: log-probe-program-name
      icon:
        sprite: Structures/Doors/Airlocks/Standard/security.rsi
        state: closed
    - type: LogProbeCartridge
    - type: GuideHelp
      guides:
      - Forensics

- type: entity
  parent: BaseItem
  id: MedTekCartridge
  name: "картридж MedTek"
  description: "Програма, яка надає медичні діагностичні інструменти."
  components:
    - type: Sprite
      sprite: Objects/Devices/cartridge.rsi
      state: cart-med
    - type: Icon
      sprite: Objects/Devices/cartridge.rsi
      state: cart-med
    - type: Cartridge
      programName: med-tek-program-name
      icon:
        sprite: Objects/Specific/Medical/healthanalyzer.rsi
        state: icon
    - type: MedTekCartridge

- type: entity
  parent: BaseItem
  id: AstroNavCartridge
  name: "картридж AstroNav"
  description: "Програма для навігації, яка надає GPS-координати."
  components:
    - type: Sprite
      sprite: Objects/Devices/cartridge.rsi
      state: cart-nav
    - type: Icon
      sprite: Objects/Devices/cartridge.rsi
      state: cart-nav
    - type: Cartridge
      programName: astro-nav-program-name
      icon:
        sprite: Objects/Devices/gps.rsi
        state: icon
    - type: AstroNavCartridge


- type: entity
  parent: BaseItem
  id: PsiWatchCartridge
  name: "картридж псі-годинника"
  description: "Картридж, який відстежує статус задокументованих псіоніків."
  components:
  - type: Sprite
    sprite: Objects/Devices/cartridge.rsi
    state: cart-psi
  - type: Icon
    sprite: Objects/Devices/cartridge.rsi
    state: cart-psi
  - type: UIFragment
    ui: !type:PsiWatchUi
  - type: Cartridge
    programName: psi-watch-program-name
    icon:
      sprite: DeltaV/Structures/Machines/glimmer_machines.rsi
      state: prober
  - type: PsiWatchCartridge
