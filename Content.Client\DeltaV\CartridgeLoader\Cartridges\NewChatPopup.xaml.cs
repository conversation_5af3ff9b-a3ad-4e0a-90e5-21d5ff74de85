using System.Linq;
using Content.Shared.Access.Components;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.DeltaV.CartridgeLoader.Cartridges;

[GenerateTypedNameReferences]
public sealed partial class NewChatPopup : DefaultWindow
{
    private const int MaxNumberLength = 4; // i hardcoded it to be 4 so suffer

    public event Action<uint, string, string?>? OnChatCreated;

    public NewChatPopup()
    {
        RobustXamlLoader.Load(this);

        // margins trolling
        ContentsContainer.Margin = new Thickness(3);

        // Button handlers
        CancelButton.OnPressed += _ => Close();
        CreateButton.OnPressed += _ => CreateChat();

        // Input validation
        NumberInput.OnTextChanged += _ => ValidateInputs();
        NameInput.OnTextChanged += _ => ValidateInputs();

        // Input validation
        NumberInput.OnTextChanged += args =>
        {
            if (args.Text.Length > MaxNumberLength)
                NumberInput.Text = args.Text[..MaxNumberLength];

            // Filter to digits only
            var newText = string.Concat(NumberInput.Text.Where(char.IsDigit));
            if (newText != NumberInput.Text)
                NumberInput.Text = newText;

            ValidateInputs();
        };

        NameInput.OnTextChanged += args =>
        {
            if (args.Text.Length > IdCardConsoleComponent.MaxFullNameLength)
                NameInput.Text = args.Text[..IdCardConsoleComponent.MaxFullNameLength];
            ValidateInputs();
        };

        JobInput.OnTextChanged += args =>
        {
            if (args.Text.Length > IdCardConsoleComponent.MaxJobTitleLength)
                JobInput.Text = args.Text[..IdCardConsoleComponent.MaxJobTitleLength];
        };
    }

    private void ValidateInputs()
    {
        var isValid = !string.IsNullOrWhiteSpace(NumberInput.Text) &&
                      !string.IsNullOrWhiteSpace(NameInput.Text) &&
                      NumberInput.Text.Length == MaxNumberLength &&
                      uint.TryParse(NumberInput.Text, out _);

        CreateButton.Disabled = !isValid;
    }

    private void CreateChat()
    {
        if (!uint.TryParse(NumberInput.Text, out var number))
            return;

        var name = NameInput.Text.Trim();
        var job = string.IsNullOrWhiteSpace(JobInput.Text) ? null : JobInput.Text.Trim();

        OnChatCreated?.Invoke(number, name, job);
        Close();
    }

    public void ClearInputs()
    {
        NumberInput.Text = string.Empty;
        NameInput.Text = string.Empty;
        JobInput.Text = string.Empty;
        ValidateInputs();
    }
}
