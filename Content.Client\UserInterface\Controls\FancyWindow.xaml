
<controls:FancyWindow xmlns="https://spacestation14.io"
                      xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                      MouseFilter="Stop"
                      MinWidth="200" MinHeight="150">
    <PanelContainer StyleClasses="AngleRect" />

    <BoxContainer Orientation="Vertical">
        <Control>
            <PanelContainer StyleClasses="WindowHeadingBackground" />
            <BoxContainer Margin="4 2 8 0" Orientation="Horizontal">
                <Label Name="WindowTitle"
                       HorizontalExpand="True" VAlign="Center" StyleClasses="FancyWindowTitle" />
                <TextureButton Name="HelpButton" StyleClasses="windowHelpButton" VerticalAlignment="Center" Disabled="True" Visible="False" Access="Public" />
                <TextureButton Name="CloseButton" StyleClasses="windowCloseButton"
                               VerticalAlignment="Center" />
            </BoxContainer>
        </Control>
        <PanelContainer StyleClasses="LowDivider" />
        <Control Access="Public" Name="ContentsContainer" Margin="0 2" RectClipContent="True" VerticalExpand="true" />
    </BoxContainer>
</controls:FancyWindow>
