# TODO: Have a seperate lid state that can be popped on and off (not as a seperate item) that changes whether the vial can be spilled or not.

- type: entity
  name: "флакон"
  parent: BaseItem
  id: BaseChemistryEmptyVial
  description: "Маленький пухирець."
  components:
  - type: Tag
    tags:
    - Trash
    - CentrifugeCompatible
  - type: PhysicalComposition
    materialComposition:
      Glass: 25
  - type: FitsInDispenser
    solution: beaker
  - type: SpaceGarbage
  - type: Sprite
    sprite: Objects/Specific/Chemistry/vial.rsi
    layers:
      - state: vial-1
      - state: vial-1-1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 6
    fillBaseName: vial-1-
    inHandsMaxFillLevels: 4
    inHandsFillBaseName: -fill-
  - type: Drink
    solution: beaker
  - type: SolutionContainerManager
    solutions:
      beaker:
        maxVol: 30
  - type: MixableSolution
    solution: beaker
  - type: RefillableSolution
    solution: beaker
  - type: DrainableSolution
    solution: beaker
  - type: ExaminableSolution
    solution: beaker
  - type: DrawableSolution
    solution: beaker
  - type: SolutionTransfer
    maxTransferAmount: 30
    canChangeTransferAmount: true
  - type: SolutionItemStatus
    solution: beaker
  - type: UserInterface
    interfaces:
      enum.TransferAmountUiKey.Key:
        type: TransferAmountBoundUserInterface
  - type: Item
    size: Tiny
    sprite: Objects/Specific/Chemistry/vial.rsi
    shape:
    - 0,0,0,0
  - type: MeleeWeapon
    soundNoDamage:
      path: "/Audio/Effects/Fluids/splat.ogg"
    damage:
      types:
        Blunt: 0
  - type: TrashOnSolutionEmpty
    solution: beaker
  - type: StaticPrice
    price: 100
  - type: DamageOnLand
    damage:
      types:
        Blunt: 5
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 2
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 15
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
          params:
            volume: -4
      - !type:SpillBehavior { }
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlass:
            min: 0
            max: 1
        transferForensics: true
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Spillable
    solution: beaker

- type: entity
  id: VestineChemistryVial
  name: "пухирчаста пробірка"
  parent: BaseChemistryEmptyVial
  components:
    - type: SolutionContainerManager
      solutions:
        beaker:
          maxVol: 30
          reagents:
            - ReagentId: Vestine
              Quantity: 30
    - type: Tag
      tags: []
