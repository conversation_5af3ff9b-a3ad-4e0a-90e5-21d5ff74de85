﻿- type: entity
  id: ImmovableRod
  name: "нерухомий стрижень"
  description: "Відчувається, що він голодний. Зазвичай це поганий знак."
  components:
  - type: Clickable
  - type: InteractionOutline
  - type: MovementIgnoreGravity
  - type: Sprite
    sprite: Objects/Fun/immovable_rod.rsi
    state: icon
    noRot: false
  - type: ImmovableRod
    minSpeed: 15 # Goobstation
    maxSpeed: 15 # Goobstation
  - type: Physics
    bodyType: Dynamic
    linearDamping: 0
  - type: PointLight
    radius: 3
    color: red
    energy: 2.0
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb # Goob edit
          bounds: "-0.2,-0.5,0.2,0.5"
        density: 2.5 # Goob edit
        hard: false
        mask: # Goob edit
        - Impassable
        - Opaque
        layer: # Goobstation
        - MidImpassable
  - type: WarpPoint
    follow: true
    location: immovable rod

# goob edit
- type: entity
  id: ImmovableVoidRod
  name: "нерухомий стрижень порожнечі"
  description: "Відчувається, що він голодний. Зазвичай це поганий знак."
  components:
  - type: Clickable
  - type: InteractionOutline
  - type: MovementIgnoreGravity
  - type: Sprite
    sprite: _Goobstation/Heretic/abilities_heretic.rsi
    state: immovable_rod
    noRot: false
  - type: ImmovableVoidRod
  - type: Physics
    bodyType: Dynamic
    linearDamping: 0
  - type: PointLight
    radius: 3
    color: blue
    energy: 2.0
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb # Goob edit
          bounds: "-0.1,-0.5,0.1,0.5"
        density: 5
        hard: false
        layer:
        - Impassable
        - Opaque

- type: entity
  id: ImmovableRodDespawn
  suffix: Despawn
  parent: ImmovableRod
  components:
  - type: TimedDespawn
    lifetime: 30.0

- type: entity
  id: ImmovableRodSlow
  parent: ImmovableRodDespawn
  suffix: Slow
  components:
  - type: ImmovableRod
    minSpeed: 1
    maxSpeed: 5

- type: entity
  parent: ImmovableRodDespawn
  id: ImmovableRodKeepTiles
  suffix: Keep Tiles
  components:
  - type: ImmovableRod
    destroyTiles: false
    hitSoundProbability: 1.0

# For Wizard Polymorph
- type: entity
  parent: ImmovableRod
  id: ImmovableRodWizard
  suffix: Wizard
  components:
  - type: ImmovableRod
    minSpeed: 6 # Goob edit
    maxSpeed: 6 # Goob edit
    hitSoundProbability: 1 # Goob edit
    ignoreResistances: true # Goobstation
    knockdownTime: 4 # Goobstation
    destroyTiles: false
    randomizeVelocity: false
    shouldGib: false
    damage:
      types:
        Blunt: 70 # Goob edit
  - type: MovementIgnoreGravity
    gravityState: true
  # - type: InputMover # Goob edit
  - type: MovementSpeedModifier
    weightlessAcceleration: 5
    weightlessModifier: 2
    weightlessFriction: 0
    friction: 0
    frictionNoInput: 0
  - type: CanMoveInAir
  - type: MovementAlwaysTouching
  - type: NoSlip
  - type: Trail # Goobstation
    frequency: 0.1
    lifetime: 1
    lerpTime: 0.1
    alphaLerpAmount: 0.4
    sprite:
      sprite: /Textures/Objects/Fun/immovable_rod.rsi
      state: icon
  - type: StatusIcon # Goobstation
    bounds: -0.5,-0.5,0.5,0.5
  - type: IsDeadIC # Goobstation
    dead: false

- type: entity
  parent: ImmovableRodKeepTiles
  id: ImmovableRodKeepTilesStill
  suffix: Keep Tiles, Still
  components:
  - type: ImmovableRod
    randomizeVelocity: false
    damage:
      types:
        Blunt: 120

- type: entity
  parent: ImmovableRodKeepTiles # Goob edit
  id: ImmovableRodMop
  name: "нерухома швабра"
  description: "Кинутий, як спис, з силою тисячі розлючених прибиральників."
  components:
  - type: Sprite
    sprite: Objects/Specific/Janitorial/mop.rsi
    state: mop
    rotation: 225
    noRot: false

- type: entity
  parent: ImmovableRodKeepTiles # Goob edit
  id: ImmovableRodShark
  name: "нерухома акула"
  description: "АКУЛИНЕ ТОРНАДО!"
  components:
  - type: Sprite
    sprite: Objects/Fun/sharkplush.rsi
    state: blue
    rotation: 90
    noRot: false

- type: entity
  parent: ImmovableRodKeepTiles # Goob edit
  id: ImmovableRodClown
  name: "нерухомий клоун"
  description: "Викинутий із сусідньої станції за одну сонячну систему від нас. ГУДОК!"
  components:
  - type: Sprite
    sprite: Markers/jobs.rsi
    state: clown
    rotation: 180
    noRot: false

- type: entity
  parent: ImmovableRodKeepTiles # Goob edit
  id: ImmovableRodBanana
  name: "нерухомий банан"
  description: "Принаймні, ви не посковзнетеся на ньому."
  components:
  - type: Sprite
    sprite: Objects/Specific/Hydroponics/banana.rsi
    state: produce
    noRot: false

- type: entity
  parent: ImmovableRodKeepTiles # Goob edit
  id: ImmovableRodHammer
  name: "нерухомий молоток"
  description: "Дзявк."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Melee/sledgehammer.rsi
    state: icon
    rotation: 225
    noRot: false

- type: entity
  parent: ImmovableRodKeepTiles # Goob edit
  id: ImmovableRodThrongler
  name: "нерухомий натовп"
  description: "Якщо зловите, можете залишити собі."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Melee/Throngler2.rsi
    state: icon
    rotation: 225
    noRot: false

- type: entity
  parent: ImmovableRodKeepTiles # Goob edit
  id: ImmovableRodGibstick
  name: "нерухомий крючок"
  description: "Чого ви очікували?"
  components:
  - type: Sprite
    sprite: Objects/Weapons/Melee/debug.rsi
    state: icon
    rotation: 225
    noRot: false

- type: entity
  parent: ImmovableRodKeepTiles # Goob edit
  id: ImmovableRodWeh
  name: "нерухоме майно"
  description: "УХ!"
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: plushie_lizard
    noRot: false
