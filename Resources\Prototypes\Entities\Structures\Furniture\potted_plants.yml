- type: entity
  id: PottedPlantBase
  parent: BaseStructureDynamic
  name: "рослина в горщику"
  description: "Трохи природи в горщику."
  abstract: true
  components:
  - type: Transform
    noRot: true
  - type: InteractionOutline
  - type: Physics
    bodyType: Dynamic
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.1
        density: 190
        mask:
        - HighImpassable
  - type: Sprite
    drawdepth: Overdoors
    offset: "0.0,0.3"
    sprite: Structures/Furniture/potted_plants.rsi
    noRot: true
  - type: PottedPlantHide
  - type: SecretStash
    secretPartName: secret-stash-part-plant
  - type: ContainerContainer
    containers:
      stash: !type:ContainerSlot {}
  - type: Pullable
  - type: Damageable
    damageContainer: Inorganic # The pot. Not the plant. Or is it plastic?
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          path: /Audio/Effects/plant_rustle.ogg

- type: entity
  id: PottedPlant0
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: applebush #whoever named this texture in the rsi sucks

- type: entity
  id: PottedPlant1
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-01

- type: entity
  id: PottedPlant2
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-02

- type: entity
  id: PottedPlant3
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-03

- type: entity
  id: PottedPlant4
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-04

- type: entity
  id: PottedPlant5
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-05

- type: entity
  id: PottedPlant6
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-06

- type: entity
  id: PottedPlant7
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-07

- type: entity
  id: PottedPlant8
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-08

- type: entity
  id: PottedPlantBioluminscent #PottedPlant9 but i didn't want map conflicts
  parent: PottedPlantBase
  name: "біолюмінесцентна рослина в горщику"
  description: "Він виробляє світло!"
  components:
  - type: Sprite
    state: plant-09
  - type: PointLight
    radius: 2
    color: "#2cb2e8"

- type: entity
  id: PottedPlant10
  parent: PottedPlantBase
  description: "Гарний шматочок природи в горщику."
  components:
  - type: Sprite
    state: plant-10

- type: entity
  id: PottedPlant11
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-11

- type: entity
  id: PottedPlant12
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-12

- type: entity
  id: PottedPlant13
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-13

- type: entity
  id: PottedPlant14
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-14

- type: entity
  id: PottedPlant15
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-15

- type: entity
  id: PottedPlant16
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-16

- type: entity
  id: PottedPlant17
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-17

- type: entity
  id: PottedPlant18
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-18

- type: entity
  id: PottedPlant19
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-19

- type: entity
  id: PottedPlant20
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-20

- type: entity
  id: PottedPlant21
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-21

- type: entity
  id: PottedPlant22
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-22

- type: entity
  id: PottedPlant23
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-23

- type: entity
  id: PottedPlant24
  parent: PottedPlantBase
  components:
  - type: Sprite
    state: plant-24

- type: entity
  id: PottedPlantRD #PottedPlant25
  parent: PottedPlantBase
  # DeltaV - Epistemics Department - Replace Research Director with Mystagogue
  name: "Кімнатна рослина містагога"
  description: "A gift from the botanical staff, presented after the Mystagogue's reassignment. There's a tag on it that says \"Y'all come back now, y'hear?\"\nIt doesn't look very healthy..."
  # End of modified code
  components:
  - type: Sprite
    state: plant-25
  - type: StealTarget
    stealGroup: PlantRD

- type: entity
  id: PottedPlant26
  parent: PottedPlantBase
  description: "Мені здається, чи він блимає?"
  components:
  - type: Sprite
    state: plant-26

#these are all the plastic plants. They inherit from the first one because they don't have
#enough differences to warrant getting a unique abstract prototype. It's just the name and description.
- type: entity
  id: PottedPlant27
  parent: PottedPlantBase
  name: "пластикова рослина в горщику"
  description: "Фальшиве, дешеве на вигляд, пластикове дерево. Ідеально підходить для людей, які вбивають кожну рослину, до якої торкаються."
  components:
  - type: Sprite
    state: plant-27

- type: entity
  id: PottedPlant28
  parent: PottedPlant27
  components:
  - type: Sprite
    state: plant-28

- type: entity
  id: PottedPlant29
  parent: PottedPlant27
  components:
  - type: Sprite
    state: plant-29

- type: entity
  id: PottedPlant30
  parent: PottedPlant27
  components:
  - type: Sprite
    state: plant-30
