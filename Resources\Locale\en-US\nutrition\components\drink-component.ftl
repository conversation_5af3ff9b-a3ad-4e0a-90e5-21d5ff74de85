drink-component-on-use-is-empty = {CAPITALIZE(THE($owner))} is empty!
drink-component-on-examine-is-empty = [color=gray]Empty[/color]
drink-component-on-examine-is-opened = [color=yellow]Opened[/color]
drink-component-on-examine-is-sealed = The seal is intact.
drink-component-on-examine-is-unsealed = The seal is broken.
drink-component-on-examine-is-full = Full
drink-component-on-examine-is-mostly-full = Mostly Full
drink-component-on-examine-is-half-full = Halfway Full
drink-component-on-examine-is-half-empty = Halfway Empty
drink-component-on-examine-is-mostly-empty = Mostly Empty
drink-component-on-examine-exact-volume = It contains {$amount}u.
drink-component-try-use-drink-not-open = Open {$owner} first!
drink-component-try-use-drink-is-empty = {CAPITALIZE(THE($entity))} is empty!
drink-component-try-use-drink-cannot-drink = You can't drink anything!
drink-component-try-use-drink-had-enough = You can't drink more!
drink-component-try-use-drink-cannot-drink-other = They can't drink anything!
drink-component-try-use-drink-had-enough-other = They can't drink more!
drink-component-try-use-drink-success-slurp = Slurp
drink-component-try-use-drink-success-slurp-taste = Slurp. {$flavors}
drink-component-force-feed = {CAPITALIZE(THE($user))} is trying to make you drink something!
drink-component-force-feed-success = {CAPITALIZE(THE($user))} forced you to drink something! {$flavors}
drink-component-force-feed-success-user = You successfully feed {THE($target)}


drink-system-verb-drink = Drink
