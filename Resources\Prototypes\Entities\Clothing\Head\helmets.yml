#These are intentionally all very mediocre as locational damage does not exist yet. Without it, armor values will stack in a way that makes it really god damn hard to determine how effective something is, along with making players extremely tanky.
#When it DOES exist, the values here should be totally reworked - probably just port them from SS13.

#Basic Helmet (Security Helmet)
- type: entity
  parent: [ClothingHeadBase, BallisticGlasses]
  id: ClothingHeadHelmetBasic
  name: "шолом"
  description: "Стандартне захисне спорядження. Захищає голову від ударів."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Helmets/security.rsi # DeltaV - resprite
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Helmets/security.rsi # DeltaV - resprite
  - type: Armor #Values seem to let the user survive one extra hit if attacked consistently.
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Heat: 0.9
  - type: Tag
    tags:
    - WhitelistChameleon
    - ClothingHeadHelmetBasic

#Mercenary Helmet
- type: entity
  parent: ClothingHeadHelmetBasic
  id: ClothingHeadHelmetMerc
  name: "шолом найманця"
  description: "Бойовий шолом, який зазвичай використовують найманці, міцний, легкий і пахне порохом та джунглями."
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/merc_helmet.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/merc_helmet.rsi

#SWAT Helmet
- type: entity
  parent: [ClothingHeadBase, ClothingHeadEVAHelmetBase, BallisticGlasses]
  id: ClothingHeadHelmetSwat
  name: "шолом спецназу"
  description: "Надзвичайно міцний шолом, який зазвичай використовується воєнізованими силами. Цей шолом має логотип Nanotrasen на верхній частині."
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/swat.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/swat.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.45
    lowPressureMultiplier: 1000
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.85
        Slash: 0.85
        Piercing: 0.85
        Heat: 0.85
        Radiation: 0.8
        Caustic: 0.9
  - type: ExplosionResistance
    damageCoefficient: 0.75

#Syndicate SWAT Helmet
- type: entity
  parent: ClothingHeadHelmetSwat
  id: ClothingHeadHelmetSwatSyndicate
  name: "шолом спецназу"
  suffix: Syndicate
  description: "Надзвичайно міцний шолом, який зазвичай використовується воєнізованими силами. Його прикрашає мерзенний візерунок у вигляді червоно-чорних смуг."
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/swat_syndicate.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/swat_syndicate.rsi

#Light Riot Helmet
- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHelmetRiot
  name: "легкий шолом для придушення бунтів"
  description: "Це шолом, спеціально розроблений для захисту від атак з близької відстані."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Helmets/light_riot.rsi # DeltaV - resprite
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Helmets/light_riot.rsi # DeltaV - resprite
  - type: IngestionBlocker
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.8
        Slash: 0.8
        Piercing: 0.95

#Bombsuit Helmet
- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHelmetBombSuit
  name: "вибухозахисний шолом"
  description: "Важкий шолом, призначений для того, щоб витримувати тиск бомби та осколків, які вона може спричинити."
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/bombsuit.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/bombsuit.rsi
  - type: IngestionBlocker
  - type: ExplosionResistance
    damageCoefficient: 0.9
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.95
        Slash: 0.95
        Piercing: 0.95
  - type: HideLayerClothing
    slots:
    - Hair
    - Snout

#Janitorial Bombsuit Helmet
- type: entity
  parent: ClothingHeadHelmetBombSuit
  id: ClothingHeadHelmetJanitorBombSuit
  name: "вибухозахисний шолом прибиральника"
  description: "Важкий шолом, призначений для протистояння вибухам, що утворюються в результаті реакцій між хімічними речовинами."
  suffix: DO NOT MAP
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/janitor_bombsuit.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/janitor_bombsuit.rsi

#Cult Helmet
- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHelmetCult
  name: "шолом культиста"
  description: "Міцний, зловісний на вигляд культовий шолом."
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/cult.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/cult.rsi
  - type: IngestionBlocker
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.8
        Piercing: 0.9
        Heat: 0.9

#Space Ninja Helmet
- type: entity
  parent: ClothingHeadEVAHelmetNoIngestionBlocker
  id: ClothingHeadHelmetSpaceNinja
  name: "шолом космічного ніндзя"
  description: "Те, що може здатися простим чорним одягом, насправді є дуже складним шоломом з наноплетіння. Стандартне спорядження ніндзя."
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/spaceninja.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/spaceninja.rsi
  - type: Tag
    tags:
    - WhitelistChameleon
  - type: IdentityBlocker
  - type: HideLayerClothing
    slots:
    - Hair
    - Snout
  - type: Unremoveable


#Templar Helmet
- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHelmetTemplar
  name: "шолом тамплієра"
  description: "ДЕУС ВУЛЬТ!"
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/templar.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/templar.rsi
  - type: IngestionBlocker
  - type: IdentityBlocker

#Thunderdome Helmet
- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHelmetThunderdome
  name: "шолом громового купола"
  description: "Нехай битва почнеться!"
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/thunderdome.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/thunderdome.rsi

#Wizard Helmet
- type: entity
  parent: [ClothingHeadBase, ClothingHeadHatWizardBaseArmor] # Goob edit
  id: ClothingHeadHelmetWizardHelm
  name: "шолом чарівника"
  description: "Дивний на вигляд шолом, який напевно належить справжньому магічному користувачеві." # Goob edit
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/wizardhelm.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/wizardhelm.rsi
  - type: IngestionBlocker
  - type: IdentityBlocker
  - type: Armor # Goobstation
    modifiers:
      coefficients:
        Blunt: 0.85
        Slash: 0.85
        Piercing: 0.85
        Heat: 0.85
        Caustic: 0.9
        Radiation: 0.9

#Fire Helmet
- type: entity
  parent: ClothingHeadLightBase
  id: ClothingHeadHelmetFire
  name: "пожежна каска"
  description: "Найкращий друг атмосферного техніка. Забезпечує певну термостійкість і виглядає круто."
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/firehelmet.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/firehelmet.rsi
    quickEquip: true
  - type: IngestionBlocker
  - type: TemperatureProtection
    coefficient: 0.005
  - type: FireProtection
    reduction: 0.15 # not fully sealed so less protection
  - type: IdentityBlocker
  - type: Tag
    tags:
    - WhitelistChameleon
  - type: HideLayerClothing
    slots:
    - Hair
    - Snout

#Atmos Fire Helmet
- type: entity
  parent: ClothingHeadLightBase
  id: ClothingHeadHelmetAtmosFire
  name: "пожежна каска атмосферного техніка"
  description: "Пожежний шолом атмосу, здатний зберігати прохолоду в будь-якій ситуації."
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/atmos_firehelmet.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/atmos_firehelmet.rsi
    quickEquip: true
  - type: IngestionBlocker
  - type: TemperatureProtection
    coefficient: 0.005
  - type: FireProtection
    reduction: 0.2
  - type: PressureProtection
    highPressureMultiplier: 0.25
    lowPressureMultiplier: 1000
  - type: IdentityBlocker
  - type: Tag
    tags:
    - WhitelistChameleon
  - type: HideLayerClothing
    slots:
    - Hair
    - Snout

#Chitinous Helmet
- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHelmetLing
  name: "хітиновий шолом"
  description: "Всепоглинаюча хітинова маса броні."
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/linghelmet.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/linghelmet.rsi
  - type: Armor #admeme item so it should be fine being overpowered while helmets are still intentionally kneecapped.
    modifiers:
      coefficients:
        Blunt: 0.5
        Slash: 0.5
        Piercing: 0.5
        Heat: 0.9

#ERT HELMETS
#ERT Leader Helmet
- type: entity
  parent: ClothingHeadHelmetBasic
  id: ClothingHeadHelmetERTLeader
  name: "шолом лідера ГШР"
  description: "Шолом для атмосфери, який носить лідер групи швидкого реагування \"НаноТрейзен\". Має сині відблиски."
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/ert_leader.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/ert_leader.rsi

#ERT Security Helmet
- type: entity
  parent: ClothingHeadHelmetBasic
  id: ClothingHeadHelmetERTSecurity
  name: "шолом охоронця ГШР"
  description: "Шолом для атмосфери, який носять співробітники служби безпеки команди реагування на надзвичайні ситуації \"НаноТрейзен\". Має червоні підсвічування."
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/ert_security.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/ert_security.rsi

#ERT Medic Helmet
- type: entity
  parent: ClothingHeadHelmetBasic
  id: ClothingHeadHelmetERTMedic
  name: "шолом медика ГШР"
  description: "Шолом для атмосфери, який носять медики команди швидкого реагування \"НаноТрейзен\". Має білі відблиски."
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/ert_medic.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/ert_medic.rsi

#ERT Engineer Helmet
- type: entity
  parent: ClothingHeadHelmetBasic
  id: ClothingHeadHelmetERTEngineer
  name: "шолом інженера ГШР"
  description: "Шолом для атмосфери, який носять інженери команди реагування на надзвичайні ситуації \"НаноТрейзен\". Має помаранчеві підсвічування."
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/ert_engineer.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/ert_engineer.rsi

#ERT Janitor Helmet
- type: entity
  parent: ClothingHeadHelmetBasic
  id: ClothingHeadHelmetERTJanitor
  name: "шолом прибиральника ГШР"
  description: "Шолом для атмосфери, який носять прибиральники з команди швидкого реагування \"НаноТрейзен\". Має темно-фіолетові відблиски."
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/ert_janitor.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/ert_janitor.rsi

# Lavaland shuttupance - Raid Helmet
- type: entity
  parent: ClothingHeadHelmetBasic
  id: ClothingHeadHelmetRaid
  name: "рейдерський шолом синдикату"
  description: "Броньований шолом для використання з рейдерським костюмом синдикату. Дуже стильний."
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/syndie-raid.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/syndie-raid.rsi
  - type: Armor
    modifiers: #There's gotta be SOME reason to use this over other helmets.
      coefficients:
        Blunt: 0.85
        Slash: 0.85
        Piercing: 0.85
        Heat: 0.85

#Bone Helmet
- type: entity
  parent: ClothingHeadHelmetBasic
  id: ClothingHeadHelmetBone
  name: "кістяний шолом"
  description: "Крутий шолом, зроблений з черепа ваших ворогів."
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/bone_helmet.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/bone_helmet.rsi
  - type: Construction
    graph: BoneHelmet
    node: helmet

- type: entity
  parent: ClothingHeadHelmetBasic
  id: ClothingHeadHelmetPodWars
  name: "шолом ironclad II"
  description: "Шолом Ironclad II, реліквія часів капсульних війн."
  components:
  - type: Sprite
    sprite: Clothing/Head/Helmets/podwars_helmet.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/podwars_helmet.rsi
