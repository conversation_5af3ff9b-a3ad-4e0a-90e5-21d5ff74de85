- type: entity
  id: SignalTimerElectronics
  parent: BaseElectronics
  name: "електроніка таймера сигналу"
  description: "Електронна плата, що використовується в схемах таймерів. Схоже, що для зміни типу плати можна використати викрутку."
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: charger_APC
  - type: Construction
    deconstructionTarget: null
    graph: TimerElectronics
    node: signal
  - type: Tag
    tags:
    - TimerSignalElectronics
  - type: StaticPrice
    price: 15

- type: entity
  id: ScreenTimerElectronics
  parent: SignalTimerElectronics
  name: "електроніка екранного таймера"
  components:
  - type: Construction
    deconstructionTarget: null
    graph: TimerElectronics
    node: screen
  - type: Tag
    tags:
    - TimerScreenElectronics

- type: entity
  id: BrigTimerElectronics
  parent: SignalTimerElectronics
  name: "електроніка таймера бригу"
  components:
  - type: Construction
    deconstructionTarget: null
    graph: TimerElectronics
    node: brig
  - type: Tag
    tags:
    - TimerBrigElectronics
