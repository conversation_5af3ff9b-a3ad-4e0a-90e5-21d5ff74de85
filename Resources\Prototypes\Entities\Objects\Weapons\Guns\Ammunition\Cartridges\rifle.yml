- type: entity
  id: BaseCartridgeRifle
  name: "патрон (гвинтівка .20)"
  parent: BaseCartridge
  abstract: true
  components:
  - type: Tag
    tags:
      - Cartridge
      - CartridgeRifle
  - type: CartridgeAmmo
    proto: BulletRifle
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Casings/ammo_casing.rsi
    layers:
    - state: base
      map: ["enum.AmmoVisualLayers.Base"]
  - type: Appearance
  - type: SpentAmmoVisuals

- type: entity
  id: CartridgeRifle
  name: "патрон (гвинтівка .20)"
  parent: BaseCartridgeRifle
  components:
  - type: CartridgeAmmo
    proto: BulletRifle

- type: entity
  id: CartridgeRiflePractice
  name: "патрон (гвинтівка калібру .20)"
  parent: BaseCartridgeRifle
  components:
  - type: CartridgeAmmo
    proto: BulletRiflePractice
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#dbdbdb"


- type: entity
  id: CartridgeRifleRubber
  name: "набій (.20 гвинтівковий гумовий)"
  parent: BaseCartridgeRifle
  components:
  - type: CartridgeAmmo
    proto: BulletRifleRubber
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#43c4f7"

- type: entity
  id: CartridgeRifleIncendiary
  name: "набій (.20 гвинтівковий запальний)"
  parent: BaseCartridgeRifle
  components:
  - type: CartridgeAmmo
    proto: BulletRifleIncendiary
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#ff6e52"

- type: entity
  id: CartridgeRifleUranium
  name: "патрон (.20 гвинтівочний уран)"
  parent: BaseCartridgeRifle
  components:
  - type: CartridgeAmmo
    proto: BulletRifleUranium
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#65fe08"

- type: entity
  id: CartridgeRifleShrapnel
  name: "патрон (.20 гвинтівковий, шрапнельний)"
  parent: BaseCartridgeRifle
  components:
  - type: CartridgeAmmo
    proto: BulletRifleShrapnelSpread
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#FF00FF"
