- type: entity
  id: DNDNecromancyServant
  name: "Некромантський слуга"
  description: "Оживлений труп, що служить некроманту."
  components:
  - type: DNDNecromancyServant
    maxDistanceFromMaster: 15.0
    maxLifetime: 300.0 # 5 хвилин
    currentCommand: Follow
  - type: HTN
    rootTask:
      task: NecromancyServantCompound
  - type: NpcFactionMember
    factions:
    - DNDNecromancyServant
  - type: MovementSpeedModifier
    baseWalkSpeed: 3.5
    baseSprintSpeed: 5.0
  - type: MobState
  - type: MobThresholds
    thresholds:
      0: Alive
      80: Critical
      120: Dead
  - type: Damageable
    damageContainer: Biological
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 120
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: StatusEffects
    allowed:
    - Stun
    - KnockedDown
    - SlowedDown
    - Flashed
  - type: Body
    prototype: Human
  - type: HumanoidAppearance
    species: Human
  - type: Inventory
    templateId: human
  - type: ContainerContainer
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 185
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: MovementIgnoreGravity
  - type: Clickable
  - type: InteractionOutline
  - type: Physics
    bodyType: KinematicController
  - type: InputMover
  - type: MobMover
  - type: Pullable
  - type: Tag
    tags:
    - CannotSuicide
    - DoorBumpOpener
    - FootstepSound
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Species/Human/parts.rsi
    layers:
    - map: ["enum.HumanoidVisualLayers.Chest"]
      color: "#e8b59b"
      sprite: Mobs/Species/Human/parts.rsi
      state: "torso_m"
    - map: ["enum.HumanoidVisualLayers.Head"]
      color: "#e8b59b"
      sprite: Mobs/Species/Human/parts.rsi
      state: "head_m"
    - map: ["enum.HumanoidVisualLayers.Eyes"]
      color: "#008800"
      sprite: Mobs/Species/Human/parts.rsi
      state: "eyes_m"
    - map: ["enum.HumanoidVisualLayers.RArm"]
      color: "#e8b59b"
      sprite: Mobs/Species/Human/parts.rsi
      state: "r_arm"
    - map: ["enum.HumanoidVisualLayers.LArm"]
      color: "#e8b59b"
      sprite: Mobs/Species/Human/parts.rsi
      state: "l_arm"
    - map: ["enum.HumanoidVisualLayers.RLeg"]
      color: "#e8b59b"
      sprite: Mobs/Species/Human/parts.rsi
      state: "r_leg"
    - map: ["enum.HumanoidVisualLayers.LLeg"]
      color: "#e8b59b"
      sprite: Mobs/Species/Human/parts.rsi
      state: "l_leg"

  - type: Temperature
    heatDamageThreshold: 360
    coldDamageThreshold: 260
    currentTemperature: 310.15
    specificHeat: 42
    coldDamage:
      types:
        Cold: 0.1 #per second, scales with temperature difference
    heatDamage:
      types:
        Heat: 1 #per second, scales with temperature difference
  - type: Respirator
    damage:
      types:
        Asphyxiation: 1.0
    damageRecovery:
      types:
        Asphyxiation: -1.0
  - type: Perishable
  - type: Bloodstream
    bloodlossDamage:
      types:
        Bloodloss: 0.5
    bloodlossHealDamage:
      types:
        Bloodloss: -1
    bloodMaxVolume: 300
  - type: CombatMode
  - type: Alerts
  - type: Actions
  - type: Eye
  - type: CameraRecoil
  - type: Examiner
  - type: CanHostGuardian
  - type: NPCRecentlyInjected
  - type: MeleeWeapon
    wideAnimationRotation: -135
    attackRate: 1.0
    damage:
      types:
        Blunt: 8
        Structural: 5
  - type: Stamina
    critThreshold: 120
  - type: Speech
    speechSounds: Zombie
  - type: Vocal
    sounds:
      Male: MaleZombie
      Female: FemaleZombie
      Unsexed: MaleZombie
  - type: TypingIndicator
    proto: zombie
  - type: ReplacementAccent
    accent: zombie
  - type: EmitSoundOnPickup
    sound:
      path: /Audio/Weapons/Guns/Gunshots/bang.ogg
  - type: EmitSoundOnDrop
    sound:
      path: /Audio/Items/bikehorn.ogg
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Effects/bodyfall1.ogg
  - type: Zombie
    zombieOnly: true
  - type: ZombieImmune
  - type: PressureImmunity
  - type: TemperatureProtection
    coefficient: 0.1
