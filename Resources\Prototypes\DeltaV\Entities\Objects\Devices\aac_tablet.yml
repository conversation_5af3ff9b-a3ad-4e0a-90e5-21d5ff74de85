- type: entity
  parent: BaseItem
  id: AACTablet
  name: "Планшет AAC"
  description: "Пристрій для \"додаткової та альтернативної комунікації\", який дозволяє людям з вадами мовлення спілкуватися."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Devices/tablets.rsi
    layers:
      - state: aac_tablet
      - state: aac_screen
        shader: unshaded
    state: icon
  - type: Item
    inhandVisuals:
      left:
      - state: aac-inhand-left
      - state: aac_screen-inhand-left
        shader: unshaded
      right:
      - state: aac-inhand-right
      - state: aac_screen-inhand-right
        shader: unshaded
  - type: ActivatableUI
    singleUser: false
    key: enum.AACTabletKey.Key
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
  - type: UserInterface
    interfaces:
      enum.AACTabletKey.Key:
        type: AACBoundUserInterface
  - type: Speech
    speechSounds: Alto
  - type: AACTablet
  - type: VoiceMask
  - type: LanguageSpeaker
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    understands:
    - TauCetiBasic
