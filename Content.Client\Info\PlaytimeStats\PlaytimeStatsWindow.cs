using System.Linq;
using System.Text.RegularExpressions;
using Content.Client.Players.PlayTimeTracking;
using Content.Client.UserInterface.Controls;
using Robust.Client.AutoGenerated;
using Robust.Client.Graphics;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Info.PlaytimeStats;

[GenerateTypedNameReferences]
public sealed partial class PlaytimeStatsWindow : FancyWindow
{
    [Dependency] private readonly JobRequirementsManager _jobRequirementsManager = default!;
    private ISawmill _sawmill = Logger.GetSawmill("PlaytimeStatsWindow");
    private readonly Color _altColor = Color.FromHex("#292B38");
    private readonly Color _defaultColor = Color.FromHex("#2F2F3B");
    private bool _useAltColor;

    public PlaytimeStatsWindow()
    {
        IoCManager.InjectDependencies(this);
        RobustXamlLoader.Load(this);

        PopulatePlaytimeHeader();
        PopulatePlaytimeData();
    }

    private void PopulatePlaytimeHeader()
    {
        var header = new PlaytimeStatsHeader();
        header.OnHeaderClicked += HeaderClicked;
        header.BackgroundColorPlaytimePanel.PanelOverride = new StyleBoxFlat(_altColor);
        RolesPlaytimeList.AddChild(header);
    }

    private void HeaderClicked(PlaytimeStatsHeader.Header header, PlaytimeStatsHeader.SortDirection direction)
    {
        switch (header)
        {
            case PlaytimeStatsHeader.Header.Role:
                SortByRole(direction);
                break;
            case PlaytimeStatsHeader.Header.Playtime:
                SortByPlaytime(direction);
                break;
        }
    }

    private void SortByRole(PlaytimeStatsHeader.SortDirection direction)
    {
        var header = RolesPlaytimeList.GetChild(0) as PlaytimeStatsHeader;

        var entries = RolesPlaytimeList.Children.OfType<PlaytimeStatsEntry>().ToList();

        RolesPlaytimeList.RemoveAllChildren();

        if (header != null)
            RolesPlaytimeList.AddChild(header);

        var sortedEntries = (direction == PlaytimeStatsHeader.SortDirection.Ascending)
            ? entries.OrderBy(entry => entry.RoleText).ToList()
            : entries.OrderByDescending(entry => entry.RoleText).ToList();

        _useAltColor = false;

        foreach (var entry in sortedEntries)
        {
            var styleBox = new StyleBoxFlat { BackgroundColor = _useAltColor ? _altColor : _defaultColor };
            entry.UpdateShading(styleBox);
            RolesPlaytimeList.AddChild(entry);
            _useAltColor ^= true;
        }
    }

    private void SortByPlaytime(PlaytimeStatsHeader.SortDirection direction)
    {
        var header = RolesPlaytimeList.GetChild(0) as PlaytimeStatsHeader;

        var entries = RolesPlaytimeList.Children.OfType<PlaytimeStatsEntry>().ToList();

        RolesPlaytimeList.RemoveAllChildren();

        if (header != null)
            RolesPlaytimeList.AddChild(header);

        var sortedEntries = (direction == PlaytimeStatsHeader.SortDirection.Ascending)
            ? entries.OrderBy(entry => entry.Playtime).ToList()
            : entries.OrderByDescending(entry => entry.Playtime).ToList();

        _useAltColor = false;

        foreach (var entry in sortedEntries)
        {
            var styleBox = new StyleBoxFlat { BackgroundColor = _useAltColor ? _altColor : _defaultColor };
            entry.UpdateShading(styleBox);
            RolesPlaytimeList.AddChild(entry);
            _useAltColor ^= true;
        }
    }


    private void PopulatePlaytimeData()
    {
        var overallPlaytime = _jobRequirementsManager.FetchOverallPlaytime();

        var formattedPlaytime = ConvertTimeSpanToHoursMinutes(overallPlaytime);
        OverallPlaytimeLabel.Text = Loc.GetString("ui-playtime-overall", ("time", formattedPlaytime));

        var rolePlaytimes = _jobRequirementsManager.FetchPlaytimeByRoles();

        RolesPlaytimeList.RemoveAllChildren();
        PopulatePlaytimeHeader();

        foreach (var rolePlaytime in rolePlaytimes)
        {
            var role = rolePlaytime.Key;
            var playtime = rolePlaytime.Value;
            AddRolePlaytimeEntryToTable(Loc.GetString(role), playtime.ToString());
        }
    }

    private void AddRolePlaytimeEntryToTable(string role, string playtimeString)
    {
        if (TimeSpan.TryParse(playtimeString, out var playtime))
        {
            var entry = new PlaytimeStatsEntry(role, playtime,
                new StyleBoxFlat(_useAltColor ? _altColor : _defaultColor));
            RolesPlaytimeList.AddChild(entry);
            _useAltColor ^= true;
        }
        else
        {
            _sawmill.Error($"The provided playtime string '{playtimeString}' is not in the correct format.");
        }
    }

    private static string ConvertTimeSpanToHoursMinutes(TimeSpan timeSpan)
    {
        var hours = (int) timeSpan.TotalHours;
        var minutes = timeSpan.Minutes;

        var formattedTimeLoc = Loc.GetString("ui-playtime-time-format", ("hours", hours), ("minutes", minutes));
        return formattedTimeLoc;
    }
}
