- type: species
  id: IPC
  name: species-name-ipc
  roundStart: true
  prototype: MobIPC
  sprites: MobIPCSprites
  defaultSkinTone: "#aaa9ad"
  markingLimits: MobIPCMarkingLimits
  dollPrototype: MobIPCDummy
  skinColoration: Hues
  minAge: 1
  maxAge: 240
  oldAge: 50
  youngAge: 50
  maleFirstNames: IpcFirst
  femaleFirstNames: IpcFirst
  lastNames: IpcLast
  naming: FirstDashLast
  sexes:
  - Unsexed

# The lack of a layer means that
# this person cannot have round-start anything
# applied to that layer. It has to instead
# be defined as a 'custom base layer'
# in either the mob's starting marking prototype,
# or it has to be added in C#.
- type: speciesBaseSprites
  id: MobIPCSprites
  sprites:
    Head: MobIPCHead
    Face: MobHumanoidAnyMarking
    HeadTop: MobHumanoidAnyMarking
    HeadSide: MobHumanoidAnyMarking
    Tail: MobHumanoidAnyMarking
    Hair: MobHumanoidMarkingMatchSkin
    FacialHair: MobHumanoidMarkingMatchSkin
    Eyes: MobHumanoidAnyMarking
    Chest: MobIPCTorso
    Underwear: MobHumanoidAnyMarking
    Undershirt: MobHumanoidAnyMarking
    LArm: MobIPCLArm
    RArm: MobIPCRArm
    LHand: MobIPCLHand
    RHand: MobIPCRHand
    LLeg: MobIPCLLeg
    RLeg: MobIPCRLeg
    LFoot: MobIPCLFoot
    RFoot: MobIPCRFoot
    Wings: MobHumanoidAnyMarking

- type: markingPoints
  id: MobIPCMarkingLimits
  points:
    Head:
      points: 6
      required: true
      defaultMarkings: [ MobIPCHeadDefault ]
    Face:
      points: 6
      required: false
    Chest:
      points: 6
      required: true
      defaultMarkings: [ MobIPCTorsoDefault ]
    HeadSide:
      points: 6
      required: false
    RightLeg:
      points: 6
      required: false
      defaultMarkings: [ MobIPCRLegDefault ]
    RightFoot:
      points: 6
      required: false
      defaultMarkings: [ MobIPCRFootDefault ]
    LeftLeg:
      points: 6
      required: false
      defaultMarkings: [ MobIPCLLegDefault ]
    LeftFoot:
      points: 6
      required: false
      defaultMarkings: [ MobIPCLFootDefault ]
    RightArm:
      points: 6
      required: false
      defaultMarkings: [ MobIPCRArmDefault ]
    RightHand:
      points: 6
      required: false
      defaultMarkings: [ MobIPCRHandDefault ]
    LeftArm:
      points: 6
      required: false
      defaultMarkings: [ MobIPCLArmDefault ]
    LeftHand:
      points: 6
      required: false
      defaultMarkings: [ MobIPCLHandDefault ]
    Wings:
      points: 1
      required: false

- type: humanoidBaseSprite
  id: MobIPCMarkingFollowSkin
  markingsMatchSkin: true
  layerAlpha: 0.5

- type: humanoidBaseSprite
  id: MobIPCScreen

# Head

- type: humanoidBaseSprite
  id: MobIPCHead

- type: humanoidBaseSprite
  id: MobIPCHeadMale

- type: humanoidBaseSprite
  id: MobIPCHeadFemale

- type: marking
  id: MobIPCHeadDefault
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [ IPC ]
  sprites:
  - sprite: Mobs/Species/IPC/parts.rsi
    state: head_m

# Torso

- type: humanoidBaseSprite
  id: MobIPCTorso

- type: humanoidBaseSprite
  id: MobIPCTorsoMale

- type: humanoidBaseSprite
  id: MobIPCTorsoFemale

- type: marking
  id: MobIPCTorsoDefault
  bodyPart: Chest
  markingCategory: Chest
  speciesRestriction: [ IPC ]
  sprites:
  - sprite: Mobs/Species/IPC/parts.rsi
    state: torso_m

- type: marking
  id: MobIPCTorsoFemaleDefault
  bodyPart: Chest
  markingCategory: Chest
  speciesRestriction: [ IPC ]
  sprites:
  - sprite: Mobs/Species/IPC/parts.rsi
    state: torso_f

# Left Leg

- type: humanoidBaseSprite
  id: MobIPCLLeg

- type: marking
  id: MobIPCLLegDefault
  bodyPart: LLeg
  markingCategory: LeftLeg
  speciesRestriction: [ IPC ]
  sprites:
  - sprite: Mobs/Species/IPC/parts.rsi
    state: l_leg

# Left Arm

- type: humanoidBaseSprite
  id: MobIPCLArm

- type: marking
  id: MobIPCLArmDefault
  bodyPart: LArm
  markingCategory: LeftArm
  speciesRestriction: [ IPC ]
  sprites:
  - sprite: Mobs/Species/IPC/parts.rsi
    state: l_arm

#LHand

- type: humanoidBaseSprite
  id: MobIPCLHand

- type: marking
  id: MobIPCLHandDefault
  bodyPart: LHand
  markingCategory: LeftHand
  speciesRestriction: [ IPC ]
  sprites:
  - sprite: Mobs/Species/IPC/parts.rsi
    state: l_hand

#LFoot

- type: humanoidBaseSprite
  id: MobIPCLFoot

- type: marking
  id: MobIPCLFootDefault
  bodyPart: LFoot
  markingCategory: LeftFoot
  speciesRestriction: [ IPC ]
  sprites:
  - sprite: Mobs/Species/IPC/parts.rsi
    state: l_foot

#RLeg

- type: humanoidBaseSprite
  id: MobIPCRLeg

- type: marking
  id: MobIPCRLegDefault
  bodyPart: RLeg
  markingCategory: RightLeg
  speciesRestriction: [ IPC ]
  sprites:
  - sprite: Mobs/Species/IPC/parts.rsi
    state: r_leg

#RArm

- type: humanoidBaseSprite
  id: MobIPCRArm

- type: marking
  id: MobIPCRArmDefault
  bodyPart: RArm
  markingCategory: RightArm
  speciesRestriction: [ IPC ]
  sprites:
  - sprite: Mobs/Species/IPC/parts.rsi
    state: r_arm

#RHand

- type: humanoidBaseSprite
  id: MobIPCRHand

- type: marking
  id: MobIPCRHandDefault
  bodyPart: RHand
  markingCategory: RightHand
  speciesRestriction: [ IPC ]
  sprites:
  - sprite: Mobs/Species/IPC/parts.rsi
    state: r_hand

#RFoot

- type: humanoidBaseSprite
  id: MobIPCRFoot

- type: marking
  id: MobIPCRFootDefault
  bodyPart: RFoot
  markingCategory: RightFoot
  speciesRestriction: [ IPC ]
  sprites:
  - sprite: Mobs/Species/IPC/parts.rsi
    state: r_foot

- type: Tag
  id: IPCMaskWearable
