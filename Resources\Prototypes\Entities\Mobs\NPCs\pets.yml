# Bounds Guide
#1D2L3U4R

- type: entity
  name: "Іан"
  parent: MobCorgi
  id: MobCorgiIan
  description: "Улюблена домашня тварина коргі."
  components:
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Pets/corgi.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: ian
  - type: DamageStateVisuals
    states:
      Alive:
        Base: ian
      Critical:
        Base: ian_dead
      Dead:
        Base: ian_dead
  - type: Grammar
    attributes:
      proper: true
      gender: male
  - type: Butcherable
    spawned:
    - id: FoodMeatCorgi
      amount: 2
    - id: MaterialHideCorgi
  - type: Tag
    tags:
    - CannotSuicide
    - VimPilot
  - type: StealTarget
    stealGroup: AnimalIan
  - type: LanguageKnowledge
    speaks:
    - Dog
    understands:
    - TauCetiBasic
    - Dog

- type: entity
  name: "Старий Іан"
  parent: MobCorgiIan
  id: MobCorgiIanOld
  description: "Досі улюблений домашній улюбленець коргі. Обожнюю його колеса."
  components:
  - type: Sprite
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: old_ian
  - type: DamageStateVisuals
    states:
      Alive:
        Base: old_ian
      Critical:
        Base: old_ian_dead
      Dead:
        Base: old_ian_dead
  - type: Butcherable
    spawned:
    - id: FoodMeatCorgi
      amount: 2
    - id: SheetSteel1
    - id: MaterialHideCorgi

- type: entity
  name: "Ліза"
  parent: MobCorgiIan
  id: MobCorgiLisa
  description: "Улюблена коргі Іана."
  components:
  - type: Sprite
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: lisa
  - type: DamageStateVisuals
    states:
      Alive:
        Base: lisa
      Critical:
        Base: lisa_dead
      Dead:
        Base: lisa_dead
  - type: Grammar
    attributes:
      proper: true
      gender: female

- type: entity
  name: "Цуценя Іан"
  parent: MobCorgiPuppy
  id: MobCorgiIanPup
  description: "Улюблене цуценя коргі. Оууу."
  components:
  - type: Grammar
    attributes:
      proper: true
      gender: male
  - type: Butcherable # A puppy? You monster...
    spawned:
    - id: FoodMeatCorgi
      amount: 2
    - id: MaterialHideCorgi

- type: entity
  name: "Виконявчик"
  parent: MobCat
  id: MobCatRuntime
  description: "Професійний мисливець на мишей. Художник по втечам."
  components:
  - type: NpcFactionMember
    factions:
      - PetsNT
      - Cat # DeltaV
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
  - type: Grammar
    attributes:
      proper: true
      gender: female
  - type: Tag
    tags:
    - CannotSuicide
    - VimPilot
  - type: LanguageKnowledge
    speaks:
    - Cat
    understands:
    - TauCetiBasic
    - Cat

- type: entity
  name: "Виняткун"
  id: MobCatException
  parent: MobCatCalico
  description: "Попросіть ввічливо, і, можливо, вони віддадуть вам одне зі своїх вільних життів."
  components:
  - type: NpcFactionMember
    factions:
      - PetsNT
      - Cat # DeltaV
  - type: Grammar
    attributes:
      proper: true
      gender: male
  - type: Tag
    tags:
    - CannotSuicide
    - VimPilot
  - type: LanguageKnowledge
    speaks:
    - Cat
    understands:
    - TauCetiBasic
    - Cat

- type: entity
  name: "Вусо-Лапо-Хвіст"
  id: MobCatFloppa
  parent: MobCatCaracal
  description: "Він тут. Його ім'я означає щось стародавнє"
  components:
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 40
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Grammar
    attributes:
      gender: male
  - type: Tag
    tags:
    - CannotSuicide
    - VimPilot

- type: entity
  name: "Бандит"
  parent: MobFerret
  id: MobBandito
  description: "Просто дурний хлопчисько!"
  components:
  - type: Grammar
    attributes:
      proper: true
      gender: male
  - type: Tag
    tags:
    - CannotSuicide
    - VimPilot

- type: entity
  name: "Бінгус"
  parent: SimpleMobBase
  id: MobBingus
  description: "Бінгус, коханий мій..."
  components:
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Pets/bingus.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: bingus
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 12
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Inventory
    speciesId: cat
    templateId: pet
  - type: InventorySlots
  - type: Strippable
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
  - type: DamageStateVisuals
    states:
      Alive:
        Base: bingus
      Critical:
        Base: bingus_dead
      Dead:
        Base: bingus_dead
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 2
  - type: InteractionPopup
    successChance: 0.9
    interactSuccessString: petting-success-bingus
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/cat_meow.ogg
  - type: Grammar
    attributes:
      proper: true
      gender: epicene
  - type: Tag
    tags:
    - CannotSuicide
    - VimPilot
  - type: StealTarget
    stealGroup: AnimalBingus

- type: entity
  name: "МакГріфф"
  parent: SimpleMobBase
  id: MobMcGriff
  description: "Цей собака відчуває, що тут чимось пахне, і це щось - ЗЛОЧИН!"
  components:
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Pets/mcgriff.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: mcgriff
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 20
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Inventory
    speciesId: puppy
    templateId: pet
  - type: InventorySlots
  - type: Strippable
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
  - type: DamageStateVisuals
    states:
      Alive:
        Base: mcgriff
      Critical:
        Base: mcgriff_dead
      Dead:
        Base: mcgriff_dead
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 2
  - type: LanguageKnowledge
    speaks:
    - Dog
    understands:
    - TauCetiBasic
    - Dog
  - type: InteractionPopup
    successChance: 0.5
    interactSuccessString: petting-success-dog
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/small_dog_bark_happy.ogg
  - type: Grammar
    attributes:
      proper: true
      gender: male
  - type: Tag
    tags:
    - CannotSuicide
    - VimPilot
  - type: StealTarget
    stealGroup: AnimalMcGriff
  - type: RandomBark
    barkType: dog
    barkMultiplier: 1.3

- type: entity
  name: "Бібліотекар"
  parent: MobSloth
  id: MobPaperwork
  description: "Отримав нову роботу - сортувати книги в бібліотеці після того, як його перевели з Космічної Станції 13. Схоже, йому це вдається так само повільно."
  components:
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Pets/paperwork.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: paperwork
  - type: DamageStateVisuals
    states:
      Alive:
        Base: paperwork
      Critical:
        Base: paperwork_dead
      Dead:
        Base: paperwork_dead
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 3
  - type: InteractionPopup
    successChance: 1
    interactSuccessString: petting-success-sloth
    interactFailureString: petting-failure-sloth
    interactSuccessSpawn: EffectHearts
  - type: Grammar
    attributes:
      proper: true
      gender: male

- type: entity
  name: "Уолтер"
  parent: SimpleMobBase
  id: MobWalter
  description: "Він любить ліки та геро... Уолтер..."
  components:
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Pets/walter.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: walter
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 50
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Inventory
    speciesId: dog
    templateId: pet
  - type: InventorySlots
  - type: Strippable
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
  - type: DamageStateVisuals
    states:
      Alive:
        Base: walter
      Critical:
        Base: walter_dead
      Dead:
        Base: walter_dead
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 3
  - type: LanguageKnowledge
    speaks:
    - Dog
    understands:
    - TauCetiBasic
    - Dog
  - type: InteractionPopup
    successChance: 0.7
    interactSuccessString: petting-success-dog
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/small_dog_bark_happy.ogg
  - type: Grammar
    attributes:
      proper: true
      gender: male
  - type: Tag
    tags:
    - CannotSuicide
    - VimPilot
  - type: StealTarget
    stealGroup: AnimalWalter
  - type: RandomBark
    barkType: dog
    barkMultiplier: 1.1

- type: entity
  name: "Морті"
  parent: MobPossum
  id: MobPossumMorty
  description: "Мешканець станції, Опосум віргінський. Чутливий, але витривалий хлопець."
  components:
  - type: InteractionPopup
    successChance: 1.0 # Hey, c'mon, this is Morty we're talking about here.
    interactSuccessString: petting-success-possum
    interactFailureString: petting-failure-possum
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/snake_hiss.ogg
  - type: Grammar
    attributes:
      proper: true
      gender: male
  - type: Tag
    tags:
    - CannotSuicide
    - VimPilot
  - type: StealTarget
    stealGroup: AnimalMorty

- type: entity
  name: "Морті"
  parent: MobPossumMorty
  id: MobPossumMortyOld
  suffix: Old sprite
  components:
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Animals/possum_old.rsi
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: possum_old
  - type: DamageStateVisuals
    states:
      Alive:
        Base: possum_old
      Dead:
        Base: possum_dead_old

- type: entity
  name: "Поппі" # the Safety Possum
  parent: MobPossumMorty
  id: MobPossumPoppy
  description: "Це опосум, невеликий сумчастий ссавець. Вона носить відповідні засоби захисту."
  components:
  - type: Grammar
    attributes:
      proper: true
      gender: female
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Animals/possum.rsi
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: poppy
  - type: DamageStateVisuals
    states:
      Alive:
        Base: poppy
      Dead:
        Base: poppy_dead

- type: entity
  name: "Єнотос Педрос"
  parent: MobRaccoon
  id: MobRaccoonMorticia
  description: "Могутнє створіння ночі. Його тіні завжди на місці."
  components:
  - type: InteractionPopup
    successChance: 0.7
    interactSuccessString: petting-success-raccoon
    interactFailureString: petting-failure-raccoon
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/raccoon_chatter.ogg
  - type: Grammar
    attributes:
      proper: true
      gender: female
  - type: Tag
    tags:
    - CannotSuicide
    - VimPilot

- type: entity
  name: "Олександр"
  parent: MobPig
  id: MobAlexander
  description: "Найкращий колега шеф-кухаря."
  components:
  - type: InteractionPopup
    successChance: 1
    interactSuccessString: petting-success-pig
    interactFailureString: petting-failure-pig
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/pig_oink.ogg
  - type: Grammar
    attributes:
      proper: true
      gender: male
  - type: Tag
    tags:
    - CannotSuicide
    - VimPilot

- type: entity
  name: "Рено"
  parent: MobFox
  id: MobFoxRenault
  description: "Надійна лисиця капітана."
  components:
  - type: InteractionPopup
    successChance: 1
    interactSuccessString: petting-success-soft-floofy
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      collection: Fox
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 3
    - id: Telecrystal5
      amount: 1
  - type: Grammar
    attributes:
      proper: true
      gender: female
  - type: Tag
    tags:
    - CannotSuicide
    - VimPilot
  - type: StealTarget
    stealGroup: AnimalRenault
  - type: LanguageKnowledge
    speaks:
    - Fox
    understands:
    - TauCetiBasic
    - Fox

- type: entity
  name: "Омак"
  parent: MobHamster
  id: MobHamsterHamlet
  description: "Буркотливий, милий і пухнастий омак. Не питай його за гриби..."
  components:
  - type: Sprite
    drawdepth: SmallMobs
    sprite: Mobs/Pets/hamlet.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base", "movement"]
      state: hamster-0
  - type: SpriteMovement
    movementLayers:
      movement:
        state: hamster-moving-0
    noMovementLayers:
      movement:
        state: hamster-0
  - type: GhostRole
    makeSentient: true
    allowSpeech: true
    allowMovement: true
    name: ghost-role-information-hamlet-name
    description: ghost-role-information-hamlet-description
  - type: GhostTakeoverAvailable
  - type: InteractionPopup
    successChance: 1
    interactSuccessString: petting-success-hamster
    interactFailureString: petting-failure-hamster
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/fox_squeak.ogg
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 1
  - type: Grammar
    attributes:
      proper: true
      gender: male
  - type: Tag
    tags:
    - CannotSuicide
    - Hamster
    - VimPilot
  - type: LanguageKnowledge
    speaks:
    - Mouse
    understands:
    - TauCetiBasic
    - Mouse

- type: entity
  name: "Бодя"
  parent: MobGiantSpider
  id: MobSpiderShiva
  description: "Перший захисник станції."
  components:
  - type: GhostRole # DeltaV
    makeSentient: true
    allowSpeech: true
    allowMovement: true
    name: ghost-role-information-shiva-name
    description: ghost-role-information-shiva-description
  - type: GhostTakeoverAvailable # DeltaV
  - type: InteractionPopup
    successChance: 0.5 # spider is mean
    interactSuccessString: petting-success-tarantula
    interactFailureString: petting-failure-hamster
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/snake_hiss.ogg
  - type: NpcFactionMember
    factions:
      - PetsNT
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base", "movement"]
      state: shiva
      sprite: Mobs/Pets/shiva.rsi
  - type: SpriteMovement
    movementLayers:
      movement:
        state: shiva-moving
    noMovementLayers:
      movement:
        state: shiva
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
  - type: Physics
  - type: DamageStateVisuals
    states:
      Alive:
        Base: shiva
      Dead:
        Base: shiva_dead
  - type: MobThresholds
    thresholds:
      0: Alive
      150: Dead
  - type: MeleeWeapon
    angle: 0
    animation: WeaponArcBite
    soundHit:
      path: /Audio/Effects/bite.ogg
    damage:
      types:
        Piercing: 8
        Poison: 2 # Sleep poison instead of die poison
  - type: Grammar
    attributes:
      proper: true
      gender: female
  - type: Tag
    tags:
    - CannotSuicide
    - VimPilot
    - DoorBumpOpener
    - FootstepSound
  - type: StealTarget
    stealGroup: AnimalShiva
  - type: SolutionContainerManager
    solutions:
      melee:
        maxVol: 12
  - type: SolutionRegeneration
    solution: melee
    generated:
      reagents:
      - ReagentId: Nocturine
        Quantity: 0.1
  - type: MeleeChemicalInjector
    solution: melee
    transferAmount: 3
  - type: Access
    tags:
    - Security
    - Brig

- type: entity
  name: "Віллоу"
  parent: MobKangaroo
  id: MobKangarooWillow
  description: "Верба - боксерський кенгуру."
  components:
  - type: InteractionPopup
    successChance: 0.8
    interactSuccessString: petting-success-kangaroo
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/kangaroo_grunt.ogg
  - type: Grammar
    attributes:
      proper: true
      gender: female
  - type: Tag
    tags:
    - CannotSuicide
    - DoorBumpOpener
    - FootstepSound
  - type: GhostRole
    prob: 0.25
    name: ghost-role-information-willow-name
    description: ghost-role-information-willow-description
  - type: GhostTakeoverAvailable
  - type: Loadout
    prototypes: [ BoxingKangarooGear ]

- type: entity
  name: "Посміхунчик"
  id: MobSlimesPet
  parent: MobAdultSlimes
  description: "Цей шедевр пройшов через тисячі експериментів. Але це наймиліше створіння у світі. Посміхнися, Слизняку!"
  components:
  - type: Sprite
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: rainbow_baby_slime
    - map: [ "enum.DamageStateVisualLayers.BaseUnshaded" ]
      state: aslime-_3
      shader: unshaded
  - type: MobThresholds
    thresholds:
      0: Alive
      100000000000000000: Dead
  - type: FootstepModifier
    footstepSoundCollection:
      path: /Audio/Effects/Footsteps/slime1.ogg
      params:
        volume: -6
  - type: CanEscapeInventory
  - type: Tag
    tags:
    - FootstepSound
    - CannotSuicide
    - VimPilot
  - type: DamageStateVisuals
    states:
      Alive:
        Base: rainbow_baby_slime
        BaseUnshaded: aslime-_3
      Dead:
        Base: rainbow_baby_slime_dead
  - type: StaminaDamageResistance
    coefficient: 0
  - type: Bloodstream
    bloodReagent: Nothing
    bloodMaxVolume: 50
  - type: Temperature
    heatDamageThreshold: 100000000000
    coldDamageThreshold: -10000000000
  - type: Damageable
    damageContainer: Biological
    damageModifierSet: Smile
  - type: SupermatterImmune
  - type: Puller
    needsHands: false
  - type: MeleeWeapon
    damage:
      types:
        Blunt: 0
  - type: SolutionContainerManager
    solutions:
      melee:
        maxVol: 10
  - type: SolutionRegeneration
    solution: melee
    generated:
      reagents:
      - ReagentId: Omnizine
        Quantity: 0.05
  - type: NoSlip
  - type: MeleeChemicalInjector
    solution: melee
    transferAmount: 0.1
  - type: MultiHandedItem
  - type: Item
    size: Ginormous
  - type: SentienceTarget
    flavorKind: station-event-random-sentience-flavor-slime
  - type: MobPrice
    price: 3000 # it is a truly valuable creature
  - type: GhostRole
    name: ghost-role-information-smile-name
    description: ghost-role-information-smile-description
    rules: ghost-role-information-smile-rules
  - type: Grammar
    attributes:
      proper: true
      gender: female
  - type: InnatePsionicPowers
    powersToAdd:
      - XenoglossyPower
      - TelepathyPower
      - HealingWordPower
      - PowerOverwhelming
  - type: LanguageSpeaker
  - type: UniversalLanguageSpeaker
  - type: SlowOnDamage
    speedModifierThresholds:
      1000000000: 1
  - type: ZombieImmune
  - type: MovementIgnoreGravity
  - type: SpeedModifiedByContactModifier
    walkModifierEffectiveness: 0
    sprintModifierEffectiveness: 0
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.30
        density: 80
        mask:
        - SmallMobMask
        layer:
        - SmallMobLayer
  - type: Storage
    clickInsert: false
    grid:
    - 0,0,1,2
    maxItemSize: Large
    storageInsertSound:
      path: /Audio/Voice/Slime/slime_squish.ogg
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
        ents: []
  - type: UserInterface
    interfaces:
      enum.StorageUiKey.Key:
        type: StorageBoundUserInterface
      enum.StoreUiKey.Key:
        type: StoreBoundUserInterface
    
- type: entity
  name: "Пун-Пун"
  parent: MobMonkey
  id: MobMonkeyPunpun
  description: "Яскравий представник мавп з необмеженим доступом до алкоголю."
  components:
  - type: GhostTakeoverAvailable
  - type: Butcherable
    butcheringType: Spike
    spawned:
    - id: FoodMeat
      amount: 3
    - id: DrinkTequilaBottleFull
      amount: 1
  - type: Flashable
  - type: Tag
    tags:
    - CannotSuicide
    - DoorBumpOpener
    - VimPilot
  - type: Loadout
    prototypes: [ MobMonkeyGear ]
  - type: Grammar
    attributes:
      proper: true
      gender: male
  - type: LanguageKnowledge
    speaks:
    - Monkey
    understands:
    - TauCetiBasic
    - Monkey
    - Kobold
  - type: Punpun

- type: entity
  name: "Містер Крабс"
  parent: MobCrab
  id: MobCrabAtmos
  description: "Благородний і стійкий захисник Атмосії. Віва!"
  components:
#  - type: GhostRole
#    prob: 1
#    makeSentient: true
#    allowSpeech: true
#    allowMovement: true
#    name: ghost-role-information-tropico-name
#    description: ghost-role-information-tropico-description
#  - type: GhostTakeoverAvailable
#  - type: Flashable
  - type: Tag
    tags:
    - VimPilot
    - CannotSuicide
  - type: Grammar
    attributes:
      proper: true
      gender: male
#  - type: AlwaysRevolutionaryConvertible
  - type: StealTarget
    stealGroup: AnimalTropico
  - type: LanguageKnowledge
    speaks:
    - Crab
    understands:
    - TauCetiBasic
    - Crab
