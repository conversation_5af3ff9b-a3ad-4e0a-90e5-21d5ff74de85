# Base

- type: entity
  parent: BaseItem
  id: BaseToolSurgery
  abstract: true
  components:
  - type: Sprite
  - type: StaticPrice
    price: 60
  - type: Tag
    tags:
    - SurgeryTool
  - type: SurgeryTool

# Cautery

- type: entity
  name: "термокаутер"
  id: Cautery
  parent: BaseToolSurgery
  description: "Хірургічний інструмент, що використовується для припікання відкритих ран."
  components:
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/cautery.rsi
    state: cautery
  - type: Item
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/cautery.rsi
    storedRotation: 90
  - type: MeleeWeapon
    damage:
       types:
        Heat: 5
    soundHit:
      path: /Audio/Effects/lightburn.ogg
  # Shitmed Change
  - type: DamageOtherOnHit
  - type: ThrowingAngle
    angle: 45
  - type: SurgeryTool
    startSound:
      path: /Audio/_Shitmed/Medical/Surgery/cautery1.ogg
    endSound:
      path: /Audio/_Shitmed/Medical/Surgery/cautery2.ogg
  - type: Cautery

# Drill

- type: entity
  name: "дриль"
  id: Drill
  parent: BaseToolSurgery
  description: "Хірургічне свердло для виконання отворів у твердих матеріалах."
  components:
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/drill.rsi
    state: drill
  - type: Item
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/drill.rsi
    shape:
    - 0,0,1,0
    - 1,1,1,1
  - type: MeleeWeapon
    attackRate: 1.25
    range: 1.4
    damage:
      types:
        Piercing: 8
    heavyRateModifier: 1.5
    heavyRangeModifier: 1.2
    heavyDamageBaseModifier: 1.5
    heavyStaminaCost: 2.5
    maxTargets: 1
    angle: 20
    soundHit:
      path: /Audio/Items/drill_hit.ogg
  - type: DamageOtherOnHit
    damage:
      types:
        Piercing: 11
    staminaCost: 8
  - type: EmbeddableProjectile
  - type: EmbedPassiveDamage
  - type: ThrowingAngle
    angle: 90
  - type: StaticPrice
    price: 40
  # Shitmed Change
  - type: SurgeryTool
    startSound:
      path: /Audio/_Shitmed/Medical/Surgery/saw.ogg
  - type: Drill

# Scalpel

- type: entity
  name: "скальпель"
  id: Scalpel
  parent: BaseToolSurgery
  description: "Хірургічний інструмент, за допомогою якого роблять розрізи плоті."
  components:
  - type: Sharp
    butcherDelayModifier: 1.5 # Butchering with a scalpel, regardless of the type, will take 50% longer
  - type: Tool
    qualities:
    - Slicing
    #speedModifier: 0.66 # pretend the sixes go on forever :)
  - type: Utensil
    types:
      - Knife
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/scalpel.rsi
    state: scalpel
  - type: Item
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/scalpel.rsi
    storedRotation: 90
  - type: MeleeWeapon
    wideAnimationRotation: 90
    swingLeft: true
    attackRate: .8
    range: 1.4
    damage:
      types:
        Slash: 7.5
    heavyRateModifier: 1.25
    heavyRangeModifier: 1.2
    heavyDamageBaseModifier: 1.25
    heavyStaminaCost: 2.5
    maxTargets: 1
    angle: 20
    soundHit:
      path: /Audio/Weapons/bladeslice.ogg
  - type: DamageOtherOnHit
    staminaCost: 5
  - type: EmbeddableProjectile
  - type: EmbedPassiveDamage
  - type: ThrowingAngle
    angle: 90
  # Shitmed Change
  - type: SurgeryTool
    startSound:
      path: /Audio/_Shitmed/Medical/Surgery/scalpel1.ogg
    endSound:
      path: /Audio/_Shitmed/Medical/Surgery/scalpel2.ogg
  - type: Scalpel
  - type: Tag
    tags:
    - SurgeryTool
    - Knife

- type: entity
  name: "заточка"
  id: ScalpelShiv
  parent: Scalpel
  description: "Загострений шматок скла, відшліфований до краю і обмотаний скотчем для ручки." # Could become a decent tool or weapon with right tool mods.
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/Surgery/scalpel.rsi
    state: shiv
  - type: Item
    sprite: Objects/Specific/Medical/Surgery/scalpel.rsi
    storedRotation: 90
    heldPrefix: shiv

- type: entity
  name: "вдосконалений скальпель"
  id: ScalpelAdvanced
  parent: Scalpel
  description: "Виготовлені з дорожчих матеріалів, гостріші та загалом надійніші."
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/Surgery/scalpel.rsi
    state: advanced
  - type: Item
    sprite: Objects/Specific/Medical/Surgery/scalpel.rsi
    storedRotation: 90
    heldPrefix: advanced
  - type: MeleeWeapon
    damage:
      types:
        Slash: 8
  - type: Scalpel # Shitmed
    speed: 1.25
  - type: Tag
    tags:
    - Knife

- type: entity
  name: "лазерний скальпель"
  id: ScalpelLaser
  parent: Scalpel
  description: "Скальпель, який використовує спрямований лазер для розрізу замість леза, для більш точної хірургії, а також припікає під час розрізу."
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/Surgery/scalpel.rsi
    state: laser
  - type: Item
    sprite: Objects/Specific/Medical/Surgery/scalpel.rsi
    storedRotation: 90
    heldPrefix: laser
  - type: MeleeWeapon
    damage:
      types:
        Slash: 6.5
        Heat: 1
  - type: Scalpel
    speed: 1.25 # These shouldnt be obtainable but yknow they are better.
  - type: Cautery
    speed: 1.25
  - type: Tag
    tags:
    - Knife

# Retractor

- type: entity
  name: "ретрактор"
  id: Retractor
  parent: BaseToolSurgery
  description: "Хірургічний інструмент, що використовується для утримання відкритих розрізів."
  components:
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/retractor.rsi
    state: retractor
  - type: Item
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/retractor.rsi
    storedRotation: 90
  # Shitmed Change
  - type: MeleeWeapon
    wideAnimationRotation: 90
    attackRate: 1.1
    range: 1.4
    damage:
      types:
        Slash: 3.5
        Blunt: 3.0
    heavyRateModifier: 1.25
    heavyDamageBaseModifier: 1.75
    heavyStaminaCost: 2.5
    maxTargets: 1
    angle: 20
    soundHit:
      path: /Audio/Weapons/bladeslice.ogg
  - type: DamageOtherOnHit
    staminaCost: 5
  - type: ThrowingAngle
    angle: 315
  - type: EmbeddableProjectile
  - type: EmbedPassiveDamage
  - type: SurgeryTool
    startSound:
      path: /Audio/_Shitmed/Medical/Surgery/retractor1.ogg
    endSound:
      path: /Audio/_Shitmed/Medical/Surgery/retractor2.ogg
  - type: Retractor

# Hemostat

- type: entity
  name: "гемостат"
  id: Hemostat
  parent: BaseToolSurgery # Shitmed Change
  description: "Хірургічний інструмент, що використовується для стискання кровоносних судин для запобігання кровотечі."
  components:
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/hemostat.rsi # Shitmed Change
    state: hemostat
  - type: Item
    heldPrefix: hemostat
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/hemostat.rsi # Shitmed Change
    storedRotation: 90
  - type: MeleeWeapon
    wideAnimationRotation: 90
    attackRate: 1.1
    range: 1.4
    damage:
      types:
        Slash: 6
    heavyRateModifier: 1.25
    heavyDamageBaseModifier: 1.25
    heavyStaminaCost: 2.5
    maxTargets: 2
    angle: 40
    soundHit:
      path: /Audio/Weapons/bladeslice.ogg
  - type: DamageOtherOnHit
    staminaCost: 5
  - type: EmbeddableProjectile
  - type: EmbedPassiveDamage
  - type: ThrowingAngle
    angle: 35
  # Shitmed Change
  - type: SurgeryTool
    startSound:
      path: /Audio/_Shitmed/Medical/Surgery/retractor1.ogg
    endSound:
      path: /Audio/_Shitmed/Medical/Surgery/hemostat1.ogg
  - type: Hemostat
  - type: Tweezers # Shitmed
  - type: Tending # Shitmed

# Bone setter - Shitmed Change
- type: entity
  parent: BaseToolSurgery
  id: Bonesetter
  name: "кісткоутворювач"
  description: "Використовується для вправлення кісток на місце."
  components:
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/bonesetter.rsi
    state: bonesetter
  - type: Item
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/bonesetter.rsi
  - type: BoneSetter
  - type: SurgeryTool
    startSound:
      path: /Audio/_Shitmed/Medical/Surgery/bone_setter.ogg

# Bone Gel - Shitmed Change
- type: entity
  parent: BaseToolSurgery
  id: BoneGel
  name: "пляшка гелю для кісток"
  description: "Контейнер для кісткового гелю, який часто потрібно поповнювати зі спеціалізованої машини."
  components:
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/bone-gel.rsi
    state: bone-gel
  - type: BoneGel

# Saws

- type: entity
  name: "пила по металу"
  id: Saw
  parent: BaseToolSurgery
  description: "Для розпилювання дерева та інших предметів на шматки. Або розпилювання кісток, у разі потреби."
  components:
  - type: Sharp
  - type: Utensil
    types:
      - Knife
  - type: Sprite
    sprite: Objects/Specific/Medical/Surgery/saw.rsi
    state: saw
  - type: Item
    size: Normal
    sprite: Objects/Specific/Medical/Surgery/saw.rsi
    storedRotation: 90
  - type: Tool
    qualities:
      - Sawing
    speedModifier: 1.0
  - type: MeleeWeapon
    attackRate: 1.3
    range: 1.35
    damage:
      types:
        Blunt: 2.5
        Slash: 6.5
    heavyRateModifier: 3
    heavyDamageBaseModifier: 2.0
    heavyStaminaCost: 20
    maxTargets: 8
    angle: 20
    soundHit:
      path: /Audio/Weapons/bladeslice.ogg
  - type: DamageOtherOnHit
    staminaCost: 10
  - type: EmbeddableProjectile
  - type: EmbedPassiveDamage
  - type: ThrowingAngle
    angle: 0
  # Shitmed Change
  - type: BoneSaw
  - type: SurgeryTool
    startSound:
      path: /Audio/_Shitmed/Medical/Surgery/saw.ogg

- type: entity
  name: "choppa"
  id: SawImprov
  parent: Saw
  description: "Злісне зубчасте лезо, зроблене з будь-яких неприємних гострих предметів, які ви могли знайти." # It would make a pretty decent weapon, given there are more space for some tool mods too.
  components:
  - type: Sprite
    state: improv
  - type: Item
    size: Small
    heldPrefix: improv
  - type: MeleeWeapon
    attackRate: 1.1
    damage:
      types:
        Blunt: 3
        Slash: 7
    bluntStaminaDamageFactor: 3
    heavyRateModifier: 1.25
    heavyDamageBaseModifier: 1.25
    heavyStaminaCost: 20
    maxTargets: 8
    angle: 20
    soundHit:
      path: /Audio/Weapons/bladeslice.ogg
  - type: ThrowingAngle
    angle: 90
  - type: Tool
    qualities:
      - Sawing
    speedModifier: 0.5
  - type: BoneSaw # Shitmed
    speed: 0.5

- type: entity
  name: "циркулярна пила"
  id: SawElectric
  parent: Saw
  description: "Для різання у важких умовах."
  components:
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/circular-saw.rsi
    state: circular-saw
  - type: Item
    size: Normal
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/circular-saw.rsi
    storedRotation: 90
  - type: MeleeWeapon
    attackRate: .8
    range: 1.4
    bluntStaminaDamageFactor: 3.0
    damage:
      types:
        Blunt: 4.5
        Slash: 5.5
    heavyRateModifier: 2
    heavyRangeModifier: 1.2
    heavyDamageBaseModifier: 1
    heavyStaminaCost: 10
    maxTargets: 8
    angle: 360
    soundHit:
      path: /Audio/Items/drill_hit.ogg
  - type: DamageOtherOnHit
    damage:
      types:
        Slash: 10
    staminaCost: 14
  - type: ThrowingAngle
    angle: 90
  - type: Tool
    qualities:
      - Sawing
    speedModifier: 1.5
  - type: BoneSaw # Shitmed
    speed: 1.5
  - type: SurgeryTool
    startSound:
      path: /Audio/_Shitmed/Medical/Surgery/saw.ogg

- type: entity
  name: "вдосконалена циркулярна пила"
  id: SawAdvanced
  parent: SawElectric
  description: "Ти думаєш, що можеш вирізати все, що завгодно."
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/Surgery/saw.rsi
    state: advanced
  - type: Item
    size: Normal
    sprite: Objects/Specific/Medical/Surgery/saw.rsi
    storedRotation: 90
    heldPrefix: advanced
  - type: MeleeWeapon
    attackRate: .8
    range: 1.4
    bluntStaminaDamageFactor: 5.0
    damage:
      types:
        Blunt: 4.5
        Slash: 7.5
    heavyRateModifier: 2
    heavyRangeModifier: 1.2
    heavyDamageBaseModifier: 1
    heavyStaminaCost: 10
    maxTargets: 8
    angle: 360
    soundHit:
      path: /Audio/Items/drill_hit.ogg
  - type: DamageOtherOnHit
    damage:
      types:
        Slash: 12
    staminaCost: 14
  - type: ThrowingAngle
    angle: 90
  - type: Tool
    qualities:
      - Sawing
    speedModifier: 2.0
  - type: BoneSaw # Shitmed
    speed: 2

# Shitmed Tools

- type: entity
  name: "інструмент для припікання"
  id: EnergyCautery
  parent: Cautery
  description: "Припікач з енергетичним наконечником також слугує дрилем у ввімкненому стані."
  components:
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/e-cautery.rsi
    state: e-cautery-on
  - type: Item
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/e-cautery.rsi
    inhandVisuals:
      left:
      - state: on-inhand-left
      right:
      - state: on-inhand-right
  - type: SurgeryTool
    startSound:
      path: /Audio/_Shitmed/Medical/Surgery/cautery1.ogg
    endSound:
      path: /Audio/_Shitmed/Medical/Surgery/cautery2.ogg
  - type: MeleeWeapon
    damage:
      types:
        Piercing: 10
        Heat: 1
  - type: PhysicalComposition #Goobstation - Recycle update
    materialComposition:
      Steel: 150
      Glass: 37
      Plasma: 37
  - type: ItemSwitch
    showLabel: true
    state: cautery
    states:
      cautery: !type:ItemSwitchState
        verb: cautery
        sprite:
          sprite: _Shitmed/Objects/Specific/Medical/Surgery/e-cautery.rsi
          state: e-cautery
        components:
          - type: Cautery
            speed: 1.5
        soundStateActivate:
          collection: WelderOn
          params:
            variation: 0.125
            volume: -5

      drill: !type:ItemSwitchState
        verb: drill
        sprite:
          sprite: _Shitmed/Objects/Specific/Medical/Surgery/e-cautery.rsi
          state: e-cautery-on
        components:
          - type: Drill
            speed: 1.5
        soundStateActivate:
          collection: sparks
          params:
            variation: 0.125
            volume: -5

- type: entity
  name: "енергетичний скальпель"
  id: EnergyScalpel
  parent: Scalpel
  description: "Скальпель, який використовує енергетичне лезо, також слугує пилкою в увімкненому стані."
  components:
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/e-scalpel.rsi
    state: e-scalpel-on
  - type: Item
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/e-scalpel.rsi
    inhandVisuals:
      left:
      - state: on-inhand-left
      right:
      - state: on-inhand-right
  - type: SurgeryTool
    startSound:
      path: /Audio/_Shitmed/Medical/Surgery/scalpel1.ogg
    endSound:
      path: /Audio/_Shitmed/Medical/Surgery/scalpel2.ogg
  - type: MeleeWeapon
    damage:
      types:
        Slash: 10
        Heat: 1
  - type: PhysicalComposition #Goobstation - Recycle update
    materialComposition:
      Steel: 150
      Glass: 37
      Gold: 37
  - type: ItemSwitch
    showLabel: true
    state: scalpel
    states:
      scalpel: !type:ItemSwitchState
        verb: scalpel
        sprite:
          sprite: _Shitmed/Objects/Specific/Medical/Surgery/e-scalpel.rsi
          state: e-scalpel
        components:
          - type: Scalpel
            speed: 1.5
        soundStateActivate:
          collection: sparks
          params:
            variation: 0.125
            volume: -5

      saw: !type:ItemSwitchState
        verb: saw
        sprite:
          sprite: _Shitmed/Objects/Specific/Medical/Surgery/e-scalpel.rsi
          state: e-scalpel-on
        components:
          - type: BoneSaw
            speed: 1.5
        soundStateActivate:
          collection: sparks
          params:
            variation: 0.125
            volume: -5

- type: entity
  name: "механічні защемлення"
  id: AdvancedRetractor
  parent: Retractor
  description: "Ретрактор з механічними зажимами також виконує функцію гемостату у включеному стані."
  components:
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/adv-retractor.rsi
    state: adv-retractor-on
  - type: Item
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/adv-retractor.rsi
    inhandVisuals:
      left:
      - state: on-inhand-left
      right:
      - state: on-inhand-right
  - type: SurgeryTool
    startSound:
      path: /Audio/_Shitmed/Medical/Surgery/retractor1.ogg
    endSound:
      path: /Audio/_Shitmed/Medical/Surgery/retractor2.ogg
  - type: MeleeWeapon
    damage:
      types:
        Slash: 6.5
        Heat: 1
  - type: PhysicalComposition #Goobstation - Recycle update
    materialComposition:
      Steel: 150
      Glass: 37
      Silver: 37
  - type: ItemSwitch
    showLabel: true
    state: retractor
    states:
      retractor: !type:ItemSwitchState
        verb: retractor
        sprite:
          sprite: _Shitmed/Objects/Specific/Medical/Surgery/adv-retractor.rsi
          state: adv-retractor
        components:
          - type: Retractor
            speed: 1.5
        soundStateActivate:
          collection: sparks
          params:
            variation: 0.125
            volume: -5

      hemostat: !type:ItemSwitchState
        verb: hemostat
        sprite:
          sprite: _Shitmed/Objects/Specific/Medical/Surgery/adv-retractor.rsi
          state: adv-retractor-on
        components:
          - type: Hemostat
            speed: 1.5
          - type: Tweezers
            speed: 1.5
          - type: Tending
            speed: 1.5
        soundStateActivate:
          collection: sparks
          params:
            variation: 0.125
            volume: -5

- type: entity
  name: "медичний мультитул"
  description: "Складний, вдосконалений медичний мультитул, розроблений Nanotrasen для прискорення операцій, а також для економії місця."
  id: OmnimedTool
  parent: BaseToolSurgery
  components:
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/omnimed.rsi
    state: omnimed
  - type: Item
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/omnimed.rsi
  - type: SurgeryTool
    startSound:
      path: /Audio/_Shitmed/Medical/Surgery/saw.ogg
  - type: Hemostat
    speed: 2
  - type: Scalpel
    speed: 2
  - type: Drill
    speed: 2
  - type: BoneSetter
    speed: 2
  - type: Retractor
    speed: 2
  - type: Cautery
    speed: 2
  - type: BoneGel
    speed: 2
  - type: BoneSaw
    speed: 2
  - type: Tweezers
    speed: 2
  - type: Tending
    speed: 2

- type: entity
  parent: OmnimedTool
  id: OmnimedToolSyndie
  components:
  - type: Sprite
    state: evil
  - type: Item
    size: Small
    heldPrefix: evil

- type: entity
  name: "Прототипний мультитул ГМЛ"
  description: "Прототипний медичний мультитул, розроблений Nanotrasen для економії цінного місця в рюкзаках високопоставлених медичних офіцерів."
  id: OmnimedToolLite
  parent: OmnimedTool
  components:
    - type: Sprite
      state: cmotool
    - type: Item
      heldPrefix: cmotool
    - type: SurgeryTool
    - type: Hemostat
      speed: 1.25
    - type: Scalpel
      speed: 1.25
    - type: Drill
      speed: 1.25
    - type: BoneSetter
      speed: 1.25
    - type: Retractor
      speed: 1.25
    - type: Cautery
      speed: 1.25
    - type: BoneGel
      speed: 1.25
    - type: BoneSaw
      speed: 1.25
    - type: Tweezers
      speed: 1.25
    - type: Tending
      speed: 1.25
