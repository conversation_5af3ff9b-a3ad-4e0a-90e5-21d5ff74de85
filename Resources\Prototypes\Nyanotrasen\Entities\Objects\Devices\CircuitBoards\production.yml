- type: entity
  id: ReverseEngineeringMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "реінжиніринг верстата плата верстата"
  description: "Друкована плата для машини зворотного інжинірингу"
  components:
    - type: Sprite
      state: engineering
    - type: MachineBoard
      prototype: ReverseEngineeringMachine
      requirements:
        MatterBin: 1
        Manipulator: 1
      materialRequirements:
        Cable: 1
        PlasmaGlass: 5
      tagRequirements:
        BorgArm:
          Amount: 3
          DefaultPrototype: LeftArmBorg
          ExamineName: borg arm
    - type: ReverseEngineering
      difficulty: 3
      recipes:
        - ReverseEngineeringMachineCircuitboard

- type: entity
  id: DeepFryerMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата фритюрниці"
  components:
    - type: Sprite
      state: service
    - type: MachineBoard
      prototype: KitchenDeepFryer
      requirements:
        Capacitor: 1
        MatterBin: 1
      materialRequirements:
        Steel: 4
        Glass: 2
        Cable: 4
    - type: ReverseEngineering
      recipes:
        - DeepFryerMachineCircuitboard

- type: entity
  id: MailTeleporterMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "машинна плата поштового телепорту"
  components:
    - type: Sprite
      state: supply
    - type: MachineBoard
      prototype: MailTeleporter
      requirements:
        Capacitor: 1
        MatterBin: 1
      materialRequirements:
        Steel: 4
        Cable: 4
