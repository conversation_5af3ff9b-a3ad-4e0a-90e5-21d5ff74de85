﻿<controls:FancyWindow xmlns="https://spacestation14.io"
                      xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
                      xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                      Title="{Loc 'bounty-console-menu-title'}"
                      SetSize="550 420"
                      MinSize="400 350">
    <BoxContainer Orientation="Vertical"
                  VerticalExpand="True"
                  HorizontalExpand="True">
        <PanelContainer VerticalExpand="True" HorizontalExpand="True" Margin="10">
            <PanelContainer.PanelOverride>
                <gfx:StyleBoxFlat BackgroundColor="#1B1B1E" />
            </PanelContainer.PanelOverride>
            <ScrollContainer HScrollEnabled="False"
                             HorizontalExpand="True"
                             VerticalExpand="True">
                <BoxContainer Name="BountyEntriesContainer"
                              Orientation="Vertical"
                              VerticalExpand="True"
                              HorizontalExpand="True">
                </BoxContainer>
            </ScrollContainer>
        </PanelContainer>
        <!-- Footer -->
        <BoxContainer Orientation="Vertical">
            <PanelContainer StyleClasses="LowDivider" />
            <BoxContainer Orientation="Horizontal" Margin="10 2 5 0" VerticalAlignment="Bottom">
                <Label Text="{Loc 'bounty-console-flavor-left'}" StyleClasses="WindowFooterText" />
                <Label Text="{Loc 'bounty-console-flavor-right'}" StyleClasses="WindowFooterText"
                       HorizontalAlignment="Right" HorizontalExpand="True"  Margin="0 0 5 0" />
                <TextureRect StyleClasses="NTLogoDark" Stretch="KeepAspectCentered"
                             VerticalAlignment="Center" HorizontalAlignment="Right" SetSize="19 19"/>
            </BoxContainer>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
