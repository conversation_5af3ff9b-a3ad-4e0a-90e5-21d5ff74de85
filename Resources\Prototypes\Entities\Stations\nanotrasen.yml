﻿- type: entity
  abstract: true
  id: BaseStationNanotrasen
  components:
    - type: NpcFactionMember
      factions:
        - NanoTrasen

- type: entity
  id: StandardNanotrasenStation
  parent:
    - BaseStation
    - BaseStationNews
    - BaseStationCargo
    - BaseStationJobsSpawning
    - BaseStationRecords
    - BaseStationArrivals
    - BaseStationGateway
    - BaseStationShuttles
    - BaseStationCentcomm
    - BaseStationEvacuation
    - BaseStationAlertLevels
    - BaseStationMagnet
    - BaseStationExpeditions
    - BaseStationSiliconLawAsimov
    - BaseStationAllEventsEligible
    - BaseStationNanotrasen
    - BaseRandomStation
    - BaseStationMail # Nyano component, required for station mail to function
    - BaseStationCaptainState # DeltaV
    - BaseStationStockMarket # DeltaV
    - BaseStationWizardTarget # Goobstation
  categories: [ HideSpawnMenu ]
  components:
    - type: Transform

- type: entity
  id: NanotrasenCentralCommand
  parent:
    - BaseStation
    - BaseStationAlertLevels
    - BaseStationNanotrasen
  categories: [ HideSpawnMenu ]
  components:
    - type: Transform

- type: entity
  id: StandardStationArena
  parent:
  - BaseStation
  - BaseStationJobsSpawning
  - BaseStationRecords
  - BaseStationNanotrasen
  categories: [ HideSpawnMenu ]
  components:
    - type: Transform

- type: entity
  id: StandardNanotrasenStationNoATS
  parent:
    - BaseStation
    - BaseStationNews
    - BaseStationCargo
    - BaseStationJobsSpawning
    - BaseStationRecords
    - BaseStationArrivals
    - BaseStationGateway
    - BaseStationShuttlesSalvageOnly
    - BaseStationCentcomm
    - BaseStationEvacuation
    - BaseStationAlertLevels
    - BaseStationMagnet
    - BaseStationExpeditions
    - BaseStationSiliconLawAsimov
    - BaseStationAllEventsEligible
    - BaseStationNanotrasen
    - BaseRandomStation
    - BaseStationMail
  categories: [ HideSpawnMenu ]
  components:
    - type: Transform

- type: entity
  id: StandardNanotrasenStationNoShuttles
  parent:
    - BaseStation
    - BaseStationNews
    - BaseStationCargo
    - BaseStationJobsSpawning
    - BaseStationRecords
    - BaseStationArrivals
    - BaseStationGateway
    - BaseStationCentcomm
    - BaseStationEvacuation
    - BaseStationAlertLevels
    - BaseStationMagnet
    - BaseStationExpeditions
    - BaseStationSiliconLawAsimov
    - BaseStationAllEventsEligible
    - BaseStationNanotrasen
    - BaseRandomStation
    - BaseStationMail
  categories: [HideSpawnMenu]
  components:
    - type: Transform

- type: entity
  id: StandardNanotrasenStationCargoOnly
  parent:
    - BaseStation
    - BaseStationNews
    - BaseStationCargo
    - BaseStationJobsSpawning
    - BaseStationRecords
    - BaseStationArrivals
    - BaseStationGateway
    - BaseStationShuttlesCargoOnly
    - BaseStationCentcomm
    - BaseStationEvacuation
    - BaseStationAlertLevels
    - BaseStationMagnet
    - BaseStationExpeditions
    - BaseStationSiliconLawAsimov
    - BaseStationAllEventsEligible
    - BaseStationNanotrasen
    - BaseRandomStation
    - BaseStationMail
  categories: [HideSpawnMenu]
  components:
    - type: Transform
