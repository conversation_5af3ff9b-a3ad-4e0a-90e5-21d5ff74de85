# White Jumpsuit
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitColorWhite
  name: "білий комбінезон"
  description: "Універсальний білий комбінезон без розпізнавальних знаків."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    layers:
    - state: icon
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
      - state: trinkets-equipped-INNERCLOTHING

# Grey Jumpsuit
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitColorGrey
  name: "сірий комбінезон"
  description: "Сірий комбінезон зі смаком нагадує про старі добрі часи."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    layers:
    - state: icon
      color: "#b3b3b3"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#b3b3b3"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#b3b3b3"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#b3b3b3"
      - state: trinkets-equipped-INNERCLOTHING

# Black Jumpsuit
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitColorBlack
  name: "чорний комбінезон"
  description: "Універсальний чорний комбінезон без розпізнавальних знаків."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    layers:
    - state: icon
      color: "#3f3f3f"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#3f3f3f"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#3f3f3f"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#3f3f3f"
      - state: trinkets-equipped-INNERCLOTHING

# Blue Jumpsuit
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitColorBlue
  name: "синій комбінезон"
  description: "Універсальний синій комбінезон без розпізнавальних знаків."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    layers:
    - state: icon
      color: "#52aecc"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#52aecc"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#52aecc"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#52aecc"
      - state: trinkets-equipped-INNERCLOTHING

# Dark Blue Jumpsuit
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitColorDarkBlue
  name: "темно-синій комбінезон"
  description: "Універсальний темно-синій комбінезон без розпізнавальних знаків."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    layers:
    - state: icon
      color: "#3285ba"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#3285ba"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#3285ba"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#3285ba"
      - state: trinkets-equipped-INNERCLOTHING

# Teal Jumpsuit
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitColorTeal
  name: "чироковий комбінезон"
  description: "Універсальний комбінезон бірюзового кольору без маркування звання."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    layers:
    - state: icon
      color: "#77f3b7"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#77f3b7"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#77f3b7"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#77f3b7"
      - state: trinkets-equipped-INNERCLOTHING

# Green Jumpsuit
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitColorGreen
  name: "зелений комбінезон"
  description: "Загальний зелений комбінезон без розпізнавальних знаків."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    layers:
    - state: icon
      color: "#9ed63a"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#9ed63a"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#9ed63a"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#9ed63a"
      - state: trinkets-equipped-INNERCLOTHING

 # Dark Green Jumpsuit
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitColorDarkGreen
  name: "темно-зелений комбінезон"
  description: "Універсальний темно-зелений комбінезон без розпізнавальних знаків."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    layers:
    - state: icon
      color: "#79CC26"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#79CC26"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#79CC26"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#79CC26"
      - state: trinkets-equipped-INNERCLOTHING

# Orange Jumpsuit
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitColorOrange
  name: "помаранчевий комбінезон"
  description: "Не носіть це поруч з параноїдальними офіцерами безпеки."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    layers:
    - state: icon
      color: "#ff8c19"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ff8c19"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#ff8c19"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ff8c19"
      - state: trinkets-equipped-INNERCLOTHING

# Pink Jumpsuit
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitColorPink
  name: "рожевий комбінезон"
  description: "Просто дивлячись на це, ви відчуваєте себе <i>казково</i>."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    layers:
    - state: icon
      color: "#ffa69b"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffa69b"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#ffa69b"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ffa69b"
      - state: trinkets-equipped-INNERCLOTHING

# Red Jumpsuit
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitColorRed
  name: "червоний комбінезон"
  description: "Універсальний червоний комбінезон без розпізнавальних знаків."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    layers:
    - state: icon
      color: "#eb0c07"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#eb0c07"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#eb0c07"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#eb0c07"
      - state: trinkets-equipped-INNERCLOTHING

# Yellow Jumpsuit
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitColorYellow
  name: "жовтий комбінезон"
  description: "Загальний жовтий комбінезон без розпізнавальних знаків."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    layers:
    - state: icon
      color: "#ffe14d"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffe14d"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#ffe14d"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ffe14d"
      - state: trinkets-equipped-INNERCLOTHING

# Purple Jumpsuit
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitColorPurple
  name: "фіолетовий комбінезон"
  description: "Універсальний світло-фіолетовий комбінезон без розпізнавальних знаків."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    layers:
    - state: icon
      color: "#9f70cc"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#9f70cc"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#9f70cc"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#9f70cc"
      - state: trinkets-equipped-INNERCLOTHING

# Light Brown Jumpsuit
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitColorLightBrown
  name: "світло-коричневий комбінезон"
  description: "Універсальний світло-коричневий комбінезон без розпізнавальних знаків."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    layers:
    - state: icon
      color: "#c59431"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#c59431"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#c59431"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#c59431"
      - state: trinkets-equipped-INNERCLOTHING

# Brown Jumpsuit
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitColorBrown
  name: "коричневий комбінезон"
  description: "Універсальний коричневий комбінезон без розпізнавальних знаків."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    layers:
    - state: icon
      color: "#a17229"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#a17229"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#a17229"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#a17229"
      - state: trinkets-equipped-INNERCLOTHING

# Maroon Jumpsuit
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitColorMaroon
  name: "бордовий комбінезон"
  description: "Загальний бордовий комбінезон без розпізнавальних знаків."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    layers:
    - state: icon
      color: "#cc295f"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#cc295f"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#cc295f"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#cc295f"
      - state: trinkets-equipped-INNERCLOTHING

# Rainbow Jumpsuit
- type: entity
  parent: [ ClothingUniformBase, ClothingUniformFoldableBase ]
  id: ClothingUniformColorRainbow
  name: "райдужний комбінезон"
  description: "Різнокольоровий комбінезон!"
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/rainbow.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/rainbow.rsi
