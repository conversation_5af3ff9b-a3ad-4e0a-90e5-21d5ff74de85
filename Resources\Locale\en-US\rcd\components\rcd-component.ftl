
### UI

rcd-component-examine-mode-details = It's currently set to '{$mode}' mode.
rcd-component-examine-build-details = It's currently set to build {MAKEPLURAL($name)}.


### Interaction Messages

# Mode change
rcd-component-change-mode = The RCD is now set to '{$mode}' mode.
rcd-component-change-build-mode = The RCD is now set to build {MAKEPLURAL($name)}.

# Ammo count
rcd-component-no-ammo-message = The RCD has run out of charges!
rcd-component-insufficient-ammo-message = The RCD doesn't have enough charges left!

# Deconstruction
rcd-component-tile-indestructible-message = That tile can't be destructed!
rcd-component-deconstruct-target-not-on-whitelist-message = You can't deconstruct that!
rcd-component-nothing-to-deconstruct-message = There's nothing to deconstruct!
rcd-component-tile-obstructed-message = You can't deconstruct tiles when there's something on top of them!

# Construction
rcd-component-no-valid-grid = You're too far into open space to build here!
rcd-component-must-build-on-empty-tile-message = A foundation already exists here!
rcd-component-cannot-build-on-empty-tile-message = You can't build that without a foundation!
rcd-component-must-build-on-subfloor-message = You can only build that on exposed subfloor!
rcd-component-cannot-build-on-subfloor-message = You can't build that on exposed subfloor!
rcd-component-cannot-build-on-occupied-tile-message = You can't build here, the space is already occupied!
rcd-component-cannot-build-identical-tile = That tile already exists there!


### Category names

rcd-component-walls-and-flooring = Walls and flooring
rcd-component-windows-and-grilles = Windows and grilles
rcd-component-airlocks = Airlocks
rcd-component-electrical = Electrical
rcd-component-lighting = Lighting
rcd-component-piping = Piping
rcd-component-atmosphericutility = Atmospheric Utility
rcd-component-pumps = Pumps & Valves
rcd-component-vents = Vents


### Prototype names (note: constructable items will be puralized)

rcd-component-deconstruct = deconstruct
rcd-component-floor-steel = steel tile
rcd-component-plating = hull plate
