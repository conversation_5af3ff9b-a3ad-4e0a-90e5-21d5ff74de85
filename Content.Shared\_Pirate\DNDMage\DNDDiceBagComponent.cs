using Robust.Shared.GameStates;
using Robust.Shared.Serialization;

namespace Content.Shared._Pirate.DNDMage;

/// <summary>
/// Компонент для сумки з кістками ДНД, який при ініціалізації додає всім кісткам всередині
/// компонент DNDBagDiceComponent, щоб вони поверталися до цієї сумки після кидка.
/// </summary>
[RegisterComponent, NetworkedComponent, AutoGenerateComponentState]
public sealed partial class DNDDiceBagComponent : Component
{
    /// <summary>
    /// Чи додавати компонент DNDBagDiceComponent до кісток при ініціалізації.
    /// </summary>
    [DataField("addComponentOnInit"), AutoNetworkedField]
    public bool AddComponentOnInit = true;
    
    /// <summary>
    /// ID контейнера, в якому зберігаються кістки.
    /// </summary>
    [DataField("containerId"), AutoNetworkedField]
    public string ContainerId = "storage-default";
}
