﻿- type: constructionGraph
  id: BananaClownJumpsuit
  start: start
  graph:
    - node: start
      edges:
        - to: jumpsuit
          steps:
            - tag: BananaPeel
              name: A Banana Peel
              icon:
                sprite: Objects/Specific/Hydroponics/banana.rsi
                state: peel
              doAfter: 1
            - tag: BananaPeel
              name: A Banana Peel
              icon:
                sprite: Objects/Specific/Hydroponics/banana.rsi
                state: peel
              doAfter: 1
            - tag: BananaPeel
              name: A Banana Peel
              icon:
                sprite: Objects/Specific/Hydroponics/banana.rsi
                state: peel
              doAfter: 1
            - material: Bananium
              amount: 1
              doAfter: 1
            - tag: ClownSuit
              name: A Clown Suit
              icon:
                sprite: Clothing/Uniforms/Jumpsuit/clown.rsi
                state: icon
              doAfter: 1
    - node: jumpsuit
      entity: ClothingUniformJumpsuitClownBanana

- type: constructionGraph
  id: BananaClownShoes
  start: start
  graph:
    - node: start
      edges:
        - to: shoes
          steps:
            - tag: BananaPeel
              name: A Banana Peel
              icon:
                sprite: Objects/Specific/Hydroponics/banana.rsi
                state: peel
              doAfter: 1
            - tag: BananaPeel
              name: A Banana Peel
              icon:
                sprite: Objects/Specific/Hydroponics/banana.rsi
                state: peel
              doAfter: 1
            - tag: BananaPeel
              name: A Banana Peel
              icon:
                sprite: Objects/Specific/Hydroponics/banana.rsi
                state: peel
              doAfter: 1
            - material: Bananium
              amount: 1
              doAfter: 1
            - tag: ClownShoes
              name: Clown Shoes
              icon:
                sprite: Clothing/Shoes/Specific/clown.rsi
                state: icon
              doAfter: 1
    - node: shoes
      entity: ClothingShoesClownBanana

- type: constructionGraph
  id: BananaClownMask
  start: start
  graph:
    - node: start
      edges:
        - to: mask
          steps:
            - tag: BananaPeel
              name: A Banana Peel
              icon:
                sprite: Objects/Specific/Hydroponics/banana.rsi
                state: peel
              doAfter: 1
            - tag: BananaPeel
              name: A Banana Peel
              icon:
                sprite: Objects/Specific/Hydroponics/banana.rsi
                state: peel
              doAfter: 1
            - tag: BananaPeel
              name: A Banana Peel
              icon:
                sprite: Objects/Specific/Hydroponics/banana.rsi
                state: peel
              doAfter: 1
            - material: Bananium
              amount: 1
              doAfter: 1
            - tag: ClownMask
              name: A Clown Mask
              icon:
                sprite: Clothing/Mask/clown.rsi
                state: icon
              doAfter: 1
    - node: mask
      entity: ClothingMaskClownBanana
