- type: entity
  id: BaseMechPart
  abstract: true
  components:
  - type: Clickable
  - type: InteractionOutline
  - type: CollisionWake
  - type: TileFrictionModifier
    modifier: 0.5
  - type: Physics
    bodyType: Dynamic
    fixedRotation: false
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 100
        mask:
        - ItemMask
        restitution: 0.3  # fite me
        friction: 0.2
  - type: Pullable
  - type: GuideHelp
    guides:
    - Robotics

# Ripley APLU

- type: entity
  id: BaseRipleyPart
  parent: BaseMechPart
  abstract: true
  components:
  - type: Sprite
    drawdepth: Items
    noRot: false
    sprite: Objects/Specific/Mech/ripley_construction.rsi

- type: entity
  id: BaseRipleyPartItem
  parent: BaseRipleyPart
  abstract: true
  components:
  - type: Item
    size: Ginormous

- type: entity
  parent: BaseRipleyPart
  id: RipleyHarness
  name: "джгути ріплі"
  description: "Ядро АПЛУ Ріплі."
  components:
  - type: Appearance
  - type: ItemMapper
    mapLayers:
      ripley_l_arm+o:
        whitelist:
          tags:
          - RipleyLArm
      ripley_r_arm+o:
        whitelist:
          tags:
          - RipleyRArm
      ripley_l_leg+o:
        whitelist:
          tags:
          - RipleyLLeg
      ripley_r_leg+o:
        whitelist:
          tags:
          - RipleyRLeg
    sprite: Objects/Specific/Mech/ripley_construction.rsi
  - type: ContainerContainer
    containers:
      mech-assembly-container: !type:Container
  - type: MechAssembly
    finishedPrototype: RipleyChassis
    requiredParts:
      RipleyLArm: false
      RipleyRArm: false
      RipleyLLeg: false
      RipleyRLeg: false
  - type: Sprite
    state: ripley_harness+o
    noRot: true

- type: entity
  parent: BaseRipleyPartItem
  id: RipleyLArm
  name: "ліва рука ріплі"
  description: "Лівий маніпулятор ПТРК Ріплі. Находиться на шасі меча."
  components:
  - type: Sprite
    state: ripley_l_arm
  - type: Tag
    tags:
    - RipleyLArm

- type: entity
  parent: BaseRipleyPartItem
  id: RipleyLLeg
  name: "ліва нога ріплі"
  description: "Ліва нога бронетранспортера Ripley APLU. Вона стоїть на шасі меча."
  components:
  - type: Sprite
    state: ripley_l_leg
  - type: Tag
    tags:
    - RipleyLLeg

- type: entity
  parent: BaseRipleyPartItem
  id: RipleyRLeg
  name: "права нога ріплі"
  description: "Права нога бронетранспортера Ripley APLU. Стоїть на шасі меча."
  components:
  - type: Sprite
    state: ripley_r_leg
  - type: Tag
    tags:
    - RipleyRLeg

- type: entity
  parent: BaseRipleyPartItem
  id: RipleyRArm
  name: "права рука ріплі"
  description: "Права рука ПТРК Ріплі. Находиться на шасі меча."
  components:
  - type: Sprite
    state: ripley_r_arm
  - type: Tag
    tags:
    - RipleyRArm

- type: entity
  id: RipleyChassis
  parent: BaseRipleyPart
  name: "шасі ріплі"
  description: "Незавершене будівництво механізму Ripley APLU."
  components:
  - type: Appearance
  - type: ContainerContainer
    containers:
      battery-container: !type:Container
  - type: MechAssemblyVisuals
    statePrefix: ripley
  - type: Sprite
    noRot: true
    state: ripley0
  - type: Construction
    graph: Ripley
    node: start
    defaultTarget: ripley

# H.O.N.K.

- type: entity
  parent: BaseMechPart
  id: BaseHonkerPart
  abstract: true
  components:
  - type: Sprite
    drawdepth: Items
    noRot: false
    sprite: Objects/Specific/Mech/honker_construction.rsi

- type: entity
  id: BaseHonkerPartItem
  parent: BaseHonkerPart
  abstract: true
  components:
  - type: Item
    size: Ginormous

- type: entity
  parent: BaseHonkerPart
  id: HonkerHarness
  name: "Упряж H.O.N.K"
  description: "Ядро механізму H.O.N.K"
  components:
  - type: Appearance
  - type: ItemMapper
    mapLayers:
      honker_l_arm+o:
        whitelist:
          tags:
          - HonkerLArm
      honker_r_arm+o:
        whitelist:
          tags:
          - HonkerRArm
      honker_l_leg+o:
        whitelist:
          tags:
          - HonkerLLeg
      honker_r_leg+o:
        whitelist:
          tags:
          - HonkerRLeg
    sprite: Objects/Specific/Mech/honker_construction.rsi
  - type: ContainerContainer
    containers:
      mech-assembly-container: !type:Container
  - type: MechAssembly
    finishedPrototype: HonkerChassis
    requiredParts:
      HonkerLArm: false
      HonkerRArm: false
      HonkerLLeg: false
      HonkerRLeg: false
  - type: Sprite
    state: honker_harness+o
    noRot: true

- type: entity
  parent: BaseHonkerPartItem
  id: HonkerLArm
  name: "H.O.N.K. ліва рука"
  description: "Ліва рука H.O.N.K. з унікальними гніздами, які приймають дивну зброю, розроблену вченими-клоунами."
  components:
  - type: Sprite
    state: honker_l_arm
  - type: Tag
    tags:
    - HonkerLArm

- type: entity
  parent: BaseHonkerPartItem
  id: HonkerLLeg
  name: "H.O.N.K. ліва нога"
  description: "Ліва нога H.O.N.K. Стопа виглядає достатньо великою, щоб повністю вмістити клоунський черевик."
  components:
  - type: Sprite
    state: honker_l_leg
  - type: Tag
    tags:
    - HonkerLLeg

- type: entity
  parent: BaseHonkerPartItem
  id: HonkerRLeg
  name: "H.O.N.K. права нога"
  description: "Права нога H.O.N.K. Стопа виглядає достатньо великою, щоб повністю вмістити клоунський черевик."
  components:
  - type: Sprite
    state: honker_r_leg
  - type: Tag
    tags:
    - HonkerRLeg

- type: entity
  parent: BaseHonkerPartItem
  id: HonkerRArm
  name: "H.O.N.K. права рука"
  description: "Права рука H.O.N.K. з унікальними гніздами, які приймають дивну зброю, розроблену вченими-клоунами."
  components:
  - type: Sprite
    state: honker_r_arm
  - type: Tag
    tags:
    - HonkerRArm

- type: entity
  id: HonkerChassis
  parent: BaseHonkerPart
  name: "Шасі H.O.N.K"
  description: "Незавершене будівництво меха H.O.N.K. Містить блок реготу, бананіумне ядро та системи підтримки гудків."
  components:
  - type: Appearance
  - type: ContainerContainer
    containers:
      battery-container: !type:Container
  - type: MechAssemblyVisuals
    statePrefix: honker
  - type: Sprite
    noRot: true
    state: honker0
  - type: Construction
    graph: Honker
    node: start
    defaultTarget: honker

# H.A.M.T.R.

- type: entity
  id: BaseHamtrPart
  parent: BaseMechPart
  abstract: true
  components:
  - type: Sprite
    drawdepth: Items
    noRot: false
    sprite: Objects/Specific/Mech/hamtr_construction.rsi

- type: entity
  id: BaseHamtrPartItem
  parent: BaseHamtrPart
  abstract: true
  components:
  - type: Item
    size: Huge

- type: entity
  parent: BaseHamtrPart
  id: HamtrHarness
  name: "Упряж HAMTR"
  description: "Ядро HAMTR."
  components:
  - type: Appearance
  - type: ItemMapper
    mapLayers:
      hamtr_l_arm+o:
        whitelist:
          tags:
          - HamtrLArm
      hamtr_r_arm+o:
        whitelist:
          tags:
          - HamtrRArm
      hamtr_l_leg+o:
        whitelist:
          tags:
          - HamtrLLeg
      hamtr_r_leg+o:
        whitelist:
          tags:
          - HamtrRLeg
    sprite: Objects/Specific/Mech/hamtr_construction.rsi
  - type: ContainerContainer
    containers:
      mech-assembly-container: !type:Container
  - type: MechAssembly
    finishedPrototype: HamtrChassis
    requiredParts:
      HamtrLArm: false
      HamtrRArm: false
      HamtrLLeg: false
      HamtrRLeg: false
  - type: Sprite
    state: hamtr_harness+o
    noRot: true

- type: entity
  parent: BaseHamtrPartItem
  id: HamtrLArm
  name: "HAMTR ліва рука"
  description: "Лівий кронштейн HAMTR. Він стоїть на шасі меча."
  components:
  - type: Sprite
    state: hamtr_l_arm
  - type: Tag
    tags:
    - HamtrLArm

- type: entity
  parent: BaseHamtrPartItem
  id: HamtrLLeg
  name: "HAMTR ліва нога"
  description: "Ліва нога HAMTR. Вона стоїть на шасі меча."
  components:
  - type: Sprite
    state: hamtr_l_leg
  - type: Tag
    tags:
    - HamtrLLeg

- type: entity
  parent: BaseHamtrPartItem
  id: HamtrRLeg
  name: "HAMTR права нога"
  description: "Права нога HAMTR. Вона стоїть на шасі меча."
  components:
  - type: Sprite
    state: hamtr_r_leg
  - type: Tag
    tags:
    - HamtrRLeg

- type: entity
  parent: BaseHamtrPartItem
  id: HamtrRArm
  name: "HAMTR права рука"
  description: "Права рука HAMTR. Він стоїть на шасі меча."
  components:
  - type: Sprite
    state: hamtr_r_arm
  - type: Tag
    tags:
    - HamtrRArm

- type: entity
  id: HamtrChassis
  parent: BaseHamtrPart
  name: "Шасі HAMTR"
  description: "Незавершене будівництво меча HAMTR."
  components:
  - type: Appearance
  - type: ContainerContainer
    containers:
      battery-container: !type:Container
  - type: MechAssemblyVisuals
    statePrefix: hamtr
  - type: Sprite
    noRot: true
    state: hamtr0
  - type: Construction
    graph: Hamtr
    node: start
    defaultTarget: hamtr

# Vim!!!!!!

- type: entity
  parent: BaseMechPart
  id: BaseVimPart
  abstract: true
  components:
  - type: Sprite
    drawdepth: Items
    noRot: false
    sprite: Objects/Specific/Mech/vim_construction.rsi

- type: entity
  parent: BaseVimPart
  id: BaseVimPartItem
  abstract: true
  components:
  - type: Item
    size: Small

- type: entity
  parent: BaseVimPartItem
  id: VimHarness
  name: "vim harness"
  description: "Невеликий монтажний кронштейн для деталей vim."
  components:
  - type: Appearance
  - type: ItemMapper
    mapLayers:
      helmet:
        whitelist:
          tags:
          - HelmetEVA
      left_leg:
        whitelist:
          tags:
          - BorgLeg
      right_leg:
        whitelist:
          tags:
          - BorgLeg
    sprite: Objects/Specific/Mech/vim_construction.rsi
  - type: ContainerContainer
    containers:
      mech-assembly-container: !type:Container
  - type: MechAssembly
    finishedPrototype: VimChassis
    requiredParts:
      HelmetEVA: false
      BorgLeg: false
  - type: Sprite
    state: harness
    noRot: true

- type: entity
  id: VimChassis
  parent: BaseVimPart
  name: "шасі vim"
  description: "У процесі створення екзокостюму \"Вім\"."
  components:
  - type: Appearance
  - type: ContainerContainer
    containers:
      battery-container: !type:Container
  - type: MechAssemblyVisuals
    statePrefix: vim
  - type: Sprite
    noRot: true
    state: vim0
  - type: Construction
    graph: Vim
    node: start
    defaultTarget: vim
