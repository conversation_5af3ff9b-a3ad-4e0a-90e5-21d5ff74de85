<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>$(TargetFramework)</TargetFramework>
        <OutputPath>..\bin\Content.YAMLLinter\</OutputPath>
        <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
        <ServerGarbageCollection>true</ServerGarbageCollection>
    </PropertyGroup>

  <ItemGroup>
      <ProjectReference Include="..\Content.Client\Content.Client.csproj" />
      <ProjectReference Include="..\Content.Server\Content.Server.csproj" />
      <ProjectReference Include="..\Content.Shared\Content.Shared.csproj" />
      <ProjectReference Include="..\Content.IntegrationTests\Content.IntegrationTests.csproj" />
      <ProjectReference Include="..\RobustToolbox\Robust.Client\Robust.Client.csproj" />
      <ProjectReference Include="..\RobustToolbox\Robust.Server\Robust.Server.csproj" />
      <ProjectReference Include="..\RobustToolbox\Robust.Shared.Maths\Robust.Shared.Maths.csproj" />
      <ProjectReference Include="..\RobustToolbox\Robust.Shared\Robust.Shared.csproj" />
    </ItemGroup>

  <ItemGroup>
    <PackageReference Include="NUnit" />
  </ItemGroup>

  <Import Project="..\RobustToolbox\MSBuild\Robust.Properties.targets" />
</Project>
