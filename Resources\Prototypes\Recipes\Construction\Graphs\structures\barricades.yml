﻿#Due to limitations with placement conditions in the construction system, the other kinds of non-fulltile barricades are currently not constructible.
#This may make it sound like it needs some big refactor to be possible, but make no mistake, it is 100% due to my own technical incompetence.
- type: constructionGraph
  id: Barricade
  start: start
  graph:
    - node: start
      edges:
        - to: barricadefull
          steps:
            - material: WoodPlank
              amount: 4
              doAfter: 3
    - node: barricadefull
      entity: Barricade
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: MaterialWoodPlank1
              amount: 3 #returns 1 less as one breaks
            - !type:DeleteEntity {}
          conditions:
            - !type:EntityAnchored
              anchored: true
          steps:
            - tool: Prying
              doAfter: 5

- type: constructionGraph
  id: BarricadeDirectional
  start: start
  graph:
    - node: start
      edges:
        - to: barricadefull
          steps:
            - material: WoodPlank
              amount: 4
              doAfter: 3
    - node: barricadefull
      entity: BarricadeDirectional
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: MaterialWoodPlank1
              amount: 3 #returns 1 less as one breaks
            - !type:DeleteEntity {}
          conditions:
            - !type:EntityAnchored
              anchored: true
          steps:
            - tool: Prying
              doAfter: 5
