- type: entity
  # Just fades out with no movement animation
  id: WeaponArcStatic
  categories: [ HideSpawnMenu ]
  components:
    - type: TimedDespawn
      lifetime: 2.0
    - type: Sprite
      sprite: Effects/arcs.rsi
      state: spear
      drawdepth: Effects
    - type: EffectVisuals
    - type: WeaponArcVisuals
    - type: Tag
      tags:
        - HideContextMenu

- type: entity
  # Plays the state animation then disappears with no fade or swing
  id: WeaponArcAnimated
  categories: [ HideSpawnMenu ]
  components:
    - type: Sprite
      sprite: Effects/arcs.rsi
      state: disarm
      drawdepth: Effects
    - type: EffectVisuals
    - type: WeaponArcVisuals
      fadeOut: false


# Uses TimedDespawn instead of EffectVisuals because auto animation is easier but doesn't raise an animation complete event.

- type: entity
  id: WeaponArcThrust
  parent: WeaponArcStatic
  categories: [ HideSpawnMenu ]
  components:
    - type: WeaponArcVisuals
      animation: Thrust

- type: entity
  id: WeaponArcSlash
  parent: WeaponArcStatic
  categories: [ HideSpawnMenu ]
  components:
    - type: WeaponArcVisuals
      animation: Slash


- type: entity
  id: WeaponArcBite
  parent: WeaponArcStatic
  categories: [ HideSpawnMenu ]
  components:
    - type: WeaponArcVisuals
      fadeOut: false
    - type: Sprite
      state: bite
    - type: TimedDespawn
      lifetime: 0.399

- type: entity
  id: WeaponArcClaw
  parent: WeaponArcStatic
  categories: [ HideSpawnMenu ]
  components:
    - type: WeaponArcVisuals
      fadeOut: false
    - type: Sprite
      state: claw
    - type: TimedDespawn
      lifetime: 0.399

- type: entity
  id: WeaponArcDisarm
  parent: WeaponArcAnimated
  categories: [ HideSpawnMenu ]
  components:
    - type: WeaponArcVisuals
      fadeOut: false
    - type: Sprite
      state: disarm
    - type: TimedDespawn
      lifetime: 0.299

- type: entity
  id: WeaponArcFist
  parent: WeaponArcStatic
  categories: [ HideSpawnMenu ]
  components:
    - type: Sprite
      state: fist

- type: entity
  id: WeaponArcPunch
  parent: WeaponArcStatic
  categories: [ HideSpawnMenu ]
  components:
    - type: WeaponArcVisuals
      fadeOut: false
    - type: Sprite
      state: punch
    - type: TimedDespawn
      lifetime: 0.499

- type: entity
  id: WeaponArcKick
  parent: WeaponArcStatic
  categories: [ HideSpawnMenu ]
  components:
    - type: WeaponArcVisuals
      fadeOut: false
    - type: Sprite
      state: kick
    - type: TimedDespawn
      lifetime: 0.299

- type: entity
  id: WeaponArcSmash
  parent: WeaponArcStatic
  categories: [ HideSpawnMenu ]
  components:
    - type: WeaponArcVisuals
      fadeOut: false
    - type: Sprite
      state: smash
    - type: TimedDespawn
      lifetime: 0.299

- type: entity
  id: WeaponArcPurplePunch      # Not inheriting so we don't have EffectVisuals to delete the entity
  categories: [ HideSpawnMenu ] # on LightFade animation complete, which causes a
  components:                   # "Predicting the queued deletion of a networked entity" error on tests.
    - type: Sprite
      sprite: Effects/arcs.rsi
      layers:
      - state: punch
        color: "#ff80f4"
      drawdepth: Effects
    - type: WeaponArcVisuals
      fadeOut: false
    - type: TimedDespawn
      lifetime: 0.549
    - type: PointLight
      radius: 1.13
      energy: 50
      color: "#ff11d5"
      netsync: false
    - type: LightFade
      rampUpDuration: 0.1
      duration: 0.449
    - type: Tag
      tags:
        - HideContextMenu

- type: entity
  id: WeaponArcShadowClaw
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: _EE/Shadowling/Effects/arcs.rsi
    layers:
    - state: shadow_claw
      color: "#000000"
    drawdepth: Effects
  - type: WeaponArcVisuals
    fadeOut: false
  - type: TimedDespawn
    lifetime: 0.4
  - type: PointLight
    radius: 1.13
    energy: 50
    color: "#880808"
    netsync: false
  - type: LightFade
    rampUpDuration: 0.1
    duration: 0.449
  - type: Tag
    tags:
    - HideContextMenu
