- type: entity
  parent: BaseItem
  id: HandLabeler
  name: "ручний етикетувальник"
  description: "Ручний етикетувальник, що використовується для маркування предметів і об'єктів."
  components:
    - type: Sprite
      sprite: Objects/Tools/hand_labeler.rsi
      state: hand_labeler
    - type: Item
      sprite: Objects/Tools/hand_labeler.rsi
      storedRotation: -90
    - type: UseDelay
      delay: 2.0
    - type: ActivatableUI
      key: enum.HandLabelerUiKey.Key
      inHandsOnly: true
    - type: UserInterface
      interfaces:
        enum.HandLabelerUiKey.Key:
          type: HandLabelerBoundUserInterface
    - type: HandLabeler
      whitelist:
        components:
          - Item
        tags:
          - Structure
    - type: Tag
      tags:
      - HandLabeler # DeltaV - fish labeler
