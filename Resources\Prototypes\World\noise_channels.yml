﻿- type: noiseChannel
  id: Density
  noiseType: Perlin
  fractalLacunarityByPi: 0.666666666
  remapTo0Through1: true
  clippingRanges:
    - 0.4, 0.6
  clippedValue: 1.658 # magic number for chunk size.
  inputMultiplier: 6 # Makes density hopefully low noise in the local area while still being interesting at scale.
  outputMultiplier: 50.0 # We scale density up significantly for more human-friendly numbers.
  minimum: 45.0

- type: noiseChannel
  id: DensityUnclipped
  noiseType: Perlin
  fractalLacunarityByPi: 0.666666666
  remapTo0Through1: true
  inputMultiplier: 6  # Makes density hopefully low noise in the local area while still being interesting at scale.
  outputMultiplier: 50.0 # We scale density up significantly for more human-friendly numbers.
  minimum: 45.0

- type: noiseChannel
  id: Carver
  noiseType: Perlin
  fractalLacunarityByPi: 0.666666666
  remapTo0Through1: true
  inputMultiplier: 6

- type: noiseChannel
  id: Wreck
  noiseType: <PERSON>lin
  fractalLacunarityByPi: 0.666666666
  clippingRanges:
    - 0.0, 0.4
  clippedValue: 0
  remapTo0Through1: true
  inputMultiplier: 16 # Makes wreck concentration very low noise at scale.

- type: noiseChannel
  id: Temperature
  noiseType: Perlin
  fractalLacunarityByPi: 0.666666666
  remapTo0Through1: true
  inputMultiplier: 6 # Makes wreck concentration very low noise at scale.
