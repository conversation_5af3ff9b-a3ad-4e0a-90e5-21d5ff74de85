- type: entity
  name: "св<PERSON>чка"
  parent: BaseItem
  id: Candle
  description: "Тонкий гніт, протягнутий крізь жир."
  components:
    - type: Sprite
      noRot: true
      sprite: Objects/Misc/candles.rsi
      layers:
        - state: candle-big
          color: "#decb8e"
    - type: Item
      size: Small
    - type: Appearance
    - type: Reactive
      groups:
        Flammable: [ Touch ]
        Extinguish: [ Touch ]
    - type: ExtinguishOnInteract
      extinguishAttemptSound:
        path: /Audio/Items/candle_blowing.ogg
        params:
          variation: 0.05
          volume: 10
    - type: UseDelay
    - type: Flammable
      fireSpread: false
      canResistFire: false
      alwaysCombustible: true
      canExtinguish: true
      firestacksOnIgnite: 3.0
      firestackFade: -0.01
      damage:
        types:
          Heat: 0.1
    - type: FireVisuals
      sprite: Objects/Misc/candles.rsi
      normalState: fire-big
    - type: ToggleableLightVisuals
      spriteLayer: null
      inhandVisuals:
        left:
        - state: inhand-left-flame
          shader: unshaded
        right:
        - state: inhand-right-flame
          shader: unshaded
    - type: Damageable
      damageModifierSet: Wood
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]

- type: entity
  name: "червона свічка"
  parent: Candle
  id: CandleRed
  components:
  - type: Sprite
    layers:
      - state: candle-big
        color: "#a12349"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#a12349"
      - state: inhand-left-flame
      right:
      - state: inhand-right
        color: "#a12349"

- type: entity
  name: "синя свічка"
  parent: Candle
  id: CandleBlue
  components:
  - type: Sprite
    layers:
      - state: candle-big
        color: "#425d7d"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#425d7d"
      right:
      - state: inhand-right
        color: "#425d7d"

- type: entity
  name: "чорна свічка"
  parent: Candle
  id: CandleBlack
  components:
  - type: Sprite
    layers:
      - state: candle-big
        color: "#1b1724"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#1b1724"
      right:
      - state: inhand-right
        color: "#1b1724"

- type: entity
  name: "зелена свічка"
  parent: Candle
  id: CandleGreen
  components:
  - type: Sprite
    layers:
      - state: candle-big
        color: "#5d997e"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#5d997e"
      right:
      - state: inhand-right
        color: "#5d997e"

- type: entity
  name: "фіолетова свічка"
  parent: Candle
  id: CandlePurple
  components:
  - type: Sprite
    layers:
      - state: candle-big
        color: "#984aa1"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#984aa1"
      right:
      - state: inhand-right
        color: "#984aa1"


- type: entity
  name: "маленька свічка"
  parent: Candle
  id: CandleSmall
  components:
    - type: Item
      size: Tiny
    - type: Sprite
      layers:
        - state: candle-small
          color: "#e2ca90"
    - type: FireVisuals
      normalState: fire-small
    - type: Flammable
      firestacksOnIgnite: 2.0
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 60
        behaviors:
        - !type:SpawnEntitiesBehavior
        - !type:DoActsBehavior
          acts: [ "Destruction" ]

- type: entity
  name: "маленька червона свічка"
  parent: CandleSmall
  id: CandleRedSmall
  components:
  - type: Sprite
    layers:
      - state: candle-small
        color: "#a12349"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#a12349"
      right:
      - state: inhand-right
        color: "#a12349"

- type: entity
  name: "маленька синя свічка"
  parent: CandleSmall
  id: CandleBlueSmall
  components:
  - type: Sprite
    layers:
      - state: candle-small
        color: "#425d7d"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#425d7d"
      right:
      - state: inhand-right
        color: "#425d7d"

- type: entity
  name: "маленька чорна свічка"
  parent: CandleSmall
  id: CandleBlackSmall
  components:
  - type: Sprite
    layers:
      - state: candle-small
        color: "#1b1724"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#1b1724"
      right:
      - state: inhand-right
        color: "#1b1724"

- type: entity
  name: "маленька зелена свічка"
  parent: CandleSmall
  id: CandleGreenSmall
  components:
  - type: Sprite
    layers:
      - state: candle-small
        color: "#5d997e"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#5d997e"
      right:
      - state: inhand-right
        color: "#5d997e"

- type: entity
  name: "маленька фіолетова свічка"
  parent: CandleSmall
  id: CandlePurpleSmall
  components:
  - type: Sprite
    layers:
      - state: candle-small
        color: "#984aa1"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#984aa1"
      right:
      - state: inhand-right
        color: "#984aa1"

#Purely decorative candles for mappers. Do not have any functionality.

- type: entity
  name: "чарівна свічка"
  description: "Чи то магія, чи то високі технології, але ця свічка ніколи не гасне. З іншого боку, її полум'я досить холодне."
  parent: BaseItem
  suffix: Decorative
  id: CandleInfinite
  components:
  - type: Sprite
    noRot: true
    sprite: Objects/Misc/candles.rsi
    layers:
      - state: candle-big
        color: "#decb8e"
      - state: fire-big
        shader: unshaded
  - type: PointLight
    color: "#e39c40"
    radius: 2.5
    power: 10

- type: entity
  name: "чарівна червона свічка"
  parent: CandleInfinite
  id: CandleRedInfinite
  components:
  - type: Sprite
    layers:
      - state: candle-big
        color: "#a12349"
      - state: fire-big
        shader: unshaded
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#a12349"
      right:
      - state: inhand-right
        color: "#a12349"

- type: entity
  name: "чарівна синя свічка"
  parent: CandleInfinite
  id: CandleBlueInfinite
  components:
  - type: Sprite
    layers:
      - state: candle-big
        color: "#425d7d"
      - state: fire-big
        shader: unshaded
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#425d7d"
      right:
      - state: inhand-right
        color: "#425d7d"

- type: entity
  name: "магічна чорна свічка"
  parent: CandleInfinite
  id: CandleBlackInfinite
  components:
  - type: Sprite
    layers:
      - state: candle-big
        color: "#1b1724"
      - state: fire-big
        shader: unshaded
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#1b1724"
      right:
      - state: inhand-right
        color: "#1b1724"

- type: entity
  name: "чарівна зелена свічка"
  parent: CandleInfinite
  id: CandleGreenInfinite
  components:
  - type: Sprite
    layers:
      - state: candle-big
        color: "#5d997e"
      - state: fire-big
        shader: unshaded
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#5d997e"
      right:
      - state: inhand-right
        color: "#5d997e"

- type: entity
  name: "чарівна фіолетова свічка"
  parent: CandleInfinite
  id: CandlePurpleInfinite
  components:
  - type: Sprite
    layers:
      - state: candle-big
        color: "#984aa1"
      - state: fire-big
        shader: unshaded
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#984aa1"
      right:
      - state: inhand-right
        color: "#984aa1"

- type: entity
  name: "маленька магічна червона свічка"
  parent: CandleInfinite
  id: CandleRedSmallInfinite
  components:
  - type: Sprite
    layers:
      - state: candle-small
        color: "#a12349"
      - state: fire-small
        shader: unshaded
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#a12349"
      right:
      - state: inhand-right
        color: "#a12349"

- type: entity
  name: "маленька чарівна синя свічка"
  parent: CandleInfinite
  id: CandleBlueSmallInfinite
  components:
  - type: Sprite
    layers:
      - state: candle-small
        color: "#425d7d"
      - state: fire-small
        shader: unshaded
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#425d7d"
      right:
      - state: inhand-right
        color: "#425d7d"

- type: entity
  name: "маленька магічна чорна свічка"
  parent: CandleInfinite
  id: CandleBlackSmallInfinite
  components:
  - type: Sprite
    layers:
      - state: candle-small
        color: "#1b1724"
      - state: fire-small
        shader: unshaded
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#1b1724"
      right:
      - state: inhand-right
        color: "#1b1724"

- type: entity
  name: "маленька чарівна зелена свічка"
  parent: CandleInfinite
  id: CandleGreenSmallInfinite
  components:
  - type: Sprite
    layers:
      - state: candle-small
        color: "#5d997e"
      - state: fire-small
        shader: unshaded
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#5d997e"
      right:
      - state: inhand-right
        color: "#5d997e"

- type: entity
  name: "маленька магічна фіолетова свічка"
  parent: CandleInfinite
  id: CandlePurpleSmallInfinite
  components:
  - type: Sprite
    layers:
      - state: candle-small
        color: "#984aa1"
      - state: fire-small
        shader: unshaded
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#984aa1"
      right:
      - state: inhand-right
        color: "#984aa1"
