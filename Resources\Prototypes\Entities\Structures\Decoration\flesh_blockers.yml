- type: entity
  id: FleshBlocker
  parent: BaseStructure
  name: "шматок м'яса"
  description: "Набридливий шматок плоті."
  components:
  - type: InteractionOutline
  - type: Sprite
    noRot: true
    sprite: Structures/Decoration/flesh_decoration.rsi
    layers:
    - state: closed
      map: [ "enum.DamageStateVisualLayers.Base" ]
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.3
        density: 190
        mask:
        - MachineMask
        layer:
        - Impassable
  - type: RandomSprite
    available:
      - enum.DamageStateVisualLayers.Base:
          closed: ""
          ajar: ""
          open: ""
  - type: Damageable
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 25
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
