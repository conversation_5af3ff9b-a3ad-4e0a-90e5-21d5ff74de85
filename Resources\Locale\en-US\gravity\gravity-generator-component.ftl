### Gravity Generator

## UI

gravity-generator-window-title = Gravity Generator

## UI field names

gravity-generator-window-status = Status:
gravity-generator-window-power = Power:
gravity-generator-window-eta = ETA:
gravity-generator-window-charge = Charge:

## UI statuses
gravity-generator-window-status-fully-charged = Fully Charged
gravity-generator-window-status-off = Off
gravity-generator-window-status-charging = Charging
gravity-generator-window-status-discharging = Discharging

## UI Power Buttons
gravity-generator-window-power-on = On
gravity-generator-window-power-off = Off
gravity-generator-window-power-label = { $draw } / { $max } W

## UI ETA label

gravity-generator-window-eta-none = N/A
gravity-generator-window-eta-value = { TOSTRING($left, "m\\:ss") }
