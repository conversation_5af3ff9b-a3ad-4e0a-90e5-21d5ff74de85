- type: gameMap
  id: Frost
  mapName: 'Фрост'
  mapPath: /Maps/frost.yml
  minPlayers: 15
  stations:
    Frost:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: '{0} Станція Фрост {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: 'TG'
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/emergency_frost.yml
        - type: StationCargoShuttle
          path: /Maps/Shuttles/cargo-Frost.yml
        - type: GridSpawn
          groups:
            trade:
              addComponents:
              - type: ProtectedGrid
              - type: TradeStation
              paths:
              - /Maps/Shuttles/trading_outpost.yml
            mining: !type:GridSpawnGroup
              paths:
              - /Maps/Shuttles/mining-Frost.yml
        - type: StationJobs
          overflowJobs:
            - Passenger
          availableJobs:
            #Command
            Captain: [ 1, 1 ]
            NanotrasenRepresentative: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1 ]
            AdministrativeAssistant: [ 1, 1 ]
            HeadOfPersonnel: [ 1, 1 ]
            HeadOfSecurity: [ 1, 1 ]
            ChiefEngineer: [ 1, 1 ]
            ChiefMedicalOfficer: [ 1, 1 ]
            ResearchDirector: [ 1, 1 ]
            Quartermaster: [ 1, 1 ]
            #Other
            Reporter: [ 2, 2 ]
            Psychologist: [ 1, 1 ]
            #Security
            Warden: [ 1, 1 ]
            SecurityOfficer: [ 6, 6 ]
            SecurityCadet: [ 2, 2 ]
            #Engineer gaming
            AtmosphericTechnician: [ 3, 3 ]
            StationEngineer: [ 4, 4 ]
            TechnicalAssistant: [ 2, 2 ]
            #Medical
            Chemist: [ 3, 3 ]
            Paramedic: [ 2, 2 ]
            MedicalDoctor: [ 4, 4 ]
            MedicalIntern: [ 2, 2 ]
            #Science
            Scientist: [ 4, 4 ]
            ResearchAssistant: [ 2, 2 ]
            #Supply
            SalvageSpecialist: [ 3, 3 ]
            CargoTechnician: [ 3, 3 ]
            #Service
            Bartender: [ 2, 2 ]
            Botanist: [ 2, 2 ]
            Chef: [ 2, 2 ]
            ServiceWorker: [ 3, 3 ]
            Janitor: [ 2, 2 ]
            Chaplain: [ 1, 1 ]
            Librarian: [ 1, 1 ]
            Detective: [ 1, 1 ]
            Lawyer: [ 2, 2 ]
            Clown: [ 1, 1 ]
            Mime: [ 1, 1 ]
            Musician: [ 1, 1 ]
            #Passenger
            Passenger: [ -1, -1 ]
            #silicon
            StationAi: [ 1, 1 ]
            Borg: [ 4, 4 ]
            MedicalBorg: [ 2, 2 ]
