- type: entity
  name: "зварювальний інструмент"
  parent: BaseItem
  id: Welder
  description: "Розплавляє все, що завгодно, якщо воно заправлене паливом, не забудьте захисні окуляри!"
  components:
  - type: EmitSoundOnPickup
    sound: /Audio/SimpleStation14/Items/Handling/welder_pickup.ogg
  - type: EmitSoundOnDrop
    sound: /Audio/SimpleStation14/Items/Handling/welder_drop.ogg
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Items/welder_drop.ogg
  - type: Sprite
    sprite: Objects/Tools/welder.rsi
    layers:
    - state: icon
    - state: welder_flame
      visible: false
      shader: unshaded
      map: ["enum.ToggleVisuals.Layer"]
  - type: Tag
    tags:
    - WeldingTool
  - type: GenericVisualizer
    visuals:
      enum.ToggleVisuals.Toggled:
        enum.ToggleVisuals.Layer:
          True: { visible: true }
          False: { visible: false }
  - type: Item
    size: Small
    sprite: Objects/Tools/welder.rsi
  - type: ItemToggle
    predictable: false
    soundActivate:
      collection: WelderOn
      params:
        variation: 0.125
        volume: -5
    soundDeactivate:
      collection: WelderOff
      params:
        variation: 0.125
        volume: -5
  - type: ItemToggleMeleeWeapon
    activatedSoundOnHit:
      path: /Audio/Weapons/Guns/Hits/energy_meat1.ogg
      params:
        variation: 0.250
        volume: -10
    activatedSoundOnHitNoDamage:
      path: /Audio/Weapons/Guns/Hits/energy_meat1.ogg
      params:
        variation: 0.250
        volume: -12
    deactivatedSoundOnHitNoDamage:
      collection: MetalThud
    activatedDamage:
        types:
            Heat: 7
  - type: ItemToggleDamageOtherOnHit
    activatedDamage:
      types:
        Heat: 4
        Blunt: 3
  - type: ItemToggleSize
    activatedSize: Large
  - type: ItemToggleHot
  - type: ComponentToggler
    components:
    - type: DisarmMalus
      malus: 0.6
  - type: ToggleableLightVisuals
    spriteLayer: flame
    inhandVisuals:
      left:
      - state: inhand-left-flame
        shader: unshaded
      right:
      - state: inhand-right-flame
        shader: unshaded
  - type: UseDelay
  - type: MeleeWeapon
    wideAnimationRotation: -90
    damage:
      types:
        Blunt: 6 #i mean... i GUESS you could use it like that
    soundHit:
      collection: MetalThud
  - type: DamageOtherOnHit
  - type: RefillableSolution
    solution: Welder
  - type: SolutionContainerManager
    solutions:
      Welder:
        reagents:
        - ReagentId: WeldingFuel
          Quantity: 100
        maxVol: 100
  - type: Tool
    useSound:
      collection: Welder
    qualities: Welding
  - type: Welder
  - type: PointLight
    enabled: false
    radius: 1.5
    color: orange
    netsync: false
  - type: ItemTogglePointLight
  - type: Appearance
  - type: RequiresEyeProtection
  - type: PhysicalComposition
    materialComposition:
      Steel: 200
  - type: StaticPrice
    price: 40
  - type: IgnitionSource
    temperature: 700
  - type: WeldingHealing # Same as Brutepack - Estacao Pirata IPCs
    damageContainers:
      - Silicon
    damage:
      types:
        Blunt: -15
        Piercing: -15
        Slash: -15
  # Shitmed Change
  - type: Cautery
    speed: 0.7
  - type: SurgeryTool
    startSound:
      collection: Welder
    endSound:
      collection: WelderOff
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 80
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  name: "промисловий зварювальний інструмент"
  parent: Welder
  id: WelderIndustrial
  description: "Промисловий зварювальний апарат з більш ніж удвічі більшим запасом палива."
  components:
  - type: Sprite
    sprite: Objects/Tools/welder_industrial.rsi
  - type: Item
    sprite: Objects/Tools/welder_industrial.rsi
  - type: SolutionContainerManager
    solutions:
      Welder:
        reagents:
        - ReagentId: WeldingFuel
          Quantity: 250
        maxVol: 250
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 125
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  name: "передовий промисловий зварювальний інструмент"
  parent: WelderIndustrial
  id: WelderIndustrialAdvanced
  description: "Удосконалений промисловий зварювальний апарат з більш ніж п'ятикратним запасом палива та більш гарячим полум'ям."
  components:
  - type: Sprite
    sprite: Objects/Tools/welder_industrialadv.rsi
  - type: Item
    sprite: Objects/Tools/welder_industrialadv.rsi
  - type: SolutionContainerManager
    solutions:
      Welder:
        reagents:
        - ReagentId: WeldingFuel
          Quantity: 500
        maxVol: 500
  - type: Tool
    speedModifier: 1.5

- type: entity
  name: "експериментальний зварювальний інструмент"
  parent: Welder
  id: WelderExperimental
  description: "Експериментальний зварювальний апарат, який менш шкідливий для очей, має більш гаряче полум'я і повільно регенерує паливо."
  components:
  - type: Sprite
    sprite: Objects/Tools/welder_experimental.rsi
  - type: Item
    sprite: Objects/Tools/welder_experimental.rsi
  - type: SolutionContainerManager
    solutions:
      Welder:
        reagents:
        - ReagentId: WeldingFuel
          Quantity: 200
        maxVol: 200
  - type: PointLight
    enabled: false
    radius: 1.5
    color: lightblue
  - type: SolutionRegeneration
    solution: Welder
    generated:
      reagents:
      - ReagentId: WeldingFuel
        Quantity: 0.5
  - type: Tool
    speedModifier: 2
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 155
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  name: "аварійний зварювальний інструмент"
  parent: Welder
  id: WelderMini
  description: "Мініатюрний зварювальний апарат, що використовується під час надзвичайних ситуацій."
  components:
  - type: Sprite
    sprite: Objects/Tools/welder_mini.rsi
  - type: Item
    size: Tiny
    sprite: Objects/Tools/welder_mini.rsi
  - type: RefillableSolution
    solution: Welder
  - type: SolutionContainerManager
    solutions:
      Welder:
        reagents:
        - ReagentId: WeldingFuel
          Quantity: 50
        maxVol: 50
  - type: Tool
    speedModifier: 0.7
  - type: PointLight
    enabled: false
    radius: 1.0
    color: orange
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 45
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2
