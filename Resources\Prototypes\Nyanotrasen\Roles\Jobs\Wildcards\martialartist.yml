- type: job
  id: MartialArtist
  name: job-name-martialartist
  description: job-description-martialartist
  playTimeTracker: JobMartialArtist
  requirements:
    - !type:CharacterOverallTimeRequirement
      min: 7200 #2 hours
  startingGear: MartialArtistGear
  icon: "JobIconMartialArtist"
  supervisors: job-supervisors-hop
  setPreference: true
#  whitelistRequired: true
  access:
  - Maintenance
  - Theatre # DeltaV - Add Theatre access
  - Boxer # DeltaV - Add Boxer access
  special:
  - !type:AddTraitSpecial
    traits:
    - MartialArtist

- type: startingGear
  id: MartialArtistGear
  subGear:
  - MartialArtistPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformMartialGi
    belt: ClothingBeltMartialBlack
    back: ClothingBackpackFilled
    id: MartialArtistPDA
    ears: ClothingHeadsetService
    shoes: ClothingShoesGeta
    gloves: ClothingHandsGlovesBoxingRed
  satchel: ClothingBackpackSatchelFilled
  duffelbag: ClothingBackpackDuffelFilled

- type: startingGear
  id: MartialArtistPlasmamanGear
  parent: BasePlasmamanGear
  equipment:
    jumpsuit: ClothingUniformEnvirosuitMartialGi
    head: ClothingHeadEnvirohelmMartialGi
    # No envirogloves, use the boxing gloves instead
    shoes: ClothingShoesColorBlack
