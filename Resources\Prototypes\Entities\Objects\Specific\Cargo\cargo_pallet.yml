- type: entity
  id: CargoPallet
  name: "вантажний піддон"
  description: "Звичний атрибут логістики та вантажу. Непомітне нагадування, куди ставити ящики під час транспортування, щоб уникнути синців на гомілках."
  parent: BaseStructure
  components:
  - type: InteractionOutline
  - type: Anchorable
    flags:
    - Anchorable
  - type: CollideOnAnchor
  - type: Physics
    canCollide: false
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.45"
        density: 15
        mask:
          - MachineMask
  - type: CargoPallet
    palletType: All
  - type: StaticPrice
    price: 100
  - type: Sprite
    drawdepth: FloorTiles
    layers:
    - sprite: Structures/catwalk.rsi
      state: catwalk_preview
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 500
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 200
        behaviors:
          - !type:SpawnEntitiesBehavior
            spawn:
              PartRodMetal: # takes two to construct, so drop less than that
                min: 0
                max: 1
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
  - type: GuideHelp
    guides:
    - Cargo

- type: entity
  id: CargoPalletSell
  name: "піддон для продажу вантажу"
  description: "Позначає дійсні товари для продажу."
  parent: CargoPallet
  components:
  - type: CargoPallet
    palletType: sell
  - type: Sprite
    drawdepth: FloorTiles
    sprite: Structures/cargo_pallets.rsi
  - type: Icon
    sprite: Structures/cargo_pallets.rsi
    state: cargo_pallet_sell
  - type: IconSmooth
    key: cargo_pallet_sell
    base: cargo_pallet_sell_

- type: entity
  id: CargoPalletBuy
  name: "палета для купівлі вантажу"
  description: "Вказує, де з'являтимуться замовлення після покупки."
  parent: CargoPallet
  components:
  - type: CargoPallet
    palletType: buy
  - type: Sprite
    drawdepth: FloorTiles
    sprite: Structures/cargo_pallets.rsi
  - type: Icon
    sprite: Structures/cargo_pallets.rsi
    state: cargo_pallet_buy
  - type: IconSmooth
    key: cargo_pallet_buy
    base: cargo_pallet_buy_
