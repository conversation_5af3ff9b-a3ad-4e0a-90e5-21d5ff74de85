## Entity

microwave-component-interact-using-no-power = It has no power!
microwave-component-interact-using-broken = It's broken!
microwave-component-interact-using-container-full = Container is full
microwave-component-interact-using-transfer-success = Transferred {$amount}u
microwave-component-interact-using-transfer-fail = That won't work!
microwave-component-suicide-multi-head-others-message = {$victim} is trying to cook their heads!
microwave-component-suicide-others-message = {$victim} is trying to cook their head!
microwave-component-suicide-multi-head-message = You cook your heads!
microwave-component-suicide-message = You cook your head!
microwave-component-upgrade-cook-time = cook time
microwave-component-interact-full = It's full.
microwave-component-interact-item-too-big = { CAPITALIZE(THE($item)) } is too big to fit in the microwave!

## Bound UI

microwave-bound-user-interface-instant-button = INSTANT
microwave-bound-user-interface-cook-time-label = COOK TIME: {$time}

## UI

microwave-menu-title = Microwave
microwave-menu-start-button = Start
microwave-menu-eject-all-text = Eject All Contents
microwave-menu-eject-all-tooltip = This vaporizes all reagents, but ejects any solids.
microwave-menu-instant-button = INSTANT
microwave-menu-footer-flavor-left = Do not insert any electronic, metallic or living objects.
microwave-menu-footer-flavor-right = v1.5
