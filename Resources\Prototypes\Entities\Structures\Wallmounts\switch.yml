- type: entity
  id: SignalSwitch
  name: "сигнальний перемикач"
  description: "Це вимикач для перемикання живлення речей."
  placement:
    mode: SnapgridCenter
    snap:
    - Wallmount
  components:
  - type: StationAiWhitelist
  - type: WallMount
    arc: 360
  - type: Clickable
  - type: InteractionOutline
  - type: Physics
    canCollide: false
  - type: Sprite
    drawdepth: SmallObjects
    sprite: Structures/Wallmounts/switch.rsi
    state: on
  - type: SignalSwitch
  - type: UseDelay
    delay: 0.5 # prevent light-toggling auto-clickers.
  - type: Rotatable
  - type: Construction
    graph: SignalSwitchGraph
    node: SignalSwitchNode
  - type: Fixtures
  - type: DeviceNetwork
    deviceNetId: Wireless
  - type: WirelessNetworkConnection
    range: 200
  - type: DeviceLinkSource
    ports:
    - On
    - Off
    - Status
    lastSignals:
      Status: false

- type: entity
  id: SignalButton
  name: "сигнальна кнопка"
  description: "Це кнопка для активац<PERSON>ї чогось."
  placement:
    mode: SnapgridCenter
    snap:
    - Wallmount
  components:
  - type: StationAiWhitelist
  - type: WallMount
    arc: 360
  - type: Clickable
  - type: InteractionOutline
  - type: Physics
    canCollide: false
  - type: Sprite
    drawdepth: SmallObjects
    sprite: Structures/Wallmounts/switch.rsi
    state: dead
  - type: UseDelay
    delay: 0.5 # prevent light-toggling auto-clickers.
  - type: SignalSwitch
    onPort: Pressed
    offPort: Pressed
    statusPort: Pressed
  - type: Rotatable
  - type: Construction
    graph: SignalButtonGraph
    node: SignalButtonNode
  - type: Fixtures
  - type: DeviceNetwork
    deviceNetId: Wireless
  - type: WirelessNetworkConnection
    range: 200
  - type: DeviceLinkSource
    ports:
      - Pressed
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 80
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 40
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
          - !type:PlaySoundBehavior
            sound:
              collection: MetalBreak
              params:
                volume: -8

- type: entity
  id: ApcNetSwitch
  name: "мережевий перемикач apc"
  description: "Це перемикач для перемикання ліхтарів, які підключені до одного акумулятора."
  placement:
    mode: SnapgridCenter
    snap:
      - Wallmount
  components:
    - type: WallMount
      arc: 360
    - type: Clickable
    - type: InteractionOutline
    - type: Physics
      canCollide: false
    - type: Transform
      anchored: true
    - type: Sprite
      drawdepth: SmallObjects
      sprite: Structures/Wallmounts/switch.rsi
      state: on
    - type: Rotatable
    - type: ExtensionCableReceiver
    - type: DeviceNetwork
      deviceNetId: Apc
      transmitFrequencyId: SmartLight # assuming people want to use it for light switches.
    - type: ApcNetworkConnection
    - type: ApcNetSwitch
    - type: Construction
      graph: LightSwitchGraph
      node: LightSwitchNode
    - type: Fixtures

- type: entity
  id: TwoWayLever
  name: "двосторонній важіль"
  description: "Двосторонній важіль."
  placement:
    mode: SnapgridCenter
  components:
    - type: StationAiWhitelist
    - type: Clickable
    - type: InteractionOutline
    - type: Sprite
      drawdepth: HighFloorObjects
      sprite: Structures/conveyor.rsi
      layers:
        - state: switch-off
          map: ["enabled", "enum.TwoWayLeverState.Middle"]
    - type: TwoWayLever
    - type: UseDelay
      delay: 0.2 # prevent light-toggling auto-clickers.
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.TwoWayLeverVisuals.State:
          enabled:
            Right: { state: switch-fwd }
            Middle: { state: switch-off }
            Left: { state: switch-rev }
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: Metallic
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - type: Construction
      graph: LeverGraph
      node: LeverNode
    - type: DeviceNetwork
      deviceNetId: Wireless
    - type: WirelessNetworkConnection
      range: 200
    - type: DeviceLinkSource
      ports:
        - Left
        - Right
        - Middle

#directional

- type: entity
  id: SignalSwitchDirectional
  name: "сигнальний перемикач"
  suffix: directional
  parent: SignalSwitch
  components:
  - type: WallMount
    arc: 175
  - type: Construction
    graph: SignalSwitchDirectionalGraph
    node: SignalSwitchDirectionalNode

- type: entity
  id: SignalButtonDirectional
  name: "сигнальна кнопка"
  suffix: directional
  parent: SignalButton
  components:
  - type: WallMount
    arc: 175
  - type: Construction
    graph: SignalButtonDirectionalGraph
    node: SignalButtonDirectionalNode

- type: entity
  id: ApcNetSwitchDirectional
  name: "мережевий перемикач apc"
  suffix: directional
  parent: ApcNetSwitch
  components:
  - type: WallMount
    arc: 175
  - type: Construction
    graph: LightSwitchDirectionalGraph
    node: LightSwitchDirectionalNode

# lockable buttons

- type: entity
  id: LockableButton
  name: "кнопка з фіксацією"
  parent: SignalButtonDirectional
  categories: [ HideSpawnMenu ]
  components:
  - type: Appearance
  - type: Lock
  - type: LockVisuals
  - type: AccessReader
  - type: Sprite
    drawdepth: WallMountedItems
    sprite: Structures/Wallmounts/locked_switch.rsi
    layers:
    - state: base
    - state: locked
      map: ["enum.LockVisualLayers.Lock"]
      shader: unshaded

- type: entity
  id: LockableButtonCaptain
  suffix: Captain
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Captain"]]

- type: entity
  id: LockableButtonHeadOfPersonnel
  suffix: HeadOfPersonnel
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["HeadOfPersonnel"]]

- type: entity
  id: LockableButtonChiefEngineer
  suffix: ChiefEngineer
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["ChiefEngineer"]]

- type: entity
  id: LockableButtonChiefMedicalOfficer
  suffix: ChiefMedicalOfficer
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["ChiefMedicalOfficer"]]

- type: entity
  id: LockableButtonHeadOfSecurity
  suffix: HeadOfSecurity
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["HeadOfSecurity"]]

- type: entity
  id: LockableButtonResearchDirector
  suffix: ResearchDirector
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["ResearchDirector"]]

- type: entity
  id: LockableButtonCommand
  suffix: Command
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Command"]]

- type: entity
  id: LockableButtonCryogenics
  suffix: Cryogenics
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Cryogenics"]]

- type: entity
  id: LockableButtonSecurity
  suffix: Security
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Security"]]

- type: entity
  id: LockableButtonDetective
  suffix: Detective
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Detective"]]

- type: entity
  id: LockableButtonArmory
  suffix: Armory
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Armory"]]

#- type: entity
#  id: LockableButtonBrig
#  suffix: Brig
#  parent: LockableButton
#  components:
#  - type: AccessReader
#    access: [["Brig"]]

- type: entity
  id: LockableButtonLawyer
  suffix: Lawyer
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Lawyer"]]

- type: entity
  id: LockableButtonEngineering
  suffix: Engineering
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Engineering"]]

- type: entity
  id: LockableButtonMedical
  suffix: Medical
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Medical"]]

- type: entity
  id: LockableButtonQuartermaster
  suffix: Quartermaster
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Quartermaster"]]

- type: entity
  id: LockableButtonSalvage
  suffix: Salvage
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Salvage"]]

- type: entity
  id: LockableButtonCargo
  suffix: Cargo
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Cargo"]]

- type: entity
  id: LockableButtonResearch
  suffix: Research
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Research"]]

- type: entity
  id: LockableButtonService
  suffix: Service
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Service"]]

- type: entity
  id: LockableButtonMaintenance
  suffix: Maintenance
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Maintenance"]]

- type: entity
  id: LockableButtonExternal
  suffix: External
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["External"]]

- type: entity
  id: LockableButtonJanitor
  suffix: Janitor
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Janitor"]]

- type: entity
  id: LockableButtonTheatre
  suffix: Theatre
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Theatre"]]

- type: entity
  id: LockableButtonBar
  suffix: Bar
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Bar"]]

- type: entity
  id: LockableButtonChemistry
  suffix: Chemistry
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Chemistry"]]

- type: entity
  id: LockableButtonKitchen
  suffix: Kitchen
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Kitchen"]]

- type: entity
  id: LockableButtonChapel
  suffix: Chapel
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Chapel"]]

- type: entity
  id: LockableButtonHydroponics
  suffix: Hydroponics
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Hydroponics"]]

- type: entity
  id: LockableButtonAtmospherics
  suffix: Atmospherics
  parent: LockableButton
  components:
  - type: AccessReader
    access: [["Atmospherics"]]

# button frames

- type: entity
  id: ButtonFrame
  name: "рамка для кнопок"
  categories: [ HideSpawnMenu ]
  description: "Це рамка, яка допомагає візуально розрізняти перемикачі."
  placement:
    mode: SnapgridCenter
    snap:
    - Wallmount
  components:
  - type: Clickable
  - type: WallMount
    arc: 360
  - type: Physics
    canCollide: false
  - type: Sprite
    drawdepth: SmallObjects
    sprite: Structures/Wallmounts/switch_frame.rsi
    state: grey
  - type: Rotatable
  - type: Fixtures

- type: entity
  id: ButtonFrameGrey
  parent: ButtonFrame
  suffix: grey
  components:
  - type: Sprite
    drawdepth: SmallObjects
    sprite: Structures/Wallmounts/switch_frame.rsi
    state: grey

- type: entity
  id: ButtonFrameCaution
  parent: ButtonFrame
  suffix: caution
  components:
  - type: Sprite
    drawdepth: SmallObjects
    sprite: Structures/Wallmounts/switch_frame.rsi
    state: caution

- type: entity
  id: ButtonFrameCautionSecurity
  parent: ButtonFrame
  suffix: caution
  components:
  - type: Sprite
    drawdepth: SmallObjects
    sprite: Structures/Wallmounts/switch_frame.rsi
    state: caution_security

- type: entity
  id: ButtonFrameExit
  parent: ButtonFrame
  suffix: exit
  components:
  - type: Sprite
    drawdepth: SmallObjects
    sprite: Structures/Wallmounts/switch_frame.rsi
    state: exit

- type: entity
  id: ButtonFrameJanitor
  parent: ButtonFrame
  suffix: janitor
  components:
  - type: Sprite
    drawdepth: SmallObjects
    sprite: Structures/Wallmounts/switch_frame.rsi
    state: janitor
