<BoxContainer xmlns="https://spacestation14.io"
         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         Orientation="Vertical" Margin="2 0 2 4">
    <Collapsible>
        <CollapsibleHeading Name="CAddress" />
        <CollapsibleBody Margin="20 0 0 0">
            <BoxContainer Orientation="Vertical">
                <BoxContainer Orientation="Horizontal" Margin="0 0 0 2">
                    <CheckBox Name="CEnableDevice" Text="{Loc 'air-alarm-ui-widget-enable'}" />
                </BoxContainer>
                <!-- Upper row: toggle, direction, volume rate, widenet, copy settings -->
                <BoxContainer Orientation="Horizontal" Margin="0 0 0 2" HorizontalExpand="True">
                    <BoxContainer Orientation="Vertical" HorizontalExpand="True">
                        <Label Text="{Loc 'air-alarm-ui-scrubber-pump-direction-label'}" Margin="0 0 0 1"/>
                        <OptionButton Name="CPumpDirection" HorizontalExpand="True" />
                    </BoxContainer>
                    <BoxContainer Orientation="Vertical" HorizontalExpand="True">
                        <Label Text="{Loc 'air-alarm-ui-scrubber-volume-rate-label'}" Margin="0 0 0 1" />
                        <FloatSpinBox Name="CVolumeRate" HorizontalExpand="True" />
                    </BoxContainer>
                </BoxContainer>
                <BoxContainer>
                    <CheckBox Name="CWideNet" Text="{Loc 'air-alarm-ui-scrubber-wide-net-label'}" />
                </BoxContainer>
                <BoxContainer Orientation="Horizontal" Margin ="0 0 0 2">
                    <Button Name="CCopySettings" Text="{Loc 'air-alarm-ui-widget-copy'}" ToolTip="{Loc 'air-alarm-ui-widget-copy-tooltip'}" />
                </BoxContainer>
                <!-- Lower row: every single gas -->
                <Collapsible Margin="2 2 2 2">
                    <CollapsibleHeading Title="Gas filters" />
                    <CollapsibleBody Margin="20 0 0 0">
                        <GridContainer HorizontalExpand="True" Name="CGasContainer" Columns="3" />
                    </CollapsibleBody>
                </Collapsible>
            </BoxContainer>
        </CollapsibleBody>
    </Collapsible>
</BoxContainer>
