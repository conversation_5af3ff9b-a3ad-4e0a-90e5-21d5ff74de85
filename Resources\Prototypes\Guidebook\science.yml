- type: guideEntry
  id: Science
  name: guide-entry-science
  text: "/ServerInfo/Guidebook/Science/Science.xml"
  children:
  - Technologies
  - AnomalousResearch
  - Xenoarchaeology
  - Robotics
  - MachineUpgrading
  - TraversalDistorter
  - ReverseEngineering
  - MantisGuide

- type: guideEntry
  id: Technologies
  name: guide-entry-technologies
  text: "/ServerInfo/Guidebook/Science/Technologies.xml"
  filterEnabled: True

- type: guideEntry
  id: AnomalousResearch
  name: guide-entry-anomalous-research
  text: "/ServerInfo/Guidebook/Science/AnomalousResearch.xml"
  children:
  - APE
  - ScannersAndVessels

- type: guideEntry
  id: ScannersAndVessels
  name: guide-entry-scanners-and-vessels
  text: "/ServerInfo/Guidebook/Science/ScannersAndVessels.xml"

- type: guideEntry
  id: APE
  name: guide-entry-ape
  text: "/ServerInfo/Guidebook/Science/APE.xml"

- type: guideEntry
  id: Xenoarchaeology
  name: guide-entry-xenoarchaeology
  text: "/ServerInfo/Guidebook/Science/Xenoarchaeology.xml"
  children:
  - ArtifactReports

- type: guideEntry
  id: Robotics
  name: guide-entry-robotics
  text: "/ServerInfo/Guidebook/Science/Robotics.xml"
  children:
  - Cyborgs

- type: guideEntry
  id: ArtifactReports
  name: guide-entry-artifact-reports
  text: "/ServerInfo/Guidebook/Science/ArtifactReports.xml"

- type: guideEntry
  id: TraversalDistorter
  name: guide-entry-traversal-distorter
  text: "/ServerInfo/Guidebook/Science/TraversalDistorter.xml"

- type: guideEntry
  id: MachineUpgrading
  name: guide-entry-machine-upgrading
  text: "/ServerInfo/Guidebook/Science/MachineUpgrading.xml"

- type: guideEntry
  id: Cyborgs
  name: guide-entry-cyborgs
  text: "/ServerInfo/Guidebook/Science/Cyborgs.xml"

- type: guideEntry
  id: MantisGuide
  name: guide-entry-Psionic-Mantis
  text: "/ServerInfo/Guidebook/Science/MantisGuide.xml"