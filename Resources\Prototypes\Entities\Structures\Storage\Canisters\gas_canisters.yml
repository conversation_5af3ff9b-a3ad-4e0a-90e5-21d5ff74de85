- type: entity
  abstract: true
  parent: BaseStructureDynamic
  id: GasCanister
  name: "газова каністра"
  description: "Каністра, яка може містити будь-який тип газу. Приєднується до портів роз'єму за допомогою гайкового ключа."
  components:
    - type: InteractionOutline
    - type: Transform
      noRot: true
    - type: Sprite
      sprite: Structures/Storage/canister.rsi
      noRot: true
      layers:
        - state: grey
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.GasPortableVisuals.ConnectedState:
          connectedToPort:
            False: { state: can-connector, visible: false }
            True: { state: can-connector, visible: true }
        enum.GasCanisterVisuals.TankInserted:
          tankInserted:
            False: { state: can-open, visible: false }
            True: { state: can-open, visible: true }
        enum.LockVisuals.Locked:
          locked:
            False: { state: unlocked, shader: "unshaded" }
            True: { state: locked, shader: "unshaded" }
        enum.GasCanisterVisuals.PressureState:
          pressureLight:
            0: { state: can-o0, shader: "unshaded" }
            1: { state: can-o1, shader: "unshaded" }
            2: { state: can-o2, shader: "unshaded" }
            3: { state: can-o3, shader: "unshaded" }
    - type: UserInterface
      interfaces:
        enum.GasCanisterUiKey.Key:
          type: GasCanisterBoundUserInterface
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 600
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
        - trigger:
            !type:DamageTrigger
            damage: 300
          behaviors:
            - !type:PlaySoundBehavior
              sound:
                collection: MetalBreak
            - !type:SpawnEntitiesBehavior
              spawn:
                GasCanisterBrokenBase:
                  min: 1
                  max: 1
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
            - !type:DumpCanisterBehavior
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: Metallic
    - type: Physics
      bodyType: Dynamic
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.25,-0.25,0.25,0.25"
          density: 380
          mask:
          - SmallMobMask
          layer:
          - MachineLayer
    - type: AtmosDevice
      requireAnchored: false
      joinSystem: true
    - type: ContainerContainer
      containers:
        tank_slot: !type:ContainerSlot {}
    - type: NodeContainer
      nodes:
        port:
          !type:PortablePipeNode
          nodeGroupID: Pipe
          rotationsEnabled: false
          volume: 1
    - type: ItemSlots
    - type: GasPortable
    - type: GasCanister
      gasTankSlot:
        name: comp-gas-canister-slot-name-gas-tank
        whitelist:
          components:
            - GasTank
            - BorgJetpack
    - type: StaticPrice
      price: 1000
    - type: AccessReader
      access: [["Atmospherics"], ["Engineering"], ["Research"]]
    - type: Lock
      locked: false

- type: entity
  parent: GasCanister
  id: StorageCanister
  name: "каністра для зберігання"
  components:
    - type: Sprite
      layers:
        - state: yellow
    - type: GasCanister
      gasMixture:
        volume: 1000
        moles: # List of gasses for easy reference
          - 0 # oxygen
          - 0 # nitrogen
          - 0 # CO2
          - 0 # Plasma
          - 0 # Tritium
          - 0 # Water vapor
          - 0 # Ammonia
          - 0 # N2O
          - 0 # Frezon
        temperature: 293.15
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 600
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 300
        behaviors:
        - !type:PlaySoundBehavior
          sound:
            collection: MetalBreak
        - !type:SpawnEntitiesBehavior
          spawn:
            StorageCanisterBroken:
              min: 1
              max: 1
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
        - !type:DumpCanisterBehavior

# Filled canisters, contain 1871.71051 moles each

- type: entity
  parent: GasCanister
  id: AirCanister
  name: "каністра з повітрям"
  description: "Каністра, яка може містити будь-який тип газу. Цей має містити повітряну суміш. Його можна прикріпити до портів роз'єму за допомогою гайкового ключа."
  components:
  - type: Sprite
    layers:
      - state: grey
  - type: GasCanister
    gasMixture:
      volume: 1000
      moles:
        - 393.0592071 # oxygen 21%
        - 1478.6513029 # nitrogen 79%
      temperature: 293.15
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 600
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          AirCanisterBroken:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:DumpCanisterBehavior

- type: entity
  parent: GasCanister
  id: OxygenCanister
  name: "кисневий балон"
  description: "Каністра, яка може містити будь-який тип газу. Цей має містити кисень. Його можна прикріпити до портів роз'єму за допомогою ключа."
  components:
  - type: Sprite
    layers:
      - state: blue
  - type: GasCanister
    gasMixture:
      volume: 1000
      moles:
        - 1871.71051 # oxygen
      temperature: 293.15
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 600
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          OxygenCanisterBroken:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:DumpCanisterBehavior

- type: entity
  id: LiquidOxygenCanister
  parent: OxygenCanister
  name: "каністра з рідким киснем"
  description: "Каністра, яка може містити будь-який тип газу. Цей балон має містити рідкий кисень. Його можна прикріпити до портів роз'єму за допомогою ключа."
  components:
  - type: GasCanister
    gasMixture:
      volume: 1000
      moles:
        - 18710.71051 # oxygen
      temperature: 72
  - type: AccessReader
    access: [["Atmospherics"]]

- type: entity
  parent: GasCanister
  id: NitrogenCanister
  name: "каністра з азотом"
  description: "Каністра, яка може містити будь-який тип газу. Цей має містити азот. Його можна прикріпити до портів роз'єму за допомогою ключа."
  components:
    - type: Sprite
      layers:
        - state: red
    - type: GasCanister
      gasMixture:
        volume: 1000
        moles:
          - 0 # oxygen
          - 1871.71051 # nitrogen
        temperature: 293.15
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 600
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 300
        behaviors:
        - !type:PlaySoundBehavior
          sound:
            collection: MetalBreak
        - !type:SpawnEntitiesBehavior
          spawn:
            NitrogenCanisterBroken:
              min: 1
              max: 1
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
        - !type:DumpCanisterBehavior

- type: entity
  id: LiquidNitrogenCanister
  parent: NitrogenCanister
  name: "каністра з рідким азотом"
  description: "Каністра, яка може містити будь-який тип газу. Цей балон має містити рідкий азот. Його можна прикріпити до портів роз'єму за допомогою ключа."
  components:
  - type: GasCanister
    gasMixture:
      volume: 1000
      moles:
        - 0 # oxygen
        - 18710.71051 # nitrogen
      temperature: 72
  - type: AccessReader
    access: [["Atmospherics"]]

- type: entity
  parent: GasCanister
  id: CarbonDioxideCanister
  name: "каністра з вуглекислим газом"
  description: "Каністра, яка може містити будь-який тип газу. Передбачається, що він містить вуглекислий газ. Його можна прикріпити до портів роз'єму за допомогою ключа."
  components:
    - type: Sprite
      layers:
        - state: black
    - type: GasCanister
      gasMixture:
        volume: 1000
        moles:
          - 0 # oxygen
          - 0 # nitrogen
          - 1871.71051 # CO2
        temperature: 293.15
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 600
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 300
        behaviors:
        - !type:PlaySoundBehavior
          sound:
            collection: MetalBreak
        - !type:SpawnEntitiesBehavior
          spawn:
            CarbonDioxideCanisterBroken:
              min: 1
              max: 1
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
        - !type:DumpCanisterBehavior
    - type: Lock
      locked: true

- type: entity
  id: LiquidCarbonDioxideCanister
  parent: CarbonDioxideCanister
  name: "балон з рідким вуглекислим газом"
  description: "Каністра, яка може містити будь-який тип газу. Цей балон має містити рідкий вуглекислий газ. Його можна прикріпити до портів роз'єму за допомогою ключа."
  components:
  - type: GasCanister
    gasMixture:
      volume: 1000
      moles:
        - 0 # oxygen
        - 0 # nitrogen
        - 18710.71051 # CO2
      temperature: 72
  - type: AccessReader
    access: [["Atmospherics"]]

- type: entity
  parent: GasCanister
  id: PlasmaCanister
  name: "каністра з плазмою"
  description: "Каністра, яка може містити будь-який тип газу. Цей балон має містити плазму. Його можна прикріпити до портів роз'єму за допомогою ключа."
  components:
    - type: Sprite
      layers:
        - state: orange
    - type: GasCanister
      gasMixture:
        volume: 1000
        moles:
          - 0 # oxygen
          - 0 # nitrogen
          - 0 # carbon dioxide
          - 1871.71051 # plasma
        temperature: 293.15
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 600
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 300
        behaviors:
        - !type:PlaySoundBehavior
          sound:
            collection: MetalBreak
        - !type:SpawnEntitiesBehavior
          spawn:
            PlasmaCanisterBroken:
              min: 1
              max: 1
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
        - !type:DumpCanisterBehavior
    - type: Lock
      locked: true

- type: entity
  parent: GasCanister
  id: TritiumCanister
  name: "каністра з тритієм"
  description: "Каністра, яка може містити будь-який тип газу. Цей має містити тритій. Його можна прикріпити до портів роз'єму за допомогою ключа."
  components:
    - type: Sprite
      layers:
        - state: green
    - type: GasCanister
      gasMixture:
        volume: 1000
        moles:
          - 0 # oxygen
          - 0 # nitrogen
          - 0 # CO2
          - 0 # Plasma
          - 1871.71051 # Tritium
        temperature: 293.15
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 600
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 300
        behaviors:
        - !type:PlaySoundBehavior
          sound:
            collection: MetalBreak
        - !type:SpawnEntitiesBehavior
          spawn:
            TritiumCanisterBroken:
              min: 1
              max: 1
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
        - !type:DumpCanisterBehavior
    - type: Lock
      locked: true

- type: entity
  parent: GasCanister
  id: WaterVaporCanister
  name: "каністра з водяною парою"
  description: "Каністра, яка може містити будь-який тип газу. Цей має містити водяну пару. Його можна прикріпити до портів роз'єму за допомогою ключа."
  components:
    - type: Sprite
      layers:
        - state: water_vapor
    - type: GasCanister
      gasMixture:
        volume: 1000
        moles:
          - 0 # oxygen
          - 0 # nitrogen
          - 0 # CO2
          - 0 # Plasma
          - 0 # Tritium
          - 1871.71051 # Water vapor
        temperature: 293.15
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 600
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 300
        behaviors:
        - !type:PlaySoundBehavior
          sound:
            collection: MetalBreak
        - !type:SpawnEntitiesBehavior
          spawn:
            WaterVaporCanisterBroken:
              min: 1
              max: 1
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
        - !type:DumpCanisterBehavior

- type: entity
  parent: GasCanister
  id: AmmoniaCanister
  name: "каністра для аміаку"
  description: "Каністра, яка може містити будь-який тип газу. Ця має містити аміак. Її можна прикріпити до портів роз'єму за допомогою ключа."
  components:
    - type: Sprite
      layers:
        - state: greenys
    - type: GasCanister
      gasMixture:
        volume: 1000
        moles:
          - 0 # oxygen
          - 0 # nitrogen
          - 0 # CO2
          - 0 # Plasma
          - 0 # Tritium
          - 0 #  Water vapor
          - 1871.71051 # Ammonia
        temperature: 293.15
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 600
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 300
        behaviors:
        - !type:PlaySoundBehavior
          sound:
            collection: MetalBreak
        - !type:SpawnEntitiesBehavior
          spawn:
            AmmoniaCanisterBroken:
              min: 1
              max: 1
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
        - !type:DumpCanisterBehavior
    - type: Lock
      locked: true

- type: entity
  parent: GasCanister
  id: NitrousOxideCanister
  name: "каністра із закисом азоту"
  description: "Балончик, який може містити будь-який тип газу. Цей балон має містити закис азоту. Його можна прикріпити до портів роз'єму за допомогою гайкового ключа."
  components:
    - type: Sprite
      layers:
        - state: redws
    - type: GasCanister
      gasMixture:
        volume: 1000
        moles:
          - 0 # oxygen
          - 0 # nitrogen
          - 0 # CO2
          - 0 # Plasma
          - 0 # Tritium
          - 0 #  Water vapor
          - 0 # Ammonia
          - 1871.71051 # N2O
        temperature: 293.15
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 600
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 300
        behaviors:
        - !type:PlaySoundBehavior
          sound:
            collection: MetalBreak
        - !type:SpawnEntitiesBehavior
          spawn:
            NitrousOxideCanisterBroken:
              min: 1
              max: 1
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
        - !type:DumpCanisterBehavior

- type: entity
  parent: GasCanister
  id: FrezonCanister
  name: "каністра з фреоном"
  description: "Каністра, яка може містити будь-який тип газу. Цей балон має містити фреон. Його можна прикріпити до портів роз'єму за допомогою гайкового ключа."
  components:
  - type: Sprite
    layers:
    - state: frezon
  - type: GasCanister
    gasMixture:
      volume: 1000
      moles:
      - 0 # oxygen
      - 0 # nitrogen
      - 0 # CO2
      - 0 # Plasma
      - 0 # Tritium
      - 0 # Water vapor
      - 0 # Ammonia
      - 0 # N2O
      - 1871.71051 # Frezon
      temperature: 293.15
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 600
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          FrezonCanisterBroken:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:DumpCanisterBehavior
  - type: Lock
    locked: true

# Broke Entities

- type: entity
  parent: BaseStructureDynamic
  id: GasCanisterBrokenBase
  name: "розбита газова каністра"
  description: "Розбитий газовий балончик. Ще не марний, адже його можна переробити на високоякісні матеріали."
  components:
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 200
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors:
        - !type:PlaySoundBehavior
          sound:
            collection: MetalBreak
        - !type:SpawnEntitiesBehavior
          spawn:
            SheetPlasteel1:
              min: 1
              max: 2
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: Metallic
    - type: InteractionOutline
    - type: Sprite
      sprite: Structures/Storage/canister.rsi
      state: grey-1
    - type: Physics
      bodyType: Dynamic
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.4,-0.3,0.4,0.3"
          density: 190
          mask:
          - MachineMask
          layer:
          - MachineLayer
    - type: PhysicalComposition
      materialComposition:
        Steel: 500
    - type: StaticPrice
      price: 60

- type: entity
  parent: GasCanisterBrokenBase
  id: StorageCanisterBroken
  categories: [ HideSpawnMenu ]
  components:
    - type: Sprite
      state: yellow-1

- type: entity
  parent: GasCanisterBrokenBase
  id: AirCanisterBroken
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    state: grey-1

- type: entity
  parent: GasCanisterBrokenBase
  id: OxygenCanisterBroken
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    state: blue-1

- type: entity
  parent: GasCanisterBrokenBase
  id: NitrogenCanisterBroken
  categories: [ HideSpawnMenu ]
  components:
    - type: Sprite
      state: red-1

- type: entity
  parent: GasCanisterBrokenBase
  id: CarbonDioxideCanisterBroken
  categories: [ HideSpawnMenu ]
  components:
    - type: Sprite
      state: black-1

- type: entity
  parent: GasCanisterBrokenBase
  id: PlasmaCanisterBroken
  categories: [ HideSpawnMenu ]
  components:
    - type: Sprite
      state: orange-1

- type: entity
  parent: GasCanisterBrokenBase
  id: TritiumCanisterBroken
  categories: [ HideSpawnMenu ]
  components:
    - type: Sprite
      state: green-1

- type: entity
  parent: GasCanisterBrokenBase
  id: WaterVaporCanisterBroken
  name: "розбита каністра з водяною парою"
  categories: [ HideSpawnMenu ]
  components:
    - type: Sprite
      state: water_vapor-1

- type: entity
  parent: GasCanisterBrokenBase
  id: AmmoniaCanisterBroken
  categories: [ HideSpawnMenu ]
  components:
    - type: Sprite
      state: greenys-1

- type: entity
  parent: GasCanisterBrokenBase
  id: NitrousOxideCanisterBroken
  categories: [ HideSpawnMenu ]
  components:
    - type: Sprite
      state: redws-1

- type: entity
  parent: GasCanisterBrokenBase
  id: FrezonCanisterBroken
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    state: frezon-1

