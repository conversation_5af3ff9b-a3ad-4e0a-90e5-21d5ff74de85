﻿- type: entity
  id: CultRuneBase
  name: "на основі руни"
  abstract: true
  placement:
    mode: SnapgridCenter
  components:
  - type: Fixtures
    fixtures:
      rune:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.4,0.4,0.4"
        hard: false
        mask:
        - ItemMask
        layer:
        - SlipLayer
  - type: Physics
  - type: Clickable
  - type: Sprite
    sprite: WhiteDream/BloodCult/Entities/Runes/regular.rsi
    state: "offering"
  - type: CultRuneBase
  - type: Appearance

- type: entity
  parent: CultRuneBase
  id: CultRuneOffering
  name: "руна підношення"
  description: "Пропонує некультиста, що знаходиться над ним, Нар'сі, або навертаючи його, або приносячи в жертву. Один культист потрібен для мертвої жертви, два - для навернення і три - для живих жертв і цілей для жертвоприношень."
  components:
  - type: Sprite
    state: "offering"
  - type: CultRuneBase
    invokePhrase: "Mah'weyh pleggh at e'ntrath!"
  - type: CultRuneOffering

- type: entity
  parent: CultRuneBase
  id: CultRuneEmpower
  name: "руна влади"
  description: "Дозволяє культистам готувати більшу кількість магії крові за набагато меншу ціну."
  components:
  - type: Sprite
    state: strength
  - type: CultRuneBase
    invokePhrase: "Qu'laris ver'don, thal'sorin mik'thar!"
  - type: CultRuneEmpower

- type: entity
  parent: CultRuneBase
  id: CultRuneTeleport
  name: "руна телепортації"
  description: "Викривляє все, що знаходиться над нею, на іншу обрану руну телепорту"
  components:
  - type: Sprite
    state: teleport
  - type: CultRuneBase
    invokePhrase: "Sas'so c'arta forbici!"
  - type: CultRuneTeleport
  - type: UserInterface
    interfaces:
      enum.ListViewSelectorUiKey.Key:
        type: ListViewSelectorBUI
      enum.NameSelectorUiKey.Key:
        type: NameSelectorBUI

- type: entity
  parent: CultRuneBase
  id: CultRuneRevive
  name: "руна омолодження"
  description: "Потрібно покласти на руну мертвого, несвідомого або бездіяльного культиста. За умови достатньої кількості жертвоприношень, він отримає нове життя."
  components:
  - type: Sprite
    state: revive
  - type: CultRuneBase
    invokePhrase: "Pasnar val'keriam usinar. Savrae ines amutan. Yam'toth remium il'tarat!"
  - type: CultRuneRevive

- type: entity
  parent: CultRuneBase
  id: CultRuneBarrier
  name: "руна бар'єру"
  description: "При виклику створює тимчасову невидиму стіну, яка блокує прохід."
  components:
  - type: Sprite
    state: barrier
  - type: CultRuneBase
    invokePhrase: "Khari'd! Eske'te tannin!"
    runeActivationRange: 1.5
    activationDamage:
      types:
        Slash: 5
  - type: CultRuneBarrier

- type: entity
  parent: CultRuneBase
  id: CultRuneSummoning
  name: "руна виклику"
  description: "Прикликає одного культиста до руни. Вимагає 2 інвокаторів."
  components:
  - type: Sprite
    state: summon
  - type: CultRuneBase
    requiredInvokers: 3
    invokePhrase: "N'ath reth sh'yro eth d'rekkathnor!"
  - type: CultRuneSummon
  - type: UserInterface
    interfaces:
      enum.ListViewSelectorUiKey.Key:
        type: ListViewSelectorBUI

- type: entity
  parent: CultRuneBase
  id: CultRuneBloodBoil
  name: "руна киплячої крові"
  description: "Закипає кров невіруючих, які бачать руну, швидко завдаючи надзвичайної шкоди. Вимагає 3 інвокаторів."
  components:
  - type: Sprite
    state: blood_boil
  - type: CultRuneBase
    invokePhrase: "N'Dedo ol'btoh!"
    requiredInvokers: 3
    activationDamage:
      types:
        Slash: 35
  - type: CultRuneBloodBoil

- type: entity
  parent: CultRuneBase
  id: CultRuneApocalypse
  name: "руна апокаліпсису"
  description: "Провісник кінця світу. Зростає в силі з відчаєм культу - але з ризиком... побічних ефектів. Потрібні 3 викликають."
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/Entities/Runes/apocalypse.rsi
    layers:
      - state: icon
        map: [ "enum.ApocalypseRuneVisuals.Layer" ]
  - type: CultRuneBase
    requiredInvokers: 3
    invokePhrase: "Ta'gh fara'qha fel d'amar det!"
    triggerRendingMarkers: true
    canBeErased: false
    activationDamage:
      types:
        Slash: 35
  - type: CultRuneApocalypse
  - type: GenericVisualizer
    visuals:
      enum.ApocalypseRuneVisuals.Used:
        enum.ApocalypseRuneVisuals.Layer:
          True: { color: "#696969" }

- type: entity
  parent: CultRuneBase
  id: CultRuneDimensionalRending
  name: "руна просторової візуалізації"
  description: "Руйнує просторові бар'єри, закликаючи Геометра. Потрібно 10 призовників"
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/Entities/Runes/dimensional_rending.rsi
    layers:
    - state: rune
      map: [ "enum.RendingRuneVisuals.Layer" ]
  - type: CultRuneBase
    requiredInvokers: 8
    invokeChatType: Speak
    invokePhrase: "TOK-LYR RQA-NAP G'OLT-ULOFT!!!"
    triggerRendingMarkers: true
    canBeErased: false
  - type: CultRuneRending
  - type: GenericVisualizer
    visuals:
      enum.RendingRuneVisuals.Active:
        enum.RendingRuneVisuals.Layer:
          True: { state: "rune_animated" }
          False: { state: "rune"}
