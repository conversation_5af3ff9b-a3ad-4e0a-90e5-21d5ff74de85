- type: polymorph
  id: Mouse
  configuration:
    entity: MobMouse
    forced: true
    duration: 30

- type: polymorph
  id: Chicken
  configuration:
    entity: Mob<PERSON><PERSON>ken
    forced: true
    inventory: Drop

- type: polymorph
  id: Monkey
  configuration:
    entity: MobMonkey
    forced: true
    inventory: Drop
    revertOnCrit: true
    revertOnDeath: true

- type: polymorph
  id: WizardForcedCarp
  configuration:
    entity: MobCarpMagic
    forced: true
    inventory: None
    transferName: true
    transferDamage: true
    revertOnCrit: false
    revertOnDeath: true

- type: polymorph
  id: WizardForcedSkeleton
  configuration:
    entity: MobSkeletonPerson
    forced: true
    inventory: Drop
    transferName: true
    transferDamage: true
    revertOnCrit: false
    revertOnDeath: false

- type: polymorph
  id: WizardForcedMonkey
  configuration:
    entity: MobMonkey
    forced: true
    inventory: None
    transferName: true
    transferDamage: true
    revertOnCrit: false
    revertOnDeath: true

- type: polymorph
  id: WizardWallDoor
  configuration:
    entities: WizardWallDoor # Goob edit
    forced: true
    inventory: None
    transferName: false
    transferDamage: false
    revertOnCrit: false
    revertOnDeath: false
    allowMovement: false # Goobstation

- type: polymorph
  id: WizardForcedCluwne
  configuration:
    entity: MobCluwne
    forced: true
    transferName: true
    transferHumanoidAppearance: true
    inventory: Transfer
    revertOnDeath: true

- type: polymorph
  id: SlimeMorphGeras
  configuration:
    entity: MobSlimesGeras
    transferName: true
    transferHumanoidAppearance: false
    inventory: Drop
    transferDamage: true
    revertOnDeath: true
    revertOnCrit: true

# this is a test for transferring some visual appearance stuff
- type: polymorph
  id: TestHumanMorph
  configuration:
    entity: MobHuman
    transferName: true
    transferHumanoidAppearance: true
    inventory: Transfer

- type: polymorph
  id: AMIVMorph
  configuration:
    entity: MobMonkey
    forced: true
    inventory: Transfer
    transferName: true
    revertOnCrit: false
    revertOnDeath: false

- type: polymorph
  id: BreadMorph
  configuration:
    entity: FoodBreadPlain
    forced: true
    inventory: None
    transferName: false
    transferDamage: true
    revertOnCrit: false
    revertOnDeath: true
    revertOnEat: true

- type: polymorph
  id: TreeMorph
  configuration:
    entity: FloraTree01
    forced: true
    transferName: true
    revertOnDeath: true
    inventory: Drop
    cooldown: 160

# this is the monkey polymorph for artifact.
- type: polymorph
  id: ArtifactMonkey
  configuration:
    entity: MobMonkey
    forced: true
    transferName: true
    allowRepeatedMorphs: true
    inventory: Transfer
    revertOnCrit: true
    revertOnDeath: true
    duration: 20

- type: polymorph
  id: ArtifactCluwne
  configuration:
    entity: MobCluwne
    forced: true
    transferName: true
    transferHumanoidAppearance: true
    inventory: None
    revertOnDeath: true
    revertOnCrit: true
    duration: 30

- type: polymorph
  id: ArtifactLizard
  configuration:
    entity: MobLizard
    forced: true
    transferName: true
    transferHumanoidAppearance: true
    inventory: None
    revertOnDeath: true
    revertOnCrit: true
    duration: 20

- type: polymorph
  id: ArtifactLuminous
  configuration:
    entity: MobLuminousPerson
    forced: true
    transferName: true
    transferHumanoidAppearance: true
    inventory: None
    revertOnDeath: true
    revertOnCrit: true
    duration: 20

# Alien evolutions

- type: polymorph
  id: AlienEvolutionGrowStageTwo
  configuration:
    entity: MobAlienLarvaGrowStageTwo
    forced: true
    transferName: true
    revertOnDeath: false
    revertOnCrit: false
    allowRepeatedMorphs: true

- type: polymorph
  id: AlienLarvaGrow
  configuration:
    entity: MobAlienLarva
    forced: true
    transferName: true
    revertOnDeath: false
    revertOnCrit: false
    allowRepeatedMorphs: true

- type: polymorph
  id: AlienEvolutionGrowStageThree
  configuration:
    entity: MobAlienLarvaGrowStageThree
    forced: true
    transferName: true
    revertOnDeath: false
    revertOnCrit: false
    allowRepeatedMorphs: true

- type: polymorph
  id: AlienEvolutionDrone
  configuration:
    entity: MobAlienDrone
    forced: true
    transferName: false
    revertOnDeath: false
    revertOnCrit: false
    allowRepeatedMorphs: true

- type: polymorph
  id: AlienEvolutionSentinel
  configuration:
    entity: MobAlienSentinel
    forced: true
    transferName: false
    revertOnDeath: false
    revertOnCrit: false
    allowRepeatedMorphs: true

- type: polymorph
  id: AlienEvolutionPraetorian
  configuration:
    entity: MobAlienPraetorian
    forced: true
    transferName: false
    revertOnDeath: false
    revertOnCrit: false
    allowRepeatedMorphs: true

- type: polymorph
  id: AlienEvolutionHunter
  configuration:
    entity: MobAlienHunter
    forced: true
    transferName: false
    revertOnDeath: false
    revertOnCrit: false
    allowRepeatedMorphs: true

- type: polymorph
  id: AlienEggGrowth
  configuration:
    entity: AlienEgg
    forced: true
    transferName: false
    revertOnDeath: false
    revertOnCrit: false
    allowRepeatedMorphs: true

- type: polymorph
  id: AlienEggHatch
  configuration:
    entity: AlienEggHatching
    forced: true
    transferName: false
    revertOnDeath: false
    revertOnCrit: false
    allowRepeatedMorphs: true

- type: polymorph
  id: AlienEvolutionQueen
  configuration:
    entity: MobAlienQueen
    forced: true
    transferName: false
    revertOnDeath: false
    revertOnCrit: false
    allowRepeatedMorphs: true

# Deactivating facehugger

- type: polymorph
  id: FacehuggerToInactive
  configuration:
    entity: FacehuggerInactive
    forced: true
    transferName: false
    revertOnDeath: false
    revertOnCrit: false
    allowRepeatedMorphs: true
# Polymorphs for Wizards polymorph self spell
- type: polymorph
  id: WizardSpider
  configuration:
    entity: MobGiantSpiderWizard #Not angry so ghosts can't just take over the wizard
    transferName: true
    inventory: None
    revertOnDeath: true
    revertOnCrit: true

- type: polymorph
  id: WizardRod
  configuration:
    entity: ImmovableRodWizard #CLANG
    transferName: true
    transferDamage: false
    inventory: None
    duration: 2 # Goob edit
    allowMovement: false # Goobstation
    forced: true
    revertOnCrit: false
    revertOnDeath: false
    attachToGridOrMap: true # Goobstation

# Temporary Jaunt
# Don't make permanent jaunts until action system can be reworked to allow do afters and cooldown pausing
- type: polymorph
  id: Jaunt
  configuration:
    entity: EtherealJaunt
    transferName: true
    inventory: None
    forced: true
    revertOnDeath: true
    revertOnCrit: true
    allowRepeatedMorphs: false
    # polymorphSound: /Audio/Magic/ethereal_enter.ogg # Goob edit
    # exitPolymorphSound: /Audio/Magic/ethereal_exit.ogg # Goob edit
    duration: 5 # Goob edot
    showPopup: false # Goobstation
    attachToGridOrMap: true # Goobstation

#Vampire
- type: polymorph
  id: VampireMouse
  configuration:
    entity: MobMouse
    revertOnDeath: true
    revertOnCrit: true

- type: polymorph
  id: VampireBat
  configuration:
    entity: MobBat
    revertOnDeath: true
    revertOnCrit: true
