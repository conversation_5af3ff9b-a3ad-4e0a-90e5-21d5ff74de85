<DefaultWindow xmlns="https://spacestation14.io"
               xmlns:ui="clr-namespace:Content.Client.UserInterface.Controls"
               Title="{Loc language-menu-window-title}"
               SetSize="300 300">
    <BoxContainer Orientation="Vertical" SeparationOverride="4" MinWidth="150">
        <PanelContainer Name="CurrentLanguageContainer" Access="Public" StyleClasses="PdaBorderRect">
            <Label Name="CurrentLanguageLabel" Access="Public" Text="Current Language:" HorizontalExpand="True"></Label>
        </PanelContainer>

        <ui:HLine></ui:HLine>

        <ScrollContainer HorizontalExpand="True" VerticalExpand="True" HScrollEnabled="False" VScrollEnabled="True" MinHeight="50">
            <BoxContainer Name="OptionsList" Access="Public" HorizontalExpand="True" SeparationOverride="2" Orientation="Vertical">
                <!-- The rest here is generated programmatically -->
            </BoxContainer>
        </ScrollContainer>
    </BoxContainer>
</DefaultWindow>
