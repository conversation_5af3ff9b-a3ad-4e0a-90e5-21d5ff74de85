- type: entity
  id: MobIPC
  parent: PlayerSiliconHumanoidBase
  name: "Уріст МакПозитронік"
  description: "Позитронний мозок у металевому тілі."
  components:
  - type: PowerCellSlot
    cellSlotId: cell_slot
    fitsInCharger: true
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellMedium
  - type: ItemSlotsRequirePanel
    slots:
      cell_slot: true
  - type: EncryptionHolderRequiresLock
  - type: SiliconEmitSoundOnDrained
    sound: "/Audio/Weapons/Guns/EmptyAlarm/smg_empty_alarm.ogg"
    minInterval: 15
    maxInterval: 30
    popUp: "silicon-power-low"
  - type: WiresPanel
    openDelay: 5
  - type: LockedWiresPanel
  - type: Lock
    locked: true
    lockOnClick: false
    unlockOnClick: false
    breakOnEmag: false
  - type: AccessReader
    breakOnEmag: true
    ownerHasAccess: true
  - type: NpcFactionMember
    factions:
      - NanoTrasen
  - type: StandingState
  - type: MobState
    allowedStates:
      - Alive
      - Critical
      - Dead
  - type: MobThresholds
    thresholds:
      0: Alive
      119.999: Critical # TO make it almost impossible
      120: Dead
    stateAlertDict:
      Alive: BorgHealth
      Critical: BorgCrit
      Dead: BorgDead
  - type: TypingIndicator
    proto: robot
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTypeTrigger
          damageType: Blunt
          damage: 400
        behaviors:
          - !type:GibBehavior { }
  - type: SlowOnDamage
    speedModifierThresholds:
      60: 0.75
      90: 0.5
      120: 0.3
  - type: SiliconDownOnDead
  - type: Inventory
    templateId: ipc
  - type: GuideHelp
    guides:
      - IPCs
  - type: Silicon
    entityType: enum.SiliconType.Player
    batteryPowered: true
    drainPerSecond: 1.5
    chargeThresholdMid: 0.80
    chargeThresholdLow: 0.35
    chargeThresholdCritical: 0.10
    speedModifierThresholds:
      4: 1
      3: 1
      2: 0.80
      1: 0.45
      0: 0.00
  - type: BatteryDrinker
  - type: EncryptionKeyHolder
    keySlots: 4
    examineWhileLocked: false
    keysExtractionMethod: Cutting
    keysUnlocked: false
  - type: ActiveRadio
  - type: IntrinsicRadioReceiver
  - type: IntrinsicRadioTransmitter
  - type: DeadStartupButton
    sound:
      path: /Audio/Effects/Silicon/startup.ogg
  - type: EmitBuzzWhileDamaged
  - type: CanHostGuardian
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    - RobotTalk
    understands:
    - TauCetiBasic
    - RobotTalk
  - type: WeldingHealable
  - type: PsionicInsulation
  - type: OfferItem
  - type: LayingDown
  - type: Carriable
  - type: StatusIcon
    bounds: -0.5,-0.5,0.5,0.5
  - type: StepTriggerImmune
    whitelist:
      types:
      - Shard
  - type: Fixtures
    fixtures: # TODO: This needs a second fixture just for mob collisions.
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 185
        restitution: 0.0
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Tag
    tags:
    - CanPilot
    - FootstepSound
    - DoorBumpOpener
    - SiliconEmotes
    - SiliconMob

- type: entity
  save: false
  name: "Уріст МакПозитронік"
  parent: MobHumanDummy
  id: MobIPCDummy
  categories: [ HideSpawnMenu ]
  description: "Манекен ІПШ, призначений для використання в налаштуванні персонажа."
  components:
  - type: HumanoidAppearance
    species: IPC
  - type: Inventory
    templateId: ipc
