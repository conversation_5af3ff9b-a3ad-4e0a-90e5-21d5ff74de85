# DeltaV Mail
- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailBooksAll # See Resources/Prototypes/Nyanotrasen/Entities/Markers/Spawners/Random/books.yml
  suffix: books
  components:
  - type: Mail
    contents:
#    # Author Books
#    - id: BookNarsieLegend
#      prob: 0.02
#      orGroup: bookauthor
#    - id: BookTruth
#      prob: 0.02
#      orGroup: bookauthor
#    - id: BookWorld
#      prob: 0.1
#      orGroup: bookauthor
#    - id: BookIanAntarctica
#      prob: 0.05
#      orGroup: bookauthor
#    - id: BookSlothClownSSS
#      prob: 0.04
#      orGroup: bookauthor
#    - id: BookSlothClownPranks
#      prob: 0.04
#      orGroup: bookauthor
#    - id: BookSlothClownMMD
#      prob: 0.04
#      orGroup: bookauthor
#    - id: BookStruck
#      prob: 0.1
#      orGroup: bookauthor
#    - id: BookSun
#      prob: 0.1
#      orGroup: bookauthor
#    - id: BookPossum
#      prob: 0.1
#      orGroup: bookauthor
#    - id: BookCafe
#      prob: 0.1
#      orGroup: bookauthor
#    - id: BookFeather
#      prob: 0.1
#      orGroup: bookauthor
#    - id: BookIanLostWolfPup
#      prob: 0.05
#      orGroup: bookauthor
#    - id: BookIanRanch
#      prob: 0.05
#      orGroup: bookauthor
#    - id: BookIanOcean
#      prob: 0.05
#      orGroup: bookauthor
#    - id: BookIanMountain
#      prob: 0.05
#      orGroup: bookauthor
#    - id: BookIanCity
#      prob: 0.05
#      orGroup: bookauthor
#    - id: BookIanArctic
#      prob: 0.05
#      orGroup: bookauthor
#    - id: BookIanDesert
#      prob: 0.05
#      orGroup: bookauthor
#    - id: BookNames
#      prob: 0.08
#      orGroup: bookauthor
#    - id: BookEarth
#      prob: 0.08
#      orGroup: bookauthor
#    - id: BookTemple
#      prob: 0.09
#      orGroup: bookauthor
#    - id: BookAurora
#      prob: 0.1
#      orGroup: bookauthor
#    - id: BookWatched
#      prob: 0.04
#      orGroup: bookauthor
#    - id: BookMedicalOfficer
#      prob: 0.06
#      orGroup: bookauthor
#    - id: BookMorgue
#      prob: 0.06
#      orGroup: bookauthor
#    - id: BookRufus
#      prob: 0.1
#      orGroup: bookauthor
#    - id: BookMap
#      prob: 0.08
#      orGroup: bookauthor
#    - id: BookJourney
#      prob: 0.1
#      orGroup: bookauthor
#    - id: BookInspiration
#      prob: 0.09
#      orGroup: bookauthor
#    - id: BookRandom # The "spawn nothing" in the 'orGroup'
#      prob: 0.3
#      orGroup: bookauthor
    # id: Guidebook Books
    - id: BookSpaceEncyclopedia
      prob: 0.2
      orGroup: bookguide
    - id: BookTheBookOfControl
      prob: 0.2
      orGroup: bookguide
    - id: BookBartendersManual
      prob: 0.2
      orGroup: bookguide
    - id: BookHowToCookForFortySpaceman
      prob: 0.2
      orGroup: bookguide
    - id: BookLeafLoversSecret
      prob: 0.2
      orGroup: bookguide
    - id: BookEngineersHandbook
      prob: 0.2
      orGroup: bookguide
    - id: BookScientistsGuidebook
      prob: 0.15
      orGroup: bookguide
    - id: BookSecurity
      prob: 0.2
      orGroup: bookguide
    - id: BookHowToKeepStationClean
      prob: 0.2
      orGroup: bookguide
    - id: BookHowToRockAndStone
      prob: 0.2
      orGroup: bookguide
    - id: BookMedicalReferenceBook
      prob: 0.2
      orGroup: bookguide
    - id: BookHowToSurvive
      prob: 0.3
      orGroup: bookguide
    - id: BookChemicalCompendium
      prob: 0.15
      orGroup: bookguide
    - id: BookRandom # The "spawn nothing" in the 'orGroup'
      prob: 0.5
      orGroup: bookguide
#    # Other Books
#    - id: BookEscalation
#      prob: 0.2
#      orGroup: bookother
#    - id: BookEscalationSecurity
#      prob: 0.15
#      orGroup: bookother
#    - id: BookAtmosDistro
#      prob: 0.1
#      orGroup: bookother
#    - id: BookAtmosWaste
#      prob: 0.1
#      orGroup: bookother
#    - id: BookAtmosAirAlarms
#      prob: 0.1
#      orGroup: bookother
#    - id: BookAtmosVentsMore
#      prob: 0.1
#      orGroup: bookother
#    - id: BookRandom # The "spawn nothing" in the 'orGroup'
#      prob: 0.7
#      orGroup: bookother

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailPumpkinPie
  suffix: pumpkinpie
  components:
  - type: Mail
    contents:
    - id: FoodPiePumpkin

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailDVCosplayFakeWizard
  suffix: cosplay-wizard, fake as fuck
  components:
  - type: Mail
    contents:
    - id: ClothingOuterWizardFake
    - id: ClothingHeadHatWizardFake
    - id: ClothingShoesWizardFake
    - id: FoodBurgerSpell
      orGroup: FakeWizard
      prob: 0.3
    - id: FoodKebabSkewer
      orGroup: FakeWizard
      prob: 0.1

- type: entity
  parent: BaseMail
  id: MailDVScarves
  suffix: scarves
  components:
  - type: Mail
    contents:
    - id: ClothingNeckScarfStripedRed
      orGroup: Scarf
    - id: ClothingNeckScarfStripedBlue
      orGroup: Scarf
    - id: ClothingNeckScarfStripedGreen
      orGroup: Scarf
    - id: ClothingNeckScarfStripedBlack
      orGroup: Scarf
    - id: ClothingNeckScarfStripedBrown
      orGroup: Scarf
    - id: ClothingNeckScarfStripedLightBlue
      orGroup: Scarf
    - id: ClothingNeckScarfStripedOrange
      orGroup: Scarf
    - id: ClothingNeckScarfStripedPurple
      orGroup: Scarf
    - id: ClothingNeckScarfStripedZebra
      orGroup: Scarf
    - id: ClothingNeckScarfStripedSyndieGreen
      orGroup: Scarf
      prob: 0.25
    - id: ClothingNeckScarfStripedSyndieRed
      orGroup: Scarf
      prob: 0.25
    - id: ClothingNeckScarfStripedCentcom
      orGroup: Scarf
      prob: 0.001

- type: entity
  parent: BaseMailLarge
  id: MailDVBoxes
  suffix: boxes
  components:
  - type: Mail
    contents:
#    - id: BoxEnvelope # TODO: we do not have this on EE?
#      orGroup: Box
    - id: BoxCardboard
      orGroup: Box
    - id: BoxMRE
      orGroup: Box
    - id: BoxPerformer
      orGroup: Box
    - id: BoxFlare
      orGroup: Box
    - id: BoxDarts
      orGroup: Box
    - id: BoxMousetrap
      orGroup: Box



# Frontier Mail, including Extended Nyano Mail. (Pony Express?)
- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFAlcohol
  suffix: alcohol, extended
  components:
  - type: Mail
    isFragile: true
    contents:
      # 12.5 overall weight, 8% base chance
    - id: DrinkAbsintheBottleFull
      orGroup: Drink
    - id: DrinkBlueCuracaoBottleFull
      orGroup: Drink
    - id: DrinkCoffeeLiqueurBottleFull
      orGroup: Drink
    - id: DrinkGinBottleFull
      orGroup: Drink
    - id: DrinkMelonLiquorBottleFull
      orGroup: Drink
    - id: DrinkRumBottleFull
      orGroup: Drink
    - id: DrinkTequilaBottleFull
      orGroup: Drink
    - id: DrinkVermouthBottleFull
      orGroup: Drink
    - id: DrinkVodkaBottleFull
      orGroup: Drink
    - id: DrinkWhiskeyBottleFull
      orGroup: Drink
    - id: DrinkWineBottleFull
      orGroup: Drink
    - id: DrinkChampagneBottleFull
      orGroup: Drink
      prob: 0.5
    - id: DrinkGildlagerBottleFull
      orGroup: Drink
      prob: 0.5
    - id: DrinkPatronBottleFull
      orGroup: Drink
      prob: 0.5
    - id: DrinkGlass
      amount: 2

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMailLarge
  id: MailNFBible
  suffix: bible, extended
  components:
  - type: Mail
    contents:
    - id: Bible
    - id: ClothingHeadHatWitch1 #DeltaV - Compensates for items that don't exist here
    - id: ClothingOuterCoatMNKBlackTopCoat #DeltaV - Compensates for items that don't exist here

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFBikeHorn
  suffix: bike horn, random
  components:
  - type: Mail
    contents:
    - id: BikeHorn
      orGroup: Horn
      prob: 0.95
    - id: CluwneHorn
      orGroup: Horn
      prob: 0.05

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMailLarge
  id: MailNFBuildABuddy
  suffix: Build-a-Buddy
  components:
  - type: Mail
    isFragile: true
    contents:
#      - id: BoxBuildABuddyGoblin # DeltaV - Goblins Aren't Real
#        orGroup: Box
      - id: BoxBuildABuddyHuman
        orGroup: Box
      - id: BoxBuildABuddyReptilian
        orGroup: Box
      - id: BoxBuildABuddySlime
        orGroup: Box
      - id: BoxBuildABuddyVulpkanin
        orGroup: Box
      - id: DrinkSpaceGlue
      - id: PaperMailNFBuildABuddy

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMailLarge
  id: MailNFCake
  suffix: cake, extended
  components:
  - type: Mail
    isFragile: true
    isPriority: true
    contents:
      # 14.8 total weight, ~6.8% base chance
    - id: FoodCakeApple
      orGroup: Cake
    - id: FoodCakeBirthday
      orGroup: Cake
    - id: FoodCakeBlueberry
      orGroup: Cake
    - id: FoodCakeCarrot
      orGroup: Cake
    - id: FoodCakeCheese
      orGroup: Cake
    - id: FoodCakeChocolate
      orGroup: Cake
    - id: FoodCakeChristmas
      orGroup: Cake
    - id: FoodCakeClown
      orGroup: Cake
    - id: FoodCakeLemon
      orGroup: Cake
    - id: FoodCakeLime
      orGroup: Cake
    - id: FoodCakeOrange
      orGroup: Cake
    - id: FoodCakePumpkin
      orGroup: Cake
    - id: FoodCakeVanilla
      orGroup: Cake
    # Uncommon
    - id: FoodMothMothmallow
      orGroup: Cake
      prob: 0.5
    - id: FoodCakeSlime
      orGroup: Cake
      prob: 0.5
    # Rare
    - id: FoodCakeBrain
      orGroup: Cake
      prob: 0.25
    - id: FoodCakeLemoon
      orGroup: Cake
      prob: 0.25
    - id: FoodCakeSuppermatter
      orGroup: Cake
      prob: 0.25
    # Ultra rare
    - id: FoodCakeSpaceman
      orGroup: Cake
      prob: 0.05
    - id: KnifePlastic
    - id: ForkPlastic
      amount: 2

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFCosplayWizard
  suffix: cosplay-wizard, extended
  components:
  - type: Mail
    contents:
    - id: ClothingOuterWizard
    - id: ClothingHeadHatWizard
    - id: ClothingShoesWizard
    - id: PonderingOrb
      orGroup: Staff
      prob: 0.3
    - id: RGBStaff
      orGroup: Staff
      prob: 0.1

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFCosplayMaid
  suffix: cosplay-maid, extended
  components:
  - type: Mail
    contents:
      - id: UniformMaid
        orGroup: Uniform
      - id: ClothingUniformJumpskirtJanimaid
        orGroup: Uniform
      - id: ClothingUniformJumpskirtJanimaidmini
        orGroup: Uniform
      - id: MegaSprayBottle
      - id: ClothingHandsGlovesColorWhite

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFCosplayNurse
  suffix: cosplay-nurse, extended
  components:
  - type: Mail
    contents:
      - id: ClothingUniformJumpskirtNurse
      - id: ClothingHeadNurseHat
      - id: Syringe

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFCheese
  suffix: cheese, extended
  components:
  - type: Mail
    isFragile: true
    isPriority: true
    contents:
    - id: FoodCheese
      orGroup: Cheese
      prob: 0.5
    - id: FoodChevre
      orGroup: Cheese
      prob: 0.5
    - id: KnifePlastic

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFCigarettes
  suffix: cigs, random
  components:
  - type: Mail
    contents:
    - id: CigPackBlack
      orGroup: Cigs
      prob: 0.19
    - id: CigPackBlue
      orGroup: Cigs
      prob: 0.19
    - id: CigPackGreen
      orGroup: Cigs
      prob: 0.19
    - id: CigPackRed
      orGroup: Cigs
      prob: 0.19
    - id: CigPackMixed
      orGroup: Cigs
      prob: 0.09 # Pool shared with CigPackMixedMedical, CigPackMixedNasty
    - id: CigPackMixedMedical
      orGroup: Cigs
      prob: 0.05
    - id: CigPackMixedNasty
      orGroup: Cigs
      prob: 0.05
      # Rare
    - id: CigPackSyndicate
      orGroup: Cigs
      prob: 0.05
    - id: CheapLighter

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFEMP
  suffix: emp
  components:
  - type: Mail
    contents:
    - id: DelayedEMP
    - id: PaperMailNFEMPPreparedness

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFSmoke
  suffix: smoke
  components:
  - type: Mail
    contents:
    - id: DelayedSmoke

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFGoldCigars
  suffix: cigars, premium
  components:
  - type: Mail
    contents:
    - id: CigarGoldCase
      orGroup: Cigars
    - id: FlippoLighter
      orGroup: Lighter
      prob: 0.95

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFCookies
  suffix: cookies, random
  components:
  - type: Mail
    isPriority: true
    contents:
      # Cookie 1
    - id: FoodBakedCookie
      orGroup: Cookie1
    - id: FoodBakedCookieOatmeal
      orGroup: Cookie1
    - id: FoodBakedCookieRaisin
      orGroup: Cookie1
    - id: FoodBakedCookieSugar
      orGroup: Cookie1
      # Cookie 2
    - id: FoodBakedCookie
      orGroup: Cookie2
    - id: FoodBakedCookieOatmeal
      orGroup: Cookie2
    - id: FoodBakedCookieRaisin
      orGroup: Cookie2
    - id: FoodBakedCookieSugar
      orGroup: Cookie2
      # Cookie 3
    - id: FoodBakedCookie
      orGroup: Cookie3
    - id: FoodBakedCookieOatmeal
      orGroup: Cookie3
    - id: FoodBakedCookieRaisin
      orGroup: Cookie3
    - id: FoodBakedCookieSugar
      orGroup: Cookie3
      # Cookie 4
    - id: FoodBakedCookie
      orGroup: Cookie4
    - id: FoodBakedCookieOatmeal
      orGroup: Cookie4
    - id: FoodBakedCookieRaisin
      orGroup: Cookie4
    - id: FoodBakedCookieSugar
      orGroup: Cookie4

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFKnife
  suffix: knife, extended
  components:
  - type: Mail
    contents:
    - id: KukriKnife
      orGroup: Knife
    - id: Machete # A little large for an envelope but "we'll live"
      orGroup: Knife
    - id: CombatKnife
      orGroup: Knife
    - id: SurvivalKnife
      orGroup: Knife

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMailLarge
  id: MailNFSword
  suffix: sword
  components:
  - type: Mail
    contents:
    - id: KatanaDulled
      orGroup: Sword
      prob: 0.95
    - id: ClaymoreDulled
      orGroup: Sword
      prob: 0.95
    - id: FoamCutlass
      orGroup: Sword
      prob: 0.95
    - id: Katana
      orGroup: Sword
      prob: 0.5
    - id: Claymore
      orGroup: Sword
      prob: 0.5
#    - id: CaneSheathFilled # TODO: don't have this yet
#      orGroup: Sword
#      prob: 0.3
    - id: Cutlass
      orGroup: Sword
      prob: 0.1
    - id: Kanabou # ah yes, swords
      orGroup: Sword
      prob: 0.1
    - id: ClothingBeltSheathFilled # Little dangerous between the reflect and the damage
      orGroup: Sword
      prob: 0.001

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFMuffins
  suffix: muffins, random
  components:
  - type: Mail
    isPriority: true
    contents:
      # Muffin 1
    - id: FoodBakedMuffinBerry
      orGroup: Muffin1
    - id: FoodBakedMuffinCherry
      orGroup: Muffin1
    - id: FoodBakedMuffinBluecherry
      orGroup: Muffin1
    - id: FoodBakedMuffin
      orGroup: Muffin1
    - id: FoodMothMoffin
      orGroup: Muffin1
      prob: 0.5
      # Muffin 2
    - id: FoodBakedMuffinBerry
      orGroup: Muffin2
    - id: FoodBakedMuffinCherry
      orGroup: Muffin2
    - id: FoodBakedMuffinBluecherry
      orGroup: Muffin2
    - id: FoodBakedMuffin
      orGroup: Muffin2
    - id: FoodMothMoffin
      orGroup: Muffin2
      prob: 0.5
      # Muffin 3
    - id: FoodBakedMuffinBerry
      orGroup: Muffin3
    - id: FoodBakedMuffinCherry
      orGroup: Muffin3
    - id: FoodBakedMuffinBluecherry
      orGroup: Muffin3
    - id: FoodBakedMuffin
      orGroup: Muffin3
    - id: FoodMothMoffin
      orGroup: Muffin3
      prob: 0.5
      # Muffin 4
    - id: FoodBakedMuffinBerry
      orGroup: Muffin4
    - id: FoodBakedMuffinCherry
      orGroup: Muffin4
    - id: FoodBakedMuffinBluecherry
      orGroup: Muffin4
    - id: FoodBakedMuffin
      orGroup: Muffin4
    - id: FoodMothMoffin
      orGroup: Muffin4
      prob: 0.5

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFPAI
  suffix: PAI, extended
  components:
  - type: Mail
    contents:
    - id: PersonalAI
      orGroup: PAI
      prob: 0.99
      # Ultra rare
    - id: SyndicatePersonalAI
      orGroup: PAI
      prob: 0.01

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFPlushie
  suffix: plushie, extended
  components:
  - type: Mail
    contents:
      # These are all grouped up now to guarantee at least one item received.
      # The downside is you're not going to get half a dozen plushies anymore.
    - id: PlushieBee
      orGroup: Plushie
    - id: PlushieRGBee
      prob: 0.5
      orGroup: Plushie
    - id: PlushieNuke
      orGroup: Plushie
    - id: PlushieArachind #DeltaV - Adds MORE PLUSHIE
      orGroup: Plushie
    - id: PlushieAtmosian #DeltaV - Adds MORE PLUSHIE
      orGroup: Plushie
    - id: PlushieXeno #DeltaV - Adds MORE PLUSHIE
      orGroup: Plushie
    - id: PlushiePenguin #DeltaV - Adds MORE PLUSHIE
      orGroup: Plushie
    - id: PlushieGhost #DeltaV - Adds MORE PLUSHIE
      orGroup: Plushie
    - id: PlushieDiona #DeltaV - Adds MORE PLUSHIE
      orGroup: Plushie
    - id: ToyMouse #DeltaV - Adds MORE PLUSHIE
      orGroup: Plushie
    - id: PlushieRouny
      orGroup: Plushie
    - id: PlushieLizard
      orGroup: Plushie
    - id: PlushieSpaceLizard
      orGroup: Plushie
    - id: PlushieRatvar
      orGroup: Plushie
    - id: PlushieNar
      orGroup: Plushie
    - id: PlushieCarp
      orGroup: Plushie
    - id: PlushieHolocarp #DeltaV - Adds MORE PLUSHIE
      orGroup: Plushie
    - id: PlushieRainbowCarp #DeltaV - Adds MORE PLUSHIE
      orGroup: Plushie
    - id: PlushieSlime
      orGroup: Plushie
    - id: PlushieSnake
      orGroup: Plushie
    - id: PlushieMothRandom
      orGroup: Plushie
    - id: PlushieMoth
      prob: 0.5
      orGroup: Plushie
    - id: PlushieMothMusician
      prob: 0.5
      orGroup: Plushie
    - id: PlushieMothBartender
      prob: 0.5
      orGroup: Plushie
    - id: PlushieShadowkin
      prob: 0.5
      orGroup: Plushie

# Random snacks, replaces MailChocolate (lousy animal organs)
- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFSnacks
  suffix: snacks, random
  components:
  - type: Mail
    contents:
    # Snack 1
    - id: FoodSnackChocolate
      orGroup: Snack1
    - id: FoodSnackPopcorn
      orGroup: Snack1
    - id: FoodSnackChips
      orGroup: Snack1
    - id: FoodSnackBoritos
      orGroup: Snack1
    - id: FoodSnackSus
      orGroup: Snack1
    - id: FoodSnackPistachios
      orGroup: Snack1
    - id: FoodSnackRaisins
      orGroup: Snack1
    - id: FoodSnackCheesie
      orGroup: Snack1
    - id: FoodSnackEnergy
      orGroup: Snack1
    - id: FoodSnackCnDs
      orGroup: Snack1
    - id: FoodSnackSemki
      orGroup: Snack1
    - id: FoodSnackSyndi
      orGroup: Snack1
      prob: 0.5
    # Snack 2
    - id: FoodSnackChocolate
      orGroup: Snack2
    - id: FoodSnackPopcorn
      orGroup: Snack2
    - id: FoodSnackChips
      orGroup: Snack2
    - id: FoodSnackBoritos
      orGroup: Snack2
    - id: FoodSnackSus
      orGroup: Snack2
    - id: FoodSnackPistachios
      orGroup: Snack2
    - id: FoodSnackRaisins
      orGroup: Snack2
    - id: FoodSnackCheesie
      orGroup: Snack2
    - id: FoodSnackEnergy
      orGroup: Snack2
    - id: FoodSnackCnDs
      orGroup: Snack2
    - id: FoodSnackSemki
      orGroup: Snack2
    - id: FoodSnackSyndi
      orGroup: Snack2
      prob: 0.5
    # Snack 3
    - id: FoodSnackChocolate
      orGroup: Snack3
    - id: FoodSnackPopcorn
      orGroup: Snack3
    - id: FoodSnackChips
      orGroup: Snack3
    - id: FoodSnackBoritos
      orGroup: Snack3
    - id: FoodSnackSus
      orGroup: Snack3
    - id: FoodSnackPistachios
      orGroup: Snack3
    - id: FoodSnackRaisins
      orGroup: Snack3
    - id: FoodSnackCheesie
      orGroup: Snack3
    - id: FoodSnackEnergy
      orGroup: Snack3
    - id: FoodSnackCnDs
      orGroup: Snack3
    - id: FoodSnackSemki
      orGroup: Snack3
    - id: FoodSnackSyndi
      orGroup: Snack3
      prob: 0.5
    # Snack 4
    - id: FoodSnackChocolate
      orGroup: Snack4
    - id: FoodSnackPopcorn
      orGroup: Snack4
    - id: FoodSnackChips
      orGroup: Snack4
    - id: FoodSnackBoritos
      orGroup: Snack4
    - id: FoodSnackSus
      orGroup: Snack4
    - id: FoodSnackPistachios
      orGroup: Snack4
    - id: FoodSnackRaisins
      orGroup: Snack4
    - id: FoodSnackCheesie
      orGroup: Snack4
    - id: FoodSnackEnergy
      orGroup: Snack4
    - id: FoodSnackCnDs
      orGroup: Snack4
    - id: FoodSnackSemki
      orGroup: Snack4
    - id: FoodSnackSyndi
      orGroup: Snack4
      prob: 0.5

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFVagueThreat
  suffix: vague-threat
  components:
  - type: Mail
    contents:
    - id: PaperMailNFVagueThreat1
      orGroup: Paper
    - id: PaperMailNFVagueThreat2
      orGroup: Paper
    - id: PaperMailNFVagueThreat3
      orGroup: Paper
    - id: PaperMailNFVagueThreat4
      orGroup: Paper
    - id: PaperMailNFVagueThreat5
      orGroup: Paper
    - id: PaperMailNFVagueThreat6
      orGroup: Paper
    - id: PaperMailNFVagueThreat7
      orGroup: Paper
    - id: PaperMailNFVagueThreat8
      orGroup: Paper
    - id: PaperMailNFVagueThreat9
      orGroup: Paper
    - id: PaperMailNFVagueThreat10
      orGroup: Paper
    - id: PaperMailNFVagueThreat11
      orGroup: Paper
    - id: PaperMailNFVagueThreat12
      orGroup: Paper
    - id: KitchenKnife
      orGroup: ThreateningObject
    - id: ButchCleaver
      orGroup: ThreateningObject
    - id: CombatKnife
      orGroup: ThreateningObject
    - id: SurvivalKnife
      orGroup: ThreateningObject
    - id: SoapHomemade
      orGroup: ThreateningObject
    - id: FoodMeat
      orGroup: ThreateningObject
    - id: OrganHumanHeart
      orGroup: ThreateningObject

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFDonkPockets
  suffix: donk pockets, random
  components:
  - type: Mail
    contents:
    - id: FoodBoxDonkpocket
      orGroup: Donk
      prob: 0.4 # Higher probability, useful for chefs.
    - id: FoodBoxDonkpocketSpicy
      orGroup: Donk
      prob: 0.1
    - id: FoodBoxDonkpocketTeriyaki
      orGroup: Donk
      prob: 0.1
    - id: FoodBoxDonkpocketPizza
      orGroup: Donk
      prob: 0.1
    - id: FoodBoxDonkpocketStonk
      orGroup: Donk
      prob: 0.05
    - id: FoodBoxDonkpocketCarp
      orGroup: Donk
      prob: 0.05
    - id: FoodBoxDonkpocketBerry
      orGroup: Donk
      prob: 0.1
    - id: FoodBoxDonkpocketHonk
      orGroup: Donk
      prob: 0.05
    - id: FoodBoxDonkpocketDink
      orGroup: Donk
      prob: 0.05 # Bad luck.

# Needs a buff?
- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFSodaPwrGame
  suffix: Pwrgame
  components:
  - type: Mail
    contents:
    - id: DrinkPwrGameCan
      amount: 3
    - id: PaperMailNFPwrGameAd

# Needs a buff?
- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFSodaRedBool
  suffix: Red Bool
  components:
  - type: Mail
    contents:
    - id: DrinkEnergyDrinkCan
      amount: 3
    - id: PaperMailNFRedBoolAd

# Needs a buff?
- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFSodaSpaceCola
  suffix: Space Cola
  components:
  - type: Mail
    contents:
    - id: DrinkColaBottleFull
    - id: PaperMailNFSpaceColaAd

# Needs a buff?
- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFSodaSpaceMountainWind
  suffix: Space Mountain Wind
  components:
  - type: Mail
    contents:
    - id: DrinkSpaceMountainWindBottleFull
    - id: PaperMailNFSpaceMountainWindAd

# Needs a buff?
- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFSodaSpaceUp
  suffix: Space Up
  components:
  - type: Mail
    contents:
    - id: DrinkSpaceUpBottleFull
    - id: PaperMailNFSpaceUpAd

#TODO: we don't have rainbow joints or blunts (yet?). Uncomment when (if?) they are added.
- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFJoints
  suffix: joints
  components:
  - type: Mail
    contents:
    # Smokeable 1
    - id: Joint
      orGroup: Smokeable1
      prob: 0.35
#    - id: JointRainbow
#      orGroup: Smokeable1
#      prob: 0.15
#    - id: Blunt
#      orGroup: Smokeable1
#      prob: 0.35
#    - id: BluntRainbow
#      orGroup: Smokeable1
#      prob: 0.15
    # Smokeable 2
    - id: Joint
      orGroup: Smokeable2
      prob: 0.35
#    - id: JointRainbow
#      orGroup: Smokeable2
#      prob: 0.15
#    - id: Blunt
#      orGroup: Smokeable2
#      prob: 0.35
#    - id: BluntRainbow
#      orGroup: Smokeable2
#      prob: 0.15
    # Smokeable 3
    - id: Joint
      orGroup: Smokeable3
      prob: 0.35
#    - id: JointRainbow
#      orGroup: Smokeable3
#      prob: 0.15
#    - id: Blunt
#      orGroup: Smokeable3
#      prob: 0.35
#    - id: BluntRainbow
#      orGroup: Smokeable3
#      prob: 0.15

# Catchalls for food that only exist in random spawners
# Mmm, mail food
# Needs a buff?
- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFUnusualFood
  suffix: unusual food
  components:
  - type: Mail
    isPriority: true
    isFragile: true
    contents:
    - id: FoodMealNachos
      orGroup: Food
    - id: FoodMealNachosCheesy
      orGroup: Food
    - id: FoodMealNachosCuban
      orGroup: Food
    - id: FoodMealEggplantParm
      orGroup: Food
    - id: FoodMealPotatoYaki
      orGroup: Food
    - id: FoodMealCornedbeef
      orGroup: Food
    - id: FoodMealBearsteak
      orGroup: Food
    - id: FoodMealPigblanket
      orGroup: Food
    - id: FoodMealEggsbenedict
      orGroup: Food
    - id: FoodMealOmelette
      orGroup: Food
    - id: FoodMealFriedegg
      orGroup: Food
    - id: FoodMealMilkape
      orGroup: Food
    - id: FoodMealMemoryleek
      orGroup: Food
    - id: DisgustingSweptSoup
      orGroup: Food
    - id: FoodBreadVolcanic
      orGroup: Food
    - id: FoodBakedWaffleSoylent
      orGroup: Food
    - id: FoodBakedWaffleRoffle
      orGroup: Food
    - id: FoodPieCherry
      orGroup: Food
    - id: FoodPieFrosty
      orGroup: Food
      prob: 0.05
    - id: FoodMeatGoliathCooked
      amount: 3
      orGroup: Food
    - id: FoodMeatRounyCooked
      amount: 3
      orGroup: Food
    - id: FoodMeatLizardCooked
      amount: 3
      orGroup: Food
    - id: FoodMeatSpiderlegCooked
      amount: 3
      orGroup: Food
    - id: FoodMeatMeatballCooked
      amount: 4
      orGroup: Food

# Needs a buff?
- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFBakedGoods
  suffix: baked goods
  components:
  - type: Mail
    isPriority: true
    isFragile: true
    contents:
    - id: FoodBakedBunHoney
      amount: 2
      orGroup: Food
    - id: FoodBakedBunHotX
      amount: 2
      orGroup: Food
    - id: FoodBakedBunMeat
      amount: 2
      orGroup: Food
    - id: FoodBakedPretzel
      amount: 2
      orGroup: Food
    - id: FoodBakedCannoli
      amount: 2
      orGroup: Food
    - id: FoodDonutPlain
      amount: 2
      orGroup: Food
    - id: FoodDonutJellyPlain
      amount: 2
      orGroup: Food
    - id: FoodDonutHomer
      amount: 2
      orGroup: Food
    - id: FoodDonutChaos
      amount: 2
      orGroup: Food
    - id: FoodDonutMeat
      amount: 2
      orGroup: Food
    - id: FoodDonutPink
      amount: 2
      orGroup: Food
    - id: FoodDonutSpaceman
      amount: 2
      orGroup: Food
    - id: FoodDonutApple
      amount: 2
      orGroup: Food
    - id: FoodDonutCaramel
      amount: 2
      orGroup: Food
    - id: FoodDonutChocolate
      amount: 2
      orGroup: Food
#    - id: FoodDonutBluePumpkin # Don't have that yet
#      amount: 2
#      orGroup: Food
    - id: FoodDonutBungo
      amount: 2
      orGroup: Food
    - id: FoodDonut
      amount: 2
      orGroup: Food
    - id: FoodDonutSweetpea
      amount: 2
      orGroup: Food
    - id: FoodDonutJellyHomer
      amount: 2
      orGroup: Food
    - id: FoodDonutJellyPink
      amount: 2
      orGroup: Food
    - id: FoodDonutJellySpaceman
      amount: 2
      orGroup: Food
    - id: FoodDonutJellyApple
      amount: 2
      orGroup: Food
    - id: FoodDonutJellyCaramel
      amount: 2
      orGroup: Food
    - id: FoodDonutJellyChocolate
      amount: 2
      orGroup: Food
#    - id: FoodDonutJellyBluePumpkin # Don't have that yet
#      amount: 2
#      orGroup: Food
    - id: FoodDonutJellyBungo
      amount: 2
      orGroup: Food
    - id: FoodDonutJelly
      amount: 2
      orGroup: Food
    - id: FoodDonutJellySweetpea
      amount: 2
      orGroup: Food
    - id: FoodDonutJellySlugcat
      amount: 2
      orGroup: Food
    - id: FoodFrozenSandwich # ah yes, baked goods
      amount: 2
      orGroup: Food
    - id: FoodFrozenFreezy
      amount: 2
      orGroup: Food
    - id: FoodFrozenSundae
      amount: 2
      orGroup: Food
    - id: FoodFrozenCornuto
      amount: 2
      orGroup: Food
    - id: FoodFrozenPopsicleOrange
      amount: 2
      orGroup: Food
    - id: FoodFrozenPopsicleBerry
      amount: 2
      orGroup: Food
    - id: FoodFrozenPopsicleJumbo
      amount: 2
      orGroup: Food
    - id: FoodFrozenSnowcone
      amount: 2
      orGroup: Food
    - id: FoodFrozenSnowconeBerry
      amount: 2
      orGroup: Food
    - id: FoodFrozenSnowconeFruit
      amount: 2
      orGroup: Food
    - id: FoodFrozenSnowconeClown
      amount: 2
      orGroup: Food
    - id: FoodFrozenSnowconeMime
      amount: 2
      orGroup: Food
    - id: FoodFrozenSnowconeRainbow
      amount: 2
      orGroup: Food
    - id: FoodFrozenSnowconeMime
      amount: 2
      orGroup: Food
    - id: FoodMealMint # unlucky
      amount: 2
      orGroup: Food

# Needs a buff?
- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFUnusualProduce
  suffix: unusual produce
  components:
  - type: Mail
    isPriority: true
    isFragile: true
    contents:
    - id: FoodLaughinPeaPod
      orGroup: Produce
      amount: 5
    - id: FoodMimana
      orGroup: Produce
      amount: 5
    - id: FoodLemoon
      orGroup: Produce
      amount: 5
    - id: FoodBlueTomato
      orGroup: Produce
      amount: 5
    - id: FoodBloodTomato
      orGroup: Produce
      amount: 5
    - id: FoodKoibean
      orGroup: Produce
      amount: 5
#     EE does not have those yet. Uncomment if any of those get ported.
#    - id: FoodGhostPepper #DeltaV
#      orGroup: Produce
#      amount: 5
#    - id: FoodCosmicRevenant #DeltaV
#      orGroup: Produce
#      amount: 5
#    - id: FoodCrystalThistle #DeltaV
#      orGroup: Produce
#      amount: 5
    - id: FoodLily
      orGroup: Produce
      amount: 5
      prob: 0.5
    - id: FoodAmbrosiaDeus
      orGroup: Produce
      amount: 5
      prob: 0.5
    - id: FoodSpacemansTrumpet
      orGroup: Produce
      amount: 5
      prob: 0.25

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFSoaps
  suffix: soap sampler
  components:
  - type: Mail
    contents:
    - id: BoxSoapsAssorted
    - id: PaperMailNTSoapAd1
      orGroup: Ad
    - id: PaperMailNTSoapAd2
      orGroup: Ad

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFSoapsOmega
  suffix: soap sampler, omega
  components:
  - type: Mail
    contents:
    - id: BoxSoapsAssortedOmega
    - id: PaperMailNTSoapAd1
      orGroup: Ad
    - id: PaperMailNTSoapAd2
      orGroup: Ad

# Could add spessman battle rules here
- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFFigurineBulk # DeltaV - No longer Bulk
  suffix: figurine, bulk #DeltaV - Spams 3 boxes instead of using the bulk figurine prototype that Frontier uses
  components:
  - type: Mail
    contents:
    - id: MysteryFigureBox
    - id: MysteryFigureBox
    - id: MysteryFigureBox

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFPen
  suffix: fancy pen
  components:
  - type: Mail
    contents:
    - id: LuxuryPen
      orGroup: Pen
      prob: 0.50
    # DeltaV: Commenting these two out because I dunno if crew should have nice things
    # - id: PenHop
    #   orGroup: Pen
    #   prob: 0.25
    # - id: PenCap
    #   orGroup: Pen
    #   prob: 0.25
    # TODO: come up with a slightly less powerful version of these
    # Ultra-rare
    # - id: CyberPen
    #   orGroup: Pen
    #   prob: 0.005
    # - id: PenCentcom
    #   orGroup: Pen
    #   prob: 0.005
    - id: PaperMailNFPaperPusherAd

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMailLarge
  id: MailNFThrongler
  suffix: throngler
  components:
  - type: Mail
    contents:
    - id: ThronglerToy

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFInstrumentSmall
  suffix: instrument, expanded
  components:
  - type: Mail
    contents:
    - id: TrumpetInstrument
      orGroup: Instrument
    - id: RecorderInstrument
      orGroup: Instrument
    - id: ClarinetInstrument
      orGroup: Instrument
    - id: FluteInstrument
      orGroup: Instrument
    - id: HarmonicaInstrument
      orGroup: Instrument
    - id: OcarinaInstrument
      orGroup: Instrument
    - id: PanFluteInstrument
      orGroup: Instrument
    - id: KalimbaInstrument
      orGroup: Instrument
    - id: WoodblockInstrument
      orGroup: Instrument
    - id: BikeHornInstrument
      orGroup: Instrument
    - id: MusicBoxInstrument
      orGroup: Instrument
    - id: MicrophoneInstrument
      orGroup: Instrument
    - id: MusicalLungInstrument
      orGroup: Instrument
    # Uncommon
    - id: PhoneInstrument
      orGroup: Instrument
      prob: 0.1
    # Rare
    - id: BananaPhoneInstrument
      orGroup: Instrument
      prob: 0.05
    # Ultra-rare
    - id: PhoneInstrumentSyndicate
      orGroup: Instrument
      prob: 0.01

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFUnusualClothing
  suffix: unusual clothing
  components:
  - type: Mail
    contents:
      - id: ClothingKimonoPink
        orGroup: Clothes
      - id: ClothingKimonoBlue
        orGroup: Clothes
      - id: ClothingKimonoPurple
        orGroup: Clothes
      - id: ClothingKimonoSky
        orGroup: Clothes
      - id: ClothingKimonoGreen
        orGroup: Clothes
      - id: ClothingUniformMartialGi
        orGroup: Clothes
      - id: ClothingNeckBling
        orGroup: Clothes
      - id: ClothingShoesBling
        orGroup: Clothes
      - id: ClothingNeckCloakAdmin
        orGroup: Clothes
      - id: ClothingHeadHatFancyCrown
        orGroup: Clothes
      - id: ClothingHeadHatCake
        orGroup: Clothes
      - id: ClothingHeadHatCone
        orGroup: Clothes
      - id: ClothingMaskOniRed
        orGroup: Clothes
      - id: ClothingMaskOniBlue
        orGroup: Clothes
      - id: ClothingHeadHatRichard
        orGroup: Clothes
      - id: ClothingHeadHatAnimalHeadslime
        orGroup: Clothes
      - id: ClothingHeadHatDogEars
        orGroup: Clothes
      - id: ClothingHeadHatCatEars
        orGroup: Clothes
      - id: ClothingEyesGlassesOutlawGlasses
        orGroup: Clothes
      - id: ClothingUniformJumpsuitGalaxyBlue
        orGroup: Clothes
        prob: 0.25
      - id: ClothingUniformJumpsuitGalaxyRed
        orGroup: Clothes
        prob: 0.25

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFCritter
  suffix: critter
  components:
  - type: Mail
    isFragile: true
    contents:
      # Bugs (weight: 2)
      - id: MobCockroach
        orGroup: Critter
        prob: 0.36
      - id: MobSlug
        orGroup: Critter
        prob: 0.36
      - id: MobArgocyteSlurva # honorary bug?
        orGroup: Critter
        prob: 0.36
      - id: MobBee
        orGroup: Critter
        prob: 0.36
      - id: MobButterfly
        orGroup: Critter
        prob: 0.36
        # Uncommon
      - id: MobMothroach
        orGroup: Critter
        prob: 0.2
      # Small reptiles (weight: 1)
      - id: MobLizard
        orGroup: Critter
        prob: 0.34
      - id: MobSnake
        orGroup: Critter
        prob: 0.33
      - id: MobFrog
        orGroup: Critter
        prob: 0.33
      # Small mammals (weight: 1)
      - id: MobMouse
        orGroup: Critter
        prob: 0.33
      - id: MobMouse1
        orGroup: Critter
        prob: 0.33
      - id: MobMouse2
        orGroup: Critter
        prob: 0.33
      - id: MobMouseCancer
        orGroup: Critter
        prob: 0.01 # Rare

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNFTacticalMaid
  suffix: tactical maid
  components:
  - type: Mail
    contents:
      - id: ClothingUniformJumpskirtTacticalMaid
      - id: ClothingHeadHatTacticalMaidHeadband
      - id: MegaSprayBottle
      - id: ClothingHandsTacticalMaidGloves

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMailLarge
  id: MailNFKendoKit
  suffix: kendo kit
  components:
  - type: Mail
    contents: # A lot of stuff here, seems spammy.
    - id: ClothingUniformKendoHakama
      amount: 2
    - id: ClothingOuterArmorKendoBogu
      amount: 2
    - id: ClothingHeadHelmetKendoMen
      amount: 2
    - id: Shinai
      amount: 2

# Base Nyano Mail
- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailSake
  suffix: osake
  components:
  - type: Mail
    contents:
    - id: DrinkSakeCup
      amount: 2
    - id: DrinkTokkuri

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailBlockGameDIY
  suffix: blockgamediy
  components:
  - type: Mail
    contents:
    - id: BlockGameArcadeComputerCircuitboard

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailCigars
  suffix: Cigars
  components:
  - type: Mail
    contents:
    - id: CigarCase
    - id: Lighter

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailCrayon
  suffix: Crayon
  components:
  - type: Mail
    contents:
    - id: CrayonBox
    - id: CrayonMagic
    - prob: .10

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailCosplayArc
  suffix: cosplay-arc
  components:
  - type: Mail
    openSound: /Audio/Nyanotrasen/Voice/Felinid/cat_wilhelm.ogg
    contents:
    - id: ClothingCostumeArcDress

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailCosplayGeisha
  suffix: cosplay-geisha
  components:
  - type: Mail
    contents:
      - id: UniformGeisha
      - id: DrinkTeapot
      - id: DrinkTeacup
        amount: 3

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailCosplaySchoolgirl
  suffix: cosplay-schoolgirl
  components:
  - type: Mail
    contents:
    - id: UniformSchoolgirlBlack
      orGroup: Color
    - id: UniformSchoolgirlBlue
      orGroup: Color
    - id: UniformSchoolgirlCyan
      orGroup: Color
    - id: UniformSchoolgirlGreen
      orGroup: Color
    - id: UniformSchoolgirlOrange
      orGroup: Color
    - id: UniformSchoolgirlPink
      orGroup: Color
    - id: UniformSchoolgirlPurple
      orGroup: Color
    - id: UniformSchoolgirlRed
      orGroup: Color

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailFlowers
  suffix: flowers
  components:
  - type: Mail
    contents:
    # TODO actual flowers
    - id: ClothingHeadHatFlowerWreath
      orGroup: Flowers
    - id: FoodPoppy
      orGroup: Flowers
    - id: FoodLily

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailSignallerKit
  suffix: signallerkit
  components:
  - type: Mail
    contents:
    - id: Multitool
    - id: RemoteSignaller

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailNoir
  suffix: noir
  components:
  - type: Mail
    contents:
    - id: ClothingUniformJumpsuitDetectiveGrey
    - id: ClothingUniformJumpskirtDetectiveGrey
    - id: ClothingHeadHatBowlerHat
    - id: ClothingOuterCoatGentle

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailRestraints
  suffix: restraints
  components:
  - type: Mail
    contents:
    - id: Handcuffs
    - id: ClothingMaskMuzzle
    - id: ClothingEyesBlindfold

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailFishingCap
  suffix: fishingcap
  components:
  - type: Mail
    contents:
    - id: ClothingHeadFishCap

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailFlashlight
  suffix: Flashlight
  components:
  - type: Mail
    contents:
    - id: FlashlightLantern

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailSpaceVillainDIY
  suffix: spacevilliandiy
  components:
  - type: Mail
    contents:
    - id: SpaceVillainArcadeComputerCircuitboard

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailSunglasses
  suffix: Sunglasses
  components:
  - type: Mail
    contents:
    - id: ClothingEyesGlassesSunglasses

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMail
  id: MailWinterCoat
  suffix: wintercoat
  components:
  - type: Mail
    contents:
    - id: ClothingOuterWinterCoat
      orGroup: Coat
    - id: ClothingOuterWinterCoatLong
      orGroup: Coat
    - id: ClothingOuterWinterCoatPlaid
      orGroup: Coat

- type: entity
  parent: BaseMail
  id: MailHydroCoPowderJuice
  suffix: HydroCo powder juice
  components:
  - type: Mail
    contents:
    - id: ReagentTinPowderedJuiceLemon
      orGroup: Juice
    - id: ReagentTinPowderedJuiceLime
      orGroup: Juice
    - id: ReagentTinPowderedJuicePineapple
      orGroup: Juice
    - id: ReagentTinPowderedJuiceBanana
      orGroup: Juice
    - id: ReagentTinPowderedJuiceBerry
      orGroup: Juice
    - id: ReagentTinPowderedJuiceWatermelon
      orGroup: Juice
    - id: ReagentTinPowderedJuiceGrape
      orGroup: Juice
    - id: ReagentTinPowderedJuiceApple
      orGroup: Juice
    - id: ReagentTinPowderedJuiceCherry
      orGroup: Juice
    - id: ReagentTinPowderedJuiceCarrot
      orGroup: Juice
    - id: ReagentTinPowderedJuiceTomato
      orGroup: Juice
    - id: SpoonPlastic
      amount: 1
    - id: PaperMailHydroCoTrial
      amount: 1
