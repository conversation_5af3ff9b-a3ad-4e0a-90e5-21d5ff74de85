- type: entity
  save: false
  name: "Уріст МакАрахн"
  parent: BaseMobHuman
  id: MobArachneBase
  abstract: true
  components:
  - type: Sprite
    # Arachne are one of the species that needs a manual visual layers setup.
    layers:
    - map: [ "enum.HumanoidVisualLayers.LLeg" ]
      sprite: Mobs/Species/arachne.rsi
      state: spider_body
    - map: [ "enum.HumanoidVisualLayers.Chest" ]
      color: "#e8b59b"
      sprite: Mobs/Species/Human/parts.rsi
      state: torso_m
    - map: [ "enum.HumanoidVisualLayers.RLeg" ]
      sprite: Mobs/Species/arachne.rsi
      state: spider_body_front
    - map: [ "enum.HumanoidVisualLayers.Head" ]
      color: "#e8b59b"
      sprite: Mobs/Species/Human/parts.rsi
      state: head_m
    - map: [ "enum.HumanoidVisualLayers.Snout" ]
    - map: [ "enum.HumanoidVisualLayers.Eyes" ]
      color: "#008800"
      sprite: Mobs/Species/eyes.rsi
      state: eyes
    - map: [ "enum.HumanoidVisualLayers.Face" ]
    - map: [ "enum.HumanoidVisualLayers.RArm" ]
      color: "#e8b59b"
      sprite: Mobs/Species/Human/parts.rsi
      state: r_arm
    - map: [ "enum.HumanoidVisualLayers.LArm" ]
      color: "#e8b59b"
      sprite: Mobs/Species/Human/parts.rsi
      state: l_arm
#     - shader: StencilClear
#      sprite: Mobs/Species/Human/parts.rsi
#      state: l_leg
    - shader: StencilMask
      map: [ "enum.HumanoidVisualLayers.StencilMask" ]
      sprite: Mobs/Customization/anytaur_masking_helpers.rsi
      state: unisex_full
      visible: false
    - map: [ "jumpsuit" ]
    - map: [ "enum.HumanoidVisualLayers.LHand" ]
      color: "#e8b59b"
      sprite: Mobs/Species/Human/parts.rsi
      state: l_hand
    - map: [ "enum.HumanoidVisualLayers.RHand" ]
      color: "#e8b59b"
      sprite: Mobs/Species/Human/parts.rsi
      state: r_hand
    - map: [ "enum.HumanoidVisualLayers.Handcuffs" ]
      color: "#ffffff"
      sprite: Objects/Misc/handcuffs.rsi
      state: body-overlay-2
      visible: false
    - map: [ "id" ]
    - map: [ "gloves" ]
    - map: [ "shoes" ]
    - map: [ "ears" ]
    - map: [ "innerBelt" ]
    - map: [ "innerNeck" ]
    - map: [ "outerClothing" ]
    - map: [ "eyes" ]
    - map: [ "belt" ]
    - map: [ "neck" ]
    - map: [ "back" ]
    - map: [ "enum.HumanoidVisualLayers.FacialHair" ]
    - map: [ "enum.HumanoidVisualLayers.Hair" ]
      state: bald
      sprite: Mobs/Customization/human_hair.rsi
    - map: [ "mask" ]
    - map: [ "enum.HumanoidVisualLayers.HeadSide" ] # This is not in the default order so earrings are rendered above masks
    - map: [ "head" ]
    - map: [ "pocket1" ]
    - map: [ "pocket2" ]
    - map: [ "enum.HumanoidVisualLayers.Tail" ]
      sprite: Mobs/Customization/masking_helpers.rsi
      state: none
      visible: false
    - map: [ "clownedon" ] # Dynamically generated
      sprite: "Effects/creampie.rsi"
      state: "creampie_human"
      visible: false
  - type: HumanoidAppearance
    species: Arachne
  - type: Sericulture
    action: ActionSericulture
    productionLength: 2
    entityProduced: MaterialWebSilk1
    hungerCost: 4 # Should total to 25 total silk on full hunger
  - type: Fixtures
    fixtures: # TODO: This needs a second fixture just for mob collisions.
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.40
        density: 140
        restitution: 0.0
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Body
    prototype: Arachne
    requiredLegs: 8
  - type: Speech
    speechSounds: Alto
  - type: Inventory
    templateId: anytaur
  - type: Tag
    tags:
    - CanPilot
    - FootstepSound
    - DoorBumpOpener
    - SpiderCraft
  - type: StepTriggerImmune
  - type: Bloodstream
    bloodReagent: DemonsBlood
    bloodRegenerationThirst: 4 # 1 unit of demon's blood satiates 4 thirst
  - type: BloodSucker
    webRequired: true
  - type: Cocooner
  - type: DamageVisuals
    targetLayers:
      - "enum.HumanoidVisualLayers.Chest"
      - "enum.HumanoidVisualLayers.Head"
      - "enum.HumanoidVisualLayers.LArm"
      - "enum.HumanoidVisualLayers.RArm"
  - type: MovedByPressure
  - type: Barotrauma
    damage:
      types:
        Blunt: 0.05 #per second, scales with pressure and other constants. Reduced Damage. This allows medicine to heal faster than damage.
  - type: MovementAlwaysTouching
  - type: MovementSpeedModifier
    baseWalkSpeed : 3.0
    baseSprintSpeed : 5.0
  - type: FireVisuals
    sprite: Mobs/Effects/onfire.rsi
    normalState: Generic_mob_burning
    alternateState: arachne_standing
    fireStackAlternateState: 3
  - type: Spider
  - type: IgnoreSpiderWeb
  - type: FootPrints
    leftBarePrint: "footprint-left-bare-spider"
    rightBarePrint: "footprint-right-bare-spider"
  - type: NoSlip

- type: entity
  save: false
  name: "Уріст МакХендс"
  parent: MobHumanDummy
  id: MobArachneDummy
  categories: [ HideSpawnMenu ]
  description: "Фіктивна павутина, призначена для використання в налаштуваннях символів."
  components:
  - type: Sprite
    layers:
    - map: [ "enum.HumanoidVisualLayers.LLeg" ]
      sprite: Mobs/Species/arachne.rsi
      state: spider_body
    - map: [ "enum.HumanoidVisualLayers.Chest" ]
      color: "#e8b59b"
      sprite: Mobs/Species/Human/parts.rsi
      state: torso_m
    - map: [ "enum.HumanoidVisualLayers.RLeg" ]
      sprite: Mobs/Species/arachne.rsi
      state: spider_body_front
    - map: [ "enum.HumanoidVisualLayers.Head" ]
      color: "#e8b59b"
      sprite: Mobs/Species/Human/parts.rsi
      state: head_m
    - map: [ "enum.HumanoidVisualLayers.Snout" ]
    - map: [ "enum.HumanoidVisualLayers.Eyes" ]
      color: "#008800"
      sprite: Mobs/Species/eyes.rsi
      state: eyes
    - map: [ "enum.HumanoidVisualLayers.Face" ]
    - map: [ "enum.HumanoidVisualLayers.RArm" ]
      color: "#e8b59b"
      sprite: Mobs/Species/Human/parts.rsi
      state: r_arm
    - map: [ "enum.HumanoidVisualLayers.LArm" ]
      color: "#e8b59b"
      sprite: Mobs/Species/Human/parts.rsi
      state: l_arm
#     - shader: StencilClear
#      sprite: Mobs/Species/Human/parts.rsi
#      state: l_leg
    - shader: StencilMask
      map: [ "enum.HumanoidVisualLayers.StencilMask" ]
      sprite: Mobs/Customization/anytaur_masking_helpers.rsi
      state: unisex_full
      visible: false
    - map: [ "jumpsuit" ]
    - map: [ "enum.HumanoidVisualLayers.LHand" ]
      color: "#e8b59b"
      sprite: Mobs/Species/Human/parts.rsi
      state: l_hand
    - map: [ "enum.HumanoidVisualLayers.RHand" ]
      color: "#e8b59b"
      sprite: Mobs/Species/Human/parts.rsi
      state: r_hand
    - map: [ "enum.HumanoidVisualLayers.Handcuffs" ]
      color: "#ffffff"
      sprite: Objects/Misc/handcuffs.rsi
      state: body-overlay-2
      visible: false
    - map: [ "id" ]
    - map: [ "gloves" ]
    - map: [ "shoes" ]
    - map: [ "ears" ]
    - map: [ "innerBelt" ]
    - map: [ "innerNeck" ]
    - map: [ "outerClothing" ]
    - map: [ "eyes" ]
    - map: [ "belt" ]
    - map: [ "neck" ]
    - map: [ "back" ]
    - map: [ "enum.HumanoidVisualLayers.FacialHair" ]
    - map: [ "enum.HumanoidVisualLayers.Hair" ]
      state: bald
      sprite: Mobs/Customization/human_hair.rsi
    - map: [ "mask" ]
    - map: [ "enum.HumanoidVisualLayers.HeadSide" ] # This is not in the default order so earrings are rendered above masks
    - map: [ "head" ]
    - map: [ "pocket1" ]
    - map: [ "pocket2" ]
    - map: [ "enum.HumanoidVisualLayers.Tail" ]
      sprite: Mobs/Customization/masking_helpers.rsi
      state: none
      visible: false
  - type: Inventory
    templateId: anytaur
  - type: HumanoidAppearance
    species: Arachne
