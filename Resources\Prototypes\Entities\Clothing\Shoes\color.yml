# DO NOT ARBITRARILY CHANGE THESE TO MAKE THEM "CONSISTENT" WITH THE COLOR JUMPSUITS. You are probably just going to make them look worse.

# Black Shoes
- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesColorBlack
  name: "чорні туфлі"
  description: "Стильні чорні туфлі."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/color.rsi
    layers:
    - state: icon
      color: "#3f3f3f" #Different from the worn state for contrast reasons.
    - state: soles-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#3f3f3f"
      - state: soles-inhand-left
      right:
      - state: inhand-right
        color: "#3f3f3f"
      - state: soles-inhand-right
  - type: Clothing
    sprite: Clothing/Shoes/color.rsi
    clothingVisuals:
      shoes:
      - state: equipped-FEET
        color: "#1d1d1d"
      - state: soles-equipped-FEET

# White Shoes
- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesColorWhite
  name: "білі туфлі"
  description: "Не знімайте їх на офісній різдвяній вечірці."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/color.rsi
    layers:
    - state: icon
      color: "#EAE8E8" #Deliberately NOT pure white
    - state: soles-icon
  - type: Item
    inhandVisuals: #We don't want the sole icons. Since this is white, it's just going to look weird (white on white doesn't really contrast.)
      left:
      - state: inhand-left
        color: "#EAE8E8"
      right:
      - state: inhand-right
        color: "#EAE8E8"
  - type: Clothing
    sprite: Clothing/Shoes/color.rsi
    clothingVisuals:
      shoes:
      - state: equipped-FEET
        color: "#EAE8E8"
      - state: contrastedsoles-equipped-FEET

# Blue Shoes
- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesColorBlue
  name: "сині туфлі"
  description: "Стильні сині туфлі."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/color.rsi
    layers:
    - state: icon
      color: "#0089EF"    
    - state: soles-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#0089EF"
      - state: soles-inhand-left
      right:
      - state: inhand-right
        color: "#0089EF"
      - state: soles-inhand-right
  - type: Clothing
    sprite: Clothing/Shoes/color.rsi
    clothingVisuals:
      shoes:
      - state: equipped-FEET
        color: "#0089EF"
      - state: soles-equipped-FEET

# Brown Shoes
- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesColorBrown
  name: "коричневі туфлі"
  description: "Пара коричневих туфель."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/color.rsi
    layers:
    - state: icon
      color: "#723A02"    
    - state: soles-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#723A02"
      - state: soles-inhand-left
      right:
      - state: inhand-right
        color: "#723A02"
      - state: soles-inhand-right
  - type: Clothing
    sprite: Clothing/Shoes/color.rsi
    clothingVisuals:
      shoes:
      - state: equipped-FEET
        color: "#592D01"
      - state: soles-equipped-FEET

# Green Shoes
- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesColorGreen
  name: "зелені туфлі"
  description: "Стильні зелені туфлі."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/color.rsi
    layers:
    - state: icon
      color: "#5ABF2F"    
    - state: soles-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#5ABF2F"
      - state: soles-inhand-left
      right:
      - state: inhand-right
        color: "#5ABF2F"
      - state: soles-inhand-right
  - type: Clothing
    sprite: Clothing/Shoes/color.rsi
    clothingVisuals:
      shoes:
      - state: equipped-FEET
        color: "#5ABF2F"
      - state: soles-equipped-FEET

# Orange Shoes
- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesColorOrange
  name: "помаранчеві туфлі"
  description: "Стильні помаранчеві туфлі."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/color.rsi
    layers:
    - state: icon
      color: "#EF8100"    
    - state: soles-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#EF8100"
      - state: soles-inhand-left
      right:
      - state: inhand-right
        color: "#EF8100"
      - state: soles-inhand-right
  - type: Clothing
    sprite: Clothing/Shoes/color.rsi
    clothingVisuals:
      shoes:
      - state: equipped-FEET
        color: "#EF8100"
      - state: soles-equipped-FEET

# Red Shoes
- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesColorRed
  name: "червоні туфлі"
  description: "Стильні червоні туфлі."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/color.rsi
    layers:
    - state: icon
      color: "#940000"    
    - state: soles-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#940000"
      - state: soles-inhand-left
      right:
      - state: inhand-right
        color: "#940000"
      - state: soles-inhand-right
  - type: Clothing
    sprite: Clothing/Shoes/color.rsi
    clothingVisuals:
      shoes:
      - state: equipped-FEET
        color: "#940000"
      - state: soles-equipped-FEET

# Yellow Shoes
- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesColorYellow
  name: "жовті туфлі"
  description: "Стильні жовті туфлі."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/color.rsi
    layers:
    - state: icon
      color: "#EBE216"    
    - state: soles-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#EBE216"
      - state: soles-inhand-left
      right:
      - state: inhand-right
        color: "#EBE216"
      - state: soles-inhand-right
  - type: Clothing
    sprite: Clothing/Shoes/color.rsi
    clothingVisuals:
      shoes:
      - state: equipped-FEET
        color: "#EBE216"
      - state: soles-equipped-FEET

# Purple Shoes
- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesColorPurple
  name: "фіолетові туфлі"
  description: "Стильні фіолетові туфлі."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/color.rsi
    layers:
    - state: icon
      color: "#9C0DE1"    
    - state: soles-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#9C0DE1"
      - state: soles-inhand-left
      right:
      - state: inhand-right
        color: "#9C0DE1"
      - state: soles-inhand-right
  - type: Clothing
    sprite: Clothing/Shoes/color.rsi
    clothingVisuals:
      shoes:
      - state: equipped-FEET
        color: "#9C0DE1"
      - state: soles-equipped-FEET