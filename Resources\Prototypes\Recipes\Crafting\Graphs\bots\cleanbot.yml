- type: constructionGraph
  id: CleanBot
  start: start
  graph:
  - node: start
    edges:
    - to: bot
      steps:
      - tag: Bucket
        icon:
          sprite: Objects/Tools/bucket.rsi
          state: icon
        name: bucket
      - tag: ProximitySensor
        icon:
          sprite: Objects/Misc/proximity_sensor.rsi
          state: icon
        name: proximity sensor
        doAfter: 2
      - tag: BorgArm
        icon:
          sprite: Mobs/Silicon/drone.rsi
          state: l_hand
        name: borg arm
        doAfter: 2
  - node: bot
    entity: MobCleanBot
