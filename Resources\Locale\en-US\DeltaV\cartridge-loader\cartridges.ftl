## CrimeAssist

# --- General ---
crime-assist-program-name = Crime Assist
crime-assist-yes-button = Yes
crime-assist-no-button = No
crime-assist-back-button = Back

# --- Severity Levels ---
crime-assist-severity-innocent = Innocent
crime-assist-severity-light = Minor Offences
crime-assist-severity-medium = Medium Offences
crime-assist-severity-heavy = Major Offences
crime-assist-severity-veryheavy = Severe Offences
crime-assist-severity-critical = Critical Offences

# --- Start & Category Questions ---
crime-assist-q-start = The incident under review involves...
crime-assist-q-category-person = ...harm to a person?
crime-assist-q-category-property = ...damage to property/station?
crime-assist-q-category-theft = ...theft?
crime-assist-q-category-possession = ...illegal possession of items?
crime-assist-q-category-authority = ...disruption of order/authority?

# --- Harm Person Questions ---
crime-assist-q-harmperson-death = Did the incident result in death?
crime-assist-q-death-intentional = Was there intent to kill (malice, premeditation)?
crime-assist-q-death-attempt = Was it an attempted murder that failed (e.g., use of lethal weapon without justification)?
crime-assist-q-death-grossnegligence = Did the death occur due to gross negligence (blatant disregard for safety leading to severe consequences)?
crime-assist-q-death-assaultresult = Did the death occur as a result of an assault (even if intent to kill is not proven)?
crime-assist-q-death-assaultvictim = Was the victim of the assault resulting in death an official (Nanotrasen contract holder)?
crime-assist-q-death-simplenegligence = Did the death occur due to simple negligence (negligence that halted work, but without gross disregard)?
crime-assist-q-harmperson-kidnapping = Was a person unlawfully held under duress / against their will?
crime-assist-q-kidnap-victimtype = Was the victim of kidnapping an official (Nanotrasen contract holder)?
crime-assist-q-harmperson-assault = Was it an assault (physical harm, unwanted contact, excessive force without intent to kill)?
crime-assist-q-assault-victimtype = Was the victim of the assault an official (Nanotrasen contract holder)?
crime-assist-q-harmperson-harassment = Was it harassment (intimidation, threats, verbal abuse, lewd acts)?
crime-assist-q-harmperson-brawl = Was it a brawl (minor physical force, <4 hits, pushing, stunning)?

# --- Harm Property Questions ---
crime-assist-q-property-intent = Were the actions damaging property/station malicious (intentional sabotage/terrorism)?
crime-assist-q-property-terrorism = Was the goal mass destruction, intimidation, or destabilization for political/ideological reasons?
crime-assist-q-property-sabotagescale = Was the intentional sabotage major (serious threat to station/crew, releasing viruses, explosion, arson, AI destruction)?
crime-assist-q-property-creatingdanger = Did unintentional actions create danger for the crew/station (accidental plasma leak, slip hazard)?
crime-assist-q-property-vandalism = Was it minor intentional damage to station or property (not graffiti)?
crime-assist-q-property-equipmisuse = Was it misuse of equipment (using others' equipment or not for its intended purpose)?

# --- Theft Questions ---
# NOTE: These questions imply a linear check due to UI limits. Ideally use radio buttons/dropdown.
crime-assist-q-theft-itemtype-strategic-transport = Was a strategic transport vehicle stolen (Cargo/Sec/CC/ERT shuttle)?
crime-assist-q-theft-itemtype-nuclear-docs = Was the Nuclear Authentication Disk or classified documentation/blueprints stolen?
crime-assist-q-theft-itemtype-other-transport = Was another space vehicle stolen (mining, research)?
crime-assist-q-theft-itemtype-high-value = Were highly valuable/confidential items stolen (>1000cr, Captain/HoP ID, mechs, consoles)?
crime-assist-q-theft-itemtype-restricted = Were restricted/dangerous/valuable items stolen (weapons, safes, spacesuits, 251-1000cr)?
crime-assist-q-theft-itemtype-robbery = Was it robbery (theft directly from a person, <250cr)?
crime-assist-q-theft-itemtype-minor = Was it minor theft (common items, from general areas, <250cr)?

# --- Possession Questions ---
# NOTE: These questions imply a linear check due to UI limits. Ideally use radio buttons/dropdown.
crime-assist-q-possession-itemtype-contraband-s = Were the items Class S contraband?
crime-assist-q-possession-itemtype-contraband-a = Were the items Class A contraband?
crime-assist-q-possession-itemtype-contraband-c = Were the items Class C contraband?
crime-assist-q-possession-itemtype-weapon = Was it combat weaponry/ammunition?
crime-assist-q-possession-distrib-wpn = Was there distribution of combat weaponry/ammunition?
crime-assist-q-possession-itemtype-pyrotox = Was it pyrotechnics/toxins (C4, explosives, Lexorin, etc.)?
crime-assist-q-possession-distrib-tox = Was there distribution of pyrotechnics/toxins?
crime-assist-q-possession-itemtype-coldweapon = Was it a cold weapon not used for work?
crime-assist-q-possession-itemtype-drug = Was it recreational narcotics?
crime-assist-q-possession-distrib-drug = Was there distribution of recreational narcotics?
crime-assist-q-possession-itemtype-selfdef = Was it self-defense equipment used outside official duties?
crime-assist-q-possession-itemtype-confiscated = Was it confiscated items (evidence) used improperly?

# --- Authority/Order Questions ---
crime-assist-q-authority-mutiny = Were the actions aimed at overthrowing/undermining command without lawful cause?
crime-assist-q-authority-trespass = Was it trespassing into an area without proper access?
# NOTE: These questions imply a linear check due to UI limits. Ideally use radio buttons/dropdown.
crime-assist-q-authority-trespasslevel-secured = Was the area secured (Security, Command, EVA, Engineering, Atmos, Toxins)?
crime-assist-q-authority-trespasslevel-brokeninto = Was entry gained by breaking in (to any area)?
crime-assist-q-authority-trespasslevel-general = Was it trespassing into a general access area without authorization?
crime-assist-q-authority-riot = Was it participation in an unauthorized riot/disturbance that refused to disperse?
crime-assist-q-authority-hooliganism = Was it hooliganism (graffiti, disruptive behavior, littering flyers)?

# --- Crime Titles (Art. XXX Name) ---
crime-assist-title-100 = Art. 100 Vandalism
crime-assist-title-102 = Art. 102 Brawling
crime-assist-title-103 = Art. 103 Possession of Narcotics
crime-assist-title-104 = Art. 104 Possession of Self-Defense Equipment
crime-assist-title-105 = Art. 105 Hooliganism
crime-assist-title-106 = Art. 106 Equipment Misuse
crime-assist-title-107 = Art. 107 Petty Theft
crime-assist-title-108 = Art. 108 Trespassing
crime-assist-title-200 = Art. 200 Creating Workplace Hazard
crime-assist-title-201 = Art. 201 Harassment
crime-assist-title-202 = Art. 202 Assault
crime-assist-title-203 = Art. 203 Distribution of Narcotics
crime-assist-title-204 = Art. 204 Possession of Cold Weapons
crime-assist-title-205 = Art. 205 Negligence
crime-assist-title-206 = Art. 206 Misuse of Confiscated Items
crime-assist-title-207 = Art. 207 Robbery
crime-assist-title-208 = Art. 208 Breaking and Entering
crime-assist-title-300 = Art. 300 Sabotage
crime-assist-title-301 = Art. 301 Kidnapping
crime-assist-title-302 = Art. 302 Assaulting an Official
crime-assist-title-303 = Art. 303 Possession of Pyrotechnics/Toxins
crime-assist-title-304 = Art. 304 Possession of Combat Weaponry/Ammunition
crime-assist-title-305 = Art. 305 Rioting
crime-assist-title-306 = Art. 306 Possession of Class C Contraband
crime-assist-title-307 = Art. 307 Theft
crime-assist-title-308 = Art. 308 Trespassing in Secured Area
crime-assist-title-400 = Art. 400 Major Sabotage
crime-assist-title-401 = Art. 401 Kidnapping an Official
crime-assist-title-402 = Art. 402 Attempted Murder
crime-assist-title-403 = Art. 403 Trafficking Pyrotechnics/Toxins
crime-assist-title-404 = Art. 404 Arms Trafficking
crime-assist-title-405 = Art. 405 Gross Negligence
crime-assist-title-406 = Art. 406 Possession of Class A Contraband
crime-assist-title-407 = Art. 407 Grand Theft
crime-assist-title-408 = Art. 408 Hijacking Space Vehicle
crime-assist-title-500 = Art. 500 Terrorism
crime-assist-title-502 = Art. 502 Murder
crime-assist-title-505 = Art. 505 Mutiny
crime-assist-title-506 = Art. 506 Possession of Class S Contraband
crime-assist-title-507 = Art. 507 Theft of Classified Documents/Nuclear Equipment
crime-assist-title-508 = Art. 508 Hijacking Strategic Space Vehicle
crime-assist-title-innocent = No Crime Determined
crime-assist-title-error = Classification Error

# --- Crime Descriptions ---
crime-assist-desc-100 = Intentional minor damage to station or property (not graffiti).
crime-assist-desc-102 = Minor physical force without intent for serious harm (>3 disarm attempts, pushing, <4 hits, stunning).
crime-assist-desc-103 = Possession of recreational narcotics (Space Drugs, Ephedrine, THC, etc.).
crime-assist-desc-104 = Use of security/non-lethal equipment (flashes, batons, tasers) outside official duties.
crime-assist-desc-105 = Graffiti, inappropriate behavior, littering flyers (without incitement to mutiny).
crime-assist-desc-106 = Use of equipment not belonging to one's department/role, or not for its intended purpose.
crime-assist-desc-107 = Taking items from areas without access, or items belonging to others/station. Theft of credits up to 250cr.
crime-assist-desc-108 = Being in an area without authorized access (general areas).
crime-assist-desc-200 = Careless actions creating danger for crew/station (without malicious intent). E.g., accidental plasma leak, slip hazard.
crime-assist-desc-201 = Intimidation, threats, verbal abuse. Lewd gestures/actions towards a person. Unwanted touching.
crime-assist-desc-202 = Application of excessive physical force without clear intent to kill. Causing serious but non-critical harm. Forcible injection of toxins/narcotics.
crime-assist-desc-202-lethal = Assault (Art. 202) that unintentionally resulted in the victim's death.
crime-assist-desc-203 = Sale/transfer of recreational narcotics.
crime-assist-desc-204 = Possession of a dangerous item (saws, spears, axes) that is not part of one's job.
crime-assist-desc-205 = Negligence that did not affect the crew but halted work for a minor period. May include accidental death.
crime-assist-desc-206 = Use of confiscated items (evidence) not for their intended purpose.
crime-assist-desc-207 = Theft of items directly from a person. Theft of credits up to 250cr. Return stolen goods.
crime-assist-desc-208 = Breaking into a department by breaching a gateway/wall.
crime-assist-desc-300 = Malicious actions hindering the operation of crew/station (releasing N2O, welding doors, cutting power, barricades).
crime-assist-desc-301 = Holding a crew member under duress / against their will (not an official).
crime-assist-desc-302 = Assaulting a person with a Nanotrasen contract (not a passenger).
crime-assist-desc-302-lethal = Assaulting an official (Art. 302) that unintentionally resulted in the victim's death.
crime-assist-desc-303 = Possession: C4, explosives, Amatoxin, Lexorin, Toxin, Fentanyl, LSD, Unstable Mutagen.
crime-assist-desc-304 = Unauthorized possession of kinetic/laser weapons or ammunition (non-training).
crime-assist-desc-305 = Participation in an unauthorized riot/disturbance that refuses to disperse.
crime-assist-desc-306 = Possession of Class C contraband items.
crime-assist-desc-307 = Theft of restricted/dangerous/valuable items. Theft of credits 251-1000cr. (Weapons, safes, spacesuits). Breaking in = theft.
crime-assist-desc-308 = Being in a restricted area (Security, Command posts, EVA, Engineering, Atmos, Toxins) without permission.
crime-assist-desc-400 = Malicious destructive actions seriously endangering crew/station (releasing viruses, explosion, arson, AI destruction, multiple hull breaches).
crime-assist-desc-401 = Kidnapping a person with a Nanotrasen contract (not a passenger).
crime-assist-desc-402 = Attempted murder that failed. Application of excessive physical force with clear intent to kill (use of lethal weapons).
crime-assist-desc-403 = Distribution: C4, explosives, Amatoxin, Lexorin, Toxin, Fentanyl, LSD, Unstable Mutagen.
crime-assist-desc-404 = Distribution of kinetic/laser weapons or ammunition.
crime-assist-desc-405 = Negligence leading to severe consequences (injury/death of crew, department shutdown >20 min).
crime-assist-desc-406 = Possession of Class A contraband items.
crime-assist-desc-407 = Theft of high-value/confidential items. Theft of credits >1000cr. (Tech, docs, Captain/HoP ID, mechs, consoles).
crime-assist-desc-408 = Hijacking of mining, research, or other non-strategic shuttles.
crime-assist-desc-500 = Actions aimed at mass destruction, intimidation, destabilization for political/ideological purposes (nuclear explosion, bio-attack).
crime-assist-desc-502 = Intentional killing of a crew member (malice, premeditation). Unauthorized execution.
crime-assist-desc-505 = Actions (individual/group) to overthrow/undermine command without lawful cause.
crime-assist-desc-506 = Possession of highest-class contraband (Class S). Automatically classifies as Enemy of the Corporation.
crime-assist-desc-507 = Theft of the Nuclear Authentication Disk, classified research, blueprints, etc.
crime-assist-desc-508 = Hijacking: Cargo shuttle, Security shuttles, CC shuttles, ERT shuttles.
crime-assist-desc-innocent = Actions do not fall under any article of Space Law.
crime-assist-desc-error = Could not definitively classify the crime based on provided answers. Requires further investigation and decision by an authorized official.

# --- Crime Punishments ---
crime-assist-punish-100 = Punishment: 5 min / 100 cr + restitution
crime-assist-punish-102 = Punishment: 4 min / 200 cr
crime-assist-punish-103 = Punishment: 3 min + confiscation / 250 cr + confiscation
crime-assist-punish-104 = Punishment: 5 min + confiscation / 300 cr + confiscation
crime-assist-punish-105 = Punishment: 3 min / 200 cr
crime-assist-punish-106 = Punishment: 4 min / 200 cr
crime-assist-punish-107 = Punishment: 5 min + return / 250 cr + return
crime-assist-punish-108 = Punishment: Warning / 100 cr
crime-assist-punish-200 = Punishment: 7 min / 350 cr
crime-assist-punish-201 = Punishment: 10 min
crime-assist-punish-202 = Punishment: 10 min
crime-assist-punish-203 = Punishment: 8 min / 500 cr + confiscation
crime-assist-punish-204 = Punishment: 7 min + confiscation / 400 cr + confiscation
crime-assist-punish-205 = Punishment: 10 min
crime-assist-punish-206 = Punishment: 8 min / 500 cr + confiscation
crime-assist-punish-207 = Punishment: 10 min + return
crime-assist-punish-208 = Punishment: 7 min / 400 cr
crime-assist-punish-300 = Punishment: 13 min
crime-assist-punish-301 = Punishment: 15 min
crime-assist-punish-302 = Punishment: 15 min
crime-assist-punish-303 = Punishment: 11 min + confiscation / 750 cr + confiscation
crime-assist-punish-304 = Punishment: 12 min + confiscation / 1200 cr + confiscation
crime-assist-punish-305 = Punishment: 15 min
crime-assist-punish-306 = Punishment: 15 min + confiscation / 1500 cr + confiscation
crime-assist-punish-307 = Punishment: 15 min + return
crime-assist-punish-308 = Punishment: 14 min
crime-assist-punish-400 = Punishment: 20 min
crime-assist-punish-401 = Punishment: 20 min
crime-assist-punish-402 = Punishment: 20 min
crime-assist-punish-403 = Punishment: 18 min + equipment confiscation
crime-assist-punish-404 = Punishment: 20 min + equipment confiscation
crime-assist-punish-405 = Punishment: 20 min
crime-assist-punish-406 = Punishment: 16 min / 1700 cr + confiscation
crime-assist-punish-407 = Punishment: 20 min + return
crime-assist-punish-408 = Punishment: 20 min
crime-assist-punish-500 = Punishment: 30 min / Permanent / Execution (at Magistrate/Captain's discretion)
crime-assist-punish-502 = Punishment: Permanent / Execution (at Magistrate/Captain's discretion)
crime-assist-punish-505 = Punishment: Permanent / Execution (at Magistrate/Captain's discretion)
crime-assist-punish-506 = Punishment: Permanent (Enemy of the Corporation)
crime-assist-punish-507 = Punishment: Permanent / Execution (at Magistrate/Captain's discretion)
crime-assist-punish-508 = Punishment: 30 min
crime-assist-punish-innocent = No punishment necessary.
crime-assist-punish-error = Requires decision by an authorized official.

## MailMetrics

# General
mail-metrics-program-name = MailMetrics
mail-metrics-header = Income from Mail Deliveries
mail-metrics-opened = Earnings (Opened)
mail-metrics-expired = Losses (Expired)
mail-metrics-damaged = Losses (Damaged)
mail-metrics-tampered = Losses (Tampered)
mail-metrics-unopened = Unopened
mail-metrics-count-header = Packages
mail-metrics-money-header = Spesos
mail-metrics-total = Total
mail-metrics-progress = {$opened} out of {$total} packages opened!
mail-metrics-progress-percent = Success rate: {$successRate}%

## StockTrading

# General
stock-trading-program-name = StockTrading
stock-trading-title = Intergalactic Stock Market
stock-trading-balance = Balance: {$balance} credits
stock-trading-no-entries = No entries
stock-trading-owned-shares = Owned: {$shares}
stock-trading-buy-button = Buy
stock-trading-sell-button = Sell
stock-trading-amount-placeholder = Amount
stock-trading-price-history = Price History

## NanoChat

# General
nano-chat-program-name = NanoChat
nano-chat-title = NanoChat
nano-chat-new-chat = New Chat
nano-chat-contacts = CONTACTS
nano-chat-no-chats = No active chats
nano-chat-select-chat = Select a chat to begin
nano-chat-message-placeholder = Type a message...
nano-chat-send = Send
nano-chat-edit = Edit
nano-chat-delete = Delete
nano-chat-loading = Loading...
nano-chat-message-too-long = Message too long ({$current}/{$max} characters)
nano-chat-max-recipients = Maximum number of chats reached
nano-chat-new-message-title = message from {$sender}
nano-chat-new-message-title-recipient = {$sender} ({$jobTitle})
nano-chat-new-message-body = {$message}
nano-chat-toggle-mute = Mute notifications
nano-chat-delivery-failed = Failed to deliver
nano-chat-look-up-no-server = No valid telecommunications server found
nano-chat-look-up = Look up numbers
nano-chat-list-number = List number

# Create chat popup
nano-chat-new-title = Add a new chat
nano-chat-number-label = Number
nano-chat-name-label = Name
nano-chat-job-label = Job title
nano-chat-number-placeholder = Enter a number
nano-chat-name-placeholder = Enter a name
nano-chat-job-placeholder = Enter a job title (optional)
nano-chat-cancel = Cancel
nano-chat-create = Create

# Edit chat popup
nano-chat-edit-title = Edit a contact
nano-chat-confirm = Confirm

# LogProbe additions
log-probe-scan-nanochat = Scanned {$card}'s NanoChat logs
log-probe-header-access = Access Log Scanner
log-probe-header-nanochat = NanoChat Log Scanner
log-probe-label-message = Message
log-probe-card-number = Card: {$number}
log-probe-recipients = {$count} Recipients
log-probe-recipient-list = Known Recipients:
log-probe-message-format = {$sender} → {$recipient}: {$content}
