- type: entity
  abstract: true
  parent: BaseItem
  id: OreBase
  description: "Шматок необробленої руди."
  components:
  - type: Sprite
    sprite: Objects/Materials/ore.rsi
  - type: Item
    sprite: Objects/Materials/ore.rsi
    size: Normal
  - type: Tag
    tags:
    - Ore
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.2
        density: 20
        mask:
          - ItemMask
        restitution: 0.3
        friction: 0.2
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]

- type: entity
  parent: OreBase
  id: GoldOre
  name: "золота руда"
  suffix: Full
  components:
  - type: Stack
    stackType: GoldOre
  - type: Sprite
    state: gold
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      RawGold: 500
  - type: Extractable
    grindableSolutionName: goldore
  - type: SolutionContainerManager
    solutions:
      goldore:
        reagents:
        - ReagentId: Gold
          Quantity: 10

- type: entity
  parent: GoldOre
  id: GoldOre1
  suffix: Single
  components:
  - type: Stack
    count: 1

- type: entity
  parent: OreBase
  id: DiamondOre
  name: "алмазна руда"
  suffix: Full
  components:
  - type: Stack
    stackType: DiamondOre
  - type: Sprite
    state: diamond
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      RawDiamond: 500
  - type: Extractable
    grindableSolutionName: diamondore
  - type: SolutionContainerManager
    solutions:
      diamondore:
        reagents:
        - ReagentId: Carbon
          Quantity: 20

- type: entity
  parent: DiamondOre
  id: DiamondOre1
  suffix: Single
  components:
  - type: Stack
    count: 1

- type: entity
  parent: OreBase
  id: SteelOre
  name: "залізна руда"
  suffix: Full
  components:
  - type: Stack
    stackType: SteelOre
  - type: Sprite
    state: iron
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      RawIron: 500
  - type: Extractable
    grindableSolutionName: ironore
  - type: SolutionContainerManager
    solutions:
      ironore:
        reagents:
        - ReagentId: Iron
          Quantity: 10

- type: entity
  id: SteelOre1
  parent: SteelOre
  suffix: Single
  components:
    - type: Stack
      count: 1

- type: entity
  parent: OreBase
  id: PlasmaOre
  name: "плазмова руда"
  suffix: Full
  components:
  - type: Stack
    stackType: PlasmaOre
  - type: Sprite
    state: plasma
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      RawPlasma: 500
  - type: PointLight
    radius: 1.2
    energy: 0.6
    castShadows: false
    color: "#e592e7"
  - type: Extractable
    grindableSolutionName: plasmaore
  - type: SolutionContainerManager
    solutions:
      plasmaore:
        reagents:
        - ReagentId: Plasma
          Quantity: 10

- type: entity
  parent: PlasmaOre
  id: PlasmaOre1
  suffix: Single
  components:
    - type: Stack
      count: 1

- type: entity
  parent: OreBase
  id: SilverOre
  name: "срібна руда"
  suffix: Full
  components:
  - type: Stack
    stackType: SilverOre
  - type: Sprite
    state: silver
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      RawSilver: 500
  - type: Extractable
    grindableSolutionName: silverore
  - type: SolutionContainerManager
    solutions:
      silverore:
        reagents:
        - ReagentId: Silver
          Quantity: 10

- type: entity
  parent: SilverOre
  id: SilverOre1
  suffix: Single
  components:
  - type: Stack
    count: 1

- type: entity
  parent: OreBase
  id: SpaceQuartz
  name: "космічний кварц"
  suffix: Full
  components:
  - type: Stack
    stackType: SpaceQuartz
  - type: Sprite
    state: spacequartz
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      RawQuartz: 500
  - type: Extractable
    grindableSolutionName: quartzore
  - type: SolutionContainerManager
    solutions:
      quartzaore:
        reagents:
        - ReagentId: Silicon
          Quantity: 10

- type: entity
  parent: SpaceQuartz
  id: SpaceQuartz1
  suffix: Single
  components:
  - type: Stack
    count: 1

- type: entity
  parent: OreBase
  id: UraniumOre
  name: "уранова руда"
  suffix: Full
  components:
  - type: Stack
    stackType: UraniumOre
  - type: Sprite
    state: uranium
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      RawUranium: 500
  - type: PointLight
    radius: 1.2
    energy: 0.8
    castShadows: false
    color: "#9be792"
  - type: Extractable
    grindableSolutionName: uraniumore
  - type: SolutionContainerManager
    solutions:
      uraniumore:
        reagents:
        - ReagentId: Uranium
          Quantity: 8
        - ReagentId: Radium
          Quantity: 2
        canReact: false

- type: entity
  parent: UraniumOre
  id: UraniumOre1
  suffix: Single
  components:
  - type: Stack
    count: 1

- type: entity
  parent: OreBase
  id: BananiumOre
  name: "бананіумна руда"
  suffix: Full
  components:
  - type: Stack
    stackType: BananiumOre
  - type: Sprite
    state: bananium
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      RawBananium: 500
  - type: PointLight
    radius: 1.2
    energy: 1
    castShadows: false
    color: "#eef066"
  - type: Extractable
    grindableSolutionName: bananiumore
  - type: SolutionContainerManager
    solutions:
      bananiumore:
        reagents:
        - ReagentId: Nutriment
          Quantity: 4
        - ReagentId: Vitamin
          Quantity: 2
        - ReagentId: Honk
          Quantity: 5

- type: entity
  parent: BananiumOre
  id: BananiumOre1
  suffix: Single
  components:
  - type: Stack
    count: 1

- type: entity
  parent: OreBase
  id: Coal
  name: "вугілля"
  suffix: Full
  components:
  - type: Stack
    stackType: Coal
  - type: Sprite
    state: coal
  - type: Material
  - type: Extractable
    grindableSolutionName: coal
  - type: SolutionContainerManager
    solutions:
      coal:
        reagents:
        - ReagentId: Carbon
          Quantity: 8.4
        - ReagentId: Ammonia
          Quantity: 0.8
        - ReagentId: Hydrogen
          Quantity: 0.5
        - ReagentId: Sulfur
          Quantity: 0.2
        - ReagentId: Mercury
          Quantity: 0.1
  - type: PhysicalComposition
    materialComposition:
      Coal: 500

- type: entity
  parent: Coal
  id: Coal1
  suffix: Single
  components:
  - type: Stack
    count: 1

- type: entity
  parent: OreBase
  id: SaltOre
  name: "сіль"
  suffix: Full
  components:
  - type: Stack
    stackType: SaltOre
  - type: Sprite
    state: salt
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      RawSalt: 500
  - type: Extractable
    grindableSolutionName: saltore
  - type: SolutionContainerManager
    solutions:
      saltore:
        reagents:
        - ReagentId: TableSalt
          Quantity: 10
        - ReagentId: Iodine
          Quantity: 5

- type: entity
  parent: SaltOre
  id: Salt1
  suffix: Single
  components:
  - type: Stack
    count: 1

- type: entity
  parent: OreBase
  id: BluespaceOre
  name: "блюспейс руда"
  suffix: Full
  components:
  - type: Stack
    stackType: BluespaceOre
  - type: Sprite
    state: bluespace
  - type: Item
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      RawBluespace: 100

- type: entity
  parent: BluespaceOre
  id: BluespaceOre1
  suffix: Single
  components:
  - type: Stack
    count: 1

- type: entity
  parent: OreBase
  id: NormalityOre
  name: "нормальна руда"
  suffix: Full
  components:
  - type: Stack
    stackType: NormalityOre
  - type: Sprite
    state: normality
  - type: Item
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      RawNormality: 100

- type: entity
  parent: NormalityOre
  id: NormalityOre1
  suffix: Single
  components:
  - type: Stack
    count: 1

- type: entity
  parent: OreBase
  id: TungstenOre
  name: "вольфраміт"
  suffix: Full
  components:
  - type: Stack
    stackType: TungstenOre
  - type: Sprite
    state: tungsten
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      RawTungsten: 500

- type: entity
  parent: TungstenOre
  id: TungstenOre1
  suffix: Single
  components:
  - type: Stack
    count: 1
