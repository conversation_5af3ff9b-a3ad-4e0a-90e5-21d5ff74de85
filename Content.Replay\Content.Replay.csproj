<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>$(TargetFramework)</TargetFramework>
    <LangVersion>12</LangVersion>
    <IsPackable>false</IsPackable>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <OutputPath>..\bin\Content.Replay\</OutputPath>
    <OutputType Condition="'$(FullRelease)' != 'True'">Exe</OutputType>
    <WarningsAsErrors>nullable</WarningsAsErrors>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Nett" />
    <PackageReference Include="JetBrains.Annotations" PrivateAssets="All" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RobustToolbox\Lidgren.Network\Lidgren.Network.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.Shared.Maths\Robust.Shared.Maths.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.Shared\Robust.Shared.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.Client\Robust.Client.csproj" />
    <ProjectReference Include="..\Content.Shared\Content.Shared.csproj" />
    <ProjectReference Include="..\Content.Client\Content.Client.csproj" />
  </ItemGroup>
  <Import Project="..\RobustToolbox\MSBuild\XamlIL.targets" />
  <Import Project="..\RobustToolbox\MSBuild\Robust.Properties.targets" />
</Project>
