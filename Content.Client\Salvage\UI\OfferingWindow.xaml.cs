using Content.Client.Computer;
using Content.Client.UserInterface.Controls;
using Content.Shared.Shuttles.BUIStates;
using Robust.Client.AutoGenerated;
using Robust.Client.Graphics;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Timing;

namespace Content.Client.Salvage.UI;

/// <summary>
/// Generic window for offering multiple selections with a timer.
/// </summary>
[GenerateTypedNameReferences]
public sealed partial class OfferingWindow : FancyWindow,
    IComputerWindow<EmergencyConsoleBoundUserInterfaceState>
{
    [Dependency] private readonly IGameTiming _timing = default!;

    public bool Claimed;
    public TimeSpan NextOffer;
    private TimeSpan? _progression;

    /// <summary>
    /// Time between NextOffers
    /// </summary>
    public TimeSpan Cooldown;

    /// <summary>
    /// Time between Progressions
    /// </summary>
    public TimeSpan ProgressionCooldown;

    /// <summary>
    /// Secondary timer used for tracking active progress.
    /// </summary>
    public TimeSpan? Progression
    {
        get => _progression;
        set
        {
            if (_progression == value)
                return;

            _progression = value;

            if (value == null)
            {
                ProgressionBox.Visible = false;
            }
            else
            {
                ProgressionBox.Visible = true;
            }
        }
    }

    public OfferingWindow()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        ProgressionBar.ForegroundStyleBoxOverride = new StyleBoxFlat(Color.FromHex("#C74EBD"));
    }

    public void AddOption(OfferingWindowOption option)
    {
        Container.AddChild(option);
    }

    public void ClearOptions()
    {
        Container.DisposeAllChildren();
    }

    protected override void FrameUpdate(FrameEventArgs args)
    {
        base.FrameUpdate(args);

        if (_progression != null)
        {
            var remaining = _progression.Value - _timing.CurTime;

            if (remaining < TimeSpan.Zero)
            {
                ProgressionBar.Value = 1f;
                ProgressionText.Text = "00:00";
            }
            else
            {
                ProgressionBar.Value = 1f - (float) (remaining / ProgressionCooldown);
                ProgressionText.Text = $"{remaining.Minutes:00}:{remaining.Seconds:00}";
            }
        }

        if (Claimed)
        {
            NextOfferBar.Value = 1f;
            NextOfferText.Text = "00:00";
        }
        else
        {
            var remaining = NextOffer - _timing.CurTime;

            if (remaining < TimeSpan.Zero)
            {
                NextOfferBar.Value = 1f;
                NextOfferText.Text = "00:00";
            }
            else
            {
                NextOfferBar.Value = 1f - (float) (remaining / Cooldown);
                NextOfferText.Text = $"{remaining.Minutes:00}:{remaining.Seconds:00}";
            }
        }
    }
}
