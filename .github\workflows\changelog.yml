name: PR Changelogs
concurrency: commit_action
on:
  pull_request_target:
    types: [closed]

env:
  GITHUB_TOKEN: ${{ secrets.BOT_TOKEN }}
  CHANGELOG_DIR: ${{ vars.CHANGELOG_DIR }}
  PR_NUMBER: ${{ github.event.number }}

jobs:
  changelog:
    runs-on: ubuntu-latest
    if: github.event.pull_request.merged == true
    permissions:
      contents: write
    steps:
    - name: Checkout Master
      uses: actions/checkout@v3
      with:
        token: ${{ secrets.BOT_TOKEN }}
        ref: ${{ vars.CHANGELOG_BRANCH }}

    - name: Setup Git
      run: |
        git config --global user.name "${{ vars.CHANGELOG_USER }}"
        git config --global user.email "${{ vars.CHANGELOG_EMAIL }}"
      shell: bash

    - name: Setup Node
      uses: actions/setup-node@v3
      with:
        node-version: 18.x

    - name: Install Dependencies
      run: |
        cd "Tools/changelogs"
        npm install
      shell: bash

    - name: Generate Changelog
      run: |
        cd "Tools/changelogs"
        node changelog.js
      shell: bash

    - name: Commit Changelog
      run: |
        git pull origin master
        git add *.yml
        git commit -m "${{ vars.CHANGELOG_MESSAGE }} (#${{ env.PR_NUMBER }})"
        git push
      shell: bash
      continue-on-error: true
