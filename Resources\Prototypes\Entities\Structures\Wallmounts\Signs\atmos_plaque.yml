﻿- type: entity
  parent: BaseSign
  id: PlaqueAtmos
  name: "атмосферна табличка"
  components:
  - type: WallMount
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.3,-0.3,0.3,0.3"
        layer:
        - MidImpassable
  - type: Sprite
    layers:
      - state: atmosplaque
        map: ["plaque"]
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.AtmosPlaqueVisuals.State:
        plaque:
          zumosplaque: { state: zumosplaque }
          atmosplaque: { state: atmosplaque }
  - type: AtmosPlaque
