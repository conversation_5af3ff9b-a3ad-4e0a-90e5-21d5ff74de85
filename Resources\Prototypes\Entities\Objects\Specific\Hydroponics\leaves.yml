# Leaves

- type: entity
  name: "листя конопель"
  parent: ProduceBase
  id: LeavesCannabis
  description: "Нещодавно легалізований у більшості галактик."
  components:
  - type: Sprite
    sprite: Objects/Specific/Hydroponics/cannabis.rsi
  - type: Produce
    seedId: cannabis
  - type: Food
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: THC
          Quantity: 15


- type: entity
  name: "сушене листя конопель"
  parent: BaseItem
  id: LeavesCannabisDried
  description: "Висушене листя конопель, готове до подрібнення."
  components:
  - type: Stack
    stackType: LeavesCannabisDried
    count: 1
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: THC
          Quantity: 12
  - type: Sprite
    sprite: Objects/Specific/Hydroponics/tobacco.rsi
    state: dried

- type: entity
  name: "мелений канабіс"
  parent: BaseItem
  id: GroundCannabis
  description: "Мелений канабіс, готовий взяти вас у подорож."
  components:
  - type: Stack
    stackType: GroundCannabis
    count: 1
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: THC
          Quantity: 20
  - type: Sprite
    sprite: Objects/Misc/reagent_fillings.rsi
    state: powderpile
    color: darkgreen
  - type: Construction
    graph: smokeableGroundCannabis
    node: ground
  - type: Tag
    tags:
      - Smokable
  - type: Item
    size: Tiny
    
- type: entity
  name: "райдужні листя канабісу"
  parent: LeavesCannabis
  id: LeavesCannabisRainbow
  description: "Вона має так світитися...?"
  components:
  - type: Sprite
    sprite: Objects/Specific/Hydroponics/rainbow_cannabis.rsi
  - type: Produce
    seedId: rainbowCannabis
  - type: PointLight
    radius: 1.5
    energy: 2
  - type: RgbLightController
    cycleRate: 0.6
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: SpaceDrugs
          Quantity: 3
        - ReagentId: Lipolicide
          Quantity: 3
        - ReagentId: MindbreakerToxin
          Quantity: 2
        - ReagentId: Happiness
          Quantity: 2
#       - ReagentId: ColorfulReagent
#         Quantity: 1
        - ReagentId: Psicodine
          Quantity: 0.6

- type: entity
  name: "сушене листя райдужної коноплі"
  parent: LeavesCannabisDried
  id: LeavesCannabisRainbowDried
  description: "Висушене райдужне листя канабісу, готове до подрібнення."
  components:
  - type: Stack
    stackType: LeavesCannabisRainbowDried
    count: 1
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 8.5 #fuck you saveload test fail
        reagents:
        - ReagentId: SpaceDrugs
          Quantity: 2.4
        - ReagentId: Lipolicide
          Quantity: 2.4
        - ReagentId: MindbreakerToxin
          Quantity: 1.6
        - ReagentId: Happiness
          Quantity: 1.6
#       - ReagentId: ColorfulReagent
#         Quantity: 0.8
        - ReagentId: Psicodine
          Quantity: 0.48
  - type: Sprite
    sprite: Objects/Specific/Hydroponics/rainbow_cannabis.rsi
    state: dried

- type: entity
  name: "мелений райдужний канабіс"
  parent: GroundCannabis
  id: GroundCannabisRainbow
  description: "Мелений райдужний канабіс, готовий до подорожі."
  components:
  - type: Stack
    stackType: GroundCannabisRainbow
    count: 1
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: SpaceDrugs
          Quantity: 4
        - ReagentId: Lipolicide
          Quantity: 4
        - ReagentId: MindbreakerToxin
          Quantity: 2.66
        - ReagentId: Happiness
          Quantity: 2.66
#       - ReagentId: ColorfulReagent
#         Quantity: 1.33
        - ReagentId: Psicodine
          Quantity: 0.8
  - type: Sprite
    sprite: Objects/Specific/Hydroponics/rainbow_cannabis.rsi
    state: powderpile_rainbow
    color: white
  - type: Construction
    graph: smokeableGroundCannabisRainbow
    node: groundRainbow

- type: entity
  name: "тютюнове листя"
  parent: ProduceBase
  id: LeavesTobacco
  description: "Висушіть їх, щоб вийшло трохи диму."
  components:
  - type: Sprite
    sprite: Objects/Specific/Hydroponics/tobacco.rsi
  - type: Produce
    seedId: tobacco

- type: entity
  name: "сушене листя тютюну"
  parent: BaseItem
  id: LeavesTobaccoDried
  description: "Висушене листя тютюну, готове до подрібнення."
  components:
  - type: Stack
    stackType: LeavesTobaccoDried
    count: 1
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nicotine
          Quantity: 2
  - type: Sprite
    sprite: Objects/Specific/Hydroponics/tobacco.rsi
    state: dried

- type: entity
  name: "мелений тютюн"
  parent: BaseItem
  id: GroundTobacco
  description: "Мелений тютюн, ідеально підходить для ручного скручування сигарет."
  components:
  - type: Stack
    stackType: GroundTobacco
    count: 1
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nicotine
          Quantity: 10
  - type: Sprite
    sprite: Objects/Misc/reagent_fillings.rsi
    state: powderpile
    color: brown
  - type: Construction
    graph: smokeableGroundTobacco
    node: ground
  - type: Tag
    tags:
      - Smokable
  - type: Item
    size: Tiny