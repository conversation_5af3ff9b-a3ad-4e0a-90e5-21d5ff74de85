- type: entity
  parent: BaseItem
  id: ChameleonProjector
  name: "проектор-хамелеон"
  description: "Технологія голопаразитів використовується для створення жорсткої світлової копії будь-якого об'єкта навколо вас. Маскування знищується при піднятті або деактивації."
  components:
  - type: Sprite
    sprite: /Textures/Objects/Devices/chameleon_projector.rsi
    state: icon
  - type: ChameleonProjector
    whitelist:
      components:
      - Anchorable
      - Item
    blacklist:
      components:
      - ChameleonDisguise # no becoming kleiner
      - InsideEntityStorage # no clark kent going in phone booth and becoming superman
      - MindContainer # no
      - Pda # PDAs currently make you invisible /!\
    polymorph:
      entity: ChameleonDisguise

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseMob
  id: ChameleonDisguise
  name: "Уріст МакКляйнер"
  components:
  # this and the name/desc get replaced, this is just placeholder incase something goes wrong
  - type: Sprite
    sprite: /Textures/Mobs/Species/Human/parts.rsi
    state: full
  # so people can attempt to pick it up
  - type: Item
  # so it can take damage
  # projector system sets health to be proportional to mass
  - type: Damageable
  - type: MobState
  - type: MobThresholds
    thresholds:
      0: Alive
      1: Critical
      200: Dead
  - type: MovementSpeedModifier
    baseWalkSpeed: 1 # precise movement for the perfect spot
    baseSprintSpeed: 5 # the jig is up
  - type: ChameleonDisguise

# actions
- type: entity
  categories: [ HideSpawnMenu ]
  id: ActionDisguiseNoRot
  name: "Перемкнути Обертання"
  description: "Використовуйте його, щоб запобігти обертанню маскування, що полегшує ховання в деяких сценаріях."
  components:
  - type: InstantAction
    icon: Interface/VerbIcons/refresh.svg.192dpi.png
    event: !type:DisguiseToggleNoRotEvent

- type: entity
  categories: [ HideSpawnMenu ]
  id: ActionDisguiseAnchor
  name: "Перемкнути Закріплення"
  description: "Для багатьох об'єктів ви захочете закріпитися, щоб не бути повністю очевидними."
  components:
  - type: InstantAction
    icon:
      sprite: Objects/Tools/wrench.rsi
      state: icon
    event: !type:DisguiseToggleAnchoredEvent
