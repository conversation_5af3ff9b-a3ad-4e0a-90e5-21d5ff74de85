<Control xmlns="https://spacestation14.io"
         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
         xmlns:style="clr-namespace:Content.Client.Stylesheets">
    <ScrollContainer>
        <BoxContainer Orientation="Vertical" Margin="5">
            <!-- Ping Name Settings -->
            <Label Text="{Loc 'ui-options-pirate-ping-name-title'}" StyleClasses="LabelHeading"/>

            <!-- Enable Ping Name -->
            <BoxContainer Orientation="Horizontal" Margin="0 5">
                <CheckBox Name="PingNameEnabledCheckBox"/>
                <Label Text="{Loc 'ui-options-pirate-ping-name-enabled'}" Margin="5 0 0 0"/>
            </BoxContainer>

            <!-- Enable Sound -->
            <BoxContainer Orientation="Horizontal" Margin="0 5">
                <CheckBox Name="PingNameSoundsCheckBox"/>
                <Label Text="{Loc 'ui-options-pirate-ping-name-sound-enabled'}" Margin="5 0 0 0"/>
            </BoxContainer>

            <!-- Enable Self Sounds -->
            <BoxContainer Orientation="Horizontal" Margin="0 5">
                <CheckBox Name="PingNameSelfSoundsCheckBox"/>
                <Label Text="{Loc 'ui-options-pirate-ping-name-self-sound-enabled'}" Margin="5 0 0 0"/>
            </BoxContainer>

            <!-- Sound Selection -->
            <BoxContainer Orientation="Horizontal" Margin="0 5">
                <Label Text="{Loc 'ui-options-pirate-ping-name-sound'}" MinSize="150 0"/>
                <OptionButton Name="PingSoundSelector" MinSize="200 0"/>
                <Button Name="TestSoundButton"
                       Text="{Loc 'ui-options-pirate-ping-name-sound-preview'}"
                       Margin="5 0 0 0"/>
            </BoxContainer>

            <!-- Sound Cooldown -->
            <BoxContainer Orientation="Horizontal" Margin="0 5">
                <Label Text="{Loc 'ui-options-pirate-ping-name-sound-cooldown'}" MinSize="150 0"/>
                <SpinBox Name="SoundCooldownSpinBox"
                        MinValue="0" MaxValue="300"
                        MinSize="100 0"
                        Value="15"/>
                <Label Text="{Loc 'ui-options-pirate-ping-name-sound-cooldown-suffix'}" Margin="5 0 0 0"/>
            </BoxContainer>

            <!-- Color Settings -->
            <Label Text="{Loc 'ui-options-pirate-ping-name-color-title'}" StyleClasses="LabelHeading" Margin="0 10 0 5"/>

            <!-- Color Preview and RGB Sliders -->
            <BoxContainer Orientation="Horizontal" Margin="0 5">
                <BoxContainer Orientation="Vertical" MinSize="200 0">
                    <Label Text="{Loc 'ui-options-pirate-ping-name-color-preview'}" Margin="0 0 0 5"/>
                    <PanelContainer Name="PingNameColorPreview"
                           MinSize="30 30"
                           ToolTip="{Loc 'ui-options-pirate-ping-name-color-preview-tooltip'}"/>
                </BoxContainer>

                <controls:ColorSliderControl Name="ColorSliders" HorizontalExpand="True" Margin="10 0 0 0"/>
            </BoxContainer>

            <Button Name="PingNameColorReset"
                   Text="{Loc 'ui-options-pirate-ping-name-color-reset'}"
                   HorizontalAlignment="Center"
                   Margin="0 5 0 0"/>

            <!-- Custom Words -->
            <Label Text="{Loc 'ui-options-pirate-ping-name-custom-words-title'}" StyleClasses="LabelHeading" Margin="0 10 0 5"/>
            <BoxContainer Orientation="Horizontal" Margin="0 5">
                <Label Text="{Loc 'ui-options-pirate-ping-name-custom-words'}" MinSize="150 0"/>
                <LineEdit Name="CustomWordsLineEdit"
                         HorizontalExpand="True"
                         PlaceHolder="{Loc 'ui-options-pirate-ping-name-custom-words-placeholder'}"/>
            </BoxContainer>

            <!-- Apply/Reset Buttons -->
            <BoxContainer Orientation="Horizontal" HorizontalAlignment="Center" Margin="0 20 0 0">
                <Button Name="ApplyButton"
                       Text="{Loc 'ui-options-apply'}"
                       Margin="0 0 5 0"/>
                <Button Name="ResetButton"
                       Text="{Loc 'ui-options-reset'}"
                       Margin="5 0 0 0"/>
            </BoxContainer>
        </BoxContainer>
    </ScrollContainer>
</Control>
