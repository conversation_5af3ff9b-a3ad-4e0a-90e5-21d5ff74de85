- type: entity
  parent: MobSiliconBase
  id: MobDisablerBot
  name: "disablerbot"
  description: "Захищає станцію від ворожих диких тварин."
  components:
  - type: StaticPrice
    price: 545
  - type: Sprite
    sprite: Mobs/Silicon/Bots/disablerbot.rsi
    state: disablerbot
  - type: Construction
    graph: DisablerBot
    node: bot
  - type: SentienceTarget
    flavorKind: station-event-random-sentience-flavor-mechanical
  - type: UseDelay
    delay: 1
  - type: NpcFactionMember
    factions:
    - NanoTrasen
  - type: CombatMode
  - type: EmagReplaceFactions
    factions:
    - Syndicate
  - type: Anchorable
  - type: BatterySelfRecharger
    autoRecharge: true
    autoRechargeRate: 300
  - type: Battery
    maxCharge: 10000
    startingCharge: 10000
  - type: Gun
    fireRate: 1
    minAngle: 2
    maxAngle: 7
    angleIncrease: 2
    angleDecay: 5
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/taser2.ogg
  - type: ProjectileBatteryAmmoProvider
    proto: BulletDisablerSmg
    fireCost: 1
  - type: MobThresholds
    thresholds:
      0: Alive
      70: Dead
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 70
      behaviors:
      - !type:TriggerBehavior
    - trigger:
        !type:DamageTrigger
        damage: 80
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          ProximitySensor:
            min: 1
            max: 1
      - !type:SpawnEntitiesBehavior
        spawn:
          WeaponDisabler:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: MovementSpeedModifier
    baseWalkSpeed: 2
    baseSprintSpeed: 3
  - type: HTN
    rootTask:
      task: DisablerbotCompound
    blackboard:
      AttackDelayDeviation: !type:Single
        0.3
  - type: InteractionPopup
    interactSuccessString: petting-success-disablerbot
    interactFailureString: petting-failure-disablerbot
    interactSuccessSound:
      path: /Audio/Ambience/Objects/periodic_beep.ogg
