# HTN поведінка для слуг некроманта - Покращена версія
- type: htnCompound
  id: NecromancyServantCompound
  branches:
  - tasks:
    - !type:HTNCompoundTask
      task: NecromancyServantCheckLoyalty
    - !type:HTNCompoundTask
      task: NecromancyServantCheckCommand
    - !type:HTNCompoundTask
      task: NecromancyServantFollowMaster
    - !type:HTNCompoundTask
      task: NecromancyServantDefendMaster
    - !type:HTNCompoundTask
      task: NecromancyServantAttackEnemies
    - !type:HTNCompoundTask
      task: NecromancyServantPatrol
    - !type:HTNCompoundTask
      task: NecromancyServantIdle

# Перевірка лояльності
- type: htnCompound
  id: NecromancyServantCheckLoyalty
  branches:
  - preconditions:
    - !type:KeyExistsPrecondition
      key: LoyaltyLevel
      value: Rebel
    tasks:
    - !type:HTNCompoundTask
      task: NecromancyServantRebel
  - preconditions:
    - !type:KeyExistsPrecondition
      key: LoyaltyLevel
      value: Low
    tasks:
    - !type:HTNCompoundTask
      task: NecromancyServantHesitate

# Повстання слуги
- type: htnCompound
  id: NecromancyServantRebel
  branches:
  - preconditions:
    - !type:KeyExistsPrecondition
      key: NecromancyMaster
    tasks:
    - !type:HTNPrimitiveTask
      operator: !type:MoveToOperator
        pathfindKey: NecromancyMaster
        removeKeyOnFinish: false
    - !type:HTNPrimitiveTask
      operator: !type:MeleeOperator
        targetKey: NecromancyMaster

# Вагання слуги
- type: htnCompound
  id: NecromancyServantHesitate
  branches:
  - tasks:
    - !type:HTNPrimitiveTask
      operator: !type:WaitOperator
        key: HesitateTime

# Перевірка команд від майстра
- type: htnCompound
  id: NecromancyServantCheckCommand
  branches:
  - preconditions:
    - !type:KeyExistsPrecondition
      key: CurrentCommand
    - !type:KeyExistsPrecondition
      key: NecromancyMaster
    tasks:
    - !type:HTNCompoundTask
      task: NecromancyServantExecuteCommand

# Виконання команд
- type: htnCompound
  id: NecromancyServantExecuteCommand
  branches:
  # Команда: Залишатися на місці
  - preconditions:
    - !type:KeyExistsPrecondition
      key: CurrentCommand
      value: Stay
    tasks:
    - !type:HTNPrimitiveTask
      operator: !type:WaitOperator
        key: StayTime
  # Команда: Охороняти позицію
  - preconditions:
    - !type:KeyExistsPrecondition
      key: CurrentCommand
      value: Guard
    - !type:KeyExistsPrecondition
      key: NearbyHostiles
    tasks:
    - !type:HTNPrimitiveTask
      operator: !type:MeleeOperator
        targetKey: NearbyHostiles
  # Команда: Атакувати
  - preconditions:
    - !type:KeyExistsPrecondition
      key: CurrentCommand
      value: Attack
    - !type:KeyExistsPrecondition
      key: NearbyHostiles
    tasks:
    - !type:HTNPrimitiveTask
      operator: !type:MoveToOperator
        pathfindKey: NearbyHostiles
        removeKeyOnFinish: false
    - !type:HTNPrimitiveTask
      operator: !type:MeleeOperator
        targetKey: NearbyHostiles

# Слідування за майстром
- type: htnCompound
  id: NecromancyServantFollowMaster
  branches:
  - preconditions:
    - !type:KeyExistsPrecondition
      key: NecromancyMaster
    - !type:TargetInRangePrecondition
      targetKey: NecromancyMaster
      rangeKey: FollowRange
      invert: true
    tasks:
    - !type:HTNPrimitiveTask
      operator: !type:MoveToOperator
        pathfindKey: NecromancyMaster
        removeKeyOnFinish: false
        requiresInRangeUnobstructed: true

# Захист майстра
- type: htnCompound
  id: NecromancyServantDefendMaster
  branches:
  - preconditions:
    - !type:KeyExistsPrecondition
      key: NecromancyMaster
    - !type:KeyExistsPrecondition
      key: NearbyHostiles
    - !type:TargetInRangePrecondition
      targetKey: NecromancyMaster
      rangeKey: DefendRange
    tasks:
    - !type:HTNPrimitiveTask
      operator: !type:MeleeOperator
        targetKey: NearbyHostiles

# Атака ворогів
- type: htnCompound
  id: NecromancyServantAttackEnemies
  branches:
  - preconditions:
    - !type:KeyExistsPrecondition
      key: NearbyHostiles
    - !type:TargetInRangePrecondition
      targetKey: NearbyHostiles
      rangeKey: AttackRange
    tasks:
    - !type:HTNPrimitiveTask
      operator: !type:MeleeOperator
        targetKey: NearbyHostiles

# Патрулювання
- type: htnCompound
  id: NecromancyServantPatrol
  branches:
  - preconditions:
    - !type:KeyExistsPrecondition
      key: CurrentCommand
      value: Patrol
    - !type:KeyExistsPrecondition
      key: PatrolPoint
    tasks:
    - !type:HTNPrimitiveTask
      operator: !type:MoveToOperator
        pathfindKey: PatrolPoint
        removeKeyOnFinish: true
    - !type:HTNPrimitiveTask
      operator: !type:WaitOperator
        key: PatrolWaitTime

# Простій стан
- type: htnCompound
  id: NecromancyServantIdle
  branches:
  - tasks:
    - !type:HTNPrimitiveTask
      operator: !type:WaitOperator
        key: IdleTime
