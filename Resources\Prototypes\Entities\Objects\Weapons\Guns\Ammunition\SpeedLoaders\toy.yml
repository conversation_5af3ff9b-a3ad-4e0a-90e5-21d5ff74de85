- type: entity
  id: BaseSpeedLoaderCap
  name: "зарядки для іграшкового пістолета"
  parent: BaseItem
  abstract: true
  components:
  - type: Tag
    tags:
      - SpeedLoaderCap
  - type: SpeedLoader
  - type: BallisticAmmoProvider
    whitelist:
      tags:
        - CartridgeCap
    capacity: 6
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-6
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 7
    zeroVisible: false
  - type: Appearance
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
        ents: []

- type: entity
  id: SpeedLoaderCap
  name: "зарядки для іграшкового пістолета"
  parent: BaseSpeedLoaderCap
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeCap
  - type: Sprite
    sprite: Objects/Fun/caps.rsi
