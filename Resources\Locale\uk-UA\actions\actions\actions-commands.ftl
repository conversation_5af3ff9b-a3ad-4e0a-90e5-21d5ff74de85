﻿## Actions Commands loc

## Upgradeaction command loc
upgradeaction-command-need-one-argument = upgradeaction має принаймні один аргумент - сутність дії uid. Другим необов'язковим аргументом є вказаний рівень.
upgradeaction-command-max-two-arguments = upgradeaction має максимум два аргументи: uid сутності дії та (необов'язковий) рівень, який потрібно встановити.
upgradeaction-command-second-argument-not-number = другим аргументом upgradeaction може бути лише число.
upgradeaction-command-less-than-required-level = upgradeaction не може приймати значення 0 або нижче.
upgradeaction-command-incorrect-entityuid-format = Для оновлення ви повинні використовувати правильний формат entityuid.
upgradeaction-command-entity-does-not-exist = Ця сутність не існує, для оновлення потрібна дійсна сутність.
upgradeaction-command-entity-is-not-action = Ця сутність не має компонента оновлення дії, тому ця дія не може бути вирівняна.
upgradeaction-command-cannot-level-up = Дія не може бути нівельована.
upgradeaction-command-description = Підвищує дію на один рівень або до вказаного рівня, якщо це можливо.
