- type: entity
  id: ActionFireball
  name: "Вогня<PERSON> Куля"
  description: "Вистрілює вибуховою вогняною кулею в напрямку клацнутої локації."
  categories: [ HideSpawnMenu ]
  components:
  - type: Magic
    requiresSpeech: true # Goobstation
    school: Evocation # Goobstation
  - type: Sprite # Goobstation - for apprentices
    sprite: _Goobstation/Wizard/actions.rsi
    state: fireball
  - type: EntityWorldTargetAction # Goob edit
    useDelay: 6 # Goob edit
    checkCanInteract: false # Goobstation
    itemIconStyle: BigAction
    checkCanAccess: false
    raiseOnUser: true
    range: 60
    sound: !type:SoundPathSpecifier
      path: /Audio/Magic/fireball.ogg
    icon:
      sprite: _Goobstation/Wizard/actions.rsi # Goob edit
      state: fireball # Goob edit
    event: !type:ProjectileSpellEvent
      prototype: ProjectileFireball
      speech: action-speech-spell-fireball
  - type: ActionUpgrade
    effectedLevels:
      2: ActionFireballII
      3: ActionFireballIII

- type: entity
  id: ActionFireballII
  parent: ActionFireball
  name: "Вогняна Куля II"
  description: "Вистрілює вогняною кулею, але швидше!"
  categories: [ HideSpawnMenu ]
  components:
  - type: EntityWorldTargetAction # Goob edit
    useDelay: 4 # Goob edit
    checkCanInteract: false # Goobstation
    renewCharges: true
    itemIconStyle: BigAction
    checkCanAccess: false
    raiseOnUser: true
    range: 60
    sound: !type:SoundPathSpecifier
      path: /Audio/Magic/fireball.ogg
    icon:
      sprite: _Goobstation/Wizard/actions.rsi # Goob edit
      state: fireball # Goob edit
    event: !type:ProjectileSpellEvent
      prototype: ProjectileFireball
      speech: action-speech-spell-fireball

- type: entity
  id: ActionFireballIII
  parent: ActionFireball
  name: "Вогняна куля III"
  description: "Найшвидша вогняна куля на заході!"
  categories: [ HideSpawnMenu ]
  components:
    - type: EntityWorldTargetAction # Goob edit
      useDelay: 2 # Goob edit
      checkCanInteract: false # Goobstation
      renewCharges: true
      itemIconStyle: BigAction
      checkCanAccess: false
      raiseOnUser: true
      range: 60
      sound: !type:SoundPathSpecifier
        path: /Audio/Magic/fireball.ogg
      icon:
        sprite: _Goobstation/Wizard/actions.rsi # Goob edit
        state: fireball # Goob edit
      event: !type:ProjectileSpellEvent
        prototype: ProjectileFireball
        speech: action-speech-spell-fireball
