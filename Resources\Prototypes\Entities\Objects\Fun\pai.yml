# Placeholder PAIs, aka semi-automatic ghost roles

- type: entity
  parent: BaseItem
  id: PersonalAI
  name: "персональний ШІ-помічник"
  description: "Ваш електронний друг, з яким весело проводити час!"
  components:
  - type: Renamable
  - type: Instrument
    allowPercussion: false
    handheld: false
    bank: 1
    program: 2
  - type: UserInterface
    interfaces:
      enum.InstrumentUiKey.Key:
        type: InstrumentBoundUserInterface
        requireInputValidation: false
      enum.StationMapUiKey.Key:
        type: StationMapBoundUserInterface
        requireInputValidation: false
      enum.SharedRenamableInterfaceKey.Key:
        type: RenamableBoundUserInterface
  - type: Sprite
    sprite: Objects/Fun/pai.rsi
    layers:
    - state: pai-base
    - state: pai-off-overlay
      shader: unshaded
      map: ["screen"]
  - type: Input
    context: "human"
  - type: PAI
  - type: BlockMovement
  - type: ToggleableGhostRole
    examineTextMindPresent: pai-system-pai-installed
    examineTextMindSearching: pai-system-still-searching
    examineTextNoMind: pai-system-off
    beginSearchingText: pai-system-searching
    roleName: pai-system-role-name
    roleDescription: pai-system-role-description
    roleRules: ghost-role-information-familiar-rules
    mindRoles:
    - MindRoleGhostRoleFamiliar
    wipeVerbText: pai-system-wipe-device-verb-text
    wipeVerbPopup: pai-system-wiped-device
    stopSearchVerbText: pai-system-stop-searching-verb-text
    stopSearchVerbPopup: pai-system-stopped-searching
  - type: Examiner
  - type: IntrinsicRadioReceiver
  - type: IntrinsicRadioTransmitter
    channels:
    - Binary
  - type: ActiveRadio
    channels:
    - Binary
    - Common
  - type: DoAfter
  - type: Actions
  - type: TypingIndicator
    proto: robot
  - type: Speech
    speechVerb: Robotic
    speechSounds: Pai
    # This has to be installed because otherwise they're not "alive",
    #  so they can ghost and come back.
    # Note that the personal AI never "dies".
  - type: MobState
    allowedStates:
      - Alive
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ToggleableGhostRoleVisuals.Status:
        screen:
          Off: { state: pai-off-overlay }
          Searching: { state: pai-searching-overlay }
          On: { state: pai-on-overlay }
  - type: StationMap
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    - SolCommon
    - NovuNederic
    - Tradeband
    - Freespeak
    - Elyran
    - RobotTalk
    understands:
    - TauCetiBasic
    - SolCommon
    - NovuNederic
    - Tradeband
    - Freespeak
    - Elyran
    - RobotTalk
    - Sign # It's intentional that they don't "Speak" sign language.
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 3

- type: entity
  parent: PersonalAI
  id: SyndicatePersonalAI
  name: "синдикат персональний ШІ-помічник"
  description: "Твій друг із Синдикату, з яким весело проводити час!"
  components:
  - type: Sprite
    sprite: Objects/Fun/pai.rsi
    layers:
    - state: pai-base
    - state: syndicate-pai-off-overlay
      shader: unshaded
      map: ["screen"]
  - type: ToggleableGhostRole
    roleName: pai-system-role-name-syndicate
    roleDescription: pai-system-role-description-syndicate
    roleRules: ghost-role-information-familiar-rules
    mindRoles:
    - MindRoleGhostRoleFamiliar
  - type: IntrinsicRadioTransmitter
    channels:
    - Syndicate
  - type: ActiveRadio
    channels:
    - Syndicate
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ToggleableGhostRoleVisuals.Status:
        screen:
          Off: { state: syndicate-pai-off-overlay }
          Searching: { state: syndicate-pai-searching-overlay }
          On: { state: syndicate-pai-on-overlay }

- type: entity
  parent: PersonalAI
  id: PotatoAI
  name: "штучний інтелект картоплі"
  description: "Картопля є ідеальним джерелом енергії для цього чіпсу."
  components:
  - type: Sprite
    sprite: Objects/Fun/pai.rsi
    layers:
    - state: potato-base
    - state: potato-off-overlay
      shader: unshaded
      map: ["screen"]
  - type: ToggleableGhostRole
    roleName: pai-system-role-name-potato
    roleDescription: pai-system-role-description-potato
    roleRules: ghost-role-information-familiar-rules
    mindRoles:
    - MindRoleGhostRoleFamiliar
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ToggleableGhostRoleVisuals.Status:
        screen:
          Off: { state: potato-off-overlay }
          Searching: { state: potato-searching-overlay }
          On: { state: potato-on-overlay }
  - type: Construction
    graph: PotatoAI
    node: potatoai

- type: entity
  id: ActionPAIPlayMidi
  name: "Відтворення MIDI"
  description: "Відкрийте свій портативний MIDI-інтерфейс, щоб заспокоїти власника."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    checkCanInteract: false
    checkConsciousness: false
    icon: Interface/Actions/pai-midi.png
    event: !type:OpenUiActionEvent
      key: enum.InstrumentUiKey.Key

- type: entity
  id: ActionPAIOpenMap
  name: "Відкрити мапу"
  description: "Відкрийте інтерфейс карти та допоможіть власнику."
  categories: [ HideSpawnMenu ]
  components:
    - type: InstantAction
      checkCanInteract: false
      checkConsciousness: false
      icon: { sprite: Interface/Actions/pai-map.rsi, state: icon }
      event: !type:OpenUiActionEvent
        key: enum.StationMapUiKey.Key
