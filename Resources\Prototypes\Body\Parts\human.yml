# TODO: Add descriptions (many)
# TODO BODY: Part damage
- type: entity
  id: PartHuman
  parent: [BaseItem, BasePart]
  name: "частина тіла людини"
  abstract: true
  components:
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 3
      - ReagentId: Blood
        Quantity: 10

- type: entity
  id: TorsoHuman
  name: "людський торс"
  parent: [PartHuman, BaseTorso]
  components:
  - type: Sprite
    sprite: Mobs/Species/Human/parts.rsi
    state: "torso_m"
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 10
      - ReagentId: Blood
        Quantity: 20

- type: entity
  id: HeadHuman
  name: "людська голова"
  parent: [PartHuman, BaseHead]
  components:
  - type: Sprite
    sprite: Mobs/Species/Human/parts.rsi
    state: "head_m"
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 5
      - ReagentId: Blood
        Quantity: 10

- type: entity
  id: LeftArmHuman
  name: "ліва рука людини"
  parent: [PartHuman, BaseLeftArm]
  components:
  - type: Sprite
    sprite: Mobs/Species/Human/parts.rsi
    state: "l_arm"

- type: entity
  id: RightArmHuman
  name: "права рука людини"
  parent: [PartHuman, BaseRightArm]
  components:
  - type: Sprite
    sprite: Mobs/Species/Human/parts.rsi
    state: "r_arm"

- type: entity
  id: LeftHandHuman
  name: "ліва долоня людини"
  parent: [PartHuman, BaseLeftHand]
  components:
  - type: Sprite
    sprite: Mobs/Species/Human/parts.rsi
    state: "l_hand"

- type: entity
  id: RightHandHuman
  name: "права долоня людини"
  parent: [PartHuman, BaseRightHand]
  components:
  - type: Sprite
    sprite: Mobs/Species/Human/parts.rsi
    state: "r_hand"

- type: entity
  id: LeftLegHuman
  name: "ліва нога людини"
  parent: [PartHuman, BaseLeftLeg]
  components:
  - type: Sprite
    sprite: Mobs/Species/Human/parts.rsi
    state: "l_leg"

- type: entity
  id: RightLegHuman
  name: "права нога людини"
  parent: [PartHuman, BaseRightLeg]
  components:
  - type: Sprite
    sprite: Mobs/Species/Human/parts.rsi
    state: "r_leg"

- type: entity
  id: LeftFootHuman
  name: "ліва стопа людини"
  parent: [PartHuman, BaseLeftFoot]
  components:
  - type: Sprite
    sprite: Mobs/Species/Human/parts.rsi
    state: "l_foot"

- type: entity
  id: RightFootHuman
  name: "права стопа людини"
  parent: [PartHuman, BaseRightFoot]
  components:
  - type: Sprite
    sprite: Mobs/Species/Human/parts.rsi
    state: "r_foot"
