- type: entity
  name: "основний шлам"
  id: BaseMobAdultSlimes
  parent: [ SimpleMobBase, MobCombat ]
  abstract: true
  description: "Це так схоже на желе. Цікаво, який він на смак?"
  components:
  - type: VentCrawler # goobstation - Ventcrawl
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/slimes.rsi
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: blue_adult_slime
  - type: Carriable
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.30
        density: 80
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: MobThresholds
    thresholds:
      0: Alive
      120: Dead
  - type: MovementSpeedModifier
    baseWalkSpeed: 2
    baseSprintSpeed: 4
  - type: FootstepModifier
    footstepSoundCollection:
      path: /Audio/Effects/Footsteps/slime1.ogg
      params:
        volume: 3
  - type: Tag
    tags:
    - FootstepSound
    - DoorBumpOpener
  - type: Butcherable
    butcheringType: Knife
    spawned:
    - id: FoodMeatSlime
      amount: 2
  - type: Respirator
    damage:
      types:
        Asphyxiation: 0.2
    damageRecovery:
      types:
        Asphyxiation: -1.0
    maxSaturation: 15
  - type: Damageable
    damageContainer: Biological
    damageModifierSet: Slime
  - type: Bloodstream
    bloodReagent: Slime
    bloodlossDamage:
      types:
        Bloodloss:
          0.5
    bloodlossHealDamage:
      types:
        Bloodloss:
          -0.25
  - type: Barotrauma
    damage:
      types:
        Blunt: 0.45
  - type: Reactive
    groups:
      Flammable: [ Touch ]
      Extinguish: [ Touch ]
    reactions:
    - reagents: [ Water, SpaceCleaner ]
      methods: [ Touch ]
      effects:
      - !type:WashCreamPieReaction
    - reagents: [ Water ]
      methods: [ Touch ]
      effects:
      - !type:HealthChange
        scaleByQuantity: true
        damage:
          types:
            Heat: 3
      - !type:PopupMessage
        type: Local
        messages: [ "slime-hurt-by-water-popup" ]
        probability: 0.25
  - type: Body
    prototype: Slimes
    requiredLegs: 1
  - type: MeleeWeapon
    altDisarm: false
    soundHit:
        path: /Audio/Weapons/punch3.ogg
    angle: 0
    animation: WeaponArcPunch
    damage:
      types:
        Blunt: 6
        Structural: 4
        Caustic: 4
  - type: InteractionPopup
    successChance: 0.5
    interactSuccessString: petting-success-slimes
    interactFailureString: petting-failure-generic
  - type: Speech
    speechVerb: Slime
    speechSounds: Slime
  - type: TypingIndicator
    proto: slime
  - type: SurgeryTarget
  - type: UserInterface
    interfaces:
      enum.SurgeryUIKey.Key:
        type: SurgeryBui
  - type: Fauna # Lavaland Change

- type: entity
  name: "основний шлам"
  id: MobAdultSlimes
  parent: BaseMobAdultSlimes
  abstract: true
  description: "Це так схоже на желе. Цікаво, який він на смак?"
  components:
  - type: LanguageKnowledge
    speaks:
    - Bubblish
    understands:
    - Bubblish
  - type: GhostTakeoverAvailable
  - type: GhostRole
    makeSentient: true
    name: ghost-role-information-slimes-name
    description: ghost-role-information-slimes-description
    rules: deltav-ghost-role-information-softantag-rules
  - type: Speech
    speechVerb: Slime
    speechSounds: Slime
    allowedEmotes: ['Squish']
  - type: TypingIndicator
    proto: slime
  - type: NpcFactionMember
    factions:
    - SimpleNeutral
  - type: HTN
    rootTask:
      task: SimpleHostileCompound

- type: entity
  name: "герас"
  description: "Герас із слизу - назва іронічна, чи не так?"
  id: MobSlimesGeras
  parent: BaseMobAdultSlimes
  categories: [ HideSpawnMenu ]
  components:
  # they portable...
  - type: MovementSpeedModifier
    baseWalkSpeed: 3
    baseSprintSpeed: 5 # +.5 from normal movement speed
  - type: MobThresholds
    thresholds:
      0: Alive
      80: Dead # weak af tho
  - type: NpcFactionMember
    factions:
    - NanoTrasen
  - type: MultiHandedItem
  - type: Item
    size: Huge
  - type: Sprite
    color: "#FFFFFF55"
  - type: MeleeWeapon
    attackRate: 2
    damage:
      types:
        Blunt: 4
  - type: DamageStateVisuals
    states:
      Alive:
        Base: blue_adult_slime
      Dead:
        Base: blue_adult_slime_dead

- type: entity
  name: "синій слиз"
  id: MobAdultSlimesBlue
  parent: MobAdultSlimes
  components:
  - type: DamageStateVisuals
    states:
      Alive:
        Base: blue_adult_slime
      Dead:
        Base: blue_adult_slime_dead

- type: entity
  name: "синій слиз"
  parent: MobAdultSlimesBlue
  id: MobAdultSlimesBlueAngry
  suffix: Angry
  components:
    - type: NpcFactionMember
      factions:
        - SimpleHostile
    - type: GhostRole
      description: ghost-role-information-angry-slimes-description
      rules: ghost-role-information-angry-slimes-rules
      mindRoles:
      - MindRoleGhostRoleTeamAntagonist
      raffle:
        settings: short

- type: entity
  name: "зелений слиз"
  parent: MobAdultSlimes
  id: MobAdultSlimesGreen
  components:
    - type: Sprite
      layers:
      - map: [ "enum.DamageStateVisualLayers.Base" ]
        state: green_adult_slime
    - type: DamageStateVisuals
      states:
        Alive:
          Base: green_adult_slime
        Dead:
          Base: green_adult_slime_dead
    - type: MeleeWeapon
      damage:
        types:
          Blunt: 6
          Structural: 4
          Caustic: 1
          Poison: 4

- type: entity
  name: "зелений слиз"
  parent: MobAdultSlimesGreen
  id: MobAdultSlimesGreenAngry
  suffix: Angry
  components:
    - type: NpcFactionMember
      factions:
        - SimpleHostile
    - type: GhostRole
      description: ghost-role-information-angry-slimes-description
      rules: ghost-role-information-angry-slimes-rules
      mindRoles:
      - MindRoleGhostRoleTeamAntagonist
      raffle:
        settings: short

- type: entity
  name: "жовтий слиз"
  parent: MobAdultSlimes
  id: MobAdultSlimesYellow
  components:
    - type: Sprite
      layers:
      - map: [ "enum.DamageStateVisualLayers.Base" ]
        state: yellow_adult_slime
    - type: DamageStateVisuals
      states:
        Alive:
          Base: yellow_adult_slime
        Dead:
          Base: yellow_adult_slime_dead
    - type: MeleeWeapon
      damage:
        types:
          Blunt: 6
          Structural: 4
          Caustic: 1
          Cellular: 3

- type: entity
  name: "жовтий слиз"
  parent: MobAdultSlimesYellow
  id: MobAdultSlimesYellowAngry
  suffix: Angry
  components:
    - type: NpcFactionMember
      factions:
        - SimpleHostile
    - type: GhostRole
      description: ghost-role-information-angry-slimes-description
      rules: ghost-role-information-angry-slimes-rules
      mindRoles:
      - MindRoleGhostRoleTeamAntagonist
      raffle:
        settings: short
