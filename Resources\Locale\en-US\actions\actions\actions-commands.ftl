﻿## Actions Commands loc

## Upgradeaction command loc
upgradeaction-command-need-one-argument = upgradeaction needs at least one argument, the action entity uid. The second optional argument is a specified level.
upgradeaction-command-max-two-arguments = upgradeaction has a maximum of two arguments, the action entity uid and the (optional) level to set.
upgradeaction-command-second-argument-not-number = upgradeaction's second argument can only be a number.
upgradeaction-command-less-than-required-level = upgradeaction cannot accept a level of 0 or lower.
upgradeaction-command-incorrect-entityuid-format = You must use a valid entityuid format for upgradeaction.
upgradeaction-command-entity-does-not-exist = This entity does not exist, a valid entity is required for upgradeaction.
upgradeaction-command-entity-is-not-action = This entity doesn't have the action upgrade component, so this action cannot be leveled.
upgradeaction-command-cannot-level-up = The action cannot be leveled up.
upgradeaction-command-description = Upgrades an action by one level, or to the specified level, if applicable.
