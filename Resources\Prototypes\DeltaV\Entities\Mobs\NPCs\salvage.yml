- type: entity
  name: "<PERSON><PERSON><PERSON><PERSON><PERSON>на Синдикату"
  parent: BaseMobHuman
  id: MobRadioGuard
  description: "Синдикат промив їм мізки і загіпнотизував до ідеальної лояльності. Вони віддадуть своє життя, щоб виконати своє завдання."
  components:
    - type: NpcFactionMember
      factions:
        - Syndicate
    - type: MobThresholds
      thresholds:
        0: Alive
        100: Dead #Tweak for "Acidify on Crit"
    - type: MindContainer
      showExamineInfo: False
    - type: Loadout
      prototypes:
        - RadioGuardGear
    - type: InputMover
    - type: MobMover
    - type: HTN
      rootTask:
        task: SimpleHumanoidHostileCompound
    - type: Respirator #Mobs can't turn on internals by themselves, so we have to simulate them having it on
      damage:
        types:
          Asphyxiation: 0
      damageRecovery:
        types:
          Asphyxiation: -1.0
    - type: Gun #Mo<PERSON> currently cannot bolt a gun, so we have to simulate them firing it
      fireRate: 2
      soundGunshot: /Audio/Weapons/Guns/Gunshots/pistol.ogg
      useKey: false
      selectedMode: SemiAuto
      availableModes:
        - SemiAuto
    - type: CombatMode
    - type: BasicEntityAmmoProvider
      proto: CartridgePistol
      capacity: 10
      count: 10
    - type: RechargeBasicEntityAmmo
      rechargeCooldown: 1
      rechargeSound:
        path: /Audio/Weapons/Guns/MagIn/bullet_insert.ogg
    - type: AutoImplant
      implants:
      - DeathAcidifierImplant
    - type: Sprite
      layers:
      - map: [ "enum.HumanoidVisualLayers.Chest" ]
      - map: [ "enum.HumanoidVisualLayers.Head" ]
      - map: [ "enum.HumanoidVisualLayers.Snout" ]
      - map: [ "enum.HumanoidVisualLayers.Eyes" ]
      - map: [ "enum.HumanoidVisualLayers.RArm" ]
      - map: [ "enum.HumanoidVisualLayers.LArm" ]
      - map: [ "enum.HumanoidVisualLayers.RLeg" ]
      - map: [ "enum.HumanoidVisualLayers.LLeg" ]
#     - shader: StencilClear
#      sprite: Mobs/Species/Human/parts.rsi
#      state: l_leg
      - shader: StencilMask
        map: ["enum.HumanoidVisualLayers.StencilMask"]
        sprite: Mobs/Customization/masking_helpers.rsi
        state: unisex_full
        visible: false
      - map: ["jumpsuit"]
      - map: ["enum.HumanoidVisualLayers.LFoot"]
      - map: ["enum.HumanoidVisualLayers.RFoot"]
      - map: ["enum.HumanoidVisualLayers.LHand"]
      - map: ["enum.HumanoidVisualLayers.RHand"]
      - map: [ "gloves" ]
      - map: [ "shoes" ]
      - map: [ "ears" ]
      - map: [ "eyes" ]
      - map: [ "belt" ]
      - map: [ "id" ]
      - map: [ "neck" ]
      - map: [ "back" ]
      - map: [ "enum.HumanoidVisualLayers.FacialHair" ]
      - map: [ "enum.HumanoidVisualLayers.Hair" ]
      - map: [ "enum.HumanoidVisualLayers.HeadSide" ]
      - map: [ "enum.HumanoidVisualLayers.HeadTop" ]
      - map: [ "enum.HumanoidVisualLayers.Tail" ]
      - map: [ "mask" ]
      - map: [ "head" ]
      - map: ["enum.HumanoidVisualLayers.Handcuffs"]
        color: "#ffffff"
        sprite: Objects/Misc/handcuffs.rsi
        state: body-overlay-2
        visible: false
      - map: [ "clownedon" ] # Dynamically generated
        sprite: "Effects/creampie.rsi"
        state: "creampie_human"
        visible: false
      - sprite: Objects/Weapons/Guns/Pistols/viper.rsi
        state: inhand-left
