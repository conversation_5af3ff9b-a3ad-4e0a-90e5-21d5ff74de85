- type: entity
  parent: PoweredlightExterior
  id: PoweredLightBlueInterior
  suffix: Blue Exterior, Delta V
  description: "Світильник. Споживає енергію і виробляє світло, якщо оснащений світловою трубкою."
  components:
  - type: PoweredLight
    hasLampOnSpawn: BlueLightTube
    damage:
      types:
        Heat: 0.2

- type: entity
  parent: AlwaysPoweredWallLight
  id: AlwaysPoweredLightBlueInterior
  suffix: Always Powered, Blue Exterior, Delta V
  description: "Світильник. Споживає енергію і виробляє світло, якщо оснащений світловою трубкою."
  components:
  - type: PointLight
    radius: 12
    energy: 3
    softness: 0.5
    color: "#B4FCF0"

- type: entity
  parent: PoweredSmallLight
  id: PoweredSmallLightMaintenance
  suffix: Maintenance
  description: "Освітлювальний прилад. Споживає енергію і виробляє світло, коли оснащений лампочкою."
  components:
  - type: PoweredLight
    hasLampOnSpawn: LightBulbMaintenance
    damage:
      types:
        Heat: 0.2

- type: entity
  parent: SmallLight
  id: AlwaysPoweredSmallLightMaintenance
  suffix: Always Powered, Maintenance
  description: "Світильник. Споживає енергію і виробляє світло, якщо оснащений світловою трубкою."
  components:
  - type: PointLight
    radius: 1.5
    energy: 0.7
    softness: 1.1
    color: "#FFD1A3"

#Colored lights

- type: entity
  id: PoweredLightColoredRed
  description: "Світильник. Споживає енергію і виробляє світло, якщо оснащений світловою трубкою."
  suffix: Soft Red #Delta V - Changed to differentiate from upstream lights.
  parent: Poweredlight
  components:
  - type: PoweredLight
    hasLampOnSpawn: ColoredLightTubeRed
    damage:
      types:
        Heat: 0.2

- type: entity
  id: AlwaysPoweredLightColoredRed
  description: "Світильник. Споживає енергію і виробляє світло, якщо оснащений світловою трубкою."
  suffix: Always Powered, Red
  parent: AlwaysPoweredWallLight
  components:
  - type: PointLight
    radius: 10
    energy: 0.9
    softness: 0.5
    color: "#FF6666"

- type: entity
  id: PoweredLightColoredFrostyBlue
  description: "Світильник. Споживає енергію і виробляє світло, якщо оснащений світловою трубкою."
  suffix: Frosty
  parent: Poweredlight
  components:
  - type: PoweredLight
    hasLampOnSpawn: ColoredLightTubeFrostyBlue
    damage:
      types:
        Heat: 0.2

- type: entity
  id: AlwaysPoweredLightColoredFrostyBlue
  description: "Світильник. Споживає енергію і виробляє світло, якщо оснащений світловою трубкою."
  suffix: Always Powered, Frosty
  parent: AlwaysPoweredWallLight
  components:
  - type: PointLight
    radius: 10
    energy: 0.8
    softness: 1
    color: "#00FFFF"

- type: entity
  id: PoweredLightColoredBlack
  description: "Світильник. Споживає енергію і виробляє світло, якщо оснащений світловою трубкою."
  suffix: Black
  parent: Poweredlight
  components:
  - type: PoweredLight
    hasLampOnSpawn: ColoredLightTubeBlackLight
    damage:
      types:
        Heat: 0.2

- type: entity
  id: AlwaysPoweredLightColoredBlack
  description: "Світильник. Споживає енергію і виробляє світло, якщо оснащений світловою трубкою."
  suffix: Always Powered, Black
  parent: AlwaysPoweredWallLight
  components:
  - type: PointLight
    radius: 10
    energy: 1.1
    softness: 1
    color: "#5D0CED"

- type: entity
  id: PoweredLightPostSmallRed
  name: "пост-світло"
  description: "Світильник. Споживає енергію і виробляє світло, якщо оснащений світловою трубкою."
  suffix: Red
  parent: PoweredLightPostSmallEmpty
  components:
  - type: Sprite
    state: base
  - type: PoweredLight
    hasLampOnSpawn: ColoredLightTubeRed
    damage:
      types:
        Heat: 2
  - type: StaticPrice #DynamicPrice
    price: 25

- type: entity
  id: AlwaysPoweredLightPostSmallRed
  name: "пост-світло"
  description: "Світильник. Споживає енергію і виробляє світло, якщо оснащений світловою трубкою."
  suffix: Always Powered, Red
  parent: LightPostSmall
  components:
  - type: Sprite
    state: base
  - type: PointLight
    radius: 10
    energy: 0.9
    softness: 1
    offset: "0, -0.5"
    color: "#FF6666"

- type: entity
  parent: PoweredSmallLight
  id: PoweredSmallLightMaintenanceRed
  suffix: Maintenance, Red
  description: "Освітлювальний прилад. Споживає енергію і виробляє світло, коли оснащений лампочкою."
  components:
  - type: PoweredLight
    hasLampOnSpawn: LightBulbMaintenanceRed
    damage:
      types:
        Heat: 0.2

- type: entity
  parent: SmallLight
  id: AlwaysPoweredSmallLightMaintenanceRed
  suffix: Always Powered, Maintenance, Red
  description: "Світильник. Споживає енергію і виробляє світло, якщо оснащений світловою трубкою."
  components:
  - type: PointLight
    radius: 1.5
    energy: 1.1
    softness: 1.1
    color: "#FF6666"
