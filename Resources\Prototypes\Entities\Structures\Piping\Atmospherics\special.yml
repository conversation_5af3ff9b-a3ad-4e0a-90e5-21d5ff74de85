# Devices which are not portable but don't link up to anything
- type: entity
  id: AtmosDeviceFanTiny
  name: "маленький вентилятор"
  description: "Крихітний вентилятор, що випускає тоненький потік повітря."
  placement:
    mode: SnapgridCenter
  components:
  - type: Transform
    anchored: true
  - type: Physics
    bodyType: Static
  - type: Sprite
    sprite: Structures/Piping/Atmospherics/tinyfan.rsi
    state: icon
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
            bounds: "-0.5,-0.5,0.5,0.5"
  - type: Airtight
    noAirWhenFullyAirBlocked: false
  - type: Clickable
  - type: Tag
    tags:
      - SpreaderIgnore

- type: entity
  id: AtmosDeviceFanDirectional
  name: "спрямований вентилятор"
  description: "Тонкий вентилятор, що зупиняє рух газів через нього."
  placement:
    mode: SnapgridCenter
  components:
  - type: Transform
    anchored: true
  - type: Physics
    bodyType: Static
  - type: Sprite
    sprite: Structures/Piping/Atmospherics/directionalfan.rsi
    state: icon
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
            bounds: "-0.48,-0.48,0.48,-0.40"
  - type: Airtight
    noAirWhenFullyAirBlocked: false
    airBlockedDirection:
      - South
  - type: Clickable
  - type: Tag
    tags:
      - SpreaderIgnore
