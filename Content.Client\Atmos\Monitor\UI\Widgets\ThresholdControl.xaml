<BoxContainer xmlns="https://spacestation14.io"
         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         Orientation="Vertical" Margin="0 0 0 4">
    <Collapsible>
        <CollapsibleHeading Name="CName" />
        <CollapsibleBody Margin="20 0 0 0">
            <BoxContainer Orientation="Vertical">
                <BoxContainer Orientation="Horizontal">
                    <CheckBox Name="CEnabled" Text="{Loc 'Enabled'}" />
                </BoxContainer>
                <!-- Upper row: Danger bounds -->
                <BoxContainer Name="CDangerBounds" Orientation="Horizontal" Margin="0 0 0 2"/>
                <!-- Lower row: Warning bounds -->
                <BoxContainer Name="CWarningBounds" Orientation="Horizontal" Margin="0 6 0 2"/>
            </BoxContainer>
        </CollapsibleBody>
    </Collapsible>
</BoxContainer>
