# Uses the chessboard and generic pieces
- type: entity
  parent: BaseBoardEntity
  id: CheckerBoard
  name: "шашківниця"
  description: "Шахова дошка. Шматки включені!"
  components:
  - type: Sprite
    sprite: Objects/Fun/Tabletop/chessboard.rsi
    state: chessboard
  - type: TabletopGame
    boardName: tabletop-checkers-board-name
    size: 338, 274
    setup:
      !type:TabletopCheckerSetup
      boardPrototype: &checkerboard CheckerBoardTabletop
      prototypePieceWhite: &pieceWhite CheckerPieceWhite
      prototypeCrownWhite: &crownWhite CheckerCrownWhite
      prototypePieceBlack: &pieceBlack CheckerPieceBlack
      prototypeCrownBlack: &crownBlack CheckerCrownBlack

# Checkerboard tabletop item (item only visible in tabletop game)
- type: entity
  id: *checkerboard
  name: "шашківниця"
  parent: BaseBoardTabletop
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Fun/Tabletop/chessboard_tabletop.rsi
    state: chessboard_tabletop

# The pieces
- type: entity
  id: *pieceWhite
  name: "біла шашка"
  parent: BaseTabletopPiece
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/checker_pieces.rsi
      state: w_checker_piece

- type: entity
  id: *crownWhite
  name: "біла коронова шашка"
  parent: BaseTabletopPiece
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/checker_pieces.rsi
      state: w_checker_crown

- type: entity
  id: *pieceBlack
  name: "чорна шашка"
  parent: BaseTabletopPiece
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/checker_pieces.rsi
      state: b_checker_piece

- type: entity
  id: *crownBlack
  name: "чорна коронова шашка"
  parent: BaseTabletopPiece
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/checker_pieces.rsi
      state: b_checker_crown
