- type: entity
  save: false
  name: "Уріст МакГарпі"
  parent: BaseMobHuman
  id: MobHarpyBase
  abstract: true
  components:
  - type: Singer
    proto: HarpySinger
  - type: Sprite
    scale: 0.9, 0.9
    layers:
      - map: [ "enum.HumanoidVisualLayers.Chest" ]
      - map: [ "enum.HumanoidVisualLayers.Head" ]
      - map: [ "enum.HumanoidVisualLayers.Snout" ]
      - map: [ "enum.HumanoidVisualLayers.Eyes" ]
      - map: [ "enum.HumanoidVisualLayers.Face" ]
      - map: [ "enum.HumanoidVisualLayers.LArm" ]
      - map: [ "enum.HumanoidVisualLayers.RLeg" ]
      - map: [ "enum.HumanoidVisualLayers.LLeg" ]
      - map: [ "enum.HumanoidVisualLayers.Underwear" ]
      - map: [ "enum.HumanoidVisualLayers.Undershirt" ]
      - map: [ "underpants" ]
      - map: [ "undershirt" ]
      - map: [ "socks" ]
      - map: [ "jumpsuit" ]
      - map: ["enum.HumanoidVisualLayers.LFoot"]
      - map: ["enum.HumanoidVisualLayers.RFoot"]
      - map: ["enum.HumanoidVisualLayers.LHand"]
      - map: ["enum.HumanoidVisualLayers.RHand"]
      - map: [ "enum.HumanoidVisualLayers.Handcuffs" ]
        color: "#ffffff"
        sprite: Objects/Misc/handcuffs.rsi
        state: body-overlay-2
        visible: false
      - map: [ "id" ]
      - map: [ "gloves" ]
      - map: [ "ears" ]
      - map: [ "innerBelt" ]
      - map: [ "innerNeck" ]
      - map: [ "outerClothing" ]
      - map: [ "eyes" ]
      - map: [ "belt" ]
      - map: [ "neck" ]
      - map: [ "back" ]
      - map: [ "enum.HumanoidVisualLayers.FacialHair" ]
      - map: [ "enum.HumanoidVisualLayers.HeadSide" ]
      - map: [ "enum.HumanoidVisualLayers.Tail" ]
      - map: [ "pocket1" ]
      - map: [ "pocket2" ]
      - map: [ "clownedon" ] # Dynamically generated
        sprite: "Effects/creampie.rsi"
        state: "creampie_human"
        visible: false
# Yes, RArm has to be down here
      - map: [ "enum.HumanoidVisualLayers.RArm" ]
      - map: [ "enum.HumanoidVisualLayers.Hair" ]
      - map: [ "enum.HumanoidVisualLayers.HeadTop" ]
      - map: [ "mask" ]
      - map: [ "head" ]
      - map: [ "neck2" ] #PIRATE
      - map: [ "neck1" ] #PIRATE
      - map: [ "head2" ] #PIRATE
      - map: [ "head1" ] #PIRATE
      - map: [ "singingLayer" ]
        sprite: Effects/harpysinger.rsi
        state: singing_music_notes
        visible: false
  - type: HumanoidAppearance
    species: Harpy
  - type: Fixtures
    fixtures: # TODO: This needs a second fixture just for mob collisions.
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.32
        density: 90
        restitution: 0.0
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Body
    prototype: Harpy
  - type: Damageable
    damageModifierSet: Harpy
  - type: MeleeWeapon
    soundHit:
      collection: AlienClaw
    animation: WeaponArcClaw
    damage:
      types:
        Piercing: 5
  - type: Speech
    speechSounds: Harpy
    speechVerb: Harpy
  - type: Vocal
    sounds:
      Male: SoundsHarpy
      Female: SoundsHarpy
      Unsexed: SoundsHarpy
  - type: GenericVisualizer
    visuals:
      enum.HarpyVisualLayers.Singing:
        singingLayer:
          False:  {visible: false}
          True:  {visible: true}
  - type: MovementSpeedModifier
    baseWalkSpeed: 3
    baseSprintSpeed: 5.5
    weightlessAcceleration: 2.5
    weightlessModifier: 1.12
  - type: Inventory
    speciesId: harpy
    templateId: digitigrade
    displacements:
      jumpsuit:
        layer:
          sprite: Mobs/Species/Harpy/displacement.rsi
          state: jumpsuit
          copyToShaderParameters:
            # Value required, provide a dummy. Gets overridden when applied.
            layerKey: dummy
            parameterTexture: displacementMap
            parameterUV: displacementUV
  - type: HarpyVisuals
  - type: UltraVision
  - type: Tag
    tags:
    - CanPilot
    - FootstepSound
    - DoorBumpOpener
    - HarpyEmotes
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    - ValyrianStandard
    understands:
    - TauCetiBasic
    - ValyrianStandard
  - type: StepTriggerImmune
    whitelist:
      types:
      - Shard
      - Landmine
      - Mousetrap
  - type: FootPrints
    leftBarePrint: "footprint-left-bare-lizard"
    rightBarePrint: "footprint-right-bare-lizard" # I was about to complain about this, then I remembered birbs have dinosaur feet. So this is valid.
  - type: CanMoveInAir

- type: entity
  save: false
  categories: [ HideSpawnMenu ]
  name: "Уріст Макбірб"
  parent: MobHumanDummy
  id: MobHarpyDummy
  description: "Манекен Гарпії, призначений для використання в налаштуванні персонажа."
  components:
  - type: HumanoidAppearance
    species: Harpy
  - type: Inventory
    speciesId: harpy
    templateId: digitigrade
    displacements:
      jumpsuit:
        layer:
          sprite: Mobs/Species/Harpy/displacement.rsi
          state: jumpsuit
          copyToShaderParameters:
            # Value required, provide a dummy. Gets overridden when applied.
            layerKey: dummy
            parameterTexture: displacementMap
            parameterUV: displacementUV
  - type: Sprite
    scale: 0.9, 0.9
    layers:
      - map: [ "enum.HumanoidVisualLayers.Chest" ]
      - map: [ "enum.HumanoidVisualLayers.Head" ]
      - map: [ "enum.HumanoidVisualLayers.Snout" ]
      - map: [ "enum.HumanoidVisualLayers.Eyes" ]
      - map: [ "enum.HumanoidVisualLayers.Face" ]
      - map: [ "enum.HumanoidVisualLayers.LArm" ]
      - map: [ "enum.HumanoidVisualLayers.RLeg" ]
      - map: [ "enum.HumanoidVisualLayers.LLeg" ]
      - map: [ "underpants" ]
      - map: [ "undershirt" ]
      - map: [ "socks" ]
      - map: [ "jumpsuit" ]
      - map: ["enum.HumanoidVisualLayers.LFoot"]
      - map: ["enum.HumanoidVisualLayers.RFoot"]
      - map: ["enum.HumanoidVisualLayers.LHand"]
      - map: ["enum.HumanoidVisualLayers.RHand"]
      - map: [ "id" ]
      - map: [ "gloves" ]
      - map: [ "ears" ]
      - map: [ "innerBelt" ]
      - map: [ "innerNeck" ]
      - map: [ "outerClothing" ]
      - map: [ "eyes" ]
      - map: [ "belt" ]
      - map: [ "neck" ]
      - map: [ "back" ]
      - map: [ "enum.HumanoidVisualLayers.FacialHair" ]
      - map: [ "enum.HumanoidVisualLayers.HeadSide" ]
      - map: [ "enum.HumanoidVisualLayers.HeadTop" ]
      - map: [ "enum.HumanoidVisualLayers.Tail" ]
      - map: [ "pocket1" ]
      - map: [ "pocket2" ]
      - map: [ "clownedon" ] # Dynamically generated
        sprite: "Effects/creampie.rsi"
        state: "creampie_human"
        visible: false
# Yes, RArm has to be down here
      - map: [ "enum.HumanoidVisualLayers.RArm" ]
      - map: [ "enum.HumanoidVisualLayers.Hair" ]
      - map: [ "mask" ]
      - map: [ "head" ]
      - map: [ "neck2" ] #PIRATE
      - map: [ "neck1" ] #PIRATE
      - map: [ "head2" ] #PIRATE
      - map: [ "head1" ] #PIRATE
      - map: [ "singingLayer" ]
        sprite: Effects/harpysinger.rsi
        state: singing_music_notes

- type: entity
  id: ActionHarpyPlayMidi
  name: "Відтворення MIDI"
  description: "Співайте від душі! Клацніть правою кнопкою миші на себе, щоб вибрати інструмент."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    checkCanInteract: false
    icon: DeltaV/Interface/Actions/harpy_sing.png
    event: !type:OpenUiActionEvent
      key: enum.InstrumentUiKey.Key

- type: entity
  id: ActionSyrinxChangeVoiceMask
  name: "Назва набору"
  description: "Змініть ім'я, яке чують інші, на інше."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: DeltaV/Interface/Actions/harpy_syrinx.png
    itemIconStyle: BigAction
    event: !type:VoiceMaskSetNameEvent

- type: entity
  id: ActionToggleFlight
  name: "Літати"
  description: "Використовуйте свої крила, щоб літати. Спростуйте звинувачення у нездатності до польоту."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    checkCanInteract: false
    icon: { sprite: Interface/Actions/flight.rsi, state: flight_off }
    iconOn: { sprite : Interface/Actions/flight.rsi, state: flight_on }
    event: !type:ToggleFlightEvent
