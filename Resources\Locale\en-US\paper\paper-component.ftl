
### UI

paper-ui-blank-page-message = This page intentionally left blank

# Shown when paper with words examined details
paper-component-examine-detail-has-words = {CAPITALIZE(THE($paper))} has something written on it.
# Shown when paper with stamps examined
paper-component-examine-detail-stamped-by = {CAPITALIZE(THE($paper))} {CONJUGATE-HAVE($paper)} been stamped by: {$stamps}.

paper-component-action-stamp-paper-other = {CAPITALIZE(THE($user))} stamps {THE($target)} with {THE($stamp)}.
paper-component-action-stamp-paper-self = You stamp {THE($target)} with {THE($stamp)}.

paper-ui-save-button = Save ({$keybind})
