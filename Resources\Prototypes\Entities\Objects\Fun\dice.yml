- type: entity
  abstract: true
  parent: BaseItem
  id: BaseDice
  components:
  - type: Dice
  - type: UseDelay
  - type: Sprite
    sprite: Objects/Fun/dice.rsi
    noRot: true # If their sprites rotate, the number becomes even more illegible than usual.
  - type: Tag
    tags:
    - Dice
  - type: Item
    size: Tiny

- type: entity
  parent: BaseDice
  id: PercentileDie
  name: "процентильна плашка"
  description: "Кубик з десятьма гранями. Краще підходить для кидків d100, ніж м'яч для гольфу."
  components:
  - type: Dice
    sides: 10
    multiplier: 10
    offset: 1 # first side is a 0
    currentValue: 0
  - type: Sprite
    state: percentile_0

- type: entity
  parent: BaseDice
  id: d20Dice
  name: "d20"
  description: "Кубик з двадцятьма гранями. Найкраще кидати на ГМ."
  components:
  - type: Dice
    sides: 20
    currentValue: 20
  - type: Sprite
    state: d20_20

- type: entity
  parent: BaseDice
  id: d12Dice
  name: "d12"
  description: "Кубик з дванадцятьма гранями. У ній відчувається якась занедбаність."
  components:
  - type: Dice
    sides: 12
    currentValue: 12
  - type: Sprite
    state: d12_12

- type: entity
  parent: BaseDice
  id: d10Dice
  name: "d10"
  description: "Кубик з десятьма гранями. Корисний для відсотків."
  components:
  - type: Dice
    sides: 10
    offset: 1
    currentValue: 0
  - type: Sprite
    state: d10_0

- type: entity
  parent: BaseDice
  id: d8Dice
  name: "d8"
  description: "Кубик з вісьмома гранями. Відчуття... удачі."
  components:
  - type: Dice
    sides: 8
    currentValue: 8
  - type: Sprite
    state: d8_8

- type: entity
  parent: BaseDice
  id: d6Dice
  name: "d6"
  description: "Плашка з шістьма сторонами. Простий і придатний до використання."
  components:
  - type: Dice
    sides: 6
    currentValue: 6
  - type: Sprite
    state: d6_6

- type: entity
  parent: BaseDice
  id: d4Dice
  name: "d4"
  description: "Кубик з чотирма гранями. Калтроп ботаніка."
  components:
  - type: Dice
    sides: 4
    currentValue: 4
  - type: Sprite
    state: d4_4
  - type: CollisionWake
    enabled: false
  - type: Fixtures
    fixtures:
      slips:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.2,-0.2,0.2,0.2"
        hard: false
        layer:
        - LowImpassable
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.2,-0.2,0.2,0.2"
        density: 30
        mask:
        - ItemMask
  - type: StepTrigger
    triggerGroups:
      types:
      - Shard
    intersectRatio: 0.2
  - type: TriggerOnStepTrigger
  - type: ClothingRequiredStepTrigger
  - type: Slippery
    slipSound:
      path: /Audio/Effects/glass_step.ogg
    launchForwardsMultiplier: 0
  - type: DamageUserOnTrigger
    damage:
      types:
        Piercing: 5
    # I love this
