<PanelContainer xmlns="https://spacestation14.io"
                xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                HorizontalExpand="True"
                Name="BigPanel"
                Margin="5 0">
    <BoxContainer Orientation="Vertical"
                  Margin="5 5">
        <!-- Title box -->
        <controls:StripeBack>
            <Label Name="TitleStripe"
                   HorizontalAlignment="Center"
                   Margin="0 5 0 5"/>
        </controls:StripeBack>
        <BoxContainer Orientation="Vertical" Name="ContentBox"/>
        <!-- Buffer so all claim buttons are in the same position -->
        <Control VerticalExpand="True"/>
        <Button Name="ClaimButton"
                HorizontalExpand="True"
                VerticalAlignment="Bottom"
                ToggleMode="True"
                Disabled="True"
                Text="{Loc 'offering-window-claim'}"/>
    </BoxContainer>
</PanelContainer>
