﻿- type: entity
  name: "AI Timed Spawner"
  id: AITimedSpawner
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - state: blue
        - sprite: Mobs/Species/Human/parts.rsi
          state: full
        - state: ai
    - type: Timer
    - type: TimedSpawner
      prototypes:
        - MobSpirate
        - MobCivilian
      chance: 0.75
      intervalSeconds: 60
      minimumEntitiesSpawned: 1
      maximumEntitiesSpawned: 5

- type: entity
  name: "Xeno AI Timed Spawner"
  id: XenoAITimedSpawner
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
      - state: blue
      - sprite: Mobs/Aliens/Xenos/burrower.rsi
        state: crit
      - state: ai
    - type: Timer
    - type: TimedSpawner
      prototypes:
      - MobXeno
      chance: 0.85
      intervalSeconds: 30
      minimumEntitiesSpawned: 2
      maximumEntitiesSpawned: 4

- type: entity
  name: "спавнер мишей з таймером"
  id: MouseTimedSpawner
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - state: blue
        - sprite: Mobs/Animals/mouse.rsi
          state: icon-2
        - state: timed
    - type: Timer
    - type: TimedSpawner
      prototypes:
        - MobMouse
        - MobMouse1
        - MobMouse2
      # round ~90m
      # one spawner should only spawn ~5 mice for sanity reasons
      # therefore 18m
      # use 50% chance for randomness and balance with halved interval
      chance: 0.5
      intervalSeconds: 540
      minimumEntitiesSpawned: 1
      maximumEntitiesSpawned: 1

- type: entity
  name: "спавнер тарганів з таймером"
  id: CockroachTimedSpawner
  parent: MouseTimedSpawner
  components:
    - type: Sprite
      layers:
        - state: blue
        - sprite: Mobs/Animals/cockroach.rsi
          state: cockroach_icon
        - state: timed
    - type: TimedSpawner
      prototypes:
        - MobCockroach
