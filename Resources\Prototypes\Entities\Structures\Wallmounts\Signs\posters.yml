- type: entity
  parent: BaseSign
  id: PosterBase
  abstract: true
  components:
  - type: WallMount
    arc: 360
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Wallmounts/posters.rsi
    snapCardinals: true
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 5
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          path: /Audio/Effects/poster_broken.ogg
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:SpawnEntitiesBehavior
        spawn:
          PosterBroken:
            min: 1
            max: 1
        offset: 0

- type: entity
  parent: BaseSign
  id: PosterBroken
  name: "розбитий плакат"
  description: "На оригіналі плакату нічого не можна розгледіти. Він знищений."
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Wallmounts/posters.rsi
    state: poster_broken
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 5
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          path: /Audio/Effects/poster_broken.ogg
      - !type:DoActsBehavior
        acts: [ "Destruction" ]

# Contraband
- type: entity
  parent: PosterBase
  id: PosterContrabandFreeTonto
  name: "Вільний Тонто"
  description: "Врятований клапоть набагато більшого прапора, кольори якого злилися воєдино і вицвіли від часу."
  components:
  - type: Sprite
    state: poster1_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandAtmosiaDeclarationIndependence
  name: "Декларація незалежності Атмосфери"
  description: "Реліквія невдалого повстання."
  components:
  - type: Sprite
    state: poster2_contraband
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-atmosia"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterContrabandFunPolice
  name: "Поліція розваг"
  description: "Плакат із засудженням дій силовиків на вокзалі."
  components:
  - type: Sprite
    state: poster3_contraband
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-funpolice"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterContrabandLustyExomorph
  name: "Хтивий екзоморф"
  description: "Єретичний плакат із зображенням титульної зірки не менш єретичної книги."
  components:
  - type: Sprite
    state: poster4_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandSyndicateRecruitment
  name: "Вербування в синдикати"
  description: "Побачити галактику! Розтрощи корумповані мегакорпорації! Приєднуйся сьогодні!"
  components:
  - type: Sprite
    state: poster5_contraband
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-syndicaterecruitment"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterContrabandClown
  name: "Клоун"
  description: "Гудок."
  components:
  - type: Sprite
    state: poster6_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandSmoke
  name: "Дим"
  description: "Плакат, що рекламує конкуруючу корпоративну марку сигарет."
  components:
  - type: Sprite
    state: poster7_contraband
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-contrabandsmoke"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterContrabandGreyTide
  name: "Сірий приплив"
  description: "Бунтівний плакат, що символізує солідарність пасажирів."
  components:
  - type: Sprite
    state: poster8_contraband
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-greytide"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterContrabandMissingGloves
  name: "Зниклі рукавички"
  description: "Цей плакат відсилає до скандалу, що виник після того, як компанія Nanotrasen скоротила фінансові витрати на закупівлю ізоляційних рукавичок."
  components:
  - type: Sprite
    state: poster9_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandHackingGuide
  name: "Посібник зі злому"
  description: "На цьому плакаті детально описано внутрішню роботу загального шлюзу Нанотрейзен. На жаль, він виглядає застарілим."
  components:
  - type: Sprite
    state: poster10_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandRIPBadger
  name: "Нехай живе Борсук"
  description: "Цей бунтарський плакат відсилає до геноциду нанотрейзеном космічної станції, повної борсуків."
  components:
  - type: Sprite
    state: poster11_contraband
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-ripbadger"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterContrabandAmbrosiaVulgaris
  name: "Амброзія вульгарна"
  description: "Цей постер виглядає досить дивно, чувак."
  components:
  - type: Sprite
    state: poster12_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandDonutCorp
  name: "Пончикова корпорація."
  description: "Цей плакат є несанкціонованою рекламою компанії Donut Corp."
  components:
  - type: Sprite
    state: poster13_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandEAT
  name: "ЇЖ."
  description: "Цей плакат пропагує ненажерливість."
  components:
  - type: Sprite
    state: poster14_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandTools
  name: "Інструменти"
  description: "Цей плакат виглядає як реклама інструментів, але насправді є підсвідомим уколом на адресу інструментів CentCom."
  components:
  - type: Sprite
    state: poster15_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandPower
  name: "Сила"
  description: "Плакат, який позиціонує місце влади за межами нанотрейзену."
  components:
  - type: Sprite
    state: poster16_contraband
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-contrabandpower"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterContrabandSpaceCube
  name: "Космічний куб"
  description: "Не знаючи про створення Гармонійного 6-гранного космічного куба природи, космонавти тупі, а освічені сингулярності тупі і злі."
  components:
  - type: Sprite
    state: poster17_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandCommunistState
  name: "Огризки плаката"
  description: "Колись тут був якийсь плакат, напевно сам розпався.."
  components:
  - type: Sprite
    state: poster_broken

- type: entity
  parent: PosterBase
  id: PosterContrabandLamarr
  name: "Ламарр"
  description: "На цьому плакаті зображений Ламарр. Ймовірно, зроблений зрадником Містагогом." # DeltaV - Epistemics Department replacing Science
  components:
  - type: Sprite
    state: poster19_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandBorgFancy
  name: "Borg Fancy"
  description: "Бути модним може кожен борг, потрібен лише костюм."
  components:
  - type: Sprite
    state: poster20_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandBorgFancyv2
  name: "Borg Fancy v2"
  description: "Borg Fancy, тепер беремо тільки наймодніше."
  components:
  - type: Sprite
    state: poster21_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandKosmicheskayaStantsiya
  name: "Космічна станція 13 не існує"
  description: "Плакат, що висміює заперечення CentCom про існування занедбаної станції поблизу Космічної станції 13."
  components:
  - type: Sprite
    state: poster22_contraband
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-KosmicheskayaStantsiya"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterContrabandRebelsUnite
  name: "Повстанці об'єднуються"
  description: "Плакат, що закликає глядача повстати проти нанотрейзену."
  components:
  - type: Sprite
    state: poster23_contraband
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-rebelsunite"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterContrabandC20r
  name: "C-20r"
  description: "Плакат з рекламою Scarborough Arms C-20r."
  components:
  - type: Sprite
    state: poster24_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandHaveaPuff
  name: "Затягніться"
  description: "Кого хвилює рак легенів, коли ти під кайфом?"
  components:
  - type: Sprite
    state: poster25_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandRevolver
  name: "Револьвер"
  description: "Тому що сім пострілів - це все, що вам потрібно."
  components:
  - type: Sprite
    state: poster26_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandDDayPromo
  name: "Промо-акція \"День перемоги"
  description: "Рекламний постер якогось репера."
  components:
  - type: Sprite
    state: poster27_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandSyndicatePistol
  name: "Пістолет Синдикату"
  description: "Плакат, що рекламує пістолети синдикату як \"до біса стильні\". Вкритий вицвілими емблемами банди."
  components:
  - type: Sprite
    state: poster28_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandEnergySwords
  name: "Енергетичні мечі"
  description: "Всіма кольорами кривавої веселки вбивств."
  components:
  - type: Sprite
    state: poster29_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandRedRum
  name: "Червоний ром"
  description: "Дивлячись на цей плакат, хочеться вбивати."
  components:
  - type: Sprite
    state: poster30_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandCC64KAd
  name: "Реклама КК 64K"
  description: "Новітній портативний комп'ютер від Comrade Computing, з цілими 64kB оперативної пам'яті!"
  components:
  - type: Sprite
    state: poster31_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandPunchShit
  name: "Punch Shit"
  description: "Боріться з речами без причини, як справжній чоловік!"
  components:
  - type: Sprite
    state: poster32_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandTheGriffin
  name: "Грифон"
  description: "Грифон наказує тобі бути найгіршим, яким ти можеш бути. Будеш?"
  components:
  - type: Sprite
    state: poster33_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandFreeDrone
  name: "Вільний дрон"
  description: "Цей плакат вшановує хоробрість дрона-ізгоя, якого колись вислали, а згодом знищили в Центркомі."
  components:
  - type: Sprite
    state: poster35_contraband
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-freedrone"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterContrabandBustyBackdoorExoBabes6
  name: "Грудаста задня двері Exo Babes 6"
  description: "Отримайте або подаруйте ці натуральні Екзо!"
  components:
  - type: Sprite
    state: poster36_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandRobustSoftdrinks
  name: "Міцні безалкогольні напої"
  description: "Міцні безалкогольні напої: Міцніші, ніж ящик з інструментами до голови!"
  components:
  - type: Sprite
    state: poster37_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandShamblersJuice
  name: "Shambler's Juice"
  description: "~Налий мені соку Шамблер!"
  components:
  - type: Sprite
    state: poster38_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandPwrGame
  name: "Pwr Game"
  description: "ПОТУЖНІСТЬ, яку прагнуть геймери! У партнерстві з \"Салатом Влада\"."
  components:
  - type: Sprite
    state: poster39_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandSunkist
  name: "Sun-kist"
  description: "Пийте зірки!"
  components:
  - type: Sprite
    state: poster40_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandSpaceCola
  name: "Космічна Кола"
  description: "Ваша улюблена кола в космосі."
  components:
  - type: Sprite
    state: poster41_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandSpaceUp
  name: "Вгору!"
  description: "Висмоктаний у космос АРОМАТОМ!"
  components:
  - type: Sprite
    state: poster42_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandKudzu
  name: "Кудзу"
  description: "Постер з рекламою фільму про рослини. Наскільки небезпечними вони можуть бути?"
  components:
  - type: Sprite
    state: poster43_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandMaskedMen
  name: "Люди в масках"
  description: "Постер з рекламою фільму про людей у масках."
  components:
  - type: Sprite
    state: poster44_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandUnreadableAnnouncement
  name: "Нечитабельне оголошення"
  description: "Плакат, на якому хтось щось анонсує, але, як не дивно, здається, забув зробити його читабельним"
  components:
  - type: Sprite
    state: poster45_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandFreeSyndicateEncryptionKey
  name: "Безкоштовний ключ шифрування синдикату"
  description: "Плакат про зрадників, які благають більше."
  components:
  - type: Sprite
    state: poster46_contraband
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-syndicateencryption"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterContrabandBountyHunters
  name: "Мисливці за головами"
  description: "Плакат з рекламою послуг мисливців за головами. \"Я чув, у вас проблема\""
  components:
  - type: Sprite
    state: poster47_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandTheBigGasTruth
  name: "Правда про великого газового гіганта"
  description: "Не вірте всьому, що бачите на плакаті, патріоти. Всі ящірки в центральному командуванні не хочуть відповісти на ПРОСТЕ ПИТАННЯ: Звідки видобувається газ, ЦЕНТРКОМ?"
  components:
  - type: Sprite
    state: poster48_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandWehWatches
  name: "Weh дивиться"
  description: "Плакат із зображенням милої зеленої ящірки."
  components:
  - type: Sprite
    state: poster50_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandVoteWeh
  name: "Голосуй!"
  description: "Стильний, лаконічний і добре ілюстрований плакат нового прогресивного кандидата, який прийде на вибори цього сезону."
  components:
  - type: Sprite
    state: poster51_contraband

# These 3 originally from VEEGEE
- type: entity
  parent: PosterBase
  id: PosterContrabandBeachStarYamamoto
  name: "Пляжна зірка Ямамото!"
  description: "Настінний сувій із зображенням старого аніме про плавання з дівчатами в маленьких купальниках. Чим довше дивишся на нього, тим більше відчуваєш себе павутинням."
  components:
  - type: Sprite
    state: poster52_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandHighEffectEngineering
  name: "Інженерія високих ефектів"
  description: "Є 3 осколки і сингулярність.  Осколки співають.  Інженери плачуть."
  components:
  - type: Sprite
    state: poster53_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandNuclearDeviceInformational
  name: "Інформація про ядерні пристрої"
  description: "На цьому плакаті зображено ядерний вибуховий пристрій старого зразка, а також корисну інформацію про те, що робити, якщо він спрацював. Він пропонує лягти на підлогу і плакати."
  components:
  - type: Sprite
    state: poster54_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandRise
  name: "Повстаньте!"
  description: "Плакат із зображенням чоловіка в сірій сорочці, який тримає в руках лом, а під ним написано \"Повстань\"."
  components:
  - type: Sprite
    state: poster55_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandRevolt
  name: "Повстання"
  description: "Революційна пропаганда, виготовлена Синдикатом."
  components:
  - type: Sprite
    state: poster56_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandMoth
  name: "Syndie Moth - Ядерна операція"
  description: "Плакат на замовлення Синдикату, який використовує Syndie Moth™, щоб закликати глядача тримати ядерний аутентифікаційний диск незахищеним. \"Мир ніколи не був варіантом!\" Жоден хороший працівник не буде слухати цю нісенітницю."
  components:
    - type: Sprite
      state: poster57_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandCybersun600
  name: "Кіберсонце: Ювілейний плакат до 600-річчя"
  description: "Художній плакат, присвячений 600-річчю безперервної діяльності компанії Cybersun Industries."
  components:
    - type: Sprite
      state: poster58_contraband
    - type: ExtendDescription
      descriptionList:
        - description: "mindshield-extenddescription-poster-cybersun600"
          fontSize: 12
          color: "#11aaff"
          requireDetailRange: false
          requirements:
          - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterContrabandDonk
  name: "DONK CO. БРЕНД ЇЖІ ДЛЯ МІКРОХВИЛЬОВОЇ ПЕЧІ"
  description: "DONK CO. БРЕНД ЇЖІ ДЛЯ МІКРОХВИЛЬОВКИ: ЗРОБЛЕНА ГОЛОДНИМИ СТУДЕНТАМИ КОЛЕДЖУ, ДЛЯ ГОЛОДНИХ СТУДЕНТІВ КОЛЕДЖУ."
  components:
    - type: Sprite
      state: poster59_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandEnlistGorlex
  name: "Вступай"
  description: "Приєднуйтесь до мародерів Gorlex вже сьогодні! Побач галактику, вбивай корпорації, отримуй гроші!"
  components:
    - type: Sprite
      state: poster60_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandInterdyne
  name: "Інтердайн Фармасьютікалс: Заради здоров'я людства"
  description: "Реклама клінік GeneClean компанії Interdyne Pharmaceutics. \"Стань господарем свого тіла!"
  components:
    - type: Sprite
      state: poster61_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandWaffleCorp
  name: "Зробіть мою корпорацію вафельною: Хороші гвинтівки, економічні ціни"
  description: "Стара реклама гвинтівок Waffle Corp. \"Краща зброя, нижчі ціни!"
  components:
    - type: Sprite
      state: poster62_contraband

- type: entity
  parent: PosterBase
  id: PosterContrabandMissingSpacepen
  name: "Пропав фломастер"
  description: "На цьому плакаті зображено те, чого ви ніколи не знайдете."
  components:
    - type: Sprite
      state: poster63_contraband

# Legit
- type: entity
  parent: PosterBase
  id: PosterLegitHereForYourSafety
  name: "Тут для вашої безпеки"
  description: "Плакат, що прославляє службу безпеки станції."
  components:
  - type: Sprite
    state: poster1_legit
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-foryoursafety"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterLegitNanotrasenLogo
  name: "Логотип Nanotrasen"
  description: "Плакат із зображенням логотипу Nanotrasen."
  components:
  - type: Sprite
    state: poster2_legit
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-nanotrasen"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterLegitCleanliness
  name: "Чистота"
  description: "Плакат, що попереджає про небезпеку поганої гігієни."
  components:
  - type: Sprite
    state: poster3_legit

- type: entity
  parent: PosterBase
  id: PosterLegitHelpOthers
  name: "Допомогти іншим"
  description: "Плакат із закликом допомагати іншим членам екіпажу."
  components:
  - type: Sprite
    state: poster4_legit
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-helpothers"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterLegitBuild
  name: "Побудова"
  description: "Плакат, що прославляє команду інженерів."
  components:
  - type: Sprite
    state: poster5_legit

- type: entity
  parent: PosterBase
  id: PosterLegitBlessThisSpess
  name: "Благослови цей день"
  description: "Плакат з благословенням цієї території."
  components:
  - type: Sprite
    state: poster6_legit

- type: entity
  parent: PosterBase
  id: PosterLegitScience
  name: "Наука"
  description: "Плакат із зображенням атома."
  components:
  - type: Sprite
    state: poster7_legit

- type: entity
  parent: PosterBase
  id: PosterLegitIan
  name: "Іане"
  description: "Арф арф. Гав."
  components:
  - type: Sprite
    state: poster8_legit

- type: entity
  parent: PosterBase
  id: PosterLegitObey
  name: "Слухайся"
  description: "Плакат, що закликає глядача підкорятися владі."
  components:
  - type: Sprite
    state: poster9_legit
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-obey"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterLegitWalk
  name: "Прогулянка"
  description: "Плакат, що закликає глядача йти, а не бігти."
  components:
  - type: Sprite
    state: poster10_legit

- type: entity
  parent: PosterBase
  id: PosterLegitStateLaws
  name: "Державні закони"
  description: "Плакат, що навчає кіборгів викладати свої закони."
  components:
  - type: Sprite
    state: poster11_legit

- type: entity
  parent: PosterBase
  id: PosterLegitLoveIan
  name: "З любов'ю, Іане"
  description: "Ян - це любов, Ян - це життя."
  components:
  - type: Sprite
    state: poster12_legit

- type: entity
  parent: PosterBase
  id: PosterLegitSpaceCops
  name: "Космічні копи."
  description: "Плакат з рекламою телесеріалу \"Космічні копи\"."
  components:
  - type: Sprite
    state: poster13_legit

- type: entity
  parent: PosterBase
  id: PosterLegitUeNo
  name: "Ні."
  description: "Це все японською мовою."
  components:
  - type: Sprite
    state: poster14_legit

- type: entity
  parent: PosterBase
  id: PosterLegitGetYourLEGS
  name: "Піднімайте свої ноги"
  description: "LEGS: Лідерство, Досвід, Геніальність, Субординація."
  components:
  - type: Sprite
    state: poster15_legit

- type: entity
  parent: PosterBase
  id: PosterLegitDoNotQuestion
  name: "Не ставити під сумнів"
  description: "Плакат із закликом до глядача не розпитувати про те, чого він не повинен знати."
  components:
  - type: Sprite
    state: poster16_legit
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-donotquestion"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterLegitWorkForAFuture
  name: "Робота заради майбутнього"
  description: "Плакат із закликом працювати заради свого майбутнього."
  components:
  - type: Sprite
    state: poster17_legit

- type: entity
  parent: PosterBase
  id: PosterLegitSoftCapPopArt
  name: "Поп-арт у м'якій шапці"
  description: "Плакатний передрук якогось дешевого поп-арту."
  components:
  - type: Sprite
    state: poster18_legit

- type: entity
  parent: PosterBase
  id: PosterLegitSafetyInternals
  name: "Безпека: Внутрішні приміщення"
  description: "Плакат, що закликає глядача носити внутрішні органи в рідкісних умовах, де немає кисню або повітря стало токсичним."
  components:
  - type: Sprite
    state: poster19_legit

- type: entity
  parent: PosterBase
  id: PosterLegitSafetyEyeProtection
  name: "Безпека: Захист очей"
  description: "Плакат, що закликає глядача носити захисні окуляри при роботі з хімікатами, димом або яскравим світлом."
  components:
  - type: Sprite
    state: poster20_legit

- type: entity
  parent: PosterBase
  id: PosterLegitSafetyReport
  name: "Безпека: Звіт"
  description: "Плакат із закликом до глядача повідомляти про підозрілу активність силам безпеки."
  components:
  - type: Sprite
    state: poster21_legit
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-safetyreport"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterLegitReportCrimes
  name: "Повідомити про злочини"
  description: "Плакат із закликом негайно повідомляти охорону вокзалу про злочини або бунтівну поведінку."
  components:
  - type: Sprite
    state: poster22_legit
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-reportcrimes"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterLegitIonRifle
  name: "Іонна гвинтівка"
  description: "Плакат із зображенням іонної гвинтівки."
  components:
  - type: Sprite
    state: poster23_legit

- type: entity
  parent: PosterBase
  id: PosterLegitFoamForceAd
  name: "Реклама піноутворювача Foam Force"
  description: "Foam Force - це піна або бути піною!"
  components:
  - type: Sprite
    state: poster24_legit

- type: entity
  parent: PosterBase
  id: PosterLegitCohibaRobustoAd
  name: "Реклама Cohiba Robusto"
  description: "Cohiba Robusto, класична сигара."
  components:
  - type: Sprite
    state: poster25_legit

- type: entity
  parent: PosterBase
  id: PosterLegit50thAnniversaryVintageReprint
  name: "Ювілейне репринтне видання до 50-річчя"
  description: "Репринт плаката 2505 року, присвяченого 50-річчю виробництва нанопостерів, дочірньої компанії \"нанотрейзен\"."
  components:
  - type: Sprite
    state: poster26_legit
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-vintagereprint50"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterLegitFruitBowl
  name: "Фруктова миска"
  description: "Простий, але вражаючий."
  components:
  - type: Sprite
    state: poster27_legit

- type: entity
  parent: PosterBase
  id: PosterLegitPDAAd
  name: "Реклама на КПК"
  description: "Плакат з рекламою новітніх КПК від постачальників Nanotrasen."
  components:
  - type: Sprite
    state: poster28_legit
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-pdaad"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterLegitEnlist
  name: "Вступай"
  description: "Запишіться в резерви ГШР NanoTrasen вже сьогодні!" # DeltaV - expunged blatant reference to Division of Asset Protection death squads
  components:
  - type: Sprite
    state: poster29_legit
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-enlist"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterLegitNanomichiAd
  name: "Оголошення про Наномічі"
  description: "Плакат з рекламою аудіокасет марки Nanomichi."
  components:
  - type: Sprite
    state: poster30_legit

- type: entity
  parent: PosterBase
  id: PosterLegit12Gauge
  name: "12 калібр"
  description: "Плакат, що вихваляється перевагою набоїв для дробовиків 12-го калібру."
  components:
  - type: Sprite
    state: poster31_legit

- type: entity
  parent: PosterBase
  id: PosterLegitHighClassMartini
  name: "Висококласний мартіні"
  description: "Я ж казав тобі збовтати, але не перемішувати."
  components:
  - type: Sprite
    state: poster32_legit

- type: entity
  parent: PosterBase
  id: PosterLegitTheOwl
  name: "Сова"
  description: "Сова зробить усе можливе, щоб захистити станцію. А ти?"
  components:
  - type: Sprite
    state: poster33_legit
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-theowl"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterLegitNoERP
  name: "Немає ERP"
  description: "Цей плакат нагадує екіпажу, що еротика і порнографія заборонені на станціях нанотрейзен."
  components:
  - type: Sprite
    state: poster34_legit

- type: entity
  parent: PosterBase
  id: PosterLegitCarbonDioxide
  name: "Вуглекислий газ"
  description: "Цей інформаційний плакат розповідає глядачеві, що таке вуглекислий газ."
  components:
  - type: Sprite
    state: poster35_legit

- type: entity
  parent: PosterBase
  id: PosterLegitDickGumshue
  name: "Дік Гамшу"
  description: "Плакат, що рекламує пригоди мишачого детектива Діка Гамшу. Закликає команду обрушити всю міць правосуддя на дротяних саботажників."
  components:
  - type: Sprite
    state: poster36_legit

- type: entity
  parent: PosterBase
  id: PosterLegitThereIsNoGasGiant
  name: "Газового гіганта не існує"
  description: "нанотрейзен випустив плакати, подібні до цього, на всіх станціях, нагадуючи, що чутки про газового гіганта неправдиві."
  components:
  - type: Sprite
    state: poster37_legit
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-nogasgiant"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterLegitJustAWeekAway
  name: "Залишився тиждень..."
  description: "Плакат, що рекламує давно відкладений проект, все ще стверджує, що до його завершення залишився \"лише тиждень...\""
  components:
  - type: Sprite
    state: poster38_legit

- type: entity
  parent: PosterBase
  id: PosterLegitSecWatch
  name: "СБ\" спостерігає за тобою"
  description: "Плакат, який нагадує, що охорона стежить за кожним вашим кроком."
  components:
  - type: Sprite
    state: poster39_legit
  - type: ExtendDescription
    descriptionList:
      - description: "mindshield-extenddescription-poster-watchingyou"
        fontSize: 12
        color: "#11aaff"
        requireDetailRange: false
        requirements:
        - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterLegitAnatomyPoster
  name: "Анатомія спейсмена"
  description: "Постер із зображенням дрібничок, які роблять вас... вами!"
  components:
  - type: Sprite
    state: poster40_legit

- type: entity
  parent: PosterBase
  id: PosterLegitMime
  name: "Мім Постмодерн"
  description: "Постмодерністське зображення міма, чудове!"
  components:
  - type: Sprite
    state: poster41_legit

- type: entity
  parent: PosterBase
  id: PosterLegitCarpMount
  name: "Настінний короп"
  description: "Лови момент!"
  components:
  - type: Sprite
    state: poster42_legit

- type: entity
  parent: PosterBase
  id: PosterLegitSafetyMothDelam
  name: "Безпечна моль - заходи безпеки при розшаруванні"
  description: "Цей інформаційний плакат за допомогою Safety Moth™ закликає глядачів сховатися в шафках, коли кристал надматерії розшарувався, щоб запобігти галюцинаціям. Евакуація може бути кращою стратегією."
  components:
    - type: Sprite
      state: poster43_legit

- type: entity
  parent: PosterBase
  id: PosterLegitSafetyMothEpi
  name: "Метелик безпеки - Епінефрин"
  description: "Цей інформаційний плакат використовує Safety Moth™, щоб проінформувати глядача про допомогу пораненим/загиблим членам екіпажу за допомогою ін'єктора з адреналіном. \"Запобігайте гниттю органів за допомогою цього простого трюку!\""
  components:
    - type: Sprite
      state: poster44_legit

- type: entity
  parent: PosterBase
  id: PosterLegitSafetyMothHardhat
  name: "Safety Moth - Каски"
  description: "Цей інформаційний плакат використовує Safety Moth™, щоб нагадати глядачеві про необхідність носити каски в небезпечних зонах. \"Це як лампа для голови!\""
  components:
    - type: Sprite
      state: poster45_legit

- type: entity
  parent: PosterBase
  id: PosterLegitSafetyMothMeth
  name: "Метелик безпеки - метамфетамін"
  description: "Цей інформаційний плакат використовує Safety Moth™, щоб нагадати глядачеві, що перед приготуванням метамфетаміну необхідно отримати дозвіл ОКУ. \"Тримайся ближче до цільової температури і ніколи не перевищуй її!\" ...Ніколи не готуйте цього."
  components:
    - type: Sprite
      state: poster46_legit

- type: entity
  parent: PosterBase
  id: PosterLegitSafetyMothPiping
  name: "Захисна моль - Трубопровід"
  description: "Цей інформаційний плакат використовує Safety Moth™, щоб розповісти технічним спеціалістам з атмосферних систем про правильні типи трубопроводів, які слід використовувати. \"Труби, а не насоси! Правильне розміщення труб запобігає поганій роботі!\""
  components:
    - type: Sprite
      state: poster47_legit

- type: entity
  parent: PosterBase
  id: PosterLegitVacation
  name: "Корпоративні пільги від нанотрейзен: Відпустка"
  description: "Цей інформаційний плакат містить інформацію про деякі призи, доступні в рамках програми NT Corporate Perks, включаючи двотижневу відпустку на двох у курортному світі Idyllus."
  components:
    - type: Sprite
      state: poster48_legit
    - type: ExtendDescription
      descriptionList:
        - description: "mindshield-extenddescription-poster-vacation"
          fontSize: 12
          color: "#11aaff"
          requireDetailRange: false
          requirements:
          - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterLegitPeriodicTable
  name: "Періодична система елементів"
  description: "Періодична таблиця елементів, від Гідрогену до Оганесона, і все, що між ними."
  components:
    - type: Sprite
      state: poster49_legit

- type: entity
  parent: PosterBase
  id: PosterLegitRenault
  name: "Постер Renault"
  description: "Яп."
  components:
    - type: Sprite
      state: poster50_legit

- type: entity
  parent: PosterBase
  id: PosterLegitNTTGC
  name: "Тактичні ігрові карти Nanotrasen"
  description: "Реклама карток TCG від нанотрейзен: КУПУЙТЕ БІЛЬШЕ КАРТОК."
  components:
    - type: Sprite
      state: poster51_legit
    - type: ExtendDescription
      descriptionList:
        - description: "mindshield-extenddescription-poster-buycards"
          fontSize: 12
          color: "#11aaff"
          requireDetailRange: false
          requirements:
          - !type:CharacterMindshieldRequirement

- type: entity
  parent: PosterBase
  id: PosterLegitSafetyMothSSD
  name: "Метелик безпеки - космічний розлад сну"
  description: "Цей інформаційний плакат використовує Safety Moth™, щоб розповісти глядачеві про розлад космічного сну (РКС) - стан, коли людина перестає реагувати на речі. \"Поводьтеся з екіпажем космічного корабля обережно! Вони можуть прокинутися в будь-який момент!\""
  components:
  - type: Sprite
    state: poster52_legit

- type: entity
  parent: PosterBase
  id: PosterLegitOppenhopper
  name: "Оппенхоппер"
  description: "Постер до давно забутого фільму. Він розповідає про групу завзятих новачків із сектору \"Коник\", які захищаються від нападів сумнозвісних \"Ядерних оперативників\". Слоган говорить: \"Nuke Ops триватиме доти, доки надійність не покращиться\""
  components:
  - type: Sprite
    state: poster53_legit

#maps

- type: entity
  parent: PosterBase
  id: PosterMapBagel
  name: "Карта бубликів"
  description: "Мапа станції \"Бублик\"."
  components:
  - type: Sprite
    state: bagelmap

- type: entity
  parent: PosterBase
  id: PosterMapDelta
  name: "Карта дельти"
  description: "Карта станції Дельта."
  components:
  - type: Sprite
    state: deltamap

- type: entity
  parent: PosterBase
  id: PosterMapMarathon
  name: "Карта марафону"
  description: "Мапа Марафонського вокзалу."
  components:
  - type: Sprite
    state: marathonmap

- type: entity
  parent: PosterBase
  id: PosterMapMoose
  name: "Карта лося"
  description: "Мапа станції Лось."
  components:
  - type: Sprite
    state: moosemap

- type: entity
  parent: PosterBase
  id: PosterMapPacked
  name: "Упакована карта"
  description: "Мапа станції \"Пакгауз\"."
  components:
  - type: Sprite
    state: packedmap

- type: entity
  parent: PosterBase
  id: PosterMapPillar
  name: "Карта стовпів"
  description: "Мапа Стовпа НСБ."
  components:
  - type: Sprite
    state: pillarmap

- type: entity
  parent: PosterBase
  id: PosterMapSaltern
  name: "Карта Салтерна"
  description: "Мапа станції Салтерн."
  components:
  - type: Sprite
    state: salternmap

- type: entity
  parent: PosterBase
  id: PosterMapSplit
  name: "Мапа станції Спліт Мапа станції Спліт"
  description: "Мапа станції Спліт."
  components:
  - type: Sprite
    state: splitmap

- type: entity
  parent: PosterBase
  id: PosterMapLighthouse
  name: "Карта маяків"
  description: "Карта Маяка."
  components:
  - type: Sprite
    state: lighthousemap

- type: entity
  parent: PosterBase
  id: PosterMapWaystation
  name: "Карта маршрутних станцій"
  description: "Карта Waystation... зачекайте, хіба вона не упакована догори дригом?"
  components:
  - type: Sprite
    state: waystationmap

- type: entity
  parent: PosterBase
  id: PosterMapOrigin
  name: "карта походження"
  description: "Карта станції Origin Station."
  components:
  - type: Sprite
    state: originmap
