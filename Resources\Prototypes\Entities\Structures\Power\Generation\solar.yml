- type: entity
  id: SolarPanelBasePhysSprite
  categories: [ HideSpawnMenu ]
  name: "сонячна панель"
  placement:
    mode: SnapgridCenter
  components:
  - type: MeleeSound
    soundGroups:
      Brute:
        collection: GlassSmash
  - type: Clickable
  - type: InteractionOutline
  - type: Transform
    anchored: true
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.1,0.45,0.1"
        density: 550
        mask:
        - MachineMask
        layer:
        - MidImpassable, LowImpassable, BulletImpassable
  - type: Sprite
    sprite: Structures/Power/Generation/solar_panel.rsi
    state: solar_panel_glass
  - type: NodeContainer
    examinable: true
    nodes:
      output:
        !type:CableDeviceNode
        nodeGroupID: HVPower
  - type: PowerMonitoringDevice
    group: Generator
    loadNode: output
    sprite: Structures/Power/Generation/solar_panel.rsi
    state: solar_panel_glass
    collectionName: SolarPanel
  - type: Anchorable
  - type: Pullable
  - type: Electrified
    onHandInteract: false
    onInteractUsing: false
    onBump: false
    requirePower: true
    highVoltageNode: output
  - type: RequireProjectileTarget

- type: entity
  id: SolarPanelPlasma
  parent: SolarPanelBasePhysSprite
  name: "плазмова сонячна панель"
  description: "Плазмова сонячна панель, яка генерує енергію."
  components:
  - type: PowerSupplier
    supplyRampTolerance: 2000
    supplyRampRate: 2000
  - type: SolarPanel
    maxSupply: 4000
  - type: Sprite
    sprite: Structures/Power/Generation/solar_panel.rsi
    state: solar_panel_plasma
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:ChangeConstructionNodeBehavior
        node: solarpanelplasma_broken
      - !type:DoActsBehavior
        acts: ["Breakage"]
  - type: Construction
    graph: SolarPanel
    node: solarpanelplasma

- type: entity
  id: SolarPanelUranium
  parent: SolarPanelBasePhysSprite
  name: "уранова сонячна батарея"
  description: "Уранова сонячна панель, яка генерує енергію."
  components:
  - type: PowerSupplier
    supplyRampTolerance: 2000
    supplyRampRate: 2000
  - type: SolarPanel
    maxSupply: 6000
  - type: Sprite
    sprite: Structures/Power/Generation/solar_panel.rsi
    state: solar_panel_uranium
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:ChangeConstructionNodeBehavior
        node: solarpaneluranium_broken
      - !type:DoActsBehavior
        acts: ["Breakage"]
  - type: Construction
    graph: SolarPanel
    node: solarpaneluranium

- type: entity
  id: SolarPanel
  parent: SolarPanelBasePhysSprite
  name: "сонячна панель"
  description: "Можна перетворити на сонячну панель або сонячний трекер."
  components:
  - type: PowerSupplier
    supplyRampTolerance: 4000
    supplyRampRate: 4000
  - type: SolarPanel
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:ChangeConstructionNodeBehavior
        node: solarpanel_broken
      - !type:DoActsBehavior
        acts: ["Breakage"]
  - type: Construction
    graph: SolarPanel
    node: solarpanel

- type: entity
  id: SolarPanelBroken
  parent: SolarPanelBasePhysSprite
  name: "сонячна панель"
  description: "Розбита сонячна панель."
  suffix: Broken
  components:
  - type: Sprite
    state: solar_panel_glass_broken
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlass:
            min: 1
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: SolarPanel
    node: solarpanel_broken

- type: entity
  id: SolarPanelPlasmaBroken
  parent: SolarPanelBasePhysSprite
  name: "плазмова сонячна панель"
  description: "Розбита плазмова сонячна панель. Ця, схоже, зламана."
  suffix: Broken
  components:
  - type: Sprite
    state: solar_panel_plasma_broken
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassPlasma:
            min: 1
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: SolarPanel
    node: solarpanelplasma_broken

- type: entity
  id: SolarPanelUraniumBroken
  parent: SolarPanelBasePhysSprite
  name: "уранова сонячна панель"
  description: "Розбита уранова сонячна панель. Ця, схоже, зламана."
  suffix: Broken
  components:
  - type: Sprite
    state: solar_panel_uranium_broken
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassUranium:
            min: 1
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: SolarPanel
    node: solarpaneluranium_broken

- type: entity
  id: SolarAssembly
  name: "збірка сонячної панелі"
  description: "Сонячна збірка. Прикріпіть якір до дроту, щоб почати будувати сонячну панель."
  placement:
    mode: SnapgridCenter
  components:
  - type: Clickable
  - type: InteractionOutline
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.25
        density: 375
        mask:
        - MachineMask
        layer:
        - MachineLayer
  - type: Sprite
    sprite: Structures/Power/Generation/solar_panel.rsi
    state: solar_assembly
  - type: Transform
    anchored: true
    noRot: true
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: ["Breakage"]
  - type: Anchorable
  - type: Pullable
  - type: Construction
    graph: SolarPanel
    node: solarassembly
    defaultTarget: solarpanel
  - type: RequireProjectileTarget

- type: entity
  id: SolarTracker
  name: "сонячний трекер"
  description: "Сонячний трекер. Відстежує найближчу зірку."
  placement:
    mode: SnapgridCenter
  components:
  - type: Clickable
  - type: InteractionOutline
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb {}
        density: 100
        mask:
        - MachineMask
        layer:
        - MachineLayer
  - type: Sprite
    sprite: Structures/Power/Generation/solar_panel.rsi
    state: solar_tracker
  - type: Transform
    anchored: true
    noRot: true
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: ["Breakage"]
  - type: Anchorable
  - type: Pullable
  - type: Construction
    graph: SolarPanel
    node: solartracker
  - type: RequireProjectileTarget
