# DND Necromancy Servant System

## Огляд

Система некромантських слуг для DND магів дозволяє оживляти трупи та керувати ними як слугами. Система інтегрована з існуючою системою DND кубиків та використовує кістку некромантії для активації заклинань.

## Компоненти

### DNDNecromancyServantComponent

Основний компонент, який додається до оживлених трупів:

- **Master**: EntityUid мага, який оживив труп
- **ServantDuration**: Тривалість життя слуги (за замовчуванням 300 секунд)
- **MaxDistanceFromMaster**: Максимальна відстань від майстра (15 метрів)
- **CurrentCommand**: Поточна команда слуги (Follow, Guard, Attack, Patrol, Stay)
- **Type**: Тип слуги (Warrior, Archer, Mage, Assassin)
- **ShouldFollowMaster**: Чи повинен слуга слідувати за майстром

### DNDNecromancyServantVisualComponent

Компонент для візуальних ефектів некромантських слуг:

- **GlowColor**: Колір свічення очей слуги
- **ParticleEffect**: Ефект частинок навколо слуги

## Система DNDNecromancyServantSystem

### Основні функції

#### TryResurrectAsServant(EntityUid corpse, EntityUid master, float duration)
Воскрешає труп як слугу некроманта:
- Перевіряє, чи це мертва істота
- Виключає магів з воскресіння
- Повністю лікує труп
- Додає компонент слуги
- Налаштовує faction та AI

#### SetupServantByType(EntityUid uid, DNDNecromancyServantComponent component)
Налаштовує слугу залежно від типу:
- **Warrior**: Підвищені HP та швидкість атаки
- **Archer**: Дальні атаки та підвищена швидкість
- **Mage**: Магічні здібності та регенерація
- **Assassin**: Висока швидкість та критичні удари

### Команди для слуг

Система підтримує наступні команди:

1. **Follow**: Слідувати за майстром
2. **Guard**: Охороняти позицію
3. **Attack**: Атакувати ворогів
4. **Patrol**: Патрулювати область
5. **Stay**: Залишатися на місці

### Інтеграція з DND кубиками

Система інтегрована з `DNDDiceSystem` через метод `CastNecromancySpell`:

```csharp
// У DNDDiceSystem.cs
private void CastNecromancySpell(EntityUid casterUid, float effectPower)
{
    // Знаходить трупи в радіусі 4 метри
    // Викликає TryResurrectAsServant для кожного трупа
    // Лікує мага за успішне воскресіння
    // Завдає пошкодження живим істотам
}
```

## Використання

### Для гравців

1. Отримайте набір DND кубиків (`DNDDiceSet`)
2. Знайдіть кістку некромантії (фіолетова d6)
3. Киньте кістку поруч з трупом
4. Труп воскресне як ваш слуга на 5-7 хвилин
5. Використовуйте команди для керування слугою

### Для адміністраторів

Спавн набору кубиків:
```
/spawn DNDDiceSet
```

Спавн окремої кістки некромантії:
```
/spawn DNDDiceD6Necromancy
```

## Прототипи

### Кістка некромантії
- **ID**: `DNDDiceD6Necromancy`
- **Сторони**: 6
- **Тип заклинання**: Necromancy
- **Сила заклинання**: 0.5
- **Кулдаун**: 10 секунд

### Слуга некроманта
- **ID**: `DNDNecromancyServant`
- **Базові HP**: 120
- **Швидкість**: 3.5/5.0
- **Пошкодження**: 8 Blunt + 5 Structural

## Локалізація

Система підтримує українську та англійську мови:

- `Resources/Locale/uk-UA/dnd/necromancy-servant.ftl`
- `Resources/Locale/en-US/dnd/necromancy-servant.ftl`

## HTN AI

Слуги використовують HTN (Hierarchical Task Network) для AI поведінки:

1. **Атака ворогів** (найвищий пріоритет)
2. **Захист області** (середній пріоритет)
3. **Слідування за майстром** (низький пріоритет)
4. **Очікування** (за замовчуванням)

## Обмеження

- Маги не можуть бути воскрешені як слуги
- Слуги існують обмежений час (5-7 хвилин)
- Максимальна відстань від майстра: 15 метрів
- Слуги повстають проти майстра після смерті майстра

## Технічні деталі

### Залежності
- `RejuvenateSystem` для воскресіння
- `MobStateSystem` для перевірки стану
- `NpcFactionSystem` для faction управління
- `HTNSystem` для AI поведінки
- `FollowerSystem` для слідування

### Події
- `ServantCommandEvent`: Команди для слуг
- `ComponentInit`: Ініціалізація слуги
- `MobStateChangedEvent`: Зміна стану слуги

## Майбутні покращення

1. Додаткові типи слуг
2. Покращені AI поведінки
3. Візуальні ефекти
4. Звукові ефекти
5. Балансування системи
