- type: entity
  abstract: true
  name: "газовик"
  description: "Гази, що видобуваються з газового гіганта внизу (вгорі?), витікають через цей масивний отвір."
  id: GasMinerBase
  placement:
    mode: SnapgridCenter
  components:
    - type: Clickable
    - type: InteractionOutline
    - type: Physics
      canCollide: false
    - type: Fixtures
    - type: Transform
      anchored: true
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: Metallic
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 100
          behaviors:
            - !type:DoActsBehavior
              acts: ["Destruction"]
    - type: Sprite
      sprite: Structures/Piping/Atmospherics/miners.rsi
      state: miner
    - type: AtmosDevice
    - type: GasMiner
      maxExternalPressure: 300
      spawnAmount: 200

- type: entity
  name: "Добувач газу O2"
  parent: GasMinerBase
  id: GasMinerOxygen
  suffix: Shuttle, 300kPa
  placement:
    mode: SnapgridCenter
  components:
    - type: GasMiner
      spawnGas: Oxygen

- type: entity
  name: "Добувач газу O2"
  parent: GasMinerOxygen
  id: GasMinerOxygenStation
  suffix: Station, 1000kPa
  components:
    - type: GasMiner
      maxExternalPressure: 1000

- type: entity
  name: "Добувач газу O2"
  parent: GasMinerOxygen
  id: GasMinerOxygenStationLarge
  suffix: Large Station, 4500kPa
  components:
    - type: GasMiner
      maxExternalPressure: 4500

- type: entity
  name: "Видобувач газу N2"
  parent: GasMinerBase
  id: GasMinerNitrogen
  suffix: Shuttle, 300kPa
  placement:
    mode: SnapgridCenter
  components:
    - type: GasMiner
      spawnGas: Nitrogen

- type: entity
  name: "Видобувач газу N2"
  parent: GasMinerNitrogen
  id: GasMinerNitrogenStation
  suffix: Station, 1000kPa
  components:
    - type: GasMiner
      maxExternalPressure: 1000

- type: entity
  name: "Видобувач газу N2"
  parent: GasMinerNitrogen
  id: GasMinerNitrogenStationLarge
  suffix: Large Station, 4500kPa
  components:
    - type: GasMiner
      maxExternalPressure: 4500

- type: entity
  name: "Добувач газу CO2"
  parent: GasMinerBase
  id: GasMinerCarbonDioxide
  placement:
    mode: SnapgridCenter
  components:
    - type: GasMiner
      spawnGas: CarbonDioxide

- type: entity
  name: "плазмовий газовий шахтар"
  parent: GasMinerBase
  id: GasMinerPlasma
  placement:
    mode: SnapgridCenter
  components:
    - type: GasMiner
      spawnGas: Plasma

- type: entity
  name: "видобувач тритієвого газу"
  parent: GasMinerBase
  id: GasMinerTritium
  placement:
    mode: SnapgridCenter
  components:
    - type: GasMiner
      spawnGas: Tritium

- type: entity
  name: "газодобувач водяної пари"
  parent: GasMinerBase
  id: GasMinerWaterVapor
  placement:
    mode: SnapgridCenter
  components:
    - type: GasMiner
      spawnGas: WaterVapor

- type: entity
  name: "видобувач аміачного газу"
  parent: GasMinerBase
  id: GasMinerAmmonia
  placement:
    mode: SnapgridCenter
  components:
    - type: GasMiner
      spawnGas: Ammonia

- type: entity
  name: "видобувач закису азоту"
  parent: GasMinerBase
  id: GasMinerNitrousOxide
  placement:
    mode: SnapgridCenter
  components:
    - type: GasMiner
      spawnGas: NitrousOxide
