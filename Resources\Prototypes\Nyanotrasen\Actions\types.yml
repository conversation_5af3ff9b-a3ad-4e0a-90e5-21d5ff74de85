- type: entity
  id: ActionEatMouse
  name: action-name-eat-mouse
  description: action-description-eat-mouse
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: Nyanotrasen/Icons/verbiconfangs.png
    event: !type:EatMouseActionEvent

- type: entity
  id: ActionHairball
  name: action-name-hairball
  description: action-description-hairball
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    charges: 1
    icon: { sprite: Nyanotrasen/Objects/Specific/Species/felinid.rsi, state: icon }
    useDelay: 30
    event: !type:HairballActionEvent