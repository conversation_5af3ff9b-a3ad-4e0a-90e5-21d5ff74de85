- type: entity
  name: "ко<PERSON><PERSON><PERSON>чна креветка"
  parent: [ SimpleMobBase, FlyingMobBase, MobCombat ]
  id: MobSpaceShrimp
  description: "Проклята креветка..."
  components:
  - type: MeleeWeapon
    soundHit:
        path: /Audio/Effects/pop.ogg
  - type: MovementSpeedModifier
    baseWalkSpeed : 6
    baseSprintSpeed : 6
  - type: Sprite
    sprite: DeltaV/Mobs/Animals/shrimp.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: shrimp
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 100
        mask:
        - FlyingMobMask
        layer:
        - FlyingMobLayer
  - type: Physics
  - type: DamageStateVisuals
    states:
      Alive:
        Base: shrimp
      Dead:
        Base: dead
  - type: Butcherable
    spawned:
    - id: FoodMeatCrab
      amount: 3
  - type: Bloodstream
    bloodMaxVolume: 100
    bloodReagent: BbqSauce # Australia reference
  - type: InteractionPopup
    successChance: 0.5
    interactSuccessString: petting-success-possum
    interactFailureString: petting-failure-possum
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/raccoon_chatter.ogg
  - type: Speech
    speechSounds: Slime
  - type: Puller
    needsHands: false
  - type: MindContainer
    showExamineInfo: true
  - type: NpcFactionMember
    factions:
    - Passive
  - type: Body
    prototype: Animal
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
