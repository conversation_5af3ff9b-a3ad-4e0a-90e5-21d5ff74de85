- type: entity
  abstract: true
  parent: MarkerBase
  id: BaseAntagSpawner
  name: "точка спавну для ролі привида"
  components:
  - type: GhostRole
    raffle:
      settings: default
  - type: GhostRoleAntagSpawner

- type: entity
  id: SpawnPointGhostRatKing
  name: "точка спавну для ролі привида"
  suffix: rat king
  parent: MarkerBase
  components:
  - type: GhostRole
    name: ghost-role-information-rat-king-name
    description: ghost-role-information-rat-king-description
    rules: ghost-role-information-rat-king-rules
    mindRoles:
    - MindRoleGhostRoleFreeAgent
    raffle:
      settings: default
  - type: GhostRoleMobSpawner
    prototype: MobRatKing
  - type: Sprite
    sprite: Markers/jobs.rsi
    layers:
      - state: green
      - sprite: Mobs/Animals/regalrat.rsi
        state: icon

- type: entity
  id: SpawnPointGhostRemilia
  name: "точка спавну для ролі привида"
  suffix: Remilia
  parent: MarkerBase
  components:
  - type: GhostRole
    name: ghost-role-information-remilia-name
    description: ghost-role-information-remilia-description
    rules: ghost-role-information-familiar-rules
    mindRoles:
    - MindRoleGhostRoleFamiliar
    raffle:
      settings: short
  - type: GhostRoleMobSpawner
    prototype: MobBatRemilia
  - type: Sprite
    sprite: Markers/jobs.rsi
    layers:
      - state: green
      - sprite: Mobs/Animals/bat.rsi
        state: bat

- type: entity
  id: SpawnPointGhostCerberus
  name: "точка спавну для ролі привида"
  suffix: cerberus
  parent: MarkerBase
  components:
  - type: GhostRole
    name: ghost-role-information-cerberus-name
    description: ghost-role-information-cerberus-description
    rules: ghost-role-information-familiar-rules
    mindRoles:
    - MindRoleGhostRoleFamiliar
    raffle:
      settings: default
  - type: GhostRoleMobSpawner
    prototype: MobCorgiCerberus
  - type: Sprite
    sprite: Markers/jobs.rsi
    layers:
      - state: green
      - sprite: Mobs/Pets/corgi.rsi
        state: narsian

- type: entity
  categories: [ HideSpawnMenu, Spawner ]
  id: SpawnPointGhostNukeOperative
  name: "точка спавну для ролі привида"
  suffix: nukeops
  parent: BaseAntagSpawner
  components:
  - type: GhostRole
    rules: ghost-role-information-rules-default-team-antagonist
    mindRoles:
    - MindRoleGhostRoleTeamAntagonist
    raffle:
      settings: default
  - type: GhostRoleMobSpawner
    prototype: MobHumanNukeOp
  - type: Sprite
    sprite: Markers/jobs.rsi
    layers:
      - state: green
      - sprite: Structures/Wallmounts/signs.rsi
        state: radiation

- type: entity
  categories: [ HideSpawnMenu, Spawner ]
  id: SpawnPointLoneNukeOperative
  name: "точка спавну для ролі привида"
  suffix: loneops
  parent: BaseAntagSpawner
  components:
  - type: GhostRole
    name: ghost-role-information-loneop-name
    description: ghost-role-information-loneop-description
    rules: ghost-role-information-loneop-rules
    requirements:
    - !type:CharacterOverallTimeRequirement
      min: 172800 # DeltaV - 48 hours
    - !type:CharacterDepartmentTimeRequirement # DeltaV - Security dept time requirement
      department: Security
      min: 36000 # DeltaV - 10 hours
    mindRoles:
    - MindRoleGhostRoleSoloAntagonist
  - type: Sprite
    sprite: Markers/jobs.rsi
    layers:
      - state: green
      - sprite: Structures/Wallmounts/signs.rsi
        state: radiation

- type: entity
  categories: [ HideSpawnMenu, Spawner ]
  parent: SpawnPointLoneNukeOperative
  id: SpawnPointNukeopsCommander
  components:
  - type: GhostRole
    name: roles-antag-nuclear-operative-commander-name
    description: roles-antag-nuclear-operative-commander-objective
    rules: ghost-role-information-rules-default-team-antagonist
    mindRoles:
    - MindRoleGhostRoleTeamAntagonist

- type: entity
  categories: [ HideSpawnMenu, Spawner ]
  parent: SpawnPointLoneNukeOperative
  id: SpawnPointNukeopsMedic
  components:
  - type: GhostRole
    name: roles-antag-nuclear-operative-agent-name
    description: roles-antag-nuclear-operative-agent-objective
    rules: ghost-role-information-rules-default-team-antagonist
    mindRoles:
    - MindRoleGhostRoleTeamAntagonist

- type: entity
  categories: [ HideSpawnMenu, Spawner ]
  parent: SpawnPointLoneNukeOperative
  id: SpawnPointNukeopsOperative
  components:
  - type: GhostRole
    name: roles-antag-nuclear-operative-name
    description: roles-antag-nuclear-operative-objective
    rules: ghost-role-information-rules-default-team-antagonist
    mindRoles:
    - MindRoleGhostRoleTeamAntagonist

- type: entity
  categories: [ HideSpawnMenu, Spawner ]
  parent: BaseAntagSpawner
  id: SpawnPointGhostDragon
  components:
  - type: GhostRole
    name: ghost-role-information-space-dragon-name
    description: ghost-role-information-space-dragon-description
    rules: ghost-role-information-space-dragon-rules
    mindRoles:
    - MindRoleGhostRoleTeamAntagonist
  - type: Sprite
    layers:
    - state: green
    - sprite: Mobs/Aliens/Carps/dragon.rsi
      state: alive

- type: entity
  parent: BaseAntagSpawner
  id: SpawnPointGhostSpaceNinja
  categories: [ HideSpawnMenu, Spawner ]
  name: "точка спавну для ролі привида"
  suffix: space ninja
  components:
  - type: GhostRole
    name: ghost-role-information-space-ninja-name
    description: ghost-role-information-space-ninja-description
    rules: ghost-role-information-antagonist-rules
    mindRoles:
    - MindRoleGhostRoleSoloAntagonist
    raffle:
      settings: default
  - type: Sprite
    sprite: Markers/jobs.rsi
    layers:
    - state: green
    - sprite: Objects/Weapons/Melee/energykatana.rsi
      state: icon

- type: entity
  id: SpawnPointGhostAlienLarva
  name: "точка спавну для ролі привида"
  suffix: alien larva
  parent: MarkerBase
  components:
  - type: GhostRole
    name: ghost-role-information-alien-larva-name
    description: ghost-role-information-alien-larva-description
    rules: ghost-role-information-xeno-rules
  - type: GhostRoleMobSpawner
    prototype: MobAlienLarvaGrowStageTwo
  - type: Sprite
    sprite: Markers/jobs.rsi
    layers:
    - state: green
    - sprite: Mobs/Aliens/Xenos/alien.rsi
      state: larva1
