using Content.Shared.CartridgeLoader.Cartridges;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.CartridgeLoader.Cartridges;

[GenerateTypedNameReferences]
public sealed partial class MailMetricUiFragment : BoxContainer
{

    private OpenedMailPercentGrade? _successGrade;

    public MailMetricUiFragment()
    {
        RobustXamlLoader.Load(this);

        // This my way of adding multiple classes to a XAML control.
        // Haha Batman I'm going to blow up Gotham City
        OpenedMailCount.StyleClasses.Add("Good");
        OpenedMailSpesos.StyleClasses.Add("Good");
        TamperedMailCount.StyleClasses.Add("Danger");
        TamperedMailSpesos.StyleClasses.Add("Danger");
        ExpiredMailCount.StyleClasses.Add("Danger");
        ExpiredMailSpesos.StyleClasses.Add("Danger");
        DamagedMailCount.StyleClasses.Add("Danger");
        DamagedMailSpesos.StyleClasses.Add("Danger");
        UnopenedMailCount.StyleClasses.Add("Danger");
    }

    public void UpdateState(MailMetricUiState state)
    {
        UpdateTextLabels(state);
        UpdateSuccessGrade(state);
    }

    public void UpdateTextLabels(MailMetricUiState state)
    {
        var stats = state.Metrics;

        OpenedMailCount.Text = stats.OpenedCount.ToString();
        OpenedMailSpesos.Text = stats.Earnings.ToString();
        TamperedMailCount.Text = stats.TamperedCount.ToString();
        TamperedMailSpesos.Text = stats.TamperedLosses.ToString();
        ExpiredMailCount.Text = stats.ExpiredCount.ToString();
        ExpiredMailSpesos.Text = stats.ExpiredLosses.ToString();
        DamagedMailCount.Text = stats.DamagedCount.ToString();
        DamagedMailSpesos.Text = stats.DamagedLosses.ToString();
        UnopenedMailCount.Text = state.UnopenedMailCount.ToString();
        TotalMailCount.Text = state.TotalMail.ToString();
        TotalMailSpesos.Text = stats.TotalIncome.ToString();
        SuccessRateCounts.Text = Loc.GetString("mail-metrics-progress",
            ("opened", stats.OpenedCount),
            ("total", state.TotalMail));
        SuccessRatePercent.Text = Loc.GetString("mail-metrics-progress-percent",
            ("successRate", state.SuccessRate));
    }

    public void UpdateSuccessGrade(MailMetricUiState state)
    {
        var previousGrade = _successGrade;
        _successGrade = GetSuccessRateGrade(state.SuccessRate);

        // No need to update if they're the same
        if (previousGrade == _successGrade)
            return;

        var previousGradeClass = GetClassForGrade(previousGrade);
        if (previousGradeClass != string.Empty)
        {
            SuccessRatePercent.StyleClasses.Remove(previousGradeClass);
        }

        SuccessRatePercent.StyleClasses.Add(GetClassForGrade(_successGrade));
    }

    private static OpenedMailPercentGrade GetSuccessRateGrade(double successRate)
    {
        return successRate switch
        {
            > 75 => OpenedMailPercentGrade.Good,
            > 50 => OpenedMailPercentGrade.Average,
            _ => OpenedMailPercentGrade.Bad,
        };
    }

    private string GetClassForGrade(OpenedMailPercentGrade? grade)
    {
        return grade switch
        {
            OpenedMailPercentGrade.Good => "Good",
            OpenedMailPercentGrade.Average => "Caution",
            OpenedMailPercentGrade.Bad => "Danger",
            _ => string.Empty,
        };
    }
}

enum OpenedMailPercentGrade
{
    Good,
    Average,
    Bad
}
