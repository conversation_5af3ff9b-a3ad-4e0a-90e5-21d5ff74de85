- type: entity
  id: CrateServiceJanitorialSupplies
  parent: CratePlastic
  name: "ящик з інвентарем прибиральника"
  description: "Дайте відсіч бруду та жиру за допомогою Засобів для Прибирання від Nanotrasen(tm)! Містить три відра, попереджувальні знаки та чистячі гранати. Також є одна швабра, мітла, аерозольний очищувач, ганчірка та мішок для сміття."
  components:
  - type: StorageFill
    contents:
      - id: MopItem
      - id: MopBucket
      - id: Bucket
        amount: 3
      - id: WetFloorSign
        amount: 2
      - id: Soap
      - id: SprayBottleSpaceCleaner
        amount: 2
      - id: TrashBag
        amount: 2
      - id: Plunger
        amount: 2
      - id: BoxCleanerGrenades

- type: entity
  id: CrateServiceReplacementLights
  parent: CrateGenericSteel
  name: "ящик з запасними лампами"
  description: "Нехай світло Ефіру осяє цю станцію! Або, принаймні, світло сорока двох ламп і двадцяти однієї лампочки."
  components:
  - type: StorageFill
    contents:
      - id: BoxLighttube
      - id: BoxLightbulb

- type: entity
  id: CrateMousetrapBoxes
  parent: CrateGenericSteel
  name: "ящик з мишоловками"
  description: "Мишоловки, для випадків, коли увесь персонал переслідує ціла орда щурів. Використовуйте економно... або ні."
  components:
    - type: StorageFill
      contents:
        - id: BoxMousetrap

- type: entity
  id: CrateServiceSmokeables
  parent: CrateGenericSteel
  name: "ящик з тютюновими виробами"
  description: "Набридла швидка смерть на станції? Замовляйте цей ящик і викурюйте свій шлях до кашляючої смерті!"
  components:
  - type: StorageFill
    contents:
      - id: CigCartonGreen
        prob: 0.50
        orGroup: CigCarton1
      - id: CigCartonRed
        orGroup: CigCarton1
      - id: CigCartonBlue
        prob: 0.50
        orGroup: CigCarton2
      - id: CigCartonBlack
        orGroup: CigCarton2
      - id: CigarGoldCase
        prob: 0.05
        orGroup: Cigars
      - id: CigarCase
        orGroup: Cigars
      - id: Matchbox
        amount: 2

- type: entity
  id: CrateServiceTheatre
  parent: CrateGenericSteel
  name: "ящик для театральних вистав"
  description: "Містить плащ молі, перукарські ножиці, форму покоївки, клоунські та мімічні атрибути, а також інше для виступу."
  components:
  - type: StorageFill
    contents:
    - id: ClothingUniformJumpskirtPerformer
    - id: ClothingShoesBootsPerformer
    - id: ClothingOuterSuitMonkey
    - id: ClothingHeadHatAnimalMonkey
    - id: ClothingNeckCloakMoth
    - id: ClothingMaskClown
    - id: ClothingMaskMime
    - id: ClothingShoesClown
    - id: ClothingUniformJumpskirtJanimaid
    - id: ClothingNeckCloakVoid
    - id: RevolverCapGun
    - id: BarberScissors
    - id: ClothingUniformJumpskirtOldDress
    - id: BikeHorn
    - id: ClownRecorder
    - id: ClothingBeltSuspenders

- type: entity
  id: CrateServiceCustomSmokable
  parent: CrateGenericSteel
  name: "ящик курильних сумішей \"зроби сам"
  description: "Хочете трохи покреативити з тим, що ви використовуєте, щоб знищити свої легені? Тоді цей ящик для вас! Тут є все необхідне для того, щоб крутити сигарети власноруч."
  components:
  - type: StorageFill
    contents:
    - id: PackPaperRolling
    - id: CigaretteFilter
      amount: 8
    - id: GroundTobacco
      amount: 4
    - id: SmokingPipe
    - id: Matchbox

- type: entity
  id: CrateServiceBureaucracy
  parent: CrateGenericSteel
  name: "ящик з бюрократією"
  description: "Кілька стосів паперу, кілька ручок та офісна іграшка. Що ще треба для щастя?"
  components:
  - type: StorageFill
    contents:
    - id: Paper
      amount: 15
    - id: Pen
      amount: 2
    - id: BoxFolderClipboard
      amount: 2
    - id: HandLabeler
    - id: BoxFolderBlue
    - id: BoxFolderRed
    - id: BoxFolderYellow
    - id: NewtonCradle

- type: entity
  id: CrateServicePersonnel
  parent: CrateCommandSecure
  name: "ящик для персоналу"
  description: "Містить коробки з пустими посвідченнями особи та КПК."
  components:
  - type: StorageFill
    contents:
    - id: BoxPDA
    - id: BoxID

- type: entity
  id: CrateServiceBooks
  parent: CrateGenericSteel
  name: "ящик з книгами"
  description: "Містить 10 порожніх книг випадкового вигляду."
  components:
    - type: StorageFill
      contents:
        - id: BookRandom
          amount: 10

- type: entity
  id: CrateServiceGuidebooks
  parent: CrateGenericSteel
  name: "ящик з керівництвами"
  description: "Містить керівництва."
  components:
    - type: StorageFill
      contents:
      - id: BookSpaceEncyclopedia
      - id: BookTheBookOfControl
      - id: BookBartendersManual
      - id: BookHowToCookForFortySpaceman
      - id: BookLeafLoversSecret
      - id: BookEngineersHandbook
      - id: BookScientistsGuidebook
      - id: BookSecurity
      - id: BookHowToKeepStationClean
      - id: BookHowToRockAndStone
      - id: BookMedicalReferenceBook
      - id: BookHowToSurvive
      - id: BookChemicalCompendium

- type: entity
  id: CrateServiceSodaDispenser
  parent: CrateGenericSteel
  name: "ящик з заправкою дозатора газованої води"
  description: "Містить заправки для диспенсеру безалкогольних напоїв."
  components:
    - type: StorageFill
      contents:
      - id: DrinkCoffeeJug
      - id: DrinkColaBottleFull
      - id: DrinkCreamCartonXL
      - id: DrinkDrGibbJug
      - id: DrinkEnergyDrinkJug
      - id: DrinkGreenTeaJug
      - id: DrinkIceJug
      - id: DrinkJuiceLimeCartonXL
      - id: DrinkJuiceOrangeCartonXL
      - id: DrinkLemonLimeJug
      - id: DrinkRootBeerJug
      - id: DrinkSodaWaterBottleFull
      - id: DrinkSpaceMountainWindBottleFull
      - id: DrinkSpaceUpBottleFull
      - id: DrinkSugarJug
      - id: DrinkTeaJug
      - id: DrinkTonicWaterBottleFull
      - id: DrinkWaterMelonJuiceJug

- type: entity
  id: CrateServiceBoozeDispenser
  parent: CrateGenericSteel
  name: "ящик з заправкою диспенсера напоїв"
  description: "Містить заправки для диспенсеру алкогольних напоїв."
  components:
    - type: StorageFill
      contents:
      - id: DrinkAleBottleFullGrowler
      - id: DrinkBeerGrowler
      - id: DrinkCoffeeLiqueurBottleFull
      - id: DrinkCognacBottleFull
      - id: DrinkGinBottleFull
      - id: DrinkMeadJug
      - id: DrinkRumBottleFull
      - id: DrinkTequilaBottleFull
      - id: DrinkVermouthBottleFull
      - id: DrinkVodkaBottleFull
      - id: DrinkWhiskeyBottleFull
      - id: DrinkWineBottleFull

- type: entity
  id: CrateServiceBox
  parent: CratePlastic
  name: "ящик з коробками"
  description: "Містить 6 порожніх коробок для універсального використання."
  components:
    - type: StorageFill
      contents:
        - id: BoxCardboard
          amount: 6

- type: entity
  id: CrateJanitorBiosuit
  parent: CratePlastic
  name: "ящик з біозахисними костюмами прибиральника"
  description: "Містить 2 біозахисні костюми, щоб жодна хвороба не відволікала вас від прибирання."
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterBioJanitor
        amount: 2
      - id: ClothingHeadHatHoodBioJanitor
        amount: 2
      - id: ClothingMaskSterile
        amount: 2

- type: entity
  id: CrateTrashCartFilled
  suffix: Filled
  parent: CrateTrashCart
  components:
    - type: StorageFill
      contents:
          # Creatures
        - id: MobCockroach
          prob: 0.05
        - id: MobMothroach
          prob: 0.03
        - id: MobMouse
          prob: 0.05
          # Food Packaging
        - id: FoodPacketBoritosTrash
          prob: 0.1
        - id: FoodPacketCheesieTrash
          prob: 0.1
        - id: FoodPacketChipsTrash
          prob: 0.1
        - id: FoodPacketSusTrash
          prob: 0.1
        - id: FoodPacketSyndiTrash
          prob: 0.1
        - id: FoodPacketChowMeinTrash
          prob: 0.1
        - id: FoodPacketDanDanTrash
          prob: 0.1
        - id: FoodPacketMRETrash
          prob: 0.1
        - id: FoodPacketPistachioTrash
          prob: 0.1
        - id: FoodPacketSemkiTrash
          prob: 0.1
        - id: FoodPacketRaisinsTrash
          prob: 0.1
          # Cans
        - id: FoodTinBeansTrash
          prob: 0.15
        - id: FoodTinPeachesTrash
          prob: 0.15
        - id: FoodTinMRETrash
          prob: 0.15
          # Cigarette Stuff
        - id: Ash
          prob: 0.15
        - id: CigaretteSpent
          prob: 0.15
          # Bacteria
        - id: FoodBreadMoldySlice
          prob: 0.15
        - id: FoodPizzaMoldySlice
          prob: 0.15
          # Botanical Waste
        - id: TrashBananaPeel
          prob: 0.15
        - id: FoodCornTrash
          prob: 0.15
          # Misc
        - id: DrinkGlass
          prob: 0.15
        - id: BrokenBottle
          prob: 0.15
        - id: LightTubeBroken
          prob: 0.15
        - id: LightBulbBroken
          prob: 0.15
        - id: MobMouseDead
          prob: 0.1
        - id: Syringe
          prob: 0.1
        - id: ShardGlassPlasma
          prob: 0.1

- type: entity
  id: CrateJanitorExplosive
  parent: ClosetJanitorBomb
  name: "ящик з бомбозахисним костюмом прибиральника"
  description: "Поставляє саперний костюм для очищення будь-яких вибухонебезпечних сполук, придбайте його вже сьогодні!"
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterSuitJanitorBomb
        amount: 1
      - id: ClothingHeadHelmetJanitorBombSuit
        amount: 1
