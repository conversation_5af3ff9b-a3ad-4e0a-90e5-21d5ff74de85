- type: entity
  id: StasisBed
  name: "стазис-ліжко"
  parent: [ BaseMachinePowered, ConstructibleMachine ]
  description: "Лі<PERSON><PERSON>о, яке значно уповільнює метаболізм пацієнта і запобігає розпаду тіла, що дає більше часу для проведення належного лікування для стабілізації стану."
  components:
  - type: StasisBed
  - type: AntiRotOnBuckle
  - type: HealOnBuckle
    damage:
      types:
        Poison: -0.001
        Blunt: -0.001
  - type: Sprite
    sprite: Structures/Machines/stasis_bed.rsi
    noRot: true
    layers:
    - state: icon
    - state: unlit
      shader: unshaded
      map: ["enum.StasisBedVisualLayers.IsOn"]
  - type: StasisBedVisuals
  - type: Appearance
  - type: ApcPowerReceiver
    powerLoad: 1000
  - type: ExtensionCableReceiver
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 75
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 2
  - type: Strap
    position: Down
    rotation: -90
  - type: Machine
    board: StasisBedMachineCircuitboard
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.05"
        density: 190
        mask:
        - LowImpassable
  - type: GuideHelp
    guides:
    - Medical Doctor
