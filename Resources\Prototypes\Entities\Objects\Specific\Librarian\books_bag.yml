- type: entity
  name: "сумка для книг"
  id: BooksBag
  parent: BaseStorageItem
  description: "Вишукана сумка для власної бібліотеки"
  components:
    - type: Sprite
      sprite: Objects/Specific/Library/Equipment/books_bag.rsi
      state: icon
    - type: Clothing
      sprite: Objects/Specific/Library/Equipment/books_bag.rsi
      quickEquip: false
      slots:
        - belt
    - type: Item
      size: Ginormous
    - type: Storage
      grid:
      - 0,0,7,3
      quickInsert: true
      areaInsert: true
      whitelist:
        tags:
          - Book
          - Document
    - type: Dumpable
