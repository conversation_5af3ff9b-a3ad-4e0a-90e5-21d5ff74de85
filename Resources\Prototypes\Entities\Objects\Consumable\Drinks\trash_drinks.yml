# These can still be used as containers
- type: entity
  name: "основа порожня пляшка"
  id: DrinkBottleBaseEmpty
  parent: BaseItem
  abstract: true
  description: "Порожня пляшка."
  suffix: Empty
  components:
  - type: Sprite
    state: icon
  - type: Item
    size: Normal
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 100
  - type: SolutionTransfer
    canChangeTransferAmount: true
    maxTransferAmount: 5
  - type: Drink
  - type: MeleeWeapon
    soundNoDamage:
      path: "/Audio/Effects/Fluids/splat.ogg"
    damage:
      types:
        Blunt: 0
  - type: MixableSolution
    solution: drink
  - type: Spillable
    solution: drink
  - type: FitsInDispenser
    solution: drink
  - type: DrawableSolution
    solution: drink
  - type: RefillableSolution
    solution: drink
  - type: DrainableSolution
    solution: drink
  - type: UserInterface
    interfaces:
      enum.TransferAmountUiKey.Key:
        type: TransferAmountBoundUserInterface
  - type: DamageOnLand
    damage:
      types:
        Blunt: 5
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 4
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 5
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:SpillBehavior { }
      - !type:SpawnEntitiesBehavior
        spawn:
          BrokenBottle:
            min: 1
            max: 1
        transferForensics: true
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Tag
    tags:
    - Trash
  - type: PhysicalComposition
    materialComposition:
      Glass: 100
  - type: SpaceGarbage

- type: entity
  name: "основа порожня пляшка"
  id: DrinkBottleBaseSmallEmpty
  parent: DrinkBottleBaseEmpty
  abstract: true
  components:
  - type: Item
    size: Small
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 50

- type: entity
  name: "основа порожня коробка"
  id: DrinkCartonBaseEmpty
  parent: BaseItem
  abstract: true
  description: "Порожня коробка."
  suffix: Empty
  components:
  - type: Sprite
    state: icon
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 50
  - type: MixableSolution
    solution: drink
  - type: SolutionTransfer
    canChangeTransferAmount: true
    maxTransferAmount: 5
  - type: Drink
  - type: Spillable
    solution: drink
  - type: FitsInDispenser
    solution: drink
  - type: DrawableSolution
    solution: drink
  - type: RefillableSolution
    solution: drink
  - type: DrainableSolution
    solution: drink
  - type: UserInterface
    interfaces:
      enum.TransferAmountUiKey.Key:
        type: TransferAmountBoundUserInterface
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 20
      behaviors:
      - !type:SpillBehavior { }
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Tag
    tags:
    - Trash
  - type: PhysicalComposition
    materialComposition:
      Cardboard: 20
  - type: SpaceGarbage
  - type: EmitSoundOnPickup
    sound: /Audio/SimpleStation14/Items/Handling/drinkglass_pickup.ogg
  - type: EmitSoundOnDrop
    sound: /Audio/SimpleStation14/Items/Handling/drinkglass_drop.ogg
  - type: EmitSoundOnLand
    sound: /Audio/SimpleStation14/Items/Handling/drinkglass_drop.ogg

- type: entity
  name: "основа порожня пляшка"
  id: DrinkCartonBaseLargeEmpty
  parent: DrinkCartonBaseEmpty
  abstract: true
  components:
  - type: Item
    size: Normal
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 100

# Containers
- type: entity
  name: "Пляшка Jailbreaker Verte"
  parent: DrinkBottleBaseEmpty
  id: DrinkBottleAbsinthe
  components:
  - type: Sprite
    sprite: Objects/Consumable/TrashDrinks/absinthebottle_empty.rsi


- type: entity
  name: "пляшка алкоголю"
  parent: DrinkBottleBaseEmpty
  id: DrinkBottleAlcoClear
  components:
  - type: Sprite
    sprite: Objects/Consumable/TrashDrinks/alco-clear.rsi


- type: entity
  name: "пляшка елю"
  parent: DrinkBottleBaseSmallEmpty
  id: DrinkBottleAle
  components:
  - type: Sprite
    sprite: Objects/Consumable/TrashDrinks/alebottle_empty.rsi


- type: entity
  name: "пивна пляшка"
  parent: DrinkBottleBaseSmallEmpty
  id: DrinkBottleBeer
  components:
  - type: Sprite
    sprite: Objects/Consumable/TrashDrinks/beer_empty.rsi


- type: entity
  name: "пляшка коньяку"
  parent: DrinkBottleBaseEmpty
  id: DrinkBottleCognac
  components:
  - type: Sprite
    sprite: Objects/Consumable/TrashDrinks/cognacbottle_empty.rsi


- type: entity
  name: "Пляшка джину Griffeater"
  parent: DrinkBottleBaseEmpty
  id: DrinkBottleGin
  components:
  - type: Sprite
    sprite: Objects/Consumable/TrashDrinks/ginbottle_empty.rsi


- type: entity
  name: "пляшка з позолотою"
  parent: DrinkBottleBaseEmpty
  id: DrinkBottleGildlager
  components:
  - type: Sprite
    sprite: Objects/Consumable/TrashDrinks/gildlagerbottle_empty.rsi


- type: entity
  name: "пляшка кавового лікеру"
  parent: DrinkBottleBaseEmpty
  id: DrinkBottleCoffeeLiqueur
  components:
  - type: Sprite
    sprite: Objects/Consumable/TrashDrinks/coffeeliqueurbottle_empty.rsi


- type: entity
  name: "пляшка nt cahors"
  parent: DrinkBottleBaseEmpty
  id: DrinkBottleNTCahors
  components:
  - type: Sprite
    sprite: Objects/Consumable/TrashDrinks/ntcahors_empty.rsi


- type: entity
  name: "пляшка-меценат"
  parent: DrinkBottleBaseEmpty
  id: DrinkBottlePatron
  components:
  - type: Sprite
    sprite: Objects/Consumable/TrashDrinks/patronbottle_empty.rsi


- type: entity
  name: "пляшка отруйного вина"
  parent: DrinkBottleBaseEmpty
  id: DrinkBottlePoisonWine
  components:
  - type: Sprite
    sprite: Objects/Consumable/TrashDrinks/pwinebottle_empty.rsi


- type: entity
  name: "пляшка рому"
  parent: DrinkBottleBaseEmpty
  id: DrinkBottleRum
  components:
  - type: Sprite
    sprite: Objects/Consumable/TrashDrinks/rumbottle_empty.rsi


- type: entity
  name: "пляшка текіли"
  parent: DrinkBottleBaseEmpty
  id: DrinkBottleTequila
  components:
  - type: Sprite
    sprite: Objects/Consumable/TrashDrinks/tequillabottle_empty.rsi


- type: entity
  name: "пляшка вермуту"
  parent: DrinkBottleBaseEmpty
  id: DrinkBottleVermouth
  components:
  - type: Sprite
    sprite: Objects/Consumable/TrashDrinks/vermouthbottle_empty.rsi


- type: entity
  name: "пляшка горілки"
  parent: DrinkBottleBaseEmpty
  id: DrinkBottleVodka
  components:
  - type: Sprite
    sprite: Objects/Consumable/TrashDrinks/vodkabottle_empty.rsi


- type: entity
  name: "пляшка віскі"
  parent: DrinkBottleBaseEmpty
  id: DrinkBottleWhiskey
  components:
  - type: Sprite
    sprite: Objects/Consumable/TrashDrinks/whiskeybottle_empty.rsi


- type: entity
  name: "пляшка вина"
  parent: DrinkBottleBaseEmpty
  id: DrinkBottleWine
  components:
  - type: Sprite
    sprite: Objects/Consumable/TrashDrinks/winebottle_empty.rsi

- type: entity
  name: "упаковка соку лайма"
  parent: DrinkCartonBaseEmpty
  id: DrinkCartonLime
  components:
  - type: Sprite
    sprite: Objects/Consumable/Drinks/limejuice.rsi


- type: entity
  name: "упаковка апельсинового соку"
  parent: DrinkCartonBaseEmpty
  id: DrinkCartonOrange
  components:
  - type: Sprite
    sprite: Objects/Consumable/Drinks/orangejuice.rsi


- type: entity
  name: "упаковка томатного соку"
  parent: DrinkCartonBaseEmpty
  id: DrinkCartonTomato
  components:
  - type: Sprite
    sprite: Objects/Consumable/Drinks/tomatojuice.rsi


- type: entity
  name: "упаковка молочних вершків"
  parent: DrinkCartonBaseEmpty
  id: DrinkCartonCream
  components:
  - type: Sprite
    sprite: Objects/Consumable/Drinks/cream.rsi


- type: entity
  name: "пакет молока"
  parent: DrinkCartonBaseLargeEmpty
  id: DrinkCartonMilk
  components:
  - type: Sprite
    sprite: Objects/Consumable/Drinks/milk.rsi
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 100

- type: entity
  name: "упаковка соєвого молока"
  parent: DrinkCartonBaseLargeEmpty
  id: DrinkCartonSoyMilk
  components:
  - type: Sprite
    sprite: Objects/Consumable/Drinks/soymilk.rsi

- type: entity
  name: "пакет вівсяного молока"
  parent: DrinkCartonBaseLargeEmpty
  id: DrinkCartonOatMilk
  components:
  - type: Sprite
    sprite: Objects/Consumable/Drinks/oatmilk.rsi
