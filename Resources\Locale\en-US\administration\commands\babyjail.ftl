cmd-babyjail-desc = Toggles the baby jail, which enables stricter restrictions on who's allowed to join the server.
cmd-babyjail-help = Usage: babyjail
babyjail-command-enabled = Baby jail has been enabled.
babyjail-command-disabled = Baby jail has been disabled.

cmd-babyjail_show_reason-desc = Toggles whether or not to show connecting clients the reason why the baby jail blocked them from joining.
cmd-babyjail_show_reason-help = Usage: babyjail_show_reason
babyjail-command-show-reason-enabled = The baby jail will now show a reason to users it blocks from connecting.
babyjail-command-show-reason-disabled = The baby jail will no longer show a reason to users it blocks from connecting.

cmd-babyjail_max_account_age-desc = Gets or sets the maximum account age in minutes that an account can have to be allowed to connect with the baby jail enabled.
cmd-babyjail_max_account_age-help = Usage: babyjail_max_account_age <hours>
babyjail-command-max-account-age-is = The maximum account age for the baby jail is {$hours} hours.
babyjail-command-max-account-age-set = Set the maximum account age for the baby jail to {$hours} hours.

cmd-babyjail_max_overall_hours-desc = Gets or sets the maximum overall playtime in minutes that an account can have to be allowed to connect with the baby jail enabled.
cmd-babyjail_max_overall_hours-help = Usage: babyjail_max_overall_hours <hours>
babyjail-command-max-overall-hours-is = The maximum overall playtime for the baby jail is {$hours} hours.
babyjail-command-max-overall-hours-set = Set the maximum overall playtime for the baby jail to {$hours} hours.
