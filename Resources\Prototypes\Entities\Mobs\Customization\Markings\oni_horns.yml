- type: marking
  id: OniHornTallCurved
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns48x48.rsi
    state: tall_curved
- type: marking
  id: OniHornTallCurved3Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns48x48.rsi
    state: tall_curved_3tone_1
  - sprite: Mobs/Customization/Oni/oni_horns48x48.rsi
    state: tall_curved_3tone_2
  - sprite: Mobs/Customization/Oni/oni_horns48x48.rsi
    state: tall_curved_3tone_3
- type: marking
  id: OniHornTallBull
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns48x48.rsi
    state: tall_bull
- type: marking
  id: OniHornTallBull3Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns48x48.rsi
    state: tall_bull_3tone_1
  - sprite: Mobs/Customization/Oni/oni_horns48x48.rsi
    state: tall_bull_3tone_2
  - sprite: Mobs/Customization/Oni/oni_horns48x48.rsi
    state: tall_bull_3tone_3
- type: marking
  id: OniHornCrowned
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: crowned

- type: marking
  id: OniHornSerket
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: serket

- type: marking
  id: OniHornSerket3Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: serket_3tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: serket_3tone_2
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: serket_3tone_3

- type: marking
  id: OniHornPisces
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: pisces

- type: marking
  id: OniHornPisces2Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: pisces_2tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: pisces_2tone_2

- type: marking
  id: OniHornPisces3Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: pisces_3tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: pisces_3tone_2
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: pisces_3tone_3

- type: marking
  id: OniHornVirgo
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: virgo

- type: marking
  id: OniHornVirgo3Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: virgo_3tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: virgo_3tone_2
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: virgo_3tone_3

- type: marking
  id: OniHornSagittarius
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: sagittarius

- type: marking
  id: OniHornSagittarius3Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: sagittarius_3tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: sagittarius_3tone_2
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: sagittarius_3tone_3

- type: marking
  id: OniHornVantas
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: vantas

- type: marking
  id: OniHornVantas3Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: vantas_3tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: vantas_3tone_2
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: vantas_3tone_3

- type: marking
  id: OniHornMakara
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns48x48.rsi
    state: makara

- type: marking
  id: OniHornMakara2Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns48x48.rsi
    state: makara_2tone_1
  - sprite: Mobs/Customization/Oni/oni_horns48x48.rsi
    state: makara_2tone_2

- type: marking
  id: OniHornMakara3Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns48x48.rsi
    state: makara_3tone_1
  - sprite: Mobs/Customization/Oni/oni_horns48x48.rsi
    state: makara_3tone_2
  - sprite: Mobs/Customization/Oni/oni_horns48x48.rsi
    state: makara_3tone_3

- type: marking
  id: OniHornNepeta
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: nepeta

- type: marking
  id: OniHornNepeta3Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: nepeta_3tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: nepeta_3tone_2
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: nepeta_3tone_3

- type: marking
  id: OniHornTaurus
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: taurus

- type: marking
  id: OniHornTaurus2Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: taurus_2tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: taurus_2tone_2

- type: marking
  id: OniHornTaurus3Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: taurus_3tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: taurus_3tone_2
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: taurus_3tone_3

- type: marking
  id: OniHornAries
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: aries

- type: marking
  id: OniHornAries3Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: aries_3tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: aries_3tone_2
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: aries_3tone_3

- type: marking
  id: OniHornTavris
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: tavris

- type: marking
  id: OniHornTavris3Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: tavris_3tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: tavris_3tone_2
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: tavris_3tone_3

- type: marking
  id: OniHornInclined
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: inclined

- type: marking
  id: OniHornInclined3Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: inclined_3tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: inclined_3tone_2
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: inclined_3tone_3

- type: marking
  id: OniHornWavy
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: wavy

- type: marking
  id: OniHornWavy2Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: wavy_2tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: wavy_2tone_2

- type: marking
  id: OniHornWavy3Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: wavy_3tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: wavy_3tone_2
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: wavy_3tone_3

- type: marking
  id: OniHornAntlers
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: antlers_2tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: antlers_2tone_2

- type: marking
  id: OniHornAntlers3Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: antlers_3tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: antlers_3tone_2
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: antlers_3tone_3

- type: marking
  id: OniHornUnicorn
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: unicorn

- type: marking
  id: OniHornErebia
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: erebia

- type: marking
  id: OniHornErebia3Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: erebia_3tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: erebia_3tone_2
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: erebia_3tone_3

- type: marking
  id: OniHornErebiaRings
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: erebia
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: erebia_rings

- type: marking
  id: OniHornDoubleThick
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: double_thick

- type: marking
  id: OniHornDoubleThick2Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: double_thick_2tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: double_thick_2tone_2

- type: marking
  id: OniHornDoubleThick3Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: double_thick_3tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: double_thick_3tone_2
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: double_thick_3tone_3

- type: marking
  id: OniHornDoubleCurved3Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: double_curved_3tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: double_curved_3tone_2
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: double_curved_3tone_3

- type: marking
  id: OniHornDoubleCurvedOutwards3Tone
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: double_curved_outwards_3tone_1
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: double_curved_outwards_3tone_2
  - sprite: Mobs/Customization/Oni/oni_horns.rsi
    state: double_curved_outwards_3tone_3
