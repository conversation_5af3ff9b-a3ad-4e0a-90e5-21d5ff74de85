- type: entity
  abstract: true
  parent: BaseItem
  id: PartBase
  components:
  - type: Sprite
    sprite: Objects/Materials/parts.rsi
    state: rods
  - type: Item
    sprite: Objects/Materials/parts.rsi
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: FlimsyMetallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]

- type: entity
  parent: PartBase
  id: PartRodMetal
  name: "металевий стрижень"
  suffix: Full
  components:
  - type: PhysicalComposition
    materialComposition:
      Steel: 50 #Half of a regular steel sheet to reflect the crafting recipe
  - type: Stack
    stackType: MetalRod
    baseLayer: base
    layerStates:
    - rods
    - rods_2
    - rods_3
    - rods_4
    - rods_5
  - type: Sprite
    state: rods_5
    layers:
    - state: rods_5
      map: ["base"]
  - type: Item
    size: Normal
#    heldPrefix: rods
  - type: Construction
    graph: MetalRod
    node: MetalRod
  - type: Appearance
  - type: FloorTile
    outputs:
    - Lattice
    - FloorReinforced
  - type: StaticPrice
    price: 0
  - type: StackPrice
    price: 5
  - type: Extractable
    grindableSolutionName: rod
  - type: SolutionContainerManager
    solutions:
      rod:
        reagents:
        - ReagentId: Iron
          Quantity: 4.5
        - ReagentId: Carbon
          Quantity: 0.5
  - type: UserInterface
    interfaces:
      enum.RadialSelectorUiKey.Key:
        type: RadialSelectorMenuBUI
  - type: ActivatableUI
    key: enum.RadialSelectorUiKey.Key
    inHandsOnly: true
    requireActiveHand: false
  - type: ShortConstruction
    entries:
    - prototype: Grille
    - prototype: GrilleDiagonal
    - category:
        name: Railings
        icon:
          sprite: Structures/Walls/railing.rsi
          state: side
        entries:
        - prototype: Railing
        - prototype: RailingCorner
        - prototype: RailingRound
        - prototype: RailingCornerSmall
    - category:
        name: Glass
        icon:
          sprite: Objects/Materials/Sheets/glass.rsi
          state: rglass_3
        entries:
        - prototype: SheetRPGlass
        - prototype: SheetRPGlass0
        - prototype: SheetRUGlass
        - prototype: SheetRUGlass0
        - prototype: SheetRGlass

- type: entity
  parent: PartRodMetal
  id: PartRodMetal10
  name: "металевий стрижень"
  suffix: 10
  components:
  - type: Tag
    tags:
    - RodMetal1
  - type: Sprite
    state: rods
  - type: Stack
    count: 10

- type: entity
  parent: PartRodMetal
  id: PartRodMetal1
  name: "металевий стрижень"
  suffix: Single
  components:
  - type: Tag
    tags:
    - RodMetal1
  - type: Sprite
    state: rods
  - type: Stack
    count: 1

- type: entity
  parent: PartRodMetal
  id: PartRodMetalLingering0
  suffix: Lingering, 0
  components:
  - type: Stack
    lingering: true
    count: 0

- type: entity
  parent: FloorTileItemSteel
  id: FloorTileItemSteelLingering0
  suffix: Lingering, 0
  components:
  - type: Stack
    lingering: true
    count: 0
