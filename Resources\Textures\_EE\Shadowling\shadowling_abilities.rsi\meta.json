{"version": 1, "license": "CC-BY-SA-3.0", "copyright": "https://github.com/tgstation/tgstation/commit/c9338f72126114446dfbff206b458b92a324fbe3. Nox Imperii by Luminal", "size": {"x": 32, "y": 32}, "states": [{"name": "engage_hatch"}, {"name": "enthrall"}, {"name": "glare"}, {"name": "veil"}, {"name": "rehatch"}, {"name": "shadow_walk"}, {"name": "icy_veins"}, {"name": "destroy_engines"}, {"name": "collective_mind"}, {"name": "blindness_smoke"}, {"name": "null_charge"}, {"name": "sonic_screech"}, {"name": "emp_enthrall"}, {"name": "nox_imperii"}, {"name": "black_recuperation"}, {"name": "annihilate"}, {"name": "lightning_storm"}, {"name": "broadcast"}, {"name": "ascendance"}]}