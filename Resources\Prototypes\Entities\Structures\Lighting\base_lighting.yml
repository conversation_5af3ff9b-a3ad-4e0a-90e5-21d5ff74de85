#==Suffix Guide==
#Always Powered - light doesn't need power to give off life
#Empty - on map init, light spawns without bulb
#Blue/Sodium/etc. - the color of the light

#Basic lights
- type: entity
  id: AlwaysPoweredWallLight
  name: "світло"
  description: "Постійно ввімкнене світло."
  suffix: Always powered
  components:
  - type: MeleeSound
    soundGroups:
      Brute:
        collection: GlassSmash
  - type: Transform
    anchored: true
  - type: Clickable
  - type: InteractionOutline
  - type: Construction
    graph: LightFixture
    node: tubeLight
  - type: Sprite
    sprite: Structures/Wallmounts/Lighting/light_tube.rsi
    drawdepth: WallMountedItems
    layers:
    - map: ["enum.PoweredLightLayers.Base"]
      state: base
    - map: ["enum.PoweredLightLayers.Glow"]
      state: glow
      shader: unshaded
    state: base
  - type: EtherealLight
  - type: PointLight
    color: "#FFE4CE" # 5000K color temp
    energy: 0.8
    radius: 10
    softness: 1
    offset: "0, -0.5"
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: RCDDeconstructable
    cost: 4
    delay: 2
    fx: EffectRCDDeconstruct2
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors: #excess damage, don't spawn entities.
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:EmptyAllContainersBehaviour
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 2
            max: 2
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
  placement:
    mode: SnapgridCenter
    snap:
    - Wallmount

- type: entity
  name: "світло"
  description: "Світильник. Споживає енергію і виробляє світло, якщо оснащений світловою трубкою."
  id: PoweredlightEmpty
  suffix: Empty
  parent: AlwaysPoweredWallLight
  components:
  - type: Sprite
    sprite: Structures/Wallmounts/Lighting/light_tube.rsi
    state: empty
  - type: PointLight
    enabled: false
  - type: PoweredLight
    bulb: Tube
    damage:
      types:
        Heat: 2
  - type: ContainerContainer
    containers:
      light_bulb: !type:ContainerSlot
  - type: ApcPowerReceiver
  - type: ExtensionCableReceiver
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: SmartLight
  - type: WirelessNetworkConnection
    range: 200
  - type: DeviceLinkSink
    ports:
      - On
      - Off
      - Toggle
  - type: Appearance
  - type: PoweredLightVisuals
    blinkingSound:
      path: "/Audio/Machines/light_tube_on.ogg"
    spriteStateMap:
      empty: empty
      off: base
      on: base
      broken: broken
      burned: burned

- type: entity
  id: Poweredlight
  description: "Світильник. Споживає енергію і виробляє світло, якщо оснащений світловою трубкою."
  suffix: ""
  parent: PoweredlightEmpty
  components:
  - type: Sprite
    state: base
  - type: PointLight
    enabled: true
  - type: PoweredLight
    hasLampOnSpawn: LightTube
    damage:
      types:
        Heat: 2
  - type: AmbientOnPowered
  - type: AmbientSound
    volume: -15
    range: 2
    sound:
      path: /Audio/Ambience/Objects/light_hum.ogg

#LED lights
- type: entity
  id: PoweredlightLED
  description: "Світильник. Споживає енергію і виробляє світло, якщо оснащений світловою трубкою."
  suffix: LED
  parent: Poweredlight
  components:
  - type: PoweredLight
    hasLampOnSpawn: LedLightTube
    damage:
      types:
        Heat: 1 #LEDs don't get as hot
  - type: PointLight
    radius: 15
    energy: 1
    softness: 0.9
    color: "#EEEEFF"

- type: entity
  parent: AlwaysPoweredWallLight
  id: AlwaysPoweredLightLED
  suffix: Always Powered, LED
  components:
  - type: PointLight
    radius: 10
    energy: 2.5
    softness: 0.9
    color: "#EEEEFF"

#Exterior lights
- type: entity
  id: PoweredlightExterior
  description: "Світильник. Споживає енергію і виробляє світло, якщо оснащений світловою трубкою."
  suffix: Exterior
  parent: Poweredlight
  components:
  - type: PoweredLight
    hasLampOnSpawn: ExteriorLightTube
    damage:
      types:
        Heat: 4 #brighter light gets hotter

- type: entity
  parent: AlwaysPoweredWallLight
  id: AlwaysPoweredLightExterior
  suffix: Always Powered, Exterior
  components:
  - type: PointLight
    radius: 12
    energy: 4.5
    softness: 0.5
    color: "#B4FCF0"

#Sodium lights
- type: entity
  id: PoweredlightSodium
  description: "Світильник. Споживає енергію і виробляє світло, якщо оснащений світловою трубкою."
  suffix: Sodium
  parent: Poweredlight
  components:
  - type: PoweredLight
    hasLampOnSpawn: SodiumLightTube
    damage:
      types:
        Heat: 2
  - type: PointLight
    radius: 10
    energy: 2.5
    softness: 0.9
    color: "#FFAF38"

- type: entity
  parent: AlwaysPoweredWallLight
  id: AlwaysPoweredLightSodium
  suffix: Always Powered, Sodium
  components:
  - type: PointLight
    radius: 10
    energy: 4
    softness: 0.5
    color: "#FFAF38"

#Small lights
- type: entity
  name: "маленький світильник"
  description: "Постійно ввімкнене світло."
  id: SmallLight
  suffix: Always Powered
  parent: AlwaysPoweredWallLight
  components:
    - type: AmbientSound
      volume: -15
      range: 2
      sound:
        path: /Audio/Ambience/Objects/light_hum.ogg
    - type: Sprite
      sprite: Structures/Wallmounts/Lighting/light_small.rsi
      state: base
    - type: PointLight
      energy: 1.0
      radius: 6
      softness: 1.1
      enabled: true
    - type: Damageable
      damageContainer: Inorganic
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 100
        behaviors: #excess damage, don't spawn entities.
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 25
        behaviors:
        - !type:EmptyAllContainersBehaviour
        - !type:SpawnEntitiesBehavior
          spawn:
            SheetSteel1:
              min: 1
              max: 1
        - !type:DoActsBehavior
          acts: ["Destruction"]
    - type: Construction
      graph: LightFixture
      node: bulbLight

- type: entity
  name: "маленький світильник"
  description: "Освітлювальний прилад. Споживає енергію і виробляє світло, коли оснащений лампочкою."
  id: PoweredSmallLightEmpty
  suffix: Empty
  parent: SmallLight
  components:
  - type: Sprite
    sprite: Structures/Wallmounts/Lighting/light_small.rsi
    state: empty
  - type: AmbientSound
    enabled: false
  - type: PointLight
    enabled: false
    offset: "0, -0.5"
  - type: ContainerContainer
    containers:
      light_bulb: !type:ContainerSlot
  - type: PoweredLight
    bulb: Bulb
    damage:
      types:
        Heat: 2
  - type: ApcPowerReceiver
  - type: ExtensionCableReceiver
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: SmartLight
  - type: WirelessNetworkConnection
    range: 200
  - type: Appearance
  - type: PoweredLightVisuals
    spriteStateMap:
      empty: empty
      off: base
      on: base
      broken: broken
      burned: burned
  - type: DeviceLinkSink
    ports:
      - On
      - Off
      - Toggle

- type: entity
  id: PoweredLEDSmallLight
  suffix: LED
  parent: PoweredSmallLightEmpty
  components:
  - type: Sprite
    state: base
  - type: PointLight
    enabled: true
    radius: 8
    energy: 1
    softness: 1
    color: "#EEEEFF"
  - type: PoweredLight
    hasLampOnSpawn: LedLightBulb
    damage:
      types:
        Heat: 1

- type: entity
  id: PoweredSmallLight
  suffix: ""
  parent: PoweredSmallLightEmpty
  components:
  - type: Sprite
    state: base
  - type: PointLight
    enabled: true
  - type: PoweredLight
    hasLampOnSpawn: LightBulb
    damage:
      types:
        Heat: 2

#Emergency Lights
- type: entity
  id: EmergencyLight
  name: "аварійне світло"
  description: "Невеликий ліхтар з внутрішнім акумулятором, який вмикається, як тільки перестає отримувати енергію. Технологія Nanotrasen дозволяє йому змінювати свій колір, щоб попередити екіпаж про умови на станції."
  parent: AlwaysPoweredWallLight
  suffix: ""
  components:
  - type: PointLight
    enabled: false
    radius: 5
    energy: 0.6
    offset: "0, 0.4"
    color: "#7CFC00"
    mask: /Textures/Effects/LightMasks/double_cone.png
  - type: ApcPowerReceiver
  - type: ExtensionCableReceiver
  - type: Battery
    maxCharge: 30000
    startingCharge: 0
  - type: EmergencyLight
  - type: RotatingLight
    speed: 40
  - type: Sprite
    sprite: Structures/Wallmounts/Lighting/emergency_light.rsi
    layers:
    - state: base
      map: [ "enum.EmergencyLightVisualLayers.Base" ]
    - state: emergency_light_off
      map: [ "enum.EmergencyLightVisualLayers.LightOff" ]
      color: "#7CFC00"
    - state: emergency_light_on
      map: [ "enum.EmergencyLightVisualLayers.LightOn" ]
      color: "#7CFC00"
      shader: "unshaded"
      visible: false
  - type: Appearance
  placement:
    mode: SnapgridCenter
    snap:
    - Wallmount

#Coloured lights

- type: entity
  id: PoweredlightCyan
  suffix: Cyan
  parent: Poweredlight
  components:
  - type: PoweredLight
    hasLampOnSpawn: LightTubeCrystalCyan
    damage:
      types:
        Heat: 2
  - type: PointLight
    radius: 8
    energy: 3
    softness: 0.5
    color: "#47f8ff"

- type: entity
  id: AlwaysPoweredlightCyan
  suffix: Always Powered, Cyan
  parent: AlwaysPoweredWallLight
  components:
  - type: PointLight
    radius: 8
    energy: 3
    softness: 0.5
    color: "#47f8ff"

- type: entity
  id: PoweredlightBlue
  suffix: Blue
  parent: Poweredlight
  components:
  - type: PoweredLight
    hasLampOnSpawn: LightTubeCrystalBlue
    damage:
      types:
        Heat: 2
  - type: PointLight
    radius: 8
    energy: 3
    softness: 0.5
    color: "#39a1ff"

- type: entity
  id: AlwaysPoweredlightBlue
  suffix: Always Powered, Blue
  parent: AlwaysPoweredWallLight
  components:
  - type: PointLight
    radius: 8
    energy: 3
    softness: 0.5
    color: "#39a1ff"

- type: entity
  id: PoweredlightPink
  suffix: Pink
  parent: Poweredlight
  components:
  - type: PoweredLight
    hasLampOnSpawn: LightTubeCrystalPink
    damage:
      types:
        Heat: 2
  - type: PointLight
    radius: 8
    energy: 3
    softness: 0.5
    color: "#ff66cc"

- type: entity
  id: AlwaysPoweredlightPink
  suffix: Always Powered, Pink
  parent: AlwaysPoweredWallLight
  components:
  - type: PointLight
    radius: 8
    energy: 3
    softness: 0.5
    color: "#ff66cc"

- type: entity
  id: PoweredlightOrange
  suffix: Orange
  parent: Poweredlight
  components:
  - type: PoweredLight
    hasLampOnSpawn: LightTubeCrystalOrange
    damage:
      types:
        Heat: 2
  - type: PointLight
    radius: 8
    energy: 3
    softness: 0.5
    color: "#ff8227"

- type: entity
  id: AlwaysPoweredlightOrange
  suffix: Always Powered, Orange
  parent: AlwaysPoweredWallLight
  components:
  - type: PointLight
    radius: 8
    energy: 3
    softness: 0.5
    color: "#ff8227"

- type: entity
  id: PoweredlightRed
  suffix: Red
  parent: Poweredlight
  components:
  - type: PoweredLight
    hasLampOnSpawn: LightTubeCrystalRed
    damage:
      types:
        Heat: 2
  - type: PointLight
    radius: 8
    energy: 3
    softness: 0.5
    color: "#fb4747"

- type: entity
  id: AlwaysPoweredlightRed
  suffix: Always Powered, Red
  parent: AlwaysPoweredWallLight
  components:
  - type: PointLight
    radius: 8
    energy: 3
    softness: 0.5
    color: "#fb4747"

- type: entity
  id: PoweredlightGreen
  suffix: Green
  parent: Poweredlight
  components:
  - type: PoweredLight
    hasLampOnSpawn: LightTubeCrystalGreen
    damage:
      types:
        Heat: 2
  - type: PointLight
    radius: 8
    energy: 3
    softness: 0.5
    color: "#52ff39"

- type: entity
  id: AlwaysPoweredlightGreen
  suffix: Always Powered, Green
  parent: AlwaysPoweredWallLight
  components:
  - type: PointLight
    radius: 8
    energy: 3
    softness: 0.5
    color: "#52ff39"
