﻿<Control xmlns="https://spacestation14.io"
                 xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls">
    <PanelContainer Name="BackgroundColorPanel" Access="Public"/>
    <BoxContainer Orientation="Horizontal"
                  HorizontalExpand="True"
                  SeparationOverride="4">
        <Label Name="UsernameLabel"
               SizeFlagsStretchRatio="3"
               HorizontalExpand="True"
               ClipText="True"
               Text="{Loc player-tab-username}"
               MouseFilter="Pass"/>
        <cc:VSeparator/>
        <Label Name="CharacterLabel"
               SizeFlagsStretchRatio="3"
               HorizontalExpand="True"
               ClipText="True"
               Text="{Loc player-tab-character}"
               MouseFilter="Pass"/>
        <cc:VSeparator/>
        <Label Name="JobLabel"
               SizeFlagsStretchRatio="3"
               HorizontalExpand="True"
               ClipText="True"
               Text="{Loc player-tab-job}"
               MouseFilter="Pass"/>
        <cc:VSeparator/>
        <Label Name="AntagonistLabel"
               SizeFlagsStretchRatio="2"
               HorizontalExpand="True"
               ClipText="True"
               Text="{Loc player-tab-antagonist}"
               MouseFilter="Pass"/>
        <cc:VSeparator/>
        <Label Name="RoleTypeLabel"
               SizeFlagsStretchRatio="2"
               HorizontalExpand="True"
               ClipText="True"
               Text="{Loc player-tab-roletype}"
               MouseFilter="Pass"/>
        <cc:VSeparator/>
        <Label Name="PlaytimeLabel"
               SizeFlagsStretchRatio="2"
               HorizontalExpand="True"
               ClipText="True"
               Text="{Loc player-tab-playtime}"
               MouseFilter="Pass"
               ToolTip="{Loc player-tab-entry-tooltip}"/>
    </BoxContainer>
</Control>
