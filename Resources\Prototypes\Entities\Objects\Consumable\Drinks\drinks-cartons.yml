- type: entity
  parent: DrinkBase
  id: DrinkCartonBaseFull
  abstract: true
  suffix: Full
  components:
  - type: Openable
    sound:
      collection: bottleOpenSounds #Could use a new sound someday ¯\_(ツ)_/¯
    closeable: true
    closeSound:
      collection: bottleCloseSounds
  - type: Sealable
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 50
  - type: PressurizedSolution
    solution: drink
  - type: Shakeable
  - type: Sprite
    state: icon
  - type: Item
    size: Small
  - type: MeleeWeapon
    soundNoDamage:
      path: "/Audio/Effects/Fluids/splat.ogg"
    damage:
      types:
        Blunt: 0
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 20
      behaviors:
      - !type:SpillBehavior { }
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
        #In future maybe add generic plastic scrap trash/debris
  - type: TrashOnSolutionEmpty
    solution: drink

- type: entity
  parent: DrinkCartonBaseFull
  id: DrinkCartonBaseLargeFull
  abstract: true
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 100
  - type: Item
    size: Normal

- type: entity
  id: DrinkCartonVisualsOpenable
  abstract: true
  components:
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.OpenableVisuals.Opened:
        enum.OpenableVisuals.Layer:
          True: {state: "icon_open"}
          False: {state: "icon"}
  - type: Sprite
    layers:
      - state: icon
        map: ["enum.OpenableVisuals.Layer"]

- type: entity
  parent: [DrinkCartonVisualsOpenable, DrinkCartonBaseFull]
  id: DrinkJuiceLimeCarton
  name: "сік лайма"
  description: "Солодко-кисла смакота."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: JuiceLime
          Quantity: 50
  - type: Sprite
    sprite: Objects/Consumable/Drinks/limejuice.rsi

- type: entity
  parent: [DrinkCartonVisualsOpenable, DrinkCartonBaseFull]
  id: DrinkJuiceOrangeCarton
  name: "апельсиновий сік"
  description: "Повно вітамінів і смакоти!"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: JuiceOrange
          Quantity: 50
  - type: Sprite
    sprite: Objects/Consumable/Drinks/orangejuice.rsi

- type: entity
  parent: [DrinkCartonVisualsOpenable, DrinkCartonBaseFull]
  id: DrinkJuiceTomatoCarton
  name: "томатний сік"
  description: "Ну, принаймні, це виглядає як томатний сік. Не скажеш, що це почервоніння."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: JuiceTomato
          Quantity: 50
  - type: Sprite
    sprite: Objects/Consumable/Drinks/tomatojuice.rsi

- type: entity
  parent: [DrinkBottleVisualsOpenable, DrinkBottlePlasticBaseFull]
  id: DrinkCoconutWaterCarton
  name: "кокосова вода"
  description: "Цінною є саме внутрішня частина кокосового горіха."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: CoconutWater
          Quantity: 50
  - type: Drink
  - type: Label
    currentLabel: coconut water
  - type: Sprite
    sprite: Objects/Consumable/Drinks/coconutwater.rsi

- type: entity
  parent: [DrinkCartonVisualsOpenable, DrinkCartonBaseFull]
  id: DrinkCreamCarton
  name: "молочні вершки"
  description: "Це вершки. Зроблені з молока. Що ще ти думав там знайти?"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Cream
          Quantity: 50
  - type: Sprite
    sprite: Objects/Consumable/Drinks/cream.rsi

- type: entity
  parent: [DrinkCartonVisualsOpenable, DrinkCartonBaseLargeFull]
  id: DrinkMilkCarton
  name: "молоко"
  description: "Непрозора біла рідина, що виробляється молочними залозами ссавців."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: Milk
          Quantity: 100
  - type: Sprite
    sprite: Objects/Consumable/Drinks/milk.rsi

- type: entity
  parent: [DrinkCartonVisualsOpenable, DrinkCartonBaseLargeFull]
  id: DrinkSoyMilkCarton
  name: "соєве молоко"
  description: "Біла та поживна соєва смакота!"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: MilkSoy
          Quantity: 100
  - type: Sprite
    sprite: Objects/Consumable/Drinks/soymilk.rsi

- type: entity
  parent: [DrinkCartonVisualsOpenable, DrinkCartonBaseLargeFull]
  id: DrinkOatMilkCarton
  name: "вівсяне молоко"
  description: "Це вівсяне молоко. Смачна і поживна смакота!"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        reagents:
        - ReagentId: MilkOat
          Quantity: 100
  - type: Sprite
    sprite: Objects/Consumable/Drinks/oatmilk.rsi

- type: entity
  parent: DrinkCartonBaseFull
  id: DrinkCartonBaseSmallFull
  abstract: true
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 25

- type: entity
  parent: [DrinkCartonVisualsOpenable, DrinkCartonBaseFull]
  id: DrinkMilkCartonMini
  name: "міні-молоко"
  description: "Непрозора біла рідина, що виробляється молочними залозами ссавців."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 25
        reagents:
        - ReagentId: Milk
          Quantity: 25
  - type: Sprite
    sprite: Objects/Consumable/Drinks/milkmini.rsi
    state: icon

- type: entity
  parent: [DrinkCartonVisualsOpenable, DrinkCartonBaseFull]
  id: DrinkMilkCartonMiniChocolate
  name: "міні-шоколадне молоко"
  description: "Молочний напій зі смаком шоколаду."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 25
        reagents:
        - ReagentId: MilkChoco
          Quantity: 25
  - type: Sprite
    sprite: Objects/Consumable/Drinks/milkminichoco.rsi
    state: icon
