# TODO: Fix - The idea is that blood and vomit is potentially not tile-bound versions of puddles(?)
- type: entity
  id: PuddleTemporary
  parent: Puddle
  abstract: true
  components:
  - type: Transform
    anchored: true
    noRot: false

- type: entity
  id: PuddleSmear
  parent: PuddleTemporary
  suffix: Smear

- type: entity
  id: PuddleVomit
  parent: PuddleTemporary
  suffix: Vomit
  components:
  - type: SolutionContainerManager
    solutions:
      puddle:
        maxVol: 1000
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Water
          Quantity: 5

- type: entity
  id: PuddleEgg
  parent: PuddleTemporary
  suffix: Egg
  components:
  - type: SolutionContainerManager
    solutions:
      puddle:
        maxVol: 1000
        reagents:
        - ReagentId: Egg
          Quantity: 6 # same as when cooking

- type: entity
  id: PuddleTomato
  parent: PuddleTemporary
  suffix: Tomato
  components:
  - type: SolutionContainerManager
    solutions:
      puddle:
        maxVol: 1000
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Water
          Quantity: 5

- type: entity
  id: PuddleWatermelon
  parent: PuddleTemporary
  suffix: Watermelon
  components:
  - type: SolutionContainerManager
    solutions:
      puddle:
        maxVol: 1000
        reagents:
        - ReagentId: Nutriment
          Quantity: 15
        - ReagentId: Water
          Quantity: 15

- type: entity
  id: PuddleFlour
  parent: PuddleTemporary
  suffix: Flour
  components:
  - type: SolutionContainerManager
    solutions:
      puddle:
        maxVol: 1000
        reagents:
        - ReagentId: Flour
          Quantity: 15

- type: entity
  id: PuddleSparkle
  name: "іскорка"
  placement:
    mode: SnapgridCenter
  components:
    # Animation is like 3 something seconds so we just need to despawn it before then.
    - type: TimedDespawn
      lifetime: 1
    - type: EvaporationSparkle
    - type: Transform
      noRot: true
      anchored: true
    - type: Sprite
      layers:
        - sprite: Fluids/wet_floor_sparkles.rsi
          state: sparkles
      drawdepth: Puddles
      color: "#FFFFFF80"

- type: entity
  name: "калюжа"
  id: Puddle
  description: "Калюжа рідини."
  placement:
    mode: SnapgridCenter
  components:
  - type: Clickable
  - type: Slippery
    launchForwardsMultiplier: 2.0
  - type: Transform
    noRot: true
    anchored: true
  - type: Sprite
    layers:
    - sprite: Fluids/puddle.rsi
      state: splat0
    drawdepth: Puddles
    color: "#FFFFFF80"
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      slipFixture:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.4,0.4,0.4"
        mask:
        - ItemMask
        layer:
        - SlipLayer
        hard: false
  - type: IconSmooth
    key: puddles
    base: splat
    mode: CardinalFlags
  - type: SolutionContainerManager
    solutions:
      puddle:
        maxVol: 1000
  - type: Puddle
  - type: MixableSolution
    solution: puddle
  - type: Appearance
  - type: ActiveEdgeSpreader
  - type: EdgeSpreader
    id: Puddle
  - type: StepTrigger
    triggerGroups:
      types:
      - SlipTile
  - type: Drink
    delay: 3
    transferAmount: 1
    solution: puddle
    examinable: false
  - type: ExaminableSolution
    solution: puddle
  - type: DrawableSolution
    solution: puddle
  - type: BadDrink
  - type: IgnoresFingerprints
  - type: PuddleFootPrints

- type: entity
  name: "крок"
  id: Footstep
  save: false
  description: "Сліди рідини."
  components:
    - type: Clickable
    - type: FootstepModifier
      footstepSoundCollection:
        collection: FootstepWater
        params:
          volume: 3
    - type: Transform
      noRot: false
    - type: Sprite
      drawdepth: FloorObjects
      color: "#FFFFFF80"
    - type: Physics
      bodyType: Static
    - type: Fixtures
      fixtures:
        # Context / examine fixture
        fix1:
          shape:
            !type:PhysShapeCircle
            radius: 0.25
        slipFixture:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.4,-0.4,0.4,0.4"
          mask:
            - ItemMask
          layer:
            - SlipLayer
          hard: false
    - type: SolutionContainerManager
      solutions:
        step: { maxVol: 2 }
    - type: FootPrint
    - type: Puddle
      solution: step
    - type: Appearance
    - type: Drink
      delay: 3
      transferAmount: 1
      solution: step
      examinable: false
    - type: ExaminableSolution
      solution: step
    - type: MixableSolution
      solution: step
    - type: DrawableSolution
      solution: step
    - type: BadDrink
    - type: IgnoresFingerprints
    - type: Tag
      tags:
      - DNASolutionScannable
