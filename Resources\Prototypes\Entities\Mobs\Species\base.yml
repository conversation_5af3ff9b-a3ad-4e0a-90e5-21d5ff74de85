- type: entity
  save: false
  parent:
  - BaseMob
  - MobDamageable
  - MobPolymorphable
  - MobCombat
  id: BaseMobSpecies
  abstract: true
  components:
  - type: Sprite
    layers:
    - map: [ "enum.HumanoidVisualLayers.Chest" ]
    - map: [ "enum.HumanoidVisualLayers.Head" ]
    - map: [ "enum.HumanoidVisualLayers.Snout" ]
    - map: [ "enum.HumanoidVisualLayers.Eyes" ]
    - map: [ "enum.HumanoidVisualLayers.Face" ]
    - map: [ "enum.HumanoidVisualLayers.RArm" ]
    - map: [ "enum.HumanoidVisualLayers.LArm" ]
    - map: [ "enum.HumanoidVisualLayers.RLeg" ]
    - map: [ "enum.HumanoidVisualLayers.LLeg" ]
#     - shader: StencilClear
#      sprite: Mobs/Species/Human/parts.rsi
#      state: l_leg
    - map: [ "enum.HumanoidVisualLayers.Underwear" ]
    - map: [ "enum.HumanoidVisualLayers.Undershirt" ]
    - map: ["jumpsuit"]
    - map: ["enum.HumanoidVisualLayers.LFoot"]
    - map: ["enum.HumanoidVisualLayers.RFoot"]
    - map: ["enum.HumanoidVisualLayers.LHand"]
    - map: ["enum.HumanoidVisualLayers.RHand"]
    - map: [ "gloves" ]
    - map: [ "shoes" ]
    - map: [ "ears" ]
    - map: [ "innerBelt" ]
    - map: [ "innerNeck" ]
    - map: [ "outerClothing" ]
    - map: [ "eyes" ]
    - map: [ "belt" ]
    - map: [ "id" ]
    - map: [ "neck" ]
    - map: [ "back" ]
    - map: [ "enum.HumanoidVisualLayers.FacialHair" ]
    - map: [ "enum.HumanoidVisualLayers.Hair" ]
    - map: [ "enum.HumanoidVisualLayers.HeadSide" ]
    - map: [ "enum.HumanoidVisualLayers.HeadTop" ]
    - map: [ "enum.HumanoidVisualLayers.Tail" ]
    - map: [ "enum.HumanoidVisualLayers.Wings" ]
    - map: [ "mask" ]
    - map: [ "head" ]
    - map: [ "neck2" ] #PIRATE
    - map: [ "neck1" ] #PIRATE
    - map: [ "head2" ] #PIRATE
    - map: [ "head1" ] #PIRATE
    - map: [ "pocket1" ]
    - map: [ "pocket2" ]
    - map: ["enum.HumanoidVisualLayers.Handcuffs"]
      color: "#ffffff"
      sprite: Objects/Misc/handcuffs.rsi
      state: body-overlay-2
      visible: false
    - map: [ "clownedon" ] # Dynamically generated
      sprite: "Effects/creampie.rsi"
      state: "creampie_human"
      visible: false
  - type: DamageVisuals
    thresholds: [ 10, 20, 30, 50, 70, 100 ]
    targetLayers:
    - "enum.HumanoidVisualLayers.Chest"
    - "enum.HumanoidVisualLayers.Head"
    - "enum.HumanoidVisualLayers.LArm"
    - "enum.HumanoidVisualLayers.LLeg"
    - "enum.HumanoidVisualLayers.RArm"
    - "enum.HumanoidVisualLayers.RLeg"
    damageOverlayGroups:
      Brute:
        sprite: Mobs/Effects/brute_damage.rsi
        color: "#FF0000"
      Burn:
        sprite: Mobs/Effects/burn_damage.rsi
  - type: GenericVisualizer
    visuals:
      enum.CreamPiedVisuals.Creamed:
        clownedon: # Not 'creampied' bc I can already see Skyrat complaining about conflicts.
          True: {visible: true}
          False: {visible: false}
  - type: StatusIcon
    bounds: -0.5,-0.5,0.5,0.5
  - type: HasJobIcons # Goobstation
  - type: RotationVisuals
    defaultRotation: 90
    horizontalRotation: 90
  - type: HumanoidAppearance
    species: Human
  - type: SlowOnDamage
    speedModifierThresholds:
      60: 0.75
      80: 0.55
  - type: Fixtures
    fixtures: # TODO: This needs a second fixture just for mob collisions.
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 185
        restitution: 0.0
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: FloorOcclusion
  - type: Mood
  - type: RangedDamageSound
    soundGroups:
      Brute:
        collection:
          MeatBulletImpact
    soundTypes:
      Heat:
        collection:
          MeatLaserImpact
  - type: Reactive
    groups:
      Flammable: [ Touch ]
      Extinguish: [ Touch ]
      Acidic: [Touch, Ingestion]
    reactions:
    - reagents: [Water, SpaceCleaner]
      methods: [Touch]
      effects:
      - !type:WashCreamPieReaction
  - type: StatusEffects
    allowed:
    - Stun
    - KnockedDown
    - SlowedDown
    - Stutter
    - SeeingRainbows
    - Electrocution
    - Drunk
    - SlurredSpeech
    - RatvarianLanguage
    - PressureImmunity
    - Muted
    - ForcedSleep
    - TemporaryBlindness
    - Pacified
    - StaminaModifier
    - Flashed
    - RadiationProtection
    - Drowsiness
    - PsionicsDisabled
    - PsionicallyInsulated
  - type: Body
    prototype: Human
    requiredLegs: 2
  - type: Identity
  - type: IdExaminable
  - type: Hands
  - type: ComplexInteraction
  - type: Internals
  - type: Inventory
  - type: InventorySlots
  - type: FloatingVisuals
  - type: Climbing
  - type: Cuffable
  - type: Ensnareable
    sprite: Objects/Misc/ensnare.rsi
    state: icon
  - type: AnimationPlayer
  - type: Buckle
  - type: CombatMode
    canDisarm: true
  - type: MeleeWeapon
    soundHit:
      collection: Punch
    angle: 30
    animation: WeaponArcFist
    attackRate: 1
    damage:
      types:
        Blunt: 5
    heavyRateModifier: 1
    heavyDamageBaseModifier: 1
    heavyPartDamageMultiplier: 1
    heavyStaminaCost: 0
    maxTargets: 1
  - type: SleepEmitSound
  - type: SSDIndicator
  - type: StandingState
  - type: Fingerprint
  - type: Dna
  - type: Scent
  - type: MindContainer
    showExamineInfo: true
  - type: CanEnterCryostorage
  - type: InteractionPopup
    successChance: 1
    interactSuccessString: hugging-success-generic
    interactSuccessSound: /Audio/Effects/thudswoosh.ogg
    messagePerceivedByOthers: hugging-success-generic-others
  - type: CanHostGuardian
  - type: NpcFactionMember
    factions:
    - NanoTrasen
  - type: CreamPied
  - type: Stripping
  - type: Strippable
  - type: UserInterface
    interfaces:
      enum.HumanoidMarkingModifierKey.Key:
        type: HumanoidMarkingModifierBoundUserInterface
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
      # Goobstation - changelings
      enum.StoreUiKey.Key:
        type: StoreBoundUserInterface
      enum.HereticLivingHeartKey.Key: # goob edit - heretics
        type: LivingHeartMenuBoundUserInterface
      enum.InstrumentUiKey.Key:
        type: InstrumentBoundUserInterface
        requireInputValidation: false
      enum.RadialSelectorUiKey.Key:
        type: RadialSelectorMenuBUI
      enum.ListViewSelectorUiKey.Key:
        type: ListViewSelectorBUI
      enum.WizardTeleportUiKey.Key: # Goobstation - wizard
        type: WizardTeleportBoundUserInterface
        requireInputValidation: false
      enum.SurgeryUIKey.Key:
        type: SurgeryBui
      enum.VampireMutationUiKey.Key:
        type: VampireMutationBoundUserInterface
  - type: Puller
  - type: Speech
    speechSounds: Alto
  - type: DamageForceSay
  - type: Vocal
    sounds:
      Male: MaleHuman
      Female: FemaleHuman
      Unsexed: MaleHuman
  - type: Emoting
  - type: BodyEmotes
    soundsId: GeneralBodyEmotes
  - type: Grammar
    attributes:
      proper: true
  - type: MobPrice
    price: 1500 # Kidnapping a living person and selling them for cred is a good move.
    deathPenalty: 0.01 # However they really ought to be living and intact, otherwise they're worth 100x less.
  - type: CanEscapeInventory # Carrying system from nyanotrasen.
  - type: LanguageKnowledge # This is here so even if species doesn't have a defined language, they at least speak GC
    speaks:
    - TauCetiBasic
    understands:
    - TauCetiBasic
  - type: Tag
    tags:
    - CanPilot
    - FootstepSound
    - DoorBumpOpener
  - type: Targeting
  - type: SurgeryTarget
  - type: TTS #PIRATE
  - type: MovedByPressure
    cutoffTime: 1.0

- type: entity
  save: false
  parent:
  - MobBloodstream
  - MobRespirator
  - MobAtmosStandard
  - MobFlammable
  - BaseMobSpecies
  id: BaseMobSpeciesOrganic
  abstract: true
  components:
  - type: Flashable
  - type: Absorbable # Goobstation - changelings
  - type: Barotrauma
    damage:
      types:
        Blunt: 0.55 #per second, scales with pressure and other constants.
  - type: PassiveDamage # Slight passive regen. Assuming one damage type, comes out to about 4 damage a minute.
    allowedStates:
    - Alive
    damageCap: 20
    damage:
      types:
        Heat: -0.07
      groups:
        Brute: -0.07
  # Organs
  - type: StatusEffects
    allowed:
    - Stun
    - KnockedDown
    - SlowedDown
    - Stutter
    - SeeingRainbows
    - Electrocution
    - ForcedSleep
    - TemporaryBlindness
    - Drunk
    - SlurredSpeech
    - RatvarianLanguage
    - PressureImmunity
    - Muted
    - Pacified
    - StaminaModifier
    - PsionicsDisabled
    - PsionicallyInsulated
    - RadiationProtection
    - Drowsiness
  - type: Blindable
  # Other
  - type: Temperature
    heatDamageThreshold: 325
    coldDamageThreshold: 260
    currentTemperature: 310.15
    specificHeat: 42
    coldDamage:
      types:
        Cold: 0.1 #per second, scales with temperature & other constants
    heatDamage:
      types:
        Heat: 1.5 #per second, scales with temperature & other constants
  - type: TemperatureSpeed
    thresholds:
      293: 0.8
      280: 0.6
      260: 0.4
  - type: ThermalRegulator
    metabolismHeat: 800
    radiatedHeat: 100
    implicitHeatRegulation: 500
    sweatHeatRegulation: 2000
    shiveringHeatRegulation: 2000
    normalBodyTemperature: 310.15
    thermalRegulationTemperatureThreshold: 25
  - type: Perishable
  - type: Butcherable
    butcheringType: Spike # TODO human.
    spawned:
      - id: FoodMeat
        amount: 5
  - type: Respirator
    damage:
      types:
        Asphyxiation: 1.0
    damageRecovery:
      types:
        Asphyxiation: -1.0
  - type: FireVisuals
    alternateState: Standing
  - type: CharacterInformation  #PIRATE # Parkstation-CharacterInformation
  - type: OfferItem
  - type: LayingDown
  - type: BloodstreamAffectedByMass
    power: 0.6 # A minimum size felinid will have 30% blood, a minimum size vulp will have 60%, a maximum size oni will have ~200%

- type: entity
  save: false
  id: BaseSpeciesDummy
  abstract: true
  components:
  - type: Hands
  - type: ComplexInteraction
  - type: Inventory
  - type: InventorySlots
  - type: ContainerContainer
  - type: Icon
    sprite: Mobs/Species/Human/parts.rsi
    state: full
  - type: Sprite
    drawdepth: Mobs
    noRot: true
    # TODO BODY Turn these into individual body parts?
    layers:
    - map: [ "enum.HumanoidVisualLayers.Chest" ]
    - map: [ "enum.HumanoidVisualLayers.Head" ]
    - map: [ "enum.HumanoidVisualLayers.Snout" ]
    - map: [ "enum.HumanoidVisualLayers.Eyes" ]
    - map: [ "enum.HumanoidVisualLayers.Face" ]
    - map: [ "enum.HumanoidVisualLayers.RArm" ]
    - map: [ "enum.HumanoidVisualLayers.LArm" ]
    - map: [ "enum.HumanoidVisualLayers.RLeg" ]
    - map: [ "enum.HumanoidVisualLayers.LLeg" ]
#     - shader: StencilClear
#      sprite: Mobs/Species/Human/parts.rsi
#      state: l_leg
    - map: [ "enum.HumanoidVisualLayers.Underwear" ]
    - map: [ "enum.HumanoidVisualLayers.Undershirt" ]
    - map: ["jumpsuit"]
    - map: ["enum.HumanoidVisualLayers.LFoot"]
    - map: ["enum.HumanoidVisualLayers.RFoot"]
    - map: ["enum.HumanoidVisualLayers.LHand"]
    - map: ["enum.HumanoidVisualLayers.RHand"]
    - map: ["enum.HumanoidVisualLayers.Handcuffs"]
      color: "#ffffff"
      sprite: Objects/Misc/handcuffs.rsi
      state: body-overlay-2
      visible: false
    - map: [ "gloves" ]
    - map: [ "shoes" ]
    - map: [ "ears" ]
    - map: [ "innerBelt" ]
    - map: [ "innerNeck" ]
    - map: [ "outerClothing" ]
    - map: [ "eyes" ]
    - map: [ "belt" ]
    - map: [ "id" ]
    - map: [ "neck" ]
    - map: [ "back" ]
    - map: [ "enum.HumanoidVisualLayers.FacialHair" ]
    - map: [ "enum.HumanoidVisualLayers.Hair" ]
    - map: [ "enum.HumanoidVisualLayers.HeadSide" ]
    - map: [ "enum.HumanoidVisualLayers.HeadTop" ]
    - map: [ "enum.HumanoidVisualLayers.Tail" ]
    - map: [ "mask" ]
    - map: [ "head" ]
    - map: [ "neck2" ] #PIRATE
    - map: [ "neck1" ] #PIRATE
    - map: [ "head2" ] #PIRATE
    - map: [ "head1" ] #PIRATE
    - map: [ "pocket1" ]
    - map: [ "pocket2" ]
  - type: Appearance
  - type: HumanoidAppearance
    species: Human
  - type: Body
    prototype: Human
    requiredLegs: 2
  - type: Absorbable # Goobstation - changelings
  - type: UserInterface
    interfaces:
      enum.HumanoidMarkingModifierKey.Key: # sure, this can go here too
        type: HumanoidMarkingModifierBoundUserInterface
