using Robust.Shared.Serialization;

namespace Content.Shared._Pirate.DNDMage;

/// <summary>
/// Event для надсилання команд слугам некроманта
/// </summary>
[Serializable, NetSerializable]
public sealed class ServantCommandEvent : EntityEventArgs
{
    /// <summary>
    /// Слуга, якому надсилається команда (використовуємо NetEntity для серіалізації)
    /// </summary>
    public NetEntity Target;

    /// <summary>
    /// Команда для слуги
    /// </summary>
    public ServantCommand Command;

    public ServantCommandEvent(NetEntity target, ServantCommand command)
    {
        Target = target;
        Command = command;
    }
}
