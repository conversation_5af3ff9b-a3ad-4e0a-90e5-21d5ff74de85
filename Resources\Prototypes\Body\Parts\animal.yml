# Just copypasta of some human basic body parts for interaction,
# only differences for now is that limbs work in pairs,
# they are unextractable and can't be spawned (no surgery on Animals!?).

- type: entity
  id: PartAnimal
  parent: BaseItem
  name: "частина тіла тварини"
  abstract: true
  components:
  # yes these sprites dont make sense i dont care its better than them being invisible
  - type: Sprite
    sprite: Mobs/Species/Reptilian/parts.rsi
  - type: Damageable
    damageContainer: OrganicPart # Shitmed
  - type: BodyPart
  - type: ContainerContainer
    containers:
      bodypart: !type:Container
        ents: []
  - type: StaticPrice
    price: 50
  - type: Tag
    tags:
      - Trash
  - type: Gibbable
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 3
      - ReagentId: Blood
        Quantity: 10

- type: entity
  id: HandsAnimal
  name: "руки тварини"
  parent: PartAnimal
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    layers:
    - state: l_hand
    - state: r_hand
  - type: BodyPart
    partType: Hand
    #symmetry: Left
    slotId: hands # Shitmed

- type: entity
  id: LegsAnimal
  name: "ноги тварини"
  parent: PartAnimal
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    layers:
    - state: l_leg
    - state: r_leg
  - type: BodyPart
    partType: Leg
    slotId: legs # Shitmed
  - type: MovementBodyPart

- type: entity
  id: FeetAnimal
  name: "лапи тварини"
  parent: PartAnimal
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    layers:
    - state: r_foot
    - state: l_foot
  - type: BodyPart
    partType: Foot
    slotId: feet # Shitmed

- type: entity
  id: TorsoAnimal
  name: "тулуб тварини"
  parent: PartAnimal
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    layers:
    - state: torso_m
  - type: BodyPart
    partType: Torso
  - type: Damageable
    damageContainer: Biological
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 10
      - ReagentId: Blood
        Quantity: 20
