﻿# Chessboard item (normal in game item you can hold in your hand)
- type: entity
  parent: BaseBoardEntity
  id: ChessBoard
  name: "шахівниця"
  description: "Шахова дошка. Фігури в комплекті!"
  components:
  - type: Sprite
    sprite: Objects/Fun/Tabletop/chessboard.rsi
    state: chessboard
  - type: TabletopGame
    boardName: tabletop-chess-board-name
    size: 338, 274
    setup:
      !type:TabletopChessSetup
      boardPrototype: ChessBoardTabletop

# Chessboard tabletop item (item only visible in tabletop game)
- type: entity
  id: ChessBoardTabletop
  name: "шахівниця"
  parent: BaseBoardTabletop
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Fun/Tabletop/chessboard_tabletop.rsi
    state: chessboard_tabletop

# White pieces
- type: entity
  id: WhiteKing
  name: "білий король"
  parent: BaseTabletopPiece
  components:
  - type: Sprite
    sprite: Objects/Fun/Tabletop/chess_pieces.rsi
    state: w_king

- type: entity
  id: WhiteQueen
  name: "біла королева"
  parent: BaseTabletopPiece
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/chess_pieces.rsi
      state: w_queen

- type: entity
  id: WhiteRook
  name: "біла тура"
  parent: BaseTabletopPiece
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/chess_pieces.rsi
      state: w_rook

- type: entity
  id: WhiteBishop
  name: "білий офіцер"
  parent: BaseTabletopPiece
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/chess_pieces.rsi
      state: w_bishop

- type: entity
  id: WhiteKnight
  name: "білий кінь"
  parent: BaseTabletopPiece
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/chess_pieces.rsi
      state: w_knight

- type: entity
  id: WhitePawn
  name: "білий пішак"
  parent: BaseTabletopPiece
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/chess_pieces.rsi
      state: w_pawn

# Black pieces
- type: entity
  id: BlackKing
  name: "чорний король"
  parent: BaseTabletopPiece
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/chess_pieces.rsi
      state: b_king

- type: entity
  id: BlackQueen
  name: "чорна королева"
  parent: BaseTabletopPiece
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/chess_pieces.rsi
      state: b_queen

- type: entity
  id: BlackRook
  name: "чорна тура"
  parent: BaseTabletopPiece
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/chess_pieces.rsi
      state: b_rook

- type: entity
  id: BlackBishop
  name: "чорний офіцер"
  parent: BaseTabletopPiece
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/chess_pieces.rsi
      state: b_bishop

- type: entity
  id: BlackKnight
  name: "чорний кінь"
  parent: BaseTabletopPiece
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/chess_pieces.rsi
      state: b_knight

- type: entity
  id: BlackPawn
  name: "чорний пішак"
  parent: BaseTabletopPiece
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/chess_pieces.rsi
      state: b_pawn
