﻿polymorph-command-description = For when you need someone to stop being a person. Takes an entity and a polymorph prototype.
polymorph-command-help-text = polymorph <id> <polymorph prototype>

add-polymorph-action-command-description = Takes an entity and gives them a voluntary polymorph.
add-polymorph-action-command-help-text = addpolymorphaction <id> <polymorph prototype>


polymorph-not-valid-prototype-error = Polymorph prototype is not valid.