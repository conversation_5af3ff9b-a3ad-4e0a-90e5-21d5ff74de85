﻿- type: decal
  id: ConcreteTrim
  abstract: true

- type: decal
  id: GrayConcreteTrim
  parent: ConcreteTrim
  abstract: true

- type: decal
  id: OldConcreteTrim
  parent: ConcreteTrim
  abstract: true

- type: decal
  id: ConcreteTrimBox
  parent: ConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: concrete_box

- type: decal
  id: ConcreteTrimCornerNe
  parent: ConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: concrete_corner_ne

- type: decal
  id: ConcreteTrimCornerSe
  parent: ConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: concrete_corner_se

- type: decal
  id: ConcreteTrimCornerNw
  parent: ConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: concrete_corner_nw

- type: decal
  id: ConcreteTrimCornerSw
  parent: ConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: concrete_corner_sw

- type: decal
  id: ConcreteTrimInnerNe
  parent: ConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: concrete_inner_ne

- type: decal
  id: ConcreteTrimInnerSe
  parent: ConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: concrete_inner_se

- type: decal
  id: ConcreteTrimInnerNw
  parent: ConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: concrete_inner_nw

- type: decal
  id: ConcreteTrimInnerSw
  parent: ConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: concrete_inner_sw

- type: decal
  id: ConcreteTrimEndN
  parent: ConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: concrete_end_n

- type: decal
  id: ConcreteTrimEndE
  parent: ConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: concrete_end_e

- type: decal
  id: ConcreteTrimEndS
  parent: ConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: concrete_end_s

- type: decal
  id: ConcreteTrimEndW
  parent: ConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: concrete_end_w

- type: decal
  id: ConcreteTrimLineN
  parent: ConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: concrete_line_n

- type: decal
  id: ConcreteTrimLineE
  parent: ConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: concrete_line_e

- type: decal
  id: ConcreteTrimLineS
  parent: ConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: concrete_line_s

- type: decal
  id: ConcreteTrimLineW
  parent: ConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: concrete_line_w

- type: decal
  id: GrayConcreteTrimBox
  parent: GrayConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: grayconcrete_box

- type: decal
  id: GrayConcreteTrimCornerNe
  parent: GrayConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: grayconcrete_corner_ne

- type: decal
  id: GrayConcreteTrimCornerSe
  parent: GrayConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: grayconcrete_corner_se

- type: decal
  id: GrayConcreteTrimCornerNw
  parent: GrayConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: grayconcrete_corner_nw

- type: decal
  id: GrayConcreteTrimCornerSw
  parent: GrayConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: grayconcrete_corner_sw

- type: decal
  id: GrayConcreteTrimInnerNe
  parent: GrayConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: grayconcrete_inner_ne

- type: decal
  id: GrayConcreteTrimInnerSe
  parent: GrayConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: grayconcrete_inner_se

- type: decal
  id: GrayConcreteTrimInnerNw
  parent: GrayConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: grayconcrete_inner_nw

- type: decal
  id: GrayConcreteTrimInnerSw
  parent: GrayConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: grayconcrete_inner_sw

- type: decal
  id: GrayConcreteTrimEndN
  parent: GrayConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: grayconcrete_end_n

- type: decal
  id: GrayConcreteTrimEndE
  parent: GrayConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: grayconcrete_end_e

- type: decal
  id: GrayConcreteTrimEndS
  parent: GrayConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: grayconcrete_end_s

- type: decal
  id: GrayConcreteTrimEndW
  parent: GrayConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: grayconcrete_end_w

- type: decal
  id: GrayConcreteTrimLineN
  parent: GrayConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: grayconcrete_line_n

- type: decal
  id: GrayConcreteTrimLineE
  parent: GrayConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: grayconcrete_line_e

- type: decal
  id: GrayConcreteTrimLineS
  parent: GrayConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: grayconcrete_line_s

- type: decal
  id: GrayConcreteTrimLineW
  parent: GrayConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: grayconcrete_line_w

- type: decal
  id: OldConcreteTrimBox
  parent: OldConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: oldconcrete_box

- type: decal
  id: OldConcreteTrimCornerNe
  parent: OldConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: oldconcrete_corner_ne

- type: decal
  id: OldConcreteTrimCornerSe
  parent: OldConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: oldconcrete_corner_se

- type: decal
  id: OldConcreteTrimCornerNw
  parent: OldConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: oldconcrete_corner_nw

- type: decal
  id: OldConcreteTrimCornerSw
  parent: OldConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: oldconcrete_corner_sw

- type: decal
  id: OldConcreteTrimInnerNe
  parent: OldConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: oldconcrete_inner_ne

- type: decal
  id: OldConcreteTrimInnerSe
  parent: OldConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: oldconcrete_inner_se

- type: decal
  id: OldConcreteTrimInnerNw
  parent: OldConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: oldconcrete_inner_nw

- type: decal
  id: OldConcreteTrimInnerSw
  parent: OldConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: oldconcrete_inner_sw

- type: decal
  id: OldConcreteTrimEndN
  parent: OldConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: oldconcrete_end_n

- type: decal
  id: OldConcreteTrimEndE
  parent: OldConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: oldconcrete_end_e

- type: decal
  id: OldConcreteTrimEndS
  parent: OldConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: oldconcrete_end_s

- type: decal
  id: OldConcreteTrimEndW
  parent: OldConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: oldconcrete_end_w

- type: decal
  id: OldConcreteTrimLineN
  parent: OldConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: oldconcrete_line_n

- type: decal
  id: OldConcreteTrimLineE
  parent: OldConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: oldconcrete_line_e

- type: decal
  id: OldConcreteTrimLineS
  parent: OldConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: oldconcrete_line_s

- type: decal
  id: OldConcreteTrimLineW
  parent: OldConcreteTrim
  tags: ["planet", "markings"]
  sprite:
    sprite: Decals/concretetrim.rsi
    state: oldconcrete_line_w

