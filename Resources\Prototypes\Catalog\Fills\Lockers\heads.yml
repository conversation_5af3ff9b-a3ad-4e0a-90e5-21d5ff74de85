- type: entity
  id: LockerQuarterMasterFilled
  suffix: Filled
  parent: LockerQuarterMaster
  components:
  - type: StorageFill
    contents:
      - id: BoxFolderQmClipboard
      - id: CargoRequestComputerCircuitboard
      - id: CargoShuttleComputerCircuitboard
      - id: CargoShuttleConsoleCircuitboard
      - id: SalvageShuttleConsoleCircuitboard
      - id: CargoBountyComputerCircuitboard
      - id: CigPackGreen
        prob: 0.50
      - id: DoorRemoteCargo
      - id: RubberStampQm
      - id: RubberStampDenied
      - id: RubberStampApproved
      - id: ClothingHeadsetAltCargo
      - id: BoxEncryptionKeyCargo
      - id: SpaceCashLuckyBill # DeltaV - LO steal objective, see Resources/Prototypes/DeltaV/Entities/Objects/Misc/first_bill.yml
      - id: BoxPDACargo # Delta-V
      - id: QuartermasterIDCard # Delta-V
      - id: AstroNavCartridge
      - id: LunchboxCommandFilledRandom # Delta-V Lunchboxes!
        prob: 0.3
      - id: Eftpos #Pirate banking
      - id: PrinterDocFlatpack  # Corvax-Printer

- type: entity
  id: LockerCaptainFilledHardsuit
  suffix: Filled, Hardsuit
  parent: LockerCaptain
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterCoatCapTrench
      - id: NukeDisk
      - id: PinpointerNuclear
      - id: CaptainIDCard
      - id: ClothingOuterHardsuitCap
      - id: CommsComputerCircuitboard
      - id: ClothingHeadsetAltCommand
      - id: PlushieNuke
        prob: 0.1
      - id: DoorRemoteCommand
      - id: RubberStampCaptain
      - id: JetpackCaptainFilled
      - id: LunchboxCommandFilledRandom # Delta-V Lunchboxes!
        prob: 0.3
      - id: PrinterDocFlatpack  # Corvax-Printer

- type: entity
  id: LockerCaptainFilled
  suffix: Filled, AntiqueLaser # Deprecated, Antique laser is now part of Captain's Loadouts.
  parent: LockerCaptain
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterCoatCapTrench
      - id: NukeDisk
      - id: PinpointerNuclear
      - id: CaptainIDCard
      - id: CommsComputerCircuitboard
      - id: ClothingHeadsetAltCommand
      - id: PlushieNuke
        prob: 0.1
      - id: DoorRemoteCommand
      - id: RubberStampCaptain
      - id: JetpackCaptainFilled
      - id: LunchboxCommandFilledRandom
        prob: 0.3
      - id: PrinterDocFlatpack  # Corvax-Printer

- type: entity
  id: LockerCaptainFilledNoLaser
  suffix: Filled
  parent: LockerCaptain
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterCoatCapTrench
      - id: NukeDisk
      - id: PinpointerNuclear
      - id: CaptainIDCard
      - id: CommsComputerCircuitboard
      - id: ClothingHeadsetAltCommand
      - id: PlushieNuke
        prob: 0.1
      - id: DoorRemoteCommand
      - id: RubberStampCaptain
      - id: JetpackCaptainFilled
      - id: LunchboxCommandFilledRandom
        prob: 0.3
      - id: PrinterDocFlatpack  # Corvax-Printer

- type: entity
  id: LockerHeadOfPersonnelFilled
  suffix: Filled
  parent: LockerHeadOfPersonnel
  components:
  - type: StorageFill
    contents:
      - id: HoPIDCard
      - id: ClothingHeadsetAltCommand # DeltaV - HoP is now a service role, replaces their all channels headset.
      - id: BoxPDA
      - id: BoxID
      - id: BoxHeadset
      - id: IDComputerCircuitboard
      - id: DoorRemoteService
      - id: RubberStampHop
      - id: RubberStampDenied
      - id: RubberStampApproved
      - id: BoxEncryptionKeyPassenger
      - id: BoxEncryptionKeyService
      - id: LunchboxCommandFilledRandom # Delta-V Lunchboxes!
        prob: 0.3
      # Pirate Banking Start
      - id: CommandBudgetCard
      - id: CommandBudgetPinPaper
      # Pirate Banking End
      - id: PrinterDocFlatpack  # Corvax-Printer

- type: entity
  id: LockerChiefEngineerFilledHardsuit
  suffix: Filled, Hardsuit
  parent: LockerChiefEngineer
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterHardsuitEngineeringWhite
      - id: ClothingMaskBreath
      - id: ClothingEyesGlassesMeson
      - id: ClothingShoesBootsMagAdv
      - id: ClothingHandsGlovesColorYellow
      - id: ClothingOuterJacketInsulated
      - id: CigarCase
        prob: 0.15
      - id: DoorRemoteEngineering
      - id: RubberStampCE
      - id: JetpackVoidFilled
      - id: ClothingHeadsetAltEngineering
      - id: BoxEncryptionKeyEngineering
      - id: AccessConfigurator
      - id: BoxPDAEngineering # Delta-V
      - id: RCD
      - id: RCDAmmo
      - id: RPD
      - id: RCDAmmo # Extra Compressed matter for the RPD
      - id: CEIDCard # Delta-V
      - id: LunchboxCommandFilledRandom # Delta-V Lunchboxes!
        prob: 0.3
      - id: PrinterDocFlatpack  # Corvax-Printer

- type: entity
  id: LockerChiefEngineerFilled
  suffix: Filled
  parent: LockerChiefEngineer
  components:
  - type: StorageFill
    contents:
      - id: ClothingEyesGlassesMeson
      - id: ClothingBeltChiefEngineerFilled
      - id: ClothingHandsGlovesColorYellow
      - id: ClothingOuterJacketInsulated
      - id: CigarCase
        prob: 0.15
      - id: DoorRemoteEngineering
      - id: RubberStampCE
      - id: ClothingHeadsetAltEngineering
      - id: BoxEncryptionKeyEngineering
      - id: AccessConfigurator
      - id: BoxPDAEngineering # Delta-V
      - id: CEIDCard # Delta-V
      - id: RCD
      - id: RCDAmmo
      - id: RPD
      - id: RCDAmmo # Extra Compressed matter for the RPD
      - id: LunchboxCommandFilledRandom # Delta-V Lunchboxes!
        prob: 0.3
      - id: PrinterDocFlatpack  # Corvax-Printer

- type: entity
  id: LockerChiefMedicalOfficerFilledHardsuit
  suffix: Filled, Hardsuit
  parent: LockerChiefMedicalOfficer
  components:
  - type: StorageFill
    contents:
      - id: MedkitFilled
      - id: ClothingHandsGlovesNitrile
      - id: ClothingOuterJacketSurgical
      - id: ClothingEyesHudMedical
      - id: ClothingHeadsetAltMedical
      - id: ClothingBackpackDuffelSurgeryFilled
      - id: ClothingMaskSterile
      - id: ClothingHeadHatBeretCmo
      - id: ClothingOuterHardsuitMedical
      - id: Hypospray
      - id: HandheldCrewMonitor
      - id: DoorRemoteMedical
      - id: RubberStampCMO
      - id: RubberStampPsychologist # DeltaV
      - id: MedicalTechFabCircuitboard
      - id: BoxEncryptionKeyMedical
      - id: BoxPDAMedical # Delta-V
      - id: ClothingBeltMilitaryWebbingCMO # DeltaV - add webbing for CMO. ON THIS STATION, IT'S DRIP OR [die], CAPTAIN!
      - id: CMOIDCard # Delta-V
      - id: LunchboxCommandFilledRandom # Delta-V Lunchboxes!
        prob: 0.3
      - id: RapidSyringeGun # Pirate
      - id: BoxSyringePax # Pirate
      - id: MedicalBiofabMachineBoard # Shitmed Change
      - id: PrinterDocFlatpack  # Corvax-Printer

- type: entity
  id: LockerChiefMedicalOfficerFilled
  suffix: Filled
  parent: LockerChiefMedicalOfficer
  components:
  - type: StorageFill
    contents:
      - id: MedkitFilled
      - id: ClothingHandsGlovesNitrile
      - id: ClothingOuterJacketSurgical
      - id: ClothingEyesHudMedical
      - id: ClothingHeadsetAltMedical
      - id: ClothingBackpackDuffelSurgeryFilled
      - id: Hypospray
      - id: HandheldCrewMonitor
      - id: DoorRemoteMedical
      - id: RubberStampCMO
      - id: MedicalTechFabCircuitboard
      - id: BoxEncryptionKeyMedical
      - id: BoxPDAMedical # Delta-V
      - id: ClothingBeltMilitaryWebbingCMO # DeltaV - add webbing for CMO. ON THIS STATION, IT'S DRIP OR [die], CAPTAIN!
      - id: CMOIDCard # Delta-V
      - id: MedTekCartridge # Delta-v
      - id: LunchboxCommandFilledRandom # Delta-V Lunchboxes!
        prob: 0.3
      - id: RapidSyringeGun # Pirate
      - id: BoxSyringePax # Pirate
      - id: MedicalBiofabMachineBoard # Shitmed Change
      - id: PrinterDocFlatpack  # Corvax-Printer

- type: entity
  id: LockerResearchDirectorFilledHardsuit
  suffix: Filled, Hardsuit
  parent: LockerResearchDirector
  components:
  - type: StorageFill
    contents:
      - id: ResearchComputerCircuitboard
      - id: ProtolatheMachineCircuitboard
      - id: CircuitImprinterMachineCircuitboard
      - id: ClothingOuterHardsuitRd
      - id: HandTeleporter
      - id: DoorRemoteResearch
      - id: ClothingBeltUtilityFilled
      - id: RubberStampRd
      - id: BoxEncryptionKeyScience
      - id: BoxPDAScience # Delta-V
      - id: RDIDCard # Delta-V
      - id: ClothingHeadsetAltScience
      - id: EncryptionKeyBinary
      - id: Intellicard
      - id: LunchboxCommandFilledRandom # Delta-V Lunchboxes!
        prob: 0.3
      - id: PsiWatchCartridge
      - id: StationAiUploadCircuitboard
      - id: AsimovCircuitBoard
      - id: AugustineCircuitBoard
      - id: CrewsimovCircuitBoard
      - id: AntimovCircuitBoard
      - id: PrinterDocFlatpack  # Corvax-Printer
        amount: 2

- type: entity
  id: LockerResearchDirectorFilled
  suffix: Filled
  parent: LockerResearchDirector
  components:
  - type: StorageFill
    contents:
      - id: ResearchComputerCircuitboard
      - id: ProtolatheMachineCircuitboard
      - id: CircuitImprinterMachineCircuitboard
      - id: HandTeleporter
      - id: DoorRemoteResearch
      - id: ClothingBeltUtilityFilled
      - id: RubberStampRd
      - id: BoxEncryptionKeyScience
      - id: BoxPDAScience # Delta-V
      - id: RDIDCard # Delta-V
      - id: ClothingHeadsetAltScience
      - id: EncryptionKeyBinary
      - id: LunchboxCommandFilledRandom # Delta-V Lunchboxes!
        prob: 0.3
      - id: PsiWatchCartridge
      - id: StationAiUploadCircuitboard
      - id: AsimovCircuitBoard
      - id: AugustineCircuitBoard
      - id: CrewsimovCircuitBoard
      - id: AntimovCircuitBoard
      - id: PrinterDocFlatpack  # Corvax-Printer

- type: entity
  id: LockerHeadOfSecurityFilledHardsuit
  suffix: Filled, Hardsuit
  parent: LockerHeadOfSecurity
  components:
  - type: StorageFill
    contents:
      - id: ClothingEyesHudSecurity
      - id: WeaponDisabler
      - id: ClothingOuterCoatHoSTrench
      - id: ClothingMaskNeckGaiter
      - id: ClothingOuterHardsuitCombatHoS # DeltaV - ClothingOuterHardsuitSecurityRed replaced in favour of head of security's advanced combat hardsuit.
      - id: ClothingBeltSecurityFilled
      - id: ClothingHeadsetAltSecurity
      - id: ClothingEyesGlassesSunglasses
      - id: ClothingShoesBootsJack
      - id: CigarRobustCase
        prob: 0.50
      - id: DoorRemoteSecurity
      - id: RubberStampHos
      - id: SecurityTechFabCircuitboard
      - id: JetpackSecurityFilled
      - id: BoxEncryptionKeySecurity
      - id: HoloprojectorSecurity
      - id: BoxPDASecurity # Delta-V
      - id: HoSIDCard # Delta-V
      - id: LunchboxCommandFilledRandom # Delta-V Lunchboxes!
        prob: 0.3
      - id: HoverbikeNfsdFlatpack #PIRATE
      - id: PrinterDocFlatpack  # Corvax-Printer

- type: entity
  id: LockerHeadOfSecurityFilled
  suffix: Filled
  parent: LockerHeadOfSecurity
  components:
  - type: StorageFill
    contents:
      - id: ClothingEyesHudSecurity
      - id: WeaponDisabler
      - id: ClothingOuterCoatHoSTrench
      - id: ClothingMaskNeckGaiter
      - id: ClothingBeltSecurityFilled
      - id: ClothingHeadsetAltSecurity
      - id: ClothingEyesGlassesSunglasses
      - id: ClothingShoesBootsJack
      - id: CigarGoldCase
        prob: 0.50
      - id: DoorRemoteSecurity
      - id: RubberStampHos
      - id: SecurityTechFabCircuitboard
      - id: BoxEncryptionKeySecurity
      - id: HoloprojectorSecurity
      - id: BoxPDASecurity # Delta-V
      - id: HoSIDCard # Delta-V
      - id: LunchboxCommandFilledRandom # Delta-V Lunchboxes!
        prob: 0.3
      - id: HoverbikeNfsdFlatpack #PIRATE
      - id: PrinterDocFlatpack  # Corvax-Printer
      - id: ClothingHandsGlovesKravMaga # Goobstation - Martial Arts

- type: entity
  id: LockerFreezerVaultFilled
  suffix: Vault, Locked
  parent: LockerFreezerBase
  components:
  - type: AccessReader
    access: [ [ "Command" ] ]
  - type: StorageFill
    contents:
    - id: WeaponRevolverDeckard
    - id: ClothingOuterHardsuitBasic
    - id: JetpackBlue
    - id: SpaceCash1000
    - id: BeachBall
    - id: BikeHorn
