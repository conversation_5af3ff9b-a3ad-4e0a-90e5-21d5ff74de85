- type: entity
  parent: BaseStructure
  id: ContainmentFieldGenerator
  name: "генератор захисного поля"
  description: "Ма<PERSON><PERSON><PERSON>, яка генерує стримуюче поле, коли живиться від випромінювача. Тримає сингулярність слухняною."
  components:
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.4
        density: 190
        mask:
        - FullTileMask
        layer:
        - WallLayer
  - type: InteractionOutline
  - type: Anchorable
  - type: Sprite
    sprite: Structures/Power/Generation/Singularity/containment.rsi
    state: icon
    noRot: true
    layers:
      - state: icon
      - state: p1
        map: ["powerLight"]
        visible: false
        shader: unshaded
      - state: a1
        map: ["fieldLight"]
        visible: false
        shader: unshaded
      - state: on
        map: ["connectedLight"]
        visible: false
        shader: unshaded
  - type: Icon
    sprite: Structures/Power/Generation/Singularity/containment.rsi
    state: icon
  - type: ContainmentFieldGenerator
  - type: PointLight
    enabled: false
    color: "#4080FF"
    radius: 32
    energy: 2.0
    softness: 32.0
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ContainmentFieldGeneratorVisuals.PowerLight:
        powerLight:
          NoPower: {visible: false}
          LowPower: {visible: true, state: p1}
          MediumPower: {visible: true, state: p3}
          HighPower: {visible: true, state: p6}
      enum.ContainmentFieldGeneratorVisuals.FieldLight:
        fieldLight:
          NoLevel: {visible: false}
          On: {visible: true, state: a1}
          OneField: {visible: true, state: a2}
          MultipleFields: {visible: true, state: a3}
      enum.ContainmentFieldGeneratorVisuals.OnLight:
        connectedLight:
          True: { visible: true }
          False: { visible: false }
  - type: GuideHelp
    guides: [ Singularity, Power ]


- type: entity
  id: ContainmentField
  name: "поле утримання"
  description: "Стримуюче поле, яке відштовхує гравітаційні сингулярності."
  placement:
    mode: SnapgridCenter
  components:
  - type: InteractionOutline
  - type: Clickable
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.5,-0.5,0.5,0.5"
        mask:
        - FullTileMask
        layer:
        - GlassLayer
  - type: Sprite
    sprite: Structures/Power/Generation/Singularity/containment_field.rsi
    state: field
    noRot: true
  - type: Icon
    sprite: Structures/Power/Generation/Singularity/containment_field.rsi
    state: field
  - type: ContainmentField
    throwForce: 50
  - type: SyncSprite
  - type: GuideHelp
    guides: [ Singularity, Power ]


