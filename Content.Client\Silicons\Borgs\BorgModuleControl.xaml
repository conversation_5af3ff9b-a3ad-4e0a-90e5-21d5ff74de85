﻿<PanelContainer xmlns="https://spacestation14.io"
                HorizontalExpand="True"
                StyleClasses="PanelBackgroundLight"
                Margin="5 5 5 0">
    <BoxContainer Orientation="Horizontal" HorizontalExpand="True" Margin="5 5 0 5">
        <SpriteView Name="ModuleView" Margin="0 0 5 0"/>
        <BoxContainer RectClipContent="True" HorizontalExpand="True">
            <Label Name="ModuleName" HorizontalExpand="True" HorizontalAlignment="Center"/>
        </BoxContainer>
        <TextureButton Name="RemoveButton" VerticalAlignment="Top" HorizontalAlignment="Right" Scale="0.5 0.5"/>
    </BoxContainer>
</PanelContainer>
