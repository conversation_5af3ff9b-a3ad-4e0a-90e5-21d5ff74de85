# Dear god please stop adding ones with thousands of entities, learn how procgen works.

# "Small"-class maps - Max size square: 7x7, indicated size: 3.5

- type: salvageMap
  id: Small1
  mapPath: /Maps/Salvage/small-1.yml

- type: salvageMap
  id: Small2
  mapPath: /Maps/Salvage/small-2.yml

- type: salvageMap
  id: SmallShip1
  mapPath: /Maps/Salvage/small-ship-1.yml

- type: salvageMap
  id: Small3
  mapPath: /Maps/Salvage/small-3.yml

- type: salvageMap
  id: SmallAISurveyDrone
  mapPath: /Maps/Salvage/small-ai-survey-drone.yml

- type: salvageMap
  id: Small4
  mapPath: /Maps/Salvage/small-4.yml

- type: salvageMap
  id: SmallCargo
  mapPath: /Maps/Salvage/small-cargo.yml

- type: salvageMap
  id: SmallChapel
  mapPath: /Maps/Salvage/small-chapel.yml

- type: salvageMap
  id: SmallChef
  mapPath: /Maps/Salvage/small-chef.yml

- type: salvageMap
  id: SmallParty
  mapPath: /Maps/Salvage/small-party.yml

- type: salvageMap
  id: SmallSyndicate
  mapPath: /Maps/Salvage/small-syndicate.yml

- type: salvageMap
  id: SmallTesla
  mapPath: /Maps/Salvage/small-tesla.yml

# Small - Asteroids

- type: salvageMap
  id: SmallA1
  mapPath: /Maps/Salvage/small-a-1.yml

# "Medium"-class maps - Max size square: 15x15, indicated size: 7.5

- type: salvageMap
  id: Medium1
  mapPath: /Maps/Salvage/medium-1.yml

- type: salvageMap
  id: MediumVault1
  mapPath: /Maps/Salvage/medium-vault-1.yml

- type: salvageMap
  id: MediumOrchestra
  mapPath: /Maps/Salvage/medium-silent-orchestra.yml

- type: salvageMap
  id: MediumLibraryWreck
  mapPath: /Maps/Salvage/medium-library.yml

- type: salvageMap
  id: MediumCargoWreck
  mapPath: /Maps/Salvage/cargo-1.yml

- type: salvageMap
  id: MediumPirateWreck
  mapPath: /Maps/Salvage/medium-pirate.yml

- type: salvageMap
  id: TickColony
  mapPath: /Maps/Salvage/tick-colony.yml

- type: salvageMap
  id: CargoDock
  mapPath: /Maps/Salvage/medium-dock.yml

- type: salvageMap
  id: SpaceWaffleHome
  mapPath: /Maps/Salvage/wh-salvage.yml

- type: salvageMap
  id: MediumShuttleWreck
  mapPath: /Maps/Salvage/medium-ruined-emergency-shuttle.yml

- type: salvageMap
  id: MediumPetHospital
  mapPath: /Maps/Salvage/medium-pet-hospital.yml

- type: salvageMap
  id: MediumCrashedShuttle
  mapPath: /Maps/Salvage/medium-crashed-shuttle.yml

- type: salvageMap
  id: Meatball
  mapPath: /Maps/Salvage/meatball.yml

- type: salvageMap
  id: VeganMeatball
  mapPath: /Maps/Salvage/vegan-meatball.yml

- type: salvageMap
  id: MediumHaulingShuttleWreck
  mapPath: /Maps/Salvage/hauling-shuttle.yml

# """Large""" maps

- type: salvageMap
  id: StationStation
  mapPath: /Maps/Salvage/stationstation.yml

- type: salvageMap
  id: AsteroidBase
  mapPath: /Maps/Salvage/asteroid-base.yml

- type: salvageMap
  id: RuinCargoBase
  mapPath: /Maps/Salvage/ruin-cargo-salvage.yml

- type: salvageMap
  id: SecurityChunk
  mapPath: /Maps/Salvage/security-chunk.yml

- type: salvageMap
  id: EngineeringChunk
  mapPath: /Maps/Salvage/engineering-chunk.yml

- type: salvageMap
  id: OutpostArm
  mapPath: /Maps/Salvage/outpost-arm.yml
