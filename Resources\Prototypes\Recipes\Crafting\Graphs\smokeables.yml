- type: constructionGraph
  id: smokeableJoint
  start: start
  graph:
    - node: start
      edges:
        - to: joint
          steps:
            - material: PaperRolling
            - material: CigaretteFilter
            - material: GroundCannabis
              doAfter: 2
    - node: joint
      entity: Joint
      
- type: constructionGraph
  id: smokeableJointRainbow
  start: start
  graph:
    - node: start
      edges:
        - to: jointRainbow
          steps:
            - material: PaperRolling
            - material: CigaretteFilter
            - material: GroundCannabisRainbow
              doAfter: 2
    - node: jointRainbow
      entity: JointRainbow

- type: constructionGraph
  id: smokeableBlunt
  start: start
  graph:
    - node: start
      edges:
        - to: blunt
          steps:
            - material: LeavesTobaccoDried
            - material: GroundCannabis
              doAfter: 2
    - node: blunt
      entity: Blunt

- type: constructionGraph
  id: smokeableBluntRainbow
  start: start
  graph:
    - node: start
      edges:
        - to: bluntRainbow
          steps:
            - material: LeavesTobaccoDried
            - material: GroundCannabisRainbow
              doAfter: 2
    - node: bluntRainbow
      entity: BluntRainbow

- type: constructionGraph
  id: smokeableCigarette
  start: start
  graph:
    - node: start
      edges:
        - to: cigarette
          steps:
            - material: PaperRolling
            - material: CigaretteFilter
            - material: GroundTobacco
              doAfter: 2
    - node: cigarette
      entity: Cigarette

- type: constructionGraph
  id: smokeableGroundCannabis
  start: start
  graph:
    - node: start
      edges:
        - to: ground
          steps:
            - material: LeavesCannabisDried
              amount: 2
              doAfter: 5
    - node: ground
      entity: GroundCannabis

- type: constructionGraph
  id: smokeableGroundCannabisRainbow
  start: start
  graph:
    - node: start
      edges:
        - to: groundRainbow
          steps:
            - material: LeavesCannabisRainbowDried
              amount: 2
              doAfter: 5
    - node: groundRainbow
      entity: GroundCannabisRainbow

- type: constructionGraph
  id: smokeableGroundTobacco
  start: start
  graph:
    - node: start
      edges:
        - to: ground
          steps:
          - material: LeavesTobaccoDried
            amount: 2
            doAfter: 5
    - node: ground
      entity: GroundTobacco
