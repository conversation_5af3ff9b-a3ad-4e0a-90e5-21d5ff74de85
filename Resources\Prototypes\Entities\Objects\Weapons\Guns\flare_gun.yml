- type: entity
  name: "сиг<PERSON>льна ракета"
  parent: BaseItem
  id: WeaponFlareGun
  description: "Компактний однозарядний пістолет, що стріляє дробовими набоями. Має запобіжник, який не дозволяє користувачеві вставляти смертоносні набої всередину."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Shotguns/flaregun.rsi
    layers:
      - state: base
        map: ["enum.GunVisualLayers.Base"]
  - type: Item
    size: Small
    sprite: Objects/Weapons/Guns/Shotguns/flaregun.rsi
  - type: ItemSlots
    slots:
      gun_chamber:
        name: Chamber
        startingItem: Flare
        priority: 1
        whitelist:
          tags: ## TODO: Add a risk of the gun blowing up if using non-light shotgun shells, and then re-enable them.
            ## - ShellShotgun
            - ShellShotgunLight
            - Flare
  - type: ContainerContainer
    containers:
      gun_chamber: !type:ContainerSlot
  - type: ChamberMagazineAmmoProvider
    autoCycle: false
    boltClosed: true
    canRack: false
    soundBoltClosed: /Audio/Weapons/Guns/Cock/revolver_cock.ogg
    soundBoltOpened: /Audio/Weapons/Guns/Cock/revolver_cock.ogg
    soundRack: /Audio/Weapons/Guns/Cock/revolver_cock.ogg
  - type: Clothing
    sprite: Objects/Weapons/Guns/Shotguns/flaregun.rsi
    quickEquip: false
    slots:
    - Belt
    - suitStorage
  - type: Appearance
  - type: Gun
    fireRate: 8
    selectedMode: SemiAuto
    availableModes:
      - SemiAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/flaregun.ogg
  # PIRATE START
  - type: CanTakeAim
  # PIRATE END


- type: entity
  name: "охоронний пістолет"
  parent: WeaponFlareGun
  id: WeaponFlareGunSecurity
  description: "Модифікована сигнальна ракета, спочатку призначена для використання службою безпеки для запуску несмертельних дробових снарядів, однак вона також може стріляти смертельними снарядами без ризику."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Shotguns/flaregun_security.rsi
    layers:
      - state: base
        map: ["enum.GunVisualLayers.Base"]
  - type: Item
    size: Small
    sprite: Objects/Weapons/Guns/Shotguns/flaregun_security.rsi
  - type: ItemSlots
    slots:
      gun_chamber:
        name: Chamber
        priority: 1
        whitelist:
          tags:
            - ShellShotgun
  - type: Tag
    tags:
    - Sidearm
