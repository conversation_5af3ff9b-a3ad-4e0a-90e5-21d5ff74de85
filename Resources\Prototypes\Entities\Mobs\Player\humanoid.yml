# Random humanoids

- type: randomHumanoidSettings
  id: EventHumanoid
  # Shitmed Change Start
  speciesBlacklist:
    - Monkey
    - Kobold
    - Abductor
  # Shitmed Change End
  components:
    - type: RandomHumanoidAppearance
      randomizeName: false
    - type: GhostTakeoverAvailable

- type: randomHumanoidSettings
  id: EventHumanoidMindShielded
  parent: EventHumanoid
  components:
    - type: MindShield
    - type: AntagImmune

# Goobstation start
#- type: randomHumanoidSettings
#  id: EventHuman
#  parent: EventHumanoid
#  speciesWhitelist: Human

#- type: randomHumanoidSettings
#  id: EventHumanMindShielded
#  parent: EventHumanoidMindShielded
#  speciesWhitelist: Human
# Goobstation end

## Death Squad

- type: entity
  id: RandomHumanoidSpawnerDeathSquad
  name: "Агент Ескадрону Смерті"
  suffix: ERTRole, Death Squad
  components:
    - type: MindShield
    - type: Sprite
      sprite: Clothing/OuterClothing/Hardsuits/deathsquad.rsi
      state: icon
    - type: RandomMetadata
      nameSegments:
        - NamesFirstMilitaryLeader
        - NamesLastMilitary
    - type: RandomHumanoidSpawner
      settings: DeathSquad

- type: randomHumanoidSettings
  id: DeathSquad
  randomizeName: false
  speciesBlacklist:
    - Shadowkin
    - Monkey # Shitmed Change
    - Kobold # Shitmed Change
  # PIRATE Change Start
    - BananaMen
    - Gingerbread
    - Skeleton
    - Vox
  # PIRATE Change End
  components:
    - type: MindShield
    - type: GhostRole
      name: ghost-role-information-Death-Squad-name
      description: ghost-role-information-Death-Squad-description
      rules: ghost-role-information-Death-Squad-rules
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ DeathSquadGear ]
    - type: RandomMetadata
      nameSegments:
        - NamesFirstMilitaryLeader
        - NamesLastMilitary

## ERT Leader

- type: entity
  id: RandomHumanoidSpawnerERTLeader
  name: "лідер ГШР"
  suffix: ERTRole, Basic
  components:
    - type: Sprite
      sprite: Markers/jobs.rsi
      state: ertleader
    - type: RandomMetadata
      nameSegments:
      - NamesFirstMilitaryLeader
      - NamesLastMilitary
    - type: RandomHumanoidSpawner
      settings: ERTLeader

- type: randomHumanoidSettings
  id: ERTLeader
  randomizeName: false
  speciesBlacklist:
    - Shadowkin
    - Monkey # Shitmed Change
    - Kobold # Shitmed Change
  # PIRATE Change Start
    - BananaMen
    - Gingerbread
    - Skeleton
    - Vox
  # PIRATE Change End
  components:
    - type: MindShield
    - type: GhostRole
      name: ghost-role-information-ert-leader-name
      description: ghost-role-information-ert-leader-description
      rules: ghost-role-information-nonantagonist-rules
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ ERTLeaderGear ]
    - type: RandomMetadata
      nameSegments:
      - NamesFirstMilitaryLeader
      - NamesLastMilitary

- type: entity
  id: RandomHumanoidSpawnerERTLeaderEVA
  parent: RandomHumanoidSpawnerERTLeader
  name: "лідер ГШР"
  suffix: ERTRole, Armored EVA
  components:
    - type: Sprite
      sprite: Markers/jobs.rsi
      state: ertleadereva
    - type: RandomHumanoidSpawner
      settings: ERTLeaderEVA

- type: randomHumanoidSettings
  id: ERTLeaderEVA
  parent: ERTLeader
  components:
    - type: MindShield
    - type: GhostRole
      name: ghost-role-information-ert-leader-name
      description: ghost-role-information-ert-leader-description
      rules: ghost-role-information-nonantagonist-rules
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ ERTLeaderGearEVA ]

- type: entity
  id: RandomHumanoidSpawnerERTLeaderEVALecter
  parent: RandomHumanoidSpawnerERTLeaderEVA
  suffix: ERTRole, Lecter, EVA
  components:
    - type: RandomHumanoidSpawner
      settings: ERTLeaderEVALecter

- type: randomHumanoidSettings
  id: ERTLeaderEVALecter
  parent: ERTLeaderEVA
  components:
    - type: MindShield
    - type: GhostRole
      name: ghost-role-information-ert-leader-name
      description: ghost-role-information-ert-leader-description
      rules: ghost-role-information-nonantagonist-rules
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ ERTLeaderGearEVALecter ]

## ERT Chaplain

- type: entity
  id: RandomHumanoidSpawnerERTChaplain
  parent: RandomHumanoidSpawnerERTLeader
  name: "капелан ERT"
  suffix: ERTRole, Basic
  components:
    - type: Sprite
      sprite: Markers/jobs.rsi
      state: chaplain #needs an ERT variant once a good suit it made.
    - type: RandomMetadata
      nameSegments:
      - NamesFirstMilitary
      - NamesLastMilitary
    - type: RandomHumanoidSpawner
      settings: ERTChaplain

- type: randomHumanoidSettings
  id: ERTChaplain
  parent: ERTLeader
  components:
    - type: MindShield
    - type: GhostRole
      name: ghost-role-information-ert-chaplain-name
      description: ghost-role-information-ert-chaplain-description
      rules: ghost-role-information-nonantagonist-rules
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: RandomMetadata
      nameSegments:
      - NamesFirstMilitary
      - NamesLastMilitary
    - type: Loadout
      prototypes: [ ERTChaplainGear ]

- type: entity
  id: RandomHumanoidSpawnerERTChaplainEVA
  parent: RandomHumanoidSpawnerERTChaplain
  name: "капелан ERT"
  suffix: ERTRole, Enviro EVA
  components:
    - type: Sprite
      sprite: Markers/jobs.rsi
      state: chaplain #needs an ERT variant once a good suit it made.
    - type: RandomHumanoidSpawner
      settings: ERTChaplainEVA

- type: randomHumanoidSettings
  id: ERTChaplainEVA
  parent: ERTChaplain
  components:
    - type: MindShield
    - type: GhostRole
      name: ghost-role-information-ert-chaplain-name
      description: ghost-role-information-ert-chaplain-description
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ ERTChaplainGearEVA ]

## ERT Janitor

- type: entity
  id: RandomHumanoidSpawnerERTJanitor
  parent: RandomHumanoidSpawnerERTLeader
  name: "прибиральник ГШР"
  suffix: ERTRole, Basic
  components:
    - type: Sprite
      sprite: Markers/jobs.rsi
      state: ertjanitor
    - type: RandomMetadata
      nameSegments:
      - NamesFirstMilitary
      - NamesLastMilitary
    - type: RandomHumanoidSpawner
      settings: ERTJanitor

- type: randomHumanoidSettings
  id: ERTJanitor
  parent: ERTLeader
  components:
    - type: MindShield
    - type: GhostRole
      name: ghost-role-information-ert-janitor-name
      description: ghost-role-information-ert-janitor-description
      rules: ghost-role-information-nonantagonist-rules
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: RandomMetadata
      nameSegments:
      - NamesFirstMilitary
      - NamesLastMilitary
    - type: Loadout
      prototypes: [ ERTJanitorGear ]

- type: entity
  id: RandomHumanoidSpawnerERTJanitorEVA
  parent: RandomHumanoidSpawnerERTJanitor
  name: "прибиральник ГШР"
  suffix: ERTRole, Enviro EVA
  components:
    - type: Sprite
      sprite: Markers/jobs.rsi
      state: ertjanitoreva
    - type: RandomHumanoidSpawner
      settings: ERTJanitorEVA

- type: randomHumanoidSettings
  id: ERTJanitorEVA
  parent: ERTJanitor
  components:
    - type: MindShield
    - type: GhostRole
      name: ghost-role-information-ert-janitor-name
      description: ghost-role-information-ert-janitor-description
      rules: ghost-role-information-nonantagonist-rules
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ ERTJanitorGearEVA ]

## ERT Engineer

- type: entity
  id: RandomHumanoidSpawnerERTEngineer
  parent: RandomHumanoidSpawnerERTLeader
  name: "інженер ГШР"
  suffix: ERTRole, Basic
  components:
    - type: Sprite
      sprite: Markers/jobs.rsi
      state: ertengineer
    - type: RandomMetadata
      nameSegments:
      - NamesFirstMilitary
      - NamesLastMilitary
    - type: RandomHumanoidSpawner
      settings: ERTEngineer

- type: randomHumanoidSettings
  id: ERTEngineer
  parent: ERTLeader
  components:
    - type: MindShield
    - type: GhostRole
      name: ghost-role-information-ert-engineer-name
      description: ghost-role-information-ert-engineer-description
      rules: ghost-role-information-nonantagonist-rules
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: RandomMetadata
      nameSegments:
      - NamesFirstMilitary
      - NamesLastMilitary
    - type: Loadout
      prototypes: [ ERTEngineerGear ]

- type: entity
  id: RandomHumanoidSpawnerERTEngineerEVA
  parent: RandomHumanoidSpawnerERTEngineer
  name: "інженер ГШР"
  suffix: ERTRole, Enviro EVA
  components:
    - type: Sprite
      sprite: Markers/jobs.rsi
      state: ertengineereva
    - type: RandomHumanoidSpawner
      settings: ERTEngineerEVA

- type: randomHumanoidSettings
  id: ERTEngineerEVA
  parent: ERTEngineer
  components:
    - type: MindShield
    - type: GhostRole
      name: ghost-role-information-ert-engineer-name
      description: ghost-role-information-ert-engineer-description
      rules: ghost-role-information-nonantagonist-rules
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ ERTEngineerGearEVA ]

## ERT Security

- type: entity
  id: RandomHumanoidSpawnerERTSecurity
  parent: RandomHumanoidSpawnerERTLeader
  name: "охоронець ГШР"
  suffix: ERTRole, Basic
  components:
    - type: Sprite
      sprite: Markers/jobs.rsi
      state: ertsecurity
    - type: RandomMetadata
      nameSegments:
      - NamesFirstMilitary
      - NamesLastMilitary
    - type: RandomHumanoidSpawner
      settings: ERTSecurity

- type: randomHumanoidSettings
  id: ERTSecurity
  parent: ERTLeader
  components:
    - type: MindShield
    - type: GhostRole
      name: ghost-role-information-ert-security-name
      description: ghost-role-information-ert-security-description
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: RandomMetadata
      nameSegments:
      - NamesFirstMilitary
      - NamesLastMilitary
    - type: Loadout
      prototypes: [ ERTSecurityGear ]

- type: entity
  id: RandomHumanoidSpawnerERTSecurityEVA
  parent: RandomHumanoidSpawnerERTSecurity
  name: "охоронець ГШР"
  suffix: ERTRole, Armored EVA
  components:
    - type: Sprite
      sprite: Markers/jobs.rsi
      state: ertsecurityeva
    - type: RandomHumanoidSpawner
      settings: ERTSecurityEVA

- type: randomHumanoidSettings
  id: ERTSecurityEVA
  parent: ERTSecurity
  components:
    - type: MindShield
    - type: GhostRole
      name: ghost-role-information-ert-security-name
      description: ghost-role-information-ert-security-description
      rules: ghost-role-information-nonantagonist-rules
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ ERTSecurityGearEVA ]

- type: entity
  id: RandomHumanoidSpawnerERTSecurityEVALecter
  parent: RandomHumanoidSpawnerERTSecurityEVA
  suffix: ERTRole, Lecter, EVA
  components:
    - type: RandomHumanoidSpawner
      settings: ERTSecurityEVALecter

- type: randomHumanoidSettings
  id: ERTSecurityEVALecter
  parent: ERTSecurityEVA
  components:
    - type: MindShield
    - type: GhostRole
      name: ghost-role-information-ert-security-name
      description: ghost-role-information-ert-security-description
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ ERTSecurityGearEVALecter ]

## ERT Medic

- type: entity
  id: RandomHumanoidSpawnerERTMedical
  parent: RandomHumanoidSpawnerERTLeader
  name: "медик ГШР"
  suffix: ERTRole, Basic
  components:
    - type: Sprite
      sprite: Markers/jobs.rsi
      state: ertmedical
    - type: RandomMetadata
      nameSegments:
      - NamesFirstMilitary
      - NamesLastMilitary
    - type: RandomHumanoidSpawner
      settings: ERTMedical

- type: randomHumanoidSettings
  id: ERTMedical
  parent: ERTLeader
  components:
    - type: MindShield
    - type: GhostRole
      name: ghost-role-information-ert-medical-name
      description: ghost-role-information-ert-medical-description
      rules: ghost-role-information-nonantagonist-rules
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: RandomMetadata
      nameSegments:
      - NamesFirstMilitary
      - NamesLastMilitary
    - type: Loadout
      prototypes: [ ERTMedicalGear ]

- type: entity
  id: RandomHumanoidSpawnerERTMedicalEVA
  parent: RandomHumanoidSpawnerERTMedical
  name: "медик ГШР"
  suffix: ERTRole, Armored EVA
  components:
    - type: Sprite
      sprite: Markers/jobs.rsi
      state: ertmedicaleva
    - type: RandomHumanoidSpawner
      settings: ERTMedicalEVA

- type: randomHumanoidSettings
  id: ERTMedicalEVA
  parent: ERTMedical
  components:
    - type: MindShield
    - type: GhostRole
      name: ghost-role-information-ert-medical-name
      description: ghost-role-information-ert-medical-description
      rules: ghost-role-information-nonantagonist-rules
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ ERTMedicalGearEVA ]

## CBURN

- type: entity
  id: RandomHumanoidSpawnerCBURNUnit
  name: "Агент CBURN"
  suffix: ERTRole
  components:
    - type: Sprite
      sprite: Markers/jobs.rsi
      state: cburn
    - type: RandomHumanoidSpawner
      settings: CBURNAgent

- type: randomHumanoidSettings
  id: CBURNAgent
  speciesBlacklist:
    - Shadowkin
    - Monkey # Shitmed Change
    - Kobold # Shitmed Change
  # PIRATE Change Start
    - BananaMen
    - Gingerbread
    - Skeleton
    - Vox
  # PIRATE Change End
  components:
    - type: MindShield
    - type: Loadout
      prototypes: [CBURNGear]
    - type: GhostRole
      name: ghost-role-information-cburn-agent-name
      description: ghost-role-information-cburn-agent-description
      rules: ghost-role-information-nonantagonist-rules
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: RandomMetadata
      nameSegments:
      - NamesFirstMilitary
      - NamesLastMilitary

## Central Command

- type: entity
  name: "офіційний представник CentCom"
  id: RandomHumanoidSpawnerCentcomOfficial
  components:
    - type: Sprite
      sprite: Markers/jobs.rsi
      state: centcom
    - type: RandomHumanoidSpawner
      settings: CentcomOfficial

- type: randomHumanoidSettings
  id: CentcomOfficial
  speciesBlacklist:
    - Shadowkin
    - Monkey # Shitmed Change
    - Kobold # Shitmed Change
  # PIRATE Change Start
    - BananaMen
    - Gingerbread
    - Skeleton
    - Vox
  # PIRATE Change End
  components:
    - type: MindShield
    - type: GhostRole
      name: ghost-role-information-centcom-official-name
      description: ghost-role-information-centcom-official-description
      rules: ghost-role-information-nonantagonist-rules
      raffle:
        settings: default
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ CentcomGear ]

## Syndicate

- type: entity
  id: RandomHumanoidSpawnerSyndicateAgent
  name: "агент синдикату"
  components:
    - type: Sprite
      sprite: Mobs/Species/Human/parts.rsi
      state: full
    - type: RandomMetadata
      nameSegments: [ names_death_commando ]
    - type: RandomHumanoidSpawner
      settings: SyndicateAgent

- type: randomHumanoidSettings
  id: SyndicateAgent
  speciesBlacklist:
    - Shadowkin
    - Monkey # Shitmed Change
    - Kobold # Shitmed Change
  # PIRATE Change Start
    - BananaMen
    - Gingerbread
    - Skeleton
    - Vox
  # PIRATE Change End
  components:
    - type: RandomHumanoidAppearance
      randomizeName: false
    - type: Loadout
      prototypes: [SyndicateOperativeGearExtremelyBasic]

- type: entity
  id: RandomHumanoidSpawnerNukeOp
  name: "Ядерний оперативник"
  components:
    - type: Sprite
      sprite: Mobs/Species/Human/parts.rsi
      state: full
    - type: RandomHumanoidSpawner
      settings: NukeOp

- type: randomHumanoidSettings
  id: NukeOp
  speciesBlacklist:
    - Shadowkin
    - Monkey # Shitmed Change
    - Kobold # Shitmed Change
  # PIRATE Change Start
    - BananaMen
    - Gingerbread
    - Plasmaman
    - Skeleton
    - Vox
  # PIRATE Change End
  components:
    - type: RandomHumanoidAppearance
      randomizeName: false
    - type: NukeOperative
    - type: Psionic
      powerRollMultiplier: 7

- type: entity
  id: RandomHumanoidSpawnerCluwne
  name: "Клувня"
  suffix: spawns a cluwne
  components:
    - type: Sprite
      sprite: Markers/jobs.rsi
      state: cluwne
    - type: RandomHumanoidSpawner
      settings: Cluwne
    - type: RandomMetadata
      nameSegments:
      - names_first
      - names_last

- type: randomHumanoidSettings
  id: Cluwne
  speciesBlacklist:
    - Shadowkin
    - Monkey # Shitmed Change
    - Kobold # Shitmed Change
  randomizeName: false
  components:
    - type: GhostRole
      name: ghost-role-information-cluwne-name
      description: ghost-role-information-cluwne-description
      raffle:
        settings: default
    - type: GhostTakeoverAvailable
    - type: Cluwne

## Lost Cargo technician

- type: entity
  name: "загублений вантажний технік спавнер"
  id: LostCargoTechnicianSpawner
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Objects/Tools/appraisal-tool.rsi
          state: icon
    - type: RandomSpawner
      prototypes:
      - RandomHumanoidLostCargoTechnician
      chance: 1

- type: entity
  id: RandomHumanoidLostCargoTechnician
  name: "роль примари втраченого вантажного техніка"
  components:
    - type: Sprite
      sprite: Objects/Tools/appraisal-tool.rsi
      state: icon
    - type: RandomHumanoidSpawner
      settings: LostCargoTechnician

- type: randomHumanoidSettings
  id: LostCargoTechnician
  randomizeName: false
  components:
    - type: GhostRole
      name: ghost-role-information-lost-cargo-technical-name
      description: ghost-role-information-lost-cargo-technical-description
      rules: ghost-role-information-lost-cargo-technical-rules
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ CargoTechGear ]
    - type: RandomMetadata
      nameSegments:
      - names_first
      - names_last

# Clown troupe

- type: entity
  name: "зачинатель клоунської трупи"
  id: ClownTroupeSpawner
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Objects/Fun/bikehorn.rsi
          state: icon
    - type: RandomSpawner
      prototypes:
      - RandomHumanoidClownTroupe
      rarePrototypes:
      - RandomHumanoidClownTroupeBanana
      rareChance: 0.3

- type: entity
  id: RandomHumanoidClownTroupe
  name: "роль примари клоунської трупи"
  components:
    - type: Sprite
      sprite: Objects/Tools/appraisal-tool.rsi
      state: icon
    - type: RandomHumanoidSpawner
      settings: ClownTroupe

- type: entity
  id: RandomHumanoidClownTroupeBanana
  name: "бананова клоунська трупа"
  parent: RandomHumanoidClownTroupe
  components:
    - type: RandomHumanoidSpawner
      settings: ClownTroupeBanana

- type: randomHumanoidSettings
  id: ClownTroupe
  randomizeName: false
  components:
    - type: GhostRole
      name: ghost-role-information-clown-troupe-name
      description: ghost-role-information-clown-troupe-description
      rules: ghost-role-information-clown-troupe-rules
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ ClownGear ]
    - type: RandomMetadata
      nameSegments:
      - names_clown

- type: randomHumanoidSettings
  id: ClownTroupeBanana
  randomizeName: false
  components:
    - type: GhostRole
      name: ghost-role-information-clown-troupe-name
      description: ghost-role-information-clown-troupe-description
      rules: ghost-role-information-clown-troupe-rules
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ BananaClown ]
    - type: RandomMetadata
      nameSegments:
      - names_clown

# Traveling exotic chef

- type: entity
  name: "мандрівний кухар-споунер"
  id: TravelingChefSpawner
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Objects/Weapons/Melee/kitchen_knife.rsi
          state: icon
    - type: RandomSpawner
      prototypes:
      - RandomHumanoidTravelingChef

- type: entity
  id: RandomHumanoidTravelingChef
  name: "роль мандрівного кухаря-привида"
  components:
    - type: Sprite
      sprite: Objects/Tools/appraisal-tool.rsi
      state: icon
    - type: RandomHumanoidSpawner
      settings: TravelingChef

- type: randomHumanoidSettings
  id: TravelingChef
  randomizeName: false
  components:
    - type: GhostRole
      name: ghost-role-information-traveling-chef-name
      description: ghost-role-information-traveling-chef-description
      rules: ghost-role-information-traveling-chef-rules
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ ChefGear ]
    - type: RandomMetadata
      nameSegments:
      - names_first
      - names_last

# Disaster victim

- type: entity
  name: "спайдер жертв стихійного лиха"
  id: DisasterVictimSpawner
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: red
      - sprite: Clothing/OuterClothing/Hardsuits/basic.rsi
        state: icon
  - type: RandomSpawner
    prototypes:
    - RandomHumanoidDisasterVictimRD
    - RandomHumanoidDisasterVictimCMO
    - RandomHumanoidDisasterVictimCaptain
    - MobSkeletonCloset

- type: entity
  id: RandomHumanoidDisasterVictimRD
  name: "жертва катастрофи роль примари RD"
  components:
    - type: Sprite
      sprite: Clothing/OuterClothing/Hardsuits/basic.rsi
      state: icon
    - type: RandomHumanoidSpawner
      settings: DisasterVictimResearchDirector

- type: entity
  id: RandomHumanoidDisasterVictimCMO
  parent: RandomHumanoidDisasterVictimRD
  name: "роль примари CMO-жертви катастрофи"
  components:
    - type: RandomHumanoidSpawner
      settings: DisasterVictimCMO

- type: entity
  id: RandomHumanoidDisasterVictimCaptain
  parent: RandomHumanoidDisasterVictimRD
  name: "жертва катастрофи роль капітана-привида"
  components:
    - type: RandomHumanoidSpawner
      settings: DisasterVictimCaptain

- type: randomHumanoidSettings
  id: DisasterVictimResearchDirector
  randomizeName: false
  components:
    - type: GhostRole
      name: ghost-role-information-disaster-victim-name
      description: ghost-role-information-disaster-victim-description
      rules: ghost-role-information-disaster-victim-rules
      raffle:
        settings: default
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ ResearchDirectorGear ]
    - type: RandomMetadata
      nameSegments:
      - names_first
      - names_last

- type: randomHumanoidSettings
  id: DisasterVictimCMO
  randomizeName: false
  components:
    - type: GhostRole
      name: ghost-role-information-disaster-victim-name
      description: ghost-role-information-disaster-victim-description
      rules: ghost-role-information-disaster-victim-rules
      raffle:
        settings: default
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ CMOGear ]
    - type: RandomMetadata
      nameSegments:
      - names_first
      - names_last

- type: randomHumanoidSettings
  id: DisasterVictimCaptain
  randomizeName: false
  components:
    - type: GhostRole
      name: ghost-role-information-disaster-victim-name
      description: ghost-role-information-disaster-victim-description
      rules: ghost-role-information-disaster-victim-rules
      raffle:
        settings: default
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ CaptainGear ]
    - type: RandomMetadata
      nameSegments:
      - names_first
      - names_last

# Syndie Disaster Victim

- type: entity
  name: "породження жертв синдикативної катастрофи"
  id: SyndieDisasterVictimSpawner
  parent: MarkerBase
  components:
  - type: Sprite
    layers:
      - state: red
      - sprite: Structures/Decoration/banner.rsi
        state: banner_syndicate
  - type: RandomSpawner
    prototypes:
    - RandomHumanoidSyndieDisasterVictim

- type: entity
  id: RandomHumanoidSyndieDisasterVictim
  name: "роль примари жертви синдикатської катастрофи"
  components:
    - type: Sprite
      sprite: Structures/Decoration/banner.rsi
      state: banner_syndicate
    - type: RandomHumanoidSpawner
      settings: SyndieDisasterVictim

- type: randomHumanoidSettings
  id: SyndieDisasterVictim
  randomizeName: false
  components:
    - type: NpcFactionMember
      factions:
      - Syndicate
    - type: GhostRole
      name: ghost-role-information-syndie-disaster-victim-name
      description: ghost-role-information-syndie-disaster-victim-description
      rules: ghost-role-information-syndie-disaster-victim-rules
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: Loadout
      prototypes: [ SyndicateOperativeGearCivilian ]
    - type: RandomMetadata
      nameSegments:
      - names_first
      - names_last
