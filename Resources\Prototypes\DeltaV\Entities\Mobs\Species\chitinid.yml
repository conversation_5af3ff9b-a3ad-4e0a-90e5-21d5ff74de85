- type: entity
  save: false
  name: "Уріст МакАнт"
  parent: BaseMobSpeciesOrganic
  id: BaseMobChitinid
  abstract: true
  components:
  - type: HumanoidAppearance
    species: Chitinid
    hideLayersOnEquip:
    - HeadTop

  - type: Hunger
    baseDecayRate: 0.0467 #needs to eat more to survive
  - type: Thirst

  - type: Carriable

  - type: Icon
    sprite: DeltaV/Mobs/Species/Chitinid/parts.rsi
    state: full

  - type: Body
    prototype: Chitinid
    requiredLegs: 2

  - type: Damageable
    damageContainer: Biological
    damageModifierSet: Chitinid

  - type: Speech
    speechVerb: Chitinid
    allowedEmotes: ['Chitter', 'Click', 'Hiss']

  - type: MeleeWeapon
    animation: WeaponArcBite
    soundHit:
      path: /Audio/Effects/bite.ogg
    damage:
      types:
        Piercing: 5


  - type: TypingIndicator
    proto: Chitinid

  - type: Butcherable
    butcheringType: Spike
    spawned:
    - id: FoodMeat
      amount: 5

  - type: Bloodstream
    bloodReagent: InsectBlood

  - type: DamageVisuals
    damageOverlayGroups:
      Brute:
        sprite: Mobs/Effects/brute_damage.rsi
        color: "#808A51"
      Burn:
        sprite: Mobs/Effects/burn_damage.rsi

  - type: Vocal
    sounds:
      Male: UnisexChitinid
      Female: UnisexChitinid
      Unsexed: UnisexChitinid

  - type: Fixtures
    fixtures: # TODO: This needs a second fixture just for mob collisions.
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.42
        density: 220
        restitution: 0.0
        mask:
        - MobMask
        layer:
        - MobLayer

  - type: Temperature # Ants hate the cold
    heatDamageThreshold: 320
    coldDamageThreshold: 230
    currentTemperature: 310.15
    specificHeat: 46
    coldDamage:
      types:
        Cold : 1.25 #per second, scales with temperature & other constants
    heatDamage:
      types:
        Heat : 1.0 #per second, scales with temperature & other constants
  - type: TemperatureSpeed
    thresholds:
      289: 0.6
      275: 0.4
      250: 0.3

  - type: Sprite # sprite again because we want different layer ordering
    noRot: true
    drawdepth: Mobs
    layers:
      - map: [ "enum.HumanoidVisualLayers.Chest" ]
      - map: [ "enum.HumanoidVisualLayers.Head" ]
      - map: [ "enum.HumanoidVisualLayers.Snout" ]
      - map: [ "enum.HumanoidVisualLayers.Eyes" ]
      - map: [ "enum.HumanoidVisualLayers.RArm" ]
      - map: [ "enum.HumanoidVisualLayers.LArm" ]
      - map: [ "enum.HumanoidVisualLayers.RLeg" ]
      - map: [ "enum.HumanoidVisualLayers.LLeg" ]
      - map: [ "jumpsuit" ]
      - map: [ "enum.HumanoidVisualLayers.LHand" ]
      - map: [ "enum.HumanoidVisualLayers.RHand" ]
      - map: [ "enum.HumanoidVisualLayers.LFoot" ]
      - map: [ "enum.HumanoidVisualLayers.RFoot" ]
      - map: [ "enum.HumanoidVisualLayers.Handcuffs" ]
        color: "#ffffff"
        sprite: Objects/Misc/handcuffs.rsi
        state: body-overlay-2
        visible: false
      - map: [ "gloves" ]
      - map: [ "shoes" ]
      - map: [ "ears" ]
      - map: [ "outerClothing" ]
      - map: [ "eyes" ]
      - map: [ "belt" ]
      - map: [ "id" ]
      - map: [ "back" ] 
      - map: [ "neck" ]
      - map: [ "enum.HumanoidVisualLayers.Tail" ] #in the utopian future we should probably have a wings enum inserted here so everyhting doesn't break
      - map: [ "enum.HumanoidVisualLayers.FacialHair" ]
      - map: [ "enum.HumanoidVisualLayers.Hair" ]
      - map: [ "enum.HumanoidVisualLayers.HeadSide" ]
      - map: [ "enum.HumanoidVisualLayers.HeadTop" ]
      - map: [ "mask" ]
      - map: [ "head" ]
      - map: [ "pocket1" ]
      - map: [ "pocket2" ]
      - map: [ "clownedon" ] # Dynamically generated
        sprite: "Effects/creampie.rsi"
        state: "creampie_moth"
        visible: false
  - type: Chitinid
  - type: BlockInjection
    blockReason: chitinid
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    - Chittin
    understands:
    - TauCetiBasic
    - Chittin

- type: entity
  parent: BaseSpeciesDummy
  id: MobChitinidDummy
  categories: [ HideSpawnMenu ]
  components:
  - type: HumanoidAppearance
    species: Chitinid
