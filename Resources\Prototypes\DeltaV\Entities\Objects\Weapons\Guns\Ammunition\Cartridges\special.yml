- type: entity
  id: BaseCartridgeSpecial
  name: "патрон (.38 special)"
  parent: BaseCartridge
  abstract: true
  components:
  - type: Tag
    tags:
      - Cartridge
      - CartridgeSpecial
  - type: CartridgeAmmo
    proto: BulletSpecial
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Casings/ammo_casing.rsi
    layers:
    - state: base
      map: ["enum.AmmoVisualLayers.Base"]
  - type: Appearance
  - type: SpentAmmoVisuals
  - type: StaticPrice
    price: 10

- type: entity
  id: CartridgeSpecial
  name: "патрон (.38 special)"
  parent: BaseCartridgeSpecial
  components:
  - type: CartridgeAmmo
    proto: BulletSpecial

- type: entity
  id: CartridgeSpecialPractice
  name: "патрон (.38 special practice)"
  parent: BaseCartridgeSpecial
  components:
  - type: CartridgeAmmo
    proto: BulletSpecialPractice
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#dbdbdb"

- type: entity
  id: CartridgeSpecialRubber
  name: "набій (.38 спеціальний гумовий)"
  parent: BaseCartridgeSpecial
  components:
  - type: CartridgeAmmo
    proto: BulletSpecialRubber
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#43c4f7"

- type: entity
  id: CartridgeSpecialIncendiary
  name: "набій (.38 спеціальний запальний)"
  parent: BaseCartridgeSpecial
  components:
  - type: CartridgeAmmo
    proto: BulletSpecialIncendiary
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#ff6e52"

- type: entity
  id: CartridgeSpecialUranium
  name: "патрон (.38 спеціальний урановий)"
  parent: BaseCartridgeSpecial
  components:
  - type: CartridgeAmmo
    proto: BulletSpecialUranium
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#65fe08"

- type: entity
  id: CartridgeSpecialHoly
  name: "патрон (.38 special holy)"
  parent: BaseCartridgeSpecial
  components:
  - type: CartridgeAmmo
    proto: BulletSpecialHoly
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#fff600"

- type: entity
  id: CartridgeSpecialMindbreaker
  name: "патрон (.38 special mindbreaker)"
  parent: BaseCartridgeSpecial
  components:
  - type: CartridgeAmmo
    proto: BulletSpecialMindbreaker
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#950ea9"
  - type: ChemicalAmmo
  - type: SolutionContainerManager
    solutions:
      ammo:
        reagents:
        - ReagentId: MindbreakerToxin
          Quantity: 9
  - type: SolutionTransfer
    maxTransferAmount: 9
