- type: entity
  id: SingularityGenerator
  name: "генератор гравітаційних сингулярностей"
  description: "Дивний пристрій, який при налаштуванні створює гравітаційну сингулярність."
  placement:
    mode: SnapgridCenter
  components:
  - type: Sprite
    sprite: Structures/Power/Generation/Singularity/generator.rsi
    state: icon
  - type: SingularityGenerator
  - type: InteractionOutline
  - type: Clickable
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          # Using a circle here makes it a lot easier to pull it all the way from Cargo
          !type:PhysShapeCircle
            radius: 0.45
        density: 190
        # Keep an eye on ParticlesProjectile when adjusting these
        mask:
        - MachineMask
        layer:
        - Opaque
  - type: Transform
    anchored: true
    noRot: true
  - type: Anchorable
  - type: Pullable
  - type: GuideHelp
    guides: [ Singularity, Power ]

