- type: entity
  id: ShuttleWindow
  name: "вікно шаттла"
  parent: WindowRCDResistant
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Windows/shuttle_window.rsi
  - type: Icon
    sprite: Structures/Windows/shuttle_window.rsi
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: RGlass
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 1000
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 500
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassReinforced:
            min: 1
            max: 2
          PartRodMetal:
            min: 1
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: IconSmooth
    base: swindow
  - type: Appearance
  - type: Tag
    tags:
    - ForceFixRotations # WWDP fix missing tags
    - Window # WWDP fix missing tags
    - WeldbotFixableStructure
  - type: DamageVisuals
    thresholds: [4, 8, 12]
    damageDivisor: 28
    trackAllDamage: true
    damageOverlay:
      sprite: Structures/Windows/cracks.rsi
  - type: StaticPrice
    price: 75

- type: entity
  parent: ShuttleWindow
  id: ShuttleWindowDiagonal
  suffix: diagonal
  placement:
    mode: SnapgridCenter
    snap:
    - Window
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Windows/shuttle_window_diagonal.rsi
    state: state0
  - type: IconSmooth
    mode: Diagonal
    key: walls
    base: state
  - type: Icon
    sprite: Structures/Windows/shuttle_window_diagonal.rsi
    state: state0
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PolygonShape
            vertices:
            - "-0.5,-0.5"
            - "0.5,0.5"
            - "0.5,-0.5"
        mask:
        - FullTileMask
        layer:
        - GlassLayer
  - type: Airtight
    noAirWhenFullyAirBlocked: false
    airBlockedDirection:
    - South
    - East
  - type: DamageVisuals
    damageOverlay:
      sprite: Structures/Windows/cracks_diagonal.rsi
