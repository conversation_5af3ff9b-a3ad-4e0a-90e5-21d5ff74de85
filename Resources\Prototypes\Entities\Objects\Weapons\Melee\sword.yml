- type: entity
  name: "<PERSON><PERSON><PERSON>"
  parent: BaseItem
  id: BaseSword
  description: "Гострий меч."
  abstract: true
  components:
  - type: Sharp
  - type: Execution
    doAfterDuration: 4.0
  - type: MeleeWeapon
    wideAnimationRotation: -135
  - type: Sprite
    state: icon
  - type: Item
    size: Normal
  - type: Utensil
    types:
      - Knife

- type: entity
  name: "шабля капітана"
  parent: BaseSword
  id: CaptainSabre
  description: "Церемоніальна зброя, що належить капітану станції."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Melee/captain_sabre.rsi
    state: icon
  - type: MeleeWeapon
    wideAnimationRotation: -135
    attackRate: .8
    range: 1.8
    soundHit:
      path: /Audio/SimpleStation14/Weapons/Melee/rapierhit.ogg
    damage:
      types:
        Slash: 15
    clickPartDamageMultiplier: 3
    heavyPartDamageMultiplier: 2.5
    heavyRateModifier: 1.25
    lightRangeModifier: 1.25
    heavyDamageBaseModifier: 1
    heavyStaminaCost: 2.5
    maxTargets: 7
    angle: 80
  - type: DamageOtherOnHit
    staminaCost: 5
  - type: EmbeddableProjectile
  - type: EmbedPassiveDamage
  - type: ThrowingAngle
    angle: 225
  - type: Reflect
    reflectProb: .1
    spread: 90
  - type: Item
    size: Normal
    sprite: Objects/Weapons/Melee/captain_sabre.rsi
  - type: Tag
    tags:
    - CaptainSabre
  - type: DisarmMalus
  - type: SentienceTarget
    flavorKind: station-event-random-sentience-flavor-inanimate
    weight: 0.02 # 5,000 times less likely than 1 regular animal
  - type: PirateAccent
    # not putting a BlockMovement component here cause that's funny.

- type: entity
  name: "катана"
  parent: BaseSword
  id: Katana
  description: "Старовинний ремісничий виріб із не такої вже й давньої пласталі."
  components:
  - type: Tag
    tags:
    - Katana
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Melee/katana.rsi #DeltaV
    state: icon
  - type: MeleeWeapon
    wideAnimationRotation: -135
    attackRate: .6
    soundHit:
      path: /Audio/SimpleStation14/Weapons/Melee/rapierhit.ogg
    damage:
      types:
        Slash: 12
    heavyRateModifier: 4
    lightRangeModifier: 1.25
    heavyDamageBaseModifier: 2
    heavyStaminaCost: 10
    maxTargets: 1
    angle: 20
  - type: DamageOtherOnHit
    staminaCost: 10
  - type: EmbeddableProjectile
  - type: EmbedPassiveDamage
  - type: ThrowingAngle
    angle: 225
  - type: Item
    size: Normal
    sprite: DeltaV/Objects/Weapons/Melee/katana.rsi #DeltaV
  - type: DisarmMalus

- type: entity
  name: "енергетична катана"
  parent: Katana
  id: EnergyKatana
  description: "Катана, наповнена сильною енергією."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Melee/energykatana.rsi
    state: icon
  - type: MeleeWeapon
    wideAnimationRotation: -60
    damage:
      types:
        Slash: 25
  - type: ThrowingAngle
    angle: 300
  - type: Item
    size: Normal
    sprite: Objects/Weapons/Melee/energykatana.rsi
  - type: EnergyKatana
  - type: DashAbility
  - type: LimitedCharges
    maxCharges: 3
    charges: 3
  - type: AutoRecharge
    rechargeDuration: 20
  - type: Clothing
    sprite: Objects/Weapons/Melee/energykatana.rsi
    slots:
    - Back
    - Belt
  - type: Reflect
    reflectProb: 0.5
    velocityBeforeNotMaxProb: 6.0 # don't punish ninjas for being ninjas
    velocityBeforeMinProb: 10.0

- type: entity
  name: "мачете"
  parent: BaseSword
  id: Machete
  description: "Велике, злісне на вигляд лезо."
  components:
  - type: Tag
    tags:
    - Machete
  - type: Sprite
    sprite: Objects/Weapons/Melee/machete.rsi
    state: icon
  - type: MeleeWeapon
    wideAnimationRotation: -135
    attackRate: 1.25
    damage:
      types:
        Slash: 15
    heavyRateModifier: 1.25
    heavyRangeModifier: 1.25
    heavyDamageBaseModifier: 1.2
    heavyStaminaCost: 2.5
    angle: 80
    soundHit:
        path: /Audio/Weapons/bladeslice.ogg
  - type: DamageOtherOnHit
    staminaCost: 5
  - type: EmbeddableProjectile
  - type: EmbedPassiveDamage
  - type: ThrowingAngle
    angle: 225
  - type: Item
    size: Normal
    sprite: Objects/Weapons/Melee/machete.rsi

- type: entity
  name: "клеймор"
  parent: BaseSword
  id: Claymore
  description: "Стародавній бойовий клинок."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Melee/claymore.rsi
    state: icon
  - type: MeleeWeapon
    wideAnimationRotation: -135
    attackRate: 1.5
    range: 1.85
    damage:
      types:
        Slash: 19
        Blunt: 1
    bluntStaminaDamageFactor: 25.0
    heavyRateModifier: 4
    heavyRangeModifier: 1.3
    heavyDamageBaseModifier: 1
    heavyStaminaCost: 10
    maxTargets: 10
    angle: 200
    soundHit:
        path: /Audio/Weapons/bladeslice.ogg
  - type: DamageOtherOnHit
    staminaCost: 18
  - type: EmbeddableProjectile
  - type: EmbedPassiveDamage
  - type: ThrowingAngle
    angle: 225
  - type: Item
    size: Normal
  - type: Clothing
    sprite: Objects/Weapons/Melee/claymore.rsi
    slots:
    - back

- type: entity
  name: "шабля"
  parent: BaseSword
  id: Cutlass
  description: "Зловісно вигнуте лезо, яке часто можна побачити в руках космічних піратів."
  components:
  - type: Tag
    tags:
    - Machete
  - type: Sprite
    sprite: Objects/Weapons/Melee/cutlass.rsi
    state: icon
  - type: MeleeWeapon
    wideAnimationRotation: -135
    attackRate: .8
    damage:
      types:
        Slash: 12
    heavyRateModifier: 1.25
    lightRangeModifier: 1.25
    heavyDamageBaseModifier: 1.2
    heavyStaminaCost: 2.5
    maxTargets: 3
    angle: 40
    soundHit:
        path: /Audio/Weapons/bladeslice.ogg
  - type: DamageOtherOnHit
    staminaCost: 5
  - type: EmbeddableProjectile
  - type: EmbedPassiveDamage
  - type: ThrowingAngle
    angle: 225
  - type: Item
    size: Normal
    sprite: Objects/Weapons/Melee/cutlass.rsi

- type: entity
  name: "Розбишака"
  parent: BaseSword
  id: Throngler
  description: "Навіщо комусь це робити?"
  components:
    - type: Sprite
      sprite: Objects/Weapons/Melee/Throngler2.rsi
      state: icon
    - type: MeleeWeapon
      wideAnimationRotation: -135
      attackRate: .1
      damage:
        types:
          Structural: 150
          Slash: 20 #Horror
          Blunt: 20
          Heat: 20
          Piercing: 20
          Radiation: 10
      soundHit:
        path: /Audio/Effects/explosion_small1.ogg
    - type: DamageOtherOnHit
    - type: Reflect
      reflectProb: .25
      spread: 90
    - type: Item
      size: Ginormous
      sprite: Objects/Weapons/Melee/Throngler-in-hand.rsi
