- type: entity
  abstract: true
  parent: Clothing
  id: ClothingHeadset
  name: "гарнітура"
  description: "Оновлений модульний інтерком, який одягається на голову. Приймає ключі шифрування."
  components:
  - type: ContainerContainer
    containers:
      key_slots: !type:Container
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyCommon
  - type: Headset
  - type: EncryptionKeyHolder
    keySlots: 4
  - type: Sprite
    state: icon
  - type: Item
    size: Small
  - type: Clothing
    slots:
    - ears
    sprite: Clothing/Ears/Headsets/base.rsi
  - type: GuideHelp
    guides:
    - Radio

- type: entity
  parent: ClothingHeadset
  id: ClothingHeadsetGrey
  name: "пасажирська гарнітура"
  components:
  - type: Sprite
    sprite: Clothing/Ears/Headsets/base.rsi

- type: entity
  parent: ClothingHeadset
  id: ClothingHeadsetCargo
  name: "логістична гарнітура" # DeltaV - Logistics Department replacing Cargo
  description: "Гарнітура, якою користуються працівники відділу постачання."
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyCargo
      - EncryptionKeyCommon
  - type: Sprite
    sprite: Clothing/Ears/Headsets/cargo.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/cargo.rsi

- type: entity
  parent: ClothingHeadsetCargo
  id: ClothingHeadsetMining
  name: "гарнітура шахтера"
  description: "Гарнітура, яку використовують шахтарі."
  components:
  - type: Sprite
    sprite: Clothing/Ears/Headsets/mining.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/mining.rsi

- type: entity
  parent: ClothingHeadsetCargo
  id: ClothingHeadsetQM
  name: "гарнітура логіста" # DeltaV - Logistics Department replacing Cargo
  description: "Гарнітура, якою користується логіст." # DeltaV - Logistics Department replacing Cargo
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyCargo
      - EncryptionKeyCommand
      - EncryptionKeyCommon

- type: entity
  parent: ClothingHeadset
  id: ClothingHeadsetCentCom
  name: "Гарнітура CentCom"
  description: "Гарнітура, якою користуються вищі ешелони Нанотрейзену."
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyCentCom
      - EncryptionKeyStationMaster
  - type: Sprite
    sprite: Clothing/Ears/Headsets/centcom.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/centcom.rsi

- type: entity
  parent: ClothingHeadset
  id: ClothingHeadsetCommand
  name: "командна гарнітура"
  description: "Гарнітура з командним каналом."
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyStationMaster
  - type: Sprite
    sprite: Clothing/Ears/Headsets/command.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/command.rsi

- type: entity
  parent: ClothingHeadset
  id: ClothingHeadsetEngineering
  name: "інженерна гарнітура"
  description: "Гарнітура для спілкування інженерів, поки навколо них горить станція."
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyEngineering
      - EncryptionKeyCommon
  - type: Sprite
    sprite: Clothing/Ears/Headsets/engineering.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/engineering.rsi

- type: entity
  parent: ClothingHeadsetEngineering
  id: ClothingHeadsetCE
  name: "гарнітура головного інженера"
  description: "Гарнітура для головного інженера, на якій можна ігнорувати всі екстрені виклики."
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyEngineering
      - EncryptionKeyCommand
      - EncryptionKeyCommon

- type: entity
  parent: ClothingHeadset
  id: ClothingHeadsetMedical
  name: "медична гарнітура"
  description: "Гарнітура для навченого персоналу медпункту."
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyMedical
      - EncryptionKeyCommon
  - type: Sprite
    sprite: Clothing/Ears/Headsets/medical.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/medical.rsi

- type: entity
  parent: ClothingHeadsetMedical
  id: ClothingHeadsetCMO
  name: "гарнітура cmo"
  description: "Гарнітура, якою користується ОКУ."
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyMedical
      - EncryptionKeyCommand
      - EncryptionKeyCommon

- type: entity
  parent: ClothingHeadset
  id: ClothingHeadsetScience
  name: "епістемологічна гарнітура" # DeltaV - Epistemics Department replacing Science
  description: "Наукова гарнітура. Як завжди." # DeltaV - Epistemics Department replacing Science
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyScience
      - EncryptionKeyCommon
  - type: Sprite
    sprite: Clothing/Ears/Headsets/science.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/science.rsi

- type: entity
  parent: ClothingHeadsetScience
  id: ClothingHeadsetMedicalScience
  name: "медична дослідницька гарнітура"
  description: "Гарнітура, яка є результатом поєднання медицини та науки."
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyMedicalScience
      - EncryptionKeyCommon
  - type: Sprite
    sprite: Clothing/Ears/Headsets/medicalscience.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/medicalscience.rsi

- type: entity
  parent: ClothingHeadsetScience
  id: ClothingHeadsetRobotics
  name: "робототехнічна гарнітура"
  description: "Зроблено спеціально для робототехніків, які не можуть вибрати між відділами."
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyRobo
      - EncryptionKeyCommon
  - type: Sprite
    sprite: Clothing/Ears/Headsets/robotics.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/robotics.rsi

- type: entity
  parent: ClothingHeadsetScience
  id: ClothingHeadsetRD
  name: "гарнітура mysta" # DeltaV - Epistemics Department replacing Science
  description: "Ламарр любив жувати це..."
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyScience
      - EncryptionKeyCommand
      - EncryptionKeyCommon

- type: entity
  parent: ClothingHeadset
  id: ClothingHeadsetSecurity
  name: "охоронна гарнітура"
  description: "Ним користується ваша елітна служба безпеки."
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeySecurity
      - EncryptionKeyCommon
  - type: Sprite
    sprite: Clothing/Ears/Headsets/security.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/security.rsi

- type: entity
  parent: ClothingHeadset
  id: ClothingHeadsetBrigmedic
  name: "санітарна гарнітура" # DeltaV - rename brigmedic to corpsman
  description: "Гарнітура, яка допомагає почути передсмертні крики."
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyMedical
      - EncryptionKeySecurity
      - EncryptionKeyCommon
  - type: Sprite
    sprite: DeltaV/Clothing/Ears/Headsets/securitymedical.rsi # DeltaV - Change the sprite to fit the others
  - type: Clothing
    sprite: DeltaV/Clothing/Ears/Headsets/securitymedical.rsi # DeltaV - Change the sprite to fit the others

- type: entity
  parent: ClothingHeadset
  id: ClothingHeadsetService
  name: "сервісна гарнітура"
  description: "Гарнітура використовується обслуговуючим персоналом, який стежить за тим, щоб станція була повною, щасливою і чистою."
  components:
  - type: ContainerFill
    containers:
      key_slots:
      - EncryptionKeyService
      - EncryptionKeyCommon
  - type: Sprite
    sprite: Clothing/Ears/Headsets/service.rsi
  - type: Clothing
    sprite: Clothing/Ears/Headsets/service.rsi

- type: entity
  parent: ClothingHeadset
  id: ClothingHeadsetFreelance
  name: "гарнітура для фрілансерів"
  description: "Це використовується роумінговою групою фрілансерів"
  components:
    - type: ContainerFill
      containers:
        key_slots:
          - EncryptionKeyFreelance
    - type: Sprite
      sprite: Clothing/Ears/Headsets/freelance.rsi
    - type: Clothing
      sprite: Clothing/Ears/Headsets/freelance.rsi
