{{!Copyright (c) Microsoft. All rights reserved. Licensed under the MIT license. See LICENSE file in the project root for full license information.}}

<footer>
  <div class="grad-bottom"></div>
  <div class="footer">
    <div class="container">
      <span class="pull-right">
        <a href="#top">Back to top</a>
      </span>
      <div class="pull-left">
        {{{_appFooter}}}
        {{^_appFooter}}<span>Generated by <strong>DocFX</strong></span>{{/_appFooter}}
      </div>
      <div class="toggle-mode pull-right visible-sm visible-xs">
        <div class="icon">
          <i aria-hidden="true">☀</i>
        </div>
        <label class="switch">
          <input type="checkbox" id="switch-style-m">
          <span class="slider round"></span>
        </label>
        <div class="icon">
          <i aria-hidden="true">☾</i>
        </div>
      </div>
    </div>
  </div>
  <script type="text/javascript" src="{{_rel}}styles/toggle-theme.js"></script>
</footer>