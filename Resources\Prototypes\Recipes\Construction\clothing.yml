- type: construction
  name: "клоунський костюм"
  id: ClownHardsuit
  graph: ClownHardsuit
  startNode: start
  targetNode: clownHardsuit
  category: construction-category-clothing
  description: "Модифікований костюм для клоуна."
  icon: { sprite: Clothing/OuterClothing/Hardsuits/clown.rsi, state: icon }
  objectType: Item

- type: construction
  name: "мімічний костюм"
  id: MimeHardsuit
  graph: MimeHardsuit
  startNode: start
  targetNode: mimeHardsuit
  category: construction-category-clothing
  description: "Модифікований костюм для міма."
  icon: { sprite: Clothing/OuterClothing/Hardsuits/mime.rsi, state: icon }
  objectType: Item

- type: construction
  name: "кістяна броня"
  id: BoneArmor
  graph: BoneArmor
  startNode: start
  targetNode: armor
  category: construction-category-clothing
  description: "Броня, зроблена з кісток."
  icon: { sprite: Clothing/OuterClothing/Armor/bone_armor.rsi, state: icon }
  objectType: Item

- type: construction
  name: "кістяний шолом"
  id: BoneHelmet
  graph: BoneHelmet
  startNode: start
  targetNode: helmet
  category: construction-category-clothing
  description: "Шолом, зроблений з кісток."
  icon: { sprite: Clothing/Head/Helmets/bone_helmet.rsi, state: icon }
  objectType: Item

- type: construction
  name: "бананова маска клоуна"
  id: BananaClownMask
  graph: BananaClownMask
  startNode: start
  targetNode: mask
  category: construction-category-clothing
  description: "Маска клоуна, покращена банановою шкіркою."
  icon: { sprite: Clothing/Mask/clown_banana.rsi, state: icon }
  objectType: Item

- type: construction
  name: "банановий костюм клоуна"
  id: BananaClownJumpsuit
  graph: BananaClownJumpsuit
  startNode: start
  targetNode: jumpsuit
  category: construction-category-clothing
  description: "Костюм клоуна, покращений банановою шкіркою."
  icon: { sprite: Clothing/Uniforms/Jumpsuit/clown_banana.rsi, state: icon }
  objectType: Item

- type: construction
  name: "бананові черевики клоуна"
  id: BananaClownShoes
  graph: BananaClownShoes
  startNode: start
  targetNode: shoes
  category: construction-category-clothing
  description: "Пара черевиків клоуна, покращених банановою шкіркою."
  icon: { sprite: Clothing/Shoes/Specific/clown_banana.rsi, state: icon }
  objectType: Item

- type: construction
  name: "медсек hud"
  id: ClothingEyesHudMedSec
  graph: HudMedSec
  startNode: start
  targetNode: medsecHud
  category: construction-category-clothing
  description: "Два HUD, з'єднані кров'ю і потом"
  icon: { sprite: Clothing/Eyes/Hud/medsec.rsi, state: icon }
  objectType: Item

- type: construction
  name: "качині капці"
  id: ClothingShoeSlippersDuck
  graph: ClothingShoeSlippersDuck
  startNode: start
  targetNode: shoes
  category: construction-category-clothing
  description: "Затишні, але переслідувані привидами качок, яких ви годували хлібом у дитинстві."
  icon: { sprite: Clothing/Shoes/Misc/duck-slippers.rsi, state: icon }
  objectType: Item

- type: construction
  name: "тканинні обгортання"
  id: ClothingClothWrap
  graph: ClothingClothWrap
  startNode: start
  targetNode: shoes
  category: construction-category-clothing
  description: "Рулон обробленого полотна, що використовується для обмотування кігтів або лап."
  icon: { sprite: Clothing/Shoes/Misc/clothWrap.rsi, state: icon }
  objectType: Item

- type: construction
  name: "сагайдак"
  id: ClothingBeltQuiver
  graph: Quiver
  startNode: start
  targetNode: Quiver
  category: construction-category-clothing
  description: "Вміщує до 15 стріл і щільно облягає талію."
  icon: { sprite: Clothing/Belt/quiver.rsi, state: icon }
  objectType: Item
