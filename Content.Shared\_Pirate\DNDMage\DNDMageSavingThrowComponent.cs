using Robust.Shared.GameStates;
using Robust.Shared.Serialization;

namespace Content.Shared._Pirate.DNDMage;

/// <summary>
/// Компонент, який додається до мага ДНД для обробки автоматичного "Saving Throw".
/// </summary>
[RegisterComponent, NetworkedComponent, AutoGenerateComponentState, Access(typeof(SharedDNDMageSavingThrowSystem))]
public sealed partial class DNDMageSavingThrowComponent : Component
{
    /// <summary>
    /// Чи активований автоматичний "Saving Throw".
    /// </summary>
    [DataField("savingThrowEnabled"), AutoNetworkedField]
    public bool SavingThrowEnabled = true;

    /// <summary>
    /// Час останнього використання "Saving Throw".
    /// </summary>
    [DataField("lastSavingThrowTime"), AutoNetworkedField]
    public TimeSpan LastSavingThrowTime = TimeSpan.Zero;

    /// <summary>
    /// Кулдаун між використаннями "Saving Throw".
    /// </summary>
    [DataField("savingThrowCooldown"), AutoNetworkedField]
    public TimeSpan SavingThrowCooldown = TimeSpan.FromMinutes(2);
}
