- type: entity
  name: "BaseSMG"
  parent: BaseItem
  id: BaseWeaponSubMachineGun
  description: "Корінцевий зубастий і пагонистий."
  abstract: true
  components:
  - type: Sprite
  - type: Item
    size: Large
  - type: Clothing
    sprite: Objects/Weapons/Guns/SMGs/atreides.rsi
    quickEquip: false
    slots:
    - Back
    - suitStorage
  - type: AmmoCounter
  - type: Gun
    minAngle: 2
    maxAngle: 16
    fireRate: 8
    burstFireRate: 8
    angleIncrease: 3
    angleDecay: 16
    selectedMode: FullAuto
    availableModes:
    - SemiAuto
    - FullAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/smg.ogg
    defaultDirection: 1, 0
  - type: ChamberMagazineAmmoProvider
    soundRack:
      path: /Audio/Weapons/Guns/Cock/smg_cock.ogg
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        startingItem: MagazinePistolSubMachineGun
        insertSound: /Audio/Weapons/Guns/MagIn/smg_magin.ogg
        ejectSound: /Audio/Weapons/Guns/MagOut/smg_magout.ogg
        priority: 2
        whitelist:
          tags:
            - MagazinePistolSubMachineGun
      gun_chamber:
        name: Chamber
        startingItem: CartridgePistol
        priority: 1
        whitelist:
          tags:
            - CartridgePistol
  - type: ContainerContainer
    containers:
      gun_magazine: !type:ContainerSlot
      gun_chamber: !type:ContainerSlot
  - type: StaticPrice
    price: 500
  - type: Execution
  - type: MeleeWeapon
    attackRate: 1.3333
    damage:
      types:
        Blunt: 9.0
    bluntStaminaDamageFactor: 1.25
    swapKeys: true
    disableHeavy: true
    animation: WeaponArcThrust
    wideAnimationRotation: 180
    soundHit:
      collection: MetalThud
  - type: IncreaseDamageOnWield
    damage:
      types:
        Blunt: 2.5
  - type: DamageOtherOnHit
    staminaCost: 8
    # PIRATE START
  - type: CanHoldAccessories
    whiteListTags:
      - CombatKnife
  - type: CanTakeAim
    # PIRATE END

- type: entity
  name: "Атрейдс"
  parent: BaseWeaponSubMachineGun
  id: WeaponSubMachineGunAtreides
  description: "Пла-кет-кет-кет-кет-кет! Використовує автоматичні набої 35-го калібру."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/SMGs/atreides.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-0
      map: ["enum.GunVisualLayers.Mag"]
  - type: Gun
    fireRate: 10
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/atreides.ogg
    fireOnDropChance: 0.3
  - type: MagazineVisuals
    magState: mag
    steps: 1
    zeroVisible: true
  - type: Appearance

- type: entity
  name: "Пістолет-кулемет C-20r"
  parent: [BaseWeaponSubMachineGun, BaseGunWieldable]
  id: WeaponSubMachineGunC20r
  description: "С-20r, вироблений компанією Cybersun-Armaments, є однією з найпопулярніших особистих стрілецьких рушниць в Коаліції Колоній. Використовує автоматичні набої калібру .35."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/SMGs/c20r.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-0
      map: ["enum.GunVisualLayers.Mag"]
  - type: Clothing
    sprite: Objects/Weapons/Guns/SMGs/c20r.rsi
  - type: Wieldable
  - type: GunWieldBonus
    minAngle: -19
    maxAngle: -16
  - type: Gun
    minAngle: 21
    maxAngle: 32
    fireRate: 10
    shotsPerBurst: 5
    availableModes:
    - SemiAuto
    - Burst
    - FullAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/c-20r.ogg
    fireOnDropChance: 0.3
  - type: ChamberMagazineAmmoProvider
    autoEject: true
  - type: MagazineVisuals
    magState: mag
    steps: 6
    zeroVisible: true
  - type: Appearance

- type: entity
  name: "Пістолет-кулемет C-20r"
  parent: [WeaponSubMachineGunC20r]
  id: WeaponSubMachineGunC20rEmpty
  description: "С-20r, вироблений компанією Cybersun-Armaments, є однією з найпопулярніших особистих стрілецьких рушниць в Коаліції Колоній. Використовує автоматичні набої калібру .35."
  components:
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        startingItem: null
        insertSound: /Audio/Weapons/Guns/MagIn/smg_magin.ogg
        ejectSound: /Audio/Weapons/Guns/MagOut/smg_magout.ogg
        priority: 2
        whitelist:
          tags:
            - MagazinePistolSubMachineGun
      gun_chamber:
        name: Chamber
        startingItem: null
        priority: 1
        whitelist:
          tags:
            - CartridgePistol

- type: entity
  name: "антикварний пістолет-кулемет С-20r"
  parent: WeaponSubMachineGunC20r
  id: WeaponSubMachineGunC20rHoS
  description: "На цьому сильно зношеному пістолеті-кулеметі викарбувано слова: \"Пам'ятай про Марс\"."
  components:
    - type: StealTarget
      stealGroup: HoSAntiqueWeapon

- type: entity
  name: "Дрозд"
  parent: BaseWeaponSubMachineGun
  id: WeaponSubMachineGunDrozd
  description: "Чудовий повністю автоматичний важкий кулемет Heavy SMG."
  components:
    - type: Sprite
      sprite: Objects/Weapons/Guns/SMGs/drozd.rsi
      layers:
        - state: base
          map: ["enum.GunVisualLayers.Base"]
        - state: mag-0
          map: ["enum.GunVisualLayers.Mag"]
    - type: Clothing
      sprite: Objects/Weapons/Guns/SMGs/drozd.rsi
    - type: Wieldable
      unwieldOnUse: false
    - type: GunWieldBonus
      minAngle: -19
      maxAngle: -16
    - type: Gun
      minAngle: 21
      maxAngle: 32
      fireRate: 12
      burstFireRate: 12
      selectedMode: FullAuto
      soundGunshot:
        path: /Audio/Weapons/Guns/Gunshots/atreides.ogg
      availableModes:
        - FullAuto
    - type: ItemSlots
      slots:
        gun_magazine:
          name: Magazine
          startingItem: MagazinePistolSubMachineGun
          insertSound: /Audio/Weapons/Guns/MagIn/smg_magin.ogg
          ejectSound: /Audio/Weapons/Guns/MagOut/smg_magout.ogg
          priority: 2
          whitelist:
            tags:
              - MagazinePistolSubMachineGun
        gun_chamber:
          name: Chamber
          startingItem: CartridgePistol
          priority: 1
          whitelist:
            tags:
              - CartridgePistol
    - type: MagazineVisuals
      magState: mag
      steps: 1
      zeroVisible: true
    - type: Appearance

#Commented out the Vector, as it is deprecated.
# - type: entity
#  name: Vector
#  parent: BaseWeaponSubMachineGun
#  id: WeaponSubMachineGunVector
#  suffix: Deprecated use Drozd
#  description: An excellent fully automatic Heavy SMG. Uses .45 magnum ammo.
#  components:
#  - type: Sprite
#    sprite: Objects/Weapons/Guns/SMGs/vector.rsi
#    layers:
#      - state: base
#        map: ["enum.GunVisualLayers.Base"]
#      - state: mag-0
#        map: ["enum.GunVisualLayers.Mag"]
#  - type: Clothing
#    sprite: Objects/Weapons/Guns/SMGs/vector.rsi
#  - type: Gun
#    fireRate: 6
#    selectedMode: FullAuto
#    soundGunshot:
#      path: /Audio/Weapons/Guns/Gunshots/atreides.ogg
#    availableModes:
#    - FullAuto
#    fireOnDropChance: 0.1
#  - type: ItemSlots
#    slots:
#      gun_magazine:
#        name: Magazine
#        startingItem: MagazineMagnumSubMachineGun
#        insertSound: /Audio/Weapons/Guns/MagIn/smg_magin.ogg
#        ejectSound: /Audio/Weapons/Guns/MagOut/smg_magout.ogg
#        priority: 2
#       whitelist:
#          tags:
#            - MagazineMagnumSubMachineGun
#      gun_chamber:
#        name: Chamber
#        startingItem: CartridgeMagnum
#        priority: 1
#        whitelist:
#          tags:
#            - CartridgeMagnum
#  - type: MagazineVisuals
#    magState: mag
##    steps: 1
#    zeroVisible: true
#  - type: Appearance

- type: entity
  name: "WT550"
  parent: BaseWeaponSubMachineGun
  id: WeaponSubMachineGunWt550
  description: "Відмінний SMG, вироблений підрозділом стрілецької зброї компанії NanoTrasen. Використовує автомобільні набої калібру .35."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/SMGs/wt550.rsi
    layers:
      - state: base
        map: ["enum.GunVisualLayers.Base"]
      - state: base-unshaded
        map: ["enum.GunVisualLayers.BaseUnshaded"]
        shader: unshaded
      - state: mag-0
        map: ["enum.GunVisualLayers.Mag"]
      - state: mag-unshaded-0
        map: ["enum.GunVisualLayers.MagUnshaded"]
        shader: unshaded
  - type: Clothing
    sprite: Objects/Weapons/Guns/SMGs/wt550.rsi
  - type: ChamberMagazineAmmoProvider
    boltClosed: null
  - type: Gun
    fireRate: 7
    minAngle: 1
    maxAngle: 6
    angleIncrease: 1.5
    angleDecay: 6
    selectedMode: FullAuto
    shotsPerBurst: 5
    burstCooldown: 0.2
    burstFireRate: 7
    availableModes:
    - SemiAuto
    - Burst
    - FullAuto
    fireOnDropChance: 0.1
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        startingItem: MagazinePistolSubMachineGunTopMounted
        insertSound: /Audio/Weapons/Guns/MagIn/smg_magin.ogg
        ejectSound: /Audio/Weapons/Guns/MagOut/smg_magout.ogg
        priority: 2
        whitelist:
          tags:
            - MagazinePistolSubMachineGunTopMounted
      gun_chamber:
        name: Chamber
        startingItem: CartridgePistol
        priority: 1
        whitelist:
          tags:
            - CartridgePistol
  - type: MagazineVisuals
    magState: mag
    steps: 6
    zeroVisible: true
  - type: Appearance

- type: entity
  name: "антикварний WT550"
  id: WeaponSubMachineGunWt550HoS
  parent: WeaponSubMachineGunWt550
  description: "Цінна річ голови безпеки. Запах засохлої крові зберігається на дулі цієї зброї. На руків'ї викарбувано маленький факел з 24 зірками навколо нього."
  components:
    - type: StealTarget
      stealGroup: HoSAntiqueWeapon


# Rubber
- type: entity
  name: "Дрозд"
  parent: WeaponSubMachineGunDrozd
  id: WeaponSubMachineGunDrozdRubber
  suffix: Non-Lethal
  components:
    - type: ItemSlots
      slots:
        gun_magazine:
          name: Magazine
          startingItem: MagazinePistolSubMachineGunRubber
          insertSound: /Audio/Weapons/Guns/MagIn/smg_magin.ogg
          ejectSound: /Audio/Weapons/Guns/MagOut/smg_magout.ogg
          priority: 2
          whitelist:
            tags:
              - MagazinePistolSubMachineGun
        gun_chamber:
          name: Chamber
          startingItem: CartridgePistolRubber
          priority: 1
          whitelist:
            tags:
              - CartridgePistol

#- type: entity
#  name: Vector
#  parent: WeaponSubMachineGunVector
#  id: WeaponSubMachineGunVectorRubber
#  description: An excellent fully automatic Heavy SMG. Uses .45 magnum ammo.
#  suffix: Non-Lethal
#  components:
#  - type: ItemSlots
#    slots:
#      gun_magazine:
#        name: Magazine
#        startingItem: MagazineMagnumSubMachineGunRubber
#        insertSound: /Audio/Weapons/Guns/MagIn/smg_magin.ogg
#        ejectSound: /Audio/Weapons/Guns/MagOut/smg_magout.ogg
#        priority: 2
#        whitelist:
#          tags:
#            - MagazineMagnumSubMachineGun
#      gun_chamber:
#        name: Chamber
#        startingItem: CartridgeMagnumRubber
#        priority: 1
#        whitelist:
#          tags:
#            - CartridgeMagnum
