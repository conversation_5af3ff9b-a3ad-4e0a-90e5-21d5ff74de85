- type: entity
  id: BoxBase
  parent: BaseStorageItem
  abstract: true
  components:
  - type: Sprite
    sprite: Objects/Storage/boxes.rsi
  - type: Item
    sprite: Objects/Storage/boxes.rsi
    size: Large
    shape:
    - 0,0,2,2
  - type: Storage
    maxItemSize: Small
    grid:
    - 0,0,2,2
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
  - type: Construction
    graph: BoxCardboard
    node: boxcardboard
    containers:
      - entity_storage
  - type: PhysicalComposition
    materialComposition:
      Cardboard: 100
  - type: StaticPrice
    price: 10
