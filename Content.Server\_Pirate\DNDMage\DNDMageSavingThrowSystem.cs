using Content.Server.Stunnable;
using Content.Shared._Pirate.DNDMage;
using Content.Shared.Mobs;
using Content.Shared.Mobs.Components;
using Content.Shared.Mobs.Systems;
using Robust.Shared.GameStates;
using Robust.Shared.Timing;

namespace Content.Server._Pirate.DNDMage;

/// <summary>
/// Система для обробки компонента DNDMageSavingThrowComponent.
/// </summary>
public sealed class DNDMageSavingThrowSystem : SharedDNDMageSavingThrowSystem
{
    [Dependency] private readonly DNDDiceSystem _dndDiceSystem = default!;
    [Dependency] private readonly IGameTiming _timing = default!;

    public override void Initialize()
    {
        base.Initialize();

        // Підписуємось на подію зміни стану моба для автоматичного "Saving Throw"
        SubscribeLocalEvent<DNDMageSavingThrowComponent, MobStateChangedEvent>(OnMobStateChanged);
    }



    /// <summary>
    /// Обробляє подію зміни стану моба для автоматичного "Saving Throw"
    /// </summary>
    private void OnMobStateChanged(EntityUid uid, DNDMageSavingThrowComponent component, MobStateChangedEvent args)
    {
        // Перевіряємо, чи активований автоматичний "Saving Throw"
        if (!component.SavingThrowEnabled)
            return;

        // Перевіряємо, чи моб перейшов у критичний стан
        if (args.NewMobState != MobState.Critical && args.NewMobState != MobState.SoftCritical)
            return;

        // Перевіряємо кулдаун (щоб уникнути спаму)
        var currentTime = _timing.CurTime;
        if (currentTime - component.LastSavingThrowTime < component.SavingThrowCooldown)
            return;

        // Оновлюємо час останнього використання
        component.LastSavingThrowTime = currentTime;
        Dirty(uid, component);

        // Делегуємо обробку DNDDiceSystem з безпечною перевіркою
        if (EntityManager.EntityExists(uid) && !Deleted(uid))
        {
            _dndDiceSystem.TryPerformSavingThrow(uid);
        }
    }
}
