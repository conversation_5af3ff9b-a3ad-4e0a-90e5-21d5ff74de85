# Base SMES
- type: entity
  abstract: true
  id: BaseSMES
  parent: [ BaseMachine, ConstructibleMachine ]
  name: "SMES"
  description: "Надпровідний магнітний накопичувач енергії (SMES) великої ємності."
  placement:
    mode: SnapgridCenter
  components:
    - type: AmbientSound
      volume: -7
      range: 3
      sound:
        path: /Audio/Ambience/Objects/periodic_beep.ogg
    - type: Sprite
      sprite: Structures/Power/smes.rsi
      snapCardinals: true
      layers:
        - state: smes
        - map: ["enum.SmesVisualLayers.Charge"]
          state: "smes-og1" # -og0 does not exist
          shader: unshaded
          visible: false
        - map: ["enum.SmesVisualLayers.Input"]
          state: "smes-oc0"
          shader: unshaded
        - map: ["enum.SmesVisualLayers.Output"]
          state: "smes-op1"
          shader: unshaded
    - type: Smes
    - type: UpgradeBattery
      maxChargeMultiplier: 2
      baseMaxCharge: 120000000
    - type: UpgradePowerSupplyRamping
      scaling: Linear
      supplyRampingMultiplier: 1
    - type: Appearance
    - type: Battery
      startingCharge: 0
    - type: ExaminableBattery
    - type: NodeContainer
      examinable: true
      nodes:
        input:
          !type:CableTerminalPortNode
          nodeGroupID: HVPower
        output:
          !type:CableDeviceNode
          nodeGroupID: HVPower
    - type: PowerMonitoringDevice
      group: SMES
      sourceNode: input
      loadNode: output
      collectionName: smes
      sprite: Structures/Power/smes.rsi
      state: static
    - type: BatteryDischarger
      voltage: High
      node: output
    - type: BatteryCharger
      voltage: High
      node: input
    - type: PowerNetworkBattery
      maxSupply: 750000
      maxChargeRate: 25000
      supplyRampTolerance: 25000
      supplyRampRate: 5000
    - type: PointLight
      radius: 1.5
      energy: 1.6
      color: "#c9c042"
      castShadows: false
    - type: WiresPanel
    - type: Machine
      board: SMESMachineCircuitboard
    - type: StationInfiniteBatteryTarget
    - type: Electrified
      onHandInteract: false
      onInteractUsing: false
      onBump: false
      requirePower: true
      highVoltageNode: input
      mediumVoltageNode: output
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: StrongMetallic
    - type: BatterySensor
    - type: DeviceNetwork
      deviceNetId: AtmosDevices
      receiveFrequencyId: AtmosMonitor
      transmitFrequencyId: AtmosMonitor
      prefix: device-address-prefix-smes
      sendBroadcastAttemptEvent: true
      examinableAddress: true
    - type: WiredNetworkConnection

# SMES' in use

- type: entity
  parent: BaseSMES
  id: SMESBasic
  suffix: Basic, 120MW
  components:
  - type: Battery
    maxCharge: 120000000
    startingCharge: 120000000

- type: entity
  parent: SMESBasic
  id: SMESBasicEmpty
  suffix: Empty
  components:
  - type: Battery
    startingCharge: 0

- type: entity
  parent: BaseSMES
  id: SMESAdvanced
  suffix: Advanced, 480MJ
  name: "просунутий SMES"
  description: "Ще більш потужний надпровідний магнітний накопичувач енергії (SMES)."
  components:
  - type: Sprite
    sprite: Structures/Power/smes.rsi
    snapCardinals: true
    layers:
    - state: advancedsmes
    - map: [ "enum.SmesVisualLayers.Charge" ]
      state: "smes-og1" # -og0 does not exist
      shader: unshaded
      visible: false
    - map: [ "enum.SmesVisualLayers.Input" ]
      state: "smes-oc0"
      shader: unshaded
    - map: [ "enum.SmesVisualLayers.Output" ]
      state: "smes-op1"
      shader: unshaded
    - map: ["enum.WiresVisualLayers.MaintenancePanel"]
      state: advancedsmes-open
  - type: Machine
    board: SMESAdvancedMachineCircuitboard
  - type: Battery
    maxCharge: 240000000
    startingCharge: 240000000
  - type: PowerMonitoringDevice
    group: SMES
    sourceNode: input
    loadNode: output
    collectionName: smes
    sprite: Structures/Power/smes.rsi
    state: advancedsmes-static

- type: entity
  parent: SMESAdvanced
  id: SMESAdvancedEmpty
  suffix: Empty
  components:
  - type: Battery
    startingCharge: 0
