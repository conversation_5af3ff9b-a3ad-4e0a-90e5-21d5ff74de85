- type: entity
  parent: BaseItem
  id: FloorTileItemBase
  description: "Підлога повинна була тримати це, а не ви."
  abstract: true
  components:
  - type: Sprite
    sprite: Objects/Tiles/tile.rsi
  - type: Item
    sprite: Objects/Tiles/tile.rsi
    size: Normal
  - type: EmbeddableProjectile
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 5.5
    staminaCost: 5
    soundHit:
      collection: MetalThud
  - type: Stack
    count: 1
  - type: Tag
    tags:
      - NoPaint
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 30
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 20
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
          params:
            volume: -8
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: DamageOnLand
    damage:
      types:
        Blunt: 2

- type: entity
  name: "сталева плитка"
  parent: FloorTileItemBase
  id: FloorTileItemSteel
  components:
  - type: Sprite
    state: steel
  - type: Item
    heldPrefix: steel
  - type: FloorTile
    outputs:
      - Plating
      - FloorSteel
  - type: Stack
    stackType: FloorTileSteel
  - type: Construction
    graph: TileSteel
    node: steeltile
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 9.5 #Metal floor tiles deal more damage than standard
    staminaCost: 6
    soundHit: /Audio/Weapons/block_metal1.ogg

- type: entity
  name: "сталева темна плитка в клітинку"
  parent: FloorTileItemSteel
  id: FloorTileItemSteelCheckerDark
  components:
  - type: Sprite
    state: checker-dark
  - type: FloorTile
    outputs:
      - Plating
      - FloorSteelCheckerDark

- type: entity
  name: "сталева світла плитка в клітинку"
  parent: FloorTileItemSteel
  id: FloorTileItemSteelCheckerLight
  components:
  - type: Sprite
    state: checker-light
  - type: FloorTile
    outputs:
      - Plating
      - FloorSteelCheckerLight

- type: entity
  name: "сталева плитка"
  parent: FloorTileItemSteel
  id: FloorTileItemMetalDiamond
  components:
  - type: Sprite
    state: metaldiamond
  - type: Item
    heldPrefix: steel
  - type: FloorTile
    outputs:
      - Plating
      - FloorMetalDiamond
  - type: Stack
    stackType: FloorTileMetalDiamond
#  - type: Construction
#    graph: TileSteel
#    node: steeltile

- type: entity
  name: "дерев'яна підлога"
  parent: FloorTileItemBase
  id: FloorTileItemWood
  components:
  - type: Sprite
    state: wood
  - type: Item
    heldPrefix: wood
  - type: FloorTile
    outputs:
      - Plating
      - FloorWood
  - type: Stack
    stackType: FloorTileWood
  - type: Construction
    graph: TileWood
    node: woodtile

- type: entity
  name: "біла плитка"
  parent: FloorTileItemBase
  id: FloorTileItemWhite
  components:
  - type: Sprite
    state: white
  - type: Item
    heldPrefix: white
  - type: FloorTile
    outputs:
      - Plating
      - FloorWhite
  - type: Stack
    stackType: FloorTileWhite
  - type: Construction
    graph: TileWhite
    node: whitetile

- type: entity
  name: "темна плитка"
  parent: FloorTileItemSteel
  id: FloorTileItemDark
  components:
  - type: Sprite
    state: dark
  - type: Item
    heldPrefix: dark
  - type: FloorTile
    outputs:
      - Plating
      - FloorDark
  - type: Stack
    stackType: FloorTileDark
  - type: Construction
    graph: TileDark
    node: darktile

- type: entity
  name: "технічний поверх"
  parent: FloorTileItemSteel
  id: FloorTileItemTechmaint
  components:
  - type: Sprite
    state: techfloor
  - type: Item
    heldPrefix: dark
  - type: FloorTile
    outputs:
      - Plating
      - FloorTechMaint
  - type: Stack
    stackType: FloorTileTechmaint

- type: entity
  name: "армована плитка"
  parent: FloorTileItemSteel
  id: FloorTileItemReinforced
  components:
  - type: Sprite
    state: reinforced
  - type: Item
    heldPrefix: reinforced
  - type: FloorTile
    outputs:
      - Plating
      - FloorReinforced
  - type: Stack
    stackType: FloorTileReinforced

# TODO add a catwalk tile item once tile smoothing is supported

- type: entity
  name: "моно плитка"
  parent: FloorTileItemBase
  id: FloorTileItemMono
  components:
  - type: Sprite
    state: monofloor
  - type: Item
    heldPrefix: steel
  - type: FloorTile
    outputs:
      - Plating
      - FloorMono
  - type: Stack
    stackType: FloorTileMono

- type: entity
  name: "лінолеум на підлозі"
  parent: FloorTileItemBase
  id: FloorTileItemLino
  components:
  - type: Sprite
    state: lino
  - type: Item
    heldPrefix: steel
  - type: FloorTile
    outputs:
      - Plating
      - FloorLino
  - type: Stack
    stackType: FloorTileLino

- type: entity
  name: "заповнена латунна пластина"
  parent: FloorTileItemBase
  id: FloorTileItemBrassFilled
  components:
  - type: Sprite
    state: brass-filled
  - type: Item
    heldPrefix: brass
  - type: FloorTile
    outputs:
      - PlatingBrass
      - FloorBrassFilled
  - type: Stack
    stackType: FloorTileBrassFilled
  - type: Construction
    graph: TilesBrass
    node: filledPlate

- type: entity
  name: "гладка латунна пластина"
  parent: FloorTileItemBase
  id: FloorTileItemBrassReebe
  components:
  - type: Sprite
    state: reebe
  - type: Item
    heldPrefix: brass
  - type: FloorTile
    outputs:
      - PlatingBrass
      - FloorBrassReebe
  - type: Stack
    stackType: FloorTileBrassReebe
  - type: Construction
    graph: TilesBrass
    node: reebe

- type: entity
  name: "брудна плитка"
  parent: FloorTileItemBase
  id: FloorTileItemDirty
  components:
  - type: Sprite
    state: dirty
  - type: Item
    heldPrefix: dirty
  - type: FloorTile
    outputs:
      - Plating
      - FloorSteelDirty
  - type: Stack
    stackType: FloorTileDirty

- type: entity
  name: "плитка для шахти ліфта"
  parent: FloorTileItemSteel
  id: FloorTileItemElevatorShaft
  components:
  - type: Sprite
    state: dark
  - type: Item
    heldPrefix: dark
  - type: FloorTile
    outputs:
      - Plating
      - FloorElevatorShaft
  - type: Stack
    stackType: FloorTileElevatorShaft

- type: entity
  name: "склепінчаста плитка"
  parent: FloorTileItemBase
  id: FloorTileItemRockVault
  components:
  - type: Sprite
    state: rockvault
  - type: Item
    heldPrefix: steel
  - type: FloorTile
    outputs:
      - Plating
      - FloorRockVault
  - type: Stack
    stackType: FloorTileRockVault

- type: entity
  name: "синя плитка"
  parent: FloorTileItemBase
  id: FloorTileItemBlue
  components:
  - type: Sprite
    state: blue
  - type: Item
    heldPrefix: carpet-blue
  - type: FloorTile
    outputs:
      - Plating
      - FloorBlue
  - type: Stack
    stackType: FloorTileBlue

- type: entity
  name: "вапняна плитка"
  parent: FloorTileItemBase
  id: FloorTileItemLime
  components:
  - type: Sprite
    state: lime
  - type: Item
    heldPrefix: steel
  - type: FloorTile
    outputs:
      - Plating
      - FloorSteelLime
  - type: Stack
    stackType: FloorTileLime

- type: entity
  name: "гірничодобувна плитка"
  parent: FloorTileItemBase
  id: FloorTileItemMining
  components:
  - type: Sprite
    state: mining
  - type: Item
    heldPrefix: mining
  - type: FloorTile
    outputs:
      - Plating
      - FloorMining
  - type: Stack
    stackType: FloorTileMining

- type: entity
  name: "темна гірнича плитка"
  parent: FloorTileItemSteel
  id: FloorTileItemMiningDark
  components:
  - type: Sprite
    state: miningdark
  - type: Item
    heldPrefix: miningdark
  - type: FloorTile
    outputs:
      - Plating
      - FloorMiningDark
  - type: Stack
    stackType: FloorTileMiningDark

- type: entity
  name: "легка гірничодобувна плитка"
  parent: FloorTileItemSteel
  id: FloorTileItemMiningLight
  components:
  - type: Sprite
    state: mininglight
  - type: Item
    heldPrefix: mininglight
  - type: FloorTile
    outputs:
      - Plating
      - FloorMiningLight
  - type: Stack
    stackType: FloorTileMiningLight

# Departamental
- type: entity
  name: "морозильна плитка"
  parent: FloorTileItemBase
  id: FloorTileItemFreezer
  components:
  - type: Sprite
    state: showroom
  - type: Item
    heldPrefix: showroom
  - type: FloorTile
    outputs:
      - Plating
      - FloorFreezer
  - type: Stack
    stackType: FloorTileFreezer

- type: entity
  name: "плитка для салону"
  parent: FloorTileItemBase
  id: FloorTileItemShowroom
  components:
  - type: Sprite
    state: showroom
  - type: Item
    heldPrefix: showroom
  - type: FloorTile
    outputs:
      - Plating
      - FloorShowroom
  - type: Stack
    stackType: FloorTileShowroom

- type: entity
  name: "гідроплитка"
  parent: FloorTileItemBase
  id: FloorTileItemHydro
  components:
  - type: Sprite
    state: hydro
  - type: Item
    heldPrefix: hydro
  - type: FloorTile
    outputs:
      - Plating
      - FloorHydro
  - type: Stack
    stackType: FloorTileHydro

- type: entity
  name: "барна плитка"
  parent: FloorTileItemBase
  id: FloorTileItemBar
  components:
  - type: Sprite
    state: bar
  - type: Item
    heldPrefix: steel
  - type: FloorTile
    outputs:
      - Plating
      - FloorBar
  - type: Stack
    stackType: FloorTileBar

- type: entity
  name: "клоунська плитка"
  parent: FloorTileItemBase
  id: FloorTileItemClown
  components:
  - type: Sprite
    state: clown
  - type: Item
    heldPrefix: steel
  - type: FloorTile
    outputs:
      - Plating
      - FloorClown
  - type: Stack
    stackType: FloorTileClown

- type: entity
  name: "мімічна плитка"
  parent: FloorTileItemBase
  id: FloorTileItemMime
  components:
  - type: Sprite
    state: mime
  - type: Item
    heldPrefix: steel
  - type: FloorTile
    outputs:
      - Plating
      - FloorMime
  - type: Stack
    stackType: FloorTileMime

- type: entity
  name: "кухонна плитка"
  parent: FloorTileItemBase
  id: FloorTileItemKitchen
  components:
  - type: Sprite
    state: kitchen
  - type: Item
    heldPrefix: dark
  - type: FloorTile
    outputs:
      - Plating
      - FloorKitchen
  - type: Stack
    stackType: FloorTileKitchen

- type: entity
  name: "плитка для прання"
  parent: FloorTileItemBase
  id: FloorTileItemLaundry
  components:
  - type: Sprite
    state: laundry
  - type: Item
    heldPrefix: steel
  - type: FloorTile
    outputs:
      - Plating
      - FloorLaundry
  - type: Stack
    stackType: FloorTileLaundry

# Concrete
- type: entity
  parent: FloorTileItemBase
  id: FloorTileItemConcrete
  name: "бетонна плитка"
  components:
  - type: Sprite
    state: concrete
  - type: Item
    heldPrefix: generic
  - type: FloorTile
    outputs:
    - Plating
    - FloorConcrete
  - type: Stack
    stackType: FloorTileConcrete

- type: entity
  parent: FloorTileItemBase
  id: FloorTileItemGrayConcrete
  name: "сіра бетонна плитка"
  components:
  - type: Sprite
    state: grayconcrete
  - type: Item
    heldPrefix: generic
  - type: FloorTile
    outputs:
    - Plating
    - FloorGrayConcrete
  - type: Stack
    stackType: FloorTileGrayConcrete

- type: entity
  parent: FloorTileItemBase
  id: FloorTileItemOldConcrete
  name: "стара бетонна плитка"
  components:
  - type: Sprite
    state: oldconcrete
  - type: Item
    heldPrefix: generic
  - type: FloorTile
    outputs:
    - Plating
    - FloorOldConcrete
  - type: Stack
    stackType: FloorTileOldConcrete

# Carpets
- type: entity
  name: "синя підлога аркади"
  parent: FloorTileItemBase
  id: FloorTileItemArcadeBlue
  components:
  - type: Sprite
    state: arcadeblue
  - type: Item
    heldPrefix: arcadeblue
  - type: FloorTile
    outputs:
      - Plating
      - FloorArcadeBlue
  - type: Stack
    stackType: FloorTileStackArcadeBlue

- type: entity
  name: "синя підлога аркади"
  parent: FloorTileItemBase
  id: FloorTileItemArcadeBlue2
  components:
  - type: Sprite
    state: arcadeblue2
  - type: Item
    heldPrefix: generic
  - type: FloorTile
    outputs:
      - Plating
      - FloorArcadeBlue2
  - type: Stack
    stackType: FloorTileStackArcadeBlue2

- type: entity
  name: "червона підлога аркади"
  parent: FloorTileItemBase
  id: FloorTileItemArcadeRed
  components:
  - type: Sprite
    state: arcadered
  - type: Item
    heldPrefix: arcadered
  - type: FloorTile
    outputs:
      - Plating
      - FloorArcadeRed
  - type: Stack
    stackType: FloorTileStackArcadeRed

- type: entity
  name: "вісімдесятий поверх"
  parent: FloorTileItemBase
  id: FloorTileItemEighties
  components:
  - type: Sprite
    state: eighties
  - type: Item
    heldPrefix: eighties
  - type: FloorTile
    outputs:
      - Plating
      - FloorEighties
  - type: Stack
    stackType: FloorTileStackEighties

- type: entity
  name: "клоунська килимова підлога"
  parent: FloorTileItemBase
  id: FloorTileItemCarpetClown
  components:
  - type: Sprite
    state: carpetclown
  - type: Item
    heldPrefix: generic
  - type: FloorTile
    outputs:
      - Plating
      - FloorCarpetClown
  - type: Stack
    stackType: FloorTileStackCarpetClown

- type: entity
  name: "офісна килимова підлога"
  parent: FloorTileItemBase
  id: FloorTileItemCarpetOffice
  components:
  - type: Sprite
    state: carpetoffice
  - type: Item
    heldPrefix: generic
  - type: FloorTile
    outputs:
      - Plating
      - FloorCarpetOffice
  - type: Stack
    stackType: FloorTileStackCarpetOffice

- type: entity
  name: "підлога боксерського рингу"
  parent: FloorTileItemBase
  id: FloorTileItemBoxing
  components:
  - type: Sprite
    state: boxing
  - type: Item
    heldPrefix: generic
  - type: FloorTile
    outputs:
      - Plating
      - FloorBoxing
  - type: Stack
    stackType: FloorTileStackBoxing

- type: entity
  name: "підлога спортзалу"
  parent: FloorTileItemBase
  id: FloorTileItemGym
  components:
  - type: Sprite
    state: gym
  - type: Item
    heldPrefix: generic
  - type: FloorTile
    outputs:
      - Plating
      - FloorGym
  - type: Stack
    stackType: FloorTileStackGym

# Shuttles
- type: entity
  name: "біла човникова підлога"
  parent: FloorTileItemBase
  id: FloorTileItemShuttleWhite
  components:
  - type: Sprite
    state: shuttlewhite
  - type: Item
    heldPrefix: shuttlewhite
  - type: FloorTile
    outputs:
      - Plating
      - FloorShuttleWhite
  - type: Stack
    stackType: FloorTileStackShuttleWhite

- type: entity
  name: "синій шатл-поверх"
  parent: FloorTileItemBase
  id: FloorTileItemShuttleBlue
  components:
  - type: Sprite
    state: shuttleblue
  - type: Item
    heldPrefix: shuttleblue
  - type: FloorTile
    outputs:
      - Plating
      - FloorShuttleBlue
  - type: Stack
    stackType: FloorTileStackShuttleBlue

- type: entity
  name: "помаранчевий поверх шаттла"
  parent: FloorTileItemBase
  id: FloorTileItemShuttleOrange
  components:
  - type: Sprite
    state: shuttleorange
  - type: Item
    heldPrefix: shuttleorange
  - type: FloorTile
    outputs:
      - Plating
      - FloorShuttleOrange
  - type: Stack
    stackType: FloorTileStackShuttleOrange

- type: entity
  name: "фіолетовий шатл-поверх"
  parent: FloorTileItemBase
  id: FloorTileItemShuttlePurple
  components:
  - type: Sprite
    state: shuttlepurple
  - type: Item
    heldPrefix: shuttlepurple
  - type: FloorTile
    outputs:
      - Plating
      - FloorShuttlePurple
  - type: Stack
    stackType: FloorTileStackShuttlePurple

- type: entity
  name: "червоний поверх шаттла"
  parent: FloorTileItemBase
  id: FloorTileItemShuttleRed
  components:
  - type: Sprite
    state: shuttlered
  - type: Item
    heldPrefix: shuttlered
  - type: FloorTile
    outputs:
      - Plating
      - FloorShuttleRed
  - type: Stack
    stackType: FloorTileStackShuttleRed

- type: entity
  name: "сіра підлога шатла"
  parent: FloorTileItemBase
  id: FloorTileItemShuttleGrey
  components:
  - type: Sprite
    state: shuttlegrey
  - type: Item
    heldPrefix: shuttlegrey
  - type: FloorTile
    outputs:
      - Plating
      - FloorShuttleGrey
  - type: Stack
    stackType: FloorTileStackShuttleGrey

- type: entity
  name: "чорна підлога човника"
  parent: FloorTileItemBase
  id: FloorTileItemShuttleBlack
  components:
  - type: Sprite
    state: shuttleblack
  - type: Item
    heldPrefix: shuttleblack
  - type: FloorTile
    outputs:
      - Plating
      - FloorShuttleBlack
  - type: Stack
    stackType: FloorTileStackShuttleBlack

# Materials
- type: entity
  name: "золота підлога"
  parent: FloorTileItemBase
  id: FloorTileItemGold
  components:
  - type: Sprite
    state: gold
  - type: Item
    heldPrefix: gold
  - type: FloorTile
    outputs:
      - Plating
      - FloorGold
  - type: Stack
    stackType: FloorTileGold

- type: entity
  name: "срібна плитка"
  parent: FloorTileItemBase
  id: FloorTileItemSilver
  components:
  - type: Sprite
    state: silver
  - type: Item
    heldPrefix: silver
  - type: FloorTile
    outputs:
      - Plating
      - FloorSilver
  - type: Stack
    stackType: FloorTileSilver

# Circuits
- type: entity
  name: "зелена контурна підлога"
  parent: FloorTileItemBase
  id: FloorTileItemGCircuit
  components:
  - type: Sprite
    state: gcircuit
  - type: Item
    heldPrefix: gcircuit
  - type: FloorTile
    outputs:
      - Plating
      - FloorGreenCircuit
  - type: Stack
    stackType: FloorTileGCircuit

- type: entity
  name: "синя підлога контуру"
  parent: FloorTileItemBase
  id: FloorTileItemBCircuit
  components:
  - type: Sprite
    state: bcircuit
  - type: Item
    heldPrefix: bcircuit
  - type: FloorTile
    outputs:
      - Plating
      - FloorBlueCircuit
  - type: Stack
    stackType: FloorTileBCircuit

- type: entity
  name: "підлога з червоною схемою"
  parent: FloorTileItemBase
  id: FloorTileItemRCircuit
  components:
  - type: Sprite
    state: rcircuit
  - type: Item
    heldPrefix: rcircuit
  - type: FloorTile
    outputs:
      - Plating
      - FloorRedCircuit
  - type: Stack
    stackType: FloorTileRCircuit

# Circuits stacks

- type: entity
  parent: FloorTileItemGCircuit
  id: FloorTileItemGCircuit4
  suffix: 4
  components:
  - type: Stack
    count: 4

- type: entity
  parent: FloorTileItemBCircuit
  id: FloorTileItemBCircuit4
  suffix: 4
  components:
  - type: Stack
    count: 4

# Terrain
- type: entity
  name: "трав'яна плитка"
  parent: FloorTileItemBase
  id: FloorTileItemGrass
  components:
  - type: Sprite
    state: grass
  - type: Item
    heldPrefix: grass
  - type: FloorTile
    outputs:
      - Plating
      - FloorGrass
  - type: Stack
    stackType: FloorTileGrass

- type: entity
  name: "плитка трави джунглів"
  parent: FloorTileItemBase
  id: FloorTileItemGrassJungle
  components:
  - type: Sprite
    state: grassjungle
  - type: Item
    heldPrefix: grassjungle
  - type: FloorTile
    outputs:
      - Plating
      - FloorGrassJungle
  - type: Stack
    stackType: FloorTileGrassJungle

- type: entity
  name: "снігова плитка"
  parent: FloorTileItemBase
  id: FloorTileItemSnow
  components:
  - type: Sprite
    state: snow
  - type: Item
    heldPrefix: snow
  - type: FloorTile
    outputs:
      - Plating
      - FloorSnow
  - type: Stack
    stackType: FloorTileSnow

- type: entity
  name: "підлога з дерев'яним малюнком"
  parent: FloorTileItemBase
  id: FloorTileItemWoodPattern
  components:
  - type: Sprite
    state: woodpatternfloor
  - type: Item
    heldPrefix: wood
  - type: FloorTile
    outputs:
      - Plating
      - FloorWoodTile
  - type: Stack
    stackType: FloorTileWoodPattern

- type: entity
  id: FloorTileItemFlesh
  parent: FloorTileItemBase
  name: "тілесна підлога"
  components:
  - type: Sprite
    state: meat
  - type: Item
    heldPrefix: meat
  - type: FloorTile
    outputs:
    - Plating
    - FloorFlesh
  - type: Stack
    stackType: FloorTileFlesh
  - type: Construction
    graph: TileFlesh
    node: fleshTile

- type: entity
  name: "сталева підлога для технічного обслуговування"
  parent: FloorTileItemBase
  id: FloorTileItemSteelMaint
  components:
  - type: Sprite
    state: steelmaintfloor
  - type: Item
    heldPrefix: steel
  - type: FloorTile
    outputs:
      - Plating
      - FloorTechMaint2
  - type: Stack
    stackType: FloorTileSteelMaint

- type: entity
  name: "решітчаста підлога"
  parent: FloorTileItemBase
  id: FloorTileItemGratingMaint
  components:
  - type: Sprite
    state: gratingmaintfloor
  - type: Item
    heldPrefix: steel
  - type: FloorTile
    outputs:
      - Plating
      - FloorTechMaint3
  - type: Stack
    stackType: FloorTileGratingMaint

- type: entity
  name: "павутинна плитка"
  parent: FloorTileItemBase
  id: FloorTileItemWeb
  components:
  - type: Sprite
    sprite: Objects/Tiles/web.rsi
    state: icon
  - type: FloorTile
    outputs:
      - FloorWebTile
  - type: Stack
    stackType: FloorTileWeb
  - type: Construction
    graph: WebObjects
    node: tile

# Faux science tiles

- type: entity
  id: FloorTileItemAstroGrass
  parent: FloorTileItemBase
  name: "астро-трава"
  description: "Штучна трава, яка приховує дроти і навіть має реалістичні нанообрізки!"
  components:
  - type: Sprite
    state: astrograss
  - type: Item
    heldPrefix: grass
  - type: FloorTile
    outputs:
    - Plating
    - FloorAstroGrass
  - type: Stack
    stackType: FloorTileAstroGrass

- type: entity
  id: FloorTileItemMowedAstroGrass
  parent: FloorTileItemBase
  name: "скошена астро-трава"
  description: "Штучна трава, яка приховує дроти і навіть має реалістичні нанообрізки!"
  components:
  - type: Sprite
    state: grass
  - type: Item
    heldPrefix: grass
  - type: FloorTile
    outputs:
    - Plating
    - FloorMowedAstroGrass
  - type: Stack
    stackType: FloorTileAstroGrass

- type: entity
  id: FloorTileItemJungleAstroGrass
  parent: FloorTileItemBase
  name: "астро-трава джунглів"
  description: "Штучна трава, яка приховує дроти і навіть має реалістичні нанообрізки!"
  components:
  - type: Sprite
    state: grassjungle
  - type: Item
    heldPrefix: grass
  - type: FloorTile
    outputs:
    - Plating
    - FloorJungleAstroGrass
  - type: Stack
    stackType: FloorTileAstroGrass

- type: entity
  id: FloorTileItemAstroIce
  parent: FloorTileItemBase
  name: "астро-лід"
  description: "Фальшивий лід, такий же слизький, як і справжній, але при цьому легко знімається!"
  components:
  - type: Sprite
    state: astroice
  - type: Item
    heldPrefix: snow
  - type: FloorTile
    outputs:
    - Plating
    - FloorAstroIce
  - type: Stack
    stackType: FloorTileAstroIce

- type: entity
  id: FloorTileItemAstroSnow
  parent: FloorTileItemBase
  name: "астро-сніг"
  description: "Штучний сніг, такий же пухнастий, як і справжній, але при цьому легко знімається!"
  components:
  - type: Sprite
    state: snow
  - type: Item
    heldPrefix: snow
  - type: FloorTile
    outputs:
    - Plating
    - FloorAstroSnow
  - type: Stack
    stackType: FloorTileAstroSnow

- type: entity
  name: "велика дерев'яна підлога"
  parent: FloorTileItemBase
  id: FloorTileItemWoodLarge
  components:
  - type: Sprite
    state: wood-large
  - type: Item
    heldPrefix: wood
  - type: FloorTile
    outputs:
      - Plating
      - FloorWoodLarge
  - type: Stack
    stackType: FloorTileWoodLarge
  - type: Construction
    graph: TileWoodLarge
    node: woodtilelarge
