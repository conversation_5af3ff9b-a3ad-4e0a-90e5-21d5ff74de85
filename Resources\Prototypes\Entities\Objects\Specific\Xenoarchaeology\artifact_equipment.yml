- type: entity
  id: CrateArtifactContainer
  parent: BaseStructureDynamic
  name: "контейнер для артефактів"
  description: "Використовується для безпечного зберігання та переміщення артефактів."
  components:
    - type: Transform
      noRot: true
    - type: AccessReader
      access: [["Research"], ["Salvage"]]
    - type: Lock
    - type: ResistLocker
    - type: Sprite
      drawdepth: Objects
      sprite: Structures/Storage/Crates/artifact.rsi
      layers:
      - state: artifact_container
        map: ["enum.StorageVisualLayers.Base"]
      - state: artifact_container_door
        map: ["enum.StorageVisualLayers.Door"]
      - state: welded
        visible: false
        map: ["enum.WeldableLayers.BaseWelded"]
      - state: locked
        map: ["enum.LockVisualLayers.Lock"]
        shader: unshaded
      - state: paper
        map: ["enum.PaperLabelVisuals.Layer"]
    - type: InteractionOutline
    - type: Physics
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeCircle
            radius: 0.45
          density: 50
          mask:
          - CrateMask #this is so they can go under plastic flaps
          layer:
          - MachineLayer
    - type: Icon
      sprite: Structures/Storage/Crates/artifact.rsi
      state: artifact_container_icon
    - type: EntityStorage
      capacity: 1
      whitelist:
        components:
        - Artifact
    - type: Weldable
    - type: SuppressArtifactContainer
    - type: RadiationBlockingContainer
      resistance: 5
    - type: PlaceableSurface
      isPlaceable: false
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: Metallic
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 50
          behaviors:
            - !type:DoActsBehavior
              acts: ["Destruction"]
    - type: PaperLabel
      labelSlot:
        insertVerbText: Attach Label
        ejectVerbText: Remove Label
        whitelist:
          components:
            - Paper
    - type: Appearance
    - type: EntityStorageVisuals
      stateDoorOpen: artifact_container_open
      stateDoorClosed: artifact_container_door
    - type: GenericVisualizer
      visuals:
        enum.PaperLabelVisuals.HasLabel:
          enum.PaperLabelVisuals.Layer:
            True: { visible: true }
            False: { visible: false }
        enum.PaperLabelVisuals.LabelType:
          enum.PaperLabelVisuals.Layer:
            Paper: { state: paper }
            Bounty: { state: bounty }
            CaptainsPaper: { state: captains_paper }
            Invoice: { state: invoice }
        enum.StorageVisuals.Open:
          enum.PaperLabelVisuals.Layer:
            True: { offset: "0.0,0.3125" }
            False: { offset: "0.0,0.0" }
            
    - type: LockVisuals
    - type: ItemSlots
    - type: ContainerContainer
      containers:
        entity_storage: !type:Container
        paper_label: !type:ContainerSlot
    - type: StaticPrice
      price: 250
