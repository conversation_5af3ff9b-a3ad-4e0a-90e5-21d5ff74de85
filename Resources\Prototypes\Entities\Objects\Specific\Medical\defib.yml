- type: entity
  id: BaseDefibrillator
  parent: BaseItem
  name: "дефібрилятор"
  description: "ЧИСТО! Дзззззз!"
  abstract: true
  components:
    - type: Sprite
      sprite: Objects/Specific/Medical/defib.rsi
      layers:
        - state: icon
        - state: screen
          map: [ "enum.ToggleVisuals.Layer" ]
          visible: false
          shader: unshaded
        - state: ready
          map: ["enum.PowerDeviceVisualLayers.Powered"]
          shader: unshaded
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.ToggleVisuals.Toggled:
          enum.ToggleVisuals.Layer:
            True: { visible: true }
            False: { visible: false }
        enum.DefibrillatorVisuals.Ready:
          enum.PowerDeviceVisualLayers.Powered:
            True: { visible: true }
            False: { visible: false }
    - type: Item
      size: Large
    - type: Speech
      speechVerb: Robotic
    - type: ItemToggle
      soundActivate:
        path: /Audio/Items/Defib/defib_safety_on.ogg
      soundDeactivate:
        path: /Audio/Items/Defib/defib_safety_off.ogg
    - type: Defibrillator
      zapHeal:
        types:
          Asphyxiation: -40
    - type: DoAfter
    - type: UseDelay
    - type: StaticPrice
      price: 30
    - type: GuideHelp
      guides:
      - Medical Doctor
    - type: DamageOtherOnHit
      damage:
        types:
          Blunt: 16
      staminaCost: 22.5
      soundHit:
        path: /Audio/Weapons/smash.ogg
    - type: ToggleCellDraw
    - type: LanguageSpeaker
    - type: LanguageKnowledge
      speaks:
      - TauCetiBasic
      understands:
      - TauCetiBasic

- type: entity
  id: Defibrillator
  parent: [ BaseDefibrillator, PowerCellSlotMediumItem ]
  components:
  - type: MultiHandedItem
  - type: PowerCellDraw
    useRate: 100

- type: entity
  id: DefibrillatorEmpty
  parent: Defibrillator
  suffix: Empty
  components:
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default

- type: entity
  id: DefibrillatorOneHandedUnpowered
  parent: BaseDefibrillator
  suffix: One-Handed, Unpowered

- type: entity
  id: DefibrillatorCompact # This should be a research item at some point
  parent: [ BaseDefibrillator, PowerCellSlotMediumItem ]
  name: "компактний дефібрилятор"
  description: "Тепер у веселому розмірі!"
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/defibsmall.rsi
    layers:
      - state: icon
      - state: screen
        map: [ "enum.ToggleVisuals.Layer" ]
        visible: false
        shader: unshaded
      - state: ready
        map: ["enum.PowerDeviceVisualLayers.Powered"]
        shader: unshaded
  - type: Item
    size: Normal
  - type: ToggleCellDraw
  - type: PowerCellDraw
    useRate: 100
  - type: Defibrillator
    zapHeal:
      types:
        Asphyxiation: -40
    doAfterDuration: 6
  - type: DoAfter
  - type: UseDelay

- type: entity
  id: DefibrillatorSyndicate
  parent: DefibrillatorCompact
  name: "дефібрилятор interdyne"
  description: "Подвоюється як зброя самозахисту від схильних до воєнних злочинів."
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/defibsyndi.rsi
    layers:
      - state: icon
      - state: screen
        map: [ "enum.ToggleVisuals.Layer" ]
        visible: false
        shader: unshaded
      - state: ready
        map: ["enum.PowerDeviceVisualLayers.Powered"]
        shader: unshaded
  - type: MeleeWeapon
    damage:
      types:
        Blunt: 8
  - type: ItemToggleMeleeWeapon
    activatedSoundOnHit:
      path: /Audio/Items/Defib/defib_zap.ogg
      params:
        variation: 0.250
    activatedSoundOnHitNoDamage:
      path: /Audio/Items/Defib/defib_zap.ogg
      params:
        variation: 0.250
        volume: -10
    activatedDamage:
      types:
        Blunt: 8
        Shock: 16
