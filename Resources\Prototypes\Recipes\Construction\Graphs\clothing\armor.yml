- type: constructionGraph
  id: BoneArmor
  start: start
  graph:
  - node: start
    edges:
    - to: armor
      steps:
      - material: Bones
        amount: 6
        doAfter: 2
  - node: armor
    entity: ClothingOuterArmorBone

- type: constructionGraph
  id: BoneHelmet
  start: start
  graph:
  - node: start
    edges:
    - to: helmet
      steps:
      - material: Bones
        amount: 4
        doAfter: 1
  - node: helmet
    entity: ClothingHeadHelmetBone
