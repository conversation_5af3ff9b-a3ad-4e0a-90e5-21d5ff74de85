# Base

# uses "icon" and "icon-open" states for the main item.
# trash prototypes use "trash" state
- type: entity
  abstract: true
  parent: [FoodBase, FoodOpenableBase]
  id: FoodTinBase
  name: "олово"
  description: "Бляшанка з чимось, щільно закрита."
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Nutriment
          Quantity: 15
  - type: Openable
    openableByHand: false
    sound:
      path: /Audio/Items/can_open3.ogg
  - type: Destructible
    thresholds:
    # if tinned food is nuked just delete it, no sound
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 6
      behaviors:
      - !type:OpenBehavior
  - type: Item
    sprite: Objects/Consumable/Food/snacks.rsi
    heldPrefix: packet
    size: Tiny
  - type: DamageOnLand
    damage:
      types:
        Blunt: 3
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 3
  - type: Damageable
    damageContainer: Inorganic
  - type: StaticPrice
    price: 50

- type: entity
  abstract: true
  parent: BaseItem
  id: FoodTinBaseTrash
  name: "порожня бляшанка"
  description: "Порожня бляшанка. З неї можна отримати трохи металу."
  components:
  - type: Sprite
    state: trash
  - type: Item
    sprite: Objects/Consumable/Food/snacks.rsi
    heldPrefix: packet
    size: Tiny
  - type: PhysicalComposition
    materialComposition:
      Steel: 100
  - type: Tag
    tags:
    - Trash

# Tins

# Need something that you can open these tins with. I suggest a prying or cutting tool.

- type: entity
  parent: FoodTinBase
  id: FoodTinPeaches
  name: "консервовані персики"
  description: "Просто гарна банка стиглих персиків, що плавають у власному соку."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/Tins/peaches.rsi
  - type: FlavorProfile
    flavors:
    - sweet
    - funny
  - type: Food
    trash: 
    - FoodTinPeachesTrash
  - type: Tag
    tags:
    - Fruit

- type: entity
  parent: FoodTinBaseTrash
  id: FoodTinPeachesTrash
  name: "консервовані персики"
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/Tins/peaches.rsi

# slightly run down texture and different name
- type: entity
  parent: FoodTinPeaches
  id: FoodTinPeachesMaint
  name: "мариновані персики"
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/Tins/maint_peaches.rsi
  - type: Food
    trash: 
    - FoodTinPeachesMaintTrash

# only exists for backwards compatibility with a few maps, nothing else uses it
- type: entity
  parent: FoodTinPeachesMaint
  id: FoodTinPeachesMaintOpen
  suffix: Open
  components:
  - type: Sprite
    layers:
    - state: icon-open
      map: ["icon"]
  - type: Openable
    opened: true

- type: entity
  parent: FoodTinBaseTrash
  id: FoodTinPeachesMaintTrash
  name: "мариновані персики"
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/Tins/maint_peaches.rsi

- type: entity
  parent: FoodTinBase
  id: FoodTinBeans
  name: "банка квасолі"
  description: "Музичний фрукт у трохи менш музичному контейнері."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/Tins/beans.rsi
  - type: FlavorProfile
    flavors:
    - savory
    - salty
    - cheap
  - type: Food
    trash: 
    - FoodTinBeansTrash

- type: entity
  parent: FoodTinBaseTrash
  id: FoodTinBeansTrash
  name: "банка квасолі"
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/Tins/beans.rsi

# MRE can

- type: entity
  parent: FoodTinBase
  id: FoodTinMRE
  name: "м'ясні консерви"
  description: "Стандартна бляшанка для м'яса зі зручною ручкою для витягування."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/Tins/meat.rsi
  # the convenient pull tab
  - type: Openable
    openableByHand: true
  - type: FlavorProfile
    flavors:
    - meaty
    - salty
    - cheap
  - type: Food
    trash: 
    - FoodTinMRETrash
  - type: Tag
    tags:
    - Meat

- type: entity
  parent: FoodTinBaseTrash
  id: FoodTinMRETrash
  name: "м'ясні консерви"
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/Tins/meat.rsi
