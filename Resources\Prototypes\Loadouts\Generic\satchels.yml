- type: loadout
  id: LoadoutBackpackSatchel
  category: Backpacks
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingBackpackSatchel
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutBackpacks

- type: loadout
  id: LoadoutItemBackpackSatchelLeather
  category: Backpacks
  cost: 0
  exclusive: true
  items:
    - ClothingBackpackSatchelLeather
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutBackpacks
    - !type:CharacterJobRequirement
       inverted: true
       jobs:
         - Prisoner

- type: loadout
  id: LoadoutBackpackSatchelHydroponics
  category: Backpacks
  cost: 0
  exclusive: true
  items:
    - ClothingBackpackSatchelHydroponics
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutBackpacks
    - !type:CharacterDepartmentRequirement
      departments:
        - Civilian

- type: loadout
  id: LoadoutBackpackSatchelPurse
  category: Backpacks
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingBackpackSatchelPurse
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutBackpacks
    - !type:CharacterJobRequirement
       inverted: true
       jobs:
         - Prisoner

- type: loadout
  id: LoadoutBackpackSatchelPurseFlipped
  category: Backpacks
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingBackpackSatchelPurseFlipped
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutBackpacks
    - !type:CharacterJobRequirement
       inverted: true
       jobs:
         - Prisoner
