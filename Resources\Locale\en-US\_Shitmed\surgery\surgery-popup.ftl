surgery-popup-step-SurgeryStepOpenIncisionScalpel = {$user} is making an incision on {$target}'s {$part}.
surgery-popup-step-SurgeryStepClampBleeders = {$user} is clamping the bleeders on {$target}'s {$part}.
surgery-popup-step-SurgeryStepRetractSkin = {$user} is retracting the skin on {$target}'s {$part}.
surgery-popup-step-SurgeryStepSawBones = {$user} is sawing through the bones on {$target}'s {$part}.
surgery-popup-step-SurgeryStepPriseOpenBones = {$user} is prising the bones open on {$target}'s {$part}.
surgery-popup-step-SurgeryStepCloseBones = {$user} is closing the bones on {$target}'s {$part}.
surgery-popup-step-SurgeryStepMendRibcage = {$user} is mending the ribcage on {$target}'s {$part}.
surgery-popup-step-SurgeryStepCloseIncision = {$user} is closing the incision on {$target}'s {$part}.

surgery-popup-step-SurgeryStepInsertFeature = {$user} is inserting something onto {$target}'s {$part}!
surgery-popup-procedure-SurgeryAttachHead-step-SurgeryStepInsertFeature = {$user} is attaching a head onto {$target}'s {$part}!
surgery-popup-procedure-SurgeryAttachLeftArm-step-SurgeryStepInsertFeature = {$user} is attaching a left arm onto {$target}'s {$part}!
surgery-popup-procedure-SurgeryAttachRightArm-step-SurgeryStepInsertFeature = {$user} is attaching a right arm onto {$target}'s {$part}!
surgery-popup-procedure-SurgeryAttachLeftLeg-step-SurgeryStepInsertFeature = {$user} is attaching a left leg onto {$target}'s {$part}!
surgery-popup-procedure-SurgeryAttachRightLeg-step-SurgeryStepInsertFeature = {$user} is attaching a right leg onto {$target}'s {$part}!
surgery-popup-procedure-SurgeryAttachLeftHand-step-SurgeryStepInsertFeature = {$user} is attaching a left hand onto {$target}'s {$part}!
surgery-popup-procedure-SurgeryAttachRightHand-step-SurgeryStepInsertFeature = {$user} is attaching a right hand onto {$target}'s {$part}!
surgery-popup-procedure-SurgeryAttachLeftFoot-step-SurgeryStepInsertFeature = {$user} is attaching a left foot onto {$target}'s {$part}!
surgery-popup-procedure-SurgeryAttachRightFoot-step-SurgeryStepInsertFeature = {$user} is attaching a right foot onto {$target}'s {$part}!
surgery-popup-procedure-SurgeryAttachLegs-step-SurgeryStepInsertFeature = {$user} is attaching legs onto {$target}'s {$part}!
surgery-popup-procedure-SurgeryAttachHands-step-SurgeryStepInsertFeature = {$user} is attaching hands onto {$target}'s {$part}!
surgery-popup-procedure-SurgeryAttachFeet-step-SurgeryStepInsertFeature = {$user} is attaching feet onto {$target}'s {$part}!

surgery-popup-step-SurgeryStepSealWounds = {$user} is sealing the wounds on {$target}'s {$part}.
surgery-popup-step-SurgeryStepSawFeature = {$user} is sawing through the bones on {$target}'s {$part}.
surgery-popup-step-SurgeryStepClampInternalBleeders = {$user} is clamping the internal bleeders on {$target}'s {$part}.
surgery-popup-step-SurgeryStepRemoveFeature = {$user} is amputating {$target}'s {$part}!
surgery-popup-step-SurgeryStepCarefulIncisionScalpel = {$user} is carefully making an incision on {$target}'s {$part}.
surgery-popup-step-SurgeryStepRepairBruteTissue = {$user} is repairing the damaged tissues on {$target}'s {$part}!
surgery-popup-step-SurgeryStepRepairBurnTissue = {$user} is repairing the burnt tissues on {$target}'s {$part}!
surgery-popup-step-SurgeryStepSealTendWound = {$user} is sealing the wounds on {$target}'s {$part}.
surgery-popup-step-SurgeryStepInsertItem = {$user} is inserting something into {$target}'s {$part}!
surgery-popup-step-SurgeryStepRemoveItem = {$user} is removing something from {$target}'s {$part}!

surgery-popup-step-SurgeryStepRemoveOrgan = {$user} is removing an organ from {$target}'s {$part}!
surgery-popup-step-SurgeryStepInsertOrgan = {$user} is inserting an organ into {$target}'s {$part}!

surgery-popup-procedure-SurgeryRemoveBrain-step-SurgeryStepRemoveOrgan = {$user} is removing the brain from {$target}'s {$part}!
surgery-popup-procedure-SurgeryRemoveHeart-step-SurgeryStepRemoveOrgan = {$user} is removing the heart from {$target}'s {$part}!
surgery-popup-procedure-SurgeryRemoveLiver-step-SurgeryStepRemoveOrgan = {$user} is removing the liver from {$target}'s {$part}!
surgery-popup-procedure-SurgeryRemoveLungs-step-SurgeryStepRemoveOrgan = {$user} is removing the lungs from {$target}'s {$part}!
surgery-popup-procedure-SurgeryRemoveEyes-step-SurgeryStepRemoveOrgan = {$user} is removing the eyes from {$target}'s {$part}!
surgery-popup-procedure-SurgeryRemoveStomach-step-SurgeryStepRemoveOrgan = {$user} is removing the stomach from {$target}'s {$part}!

surgery-popup-procedure-SurgeryInsertBrain-step-SurgeryStepInsertOrgan = {$user} is inserting a brain into {$target}'s {$part}!
surgery-popup-step-SurgeryStepInsertLungs = {$user} is inserting lungs into {$target}'s {$part}!
surgery-popup-step-SurgeryStepInsertLiver = {$user} is inserting a liver into {$target}'s {$part}!
surgery-popup-step-SurgeryStepInsertEyes = {$user} is inserting eyes into {$target}'s {$part}!
surgery-popup-step-SurgeryStepInsertHeart = {$user} is inserting a heart into {$target}'s {$part}!
surgery-popup-step-SurgeryStepInsertStomach = {$user} is inserting a stomach into {$target}'s {$part}!

surgery-popup-step-SurgeryStepSealOrganWound = {$user} is sealing the wounds on {$target}'s {$part}.
surgery-popup-step-SurgeryStepLobotomize = {$user} is lobotomizing {$target}!
surgery-popup-step-SurgeryStepMendBrainTissue = {$user} is mending the brain tissue on {$target}'s {$part}.

surgery-popup-step-SurgeryStepTreatTumor = {$user} is treating the tumor on {$target}'s {$part}.
