- type: construction
  name: "нанотрасеновий вівтар"
  id: AltarNanotrasen
  graph: Altar
  startNode: start
  targetNode: nanotrasen
  category: construction-category-furniture
  description: "Вівтар богів. Ті, хто має певний божественний потенціал, можуть принести на нього в жертву псионіку."
  icon:
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    state: nanotrasen
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "вівтар хаосу"
  id: AltarChaos
  graph: Altar
  startNode: start
  targetNode: chaos
  category: construction-category-furniture
  description: "Вівтар богів. Ті, хто має певний божественний потенціал, можуть принести на нього в жертву псионіку."
  icon:
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    state: chaos
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "друїдський вівтар"
  id: AltarDruid
  graph: Altar
  startNode: start
  targetNode: druid
  category: construction-category-furniture
  description: "Вівтар богів. Ті, хто має певний божественний потенціал, можуть принести на нього в жертву псионіку."
  icon:
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    state: druid
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "ящик для інструментів вівтар"
  id: AltarToolbox
  graph: Altar
  startNode: start
  targetNode: toolbox
  category: construction-category-furniture
  description: "Вівтар богів. Ті, хто має певний божественний потенціал, можуть принести на нього в жертву псионіку."
  icon:
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    state: toolbox
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "космічно-християнський вівтар"
  id: AltarSpaceChristian
  graph: Altar
  startNode: start
  targetNode: spacechristian
  category: construction-category-furniture
  description: "Вівтар богів. Ті, хто має певний божественний потенціал, можуть принести на нього в жертву псионіку."
  icon:
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    state: space-christian
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "сатанинський вівтар"
  id: AltarSatana
  graph: Altar
  startNode: start
  targetNode: satana
  category: construction-category-furniture
  description: "Вівтар богів. Ті, хто має певний божественний потенціал, можуть принести на нього в жертву псионіку."
  icon:
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    state: satana
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "технологічний вівтар"
  id: AltarTechnology
  graph: Altar
  startNode: start
  targetNode: technology
  category: construction-category-furniture
  description: "Вівтар богів. Ті, хто має певний божественний потенціал, можуть принести на нього в жертву псионіку."
  icon:
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    state: technology
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "фестивальний вівтар"
  id: AltarConvertFestival
  graph: Altar
  startNode: start
  targetNode: festival
  category: construction-category-furniture
  description: "Вівтар богів. Ті, хто має певний божественний потенціал, можуть принести на нього в жертву псионіку."
  icon:
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    state: festival
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "вівтар"
  id: AltarConvertMaint
  graph: Altar
  startNode: start
  targetNode: maint
  category: construction-category-furniture
  description: "Вівтар богів. Ті, хто має певний божественний потенціал, можуть принести на нього в жертву псионіку."
  icon:
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    state: maint
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "блакитний вівтар"
  id: AltarConvertBlue
  graph: Altar
  startNode: start
  targetNode: blue
  category: construction-category-furniture
  description: "Вівтар богів. Ті, хто має певний божественний потенціал, можуть принести на нього в жертву псионіку."
  icon:
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    state: blue
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "вівтар тягарів"
  id: AltarConvertBurden
  graph: Altar
  startNode: start
  targetNode: burden
  category: construction-category-furniture
  description: "Вівтар богів. Ті, хто має певний божественний потенціал, можуть принести на нього в жертву псионіку."
  icon:
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    state: burden
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "перетворити вівтар"
  id: AltarConvert
  graph: Altar
  startNode: start
  targetNode: convert
  category: construction-category-furniture
  description: "Вівтар богів. Ті, хто має певний божественний потенціал, можуть принести на нього в жертву псионіку."
  icon:
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    state: convertaltar
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "помаранчевий вівтар"
  id: AltarConvertOrange
  graph: Altar
  startNode: start
  targetNode: orange
  category: construction-category-furniture
  description: "Вівтар богів. Ті, хто має певний божественний потенціал, можуть принести на нього в жертву псионіку."
  icon:
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    state: orange
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "червоний вівтар"
  id: AltarConvertRed
  graph: Altar
  startNode: start
  targetNode: red
  category: construction-category-furniture
  description: "Вівтар богів. Ті, хто має певний божественний потенціал, можуть принести на нього в жертву псионіку."
  icon:
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    state: red
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "білий вівтар"
  id: AltarConvertWhite
  graph: Altar
  startNode: start
  targetNode: white
  category: construction-category-furniture
  description: "Вівтар богів. Ті, хто має певний божественний потенціал, можуть принести на нього в жертву псионіку."
  icon:
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    state: white
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "жовтий вівтар"
  id: AltarConvertYellow
  graph: Altar
  startNode: start
  targetNode: yellow
  category: construction-category-furniture
  description: "Вівтар богів. Ті, хто має певний божественний потенціал, можуть принести на нього в жертву псионіку."
  icon:
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    state: yellow
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "небесний вівтар"
  id: AltarHeaven
  graph: Altar
  startNode: start
  targetNode: heaven
  category: construction-category-furniture
  description: "Вівтар богів. Ті, хто має певний божественний потенціал, можуть принести на нього в жертву псионіку."
  icon:
    sprite: Structures/Furniture/Altars/Cults/heaven.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "ікластий вівтар"
  id: AltarFangs
  graph: Altar
  startNode: start
  targetNode: fanged
  category: construction-category-furniture
  description: "Вівтар богів. Ті, хто має певний божественний потенціал, можуть принести на нього в жертву псионіку."
  icon:
    sprite: Structures/Furniture/Altars/Cults/fangs.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "армований дерев'яний стіл"
  id: TableWoodReinforced
  graph: Table
  startNode: start
  targetNode: TableWoodReinforced
  category: construction-category-furniture
  description: "Квадратний шматок дерева, що стоїть на чотирьох металевих ніжках. Дуже міцний."
  icon:
    sprite: Nyanotrasen/Structures/Furniture/Tables/rwood.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "татамі"
  id: tatamisingle
  graph: tatami
  startNode: start
  targetNode: tatamiSquare
  category: construction-category-furniture
  description: "Це татамі, але квадратне."
  icon:
    sprite: Nyanotrasen/Structures/Furniture/tatami.rsi
    state: tatami_1x1
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "килимок татамі"
  id: tatamimat
  graph: tatami
  startNode: start
  targetNode: tatamiPart
  category: construction-category-furniture
  description: "Це частина килимка для татамі."
  icon:
    sprite: Nyanotrasen/Structures/Furniture/tatami.rsi
    state: tatami_1x1P
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked
