using System.Numerics;
using Content.Client.Stylesheets;
using Content.Shared.Vampire;
using Content.Shared.Vampire.Components;
using Content.Client.Resources;
using Robust.Client.ResourceManagement;
using Robust.Client.AutoGenerated;
using Robust.Client.GameObjects;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;

namespace Content.Client.Vampire;

[GenerateTypedNameReferences]
public sealed partial class VampireMutationMenu : DefaultWindow
{
    [Dependency] private readonly IPrototypeManager _prototypeManager = default!;
    [Dependency] private readonly IEntityManager _entityManager = default!;
    private IResourceCache _resourceCache;
    private readonly SpriteSystem _sprite;
    public event Action<VampireMutationsType>? OnIdSelected;

    private HashSet<VampireMutationsType> _possibleMutations = new();
    private VampireMutationsType _selectedId;

    public VampireMutationMenu()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
        _sprite = _entityManager.System<SpriteSystem>();
        _resourceCache = IoCManager.Resolve<IResourceCache>();
    }
    
    public void UpdateState(HashSet<VampireMutationsType> mutationList, VampireMutationsType selectedMutation)
    {
        _possibleMutations = mutationList;
        _selectedId = selectedMutation;
        UpdateGrid();
    }
    
    private void UpdateGrid()
    {
        ClearGrid();

        var group = new ButtonGroup();

        foreach (var Mutation in _possibleMutations)
        {
            //if (!_prototypeManager.TryIndex("NormalBlobTile", out EntityPrototype? proto))
            //    continue;
        
            string texturePath = Mutation switch
            {
                VampireMutationsType.None => "/Textures/Interface/Actions/actions_vampire.rsi/deathsembrace.png",
                VampireMutationsType.Hemomancer => "/Textures/Interface/Actions/actions_vampire.rsi/hemomancer.png",
                VampireMutationsType.Umbrae => "/Textures/Interface/Actions/actions_vampire.rsi/umbrae.png",
                VampireMutationsType.Gargantua => "/Textures/Interface/Actions/actions_vampire.rsi/gargantua.png",
                VampireMutationsType.Dantalion => "/Textures/Interface/Actions/actions_vampire.rsi/dantalion.png",
                VampireMutationsType.Bestia => "/Textures/Interface/Actions/actions_vampire.rsi/bestia.png",
                _ => "/Textures/Interface/Actions/actions_vampire.rsi/deathsembrace.png"
            };

            var button = new Button
            {
                MinSize = new Vector2(64, 64),
                HorizontalExpand = true,
                Group = group,
                StyleClasses = {StyleBase.ButtonSquare},
                ToggleMode = true,
                Pressed = _selectedId == Mutation,
                ToolTip = Loc.GetString($"vampire-mutation-{Mutation.ToString().ToLower()}-info"),
                TooltipDelay = 0.01f,
            };
            button.OnPressed += _ => OnIdSelected?.Invoke(Mutation);
            Grid.AddChild(button);

            var texture = _resourceCache.GetTexture(texturePath);
            button.AddChild(new TextureRect
            {
                Stretch = TextureRect.StretchMode.KeepAspectCentered,
                Texture = texture,
            });
        }
    }
    
    private void ClearGrid()
    {
        Grid.RemoveAllChildren();
    }
}