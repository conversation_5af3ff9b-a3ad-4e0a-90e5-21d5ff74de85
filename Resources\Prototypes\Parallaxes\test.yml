# looks like the AME became the universe again.
# someone call that guy who went and became the universe last time.
# using Content.Client.Parallax.Managers; IoCManager.Resolve<IParallaxManager>().ParallaxName = "test";

- type: parallax
  id: test
  layers:
    - texture:
        !type:ImageParallaxTextureSource
        path: "/Textures/Decals/dirty.rsi/damaged.png"
      slowness: 0.5
      scale: "2, 2"
    - texture:
        !type:ImageParallaxTextureSource
        path: "/Textures/Decals/dirty.rsi/rust.png"
      slowness: 3.0
      scale: "3, 3"
    - texture:
        !type:ImageParallaxTextureSource
        path: "/Textures/Objects/Devices/flatpack.rsi/ame-part.png"
      slowness: 0.995
      tiled: false
      controlHomePosition: "-128, -128"
      worldHomePosition: "0, 0"
      worldAdjustPosition: "4, 4"
      scale: "8, 8"
    - texture:
        !type:ImageParallaxTextureSource
        path: "/Textures/Objects/Power/AME/ame_jar.rsi/jar.png"
      slowness: 0.0
      tiled: false
      scale: "2, 2"
    - texture:
        !type:ImageParallaxTextureSource
        path: "/Textures/Objects/Power/AME/ame_jar.rsi/jar.png"
      slowness: 0.125
      tiled: false
      scale: "1, 1"
    - texture:
        !type:ImageParallaxTextureSource
        path: "/Textures/Objects/Devices/flatpack.rsi/ame-part.png"
      slowness: 0.0
      tiled: false
      controlHomePosition: "0, 32"
      worldHomePosition: "0, 1"
      scale: "2, 2"
  layersLQ:
    - texture:
        !type:ImageParallaxTextureSource
        path: "/Textures/Decals/dirty.rsi/rust.png"
      slowness: 3.0
      scale: "3, 3"
  layersLQUseHQ: false

