- type: construction
  name: "бейсбольна біта"
  id: bat
  graph: WoodenBat
  startNode: start
  targetNode: bat
  category: construction-category-weapons
  description: "Міцна бейсбольна бита."
  icon:
    sprite: Objects/Weapons/Melee/baseball_bat.rsi
    state: icon
  objectType: Item

- type: construction
  name: "примарний аркуш"
  id: ghost_sheet
  graph: GhostSheet
  startNode: start
  targetNode: ghost_sheet
  category: construction-category-clothing
  description: "Стань моторошним привидом. Бу!"
  icon:
    sprite: Clothing/OuterClothing/Misc/ghostsheet.rsi
    state: icon
  objectType: Item

- type: construction
  name: "саморобні наручники"
  id: makeshifthandcuffs
  graph: makeshifthandcuffs
  startNode: start
  targetNode: cuffscable
  category: construction-category-tools
  description: "Саморобні наручники, виготовлені з запасних кабелів."
  icon: { sprite: Objects/Misc/cablecuffs.rsi, state: cuff }
  objectType: Item

- type: construction
  name: "саморобна палиця-шокер"
  id: makeshiftstunprod
  graph: makeshiftstunprod
  startNode: start
  targetNode: msstunprod
  category: construction-category-weapons
  description: "Саморобний електрошокер."
  icon: { sprite: Objects/Weapons/Melee/stunprod.rsi, state: stunprod_off }
  objectType: Item

- type: construction
  name: "кляп"
  id: muzzle
  graph: Muzzle
  startNode: start
  targetNode: muzzle
  category: construction-category-tools
  objectType: Item
  description: "Намордник, щоб змусити жертву замовкнути."
  icon:
    sprite: Clothing/Mask/muzzle.rsi
    state: icon

- type: construction
  name: "саморобна пневматична гармата"
  id: pneumaticcannon
  graph: PneumaticCannon
  startNode: start
  targetNode: cannon
  category: construction-category-weapons
  objectType: Item
  description: "Ця суча дочка може стріляти усім, що в неї влізе, використовуючи лише трохи газу."
  icon:
    sprite: Objects/Weapons/Guns/Cannons/pneumatic_cannon.rsi
    state: icon

- type: construction
  name: "марля"
  id: gauze
  graph: Gauze
  startNode: start
  targetNode: gauze
  category: construction-category-tools
  objectType: Item
  description: "Коли у тебе справді нічого не залишилося."
  icon:
    sprite: Objects/Specific/Medical/medical.rsi
    state: gauze

- type: construction
  name: "пов'язка на очі"
  id: blindfold
  graph: Blindfold
  startNode: start
  targetNode: blindfold
  category: construction-category-tools
  objectType: Item
  description: "Сподівайтеся, що всі закриють очі на те, що ви створюєте цю пікантну річ..."
  icon:
    sprite: Clothing/Eyes/Misc/blindfold.rsi
    state: icon

- type: construction
  name: "квітковий вінок"
  id: flowerwreath
  graph: flowerwreath
  startNode: start
  targetNode: flowerwreath
  category: construction-category-clothing
  description: "Вінок з різнокольорових квітів. Можна носити як на голові, так і на шиї."
  icon:
    sprite: Clothing/Head/Misc/flower-wreath.rsi
    state: icon
  objectType: Item

- type: construction
  name: "волога ганчірка"
  id: rag
  graph: Rag
  startNode: start
  targetNode: rag
  category: construction-category-tools
  objectType: Item
  description: "Волога ганчірка для прибирання землі. Це краще, ніж ковзати цілий день."
  icon:
    sprite: Objects/Specific/Janitorial/rag.rsi
    state: rag

- type: construction
  name: "саморобна рушниця"
  id: improvisedshotgun
  graph: ImprovisedShotgunGraph
  startNode: start
  targetNode: shotgun
  category: construction-category-weapons
  objectType: Item
  description: "Гівняна однозарядна рушниця, зроблена з утилізованих і зібраних вручну частин зброї. Набої не входять до комплекту."
  icon:
    sprite: Objects/Weapons/Guns/Shotguns/improvised_shotgun.rsi
    state: icon

- type: construction
  name: "імпровізований дробовий снаряд"
  id: ShellShotgunImprovised
  graph: ImprovisedShotgunShellGraph
  startNode: start
  targetNode: shell
  category: construction-category-weapons
  objectType: Item
  description: "Саморобний дробовий снаряд, що стріляє болючими скляними осколками. Розліт настільки широкий, що він не міг влучити в широку сторону сараю"
  icon:
    sprite: Objects/Weapons/Guns/Ammunition/Casings/shotgun_shell.rsi
    state: improvised

- type: construction
  name: "ложе гвинтівки"
  id: riflestock
  graph: RifleStockGraph
  startNode: start
  targetNode: riflestock
  category: construction-category-weapons
  objectType: Item
  description: "Ложа, вирізана з дерева, життєво необхідна для імпровізованої вогнепальної зброї."
  icon:
    sprite: Objects/Misc/rifle_stock.rsi
    state: icon

- type: construction
  name: "запальна бомба"
  id: firebomb
  graph: FireBomb
  startNode: start
  targetNode: firebomb
  category: construction-category-weapons
  objectType: Item
  description: "Слабкий, саморобний запальний пристрій."
  icon:
    sprite: Objects/Weapons/Bombs/ied.rsi
    state: icon

- type: construction
  name: "бавовняна ткана тканина"
  id: CottonWovenCloth
  graph: CottonObjects
  startNode: start
  targetNode: cottoncloth
  category: construction-category-misc
  description: "Саморобний шматок бавовняної тканини, на дотик грубий."
  icon:
    sprite: Objects/Materials/materials.rsi
    state: cloth_3
  objectType: Item

- type: construction
  name: "солом'яний капелюх"
  id: strawHat
  graph: StrawHat
  startNode: start
  targetNode: strawhat
  category: construction-category-clothing
  description: "Модний капелюх для спекотних днів! Не рекомендується носити біля вогню."
  icon:
    sprite: Clothing/Head/Hats/straw_hat.rsi
    state: icon
  objectType: Item

- type: construction
  name: "трубчаста бомба"
  id: pipebomb
  graph: PipeBomb
  startNode: start
  targetNode: pipebomb
  category: construction-category-weapons
  objectType: Item
  description: "Саморобна вибухівка з труб та дроту."
  icon:
    sprite: Objects/Weapons/Bombs/pipebomb.rsi
    state: icon

- type: construction
  name: "персональний адаптер ШІ"
  id: AiAdapter
  graph: AiAdapter
  startNode: start
  targetNode: paiadapter
  category: construction-category-misc
  description: "Затишний будиночок для вашого електронного друга."
  icon:
    sprite: _EE/Objects/Specific/Robotics/paiadapter.rsi
    state: mmi_off
  objectType: Item
