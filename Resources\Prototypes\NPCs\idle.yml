# Picks a random location for the NPC to move to and idle.
- type: htnCompound
  id: IdleCompound
  branches:
    # Pick a new spot and wait there.
    - preconditions:
        - !type:BuckledPrecondition
          isBuckled: false
        - !type:PulledPrecondition
          isPulled: false
      tasks:
        - !type:HTNPrimitiveTask
          operator: !type:PickAccessibleOperator
            rangeKey: IdleRange

        - !type:HTNPrimitiveTask
          operator: !type:MoveToOperator
            pathfindInPlanning: false

        - !type:HTNPrimitiveTask
          operator: !type:RandomOperator
            targetKey: IdleTime
            minKey: MinimumIdleTime
            maxKey: MaximumIdleTime

        - !type:HTNPrimitiveTask
          operator: !type:WaitOperator
            key: IdleTime
          preconditions:
            - !type:KeyExistsPrecondition
              key: IdleTime

# Spin to a random rotation and idle.
- type: htnCompound
  id: IdleSpinCompound
  branches:
    - tasks:
        - !type:HTNPrimitiveTask
          operator: !type:WaitOperator
            key: IdleTime
          preconditions:
            - !type:KeyExistsPrecondition
              key: IdleTime

    # Pick a new angle and spin there
    - tasks:
        - !type:HTNPrimitiveTask
          operator: !type:PickRandomRotationOperator
            targetKey: RotateTarget

        - !type:HTNPrimitiveTask
          operator: !type:RotateToTargetOperator
            targetKey: RotateTarget

        - !type:HTNPrimitiveTask
          operator: !type:RandomOperator
            targetKey: IdleTime
            minKey: MinimumIdleTime
            maxKey: MaximumIdleTime

        - !type:HTNPrimitiveTask
          operator: !type:WaitOperator
            key: IdleTime
          preconditions:
            - !type:KeyExistsPrecondition
              key: IdleTime
