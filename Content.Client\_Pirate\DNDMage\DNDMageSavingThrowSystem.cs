using Content.Shared._Pirate.DNDMage;
using Robust.Client.GameObjects;

namespace Content.Client._Pirate.DNDMage;

/// <summary>
/// Client-side system for handling DNDMageSavingThrowComponent.
/// </summary>
public sealed class DNDMageSavingThrowSystem : SharedDNDMageSavingThrowSystem
{
    public override void Initialize()
    {
        base.Initialize();

        SubscribeLocalEvent<DNDMageSavingThrowComponent, ComponentInit>(OnComponentInit);
    }

    private void OnComponentInit(EntityUid uid, DNDMageSavingThrowComponent component, ComponentInit args)
    {
        // Client-side initialization logic if needed
    }
}
