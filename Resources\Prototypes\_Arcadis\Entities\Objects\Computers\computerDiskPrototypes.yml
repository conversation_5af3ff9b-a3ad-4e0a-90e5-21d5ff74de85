- type: entity
  id: UnburnedDiskPrototype
  name: "прототип фіктивного диска з незапаленим диском"
  categories: [HideSpawnMenu]

- type: entity
  id: CrewMonitorDiskPrototype
  name: "Спостерігач екіпажу"
  categories: [HideSpawnMenu]
  components:
    - type: ActivatableUI
      key: enum.CrewMonitoringUIKey.Key
    - type: UserInterface
      interfaces:
        enum.CrewMonitoringUIKey.Key:
          type: CrewMonitoringBoundUserInterface
    - type: CrewMonitoringConsole
    - type: DeviceNetwork
      deviceNetId: Wireless
      receiveFrequencyId: CrewMonitor
    - type: WirelessNetworkConnection
      range: 1200

- type: entity
  id: CommunicationsConsoleDiskPrototype
  name: "Консоль зв'язку"
  categories: [HideSpawnMenu]
  components:
    - type: AccessReader
      access: [["Command"]]
    - type: CommunicationsConsole
      title: comms-console-announcement-title-station
    - type: DeviceNetwork
      transmitFrequencyId: ShuttleTimer
    - type: ActivatableUI
      key: enum.CommunicationsConsoleUiKey.Key
    - type: UserInterface
      interfaces:
        enum.CommunicationsConsoleUiKey.Key:
          type: CommunicationsConsoleBoundUserInterface
