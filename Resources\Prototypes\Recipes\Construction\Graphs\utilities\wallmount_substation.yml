﻿- type: constructionGraph
  id: WallmountSubstation
  start: start
  graph:
    - node: start
      edges:
      - to: frame
        steps:
        - material: Steel
          amount: 5
          doAfter: 2

    - node: frame
      entity: BaseSubstationWallFrame
      edges:
      - to: start
        steps:
        - tool: Welding
          doAfter: 4
        completed:
        - !type:GivePrototype
          prototype: SheetSteel1
          amount: 5
        - !type:DeleteEntity {}
      - to: parts
        steps:
        - material: Cable
          amount: 5
          doAfter: 0.5
        - material: CableMV
          amount: 5
          doAfter: 0.5
        - material: CableHV
          amount: 5
          doAfter: 0.5
        - tool: Screwing
          doAfter: 2

    - node: parts
      entity: BaseSubstationWallFrame
      edges:
      - to: electronics
        steps:
        - tag: WallmountSubstationElectronics
          store: board
          name: "wallmount substation circuit board"
          icon:
            sprite: "Objects/Misc/module.rsi"
            state: "charger_APC"
          doAfter: 0.5
        - anyTags:
            - PowerCell
            - PowerCellSmall
          store: powercell
          name: a powercell
          icon:
            sprite: "Objects/Power/power_cells.rsi"
            state: "medium"
          doAfter: 0.5
        - tag: CapacitorStockPart
          name: a capacitor
          store: capacitor
          icon:
            sprite: "Objects/Misc/stock_parts.rsi"
            state: "capacitor"
          doAfter: 0.5
      - to: frame
        completed:
        - !type:GivePrototype
          prototype: CableApcStack1
          amount: 5
        - !type:GivePrototype
          prototype: CableMVStack1
          amount: 5
        - !type:GivePrototype
          prototype: CableHVStack1
          amount: 5
        steps:
        - tool: Cutting
          doAfter: 1


    - node: electronics
      edges:
      - to: substation
        steps:
        - tool: Screwing
          doAfter: 2

    - node: substation
      entity: BaseSubstationWall
      edges:
      - to: parts
        conditions:
        - !type:WirePanel {}
        - !type:ContainerNotEmpty
          container: board
        completed:
        - !type:EmptyAllContainers
          pickup: true
          emptyAtUser: true
        steps:
        - tool: Prying
          doAfter: 1
