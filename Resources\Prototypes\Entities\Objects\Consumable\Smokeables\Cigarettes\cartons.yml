- type: entity
  id: CigCartonGreen
  parent: [ BoxCardboard, BaseBagOpenClose ]
  name: "Коро<PERSON><PERSON><PERSON>'s Smokes"
  description: "Коробка, що містить 6 пачок <PERSON><PERSON><PERSON>'s Smokes."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigarettes/Cartons/green.rsi
    layers:
      - state: closed
      - state: open
        map: ["openLayer"]
  - type: Item
    sprite: Objects/Consumable/Smokeables/Cigarettes/Cartons/green.rsi
    size: Normal
  - type: Storage
    grid:
    - 0,0,4,1
  - type: StorageFill
    contents:
      - id: CigPackGreen
        amount: 5
  - type: Tag
    tags:
    - Trash
  - type: PhysicalComposition
    materialComposition:
      Plastic: 50
  - type: SpaceGarbage

- type: entity
  id: CigCartonRed
  parent: CigCartonGreen
  name: "Коробка DromedaryCo"
  description: "Коробка, що містить 6 пачок DromedaryCo."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigarettes/Cartons/red.rsi
    layers:
      - state: closed
  - type: Item
    sprite: Objects/Consumable/Smokeables/Cigarettes/Cartons/red.rsi
  - type: StorageFill
    contents:
      - id: CigPackRed
        amount: 5

- type: entity
  id: CigCartonBlue
  parent: CigCartonGreen
  name: "Коробка AcmeCo"
  description: "Коробка, що містить 6 пачок AcmeCo."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigarettes/Cartons/blue.rsi
    layers:
      - state: closed
  - type: Item
    sprite: Objects/Consumable/Smokeables/Cigarettes/Cartons/blue.rsi
  - type: StorageFill
    contents:
      - id: CigPackBlue
        amount: 5

- type: entity
  id: CigCartonBlack
  parent: CigCartonGreen
  name: "Коробка Nomads"
  description: "Коробка, що містить 6 пачок Nomads."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigarettes/Cartons/black.rsi
    layers:
      - state: closed
  - type: Item
    sprite: Objects/Consumable/Smokeables/Cigarettes/Cartons/black.rsi
  - type: StorageFill
    contents:
      - id: CigPackBlack
        amount: 5

- type: entity
  id: CigCartonMixed
  parent: CigCartonGreen
  name: "пачка Dan's Soaked"
  description: "Коробка з 3 пачками просоченого тютюну від Дена."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigarettes/Cartons/mixed.rsi
    layers:
      - state: closed
  - type: Item
    sprite: Objects/Consumable/Smokeables/Cigarettes/Cartons/mixed.rsi
  - type: StorageFill
    contents:
      - id: CigPackMixedMedical
      - id: CigPackMixed
      - id: CigPackMixedNasty
