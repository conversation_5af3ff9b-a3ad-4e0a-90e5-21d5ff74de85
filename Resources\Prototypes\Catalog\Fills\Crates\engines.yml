# AME

- type: entity
  id: CrateEngineeringAMEShielding
  parent: CrateEngineeringSecure
  name: "ящик з упакованим реактором на антиматерії"
  description: "9 частин для основного корпусу реактора на антиматерії або для розширення існуючого."
  components:
  - type: StorageFill
    contents:
      - id: AmePartFlatpack
        amount: 9

- type: entity
  id: CrateEngineeringAMEJar
  parent: CrateEngineeringSecure
  name: "ящик контейнерів для утримання антиматерії"
  description: "3 банки з антиматерією, для заправки відповідного реактора."
  components:
  - type: StorageFill
    contents:
      - id: AmeJar
        amount: 3

- type: entity
  id: CrateEngineeringAMEControl
  parent: CrateEngineeringSecure
  name: "ящик блоку управління антиматерії"
  description: "Блок управління реактором антиматерії."
  components:
  - type: StorageFill
    contents:
      - id: AmeControllerUnanchored

# Singularity

- type: entity
  id: CrateEngineeringSingularityEmitter
  parent: CrateEngineeringSecure
  name: "ящик випромінювача"
  description: "Випромінювач, який найкраще використовувати для двигунів сингулярності."
  components:
  - type: StorageFill
    contents:
      - id: EmitterFlatpack # TODO change to flatpack

- type: entity
  id: CrateEngineeringSingularityCollector
  parent: CrateEngineeringSecure
  name: "ящик з колектором радіації"
  description: "Колектор випромінювання, найкраще підходить для сингулярних двигунів. Плазма в комплекті."
  components:
  - type: StorageFill
    contents:
      - id: RadiationCollectorFlatpack
      - id: PlasmaTankFilled

- type: entity
  id: CrateEngineeringSingularityContainment
  parent: CrateEngineeringSecure
  name: "ящик з генератором поля утримання"
  description: "Генератор поля стримування, що утримує сингуло в покорі."
  components:
  - type: StorageFill
    contents:
      - id: ContainmentFieldGeneratorFlatpack

- type: entity
  id: CrateEngineeringSingularityGenerator
  parent: CrateEngineeringSecure
  name: "ящик з генератором сингулярності"
  description: "Генератор сингулярностей, мати звіра."
  components:
  - type: StorageFill
    contents:
      - id: SingularityGeneratorFlatpack

# Particle Accelerator

- type: entity
  id: CrateEngineeringParticleAccelerator
  parent: CrateEngineeringSecure
  name: "ящик з прискорювачем частинок"
  description: "Складний у налаштуванні, але до біса корисний."
  components:
  - type: StorageFill
    contents:
      - id: MachineParticleAcceleratorEndCapCircuitboard
      - id: MachineParticleAcceleratorEmitterStarboardCircuitboard
      - id: MachineParticleAcceleratorEmitterForeCircuitboard
      - id: MachineParticleAcceleratorEmitterPortCircuitboard
      - id: MachineParticleAcceleratorFuelChamberCircuitboard
      - id: MachineParticleAcceleratorPowerBoxCircuitboard
      - id: ParticleAcceleratorComputerCircuitboard

# Non-functional for some reason

#- type: entity
#  id: CrateEngineeringSingularity
#  name:  singularity crate
#  description:  "Prank the station!"
#  parent: CrateEngineeringSecure
#  components:
#  - type: StorageFill
#    contents:
#      - id: Singularity
#        amount: 1

- type: entity
  id: CrateEngineeringGenerator
  parent: CrateEngineering
  name: "ящик з генератором"
  suffix: DEBUG
  components:
  - type: StorageFill
    contents:
      - id: DebugGenerator # TODO change to flatpack

- type: entity
  id: CrateEngineeringSolar
  parent: CrateEngineering
  name: "ящик для збірки сонячних батарей"
  description: "Набір із сонячними пластинами та склом для виготовлення десяти сонячних панелей."
  components:
  - type: StorageFill
    contents:
      - id: SolarAssemblyFlatpack
        amount: 10
      - id: SheetGlass10
        amount: 2

- type: entity
  id: CrateEngineeringShuttle
  parent: CrateEngineeringSecure
  name: "ящик живлення шаттлу"
  description: "Ящик з усім необхідним для живлення шатлу."
  components:
  - type: StorageFill
    contents:
      - id: WallmountSubstationElectronics
      - id: WallmountGeneratorAPUElectronics
      - id: HandheldGPSBasic
      - id: InflatableDoorStack1

- type: entity
  id: CrateEngineeringTeslaGenerator
  parent: CrateEngineeringSecure
  name: "ящик генератора тесла"
  description: "Генератор тесли. Боже, бережи тебе."
  components:
  - type: StorageFill
    contents:
      - id: TeslaGeneratorFlatpack

- type: entity
  id: CrateEngineeringTeslaCoil
  parent: CrateEngineeringSecure
  name: "ящик котушки тесли"
  description: "Котушка тесли. Притягує блискавку і генерує з неї енергію."
  components:
  - type: StorageFill
    contents:
      - id: TeslaCoilFlatpack

- type: entity
  id: CrateEngineeringTeslaGroundingRod
  parent: CrateEngineeringSecure
  name: "ящик з заземлювальним стрижнем тесли"
  description: "Заземлювач, найкраще для захисту від блискавки."
  components:
  - type: StorageFill
    contents:
      - id: TeslaGroundingRodFlatpack

- type: entity
  id: CrateEngineeringTEGKit
  parent: CrateEngineeringSecure
  name: "Ящик з ТЕГ"
  description: "Набір для створення власного ТЕГ. Потрібна деяка збірка."
  components:
  - type: StorageFill
    contents:
      - id: TegCirculatorPartFlatpack
      - id: TegCirculatorPartFlatpack
      - id: TegCenterPartFlatpack
      - id: GasAnalyzer
      - id: SheetSteel
        amount: 1
