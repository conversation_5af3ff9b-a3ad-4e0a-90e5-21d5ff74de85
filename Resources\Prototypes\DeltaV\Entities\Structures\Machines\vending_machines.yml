- type: entity
  parent: VendingMachine
  id: VendingMachinePride
  name: "Прайд-О-Мат"
  description: "Торговий автомат зі злочинами."
  components:
  - type: VendingMachine
    pack: PrideDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Advertise
    pack: PrideDrobeAds
  - type: SpeakOnUIClosed
    pack: PrideDrobeGoodbyes
  - type: Speech
  - type: Sprite
    sprite: DeltaV/Structures/Machines/VendingMachines/pride.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]

- type: entity
  parent: VendingMachine
  id: VendingMachineCourierDrobe
  name: "CourierDrobe"
  description: "Ні сонячні спалахи, ні метеорити, ні плазмовий вогонь, ні космічна порожнеча не заважають цим кур'єрам якнайшвидше завершити призначені їм рейси."
  components:
  - type: VendingMachine
    pack: CourierDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Advertise
    pack: CourierDrobeAds
  - type: SpeakOnUIClosed
    pack: CourierDrobeGoodbyes
  - type: Sprite
    sprite: DeltaV/Structures/Machines/VendingMachines/courierdrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Mail"]]

- type: entity
  parent: VendingMachineBooze
  id: VendingMachineBoozeUnlocked
  suffix: Unlocked
  components:
  - type: VendingMachine
    pack: BoozeOMatUnlockedInventory
  - type: AccessReader
    enabled: false
