﻿- type: entity
  id: BasePortal
  abstract: true
  name: "портал bluespace"
  description: "Перенесе вас до пов'язаного пункту призначення!"
  components:
  - type: Transform
    anchored: True
  - type: InteractionOutline
  - type: Clickable
  - type: Physics
    bodyType: Static
  - type: Sprite
    sprite: /Textures/Effects/portal.rsi
  - type: Fixtures
    fixtures:
      portalFixture:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.48,0.25,0.48"
        mask:
        - FullTileMask
        layer:
        - WallLayer
        hard: false
  - type: Portal

- type: entity
  id: PortalRed
  parent: BasePortal
  description: "Цей більше схожий на портал червоного простору."
  components:
  - type: Sprite
    layers:
    - state: portal-red
  - type: PointLight
    color: OrangeRed
    radius: 3
    energy: 1
    netsync: false

- type: entity
  id: PortalBlue
  parent: BasePortal
  components:
  - type: Sprite
    layers:
    - state: portal-blue
  - type: PointLight
    color: SkyBlue
    radius: 3
    energy: 1
    netsync: false

- type: entity
  id: PortalArtifact
  parent: BasePortal
  components:
  - type: Sprite
    layers:
    - state: portal-artifact
  - type: PointLight
    color: "#ed85c2"
    radius: 3
    energy: 1
    netsync: false
  - type: TimedDespawn
    lifetime: 120
  - type: Portal
    canTeleportToOtherMaps: true

- type: entity
  id: PortalGatewayBlue
  parent: BasePortal
  components:
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/gateway.rsi
    color: SkyBlue
    layers:
    - state: portal
  - type: PointLight
    color: SkyBlue
    radius: 3
    energy: 1
    netsync: false

- type: entity
  id: PortalGatewayOrange
  parent: BasePortal
  components:
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/gateway.rsi
    color: OrangeRed
    layers:
    - state: portal
  - type: PointLight
    color: OrangeRed
    radius: 3
    energy: 1
    netsync: false

- type: entity
  id: ShadowPortal
  name: "тіньовий розлом"
  description: "Виглядає нестабільним."
  parent: [ BasePortal, BaseShadow ]
  components:
  - type: Portal
    arrivalSound: /Audio/Items/hiss.ogg
    departureSound: /Audio/Items/hiss.ogg
  - type: Sprite
    state: portal-artifact
    color: "#793a80dd"
  - type: PointLight
    color: "#793a80dd"
    radius: 3
    energy: 1
    netsync: false
  - type: AmbientSound
    range: 6
    volume: -3
    sound:
      path: /Audio/Ambience/anomaly_scary.ogg

- type: entity
  name: "нестабільний тіньовий розлом"
  id: ShadowPortalTemp
  description: "Він уже закривається!"
  parent: ShadowPortal
  components:
    - type: TimedDespawn
      lifetime: 12
