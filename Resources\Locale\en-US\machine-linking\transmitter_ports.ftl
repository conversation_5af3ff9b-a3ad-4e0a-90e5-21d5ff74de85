signal-port-name-pressed = Pressed
signal-port-description-pressed = This port is invoked whenever the transmitter is activated.

signal-port-name-on-transmitter = On
signal-port-description-on-transmitter = This port is invoked whenever the transmitter is turned on.

signal-port-name-off-transmitter = Off
signal-port-description-off-transmitter = This port is invoked whenever the transmitter is turned off.

signal-port-name-status-transmitter = Status
signal-port-description-status-transmitter = This port is invoked with HIGH or LOW depending on the transmitter status.

signal-port-name-left = Left
signal-port-description-left = This port is invoked whenever the lever is moved to the leftmost position.

signal-port-name-right = Right
signal-port-description-right = This port is invoked whenever the lever is moved to the rightmost position.

signal-port-name-doorstatus = Door status
signal-port-description-doorstatus = This port is invoked with <PERSON><PERSON><PERSON> when the door opens and <PERSON>OW when the door finishes closing.

signal-port-name-dockstatus = Dock status
signal-port-description-dockstatus = This port is invoked with <PERSON>IGH when docked and LOW when undocked.

signal-port-name-middle = Middle
signal-port-description-middle = This port is invoked whenever the lever is moved to the neutral position.

signal-port-name-timer-trigger = Timer Trigger
signal-port-description-timer-trigger = This port is invoked whenever the timer triggers.

signal-port-name-timer-start = Timer Start
signal-port-description-timer-start = This port is invoked whenever the timer starts.

signal-port-name-logic-output = Output
signal-port-description-logic-output = This port is invoked with HIGH or LOW depending on the selected gate and inputs.

signal-port-name-logic-output-high = High Output
signal-port-description-logic-output-high = This port is invoked whenever the input has a rising edge.

signal-port-name-logic-output-low = Low Output
signal-port-description-logic-output-low = This port is invoked whenever the input has a falling edge.

signal-port-name-air-danger = Danger
signal-port-description-air-danger = This port is invoked with HIGH when in danger mode and LOW when not.

signal-port-name-air-warning = Warning
signal-port-description-air-warning = This port is invoked with HIGH when in warning mode and LOW when not.

signal-port-name-air-normal = Normal
signal-port-description-air-normal = This port is invoked with HIGH when in normal mode and LOW when not.

signal-port-name-decaying = Decaying
signal-port-description-decaying = This port is invoked when a bound anomaly starts to decay.

signal-port-name-stabilize = Stabilize
signal-port-description-stabilize = This port is invoked when a bound anomaly is normalized.

signal-port-name-growing = Growing
signal-port-description-growing = This port is invoked when a bound anomaly starts to grow.

signal-port-name-pulse = Pulse
signal-port-description-pulse = This port is invoked when a bound anomaly is pulsing.

signal-port-name-supercrit = Supercritical
signal-port-description-supercrit = This port is invoked when a bound anomaly explode after supercrit state.

signal-port-name-power-charging = Charging
signal-port-description-power-charging = This port is invoked with HIGH when the battery is gaining charge and LOW when not.

signal-port-name-power-discharging = Discharging
signal-port-description-power-discharging = This port is invoked with HIGH when the battery is losing charge and LOW when not.

signal-port-name-material-silo = Material silo
signal-port-description-material-silo = Bluespace storage for station materials

signal-port-name-fillitems = Fillbot Items
signal-port-description-fillitems = Items picked up by the Fillbot.
