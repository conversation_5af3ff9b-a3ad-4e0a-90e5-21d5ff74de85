# Misc electronic trigger devices.
# TODO:
# - proximity
# - voice
# - machine linking
# - device network
# - biometric/health (maybe just via device nets?)
# - booby-trap / on-storage-open

- type: entity
  parent: BaseItem
  id: TimerTrigger
  name: "таймер-тригер"
  description: "Таймер, який можна налаштувати."
  components:
  - type: Sprite
    sprite: Objects/Devices/timer.rsi
    state: timer
  - type: Item
    size: Small
  - type: StaticPrice
    price: 40
  - type: PayloadTrigger
    components:
    - type: OnUseTimerTrigger
      delay: 5
      delayOptions: [3, 5, 10, 15, 30]
      initialBeepDelay: 0
      beepSound:
        path: /Audio/Machines/Nuke/general_beep.ogg
        params:
          volume: -2

- type: entity
  parent: TimerTrigger
  id: SignalTrigger
  name: "тригер сигналу"
  description: "Додає машинний зв'язок, який спрацьовує за сигналами."
  components:
  - type: Sprite
    sprite: Objects/Devices/signaltrigger.rsi
    state: signaltrigger
  - type: StaticPrice
    price: 40
  - type: Tag
    tags:
    - SignalTrigger
  - type: PayloadTrigger
    components:
    - type: TriggerOnSignal
    - type: DeviceNetwork
      deviceNetId: Wireless
      receiveFrequencyId: BasicDevice
    - type: WirelessNetworkConnection
      range: 200
    - type: DeviceLinkSink

- type: entity
  parent: BaseItem
  id: VoiceTrigger
  name: "голосовий тригер"
  description: "Додає машинний зв'язок, який запускається за голосовими ключовими словами"
  components:
  - type: Sprite
    sprite: Objects/Devices/voice.rsi
    state: voice
  - type: StaticPrice
    price: 40
  - type: Tag
    tags:
    - VoiceTrigger
  - type: PayloadTrigger
    components:
    - type: TriggerOnVoice
