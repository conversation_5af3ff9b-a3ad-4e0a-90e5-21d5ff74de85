- type: entity
  parent: ClothingHandsButcherable
  id: ClothingHandsChameleon # doesn't protect from electricity or heat
  name: "чорні рукавички"
  description: "Звичайні чорні рукавички, які не заважають смажити."
  suffix: Chameleon
  components:
    - type: Tag
      tags: [] # ignore "WhitelistChameleon" tag
    - type: Sprite
      sprite: Clothing/Hands/Gloves/Color/black.rsi
    - type: Clothing
      sprite: Clothing/Hands/Gloves/Color/black.rsi
    - type: ChameleonClothing
      slot: [gloves]
      default: ClothingHandsGlovesColorBlack
    - type: Fiber
      fiberMaterial: fibers-chameleon
    - type: FingerprintMask
    - type: UserInterface
      interfaces:
        enum.ChameleonUiKey.Key:
          type: ChameleonBoundUserInterface

- type: entity
  parent: ClothingHandsChameleon
  id: ClothingHandsChameleonThief
  suffix: Chameleon, Thieving
  components:
  - type: Thieving
    stripTimeReduction: 2
