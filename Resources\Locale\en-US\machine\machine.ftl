machine-insert-item = {THE($user)} inserted {THE($item)} into {THE($machine)}.

machine-upgrade-examinable-verb-text = Upgrades
machine-upgrade-examinable-verb-message = Examine the machine upgrades.
machine-upgrade-increased-by-percentage = [color=yellow]{CAPITALIZE($upgraded)}[/color] increased by {$percent}%.
machine-upgrade-decreased-by-percentage = [color=yellow]{CAPITALIZE($upgraded)}[/color] decreased by {$percent}%.
machine-upgrade-increased-by-amount = [color=yellow]{CAPITALIZE($upgraded)}[/color] increased by {$difference}.
machine-upgrade-decreased-by-amount = [color=yellow]{CAPITALIZE($upgraded)}[/color] decreased by {$difference}.
machine-upgrade-not-upgraded = [color=yellow]{CAPITALIZE($upgraded)}[/color] not upgraded.

machine-part-name-capacitor = Capacitor
machine-part-name-manipulator = Manipulator
machine-part-name-matter-bin = Matter Bin
machine-part-name-power-cell = Power Cell

upgrade-power-draw = power draw
upgrade-max-charge = max charge
upgrade-power-supply = power supply
upgrade-power-supply-ramping = power ramp rate

two-way-lever-left = push left
two-way-lever-right = push right
two-way-lever-cant = can't push the lever that way!

recycler-count-items = Recycled {$items} objects.

machine-already-in-use = {CAPITALIZE(THE($machine))} is already in use.
