- type: constructionGraph
  id: HonkBot
  start: start
  graph:
  - node: start
    edges:
    - to: bot
      steps:
      - tag: HappyHonk
        icon:
          sprite: Objects/Storage/Happyhonk/clown.rsi
          state: box
        name: happy honk meal
      - tag: BikeHorn
        icon:
            sprite: Objects/Fun/bikehorn.rsi
            state: icon
        name: bike horn
        doAfter: 2
      - tag: ProximitySensor
        icon:
          sprite: Objects/Misc/proximity_sensor.rsi
          state: icon
        name: proximity sensor
      - tag: BorgArm
        icon:
          sprite: Mobs/Silicon/drone.rsi
          state: l_hand
        name: borg arm
        doAfter: 2
  - node: bot
    entity: MobHonkBot

- type: constructionGraph
  id: JonkBot
  start: start
  graph:
  - node: start
    edges:
    - to: bot
      steps:
      - tag: CluwneHappyHonk
        icon:
          sprite: Objects/Storage/Happyhonk/cluwne.rsi
          state: box
        name: woeful cluwne meal
      - tag: CluwneHorn
        icon:
          sprite: Objects/Fun/cluwnehorn.rsi
          state: icon
        name: broken bike horn
        doAfter: 2
      - tag: ProximitySensor
        icon:
          sprite: Objects/Misc/proximity_sensor.rsi
          state: icon
        name: proximity sensor
      - tag: BorgArm
        icon:
          sprite: Mobs/Silicon/drone.rsi
          state: l_hand
        name: borg arm
        doAfter: 2
  - node: bot
    entity: MobJonkBot
