- type: latheRecipe
  id: CanilunztTranslator
  result: CanilunztTranslator
  completetime: 2
  materials:
    Steel: 500
    Glass: 100
    Plastic: 50
    Gold: 50

- type: latheRecipe
  id: BubblishTranslator
  result: BubblishTranslator
  completetime: 2
  materials:
    Steel: 500
    Glass: 100
    Plastic: 50
    Gold: 50

- type: latheRecipe
  id: NekomimeticTranslator
  result: NekomimeticTranslator
  completetime: 2
  materials:
    Steel: 500
    Glass: 100
    Plastic: 50
    Gold: 50

- type: latheRecipe
  id: DraconicTranslator
  result: DraconicTranslator
  completetime: 2
  materials:
    Steel: 500
    Glass: 100
    Plastic: 50
    Gold: 50

- type: latheRecipe
  id: SolCommonTranslator
  result: SolCommonTranslator
  completetime: 2
  materials:
    Steel: 500
    Glass: 100
    Plastic: 50
    Gold: 50

- type: latheRecipe
  id: NovuNedericTranslator
  result: NovuNedericTranslator
  completetime: 2
  materials:
    Steel: 500
    Glass: 100
    Plastic: 50
    Gold: 50

- type: latheRecipe
  id: RootSpeakTranslator
  result: RootSpeakTranslator
  completetime: 2
  materials:
    Steel: 500
    Glass: 100
    Plastic: 50
    Gold: 50

- type: latheRecipe
  id: MofficTranslator
  result: MofficTranslator
  completetime: 2
  materials:
    Steel: 500
    Glass: 100
    Plastic: 50
    Gold: 50

- type: latheRecipe
  id: BasicGalaticCommonTranslatorImplanter
  result: BasicGalaticCommonTranslatorImplanter
  completetime: 2
  materials:
    Steel: 500
    Glass: 500
    Plastic: 100
    Gold: 50
    Silver: 50

- type: latheRecipe
  id: XenoTranslator
  result: XenoTranslator
  completetime: 2
  materials:
    Steel: 200
    Plastic: 50
    Gold: 50
    Plasma: 50
    Silver: 50

- type: latheRecipe
  id: AdvancedGalaticCommonTranslatorImplanter
  result: AdvancedGalaticCommonTranslatorImplanter
  completetime: 2
  materials:
    Steel: 500
    Glass: 500
    Plastic: 100
    Gold: 50
    Silver: 50

- type: latheRecipe
  id: BubblishTranslatorImplanter
  result: BubblishTranslatorImplanter
  completetime: 2
  materials:
    Steel: 500
    Glass: 500
    Plastic: 100
    Gold: 50
    Silver: 50

- type: latheRecipe
  id: NekomimeticTranslatorImplanter
  result: NekomimeticTranslatorImplanter
  completetime: 2
  materials:
    Steel: 500
    Glass: 500
    Plastic: 100
    Gold: 50
    Silver: 50

- type: latheRecipe
  id: DraconicTranslatorImplanter
  result: DraconicTranslatorImplanter
  completetime: 2
  materials:
    Steel: 500
    Glass: 500
    Plastic: 100
    Gold: 50
    Silver: 50

- type: latheRecipe
  id: CanilunztTranslatorImplanter
  result: CanilunztTranslatorImplanter
  completetime: 2
  materials:
    Steel: 500
    Glass: 500
    Plastic: 100
    Gold: 50
    Silver: 50

- type: latheRecipe
  id: SolCommonTranslatorImplanter
  result: SolCommonTranslatorImplanter
  completetime: 2
  materials:
    Steel: 500
    Glass: 500
    Plastic: 100
    Gold: 50
    Silver: 50

- type: latheRecipe
  id: RootSpeakTranslatorImplanter
  result: RootSpeakTranslatorImplanter
  completetime: 2
  materials:
    Steel: 500
    Glass: 500
    Plastic: 100
    Gold: 50
    Silver: 50

- type: latheRecipe
  id: MofficTranslatorImplanter
  result: MofficTranslatorImplanter
  completetime: 2
  materials:
    Steel: 500
    Glass: 500
    Plastic: 100
    Gold: 50
    Silver: 50

- type: latheRecipe
  id: AnimalTranslator
  result: AnimalTranslator
  completetime: 2
  materials:
    Steel: 200
    Plastic: 50
    Gold: 50
    Plasma: 50
    Silver: 5
