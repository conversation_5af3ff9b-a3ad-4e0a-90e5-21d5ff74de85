- type: species
  id: Reptilian
  name: species-name-reptilian
  roundStart: true
  prototype: MobReptilian
  sprites: MobReptilianSprites
  defaultSkinTone: "#34a223"
  markingLimits: MobReptilianMarkingLimits
  dollPrototype: MobReptilianDummy
  skinColoration: Hues
  maleFirstNames: names_reptilian_male
  femaleFirstNames: names_reptilian_female
  naming: FirstDashFirst
  minHeight: 0.7
  defaultHeight: 0.95
  maxHeight: 1.25
  minWidth: 0.65
  defaultWidth: 0.95
  maxWidth: 1.3

- type: speciesBaseSprites
  id: MobReptilianSprites
  sprites:
    Head: MobReptilianHead
    Face: MobHumanoidAnyMarking
    Snout: MobHumanoidAnyMarking
    Underwear: MobHumanoidAnyMarking
    Undershirt: MobHumanoidAnyMarking
    Chest: MobReptilianTorso
    HeadTop: MobHumanoidAnyMarking
    HeadSide: MobHumanoidAnyMarking
    Tail: MobHumanoidAnyMarking
    Eyes: MobHumanoidEyes
    LArm: MobReptilianLArm
    RArm: MobReptilianRArm
    LHand: MobReptilianLHand
    RHand: MobReptilianRHand
    LLeg: MobReptilianLLeg
    RLeg: MobReptilianRLeg
    LFoot: MobReptilianLFoot
    RFoot: MobReptilianRFoot

- type: markingPoints
  id: MobReptilianMarkingLimits
  onlyWhitelisted: true
  points:
    Hair:
      points: 0
      required: false
    FacialHair:
      points: 0
      required: false
    Tail:
      points: 1
      required: true
      defaultMarkings: [ LizardTailSmooth ]
    Snout:
      points: 1
      required: true
      defaultMarkings: [ LizardSnoutRound ]
    HeadTop:
      points: 6
      required: false
    HeadSide:
      points: 6
      required: false
    Chest:
      points: 6
      required: false
    Underwear:
      points: 1
      required: false
    Undershirt:
      points: 1
      required: false
    RightLeg:
      points: 6
      required: false
    RightFoot:
      points: 6
      required: false
    LeftLeg:
      points: 6
      required: false
    LeftFoot:
      points: 6
      required: false
    RightArm:
      points: 6
      required: false
    RightHand:
      points: 6
      required: false
    LeftArm:
      points: 6
      required: false
    LeftHand:
      points: 6
      required: false

- type: humanoidBaseSprite
  id: MobReptilianHead
  baseSprite:
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobReptilianHeadMale
  baseSprite:
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobReptilianHeadFemale
  baseSprite:
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: head_f

- type: humanoidBaseSprite
  id: MobReptilianTorso
  baseSprite:
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobReptilianTorsoMale
  baseSprite:
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobReptilianTorsoFemale
  baseSprite:
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: torso_f

- type: humanoidBaseSprite
  id: MobReptilianLLeg
  baseSprite:
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: l_leg

- type: humanoidBaseSprite
  id: MobReptilianLHand
  baseSprite:
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: l_hand

- type: humanoidBaseSprite
  id: MobReptilianLArm
  baseSprite:
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: l_arm

- type: humanoidBaseSprite
  id: MobReptilianLFoot
  baseSprite:
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: l_foot

- type: humanoidBaseSprite
  id: MobReptilianRLeg
  baseSprite:
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: r_leg

- type: humanoidBaseSprite
  id: MobReptilianRHand
  baseSprite:
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: r_hand

- type: humanoidBaseSprite
  id: MobReptilianRArm
  baseSprite:
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: r_arm

- type: humanoidBaseSprite
  id: MobReptilianRFoot
  baseSprite:
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: r_foot
