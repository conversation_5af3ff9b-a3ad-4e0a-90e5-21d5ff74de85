# When adding new food also add to random spawner located in Resources\Prototypes\Entities\Markers\Spawners\Random\Food_Drinks\food_single.yml
# Bun

- type: entity
  name: "булочка"
  parent: FoodBreadSliceBase
  id: FoodBreadBun
  description: "Булочка для гамбургера. Кругла і зручна для тримання."
  components:
  - type: Food
  - type: Sprite
    sprite: Objects/Consumable/Food/burger.rsi
    layers:
    - state: bun
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 6.66 # 1/3 of a loaf of bread, technically losing 0.01 nutriment per batch of three buns over making bread loaves/slices
  - type: Butcherable
    butcherDelay: 1
    spawned:
      - id: FoodBreadBunTop
        amount: 1
      - id: FoodBreadBunBottom
        amount: 1

- type: entity
  id: FoodBreadBunBottom
  parent: FoodBreadSliceBase
  name: "нижня булочка"
  description: "Час починати будувати вежу для бургерів."
  components:
  - type: Item
    size: Normal #patch until there is an adequate resizing system in place 
  - type: Food
  - type: Sprite
    drawdepth: Mobs
    noRot: true
    sprite: Objects/Consumable/Food/burger_sequence.rsi
    layers:
    - state: bun_bottom
    - map: ["foodSequenceLayers"]
  - type: FoodSequenceStartPoint
    key: Burger
    maxLayers: 10
    startPosition: 0, 0
    offset: 0, 0.07
    minLayerOffset: -0.05, 0
    maxLayerOffset: 0.05, 0
    nameGeneration: food-sequence-burger-gen
  - type: Appearance
  - type: FoodMetamorphableByAdding
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 5
        canReact: false # Dont want cause reactions inside burgers after merging ingredients
        reagents:
        - ReagentId: Nutriment
          Quantity: 3.3 # 1/2 of a bun

- type: entity
  id: FoodBreadBunTop
  parent: FoodBreadSliceBase
  name: "найкраща булочка"
  description: "ідеальне покриття для вашої бургерної"
  components:
  - type: Food
  - type: Sprite
    sprite: Objects/Consumable/Food/burger_sequence.rsi
    layers:
    - state: bun_top
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 5
        reagents:
        - ReagentId: Nutriment
          Quantity: 3.3 # 1/2 of a bun
  - type: FoodSequenceElement
    entries:
      Burger: BunTopBurger
  
# Base

- type: entity
  parent: FoodInjectableBase
  id: FoodBurgerBase
  abstract: true
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - meaty
  - type: Food
    transferAmount: 5
  - type: Sprite
    sprite: Objects/Consumable/Food/burger.rsi
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 7
        - ReagentId: Vitamin
          Quantity: 3
  - type: Item
    sprite: Objects/Consumable/Food/burger.rsi

# Custom Burger Example

- type: entity
  name: "желейний бургер"
  parent: FoodBurgerBase
  id: FoodBurgerJelly
  description: "Кулінарні шедеври...?"
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - jelly
  - type: Sprite
    layers:
    - state: custburg-bottom
    - state: custburg-filling
      color: red
    - state: custburg-top

# Burger

- type: entity
  name: "апендикс-бургер"
  parent: FoodBurgerBase
  id: FoodBurgerAppendix
  description: "На смак як апендицит."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - grass
  - type: Sprite
    state: appendix
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Nutriment
          Quantity: 2
        - ReagentId: Protein
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 6
  - type: Tag
    tags:
    - Meat
# Tastes like bun, grass.

- type: entity
  name: "бургер з беконом"
  parent: FoodBurgerBase
  id: FoodBurgerBacon
  description: "Ідеальне поєднання всього американського."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - bacon
  - type: Sprite
    state: bacon
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 30
        reagents:
        - ReagentId: Nutriment
          Quantity: 19
        - ReagentId: Vitamin
          Quantity: 5
  - type: Tag
    tags:
    - Meat
# Tastes like bun, bacon.

- type: entity
  name: "бейсбольний бургер"
  parent: FoodBurgerBase
  id: FoodBurgerBaseball
  description: "Він ще теплий. Пара, що йде від нього, пахне потом."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - homerun
  - type: Sprite
    state: baseball
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
        - ReagentId: Protein
          Quantity: 5
        - ReagentId: Vitamin
          Quantity: 2
  - type: Tag
    tags:
    - Meat

- type: entity
  name: "біржовик"
  parent: FoodBurgerBase
  id: FoodBurgerBear
  description: "Найкраще подавати сирим."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - meaty
  - type: Sprite
    state: bearger
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
        - ReagentId: Protein
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 5
  - type: Tag
    tags:
    - Meat

- type: entity
  name: "бургер з великим укусом"
  parent: FoodBurgerBase
  id: FoodBurgerBig
  description: "Забудьте про Біг Мак. Це майбутнє!"
  components:
  - type: FlavorProfile
    flavors: # What bun?
      - meaty
      - oily
      - cheesy
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 33
        reagents:
        - ReagentId: Nutriment
          Quantity: 17
        - ReagentId: Vitamin
          Quantity: 9
  - type: Sprite
    state: bigbite
  - type: Tag
    tags:
    - Meat
# Tastes like bun, silver, magic.

- type: entity
  name: "мозковий бургер"
  parent: FoodBurgerBase
  id: FoodBurgerBrain
  description: "Дивний на вигляд бургер. Виглядає майже живим."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - meaty
  - type: Sprite
    state: brain
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Nutriment
          Quantity: 6
        - ReagentId: Protein
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 5
  - type: Tag
    tags:
    - Meat
# Tastes like bun, brains.

- type: entity
  name: "котячий бургер"
  parent: FoodBurgerBase
  id: FoodBurgerCat
  description: "Нарешті ці коти та люди чогось варті!"
  components:
  - type: Sprite
    state: cat
  - type: FlavorProfile
    flavors:
      - bun
      - meaty
      - cat
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 6
        - ReagentId: Protein
          Quantity: 3
        - ReagentId: Vitamin
          Quantity: 2
  - type: Tag
    tags:
    - Meat

- type: entity
  name: "сирний бургер"
  parent: FoodBurgerBase
  id: FoodBurgerCheese
  description: "Цей благородний бургер гордо стоїть, вкритий золотистим сиром."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - meaty
      - cheesy
  - type: Sprite
    state: cheese
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
        - ReagentId: Vitamin
          Quantity: 7
        - ReagentId: Protein
          Quantity: 1
  - type: Tag
    tags:
    - Meat
# TODO: Make this work.
  # - type: Sprite
  #   state: plate
  # - type: RandomSpriteState
  #   spriteLayer: 1
  #   spriteStates:
  #     - cheese
  #     - cheesealt
# Tastes like bun, beef patty, cheese.

- type: entity
  name: "курячий сендвіч" # Burger for you euro-cucks
  parent: FoodBurgerBase
  id: FoodBurgerChicken
  description: "Смачний курячий сендвіч, кажуть, що виручка від цього частування допомагає криміналізувати роззброєння людей на космічному кордоні."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - chicken
  - type: Sprite
    state: chicken
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
        - ReagentId: Protein
          Quantity: 7
        - ReagentId: Vitamin
          Quantity: 1
        - ReagentId: Mayo
          Quantity: 3
  - type: Tag
    tags:
    - Meat
# Tastes like bun, chicken.

- type: entity
  name: "клоунський бургер"
  parent: FoodBurgerBase
  id: FoodBurgerClown
  description: "Дивний смак..."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - funny
  - type: Sprite
    state: clown
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 4
        - ReagentId: Protein
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 6
  - type: Tag
    tags:
    - Meat

- type: entity
  name: "корґер\"" #not curger
  parent: FoodBurgerBase
  id: FoodBurgerCorgi
  description: "Улюбленець керівника відділу кадрів!"
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - validhunting
  - type: Sprite
    state: ian
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 25
        reagents:
        - ReagentId: Bicaridine #keeping it somewhat consistent with the meat itself
          Quantity: 7
        - ReagentId: Vitamin
          Quantity: 12
  - type: Tag
    tags:
    - Meat

- type: entity
  name: "крабовий бургер"
  parent: FoodBurgerBase
  id: FoodBurgerCrab
  description: "Смачний крабовий пиріжок, затиснутий між булочкою."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - crabby
  - type: Sprite
    state: crab
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 4
        - ReagentId: Protein
          Quantity: 5
        - ReagentId: Vitamin
          Quantity: 4
  - type: Tag
    tags:
    - Meat

- type: entity
  name: "божевільний гамбургер" # Burger for you euro-cucks
  parent: FoodBurgerBase
  id: FoodBurgerCrazy
  description: "Це схоже на їжу, яку міг би приготувати божевільний клоун у плащі."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - meaty
      - cheesy
      - flare
  - type: Sprite
    state: crazy
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 25
        reagents:
        - ReagentId: Nutriment
          Quantity: 4
        - ReagentId: Protein
          Quantity: 6
        - ReagentId: CapsaicinOil
          Quantity: 3
        - ReagentId: Vitamin
          Quantity: 6
  - type: Tag
    tags:
    - Meat

- type: entity
  name: "бутерброд з качкою" # Burger for you sick bastards
  parent: FoodBurgerBase
  id: FoodBurgerDuck
  description: "Качиний сендвіч, тільки злочинно божевільний наважиться їсти м'ясо такого чарівного створіння."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - duck
  - type: Sprite
    state: chicken
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
        - ReagentId: Protein
          Quantity: 7
        - ReagentId: Vitamin
          Quantity: 1
  - type: Tag
    tags:
    - Meat
# Tastes like bun, duck.

- type: entity
  name: "заряджений бургер"
  parent: FoodBurgerBase
  id: FoodBurgerEmpowered
  description: "Це шокуюче добре, якщо ви живете за рахунок електрики."
  components:
  - type: Sprite
    state: empowered
  - type: FlavorProfile
    flavors:
      - bun
      - shocking
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 8
        - ReagentId: Licoxide
          Quantity: 5
# Tastes like bun, pure electricity.

- type: entity
  name: "бургер з філе коропа"
  parent: FoodBurgerBase
  id: FoodBurgerCarp
  description: "Наче короп десь кричить..."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - fishy
  - type: Sprite
    state: fish
  - type: Tag
    tags:
    - Meat
# Tastes like bun, fish.

- type: entity
  name: "бургер П'ять тривог"
  parent: FoodBurgerBase
  id: FoodBurgerFive
  description: "ГАРЯЧЕ! ГАРЯЧЕ! ГАРЯЧЕ!"
  components:
  - type: FlavorProfile
    flavors:
      - meaty
      - spicy
  - type: Sprite
    state: fivealarm
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 6
        - ReagentId: CapsaicinOil
          Quantity: 5
        - ReagentId: Blackpepper
          Quantity: 5
        - ReagentId: Vitamin
          Quantity: 1

# Tastes like bun, HEAT.

- type: entity
  name: "бургер-привид"
  parent: FoodBurgerBase
  id: FoodBurgerGhost
  description: "Занадто моторошно!"
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - spooky
  - type: Sprite
    state: ghost
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 25
        reagents:
        - ReagentId: Nutriment
          Quantity: 7
        - ReagentId: Vitamin
          Quantity: 12
  - type: Tag
    tags:
      - ClothMade
      - Meat
# Tastes like bun, ectoplasm.

- type: entity
  name: "людський бургер"
  parent: FoodBurgerBase
  id: FoodBurgerHuman
  description: "Не можна сказати, з кого це зроблено..."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - people
  - type: Sprite
    state: human
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 2
        - ReagentId: Protein
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 5
  - type: Tag
    tags:
    - Meat

- type: entity
  name: "Макгафін"
  parent: FoodBurgerBase
  id: FoodBurgerMcguffin
  description: "Дешева і жирна імітація яєчні \"Бенедикт\"."
  components:
  - type: FlavorProfile
    flavors:
      - muffin
      - egg
  - type: Sprite
    state: mcguffin
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 2
        - ReagentId: Protein
          Quantity: 7
        - ReagentId: Vitamin
          Quantity: 1
  - type: Tag
    tags:
    - Meat

- type: entity
  name: "Сендвіч з реберцями BBQ"
  parent: FoodBurgerBase
  id: FoodBurgerMcrib
  description: "Невловимий бургер у формі ребер з обмеженою доступністю по всій галактиці. Не такий смачний, як ви його пам'ятаєте."
  components:
  - type: Food
    trash: 
    - FoodKebabSkewer
  - type: FlavorProfile
    flavors:
      - bun
      - bacon
  - type: Sprite
    state: mcrib
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 2
        - ReagentId: Protein
          Quantity: 7
        - ReagentId: Vitamin
          Quantity: 4
        - ReagentId: BbqSauce
          Quantity: 1
  - type: Tag
    tags:
    - Meat
# Tastes like bun, pork patty.

- type: entity
  name: "мім-бургер"
  parent: FoodBurgerBase
  id: FoodBurgerMime
  description: "Його смак не піддається опису."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - nothing
  - type: Sprite
    state: mime
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 25
        reagents:
        - ReagentId: Nutriment
          Quantity: 6
        - ReagentId: Protein
          Quantity: 9
        - ReagentId: Vitamin
          Quantity: 4
        - ReagentId: Nothing
          Quantity: 1
# Tastes like  .

- type: entity
  name: "звичайний бургер"
  parent: FoodBurgerBase
  id: FoodBurgerPlain
  description: "Нудний, сухий бургер."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - meaty
  - type: Sprite
    state: plain
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 25
        reagents:
        - ReagentId: Nutriment
          Quantity: 2
        - ReagentId: Protein
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 3
  - type: Tag
    tags:
    - Meat

- type: entity
  name: "щурячий бургер"
  parent: FoodBurgerBase
  id: FoodBurgerRat
  description: "Майже те, чого можна було очікувати..."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - meaty
  - type: Sprite
    state: rat
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
        - ReagentId: Protein
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 2
  - type: Tag
    tags:
    - Meat
# Tastes like bun, HEAT.

- type: entity
  name: "робургер"
  parent: FoodBurgerBase
  id: FoodBurgerRobot
  description: "Листя салату - єдиний органічний компонент. Біп."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - leafy
      - metallic
  - type: Sprite
    state: ro
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: SulfuricAcid
          Quantity: 5
  - type: Tag
    tags:
    - Meat
# Tastes like bun, lettuce, sludge.

- type: entity
  name: "соковитий бургер"
  parent: FoodBurgerBase
  id: FoodBurgerSoy
  description: "Після цього у вас з'являється непереборне бажання придбати фігурки супергероїв за завищеними цінами."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - soy
  - type: Sprite
    state: soylent
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 4
        - ReagentId: Protein
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 4
# Tastes like bun, redditors.

- type: entity
  name: "бургер із заклинанням"
  parent: FoodBurgerBase
  id: FoodBurgerSpell
  description: "Це абсолютно Ей Нат."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - magical
  - type: Sprite
    state: spell
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Nutriment
          Quantity: 4
        - ReagentId: Protein
          Quantity: 5
        - ReagentId: Vitamin
          Quantity: 10
# Tastes like bun, silver.

- type: entity
  name: "бургер \"супер укус"
  parent: FoodBurgerBase
  id: FoodBurgerSuper
  description: "Це гора бургерів. ЇЖА!"
  components:
  - type: FlavorProfile
    flavors: # What bun?
      - meaty
      - oily
      - cheesy
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 55
        reagents:
        - ReagentId: Nutriment
          Quantity: 44
        - ReagentId: Vitamin
          Quantity: 24
        - ReagentId: TableSalt
          Quantity: 5
        - ReagentId: Blackpepper
          Quantity: 5
  - type: Sprite
    state: superbite
  - type: Tag
    tags:
    - Meat
# Tastes like bun, diabetes.

- type: entity
  name: "бургер з тофу"
  parent: FoodBurgerBase
  id: FoodBurgerTofu
  description: "Що... це за м'ясо?"
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - tofu
  - type: Sprite
    state: tofu
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
        - ReagentId: Protein
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 3
# Tastes like bun, tofu.

- type: entity
  name: "ксенобургер"
  parent: FoodBurgerBase
  id: FoodBurgerXeno
  description: "Пахне їдким. На смак як єресь."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - meaty
      - acid
  - type: Sprite
    state: x
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
        - ReagentId: Protein
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 5
  - type: Tag
    tags:
    - Meat
# Tastes like bun, acid.

# Note: I would put a bunch of colored burgers here as listed in the tg .dm but
# I'd rather wait for a custom burger component.

- type: entity
  name: "бургер з тарганомілі"
  parent: FoodBurgerBase
  id: FoodBurgerMothRoach
  description: "Останньою лампою, яку вона бачила, була лампа всередині мікрохвильовки. Монстр."
  components:
  - type: FlavorProfile
    flavors:
      - bun
      - meaty
      - light
  - type: Sprite
    state: mothroach
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
        - ReagentId: Protein
          Quantity: 4
        - ReagentId: Vitamin
          Quantity: 7
  - type: Tag
    tags:
    - Meat
    
