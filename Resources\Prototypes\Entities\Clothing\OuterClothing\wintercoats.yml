- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterWinterCoat
  name: "зимове пальто"
  description: "Важка куртка з \"синтетичного\" хутра тварин."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coat.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coat.rsi
  - type: TemperatureProtection
    coefficient: 0.1
  - type: Item
    size: Normal
  - type: Armor
    modifiers:
      coefficients:
        Slash: 0.95
        Heat: 0.90
    priceMultiplier: 0
  - type: Food
    requiresSpecialDigestion: true
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 30
        reagents:
        - ReagentId: Fiber
          Quantity: 30
  - type: Tag
    tags:
    - ClothMade
    - WhitelistChameleon
    - WinterCoat # Goobstation
  - type: StaticPrice
    price: 50

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterWinterCoatToggleable
  name: "зимове пальто з капюшоном"
  categories: [ HideSpawnMenu ]
  components:
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterDefault
    slot: head
  - type: ContainerContainer
    containers:
      toggleable-clothing: !type:Container {}
      storagebase: !type:Container
        ents: []
  - type: Tag # Goobstation
    tags:
    - ClothMade
    - WhitelistChameleon
    - WinterCoat

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterAtmos
  name: "зимове пальто атмосферного техніка"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatatmos.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatatmos.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterEngineer

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterBar
  name: "зимове пальто бармена"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatbar.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatbar.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterBartender

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterCap
  name: "зимове пальто капітана"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatcap.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatcap.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterCaptain
  - type: Armor # DeltaV - adds resists to coat, same as HoS coat. Still better than nothing, but nowhere as good as a carapace
    modifiers:
      coefficients:
        Blunt: 0.75
        Slash: 0.75
        Piercing: 0.75
        Heat: 0.75

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterCargo
  name: "зимове пальто вантажника" # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatcargo.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatcargo.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterCargo

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterCE
  name: "зимове пальто головного інженера"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatce.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatce.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterCE

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterCentcom
  name: "зимове пальто ЦентКом"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatcentcom.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatcentcom.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterCentcom

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterWinterChef
  name: "кухарський фартух для морозильника"
  description: "Пальто, спеціально розроблене для роботи в холодильних камерах, вкрай необхідне холоднокровним кухарям-ящіркам."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatchef.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatchef.rsi

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterChem
  name: "зимове пальто хіміка"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatchem.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatchem.rsi
  - type: Armor
    modifiers:
      coefficients:
        Slash: 0.95
        Heat: 0.90
        Caustic: 0.75
    priceMultiplier: 0.15
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterChem

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterCMO
  name: "зимове пальто головного лікаря"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatcmo.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatcmo.rsi
  - type: Armor
    modifiers:
      coefficients:
        Slash: 0.95
        Heat: 0.90
        Caustic: 0.75
    priceMultiplier: 0.15
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterCMO

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterWinterClown
  name: "зимове пальто клоуна"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatclown.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatclown.rsi

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterEngi
  name: "зимове пальто інженера"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatengi.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatengi.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterEngineer

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterGen
  name: "зимове пальто генетика"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatgen.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatgen.rsi
  - type: Armor
    modifiers:
      coefficients:
        Slash: 0.95
        Heat: 0.90
        Caustic: 0.9
    priceMultiplier: 0.15
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterSci

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterHoP
  name: "зимове пальто голови персоналу"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coathop.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coathop.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterHOP

- type: entity
  parent: [ClothingOuterArmorHoS, ClothingOuterWinterCoatToggleable]
  id: ClothingOuterWinterHoS
  name: "броньоване зимове пальто голови безпеки"
  description: "Міцне, утилітарне зимове пальто, призначене для захисту керівника служби безпеки від будь-яких загроз, пов'язаних з перебуванням у брігу та переохолодженням."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coathosarmored.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coathosarmored.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterHOS

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterHoSUnarmored
  name: "зимове пальто голови безпеки"
  description: "Міцне пальто, тепле пальто, але не бронежилет."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coathos.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coathos.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterHOS
  - type: Armor # DeltaV - adds resists to coat. As good as Cap's, but I don't know why you'd wear this over a trench coat
    modifiers:
      coefficients:
        Blunt: 0.75
        Slash: 0.75
        Piercing: 0.75
        Heat: 0.75
        Caustic: 0.75 #not the full 90% from ss13 because of the head

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterHydro
  name: "зимове пальто гідропоніки"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coathydro.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coathydro.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterHydro

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterJani
  name: "зимове пальто прибиральника"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatjani.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatjani.rsi
  - type: Armor
    modifiers:
      coefficients:
        Slash: 0.95
        Heat: 0.9
        Caustic: 0.9
    priceMultiplier: 0.15
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterJani

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterMed
  name: "зимовий пальто лікаря"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatmed.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatmed.rsi
  - type: Armor
    modifiers:
      coefficients:
        Slash: 0.95
        Heat: 0.90
        Caustic: 0.9
    priceMultiplier: 0.15
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterMed

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterMime
  name: "зимове пальто міма"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatmime.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatmime.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterMime

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterMiner
  name: "зимове пальто шахтаря"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatminer.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatminer.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterMiner

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterPara
  name: "зимове пальто парамедика"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatparamed.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatparamed.rsi
  - type: Armor
    modifiers:
      coefficients:
        Slash: 0.95
        Heat: 0.75
        Caustic: 0.9
    priceMultiplier: 0.15
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterPara

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterQM
  name: "зимове пальто офіцера логістики" # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatqm.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatqm.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterQM


- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterRD
  name: "зимове пальто наукового директора" # DeltaV - Epistemics Department replacing Science
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatrd.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatrd.rsi
  - type: Armor
    modifiers:
      coefficients:
        Slash: 0.95
        Heat: 0.90
        Caustic: 0.9
    priceMultiplier: 0.15
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterRD

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterRobo
  name: "зимове пальто робототехніка"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatrobo.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatrobo.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterRobo


- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterSci
  name: "зимове пальто епістеміки" # DeltaV - Epistemics Department replacing Science
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatsci.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatsci.rsi
  - type: Armor
    modifiers:
      coefficients:
        Slash: 0.95
        Heat: 0.90
        Caustic: 0.9
    priceMultiplier: 0.15
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterSci

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterSec
  name: "зимове пальто служби безпеки"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatsec.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatsec.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterSec
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.85
        Slash: 0.85
        Piercing: 0.85 #Can save you, but bullets will still hurt. Will take about 10 shots from a Viper before critting, as opposed to 7 while unarmored and 16~ with a bulletproof vest.
        Heat: 0.75

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterViro
  name: "зимове пальто вірусолога"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatviro.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatviro.rsi
  - type: Armor
    modifiers:
      coefficients:
        Slash: 0.95
        Heat: 0.90
        Caustic: 0.9
    priceMultiplier: 0.15
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterSci

- type: entity
  parent: [ClothingOuterArmorWarden, ClothingOuterWinterCoatToggleable]
  id: ClothingOuterWinterWarden
  name: "броньоване зимове пальто наглядача"
  description: "Міцне, утилітарне зимове пальто, призначене для захисту наглядача від будь-яких загроз та переохолоджень, пов'язаних з перебуванням у брігу."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatwardenarmored.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatwardenarmored.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterWarden

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterWardenUnarmored
  name: "зимове пальто наглядача"
  description: "Міцне пальто, тепле пальто, але не бронежилет."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatwarden.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatwarden.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterWarden

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterSyndieCap
  name: "зимове пальто синдикату"
  description: "Зимове пальто синдикату виготовлене з міцної тканини з позолоченими візерунками та грубої вовни."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/syndicate/coatsyndiecap.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/syndicate/coatsyndiecap.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterSyndie

- type: entity
  parent: ClothingOuterWinterWarden
  id: ClothingOuterWinterSyndieCapArmored
  name: "броньоване зимове пальто синдикату"
  description: "Броньоване зимове пальто синдикату виготовлене з міцної тканини з позолоченими візерунками та грубої вовни."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/syndicate/coatsyndiecaparmored.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/syndicate/coatsyndiecaparmored.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterSyndie

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterSyndie
  name: "зимове пальто синдикату"
  description: "Утеплене зимове пальто, схоже на мерч від \"Syndieland\""
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/syndicate/wintercoatsyndie.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/syndicate/wintercoatsyndie.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterSyndie

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterWinterMusician
  name: "зимове пальто музиканта"
  description: "Великий, пластиліновий космічний смокінг, який змусить людей запитувати: \"Ви мене знаєте?\""
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatnomi.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatnomi.rsi

- type: entity
  parent: ClothingOuterWinterCoatToggleable
  id: ClothingOuterWinterWeb
  name: "павутиння зимове пальто"
  description: "Напрочуд теплий і міцний."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/coatweb.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/coatweb.rsi
  - type: Construction
    graph: WebObjects
    node: coat
  - type: FlavorProfile
    flavors:
      - cobwebs
    ignoreReagents:
      - Fiber
  - type: SolutionContainerManager
    solutions: # 15 (3 (fiber count of web) * 5 (to craft)) + 5 (magical crafting bonus)
      food:
        maxVol: 20
        reagents:
        - ReagentId: Fiber
          Quantity: 20
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterWeb

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterZiCorporateJacket
  name: "Корпоративна куртка Zavodskoi Interstellar"
  description: "Затишна куртка з логотипом Zavodskoi Interstellar на спині. Товар, яким нагороджуються станції з коефіцієнтом безпеки... сім."
  components:
  - type: Sprite
    sprite: _EE/Clothing/corpo_jacket_zavodskoi_interstellar.rsi
  - type: Clothing
    sprite: _EE/Clothing/corpo_jacket_zavodskoi_interstellar.rsi

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterCsCorporateJacket
  name: "Корпоративна Куртка Cybersun"
  description: "Затишна куртка з логотипом Cybersun на спині. Нагороджуються станції з коефіцієнтом безпеки від е-е-е... семи."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/corpo_jacket_cybersun.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/corpo_jacket_cybersun.rsi

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterEeCorporateJacket
  name: "Корпоративна Куртка Einstein Engines"
  description: "Затишна куртка з логотипом Einstein Engines на спині. Нагороджуються станції з коефіцієнтом безпеки від е-е-е... семи."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/corpo_jacket_einstein_engines.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/corpo_jacket_einstein_engines.rsi
  - type: GuideHelp
    guides: [ EinsteinEngines ]

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterHiCorporateJacket
  name: "Корпоративна Куртка Hephaestus Industries"
  description: "Затишна куртка з логотипом Hephaestus Industries на спині. Нагороджуються станції з коефіцієнтом безпеки від е-е-е... семи."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/corpo_jacket_hephestus_industries.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/corpo_jacket_hephestus_industries.rsi
  - type: GuideHelp
    guides: [ HephaestusIndustries ]

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterHmCorporateJacket
  name: "Корпоративна Куртка Hawkmoon Acquisitions"
  description: "Затишна куртка з логотипом Hawkmoon Acquisitions на спині. Нагороджуються станції з коефіцієнтом безпеки від е-е-е... семи."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/corpo_jacket_hawkmoon_aquisitions.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/corpo_jacket_hawkmoon_aquisitions.rsi

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterIdCorporateJacket
  name: "Корпоративна Куртка Interdyne"
  description: "Затишна куртка з логотипом Interdyne на спині. Винагорода станціям з коефіцієнтом безпеки е-е-е... сім."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/corpo_jacket_interdyne.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/corpo_jacket_interdyne.rsi

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterBcCorporateJacket
  name: "Корпоративна Куртка Bishop Cybernetics"
  description: "Затишна куртка з логотипом Bishop Cybernetics на спині. Товар видається станціям з коефіцієнтом безпеки... е-е-е... сім."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/corpo_jacket_bishop_cybernetics.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/corpo_jacket_bishop_cybernetics.rsi
  - type: GuideHelp
    guides: [ ZengHuPharmaceuticals ]

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterDdCorporateJacket
  name: "Корпоративна Куртка Discount Dan's"
  description: "Затишна куртка з логотипом Discount Dan на спині. Винагорода на станціях з коефіцієнтом безпеки е-е-е... сім."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/corpo_jacket_discount_dans.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/corpo_jacket_discount_dans.rsi

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterFaCorporateJacket
  name: "Корпоративна Куртка Five Points Armory"
  description: "Затишна куртка з логотипом Five Points Armory на спині. Видаються на станціях з коефіцієнтом безпеки... сім."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/corpo_jacket_five_points_armory.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/corpo_jacket_five_points_armory.rsi

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterGeCorporateJacket
  name: "Корпоративна Куртка Gilithari Exports"
  description: "Затишна куртка з логотипом Gilthari Exports на спині. Товар, що видається станціям з коефіцієнтом безпеки е-е-е... сім."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/corpo_jacket_gilthari_exports.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/corpo_jacket_gilthari_exports.rsi

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterZhCorporateJacket
  name: "Корпоративна Куртка Zeng-Hu Pharmaceuticals"
  description: "Затишна куртка з логотипом Zeng-Hu Pharmaceuticals на спині. Винагорода на станціях з коефіцієнтом безпеки е-е-е... сім."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/WinterCoats/corpo_jacket_zeng_hu_pharma.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/WinterCoats/corpo_jacket_zeng_hu_pharma.rsi
  - type: GuideHelp
    guides: [ ZengHuPharmaceuticals ]

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterIiCorporateJacket
  name: "Корпоративна куртка Idris Incorporated"
  description: "Затишна куртка з логотипом Idris Incorporated на спині. Товар, яким нагороджуються станції з коефіцієнтом безпеки... сім."
  components:
  - type: Sprite
    sprite: _EE/Clothing/corpo_jacket_idris_incorporated.rsi
  - type: Clothing
    sprite: _EE/Clothing/corpo_jacket_idris_incorporated.rsi
  - type: GuideHelp
    guides: [ ZengHuPharmaceuticals ]

- type: entity
  parent: ClothingOuterWinterCoat
  id: ClothingOuterOeCorporateJacket
  name: "Корпоративна куртка Orion Express"

  description: "Затишна куртка з логотипом Orion Express на спині. Товар, яким нагороджуються станції з коефіцієнтом безпеки... сім."
  components:
  - type: Sprite
    sprite: _EE/Clothing/corpo_jacket_orion_express.rsi
  - type: Clothing
    sprite: _EE/Clothing/corpo_jacket_orion_express.rsi
  - type: GuideHelp
    guides: [ ZengHuPharmaceuticals ]
