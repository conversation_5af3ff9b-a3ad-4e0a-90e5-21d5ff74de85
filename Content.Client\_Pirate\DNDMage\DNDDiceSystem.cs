using Content.Shared._Pirate.DNDMage;
using Robust.Client.GameObjects;

namespace Content.Client._Pirate.DNDMage;

/// <summary>
/// Client-side system for handling DNDDiceComponent.
/// </summary>
public sealed class DNDDiceSystem : EntitySystem
{
    public override void Initialize()
    {
        base.Initialize();
        
        SubscribeLocalEvent<DNDDiceComponent, ComponentInit>(OnComponentInit);
        SubscribeLocalEvent<DNDBagDiceComponent, ComponentInit>(OnBagComponentInit);
    }

    private void OnComponentInit(EntityUid uid, DNDDiceComponent component, ComponentInit args)
    {
        // Client-side initialization logic if needed
    }
    
    private void OnBagComponentInit(EntityUid uid, DNDBagDiceComponent component, ComponentInit args)
    {
        // Client-side initialization logic if needed
    }
}
