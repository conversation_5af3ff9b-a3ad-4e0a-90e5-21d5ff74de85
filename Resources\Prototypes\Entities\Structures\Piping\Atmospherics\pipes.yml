- type: entity
  abstract: true
  id: GasPipeBase
  parent: BaseItem
  name: "труба"
  description: "Тримає газ."
  placement:
    mode: SnapgridCenter
  components:
  - type: Item
    size: Normal
  - type: Transform
    anchored: true
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: SubFloorHide
  - type: CollideOnAnchor
  - type: PipeAppearance
  - type: Anchorable
  - type: Rotatable
  - type: RCDDeconstructable
    cost: 1
    delay: 2
    fx: EffectRCDDeconstruct2
    rpd: true
  - type: Destructible
    thresholds:
    - trigger: # for nukes
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
    - trigger: # regular damage trigger (replace with broken pipe)
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
          params:
            volume: -8
      - !type:SpawnEntitiesBehavior
        spawn:
          GasPipeBroken:
            min: 1
            max: 1
        offset: 0
      - !type:DoActsBehavior
        acts: ["Breakage"]
  - type: Sprite
    sprite: Structures/Piping/Atmospherics/pipe.rsi
    drawdepth: ThinPipe
    visible: false
  - type: Appearance
  - type: PipeColorVisuals
  - type: NodeContainer
  # - type: PipeRestrictOverlap - Allow pipe stacking
  - type: AtmosUnsafeUnanchor
  - type: AtmosPipeColor
  - type: AtmosMonitoringConsoleDevice
  - type: Tag
    tags:
    - Pipe
  - type: Physics
    canCollide: false
    bodyType: static
  - type: StaticPrice
    price: 11
  - type: ContainerContainer # Goobstation - ventcrawl
    containers:
      VentCrawlerTube: !type:Container

- type: entity  # Goobstation - ventcrawl
  id: VentCrawlerHolder
  categories: [ HideSpawnMenu ]
  name: "тримач вентиляційного люка"
  components:
    - type: VentCrawlerHolder
    - type: InputMover
    - type: ContainerContainer
      containers:
        VentCrawlerHolderComponent: !type:Container

# Goobstation - Allow device-on-pipe stacking

- type: entity
  abstract: true
  parent: GasPipeBase
  id: GasPipeNoOverlap
  components:
  - type: PipeRestrictOverlap

#Note: The PipeDirection of the PipeNode should be the south-facing version, because the entity starts at an angle of 0 (south)

- type: entity
  parent: GasPipeNoOverlap
  id: GasPipeHalf
  suffix: Half
  components:
  - type: NodeContainer
    nodes:
      pipe:
        !type:PipeNode
        nodeGroupID: Pipe
        pipeDirection: South
  - type: Sprite
    layers:
      - state: pipeHalf
        map: [ "enum.PipeVisualLayers.Pipe" ]
  - type: Construction
    graph: GasPipe
    node: half

- type: entity
  parent: GasPipeBase
  id: GasPipeStraight
  suffix: Straight
  components:
  - type: NodeContainer
    nodes:
      pipe:
        !type:PipeNode
        nodeGroupID: Pipe
        pipeDirection: Longitudinal
  - type: VentCrawlerTube # Goobstation - ventcrawl
    containerId: VentCrawlerTube
  - type: VentCrawlerTransit # Goobstation - ventcrawl
  - type: ContainerContainer # Goobstation - ventcrawl
    containers:
      VentCrawlerTube: !type:Container
  - type: Sprite
    layers:
      - state: pipeStraight
        map: [ "enum.PipeVisualLayers.Pipe" ]
  - type: Construction
    graph: GasPipe
    node: straight
  - type: Item
    size: Normal
    storedSprite:
      sprite: Structures/Piping/Atmospherics/pipe.rsi
      state: storageStraight
    shape:
    - 0,0,0,2
  - type: MeleeWeapon
    attackRate: 1
    damage:
      types:
        Blunt: 8
    soundHit:
      collection: MetalThud # this NEEDS to changed to the metal pipe falling sound effect on april first every year

- type: entity
  parent: GasPipeNoOverlap
  id: GasPipeBend
  suffix: Bend
  components:
  - type: NodeContainer
    nodes:
      pipe:
        !type:PipeNode
        nodeGroupID: Pipe
        pipeDirection: SWBend
  - type: VentCrawlerTube # Goobstation - ventcrawl
    containerId: VentCrawlerTube
  - type: VentCrawlerBend # Goobstation - ventcrawl
  - type: ContainerContainer # Goobstation - ventcrawl
    containers:
      VentCrawlerTube: !type:Container
  - type: Sprite
    layers:
      - state: pipeBend
        map: [ "enum.PipeVisualLayers.Pipe" ]
  - type: Construction
    graph: GasPipe
    node: bend
  - type: Item
    size: Small
    shape:
    - 0,0,1,0
    - 1,1,1,1
    heldPrefix: Bend
    storedSprite:
      sprite: Structures/Piping/Atmospherics/pipe.rsi
      state: storageBend
  - type: MeleeWeapon
    wideAnimationRotation: 180
    attackRate: 1
    damage:
      types:
        Blunt: 6
    soundHit:
      collection: MetalThud
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 3 #This should be 6 but throwing damage is doubled at the moment for some reason so for now it's 3

- type: entity
  parent: GasPipeNoOverlap
  id: GasPipeTJunction
  suffix: TJunction
  components:
  - type: NodeContainer
    nodes:
      pipe:
        !type:PipeNode
        nodeGroupID: Pipe
        pipeDirection: TSouth
  - type: Sprite
    layers:
      - state: pipeTJunction
        map: [ "enum.PipeVisualLayers.Pipe" ]
  - type: Construction
    graph: GasPipe
    node: tjunction
  - type: Item
    size: Normal
    shape:
    - 0,0,2,0
    - 1,1,1,1
    heldPrefix: TJunction
    storedSprite:
      sprite: Structures/Piping/Atmospherics/pipe.rsi
      state: storageTJunction
  - type: MeleeWeapon
    wideAnimationRotation: 90
    attackRate: 0.75
    damage:
      types:
        Blunt: 10
    soundHit:
      collection: MetalThud
  - type: VentCrawlerTube # Goobstation - ventcrawl
    containerId: VentCrawlerTube
  - type: VentCrawlerJunction # Goobstation - ventcrawl
    degrees:
      - 0
      - 90
      - -90
  - type: ContainerContainer # Goobstation - ventcrawl
    containers:
      VentCrawlerTube: !type:Container

- type: entity
  parent: GasPipeNoOverlap
  id: GasPipeFourway
  suffix: Fourway
  components:
  - type: Transform
    noRot: true
  - type: NodeContainer
    nodes:
      pipe:
        !type:PipeNode
        nodeGroupID: Pipe
        pipeDirection: Fourway
  - type: Sprite
    layers:
      - state: pipeFourway
        map: [ "enum.PipeVisualLayers.Pipe" ]
  - type: Construction
    graph: GasPipe
    node: fourway
  - type: VentCrawlerTube # Goobstation - ventcrawl
    containerId: VentCrawlerTube
  - type: VentCrawlerJunction # Goobstation - ventcrawl
    degrees:
      - 0
      - 90
      - -90
      - 180
  - type: ContainerContainer # Goobstation - ventcrawl
    containers:
      VentCrawlerTube: !type:Container
  - type: Item
    size: Normal
    shape:
    - 1,0,1,2
    - 0,1,2,1
    heldPrefix: Fourway
  - type: MeleeWeapon
    wideAnimationRotation: 90
    attackRate: 0.75
    damage:
      types:
        Blunt: 10
    soundHit:
      collection: MetalThud

- type: entity
  id: GasPipeBroken
  parent: GasPipeNoOverlap
  name: "прорвана труба"
  description: "Там був газ."
  components:
  - type: Sprite
    sprite: Structures/Piping/Atmospherics/pipe.rsi
    drawdepth: ThinPipe
    state: pipeBroken
    layers:
      - state: pipeBroken
        map: [ "enum.PipeVisualLayers.Pipe" ]
  - type: Construction
    graph: GasPipe
    node: broken
  - type: Destructible
    thresholds: # override parent to avoid recursive destruction
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
