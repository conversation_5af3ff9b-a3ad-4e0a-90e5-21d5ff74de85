﻿# If you spawn any of these, they should probably be made cleanable as well.

- type: decal
  id: Dirty
  abstract: true

- type: decal
  id: Dirt
  parent: Dirty
  tags: ["station", "dirty"]
  defaultCleanable: true
  sprite:
    sprite: Decals/dirty.rsi
    state: dirt

- type: decal
  id: DirtLight
  parent: Dirty
  tags: ["station", "dirty"]
  defaultCleanable: true
  sprite:
    sprite: Decals/dirty.rsi
    state: dirtlight

- type: decal
  id: DirtMedium
  parent: Dirty
  tags: ["station", "dirty"]
  defaultCleanable: true
  sprite:
    sprite: Decals/dirty.rsi
    state: dirtmedium

- type: decal
  id: DirtHeavy
  parent: Dirty
  tags: ["station", "dirty"]
  defaultCleanable: true
  sprite:
    sprite: Decals/dirty.rsi
    state: dirtheavy

- type: decal
  id: DirtHeavyMonotile
  parent: Dirty
  tags: ["station", "dirty"]
  defaultCleanable: true
  sprite:
    sprite: Decals/dirty.rsi
    state: dirtheavy-mono

- type: decal
  id: Damaged
  parent: Dirty
  tags: ["station", "dirty"]
  defaultCleanable: true
  sprite:
    sprite: Decals/dirty.rsi
    state: damaged

- type: decal
  id: Remains
  parent: Dirty
  tags: ["station", "dirty"]
  defaultCleanable: true
  sprite:
    sprite: Decals/dirty.rsi
    state: remains

- type: decal
  id: Rust
  parent: Dirty
  tags: ["station", "dirty"]
  defaultCleanable: true
  sprite:
    sprite: Decals/dirty.rsi
    state: rust

