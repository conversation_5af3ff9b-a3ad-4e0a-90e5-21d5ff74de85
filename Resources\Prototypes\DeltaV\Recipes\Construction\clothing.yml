- type: construction
  name: "рецептурний медхуд"
  id: PrescriptionMedHud
  graph: PrescriptionMedHud
  startNode: start
  targetNode: prescmedhud
  category: construction-category-clothing
  description: "Рецепт медхуда, з'єднані разом окуляри і медхуд завдяки щасливому випадку і кабелям з клеєм."
  icon: { sprite: DeltaV/Clothing/Eyes/Hud/prescmedhud.rsi, state: icon }
  objectType: Item

- type: construction
  name: "рецептурний сечуд"
  id: PrescriptionHudSecurity
  graph: PrescriptionSecHud
  startNode: start
  targetNode: prescsechud
  category: construction-category-clothing
  description: "Сечуд за рецептом, злиті докупи склянки і сечуд завдяки щасливому випадку і кабелям з клеєм."
  icon: { sprite: DeltaV/Clothing/Eyes/Hud/prescsechud.rsi, state: icon }
  objectType: Item

- type: construction
  name: "окуляри санітара"
  id: ClothingEyesGlassesCorpsman
  graph: GlassesCorpsHUD
  startNode: start
  targetNode: glassesCorps
  category: construction-category-clothing
  description: "Пара сонцезахисних окулярів, модифікованих з вбудованим захисним та медичним HUD."
  icon: { sprite: DeltaV/Clothing/Eyes/Glasses/corpsglasses.rsi, state: icon }
  objectType: Item
