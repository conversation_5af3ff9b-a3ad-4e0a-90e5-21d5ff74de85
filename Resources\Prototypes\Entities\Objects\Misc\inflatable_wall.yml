- type: entity
  id: InflatableWall
  name: "надувна барикада"
  description: "Надута мембрана. Активувати, щоб здути. Не проколювати."
  components:
  - type: Clickable
  - type: InteractionOutline
  - type: Sprite
    sprite: Objects/Misc/inflatable_wall.rsi
    state: inflatable_wall
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.5,-0.5,0.5,0.5"
        density: 15
        layer:
        - WallLayer
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Inflatable
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 20
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: DisassembleOnAltVerb
    prototype: InflatableWallStack1
    doAfter: 3
  - type: Airtight
  - type: Transform
    anchored: true
  placement:
    mode: SnapgridCenter

- type: entity
  id: InflatableDoor
  name: "надувні двері"
  parent: BaseMaterialDoor
  description: "Надута мембрана. Активувати, щоб здути. Тепер з дверима. Не проколіть."
  components:
  - type: Clickable
  - type: InteractionOutline
  - type: Sprite
    sprite: Objects/Misc/inflatable_door.rsi
    state: closed
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.5,-0.5,0.5,0.5"
        density: 15
        layer:
        - WallLayer
  - type: Door
    occludes: false
    openSound:
      path: /Audio/Misc/zip.ogg
    closeSound:
      path: /Audio/Misc/zip.ogg
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Inflatable
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 20
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: DisassembleOnAltVerb
    prototype: InflatableDoorStack1
    doAfter: 3
  - type: Occluder
    enabled: false
