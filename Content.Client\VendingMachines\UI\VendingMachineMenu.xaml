<controls:FancyWindow
    xmlns="https://spacestation14.io"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
    xmlns:style="clr-namespace:Content.Client.Stylesheets">
    <BoxContainer Orientation="Vertical">
        <LineEdit Name="SearchBar" PlaceHolder="{Loc 'vending-machine-component-search-filter'}" HorizontalExpand="True"  Margin ="4 4" Access="Public"/>
        <!-- Pirate banking start -->
        <Label Name="CreditsLabel" HorizontalExpand="True"/>
        <Button
            Name="WithdrawButton"
            Text="{Loc 'store-ui-default-withdraw-text'}"
            HorizontalAlignment="Right"/>
        <!-- Pirate banking end -->
        <ItemList Name="VendingContents"
                  SizeFlagsStretchRatio="8"
                  VerticalExpand="True"
                  ItemSeparation="2"
                  Margin="4 0"
                  SelectMode="Button"
                  StyleClasses="transparentBackgroundItemList">
        </ItemList>
         <!-- Footer -->
        <BoxContainer Orientation="Vertical">
            <PanelContainer StyleClasses="LowDivider" />
            <BoxContainer Orientation="Horizontal" Margin="10 2 5 0" VerticalAlignment="Bottom">
                <Label Text="{Loc 'vending-machine-flavor-left'}" StyleClasses="WindowFooterText" />
                <Label Text="{Loc 'vending-machine-flavor-right'}" StyleClasses="WindowFooterText"
                        HorizontalAlignment="Right" HorizontalExpand="True"  Margin="0 0 5 0" />
                <TextureRect StyleClasses="NTLogoDark" Stretch="KeepAspectCentered"
                        VerticalAlignment="Center" HorizontalAlignment="Right" SetSize="19 19"/>
            </BoxContainer>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
