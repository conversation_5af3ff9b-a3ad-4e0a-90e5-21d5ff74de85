- type: entity
  save: false
  name: "Уріст МакХендс"
  parent: BaseMobHuman
  id: MobHuman

#Syndie
- type: entity
  parent: MobHuman
  id: MobHumanSyndicateAgentBase
  name: "агент синдикату"
  suffix: Human, Base
  components:
    - type: Tag
      tags:
      - CanPilot
      - FootstepSound
      - DoorBumpOpener
      - IgnoreBalanceChecks # Pirate banking
    - type: Loadout
      prototypes: [SyndicateOperativeGearExtremelyBasic]
    - type: RandomMetadata
      nameSegments: [names_death_commando]
    - type: NpcFactionMember
      factions:
      - Syndicate

- type: entity
  parent: MobHumanSyndicateAgentBase
  id: MobHumanSyndicateAgent
  name: "агент синдикату"
  suffix: Human, Traitor
  components:
  # make the player a traitor once its taken
  - type: AutoTraitor
    profile: TraitorReinforcement

- type: entity
  parent: MobHumanSyndicateAgentBase
  id: MobHumanSyndicateAgentNukeops # Reinforcement exclusive to nukeops uplink
  suffix: Human, NukeOps
  components:
    - type: NukeOperative

# Nuclear Operative
- type: entity
  categories: [ HideSpawnMenu ]
  name: "Ядерний оперативник"
  parent: MobHuman
  id: MobHumanNukeOp
  components:
    - type: Tag
      tags:
      - CanPilot
      - FootstepSound
      - DoorBumpOpener
      - IgnoreBalanceChecks # Pirate banking
    - type: NukeOperative
    - type: RandomHumanoidAppearance
    - type: Psionic
      powerRollMultiplier: 7

- type: entity
  categories: [ HideSpawnMenu ]
  parent: MobHuman
  id: MobHumanLoneNuclearOperative
  name: "Самотній оперативник"
  components:
    - type: Tag
      tags:
      - CanPilot
      - FootstepSound
      - DoorBumpOpener
      - IgnoreBalanceChecks # Pirate banking
    - type: RandomHumanoidAppearance
      randomizeName: false
    - type: NukeOperative
    - type: Loadout
      prototypes: [SyndicateOperativeGearFull]
    - type: RandomMetadata
      nameSegments:
      - SyndicateNamesPrefix
      - SyndicateNamesNormal
    - type: NpcFactionMember
      factions:
      - Syndicate
    - type: Psionic
      powerRollMultiplier: 7
