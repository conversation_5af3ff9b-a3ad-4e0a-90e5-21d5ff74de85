using System.Linq;
using System.Text;
using Content.Client.Players.PlayTimeTracking;
using Content.Client.Stylesheets;
using Content.Shared.Customization.Systems;
using Content.Shared.Preferences;
using Content.Shared.Roles;
using Content.Shared.Traits;
using Robust.Client.AutoGenerated;
using Robust.Client.Graphics;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Configuration;
using Robust.Shared.Prototypes;
using Robust.Shared.Utility;

namespace Content.Client.Lobby.UI;


[GenerateTypedNameReferences]
public sealed partial class TraitPreferenceSelector : Control
{
    public TraitPrototype Trait { get; }

    public bool Valid;
    private bool _showUnusable;
    public bool ShowUnusable
    {
        get => _showUnusable;
        set
        {
            _showUnusable = value;
            Visible = Valid || _showUnusable;
            PreferenceButton.RemoveStyleClass(StyleBase.ButtonDanger);
            PreferenceButton.AddStyleClass(Valid ? "" : StyleBase.ButtonDanger);
        }
    }

    public bool Preference
    {
        get => PreferenceButton.Pressed;
        set => PreferenceButton.Pressed = value;
    }

    public event Action<bool>? PreferenceChanged;

    public TraitPreferenceSelector(TraitPrototype trait, JobPrototype highJob, HumanoidCharacterProfile profile,
        IEntityManager entityManager, IPrototypeManager prototypeManager, IConfigurationManager configManager,
        CharacterRequirementsSystem characterRequirementsSystem, JobRequirementsManager jobRequirementsManager)
    {
        RobustXamlLoader.Load(this);

        Trait = trait;

        // Create a checkbox to get the loadout
        PreferenceButton.AddChild(new BoxContainer
        {
            Children =
            {
                new Label
                {
                    Text = trait.Points.ToString(),
                    StyleClasses = { StyleBase.StyleClassLabelHeading },
                    MinWidth = 32,
                    MaxWidth = 32,
                    ClipText = true,
                    Margin = new(0, 0, 8, 0),
                },
                new Label
                {
                    Text = Loc.GetString($"trait-name-{trait.ID}"),
                    Margin = new(8, 0, 0, 0),
                },
            },
        });
        PreferenceButton.OnToggled += OnPrefButtonToggled;


        var tooltip = new Tooltip();
        PreferenceButton.TooltipSupplier = _ => tooltip;
        var toolBox = (BoxContainer) tooltip.Children.First();

        // Add the loadout description to the tooltip if there is one
        var desc = Loc.GetString($"trait-description-{trait.ID}");
        if (!string.IsNullOrEmpty(desc) && desc != $"trait-description-{trait.ID}")
            tooltip.SetMessage(FormattedMessage.FromMarkupPermissive(desc));
        if (trait.Requirements.Any())
        {
            toolBox.AddChild(
                new Label
                {
                    Text = Loc.GetString("character-requirement-desc"),
                    StyleClasses = { StyleBase.StyleClassLabelHeading, },
                    Margin = new(0, 8, 0, 4),
                });

            MakeTooltipTree(toolBox, trait.Requirements);
            toolBox.AddChild(new() { Margin = new(0, 2), });
        }

        return;

        void MakeTooltipTree(BoxContainer box, List<CharacterRequirement> requirements)
        {
            foreach (var requirement in requirements)
            {
                if (requirement is CharacterLogicRequirement logicRequirement)
                {
                    requirement.IsValid(
                        highJob, profile, new(), jobRequirementsManager.IsWhitelisted(), trait,
                        entityManager, prototypeManager, configManager, out var reason);
                    box.AddChild(new RichTextLabel { Text = reason?.Split("\n")[0], Margin = new(8, 2), });
                    var newBox = new BoxContainer { Orientation = BoxContainer.LayoutOrientation.Vertical, };
                    box.AddChild(new PanelContainer
                        {
                            PanelOverride = new StyleBoxFlat
                            {
                                BackgroundColor = Color.FromHex("#1B1B1C"),
                                BorderColor = Color.FromHex("#3A3A3D"),
                                BorderThickness = new(1),
                            },
                            Margin = new(8, 2),
                            Children = { newBox, },
                        });
                    MakeTooltipTree(newBox, logicRequirement.Requirements);
                }
                else
                {
                    requirement.IsValid(
                        highJob, profile, new(), jobRequirementsManager.IsWhitelisted(), trait,
                        entityManager, prototypeManager, configManager, out var reason);
                    box.AddChild(new RichTextLabel
                    {
                        Text = reason,
                        Margin = new(8, 2),
                    });
                }
            }
        }
    }

    private void OnPrefButtonToggled(BaseButton.ButtonToggledEventArgs args)
    {
        PreferenceChanged?.Invoke(Preference);
    }
}
