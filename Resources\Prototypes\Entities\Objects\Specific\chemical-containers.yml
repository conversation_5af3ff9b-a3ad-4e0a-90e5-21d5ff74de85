- type: entity
  name: "бутль"
  parent: BaseItem
  id: Jug
  description: "Використовується для зберігання дуже великої кількості хімічних речовин або розчинів. Вживати його вкрай нерозумно."
  components:
    - type: SolutionContainerManager
      solutions:
        beaker:
          maxVol: 200
    - type: Sprite
      sprite: Objects/Specific/Chemistry/jug.rsi
      layers:
        - state: jug
        - state: jug1
          map: [ "enum.SolutionContainerLayers.Fill" ]
          visible: false
    - type: Item
      size: Normal
      sprite: Objects/Specific/Chemistry/jug.rsi
    - type: MixableSolution
      solution: beaker
    - type: RefillableSolution
      solution: beaker
    - type: DrainableSolution
      solution: beaker
    - type: ExaminableSolution
      solution: beaker
    - type: DrawableSolution
      solution: beaker
    - type: InjectableSolution
      solution: beaker
    - type: SolutionTransfer
      canChangeTransferAmount: true
    - type: SolutionItemStatus
      solution: beaker
    - type: UserInterface
      interfaces:
        enum.TransferAmountUiKey.Key:
          type: TransferAmountBoundUserInterface
    - type: Drink
      solution: beaker
    - type: Spillable
      solution: beaker
    - type: Appearance
    - type: SolutionContainerVisuals
      maxFillLevels: 6
      fillBaseName: jug
      inHandsMaxFillLevels: 5
      inHandsFillBaseName: -fill-
    - type: StaticPrice
      price: 60
    - type: Damageable
      damageContainer: Inorganic
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 200
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 20
        behaviors:
        - !type:PlaySoundBehavior
          sound:
            collection: MetalBreak
            params:
              volume: -4
        - !type:SpillBehavior { }
        - !type:SpawnEntitiesBehavior
          spawn:
            SheetPlastic1:
              min: 0
              max: 1
          transferForensics: true
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - type: Label
    - type: Tag
      tags:
      - ChemDispensable

- type: entity
  parent: Jug
  id: JugCarbon
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-carbon
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Carbon
              Quantity: 200

- type: entity
  parent: Jug
  id: JugIodine
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-iodine
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Iodine
              Quantity: 200

- type: entity
  parent: Jug
  id: JugFluorine
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-fluorine
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Fluorine
              Quantity: 200

- type: entity
  parent: Jug
  id: JugChlorine
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-chlorine
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Chlorine
              Quantity: 200

- type: entity
  parent: Jug
  id: JugAluminium
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-aluminium
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Aluminium
              Quantity: 200

- type: entity
  parent: Jug
  id: JugPhosphorus
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-phosphorus
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Phosphorus
              Quantity: 200

- type: entity
  parent: Jug
  id: JugSulfur
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-sulfur
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Sulfur
              Quantity: 200

- type: entity
  parent: Jug
  id: JugSilicon
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-silicon
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Silicon
              Quantity: 200

- type: entity
  parent: Jug
  id: JugHydrogen
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-hydrogen
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Hydrogen
              Quantity: 200

- type: entity
  parent: Jug
  id: JugLithium
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-lithium
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Lithium
              Quantity: 200

- type: entity
  parent: Jug
  id: JugSodium
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-sodium
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Sodium
              Quantity: 200

- type: entity
  parent: Jug
  id: JugPotassium
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-potassium
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Potassium
              Quantity: 200

- type: entity
  parent: Jug
  id: JugRadium
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-radium
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Radium
              Quantity: 200

- type: entity
  parent: Jug
  id: JugIron
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-iron
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Iron
              Quantity: 200

- type: entity
  parent: Jug
  id: JugCopper
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-copper
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Copper
              Quantity: 200

- type: entity
  parent: Jug
  id: JugGold
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-gold
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Gold
              Quantity: 200

- type: entity
  parent: Jug
  id: JugMercury
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-mercury
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Mercury
              Quantity: 200

- type: entity
  parent: Jug
  id: JugSilver
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-silver
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Silver
              Quantity: 200

- type: entity
  parent: Jug
  id: JugEthanol
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-ethanol
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Ethanol
              Quantity: 200

- type: entity
  parent: Jug
  id: JugSugar
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-sugar
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Sugar
              Quantity: 200

- type: entity
  parent: Jug
  id: JugNitrogen
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-nitrogen
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Nitrogen
              Quantity: 200

- type: entity
  parent: Jug
  id: JugOxygen
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-oxygen
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Oxygen
              Quantity: 200

- type: entity
  parent: Jug
  id: JugPlantBGone
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-plant-b-gone
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: PlantBGone
              Quantity: 200

- type: entity
  parent: Jug
  id: JugWeldingFuel
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-welding-fuel
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: WeldingFuel
              Quantity: 200

- type: entity
  parent: Jug
  id: JugNutriment
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-nutriment
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Nutriment
              Quantity: 200

- type: entity
  parent: Jug
  id: JugWater
  categories: [ HideSpawnMenu ]
  components:
    - type: Label
      currentLabel: reagent-name-water
    - type: SolutionContainerManager
      solutions:
        beaker:
          reagents:
            - ReagentId: Water
              Quantity: 200
