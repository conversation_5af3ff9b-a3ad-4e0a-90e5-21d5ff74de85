﻿<Control
    xmlns="https://spacestation14.io"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls"
    xmlns:at="clr-namespace:Content.Client.Administration.UI.Tabs.AtmosTab"
    Margin="4"
    MinSize="50 50">
    <GridContainer Columns="4">
        <cc:UICommandButton Text="{Loc Add Atmos}" Command="addatmos" WindowType="{x:Type at:AddAtmosWindow}" />
        <cc:UICommandButton Text="{Loc Add Gas}" Command="addgas" WindowType="{x:Type at:AddGasWindow}" />
        <cc:UICommandButton Text="{Loc Fill Gas}" Command="fillgas" WindowType="{x:Type at:FillGasWindow}" />
        <cc:UICommandButton Text="{Loc Set Temperature}" Command="settemp"
                             WindowType="{x:Type at:SetTemperatureWindow}" />
    </GridContainer>
</Control>
