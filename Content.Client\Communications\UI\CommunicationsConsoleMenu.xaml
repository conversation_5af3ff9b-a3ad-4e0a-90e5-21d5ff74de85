<controls:FancyWindow xmlns="https://spacestation14.io"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
    Title="{Loc 'comms-console-menu-title'}"
    MinSize="400 300">

    <!-- Main Container -->
    <BoxContainer Orientation="Vertical"
        HorizontalExpand="False"
        VerticalExpand="True"
        Margin="6 6 6 5">

        <TextEdit Name="MessageInput"
            VerticalExpand="True"
            HorizontalExpand="True"
            VerticalAlignment="Stretch"
            HorizontalAlignment="Stretch"
            MinHeight="100"/>

        <!-- ButtonsPart -->
        <BoxContainer Orientation="Vertical"
            VerticalAlignment="Bottom"
            SeparationOverride="4">

            <!-- AnnouncePart -->
            <BoxContainer Orientation="Vertical"
                Margin="0 2">

                <Button Name="AnnounceButton"
                    Access="Public"
                    Text="{Loc 'comms-console-menu-announcement-button'}"
                    ToolTip="{Loc 'comms-console-menu-announcement-button-tooltip'}"
                    StyleClasses="OpenLeft"
                    Margin="0 0 1 0"
                    Disabled="True"/>

                <Button Name="BroadcastButton"
                    Access="Public"
                    Text="{Loc 'comms-console-menu-broadcast-button'}"
                    ToolTip="{Loc 'comms-console-menu-broadcast-button-tooltip'}"
                    StyleClasses="OpenBoth"/>

                <OptionButton Name="AlertLevelButton"
                    Access="Public"
                    ToolTip="{Loc 'comms-console-menu-alert-level-button-tooltip'}"
                    StyleClasses="OpenRight"/>

            </BoxContainer>

            <!-- EmergencyPart -->
            <BoxContainer Orientation="Vertical"
                SeparationOverride="6">

                <RichTextLabel Name="CountdownLabel"/>

                <Button Name="EmergencyShuttleButton"
                    Access="Public" 
                    Text="Placeholder Text"
                    ToolTip="{Loc 'comms-console-menu-emergency-shuttle-button-tooltip'}"/>
            </BoxContainer>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
