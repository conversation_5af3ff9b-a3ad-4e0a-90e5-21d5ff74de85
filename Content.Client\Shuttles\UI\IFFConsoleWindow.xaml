<controls:FancyWindow xmlns="https://spacestation14.io"
                           xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                           Title="{Loc 'iff-console-window-title'}"
                           MinSize="200 200">
    <BoxContainer Orientation="Vertical" HorizontalExpand="True">
        <GridContainer Margin="4 0 0 0" Columns="2">
            <Label Name="ShowIFFLabel" Text="{Loc 'iff-console-show-iff-label'}" HorizontalExpand="True" StyleClasses="StatusFieldTitle" />
            <BoxContainer Orientation="Horizontal" MinWidth="120">
                <Button Name="ShowIFFOnButton" Text="{Loc 'iff-console-on'}" StyleClasses="OpenRight" />
                <Button Name="ShowIFFOffButton" Text="{Loc 'iff-console-off'}" StyleClasses="OpenLeft" />
            </BoxContainer>

            <Label Name="ShowVesselLabel" Text="{Loc 'iff-console-show-vessel-label'}" HorizontalExpand="True" StyleClasses="StatusFieldTitle" />
            <BoxContainer Orientation="Horizontal" MinWidth="120">
                <Button Name="ShowVesselOnButton" Text="{Loc 'iff-console-on'}" StyleClasses="OpenRight" />
                <Button Name="ShowVesselOffButton" Text="{Loc 'iff-console-off'}" StyleClasses="OpenLeft" />
            </BoxContainer>
        </GridContainer>
    </BoxContainer>
</controls:FancyWindow>
