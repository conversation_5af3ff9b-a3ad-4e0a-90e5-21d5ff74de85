- type: entity
  id: BaseMagazineUniversalMagnum
  name: "мк 32 'Універсальний' магазин (.45 магнум)"
  parent: BaseMagazinePistol
  abstract: true
  components:
  - type: Tag
    tags:
      - MagazineUniversalMagnum
  - type: BallisticAmmoProvider
    whitelist:
      tags:
        - CartridgeMagnum
    proto: CartridgeMagnum
    capacity: 12
  - type: Item
    size: Small
  - type: Sprite
    sprite: Nyanotrasen/Objects/Weapons/Guns/Ammunition/Magazine/Magnum/universal_mag.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 12
    zeroVisible: false
  - type: Appearance

# Magazines for the Universal Pistol

- type: entity
  id: MagazineUniversalMagnumEmpty
  name: "мк 32 'Універсальний' магазин (.45 магнум будь-який)"
  suffix: empty
  parent: BaseMagazineUniversalMagnum
  components:
  - type: BallisticAmmoProvider
    proto: null
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]

- type: entity
  id: MagazineUniversalMagnum
  name: "мк 32 'Універсальний' магазин (.45 магнум)"
  parent: BaseMagazineUniversalMagnum
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnum
    capacity: 12
  - type: Sprite
    layers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 12
    zeroVisible: false

- type: entity
  id: MagazineUniversalMagnumPractice
  name: "мк 32 'Універсальний' магазин (.45 магнум тренувальний)"
  parent: BaseMagazineUniversalMagnum
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumPractice
    capacity: 12
  - type: Sprite
    layers:
    - state: practice
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 12
    zeroVisible: false

- type: entity
  id: MagazineUniversalMagnumRubber
  name: "мк 32 'Універсальний' магазин (.45 магнум гумовий)"
  parent: BaseMagazineUniversalMagnum
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumRubber
    capacity: 12
  - type: Sprite
    layers:
    - state: rubber
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 12
    zeroVisible: false

- type: entity
  id: MagazineUniversalMagnumIncendiary
  name: "мк 32 'Універсальний' магазин (.45 магнум запалювальний)"
  parent: BaseMagazineUniversalMagnum
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumIncendiary
    capacity: 12
  - type: Sprite
    layers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 12
    zeroVisible: false

- type: entity
  id: MagazineUniversalMagnumUranium
  name: "мк 32 'Універсальний' магазин (.45 магнум урановий)"
  parent: BaseMagazineUniversalMagnum
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumUranium
    capacity: 12
  - type: Sprite
    layers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 12
    zeroVisible: false

- type: entity
  id: MagazineUniversalMagnumShrapnel
  name: "мк 32 'Універсальний' магазин (.45 магнум шрапнельний)"
  parent: BaseMagazineUniversalMagnum
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumShrapnel
    capacity: 12
  - type: Sprite
    layers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 12
    zeroVisible: false
