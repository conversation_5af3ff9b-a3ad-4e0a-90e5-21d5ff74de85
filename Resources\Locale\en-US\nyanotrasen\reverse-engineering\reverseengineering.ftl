reverse-engineering-machine-menu-title = reverse engineering machine
reverse-engineering-machine-server-list-button = Server List
reverse-engineering-machine-scan-button = Analyze
reverse-engineering-machine-scan-tooltip-info = Analyze the inserted item to attempt to reverse engineer it.
reverse-engineering-machine-safety-button = Safety
reverse-engineering-machine-safety-tooltip-info = Toggle safety protocols. Safety off will use stronger but possibly destructive methods of analysis.
reverse-engineering-machine-autoscan-button = AutoProbe
reverse-engineering-machine-autoscan-tooltip-info = Toggle whether to automatically start a new probe when the previous one finishes.
reverse-engineering-machine-stop-button = Stop
reverse-engineering-machine-stop-tooltip-info = Stop the current probe.
reverse-engineering-machine-eject-button = Eject
reverse-engineering-machine-eject-tooltip-info = Eject the current item.

reverse-engineering-status-ready = Insert item to reverse engineer.
reverse-engineering-current-item = Current item: {$item}
reverse-engineering-analysis-score = Analysis power: {$score}
reverse-engineering-item-difficulty = Difficulty: {$difficulty}
reverse-engineering-progress = Progress: {$progress}%
reverse-engineering-last-attempt-result = Last probe result: {$result}

reverse-engineering-total-progress-label = Total

reverse-engineering-failure = CRITICAL FAILURE
reverse-engineering-stagnation = Minimal Progress
reverse-engineering-minor = Minor progress
reverse-engineering-average = Acceptable progress
reverse-engineering-major = Major progress
reverse-engineering-success = Breakthrough

reverse-engineering-machine-bonus-upgrade = Analysis power
reverse-engineering-machine-aversion-upgrade = Destruction aversion bonus

reverse-engineering-popup-failure = {CAPITALIZE(THE($machine))} blows smoke and debris everywhere!

reverse-engineering-examine = [color=yellow]This item can be reverse engineered. Difficulty: {$diff}[/color]
