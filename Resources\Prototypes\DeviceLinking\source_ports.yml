- type: sourcePort
  id: Pressed
  name: signal-port-name-pressed
  description: signal-port-description-pressed
  defaultLinks: [ Toggle, Trigger ]

- type: sourcePort
  id: On
  name: signal-port-name-on-transmitter
  description: signal-port-description-on-transmitter
  defaultLinks: [ On, Open, Forward, Trigger ]

- type: sourcePort
  id: Off
  name: signal-port-name-off-transmitter
  description: signal-port-description-off-transmitter
  defaultLinks: [ Off, Close ]

- type: sourcePort
  id: Status
  name: signal-port-name-status-transmitter
  description: signal-port-description-status-transmitter

- type: sourcePort
  id: Left
  name: signal-port-name-left
  description: signal-port-description-left
  defaultLinks: [ On, Open, Forward, Trigger ]

- type: sourcePort
  id: Right
  name: signal-port-name-right
  description: signal-port-description-right
  defaultLinks: [ On, Open, Reverse, Trigger ]

- type: sourcePort
  id: Middle
  name: signal-port-name-middle
  description: signal-port-description-middle
  defaultLinks: [ Off, Close ]

- type: sourcePort
  id: DoorStatus
  name: signal-port-name-doorstatus
  description: signal-port-description-doorstatus
  defaultLinks: [ DoorBolt ]

- type: sourcePort
  id: DockStatus
  name: signal-port-name-dockstatus
  description: signal-port-description-dockstatus

- type: sourcePort
  id: OrderSender
  name: signal-port-name-order-sender
  description: signal-port-description-order-sender
  defaultLinks: [ OrderReceiver ]

- type: sourcePort
  id: CloningPodSender
  name: signal-port-name-pod-receiver
  description: signal-port-description-pod-sender

- type: sourcePort
  id: MedicalScannerSender
  name: signal-port-name-med-scanner-sender
  description: signal-port-description-med-scanner-sender

- type: sourcePort
  id: ArtifactAnalyzerSender
  name: signal-port-name-artifact-analyzer-sender
  description: signal-port-description-artifact-analyzer-sender
  defaultLinks: [ ArtifactAnalyzerReceiver ]

- type: sourcePort
  id: Timer
  name: signal-port-name-timer-trigger
  description: signal-port-description-timer-trigger
  defaultLinks: [ AutoClose, On, Open, Forward, Trigger ]

- type: sourcePort
  id: Start
  name: signal-port-name-timer-start
  description: signal-port-description-timer-start
  defaultLinks: [ Close, Off ]

- type: sourcePort
  id: Output
  name: signal-port-name-logic-output
  description: signal-port-description-logic-output
  defaultLinks: [ Input ]

- type: sourcePort
  id: OutputHigh
  name: signal-port-name-logic-output-high
  description: signal-port-description-logic-output-high
  defaultLinks: [ On, Open, Forward, Trigger ]

- type: sourcePort
  id: OutputLow
  name: signal-port-name-logic-output-low
  description: signal-port-description-logic-output-low
  defaultLinks: [ Off, Close ]

- type: sourcePort
  id: AirDanger
  name: signal-port-name-air-danger
  description: signal-port-description-air-danger
  defaultLinks: [ DoorBolt ]

- type: sourcePort
  id: AirWarning
  name: signal-port-name-air-warning
  description: signal-port-description-air-warning
  defaultLinks: [ DoorBolt ]

- type: sourcePort
  id: AirNormal
  name: signal-port-name-air-normal
  description: signal-port-description-air-normal
  defaultLinks: [ DoorBolt ]

- type: sourcePort
  id: Decaying
  name: signal-port-name-decaying
  description: signal-port-description-decaying

- type: sourcePort
  id: Stabilize
  name: signal-port-name-stabilize
  description: signal-port-description-stabilize

- type: sourcePort
  id: Growing
  name: signal-port-name-growing
  description: signal-port-description-growing

- type: sourcePort
  id: Pulse
  name: signal-port-name-pulse
  description: signal-port-description-pulse

- type: sourcePort
  id: Supercritical
  name: signal-port-name-supercrit
  description: signal-port-description-supercrit

- type: sourcePort
  id: PowerCharging
  name: signal-port-name-power-charging
  description: signal-port-description-power-charging

- type: sourcePort
  id: PowerDischarging
  name: signal-port-name-power-discharging
  description: signal-port-description-power-discharging

- type: sourcePort
  id: MaterialSilo
  name: signal-port-name-material-silo
  description: signal-port-description-material-silo
  defaultLinks: [ MaterialSiloUtilizer ]

- type: sourcePort
  id: FillItems # Used by Fillbot
  name: signal-port-name-fillitems
  description: signal-port-description-fillitems
  defaultLinks: [ FillbotAnyItem ]
