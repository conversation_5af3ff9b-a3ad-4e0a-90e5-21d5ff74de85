- type: entity
  save: false
  name: "Уріст Маквебс"
  parent: BaseMobSpeciesOrganic
  id: BaseMobArachnid
  abstract: true
  components:
  - type: Body
    prototype: Arachnid
    requiredLegs: 2 # It would be funny if arachnids could use their little back limbs to move around once they lose their legs, but just something to consider post-woundmed
  - type: HumanoidAppearance
    species: Arachnid
  - type: Hunger
    baseDecayRate: 0.0125 # Spiders have slow metabolisms all things considered, so I decided to just make their hunger drain slower.
  - type: Carriable # Carrying system from nyanotrasen.
  - type: Thirst
  - type: Sericulture
    action: ActionSericulture
    productionLength: 2
    entityProduced: MaterialWebSilk1
    hungerCost: 4 # Should total to 25 total silk on full hunger
  - type: Tag
    tags:
    - CanPilot
    - FootstepSound
    - DoorBumpOpener
    - SpiderCraft
  - type: Butcherable
    butcheringType: Spike
    spawned:
      - id: FoodMeatSpider
        amount: 5
  - type: Inventory
    templateId: arachnid
  - type: Reactive
    reactions:
    - reagents: [Water]
      methods: [Touch]
      effects:
      - !type:WearableReaction
        slot: head
        prototypeID: WaterDropletHat
    - reagents: [Water, SpaceCleaner]
      methods: [Touch]
      effects:
      - !type:WashCreamPieReaction
  - type: BloodSucker
    webRequired: true
  - type: Cocooner
  # Damage (Self)
  - type: Bloodstream
    bloodReagent: CopperBlood
  # Damage (Others)
  - type: MeleeWeapon
    animation: WeaponArcBite
    soundHit:
      path: /Audio/Effects/bite.ogg
    damage:
      types:
        Piercing: 5
  # Visual & Audio
  - type: DamageVisuals
    damageOverlayGroups:
      Brute:
        sprite: Mobs/Effects/brute_damage.rsi
        color: "#162581"
  - type: Speech
    speechVerb: Arachnid
    speechSounds: Arachnid
    allowedEmotes: ['Click', 'Chitter', 'Hiss'] # DeltaV: Added Hiss
  - type: Vocal
    sounds:
      Male: UnisexArachnid
      Female: UnisexArachnid
      Unsexed: UnisexArachnid
  - type: TypingIndicator
    proto: spider
  - type: Sprite # I'd prefer if these maps were better. Insert map pun here.
    layers:
      - map: [ "enum.HumanoidVisualLayers.Chest" ]
      - map: [ "enum.HumanoidVisualLayers.Head" ]
      - map: [ "enum.HumanoidVisualLayers.Snout" ]
      - map: [ "enum.HumanoidVisualLayers.Eyes" ]
      - map: [ "enum.HumanoidVisualLayers.Face" ]
      - map: [ "enum.HumanoidVisualLayers.RArm" ]
      - map: [ "enum.HumanoidVisualLayers.LArm" ]
      - map: [ "enum.HumanoidVisualLayers.RLeg" ]
      - map: [ "enum.HumanoidVisualLayers.LLeg" ]
#     - shader: StencilClear
#      sprite: Mobs/Species/Human/parts.rsi
#      state: l_leg
      - map: [ "enum.HumanoidVisualLayers.Underwear" ]
      - map: [ "enum.HumanoidVisualLayers.Undershirt" ]
      - map: ["jumpsuit"]
      - map: ["enum.HumanoidVisualLayers.LFoot"]
      - map: ["enum.HumanoidVisualLayers.RFoot"]
      - map: ["enum.HumanoidVisualLayers.LHand"]
      - map: ["enum.HumanoidVisualLayers.RHand"]
      - map: [ "gloves" ]
      - map: [ "shoes" ]
      - map: [ "ears" ]
      - map: [ "innerBelt" ]
      - map: [ "innerNeck" ]
      - map: [ "outerClothing" ]
      - map: [ "eyes" ]
      - map: [ "belt" ]
      - map: [ "id" ]
      - map: [ "enum.HumanoidVisualLayers.Tail" ] # Mentioned in moth code: This needs renaming lol.
      - map: [ "neck" ]
      - map: [ "back" ]
      - map: [ "enum.HumanoidVisualLayers.FacialHair" ]
      - map: [ "enum.HumanoidVisualLayers.Hair" ] # Do these need to be here? (arachnid hair arachnid hair)
      - map: [ "enum.HumanoidVisualLayers.HeadSide" ]
      - map: [ "enum.HumanoidVisualLayers.HeadTop" ]
      - map: [ "mask" ]
      - map: [ "head" ]
      - map: [ "neck2" ] #PIRATE
      - map: [ "neck1" ] #PIRATE
      - map: [ "head2" ] #PIRATE
      - map: [ "head1" ] #PIRATE
      - map: [ "pocket1" ]
      - map: [ "pocket2" ]
      - map: ["enum.HumanoidVisualLayers.Handcuffs"]
        color: "#ffffff"
        sprite: Objects/Misc/handcuffs.rsi
        state: body-overlay-2
        visible: false
      - map: [ "clownedon" ] # Dynamically generated
        sprite: "Effects/creampie.rsi"
        state: "creampie_arachnid"
        visible: false
  - type: Spider
  - type: IgnoreSpiderWeb
  - type: FootPrints
    leftBarePrint: "footprint-left-bare-spider"
    rightBarePrint: "footprint-right-bare-spider"

- type: entity
  parent: BaseSpeciesDummy
  id: MobArachnidDummy
  categories: [ HideSpawnMenu ]
  components:
  - type: HumanoidAppearance
    species: Arachnid

#88w88
