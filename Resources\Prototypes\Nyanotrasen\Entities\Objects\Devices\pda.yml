- type: entity
  parent: BasePDA
  id: PrisonerPDA
  name: "кПК в'язня"
  description: "Чисто, щоб ви могли переконатися, що всередині нічого не проносять контрабандою."
  components:
  - type: Pda
    id: PrisonerIDCard
    state: pda-clear
  - type: Icon
    state: pda-clear

- type: entity
  parent: PrisonerPDA
  id: GladiatorPDA
  name: "гладіатор КПК"
  components:
  - type: Pda
    id: GladiatorIDCard

- type: entity
  parent: BasePDA
  id: PrisonGuardPDA
  name: "тюремний охоронець КПК"
  description: "Червоний, щоб приховати плями крові в'язнів."
  components:
  - type: Pda
    id: PrisonGuardIDCard
    state: pda-security
  - type: PdaBorderColor
    borderColor: "#A32D26"
    accentVColor: "#DFDFDF"
  - type: Icon
    state: pda-security
  - type: CartridgeLoader # Adds Crime Assist and Sec<PERSON>atch
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - CrimeAssistCartridge
      - SecWatchCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge

- type: entity
  parent: BasePDA
  id: MailCarrierPDA
  name: "кур'єрський КПК"
  description: "Пахне нерозпечатаними листами."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Devices/pda.rsi
    layers:
    - map: [ "enum.PdaVisualLayers.Base" ]
    - state: "light_overlay"
      map: [ "enum.PdaVisualLayers.Flashlight" ]
      shader: "unshaded"
      visible: false
    - state: "id_overlay"
      map: [ "enum.PdaVisualLayers.IdLight" ]
      shader: "unshaded"
      visible: false
  - type: Pda
    id: MailCarrierIDCard
    state: pda-mailcarrier
  - type: PdaBorderColor
    borderColor: "#e39751"
    accentVColor: "#050c4d"
  - type: Icon
    sprite: DeltaV/Objects/Devices/pda.rsi
    state: pda-mailcarrier
  - type: CartridgeLoader # Adds a courier performance tracker
    preinstalled:
    - CrewManifestCartridge
    - NotekeeperCartridge
    - NewsReaderCartridge
    - MailMetricsCartridge
    - NanoChatCartridge

- type: entity
  parent: BasePDA
  id: MartialArtistPDA
  name: "бойовий художник КПК"
  description: "Пахне соломою."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Devices/pda.rsi
    layers:
    - map: [ "enum.PdaVisualLayers.Base" ]
    - state: "light_overlay"
      map: [ "enum.PdaVisualLayers.Flashlight" ]
      shader: "unshaded"
      visible: false
    - state: "id_overlay"
      map: [ "enum.PdaVisualLayers.IdLight" ]
      shader: "unshaded"
      visible: false
  - type: Pda
    id: MartialArtistIDCard
    state: pda-martialartist
  - type: PdaBorderColor
    borderColor: "#d7d7d0"
    accentHColor: "#232323"
  - type: Icon
    sprite: DeltaV/Objects/Devices/pda.rsi
    state: pda-martialartist

- type: entity
  parent: BasePDA
  id: ForensicMantisPDA
  name: "кПК мантіса"
  description: "Пахне забороненими речовинами."
  components:
  - type: Pda
    id: ForensicMantisIDCard
    state: pda-science
  - type: Icon
    state: pda-science
  - type: CartridgeLoader
    preinstalled:
     - CrewManifestCartridge
     - NotekeeperCartridge
     - NewsReaderCartridge
     - GlimmerMonitorCartridge
     - WalletCartridge # Pirate banking
     - NanoChatCartridge
     - PsiWatchCartridge
