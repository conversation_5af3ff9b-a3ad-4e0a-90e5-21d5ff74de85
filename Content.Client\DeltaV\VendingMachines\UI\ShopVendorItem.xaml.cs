using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;

namespace Content.Client.DeltaV.VendingMachines.UI;

[GenerateTypedNameReferences]
public sealed partial class ShopVendorItem : BoxContainer
{
    public ShopVendorItem(EntProtoId entProto, string text, uint cost)
    {
        RobustXamlLoader.Load(this);

        ItemPrototype.SetPrototype(entProto);

        NameLabel.Text = text;

        CostLabel.Text = cost.ToString();
    }
}
