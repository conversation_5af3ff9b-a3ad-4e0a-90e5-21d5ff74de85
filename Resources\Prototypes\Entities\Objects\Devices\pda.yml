- type: entity
  abstract: true
  parent: [ BaseItem, StorePresetUplink ] #PDA's have uplinks so they have to inherit the data.
  id: BasePDA
  name: "КПК"
  description: "Асистент персональних даних."
  components:
  - type: Appearance
  - type: Sprite
    sprite: Objects/Devices/pda.rsi
    layers:
    - map: [ "enum.PdaVisualLayers.Base" ]
    - state: "light_overlay"
      map: [ "enum.PdaVisualLayers.Flashlight" ]
      shader: "unshaded"
      visible: false
    - state: "id_overlay"
      map: [ "enum.PdaVisualLayers.IdLight" ]
      shader: "unshaded"
      visible: false
  - type: Icon
    sprite: Objects/Devices/pda.rsi
    state: pda
  - type: Pda
    state: pda
    paiSlot:
      priority: -2
      whitelist:
        components:
        - PAI
    penSlot:
      startingItem: Pen
      priority: -1
      whitelist:
        tags:
        - Write
    idSlot:
      name: access-id-card-component-default
      ejectSound: /Audio/Machines/id_swipe.ogg
      insertSound: /Audio/Machines/id_insert.ogg
      whitelist:
        components:
        - IdCard
  - type: Item
    size: Small
  - type: ContainerContainer
    containers:
      PDA-id: !type:ContainerSlot {}
      PDA-pen: !type:ContainerSlot {}
      PDA-pai: !type:ContainerSlot {}
      Cartridge-Slot: !type:ContainerSlot {}
      program-container: !type:Container
  - type: ItemSlots
  - type: Clothing
    quickEquip: false
    slots:
    - idcard
    - Belt
  - type: UnpoweredFlashlight
  - type: PointLight
    enabled: false
    radius: 1.5
    softness: 5
    autoRot: true
  - type: Ringer
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: PDA
    prefix: device-address-prefix-console
    savableAddress: false
  - type: WirelessNetworkConnection
    range: 500
  - type: CartridgeLoader
    uiKey: enum.PdaUiKey.Key
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge # DeltaV
    cartridgeSlot:
      priority: -1
      name: device-pda-slot-component-slot-name-cartridge
      ejectSound: /Audio/Machines/id_swipe.ogg
      insertSound: /Audio/Machines/id_insert.ogg
      whitelist:
        components:
          - Cartridge
  - type: ActivatableUI
    key: enum.PdaUiKey.Key
    singleUser: true
  - type: UserInterface
    interfaces:
      enum.PdaUiKey.Key:
        type: PdaBoundUserInterface
      enum.StoreUiKey.Key:
        type: StoreBoundUserInterface
      enum.RingerUiKey.Key:
        type: RingerBoundUserInterface
      enum.InstrumentUiKey.Key:
        type: InstrumentBoundUserInterface
      enum.HealthAnalyzerUiKey.Key:
        type: HealthAnalyzerBoundUserInterface
  - type: Tag
    tags:
    - DoorBumpOpener
  - type: Input
    context: "human"
  - type: LanguageSpeaker
  - type: LanguageKnowledge
    speaks: [TauCetiBasic, RobotTalk]
    understands: [TauCetiBasic, RobotTalk]
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 4
  - type: SentienceTarget # sentient PDA = pAI lite
    flavorKind: station-event-random-sentience-flavor-mechanical
    weight: 0.001 # 1,000 PDAs = as likely to be picked as 1 regular animal
  - type: BlockMovement
    blockInteraction: false # lets the PDA toggle its own flashlight
  - type: TypingIndicator
    proto: robot
  - type: Speech
    speechVerb: Robotic

- type: entity
  parent: BasePDA
  id: BaseMedicalPDA
  abstract: true
  components:
  - type: CartridgeLoader
    uiKey: enum.PdaUiKey.Key
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - MedTekCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge # DeltaV

- type: entity
  parent: BasePDA
  id: PassengerPDA
  name: "кПК пасажира"
  description: "Чому він не сірий?"
  components:
  - type: Pda
    id: PassengerIDCard
    state: pda
  - type: PdaBorderColor
    borderColor: "#717059"

- type: entity
  parent: BasePDA
  id: TechnicalAssistantPDA
  name: "кПК технічного асистента"
  description: "Чому він не жовтий?"
  components:
  - type: Pda
    id: TechnicalAssistantIDCard
    state: pda-interntech
  - type: PdaBorderColor
    borderColor: "#717059"
    accentVColor: "#949137"
  - type: Icon
    state: pda-interntech

- type: entity
  parent: BaseMedicalPDA
  id: MedicalInternPDA
  name: "кПК лікаря-інтерна"
  description: "Чому він не білий?"
  components:
  - type: Pda
    id: MedicalInternIDCard
    state: pda-internmed
    penSlot: # Pen Lights
      startingItem: PenLightBase
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#717059"
    accentVColor: "#447987"
  - type: Icon
    state: pda-internmed
  - type: GuideHelp
    guides:
    - Medical Doctor

- type: entity
  parent: BasePDA
  id: SecurityCadetPDA
  name: "кПК кадета"
  description: "Чому він не червоний?"
  components:
  - type: Pda
    id: SecurityCadetIDCard
    state: pda-interncadet
  - type: PdaBorderColor
    borderColor: "#717059"
    accentVColor: "#A32D26"
  - type: Icon
    state: pda-interncadet
  - type: CartridgeLoader # DeltaV - Crime Assist + SecWatch
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - CrimeAssistCartridge
      - SecWatchCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge

- type: entity
  parent: BasePDA
  id: ResearchAssistantPDA
  name: "кПК наукового асистента"
  description: "Чому він не фіолетовий?"
  components:
  - type: Pda
    id: ResearchAssistantIDCard
    state: pda-internsci
  - type: PdaBorderColor
    borderColor: "#717059"
    accentVColor: "#8900c9"
  - type: Icon
    state: pda-internsci

- type: entity
  parent: BasePDA
  id: ServiceWorkerPDA
  name: "кПК сервісного працвника"
  description: "Чому він не сірий?"
  components:
  - type: Pda
    id: ServiceWorkerIDCard
    state: pda-internservice
  - type: PdaBorderColor
    borderColor: "#717059"
    accentVColor: "#00cc35"
  - type: Icon
    state: pda-internservice

- type: entity
  parent: BasePDA
  id: ChefPDA
  name: "кПК шеф-кухаря"
  description: "Покриваємо жиром і борошном."
  components:
  - type: Pda
    id: ChefIDCard
    state: pda-cook
  - type: PdaBorderColor
    borderColor: "#d7d7d0"
  - type: Icon
    state: pda-cook
  - type: ReplacementAccent # for random sentience event
    accent: italian

- type: entity
  parent: BasePDA
  id: BotanistPDA
  name: "кПК ботаніка"
  description: "Має землистий аромат."
  components:
  - type: Pda
    id: BotanistIDCard
    state: pda-hydro
  - type: PdaBorderColor
    borderColor: "#44843c"
    accentVColor: "#00cc35"
  - type: Icon
    state: pda-hydro

- type: entity
  parent: BasePDA
  id: ClownPDA
  name: "кПК клоуна "
  description: "Зовнішність може бути оманливою."
  components:
  - type: Pda
    id: ClownIDCard
    state: pda-clown
    penSlot:
      startingItem: CrayonOrange # no pink crayon?!?
      # ^ Still unacceptable.
      ejectSound: /Audio/Items/bikehorn.ogg
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#C18199"
  - type: Icon
    state: pda-clown
  - type: Slippery
    paralyzeTime: 4
    launchForwardsMultiplier: 1.5
  - type: StepTrigger
    triggerGroups:
      types:
      - SlipEntity
  - type: CollisionWake
    enabled: false
  - type: Physics
    bodyType: Dynamic
  - type: Fixtures
    fixtures:
      slips:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.3,0.4,0.3"
        hard: false
        layer:
          - SlipLayer
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.3,0.4,0.3"
        density: 5
        mask:
        - ItemMask

- type: entity
  parent: BasePDA
  id: MimePDA
  name: "кПК міма"
  description: "На диво, не у вимкненому стані."
  components:
  - type: Pda
    id: MimeIDCard
    state: pda-mime
    idSlot: #  rewrite without sound because mime
      name: ID Card
      whitelist:
        components:
        - IdCard
  - type: PdaBorderColor
    borderColor: "#d7d7d0"
    accentHColor: "#333333"
  - type: Icon
    state: pda-mime
  - type: Muted # for random sentience event

- type: entity
  name: "кПК капелана"
  parent: SciencePDA # DeltaV - Chaplain is part of episetmics
  id: ChaplainPDA
  description: "Обраний Богом КПК."
  components:
  - type: Sprite # DeltaV - Give Chaplain PDA Epistemics colors
    sprite: DeltaV/Objects/Devices/pda.rsi
    layers:
    - map: [ "enum.PdaVisualLayers.Base" ]
    - state: "light_overlay"
      map: [ "enum.PdaVisualLayers.Flashlight" ]
      shader: "unshaded"
      visible: false
    - state: "id_overlay"
      map: [ "enum.PdaVisualLayers.IdLight" ]
      shader: "unshaded"
      visible: false
  - type: Pda
    id: ChaplainIDCard
    state: pda-chaplain
  - type: PdaBorderColor
    borderColor: "#333333"
    accentVColor: "#8900c9" # DeltaV - Give Chaplain PDA Epistemics colors
  - type: Icon
    sprite: DeltaV/Objects/Devices/pda.rsi # DeltaV - Give Chaplain PDA Epistemics colors
    state: pda-chaplain
  - type: CartridgeLoader # Nyanotrasen - Glimmer Monitor
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - GlimmerMonitorCartridge
      - NanoChatCartridge # DeltaV
      - WalletCartridge # Pirate banking
      - PsiWatchCartridge

- type: entity
  name: "кПК голови карго" # DeltaV - Logistics Department replacing Cargo
  parent: BasePDA
  id: QuartermasterPDA
  description: "КПК для хлопця, який керує логістикою." # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Pda
    id: QuartermasterIDCard
    state: pda-qm
  - type: PdaBorderColor
    borderColor: "#e39751"
    accentVColor: "#a23e3e"
  - type: Icon
    state: pda-qm
  - type: CartridgeLoader # Adds the MailMetrics courier tracker
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - MailMetricsCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge
      - StockTradingCartridge # DeltaV - StockTrading

- type: entity
  name: "спеціальний КПК Голови Карго" # DeltaV - Logistics Department replacing Cargo
  parent: BasePDA
  id: QuartermasterNTPDA
  description: "КПК для хлопця, який керує логістикою. На ньому золотими літерами написано \"спеціальний підрозділ, унікальний для найкращого логіста в секторі\"." # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Pda
    id: QuartermasterIDCard
    state: pda-qmnano
  - type: PdaBorderColor
    borderColor: "#e39751"
    accentVColor: "#a23e3e"
  - type: Icon
    state: pda-qmnano
  - type: CartridgeLoader # Adds the MailMetrics courier tracker
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - WalletCartridge # Pirate banking
      - MailMetricsCartridge # DeltaV - MailMetrics courier tracker
      - StockTradingCartridge # DeltaV - StockTrading
      - NanoChatCartridge # DeltaV

- type: entity
  parent: BasePDA
  id: CargoPDA
  name: "кПК карго" # DeltaV - Logistics Department replacing Cargo
  description: "КПК для хлопців, які замовляють піцу." # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Pda
    id: CargoIDCard
    state: pda-cargo
  - type: PdaBorderColor
    borderColor: "#e39751"
  - type: Icon
    state: pda-cargo
  - type: CartridgeLoader # DeltaV
    preinstalled:
    - CrewManifestCartridge
    - NotekeeperCartridge
    - NewsReaderCartridge
    - WalletCartridge # Pirate banking
    - StockTradingCartridge # DeltaV - StockTrading
    - NanoChatCartridge # DeltaV

- type: entity
  parent: BasePDA
  id: SalvagePDA
  name: "кПК утилізатора"
  description: "Пахне попелом."
  components:
  - type: Pda
    id: SalvageIDCard
    state: pda-miner
  - type: PdaBorderColor
    borderColor: "#af9366"
    accentVColor: "#8900c9"
  - type: Icon
    state: pda-miner
  - type: CartridgeLoader
    uiKey: enum.PdaUiKey.Key
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - WalletCartridge # Pirate banking
      - AstroNavCartridge
      - NanoChatCartridge

- type: entity
  parent: BasePDA
  id: BartenderPDA
  name: "кПК бармена"
  description: "Пахне пивом."
  components:
  - type: Pda
    id: BartenderIDCard
    state: pda-bartender
  - type: PdaBorderColor
    borderColor: "#333333"
  - type: Icon
    state: pda-bartender

- type: entity
  parent: BasePDA
  id: LibrarianPDA
  name: "кПК бібліотекаря"
  description: "Пахне книжками."
  components:
  - type: Pda
    id: LibrarianIDCard
    state: pda-library
    penSlot:
      startingItem: LuxuryPen
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#858585"
  - type: Icon
    state: pda-library
  - type: CartridgeLoader
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - GlimmerMonitorCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge
      - PsiWatchCartridge

- type: entity
  parent: BasePDA
  id: LawyerPDA
  name: "кПК адвоката"
  description: "Юристам для переманювання сумнівних клієнтів."
  components:
  - type: Pda
    id: LawyerIDCard
    state: pda-lawyer
    penSlot:
      startingItem: LuxuryPen
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#6f6192"
  - type: Icon
    state: pda-lawyer
  - type: CartridgeLoader # DeltaV - Crime Assist + SecWatch
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - CrimeAssistCartridge
      - SecWatchCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge

- type: entity
  parent: BasePDA
  id: JanitorPDA
  name: "кПК прибиральника"
  description: "Пахне відбілювачем."
  components:
  - type: Pda
    id: JanitorIDCard
    state: pda-janitor
  - type: PdaBorderColor
    borderColor: "#5D2D56"
  - type: Icon
    state: pda-janitor

- type: entity
  parent: BasePDA
  id: CaptainPDA
  name: "кПК капітана"
  description: "На диво, нічим не відрізняється від вашого КПК."
  components:
  - type: Pda
    id: CaptainIDCard
    state: pda-captain
    penSlot:
      startingItem: PenCap
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#7C5D00"
  - type: Icon
    state: pda-captain
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 8
    staminaCost: 5
    soundHit:
      collection: MetalThud

- type: entity
  parent: BasePDA
  id: CaptainNTPDA
  name: "спеціальний КПК Капітана"
  description: "неймовірно блискучий з рельєфними деталями. на ньому золотом написано \"спеціальна одиниця, унікальна для найкращого капітана в секторі\"."
  components:
  - type: Pda
    id: CaptainIDCard
    state: pda-captainnano
    penSlot:
      startingItem: PenCap
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#7C5D00"
  - type: Icon
    state: pda-captainnano

- type: entity
  parent: BasePDA
  id: HoPPDA
  name: "кПК голови персоналу"
  description: "Виглядає так, ніби його погризли."
  components:
  - type: Pda
    id: HoPIDCard
    state: pda-hop
    penSlot:
      startingItem: PenHop
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#789876"
    accentHColor: "#447987"
  - type: Icon
    state: pda-hop

- type: entity
  parent: BasePDA
  id: HoPNTPDA
  name: "спеціальний КПК Голови Персоналу"
  description: "Виглядає так, ніби його вже пережовували. Тут золотими літерами написано \"спеціальний підрозділ, унікальний для найкращого керівника персоналу в секторі\"."
  components:
  - type: Pda
    id: HoPIDCard
    state: pda-hopnano
    penSlot:
      startingItem: PenHop
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#789876"
    accentHColor: "#447987"
  - type: Icon
    state: pda-hopnano

- type: entity
  parent: BasePDA
  id: CEPDA
  name: "кПК головного інженера"
  description: "Виглядає так, ніби ним майже не користувалися."
  components:
  - type: Pda
    id: CEIDCard
    state: pda-ce
  - type: PdaBorderColor
    borderColor: "#949137"
    accentHColor: "#447987"
  - type: Icon
    state: pda-ce

- type: entity
  parent: BasePDA
  id: CENTPDA
  name: "спеціальний КПК Головного Інженера"
  description: "Виглядає так, ніби ним майже не користувалися. На ньому золотими літерами написано \"спеціальний пристрій, унікальний для найкращого головного інженера в секторі\"."
  components:
  - type: Pda
    id: CEIDCard
    state: pda-cenano
  - type: PdaBorderColor
    borderColor: "#949137"
    accentHColor: "#447987"
  - type: Icon
    state: pda-cenano

- type: entity
  parent: BasePDA
  id: EngineerPDA
  name: "кПК інженера"
  description: "Міцний і зносостійкий."
  components:
  - type: Pda
    id: EngineeringIDCard
    state: pda-engineer
  - type: PdaBorderColor
    borderColor: "#949137"
    accentVColor: "#A32D26"
  - type: Icon
    state: pda-engineer

- type: entity
  parent: BaseMedicalPDA
  id: CMOPDA
  name: "головний лікар КПК"
  description: "Надзвичайно блискучі та стерильні."
  components:
  - type: Pda
    id: CMOIDCard
    state: pda-cmo
    penSlot: # Fancy Pen Light
      startingItem: CMOPenLight
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#d7d7d0"
    accentHColor: "#447987"
    accentVColor: "#447987"
  - type: Icon
    state: pda-cmo

- type: entity
  parent: BaseMedicalPDA
  id: CMONTPDA
  name: "спеціальний КПК Головного Лікаря"
  description: "Надзвичайно блискучий і стерильний. На ньому золотими літерами написано \"спеціальний блок, унікальний для найкращого головного лікаря в секторі\". Має вбудований аналізатор здоров'я."
  components:
  - type: Pda
    id: CMOIDCard
    state: pda-cmonano
    penSlot: # Fancy Pen Light
      startingItem: CMOPenLight
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#d7d7d0"
    accentHColor: "#447987"
    accentVColor: "#447987"
  - type: Icon
    state: pda-cmonano

- type: entity
  parent: BaseMedicalPDA
  id: MedicalPDA
  name: "медичний КПК"
  description: "Блискучі та стерильні."
  components:
  - type: Pda
    id: MedicalIDCard
    state: pda-medical
    penSlot: # Pen Lights
      startingItem: PenLightBase
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#d7d7d0"
    accentVColor: "#447987"
  - type: Icon
    state: pda-medical
  - type: GuideHelp
    guides:
    - Medical Doctor

- type: entity
  parent: BaseMedicalPDA
  id: ParamedicPDA
  name: "парамедик КПК"
  description: "Блискучі та стерильні."
  components:
  - type: Pda
    id: ParamedicIDCard
    state: pda-paramedic
    penSlot: # Pen Lights
      startingItem: PenLightBase
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#d7d7d0"
    accentVColor: "#2a4b5b"
  - type: Icon
    state: pda-paramedic

- type: entity
  parent: BaseMedicalPDA
  id: ChemistryPDA
  name: "кПК хіміка"
  description: "На ньому є кілька знебарвлених плям тут і там."
  components:
  - type: Pda
    id: ChemistIDCard
    state: pda-chemistry
    penSlot: # Pen Lights
      startingItem: PenLightBase
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#d7d7d0"
    accentVColor: "#B34200"
  - type: Icon
    state: pda-chemistry

- type: entity
  parent: SciencePDA
  id: RnDPDA
  name: "кПК містагого" # DeltaV - Epistemics Department replacing Science
  description: "Він здається напрочуд звичайним."
  components:
  - type: Pda
    id: RDIDCard
    state: pda-rd
  - type: PdaBorderColor
    borderColor: "#d7d7d0"
    accentHColor: "#447987"
    accentVColor: "#8900c9"
  - type: Icon
    state: pda-rd
  - type: CartridgeLoader # Nyanotrasen - Glimmer Monitor
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - GlimmerMonitorCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge # DeltaV
      - PsiWatchCartridge

- type: entity
  parent: BasePDA
  id: RnDNTPDA
  name: "спеціальний КПК Наукового Директора" # DeltaV - Epistemics Department replacing Science
  description: "На ньому золотими літерами написано: \"Спеціальна одиниця, унікальна для найкращого містагога в секторі\"."
  components:
  - type: Pda
    id: RDIDCard
    state: pda-rdnano
  - type: PdaBorderColor
    borderColor: "#d7d7d0"
    accentHColor: "#447987"
    accentVColor: "#8900c9"
  - type: Icon
    state: pda-rdnano
  - type: CartridgeLoader # Nyanotrasen - Glimmer Monitor
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - GlimmerMonitorCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge
      - PsiWatchCartridge

- type: entity
  parent: BasePDA
  id: SciencePDA
  name: "епістеміка КПК" # DeltaV - Epistemics Department replacing Science
  description: "Вона вкрита невідомою липкою речовиною."
  components:
  - type: Pda
    id: ResearchIDCard
    state: pda-science
  - type: PdaBorderColor
    borderColor: "#d7d7d0"
    accentVColor: "#8900c9"
  - type: Icon
    state: pda-science
  - type: CartridgeLoader # Nyanotrasen - Glimmer Monitor
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - GlimmerMonitorCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge # DeltaV

- type: entity
  parent: SciencePDA
  name: "пДА для робототехніки"
  id: RoboticsPDA
  description: "Задня панель покрита скотчем"
  components:
  - type: Pda
    id: RoboticsIDCard
    state: pda-robotics
  - type: PdaBorderColor
    borderColor: "#d7d7d0"
    accentVColor: "#952004"
  - type: Icon
    state: pda-robotics

- type: entity
  parent: BasePDA
  id: HoSPDA
  name: "кПК голови служби безпеки"
  description: "Хто б не носив цей КПК, він є законом."
  components:
  - type: Pda
    id: HoSIDCard
    state: pda-hos
  - type: PdaBorderColor
    borderColor: "#A32D26"
    accentHColor: "#447987"
  - type: Icon
    state: pda-hos
  - type: CartridgeLoader # DeltaV - Crime Assist + SecWatch
    preinstalled:
    - CrewManifestCartridge
    - NotekeeperCartridge
    - NewsReaderCartridge
    - WalletCartridge # Pirate banking
    - CrimeAssistCartridge # DeltaV
    - SecWatchCartridge # DeltaV: SecWatch replaces WantedList
    - LogProbeCartridge
    - NanoChatCartridge # DeltaV

- type: entity
  parent: BasePDA
  id: HoSNTPDA
  name: "спеціальний КПК Голови Безпеки"
  description: "Хто б не носив цей КПК, він - закон. На ньому золотими літерами написано \"спеціальний підрозділ, унікальний для найкращого керівника служби безпеки в секторі\"."
  components:
  - type: Pda
    id: HoSIDCard
    state: pda-hosnano
  - type: PdaBorderColor
    borderColor: "#A32D26"
    accentHColor: "#447987"
  - type: Icon
    state: pda-hosnano
  - type: CartridgeLoader # DeltaV - Crime Assist + SecWatch
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - CrimeAssistCartridge
      - SecWatchCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge

- type: entity
  parent: BasePDA
  id: WardenPDA
  name: "кПК  наглядача"
  description: "Схоже, що операційна система була зламана."
  components:
  - type: Pda
    id: WardenIDCard
    state: pda-warden
  - type: PdaBorderColor
    borderColor: "#A32D26"
    accentVColor: "#949137"
  - type: Icon
    state: pda-warden
  - type: CartridgeLoader # DeltaV - Crime Assist + SecWatch
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - CrimeAssistCartridge
      - SecWatchCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge

- type: entity
  parent: BasePDA
  id: SecurityPDA
  name: "кПК  служби безпеки"
  description: "Червоний, щоб приховати плями крові пасажирів."
  components:
  - type: Pda
    id: SecurityIDCard
    state: pda-security
  - type: PdaBorderColor
    borderColor: "#A32D26"
  - type: Icon
    state: pda-security
  - type: CartridgeLoader # DeltaV - Crime Assist + SecWatch
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - CrimeAssistCartridge
      - SecWatchCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge

- type: entity
  parent: BasePDA
  id: CentcomPDA
  name: "КПК ЦентКом"
  description: "Світло-зелений знак ходячої бюрократії."
  components:
  - type: Pda
    id: CentcomIDCard
    state: pda-centcom
    penSlot:
      startingItem: PenCentcom
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#00842e"
  - type: Icon
    state: pda-centcom
  - type: CartridgeLoader
    uiKey: enum.PdaUiKey.Key
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - SecWatchCartridge # DeltaV: SecWatch replaces WantedList
      - LogProbeCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge # DeltaV

- type: entity
  parent: CentcomPDA
  id: AdminPDA
  name: "КПК Адміна"
  description: "Якщо ви не є адміністратором, будь ласка, поверніть цей КПК найближчому адміністратору."
  components:
  - type: Pda
    id: PassengerIDCard
  - type: HealthAnalyzer
    scanDelay: 0
  - type: CartridgeLoader
    uiKey: enum.PdaUiKey.Key
    notificationsEnabled: false
    diskSpace: 10 # DeltaV
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - LogProbeCartridge
      - WalletCartridge # Pirate banking
      - SecWatchCartridge # DeltaV: SecWatch replaces WantedList
      - StockTradingCartridge # Delta-V
      - NanoChatCartridge # DeltaV
      - PsiWatchCartridge

- type: entity
  parent: CentcomPDA
  id: CentcomPDAFake
  suffix: Fake
  components:
  - type: Pda
    id: CentcomIDCardSyndie

- type: entity
  parent: CentcomPDA
  id: DeathsquadPDA
  suffix: Death Squad
  components:
  - type: Pda
    id: CentcomIDCardDeathsquad

- type: entity
  parent: BasePDA
  id: MusicianPDA
  name: "кПК музиканта"
  description: "Це наповнює натхненням."
  components:
  - type: Pda
    id: MusicianIDCard
    state: pda-musician
  - type: PdaBorderColor
    borderColor: "#333333"
  - type: Icon
    state: pda-musician
  - type: Instrument
    allowPercussion: false
    handheld: true
    bank: 1
    program: 2

- type: entity
  parent: BasePDA
  id: AtmosPDA
  name: "кПК атмосферного техніка"
  description: "Все ще пахне плазмою."
  components:
  - type: Pda
    id: AtmosIDCard
    state: pda-atmos
  - type: PdaBorderColor
    borderColor: "#949137"
    accentVColor: "#447987"
  - type: Icon
    state: pda-atmos

- type: entity
  parent: BasePDA
  id: ClearPDA
  name: "кПК прибиральника"
  description: "99 і 44/100 відсотків чистого пластику."
  components:
  - type: Pda
    id: PassengerIDCard
    state: pda-clear
  - type: PdaBorderColor
    borderColor: "#288e4d"
  - type: Icon
    state: pda-clear

- type: entity
  parent: BasePDA
  id: SyndiPDA
  name: "кПК синдикату"
  description: "Гаразд, час бути продуктивним членом - о, круто, я поганий хлопець, час вбивати людей!"
  components:
  - type: Pda
    id: SyndicateIDCard
    state: pda-syndi
  - type: PdaBorderColor
    borderColor: "#891417"
  - type: Icon
    state: pda-syndi
  - type: CartridgeLoader
    uiKey: enum.PdaUiKey.Key
    preinstalled:
      - NotekeeperCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge # DeltaV
    cartridgeSlot:
      priority: -1
      name: Cartridge
      ejectSound: /Audio/Machines/id_swipe.ogg
      insertSound: /Audio/Machines/id_insert.ogg
      whitelist:
        components:
          - Cartridge
  - type: ClothingAddFaction
    faction: Syndicate

- type: entity
  parent: BasePDA
  id: ERTLeaderPDA
  name: "КПК лідера ERT"
  suffix: Leader
  description: "Червоний - вогнева міць."
  components:
  - type: Pda
    id: ERTLeaderIDCard
    state: pda-ert
  - type: PdaBorderColor
    borderColor: "#A32D26"
    accentHColor: "#447987"
    accentVColor: "#447987"
  - type: Icon
    state: pda-ert
  - type: CartridgeLoader
    uiKey: enum.PdaUiKey.Key
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - WalletCartridge # Pirate banking
      - SecWatchCartridge # DeltaV: SecWatch replaces WantedList
      - LogProbeCartridge
      - NanoChatCartridge # DeltaV
      - MedTekCartridge

- type: entity
  parent: ERTLeaderPDA
  id: ERTChaplainPDA
  name: "КПК капелана ЕРТ"
  suffix: Chaplain
  description: "Червоний - вогнева міць, він благословенний."
  components:
  - type: Pda
    id: ERTChaplainIDCard

- type: entity
  parent: ERTLeaderPDA
  id: ERTEngineerPDA
  name: "КПК інженера ERT"
  suffix: Engineer
  description: "Червоний колір означає вогневу міць, він добре поношений."
  components:
  - type: Pda
    id: ERTEngineerIDCard

- type: entity
  parent: ERTLeaderPDA
  id: ERTJanitorPDA
  name: "КПК прибиральника ERT"
  suffix: Janitor
  description: "Червоний колір означає вогневу міць, він блискуче чистий."
  components:
  - type: Pda
    id: ERTJanitorIDCard

- type: entity
  parent: ERTLeaderPDA
  id: ERTMedicPDA
  name: "КПК ERT Medic"
  suffix: Medic
  description: "Червоний для вогневої потужності, він блискучий і стерильний."
  components:
  - type: Pda
    id: ERTMedicIDCard
  - type: CartridgeLoader
    uiKey: enum.PdaUiKey.Key
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - MedTekCartridge
      - WalletCartridge # Pirate banking
      - CrimeAssistCartridge
      - SecWatchCartridge

- type: entity
  parent: ERTLeaderPDA
  id: ERTSecurityPDA
  name: "КПК служби безпеки ERT"
  suffix: Security
  description: "Червоний колір означає вогневу міць, збоку викарбувані мітки підрахунку."
  components:
  - type: Pda
    id: ERTSecurityIDCard

- type: entity
  parent: ERTLeaderPDA
  id: CBURNPDA
  name: "КПК CBURN"
  description: "Пахне гнилою плоттю."
  components:
  - type: Pda
    id: CBURNIDcard
  - type: PdaBorderColor
    borderColor: "#A32D26"
    accentHColor: "#447987"
    accentVColor: "#447987"

- type: entity
  parent: BaseMedicalPDA
  id: PsychologistPDA
  name: "кПК психолога"
  description: "Виглядає бездоганно прибраним."
  components:
  - type: Pda
    id: PsychologistIDCard
    state: pda-medical
    penSlot: # Pen Lights
      startingItem: PenLightBase
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#d7d7d0"
    accentVColor: "#447987"
  - type: Icon
    state: pda-medical

- type: entity
  parent: BasePDA
  id: ReporterPDA
  name: "кПК репортера"
  description: "Пахне свіжою пресою."
  components:
  - type: Pda
    id: ReporterIDCard
    state: pda-reporter
    penSlot:
      startingItem: LuxuryPen
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#3f3f74"
  - type: Icon
    state: pda-reporter
  - type: CartridgeLoader # DeltaV
    preinstalled:
    - CrewManifestCartridge
    - NotekeeperCartridge
    - NewsReaderCartridge
    - WalletCartridge # Pirate banking
    - StockTradingCartridge # DeltaV - StockTrading
    - NanoChatCartridge # DeltaV

- type: entity
  parent: BasePDA
  id: ZookeeperPDA
  name: "кПК зоолога"
  description: "Зроблено з натуральної штучної шкіри. Нічого собі!"
  components:
  - type: Pda
    id: ZookeeperIDCard
    state: pda-zookeeper
  - type: PdaBorderColor
    borderColor: "#ffe685"
  - type: Icon
    state: pda-zookeeper

- type: entity
  parent: BasePDA
  id: BoxerPDA
  name: "кПК босера"
  description: "Пурхайте, як метелик, дзенькайте, як бджілка."
  components:
  - type: Pda
    id: BoxerIDCard
    state: pda-boxer
  - type: PdaBorderColor
    borderColor: "#333333"
    accentVColor: "#390504"
  - type: Icon
    state: pda-boxer

- type: entity
  parent: BasePDA
  id: DetectivePDA
  name: "кПК детектива"
  description: "Пахне дощем... що ллється з дахів..."
  components:
  - type: Pda
    id: DetectiveIDCard
    state: pda-detective
  - type: PdaBorderColor
    borderColor: "#774705"
  - type: Icon
    state: pda-detective
  - type: CartridgeLoader # DeltaV - Crime Assist + SecWatch
    preinstalled:
    - CrewManifestCartridge
    - NotekeeperCartridge
    - NewsReaderCartridge
    - WalletCartridge # Pirate banking
    - CrimeAssistCartridge # DeltaV
    - SecWatchCartridge # DeltaV: SecWatch replaces WantedList
    - LogProbeCartridge
    - NanoChatCartridge # DeltaV

- type: entity
  parent: BaseMedicalPDA
  id: BrigmedicPDA
  name: "кПК бригадного лікаря"
  description: "Цікаво, чий пульс на екрані? Сподіваюся, він не зупиниться..."
  components:
  - type: Pda
    id: BrigmedicIDCard
    state: pda-brigmedic
    penSlot: # Pen Lights
      startingItem: PenLightBase
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#A32D26"
    accentHColor: "#d7d7d0"
    accentVColor: "#d7d7d0"
  - type: Icon
    state: pda-brigmedic
  - type: CartridgeLoader # DeltaV - Crime Assist + SecWatch
    preinstalled:
    - CrewManifestCartridge
    - NotekeeperCartridge
    - NewsReaderCartridge
    - WalletCartridge # Pirate banking
    - CrimeAssistCartridge # DeltaV
    - SecWatchCartridge # DeltaV: SecWatch replaces WantedList
    - NanoChatCartridge # DeltaV

- type: entity
  parent: ClownPDA
  id: CluwnePDA
  name: "кПК клувні"
  suffix: Unremoveable
  description: "Проклятий клятий КПК."
  components:
  - type: Pda
    id: CluwneIDCard
    state: pda-cluwne
    penSlot:
      startingItem: CrayonGreen
      ejectSound: /Audio/Items/bikehorn.ogg
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#1c8f4d"
  - type: Icon
    state: pda-cluwne
  - type: Unremoveable

- type: entity
  parent: BasePDA
  id: SeniorEngineerPDA
  name: "кПК старшого інженера"
  description: "Схоже, його кілька разів розбирали і збирали назад."
  components:
  - type: Pda
    id: SeniorEngineerIDCard
    state: pda-seniorengineer
  - type: PdaBorderColor
    borderColor: "#949137"
    accentVColor: "#CD6900"
  - type: Icon
    state: pda-seniorengineer

- type: entity
  parent: SciencePDA
  id: SeniorResearcherPDA
  name: "кПК старшого науковця"
  description: "Виглядає так, ніби вона пережила роки хімічних опіків і вибухів."
  components:
  - type: Pda
    id: SeniorResearcherIDCard
    state: pda-seniorresearcher
  - type: PdaBorderColor
    borderColor: "#d7d7d0"
    accentHColor: "#8900c9"
    accentVColor: "#8900c9"
  - type: Icon
    state: pda-seniorresearcher

- type: entity
  parent: BaseMedicalPDA
  id: SeniorPhysicianPDA
  name: "старший лікар КПК"
  description: "Слабко пахне залізом і хімікатами."
  components:
  - type: Pda
    id: SeniorPhysicianIDCard
    state: pda-seniorphysician
    penSlot: # Pen Lights
      startingItem: PenLightBase
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#d7d7d0"
    accentHColor: "#447987"
    accentVColor: "#B34200"
  - type: Icon
    state: pda-seniorphysician

- type: entity
  parent: BasePDA
  id: SeniorOfficerPDA
  name: "кПК старшого офіцера"
  description: "Побиті, побиті та зламані, але ледь придатні для використання."
  components:
  - type: Pda
    id: SeniorOfficerIDCard
    state: pda-seniorofficer
  - type: PdaBorderColor
    borderColor: "#A32D26"
    accentVColor: "#DFDFDF"
  - type: Icon
    state: pda-seniorofficer
  - type: CartridgeLoader # DeltaV - Crime Assist + SecWatch
    preinstalled:
      - CrewManifestCartridge
      - NotekeeperCartridge
      - NewsReaderCartridge
      - CrimeAssistCartridge
      - SecWatchCartridge
      - WalletCartridge # Pirate banking
      - NanoChatCartridge

- type: entity
  parent: SyndiPDA
  id: PiratePDA
  name: "кПК пірата"
  description: "Ягх!"
  components:
  - type: Pda
    id: PirateIDCard
    state: pda-pirate
  - type: Icon
    state: pda-pirate

- type: entity
  parent: BaseMedicalPDA
  id: SyndiAgentPDA
  name: "кПК агента синдикату"
  description: "У ті дні, коли зцілення звичайних синдикатів недостатньо, спробуйте замість цього зцілити ядерних оперативників!"
  components:
  - type: Pda
    id: SyndicateIDCard
    state: pda-syndi-agent
    penSlot: # Pen Lights
      startingItem: PenLightBase
      priority: -1
      whitelist:
        tags:
        - Write
  - type: PdaBorderColor
    borderColor: "#891417"
  - type: Icon
    state: pda-syndi-agent
  - type: CartridgeLoader
    uiKey: enum.PdaUiKey.Key
    preinstalled:
      - NotekeeperCartridge
      - WalletCartridge # Pirate banking
      - MedTekCartridge
      - NanoChatCartridge
    cartridgeSlot:
      priority: -1
      name: Cartridge
      ejectSound: /Audio/Machines/id_swipe.ogg
      insertSound: /Audio/Machines/id_insert.ogg
      whitelist:
        components:
          - Cartridge
  - type: ClothingAddFaction
    faction: Syndicate

#PIRATE START UNTIL END OF FILE
- type: entity
  parent: BasePDA
  id: RoboticistPDA
  name: "КПК робототехніка"
  description: "Найкращий друг робота."
  components:
  - type: Pda
    id: RoboticistIDCard
    state: pda-roboticist
  - type: PdaBorderColor
    borderColor: "#333333"
    accentVColor: "#891417"
  - type: Icon
    state: pda-science

