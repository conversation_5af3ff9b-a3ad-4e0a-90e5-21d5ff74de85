# Food

- type: stack
  id: Pancake
  name: "млинець"
  spawn: FoodBakedPancake
  maxCount: 3

# Food Containers

- type: stack
  id: PizzaBox
  name: "коробка для піци"
  icon: { sprite: Objects/Consumable/Food/Baked/pizza.rsi, state: box }
  spawn: FoodBoxPizza
  maxCount: 30

# Smokeables

- type: stack
  id: PaperRolling
  name: "папір для закруток"
  icon: { sprite: /Textures/Objects/Consumable/Smokeables/Cigarettes/paper.rsi, state: cigpaper }
  spawn: PaperRolling
  maxCount: 5

- type: stack
  id: CigaretteFilter
  name: "сигаретний фільтр"
  icon: { sprite: /Textures/Objects/Consumable/Smokeables/Cigarettes/paper.rsi, state: cigfilter }
  spawn: CigaretteFilter
  maxCount: 5

- type: stack
  id: GroundTobacco
  name: "мелений тютюн"
  icon: { sprite: /Textures/Objects/Misc/reagent_fillings.rsi, state: powderpile }
  spawn: GroundTobacco
  maxCount: 5

- type: stack
  id: GroundCannabis
  name: "мелений канабіс"
  icon: { sprite: /Textures/Objects/Misc/reagent_fillings.rsi, state: powderpile }
  spawn: GroundCannabis
  maxCount:

- type: stack
  id: GroundCannabisRainbow
  name: "мелений райдужний канабіс"
  icon: { sprite: /Textures/Objects/Specific/Hydroponics/rainbow_cannabis.rsi, state: powderpile_rainbow }
  spawn: GroundCannabisRainbow
  maxCount:

- type: stack
  id: LeavesTobaccoDried
  name: "сушене листя тютюну"
  icon: { sprite: /Textures/Objects/Specific/Hydroponics/tobacco.rsi, state: dried }
  spawn: LeavesTobaccoDried
  maxCount: 5

- type: stack
  id: LeavesCannabisDried
  name: "сушене листя конопель"
  icon: { sprite: /Textures/Objects/Specific/Hydroponics/tobacco.rsi, state: dried }
  spawn: LeavesCannabisDried
  maxCount: 5

- type: stack
  id: LeavesCannabisRainbowDried
  name: "сушене листя райдужної коноплі"
  icon: { sprite: /Textures/Objects/Specific/Hydroponics/rainbow_cannabis.rsi, state: dried }
  spawn: LeavesCannabisRainbowDried
