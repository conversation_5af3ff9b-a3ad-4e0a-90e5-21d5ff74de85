- type: entity
  parent: BaseStructureComputer
  id: BaseComputer
  name: "консоль"
  placement:
    mode: SnapgridCenter
  components:
  - type: MeleeSound
    soundGroups:
      Brute:
        collection: GlassSmash
  - type: Construction
    graph: Computer
    node: computer
    containers:
      - board
  - type: Computer
  - type: ApcPowerReceiver
    powerLoad: 200
  - type: ExtensionCableReceiver
  - type: ActivatableUIRequiresPower
  - type: Sprite
    sprite: Structures/Machines/computers.rsi
    layers:
    - map: ["computerLayerBody"]
      state: computer
    - map: ["computerLayerKeyboard"]
      state: generic_keyboard
    - map: ["computerLayerScreen"]
      state: generic
    - map: ["computerLayerKeys"]
      state: generic_keys
    - map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      state: generic_panel_open
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ComputerVisuals.Powered:
        computerLayerScreen:
          True: { visible: true, shader: unshaded }
          False: { visible: false }
        computerLayerKeys:
          True: { visible: true, shader: unshaded }
          False: { visible: true, shader: shaded }
      enum.WiresVisuals.MaintenancePanelState:
        enum.WiresVisualLayers.MaintenancePanel:
          True: { visible: false }
          False: { visible: true }
  - type: LitOnPowered
  - type: PointLight
    radius: 1.5
    energy: 1.6
    enabled: false
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
    offset: "0, 0.4" # shine from the top, not bottom of the computer
    castShadows: false
  - type: EmitSoundOnUIOpen
    sound:
      collection: Keyboard
      params:
        volume: -1
        variation: 0.10
        pitch: 1.10  # low pitch keyboard sounds feel kinda weird
    blacklist:
      tags:
      - NoConsoleSound
  - type: ContainerContainer
    containers:
      board: !type:Container
        ents: []
  - type: LightningTarget
    priority: 1
  - type: RequireProjectileTarget
  - type: LanguageSpeaker
    currentLanguage: TauCetiBasic
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    - RobotTalk
    understands:
    - TauCetiBasic
    - RobotTalk
  - type: Electrified
    enabled: false
    usesApcPower: true
  - type: WiresPanel
  - type: WiresVisuals
  - type: Wires
    boardName: wires-board-name-computer
    layoutId: Computer

#
#     This is overwritten by children, so needs to be defined there
#  - type: UserInterface
#    interfaces:
#      enum.WiresUiKey.Key:
#        type: WiresBoundUserInterface

- type: entity
  parent: BaseComputer
  id: BaseComputerAiAccess
  components:
  - type: StationAiWhitelist
  - type: Wires
    boardName: wires-board-name-computer
    layoutId: ComputerAi
