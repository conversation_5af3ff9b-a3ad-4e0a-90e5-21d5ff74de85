- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackDuffel
  name: "спортивна сумка"
  description: "Велика сумка для зберігання додаткових речей."
  components:
  - type: Sprite
    sprite: Clothing/Back/Duffels/duffel.rsi
  - type: Storage
    maxItemSize: Huge
    grid:
    - 0,0,7,4
  - type: ClothingSpeedModifier
    walkModifier: 1
    sprintModifier: 0.9
  - type: HeldSpeedModifier

- type: entity
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelEngineering
  name: "інженерна сумка"
  description: "Велика сумка для зберігання додаткових інструментів та матеріалів."
  components:
    - type: Sprite
      sprite: Clothing/Back/Duffels/engineering.rsi

- type: entity
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelAtmospherics
  name: "атмосферний мішок"
  description: "Велика спортивна сумка з вогнетривких волокон. Пахне плазмою."
  components:
    - type: Sprite
      sprite: Clothing/Back/Duffels/atmospherics.rsi

- type: entity
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelMedical
  name: "медична сумка"
  description: "Велика сумка для зберігання додаткового медичного приладдя."
  components:
    - type: Sprite
      sprite: Clothing/Back/Duffels/medical.rsi

- type: entity
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelCaptain
  name: "спортивна сумка капітана"
  description: "Велика сумка для зберігання додаткових капітанських речей."
  components:
    - type: Sprite
      sprite: Clothing/Back/Duffels/captain.rsi

- type: entity
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelClown
  name: "клоунська сумка"
  description: "Велика спортивна сумка для зберігання додаткових речей."
  components:
    - type: Sprite
      sprite: Clothing/Back/Duffels/clown.rsi
    - type: Storage
      storageOpenSound:
        collection: BikeHorn

- type: entity
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelSecurity
  name: "сумка служби безпеки"
  description: "Велика спортивна сумка для зберігання додаткових речей, пов'язаних з безпекою."
  components:
    - type: Sprite
      sprite: Clothing/Back/Duffels/security.rsi

- type: entity
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelBrigmedic
  name: "сумка бріг-медика" # DeltaV - rename brigmedic to corpsman
  description: "Велика сумка для зберігання додаткових медичних речей."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Back/Duffels/brigmedic.rsi # DeltaV - resprite

- type: entity
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelChemistry
  name: "сумка для хімії"
  description: "Велика сумка для зберігання додаткових мензурок і пробірок."
  components:
    - type: Sprite
      sprite: Clothing/Back/Duffels/chemistry.rsi

- type: entity
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelVirology
  name: "вірусологічна сумка"
  description: "Велика спортивна сумка з гіпоалергенних волокон. Вона створена, щоб допомогти запобігти поширенню хвороб. Пахне мавпочкою."
  components:
    - type: Sprite
      sprite: Clothing/Back/Duffels/virology.rsi

- type: entity
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelGenetics
  name: "генетична сумка"
  description: "Велика спортивна сумка для зберігання додаткових генетичних мутацій."
  components:
    - type: Sprite
      sprite: Clothing/Back/Duffels/genetics.rsi

- type: entity
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelMime
  name: "мімічна сумка"
  description: "Велика сумка для зберігання... мімічних... речей."
  components:
    - type: Sprite
      sprite: Clothing/Back/Duffels/mime.rsi
      storageOpenSound:
        collection: null
      storageInsertSound:
        collection: null

- type: entity
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelScience
  name: "сумка для епістемології" # DeltaV - Epistemics Department replacing Science
  description: "Велика сумка для зберігання додаткових речей, пов'язаних з наукою."
  components:
    - type: Sprite
      sprite: Clothing/Back/Duffels/science.rsi

- type: entity
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelRobotics
  name: "рюкзак для робототехніки"
  description: "Велика сумка для робототехнічного обладнання."
  components:
  - type: Sprite
    sprite: Clothing/Back/Duffels/robotics.rsi

- type: entity
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelHydroponics
  name: "сумка для гідропоніки"
  description: "Велика сумка для зберігання додаткових садових інструментів."
  components:
    - type: Sprite
      sprite: Clothing/Back/Duffels/hydroponics.rsi

- type: entity
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelCargo
  name: "логістична сумка" # DeltaV - Logistics Department replacing Cargo
  description: "Великий речовий мішок для крадіжки дорогоцінної логістичної здобичі." # DeltaV - Logistics Department replacing Cargo
  components:
    - type: Sprite
      sprite: Clothing/Back/Duffels/cargo.rsi

- type: entity
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelSalvage
  name: "речовий мішок утилізатора"
  description: "Велика сумка для зберігання екзотичних скарбів."
  components:
    - type: Sprite
      sprite: Clothing/Back/Duffels/salvage.rsi

- type: entity
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelSyndicate
  name: "синдикатська сумка"
  description: "Великий речовий мішок для зберігання різного зрадницького добра."
  components:
  - type: Sprite
    sprite: Clothing/Back/Duffels/syndicate.rsi
  - type: ExplosionResistance
    damageCoefficient: 0.1
  - type: Storage
    grid:
    - 0,0,8,4

- type: entity
  parent: ClothingBackpackDuffelSyndicate
  id: ClothingBackpackDuffelSyndicateBundle
  abstract: true
  components:
    - type: Tag
      tags: [] # ignore "WhitelistChameleon" tag

- type: entity
  parent: ClothingBackpackDuffelSyndicate
  id: ClothingBackpackDuffelSyndicateAmmo
  name: "синдикатська сумка"
  components:
    - type: Sprite
      sprite: Clothing/Back/Duffels/syndicate.rsi
      state: icon-ammo
    - type: Item
      heldPrefix: ammo
    - type: Clothing
      equippedPrefix: ammo

- type: entity
  parent: ClothingBackpackDuffelSyndicateAmmo
  id: ClothingBackpackDuffelSyndicateAmmoBundle
  abstract: true
  components:
    - type: Tag
      tags: [] # ignore "WhitelistChameleon" tag

- type: entity
  parent: ClothingBackpackDuffelSyndicate
  id: ClothingBackpackDuffelSyndicateMedical
  name: "синдикатська сумка"
  components:
    - type: Sprite
      sprite: Clothing/Back/Duffels/syndicate.rsi
      state: icon-med
    - type: Item
      heldPrefix: med
    - type: Clothing
      equippedPrefix: med

- type: entity
  parent: ClothingBackpackDuffelSyndicateMedical
  id: ClothingBackpackDuffelSyndicateMedicalBundle
  abstract: true
  components:
    - type: Tag
      tags: [] # ignore "WhitelistChameleon" tag

- type: entity
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelHolding
  name: "спортивна сумка зберігання"
  description: "Речова сумка, яка відкривається в локальну кишеню блюспейс простору."
  components:
  - type: Sprite
    sprite: Clothing/Back/Duffels/holding.rsi
    state: icon
    layers:
    - state: icon
    - state: icon-unlit
      shader: unshaded
  - type: Item
    size: Ginormous
  - type: Storage
    maxItemSize: Huge
    grid:
    - 0,0,19,9
  - type: ClothingSpeedModifier
    sprintModifier: 1 # makes its stats identical to other variants of bag of holding
  - type: HeldSpeedModifier

- type: entity
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelCBURN
  name: "Сумка-рюкзак CBURN"
  description: "Сумка з різноманітним обладнанням для біологічного захисту."
  components:
  - type: Storage
    maxItemSize: Huge
  - type: ClothingSpeedModifier
    walkModifier: 1
    sprintModifier: 1
  - type: HeldSpeedModifier
