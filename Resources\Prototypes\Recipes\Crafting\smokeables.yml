- type: construction
  name: "самокрут"
  id: smokeableJoint
  graph: smokeableJoint
  startNode: start
  targetNode: joint
  category: construction-category-misc
  description: "Рулон висушеної рослинної сировини, загорнутий у тонкий папір."
  icon: { sprite: Objects/Consumable/Smokeables/Cannabis/joint.rsi, state: unlit-icon }
  objectType: Item
  
- type: construction
  name: "райдужний самокрут"
  id: smokeableJointRainbow
  graph: smokeableJointRainbow
  startNode: start
  targetNode: jointRainbow
  category: construction-category-misc
  description: "Рулон висушеної рослинної сировини, загорнутий у тонкий папір."
  icon: { sprite: Objects/Consumable/Smokeables/Cannabis/joint.rsi, state: unlit-icon }
  objectType: Item

- type: construction
  name: "косяк"
  id: smokeableBlunt
  graph: smokeableBlunt
  startNode: start
  targetNode: blunt
  category: construction-category-misc
  description: "Рулон сушеної рослинної сировини, загорнутий у висушений тютюновий лист."
  icon: { sprite: Objects/Consumable/Smokeables/Cannabis/blunt.rsi, state: unlit-icon }
  objectType: Item

- type: construction
  name: "райдужний косячок"
  id: smokeableBluntRainbow
  graph: smokeableBluntRainbow
  startNode: start
  targetNode: bluntRainbow
  category: construction-category-misc
  description: "Рулон сушеної рослинної сировини, загорнутий у висушений тютюновий лист."
  icon: { sprite: Objects/Consumable/Smokeables/Cannabis/blunt.rsi, state: unlit-icon }
  objectType: Item

- type: construction
  name: "цигарка"
  id: smokeableCigarette
  graph: smokeableCigarette
  startNode: start
  targetNode: cigarette
  category: construction-category-misc
  description: "Рулон тютюну та нікотину."
  icon: { sprite: Objects/Consumable/Smokeables/Cigarettes/cigarette.rsi, state: unlit-icon }
  objectType: Item

# I wanted to put a hand-grinder here but we need construction graphs that use non-consumed catalysts first.

- type: construction
  name: "мелений канабіс"
  id: smokeableGroundCannabis
  graph: smokeableGroundCannabis
  startNode: start
  targetNode: ground
  category: construction-category-misc
  description: "Мелений канабіс, готовий взяти вас у подорож."
  icon: { sprite: Objects/Misc/reagent_fillings.rsi, state: powderpile }
#    color: darkgreen
  objectType: Item

- type: construction
  name: "мелений райдужний канабіс"
  id: smokeableGroundCannabisRainbow
  graph: smokeableGroundCannabisRainbow
  startNode: start
  targetNode: groundRainbow
  category: construction-category-misc
  description: "Мелений райдужний канабіс, готовий до подорожі."
  icon: { sprite: Objects/Specific/Hydroponics/rainbow_cannabis.rsi, state: powderpile_rainbow }
  objectType: Item

- type: construction
  name: "мелений тютюн"
  id: smokeableGroundTobacco
  graph: smokeableGroundTobacco
  startNode: start
  targetNode: ground
  category: construction-category-misc
  description: "Мелений тютюн, ідеально підходить для ручного скручування сигарет."
  icon: { sprite: Objects/Misc/reagent_fillings.rsi, state: powderpile }
#    color: brown
  objectType: Item
