# Empty mags
- type: entity
  id: BaseMagazineRifle
  name: "магазин (гвинтівка калібру .20)"
  parent: BaseItem
  abstract: true
  components:
  - type: Tag
    tags:
      - MagazineRifle
  - type: Item
    size: Small
  - type: BallisticAmmoProvider
    mayTransfer: true
    whitelist:
      tags:
        - CartridgeRifle
    capacity: 25
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Magazine/Rifle/rifle_mag.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 2
    zeroVisible: false
  - type: Appearance

# Magazines
- type: entity
  id: MagazineRifle
  name: "магазин (гвинтівка калібру .20)"
  parent: BaseMagazineRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeRifle
  - type: Sprite
    layers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineRifleEmpty
  name: "магазин (.20 гвинтівковий будь-який)"
  suffix: empty
  parent: MagazineRifle
  components:
  - type: BallisticAmmoProvider
    proto: null
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]

- type: entity
  id: MagazineRifleIncendiary
  name: "магазин (.20 гвинтівковий запальний)"
  parent: MagazineRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeRifleIncendiary

- type: entity
  id: MagazineRiflePractice
  name: "магазин (практична стрільба з гвинтівки калібру .20)"
  parent: BaseMagazineRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeRiflePractice
  - type: Sprite
    layers:
    - state: practice
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineRifleRubber
  name: "магазин (.20 гвинтівковий гумовий)"
  parent: BaseMagazineRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeRifleRubber
  - type: Sprite
    layers:
    - state: rubber
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineRifleUranium
  name: "магазин (.20 гвинтівочний уран)"
  parent: BaseMagazineRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeRifleUranium
  - type: Sprite
    layers:
    - state: uranium
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineRifleShrapnel
  name: "магазин (.20 гвинтівковий, шрапнельний)"
  parent: BaseMagazineRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeRifleShrapnel
  - type: Sprite
    layers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
