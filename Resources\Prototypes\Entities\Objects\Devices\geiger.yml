- type: entity
  parent: BaseItem
  id: GeigerCounter
  name: "Л<PERSON><PERSON><PERSON>льник Гейгера"
  description: "Портативний пристрій, що використовується для виявлення та вимірювання радіаційних імпульсів."
  components:
    - type: Sprite
      sprite: Objects/Tools/geiger.rsi
      layers:
        - state: geiger_base
        - state: geiger_on_idle
          map: ["enum.GeigerLayers.Screen"]
          shader: unshaded
          visible: false
    - type: Item
      sprite: Objects/Tools/geiger.rsi
    - type: Geiger
      showControl: true
      showExamine: true
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.GeigerVisuals.IsEnabled:
          GeigerLayers.Screen:
            True: { visible: True }
            False: { visible: False }
        enum.GeigerVisuals.DangerLevel:
          GeigerLayers.Screen:
            None: {state: geiger_on_idle}
            Low: {state: geiger_on_low}
            Med: {state: geiger_on_med}
            High: {state: geiger_on_high}
            Extreme: {state: geiger_on_ext}
    - type: PhysicalComposition
      materialComposition:
        Plastic: 100
    - type: Tag # goob edit
      tags:
        - GeigerCounter

