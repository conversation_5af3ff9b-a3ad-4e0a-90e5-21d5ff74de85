# Base

- type: entity
  parent: FoodInjectableBase
  id: FoodDonkpocketBase
  abstract: true
  components:
  - type: Food
  - type: Sprite
    sprite: Objects/Consumable/Food/Baked/donkpocket.rsi
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
  - type: Item
    size: Tiny
  - type: Tag
    tags:
    - DonkPocket
    - Meat

# Donkpocket

# Warm gives +5 nutriment.
# Warm ones contain a small amount of Omnizine.

- type: entity
  name: "донк-покет"
  parent: FoodDonkpocketBase
  id: FoodDonkpocket
  description: "Їжа на вибір для досвідченого зрадника."
  components:
  - type: FlavorProfile
    flavors:
      - bread
      - meaty
      - cheap
  - type: Sprite
    state: plain

- type: entity
  name: "нагрітий донк-покет"
  parent: FoodDonkpocket
  id: FoodDonkpocketWarm
  description: "Підігріта їжа для досвідченого зрадника."
  components:
  - type: FlavorProfile
    flavors:
      - bread
      - meaty
      - cheap
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 8
        - ReagentId: Omnizine
          Quantity: 2

- type: entity
  name: "сирий донк-покет"
  parent: FoodDonkpocketBase
  id: FoodDonkpocketDank
  description: "Їжа на вибір для досвідченого ботаніка."
  components:
  - type: FlavorProfile
    flavors:
      - leafy
      - cheap
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
  - type: Sprite
    state: dank

- type: entity
  name: "нагрітий сирий донк-покет"
  parent: FoodDonkpocketDank
  id: FoodDonkpocketDankWarm
  description: "Підігріта їжа на вибір досвідченого ботаніка."
  components:
  - type: FlavorProfile
    flavors:
      - leafy
      - cheap
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Omnizine
          Quantity: 4

- type: entity
  name: "гострий донк-покет"
  parent: FoodDonkpocketBase
  id: FoodDonkpocketSpicy
  description: "Класична закуска, тепер із пряним ароматом, що активується під час нагрівання."
  components:
  - type: FlavorProfile
    flavors:
      - spicy
      - meaty
      - cheap
  - type: Sprite
    state: plain

- type: entity
  name: "нагрітий гострий донк-покет"
  parent: FoodDonkpocketSpicy
  id: FoodDonkpocketSpicyWarm
  description: "Класична їжа для перекусу, тепер, можливо, трохи занадто гостра."
  components:
  - type: FlavorProfile
    flavors:
      - spicy
      - meaty
      - cheap
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Omnizine
          Quantity: 2
  - type: Sprite
    state: spicy

- type: entity
  name: "теріякі-покет"
  parent: FoodDonkpocketBase
  id: FoodDonkpocketTeriyaki
  description: "Східно-азійський варіант класичної закуски на вокзалі."
  components:
  - type: FlavorProfile
    flavors:
      - meaty
      - sweet
      - cheap
  - type: Sprite
    state: teriyaki

- type: entity
  name: "нагрітий теріякі-покет"
  parent: FoodDonkpocketTeriyaki
  id: FoodDonkpocketTeriyakiWarm
  description: "Східно-азійський варіант класичного вокзального перекусу, тепер ще й з парою та теплом."
  components:
  - type: FlavorProfile
    flavors:
      - meaty
      - sweet
      - cheap
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Omnizine
          Quantity: 2

- type: entity
  name: "піцца-покет"
  parent: FoodDonkpocketBase
  id: FoodDonkpocketPizza
  description: "Смачний, сирний і напрочуд ситний."
  components:
  - type: FlavorProfile
    flavors:
      - bread
      - cheesy
      - tomato
      - cheap
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
  - type: Sprite
    state: pizza

- type: entity
  name: "нагріта піцца-покет"
  parent: FoodDonkpocketPizza
  id: FoodDonkpocketPizzaWarm
  description: "Сирна начинка дуже смакує в теплому вигляді."
  components:
  - type: FlavorProfile
    flavors:
      - bread
      - cheesy
      - tomato
      - cheap
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 15
        - ReagentId: Omnizine
          Quantity: 2

- type: entity
  name: "гудковий-покет"
  parent: FoodDonkpocketBase
  id: FoodDonkpocketHonk
  description: "Удостоєна нагород кишеня для осла, яка завоювала серця клоунів і людей."
  components:
  - type: FlavorProfile
    flavors:
      - funny
      - cheap
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Omnizine
          Quantity: 2
  - type: Sprite
    state: banana

- type: entity
  name: "нагрітий гудковий-покет"
  parent: FoodDonkpocketHonk
  id: FoodDonkpocketHonkWarm
  description: "Відзначена нагородами кишеня для донкіхотів, тепер тепла і підсмажена."
  components:
  - type: FlavorProfile
    flavors:
      - funny
      - cheap
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Omnizine
          Quantity: 2

- type: entity
  name: "ягідний-покет"
  parent: FoodDonkpocketBase
  id: FoodDonkpocketBerry
  description: "Невтомно солодкий ослик-кишеня. Зроблено зі 100% штучних ароматизаторів."
  components:
  - type: FlavorProfile
    flavors:
      - sweet
      - cheap
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
  - type: Sprite
    state: berry

- type: entity
  name: "нагрітий ягідний-покет"
  parent: FoodDonkpocketBerry
  id: FoodDonkpocketBerryWarm
  description: "Незмінно солодкий ослик-кишеня, тепер теплий і смачний."
  components:
  - type: FlavorProfile
    flavors:
      - sweet
      - cheap
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Omnizine
          Quantity: 2
        - ReagentId: Sugar
          Quantity: 2

- type: entity
  name: "стонк-покет"
  parent: FoodDonkpocketBase
  id: FoodDonkpocketStonk
  description: "Смачно, але сумне нагадування про великий крах 24-го року"
  components:
  - type: FlavorProfile
    flavors:
      - profits
      - cheap
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
  - type: Sprite
    state: stonk

- type: entity
  name: "нагрітий стонк-покет"
  parent: FoodDonkpocketStonk
  id: FoodDonkpocketStonkWarm
  components:
  - type: FlavorProfile
    flavors:
      - profits
      - cheap
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Omnizine
          Quantity: 2

- type: entity
  name: "короп-покет"
  parent: FoodDonkpocketBase
  id: FoodDonkpocketCarp
  description: "Давно втрачене видання донк-покетів, зроблене спеціально для працьовитих рятувальників."
  components:
  - type: FlavorProfile
    flavors:
    - cheap
    - fishops
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
  - type: Sprite
    state: carp

- type: entity
  name: "нагрітий короп-покет"
  parent: FoodDonkpocketCarp
  id: FoodDonkpocketCarpWarm
  components:
  - type: FlavorProfile
    flavors:
    - cheap
    - fishops
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Omnizine
          Quantity: 2

- type: entity
  name: "ящірний донк-покет"
  parent: FoodDonkpocketBase
  id: FoodDonkpocketDink
  description: "Небрендована кишеня з ящірки, наповнена маринованою морквою та загорнута у водорості. Найкраще їсти холодною, а ще краще - не їсти взагалі."
  components:
  - type: FlavorProfile
    flavors:
      - cold
      - cheap
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nutriment
          Quantity: 2
        - ReagentId: JuiceCarrot
          Quantity: 1
  - type: Sprite
    state: dink
