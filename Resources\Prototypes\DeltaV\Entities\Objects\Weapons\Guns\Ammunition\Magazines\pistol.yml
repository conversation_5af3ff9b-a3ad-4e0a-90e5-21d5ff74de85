- type: entity
  id: BaseMagazinePistolSpecial
  name: "пістолетний магазин (.38 special)"
  parent: BaseItem
  abstract: true
  components:
  - type: Tag
    tags:
      - MagazinePistolSpecial
  - type: BallisticAmmoProvider
    mayTransfer: true
    whitelist:
      tags:
        - CartridgeSpecial
    capacity: 5
  - type: Item
    size: Small
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Ammunition/Magazine/Pistol/pistol_special_mag.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 6
    zeroVisible: false
  - type: Appearance

- type: entity
  id: MagazinePistolSpecial
  name: "пістолетний магазин (.38 special)"
  parent: BaseMagazinePistolSpecial
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeSpecial
  - type: Sprite
    layers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazinePistolSpecialPractice
  name: "магазин до пістолета (.38 special practice)"
  parent: BaseMagazinePistolSpecial
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeSpecialPractice
  - type: Sprite
    layers:
    - state: practice
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazinePistolSpecialRubber
  name: "пістолетний магазин (.38 спеціальний гумовий)"
  parent: BaseMagazinePistolSpecial
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeSpecialRubber
  - type: Sprite
    layers:
    - state: rubber
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazinePistolSpecialMindbreaker
  name: "пістолетний магазин (.38 special mindbreaker)"
  parent: BaseMagazinePistolSpecial
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeSpecialMindbreaker
  - type: Sprite
    layers:
    - state: mindbreaker
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
