﻿- type: constructionGraph
  id: Computer
  start: start
  graph:
    - node: start
      edges:
        - to: frameUnsecured
          completed:
            - !type:SetAnchor
              value: false
          steps:
            - material: Steel
              amount: 5
              doAfter: 2.5

    - node: frameUnsecured
      actions:
      - !type:AppearanceChange
      entity: ComputerFrame
      edges:
        - to: boardUnsecured
          conditions:
            - !type:EntityAnchored {}
          steps:
            - component: ComputerBoard
              store: board
              name: any computer circuit board
              icon:
                sprite: "Objects/Misc/module.rsi"
                state: "id_mod"

        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: SheetSteel1
              amount: 5
            - !type:DeleteEntity {}
          steps:
            - tool: Screwing
              doAfter: 2

    - node: boardUnsecured
      actions:
      - !type:AppearanceChange
      edges:
        - to: missingWires
          conditions:
            - !type:EntityAnchored {}
          steps:
            - tool: Screwing

        - to: frameUnsecured
          conditions:
            - !type:EntityAnchored { }
          completed:
            - !type:EmptyAllContainers {}
          steps:
            - tool: Prying

    - node: missingWires
      actions:
      - !type:AppearanceChange
      edges:
        - to: monitorMissing
          conditions:
            - !type:EntityAnchored {}
          steps:
            - material: Cable
              amount: 5

        - to: boardUnsecured
          conditions:
            - !type:EntityAnchored { }
          steps:
            - tool: Screwing

    - node: monitorMissing
      entity: ComputerFrame
      actions:
        - !type:SetAnchor { }
        - !type:AppearanceChange
      edges:
        - to: monitorUnsecured
          conditions:
            - !type:EntityAnchored {}
          steps:
            - material: Glass
              amount: 2

        - to: missingWires
          conditions:
            - !type:EntityAnchored { }
          completed:
            - !type:SpawnPrototype
              prototype: CableApcStack1
              amount: 5
          steps:
            - tool: Cutting

    - node: monitorUnsecured
      actions:
      - !type:AppearanceChange
      entity: ComputerFrame
      edges:
        - to: computer
          conditions:
            - !type:EntityAnchored {}
          steps:
            - tool: Screwing

        - to: monitorMissing
          conditions:
            - !type:EntityAnchored {}
          completed:
            - !type:SpawnPrototype
              prototype: SheetGlass1
              amount: 2
          steps:
            - tool: Prying
              doAfter: 1

    - node: computer
      entity: !type:BoardNodeEntity { container: board }
      edges:
        - to: monitorUnsecured
          steps:
            - tool: Prying
              doAfter: 1

    - node: monitorBroken
      entity: ComputerBroken
      edges:
        - to: monitorMissing
          conditions:
            - !type:EntityAnchored {}
          completed:
            - !type:SpawnPrototype
              prototype: ShardGlass
              amount: 2
          steps:
            - tool: Prying
              doAfter: 2
