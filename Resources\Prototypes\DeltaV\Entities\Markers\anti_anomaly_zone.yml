- type: entity
  name: "антианомальна зона"
  description: "Аномалії не зможуть з'явитися в радіусі 10 блоків від цієї точки."
  id: AntiAnomalyZone
  suffix: "range 10"
  parent: MarkerBase
  components:
  - type: Sprite
    sprite: Structures/Specific/Anomalies/ice_anom.rsi
    layers:
      - state: anom
      - sprite: Markers/cross.rsi
        state: pink
  - type: AntiAnomalyZone
    zoneRadius: 10

- type: entity
  parent: AntiAnomalyZone
  id: AntiAnomalyZone20
  suffix: "range 20"
  description: "Аномалії не зможуть з'явитися в радіусі 20 блоків від цієї точки."
  components:
  - type: AntiAnomalyZone
    zoneRadius: 20

- type: entity
  parent: AntiAnomalyZone
  id: AntiAnomalyZone50
  suffix: "range 50"
  description: "Аномалії не зможуть з'явитися в радіусі 50 блоків від цієї точки."
  components:
  - type: AntiAnomalyZone
    zoneRadius: 50
