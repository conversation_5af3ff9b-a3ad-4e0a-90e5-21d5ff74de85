- type: entity
  name: "BaseWeaponLauncher"
  parent: BaseItem
  id: BaseWeaponLauncher
  description: "Корінцевий зубастий і пагонистий."
  abstract: true
  components:
  - type: Sprite
  - type: Clothing
    sprite: Objects/Weapons/Guns/Launchers/china_lake.rsi
    quickEquip: false
    slots:
    - Back
  - type: Item
    size: Huge
  - type: StaticPrice
    price: 500
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
        ents: []
  - type: MeleeWeapon
    attackRate: 1.5
    damage:
      types:
        Blunt: 14
    bluntStaminaDamageFactor: 1.5
    swapKeys: true
    disableHeavy: true
    animation: WeaponArcThrust
    wideAnimationRotation: 180
    soundHit:
      collection: MetalThud
  - type: DamageOtherOnHit
    staminaCost: 14
  - type: Execution
  # PIRATE START
  - type: CanHoldAccessories
    whiteListTags:
      - CombatKnife
  - type: CanTakeAim
  # PIRATE END

- type: entity
  name: "гранатомет \"Китайське озеро"
  parent: [BaseWeaponLauncher, BaseGunWieldable]
  id: WeaponLauncherChinaLake
  description: "ПЛОП"
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Launchers/china_lake.rsi
    layers:
      - state: icon
        map: ["enum.GunVisualLayers.Base"]
  - type: Clothing
    sprite: Objects/Weapons/Guns/Launchers/china_lake.rsi
    slots:
    - Back
    - suitStorage
  - type: AmmoCounter
  - type: Gun
    fireRate: 1
    selectedMode: SemiAuto
    availableModes:
      - SemiAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/grenade_launcher.ogg
  - type: BallisticAmmoProvider
    whitelist:
      tags:
        - Grenade
    capacity: 3
    autoCycle: false
    proto: GrenadeFrag
    soundInsert:
      path: /Audio/Weapons/Guns/MagIn/batrifle_magin.ogg

- type: entity
  name: "РПГ-7"
  parent: BaseWeaponLauncher
  id: WeaponLauncherRocket
  description: "Модифікований старовинний реактивний гранатомет."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Launchers/rocket.rsi
    layers:
      - state: base
        map: ["enum.GunVisualLayers.Base"]
      - state: mag-0
        map: ["enum.GunVisualLayers.Mag"]
  - type: Clothing
    sprite: Objects/Weapons/Guns/Launchers/rocket.rsi
  - type: Gun
    fireRate: 0.5
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/rpgfire.ogg
  - type: BallisticAmmoProvider
    whitelist:
      tags:
        - CartridgeRocket
    proto: CartridgeRocket
    capacity: 1
    soundInsert:
      path: /Audio/Weapons/Guns/MagIn/batrifle_magin.ogg
  - type: MagazineVisuals
    magState: mag
    steps: 2
    zeroVisible: false
  - type: Appearance

- type: entity
  name: "реактивна система залпового вогню"
  parent: BaseWeaponLauncher
  id: WeaponLauncherMultipleRocket
  description: "Модифікований старовинний реактивний гранатомет."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Launchers/rocket.rsi
    layers:
      - state: base
        map: ["enum.GunVisualLayers.Base"]
      - state: mag-0
        map: ["enum.GunVisualLayers.Mag"]
  - type: Clothing
    sprite: Objects/Weapons/Guns/Launchers/rocket.rsi
  - type: Gun
    fireRate: 6
    selectedMode: FullAuto
    availableModes:
      - FullAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/rpgfire.ogg
  - type: BallisticAmmoProvider
    whitelist:
      tags:
        - CartridgeRocket
    proto: CartridgeRocketSlow
    soundInsert:
      path: /Audio/Weapons/Guns/MagIn/batrifle_magin.ogg
  - type: MagazineVisuals
    magState: mag
    steps: 1
    zeroVisible: true
  - type: Appearance

- type: entity
  name: "піратська гармата"
  parent: BaseWeaponLauncher
  id: WeaponLauncherPirateCannon
  description: "Бабах!"
  components:
    - type: Sprite
      sprite: Objects/Weapons/Guns/Launchers/pirate_cannon.rsi
      layers:
        - state: icon
          map: ["enum.GunVisualLayers.Base"]
    - containers:
        balistic-ammo: !type:Container
          ents: []
        ballistic-ammo: !type:Container
          ents: []
      type: ContainerContainer
    - type: Clothing
      sprite: Objects/Weapons/Guns/Launchers/pirate_cannon.rsi
    - type: Gun
      fireRate: 1
      soundGunshot:
        path: /Audio/Weapons/Guns/Gunshots/mateba.ogg
    - type: BallisticAmmoProvider
      whitelist:
        tags:
          - CannonBall
      capacity: 1
      proto: CannonBall
      soundInsert:
        path: /Audio/Weapons/Guns/Gunshots/grenade_launcher.ogg

# - type: entity
#   name: tether gun
#   parent:
#   - BaseItem
#   - PowerCellSlotMediumItem
#   id: WeaponTetherGun
#   description: Manipulates gravity around objects to fling them at high velocities.
#   components:
#     - type: Item
#       storedRotation: -90
#     - type: TetherGun
#       frequency: 5
#       dampingRatio: 4
#     - type: ItemToggle
#       onUse: false
#     - type: PowerCellDraw
#     - type: ToggleCellDraw
#     - type: Sprite
#       sprite: Objects/Weapons/Guns/Launchers/tether_gun.rsi
#       layers:
#         - state: base
#         - state: base-unshaded
#           map: [ "unshaded" ]
#           shader: unshaded
#           visible: false
#     - type: ToggleableLightVisuals
#       spriteLayer: unshaded
#       inhandVisuals:
#         left:
#           - state: inhand-left-unshaded
#             shader: unshaded
#         right:
#           - state: inhand-right-unshaded
#             shader: unshaded
#     - type: Appearance
#     - type: GenericVisualizer
#       visuals:
#         enum.TetherVisualsStatus.Key:
#           unshaded:
#             True: { visible: true }
#             False: { visible: false }

# - type: entity
#   name: force gun
#   parent:
#     - BaseItem
#     - PowerCellSlotMediumItem
#   id: WeaponForceGun
#   description: Manipulates gravity around objects to fling them at high velocities.
#   components:
#     - type: Item
#       storedRotation: -90
#     - type: ForceGun
#       frequency: 15
#       dampingRatio: 4
#       massLimit: 50
#       lineColor: "#18a2d5"
#       soundLaunch:
#         path: /Audio/Weapons/soup.ogg
#         params:
#           volume: 2
#     - type: ItemToggle
#       onUse: false
#     - type: PowerCellDraw
#     - type: ToggleCellDraw
#     - type: Sprite
#       sprite: Objects/Weapons/Guns/Launchers/force_gun.rsi
#       layers:
#         - state: base
#         - state: base-unshaded
#           map: [ "unshaded" ]
#           shader: unshaded
#           visible: false
#         - type: ToggleableLightVisuals
#           spriteLayer: unshaded
#           inhandVisuals:
#             left:
#               - state: inhand-left-unshaded
#                 shader: unshaded
#             right:
#               - state: inhand-right-unshaded
#                 shader: unshaded
#         - type: Appearance
#         - type: GenericVisualizer
#           visuals:
#             enum.TetherVisualsStatus.Key:
#               unshaded:
#                 True: { visible: true }
#                 False: { visible: false }

# - type: entity
#   name: grappling gun
#   parent: BaseItem
#   id: WeaponGrapplingGun
#   components:
#     - type: AmmoCounter
#     - type: GrapplingGun
#     - type: Gun
#       soundGunshot: /Audio/Weapons/Guns/Gunshots/harpoon.ogg
#       fireRate: 0.5
#     - type: BasicEntityAmmoProvider
#       proto: GrapplingHook
#       capacity: 1
#     - type: Sprite
#       sprite: Objects/Weapons/Guns/Launchers/grappling_gun.rsi
#       layers:
#         - state: base
#         - state: base-unshaded
#           map: [ "unshaded" ]
#           shader: unshaded
#           visible: true
#     - type: UseDelay
#       delay: 1.5
#     - type: Appearance
#     - type: GenericVisualizer
#       visuals:
#         enum.TetherVisualsStatus.Key:
#           unshaded:
#             True: { state: base-unshaded }
#             False: { state: base-unshaded-off }

# # Admeme
# - type: entity
#   name: tether gun
#   parent: BaseItem
#   id: WeaponTetherGunAdmin
#   suffix: Admeme
#   description: Manipulates gravity around objects to fling them at high velocities.
#   components:
#     - type: TetherGun
#       canTetherAlive: true
#       canUnanchor: true
#       maxForce: 10000
#       massLimit: 10000
#       dampingRatio: 4
#       frequency: 20
#     - type: Sprite
#       sprite: Objects/Weapons/Guns/Launchers/tether_gun.rsi
#       layers:
#         - state: base
#         - state: base-unshaded
#           map: [ "unshaded" ]
#           shader: unshaded
#           visible: false
#     - type: ToggleableLightVisuals
#       spriteLayer: unshaded
#       inhandVisuals:
#         left:
#           - state: inhand-left-unshaded
#             shader: unshaded
#         right:
#           - state: inhand-right-unshaded
#             shader: unshaded
#     - type: Appearance
#     - type: GenericVisualizer
#       visuals:
#         enum.TetherVisualsStatus.Key:
#           unshaded:
#             True: { visible: true }
#             False: { visible: false }

# - type: entity
#   name: force gun
#   parent: BaseItem
#   id: WeaponForceGunAdmin
#   suffix: Admeme
#   description: Manipulates gravity around objects to fling them at high velocities.
#   components:
#     - type: ForceGun
#       canTetherAlive: true
#       canUnanchor: true
#       maxForce: 10000
#       massLimit: 10000
#       frequency: 15
#       dampingRatio: 4
#       throwForce: 50
#       throwDistance: 100
#       lineColor: "#18a2d5"
#     - type: Sprite
#       sprite: Objects/Weapons/Guns/Launchers/force_gun.rsi
#       layers:
#         - state: base
#         - state: base-unshaded
#           map: [ "unshaded" ]
#           shader: unshaded
#           visible: false
#     - type: ToggleableLightVisuals
#       spriteLayer: unshaded
#       inhandVisuals:
#         left:
#           - state: inhand-left-unshaded
#             shader: unshaded
#         right:
#           - state: inhand-right-unshaded
#             shader: unshaded
#     - type: Appearance
#     - type: GenericVisualizer
#       visuals:
#         enum.TetherVisualsStatus.Key:
#           unshaded:
#             True: { visible: true }
#            False: { visible: false }

- type: entity
  name: "метеоритна рушниця"
  parent: WeaponLauncherMultipleRocket
  id: WeaponLauncherAdmemeMeteorLarge
  suffix: Admeme
  description: "Він вистрілює великими метеоритами"
  components:
  - type: BallisticAmmoProvider
    whitelist:
      tags:
        - CartridgeRocket
    proto: MeteorLarge

- type: entity
  name: "нерухома пускова установка для стрижнів"
  parent: WeaponLauncherAdmemeMeteorLarge
  id: WeaponLauncherAdmemeImmovableRodSlow
  suffix: Admeme
  description: "Він стріляє повільними нерухомими стрижнями."
  components:
  - type: BallisticAmmoProvider
    whitelist:
      tags:
        - CartridgeRocket
    proto: ImmovableRodSlow

# Frontier mail RPDS
- type: entity
  name: "пошта RPDS"
  parent: WeaponLauncherChinaLake
  id: WeaponMailLake
  description: "Швидка Система Доставки Посилок"
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Launchers/mail.rsi
    layers:
    - state: icon
      map: ["enum.GunVisualLayers.Base"]
  - type: Clothing
    sprite: Objects/Weapons/Guns/Launchers/mail.rsi
    quickEquip: false
    slots:
    - Back
    - Belt
    - suitStorage
  - type: BallisticAmmoProvider
    proto: null
    whitelist:
      tags:
      - MailCapsule
    capacity: 4
