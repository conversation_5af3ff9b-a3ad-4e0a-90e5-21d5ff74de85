<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <OutputPath>..\bin\Content.MapRenderer\</OutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <Nullable>enable</Nullable>
    <WarningsAsErrors>nullable</WarningsAsErrors>
    <ServerGarbageCollection>true</ServerGarbageCollection>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Content.IntegrationTests\Content.IntegrationTests.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.UnitTesting\Robust.UnitTesting.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="NUnit" />
    <PackageReference Include="SixLabors.ImageSharp" />
  </ItemGroup>

  <Import Project="..\RobustToolbox\MSBuild\Robust.Properties.targets" />
</Project>
