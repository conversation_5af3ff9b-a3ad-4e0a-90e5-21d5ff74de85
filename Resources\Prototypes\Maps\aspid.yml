- type: gameMap
  id: Aspid
  mapName: Аспід
  mapPath: /Maps/aspid.yml
  minPlayers: 10
  maxPlayers: 80
  stations:
    Aspid:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: '{0} NCS Aspid {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: '14'
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/emergency_courser.yml
        - type: StationJobs
          overflowJobs:
            - Passenger
          availableJobs:
            #service
            Captain: [ 1, 1 ]
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 1, 2 ]
            Botanist: [ 2, 3 ]
            Chef: [ 1, 3 ]
            Janitor: [ 2, 3 ]
            Chaplain: [ 1, 1 ]
            Li<PERSON>rian: [ 1, 1 ]
            Lawyer: [ 2, 2 ]
            ServiceWorker: [ 1, 1 ]
            #engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 2, 2 ]
            StationEngineer: [ 3, 4 ]
            TechnicalAssistant: [ 4, 4 ]
            #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            Paramedic: [ 1, 1 ]
            Chemist: [ 1, 2 ]
            MedicalDoctor: [ 4, 4 ]
            MedicalIntern: [ 4, 4 ]
            #science
            ResearchDirector: [ 1, 1 ]
            Scientist: [ 3, 4 ]
            ResearchAssistant: [ 4, 4 ]
            Borg: [ 2, 2 ]
            Roboticist: [ 2, 2 ]
            #security
            HeadOfSecurity: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1 ]
            Warden: [ 1, 1 ]
            Detective: [ 1, 1 ]
            SecurityOfficer: [ 4, 5 ]
            SecurityCadet: [ 4, 4 ]
            #supply
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ 3, 4 ]
            CargoTechnician: [ 2, 3 ]
            #civillian
            Passenger: [ -1, -1 ]
            Clown: [ 1, 1 ]
            Mime: [ 1, 1 ]
            Musician: [ 1, 1 ]
            #other
            MailCarrier: [ 1, 1 ]
            Gladiator: [ 1, 1 ]
            MartialArtist: [ 1, 1 ]
            PrisonGuard: [ 1, 1 ]
            Brigmedic:  [ 1, 1 ]

