﻿# Biscuits for the slips
- type: entity
  parent: BaseItem
  id: PaperBiscuit
  name: "бісквітна картка"
  description: "Листівка з печивом. На звороті великими літерами надруковано \"НЕ ТРАВИТИ\"."
  components:
    - type: Sprite
      sprite: DeltaV/Objects/Misc/biscuits.rsi
      layers:
        - state: biscuit
        - state: biscuit_paper
          map: [ "biscuit_paper" ]
          visible: false
        - state: biscuit_top
          map: [ "enum.BiscuitVisualLayers.Top" ]
    - type: Item
      size: Small
    - type: Tag
      tags:
        - Document
    - type: Appearance
    - type: Damageable
      damageModifierSet: Wood
    - type: Biscuit
    - type: BiscuitVisuals
    - type: ItemSlots
      slots:
        PaperSlip:
          name: Slip
          whitelist:
            tags:
              - PaperSlip
          locked: true
    - type: ContainerContainer
      containers:
        PaperSlip: !type:ContainerSlot {}
    - type: ItemMapper
      sprite: DeltaV/Objects/Misc/biscuits.rsi
      mapLayers:
        biscuit_paper:
          whitelist:
            components:
              - Paper
      spriteLayers:
        - biscuit_paper

- type: entity
  parent: PaperBiscuit
  id: PaperBiscuitFilled
  suffix: Filled
  components:
    - type: ItemSlots
      slots:
        PaperSlip:
          startingItem: PaperSlip
          name: Slip
          whitelist:
            tags:
              - PaperSlip
          locked: true

- type: entity
  parent: PaperBiscuit
  id: PaperBiscuitCorporate
  name: "конфіденційна бісквітна картка"
  description: "Конфіденційна бісквітна картка. Вишуканий синій колір і логотип NT на лицьовій стороні робить її схожою на шоколадку."
  components:
    - type: Sprite
      sprite: DeltaV/Objects/Misc/biscuits.rsi
      layers:
        - state: biscuit_secret
          map: [ "enum.BiscuitVisualLayers.Base" ]
        - state: biscuit_paper_corp
          map: [ "biscuit_paper" ]
          visible: false
        - state: biscuit_secret_top
          map: [ "enum.BiscuitVisualLayers.Top" ]
          visible: true

- type: entity
  parent: PaperBiscuitCorporate
  id: PaperBiscuitCorporateFilled
  suffix: Filled
  components:
    - type: ItemSlots
      slots:
        PaperSlip:
          startingItem: PaperSlipCorporate
          name: Slip
          whitelist:
            tags:
              - PaperSlip
          locked: true

- type: entity
  parent: PaperBiscuitCorporate
  id: PaperBiscuitCorporateSpareID
  name: "бісквіт аварійного доступу"
  components:
    - type: ItemSlots
      slots:
        PaperSlip:
          startingItem: PaperSlipSpareID
          name: Slip
          whitelist:
            tags:
              - PaperSlip
          locked: true

# Paper slips
- type: entity
  parent: BaseItem
  id: PaperSlip
  name: "паперовий шматок"
  description: "Маленький клаптик паперу, що залишився після того, як відрізали більший шматок. Ого."
  components:
    - type: Sprite
      sprite: DeltaV/Objects/Misc/biscuits.rsi
      layers:
        - state: slip
        - state: slip_words
          map: [ "enum.PaperVisualLayers.Writing" ]
          visible: false
    - type: Paper
    - type: ActivatableUI
      key: enum.PaperUiKey.Key
      requireActiveHand: false
    - type: UserInterface
      interfaces:
        enum.PaperUiKey.Key:
          type: PaperBoundUserInterface
    - type: Item
      size: Tiny
    - type: Tag
      tags:
        - Document
        - PaperSlip
    - type: Appearance
    - type: PaperVisuals
    - type: Damageable
      damageModifierSet: Wood
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 15
          behaviors:
            - !type:SpawnEntitiesBehavior
              spawn:
                Ash:
                  min: 1
                  max: 1
            - !type:DoActsBehavior
              acts: [ "Destruction" ]

- type: entity
  parent: PaperSlip
  id: PaperSlipCorporate
  name: "корпоративна пластикова картка"
  description: "Пластикова картка для конфіденційних корпоративних справ."
  components:
    - type: Paper
      canEdit: false
    - type: Sprite
      layers:
        - state: corpslip
        - state: corpslip_words
          map: [ "enum.PaperVisualLayers.Writing" ]
          visible: false
    - type: PaperVisuals
      backgroundImagePath: "/Textures/DeltaV/Interface/Paper/paper_background_corpcard.svg.96dpi.png"
      contentMargin: 70.0, 16.0, 70.0, 16.0
      maxWritableArea: 400.0, 600.0


- type: entity
  parent: PaperSlipCorporate
  id: PaperSlipSpareID
  suffix: SpareID
  name: "картка аварійного доступу"
  description: "Пластикова картка для конфіденційних корпоративних справ. Має магнітну смугу на звороті."
  components:
    - type: Access
      tags:
        - DV-SpareSafe
