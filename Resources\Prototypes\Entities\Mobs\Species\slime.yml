- type: entity
  name: "Уріст МакСлайм"
  parent: BaseMobSpeciesOrganic
  id: BaseMobSlimePerson
  abstract: true
  components:
  - type: Hunger
  - type: Thirst
  - type: Carriable # Carrying system from nyanotrasen.
  - type: Icon
    sprite: Mobs/Species/Slime/parts.rsi
    state: full
  - type: Body
    prototype: Slime
    requiredLegs: 2
  # they like eat it idk lol
  - type: Storage
    clickInsert: false
    grid:
    - 0,0,1,2
    maxItemSize: Large
    storageInsertSound:
      path: /Audio/Voice/Slime/slime_squish.ogg
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
        ents: []
  - type: UserInterface
    interfaces:
      enum.StorageUiKey.Key:
        type: StorageBoundUserInterface
      enum.StoreUiKey.Key:
        type: StoreBoundUserInterface
      enum.HumanoidMarkingModifierKey.Key:
        type: HumanoidMarkingModifierBoundUserInterface
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
      # Goobstation - changelings
      enum.HereticLivingHeartKey.Key: # goob edit - heretics
        type: LivingHeartMenuBoundUserInterface
      # Shitmed
      enum.SurgeryUIKey.Key:
        type: SurgeryBui
  # to prevent bag open/honk spam
  - type: UseDelay
    delay: 0.5
  - type: HumanoidAppearance
    species: SlimePerson
  - type: Speech
    speechVerb: Slime
    speechSounds: Slime
    allowedEmotes: ['Squish']
  - type: TypingIndicator
    proto: slime
  - type: Vocal
    sounds:
      Male: MaleSlime
      Female: FemaleSlime
      Unsexed: MaleSlime
  - type: Damageable
    damageContainer: Biological
    damageModifierSet: Slime
  - type: Geras
  - type: PassiveDamage # Around 8 damage a minute healed
    allowedStates:
    - Alive
    damageCap: 65
    damage:
      types:
        Heat: -0.14
      groups:
        Brute: -0.14
  - type: DamageVisuals
    damageOverlayGroups:
      Brute:
        sprite: Mobs/Effects/brute_damage.rsi
        color: "#2cf274"
  - type: Bloodstream
    bloodReagent: Slime # TODO Color slime blood based on their slime color or smth
    bloodRegenerationHunger: 2
    bloodRegenerationThirst: 2 # 1 of slime satiates 2 hunger
  - type: Barotrauma
    damage:
      types:
        Blunt: 0.60 #per second, scales with pressure and other constants. Slighty more than humans.
  - type: Reactive
    groups:
      Flammable: [ Touch ]
      Extinguish: [ Touch ]
    reactions:
    - reagents: [ Water, SpaceCleaner ]
      methods: [ Touch ]
      effects:
      - !type:WashCreamPieReaction
    - reagents: [ Water ]
      methods: [ Touch ]
      effects:
      - !type:HealthChange
        scaleByQuantity: true
        damage:
          types:
            Heat: 2
      - !type:PopupMessage
        type: Local
        visualType: Large
        messages: [ "slime-hurt-by-water-popup" ]
        probability: 0.25
  - type: Butcherable
    butcheringType: Spike
    spawned:
    - id: FoodMeatSlime
      amount: 5
  - type: Respirator
    damage:
      types:
        Asphyxiation: 0.2
    damageRecovery:
      types:
        Asphyxiation: -1.0
    maxSaturation: 15
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    - Bubblish
    understands:
    - TauCetiBasic
    - Bubblish
  - type: FootPrints
    leftBarePrint: "footprint-left-bare-slime"
    rightBarePrint: "footprint-right-bare-slime"

- type: entity
  parent: MobHumanDummy
  id: MobSlimePersonDummy
  categories: [ HideSpawnMenu ]
  components:
    - type: HumanoidAppearance
      species: SlimePerson
