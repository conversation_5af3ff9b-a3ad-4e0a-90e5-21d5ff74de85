
### Interaction Messages

# When trying to eat food without the required utensil... but you gotta hold it
food-you-need-to-hold-utensil = You need to be holding a {$utensil} to eat that!

food-nom = Nom. {$flavors}
food-swallow = You swallow the {$food}. {$flavors}

food-has-used-storage = You cannot eat the {$food} with an item stored inside.

food-system-remove-mask = You need to take off the {$entity} first.

## System

food-system-you-cannot-eat-any-more = You can't eat any more!
food-system-you-cannot-eat-any-more-other = They can't eat any more!
food-system-try-use-food-is-empty = {CAPITALIZE(THE($entity))} is empty!
food-system-wrong-utensil = You can't eat {THE($food)} with {INDEFINITE($utensil)} {$utensil}.
food-system-cant-digest = You can't digest {THE($entity)}!
food-system-cant-digest-other = They can't digest {THE($entity)}!

food-system-verb-eat = Eat

## Force feeding

food-system-force-feed = {CAPITALIZE(THE($user))} is trying to feed you something!
food-system-force-feed-success = {CAPITALIZE(THE($user))} forced you to eat something! {$flavors}
food-system-force-feed-success-user = You successfully feed {THE($target)}
