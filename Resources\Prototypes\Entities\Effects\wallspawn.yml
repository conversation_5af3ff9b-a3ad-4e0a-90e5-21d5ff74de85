﻿- type: entity
  id: WallSpawnAsteroid
  components:
  - type: Transform
    anchored: True
  - type: InteractionOutline
  - type: Physics
    bodyType: Static
  - type: Sprite
    sprite: /Textures/Effects/rockspawn.rsi
    state: asteroid
  - type: Fixtures
    fixtures:
      portalFixture:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.48,0.25,0.48"
        mask:
        - FullTileMask
        layer:
        - Wall<PERSON>ayer
        hard: false
  - type: TimedDespawn
    lifetime: 2.4
  - type: SpawnOnDespawn
    prototype: AsteroidRock

- type: entity
  id: WallSpawnAsteroidUraniumCrab
  parent: WallSpawnAsteroid
  components:
  - type: SpawnOnDespawn
    prototype: AsteroidRockUraniumCrab

- type: entity
  id: WallSpawnAsteroidUranium
  parent: WallSpawnAsteroid
  components:
  - type: SpawnOnDespawn
    prototype: AsteroidRockUranium

- type: entity
  id: WallSpawnAsteroidQuartzCrab
  parent: WallSpawnAsteroid
  components:
  - type: SpawnOnDespawn
    prototype: AsteroidRockQuartzCrab

- type: entity
  id: WallSpawnAsteroidQuartz
  parent: WallSpawnAsteroid
  components:
  - type: SpawnOnDespawn
    prototype: AsteroidRockQuartz

- type: entity
  id: WallSpawnAsteroidSilverCrab
  parent: WallSpawnAsteroid
  components:
  - type: SpawnOnDespawn
    prototype: AsteroidRockSilverCrab

- type: entity
  id: WallSpawnAsteroidSilver
  parent: WallSpawnAsteroid
  components:
  - type: SpawnOnDespawn
    prototype: AsteroidRockSilver

- type: entity
  id: WallSpawnAsteroidIronCrab
  parent: WallSpawnAsteroid
  components:
  - type: SpawnOnDespawn
    prototype: AsteroidRockTinCrab

- type: entity
  id: WallSpawnAsteroidIron
  parent: WallSpawnAsteroid
  components:
  - type: SpawnOnDespawn
    prototype: AsteroidRockTin