﻿- type: decal
  id: OriginStationSign
  abstract: true

- type: decal
  id: OriginStationSign1
  parent: OriginStationSign
  tags: ["station"]
  sprite:
    sprite: Decals/originsign.rsi
    state: origin1

- type: decal
  id: OriginStationSign2
  parent: OriginStationSign
  tags: ["station"]
  sprite:
    sprite: Decals/originsign.rsi
    state: origin2

- type: decal
  id: OriginStationSign3
  parent: OriginStationSign
  tags: ["station"]
  sprite:
    sprite: Decals/originsign.rsi
    state: origin3

- type: decal
  id: OriginStationSign4
  parent: OriginStationSign
  tags: ["station"]
  sprite:
    sprite: Decals/originsign.rsi
    state: origin4

- type: decal
  id: OriginStationSign5
  parent: OriginStationSign
  tags: ["station"]
  sprite:
    sprite: Decals/originsign.rsi
    state: origin5

- type: decal
  id: OriginStationSign6
  parent: OriginStationSign
  tags: ["station"]
  sprite:
    sprite: Decals/originsign.rsi
    state: origin6

- type: decal
  id: OriginStationSign7
  parent: OriginStationSign
  tags: ["station"]
  sprite:
    sprite: Decals/originsign.rsi
    state: origin7


- type: decal
  id: OriginStationSign8
  parent: OriginStationSign
  tags: ["station"]
  sprite:
    sprite: Decals/originsign.rsi
    state: origin8

- type: decal
  id: OriginStationSign9
  parent: OriginStationSign
  tags: ["station"]
  sprite:
    sprite: Decals/originsign.rsi
    state: origin9

- type: decal
  id: OriginStationSign10
  parent: OriginStationSign
  tags: ["station"]
  sprite:
    sprite: Decals/originsign.rsi
    state: origin10

- type: decal
  id: OriginStationSign11
  parent: OriginStationSign
  tags: ["station"]
  sprite:
    sprite: Decals/originsign.rsi
    state: origin11

- type: decal
  id: OriginStationSign12
  parent: OriginStationSign
  tags: ["station"]
  sprite:
    sprite: Decals/originsign.rsi
    state: origin12

- type: decal
  id: OriginStationSign13
  parent: OriginStationSign
  tags: ["station"]
  sprite:
    sprite: Decals/originsign.rsi
    state: origin13

