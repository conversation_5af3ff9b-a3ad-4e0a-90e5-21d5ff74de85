# Translator that doesn't need power to work
- type: entity
  categories: [ HideSpawnMenu ]
  id: TranslatorUnpowered
  parent: BaseItem
  name: "перекладач"
  description: "Перекладає мову."
  components:
  - type: Sprite
    sprite: Objects/Devices/translator.rsi
    state: icon
    layers:
      - state: icon
      - state: translator
        shader: unshaded
        visible: false
        map: [ "enum.ToggleVisuals.Layer", "enum.PowerDeviceVisualLayers.Powered" ]
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ToggleVisuals.Toggled:
        enum.ToggleVisuals.Layer:
          True: { visible: true }
          False: { visible: false }
  - type: HandheldTranslator
    enabled: false
  - type: Clothing # To allow equipping translators on the neck slot
    slots: [neck]
    equipDelay: 0.3
    unequipDelay: 0.3
    quickEquip: false # Would conflict

# Base translator that uses a power cell. Starts with an empty slot.
- type: entity
  categories: [ HideSpawnMenu ]
  id: TranslatorPoweredBase
  parent: [ TranslatorUnpowered, PowerCellSlotMediumItem ]
  components:
  - type: ItemToggle
  - type: PowerCellDraw
    drawRate: 1
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default

# Normal translator with medium power cell in it
- type: entity
  categories: [ HideSpawnMenu ]
  id: Translator
  parent: [ PowerCellSlotMediumItem, TranslatorPoweredBase ]
  suffix: Powered

# Normal translator with a high power cell and special appearance
- type: entity
  categories: [ HideSpawnMenu ]
  id: TranslatorForeigner
  parent: [ PowerCellSlotHighItem, TranslatorPoweredBase ]
  name: "перекладач іноземців"
  description: "Спеціальний перекладач, який допомагає іноземцям говорити і розуміти основну мову цієї станції."


- type: entity
  id: CanilunztTranslator
  parent: [ TranslatorPoweredBase ]
  name: "Перекладач Canilunzt"
  description: "Перекладає мову між Canilunzt і Galactic Common, дозволяючи вашим місцевим юзерам спілкуватися з місцевими і навпаки!"
  components:
  - type: HandheldTranslator
    spoken:
    - TauCetiBasic
    - Canilunzt
    understood:
    - TauCetiBasic
    - Canilunzt
    requires:
    - TauCetiBasic
    - Canilunzt

- type: entity
  id: BubblishTranslator
  parent: [ TranslatorPoweredBase ]
  name: "Перекладач Bubblish"
  description: "Перекладає мову з баблішської на галактичну, допомагаючи спілкуватися зі слимаками та людьми-слимаками."
  components:
  - type: HandheldTranslator
    spoken:
    - TauCetiBasic
    - Bubblish
    understood:
    - TauCetiBasic
    - Bubblish
    requires:
    - TauCetiBasic
    - Bubblish

- type: entity
  id: NekomimeticTranslator
  parent: [ TranslatorPoweredBase ]
  name: "Неміметичний перекладач"
  description: "Перекладає мову з Некоміметичної на Галактичну загальну, дозволяючи вам спілкуватися з вашими домашніми котами."
  components:
  - type: HandheldTranslator
    spoken:
    - TauCetiBasic
    - Nekomimetic
    understood:
    - TauCetiBasic
    - Nekomimetic
    requires:
    - TauCetiBasic
    - Nekomimetic

- type: entity
  id: DraconicTranslator
  parent: [ TranslatorPoweredBase ]
  name: "Перекладач синта-унаті"
  description: "Перекладає мову з синта-унаті на тау-сеті базову, полегшуючи розуміння вашої місцевої унаті."
  components:
  - type: HandheldTranslator
    spoken:
    - TauCetiBasic
    - Draconic
    understood:
    - TauCetiBasic
    - Draconic
    requires:
    - TauCetiBasic
    - Draconic

- type: entity
  id: SolCommonTranslator
  parent: [ TranslatorPoweredBase ]
  name: "Сол Спільний перекладач"
  description: "Перекладає мову з сол-спільної на галактичну. Як справжній землянин!"
  components:
  - type: HandheldTranslator
    spoken:
    - TauCetiBasic
    - SolCommon
    understood:
    - TauCetiBasic
    - SolCommon
    requires:
    - TauCetiBasic
    - SolCommon

- type: entity
  id: NovuNedericTranslator
  parent: [ TranslatorPoweredBase ]
  name: "перекладач нову-недерік"
  description: "Перекладає мову між нову-недерік і галактичною спільною. Як справжній землянин!"
  components:
  - type: HandheldTranslator
    spoken:
    - TauCetiBasic
    - NovuNederic
    understood:
    - TauCetiBasic
    - NovuNederic
    requires:
    - TauCetiBasic
    - NovuNederic

- type: entity
  id: RootSpeakTranslator
  parent: [ TranslatorPoweredBase ]
  name: "Перекладач RootSpeak"
  description: "Перекладає мову між RootSpeak та Galactic Common. Тепер ви можете говорити від імені дерев."
  components:
  - type: HandheldTranslator
    spoken:
    - TauCetiBasic
    - RootSpeak
    understood:
    - TauCetiBasic
    - RootSpeak
    requires:
    - TauCetiBasic
    - RootSpeak

- type: entity
  id: MofficTranslator
  parent: [ TranslatorPoweredBase ]
  name: "Moffic перекладач"
  description: "Перекладає мову між Moffic та Galactic Common, допомагаючи вам зрозуміти дзижчання вашого домашнього таргана!"
  components:
  - type: HandheldTranslator
    spoken:
    - TauCetiBasic
    - Moffic
    understood:
    - TauCetiBasic
    - Moffic
    requires:
    - TauCetiBasic
    - Moffic

- type: entity
  id: XenoTranslator
  parent: [ TranslatorPoweredBase ]
  name: "Ксеноперекладач"
  description: "Перекладає мову між Ксено та галактичною мовою. Однак це, мабуть, не допоможе вам вижити під час сутички."
  components:
  - type: HandheldTranslator
    spoken:
    - TauCetiBasic
    - Xeno
    understood:
    - TauCetiBasic
    - Xeno
    requires:
    - TauCetiBasic

- type: entity
  id: AnimalTranslator
  parent: [ TranslatorPoweredBase ]
  name: "Перекладач тварин"
  description: "Перекладає всі милі звуки, які видає більшість тварин, у більш зрозумілу форму!"
  components:
  - type: HandheldTranslator
    understood:
    - Cat
    - Dog
    - Fox
    - Monkey
    - Mouse
    - Chicken
    - Duck
    - Cow
    - Sheep
    - Kangaroo
    - Pig
    - Crab
    - Kobold
    - Hissing
    requires:
    - TauCetiBasic
    setLanguageOnInteract: false

- type: entity
  id: ValyrianStandardTranslator
  parent: [ TranslatorPoweredBase ]
  name: "Перекладач валірійського стандарту"
  description: "Перекладає мову з валірійської стандартної на тау-цеті базову. За розмову з гарпіями!"
  components:
  - type: HandheldTranslator
    spoken:
    - TauCetiBasic
    - ValyrianStandard
    understood:
    - TauCetiBasic
    - ValyrianStandard
    requires:
    - TauCetiBasic
    - ValyrianStandard

- type: entity
  id: AzazibaTranslator
  parent: [ TranslatorPoweredBase ]
  name: "перекладач Азазіба"
  description: "Перекладає мову між унаті та азазіба. Щоб унати говорили архаїчною формою своєї рідної мови!" # Intended for Admins Only, this item is for lore reasons not obtainable.
  components:
  - type: HandheldTranslator
    spoken:
    - Draconic
    - Azaziba
    understood:
    - Draconic
    - Azaziba
    requires:
    - Draconic
    - Azaziba

- type: entity
  id: ChittinTranslator
  parent: [ TranslatorPoweredBase ]
  name: "Перекладач читтин"
  description: "Перекладає мову між Хіттіном і Тау-Сеті Базовою. Для спілкування з хітинідами!"
  components:
  - type: HandheldTranslator
    spoken:
    - TauCetiBasic
    - Chittin
    understood:
    - TauCetiBasic
    - Chittin
    requires:
    - TauCetiBasic
    - Chittin

- type: entity
  id: SiikMaasTranslator
  parent: [ TranslatorPoweredBase ]
  name: "Перекладач Сіік'маас"
  description: "Перекладає мовлення між сіік'маас та тау-цеті базовою. За розмову з Таджарою!"
  components:
  - type: HandheldTranslator
    spoken:
    - TauCetiBasic
    - SiikMaas
    understood:
    - TauCetiBasic
    - SiikMaas
    requires:
    - TauCetiBasic
    - SiikMaas
