#Where all of the uniforms for the yet-to-be-implimented ship vs. ship gamemode go (or Nanotrasen VS. Syndicate, if you'd like.)
#In its own file to further avoid bloating jumpsuits.yml.

#CREW
#Recruit
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitRecruitNT
  name: "комбінезон новобранця"
  description: "Стильний комбінезон сірого кольору з синьою обробкою. Ідеальний для гідної помічниці."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/recruit_nt.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/recruit_nt.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitRecruitSyndie
  name: "комбінезон синдикату"
  description: "Сумнівний темно-сірий комбінезон. Ніби пасажири не були достатньо сумнівними."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/recruit_syndie.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/recruit_syndie.rsi

#Repairman
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitRepairmanNT
  name: "комбінезон ремонтника"
  description: "Комбінезон, який нагадує вам про певну робочу позицію в екіпажі. Сподіваємося, вам не доведеться виконувати ту саму роботу, що й цим виродкам."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/repairman_nt.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/repairman_nt.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitRepairmanSyndie
  name: "комбінезон ремонтника синдикату"
  description: "Функціональний, модний і крутий. Інженери Nanotrasen хотіли б виглядати так само добре."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/repairman_syndie.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/repairman_syndie.rsi

#Paramedic
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitParamedicNT
  name: "комбінезон парамедика"
  description: "Базовий біло-синій комбінезон для парамедиків \"Нанотрейзен\", які перебувають у бойових секторах."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/paramedic_nt.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/paramedic_nt.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitParamedicSyndie
  name: "комбінезон парамедика синдикату"
  description: "Чомусь, одягаючи його, ви відчуваєте, що дуже близькі до того, щоб порушити Женевську конвенцію."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/paramedic_syndie.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/paramedic_syndie.rsi

#HEADS OF STAFF
#Chief Engineer
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitChiefEngineerNT
  name: "комбінезон головного інженера"
  description: "Часто жартують, що роль головного інженера бойового сектору - це місце, куди просувають справжніх, логістично мислячих інженерів. Щасти вам."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/ce_nt.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/ce_nt.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitChiefEngineerSyndie
  name: "комбінезон головного інженера синдикату"
  description: "Зловісний комбінезон зі світловідбиваючим жилетом і червоною майкою." #TODO: Write a better description for this once Ship vs. Ship is real and actual player habits begin forming
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/ce_syndie.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/ce_syndie.rsi
