#Normal windoors
- type: entity
  id: BaseWindoor
  parent: BaseStructure
  abstract: true
  placement:
    mode: SnapgridCenter
  components:
  - type: MeleeSound
    soundGroups:
      Brute:
        collection: GlassSmash
  - type: InteractionOutline
  - type: StationAiWhitelist
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.49,-0.49,0.49,-0.36"
        density: 1500
        mask:
        - TabletopMachineMask
        layer:
        - GlassAirlockLayer
  - type: Sprite
    sprite: Structures/Doors/Windoors/windoor.rsi
    layers:
    - state: closed
      map: ["enum.DoorVisualLayers.Base"]
    - state: closed_unlit
      shader: unshaded
      map: ["enum.DoorVisualLayers.BaseUnlit"]
    - state: welded
      map: ["enum.WeldableLayers.BaseWelded"]
    - state: bolted_unlit
      shader: unshaded
      map: ["enum.DoorVisualLayers.BaseBolted"]
    - state: emergency_unlit
      shader: unshaded
      map: ["enum.DoorVisualLayers.BaseEmergencyAccess"]
    - state: panel_open
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AnimationPlayer
  - type: Tag
    tags:
      - WeldbotFixableStructure
  - type: ApcPowerReceiver
  - type: ExtensionCableReceiver
  - type: DoorSignalControl
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: BasicDevice
  - type: WirelessNetworkConnection
    range: 200
  - type: DeviceLinkSink
    ports:
      - Open
      - Close
      - Toggle
      - AutoClose
      - DoorBolt
  - type: DeviceLinkSource
    ports:
    - DoorStatus
    lastSignals:
      DoorStatus: false
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Glass
  - type: ExaminableDamage
    messages: WindowMessages
  - type: RCDDeconstructable
    cost: 8
    delay: 8
    fx: EffectRCDDeconstruct8
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 400
      behaviors: #excess damage (nuke?). avoid computational cost of spawning entities.
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlass:
            min: 1
            max: 2
          SheetSteel1:
            min: 2
            max: 4
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: ContainerFill
    containers:
      board: [ DoorElectronics ]
  - type: AccessReader
    containerAccessProvider: board
  - type: ContainerContainer
    containers:
      board: !type:Container
  - type: Door
    canCrush: false
    openingAnimationTime: 0.9
    closingAnimationTime: 0.9
    openSound:
      path: /Audio/Machines/windoor_open.ogg
    closeSound:
      path: /Audio/Machines/windoor_open.ogg
    denySound:
      path: /Audio/Machines/airlock_deny.ogg
  - type: Airlock
    keepOpenIfClicked: true
    openPanelVisible: true
    denyAnimationTime: 0.4
    animatePanel: false
    openUnlitVisible: true
    # needed so that windoors will close regardless of whether there are people in it; it doesn't crush after all
    safety: false
  - type: NavMapDoor
  - type: DoorBolt
  - type: Electrified
    enabled: false
    usesApcPower: true
  - type: WiresPanel
  - type: Wires
    boardName: wires-board-name-windoor
    layoutId: Airlock
  - type: UserInterface
    interfaces:
      enum.AiUi.Key:
        type: StationAiBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: Appearance
  - type: WiresVisuals
  - type: Airtight
    noAirWhenFullyAirBlocked: false
    airBlockedDirection:
      - South
  - type: Construction
    graph: Windoor
    node: windoor
    containers:
    - board
  - type: StaticPrice
    price: 100
  - type: PryUnpowered
  - type: InteractionVerbs
    allowedVerbs:
    - KnockOn

- type: entity
  id: BaseSecureWindoor
  parent: BaseWindoor
  abstract: true
  components:
  - type: Sprite
    sprite: Structures/Doors/Windoors/windoor.rsi
    layers:
    - state: secure_underlay
    - state: closed
      map: [ "enum.DoorVisualLayers.Base" ]
    - state: closed_unlit
      shader: unshaded
      map: [ "enum.DoorVisualLayers.BaseUnlit" ]
    - state: welded
      map: [ "enum.WeldableLayers.BaseWelded" ]
    - state: bolted_unlit
      shader: unshaded
      map: [ "enum.DoorVisualLayers.BaseBolted" ]
    - state: emergency_unlit
      shader: unshaded
      map: [ "enum.DoorVisualLayers.BaseEmergencyAccess" ]
    - state: panel_open
      map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      visible: false
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 600
      behaviors: #excess damage (nuke?). avoid computational cost of spawning entities.
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 400
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlass:
            min: 1
            max: 2
          SheetPlasteel1:
            min: 1
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: Windoor
    node: windoorSecure
  - type: StaticPrice
    price: 200

#Plasma Windoors
- type: entity
  id: BasePlasmaWindoor
  parent: BaseWindoor
  abstract: true
  components:
  - type: Sprite
    sprite: Structures/Doors/Windoors/plasma.rsi
    layers:
    - state: closed
      map: [ "enum.DoorVisualLayers.Base" ]
    - state: closed_unlit
      shader: unshaded
      map: [ "enum.DoorVisualLayers.BaseUnlit" ]
    - state: welded
      map: [ "enum.WeldableLayers.BaseWelded" ]
    - state: bolted_unlit
      shader: unshaded
      map: [ "enum.DoorVisualLayers.BaseBolted" ]
    - state: emergency_unlit
      shader: unshaded
      map: [ "enum.DoorVisualLayers.BaseEmergencyAccess" ]
    - state: panel_open
      map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      visible: false
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 480
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 240
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassPlasma:
            min: 1
            max: 2
          SheetSteel1:
            min: 1
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: Windoor
    node: pwindoor
  - type: StaticPrice
    price: 170
  - type: RadiationBlocker
    resistance: 4

- type: entity
  id: BaseSecurePlasmaWindoor
  parent: BaseWindoor
  abstract: true
  components:
  - type: Sprite
    sprite: Structures/Doors/Windoors/plasma.rsi
    layers:
    - state: secure_underlay
    - state: closed
      map: [ "enum.DoorVisualLayers.Base" ]
    - state: closed_unlit
      shader: unshaded
      map: [ "enum.DoorVisualLayers.BaseUnlit" ]
    - state: welded
      map: [ "enum.WeldableLayers.BaseWelded" ]
    - state: bolted_unlit
      shader: unshaded
      map: [ "enum.DoorVisualLayers.BaseBolted" ]
    - state: emergency_unlit
      shader: unshaded
      map: [ "enum.DoorVisualLayers.BaseEmergencyAccess" ]
    - state: panel_open
      map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      visible: false
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 800
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 530
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassPlasma:
            min: 1
            max: 2
          SheetPlasteel1:
            min: 1
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: Windoor
    node: pwindoorSecure
  - type: StaticPrice
    price: 312
  - type: RadiationBlocker
    resistance: 6

#Uranium Windoors
- type: entity
  id: BaseUraniumWindoor
  parent: BaseWindoor
  abstract: true
  components:
  - type: Sprite
    sprite: Structures/Doors/Windoors/uranium.rsi
    layers:
    - state: closed
      map: [ "enum.DoorVisualLayers.Base" ]
    - state: closed_unlit
      shader: unshaded
      map: [ "enum.DoorVisualLayers.BaseUnlit" ]
    - state: welded
      map: [ "enum.WeldableLayers.BaseWelded" ]
    - state: bolted_unlit
      shader: unshaded
      map: [ "enum.DoorVisualLayers.BaseBolted" ]
    - state: emergency_unlit
      shader: unshaded
      map: [ "enum.DoorVisualLayers.BaseEmergencyAccess" ]
    - state: panel_open
      map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      visible: false
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 400
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 240
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassUranium:
            min: 1
            max: 2
          SheetSteel1:
            min: 1
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: Windoor
    node: uwindoor
  - type: StaticPrice
    price: 180
  - type: RadiationBlocker
    resistance: 5

- type: entity
  id: BaseSecureUraniumWindoor
  parent: BaseWindoor
  abstract: true
  components:
  - type: Sprite
    sprite: Structures/Doors/Windoors/uranium.rsi
    layers:
    - state: secure_underlay
    - state: closed
      map: [ "enum.DoorVisualLayers.Base" ]
    - state: closed_unlit
      shader: unshaded
      map: [ "enum.DoorVisualLayers.BaseUnlit" ]
    - state: welded
      map: [ "enum.WeldableLayers.BaseWelded" ]
    - state: bolted_unlit
      shader: unshaded
      map: [ "enum.DoorVisualLayers.BaseBolted" ]
    - state: emergency_unlit
      shader: unshaded
      map: [ "enum.DoorVisualLayers.BaseEmergencyAccess" ]
    - state: panel_open
      map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      visible: false
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 1200
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 400
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassUranium:
            min: 1
            max: 2
          SheetPlasteel1:
            min: 1
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: Windoor
    node: uwindoorSecure
  - type: StaticPrice
    price: 462
  - type: RadiationBlocker
    resistance: 8
