# shuttles from the shipyard will try to dock here
- type: entity
  parent: AirlockGlassShuttle
  id: AirlockExternalGlassShuttleShipyard
  suffix: External, Shipyard, Glass, Docking
  components:
  - type: PriorityDock
    tag: DockShipyard

# Delta V specific roles
- type: entity
  parent: AirlockScience
  id: AirlockMantisLocked
  suffix: Mantis, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsMantis ]


- type: entity
  parent: AirlockScienceGlass
  id: AirlockMantisGlassLocked
  suffix: Mantis, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsMantis ]

- type: entity
  parent: AirlockCommand
  id: AirlockChiefJusticeLocked
  suffix: Chief Justice, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsChiefJustice ]

- type: entity
  parent: AirlockCommandGlass
  id: AirlockChiefJusticeGlassLocked
  suffix: ChiefJustice, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsChiefJustice ]

- type: entity
  parent: AirlockJustice
  id: AirlockJusticeLocked
  suffix: Justice, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsJustice ]

- type: entity
  parent: AirlockJusticeGlass
  id: AirlockJusticeGlassLocked
  suffix: Justice, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsJustice ]

- type: entity
  parent: AirlockJustice
  id: AirlockProsecutorLocked
  suffix: Prosecutor, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsProsecutor ]

- type: entity
  parent: AirlockJusticeGlass
  id: AirlockProsecutorGlassLocked
  suffix: Prosecutor, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsProsecutor ]

- type: entity
  parent: AirlockJustice
  id: AirlockClerkLocked
  suffix: Clerk, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsClerk ]

- type: entity
  parent: AirlockJusticeGlass
  id: AirlockClerkGlassLocked
  suffix: Clerk, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsClerk ]

# Maintenance
- type: entity
  parent: AirlockMaint
  id: AirlockMaintChiefJusticeLocked
  suffix: ChiefJustice, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsChiefJustice ]

- type: entity
  parent: AirlockMaint
  id: AirlockMaintJusticeLocked
  suffix: Justice, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsJustice ]

- type: entity
  parent: AirlockMaint
  id: AirlockMaintProsecutorLocked
  suffix: Prosecutor, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsProsecutor ]

- type: entity
  parent: AirlockMaint
  id: AirlockMaintClerkLocked
  suffix: Clerk, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsClerk ]

- type: entity
  parent: AirlockSecurity
  id: AirlockCorpsmanLocked
  suffix: Corpsman, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsCorpsman ]

- type: entity
  parent: AirlockSecurityGlass
  id: AirlockCorpsmanGlassLocked
  suffix: Corpsman, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsCorpsman ]

- type: entity
  parent: AirlockGlassShuttle
  id: AirlockExternalGlassShuttleEscapeSub
  suffix: External, Escape Sub, Glass, Docking
  components:
    - type: GridFill
      path: /Maps/Shuttles/DeltaV/sub_escape_pod.yml

- type: entity
  parent: Airlock
  id: AirlockBoxerLocked
  suffix: Boxer, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsBoxer ]

- type: entity
  parent: Airlock
  id: AirlockClownLocked
  suffix: Clown, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsClown ]

- type: entity
  parent: Airlock
  id: AirlockMimeLocked
  suffix: Mime, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsMime ]

- type: entity
  parent: Airlock
  id: AirlockMusicianLocked
  suffix: Musician, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsMusician ]

- type: entity
  parent: Airlock
  id: AirlockReporterLocked
  suffix: Reporter, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsReporter ]

- type: entity
  parent: Airlock
  id: AirlockLibraryLocked
  suffix: Library, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsLibrary ]

- type: entity
  parent: Airlock
  id: AirlockZookeeperLocked
  suffix: Zookeeper, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsZookeeper ]

- type: entity
  parent: AirlockExternal
  id: AirlockExternalSalvageLocked
  suffix: External, Salvage, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsSalvage ]

- type: entity
  parent: AirlockMedical
  id: AirlockPsychologistLocked
  suffix: Psychologist, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsPsychologist ]

# Glass Airlocks
- type: entity
  parent: AirlockGlass
  id: AirlockBoxerGlassLocked
  suffix: Boxer, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsBoxer ]

- type: entity
  parent: AirlockGlass
  id: AirlockClownGlassLocked
  suffix: Clown, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsClown ]

- type: entity
  parent: AirlockGlass
  id: AirlockMimeGlassLocked
  suffix: Mime, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsMime ]

- type: entity
  parent: AirlockGlass
  id: AirlockMusicianGlassLocked
  suffix: Musician, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsMusician ]

- type: entity
  parent: AirlockGlass
  id: AirlockReporterGlassLocked
  suffix: Reporter, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsReporter ]

- type: entity
  parent: AirlockGlass
  id: AirlockLibraryGlassLocked
  suffix: Library, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsLibrary ]

- type: entity
  parent: AirlockGlass
  id: AirlockZookeeperGlassLocked
  suffix: Zookeeper, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsZookeeper ]

- type: entity
  parent: AirlockExternalGlass
  id: AirlockExternalGlassSalvageLocked
  suffix: External, Glass, Salvage, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsSalvage ]

- type: entity
  parent: AirlockMedicalGlass
  id: AirlockPsychologistGlassLocked
  suffix: Psychologist, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsPsychologist ]

# Maintenance Hatches
- type: entity
  parent: AirlockMaint
  id: AirlockMaintBoxerLocked
  suffix: Boxer, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsBoxer ]

- type: entity
  parent: AirlockMaint
  id: AirlockMaintClownLocked
  suffix: Clown, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsClown ]

- type: entity
  parent: AirlockMaint
  id: AirlockMaintMimeLocked
  suffix: Mime, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsMime ]

- type: entity
  parent: AirlockMaint
  id: AirlockMaintMusicianLocked
  suffix: Musician, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsMusician ]

- type: entity
  parent: AirlockMaint
  id: AirlockMaintReporterLocked
  suffix: Reporter, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsReporter ]

- type: entity
  parent: AirlockMaint
  id: AirlockMaintLibraryLocked
  suffix: Library, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsLibrary ]

- type: entity
  parent: AirlockMaint
  id: AirlockMaintZookeeperLocked
  suffix: Zookeeper, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsZookeeper ]

- type: entity
  parent: AirlockMaint
  id: AirlockMaintPsychologistLocked
  suffix: Psychologist, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsPsychologist ]

- type: entity
  parent: AirlockMaint
  id: AirlockMaintSecurityLawyerLocked
  suffix: Security/Lawyer, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsSecurityLawyer ]

# Command-locked External airlocks. These don't exist upstream for some reason.
- type: entity
  parent: AirlockExternal
  id: AirlockExternalCommandLocked
  suffix: External, Command, Locked
  components:
  - type: WiresPanelSecurity
    securityLevel: medSecurity
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsCommand ]

- type: entity
  parent: AirlockExternalGlass
  id: AirlockExternalGlassCommandLocked
  suffix: External, Glass, Command, Locked
  components:
  - type: WiresPanelSecurity
    securityLevel: medSecurity
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsCommand ]

- type: entity
  parent: AirlockExternal
  id: AirlockExternalSecurityLocked
  suffix: External, Security, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsSecurity ]

- type: entity
  parent: AirlockExternalGlass
  id: AirlockExternalGlassSecurityLocked
  suffix: External, Glass, Security, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsSecurity ]
