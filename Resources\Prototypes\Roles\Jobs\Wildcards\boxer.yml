- type: job
  id: Boxer
  name: job-name-boxer
  description: job-description-boxer
  playTimeTracker: JobBoxer
  requirements:
    - !type:CharacterOverallTimeRequirement
      min: 7200 #DeltaV 2 hours
  startingGear: BoxerGear
  icon: "JobIconBoxer"
  supervisors: job-supervisors-hop
  access:
  - Maintenance
  - Theatre # DeltaV - Add Theatre access
  - Boxer # DeltaV - Add Boxer access
  special: # Nyanotrasen - BoxerComponent, see Content.Server/Nyanotrasen/Abilities/Boxer/Boxer/BoxerComponent.cs
  - !type:AddTraitSpecial
    traits:
    - MartialArtist

- type: startingGear
  id: BoxerGear
  subGear:
  - BoxerPlasmamanGear
  equipment:
    jumpsuit: UniformShortsRed
    back: ClothingBackpackFilled
    id: BoxerPDA
    ears: ClothingHeadsetService
    gloves: ClothingHandsGlovesBoxingRed
    shoes: ClothingShoesColorRed
    belt: ClothingBeltChampion
  innerClothingSkirt: UniformShortsRedWithTop
  satchel: ClothingBackpackSatchelFilled
  duffelbag: ClothingBackpackDuffelFilled

- type: startingGear
  id: BoxerPlasmamanGear
  parent: BasePlasmamanGear
  equipment:
    jumpsuit: ClothingUniformEnvirosuitBoxing
    head: ClothingHeadEnvirohelmBoxing
    # No envirogloves, use the boxing gloves instead
