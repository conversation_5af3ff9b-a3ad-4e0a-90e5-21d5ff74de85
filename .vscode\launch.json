{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        // CLIENT CONFIGS
        {
            "name": "Client",
            "type": "coreclr",
            "request": "launch",
            "program": "${workspaceFolder}/bin/Content.Client/Content.Client.dll",
            "args": [],
            "console": "internalConsole",
            "stopAtEntry": false
        },
        {
            "name": "Client (Tools)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build (Tools)",
            "program": "${workspaceFolder}/bin/Content.Client/Content.Client.dll",
            "args": [],
            "console": "internalConsole",
            "stopAtEntry": false
        },
        {
            "name": "Client (Release)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build (Release)",
            "program": "${workspaceFolder}/bin/Content.Client/Content.Client.dll",
            "args": [],
            "console": "internalConsole",
            "stopAtEntry": false
        },
        // COMPATABILITY RENDERERS
        {
            "name": "Client (Compatibility Renderer)",
            "type": "coreclr",
            "request": "launch",
            "program": "${workspaceFolder}/bin/Content.Client/Content.Client.dll",
            "args": "--cvar display.compat=true",
            "console": "internalConsole",
            "stopAtEntry": false
        },
        {
            "name": "Client (Tools) - (Compatability Renderer)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build (Tools)",
            "program": "${workspaceFolder}/bin/Content.Client/Content.Client.dll",
            "args": ["--cvar display.compat=true"],
            "console": "internalConsole",
            "stopAtEntry": false
        },
        {
            "name": "Client (Release) - (Compatability Renderer)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build (Release)",
            "program": "${workspaceFolder}/bin/Debug/Content.Client/Content.Client.dll",
            "args": ["--cvar display.compat=true"],
            "console": "internalConsole",
            "stopAtEntry": false
        },
        // SERVER CONFIGS
        {
            "name": "Server",
            "type": "coreclr",
            "request": "launch",
            "program": "${workspaceFolder}/bin/Content.Server/Content.Server.dll",
            "args": [],
            "console": "integratedTerminal",
            "stopAtEntry": false
        },
        {
            "name": "Server (Tools)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build (Tools)",
            "program": "${workspaceFolder}/bin/Content.Server/Content.Server.dll",
            "args": [],
            "console": "internalConsole",
            "stopAtEntry": false
        },
        {
            "name": "Server (Release)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build (Release)",
            "program": "${workspaceFolder}/bin/Content.Server/Content.Server.dll",
            "args": [],
            "console": "internalConsole",
            "stopAtEntry": false
        },
        // LINTER
        {
            "name": "YAML Linter",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-yaml-linter",
            "program": "${workspaceFolder}/bin/Content.YAMLLinter/Content.YAMLLinter.dll",
            "cwd": "${workspaceFolder}/Content.YAMLLinter",
            "console": "internalConsole",
            "stopAtEntry": false
        }
    ],
    "compounds": [
        {
            "name": "Server/Client",
            "configurations": [
                "Server",
                "Client"
            ],
            "preLaunchTask": "build"
        },
        {
            "name": "Server/Client (Tools)",
            "configurations": [
                "Server (Tools)",
                "Client (Tools)"
            ],
            "preLaunchTask": "build (Tools)"
        },
        {
            "name": "Server/Client (Release)",
            "configurations": [
                "Server (Release)",
                "Client (Release)"
            ],
            "preLaunchTask": "build (Release)"
        }
    ]
}
