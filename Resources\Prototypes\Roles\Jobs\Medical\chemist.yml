- type: job
  id: Chemist
  name: job-name-chemist
  description: job-description-chemist
  playTimeTracker: JobChemist
  requirements:
    - !type:CharacterDepartmentTimeRequirement
      department: Medical
      min: 28800 # DeltaV - 8 hours
    - !type:CharacterEmployerRequirement
      employers:
      - ZengHuPharmaceuticals
      - Interdyne
      - NanoTrasen
  startingGear: ChemistGear
  icon: "JobIconChemist"
  supervisors: job-supervisors-cmo
  access:
  - Medical
  - Chemistry
  - Maintenance
  special:
  - !type:AddComponentSpecial
    components:
    - type: CPRTraining
    - type: SurgerySpeedModifier
      speedModifier: 1.75

- type: startingGear
  id: ChemistGear
  subGear:
  - ChemistPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitChemistry
    back: ClothingBackpackChemistryFilled
    shoes: ClothingShoesColorWhite
    id: ChemistryPDA
    ears: ClothingHeadsetMedical
  innerClothingSkirt: ClothingUniformJumpskirtChemistry
  satchel: ClothingBackpackSatchelChemistryFilled
  duffelbag: ClothingBackpackDuffelChemistryFilled

- type: startingGear
  id: ChemistPlasmamanGear
  parent: BasePlasmamanGear
  equipment:
    jumpsuit: ClothingUniformEnvirosuitChemist
    head: ClothingHeadEnvirohelmChemist
    gloves: ClothingHandsGlovesEnviroglovesWhite
