- type: entity
  categories: [ HideSpawnMenu ]
  id: BaseAPC
  name: "АП<PERSON>"
  description: "Термінал управління електричними системами району."
  placement:
    mode: SnapgridCenter
  components:
  - type: StationAiWhitelist
  - type: AmbientOnPowered
  - type: AmbientSound
    volume: -9
    range: 2
    sound:
      path: /Audio/Ambience/Objects/hdd_buzz.ogg
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#3db83b"
    castShadows: false
    netsync: false
  - type: Clickable
  - type: AccessReader
    access: [["Engineering"]]
  - type: InteractionOutline
  - type: Transform
    anchored: true
  - type: Sprite
    drawdepth: WallMountedItems
    sprite: Structures/Power/apc.rsi
    layers:
    - state: base
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
      visible: false
    - state: display-charging
      shader: unshaded
      map: ["enum.ApcVisualLayers.ChargeState"]
    - state: lock0-unlocked
      shader: unshaded
      map: ["enum.ApcVisualLayers.InterfaceLock"]
    - state: lock1-unlocked
      shader: unshaded
      map: ["enum.ApcVisualLayers.PanelLock"]
    - state: channel0-auto_on
      shader: unshaded
      map: ["enum.ApcVisualLayers.Equipment"]
    - state: channel1-auto_on
      shader: unshaded
      map: ["enum.ApcVisualLayers.Lighting"]
    - state: channel2-auto_on
      shader: unshaded
      map: ["enum.ApcVisualLayers.Environment"]
  - type: Appearance
  - type: ApcVisuals
  - type: Battery
    maxCharge: 50000
    startingCharge: 0
  - type: ExaminableBattery
  - type: NodeContainer
    examinable: true
    nodes:
      input:
        !type:CableDeviceNode
        nodeGroupID: MVPower
      output:
        !type:CableDeviceNode
        nodeGroupID: Apc
  - type: PowerMonitoringDevice
    group: APC
    sourceNode: input
    loadNode: output
    collectionName: apc
    sprite: Structures/Power/apc.rsi
    state: static
  - type: BatteryCharger
    voltage: Medium
  - type: PowerProvider
    voltage: Apc
  - type: Apc
    voltage: Apc
  - type: ExtensionCableProvider
  - type: UserInterface
    interfaces:
      enum.ApcUiKey.Key:
        type: ApcBoundUserInterface
  - type: ActivatableUI
    inHandsOnly: false
    singleUser: true
    key: enum.ApcUiKey.Key
  - type: Construction
    graph: APC
    node: apc
  - type: PowerNetworkBattery
    maxSupply: 10000
    maxChargeRate: 5000
    supplyRampTolerance: 1000
    supplyRampRate: 500
  - type: WallMount
  - type: WiresPanel
  - type: WiresVisuals
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: StrongMetallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors: #excess damage, don't spawn entities.
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
  - type: StationInfiniteBatteryTarget
  - type: Electrified
    onHandInteract: false
    onInteractUsing: false
    onBump: false
    requirePower: true
    mediumVoltageNode: input
    lowVoltageNode: output
  - type: LightningTarget
    priority: 1
  - type: StaticPrice
    price: 500
  - type: BatteryDrinkerSource # Parkstation IPCs
    maxAmount: 10000

# APC under construction
- type: entity
  categories: [ HideSpawnMenu ]
  id: APCFrame
  name: "Рама АПЦ"
  description: "Термінал управління електричними системами району, в якому відсутня електроніка."
  placement:
    mode: SnapgridCenter
  components:
  - type: Clickable
  - type: InteractionOutline
  - type: Transform
    anchored: true
  - type: Sprite
    drawdepth: WallMountedItems
    sprite: Structures/Power/apc.rsi
    state: frame
  - type: Construction
    graph: APC
    node: apcFrame
  - type: WallMount
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors: #excess damage, don't spawn entities.
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 1
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:DoActsBehavior
        acts: [ "Destruction" ]

# Constructed APC
- type: entity
  parent: BaseAPC
  id: APCConstructed
  suffix: Open
  components:
  - type: WiresPanel
    open: true

# APCs in use
- type: entity
  parent: BaseAPC
  id: APCBasic
  suffix: Basic, 50kW
  components:
  - type: Battery
    maxCharge: 50000
    startingCharge: 50000
  - type: BatteryDrinkerSource # Parkstation IPCs
    maxAmount: 5000

- type: entity
  parent: BaseAPC
  id: APCHighCapacity
  suffix: High Capacity, 100kW
  components:
  - type: Battery
    maxCharge: 100000
    startingCharge: 100000
  - type: BatteryDrinkerSource # Parkstation IPCs
    maxAmount: 12000

- type: entity
  parent: BaseAPC
  id: APCSuperCapacity
  suffix: Super Capacity, 150kW
  components:
  - type: Battery
    maxCharge: 150000
    startingCharge: 150000
  - type: BatteryDrinkerSource # Parkstation IPCs
    maxAmount: 18000

- type: entity
  parent: BaseAPC
  id: APCHyperCapacity
  suffix: Hyper Capacity, 200kW
  components:
  - type: Battery
    maxCharge: 200000
    startingCharge: 200000
  - type: BatteryDrinkerSource # Parkstation IPCs # Parkstation IPCs
    maxAmount: 26000
