# SURVEILLANCE
- type: construction
  name: "камера"
  id: camera
  graph: SurveillanceCamera
  startNode: start
  targetNode: camera
  category: construction-category-utilities
  description: "Камера спостереження. Вона спостерігає. Скоро."
  icon:
    sprite: Structures/Wallmounts/camera.rsi
    state: camera
  objectType: Structure
  placementMode: SnapgridCenter

- type: construction
  name: "телеекран"
  id: WallmountTelescreen
  graph: WallmountTelescreen
  startNode: start
  targetNode: Telescreen
  category: construction-category-utilities
  description: "Монітор для камери спостереження на стіну."
  icon:
    sprite: Structures/Machines/computers.rsi
    state: telescreen_frame
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true

- type: construction
  name: "мапа станції"
  id: StationMap
  graph: StationMap
  startNode: start
  targetNode: station_map
  category: construction-category-structures
  description: "Мапа станції."
  icon:
    sprite: Structures/Machines/station_map.rsi
    state: station_map0
  placementMode: SnapgridCenter
  objectType: Structure
  canRotate: true
  canBuildInImpassable: true
  conditions:
  - !type:WallmountCondition {}

# POWER
- type: construction
  name: "АПЦ"
  id: APC
  graph: APC
  startNode: start
  targetNode: apc
  category: construction-category-utilities
  description: "Контролер живлення зони (АПЦ). Контролює живлення. У зоні."
  icon:
    sprite: Structures/Power/apc.rsi
    state: base
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true

- type: construction
  name: "збірка сонячної панелі"
  id: SolarPanel
  graph: SolarPanel
  startNode: start
  targetNode: solarpanel
  category: construction-category-utilities
  description: "Можна перетворити на сонячну панель або сонячний трекер."
  icon:
    sprite: Structures/Power/Generation/solar_panel.rsi
    state: solar_assembly
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked
      failIfSpace: false

- type: construction
  name: "кабельна клема"
  id: CableTerminal
  graph: CableTerminal
  startNode: start
  targetNode: cable_terminal
  category: construction-category-utilities
  description: "Вхід для пристроїв типу SMES. Червоні кабелі повинні бути спрямовані до пристрою."
  icon:
    sprite: Structures/Power/cable_terminal.rsi
    state: term
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false

- type: construction
  name: "настінна підстанція"
  id: WallmountSubstation
  graph: WallmountSubstation
  startNode: start
  targetNode: substation
  category: construction-category-utilities
  description: "Настінна підстанція для компактних приміщень. Переконайтеся, що під нею прокладено кабель, перш ніж будувати стіну."
  icon:
    sprite: Structures/Power/substation.rsi
    state: substation_wall
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true

- type: construction
  name: "настінний генератор"
  id: WallmountGenerator
  graph: WallmountGenerator
  startNode: start
  targetNode: generator
  category: construction-category-utilities
  description: "Настінний генератор для компактних приміщень. Переконайтеся, що під нею прокладено кабель, перш ніж будувати стіну."
  icon:
    sprite: Structures/Power/Generation/wallmount_generator.rsi
    state: panel
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true

- type: construction
  name: "настінний блок живлення"
  id: WallmountGeneratorAPU
  graph: WallmountGenerator
  startNode: start
  targetNode: APU
  category: construction-category-utilities
  description: "Настінний блок живлення для компактних шатлів. Перед зведенням стіни обов'язково прокладіть під ним кабель."
  icon:
    sprite: Structures/Power/Generation/wallmount_generator.rsi
    state: panel
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true

# DISPOSALS
- type: construction
  name: "утилізаційна установка"
  description: "Пневматична установка для утилізації відходів."
  id: DisposalUnit
  graph: DisposalMachine
  startNode: start
  targetNode: disposal_unit
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/disposal.rsi
    state: "disposal"

- type: construction
  name: "поштовий блок"
  description: "Пневматичний пристрій для доставки пошти."
  id: MailingUnit
  graph: DisposalMachine
  startNode: start
  targetNode: mailing_unit
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/disposal.rsi
    state: "mailing"

- type: construction
  name: "каналізаційна труба"
  id: DisposalPipe
  description: "Величезний сегмент труби, що використовується для побудови систем утилізації."
  graph: DisposalPipe
  startNode: start
  targetNode: pipe
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/disposal.rsi
    state: conpipe-s

- type: construction
  name: "маркувальник утилізації"
  description: "Труба, яка тегує сутності для маршрутизації."
  id: DisposalTagger
  graph: DisposalPipe
  startNode: start
  targetNode: tagger
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/disposal.rsi
    state: conpipe-tagger

- type: construction
  name: "сміттєвий бак"
  description: "Трубна магістраль, що використовується як точка входу для систем утилізації."
  id: DisposalTrunk
  graph: DisposalPipe
  startNode: start
  targetNode: trunk
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/disposal.rsi
    state: conpipe-t

- type: construction
  name: "утилізаційний маршрутизатор"
  description: "Тристоронній маршрутизатор. Сутності з відповідними тегами перенаправляються в сторону."
  id: DisposalRouter
  graph: DisposalPipe
  startNode: start
  targetNode: router
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/disposal.rsi
    state: conpipe-j1s
  mirror: DisposalRouterFlipped

- type: construction
  hide: true
  name: "утилізаційний маршрутизатор"
  description: "Тристоронній маршрутизатор. Сутності з відповідними тегами перенаправляються в сторону."
  id: DisposalRouterFlipped
  graph: DisposalPipe
  startNode: start
  targetNode: routerflipped
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/disposal.rsi
    state: conpipe-j2s
  mirror: DisposalRouter

- type: construction
  name: "маршрутизатор сигналу утилізації"
  description: "Тристоронній маршрутизатор, керований сигналом."
  id: DisposalSignalRouter
  graph: DisposalPipe
  startNode: start
  targetNode: signal_router
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/disposal.rsi
    state: signal-router-free
  mirror: DisposalSignalRouterFlipped

- type: construction
  hide: true
  name: "маршрутизатор сигналу утилізації"
  description: "Тристоронній маршрутизатор, керований сигналом."
  id: DisposalSignalRouterFlipped
  graph: DisposalPipe
  startNode: start
  targetNode: signal_router_flipped
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/disposal.rsi
    state: signal-router-flipped-free
  mirror: DisposalSignalRouter

- type: construction
  name: "утилізаційний вузол"
  description: "Тристороннє перехрестя. Стрілка вказує, куди виходять елементи."
  id: DisposalJunction
  graph: DisposalPipe
  startNode: start
  targetNode: junction
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/disposal.rsi
    state: conpipe-j1
  mirror: DisposalJunctionFlipped

- type: construction
  hide: true
  name: "утилізаційний вузол"
  description: "Тристороннє перехрестя. Стрілка вказує, куди виходять елементи."
  id: DisposalJunctionFlipped
  graph: DisposalPipe
  startNode: start
  targetNode: junctionflipped
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/disposal.rsi
    state: conpipe-j2
  mirror: DisposalJunction

- type: construction
  name: "утилізація Y-образного переходу"
  description: "Тристороння розв'язка з ще однією точкою виходу."
  id: DisposalYJunction
  graph: DisposalPipe
  startNode: start
  targetNode: yJunction
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/disposal.rsi
    state: conpipe-y

- type: construction
  name: "утилізаційний вигин"
  description: "Трубка, зігнута під кутом 90 градусів."
  id: DisposalBend
  graph: DisposalPipe
  startNode: start
  targetNode: bend
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/disposal.rsi
    state: conpipe-c

# ATMOS
- type: construction
  name: "повітряна сигналізація"
  id: AirAlarmFixture
  graph: AirAlarm
  startNode: start
  targetNode: air_alarm
  category: construction-category-structures
  description: "Повітряна сигналізація. Сигналізує... повітря?"
  icon:
    sprite: Structures/Wallmounts/air_monitors.rsi
    state: alarm0
  placementMode: SnapgridCenter
  objectType: Structure
  canRotate: true
  canBuildInImpassable: true
  conditions:
  - !type:WallmountCondition {}

- type: construction
  name: "пожежна сигналізація"
  id: FireAlarm
  graph: FireAlarm
  startNode: start
  targetNode: fire_alarm
  category: construction-category-structures
  description: "Пожежна тривога. Гаряче!"
  icon:
    sprite: Structures/Wallmounts/air_monitors.rsi
    state: fire0
  placementMode: SnapgridCenter
  objectType: Structure
  canRotate: true
  canBuildInImpassable: true
  conditions:
  - !type:WallmountCondition {}

- type: construction
  name: "датчик повітря"
  id: AirSensor
  graph: AirSensor
  startNode: start
  targetNode: sensor
  category: construction-category-structures
  description: "Датчик повітря. Відчуває повітря."
  icon:
    sprite: Structures/Specific/Atmospherics/sensor.rsi
    state: gsensor1
  placementMode: SnapgridCenter
  objectType: Structure
  canRotate: true

- type: construction
  name: "датчик газових труб"
  id: GasPipeSensor
  graph: GasPipeSensor
  startNode: start
  targetNode: sensor
  category: construction-category-structures
  description: "Звіти про стан газу в приєднаній трубопровідній мережі."
  icon:
    sprite: Structures/Piping/Atmospherics/gas_pipe_sensor.rsi
    state: icon
  placementMode: SnapgridCenter
  objectType: Structure
  canRotate: true

# ATMOS PIPES
- type: construction
  name: "газова труба половина"
  id: GasPipeHalf
  description: "Половина газової труби. Жодних скейтбордів."
  graph: GasPipe
  startNode: start
  targetNode: half
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: true
  icon:
    sprite: Structures/Piping/Atmospherics/pipe.rsi
    state: pipeHalf

- type: construction
  name: "газова труба пряма"
  id: GasPipeStraight
  description: "Прямий сегмент труби."
  graph: GasPipe
  startNode: start
  targetNode: straight
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: true
  icon:
    sprite: Structures/Piping/Atmospherics/pipe.rsi
    state: pipeStraight

- type: construction
  name: "газова труба кутова"
  id: GasPipeBend
  description: "Сегмент труби, зігнутий під кутом 90 градусів."
  graph: GasPipe
  startNode: start
  targetNode: bend
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: true
  icon:
    sprite: Structures/Piping/Atmospherics/pipe.rsi
    state: pipeBend

- type: construction
  name: "газова труба Т-образна"
  id: GasPipeTJunction
  description: "Сегмент труби з Т-подібним перехідником."
  graph: GasPipe
  startNode: start
  targetNode: tjunction
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: true
  icon:
    sprite: Structures/Piping/Atmospherics/pipe.rsi
    state: pipeTJunction

- type: construction
  name: "газова труба чотиристороння"
  id: GasPipeFourway
  description: "Сегмент труби з чотиристороннім перехідником."
  graph: GasPipe
  startNode: start
  targetNode: fourway
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: true
  icon:
    sprite: Structures/Piping/Atmospherics/pipe.rsi
    state: pipeFourway

# ATMOS UNARY
- type: construction
  name: "вентиляційний отвір"
  description: "Накачує газ у кімнату."
  id: GasVentPump
  graph: GasUnary
  startNode: start
  targetNode: ventpump
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/Atmospherics/vent.rsi
    state: vent_off
  layers:
  - sprite: Structures/Piping/Atmospherics/pipe.rsi
    state: pipeHalf
  - sprite: Structures/Piping/Atmospherics/vent.rsi
    state: vent_off
  conditions:
    - !type:NoUnstackableInTile

- type: construction
  name: "пасивна вентиляція"
  description: "Непривідний вентилятор, який вирівнює гази з обох боків."
  id: GasPassiveVent
  graph: GasUnary
  startNode: start
  targetNode: passivevent
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/Atmospherics/vent.rsi
    state: vent_off
  layers:
  - sprite: Structures/Piping/Atmospherics/pipe.rsi
    state: pipeHalf
  - sprite: Structures/Piping/Atmospherics/vent.rsi
    state: vent_off
  conditions:
    - !type:NoUnstackableInTile

- type: construction
  name: "повітряний скрубер"
  description: "Всмоктує газ у підключені труби."
  id: GasVentScrubber
  graph: GasUnary
  startNode: start
  targetNode: ventscrubber
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/Atmospherics/scrubber.rsi
    state: scrub_off
  layers:
  - sprite: Structures/Piping/Atmospherics/pipe.rsi
    state: pipeHalf
  - sprite: Structures/Piping/Atmospherics/scrubber.rsi
    state: scrub_off
  conditions:
    - !type:NoUnstackableInTile

- type: construction
  name: "інжектор повітря"
  description: "Нагнітає повітря в атмосферу."
  id: GasOutletInjector
  graph: GasUnary
  startNode: start
  targetNode: outletinjector
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/Atmospherics/outletinjector.rsi
    state: injector
  layers:
  - sprite: Structures/Piping/Atmospherics/pipe.rsi
    state: pipeHalf
  - sprite: Structures/Piping/Atmospherics/outletinjector.rsi
    state: injector
  conditions:
    - !type:NoUnstackableInTile

# ATMOS BINARY
- type: construction
  name: "газовий насос"
  id: GasPressurePump
  description: "Насос, який переміщує газ під дією тиску."
  graph: GasBinary
  startNode: start
  targetNode: pressurepump
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/Atmospherics/pump.rsi
    state: pumpPressure
  layers:
  - sprite: Structures/Piping/Atmospherics/pipe.rsi
    state: pipeStraight
  - sprite: Structures/Piping/Atmospherics/pump.rsi
    state: pumpPressure
  conditions:
    - !type:NoUnstackableInTile

- type: construction
  name: "об'ємний газовий насос"
  description: "Насос, який переміщує газ за об'ємом."
  id: GasVolumePump
  graph: GasBinary
  startNode: start
  targetNode: volumepump
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/Atmospherics/pump.rsi
    state: pumpVolume
  layers:
  - sprite: Structures/Piping/Atmospherics/pipe.rsi
    state: pipeStraight
  - sprite: Structures/Piping/Atmospherics/pump.rsi
    state: pumpVolume
  conditions:
    - !type:NoUnstackableInTile

- type: construction
  id: GasPassiveGate
  name: "пасивні ворота"
  description: "Односторонній повітряний клапан, який не потребує живлення."
  graph: GasBinary
  startNode: start
  targetNode: passivegate
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/Atmospherics/pump.rsi
    state: pumpPassiveGate
  layers:
  - sprite: Structures/Piping/Atmospherics/pipe.rsi
    state: pipeStraight
  - sprite: Structures/Piping/Atmospherics/pump.rsi
    state: pumpPassiveGate
  conditions:
    - !type:NoUnstackableInTile

- type: construction
  id: GasValve
  name: "ручний клапан"
  description: "Труба з клапаном, за допомогою якого можна перекрити потік газу через неї."
  graph: GasBinary
  startNode: start
  targetNode: valve
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/Atmospherics/pump.rsi
    state: pumpManualValve
  layers:
  - sprite: Structures/Piping/Atmospherics/pipe.rsi
    state: pipeStraight
  - sprite: Structures/Piping/Atmospherics/pump.rsi
    state: pumpManualValve
  conditions:
    - !type:NoUnstackableInTile

- type: construction
  id: SignalControlledValve
  name: "сигнальний клапан"
  description: "Клапан керується за допомогою сигнальних входів."
  graph: GasBinary
  startNode: start
  targetNode: signalvalve
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/Atmospherics/pump.rsi
    state: pumpSignalValve
  layers:
  - sprite: Structures/Piping/Atmospherics/pipe.rsi
    state: pipeStraight
  - sprite: Structures/Piping/Atmospherics/pump.rsi
    state: pumpSignalValve
  conditions:
  - !type:NoUnstackableInTile

- type: construction
  id: GasPort
  name: "порт роз'єму"
  description: "Для підключення портативних пристроїв, пов'язаних з контролем атмосфери."
  graph: GasBinary
  startNode: start
  targetNode: port
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/Atmospherics/gascanisterport.rsi
    state: gasCanisterPort
  layers:
  - sprite: Structures/Piping/Atmospherics/pipe.rsi
    state: pipeHalf
  - sprite: Structures/Piping/Atmospherics/gascanisterport.rsi
    state: gasCanisterPort
  conditions:
    - !type:NoUnstackableInTile

- type: construction
  id: GasDualPortVentPump
  name: "двопортовий вентиляційний отвір"
  description: "Має клапан і прикріплений до нього насос. Має два порти, один - вхідний для випуску повітря, інший - вихідний при сифонізації."
  graph: GasBinary
  startNode: start
  targetNode: dualportventpump
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/Atmospherics/vent.rsi
    state: vent_off
  layers:
  - sprite: Structures/Piping/Atmospherics/pipe.rsi
    state: pipeStraight
  - sprite: Structures/Piping/Atmospherics/vent.rsi
    state: vent_off

- type: construction
  id: HeatExchanger
  name: "радіатор"
  description: "Передає тепло між трубою та її оточенням."
  graph: GasBinary
  startNode: start
  targetNode: radiator
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/Atmospherics/heatexchanger.rsi
    state: heStraight

# ATMOS TRINARY
- type: construction
  id: GasFilter
  name: "газовий фільтр"
  description: "Дуже корисний для фільтрації газів."
  graph: GasTrinary
  startNode: start
  targetNode: filter
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/Atmospherics/gasfilter.rsi
    state: gasFilter
  mirror: GasFilterFlipped
  conditions:
    - !type:NoUnstackableInTile

- type: construction
  id: GasFilterFlipped
  hide: true
  name: "газовий фільтр"
  description: "Дуже корисний для фільтрації газів."
  graph: GasTrinary
  startNode: start
  targetNode: filterflipped
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/Atmospherics/gasfilter.rsi
    state: gasFilterF
  mirror: GasFilter
  conditions:
    - !type:NoUnstackableInTile

- type: construction
  id: GasMixer
  name: "газозмішувач"
  description: "Дуже корисний для змішування газів."
  graph: GasTrinary
  startNode: start
  targetNode: mixer
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/Atmospherics/gasmixer.rsi
    state: gasMixer
  mirror: GasMixerFlipped
  conditions:
    - !type:NoUnstackableInTile

- type: construction
  id: GasMixerFlipped
  hide: true
  name: "газозмішувач"
  description: "Дуже корисний для змішування газів."
  graph: GasTrinary
  startNode: start
  targetNode: mixerflipped
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/Atmospherics/gasmixer.rsi
    state: gasMixerF
  mirror: GasMixer
  conditions:
    - !type:NoUnstackableInTile

- type: construction
  id: PressureControlledValve
  name: "пневматичний клапан"
  description: "Клапан, керований тиском."
  graph: GasTrinary
  startNode: start
  targetNode: pneumaticvalve
  category: construction-category-utilities
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Piping/Atmospherics/pneumaticvalve.rsi
    state: off
  conditions:
    - !type:NoUnstackableInTile

# INTERCOM
- type: construction
  name: "інтерком"
  id: IntercomAssembly
  graph: Intercom
  startNode: start
  targetNode: intercom
  category: construction-category-structures
  description: "Переговорний пристрій. Для випадків, коли станція просто хоче щось дізнатися."
  icon:
    sprite: Structures/Wallmounts/intercom.rsi
    state: base
  placementMode: SnapgridCenter
  objectType: Structure
  canRotate: true
  canBuildInImpassable: true
  conditions:
  - !type:WallmountCondition {}

# TIMERS
- type: construction
  name: "таймер сигналу"
  id: SignalTimer
  graph: Timer
  startNode: start
  targetNode: signal
  category: construction-category-utilities
  description: "Настінний таймер для надсилання синхронізованих сигналів речам."
  icon:
    sprite: Structures/Wallmounts/switch.rsi
    state: on
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true
  conditions:
  - !type:WallmountCondition

- type: construction
  name: "таймер на екрані"
  id: ScreenTimer
  graph: Timer
  startNode: start
  targetNode: screen
  category: construction-category-utilities
  description: "Настінний таймер для надсилання часових сигналів предметам. Цей має екран для відображення тексту."
  icon:
    sprite: Structures/Wallmounts/signalscreen.rsi
    state: signalscreen
  objectType: Structure
  canRotate: false
  placementMode: SnapgridCenter
  canBuildInImpassable: true
  conditions:
  - !type:WallmountCondition

- type: construction
  name: "таймер гауптвахти"
  id: BrigTimer
  graph: Timer
  startNode: start
  targetNode: brig
  category: construction-category-utilities
  description: "Настінний таймер для надсилання часових сигналів предметам. Цей таймер має екран для відображення тексту і вимагає доступу до системи безпеки для використання."
  icon:
    sprite: Structures/Wallmounts/signalscreen.rsi
    state: signalscreen
  objectType: Structure
  canRotate: false
  placementMode: SnapgridCenter
  canBuildInImpassable: true
  conditions:
  - !type:WallmountCondition

# DEFENSES
- type: construction
  name: "панель керування туреллю"
  id: WeaponEnergyTurretControlPanel
  graph: WeaponEnergyTurretControlPanel
  startNode: start
  targetNode: finish
  category: construction-category-utilities
  description: "Настінний інтерфейс для дистанційного налаштування робочих параметрів підключених сторожових турелей."
  icon:
    sprite: Structures/Wallmounts/turret_controls.rsi
    state: base
  objectType: Structure
  canRotate: true
  placementMode: SnapgridCenter
  canBuildInImpassable: true
  conditions:
  - !type:WallmountCondition

# BOTANY
- type: construction
  name: "саморобний гідропонний лоток"
  id: makeshiftHydroTray
  graph: MakeshiftHydroTrayGraph
  startNode: start
  targetNode: makeshiftHydroTray
  category: construction-category-utilities
  description: "Дешевий гідропонний лоток без будь-яких вигадливих технологій. Уважно стежте за космічними бур'янами, оскільки ці лотки не мають попереджувальних вогнів!"
  icon:
    sprite: Structures/Hydroponics/containers.rsi
    state: hydrotray2
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
