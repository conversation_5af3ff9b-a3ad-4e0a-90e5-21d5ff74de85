- type: entity
  id: FloorWaterEntity
  name: "вода"
  description: "Справжній напій для втамування спраги."
  placement:
    mode: SnapgridCenter
    snap:
    - Wall
  components:
  - type: FloorOccluder
  - type: Transform
    anchored: true
  - type: SyncSprite
  - type: Clickable
  - type: Sprite
    sprite: Tiles/Planet/water.rsi
    drawdepth: BelowFloor
    layers:
      - state: shoreline_water
  - type: SolutionContainerManager
    solutions:
      pool:
        maxVol: 9999999 #.inf seems to break the whole yaml file, but would definitely be preferable.
        reagents:
        - ReagentId: Water
          Quantity: 9999999
  - type: SolutionRegeneration
    solution: tank
    generated:
      reagents:
      - ReagentId: Water
        Quantity: 100
  - type: DrainableSolution
    solution: pool
  - type: SpeedModifierContacts
    walkSpeedModifier: 0.5
    sprintSpeedModifier: 0.5
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.5,-0.5,0.5,0.5"
        layer:
          - SlipLayer
        mask:
          - ItemMask
        density: 1000
        hard: false
      # Goobstation - fishing
      fishing:
        shape:
          !type:PhysShapeCircle
          radius: 0.4
        layer:
        - ItemMask
        mask:
        - HighImpassable
        density: 1000
        hard: false
      # Goobstation - fishing
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepWater
      params:
        volume: 8
  # Goobstation - fishing
  - type: FishingSpot
    fishList: !type:NestedSelector
      tableId: WaterFishingLootTable
    fishDefaultTimer: 25.0
    fishTimerVariety: 15.0
  # Goobstation - fishing
