- type: entity
  abstract: true
  parent: BaseObjective
  id: BaseParadoxAnomalyObjective
  components:
  - type: Objective
    difficulty: 1
    issuer: objective-issuer-self

# not using base kill/keep alive objectives since these intentionally conflict with eachother
- type: entity
  parent: BaseParadoxAnomalyObjective
  id: ParadoxAnomalyKillObjective
  description: "У цьому всесвіті немає місця для нас обох."
  components:
  - type: Objective
    icon:
      sprite: Objects/Weapons/Guns/Pistols/viper.rsi
      state: icon
  - type: TargetObjective
    title: objective-paradox-anomaly-kill-title
  - type: TerminatorTargetOverride
  - type: KillPersonCondition
    requireDead: true

- type: entity
  parent: BaseParadoxAnomalyObjective
  id: ParadoxAnomalyFriendObjective
  description: "Можливо, знайдеться місце, як для друзів."
  components:
  - type: Objective
    icon:
      sprite: Objects/Misc/bureaucracy.rsi
      state: folder-white
  - type: TargetObjective
    title: objective-paradox-anomaly-friend-title
  - type: TerminatorTargetOverride
  - type: KeepAliveCondition

- type: entity
  parent: [BaseParadoxAnomalyObjective, BaseLivingObjective]
  id: ParadoxAnomalyEscapeObjective
  name: "Втекти до ЦК живим і неушкодженим."
  description: "Тепер це твій всесвіт."
  components:
  - type: Objective
    icon:
      sprite: Structures/Furniture/chairs.rsi
      state: shuttle
  - type: EscapeShuttleCondition
