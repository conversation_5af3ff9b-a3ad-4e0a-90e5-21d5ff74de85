# Tier 1

# STOP. STOP ADDING NEW WEAPONS AS A SINGLE RESEARCH ENTRY. ADD NEW WEAPONS HERE, NOT ON THEIR OWN.
# DO NOT ADD ANOTHER TECH
- type: technology
  id: BasicWeapons
  name: research-technology-basic-weapons
  icon:
    sprite: Objects/Weapons/Guns/Battery/laser_gun.rsi
    state: icon
  discipline: Arsenal
  tier: 1
  cost: 10000
  recipeUnlocks:
  - WeaponProtoKineticAccelerator
  - ShuttleGunKineticCircuitboard
  - WeaponLaserCarbine
  - WeaponMechCombatFiredartLaser # Goobstation
  - WeaponEnergyGun
  - WeaponEnergyGunMini
  - WeaponLaserSvalinn
  - WeaponEnergyGunPistol
  - WeaponDisablerSMG
  - Truncheon
  - WeaponDisabler
  - WeaponCivilianDisabler
  - WeaponMechCombatDisabler # Goobstation
  #- WeaponPistolMk58 #todo: Add a bunch of basic ballistic guns to the list and make lathe recipes for them.
  - MechEquipmentKineticAccelerator # Goobstation
  - WeaponPistolMk58
  # These are roundstart but not replenishable for salvage
  # Goobstation R&D Console rework start
  position: 1,0
  # Goobstation R&D Console rework end

- type: technology
  id: DraconicMunitions
  name: research-technology-draconic-munitions
  icon:
    sprite: Objects/Weapons/Guns/Ammunition/Boxes/pistol.rsi
    state: incendiarydisplay
  discipline: Arsenal
  tier: 1
  cost: 10000
  recipeUnlocks:
  - BoxShotgunIncendiary
  - MagazineRifleIncendiary
  - MagazinePistolIncendiary
  - MagazinePistolSubMachineGunIncendiary
  - MagazineMagnumIncendiary
  - MagazineLightRifleIncendiary
  - MagazineCaselessRifleIncendiary
  - SpeedLoaderMagnumIncendiary
  - SpeedLoaderRifleHeavyIncendiary
  - MagazineShotgunIncendiary
  - MagazineBoxPistolIncendiary
  - MagazineBoxMagnumIncendiary
  - MagazineBoxLightRifleIncendiary
  - MagazineBoxRifleIncendiary
  - MagazineBoxCaselessRifleIncendiary
  - CartridgeSpecialIncendiary
  - MagazineBoxSpecialIncendiary
  - MagazineUniversalMagnumIncendiary
  # Goobstation R&D Console rework start
  position: 3,-1
  technologyPrerequisites:
    - ShrapnelMunitions
  # Goobstation R&D Console rework end

- type: technology
  id: UraniumMunitions
  name: research-technology-uranium-munitions
  icon:
    sprite: Objects/Materials/Sheets/other.rsi
    state: uranium
  discipline: Arsenal
  tier: 1
  cost: 7500
  recipeUnlocks:
  - MagazineRifleUranium
  - MagazinePistolUranium
  - MagazinePistolSubMachineGunUranium
  - MagazineMagnumUranium
  - MagazineLightRifleUranium
  - MagazineCaselessRifleUranium
  - SpeedLoaderMagnumUranium
  - SpeedLoaderRifleHeavyUranium # Frontier
  - MagazineBoxPistolUranium
  - MagazineBoxMagnumUranium
  - MagazineBoxLightRifleUranium
  - MagazineBoxRifleUranium
  - MagazineBoxCaselessRifleUranium
  - BoxShotgunUranium
  - CartridgeSpecialUranium
  - MagazineBoxSpecialUranium
  - MagazineUniversalMagnumUranium
  # Goobstation R&D Console rework start
  position: 3,-2
  technologyPrerequisites:
    - ShrapnelMunitions
  # Goobstation R&D Console rework end

# Tier 2

- type: technology
  id: AdvancedTacsuits
  name: research-technology-advanced-tacsuits
  icon:
    sprite: DeltaV/Clothing/OuterClothing/Hardsuits/Combat/standard.rsi
    state: icon
  discipline: Arsenal
  tier: 2
  cost: 15000
  recipeUnlocks:
  - ClothingOuterHardsuitCombatStandard
  - ClothingOuterHardsuitCombatMedical
  - ClothingOuterHardsuitCombatRiot
  # Goobstation R&D Console rework start
  position: 4,-2
  technologyPrerequisites:
    - AdvancedWeapons
  #  - HardsuitsArmoredAdvanced PIRATE
  # Goobstation R&D Console rework end

- type: technology
  id: ExplosiveTechnology
  name: research-technology-explosive-technology
  icon:
    sprite: Objects/Devices/payload.rsi
    state: payload-explosive-armed
  discipline: Arsenal
  tier: 2
  cost: 10000
  recipeUnlocks:
  - SignallerAdvanced
  - SignalTrigger
  - VoiceTrigger
  - TimerTrigger
  - FlashPayload
  - ExplosivePayload
  - ChemicalPayload
  # TODO: Add "Explosive Ammunition" to this tech to make it waaaay more interesting.
  # Goobstation R&D Console rework start
  position: 4,-1
  technologyPrerequisites:
    - UraniumMunitions
  # Goobstation R&D Console rework end

- type: technology
  id: ShrapnelMunitions
  name: research-technology-shrapnel-munitions
  icon:
    sprite: Objects/Weapons/Guns/Ammunition/Casings/shotgun_shell.rsi
    state: base
  discipline: Arsenal
  tier: 2
  cost: 7500
  recipeUnlocks:
  - MagazineRifleShrapnel
  - MagazinePistolShrapnel
  - MagazinePistolSubMachineGunShrapnel
  - MagazineMagnumShrapnel
  - MagazineLightRifleShrapnel
  - MagazineCaselessRifleShrapnel
  - MagazineBoxPistolShrapnel
  - MagazineBoxMagnumShrapnel
  - MagazineBoxLightRifleShrapnel
  - MagazineBoxRifleShrapnel
  - MagazineBoxCaselessRifleShrapnel
  - MagazineUniversalMagnumShrapnel
  # Goobstation R&D Console rework start
  position: 2,-2
  technologyPrerequisites:
    - BasicWeapons
  # Goobstation R&D Console rework end

- type: technology
  id: AdvancedWeapons
  name: research-technology-advanced-weapons
  icon:
    sprite: Objects/Weapons/Guns/Battery/laser_cannon.rsi
    state: icon
  discipline: Arsenal
  tier: 2
  cost: 15000
  recipeUnlocks:
  - PowerCageRechargerCircuitboard
  - PowerCageSmall
  - PowerCageMedium
  - MagazineGrenadeEmpty
  - GrenadeFlash
  - GrenadeBlast
  - ShuttleGunSvalinnMachineGunCircuitboard
  - ShuttleGunPerforatorCircuitboard
  - ShuttleGunFriendshipCircuitboard
  - WeaponXrayCannon
  - WeaponGunLaserCarbineAutomatic
  - EnergyDomeDirectionalTurtle #PIRATE
  - WeaponLaserCannon
  - WeaponMechCombatSolarisLaser # Goobstation
  - WeaponSubMachineGunFPA90
  - WeaponPistolN1984
  - WeaponPistolViper
  - WeaponPistolCobra
  - Terminus
  - WeaponPistolUniversal
  # Goobstation R&D Console rework start
  technologyPrerequisites:
  - WeaponPlasmaRifleTech
  position: 3,0
  # Goobstation R&D Console rework end

# Tier 3

- type: technology
  id: PrototypeTacsuits
  name: research-technology-prototype-tacsuits
  icon:
    sprite: Nyanotrasen/Clothing/OuterClothing/ReverseEngineering/syndicate.rsi
    state: icon
  discipline: Arsenal
  tier: 3
  cost: 25000
  recipeUnlocks:
  - ClothingOuterHardsuitCombatAdvanced
  - ClothingOuterHardsuitShanlinUnpainted
  - ClothingOuterHardsuitShiweiUnpainted
  - ClothingOuterHardsuitJuggernautReverseEngineered
  # Goobstation R&D Console rework start
  technologyPrerequisites:
  - AdvancedTacsuits
  position: 5,-2
  # Goobstation R&D Console rework end

- type: technology
  id: LaserTurrets
  name: research-technology-laser-sentry-turrets
  icon:
    sprite: Objects/Weapons/Guns/Turrets/sentry_turret.rsi
    state: base
  discipline: Arsenal
  tier: 3
  cost: 20000
  recipeUnlocks:
  - WeaponEnergyTurretAIMachineCircuitboard
  - WeaponEnergyTurretStationMachineCircuitboard
  - WeaponEnergyTurretAIControlPanelElectronics
  - WeaponEnergyTurretStationControlPanelElectronics
  # Goobstation R&D Console rework start
  technologyPrerequisites:
  - PrototypeWeapons
  position: 5,1
  # Goobstation R&D Console rework end

- type: technology
  id: PrototypeWeapons
  name: research-technology-prototype-weapons
  icon:
    sprite: Objects/Weapons/Guns/Battery/advancedlasergun.rsi
    state: icon
  discipline: Arsenal
  tier: 3
  cost: 20000
  recipeUnlocks:
  - WeaponAdvancedLaser
  - PortableRecharger
  - GrenadeEMP
  - PowerCageHigh
  - ShuttleGunDusterCircuitboard
  - WeaponMechCombatImmolationGun # Goobstation
  - EnergySword # TODO: Add a bunch of stupidly exotic energy weapons to act as a reaaaaaally nice incentive for research to consider this.
  - EnergySwordDouble
  - EnergyCutlass
  - WeaponSubMachineGunBRDIR25
  # Goobstation R&D Console rework start
  technologyPrerequisites:
    - AdvancedWeapons
  position: 4,0
  # Goobstation R&D Console rework end
