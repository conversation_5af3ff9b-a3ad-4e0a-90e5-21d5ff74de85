﻿- type: construction
  name: "ко<PERSON><PERSON>'ютер"
  id: Computer
  graph: Computer
  startNode: start
  targetNode: computer
  category: construction-category-machines
  description: "Рама, яка використовується для побудови чого-небудь з комп'ютерною платою."
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Machines/parts.rsi
    state: 4

- type: construction
  name: "рама машини"
  description: "Машина на стадії будівництва. Потрібно більше деталей."
  id: MachineFrame
  graph: Machine
  startNode: start
  targetNode: machine
  category: construction-category-machines
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Machines/parts.rsi
    state: "box_0"

# Switching
- type: construction
  name: "двосторонній важіль"
  id: TwoWayLeverRecipe
  graph: LeverGraph
  startNode: start
  targetNode: LeverNode
  category: construction-category-machines
  description: "Важіль для керування машинами. Має 3 режими."
  objectType: Structure
  canBuildInImpassable: false
  icon:
    sprite: Structures/conveyor.rsi
    state: switch-off
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "вимикач світла"
  id: LightSwitchRecipe
  graph: LightSwitchGraph
  startNode: start
  targetNode: LightSwitchNode
  category: construction-category-machines
  description: "Вимикач для ввімкнення/вимкнення світла, підключеного до того ж АПЦ."
  icon:
    sprite: Structures/Wallmounts/switch.rsi
    state: on
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: true
  canBuildInImpassable: true
  conditions:
    - !type:WallmountCondition

- type: construction
  name: "сигнальний вимикач"
  id: SignalSwitchRecipe
  graph: SignalSwitchGraph
  startNode: start
  targetNode: SignalSwitchNode
  category: construction-category-machines
  description: "Це вимикач для ввімкнення/вимкнення живлення."
  icon:
    sprite: Structures/Wallmounts/switch.rsi
    state: on
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: true
  canBuildInImpassable: true
  conditions:
    - !type:WallmountCondition

- type: construction
  name: "сигнальна кнопка"
  id: SignalButtonRecipe
  graph: SignalButtonGraph
  startNode: start
  targetNode: SignalButtonNode
  category: construction-category-machines
  description: "Це кнопка для активації чогось."
  icon:
    sprite: Structures/Wallmounts/switch.rsi
    state: on
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: true
  canBuildInImpassable: true
  conditions:
    - !type:WallmountCondition

- type: construction
  name: "направлений вимикач світла"
  id: LightSwitchDirectionalRecipe
  graph: LightSwitchDirectionalGraph
  startNode: start
  targetNode: LightSwitchDirectionalNode
  category: construction-category-machines
  description: "Вимикач для ввімкнення/вимкнення світла, підключеного до того ж АПЦ."
  icon:
    sprite: Structures/Wallmounts/switch.rsi
    state: on
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: true
  canBuildInImpassable: true
  conditions:
    - !type:WallmountCondition

- type: construction
  name: "направлений сигнальний вимикач"
  id: SignalSwitchDirectionalRecipe
  graph: SignalSwitchDirectionalGraph
  startNode: start
  targetNode: SignalSwitchDirectionalNode
  category: construction-category-machines
  description: "Це вимикач для ввімкнення/вимкнення живлення."
  icon:
    sprite: Structures/Wallmounts/switch.rsi
    state: on
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: true
  canBuildInImpassable: true
  conditions:
    - !type:WallmountCondition

- type: construction
  name: "направлена сигнальна кнопка"
  id: SignalButtonDirectionalRecipe
  graph: SignalButtonDirectionalGraph
  startNode: start
  targetNode: SignalButtonDirectionalNode
  category: construction-category-machines
  description: "Це кнопка для активації чогось."
  icon:
    sprite: Structures/Wallmounts/switch.rsi
    state: on
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: true
  canBuildInImpassable: true
  conditions:
    - !type:WallmountCondition
