# EnergyGunComponent

# Hardlight Bow
mode-selected = Selected {$mode}

# Hardlight Bow
ammo-selector-examine-mode = Current mode: {$mode}
mode-select-verb-text = Select firing mode

# RequiresDualWieldComponent
dual-wield-component-requires = That doesn't feel cool enough, you need to dual wield.
gun-requires-dual-wield-component-examine = This weapon needs to be dual wielded with another of its kind in order to be fired.
