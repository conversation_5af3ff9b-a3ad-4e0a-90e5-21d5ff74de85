- type: entity
  name: "зелена паличка"
  parent: BaseItem
  id: GlowstickBase
  description: "Стане в нагоді для рейвів та екстрених ситуацій."
  components:
    - type: SpaceGarbage
    - type: ExpendableLight
      spentName: expendable-light-spent-green-glowstick-name
      spentDesc: expendable-light-spent-glowstick-desc
      glowDuration: 900 # time in seconds
      glowColorLit: "#00FF00"
      fadeOutDuration: 300
      turnOnBehaviourID: turn_on
      fadeOutBehaviourID: fade_out
      iconStateLit: glowstick_lit
      iconStateSpent: glowstick_unlit
      litSound:
        path: /Audio/Items/Handcuffs/rope_breakout.ogg
    - type: Sprite
      sprite: Objects/Misc/glowstick.rsi
      layers:
      - map: [ enum.ExpendableLightVisualLayers.Base ]
        state: glowstick_base
      - map: [ enum.ExpendableLightVisualLayers.Glow ]
        state: glowstick_glow
        visible: false
        shader: unshaded
      - map: [ enum.ExpendableLightVisualLayers.Overlay ]
        state: glowstick_unlit
    - type: Item
      sprite: Objects/Misc/glowstick.rsi
      heldPrefix: unlit
    - type: Appearance
    - type: PointLight
      enabled: false
      color: "#00FF00"
      radius: 5
      energy: 0
      netsync: false
    - type: LightBehaviour
      behaviours:
        - !type:FadeBehaviour # slowly fade in once activated
          id: turn_on
          interpolate: Linear
          maxDuration: 10.0
          startValue: 0.0
          endValue: 3.0
          property: Energy
        - !type:FadeBehaviour # fade out energy as it burns out
          id: fade_out
          interpolate: Linear
          maxDuration: 10 # 300.0
          startValue: 3.0
          endValue: 0.2
          property: Energy
        - !type:FadeBehaviour # fade out radius as it burns out
          id: fade_out
          interpolate: Linear
          maxDuration: 10 # 300.0
          startValue: 5.0
          endValue: 1.5
    - type: Tag
      tags:
      - Trash

- type: entity
  name: "червона паличка"
  parent: GlowstickBase
  id: GlowstickRed
  components:
    - type: ExpendableLight
      spentName: expendable-light-spent-red-glowstick-name
      glowColorLit: "#FF0000"
    - type: PointLight
      color: "#FF0000"

- type: entity
  name: "фіолетова паличка"
  parent: GlowstickBase
  id: GlowstickPurple
  components:
    - type: ExpendableLight
      spentName: expendable-light-spent-purple-glowstick-name
      glowColorLit: "#FF00FF"
    - type: PointLight
      color: "#FF00FF"

- type: entity
  name: "жовта паличка"
  parent: GlowstickBase
  id: GlowstickYellow
  components:
    - type: ExpendableLight
      spentName: expendable-light-spent-yellow-glowstick-name
      glowColorLit: "#FFFF00"
    - type: PointLight
      color: "#FFFF00"

- type: entity
  name: "блакитна паличка"
  parent: GlowstickBase
  id: GlowstickBlue
  components:
    - type: ExpendableLight
      spentName: expendable-light-spent-blue-glowstick-name
      glowColorLit: "#0000FF"
    - type: PointLight
      color: "#0000FF"

# ----------------------------------------------------------------------------
# THE FOLLOWING ARE ALL DUMMY ENTITIES USED TO TEST THE LIGHT BEHAVIOUR SYSTEM
# ----------------------------------------------------------------------------
- type: entity
  name: "тест світловим імпульсом"
  parent: BaseItem
  id: LightBehaviourTest1
  categories: [ HideSpawnMenu ]
  components:
    - type: Sprite
      sprite: Objects/Misc/glowstick.rsi
      layers:
        - state: glowstick_base
        - state: glowstick_unlit
          shader: unshaded
          color: "#FF0000"
    - type: Item
      sprite: Objects/Misc/glowstick.rsi
      heldPrefix: unlit
    - type: PointLight
      enabled: true
      color: "#FF0000"
      radius: 5
    - type: LightBehaviour
      behaviours:
        - !type:PulseBehaviour
          interpolate: Cubic
          maxDuration: 10.0
          minValue: 1.0
          maxValue: 7.0
          isLooped: true
          property: Energy
          enabled: true

- type: entity
  name: "тест кольорового циклу"
  parent: BaseItem
  id: LightBehaviourTest2
  categories: [ HideSpawnMenu ]
  components:
    - type: Sprite
      sprite: Objects/Misc/glowstick.rsi
      layers:
        - state: glowstick_base
        - state: glowstick_unlit
          shader: unshaded
          color: "#FF0000"
    - type: Item
      sprite: Objects/Misc/glowstick.rsi
      heldPrefix: unlit
    - type: PointLight
      enabled: true
      color: "#FF0000"
      radius: 5
    - type: LightBehaviour
      behaviours:
        - !type:ColorCycleBehaviour
          interpolate: Nearest
          maxDuration: 0.8
          isLooped: true
          enabled: true
          colors:
            - red
            - blue
            - green

- type: entity
  name: "мультиповедінковий світловий тест"
  parent: BaseItem
  id: LightBehaviourTest3
  categories: [ HideSpawnMenu ]
  components:
    - type: Sprite
      sprite: Objects/Misc/glowstick.rsi
      layers:
        - state: glowstick_base
        - state: glowstick_unlit
          shader: unshaded
          color: "#FF0000"
    - type: Item
      sprite: Objects/Misc/glowstick.rsi
      heldPrefix: unlit
    - type: PointLight
      enabled: false
      color: "#FF0000"
      radius: 5
    - type: LightBehaviour
      behaviours:
        - !type:PulseBehaviour
          interpolate: Nearest
          minDuration: 0.2
          maxDuration: 1.0
          maxValue: 0.2
          property: AnimatedEnable
          isLooped: true
          enabled: true
        - !type:ColorCycleBehaviour
          interpolate: Cubic
          maxDuration: 0.8
          isLooped: true
          enabled: true
          colors:
            - red
            - blue
            - green

- type: entity
  name: "згасання світла в тесті"
  parent: BaseItem
  id: LightBehaviourTest4
  categories: [ HideSpawnMenu ]
  components:
    - type: Sprite
      sprite: Objects/Misc/glowstick.rsi
      layers:
        - state: glowstick_base
        - state: glowstick_unlit
          shader: unshaded
          color: "#FF0000"
    - type: Item
      sprite: Objects/Misc/glowstick.rsi
      heldPrefix: unlit
    - type: PointLight
      enabled: false
      color: "#FF0000"
      radius: 5
    - type: LightBehaviour
      behaviours:
        - !type:FadeBehaviour
          interpolate: Cubic
          maxDuration: 5.0
          minValue: 0.0
          maxValue: 10.0
          isLooped: true
          property: Energy
          enabled: true

- type: entity
  name: "тест на радіус дії світлового імпульсу"
  parent: BaseItem
  id: LightBehaviourTest5
  categories: [ HideSpawnMenu ]
  components:
    - type: Sprite
      sprite: Objects/Misc/glowstick.rsi
      layers:
        - state: glowstick_base
        - state: glowstick_unlit
          shader: unshaded
          color: "#FF0000"
    - type: Item
      sprite: Objects/Misc/glowstick.rsi
      heldPrefix: unlit
    - type: PointLight
      enabled: false
      color: "#FF0000"
      radius: 5
    - type: LightBehaviour
      behaviours:
        - !type:PulseBehaviour
          interpolate: Cubic
          minDuration: 1.0
          maxDuration: 5.0
          minValue: 2.0
          maxValue: 10.0
          isLooped: true
          enabled: true

- type: entity
  name: "тест на рандомізацію радіусу дії світла"
  parent: BaseItem
  id: LightBehaviourTest6
  categories: [ HideSpawnMenu ]
  components:
    - type: Sprite
      sprite: Objects/Misc/glowstick.rsi
      layers:
        - state: glowstick_base
        - state: glowstick_unlit
          shader: unshaded
          color: "#FF0000"
    - type: Item
      sprite: Objects/Misc/glowstick.rsi
      heldPrefix: unlit
    - type: PointLight
      enabled: false
      color: "#FF0000"
      radius: 5
      energy: 10
    - type: LightBehaviour
      behaviours:
        - !type:RandomizeBehaviour
          interpolate: Nearest
          maxDuration: 0.5
          minValue: 10.0
          maxValue: 25.0
          isLooped: true
          enabled: true
