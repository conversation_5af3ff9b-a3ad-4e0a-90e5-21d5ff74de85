- type: entity
  id: BaseStructure
  abstract: true
  placement:
    mode: SnapgridCenter
  components:
  - type: Transform
    anchored: true
  - type: Clickable
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.45"
        density: 60
        mask:
        - MachineMask
        layer:
        - MidImpassable
        - LowImpassable
  - type: Pullable
  - type: Tag
    tags:
      - Structure
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 9
    staminaCost: 50
    soundHit:
      collection: MetalThud

- type: entity
  # This means that it's not anchored on spawn.
  id: BaseStructureDynamic
  parent: BaseStructure
  abstract: true
  components:
  - type: MovedByPressure
  - type: Transform
    anchored: false
  - type: Clickable
  - type: Physics
    bodyType: Dynamic
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.45"
        density: 60
        mask:
        - MachineMask
        layer:
        - MidImpassable
        - LowImpassable
  - type: Anchorable

# For use with yaml composition, so that all wheeled structures can easily be changed.
- type: entity
  id: StructureWheeled
  abstract: true
  components:
    - type: TileFrictionModifier
      modifier: 0.4

- type: Tag
  id: Structure
