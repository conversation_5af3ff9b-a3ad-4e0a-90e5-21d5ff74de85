using Content.Shared.DeltaV.CartridgeLoader.Cartridges;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.DeltaV.CartridgeLoader.Cartridges;

[GenerateTypedNameReferences]
public sealed partial class NanoChatEntry : BoxContainer
{
    private const int MaxNameLength = 14;
    private const int MaxJobLength = 20;

    public event Action<uint>? OnPressed;
    private uint _number;
    private Action<EventArgs>? _pressHandler;

    public NanoChatEntry()
    {
        RobustXamlLoader.Load(this);
    }

    public void SetRecipient(NanoChatRecipient recipient, uint number, bool isSelected)
    {
        // Remove old handler if it exists
        if (_pressHandler != null)
            ChatButton.OnPressed -= _pressHandler;

        _number = number;

        // Create and store new handler
        _pressHandler = _ => OnPressed?.Invoke(_number);
        ChatButton.OnPressed += _pressHandler;

        NameLabel.Text = Truncate(recipient.Name, MaxNameLength);
        JobLabel.Text = Truncate(recipient.JobTitle ?? "", MaxJobLength);
        JobLabel.Visible = !string.IsNullOrEmpty(recipient.JobTitle);
        UnreadIndicator.Visible = recipient.HasUnread;

        ChatButton.ModulateSelfOverride = isSelected ? NanoChatMessageBubble.OwnMessageColor : null;
    }

    /// <summary>
    ///     Truncates a string to a maximum length.
    /// </summary>
    private static string Truncate(string text, int maxLength) =>
        text.Length <= maxLength
            ? text
            : text[..(maxLength - 3)] + "...";
}
