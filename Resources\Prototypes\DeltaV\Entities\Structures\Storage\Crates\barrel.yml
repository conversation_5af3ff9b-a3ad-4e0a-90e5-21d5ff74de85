- type: entity
  parent: CrateGeneric
  id: WoodenBarrel
  name: "дерев'яна бочка"
  description: "Стара запліснявіла дерев'яна бочка."
  components:
  - type: Sprite
    sprite: DeltaV/Structures/Storage/barrel.rsi
    layers:
    - state: base
      map: ["enum.StorageVisualLayers.Base"]
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
  - type: Icon
    sprite: DeltaV/Structures/Storage/barrel.rsi
    state: base
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: Wood
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 30
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank1:
            min: 1
            max: 4
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: WoodenBarrel
    node: woodenbarrel
    containers:
    - entity_storage
  - type: StaticPrice
    price: 50
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.2,-0.3,0.2,0.4"
        density: 125
        mask:
        - SmallMobMask #this is so they can go under plastic flaps
        layer:
        - MachineLayer
  - type: Climbable

- type: entity
  parent: BaseStructureDynamic
  id: WoodenKeg
  name: "дерев'яна бочка"
  description: "Стара запліснявіла дерев'яна бочка з краном, прикріпленим спереду."
  components:
  - type: SolutionContainerManager
    solutions:
      tank:
        maxVol: 500
  - type: DrainableSolution
    solution: tank
  - type: ExaminableSolution
    solution: tank
  - type: UserInterface
    interfaces:
        enum.TransferAmountUiKey.Key:
          type: TransferAmountBoundUserInterface
  - type: DrawableSolution
    solution: tank
  - type: InjectableSolution
    solution: tank
  - type: Drink
    solution: tank
  - type: Spillable
    solution: tank
  - type: DumpableSolution
    solution: tank
  - type: Sprite
    sprite: DeltaV/Structures/Storage/keg.rsi
    layers:
    - state: base
      map: ["enum.StorageVisualLayers.Base"]
  - type: Icon
    sprite: DeltaV/Structures/Storage/keg.rsi
    state: base
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: Wood
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 30
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpillBehavior
        solution: tank
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank1:
            min: 1
            max: 4
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: StaticPrice
    price: 50
  - type: Construction
    graph: WoodenKeg
    node: woodenkeg
    containers:
    - entity_storage
  - type: InteractionOutline
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.2,-0.3,0.2,0.4"
        density: 125
        mask:
        - SmallMobMask #this is so they can go under plastic flaps
        layer:
        - MachineLayer
  - type: Transform
    noRot: true

- type: entity
  parent: WoodenKeg
  name: "бочка з-під кореневого пива"
  description: "Стара запліснявіла дерев'яна бочка з краном, прикріпленим спереду. Пахне рутбіром."
  id: WoodenKegRootBeer
  components:
    - type: Label
      currentLabel: reagent-name-root-beer
    - type: SolutionContainerManager
      solutions:
        tank:
          reagents:
            - ReagentId: RootBeer
              Quantity: 500

- type: entity
  parent: WoodenKeg
  name: "пивна бочка"
  description: "Стара запліснявіла дерев'яна бочка, з прикріпленим спереду краном. Пахне пивом."
  id: WoodenKegBeer
  components:
    - type: Label
      currentLabel: reagent-name-beer
    - type: SolutionContainerManager
      solutions:
        tank:
          reagents:
            - ReagentId: Beer
              Quantity: 500

- type: entity
  parent: WoodenKeg
  name: "винна бочка"
  description: "Стара запліснявіла дерев'яна бочка, з прикріпленим спереду краном. Пахне вином."
  id: WoodenKegWine
  components:
    - type: Label
      currentLabel: reagent-name-wine
    - type: SolutionContainerManager
      solutions:
        tank:
          reagents:
            - ReagentId: Wine
              Quantity: 500
