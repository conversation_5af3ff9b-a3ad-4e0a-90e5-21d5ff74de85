- type: entity
  abstract: true
  parent: [ BaseStructureDynamic, SmallConstructibleMachine ]
  id: SurveillanceWirelessCameraBase
  name: "бездротова камера"
  description: "Камера. Вона стежить за тобою. Типу того."
  components:
    - type: InteractionOutline
    - type: Eye
    - type: WirelessNetworkConnection
      range: 200
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: Metallic
    - type: Rotatable
      rotateWhileAnchored: true
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeCircle
            radius: 0.45
          density: 80
          mask:
            - MachineMask
          layer:
            - BulletImpassable
    - type: SurveillanceCameraMicrophone
      blacklist:
        components:
          - SurveillanceCamera
          - SurveillanceCameraMonitor
          - RadioSpeaker
    - type: ActiveListener
      range: 10
    - type: UserInterface
      interfaces:
        enum.SurveillanceCameraSetupUiKey.Camera:
          type: SurveillanceCameraSetupBoundUi
  placement:
    mode: SnapgridCenter

- type: entity
  abstract: true
  id: SurveillanceWirelessCameraAnchoredBase
  parent: SurveillanceWirelessCameraBase
  suffix: Anchored
  components:
    - type: Machine
      board: SurveillanceWirelessCameraAnchoredCircuitboard
    - type: Anchorable
    - type: Transform
      anchored: true
    - type: Physics
      bodyType: Static
    - type: Sprite
      noRot: true
      sprite: Structures/monitors.rsi
      layers:
        - map: [ "enum.SurveillanceCameraVisualsKey.Key" ]
          state: television

- type: entity
  abstract: true
  id: SurveillanceWirelessCameraMovableBase
  parent: SurveillanceWirelessCameraBase
  suffix: Movable
  components:
    - type: Machine
      board: SurveillanceWirelessCameraMovableCircuitboard
    - type: Transform
    - type: Sprite
      noRot: true
      sprite: Structures/monitors.rsi
      layers:
        - map: [ "enum.SurveillanceCameraVisualsKey.Key" ]
          state: mobilevision

- type: entity
  parent: SurveillanceWirelessCameraAnchoredBase
  suffix: Constructed, Anchored
  id: SurveillanceWirelessCameraAnchoredConstructed
  components:
    - type: DeviceNetwork
      deviceNetId: Wireless
      receiveFrequencyId: SurveillanceCamera
      transmitFrequencyId: SurveillanceCamera
    - type: SurveillanceCamera
      setupAvailableNetworks:
        - SurveillanceCameraEntertainment

- type: entity
  parent: SurveillanceWirelessCameraMovableBase
  suffix: Constructed, Movable
  id: SurveillanceWirelessCameraMovableConstructed
  components:
    - type: DeviceNetwork
      deviceNetId: Wireless
      receiveFrequencyId: SurveillanceCameraEntertainment
      transmitFrequencyId: SurveillanceCamera
    - type: SurveillanceCamera
      setupAvailableNetworks:
        - SurveillanceCameraEntertainment

- type: entity
  parent: SurveillanceWirelessCameraAnchoredBase
  suffix: Entertainment, Anchored
  id: SurveillanceWirelessCameraAnchoredEntertainment
  components:
    - type: DeviceNetwork
      deviceNetId: Wireless
      receiveFrequencyId: SurveillanceCameraEntertainment
      transmitFrequencyId: SurveillanceCamera
    - type: SurveillanceCamera
      networkSet: true

- type: entity
  parent: SurveillanceWirelessCameraMovableBase
  suffix: Entertainment, Movable
  id: SurveillanceWirelessCameraMovableEntertainment
  components:
    - type: DeviceNetwork
      deviceNetId: Wireless
      receiveFrequencyId: SurveillanceCameraEntertainment
      transmitFrequencyId: SurveillanceCamera
    - type: SurveillanceCamera
      networkSet: true
#PIRATE START UNTIL END OF FILE
- type: entity
  name: "камера журналіста"
  parent: Clothing
  id: JournalistCamera
  description: "Фотографія скаже більше, ніж тисяча слів. Поставляється з ультраяскравим спалахом і внутрішнім фоторулоном, тому переконайтеся, що кожен знімок має значення!"
  components:
    - type: Sprite
      sprite: _Pirate/Objects/Devices/camera.rsi
      layers:
      - state: icon
    - type: Flash
      aoeFlashDuration: 100
      # stunTime: 3000
    - type: LimitedCharges
      maxCharges: 4
      charges: 4
    - type: MeleeWeapon
      wideAnimationRotation: 180
      damage:
        types:
          Blunt: 0 # melee weapon to allow flashing individual targets
      angle: 10
    - type: Item
      size: Small
      sprite: _Pirate/Objects/Devices/camera.rsi
    - type: UseDelay
    - type: StaticPrice
      price: 100
    - type: Clothing
      slots:
      - neck
      - neck1
      - neck2
      sprite: _Pirate/Objects/Devices/camera.rsi
    - type: Tag
      tags:
      - WhitelistChameleon
    - type: SurveillanceCameraMicrophone
      blacklist:
        components:
          - SurveillanceCamera
          - SurveillanceCameraMonitor
          - RadioSpeaker
    - type: ActiveListener
      range: 10
    - type: UserInterface
      interfaces:
          enum.SurveillanceCameraSetupUiKey.Camera:
            type: SurveillanceCameraSetupBoundUi
    - type: InteractionOutline
    - type: Eye
    - type: WirelessNetworkConnection
      range: 200
    - type: DeviceNetwork
      deviceNetId: Wireless
      receiveFrequencyId: SurveillanceCameraEntertainment
      transmitFrequencyId: SurveillanceCamera
    - type: SurveillanceCamera
      setupAvailableNetworks:
        - SurveillanceCameraEntertainment
