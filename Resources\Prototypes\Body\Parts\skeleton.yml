# TODO BODY: Part damage
- type: entity
  id: PartSkeleton
  parent: BaseItem
  name: "частина тіла скелета"
  abstract: true
  components:
  - type: Damageable
    damageContainer: OrganicPart # Shitmed Change
  - type: BodyPart
  - type: ContainerContainer
    containers:
      bodypart: !type:Container
        ents: []
  - type: StaticPrice
    price: 20
  - type: Gibbable
  - type: Tag
    tags:
      - Trash
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTypeTrigger
        damageType: Blunt
        damage: 50
      behaviors:
      - !type:GibPartBehavior { }
    - trigger:
        !type:DamageTypeTrigger
        damageType: Slash
        damage: 100
      behaviors:
      - !type:Gib<PERSON>artBehavior { }
    - trigger:
        !type:DamageTypeTrigger
        damageType: Heat
        damage: 200
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawnInContainer: true
        spawn:
          Ash:
            min: 1
            max: 1
      - !type:BurnBodyBehavior { }
      - !type:PlaySoundBehavior
        sound:
          collection: MeatLaserImpact

- type: entity
  id: TorsoSkeleton
  name: "торс скелета"
  parent: PartSkeleton
  components:
    - type: Sprite
      sprite: Mobs/Species/Skeleton/parts.rsi
      state: "torso_m"
    - type: Icon
      sprite: Mobs/Species/Skeleton/parts.rsi
      state: "torso_m"
    - type: BodyPart
      partType: Torso

- type: entity
  id: HeadSkeleton
  name: "череп"
  description: "Ех, бідний Йорик..."
  parent: PartSkeleton
  components:
    - type: Sprite
      sprite: Mobs/Species/Skeleton/parts.rsi
      state: "skull_icon"
      scale: 0.5, 0.5 # DeltaV - Scale down sprite because it looks too big
    - type: Icon
      sprite: Mobs/Species/Skeleton/parts.rsi
      state: "skull_icon"
    - type: BodyPart
      partType: Head
    - type: Input
      context: "human"
    - type: Speech
      speechVerb: Skeleton
    - type: SkeletonAccent
    - type: Actions
    - type: Vocal
      sounds:
        Male: Skeleton
        Female: Skeleton
        Unsexed: Skeleton
    - type: Emoting
    - type: Grammar
      attributes:
        proper: true
    - type: Examiner
    - type: DoAfter
    - type: MobState
      allowedStates:
        - Alive
    - type: Tag
      tags:
        - MindTransferTarget
        - Head

- type: entity
  id: LeftArmSkeleton
  name: "ліва рука скелета"
  parent: PartSkeleton
  components:
    - type: Sprite
      sprite: Mobs/Species/Skeleton/parts.rsi
      state: "l_arm"
    - type: Icon
      sprite: Mobs/Species/Skeleton/parts.rsi
      state: "l_arm"
    - type: BodyPart
      partType: Arm
      symmetry: Left

- type: entity
  id: RightArmSkeleton
  name: "права рука скелета"
  parent: PartSkeleton
  components:
    - type: Sprite
      sprite: Mobs/Species/Skeleton/parts.rsi
      state: "r_arm"
    - type: Icon
      sprite: Mobs/Species/Skeleton/parts.rsi
      state: "r_arm"
    - type: BodyPart
      partType: Arm
      symmetry: Right

- type: entity
  id: LeftHandSkeleton
  name: "ліва долоня скелета"
  parent: PartSkeleton
  components:
    - type: Sprite
      sprite: Mobs/Species/Skeleton/parts.rsi
      state: "l_hand"
    - type: Icon
      sprite: Mobs/Species/Skeleton/parts.rsi
      state: "l_hand"
    - type: BodyPart
      partType: Hand
      symmetry: Left

- type: entity
  id: RightHandSkeleton
  name: "права долоня скелета"
  parent: PartSkeleton
  components:
    - type: Sprite
      sprite: Mobs/Species/Skeleton/parts.rsi
      state: "r_hand"
    - type: Icon
      sprite: Mobs/Species/Skeleton/parts.rsi
      state: "r_hand"
    - type: BodyPart
      partType: Hand
      symmetry: Right

- type: entity
  id: LeftLegSkeleton
  name: "ліва нога скелета"
  parent: PartSkeleton
  components:
    - type: Sprite
      sprite: Mobs/Species/Skeleton/parts.rsi
      state: "l_leg"
    - type: Icon
      sprite: Mobs/Species/Skeleton/parts.rsi
      state: "l_leg"
    - type: BodyPart
      partType: Leg
      symmetry: Left
    - type: MovementBodyPart

- type: entity
  id: RightLegSkeleton
  name: "права нога скелета"
  parent: PartSkeleton
  components:
    - type: Sprite
      sprite: Mobs/Species/Skeleton/parts.rsi
      state: "r_leg"
    - type: Icon
      sprite: Mobs/Species/Skeleton/parts.rsi
      state: "r_leg"
    - type: BodyPart
      partType: Leg
      symmetry: Right
    - type: MovementBodyPart

- type: entity
  id: LeftFootSkeleton
  name: "ліва стопа скелета"
  parent: PartSkeleton
  components:
    - type: Sprite
      sprite: Mobs/Species/Skeleton/parts.rsi
      state: "l_foot"
    - type: Icon
      sprite: Mobs/Species/Skeleton/parts.rsi
      state: "l_foot"
    - type: BodyPart
      partType: Foot
      symmetry: Left

- type: entity
  id: RightFootSkeleton
  name: "права стопа скелета"
  parent: PartSkeleton
  components:
    - type: Sprite
      sprite: Mobs/Species/Skeleton/parts.rsi
      state: "r_foot"
    - type: Icon
      sprite: Mobs/Species/Skeleton/parts.rsi
      state: "r_foot"
    - type: BodyPart
      partType: Foot
      symmetry: Right
