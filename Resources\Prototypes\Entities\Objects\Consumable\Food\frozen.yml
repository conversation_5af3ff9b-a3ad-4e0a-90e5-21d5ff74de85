# When adding new food also add to random spawner located in Resources\Prototypes\Entities\Markers\Spawners\Random\Food_Drinks\food_snack.yml
# Base

- type: entity
  parent: FoodInjectableBase
  id: FoodFrozenBase
  abstract: true
  components:
  - type: Food
  - type: Sprite
    sprite: Objects/Consumable/Food/frozen.rsi
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20 # For sprinkles or something? Idk.
        reagents:
        - ReagentId: Nutriment
          Quantity: 10

# Ice-cream

- type: entity
  name: "бутерброд з морозивом"
  parent: FoodFrozenBase
  id: FoodFrozenSandwich
  description: "Портативне морозиво у власній упаковці."
  components:
  - type: Sprite
    state: sandwich

- type: entity
  name: "бутерброд з полуничним морозивом"
  parent: FoodFrozenBase
  id: FoodFrozenSandwichStrawberry
  description: "Порційне морозиво у власній упаковці полуничного сорту."
  components:
  - type: Sprite
    state: sandwich-strawberry

- type: entity
  name: "заморожування простору"
  parent: FoodFrozenBase
  id: FoodFrozenFreezy
  description: "Найкраще морозиво в космосі."
  components:
  - type: Sprite
    state: spacefreezy

- type: entity
  name: "морозиво сандвіч"
  parent: FoodFrozenBase
  id: FoodFrozenSundae
  description: "Класичний десерт."
  components:
  - type: Sprite
    state: spacefreezy

- type: entity
  name: "корнуто"
  parent: FoodFrozenBase
  id: FoodFrozenCornuto
  description: "Неаполітанський ріжок ванільно-шоколадного морозива. Погрожує посипкою з карамелізованих горіхів."
  components:
  - type: Sprite
    state: cornuto
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Theobromine
          Quantity: 1

# Popsicle

- type: entity
  name: "апельсинове морозиво"
  parent: FoodFrozenBase
  id: FoodFrozenPopsicleOrange
  description: "Класичне апельсинове морозиво. Сонячні заморожені ласощі."
  components:
  - type: Sprite
    layers:
    - state: stick
    - state: popsicle-base
    - state: popsicle
      color: orange
  - type: Food
    trash: 
    - FoodFrozenPopsicleTrash

- type: entity
  name: "ягідне морозиво"
  parent: FoodFrozenBase
  id: FoodFrozenPopsicleBerry
  description: "Яскраве ягідне морозиво. Ягідні ласощі в замороженому вигляді."
  components:
  - type: Sprite
    layers:
    - state: stick
    - state: popsicle-base
    - state: popsicle
      color: red
  - type: Food
    trash: 
    - FoodFrozenPopsicleTrash
  - type: Tag
    tags:
    - Fruit

- type: entity
  name: "гігантське морозиво"
  parent: FoodFrozenBase
  id: FoodFrozenPopsicleJumbo
  description: "Розкішне морозиво, вкрите насиченим шоколадом. Воно менше, ніж ви пам'ятаєте."
  components:
  - type: Sprite
    layers:
    - state: stick
    - state: jumbo
  - type: Food
    trash: 
    - FoodFrozenPopsicleTrash
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 30
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Theobromine
          Quantity: 1

# Snowcone

# Use this one as a base for other colored snowcones. Apply a color to the alpha
# layer depending on soda/syrup/waterever. Name and description will also have
# to be modified.
- type: entity
  name: "солодкий сніжок"
  #  name: [Flavor] snowcone
  parent: FoodFrozenBase
  id: FoodFrozenSnowconeBase
  description: "Лише колотий лід і простий сироп, мінімум зусиль."
  #  description: [Liquid] drizzled over a snowball in a paper cup.
  components:
    - type: Sprite
      layers:
        - state: cone
        - state: alpha-filling
    #      color: foo
    - type: Food
      trash: 
      - FoodFrozenSnowconeTrash
    - type: SolutionContainerManager
      solutions:
        food:
          maxVol: 20 # For sprinkles or something? Idk.
          reagents:
            - ReagentId: Ice
              Quantity: 8
            - ReagentId: Water
              Quantity: 2
            - ReagentId: Sugar
              Quantity: 2

- type: entity
  name: "сніжок без смаку"
  parent: FoodFrozenSnowconeBase
  id: FoodFrozenSnowcone
  description: "Це просто колотий лід. Все одно весело жувати."
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
          - ReagentId: Ice
            Quantity: 8

- type: entity
  name: "ягідний сніжок"
  parent: FoodFrozenSnowconeBase
  id: FoodFrozenSnowconeBerry
  description: "Ягідним сиропом поливаємо сніжок у паперовому стаканчику."
  components:
  - type: Sprite
    layers:
    - state: cone
    - state: berry
  - type: Food
    trash: 
    - FoodFrozenSnowconeTrash
  - type: Tag
    tags:
    - Fruit

- type: entity
  name: "фруктовий салат сніжок"
  parent: FoodFrozenSnowconeBase
  id: FoodFrozenSnowconeFruit
  description: "Чудова суміш цитрусових сиропів поливає сніжок у паперовому стаканчику."
  components:
  - type: Sprite
    layers:
    - state: cone
    - state: fruitsalad
  - type: Food
    trash: 
    - FoodFrozenSnowconeTrash
  - type: Tag
    tags:
    - Fruit

- type: entity
  name: "клоунський конус"
  parent: FoodFrozenSnowconeBase
  id: FoodFrozenSnowconeClown
  description: "Сміх розливався над сніжкою в паперовому стаканчику."
  components:
  - type: Sprite
    layers:
    - state: cone
    - state: clown
  - type: Food
    trash: 
    - FoodFrozenSnowconeTrash

- type: entity
  name: "мім-сніжок"
  parent: FoodFrozenSnowconeBase
  id: FoodFrozenSnowconeMime
  description: "..."
  components:
  - type: Sprite
    layers:
    - state: cone
    - state: mime
  - type: Food
    trash: 
    - FoodFrozenSnowconeTrash

- type: entity
  name: "райдужний сніжок"
  parent: FoodFrozenSnowconeBase
  id: FoodFrozenSnowconeRainbow
  description: "Дуже барвистий сніжок у паперовому стаканчику."
  components:
  - type: Sprite
    layers:
    - state: cone
    - state: rainbow
  - type: Food
    trash: 
    - FoodFrozenSnowconeTrash

# Trash

- type: entity
  name: "паперовий конус"
  parent: BaseItem
  id: FoodFrozenSnowconeTrash
  description: "Зім'ятий паперовий конус, що використовується для льодяників. Нічого не вартий."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/frozen.rsi
    state: cone-trash
  - type: Tag
    tags:
    - Trash

- type: entity
  name: "паличка для ескімо"
  parent: BaseItem
  id: FoodFrozenPopsicleTrash
  description: "Колись тут було дуже смачно. Тепер це пустка."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Food/frozen.rsi
    layers:
      - state: stick
        map: [ "enum.DamageStateVisualLayers.Base" ]
  - type: RandomSprite
    available:
      - enum.DamageStateVisualLayers.Base:
          stick: ""
          stick2: ""
  - type: Tag
    tags:
    - Trash
  - type: SpaceGarbage
