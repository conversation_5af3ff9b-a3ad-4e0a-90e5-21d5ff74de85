- type: constructionGraph
  id: WeldBot
  start: start
  graph:
  - node: start
    edges:
    - to: bot
      steps:
      - tag: ProximitySensor
        icon:
          sprite: Objects/Misc/proximity_sensor.rsi
          state: icon
        name: proximity sensor
        doAfter: 2
      - tag: WeldingMask
        icon:
          sprite: Clothing/Head/Welding/welding.rsi
          state: icon
        name: welding mask
        doAfter: 2
      - tag: WeldingTool
        icon:
          sprite: Objects/Tools/welder.rsi
          state: icon
        name: welding tool
        doAfter: 2
  - node: bot
    entity: MobWeldbot
