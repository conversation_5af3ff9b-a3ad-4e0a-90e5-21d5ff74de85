using System;
using System.Collections.Generic;
using System.Globalization;
using Content.Shared.Atmos.Prototypes;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Localization;

namespace Content.Client.Atmos.UI
{
    /// <summary>
    /// Client-side UI used to control a gas filter.
    /// </summary>
    [GenerateTypedNameReferences]
    public sealed partial class GasFilterWindow : DefaultWindow
    {
        private readonly ButtonGroup _buttonGroup = new();

        public bool FilterStatus = true;
        public string? SelectedGas;
        public string? CurrentGasId;

        public event Action? ToggleStatusButtonPressed;
        public event Action<string>? FilterTransferRateChanged;
        public event Action? SelectGasPressed;

        public GasFilterWindow()
        {
            RobustXamlLoader.Load(this);

            ToggleStatusButton.OnPressed += _ => SetFilterStatus(!FilterStatus);
            ToggleStatusButton.OnPressed += _ => ToggleStatusButtonPressed?.Invoke();

            FilterTransferRateInput.OnTextChanged += _ => SetFilterRate.Disabled = false;
            SetFilterRate.OnPressed += _ =>
            {
                FilterTransferRateChanged?.Invoke(FilterTransferRateInput.Text);
                SetFilterRate.Disabled = true;
            };

            SelectGasButton.OnPressed += _ => SelectGasPressed?.Invoke();

            GasList.OnItemSelected += GasListOnItemSelected;
            GasList.OnItemDeselected += GasListOnItemDeselected;
        }

        public void SetTransferRate(float rate)
        {
            FilterTransferRateInput.Text = rate.ToString(CultureInfo.CurrentCulture);
        }

        public void SetFilterStatus(bool enabled)
        {
            FilterStatus = enabled;
            if (enabled)
            {
                ToggleStatusButton.Text = Loc.GetString("comp-gas-filter-ui-status-enabled");
            }
            else
            {
                ToggleStatusButton.Text = Loc.GetString("comp-gas-filter-ui-status-disabled");
            }
        }

        public void SetGasFiltered(string? id, string name)
        {
            CurrentGasId = id;
            CurrentGasLabel.Text = Loc.GetString("comp-gas-filter-ui-filter-gas-current") + $" {name}";
            GasList.ClearSelected();
            SelectGasButton.Disabled = true;
        }

        public void PopulateGasList(IEnumerable<GasPrototype> gases)
        {
            GasList.Add(new ItemList.Item(GasList)
            {
                Metadata = null,
                Text = Loc.GetString("comp-gas-filter-ui-filter-gas-none")
            });

            foreach (var gas in gases)
            {
                var gasName = Loc.GetString(gas.Name);
                GasList.Add(GetGasItem(gas.ID, gasName, GasList));
            }
        }

        private static ItemList.Item GetGasItem(string id, string name, ItemList itemList)
        {
            return new(itemList)
            {
                Metadata = id,
                Text = name
            };
        }

        private void GasListOnItemSelected(ItemList.ItemListSelectedEventArgs obj)
        {
            SelectedGas = (string) obj.ItemList[obj.ItemIndex].Metadata!;
            if(SelectedGas != CurrentGasId) SelectGasButton.Disabled = false;
        }

        private void GasListOnItemDeselected(ItemList.ItemListDeselectedEventArgs obj)
        {
            SelectedGas = CurrentGasId;
            SelectGasButton.Disabled = true;
        }
    }
}
