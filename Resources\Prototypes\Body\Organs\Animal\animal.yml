﻿- type: entity
  id: BaseAnimalOrganUnGibbable
  parent: BaseItem
  abstract: true
  components:
  - type: Organ
  - type: Food
  - type: Sprite
    sprite: Mobs/Species/Human/organs.rsi
  - type: StaticPrice
    price: 50
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 5
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 5
  - type: FlavorProfile
    flavors:
      - chicken # everything kinda tastes like chicken
  - type: Tag
    tags:
      - Meat
      - Organ # goob edit

- type: entity
  id: BaseAnimalOrgan
  parent: BaseAnimalOrganUnGibbable
  abstract: true
  components:
  - type: Gibbable

- type: entity
  id: OrganAnimalLungs
  parent: BaseAnimalOrgan
  name: "легені"
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    layers:
    - state: lung-l
    - state: lung-r
  - type: Organ
    slotId: lungs # Shitmed
  - type: Lung
  - type: Metabolizer
    removeEmpty: true
    solutionOnBody: false
    solution: "Lung"
    metabolizerTypes: [ Animal ]
    groups:
    - id: Gas
      rateModifier: 100.0
  - type: SolutionContainerManager
    solutions:
      Lung:
        maxVol: 100.0
        canReact: false
      food:
        maxVol: 5
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 5
  - type: Tag # goob edit HERETICS
    tags:
    - Meat
    - Organ
    - Lungs

- type: entity
  id: OrganAnimalStomach
  parent: BaseAnimalOrgan
  name: "шлунок"
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    state: stomach
  - type: Organ
    slotId: stomach # Shitmed
  - type: SolutionContainerManager
    solutions:
      stomach:
        maxVol: 40
      food:
        maxVol: 5
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 5
  - type: Stomach
  - type: Metabolizer
    maxReagents: 3
    metabolizerTypes: [ Animal ]
    groups:
    - id: Food
    - id: Drink
  - type: Tag # goob edit HERETICS
    tags:
    - Meat
    - Organ
    - Stomach

- type: entity
  id: OrganMouseStomach
  parent: OrganAnimalStomach
  name: "шлунок"
  categories: [ HideSpawnMenu ]
  components:
  - type: SolutionContainerManager
    solutions:
      stomach:
        maxVol: 30

- type: entity
  id: OrganAnimalLiver
  parent: BaseAnimalOrgan
  name: "печінка"
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    state: liver
  - type: Organ
    slotId: liver # Shitmed
  - type: Metabolizer
    maxReagents: 1
    metabolizerTypes: [ Animal ]
    groups:
    - id: Alcohol
      rateModifier: 0.1
  - type: Tag # goob edit HERETICS
    tags:
    - Meat
    - Organ
    - Liver
  - type: Liver # Shitmed

- type: entity
  id: OrganAnimalHeart
  parent: BaseAnimalOrgan
  name: "серце"
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    state: heart-on
  - type: Organ
    slotId: heart # Shitmed
  - type: Metabolizer
    maxReagents: 2
    metabolizerTypes: [ Animal ]
    groups:
    - id: Medicine
    - id: Poison
    - id: Narcotic
  - type: Tag # goob edit HERETICS
    tags:
    - Meat
    - Organ
    - Heart
  - type: Heart # Shitmed


- type: entity
  id: OrganAnimalKidneys
  parent: BaseAnimalOrgan
  name: "нирки"
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    layers:
    - state: kidney-l
    - state: kidney-r
  - type: Organ
    slotId: kidneys # Shitmed
  - type: Metabolizer
    maxReagents: 5
    metabolizerTypes: [ Animal ]
    removeEmpty: true
  - type: Tag # goob edit HERETICS
    tags:
    - Meat
    - Organ
    - Kidneys