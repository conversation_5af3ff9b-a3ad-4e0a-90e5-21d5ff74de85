#When adding new hardsuits, please try to keep the organization consistent with hardsuit.yml (if possible.)

#For now, since locational damage is not a thing, all "combat" hardsuits (with the exception of the deathsquad hardsuit) have the equvilent of a helmet in terms of armor.
#This is so people don't need to wear both regular, on-station helmets and hardsuits to get full protection.
#Generally, unless you're adding something like caustic damage, you should probably avoid messing with armor here outside of the above scenario.

#CREW HARDSUITS
#Standard Hardsuit
- type: entity
  parent: ClothingHeadHardsuitBase
  id: ClothingHeadHelmetHardsuitBasic
  name: "базовий шолом захисного костюму"
  description: "Базовий на вигляд шолом для захисного костюма, який забезпечує мінімальний захист від більшості джерел пошкоджень."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/basic.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/basic.rsi
  - type: HideLayerClothing
    slots:
    - Snout

#Atmospherics Hardsuit
- type: entity
  parent: ClothingHeadHardsuitWithLightBase
  id: ClothingHeadHelmetHardsuitAtmos
  name: "шолом скафандра атмосферного техніка"
  description: "Спеціальний шолом захистного скафандру, призначений для роботи в умовах низького тиску та високої температури."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/atmospherics.rsi
    layers:
    - state: icon
    - state: icon-unshaded
      shader: unshaded
    - state: light-overlay
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: HandheldLight
    addPrefix: false
  - type: ToggleableLightVisuals
    spriteLayer: light
    clothingVisuals:
      head:
      - state: equipped-head-light
        shader: unshaded
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-head
      - state: equipped-head-unshaded
        shader: unshaded
  - type: PointLight
    color: "#adffe6"
  - type: PressureProtection
    highPressureMultiplier: 0.08
    lowPressureMultiplier: 1000
  - type: TemperatureProtection
    coefficient: 0.005

#Engineering Hardsuit
- type: entity
  parent: ClothingHeadHelmetHardsuitEngineering
  id: ClothingHeadHelmetHardsuitEngineeringUnpainted
  name: "інженерний захисний шолом"
  description: "Інженерний захисний шолом, призначений для роботи в умовах низького тиску та високої радіоактивності."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/engineering-base.rsi
    layers:
    - state: icon
    - state: icon-unshaded
      shader: unshaded
    - state: light-overlay
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/engineering-base.rsi
    clothingVisuals:
      head:
      - state: equipped-head
      - state: equipped-head-unshaded
        shader: unshaded
  - type: ToggleableLightVisuals
    spriteLayer: light
    clothingVisuals:
      head:
      - state: equipped-head-light
        shader: unshaded

- type: entity
  parent: ClothingHeadHardsuitWithLightBase
  id: ClothingHeadHelmetHardsuitEngineering
  name: "інженерний захисний шолом"
  description: "Інженерний захисний шолом, призначений для роботи в умовах низького тиску та високої радіоактивності."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/engineering.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/engineering.rsi
  - type: PointLight
    color: "#ffdbad"
  - type: PressureProtection
    highPressureMultiplier: 0.1
    lowPressureMultiplier: 1000

#Spationaut Hardsuit
- type: entity
  parent: ClothingHeadHardsuitWithLightBase
  id: ClothingHeadHelmetHardsuitSpatio
  name: "шолом скафандра рятувальника"
  description: "Міцний шолом, призначений для складних промислових операцій у космосі."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/spatiohelm.rsi
    layers:
    - state: icon
    - state: icon-unshaded
      shader: unshaded
    - state: light-overlay
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: HandheldLight
    addPrefix: false
  - type: ToggleableLightVisuals
    clothingVisuals:
      head:
      - state: equipped-head-light
        shader: unshaded
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-head
      - state: equipped-head-unshaded
        shader: unshaded
  - type: PressureProtection
    highPressureMultiplier: 0.72
    lowPressureMultiplier: 1000

#Salvage Hardsuit
- type: entity
  parent: ClothingHeadHardsuitWithLightBase
  id: ClothingHeadHelmetHardsuitSalvage
  name: "шолом скафандра утилізатора"
  description: "Спеціальний шолом, розроблений для роботи в небезпечному середовищі з низьким тиском. Має посилене покриття для захисту від диких тварин та подвійні прожектори."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/salvage.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/salvage.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.525
    lowPressureMultiplier: 1000
  - type: PointLight
    radius: 7
    energy: 3

- type: entity
  parent: ClothingHeadHardsuitBase
  id: ClothingHeadHelmetHardsuitMaxim
  categories: [ HideSpawnMenu ]
  name: "шолом скафандра утилізатора Максима"
  description: "Передчуття занепаду омиває ваш розум."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/maxim.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/maxim.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.08
    lowPressureMultiplier: 1000
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Heat: 0.9

#Paramedic Void Helmet
- type: entity
  parent: ClothingHeadHardsuitBase
  id: ClothingHeadHelmetHardsuitVoidParamed
  name: "войд шолом парамедика"
  description: "Войд шолом для парамедиків."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/paramedhelm.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/paramedhelm.rsi
  - type: Armor
    modifiers:
      coefficients:
        Heat: 0.90
        Radiation: 0.75


#Security Hardsuit
- type: entity
  parent: ClothingHeadHardsuitWithLightBase
  id: ClothingHeadHelmetHardsuitSecurity
  name: "шолом скафандра слуєби безпеки"
  description: "Броньований шолом для потреб безпеки."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/security.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/security.rsi
  - type: PointLight
    color: "#ffeead"
  - type: PressureProtection
    highPressureMultiplier: 0.525
    lowPressureMultiplier: 1000
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Heat: 0.9
  - type: FlashImmunity # Goobstation
  - type: FlashSoundSuppression # Goobstation

#Brigmedic Hardsuit
- type: entity
  parent: ClothingHeadHardsuitWithLightBase
  id: ClothingHeadHelmetHardsuitBrigmedic
  name: "шолом скафандра брігмедика" # DeltaV - rename brigmedic to corpsman
  description: "Легкий шолом санітарного костюма. Захищає від вірусів та клоунів." # Delta V - rename brigmedic to corpsman
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/brigmedic.rsi
  - type: Clothing
    clothingVisuals:
      head:
      - state: off-equipped-HELMET
      - state: off-equipped-HELMET-glow
        shader: unshaded
  - type: ToggleableLightVisuals
    spriteLayer: light
    clothingVisuals:
      head:
      - state: on-equipped-HELMET
      - state: on-equipped-HELMET-glow
        shader: unshaded
    sprite: Clothing/Head/Hardsuits/brigmedic.rsi
  - type: PointLight
    color: "#00FFFF"
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.95
        Slash: 0.95
        Piercing: 0.95
        Heat: 0.95
        Radiation: 0.90
        Caustic: 0.90
  - type: PressureProtection
    highPressureMultiplier: 0.6
    lowPressureMultiplier: 1000
  - type: FlashImmunity # Goobstation
  - type: FlashSoundSuppression # Goobstation

#Warden's Hardsuit
- type: entity
  parent: ClothingHeadHardsuitWithLightBase
  id: ClothingHeadHelmetHardsuitWarden
  name: "шолом скафандра вардена"
  description: "Модифікований шолом. На диво зручний."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/security-warden.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/security-warden.rsi
  - type: PointLight
    color: "#ffeead"
  - type: PressureProtection
    highPressureMultiplier: 0.525
    lowPressureMultiplier: 1000
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Heat: 0.9
  - type: FlashImmunity # Goobstation
  - type: FlashSoundSuppression # Goobstation

#Captain's Hardsuit
- type: entity
  parent: ClothingHeadHardsuitWithLightBase
  id: ClothingHeadHelmetHardsuitCap
  name: "шолом скафандра капітана"
  description: "Спеціальний захисний шолом, виготовлений для Капітана станції."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/capspace.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/capspace.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.3
    lowPressureMultiplier: 1000
  - type: FlashImmunity # Goobstation
  - type: FlashSoundSuppression # Goobstation

#Chief Engineer's Hardsuit
- type: entity
  parent: ClothingHeadHardsuitWithLightBase
  id: ClothingHeadHelmetHardsuitEngineeringWhite
  name: "шолом скафандра головного інженера"
  description: "Спеціальний захисний шолом, виготовлений для Головного Інженера станції."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/engineering-white.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/engineering-white.rsi
  - type: PointLight
    color: "#daffad"
  - type: PressureProtection
    highPressureMultiplier: 0.08
    lowPressureMultiplier: 1000

#Chief Medical Officer's Hardsuit
- type: entity
  parent: ClothingHeadHardsuitWithLightBase
  id: ClothingHeadHelmetHardsuitMedical
  name: "шолом скафандра головного лікаря"
  description: "Легкий медичний шолом-гардкостюм, що не обмежує рухів голови."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/medical.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/medical.rsi
  - type: PointLight
    color: "#adf1ff"
  - type: PressureProtection
    highPressureMultiplier: 0.6
    lowPressureMultiplier: 1000

#Research Director's Hardsuit
- type: entity
  parent: ClothingHeadHardsuitWithLightBase
  id: ClothingHeadHelmetHardsuitRd
  name: "шолом експериментального скафандра вчених"
  description: "Легкий шолом-гардкостюм, що не обмежує рухів голови."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/rd.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/rd.rsi
  - type: PointLight
    color: "#d6adff"
  - type: PressureProtection
    highPressureMultiplier: 0.60
    lowPressureMultiplier: 1000

#Head of Security's hardsuit
- type: entity
  parent: ClothingHeadHardsuitWithLightBase
  id: ClothingHeadHelmetHardsuitSecurityRed
  name: "шолом скафандра голови безпеки"
  description: "Захисний шолом з новітнім надсекретним програмним забезпеченням NT-HUD. Належить Голові Безпеки."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/security-red.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/security-red.rsi
  - type: PointLight
    color: "#ffeead"
  - type: PressureProtection
    highPressureMultiplier: 0.45
    lowPressureMultiplier: 1000
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Heat: 0.9
  - type: FlashImmunity # Goobstation
  - type: FlashSoundSuppression # Goobstation

#Luxury Mining Hardsuit
- type: entity
  parent: ClothingHeadHardsuitWithLightBase
  id: ClothingHeadHelmetHardsuitLuxury #DO NOT MAP - https://github.com/space-wizards/space-station-14/pull/19738#issuecomment-1703486738
  name: "шолом розкішного шахтерського скафандра"
  description: "Відремонтована гірничий шолом з атласною підкладкою та додатковою (непрацюючою) антеною, адже ви - той самий зайвий."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/luxury.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/luxury.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.525
    lowPressureMultiplier: 1000
  - type: PointLight
    radius: 7
    energy: 3

#ANTAG HARDSUITS
#Blood-red Hardsuit
- type: entity
  parent: ClothingHeadHelmetHardsuitSyndie
  id: ClothingHeadHelmetHardsuitShanlinUnpainted
  name: "Шолом-комбінезон Cybersun"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/syndicate-base.rsi
    layers:
    - state: icon
    - state: icon-unshaded
      shader: unshaded
    - state: light-overlay
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/syndicate-base.rsi
    clothingVisuals:
      head:
      - state: equipped-head
      - state: equipped-head-unshaded
        shader: unshaded
  - type: ToggleableLightVisuals
    spriteLayer: light
    clothingVisuals:
      head:
      - state: equipped-head-light
        shader: unshaded

- type: entity
  parent: ClothingHeadHardsuitWithLightBase
  id: ClothingHeadHelmetHardsuitSyndie
  name: "криваво-червоний шолом скафандра"
  description: "Важкоброньований шолом, призначений для роботи в спецопераціях. Власність Gorlex Marauders."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: _Impstation/Clothing/Head/Hardsuits/syndicate.rsi
  - type: Clothing
    sprite: _Impstation/Clothing/Head/Hardsuits/syndicate.rsi
    clothingVisuals:
      head:
      - state: off-equipped-HELMET
      - state: off-equipped-HELMET-glow
        shader: unshaded
  - type: PointLight
    color: green
  - type: PressureProtection
    highPressureMultiplier: 0.08
    lowPressureMultiplier: 1000
  - type: HideLayerClothing
    slots:
    - Snout
    - Hair
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Heat: 0.9
  - type: FlashImmunity # Goobstation
  - type: FlashSoundSuppression # Goobstation

#Blood-red Medic Hardsuit
- type: entity
  parent: ClothingHeadHardsuitWithLightBase
  id: ClothingHeadHelmetHardsuitSyndieMedic
  name: "криваво-червоний шолом медичного скафандра"
  description: "Удосконалений шолом червоного кольору, спеціально розроблений для польових медиків."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: _Impstation/Clothing/Head/Hardsuits/syndicate_agent.rsi
  - type: Clothing
    sprite: _Impstation/Clothing/Head/Hardsuits/syndicate_agent.rsi
    clothingVisuals:
      head:
      - state: off-equipped-HELMET
      - state: off-equipped-HELMET-glow
        shader: unshaded
  - type: PointLight
    color: green
  - type: PressureProtection
    highPressureMultiplier: 0.08
    lowPressureMultiplier: 1000
  - type: HideLayerClothing
    slots:
    - Snout
    - Hair
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Heat: 0.9
  - type: FlashImmunity # Goobstation
  - type: FlashSoundSuppression # Goobstation

#Syndicate Elite Hardsuit
- type: entity
  parent: ClothingHeadHelmetHardsuitSyndieElite
  id: ClothingHeadHelmetHardsuitShiweiUnpainted
  name: "Елітний шолом-комбінезон Cybersun"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/syndicate-elite-base.rsi
    layers:
    - state: icon
    - state: icon-unshaded
      shader: unshaded
    - state: light-overlay
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/syndicate-elite-base.rsi
    clothingVisuals:
      head:
      - state: equipped-head
      - state: equipped-head-unshaded
        shader: unshaded
  - type: ToggleableLightVisuals
    spriteLayer: light
    clothingVisuals:
      head:
      - state: equipped-head-light
        shader: unshaded

- type: entity
  parent: ClothingHeadHardsuitWithLightBase
  id: ClothingHeadHelmetHardsuitSyndieElite
  name: "синдикатський елітний шолом"
  description: "Елітна версія криваво-червоного шолома, з покращеною бронею та вогнетривкістю. Власність \"Мародерів Горлекса\"."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: _Impstation/Clothing/Head/Hardsuits/syndicate_elite.rsi
  - type: Clothing
    sprite: _Impstation/Clothing/Head/Hardsuits/syndicate_elite.rsi
    clothingVisuals:
      head:
      - state: off-equipped-HELMET
      - state: off-equipped-HELMET-glow
        shader: unshaded
  - type: PointLight
    color: red
  - type: PressureProtection
    highPressureMultiplier: 0.08
    lowPressureMultiplier: 1000
  - type: HideLayerClothing
    slots:
    - Snout
    - Hair
  - type: TemperatureProtection
    coefficient: 0.005
  - type: FireProtection
    reduction: 0.2
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Heat: 0.9
  - type: FlashImmunity # Goobstation
  - type: FlashSoundSuppression # Goobstation

#Syndicate Commander Hardsuit
- type: entity
  parent: ClothingHeadHardsuitWithLightBase
  id: ClothingHeadHelmetHardsuitSyndieCommander
  name: "шолом командира синдикату"
  description: "Збільшена версія криваво-червоного шолома, спеціально розроблена для командира оперативного загону синдикату. Має значно покращену броню для смертельних перестрілок на лінії фронту."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: _Impstation/Clothing/Head/Hardsuits/syndicate_commander.rsi
  - type: Clothing
    sprite: _Impstation/Clothing/Head/Hardsuits/syndicate_commander.rsi
    clothingVisuals:
      head:
      - state: off-equipped-HELMET
      - state: off-equipped-HELMET-glow
        shader: unshaded
  - type: PointLight
    color: green
  - type: PressureProtection
    highPressureMultiplier: 0.08
    lowPressureMultiplier: 1000
  - type: HideLayerClothing
    slots:
    - Snout
    - Hair
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Heat: 0.9
  - type: FlashImmunity # Goobstation
  - type: FlashSoundSuppression # Goobstation

#Cybersun Juggernaut Hardsuit
- type: entity
  parent: ClothingHeadHardsuitBase
  id: ClothingHeadHelmetHardsuitCybersun
  name: "шолом джаггернаута cybersun"
  description: "Цей шолом, виготовлений зі спресованої червоної матерії, був розроблений у хромосферній установці Tau."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: _Impstation/Clothing/Head/Hardsuits/syndicate_juggernaut.rsi
  - type: Clothing
    sprite: _Impstation/Clothing/Head/Hardsuits/syndicate_juggernaut.rsi
    clothingVisuals:
      head:
      - state: equipped-HELMET
      - state: equipped-HELMET-glow
        shader: unshaded
  - type: PressureProtection
    highPressureMultiplier: 0.3
    lowPressureMultiplier: 1000
  - type: HideLayerClothing
    slots:
    - Snout
    - Hair
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Heat: 0.9
  - type: FlashImmunity # Goobstation
  - type: FlashSoundSuppression # Goobstation

#Wizard Hardsuit
- type: entity
  parent: [ClothingHeadHardsuitWithLightBase, ClothingHeadHatWizardBaseArmor] # Goob edit
  id: ClothingHeadHelmetHardsuitWizard
  name: "захисний шолом чарівника"
  description: "Химерний шолом, інкрустований коштовним камінням, що випромінює магічну енергію."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/wizard.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/wizard.rsi
  - type: PointLight # Goob edit
    color: "#4d7ae3"
    energy: 10
    radius: 5
  - type: PressureProtection
    highPressureMultiplier: 0.27
    lowPressureMultiplier: 1000
  - type: Armor # Goob edit
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Heat: 0.9
  - type: FlashImmunity # Goobstation
  - type: FlashSoundSuppression # Goobstation
  - type: WizardClothes # Goobstation

#Organic Space Suit
- type: entity
  parent: ClothingHeadHardsuitBase
  id: ClothingHeadHelmetHardsuitLing
  name: "органічний космічний шолом"
  description: "Космічна біомаса зі стійкої до тиску та температури тканини."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/lingspacehelmet.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/lingspacehelmet.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.225
    lowPressureMultiplier: 1000

#Pirate EVA Suit (Deep Space EVA Suit)
- type: entity
  parent: ClothingHeadHardsuitBase
  id: ClothingHeadHelmetHardsuitPirateEVA
  name: "шолом EVA для глибокого космосу"
  suffix: Pirate
  description: "Шолом EVA для глибокого космосу, дуже важкий, але забезпечує хороший захист."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/pirateeva.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/pirateeva.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.3
    lowPressureMultiplier: 1000

#Pirate Captain Hardsuit
- type: entity
  parent: ClothingHeadHardsuitBase
  id: ClothingHeadHelmetHardsuitPirateCap
  name: "шолом піратського капітана"
  suffix: Pirate
  description: "Спеціальний шолом, виготовлений для капітана піратського корабля."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/piratecaptainhelm.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/piratecaptainhelm.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.3
    lowPressureMultiplier: 1000
  - type: FlashImmunity # Goobstation
  - type: FlashSoundSuppression # Goobstation

#CENTCOMM / ERT HARDSUITS
#ERT Leader Hardsuit
- type: entity
  parent: ClothingHeadHelmetHardsuitSyndie
  id: ClothingHeadHelmetHardsuitERTLeader
  name: "шолом лідера ГШР"
  description: "Спеціальний захисний шолом, який носять члени команди реагування на надзвичайні ситуації."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/ERThelmets/ertleader.rsi
  - type: Clothing
    clothingVisuals:
      head:
      - state: off-equipped-HELMET
      - state: off-equipped-HELMET-unshaded
        shader: unshaded
    sprite: Clothing/Head/Hardsuits/ERThelmets/ertleader.rsi
  - type: PointLight
    color: "#addbff"
  - type: ToggleableLightVisuals
    spriteLayer: light
    clothingVisuals:
      head:
      - state: on-equipped-HELMET
      - state: on-equipped-HELMET-unshaded
        shader: unshaded
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Heat: 0.9

#ERT Chaplain Hardsuit
- type: entity
  parent: ClothingHeadHelmetHardsuitSyndie
  id: ClothingHeadHelmetHardsuitERTChaplain
  name: "Шолом капелана ERT"
  description: "Спеціальний захисний шолом, який носять члени команди реагування на надзвичайні ситуації."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/ERThelmets/ertchaplain.rsi
  - type: Clothing
    clothingVisuals:
      head:
      - state: off-equipped-HELMET
      - state: off-equipped-HELMET-unshaded
        shader: unshaded
    sprite: Clothing/Head/Hardsuits/ERThelmets/ertchaplain.rsi
  - type: PointLight
    color: "#ffffff"
  - type: ToggleableLightVisuals
    spriteLayer: light
    clothingVisuals:
      head:
      - state: on-equipped-HELMET
      - state: on-equipped-HELMET-unshaded
        shader: unshaded

#ERT Engineer Hardsuit
- type: entity
  parent: ClothingHeadHelmetHardsuitSyndie
  id: ClothingHeadHelmetHardsuitERTEngineer
  name: "шолом інженера ГШР"
  description: "Спеціальний захисний шолом, який носять члени команди реагування на надзвичайні ситуації."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/ERThelmets/ertengineer.rsi
  - type: Clothing
    clothingVisuals:
      head:
      - state: off-equipped-HELMET
      - state: off-equipped-HELMET-unshaded
        shader: unshaded
    sprite: Clothing/Head/Hardsuits/ERThelmets/ertengineer.rsi
  - type: PointLight
    color: "#f4ffad"
  - type: ToggleableLightVisuals
    spriteLayer: light
    clothingVisuals:
      head:
      - state: on-equipped-HELMET
      - state: on-equipped-HELMET-unshaded
        shader: unshaded
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Heat: 0.9

#ERT Medical Hardsuit
- type: entity
  parent: ClothingHeadHelmetHardsuitSyndie
  id: ClothingHeadHelmetHardsuitERTMedical
  name: "шолом медика ГШР"
  description: "Спеціальний захисний шолом, який носять члени команди реагування на надзвичайні ситуації."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/ERThelmets/ertmedical.rsi
  - type: Clothing
    clothingVisuals:
      head:
      - state: off-equipped-HELMET
      - state: off-equipped-HELMET-unshaded
        shader: unshaded
    sprite: Clothing/Head/Hardsuits/ERThelmets/ertmedical.rsi
  - type: PointLight
    color: "#adffec"
  - type: ToggleableLightVisuals
    spriteLayer: light
    clothingVisuals:
      head:
      - state: on-equipped-HELMET
      - state: on-equipped-HELMET-unshaded
        shader: unshaded

#ERT Security Hardsuit
- type: entity
  parent: ClothingHeadHelmetHardsuitSyndie
  id: ClothingHeadHelmetHardsuitERTSecurity
  name: "шолом охоронця ГШР"
  description: "Спеціальний захисний шолом, який носять члени команди реагування на надзвичайні ситуації."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/ERThelmets/ertsecurity.rsi
  - type: Clothing
    clothingVisuals:
      head:
      - state: off-equipped-HELMET
      - state: off-equipped-HELMET-unshaded
        shader: unshaded
    sprite: Clothing/Head/Hardsuits/ERThelmets/ertsecurity.rsi
  - type: PointLight
    color: "#ffadc6"
  - type: ToggleableLightVisuals
    spriteLayer: light
    clothingVisuals:
      head:
      - state: on-equipped-HELMET
      - state: on-equipped-HELMET-unshaded
        shader: unshaded
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Heat: 0.9

#ERT Janitor Hardsuit
- type: entity
  parent: ClothingHeadHelmetHardsuitSyndie
  id: ClothingHeadHelmetHardsuitERTJanitor
  name: "шолом прибиральника ГШР"
  description: "Спеціальний захисний шолом, який носять члени команди реагування на надзвичайні ситуації."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/ERThelmets/ertjanitor.rsi
  - type: Clothing
    clothingVisuals:
      head:
      - state: off-equipped-HELMET
      - state: off-equipped-HELMET-unshaded
        shader: unshaded
    sprite: Clothing/Head/Hardsuits/ERThelmets/ertjanitor.rsi
  - type: PointLight
    color: "#cbadff"
  - type: ToggleableLightVisuals
    spriteLayer: light
    clothingVisuals:
      head:
      - state: on-equipped-HELMET
      - state: on-equipped-HELMET-unshaded
        shader: unshaded

#CBURN Hardsuit
- type: entity
  parent: ClothingHeadHardsuitWithLightBase
  id: ClothingHeadHelmetCBURN
  name: "шолом екзокостюму CBURN"
  description: "Стійкий до тиску та вогнетривкий капюшон, який одягається спеціальними прибиральними машинами."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/cburn.rsi
    layers:
    - state: icon
    - state: icon-unshaded
      shader: unshaded
    - state: light-overlay
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-head
      - state: equipped-head-unshaded
        shader: unshaded
  - type: PointLight
    color: orange
  - type: PressureProtection
    highPressureMultiplier: 0.08
    lowPressureMultiplier: 1000
  - type: TemperatureProtection
    coefficient: 0.005
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Heat: 0.9
  - type: FlashImmunity # Goobstation
  - type: FlashSoundSuppression # Goobstation

#Deathsquad Hardsuit
- type: entity
  parent: ClothingHeadHardsuitBase
  id: ClothingHeadHelmetHardsuitDeathsquad
  name: "шолом ескадрону смерті"
  description: "Надійний шолом для спецоперацій."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/deathsquad.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/deathsquad.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.08
    lowPressureMultiplier: 1000
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.80
        Slash: 0.80
        Piercing: 0.80
        Heat: 0.80
        Radiation: 0.80
        Caustic: 0.95
  - type: ThermalVision
    isEquipment: true
    color: "#98EEFB"
    lightRadius: 15
  - type: FlashImmunity # Goobstation
  - type: FlashSoundSuppression # Goobstation
    protectionRange: 0

#MISC. HARDSUITS
#Clown Hardsuit
- type: entity
  parent: ClothingHeadHelmetHardsuitSecurity
  id: ClothingHeadHelmetHardsuitClown
  name: "шолом скафандру клоуна"
  description: "Клоунський шолом."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/clown.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/clown.rsi
    equipSound: /Audio/Mecha/mechmove03.ogg
    unequipSound: /Audio/Effects/Emotes/parp1.ogg

#Mime Hardsuit
- type: entity
  parent: ClothingHeadHelmetHardsuitSecurity
  id: ClothingHeadHelmetHardsuitMime
  name: "шолом скафандру міма"
  description: "Мімічний шолом."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/mime.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/mime.rsi

#Santas Hardsuit
- type: entity
  parent: ClothingHeadHardsuitBase
  id: ClothingHeadHelmetHardsuitSanta
  name: "шолом Санта-Клауса"
  description: "Святковий захисний шолом, який захистить дарувальника від впливу середовища з низьким тиском."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Hardsuits/santahelm.rsi
  - type: Clothing
    sprite: Clothing/Head/Hardsuits/santahelm.rsi
  - type: PointLight
    color: "#f4ffad"
    radius: 5
    energy: 2
