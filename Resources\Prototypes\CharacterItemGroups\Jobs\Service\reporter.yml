# Reporter
#- type: characterItemGroup
#  id: LoadoutReporterBackpacks
#  maxItems: 1
#  items:
#
#- type: characterItemGroup
#  id: LoadoutReporterBelt
#  maxItems: 1
#  items:
#
#- type: characterItemGroup
#  id: LoadoutReporterEars
#  maxItems: 1
#  items:
#
#- type: characterItemGroup
#  id: LoadoutReporterEquipment
#  maxItems: 1
#  items:
#
#- type: characterItemGroup
#  id: LoadoutReporterEyes
#  maxItems: 1
#  items:
#
#- type: characterItemGroup
#  id: LoadoutReporterGloves
#  maxItems: 1
#  items:
#
#- type: characterItemGroup
#  id: LoadoutReporterHead
#  maxItems: 1
#  items:
#
#- type: characterItemGroup
#  id: LoadoutReporterId
#  maxItems: 1
#  items:
#
#- type: characterItemGroup
#  id: LoadoutReporterNeck
#  maxItems: 1
#  items:
#
#- type: characterItemGroup
#  id: LoadoutReporterMask
#  maxItems: 1
#  items:
#
#- type: characterItemGroup
#  id: LoadoutReporterOuter
#  maxItems: 1
#  items:
#
#- type: characterItemGroup
#  id: LoadoutReporterShoes
#  maxItems: 1
#  items:
#
- type: characterItemGroup
  id: LoadoutReporterUniforms
  maxItems: 1
  items:
    - type: loadout
      id: LoadoutServiceReporterUniformDetectivesuit
    - type: loadout
      id: LoadoutServiceReporterUniformDetectiveskirt
    - type: loadout
      id: LoadoutServiceReporterUniformJournalist
