- type: loadout
  id: LoadoutEyesEyepatch
  category: Eyes
  cost: 0
  canBeHeirloom: true
  items:
    - ClothingEyesEyepatch
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutEyesEyepatchColor
  category: Eyes
  cost: 0
  canBeHeirloom: true
  customColorTint: true
  items:
    - ClothingEyesEyepatchColor
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutEyesEyepatchColorFlipped
  category: Eyes
  cost: 0
  canBeHeirloom: true
  customColorTint: true
  items:
    - ClothingEyesEyepatchColorFlipped
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutEyesGlasses
  category: Eyes
  cost: 0
  customColorTint: true
  items:
    - ClothingEyesGlasses
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutEyesGlassesJamjar
  category: Eyes
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingEyesGlassesJamjar
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutEyesGlasses3D
  category: Eyes
  cost: 0
  exclusive: true
  items:
    - ClothingEyesGlasses3D
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutEyesGlassesHipster
  category: Eyes
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingEyesGlassesHipster
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutEyesGlassesMonocle
  category: Eyes
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingEyesGlassesMonocle
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutEyesGlassesPanto
  category: Eyes
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingEyesGlassesPanto
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutEyesGlassesJensen
  category: Eyes
  cost: 0
  exclusive: true
  items:
    - ClothingEyesGlassesJensen
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutEyesBlindfold
  category: Eyes
  cost: 0
  items:
    - ClothingEyesBlindfold
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutEyesBlindfoldColor
  category: Eyes
  cost: 0
  customColorTint: true
  items:
    - ClothingEyesBlindfoldColor
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutItemCheapSunglasses
  category: Eyes
  cost: 0
  exclusive: true
  items:
    - ClothingEyesGlassesCheapSunglasses
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutEyesGlassesCheapSunglassesAviator
  category: Eyes
  cost: 0
  exclusive: true
  customColorTint: true
  items:
    - ClothingEyesGlassesCheapSunglassesAviator
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutEyesGlassesCheapSunglassesBig
  category: Eyes
  cost: 0
  exclusive: true
  items:
    - ClothingEyesGlassesCheapSunglassesBig
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutEyesGlassesCheapSunglassesVisor
  category: Eyes
  cost: 0
  exclusive: true
  items:
    - ClothingEyesGlassesCheapSunglassesVisor
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutItemSunglasses
  category: Eyes
  cost: 7
  exclusive: true
  items:
    - ClothingEyesGlassesSunglasses
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutEyesGlassesSunglassesAviator
  category: Eyes
  cost: 7
  exclusive: true
  customColorTint: true
  items:
    - ClothingEyesGlassesSunglassesAviator
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutEyesGlassesSunglassesBig
  category: Eyes
  cost: 7
  exclusive: true
  items:
    - ClothingEyesGlassesSunglassesBig
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutEyesGlassesSunglassesVisor
  category: Eyes
  cost: 7
  exclusive: true
  items:
    - ClothingEyesGlassesSunglassesVisor
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutItemBlindfoldFake
  category: Eyes
  cost: 0
  exclusive: true
  items:
    - ClothingEyesBlindfoldFake
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes

- type: loadout
  id: LoadoutEyesBlindfoldFakeColor
  category: Eyes
  cost: 0
  customColorTint: true
  items:
    - ClothingEyesBlindfoldFakeColor
  requirements:
    - !type:CharacterItemGroupRequirement
      group: LoadoutEyes
