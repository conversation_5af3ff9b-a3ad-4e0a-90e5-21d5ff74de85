- type: entity
  name: "BaseWeaponShotgun"
  parent: BaseItem
  id: BaseWeaponShotgun
  description: "Корінцевий зубастий і пагонистий."
  abstract: true
  components:
  - type: Sprite
    layers:
    - state: icon
      map: [ "enum.GunVisualLayers.Base" ]
  - type: Item
    # If you update this also update the bulldog's size.
    size: Large
  - type: Clothing
    sprite: DeltaV/Objects/Weapons/Guns/Shotguns/db_shotgun.rsi # Delta-V
    quickEquip: false
    slots:
    - Back
    - suitStorage
  - type: AmmoCounter
  - type: Gun
    fireRate: 2
    selectedMode: SemiAuto
    availableModes:
    - SemiAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/shotgun.ogg
    soundEmpty:
      path: /Audio/Weapons/Guns/Empty/empty.ogg
    fireOnDropChance: 0.2
  - type: BallisticAmmoProvider
    whitelist:
      tags:
      - ShellShotgun
      - Flare
    capacity: 7
    proto: ShellShotgun
    soundInsert:
      path: /Audio/Weapons/Guns/MagIn/shotgun_insert.ogg
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
        ents: []
  - type: StaticPrice
    price: 500
  - type: Execution
  - type: MeleeWeapon
    attackRate: 1.4
    damage:
      types:
        Blunt: 10
    bluntStaminaDamageFactor: 1.3333
    swapKeys: true
    disableHeavy: true
    animation: WeaponArcThrust
    wideAnimationRotation: 180
    soundHit:
      collection: MetalThud
  - type: IncreaseDamageOnWield
    damage:
      types:
        Blunt: 2.5
  - type: DamageOtherOnHit
    staminaCost: 9.5
    # PIRATE START
  - type: CanHoldAccessories
    whiteListTags:
      - CombatKnife
  - type: CanTakeAim
    # PIRATE END

- type: entity
  name: "Бульдог"
  # Don't parent to BaseWeaponShotgun because it differs significantly
  parent: [BaseItem, BaseGunWieldable]
  id: WeaponShotgunBulldog
  description: "Це рушниця з магазинним живленням, призначена для ближнього бою. Використовує набої калібру .50."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Shotguns/bulldog.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-0
      map: ["enum.GunVisualLayers.Mag"]
  - type: Item
    size: Large
  - type: Clothing
    sprite: Objects/Weapons/Guns/Shotguns/bulldog.rsi
    quickEquip: false
    slots:
    - Back
    - suitStorage
  - type: AmmoCounter
  - type: GunRequiresWield #remove when inaccuracy on spreads is fixed
  - type: Gun
    fireRate: 2
    selectedMode: FullAuto
    availableModes:
    - FullAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/shotgun.ogg
    soundEmpty:
      path: /Audio/Weapons/Guns/Empty/empty.ogg
    fireOnDropChance: 0.3
    shotgunSpreadMultiplier: 1.5
    shotgunProjectileCountModifier: 1.33
  - type: Wieldable
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        startingItem: MagazineShotgun
        priority: 2
        whitelist:
          tags:
          - MagazineShotgun
        insertSound: /Audio/Weapons/Guns/MagIn/smg_magin.ogg
        ejectSound: /Audio/Weapons/Guns/MagOut/smg_magout.ogg
  - type: ContainerContainer
    containers:
      gun_magazine: !type:ContainerSlot
      gun_chamber: !type:ContainerSlot
  - type: MagazineAmmoProvider
  - type: MagazineVisuals
    magState: mag
    steps: 1
    zeroVisible: true
  - type: Appearance
  - type: StaticPrice
    price: 500
  - type: Execution
  - type: MeleeWeapon
    attackRate: 1.4
    damage:
      types:
        Blunt: 10
    bluntStaminaDamageFactor: 1.3333
    swapKeys: true
    disableHeavy: true
    animation: WeaponArcThrust
    wideAnimationRotation: 180
    soundHit:
      collection: MetalThud
  - type: IncreaseDamageOnWield
    damage:
      types:
        Blunt: 2.5
  - type: DamageOtherOnHit
    staminaCost: 9.5

- type: entity
  name: "Бульдог"
  parent: [WeaponShotgunBulldog]
  id: WeaponShotgunBulldogEmpty
  description: "Це рушниця з магазинним живленням, призначена для ближнього бою. Використовує набої калібру .50."
  components:
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        startingItem: null
        priority: 2
        whitelist:
          tags:
          - MagazineShotgun
        insertSound: /Audio/Weapons/Guns/MagIn/smg_magin.ogg
        ejectSound: /Audio/Weapons/Guns/MagOut/smg_magout.ogg

- type: entity
  name: "антикварний Бульдог"
  parent: WeaponShotgunBulldog
  id: WeaponShotgunBulldogHoS
  description: "Це, на перший погляд, звичайна рушниця, яка нічим не відрізняється від тих, що є на озброєнні ВМС республіки Бізель. При уважному розгляді виявляється, що серійний номер цієї зброї - 000000013."
  components:
    - type: StealTarget
      stealGroup: HoSAntiqueWeapon

- type: entity
  name: "двоствольна рушниця"
  parent: [BaseWeaponShotgun, BaseGunWieldable]
  id: WeaponShotgunDoubleBarreled
  description: "Безсмертна класика. Використовує гільзи 50-го калібру."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Shotguns/db_shotgun.rsi # Delta-V
  - type: Item
    size: Normal
    shape:
    - 0,0,4,0
    sprite: Objects/Weapons/Guns/Shotguns/db_shotgun_inhands_64x.rsi
  - type: GunRequiresWield #remove when inaccuracy on spreads is fixed
  - type: Gun
    fireRate: 2
    fireOnDropChance: 0.5
  - type: Wieldable
  - type: BallisticAmmoProvider
    capacity: 2
  - type: Construction
    graph: ShotgunSawn
    node: start
    deconstructionTarget: null
  - type: MeleeWeapon
    damage:
      types:
        Blunt: 8.5
  - type: DamageOtherOnHit
    staminaCost: 7.5

- type: entity
  name: "двоствольна рушниця"
  parent: WeaponShotgunDoubleBarreled
  id: WeaponShotgunDoubleBarreledRubber
  description: "Безсмертна класика. Використовує гільзи 50-го калібру."
  suffix: Non-Lethal
  components:
  - type: BallisticAmmoProvider
    proto: ShellShotgunBeanbag

- type: entity
  name: "Enforcer"
  parent: [BaseWeaponShotgun, BaseGunWieldable]
  id: WeaponShotgunEnforcer
  description: "Бойова рушниця преміум-класу, створена на основі конструкції Kammerer, зі збільшеною місткістю обойми. Дробові набої калібру .50."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Shotguns/enforcer.rsi
  - type: Clothing
    sprite: DeltaV/Objects/Weapons/Guns/Shotguns/enforcer.rsi
  - type: Item
    sprite: Objects/Weapons/Guns/Shotguns/enforcer_inhands_64x.rsi
  - type: BallisticAmmoProvider
    capacity: 7
    autoCycle: false
  - type: Wieldable
  - type: GunRequiresWield
  - type: MeleeWeapon
    attackRate: 1.4
    damage:
      types:
        Blunt: 9
  - type: DamageOtherOnHit
    staminaCost: 8.0

- type: entity
  parent: WeaponShotgunEnforcer
  id: WeaponShotgunEnforcerRubber
  suffix: Non-Lethal
  components:
  - type: BallisticAmmoProvider
    proto: ShellShotgunBeanbag

- type: entity
  name: "Каммерер"
  parent: [BaseWeaponShotgun, BaseGunWieldable]
  id: WeaponShotgunKammerer
  description: "Коли старий дизайн Ремінгтона поєднується з сучасними матеріалами, виходить ось такий результат. Улюблена зброя ополченців у багатьох країнах світу. Використовує набої калібру .50."
  components:
  - type: Item
    size: Normal
    shape:
    - 0,0,4,0
    sprite: Objects/Weapons/Guns/Shotguns/pump_inhands_64x.rsi
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Shotguns/pump.rsi
  - type: Clothing
    sprite: Objects/Weapons/Guns/Shotguns/pump.rsi
  - type: GunRequiresWield #remove when inaccuracy on spreads is fixed
  - type: BallisticAmmoProvider
    capacity: 4
    autoCycle: false
  - type: Tag
    tags:
    - WeaponShotgunKammerer
  - type: Wieldable
  - type: Gun
    shotgunSpreadMultiplier: 0.5
  - type: ExtendDescription
    descriptionList:
      - description: "gun-modifier-choke"
        fontSize: 12
        color: "#ff4f00"
        requireDetailRange: false

- type: entity
  name: "обріз рушниці"
  parent: BaseWeaponShotgun
  id: WeaponShotgunSawn
  description: "Круто! Використовує гільзи 50-го калібру."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Shotguns/sawn.rsi # Delta-V
  - type: Clothing
    sprite: DeltaV/Objects/Weapons/Guns/Shotguns/sawn.rsi # Delta-V
  - type: Item
    size: Small
    sprite: Objects/Weapons/Guns/Shotguns/sawn_inhands_64x.rsi
  - type: Gun
    fireRate: 4
    fireOnDropChance: 0.5
    shotgunSpreadMultiplier: 3
    damageModifier: 1.5
  - type: BallisticAmmoProvider
    capacity: 2
  - type: Construction
    graph: ShotgunSawn
    node: shotgunsawn
    deconstructionTarget: null
  - type: MeleeWeapon
    attackRate: 1.2
    damage:
      types:
        Blunt: 7.5
  - type: DamageOtherOnHit
    staminaCost: 6

- type: entity
  parent: WeaponShotgunSawn
  id: WeaponShotgunSawnLumen
  suffix: Lumenblast
  description: "Круто! Використовує гільзи 50-го калібру."
  components:
  - type: BallisticAmmoProvider
    capacity: 2
    proto: ShellShotgunLumen

- type: entity
  name: "відпиляний сьогун"
  parent: WeaponShotgunSawn
  id: WeaponShotgunSawnEmpty
  description: "Круто! Використовує гільзи 50-го калібру."
  suffix: Empty
  components:
  - type: BallisticAmmoProvider
    proto: null
  - type: Construction
    graph: ShotgunSawn
    node: shotgunsawn
    deconstructionTarget: null

- type: entity
  name: "пістолет ручної роботи"
  parent: BaseWeaponShotgun
  id: WeaponShotgunHandmade
  description: "Виглядає ненадійно. Використовує гільзи від дробовика 50-го калібру."
  components:
  - type: Item
    size: Small
    storedRotation: 90
  - type: Sprite
    sprite: Objects/Weapons/Guns/Shotguns/hm_pistol.rsi
  - type: Clothing
    sprite: Objects/Weapons/Guns/Shotguns/hm_pistol.rsi
  - type: Gun
    fireRate: 4
    fireOnDropChance: 1
  - type: BallisticAmmoProvider
    capacity: 1
  - type: Construction
    graph: ShotgunSawn
    node: shotgunsawn
    deconstructionTarget: null
  - type: StaticPrice
    price: 0
  - type: MeleeWeapon
    attackRate: 1.2
    damage:
      types:
        Blunt: 7.5
    wideAnimationRotation: 135
  - type: DamageOtherOnHit
    staminaCost: 5

- type: entity
  name: "мушкетон"
  parent: [BaseWeaponShotgun, BaseGunWieldable]
  id: WeaponShotgunBlunderbuss
  suffix: Pirate
  description: "Смертельна з близької відстані."
  components:
  - type: Item
    size: Normal
    shape:
    - 0,0,4,0
  - type: Sprite
    sprite: Objects/Weapons/Guns/Shotguns/blunderbuss.rsi
  - type: GunRequiresWield #remove when inaccuracy on spreads is fixed
  - type: Gun
    fireRate: 2
    fireOnDropChance: 1
  - type: BallisticAmmoProvider
    capacity: 1
  - type: StaticPrice
    price: 0
  - type: Wieldable

- type: entity
  name: "імпровізовану рушницю"
  parent: [BaseWeaponShotgun, BaseGunWieldable]
  id: WeaponShotgunImprovised
  description: "Гівняна ручна рушниця, яка використовує набої калібру .50. В патронник може поміститися лише один патрон."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Shotguns/improvised_shotgun.rsi # Delta-V
  - type: Clothing
    sprite: DeltaV/Objects/Weapons/Guns/Shotguns/improvised_shotgun.rsi # Delta-V
  - type: Item
    size: Normal
    shape:
    - 0,0,4,0
    sprite: Objects/Weapons/Guns/Shotguns/improvised_shotgun_inhands_64x.rsi
  - type: GunRequiresWield #remove when inaccuracy on spreads is fixed
  - type: Gun
    fireRate: 4 #No reason to stifle the firerate since you have to manually reload every time anyways.
    fireOnDropChance: 1
  - type: BallisticAmmoProvider
    capacity: 1
    proto: null
  - type: Construction
    graph: ImprovisedShotgunGraph
    node: shotgun
  - type: StaticPrice
    price: 20

- type: entity
  name: "імпровізовану рушницю"
  parent: WeaponShotgunImprovised
  suffix: Loaded
  id: WeaponShotgunImprovisedLoaded
  components:
  - type: BallisticAmmoProvider
    proto: ShellShotgunImprovised
