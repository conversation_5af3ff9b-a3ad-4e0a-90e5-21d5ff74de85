- type: job
  id: SeniorPhysician
  name: job-name-senior-physician
  description: job-description-senior-physician
  playTimeTracker: JobSeniorPhysician
  requirements:
    - !type:CharacterPlaytimeRequirement
      tracker: JobChemist
      min: 21600 #6 hrs
    - !type:CharacterPlaytimeRequirement
      tracker: JobMedicalDoctor
      min: 21600 #6 hrs
    - !type:CharacterDepartmentTimeRequirement
      department: Medical
      min: 216000 # 60 hrs
    - !type:CharacterEmployerRequirement
      employers:
      - ZengHuPharmaceuticals
      - Interdyne
      - NanoTrasen
  startingGear: SeniorPhysicianGear
  icon: "JobIconSeniorPhysician"
  supervisors: job-supervisors-cmo
  access:
  - Medical
  - Maintenance
  - Chemistry
  special:
  - !type:AddComponentSpecial
    components:
    - type: CPRTraining
    - type: SurgerySpeedModifier
      speedModifier: 2.0

- type: startingGear
  id: SeniorPhysicianGear
  subGear:
  - DoctorPlasmamanGear
  equipment:
    head: ClothingHeadHatBeretSeniorPhysician
    jumpsuit: ClothingUniformJumpsuitSeniorPhysician
    back: ClothingBackpackMedicalFilled
    shoes: ClothingShoesColorBlack
    outerClothing: ClothingOuterCoatLabSeniorPhysician
    id: SeniorPhysicianPDA
    ears: ClothingHeadsetMedical
    belt: ClothingBeltMedicalFilled
  innerClothingSkirt: ClothingUniformJumpskirtSeniorPhysician
  satchel: ClothingBackpackSatchelMedicalFilled
  duffelbag: ClothingBackpackDuffelMedicalFilled
