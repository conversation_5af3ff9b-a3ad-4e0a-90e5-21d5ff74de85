- type: entity
  id: ThiefBeacon
  name: "злодійський маяк"
  description: "Пристрій, який телепортує все навколо себе до сховища злодія наприкінці зміни."
  components:
    - type: ThiefBeacon
    - type: StealArea
    - type: Item
      size: Normal
    - type: Physics
      bodyType: Dynamic
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.25,-0.4,0.25,0.1"
          density: 20
          mask:
            - Impassable
    - type: Foldable
      folded: true
    - type: Clickable
    - type: InteractionOutline
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.FoldedVisuals.State:
          foldedLayer:
            True: { state: folded_extraction }
            False: { state: extraction_point }
    - type: Sprite
      sprite: Objects/Tools/thief_beacon.rsi
      drawdepth: SmallObjects
      noRot: true
      layers:
        - state: extraction_point
          map: [ "foldedLayer" ]