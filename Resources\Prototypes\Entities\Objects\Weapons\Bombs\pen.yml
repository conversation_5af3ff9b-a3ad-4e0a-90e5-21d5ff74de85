- type: entity
  name: "ручка"
  suffix: Exploding
  parent: PenEmbeddable
  description: "Ручка з темним чорнилом."
  id: PenExploding
  components:
  - type: OnUseTimerTrigger
    delay: 4
    examinable: false
  - type: Explosive
    explosionType: Default
    maxIntensity: 8
    intensitySlope: 5
    totalIntensity: 20
    canCreateVacuum: false
  - type: ActivateOnPaperOpened
  - type: ExplodeOnTrigger
  - type: TriggerOnSignal
  - type: DeviceLinkSink
    ports:
      - Trigger

- type: entity
  parent: BaseItem
  id: PenExplodingBox
  name: "вибухнула скринька з ручкою"
  description: "Невеличка коробочка з вибуховою ручкою. Упаковка розпадається при відкритті, не залишаючи після себе жодних доказів."
  components:
  - type: Item
    size: Tiny
  - type: Sprite
    sprite: Objects/Storage/penbox.rsi
    state: exploding_pen
  - type: SpawnItemsOnUse
    items:
    - id: PenExploding
    sound:
      path: /Audio/Effects/unwrap.ogg
