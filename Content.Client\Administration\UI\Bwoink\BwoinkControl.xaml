﻿<Control
    xmlns="https://spacestation14.io"
    xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls">
    <PanelContainer StyleClasses="BackgroundDark">
        <SplitContainer Orientation="Horizontal" VerticalExpand="True">
            <cc:PlayerListControl Access="Public" Name="ChannelSelector" HorizontalExpand="True" SizeFlagsStretchRatio="1" />
            <BoxContainer Orientation="Vertical" HorizontalExpand="True" SizeFlagsStretchRatio="2">
                <BoxContainer Access="Public" Name="BwoinkArea" VerticalExpand="True" />
                <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
                    <CheckBox Visible="True" Name="PlaySound" Access="Public" Text="{Loc 'admin-bwoink-play-sound'}" Pressed="True" />
                    <Control HorizontalExpand="True" MinWidth="5" />
                    <Button Visible="True" Name="PopOut" Access="Public" Text="{Loc 'admin-logs-pop-out'}" StyleClasses="OpenBoth" HorizontalAlignment="Left" />
                    <Control HorizontalExpand="True" />
                    <Button Visible="False" Name="Bans" Text="{Loc 'admin-player-actions-bans'}" StyleClasses="OpenRight" />
                    <Button Visible="False" Name="Notes" Text="{Loc 'admin-player-actions-notes'}" StyleClasses="OpenBoth" />
                    <Button Visible="False" Name="Kick" Text="{Loc 'admin-player-actions-kick'}" StyleClasses="OpenBoth" />
                    <Button Visible="False" Name="Ban" Text="{Loc 'admin-player-actions-ban'}" StyleClasses="OpenBoth" />
                    <Button Visible="False" Name="Respawn" Text="{Loc 'admin-player-actions-respawn'}" StyleClasses="OpenBoth" />
                    <Button Visible="False" Name="Follow" Text="{Loc 'admin-player-actions-follow'}" StyleClasses="OpenLeft" />
                </BoxContainer>
            </BoxContainer>
        </SplitContainer>
    </PanelContainer>
</Control>
