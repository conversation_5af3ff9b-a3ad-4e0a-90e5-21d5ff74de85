- type: construction
  id: ClosetSteel
  name: "шафа"
  graph: ClosetSteel
  startNode: start
  targetNode: done
  category: construction-category-storage
  description: "Високий сталевий ящик, який неможливо замкнути."
  icon: { sprite: Structures/Storage/closet.rsi, state: generic_icon }
  objectType: Structure

- type: construction
  id: ClosetSteelSecure
  name: "закріплена шафа"
  graph: ClosetSteelSecure
  startNode: start
  targetNode: done
  category: construction-category-storage
  description: "Високий сталевий ящик, який можна замкнути."
  icon: { sprite: Structures/Storage/closet.rsi, state: secure_icon }
  objectType: Structure

- type: construction
  id: ClosetWall
  name: "настінна шафа"
  graph: ClosetWall
  startNode: start
  targetNode: done
  category: construction-category-storage
  description: "Стандартний контейнер для зберігання від компанії Нанотрейзен, тепер на стінах."
  icon: { sprite: Structures/Storage/wall_locker.rsi, state: generic_icon }
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: true
  canBuildInImpassable: true
  conditions:
    - !type:WallmountCondition