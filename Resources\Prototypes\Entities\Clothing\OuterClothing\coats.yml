- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterCoatBomber
  name: "куртка-бомбер"
  description: "Товста, добре поношена шкіряна куртка-бомбер часів Другої світової війни."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/bomber.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/bomber.rsi
  - type: TemperatureProtection
    coefficient: 0.1

- type: entity
  parent: [ClothingOuterStorageBase, AllowSuitStorageClothing]
  id: ClothingOuterCoatDetective
  name: "плащ детектива"
  description: "Міцний брезентовий тренч, розроблений і створений компанією TX Fabrication Corp. Одягаючи його, ви відчуваєте важку долю тибетців."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/detective.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/detective.rsi
  - type: StorageFill
    contents:
    - id: SmokingPipeFilledTobacco
    - id: FlippoEngravedLighter
  - type: Armor #same as regular sec armor
    modifiers:
      coefficients:
        Blunt: 0.70
        Slash: 0.70
        Piercing: 0.70
        Heat: 0.80

- type: entity
  parent: ClothingOuterCoatDetective
  id: ClothingOuterCoatDetectiveLoadout
  components:
  - type: StorageFill
    contents:
    - id: SmokingPipeFilledTobacco
    - id: FlippoLighter #Not the steal objective, only difference from normal detective trenchcoat

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterCoatGentle
  name: "ніжне пальто"
  description: "Ніжне пальто для ніжного чоловіка або жінки."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/gentlecoat.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/gentlecoat.rsi

- type: entity
  parent: [ClothingOuterArmorCaptainCarapace, ClothingOuterStorageBase]
  id: ClothingOuterCoatCapTrench
  name: "бронежилет капітана"
  description: "Плащ зі спеціального сплаву для додаткового захисту та стилю для тих, хто має владні повноваження, прикрашений емблемами та декором, як і належить його власнику. Видається лише найкращим працівникам станції."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/cap_trenchcoat.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/cap_trenchcoat.rsi

- type: entity
  abstract: true
  parent: AllowSuitStorageClothing
  id: ClothingOuterArmorHoS
  components:
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.7
        Slash: 0.7
        Piercing: 0.7
        Heat: 0.7
        Caustic: 0.75 # not the full 90% from ss13 because of the head
  - type: ExplosionResistance
    damageCoefficient: 0.9

- type: entity
  abstract: true
  parent: AllowSuitStorageClothing
  id: ClothingOuterArmorWarden
  components:
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.7
        Slash: 0.7
        Piercing: 0.7
        Heat: 0.7
  - type: ExplosionResistance
    damageCoefficient: 0.9

- type: entity
  parent: [ClothingOuterArmorHoS, ClothingOuterStorageBase]
  id: ClothingOuterCoatHoSTrench
  name: "бронежилет начальника служби безпеки"
  description: "Пальто, посилене спеціальним сплавом для додаткового захисту та стилю для тих, хто має владний вигляд."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/OuterClothing/Coats/hos_trenchcoat.rsi # DeltaV - resprite
  - type: Clothing
    sprite: DeltaV/Clothing/OuterClothing/Coats/hos_trenchcoat.rsi # DeltaV - resprite

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterCoatInspector
  name: "пальто інспектора"
  description: "Суворий плащ інспектора для залякування під час перевірок."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/insp_coat.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/insp_coat.rsi

- type: entity
  parent: ClothingOuterStorageToggleableBase
  id: ClothingOuterCoatJensen
  name: "пальто дженсена"
  description: "Пальто Дженсена."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/jensencoat.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/jensencoat.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodChaplainHood

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterCoatTrench
  name: "шинель"
  description: "Зручна шинель."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/trenchcoat.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/trenchcoat.rsi
  - type: TemperatureProtection
    coefficient: 0.1
  - type: Armor
    modifiers:
      coefficients:
        Slash: 0.95
  - type: Food
    requiresSpecialDigestion: true
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Fiber
          Quantity: 20

- type: entity
  parent: ClothingOuterStorageFoldableBase
  id: ClothingOuterCoatLab
  name: "лабораторний халат"
  description: "Халат, що захищає від незначних розливів хімічних речовин."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/labcoat.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/labcoat.rsi
  - type: Armor
    modifiers:
      coefficients:
        Caustic: 0.75

- type: entity
  parent: [ClothingOuterStorageFoldableBaseOpened, ClothingOuterCoatLab]
  id: ClothingOuterCoatLabOpened
  name: "лабораторний халат"

- type: entity
  parent: ClothingOuterStorageFoldableBase
  id: ClothingOuterCoatLabChem
  name: "лабораторний халат хіміка"
  description: "Халат, що захищає від незначних розливів хімічних речовин. Має помаранчеву смужку на плечі."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/labcoat_chem.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/labcoat_chem.rsi
  - type: Armor
    modifiers:
      coefficients:
        Caustic: 0.75

- type: entity
  parent: [ClothingOuterStorageFoldableBaseOpened, ClothingOuterCoatLabChem]
  id: ClothingOuterCoatLabChemOpened
  name: "лабораторний халат хіміка"

- type: entity
  parent: ClothingOuterStorageFoldableBase
  id: ClothingOuterCoatLabViro
  name: "халат вірусолога"
  description: "Халат, що захищає від бактерій та вірусів. Має зелену смужку на плечі."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/labcoat_viro.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/labcoat_viro.rsi
  - type: Armor
    modifiers:
      coefficients:
        Caustic: 0.75

- type: entity
  parent: [ClothingOuterStorageFoldableBaseOpened, ClothingOuterCoatLabViro]
  id: ClothingOuterCoatLabViroOpened
  name: "халат вірусолога"

- type: entity
  parent: ClothingOuterStorageFoldableBase
  id: ClothingOuterCoatLabGene
  name: "халат генетика"
  description: "Халат, що захищає від незначних розливів хімічних речовин. Має синю смужку на плечі."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/labcoat_gene.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/labcoat_gene.rsi
  - type: Armor
    modifiers:
      coefficients:
        Caustic: 0.75

- type: entity
  parent: [ClothingOuterStorageFoldableBaseOpened, ClothingOuterCoatLabGene]
  id: ClothingOuterCoatLabGeneOpened
  name: "халат генетика"

- type: entity
  parent: ClothingOuterStorageFoldableBase
  id: ClothingOuterCoatLabCmo
  name: "халат головного лікаря"
  description: "Синій лабораторний халат для головного лікаря, виготовлений на замовлення, забезпечує покращений захист від розливу хімічних речовин та дрібних порізів"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/labcoat_cmo.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/labcoat_cmo.rsi
  - type: Armor
    modifiers:
      coefficients:
        Slash: 0.95
        Heat: 0.95
        Caustic: 0.65

- type: entity
  parent: [ClothingOuterStorageFoldableBaseOpened, ClothingOuterCoatLabCmo]
  id: ClothingOuterCoatLabCmoOpened
  name: "халат головного лікаря"

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterCoatRnd
  name: "лабораторний халат епістемології" # DeltaV - Epistemics Department replacing Science
  description: "Костюм, що захищає від незначних розливів хімічних речовин. Має фіолетову смужку на плечі."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/OuterClothing/Coats/epicoat.rsi # DeltaV - Epistemic lab coat
  - type: Clothing
    sprite: DeltaV/Clothing/OuterClothing/Coats/epicoat.rsi # DeltaV - Epistemic lab coat
  - type: Armor
    modifiers:
      coefficients:
        Caustic: 0.75

- type: entity
  parent: [ClothingOuterStorageFoldableBaseOpened, ClothingOuterCoatRnd]
  id: ClothingOuterCoatRndOpened
  name: "лабораторний халат вченого"

- type: entity
  parent: ClothingOuterStorageFoldableBase
  id: ClothingOuterCoatRobo
  name: "лабораторний халат робототехніка"
  description: "Більше схоже на ексцентричне пальто, ніж на лабораторний халат. Допомагає замаскувати плями крові як частину естетики. Поставляється з червоними наплічниками."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/labcoat_robo.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/labcoat_robo.rsi
  - type: Armor
    modifiers:
      coefficients:
        Caustic: 0.75

- type: entity
  parent: [ClothingOuterStorageFoldableBaseOpened, ClothingOuterCoatRobo]
  id: ClothingOuterCoatRoboOpened
  name: "лабораторний халат робототехніка"

- type: entity
  parent: ClothingOuterStorageFoldableBase
  id: ClothingOuterCoatRD
  name: "лабораторний халат голови наукового відділу"
  description: "Цей лабораторний халат, виготовлений за найсучаснішою технологією, захищає від радіації так само, як і експериментальний скафандр."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/rd_coat.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/rd_coat.rsi
  - type: Armor
    modifiers:
      coefficients:
        Caustic: 0.75
        Radiation: 0.9

- type: entity
  parent: [ClothingOuterStorageFoldableBaseOpened, ClothingOuterCoatRD]
  id: ClothingOuterCoatRDOpened
  name: "лабораторний халат голови наукового відділу"

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterCoatPirate
  name: "піратський одяг"
  description: "Ярр."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/pirate.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/pirate.rsi

- type: entity
  parent: [ClothingOuterArmorWarden, ClothingOuterStorageBase]
  id: ClothingOuterCoatWarden
  name: "броньована куртка наглядача"
  description: "Міцна, утилітарна куртка, призначена для захисту наглядача від будь-яких загроз, пов'язаних з перебуванням на гауптвахті."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/warden.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/warden.rsi

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterDameDane
  name: "пальто якудза"
  description: "П'ятниця..."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/damedanecoat.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/damedanecoat.rsi

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterClownPriest
  name: "мантія хонкоматері"
  description: "Призначений для клоуна."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/clownpriest.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/clownpriest.rsi

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterDogi
  name: "догі самурая"
  description: "Доґі - вид традиційного японського одягу. Доґі виготовляється з важкої, міцної тканини, він практичний в бою і стильний на вигляд. Прикрашається складними візерунками та вишивкою на спині."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/dogi.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/dogi.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.8
        Slash: 0.65
        Piercing: 0.85

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterCoatParamedicWB
  name: "вітровка парамедика"
  description: "Вірна вітровка парамедика, що захищає від космічного вітру."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/windbreaker_paramedic.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/windbreaker_paramedic.rsi

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterCoatSyndieCap
  name: "плащ синдикату"
  description: "Пальто синдикату виготовлене з міцної тканини, з позолоченими візерунками."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/syndicate/coatsyndiecap.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/syndicate/coatsyndiecap.rsi

- type: entity
  parent: ClothingOuterCoatHoSTrench
  id: ClothingOuterCoatSyndieCapArmored
  name: "броньований плащ синдикату"
  description: "Бронежилет синдикату виготовлений з міцної тканини, з позолоченими візерунками."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/syndicate/coatsyndiecaparmored.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/syndicate/coatsyndiecaparmored.rsi

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterCoatAMG
  name: "броньований медичний халат"
  description: "Варіант медичного халату з елементами бронежилета виглядає дивно, але ваше серце захищене."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/brigmedic.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/brigmedic.rsi
  - type: Armor # DeltaV - tweak for Velta armour
    modifiers:
      coefficients:
        Blunt: 0.85
        Slash: 0.85
        Piercing: 0.85
        Heat: 0.75
        Caustic: 0.75

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterCoatLabSeniorResearcher
  name: "халат старшого вченого"
  description: "Костюм, що захищає від незначних розливів хімічних речовин. Має фіолетовий комір та манжети на зап'ястях."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/labcoat_senior_researcher.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/labcoat_senior_researcher.rsi
  - type: Armor
    modifiers:
      coefficients:
        Caustic: 0.75

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterCoatLabSeniorPhysician
  name: "халат старшого лікаря"
  description: "Костюм, що захищає від незначних розливів хімічних речовин. Має світло-блакитні рукави та помаранчевий пояс."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/labcoat_senior_physician.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/labcoat_senior_physician.rsi
  - type: Armor
    modifiers:
      coefficients:
        Caustic: 0.75

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterCoatSpaceAsshole
  name: "плащ космічного мудака"
  description: "І тут з'явився він..."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/space_asshole.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/space_asshole.rsi

- type: entity
  parent: ClothingOuterStorageBase
  id: ClothingOuterCoatExpensive
  name: "дороге пальто"
  description: "Дуже пухнасте рожеве пальто, зроблене з дуже дорогого хутра (очевидно)."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/expensive_coat.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/expensive_coat.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterCoatModern
  name: "сучасне пальто"
  description: "Пальто, яке має приємне відчуття завершеності."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/modern_coat.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/modern_coat.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterTailcoat
  name: "фрак"
  description: "Модний фрак у вікторіанському стилі."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/tailcoat.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/tailcoat.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterCoatVictorian
  name: "чорне вікторіанське пальто"
  description: "Модне пальто у вікторіанському стилі."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/victorian_coat.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/victorian_coat.rsi

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterCoatVictorianRed
  name: "червоне вікторіанське пальто"
  description: "Модне пальто у вікторіанському стилі червоного відтінку."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/victorian_coat_red.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/victorian_coat_red.rsi

- type: entity
  parent: ClothingOuterStorageFoldableBase
  id: ClothingOuterCoatPeacoat
  name: "бушлат"
  description: "Добре скроєний, стильний бушлат."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/peacoat.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/peacoat.rsi

- type: entity
  parent: [ClothingOuterFoldableBaseOpened, ClothingOuterCoatPeacoat]
  id: ClothingOuterCoatPeacoatOpened
  name: "бушлат"

- type: entity
  parent: ClothingOuterStorageFoldableBase
  id: ClothingOuterCoatAsymmetric
  name: "асиметричне пальто"
  description: "Суцільне пальто без рукавів, яке закриває лише верхню частину тіла і задню частину ніг."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/asymmetric_coat.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/asymmetric_coat.rsi

- type: entity
  parent: [ClothingOuterFoldableBaseOpened, ClothingOuterCoatAsymmetric]
  id: ClothingOuterCoatAsymmetricOpened
  name: "асиметричне пальто"

- type: entity
  parent: ClothingOuterStorageFoldableBase
  id: ClothingOuterCoatSubmariner
  name: "пальто підводника"
  description: "Шкіряна куртка з коміром зі штучного хутра ідеально підходить для дослідження печер Submarine."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Coats/submariner.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Coats/submariner.rsi

- type: entity
  parent: [ClothingOuterFoldableBaseOpened, ClothingOuterCoatSubmariner]
  id: ClothingOuterCoatSubmarinerOpened
  name: "пальто підводника"
