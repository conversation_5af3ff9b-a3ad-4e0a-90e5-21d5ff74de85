- type: entity
  id: MachineAnomalyVessel
  parent: [ BaseMachinePowered, ConstructibleMachine ]
  name: "аномальне судно"
  description: "Контейнер, здатний використовувати сканування аномалії та перетворювати його на дослідницькі дані."
  components:
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/Anomaly/anomaly_vessel.rsi
    layers:
    - state: base
    - state: powered-1
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
    - state: anomaly-1
      visible: false
      shader: unshaded
      map: ["enum.AnomalyVesselVisualLayers.Base"]
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: Transform
    noRot: false
  - type: AnomalyVessel
  - type: ResearchClient
  - type: ActivatableUI
    key: enum.ResearchClientUiKey.Key
  - type: ActivatableUIRequiresPower
  - type: UserInterface
    interfaces:
      enum.ResearchClientUiKey.Key:
        type: ResearchClientBoundUserInterface
  - type: Machine
    board: AnomalyVesselCircuitboard
  - type: PointLight
    radius: 1.2
    energy: 2
    color: "#fca3c0"
  - type: Appearance
  - type: WiresPanel
  - type: AmbientSound
    enabled: false
    range: 3
    volume: -8
    sound:
      path: /Audio/Ambience/anomaly_drone.ogg
  - type: GenericVisualizer
    visuals:
      enum.PowerDeviceVisuals.Powered:
        enum.PowerDeviceVisualLayers.Powered:
          True: { visible: true }
          False: { visible: false }
      enum.AnomalyVesselVisuals.HasAnomaly:
        enum.AnomalyVesselVisualLayers.Base:
          True: { visible: true }
          False: { visible: false }
      enum.AnomalyVesselVisuals.AnomalyState:
        enum.PowerDeviceVisualLayers.Powered:
          1: { state: powered-1 }
          2: { state: powered-2 }
          3: { state: powered-3 }
        enum.AnomalyVesselVisualLayers.Base:
          1: { state: anomaly-1 }
          2: { state: anomaly-2 }
          3: { state: anomaly-3 }
      enum.WiresVisuals.MaintenancePanelState:
        enum.WiresVisualLayers.MaintenancePanel:
          True: { visible: false }
          False: { visible: true }
  - type: Explosive
    explosionType: Default
    maxIntensity: 20
    intensitySlope: 30
    totalIntensity: 30
    canCreateVacuum: false
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
      - !type:ExplodeBehavior
  - type: GuideHelp
    guides:
    - ScannersAndVessels

- type: entity
  id: MachineAnomalyVesselExperimental
  parent: MachineAnomalyVessel
  name: "експериментальний аномальний корабель"
  description: "Вдосконалений аномальний апарат, здатний до більшого дослідницького потенціалу ціною підвищеної летючості та низького рівня радіоактивного розпаду в навколишнє середовище."
  components:
  - type: Sprite
    sprite: Structures/Machines/Anomaly/adv_anomaly_vessel.rsi
    offset: 0,0.5
    drawdepth: Mobs
  - type: SpriteFade
  - type: AnomalyVessel
    basePointMultiplier: 2
  - type: RadiationSource
    slope: 0.1
    enabled: false
  - type: Machine
    board: AnomalyVesselExperimentalCircuitboard
  - type: Explosive
    explosionType: Default
    maxIntensity: 50
    intensitySlope: 7.5
    totalIntensity: 500
    canCreateVacuum: true

- type: entity
  id: MachineAPE
  parent: [ BaseMachinePowered, ConstructibleMachine ]
  name: "A.P.E."
  description: "Випромінювач аномальних частинок, здатний вистрілювати нестабільні частинки, які можуть взаємодіяти з аномаліями."
  components:
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/Anomaly/ape.rsi
    layers:
    - state: base
    - state: unshaded
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
    - state: firing
      shader: unshaded
      visible: false
      map: ["enum.EmitterVisualLayers.Lights"]
    - state: locked
      shader: unshaded
      visible: false
      map: ["enum.LockVisualLayers.Lock"]
  - type: Transform
    noRot: false
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 50
        mask:
        - MachineMask
        layer:
        - MachineLayer
  - type: Rotatable
    rotateWhileAnchored: true
  - type: Machine
    board: APECircuitboard
  - type: Lock
    locked: false
  - type: LockedWiresPanel
  - type: AccessReader
    access: [[ "Research" ]]
  - type: Emitter
    onState: firing
    powerUseActive: 100
    boltType: AnomalousParticleDelta
    underpoweredState: underpowered
    selectableTypes:
    - AnomalousParticleDelta
    - AnomalousParticleEpsilon
    - AnomalousParticleZeta
    - AnomalousParticleSigma
    setTypePorts:
      SetParticleDelta: AnomalousParticleDelta
      SetParticleEpsilon: AnomalousParticleEpsilon
      SetParticleZeta: AnomalousParticleZeta
      SetParticleSigma: AnomalousParticleSigma
    fireBurstSize: 1
    baseFireBurstDelayMin: 2
    baseFireBurstDelayMax: 6
  - type: ApcPowerReceiver
    powerLoad: 100
  - type: GuideHelp
    guides:
    - APE
  - type: Gun
    projectileSpeed: 4
    fireRate: 10 #just has to be fast enough to keep up with upgrades
    showExamineText: false
    selectedMode: SemiAuto
    availableModes:
      - SemiAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/taser2.ogg
  - type: Appearance
  - type: WiresPanel
  - type: WiresVisuals
  - type: GenericVisualizer
    visuals:
      enum.PowerDeviceVisuals.Powered:
        enum.PowerDeviceVisualLayers.Powered:
          True: { visible: true }
          False: { visible: false }
  - type: LockVisuals
  - type: LockedAnchorable
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: BasicDevice
  - type: WirelessNetworkConnection
    range: 200
  - type: DeviceLinkSink
    ports:
      - On
      - Off
      - Toggle
      - SetParticleDelta
      - SetParticleEpsilon
      - SetParticleZeta
      - SetParticleSigma

- type: entity
  id: MachineAnomalyGenerator
  parent: BaseMachinePowered
  name: "генератор аномалій"
  description: "Пік псевдонаукових технологій."
  placement:
    mode: AlignTileAny
  components:
  - type: StationAiWhitelist
  - type: SpriteFade
  - type: Sprite
    sprite: Structures/Machines/Anomaly/anomaly_generator.rsi
    offset: 0,1
    drawdepth: Mobs
    layers:
    - state: base
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
      visible: false
    - state: unshaded
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
    - state: inserting
      visible: false
      map: ["enum.MaterialStorageVisualLayers.Inserting"]
    - state: generating
      visible: false
      shader: unshaded
      map: ["enum.AnomalyGeneratorVisualLayers.Base"]
  - type: Transform
    anchored: true
  - type: ApcPowerReceiver
    powerLoad: 1500
  - type: ExtensionCableReceiver
  - type: AmbientSound
    range: 5
    volume: -6
    sound:
      path: /Audio/Ambience/Objects/anomaly_generator.ogg
  - type: Physics
    bodyType: Static
  - type: AnomalyGenerator
    generatingSound:
      path: /Audio/Machines/anomaly_generate.ogg
    generatingFinishedSound:
      path: /Audio/Machines/beep.ogg
  - type: MaterialStorage
    whitelist:
      tags:
        - Sheet
    materialWhiteList:
    - Plasma
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-1.5,-0.5,1.5,0.6"
        density: 50
        mask:
        - LargeMobMask
        layer:
        - WallLayer
  - type: Repairable
    fuelCost: 10
    doAfterDelay: 5
  - type: WiresPanel
  - type: Wires
    boardName: wires-board-name-anomalygenerator
    layoutId: AnomalyGenerator
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 500
      behaviors:
      - !type:DoActsBehavior
        acts: ["Breakage"]
  - type: ActivatableUI
    key: enum.AnomalyGeneratorUiKey.Key
  - type: ActivatableUIRequiresAccess
  - type: ActivatableUIRequiresPower
  - type: UserInterface
    interfaces:
        enum.AnomalyGeneratorUiKey.Key:
          type: AnomalyGeneratorBoundUserInterface
        enum.WiresUiKey.Key:
          type: WiresBoundUserInterface
  - type: Appearance
  - type: ActiveRadio
    channels:
    - Science
  - type: GenericVisualizer
    visuals:
      enum.PowerDeviceVisuals.Powered:
        enum.PowerDeviceVisualLayers.Powered:
          True: { visible: true }
          False: { visible: false }
      enum.AnomalyGeneratorVisuals.Generating:
        enum.AnomalyGeneratorVisualLayers.Base:
          True: { visible: true }
          False: { visible: false }
  - type: WiresVisuals
  - type: StaticPrice
    price: 5000
  - type: AccessReader
    access: [["Research"]]
  - type: GuideHelp
    guides:
    - AnomalousResearch
