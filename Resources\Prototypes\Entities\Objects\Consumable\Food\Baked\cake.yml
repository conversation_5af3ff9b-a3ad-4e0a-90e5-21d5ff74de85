# When adding new food also add to random spawner located in Resources\Prototypes\Entities\Markers\Spawners\Random\Food_Drinks\food_baked_whole.yml & food_baked_single.yml
# Base

- type: entity
  parent: FoodInjectableBase
  id: FoodCakeBase
  abstract: true
  components:
  - type: FlavorProfile
    flavors:
      - sweet
  - type: Food
  - type: Sprite
    sprite: Objects/Consumable/Food/Baked/cake.rsi
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 30
        reagents:
        - ReagentId: Nutriment
          Quantity: 20
        - ReagentId: Vitamin
          Quantity: 5
  - type: Item
    size: Normal
  - type: Tag
    tags:
    - Cake

- type: entity
  parent: FoodCakeBase
  id: FoodCakeSliceBase
  abstract: true
  description: "Просто шматочок торта, його вистачить на всіх."
  components:
  - type: FlavorProfile
    flavors:
      - sweet
  - type: Food
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 4
        - ReagentId: Vitamin
          Quantity: 1
  - type: Item
    size: Tiny
  - type: Tag
    tags:
    - Cake
    - Slice

# Custom Cake Example

- type: entity
  name: "чорничний торт"
  parent: FoodCakeBase
  id: FoodCakeBlueberry
  description: "Забруднює зуби."
  components:
  - type: Sprite
    layers:
    - state: plain
    - state: alpha-filling
      color: blue
  - type: SliceableFood
    slice: FoodCakeBlueberrySlice
  - type: Tag
    tags:
    - Cake
    - Fruit

- type: entity
  name: "шматочок чорниці"
  parent: FoodCakeSliceBase
  id: FoodCakeBlueberrySlice
  description: "Забруднює зуби."
  components:
  - type: Sprite
    layers:
    - state: plain-slice
    - state: alpha-slice-filling
      color: blue
  - type: Tag
    tags:
    - Cake
    - Fruit
    - Slice

# Cake

- type: entity
  name: "торт"
  parent: FoodCakeBase
  id: FoodCakePlain
  description: "Звичайний торт, а не брехня."
  components:
  - type: Sprite
    state: plain
  - type: SliceableFood
    slice: FoodCakePlainSlice

# Added in type lines above

- type: entity
  name: "шматочок торта"
  parent: FoodCakeSliceBase
  id: FoodCakePlainSlice
  components:
  - type: Sprite
    state: plain-slice
# Tastes like sweetness, cake.

- type: entity
  name: "морквяний пиріг"
  parent: FoodCakeBase
  id: FoodCakeCarrot
  description: "Улюблена пустеля одного васкальського кролика."
  components:
  - type: Sprite
    state: carrot
  - type: SliceableFood
    slice: FoodCakeCarrotSlice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 30
        reagents:
        - ReagentId: JuiceCarrot
          Quantity: 15
        - ReagentId: Sugar
          Quantity: 5
        - ReagentId: Vitamin
          Quantity: 5

- type: entity
  name: "шматочок морквяного пирога"
  parent: FoodCakeSliceBase
  id: FoodCakeCarrotSlice
  description: "Морквяна скибочка морквяного торта. Морква корисна для очей!"
  components:
  - type: Sprite
    state: carrot-slice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 6
        reagents:
        - ReagentId: JuiceCarrot
          Quantity: 3
        - ReagentId: Sugar
          Quantity: 1
        - ReagentId: Vitamin
          Quantity: 1

# Tastes like sweetness, cake, carrot.

- type: entity
  name: "мозговий торт"
  parent: FoodCakeBase
  id: FoodCakeBrain
  description: "Хрустке тістечко, схоже на торт."
  components:
  - type: Sprite
    state: brain
  - type: SliceableFood
    slice: FoodCakeBrainSlice


- type: entity
  name: "шматочок мозкового пирога"
  parent: FoodCakeSliceBase
  id: FoodCakeBrainSlice
  description: "Дозвольте розповісти вам дещо про пріони. ВОНИ ДУЖЕ СМАЧНІ."
  components:
  - type: Sprite
    state: brain-slice
# Tastes like sweetness, cake, brains.

- type: entity
  name: "сирний пиріг"
  parent: FoodCakeBase
  id: FoodCakeCheese
  description: "Небезпечно сирні."
  components:
  - type: Sprite
    state: cheese
  - type: SliceableFood
    slice: FoodCakeCheeseSlice

- type: entity
  name: "шматочок сирного пирога"
  parent: FoodCakeSliceBase
  id: FoodCakeCheeseSlice
  description: "Шматочок чистого задоволення."
  components:
  - type: Sprite
    state: cheese-slice
# Tastes like sweetness, cream cheese.

- type: entity
  name: "апельсиновий торт"
  parent: FoodCakeBase
  id: FoodCakeOrange
  description: "Торт з додаванням апельсина."
  components:
  - type: Sprite
    state: orange
  - type: SliceableFood
    slice: FoodCakeOrangeSlice
  - type: Tag
    tags:
    - Cake
    - Fruit

- type: entity
  name: "шматочок апельсинового пирога"
  parent: FoodCakeSliceBase
  id: FoodCakeOrangeSlice
  components:
  - type: Sprite
    state: orange-slice
  - type: Tag
    tags:
    - Cake
    - Fruit
    - Slice
# Tastes like sweetness, cake, oranges.

- type: entity
  name: "лаймовий пиріг"
  parent: FoodCakeBase
  id: FoodCakeLime
  description: "Торт з додаванням лайму."
  components:
  - type: Sprite
    state: lime
  - type: SliceableFood
    slice: FoodCakeLimeSlice
  - type: Tag
    tags:
    - Cake
    - Fruit

- type: entity
  name: "шматочок лаймового торта"
  parent: FoodCakeSliceBase
  id: FoodCakeLimeSlice
  components:
  - type: Sprite
    state: lime-slice
  - type: Tag
    tags:
    - Cake
    - Fruit
    - Slice
# Tastes like sweetness, cake, limes.

- type: entity
  name: "лимонний торт"
  parent: FoodCakeBase
  id: FoodCakeLemon
  description: "Торт з додаванням лимона."
  components:
  - type: Sprite
    state: lemon
  - type: SliceableFood
    slice: FoodCakeLemonSlice
  - type: Tag
    tags:
    - Cake
    - Fruit

- type: entity
  name: "шматочок лимонного торта"
  parent: FoodCakeSliceBase
  id: FoodCakeLemonSlice
  components:
  - type: Sprite
    state: lemon-slice
  - type: Tag
    tags:
    - Cake
    - Fruit
    - Slice
# Tastes like sweetness, cake, lemons.

- type: entity
  name: "лимонний торт"
  parent: FoodCakeBase
  id: FoodCakeLemoon
  description: "Торт, який представляє місяць землі"
  components:
  - type: Sprite
    state: lemoon
  - type: FlavorProfile
    flavors:
      - lemoon
      - berry
  - type: SliceableFood
    slice: FoodCakeLemoonSlice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 30
        reagents:
        - ReagentId: Nutriment
          Quantity: 15
        - ReagentId: Vitamin
          Quantity: 5
        - ReagentId: Milk
          Quantity: 5
  - type: Tag
    tags:
    - Cake
    - Fruit

- type: entity
  name: "шматочок лимонного торта"
  parent: FoodCakeSliceBase
  id: FoodCakeLemoonSlice
  description: "Осколок місяця, має запах молока."
  components:
  - type: Sprite
    state: lemoon-slice
  - type: FlavorProfile
    flavors:
      - lemoon
      - berry
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
        - ReagentId: Vitamin
          Quantity: 1
        - ReagentId: Milk
          Quantity: 1
  - type: Tag
    tags:
    - Cake
    - Fruit
    - Slice

- type: entity
  name: "шоколадний торт"
  parent: FoodCakeBase
  id: FoodCakeChocolate
  description: "Торт з додаванням шоколаду."
  components:
  - type: Sprite
    state: chocolate
  - type: SliceableFood
    slice: FoodCakeChocolateSlice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 35
        reagents:
        - ReagentId: Nutriment
          Quantity: 20
        - ReagentId: Theobromine
          Quantity: 5
        - ReagentId: Vitamin
          Quantity: 5

- type: entity
  name: "шматочок шоколадного торта"
  parent: FoodCakeSliceBase
  id: FoodCakeChocolateSlice
  components:
  - type: Sprite
    state: chocolate-slice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 8
        reagents:
        - ReagentId: Nutriment
          Quantity: 4
        - ReagentId: Theobromine
          Quantity: 1
        - ReagentId: Vitamin
          Quantity: 1
# Tastes like sweetness, cake, chocolate.

- type: entity
  name: "яблучний пиріг"
  parent: FoodCakeBase
  id: FoodCakeApple
  description: "Пиріг з яблуком у центрі."
  components:
  - type: Sprite
    state: apple
  - type: SliceableFood
    slice: FoodCakeAppleSlice
  - type: Tag
    tags:
    - Cake
    - Fruit

- type: entity
  name: "шматочок яблучного пирога"
  parent: FoodCakeSliceBase
  id: FoodCakeAppleSlice
  description: "Шматочок небесного пирога."
  components:
  - type: Sprite
    state: apple-slice
  - type: Tag
    tags:
    - Cake
    - Fruit
    - Slice
# Tastes like sweetness, cake, slime.

- type: entity
  name: "слизовий торт"
  parent: FoodCakeBase
  id: FoodCakeSlime
  description: "Торт зі слизу. Напевно, не під напругою."
  components:
  - type: Sprite
    state: slime
  - type: SliceableFood
    slice: FoodCakeSlimeSlice

- type: entity
  name: "шматочок слизового торта"
  parent: FoodCakeSliceBase
  id: FoodCakeSlimeSlice
  description: "Шматочок торта зі слизу."
  components:
  - type: Sprite
    state: slime-slice
# Tastes like sweetness, cake, slime.

- type: entity
  name: "гарбузово-пряний пиріг"
  parent: FoodCakeBase
  id: FoodCakePumpkin
  description: "Порожнистий пиріг зі справжнім гарбузом."
  components:
  - type: Sprite
    state: pumpkinspice
  - type: SliceableFood
    slice: FoodCakePumpkinSlice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50
        reagents:
        - ReagentId: Nutriment
          Quantity: 32
        - ReagentId: Vitamin
          Quantity: 11
  - type: Tag
    tags:
    - Cake
    - Fruit

- type: entity
  name: "шматочок гарбузово-пряного пирога"
  parent: FoodCakeSliceBase
  id: FoodCakePumpkinSlice
  description: "Пряний шматочок гарбузової смакоти."
  components:
  - type: Sprite
    state: pumpkinspice-slice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 6.4
        - ReagentId: Vitamin
          Quantity: 2.2
  - type: Tag
    tags:
    - Cake
    - Fruit
    - Slice
# Tastes like sweetness, cake, pumpkin.

- type: entity
  name: "різдвяний торт"
  parent: FoodCakeBase
  id: FoodCakeChristmas
  description: "Торт, зроблений з Різдва."
  components:
  - type: Sprite
    state: christmas
  - type: SliceableFood
    slice: FoodCakeChristmasSlice

- type: entity
  name: "шматочок різдвяного пирога"
  parent: FoodCakeSliceBase
  id: FoodCakeChristmasSlice
  components:
  - type: Sprite
    state: christmas-slice
# Tastes like sweetness, cake, christmas.

- type: entity
  name: "святковий торт"
  parent: FoodCakeBase
  id: FoodCakeBirthday
  description: "З днем народження, маленький клоуне..."
  components:
  - type: Sprite
    state: birthday
  - type: SliceableFood
    slice: FoodCakeBirthdaySlice

- type: entity
  name: "шматочок святкового торта"
  parent: FoodCakeSliceBase
  id: FoodCakeBirthdaySlice
  description: "Шматочок твого дня народження."
  components:
  - type: Sprite
    state: birthday-slice
# Tastes like sweetness, cake, christmas.

- type: entity
  name: "ванільний торт"
  parent: FoodCakeBase
  id: FoodCakeVanilla
  description: "Ванільний торт з ванільною глазур'ю."
  components:
  - type: Sprite
    state: vanilla
  - type: SliceableFood
    slice: FoodCakeVanillaSlice
  - type: SolutionContainerManager #TODO Sprinkles
    solutions:
      food:
        maxVol: 45
        reagents:
        - ReagentId: Nutriment
          Quantity: 20
        - ReagentId: Vitamin
          Quantity: 5
        - ReagentId: Sugar
          Quantity: 15

- type: entity
  name: "шматочок ванільного торта"
  parent: FoodCakeSliceBase
  id: FoodCakeVanillaSlice
  description: "Шматочок ванільного торта з ванільною глазур'ю."
  components:
  - type: Sprite
    state: vanilla-slice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 45
        reagents:
        - ReagentId: Nutriment
          Quantity: 4
        - ReagentId: Vitamin
          Quantity: 1
        - ReagentId: Sugar
          Quantity: 3
# Tastes like sweetness, cake, vanilla.

- type: entity
  name: "клоунський торт"
  parent: FoodCakeBase
  id: FoodCakeClown
  description: "Кумедний торт з обличчям клоуна."
  components:
  - type: Sprite
    state: clown
  - type: SliceableFood
    slice: FoodCakeClownSlice
  - type: SolutionContainerManager #TODO Sprinkles
    solutions:
      food:
        maxVol: 45
        reagents:
        - ReagentId: Nutriment
          Quantity: 20
        - ReagentId: Vitamin
          Quantity: 5
        - ReagentId: Sugar
          Quantity: 15

- type: entity
  name: "шматочок клоунського торта"
  parent: FoodCakeSliceBase
  id: FoodCakeClownSlice
  description: "Нарізка невдалих жартів і безглуздого реквізиту."
  components:
  - type: Sprite
    state: clown-slice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 45
        reagents:
        - ReagentId: Nutriment
          Quantity: 4
        - ReagentId: Vitamin
          Quantity: 1
        - ReagentId: Sugar
          Quantity: 3
# Tastes like sweetness, cake, clown.

- type: entity
  name: "торт космонавта"
  parent: FoodCakeBase
  id: FoodCakeSpaceman
  description: "Торт з глазур'ю у вигляді сурми космонавта."
  components:
  - type: Sprite
    state: trumpet
  - type: SliceableFood
    slice: FoodCakeSpacemanSlice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 45
        reagents:
        - ReagentId: Nutriment
          Quantity: 20
        - ReagentId: Vitamin
          Quantity: 5
        - ReagentId: Omnizine #This is a really rare cake with healing stuff and we don't have any of its chems yet
          Quantity: 15

- type: entity
  name: "шматок торта космонавта"
  parent: FoodCakeSliceBase
  id: FoodCakeSpacemanSlice
  description: "Торт з глазур'ю у вигляді сурми космонавта."
  components:
  - type: Sprite
    state: trumpet-slice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 4
        - ReagentId: Vitamin
          Quantity: 1
        - ReagentId: Omnizine
          Quantity: 3
# Tastes like sweetness, cake, jam.

- type: entity
  name: "пиріг"
  id: MobCatCake
  parent: FoodCakeBase
  description: "Це торт. Це кіт. Це торт."
  components:
  - type: Sprite
    noRot: true
    drawdepth: Mobs
    sprite: Mobs/Pets/cat.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: cak
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 50
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: RotationVisuals
    defaultRotation: 0
    horizontalRotation: 0
  - type: Item
    size: Ginormous
    heldPrefix: cak
  - type: Clothing
    quickEquip: false
    sprite: Mobs/Pets/cat.rsi
    equippedPrefix: cak
    slots:
    - HEAD
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
  - type: GhostRole
    prob: 1
    name: ghost-role-information-Cak-name
    allowMovement: true
    description: ghost-role-information-Cak-description
    rules: ghost-role-information-Cak-rules
    raffle:
      settings: short
  - type: GhostTakeoverAvailable
  - type: OwOAccent
  - type: Speech
    speechSounds: Cat
    speechVerb: SmallMob
  - type: MovementSpeedModifier
    baseWalkSpeed : 5
    baseSprintSpeed : 3
  - type: Tag
    tags:
    - VimPilot
    - DoorBumpOpener
    - Cake
  - type: CanEscapeInventory
    baseResistTime: 2
  - type: Puller
    needsHands: false
  - type: Examiner
  - type: DoAfter
  - type: CombatMode
  - type: MeleeWeapon
    soundHit:
      path: /Audio/Animals/cat_hiss.ogg
    hidden: true
    damage:
      groups:
        Brute: 1

# Suppermatter
# I can't figure out where to put this. It kind of acts like a cake I guess?

- type: entity
  name: "надречовина"
  parent: FoodCakeBase
  id: FoodCakeSuppermatter
  description: "Надзвичайно щільна і потужна їжа."
  components:
  - type: Sprite
    state: suppermatter
  - type: SliceableFood
    slice: FoodCakeSuppermatterSlice
    count: 8
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 48
        reagents:
        - ReagentId: Nutriment
          Quantity: 48
  - type: Food
    transferAmount: 12
  - type: Item
    size: Normal
  - type: PointLight
    color: "#FFFF00"
    radius: 2
    energy: 1.4

- type: entity
  name: "осколок суперматерії"
  parent: FoodCakeSliceBase
  id: FoodCakeSuppermatterSlice
  description: "Єдина порція енергії."
  components:
  - type: Sprite
    state: suppermatter-shard
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 12
        reagents:
        - ReagentId: Nutriment
          Quantity: 12
  - type: Food
    transferAmount: 3
  - type: PointLight
    color: "#FFFF00"
    radius: 1.4
    energy: 1.4
  - type: Tag
    tags:
    - Slice
  - type: FoodSequenceElement
    entries:
      Taco: Suppermatter
      Burger: SuppermatterBurger
