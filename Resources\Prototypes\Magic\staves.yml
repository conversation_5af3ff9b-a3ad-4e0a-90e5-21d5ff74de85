﻿# non-projectile / "gun" staves

# wand that gives lights an RGB effect.
- type: entity
  id: RGBStaff
  parent: BaseItem
  name: "RGB посох"
  description: "Допомагає виправити нестачу RGB-обладнання на станції."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Basic/staves.rsi
    layers:
    - state: nothing
    - state: nothing-unshaded
      shader: unshaded
  - type: ActionOnInteract
    actions:
    - ActionRgbLight
  - type: Item
    size: Normal
    inhandVisuals:
      left:
      - state: staff-inhand-left
      - state: staff-inhand-left-unshaded
        shader: unshaded
      right:
      - state: staff-inhand-right
      - state: staff-inhand-right-unshaded
        shader: unshaded
  - type: RgbLightController
  - type: PointLight
    enabled: true
    radius: 2

- type: entity
  id: ActionRgbLight
  categories: [ HideSpawnMenu ]
  components:
  - type: EntityTargetAction
    whitelist: { components: [ PointLight ] }
    charges: 25
    sound: /Audio/Magic/blink.ogg
    event: !type:ChangeComponentsSpellEvent
      toAdd:
      - type: RgbLightController
