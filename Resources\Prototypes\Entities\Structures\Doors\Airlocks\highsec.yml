- type: entity
  id: HighSecDoor
  parent: BaseStructure
  name: "двері з високим ступенем захисту"
  description: "Тримає погане назовні, а хороше - всередині."
  placement:
    mode: SnapgridCenter
  components:
  - type: StationAiWhitelist
  - type: InteractionOutline
  - type: Sprite
    sprite: Structures/Doors/Airlocks/highsec/highsec.rsi
    layers:
    - state: closed
      map: ["enum.DoorVisualLayers.Base"]
    - state: closed_unlit
      shader: unshaded
      map: ["enum.DoorVisualLayers.BaseUnlit"]
    - state: welded
      map: ["enum.WeldableLayers.BaseWelded"]
    - state: bolted_unlit
      shader: unshaded
      map: ["enum.DoorVisualLayers.BaseBolted"]
    - state: emergency_unlit
      map: ["enum.DoorVisualLayers.BaseEmergencyAccess"]
      shader: unshaded
    - state: panel_open
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AnimationPlayer
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.49,-0.49,0.49,0.49" # don't want this colliding with walls or they won't close
        density: 100
        mask:
        - FullTileMask
        layer:
        - WallLayer
  - type: ContainerFill
    containers:
      board: [ DoorElectronics ]
  - type: ContainerContainer
    containers:
      board: !type:Container
  - type: Door
    crushDamage:
      types:
        Blunt: 50
    openSound:
      path: /Audio/Machines/airlock_open.ogg
    closeSound:
      path: /Audio/Machines/airlock_close.ogg
    denySound:
      path: /Audio/Machines/airlock_deny.ogg
  - type: Weldable
    fuel: 10
    time: 10
  - type: Airlock
  - type: NavMapDoor
  - type: DoorBolt
  - type: AccessReader
    containerAccessProvider: board
  - type: Appearance
  - type: WiresVisuals
  - type: ApcPowerReceiver
    powerLoad: 20
  - type: ExtensionCableReceiver
  - type: Electrified
    enabled: false
    usesApcPower: true
  - type: WiresPanel
  - type: WiresPanelSecurity
    securityLevel: maxSecurity
  - type: Wires
    boardName: wires-board-name-highsec
    layoutId: HighSec
    alwaysRandomize: true
  - type: UserInterface
    interfaces:
      enum.AiUi.Key:
        type: StationAiBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: Airtight
  - type: Occluder
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: StrongMetallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 1500
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: IconSmooth
    key: walls
    mode: NoSprite
  - type: Construction
    graph: Airlock
    node: highSecDoor
  - type: Tag
    tags:
      - HighSecDoor
      # This tag is used to nagivate the Airlock construction graph. It's needed because this construction graph is shared between Airlock, AirlockGlass, and HighSecDoor
  - type: PsionicInsulation # Psifoil shield the high sec airlocks.
