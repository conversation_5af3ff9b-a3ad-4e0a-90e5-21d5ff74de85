- type: entity
  id: BaseBorgModule
  parent: BaseItem
  name: "модуль borg"
  description: "Технологія, яка дає кіборгам нові здібності."
  abstract: true
  components:
  - type: Item
    storedRotation: -90
  - type: Sprite
    sprite: Objects/Specific/Robotics/borgmodule.rsi
  - type: BorgModule
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: no-action }
  - type: StaticPrice
    price: 100
  - type: Tag
    tags:
    - BorgModuleGeneric
  - type: GuideHelp
    guides:
      - Cyborgs

- type: entity
  id: BaseProviderBorgModule
  abstract: true
  components:
  - type: SelectableBorgModule
  - type: ContainerContainer
    containers:
      provided_container: !type:Container { }

- type: entity
  id: ActionBorgSwapModule
  name: "Модуль заміни"
  description: "Виберіть цей модуль, щоб скористатися інструментами, які він надає."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    itemIconStyle: BigAction
    useDelay: 0.5
    event: !type:BorgModuleActionSelectedEvent

- type: entity
  id: BaseBorgModuleCargo
  parent: BaseBorgModule
  abstract: true
  components:
  - type: Tag
    tags:
    - BorgModuleCargo

- type: entity
  id: BaseBorgModuleEngineering
  parent: BaseBorgModule
  abstract: true
  components:
  - type: Tag
    tags:
    - BorgModuleEngineering

- type: entity
  id: BaseBorgModuleJanitor
  parent: BaseBorgModule
  abstract: true
  components:
  - type: Tag
    tags:
    - BorgModuleJanitor

- type: entity
  id: BaseBorgModuleMedical
  parent: BaseBorgModule
  abstract: true
  components:
  - type: Tag
    tags:
    - BorgModuleMedical

- type: entity
  id: BaseBorgModuleService
  parent: BaseBorgModule
  abstract: true
  components:
  - type: Tag
    tags:
    - BorgModuleService

- type: entity
  id: BaseBorgModuleSyndicate
  parent: BaseBorgModule
  abstract: true
  components:
    - type: Tag
      tags:
        - BorgModuleSyndicate

- type: entity
  id: BaseBorgModuleSyndicateAssault
  parent: BaseBorgModule
  abstract: true
  components:
    - type: Tag
      tags:
        - BorgModuleSyndicateAssault

# generic modules
- type: entity
  id: BorgModuleCable
  parent: [ BaseBorgModule, BaseProviderBorgModule ]
  name: "кабельний модуль кіборг"
  components:
  - type: Sprite
    layers:
    - state: generic
    - state: icon-cables
  - type: ItemBorgModule
    items:
    - CableApcStackLingering10
    - CableMVStackLingering10
    - CableHVStackLingering10
    - Wirecutter
    - trayScanner
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: wire-module }

- type: entity
  id: BorgModuleFireExtinguisher
  parent: [ BaseBorgModule, BaseProviderBorgModule ]
  name: "кібермодуль вогнегасника"
  components:
  - type: Sprite
    layers:
    - state: generic
    - state: icon-fire-extinguisher
  - type: ItemBorgModule
    items:
    - FireExtinguisher
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: extinguisher-module }

- type: entity
  id: BorgModuleGPS
  parent: [ BaseBorgModule, BaseProviderBorgModule ]
  name: "GPS модуль кіборга"
  components:
  - type: Sprite
    layers:
    - state: generic
    - state: icon-gps
  - type: ItemBorgModule
    items:
    - HandheldGPSBasic
    - HandHeldMassScannerBorg
    - HandheldStationMapUnpowered
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: gps-module }

- type: entity
  id: BorgModuleRadiationDetection
  parent: [ BaseBorgModule, BaseProviderBorgModule ]
  name: "модуль виявлення радіації для кіборгів"
  components:
  - type: Sprite
    layers:
    - state: generic
    - state: icon-radiation
  - type: ItemBorgModule
    items:
    - GeigerCounter
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: geiger-module }

- type: entity
  id: BorgModuleTool
  parent: [ BaseBorgModule, BaseProviderBorgModule ]
  name: "інструментальний модуль кіборга"
  components:
  - type: Sprite
    layers:
    - state: generic
    - state: icon-tools
  - type: ItemBorgModule
    items:
    - Crowbar
    - Wrench
    - Screwdriver
    - Wirecutter
    - Multitool
    - WelderIndustrial
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: tool-module }

# cargo modules
- type: entity
  id: BorgModuleAppraisal
  parent: [ BaseBorgModuleCargo, BaseProviderBorgModule ]
  name: "оціночний модуль \"кіборг"
  components:
  - type: Sprite
    layers:
    - state: cargo
    - state: icon-appraisal
  - type: ItemBorgModule
    items:
    - AppraisalTool
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: appraisal-module }

- type: entity
  id: BorgModuleMining
  parent: [ BaseBorgModuleCargo, BaseProviderBorgModule ]
  name: "шахтарський кіборг-модуль"
  components:
  - type: Sprite
    layers:
    - state: cargo
    - state: icon-mining
  - type: ItemBorgModule
    items:
    - MiningDrill
    # - Shovel # Goobstation
    - OreBag
    - Crowbar
    - RadioHandheld
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: mining-module }

- type: entity
  id: BorgModuleJetpack
  parent: [ BaseBorgModuleCargo, BaseProviderBorgModule ]
  name: "реактивний модуль кіборга"
  description: "Технологічний пристрій, що надає кіборгам нові здібності. Має бути завантажена кіборгом, перш ніж ви зможете перезарядити реактивний ранець."
  components:
  - type: Sprite
    layers:
    - state: cargo
    - state: icon-jetpack
  - type: BorgJetpack
  - type: ItemBorgModule
    items:
    - JetpackMicroFilled
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: jetpack-module }

- type: entity
  id: BorgModulePka
  parent: [ BaseBorgModuleCargo, BaseProviderBorgModule ]
  name: "прото-кінетичний прискорювач кіборг-модуль"
  components:
  - type: Sprite
    layers:
    - state: cargo
    - state: icon-pka
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: pka-module }

# - type: entity
#   id: BorgModuleGrapplingGun
#   parent: [ BaseBorgModuleCargo, BaseProviderBorgModule ]
#   name: grappling gun cyborg module
#   components:
#   - type: Sprite
#     layers:
#     - state: cargo
#     - state: icon-grappling-gun
#   - type: ItemBorgModule
#     items:
#     - WeaponGrapplingGun
#     - HandheldGPSBasic
#   - type: BorgModuleIcon
#     icon: { sprite: Interface/Actions/actions_borg.rsi, state: grappling-module }

# engineering modules
- type: entity
  id: BorgModuleAdvancedTool
  parent: [ BaseBorgModuleEngineering, BaseProviderBorgModule ]
  name: "вдосконалений інструментальний модуль кіборга"
  components:
  - type: Sprite
    layers:
    - state: engineering
    - state: icon-tools-adv
  - type: ItemBorgModule
    items:
    - Omnitool
    - WelderExperimental
    - NetworkConfigurator
    - RemoteSignaller
    - GasAnalyzer
    - GeigerCounter
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: adv-tools-module }

- type: entity
  id: BorgModuleConstruction
  parent: [ BaseBorgModuleEngineering, BaseProviderBorgModule ]
  name: "будівельний модуль-кіборг"
  components:
  - type: Sprite
    layers:
    - state: engineering
    - state: icon-construction
  - type: ItemBorgModule
    items:
    - SheetSteelLingering0
    - SheetGlassLingering0
    - PartRodMetalLingering0
    - FloorTileItemSteelLingering0
    # Frontier: droppable borg items
    droppableItems:
    - id: APCElectronics
      whitelist:
        components:
        - Electronics
        - MachineBoard
    - id: CapacitorStockPart
      whitelist:
        components:
        - StockPart
    # End Frontier: droppable borg items
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: construction-module }


- type: entity
  id: BorgModuleRCD
  parent: [ BaseBorgModuleEngineering, BaseProviderBorgModule ]
  name: "Модуль ПЗВ кіборг"
  components:
  - type: Sprite
    layers:
    - state: engineering
    - state: icon-rcd
  - type: ItemBorgModule
    items:
    - RCDRecharging
    - RPDRecharging
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: rcd-module }

# janitorial modules (this gets its own unique things because janis are epic)
- type: entity
  id: BorgModuleLightReplacer
  parent: [ BaseBorgModuleJanitor, BaseProviderBorgModule ]
  name: "кіборг-модуль: замінник світла"
  components:
  - type: Sprite
    layers:
    - state: janitor
    - state: icon-light-replacer
  - type: ItemBorgModule
    items:
    - LightReplacer
    - Crowbar
    - Screwdriver
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: light-replacer-module }

- type: entity
  id: BorgModuleCleaning
  parent: [ BaseBorgModuleJanitor, BaseProviderBorgModule ]
  name: "очисний кіборг-модуль"
  components:
  - type: Sprite
    layers:
    - state: janitor
    - state: icon-mop
  - type: ItemBorgModule
    items:
    - MopItem
    - Bucket
    - TrashBag
    - Plunger
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: cleaning-module }

- type: entity
  id: BorgModuleAdvancedCleaning
  parent: [ BaseBorgModuleJanitor, BaseProviderBorgModule ]
  name: "вдосконалений модуль кіборга для прибирання"
  components:
  - type: Sprite
    layers:
    - state: janitor
    - state: icon-mop-adv
  - type: ItemBorgModule
    items:
    - AdvMopItem
    - HoloprojectorBorg
    - SprayBottleSpaceCleaner
    - Dropper
    - TrashBag
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: adv-cleaning-module }

# medical modules
- type: entity
  id: BorgModuleDiagnosis # todo: reuse when med refractor is finished
  parent: [ BaseBorgModuleMedical, BaseProviderBorgModule ]
  name: "модуль діагностики кіборгів"
  components:
  - type: Sprite
    layers:
    - state: medical
    - state: icon-diagnosis
  - type: ItemBorgModule
    items:
    - HandheldHealthAnalyzerUnpowered
    - ClothingNeckStethoscope
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: diagnosis-module }

- type: entity
  id: BorgModuleTreatment
  parent: [ BaseBorgModuleMedical, BaseProviderBorgModule ]
  name: "лікувальний модуль кіборга"
  components:
  - type: Sprite
    layers:
    - state: medical
    - state: icon-treatment
  - type: ItemBorgModule
    items:
    - Brutepack10Lingering
    - Ointment10Lingering
    - Gauze10Lingering
    - Bloodpack10Lingering
    - Syringe
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: treatment-module }

- type: entity
  id: BorgModuleDefibrillator
  parent: [ BaseBorgModuleMedical, BaseProviderBorgModule ]
  name: "модуль дефібрилятора-кіборга"
  components:
  - type: Sprite
    layers:
    - state: medical
    - state: icon-defib
  - type: ItemBorgModule
    items:
    - DefibrillatorOneHandedUnpowered
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: defib-module }

- type: entity
  id: BorgModuleAdvancedTreatment
  parent: [ BaseBorgModuleMedical, BaseProviderBorgModule ]
  name: "модуль розширеного лікування кіборгів"
  components:
  - type: Sprite
    layers:
    - state: medical
    - state: icon-chemist
  - type: ItemBorgModule
    items:
    - HandheldHealthAnalyzerUnpowered
#    - Beaker # Frontier
#    - Beaker # Frontier
#    - BorgDropper # Frontier
    - BorgHypo
    # Frontier: droppable borg items
    droppableItems:
    - id: Beaker
      whitelist:
        components:
        - FitsInDispenser
    # End Frontier: droppable borg items
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: adv-diagnosis-module }

# science modules
# todo: if science ever gets their own custom robot, add more "sci" modules.
- type: entity
  id: BorgModuleArtifact
  parent: [ BaseBorgModule, BaseProviderBorgModule ]
  name: "артефактний модуль кіборга"
  components:
  - type: Sprite
    layers:
    - state: science
    - state: icon-artifacts
  - type: ItemBorgModule
    items:
    - NodeScanner
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: node-scanner-module }

- type: entity
  id: BorgModuleAnomaly
  parent: [ BaseBorgModule, BaseProviderBorgModule ]
  name: "модуль аномального кіборга"
  components:
  - type: Sprite
    layers:
    - state: science
    - state: icon-anomalies
  - type: ItemBorgModule
    items:
    - AnomalyScanner
    - AnomalyLocatorUnpowered
    - AnomalyLocatorWideUnpowered
    - RemoteSignaller
    - Multitool
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: anomaly-module }

# service modules
- type: entity
  id: BorgModuleService
  parent: [ BaseBorgModuleService, BaseProviderBorgModule ]
  name: "сервісний модуль кіборга"
  components:
  - type: Sprite
    layers:
    - state: service
    - state: icon-pen
  - type: ItemBorgModule
    items:
    - Pen
    - BooksBag
    - HandLabeler
    - Lighter
    # Frontier: droppable borg items
    # - DrinkShaker
    # - BorgDropper
    droppableItems:
    - id: DrinkShaker
      whitelist:
        components:
        - FitsInDispenser
    # End Frontier: droppable borg items
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: service-module }

- type: entity
  id: BorgModuleMusique
  parent: [ BaseBorgModuleService, BaseProviderBorgModule ]
  name: "модуль музичного кіборга"
  components:
  - type: Sprite
    layers:
    - state: service
    - state: icon-musique
  - type: ItemBorgModule
    items:
    - SynthesizerInstrument
    - ElectricGuitarInstrument
    - SaxophoneInstrument
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: musical-module }

- type: entity
  id: BorgModuleGardening
  parent: [ BaseBorgModuleService, BaseProviderBorgModule ]
  name: "модуль садівничого кіборга"
  components:
  - type: Sprite
    layers:
    - state: service
    - state: icon-gardening
  - type: ItemBorgModule
    items:
    - HydroponicsToolMiniHoe
    - HydroponicsToolSpade
    - HydroponicsToolClippers
    - Bucket
    # Frontier: droppable borg items
    droppableItems:
    - id: AppleSeeds
      whitelist:
        components:
        - Seed
    # End Frontier: droppable borg items
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: gardening-module }

- type: entity
  id: BorgModuleHarvesting
  parent: [ BaseBorgModuleService, BaseProviderBorgModule ]
  name: "модуль збирання врожаю кіборга"
  components:
  - type: Sprite
    layers:
    - state: service
    - state: icon-harvesting
  - type: ItemBorgModule
    items:
    - HydroponicsToolScythe
    - HydroponicsToolHatchet
    - PlantBag
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: harvesting-module }

- type: entity
  id: BorgModuleClowning
  parent: [ BaseBorgModuleService, BaseProviderBorgModule ]
  name: "модуль клоунського кіборга"
  components:
  - type: Sprite
    layers:
    - state: service
    - state: icon-clown
  - type: ItemBorgModule
    items:
    - BikeHorn
    - ClownRecorder
    - BikeHornInstrument
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: clowning-module }

#syndicate modules
- type: entity
  id: BorgModuleSyndicateWeapon
  parent: [ BaseBorgModule, BaseProviderBorgModule ]
  name: "збройовий кіборг-модуль"
  components:
  - type: Sprite
    layers:
    - state: syndicate
    - state: icon-syndicate
  - type: ItemBorgModule
    items:
    - WeaponAdvancedLaser
    - Machete
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: syndicate-weapon-module }

- type: entity
  id: BorgModuleOperative
  parent: [ BaseBorgModuleSyndicate, BaseProviderBorgModule ]
  name: "оперативний модуль кіборгів"
  description: "Модуль, який поставляється з ломом, Емагом і синдикативною указкою."
  components:
    - type: Sprite
      layers:
      - state: syndicate
      - state: icon-syndicate
    - type: ItemBorgModule
      items:
      - Crowbar
      - Emag
      - PinpointerSyndicateNuclear
    - type: BorgModuleIcon
      icon: { sprite: Interface/Actions/actions_borg.rsi, state: syndicate-operative-module }

- type: entity
  id: BorgModuleEsword
  parent: [ BaseBorgModuleSyndicate, BaseProviderBorgModule ]
  name: "модуль кіборга з енергетичним мечем"
  description: "Модуль, який постачається з подвійним енергетичним мечем."
  components:
    - type: Sprite
      layers:
      - state: syndicate
      - state: icon-syndicate
    - type: ItemBorgModule
      items:
      - EnergySwordDouble
      - PinpointerSyndicateNuclear
    - type: BorgModuleIcon
      icon: { sprite: Interface/Actions/actions_borg.rsi, state: syndicate-esword-module }

- type: entity
  id: BorgModuleL6C
  parent: [ BaseBorgModuleSyndicateAssault, BaseProviderBorgModule ]
  name: "Модуль кіборга L6C ROW"
  description: "Модуль, який постачається з L6C."
  components:
    - type: Sprite
      layers:
      - state: syndicate
      - state: icon-syndicate
    - type: ItemBorgModule
      items:
        - WeaponLightMachineGunL6C
        - PinpointerSyndicateNuclear
    - type: BorgModuleIcon
      icon: { sprite: Interface/Actions/actions_borg.rsi, state: syndicate-l6c-module }

- type: entity
  id: BorgModuleMartyr
  parent: [ BaseBorgModule, BaseProviderBorgModule ]
  name: "модуль кіборга-мученика"
  description: "Модуль з вибухівкою, з якою ви, ймовірно, не захочете поводитися самостійно."
  components:
    - type: Sprite
      layers:
        - state: syndicateborgbomb
        - state: icon-bomb
    - type: ItemBorgModule
      items:
        - SelfDestructSeq
    - type: BorgModuleIcon
      icon: { sprite: Interface/Actions/actions_borg.rsi, state: syndicate-martyr-module }

# Shitmed Modules

- type: entity
  id: BorgModuleSurgery
  parent: [ BaseBorgModuleMedical, BaseProviderBorgModule ]
  name: "хірургічний модуль \"кіборг"
  components:
  - type: Sprite
    layers:
    - state: medical
    - state: icon-surgery
  - type: ItemBorgModule
    items:
    - Scalpel
    - Drill
    - Hemostat
    - Retractor
    - Cautery
    - SawElectric
    - BoneGel
    # Frontier: droppable borg items
    droppableItems:
    - id: OrganHumanHeart
      whitelist:
        components:
        - BodyPart
        - Organ
        tags:
        - Organ
    # End Frontier: droppable borg items
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: surgery-module }

- type: entity
  id: BorgModuleAdvancedSurgery
  parent: [ BaseBorgModuleMedical, BaseProviderBorgModule ]
  name: "передовий хірургічний модуль-кіборг"
  components:
  - type: Sprite
    layers:
    - state: medical
    - state: icon-advanced-surgery
  - type: ItemBorgModule
    items:
    - EnergyScalpel
    - EnergyCautery
    - AdvancedRetractor
    - BoneGel
    # Frontier: droppable borg items
    droppableItems:
    - id: OrganHumanHeart
      whitelist:
        components:
        - BodyPart
        - Organ
        tags:
        - Organ
    # End Frontier: droppable borg items
  - type: BorgModuleIcon
    icon: { sprite: Interface/Actions/actions_borg.rsi, state: adv-surgery-module }
