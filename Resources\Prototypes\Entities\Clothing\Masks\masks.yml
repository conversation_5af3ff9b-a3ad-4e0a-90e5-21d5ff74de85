- type: entity
  parent: ClothingMaskPullableBase
  id: ClothingMaskGas
  name: "протигаз"
  description: "Ма<PERSON><PERSON>а, що закриває обличчя, яку можна під'єднати до системи подачі повітря."
  components:
  - type: Sprite
    sprite: Clothing/Mask/gas.rsi
  - type: Clothing
    sprite: Clothing/Mask/gas.rsi
  - type: BreathMask
  - type: IngestionBlocker
  - type: IdentityBlocker
  - type: Tag
    tags:
    - HamsterWearable
    - WhitelistChameleon
    - GasMask # goob edit
    - Mask # goob edit
  - type: HideLayerClothing
    slots:
    - Snout
    hideOnToggle: true

- type: entity
  parent: ClothingMaskGas
  id: ClothingMaskGasSecurity
  name: "протигаз охоронця"
  description: "Стандартний протигаз охорони."
  components:
  - type: Item
    size: Tiny
  - type: Sprite
    sprite: Clothing/Mask/gassecurity.rsi
  - type: Clothing
    sprite: Clothing/Mask/gassecurity.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.95
        Slash: 0.95
        Piercing: 0.95
        Heat: 0.95
  - type: IngestionBlocker # Goobstation
    blockSmokeIngestion: true

- type: entity
  parent: ClothingMaskGas
  id: ClothingMaskGasSyndicate
  name: "синдикатський протигаз"
  description: "Тактична маска, що щільно прилягає до обличчя, яку можна під'єднати до системи подачі повітря."
  components:
  - type: Sprite
    sprite: Clothing/Mask/gassyndicate.rsi
  - type: Clothing
    sprite: Clothing/Mask/gassyndicate.rsi
  - type: FlashImmunity
  - type: EyeProtection
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.95
        Slash: 0.95
        Piercing: 0.95
        Heat: 0.95
  - type: IngestionBlocker # Goobstation
    blockSmokeIngestion: true

- type: entity
  parent: ClothingMaskGas
  id: ClothingMaskGasAtmos
  name: "протигаз атмосферного техніка"
  description: "Покращений протигаз, що використовується атмосферними техніками. Він вогнетривкий!"
  components:
  - type: Sprite
    sprite: Clothing/Mask/gasatmos.rsi
  - type: Clothing
    sprite: Clothing/Mask/gasatmos.rsi
  - type: Armor
    modifiers:
      coefficients:
        Heat: 0.80

- type: entity
  parent: ClothingMaskGasAtmos
  id: ClothingMaskGasCaptain
  name: "протигаз капітана"
  description: "Нанотрейзен пішов на компроміс і перефарбував запасний атмосферний протигаз, але нікому не кажіть."
  components:
  - type: Sprite
    sprite: Clothing/Mask/gascaptain.rsi
  - type: Clothing
    sprite: Clothing/Mask/gascaptain.rsi
  - type: BreathMask
  - type: IngestionBlocker

- type: entity
  parent: ClothingMaskGasExplorer
  id: ClothingMaskGasCaptainCombat
  name: "бойовий протигаз капітана"
  description: "Військовий протигаз, який можна під'єднати до системи подачі повітря, пофарбований і оснащений емблемою, що відповідає його власнику. Видається лише найкращим працівникам станції."
  components:
  - type: Sprite
    sprite: Clothing/Mask/gascaptaincombat.rsi
  - type: Clothing
    sprite: Clothing/Mask/gascaptaincombat.rsi

- type: entity
  parent: ClothingMaskGasAtmos
  id: ClothingMaskGasCentcom
  name: "протигаз ЦентКому"
  description: "Ооо, золото і зелень. Фантастично! Це повинно допомогти сидіти в офісі."
  components:
  - type: Sprite
    sprite: Clothing/Mask/gascentcom.rsi
  - type: Clothing
    sprite: Clothing/Mask/gascentcom.rsi
  - type: BreathMask
  - type: IngestionBlocker

- type: entity
  parent: ClothingMaskGas
  id: ClothingMaskGasExplorer
  name: "протигаз дослідника"
  description: "Військовий протигаз, який можна під'єднати до системи подачі повітря."
  components:
  - type: Sprite
    sprite: Clothing/Mask/gasexplorer.rsi
  - type: Clothing
    sprite: Clothing/Mask/gasexplorer.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.90
        Slash: 0.90
        Piercing: 0.95
        Heat: 0.95

- type: entity
  parent: ClothingMaskPullableBase
  id: ClothingMaskBreathMedical
  name: "медична маска"
  description: "Стерильна маска, що щільно прилягає, яку можна під'єднати до системи подачі повітря."
  components:
  - type: Item
    size: Tiny
  - type: Sprite
    sprite: Clothing/Mask/medical.rsi
  - type: Clothing
    sprite: Clothing/Mask/medical.rsi
  - type: BreathMask
  - type: IngestionBlocker
  - type: Tag
    tags:
    - PetWearable
    - WhitelistChameleon

- type: entity
  parent: ClothingMaskBreathMedical
  id: ClothingMaskBreathMedicalSecurity
  name: "військова медична маска"
  description: "Медична маска з невеликим шаром захисту від пошкоджень та вірусів, подібна до тієї, що використовували медичні підрозділи часів першої корпоративної війни."
  components:
  - type: Item
    size: Tiny
  - type: Sprite
    sprite: Clothing/Mask/medicalsecurity.rsi
  - type: Clothing
    sprite: Clothing/Mask/medicalsecurity.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.95
        Slash: 0.95
        Piercing: 0.95
        Heat: 0.95
  # PIRATE START
  - type: IngestionBlocker
    blockSmokeIngestion: true
  # PIRATE END

- type: entity
  parent: ClothingMaskPullableBase
  id: ClothingMaskBreath
  name: "респіратор"
  description: "З таким же успіхом можна залишити його вдягненим 24/7."
  components:
  - type: Item
    size: Tiny
  - type: Sprite
    sprite: Clothing/Mask/breath.rsi
  - type: Clothing
    sprite: Clothing/Mask/breath.rsi
  - type: BreathMask
  - type: IngestionBlocker
  - type: Tag
    tags:
    - PetWearable
    - WhitelistChameleon
  - type: PhysicalComposition
    materialComposition:
      Steel: 50
      Plastic: 100
  - type: StaticPrice
    price: 12.5 # increases in price after decomposed into raw materials

- type: entity
  parent: ClothingMaskBase
  id: ClothingMaskClownBase
  abstract: true
  name: "клоунська перука та маска"
  description: "Лицьове вбрання справжнього жартівника. Клоун неповний без перуки та маски."
  components:
  - type: Sprite
    sprite: Clothing/Mask/clown.rsi
  - type: Clothing
    sprite: Clothing/Mask/clown.rsi
  - type: BreathMask
  - type: IdentityBlocker
  - type: Tag
    tags:
    - ClownMask
    - WhitelistChameleon
    - IPCMaskWearable
  - type: HideLayerClothing
    slots:
    - Snout

- type: entity
  parent: ClothingMaskClownBase
  id: ClothingMaskClown
  components:
  - type: Tag
    tags:
    - ClownMask
    - HamsterWearable
    - WhitelistChameleon
  - type: HideLayerClothing
    slots:
    - Snout

- type: entity
  parent: ClothingMaskClown
  id: ClothingMaskClownBanana
  name: "перука бананового клоуна з маскою"
  components:
  - type: Sprite
    sprite: Clothing/Mask/clown_banana.rsi
  - type: Clothing
    sprite: Clothing/Mask/clown_banana.rsi
  - type: Construction
    graph: BananaClownMask
    node: mask

- type: entity
  parent: ClothingMaskBase
  id: ClothingMaskJoy
  name: "маска радості"
  description: "Висловіть своє щастя або приховуйте свої печалі за допомогою цього смішного обличчя з вирізаними сльозами радості."
  components:
  - type: Sprite
    sprite: Clothing/Mask/joy.rsi
  - type: Clothing
    sprite: Clothing/Mask/joy.rsi
  - type: BreathMask
  - type: IdentityBlocker
  - type: Tag
    tags:
    - IPCMaskWearable
  - type: HideLayerClothing
    slots:
    - Snout

- type: entity
  parent: ClothingMaskBase
  id: ClothingMaskMime
  name: "маска міма"
  description: "Традиційна маска міма. Має моторошну позу обличчя."
  components:
  - type: Sprite
    sprite: Clothing/Mask/mime.rsi
  - type: Clothing
    sprite: Clothing/Mask/mime.rsi
  - type: BreathMask
  - type: IdentityBlocker
  - type: Tag
    tags:
    - HamsterWearable
    - WhitelistChameleon
    - IPCMaskWearable
  - type: HideLayerClothing
    slots:
    - Snout

- type: entity
  parent: ClothingMaskPullableBase
  id: ClothingMaskSterile
  name: "стерильна маска"
  description: "Стерильна маска, призначена для запобігання поширенню хвороб."
  components:
  - type: Sprite
    sprite: Clothing/Mask/sterile.rsi
  - type: Clothing
    sprite: Clothing/Mask/sterile.rsi
  - type: IngestionBlocker
  - type: Item
    size: Tiny
  - type: IdentityBlocker
    coverage: MOUTH
  - type: PhysicalComposition
    materialComposition:
      Plastic: 25

- type: entity
  parent: ClothingMaskBase
  id: ClothingMaskMuzzle
  name: "намордник"
  description: "Щоб зупинити цей жахливий шум."
  components:
  - type: Item
    size: Tiny
  - type: Sprite
    sprite: Clothing/Mask/muzzle.rsi
  - type: Clothing
    sprite: Clothing/Mask/muzzle.rsi
  - type: IngestionBlocker
  - type: AddAccentClothing
    accent: ReplacementAccent
    replacement: mumble
  - type: Construction
    graph: Muzzle
    node: muzzle

- type: entity
  parent: ClothingMaskPullableBase
  id: ClothingMaskPlague
  name: "маска чумного лікаря"
  description: "Поганий знак."
  components:
  - type: Sprite
    sprite: Clothing/Mask/plaguedoctormask.rsi
  - type: Clothing
    sprite: Clothing/Mask/plaguedoctormask.rsi
  - type: BreathMask
  - type: IngestionBlocker
  - type: IdentityBlocker
  - type: HideLayerClothing
    slots:
    - Snout
    hideOnToggle: true

- type: entity
  parent: ClothingMaskClownBase
  id: ClothingMaskCluwne
  name: "маска та перука клувні"
  suffix: Unremoveable
  description: "Прокляте обличчя і волосся клувні."
  components:
  - type: Sprite
    sprite: Clothing/Mask/cluwne.rsi
  - type: Clothing
    sprite: Clothing/Mask/cluwne.rsi
  - type: Unremoveable
  - type: AddAccentClothing
    accent: StutteringAccent

- type: entity
  parent: ClothingMaskGasExplorer
  id: ClothingMaskGasSwat
  name: "протигаз спецназу"
  description: "Елітний протигаз служби безпеки."
  components:
  - type: Sprite
    sprite: Clothing/Mask/swat.rsi
  - type: Clothing
    sprite: Clothing/Mask/swat.rsi
  - type: Tag
    tags:
    - WhitelistChameleon
  - type: HideLayerClothing
    slots:
    - Hair
    - Snout
    hideOnToggle: true

- type: entity
  parent: ClothingMaskGasExplorer
  id: ClothingMaskGasMerc
  name: "протигаз найманця"
  description: "Трохи застарілий, але надійний протигаз військового зразка."
  components:
  - type: Sprite
    sprite: Clothing/Mask/merc.rsi
  - type: Clothing
    sprite: Clothing/Mask/merc.rsi

- type: entity
  parent: ClothingMaskGasSyndicate
  id: ClothingMaskGasERT
  name: "протигаз ГШР"
  description: "Протигаз елітного загону швидкої допомоги."
  components:
  - type: Sprite
    sprite: Clothing/Mask/ert.rsi
  - type: Clothing
    sprite: Clothing/Mask/ert.rsi
  - type: Tag
    tags:
    - WhitelistChameleon
  - type: HideLayerClothing
    slots:
    - Hair
    - Snout
    hideOnToggle: true
  - type: IngestionBlocker # Goobstation
    blockSmokeIngestion: true

- type: entity
  parent: ClothingMaskGasERT
  id: ClothingMaskGasDeathSquad
  name: "протигаз ескадрону смерті"
  description: "Унікальний протигаз для найелітнішого підрозділу Нацгвардії."
  components:
  - type: Sprite
    sprite: Clothing/Mask/squadron.rsi
  - type: Clothing
    sprite: Clothing/Mask/squadron.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.80
        Slash: 0.80
        Piercing: 0.90
        Heat: 0.90

- type: entity
  parent: ClothingMaskBase
  id: ClothingMaskRat
  name: "маска щура"
  description: "Маска щура, схожого на щура. Можливо, вас приймуть за щура."
  components:
  - type: Sprite
    sprite: Clothing/Mask/rat.rsi
  - type: Clothing
    sprite: Clothing/Mask/rat.rsi
  - type: Tag
    tags:
    - IPCMaskWearable # Estacao Pirata - IPCs
    - HamsterWearable
    - CursedAnimalMask # Goobstation
    - WhitelistChameleon
  - type: HideLayerClothing
    slots:
    - Snout
  - type: IdentityBlocker
  - type: AddAccentClothing # Goobstation
    accent: RatAccent

- type: entity
  parent: ClothingMaskBase
  id: ClothingMaskFox
  name: "маска лисиці"
  description: "Що каже лисиця?"
  components:
  - type: Sprite
    sprite: Clothing/Mask/fox.rsi
  - type: Clothing
    sprite: Clothing/Mask/fox.rsi
  - type: IdentityBlocker
  - type: Tag
    tags:
    - IPCMaskWearable
    - CursedAnimalMask # Goobstation
    - WhitelistChameleon # Goobstation
  - type: HideLayerClothing
    slots:
    - Snout
  - type: AddAccentClothing # Goobstation
    accent: FoxAccent

- type: entity
  parent: ClothingMaskBase
  id: ClothingMaskBee
  name: "маска бджоли"
  description: "Для королеви!"
  components:
  - type: Sprite
    sprite: Clothing/Mask/bee.rsi
  - type: Clothing
    sprite: Clothing/Mask/bee.rsi
  - type: IdentityBlocker
  - type: Tag
    tags:
    - IPCMaskWearable
    - CursedAnimalMask # Goobstation
    - WhitelistChameleon # Goobstation
  - type: HideLayerClothing
    slots:
    - Snout
  - type: AddAccentClothing # Goobstation
    accent: BeeAccent

- type: entity
  parent: ClothingMaskBase
  id: ClothingMaskBear
  name: "маска ведмедя"
  description: "Я хмарка, хмарка, хмарка, хмарка, я зовсім не ведмідь."
  components:
  - type: Sprite
    sprite: Clothing/Mask/bear.rsi
  - type: Clothing
    sprite: Clothing/Mask/bear.rsi
  - type: IdentityBlocker
  - type: Tag
    tags:
    - IPCMaskWearable
    - CursedAnimalMask # Goobstation
    - WhitelistChameleon # Goobstation
  - type: HideLayerClothing
    slots:
    - Snout
  - type: AddAccentClothing # Goobstation
    accent: BearAccent

- type: entity
  parent: ClothingMaskBase
  id: ClothingMaskRaven
  name: "маска ворона"
  description: "Там, де я, смерть... або блиск."
  components:
  - type: Sprite
    sprite: Clothing/Mask/raven.rsi
  - type: Clothing
    sprite: Clothing/Mask/raven.rsi
  - type: IdentityBlocker
  - type: Tag
    tags:
    - IPCMaskWearable
    - CursedAnimalMask # Goobstation
    - WhitelistChameleon # Goobstation
  - type: HideLayerClothing
    slots:
    - Snout
  - type: AddAccentClothing # Goobstation
    accent: RavenAccent

- type: entity
  parent: ClothingMaskBase
  id: ClothingMaskJackal
  name: "маска шакала"
  description: "До власника маски краще не повертатися спиною, він може вкусити."
  components:
  - type: Sprite
    sprite: Clothing/Mask/jackal.rsi
  - type: Clothing
    sprite: Clothing/Mask/jackal.rsi
  - type: IdentityBlocker
  - type: Tag
    tags:
    - IPCMaskWearable
    - CursedAnimalMask # Goobstation
    - WhitelistChameleon # Goobstation
  - type: HideLayerClothing
    slots:
    - Snout
  - type: AddAccentClothing # Goobstation
    accent: JackalAccent

- type: entity
  parent: ClothingMaskBase
  id: ClothingMaskBat
  name: "маска кажана"
  description: "Вночі - кровопивця, а вдень - милий, сліпий звір."
  components:
  - type: Sprite
    sprite: Clothing/Mask/bat.rsi
  - type: Clothing
    sprite: Clothing/Mask/bat.rsi
  - type: IdentityBlocker
  - type: Tag
    tags:
    - IPCMaskWearable
    - CursedAnimalMask # Goobstation
    - WhitelistChameleon # Goobstation
  - type: HideLayerClothing
    slots:
    - Snout
  - type: AddAccentClothing # Goobstation
    accent: BatAccent

- type: entity
  parent: ClothingMaskBase
  id: ClothingMaskNeckGaiter
  name: "баф"
  description: "Стильні гетри для шиї, чи здатні захистити від космічного вітру?"
  components:
  - type: Sprite
    sprite: Clothing/Mask/neckgaiter.rsi
  - type: Clothing
    sprite: Clothing/Mask/neckgaiter.rsi
  - type: IdentityBlocker
    coverage: MOUTH
  - type: Tag
    tags:
    - WhitelistChameleon
    - IPCMaskWearable # Estacao Pirata - IPCs

- type: entity
  parent: ClothingMaskNeckGaiter
  id: ClothingMaskNeckGaiterWhite
  name: "баф"
  components:
  - type: Sprite
    sprite: Clothing/Mask/neckgaiterwhite.rsi
  - type: Clothing
    sprite: Clothing/Mask/neckgaiterwhite.rsi

- type: entity
  parent: ClothingMaskNeckGaiter
  id: ClothingMaskNeckGaiterRed
  name: "червоний баф"
  components:
  - type: Sprite
    sprite: Clothing/Mask/neckgaiterred.rsi
  - type: Clothing
    sprite: Clothing/Mask/neckgaiterred.rsi

- type: entity
  parent: ClothingMaskClownBase
  id: ClothingMaskSexyClown
  name: "маска сексуального клоуна"
  description: "Деякі пустотливі клоуни думають, що саме так виглядає Хонкматір."
  components:
  - type: Sprite
    sprite: Clothing/Mask/sexyclown.rsi
  - type: Clothing
    sprite: Clothing/Mask/sexyclown.rsi

- type: entity
  parent: ClothingMaskMime
  id: ClothingMaskSexyMime
  name: "маска сексуального міма"
  description: "Ці рум'яні щічки так і просяться, щоб їх розтерли."
  components:
  - type: Sprite
    sprite: Clothing/Mask/sexymime.rsi
  - type: Clothing
    sprite: Clothing/Mask/sexymime.rsi

- type: entity
  parent: ClothingMaskMime
  id: ClothingMaskSadMime
  name: "маска сумного міма"
  description: "Багато хто думає, що саме так виглядає справжня мімічна маска."
  components:
  - type: Sprite
    sprite: Clothing/Mask/sadmime.rsi
  - type: Clothing
    sprite: Clothing/Mask/sadmime.rsi

- type: entity
  parent: ClothingMaskMime
  id: ClothingMaskScaredMime
  name: "маска переляканого міма"
  description: "Виглядає так, ніби вона кричить, якби не була маскою"
  components:
  - type: Sprite
    sprite: Clothing/Mask/scaredmime.rsi
  - type: Clothing
    sprite: Clothing/Mask/scaredmime.rsi

- type: entity
  parent: ClothingMaskBase
  id: ClothingMaskItalianMoustache
  name: "італійські вуса"
  description: "Виготовлений із справжніх італійських волосків вусів. Дає власникові непереборне бажання дико жестикулювати."
  components:
  - type: Sprite
    sprite: Clothing/Mask/italian_moustache.rsi
  - type: Clothing
    sprite: Clothing/Mask/italian_moustache.rsi
  - type: Item
    storedRotation: -90
  - type: AddAccentClothing
    accent: ReplacementAccent
    replacement: italian

- type: entity
  parent: ClothingMaskBase
  id: ClothingMaskNinja
  name: "маска ніндзя"
  description: "Щільно прилягаюча нано-посилена маска, яка діє одночасно як повітряний фільтр і як постмодерністський модний аксесуар."
  components:
  - type: Sprite
    sprite: Clothing/Mask/ninja.rsi
  - type: Clothing
    sprite: Clothing/Mask/ninja.rsi
  - type: Item
    storedRotation: -90
  - type: EyeProtection
  - type: BreathMask
  - type: IdentityBlocker
  - type: Tag
    tags:
    - IPCMaskWearable # Estacao Pirata - IPCs
  - type: Unremoveable
