- type: entity
  name: "кар<PERSON>о<PERSON>на коробка"
  parent: BoxBase
  id: BoxCardboard
  description: "Невеличка коробка для зберігання речей."
  components:
  - type: Item
    size: Large
    shape:
    - 0,0,2,2
  - type: Storage
    maxItemSize: Small
    grid:
    - 0,0,3,3
  - type: Sprite
    state: box
  - type: EmitSoundOnPickup
    sound: /Audio/SimpleStation14/Items/Handling/cardboardbox_pickup.ogg
  - type: EmitSoundOnDrop
    sound: /Audio/SimpleStation14/Items/Handling/cardboardbox_drop.ogg
  - type: EmitSoundOnLand
    sound: /Audio/SimpleStation14/Items/Handling/cardboardbox_drop.ogg
  - type: Tag
    tags:
    - BoxCardboard

- type: entity
  name: "коробка з мишоловками"
  parent: BoxCardboard
  id: BoxMousetrap
  description: "Ця коробка наповнена мишоловками. Намагайтеся не застрягти в ній рукою."
  components:
    - type: StorageFill
      contents:
        - id: Mousetrap
          amount: 4
    - type: Sprite
      layers:
        - state: box
        - state: mousetraps

- type: entity
  name: "коробка з лампочками"
  parent: BoxCardboard
  id: BoxLightbulb
  description: "Ця коробка зсередини має таку форму, щоб туди помістилися лише лампочки та трубки."
  components:
  - type: StorageFill
    contents:
      - id: LightBulb
        amount: 12
  - type: Sprite
    layers:
      - state: box
      - state: light
  - type: Storage
    grid:
    - 0,0,5,3
    whitelist:
      components:
      - LightBulb

- type: entity
  name: "коробка з лампами"
  parent: BoxCardboard
  id: BoxLighttube
  description: "Ця коробка зсередини має таку форму, щоб туди помістилися лише лампочки та трубки."
  components:
  - type: StorageFill
    contents:
      - id: LightTube
        amount: 12
  - type: Sprite
    layers:
      - state: box
      - state: lighttube
  - type: Storage
    grid:
    - 0,0,5,3
    whitelist:
      components:
      - LightBulb

- type: entity
  name: "коробка з лампами різного кольору"
  parent: BoxCardboard
  id: BoxLightMixed
  description: "Ця коробка зсередини має таку форму, щоб туди помістилися лише лампочки та трубки."
  components:
  - type: StorageFill
    contents:
      - id: LightTube
        amount: 6
      - id: LightBulb
        amount: 6
  - type: Sprite
    layers:
      - state: box
      - state: lightmixed
  - type: Storage
    grid:
    - 0,0,5,3
    whitelist:
      components:
      - LightBulb

- type: entity
  name: "коробка з КПК"
  parent: BoxCardboard
  id: BoxPDA
  description: "Коробка запасних мікрокомп'ютерів КПК."
  components:
  - type: StorageFill
    contents:
      - id: PassengerPDA
        amount: 4
  - type: Sprite
    layers:
      - state: box
      - state: pda

- type: entity
  name: "коробка з ID-картами"
  parent: BoxCardboard
  id: BoxID
  description: "Коробка запасних бланків посвідчень особи."
  components:
  - type: StorageFill
    contents:
      - id: PassengerIDCard
        amount: 4
  - type: Sprite
    layers:
      - state: box
      - state: pda

- type: entity
  name: "коробка з гарнітурами"
  parent: BoxCardboard
  id: BoxHeadset
  description: "Коробка запасних пасажирських навушників."
  components:
  - type: StorageFill
    contents:
      - id: ClothingHeadsetGrey
        amount: 4
  - type: Sprite
    layers:
      - state: box
      - state: headset

- type: entity
  name: "коробка з мезонними окулярами"
  parent: BoxCardboard
  id: BoxMesonScanners
  description: "Коробка запасних мезонних окулярів."
  components:
  - type: StorageFill
    contents:
      - id: ClothingEyesGlassesMeson
        amount: 4
  - type: Sprite
    layers:
      - state: box
      - state: meson

- type: entity
  name: "І.Р.Х."
  parent: BoxCardboard
  id: BoxMRE
  description: "Коробка з надлишковим військовим пайком десятирічної давнини. Воно на диво не згнило."
  components:
  - type: StorageFill
    contents:
      - id: DrinkMREFlask
      - id: FoodSnackNutribrick
      - id: FoodSnackMREBrownie
      - id: FoodCondimentPacketKetchup
  - type: Sprite
    layers:
      - state: box_olive
      - state: writing

- type: entity
  parent: BoxHug
  id: BoxHugHealing
  suffix: Medical
  components:
  - type: StorageFill
    contents:
      - id: Brutepack
        amount: 3

- type: entity
  name: "коробка з надувними стінами"
  parent: BoxCardboard
  id: BoxInflatable
  description: "Заборонено: надувні стіни використовувати як плавучі засоби."
  components:
  - type: StorageFill
    contents:
        - id: InflatableWallStack
          amount: 2
        - id: InflatableDoorStack
          amount: 2
  - type: Sprite
    layers:
      - state: box
      - state: inflatable


- type: entity
  name: "повсякденна сумка Хацуне Міку"
  parent: ClothingBackpackDuffel
  id: BoxPerformer
  description: "З Днем Хацуне Міку!"
  components:
  - type: StorageFill
    contents:
      - id: ClothingShoesBootsPerformer
      - id: ClothingUniformJumpskirtPerformer
      - id: FoodMealMemoryleek
        amount: 2

- type: entity
  name: "коробка з сигнальними ракетами"
  parent: BoxCardboard
  id: BoxFlare
  description: "Коробка сигнальних ракет. Час для вечірки."
  components:
  - type: StorageFill
    contents:
      - id: Flare
        amount: 6
  - type: Sprite
    layers:
      - state: box
      - state: flare

- type: entity
  name: "коробка з сміттєвими пакетами"
  parent: BoxCardboard
  id: BoxTrashbag
  description: "Коробка з мішками для сміття. Щасливі звуки прибиральника."
  components:
  - type: StorageFill
    contents:
      - id: TrashBag
        amount: 6
  - type: Storage
    grid:
    - 0,0,5,3
    maxItemSize: Large
    whitelist:
      tags:
        - TrashBag
  - type: Sprite
    layers:
      - state: box
      - state: trashbag

- type: entity
  name: "коробка з ключами шифрування пасажирів"
  parent: BoxCardboard
  id: BoxEncryptionKeyPassenger
  description: "Коробка запасних ключів шифрування."
  components:
  - type: StorageFill
    contents:
      - id: EncryptionKeyCommon
        amount: 4
  - type: Sprite
    layers:
      - state: box
      - state: encryptokey
  - type: Storage
    whitelist:
      components:
      - EncryptionKey

- type: entity
  name: "коробка з ключами шифрування логістики" # DeltaV - Logistics Department replacing Cargo
  parent: BoxEncryptionKeyPassenger
  id: BoxEncryptionKeyCargo
  components:
  - type: StorageFill
    contents:
      - id: EncryptionKeyCargo
        amount: 4

- type: entity
  name: "коробка з ключами шифрування інженерії"
  parent: BoxEncryptionKeyPassenger
  id: BoxEncryptionKeyEngineering
  components:
  - type: StorageFill
    contents:
      - id: EncryptionKeyEngineering
        amount: 4

- type: entity
  name: "коробка з ключами шифрування лікарів-вчених"
  parent: BoxEncryptionKeyPassenger
  id: BoxEncryptionKeyMedicalScience
  components:
  - type: StorageFill
    contents:
      - id: EncryptionKeyMedicalScience
        amount: 4

- type: entity
  name: "коробка з ключами шифрування лікарів"
  parent: BoxEncryptionKeyPassenger
  id: BoxEncryptionKeyMedical
  components:
  - type: StorageFill
    contents:
      - id: EncryptionKeyMedical
        amount: 4

- type: entity
  name: "коробка з ключами шифрування робототехніки"
  parent: BoxEncryptionKeyPassenger
  id: BoxEncryptionKeyRobo
  components:
  - type: StorageFill
    contents:
      - id: EncryptionKeyRobo
        amount: 4

- type: entity
  name: "коробка з ключами шифрування епістеміки" # DeltaV - Epistemics Department replacing Science
  parent: BoxEncryptionKeyPassenger
  id: BoxEncryptionKeyScience
  components:
  - type: StorageFill
    contents:
      - id: EncryptionKeyScience
        amount: 4

- type: entity
  name: "коробка з ключами шифрування служби безпеки"
  parent: BoxEncryptionKeyPassenger
  id: BoxEncryptionKeySecurity
  components:
  - type: StorageFill
    contents:
      - id: EncryptionKeySecurity
        amount: 4

- type: entity
  name: "коробка з ключами шифрування сервісу"
  parent: BoxEncryptionKeyPassenger
  id: BoxEncryptionKeyService
  components:
  - type: StorageFill
    contents:
      - id: EncryptionKeyService
        amount: 4

- type: entity
  name: "коробка з ключами шифрування синдикату"
  parent: BoxEncryptionKeyPassenger
  id: BoxEncryptionKeySyndie
  description: "Два ключі шифрування синдикату за ціною одного. Мініатюризовані для зручності використання."
  components:
  - type: Item
    size: Normal
  - type: StorageFill
    contents:
      - id: EncryptionKeySyndie
        amount: 2
  - type: Storage

- type: entity
  name: "коробка для імплантатів deathrattle"
  parent: BoxCardboard
  id: BoxDeathRattleImplants
  description: "Шість імплантатів смертоносного брязкальця та ручні GPS-навігатори для всього загону."
  components:
  - type: Item
    size: Normal
  - type: StorageFill
    contents:
      - id: DeathRattleImplanter
        amount: 6
      - id: HandheldGPSBasic
        amount: 6
  - type: Storage
    grid:
    - 0,0,5,3
  - type: Sprite
    layers:
      - state: box
      - state: syringe

- type: entity
  name: "свинцева коробка"
  parent: BoxCardboard
  suffix: DEBUG
  id: BoxLeadLined
  description: "Ця коробка перешкоджає передачі шкідливого випромінювання."
  components:
  - type: Sprite
    state: box
  - type: RadiationBlockingContainer
    resistance: 2

- type: entity
  name: "коробка з свічками"
  parent: BoxCardboard
  id: BoxCandle
  components:
  - type: Sprite
    layers:
      - state: box
      - state: candle
  - type: Storage
    grid:
    - 0,0,9,2
  - type: StorageFill
    contents:
      - id: Candle
        amount: 3
      - id: CandleBlue
        amount: 3
      - id: CandleRed
        amount: 3
      - id: CandleGreen
        amount: 3
      - id: CandlePurple
        amount: 3

- type: entity
  name: "коробка з маленькими свічками"
  parent: BoxCardboard
  id: BoxCandleSmall
  components:
  - type: Sprite
    layers:
      - state: box
      - state: candle
  - type: Storage
    grid:
    - 0,0,9,2
  - type: StorageFill
    contents:
      - id: CandleSmall
        amount: 5
      - id: CandleBlueSmall
        amount: 5
      - id: CandleRedSmall
        amount: 5
      - id: CandleGreenSmall
        amount: 5
      - id: CandlePurpleSmall
        amount: 5

- type: entity
  name: "коробка з дротиками для дартсу"
  parent: BoxCardboard
  id: BoxDarts
  description: "Ця коробка наповнена різнокольоровими дротиками."
  components:
  - type: Item
    size: Normal
  - type: StorageFill
    contents:
      - id: Dart
        amount: 3
      - id: DartBlue
        amount: 3
      - id: DartPurple
        amount: 3
      - id: DartYellow
        amount: 3
  - type: Storage
    grid:
    - 0,0,5,3
  - type: Sprite
    layers:
      - state: box
      - state: darts
