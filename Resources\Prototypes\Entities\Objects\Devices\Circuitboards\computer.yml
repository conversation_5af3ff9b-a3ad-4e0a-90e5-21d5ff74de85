- type: entity
  parent: BaseItem
  id: BaseComputerCircuitboard
  name: "консольна плата"
  abstract: true
  components:
    - type: ComputerBoard
    - type: Sprite
      sprite: Objects/Misc/module.rsi
      state: cpuboard
    - type: Item
      storedRotation: -90
    - type: StaticPrice
      price: 100
    - type: PhysicalComposition
      materialComposition:
        Glass: 230
      chemicalComposition:
        Silicon: 20
    - type: Tag
      tags:
        - ComputerBoard

- type: entity
  parent: BaseComputerCircuitboard
  id: AlertsComputerCircuitboard
  name: "плата консолі атмосферних сповіщень"
  description: "Комп'ютерна друкована плата для комп'ютера атмосферних сповіщень."
  components:
    - type: ComputerBoard
      prototype: ComputerAlert

- type: entity
  parent: BaseComputerCircuitboard
  id: AtmosMonitoringComputerCircuitboard
  name: "плата монітору атмосферної мережі"
  description: "Комп'ютерна друкована плата для атмосферного мережевого монітора."
  components:
    - type: ComputerBoard
      prototype: ComputerAtmosMonitoring

- type: entity
  parent: BaseComputerCircuitboard
  id: PowerComputerCircuitboard
  name: "плата консолі моніторингу живлення"
  description: "Комп'ютерна друкована плата для комп'ютера для моніторингу енергоспоживання."
  components:
    - type: Sprite
      state: cpu_engineering
    - type: ComputerBoard
      prototype: ComputerPowerMonitoring
    - type: ReverseEngineering # Nyano
      recipes:
        - PowerComputerCircuitboard

- type: entity
  parent: BaseComputerCircuitboard
  id: MedicalRecordsComputerCircuitboard
  name: "плата консолі медичних записів"
  description: "Комп'ютерна друкована плата для комп'ютера для ведення медичної документації."
  components:
    - type: Sprite
      state: cpu_medical
    - type: ComputerBoard
      prototype: ComputerMedicalRecords

- type: entity
  parent: BaseComputerCircuitboard
  id: CriminalRecordsComputerCircuitboard
  name: "плата консолі кримінальних записів"
  description: "Комп'ютерна друкована плата для комп'ютера з кримінальною базою даних."
  components:
    - type: Sprite
      state: cpu_security
    - type: ComputerBoard
      prototype: ComputerCriminalRecords

- type: entity
  parent: BaseComputerCircuitboard
  id: PsionicsRecordsComputerCircuitboard
  name: "плата реєстру псионіки"
  description: "Комп'ютерна друкована плата для реєстрового комп'ютера псионіки."
  components:
    - type: Sprite
      state: cpu_science
    - type: ComputerBoard
      prototype: ComputerPsionicsRecords

- type: entity
  parent: BaseComputerCircuitboard
  id: StationRecordsComputerCircuitboard
  name: "плата консолі записів станції"
  description: "Друкована плата для комп'ютера, що реєструє станцію."
  components:
    - type: Sprite
      state: cpu_command
    - type: ComputerBoard
      prototype: ComputerStationRecords

- type: entity
  parent: BaseComputerCircuitboard
  id: CargoRequestComputerCircuitboard
  name: "плата консолі заявок на вантаж"
  description: "Друкована плата для комп'ютера для запиту вантажу."
  components:
    - type: Sprite
      state: cpu_supply
    - type: ComputerBoard
      prototype: ComputerCargoOrders
    - type: StaticPrice
      price: 750

- type: entity
  id: CargoBountyComputerCircuitboard
  parent: BaseComputerCircuitboard
  name: "плата консолі завдань на вантаж"
  description: "Друкована плата для комп'ютера для вантажного баунті-комп'ютера."
  components:
  - type: Sprite
    state: cpu_supply
  - type: ComputerBoard
    prototype: ComputerCargoBounty
  - type: StaticPrice

- type: entity
  parent: BaseComputerCircuitboard
  id: CargoShuttleComputerCircuitboard
  name: "плата консолі вантажного шатлу ЦК"
  description: "Друкована плата для комп'ютера вантажного шаттла."
  components:
    - type: Sprite
      state: cpu_supply
    - type: ComputerBoard
      prototype: ComputerCargoShuttle

- type: entity
  parent: BaseComputerCircuitboard
  id: SalvageExpeditionsComputerCircuitboard
  name: "плата консолі утилізаційних експедицій"
  description: "Комп'ютерна друкована плата для комп'ютера утилізаційних експедицій."
  components:
    - type: Sprite
      state: cpu_supply
    - type: ComputerBoard
      prototype: ComputerSalvageExpedition
    - type: StealTarget
      stealGroup: SalvageExpeditionsComputerCircuitboard
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - SalvageExpeditionsComputerCircuitboard

- type: entity
  parent: BaseComputerCircuitboard
  id: CargoShuttleConsoleCircuitboard
  name: "плата консолі вантажного шатлу"
  description: "Комп'ютерна друкована плата для консолі вантажного шаттла."
  components:
    - type: ComputerBoard
      prototype: ComputerShuttleCargo
    - type: StealTarget
      stealGroup: CargoShuttleConsoleCircuitboard

- type: entity
  parent: BaseComputerCircuitboard
  id: SalvageShuttleConsoleCircuitboard
  name: "плата консолі утилізаційного шатлу"
  description: "Комп'ютерна друкована плата для консолі рятувального шатла."
  components:
    - type: ComputerBoard
      prototype: ComputerShuttleSalvage
    - type: StealTarget
      stealGroup: SalvageShuttleConsoleCircuitboard

- type: entity
  parent: BaseComputerCircuitboard
  id: SurveillanceCameraMonitorCircuitboard
  name: "плата консолі камер спостереження"
  description: "Комп'ютерна друкована плата для монітора камери спостереження."
  components:
    - type: ComputerBoard
      prototype: ComputerSurveillanceCameraMonitor
    - type: Tag
      tags:
        - SurveillanceCameraMonitorCircuitboard

- type: entity
  parent: BaseComputerCircuitboard
  id: SurveillanceWirelessCameraMonitorCircuitboard
  name: "плата консолі бездротових камер спостереження"
  description: "Комп'ютерна друкована плата для монітора бездротової камери спостереження."
  components:
    - type: ComputerBoard
      prototype: ComputerSurveillanceWirelessCameraMonitor

- type: entity
  parent: BaseComputerCircuitboard
  id: ComputerTelevisionCircuitboard
  name: "плата телевізора"
  description: "Комп'ютерна друкована плата для телевізора."
  components:
    - type: ComputerBoard
      prototype: ComputerTelevision
    - type: Tag
      tags:
        - ComputerTelevisionCircuitboard
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - ComputerTelevisionCircuitboard

- type: entity
  parent: BaseComputerCircuitboard
  id: ResearchComputerCircuitboard
  name: "плата консолі R&D"
  description: "Комп'ютерна друкована плата для R&D консолі."
  components:
    - type: Sprite
      state: cpu_science
    - type: ComputerBoard
      prototype: ComputerResearchAndDevelopment

- type: entity
  parent: BaseComputerCircuitboard
  id: AnalysisComputerCircuitboard
  name: "плата аналітичної консолі"
  description: "Комп'ютерна друкована плата для аналітичної консолі."
  components:
    - type: Sprite
      state: cpu_science
    - type: ComputerBoard
      prototype: ComputerAnalysisConsole
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - AnalysisComputerCircuitboard

- type: entity
  parent: BaseComputerCircuitboard
  id: TechDiskComputerCircuitboard
  name: "плата терміналу технологічних дисків"
  description: "Комп'ютерна друкована плата для технологічного дискового терміналу."
  components:
  - type: Sprite
    state: cpu_science
  - type: ComputerBoard
    prototype: ComputerTechnologyDiskTerminal
  - type: ReverseEngineering # Nyano
    difficulty: 2
    recipes:
      - TechDiskComputerCircuitboard

- type: entity
  parent: BaseComputerCircuitboard
  id: CrewMonitoringComputerCircuitboard
  name: "плата консолі моніторингу екіпажу"
  description: "Комп'ютерна друкована плата для консолі моніторингу екіпажу."
  components:
    - type: ComputerBoard
      prototype: ComputerCrewMonitoring
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - CrewMonitoringComputerCircuitboard

- type: entity
  parent: BaseComputerCircuitboard
  id: IDComputerCircuitboard
  name: "плата консолі ID карток"
  description: "Комп'ютерна друкована плата для консолі з ідентифікаційними картками."
  components:
    - type: Sprite
      state: cpu_command
    - type: ComputerBoard
      prototype: ComputerId
    - type: StaticPrice
      price: 750
    - type: Tag
      tags:
      - HighRiskItem

- type: entity
  parent: BaseComputerCircuitboard
  id: BodyScannerComputerCircuitboard
  name: "плата консолі сканера тіла"
  description: "Комп'ютерна друкована плата для консолі сканера тіла."
  components:
    - type: Sprite
      state: cpu_medical
    - type: ComputerBoard
      prototype: computerBodyScanner

- type: entity
  parent: BaseComputerCircuitboard
  id: CommsComputerCircuitboard
  name: "плата консолі комунікацій"
  description: "Комп'ютерна друкована плата для консолі комунікацій."
  components:
    - type: Sprite
      state: cpu_command
    - type: ComputerBoard
      prototype: ComputerComms
      modularComputerProgramPrototype: CommunicationsConsoleDiskPrototype

- type: entity
  parent: BaseComputerCircuitboard
  id: SyndicateCommsComputerCircuitboard
  name: "плата консолі зв'язку синдикату"
  description: "Комп'ютерна друкована плата для синдикатського пульта зв'язку."
  components:
    - type: ComputerBoard
      prototype: SyndicateComputerComms

- type: entity
  parent: BaseComputerCircuitboard
  id: RadarConsoleCircuitboard
  name: "плата радіолокаційної консолі"
  components:
  - type: ComputerBoard
    prototype: ComputerRadar
  - type: ReverseEngineering # Nyano
    difficulty: 2
    recipes:
       - RadarConsoleCircuitboard

- type: entity
  parent: BaseComputerCircuitboard
  id: SolarControlComputerCircuitboard
  name: "плата консолі сонячних батарей"
  description: "Комп'ютерна друкована плата для консолі керування сонячними батареями."
  components:
    - type: Sprite
      state: cpu_engineering
    - type: ComputerBoard
      prototype: ComputerSolarControl
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - SolarControlComputerCircuitboard

- type: entity
  parent: BaseComputerCircuitboard
  id: SpaceVillainArcadeComputerCircuitboard
  name: "плата аркади Космічні Лиходії"
  description: "Комп'ютерна друкована плата для ігрової аркади з космічним лиходієм."
  components:
    - type: ComputerBoard
      prototype: SpaceVillainArcade

- type: entity
  parent: BaseComputerCircuitboard
  id: BlockGameArcadeComputerCircuitboard
  name: "плата аркади НТетріс"
  description: "Комп'ютерна друкована плата для аркади гри у блоки"
  components:
    - type: ComputerBoard
      prototype: BlockGameArcade

- type: entity
  parent: BaseComputerCircuitboard
  id: ParticleAcceleratorComputerCircuitboard
  name: "плата консолі управління прискорювачем частинок"
  description: "Комп'ютерна друкована плата для блоку управління прискорювачем частинок."
  components:
    - type: Sprite
      state: cpu_engineering
    - type: ComputerBoard
      prototype: ParticleAcceleratorControlBoxUnfinished

- type: entity
  parent: BaseComputerCircuitboard
  id: ShuttleConsoleCircuitboard
  name: "плата консолі шатлу"
  description: "Комп'ютерна друкована плата для шатл-консолі."
  components:
    - type: ComputerBoard
      prototype: ComputerShuttle
    - type: ReverseEngineering # Nyano
      difficulty: 3
      recipes:
        - ShuttleConsoleCircuitboard

- type: entity
  parent: BaseComputerCircuitboard
  id: SyndicateShuttleConsoleCircuitboard
  name: "плата консолі шатлу синдикату"
  description: "Комп'ютерна друкована плата для консолі шаттла синдикату."
  components:
    - type: ComputerBoard
      prototype: ComputerShuttleSyndie

- type: entity
  parent: BaseComputerCircuitboard
  id: CloningConsoleComputerCircuitboard
  name: "плата консолі клонування"
  description: "Комп'ютерна друкована плата для консолі клонування."
  components:
    - type: Sprite
      state: cpu_medical
    - type: ComputerBoard
      prototype: ComputerCloningConsole
    - type: ReverseEngineering # Nyano
      difficulty: 3
      recipes:
        - CloningConsoleComputerCircuitboard

- type: entity
  parent: BaseComputerCircuitboard
  id: ComputerIFFCircuitboard
  name: "плата консолі IFF"
  description: "Дозволяє контролювати IFF-характеристики цього судна."
  components:
    - type: Sprite
      state: cpu_command
    - type: ComputerBoard
      prototype: ComputerIFF

- type: entity
  parent: BaseComputerCircuitboard
  id: ComputerIFFSyndicateCircuitboard
  name: "плата консолі IFF синдикату"
  description: "Дозволяє контролювати IFF і стелс-характеристики цього судна."
  components:
    - type: ComputerBoard
      prototype: ComputerIFFSyndicate

- type: entity
  parent: BaseComputerCircuitboard
  id: ComputerMassMediaCircuitboard
  name: "плата консолі менеджменту новин"
  description: "Напишіть своє послання світу!"
  components:
    - type: Sprite
      state: cpu_service
    - type: StaticPrice
      price: 150
    - type: ComputerBoard
      prototype: ComputerMassMedia

- type: entity
  parent: BaseComputerCircuitboard
  id: SensorConsoleCircuitboard
  name: "плата консолі моніторингу датчиків"
  description: "Комп'ютерна друкована плата для консолі моніторингу датчиків."
  components:
    - type: Sprite
      state: cpu_engineering
    - type: ComputerBoard
      prototype: ComputerSensorMonitoring

- type: entity
  parent: BaseComputerCircuitboard
  id: RoboticsConsoleCircuitboard
  name: "плата консолі управління робототехнікою"
  description: "Комп'ютерна друкована плата для пульта керування робототехнікою."
  components:
    - type: Sprite
      state: cpu_science
    - type: ComputerBoard
      prototype: ComputerRoboticsControl

- type: entity
  parent: BaseComputerCircuitboard
  id: StationAiUploadCircuitboard
  name: "консоль завантаження станційного ШІ"
  description: "Комп'ютерна друкована плата для консолі завантаження ШІ станції."
  components:
    - type: Sprite
      state: cpu_science
    - type: ComputerBoard
      prototype: StationAiUploadComputer
