- type: entity
  id: BaseGameRule
  abstract: true
  categories: [ HideSpawnMenu ]
  components:
  - type: GameRule

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseGameRule
  id: SubGamemodesRule
  components:
  - type: SubGamemodes
    rules:
    - id: Thief
      prob: 0.5
    - id: Vampire
      prob: 0.25
    - id: Devil # Goob
      prob: 0.3

- type: entity
  id: DeathMatch31
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: DeathMatchRule
    rewardSpawns:
    - id: HealingToolbox
    - id: ClothingOuterArmorBasicSlim
      orGroup: loot
    - id: ClothingHeadHelmetBasic
      orGroup: loot
    - id: SoapNT
      orGroup: loot
    - id: Bola
      orGroup: loot
    - id: Spear
      orGroup: loot
    - id: ClothingShoesGaloshes
      orGroup: loot
    - id: FoodPieBananaCream
      orGroup: loot
    - id: Stimpack
      orGroup: loot
  - type: KillCalloutRule
  - type: PointManager
  - type: RespawnDeadRule
  - type: RespawnTracker
    respawnDelay: 5

- type: entity
  id: InactivityTimeRestart
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: InactivityRule
    inactivityMaxTime: 600
    roundEndDelay: 10

- type: entity
  id: MaxTimeRestart
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: MaxTimeRestartRule
    roundMaxTime: 300
    roundEndDelay: 10

- type: entity
  abstract: true
  parent: BaseGameRule
  id: BaseNukeopsRule
  components:
  - type: RandomMetadata #this generates the random operation name cuz it's cool.
    nameSegments:
    - operationPrefix
    - operationSuffix
  - type: NukeopsRule
  - type: RuleGrids
  - type: AntagSelection
  - type: AntagLoadProfileRule

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseNukeopsRule
  id: Nukeops
  components:
  - type: GameRule
    minPlayers: 3
  - type: LoadMapRule
    gameMap: NukieOutpost
  - type: AntagSelection
    selectionTime: PrePlayerSpawn
    definitions:
    - prefRoles: [ NukeopsCommander ]
      fallbackRoles: [ Nukeops, NukeopsMedic ]
      spawnerPrototype: SpawnPointNukeopsCommander
      startingGear: SyndicateCommanderGearFull
      components:
      - type: NukeOperative
      - type: RandomMetadata
        nameSegments:
        - nukeops-role-commander
        - SyndicateNamesElite
      - type: NpcFactionMember
        factions:
        - Syndicate
      mindRoles:
      - MindRoleNukeopsCommander
    - prefRoles: [ NukeopsMedic ]
      fallbackRoles: [ Nukeops, NukeopsCommander ]
      spawnerPrototype: SpawnPointNukeopsMedic
      startingGear: SyndicateOperativeMedicFull
      components:
      - type: NukeOperative
      - type: RandomMetadata
        nameSegments:
        - nukeops-role-agent
        - SyndicateNamesNormal
      - type: NpcFactionMember
        factions:
        - Syndicate
      mindRoles:
      - MindRoleNukeopsMedic
    - prefRoles: [ Nukeops ]
      fallbackRoles: [ NukeopsCommander, NukeopsMedic ]
      spawnerPrototype: SpawnPointNukeopsOperative
      max: 3
      playerRatio: 10
      startingGear: SyndicateOperativeGearFull
      components:
      - type: NukeOperative
      - type: RandomMetadata
        nameSegments:
        - nukeops-role-operator
        - SyndicateNamesNormal
      - type: NpcFactionMember
        factions:
        - Syndicate
      mindRoles:
      - MindRoleNukeops

- type: entity
  abstract: true
  parent: BaseGameRule
  id: BaseTraitorRule
  components:
  - type: TraitorRule
  # TODO: codewords in yml
  # TODO: uplink in yml
  - type: AntagRandomObjectives
    sets:
    - groups: TraitorObjectiveGroups
    maxDifficulty: 5
  - type: AntagSelection
    agentName: traitor-round-end-agent-name

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseTraitorRule
  id: Traitor
  components:
  - type: GameRule
    minPlayers: 3
    delay:
      min: 240
      max: 420
  - type: AntagSelection
    definitions:
    - prefRoles: [ Traitor ]
      max: 8
      playerRatio: 10
      blacklist:
        components:
        - AntagImmune
      lateJoinAdditional: true
      mindRoles:
      - MindRoleTraitor

- type: entity
  id: TraitorReinforcement
  parent: Traitor
  components:
  - type: TraitorRule
    giveUplink: false
    giveCodewords: false # It would actually give them a different set of codewords than the regular traitors, anyway
    giveBriefing: false
  - type: AntagSelection
    definitions:
    - prefRoles: [ Traitor ]
      mindRoles:
      - MindRoleTraitorReinforcement

- type: entity
  id: Revolutionary
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: GameRule
    minPlayers: 3
  - type: RevolutionaryRule
  - type: AntagSelection
    definitions:
    - prefRoles: [ HeadRev ]
      max: 2
      playerRatio: 20 # WD
      briefing:
        text: head-rev-role-greeting
        color: CornflowerBlue
        sound: "/Audio/Ambience/Antag/headrev_start.ogg"
      startingGear: HeadRevGear
      components:
      - type: Revolutionary
      - type: HeadRevolutionary
      mindRoles:
      - MindRoleHeadRevolutionary

- type: entity
  id: Sandbox
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: SandboxRule

- type: entity
  id: Secret
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: SecretRule

- type: entity
  id: Zombie
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: GameRule
    minPlayers: 3
    delay:
      min: 600
      max: 900
  - type: ZombieRule
  - type: AntagSelection
    definitions:
    - prefRoles: [ InitialInfected ]
      max: 6
      playerRatio: 10
      blacklist:
        components:
        - ZombieImmune
        - AntagImmune
      briefing:
        text: zombie-patientzero-role-greeting
        color: Plum
        sound: "/Audio/Ambience/Antag/zombie_start.ogg"
      components:
      - type: PendingZombie
      - type: ZombifyOnDeath
      - type: IncurableZombie
      - type: InitialInfected
      mindRoles:
      - MindRoleInitialInfected

# event schedulers
- type: entity
  id: BasicStationEventScheduler
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: BasicStationEventScheduler

- type: entity
  id: RampingStationEventScheduler
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: RampingStationEventScheduler

- type: entity
  id: HellshiftStationEventScheduler
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: RampingStationEventScheduler
    chaosModifier: 5 # By default, one event each 30-10 seconds after two hours. Changing CVars will cause this to deviate.
    startingChaosRatio: 0.1
    shiftLengthModifier: 2.5

- type: entity
  id: IrregularStationEventScheduler
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: OscillatingStationEventScheduler
    minChaos: 0.8
    maxChaos: 14
    startingSlope: 0.2
    downwardsLimit: -0.35
    upwardsLimit: 0.4

# More likely to go down than up, so calmness prevails
- type: entity
  id: IrregularExtendedStationEventScheduler
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: OscillatingStationEventScheduler
    minChaos: 0.8
    maxChaos: 8
    startingSlope: -1
    downwardsLimit: -0.4
    upwardsLimit: 0.3
    downwardsBias: -1.1
    upwardsBias: 0.9

# variation passes
- type: entity
  id: BasicRoundstartVariation
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: RoundstartStationVariationRule
    rules:
#    - id: BasicPoweredLightVariationPass
    - id: BasicTrashVariationPass
#    - id: SolidWallRustingVariationPass
#    - id: ReinforcedWallRustingVariationPass
    - id: BasicPuddleMessVariationPass
      prob: 0.99
      orGroup: puddleMess
#    - id: BloodbathPuddleMessVariationPass
#      prob: 0.01
#      orGroup: puddleMess
    - id: SmugglerStashVariationPass
      prob: 0.90

