- type: entity
  parent: ComputerSurveillanceWirelessCameraMonitor
  id: ComputerTelevision
  name: "дерев'яний телевізор"
  description: "Нарешті тут з'явився пристойний прийом..."
  components:
  - type: Sprite
    noRot: true
    drawdepth: SmallObjects
    layers:
    - map: ["computerLayerBody"]
      state: television
    - map: ["computerLayerScreen"]
      state: detective_television
  - type: Computer
    board: ComputerTelevisionCircuitboard
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#b89f25"
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 200
        mask:
          - TabletopMachineMask
        layer:
          - TabletopMachineLayer

- type: entity
  parent: BaseComputer
  id: WallmountTelescreenFrame
  name: "рама телескопічного екрану"
  description: "Нарешті тут з'явився пристойний прийом..."
  components:
  - type: Construction
    graph: WallmountTelescreen
    node: TelescreenFrame
  - type: Sprite
    drawdepth: WallMountedItems
    sprite: Structures/Machines/computers.rsi
    layers:
      - map: ["computerLayerBody"]
        state: telescreen_frame
      - map: ["computerLayerScreen"]
        state: telescreen
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.20,-0.10,0.25,0.35"
        density: 250
        mask:
          - FullTileMask
        layer:
          - WallLayer
  - type: WallMount
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors: #excess damage, don't spawn entities.
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Transform
    anchored: true

- type: entity
  parent: WallmountTelescreenFrame
  id: WallmountTelescreen
  suffix: camera monitor
  name: "телеекран"
  description: "Нарешті тут з'явився пристойний прийом..."
  components:
  - type: Construction
    graph: WallmountTelescreen
    node: Telescreen
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#b89f25"
  - type: DeviceNetwork
    deviceNetId: Wired
    receiveFrequencyId: SurveillanceCamera
    transmitFrequencyId: SurveillanceCamera
  - type: WiredNetworkConnection
  - type: DeviceNetworkRequiresPower
  - type: SurveillanceCameraMonitor
  - type: ActivatableUI
    key: enum.SurveillanceCameraMonitorUiKey.Key
  - type: ActivatableUIRequiresPower
  - type: UserInterface
    interfaces:
        enum.SurveillanceCameraMonitorUiKey.Key:
          type: SurveillanceCameraMonitorBoundUserInterface

# Wall Televisions

- type: entity
  parent: WallmountTelescreenFrame
  id: WallmountTelevisionFrame
  name: "телевізійна рамка"
  description: "Нарешті тут з'явився пристойний прийом..."
  components:
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.75,-0.10,0.75,0.35"
        density: 75
        mask:
          - FullTileMask
        layer:
          - WallLayer
  - type: Construction
    graph: WallmountTelevision
    node: TelevisionFrame
  - type: Sprite
    drawdepth: WallMountedItems
    sprite: Structures/Wallmounts/flatscreentv.rsi
    layers:
      - map: ["computerLayerBody"]
        state: television_wall
      - map: ["computerLayerScreen"]
        state: television_wallscreen

- type: entity
  parent: WallmountTelevisionFrame
  id: WallmountTelevision
  suffix: entertainment
  name: "телебачення"
  description: "Нарешті тут з'явився пристойний прийом..."
  components:
  - type: Construction
    graph: WallmountTelevision
    node: Television
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: SurveillanceCamera
    transmitFrequencyId: SurveillanceCamera
  - type: WirelessNetworkConnection
    range: 200
  - type: DeviceNetworkRequiresPower
  - type: Speech
  - type: SurveillanceCameraSpeaker
  - type: SurveillanceCameraMonitor
  - type: ActivatableUI
    key: enum.SurveillanceCameraMonitorUiKey.Key
  - type: ActivatableUIRequiresPower
  - type: UserInterface
    interfaces:
        enum.SurveillanceCameraMonitorUiKey.Key:
          type: SurveillanceCameraMonitorBoundUserInterface
  - type: PointLight
    radius: 1.5
    energy: 1.6
    color: "#b89f25"
