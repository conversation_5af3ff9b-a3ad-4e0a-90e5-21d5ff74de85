- type: construction
  name: "паву<PERSON><PERSON><PERSON>на стіна"
  id: WallWeb
  graph: WebStructures
  startNode: start
  targetNode: wall
  category: construction-category-structures
  description: "Досить слабка, але шовковисто-гладка стіна."
  icon:
    sprite: Structures/Walls/web.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
  entityWhitelist:
    tags:
    - SpiderCraft
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "павутинний стіл"
  id: TableWeb
  graph: WebStructures
  startNode: start
  targetNode: table
  category: construction-category-furniture
  description: "Необхідний для будь-якої серйозної веб-розробки."
  icon:
    sprite: Structures/Furniture/Tables/web.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
  entityWhitelist:
    tags:
    - SpiderCraft
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "павутинне ліжко"
  id: WebBed
  graph: WebStructures
  startNode: start
  targetNode: bed
  category: construction-category-furniture
  description: "Цікавий факт: те, що ви їсте павуків уві сні - неправда."
  icon:
    sprite: Structures/Web/bed.rsi
    state: icon
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
  entityWhitelist:
    tags:
    - SpiderCraft
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "паутинне крісло"
  id: ChairWeb
  graph: WebStructures
  startNode: start
  targetNode: chair
  category: construction-category-furniture
  description: "Хочеш серйозно зайнятися веб-розробкою? Сідай на цей стілець!"
  icon:
    sprite: Structures/Web/chair.rsi
    state: icon
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  entityWhitelist:
    tags:
    - SpiderCraft

- type: construction
  name: "павутинний ящик"
  id: CrateWeb
  graph: WebStructures
  startNode: start
  targetNode: crate
  category: construction-category-storage
  description: "Для зберігання їжі та інших речей. Не такий міцний, як звичайний ящик, і не може бути заварений."
  icon:
    sprite: Structures/Storage/Crates/web.rsi
    state: icon
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
  entityWhitelist:
    tags:
    - SpiderCraft

- type: construction
  name: "павутинні двері"
  id: WebDoor
  graph: WebStructures
  startNode: start
  targetNode: door
  category: construction-category-structures
  description: "Ручні дверцята з павутиння, зазвичай розміщені безпосередньо перед ямою або пасткою."
  icon:
    sprite: Structures/Doors/web_door.rsi
    state: closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  entityWhitelist:
    tags:
    - SpiderCraft
  conditions:
    - !type:TileNotBlocked
