- type: entity
  id: KitchenMicrowave
  parent: [ BaseMachinePowered, SmallConstructibleMachine ]
  name: "мікрохвильова піч"
  description: "Це магія."
  components:
  - type: Microwave
    capacity: 10
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.MicrowaveVisualState.Bloody:
        bloody:
          True: { visible: true }
          False: { visible: false }
        bloodyunshaded:
          True: { visible: true }
          False: { visible: false }
      enum.PowerDeviceVisuals.VisualState:
        enum.MicrowaveVisualizerLayers.Base:
          Idle: { state: "mw" }
          Broken: { state: "mwb" }
          Cooking: { state: "mw" }
        enum.MicrowaveVisualizerLayers.BaseUnlit:
          Idle: { state: "mw_unlit" }
          Broken: { state: "mw_unlit" }
          Cooking: { state: "mw_running_unlit" }
        bloodyunshaded:
          Idle: { visible: false }
          Broken: { visible: false }
      enum.PowerDeviceVisuals.Powered:
        enum.MicrowaveVisualizerLayers.BaseUnlit:
          True: { visible: true }
          False: { visible: false }
  - type: ActivatableUI
    key: enum.MicrowaveUiKey.Key
  - type: DeviceLinkSink
    ports:
    - On
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: BasicDevice
  - type: WirelessNetworkConnection
    range: 200
  - type: UserInterface
    interfaces:
      enum.MicrowaveUiKey.Key:
        type: MicrowaveBoundUserInterface
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.3,-0.16,0.3,0.16"
        density: 190
        mask:
        - TabletopMachineMask
        layer:
        - TabletopMachineLayer
  - type: Sprite
    sprite: Structures/Machines/microwave.rsi
    drawdepth: SmallObjects
    snapCardinals: true
    layers:
    - state: mw0
      map: ["enum.MicrowaveVisualizerLayers.Base"]
    - state: mw_unlit
      shader: unshaded
      map: ["enum.MicrowaveVisualizerLayers.BaseUnlit"]
    - state: mwbloody0
      map: ["bloody"]
      visible: false
    - state: mwbloody1
      shader: unshaded
      map: ["bloodyunshaded"]
      visible: false
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: ["Breakage"]
  - type: Explosive
    explosionType: Default
    maxIntensity: 10
    totalIntensity: 5
    intensitySlope: 5
    canCreateVacuum: false
    deleteAfterExplosion: false
  - type: ApcPowerReceiver
    powerLoad: 400
  - type: Machine
    board: MicrowaveMachineCircuitboard
  - type: ContainerContainer
    containers:
      microwave_entity_container: !type:Container
      machine_board: !type:Container
      machine_parts: !type:Container
  - type: EmptyOnMachineDeconstruct
    containers:
    - microwave_entity_container
  - type: GuideHelp
    guides:
    - Chef

- type: entity
  id: SyndicateMicrowave
  parent: KitchenMicrowave
  name: "мікрохвильова піч donk co"
  description: "Настільки просунутий, що може приготувати кармазини всього за 2,5 секунди!"
  components:
  - type: Microwave
    cookTimeMultiplier: 0.5
    capacity: 10
    canMicrowaveIdsSafely: false
    explosionChance: 0.3
  - type: Sprite
    sprite: Structures/Machines/microwave_syndie.rsi
    drawdepth: SmallObjects
    snapCardinals: true
  - type: Machine
    board: SyndicateMicrowaveMachineCircuitboard
  - type: FoodRecipeProvider
    providedRecipes:
    - RecipeBaguetteSword
    - RecipeThrowingCroissant
