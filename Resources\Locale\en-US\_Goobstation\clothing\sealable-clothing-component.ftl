sealable-clothing-equipment-not-toggled = Deploy all parts first!
sealable-clothing-equipment-seal-failed = Sealing failed!
sealable-clothing-seal-verb = Toggle Seals

sealable-clothing-seal-up = The {$partName} is sealing
sealable-clothing-seal-up-helmet =  The {$partName} hisses as it closes.
sealable-clothing-seal-up-gauntlets = The {$partName} tightens around your fingers and wrists.
sealable-clothing-seal-up-chestplate = The {$partName} clenches tightly around your chest.
sealable-clothing-seal-up-boots = The {$partName} seals around your feet.

sealable-clothing-seal-down = The {$partName} is unsealing
sealable-clothing-seal-down-helmet = The {$partName} hisses open.
sealable-clothing-seal-down-gauntlets = The {$partName} become loose around your fingers.
sealable-clothing-seal-down-chestplate = The {$partName} releases your chest.
sealable-clothing-seal-down-boots= The {$partName} relaxes its grip on your legs.

sealable-clothing-sealed-process-toggle-fail = Suit is already shutting down!
sealable-clothing-unsealed-process-toggle-fail = Suit is already starting up!
sealable-clothing-sealed-toggle-fail = Deactivate the suit first!

sealable-clothing-not-powered = Suit is not powered!
sealable-clothing-open-sealed-panel-fail = Wiring panel is too tightly sealed!
sealable-clothing-close-panel-first = Close the wiring panel first!
