### Localization used for the list verbs command.
# Mostly help + error messages.

list-verbs-command-description = Перераховує всі дієслова, які гравець може використати на даному об'єкті.
list-verbs-command-help = listverbs <playerUid | "self"> <targetUid>

list-verbs-command-invalid-args = listverbs потрібно 2 аргументи.

list-verbs-command-invalid-player-uid = Не вдалося розібрати uid гравця, або не було передано "self".
list-verbs-command-invalid-target-uid = Не вдалося розібрати цільовий uid.

list-verbs-command-invalid-player-entity = Введений uid гравця не відповідає дійсній сутності.
list-verbs-command-invalid-target-entity = Цільовий uid не відповідає дійсному об'єкту.

list-verbs-verb-listing = { $type }: { $verb }
