## SpillTargetVerb

spill-target-verb-get-data-text = Spill liquid
spill-target-verb-activate-cannot-drain-message = You can't pour anything from {$owner}!
spill-target-verb-activate-is-empty-message = {$owner} is empty!

spill-melee-hit-attacker = You spill {$amount}u of {THE($spillable)} onto {THE($target)}!
spill-melee-hit-others = {CAPITALIZE(THE($attacker))} spills some of {THE($spillable)} onto {THE($target)}!

spill-land-spilled-on-other = {CAPITALIZE(THE($spillable))} spills some of its solution onto {THE($target)}!

spill-examine-is-spillable = This container looks spillable.
spill-examine-spillable-weapon = You could splash this onto someone with a melee attack.
