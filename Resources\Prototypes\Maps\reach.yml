- type: gameMap
  id: Reach
  mapName: Річ
  mapPath: /Maps/reach.yml
  minPlayers: 0
  maxPlayers: 7
  stations:
    Reach:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: '{0} Reach Transport {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: 'SC'
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/emergency.yml
        - type: StationJobs
          overflowJobs:
            - Passenger
          availableJobs:
            Captain: [ 1, 1 ]
            HeadOfSecurity: [ 1, 1 ]
            SecurityOfficer: [ 1, 3 ]
            CargoTechnician: [ 1, 1 ]
            SalvageSpecialist: [ 1, 1 ]
            Bartender: [ 1, 1 ]
            Botanist: [ 1, 1 ]
            Chef: [ 1, 1 ]
            MedicalDoctor: [ 1, 2 ]
            Chemist: [ 1, 1 ]
            Janitor: [ 1, 1 ]
            Musician: [ 1, 1 ]
            AtmosphericTechnician: [ 1, 1 ]
            StationEngineer: [ 1, 2 ]
            Scientist: [ 1, 1 ]
            Passenger: [ -1, -1 ]

        # Goobstation blob-config-start LOW
        - type: StationBlobConfig
          stageBegin: 15
          stageCritical: 200
          stageTheEnd: 400
        # Goobstation blob-config-end


