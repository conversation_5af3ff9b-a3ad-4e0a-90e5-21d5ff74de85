﻿- type: alert
  id: Essence
  category: Health #it's like ghostie health
  icons:
  - sprite: /Textures/Interface/Alerts/essence_counter.rsi
    state: essence0
  alertViewEntity: AlertEssenceSpriteView
  name: alerts-revenant-essence-name
  description: alerts-revenant-essence-desc

- type: alert
  id: Corporeal
  icons: [ /Textures/DeltaV/Mobs/Ghosts/revenant.rsi/icon.png ] # DeltaV - Custom revenant sprite
  name: alerts-revenant-corporeal-name
  description: alerts-revenant-corporeal-desc

- type: entity
  id: AlertEssenceSpriteView
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: /Textures/Interface/Alerts/essence_counter.rsi
    layers:
    - map: [ "enum.AlertVisualLayers.Base" ]
    - map: [ "enum.RevenantVisualLayers.Digit1" ]
    - map: [ "enum.RevenantVisualLayers.Digit2" ]
      offset: 0.125, 0
    - map: [ "enum.RevenantVisualLayers.Digit3" ]
      offset: 0.25, 0
