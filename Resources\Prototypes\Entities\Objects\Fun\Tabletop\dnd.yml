﻿# Board item (given to players)
- type: entity
  parent: BaseBoardEntity
  id: BaseBattlemap
  name: "карта бою"
  abstract: true
  description: "Карта бою для початку вашого епічного дослідження підземелля, фігурки не включені!"
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/grassbm.rsi
      state: icon
    - type: TabletopGame
      boardName: tabletop-battlemap-board-name
      setup:
        !type:TabletopEmptySetup
        boardPrototype: GrassBoardTabletop

- type: entity
  parent: BaseBattlemap
  id: GrassBattlemap
  name: "карта бою на траві"
  description: "Карта бою для початку вашого епічного дослідження підземелля, фігурки не включені!"
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/Battlemaps/grassbm.rsi
    - type: TabletopGame
      setup:
        !type:TabletopEmptySetup
        boardPrototype: GrassBoardTabletop

- type: entity
  parent: BaseBattlemap
  id: MoonBattlemap
  name: "карта бою на Місяці"
  description: "Карта бою для початку вашого епічного дослідження Місяця (деталі не включені)!"
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/Battlemaps/moonbm.rsi
    - type: TabletopGame
      setup:
        !type:TabletopEmptySetup
        boardPrototype: MoonBoardTabletop

- type: entity
  parent: BaseBattlemap
  id: SandBattlemap
  name: "піщана карта бою"
  description: "Карта бою для початку ваших епічних пляжних епізодів (деталі не включені)!"
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/Battlemaps/sandbm.rsi
    - type: TabletopGame
      setup:
        !type:TabletopEmptySetup
        boardPrototype: SandBoardTabletop

- type: entity
  parent: BaseBattlemap
  id: SnowBattlemap
  name: "карта снігової битви"
  description: "Бойова карта для початку вашого холодного дослідження, деталі не включені!" # if this isn't funny enough i can remove it
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/Battlemaps/snowbm.rsi
    - type: TabletopGame
      setup:
        !type:TabletopEmptySetup
        boardPrototype: SnowBoardTabletop

- type: entity
  parent: BaseBattlemap
  id: ShipBattlemap
  name: "карта бою корабля"
  description: "Карта бою для початку вашого епічного дослідження космосу (фігурки не включені)!"
  components:
    - type: Sprite
      sprite: Objects/Fun/Tabletop/Battlemaps/shipbm.rsi
    - type: TabletopGame
      size: 543, 543
      setup:
        !type:TabletopEmptySetup
        boardPrototype: ShipBoardTabletop

# Background entity (actually shown in the board)
- type: entity
  parent: BaseBoardTabletop
  id: GrassBoardTabletop
  name: "карта бою на траві"
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Fun/Tabletop/Battlemaps/grassbm_tabletop.rsi
    state: tabletop
    noRot: false
    drawdepth: FloorTiles

- type: entity
  parent: BaseBoardTabletop
  id: MoonBoardTabletop
  name: "карта бою на траві"
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Fun/Tabletop/Battlemaps/moonbm_tabletop.rsi
    state: tabletop

- type: entity
  parent: BaseBoardTabletop
  id: SandBoardTabletop
  name: "піщана карта бою"
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Fun/Tabletop/Battlemaps/sandbm_tabletop.rsi
    state: tabletop

- type: entity
  parent: BaseBoardTabletop
  id: SnowBoardTabletop
  name: "карта снігової битви"
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Fun/Tabletop/Battlemaps/snowbm_tabletop.rsi
    state: tabletop

- type: entity
  parent: BaseBoardTabletop
  id: ShipBoardTabletop
  name: "карта бою корабля"
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Fun/Tabletop/Battlemaps/shipbm_tabletop.rsi
    state: tabletop
