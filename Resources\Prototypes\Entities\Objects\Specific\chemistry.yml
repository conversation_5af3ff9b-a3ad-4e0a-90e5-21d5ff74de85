- type: entity
  parent: BaseItem
  id: BaseBeaker
  abstract: true
  components:
  - type: Tag
    tags:
    - GlassBeaker
  - type: Sprite
    sprite: Objects/Specific/Chemistry/beaker.rsi
    layers:
      - state: beaker
      - state: beaker1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
  - type: Item
    sprite: Objects/Specific/Chemistry/beaker.rsi
  - type: MeleeWeapon
    soundNoDamage:
      path: "/Audio/Effects/Fluids/splat.ogg"
    damage:
      types:
        Blunt: 0
  - type: SolutionContainerManager
    solutions:
      beaker:
        maxVol: 50
  - type: MixableSolution
    solution: beaker
  - type: FitsInDispenser
    solution: beaker
  - type: RefillableSolution
    solution: beaker
  - type: DrainableSolution
    solution: beaker
  - type: ExaminableSolution
    solution: beaker
  - type: DrawableSolution
    solution: beaker
  - type: InjectableSolution
    solution: beaker
  - type: SolutionTransfer
    canChangeTransferAmount: true
  - type: SolutionItemStatus
    solution: beaker
  - type: UserInterface
    interfaces:
      enum.TransferAmountUiKey.Key:
        type: TransferAmountBoundUserInterface
  - type: Drink
    solution: beaker
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 6
    fillBaseName: beaker
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Glass
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 5
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
          params:
            volume: -4
      - !type:SpillBehavior
        solution: beaker
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlass:
            min: 1
            max: 1
        transferForensics: true
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: DamageOnLand
    damage:
      types:
        Blunt: 10 # glass resistance set reduces damage. Need to land twice (w/o hitting wall)
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 5
  - type: DamageOnHighSpeedImpact
    minimumSpeed: 2
    damage:
      types:
        Blunt: 5
  - type: StaticPrice
    price: 30

- type: entity
  parent: BaseItem
  id: BaseBeakerMetallic
  abstract: true
  components:
  - type: Tag
    tags:
    - GlassBeaker
  - type: Sprite
    sprite: Objects/Specific/Chemistry/beaker.rsi
    layers:
      - state: beaker
      - state: beaker1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
  - type: Item
    sprite: Objects/Specific/Chemistry/beaker.rsi
  - type: MeleeWeapon
    soundNoDamage:
      path: "/Audio/Effects/Fluids/splat.ogg"
    damage:
      types:
        Blunt: 0
  - type: SolutionContainerManager
    solutions:
      beaker:
        maxVol: 50
  - type: MixableSolution
    solution: beaker
  - type: FitsInDispenser
    solution: beaker
  - type: RefillableSolution
    solution: beaker
  - type: DrainableSolution
    solution: beaker
  - type: ExaminableSolution
    solution: beaker
  - type: DrawableSolution
    solution: beaker
  - type: InjectableSolution
    solution: beaker
  - type: SolutionTransfer
    canChangeTransferAmount: true
  - type: SolutionItemStatus
    solution: beaker
  - type: UserInterface
    interfaces:
      enum.TransferAmountUiKey.Key:
        type: TransferAmountBoundUserInterface
  - type: Drink
    solution: beaker
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 6
    fillBaseName: beaker
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Glass
  - type: StaticPrice
    price: 30

- type: entity
  name: "мензурка"
  parent: BaseBeaker
  description: "Використовується для зберігання помірної кількості хімічних речовин і розчинів."
  id: Beaker
  components:
  - type: Spillable
    solution: beaker
  - type: StaticPrice
    price: 10
  - type: PhysicalComposition
    materialComposition:
      Glass: 50
  - type: SolutionContainerVisuals
    maxFillLevels: 6
    fillBaseName: beaker
    inHandsMaxFillLevels: 3
    inHandsFillBaseName: -fill-

- type: entity
  name: "мензурка з кріоксадоном"
  description: "Заповнені реагентом, що використовується в кріогенних пробірках."
  parent: Beaker
  id: CryoxadoneBeakerSmall
  components:
  - type: SolutionContainerManager
    solutions:
      beaker:
        maxVol: 50
        reagents:
        - ReagentId: Cryoxadone
          Quantity: 50

- type: entity
  name: "велика мензурка"
  parent: BaseBeaker
  description: "Використовується для зберігання великої кількості хімічних речовин або розчинів."
  id: LargeBeaker
  components:
  - type: Spillable
    solution: beaker
  - type: Sprite
    sprite: Objects/Specific/Chemistry/beaker_large.rsi
    layers:
      - state: beakerlarge
      - state: beakerlarge1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
  - type: Item
    size: Normal
    sprite: Objects/Specific/Chemistry/beaker_large.rsi
  - type: SolutionContainerManager
    solutions:
      beaker:
        maxVol: 100
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 6
    fillBaseName: beakerlarge
    inHandsMaxFillLevels: 4
    inHandsFillBaseName: -fill-
  - type: PhysicalComposition
    materialComposition:
      Glass: 100
  - type: StaticPrice
    price: 20

- type: entity
  name: "кріостазисна мензурка"
  parent: BaseBeakerMetallic
  description: "Використовується для утримання хімічних речовин або розчинів, що не вступають у реакцію."
  id: CryostasisBeaker
  components:
  - type: Spillable
    solution: beaker
  - type: Sprite
    sprite: Objects/Specific/Chemistry/beaker_cryostasis.rsi
    layers:
      - state: beakernoreact
  - type: SolutionContainerManager
    solutions:
      beaker:
        maxVol: 60
        canReact: false
  - type: ReverseEngineering # Delta
    difficulty: 3
    recipes:
      - CryostasisBeaker

- type: entity
  name: "блюспейс мензурка"
  parent: BaseBeakerMetallic
  description: "Працює на експериментальній технології bluespace."
  id: BluespaceBeaker
  components:
  - type: Spillable
    solution: beaker
  - type: Sprite
    sprite: _Impstation/Objects/Specific/Chemistry/beaker_bluespace.rsi # imp texture
    # sprite: Objects/Specific/Chemistry/beaker_bluespace.rsi
    layers:
      - state: beakerbluespace
  - type: SolutionContainerManager
    solutions:
      beaker:
        maxVol: 1000
  - type: ReverseEngineering
    difficulty: 4
    recipes:
      - BluespaceBeaker

- type: entity
  name: "піпетка"
  parent: BaseItem
  description: "Використовується для переміщення невеликих кількостей хімічного розчину між контейнерами."
  id: Dropper
  components:
  - type: Sprite
    sprite: Objects/Specific/Chemistry/dropper.rsi
    layers:
      - state: dropper
      - state: dropper1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
  - type: SolutionContainerManager
    solutions:
      injector:
        maxVol: 5
  - type: Injector
    injectOnly: false
    ignoreMobs: true
    minTransferAmount: 1
    maxTransferAmount: 5
    transferAmount: 1
    toggleState: 1 # draw
  - type: ExaminableSolution
    solution: dropper
  - type: UserInterface
    interfaces:
      enum.TransferAmountUiKey.Key:
        type: TransferAmountBoundUserInterface
  - type: Spillable
    solution: injector
  - type: Item
    sprite: Objects/Specific/Chemistry/dropper.rsi
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 1
    fillBaseName: dropper
    inHandsMaxFillLevels: 1
    inHandsFillBaseName: -fill-
  - type: StaticPrice
    price: 40

- type: entity
  name: "боргдроппер"
  parent: Dropper
  description: "Використовується для перенесення невеликих кількостей хімічних розчинів між контейнерами. Розширений для використання медичними боргами."
  id: BorgDropper
  components:
  - type: Sprite
    sprite: Objects/Specific/Chemistry/dropper.rsi
    layers:
      - state: borgdropper
      - state: dropper1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
  - type: SolutionContainerManager
    solutions:
      injector:
        maxVol: 10
  - type: Item
    sprite: Objects/Specific/Chemistry/dropper.rsi

- type: entity
  name: "одноразовий шприц"
  parent: BaseItem
  description: "Використовується для ін'єкцій мобів з контрольованими дозами певних реагентів. Одноразове використання."
  id: DisposableSyringe
  components:
  - type: Sprite
    sprite: Objects/Specific/Chemistry/disposablesyringe.rsi
    layers:
      - state: syringe1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
      - state: syringe_base0
        map: ["enum.SolutionContainerLayers.Base"]
  - type: Icon
    sprite: Objects/Specific/Chemistry/disposablesyringe.rsi
    state: "syringe_base0"
  - type: Item
    size: Tiny
    sprite: Objects/Specific/Chemistry/disposablesyringe.rsi
  - type: SolutionContainerManager
    solutions:
      injector:
        maxVol: 15
  - type: FillableOneTimeInjector
    transferAmount: 15
    toggleState: Draw
  - type: ExaminableSolution
    solution: injector
  - type: Spillable
    solution: injector
  - type: TrashOnSolutionEmpty
    solution: injector
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 2
    fillBaseName: syringe
    inHandsMaxFillLevels: 3
    inHandsFillBaseName: -fill-

- type: entity
  name: "шприц"
  parent: BaseItem
  description: "Використовується для відбору зразків крові у натовпу або для ін'єкцій реагентів."
  id: BaseSyringe
  abstract: true
  components:
  - type: Sprite
    sprite: Objects/Specific/Chemistry/syringe.rsi
    layers:
      - state: syringe1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
      - state: syringe_base0
        map: ["enum.SolutionContainerLayers.Base"]
  - type: Icon
    sprite: Objects/Specific/Chemistry/syringe.rsi
    state: "syringe_base0"
  - type: Item
    size: Tiny
    sprite: Objects/Specific/Chemistry/syringe.rsi
  - type: SolutionContainerManager
    solutions:
      injector:
        maxVol: 15
  - type: Injector
    injectOnly: false
    # GoobStation Start - Added embeddable properties for syringe gun
  - type: EmbeddableProjectile
    offset: "-0.1,0"
    minimumSpeed: 3
    removalTime: 0.25
    embedOnThrow: true
  - type: SolutionInjectOnEmbed
    transferAmount: 5
    solution: injector
  - type: Fixtures
    fixtures:
      fix1:
        shape: !type:PhysShapeCircle
          radius: 0.2
        density: 5
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.1,-0.3,0.1,0.3"
        hard: false
        mask:
        - Impassable
        - BulletImpassable
  - type: Projectile
    deleteOnCollide: false
    onlyCollideWhenShot: true
    damage:
      types:
        Piercing: 5
    # GoobStation End
  - type: ExaminableSolution
    solution: injector
  - type: Spillable
    solution: injector
  - type: TrashOnSolutionEmpty
    solution: injector
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 2
    fillBaseName: syringe
    inHandsMaxFillLevels: 3
    inHandsFillBaseName: -fill-
  - type: Tag
    tags:
    - Syringe
    - SyringeGunAmmo # GoobStation

- type: entity
  parent: BaseSyringe
  id: Syringe
  components:
  - type: Injector
    transferAmount: 15
    toggleState: Draw
  - type: Tag
    tags:
    - Syringe
    - Trash
    - SyringeGunAmmo # GoobStation

- type: entity
  parent: BaseSyringe
  id: PrefilledSyringe
  components:
  - type: Injector
    toggleState: Inject

- type: entity
  id: SyringeBluespace
  parent: BaseSyringe
  name: "блюспейс шприц"
  description: "Ін'єкції за допомогою передової технології bluespace."
  components:
  - type: Sprite
    layers:
    - state: syringe1
      map: ["enum.SolutionContainerLayers.Fill"]
      visible: false
    - state: bluespace_base0
      map: ["enum.SolutionContainerLayers.Base"]
  - type: Icon
    sprite: Objects/Specific/Chemistry/syringe.rsi
    state: bluespace_base0
  - type: SolutionContainerManager
    solutions:
      injector:
        maxVol: 100
  - type: Injector
    delay: 2.5
    injectOnly: false
  - type: SolutionContainerVisuals
    maxFillLevels: 2
    fillBaseName: syringe
  - type: Tag
    tags:
    - Syringe
    - Trash
  - type: ReverseEngineering # Delta
    difficulty: 4
    recipes:
      - SyringeBluespace

- type: entity
  id: SyringeCryostasis
  parent: BaseSyringe
  name: "кріостазний шприц"
  description: "Шприц, який використовується для зберігання хімічних речовин або розчинів без реакції."
  components:
  - type: Sprite
    layers:
    - state: syringe1
      map: ["enum.SolutionContainerLayers.Fill"]
      visible: false
    - state: cryo_base0
      map: ["enum.SolutionContainerLayers.Base"]
  - type: Icon
    sprite: Objects/Specific/Chemistry/syringe.rsi
    state: cryo_base0
  - type: SolutionContainerManager
    solutions:
      injector:
        maxVol: 10
        canReact: false
  - type: Injector
    injectOnly: false
    minTransferAmount: 5
    maxTransferAmount: 10
    transferAmount: 10
  - type: Tag
    tags:
    - Syringe
    - Trash
  - type: ReverseEngineering # Delta
    difficulty: 3
    recipes:
      - SyringeCryostasis

- type: entity
  name: "таблетка"
  parent: BaseItem
  id: Pill
  description: "Це не супозиторії."
  components:
  - type: Sprite
    sprite: Objects/Specific/Chemistry/pills.rsi
    state: pill
  - type: Item
    size: Tiny
    sprite: Objects/Specific/Chemistry/pills.rsi
  - type: Pill
  - type: Food
    forceFeedDelay: 1
    transferAmount: null
    eatMessage: food-swallow
    useSound: /Audio/Items/pill.ogg
  - type: BadFood
  - type: FlavorProfile
    ignoreReagents: []
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
  - type: SolutionSpiker
    sourceSolution: food
  - type: Extractable
    grindableSolutionName: food
  - type: StaticPrice
    price: 0
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 10
      behaviors:
      - !type:SpillBehavior
        solution: food
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Tag
    tags:
    - Pill

- type: entity
  name: "баночка з-під таблеток"
  id: PillCanister
  parent: BaseStorageItem
  description: "Містить до 10 таблеток."
  components:
  - type: Item
    size: Tiny
    sprite: Objects/Specific/Chemistry/pills_canister.rsi
  - type: Sprite
    sprite: Objects/Specific/Chemistry/pills_canister.rsi
    state: pill_canister
  - type: Tag
    tags:
      - PillCanister
  - type: Storage
    grid:
    - 0,0,4,1
    quickInsert: true
    areaInsert: true
    areaInsertRadius: 1
    storageInsertSound: /Audio/Effects/pill_insert.ogg
    storageRemoveSound: /Audio/Effects/pill_remove.ogg
  - type: Dumpable
