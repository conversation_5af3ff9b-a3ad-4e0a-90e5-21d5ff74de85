using Content.Client.UserInterface.Controls;
using Content.Shared._Shitmed.Autodoc;
using Content.Shared._Shitmed.Medical.Surgery;
using Content.Shared._Shitmed.Medical.Surgery.Conditions;
using Content.Shared.Body.Part;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;

namespace Content.Client._Shitmed.Autodoc;

[GenerateTypedNameReferences]
public sealed partial class PickSurgeryWindow : FancyWindow
{
    [Dependency] private readonly IEntityManager _entMan = default!;
    [Dependency] private readonly IPrototypeManager _proto = default!;
    private readonly SharedSurgerySystem _surgery;

    public event Action<IAutodocStep>? OnAddStep;

    private BodyPartType? _part;
    private BodyPartSymmetry? _symmetry;
    private EntProtoId<SurgeryComponent>? _surgeryId;

    public PickSurgeryWindow()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        _surgery = _entMan.System<SharedSurgerySystem>();

        OnAddStep += _ => Close();

        var options = new RadioOptions<BodyPartSymmetry?>(RadioOptionsLayout.Horizontal);
        options.ButtonStyle = "OpenBoth";
        options.FirstButtonStyle = "OpenRight";
        options.LastButtonStyle = "OpenLeft";
        options.AddItem(Loc.GetString("autodoc-body-symmetry-ignored"), null);

        foreach (var symmetry in Enum.GetValues<BodyPartSymmetry>())
        {
            var name = Loc.GetString("autodoc-body-symmetry-" + symmetry.ToString());
            options.AddItem(name, symmetry);
        }

        Symmetry.AddChild(options);

        options.OnItemSelected += args =>
        {
            args.Button.Select(args.Id);
            _symmetry = args.Button.SelectedValue;
            UpdateSurgeries();
        };

        foreach (var part in Enum.GetValues<BodyPartType>())
        {
            var name = Loc.GetString("autodoc-body-part-" + part.ToString());
            Parts.AddItem(name, metadata: part);
        }

        Parts.OnItemSelected += args =>
        {
            if (Parts[args.ItemIndex].Metadata is not BodyPartType part)
                return;

            _part = part;
            UpdateSubmit();
            UpdateSurgeries();
        };
        Parts.OnItemDeselected += _ =>
        {
            _part = null;
            SubmitButton.Disabled = true;
        };

        Surgeries.OnItemSelected += args =>
        {
            if (Surgeries[args.ItemIndex].Metadata is not EntProtoId<SurgeryComponent> id)
                return;

            _surgeryId = id;
            UpdateSubmit();
        };
        Surgeries.OnItemDeselected += _ =>
        {
            _surgeryId = null;
            SubmitButton.Disabled = true;
        };

        SubmitButton.OnPressed += _ =>
        {
            var step = new SurgeryAutodocStep()
            {
                Part = _part!.Value,
                Symmetry = _symmetry,
                Surgery = _surgeryId!.Value
            };
            OnAddStep?.Invoke(step);
        };

        UpdateSurgeries();
    }

    // doesn't handle prototype reload so you have to reopen the window to see new surgeries
    private void UpdateSurgeries()
    {
        Surgeries.Clear();

        foreach (var id in _surgery.AllSurgeries)
        {
            var name = _proto.Index(id).Name;
            var protoId = new EntProtoId<SurgeryComponent>(id);
            if (_part is not BodyPartType part)
            {
                Surgeries.AddItem(name, metadata: protoId);
                continue;
            }

            var ent = _surgery.GetSingleton(protoId);
            if (ent is null)
                continue;

            if (!_entMan.TryGetComponent<SurgeryPartConditionComponent>(ent.Value, out var comp))
            {
                Surgeries.AddItem(name, metadata: protoId);
                continue;
            }

            var partOk = comp.Part == part;
            var symmetryOk = (comp.Symmetry == null || _symmetry == null) ? true : comp.Symmetry == _symmetry;

            var passesFilter = (partOk && symmetryOk) ^ comp.Inverse;

            if (passesFilter)
                Surgeries.AddItem(name, metadata: protoId);
        }
        Surgeries.SortItemsByText();
    }

    private void UpdateSubmit()
    {
        // symmetry is optional, others are not
        SubmitButton.Disabled = _part == null || _surgeryId == null;
    }
}
