<controls:SurgeryWindow
    xmlns="https://spacestation14.io"
    xmlns:controls="clr-namespace:Content.Client._Shitmed.Medical.Surgery"
    xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls"
    xmlns:graphics="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
    MinSize="400 400">
    <BoxContainer Orientation="Vertical" HorizontalExpand="True" VerticalExpand="True">
        <BoxContainer Orientation="Horizontal" HorizontalExpand="True" Margin="0 0 0 10">
            <Button Name="PartsButton" Access="Public" Text="{Loc 'surgery-ui-window-parts'}"
                    HorizontalExpand="True" StyleClasses="OpenBoth" />
            <Button Name="SurgeriesButton" Access="Public" Text="{Loc 'surgery-ui-window-surgeries'}"
                    HorizontalExpand="True" StyleClasses="OpenBoth" />
            <Button Name="StepsButton" Access="Public" Text="{Loc 'surgery-ui-window-steps'}"
                    HorizontalExpand="True" StyleClasses="OpenBoth" />
        </BoxContainer>
        <cc:HSeparator />
        <ScrollContainer VScrollEnabled="True" HorizontalExpand="True" VerticalExpand="True">
            <BoxContainer Name="Parts" Access="Public" Orientation="Vertical" Visible="False" />
            <BoxContainer Name="Surgeries" Access="Public" Orientation="Vertical" Visible="False" />
            <BoxContainer Name="Steps" Access="Public" Orientation="Vertical" Visible="False" />
        </ScrollContainer>
    </BoxContainer>
</controls:SurgeryWindow>
