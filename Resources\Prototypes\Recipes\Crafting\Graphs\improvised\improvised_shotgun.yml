- type: constructionGraph
  id: ImprovisedShotgunGraph
  start: start
  graph:
  - node: start
    edges:
    - to: shotgun
      steps:
      - tag: Pipe
        icon:
          sprite: Structures/Piping/Atmospherics/pipe.rsi
          state: pipeStraight
        name: pipe
      - tag: ModularReceiver
        icon:
          sprite: Objects/Misc/modular_receiver.rsi
          state: icon
        name: modular receiver
      - tag: RifleStock
        icon:
          sprite: Objects/Misc/rifle_stock.rsi
          state: icon
        name: rifle stock
      - material: Cloth
        amount: 3
        doAfter: 10
  - node: shotgun
    entity: WeaponShotgunImprovised
