﻿# Special entity that indicates that there is something in a player's pockets, without showing the actual entity.
# This **could** just be done via UI elements, but the existing UI uses sprite-views & the examine-system. So this is easier than special casing this.

- type: entity
  id: StrippingHiddenEntity
  name: "Прихована сутність"
  description: "У цій кишені щось є." #Or maybe they ar... nah... too obvious a joke.
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    texture: Interface/VerbIcons/information.svg.192dpi.png
    scale: 0.3,0.3
