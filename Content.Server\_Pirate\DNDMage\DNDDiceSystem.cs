using Content.Server.Body.Components;
using Content.Server.Body.Systems;
using Content.Server.Popups;
using Content.Server.Storage.EntitySystems;
using Content.Shared._Pirate.DNDMage;
using Content.Shared.Damage;
using Content.Shared.Damage.Components;
using Content.Shared.Damage.Prototypes;
using Content.Shared.Dice;
using Content.Shared.FixedPoint;
using Content.Shared.Hands.Components;
using Content.Shared.Hands.EntitySystems;
using Content.Shared.Inventory;
using Content.Shared.Mobs;
using Content.Shared.Mobs.Components;
using Content.Shared.Mobs.Systems;
using Content.Shared.Tag;
using Content.Shared.Throwing;
using Content.Shared.Verbs;
using Robust.Shared.Audio;
using Robust.Shared.Audio.Systems;
using Robust.Shared.Containers;
using Robust.Shared.Prototypes;
using Robust.Shared.Random;
using Robust.Shared.Timing;
using Robust.Shared.Utility;
using System.Numerics;
using Content.Shared.Damage.Systems;
using Content.Shared.StatusEffect;
using Content.Shared.Movement.Components;
using Content.Shared.Movement.Systems;

namespace Content.Server._Pirate.DNDMage;

public sealed class DNDDiceSystem : EntitySystem
{
    [Dependency] private readonly IRobustRandom _random = default!;
    [Dependency] private readonly PopupSystem _popupSystem = default!;
    [Dependency] private readonly SharedAudioSystem _audio = default!;
    [Dependency] private readonly IGameTiming _timing = default!;
    [Dependency] private readonly SharedHandsSystem _hands = default!;
    [Dependency] private readonly InventorySystem _inventory = default!;
    [Dependency] private readonly EntityLookupSystem _lookup = default!;
    [Dependency] private readonly SharedTransformSystem _transform = default!;
    [Dependency] private readonly TagSystem _tagSystem = default!;
    [Dependency] private readonly SharedContainerSystem _containerSystem = default!;
    [Dependency] private readonly SharedDiceSystem _diceSystem = default!;
    [Dependency] private readonly DamageableSystem _damageableSystem = default!;
    [Dependency] private readonly BloodstreamSystem _bloodstreamSystem = default!;
    [Dependency] private readonly MobStateSystem _mobStateSystem = default!;
    [Dependency] private readonly IPrototypeManager _prototypeManager = default!;
    [Dependency] private readonly DNDDiceBagSystem _diceBagSystem = default!;
    [Dependency] private readonly StaminaSystem _staminaSystem = default!;
    [Dependency] private readonly StatusEffectsSystem _statusEffectsSystem = default!;
    [Dependency] private readonly MovementSpeedModifierSystem _movement = default!;

    // Радіус для телепатичного повідомлення (приблизно 10 метрів)
    private const float TelepathicRadius = 10f;

    // Звук для повернення кістки
    private readonly SoundSpecifier _returnSound = new SoundPathSpecifier("/Audio/Magic/blink.ogg");

    public override void Initialize()
    {
        base.Initialize();

        // Підписуємось на подію приземлення кістки
        SubscribeLocalEvent<DNDDiceComponent, LandEvent>(OnLand);

        // Підписуємось на подію видалення з контейнера для відстеження походження кістки
        SubscribeLocalEvent<DNDDiceComponent, EntRemovedFromContainerMessage>(OnRemovedFromContainer);

        // Підписуємось на подію отримання дієслів для додавання "Saving Throw"
        SubscribeLocalEvent<DNDDiceComponent, GetVerbsEvent<AlternativeVerb>>(AddSavingThrowVerb);

    }

    /// <summary>
    /// Спроба виконати "Saving Throw" для мага ДНД
    /// </summary>
    public void TryPerformSavingThrow(EntityUid uid)
    {
        // Перевіряємо, чи існує сутність і чи має вона необхідні компоненти
        if (Deleted(uid) || !EntityManager.EntityExists(uid) || !HasComp<MetaDataComponent>(uid))
            return;

        // Перевіряємо, чи є у моба кістки з компонентом DNDBagDiceComponent
        // Спочатку перевіряємо інвентар
        if (_inventory.TryGetSlotEntity(uid, "belt", out var beltItem) &&
            beltItem.HasValue &&
            EntityManager.EntityExists(beltItem.Value) &&
            !Deleted(beltItem.Value) &&
            HasComp<MetaDataComponent>(beltItem.Value) &&
            _tagSystem.HasTag(beltItem.Value, "DiceBag"))
        {
            // Перевіряємо, чи є в сумці кістки
            if (_containerSystem.TryGetContainer(beltItem.Value, "storage-default", out var container))
            {
                foreach (var item in container.ContainedEntities)
                {
                    // Перевіряємо, чи існує сутність і чи має вона необхідні компоненти
                    if (!EntityManager.EntityExists(item) || Deleted(item) || !HasComp<MetaDataComponent>(item))
                        continue;

                    // Перевіряємо, чи це кістка з компонентом DNDBagDiceComponent
                    if (HasComp<DNDDiceComponent>(item) &&
                        TryComp<DNDBagDiceComponent>(item, out var bagComp) &&
                        bagComp.CanSavingThrow)
                    {
                        // Перевіряємо кулдаун
                        var currentTime = _timing.CurTime;
                        if (currentTime - bagComp.LastSavingThrowTime < bagComp.SavingThrowCooldown)
                            continue;

                        // Показуємо повідомлення про автоматичний "Saving Throw"
                        _popupSystem.PopupEntity("💫 Магічна кістка активує Saving Throw для порятунку! 💫", uid, uid);

                        // Активуємо "Saving Throw" безпосередньо без Timer.Spawn для кращої стабільності
                        PerformSavingThrowSafe(item, uid, bagComp);

                        // Активуємо тільки одну кістку
                        return;
                    }
                }
            }
        }

        // Якщо не знайшли кістки в сумці, перевіряємо руки
        if (TryComp<HandsComponent>(uid, out var handsComp))
        {
            foreach (var held in _hands.EnumerateHeld(uid, handsComp))
            {
                // Перевіряємо, чи існує сутність і чи має вона необхідні компоненти
                if (!EntityManager.EntityExists(held) || Deleted(held) || !HasComp<MetaDataComponent>(held))
                    continue;

                // Перевіряємо, чи це кістка з компонентом DNDBagDiceComponent
                if (HasComp<DNDDiceComponent>(held) &&
                    TryComp<DNDBagDiceComponent>(held, out var bagComp) &&
                    bagComp.CanSavingThrow)
                {
                    // Перевіряємо кулдаун
                    var currentTime = _timing.CurTime;
                    if (currentTime - bagComp.LastSavingThrowTime < bagComp.SavingThrowCooldown)
                        continue;

                    // Показуємо повідомлення про автоматичний "Saving Throw"
                    _popupSystem.PopupEntity("💫 Магічна кістка активує Saving Throw для порятунку! 💫", uid, uid);

                    // Активуємо "Saving Throw" безпосередньо без Timer.Spawn для кращої стабільності
                    PerformSavingThrowSafe(held, uid, bagComp);

                    // Активуємо тільки одну кістку
                    return;
                }
            }
        }
    }

    /// <summary>
    /// Додає дієслово "Saving Throw" до кістки, якщо вона має компонент DNDBagDiceComponent
    /// </summary>
    private void AddSavingThrowVerb(EntityUid uid, DNDDiceComponent component, GetVerbsEvent<AlternativeVerb> args)
    {
        if (!args.CanAccess || !args.CanInteract)
            return;

        // Перевіряємо, чи є у кістки компонент DNDBagDiceComponent
        if (!TryComp<DNDBagDiceComponent>(uid, out var bagComp) || !bagComp.CanSavingThrow)
            return;

        // Перевіряємо, чи не на кулдауні "saving throw"
        var currentTime = _timing.CurTime;
        var timeSinceLastUse = currentTime - bagComp.LastSavingThrowTime;
        if (timeSinceLastUse < bagComp.SavingThrowCooldown)
        {
            var remainingTime = bagComp.SavingThrowCooldown - timeSinceLastUse;
            var minutes = Math.Floor(remainingTime.TotalMinutes);
            var seconds = Math.Floor(remainingTime.TotalSeconds - minutes * 60);

            // Додаємо дієслово з інформацією про кулдаун
            var verb = new AlternativeVerb
            {
                Act = () => _popupSystem.PopupEntity($"Ви не можете використати Saving Throw ще {minutes:00}:{seconds:00}", args.User, args.User),
                Text = $"Saving Throw (Кулдаун: {minutes:00}:{seconds:00})",
                Priority = -1,
                Icon = new SpriteSpecifier.Texture(new ResPath("/Textures/Interface/VerbIcons/refresh.svg.192dpi.png"))
            };
            args.Verbs.Add(verb);
            return;
        }

        // Додаємо дієслово "Saving Throw"
        var savingThrowVerb = new AlternativeVerb
        {
            Act = () => PerformSavingThrow(uid, args.User, bagComp),
            Text = "Saving Throw",
            Priority = 10,
            Icon = new SpriteSpecifier.Texture(new ResPath("/Textures/Interface/VerbIcons/plus.svg.192dpi.png"))
        };
        args.Verbs.Add(savingThrowVerb);
    }

    /// <summary>
    /// Безпечна версія виконання "saving throw" з покращеною обробкою помилок
    /// </summary>
    private void PerformSavingThrowSafe(EntityUid diceUid, EntityUid userUid, DNDBagDiceComponent bagComp)
    {
        try
        {
            // Перевіряємо, чи існують сутності і чи мають вони необхідні компоненти
            if (Deleted(diceUid) || Deleted(userUid) ||
                !EntityManager.EntityExists(diceUid) || !EntityManager.EntityExists(userUid) ||
                !HasComp<MetaDataComponent>(diceUid) || !HasComp<MetaDataComponent>(userUid))
                return;

            // Оновлюємо час останнього використання
            bagComp.LastSavingThrowTime = _timing.CurTime;
            Dirty(diceUid, bagComp);

            // Показуємо повідомлення про використання "saving throw"
            _popupSystem.PopupEntity("🎲 Ви використовуєте Saving Throw! 🎲", userUid, userUid);

            // Програємо звук активації
            var activationSound = new SoundPathSpecifier("/Audio/Magic/blink.ogg");
            _audio.PlayPvs(activationSound, userUid);

            // Отримуємо позицію над магом
            if (!TryComp<TransformComponent>(userUid, out var userTransform))
                return;

            var userPosition = _transform.GetMapCoordinates(userUid);
            var dicePosition = userPosition.Offset(new Vector2(0, 0.5f)); // Трохи вище голови

            // Переміщуємо кістку над магом
            _transform.SetWorldPosition(diceUid, dicePosition.Position);

            // Кидаємо кістку
            if (TryComp<DiceComponent>(diceUid, out var diceComp))
            {
                // Генеруємо випадкове значення
                var roll = _random.Next(1, diceComp.Sides + 1);
                _diceSystem.SetCurrentSide(diceUid, roll, diceComp);

                // Показуємо результат кидка
                _popupSystem.PopupEntity($"🎯 Saving Throw: {roll}! 🎯", userUid);

                // Застосовуємо ефект в залежності від результату
                ApplySavingThrowEffectSafe(userUid, roll, diceComp.Sides);
            }
        }
        catch (Exception ex)
        {
            Log.Error($"Помилка при виконанні Saving Throw для {ToPrettyString(userUid)}: {ex}");
            // Показуємо повідомлення про помилку гравцю
            if (EntityManager.EntityExists(userUid) && !Deleted(userUid))
            {
                _popupSystem.PopupEntity("❌ Помилка при виконанні Saving Throw! ❌", userUid, userUid);
            }
        }
    }

    /// <summary>
    /// Виконує "saving throw" - кидає кістку над магом для його порятунку (застаріла версія)
    /// </summary>
    private void PerformSavingThrow(EntityUid diceUid, EntityUid userUid, DNDBagDiceComponent bagComp)
    {
        // Перенаправляємо на безпечну версію
        PerformSavingThrowSafe(diceUid, userUid, bagComp);
    }

    /// <summary>
    /// Безпечна версія застосування ефекту "saving throw" з покращеною обробкою помилок
    /// </summary>
    private void ApplySavingThrowEffectSafe(EntityUid userUid, int roll, int sides)
    {
        try
        {
            // Перевіряємо, чи існує сутність і чи має вона необхідні компоненти
            if (Deleted(userUid) || !EntityManager.EntityExists(userUid) || !HasComp<MetaDataComponent>(userUid))
                return;

            // Розраховуємо ефективність "saving throw"
            var successRate = (float) roll / sides;

            // Кидаємо кістки захисного поля та лікування (безпечно)
            ThrowDefenseAndHealingDiceSafe(userUid, successRate);

            // Застосовуємо ефект в залежності від результату
            if (roll == sides) // Критичний успіх
            {
                ApplyCriticalSuccessHealing(userUid);
            }
            else if (successRate >= 0.75f) // Дуже успішно
            {
                ApplyMajorHealing(userUid, successRate);
            }
            else if (successRate >= 0.5f) // Успішно
            {
                ApplyModerateHealing(userUid, successRate);
            }
            else if (successRate >= 0.25f) // Посередньо
            {
                ApplyMinorHealing(userUid, successRate);
            }
            else if (roll == 1) // Критична невдача
            {
                ApplyCriticalFailureDamage(userUid);
            }
            else // Невдача
            {
                // Показуємо ефект невдачі
                _popupSystem.PopupEntity("❌ Невдача! Нічого не відбувається! ❌", userUid, userUid);
            }
        }
        catch (Exception ex)
        {
            Log.Error($"Помилка при застосуванні ефекту Saving Throw для {ToPrettyString(userUid)}: {ex}");
            if (EntityManager.EntityExists(userUid) && !Deleted(userUid))
            {
                _popupSystem.PopupEntity("❌ Помилка при застосуванні ефекту! ❌", userUid, userUid);
            }
        }
    }

    /// <summary>
    /// Застосовує критичний успіх - повне зцілення
    /// </summary>
    private void ApplyCriticalSuccessHealing(EntityUid userUid)
    {
        try
        {
            // Повне зцілення
            var healAmount = (FixedPoint2) 100.0f;

            // Створюємо специфікації лікування для різних типів пошкоджень
            var healBlunt = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Blunt"), -healAmount);
            var healSlash = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Slash"), -healAmount);
            var healPierce = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Piercing"), -healAmount);
            var healHeat = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Heat"), -healAmount);
            var healCold = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Cold"), -healAmount);
            var healPoison = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Poison"), -healAmount);
            var healRadiation = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Radiation"), -healAmount);
            var healBloodloss = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Bloodloss"), -healAmount);
            var healAsphyxiation = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Asphyxiation"), -healAmount);

            // Застосовуємо лікування
            _damageableSystem.TryChangeDamage(userUid, healBlunt);
            _damageableSystem.TryChangeDamage(userUid, healSlash);
            _damageableSystem.TryChangeDamage(userUid, healPierce);
            _damageableSystem.TryChangeDamage(userUid, healHeat);
            _damageableSystem.TryChangeDamage(userUid, healCold);
            _damageableSystem.TryChangeDamage(userUid, healPoison);
            _damageableSystem.TryChangeDamage(userUid, healRadiation);
            _damageableSystem.TryChangeDamage(userUid, healBloodloss);
            _damageableSystem.TryChangeDamage(userUid, healAsphyxiation);

            // Зупиняємо кровотечу
            if (HasComp<BloodstreamComponent>(userUid))
            {
                _bloodstreamSystem.TryModifyBleedAmount(userUid, -100);
            }

            // Показуємо ефект критичного успіху
            _popupSystem.PopupEntity("✨ Критичний успіх! Ви повністю зцілені! ✨", userUid, userUid);
        }
        catch (Exception ex)
        {
            Log.Error($"Помилка при застосуванні критичного успіху для {ToPrettyString(userUid)}: {ex}");
        }
    }

    /// <summary>
    /// Застосовує ефект "saving throw" в залежності від результату кидка (застаріла версія)
    /// </summary>
    private void ApplySavingThrowEffect(EntityUid userUid, int roll, int sides)
    {
        // Перенаправляємо на безпечну версію
        ApplySavingThrowEffectSafe(userUid, roll, sides);
    }

    /// <summary>
    /// Застосовує значне лікування
    /// </summary>
    private void ApplyMajorHealing(EntityUid userUid, float successRate)
    {
        try
        {
            // Значне зцілення
            var healAmount = (FixedPoint2) (50.0f * successRate);

            // Створюємо специфікації лікування для різних типів пошкоджень
            var healBlunt = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Blunt"), -healAmount);
            var healSlash = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Slash"), -healAmount);
            var healPierce = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Piercing"), -healAmount);

            // Застосовуємо лікування
            _damageableSystem.TryChangeDamage(userUid, healBlunt);
            _damageableSystem.TryChangeDamage(userUid, healSlash);
            _damageableSystem.TryChangeDamage(userUid, healPierce);

            // Показуємо ефект успіху
            _popupSystem.PopupEntity($"✨ Успіх! Ви зцілені на {healAmount}! ✨", userUid, userUid);
        }
        catch (Exception ex)
        {
            Log.Error($"Помилка при застосуванні значного лікування для {ToPrettyString(userUid)}: {ex}");
        }
    }

    /// <summary>
    /// Застосовує помірне лікування
    /// </summary>
    private void ApplyModerateHealing(EntityUid userUid, float successRate)
    {
        try
        {
            // Помірне зцілення
            var healAmount = (FixedPoint2) (30.0f * successRate);

            // Створюємо специфікації лікування для різних типів пошкоджень
            var healBlunt = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Blunt"), -healAmount);
            var healSlash = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Slash"), -healAmount);

            // Застосовуємо лікування
            _damageableSystem.TryChangeDamage(userUid, healBlunt);
            _damageableSystem.TryChangeDamage(userUid, healSlash);

            // Показуємо ефект успіху
            _popupSystem.PopupEntity($"✨ Успіх! Ви зцілені на {healAmount}! ✨", userUid, userUid);
        }
        catch (Exception ex)
        {
            Log.Error($"Помилка при застосуванні помірного лікування для {ToPrettyString(userUid)}: {ex}");
        }
    }

    /// <summary>
    /// Застосовує невелике лікування
    /// </summary>
    private void ApplyMinorHealing(EntityUid userUid, float successRate)
    {
        try
        {
            // Невелике зцілення
            var healAmount = (FixedPoint2) (15.0f * successRate);

            // Створюємо специфікації лікування для різних типів пошкоджень
            var healBlunt = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Blunt"), -healAmount);

            // Застосовуємо лікування
            _damageableSystem.TryChangeDamage(userUid, healBlunt);

            // Показуємо ефект посереднього успіху
            _popupSystem.PopupEntity($"✨ Посередній успіх! Ви зцілені на {healAmount}! ✨", userUid, userUid);
        }
        catch (Exception ex)
        {
            Log.Error($"Помилка при застосуванні невеликого лікування для {ToPrettyString(userUid)}: {ex}");
        }
    }

    /// <summary>
    /// Застосовує пошкодження при критичній невдачі
    /// </summary>
    private void ApplyCriticalFailureDamage(EntityUid userUid)
    {
        try
        {
            // Невелике пошкодження
            var damageAmount = (FixedPoint2) 5.0f;

            // Створюємо специфікації пошкодження
            var damageBlunt = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Blunt"), damageAmount);

            // Застосовуємо пошкодження
            _damageableSystem.TryChangeDamage(userUid, damageBlunt);

            // Показуємо ефект критичної невдачі
            _popupSystem.PopupEntity("❌ Критична невдача! Ви отримали невелике пошкодження! ❌", userUid, userUid);
        }
        catch (Exception ex)
        {
            Log.Error($"Помилка при застосуванні критичної невдачі для {ToPrettyString(userUid)}: {ex}");
        }
    }

    /// <summary>
    /// Безпечна версія кидання кісток захисного поля та лікування при saving throw
    /// </summary>
    private void ThrowDefenseAndHealingDiceSafe(EntityUid userUid, float successRate)
    {
        try
        {
            // Перевіряємо, чи існує сутність і чи має вона необхідні компоненти
            if (Deleted(userUid) || !EntityManager.EntityExists(userUid) || !HasComp<MetaDataComponent>(userUid))
                return;

            // Шукаємо сумку з кістками
            if (!_inventory.TryGetSlotEntity(userUid, "belt", out var beltItem) ||
                !beltItem.HasValue ||
                !EntityManager.EntityExists(beltItem.Value) ||
                Deleted(beltItem.Value) ||
                !HasComp<MetaDataComponent>(beltItem.Value) ||
                !_tagSystem.HasTag(beltItem.Value, "DiceBag") ||
                !_containerSystem.TryGetContainer(beltItem.Value, "storage-default", out var container))
                return;

            // Шукаємо кістки захисту та лікування
            EntityUid? defenseDice = null;
            EntityUid? healingDice = null;

            foreach (var item in container.ContainedEntities)
            {
                // Перевіряємо, чи існує сутність і чи має вона необхідні компоненти
                if (!EntityManager.EntityExists(item) || Deleted(item) || !HasComp<MetaDataComponent>(item))
                    continue;

                if (!TryComp<DNDDiceComponent>(item, out var dndDice))
                    continue;

                if (dndDice.SpellType == "Defense" && defenseDice == null)
                    defenseDice = item;
                else if (dndDice.SpellType == "Utility" && healingDice == null)
                    healingDice = item;

                if (defenseDice != null && healingDice != null)
                    break;
            }

            // Кидаємо кістку захисту безпосередньо (без Timer.Spawn)
            if (defenseDice != null)
            {
                ThrowDefenseDiceSafe(userUid, defenseDice.Value, successRate);
            }

            // Кидаємо кістку лікування безпосередньо (без Timer.Spawn)
            if (healingDice != null)
            {
                ThrowHealingDiceSafe(userUid, healingDice.Value, successRate);
            }
        }
        catch (Exception ex)
        {
            Log.Error($"Помилка при кидання захисних кісток для {ToPrettyString(userUid)}: {ex}");
        }
    }

    /// <summary>
    /// Безпечно кидає захисну кістку
    /// </summary>
    private void ThrowDefenseDiceSafe(EntityUid userUid, EntityUid defenseDice, float successRate)
    {
        try
        {
            if (Deleted(userUid) || Deleted(defenseDice) ||
                !EntityManager.EntityExists(userUid) || !EntityManager.EntityExists(defenseDice) ||
                !HasComp<MetaDataComponent>(userUid) || !HasComp<MetaDataComponent>(defenseDice))
                return;

            // Отримуємо позицію над магом
            var userPosition = _transform.GetMapCoordinates(userUid);
            var dicePosition = userPosition.Offset(new Vector2(0.3f, 0.5f)); // Трохи вище голови і праворуч

            // Переміщуємо кістку над магом
            _transform.SetWorldPosition(defenseDice, dicePosition.Position);

            // Кидаємо кістку
            if (TryComp<DiceComponent>(defenseDice, out var diceComp))
            {
                // Генеруємо випадкове значення з урахуванням успішності saving throw
                int minRoll = (int)(diceComp.Sides * Math.Max(0.2f, successRate - 0.2f));
                int roll = _random.Next(minRoll, diceComp.Sides + 1);
                _diceSystem.SetCurrentSide(defenseDice, roll, diceComp);

                // Показуємо результат кидка
                _popupSystem.PopupEntity($"🛡️ Захисна кістка: {roll}! 🛡️", userUid);

                // Активуємо захисне заклинання
                if (TryComp<DNDDiceComponent>(defenseDice, out var component))
                {
                    component.LastRoll = roll;
                    ActivateSpellEffect(userUid, component, diceComp.Sides);
                }
            }
        }
        catch (Exception ex)
        {
            Log.Error($"Помилка при кидання захисної кістки для {ToPrettyString(userUid)}: {ex}");
        }
    }

    /// <summary>
    /// Безпечно кидає лікувальну кістку
    /// </summary>
    private void ThrowHealingDiceSafe(EntityUid userUid, EntityUid healingDice, float successRate)
    {
        try
        {
            if (Deleted(userUid) || Deleted(healingDice) ||
                !EntityManager.EntityExists(userUid) || !EntityManager.EntityExists(healingDice) ||
                !HasComp<MetaDataComponent>(userUid) || !HasComp<MetaDataComponent>(healingDice))
                return;

            // Отримуємо позицію над магом
            var userPosition = _transform.GetMapCoordinates(userUid);
            var dicePosition = userPosition.Offset(new Vector2(-0.3f, 0.5f)); // Трохи вище голови і ліворуч

            // Переміщуємо кістку над магом
            _transform.SetWorldPosition(healingDice, dicePosition.Position);

            // Кидаємо кістку
            if (TryComp<DiceComponent>(healingDice, out var diceComp))
            {
                // Генеруємо випадкове значення з урахуванням успішності saving throw
                int minRoll = (int)(diceComp.Sides * Math.Max(0.2f, successRate - 0.2f));
                int roll = _random.Next(minRoll, diceComp.Sides + 1);
                _diceSystem.SetCurrentSide(healingDice, roll, diceComp);

                // Показуємо результат кидка
                _popupSystem.PopupEntity($"💚 Лікувальна кістка: {roll}! 💚", userUid);

                // Активуємо лікувальне заклинання
                if (TryComp<DNDDiceComponent>(healingDice, out var component))
                {
                    component.LastRoll = roll;
                    ActivateSpellEffect(userUid, component, diceComp.Sides);
                }
            }
        }
        catch (Exception ex)
        {
            Log.Error($"Помилка при кидання лікувальної кістки для {ToPrettyString(userUid)}: {ex}");
        }
    }

    /// <summary>
    /// Кидає кістки захисного поля та лікування при saving throw (застаріла версія)
    /// </summary>
    private void ThrowDefenseAndHealingDice(EntityUid userUid, float successRate)
    {
        // Перенаправляємо на безпечну версію
        ThrowDefenseAndHealingDiceSafe(userUid, successRate);
    }

    /// <summary>
    /// Обробляє подію видалення кістки з контейнера, щоб запам'ятати, з якого контейнера вона була заспавнена
    /// </summary>
    private void OnRemovedFromContainer(EntityUid uid, DNDDiceComponent component, EntRemovedFromContainerMessage args)
    {
        // Перевіряємо, чи це сумка для кісток
        if (!_tagSystem.HasTag(args.Container.Owner, "DiceBag"))
            return;

        // Прив'язуємо кістку до сумки
        _diceBagSystem.BindDiceToBag(uid, args.Container.Owner, args.Container.ID);
    }

    private void OnLand(EntityUid uid, DNDDiceComponent component, ref LandEvent args)
    {
        // Перевіряємо, чи є компонент DiceComponent, який містить інформацію про кількість сторін
        if (!TryComp<DiceComponent>(uid, out var diceComp))
        {
            Log.Error($"DNDDice {ToPrettyString(uid)} не має компонента DiceComponent!");
            return;
        }

        // Зберігаємо власника кістки
        var thrower = args.User;

        // Зберігаємо параметри кістки для відновлення після повернення
        var spellPower = component.SpellPower;
        var spellType = component.SpellType;

        // Кидаємо кістку, використовуючи кількість сторін з DiceComponent
        component.LastRoll = _random.Next(1, diceComp.Sides + 1);

        // Програємо звук кидка (використовуємо звук з DiceComponent)
        _audio.PlayPvs(diceComp.Sound, uid);

        // Оновлюємо візуальний стан кістки через DiceComponent
        _diceSystem.SetCurrentSide(uid, component.LastRoll, diceComp);

        // Визначаємо ефект в залежності від результату
        var effectMessage = DetermineEffect(component, diceComp.Sides);

        // Показуємо результат кидка власнику
        if (thrower != null)
        {
            var result = $"Ви кинули d{diceComp.Sides} і отримали {component.LastRoll}!";
            _popupSystem.PopupEntity(result, uid);

            // Додаємо затримку 0.5 секунди перед показом повідомлення про успіх/невдачу
            if (!string.IsNullOrEmpty(effectMessage))
            {
                Timer.Spawn(TimeSpan.FromSeconds(0.5), () =>
                {
                    if (!Deleted(thrower.Value))
                    {
                        _popupSystem.PopupEntity(effectMessage, thrower.Value, thrower.Value);
                    }
                });
            }
        }

        // Показуємо результат всім навколо в радіусі ~10 метрів
        if (TryComp(uid, out TransformComponent? transform))
        {
            var position = _transform.GetMapCoordinates(uid);
            var message = $"d{diceComp.Sides} показує {component.LastRoll}!";

            var telepathicMessage = string.IsNullOrEmpty(effectMessage)
                ? message
                : $"{message} {effectMessage}";

            var entities = _lookup.GetEntitiesInRange(position.MapId, position.Position, TelepathicRadius);

            foreach (var entity in entities)
            {
                // Пропускаємо власника, йому вже показали повідомлення
                if (entity == thrower)
                    continue;

                // Показуємо повідомлення тільки мобам (гравцям та NPC)
                if (HasComp<MobStateComponent>(entity))
                {
                    // Додаємо затримку 0.5 секунди перед показом повідомлення про успіх/невдачу
                    Timer.Spawn(TimeSpan.FromSeconds(0.5), () =>
                    {
                        if (!Deleted(entity))
                        {
                            _popupSystem.PopupEntity(telepathicMessage, entity, entity);
                        }
                    });
                }
            }
        }

        // Активуємо ефект заклинання з затримкою 0.75 секунди
        if (thrower != null && !string.IsNullOrEmpty(effectMessage) && component.LastRoll > 1)
        {
            Timer.Spawn(TimeSpan.FromSeconds(0.75), () =>
            {
                if (!Deleted(uid) && !Deleted(thrower.Value))
                {
                    ActivateSpellEffect(thrower.Value, component, diceComp.Sides);
                }
            });
        }

        // Плануємо повернення кістки до власника через 5 секунд
        if (thrower != null)
        {
            var returnTime = _timing.CurTime + TimeSpan.FromSeconds(5);
            Timer.Spawn(TimeSpan.FromSeconds(5), () =>
            {
                // Перевіряємо, чи існує ще кістка
                if (!Deleted(uid) && TryComp(uid, out DNDDiceComponent? dndDice))
                {
                    // Відновлюємо оригінальні параметри кістки
                    dndDice.SpellPower = spellPower;
                    dndDice.SpellType = spellType;

                    // Повертаємо кістку до власника
                    ReturnDiceToOwner(uid, thrower.Value, dndDice);
                }
            });
        }
    }

    /// <summary>
    /// Повертає кістку до власника, використовуючи респавн замість телепортації для більшої надійності
    /// </summary>
    private void ReturnDiceToOwner(EntityUid diceUid, EntityUid ownerUid, DNDDiceComponent dndComponent)
    {
        // Перевіряємо, чи існують ще кістка та власник
        if (Deleted(diceUid) || Deleted(ownerUid) || !EntityManager.EntityExists(diceUid) || !EntityManager.EntityExists(ownerUid))
            return;

        // Програємо звук повернення
        _audio.PlayPvs(_returnSound, diceUid);

        // Показуємо повідомлення власнику
        _popupSystem.PopupEntity("Магічна кістка повертається до вас!", ownerUid, ownerUid);

        // Зберігаємо інформацію про кістку для респавну
        var dicePrototype = MetaData(diceUid).EntityPrototype?.ID;
        if (string.IsNullOrEmpty(dicePrototype))
        {
            // Якщо не можемо визначити прототип, просто телепортуємо кістку
            FallbackTeleportDice(diceUid, ownerUid);
            return;
        }

        // Спочатку перевіряємо, чи є у кістки компонент DNDBagDiceComponent
        if (TryComp<DNDBagDiceComponent>(diceUid, out var bagComp) && bagComp.IsBound)
        {
            // Перевіряємо, чи існує сумка
            if (!Deleted(bagComp.DiceBagUid) && EntityManager.EntityExists(bagComp.DiceBagUid) && HasComp<MetaDataComponent>(bagComp.DiceBagUid))
            {
                // Спробуємо респавнити кістку в оригінальну сумку
                if (_containerSystem.TryGetContainer(bagComp.DiceBagUid, bagComp.ContainerId, out var originContainer))
                {
                    if (RespawnDiceInContainer(diceUid, dndComponent, dicePrototype, originContainer))
                    {
                        return;
                    }
                }
            }
        }

        // Спробуємо знайти будь-яку сумку для кісток у власника
        if (_inventory.TryGetSlotEntity(ownerUid, "belt", out var beltItem) &&
            beltItem.HasValue &&
            EntityManager.EntityExists(beltItem.Value) &&
            !Deleted(beltItem.Value) &&
            HasComp<MetaDataComponent>(beltItem.Value) &&
            _tagSystem.HasTag(beltItem.Value, "DiceBag"))
        {
            // Намагаємося респавнити кістку в сумку
            if (_containerSystem.TryGetContainer(beltItem.Value, "storage-default", out var container))
            {
                if (RespawnDiceInContainer(diceUid, dndComponent, dicePrototype, container))
                {
                    return;
                }
            }
        }

        // Якщо не вдалося респавнити в сумку, використовуємо fallback телепортацію
        FallbackTeleportDice(diceUid, ownerUid);
    }

    /// <summary>
    /// Респавнить кістку в заданому контейнері
    /// </summary>
    private bool RespawnDiceInContainer(EntityUid oldDiceUid, DNDDiceComponent dndComponent, string dicePrototype, BaseContainer container)
    {
        try
        {
            // Створюємо нову кістку
            var newDiceUid = Spawn(dicePrototype, Transform(container.Owner).Coordinates);

            // Копіюємо властивості DNDDiceComponent
            if (TryComp<DNDDiceComponent>(newDiceUid, out var newDndComponent))
            {
                newDndComponent.SpellPower = dndComponent.SpellPower;
                newDndComponent.SpellType = dndComponent.SpellType;
                newDndComponent.LastRoll = dndComponent.LastRoll;
                newDndComponent.Owner = dndComponent.Owner;
                Dirty(newDiceUid, newDndComponent);
            }

            // Намагаємося вставити нову кістку в контейнер
            if (_containerSystem.Insert(newDiceUid, container))
            {
                // Прив'язуємо нову кістку до сумки
                _diceBagSystem.BindDiceToBag(newDiceUid, container.Owner);

                // Видаляємо стару кістку
                QueueDel(oldDiceUid);

                return true;
            }
            else
            {
                // Якщо не вдалося вставити, видаляємо нову кістку
                QueueDel(newDiceUid);
                return false;
            }
        }
        catch (Exception ex)
        {
            // Логуємо помилку та повертаємо false
            Logger.Error($"Помилка при респавні кістки: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Fallback метод для телепортації кістки, якщо респавн не спрацював
    /// </summary>
    private void FallbackTeleportDice(EntityUid diceUid, EntityUid ownerUid)
    {
        // Якщо не вдалося респавнити, телепортуємо кістку до ніг власника
        var ownerCoords = _transform.GetMapCoordinates(ownerUid);
        _transform.SetWorldPosition(diceUid, ownerCoords.Position);
        _transform.SetLocalRotation(diceUid, 0);

        // Показуємо повідомлення про fallback
        _popupSystem.PopupEntity("Кістка телепортується до ваших ніг (резервний метод).", ownerUid, ownerUid);
    }

    /// <summary>
    /// Визначає ефект заклинання в залежності від результату кидка та кількості сторін кістки
    /// </summary>
    /// <param name="component">Компонент DNDDice</param>
    /// <param name="sides">Кількість сторін кістки (з DiceComponent)</param>
    /// <returns>Повідомлення про ефект заклинання</returns>
    private static string DetermineEffect(DNDDiceComponent component, int sides)
    {
        // Базова логіка для визначення ефекту в залежності від типу кістки та результату
        var roll = component.LastRoll;

        // Критичний успіх
        if (roll == sides)
        {
            return $"Критичний успіх! Ваше заклинання максимально ефективне!";
        }

        // Критична невдача
        if (roll == 1)
        {
            return $"Критична невдача! Ваше заклинання не спрацювало!";
        }

        // Звичайний результат
        var successRate = (float) roll / sides;

        if (successRate > 0.75f)
        {
            return $"Дуже успішно! Ваше заклинання працює відмінно.";
        }
        else if (successRate > 0.5f)
        {
            return $"Успішно. Ваше заклинання працює нормально.";
        }
        else if (successRate > 0.25f)
        {
            return $"Посередньо. Ваше заклинання працює, але не дуже ефективно.";
        }
        else
        {
            return $"Невдача. Ваше заклинання майже не працює.";
        }
    }

    /// <summary>
    /// Активує ефект заклинання в залежності від типу кістки та результату кидка
    /// </summary>
    /// <param name="casterUid">EntityUid мага, який кинув кістку</param>
    /// <param name="component">Компонент DNDDice</param>
    /// <param name="sides">Кількість сторін кістки</param>
    private void ActivateSpellEffect(EntityUid casterUid, DNDDiceComponent component, int sides)
    {
        // Якщо критична невдача (1), заклинання не спрацьовує
        if (component.LastRoll == 1)
            return;

        // Якщо це кістка покращення спелів, вона не активує заклинання сама по собі
        if (component.SpellType == "Amplifier")
            return;

        // Розраховуємо ефективність заклинання в залежності від результату кидка
        var effectPower = CalculateSpellEffectPower(component.LastRoll, sides, component.SpellPower);

        // Перевіряємо, чи є поруч кістка покращення спелів
        var position = _transform.GetMapCoordinates(casterUid);
        var amplifierRadius = 1.0f; // Радіус пошуку кістки покращення спелів (1 метр)
        var entities = _lookup.GetEntitiesInRange(position.MapId, position.Position, amplifierRadius);

        var amplifierBonus = 0.0f;
        EntityUid? amplifierDice = null;

        foreach (var entity in entities)
        {
            // Пропускаємо саму кістку заклинання
            if (entity == casterUid)
                continue;

            // Перевіряємо, чи це кістка покращення спелів
            if (TryComp<DNDDiceComponent>(entity, out var dndDice) &&
                dndDice.SpellType == "Amplifier" &&
                TryComp<DiceComponent>(entity, out var diceComp))
            {
                // Зберігаємо кістку для подальшого видалення
                amplifierDice = entity;

                // Розраховуємо бонус від кістки покращення спелів
                // Значення від 0 до 100, ділимо на 100, щоб отримати коефіцієнт від 0 до 1
                var amplifierValue = diceComp.CurrentValue / 100.0f;

                // Якщо значення менше 0.3 (30%), то погіршуємо заклинання
                if (amplifierValue < 0.3f)
                {
                    // Погіршуємо заклинання на 10-30%
                    var penalty = 0.1f + (0.3f - amplifierValue);
                    amplifierBonus = -penalty;

                    // Показуємо повідомлення про погіршення заклинання
                    _popupSystem.PopupEntity($"Кістка покращення спелів погіршує заклинання на {penalty * 100:F0}%!", entity);
                }
                else
                {
                    // Покращуємо заклинання
                    amplifierBonus = amplifierValue;

                    // Показуємо повідомлення про покращення заклинання
                    _popupSystem.PopupEntity($"Кістка покращення спелів підсилює заклинання на {amplifierValue * 100:F0}%!", entity);
                }

                // Використовуємо тільки одну кістку покращення
                break;
            }
        }

        // Застосовуємо бонус від кістки покращення спелів
        effectPower *= 1.0f + amplifierBonus;

        // Видаляємо кістку покращення спелів після використання
        if (amplifierDice != null)
        {
            // Повертаємо кістку до власника
            if (TryComp<DNDDiceComponent>(amplifierDice.Value, out var dndDice))
            {
                ReturnDiceToOwner(amplifierDice.Value, casterUid, dndDice);
            }
        }

        // Активуємо заклинання в залежності від типу кістки
        switch (component.SpellType)
        {
            case "Utility":
                CastHealingSpell(casterUid, effectPower);
                break;
            case "Defense":
                CastDefenseSpell(casterUid, effectPower);
                break;
            case "Attack":
                CastAttackSpell(casterUid, effectPower);
                break;
            case "Teleportation":
                CastTeleportationSpell(casterUid, effectPower);
                break;
            case "Illusion":
                CastIllusionSpell(casterUid, effectPower);
                break;
            case "Elemental":
                CastElementalSpell(casterUid, effectPower);
                break;
            case "Necromancy":
                CastNecromancySpell(casterUid, effectPower);
                break;
            case "TimeStop":
                CastTimeStopSpell(casterUid, effectPower);
                break;
            default:
                // Загальні заклинання
                break;
        }
    }

    /// <summary>
    /// Розраховує силу ефекту заклинання в залежності від результату кидка
    /// </summary>
    /// <param name="roll">Результат кидка</param>
    /// <param name="sides">Кількість сторін кістки</param>
    /// <param name="baseSpellPower">Базова сила заклинання</param>
    /// <returns>Коефіцієнт сили заклинання</returns>
    private static float CalculateSpellEffectPower(int roll, int sides, float baseSpellPower)
    {
        // Критичний успіх - максимальна сила
        if (roll == sides)
            return baseSpellPower * 2.0f;

        // Звичайний результат - сила пропорційна результату кидка
        var successRate = (float) roll / sides;
        return baseSpellPower * successRate;
    }

    /// <summary>
    /// Активує заклинання лікування
    /// </summary>
    /// <param name="casterUid">EntityUid мага, який кинув кістку</param>
    /// <param name="effectPower">Сила ефекту заклинання</param>
    private void CastHealingSpell(EntityUid casterUid, float effectPower)
    {
        // Звук лікування
        var healSound = new SoundPathSpecifier("/Audio/Magic/staff_healing.ogg");
        _audio.PlayPvs(healSound, casterUid);

        // Показуємо візуальний ефект лікування
        _popupSystem.PopupEntity("✨ Лікувальна магія! ✨", casterUid);

        // Знаходимо всіх істот в радіусі лікування (3-5 метрів залежно від сили)
        var healingRadius = 3.0f + (2.0f * effectPower);
        var position = _transform.GetMapCoordinates(casterUid);
        var entities = _lookup.GetEntitiesInRange(position.MapId, position.Position, healingRadius);

        // Базова кількість лікування
        var baseHealAmount = 20.0f * effectPower;
        var entitiesHealed = 0;

        foreach (var entity in entities)
        {
            // Лікуємо тільки живих істот
            if (!HasComp<MobStateComponent>(entity) || _mobStateSystem.IsDead(entity))
                continue;

            // Маги отримують подвійне лікування
            var healMultiplier = HasComp<DNDMageComponent>(entity) ? 2.0f : 1.0f;
            var healAmount = (FixedPoint2)(baseHealAmount * healMultiplier);

            // Створюємо специфікації лікування для різних типів пошкоджень
            var healBlunt = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Blunt"), -healAmount);
            var healSlash = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Slash"), -healAmount);
            var healPierce = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Piercing"), -healAmount);
            var healHeat = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Heat"), -healAmount);
            var healCold = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Cold"), -healAmount);
            var healPoison = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Poison"), -healAmount);
            var healRadiation = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Radiation"), -healAmount);
            var healBloodloss = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Bloodloss"), -healAmount);
            var healAsphyxiation = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Asphyxiation"), -healAmount);

            // Застосовуємо лікування
            _damageableSystem.TryChangeDamage(entity, healBlunt);
            _damageableSystem.TryChangeDamage(entity, healSlash);
            _damageableSystem.TryChangeDamage(entity, healPierce);
            _damageableSystem.TryChangeDamage(entity, healHeat);
            _damageableSystem.TryChangeDamage(entity, healCold);
            _damageableSystem.TryChangeDamage(entity, healPoison);
            _damageableSystem.TryChangeDamage(entity, healRadiation);
            _damageableSystem.TryChangeDamage(entity, healBloodloss);
            _damageableSystem.TryChangeDamage(entity, healAsphyxiation);

            // Зупиняємо кровотечу
            if (HasComp<BloodstreamComponent>(entity))
            {
                _bloodstreamSystem.TryModifyBleedAmount(entity, -100);
            }

            // Додаткові ефекти для потужного лікування
            if (effectPower > 1.5f)
            {
                // Відновлюємо стамину
                if (TryComp<StaminaComponent>(entity, out var stamina))
                {
                    _staminaSystem.TryTakeStamina(entity, -50f, stamina);
                }

                // Очищаємо негативні статус ефекти
                if (TryComp<StatusEffectsComponent>(entity, out var statusEffects))
                {
                    _statusEffectsSystem.TryRemoveStatusEffect(entity, "Stun");
                    _statusEffectsSystem.TryRemoveStatusEffect(entity, "KnockedDown");
                    _statusEffectsSystem.TryRemoveStatusEffect(entity, "SlowedDown");
                }
            }

            entitiesHealed++;

            // Показуємо ефект лікування
            var healMessage = healMultiplier > 1.0f ? "✨ Магічна енергія повністю відновлює вас! ✨" : "✨ Ви відчуваєте зцілення! ✨";
            _popupSystem.PopupEntity(healMessage, entity, entity);
        }

        // Показуємо результат заклинання
        if (entitiesHealed > 0)
        {
            _popupSystem.PopupEntity($"✨ Ви зцілили {entitiesHealed} істот! ✨", casterUid, casterUid);
        }
        else
        {
            _popupSystem.PopupEntity("✨ Навколо немає поранених для лікування! ✨", casterUid, casterUid);
        }
    }

    /// <summary>
    /// Активує заклинання захисного поля
    /// </summary>
    /// <param name="casterUid">EntityUid мага, який кинув кістку</param>
    /// <param name="effectPower">Сила ефекту заклинання</param>
    private void CastDefenseSpell(EntityUid casterUid, float effectPower)
    {
        // Звук активації захисного поля
        var shieldSound = new SoundPathSpecifier("/Audio/Magic/forcewall.ogg");
        _audio.PlayPvs(shieldSound, casterUid);

        // Показуємо візуальний ефект захисного поля
        _popupSystem.PopupEntity("🛡️ Захисне поле активовано! 🛡️", casterUid);

        // Тривалість захисного поля залежить від сили ефекту (від 10 до 30 секунд)
        var duration = TimeSpan.FromSeconds(10 + (20 * effectPower));

        // Показуємо повідомлення про тривалість захисту
        var durationSeconds = (int)duration.TotalSeconds;
        _popupSystem.PopupEntity($"Захисне поле триватиме {durationSeconds} секунд.", casterUid, casterUid);

        // Створюємо візуальний ефект захисного поля
        var effectEntity = Spawn("ForceFieldEffect", _transform.GetMapCoordinates(casterUid));

        // Прив'язуємо ефект до мага
        var effectTransform = Transform(effectEntity);
        var casterTransform = Transform(casterUid);
        if (effectTransform != null && casterTransform != null)
        {
            _transform.SetParent(effectEntity, casterUid);
            _transform.SetLocalPosition(effectEntity, Vector2.Zero);
        }

        // Лікуємо мага при активації захисного поля
        var healAmount = (FixedPoint2)(10.0f * effectPower);
        var healBlunt = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Blunt"), -healAmount);
        var healSlash = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Slash"), -healAmount);
        var healPierce = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Piercing"), -healAmount);

        // Застосовуємо лікування
        _damageableSystem.TryChangeDamage(casterUid, healBlunt);
        _damageableSystem.TryChangeDamage(casterUid, healSlash);
        _damageableSystem.TryChangeDamage(casterUid, healPierce);

        // Показуємо ефект лікування
        _popupSystem.PopupEntity($"Захисне поле зцілює вас на {healAmount}!", casterUid, casterUid);

        // Видаляємо ефект після закінчення дії захисного поля
        Timer.Spawn(duration, () =>
        {
            if (!Deleted(effectEntity))
            {
                QueueDel(effectEntity);
            }

            if (!Deleted(casterUid))
            {
                _popupSystem.PopupEntity("Захисне поле зникло!", casterUid, casterUid);
            }
        });
    }

    /// <summary>
    /// Активує атакуюче заклинання
    /// </summary>
    /// <param name="casterUid">EntityUid мага, який кинув кістку</param>
    /// <param name="effectPower">Сила ефекту заклинання</param>
    private void CastAttackSpell(EntityUid casterUid, float effectPower)
    {
        // Звук атакуючого заклинання
        var attackSound = new SoundPathSpecifier("/Audio/Magic/lightning.ogg");
        _audio.PlayPvs(attackSound, casterUid);

        // Показуємо візуальний ефект атаки
        _popupSystem.PopupEntity("⚡ Магічна атака! ⚡", casterUid);

        // Знаходимо всіх ворогів в радіусі атаки (4 метри)
        var attackRadius = 4.0f;
        var position = _transform.GetMapCoordinates(casterUid);
        var entities = _lookup.GetEntitiesInRange(position.MapId, position.Position, attackRadius);

        foreach (var entity in entities)
        {
            // Пропускаємо самого мага
            if (entity == casterUid)
                continue;

            // Атакуємо тільки істот з компонентом Damageable
            if (!HasComp<DamageableComponent>(entity))
                continue;

            // Перевіряємо, чи це ворог (не маг)
            if (HasComp<DNDMageComponent>(entity))
                continue;

            // Розраховуємо пошкодження в залежності від сили ефекту
            var damageAmount = (FixedPoint2)(25.0f * effectPower);

            // Створюємо специфікації пошкоджень
            var magicDamage = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Heat"), damageAmount);
            var shockDamage = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Shock"), damageAmount * 0.5f);

            // Застосовуємо пошкодження
            _damageableSystem.TryChangeDamage(entity, magicDamage);
            _damageableSystem.TryChangeDamage(entity, shockDamage);

            // Показуємо ефект атаки
            _popupSystem.PopupEntity("⚡ Вас вразила магічна блискавка! ⚡", entity, entity);
        }
    }

    /// <summary>
    /// Активує заклинання телепортації
    /// </summary>
    /// <param name="casterUid">EntityUid мага, який кинув кістку</param>
    /// <param name="effectPower">Сила ефекту заклинання</param>
    private void CastTeleportationSpell(EntityUid casterUid, float effectPower)
    {
        // Звук телепортації
        var teleportSound = new SoundPathSpecifier("/Audio/Magic/blink.ogg");
        _audio.PlayPvs(teleportSound, casterUid);

        // Показуємо візуальний ефект телепортації
        _popupSystem.PopupEntity("🌀 Телепортація! 🌀", casterUid);

        // Розраховуємо дистанцію телепортації в залежності від сили ефекту (3-12 метрів)
        var teleportDistance = 3.0f + (9.0f * effectPower);

        // Генеруємо випадковий напрямок або телепортуємо до найближчого ворога
        Vector2 direction;
        var shouldTeleportToEnemy = effectPower > 1.2f && _random.Prob(0.3f); // 30% шанс при високій силі

        if (shouldTeleportToEnemy)
        {
            // Шукаємо найближчого ворога для телепортації
            var currentPosition = _transform.GetMapCoordinates(casterUid);
            var searchRadius = teleportDistance * 1.5f;
            var entities = _lookup.GetEntitiesInRange(currentPosition.MapId, currentPosition.Position, searchRadius);

            EntityUid? targetEnemy = null;
            var closestDistance = float.MaxValue;

            foreach (var entity in entities)
            {
                if (entity == casterUid || !HasComp<DamageableComponent>(entity) || HasComp<DNDMageComponent>(entity))
                    continue;

                var distance = Vector2.Distance(currentPosition.Position, _transform.GetWorldPosition(entity));
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    targetEnemy = entity;
                }
            }

            if (targetEnemy != null)
            {
                // Телепортуємося поруч з ворогом
                var enemyPosition = _transform.GetWorldPosition(targetEnemy.Value);
                var offsetAngle = _random.NextFloat() * MathF.PI * 2;
                var offset = new Vector2(MathF.Cos(offsetAngle), MathF.Sin(offsetAngle)) * 2.0f; // 2 метри від ворога
                var newPosition = enemyPosition + offset;

                _transform.SetWorldPosition(casterUid, newPosition);
                _popupSystem.PopupEntity("🌀 Тактична телепортація до ворога! 🌀", casterUid, casterUid);
            }
            else
            {
                // Якщо ворогів немає, звичайна телепортація
                var angle = _random.NextFloat() * MathF.PI * 2;
                direction = new Vector2(MathF.Cos(angle), MathF.Sin(angle));
                var newPosition = currentPosition.Position + direction * teleportDistance;
                _transform.SetWorldPosition(casterUid, newPosition);
            }
        }
        else
        {
            // Звичайна випадкова телепортація
            var angle = _random.NextFloat() * MathF.PI * 2;
            direction = new Vector2(MathF.Cos(angle), MathF.Sin(angle));
            var currentPosition = _transform.GetMapCoordinates(casterUid);
            var newPosition = currentPosition.Position + direction * teleportDistance;
            _transform.SetWorldPosition(casterUid, newPosition);
        }

        // Додаткові ефекти для потужної телепортації
        if (effectPower > 1.5f)
        {
            // Створюємо вибух енергії в старій позиції
            var oldPosition = _transform.GetMapCoordinates(casterUid);
            var explosionEntities = _lookup.GetEntitiesInRange(oldPosition.MapId, oldPosition.Position, 2.0f);

            foreach (var entity in explosionEntities)
            {
                if (entity == casterUid || !HasComp<DamageableComponent>(entity) || HasComp<DNDMageComponent>(entity))
                    continue;

                var explosionDamage = (FixedPoint2)(10.0f * effectPower);
                var energyDamage = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Heat"), explosionDamage);
                _damageableSystem.TryChangeDamage(entity, energyDamage);
                _popupSystem.PopupEntity("💥 Вас вдарила хвиля телепортаційної енергії! 💥", entity, entity);
            }

            // Лікуємо мага після телепортації
            var healAmount = (FixedPoint2)(15.0f * effectPower);
            var healBlunt = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Blunt"), -healAmount);
            _damageableSystem.TryChangeDamage(casterUid, healBlunt);
        }

        // Показуємо ефект після телепортації
        Timer.Spawn(TimeSpan.FromSeconds(0.1), () =>
        {
            if (!Deleted(casterUid))
            {
                _audio.PlayPvs(teleportSound, casterUid);
                _popupSystem.PopupEntity("✨ Телепортація завершена! ✨", casterUid, casterUid);
            }
        });
    }

    /// <summary>
    /// Активує заклинання ілюзії
    /// </summary>
    /// <param name="casterUid">EntityUid мага, який кинув кістку</param>
    /// <param name="effectPower">Сила ефекту заклинання</param>
    private void CastIllusionSpell(EntityUid casterUid, float effectPower)
    {
        // Звук ілюзії
        var illusionSound = new SoundPathSpecifier("/Audio/Magic/disintegrate.ogg");
        _audio.PlayPvs(illusionSound, casterUid);

        // Показуємо візуальний ефект ілюзії
        _popupSystem.PopupEntity("👻 Ілюзія! 👻", casterUid);

        // Знаходимо всіх істот в радіусі ілюзії (5-8 метрів залежно від сили)
        var illusionRadius = 5.0f + (3.0f * effectPower);
        var position = _transform.GetMapCoordinates(casterUid);
        var entities = _lookup.GetEntitiesInRange(position.MapId, position.Position, illusionRadius);

        var affectedEntities = 0;

        foreach (var entity in entities)
        {
            // Пропускаємо самого мага
            if (entity == casterUid)
                continue;

            // Впливаємо тільки на істот з компонентом MobState
            if (!HasComp<MobStateComponent>(entity))
                continue;

            // Не впливаємо на інших магів
            if (HasComp<DNDMageComponent>(entity))
                continue;

            // Тривалість ілюзії залежить від сили ефекту (8-20 секунд)
            var duration = TimeSpan.FromSeconds(8 + (12 * effectPower));

            // Показуємо ефект ілюзії
            _popupSystem.PopupEntity("😵‍💫 Ви бачите дивні ілюзії та втрачаєте орієнтацію! 😵‍💫", entity, entity);

            // Додаємо статус ефекти
            if (TryComp<StatusEffectsComponent>(entity, out var statusEffects))
            {
                // Додаємо сплутаність (уповільнення)
                _statusEffectsSystem.TryAddStatusEffect(entity, "SlowedDown", duration, true);

                // При високій силі додаємо додаткові ефекти
                if (effectPower > 1.3f)
                {
                    // Короткочасне приголомшення
                    _statusEffectsSystem.TryAddStatusEffect(entity, "Stun", TimeSpan.FromSeconds(2), true);
                }
            }

            // Створюємо фантомні копії мага навколо цілі
            if (effectPower > 1.0f)
            {
                for (int i = 0; i < 3; i++)
                {
                    var phantomAngle = (MathF.PI * 2 / 3) * i;
                    var phantomOffset = new Vector2(MathF.Cos(phantomAngle), MathF.Sin(phantomAngle)) * 2.0f;
                    var phantomPosition = _transform.GetWorldPosition(entity) + phantomOffset;

                    // Створюємо візуальний ефект фантома
                    Timer.Spawn(TimeSpan.FromSeconds(i * 0.5), () =>
                    {
                        if (!Deleted(entity))
                        {
                            _popupSystem.PopupEntity("👻", entity, entity);
                        }
                    });
                }
            }

            affectedEntities++;

            // Створюємо ефект сплутаності
            Timer.Spawn(duration, () =>
            {
                if (!Deleted(entity))
                {
                    _popupSystem.PopupEntity("✨ Ілюзії зникли, ваш розум прояснився! ✨", entity, entity);
                }
            });
        }

        // Показуємо результат заклинання
        if (affectedEntities > 0)
        {
            _popupSystem.PopupEntity($"👻 Ви заплутали {affectedEntities} істот ілюзіями! 👻", casterUid, casterUid);

            // Лікуємо мага за успішне заклинання
            var healAmount = (FixedPoint2)(5.0f * effectPower * affectedEntities);
            var healBlunt = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Blunt"), -healAmount);
            _damageableSystem.TryChangeDamage(casterUid, healBlunt);
        }
        else
        {
            _popupSystem.PopupEntity("👻 Навколо немає підходящих цілей для ілюзій! 👻", casterUid, casterUid);
        }
    }

    /// <summary>
    /// Активує елементальне заклинання
    /// </summary>
    /// <param name="casterUid">EntityUid мага, який кинув кістку</param>
    /// <param name="effectPower">Сила ефекту заклинання</param>
    private void CastElementalSpell(EntityUid casterUid, float effectPower)
    {
        // Випадково вибираємо тип елементального заклинання
        var elementType = _random.Next(1, 5); // 1-4 для різних елементів

        switch (elementType)
        {
            case 1: // Вогонь
                CastFireSpell(casterUid, effectPower);
                break;
            case 2: // Лід
                CastIceSpell(casterUid, effectPower);
                break;
            case 3: // Блискавка
                CastLightningSpell(casterUid, effectPower);
                break;
            case 4: // Земля
                CastEarthSpell(casterUid, effectPower);
                break;
        }
    }

    /// <summary>
    /// Активує некромантське заклинання
    /// </summary>
    /// <param name="casterUid">EntityUid мага, який кинув кістку</param>
    /// <param name="effectPower">Сила ефекту заклинання</param>
    private void CastNecromancySpell(EntityUid casterUid, float effectPower)
    {
        // Звук некромантії
        var necromancySound = new SoundPathSpecifier("/Audio/Magic/staff_chaos.ogg");
        _audio.PlayPvs(necromancySound, casterUid);

        // Показуємо візуальний ефект некромантії
        _popupSystem.PopupEntity("💀 Некромантія! 💀", casterUid);

        // Знаходимо всіх істот в радіусі некромантії (4 метри)
        var necromancyRadius = 4.0f;
        var position = _transform.GetMapCoordinates(casterUid);
        var entities = _lookup.GetEntitiesInRange(position.MapId, position.Position, necromancyRadius);

        var corpsesResurrected = 0;
        var livingDamaged = 0;

        foreach (var entity in entities)
        {
            // Пропускаємо самого мага
            if (entity == casterUid)
                continue;

            // Перевіряємо, чи це істота з компонентом MobState
            if (!TryComp<MobStateComponent>(entity, out var mobState))
                continue;

            // Не впливаємо на інших магів
            if (HasComp<DNDMageComponent>(entity))
                continue;

            // Перевіряємо, чи це труп
            if (_mobStateSystem.IsDead(entity, mobState))
            {
                // Спробуємо воскресити труп як слугу
                if (EntitySystem.Get<DNDNecromancyServantSystem>().TryResurrectAsServant(entity, casterUid, 300f + (effectPower * 120f)))
                {
                    corpsesResurrected++;
                    _popupSystem.PopupEntity("💀 Труп піднімається, щоб служити вам! 💀", entity, casterUid);

                    // Лікуємо мага за воскресіння
                    var healAmount = (FixedPoint2)(25.0f * effectPower);
                    var healBlunt = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Blunt"), -healAmount);
                    var healSlash = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Slash"), -healAmount);
                    _damageableSystem.TryChangeDamage(casterUid, healBlunt);
                    _damageableSystem.TryChangeDamage(casterUid, healSlash);
                }
            }
            else if (_mobStateSystem.IsAlive(entity, mobState))
            {
                // Якщо це жива істота, завдаємо пошкодження
                if (!HasComp<DamageableComponent>(entity))
                    continue;

                var drainAmount = (FixedPoint2)(15.0f * effectPower);

                // Створюємо специфікації пошкоджень (висмоктування життєвої сили)
                var necroticDamage = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Cold"), drainAmount);
                var poisonDamage = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Poison"), drainAmount * 0.5f);

                // Застосовуємо пошкодження
                _damageableSystem.TryChangeDamage(entity, necroticDamage);
                _damageableSystem.TryChangeDamage(entity, poisonDamage);

                // Показуємо ефект висмоктування
                _popupSystem.PopupEntity("💀 Ваша життєва сила висмоктується! 💀", entity, entity);

                livingDamaged++;

                // Лікуємо мага за рахунок висмоктаної енергії
                var healAmount = drainAmount * 0.3f; // 30% від завданого пошкодження
                var healBlunt = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Blunt"), -healAmount);
                var healSlash = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Slash"), -healAmount);

                // Застосовуємо лікування до мага
                _damageableSystem.TryChangeDamage(casterUid, healBlunt);
                _damageableSystem.TryChangeDamage(casterUid, healSlash);
            }
        }

        // Показуємо результат заклинання
        if (corpsesResurrected > 0 && livingDamaged > 0)
        {
            _popupSystem.PopupEntity($"💀 Ви воскресили {corpsesResurrected} трупів та висмоктали життєву силу з {livingDamaged} живих! 💀", casterUid, casterUid);
        }
        else if (corpsesResurrected > 0)
        {
            _popupSystem.PopupEntity($"💀 Ви воскресили {corpsesResurrected} трупів як своїх слуг! 💀", casterUid, casterUid);
        }
        else if (livingDamaged > 0)
        {
            _popupSystem.PopupEntity($"💀 Ви висмоктали життєву силу з {livingDamaged} істот! 💀", casterUid, casterUid);
        }
        else
        {
            _popupSystem.PopupEntity("💀 Навколо немає підходящих цілей для некромантії! 💀", casterUid, casterUid);
        }
    }

    /// <summary>
    /// Активує вогняне заклинання
    /// </summary>
    private void CastFireSpell(EntityUid casterUid, float effectPower)
    {
        var fireSound = new SoundPathSpecifier("/Audio/Magic/fireball.ogg");
        _audio.PlayPvs(fireSound, casterUid);
        _popupSystem.PopupEntity("🔥 Вогняна куля! 🔥", casterUid);

        var attackRadius = 3.5f;
        var position = _transform.GetMapCoordinates(casterUid);
        var entities = _lookup.GetEntitiesInRange(position.MapId, position.Position, attackRadius);

        foreach (var entity in entities)
        {
            if (entity == casterUid || !HasComp<DamageableComponent>(entity) || HasComp<DNDMageComponent>(entity))
                continue;

            var damageAmount = (FixedPoint2)(20.0f * effectPower);
            var fireDamage = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Heat"), damageAmount);
            _damageableSystem.TryChangeDamage(entity, fireDamage);
            _popupSystem.PopupEntity("🔥 Вас обпалило вогнем! 🔥", entity, entity);
        }
    }

    /// <summary>
    /// Активує крижане заклинання
    /// </summary>
    private void CastIceSpell(EntityUid casterUid, float effectPower)
    {
        var iceSound = new SoundPathSpecifier("/Audio/Magic/iceburst.ogg");
        _audio.PlayPvs(iceSound, casterUid);
        _popupSystem.PopupEntity("❄️ Крижана буря! ❄️", casterUid);

        var attackRadius = 3.5f;
        var position = _transform.GetMapCoordinates(casterUid);
        var entities = _lookup.GetEntitiesInRange(position.MapId, position.Position, attackRadius);

        foreach (var entity in entities)
        {
            if (entity == casterUid || !HasComp<DamageableComponent>(entity) || HasComp<DNDMageComponent>(entity))
                continue;

            var damageAmount = (FixedPoint2)(18.0f * effectPower);
            var coldDamage = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Cold"), damageAmount);
            _damageableSystem.TryChangeDamage(entity, coldDamage);
            _popupSystem.PopupEntity("❄️ Вас заморозило! ❄️", entity, entity);
        }
    }

    /// <summary>
    /// Активує блискавичне заклинання
    /// </summary>
    private void CastLightningSpell(EntityUid casterUid, float effectPower)
    {
        var lightningSound = new SoundPathSpecifier("/Audio/Magic/lightning.ogg");
        _audio.PlayPvs(lightningSound, casterUid);
        _popupSystem.PopupEntity("⚡ Блискавка! ⚡", casterUid);

        var attackRadius = 4.0f;
        var position = _transform.GetMapCoordinates(casterUid);
        var entities = _lookup.GetEntitiesInRange(position.MapId, position.Position, attackRadius);

        foreach (var entity in entities)
        {
            if (entity == casterUid || !HasComp<DamageableComponent>(entity) || HasComp<DNDMageComponent>(entity))
                continue;

            var damageAmount = (FixedPoint2)(22.0f * effectPower);
            var shockDamage = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Shock"), damageAmount);
            _damageableSystem.TryChangeDamage(entity, shockDamage);
            _popupSystem.PopupEntity("⚡ Вас вразила блискавка! ⚡", entity, entity);
        }
    }

    /// <summary>
    /// Активує земляне заклинання
    /// </summary>
    private void CastEarthSpell(EntityUid casterUid, float effectPower)
    {
        var earthSound = new SoundPathSpecifier("/Audio/Magic/rumble.ogg");
        _audio.PlayPvs(earthSound, casterUid);
        _popupSystem.PopupEntity("🌍 Земляний удар! 🌍", casterUid);

        var attackRadius = 3.0f;
        var position = _transform.GetMapCoordinates(casterUid);
        var entities = _lookup.GetEntitiesInRange(position.MapId, position.Position, attackRadius);

        foreach (var entity in entities)
        {
            if (entity == casterUid || !HasComp<DamageableComponent>(entity) || HasComp<DNDMageComponent>(entity))
                continue;

            var damageAmount = (FixedPoint2)(25.0f * effectPower);
            var bluntDamage = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Blunt"), damageAmount);
            _damageableSystem.TryChangeDamage(entity, bluntDamage);
            _popupSystem.PopupEntity("🌍 Вас вдарило каменем! 🌍", entity, entity);
        }
    }

    /// <summary>
    /// Активує заклинання зупинки часу
    /// </summary>
    /// <param name="casterUid">EntityUid мага, який кинув кістку</param>
    /// <param name="effectPower">Сила ефекту заклинання</param>
    private void CastTimeStopSpell(EntityUid casterUid, float effectPower)
    {
        // Звук зупинки часу
        var timeStopSound = new SoundPathSpecifier("/Audio/Magic/staff_animation.ogg");
        _audio.PlayPvs(timeStopSound, casterUid);

        // Показуємо візуальний ефект зупинки часу
        _popupSystem.PopupEntity("⏰ ЗУПИНКА ЧАСУ! ⏰", casterUid);

        // Знаходимо всіх істот в радіусі зупинки часу (6-12 метрів залежно від сили)
        var timeStopRadius = 6.0f + (6.0f * effectPower);
        var position = _transform.GetMapCoordinates(casterUid);
        var entities = _lookup.GetEntitiesInRange(position.MapId, position.Position, timeStopRadius);

        // Тривалість зупинки часу залежить від сили ефекту (3-8 секунд)
        var duration = TimeSpan.FromSeconds(3 + (5 * effectPower));
        var affectedEntities = 0;

        foreach (var entity in entities)
        {
            // Пропускаємо самого мага
            if (entity == casterUid)
                continue;

            // Впливаємо тільки на істот з компонентом MobState
            if (!HasComp<MobStateComponent>(entity))
                continue;

            // Не впливаємо на інших магів (вони мають імунітет до зупинки часу)
            if (HasComp<DNDMageComponent>(entity))
                continue;

            // Додаємо статус ефект зупинки часу (повна нерухомість)
            if (TryComp<StatusEffectsComponent>(entity, out var statusEffects))
            {
                _statusEffectsSystem.TryAddStatusEffect(entity, "Stun", duration, true);
                _statusEffectsSystem.TryAddStatusEffect(entity, "KnockedDown", duration, true);
            }

            // Зупиняємо рух
            if (TryComp<MovementSpeedModifierComponent>(entity, out var movement))
            {
                var originalSpeed = movement.BaseWalkSpeed;
                _movement.ChangeBaseSpeed(entity, 0f, 0f, movement.Acceleration);

                // Відновлюємо швидкість після закінчення заклинання
                Timer.Spawn(duration, () =>
                {
                    if (!Deleted(entity) && TryComp<MovementSpeedModifierComponent>(entity, out var movementComp))
                    {
                        _movement.ChangeBaseSpeed(entity, originalSpeed, originalSpeed * 1.5f, movementComp.Acceleration);
                    }
                });
            }

            affectedEntities++;

            // Показуємо ефект зупинки часу
            _popupSystem.PopupEntity("⏰ Час зупинився навколо вас! ⏰", entity, entity);

            // Створюємо візуальний ефект заморожування
            Timer.Spawn(duration, () =>
            {
                if (!Deleted(entity))
                {
                    _popupSystem.PopupEntity("⏰ Час знову почав текти! ⏰", entity, entity);
                }
            });
        }

        // Показуємо результат заклинання
        if (affectedEntities > 0)
        {
            _popupSystem.PopupEntity($"⏰ Ви зупинили час для {affectedEntities} істот на {duration.TotalSeconds:F1} секунд! ⏰", casterUid, casterUid);

            // Лікуємо мага за успішне заклинання
            var healAmount = (FixedPoint2)(10.0f * effectPower * affectedEntities);
            var healBlunt = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Blunt"), -healAmount);
            var healSlash = new DamageSpecifier(_prototypeManager.Index<DamageTypePrototype>("Slash"), -healAmount);
            _damageableSystem.TryChangeDamage(casterUid, healBlunt);
            _damageableSystem.TryChangeDamage(casterUid, healSlash);

            // Додаємо тимчасове прискорення магу
            if (TryComp<MovementSpeedModifierComponent>(casterUid, out var mageMovement))
            {
                var originalSpeed = mageMovement.BaseWalkSpeed;
                _movement.ChangeBaseSpeed(casterUid, originalSpeed * 1.5f, originalSpeed * 2.0f, mageMovement.Acceleration);

                // Відновлюємо нормальну швидкість після закінчення заклинання
                Timer.Spawn(duration, () =>
                {
                    if (!Deleted(casterUid) && TryComp<MovementSpeedModifierComponent>(casterUid, out var mageMoveComp))
                    {
                        _movement.ChangeBaseSpeed(casterUid, originalSpeed, originalSpeed * 1.5f, mageMoveComp.Acceleration);
                    }
                });
            }
        }
        else
        {
            _popupSystem.PopupEntity("⏰ Навколо немає підходящих цілей для зупинки часу! ⏰", casterUid, casterUid);
        }
    }
}


