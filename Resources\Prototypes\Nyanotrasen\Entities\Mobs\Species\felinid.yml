- type: entity
  save: false
  name: "Уріст МакФелінід"
  parent: BaseMobHuman
  id: MobFelinidBase
  abstract: true
  components:
  - type: Sprite
    scale: 0.8, 0.8
  - type: ScaleData # Goobstation
    scale: 0.8, 0.8
  - type: HumanoidAppearance
    species: Felinid
  - type: Fixtures
    fixtures: # TODO: This needs a second fixture just for mob collisions.
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.28
        density: 140
        restitution: 0.0
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Body
    prototype: Felinid
  - type: Damageable
    damageModifierSet: Felinid
  - type: SlowOnDamage
    speedModifierThresholds:
      60: 0.85  # 0.7 is base speed.
      80: 0.75  # 0.5 is base speed.
  - type: MeleeWeapon
    soundHit:
      collection: Punch
    animation: WeaponArcClaw
    damage:
      types:
        Slash: 4
        Piercing: 1
#  - type: DiseaseCarrier
#    naturalImmunities:
#    - OwOnavirus
  - type: Speech
    speechSounds: Alto
  - type: DamageOnHighSpeedImpact # Landing on all fours!
    damage:
      types:
        Blunt: 1
  - type: Stamina
    critThreshold: 85
    decay: 2.55 # 3 base decay multiplied by 0.85 = 2.55
  - type: TypingIndicator
    proto: felinid
  - type: PseudoItem
    storedOffset: 0,17
    shape:
      - 0,0,1,4
      - 0,2,3,4
      - 4,0,5,4
  - type: Vocal
    wilhelm: "/Audio/Nyanotrasen/Voice/Felinid/cat_wilhelm.ogg"
    sounds:
      Male: MaleFelinid
      Female: FemaleFelinid
      Unsexed: MaleFelinid
  - type: Felinid
  - type: NoShoesSilentFootsteps
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    - Nekomimetic
    understands:
    - TauCetiBasic
    - Nekomimetic
  - type: Thieving
    ignoreStripHidden: true
    stealth: Subtle
    stripTimeReduction: 0
    stripTimeMultiplier: 0.667
  - type: StepTriggerImmune
    whitelist:
      types:
        - Shard
        - Landmine
        - Mousetrap
        - SlipEntity
  - type: Tag
    tags:
    - CanPilot
    - FootstepSound
    - DoorBumpOpener
    - FelinidEmotes

- type: entity
  save: false
  name: "Уріст МакХендс"
  parent: MobHumanDummy
  id: MobFelinidDummy
  categories: [ HideSpawnMenu ]
  description: "Фіктивний фелінід, призначений для використання в налаштуваннях персонажів."
  components:
  - type: HumanoidAppearance
    species: Felinid
