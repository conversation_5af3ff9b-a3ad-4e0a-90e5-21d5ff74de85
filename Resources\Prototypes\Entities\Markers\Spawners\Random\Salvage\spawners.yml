- type: entityTable
  id: SalvageScrapSpawnerCommon
  table: !type:GroupSelector
    children:
    # 70% chance of scrap of some kind
    - !type:GroupSelector
      weight: 70
      children:
      - !type:NestedSelector
        tableId: SalvageScrapLowValue
        weight: 60
      - !type:NestedSelector
        tableId: SalvageScrapHighValue
        weight: 30
      - !type:NestedSelector
        tableId: SalvageScrapLarge
        weight: 10
    # 15% chance of low-value equipment
    - !type:NestedSelector
      tableId: SalvageEquipmentCommon
      weight: 15
    # 5% chance of low-value treasure or maintenance tools
    - !type:GroupSelector
      weight: 5
      children:
      - !type:NestedSelector
        tableId: SalvageTreasureCommon
      - !type:NestedSelector
        tableId: MaintToolsTable

- type: entity
  parent: MarkerBase
  id: SalvageSpawnerScrapCommon
  name: "Точка появи звичайного брухту"
  suffix: Common, 50%
  components:
  - type: Sprite
    layers:
    - state: pink
    - sprite: Objects/Materials/Scrap/generic.rsi
      state: metal-1
  - type: EntityTableSpawner
    offset: 0.4
    table: !type:NestedSelector
      tableId: SalvageScrapSpawnerCommon
      prob: 0.5

- type: entity
  parent: SalvageSpawnerScrapCommon
  id: SalvageSpawnerScrapCommon75
  suffix: Common, 75%
  components:
  - type: EntityTableSpawner
    offset: 0.4
    table: !type:NestedSelector
      tableId: SalvageScrapSpawnerCommon
      prob: 0.75

- type: entityTable
  id: SalvageScrapSpawnerValuable
  table: !type:GroupSelector
    children:
    # 75% chance of scrap of some kind
    - !type:GroupSelector
      weight: 75
      children:
      - !type:NestedSelector
        tableId: SalvageScrapLowValue
        weight: 20
      - !type:NestedSelector
        tableId: SalvageScrapHighValue
        weight: 40
      - !type:NestedSelector
        tableId: SalvageScrapLarge
        weight: 40
    # 15% chance of low-value equipment
    - !type:NestedSelector
      tableId: SalvageEquipmentCommon
      weight: 15
    # 10% chance of low-value treasure or maintenance tools
    - !type:GroupSelector
      weight: 10
      children:
      - !type:NestedSelector
        tableId: SalvageTreasureCommon
      - !type:NestedSelector
        tableId: MaintToolsTable
      - !type:NestedSelector
        tableId: SalvageTreasureUncommon
        weight: 0.25

- type: entity
  parent: MarkerBase
  id: SalvageSpawnerScrapValuable
  name: "Точка появи звичайного брухту"
  suffix: Valuable, 50%
  components:
  - type: Sprite
    layers:
    - state: pink
    - sprite: Objects/Materials/Scrap/generic.rsi
      state: junk-airlock-1
  - type: EntityTableSpawner
    offset: 0.4
    table: !type:NestedSelector
      tableId: SalvageScrapSpawnerValuable
      prob: 0.5

- type: entity
  parent: SalvageSpawnerScrapValuable
  id: SalvageSpawnerScrapValuable75
  suffix: Valuable, 75%
  components:
  - type: EntityTableSpawner
    offset: 0.4
    table: !type:NestedSelector
      tableId: SalvageScrapSpawnerValuable
      prob: 0.75

- type: entityTable
  id: SalvageTreasureSpawnerCommon
  table: !type:GroupSelector
    children:
    # 80% chance of some treasure
    - !type:GroupSelector
      weight: 80
      children:
      - !type:NestedSelector
        tableId: SalvageTreasureCommon
        weight: 60
      - !type:NestedSelector
        tableId: SalvageTreasureUncommon
        weight: 30
      - !type:NestedSelector
        tableId: SalvageTreasureRare
        weight: 9
      - !type:NestedSelector
        tableId: SalvageTreasureLegendary
        weight: 1
    # 10% chance of low-level equipment
    - !type:GroupSelector
      weight: 10
      children:
      - !type:NestedSelector
        tableId: SalvageEquipmentCommon
        weight: 60
      - !type:NestedSelector
        tableId: SalvageEquipmentUncommon
        weight: 40
    # 5% chance of moderate scrap
    - !type:GroupSelector
      weight: 5
      children:
      - !type:NestedSelector
        tableId: SalvageScrapLowValue
        weight: 60
      - !type:NestedSelector
        tableId: SalvageScrapHighValue
        weight: 30
      - !type:NestedSelector
        tableId: SalvageScrapLarge
        weight: 10
    # 5% chance of maintenance fluff
    - !type:NestedSelector
      tableId: MaintFluffTable
      weight: 5

- type: entity
  parent: MarkerBase
  id: SalvageSpawnerTreasure
  name: "Точка появи скарбів"
  suffix: Common, 75%
  components:
  - type: Sprite
    layers:
    - state: pink
    - sprite: Objects/Materials/materials.rsi
      state: diamond
  - type: EntityTableSpawner
    offset: 0.4
    table: !type:NestedSelector
      tableId: SalvageTreasureSpawnerCommon
      prob: 0.75

- type: entityTable
  id: SalvageTreasureSpawnerValuable
  table: !type:GroupSelector
    children:
    # 80% chance of some treasure
    - !type:GroupSelector
      weight: 80
      children:
      - !type:NestedSelector
        tableId: SalvageTreasureCommon
        weight: 45
      - !type:NestedSelector
        tableId: SalvageTreasureUncommon
        weight: 35
      - !type:NestedSelector
        tableId: SalvageTreasureRare
        weight: 15
      - !type:NestedSelector
        tableId: SalvageTreasureLegendary
        weight: 5
    # 10% chance of low-level equipment
    - !type:GroupSelector
      weight: 10
      children:
      - !type:NestedSelector
        tableId: SalvageEquipmentCommon
        weight: 50
      - !type:NestedSelector
        tableId: SalvageEquipmentUncommon
        weight: 40
      - !type:NestedSelector
        tableId: SalvageEquipmentUncommon
        weight: 10
    # 5% chance of moderate scrap
    - !type:GroupSelector
      weight: 5
      children:
      - !type:NestedSelector
        tableId: SalvageScrapLowValue
        weight: 30
      - !type:NestedSelector
        tableId: SalvageScrapHighValue
        weight: 45
      - !type:NestedSelector
        tableId: SalvageScrapLarge
        weight: 25
    # 5% chance of maintenance fluff
    - !type:NestedSelector
      tableId: MaintFluffTable
      weight: 5
      rolls: !type:RangeNumberSelector
        range: 1, 2

- type: entity
  parent: MarkerBase
  id: SalvageSpawnerTreasureValuable
  name: "Точка появи скарбів"
  suffix: Valuable, 75%
  components:
  - type: Sprite
    layers:
    - state: pink
    - sprite: Objects/Materials/materials.rsi
      state: diamond
  - type: EntityTableSpawner
    offset: 0.4
    table: !type:NestedSelector
      tableId: SalvageTreasureSpawnerValuable
      prob: 0.75

- type: entityTable
  id: SalvageEquipmentSpawnerCommon
  table: !type:GroupSelector
    children:
    # 90% chance of equipment item
    - !type:GroupSelector
      weight: 90
      children:
      - !type:NestedSelector
        tableId: SalvageEquipmentCommon
        weight: 50
      - !type:NestedSelector
        tableId: SalvageEquipmentUncommon
        weight: 35
      - !type:NestedSelector
        tableId: SalvageEquipmentRare
        weight: 14
      - !type:NestedSelector
        tableId: SalvageEquipmentLegendary
        weight: 1
    # 5% chance of decent-ish treasure
    - !type:GroupSelector
      weight: 5
      children:
      - !type:NestedSelector
        tableId: SalvageTreasureCommon
        weight: 75
      - !type:NestedSelector
        tableId: SalvageTreasureUncommon
        weight: 20
      - !type:NestedSelector
        tableId: SalvageTreasureRare
        weight: 5
    # 5% chance of decent maintenance loot
    - !type:GroupSelector
      weight: 5
      children:
      - !type:NestedSelector
        tableId: MaintToolsTable
      - !type:NestedSelector
        tableId: MaintFluffTable

- type: entity
  parent: MarkerBase
  id: SalvageSpawnerEquipment
  name: "Точка появи обладнання"
  suffix: Common, 75%
  components:
  - type: Sprite
    layers:
    - state: pink
    - sprite: Objects/Devices/communication.rsi
      state: walkietalkie
  - type: EntityTableSpawner
    offset: 0.4
    table: !type:NestedSelector
      tableId: SalvageEquipmentSpawnerCommon
      prob: 0.75

- type: entityTable
  id: SalvageEquipmentSpawnerValuable
  table: !type:GroupSelector
    children:
    # 90% chance of equipment item
    - !type:GroupSelector
      weight: 90
      children:
      - !type:NestedSelector
        tableId: SalvageEquipmentCommon
        weight: 15
      - !type:NestedSelector
        tableId: SalvageEquipmentUncommon
        weight: 40
      - !type:NestedSelector
        tableId: SalvageEquipmentRare
        weight: 35
      - !type:NestedSelector
        tableId: SalvageEquipmentLegendary
        weight: 10
    # 5% chance of decent-ish treasure
    - !type:GroupSelector
      weight: 5
      children:
      - !type:NestedSelector
        tableId: SalvageTreasureCommon
        weight: 60
      - !type:NestedSelector
        tableId: SalvageTreasureUncommon
        weight: 30
      - !type:NestedSelector
        tableId: SalvageTreasureRare
        weight: 10
    # 4% chance of decent maintenance loot
    - !type:GroupSelector
      weight: 4
      children:
      - !type:NestedSelector
        tableId: MaintToolsTable
      - !type:NestedSelector
        tableId: MaintFluffTable
    # 1% chance of syndie maintenance loot
    - !type:GroupSelector
      weight: 1
      children:
      - !type:NestedSelector
        tableId: SyndieMaintLoot

- type: entity
  parent: MarkerBase
  id: SalvageSpawnerEquipmentValuable
  name: "Точка появи обладнання"
  suffix: Valuable, 75%
  components:
  - type: Sprite
    layers:
    - state: pink
    - sprite: Objects/Devices/communication.rsi
      state: walkietalkie
  - type: EntityTableSpawner
    offset: 0.4
    table: !type:NestedSelector
      tableId: SalvageEquipmentSpawnerValuable
      prob: 0.75

- type: entity
  parent: MarkerBase
  id: SalvageSpawnerMobMiningAsteroid
  name: "Точка появи мобів на гірничому астероїді"
  components:
  - type: Sprite
    layers:
    - state: green
    - sprite: Mobs/Aliens/Asteroid/goliath.rsi
      state: goliath
  - type: EntityTableSpawner
    table: !type:GroupSelector
      children:
      - id: MobGoliath
        weight: 35
      #- id: MobHivelord
      #  weight: 30
      #- id: MobArgocyteLeviathing #goobstation
      #  weight: 10
      #- id: MobArgocyteFounder
      #  weight: 25
