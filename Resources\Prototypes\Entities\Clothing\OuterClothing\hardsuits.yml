#When adding new hardsuits, please try to keep the organization consistent with hardsuit-helmets.yml (if possible.)

#CREW HARDSUITS
#Basic Hardsuit
- type: entity
  parent: ClothingOuterHardsuitBase
  id: ClothingOuterHardsuitBasic
  name: "базовий скафандр"
  description: "Базовий, універсальний скафандр, який захищає власника від жахів життя в космосі. Принаймні, це краще, ніж не мати його взагалі."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/basic.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/basic.rsi
  - type: ExplosionResistance
    damageCoefficient: 0.9
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Caustic: 0.9
  - type: ClothingSpeedModifier
    walkModifier: 0.80
    sprintModifier: 0.80
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitBasic

#Atmospherics Hardsuit
- type: entity
  parent: [ClothingOuterHardsuitBase, BaseIntegratedMagboot]
  id: ClothingOuterHardsuitAtmos
  name: "скафандр атмосферного техніка"
  description: "Спеціальний костюм, що захищає від небезпечних середовищ з низьким тиском. Має термозахист."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/atmospherics.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/atmospherics.rsi
    clothingVisuals:
      outerClothing:
      - state: equipped-OUTERCLOTHING
      - state: equipped-OUTERCLOTHING-unshaded
        shader: unshaded
  - type: PressureProtection
    highPressureMultiplier: 0.02
    lowPressureMultiplier: 1000
  - type: TemperatureProtection
    coefficient: 0.001
  - type: FireProtection
    reduction: 1
  - type: ExplosionResistance
    damageCoefficient: 0.5
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Heat: 0.8
        Radiation: 0.5
  - type: ClothingSpeedModifier
    walkModifier: 0.7
    sprintModifier: 0.7
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitAtmos
  - type: GuideHelp
    guides: [ HephaestusIndustries ]

#Engineering Hardsuit
- type: entity
  parent: [ClothingOuterHardsuitBase, BaseIntegratedMagboot]
  id: ClothingOuterHardsuitEngineeringUnpainted
  name: "інженерний комбінезон"
  description: "Спеціальний костюм, який захищає від небезпечних середовищ з низьким тиском. Має радіаційний захист."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/engineering-base.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/engineering-base.rsi
    clothingVisuals:
      outerClothing:
      - state: equipped-OUTERCLOTHING
      - state: equipped-OUTERCLOTHING-unshaded
        shader: unshaded
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitEngineeringUnpainted

- type: entity
  parent: [ClothingOuterHardsuitBase, BaseIntegratedMagboot]
  id: ClothingOuterHardsuitEngineering
  name: "інженерний комбінезон"
  description: "Спеціальний костюм, який захищає від небезпечних середовищ з низьким тиском. Має радіаційний захист."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/engineering.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/engineering.rsi
    clothingVisuals:
      outerClothing:
      - state: equipped-OUTERCLOTHING
      - state: equipped-OUTERCLOTHING-unshaded
        shader: unshaded
  - type: PressureProtection
    highPressureMultiplier: 0.04
    lowPressureMultiplier: 1000
  - type: ExplosionResistance
    damageCoefficient: 0.5
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Shock: 0.8
        Caustic: 0.5
        Radiation: 0.2
  - type: ClothingSpeedModifier
    walkModifier: 0.7
    sprintModifier: 0.7
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitEngineering
  - type: StaminaDamageResistance
    coefficient: 0.75 # 25%
  - type: GuideHelp
    guides: [ HephaestusIndustries ]
  - type: TemperatureProtection
    coefficient: 0.001
  - type: FireProtection
    reduction: 1

#Spationaut Hardsuit
- type: entity
  parent: [ClothingOuterHardsuitBase, BaseIntegratedMagboot]
  id: ClothingOuterHardsuitSpatio
  name: "скафандр рятувальника"
  description: "Легкий хардс'ют, розроблений для промислового використання EVA в умовах невагомості."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/spatio.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/spatio.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.7
    lowPressureMultiplier: 1000
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Radiation: 0.3 #salv is supposed to have radiation hazards in the future
        Caustic: 0.8
  - type: ClothingSpeedModifier
    walkModifier: 0.9
    sprintModifier: 0.8
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitSpatio
  - type: GuideHelp
    guides: [ HephaestusIndustries ]

#Salvage Hardsuit
- type: entity
  parent: [ClothingOuterHardsuitBase, BaseIntegratedMagboot]
  id: ClothingOuterHardsuitSalvage
  name: "скафандр шахтаря"
  description: "Спеціальний костюм, що захищає від небезпечних середовищ з низьким тиском. Має посилене покриття на випадок зустрічі з дикими тваринами."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/salvage.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/salvage.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.5
    lowPressureMultiplier: 1000
  - type: ExplosionResistance
    damageCoefficient: 0.3
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.7
        Slash: 0.7
        Piercing: 0.5
        Radiation: 0.3
        Caustic: 0.7
  - type: ClothingSpeedModifier
    walkModifier: 0.75
    sprintModifier: 0.75
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitSalvage
  - type: GuideHelp
    guides: [ HephaestusIndustries ]

#Paramedic Voidsuit
- type: entity
  parent: [ClothingOuterHardsuitBase, BaseIntegratedMagboot, BaseIntegratedManeuveringThrusters]
  id: ClothingOuterHardsuitVoidParamed
  name: "костюм парамедика"
  description: "Костюм для парамедиків."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/paramed.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/paramed.rsi
  - type: TemperatureProtection
    coefficient: 0.1
  - type: PressureProtection
    highPressureMultiplier: 0.5
    lowPressureMultiplier: 1000
  - type: Armor
    modifiers:
      coefficients:
        Heat: 0.90
        Radiation: 0.75
        Caustic: 0.5
  - type: StealTarget
    stealGroup: ClothingOuterHardsuitVoidParamed
  - type: ClothingSpeedModifier
    walkModifier: 0.9
    sprintModifier: 0.9
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitVoidParamed
  - type: GuideHelp
    guides: [ ZengHuPharmaceuticals ]


- type: entity
  parent: [ClothingOuterHardsuitBaseMedium, BaseIntegratedMagboot, BaseIntegratedManeuveringThrusters]
  id: ClothingOuterHardsuitMaxim
  name: "скафандр утилізатора максима"
  description: "Вогонь. Тепло. Ці речі кують велику зброю, вони також кують великих рятівників."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/maxim.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/maxim.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.02
    lowPressureMultiplier: 1000
  - type: ClothingSpeedModifier
    walkModifier: 1.0
    sprintModifier: 1.0
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.6
        Slash: 0.6
        Piercing: 0.5
        Heat: 0.3
        Radiation: 0.1
  - type: ExplosionResistance
    damageCoefficient: 0.2
  - type: TemperatureProtection
    coefficient: 0.001
  - type: FireProtection
    reduction: 1
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitMaxim
  - type: GuideHelp
    guides: [ HephaestusIndustries ]

#Security Hardsuit
- type: entity
  parent: ClothingOuterHardsuitBaseMedium
  id: ClothingOuterHardsuitSecurity
  name: "скафандр служби безпеки"
  description: "Спеціальний костюм, який захищає від небезпечних середовищ з низьким тиском. Має додатковий шар броні."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/security.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/security.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.5
    lowPressureMultiplier: 1000
  - type: ExplosionResistance
    damageCoefficient: 0.4
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.6
        Slash: 0.6
        Piercing: 0.6
        Caustic: 0.7
        Heat: 0.7 # Goobstation
  - type: ClothingSpeedModifier
    walkModifier: 0.75
    sprintModifier: 0.75
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitSecurity
  - type: StaminaDamageResistance
    coefficient: 0.75 # 25%

#Brigmedic Hardsuit
- type: entity
  parent: ClothingOuterHardsuitBaseMedium
  id: ClothingOuterHardsuitBrigmedic
  name: "скафандр брігмедика" # DeltaV - rename brigmedic to corpsman
  description: "Спеціальний костюм ангела-охоронця гауптвахти. Це медична версія захисного костюма." # I will fix the rest of this entry later when I resprite sec suits
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/brigmedic.rsi
  - type: Clothing
    clothingVisuals:
      outerClothing:
      - state: equipped-OUTERCLOTHING
      - state: equipped-OUTERCLOTHING-glow
        shader: unshaded
    sprite: Clothing/OuterClothing/Hardsuits/brigmedic.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.3
    lowPressureMultiplier: 1000
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.8
        Slash: 0.8
        Piercing: 0.7
        Heat: 0.7 # Goobstation
  - type: ClothingSpeedModifier
    walkModifier: 0.65
    sprintModifier: 0.65
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitBrigmedic
  - type: StaminaDamageResistance
    coefficient: 0.75 # 25%

#Warden's Hardsuit
- type: entity
  parent: ClothingOuterHardsuitBaseHeavy
  id: ClothingOuterHardsuitWarden
  name: "скафандр наглядача"
  description: "Спеціалізований костюм для боротьби з масовими заворушеннями, пристосований до умов низького тиску."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/security-warden.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/security-warden.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.5
    lowPressureMultiplier: 1000
  - type: ExplosionResistance
    damageCoefficient: 0.4
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.5
        Slash: 0.6
        Piercing: 0.6
        Caustic: 0.7
        Heat: 0.7 # Goobstation
  - type: ClothingSpeedModifier
    walkModifier: 0.7
    sprintModifier: 0.7
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitWarden
  - type: StaminaDamageResistance
    coefficient: 0.65 # 35%

#Captain's Hardsuit
- type: entity
  parent: [ClothingOuterHardsuitBaseMedium, BaseIntegratedMagboot, BaseIntegratedManeuveringThrusters]
  id: ClothingOuterHardsuitCap
  name: "броньований скафандр капітана"
  description: "Офіційний броньований скафандр, виготовлений для капітана станції."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/capspace.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/capspace.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.02
    lowPressureMultiplier: 1000
  - type: ExplosionResistance
    damageCoefficient: 0.5
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.8
        Slash: 0.8
        Piercing: 0.6
        Heat: 0.5
        Radiation: 0.5
        Caustic: 0.6
  - type: ClothingSpeedModifier
    walkModifier: 0.8
    sprintModifier: 0.8
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitCap
  - type: StaminaDamageResistance
    coefficient: 0.5 # 50%
  - type: GuideHelp
    guides: [ NanoTrasen ]

#Chief Engineer's Hardsuit
- type: entity
  parent: [ClothingOuterHardsuitBaseMedium, BaseIntegratedMagboot, BaseIntegratedManeuveringThrusters]
  id: ClothingOuterHardsuitEngineeringWhite
  name: "скафандр головного інженера"
  description: "Спеціальний захисний костюм, що захищає від небезпечних середовищ з низьким тиском, виготовлений для головного інженера станції."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/engineering-white.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/engineering-white.rsi
    clothingVisuals:
      outerClothing:
      - state: equipped-OUTERCLOTHING
      - state: equipped-OUTERCLOTHING-unshaded
        shader: unshaded
  - type: PressureProtection
    highPressureMultiplier: 0.02
    lowPressureMultiplier: 1000
  - type: ExplosionResistance
    damageCoefficient: 0.2
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.8
        Slash: 0.8
        Piercing: 0.8
        Heat: 0.4
        Radiation: 0.0
        Caustic: 0.7
  - type: ClothingSpeedModifier
    walkModifier: 0.75
    sprintModifier: 0.8
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitEngineeringWhite
  - type: ClothingGrantComponent
    component:
    - type: SupermatterImmune
  - type: StaminaDamageResistance
    coefficient: 0.65 # 35%
  - type: GuideHelp
    guides: [ HephaestusIndustries ]
  - type: TemperatureProtection
    coefficient: 0.001
  - type: FireProtection
    reduction: 1

#Chief Medical Officer's Hardsuit
- type: entity
  parent: [ClothingOuterHardsuitBase, BaseIntegratedMagboot, BaseIntegratedManeuveringThrusters]
  id: ClothingOuterHardsuitMedical
  name: "скафандр головного лікаря"
  description: "Спеціальний костюм, що захищає від небезпечних середовищ з низьким тиском. Виготовлений з легких матеріалів для полегшення руху."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/medical.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/medical.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.3
    lowPressureMultiplier: 1000
  - type: Armor
    modifiers:
      coefficients:
        Caustic: 0.1
  - type: ClothingSpeedModifier
    walkModifier: 0.9
    sprintModifier: 0.95
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitMedical
  - type: GuideHelp
    guides: [ ZengHuPharmaceuticals ]

#Research Director's Hardsuit
- type: entity
  parent: ClothingOuterHardsuitBaseHeavy
  id: ClothingOuterHardsuitRd
  name: "експериментальний дослідницький костюм"
  description: "Спеціальний костюм, який захищає від небезпечних середовищ з низьким тиском. Має додатковий шар броні."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/rd.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/rd.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.02
    lowPressureMultiplier: 1000
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.6
        Slash: 0.8
        Piercing: 0.9
        Heat: 0.3
        Radiation: 0.1
        Caustic: 0.2
  - type: ExplosionResistance
    damageCoefficient: 0.1
  - type: ClothingSpeedModifier
    walkModifier: 0.75
    sprintModifier: 0.75
  - type: HeldSpeedModifier
  - type: Item
    size: Huge
    shape:
    - 0,0,4,4 #5X5, can fit in a duffel bag but nothing smaller.
  - type: Tag
    tags:
    - WhitelistChameleon
    - HighRiskItem
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitRd
  - type: StaticPrice
    price: 750
  - type: StealTarget
    stealGroup: ClothingOuterHardsuitRd
  - type: StaminaDamageResistance
    coefficient: 0.75 # 25% as in "shock resistance" :trollface:
  - type: GuideHelp
    guides: [ NanoTrasen ]

#Head of Security's Hardsuit
- type: entity
  parent: ClothingOuterHardsuitSecurity
  id: ClothingOuterHardsuitSecurityRed
  name: "скафандр голови безпеки"
  description: "Спеціальний костюм, який захищає від небезпечних середовищ з низьким тиском. Має додатковий шар броні."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/security-red.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/security-red.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.45
    lowPressureMultiplier: 1000
  - type: ExplosionResistance
    damageCoefficient: 0.6
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.6
        Slash: 0.5
        Piercing: 0.5
        Radiation: 0.5
        Caustic: 0.6
        Heat: 0.6 # Goobstation
  - type: ClothingSpeedModifier
    walkModifier: 0.8
    sprintModifier: 0.8
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitSecurityRed
  - type: StaminaDamageResistance
    coefficient: 0.5 # 50%

#Luxury Mining Hardsuit
- type: entity
  parent: [ClothingOuterHardsuitBaseMedium, BaseIntegratedMagboot, BaseIntegratedManeuveringThrusters]
  id: ClothingOuterHardsuitLuxury
  name: "розкішний гірничий скафандр"
  description: "Відремонтований гірничий комбінезон, виконаний у кольорах офіцера логістики. Графенова підкладка забезпечує менший захист, але в ньому набагато легше рухатися." # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/luxury.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/luxury.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.5
    lowPressureMultiplier: 1000
  - type: ExplosionResistance
    damageCoefficient: 0.5
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.8
        Radiation: 0.5
        Caustic: 0.8
  - type: ClothingSpeedModifier
    walkModifier: 0.85
    sprintModifier: 0.9
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitLuxury
  - type: GuideHelp
    guides: [ HephaestusIndustries ]

#ANTAG HARDSUITS
#Blood-red Hardsuit
- type: entity
  parent: ClothingOuterHardsuitSyndie
  id: ClothingOuterHardsuitShanlinUnpainted
  name: "Кіберкостюм \"Кіберпанцир"
  description: "Спеціальний костюм, який захищає від небезпечних середовищ з низьким тиском. Має радіаційний захист."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/syndicate-base.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/syndicate-base.rsi
    clothingVisuals:
      outerClothing:
      - state: equipped-OUTERCLOTHING
      - state: equipped-OUTERCLOTHING-unshaded
        shader: unshaded
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitShanlinUnpainted

- type: entity
  parent: [ClothingOuterHardsuitBaseMedium, BaseIntegratedMagboot, BaseIntegratedManeuveringThrusters]
  id: ClothingOuterHardsuitSyndie
  name: "криваво-червоний скафандр"
  description: "Важкоброньований скафандр, призначений для роботи в спецопераціях. Власність \"Мародерів Горлекса\"."
  components:
  - type: Sprite
    sprite: _Impstation/Clothing/OuterClothing/Hardsuits/syndicate.rsi
  - type: Item
    size: Huge
  - type: Clothing
    sprite: _Impstation/Clothing/OuterClothing/Hardsuits/syndicate.rsi
    clothingVisuals:
      outerClothing:
      - state: equipped-OUTERCLOTHING
      - state: equipped-OUTERCLOTHING-glow
        shader: unshaded
  - type: PressureProtection
    highPressureMultiplier: 0.05
    lowPressureMultiplier: 1000
  - type: ExplosionResistance
    damageCoefficient: 0.5
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.5
        Slash: 0.5
        Piercing: 0.5
        Heat: 0.5
        Radiation: 0.5
        Caustic: 0.5
  - type: ClothingSpeedModifier
    walkModifier: 0.9
    sprintModifier: 0.9
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitSyndie
  - type: Tag
    tags:
    - MonkeyWearable
    - Hardsuit
    - WhitelistChameleon
    - HidesHarpyWings
  - type: StaminaDamageResistance
    coefficient: 0.5 # 50%

# Syndicate Medic Hardsuit
- type: entity
  parent: ClothingOuterHardsuitSyndie
  id: ClothingOuterHardsuitSyndieMedic
  name: "криваво-червоний медичний скафандр"
  description: "Важкоброньований та маневрений вдосконалений скафандр, спеціально розроблений для польових медиків."
  components:
  - type: Sprite
    sprite: _Impstation/Clothing/OuterClothing/Hardsuits/syndicate_agent.rsi
  - type: Clothing
    sprite: _Impstation/Clothing/OuterClothing/Hardsuits/syndicate_agent.rsi
    clothingVisuals:
      outerClothing:
      - state: equipped-OUTERCLOTHING
      - state: equipped-OUTERCLOTHING-glow
        shader: unshaded
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitSyndieMedic
  - type: Tag
    tags:
    - Hardsuit
    - WhitelistChameleon
    - HidesHarpyWings
  - type: StaminaDamageResistance
    coefficient: 0.5 # 50%

#Syndicate Elite Hardsuit
- type: entity
  parent: ClothingOuterHardsuitSyndieElite
  id: ClothingOuterHardsuitShiweiUnpainted
  name: "Кіберкостюм \"Кіберпанцир"
  description: "Спеціальний костюм, який захищає від небезпечних середовищ з низьким тиском. Має радіаційний захист."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/syndicate-elite-base.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/syndicate-elite-base.rsi
    clothingVisuals:
      outerClothing:
      - state: equipped-OUTERCLOTHING
      - state: equipped-OUTERCLOTHING-unshaded
        shader: unshaded
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitShiweiUnpainted

- type: entity
  parent: [ClothingOuterHardsuitBase, BaseIntegratedMagboot, BaseIntegratedManeuveringThrusters]
  id: ClothingOuterHardsuitSyndieElite
  name: "скафандр еліти синдикату"
  description: "Елітна версія криваво-червоного скафандра, з покращеною мобільністю та вогнестійкістю. Власність \"Мародерів Горлекса\"."
  components:
  - type: Sprite
    sprite: _Impstation/Clothing/OuterClothing/Hardsuits/syndicate_elite.rsi
  - type: Clothing
    sprite: _Impstation/Clothing/OuterClothing/Hardsuits/syndicate_elite.rsi
    clothingVisuals:
      outerClothing:
      - state: equipped-OUTERCLOTHING
      - state: equipped-OUTERCLOTHING-glow
        shader: unshaded
  - type: PressureProtection
    highPressureMultiplier: 0.02
    lowPressureMultiplier: 1000
  - type: TemperatureProtection
    coefficient: 0.001
  - type: ExplosionResistance
    damageCoefficient: 0.2
  - type: FireProtection
    reduction: 1 # perfect protection like atmos firesuit for pyro tf2 ops
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.6
        Slash: 0.6
        Piercing: 0.6
        Heat: 0.2
        Radiation: 0.01
        Caustic: 0.5
  - type: Item
    size: Huge
  - type: ClothingSpeedModifier
    walkModifier: 1.0
    sprintModifier: 1.0
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitSyndieElite
  - type: StaminaDamageResistance
    coefficient: 0.5 # 50%

#Syndicate Commander Hardsuit
- type: entity
  parent: [ClothingOuterHardsuitBaseHeavy, BaseIntegratedMagboot, BaseIntegratedManeuveringThrusters]
  id: ClothingOuterHardsuitSyndieCommander
  name: "скафандр командира синдикату"
  description: "Посилена версія криваво-червоного скафандра, створена спеціально для командира оперативного загону синдикату. Має значно покращену броню для смертельних перестрілок на передовій."
  components:
  - type: Sprite
    sprite: _Impstation/Clothing/OuterClothing/Hardsuits/syndicate_commander.rsi
  - type: Clothing
    sprite: _Impstation/Clothing/OuterClothing/Hardsuits/syndicate_commander.rsi
    clothingVisuals:
      outerClothing:
      - state: equipped-OUTERCLOTHING
      - state: equipped-OUTERCLOTHING-glow
        shader: unshaded
  - type: PressureProtection
    highPressureMultiplier: 0.05
    lowPressureMultiplier: 1000
  - type: ExplosionResistance
    damageCoefficient: 0.5
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.4
        Slash: 0.4
        Piercing: 0.3
        Heat: 0.5
        Radiation: 0.25
        Caustic: 0.4
  - type: ClothingSpeedModifier
    walkModifier: 1.0
    sprintModifier: 1.0
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitSyndieCommander
  - type: StaminaDamageResistance
    coefficient: 0.5 # 50%
  - type: Item
    size: Huge

#Cybersun Juggernaut Hardsuit
- type: entity
  parent: [ClothingOuterHardsuitBaseHeavy, BaseIntegratedMagboot, BaseIntegratedManeuveringThrusters]
  id: ClothingOuterHardsuitJuggernaut
  name: "скафандр джаггернаута cybersun"
  description: "Костюм, виготовлений найсучаснішим відділом досліджень та розробок компанії Cybersun, щоб бути надзвичайно стійким до зовнішніх впливів."
  components:
  - type: Sprite
    sprite: _Impstation/Clothing/OuterClothing/Hardsuits/syndicate_juggernaut.rsi
  - type: Clothing
    sprite: _Impstation/Clothing/OuterClothing/Hardsuits/syndicate_juggernaut.rsi
    clothingVisuals:
      outerClothing:
      - state: equipped-OUTERCLOTHING
      - state: equipped-OUTERCLOTHING-glow
        shader: unshaded
  - type: PressureProtection
    highPressureMultiplier: 0.2
    lowPressureMultiplier: 1000
  - type: ExplosionResistance
    damageCoefficient: 0.1
  - type: Armor
    modifiers:
      flatReductions:
        Blunt: 3
        Piercing: 3
        Slash: 3
      coefficients:
        Blunt: 0.4
        Slash: 0.4
        Piercing: 0.3
        Heat: 0.4
        Radiation: 0.75
        Caustic: 0.6
  - type: ClothingSpeedModifier
    sprintModifier: 0.7
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitJuggernautReverseEngineered
  - type: StaminaDamageResistance
    coefficient: 1 # Replaced with HEAVY modify stun time and slow protection
  - type: Reflect
    spread: 270
    reflectProb: 0.45
    innate: true
    reflects:
      - Energy
      - NonEnergy
  - type: ClothingSlowOnDamageModifier
    modifier: 0.5
  - type: ClothingModifyStunTime # Goobstation
    modifier: 0.2

#Wizard Hardsuit
- type: entity
  parent: [ClothingOuterHardsuitBase, ClothingOuterWizardBaseArmor] # Goob edit
  id: ClothingOuterHardsuitWizard
  name: "скафандр чарівника"
  description: "Химерний костюм, інкрустований дорогоцінним камінням, що випромінює магічну енергію."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/wizard.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/wizard.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.05
    lowPressureMultiplier: 1000
  - type: ExplosionResistance
    damageCoefficient: 0.5
  - type: Armor # Goob edit
    modifiers:
      coefficients:
        Blunt: 0.4
        Slash: 0.4
        Piercing: 0.4
        Heat: 0.4
        Radiation: 0.1
        Caustic: 0.1
  - type: ClothingSpeedModifier
    walkModifier: 0.9 # Goob edit
    sprintModifier: 0.9 # Goob edit
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitWizard
  - type: StaminaDamageResistance
    coefficient: 0.5 # 50%
  - type: WizardClothes # Goobstation
  - type: Tag # Goobstation
    tags:
    - Hardsuit
    - WhitelistChameleon
    - SyringeArmor

#Ling Space Suit
- type: entity
  parent: ClothingOuterHardsuitBase
  id: ClothingOuterHardsuitLing
  name: "органічний скафандр"
  description: "Космічна біомаса зі стійкої до тиску та температури тканини."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/lingspacesuit.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/lingspacesuit.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.225
    lowPressureMultiplier: 1000
  - type: ExplosionResistance
    damageCoefficient: 0.2
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.95
        Slash: 0.95
        Piercing: 1
        Heat: 0.5
  - type: ClothingSpeedModifier
    walkModifier: 0.8
    sprintModifier: 0.8
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitLing

#Pirate EVA Suit (Deep Space EVA Suit)
#Despite visually appearing like a softsuit, it functions exactly like a hardsuit would (parents off of base hardsuit, has resistances and toggleable clothing, etc.) so it goes here.
- type: entity
  parent:  ClothingOuterHardsuitBase
  id:  ClothingOuterHardsuitPirateEVA
  name: "скафандр для глибокого космосу"
  suffix: Pirate
  description: "Важкий скафандр, який забезпечує базовий захист від холодних суворих реалій відкритого космосу."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/pirateeva.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/pirateeva.rsi
  - type: ExplosionResistance
    damageCoefficient: 0.7
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.8
        Slash: 0.8
        Piercing: 0.9
        Heat: 0.4
        Caustic: 0.75
  - type: ClothingSpeedModifier
    walkModifier: 0.6
    sprintModifier: 0.6
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitPirateEVA
  - type: StaticPrice
    price: 0

#Pirate Captain Hardsuit
- type: entity
  parent: ClothingOuterHardsuitBase
  id: ClothingOuterHardsuitPirateCap
  name: "скафандр капітана піратів"
  description: "Старовинний броньований скафандр, що ідеально захищає від космічної цинги та відморозків, які носять ящик інструментів."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/piratecaptain.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/piratecaptain.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.02
    lowPressureMultiplier: 1000
  - type: ExplosionResistance
    damageCoefficient: 0.6
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.7
        Slash: 0.8
        Piercing: 0.85
        Heat: 0.4
        Caustic: 0.75
  - type: ClothingSpeedModifier
    walkModifier: 0.8
    sprintModifier: 0.8
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitPirateCap
  - type: StaticPrice
    price: 0
  - type: StaminaDamageResistance
    coefficient: 0.75 # 25%

#CENTCOMM / ERT HARDSUITS
#ERT Leader Hardsuit
- type: entity
  parent: ClothingOuterHardsuitCBURN
  id: ClothingOuterHardsuitERTLeader
  name: "скафандр лідера ГШР"
  description: "Захисний скафандр, який носить лідер ГШР."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/ERTSuits/ertleader.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/ERTSuits/ertleader.rsi
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitERTLeader
  - type: StaminaDamageResistance
    coefficient: 0.5 # 50%

#ERT Chaplain Hardsuit
- type: entity
  parent: ClothingOuterHardsuitCBURN
  id: ClothingOuterHardsuitERTChaplain
  name: "Костюм капелана ERT"
  description: "Захисний комбінезон, який носять капелани групи швидкого реагування."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/ERTSuits/ertchaplain.rsi #if you change this, please update the humanoid.yml with a better markers sprite.
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/ERTSuits/ertchaplain.rsi
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitERTChaplain
  - type: StaminaDamageResistance
    coefficient: 0.5 # 50%

#ERT Engineer Hardsuit
- type: entity
  parent: ClothingOuterHardsuitCBURN
  id: ClothingOuterHardsuitERTEngineer
  name: "скафандр інженера ГШР"
  description: "Захисний скафандр, який носять інженери ГШР."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/ERTSuits/ertengineer.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/ERTSuits/ertengineer.rsi
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitERTEngineer
  - type: StaminaDamageResistance
    coefficient: 0.5 # 50%
  - type: TemperatureProtection
    coefficient: 0.001
  - type: FireProtection
    reduction: 1

#ERT Medic Hardsuit
- type: entity
  parent: ClothingOuterHardsuitCBURN
  id: ClothingOuterHardsuitERTMedical
  name: "скафандр медика ГШР"
  description: "Захисний скафандр, який носять медики ГШР."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/ERTSuits/ertmedical.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/ERTSuits/ertmedical.rsi
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitERTMedical
  - type: StaminaDamageResistance
    coefficient: 0.5 # 50%

#ERT Security Hardsuit
- type: entity
  parent: ClothingOuterHardsuitCBURN
  id: ClothingOuterHardsuitERTSecurity
  name: "скафандр охоронця ГШР"
  description: "Захисний скафандр, який носять охоронці ГШР."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/ERTSuits/ertsecurity.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/ERTSuits/ertsecurity.rsi
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitERTSecurity
  - type: Tag
    tags:
    - Hardsuit
    - WhitelistChameleon
  - type: StaminaDamageResistance
    coefficient: 0.5 # 50%

#ERT Janitor Hardsuit
- type: entity
  parent: ClothingOuterHardsuitCBURN
  id: ClothingOuterHardsuitERTJanitor
  name: "скафандр прибиральника ГШР"
  description: "Захисний скафандр, який носять прибиральники ГШР."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/ERTSuits/ertjanitor.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/ERTSuits/ertjanitor.rsi
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitERTJanitor
  - type: StaminaDamageResistance
    coefficient: 0.5 # 50%

#Deathsquad
- type: entity
  parent: [ClothingOuterHardsuitBaseHeavy, BaseIntegratedMagboot, BaseIntegratedManeuveringThrusters]
  id: ClothingOuterHardsuitDeathsquad
  name: "скафандр ескадрону смерті"
  description: "Удосконалений скафандр, який використовують спецпризначенці під час спецоперацій."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/deathsquad.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/deathsquad.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.02
    lowPressureMultiplier: 1000
  - type: TemperatureProtection
    coefficient: 0.001
  - type: ExplosionResistance
    damageCoefficient: 0.2
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.1 #best armor in the game
        Slash: 0.1
        Piercing: 0.1
        Heat: 0.1
        Radiation: 0.1
        Caustic: 0.1
  - type: ClothingSpeedModifier
    walkModifier: 1.0
    sprintModifier: 1.0
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitDeathsquad
  - type: StaminaDamageResistance
    coefficient: 0.1 # 90%

#CBURN Hardsuit
- type: entity
  parent: ClothingOuterHardsuitBaseMedium
  id: ClothingOuterHardsuitCBURN
  name: "екзокостюм CBURN"
  description: "Легкий, але міцний екзокостюм, який використовується для спеціальних операцій з очищення."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/cburn.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/cburn.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.02
    lowPressureMultiplier: 1000
  - type: TemperatureProtection
    coefficient: 0.001
  - type: ExplosionResistance
    damageCoefficient: 0.7
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.7
        Slash: 0.7
        Piercing: 0.6
        Heat: 0.1
        Cold: 0.1
        Shock: 0.1
        Radiation: 0.1
        Caustic: 0.1
  - type: ClothingSpeedModifier
    walkModifier: 1.0
    sprintModifier: 1.0
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetCBURN

#MISC. HARDSUITS
#Clown Hardsuit
- type: entity
  parent: ClothingOuterHardsuitBase
  id: ClothingOuterHardsuitClown
  name: "скафандр клоуна"
  description: "Костюм клоуна, зроблений на замовлення."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/clown.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/clown.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.5
    lowPressureMultiplier: 1000
  - type: ExplosionResistance
    damageCoefficient: 0.9
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Caustic: 0.8
  - type: ClothingSpeedModifier
    walkModifier: 0.9
    sprintModifier: 0.9
  - type: HeldSpeedModifier
  - type: Construction
    graph: ClownHardsuit
    node: clownHardsuit
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitClown

#Mime Hardsuit
- type: entity
  parent: ClothingOuterHardsuitClown
  id: ClothingOuterHardsuitMime
  name: "скафандр міма"
  description: "Костюм міма, пошитий на замовлення."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/mime.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/mime.rsi
  - type: Construction
    graph: MimeHardsuit
    node: mimeHardsuit
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitMime

#Santa's Hardsuit
- type: entity
  parent: ClothingOuterHardsuitBase
  id: ClothingOuterHardsuitSanta
  name: "скафандр Санти"
  description: "Святковий, веселий скафандр, що захищає веселого дарувальника під час подорожі на санях у космосі. Має певний захист від ударів астероїдів."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Hardsuits/santahardsuit.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Hardsuits/santahardsuit.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.5
    lowPressureMultiplier: 1000
  - type: ExplosionResistance
    damageCoefficient: 0.85
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.85
        Slash: 0.9
        Piercing: 0.85
        Caustic: 0.8
  - type: ClothingSpeedModifier
    walkModifier: 0.9
    sprintModifier: 0.9
  - type: HeldSpeedModifier
  - type: ToggleableClothing
    replaceCurrentClothing: true
    clothingPrototypes:
      head: ClothingHeadHelmetHardsuitSanta
