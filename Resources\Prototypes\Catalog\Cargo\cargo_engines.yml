- type: cargoProduct
  id: EngineAmeJar
  icon:
    sprite: Objects/Power/AME/ame_jar.rsi
    state: jar
  product: CrateEngineeringAMEJar
  cost: 2000
  category: cargoproduct-category-name-engineering
  group: market

- type: cargoProduct
  id: EngineSingularityGenerator
  icon:
    sprite: Structures/Power/Generation/Singularity/generator.rsi
    state: icon
  product: CrateEngineeringSingularityGenerator
  cost: 4000
  category: cargoproduct-category-name-engineering
  group: market

- type: cargoProduct
  id: EngineSingularityContainment
  icon:
    sprite: Structures/Power/Generation/Singularity/containment.rsi
    state: icon
  product: CrateEngineeringSingularityContainment
  cost: 1000
  category: cargoproduct-category-name-engineering
  group: market

- type: cargoProduct
  name: "ящик випромінювача"
  id: EngineSingularityEmitter
  description: "Містить випромінювач. Використовується тільки для небезпечних застосувань."
  icon:
    sprite: Structures/Power/Generation/Singularity/emitter.rsi
    state: emitter2
  product: CrateEngineeringSingularityEmitter
  cost: 3000
  category: cargoproduct-category-name-engineering
  group: market

- type: cargoProduct
  id: EngineSingularityCollector
  icon:
    sprite: Structures/Power/Generation/Singularity/collector.rsi
    state: ca_on
  product: CrateEngineeringSingularityCollector
  cost: 1000
  category: cargoproduct-category-name-engineering
  group: market

- type: cargoProduct
  id: EngineParticleAccelerator
  icon:
    sprite: Structures/Power/Generation/PA/control_box.rsi
    state: completed
  product: CrateEngineeringParticleAccelerator
  cost: 2000
  category: cargoproduct-category-name-engineering
  group: market

- type: cargoProduct
  id: EngineSolar
  icon:
    sprite: Objects/Devices/flatpack.rsi
    state: solar-assembly-part
  product: CrateEngineeringSolar
  cost: 1250
  category: cargoproduct-category-name-engineering
  group: market

- type: cargoProduct
  id: EngineTeslaGenerator
  icon:
    sprite: Structures/Power/Generation/Tesla/generator.rsi
    state: icon
  product: CrateEngineeringTeslaGenerator
  cost: 4000
  category: cargoproduct-category-name-engineering
  group: market

- type: cargoProduct
  id: EngineTeslaCoil
  icon:
    sprite: Structures/Power/Generation/Tesla/coil.rsi
    state: coil
  product: CrateEngineeringTeslaCoil
  cost: 1200
  category: cargoproduct-category-name-engineering
  group: market

- type: cargoProduct
  id: EngineTeslaGroundingRod
  icon:
    sprite: Structures/Power/Generation/Tesla/coil.rsi
    state: grounding_rod
  product: CrateEngineeringTeslaGroundingRod
  cost: 400
  category: cargoproduct-category-name-engineering
  group: market

- type: cargoProduct
  id: EngineTEGKit
  icon:
    sprite: Structures/Power/Generation/teg.rsi
    state: static
  product: CrateEngineeringTEGKit
  cost: 8000
  category: cargoproduct-category-name-engineering
  group: market