signal-port-name-pressed = Натиснуто
signal-port-description-pressed = Цей порт викликається щоразу, коли передавач активовано.

signal-port-name-on-transmitter = Увімкнено
signal-port-description-on-transmitter = Цей порт викликається щоразу, коли передавач увімкнено.

signal-port-name-off-transmitter = Вимкнено
signal-port-description-off-transmitter = Цей порт викликається щоразу, коли передавач вимикається.

signal-port-name-status-transmitter = Статус
signal-port-description-status-transmitter = Цей порт викликається зі значенням HIGH або LOW залежно від стану передавача.

signal-port-name-left = Ліво
signal-port-description-left = Цей порт викликається щоразу, коли важіль переміщується в крайнє ліве положення.

signal-port-name-right = Право
signal-port-description-right = Цей порт викликається щоразу, коли важіль переміщується в крайнє праве положення.

signal-port-name-doorstatus = Стан дверей
signal-port-description-doorstatus = Цей порт викликається зі значенням HIGH, коли двері відчиняються, і LOW, коли двері зачиняються.

signal-port-name-middle = Середина
signal-port-description-middle = Цей порт викликається щоразу, коли важіль переводиться в нейтральне положення.

signal-port-name-timer-trigger = Часовий Трігер
signal-port-description-timer-trigger = Цей порт викликається щоразу, коли спрацьовує таймер.

signal-port-name-timer-start = Старт за Таймером
signal-port-description-timer-start = Цей порт викликається щоразу, коли запускається таймер.

signal-port-name-logic-output = Вихідні дані
signal-port-description-logic-output = Цей порт викликається за допомогою HIGH або LOW, залежно від обраного вентилю та входів.

signal-port-name-logic-output-high = Високий Вхід
signal-port-description-logic-output-high = Цей порт викликається щоразу, коли на вході з'являється передній фронт сигналу.

signal-port-name-logic-output-low = Низький Вихід
signal-port-description-logic-output-low = Цей порт викликається щоразу, коли вхід має спадний фронт.

signal-port-name-air-danger = Небезпека
signal-port-description-air-danger = Цей порт викликається зі значенням HIGH, якщо він у режимі небезпеки, і LOW, якщо ні.

signal-port-name-air-warning = Попередження
signal-port-description-air-warning = Цей порт викликається зі значенням HIGH, коли він у режимі попередження, і LOW, коли ні.

signal-port-name-air-normal = Нормально
signal-port-description-air-normal = Цей порт викликається зі значенням HIGH у звичайному режимі та LOW - у нестандартному.

signal-port-name-decaying = Розпадається
signal-port-description-decaying = Цей порт викликається, коли зв'язана аномалія починає розпадатися.

signal-port-name-stabilize = Стабілізована
signal-port-description-stabilize = Цей порт викликається, коли зв'язана аномалія нормалізується.

signal-port-name-growing = Зростає
signal-port-description-growing = Цей порт викликається, коли пов'язана аномалія починає зростати.

signal-port-name-pulse = Пульс
signal-port-description-pulse = Цей порт викликається, коли пов'язана аномалія пульсує.

signal-port-name-supercrit = Надкритична
signal-port-description-supercrit = Цей порт викликається, коли зв'язана аномалія вибухає після надкритичного стану.

signal-port-name-power-charging = Зарядка
signal-port-description-power-charging = Цей порт викликається зі значенням HIGH, коли батарея набирає заряд, і LOW, коли ні.

signal-port-name-power-discharging = Розрядка
signal-port-description-power-discharging = Цей порт викликається зі значенням HIGH, коли батарея втрачає заряд, і LOW, коли ні.

signal-port-name-dockstatus = Стан дока
signal-port-description-dockstatus = Цей порт викликається зі значенням HIGH, коли він пристикований, і LOW, коли він відстикований.
signal-port-name-material-silo = Силос для матеріалів
signal-port-description-material-silo = Сховище Bluespace для матеріалів станції
signal-port-name-fillitems = Предмети Філлбота
signal-port-description-fillitems = Предмети, підібрані Філлботом.