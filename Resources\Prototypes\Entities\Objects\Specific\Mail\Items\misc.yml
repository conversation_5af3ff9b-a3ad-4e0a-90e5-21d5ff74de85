# Mail-only items. If/when these get used for anything else, please move them to another folder.
# Pranks: admin items or effects put into an envelope, released when opened or damaged.
- type: entity
  id: DelayedSmoke
  parent: BaseItem
  categories: [ HideSpawnMenu ]
  name: "затримка диму"
  suffix: "(10s)"
  components:
  - type: Sprite #DeltaV: Apparently these want sprites, probably because they're baseitems
    sprite: /Textures/Objects/Fun/goldbikehorn.rsi
    visible: false
    state: icon
  - type: DelayedItem
    item: AdminInstantEffectSmoke10

- type: entity
  id: AdminInstantEffectEMP7
  categories: [ HideSpawnMenu ]
  suffix: EMP, 7 meters
  parent: AdminInstantEffectBase
  components:
  - type: EmpOnTrigger
    range: 7
    energyConsumption: 50000

- type: entity
  id: DelayedEMP
  parent: BaseItem
  categories: [ HideSpawnMenu ]
  name: "запізнілий ЕМІ (7 метрів)"
  components:
  - type: Sprite #DeltaV: Apparently these want sprites, probably because they're baseitems
    sprite: /Textures/Objects/Fun/goldbikehorn.rsi
    visible: false
    state: icon
  - type: DelayedItem
    item: AdminInstantEffectEMP7

# Miscellaneous Items

- type: entity
  id: SyringeCognizine
  parent: Syringe
  name: "шприц когнізину"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 15
        reagents:
        - ReagentId: Cognizine
          Quantity: 15 # Surely three friends is enough.

- type: entity
  id: SyringeOpporozidone
  parent: Syringe
  name: "шприц з оппорозидоном"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 15
#        reagents: # TODO: we don't have that yet. Guess the people will receive an empty syringe instead.
#        - ReagentId: Opporozidone
#          Quantity: 15

- type: entity
  id: NecrosolChemistryBottle
  parent: BaseChemistryBottleFilled
  name: "пляшка з некрозолом"
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: Necrosol
          Quantity: 30

# Premium Alcohol: wait, it's just marketing?
# TODO: different sprites would be nice.
- type: entity
  id: DrinkPremiumVodkaBottleFull
  parent: DrinkVodkaBottleFull
  name: "Пляшка горілки Moment of Clarity"
  description: "Коли все стає трохи метушливим, все, що вам потрібно - це Момент Ясності."

- type: entity
  id: DrinkPremiumGinBottleFull
  parent: DrinkGinBottleFull
  name: "Пляшка джину Гаррі"
  description: "Цікавий набір ботанічних матеріалів, безперечно. Це гарбуз?"

- type: entity
  id: DrinkPremiumTequilaBottleFull
  parent: DrinkTequilaBottleFull
  name: "Пляшка текіли Casa del Eorg"
  description: "Залиште найкраще наостанок. Casa del Eorg, 100% агава."

- type: entity
  id: DrinkPremiumWhiskeyBottleFull
  parent: DrinkWhiskeyBottleFull
  name: "Пляшка віскі Ol' Prowler 18"
  description: "Напрочуд плавний, він має неприємну звичку підкрадатися до вас."

- type: entity
  id: DrinkPremiumRumBottleFull
  parent: DrinkRumBottleFull
  name: "Пляшка рому Redeemer's Bounty"
  description: "Ну, ти сам напросився. Сила флоту."

- type: entity
  id: DrinkPremiumAbsintheBottleFull
  parent: DrinkAbsintheBottleFull
  name: "Пляшка абсенту \"Поцілунок бюрократії"
  description: "Вишуканий смак, який має тенденцію затримуватися."
