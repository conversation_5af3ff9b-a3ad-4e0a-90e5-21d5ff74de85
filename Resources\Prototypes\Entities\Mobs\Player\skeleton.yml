- type: entity
  save: false
  parent: BaseMobSkeletonPerson
  id: MobSkeletonPerson

- type: entity
  name: "пірат-скелет"
  parent: MobSkeletonPerson
  id: MobSkeletonPirate
  components:
  - type: GhostRole
    name: ghost-role-information-skeleton-pirate-name
    description: ghost-role-information-skeleton-pirate-description
    rules: ghost-role-information-freeagent-rules
    mindRoles:
    - MindRoleGhostRoleFreeAgent
    raffle:
      settings: default
  - type: GhostTakeoverAvailable
  - type: Loadout
    prototypes: [PirateGear]
  - type: RandomHumanoidAppearance


- type: entity
  name: "скелетон-байкер"
  parent: MobSkeletonPerson
  id: MobSkeletonBiker
  components:
  - type: GhostRole
    name: ghost-role-information-skeleton-biker-name
    description: ghost-role-information-skeleton-biker-description
    rules: ghost-role-information-freeagent-rules
    mindRoles:
    - MindRoleGhostRoleFreeAgent
    raffle:
      settings: default
  - type: GhostTakeoverAvailable
  - type: Loadout
    prototypes: [SkeletonBiker]
  - type: RandomHumanoidAppearance

- type: entity
  parent: MobSkeletonPerson
  id: MobSkeletonCloset
  name: "каркас шафи"
  components:
  - type: GhostRole
    name: ghost-role-information-closet-skeleton-name
    description: ghost-role-information-closet-skeleton-description
    rules: ghost-role-information-freeagent-rules
    mindRoles:
    - MindRoleGhostRoleFreeAgent
    raffle:
      settings: default
  - type: GhostTakeoverAvailable
  - type: Loadout
    prototypes: [LimitedPassengerGear]
  - type: RandomHumanoidAppearance
#PIRATES
- type: entity
  name: "розлючений скелет"
  parent: MobSkeletonPerson
  id: MobSkeletonAngry
  components:
  - type: GhostRole
    name: ghost-role-information-skeleton-pirate-name
    description: ghost-role-information-skeleton-pirate-description
  - type: GhostTakeoverAvailable
  - type: RandomHumanoidAppearance
  - type: MobThresholds
    thresholds:
      0: Alive
      75: Critical
      100: Dead
  - type: TransferMindOnGib
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:GibBehavior { }
