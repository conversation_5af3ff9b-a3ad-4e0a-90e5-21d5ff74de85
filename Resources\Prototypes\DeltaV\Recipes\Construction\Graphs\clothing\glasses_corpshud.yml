- type: constructionGraph
  id: GlassesCorpsHUD
  start: start
  graph:
    - node: start
      edges:
        - to: glassesCorps
          steps:
            - tag: Sunglasses
              name: sun glasses
              icon:
                sprite: Clothing/Eyes/Glasses/sunglasses.rsi
                state: icon
              doAfter: 5
            - tag: HudMedicalSecurity
              name: medsec hud
              icon:
                sprite: Clothing/Eyes/Hud/medsec.rsi
                state: icon
              doAfter: 5
            - material: Cable
              amount: 5
              doAfter: 5
    - node: glassesCorps
      entity: ClothingEyesGlassesCorpsman
