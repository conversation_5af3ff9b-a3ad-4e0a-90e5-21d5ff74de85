- type: entity
  name: "коробка картриджів для Солбрейкера"
  parent: BaseAmmoProvider
  id: BoxShellSoulbreaker
  description: "Коробка з антипіонерськими патронами, призначеними для рушниць для розбиття масових заворушень."
  components:
    - type: BallisticAmmoProvider
      proto: ShellSoulbreaker
      capacity: 12
    - type: Sprite
      layers:
        - state: boxwide
        - state: shellslug
    - type: GuideHelp
      guides: [ Mindbreaking ]


- type: entity
  name: "коробка магазинів під набій .45 магнум Універсальний"
  parent: BoxMagazine
  id: BoxMagazineUniversalMagnum
  description: "Коробка, повна магазинів до набоїв .45 магнум."
  components:
  - type: StorageFill
    contents:
      - id: MagazineUniversalMagnum
        amount: 6
  - type: Storage
    maxItemSize: Small
    grid:
      - 0,0,3,2
    whitelist:
      components:
        - BallisticAmmoProvider

- type: entity
  name: "коробка магазинів .45 магнум Універсальний (тренувальні)"
  parent: BoxMagazine
  id: BoxMagazineUniversalMagnumPractice
  description: "Коробка, повна тренувальних магазинів під набій .45 магнум."
  components:
  - type: StorageFill
    contents:
      - id: MagazineUniversalMagnumPractice
        amount: 6
  - type: Storage
    maxItemSize: Small
    grid:
      - 0,0,3,2
    whitelist:
      components:
        - BallisticAmmoProvider

- type: entity
  name: "коробка магазинів .45 магнум Універсальний (гумові)"
  parent: BoxMagazine
  id: BoxMagazineUniversalMagnumRubber
  description: "Коробка, повна гумових магазинів .45 магнум."
  components:
  - type: StorageFill
    contents:
      - id: MagazineUniversalMagnumRubber
        amount: 6
  - type: Storage
    maxItemSize: Small
    grid:
      - 0,0,3,2
    whitelist:
      components:
        - BallisticAmmoProvider

- type: entity
  name: "коробка швидкісних зарядних пристроїв для гвинтівок калібру .30"
  parent: BoxMagazine
  id: BoxSpeedLoaderLightRifle
  description: "Коробка, повна швидкісних набоїв для гвинтівок калібру .30."
  components:
  - type: StorageFill
    contents:
    - id: SpeedLoaderLightRifle
      amount: 6
  - type: Storage
    maxItemSize: Small
    grid:
      - 0,0,3,2
    whitelist:
      tags:
        - SpeedLoaderRifle
