﻿using Content.Client.Administration.UI.CustomControls;
using Content.Shared.Administration.BanList;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Administration.UI.BanList.RoleBans;

[GenerateTypedNameReferences]
public sealed partial class RoleBanListControl : Control
{
    public event Action<RoleBanListLine>? LineIdsClicked;

    public RoleBanListControl()
    {
        RobustXamlLoader.Load(this);
    }

    public void SetRoleBans(List<SharedServerRoleBan> bans)
    {
        for (var i = RoleBans.ChildCount - 1; i >= 1; i--)
        {
            RoleBans.GetChild(i).Dispose();
        }

        foreach (var ban in bans)
        {
            RoleBans.AddChild(new HSeparator());

            var line = new RoleBanListLine(ban);
            line.IdsClicked += LineIdsClicked;

            RoleBans.AddChild(line);
        }
    }
}
