- type: reagent
  id: Cryptobiolin
  name: reagent-name-cryptobiolin
  group: Medicine
  desc: reagent-desc-cryptobiolin
  physicalDesc: reagent-physical-desc-fizzy
  flavor: medicine
  color: "#081a80"
  metabolisms:
    Medicine:
      metabolismRate: 0.1
      effects:
      - !type:GenericStatusEffect
        key: PsionicallyInsulated
        component: PsionicInsulation
        type: Add
        time: 900
      - !type:GenericStatusEffect
        key: Stutter
        component: ScrambledAccent
        type: Add
        time: 900
      - !type:GenericStatusEffect
        key: PsionicsDisabled
        component: PsionicsDisabled
        type: Add
        time: 900
      - !type:Drunk
        slurSpeech: false
        boozePower: 20

- type: reagent
  id: Dylovene
  name: reagent-name-dylovene
  group: Medicine
  desc: reagent-desc-dylovene
  physicalDesc: reagent-physical-desc-translucent
  flavor: medicine
  color: "#3a1d8a"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        damage:
          types:
            Poison: -1.5 # Was 1, Slight Buff as it should heal for half the amount as <PERSON><PERSON> or <PERSON>elli
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 20
        damage:
          groups:
            Brute: 2
      - !type:Jitter
        conditions:
        - !type:ReagentThreshold
          min: 20
      - !type:PopupMessage
        conditions:
        - !type:ReagentThreshold
          min: 20
        type: Local
        visualType: Medium
        messages: [ "generic-reagent-effect-nauseous" ]
        probability: 0.2
      - !type:ChemVomit
        conditions:
        - !type:ReagentThreshold
          min: 20
        probability: 0.02
      - !type:Drunk
        conditions:
        - !type:ReagentThreshold
          min: 15
  plantMetabolism:
  - !type:PlantAdjustToxins
    amount: -10
  - !type:PlantAdjustHealth
    amount: 1

- type: reagent
  id: Diphenhydramine
  name: reagent-name-diphenhydramine
  group: Medicine
  desc: reagent-desc-diphenhydramine
  physicalDesc: reagent-physical-desc-chalky
  flavor: medicine
  color: "#64ffe6"
  metabolisms:
    Medicine:
      effects:
      - !type:AdjustReagent
        reagent: Histamine
        amount: -3.0
      - !type:GenericStatusEffect
        key: Jitter
        time: 3.0
        type: Remove
      - !type:HealthChange
        damage:
          types:
            Poison: -3

- type: reagent
  id: Ethylredoxrazine
  name: reagent-name-ethylredoxrazine
  group: Medicine
  desc: reagent-desc-ethylredoxrazine
  physicalDesc: reagent-physical-desc-opaque
  flavor: medicine
  color: "#2d5708"
  metabolisms:
    Medicine:
      effects:
      - !type:GenericStatusEffect
        key: Drunk
        time: 6.0
        type: Remove
      - !type:HealthChange
        damage:
          types:
            Poison: -0.5

- type: reagent
  id: Arithrazine
  name: reagent-name-arithrazine
  group: Medicine
  desc: reagent-desc-arithrazine
  physicalDesc: reagent-physical-desc-cloudy
  flavor: medicine
  color: "#bd5902"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        damage:
          types:
            Radiation: -3
          groups:
            Brute: 0.5
      - !type:ChemAddMoodlet
        moodPrototype: StrongPaincauser

- type: reagent
  id: Bicaridine
  name: reagent-name-bicaridine
  group: Medicine
  desc: reagent-desc-bicaridine
  physicalDesc: reagent-physical-desc-opaque
  flavor: medicine
  color: "#ffaa00"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        damage:
          groups:
            Brute: -2.5 # Was 2, Buffed due to limb damage changes
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 15
        damage:
          types:
            Asphyxiation: 0.5
            Poison: 1.5
      - !type:ChemVomit
        conditions:
        - !type:ReagentThreshold
          min: 30
        probability: 0.02
      - !type:Jitter
        conditions:
        - !type:ReagentThreshold
          min: 15
      - !type:Drunk
      - !type:ChemAddMoodlet
        moodPrototype: MildPaincauser

- type: reagent
  id: Cryoxadone
  name: reagent-name-cryoxadone
  group: Medicine
  desc: reagent-desc-cryoxadone
  physicalDesc: reagent-physical-desc-fizzy
  flavor: medicine
  color: "#0091ff"
  worksOnTheDead: true
  plantMetabolism:
  - !type:PlantAdjustToxins
    amount: -5
  - !type:PlantAdjustHealth
    amount: 5
  - !type:PlantCryoxadone {}
  metabolisms:
    Medicine:
      effects:
        - !type:HealthChange
          conditions:
          - !type:Temperature
            # this is a little arbitrary but they gotta be pretty cold
            max: 213.0
          damage:
          # todo scale with temp like SS13
            groups:
              Airloss: -10
        - !type:ModifyBloodLevel
          amount: 5

- type: reagent
  id: Doxarubixadone
  name: reagent-name-doxarubixadone
  group: Medicine
  desc: reagent-desc-doxarubixadone
  physicalDesc: reagent-physical-desc-bubbling
  flavor: medicine
  color: "#32cd32"
  worksOnTheDead: true
  metabolisms:
    Medicine:
      effects:
        - !type:HealthChange
          conditions:
          - !type:Temperature
            max: 213.0
          damage:
            types:
             Cellular: -4
            groups:
                Brute: 1

- type: reagent
  id: Dermaline
  name: reagent-name-dermaline
  group: Medicine
  desc: reagent-desc-dermaline
  physicalDesc: reagent-physical-desc-translucent
  flavor: medicine
  color: "#215263"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        damage:
          types:
            Heat: -2
            Shock: -2
            Cold: -2 # Was 1.5, Buffed due to limb damage changes
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 10
        damage:
          types:
            Asphyxiation: 1
            Cold: 2
          groups:
            Brute: 0.5
      - !type:Jitter
        conditions:
        - !type:ReagentThreshold
          min: 10

- type: reagent
  id: Dexalin
  name: reagent-name-dexalin
  group: Medicine
  desc: reagent-desc-dexalin
  physicalDesc: reagent-physical-desc-opaque
  flavor: medicine
  color: "#0041a8"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        damage:
          types:
            Asphyxiation: -1
            Bloodloss: -0.5
      - !type:HealthChange
        conditions:
          - !type:ReagentThreshold
            min: 20
        damage:
          types:
            Asphyxiation: 3
            Cold: 1

- type: reagent
  id: DexalinPlus
  name: reagent-name-dexalin-plus
  group: Medicine
  desc: reagent-desc-dexalin-plus
  physicalDesc: reagent-physical-desc-cloudy
  flavor: medicine
  color: "#4da0bd"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        damage:
          types:
            Asphyxiation: -3.5
            Bloodloss: -3
      - !type:AdjustReagent
        conditions:
        - !type:ReagentThreshold
          reagent: HeartbreakerToxin
          min: 1
        reagent: HeartbreakerToxin
        amount: -3
      - !type:HealthChange
        conditions:
          - !type:ReagentThreshold
            min: 25
        damage:
          types:
            Asphyxiation: 5
            Cold: 3

# this ones a doozy
- type: reagent
  id: Epinephrine
  name: reagent-name-epinephrine
  group: Medicine
  desc: reagent-desc-epinephrine
  physicalDesc: reagent-physical-desc-odorless
  flavor: medicine
  color: "#d2fffa"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        conditions:
          # they gotta be in crit first
        - !type:MobStateCondition
          mobstate: Critical
        - !type:ReagentThreshold
          min: 0
          max: 20
        damage:
          types:
            Asphyxiation: -3
            Poison: -0.5
          groups:
            Brute: -1
            Burn: -1 # Was .5, Buffed due to limb damage changes
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 20
        damage:
          types:
            Asphyxiation: 1
            Poison: 1
      - !type:AdjustReagent
        reagent: HeartbreakerToxin
        amount: -2
      - !type:AdjustReagent
        conditions:
        - !type:ReagentThreshold
          reagent: HeartbreakerToxin
          min: 1
        reagent: Epinephrine
        amount: -1
      - !type:AdjustReagent
        probability: 0.1
        conditions:
        - !type:ReagentThreshold
          reagent: HeartbreakerToxin
          min: 1
        reagent: Histamine
        amount: 4
      - !type:GenericStatusEffect
        key: Stun
        time: 0.75
        type: Remove
      - !type:GenericStatusEffect
        key: KnockedDown
        time: 0.75
        type: Remove
      - !type:GenericStatusEffect
        key: Adrenaline # Guess what epinephrine is...
        component: IgnoreSlowOnDamage
        time: 5 # lingers less than hivemind reagent to give it a niche
      - !type:MovespeedModifier # Goob edit
        walkSpeedModifier: 1.1
        sprintSpeedModifier: 1.1
      - !type:ChemAddMoodlet
        moodPrototype: EpinephrineEffect

- type: reagent
  id: Hyronalin
  name: reagent-name-hyronalin
  group: Medicine
  desc: reagent-desc-hyronalin
  physicalDesc: reagent-physical-desc-cloudy
  flavor: medicine
  color: "#4cb580"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        damage:
          types:
            Radiation: -1
      - !type:ChemVomit
        probability: 0.02
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 30
        damage:
          types:
           Heat: 2
      - !type:Jitter
        conditions:
        - !type:ReagentThreshold
          min: 30

- type: reagent
  id: Ipecac
  name: reagent-name-ipecac
  group: Medicine
  desc: reagent-desc-ipecac
  physicalDesc: reagent-physical-desc-inky
  flavor: medicine
  color: "#422912"
  metabolisms:
    Medicine:
      effects:
      - !type:ChemVomit
        conditions:
        - !type:ReagentThreshold
          min: 4
        probability: 0.3

- type: reagent
  id: Inaprovaline
  name: reagent-name-inaprovaline
  group: Medicine
  desc: reagent-desc-inaprovaline
  physicalDesc: reagent-physical-desc-opaque
  flavor: medicine
  color: "#731024"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        conditions:
          # they gotta be in crit first
        - !type:MobStateCondition
          mobstate: Critical
        damage:
          types:
            Asphyxiation: -5
      - !type:ModifyBleedAmount
        amount: -0.25
      - !type:ChemAddMoodlet
        moodPrototype: MildPainkiller

- type: reagent
  id: Kelotane
  name: reagent-name-kelotane
  group: Medicine
  desc: reagent-desc-kelotane
  physicalDesc: reagent-physical-desc-strong-smelling
  flavor: medicine
  color: "#bf3d19"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        damage:
          types:
            Heat: -0.5
            Shock: -0.5
            Cold: -0.5 # Was .33, Buffed due to limb damage changes
      - !type:SatiateThirst
        factor: -10
        conditions:
        - !type:ReagentThreshold
          min: 30
      - !type:PopupMessage
        type: Local
        messages:
        - generic-reagent-effect-nauseous
        - generic-reagent-effect-thirsty
        - generic-reagent-effect-parched
        probability: 0.1
        conditions:
        - !type:ReagentThreshold
          min: 25
      - !type:ChemAddMoodlet
        moodPrototype: MildPainkiller

- type: reagent
  id: Leporazine
  name: reagent-name-leporazine
  group: Medicine
  desc: reagent-desc-leporazine
  physicalDesc: reagent-physical-desc-pungent
  flavor: medicine
  color: "#ff7db5"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        damage:
          types:
            Cold: -4
      - !type:AdjustTemperature
        conditions:
        - !type:Temperature
          max: 288.15
        amount: 100000 # thermal energy, not temperature!
      - !type:AdjustTemperature
        conditions:
        - !type:Temperature
          min: 298.15
        amount: -10000
      - !type:PopupMessage
        type: Local
        visualType: Medium
        messages: [ "leporazine-effect-temperature-adjusting" ]
        probability: 0.2
      - !type:AdjustReagent
        reagent: Fresium
        amount: -5

- type: reagent
  id: Barozine
  name: reagent-name-barozine
  group: Medicine
  desc: reagent-desc-barozine
  physicalDesc: reagent-physical-desc-viscous
  flavor: medicine
  color: "#ff867d"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        probability: 0.3
        damage:
          types:
            Heat: 0.5
      - !type:HealthChange
        conditions:
          - !type:ReagentThreshold
            min: 30
        damage:
          types:
            Poison: 3
      - !type:ChemVomit
        probability: 0.15
        conditions:
          - !type:ReagentThreshold
            min: 30
      - !type:GenericStatusEffect
        key: PressureImmunity
        component: PressureImmunity
      - !type:GenericStatusEffect
        key: Stutter
        component: StutteringAccent
      - !type:Jitter
      - !type:Emote
        emote: Scream
        probability: 0.2
      - !type:PopupMessage
        type: Local
        visualType: Large
        messages: [ "barozine-effect-skin-burning", "barozine-effect-muscle-contract" ]
        probability: 0.1

- type: reagent
  id: Phalanximine
  name: reagent-name-phalanximine
  group: Medicine
  desc: reagent-desc-phalanximine
  physicalDesc: reagent-physical-desc-acrid
  flavor: medicine
  color: "#c8ff75"
  plantMetabolism:
    - !type:PlantAdjustToxins
      amount: 6
    - !type:PlantPhalanximine
      conditions:
      - !type:ReagentThreshold
        min: 4
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        damage:
          types:
            Cellular: -1
            Radiation: 1
      - !type:ChemVomit
        probability: 0.05

- type: reagent
  id: PolypyryliumOligomers
  name: reagent-name-polypyrylium-oligomers
  group: Medicine
  desc: reagent-desc-polypyrylium-oligomers
  physicalDesc: reagent-physical-desc-pungent
  flavor: violets
  color: "#9423FF"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        damage:
          groups:
            Brute: -3.5
          types:
            Asphyxiation: -2.5
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 30
        damage:
          types:
            Asphyxiation: 3.5
      - !type:ModifyBleedAmount
        amount: -0.25

- type: reagent
  id: Ambuzol
  name: reagent-name-ambuzol
  group: Medicine
  desc: reagent-desc-ambuzol
  physicalDesc: reagent-physical-desc-crisp
  flavor: medicine
  color: "#86caf7"
  metabolisms:
    Medicine:
      effects:
        - !type:CureZombieInfection
          conditions:
            - !type:ReagentThreshold
              min: 10
        - !type:ChemRemoveMoodlet
          moodPrototype: RomerolEffect
          conditions:
            - !type:ReagentThreshold
              min: 10

- type: reagent
  id: AmbuzolPlus
  name: reagent-name-ambuzol-plus
  group: Medicine
  desc: reagent-desc-ambuzol-plus
  physicalDesc: reagent-physical-desc-crisp
  flavor: medicine
  color: "#1274b5"
  metabolisms:
    Medicine:
      effects:
        - !type:CureZombieInfection
          innoculate: true
          conditions:
            - !type:ReagentThreshold
              min: 5
        - !type:ChemRemoveMoodlet
          moodPrototype: RomerolEffect
          conditions:
            - !type:ReagentThreshold
              min: 5

- type: reagent
  id: PulpedBananaPeel
  name: reagent-name-pulped-banana-peel
  group: Medicine
  desc: reagent-desc-pulped-banana-peel
  physicalDesc: reagent-physical-desc-pulpy
  flavor: medicine
  color: "#FFE774"
  metabolisms:
    Medicine:
      effects:
      - !type:ModifyBleedAmount
        amount: -0.5

- type: reagent
  id: Saline
  name: reagent-name-saline
  group: Medicine
  desc: reagent-desc-saline
  physicalDesc: reagent-physical-desc-salty
  flavor: salty
  color: "#0064C8"
  metabolisms:
    Drink:
      effects:
        - !type:SatiateThirst
          factor: 6
        - !type:ModifyBloodLevel
          amount: 6

- type: reagent
  id: Siderlac
  name: reagent-name-siderlac
  group: Medicine
  desc: reagent-desc-siderlac
  physicalDesc: reagent-physical-desc-milky
  flavor: medicine
  color: "#f4dab8"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        damage:
          types:
            Caustic: -5

- type: reagent
  id: Stellibinin
  name: reagent-name-stellibinin
  group: Medicine
  desc: reagent-desc-stellibinin
  physicalDesc: reagent-physical-desc-starry
  flavor: medicine
  color: "#2b2f77"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        damage:
          types:
            Poison: -4
      - !type:AdjustReagent
        conditions:
        - !type:ReagentThreshold
          reagent: Amatoxin
          min: 1
        reagent: Amatoxin
        amount: -3

- type: reagent
  id: Synaptizine
  name: reagent-name-synaptizine
  group: Medicine
  desc: reagent-desc-synaptizine
  physicalDesc: reagent-physical-desc-pungent
  flavor: medicine
  color: "#d49a2f"
  metabolisms:
    Poison:
      effects:
      - !type:HealthChange
        damage:
          types:
            Poison: 2
      - !type:GenericStatusEffect
        key: Stun
        time: 3.0
        type: Remove
      - !type:GenericStatusEffect
        key: KnockedDown
        time: 3.0
        type: Remove
      - !type:GenericStatusEffect
        key: SeeingRainbows
        time: 15.0
        type: Remove
      - !type:ChemPurgeMoodlets
        conditions:
        - !type:ReagentThreshold
          reagent: Synaptizine
          min: 10

- type: reagent
  id: TranexamicAcid
  name: reagent-name-tranexamic-acid
  group: Medicine
  desc: reagent-desc-tranexamic-acid
  physicalDesc: reagent-physical-desc-viscous
  flavor: medicine
  color: "#ba7d7d"
  metabolisms:
    Medicine:
      effects:
      # Medium-large quantities can hurt you instead,
      # but still technically stop your bleeding.
      - !type:ModifyBleedAmount
        amount: -1.5
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 15
        damage:
          types:
            Bloodloss: 3

- type: reagent
  id: Tricordrazine
  name: reagent-name-tricordrazine
  group: Medicine
  desc: reagent-desc-tricordrazine
  physicalDesc: reagent-physical-desc-opaque
  flavor: medicine
  color: "#00e5ff"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        conditions:
        - !type:TotalDamage
          max: 50
        damage:
          groups:
            Brute: -1
          types:
            Poison: -0.5 ##Should be about what it was when it healed the toxin group
            Radiation: -0.15 ##Should really only heal minor accidental exposures
            Heat: -0.5
            Shock: -0.5
            Cold: -0.5 # Was .33, Buffed due to limb damage changes
      - !type:ChemAddMoodlet
        moodPrototype: MildPainkiller

- type: reagent
  id: Lipozine
  name: reagent-name-lipozine
  group: Medicine
  desc: reagent-desc-lipozine
  physicalDesc: reagent-physical-desc-oily
  flavor: medicine
  color: "#2690b5"
  metabolisms:
    Medicine:
      effects:
      # what the hell, this isn't satiating at all!!
      - !type:SatiateHunger
        factor: -1

  # Should heal quite literally everything, use in very small amounts
- type: reagent
  id: Omnizine
  name: reagent-name-omnizine
  group: Medicine
  desc: reagent-desc-omnizine
  physicalDesc: reagent-physical-desc-soothing
  flavor: medicine
  color: "#fcf7f9"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        damage:
          groups:
            Burn: -3
            Toxin: -3
            Airloss: -3
            Brute: -3 # Was 2, Buffed due to limb damage changes

- type: reagent
  id: Ultravasculine
  name: reagent-name-ultravasculine
  group: Medicine
  desc: reagent-desc-ultravasculine
  physicalDesc: reagent-physical-desc-thick-and-grainy
  flavor: medicine
  color: "#520e30"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 0
          max: 30
        damage:
          groups:
            Toxin: -3
            Brute: 0.5
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 30
        damage:
          groups:
            Toxin: -1
            Brute: 3
      - !type:AdjustReagent
        conditions:
        - !type:ReagentThreshold
          reagent: Histamine
          min: 1
        reagent: Histamine
        amount: -1
      - !type:AdjustReagent
        conditions:
        - !type:ReagentThreshold
          reagent: Histamine
          min: 1
        reagent: Ultravasculine
        amount: 0.5

- type: reagent
  id: Oculine
  name: reagent-name-oculine
  desc: reagent-desc-oculine
  group: Medicine
  physicalDesc: reagent-physical-desc-translucent
  flavor: medicine
  color: "#404040"
  metabolisms:
    Medicine:
      effects:
      - !type:ChemHealEyeDamage

- type: reagent
  id: Cognizine
  name: reagent-name-cognizine
  desc: reagent-desc-cognizine
  group: Medicine
  physicalDesc: reagent-physical-desc-enigmatic
  flavor: magical
  color: "#b50ee8"
  metabolisms:
    Medicine:
      effects:
      - !type:MakeSentient
        conditions:
        - !type:ReagentThreshold
          min: 5

- type: reagent
  id: Ethyloxyephedrine
  name: reagent-name-ethyloxyephedrine
  desc: reagent-desc-ethyloxyephedrine
  group: Medicine
  physicalDesc: reagent-physical-desc-shiny
  flavor: medicine
  color: "#d5d5e4"
  metabolisms:
    Medicine:
      effects:
      - !type:Jitter
      - !type:GenericStatusEffect
        key: Stutter
        component: StutteringAccent
      - !type:GenericStatusEffect
        key: Drowsiness
        time: 10
        type: Remove
      - !type:ResetNarcolepsy
        conditions:
        - !type:ReagentThreshold
          min: 10
      - !type:PopupMessage
        visualType: Medium
        messages: ["ethyloxyephedrine-effect-feeling-awake", "ethyloxyephedrine-effect-clear-mind"]
        type: Local
        probability: 0.1

- type: reagent
  id: Diphenylmethylamine
  name: reagent-name-diphenylmethylamine
  desc: reagent-desc-diphenylmethylamine
  group: Medicine
  physicalDesc: reagent-physical-desc-glowing
  flavor: medicine
  color: "#b0abaa"
  metabolisms:
    Medicine:
      effects:
      - !type:GenericStatusEffect
        key: Drowsiness
        time: 10
        type: Remove
      - !type:ResetNarcolepsy
        conditions:
        - !type:ReagentThreshold
          min: 5
      - !type:PopupMessage
        visualType: Medium
        messages: ["ethyloxyephedrine-effect-feeling-awake", "ethyloxyephedrine-effect-clear-mind"]
        type: Local
        probability: 0.1

- type: reagent
  id: Sigynate
  name: reagent-name-sigynate
  group: Medicine
  desc: reagent-desc-sigynate
  physicalDesc: reagent-physical-desc-thick
  flavor: sweet
  color: "#e0a5b9"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        damage:
          types:
            Caustic: -3
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 16
        damage:
          types:
            Heat: 2
      - !type:Jitter
        conditions:
        - !type:ReagentThreshold
          min: 20
      - !type:PopupMessage
        conditions:
        - !type:ReagentThreshold
          min: 20
        type: Local
        visualType: Medium
        messages: [ "generic-reagent-effect-nauseous" ]
        probability: 0.2
      - !type:ChemVomit
        conditions:
        - !type:ReagentThreshold
          min: 30
        probability: 0.02

- type: reagent
  id: Lacerinol
  name: reagent-name-lacerinol
  group: Medicine
  desc: reagent-desc-lacerinol
  physicalDesc: reagent-physical-desc-viscous
  flavor: syrupy
  color: "#283332"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        damage:
          types:
            Slash: -4 # Was 3, Buffed due to limb damage changes
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 12
        damage:
          types:
            Cold: 3
      - !type:ChemAddMoodlet
        moodPrototype: LacerinolEffect

- type: reagent
  id: Puncturase
  name: reagent-name-puncturase
  group: Medicine
  desc: reagent-desc-puncturase
  physicalDesc: reagent-physical-desc-fizzy
  flavor: fizzy
  color: "#b9bf93"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        damage:
          types:
            Piercing: -4
            Blunt: 0.1
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 11
        damage:
          types:
            Blunt: 5
      - !type:ChemAddMoodlet
        moodPrototype: PuncturaseEffect

- type: reagent
  id: Bruizine
  name: reagent-name-bruizine
  group: Medicine
  desc: reagent-desc-bruizine
  physicalDesc: reagent-physical-desc-mucus-like
  flavor: sour
  color: "#ff3636"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        damage:
          types:
            Blunt: -4 # Was 3, Buffed due to limb damage changes(GoobStation)
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 10.5
        damage:
          types:
            Poison: 4
      - !type:ChemAddMoodlet
        moodPrototype: BruizineEffect

- type: reagent
  id: Pyrazine
  name: reagent-name-pyrazine
  group: Medicine
  desc: reagent-desc-pyrazine
  physicalDesc: reagent-physical-desc-thick
  flavor: syrupy
  color: "#aa4308"
  metabolisms:
    Medicine:
      metabolismRate: 0.1 # slow metabolism to not be a godly combat med, its for treating burn victims efficiently
      effects:
      - !type:HealthChange
        damage:
          types:
            Heat: -1
      # od causes massive bleeding
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 20
        damage:
          types:
            Slash: 0.5
            Piercing: 0.5
      - !type:ChemVomit
        conditions:
        - !type:ReagentThreshold
          min: 15
        probability: 0.1
      - !type:Emote
        conditions:
        - !type:ReagentThreshold
          min: 20
        emote: Scream
        probability: 0.2

- type: reagent
  id: Insuzine
  name: reagent-name-insuzine
  group: Medicine
  desc: reagent-desc-insuzine
  physicalDesc: reagent-physical-desc-frosty
  flavor: metallic
  color: "#8147ff"
  metabolisms:
    Medicine:
      effects:
      # heals shocks and removes shock chems
      - !type:HealthChange
        damage:
          types:
            Shock: -4
      - !type:AdjustReagent
        reagent: Licoxide
        amount: -4
      - !type:AdjustReagent
        reagent:  Tazinide
        amount: -4
      # makes you a little chilly when not oding
      - !type:AdjustTemperature
        amount: -5000
      # od makes you freeze to death
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 12
        damage:
          types:
            Cold: 2
      - !type:AdjustTemperature
        conditions:
        - !type:ReagentThreshold
          min: 12
        amount: -30000
      - !type:Jitter
        conditions:
        - !type:ReagentThreshold
          min: 12

- type: reagent
  id: Opporozidone #Name based of an altered version of the startreck chem "Opporozine"
  name: reagent-name-opporozidone
  group: Medicine
  desc: reagent-desc-opporozidone
  physicalDesc: reagent-physical-desc-sickly
  flavor: acid
  color: "#b5e36d"
  worksOnTheDead: true
  metabolisms:
    Medicine:
      effects:
        - !type:ReduceRotting
          seconds: 20
          conditions:
          #Patient must be dead and in a cryo tube (or something cold)
          - !type:Temperature
            max: 150.0
          - !type:MobStateCondition
            mobstate: Dead

- type: reagent
  id: Necrosol
  name: reagent-name-necrosol
  group: Medicine
  desc: reagent-desc-necrosol
  physicalDesc: reagent-physical-desc-necrotic
  flavor: medicine
  color: "#86a5bd"
  worksOnTheDead: true
  plantMetabolism:
  - !type:PlantAdjustToxins
    amount: -5
  - !type:PlantAdjustHealth
    amount: 5
  - !type:PlantCryoxadone {}
  metabolisms:
    Medicine:
      effects:
        - !type:HealthChange
          conditions:
          - !type:Temperature
            max: 213.0
          damage:
            groups:
              Brute: -4
              Burn: -5
            types:
              Poison: -2

- type: reagent
  id : Aloxadone
  name: reagent-name-aloxadone
  group: Medicine
  desc: reagent-desc-aloxadone
  physicalDesc: reagent-physical-desc-soothing
  flavor: medicine
  color: "#89f77f"
  worksOnTheDead: true
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        conditions:
        - !type:Temperature
          max: 213.0
        damage:
          types:
            Heat: -5.0 # DeltaV - Increased from -3.0 to -5.0
            Shock: -5.0 # DeltaV - Increased from -3.0 to -5.0
            Cold: -5.0 # DeltaV - Added Cold healing to match Heat/Shock
            Caustic: -2.0 # DeltaV - Increased from -1.0 to -2.0

- type: reagent
  id : Mannitol # currently this is just a way to create psicodine
  name: reagent-name-mannitol
  group: Medicine
  desc: reagent-desc-mannitol
  physicalDesc: reagent-physical-desc-opaque
  flavor: sweet
  color: "#A0A0A0"
  metabolisms:
    Medicine:
      effects:
      - !type:PopupMessage
        conditions:
        - !type:ReagentThreshold
          min: 15
        type: Local
        visualType: Medium
        messages: [ "mannitol-effect-enlightened" ]
        probability: 0.2

- type: reagent
  id: Psicodine
  name: reagent-name-psicodine
  group: Medicine
  desc: reagent-desc-psicodine
  physicalDesc: reagent-physical-desc-shiny
  flavor: bitter
  color: "#07E79E"
  metabolisms:
    Medicine:
      effects:
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 30
        damage:
          types:
            Poison: 2
      - !type:GenericStatusEffect
        conditions:
        - !type:ReagentThreshold
          min: 30
        key: SeeingRainbows
        component: SeeingRainbows
        type: Add
        time: 8
        refresh: false
      - !type:GenericStatusEffect
        key: Jitter
        time: 2.0
        type: Remove
      - !type:GenericStatusEffect
        key: Drunk
        time: 6.0
        type: Remove
      - !type:ChemAddMoodlet
        moodPrototype: PsicodineEffect

- type: reagent
  id: PotassiumIodide
  name: reagent-name-potassium-iodide
  group: Medicine
  desc: reagent-desc-potassium-iodide
  physicalDesc: reagent-physical-desc-grainy
  flavor: medicine
  color: "#baa15d"
  metabolisms:
    Medicine:
      effects:
      - !type:GenericStatusEffect
        key: RadiationProtection
        component: RadiationProtection
        time: 2
        type: Add
        refresh: false
      - !type:HealthChange
        conditions:
          - !type:ReagentThreshold
            min: 20
        damage:
          types:
            Poison: 1

- type: reagent
  id: Haloperidol
  name: reagent-name-haloperidol
  group: Medicine
  desc: reagent-desc-haloperidol
  physicalDesc: reagent-physical-desc-crystalline
  flavor: medicine
  color: "#27870a"
  metabolisms:
    Medicine:
      effects:
      - !type:Emote
        emote: Yawn
        showInChat: true
        probability: 0.1
      - !type:GenericStatusEffect
        key: Drowsiness
        component: Drowsiness
        time: 4
        type: Add
        refresh: false
      - !type:GenericStatusEffect
        key: Jitter
        time: 4.0
        type: Remove
      - !type:GenericStatusEffect
        key: SeeingRainbows
        time: 10.0
        type: Remove
      - !type:AdjustReagent
        reagent: Desoxyephedrine
        amount: -3.0
      - !type:AdjustReagent
        reagent: Ephedrine
        amount: -3.0
      - !type:AdjustReagent
        reagent: Stimulants
        amount: -3.0
      - !type:AdjustReagent
        reagent: THC
        amount: -3.0
      - !type:AdjustReagent
        reagent: SpaceDrugs
        amount: -3.0
      - !type:AdjustReagent
        reagent: Bananadine
        amount: -3.0
      - !type:AdjustReagent
        reagent: SpaceGlue
        amount: -3.0
      - !type:AdjustReagent
        reagent: MindbreakerToxin
        amount: -3.0

- type: reagent
  id: HolyWater
  name: reagent-name-holywater
  group: Medicine
  desc: reagent-desc-holywater
  physicalDesc: reagent-physical-desc-translucent
  flavor: holy
  color: "#75b1f0"
  boilingPoint: 100.0
  meltingPoint: 0.0
  reactiveEffects:
    Unholy: # Vampire
      methods: [ Touch ]
      effects:
        - !type:HealthChange
          damage:
            types:
              Heat: 5
        - !type:Emote
          emote: Scream
    Acidic:
      methods: [ Touch ]
      effects: # imp edit cosmic cult
      - !type:CleanseCult
        conditions:
        - !type:ReagentThreshold
          min: 10
      - !type:HealthChange
        scaleByQuantity: true
        ignoreResistances: false
        damage:
          types:
            Holy: 0.5
  metabolisms: #Could nullify debuffs of feeding.
    Drink:
      effects:
      - !type:SatiateThirst
        factor: 3
    Medicine:
      effects:
      #If vampire
      - !type:HealthChange
        conditions:
        - !type:OrganType
          type: Vampire
        damage:
          types:
            Heat: 2
      - !type:Emote
        conditions:
        - !type:OrganType
          type: Vampire
        emote: Scream
        probability: 0.3
      #If not vampire
      - !type:ModifyBloodLevel
        amount: 0.1
      - !type:HealthChange
        conditions:
        - !type:OrganType
          type: Vampire
          shouldHave: false
        damage:
          groups:
            Brute: -0.5
            Burn: -0.5
          types:
            Holy: 1
      - !type:PurifyEvil
        conditions:
        - !type:ReagentThreshold
          min: 15
  plantMetabolism: #Heals plants a little with the holy power within it.
  - !type:PlantAdjustHealth
    amount: 0.1

- type: reagent
  id: Artiplates
  name: reagent-name-artiplates
  group: Medicine
  desc: reagent-desc-artiplates
  physicalDesc: reagent-physical-desc-cloudy
  flavor: medicine
  color: "#382766"
  metabolisms:
    Medicine:
      effects:
      - !type:ModifyBleedAmount
        conditions:
        - !type:ReagentThreshold
          max: 11
        amount: -1
      - !type:ModifyBloodLevel
        amount: 8 # at least you still make blood while ODing :]
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          max: 11
        damage:
          types:
            Bloodloss: -5
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 11
        damage:
          types:
            Slash: 3.5
