ent-PrintedDocument = { ent-Paper }
    .desc = Bureaucratic unit. A document printed on a printer.
ent-PrintedDocumentReportStation = Report on the situation at the station
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentReportOnEliminationOfViolations = Report on elimination of violations
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentReportDepartment = Report on the work of the department
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentReportEmployeePerformance = Report on employee performance
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentReportOnTheChaptersMeeting = Report on chapter meeting
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentInternalAffairsAgentsReport = Internal Investigation Report
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentConditionReport = Report on technical condition
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentReportStudyObject = Report on object Investigation
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentExperimentReport = Experiment report
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentDisposalReport = Disposal report
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentApplicationAppointmentInterim = Statement of Appointment to the Temporary Acting Authority
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentApplicationEmployment = Statement of Employment
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentLetterResignation = Statement of Dismissal
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentApplicationAccess = Statement of access
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentApplicationEquipment = Statement for equipment
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentAppeal = Appeal
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentEvacuationShuttleRequest = Evacuation Shuttle Request
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentShuttleRegistrationRequest = Shuttle registration request
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentRequestCallMembersCentralCommitteeDSO = Request to call members of CC, DSO
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentRequestRequestToEstablishThreatLevel = Request to establish threat level
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentRequestChangeSalary = Request for salary change
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentRequestForNonlistedEmployment = Request for non-listed employment
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentRequestForPromotion = Request for promotion
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentRequestDocuments = Request for the provision of documents
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentRequestEuthanasia = Request for euthanasia
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentRequestConstructionWork = Request for construction works
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentRequestModernization = Request for modernization
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentComplaintViolationLaborRules = Complaint for violation of labor order
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentComplaintOffense = Complaint about an offense
  .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentPermissionEquipment = Authorization to use equipment
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentPermissionToTravelInCaseOfThreat = Permission to travel in case of threat
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentSearchPermission = Authorization to search
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentPermissionToCarryWeapons = Permission to carry weapons
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentPrescriptionDrugAuthorization = Prescription Drug Authorization
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentPermissionDisposeBody = Authorization to dispose of the body
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentConstructionPermit = Building permit
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentPermissionToExtendMarriage = Permission to extend marriage
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentOrderDismissal = Dismissal order
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentOrderDeprivationAccess = Denial of access order
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentOrderEncouragement = Incentive order
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentOrderParolePrisoner = Prisoner parole order
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentOrderRecognizingSentienceCreature = An order recognizing the reasonableness of the substance
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentOrderMedicalIntervention = Order for medical intervention
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentProductManufacturingOrder = Order for the production of a product
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentOrderPurchaseResourcesEquipment = Purchase order for resources, equipment
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentOrderingSpecialEquipment = Ordering special equipment
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentOrderPurchaseWeapons = Purchase order for armaments
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentCertificate = Certificate
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentCertificateAdvancedTraining = Certificate of advanced training
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentCertificateOffense = Certificate of Offense
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentDeathCertificate = Death certificate
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentMarriageCertificate = Marriage certificate
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentDivorceCertificate = Certificate of divorce
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentClosingIndictment = Indictment
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentSentence = Sentence
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentJudgment = Judgment
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentStatementHealth = Health report
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentDecisionToStartTrial = Decision to start a trial
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentErrorLoadingFormHeader = ERROR loading form header
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentNoticeOfLiquidation = NOTICE OF LIQUIDATION
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentBusinessDeal = BUSINESS DEAL
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentNoteBeginningMilitaryActions = NOTE BEGINNING MILITARY ACTIONS
    .desc = { ent-PrintedDocument.desc }
ent-PrintedDocumentReportAccomplishmentGoals = REPORT ACCOMPLISHMENT GOALS
    .desc = { ent-PrintedDocument.desc }
