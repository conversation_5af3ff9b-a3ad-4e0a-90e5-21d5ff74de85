using Robust.Shared.GameStates;
using Robust.Shared.Serialization;

namespace Content.Shared._Pirate.DNDMage;

/// <summary>
/// Компонент, який зберігає інформацію про сумку, з якої була заспавнена кістка,
/// та додає функціональність "saving throws" для порятунку мага в критичний момент.
/// </summary>
[RegisterComponent, NetworkedComponent, AutoGenerateComponentState]
public sealed partial class DNDBagDiceComponent : Component
{
    /// <summary>
    /// EntityUid сумки, з якої була заспавнена кістка.
    /// Не серіалізується через DataField, оскільки EntityUid не підтримує серіалізацію.
    /// </summary>
    [AutoNetworkedField]
    public EntityUid DiceBagUid;

    /// <summary>
    /// ID контейнера в сумці, з якого була заспавнена кістка.
    /// </summary>
    [DataField("containerId"), AutoNetworkedField]
    public string ContainerId = "storage-default";

    /// <summary>
    /// Чи була кістка вже прив'язана до сумки.
    /// </summary>
    [DataField("isBound"), AutoNetworkedField]
    public bool IsBound = false;

    /// <summary>
    /// Чи може кістка бути використана для "saving throw".
    /// </summary>
    [DataField("canSavingThrow"), AutoNetworkedField]
    public bool CanSavingThrow = true;

    /// <summary>
    /// Час останнього використання "saving throw" для цієї кістки.
    /// </summary>
    [DataField("lastSavingThrowTime"), AutoNetworkedField]
    public TimeSpan LastSavingThrowTime;

    /// <summary>
    /// Затримка між використаннями "saving throw" для цієї кістки.
    /// </summary>
    [DataField("savingThrowCooldown"), AutoNetworkedField]
    public TimeSpan SavingThrowCooldown = TimeSpan.FromMinutes(5);
}
