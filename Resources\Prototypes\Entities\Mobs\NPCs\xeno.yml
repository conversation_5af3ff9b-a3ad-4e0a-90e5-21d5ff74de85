# Hacky for the stress test so don't even consider adding to this
- type: entity
  name: "<PERSON><PERSON><PERSON><PERSON><PERSON>"
  id: MobXeno
  parent: SimpleSpaceMobBase
  description: "Вони приходять переважно вночі. Здебільшого."
  components:
  - type: Insulated
  - type: CombatMode
  - type: InputMover
  - type: MobMover
  - type: Absorbable
  - type: HTN
    rootTask:
      task: XenoCompound
    blackboard:
      NavClimb: !type:Bool
        true
      NavInteract: !type:Bool
        true
      NavPry: !type:Bool
        true
      NavSmash: !type:Bool
        true
  - type: Tool
    speedModifier: 1.5
    qualities:
      - Prying
  - type: Prying
    pryPowered: !type:Bool
        true
    force: !type:Bool
      true
    useSound:
      path: /Audio/Items/crowbar.ogg
  - type: Reactive
    groups:
      Flammable: [Touch]
      Extinguish: [Touch]
  - type: NpcFactionMember
    factions:
    - Xeno
  - type: Hands
  - type: ComplexInteraction
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/Xenos/burrower.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: running
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.25
        density: 1000
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: MobState
  - type: Deathgasp
  - type: MobStateActions
    actions:
      Critical:
      - ActionCritSuccumb
      - ActionCritFakeDeath
      - ActionCritLastWords
  - type: MobThresholds
    thresholds:
      0: Alive
      50: Critical
      100: Dead
  - type: SlowOnDamage
    speedModifierThresholds:
      25: 0.5
  - type: Stamina
    critThreshold: 200
  - type: Bloodstream
    bloodReagent: FluorosulfuricAcid
    bloodMaxVolume: 650
  - type: MeleeWeapon
    altDisarm: false
    angle: 0
    soundHit:
     collection: AlienClaw
    animation: WeaponArcBite
    damage:
      groups:
        Brute: 6
  - type: DamageStateVisuals
    rotate: true
    states:
      Alive:
        Base: running
      Critical:
        Base: crit
      Dead:
        Base: dead
  - type: Puller
    needsHands: false
  - type: Butcherable
    butcheringType: Spike
    spawned:
    - id: FoodMeatXeno
      amount: 5
  - type: GhostRole
    allowMovement: true
    allowSpeech: true
    makeSentient: true
    name: ghost-role-information-xeno-name
    description: ghost-role-information-xeno-description
    rules: ghost-role-information-xeno-rules
    raffle:
      settings: default
  - type: GhostTakeoverAvailable
  - type: TypingIndicator
    proto: alien
  - type: Temperature
    heatDamageThreshold: 360
    coldDamageThreshold: -150
    currentTemperature: 310.15
  - type: Tag
    tags:
      - CannotSuicide
      - DoorBumpOpener
      - FootstepSound
  - type: NoSlip
  - type: Perishable #Ummmm the acid kills a lot of the bacteria or something
    molsPerSecondPerUnitMass: 0.0005
  - type: Speech
    speechVerb: LargeMob
    speechSounds: Xenonid # DeltaV: Use RMC's speech sounds for sentient xenos
  - type: Psionic
    removable: false
    roller: false
  - type: InnatePsionicPowers
    powersToAdd:
      - TelepathyPower
  - type: LanguageKnowledge
    speaks:
    - Xeno
    understands:
    - Xeno
  - type: ThermalVision
    isActive: true
    lightRadius: 15
    color: "#808080"
    activateSound: null
    deactivateSound: null
  - type: Fauna # Lavaland Change

- type: entity
  name: "Преторіанський"
  parent: MobXeno
  id: MobXenoPraetorianNPC # Delta V - These no longer inherit the ghost takeover comp
  components:
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/Xenos/praetorian.rsi
    offset: 0,0.4
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: running
  - type: Absorbable
  - type: MobThresholds
    thresholds:
      0: Alive
      100: Critical
      150: Dead
  - type: Stamina
    critThreshold: 300
  - type: SlowOnDamage
    speedModifierThresholds:
      50: 0.7
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.45
        density: 400
        mask:
        - MobMask
        layer:
        - MobLayer

- type: entity
  name: "Дрон"
  parent: MobXeno
  id: MobXenoDroneNPC # Delta V - These no longer inherit the ghost takeover comp
  components:
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/Xenos/drone.rsi
    offset: 0,0.2
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: running
  - type: Absorbable
  - type: MobThresholds
    thresholds:
      0: Alive
      80: Critical
      130: Dead
  - type: SlowOnDamage
    speedModifierThresholds:
      40: 0.7
  - type: MeleeWeapon
    damage:
      groups:
        Brute: 6
  - type: MovementSpeedModifier
    baseSprintSpeed: 4
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.45
        density: 450
        mask:
        - MobMask
        layer:
        - MobLayer

- type: entity
  name: "Королева"
  parent: MobXeno
  id: MobXenoQueenNPC # Delta V - These no longer inherit the ghost takeover comp
  components:
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/Xenos/queen.rsi
    offset: 0,0.4
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: running
  - type: Absorbable
  - type: MobThresholds
    thresholds:
      0: Alive
      300: Critical
      350: Dead
  - type: SlowOnDamage
    speedModifierThresholds:
      150: 0.7
  - type: MovementSpeedModifier
  - type: MeleeWeapon
    damage:
     groups:
       Brute: 12
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.45
        density: 15500
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Tag
    tags:
    - CannotSuicide
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    - Xeno
    understands:
    - TauCetiBasic
    - Xeno

- type: entity
  name: "Ravager"
  parent: MobXeno
  id: MobXenoRavagerNPC # Delta V - These no longer inherit the ghost takeover comp
  components:
  - type: Absorbable
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/Xenos/ravager.rsi
    offset: 0,0.5
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: running
  - type: MobThresholds
    thresholds:
      0: Alive
      100: Critical
      150: Dead
  - type: MovementSpeedModifier
    baseSprintSpeed: 4
  - type: MeleeWeapon
    damage:
     groups:
       Brute: 10
  - type: SlowOnDamage
    speedModifierThresholds:
      50: 0.7
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.45
        density: 1350
        mask:
        - MobMask
        layer:
        - MobLayer

- type: entity
  name: "Бігун"
  parent: MobXeno
  id: MobXenoRunnerNPC # Delta V - These no longer inherit the ghost takeover comp
  components:
  - type: Absorbable
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/Xenos/runner.rsi
    offset: 0,0.6
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: running
  - type: MovementSpeedModifier
    baseSprintSpeed: 6.0
  - type: MeleeWeapon
    damage:
     groups:
       Brute: 10
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 235
        mask:
        - MobMask
        layer:
        - MobLayer

- type: entity
  name: "Руні"
  parent: MobXenoRunnerNPC # Delta V - These no longer inherit the ghost takeover comp
  id: MobXenoRounyNPC # Delta V - These no longer inherit the ghost takeover comp
  components:
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/Xenos/rouny.rsi
    offset: 0,0.6
    scale: 0.7, 0.7
  - type: Butcherable
    butcheringType: Spike
    spawned:
    - id: FoodMeatRouny
      amount: 3

- type: entity
  name: "Спіттер"
  parent: MobXeno
  id: MobXenoSpitterNPC # Delta V - These no longer inherit the ghost takeover comp
  components:
  - type: Absorbable
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/Xenos/spitter.rsi
    offset: 0,0.3
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: running
  - type: MobThresholds
    thresholds:
      0: Alive
      50: Dead
  - type: SlowOnDamage
    speedModifierThresholds:
      25: 0.7
  - type: HTN
    rootTask:
      task: SimpleRangedHostileCompound
  - type: RechargeBasicEntityAmmo
    rechargeCooldown: 0.75
  - type: BasicEntityAmmoProvider
    proto: BulletAcid
    capacity: 1
    count: 1
  - type: Gun
    fireRate: 0.75
    useKey: false
    selectedMode: FullAuto
    availableModes:
      - FullAuto
    soundGunshot: /Audio/Weapons/Xeno/alien_spitacid.ogg
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.45
        density: 235
        mask:
        - MobMask
        layer:
        - MobLayer

- type: entity
  name: "просторовий суматор"
  parent: SimpleSpaceMobBase
  id: MobPurpleSnake
  description: "Загрозлива фіолетова змія з Kepler-283c."
  components:
  - type: Absorbable
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/Xenos/purple_snake.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: purple_snake
  - type: DamageStateVisuals
    states:
      Alive:
        Base: purple_snake
      Critical:
        Base: dead_purple_snake
      Dead:
        Base: dead_purple_snake
  - type: Grammar
    attributes:
      proper: true
      gender: male
  - type: InputMover
  - type: MobMover
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
  - type: NpcFactionMember
    factions:
    - Xeno
  - type: MeleeWeapon
    angle: 0
    animation: WeaponArcBite
    damage:
      types:
        Piercing: 5
  - type: SolutionContainerManager
    solutions:
      melee:
        reagents:
        - ReagentId: Toxin
          Quantity: 50
  - type: MeleeChemicalInjector
    solution: melee
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 25
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepSnake
  - type: Tag
    tags:
    - DoorBumpOpener
    - FootstepSound

- type: entity
  name: "просторовий суматор"
  parent: MobPurpleSnake
  id: MobSmallPurpleSnake
  suffix: small
  description: "Зменшена версія загрозливої фіолетової змії з Kepler-283c."
  components:
  - type: Absorbable
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/Xenos/purple_snake.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: small_purple_snake
  - type: DamageStateVisuals
    states:
      Alive:
        Base: small_purple_snake
      Critical:
        Base: dead_small_purple_snake
      Dead:
        Base: dead_small_purple_snake
  - type: SolutionTransfer
    maxTransferAmount: 1
