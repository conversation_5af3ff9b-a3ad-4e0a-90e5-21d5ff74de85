- type: entity
  parent: BaseItem
  id: UtensilBase
  abstract: true
  components:
  - type: Sprite
    sprite: Objects/Misc/utensils.rsi
  - type: Item # TODO add inhand sprites for all utensils
    sprite: Objects/Misc/utensils.rsi
  - type: SpaceGarbage
  # Shitmed Change
  - type: Tweezers # Any utensil can poorly remove organs
    speed: 0.2
  - type: SurgeryTool
    startSound:
      path: /Audio/_Shitmed/Medical/Surgery/retractor1.ogg
    endSound:
      path: /Audio/_Shitmed/Medical/Surgery/hemostat1.ogg

- type: entity
  parent: UtensilBase
  id: UtensilBasePlastic
  abstract: true
  components:
  - type: Utensil
    breakChance: 0.20
  - type: StaticPrice
    price: 0
  - type: Tag
    tags:
    - Plastic
    - Trash
  - type: MeleeWeapon
    wideAnimationRotation: 180
    attackRate: .6666
    damage:
      types:
        Blunt: 0

- type: entity
  parent: UtensilBase
  id: Fork
  name: "вилка"
  description: "Прилад для їжі, ідеально підходить для нанесення ножових ран."
  components:
  - type: Tag
    tags:
    - Hoe
    - Metal
  - type: Sprite
    state: fork
  - type: Utensil
    types:
    - Fork
  - type: MeleeWeapon
    wideAnimationRotation: 180
    attackRate: .6666
    damage:
      types:
        Piercing: 5
  # Shitmed Change
  - type: Tweezers # Forks are better than spoons
    speed: 0.35
  - type: DamageOtherOnHit
    staminaCost: 2.5
  - type: EmbeddableProjectile
    removalTime: 0.5
    autoRemoveDuration: 10
  - type: EmbedPassiveDamage
  - type: ThrowingAngle
    angle: 180

- type: entity
  parent: UtensilBasePlastic
  id: ForkPlastic
  name: "пластикова виделка"
  description: "Прилад для їжі, ідеально підходить для нанесення ножових ран."
  components:
  - type: Sprite
    state: plastic_fork
  - type: Utensil
    types:
    - Fork
  # Shitmed Change
  - type: Tweezers # Forks are better than spoons
    speed: 0.35

- type: entity
  parent: UtensilBase
  id: Spoon
  name: "ложка"
  description: "Ложки немає."
  components:
  - type: Tag
    tags:
    - Metal
  - type: Sprite
    state: spoon
  - type: Item
    heldPrefix: spoon
  - type: Utensil
    types:
    - Spoon
  - type: MeleeWeapon
    wideAnimationRotation: 180
    attackRate: .6666
    damage:
      types:
        Blunt: 1
  - type: DamageOtherOnHit
  - type: ThrowingAngle
    angle: 180
  - type: Shovel
    speedModifier: 0.1 # you can try
  - type: ReactionMixer
    mixMessage: "spoon-mixing-success"
    timeToMix: 0.5
    reactionTypes:
    - Stir

- type: entity
  parent: UtensilBasePlastic
  id: SpoonPlastic
  name: "пластикова ложка"
  description: "Ложки немає."
  components:
  - type: Sprite
    state: plastic_spoon
  - type: Item
    heldPrefix: spoon
  - type: Utensil
    types:
    - Spoon
  - type: Shovel
    speedModifier: 0.1 # you can try
  - type: ReactionMixer
    mixMessage: "spoon-mixing-success"
    timeToMix: 0.5
    reactionTypes:
    - Stir

- type: entity
  parent: UtensilBasePlastic
  id: KnifePlastic
  name: "пластиковий ніж"
  description: "Та... це не ніж. Це - ніж."
  components:
  - type: Sprite
    state: plastic_knife
  - type: Utensil
    types:
    - Knife
  - type: Tag
    tags:
    - Plastic
    - Trash
    - Knife

- type: entity
  parent: UtensilBase
  id: BarSpoon
  name: "барна ложка"
  description: "Ваш особистий помічник, який змішує напої та змінює життя."
  components:
  - type: Tag
    tags:
    - Metal
  - type: Sprite
    state: bar_spoon
  - type: Item
    heldPrefix: spoon
  - type: Utensil
    types:
    - Spoon
  - type: ReactionMixer
    mixMessage: "spoon-mixing-success"
    timeToMix: 0.5
    reactionTypes:
    - Stir
  - type: MeleeWeapon
    wideAnimationRotation: 180
    attackRate: .5
    damage:
      types:
        Blunt: 2
  - type: DamageOtherOnHit
  - type: ThrowingAngle
    angle: 180
  - type: Shovel
    speedModifier: 0.05 # nah
