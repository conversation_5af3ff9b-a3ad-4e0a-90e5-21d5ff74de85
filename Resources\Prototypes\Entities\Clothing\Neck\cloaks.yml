- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakCentcom
  name: "плащ центрального командира"
  description: "Помпезний та елітний зелений плащ з гарним золотим оздобленням, пошитий спеціально для командира ЦК. Він настільки важкий, що золоте оздоблення може бути справжнім."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/centcomcloakformal.rsi
  - type: StealTarget
    stealGroup: HeadCloak # leaving this here because I suppose it might be interesting?

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakCap
  name: "плащ капітана"
  description: "Помпезний і зручний синій плащ з красивою золотою обробкою, хоч і не є особливо цінним, як інші ваші речі, але він, безумовно, вишуканий."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/cap.rsi
  - type: StealTarget
    stealGroup: HeadCloak

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakHos
  name: "плащ начальника служби безпеки"
  description: "Вишуканий темно-червоний плащ для тих, хто здатен довести свою перевагу над кривдниками. Спробуйте бути цивілізованим у судовому переслідуванні!"
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/hos.rsi
  - type: StealTarget
    stealGroup: HeadCloak

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakCe
  name: "плащ головного інженера"
  description: "Темно-зелений плащ зі світло-блакитним орнаментом, який давали тим, хто довів, що володіє точним інженерним мистецтвом."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/ce.rsi
  - type: StealTarget
    stealGroup: HeadCloak

- type: entity
  parent: ClothingNeckBase
  id: ClothingCloakCmo
  name: "плащ головного лікаря"
  description: "Стерильний блакитний плащ із зеленим хрестом, що випромінює почуття обов'язку і готовність допомогти іншим."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/cmo.rsi
  - type: StealTarget
    stealGroup: HeadCloak

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakRd
  name: "плащ наукового директора" # DeltaV - Epistemics Department replacing Science
  description: "Білий плащ з фіолетовими смужками, що демонструє ваш статус арбітра передових технологій."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/rd.rsi
  - type: StealTarget
    stealGroup: HeadCloak

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakQm
  name: "плащ офіцера логістики" # DeltaV - Logistics Department replacing Cargo
  description: "Міцний коричневий плащ зі світловідбиваючою смугою, хоч і не такий вигадливий, як інші, але демонструє ваші управлінські здібності."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/qm.rsi
  - type: StealTarget
    stealGroup: HeadCloak

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakHop
  name: "плащ голови персоналу"
  description: "Синій плащ з червоними плечима і золотими ґудзиками, який доводить, що ви є воротарем будь-якого шлюзу на станції."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/hop.rsi
  - type: StealTarget
    stealGroup: HeadCloak

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakHerald
  name: "плащ герольда"
  description: "Зловісний червоний плащ з шипами на плечах."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/herald.rsi

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakNanotrasen
  name: "плащ Нанотрейзен"
  description: "Величний синій плащ, що представляє НаноТрейзен."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/nanotrasen.rsi

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakCapFormal
  name: "формальний плащ капітана"
  description: "Пишний та оздоблений плащ для особливих випадків."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/capcloakformal.rsi
  - type: StealTarget
    stealGroup: HeadCloak

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakAdmin
  name: "плащ адміністратора"
  description: "Ух!"
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/admin.rsi
  - type: StealTarget
    stealGroup: HeadCloak

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakMiner
  name: "плащ шахтаря"
  description: "Його носили найдосвідченіші шахтарі, ті, хто зрушив гори і висипав долини."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/miner.rsi

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakTrans
  name: "плащ вампіра"
  description: "Носиться високопоставленими вампірами трансильванського товариства вампірів."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/trans.rsi

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakGoliathCloak
  name: "плащ голіафа"
  description: "Плащ, виготовлений зі шкури витривалої фауни з далекої планети, щоправда, з віком його захисні властивості потьмяніли."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/goliathcloak.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodGoliathCloak
    requiredSlot:
    - neck
    slot: head
  - type: ContainerContainer
    containers:
      toggleable-clothing: !type:Container {}

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakPirateCap
  name: "плащ капітана піратів"
  description: "Досить привабливий чорний піратський плащ, доповнений мотивом черепа."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/piratecap.rsi

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakMoth
  name: "плащ молі"
  description: "Накидка у вигляді крил метелика - незвичайний і оригінальний елемент гардеробу, здатний привернути увагу оточуючих. Він виготовлений з тонкої тканини, що імітує крила метелика, з м'якими та пухнастими краями. Дощовик застібається на шиї за допомогою липучки, має капюшон у формі голови метелика."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/moth.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodMoth
    requiredSlot:
    - neck
    slot: head
  - type: ContainerContainer
    containers:
      toggleable-clothing: !type:Container {}
  - type: TypingIndicatorClothing
    proto: moth

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakVoid
  name: "плащ порожнечі"
  description: "Плащ темряви. Для тих, хто перейшов на темний бік сили."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/void.rsi
  - type: TypingIndicatorClothing
    proto: alien

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakAce
  name: "плащ пілота"
  description: "Плащ вручається найкращим космічним асам Нанотрейзена."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/ace.rsi

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakAro
  name: "плащ перевертня"
  description: "Цей плащ дає іншим зрозуміти, що ви вовк-одинак."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/aro.rsi

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakBi
  name: "плащ отрути"
  description: "Фіолетовий колір є чітким індикатором того, що ви отруйні."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/bi.rsi

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakIntersex
  name: "плащ циклопа"
  description: "Коло на цьому плащі символізує око циклопа."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/intersex.rsi

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakLesbian
  name: "плащ поета"
  description: "Цей плащ належав давньому поетові, ви забули, якому саме."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/les.rsi

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakGay
  name: "плащ багаторівневого маркетингу"
  description: "Цей плащ користується великим попитом у маркетингових офісах Нанотрейзен."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/gay.rsi

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakEnby
  name: "плащ мисливця за скарбами"
  description: "Цей плащ належав жадібному шукачеві скарбів."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/enby.rsi

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakPan
  name: "кухарський фартух"
  description: "Призначений для носіння поряд зі сковорідкою."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/pan.rsi

- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckCloakRedHood
  name: "Червоний плащ"
  description: "Плащ, створений для подорожей та комфорту. Злегка пахне вишневим пирогом?"
  components:
  - type: Sprite
    sprite: Clothing/Neck/Cloaks/redhood.rsi
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodRedHood
    requiredSlot:
    - neck
    slot: head
  - type: ContainerContainer
    containers:
      toggleable-clothing: !type:Container {}
