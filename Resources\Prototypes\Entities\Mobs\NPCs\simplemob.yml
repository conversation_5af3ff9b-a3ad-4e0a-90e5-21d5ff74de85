- type: entity
  save: false
  parent:
  - BaseMob
  - MobDamageable
  - MobPolymorphable
  - MobAtmosExposed
  id: BaseSimpleMob
  suffix: AI
  abstract: true
  components:
  - type: Reactive
    groups:
      Flammable: [Touch]
      Extinguish: [Touch]
      Acidic: [Touch, Ingestion]
  - type: Internals
  - type: MovementSpeedModifier
    baseWalkSpeed : 4
    baseSprintSpeed : 4
  - type: StatusEffects
    allowed:
    - SlowedDown
    - Stutter
    - Electrocution
    - ForcedSleep
    - TemporaryBlindness
    - Pacified
    - Flashed
    - RadiationProtection
    - Drowsiness
  - type: Buckle
  - type: StandingState
  - type: Tag
    tags:
    - DoorBumpOpener
  - type: StatusIcon # Goobstation
    bounds: -0.5,-0.5,0.5,0.5

- type: entity
  abstract: true
  parent:
  - BaseSimpleMob
  - MobCombat
  - MobBloodstream
  - MobFlammable
  id: SimpleSpaceMobBase # Mob without barotrauma, freezing and asphyxiation (for space carps!?)
  suffix: AI
  components:
  - type: NpcFactionMember
    factions:
    - SimpleNeutral
  - type: HTN
    rootTask:
      task: IdleCompound
  - type: MeleeWeapon
    angle: 0
    animation: WeaponArcBite
  - type: Body
    prototype: Animal
  - type: Climbing
  - type: NameIdentifier
    group: GenericNumber
  - type: SlowOnDamage
    speedModifierThresholds:
      60: 0.7
      80: 0.5
  - type: MobPrice
    price: 1000 # Living critters are valuable in space.
  - type: Perishable

- type: entity
  parent:
  - MobRespirator
  - MobAtmosStandard
  - SimpleSpaceMobBase
  id: SimpleMobBase # for air breathers
  suffix: AI
  abstract: true
  components:
  - type: Hunger
    thresholds: # only animals and rats are derived from this prototype so let's override it here and in rats' proto
      Overfed: 100
      Okay: 50
      Peckish: 25
      Starving: 10
      Dead: 0
    baseDecayRate: 0.00925925925926 # it is okay for animals to eat and drink less than humans, but more frequently
  - type: Thirst
    thresholds:
      OverHydrated: 200
      Okay: 150
      Thirsty: 100
      Parched: 50
      Dead: 0
    baseDecayRate: 0.04
  - type: StatusEffects
    allowed:
    - Stun
    - KnockedDown
    - SlowedDown
    - Stutter
    - Electrocution
    - ForcedSleep
    - TemporaryBlindness
    - Pacified
    - StaminaModifier
    - Flashed
    - RadiationProtection
    - Drowsiness
  - type: Bloodstream
    bloodMaxVolume: 150
  - type: MobPrice
    price: 150
  - type: FloatingVisuals
  - type: Speech
