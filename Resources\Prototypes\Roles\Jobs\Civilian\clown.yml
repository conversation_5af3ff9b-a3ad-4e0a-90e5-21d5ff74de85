- type: job
  id: Clown
  name: job-name-clown
  description: job-description-clown
  playTimeTracker: JobClown
  startingGear: ClownGear
  icon: "JobIconClown"
  supervisors: job-supervisors-hop
  access:
  - Theatre
  - Maintenance
  - Clown # DeltaV - Add Clown access
  special:
  - !type:AddComponentSpecial
    components:
    - type: Clumsy
      gunShootFailDamage:
        types: #literally just picked semi random valus. i tested this once and tweaked it.
          Blunt: 5
          Piercing: 4
        groups:
          Burn: 3
    - type: SleepEmitSound
      snore: /Audio/Voice/Misc/silly_snore.ogg
      interval: 10
    - type: Snoring # Necessary so SleepEmitSound sound effects play
  - !type:AddImplantSpecial
    implants: [ SadTromboneImplant ]
  requirements:
    - !type:CharacterOverallTimeRequirement # DeltaV - Playtime requirement
      min: 7200 #2 hrs
    - !type:CharacterWhitelistRequirement # PIRATE - Whitelist requirement

- type: startingGear
  id: ClownGear
  subGear:
  - ClownPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitClown
    back: ClothingBackpackClownFilled
    shoes: ClothingShoesClown
    mask: ClothingMaskClown
    pocket1: BikeHorn
    pocket2: ClownRecorder
    id: ClownPDA
    ears: ClothingHeadsetService
  satchel: ClothingBackpackSatchelClownFilled
  duffelbag: ClothingBackpackDuffelClownFilled

- type: startingGear
  id: ClownPlasmamanGear
  parent: BasePlasmamanGear
  equipment:
    jumpsuit: ClothingUniformEnvirosuitClown
    head: ClothingHeadEnvirohelmClown
    gloves: ClothingHandsGlovesEnviroglovesClown
    mask: ClothingMaskClown # Parent sets mask to breath mask so set it again here
