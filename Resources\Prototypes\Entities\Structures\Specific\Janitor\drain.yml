- type: entity
  id: FloorDrain
  name: "злив"
  description: "Зливає калюжі навколо себе. Корисний для викидання відер зі швабрами або для підтримання чистоти в певних приміщеннях."
  placement:
    mode: SnapgridCenter
  components:
    - type: Sprite
      drawdepth: FloorObjects
      sprite: Objects/Specific/Janitorial/drain.rsi
      layers:
      - state: icon
      - map: [ "enum.SolutionContainerLayers.Fill" ]
        state: fill-1
        visible: false
    - type: InteractionOutline
    - type: Clickable
    - type: Transform
      anchored: true
    - type: Physics
      bodyType: Static
      canCollide: false
    - type: AmbientSound
      enabled: false
      volume: -8
      range: 8
      sound:
        path: /Audio/Ambience/Objects/drain.ogg
    - type: Drain
    - type: DumpableSolution
      solution: drainBuffer
    - type: Appearance
    - type: SolutionContainerVisuals
      maxFillLevels: 1
      fillBaseName: fill-
      solutionName: drainBuffer
    - type: SolutionContainerManager
      solutions:
        drainBuffer:
          maxVol: 1000
    - type: DrainableSolution
      solution: drainBuffer
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: Metallic
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 200
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
        - trigger:
            !type:DamageTrigger
            damage: 100
          behaviors:
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
            - !type:PlaySoundBehavior
              sound:
                collection: MetalBreak
