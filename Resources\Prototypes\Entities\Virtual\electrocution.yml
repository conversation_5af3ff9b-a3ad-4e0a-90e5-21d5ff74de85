﻿# Special entity used to attach to power networks as load when somebody gets electrocuted.
- type: entity
  id: VirtualElectrocutionLoadBase
  abstract: true
  components:
  - type: Electrocution
  - type: Icon
    # Shows up inside the power monitoring console.
    sprite: "Structures/Wallmounts/signs.rsi"
    state: "shock"

- type: entity
  id: VirtualElectrocutionLoadHVPower
  parent: VirtualElectrocutionLoadBase
  categories: [ HideSpawnMenu ]
  components:
  - type: NodeContainer
    nodes:
      electrocution: !type:ElectrocutionNode
        nodeGroupID: HVPower
        needAnchored: false
  - type: PowerConsumer
    voltage: High
    drawRate: 50000

- type: entity
  id: VirtualElectrocutionLoadMVPower
  parent: VirtualElectrocutionLoadBase
  categories: [ HideSpawnMenu ]
  components:
  - type: NodeContainer
    nodes:
      electrocution: !type:ElectrocutionNode
        nodeGroupID: MVPower
        needAnchored: false
  - type: PowerConsumer
    voltage: Medium
    drawRate: 50000

- type: entity
  id: VirtualElectrocutionLoadApc
  parent: VirtualElectrocutionLoadBase
  categories: [ HideSpawnMenu ]
  components:
  - type: NodeContainer
    nodes:
      electrocution: !type:ElectrocutionNode
        nodeGroupID: Apc
        needAnchored: false
  - type: PowerConsumer
    voltage: Apc
    drawRate: 50000
