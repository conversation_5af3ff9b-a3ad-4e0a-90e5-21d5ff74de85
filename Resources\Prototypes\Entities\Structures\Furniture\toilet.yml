- type: entity
  name: "туалет"
  id: ToiletEmpty
  suffix: Empty
  parent: [ DisposalUnitBase, SeatBase ]
  description: "Пристрій для змивання людських екскрементів."
  components:
  - type: Sprite
    sprite: Structures/Furniture/toilet.rsi
    layers:
    - state: condisposal
      map: [ "enum.DisposalUnitVisualLayers.Unanchored" ]
    - state: disposal
      map: [ "enum.DisposalUnitVisualLayers.Base" ]
    - state: disposal-flush
      map: [ "enum.DisposalUnitVisualLayers.OverlayFlush" ]
    - state: dispover-charge
      map: [ "enum.DisposalUnitVisualLayers.OverlayCharging" ]
    - state: dispover-ready
      map: [ "enum.DisposalUnitVisualLayers.OverlayReady" ]
    - state: dispover-full
      map: [ "enum.DisposalUnitVisualLayers.OverlayFull" ]
    - state: dispover-handle
      map: [ "enum.DisposalUnitVisualLayers.OverlayEngaged" ]
    - map: [ "DoorVisualState.DoorOpen" ]
    - map: [ "SeatVisualState.SeatUp" ]
  - type: Rotatable
  - type: Transform
    noRot: false
  - type: Strap
    whitelist:
      components:
      - HumanoidAppearance
  - type: DisposalUnit
    autoEngageEnabled: false
    noUI: true
    blacklist:
      components:
      - HumanoidAppearance
      - Plunger
      - SolutionTransfer
    whitelist:
      components:
      - Item
    soundFlush: /Audio/Effects/Fluids/flush.ogg
    soundInsert: /Audio/Effects/Fluids/splash.ogg
  - type: Toilet
  - type: ContainerContainer
    containers:
      stash: !type:ContainerSlot {}
      disposals: !type:Container
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        hard: false
      # Goobstation - fishing
      fishing:
        shape:
          !type:PhysShapeCircle
          radius: 0.4
        layer:
        - ItemMask
        mask:
        - HighImpassable
        density: 1000
        hard: false
      # Goobstation - fishing
  - type: Construction
    graph: Toilet
    node: toilet
  - type: PlungerUse
  - type: Appearance
  - type: SecretStash
    secretPartName: secret-stash-part-toilet
    examineStash: toilet-component-on-examine-found-hidden-item
    openableStash: true
  - type: Drain
    autoDrain: false
  - type: StaticPrice
    price: 25
  - type: UserInterface
    interfaces:
      enum.DisposalUnitUiKey.Key:
        type: DisposalUnitBoundUserInterface
  - type: RatKingRummageable
  - type: SolutionContainerManager
    solutions:
      drainBuffer:
        maxVol: 100
      tank:
        maxVol: 500
  #- type: SolutionRegeneration - Goobstation - laggy, also who uses toilets?
  #  solution: tank
  #  generated:
  #    reagents:
  #    - ReagentId: Water
  #      Quantity: 1
  # Goobstation - fishing in toilet is fun
  - type: FishingSpot
    fishList: !type:NestedSelector
      tableId: ToiletFishingLootTable
    fishDefaultTimer: 45.0
    fishTimerVariety: 15.0
  # Goobstation - fishing
  - type: DrainableSolution
    solution: tank
  - type: ReagentTank
  - type: DumpableSolution
    solution: drainBuffer
  - type: GenericVisualizer
    visuals:
      enum.ToiletVisuals.SeatVisualState:
        SeatVisualState.SeatUp:
          SeatUp: { state: disposal-up }
          SeatDown: { state: disposal-down }
      enum.StashVisuals.DoorVisualState:
        DoorVisualState.DoorOpen:
          DoorOpen: { state: disposal-open }
          DoorClosed: { state: disposal-closed }

- type: entity
  id: ToiletDirtyWater
  parent: ToiletEmpty
  suffix: Dirty Water
  components:
  - type: SolutionContainerManager
    solutions:
      drainBuffer:
        maxVol: 100
      tank:
        maxVol: 500
        reagents:
        - ReagentId: Water
          Quantity: 180
        - ReagentId: GastroToxin
          Quantity: 20
