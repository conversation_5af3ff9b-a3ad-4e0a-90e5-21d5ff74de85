﻿# Actions
- type: entity
  id: ActionJumpToCore
  name: "Перейти до суті"
  description: "Повертає погляд до суті."
  components:
  - type: InstantAction
    priority: -10
    itemIconStyle: BigAction
    icon:
      sprite: Interface/Actions/actions_ai.rsi
      state: ai_core
    event: !type:JumpToCoreEvent

- type: entity
  id: ActionShowJobIcons
  name: "Показати іконки вакансій"
  description: "Показує іконки завдань для членів екіпажу."
  components:
  - type: InstantAction
    priority: -5
    itemIconStyle: BigAction
    icon:
      sprite: Interface/Actions/actions_ai.rsi
      state: job_view
    event: !type:ActionComponentChangeEvent
      components:
      - type: ShowJobIcons

- type: entity
  id: ActionSurvCameraLights
  name: "Увімкнути підсвічування камери"
  description: "Увімкніть підсвічування камери спостереження поруч з тим місцем, яке ви переглядаєте."
  components:
  - type: InstantAction
    priority: -6
    itemIconStyle: BigAction
    icon:
      sprite: Interface/Actions/actions_ai.rsi
      state: camera_light
    event: !type:RelayedActionComponentChangeEvent
      components:
      - type: LightOnCollideCollider
      - type: FixturesChange
        fixtures:
          lightTrigger:
            shape:
              !type:PhysShapeCircle
              radius: 0.35
            density: 80
            hard: false
            layer:
            - GhostImpassable


- type: entity
  id: ActionAIViewLaws
  name: "Переглянути закони"
  description: "Ознайомтеся з законами, яких ви повинні дотримуватися."
  components:
  - type: InstantAction
    priority: -3
    itemIconStyle: NoItem
    icon:
      sprite: Interface/Actions/actions_ai.rsi
      state: state_laws
    event: !type:ToggleLawsScreenEvent
    useDelay: 0.5
