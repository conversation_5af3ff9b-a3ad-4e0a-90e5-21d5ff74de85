- type: entity
  id: BaseMagazinePistol
  name: "пістолетний магазин (.35 auto)"
  parent: BaseItem
  abstract: true
  components:
  - type: Tag
    tags:
      - MagazinePistol
  - type: BallisticAmmoProvider
    mayTransfer: true
    whitelist:
      tags:
        - CartridgePistol
    capacity: 12
  - type: Item
    size: Small
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Magazine/Pistol/pistol_mag.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 5
    zeroVisible: false
  - type: Appearance

- type: entity
  id: BaseMagazinePistolHighCapacity
  name: "магазин для пістолета-кулемета (.35 auto)"
  parent: BaseItem
  abstract: true
  components:
  - type: Tag
    tags:
      - MagazinePistolHighCapacity
  - type: BallisticAmmoProvider
    mayTransfer: true
    whitelist:
      tags:
        - CartridgePistol
    capacity: 16
  - type: Item
    size: Small
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Magazine/Pistol/pistol_high_capacity_mag.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 6
    zeroVisible: false
  - type: Appearance

- type: entity
  id: BaseMagazinePistolSubMachineGun  # Yeah it's weird but it's pistol caliber
  name: "Магазин SMG (.35 auto)"
  parent: BaseItem
  abstract: true
  components:
  - type: Tag
    tags:
      - MagazinePistolSubMachineGun
  - type: BallisticAmmoProvider
    mayTransfer: true
    whitelist:
      tags:
        - CartridgePistol
    capacity: 35
  - type: Item
    size: Small
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Magazine/Pistol/smg_mag.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 6
    zeroVisible: false
  - type: Appearance

- type: entity
  id: MagazinePistolSubMachineGunTopMounted
  name: "Магазин WT550 (.35 auto top mounted)"
  parent: BaseItem
  components:
  - type: Tag
    tags:
      - MagazinePistolSubMachineGunTopMounted
  - type: BallisticAmmoProvider
    mayTransfer: true
    proto: CartridgePistol
    whitelist:
      tags:
      - CartridgePistol
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Magazine/Pistol/smg_mag_top_mounted.rsi
    layers:
      - state: base
        map: ["enum.GunVisualLayers.Base"]
      - state: mag-unshaded-1
        map: ["enum.GunVisualLayers.MagUnshaded"]
        shader: unshaded
  - type: MagazineVisuals
    magState: mag
    steps: 6
    zeroVisible: false
  - type: Appearance
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container

- type: entity
  id: MagazinePistolSubMachineGunTopMountedEmpty
  name: "магазин WT550 (.35 авто верхньої подачі будь-який)"
  parent: MagazinePistolSubMachineGunTopMounted
  components:
  - type: BallisticAmmoProvider
    proto: null

- type: entity
  id: MagazinePistol
  name: "пістолетний магазин (.35 auto)"
  parent: BaseMagazinePistol
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistol
  - type: Sprite
    layers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazinePistolEmpty
  name: "магазин до пістолета (.35 авто будь-який)"
  suffix: empty
  parent: MagazinePistol
  components:
  - type: BallisticAmmoProvider
    proto: null
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]


- type: entity
  id: MagazinePistolIncendiary
  name: "пістолетний магазин (.35 авто запальний)"
  parent: MagazinePistol
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistolIncendiary

- type: entity
  id: MagazinePistolPractice
  name: "магазин для пістолета (.35 auto practice)"
  parent: BaseMagazinePistol
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistolPractice
  - type: Sprite
    layers:
    - state: practice
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazinePistolRubber
  name: "пістолетний магазин (.35 авто гумовий)"
  parent: BaseMagazinePistol
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistolRubber
  - type: Sprite
    layers:
    - state: rubber
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazinePistolUranium
  name: "пістолетний магазин (.35 авто урановий)"
  parent: BaseMagazinePistol
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistolUranium
  - type: Sprite
    layers:
    - state: uranium
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazinePistolShrapnel
  name: "пістолетний магазин (.35 авто, шрапнельний)"
  parent: BaseMagazinePistol
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistolShrapnel
  - type: Sprite
    layers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazinePistolHighCapacityEmpty
  name: "магазин для автоматичного пістолета (.35 auto будь-який)"
  suffix: empty
  parent: BaseMagazinePistolHighCapacity
  components:
  - type: BallisticAmmoProvider
    proto: null
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]

- type: entity
  id: MagazinePistolHighCapacity
  name: "магазин для пістолета-кулемета (.35 auto)"
  parent: BaseMagazinePistolHighCapacity
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistol
  - type: Sprite
    layers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazinePistolHighCapacityPractice
  name: "магазин для пістолета-кулемета (.35 auto practice)"
  parent: BaseMagazinePistolHighCapacity
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistol
  - type: Sprite
    layers:
    - state: practice
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazinePistolHighCapacityRubber
  name: "магазин для автоматичного пістолета (.35 авто гумовий)"
  parent: BaseMagazinePistolHighCapacity
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistol
  - type: Sprite
    layers:
    - state: rubber
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazinePistolHighCapacityShrapnel
  name: "магазин для автоматичного пістолета (.35 авто, шрапнельний)"
  parent: BaseMagazinePistolHighCapacity
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistolShrapnel
  - type: Sprite
    layers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazinePistolSubMachineGun
  name: "Магазин SMG (.35 auto)"
  parent: BaseMagazinePistolSubMachineGun
  components:
  - type: BallisticAmmoProvider
    capacity: 30
    proto: CartridgePistol
    whitelist:
      tags:
        - CartridgePistol
    soundInsert:
      path: /Audio/Weapons/Guns/MagIn/bullet_insert.ogg
  - type: Sprite
    layers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazinePistolSubMachineGunEmpty
  name: "магазин для пістолета-кулемета (.35 auto будь-який)"
  suffix: empty
  parent: BaseMagazinePistolSubMachineGun
  components:
  - type: BallisticAmmoProvider
    proto: null
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]

- type: entity
  id: MagazinePistolSubMachineGunPractice
  name: "Магазин SMG (.35 автопрактика)"
  parent: BaseMagazinePistolSubMachineGun
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistolPractice
  - type: Sprite
    layers:
    - state: practice
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazinePistolSubMachineGunRubber
  name: "магазин для пістолета-кулемета (.35 авто гумовий)"
  parent: BaseMagazinePistolSubMachineGun
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistolRubber
  - type: Sprite
    layers:
    - state: rubber
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazinePistolSubMachineGunIncendiary
  name: "магазин для пістолета-кулемета (.35 авто запальний)"
  parent: BaseMagazinePistolSubMachineGun
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistolIncendiary
  - type: Sprite
    layers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazinePistolSubMachineGunUranium
  name: "Магазин SMG (.35 auto uranium)"
  parent: BaseMagazinePistolSubMachineGun
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistolUranium
  - type: Sprite
    layers:
    - state: uranium
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazinePistolSubMachineGunShrapnel
  name: "магазин для пістолета-кулемета (.35 авто, шрапнельний)"
  parent: BaseMagazinePistolSubMachineGun
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistolShrapnel
