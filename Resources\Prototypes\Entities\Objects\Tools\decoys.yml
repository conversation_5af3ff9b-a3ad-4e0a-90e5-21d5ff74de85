
# Operative Balloons, looks just like the real thing!

- type: entity
  parent: [BaseItem, BaseFoldable]
  id: BaseDecoy
  abstract: true
  components:
  - type: Rotatable
  - type: Sprite
    sprite: Objects/Tools/Decoys/operative_decoy.rsi
    layers:
    - state: oballoon
      map: ["unfoldedLayer"]
    - state: folded
      map: ["foldedLayer"]
      visible: false
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
        - !type:PlaySoundBehavior
          sound:
            path: /Audio/Effects/pop_high.ogg
  - type: Appearance
  - type: Item
    size: Normal
  - type: RandomMetadata # No metagaming these, jimbo.
    nameSegments:
    - SyndicateNamesPrefix
    - SyndicateNamesNormal
  - type: Damageable
    damageContainer: Inorganic
  - type: ToggleableLightVisuals
  - type: Foldable
    folded: true
  - type: PowerCellSlot
    cellSlotId: cell_slot
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellHigh
  - type: ContainerContainer
    containers:
      cell_slot: !type:ContainerSlot
  - type: HandheldLight
    addPrefix: false
  - type: PointLight # Just like the real thing...
    autoRot: true
    radius: 1.5
    energy: 2.3
    offset: 0, -1
    color: green
    netsync: false
  - type: Tag
    tags:
    - Balloon
    - Flashlight

- type: entity
  parent: BaseDecoy
  id: BalloonOperative
  name: "оперативна повітряна куля"
  description: "При ближчому розгляді цей оперативник Синдикату насправді є повітряною кулькою."
  components:
  - type: Sprite
    sprite: Objects/Tools/Decoys/operative_decoy.rsi
    layers:
    - state: oballoon
      map: ["unfoldedLayer"]
    - state: folded
      map: ["foldedLayer"]
      visible: false

- type: entity
  parent: BaseDecoy
  id: BalloonAgent
  name: "повітряна куля з агентом"
  description: "При ближчому розгляді цей агент Синдикату насправді є повітряною кулею."
  components:
  - type: Sprite
    sprite: Objects/Tools/Decoys/agent_decoy.rsi
    layers:
    - state: aballoon
      map: ["unfoldedLayer"]
    - state: folded
      map: ["foldedLayer"]
      visible: false

- type: entity
  parent: BaseDecoy
  id: BalloonElite
  name: "елітна оперативна повітряна куля"
  description: "При ближчому розгляді цей елітний оперативник Синдикату виявляється насправді повітряною кулькою."
  components:
  - type: Sprite
    sprite: Objects/Tools/Decoys/elite_decoy.rsi
    layers:
    - state: eballoon
      map: ["unfoldedLayer"]
    - state: folded
      map: ["foldedLayer"]
      visible: false
  - type: PointLight
    color: red

- type: entity
  parent: BaseDecoy
  id: BalloonJuggernaut
  name: "повітряна куля-джаггернаут"
  description: "При ближчому розгляді, цей джаггернаут Синдикату насправді є повітряною кулею."
  components:
  - type: Sprite
    sprite: Objects/Tools/Decoys/juggernaut_decoy.rsi
    layers:
    - state: jballoon
      map: ["unfoldedLayer"]
    - state: folded
      map: ["foldedLayer"]
      visible: false

- type: entity
  parent: BaseDecoy
  id: BalloonCommander
  name: "командирська куля"
  description: "При ближчому розгляді цей командир Синдикату насправді є повітряною кулькою."
  components:
  - type: Sprite
    sprite: Objects/Tools/Decoys/commander_decoy.rsi
    layers:
    - state: cballoon
      map: ["unfoldedLayer"]
    - state: folded
      map: ["foldedLayer"]
      visible: false
