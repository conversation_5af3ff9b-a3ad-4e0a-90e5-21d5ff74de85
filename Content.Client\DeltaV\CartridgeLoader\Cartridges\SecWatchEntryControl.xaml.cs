using Content.Shared.CartridgeLoader.Cartridges;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.DeltaV.CartridgeLoader.Cartridges;

[GenerateTypedNameReferences]
public sealed partial class SecWatchEntryControl : BoxContainer
{
    public SecWatchEntryControl(SecWatchEntry entry)
    {
        RobustXamlLoader.Load(this);

        Status.Text = Loc.GetString($"criminal-records-status-{entry.Status.ToString().ToLower()}");
        Title.Text = Loc.GetString("sec-watch-entry", ("name", entry.Name), ("job", entry.Job));

        Reason.Text = entry.Reason ?? Loc.GetString("sec-watch-no-reason");
    }
}
