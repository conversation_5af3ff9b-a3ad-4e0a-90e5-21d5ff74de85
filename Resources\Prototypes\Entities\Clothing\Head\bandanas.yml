- type: entity
  parent: [ClothingHeadBaseButcherable, BaseFoldable]
  id: ClothingHeadBandBase
  abstract: true
  components:
  - type: Foldable
    folded: true
  - type: Mask
    isToggled: true
  - type: IngestionBlocker
    enabled: false
  - type: IdentityBlocker
    enabled: false
    coverage: MOUTH
  - type: Sprite # needed for vendor inventory icons
    layers:
    - state: icon
      map: ["foldedLayer"]
      visible: true
    - state: icon_mask
      map: [ "unfoldedLayer" ]
      visible: false
  - type: Tag
    tags:
    - Bandana

- type: entity
  parent: [ClothingHeadBandBase, ClothingMaskBandWhite]
  id: ClothingHeadBandWhite
  name: "бандана"

- type: entity
  parent: [ClothingHeadBandBase, ClothingMaskBandBlack]
  id: ClothingHeadBandBlack
  name: "чорна бандана"

- type: entity
  parent: [ClothingHeadBandBase, ClothingMaskBandBlue]
  id: ClothingHeadBandBlue
  name: "синя бандана"

- type: entity
  parent: [ClothingHeadBandBase, ClothingMaskBandBotany]
  id: ClothingHeadBandBotany
  name: "бо<PERSON><PERSON><PERSON><PERSON><PERSON>на бандана"

- type: entity
  parent: [<PERSON>lot<PERSON><PERSON><PERSON>BandB<PERSON>, ClothingMaskBandGold]
  id: ClothingHeadBandGold
  name: "золота бандана"

- type: entity
  parent: [ClothingHeadBandBase, ClothingMaskBandGreen]
  id: ClothingHeadBandGreen
  name: "зелена бандана"

- type: entity
  parent: [ClothingHeadBandBase, ClothingMaskBandGrey]
  id: ClothingHeadBandGrey
  name: "сіра бандана"

- type: entity
  parent: [ClothingHeadBandBase, ClothingMaskBandRed]
  id: ClothingHeadBandRed
  name: "червона бандана"

- type: entity
  parent: [ClothingHeadBandBase, ClothingMaskBandSkull]
  id: ClothingHeadBandSkull
  name: "бандана з черепом"

- type: entity
  parent: [ClothingHeadBandBase, ClothingMaskBandMerc]
  id: ClothingHeadBandMerc
  name: "бандана найманця"

- type: entity
  parent: [ClothingHeadBandBase, ClothingMaskBandBrown]
  id: ClothingHeadBandBrown
  name: "коричнева бандана"
