- type: department
  id: Logistics # DeltaV - Logistics Department replacing Cargo
  description: department-Cargo-description
  color: "#A46106"
  roles:
  - CargoTechnician
  - Quartermaster
  - SalvageSpecialist
  - MailCarrier # Nyanotrasen - MailCarrier, see Resources/Prototypes/Nyanotrasen/Roles/Jobs/Cargo/mail-carrier.yml

- type: department
  id: Civilian
  description: department-Civilian-description
  color: "#9FED58"
  weight: -10
  roles:
  - Bart<PERSON>
  - Botanist
  - Boxer
  # - Chaplain # DeltaV - Move Chaplain into Epistemics
  - Chef
  - Clown
  - HeadOfPersonnel
  - Janitor
  # - Lawyer # DeltaV - Move Lawyer into Justice
  - Mime
  - Musician
  - Passenger
  - Reporter
  - Visitor
  - Zookeeper
  - ServiceWorker
  - MartialArtist # Nyanotrasen - MartialArtist, see Resources/Prototypes/Nyanotrasen/Roles/Jobs/Wildcards/martialartist.yml
  - Prisoner # Nyanotrasen - Prisoner, see Resources/Prototypes/Nyanotrasen/Roles/Jobs/Wildcards/prisoner.yml
  - Gladiator # Nyanotrasen - Gladiator, see Resources/Prototypes/Nyanotrasen/Roles/Jobs/Wildcards/gladiator.yml

- type: department
  id: Command
  description: department-Command-description
  color: "#334E6D"
  roles:
  - Captain
  - CentralCommandOfficial
  - ChiefE<PERSON>ineer
  - ChiefMedicalOfficer
  - HeadOfPersonnel
  - HeadOfSecurity
  - ResearchDirector
  - Quartermaster
  - ChiefJustice # DeltaV - chief justice is in command staff
  - CentralCommandOfficial
  - CBURN
  - ERTLeader
  - ERTChaplain
  - ERTJanitor
  - ERTMedical
  - ERTSecurity
  - ERTEngineer
  - DeathSquad
  - AdministrativeAssistant # Delta V - Administrative Assistant, see Resources/Prototypes/Nyanotrasen/Roles/Jobs/Command/admin_assistant.yml
  primary: false
  weight: 100

- type: department
  id: Dignitary
  description: department-Dignitary-description
  color: "#33FE6D"
  roles:
  - BlueshieldOfficer # Goobstation
  - NanotrasenRepresentative # Goobstation
  - Magistrate # Goobstation
  primary: false
  weight: 101

- type: department
  id: Engineering
  description: department-Engineering-description
  color: "#EFB341"
  roles:
  - AtmosphericTechnician
  - ChiefEngineer
  - SeniorEngineer
  - StationEngineer
  - TechnicalAssistant

- type: department
  id: Medical
  description: department-Medical-description
  color: "#52B4E9"
  roles:
  - Chemist
  - ChiefMedicalOfficer
  - MedicalDoctor
  - MedicalIntern
  - Psychologist
  - Paramedic
  - SeniorPhysician
  - MedicalBorg  # Delta V - Medical Borg, see Resources/Prototypes/DeltaV/Roles/Jobs/Medical/medicalborg.yml

- type: department
  id: Security
  description: department-Security-description
  color: "#DE3A3A"
  weight: 20
  roles:
  - HeadOfSecurity
  - SecurityCadet
  - SecurityOfficer
  - SeniorOfficer
  - Detective
  - Warden
  - PrisonGuard # Nyanotrasen - PrisonGuard, see Resources/Prototypes/Nyanotrasen/Roles/Jobs/Security/prisonguard.yml
  - Brigmedic # DeltaV - Brigmedic, see Resources/Prototypes/DeltaV/Roles/Jobs/Security/brigmedic.yml

- type: department
  id: Epistemics # DeltaV - Epistemics Department replacing Science
  description: department-Science-description
  color: "#D381C9"
  roles:
  - ResearchDirector
  - SeniorResearcher
  - Scientist
  - ResearchAssistant
  - Chaplain # DeltaV - Move Chaplain into Epistemics
  - ForensicMantis # Nyanotrasen - ForensicMantis, see Resources/Prototypes/Nyanotrasen/Roles/Jobs/Epistemics/forensicmantis.yml
  - Librarian
  - Roboticist

- type: department
  id: Silicon
  description: department-Silicon-description
  color: "#D381C9"
  roles:
  - Borg
  - StationAi

- type: department
  id: Specific
  description: department-Specific-description
  color: "#9FED58"
  weight: 10
  roles:
  - Boxer
  - Reporter
  - Zookeeper
  - Psychologist
  - MartialArtist # Nyanotrasen - MartialArtist, see Resources/Prototypes/Nyanotrasen/Roles/Jobs/Wildcards/martialartist.yml
  - Gladiator # Nyanotrasen - Gladiator, see Resources/Prototypes/Nyanotrasen/Roles/Jobs/Wildcards/gladiator.yml
  - Prisoner # Nyanotrasen - Prisoner, see Resrouces/Prototypes/Nyanotrasen/Roles/Jobs/Wildcards/prisoner.yml
  - Brigmedic # DeltaV - Corpsman, see Resources/Prototypes/DeltaV/Roles/Jobs/Security/brigmedic.yml
  primary: false
