﻿<ui:VoteCallMenu xmlns="https://spacestation14.io"
                 xmlns:ui="clr-namespace:Content.Client.Voting.UI"
                 xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                 MouseFilter="Stop" MinSize="350 150">
    <PanelContainer StyleClasses="AngleRect" />
    <BoxContainer Orientation="Vertical">
        <BoxContainer Margin="8 0" Orientation="Horizontal">
            <Label Text="{Loc 'ui-vote-create-title'}"
                   HorizontalExpand="True" VAlign="Center" StyleClasses="LabelHeading" />
            <TextureButton Name="CloseButton" StyleClasses="windowCloseButton"
                           VerticalAlignment="Center" />
        </BoxContainer>
        <controls:HighDivider />

        <BoxContainer Orientation="Vertical" Margin="8 2 8 0" VerticalExpand="True" VerticalAlignment="Top">
            <BoxContainer Orientation="Horizontal">
                <OptionButton Name="VoteTypeButton" HorizontalExpand="True" />
                <Control HorizontalExpand="True">
                    <OptionButton Name="VoteSecondButton" Visible="False" />
                </Control>
            </BoxContainer>
            <Label Name="VoteTypeTimeoutLabel" Visible="False" />
        </BoxContainer>

        <Button Margin="8 2" Name="CreateButton" Text="{Loc 'ui-vote-create-button'}" />

        <PanelContainer StyleClasses="LowDivider" />
        <Label Margin="12 0 0 0" StyleClasses="LabelSubText" Text="{Loc 'ui-vote-fluff'}" />
    </BoxContainer>
</ui:VoteCallMenu>
