- type: entity
  parent: BaseItem
  id: ShuttleCurse
  name: "проклята куля"
  description: "Ви вдивляєтесь у цю димчасту кулю і бачите жахливі долі, що спіткали шатл аварійної евакуації."
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/Entities/Items/shuttle_curse.rsi
    state: icon
  - type: CultItem
  - type: ShuttleCurse

- type: entity
  parent: BaseItem
  id: WhetstoneCult
  name: "жахливе точило"
  description: "Блок, посилений темною магією. Гостра зброя посилюється при використанні на камені."
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/Entities/Items/whetstone_cult.rsi
    layers:
    - state: icon
      map: [ "enum.GenericCultVisuals.Layer" ]
  - type: CultItem
  - type: Whetstone
    whitelist:
      components:
      - Sharp
    blacklist:
      components:
      - EnergySword
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.GenericCultVisuals.State:
        enum.GenericCultVisuals.Layer:
          True: { state: icon }
          False: { state: icon_off }

- type: entity
  parent: BaseItem
  id: VeilShifter
  name: "зрушувач завіси"
  description: "Ця реліквія миттєво телепортує вас і все, що ви тягнете, вперед на помірну відстань."
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/Entities/Items/veil_shifter.rsi
    layers:
    - state: icon
      map: [ "enum.GenericCultVisuals.Layer" ]
  - type: CultItem
  - type: VeilShifter
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.GenericCultVisuals.State:
        enum.GenericCultVisuals.Layer:
          True: { state: icon }
          False: { state: icon_off }

- type: entity
  parent: BaseItem
  id: VoidTorch
  name: "смолоскип порожнечі"
  description: "Використовується ветеранами-культистами для миттєвої передачі предметів своїм нужденним побратимам"
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/Entities/Items/void_torch.rsi
    layers:
    - state: icon
      map: [ "enum.GenericCultVisuals.Layer" ]
  - type: CultItem
  - type: VoidTorch
  - type: IgnitionSource
    temperature: 400
    ignited: false
  - type: PointLight
    enabled: false
    color: "#e33119"
    radius: 1.0
    energy: 5.0
    netsync: false
  - type: LightBehaviour
    behaviours:
    - !type:RandomizeBehaviour # immediately make it bright and flickery
      id: turn_on
      interpolate: Nearest
      minDuration: 0.02
      maxDuration: 0.06
      startValue: 6.0
      endValue: 9.0
      property: Energy
      isLooped: true
    - !type:FadeBehaviour # have the radius start small and get larger as it starts to burn
      id: turn_on
      maxDuration: 8.0
      startValue: 1.0
      endValue: 6.0
    - !type:RandomizeBehaviour # weaker flicker as it fades out
      id: fade_out
      interpolate: Nearest
      minDuration: 0.02
      maxDuration: 0.06
      startValue: 4.0
      endValue: 8.0
      property: Energy
      isLooped: true
    - !type:FadeBehaviour # fade out radius as it burns out
      id: fade_out
      maxDuration: 4.0
      startValue: 6.0
      endValue: 1.0
  - type: UserInterface
    interfaces:
      enum.ListViewSelectorUiKey.Key:
        type: ListViewSelectorBUI
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.GenericCultVisuals.State:
        enum.GenericCultVisuals.Layer:
          True: { state: icon }
          False: { state: icon_off }
  - type: Tag
    tags:
    - Torch

- type: entity
  parent: BaseItem
  id: ShadowShackles
  name: "тіньові кайдани"
  description: "Кайдани, що зловісною магією сковують зап'ястя."
  components:
  - type: Item
    size: Small
    storedRotation: 90
  - type: Handcuff
    breakoutTime: 5
    breakOnRemove: true
    cuffTime: 2
    cuffedRSI: Objects/Misc/cablecuffs.rsi
    bodyIconState: body-overlay
    color: black
  - type: Sprite
    sprite: WhiteDream/BloodCult/actions.rsi
    state: cuff
  - type: Tag
    tags:
    - Handcuffs
  - type: UseDelay
    delay: 6

- type: entity
  parent: BaseItem
  id: MirrorShieldCult
  name: "дзеркальний щит"
  description: "Сумнозвісний щит, який використовували секти Нар'Сі, щоб збити з пантелику та дезорієнтувати своїх ворогів. Його краї обтяжені, щоб використовувати як метальну зброю здатну вивести з ладу кількох ворогів з надприродною точністю."
  components:
  - type: Sprite
    sprite: WhiteDream/BloodCult/Entities/Items/mirror_shield.rsi
    state: icon
  - type: Item
    size: Ginormous
  - type: CultItem
  - type: StunOnCollide
    blacklist:
      components:
      - BloodCultist
  - type: Reflect
    reflectProb: 0.75
    innate: true
    reflects:
    - Energy
  - type: Blocking
    passiveBlockModifier:
      coefficients:
        Blunt: 0.8
        Slash: 0.8
        Piercing: 0.8
        Heat: 0.8
    activeBlockModifier:
      coefficients:
        Blunt: 0.8
        Slash: 0.8
        Piercing: 0.8
        Heat: 0.8
      flatReductions:
        Blunt: 1
        Slash: 1
        Piercing: 1
        Heat: 1
    blockSound: !type:SoundPathSpecifier
      path: /Audio/Effects/glass_step.ogg
  - type: Damageable
    damageContainer: Shield
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
