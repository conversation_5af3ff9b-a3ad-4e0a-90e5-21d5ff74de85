- type: entity
  id: DiskBurner
  parent: [ BaseMachinePowered, ConstructibleMachine ]
  name: "дисков<PERSON> пальник"
  description: "Отримує на вході комп'ютерну плату та диск. Записує програму комп'ютерної плати на диск."
  components:
  - type: Sprite
    sprite: Structures/Machines/circuit_imprinter.rsi
    snapCardinals: true
    layers:
    - state: icon
      map: ["enum.LatheVisualLayers.IsRunning"]
    - state: unlit
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
  - type: Machine
    board: DiskBurnerMachineCircuitboard
  - type: ItemSlots
    slots:
      diskSlot:
        name: Disk
        insertSound:
          path: /Audio/Machines/terminal_insert_disc.ogg
        ejectSound:
          path: /Audio/Machines/terminal_insert_disc.ogg
        whitelist:
          tags:
            - ComputerDisk
      boardSlot:
        name: Board
        whitelist:
          tags:
            - ComputerBoard
  - type: DiskBurner
  - type: ContainerContainer
    containers:
      machine_board: !type:Container
        showEnts: False
        occludes: True
        ents: []
      machine_parts: !type:Container
        showEnts: False
        occludes: True
        ents: []
      diskSlot: !type:ContainerSlot
        showEnts: False
        occludes: True
        ent: null
      boardSlot: !type:ContainerSlot
        showEnts: False
        occludes: True
        ent: null

- type: entity
  id: DiskBurnerMachineCircuitboard
  parent: BaseMachineCircuitboard
  name: "плата машини для запису дисків"
  components:
  - type: Sprite
    state: science
  - type: MachineBoard
    prototype: DiskBurner
    requirements:
      Capacitor: 2
      Manipulator: 2

- type: latheRecipe
  id: DiskBurnerMachineCircuitboard
  result: DiskBurnerMachineCircuitboard
  category: Circuitry
  completetime: 4
  materials:
     Steel: 100
     Glass: 700
     Gold: 100
