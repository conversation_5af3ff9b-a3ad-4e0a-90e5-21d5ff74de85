- type: entity
  abstract: true
  parent: BaseMagazineBox
  id: BaseMagazineBoxPistol
  name: "коробочка з набоями (.35 auto)"
  components:
  - type: BallisticAmmoProvider
    mayTransfer: true
    whitelist:
      tags:
        - CartridgePistol
    proto: CartridgePistol
    capacity: 60
  - type: Item
    size: Small
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Boxes/pistol.rsi
  - type: MagazineVisuals
    magState: mag
    steps: 2
    zeroVisible: false
  - type: Appearance

# Boxes
- type: entity
  parent: BaseMagazineBoxPistol
  id: MagazineBoxPistol
  name: "коробочка з набоями (.35 auto)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistol
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  parent: BaseMagazineBoxPistol
  id: MagazineBoxPistolPractice
  name: "коробочка з набоями (.35 auto practice)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistolPractice
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: practice

- type: entity
  parent: BaseMagazineBoxPistol
  id: MagazineBoxPistolRubber
  name: "коробка боєприпасів (.35 авто гумовий)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistolRubber
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: rubber

- type: entity
  id: MagazineBoxPistolIncendiary
  parent: BaseMagazineBoxPistol
  name: "коробка боєприпасів (.35 авто запальний)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistolIncendiary
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: incendiary

- type: entity
  id: MagazineBoxPistolUranium
  parent: BaseMagazineBoxPistol
  name: "коробочка з набоями (.35 auto uranium)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistolUranium
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: uranium

- type: entity
  parent: BaseMagazineBoxPistol
  id: MagazineBoxPistolShrapnel
  name: "коробка боєприпасів (.35 авто, шрапнельний)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgePistolShrapnel
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
