cmd-dungen-desc = Generates a procedural dungeon with the specified preset, position, and seed. Will spawn in space if the MapId doesn't have MapGridComponent.
cmd-dungen-help = dungen <MapId> <DungeonPreset> <PosX> <PosY> [seed]
cmd-dungen-arg-count = Require 4 args.
cmd-dungen-map-parse = Unable to parse MapId.
cmd-dungen-mapgrid = Unable to find MapGrid.
cmd-dungen-config = Unable to find dungeon config.
cmd-dungen-pos = Unable to parse position.
cmd-dungen-seed = Unable to parse seed.
cmd-dungen-start = Generating dungeon with seed {$seed}

cmd-dungen-hint-map = Map Id
cmd-dungen-hint-config = Dungeon config
cmd-dungen-hint-posx = Position X
cmd-dungen-hint-posy = Position Y
cmd-dungen-hint-seed = [Seed]

cmd-dungen_preset_vis-desc = Generates a tile-based preview of a dungeon preset.
cmd-dungen_preset_vis-help = dungen_preset_vis <mapid> <preset>

cmd-dungen_pack_vis-success = Success
cmd-dungen_pack_vis-desc = Generates a tile-based preview of a dungeon pack.
cmd-dungen_pack_vis-help = dungen_pack_vis <mapid> <pack>
