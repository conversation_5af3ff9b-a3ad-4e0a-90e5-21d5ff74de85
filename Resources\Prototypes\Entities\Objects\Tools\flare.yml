﻿- type: entity
  name: "авар<PERSON>йний факел"
  parent: BaseItem
  id: Flare
  description: "Факел, який виробляє дуже яскраве світло на короткий час. Спрямуйте полум'я від себе."
  components:
  - type: Tag
    tags:
    - Flare
    - Trash
    - Cartridge
    - ShellShotgunLight
  - type: SpaceGarbage
  - type: ExpendableLight
    spentName: expendable-light-spent-flare-name
    spentDesc: expendable-light-spent-flare-desc
    glowDuration: 225
    fadeOutDuration: 15
    iconStateOn: flare_unlit
    iconStateSpent: flare_spent
    turnOnBehaviourID: turn_on
    fadeOutBehaviourID: fade_out
    litSound:
      path: /Audio/Items/Flare/flare_on.ogg
    loopedSound:
      path: /Audio/Items/Flare/flare_burn.ogg
      params:
        loop: true
        volume: -10
        maxDistance: 5
  - type: CartridgeAmmo
    proto: PelletShotgunFlare
    deleteOnSpawn: true


  - type: Sprite
    sprite: Objects/Misc/flare.rsi
    layers:
      - map: [ enum.ExpendableLightVisualLayers.Base ]
        state: flare_base
      - map: [ enum.ExpendableLightVisualLayers.Glow ]
        state: flare_burn
        color: "#FFFFFF"
        visible: false
        shader: unshaded
      - map: [ enum.ExpendableLightVisualLayers.Overlay ]
        state: flare_unlit
        color: "#FF0000"
  - type: Icon
    sprite: Objects/Misc/flare.rsi
    state: icon
  - type: Item
    sprite: Objects/Misc/flare.rsi
    heldPrefix: unlit
    size: Tiny
  - type: Appearance
  - type: PointLight
    enabled: false
    color: "#FF8080"
    radius: 1.0
    energy: 9.0
    netsync: false
  - type: IgnitionSource
    temperature: 1000
  - type: LightBehaviour
    behaviours:
      - !type:FadeBehaviour # have the radius start small and get larger as it starts to burn
        id: turn_on
        maxDuration: 45.0
        startValue: 2.5
        endValue: 10.0
      - !type:RandomizeBehaviour # weaker flicker as it fades out
        id: fade_out
        interpolate: Nearest
        minDuration: 0.001
        maxDuration: 0.001
        startValue: 4.0
        endValue: 8.0
        property: Energy
        isLooped: true
      - !type:FadeBehaviour # fade out radius as it burns out
        id: fade_out
        maxDuration: 15.0
        startValue: 10.0
        endValue: 1.0
  - type: Extractable
    grindableSolutionName: flare
  - type: SolutionContainerManager
    solutions:
      flare:
        maxVol: 20
        reagents:
        # flare casing
        - ReagentId: Iron
          Quantity: 10
        # red phosphorus = red flare trust
        - ReagentId: Phosphorus
          Quantity: 3
        - ReagentId: Carbon
          Quantity: 1
        - ReagentId: Oxygen
          Quantity: 2
        # fuel or something
        - ReagentId: Charcoal
          Quantity: 2
        - ReagentId: Sulfur
          Quantity: 2
