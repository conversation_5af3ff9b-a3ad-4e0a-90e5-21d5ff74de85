- type: entity
  abstract: true
  id: DisposalUnitBase
  parent: BaseMachinePowered
  description: "Пневматична установка для утилізації відходів."
  placement:
    mode: SnapgridCenter
    snap:
    - Disposal
  components:
  - type: Sprite
    sprite: Structures/Piping/disposal.rsi
    layers:
    - state: condisposal
      map: [ "enum.DisposalUnitVisualLayers.Unanchored" ]
    - state: disposal
      map: [ "enum.DisposalUnitVisualLayers.Base" ]
    - state: disposal-charging
      map: [ "enum.DisposalUnitVisualLayers.BaseCharging" ]
    - state: disposal-flush
      map: [ "enum.DisposalUnitVisualLayers.OverlayFlush" ]
    - state: dispover-charge
      map: [ "enum.DisposalUnitVisualLayers.OverlayCharging" ]
    - state: dispover-ready
      map: [ "enum.DisposalUnitVisualLayers.OverlayReady" ]
    - state: dispover-full
      map: [ "enum.DisposalUnitVisualLayers.OverlayFull" ]
    - state: dispover-handle
      map: [ "enum.DisposalUnitVisualLayers.OverlayEngaged" ]
  - type: Physics
    bodyType: Static
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
          params:
            volume: -6
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 1
  - type: Appearance
  - type: UserInterface
    interfaces:
      enum.DisposalUnitUiKey.Key:
        type: DisposalUnitBoundUserInterface
  - type: ContainerContainer
    containers:
      disposals: !type:Container
  - type: StaticPrice
    price: 62
  - type: PowerSwitch


- type: entity
  id: DisposalUnit
  parent: DisposalUnitBase
  name: "утилізаційна установка"
  components:
  - type: Sprite
    sprite: Structures/Piping/disposal.rsi
    snapCardinals: true
  - type: Construction
    graph: DisposalMachine
    node: disposal_unit
  - type: DisposalUnit
    blacklist:
      components:
      - SegmentedEntity
  - type: ThrowInsertContainer
    containerId: disposals
  - type: UserInterface
    interfaces:
      enum.DisposalUnitUiKey.Key:
        type: DisposalUnitBoundUserInterface
  - type: RatKingRummageable
  - type: RequireProjectileTarget
  - type: DeviceLinkSink
    ports:
      - FillbotAnyItem

- type: entity
  id: MailingUnit
  parent: DisposalUnitBase
  name: "поштовий блок"
  description: "Пневматичний пристрій для доставки пошти."
  components:
  - type: Sprite
    sprite: Structures/Piping/disposal.rsi
    snapCardinals: true
    layers:
      - state: conmailing
        map: [ "enum.DisposalUnitVisualLayers.Unanchored" ]
      - state: mailing
        map: [ "enum.DisposalUnitVisualLayers.Base" ]
      - state: mailing-charging
        map: [ "enum.DisposalUnitVisualLayers.BaseCharging" ]
      - state: mailing-flush
        map: [ "enum.DisposalUnitVisualLayers.OverlayFlush" ]
      - state: dispover-charge
        map: [ "enum.DisposalUnitVisualLayers.OverlayCharging" ]
      - state: dispover-ready
        map: [ "enum.DisposalUnitVisualLayers.OverlayReady" ]
      - state: dispover-full
        map: [ "enum.DisposalUnitVisualLayers.OverlayFull" ]
      - state: mailover-handle
        map: [ "enum.DisposalUnitVisualLayers.OverlayEngaged" ]
  - type: Construction
    graph: DisposalMachine
    node: mailing_unit
  - type: DisposalUnit
    autoEngageEnabled: false
    whitelist:
      components:
      - Item
  - type: MailingUnit
  - type: DeviceNetwork
    deviceNetId: Wired
    receiveFrequencyId: MailingUnit
    transmitFrequencyId: MailingUnit
  - type: WiredNetworkConnection
  - type: Configuration
    config:
      tag:
  - type: Appearance
  - type: UserInterface
    interfaces:
      enum.MailingUnitUiKey.Key:
        type: DisposalUnitBoundUserInterface
      enum.ConfigurationUiKey.Key:
        type: ConfigurationBoundUserInterface
  - type: DeviceLinkSink
    ports:
      - FillbotAnyItem
