﻿<Control xmlns="https://spacestation14.io"
         xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
         xmlns:xNamespace="http://schemas.microsoft.com/winfx/2006/xaml"
         xmlns:s="clr-namespace:Content.Client.Stylesheets">
    <BoxContainer Orientation="Vertical">
        <BoxContainer Orientation="Horizontal">
            <Label Text="{Loc 'ui-options-binds-search'}"
                   Margin="5 0 5 0"
                   FontColorOverride="{xNamespace:Static s:StyleNano.NanoGold}"
                   />
            <LineEdit Name="SearchInput"  HorizontalExpand="True" SizeFlagsStretchRatio="2"/>
        </BoxContainer>
        <ScrollContainer VerticalExpand="True">
            <BoxContainer Name="KeybindsContainer" Orientation="Vertical" Margin="8 8 8 8">

                <!-- Keybind buttons are added here in the C# code -->

            </BoxContainer>
        </ScrollContainer>
        <controls:StripeBack HasBottomEdge="False" HasMargins="False">
            <BoxContainer Orientation="Horizontal" Margin="8 8">
                <Control MinSize="8 0" />
                <Label Text="{Loc 'ui-options-binds-explanation'}" StyleClasses="LabelSubText" />
                <Button Name="ResetAllButton"
                        Text="{Loc 'ui-options-binds-reset-all'}"
                        HorizontalExpand="True"
                        HorizontalAlignment="Right" />
            </BoxContainer>
        </controls:StripeBack>
    </BoxContainer>
</Control>
