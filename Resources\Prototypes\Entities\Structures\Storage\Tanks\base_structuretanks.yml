﻿- type: entity
  id: StorageTank
  parent: BaseStructureDynamic
  name: "резервуар для зберігання"
  description: "Резервуар для зберігання рідин."
  abstract: true
  components:
  - type: Sprite
    noRot: true
  - type: InteractionOutline
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.4,0.4,0.4"
        density: 155
        mask:
        - MachineMask
        layer:
        - WallLayer
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTypeTrigger
        damageType: Heat
        damage: 5
      behaviors:
      - !type:SolutionExplosionBehavior
        solution: tank
    - trigger:
        !type:DamageTypeTrigger
        damageType: Piercing
        damage: 5
      behaviors:
      - !type:SolutionExplosionBehavior
        solution: tank
    - trigger:
        !type:DamageTrigger
        damage: 10
      behaviors:
      - !type:SpillBehavior
        solution: tank
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: SolutionContainerManager
    solutions:
      tank:
        maxVol: 1500
  - type: DrainableSolution
    solution: tank
  - type: ReagentTank
  - type: Transform
    noRot: true

# For highcap tanks
- type: entity
  id: StorageTankBig
  parent: StorageTank
  abstract: true
  components:
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.4,0.4,0.4"
        density: 455 #very heavy, they store 10k units of reagents after all.
        mask:
        - MachineMask
        layer:
        - WallLayer
