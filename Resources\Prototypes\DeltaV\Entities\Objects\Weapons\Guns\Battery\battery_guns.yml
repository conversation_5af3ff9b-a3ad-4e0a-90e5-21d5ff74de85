- type: entity
  name: "енергетична гармата"
  parent: BaseWeaponBattery
  id: WeaponEnergyGun
  description: "Базова гібридна енергетична гармата з двома налаштуваннями: вивести з ладу та вбити."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Battery/energygun.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mode-disabler
      shader: unshaded
      map: [ "Firemode" ]
    - state: mag-unshaded-4
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: Clothing
    sprite: DeltaV/Objects/Weapons/Guns/Battery/energygun.rsi
  - type: Gun
    soundGunshot:
      path: /Audio/DeltaV/Weapons/Guns/Gunshots/laser.ogg
    soundEmpty:
      path: /Audio/DeltaV/Weapons/Guns/Empty/dry_fire.ogg
  - type: Battery
    maxCharge: 1000
    startingCharge: 1000
  - type: ProjectileBatteryAmmoProvider
    proto: BulletDisabler
    fireCost: 50
  - type: EnergyGun
    fireModes:
    - proto: BulletDisabler
      fireCost: 50
      name: disable
      state: disabler
    - proto: BulletEnergyGunLaser
      fireCost: 100
      name: lethal
      state: lethal
  - type: MagazineVisuals
    magState: mag
    steps: 5
    zeroVisible: true
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.EnergyGunFireModeVisuals.State:
        Firemode:
          Disabler: { state: mode-disabler }
          Lethal: { state: mode-lethal }
          Special: { state: mode-stun } # Unused

- type: entity
  name: "x-01 багатофазна енергетична гармата"
  parent: BaseWeaponBatterySmall
  id: WeaponEnergyGunMultiphase
  description: "Це дороге сучасне відтворення старовинної лазерної гармати. Ця зброя має кілька унікальних режимів вогню, але не має можливості перезаряджатися з часом."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Battery/multiphase_energygun.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mode-disabler
      shader: unshaded
      map: [ "Firemode" ]
    - state: mag-unshaded-4
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: Clothing
    sprite: DeltaV/Objects/Weapons/Guns/Battery/multiphase_energygun.rsi
  - type: Gun
    soundGunshot:
      path: /Audio/DeltaV/Weapons/Guns/Gunshots/laser.ogg
    soundEmpty:
      path: /Audio/DeltaV/Weapons/Guns/Empty/dry_fire.ogg
  - type: Battery
    maxCharge: 1000
    startingCharge: 1000
  - type: ProjectileBatteryAmmoProvider
    proto: BulletDisabler
    fireCost: 50
  - type: EnergyGun
    fireModes:
    - proto: BulletDisabler
      fireCost: 50
      name: disable
      state: disabler
    - proto: BulletEnergyGunLaser
      fireCost: 100
      name: lethal
      state: lethal
  - type: MagazineVisuals
    magState: mag
    steps: 5
    zeroVisible: true
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.EnergyGunFireModeVisuals.State:
        Firemode:
          Disabler: { state: mode-disabler }
          Lethal: { state: mode-lethal }
          Special: { state: mode-ion }
  - type: Tag
    tags:
    - HighRiskItem
    - Sidearm
  - type: StaticPrice
    price: 750
  - type: StealTarget
    stealGroup: HoSAntiqueWeapon

- type: entity
  name: "мініатюрна енергетична гармата"
  parent: BaseWeaponBatterySmall
  id: WeaponEnergyGunMini
  description: "Полегшена версія гармати Energy з меншою потужністю."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Battery/mini_energygun.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mode-disabler
      shader: unshaded
      map: [ "Firemode" ]
  - type: Clothing
    sprite: DeltaV/Objects/Weapons/Guns/Battery/mini_energygun.rsi
  - type: Gun
    soundGunshot:
      path: /Audio/DeltaV/Weapons/Guns/Gunshots/laser.ogg
    soundEmpty:
      path: /Audio/DeltaV/Weapons/Guns/Empty/dry_fire.ogg
  - type: Battery
    maxCharge: 500
    startingCharge: 500
  - type: ProjectileBatteryAmmoProvider
    proto: BulletDisabler
    fireCost: 50
  - type: EnergyGun
    fireModes:
    - proto: BulletDisabler
      fireCost: 50
      name: disable
      state: disabler
    - proto: BulletEnergyGunLaser
      fireCost: 100
      name: lethal
      state: lethal
  - type: MagazineVisuals
    magState: mag
    steps: 5
    zeroVisible: true
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.EnergyGunFireModeVisuals.State:
        Firemode:
          Disabler: { state: mode-disabler }
          Lethal: { state: mode-lethal }
          Special: { state: mode-stun } # Unused
  - type: MeleeWeapon
    attackRate: 1.2
    damage:
      types:
        Blunt: 7.5
    bluntStaminaDamageFactor: 1.0
    wideAnimationRotation: 135
  - type: DamageOtherOnHit
    staminaCost: 5

- type: entity
  name: "мініатюрна енергетична гармата"
  parent: WeaponEnergyGunMini
  id: WeaponEnergyGunMiniSecurity
  description: "Полегшена версія пістолета Energy з меншою потужністю. Серійний номер на руків'ї вказує на те, що цей пістолет належить співробітнику служби безпеки NT."
  components:
  - type: GuideHelp
    guides: [ SecurityWeapons ]

- type: entity
  name: "Енергетичний пістолет PDW-9"
  parent: BaseWeaponBatterySmall
  id: WeaponEnergyGunPistol
  description: "Зброя військового зразка, що використовується багатьма ополченськими силами в місцевому секторі."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Battery/energygun_pistol.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mode-disabler
      shader: unshaded
      map: [ "Firemode" ]
    - state: mag-unshaded-4
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: Clothing
    sprite: DeltaV/Objects/Weapons/Guns/Battery/multiphase_energygun.rsi
  - type: Gun
    soundGunshot:
      path: /Audio/DeltaV/Weapons/Guns/Gunshots/laser.ogg
    soundEmpty:
      path: /Audio/DeltaV/Weapons/Guns/Empty/dry_fire.ogg
  - type: Battery
    maxCharge: 800
    startingCharge: 800
  - type: ProjectileBatteryAmmoProvider
    proto: BulletDisabler
    fireCost: 50
  - type: EnergyGun
    fireModes:
    - proto: BulletDisabler
      fireCost: 50
      name: disable
      state: disabler
    - proto: BulletEnergyGunLaser
      fireCost: 100
      name: lethal
      state: lethal
  - type: MagazineVisuals
    magState: mag
    steps: 5
    zeroVisible: true
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.EnergyGunFireModeVisuals.State:
        Firemode:
          Disabler: { state: mode-disabler }
          Lethal: { state: mode-lethal }
  - type: Tag
    tags:
    - Sidearm
  - type: StaticPrice
    price: 750
  - type: MeleeWeapon
    attackRate: 1.2
    damage:
      types:
        Blunt: 7.5
    bluntStaminaDamageFactor: 1.0
    wideAnimationRotation: 135
  - type: DamageOtherOnHit
    staminaCost: 5

- type: entity
  name: "Енергетичний пістолет PDW-9"
  parent: WeaponEnergyGunPistol
  id: WeaponEnergyGunPistolSecurity
  description: "Пістолет військового зразка, який використовується багатьма міліцейськими підрозділами у місцевому секторі. Серійний номер на рукоятці вказує на те, що цей пістолет належить співробітнику служби безпеки НТ."
  components:
  - type: GuideHelp
    guides: [ SecurityWeapons ]

- type: entity
  name: "Енергетичний карабін IK-60"
  parent: BaseWeaponBattery
  id: WeaponGunLaserCarbineAutomatic
  description: "Напівавтоматичний енергетичний карабін на 20 набоїв."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Battery/energygun_carbine.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-unshaded-4
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: Clothing
    sprite: DeltaV/Objects/Weapons/Guns/Battery/energygun_carbine.rsi
  - type: Gun
    soundGunshot:
      path: /Audio/DeltaV/Weapons/Guns/Gunshots/laser.ogg
    soundEmpty:
      path: /Audio/DeltaV/Weapons/Guns/Empty/dry_fire.ogg
    selectedMode: SemiAuto
    fireRate: 3
    availableModes:
      - SemiAuto
  - type: Battery
    maxCharge: 2000
    startingCharge: 2000
  - type: ProjectileBatteryAmmoProvider
    proto: BulletEnergyGunLaser
    fireCost: 100
  - type: MagazineVisuals
    magState: mag
    steps: 5
    zeroVisible: true
  - type: Appearance

#PIRATE
- type: entity
  name: "передовий енергетичний револьвер"
  parent: BaseWeaponBatterySmall
  id: WeaponEnergyGunAdvancedRevolver
  description: "Полегшена версія гармати Energy з меншою потужністю."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Battery/advanced_revolver.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mode-disabler
      shader: unshaded
      map: [ "Firemode" ]
    - state: mag-unshaded-4
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: Clothing
    sprite: DeltaV/Objects/Weapons/Guns/Battery/advanced_revolver.rsi
  - type: Gun
    soundGunshot:
      path: /Audio/DeltaV/Weapons/Guns/Gunshots/laser.ogg
    soundEmpty:
      path: /Audio/DeltaV/Weapons/Guns/Empty/dry_fire.ogg
  - type: Battery
    maxCharge: 2000
    startingCharge: 2000
  - type: ProjectileBatteryAmmoProvider
    proto: BulletDisabler
    fireCost: 100
  - type: EnergyGun
    fireModes:
    - proto: BulletDisabler
      fireCost: 100
      name: disable
      state: disabler
    - proto: BulletEnergyGunLaser
      fireCost: 200
      name: lethal
      state: lethal
    # - proto: BulletEnergyGunIon
    #   fireCost: 500
    #   name: ion
    #   state: special
  - type: MagazineVisuals
    magState: mag
    steps: 5
    zeroVisible: true
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.EnergyGunFireModeVisuals.State:
        Firemode:
          Disabler: { state: mode-disabler }
          Lethal: { state: mode-lethal }
          Special: { state: mode-ion }
  - type: Tag
    tags:
    - HighRiskItem
    - Sidearm
  - type: StaticPrice
    price: 750

- type: entity
  name: "x-02 енерго-поліморф"
  parent: BaseWeaponBatterySmall
  id: WeaponEnergyGunMultiphasePolymorh
  description: "Чаклунська модифікація енерго-пістолету, що викликає небачені результати."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Weapons/Guns/Battery/multiphase_energygun.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mode-disabler
      shader: unshaded
      map: [ "Firemode" ]
    - state: mag-unshaded-4
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: Clothing
    quickEquip: false # Frontier
    slots: # Frontier
    - Back # Frontier
    - suitStorage # Frontier
    sprite: DeltaV/Objects/Weapons/Guns/Battery/multiphase_energygun.rsi
  - type: Gun
    soundGunshot:
      path: /Audio/DeltaV/Weapons/Guns/Gunshots/laser.ogg
    soundEmpty:
      path: /Audio/DeltaV/Weapons/Guns/Empty/dry_fire.ogg
  - type: Battery
    maxCharge: 500
    startingCharge: 500
  - type: ProjectileBatteryAmmoProvider
    proto: ProjectilePolyboltMouseTimed
    fireCost: 100
  - type: EnergyGun
    fireModes:
    - proto: ProjectilePolyboltMouseTimed
      fireCost: 100
      name: disable
      state: disabler
    - proto: ProjectilePolyboltMonkeyTimed
      fireCost: 100
      name: disable
      state: disabler
    - proto: ProjectilePolyboltGoatTimed
      fireCost: 100
      name: disable
      state: disabler
    - proto: ProjectilePolyboltCowTimed
      fireCost: 100
      name: disable
      state: disabler
    - proto: ProjectilePolyboltPigTimed
      fireCost: 100
      name: disable
      state: disabler
    - proto: ProjectilePolyboltHamsterTimed
      fireCost: 100
      name: disable
      state: disabler
    - proto: ProjectilePolyboltWizardCatTimed
      fireCost: 100
      name: disable
      state: disabler
    - proto: ProjectilePolyboltCluwneTimed
      fireCost: 100
      name: lethal
      state: lethal
    - proto: ProjectilePolyboltBreadTimed
      fireCost: 100
      name: ion
      state: special
  - type: MagazineVisuals
    magState: mag
    steps: 5
    zeroVisible: true
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.EnergyGunFireModeVisuals.State:
        Firemode:
          Disabler: { state: mode-disabler }
          Lethal: { state: mode-lethal }
          Special: { state: mode-ion }
  - type: Tag
    tags:
    - HighRiskItem
    - Sidearm
  - type: StaticPrice
    price: 750
  - type: BatterySelfRecharger
    autoRecharge: true
    autoRechargeRate: 5

