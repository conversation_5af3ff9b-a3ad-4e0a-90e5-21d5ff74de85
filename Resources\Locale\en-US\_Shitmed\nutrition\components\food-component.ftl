## System

food-system-eat-broadcasted = {CAPITALIZE(THE($user))} is trying to eat {THE($food)}!
food-system-eat-broadcasted-self = You start trying to eat {THE($food)}!
food-system-force-feed-broadcasted = {CAPITALIZE(THE($user))} is trying to feed {THE($target)} {THE($food)}!
food-system-force-feed-broadcasted-success = {CAPITALIZE(THE($user))} forced {THE($target)} to eat {THE($food)}!
food-system-eat-broadcasted-success = {CAPITALIZE(THE($user))} ate {THE($food)}!
