<Control xmlns="https://spacestation14.io"
         xmlns:customControls="clr-namespace:Content.Client.Administration.UI.CustomControls"
         Margin="0 0 0 10">
    <PanelContainer VerticalExpand="True" StyleClasses="BackgroundDark">
        <BoxContainer Orientation="Vertical"
                      HorizontalExpand="True"
                      VerticalExpand="True"
                      Margin="5 5 5 5">
            <RichTextLabel Name="LawNumberLabel" StyleClasses="LabelKeyText"/>
            <customControls:HSeparator Margin="0 5 0 5"/>
            <RichTextLabel Name="LawLabel"/>
            <BoxContainer Name="LawAnnouncementButtons" Orientation="Horizontal" HorizontalExpand="True" Margin="0 5 0 0">
                <Label Text="{Loc laws-ui-state-law}" StyleClasses="StatusFieldTitle"/>
                <Control Margin="5 0 0 0" />
                <!-- Buttons are added via C# code-->
            </BoxContainer>
        </BoxContainer>
    </PanelContainer>
</Control>
