﻿- type: entity
  id: Recycler
  parent: BaseMachinePowered
  name: "переробник"
  description: "Велика дробильна машина, що використовується для неефективної переробки дрібних предметів. Збоку є ліхтарі."
  components:
  - type: AmbientSound
    enabled: false
    volume: -8
    range: 5
    sound:
      # TODO: https://freesound.org/people/derjuli/sounds/448133/ CC-NC-
      path: /Audio/Ambience/Objects/circular_saw.ogg
  - type: Fixtures
    fixtures:
      brrt:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.15,-0.15,0.15,0.15"
        hard: false
        layer:
        - FullTileLayer
      collision:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.49,-0.49,0.49,0.49"
        hard: true
        mask:
        - Impassable
        layer:
        - Opaque
        - BulletImpassable
      conveyor:
        shape: !type:PolygonShape
          vertices:
          - -0.55,-0.55
          - 0.55,-0.55
          - 0.55,0.55
          - -0.55,0.55
        layer:
        - Impassable
        - MidImpassable
        - LowImpassable
        hard: False
  - type: DeviceLinkSink
    ports:
      - Reverse
      - Forward
      - Off
  - type: Transform
    anchored: true
    noRot: false
  - type: Sprite
    drawdepth: Doors
    sprite: Structures/Machines/recycling.rsi
    layers:
    - state: grinder-o0
      map: ["enum.RecyclerVisualLayers.Main"]
    - state: grinder-o0bld
      map: ["enum.RecyclerVisualLayers.Bloody"]
      visible: false
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.RecyclerVisuals.Bloody:
        enum.RecyclerVisualLayers.Main:
          True: { visible: false }
          False: { visible: true }
        enum.RecyclerVisualLayers.Bloody:
          True: { visible: true }
          False: { visible: false }
      enum.ConveyorVisuals.State:
        enum.RecyclerVisualLayers.Main:
          Forward: { state: grinder-o1 }
          Reverse: { state: grinder-o1 }
          Off: { state: grinder-o0 }
        enum.RecyclerVisualLayers.Bloody:
          Forward: { state: grinder-o1bld }
          Reverse: { state: grinder-o1bld }
          Off: { state: grinder-o0bld }
  - type: CollideMaterialReclaimer
  - type: MaterialReclaimer
    enabled: false
    efficiency: 0.25
    scaleProcessSpeed: false #instant!
    minimumProcessDuration: 0
    whitelist:
      components:
      - PhysicalComposition
      - MobState
      - SpaceGarbage
      tags:
      - Trash
      - Recyclable
    blacklist:
      components:
      - Material
      - Pda
      - IdCard
      tags:
      - HighRiskItem
    sound:
      path: /Audio/Effects/saw.ogg
      params:
        volume: -3
    cutOffSound: false
  - type: SolutionContainerManager
    solutions:
      output:
        maxVol: 0 #exists only for the overflow stuff on material reclaimer
  - type: MaterialStorage
  - type: Conveyor
  - type: Rotatable
  - type: InteractionPopup
    successChance: 1.0
    interactSuccessString: petting-success-recycler
    interactFailureString: petting-failure-generic
    interactSuccessSound:
      path: /Audio/Items/drill_hit.ogg
  - type: ApcPowerReceiver
