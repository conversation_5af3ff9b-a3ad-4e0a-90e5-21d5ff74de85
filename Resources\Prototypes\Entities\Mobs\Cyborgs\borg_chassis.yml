- type: entity
  id: BorgChassisSelectable
  parent: BaseBorgChassisNT
  components:
  - type: Sprite
    layers:
    - state: robot
      map: ["enum.BorgVisualLayers.Body", "movement"]
    - state: robot_e_r
      map: ["enum.BorgVisualLayers.Light"]
      shader: unshaded
      visible: false
    - state: robot_l
      shader: unshaded
      map: ["light","enum.BorgVisualLayers.LightStatus"]
      visible: false
  - type: BorgChassis
    # Default borg can take no modules until selected type.
    maxModules: 0
    hasMindState: robot_e
    noMindState: robot_e_r
  - type: BorgTransponder
    sprite:
      sprite: Mobs/Silicon/chassis.rsi
      state: robot
    name: cyborg
  - type: InteractionPopup
    interactSuccessString: petting-success-generic-cyborg
    interactFailureString: petting-failure-generic-cyborg
  - type: BorgSwitchableType
    inherentRadioChannels:
    - Common
    - Binary
  # Have to add the following components here or it doesn't work.
  - type: SurgeryIgnoreClothing
  - type: SurgerySpeedModifier
    speedModifier: 1.5
  - type: Sanitized

- type: entity
  id: BorgChassisGeneric
  parent: BorgChassisSelectable
  name: "загальний кіборг"
  suffix: type picked
  components:
  - type: BorgSwitchableType
    selectedBorgType: generic

- type: entity
  id: BorgChassisMining
  parent: BorgChassisSelectable
  name: "кіборг-утилізатор"
  components:
  - type: BorgSwitchableType
    selectedBorgType: mining

- type: entity
  id: BorgChassisEngineer
  parent: BorgChassisSelectable
  name: "кіборг-інженер"
  components:
  - type: BorgSwitchableType
    selectedBorgType: engineering

- type: entity
  id: BorgChassisJanitor
  parent: BorgChassisSelectable
  name: "кіборг-прибиральник"
  components:
  - type: BorgSwitchableType
    selectedBorgType: janitor

- type: entity
  id: BorgChassisService
  parent: BorgChassisSelectable
  name: "сервісний кіборг"
  components:
  - type: BorgSwitchableType
    selectedBorgType: service

- type: entity
  id: BorgChassisMedical
  parent: BorgChassisSelectable
  name: "медичний кіборг"
  components:
  - type: BorgSwitchableType
    selectedBorgType: medical
  # Have to add the following components here or it doesn't work.
  - type: SurgeryIgnoreClothing
  - type: SurgerySpeedModifier
    speedModifier: 1.5
  - type: Sanitized

# Robotics Made

- type: entity
  id: BorgChassisGenericBare
  parent: BaseBorgChassisNT
  components:
  - type: Sprite
    layers:
    - state: robot
    - state: robot_e_r
      map: ["enum.BorgVisualLayers.Light"]
      shader: unshaded
      visible: false
    - state: robot_l
      shader: unshaded
      map: ["light"]
      visible: false
  - type: BorgChassis
    maxModules: 6
    moduleWhitelist:
      tags:
      - BorgModuleGeneric
    hasMindState: robot_e
    noMindState: robot_e_r
  - type: BorgTransponder
    sprite:
      sprite: Mobs/Silicon/chassis.rsi
      state: robot
    name: cyborg
  - type: Construction
    node: cyborg
  - type: Speech
    speechVerb: Robotic

- type: entity
  id: BorgChassisMiningBare
  parent: BaseBorgChassisNT
  name: "кіборг-утилізатор"
  components:
  - type: Sprite
    layers:
    - state: miner
      map: ["movement"]
    - state: miner_e_r
      map: ["enum.BorgVisualLayers.Light"]
      shader: unshaded
      visible: false
    - state: miner_l
      shader: unshaded
      map: ["light"]
      visible: false
  - type: SpriteMovement
    movementLayers:
      movement:
        state: miner_moving
    noMovementLayers:
      movement:
        state: miner
  - type: BorgChassis
    maxModules: 6
    moduleWhitelist:
      tags:
      - BorgModuleGeneric
      - BorgModuleCargo
    hasMindState: miner_e
    noMindState: miner_e_r
  - type: BorgTransponder
    sprite:
      sprite: Mobs/Silicon/chassis.rsi
      state: miner
    name: "кіборг-утилізатор"
  - type: Construction
    node: mining
  - type: IntrinsicRadioTransmitter
    channels:
    - Supply
    - Binary
    - Common
    - Science
  - type: ActiveRadio
    channels:
    - Supply
    - Binary
    - Common
    - Science
  - type: AccessReader
    access: [["Cargo"], ["Salvage"], ["Command"], ["Research"]]
  - type: Inventory
    templateId: borgTall

- type: entity
  id: BorgChassisEngineerBare
  parent: BaseBorgChassisNT
  name: "кіборг-інженер"
  components:
  - type: Sprite
    layers:
    - state: engineer
    - state: engineer_e_r
      map: ["enum.BorgVisualLayers.Light"]
      shader: unshaded
      visible: false
    - state: engineer_l
      shader: unshaded
      map: ["light"]
      visible: false
  - type: BorgChassis
    maxModules: 7
    moduleWhitelist:
      tags:
      - BorgModuleGeneric
      - BorgModuleEngineering
    hasMindState: engineer_e
    noMindState: engineer_e_r
  - type: BorgTransponder
    sprite:
      sprite: Mobs/Silicon/chassis.rsi
      state: engineer
    name: "кіборг-інженер"
  - type: Construction
    node: engineer
  - type: IntrinsicRadioTransmitter
    channels:
    - Engineering
    - Binary
    - Common
    - Science
  - type: ActiveRadio
    channels:
    - Engineering
    - Binary
    - Common
    - Science
  - type: AccessReader
    access: [["Engineering"], ["Command"], ["Research"]]
  - type: Inventory
    templateId: borgShort
  - type: SiliconLawProvider # Delta-V - Adds custom lawset for Engineering Cyborg
    laws: Engineer

- type: entity
  id: BorgChassisJanitorBare
  parent: BaseBorgChassisNT
  name: "кіборг-прибиральник"
  components:
  - type: Sprite
    layers:
    - state: janitor
      map: ["movement"]
    - state: janitor_e_r
      map: ["enum.BorgVisualLayers.Light"]
      shader: unshaded
      visible: false
    - state: janitor_l
      shader: unshaded
      map: ["light"]
      visible: false
  - type: SpriteMovement
    movementLayers:
      movement:
        state: janitor_moving
    noMovementLayers:
      movement:
        state: janitor
  - type: BorgChassis
    maxModules: 5
    moduleWhitelist:
      tags:
      - BorgModuleGeneric
      - BorgModuleJanitor
    hasMindState: janitor_e
    noMindState: janitor_e_r
  - type: BorgTransponder
    sprite:
      sprite: Mobs/Silicon/chassis.rsi
      state: janitor
    name: "кіборг-прибиральник"
  - type: Construction
    node: janitor
  - type: IntrinsicRadioTransmitter
    channels:
    - Service
    - Binary
    - Common
    - Science
  - type: ActiveRadio
    channels:
    - Service
    - Binary
    - Common
    - Science
  - type: AccessReader
    access: [["Service"], ["Command"], ["Research"]]
  - type: Inventory
    templateId: borgShort
  - type: SiliconLawProvider # Delta-V Adds custom lawset for Janitor Cyborg
    laws: Janitor

- type: entity
  id: BorgChassisMedicalBare
  parent: BaseBorgChassisNT
  name: "медичний кіборг"
  components:
  - type: Sprite
    layers:
    - state: medical
      map: ["movement"]
    - state: medical_e_r
      map: ["enum.BorgVisualLayers.Light"]
      shader: unshaded
      visible: false
    - state: medical_l
      shader: unshaded
      map: ["light"]
      visible: false
  - type: SpriteMovement
    movementLayers:
      movement:
        state: medical_moving
    noMovementLayers:
      movement:
        state: medical
  - type: BorgChassis
    maxModules: 4
    moduleWhitelist:
      tags:
      - BorgModuleGeneric
      - BorgModuleMedical
    hasMindState: medical_e
    noMindState: medical_e_r
  - type: BorgTransponder
    sprite:
      sprite: Mobs/Silicon/chassis.rsi
      state: medical
    name: "медичний кіборг"
  - type: Construction
    node: medical
  - type: IntrinsicRadioTransmitter
    channels:
    - Medical
    - Binary
    - Common
    - Science
  - type: ActiveRadio
    channels:
    - Medical
    - Binary
    - Common
    - Science
  - type: AccessReader
    access: [["Medical"], ["Command"], ["Research"]]
  - type: Inventory
    templateId: borgDutch
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepHoverBorg
  - type: FabricateActions
    actions:
    - ActionFabricateLollipop
    - ActionFabricateGumball
  - type: ShowHealthBars
    damageContainers:
    - Biological
  - type: ShowHealthIcons
    damageContainers:
    - Biological
  - type: SiliconLawProvider
    laws: Medical
  - type: SurgeryTarget # Shitmed
  - type: Sanitized # Shitmed
  - type: SolutionScanner
  - type: InteractionPopup
    interactSuccessString: petting-success-medical-cyborg
    interactFailureString: petting-failure-medical-cyborg
    interactSuccessSound:
      path: /Audio/Ambience/Objects/periodic_beep.ogg
  - type: SurgeryIgnoreClothing
  - type: SurgerySpeedModifier
    speedModifier: 1.5

- type: entity
  id: BorgChassisServiceBare
  parent: BorgChassisSelectable
  name: "сервісний кіборг"
  components:
  - type: Sprite
    layers:
    - state: service
    - state: service_e_r
      map: ["enum.BorgVisualLayers.Light"]
      shader: unshaded
      visible: false
    - state: service_l
      shader: unshaded
      map: ["light"]
      visible: false
  - type: BorgChassis
    maxModules: 6
    moduleWhitelist:
      tags:
      - BorgModuleGeneric
      - BorgModuleService
    hasMindState: service_e
    noMindState: service_e_r
  - type: BorgTransponder
    sprite:
      sprite: Mobs/Silicon/chassis.rsi
      state: service
    name: "сервісний кіборг"
  - type: Construction
    node: service
  - type: IntrinsicRadioTransmitter
    channels:
    - Service
    - Binary
    - Common
    - Science
  - type: ActiveRadio
    channels:
    - Service
    - Binary
    - Common
    - Science
  - type: AccessReader
    access: [["Service"], ["Command"], ["Research"]]
  - type: Inventory
    templateId: borgTall

- type: entity
  id: BorgChassisSyndicateAssault
  parent: BaseBorgChassisSyndicate
  name: "синдикатський штурмовий кіборг"
  description: "Худорлява, жорстока машина для вбивства з доступом до різноманітних смертоносних модулів."
  components:
    - type: Sprite
      layers:
        - state: synd_sec
        - state: synd_sec_e
          map: ["enum.BorgVisualLayers.Light"]
          shader: unshaded
          visible: false
        - state: synd_sec_l
          shader: unshaded
          map: ["light"]
          visible: false
    - type: BorgChassis
      maxModules: 3
      moduleWhitelist:
        tags:
          - BorgModuleGeneric
          - BorgModuleSyndicate
          - BorgModuleSyndicateAssault
      hasMindState: synd_sec_e
      noMindState: synd_sec
    - type: InteractionPopup
      interactSuccessString: petting-success-syndicate-cyborg
      interactFailureString: petting-failure-syndicate-cyborg
      interactSuccessSound:
        path: /Audio/Ambience/Objects/periodic_beep.ogg

- type: entity
  id: BorgChassisSyndicateMedical
  parent: BaseBorgChassisSyndicate
  name: "синдикатський медичний кіборг"
  description: "Бойовий медичний кіборг. Має обмежений наступальний потенціал, але з лишком компенсує його своїми можливостями підтримки."
  components:
    - type: Sprite
      layers:
        - state: synd_medical
        - state: synd_medical_e
          map: ["enum.BorgVisualLayers.Light"]
          shader: unshaded
          visible: false
        - state: synd_medical_l
          shader: unshaded
          map: ["light"]
          visible: false
    - type: BorgChassis
      maxModules: 3
      moduleWhitelist:
        tags:
          - BorgModuleGeneric
          - BorgModuleMedical
          - BorgModuleSyndicate
      hasMindState: synd_medical_e
      noMindState: synd_medical
    - type: ShowHealthBars
    - type: InteractionPopup
      interactSuccessString: petting-success-syndicate-cyborg
      interactFailureString: petting-failure-syndicate-cyborg
      interactSuccessSound:
        path: /Audio/Ambience/Objects/periodic_beep.ogg
    - type: SolutionScanner
    - type: FootstepModifier
      footstepSoundCollection:
        collection: FootstepHoverBorg
        params:
          volume: -6

- type: entity
  id: BorgChassisSyndicateSaboteur
  parent: BaseBorgChassisSyndicate
  name: "синдикатський диверсійний кіборг"
  description: "Обтічний інженерний кіборг, оснащений прихованими модулями. Проектор-хамелеон дозволяє йому маскуватися під кіборга \"НаноТрейзен\"."
  components:
    - type: Sprite
      layers:
        - state: synd_engi
        - state: synd_engi_e
          map: ["enum.BorgVisualLayers.Light"]
          shader: unshaded
          visible: false
        - state: synd_engi_l
          shader: unshaded
          map: ["light"]
          visible: false
    - type: BorgChassis
      maxModules: 3
      moduleWhitelist:
        tags:
          - BorgModuleGeneric
          - BorgModuleEngineering
          - BorgModuleSyndicate
      hasMindState: synd_engi_e
      noMindState: synd_engi
    - type: ShowHealthBars
      damageContainers:
      - Inorganic
      - Silicon
    - type: InteractionPopup
      interactSuccessString: petting-success-syndicate-cyborg
      interactFailureString: petting-failure-syndicate-cyborg
      interactSuccessSound:
        path: /Audio/Ambience/Objects/periodic_beep.ogg
