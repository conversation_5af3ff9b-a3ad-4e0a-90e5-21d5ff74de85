- type: entity
  id: DoorElectronics
  parent: BaseElectronics
  name: "плата дверей"
  description: "Електронна плата, що використовується у дверях і шлюзах"
  components:
    - type: Sprite
      sprite: Objects/Misc/module.rsi
      state: door_electronics
    - type: Tag
      tags:
        - DoorElectronics
    - type: DoorElectronics
    - type: ReverseEngineering # Nyano
      recipes:
        - DoorElectronics
    - type: StaticPrice
      price: 55
    - type: AccessReader
    - type: ActivatableUI
      key: enum.DoorElectronicsConfigurationUiKey.Key
      requiredItems:
        tags:
        - DoorElectronicsConfigurator
    - type: UserInterface
      interfaces:
        enum.DoorElectronicsConfigurationUiKey.Key:
          type: DoorElectronicsBoundUserInterface
