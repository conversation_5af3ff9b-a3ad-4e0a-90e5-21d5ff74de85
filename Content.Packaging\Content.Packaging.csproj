<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <ServerGarbageCollection>True</ServerGarbageCollection>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\RobustToolbox\Robust.Packaging\Robust.Packaging.csproj" />
  </ItemGroup>

  <Import Project="..\RobustToolbox\MSBuild\Robust.Properties.targets" />
</Project>
