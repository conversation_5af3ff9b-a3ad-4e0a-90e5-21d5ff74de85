using Content.Client.UserInterface.Controls;
using Content.Shared._Impstation.Thaven.Components;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;

namespace Content.Client._Impstation.Thaven;

[GenerateTypedNameReferences]
public sealed partial class ThavenMoodsMenu : FancyWindow
{
    public ThavenMoodsMenu()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
    }

    public void Update(EntityUid uid, ThavenMoodsBuiState state)
    {
        MoodDisplayContainer.Children.Clear();

        foreach (var mood in state.SharedMoods)
            MoodDisplayContainer.AddChild(new MoodDisplay(mood, true));

        foreach (var mood in state.Moods)
            MoodDisplayContainer.AddChild(new MoodDisplay(mood, false));
    }
}
