## UI

hypospray-all-mode-text = Only Injects
hypospray-mobs-only-mode-text = Draws and Injects
hypospray-invalid-text = Invalid
hypospray-volume-label = Volume: [color=white]{$currentVolume}/{$totalVolume}u[/color]
    Mode: [color=white]{$modeString}[/color]

## Entity

hypospray-component-inject-other-message = You inject {$other}.
hypospray-component-inject-self-message = You inject yourself.
hypospray-component-inject-self-clumsy-message = Oops! You injected yourself.
hypospray-component-empty-message = Nothing to inject.
hypospray-component-feel-prick-message = You feel a tiny prick!
hypospray-component-transfer-already-full-message = {$owner} is already full!
hypospray-cant-inject = Can't inject into {$target}!

hypospray-verb-mode-label = Toggle Container Draw
hypospray-verb-mode-inject-all = You cannot draw from containers anymore.
hypospray-verb-mode-inject-mobs-only = You can now draw from containers.
