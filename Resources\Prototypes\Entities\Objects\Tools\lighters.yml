- type: entity
  name: "звичайна запальничка"
  parent: BaseItem
  id: Lighter
  description: "Проста пластикова запальничка."
  components:
  - type: IgnitionSource
    ignited: false
  - type: ItemToggle
    predictable: false
    soundActivate:
      collection: lighterOnSounds
    soundDeactivate:
      collection: lighterOffSounds
  - type: ItemToggleMeleeWeapon
    activatedDamage:
        types:
            Heat: 1
    activatedSoundOnHit:
      path: /Audio/Weapons/Guns/Hits/energy_meat1.ogg
      params:
        variation: 0.250
        volume: -15
    activatedSoundOnHitNoDamage:
      path: /Audio/Weapons/Guns/Hits/energy_meat1.ogg
      params:
        variation: 0.250
        volume: -17
  - type: ItemToggleDamageOtherOnHit
  - type: ItemToggleSize
    activatedSize: Small
  - type: ItemToggleHot
  - type: Sprite
    sprite: Objects/Tools/lighters.rsi
    layers:
    - state: icon_map
    - state: cheap_icon_base
      map: [ "skin" ]
    - state: basic_icon_top
    - state: lighter_flame
      visible: false
      shader: unshaded
      map: [ "flame" ]
  - type: Appearance
  - type: RandomSprite
    available:
      - skin:
          basic_icon_base-1: ""
          basic_icon_base-2: ""
          basic_icon_base-3: ""
          basic_icon_base-4: ""
          basic_icon_base-5: ""
          basic_icon_base-6: ""
          basic_icon_base-7: ""
          basic_icon_base-8: ""
          basic_icon_base-9: ""
          basic_icon_base-10: ""
          basic_icon_base-11: ""
  - type: GenericVisualizer
    visuals:
      enum.ToggleVisuals.Toggled:
        flame:
          True: { visible: true }
          False: { visible: false }
  - type: ToggleableLightVisuals
    spriteLayer: lighter_flame
    inhandVisuals:
      left:
      - state: inhand-left-flame
        shader: unshaded
      right:
      - state: inhand-right-flame
        shader: unshaded
  - type: Item
    size: Tiny
    sprite: Objects/Tools/lighters.rsi
  - type: UseDelay
  - type: RefillableSolution
    solution: Welder
  - type: SolutionContainerManager
    solutions:
      Welder:
        reagents:
        - ReagentId: WeldingFuel
          Quantity: 8
        maxVol: 8 #uses less fuel than a welder, so this isnt as bad as it looks
  - type: MeleeWeapon
    wideAnimationRotation: 180
    damage:
      types:
        Blunt: 0
  - type: DamageOtherOnHit
  - type: Welder
    fuelConsumption: 0.01
    fuelLitCost: 0.1
    tankSafe: true
  - type: PointLight
    enabled: false
    netsync: false
    radius: 1.1 #smallest possible
    color: orange
  # Shitmed Change
  - type: Cautery
    speed: 0.45
  - type: SurgeryTool
    startSound:
      collection: lighterOnSounds
    endSound:
      collection: lighterOffSounds
  - type: ItemTogglePointLight
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 10
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2
  - type: SpaceGarbage

- type: entity
  name: "дешева запальничка"
  parent: Lighter
  id: CheapLighter
  description: "Небезпечно дешева пластикова запальничка, не обпечіть свої пальці!"
  components:
  - type: Sprite
    sprite: Objects/Tools/lighters.rsi
    layers:
    - state: icon_map
    - state: cheap_icon_base
      map: [ "skin" ]
    - state: cheap_icon_top
    - state: lighter_flame
      visible: false
      shader: unshaded
      map: [ "flame" ]
  - type: RandomSprite
    available:
      - skin:
          cheap_icon_base: Rainbow
  - type: SolutionContainerManager
    solutions:
      Welder:
        reagents:
        - ReagentId: WeldingFuel
          Quantity: 4
        maxVol: 4 #uses less fuel than a welder, so this isnt as bad as it looks

- type: entity
  name: "запальничка фліппо"
  parent: BaseItem
  id: FlippoLighter
  description: "Міцна металева запальничка, служитиме досить довго."
  components:
  - type: Sprite
    sprite: Objects/Tools/lighters.rsi
    layers:
    - state: zippo_icon_base
      map: ["base"]
    - state: zippo_top
      map: ["top"]
      visible: false
    - state: zippo_open
      map: ["open"]
      visible: false
    - state: lighter_flame
      map: ["flame"]
      visible: false
      shader: unshaded
  - type: ItemToggle
    predictable: false
    soundActivate:
      path: /Audio/Items/Lighters/zippo_open.ogg
      params:
        volume: -5
    soundDeactivate:
      path: /Audio/Items/Lighters/zippo_close.ogg
      params:
        volume: -5
  - type: ItemToggleMeleeWeapon
    activatedDamage:
        types:
            Heat: 1
    activatedSoundOnHit:
      path: /Audio/Weapons/Guns/Hits/energy_meat1.ogg
      params:
        variation: 0.250
        volume: -15
    activatedSoundOnHitNoDamage:
      path: /Audio/Weapons/Guns/Hits/energy_meat1.ogg
      params:
        variation: 0.250
        volume: -17
  - type: ItemToggleDamageOtherOnHit
  - type: ItemToggleSize
    activatedSize: Small
  - type: ItemToggleHot
  - type: Item
    size: Tiny
    sprite: Objects/Tools/lighters.rsi
    heldPrefix: zippo
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ToggleVisuals.Toggled:
        flame:
          True: { visible: true }
          False: { visible: false }
        open:
          True: { visible: true }
          False: { visible: false }
        top:
          True: { visible: true }
          False: { visible: false }
        base:
          True: { visible: false }
          False: { visible: true }
  - type: SolutionContainerManager
    solutions:
      Welder:
        reagents:
        - ReagentId: WeldingFuel
          Quantity: 12
        maxVol: 12 #uses less fuel than a welder, so this isnt as bad as it looks
  - type: Welder
    fuelConsumption: 0.01
    fuelLitCost: 0.1
    tankSafe: true
  - type: ToggleableLightVisuals
    spriteLayer: lighter_flame
    inhandVisuals:
      left:
      - state: zippo-inhand-left-flame
        shader: unshaded
      right:
      - state: zippo-inhand-right-flame
        shader: unshaded
  - type: MeleeWeapon
    wideAnimationRotation: 180
    damage:
      types:
        Blunt: 1 # does a little bit of damage on hit when off
  - type: DamageOtherOnHit
  - type: PointLight
    enabled: false
    netsync: false
    radius: 1.2 #slightly stronger than the other lighters
    color: orange
  - type: ItemTogglePointLight
  - type: UseDelay
  - type: IgnitionSource
    ignited: false

- type: entity
  name: "запальничка з гравіюванням фліппо"
  parent: FlippoLighter
  id: FlippoEngravedLighter
  description: "Міцна золота запальничка, яка служитиме досить довго. Гравіювання не дають жодної тактичної переваги."
  components:
  - type: Sprite
    sprite: Objects/Tools/lighters.rsi
    layers:
    - state: zippo_engraved_icon_base
      map: ["base"]
    - state: zippo_top
      map: ["top"]
      visible: false
    - state: zippo_engraved_open
      map: ["open"]
      visible: false
    - state: lighter_flame
      map: ["flame"]
      visible: false
      shader: unshaded
  - type: StealTarget
    stealGroup: FlippoEngravedLighter
