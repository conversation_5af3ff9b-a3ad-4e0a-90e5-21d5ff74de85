<paper:PaperWindow xmlns="https://spacestation14.io"
    xmlns:paper="clr-namespace:Content.Client.Paper.UI"
    MouseFilter="Stop" Resizable="True" MinSize="150 150"
    SetSize="510 660"> <!-- Provide some reasonable sizes by default. Can be changed by the component -->

    <BoxContainer Name="ContentsRoot" Orientation="Vertical">
        <PanelContainer StyleClasses="AngleRect" VerticalAlignment="Top" HorizontalAlignment="Right" Margin="6">
            <TextureButton Name="CloseButton" StyleClasses="windowCloseButton"/>
        </PanelContainer>
        <PanelContainer Name="PaperBackground" StyleClasses="PaperDefaultBorder" VerticalExpand="True" HorizontalExpand="True">
            <ScrollContainer Name="ScrollingContents" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" HorizontalExpand="True" VerticalExpand="True" HScrollEnabled="False">
                <PanelContainer Name="PaperContent" VerticalExpand="True" HorizontalExpand="True" MaxWidth="600">
                    <BoxContainer Orientation="Vertical" VerticalAlignment="Stretch">
                        <TextureButton Name="HeaderImage" HorizontalAlignment="Center" VerticalAlignment="Top" MouseFilter="Ignore"/>
                        <Control Name="TextAlignmentPadding" VerticalAlignment="Top"/>
                        <RichTextLabel Name="BlankPaperIndicator" StyleClasses="LabelSecondaryColor" VerticalAlignment="Top" HorizontalAlignment="Center"/>
                        <RichTextLabel StyleClasses="PaperWrittenText" Name="WrittenTextLabel" VerticalAlignment="Top"/>
                        <PanelContainer Name="InputContainer" StyleClasses="TransparentBorderedWindowPanel" MinHeight="100"
                                        VerticalAlignment="Stretch" VerticalExpand="True" HorizontalExpand="True">
                            <TextEdit Name="Input" StyleClasses="PaperLineEdit" Access="Public" />
                        </PanelContainer>
                    </BoxContainer>
                    <paper:StampCollection Name="StampDisplay" VerticalAlignment="Bottom" Margin="6"/>

                </PanelContainer>
            </ScrollContainer>
        </PanelContainer>
        <!-- Bottom buttons for editing -->
        <BoxContainer Name="EditButtons" Orientation="Horizontal" HorizontalAlignment="Right" Margin="6">
            <Button Name="SaveButton" />
        </BoxContainer>
    </BoxContainer>
</paper:PaperWindow>
