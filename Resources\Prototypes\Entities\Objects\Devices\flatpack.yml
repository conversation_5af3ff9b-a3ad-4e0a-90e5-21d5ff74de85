- type: entity
  parent: BaseItem
  id: BaseFlatpack
  name: "базовий флетпак"
  description: "Плаский пакунок, який використовується для побудови чогось."
  categories: [ HideSpawnMenu ]
  components:
  - type: Item
    size: Large
  - type: Sprite
    sprite: Objects/Devices/flatpack.rsi
    layers:
    - state: base
    - state: overlay
      color: "#cec8ac"
      map: ["enum.FlatpackVisualLayers.Overlay"]
    - state: icon-default
  - type: Appearance
  - type: Flatpack
    boardColors:
      command: "#334E6D"
      medical: "#52B4E9"
      service: "#9FED58"
      engineering: "#EFB341"
      security: "#DE3A3A"
      science: "#D381C9"
      supply: "#A46106"
      cpu_command: "#334E6D"
      cpu_medical: "#52B4E9"
      cpu_service: "#9FED58"
      cpu_engineering: "#EFB341"
      cpu_security: "#DE3A3A"
      cpu_science: "#D381C9"
      cpu_supply: "#A46106"
  - type: StaticPrice
    price: 250

- type: entity
  parent: BaseFlatpack
  id: SolarAssemblyFlatpack
  name: "флетпак сонячної панелі"
  description: "Флетпак, що використовується для побудови сонячної батареї."
  components:
  - type: Flatpack
    entity: SolarAssembly
  - type: Sprite
    layers:
    - state: solar-assembly-part
  - type: StaticPrice
    price: 75

- type: entity
  parent: BaseFlatpack
  id: AmePartFlatpack
  name: "флетпак ДАМу"
  description: "Флетпак, що використовується для побудови реактора двигуна антиматерії."
  components:
  - type: Sprite
    layers:
    - state: ame-part
  - type: Flatpack
    entity: AmeShielding
  - type: StaticPrice
    price: 500
  - type: GuideHelp
    guides: [ AME, Power ]
  - type: StealTarget
    stealGroup: AmePartFlatpack

- type: entity
  parent: BaseFlatpack
  id: TegCenterPartFlatpack
  name: "флетпак Центра ТЕГ"
  description: "Флетпак, що використовується для будівництва центрального ядра ТЕГ."
  components:
  - type: Flatpack
    entity: TegCenter
  - type: Sprite
    layers:
    - state: base
    - state: overlay
      color: "#cec8ac"
      map: ["enum.FlatpackVisualLayers.Overlay"]
    - state: icon-default
  - type: GuideHelp
    guides: [ TEG, Power ]

- type: entity
  parent: BaseFlatpack
  id: TegCirculatorPartFlatpack
  name: "флетпак Циркулятора ТЕГ"
  description: "Флетпак, що використовується для будівництва циркулятора ТЕГ."
  components:
  - type: Flatpack
    entity: TegCirculator
  - type: Sprite
    layers:
    - state: base
    - state: overlay
      color: "#cec8ac"
      map: ["enum.FlatpackVisualLayers.Overlay"]
    - state: icon-default
  - type: GuideHelp
    guides: [ TEG, Power ]

- type: entity
  parent: BaseFlatpack
  id: SingularityGeneratorFlatpack
  name: "флетпак генератора сингулярностей"
  description: "Флетпак, що використовується для побудови генератора сингулярностей."
  components:
  - type: Flatpack
    entity: SingularityGenerator
  - type: Sprite
    layers:
    - state: singularity-generator
  - type: GuideHelp
    guides: [ Singularity, Power ]

- type: entity
  parent: BaseFlatpack
  id: RadiationCollectorFlatpack
  name: "флетпак радіаційного колектора"
  description: "Флетпак, що використовується для побудови радіаційного колектора."
  components:
  - type: Flatpack
    entity: RadiationCollectorNoTank
  - type: Sprite
    layers:
    - state: radiation-collector
  - type: GuideHelp
    guides: [ Singularity, Power ]

- type: entity
  parent: BaseFlatpack
  id: ContainmentFieldGeneratorFlatpack
  name: "флетпак генератора захисного поля"
  description: "Флетпак, що використовується для побудови генератора захисного поля."
  components:
  - type: Flatpack
    entity: ContainmentFieldGenerator
  - type: Sprite
    layers:
    - state: containment-field-generator
  - type: GuideHelp
    guides: [ Singularity, Power ]

- type: entity
  parent: BaseFlatpack
  id: EmitterFlatpack
  name: "флетпак випромінювача"
  description: "Флетпак, що використовується для побудови випромінювача."
  components:
  - type: Flatpack
    entity: Emitter
  - type: Sprite
    layers:
    - state: emitter
  - type: GuideHelp
    guides: [ Singularity, Power ]

- type: entity
  parent: BaseFlatpack
  id: TeslaGeneratorFlatpack
  name: "флетпак генератора тесли"
  description: "Флетпак, що використовується для побудови тесла-генератора."
  components:
  - type: Flatpack
    entity: TeslaGenerator
  - type: Sprite
    layers:
    - state: tesla-generator
  - type: GuideHelp
    guides: [ Power ] # TODO add tesla guide

- type: entity
  parent: BaseFlatpack
  id: TeslaCoilFlatpack
  name: "флетпак котушки тесли"
  description: "Флетпак, що використовується для побудови котушки Тесли."
  components:
  - type: Flatpack
    entity: TeslaCoil
  - type: Sprite
    layers:
    - state: tesla-coil
  - type: GuideHelp
    guides: [ Power ] # TODO add tesla guide

- type: entity
  parent: BaseFlatpack
  id: TeslaGroundingRodFlatpack
  name: "флетпак заземлювача тесли"
  description: "Флетпак, що використовується для виготовлення заземлювача."
  components:
  - type: Flatpack
    entity: TeslaGroundingRod
  - type: Sprite
    layers:
    - state: grounding-rod
  - type: GuideHelp
    guides: [ Power ] # TODO add tesla guide

- type: entity
  parent: BaseFlatpack
  id: GyroscopeFlatpack
  name: "флетпак гіроскопа"
  description: "Флетпак, що використовується для побудови гіроскопа."
  components:
  - type: Flatpack
    entity: Gyroscope
  - type: Sprite
    layers:
    - state: base
    - state: overlay
      color: "#cec8ac"
    - state: icon-default

- type: entity
  parent: BaseFlatpack
  id: ThrusterFlatpack
  name: "флетпак рушійного двигуна"
  description: "Флетпак, що використовується для побудови рушія."
  components:
  - type: Flatpack
    entity: Thruster
  - type: Sprite
    layers:
    - state: base
    - state: overlay
      color: "#cec8ac"
    - state: icon-default
