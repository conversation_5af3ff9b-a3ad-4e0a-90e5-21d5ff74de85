- type: entity
  abstract: true
  parent: Clothing
  id: ClothingOuterBase
  components:
  - type: Clothing
    slots:
    - outerClothing
  - type: Sprite
    state: icon

- type: entity
  abstract: true
  parent: ClothingOuterBase
  id: ClothingOuterBaseLarge
  components:
  - type: Item
    size: Huge
  - type: Clothing
    slots:
    - outerClothing
  - type: ClothingSpeedModifier
    walkModifier: 0.9
    sprintModifier: 0.9
  - type: HeldSpeedModifier
  - type: Tag
    tags:
      - WhitelistChameleon
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 22
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  abstract: true
  parent: ClothingOuterBase
  id: ClothingOuterStorageBase
  components:
  - type: Storage
    grid:
    - 0,0,2,1
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
        ents: []
  - type: UserInterface
    interfaces:
      enum.StorageUiKey.Key:
        type: StorageBoundUserInterface
  - type: StaticPrice
    price: 70

- type: entity
  abstract: true
  parent: [ClothingOuterBase, BaseFoldable]
  id: ClothingOuterFoldableBase
  components:
  - type: Appearance
  - type: Foldable
    canFoldInsideContainer: true
    unfoldVerbText: fold-zip-verb
    foldVerbText: fold-unzip-verb
  - type: FoldableClothing
    foldedEquippedPrefix: open
    foldedHeldPrefix: open
  - type: Sprite
    layers:
    - state: icon
      map: [ "unfoldedLayer" ]
    - state: icon-open
      map: ["foldedLayer"]
      visible: false

- type: entity
  abstract: true
  parent: ClothingOuterFoldableBase
  id: ClothingOuterFoldableBaseOpened
  suffix: opened
  components:
  - type: Foldable
    folded: true
  - type: Clothing
    equippedPrefix: open
  - type: Item
    heldPrefix: open
  - type: Sprite
    layers:
    - state: icon
      map: [ "unfoldedLayer" ]
      visible: false
    - state: icon-open
      map: ["foldedLayer"]
      visible: true

- type: entity
  abstract: true
  parent: [ClothingOuterStorageBase, ClothingOuterFoldableBase]
  id: ClothingOuterStorageFoldableBase

- type: entity
  abstract: true
  parent: [ClothingOuterStorageFoldableBase, ClothingOuterFoldableBaseOpened]
  id: ClothingOuterStorageFoldableBaseOpened
  suffix: opened

- type: entity
  abstract: true
  parent: ClothingOuterStorageBase
  id: ClothingOuterStorageToggleableBase
  components:
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterDefault
    slot: head
  - type: ContainerContainer
    containers:
      toggleable-clothing: !type:Container {}
      storagebase: !type:Container
        ents: []

- type: entity
  abstract: true
  parent: [ClothingOuterBase, GeigerCounterClothing, AllowSuitStorageClothing]
  id: ClothingOuterHardsuitBase
  name: "базовий скафандр"
  components:
  - type: PressureProtection
    highPressureMultiplier: 0.3
    lowPressureMultiplier: 1000
  - type: TemperatureProtection
    coefficient: 0.01
  - type: FireProtection
    reduction: 0.75 # almost perfectly sealed, atmos firesuit is better
  - type: IgniteFromGasImmunity
    parts:
    - Torso
    - Groin
    - LeftArm
    - LeftHand
    - RightArm
    - RightHand
    - LeftLeg
    - LeftFoot
    - RightLeg
    - RightFoot
  - type: ClothingSpeedModifier
    walkModifier: 0.4
    sprintModifier: 0.6
  - type: HeldSpeedModifier
  - type: Item
    size: Ginormous
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.90
        Slash: 0.90
        Piercing: 0.95
        Heat: 0.90
        Radiation: 0.25
  - type: ToggleableClothing
    slot: head
  - type: ContainerContainer
    containers:
      toggleable-clothing: !type:Container {}
  - type: GroupExamine
  - type: Tag
    tags:
    - Hardsuit
    - WhitelistChameleon
    - HidesHarpyWings #DeltaV: Used by harpies to help render their hardsuit sprites
    - AllowLamiaHardsuit
    - FullBodyOuter
    - PlasmamanSafe
  - type: Clothing
    equipDelay: 2.5 # Hardsuits are heavy and take a while to put on/off.
    unequipDelay: 2.5
  - type: Geiger
    attachedToSuit: true
    localSoundOnly: true
  - type: StaminaDamageResistance
    coefficient: 0.75 # 25%
  - type: ClothingRequiredStepTriggerImmune
    slots: WITHOUT_POCKET
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 19
    staminaCost: 44
    soundHit:
      collection: MetalThud
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 400 # According to Google, a NASA Eva Suit weighs 68kg. Since Hardsuits are "Armored", I'm setting it to 100kg
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2
  - type: EmitsSoundOnMove
    soundCollection:
      collection: FootstepHardsuitLight
      params:
        volume: -5
    requiresWorn: true
    distanceWalking: 2
    distanceSprinting: 3
  - type: Reflect
    spread: 270
    reflectProb: 0.03
    innate: true
    reflects:
      - Energy
      - NonEnergy

- type: entity
  abstract: true
  parent: ClothingOuterHardsuitBase
  id: ClothingOuterHardsuitBaseMedium
  name: "базовий скафандр"
  components:
  - type: EmitsSoundOnMove
    soundCollection:
      collection: FootstepHardsuitMedium
      params:
        volume: -5
    requiresWorn: true
    distanceWalking: 2
    distanceSprinting: 3
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 600
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 30
    staminaCost: 60
    soundHit:
      collection: MetalThud
  - type: Clothing
    equipDelay: 4 # For stuff like standard Tacsuits and Heavy Hardsuits.
    unequipDelay: 4
  - type: Reflect
    spread: 270
    reflectProb: 0.04
    innate: true
    reflects:
      - Energy
      - NonEnergy

- type: entity
  abstract: true
  parent: ClothingOuterHardsuitBase
  id: ClothingOuterHardsuitBaseHeavy
  name: "базовий скафандр"
  components:
  - type: EmitsSoundOnMove
    soundCollection:
      collection: FootstepHardsuitHeavy
      params:
        volume: -5
    requiresWorn: true
    distanceWalking: 2
    distanceSprinting: 3
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 800
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 45
    staminaCost: 110
    soundHit:
      collection: MetalThud
  - type: Clothing
    equipDelay: 5 # For stuff like "Heavy" Tacsuits.
    unequipDelay: 5
  - type: Reflect
    spread: 270
    reflectProb: 0.05
    innate: true
    reflects:
      - Energy
      - NonEnergy

- type: entity
  abstract: true
  parent: [ClothingOuterBase, AllowSuitStorageClothing]
  id: ClothingOuterEVASuitBase
  name: "базовий скафандр EVA"
  components:
  - type: PressureProtection
    highPressureMultiplier: 0.6
    lowPressureMultiplier: 1000
  - type: TemperatureProtection
    coefficient: 0.01 # Not complete protection from fire
  - type: IgniteFromGasImmunity
    parts:
    - Torso
    - Groin
    - LeftArm
    - LeftHand
    - RightArm
    - RightHand
    - LeftLeg
    - LeftFoot
    - RightLeg
    - RightFoot
  - type: ClothingSpeedModifier
    walkModifier: 0.8
    sprintModifier: 0.8
  - type: HeldSpeedModifier
  - type: Item
    size: Huge
  - type: Tag
    tags:
    - AllowLamiaHardsuit #DeltaV: Used by Lamia to render snek hardsuits
    - HidesHarpyWings #DeltaV: Used by harpies to help render their hardsuit sprites
    - PlasmamanSafe
  - type: Clothing
    equipDelay: 1.25 # Softsuits are easier to put on and off
    unequipDelay: 1
  - type: ClothingRequiredStepTriggerImmune
    slots: WITHOUT_POCKET
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 272 # According to Google, a NASA Eva Suit weighs 68kg. Thus, my reference.
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterBaseToggleable
  name: "худі з капюшоном"
  abstract: True
  components:
  - type: ToggleableClothing
    clothingPrototype: ClothingHeadHatHoodWinterDefault
    slot: head
  - type: ContainerContainer
    containers:
      toggleable-clothing: !type:Container {}
      storagebase: !type:Container
        ents: []

- type: entity
  abstract: true
  parent: ClothingOuterBase
  id: ClothingOuterBaseMedium
  components:
  - type: Item
    size: Huge
