- type: cargoProduct
  id: ServiceJanitorial
  icon:
    sprite: Objects/Specific/Janitorial/janitorial.rsi
    state: cleaner
  product: CrateServiceJanitorialSupplies
  cost: 560
  category: cargoproduct-category-name-service
  group: market

- type: cargoProduct
  id: ServiceLightsReplacement
  icon:
    sprite: Objects/Power/light_bulb.rsi
    state: normal
  product: CrateServiceReplacementLights
  cost: 600
  category: cargoproduct-category-name-service
  group: market

- type: cargoProduct
  id: MousetrapBoxes
  icon:
    sprite: Objects/Devices/mousetrap.rsi
    state: mousetrap
  product: CrateMousetrapBoxes
  cost: 500
  category: cargoproduct-category-name-service
  group: market

- type: cargoProduct
  id: ServiceTheatre
  icon:
    sprite: Clothing/Mask/mime.rsi
    state: icon
  product: CrateServiceTheatre
  cost: 1800
  category: cargoproduct-category-name-service
  group: market

- type: cargoProduct
  id: ServiceSmokeables
  icon:
    sprite: Objects/Consumable/Smokeables/Cigarettes/Cartons/green.rsi
    state: closed
  product: CrateServiceSmokeables
  cost: 1500
  category: cargoproduct-category-name-service
  group: market

- type: cargoProduct
  id: ServiceCustomSmokable
  icon:
    sprite: Objects/Consumable/Smokeables/Cigarettes/Cartons/green.rsi
    state: closed
  product: CrateServiceCustomSmokable
  cost: 1000
  category: cargoproduct-category-name-service
  group: market

- type: cargoProduct
  id: ServiceBureaucracy
  icon:
    sprite: Objects/Misc/bureaucracy.rsi
    state: pen
  product: CrateServiceBureaucracy
  cost: 1000
  category: cargoproduct-category-name-service
  group: market

- type: cargoProduct
  id: ServicePersonnel
  icon:
    sprite: Objects/Misc/id_cards.rsi
    state: default
  product: CrateServicePersonnel
  cost: 1000
  category: cargoproduct-category-name-service
  group: market

- type: cargoProduct
  id: ServiceBooks
  icon:
    sprite: Objects/Misc/books.rsi
    state: book_icon
  product: CrateServiceBooks
  cost: 1000
  category: cargoproduct-category-name-service
  group: market

- type: cargoProduct
  id: ServiceGuidebooks
  icon:
    sprite: Objects/Misc/books.rsi
    state: book_icon
  product: CrateServiceGuidebooks
  cost: 1300
  category: cargoproduct-category-name-service
  group: market

- type: cargoProduct
  id: ServiceSodaDispenser
  icon:
    sprite: Objects/Consumable/Drinks/generic_jug.rsi
    state: icon
  product: CrateServiceSodaDispenser
  cost: 850
  category: cargoproduct-category-name-service
  group: market

- type: cargoProduct
  id: ServiceBoozeDispenser
  icon:
    sprite: Objects/Consumable/Drinks/generic_jug.rsi
    state: icon
  product: CrateServiceBoozeDispenser
  cost: 750
  category: cargoproduct-category-name-service
  group: market

- type: cargoProduct
  id: ServiceBoxes
  icon:
    sprite: Objects/Storage/boxes.rsi
    state: box
  product: CrateServiceBox
  cost: 400
  category: cargoproduct-category-name-service
  group: market

- type: cargoProduct
  id: ServiceJanitorBiosuit
  icon:
    sprite: Clothing/Head/Hoods/Bio/janitor.rsi
    state: icon
  product: CrateJanitorBiosuit
  cost: 800
  category: cargoproduct-category-name-service
  group: market

- type: cargoProduct
  id: ServiceFoodCartHot
  icon:
    sprite: Objects/Specific/Kitchen/food_carts.rsi
    state: icon-hot
  product: FoodCartHot
  cost: 2000
  category: cargoproduct-category-name-service
  group: market

- type: cargoProduct
  id: ServiceFoodCartCold
  icon:
    sprite: Objects/Specific/Kitchen/food_carts.rsi
    state: icon-cold
  product: FoodCartCold
  cost: 2000
  category: cargoproduct-category-name-service
  group: market

- type: cargoProduct
  id: ServiceAnimalCarrier
  icon:
    sprite: Objects/Storage/petcarrier.rsi
    state: icon
  product: PetCarrier
  cost: 500
  category: cargoproduct-category-name-service
  group: market

- type: cargoProduct
  id: JanitorExplosive
  icon:
    sprite: Clothing/Head/Helmets/janitor_bombsuit.rsi
    state: icon
  product: CrateJanitorExplosive
  cost: 1000
  category: cargoproduct-category-name-service
  group: market

- type: cargoProduct
  id: ServiceJanitorTrolley
  icon:
    sprite: Objects/Specific/Janitorial/janitorial_cart.rsi
    state: icon-cart
  product: JanitorialTrolley
  cost: 300
  category: cargoproduct-category-name-service
  group: market

