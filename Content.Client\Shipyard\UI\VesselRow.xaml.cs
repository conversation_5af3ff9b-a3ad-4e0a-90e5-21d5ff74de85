using Content.Shared.Shipyard.Prototypes;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Utility;

namespace Content.Client.Shipyard.UI;

[GenerateTypedNameReferences]
public sealed partial class VesselRow : PanelContainer
{
    public event Action? OnPurchasePressed;

    public VesselRow(VesselPrototype vessel, bool access)
    {
        RobustXamlLoader.Load(this);

        VesselName.Text = vessel.Name;

        var tooltip = new Tooltip();
        tooltip.SetMessage(FormattedMessage.FromMarkup(vessel.Description));
        Purchase.TooltipSupplier = _ => tooltip;
        Purchase.Disabled = !access;
        Purchase.OnPressed += _ => OnPurchasePressed?.Invoke();

        Price.Text = Loc.GetString("cargo-console-menu-points-amount", ("amount", vessel.Price));
    }
}
