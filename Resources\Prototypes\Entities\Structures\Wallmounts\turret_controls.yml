- type: entity
  id: WeaponEnergyTurretControlPanelFrame
  name: "збірка панелі керування туреллю"
  description: "Незавершена настінна збірка для панелі керування сторожовою туреллю."
  categories: [ HideSpawnMenu ]
  components:

  # Sprites and appearance
  - type: Sprite
    noRot: false
    drawdepth: SmallObjects
    sprite: Structures/Wallmounts/turret_controls.rsi
    layers:
    - state: base

  # Destruction
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: StructuralMetallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
          params:
            volume: -4
      - !type:ChangeConstructionNodeBehavior
        node: machineFrame
      - !type:DoActsBehavior
        acts: [ "Destruction" ]

  # General properties
  - type: Transform
    anchored: true
  - type: WallMount
  - type: Clickable
  - type: InteractionOutline
  - type: Construction
    graph: WeaponEnergyTurretControlPanel
    node: frame
  placement:
    mode: SnapgridCenter
    snap:
    - Wallmount

- type: entity
  parent: WeaponEnergyTurretControlPanelFrame
  id: WeaponEnergyTurretStationControlPanel
  name: "панель керування туреллю"
  description: "Настінний інтерфейс для дистанційного налаштування робочих параметрів підключених сторожових турелей."
  components:

  # Sprites and appearance
  - type: Appearance
  - type: Sprite
    noRot: false
    drawdepth: SmallObjects
    sprite: Structures/Wallmounts/turret_controls.rsi
    layers:
    - state: base
    - state: safe
      map: ["enum.PowerDeviceVisualLayers.Powered"]
      shader: unshaded
    - state: wires
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
      visible: false
  - type: GenericVisualizer
    visuals:
      enum.WiresVisualLayers.MaintenancePanel:
        enum.WiresVisualLayers.MaintenancePanel:
          True: { visible: true }
          False: { visible: false }
      enum.PowerDeviceVisuals.Powered:
        enum.PowerDeviceVisualLayers.Powered:
          True: { visible: true }
          False: { visible: false }
      enum.TurretControllerVisuals.ControlPanel:
        enum.PowerDeviceVisualLayers.Powered:
          -1: { state: safe }
          0: { state: stun }
          1: { state: lethal }

  # Faction / control
  - type: StationAiWhitelist
  - type: AccessReader
    access: [["Security"]]

  # Turret related
  - type: TurretTargetSettings
    exemptAccessLevels:
    - Captain
    - HeadOfSecurity
    - Security
    - Borg
    - BasicSilicon
  - type: DeployableTurretController
    accessGroups:
    - Cargo
    - Command
    - Engineering
    - General
    - Medical
    - Research
    - Security
    - Service
    - Silicon
    accessLevels:
    - Armory
    - Atmospherics
    - Bar
    - BasicSilicon
    - Borg
    - Brig
    - Detective
    - Captain
    - Cargo
    - Chapel
    - Chemistry
    - ChiefEngineer
    - ChiefMedicalOfficer
    - Command
    - Cryogenics
    - Engineering
    - External
    - HeadOfPersonnel
    - HeadOfSecurity
    - Hydroponics
    - Janitor
    - Kitchen
    - Lawyer
    - Maintenance
    - Medical
    - Quartermaster
    - Research
    - ResearchDirector
    - Salvage
    - Security
    - Service
    - Theatre

  # Device network
  - type: DeviceList
    isAllowList: true
  - type: DeviceNetwork
    deviceNetId: Wired
    receiveFrequencyId: Turret
    transmitFrequencyId: TurretControl
    sendBroadcastAttemptEvent: true
    prefix: device-address-prefix-console
  - type: DeviceNetworkRequiresPower
  - type: WiredNetworkConnection

  # UI
  - type: ActivatableUI
    key: enum.DeployableTurretControllerUiKey.Key
  - type: ActivatableUIRequiresPower
  - type: UserInterface
    interfaces:
      enum.DeployableTurretControllerUiKey.Key:
        type: TurretControllerBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface

  # Wires
  - type: WiresPanel
  - type: WiresVisuals
  - type: Wires
    boardName: wires-board-name-turret-controls
    layoutId: TurretControls
  - type: Lock
    locked: true
    unlockOnClick: false
  - type: LockedWiresPanel

  # General properties
  - type: ApcPowerReceiver
  - type: ExtensionCableReceiver
  - type: Electrified
    enabled: false
    usesApcPower: true
  - type: ContainerFill
    containers:
      board: [ WeaponEnergyTurretStationControlPanelElectronics ]
  - type: ContainerContainer
    containers:
      board: !type:Container
  - type: Construction
    graph: WeaponEnergyTurretControlPanel
    node: finish

- type: entity
  parent: WeaponEnergyTurretStationControlPanel
  id: WeaponEnergyTurretAIControlPanel
  name: "панель керування туреллю ШІ"
  description: "Настінний інтерфейс, що дозволяє локальному штучному інтелекту налаштовувати робочі параметри підключених сторожових турелей."
  components:
  - type: AccessReader
    access: [["StationAi"], ["Captain"], ["ResearchDirector"]]
  - type: TurretTargetSettings
    exemptAccessLevels:
    - Captain
    - ResearchDirector
    - BasicSilicon
    - Borg
  - type: DeviceNetwork
    receiveFrequencyId: TurretAI
    transmitFrequencyId: TurretControlAI
  - type: ContainerFill
    containers:
      board: [ WeaponEnergyTurretAIControlPanelElectronics ]
