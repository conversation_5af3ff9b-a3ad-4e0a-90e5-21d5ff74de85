- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesChef
  name: "взуття шеф-кухара"
  description: "Міцне взуття, яке мінімізує травми від падаючих предметів або ножів."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Specific/chef.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Specific/chef.rsi

# stuff common to all clown & jester shoes
- type: entity
  abstract: true
  parent: [ClothingShoesBaseButcherable, ClothingSlotBase]
  id: ClothingShoesClownBase
  components:
  - type: WaddleWhenWorn
  - type: ItemSlots
    slots:
      item:
        name: clothing-boots-sidearm
        whitelist:
          tags:
          - Knife
          - ToySidearm
        blacklist:
          components:
          - Sharp

- type: entity
  parent: ClothingShoesClownBase
  id: ClothingShoesClown
  name: "клоунські черевики"
  description: "Стандартне клоунське взуття жартівника. Дідько, вони величезні!"
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Specific/clown.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Specific/clown.rsi
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepClown
  # for H.O.N.K. construction
  - type: Tag
    tags:
    - ClownShoes
    - WhitelistChameleon

- type: entity
  parent: ClothingShoesClown
  id: ClothingShoesClownBanana
  name: "бананові клоунські черевики"
  description: "Коли гумор і взуття поєднуються на нових рівнях абсурду."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Specific/clown_banana.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Specific/clown_banana.rsi
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepSlip
  - type: Construction
    graph: BananaClownShoes
    node: shoes
  - type: Skates
    minimumSpeed: 20
    acceleration: 5

- type: entity
  parent: ClothingShoesClownBase
  id: ClothingShoesBling
  name: "блискучі клоунські черевики"
  description: "Виготовлені з переробленого бананіуму і сяють м'якоттю свіжої бананової шкірки. Вони роблять яскраву заяву."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Specific/bling.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Specific/bling.rsi
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepClown
  - type: PointLight
    enabled: true
    radius: 3
    energy: 2
    color: "#FFD800"
    netsync: false

- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesCult
  name: "культове взуття"
  description: "Пара чобіт, які носять послідовники Нар'Сі."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Specific/cult.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Specific/cult.rsi

- type: entity
  parent: ClothingShoesBase
  id: ClothingShoesGaloshes
  name: "калоші"
  description: "Гумові чоботи."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Specific/galoshes.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Specific/galoshes.rsi
  - type: NoSlip

- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesSpaceNinja
  name: "взуття космічного ніндзя"
  description: "Пара нано-вдосконалених черевиків з вбудованими магнітними присосками."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Specific/spaceninja.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Specific/spaceninja.rsi
  - type: NoSlip
  - type: ClothingSpeedModifier
    # ninja are masters of sneaking around relatively quickly, won't break cloak
    walkModifier: 1.1
    sprintModifier: 1.3
  - type: Unremoveable

- type: entity
  parent: ClothingShoesMilitaryBase
  id: ClothingShoesSwat
  name: "взуття спецназу"
  description: "Коли хочеться додати жару."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Specific/swat.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Specific/swat.rsi
  - type: ClothingSlowOnDamageModifier
    modifier: 0.7

- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesWizard
  name: "черевики чарівника"
  description: "Пара чарівних черевиків."
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Specific/wizard.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Specific/wizard.rsi

- type: entity
  parent: ClothingShoesBase
  id: ClothingShoesChameleon
  name: "чорні туфлі"
  suffix: Chameleon
  description: "Стильні чорні туфлі."
  components:
    - type: Tag
      tags: [] # ignore "WhitelistChameleon" tag
    - type: Sprite
      sprite: Clothing/Shoes/color.rsi
      layers:
      - state: icon
        color: "#3f3f3f"
      - state: soles-icon
    - type: Clothing
      sprite: Clothing/Shoes/color.rsi
      clothingVisuals:
        shoes:
        - state: equipped-FEET
          color: "#1d1d1d"
        - state: soles-equipped-FEET
    - type: Item
      inhandVisuals:
        left:
        - state: inhand-left
          color: "#3f3f3f"
        - state: soles-inhand-left
        right:
        - state: inhand-right
          color: "#3f3f3f"
        - state: soles-inhand-right
    - type: ChameleonClothing
      slot: [FEET]
      default: ClothingShoesColorBlack
    - type: UserInterface
      interfaces:
        enum.ChameleonUiKey.Key:
          type: ChameleonBoundUserInterface

- type: entity
  parent: ClothingShoesChameleon
  id: ClothingShoesChameleonNoSlips
  name: "чорні туфлі" #actual name and description in uplink_catalog.yml
  suffix: No-slip, Chameleon
  description: "Стильні чорні туфлі."
  components:
  - type: NoSlip

- type: entity
  parent: ClothingShoesClown
  id: ClothingShoesJester
  name: "блазнівські черевики"
  description: "Взуття придворного блазня, оновлене за допомогою сучасної технології пищання."
  components:
    - type: Sprite
      sprite: Clothing/Shoes/Specific/jester.rsi
    - type: Clothing
      sprite: Clothing/Shoes/Specific/jester.rsi
    - type: FootstepModifier
      footstepSoundCollection:
        collection: FootstepJester

- type: entity
  parent: ClothingShoesClown
  id: ClothingShoesCluwne
  name: "взуття клувні"
  suffix: Unremoveable
  description: "Проклята пара туфель клувні"
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Specific/cluwne.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Specific/cluwne.rsi
  - type: Unremoveable

- type: entity
  parent: ClothingShoesClown
  id: ClothingShoesClownLarge
  name: "великі клоунські черевики"
  description: "Коли вам потрібно виділитися в кімнаті, повній клоунів!"
  components:
  - type: Sprite
    state: "icon"
    sprite: Clothing/Shoes/Specific/large_clown.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Specific/large_clown.rsi
    clothingVisuals:
      shoes:
      - state: equipped-FEET
        offset: "0, -0.02"
  - type: Item
    size: Small
    sprite: Clothing/Shoes/Specific/large_clown.rsi
  - type: ClothingSpeedModifier
    walkModifier: 0.85
    sprintModifier: 0.8

- type: entity
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesSkates
  name: "роликові ковзани"
  description: "Одягайте ковзани!"
  components:
  - type: Sprite
    sprite: Clothing/Shoes/Specific/skates.rsi
  - type: Clothing
    sprite: Clothing/Shoes/Specific/skates.rsi
    clothingVisuals:
      shoes:
      - state: equipped-FEET
        offset: "0, -0.04"
  - type: Item
    sprite: Clothing/Shoes/Specific/skates.rsi
  - type: ClothingSpeedModifier
    walkModifier: 1.2 # Goob edit
    sprintModifier: 1.2 # Goob edit
  - type: Skates
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepSkates
