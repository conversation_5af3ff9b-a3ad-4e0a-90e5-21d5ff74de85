## UI

research-console-menu-title = R&D Console
research-console-menu-research-points-text = Research Points: [color=orchid]{$points}[/color]
research-console-menu-main-discipline = Main Discipline: [color={$color}]{$name}[/color]
research-console-menu-server-selection-button = Server list
research-console-menu-server-sync-button = Sync
research-console-menu-server-research-button = Research
research-console-available-text = Researchable Technologies
research-console-unlocked-text = Unlocked Technologies
research-console-tier-discipline-info = Tier {$tier}, [color={$color}]{$discipline}[/color]
research-console-tier-info-small = : Tier {$tier}
research-console-cost = Cost: [color=orchid]{$amount}[/color]
research-console-unlocks-list-start = Unlocks:
research-console-unlocks-list-entry = - [color=yellow]{$name}[/color]
research-console-unlocks-list-entry-generic = - [color=green]{$text}[/color]
research-console-prereqs-list-start = Requires:
research-console-prereqs-list-entry = - [color=orchid]{$text}[/color]

research-console-no-access-popup = No access!
research-console-unlock-technology-radio-broadcast = Unlocked [bold]{$technology}[/bold] for [bold]{$amount}[/bold] research by [bold]{$approver}[/bold].
