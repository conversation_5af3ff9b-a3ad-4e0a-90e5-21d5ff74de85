- type: weightedRandom
  id: AsteroidOre
  weights:
    OreIron: 1.0
    OreQuartz: 1.0
    OreCoal: 1.0
    OreSalt: 1.0
    OreGold: 0.25
    OreDiamond: 0.05
    OreSilver: 0.25
    OrePlasma: 0.15
    OreUranium: 0.15
    OreArtifactFragment: 0.15
    OreBluespace: 0.1
    OreNormality: 0.1

# Large asteroids, typically 1
- type: dungeonConfig
  id: BlobAsteroid
  # Floor generation
  generator: !type:NoiseDunGen
    tileCap: 1500
    capStd: 32
    iterations: 3
    layers:
      - tile: FloorAsteroidSand
        threshold: 0.30
        noise:
          frequency: 0.020
          noiseType: OpenSimplex2
          fractalType: FBm
          octaves: 2
          lacunarity: 2
  # Everything else
  postGeneration:
    # Generate biome
    - !type:BiomePostGen
      biomeTemplate: Asteroid

    # Generate ore veins
    - !type:MarkerLayerPostGen
      markerTemplate: AsteroidOre

# Multiple smaller asteroids
# This is a pain so we generate fewer tiles
- type: dungeonConfig
  id: ClusterAsteroid
  # Floor generation
  generator: !type:NoiseDunGen
    tileCap: 1000
    capStd: 32
    layers:
      - tile: FloorAsteroidSand
        threshold: 0.10
        noise:
          frequency: 0.130
          noiseType: OpenSimplex2
          fractalType: FBm
          octaves: 2
          lacunarity: 2
  # Everything else
  postGeneration:
    # Generate biome
    - !type:BiomePostGen
      biomeTemplate: Asteroid

    # Generate ore veins
    - !type:MarkerLayerPostGen
      markerTemplate: AsteroidOre

# Long and spindly, less smooth than blob
- type: dungeonConfig
  id: SpindlyAsteroid
  # Floor generation
  generator: !type:NoiseDunGen
    tileCap: 1500
    capStd: 32
    layers:
      - tile: FloorAsteroidSand
        threshold: -0.50
        noise:
          frequency: 0.055
          noiseType: Cellular
          fractalType: FBm
          octaves: 3
          lacunarity: 2
          cellularDistanceFunction: Euclidean
  postGeneration:
    # Generate biome
    - !type:BiomePostGen
      biomeTemplate: Asteroid

    # Generate ore veins
    - !type:MarkerLayerPostGen
      markerTemplate: AsteroidOre

# Lots of holes in it
- type: dungeonConfig
  id: SwissCheeseAsteroid
  # Floor generation
  generator: !type:NoiseDunGen
    tileCap: 1500
    capStd: 32
    layers:
      - tile: FloorAsteroidSand
        threshold: -0.10
        noise:
          frequency: 0.155
          noiseType: OpenSimplex2
          fractalType: FBm
          octaves: 2
          lacunarity: 2
  # Everything else
  postGeneration:
    # Generate biome
    - !type:BiomePostGen
      biomeTemplate: Asteroid

    # Generate ore veins
    - !type:MarkerLayerPostGen
      markerTemplate: AsteroidOre
