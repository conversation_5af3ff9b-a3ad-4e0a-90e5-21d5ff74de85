- type: gameMap
  id: Pebble
  mapName: Пеббл
  mapPath: /Maps/pebble.yml
  minPlayers: 0
  stations:
    PebbleStation:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: '{0} Pebble Station {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: 'NYA'
#        - type: GridSpawn
#          groups:
#            AISAT:
#              paths:
#              - /Maps/Shuttles/AISAT.yml
        - type: StationJobs
          overflowJobs:
            - Passenger
          availableJobs:
          #service
            Captain: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1]
            NanotrasenRepresentative: [ 1, 1 ]
            Magistrate: [ 1, 1 ]
            AdministrativeAssistant: [ 1, 1 ]
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 1, 2 ]
            Botanist: [ 2, 2 ]
            Boxer: [ 2, 2 ]
            Chef: [ 2 , 2 ]
            Clown: [ 1, 1 ]
            Lawyer: [ 1, 1 ]
            Musician: [ 1, 1 ]
            Janitor: [ 1, 2 ]
            Mime: [ 1, 1 ]
          #engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 1, 1]
            SeniorEngineer: [ 1, 1 ]
            StationEngineer: [ 2, 3 ]
            TechnicalAssistant: [ 2, 2 ]
          #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            Chemist: [ 1, 1 ]
            SeniorPhysician: [ 1, 1 ]
            MedicalDoctor: [ 2, 3 ]
            MedicalIntern: [ 2, 2 ]
            Paramedic: [ 1, 1 ]
            Psychologist: [ 1, 1 ]
          #science
            ResearchDirector: [ 1, 1 ]
            Roboticist: [ 1, 1 ] #PIRATE
            Chaplain: [ 1, 1 ]
            ForensicMantis: [ 1, 1 ]
            SeniorResearcher: [ 1, 1]
            Scientist: [ 2, 3 ]
            ResearchAssistant: [ 2, 2 ]
            Borg: [ 1, 1 ]
          #security
            HeadOfSecurity: [ 1, 1 ]
            Warden: [ 1, 1 ]
            Detective: [ 1, 1 ]
            SeniorOfficer: [ 1, 1 ]
            SecurityOfficer: [ 2, 2 ]
            SecurityCadet: [ 1, 1 ]
          #supply
            Quartermaster: [ 1, 1 ]
            MailCarrier: [ 1, 2 ]
            SalvageSpecialist: [ 2, 2 ]
            CargoTechnician: [ 2, 3 ]
          #civilian
            Passenger: [ -1, -1 ]
          # Silicon
            StationAi: [ 1, 1 ]
        # blob-config-start SMALL+
        - type: StationBlobConfig
          stageBegin: 25
          stageCritical: 350
          stageTheEnd: 700
        # blob-config-end
