- type: entity
  parent: RingBase
  id: GoldRing
  name: "золота каблучка"
  description: "Дорогоцінна каблучка."
  components:
    - type: Sprite
      sprite: Clothing/Hands/Rings/basic.rsi
      layers:
        - state: ring
          color: "#ffc833"
    - type: StaticPrice
      price: 300
    - type: Appearance

- type: entity
  parent: RingBase
  id: SilverRing
  name: "срібна каблучка"
  description: "Виглядає трохи менш цінною, ніж золота."
  components:
    - type: Sprite
      sprite: Clothing/Hands/Rings/basic.rsi
      layers:
        - state: ring
    - type: StaticPrice
      price: 275
    - type: Appearance

- type: entity
  parent: RingBase
  id: GoldRingDiamond
  name: "золота каблучка з діамантом"
  description: "Зроблено з етично видобутих космічних діамантів."
  components:
    - type: Sprite
      sprite: Clothing/Hands/Rings/basic.rsi
      layers:
        - state: ring
          color: "#ffc833"
        - state: gem
          color: "#c1ffff"
    - type: StaticPrice
      price: 1500
    - type: Appearance

- type: entity
  parent: RingBase
  id: SilverRingDiamond
  name: "срібна каблучка з діамантом"
  description: "Зроблено з етично видобутих космічних діамантів."
  components:
    - type: Sprite
      sprite: Clothing/Hands/Rings/basic.rsi
      layers:
        - state: ring
        - state: gem
          color: "#c1ffff"
    - type: StaticPrice
      price: 1400
    - type: Appearance

- type: entity
  parent: RingBase
  id: GoldRingGem
  name: "золота каблучка з коштовним каменем"
  description: "Блискуча та цінна!"
  components:
    - type: Sprite
      sprite: Clothing/Hands/Rings/basic.rsi
      layers:
        - state: ring
          color: "#ffc833"
        - state: gem
          map: [ "gemColor" ]
    - type: StaticPrice
      price: 2100
    - type: Appearance
    - type: RandomSprite
      getAllGroups: true
      available:
        - gemColor:
            gem: Rainbow

- type: entity
  parent: RingBase
  id: SilverRingGem
  name: "срібна каблучка з коштовним каменем"
  description: "Блискуча і не така вже й цінна!"
  components:
    - type: Sprite
      sprite: Clothing/Hands/Rings/basic.rsi
      layers:
        - state: ring
        - state: gem
          map: [ "gemColor" ]
    - type: StaticPrice
      price: 2000
    - type: Appearance
    - type: RandomSprite
      getAllGroups: true
      available:
        - gemColor:
            gem: Rainbow
