- type: entity
  id: MobRevenant
  parent:
  - BaseMob
  - Incorporeal
  name: "ревенант"
  description: "Моторошний привид."
  components:
  - type: Input
    context: "ghost"
  - type: MovementSpeedModifier
    baseWalkSpeed: 6
    baseSprintSpeed: 6
  - type: Sprite
    noRot: true
    drawdepth: Ghosts
    sprite: DeltaV/Mobs/Ghosts/revenant.rsi # DeltaV - Custom revenant sprite
    layers:
    - state: active
  - type: Reactive # Nyanotrasen - Holy Water affects Revenants
    groups:
      Acidic: [Touch]
  - type: Clickable
  - type: StatusEffects
    allowed:
    - Stun
    - Corporeal
  - type: InteractionOutline
  - type: Physics
    bodyType: KinematicController
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.40
        density: 80
        mask:
        - GhostImpassable
  - type: MovementIgnoreGravity
  - type: Damageable # Nyanotrasen - Corporeal Spirit allows Holy water to do damage
    damageContainer: CorporealSpirit
    damageModifierSet: CorporealSpirit
  - type: Examiner
  - type: NoSlip
  - type: Eye
    drawFov: false
    visMask:
      - Normal
      - Ghost
  - type: ContentEye
    maxZoom: 1.2, 1.2
  - type: NameIdentifier
    group: GenericNumber
  - type: GhostRole
    makeSentient: true
    name: ghost-role-information-revenant-name
    description: ghost-role-information-revenant-description
    rules: ghost-role-information-antagonist-rules
    mindRoles:
    - MindRoleGhostRoleSoloAntagonist
    raffle:
      settings: default
  - type: GhostTakeoverAvailable
  - type: Revenant
    malfunctionWhitelist:
      components:
      # emag lockers open
      - EntityStorage
      # emag doors open
      - Airlock
      # troll medical to help get kills
      - StasisBed
      - EmaggableMedibot
      # borg become killer
      - EmagSiliconLaw
  - type: PointLight
    color: MediumPurple
    radius: 2
    softness: 1
  - type: UserInterface
    interfaces:
      enum.StoreUiKey.Key:
        type: StoreBoundUserInterface
  - type: Visibility
    layer: 2 #ghost vis layer
  - type: Store
    categories:
    - RevenantAbilities
    currencyWhitelist:
    - StolenEssence
  - type: RandomMetadata
    nameSegments:
    - The
    - RevenantType
    - of
    - RevenantAdjective
    - RevenantTheme
  - type: Speech
    speechVerb: Ghost
  - type: Psionic
    removable: false
    roller: false
    psychognomicDescriptors:
      - p-descriptor-vampiric
  - type: InnatePsionicPowers
    powersToAdd:
      - XenoglossyPower
      - TelepathyPower
  - type: LanguageSpeaker
  - type: UniversalLanguageSpeaker
  - type: Tag
    tags:
      - NoPaint
