- type: constructionGraph
  id: Grille
  start: start
  graph:
    - node: start
      edges:
        - to: grille
          completed:
            - !type:SnapToGrid
              southRotation: true
          steps:
            - material: MetalRod
              amount: 2
              doAfter: 1

    - node: grille
      entity: Grille
      edges:
        - to: start
          completed:
            - !type:AdminLog # Needs a log for start of attempt in addition to the completion log
              message: "A grille was cut"
            - !type:SpawnPrototype
              prototype: PartRodMetal1
              amount: 2
            - !type:DeleteEntity
          steps:
            - tool: Cutting
              doAfter: 0.25

    - node: grilleBroken
      entity: GrilleBroken
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: PartRodMetal1
              amount: 1
            - !type:DeleteEntity
          steps:
            - tool: Cutting
        - to: grille
          steps:
            - material: MetalRod
              amount: 1
              doAfter: 0.5
