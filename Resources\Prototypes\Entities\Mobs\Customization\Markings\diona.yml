- type: marking
  id: DionaThornsHead
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: thorns_head

- type: marking
  id: DionaThornsBody
  bodyPart: Chest
  markingCategory: Chest
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: thorns_body

- type: marking
  id: DionaFlowersHead
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: flowers_head

- type: marking
  id: DionaFlowersBody
  bodyPart: Chest
  markingCategory: Chest
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: flowers_body

- type: marking
  id: DionaLeafCover
  bodyPart: Chest
  markingCategory: Chest
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SkinColoring
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: leaf_cover

- type: marking
  id: DionaBloomHead
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: bloom

- type: marking
  id: DionaBracketHead
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: bracket

- type: marking
  id: DionaBrushHead
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SkinColoring
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: brush

- type: marking
  id: DionaCornflowerHead
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: cornflower

- type: marking
  id: DionaFicusHead
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: ficus

- type: marking
  id: DionaGarlandHead
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: garland

- type: marking
  id: DionaKingHead
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: king

- type: marking
  id: DionaLaurelHead
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: laurel

- type: marking
  id: DionaLeafyHeadTop
  bodyPart: HeadTop
  markingCategory: HeadTop
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: leafy

- type: marking
  id: DionaLotusHead
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: lotus

- type: marking
  id: DionaMeadowHeadTop
  bodyPart: HeadTop
  markingCategory: HeadTop
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: meadow

- type: marking
  id: DionaOakHead
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: oak

- type: marking
  id: DionaPalmHead
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: palm

- type: marking
  id: DionaRootHead
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: root

- type: marking
  id: DionaRoseHead
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: rose

- type: marking
  id: DionaRoseyHead
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: rosey

- type: marking
  id: DionaShrubHeadTop
  bodyPart: HeadTop
  markingCategory: HeadTop
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: shrub

- type: marking
  id: DionaSpinnerHeadSide
  bodyPart: HeadSide
  markingCategory: HeadSide
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: spinner

- type: marking
  id: DionaSproutHeadSide
  bodyPart: HeadSide
  markingCategory: HeadSide
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: sprout

- type: marking
  id: DionaVineHeadTop
  bodyPart: HeadTop
  markingCategory: HeadTop
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: vine

- type: marking
  id: DionaVinelHead
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: vinel

- type: marking
  id: DionaVinesHead
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: vines

- type: marking
  id: DionaWildflowerHead
  bodyPart: Head
  markingCategory: Head
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
          color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: wildflower

- type: marking
  id: DionaVineOverlay
  bodyPart: LLeg
  markingCategory: Overlay
  speciesRestriction: [Diona]
  coloring:
    default:
      type:
        !type:SimpleColoring
        color: "#5f7039"
  sprites:
  - sprite: Mobs/Customization/diona.rsi
    state: overlay
