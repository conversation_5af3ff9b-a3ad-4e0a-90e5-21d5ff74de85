- type: entity
  name: "мішок для сміття"
  id: TrashBag
  parent: BaseStorageItem
  components:
  - type: Sprite
    sprite: Objects/Specific/Janitorial/trashbag.rsi
    layers:
      - state: icon-0
        map: ["enum.StorageFillLayers.Fill"]
  - type: Storage
    maxItemSize: Normal
    grid:
    - 0,0,7,5
    quickInsert: true
    areaInsert: true
    storageOpenSound:
      collection: trashBagRustle
    storageInsertSound:
      collection: trashBagRustle
    whitelist:
      tags:
        - Cartridge
        - Trash
  - type: UseDelay
    delay: 0.5
  - type: Tag
    tags:
    - TrashBag
  - type: Appearance
  - type: StorageFillVisualizer
    maxFillLevels: 4
    fillBaseName: icon
  - type: Dumpable
  - type: Clothing
    slots: [belt]
    sprite: Objects/Specific/Janitorial/trashbag.rsi
  - type: Item
    size: Normal

- type: entity
  name: "мішок для сміття"
  id: TrashBagBlue
  parent: TrashBag
  components:
  - type: Sprite
    layers:
      - state: blue-icon-0
        map: ["enum.StorageFillLayers.Fill"]
  - type: Item
    heldPrefix: blue
  - type: StorageFillVisualizer
    fillBaseName: blue-icon

- type: entity
  name: "заклинання всепоглинаючої чистоти"
  id: BagOfSummoningGarbage
  parent: TrashBagBlue
  components:
  - type: Storage
    maxItemSize: Huge
    grid:
    - 0,0,19,9
    quickInsert: true
    areaInsert: true
    areaInsertRadius: 5
