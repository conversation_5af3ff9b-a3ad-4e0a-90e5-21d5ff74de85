- type: entity
  id: Jukebox
  name: "музичний автомат"
  parent: [ BaseMachinePowered, ConstructibleMachine ]
  description: "<PERSON><PERSON><PERSON><PERSON><PERSON>, здатна відтворювати найрізноманітніші мелодії. Задоволення не гарантується."
  components:
  - type: StationAiWhitelist
  - type: Sprite
    sprite: Structures/Machines/jukebox.rsi
    layers:
    - state: "off"
      map: ["enum.JukeboxVisualLayers.Base"]
  - type: Transform
    anchored: true
  - type: Jukebox
    onState: on
    offState: off
    selectState: select
  - type: Machine
    board: JukeboxCircuitBoard
  - type: Appearance
  - type: ApcPowerReceiver
    powerLoad: 100
  - type: ExtensionCableReceiver
  - type: ActivatableUI
    key: enum.JukeboxUiKey.Key
  - type: ActivatableUIRequiresPower
  - type: UserInterface
    interfaces:
        enum.JukeboxUiKey.Key:
          type: JukeboxBoundUserInterface
        enum.WiresUiKey.Key:
          type: WiresBoundUserInterface
  - type: WiresPanel
  - type: Wires
    boardName: wires-board-name-jukebox
    layoutId: Jukebox
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 75
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 2
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.45,0.25,0.45"
        mask:
        - MachineMask
        layer:
        - MachineLayer
        density: 200
