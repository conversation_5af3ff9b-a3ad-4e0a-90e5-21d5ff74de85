using System.Text.RegularExpressions;
using Content.Client.Message;
using Content.Shared._Pirate.Banking;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Timing;

namespace Content.Client._Pirate.Banking.UI;

[GenerateTypedNameReferences]
public sealed partial class BankUiFragment : BoxContainer
{
    public Action<BankAccountLinkMessage>? OnLinkAttempt;

    private bool _accountLinkActive;

    private readonly string _lineEditPattern = "[^0-9]";

    public BankUiFragment()
    {
        RobustXamlLoader.Load(this);

        AccountLinkButton.OnPressed += _ =>
        {
            _accountLinkActive = true;
            AccountLinkResultLabel.Visible = false;
            UpdateAccountLinkUi();
        };

        LinkCancelButton.OnPressed += _ =>
        {
            _accountLinkActive = false;
            UpdateAccountLinkUi();
        };

        PinLineEdit.OnTextChanged += _ =>
        {
            ValidateLineEdit(PinLineEdit, 4);
        };

        AccountLineEdit.OnTextChanged += _ =>
        {
            ValidateLineEdit(AccountLineEdit, 6);
        };

        LinkConfirmButton.OnPressed += _ =>
        {
            if (PinLineEdit.Text.Length != 4 || AccountLineEdit.Text.Length != 6)
                return;
            var accountId = int.Parse((string) AccountLineEdit.Text);
            var pin = int.Parse((string) PinLineEdit.Text);
            AccountLinkResultLabel.Visible = true;
            _accountLinkActive = false;
            OnLinkAttempt?.Invoke(new BankAccountLinkMessage(accountId, pin));
        };
    }

    public void UpdateState(BankCartridgeUiState state)
    {
        var accountLinked = state.AccountId != null;

        RichTextLabelExt.SetMarkup(AccountLinkMessageLabel, state.AccountLinkMessage);
        RichTextLabelExt.SetMarkup(AccountLinkResultLabel, state.AccountLinkResult);

        LinkedAccount.Visible = accountLinked;
        NoLinkedAccountLabel.Visible = !accountLinked;

        if (accountLinked)
        {
            RichTextLabelExt.SetMarkup(LinkedAccountNumberLabel, Loc.GetString("bank-program-ui-account-number-text",
                ("account", state.AccountId!.Value)));
            RichTextLabelExt.SetMarkup(LinkedAccountNameLabel, Loc.GetString("bank-program-ui-account-owner-text",
                ("owner", state.OwnerName)));
            RichTextLabelExt.SetMarkup(LinkedAccountBalanceLabel, Loc.GetString("atm-ui-balance", ("balance", state.Balance)));
            UpdateAccountLinkUi();
            return;
        }

        RichTextLabelExt.SetMarkup(NoLinkedAccountLabel, Loc.GetString("bank-program-ui-no-account"));
        UpdateAccountLinkUi();
    }

    private void UpdateAccountLinkUi()
    {
        AccountLinkButton.Visible = !_accountLinkActive;
        AccountLink.Visible = _accountLinkActive;
    }

    protected override void FrameUpdate(FrameEventArgs args)
    {
        base.FrameUpdate(args);
        LinkConfirmButton.Disabled = PinLineEdit.Text.Length != 4 || AccountLineEdit.Text.Length != 6;
    }

    private void ValidateLineEdit(LineEdit lineEdit, int length)
    {
        var text = Regex.Replace(lineEdit.Text, _lineEditPattern, string.Empty);

        if (text.Length > length)
        {
            text = text[..length];
        }

        lineEdit.Text = text;
    }
}
