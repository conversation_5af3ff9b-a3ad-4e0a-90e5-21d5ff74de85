- type: entity
  id: BaseSpellbook
  name: "книга заклинань"
  parent: [BaseItem] # Goob edit
  abstract: true
  components:
    - type: Sprite
      sprite: Objects/Misc/books.rsi
      layers:
      - state: paper_blood
      - state: cover_strong
        color: "#645a5a"
      - state: decor_wingette_flat
        color: "#4d0303"
      - state: icon_pentagramm
        color: "#f7e19f"
    - type: Spellbook
    - type: Tag
      tags:
      - Spellbook
    - type: EmitSoundOnPickup
      sound: /Audio/SimpleStation14/Items/Handling/book_pickup.ogg
    - type: EmitSoundOnDrop
      sound: /Audio/SimpleStation14/Items/Handling/book_drop.ogg
    - type: EmitSoundOnLand
      sound: /Audio/SimpleStation14/Items/Handling/book_drop.ogg

# For the Wizard Antag
# Do not add discounts or price inflation
- type: entity
  id: WizardsGrimoire
  name: "гримуар чарівників"
  suffix: Wizard
  parent: [ BaseItem, StorePresetSpellbook ] # Goob edit
  components:
    - type: Sprite
      sprite: Objects/Misc/books.rsi
      layers:
      - state: paper_blood
      - state: cover_strong
        color: "#645a5a"
      - state: decor_wingette_flat
        color: "#4d0303"
      - state: icon_pentagramm
        color: "#f7e19f"
    - type: UserInterface
      interfaces:
        enum.StoreUiKey.Key:
          type: StoreBoundUserInterface
        enum.RefundUiKey.Key:
          type: StoreRefundBoundUserInterface
    - type: ActivatableUI
      key: enum.StoreUiKey.Key
    - type: Store
      refundAllowed: false
      ownerOnly: true # get your own tome!
      balance:
        WizCoin: 10 # prices are balanced around this 10 point maximum and how strong the spells are
    - type: GuideHelp # Goobstation
      guides:
      - Wizard

# Not meant for wizard antag but meant for spawning, so people can't abuse refund if they were given a tome
- type: entity
  id: WizardsGrimoireNoRefund
  name: "гримуар чарівників"
  suffix: Wizard, No Refund
  parent: [ WizardsGrimoire, StorePresetSpellbook ]
  components:
    - type: Store
      refundAllowed: false
      ownerOnly: true # get your own tome!
      balance:
        WizCoin: 10 # prices are balanced around this 10 point maximum and how strong the spells are

- type: entity
  id: SpawnSpellbook
  name: "книга заклинань відтворення"
  parent: BaseSpellbook
  components:
    - type: Spellbook
      spellActions:
        ActionSpawnMagicarpSpell: -1

- type: entity
  id: ForceWallSpellbook
  name: "книга заклинань силової стіни"
  parent: BaseSpellbook
  components:
    - type: Sprite
      sprite: Objects/Misc/books.rsi
      layers:
      - state: paper
      - state: cover_strong
        color: "#366ed6"
      - state: decor_vertical_middle
        color: "#95ffff"
      - state: decor_wingette_circle
        color: "#95ffff"
      - state: icon_magic_forcewall
        shader: unshaded
      - state: detail_rivets
        color: gold
    - type: Spellbook
      spellActions:
        ActionForceWall: -1

- type: entity
  id: BlinkBook
  name: "книга заклинань блимання"
  parent: BaseSpellbook
  components:
    - type: Sprite
      sprite: Objects/Misc/books.rsi
      layers:
      - state: paper
      - state: cover_old
        color: "#657e9c"
      - state: icon_text3
      - state: decor_wingette_circle
        color: gold
      - state: icon_magic
      - state: detail_rivets
        color: gold
    - type: Spellbook
      spellActions:
        ActionBlinkSpell: -1 # Goob edit

- type: entity
  id: SmiteBook
  name: "книга заклинань удару"
  parent: BaseSpellbook
  components:
    - type: Sprite
      sprite: Objects/Misc/books.rsi
      layers:
      - state: paper
      - state: cover_old
        color: "#c42b40"
      - state: decor_wingette_circle
        color: gold
      - state: icon_magic
      - state: detail_rivets
        color: gold
      - state: detail_bookmark
        color: red
      - state: overlay_blood
    - type: Spellbook
      spellActions:
        ActionSmite: -1

- type: entity
  id: KnockSpellbook
  name: "книга заклинань оглушення"
  parent: BaseSpellbook
  components:
    - type: Sprite
      sprite: Objects/Misc/books.rsi
      layers:
      - state: paper
      - state: cover_strong
        color: "#117045"
      - state: decor_wingette_circle
        color: gold
      - state: icon_magic_knock
      - state: detail_rivets
        color: gold
      - state: detail_bookmark
        color: "#98c495"
    - type: Spellbook
      spellActions:
        ActionKnock: -1

- type: entity
  id: FireballSpellbook
  name: "книга заклинань вогняної кулі"
  parent: BaseSpellbook
  components:
    - type: Sprite
      sprite: Objects/Misc/books.rsi
      layers:
      - state: paper
      - state: cover_old
        color: "#ba5a14"
      - state: decor_wingette_circle
        color: gold
      - state: detail_rivets
        color: gold
      - state: detail_bookmark
        color: "#e89b3c"
      - state: overlay_blood
      - state: icon_magic_fireball
        shader: unshaded
    - type: Spellbook
      spellActions:
        ActionFireball: -1

- type: entity
  id: ScrollRunes
  name: "сувій рун" # Goob edit
  parent: BaseSpellbook
  components:
  - type: Item
    size: Normal
  - type: Sprite
    sprite: Objects/Magic/magicactions.rsi
    layers:
    - state: spell_default
  - type: Spellbook
    spellActions:
      ActionTrapsSpell: -1 # Goob edit
