- type: entity
  name: "ліх<PERSON>арик"
  parent: BaseItem
  id: FlashlightLantern
  description: "Він освітлює шлях до свободи."
  components:
  - type: Tag
    tags:
    - Flashlight
  - type: HandheldLight
    addPrefix: false
    blinkingBehaviourId: blinking
    radiatingBehaviourId: radiating
  - type: LightBehaviour
    behaviours:
      - !type:FadeBehaviour
        id: radiating
        maxDuration: 2.0
        startValue: 3.0
        endValue: 2.0
        isLooped: true
        reverseWhenFinished: true
      - !type:PulseBehaviour
        id: blinking
        interpolate: Nearest
        maxDuration: 1.0
        minValue: 0.1
        maxValue: 2.0
        isLooped: true
  - type: ToggleableLightVisuals
    spriteLayer: light
    inhandVisuals:
      left:
      - state: inhand-left-light
        shader: unshaded
      right:
      - state: inhand-right-light
        shader: unshaded
  - type: PowerCellSlot
    cellSlotId: cell_slot
  - type: ContainerContainer
    containers:
      cell_slot: !type:ContainerSlot
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellMedium
  - type: Sprite
    sprite: Objects/Tools/flashlight.rsi
    layers:
      - state: flashlight
      - state: flashlight-overlay
        shader: unshaded
        visible: false
        map: [ "light" ]
  - type: MeleeWeapon
    attackRate: 1.25
    bluntStaminaDamageFactor: 1.5
    damage:
      types:
        Blunt: 7
    soundHit:
      collection: MetalThud
  - type: DamageOtherOnHit
    staminaCost: 3.5
  - type: Item
    sprite: Objects/Tools/flashlight.rsi
    storedRotation: -90
  - type: EtherealLight
  - type: PointLight
    enabled: false
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
    radius: 6
    netsync: false
  - type: Appearance
  - type: StaticPrice
    price: 40
  - type: StealTarget
    stealGroup: LAMP
  - type: Clothing
    sprite: Objects/Tools/flashlight.rsi
    quickEquip: false
    slots:
      - Belt
  - type: AttachmentFlashlight # Lavaland Change

- type: entity
  name: "ліхтарик служби безпеки"
  parent: FlashlightLantern
  id: FlashlightSeclite
  description: "Міцний ліхтарик, що використовується службою безпеки."
  components:
  - type: Tag
    tags:
      - SecBeltEquip
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellHigh
  - type: HandheldLight
    addPrefix: false
  - type: ToggleableLightVisuals
    inhandVisuals:
      left:
      - state: inhand-left-light
        shader: unshaded
      right:
      - state: inhand-right-light
        shader: unshaded
  - type: Sprite
    sprite: Objects/Tools/seclite.rsi
    layers:
      - state: seclite
      - state: seclite-overlay
        shader: unshaded
        visible: false
        map: [ "light" ]
  - type: MeleeWeapon
    wideAnimationRotation: 90
    attackRate: 1.25
    damage:
      types:
        Blunt: 8
    bluntStaminaDamageFactor: 2.5
    heavyRateModifier: 2
    heavyRangeModifier: 1.2
    heavyDamageBaseModifier: 1.25
    maxTargets: 1
    angle: 20
    soundHit:
      collection: MetalThud
  - type: Item
    sprite: Objects/Tools/seclite.rsi
  - type: PointLight
    enabled: false
    radius: 8
  - type: Clothing
    sprite: Objects/Tools/seclite.rsi
    quickEquip: false
    slots:
      - Belt

- type: entity
  parent: FlashlightLantern
  id: EmptyFlashlightLantern
  suffix: Empty
  components:
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
