using Content.Shared._Pirate.Banking;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client._Pirate.Banking.UI;

[GenerateTypedNameReferences]
public sealed partial class EftposWindow : DefaultWindow
{
    public Action<EftposLockMessage>? OnCardButtonPressed;

    public EftposWindow()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        AmountLineEdit.OnTextChanged += _ =>
        {
            if (!int.TryParse((string?)AmountLineEdit.Text, out var result) || result <= 0)
                AmountLineEdit.Text = string.Empty;
            AmountLineEdit.Text = result.ToString();
        };

        CardButton.OnPressed += _ =>
        {
            if (!int.TryParse((string?)AmountLineEdit.Text, out var result) || result < 0)
                result = 0;
            OnCardButtonPressed?.Invoke(new EftposLockMessage(result));
        };
    }

    public void UpdateState(BoundUserInterfaceState state)
    {
        if (state is not EftposBuiState eftState)
            return;

        AmountLineEdit.Text = eftState.Amount == 0 ? string.Empty : eftState.Amount.ToString();
        if (eftState.Locked)
        {
            CardButton.Text = Loc.GetString("eftpos-ui-card-unlock-text");
            CardButton.ToolTip = Loc.GetString("eftpos-ui-card-unlock-desc");
            AmountLineEdit.Editable = false;
        }
        else
        {
            CardButton.Text = Loc.GetString("eftpos-ui-card-lock-text");
            CardButton.ToolTip = Loc.GetString("eftpos-ui-card-lock-desc");
            AmountLineEdit.Editable = true;
        }

        AccountLabel.Text = eftState.Owner == string.Empty
            ? string.Empty
            : Loc.GetString("eftpos-ui-account-text", ("owner", eftState.Owner));
    }
}
