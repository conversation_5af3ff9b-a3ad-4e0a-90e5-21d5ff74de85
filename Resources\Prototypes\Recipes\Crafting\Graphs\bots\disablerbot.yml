- type: constructionGraph
  id: DisablerBot
  start: start
  graph:
  - node: start
    edges:
    - to: bot
      steps:
      - tag: ProximitySensor
        icon:
          sprite: Objects/Misc/proximity_sensor.rsi
          state: icon
        name: Proximity sensor
        doAfter: 2
      - tag: WeaponDisabler
        icon:
          sprite: Objects/Weapons/Guns/Battery/disabler.rsi
          state: base
        name: Disabler
        doAfter: 2
      - tag: ClothingHeadHelmetBasic
        icon:
          sprite: DeltaV/Clothing/Head/Helmets/security.rsi
          state: icon
        name: Security Helmet
        doAfter: 2
  - node: bot
    entity: MobDisablerBot
