name: <PERSON><PERSON><PERSON>

on:
  push:
    branches: [ master, staging, trying ]
  merge_group:
  pull_request:
    types: [ opened, reopened, synchronize, ready_for_review ]

jobs:
  build:
    name: <PERSON>AM<PERSON>
    if: github.actor != 'PJBot' && github.event.pull_request.draft == false && github.actor != 'DeltaV-Bot' && github.actor != 'SimpleStation14'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3.6.0
      - name: Setup submodule
        run: |
          git submodule update --init --recursive
      - name: Pull engine updates
        uses: space-wizards/submodule-dependency@v0.1.5
      - name: Update Engine Submodules
        run: |
          cd RobustToolbox/
          git submodule update --init --recursive
      - name: Setup .NET Core
        uses: actions/setup-dotnet@v3.2.0
        with:
          dotnet-version: 9.0.x
      - name: Install dependencies
        run: dotnet restore
      - name: Build
        run: dotnet build --configuration Release --no-restore /p:WarningsAsErrors= /m
      - name: <PERSON> Lin<PERSON>
        run: dotnet run --project Content.YAMLLinter/Content.YAMLLinter.csproj --no-build
