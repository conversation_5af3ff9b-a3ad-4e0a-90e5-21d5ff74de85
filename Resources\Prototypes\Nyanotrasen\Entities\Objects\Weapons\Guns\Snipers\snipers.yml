- type: entity
  name: "гвинтівка mark 1"
  parent: BaseWeaponRifle
  id: WeaponSniperGrand
  description: "Важка та надійна самозарядна гвинтівка, що використовує модифіковану версію багатовікової конструкції. Використовує гвинтівкові набої калібру .30."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Objects/Weapons/Guns/Snipers/grand_rifle.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
  - type: Clothing
    sprite: Nyanotrasen/Objects/Weapons/Guns/Snipers/grand_rifle.rsi
  - type: Gun
    fireRate: 2
    selectedMode: SemiAuto
    availableModes:
    - SemiAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/rifle2.ogg
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        startingItem: MagazineLightRifleMarkOne
        priority: 2
        whitelist:
          tags:
            - MagazineMarkOne
      gun_chamber:
        name: Chamber
        startingItem: CartridgeLightRifle
        priority: 1
        whitelist:
          tags:
            - CartridgeLightRifle
  - type: ChamberMagazineAmmoProvider
    autoEject: true
    soundAutoEject:
      path: /Audio/Weapons/Guns/EmptyAlarm/M1_empty_alarm.ogg

- type: entity
  name: "парадна гвинтівка"
  parent: BaseWeaponRifle
  id: WeaponSniperCeremonial
  description: "Парадний варіант гвинтівки Mark 1, зі смаком виконаний у синьо-білому кольорі. Під набій .30."
  components:
  - type: Sprite
    sprite: Nyanotrasen/Objects/Weapons/Guns/Snipers/ceremonial_grand.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
  - type: Clothing
    sprite: Nyanotrasen/Objects/Weapons/Guns/Snipers/ceremonial_grand.rsi
  - type: Gun
    fireRate: 2
    selectedMode: SemiAuto
    availableModes:
    - SemiAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/rifle2.ogg
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        startingItem: MagazineLightRifleMarkOne
        priority: 2
        whitelist:
          tags:
            - MagazineMarkOne
      gun_chamber:
        name: Chamber
        startingItem: CartridgeLightRifle
        priority: 1
        whitelist:
          tags:
            - CartridgeLightRifle
  - type: ChamberMagazineAmmoProvider
    autoEject: true
    soundAutoEject:
      path: /Audio/Weapons/Guns/EmptyAlarm/M1_empty_alarm.ogg
