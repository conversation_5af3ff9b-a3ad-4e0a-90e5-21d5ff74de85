supermatter-console-window-title = Supermatter Monitoring Console
supermatter-console-window-station-name = [color=white][font size=14]{$stationName}[/font][/color]
supermatter-console-window-unknown-location = Unknown location
supermatter-console-window-no-supermatters = [font size=16][color=white]No supermatter detected[/font]

supermatter-console-window-label-sm = {CAPITALIZE($name)}

supermatter-console-window-label-alert-types = Supermatter status:
supermatter-console-window-error-status = Error
supermatter-console-window-inactive-status = Inactive
supermatter-console-window-normal-status = Normal
supermatter-console-window-caution-status = Caution
supermatter-console-window-warning-status = Warning
supermatter-console-window-danger-status = Danger
supermatter-console-window-emergency-status = Emergency
supermatter-console-window-delaminating-status = Delaminating

supermatter-console-window-label-integrity = Integrity:
supermatter-console-window-label-integrity-bar = {$integrity}%

supermatter-console-window-label-power = Internal Energy:
supermatter-console-window-label-power-bar = {$power} {$prefix}eV

supermatter-console-window-label-radiation = Radiation Emission:
supermatter-console-window-label-radiation-bar = {$radiation} rads

supermatter-console-window-label-moles = Absorbed Moles:
supermatter-console-window-label-moles-bar = {$moles} Moles

supermatter-console-window-label-temperature = Temperature:
supermatter-console-window-label-temperature-limit = Temperature Limit:
supermatter-console-window-label-temperature-bar = {$temperature} K

supermatter-console-window-label-waste = Waste Multiplier:
supermatter-console-window-label-waste-bar = {$waste} x

supermatter-console-window-label-absorption = Absorption Ratio:
supermatter-console-window-label-absorption-bar = {$absorption}%

supermatter-console-window-label-gas = Unknown gas
supermatter-console-window-label-gas-bar = {$gas}%

supermatter-console-window-flavor-left = ⚠ Do not approach the crystal
supermatter-console-window-flavor-right = v1.1
