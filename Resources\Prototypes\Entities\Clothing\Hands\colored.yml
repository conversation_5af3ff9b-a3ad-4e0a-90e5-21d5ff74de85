#DO NOT MAKE THESE THE SAME COLOR AS THE JUMPSUIT. It is going to cause contrast issues for those wearing the full set of color clothing and is almost definitely going to look worse.
#If you want to make it similar to the jumpsuit color, it should be slightly off.
#P.S: Most of these just use the shoe colors, so they end up having a nice "secondary" color when wearing the full set of color clothing.

# Purple Gloves
- type: entity
  parent: ClothingHandsGlovesSyntheticBase
  id: ClothingHandsGlovesColorPurple
  name: "фіолетові рукавички"
  description: "Звичайні фіолетові рукавички, які не заважають смажити."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/Color/color.rsi
    layers:
    - state: icon
      color: "#9C0DE1"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#9C0DE1"
      right:
      - state: inhand-right
        color: "#9C0DE1"
  - type: Clothing
    sprite: Clothing/Hands/Gloves/Color/color.rsi
    clothingVisuals:
      gloves:
      - state: equipped-HAND
        color: "#9C0DE1"
  - type: Fiber
    fiberColor: fibers-purple

# Red Gloves
- type: entity
  parent: ClothingHandsGlovesSyntheticBase
  id: ClothingHandsGlovesColorRed
  name: "червоні рукавички"
  description: "Звичайні червоні рукавички, які не заважають смажити."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/Color/color.rsi
    layers:
    - state: icon
      color: "#940000"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#940000"
      right:
      - state: inhand-right
        color: "#940000"
  - type: Clothing
    sprite: Clothing/Hands/Gloves/Color/color.rsi
    clothingVisuals:
      gloves:
      - state: equipped-HAND
        color: "#940000"
  - type: Fiber
    fiberColor: fibers-red

# Blue Gloves
- type: entity
  parent: ClothingHandsGlovesSyntheticBase
  id: ClothingHandsGlovesColorBlue
  name: "сині рукавички"
  description: "Звичайні сині рукавички, які не заважають смажити."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/Color/color.rsi
    layers:
    - state: icon
      color: "#0089EF"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#0089EF"
      right:
      - state: inhand-right
        color: "#0089EF"
  - type: Clothing
    sprite: Clothing/Hands/Gloves/Color/color.rsi
    clothingVisuals:
      gloves:
      - state: equipped-HAND
        color: "#0089EF"
  - type: Fiber
    fiberColor: fibers-blue

# Brown Gloves
- type: entity
  parent: ClothingHandsGlovesSyntheticBase
  id: ClothingHandsGlovesColorBrown
  name: "коричневі рукавички"
  description: "Звичайні коричневі рукавички, які не заважають смажити."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/Color/color.rsi
    layers:
    - state: icon
      color: "#723A02"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#723A02"
      right:
      - state: inhand-right
        color: "#723A02"
  - type: Clothing
    sprite: Clothing/Hands/Gloves/Color/color.rsi
    clothingVisuals:
      gloves:
      - state: equipped-HAND
        color: "#723A02"
  - type: Fiber
    fiberColor: fibers-brown

# Grey Gloves
- type: entity
  parent: ClothingHandsGlovesSyntheticBase
  id: ClothingHandsGlovesColorGray
  name: "сірі рукавички"
  description: "Звичайні сірі рукавички, які не заважають смажити."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/Color/color.rsi
    layers:
    - state: icon
      color: "#999999"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#999999"
      right:
      - state: inhand-right
        color: "#999999"
  - type: Clothing
    sprite: Clothing/Hands/Gloves/Color/color.rsi
    clothingVisuals:
      gloves:
      - state: equipped-HAND
        color: "#999999"
  - type: Fiber
    fiberColor: fibers-grey

# Green Gloves
- type: entity
  parent: ClothingHandsGlovesSyntheticBase
  id: ClothingHandsGlovesColorGreen
  name: "зелені рукавички"
  description: "Звичайні зелені рукавички, які не заважають смажити."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/Color/color.rsi
    layers:
    - state: icon
      color: "#5ABF2F"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#5ABF2F"
      right:
      - state: inhand-right
        color: "#5ABF2F"
  - type: Clothing
    sprite: Clothing/Hands/Gloves/Color/color.rsi
    clothingVisuals:
      gloves:
      - state: equipped-HAND
        color: "#5ABF2F"
  - type: Fiber
    fiberColor: fibers-green

# Light Brown Gloves
- type: entity
  parent: ClothingHandsGlovesSyntheticBase
  id: ClothingHandsGlovesColorLightBrown
  name: "світло-коричневі рукавички"
  description: "Звичайні світло-коричневі рукавички, які не заважають смажити."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/Color/color.rsi
    layers:
    - state: icon
      color: "#C09F72"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#C09F72"
      right:
      - state: inhand-right
        color: "#C09F72"
  - type: Clothing
    sprite: Clothing/Hands/Gloves/Color/color.rsi
    clothingVisuals:
      gloves:
      - state: equipped-HAND
        color: "#C09F72"
  - type: Fiber
    fiberColor: fibers-brown

# Orange Gloves
- type: entity
  parent: ClothingHandsGlovesSyntheticBase
  id: ClothingHandsGlovesColorOrange
  name: "помаранчеві рукавички"
  description: "Звичайні помаранчеві рукавички, які не заважають смажити."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/Color/color.rsi
    layers:
    - state: icon
      color: "#EF8100"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#EF8100"
      right:
      - state: inhand-right
        color: "#EF8100"
  - type: Clothing
    sprite: Clothing/Hands/Gloves/Color/color.rsi
    clothingVisuals:
      gloves:
      - state: equipped-HAND
        color: "#EF8100"
  - type: Fiber
    fiberColor: fibers-orange

# White Gloves
- type: entity
  parent: ClothingHandsGlovesSyntheticBase
  id: ClothingHandsGlovesColorWhite
  name: "білі рукавички"
  description: "Звичайні білі рукавички, які не заважають смажити."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/Color/color.rsi
    layers:
    - state: icon
      color: "#EAE8E8"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#EAE8E8"
      right:
      - state: inhand-right
        color: "#EAE8E8"
  - type: Clothing
    sprite: Clothing/Hands/Gloves/Color/color.rsi
    clothingVisuals:
      gloves:
      - state: equipped-HAND
        color: "#EAE8E8"
  - type: Fiber
    fiberColor: fibers-white

# Black Gloves
# TECHNICALLY, if you ported the worn state to the RSI, this could be greyscaled, but I do not really feel like doing that.
- type: entity
  parent: ClothingHandsGlovesSyntheticBase
  id: ClothingHandsGlovesColorBlack
  name: "чорні рукавички"
  description: "Звичайні чорні рукавички, які не заважають смажити."
  components:
  - type: Sprite
    sprite: Clothing/Hands/Gloves/Color/black.rsi
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#535353"
      right:
      - state: inhand-right
        color: "#535353"
  - type: Clothing
    sprite: Clothing/Hands/Gloves/Color/black.rsi
  - type: GloveHeatResistance
    heatResistance: 1400
  - type: Butcherable
    butcheringType: Knife
    spawned:
    - id: ClothingHandsGlovesFingerless
  - type: Fiber
    fiberColor: fibers-black

# Insulated Gloves (Yellow Gloves)
# Currently can not be greyscaled without looking like shit.
- type: entity
  parent: ClothingHandsBase
  id: ClothingHandsGlovesColorYellow
  name: "ізольовані рукавички"
  description: "Ці рукавички захистять користувача від ураження електричним струмом."
  components:
  - type: FingerprintMask
  - type: Sprite
    sprite: Clothing/Hands/Gloves/Color/yellow.rsi
  - type: Clothing
    sprite: Clothing/Hands/Gloves/Color/yellow.rsi
  - type: GloveHeatResistance
    heatResistance: 1400
  - type: Butcherable
    butcheringType: Knife
    spawned:
    - id: ClothingHandsGlovesFingerlessInsulated
  - type: Insulated
  - type: Fiber
    fiberMaterial: fibers-insulative
    fiberColor: fibers-yellow

# Budget Insulated Gloves
- type: entity
  parent: ClothingHandsGlovesColorYellow
  id: ClothingHandsGlovesColorYellowBudget
  name: "бюджетні ізольовані рукавички"
  description: "Ці рукавички є дешевими підробками омріяних рукавичок - нічим поганим це не може закінчитися."
  components:
  - type: GloveHeatResistance
    # can't take out lights using budgets
    heatResistance: 0
  - type: RandomInsulation
    # Why repeated numbers? So some numbers are more common, of course!
    list:
      - 0
      - 0
      - 0
      - 0.5
      - 0.5
      - 0.5
      - 0.75
      - 1.25
      - 1.25
      - 1.5
      - 1.5
      - 1.5
      - 1.5

# Conductive Insulated Gloves
- type: entity
  parent: ClothingHandsGlovesColorYellow
  id: ClothingHandsGlovesConducting
  suffix: Conducting
  components:
  - type: Insulated
    coefficient: 5 # zap em good
