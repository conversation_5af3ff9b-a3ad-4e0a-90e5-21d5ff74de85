ghost-gui-return-to-body-button = Return to body
ghost-gui-ghost-warp-button = Ghost Warp
ghost-gui-ghost-roles-button = Ghost Roles ({$count})
ghost-gui-toggle-ghost-visibility-popup-on = Enabled visibility of ghosts.
ghost-gui-toggle-ghost-visibility-popup-off = Disabled visibility of ghosts.
ghost-gui-toggle-lighting-manager-popup-normal = Lighting normal.
ghost-gui-toggle-lighting-manager-popup-personal-light = Enabled personal light.
ghost-gui-toggle-lighting-manager-popup-fullbright = Fullbright mode.
ghost-gui-toggle-fov-popup = Toggled field-of-view.

ghost-gui-toggle-hearing-popup-on = You can now hear all messages.
ghost-gui-toggle-hearing-popup-off = You can now only hear radio and nearby messages.

ghost-target-window-title = Ghost Warp
ghost-target-window-current-button = Warp: {$name}
ghost-target-window-warp-to-most-followed = Warp to Most Followed

ghost-roles-window-title = Ghost Roles
ghost-roles-window-join-raffle-button = Join raffle
ghost-roles-window-raffle-in-progress-button =
    Join raffle ({$time} left, { $players ->
         [one] {$players} player
        *[other] {$players} players
    })
ghost-roles-window-leave-raffle-button =
    Leave raffle ({$time} left, { $players ->
         [one] {$players} player
        *[other] {$players} players
    })
ghost-roles-window-request-role-button = Request
ghost-roles-window-request-role-button-timer = Request ({$time}s)
ghost-roles-window-follow-role-button = Follow
ghost-roles-window-no-roles-available-label = There are currently no available ghost roles.
ghost-roles-window-rules-footer = The button will enable after {$time} seconds (this delay is to make sure you read the rules).

ghost-return-to-body-title = Return to Body
ghost-return-to-body-text = You are being revived! Return to your body?
ghost-gui-return-to-round-button = Return to round
