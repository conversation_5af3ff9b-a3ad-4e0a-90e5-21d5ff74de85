- type: entity
  parent: BaseItem
  id: MMI
  name: "людино-машинний інтерфейс"
  description: "<PERSON><PERSON><PERSON><PERSON><PERSON>, здатна полегшити комунікацію між біологічним мозком і електронікою, що дозволяє екіпажу продовжувати приносити користь після інцидентів, пов'язаних з роботою."
  components:
  - type: Sprite
    sprite: Objects/Specific/Robotics/mmi.rsi
    layers:
    - state: mmi_brain
      map: ["enum.MMIVisualLayers.Brain"]
      visible: false
    - state: mmi_off
      map: ["enum.MMIVisualLayers.Base"]
  - type: Input
    context: human
  - type: MMI
  - type: BorgBrain
  - type: BlockMovement
  - type: Examiner
  - type: IntrinsicRadioReceiver
  - type: IntrinsicRadioTransmitter
    channels:
    - Binary
  - type: ActiveRadio
    channels:
    - Binary
    - Common
  - type: NameIdentifier
    group: MMI
  - type: DoAfter
  - type: Actions
  - type: TypingIndicator
    proto: robot
  - type: Speech
    speechSounds: Pai
  - type: ItemSlots
    slots:
      brain_slot:
        name: positronic-brain-slot-component-slot-name-brain
        whitelist:
          tags:
          - Brain
  - type: ContainerContainer
    containers:
      brain_slot: !type:ContainerSlot
  - type: Appearance
  - type: GuideHelp
    guides:
      - Cyborgs
  - type: LanguageSpeaker
  - type: LanguageKnowledge
    speaks: [TauCetiBasic, RobotTalk]
    understands: [TauCetiBasic, RobotTalk]
  - type: Brain # IPCMMI
    active: false
  - type: Organ
    slotId: posibrain

- type: entity
  parent: MMI
  id: MMIFilled
  suffix: Filled
  components:
  - type: ItemSlots
    slots:
      brain_slot:
        name: "Brain"
        startingItem: OrganHumanBrain
        whitelist:
          tags: # IPCMMI - changed component requirement to a tag bc you'd be able to infinitely stack regular + reverse MMIs otherwise
            - Brain

- type: entity
  parent: BaseItem
  id: PositronicBrain
  name: "позитронний мозок"
  description: "Штучний мозок, здатний до спонтанної нейронної активності."
  components:
    - type: Sprite
      sprite: Objects/Specific/Robotics/mmi.rsi
      layers:
      - state: posibrain
        map: ["base"]
    - type: Input
      context: human
    - type: ToggleableGhostRole
      examineTextMindPresent: positronic-brain-installed
      examineTextMindSearching: positronic-brain-still-searching
      examineTextNoMind: positronic-brain-off
      beginSearchingText: positronic-brain-searching
      roleName: positronic-brain-role-name
      roleDescription: positronic-brain-role-description
      roleRules: ghost-role-information-silicon-rules
      mindRoles:
      - MindRoleGhostRoleSilicon
      wipeVerbText: positronic-brain-wipe-device-verb-text
      wipeVerbPopup: positronic-brain-wiped-device
      stopSearchVerbText: positronic-brain-stop-searching-verb-text
      stopSearchVerbPopup: positronic-brain-stopped-searching
    - type: BlockMovement
    - type: Examiner
    - type: Brain # hopefully this doesn't break everything
      active: false
    - type: BorgBrain
    - type: IntrinsicRadioReceiver
    - type: IntrinsicRadioTransmitter
      channels:
      - Binary
    - type: ActiveRadio
      channels:
      - Binary
      - Common
    - type: NameIdentifier
      group: PositronicBrain
    - type: DoAfter
    - type: Actions
    - type: TypingIndicator
      proto: robot
    - type: Speech
      speechSounds: Pai
    - type: MobState
      allowedStates:
      - Alive
    - type: Appearance
    - type: Tag
      tags:
      - CannotSuicide
      - Posibrain # IPCMMI - added posibrain tag
    - type: GenericVisualizer
      visuals:
        enum.ToggleableGhostRoleVisuals.Status:
          base:
            Off: { state: posibrain }
            Searching: { state: posibrain-searching }
            On: { state: posibrain-occupied }
    - type: GuideHelp
      guides:
      - Cyborgs
    - type: Organ # Estacao Pirata - IPCs
      slotId: posibrain
    - type: LanguageSpeaker
    - type: LanguageKnowledge
      speaks: [RobotTalk]
      understands: [RobotTalk]
