- type: entity
  id: JetpackEffect
  categories: [ HideSpawnMenu ]
  components:
  - type: FadingTimedDespawn # Goobstation
    lifetime: 2
    fadeOutTime: 1
  - type: Sprite
    drawdepth: SmallMobs
    noRot: true
    layers:
    - shader: unshaded
      map: ["enum.EffectLayers.Unshaded"]
      sprite: Effects/atmospherics.rsi
      state: frezon_old
  # - type: EffectVisuals
  - type: Tag
    tags:
      - HideContextMenu
  - type: AnimationPlayer

- type: entity
  parent: GasTankBase
  abstract: true
  id: BaseJetpack
  name: "реактивний ранець"
  description: "Це реактивний ранець."
  components:
    - type: InputMover
      toParent: true
    - type: MovementSpeedModifier
      weightlessAcceleration: 1
      weightlessFriction: 0.3
      weightlessModifier: 1.2
    - type: CanMoveInAir
    - type: Sprite
      sprite: Objects/Tanks/Jetpacks/blue.rsi
      state: icon
    - type: Item
      sprite: Objects/Tanks/Jetpacks/blue.rsi
      size: Huge
    - type: UserInterface
      interfaces:
        enum.SharedGasTankUiKey.Key:
          type: GasTankBoundUserInterface
    - type: Clothing
      sprite: Objects/Tanks/Jetpacks/blue.rsi
      quickEquip: false
      slots:
        - Back
    - type: GasTank
      outputPressure: 21.3
      air:
        volume: 5
        temperature: 293.15
    - type: Jetpack
      moleUsage: 0.00085
    - type: Appearance
    - type: StaticPrice
      price: 100
#    - type: DynamicPrice
#      price: 100
    - type: ReverseEngineering # Nyano
      generic: true
      difficulty: 3
      recipes:
        - JetpackBlue

- type: entity
  id: ActionToggleJetpack
  name: "Перекидний реактивний ранець"
  description: "Вмикає реактивний ранець, дозволяючи вам пересуватися за межами станції."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon:
      sprite: Objects/Tanks/Jetpacks/blue.rsi
      state: icon
    iconOn:
      sprite: Objects/Tanks/Jetpacks/blue.rsi
      state: icon-on
    useDelay: 1.0
    event: !type:ToggleJetpackEvent

#Empty blue
- type: entity
  id: JetpackBlue
  parent: BaseJetpack
  name: "реактивний ранець"
  suffix: Empty
  components:
  - type: Sprite
    sprite: Objects/Tanks/Jetpacks/blue.rsi
  - type: Clothing
    sprite: Objects/Tanks/Jetpacks/blue.rsi
    slots:
      - Back

# Filled blue
- type: entity
  id: JetpackBlueFilled
  parent: JetpackBlue
  name: "реактивний ранець"
  suffix: Filled
  components:
  - type: GasTank
    outputPressure: 42.6
    air:
      # 13 minutes of thrust
      volume: 5
      temperature: 293.15
      moles:
        - 1.025689525 # oxygen
        - 1.025689525 # nitrogen

#Empty black
- type: entity
  id: JetpackBlack
  parent: BaseJetpack
  name: "реактивний ранець"
  suffix: Empty
  components:
  - type: Item
    sprite: Objects/Tanks/Jetpacks/black.rsi
  - type: Sprite
    sprite: Objects/Tanks/Jetpacks/black.rsi
  - type: Clothing
    sprite: Objects/Tanks/Jetpacks/black.rsi
    slots:
      - Back

# Filled black
- type: entity
  id: JetpackBlackFilled
  parent: JetpackBlack
  name: "реактивний ранець"
  suffix: Filled
  components:
  - type: GasTank
    outputPressure: 42.6
    air:
      # 13 minutes of thrust
      volume: 5
      temperature: 293.15
      moles:
        - 1.025689525 # oxygen
        - 1.025689525 # nitrogen

#Empty captain
- type: entity
  id: JetpackCaptain
  parent: BaseJetpack
  name: "реактивний ранець капітана"
  suffix: Empty
  components:
  - type: Sprite
    sprite: Objects/Tanks/Jetpacks/captain.rsi
  - type: Clothing
    sprite: Objects/Tanks/Jetpacks/captain.rsi
    slots:
      - Back
      - SuitStorage
  - type: Item
    sprite: Objects/Tanks/Jetpacks/captain.rsi
    size: Normal
  - type: Tag
    tags:
    - HighRiskItem
  - type: StealTarget
    stealGroup: JetpackCaptainFilled

# Filled captain
- type: entity
  id: JetpackCaptainFilled
  parent: JetpackCaptain
  name: "реактивний ранець капітана"
  suffix: Filled
  components:
  - type: GasTank
    outputPressure: 42.6
    air:
      # 13 minutes of thrust
      volume: 5
      temperature: 293.15
      moles:
        - 1.025689525 # oxygen
        - 1.025689525 # nitrogen

#Empty mini
- type: entity
  id: JetpackMini
  parent: BaseJetpack
  name: "міні реактивний ранець"
  suffix: Empty
  components:
    - type: Item
      sprite: Objects/Tanks/Jetpacks/mini.rsi
    - type: Sprite
      sprite: Objects/Tanks/Jetpacks/mini.rsi
    - type: Clothing
      sprite: Objects/Tanks/Jetpacks/mini.rsi
      slots:
        - Back
        - suitStorage
        - Belt
    - type: GasTank
      outputPressure: 42.6
      air:
        volume: 1.5
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - JetpackMini

# Filled mini
- type: entity
  id: JetpackMiniFilled
  parent: JetpackMini
  name: "міні реактивний ранець"
  suffix: Filled
  components:
  - type: GasTank
    outputPressure: 42.6
    air:
      # 4 minutes of thrust
      volume: 1.5
      temperature: 293.15
      moles:
        - 0.307706858 # oxygen
        - 0.307706858 # nitrogen

#Empty security
- type: entity
  id: JetpackSecurity
  parent: BaseJetpack
  name: "захисний реактивний ранець"
  suffix: Empty
  components:
  - type: Item
    sprite: Objects/Tanks/Jetpacks/security.rsi
  - type: Sprite
    sprite: Objects/Tanks/Jetpacks/security.rsi
  - type: Clothing
    sprite: Objects/Tanks/Jetpacks/security.rsi
    slots:
      - Back

#Filled security
- type: entity
  id: JetpackSecurityFilled
  parent: JetpackSecurity
  name: "захисний реактивний ранець"
  suffix: Filled
  components:
  - type: GasTank
    outputPressure: 42.6
    air:
      # 13 minutes thrust
      volume: 5
      temperature: 293.15
      moles:
        - 1.025689525 # oxygen
        - 1.025689525 # nitrogen

#Empty void
- type: entity
  id: JetpackVoid
  parent: BaseJetpack
  name: "реактивний ранець порожнечі"
  suffix: Empty
  components:
  - type: Item
    sprite: Objects/Tanks/Jetpacks/void.rsi
  - type: Sprite
    sprite: Objects/Tanks/Jetpacks/void.rsi
  - type: Clothing
    sprite: Objects/Tanks/Jetpacks/void.rsi
    slots:
      - Back
      - suitStorage
      - Belt

# Filled void
- type: entity
  id: JetpackVoidFilled
  parent: JetpackVoid
  name: "реактивний ранець порожнечі"
  suffix: Filled
  components:
  - type: GasTank
    outputPressure: 42.6
    air:
      # 13 minutes of thrust
      volume: 5
      temperature: 293.15
      moles:
        - 1.025689525 # oxygen
        - 1.025689525 # nitrogen

#Empty micro - Used in the Cyborg module, visually the same as mini jetpack.
- type: entity
  id: JetpackMicro
  parent: BaseJetpack
  name: "мікро реактивний ранець"
  suffix: Empty
  components:
    - type: Item
      sprite: Objects/Tanks/Jetpacks/mini.rsi
    - type: Sprite
      sprite: Objects/Tanks/Jetpacks/mini.rsi
    - type: Clothing
      sprite: Objects/Tanks/Jetpacks/mini.rsi
      slots:
        - Back
        - suitStorage
        - Belt
    - type: GasTank
      outputPressure: 42.6
      air:
        volume: 0.75


# Filled micro
- type: entity
  id: JetpackMicroFilled
  parent: JetpackMicro
  name: "мікро реактивний ранець"
  suffix: Filled
  components:
  - type: GasTank
    outputPressure: 42.6
    air:
      # 2 minutes of thrust
      volume: 0.75
      temperature: 293.15
      moles:
        - 0.153853429 # oxygen
        - 0.153853429 # nitrogen
