﻿- type: decal
  id: Overlays
  abstract: true

- type: decal
  id: FullTileOverlayGreyscale
  parent: Overlays
  tags: ["station", "overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: fulltile_overlay


# Brick
- type: decal
  id: Brick
  parent: Overlays
  abstract: true

- type: decal
  id: BrickBoxOverlay
  parent: Brick
  tags: ["station", "overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: brick_box

- type: decal
  id: BrickCornerOverlayNE
  parent: Brick
  tags: ["station", "overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: brick_corner_ne

- type: decal
  id: BrickCornerOverlayNW
  parent: Brick
  tags: ["station", "overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: brick_corner_nw

- type: decal
  id: BrickCornerOverlaySE
  parent: Brick
  tags: ["station", "overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: brick_corner_se

- type: decal
  id: BrickCornerOverlaySW
  parent: Brick
  tags: ["station", "overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: brick_corner_sw

- type: decal
  id: BrickEndOverlayE
  parent: Brick
  tags: ["station", "overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: brick_end_e

- type: decal
  id: BrickEndOverlayN
  parent: Brick
  tags: ["station", "overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: brick_end_n

- type: decal
  id: BrickEndOverlayS
  parent: Brick
  tags: ["station", "overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: brick_end_s

- type: decal
  id: BrickEndOverlayW
  parent: Brick
  tags: ["station", "overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: brick_end_w

- type: decal
  id: BrickLineOverlayE
  parent: Brick
  tags: ["station", "overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: brick_line_e

- type: decal
  id: BrickLineOverlayN
  parent: Brick
  tags: ["station", "overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: brick_line_n

- type: decal
  id: BrickLineOverlayS
  parent: Brick
  tags: ["station", "overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: brick_line_s

- type: decal
  id: BrickLineOverlayW
  parent: Brick
  tags: ["station", "overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: brick_line_w


# HalfTile
- type: decal
  id: HalfTile
  parent: Overlays
  abstract: true

- type: decal
  id: HalfTileOverlayGreyscale
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: HalfTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: halftile_overlay

- type: decal
  id: HalfTileOverlayGreyscale90
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: HalfTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: halftile_overlay_90

- type: decal
  id: HalfTileOverlayGreyscale180
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: HalfTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: halftile_overlay_180

- type: decal
  id: HalfTileOverlayGreyscale270
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: HalfTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: halftile_overlay_270

# QuarterTile
- type: decal
  id: QuarterTile
  parent: Overlays
  abstract: true

- type: decal
  id: QuarterTileOverlayGreyscale
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: QuarterTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: quartertile_overlay

- type: decal
  id: QuarterTileOverlayGreyscale90
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: QuarterTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: quartertile_overlay_90

- type: decal
  id: QuarterTileOverlayGreyscale180
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: QuarterTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: quartertile_overlay_180

- type: decal
  id: QuarterTileOverlayGreyscale270
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: QuarterTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: quartertile_overlay_270

# QuarterTile
- type: decal
  id: ThreeQuarterTile
  parent: Overlays
  abstract: true

- type: decal
  id: ThreeQuarterTileOverlayGreyscale
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: ThreeQuarterTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: threequartertile_overlay

- type: decal
  id: ThreeQuarterTileOverlayGreyscale90
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: ThreeQuarterTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: threequartertile_overlay_90

- type: decal
  id: ThreeQuarterTileOverlayGreyscale180
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: ThreeQuarterTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: threequartertile_overlay_180

- type: decal
  id: ThreeQuarterTileOverlayGreyscale270
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: ThreeQuarterTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: threequartertile_overlay_270

- type: decal
  id: MonoOverlay
  parent: Overlays
  tags: ["station", "overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: mono

# Checker
- type: decal
  id: Checker
  parent: Overlays
  abstract: true

- type: decal
  id: CheckerNESW
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: Checker
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: checkerNESW

- type: decal
  id: CheckerNWSE
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: Checker
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: checkerNWSE

- type: decal
  id: DiagonalOverlay
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: Checker
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: diagonal

- type: decal
  id: DiagonalCheckerAOverlay
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: Checker
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: diagonal_checker_a

- type: decal
  id: DiagonalCheckerBOverlay
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: Checker
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: diagonal_checker_b

- type: decal
  id: HerringboneOverlay
  parent: Overlays
  tags: ["station", "overlay"]
  defaultCustomColor: true
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: herringbone

# MiniTile
- type: decal
  id: MiniTile
  parent: Overlays
  abstract: true

- type: decal
  id: MiniTileOverlay
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile

- type: decal
  id: MiniTileCheckerAOverlay
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_checker_a

- type: decal
  id: MiniTileCheckerBOverlay
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_checker_b

- type: decal
  id: MiniTileDiagonalOverlay
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_diagonal

- type: decal
  id: MiniTileDiagonalCheckerAOverlay
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_diagonal_a

- type: decal
  id: MiniTileDiagonalCheckerBOverlay
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_diagonal_b

- type: decal
  id: MiniTileBoxOverlay
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_box

- type: decal
  id: MiniTileCornerOverlayNE
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_corner_ne

- type: decal
  id: MiniTileCornerOverlayNW
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_corner_nw

- type: decal
  id: MiniTileCornerOverlaySE
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_corner_se

- type: decal
  id: MiniTileCornerOverlaySW
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_corner_sw

- type: decal
  id: MiniTileEndOverlayE
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_end_e

- type: decal
  id: MiniTileEndOverlayN
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_end_n

- type: decal
  id: MiniTileEndOverlayS
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_end_s

- type: decal
  id: MiniTileEndOverlayW
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_end_w

- type: decal
  id: MiniTileInnerOverlayNE
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_inner_ne

- type: decal
  id: MiniTileInnerOverlayNW
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_inner_nw

- type: decal
  id: MiniTileInnerOverlaySE
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_inner_se

- type: decal
  id: MiniTileInnerOverlaySW
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_inner_sw

- type: decal
  id: MiniTileLineOverlayE
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_line_e

- type: decal
  id: MiniTileLineOverlayN
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_line_n

- type: decal
  id: MiniTileLineOverlayS
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_line_s

- type: decal
  id: MiniTileLineOverlayW
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: MiniTile
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: minitile_line_w

# Offset
- type: decal
  id: Offset
  parent: Overlays
  abstract: true

- type: decal
  id: OffsetOverlay
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: Offset
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: offset

- type: decal
  id: OffsetCheckerAOverlay
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: Offset
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: offset_checker_a

- type: decal
  id: OffsetCheckerBOverlay
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: Offset
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: offset_checker_b

# Pavement
- type: decal
  id: Pavement
  parent: Overlays
  abstract: true

- type: decal
  id: PavementOverlay
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: Pavement
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: pavement

- type: decal
  id: PavementCheckerAOverlay
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: Pavement
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: pavement_checker_a

- type: decal
  id: PavementCheckerBOverlay
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: Pavement
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: pavement_checker_b

- type: decal
  id: PavementVerticalOverlay
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: Pavement
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: pavement_vertical

- type: decal
  id: PavementVerticalCheckerAOverlay
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: Pavement
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: pavement_vertical_checker_a

- type: decal
  id: PavementVerticalCheckerBOverlay
  tags: ["station", "overlay"]
  defaultCustomColor: true
  parent: Pavement
  sprite:
    sprite: Decals/Overlays/greyscale.rsi
    state: pavement_vertical_checker_b

