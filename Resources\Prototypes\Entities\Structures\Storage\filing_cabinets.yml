- type: entity
  name: "шафка для документів"
  parent: [ BaseStructureDynamic, BaseBagOpenClose ]
  id: filingCabinet
  suffix: Empty
  description: "Шафа для всіх ваших картотечних потреб."
  components:
  - type: Storage
    grid:
    - 0,0,9,3
    maxItemSize: Normal
    whitelist:
      tags:
        - Document
        - Folder
        - Write
  - type: Sprite
    sprite: Structures/Storage/cabinets.rsi
    state: filingcabinet
    noRot: true
    layers:
    - state: filingcabinet
    - state: filingcabinet-open
      map: ["openLayer"]
  - type: UserInterface
    interfaces:
      enum.StorageUiKey.Key:
        type: StorageBoundUserInterface
  - type: Transform
    noRot: true
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.18,-0.48,0.18,0.48"
        density: 200
        mask:
        - MachineMask
        layer:
        - MachineLayer
  - type: InteractionOutline
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 2
  - type: Construction
    graph: FilingCabinet
    node: filingCabinet
  - type: StaticPrice
    price: 20

- type: entity
  name: "висока шафа"
  parent: filingCabinet
  id: filingCabinetTall
  suffix: Empty
  components:
  - type: Sprite
    sprite: Structures/Storage/cabinets.rsi
    state: tallcabinet
    layers:
    - state: tallcabinet
    - state: tallcabinet-open
      map: ["openLayer"]
  - type: Appearance
  - type: Construction
    graph: FilingCabinet
    node: tallCabinet

- type: entity
  name: "комод"
  parent: BaseStructureDynamic
  id: filingCabinetDrawer
  suffix: Empty
  description: "Невелика шухляда для всіх ваших потреб, тепер на коліщатках!"
  components:
  - type: Storage
    grid:
    - 0,0,7,2
    maxItemSize: Normal
    whitelist:
      tags:
        - Document
        - Folder
        - Write
  - type: Sprite
    sprite: Structures/Storage/cabinets.rsi
    state: chestdrawer
    noRot: true
    layers:
    - state: chestdrawer
    - state: chestdrawer-open
      map: ["openLayer"]
  - type: Appearance
  - type: UserInterface
    interfaces:
      enum.StorageUiKey.Key:
        type: StorageBoundUserInterface
  - type: Transform
    noRot: true
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.22,-0.42,0.22,0.34"
        density: 210
        mask:
        - MachineMask
        layer:
        - MachineLayer
  - type: InteractionOutline
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 2
  - type: Construction
    graph: FilingCabinet
    node: chestDrawer
  - type: StaticPrice
    price: 15
  - type: RequireProjectileTarget

- type: entity
  abstract: true
  id: BaseBureaucraticStorageFill
  components:
  - type: StorageFill
    contents:
      - id: Pen
        prob: 0.5
      - id: PaperOffice
        amount: 1
        maxAmount: 3
      - id: BoxFolderBlue
        orGroup: Folder
      - id: BoxFolderRed
        orGroup: Folder
      - id: BoxFolderYellow
        orGroup: Folder
      - id: BoxFolderWhite
        orGroup: Folder
      - id: BoxFolderGrey
        orGroup: Folder
      - id: BoxFolderBlack
        orGroup: Folder

- type: entity
  parent: [filingCabinet, BaseBureaucraticStorageFill]
  id: filingCabinetRandom
  suffix: Random

- type: entity
  parent: [filingCabinetTall, BaseBureaucraticStorageFill]
  id: filingCabinetTallRandom
  suffix: Random

- type: entity
  parent: [filingCabinetDrawer, BaseBureaucraticStorageFill]
  id: filingCabinetDrawerRandom
  suffix: Random
