- type: entity
  id: BasePart
  parent: BaseItem
  name: "частина тіла"
  abstract: true
  components:
  - type: Damageable
    damageContainer: OrganicPart # Shitmed Change
  - type: BodyPart
  - type: Gibbable
  - type: ContainerContainer
    containers:
      bodypart: !type:Container
        ents: []
  - type: StaticPrice
    price: 100
  - type: Tag
    tags:
      - Trash
      - Meat
  # Shitmed Change Start
  - type: SurgeryTool
    startSound:
      path: /Audio/_Shitmed/Medical/Surgery/organ1.ogg
    endSound:
      path: /Audio/_Shitmed/Medical/Surgery/organ2.ogg
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTypeTrigger
        damageType: Blunt
        damage: 110
      behaviors:
      - !type:GibPartBehavior { }
    - trigger:
        !type:DamageTypeTrigger
        damageType: Slash
        damage: 150
      behaviors:
      - !type:GibPartBehavior { }
    - trigger:
        !type:DamageTypeTrigger
        damageType: Heat
        damage: 200
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawnInContainer: true
        spawn:
          Ash:
            min: 1
            max: 1
      - !type:BurnBodyBehavior { }
      - !type:PlaySoundBehavior
        sound:
          collection: Meat<PERSON>aserImpact
  # Shitmed Change End

- type: entity
  id: BaseTorso
  name: "тулуб"
  parent: BasePart
  abstract: true
  components:
  - type: BodyPart
    partType: Torso
    # Shitmed Change Start
    toolName: "a torso"
    containerName: "torso_slot"
  - type: ContainerContainer
    containers:
      torso_slot: !type:ContainerSlot {}
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTypeTrigger
        damageType: Blunt
        damage: 400
      behaviors:
      - !type:GibPartBehavior { }
    - trigger:
        !type:DamageTypeTrigger
        damageType: Slash
        damage: 400
      behaviors:
      - !type:GibPartBehavior { }
    - trigger:
        !type:DamageTypeTrigger
        damageType: Heat
        damage: 400
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawnInContainer: true
        spawn:
          Ash:
            min: 1
            max: 1
      - !type:BurnBodyBehavior { }
      - !type:PlaySoundBehavior
        sound:
          collection: MeatLaserImpact
    # Shitmed Change End
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 11
    staminaCost: 12

- type: entity
  id: BaseHead
  name: "голова"
  parent: BasePart
  abstract: true
  components:
  - type: BodyPart
    partType: Head
    toolName: "a head" # Shitmed Change
    vital: true
  - type: Input
    context: "ghost"
  - type: Tag
    tags:
      - Head
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 5
    staminaCost: 5

- type: entity
  id: BaseLeftArm
  name: "ліва рука"
  parent: BasePart
  abstract: true
  components:
  - type: BodyPart
    partType: Arm
    symmetry: Left
    toolName: "a left arm" # Shitmed Change
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 7
    staminaCost: 7

- type: entity
  id: BaseRightArm
  name: "права рука"
  parent: BasePart
  abstract: true
  components:
  - type: BodyPart
    partType: Arm
    symmetry: Right
    toolName: "a right arm" # Shitmed Change
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 7
    staminaCost: 7

- type: entity
  id: BaseLeftHand
  name: "ліва долоня"
  parent: BasePart
  abstract: true
  components:
  - type: BodyPart
    partType: Hand
    symmetry: Left
    toolName: "a left hand" # Shitmed Change
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 3

- type: entity
  id: BaseRightHand
  name: "права долоня"
  parent: BasePart
  abstract: true
  components:
  - type: BodyPart
    partType: Hand
    symmetry: Right
    toolName: "a right hand" # Shitmed Change
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 3

- type: entity
  id: BaseLeftLeg
  name: "ліва нога"
  parent: BasePart
  abstract: true
  components:
  - type: BodyPart
    partType: Leg
    symmetry: Left
    toolName: "a left leg" # Shitmed Change
  - type: MovementBodyPart
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 8
    staminaCost: 9

- type: entity
  id: BaseRightLeg
  name: "права нога"
  parent: BasePart
  abstract: true
  components:
  - type: BodyPart
    partType: Leg
    symmetry: Right
    toolName: "a right leg" # Shitmed Change
  - type: MovementBodyPart
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 8
    staminaCost: 9

- type: entity
  id: BaseLeftFoot
  name: "ліва стопа"
  parent: BasePart
  abstract: true
  components:
  - type: BodyPart
    partType: Foot
    symmetry: Left
    toolName: "a left foot" # Shitmed Change
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 4

- type: entity
  id: BaseRightFoot
  name: "права стопа"
  parent: BasePart
  abstract: true
  components:
  - type: BodyPart
    partType: Foot
    symmetry: Right
    toolName: "a right foot" # Shitmed Change
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 4
