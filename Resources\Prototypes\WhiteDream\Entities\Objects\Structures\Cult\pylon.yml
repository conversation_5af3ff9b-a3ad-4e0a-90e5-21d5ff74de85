﻿- type: entity
  id: CultPylon
  parent: BaseStructure
  name: "пілон"
  description: "Плавучий кристал, який повільно зцілює вірних Нар'є."
  components:
  - type: Transform
    noRot: true
  - type: Fixtures
    fixtures:
      pylonFix:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.1,-0.3,0.1,0.3"
        density: 190
        mask:
        - TabletopMachineMask
        layer:
        - TabletopMachineLayer
  - type: Sprite
    noRot: true
    sprite: WhiteDream/BloodCult/Entities/Structures/pylon.rsi
    layers:
    - state: icon
      map: [ "enum.PylonVisuals.Layer" ]
  - type: InteractionOutline
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: Glass
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlass:
            min: 1
            max: 2
  - type: Appearance
  - type: Pylon
    healing:
      groups:
        Brute: -20
        Burn: -20
        Toxin: -10
        Genetic: -5
        Airloss: -20
    damageOnInteract:
      groups:
        Burn: 5
  - type: PointLight
    color: "#FF0000"
    radius: 2
    energy: 2
    enabled: true
  - type: GenericVisualizer
    visuals:
      enum.PylonVisuals.Activated:
        enum.PylonVisuals.Layer:
          True: { state: "icon" }
          False: { state: "icon_off" }
  - type: Construction
    graph: CultPylon
    node: pylon
