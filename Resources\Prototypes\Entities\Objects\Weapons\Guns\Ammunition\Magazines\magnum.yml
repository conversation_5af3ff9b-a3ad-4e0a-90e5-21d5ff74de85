- type: entity
  id: BaseMagazineMagnum
  name: "пістолетний магазин (.45 magnum)"
  parent: BaseMagazinePistol
  abstract: true
  components:
  - type: Tag
    tags:
      - MagazineMagnum
  - type: BallisticAmmoProvider
    mayTransfer: true
    whitelist:
      tags:
        - CartridgeMagnum
    capacity: 7

- type: entity
  id: BaseMagazineMagnumSubMachineGun
  name: "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON> Vector (.45 magnum)"
  parent: BaseItem
  abstract: true
  components:
  - type: Tag
    tags:
      - MagazineMagnumSubMachineGun
  - type: BallisticAmmoProvider
    mayTransfer: true
    whitelist:
      tags:
        - CartridgeMagnum
    capacity: 25
  - type: Item
    size: Small
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Magazine/Magnum/magnum_smg_mag.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 2
    zeroVisible: false
  - type: Appearance

- type: entity
  id: MagazineMagnumEmpty
  name: "пістолетний магазин (.45 магнум будь-який)"
  suffix: empty
  parent: BaseMagazineMagnum
  components:
  - type: BallisticAmmoProvider
    proto: null
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]

- type: entity
  id: MagazineMagnum
  name: "пістолетний магазин (.45 magnum)"
  parent: BaseMagazineMagnum
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnum
  - type: Sprite
    layers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineMagnumPractice
  name: "пістолетний магазин (.45 магнум практика)"
  parent: BaseMagazineMagnum
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumPractice
  - type: Sprite
    layers:
    - state: practice
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineMagnumRubber
  name: "пістолетний магазин (.45 магнум гумовий)"
  parent: BaseMagazineMagnum
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumRubber
  - type: Sprite
    layers:
    - state: rubber
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineMagnumIncendiary
  name: "пістолетний магазин (.45 магнум запальний)"
  parent: BaseMagazineMagnum
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumIncendiary
  - type: Sprite
    layers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineMagnumUranium
  name: "пістолетний магазин (.45 магнум уран)"
  parent: BaseMagazineMagnum
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumUranium
  - type: Sprite
    layers:
    - state: uranium
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineMagnumAP
  name: "пістолетний магазин (.45 магнум бронебійний)"
  parent: BaseMagazineMagnum
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumAP
  - type: Sprite
    layers:
    - state: piercing
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineMagnumShrapnel
  name: "пістолетний магазин (.45 магнум, шрапнельний)"
  parent: BaseMagazineMagnum
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumShrapnel
  - type: Sprite
    layers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineMagnumSubMachineGunEmpty
  name: "магазин Vector (.45 магнум будь-який)"
  suffix: empty
  parent: BaseMagazineMagnumSubMachineGun
  components:
  - type: BallisticAmmoProvider
    proto: null
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]

- type: entity
  id: MagazineMagnumSubMachineGun
  name: "Магазин Vector (.45 magnum)"
  parent: BaseMagazineMagnumSubMachineGun
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnum
  - type: Sprite
    layers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineMagnumSubMachineGunPractice
  name: "Журнал \"Вектор\" (.45 магнум практика)"
  parent: BaseMagazineMagnumSubMachineGun
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumPractice
  - type: Sprite
    layers:
    - state: practice
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineMagnumSubMachineGunRubber
  name: "магазин Vector (.45 магнум гумовий)"
  parent: BaseMagazineMagnumSubMachineGun
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumRubber
  - type: Sprite
    layers:
    - state: rubber
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineMagnumSubMachineGunUranium
  name: "Магазин Vector (.45 magnum uranium)"
  parent: BaseMagazineMagnumSubMachineGun
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumUranium
  - type: Sprite
    layers:
    - state: uranium
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineMagnumSubMachineGunPiercing
  name: "Магазин Vector (.45 magnum бронебійний)"
  parent: BaseMagazineMagnumSubMachineGun
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumAP
  - type: Sprite
    layers:
    - state: piercing
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
