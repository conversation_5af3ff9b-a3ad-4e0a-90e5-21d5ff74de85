- type: constructionGraph
  id: MeatSpike
  start: start
  graph:
    - node: start
      actions:
        - !type:DeleteEntity {}
      edges:
        - to: MeatSpike
          completed:
            - !type:SnapToGrid
              southRotation: true
          steps:
            - material: Steel
              amount: 15
              doAfter: 2
    - node: MeatSpike
      entity: KitchenSpike
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: SheetSteel1
              amount: 15
          steps:
            - tool: Screwing
              doAfter: 1
