- type: entity
  parent: BaseFlatpack
  id: InstrumentFlatpack
  name: "футляр для інструментів"
  description: "Плаский пакунок, який використовується для побудови чогось."
  categories: [ HideSpawnMenu ]
  components:
  - type: Item
    size: Normal
  - type: Sprite
    layers:
    - state: service_music

- type: entity
  parent: InstrumentFlatpack
  id: PianoFlatpack
  name: "флетпак фортепіано"
  description: "Плаский пакунок, який використовується для виготовлення фортепіано."
  components:
  - type: Flatpack
    entity: PianoInstrument

- type: entity
  parent: InstrumentFlatpack
  id: UprightPianoFlatpack
  name: "флетпак піаніно"
  description: "Плаский пакунок, який використовується для побудови піаніно."
  components:
  - type: Flatpack
    entity: UprightPianoInstrument

- type: entity
  parent: InstrumentFlatpack
  id: VibraphoneFlatpack
  name: "флетпак вібрафон"
  description: "Флетпак, що використовується для виготовлення вібрафона."
  components:
  - type: Flatpack
    entity: VibraphoneInstrument

- type: entity
  parent: InstrumentFlatpack
  id: MarimbaFlatpack
  name: "флетпак маримба"
  description: "Плоский пакунок, що використовується для виготовлення маримби."
  components:
  - type: Flatpack
    entity: MarimbaInstrument

- type: entity
  parent: InstrumentFlatpack
  id: ChurchOrganFlatpack
  name: "флетпак церковний орган"
  description: "Плоский пакунок, що використовується для побудови церковного органу."
  components:
  - type: Flatpack
    entity: ChurchOrganInstrument

- type: entity
  parent: InstrumentFlatpack
  id: TubaFlatpack
  name: "флетпак туба"
  description: "Флетпак, що використовується для виготовлення туби."
  components:
  - type: Flatpack
    entity: TubaInstrument

- type: entity
  parent: InstrumentFlatpack
  id: HarpFlatpack
  name: "флетпак арфа"
  description: "Плаский пакунок, що використовується для виготовлення арфи."
  components:
  - type: Flatpack
    entity: HarpInstrument

- type: entity
  parent: InstrumentFlatpack
  id: TimpaniFlatpack
  name: "флетпак литаври"
  description: "Плоский пакунок, що використовується для виготовлення литавр."
  components:
  - type: Flatpack
    entity: TimpaniInstrument

- type: entity
  parent: InstrumentFlatpack
  id: TaikoFlatpack
  name: "флетпак тайко"
  description: "Футляр, який використовується для виготовлення тайко."
  components:
  - type: Flatpack
    entity: TaikoInstrument

- type: entity
  parent: InstrumentFlatpack
  id: ContrabassFlatpack
  name: "флетпак контрабас"
  description: "Флетпак, що використовується для побудови контрабаса."
  components:
  - type: Flatpack
    entity: ContrabassInstrument

- type: entity
  parent: InstrumentFlatpack
  id: MinimoogFlatpack
  name: "флетпак мінімуг"
  description: "Флетпак, що використовується для побудови міні-муга."
  components:
  - type: Flatpack
    entity: MinimoogInstrument

- type: entity
  parent: InstrumentFlatpack
  id: TomDrumsFlatpack
  name: "флетпак томні барабани"
  description: "Флетпак, що використовується для побудови томних барабанів."
  components:
  - type: Flatpack
    entity: TomDrumsInstrument
