﻿- type: entity
  parent: BaseItem
  id: BaseXenoArtifactItem
  name: "інопланетний артефакт"
  description: "Дивний ручний пристрій прибульців."
  abstract: true
  components:
  - type: Sprite
    sprite: Objects/Specific/Xenoarchaeology/item_artifacts.rsi
    layers:
    - state: ano01
      map: [ "enum.ArtifactsVisualLayers.Base" ]
    - state: ano01_on
      map: [ "enum.ArtifactsVisualLayers.Effect" ]
      visible: false
  - type: Damageable
  - type: Physics
    bodyType: Dynamic
  - type: CollisionWake
    enabled: false
  - type: InteractionOutline
  - type: Reactive
    groups:
      Acidic: [Touch]
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 20
        mask:
        - ItemMask
        layer:
        - Opaque
        restitution: 0.3  # fite me
        friction: 0.2
  - type: Artifact
  - type: RandomArtifactSprite
    maxSprite: 11
    activationTime: 2.4
  - type: RandomSprite
    available:
    - enum.ArtifactsVisualLayers.Effect:
        ano01_on: Rainbow
  - type: UserInterface #needs to be here for certain effects
    interfaces:
      enum.StorageUiKey.Key:
        type: StorageBoundUserInterface
      enum.TransferAmountUiKey.Key:
        type: TransferAmountBoundUserInterface
      enum.InstrumentUiKey.Key:
        type: InstrumentBoundUserInterface
      enum.IntercomUiKey.Key:
        type: IntercomBoundUserInterface
  - type: Appearance
  - type: Item
    size: Normal
    sprite: Objects/Specific/Xenoarchaeology/item_artifacts.rsi
    heldPrefix: ano01
  - type: Actions
  - type: Psionic #Nyano - Summary: makes psionic on creation.
  - type: Construction
    graph: Artifact
    node: done
  - type: GuideHelp
    guides:
    - Xenoarchaeology

- type: entity
  parent: BaseXenoArtifactItem
  id: SimpleXenoArtifactItem
  suffix: Simple
  components:
  - type: Artifact
    nodesMin: 2
    nodesMax: 5

- type: entity
  parent: BaseXenoArtifactItem
  id: MediumXenoArtifactItem
  suffix: Medium
  components:
  - type: Artifact
    nodesMin: 5
    nodesMax: 9

- type: entity
  parent: BaseXenoArtifactItem
  id: ComplexXenoArtifactItem
  suffix: Complex
  components:
  - type: Artifact
    nodesMin: 9
    nodesMax: 13

# this exists for crafting item artifacts so that the final result can be simple, medium, or complex.
- type: entity
  parent: BaseXenoArtifactItem
  id: VariedXenoArtifactItem
  suffix: Varied
  components:
  - type: Artifact
    nodesMin: 2
    nodesMax: 13

- type: entity
  id: ArtifactFragment
  parent: BaseItem
  name: "фрагмент артефакту"
  description: "Зламаний шматок артефакту. Якби у вас було більше, ви б змогли його полагодити."
  components:
  - type: Sprite
    sprite: Objects/Specific/Xenoarchaeology/artifact_fragments.rsi
    layers:
    - state: ancientball1
      map: [ "enum.ArtifactsVisualLayers.Base" ]
  - type: RandomSprite
    available: #yaml hero
    - enum.ArtifactsVisualLayers.Base:
        ancientball1: ""
        ancientball2: ""
        ancientball3: ""
        ancientball4: ""
        ancientball5: ""
        ancientball6: ""
        eldritchball1: ""
        eldritchball2: ""
        eldritchball3: ""
        eldritchball4: ""
        eldritchball5: ""
        eldritchball6: ""
        martianball1: ""
        martianball2: ""
        martianball3: ""
        martianball4: ""
        martianball5: ""
        martianball6: ""
        precursorball1: ""
        precursorball2: ""
        precursorball3: ""
        precursorball4: ""
        precursorball5: ""
        precursorball6: ""
        wizardball1: ""
        wizardball2: ""
        wizardball3: ""
        wizardball4: ""
        wizardball5: ""
        wizardball6: ""
  - type: Appearance
  - type: Item
    size: Normal
  - type: Tag
    tags:
    - ArtifactFragment
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Artifexium
        Quantity: 10
  - type: StaticPrice
    price: 0
  - type: Stack
    stackType: ArtifactFragment
  - type: GuideHelp
    guides:
    - Xenoarchaeology

- type: entity
  parent: ArtifactFragment
  id: ArtifactFragment1
  suffix: Single
  components:
  - type: Stack
    count: 1
