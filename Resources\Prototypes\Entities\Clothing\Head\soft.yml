- type: entity
  parent: [ClothingHeadBaseButcherable, BaseFoldable]
  id: ClothingHeadHeadHatBaseFlippable
  abstract: true
  components:
  - type: Appearance
  - type: Foldable
    canFoldInsideContainer: true
    unfoldVerbText: fold-flip-verb
    foldVerbText: fold-flip-verb
  - type: FoldableClothing
    foldedEquippedPrefix: flipped
    foldedHeldPrefix: flipped
  - type: Sprite
    layers:
    - state: icon
      map: [ "unfoldedLayer" ]
    - state: icon_flipped
      map: ["foldedLayer"]
      visible: false

- type: entity
  parent: ClothingHeadHeadHatBaseFlippable
  id: ClothingHeadHeadHatBaseFlipped
  suffix: flipped
  abstract: true
  components:
  - type: Foldable
    folded: true
  - type: Clothing
    equippedPrefix: flipped
  - type: Item
    heldPrefix: flipped
  - type: Sprite
    layers:
    - state: icon
      map: [ "unfoldedLayer" ]
      visible: false
    - state: icon_flipped
      map: ["foldedLayer"]
      visible: true

- type: entity
  parent: ClothingHeadHeadHatBaseFlippable
  id: ClothingHeadHatBluesoft
  name: "синя бейсболка"
  description: "Це бейсболка без смаку синього кольору."
  components:
  - type: Sprite
    sprite: Clothing/Head/Soft/bluesoft.rsi
  - type: Clothing
    sprite: Clothing/Head/Soft/bluesoft.rsi

- type: entity
  parent: [ClothingHeadHeadHatBaseFlipped, ClothingHeadHatBluesoft]
  id: ClothingHeadHatBluesoftFlipped
  name: "синя бейсболка"

- type: entity
  parent: ClothingHeadHeadHatBaseFlippable
  id: ClothingHeadHatCargosoft
  name: "кашкет вантажника" # DeltaV - Logistics Department replacing Cargo
  description: "Це кашкет, розфарбований у кольори Логістики." # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Sprite
    sprite: Clothing/Head/Soft/cargosoft.rsi
  - type: Clothing
    sprite: Clothing/Head/Soft/cargosoft.rsi
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: [ClothingHeadHeadHatBaseFlipped, ClothingHeadHatCargosoft]
  id: ClothingHeadHatCargosoftFlipped
  name: "кашкет вантажника" # DeltaV - Logistics Department replacing Cargo
  description: "Це кашкет, розфарбований в кольори Логістики. Перевернутий." # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: ClothingHeadHeadHatBaseFlippable
  id: ClothingHeadHatQMsoft
  name: "кашкет голови карго" # DeltaV - Logistics Department replacing Cargo
  description: "Це кашкет, розфарбований у кольори Офіцера Логістики."
  components:
  - type: Sprite
    sprite: Clothing/Head/Soft/qmsoft.rsi
  - type: Clothing
    sprite: Clothing/Head/Soft/qmsoft.rsi

- type: entity
  parent: [ClothingHeadHeadHatBaseFlipped, ClothingHeadHatQMsoft]
  id: ClothingHeadHatQMsoftFlipped
  name: "кашкет голови карго"

- type: entity
  parent: ClothingHeadHeadHatBaseFlippable
  id: ClothingHeadHatCorpsoft
  name: "корпоративна кепка"
  description: "Бейсбольна біта в корпоративних кольорах."
  components:
  - type: Sprite
    sprite: Clothing/Head/Soft/corpsoft.rsi
  - type: Clothing
    sprite: Clothing/Head/Soft/corpsoft.rsi

- type: entity
  parent: [ClothingHeadHeadHatBaseFlipped, ClothingHeadHatCorpsoft]
  id: ClothingHeadHatCorpsoftFlipped
  name: "корпоративна кепка"

- type: entity
  parent: ClothingHeadHeadHatBaseFlippable
  id: ClothingHeadHatGreensoft
  name: "зелена бейсболка"
  description: "Це бейсболка без смаку зеленого кольору."
  components:
  - type: Sprite
    sprite: Clothing/Head/Soft/greensoft.rsi
  - type: Clothing
    sprite: Clothing/Head/Soft/greensoft.rsi

- type: entity
  parent: [ClothingHeadHeadHatBaseFlipped, ClothingHeadHatGreensoft]
  id: ClothingHeadHatGreensoftFlipped
  name: "зелена бейсболка"

- type: entity
  parent: ClothingHeadHeadHatBaseFlippable
  id: ClothingHeadHatBlacksoft
  name: "чорна бейсболка"
  description: "Це бейсболка позбавленого смаку чорного кольору."
  components:
  - type: Sprite
    sprite: Clothing/Head/Soft/blacksoft.rsi
  - type: Clothing
    sprite: Clothing/Head/Soft/blacksoft.rsi

- type: entity
  parent: [ClothingHeadHeadHatBaseFlipped, ClothingHeadHatBlacksoft]
  id: ClothingHeadHatBlacksoftFlipped
  name: "чорна бейсболка"

- type: entity
  parent: ClothingHeadHeadHatBaseFlippable
  id: ClothingHeadHatGreysoft
  name: "сіра бейсболка"
  description: "Це бейсболка позбавленого смаку сірого кольору."
  components:
  - type: Sprite
    sprite: Clothing/Head/Soft/greysoft.rsi
  - type: Clothing
    sprite: Clothing/Head/Soft/greysoft.rsi

- type: entity
  parent: [ClothingHeadHeadHatBaseFlipped, ClothingHeadHatGreysoft]
  id: ClothingHeadHatGreysoftFlipped
  name: "сіра бейсболка"

- type: entity
  parent: ClothingHeadHeadHatBaseFlippable
  id: ClothingHeadHatMimesoft
  name: "кепка міма"
  description: "Це бейсболка позбавленого смаку білого кольору."
  components:
  - type: Sprite
    sprite: Clothing/Head/Soft/mimesoft.rsi
  - type: Clothing
    sprite: Clothing/Head/Soft/mimesoft.rsi

- type: entity
  parent: [ClothingHeadHeadHatBaseFlipped, ClothingHeadHatMimesoft]
  id: ClothingHeadHatMimesoftFlipped
  name: "кепка міма"

- type: entity
  parent: ClothingHeadHeadHatBaseFlippable
  id: ClothingHeadHatOrangesoft
  name: "помаранчева бейсболка"
  description: "Це бейсболка гарного помаранчевого кольору."
  components:
  - type: Sprite
    sprite: Clothing/Head/Soft/orangesoft.rsi
  - type: Clothing
    sprite: Clothing/Head/Soft/orangesoft.rsi

- type: entity
  parent: [ClothingHeadHeadHatBaseFlipped, ClothingHeadHatOrangesoft]
  id: ClothingHeadHatOrangesoftFlipped
  name: "помаранчева бейсболка"

- type: entity
  parent: ClothingHeadHeadHatBaseFlippable
  id: ClothingHeadHatPurplesoft
  name: "фіолетова бейсболка"
  description: "Це бейсболка позбавленого смаку фіолетового кольору."
  components:
  - type: Sprite
    sprite: Clothing/Head/Soft/purplesoft.rsi
  - type: Clothing
    sprite: Clothing/Head/Soft/purplesoft.rsi
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: [ClothingHeadHeadHatBaseFlipped, ClothingHeadHatPurplesoft]
  id: ClothingHeadHatPurplesoftFlipped
  name: "фіолетова бейсболка"
  components:
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: ClothingHeadHeadHatBaseFlippable
  id: ClothingHeadHatRedsoft
  name: "червона бейсболка"
  description: "Це бейсболка позбавленого смаку червоного кольору."
  components:
  - type: Sprite
    sprite: Clothing/Head/Soft/redsoft.rsi
  - type: Clothing
    sprite: Clothing/Head/Soft/redsoft.rsi

- type: entity
  parent: [ClothingHeadHeadHatBaseFlipped, ClothingHeadHatRedsoft]
  id: ClothingHeadHatRedsoftFlipped
  name: "червона бейсболка"

- type: entity
  parent: ClothingHeadHeadHatBaseFlippable
  id: ClothingHeadHatSecsoft
  name: "кашкет охоронця"
  description: "Цей міцний кашкет зі смаком виконаний в червоному кольорі."
  components:
  - type: Sprite
    sprite: Clothing/Head/Soft/secsoft.rsi
  - type: Clothing
    sprite: Clothing/Head/Soft/secsoft.rsi

- type: entity
  parent: [ClothingHeadHeadHatBaseFlipped, ClothingHeadHatSecsoft]
  id: ClothingHeadHatSecsoftFlipped
  name: "кашкет охоронця"

- type: entity
  parent: ClothingHeadHeadHatBaseFlippable
  id: ClothingHeadHatYellowsoft
  name: "жовта бейсболка"
  description: "Жовта бейсболка."
  components:
  - type: Sprite
    sprite: Clothing/Head/Soft/yellowsoft.rsi
  - type: Clothing
    sprite: Clothing/Head/Soft/yellowsoft.rsi

- type: entity
  parent: [ClothingHeadHeadHatBaseFlipped, ClothingHeadHatYellowsoft]
  id: ClothingHeadHatYellowsoftFlipped
  name: "жовта бейсболка"

- type: entity
  parent: ClothingHeadHeadHatBaseFlippable
  id: ClothingHeadHatBizarreSoft
  name: "кепка баламута"
  description: "Дійсно... дивний аксесуар."
  components:
  - type: Sprite
    sprite: Clothing/Head/Soft/bizarresoft.rsi
  - type: Clothing
    sprite: Clothing/Head/Soft/bizarresoft.rsi

- type: entity
  parent: [ClothingHeadHeadHatBaseFlipped, ClothingHeadHatBizarreSoft]
  id: ClothingHeadHatBizarreSoftFlipped
  name: "кепка баламута"

- type: entity
  parent: ClothingHeadHeadHatBaseFlippable
  id: ClothingHeadHatParamedicsoft
  name: "кашкет парамедика"
  description: "Це бейсболка парамедика з логотипом медичного відділу."
  components:
  - type: Sprite
    sprite: Clothing/Head/Soft/paramedicsoft.rsi
  - type: Clothing
    sprite: Clothing/Head/Soft/paramedicsoft.rsi
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: [ClothingHeadHeadHatBaseFlipped, ClothingHeadHatParamedicsoft]
  id: ClothingHeadHatParamedicsoftFlipped
  name: "кашкет парамедика"
  components:
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon
