- type: construction
  name: "чистобот"
  id: cleanbot
  graph: CleanBot
  startNode: start
  targetNode: bot
  category: construction-category-utilities
  objectType: Item
  description: "Цей бот блукає станцією, витираючи калюжі, які бачить."
  icon:
    sprite: Mobs/Silicon/Bots/cleanbot.rsi
    state: cleanbot

- type: construction
  name: "хонкбот"
  id: honkbot
  graph: HonkBot
  startNode: start
  targetNode: bot
  category: construction-category-utilities
  objectType: Item
  description: "Цей бот сигналить і підставляє людей."
  icon:
    sprite: Mobs/Silicon/Bots/honkbot.rsi
    state: honkbot

- type: construction
  name: "джонкбот"
  id: jonkbot
  graph: JonkBot
  startNode: start
  targetNode: bot
  category: construction-category-utilities
  objectType: Item
  description: "Цей клятий бот сигналить, сміється і підставляє людей."
  icon:
    sprite: Mobs/Silicon/Bots/honkbot.rsi
    state: jonkbot

- type: construction
  name: "медібот"
  id: medibot
  graph: MediBot
  startNode: start
  targetNode: bot
  category: construction-category-utilities
  objectType: Item
  description: "Цей бот може допомогти в наданні базової медичної допомоги."
  icon:
    sprite: Mobs/Silicon/Bots/medibot.rsi
    state: medibot

- type: construction
  name: "мімбот"
  id: mimebot
  graph: MimeBot
  startNode: start
  targetNode: bot
  category: construction-category-utilities
  objectType: Item
  description: "Цей бот вміє махати руками."
  icon:
    sprite: Mobs/Silicon/Bots/mimebot.rsi
    state: mimebot

- type: construction
  name: "логібот"
  id: supplybot
  graph: SupplyBot
  startNode: start
  targetNode: bot
  category: construction-category-utilities
  objectType: Item
  description: "Цей бот може бути завантажений вантажем, щоб здійснювати доставку."
  icon:
    sprite: Mobs/Silicon/Bots/supplybot.rsi
    state: supplybot

- type: construction
  name: "гладіатор"
  id: gladiabot
  graph: GladiaBot
  startNode: start
  targetNode: bot
  category: construction-category-utilities
  objectType: Item
  description: "Цей бот бореться за честь і славу!"
  icon:
    sprite: Mobs/Silicon/Bots/gladiabot.rsi
    state: GladiabotFFA

- type: construction
  name: "батонбот"
  id: batonbot
  graph: BatonBot
  startNode: start
  targetNode: bot
  category: construction-category-utilities
  objectType: Item
  description: "Захищає станцію від ворожих диких тварин."
  icon:
    sprite: Mobs/Silicon/Bots/batonbot.rsi
    state: batonbot

- type: construction
  name: "майнебот"
  id: minebot
  graph: MineBot
  startNode: start
  targetNode: bot
  category: construction-category-utilities
  objectType: Item
  description: "Допомагає шахтарям у їхній роботі."
  icon:
    sprite: Mobs/Silicon/Bots/minebot.rsi
    state: minebot

- type: construction
  name: "дізейблербот"
  id: disablerbot
  graph: DisablerBot
  startNode: start
  targetNode: bot
  category: construction-category-utilities
  objectType: Item
  description: "Захищає станцію від ворожих диких тварин."
  icon:
    sprite: Mobs/Silicon/Bots/disablerbot.rsi
    state: disablerbot

- type: construction
  name: "зварювальний робот"
  id: weldbot
  graph: WeldBot
  startNode: start
  targetNode: bot
  category: construction-category-utilities
  objectType: Item
  description: "Ремонтує неорганічний екіпаж."
  icon:
    sprite: Mobs/Silicon/Bots/weldbot.rsi
    state: weldbot

- type: construction
  name: "рослинобот"
  id: plantbot
  graph: PlantBot
  startNode: start
  targetNode: bot
  category: construction-category-utilities
  objectType: Item
  description: "Цей бот доглядає за рослинами."
  icon:
    sprite: Mobs/Silicon/Bots/plantbot.rsi
    state: plantbot

- type: construction
  name: "філлбот"
  id: fillbot
  graph: FillBot
  startNode: start
  targetNode: bot
  category: construction-category-utilities
  objectType: Item
  description: "Цей бот пов’язаний з машиною, в яку можна вставляти предмети за допомогою мультитула."
  icon:
    sprite: Mobs/Silicon/Bots/fillbot.rsi
    state: fillbot

- type: construction
  name: "дрон"
  id: drone
  graph: Drone
  startNode: start
  targetNode: bot
  category: construction-category-utilities
  objectType: Item
  description: "Цей бот є надзвичайно просунутим інженером, керованим ШІ."
  icon:
    sprite: _Imp/Drone/drone.rsi
    state: shell
