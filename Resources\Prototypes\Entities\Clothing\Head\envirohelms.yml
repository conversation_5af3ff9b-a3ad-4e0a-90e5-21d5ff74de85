- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelm
  name: "шолом плазмового екокостюму"
  description: "Спеціальний захисний шолом, який дозволяє плазмовим формам життя безпечно існувати в середовищі, насиченому киснем."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/plain.rsi
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
      right:
      - state: inhand-right
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/plain.rsi
    clothingVisuals:
      head:
      - state: equipped-HELMET
      - state: on-equipped-HELMET
  - type: ToggleableLightVisuals
    inhandVisuals:
      left:
      - state: on-inhand-left
        shader: unshaded
      right:
      - state: on-inhand-right
        shader: unshaded

- type: entity
  parent: ClothingHeadEnvirohelm
  id: ClothingHeadEnvirohelmEmpty
  suffix: Empty
  components:
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmAtmos
  name: "шолом аеродинамічного костюма"
  description: "Космічний шолом, спеціально розроблений для технічного персоналу Plasmamen, на якому звичайні фіолетові смуги замінені на блакитні у стилі atmos."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/atmos.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/atmos.rsi

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmCargo
  name: "логістичний екокостюм шолом"
  description: "Екошолом Plasmaman, призначений для вантажних техніків."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/cargo.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/cargo.rsi

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmCaptain
  name: "шолом капітанського аерокостюма"
  description: "Спеціальний захисний шолом для капітана."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/captain.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/captain.rsi

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmChiefEngineer
  name: "шолом головного інженера в екокостюмі"
  description: "Спеціальний захисний шолом, розроблений для головного інженера, на якому звичайні фіолетові смуги замінені на зелені."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/ce.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/ce.rsi

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmChaplain
  name: "шолом капелана з івіброкостюмом"
  description: "Екологічний шолом, спеціально розроблений для найблагочестивіших плазмоїдів."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/chaplain.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/chaplain.rsi

- type: entity
  parent: ClothingHeadEnvirohelmEnviroslacksColorOrange
  id: ClothingHeadEnvirohelmDetective
  name: "шолом детектива в екокостюмі"
  description: "Спеціальний захисний шолом, розроблений для детективів, що захищає їх від згоряння заживо разом з іншими небажаними чинниками."

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmChemist
  name: "захисний шолом для хімікатів"
  description: "Екокостюм Plasmaman, розроблений для хіміків, має дві помаранчеві смуги, що йдуть по обличчю."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/chemist.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/chemist.rsi

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmClown
  name: "шолом для клоуна в екокостюмі"
  description: "Макіяж нафарбований, диво, що він не обсипається. ХОНК!"
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/clown.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/clown.rsi
  - type: PointLight
    color: "#a777ff"

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmCMO
  name: "шолом головного лікаря івіброкостюм"
  description: "Спеціальний захисний шолом, призначений для головного лікаря. Золота смуга відрізняє їх від іншого медичного персоналу."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/cmo.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/cmo.rsi

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmEngineering
  name: "інженерний шолом-комбінезон"
  description: "Космічний шолом, спеціально розроблений для інженера Плазмамена, зі звичними фіолетовими смужками, заміненими на інженерні помаранчеві."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/engineering.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/engineering.rsi

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmHoP
  name: "шолом керівника підрозділу в екокостюмі"
  description: "Спеціальний захисний шолом, розроблений для начальника відділу кадрів."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/hop.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/hop.rsi

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmHoS
  name: "шолом керівника служби безпеки в екокостюмі"
  description: "Спеціальний захисний шолом, призначений для керівника служби безпеки. Пара золотих смуг відрізняє його від інших членів охорони."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/hos.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/hos.rsi

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmHydroponics
  name: "гідропоніка екокостюм шолом"
  description: "Зелено-синій екошолом, що позначає його власника як ботаніка."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/hydroponics.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/hydroponics.rsi

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmJanitor
  name: "шолом прибиральника в екокостюмі"
  description: "Сіра каска з фіолетовими смужками, що означають, що її власник - двірник."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/janitor.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/janitor.rsi

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmAncientVoid
  name: "Шолом NTSRA в екокостюмі"
  description: "Цей шолом, виготовлений з модифікованого вакцинального костюму NTSRA, був першим розробленим Nanotrasen екологічним шоломом для Plasmamen."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/ancientvoid.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/ancientvoid.rsi
  - type: ToggleableLightVisuals
    clothingVisuals:
      head:
      - state: on-equipped-HELMET
        shader: unshaded
  - type: PointLight
    color: "#ffffff"

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmMedicalDoctor
  name: "шолом медичного аерокостюму"
  description: "Екошолом, розроблений для медичного персоналу Plasmaman, має дві смуги по всій довжині для позначення кількості."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/medical.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/medical.rsi

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmGenitcs
  name: "генетика екокостюм шолом"
  description: "Екошолом Plasmaman, розроблений для генетиків."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/genetics.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/genetics.rsi

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmMime
  name: "шолом для мімічного екокостюму"
  description: "Макіяж намальований, диво, що він не обсипається. Він не дуже яскравий."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/mime.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/mime.rsi
  - type: PointLight
    color: "#ffffff"

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmParamedic
  name: "шолом парамедика в екокостюмі"
  description: "Екошолом, розроблений для парамедиків Plasmaman, з темнішими синіми смугами порівняно з медичною моделлю."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/paramedic.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/paramedic.rsi

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmPrisoner
  name: "шолом для ув'язнених в екокостюмі"
  description: "Захисний шолом Plasmaman для ув'язнених."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ff8300"
    - state: accent-icon
      color: "#3c3c3c"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ff8300"
      - state: accent-inhand-left
        color: "#3c3c3c"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#ff8300"
      - state: accent-inhand-right
        color: "#3c3c3c"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#ff8300"
      - state: accent-equipped-HELMET
        color: "#3c3c3c"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmResearchDirector
  name: "шолом екокостюму наукового директора"
  description: "Спеціальний захисний шолом, розроблений для наукових директорів. Світло-коричневий дизайн, щоб відрізнити їх від інших науковців."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/rd.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/rd.rsi

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmRoboticist
  name: "шолом робототехніка в екокостюмі"
  description: "Екошолом Plasmaman, розроблений для робототехніків."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/roboticist.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/roboticist.rsi
- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmSalvage
  name: "шолом рятувального екокостюму"
  description: "Каска кольору хакі, передана рятувальникам Plasmamen."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/salvage.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/salvage.rsi
  - type: PointLight
    color: "#c77eff"

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmScientist
  name: "шолом для наукового екокостюму"
  description: "Екошолом Plasmaman, розроблений для науковців."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/scientist.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/scientist.rsi

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmSec
  name: "захисний шолом-комбінезон"
  description: "Захисний шолом Plasmaman, розроблений для співробітників служб безпеки, захищає їх від згоряння заживо та інших небажаних наслідків."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#8f3132"
    - state: accent-icon
      color: "#2e2e2e"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#8f3132"
      - state: accent-inhand-left
        color: "#2e2e2e"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#8f3132"
      - state: accent-inhand-right
        color: "#2e2e2e"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#8f3132"
      - state: accent-equipped-HELMET
        color: "#2e2e2e"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmSecBlue
  name: "синій захисний шолом для екокостюму"
  description: "Крутий синій шолом для співробітників служби безпеки Plasmaman."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#b9c1d9"
    - state: accent-icon
      color: "#36476b"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#b9c1d9"
      - state: accent-inhand-left
        color: "#36476b"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#b9c1d9"
      - state: accent-inhand-right
        color: "#36476b"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#b9c1d9"
      - state: accent-equipped-HELMET
        color: "#36476b"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmSecGrey
  name: "сірий захисний шолом-комбінезон"
  description: "Світло-сірий шолом з яскраво-червоними підсвічуваннями для персоналу Plasmamen Security."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#7e7e7e"
    - state: accent-icon
      color: "#a61d1d"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#7e7e7e"
      - state: accent-inhand-left
        color: "#a61d1d"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#7e7e7e"
      - state: accent-inhand-right
        color: "#a61d1d"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#7e7e7e"
      - state: accent-equipped-HELMET
        color: "#a61d1d"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmVirology
  name: "вірусологічний екокостюм шолом"
  description: "Шолом, який носять найбезпечніші люди на станції, ті, хто має повний імунітет до жахіть, які вони створюють."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/virology.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/virology.rsi

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmWarden
  name: "шолом охоронця в костюмі наглядача"
  description: "Захисний шолом Plasmaman, призначений для наглядача. Пара білих смуг додається, щоб відрізнити їх від інших членів охорони."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/warden.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/warden.rsi

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmOperative
  name: "оперативний шолом-комбінезон"
  description: "Будь-хто, хто носить це - крутий і заслуговує хоча б побіжного кивка в знак поваги."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/tacticool.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/tacticool.rsi

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmTacticool
  name: "шолом tacticool envirosuit"
  description: "Без сумніву, цей шолом ставить вас над усіма іншими плазмаменами."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/tacticool.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/tacticool.rsi

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmCentcomAgent
  name: "Шолом агента CentCom в аерокостюмі"
  description: "Спеціальний захисний шолом, розроблений для юридичної команди CentCom. Щоб пролита кава не вбила бідолаху."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/centcom_agent.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/centcom_agent.rsi

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmCentcomOfficial
  name: "Шолом офіцера CentCom в аерокостюмі"
  description: "Спеціальний захисний шолом, розроблений для співробітників CentCom. Вони дійсно люблять свій зелений колір."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/centcom_official.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/centcom_official.rsi

- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmCentcomOfficer
  name: "Шолом офіцера CentCom в аерокостюмі"
  description: "Спеціальний захисний шолом, розроблений для офіцерів Центру."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/centcom_officer.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/centcom_officer.rsi

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmCourier
  name: "шолом кур'єра в екокостюмі"
  description: "Шолом для кур'єра в костюмі-антикостюмі."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#4a281f"
    - state: accent-icon
      color: "#c2911e"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#4a281f"
      - state: accent-inhand-left
        color: "#c2911e"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#4a281f"
      - state: accent-inhand-right
        color: "#c2911e"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#4a281f"
      - state: accent-equipped-HELMET
        color: "#c2911e"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmMailCarrier
  name: "шолом івіброкостюм листоноші"
  description: "Пахне хорошою пенсією."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#394dc6"
    - state: accent-icon
      color: "#dcdcdc"
    - state: midaccent-icon
      color: "#d82927"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#394dc6"
      - state: accent-inhand-left
        color: "#dcdcdc"
      - state: midaccent-inhand-left
        color: "#d82927"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#394dc6"
      - state: accent-inhand-right
        color: "#dcdcdc"
      - state: midaccent-inhand-right
        color: "#d82927"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#394dc6"
      - state: accent-equipped-HELMET
        color: "#dcdcdc"
      - state: midaccent-equipped-HELMET
        color: "#d82927"
      - state: visor-equipped-HELMET
  - type: ClothingAddFaction
    faction: Mailman

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmMusician
  name: "шолом музиканта в аерокостюмі"
  description: "Експерти дивуються, як Плазмамен може грати на трубі в такому шоломі."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#3c335b"
    - state: accent-icon
      color: "#f3f5f4"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#3c335b"
      - state: accent-inhand-left
        color: "#f3f5f4"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#3c335b"
      - state: accent-inhand-right
        color: "#f3f5f4"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#3c335b"
      - state: accent-equipped-HELMET
        color: "#f3f5f4"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmReporter
  name: "шолом репортера в екокостюмі"
  description: "Шолом для репортера з екокостюма."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#112334"
    - state: accent-icon
      color: "#79121b"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#112334"
      - state: accent-inhand-left
        color: "#79121b"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#112334"
      - state: accent-inhand-right
        color: "#79121b"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#112334"
      - state: accent-equipped-HELMET
        color: "#79121b"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmGladiator
  name: "гладіаторський шолом-комбінезон"
  description: "Захищає голову від іграшкових списів та отруйного кисню."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#dab13b"
    - state: accent-icon
      color: "#a349a4"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#dab13b"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#dab13b"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#dab13b"
      - state: accent-equipped-HELMET
        color: "#a349a4"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmMantis
  name: "шолом мантіса в екокостюмі"
  description: "Шолом для мантіса з фантазійною золотою смужкою."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#46566d"
    - state: accent-icon
      color: "#7d2322"
    - state: midaccent-icon
      color: "#d4af48"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#46566d"
      - state: accent-inhand-left
        color: "#7d2322"
      - state: midaccent-inhand-left
        color: "#d4af48"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#46566d"
      - state: accent-inhand-right
        color: "#7d2322"
      - state: midaccent-inhand-right
        color: "#d4af48"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#46566d"
      - state: accent-equipped-HELMET
        color: "#7d2322"
      - state: midaccent-equipped-HELMET
        color: "#d4af48"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmSafari
  name: "шолом для сафарі-інвайронмента"
  description: "Це робить вас мішенню для місцевих."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#d3b986"
    - state: accent-icon
      color: "#a349a4"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#d3b986"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#d3b986"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#d3b986"
      - state: accent-equipped-HELMET
        color: "#a349a4"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmMartialGi
  name: "шолом для екокостюму gi"
  description: "Білий шолом з чорними смугами, що використовується для бойових мистецтв."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ffffff"
    - state: accent-icon
      color: "#3b3b3b"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffffff"
      - state: accent-inhand-left
        color: "#3b3b3b"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#ffffff"
      - state: accent-inhand-right
        color: "#3b3b3b"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#ffffff"
      - state: accent-equipped-HELMET
        color: "#3b3b3b"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmQM
  name: "шолом офіцера логістики з івіброкостюмом"
  description: "Спеціальний захисний шолом для офіцера логістики."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#bb934b"
    - state: accent-icon
      color: "#ffc000"
    - state: midaccent-icon
      color: "#d08200"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#bb934b"
      - state: accent-inhand-left
        color: "#ffc000"
      - state: midaccent-inhand-left
        color: "#d08200"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#bb934b"
      - state: accent-inhand-right
        color: "#ffc000"
      - state: midaccent-inhand-right
        color: "#d08200"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#bb934b"
      - state: accent-equipped-HELMET
        color: "#ffc000"
      - state: midaccent-equipped-HELMET
        color: "#d08200"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmBoxing
  name: "шолом боксерський екокостюм"
  description: "Білий шолом з червоними смужками."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#eeeeee"
    - state: accent-icon
      color: "#a81818"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#eeeeee"
      - state: accent-inhand-left
        color: "#a81818"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#eeeeee"
      - state: accent-inhand-right
        color: "#a81818"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#eeeeee"
      - state: accent-equipped-HELMET
        color: "#a81818"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmAdminAssistant
  name: "шолом івіброкостюм для адміністративного асистента"
  description: "Білий шолом з темно-синіми смужками."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ffffff"
    - state: accent-icon
      color: "#315266"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffffff"
      - state: accent-inhand-left
        color: "#315266"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#ffffff"
      - state: accent-inhand-right
        color: "#315266"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#ffffff"
      - state: accent-equipped-HELMET
        color: "#315266"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmBlackPink
  name: "чорно-рожевий шолом з екокостюма"
  description: "Як вам цей шолом з екокостюма?"
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#292929"
    - state: accent-icon
      color: "#f4a1b7"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#292929"
      - state: accent-inhand-left
        color: "#f4a1b7"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#292929"
      - state: accent-inhand-right
        color: "#f4a1b7"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#292929"
      - state: accent-equipped-HELMET
        color: "#f4a1b7"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmBlackPinkAlt
  name: "чорно-рожевий шолом з екокостюма"
  suffix: Alternative
  description: "У цьому шоломі-ковпакуванні хочеться вбити цю любов."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#f4a1b7"
    - state: accent-icon
      color: "#292929"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#f4a1b7"
      - state: accent-inhand-left
        color: "#292929"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#f4a1b7"
      - state: accent-inhand-right
        color: "#292929"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#f4a1b7"
      - state: accent-equipped-HELMET
        color: "#292929"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmBlueshield
  name: "шолом івіброкостюму Blueshield"
  description: "Шолом-анвікостюм Plasmaman, розроблений для блакитного щита."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#535353"
    - state: accent-icon
      color: "#0044d4"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#535353"
      - state: accent-inhand-left
        color: "#0044d4"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#535353"
      - state: accent-inhand-right
        color: "#0044d4"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#535353"
      - state: accent-equipped-HELMET
        color: "#0044d4"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmTrans
  name: "шолом для транс енвайронментального костюму"
  description: "Улюблений головний убір трансильванських плазмаменів, щоб запобігти опікам від кисню."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#FFFFFF"
    - state: accent-icon
      color: "#ffb0c0"
    - state: sideaccent-icon
      color: "#5dd2ff"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#FFFFFF"
      - state: accent-inhand-left
        color: "#ffb0c0"
      - state: sideaccent-inhand-left
        color: "#5dd2ff"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#FFFFFF"
      - state: accent-inhand-right
        color: "#ffb0c0"
      - state: sideaccent-inhand-right
        color: "#5dd2ff"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#FFFFFF"
      - state: accent-equipped-HELMET
        color: "#ffb0c0"
      - state: sideaccent-equipped-HELMET
        color: "#5dd2ff"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmPrisonGuard
  name: "шолом тюремного охоронця в екокостюмі"
  description: "Сподіваємось, ув'язнений не забере його у вас!"
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#d76b00"
    - state: accent-icon
      color: "#363636"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#d76b00"
      - state: accent-inhand-left
        color: "#363636"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#d76b00"
      - state: accent-inhand-right
        color: "#363636"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#d76b00"
      - state: accent-equipped-HELMET
        color: "#363636"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmBrigmedic
  name: "шолом санітара в екокостюмі"
  description: "Каска, надана санітарам-плазмотерапевтам."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#486782"
    - state: accent-icon
      color: "#2e2e2e"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#486782"
      - state: accent-inhand-left
        color: "#2e2e2e"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#486782"
      - state: accent-inhand-right
        color: "#2e2e2e"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#486782"
      - state: accent-equipped-HELMET
        color: "#2e2e2e"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmNanotrasenRepresentative
  name: "шолом скафандра корпоративного представника"
  description: "Чорний шолом скафандра, який носить корпоративний представник, з чорними та золотими акцентами."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#292929"
    - state: accent-icon
      color: "#ffce5b"
    - state: midaccent-icon
      color: "#266199"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#292929"
      - state: accent-inhand-left
        color: "#ffce5b"
      - state: midaccent-inhand-left
        color: "#266199"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#292929"
      - state: accent-inhand-right
        color: "#ffce5b"
      - state: midaccent-inhand-right
        color: "#266199"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#292929"
      - state: accent-equipped-HELMET
        color: "#ffce5b"
      - state: midaccent-equipped-HELMET
        color: "#266199"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmMagistrate
  name: "шолом судді в екокостюмі"
  description: "Однотонний білий екокостюм з жовтими смужками."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ebebeb"
    - state: accent-icon
      color: "#ffce5b"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ebebeb"
      - state: accent-inhand-left
        color: "#ffce5b"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#ebebeb"
      - state: accent-inhand-right
        color: "#ffce5b"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#ebebeb"
      - state: accent-equipped-HELMET
        color: "#ffce5b"
      - state: visor-equipped-HELMET

# Color envirohelms
- type: entity
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmColorWhite
  name: "шолом білого екокостюму"
  description: "Типовий білий енвірогельмінт."
  components:
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/white.rsi
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/white.rsi

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmColorGrey
  name: "сірий шолом-комбінезон"
  description: "Сірий шолом з екокостюма."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#b3b3b3"
    - state: accent-icon
      color: "#a349a4"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#b3b3b3"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#b3b3b3"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#b3b3b3"
      - state: accent-equipped-HELMET
        color: "#a349a4"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmColorBlack
  name: "шолом чорного кольору з екокостюма"
  description: "Чорний шолом з екокостюма."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#3f3f3f"
    - state: accent-icon
      color: "#a349a4"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#3f3f3f"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#3f3f3f"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#3f3f3f"
      - state: accent-equipped-HELMET
        color: "#a349a4"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmColorRed
  name: "шолом червоного екокостюму"
  description: "Червоний шолом-комбінезон."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#d1423f"
    - state: accent-icon
      color: "#a349a4"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#d1423f"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#d1423f"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#d1423f"
      - state: accent-equipped-HELMET
        color: "#a349a4"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmColorGreen
  name: "зелений шолом екокостюму"
  description: "Зелений шолом з екокостюма."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#9ed63a"
    - state: accent-icon
      color: "#a349a4"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#9ed63a"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#9ed63a"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#9ed63a"
      - state: accent-equipped-HELMET
        color: "#a349a4"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmColorDarkGreen
  name: "темно-зелений шолом з екокостюму"
  description: "Темно-зелений шолом з екокостюма."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#79CC26"
    - state: accent-icon
      color: "#a349a4"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#79CC26"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#79CC26"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#79CC26"
      - state: accent-equipped-HELMET
        color: "#a349a4"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmColorBlue
  name: "синій шолом-комбінезон"
  description: "Синій шолом з екокостюма."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#52aecc"
    - state: accent-icon
      color: "#a349a4"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#52aecc"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#52aecc"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#52aecc"
      - state: accent-equipped-HELMET
        color: "#a349a4"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmColorDarkBlue
  name: "темно-синій шолом з екокостюма"
  description: "Темно-синій шолом з екокостюма."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#3285ba"
    - state: accent-icon
      color: "#a349a4"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#3285ba"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#3285ba"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#3285ba"
      - state: accent-equipped-HELMET
        color: "#a349a4"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmColorTeal
  name: "шолом для екокостюму чирок"
  description: "Шолом для екокостюма."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#77f3b7"
    - state: accent-icon
      color: "#a349a4"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#77f3b7"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#77f3b7"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#77f3b7"
      - state: accent-equipped-HELMET
        color: "#a349a4"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmColorMaroon
  name: "бордовий шолом екокостюму"
  description: "Бордовий шолом-комбінезон."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#cc295f"
    - state: accent-icon
      color: "#a349a4"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#cc295f"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#cc295f"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#cc295f"
      - state: accent-equipped-HELMET
        color: "#a349a4"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmColorPink
  name: "рожевий шолом-комбінезон"
  description: "Рожевий шолом-антикостюм. Тож принеси!"
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ff8cff"
    - state: accent-icon
      color: "#8b3e8c"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ff8cff"
      - state: accent-inhand-left
        color: "#8b3e8c"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#ff8cff"
      - state: accent-inhand-right
        color: "#8b3e8c"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#ff8cff"
      - state: accent-equipped-HELMET
        color: "#8b3e8c"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmColorYellow
  name: "жовтий захисний шолом для екокостюму"
  description: "Жовтий шолом з екокостюма."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ffe14d"
    - state: accent-icon
      color: "#a349a4"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffe14d"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#ffe14d"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#ffe14d"
      - state: accent-equipped-HELMET
        color: "#a349a4"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmColorPurple
  name: "фіолетовий шолом для екокостюму"
  description: "Фіолетовий шолом з екокостюма."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#9f70cc"
    - state: accent-icon
      color: "#843b85"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#9f70cc"
      - state: accent-inhand-left
        color: "#843b85"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#9f70cc"
      - state: accent-inhand-right
        color: "#843b85"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#9f70cc"
      - state: accent-equipped-HELMET
        color: "#843b85"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmColorOrange
  name: "помаранчевий шолом екокостюму"
  description: "Помаранчевий шолом з екокостюма."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ff8c19"
    - state: accent-icon
      color: "#a349a4"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ff8c19"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#ff8c19"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#ff8c19"
      - state: accent-equipped-HELMET
        color: "#a349a4"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmColorLightBrown
  name: "світло-коричневий шолом з екокостюму"
  description: "Світло-коричневий шолом з екокостюма."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#a17229"
    - state: accent-icon
      color: "#a349a4"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#a349a4"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#a17229"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#a17229"
      - state: accent-equipped-HELMET
        color: "#a349a4"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmColorBrown
  name: "коричневий шолом з екокостюма"
  description: "Коричневий шолом з екокостюма."
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#543e1b"
    - state: accent-icon
      color: "#a349a4"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#543e1b"
      - state: accent-inhand-left
        color: "#a349a4"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#543e1b"
      - state: accent-inhand-right
        color: "#a349a4"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#543e1b"
      - state: accent-equipped-HELMET
        color: "#a349a4"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmEnviroslacksColorRed
  name: "червоний шолом enviroslacks"
  description: "Улюблений проект особливо розкішного Плазмамена, цей екологічний шолом має червоні акценти. Модно!"
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ffffff"
    - state: accent-icon
      color: "#99211f"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffffff"
      - state: accent-inhand-left
        color: "#99211f"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#ffffff"
      - state: accent-inhand-right
        color: "#99211f"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#ffffff"
      - state: accent-equipped-HELMET
        color: "#99211f"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmEnviroslacksColorOrange
  name: "помаранчевий шолом enviroslacks"
  description: "Улюблений проект особливо розкішного Plasmaman, цей екологічний шолом має помаранчеві акценти. Пікантно!"
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ffffff"
    - state: accent-icon
      color: "#c2680f"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffffff"
      - state: accent-inhand-left
        color: "#c2680f"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#ffffff"
      - state: accent-inhand-right
        color: "#c2680f"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#ffffff"
      - state: accent-equipped-HELMET
        color: "#c2680f"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmEnviroslacksColorGreen
  name: "зелений шолом enviroslacks"
  description: "Улюблений проект особливо розкішного Плазмамена, цей екологічний шолом має зелені акценти. Зелений!"
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ffffff"
    - state: accent-icon
      color: "#5b991f"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffffff"
      - state: accent-inhand-left
        color: "#5b991f"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#ffffff"
      - state: accent-inhand-right
        color: "#5b991f"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#ffffff"
      - state: accent-equipped-HELMET
        color: "#5b991f"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmEnviroslacksColorBlue
  name: "синій шолом enviroslacks"
  description: "Улюблений проект особливо розкішного Плазмамена, цей екологічний шолом має сині акценти. Круто!"
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ffffff"
    - state: accent-icon
      color: "#2b5c99"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffffff"
      - state: accent-inhand-left
        color: "#2b5c99"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#ffffff"
      - state: accent-inhand-right
        color: "#2b5c99"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#ffffff"
      - state: accent-equipped-HELMET
        color: "#2b5c99"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmEnviroslacksMNK
  name: "Шолом MNK enviroslacks"
  description: "Витончений екологічний шолом від MNK. Класика!"
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#ffffff"
    - state: accent-icon
      color: "#363636"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffffff"
      - state: accent-inhand-left
        color: "#363636"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#ffffff"
      - state: accent-inhand-right
        color: "#363636"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#ffffff"
      - state: accent-equipped-HELMET
        color: "#363636"
      - state: visor-equipped-HELMET

- type: entity
  parent: ClothingHeadEnvirohelmCustomBase
  id: ClothingHeadEnvirohelmEnviroslacksMNKAlt
  name: "однотонний шолом enviroslacks"
  description: "Витончений екологічний шолом від MNK. Нуар!"
  suffix: Alternative
  components:
  - type: Sprite
    layers:
    - state: icon
      color: "#3b3b3b"
    - state: accent-icon
      color: "#d6d6d6"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#3b3b3b"
      - state: accent-inhand-left
        color: "#d6d6d6"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#3b3b3b"
      - state: accent-inhand-right
        color: "#d6d6d6"
      - state: visor-inhand-right
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#3b3b3b"
      - state: accent-equipped-HELMET
        color: "#d6d6d6"
      - state: visor-equipped-HELMET
