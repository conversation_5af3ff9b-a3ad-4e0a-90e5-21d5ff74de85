- type: job
  id: AtmosphericTechnician
  name: job-name-atmostech
  description: job-description-atmostech
  playTimeTracker: JobAtmosphericTechnician
  requirements:
    - !type:CharacterDepartmentTimeRequirement
      department: Engineering
      min: 36000 # DeltaV - 10 hours
    - !type:CharacterEmployerRequirement
      employers:
      - He<PERSON>estusIndustries
      - EinsteinEngines
      - NanoTrasen
  startingGear: AtmosphericTechnicianGear
  icon: "JobIconAtmosphericTechnician"
  supervisors: job-supervisors-ce
  access:
  - Maintenance
  - Engineering
  - External
  - Atmospherics

- type: startingGear
  id: AtmosphericTechnicianGear
  subGear:
  - AtmosphericTechnicianPlasmamanGear

  equipment:
    jumpsuit: ClothingUniformJumpsuitAtmos
    back: ClothingBackpackAtmospherics
    shoes: ClothingShoesColorWhite
    id: AtmosPDA
    ears: ClothingHeadsetEngineering
  innerClothingSkirt: ClothingUniformJumpskirtAtmos
  satchel: ClothingBackpackSatchelEngineeringFilled
  duffelbag: ClothingBackpackDuffelEngineeringFilled

- type: startingGear
  id: AtmosphericTechnicianPlasmamanGear
  parent: BasePlasmamanGear
  equipment:
    jumpsuit: ClothingUniformEnvirosuitAtmos
    head: ClothingHeadEnvirohelmAtmos
    gloves: ClothingHandsGlovesEnviroglovesAtmos
