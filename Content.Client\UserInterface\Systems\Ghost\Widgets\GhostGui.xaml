<widgets:GhostGui xmlns="https://spacestation14.io"
                  xmlns:widgets="clr-namespace:Content.Client.UserInterface.Systems.Ghost.Widgets"
                  HorizontalAlignment="Center">
    <BoxContainer Orientation="Horizontal">
        <Button Name="ReturnToBodyButton" Text="{Loc ghost-gui-return-to-body-button}" />
        <Button Name="GhostWarpButton" Text="{Loc ghost-gui-ghost-warp-button}" />
        <Button Name="GhostRolesButton" />
        <Button Name="GhostBarButton" Text="{Loc 'ghost-target-window-ghostbar'}" /> <!-- Goobstation - Ghost Bar -->
        <Button Name="ReturnToRound" Text="{Loc ghost-gui-return-to-round-button}" />
    </BoxContainer>
</widgets:GhostGui>
