## UI
door-remote-open-close-text = Opens and Closes Doors
door-remote-toggle-bolt-text = Toggles Bolts
door-remote-emergency-access-text = Toggles Emergency Access
door-remote-invalid-text = Invalid
door-remote-mode-label = Mode: [color=white]{$modeString}[/color]

## Entity

door-remote-switch-state-open-close = You switch the remote to open and close doors
door-remote-switch-state-toggle-bolts = You switch the remote to toggle bolts
door-remote-switch-state-toggle-emergency-access = You switch the remote to toggle emergency access
door-remote-no-power = The door is not powered
door-remote-denied = Access denied

