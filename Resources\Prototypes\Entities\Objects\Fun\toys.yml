## Plushies

- type: entity
  parent: BaseItem
  id: BasePlushie
  abstract: true
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
  - type: Tag
    tags:
      - Payload
      - ClothMade
  - type: EmitSoundOnUse
    sound:
      collection: ToySqueak
  - type: EmitSoundOnActivate
    sound:
      collection: ToySqueak
  - type: EmitSoundOnCollide
    sound:
      collection: ToySqueak
  - type: EmitSoundOnLand
    sound:
      collection: ToyFall
  - type: EmitSoundOnTrigger
    sound:
      collection: ToySqueak
  - type: UseDelay
    delay: 1.0
  - type: MeleeWeapon
    wideAnimationRotation: 180
    soundHit:
      collection: ToySqueak
    damage:
      types:
        Blunt: 0
  - type: DamageOtherOnHit
  - type: PhysicalComposition
    materialComposition:
      Cloth: 100
  - type: StaticPrice
    price: 5
  - type: Food
    requiresSpecialDigestion: true
    useSound:
      collection: ToySqueak
    delay: 2
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Fiber
          Quantity: 10

- type: entity
  parent: BasePlushie
  id: PlushieThrongler
  name: "плюшевий нагрудник"
  description: "М'яка іграшка, що нагадує вантажникам про те, чого вони більше не можуть мати."
  components:
    - type: Sprite
      sprite: Objects/Weapons/Melee/ThronglerPlushie.rsi
      state: icon
    - type: MeleeWeapon
      wideAnimationRotation: -135
      attackRate: 10
    - type: Item
      size: Ginormous
      sprite: Objects/Weapons/Melee/Throngler-in-hand.rsi

- type: entity
  parent: BasePlushie
  id: PlushieGhost
  name: "м'яка іграшка-привид"
  description: "Померла 8 хвилин тому"
  components:
    - type: Sprite
      sprite: Mobs/Ghosts/ghost_human.rsi
      state: icon
      noRot: true
    - type: Item
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.25,-0.25,0.25,0.25"
          density: 20
          mask:
            - ItemMask
          restitution: 0.98
          friction: 0.01
    - type: Physics
      angularDamping: 0.02
      linearDamping: 0.02
      fixedRotation: true
      bodyType: Dynamic
    - type: TileFrictionModifier
      modifier: 0.1
    - type: Tag
      tags:
        - ForceableFollow
        - PlushieGhost
        - Payload
    - type: RandomWalk
      accumulatorRatio: 0.5
      maxSpeed: 1
      minSpeed: 0.25

- type: entity
  parent: PlushieGhost
  id: PlushieGhostRevenant
  name: "рлюшевий ревенант"
  suffix: DO NOT MAP
  description: "Іграшка, якою можна налякати медпункт."
  components:
    - type: Item
      size: Normal
    - type: Sprite
      sprite: DeltaV/Mobs/Ghosts/revenant.rsi # DeltaV - Custom revenant sprite
      state: icon
      noRot: true
    - type: Construction
      graph: PlushieGhostRevenant
      node: plushie

- type: entity
  parent: BasePlushie
  id: PlushieBee
  name: "плюшева бджола"
  description: "Мила іграшка, що нагадує милих осіб працюючих в ІТ. Лише найжорстокіші можуть викинути таку."
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: plushie_h
  - type: Item
    size: Normal
    sprite: Objects/Fun/toys.rsi
    heldPrefix: bee
  - type: Extractable
    grindableSolutionName: bee
  - type: SolutionContainerManager
    solutions:
      bee:
        reagents:
        - ReagentId: GroundBee
          Quantity: 10
      food:
        maxVol: 10
        reagents:
        - ReagentId: GroundBee
          Quantity: 5
        - ReagentId: Fiber
          Quantity: 5
  - type: Clothing
    quickEquip: false
    sprite: Objects/Fun/toys.rsi
    equippedPrefix: bee
    slots:
    - HEAD
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE

- type: entity
  parent: BasePlushie
  id: PlushieHampter
  name: "плюшевий хом'як"
  description: "Мила м'яка іграшка, що нагадує ом'яка з сплющеною мордочкою."
  components:
  - type: Sprite
    state: plushie_hampter
  - type: EmitSoundOnUse
    sound:
      path: /Audio/Items/Toys/mousesqueek.ogg
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Items/Toys/mousesqueek.ogg
  - type: EmitSoundOnActivate
    sound:
      path: /Audio/Items/Toys/mousesqueek.ogg
  - type: EmitSoundOnTrigger
    sound:
      path: /Audio/Items/Toys/mousesqueek.ogg
  - type: Food
    requiresSpecialDigestion: true
    useSound:
      path: /Audio/Items/Toys/mousesqueek.ogg
  - type: MeleeWeapon
    wideAnimationRotation: 180
    soundHit:
      path: /Audio/Items/Toys/mousesqueek.ogg

- type: entity
  parent: PlushieBee
  id: PlushieRGBee
  name: "плюшева RGBджола"
  description: "Мила іграшка, що нагадує звичайну плюшеву бджілку, коли ви під Оракульною олією."
  components:
  - type: PointLight
    radius: 1.5
    energy: 2
  - type: RgbLightController
    layers: [ 0 ]

- type: entity
  parent: BasePlushie
  id: PlushieNuke
  name: "плюшевий нюкер"
  description: "М'яка іграшка, що нагадує ядерного оперативника Синдикату. На етикетці зазначено, що це виключно плід уяви."
  components:
  - type: Sprite
    state: plushie_nuke
  - type: Item
    heldPrefix: plushienuke

- type: entity
  parent: BasePlushie
  id: PlushieRouny
  name: "руніплюш"
  description: "Руні з плюшевим рум'янцем"
  components:
  - type: Sprite
    state: plushie_rouny
  - type: Rotatable

- type: entity
  parent: BasePlushie
  id: PlushieLamp
  name: "плюшевий світильник"
  description: "Друг, що випромінює світло!"
  components:
  - type: Sprite
    state: plushie_lamp
  - type: PointLight
    radius: 1.5
    energy: 2
    netsync: false

- type: entity
  parent: BasePlushie
  id: PlushieArachind
  name: "плюшевий арахнід"
  description: "Чарівна м'яка іграшка, що нагадує арахнідів. На дотик шовковиста."
  components:
  - type: Sprite
    state: plushie_arachnid
  - type: EmitSoundOnUse
    sound:
      path: /Audio/Voice/Arachnid/arachnid_laugh.ogg
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Voice/Arachnid/arachnid_laugh.ogg
  - type: EmitSoundOnActivate
    sound:
      path: /Audio/Voice/Arachnid/arachnid_laugh.ogg
  - type: EmitSoundOnTrigger
    sound:
      path: /Audio/Voice/Arachnid/arachnid_laugh.ogg
  - type: Food
    requiresSpecialDigestion: true
    useSound:
      path: /Audio/Voice/Arachnid/arachnid_laugh.ogg
  - type: MeleeWeapon
    wideAnimationRotation: 180
    soundHit:
      path: /Audio/Voice/Arachnid/arachnid_laugh.ogg

- type: entity
  parent: BasePlushie
  id: PlushieLizard #Weh!
  name: "плюшева рептилія"
  description: "Мила м'яка іграшка, що нагадує прямоходячих рептилій. Виготовлена компанією ЦентКом на підтримку боротьби зі специсизмом на робочому місці. \"Привітайте своїх нових колег як цю іграшку, з відкритими обіймами!\""
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: plushie_lizard
  - type: Item
    heldPrefix: plushielizard
  - type: EmitSoundOnUse
    sound:
      path: /Audio/Items/Toys/weh.ogg
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Items/Toys/weh.ogg
  - type: EmitSoundOnActivate
    sound:
      path: /Audio/Items/Toys/weh.ogg
  - type: EmitSoundOnTrigger
    sound:
      path: /Audio/Items/Toys/weh.ogg
  - type: Food
    requiresSpecialDigestion: true
    useSound:
      path: /Audio/Items/Toys/weh.ogg
  - type: MeleeWeapon
    wideAnimationRotation: 180
    soundHit:
      path: /Audio/Items/Toys/weh.ogg
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: JuiceThatMakesYouWeh
        Quantity: 30
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Fiber
          Quantity: 10
        - ReagentId: JuiceThatMakesYouWeh
          Quantity: 10
  - type: Clothing
    quickEquip: false
    sprite: Objects/Fun/toys.rsi
    equippedPrefix: lizard
    slots:
    - HEAD

- type: entity
  parent: PlushieLizard
  id: PlushieLizardMirrored
  components:
  - type: Item
    size: Small
  - type: Sprite
    state: plushie_lizard_mirrored

- type: entity
  parent: BasePlushie
  id: PlushieSpaceLizard #ᵂᵉʰ!
  name: "плюшева косморептилія"
  description: "Мила м'яка іграшка, що нагадує прямоходячу рептилію в EVA скафандрі. Виготовлена компанією ЦентКом на підтримку боротьби зі специсизмом на просторах космосу. \"Привітайте своїх нових колег як цю іграшку, з відкритими обіймами!\""
  components:
  - type: Item
    heldPrefix: spacelizard
  - type: Sprite
    state: plushie_spacelizard
  - type: EmitSoundOnUse
    sound:
      path: /Audio/Items/Toys/muffled_weh.ogg
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Items/Toys/muffled_weh.ogg
  - type: EmitSoundOnActivate
    sound:
      path: /Audio/Items/Toys/muffled_weh.ogg
  - type: EmitSoundOnTrigger
    sound:
      path: /Audio/Items/Toys/muffled_weh.ogg
  - type: Food
    requiresSpecialDigestion: true
    useSound:
      path: /Audio/Items/Toys/muffled_weh.ogg
  - type: MeleeWeapon
    wideAnimationRotation: 180
    soundHit:
      path: /Audio/Items/Toys/muffled_weh.ogg
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: JuiceThatMakesYouWeh
        Quantity: 30
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Fiber
          Quantity: 10
        - ReagentId: JuiceThatMakesYouWeh
          Quantity: 10
  - type: Clothing
    slots:
    - HEAD
    quickEquip: false
    clothingVisuals:
      head:
      - state: spacelizard-equipped-HELMET
        offset: "0, 0.03"

- type: entity
  parent: BasePlushie
  id: PlushieDiona
  name: "плюшева діона"
  description: "Чарівна м'яка іграшка, що нагадує діону. Любить воду і обійми. Не мочити!"
  components:
    - type: Sprite
      layers:
        - state: plushie_diona
        - state: plushie_diona1
          map: [ "enum.SolutionContainerLayers.Fill" ]
          visible: false
    - type: EmitSoundOnUse
      sound:
        path: /Audio/Items/Toys/toy_rustle.ogg
    - type: EmitSoundOnLand
      sound:
        path: /Audio/Items/Toys/toy_rustle.ogg
    - type: EmitSoundOnActivate
      sound:
        path: /Audio/Items/Toys/toy_rustle.ogg
    - type: EmitSoundOnTrigger
      sound:
        path: /Audio/Items/Toys/toy_rustle.ogg
    - type: MeleeWeapon
      wideAnimationRotation: 180
      soundHit:
        path: /Audio/Items/Toys/toy_rustle.ogg
    - type: Food
      requiresSpecialDigestion: true
      useSound:
        path: /Audio/Items/Toys/toy_rustle.ogg
    - type: SolutionContainerManager
      solutions:
        plushie:
          maxVol: 1
        food:
          maxVol: 10
          reagents:
          - ReagentId: Fiber
            Quantity: 10

    - type: RefillableSolution
      solution: plushie
    - type: SolutionContainerVisuals
      maxFillLevels: 1
      fillBaseName: plushie_diona
      changeColor: false
    - type: Appearance
    - type: Reactive
      reactions:
        - reagents: [ Water ]
          methods: [ Touch ]
          effects:
            - !type:AddToSolutionReaction
              solution: plushie
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.3,-0.3,0.3,0.3"
          density: 15
          mask:
            - ItemMask
        rehydrate:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.3,-0.3,0.3,0.3"
          hard: false
          layer:
            - LowImpassable
    - type: CollisionWake
      enabled: false

- type: entity
  parent: BasePlushie
  id: PlushieSharkBlue
  name: "м'яка блакитна акула"
  description: "Велика і безпечна, для того щоб відкрити для себе глибинний світ океану."
  components:
  - type: Sprite
    sprite: Objects/Fun/sharkplush.rsi
    state: blue
  - type: Item
    heldPrefix: blue
    storedRotation: -90
  - type: Tag
    tags:
      - PlushieSharkBlue
      - Payload

- type: entity
  parent: PlushieSharkBlue
  id: PlushieSharkPink
  name: "м'яка рожева акула"
  description: "Хіхі пік :3"
  components:
  - type: Sprite
    sprite: Objects/Fun/sharkplush.rsi
    state: pink
  - type: Item
    heldPrefix: pink
  - type: Tag
    tags:
      - PlushieSharkPink
      - Payload

- type: entity
  parent: PlushieSharkBlue
  id: PlushieSharkGrey
  name: "м'яка сіра акула"
  description: "Варіант для тихих та стриманих. Обожнює сідлати похмурі хвилі."
  components:
  - type: Sprite
    sprite: Objects/Fun/sharkplush.rsi
    state: grey
  - type: Item
    heldPrefix: grey
  - type: Tag
    tags:
      - PlushieSharkGrey
      - Payload

- type: entity
  parent: BasePlushie
  id: PlushieRatvar
  name: "ратплюш"
  description: "Невеличка м'яка іграшка у вигляді верховного божества Ратвара."
  components:
  - type: Item
    size: Normal
  - type: Sprite
    state: plushie_ratvar

- type: entity
  parent: BasePlushie
  id: PlushieNar
  name: "нар'плюш"
  description: "Невеличка м'яка іграшка у вигляді старшого божества Нар'сі."
  components:
  - type: Item
    size: Normal
  - type: Sprite
    state: narplush

- type: entity
  parent: BasePlushie
  id: PlushieCarp
  name: "плюшевий космокороп"
  description: "Чарівна м'яка іграшка, що нагадує страхітливого космічного коропа."
  components:
  - type: Sprite
    state: carpplush
  - type: Item
    heldPrefix: carpplush
    storedRotation: -90
  - type: EmitSoundOnUse
    sound:
      path: /Audio/Effects/bite.ogg
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Effects/bite.ogg
  - type: EmitSoundOnActivate
    sound:
      path: /Audio/Effects/bite.ogg
  - type: EmitSoundOnTrigger
    sound:
      path: /Audio/Effects/bite.ogg
  - type: Food
    requiresSpecialDigestion: true
    useSound:
      path: /Audio/Effects/bite.ogg
  - type: MeleeWeapon
    wideAnimationRotation: -90
    soundHit:
      path: /Audio/Effects/bite.ogg
    angle: 0
    animation: WeaponArcBite # Rrrr!
  - type: Tag
    tags:
    - PlushieCarp # DeltaV - fish labeler craft

- type: entity
  parent: PlushieCarp
  id: PlushieMagicarp
  name: "плюшевий магікороп"
  description: "Чарівна м'яка іграшка, що нагадує надзвичайно рідкісного магігарпа."
  components:
  - type: Sprite
    state: magicplush

- type: entity
  parent: PlushieCarp
  id: PlushieRainbowCarp
  name: "веселковий плюшевий короп"
  description: "Чарівна м'яка іграшка, що нагадує легендарного веселкового коропа."
  components:
  - type: Sprite
    state: rainbowcarpplush
  - type: PointLight
    radius: 1.5
    energy: 2
  - type: RgbLightController
    layers: [ 0 ]

- type: entity
  parent: PlushieCarp
  id: PlushieHolocarp
  name: "плюшевий голокороп"
  description: "Голографічна м'яка іграшка, що нагадує заклятого ворога науки - голокоропа."
  components:
  - type: Sprite
    state: holoplush

- type: entity
  parent: BasePlushie
  id: PlushieSlime
  name: "плюшевий слимак"
  description: "Мила м'яка іграшка, що нагадує слимака. По суті, це мішок для хокею з м'ячем."
  components:
  - type: Sprite
    state: plushie_slime

- type: entity
  parent: BasePlushie
  id: PlushieSnake
  name: "плюшева змійка"
  description: "Чарівна м'яка іграшка, що нагадує змію."
  components:
  - type: Sprite
    state: plushie_snake
  - type: EmitSoundOnUse
    sound:
      path: /Audio/Items/Toys/rattle.ogg
  - type: EmitSoundOnTrigger
    sound:
      path: /Audio/Items/Toys/rattle.ogg
  - type: Food
    requiresSpecialDigestion: true
    useSound:
      path: /Audio/Items/Toys/rattle.ogg
  - type: MeleeWeapon
    wideAnimationRotation: 180
    soundHit:
      path: /Audio/Items/Toys/rattle.ogg

- type: entity
  parent: BasePlushie
  id: ToyMouse
  name: "плюшева миша"
  description: "Барвиста іграшкова мишка!"
  components:
  - type: Sprite
    state: toy_mouse
  - type: EmitSoundOnUse
    sound:
      path: /Audio/Items/Toys/mousesqueek.ogg
  - type: EmitSoundOnTrigger
    sound:
      path: /Audio/Items/Toys/mousesqueek.ogg
  - type: Food
    requiresSpecialDigestion: true
    useSound:
      path: /Audio/Items/Toys/mousesqueek.ogg
  - type: MeleeWeapon
    wideAnimationRotation: -90
    soundHit:
      path: /Audio/Items/Toys/mousesqueek.ogg
  - type: Clothing
    quickEquip: false
    sprite: Objects/Fun/toys.rsi
    equippedPrefix: mouse
    slots:
    - HEAD
  # Begin DeltaV additions: make cats attack mouse toys to play with them
  - type: Damageable
    damageContainer: StructuralInorganic # not actually destructible, just lets it be attacked
  - type: MobState
  - type: MobThresholds
    thresholds:
      0: Alive # always be considered alive
  - type: NpcFactionMember
    factions:
    - Mouse
  # End DeltaV additions

- type: entity
  parent: BasePlushie
  id: ToyRubberDuck
  name: "гумова качечка"
  description: "Не принесена сюди океанськими течіями."
  components:
  - type: Item
    size: Tiny
  - type: Sprite
    sprite: Objects/Fun/ducky.rsi
    state: icon
  - type: MeleeWeapon
    attackRate: .6
    range: 1.6
    damage:
      types:
        Blunt: 0.1
    heavyDamageBaseModifier: 2
    maxTargets: 8
    angle: 25
  - type: Clothing
    quickEquip: false
    sprite: Objects/Fun/ducky.rsi
    slots:
    - HEAD
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
  - type: EmitSoundOnUse
    sound:
      path: /Audio/Items/Toys/quack.ogg
  - type: EmitSoundOnTrigger
    sound:
      path: /Audio/Items/Toys/quack.ogg
  - type: Food
    requiresSpecialDigestion: true
    useSound:
      path: /Audio/Items/Toys/mousesqueek.ogg
  - type: Tag
    tags:
    - ToyRubberDuck

- type: entity
  parent: BasePlushie
  id: PlushieVox
  name: "плюшевий вокс"
  description: "СКРІІІІІІІІІ!"
  components:
  - type: Sprite
    state: plushie_vox
  - type: EmitSoundOnUse
    sound:
      path: /Audio/Voice/Vox/shriek1.ogg
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Voice/Vox/shriek1.ogg
  - type: EmitSoundOnTrigger
    sound:
      path: /Audio/Voice/Vox/shriek1.ogg
  - type: Food
    requiresSpecialDigestion: true
    useSound:
      path: /Audio/Voice/Vox/shriek1.ogg
  - type: MeleeWeapon
    wideAnimationRotation: 180
    soundHit:
      path: /Audio/Voice/Vox/shriek1.ogg

- type: entity
  parent: BasePlushie
  id: PlushieAtmosian
  name: "плюшевий атмоінженер"
  description: "Мила м'яка іграшка, що нагадує відважного атмоінженера. На жаль, не працююча на справжній роботі."
  components:
  - type: Sprite
    state: plushie_atmosian

- type: entity
  parent: BasePlushie
  id: PlushieXeno
  name: "ксеноплюш"
  description: "Мила м'яка іграшка, що нагадує страшного ксеноморфа. Нам пощастило, що це лише іграшка."
  components:
  - type: Item
    size: Normal
  - type: Sprite
    state: plushie_xeno
  - type: EmitSoundOnUse
    sound:
      path: /Audio/Weapons/Xeno/alien_spitacid.ogg
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Weapons/Xeno/alien_spitacid.ogg
  - type: EmitSoundOnActivate
    sound:
      path: /Audio/Weapons/Xeno/alien_spitacid.ogg
  - type: EmitSoundOnTrigger
    sound:
      path: /Audio/Weapons/Xeno/alien_spitacid.ogg
  - type: Food
    requiresSpecialDigestion: true
    useSound:
      path: /Audio/Items/Toys/mousesqueek.ogg
  - type: MeleeWeapon
    wideAnimationRotation: 180
    soundHit:
      path: /Audio/Weapons/Xeno/alien_spitacid.ogg

- type: entity
  parent: BasePlushie
  id: PlushiePenguin
  name: "плюшевий пінгвін"
  description: "Діставай вибухівку"
  components:
  - type: Sprite
    state: plushie_penguin

- type: entity
  parent: BasePlushie
  id: PlushieHuman
  name: "плюшева людина"
  description: "М'яка іграшка низької якості, зроблена з фетру. Ця людина гола. Ця людина плаче. Ця людина кричить."
  components:
  - type: Sprite
    state: plushie_human
  - type: EmitSoundOnUse
    sound:
      path: /Audio/Voice/Human/malescream_1.ogg
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Voice/Human/malescream_2.ogg
  - type: EmitSoundOnActivate
    sound:
      path: /Audio/Voice/Human/malescream_3.ogg
  - type: Food
    requiresSpecialDigestion: true
    useSound:
      path: /Audio/Voice/Human/malescream_1.ogg
  - type: MeleeWeapon
    soundHit:
      path: /Audio/Voice/Human/malescream_4.ogg
  - type: EmitSoundOnTrigger
    sound:
      path: /Audio/Voice/Human/malescream_5.ogg

- type: entity
  parent: BasePlushie
  id: PlushieArachne
  name: "Плюшевий арахнід"
  description: "Плюшева фігурка арахніда, напівлюдини-напівпавука. Чому вона виглядає знайомо?"
  components:
  - type: Sprite
    state: plushie_arachne
  - type: EmitSoundOnUse
    sound:
      path: /Audio/Voice/Human/womanlaugh.ogg
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Voice/Human/female_sigh.ogg
  - type: EmitSoundOnActivate
    sound:
      path: /Audio/Voice/Human/womanlaugh.ogg
  - type: Food
    requiresSpecialDigestion: true
    useSound:
      path: /Audio/Voice/Human/femalescream_4.ogg
  - type: MeleeWeapon
    soundHit:
      path: /Audio/Voice/Human/femalescream_2.ogg
  - type: EmitSoundOnTrigger
    sound:
      path: /Audio/Voice/Human/femalescream_5.ogg

- type: entity
  parent: BasePlushie
  id: PlushieMoth
  name: "плюшева моль"
  description: "Мила і пухнастка іграшка. Для теплих обіймів, бз сквік!"
  components:
  - type: Sprite
    state: plushie_moth
  - type: EmitSoundOnUse
    sound:
      path: /Audio/Voice/Moth/moth_squeak.ogg # DeltaV - Give back the mothplushie the squeak
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Voice/Moth/moth_scream.ogg # DeltaV - Make the mothplushie scream on landing
  - type: EmitSoundOnActivate
    sound:
      path: /Audio/Voice/Moth/moth_squeak.ogg # DeltaV - Give back the mothplushie the squeak
  - type: EmitSoundOnTrigger
    sound:
      path: /Audio/Voice/Moth/moth_scream.ogg # DeltaV - Make the mothplushie scream on trigger
  - type: MeleeWeapon
    soundHit:
      path: /Audio/Voice/Moth/moth_squeak.ogg # DeltaV - Give back the mothplushie the squeak
  - type: Construction # DeltaV - Mothroach hide craft, see Prototypes/Nyanotrasen/Recipes/Crafting/Graphs/fun/mothplushie.yml
    graph: MothPlushie
    node: plush
  - type: Food
    requiresSpecialDigestion: true
    useSound:
      path: /Audio/Voice/Moth/moth_chitter.ogg

- type: entity
  parent: BasePlushie
  id: PlushieJester #Weh!
  name: "плюшевий блазень"
  description: "Сумнівна маленька істота, що замишляє недобрі справи."
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: plushie_jester

- type: entity
  parent: BasePlushie
  id: PlushieSlips
  name: "плюшевий прибиральник"
  description: "Безшумний прибиральник, той, що не говорить \"Ух\"!"
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: plushie_slips
  - type: EmitSoundOnUse
    sound:
      path: /Audio/Effects/silence.ogg
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Effects/silence.ogg
  - type: EmitSoundOnActivate
    sound:
      path: /Audio/Effects/silence.ogg
  - type: MeleeWeapon
    soundHit:
      path: /Audio/Effects/silence.ogg
  - type: Tag
    tags:
    - Soap
  - type: Slippery
    paralyzeTime: 0.7
    launchForwardsMultiplier: 0.5
  - type: StepTrigger
    intersectRatio: 0.2
  - type: CollisionWake
    enabled: false
  - type: Physics
    bodyType: Dynamic
  - type: Fixtures
    fixtures:
      slips:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.3,0.4,0.3"
        layer:
        - SlipLayer
        hard: false
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.3,0.4,0.3"
        density: 10
        mask:
        - ItemMask

- type: entity
  parent: BasePlushie
  id: PlushieGnome
  name: "плюшевий гном"
  description: "М'яка іграшка, що нагадує гнома! або дроворуба..."
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: gnome

- type: entity
  parent: BasePlushie
  id: PlushieLoveable
  name: "плюшевий красунчик"
  description: "М'яка іграшка, що нагадує... істоту."
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: loveable

- type: entity
  parent: BasePlushie
  id: PlushieDeer
  name: "плюшевий олень"
  description: "М'яка іграшка, що нагадує оленя!"
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: deer

- type: entity
  parent: BasePlushie
  id: PlushieIpc
  name: "плюшевий ipc"
  description: "М'яка іграшка, що нагадує ipc!"
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: ipc

- type: entity
  parent: BasePlushie
  id: PlushieGrey
  name: "плюшевий сірий"
  description: "М'яка іграшка, що нагадує сірого!"
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: grey

- type: entity
  parent: BasePlushie
  id: PlushieAbductor
  name: "плюшевий викрадач"
  description: "М'яка іграшка, що нагадує викрадача!"
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: abductor

- type: entity
  parent: BasePlushie
  id: PlushieAbductorAgent
  name: "плюшевий агент-викрадач"
  description: "М'яка іграшка, що нагадує агента-викрадача!"
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: abductor_agent

- type: entity
  parent: BasePlushie
  id: PlushieRedFox
  name: "плюшевий рудий лис"
  description: "Симпатична плюшева іграшка, що схожа на рудого фоксо!"
  components:
  - type: Sprite
    sprite: Objects/Fun/foxplushie.rsi
    state: red

- type: entity
  parent: BasePlushie
  id: PlushiePurpleFox
  name: "плюшевий фіолетовий лис"
  description: "Симпатична плюшева іграшка, що схожа на фіолетового фоксо!"
  components:
  - type: Sprite
    sprite: Objects/Fun/foxplushie.rsi
    state: purple

- type: entity
  parent: BasePlushie
  id: PlushiePinkFox
  name: "плюшевий рожевий лис"
  description: "Симпатична плюшева іграшка, що схожа рожевого фоксо!"
  components:
  - type: Sprite
    sprite: Objects/Fun/foxplushie.rsi
    state: pink

- type: entity
  parent: BasePlushie
  id: PlushieOrangeFox
  name: "плюшевий помаранчевий лис"
  description: "Симпатична плюшева іграшка, що схожа на помаранчевого фоксо!"
  components:
  - type: Sprite
    sprite: Objects/Fun/foxplushie.rsi
    state: orange

- type: entity
  parent: BasePlushie
  id: PlushieMarbleFox
  name: "плюшевий мармуровий лис"
  description: "Симпатична плюшева іграшка, що схожа на мармурового фоксо!"
  components:
  - type: Sprite
    sprite: Objects/Fun/foxplushie.rsi
    state: marble

- type: entity
  parent: BasePlushie
  id: PlushieCrimsonFox
  name: "плюшевий малиновий лис"
  description: "Симпатична плюшева іграшка, що схожа на малинового фоксо!"
  components:
  - type: Sprite
    sprite: Objects/Fun/foxplushie.rsi
    state: crimson

- type: entity
  parent: BasePlushie
  id: PlushieCoffeeFox
  name: "плюшевий кавовий лис"
  description: "Симпатична плюшева іграшка, що схожа на кавового фоксо!"
  components:
  - type: Sprite
    sprite: Objects/Fun/foxplushie.rsi
    state: coffee

- type: entity
  parent: BasePlushie
  id: PlushieBlueFox
  name: "плюшевий блакитний лис"
  description: "Симпатична плюшева іграшка, що схожа на блакитного фоксо!"
  components:
  - type: Sprite
    sprite: Objects/Fun/foxplushie.rsi
    state: blue

- type: entity
  parent: BasePlushie
  id: PlushieBlackFox
  name: "плюшевий чорний лис"
  description: "Симпатична плюшева іграшка, що схожа на чорного фоксо!"
  components:
  - type: Sprite
    sprite: Objects/Fun/foxplushie.rsi
    state: black

- type: entity
  parent: BasePlushie
  id: PlushieVulp
  name: "плюшева вульпа"
  description: "Вульпканіна плюшевий ведмедик, якого принаймні можна обійняти без ризику бути укушеним."
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: plushie_vulp
  - type: EmitSoundOnUse
    sound:
      path: /Audio/DeltaV/Voice/Vulpkanin/dog_bark1.ogg
  - type: EmitSoundOnLand
    sound:
      path: /Audio/DeltaV/Voice/Vulpkanin/dog_bark1.ogg
  - type: EmitSoundOnActivate
    sound:
      path: /Audio/DeltaV/Voice/Vulpkanin/dog_bark1.ogg
  - type: MeleeWeapon
    soundHit:
      path: /Audio/DeltaV/Voice/Vulpkanin/dog_bark1.ogg

- type: entity
  parent: PlushieVulp
  id: PlushieTrystan
  name: "плюшева офісна вульпа"
  description: "Ті, хто гигикають!"
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: plushie_trystan

- type: entity
  parent: PlushieVulp
  id: PlushieCorgi
  name: "плюшевий коргі"
  description: "Видання в плюшевій обкладинці!"
  components:
  - type: Sprite
    sprite: Objects/Fun/corgiplushie.rsi
    state: corgi

- type: entity
  parent: PlushieVulp
  id: PlushieGirlyCorgi
  name: "плюшевий коргі для дівчаток"
  components:
  - type: Sprite
    sprite: Objects/Fun/corgiplushie.rsi
    state: girlycorgi

- type: entity
  parent: PlushieVulp
  id: PlushieRobotCorgi
  name: "плюшевий робот коргі"
  components:
  - type: Sprite
    sprite: Objects/Fun/corgiplushie.rsi
    state: robotcorgi

- type: entity
  parent: BasePlushie
  id: BasePlushieCat
  description: "М'яка іграшка, що нагадує милого котика!"
  abstract: true
  components:
  - type: EmitSoundOnUse
    sound:
      path: /Audio/Nyanotrasen/Voice/Felinid/cat_meow1.ogg
  - type: EmitSoundOnLand
    sound:
      path: /Audio/Nyanotrasen/Voice/Felinid/cat_meow1.ogg
  - type: EmitSoundOnActivate
    sound:
      path: /Audio/Nyanotrasen/Voice/Felinid/cat_meow1.ogg
  - type: MeleeWeapon
    soundHit:
      path: /Audio/Nyanotrasen/Voice/Felinid/cat_meow1.ogg

- type: entity
  parent: BasePlushieCat
  id: PlushieCatBlack
  name: "плюшевий чорний кіт"
  components:
  - type: Sprite
    sprite: Objects/Fun/catplushie.rsi
    state: black

- type: entity
  parent: BasePlushieCat
  id: PlushieCatGrey
  name: "плюшевий сірий кіт"
  components:
  - type: Sprite
    sprite: Objects/Fun/catplushie.rsi
    state: grey

- type: entity
  parent: BasePlushieCat
  id: PlushieCatOrange
  name: "плюшевий помаранчевий кіт"
  components:
  - type: Sprite
    sprite: Objects/Fun/catplushie.rsi
    state: orange

- type: entity
  parent: BasePlushieCat
  id: PlushieCatSiames
  name: "плюшевий сіамський кіт"
  components:
  - type: Sprite
    sprite: Objects/Fun/catplushie.rsi
    state: siames

- type: entity
  parent: BasePlushieCat
  id: PlushieCatTabby
  name: "плюшевий кіт таббі"
  components:
  - type: Sprite
    sprite: Objects/Fun/catplushie.rsi
    state: tabby

- type: entity
  parent: BasePlushieCat
  id: PlushieCatTuxedo
  name: "плюшевий кіт у смокінгу"
  components:
  - type: Sprite
    sprite: Objects/Fun/catplushie.rsi
    state: tuxedo

- type: entity
  parent: BasePlushieCat
  id: PlushieCatWhite
  name: "плюшевий білий кіт"
  components:
  - type: Sprite
    sprite: Objects/Fun/catplushie.rsi
    state: white

## Cheapo Figurines

- type: entity
  parent: BaseItem
  id: BaseFigurineCheapo
  name: "статуетка"
  description: "Маленька мініатюра."
  abstract: true
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
  - type: PhysicalComposition
    materialComposition:
      Plastic: 100
  - type: StaticPrice
    price: 25
  - type: Tag
    tags:
    - Figurine

- type: entity
  parent: BaseFigurineCheapo
  id: ToyAi
  name: "іграшковий ШІ"
  description: "Зменшене ядро іграшкового АІ."
  components:
  - type: Sprite
    state: AI

- type: entity
  parent: BaseFigurineCheapo
  id: ToyNuke
  name: "ядерна іграшка"
  description: "Пластикова модель ядерної бомби. Без урану в складі... напевно."
  components:
  - type: Sprite
    state: nuketoy
  - type: Tag
    tags:
      - Payload
  - type: UseDelay
    delay: 180.0
  - type: EmitSoundOnUse
    sound:
      path: /Audio/Machines/Nuke/nuke_alarm.ogg
      params:
        volume: -5
        maxDistance: 10
  - type: EmitSoundOnActivate
    sound:
      path: /Audio/Machines/Nuke/nuke_alarm.ogg
      params:
        volume: -5
        maxDistance: 10
  - type: EmitSoundOnTrigger
    sound:
      path: /Audio/Machines/Nuke/nuke_alarm.ogg
      params:
        volume: -5
        maxDistance: 10

- type: entity
  parent: BasePlushie
  id: ToyIan
  name: "іграшка Іана"
  description: "Не може їсти, але такий же пухнастий, як справжній хлопець!"
  components:
  - type: Item
    size: Tiny
  - type: Sprite
    state: ian
  - type: EmitSoundOnUse
    sound:
      path: /Audio/Items/Toys/ian.ogg
  - type: EmitSoundOnTrigger
    sound:
      path: /Audio/Items/Toys/ian.ogg
  - type: MeleeWeapon
    soundHit:
      path: /Audio/Items/Toys/ian.ogg
  - type: Food
    requiresSpecialDigestion: true
    useSound:
      path: /Audio/Items/Toys/ian.ogg

## Toyweapons

- type: entity
  abstract: true
  parent: BaseItem
  id: FoamWeaponBase
  components:
  - type: Sprite
  - type: Item
    size: Normal

- type: entity
  parent: FoamWeaponBase
  id: FoamCrossbow
  name: "пінопластовий арбалет"
  description: "Спрямувавши його на службу безпеки, ви можете наїстися свинцю."
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: foamcrossbow
  - type: Item
    size: Normal
    sprite: Objects/Fun/toys.rsi
    heldPrefix: foamcrossbow
  - type: Gun
    fireRate: 0.5
    selectedMode: SemiAuto
    availableModes:
      - SemiAuto
    soundGunshot:
      path: /Audio/Weapons/click.ogg
  - type: BallisticAmmoProvider
    whitelist:
      tags:
        - BulletFoam
    capacity: 1
    soundInsert:
      path: /Audio/Weapons/drawbow2.ogg
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
        ents: []

- type: entity
  parent: BaseItem
  id: ToyGunBase
  name: "ToyGunBase"
  description: "Корінцевий зубастий і пагонистий."
  abstract: true
  components:
  - type: Sprite

- type: entity
  parent: ToyGunBase
  id: RevolverCapGun
  name: "іграшковий пістолет"
  description: "Виглядає майже як справжній! Від 8 років і старше."
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
  - type: Item
    sprite: Objects/Fun/toys.rsi
    heldPrefix: capgun
  - type: Tag
    tags:
    - Sidearm
    - ToySidearm
  - type: Gun
    selectedMode: SemiAuto
    availableModes:
      - SemiAuto
    fireRate: 2
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/revolver.ogg
      params:
        volume: 2.25
    clumsyProof: true
  - type: RevolverAmmoProvider
    whitelist:
      tags:
        - CartridgeCap
        - SpeedLoaderCap
    proto: CartridgeCap
    capacity: 6
    chambers: [ True, True, True, True, True, True ]
    ammoSlots: [ null, null, null, null, null, null ]
    soundInsert:
      path: /Audio/Weapons/Guns/MagIn/revolver_magin.ogg
  - type: ContainerContainer
    containers:
      revolver-ammo: !type:Container

- type: entity
  parent: RevolverCapGun
  id: RevolverCapGunFake
  name: "іграшковий пістолет"
  suffix: Fake
  description: "Виглядає майже як справжній! Від 8 років і старше."
  components:
  - type: RevolverAmmoProvider
    whitelist:
      tags:
        - CartridgeCap
        - SpeedLoaderCap
        - CartridgeMagnum
        - SpeedLoaderMagnum
    proto: CartridgeMagnumAP

- type: entity
  parent: BaseItem
  id: FoamBlade
  name: "піноблейд"
  description: "На ньому написано \"Найвідданійший фанат Жалослава Перевертовича\"."
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: foamblade
  - type: MeleeWeapon
    attackRate: .6666
    angle: 0
    animation: WeaponArcThrust
    wideAnimationRotation: 90
    damage:
      types:
        Blunt: 0
  - type: Item
    size: Small
    sprite: Objects/Fun/toys.rsi
    heldPrefix: foamblade
  - type: UseDelay

# MISC

- type: entity
  parent: BaseItem
  id: Basketball
  name: "баскетбольний м'яч"
  description: "Де знаходяться суди?"
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: basketball
  - type: EmitSoundOnCollide
    sound:
      path: /Audio/Effects/Footsteps/bounce.ogg
  - type: Item
    size: Normal
    sprite: Objects/Fun/toys.rsi
    heldPrefix: bask
  - type: TileFrictionModifier
    modifier: 0.5

- type: entity
  parent: BaseItem
  id: Football
  name: "футбольний м'яч"
  description: "Інакше відома як гандеґґ."
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: football
  - type: Item
    size: Small
    sprite: Objects/Fun/toys.rsi
    heldPrefix: footb

- type: entity
  parent: BaseItem
  id: BeachBall
  name: "пляжний м'яч"
  description: "Простий пляжний м'яч - один з найпопулярніших продуктів Nanotrasen. \"Чому ми робимо пляжні м'ячі? Тому що ми можемо! (ТМ)\" - Nanotrasen"
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: beachball
  - type: EmitSoundOnCollide
    sound:
      path: /Audio/Effects/Footsteps/bounce.ogg
  - type: Item
    size: Normal
    sprite: Objects/Fun/toys.rsi
    heldPrefix: beachb
  - type: TileFrictionModifier
    modifier: 0.05

- type: entity
  parent: BaseItem
  id: BalloonSyn
  name: "повітряна куля Синдикату "
  description: "Вручається найсміливішим душам, які пережили атракціон \"атомний твістер\" у \"Сінділенді\"."
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: synb
  - type: Item
    size: Small
    sprite: Objects/Fun/toys.rsi
    heldPrefix: synb
  - type: Damageable
    damageContainer: Inorganic
  - type: Tag
    tags:
    - Balloon

- type: entity
  parent: BaseItem
  id: BalloonNT
  name: "повітряна куля НаноТрейзену"
  description: "Спеціально розроблений, щоб завдати максимальної психологічної шкоди оперативникам Синдикату."
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: ntb
  - type: Item
    size: Small
    sprite: Objects/Fun/toys.rsi
    heldPrefix: ntb
  - type: Damageable
    damageContainer: Inorganic
  - type: Tag
    tags:
    - Balloon

- type: entity
  parent: BaseItem
  id: BalloonCorgi
  name: "повітряна куля коргі"
  description: "Це як володіти справжнім собакою - але набагато легше."
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: corgib
  - type: Item
    size: Small
    sprite: Objects/Fun/toys.rsi
    heldPrefix: corgib
  - type: Damageable
    damageContainer: Inorganic
  - type: Tag
    tags:
    - Balloon

- type: entity
  parent: BaseItem
  id: SingularityToy
  name: "сингулярна брендована іграшка"
  description: "Масове виробництво садистського корпоративного конгломерату!"
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: singularitytoy
  - type: Icon
    sprite: Objects/Fun/toys.rsi
    state: singularitytoy
  - type: SingularityDistortion
    intensity: 2000
    falloffPower: 2.6
  - type: Item
    size: Normal
    sprite: Objects/Fun/toys.rsi
    heldPrefix: singularitytoy

- type: entity
  parent: BaseItem
  id: TeslaToy
  name: "Тедді Тесла"
  description: "Улюблена іграшка великого інженера Ніколи Тесли."
  components:
  - type: Sprite
    sprite: Structures/Power/Generation/Tesla/energy_miniball.rsi
    shader: unshaded
    layers:
    - state: tesla_projectile
  - type: PointLight
    enabled: true
    radius: 5
    color: "#4080FF"
  - type: Electrified
    shockDamage: 2
    shockTime: 0.8
    requirePower: false
  - type: EmitSoundOnCollide
    sound:
      path: /Audio/Effects/Lightning/lightningshock.ogg
      params:
        variation: 0.65
        volume: -10

- type: entity
  parent: BaseItem
  id: PonderingOrb
  name: "сфера роздумів"
  description: "Обтяжливо, чувак... Дійсно обтяжливо."
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: orb
    shader: unshaded
  - type: PointLight
    radius: 2
    color: "#00CCFF"
  - type: Item
    size: Normal
    sprite: Objects/Fun/toys.rsi
    heldPrefix: orb
  - type: TileFrictionModifier
    modifier: 0.001

- type: entity
  parent: BaseItem
  id: ToySword
  name: "іграшковий меч"
  description: "Новий пластиковий меч Sandy-Cat! Поставляється з реалістичним звуком і повним кольором! Виглядає майже як справжній!"
  components:
    - type: EnergySword
      colorOptions:
        - DodgerBlue
    - type: ItemToggle
      soundActivate:
        path: /Audio/Weapons/ebladeon.ogg
      soundDeactivate:
        path: /Audio/Weapons/ebladeoff.ogg
    - type: ItemToggleActiveSound
      activeSound:
        path: /Audio/Weapons/ebladehum.ogg
    - type: Sprite
      sprite: Objects/Fun/toy_sword.rsi
      layers:
        - state: e_sword
        - state: e_sword_blade
          color: "#FFFFFF"
          visible: false
          shader: unshaded
          map: [ "blade" ]
    - type: Item
      size: Small
      sprite: Objects/Fun/toy_sword.rsi
    - type: UseDelay
      delay: 1.0
    - type: PointLight
      enabled: false
      radius: 2
      energy: 2
      color: white
      netsync: false
    - type: Appearance
    - type: ToggleableLightVisuals
      spriteLayer: blade
      inhandVisuals:
        left:
          - state: inhand-left-blade
            shader: unshaded
        right:
          - state: inhand-right-blade
            shader: unshaded
    - type: ItemTogglePointLight
    - type: DisarmMalus
      malus: 0
    - type: StaminaDamageOnHit
      damage: 8
    - type: MeleeWeapon
      damage:
        types:
          Blunt: 0
    - type: ItemToggleMeleeWeapon
      activatedSoundOnHit:
        path: /Audio/Weapons/eblade1.ogg
        params:
          variation: 0.250
      activatedSoundOnHitNoDamage:
        path: /Audio/Weapons/eblade1.ogg
        params:
          variation: 0.250
      activatedSoundOnSwing:
        path: /Audio/Weapons/eblademiss.ogg
        params:
          variation: 0.125

- type: entity
  parent: BasePlushie
  id: ToyAmongPequeno
  name: "малий амонг"
  description: "sus!"
  components:
  - type: Sprite
    sprite: Objects/Fun/pequeno.rsi
    layers:
    - state: base
      map: [ "enum.DamageStateVisualLayers.Base" ]
    - state: visor
  - type: RandomSprite
    available:
      - enum.DamageStateVisualLayers.Base:
          base: Sixteen

- type: entity
  parent: BaseItem
  id: FoamCutlass
  name: "пінопластовий ніж"
  description: "Перевдягніться в пірата і змусьте своїх друзів пройтись по дошці."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Melee/cutlass.rsi
    state: foam_icon
  - type: MeleeWeapon
    attackRate: .6666
    range: 2.0
    angle: 0
    animation: WeaponArcThrust
    damage:
      types:
        Blunt: 0
  - type: Item
    size: Normal
    sprite: Objects/Weapons/Melee/cutlass.rsi

- type: entity
  parent: BaseItem
  id: ClownRecorder
  name: "клоунський диктофон"
  description: "Коли ти просто не можеш розсміятися природним чином!"
  components:
  - type: Sprite
    sprite: Objects/Fun/clownrecorder.rsi
    state: icon
  - type: EmitSoundOnUse
    sound:
      collection: ClownRecorder
  - type: EmitSoundOnActivate
    sound:
      collection: ClownRecorder
  - type: EmitSoundOnTrigger
    sound:
      collection: ClownRecorder
  - type: UseDelay
    delay: 30.0
  - type: Tag
    tags:
    - ClownRecorder
    - Payload

- type: entity
  parent: BaseItem
  id: ToyHammer
  name: "гумовий молоток"
  description: "Яскраво забарвлений молоток з гуми."
  components:
    - type: Sprite
      sprite: Objects/Fun/rubber_hammer.rsi
      state: icon
    - type: WeaponRandom
      damageBonus:
          types:
              Blunt: 1000
    - type: StaminaDamageOnHit
      damage: 8
    - type: Item
      size: Small
      sprite: Objects/Fun/rubber_hammer.rsi
    - type: Appearance
    - type: DisarmMalus
      malus: 0
    - type: MeleeWeapon
      soundHit:
        collection: RubberHammer
        params:
            variation: 0.03
            volume: 3
      soundNoDamage:
        collection: RubberHammer
        params:
            variation: 0.03
            volume: 3
      damage:
        types:
          Blunt: 0

- type: entity
  parent: ToyHammer
  id: BwoinkHammer
  name: "бвоінк молоток"
  description: "Іграшковий молоток, що видає моторошний звук. Він втратив свій старий дух і все ще не придатний для використання."
  suffix: DO NOT MAP
  components:
    - type: Sprite
      sprite: Objects/Fun/bwoink_hammer.rsi
      state: icon
    - type: Item
      size: Small
      sprite: Objects/Fun/bwoink_hammer.rsi
    - type: MeleeWeapon
      soundHit:
        path: /Audio/Admin/adminhelp_old.ogg
        params:
            variation: 0.03
            volume: 3
      soundNoDamage:
        path: /Audio/Admin/adminhelp_old.ogg
        params:
            variation: 0.03
            volume: 3
      damage:
        types:
          Blunt: 0

- type: entity
  parent: BaseItem
  id: WhoopieCushion
  name: "подушка-пустушка"
  description: "Розіграш, пов'язаний з гумором про метеоризм."
  components:
  - type: Sprite
    sprite: Objects/Fun/whoopie.rsi
    state: icon
    quickEquip: false
  - type: Tag
    tags:
      - Payload
  - type: EmitSoundOnUse
    sound:
      collection: Parp
      params:
        variation: 0.125
  - type: UseDelay
    delay: 0.8
  - type: Slippery
    paralyzeTime: 0
    slipSound:
      collection: Parp
      params:
        variation: 0.125
  - type: MeleeWeapon
    soundHit:
      collection: Parp
      params:
        variation: 0.125
    damage:
      types:
        Blunt: 0
  - type: StepTrigger
    intersectRatio: 0.2
    requiredTriggeredSpeed: 1
  - type: TriggerOnStepTrigger
  - type: EmitSoundOnTrigger
    sound:
      collection: Parp
      params:
        variation: 0.125
  - type: Appearance
  - type: CollisionWake
    enabled: false
  - type: Physics
    bodyType: Dynamic
  - type: Fixtures
    fixtures:
      slips:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.2,-0.2,0.2,0.2"
        hard: false
        layer:
        - SlipLayer
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.2,-0.2,0.2,0.2"
        density: 30
        mask:
        - ItemMask

- type: entity
  name: "банан"
  parent: FoodProduceBase
  id: PlasticBanana
  description: "Пластиковий банан."
  suffix: Plastic
  components:
  - type: FlavorProfile
    flavors:
      - plastic
  - type: Food
  - type: Sprite
    sprite: Objects/Specific/Hydroponics/banana.rsi
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Nothing
          Quantity: 100
  - type: Tag
    tags:
    - Banana

- type: entity
  parent: DrinkBase
  id: CrazyGlue
  name: "божевільний клей"
  description: "Пляшка шаленого клею виробництва Honk! Co."
  components:
  - type: Openable
    sound:
      collection: packetOpenSounds
  - type: Sprite
    sprite: Objects/Fun/glue.rsi
    state: icon
    layers:
      - state: icon
        map: ["enum.OpenableVisuals.Layer"]
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.OpenableVisuals.Opened:
        enum.OpenableVisuals.Layer:
          True: {state: "icon_open"}
          False: {state: "icon"}
  - type: Glue
    consumptionUnit: 10
  - type: Item
    sprite: Objects/Fun/glue.rsi
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 100
        reagents:
          - ReagentId: SpaceGlue
            Quantity: 100
  - type: TrashOnSolutionEmpty
    solution: drink
  - type: Tag
    tags:
      - DrinkSpaceGlue

- type: entity
  parent: BaseItem
  id: NewtonCradle
  name: "ньютонівська колиска"
  description: "Пристрій, яким користуються нудьгуючі штовхачі паперу, щоб нагадати собі, що час ще не зупинився. Містить гравітацію."
  components:
  - type: Sprite
    sprite: Objects/Fun/newton_cradle.rsi
    state: icon-off
  - type: Item
    sprite: Objects/Fun/newton_cradle.rsi
    storedRotation: -90
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ToggleVisuals.Toggled:
        enum.ToggleVisuals.Layer:
          True: { state: icon }
          False: { state: icon-off }
  - type: ItemToggle
  - type: UseDelay
    delay: 1
#  - type: ItemToggleActiveSound # DeltaV - No more annoying clicking
#    activeSound:
#      path: /Audio/Items/newton_cradle.ogg
#      params:
#        volume: -9
#        maxDistance: 3

- type: entity
  name: "Розбишака"
  parent: ToyHammer
  id: ThronglerToy
  suffix: Toy
  description: "Навіщо ви це робите?"
  components:
    - type: Sprite
      sprite: Objects/Weapons/Melee/Throngler2.rsi
      state: icon
    - type: StaminaDamageOnHit
      damage: 0.8
    - type: MeleeWeapon
      wideAnimationRotation: -135
      attackRate: .1
      damage:
        types:
          Blunt: 0
      soundHit:
        path: /Audio/Effects/explosion_small1.ogg
      soundNoDamage:
        path: /Audio/Effects/explosion_small1.ogg
    - type: Item
      size: Ginormous
      sprite: Objects/Weapons/Melee/Throngler-in-hand.rsi
    - type: DisarmMalus
      malus: 0

- type: entity
  parent: BasePlushie
  id: PlushieShadowkin
  name: "плюшевий шедовкін"
  description: "Плюшева іграшка Шедовкіна. Вона дуже м'яка."
  components:
    - type: Sprite
      sprite: Objects/Fun/toys.rsi
      state: shadowkin

- type: entity
  parent: BasePlushie
  id: PlushieMort
  name: "плюшевий Морті"
  description: "Плюшева фігурка чарівного Морті. Це пружний, але чутливий тип плюшу."
  components:
    - type: Sprite
      sprite: Objects/Fun/toys.rsi
      state: mortplush

- type: entity
  parent: BasePlushie
  id: PlushieHarpy
  name: "плюшева гарпія"
  description: "М'яка плюшева гарпія! Маленька бірочка на ньому гарантує, що все пір'я отримане з етичним спосбом."
  components:
  - type: Sprite
    sprite: Objects/Fun/toys.rsi
    state: harpyplushie
  - type: StaminaDamageOnHit
    damage: 0.8
  - type: EmitSoundOnActivate
    sound:
      path: /Audio/DeltaV/Voice/Harpy/caw1.ogg
      params:
        variation: 0.05
  - type: EmitSoundOnUse
    sound:
      path: /Audio/DeltaV/Voice/Harpy/caw1.ogg
  - type: EmitSoundOnCollide
    sound:
      path: /Audio/DeltaV/Voice/Harpy/caw1.ogg
      params:
        variation: 0.05
  - type: EmitSoundOnLand
    sound:
      path: /Audio/DeltaV/Voice/Harpy/caw1.ogg
      params:
        variation: 0.05
  - type: UseDelay
    delay: 0.8
  - type: MeleeWeapon
    wideAnimationRotation: -135
    attackRate: 0.5
    damage:
      types:
        Blunt: 0
    soundHit:
      path: /Audio/DeltaV/Voice/Harpy/caw1.ogg
      params:
        variation: 0.05
    soundSwing:
      path: /Audio/DeltaV/Voice/Harpy/caw1.ogg
      params:
        variation: 0.05
    soundNoDamage:
      path: /Audio/DeltaV/Voice/Harpy/caw1.ogg
      params:
        variation: 0.05
