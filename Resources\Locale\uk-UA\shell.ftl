### for technical and/or system messages

## General

shell-server-cannot = Сервер не може цього зробити.
shell-command-success = Команда успішна
shell-invalid-command = Невірна команда.
shell-invalid-command-specific = Невірна команда {$commandName}.
shell-cannot-run-command-from-server = Ви не можете запустити цю команду з сервера.
shell-only-players-can-run-this-command = Тільки гравці можуть робити цю команду.
shell-must-be-attached-to-entity = Щоб виконати цю команду, ви маєте бути приєднані до сутності.

## Аргументи

shell-need-exactly-one-argument = Потрібно рівно 1 аргумент (параметр).
shell-wrong-arguments-number-need-specific = Потрібно {$properAmount} аргументів, надано {$currentAmount}.
shell-argument-must-be-number = Аргумент має бути числом.
shell-argument-must-be-boolean = Аргумент має бути true/false.
shell-wrong-arguments-number = Невірна кількість аргументів.
shell-need-between-arguments = Потрібно від {$lower} до {$upper} аргументів!
shell-need-minimum-arguments = Потрібно щонайменше {$minimum} аргументів!
shell-need-minimum-one-argument = Потрібно хоча б 1 аргумент!

shell-argument-uid = EntityUid

## Охорона

shell-entity-is-not-mob = Цільовий суб'єкт не є натовпом!
shell-invalid-entity-id = Невірний ідентифікатор суб'єкта.
shell-invalid-grid-id = Неправильний ідентифікатор сітки.
shell-invalid-map-id = Невірний ідентифікатор мапи.
shell-invalid-entity-uid = {$uid} не є дійсним uid сутності
shell-invalid-bool = Неправильний булін-вираз.
shell-entity-uid-must-be-number = EntityUid має бути числом.
shell-could-not-find-entity = Не вдалося знайти сутність {$entity}
shell-could-not-find-entity-with-uid = Не вдалося знайти сутність з uid {$uid}
shell-entity-with-uid-lacks-component = Сутність з uid {$uid} не має компонента {$componentName}
shell-invalid-color-hex = Неправильний hex-код кольору!
shell-target-player-does-not-exist = Цільовий гравець не існує!
shell-target-entity-does-not-have-message = Цільова сутність не має {$missing}!
shell-timespan-minutes-must-be-correct = {$span} не є допустимим проміжком часу в хвилинах.
shell-argument-must-be-prototype = Аргумент {$index} має бути {LOC($prototypeName)}!
shell-argument-number-must-be-between = Аргумент {$index} має бути числом між {$lower} та {$upper}!
shell-argument-station-id-invalid = Аргумент {$index} має бути дійсним ідентифікатором станції!
shell-argument-map-id-invalid = Аргумент {$index} повинен бути дійсним ідентифікатором мапи!
shell-argument-number-invalid = Аргумент {$index} повинен бути дійсним числом!

# Підказки
shell-argument-username-hint = <ім'я користувача>

shell-argument-username-optional-hint = [ім'я користувача]
