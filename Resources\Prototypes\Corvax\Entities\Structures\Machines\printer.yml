- type: entity
  parent: BaseLathe
  id: PrinterDoc
  name: "принтер документів"
  description: "Бюрократична досконалість. Зберігає базу даних усіх документів Nanotrasen і дозволяє роздруковувати їх, якщо у вас є папір."
  components:
  - type: Sprite
    sprite: Corvax/Structures/Machines/printer.rsi
    snapCardinals: true
    layers:
    - state: icon
      map: ["enum.LatheVisualLayers.IsRunning"]
    - state: idle
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
    - state: inserting
      map: ["enum.MaterialStorageVisualLayers.Inserting"]
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: Transform
    noRot: false
  - type: Machine
    board: PrinterDocMachineCircuitboard
  - type: Lathe
    producingSound: /Audio/Machines/scanning.ogg
    idleState: icon
    runningState: building
    staticRecipes:
      - PrintedDocumentReportStationRecipe
      - PrintedDocumentReportOnEliminationOfViolationsRecipe
      - PrintedDocumentReportDepartmentRecipe
      - PrintedDocumentReportEmployeePerformanceRecipe
      - PrintedDocumentReportOnTheChaptersMeetingRecipe
      - PrintedDocumentInternalAffairsAgentsReportRecipe
      - PrintedDocumentConditionReportRecipe
      - PrintedDocumentReportStudyObjectRecipe
      - PrintedDocumentExperimentReportRecipe
      - PrintedDocumentDisposalReportRecipe
      - PrintedDocumentApplicationAppointmentInterimRecipe
      - PrintedDocumentApplicationEmploymentRecipe
      - PrintedDocumentLetterResignationRecipe
      - PrintedDocumentApplicationAccessRecipe
      - PrintedDocumentApplicationEquipmentRecipe
      - PrintedDocumentAppealRecipe
      - PrintedDocumentEvacuationShuttleRequestRecipe
      - PrintedDocumentShuttleRegistrationRequestRecipe
      - PrintedDocumentRequestCallMembersCentralCommitteeDSORecipe
      - PrintedDocumentRequestRequestToEstablishThreatLevelRecipe
      - PrintedDocumentRequestChangeSalaryRecipe
      - PrintedDocumentRequestForNonlistedEmploymentRecipe
      - PrintedDocumentRequestForPromotionRecipe
      - PrintedDocumentRequestDocumentsRecipe
      - PrintedDocumentRequestEuthanasiaRecipe
      - PrintedDocumentRequestConstructionWorkRecipe
      - PrintedDocumentRequestModernizationRecipe
      - PrintedDocumentComplaintViolationLaborRulesRecipe
      - PrintedDocumentComplaintOffenseRecipe
      - PrintedDocumentPermissionEquipmentRecipe
      - PrintedDocumentPermissionToTravelInCaseOfThreatRecipe
      - PrintedDocumentSearchPermissionRecipe
      - PrintedDocumentPermissionToCarryWeaponsRecipe
      - PrintedDocumentPrescriptionDrugAuthorizationRecipe
      - PrintedDocumentPermissionDisposeBodyRecipe
      - PrintedDocumentConstructionPermitRecipe
      - PrintedDocumentPermissionToExtendMarriageRecipe
      - PrintedDocumentOrderDismissalRecipe
      - PrintedDocumentOrderDeprivationAccessRecipe
      - PrintedDocumentOrderEncouragementRecipe
      - PrintedDocumentOrderParolePrisonerRecipe
      - PrintedDocumentOrderRecognizingSentienceCreatureRecipe
      - PrintedDocumentOrderMedicalInterventionRecipe
      - PrintedDocumentProductManufacturingOrderRecipe
      - PrintedDocumentOrderPurchaseResourcesEquipmentRecipe
      - PrintedDocumentOrderingSpecialEquipmentRecipe
      - PrintedDocumentOrderPurchaseWeaponsRecipe
      - PrintedDocumentCertificateRecipe
      - PrintedDocumentCertificateAdvancedTrainingRecipe
      - PrintedDocumentCertificateOffenseRecipe
      - PrintedDocumentDeathCertificateRecipe
      - PrintedDocumentMarriageCertificateRecipe
      - PrintedDocumentDivorceCertificateRecipe
      - PrintedDocumentClosingIndictmentRecipe
      - PrintedDocumentSentenceRecipe
      - PrintedDocumentJudgmentRecipe
      - PrintedDocumentStatementHealtheRecipe
      - PrintedDocumentDecisionToStartTrialRecipe
  - type: EmagLatheRecipes
    emagStaticRecipes:
        - PrintedDocumentErrorLoadingFormHeaderRecipe
        - PrintedDocumentNoticeOfLiquidationRecipe
        - PrintedDocumentBusinessDealRecipe
        - PrintedDocumentNoteBeginningMilitaryActionsRecipe
        - PrintedDocumentReportAccomplishmentGoalsRecipe
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.4,0.25,0.4"
        density: 25
        mask:
        - MachineMask
        layer:
        - MachineLayer
  - type: MaterialStorage
    whitelist:
      tags:
      - Document
    storage:
      SheetPrinter: 0
