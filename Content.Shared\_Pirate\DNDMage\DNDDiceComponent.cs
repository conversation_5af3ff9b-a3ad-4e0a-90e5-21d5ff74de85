using Robust.Shared.GameStates;
using Robust.Shared.Serialization;

namespace Content.Shared._Pirate.DNDMage;

/// <summary>
/// Component for DND dice that handles magical effects.
/// Works together with the regular DiceComponent, which handles visual changes,
/// number of sides, and throwing sounds.
/// </summary>
[RegisterComponent, NetworkedComponent, AutoGenerateComponentState]
public sealed partial class DNDDiceComponent : Component
{
    /// <summary>
    /// The magical power of the spell cast with this dice.
    /// </summary>
    [DataField("spellPower"), AutoNetworkedField]
    public float SpellPower = 1.0f;

    /// <summary>
    /// The type of spell this dice is used for.
    /// </summary>
    [DataField("spellType"), AutoNetworkedField]
    public string SpellType = "General";

    /// <summary>
    /// The last roll result. This is stored here for reference, but the actual
    /// visual representation is handled by the DiceComponent.
    /// </summary>
    [<PERSON><PERSON><PERSON>("lastRoll"), AutoNetworkedField]
    public int LastRoll = 0;
}
