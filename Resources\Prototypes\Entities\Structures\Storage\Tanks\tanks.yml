# Welding

- type: entity
  id: WeldingFuelTank
  parent: [StorageTank, StructureWheeled]
  name: "паливний бак"
  description: "Паливний бак. Використовується для зберігання великої кількості пального."
  suffix: Empty
  components:
  - type: StaticPrice
    price: 750
  - type: Sprite
    sprite: Structures/Storage/tanks.rsi
    layers:
      - state: fueltank-2
      - state: fueltank-2-1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 3
    fillBaseName: fueltank-2-
  - type: ExaminableSolution
    solution: tank
  - type: ReagentTank
    tankType: Fuel
  - type: DamageOnToolInteract
    tools: Welding
    weldingDamage:
      types:
        Heat: 10
  - type: PacifismDangerousAttack
  - type: Explosive
    explosionType: Default
    totalIntensity: 120 # ~ 5 tile radius

- type: entity
  id: WeldingFuelTankFull
  parent: WeldingFuelTank
  suffix: Full
  components:
  - type: SolutionContainerManager
    solutions:
      tank:
        reagents:
        - ReagentId: WeldingFuel
          Quantity: 1500

- type: entity
  parent: [StorageTankBig, WeldingFuelTank] # StorageTankBig must come first, or else the desnity won't get inherited.
  id: WeldingFuelTankHighCapacity
  name: "паливний бак великої місткості"
  description: "Паливний бак під високим тиском, призначений для зберігання величезної кількості зварювального палива."
  suffix: Full
  components:
  - type: StaticPrice
    price: 2500
  - type: Sprite
    sprite: Structures/Storage/tanks.rsi
    layers:
      - state: highfueltank-2
      - state: highfueltank-2-1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
  - type: SolutionContainerVisuals
    maxFillLevels: 7
    fillBaseName: highfueltank-2-
  - type: SolutionContainerManager
    solutions:
      tank:
        reagents:
        - ReagentId: WeldingFuel
          Quantity: 5000
        maxVol: 5000
  - type: Explosive
    explosionType: Default
    totalIntensity: 140

# Water

- type: entity
  id: WaterTank
  parent: [StorageTank, StructureWheeled]
  name: "бак для води"
  description: "Резервуар для води. Використовується для зберігання великої кількості води."
  suffix: Empty
  components:
  - type: Tag # goob edit
    tags:
    - WaterTank
  - type: StaticPrice
    price: 500
  - type: Sprite
    sprite: Structures/Storage/tanks.rsi
    layers:
      - state: watertank-2
      - state: watertank-2-1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 5
    fillBaseName: watertank-2-
  - type: ExaminableSolution
    solution: tank

- type: entity
  parent: WaterTank
  id: WaterTankFull
  suffix: Full
  components:
  - type: SolutionContainerManager
    solutions:
      tank:
        reagents:
        - ReagentId: Water
          Quantity: 1500

- type: entity
  parent: StorageTank
  id: WaterCooler
  name: "кулер для води"
  description: "Здається, це гарне місце, щоб постояти і згаяти час. Збоку є запас паперових стаканчиків."
  components:
  - type: Sprite
    sprite: Structures/Storage/tanks.rsi
    layers:
      - state: watercooler-2
      - state: watercooler-2-1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 4
    fillBaseName: watercooler-2-
  - type: SolutionContainerManager
    solutions:
      tank:
        reagents:
        - ReagentId: Water
          Quantity: 500
  - type: ContainerContainer
    containers:
      bin-container: !type:Container
  - type: Bin
    initialContents:
    - DrinkWaterCup
    - DrinkWaterCup
    - DrinkWaterCup
    - DrinkWaterCup
    - DrinkWaterCup
    - DrinkWaterCup
    - DrinkWaterCup
    - DrinkWaterCup
    - DrinkWaterCup
    - DrinkWaterCup
    whitelist:
      requireAll: true
      components:
      - FitsInDispenser
      tags:
      - Trash
  - type: ExaminableSolution
    solution: tank
  - type: StaticPrice
    price: 500

- type: entity
  parent: [StorageTankBig, WaterTank]
  id: WaterTankHighCapacity
  name: "ємність для води великої місткості"
  description: "Резервуар для води під високим тиском, призначений для зберігання гігантської кількості води."
  suffix: Full
  components:
  - type: StaticPrice
    price: 2500
  - type: Sprite
    sprite: Structures/Storage/tanks.rsi
    layers:
      - state: highwatertank-2
      - state: highwatertank-2-1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 7
    fillBaseName: highwatertank-2-
  - type: SolutionContainerManager
    solutions:
      tank:
        reagents:
        - ReagentId: Water
#         This is *100, 000* on /tg/
          Quantity: 10000
        maxVol: 10000
#   It's pressurized...
  - type: ExaminableSolution
    solution: tank
  - type: ReagentTank
    transferAmount: 100

- type: entity
  id: GenericTank
  parent: [StorageTank, StructureWheeled]
  suffix: Empty
  components:
    - type: StaticPrice
      price: 500
    - type: Sprite
      sprite: Structures/Storage/tanks.rsi
      layers:
        - state: generictank-1
        - state: watertank-2-1
          map: ["enum.SolutionContainerLayers.Fill"]
          visible: false
    - type: Appearance
    - type: SolutionContainerVisuals
      maxFillLevels: 5
      fillBaseName: watertank-2-
    - type: ExaminableSolution
      solution: tank
