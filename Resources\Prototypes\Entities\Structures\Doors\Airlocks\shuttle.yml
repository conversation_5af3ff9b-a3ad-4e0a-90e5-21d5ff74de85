- type: entity
  parent: AirlockRCDResistant
  id: AirlockShuttle
  suffix: Docking
  name: "шаттл-шлюз"
  description: "Він відкривається, закривається і, можливо, розчавить вас. Необхідний для з'єднання двох космічних кораблів між собою."
  components:
  - type: Docking
  - type: DockingSignalControl
  - type: DeviceLinkSource
    ports:
      - DoorStatus
      - DockStatus
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.49,-0.49,0.49,0.49" # don't want this colliding with walls or they won't close
        density: 100
        mask:
          - FullTileMask
        layer:
          - AirlockLayer
      docking:
        shape:
          !type:PhysShapeCircle
          radius: 0.2
          position: "0,-0.5"
        hard: false
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Standard/shuttle.rsi
    snapCardinals: false
  - type: Wires
    layoutId: Docking
  - type: Door
    closeTimeTwo: 0.4
    openTimeTwo: 0.4
    crushDamage:
      types:
        Blunt: 15
    openSound:
      path: /Audio/Effects/docking.ogg
    closeSound:
      path: /Audio/Effects/docking.ogg
    denySound:
      path: /Audio/Machines/airlock_deny.ogg
  - type: Airtight
    noAirWhenFullyAirBlocked: false
    airBlockedDirection:
      - South
  - type: Tag
    tags:
      - ForceNoFixRotations
  - type: PaintableAirlock
    group: Shuttle
    department: null
  - type: Construction
    graph: AirlockShuttle
    node: airlock
  - type: StaticPrice
    price: 350

- type: entity
  id: AirlockGlassShuttle
  parent: AirlockShuttle
  name: "шаттл-шлюз"
  suffix: Glass, Docking
  description: "Він відкривається, закривається і, можливо, розчавить вас. Необхідний для з'єднання двох космічних кораблів між собою."
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Glass/shuttle.rsi
  - type: Occluder
    enabled: false
  - type: PaintableAirlock
    group: ShuttleGlass
  - type: Door
    occludes: false
  - type: Fixtures
    fixtures:
      fix1:
        layer:     #removed opaque from the layer, allowing lasers to pass through glass airlocks
        - GlassAirlockLayer

- type: entity
  id: AirlockShuttleAssembly
  parent: AirlockAssembly
  name: "зовнішній шлюз у зборі"
  suffix: Docking
  description: "Незавершена конструкція, необхідна для з'єднання двох космічних апаратів разом."
  components:
  - type: Transform
    anchored: true
    noRot: false
  - type: Rotatable
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Glass/shuttle.rsi
    state: closed
    snapCardinals: false
  - type: Construction
    graph: AirlockShuttle
    node: assembly

- type: entity
  id: AirlockGlassShuttleSyndicate
  parent: AirlockGlassShuttle
  name: "шаттл-шлюз"
  suffix: Glass, Docking
  description: "Він відкривається, закривається і, можливо, розчавить вас. Необхідний для з'єднання двох космічних кораблів між собою."
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Glass/shuttle_syndicate.rsi

- type: entity
  parent: AirlockShuttle
  id: AirlockShuttleSyndicate
  suffix: Docking
  name: "шаттл-шлюз"
  description: "Він відкривається, закривається і, можливо, розчавить вас. Необхідний для з'єднання двох космічних кораблів між собою."
  components:
  - type: Sprite
    sprite: Structures/Doors/Airlocks/Standard/shuttle_syndicate.rsi