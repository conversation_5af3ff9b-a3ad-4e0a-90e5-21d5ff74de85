- type: entity
  parent:
  - BaseMob
  - MobCombat
  - MobDamageable
  id: BaseMobPsionicFamiliar
  abstract: true
  components:
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: bat
      sprite: Mobs/Animals/bat.rsi
  - type: GhostRole
    makeSentient: true
    allowMovement: true
    allowSpeech: true
    name: ghost-role-information-familiar-name
    description: ghost-role-information-familiar-description
    rules: ghost-role-information-familiar-rules
    raffle:
      settings: default
  - type: GhostTakeoverAvailable
  - type: Tag
    tags:
    - DoorBumpOpener
  - type: MobThresholds
    thresholds:
      0: Alive
      50: Dead
  - type: Damageable
    damageContainer: CorporealSpirit
    damageModifierSet: CorporealSpirit
  - type: MindContainer
    showExamineInfo: false
  - type: NpcFactionMember
    factions:
    - PsionicInterloper
  - type: Alerts
  - type: Familiar
  - type: Psionic
    removable: false
    roller: false
    psychognomicDescriptors:
      - p-descriptor-bound
      - p-descriptor-cyclic
  - type: InnatePsionicPowers
    powersToAdd:
      - TelepathyPower
  - type: HTN
    rootTask:
      task: MeleePsionicFamiliarCompound
    blackboard:
      IdleRange: !type:Single
        3.5
      FollowCloseRange: !type:Single
        2.0
      FollowRange: !type:Single
        3.0
  - type: NPCRetaliation
    attackMemoryLength: 10
    retaliateFriendlies: true
  - type: PsionicFamiliar
  - type: Dispellable
  - type: DamageOnDispel
    damage:
      types:
        Heat: 100

- type: entity
  name: "Ремілія"
  parent: BaseMobPsionicFamiliar
  id: MobBatRemilia
  description: "Фамільяр капелана. Любить фрукти."
  components:
  - type: GhostRole
    makeSentient: true
    allowMovement: true
    allowSpeech: true
    name: ghost-role-information-remilia-name
    description: ghost-role-information-remilia-description
    rules: ghost-role-information-remilia-rules
    mindRoles:
    - MindRoleGhostRoleFamiliar
  - type: GhostTakeoverAvailable
  - type: Grammar
    attributes:
      gender: female
      proper: true
  - type: Tag
    tags:
    - DoorBumpOpener
    - VimPilot
  - type: Access
    tags:
    - Chapel
  - type: InnatePsionicPowers
    powersToAdd:
      - TelepathyPower
      - XenoglossyPower
  - type: MovementSpeedModifier
    baseWalkSpeed : 3
    baseSprintSpeed : 6
  - type: Speech
    speechSounds: Squeak
    speechVerb: SmallMob
    allowedEmotes: ['Squeak']
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.25
        density: 0.8
        mask:
        - FlyingMobMask
        layer:
        - FlyingMobLayer
  - type: InteractionPopup
    successChance: 0.2
    interactSuccessString: petting-success-soft-floofy
    interactFailureString: petting-failure-bat
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/fox_squeak.ogg
  - type: MeleeWeapon
    soundHit:
        path: /Audio/Effects/bite.ogg
    angle: 0
    animation: WeaponArcBite
    damage:
      types:
        Piercing: 5
  - type: MobThresholds
    thresholds:
      0: Alive
      30: Dead

- type: entity
  name: "Цербер"
  parent: MobCorgiNarsi
  id: MobCorgiCerberus
  description: "Це цуценя не є милим."
  components:
  - type: GhostRole
    makeSentient: true
    allowMovement: true
    allowSpeech: true
    name: ghost-role-information-cerberus-name
    description: ghost-role-information-cerberus-description
    rules: ghost-role-information-cerberus-rules
    mindRoles:
    - MindRoleGhostRoleFamiliar
    raffle:
      settings: default
  - type: GhostTakeoverAvailable
  - type: MeleeWeapon
    altDisarm: false
    animation: WeaponArcBite
    damage:
      types:
        Piercing: 8
        Slash: 7
  - type: InputMover
  - type: MobMover
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
  - type: NpcFactionMember
    factions:
    - Syndicate
    - PsionicInterloper
  - type: InteractionPopup
    successChance: 0.5
    interactSuccessString: petting-success-corrupted-corgi
    interactFailureString: petting-failure-corrupted-corgi
  - type: MobState
  - type: MobThresholds
    thresholds:
      0: Alive
      120: Dead
  - type: Grammar
    attributes:
      gender: male
      proper: true
  - type: Tag
    tags:
    - DoorBumpOpener
    - VimPilot
  - type: Access
    tags:
    - Chapel
  - type: MindContainer
    showExamineInfo: true
  - type: Familiar
  - type: Dispellable
  - type: Psionic
    removable: false
    roller: false
  - type: InnatePsionicPowers
    powersToAdd:
      - TelepathyPower
  - type: Vocal
    sounds:
      Male: Cerberus
      Female: Cerberus
      Unsexed: Cerberus

- type: entity
  name: "імп-фамільяр"
  parent: BaseMobPsionicFamiliar
  id: MobPsionicFamiliarImp
  description: "Жива крихта полум'я, викликана з Геєни."
  components:
  - type: Sprite
    drawdepth: Mobs
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: imp
      sprite: Mobs/Demons/imp.rsi
  - type: InnatePsionicPowers
    powersToAdd:
      - TelepathyPower
      - PyrokineticFlare
      - XenoglossyPower
  - type: MeleeWeapon
    damage:
      types:
        Heat: 9
    soundHit:
      path: /Audio/Weapons/Guns/Hits/energy_meat1.ogg
      params:
        variation: 0.250
        volume: -10
  - type: PointLight
    radius: 2
    energy: 30
    color: "#ff4500"
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 13
        mask:
        - Opaque
        layer:
        - MobLayer
  - type: RandomMetadata
    nameSegments: [names_golem]
