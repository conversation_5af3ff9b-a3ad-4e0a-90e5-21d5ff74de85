- type: entity
  name: "косм<PERSON>чний короп"
  id: BaseBaseMobCarp # made to allow space goldfish to be picked up on click (interactionpopup overrides that)
  parent: [ SimpleSpaceMobBase, FlyingMobBase, MobCombat ]
  description: "Це космічний короп."
  abstract: true
  components:
    - type: Absorbable
    - type: InputMover
    - type: MobMover
    - type: HTN
      rootTask:
        task: SimpleHostileCompound
      blackboard:
        NavSmash: !type:Bool
          true
    - type: NpcFactionMember
      factions:
      - Dragon
    - type: Sprite
      drawdepth: Mobs
      sprite: Mobs/Aliens/Carps/space.rsi
      layers:
      - map: [ "enum.DamageStateVisualLayers.Base" ]
        state: alive
    - type: Carriable # This one is for you, deltanedas o7
    - type: CombatMode
    - type: Physics
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeCircle
            radius: 0.40
          density: 80
          mask:
            - FlyingMobMask
          layer:
            - FlyingMobLayer
    - type: MobState
    - type: MobThresholds
      thresholds:
        0: Alive
        50: Dead
    - type: Stamina
      critThreshold: 100
    - type: DamageStateVisuals
      states:
        Alive:
          Base: alive
          BaseUnshaded: mouth
        Dead:
          Base: dead
          BaseUnshaded: dead_mouth
    - type: Butcherable
      spawned:
        - id: FoodMeatFish
          amount: 2
    - type: MeleeWeapon
      altDisarm: false
      angle: 0
      animation: WeaponArcBite
      soundHit:
        path: /Audio/Effects/bite.ogg
      damage:
        types:
          Piercing: 5
          Slash: 10
    - type: TypingIndicator
      proto: alien
    - type: Tag
      tags:
        - Carp
        - DoorBumpOpener
        - NoPaint
    - type: ReplacementAccent
      accent: genericAggressive
    - type: Speech
      speechVerb: LargeMob
    - type: Body # Shitmed - Adds carp organs.
      prototype: Carp
    - type: SurgeryTarget
    - type: UserInterface
      interfaces:
        enum.SurgeryUIKey.Key:
          type: SurgeryBui
    - type: NightVision
      isActive: true
      toggleAction: null
      color: "#808080"
      activateSound: null
      deactivateSound: null
    - type: Fauna # Lavaland Change

- type: entity
  parent: BaseBaseMobCarp
  id: BaseMobCarp
  abstract: true
  components:
    - type: Absorbable
    - type: InteractionPopup
      successChance: 0.25 # not very nice
      interactSuccessString: petting-success-carp
      interactFailureString: petting-failure-carp
      interactFailureSound:
        path: /Audio/Effects/bite.ogg

- type: entity
  parent: BaseMobCarp
  id: MobCarp
  components:
  - type: Sprite
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: alive
    - map: [ "enum.DamageStateVisualLayers.BaseUnshaded" ]
      state: mouth
      shader: unshaded
  - type: RandomSprite
    available:
    - enum.DamageStateVisualLayers.Base:
        alive: Rainbow
      enum.DamageStateVisualLayers.BaseUnshaded:
        mouth: ""

- type: entity
  name: "magicarp"
  parent: BaseMobCarp
  id: MobCarpMagic
  description: "Схоже на якусь рибу. Може, чарівна."
  components:
    - type: Absorbable
    - type: Sprite
      sprite: Mobs/Aliens/Carps/magic.rsi
    - type: TypingIndicator
      proto: guardian

- type: entity
  name: "голокарп"
  parent: BaseMobCarp
  id: MobCarpHolo
  description: "Короп з голографічних енергій. На жаль для вас, це цілком реально."
  components:
    - type: Absorbable
    - type: Sprite
      sprite: Mobs/Aliens/Carps/holo.rsi
    - type: Physics
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeCircle
            radius: 0.40
          density: 10
          mask:
            - MobMask
          layer:
            - Opaque
    - type: TypingIndicator
      proto: robot

- type: entity
  parent: MobCarp
  id: MobCarpRainbow
  name: "райдужний короп"
  description: "Ого, яка блискуча рибка!"
  components:
  - type: Absorbable
  - type: PointLight
    radius: 1.5
    energy: 0.5
  - type: RgbLightController
    layers: [ 0 ]

- type: entity
  id: MobCarpSalvage
  parent: MobCarp
  suffix: "Salvage Ruleset"
  components:
    - type: Absorbable
    - type: SalvageMobRestrictions

- type: entity
  name: "космічний короп"
  id: MobCarpDragon
  suffix: DragonBrood
  parent: MobCarp
  components:
    - type: Absorbable
    - type: GhostRole
      allowMovement: true
      allowSpeech: true
      makeSentient: true
      name: ghost-role-information-sentient-carp-name
      description: ghost-role-information-sentient-carp-description
      rules: ghost-role-information-space-dragon-summoned-carp-rules
      mindRoles:
      - MindRoleGhostRoleTeamAntagonist
      raffle:
        settings: short
    - type: GhostTakeoverAvailable
    - type: HTN
      rootTask:
        task: DragonCarpCompound

- type: entity
  id: MobCarpDungeon
  parent: MobCarp
  suffix: Dungeon
  components:
  - type: Absorbable
  - type: MobThresholds
    thresholds:
      0: Alive
      50: Dead
  - type: SlowOnDamage
    speedModifierThresholds:
      25: 0.7
  - type: MeleeWeapon
    damage:
      types:
        Slash: 6

- type: entity
  name: "sharkminnow"
  parent: BaseMobCarp
  id: MobShark
  description: "Небезпечна акула з чорноти нескінченного космосу, яка любить пити кров."
  components:
    - type: Absorbable
    - type: Sprite
      sprite: Mobs/Aliens/Carps/sharkminnow.rsi
      layers:
      - map: [ "enum.DamageStateVisualLayers.Base" ]
        state: alive
      - map: [ "enum.DamageStateVisualLayers.BaseUnshaded" ]
        state: mouth
        shader: unshaded
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeCircle
            radius: 0.40
          density: 100
          mask:
            - FlyingMobMask
          layer:
            - FlyingMobLayer
    - type: MobThresholds
      thresholds:
        0: Alive
        180: Dead
    - type: Stamina
      critThreshold: 150
    - type: DamageStateVisuals
      states:
        Alive:
          Base: alive
          BaseUnshaded: mouth
        Dead:
          Base: dead
          BaseUnshaded: dead_mouth
    - type: Body
      prototype: Bloodsucker
      requiredLegs: 1
    - type: Butcherable
      spawned:
        - id: FoodMeatFish
          amount: 4
    - type: MeleeWeapon
      damage:
        types:
          Slash: 12
          Bloodloss: 5
    - type: SurgeryTarget
    - type: UserInterface
      interfaces:
        enum.SurgeryUIKey.Key:
          type: SurgeryBui

- type: entity
  id: MobSharkSalvage
  parent: MobShark
  suffix: "Salvage Ruleset"
  components:
    - type: Absorbable
    - type: SalvageMobRestrictions
