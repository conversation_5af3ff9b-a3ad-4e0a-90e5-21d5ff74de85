# - type: entity
#   name: <PERSON><PERSON> McGolem
#   id: MobGolemBase
#   save: false
#   abstract: true
#   description: An artificial construct imitating life. This one has no soul installed yet.
#   components:
#   - type: Sprite
#     noRot: true
#     drawdepth: Mobs
#     layers:
#     - map: [ "enum.HumanoidVisualLayers.Chest" ]
#       color: "#e8b59b"
#       sprite: Nyanotrasen/Mobs/Species/Golem/cult.rsi
#       state: cultgolem_chest
#     - map: [ "enum.HumanoidVisualLayers.Head" ]
#       color: "#e8b59b"
#       sprite: Nyanotrasen/Mobs/Species/Golem/cult.rsi
#       state: cultgolem_head
#     - map: [ "enum.ToggleVisuals.Layer" ]
#       sprite: Nyanotrasen/Mobs/Species/Golem/cult.rsi
#       state: lights
#       visible: false
#       shader: unshaded
#     - map: [ "enum.HumanoidVisualLayers.RArm" ]
#       color: "#e8b59b"
#       sprite: Nyanotrasen/Mobs/Species/Golem/cult.rsi
#       state: cultgolem_r_arm
#     - map: [ "enum.HumanoidVisualLayers.LArm" ]
#       color: "#e8b59b"
#       sprite: Nyanotrasen/Mobs/Species/Golem/cult.rsi
#       state: cultgolem_l_arm
#     - map: [ "enum.HumanoidVisualLayers.RLeg" ]
#       color: "#e8b59b"
#       sprite: Nyanotrasen/Mobs/Species/Golem/cult.rsi
#       state: cultgolem_r_leg
#     - map: [ "enum.HumanoidVisualLayers.LLeg" ]
#       color: "#e8b59b"
#       sprite: Nyanotrasen/Mobs/Species/Golem/cult.rsi
#       state: cultgolem_l_leg
#     - map: [ "jumpsuit" ]
#     - map: [ "enum.HumanoidVisualLayers.LHand" ]
#       color: "#e8b59b"
#       sprite: Nyanotrasen/Mobs/Species/Golem/cult.rsi
#       state: cultgolem_l_hand
#     - map: [ "enum.HumanoidVisualLayers.RHand" ]
#       color: "#e8b59b"
#       sprite: Nyanotrasen/Mobs/Species/Golem/cult.rsi
#       state: cultgolem_r_hand
#     - map: [ "enum.HumanoidVisualLayers.Handcuffs" ]
#       color: "#ffffff"
#       sprite: Objects/Misc/handcuffs.rsi
#       state: body-overlay-2
#       visible: false
#     - map: [ "id" ]
#     - map: [ "gloves" ]
#     - map: [ "shoes" ]
#     - map: [ "ears" ]
#     - map: [ "outerClothing" ]
#     - map: [ "eyes" ]
#     - map: [ "belt" ]
#     - map: [ "neck" ]
#     - map: [ "back" ]
#     - map: [ "mask" ]
#     - map: [ "head" ]
#     - map: [ "pocket1" ]
#     - map: [ "pocket2" ]
#     - map: [ "clownedon" ]
#       sprite: "Effects/creampie.rsi"
#       state: "creampie_human"
#       visible: false
#   - type: LagCompensation
#   - type: RangedDamageSound
#     soundGroups:
#       Brute:
#         collection:
#           MeatBulletImpact
#     soundTypes:
#       Heat:
#         collection:
#           MeatLaserImpact
#   - type: Tag
#     tags:
#     - CanPilot
#     - DoorBumpOpener
#     - ShoesRequiredStepTriggerImmune
#     # TODO: https://github.com/Nyanotrasen/Nyanotrasen/issues/1628
#     # - GunsDisabled
#   - type: Reactive
#     groups:
#       Acidic: [Touch]
#     reactions:
#     - reagents: [Water, SpaceCleaner]
#       methods: [Touch]
#       effects:
#       - !type:WashCreamPieReaction
#   - type: Flashable
#   - type: Polymorphable
#   - type: Identity
#   - type: Hands
#   - type: MovementSpeedModifier
#     baseWalkSpeed: 1.8
#     baseSprintSpeed: 3
#   - type: MovedByPressure
#   - type: DamageOnHighSpeedImpact
#     damage:
#       types:
#         Blunt: 5
#     soundHit:
#       path: /Audio/Effects/hit_kick.ogg
#   - type: Stamina
#     excess: 300
#   - type: IdExaminable
#   - type: HealthExaminable
#     examinableTypes:
#       - Blunt
#       - Slash
#       - Piercing
#       - Heat
#       - Shock
#   - type: StatusEffects
#     allowed:
#       - Stun
#       - KnockedDown
#       - SlowedDown
#       - Muted
#       - Pacified
#   - type: Inventory
#     templateId: golem
#   - type: InventorySlots
#   - type: Clickable
#   - type: InteractionOutline
#   - type: Icon
#     sprite: Nyanotrasen/Mobs/Species/Golem/cult.rsi
#     state: full
#   - type: Physics
#     bodyType: KinematicController
#   - type: Fixtures
#     fixtures: # TODO: This needs a second fixture just for mob collisions.
#       fix1:
#         shape:
#           !type:PhysShapeCircle
#           radius: 0.35
#         density: 400
#         restitution: 0.0
#         mask:
#           - MobMask
#         layer:
#           - MobLayer
#   - type: Body
#     prototype: Golem
#   - type: Damageable
#     damageContainer: Inorganic
#     damageModifierSet: Metallic
#   - type: MobState
#   - type: MobThresholds
#     thresholds:
#       0: Alive
#       100: Dead
#   - type: SlowOnDamage
#     speedModifierThresholds:
#       60: 0.7
#       80: 0.5
#   - type: Appearance
#   - type: RotationVisuals
#   - type: CombatMode
#     canDisarm: true
#   - type: Climbing
#   - type: Cuffable
#   - type: Ensnareable
#     sprite: Objects/Misc/ensnare.rsi
#     state: icon
#   - type: AnimationPlayer
#   - type: Buckle
#   - type: MeleeWeapon
#     hidden: true
#     soundHit:
#       collection: Punch
#       params:
#         pitchscale: 0.3
#     angle: 30
#     animation: WeaponArcFist
#     attackRate: 1
#     damage:
#       types:
#         Blunt: 12
#   - type: Pullable
#   - type: DoAfter
#   - type: CreamPied
#   - type: Stripping
#   - type: Strippable
#   - type: UserInterface
#     interfaces:

#       - key: enum.HumanoidMarkingModifierKey.Key
#         type: HumanoidMarkingModifierBoundUserInterface
#       - key: enum.StrippingUiKey.Key
#         type: StrippableBoundUserInterface
#       - key: enum.LawsUiKey.Key
#         type: LawsBoundUserInterface
#       - key: enum.GolemUiKey.Key
#         type: GolemBoundUserInterface
#   - type: Puller
#   - type: Speech
#     speechSounds: Baritone
#   - type: Vocal
#   - type: Emoting
#   - type: Grammar
#     attributes:
#       proper: true
#   - type: StandingState
#   - type: CanEscapeInventory
#   - type: MobPrice
#     price: 6000
#     deathPenalty: 0.1
#   - type: MindContainer
#   - type: Input
#     context: "human"
#   - type: MobMover
#   - type: InputMover
#   - type: Alerts
#   - type: Actions
#   - type: Eye
#   - type: CameraRecoil
#   - type: Examiner
#   - type: CanHostGuardian
#   - type: NpcFactionMember
#     factions:
#     - NanoTrasen
#     - PsionicInterloper
#   - type: ItemSlots
#     slots:
#       crystal_slot:
#         name: SoulCrystal
#         locked: true
#   - type: Golem
#   - type: GenericVisualizer
#     visuals:
#       enum.ToggleVisuals.Toggled:
#         enum.ToggleVisuals.Layer:
#           True: { visible: true }
#           False: { visible: false }
#       enum.CreamPiedVisuals.Creamed:
#         clownedon:
#           True: {visible: true}
#           False: {visible: false}
#   - type: Psionic
#     removable: false
#   - type: Laws
#   - type: IntrinsicUI
#     uis:
#       - key: enum.LawsUiKey.Key
#         toggleAction:
#           name: action-name-show-laws
#           description: action-description-show-laws
#           icon: Structures/Wallmounts/posters.rsi/poster11_legit.png #someone wanna make new icons?
#           iconOn: Structures/Wallmounts/posters.rsi/poster11_legit.png
#           keywords: [ "AI", "console", "interface", "laws", "borg" ]
#           priority: -3
#           event: !type:ToggleIntrinsicUIEvent
#   - type: GuideHelp
#     guides:
#     - AltarsGolemancy

# - type: entity
#   parent: MobGolemBase
#   id: MobGolemCult
#   name: golem
#   suffix: shell
#   components:
#   - type: Repairable
#     fuelcost: 60
#     doAfterDelay: 32
#   - type: Construction
#     graph: Golem
#     node: golem

# - type: entity
#   parent: MobGolemBase
#   id: MobGolemWood
#   name: wood golem
#   description: An artificial construct imitating life. This one has no soul installed yet.
#   components:
#   - type: Sprite
#     noRot: true
#     drawdepth: Mobs
#     layers:
#     - map: [ "enum.HumanoidVisualLayers.Chest" ]
#       color: "#e8b59b"
#       sprite: Mobs/Species/Diona/parts.rsi
#       state: torso_m
#     - map: [ "enum.HumanoidVisualLayers.Head" ]
#       color: "#e8b59b"
#       sprite: Nyanotrasen/Mobs/Species/Golem/wood.rsi
#       state: head
#     - map: [ "enum.ToggleVisuals.Layer" ]
#       sprite: Nyanotrasen/Mobs/Species/Golem/wood.rsi
#       state: lights
#       visible: false
#       shader: unshaded
#     - map: [ "enum.PlantHolderLayers.Plant" ]
#       offset: 0, 0.55
#       scale: 0.5, 0.5
#     - map: [ "enum.HumanoidVisualLayers.RArm" ]
#       color: "#e8b59b"
#       sprite: Mobs/Species/Diona/parts.rsi
#       state: r_arm
#     - map: [ "enum.HumanoidVisualLayers.LArm" ]
#       color: "#e8b59b"
#       sprite: Mobs/Species/Diona/parts.rsi
#       state: l_arm
#     - map: [ "enum.HumanoidVisualLayers.RLeg" ]
#       color: "#e8b59b"
#       sprite: Mobs/Species/Diona/parts.rsi
#       state: r_leg
#     - map: [ "enum.HumanoidVisualLayers.LLeg" ]
#       color: "#e8b59b"
#       sprite: Mobs/Species/Diona/parts.rsi
#       state: l_leg
#     - map: [ "jumpsuit" ]
#     - map: [ "enum.HumanoidVisualLayers.LHand" ]
#       color: "#e8b59b"
#       sprite: Mobs/Species/Diona/parts.rsi
#       state: l_hand
#     - map: [ "enum.HumanoidVisualLayers.RHand" ]
#       color: "#e8b59b"
#       sprite: Mobs/Species/Diona/parts.rsi
#       state: r_hand
#     - map: [ "enum.HumanoidVisualLayers.Handcuffs" ]
#       color: "#ffffff"
#       sprite: Objects/Misc/handcuffs.rsi
#       state: body-overlay-2
#       visible: false
#     - map: [ "id" ]
#     - map: [ "gloves" ]
#     - map: [ "shoes" ]
#     - map: [ "ears" ]
#     - map: [ "outerClothing" ]
#     - map: [ "eyes" ]
#     - map: [ "belt" ]
#     - map: [ "neck" ]
#     - map: [ "back" ]
#     - map: [ "mask" ]
#     - map: [ "head" ]
#     - map: [ "pocket1" ]
#     - map: [ "pocket2" ]
#   - type: Inventory
#     templateId: golem_shoeless
#   - type: Damageable
#     damageContainer: Inorganic
#     damageModifierSet: Wood
#   - type: Flammable
#     fireSpread: true
#     canResistFire: true
#     damage:
#       types:
#         Heat: 1.2 #per second, scales with number of fire 'stacks'
#   - type: PlantHolder
#   - type: PlantHolderVisuals
#   - type: SolutionContainerManager
#     solutions:
#       soil:
#         maxVol: 100
#   - type: RefillableSolution
#     solution: soil
#     maxRefill: 50
#   - type: Repairable #needs plant repair stuff l8er
#     fuelcost: 60
#     doAfterDelay: 32
#   - type: Reactive
#     groups:
#       Acidic: [Touch]
#       Flammable: [Touch]
#       Extinguish: [Touch]
#     reactions:
#     - reagents: [Water, SpaceCleaner]
#       methods: [Touch]
#       effects:
#       - !type:WashCreamPieReaction
#   - type: Construction
#     graph: GolemWood
#     node: golem

# - type: entity
#   parent: MobGolemBase
#   name: silver golem
#   id: MobGolemSilver
#   description: A fusion between soul and quantum machine, the epitome of epistemics.
#   components:
#     - type: MovementSpeedModifier
#       baseWalkSpeed: 4
#       baseSprintSpeed: 5.5
#     - type: RandomSprite
#       available:
#         - enum.DamageStateVisualLayers.Base:
#             silver_base: ""
#           enum.ToggleVisuals.Layer:
#             lights: Sixteen
#     - type: Sprite
#       drawdepth: Mobs
#       sprite: Nyanotrasen/Mobs/Species/Golem/silver.rsi
#       layers:
#         - map: [ "enum.DamageStateVisualLayers.Base" ]
#           state: silver_base
#         - map: [ "enum.HumanoidVisualLayers.Handcuffs" ]
#           color: "#ffffff"
#           sprite: Objects/Misc/handcuffs.rsi
#           state: body-overlay-2
#           visible: false
#         - map: [ "enum.ToggleVisuals.Layer" ]
#           sprite: Nyanotrasen/Mobs/Species/Golem/silver.rsi
#           state: lights
#           color: "#40a7d7"
#           shader: unshaded
#           visible: false
#     - type: Icon
#       sprite: Nyanotrasen/Mobs/Species/Golem/silver.rsi
#       state: full
#     - type: Fixtures
#       fixtures:
#         fix1:
#           shape:
#             !type:PhysShapeCircle
#             radius: 0.35
#           density: 200
#           mask:
#             - FlyingMobMask
#           layer:
#             - FlyingMobLayer
#     - type: HeatResistance
#     - type: Internals
#     - type: TypingIndicator
#       proto: robot
#     - type: MeleeWeapon
#       hidden: true
#       angle: 30
#       animation: WeaponArcFist
#       soundHit:
#         path: /Audio/Nyanotrasen/Mobs/SilverGolem/sonic_jackhammer.ogg
#       attackRate: 1.5
#       damage:
#         types:
#           Blunt: 10
#           Holy: 8
#     - type: Inventory
#       templateId: golem_silver
#     - type: PsychokinesisPower
#     - type: Construction
#       graph: GolemSilver
#       node: golem
#     - type: MovementIgnoreGravity
#     - type: RangedDamageSound
#       soundGroups:
#         Brute:
#           collection:
#             MetalBulletImpact
#       soundTypes:
#         Heat:
#           collection:
#             MetalLaserImpact
