# heats an entity or solution placed on it
- type: entity
  abstract: true
  parent: [ BaseMachinePowered, ConstructibleMachine ]
  id: BaseHeaterMachine
  components:
  - type: Transform
    anchored: true
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.08,-0.35,0.15,0.25"
        mask:
        - TabletopMachineMask
        layer:
        - Impassable
        - MidImpassable
        - LowImpassable
        hard: false
  - type: ApcPowerReceiver
    powerLoad: 300
  - type: Appearance
  - type: ContainerContainer
    containers:
      machine_board: !type:Container
      machine_parts: !type:Container

- type: entity
  parent: BaseHeaterMachine
  id: ChemistryHotplate
  name: "плита"
  description: "Нащадок мікрохвильових печей, наш новітній винахід у технології нагрівання мензурок: конфорка!"
  components:
  - type: Sprite
    sprite: Structures/Machines/hotplate.rsi
    drawdepth: SmallObjects
    snapCardinals: true
    layers:
    - state: icon
    - state: on
      map: ["enum.SolutionHeaterVisuals.IsOn"]
      shader: unshaded
  - type: SolutionHeater
    baseHeatPerSecond: 160
  - type: ItemPlacer
    whitelist:
      components:
      - FitsInDispenser
  - type: PlaceableSurface
    placeCentered: true
    positionOffset: 0, 0.25
  - type: Machine
    board: HotplateMachineCircuitboard
  - type: GenericVisualizer
    visuals:
      enum.SolutionHeaterVisuals.IsOn:
        enum.SolutionHeaterVisuals.IsOn:
          True: { visible: true }
          False: { visible: false }
