- type: entity
  parent: BaseItem
  id: SpaceCash
  name: "спесо"
  description: "Треба мати гроші."
  components:
  - type: Cash
  - type: Item
    shape:
    - 0,0,1,0
    storedOffset: 0,-2
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      Credit: 1
  - type: StaticPrice
    price: 0
  - type: Stack
    stackType: Credit
    count: 1
    baseLayer: base
    layerStates:
    - cash
    - cash_10
    - cash_100
    - cash_500
    - cash_1000
    - cash_5000 # Frontier: larger denominations
    - cash_10000 # Frontier: larger denominations
    - cash_25000 # Frontier: larger denominations
    - cash_50000 # Frontier: larger denominations
    - cash_100000 # Frontier: larger denominations
    - cash_250000 # Frontier: larger denominations (cash_1000000<cash_250000)
    layerFunction: Threshold # Frontier: multicolour cash
  - type: StackLayerThreshold # Frontier
    thresholds: [10, 100, 500, 1000, 5000, 10000, 25000, 50000, 100000, 250000] # Frontier
  - type: Sprite
    sprite: _NF/Objects/Economy/cash.rsi # Frontier: larger denominations
    state: cash
    layers:
    - state: cash
      map: ["base"]
  - type: Physics
    bodyType: Dynamic
  - type: Fixtures
    fixtures:
      fix1:
        density: 30
        shape:
          !type:PhysShapeAabb
          bounds: "-0.35,-0.2,0.25,0.1"
        mask:
        - ItemMask
  - type: Appearance
  - type: CargoSellBlacklist
  # Pirate banking
  - type: Currency
    price:
      SpaceCash: 1
  # Pirate banking end

- type: material
  id: Credit
  name: "спесо"
  unit: materials-unit-bill
  stackEntity: SpaceCash
  icon: { sprite: _NF/Objects/Economy/cash.rsi, state: cash } # Frontier: use Frontier sprite set
  price: 1

- type: stack
  id: Credit
  name: "спесо"
  icon: { sprite: _NF/Objects/Economy/cash.rsi, state: cash } # Frontier: use Frontier sprite set
  spawn: SpaceCash

- type: entity
  parent: SpaceCash
  id: SpaceCash10
  suffix: 10
  components:
  - type: Icon
    sprite: _NF/Objects/Economy/cash.rsi # Frontier: use Frontier sprite set
    state: cash_10
  - type: Stack
    count: 10

- type: entity
  parent: SpaceCash
  id: SpaceCash100
  suffix: 100
  components:
  - type: Icon
    sprite: _NF/Objects/Economy/cash.rsi # Frontier: use Frontier sprite set
    state: cash_100
  - type: Stack
    count: 100

- type: entity
  parent: SpaceCash
  id: SpaceCash500
  suffix: 500
  components:
  - type: Icon
    sprite: _NF/Objects/Economy/cash.rsi # Frontier: use Frontier sprite set
    state: cash_500
  - type: Stack
    count: 500

- type: entity
  parent: SpaceCash
  id: SpaceCash1000
  suffix: 1000
  components:
  - type: Icon
    sprite: _NF/Objects/Economy/cash.rsi # Frontier: use Frontier sprite set
    state: cash_1000
  - type: Stack
    count: 1000

- type: entity
  parent: SpaceCash
  id: SpaceCash2500
  suffix: 2500
  components:
  - type: Icon
    sprite: _NF/Objects/Economy/cash.rsi # Frontier: use Frontier sprite set
    state: cash_1000
  - type: Stack
    count: 2500

- type: entity
  parent: SpaceCash
  id: SpaceCash5000
  suffix: 5000
  components:
  - type: Icon
    sprite: _NF/Objects/Economy/cash.rsi # Frontier: use Frontier sprite set
    state: cash_5000 # Frontier: cash_1000<cash_5000
  - type: Stack
    count: 5000

- type: entity
  parent: SpaceCash
  id: SpaceCash10000
  suffix: 10000
  components:
  - type: Icon
    sprite: _NF/Objects/Economy/cash.rsi # Frontier: use Frontier sprite set
    state: cash_10000 # Frontier: cash_1000<cash_10000
  - type: Stack
    count: 10000

- type: entity
  parent: SpaceCash
  id: SpaceCash20000
  suffix: 20000
  components:
  - type: Icon
    sprite: _NF/Objects/Economy/cash.rsi # Frontier: use Frontier sprite set
    state: cash_10000 # Frontier: cash_1000<cash_10000
  - type: Stack
    count: 20000

- type: entity
  parent: SpaceCash
  id: SpaceCash30000
  suffix: 30000
  components:
  - type: Icon
    sprite: _NF/Objects/Economy/cash.rsi # Frontier: use Frontier sprite set
    state: cash_25000 # Frontier: cash_1000<cash_25000
  - type: Stack
    count: 30000

- type: entity
  parent: SpaceCash
  id: SpaceCash50000
  suffix: 50000
  components:
  - type: Icon
    sprite: _NF/Objects/Economy/cash.rsi # Frontier: use Frontier sprite set
    state: cash_50000 # Frontier: cash_1000<cash_50000
  - type: Stack
    count: 50000

- type: entity
  parent: SpaceCash
  id: SpaceCash100000
  suffix: 100000
  components:
  - type: Icon
    sprite: _NF/Objects/Economy/cash.rsi # Frontier: use Frontier sprite set
    state: cash_100000 # Frontier: cash_1000<cash_100000
  - type: Stack
    count: 100000

- type: entity
  parent: SpaceCash
  id: SpaceCash1000000
  suffix: 1000000
  components:
  - type: Icon
    sprite: _NF/Objects/Economy/cash.rsi # Frontier: use Frontier sprite set
    state: cash_250000 # Frontier: cash_1000000<cash_250000
  - type: Stack
    count: 1000000
