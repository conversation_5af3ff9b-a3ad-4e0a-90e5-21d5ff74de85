using Content.Shared._Pirate.DNDMage;
using Content.Shared.Mobs.Components;
using Content.Shared.Mobs.Systems;
using Robust.Shared.Timing;

namespace Content.Server._Pirate.DNDMage;

/// <summary>
/// Server-side system for handling DNDMageComponent.
/// </summary>
public sealed class DNDMageSystem : EntitySystem
{
    [Dependency] private readonly IGameTiming _gameTiming = default!;
    [Dependency] private readonly MobStateSystem _mobStateSystem = default!;

    private readonly Dictionary<EntityUid, TimeSpan> _lastManaRegenTime = new();

    public override void Initialize()
    {
        base.Initialize();

        SubscribeLocalEvent<DNDMageComponent, ComponentInit>(OnComponentInit);
        SubscribeLocalEvent<DNDMageComponent, ComponentRemove>(OnComponentRemove);
    }

    public override void Update(float frameTime)
    {
        base.Update(frameTime);

        // Регенерація магічної сили для всіх магів
        var curTime = _gameTiming.CurTime;

        var query = EntityQueryEnumerator<DNDMageComponent>();
        while (query.MoveNext(out var uid, out var comp))
        {
            // Пропускаємо мертвих магів
            if (TryComp<MobStateComponent>(uid, out var mobState) &&
                _mobStateSystem.IsDead(uid, mobState))
                continue;

            // Перевіряємо, чи пройшов час для регенерації
            if (!_lastManaRegenTime.TryGetValue(uid, out var lastRegen))
            {
                _lastManaRegenTime[uid] = curTime;
                continue;
            }

            // Регенеруємо ману кожну секунду
            if (curTime - lastRegen < TimeSpan.FromSeconds(1))
                continue;

            // Оновлюємо час останньої регенерації
            _lastManaRegenTime[uid] = curTime;

            // Регенеруємо ману
            if (comp.MagicPower < comp.MaxMagicPower)
            {
                comp.MagicPower = Math.Min(comp.MaxMagicPower, comp.MagicPower + (int)Math.Ceiling(comp.MagicRegenRate));
                Dirty(uid, comp);
            }
        }
    }

    private void OnComponentInit(EntityUid uid, DNDMageComponent component, ComponentInit args)
    {
        _lastManaRegenTime[uid] = _gameTiming.CurTime;
    }

    private void OnComponentRemove(EntityUid uid, DNDMageComponent component, ComponentRemove args)
    {
        _lastManaRegenTime.Remove(uid);
    }

    /// <summary>
    /// Спроба використати магічну силу
    /// </summary>
    /// <returns>True, якщо вдалося використати магічну силу</returns>
    public bool TryUseMagicPower(EntityUid uid, int amount, DNDMageComponent? component = null)
    {
        if (!Resolve(uid, ref component))
            return false;

        if (component.MagicPower < amount)
            return false;

        component.MagicPower -= amount;
        Dirty(uid, component);
        return true;
    }

    /// <summary>
    /// Додати магічну силу
    /// </summary>
    public void AddMagicPower(EntityUid uid, int amount, DNDMageComponent? component = null)
    {
        if (!Resolve(uid, ref component))
            return;

        component.MagicPower = Math.Min(component.MaxMagicPower, component.MagicPower + amount);
        Dirty(uid, component);
    }
}
