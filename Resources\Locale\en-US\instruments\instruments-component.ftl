# InstrumentComponent
instrument-component-finger-cramps-light-message = Your fingers are beginning to a cramp a little!
instrument-component-finger-cramps-serious-message = Your fingers are seriously cramping up!
instrument-component-finger-cramps-max-message = Your fingers cramp up from playing!
instruments-component-menu-no-midi-support = MIDI support is currently not
                                             available on your system.
                                             If on Linux, you may need to install
                                             FluidSynth or a development package
                                             for FluidSynth.
instruments-component-menu-input-button = MIDI Input
instruments-component-menu-band-button = Join Band
instruments-component-menu-play-button = Play MIDI
instruments-component-menu-loop-button = Loop
instruments-component-menu-channels-button = Channels
instruments-component-menu-stop-button = Stop
instruments-component-band-menu = Choose band leader
instrument-component-band-refresh = Refresh
instruments-component-channels-menu = MIDI Channel Selection
instrument-component-channel-name = MIDI Channel {$number}
instruments-component-channels-all-button = All
instruments-component-channels-clear-button = Clear

# SwappableInstrumentComponent
swappable-instrument-component-style-set = Style set to "{$style}"
