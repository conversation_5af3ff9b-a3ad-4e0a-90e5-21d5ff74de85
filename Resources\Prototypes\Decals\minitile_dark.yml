﻿- type: decal
  id: MiniTileDark
  parent: MiniTile
  abstract: true

- type: decal
  id: MiniTileDarkBox
  parent: MiniTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: dark_box

- type: decal
  id: MiniTileDarkCornerNe
  parent: MiniTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: dark_corner_ne

- type: decal
  id: MiniTileDarkCornerSe
  parent: MiniTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: dark_corner_se

- type: decal
  id: MiniTileDarkCornerNw
  parent: MiniTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: dark_corner_nw

- type: decal
  id: MiniTileDarkCornerSw
  parent: MiniTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: dark_corner_sw

- type: decal
  id: MiniTileDarkInnerNe
  parent: MiniTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: dark_inner_ne

- type: decal
  id: MiniTileDarkInnerSe
  parent: MiniTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: dark_inner_se

- type: decal
  id: MiniTileDarkInnerNw
  parent: MiniTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: dark_inner_nw

- type: decal
  id: MiniTileDarkInnerSw
  parent: MiniTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: dark_inner_sw

- type: decal
  id: MiniTileDarkEndN
  parent: MiniTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: dark_end_n

- type: decal
  id: MiniTileDarkEndE
  parent: MiniTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: dark_end_e

- type: decal
  id: MiniTileDarkEndS
  parent: MiniTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: dark_end_s

- type: decal
  id: MiniTileDarkEndW
  parent: MiniTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: dark_end_w

- type: decal
  id: MiniTileDarkLineN
  parent: MiniTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: dark_line_n

- type: decal
  id: MiniTileDarkLineE
  parent: MiniTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: dark_line_e

- type: decal
  id: MiniTileDarkLineS
  parent: MiniTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: dark_line_s

- type: decal
  id: MiniTileDarkLineW
  parent: MiniTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: dark_line_w

