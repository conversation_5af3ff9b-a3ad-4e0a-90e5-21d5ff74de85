using System.Linq;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using static Content.Client.Stylesheets.StyleBase;

namespace Content.Client.UserInterface.Controls;

/// Automatically styles a group of HORIZONTAL buttons based on visibility, count, and position
[GenerateTypedNameReferences]
public sealed partial class StyledButtonGroup : BoxContainer
{
    public StyledButtonGroup()
    {
        RobustXamlLoader.Load(this);

        OnChildAdded += _ => UpdateStyles();
        OnChildMoved += _ => UpdateStyles();
        OnChildRemoved += _ => UpdateStyles();
    }

    public void UpdateStyles()
    {
        var children = Children.Where(c => c.Visible && c is Button).ToArray();
        for (var i = 0; i < children.Length; i++)
        {
            var child = children[i];
            var button = (child as Button)!;

            button.RemoveStyleClass(ButtonOpenRight);
            button.RemoveStyleClass(ButtonOpenLeft);
            button.RemoveStyleClass(ButtonOpenBoth);

            if (i == 0)
                button.AddStyleClass(children.Length == 1 ? "" : ButtonOpenRight);
            else if (i == children.Length - 1)
                button.AddStyleClass(ButtonOpenLeft);
            else
                button.AddStyleClass(ButtonOpenBoth);
        }
    }
}
