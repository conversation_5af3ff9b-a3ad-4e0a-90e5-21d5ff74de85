- type: entity
  name: "вогнегасник"
  parent: BaseItem
  id: FireExtinguisher
  description: "Він гасить пожежі."
  components:
  - type: Sprite
    sprite: Objects/Misc/fire_extinguisher.rsi
    layers:
        - state: fire_extinguisher_closed
          map: [ "enabled" ]
  - type: Item
    sprite: Objects/Misc/fire_extinguisher.rsi
    size: Normal
  - type: SolutionContainerManager
    solutions:
      spray:
        maxVol: 100
        reagents:
        - ReagentId: Water
          Quantity: 100
  - type: RefillableSolution
    solution: spray
  - type: DrainableSolution
    solution: spray
  - type: SolutionTransfer
  - type: UseDelay
  - type: Spray
    transferAmount: 10
    pushbackAmount: 60
    spraySound:
      path: /Audio/Effects/extinguish.ogg
    sprayedPrototype: ExtinguisherSpray
    vaporAmount: 3
    vaporSpread: 90
    sprayVelocity: 2.0
  - type: FireExtinguisher
    hasSafety: true
  - type: MeleeWeapon
    wideAnimationRotation: 180
    attackRate: 1.25
    bluntStaminaDamageFactor: 2.5
    range: 1.75
    damage:
      types:
        Blunt: 8
    heavyRateModifier: 1.5
    heavyDamageBaseModifier: 2
    heavyStaminaCost: 5
    maxTargets: 6
    soundHit:
      path: /Audio/Weapons/smash.ogg
  - type: DamageOtherOnHit
    staminaCost: 9
  - type: Tool
    qualities:
    - Rolling
    speedModifier: 0.5 # its very big, akward to use
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.FireExtinguisherVisuals.Safety:
        enabled:
          True: { state: fire_extinguisher_closed }
          False: { state: fire_extinguisher_open }
  - type: PhysicalComposition
    materialComposition:
      Steel: 100

- type: entity
  name: "вогнегасник спрей"
  id: ExtinguisherSpray
  parent: Vapor
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Effects/extinguisherSpray.rsi
    layers:
      - state: extinguish
        map: [ "enum.VaporVisualLayers.Base" ]
  - type: Physics
    bodyType: Dynamic
  - type: Appearance
  - type: VaporVisuals
    animationTime: 0.8
    animationState: extinguish
