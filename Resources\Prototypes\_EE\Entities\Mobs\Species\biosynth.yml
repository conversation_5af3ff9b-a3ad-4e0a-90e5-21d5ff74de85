- type: entity
  parent: MobHuman
  id: MobSynthHuman
  name: "біосинтетичний тулуб"
  description: "Налаштовуване синтетичне людське тіло."
  suffix: Alive
  components:
  - type: HumanoidAppearance
    species: SynthHuman
  - type: Body
    prototype: SynthHuman
    requiredLegs: 2
  - type: Renamable
    singleUse: true
  - type: ChangeableGender
    singleUse: true
  - type: ChangeableSex
    singleUse: true
  - type: UserInterface
    interfaces:
      enum.HumanoidMarkingModifierKey.Key:
        type: HumanoidMarkingModifierBoundUserInterface
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
      # Goobstation - changelings
      enum.StoreUiKey.Key:
        type: StoreBoundUserInterface
      enum.InstrumentUiKey.Key:
        type: InstrumentBoundUserInterface
        requireInputValidation: false
      enum.RadialSelectorUiKey.Key:
        type: RadialSelectorMenuBUI
      enum.ListViewSelectorUiK<PERSON>.Key:
        type: ListViewSelectorBUI
      enum.SurgeryUIKey.Key:
        type: SurgeryBui
      enum.SharedRenamableInterfaceKey.Key:
        type: RenamableBoundUserInterface
  - type: NameIdentifier
    group: Synth
  - type: Damageable
    damage:
      types:
        Bloodloss: -100 # they spawn with 100 bloodloss for whatever reason
  - type: Debrained
