#EVA Helmet
- type: entity
  parent: ClothingHeadEVAHelmetBase
  id: ClothingHeadHelmetEVA
  name: "шолом EVA"
  description: "Старий, але золотий шолом, призначений для позашляхової діяльності. Сумнозвісний тим, що робить офіцерів служби безпеки параноїдальними."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Helmets/eva.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/eva.rsi
  - type: Tag
    tags:
    - HelmetEVA
    - WhitelistChameleon

#Large EVA Helmet
- type: entity
  parent: ClothingHeadEVAHelmetBase
  id: ClothingHeadHelmetEVALarge
  name: "шолом EVA"
  description: "Старий, але золотий шолом, призначений для позашляхової активності."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Helmets/eva_large.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/eva_large.rsi

#Syndicate EVA Helmet
- type: entity
  parent: ClothingHeadEVAHelmetBase
  id: ClothingHeadHelmetSyndicate
  name: "синдикатний шолом EVA"
  description: "Простий, стильний шолом з EVA. Створений для максимально скромного космічного нахабства."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Helmets/eva_syndicate.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/eva_syndicate.rsi

#Cosmonaut Helmet
- type: entity
  parent: ClothingHeadEVAHelmetBase
  id: ClothingHeadHelmetCosmonaut
  name: "шолом космонавта"
  description: "Старовинний дизайн, але сучасне виробництво." #Description here originally started with " A deceptively well armored space helmet." Potentially had armor values in SS13 that weren't brought over?
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Helmets/cosmonaut.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/cosmonaut.rsi

#Ancient Void Helmet
- type: entity
  parent: ClothingHeadEVAHelmetBase
  id: ClothingHeadHelmetAncient
  name: "NTSRA шолом порожнечі"
  description: "Старовинний космічний шолом, розроблений філією NTSRA компанії CentCom."
  components:
  - type: BreathMask
  - type: Sprite
    sprite: Clothing/Head/Helmets/ancientvoidsuit.rsi
  - type: Clothing
    sprite: Clothing/Head/Helmets/ancientvoidsuit.rsi
