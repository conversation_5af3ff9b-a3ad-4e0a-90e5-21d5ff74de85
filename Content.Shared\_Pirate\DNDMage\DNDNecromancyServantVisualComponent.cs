using Robust.Shared.GameStates;

namespace Content.Shared._Pirate.DNDMage;

/// <summary>
/// Компонент для візуальних ефектів слуг некроманта
/// </summary>
[RegisterComponent, NetworkedComponent, AutoGenerateComponentState]
public sealed partial class DNDNecromancyServantVisualComponent : Component
{
    /// <summary>
    /// Чи показувати темну ауру навколо слуги
    /// </summary>
    [DataField, AutoNetworkedField]
    public bool ShowDarkAura = true;

    /// <summary>
    /// Колір світіння очей слуги
    /// </summary>
    [DataField, AutoNetworkedField]
    public Color EyeGlowColor = Color.Red;

    /// <summary>
    /// Інтенсивність світіння очей (0-1)
    /// </summary>
    [DataField, AutoNetworkedField]
    public float EyeGlowIntensity = 0.8f;

    /// <summary>
    /// Колір темної аури
    /// </summary>
    [DataField, AutoNetworkedField]
    public Color AuraColor = Color.Purple;

    /// <summary>
    /// Інтенсивність темної аури (0-1)
    /// </summary>
    [DataField, AutoNetworkedField]
    public float AuraIntensity = 0.6f;

    /// <summary>
    /// Радіус темної аури
    /// </summary>
    [DataField, AutoNetworkedField]
    public float AuraRadius = 1.5f;

    /// <summary>
    /// Чи показувати частинки некромантії
    /// </summary>
    [DataField, AutoNetworkedField]
    public bool ShowNecromancyParticles = true;

    /// <summary>
    /// Тип візуального ефекту залежно від типу слуги
    /// </summary>
    [DataField, AutoNetworkedField]
    public ServantVisualType VisualType = ServantVisualType.Basic;
}

/// <summary>
/// Типи візуальних ефектів для слуг
/// </summary>
public enum ServantVisualType
{
    Basic,      // Базові ефекти
    Warrior,    // Червоне світіння, агресивна аура
    Guardian,   // Синє світіння, захисна аура
    Scout,      // Зелене світіння, швидкі частинки
    Mage        // Фіолетове світіння, магічні ефекти
}
