psionics-records-console-window-title = Psionics Registry Records
psionics-records-console-records-list-title = Crewmembers
psionics-records-console-select-record-info = Select a record.
psionics-records-console-no-records = No records found!
psionics-records-console-no-record-found = No record was found for the selected person.

## Status

psionics-records-console-status = Status
psionics-records-status-none = None
psionics-records-status-registered = Registered Psionic
psionics-records-status-suspected = Suspected Psionics
psionics-records-status-abusing = Abusing Psionics

psionics-records-console-wanted-reason = [color=gray]Psionics Listed[/color]
psionics-records-console-suspected-reason = [color=gray]Suspected Reason[/color]
psionics-records-console-reason = Psionics/Reason
psionics-records-console-reason-placeholder = For example: {$placeholder}

psionics-records-permission-denied = Permission denied

## Security channel notifications

psionics-records-console-registered = {$name} is registered as psionic by {$officer}: {$reason}.
psionics-records-console-suspected = {$officer} marked {$name} as a possible psionic because of: {$reason}.
psionics-records-console-not-suspected = {$name} is no longer a suspected psionic.
psionics-records-console-not-registered = {$name} is no longer registered as psionic.
psionics-records-console-abusing = {$officer} marked {$name} as abusing psionics because of: {$reason}.
psionics-records-console-not-abusing = {$name} is no longer marked as abusing psionics.
psionics-records-console-unknown-officer = <unknown officer>

## Filters

psionics-records-filter-placeholder = Input text and press "Enter"
psionics-records-name-filter = Name
psionics-records-prints-filter = Fingerprints
psionics-records-dna-filter = DNA
