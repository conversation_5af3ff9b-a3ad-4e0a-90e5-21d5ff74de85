- type: entity
  id: KitchenDeepFryer
  parent: [ BaseMachinePowered, ConstructibleMachine ]
  name: "фритюрниця"
  description: "Промислова фритюрниця. Великий хіт на державних ярмарках!"
  components:
  - type: Transform
    noRot: false
  - type: Sprite
    netsync: false
    sprite: Nyanotrasen/Structures/Machines/deep_fryer.rsi
    layers:
      - state: off-0
      - map: ["enum.SolutionContainerLayers.Fill"]
        state: off-1
  - type: AmbientSound
    volume: -4
    range: 5
    enabled: false
    sound:
      path: /Audio/Nyanotrasen/Ambience/Objects/deepfryer_sizzling.ogg
  - type: SolutionContainerVisuals
    maxFillLevels: 8
    fillBaseName: off-
  - type: Anchorable
  - type: Pullable
  - type: Rotatable
    rotateWhilePulling: false
  - type: Physics
    bodyType: Static
  - type: Climbable
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.30,-0.25,0.30,0.45"
        density: 600
        mask:
        - MachineMask
        layer:
        - MachineLayer
  - type: DeepFryer
    blacklist:
      components:
      # The classic.
      - NukeDisk
      # SliceableFood is handled easily enough, but there's not much to be
      # gained by doing special handling for Stacks, especially since most
      # of the items that use Stack aren't even remotely edible.
      - Stack
    whitelist:
      components:
      # It's what meat is.
      - BodyPart
      # Some clothes, shoes, uniforms.
      - Butcherable
      # It's already food.
      - Food
      - Seed
      # A good source of fiber.
      - Paper
      # May as well let actual garbage get turned into food.
      - SpaceGarbage
      tags:
      - Recyclable
      - Trash
      - Document
    charredPrototype: FoodBadRecipe
    goodReagents:
      - ReagentId: Omnizine
        Quantity: 1
    badReagents:
      - ReagentId: Toxin
        Quantity: 2
    wasteReagents:
      - ReagentId: Charcoal
        Quantity: 0.5
      - ReagentId: Ash
        Quantity: 0.5
    # What food items are actually going to be improved by deep-frying?
    # This is based on flavor profiles.
    goodFlavors:
      - bread
      - bun
      - donk
      - dough
      - fishy
      - meaty
      - pasta
      - potatoes
      - tofu
      - onion
      - mushroom
    badFlavors:
      - acid
      - chocolate
      - gunpowder
      - minty
      - raisins
      - tea
      - terrible
    solution: vat_oil
    fryingOils:
      - Cornoil
      - OilGhee
      - OilOlive
    #unsafeOilVolumeEffects: #Can't pass UninitializedSaveTest. Modifies itself on spawn. Very weird.
    #- !type:AreaReactionEffect
    #  duration: 10
    #  prototypeId: Smoke
    #  sound:
    #    path: /Audio/Effects/smoke.ogg
  - type: SolutionContainerManager
    solutions:
      vat_oil:
        maxVol: 100
  - type: DrainableSolution
    solution: vat_oil
  - type: ExaminableSolution
    solution: vat_oil
  - type: InjectableSolution
    solution: vat_oil
  - type: RefillableSolution
    solution: vat_oil
  - type: Drink
    solution: vat_oil
  - type: Appearance
  - type: ActivatableUI
    key: enum.DeepFryerUiKey.Key
  - type: UserInterface
    interfaces:
      enum.DeepFryerUiKey.Key:
        type: DeepFryerBoundUserInterface
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:SpillBehavior
        solution: vat_oil
      - !type:SpawnEntitiesBehavior
        spawn:
          MachineFrameDestroyed:
            min: 1
            max: 1
  - type: ApcPowerReceiver
    powerLoad: 300
  - type: Machine
    board: DeepFryerMachineCircuitboard
  - type: EmptyOnMachineDeconstruct
    containers:
    - vat_entities
  - type: ContainerContainer
    containers:
      vat_entities: !type:Container
      machine_board: !type:Container
      machine_parts: !type:Container
  - type: PowerSwitch
