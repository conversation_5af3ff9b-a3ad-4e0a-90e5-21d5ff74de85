- type: entity
  parent: [ SimpleMobBase, MobCombat ]
  id: BaseMobFlesh
  name: "химерна плоть"
  description: "Безладна маса плоті, оживлена аномальною енергією. Ніяк не виглядає на збоченця!"
  abstract: true
  components:
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
    blackboard:
      NavClimb: !type:Bool
        true
      NavSmash: !type:Bool
        true
  - type: NpcFactionMember
    factions:
    - SimpleHostile
  - type: Tag
    tags:
    - DoorBumpOpener
    - Flesh
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/flesh.rsi
  - type: MovementAlwaysTouching
  - type: MovementSpeedModifier
    baseWalkSpeed: 1
    baseSprintSpeed: 1.5
  - type: MobState
    allowedStates:
    - Alive
    - Dead
  - type: MobThresholds
    thresholds:
      0: Alive
      75: Dead
  - type: Stamina
    critThreshold: 50
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 1
  - type: Bloodstream
    bloodMaxVolume: 100
  - type: CombatMode
  - type: MeleeWeapon
    soundHit:
      path: /Audio/Weapons/Xeno/alien_claw_flesh3.ogg
    angle: 0
    animation: WeaponArcClaw
    damage:
      types:
        Slash: 6
  - type: ReplacementAccent
    accent: genericAggressive
  - type: Psionic
    removable: false
    roller: false
    assayFeedback:
      - flesh-golem-feedback
  - type: Fauna # Lavaland Change

- type: entity
  parent: BaseMobFlesh
  id: MobFleshJared
  components:
  - type: Sprite
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: jared
  - type: DamageStateVisuals
    states:
      Alive:
        Base: jared
      Critical:
        Base: dead
      Dead:
        Base: dead
  - type: MeleeWeapon
    soundHit:
        path: /Audio/Weapons/Xeno/alien_claw_flesh3.ogg
    angle: 0
    animation: WeaponArcClaw
    damage:
      types:
        Slash: 8

- type: entity
  parent: BaseMobFlesh
  id: MobFleshGolem
  components:
  - type: Sprite
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: golem
  - type: DamageStateVisuals
    states:
      Alive:
        Base: golem
      Critical:
        Base: dead
      Dead:
        Base: dead
  - type: MobThresholds
    thresholds:
      0: Alive
      50: Dead
  - type: MeleeWeapon
    soundHit:
        path: /Audio/Weapons/Xeno/alien_claw_flesh3.ogg
    angle: 0
    animation: WeaponArcClaw
    damage:
      types:
        Slash: 8

- type: entity
  parent: BaseMobFlesh
  id: MobFleshClamp
  components:
  - type: Sprite
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: clamp
  - type: DamageStateVisuals
    states:
      Alive:
        Base: clamp
      Critical:
        Base: dead
      Dead:
        Base: dead
  - type: MobThresholds
    thresholds:
      0: Alive
      30: Dead
  - type: MovementSpeedModifier
    baseWalkSpeed: 2
    baseSprintSpeed: 2.5

- type: entity
  parent: BaseMobFlesh
  id: MobFleshLover
  components:
  - type: Sprite
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: lover
  - type: DamageStateVisuals
    states:
      Alive:
        Base: lover
      Critical:
        Base: dead
      Dead:
        Base: dead
  - type: MobThresholds
    thresholds:
      0: Alive
      30: Dead
  - type: MovementSpeedModifier
    baseWalkSpeed: 2
    baseSprintSpeed: 2.5

- type: entity
  parent: BaseMobFlesh
  id: MobAbomination
  name: "гидота"
  description: "Відкинутий клон, який постійно відчуває біль і прагне помсти."
  components:
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Demons/abomination.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: abomination
  - type: DamageStateVisuals
    states:
      Alive:
        Base: abomination
      Dead:
        Base: dead
  - type: MobState
    allowedStates:
    - Alive
    - Dead
  - type: MobThresholds
    thresholds:
      0: Alive
      100: Dead
  - type: MovementSpeedModifier
    baseWalkSpeed: 1.5
    baseSprintSpeed: 2.5
  - type: MeleeWeapon
    soundHit:
        path: /Audio/Weapons/Xeno/alien_claw_flesh3.ogg
    angle: 0
    animation: WeaponArcClaw
    damage:
      types:
        Blunt: 10

- type: entity
  parent: [ SimpleSpaceMobBase, MobCombat ]
  id: BaseMobFleshSalvage #This one is immune to space!
  name: "химерна плоть"
  suffix: "Salvage Ruleset"
  description: "Безладна маса плоті, оживлена аномальною енергією. Ніяк не виглядає на збоченця!"
  abstract: true
  components:
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
    blackboard:
      NavClimb: !type:Bool
        true
      NavSmash: !type:Bool
        true
  - type: NpcFactionMember
    factions:
    - SimpleHostile
  - type: Tag
    tags:
    - DoorBumpOpener
    - Flesh
  - type: Sprite
    drawdepth: Mobs
    sprite: Mobs/Aliens/flesh.rsi
  - type: MovementAlwaysTouching
  - type: MovementSpeedModifier
    baseWalkSpeed: 1
    baseSprintSpeed: 1.5
  - type: MobState
    allowedStates:
    - Alive
    - Dead
  - type: MobThresholds
    thresholds:
      0: Alive
      75: Dead
  - type: Stamina
    critThreshold: 50
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 1
  - type: Bloodstream
    bloodMaxVolume: 100
  - type: CombatMode
  - type: MeleeWeapon
    soundHit:
      path: /Audio/Weapons/Xeno/alien_claw_flesh3.ogg
    angle: 0
    animation: WeaponArcClaw
    damage:
      types:
        Slash: 6
  - type: ReplacementAccent
    accent: genericAggressive
  - type: SalvageMobRestrictions

- type: entity
  parent: BaseMobFleshSalvage
  id: MobFleshJaredSalvage
  components:
  - type: Sprite
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: jared
  - type: DamageStateVisuals
    states:
      Alive:
        Base: jared
      Critical:
        Base: dead
      Dead:
        Base: dead
  - type: MeleeWeapon
    soundHit:
        path: /Audio/Weapons/Xeno/alien_claw_flesh3.ogg
    angle: 0
    animation: WeaponArcClaw
    damage:
      types:
        Slash: 8

- type: entity
  parent: BaseMobFleshSalvage
  id: MobFleshGolemSalvage
  components:
  - type: Sprite
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: golem
  - type: DamageStateVisuals
    states:
      Alive:
        Base: golem
      Critical:
        Base: dead
      Dead:
        Base: dead
  - type: MobThresholds
    thresholds:
      0: Alive
      50: Dead
  - type: MeleeWeapon
    soundHit:
        path: /Audio/Weapons/Xeno/alien_claw_flesh3.ogg
    angle: 0
    animation: WeaponArcClaw
    damage:
      types:
        Slash: 8

- type: entity
  parent: BaseMobFleshSalvage
  id: MobFleshClampSalvage
  components:
  - type: Sprite
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: clamp
  - type: DamageStateVisuals
    states:
      Alive:
        Base: clamp
      Critical:
        Base: dead
      Dead:
        Base: dead
  - type: MobThresholds
    thresholds:
      0: Alive
      30: Dead
  - type: MovementSpeedModifier
    baseWalkSpeed: 2
    baseSprintSpeed: 2.5

- type: entity
  parent: BaseMobFleshSalvage
  id: MobFleshLoverSalvage
  components:
  - type: Sprite
    layers:
    - map: [ "enum.DamageStateVisualLayers.Base" ]
      state: lover
  - type: DamageStateVisuals
    states:
      Alive:
        Base: lover
      Critical:
        Base: dead
      Dead:
        Base: dead
  - type: MobThresholds
    thresholds:
      0: Alive
      30: Dead
  - type: MovementSpeedModifier
    baseWalkSpeed: 2
    baseSprintSpeed: 2.5
