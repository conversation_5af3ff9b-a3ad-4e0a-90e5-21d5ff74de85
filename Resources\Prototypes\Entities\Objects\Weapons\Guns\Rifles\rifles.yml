- type: entity
  name: "BaseWeaponRifle"
  parent: [BaseItem, BaseGunWieldable]
  id: BaseWeaponRifle
  description: "Корінцевий зубастий і пагонистий."
  abstract: true
  components:
  - type: Sprite
  - type: Item
    size: Huge
  - type: Clothing
    sprite: Objects/Weapons/Guns/Rifles/ak.rsi
    quickEquip: false
    slots:
    - Back
    - suitStorage
  - type: Wieldable
  - type: GunRequiresWield
  - type: AmmoCounter
  - type: Gun
    fireRate: 5
    selectedMode: FullAuto
    availableModes:
      - FullAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/batrifle.ogg
  - type: ChamberMagazineAmmoProvider
    soundRack:
      path: /Audio/Weapons/Guns/Cock/sf_rifle_cock.ogg
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        startingItem: MagazineLightRifle
        insertSound: /Audio/Weapons/Guns/MagIn/batrifle_magin.ogg
        ejectSound: /Audio/Weapons/Guns/MagOut/batrifle_magout.ogg
        priority: 2
        whitelist:
          tags:
            - MagazineLightRifle
      gun_chamber:
        name: Chamber
        startingItem: CartridgeLightRifle
        priority: 1
        whitelist:
          tags:
            - CartridgeLightRifle
  - type: ContainerContainer
    containers:
      gun_magazine: !type:ContainerSlot
      gun_chamber: !type:ContainerSlot
  - type: StaticPrice
    price: 500
  - type: Execution
  - type: MeleeWeapon
    attackRate: 1.3333
    damage:
      types:
        Blunt: 8.5
    bluntStaminaDamageFactor: 1.25
    swapKeys: true
    disableHeavy: true
    animation: WeaponArcThrust
    wideAnimationRotation: 180
    soundHit:
      collection: MetalThud
  - type: IncreaseDamageOnWield
    damage:
      types:
        Blunt: 2.5
  - type: DamageOtherOnHit
    staminaCost: 7.5
    # PIRATE START
  - type: CanHoldAccessories
    whiteListTags:
      - CombatKnife
  - type: CanTakeAim
    # PIRATE END

- type: entity
  name: "AKMS"
  parent: BaseWeaponRifle
  id: WeaponRifleAk
  description: "Культова зброя війни. Використовує гвинтівкові патрони калібру .30."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Rifles/ak.rsi
    layers:
      - state: base
        map: ["enum.GunVisualLayers.Base"]
      - state: mag-0
        map: ["enum.GunVisualLayers.Mag"]
  - type: Gun
    fireRate: 5
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/rifle2.ogg
    fireOnDropChance: 0.5
  - type: ChamberMagazineAmmoProvider
    soundRack:
      path: /Audio/Weapons/Guns/Cock/ltrifle_cock.ogg
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        startingItem: MagazineLightRifle
        insertSound: /Audio/Weapons/Guns/MagIn/ltrifle_magin.ogg
        ejectSound: /Audio/Weapons/Guns/MagOut/ltrifle_magout.ogg
        priority: 2
        whitelist:
          tags:
            - MagazineLightRifle
      gun_chamber:
        name: Chamber
        startingItem: CartridgeLightRifle
        priority: 1
        whitelist:
          tags:
            - CartridgeLightRifle
  - type: ContainerContainer
    containers:
      gun_magazine: !type:ContainerSlot
      gun_chamber: !type:ContainerSlot
  - type: MagazineVisuals
    magState: mag
    steps: 1
    zeroVisible: true
  - type: Appearance

- type: entity
  name: "М-90г"
  parent: BaseWeaponRifle
  id: WeaponRifleM90GrenadeLauncher
  description: "Стара модель карабіна буллпап, з підствольним гранатометом. Використовує гвинтівкові набої калібру .20."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Rifles/carbine.rsi
    layers:
      - state: base
        map: ["enum.GunVisualLayers.Base"]
      - state: mag-0
        map: ["enum.GunVisualLayers.Mag"]
  - type: Clothing
    sprite: Objects/Weapons/Guns/Rifles/carbine.rsi
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        startingItem: MagazineRifle
        insertSound: /Audio/Weapons/Guns/MagIn/batrifle_magin.ogg
        ejectSound: /Audio/Weapons/Guns/MagOut/batrifle_magout.ogg
        priority: 2
        whitelist:
          tags:
            - MagazineRifle
      gun_chamber:
        name: Chamber
        startingItem: CartridgeRifle
        priority: 1
        whitelist:
          tags:
            - CartridgeRifle
  - type: ContainerContainer
    containers:
      gun_magazine: !type:ContainerSlot
      gun_chamber: !type:ContainerSlot
  - type: MagazineVisuals
    magState: mag
    steps: 1
    zeroVisible: true
  - type: Appearance

- type: entity
  name: "Лектер"
  parent: BaseWeaponRifle
  id: WeaponRifleLecter
  description: "Штурмова гвинтівка високого військового класу. Використовує набої 20-го калібру."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Rifles/lecter.rsi
    layers:
      - state: base
        map: ["enum.GunVisualLayers.Base"]
      - state: mag-0
        map: ["enum.GunVisualLayers.Mag"]
  - type: Clothing
    sprite: Objects/Weapons/Guns/Rifles/lecter.rsi
  - type: Gun
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/ltrifle.ogg
    fireOnDropChance: 0.2
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        startingItem: MagazineRifle
        insertSound: /Audio/Weapons/Guns/MagIn/ltrifle_magin.ogg
        ejectSound: /Audio/Weapons/Guns/MagOut/ltrifle_magout.ogg
        priority: 2
        whitelist:
          tags:
            - MagazineRifle
      gun_chamber:
        name: Chamber
        startingItem: CartridgeRifle
        priority: 1
        whitelist:
          tags:
            - CartridgeRifle
  - type: ContainerContainer
    containers:
      gun_magazine: !type:ContainerSlot
      gun_chamber: !type:ContainerSlot
  - type: MagazineVisuals
    magState: mag
    steps: 1
    zeroVisible: true
  - type: Appearance

- type: entity
  name: "Лектер"
  parent: WeaponRifleLecter
  id: WeaponRifleLecterRubber
  suffix: Non-lethal
  components:
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        startingItem: MagazineRifleRubber
        insertSound: /Audio/Weapons/Guns/MagIn/ltrifle_magin.ogg
        ejectSound: /Audio/Weapons/Guns/MagOut/ltrifle_magout.ogg
        priority: 2
        whitelist:
          tags:
            - MagazineRifle
      gun_chamber:
        name: Chamber
        startingItem: CartridgeRifleRubber
        priority: 1
        whitelist:
          tags:
            - CartridgeRifle

- type: entity
  name: "Піна Foam Force Astro Ace"
  parent: [BaseWeaponShotgun, BaseGunWieldable]
  id: WeaponRifleFoam
  description: "Преміальна пістолетна гвинтівка найвищої якості. Її пластик здається міцним, а механізми - надійними."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Rifles/foam_rifle.rsi
  - type: Clothing
    sprite: Objects/Weapons/Guns/Rifles/foam_rifle.rsi
  - type: Item
    sprite: Objects/Weapons/Guns/Rifles/foam_rifle_inhand_64x.rsi
  - type: BallisticAmmoProvider
    whitelist:
      tags:
      - BulletFoam
    capacity: 10
    proto: BulletFoam
  - type: GunRequiresWield #remove when inaccuracy on spreads is fixed
  - type: Gun
    fireRate: 2
    selectedMode: SemiAuto
    availableModes:
    - SemiAuto
    soundGunshot:
      path: /Audio/Effects/thunk.ogg
    soundEmpty:
      path: /Audio/Weapons/Guns/Empty/empty.ogg
    clumsyProof: true
  - type: MeleeWeapon
    damage:
      types:
        Blunt: 0 # Krillions must bubble-wrap the foam rifle
    soundHit:
      collection: ToySqueak
  - type: IncreaseDamageOnWield
    damage:
      types:
        Blunt: 0
  - type: DamageOtherOnHit
    staminaCost: 3.5
