revenant-essence-amount = You have [color=plum]{$current} Essence[/color]. Your regen amount is [color=plum]{$max} Essence[/color].
revenant-max-essence-increased = Your max essence has increased!

revenant-not-enough-essence = Not enough essence!
revenant-in-solid = You cannot use this ability while within a solid object.

revenant-soul-too-powerful = This soul is too strong to harvest!
revenant-soul-harvested = This soul has already been harvested!

revenant-soul-searching = You search for the soul of {THE($target)}.

revenant-soul-yield-high = {CAPITALIZE(THE($target))} has an above average soul!
revenant-soul-yield-average = {CAPITALIZE(THE($target))} has an average soul.
revenant-soul-yield-low = {CAPITALIZE(THE($target))} has a below average soul.

revenant-soul-begin-harvest = {CAPITALIZE(THE($target))} suddenly rises slightly into the air, {POSS-ADJ($target)} skin turning an ashy gray.
revenant-soul-finish-harvest = {CAPITALIZE(THE($target))} slumps onto the ground!

# UI
revenant-user-interface-title = Ability Shop
revenant-user-interface-essence-amount = [color=plum]{$amount}[/color] Stolen Essence

revenant-user-interface-cost = {$price} Essence