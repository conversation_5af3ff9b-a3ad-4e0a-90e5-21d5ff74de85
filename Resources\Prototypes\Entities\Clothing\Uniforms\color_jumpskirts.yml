# All of the jumpskirts here have had their parent changed to ClothingUniformSkirtBase, as to allow harpies to wear them.
# White Jumpskirt
- type: entity
  parent: ClothingUniformSkirtBase
  id: ClothingUniformJumpskirtColorWhite
  name: "біла комбінезон-спідниця"
  description: "Універсальний білий комбінезон без жодних позначок про звання."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    layers:
    - state: icon
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
      - state: trinkets-equipped-INNERCLOTHING

# Grey Jumpskirt
- type: entity
  parent: ClothingUniformSkirtBase
  id: ClothingUniformJumpskirtColorGrey
  name: "сірий комбінезон-спідниця"
  description: "Зі смаком підібрана сіра спідниця-комбінезон, що нагадує про старі добрі часи."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    layers:
    - state: icon
      color: "#b3b3b3"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#b3b3b3"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#b3b3b3"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#b3b3b3"
      - state: trinkets-equipped-INNERCLOTHING

# Black Jumpskirt
- type: entity
  parent: ClothingUniformSkirtBase
  id: ClothingUniformJumpskirtColorBlack
  name: "чорна комбінезон-спідниця"
  description: "Універсальний чорний комбінезон без маркування рангу."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    layers:
    - state: icon
      color: "#3f3f3f"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#3f3f3f"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#3f3f3f"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#3f3f3f"
      - state: trinkets-equipped-INNERCLOTHING

# Blue Jumpskirt
- type: entity
  parent: ClothingUniformSkirtBase
  id: ClothingUniformJumpskirtColorBlue
  name: "синя спідниця"
  description: "Універсальний синій комбінезон без жодних позначок про звання."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    layers:
    - state: icon
      color: "#52aecc"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#52aecc"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#52aecc"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#52aecc"
      - state: trinkets-equipped-INNERCLOTHING

# Dark Blue Jumpskirt
- type: entity
  parent: ClothingUniformSkirtBase
  id: ClothingUniformJumpskirtColorDarkBlue
  name: "темно-синя комбінезон-спідниця"
  description: "Універсальний темно-синій комбінезон без маркування звання."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    layers:
    - state: icon
      color: "#3285ba"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#3285ba"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#3285ba"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#3285ba"
      - state: trinkets-equipped-INNERCLOTHING

# Teal Jumpskirt
- type: entity
  parent: ClothingUniformSkirtBase
  id: ClothingUniformJumpskirtColorTeal
  name: "бірюзова комбінезон-спідниця"
  description: "Універсальна спідниця-комбінезон бірюзового кольору без маркування рангу."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    layers:
    - state: icon
      color: "#77f3b7"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#77f3b7"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#77f3b7"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#77f3b7"
      - state: trinkets-equipped-INNERCLOTHING

# Green Jumpskirt
- type: entity
  parent: ClothingUniformSkirtBase
  id: ClothingUniformJumpskirtColorGreen
  name: "зелена комбінезон-спідниця"
  description: "Універсальний зелений комбінезон без маркування звання."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    layers:
    - state: icon
      color: "#9ed63a"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#9ed63a"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#9ed63a"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#9ed63a"
      - state: trinkets-equipped-INNERCLOTHING

 # Dark Green Jumpskirt
- type: entity
  parent: ClothingUniformSkirtBase
  id: ClothingUniformJumpskirtColorDarkGreen
  name: "темно-зелена комбінезон-спідниця"
  description: "Універсальний темно-зелений комбінезон без маркування звання."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    layers:
    - state: icon
      color: "#79CC26"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#79CC26"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#79CC26"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#79CC26"
      - state: trinkets-equipped-INNERCLOTHING

# Orange Jumpskirt
- type: entity
  parent: ClothingUniformSkirtBase
  id: ClothingUniformJumpskirtColorOrange
  name: "помаранчева комбінезон-спідниця"
  description: "Не носіть це поруч з параноїдальними офіцерами безпеки."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    layers:
    - state: icon
      color: "#ff8c19"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ff8c19"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#ff8c19"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ff8c19"
      - state: trinkets-equipped-INNERCLOTHING

# Pink Jumpskirt
- type: entity
  parent: ClothingUniformSkirtBase
  id: ClothingUniformJumpskirtColorPink
  name: "рожева комбінезон-спідниця"
  description: "Просто дивлячись на це, ви відчуваєте себе <i>казково</i>."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    layers:
    - state: icon
      color: "#ffa69b"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffa69b"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#ffa69b"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ffa69b"
      - state: trinkets-equipped-INNERCLOTHING

# Red Jumpskirt
- type: entity
  parent: ClothingUniformSkirtBase
  id: ClothingUniformJumpskirtColorRed
  name: "червона комбінезон-спідниця"
  description: "Універсальний червоний комбінезон без жодних позначок про звання."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    layers:
    - state: icon
      color: "#eb0c07"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#eb0c07"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#eb0c07"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#eb0c07"
      - state: trinkets-equipped-INNERCLOTHING

# Yellow Jumpskirt
- type: entity
  parent: ClothingUniformSkirtBase
  id: ClothingUniformJumpskirtColorYellow
  name: "жовта комбінезон-спідниця"
  description: "Універсальний жовтий комбінезон без жодних позначок про звання."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    layers:
    - state: icon
      color: "#ffe14d"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ffe14d"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#ffe14d"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ffe14d"
      - state: trinkets-equipped-INNERCLOTHING

# Purple Jumpskirt
- type: entity
  parent: ClothingUniformSkirtBase
  id: ClothingUniformJumpskirtColorPurple
  name: "фіолетова комбінезон-спідниця"
  description: "Універсальний світло-фіолетовий комбінезон без маркування звання."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    layers:
    - state: icon
      color: "#9f70cc"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#9f70cc"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#9f70cc"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#9f70cc"
      - state: trinkets-equipped-INNERCLOTHING

# Light Brown Jumpskirt
- type: entity
  parent: ClothingUniformSkirtBase
  id: ClothingUniformJumpskirtColorLightBrown
  name: "світло-коричнева комбінезон-спідниця"
  description: "Універсальна світло-коричнева спідниця-комбінезон без знаків розрізнення."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    layers:
    - state: icon
      color: "#c59431"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#c59431"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#c59431"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#c59431"
      - state: trinkets-equipped-INNERCLOTHING

# Brown Jumpskirt
- type: entity
  parent: ClothingUniformSkirtBase
  id: ClothingUniformJumpskirtColorBrown
  name: "коричнева комбінезон-спідниця"
  description: "Універсальний коричневий комбінезон без маркування звання."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    layers:
    - state: icon
      color: "#a17229"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#a17229"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#a17229"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#a17229"
      - state: trinkets-equipped-INNERCLOTHING

# Maroon Jumpskirt
- type: entity
  parent: ClothingUniformSkirtBase
  id: ClothingUniformJumpskirtColorMaroon
  name: "бордова комбінезон-спідниця"
  description: "Універсальний бордовий комбінезон без маркування звання."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    layers:
    - state: icon
      color: "#cc295f"
    - state: trinkets-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#cc295f"
      - state: trinkets-inhand-left
      right:
      - state: inhand-right
        color: "#cc295f"
      - state: trinkets-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpskirt/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#cc295f"
      - state: trinkets-equipped-INNERCLOTHING
