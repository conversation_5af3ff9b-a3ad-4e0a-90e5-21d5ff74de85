- type: entity
  parent: BaseVehicle
  id: BaseVehicleRideable
  abstract: true
  name: "Транспортний засіб"
  components:
  - type: Strap
    buckleOffset: "0.10, 0.36"
  - type: InputMover
  - type: InteractionOutline
  - type: Vehicle
  - type: Pullable
  - type: Physics
    bodyType: KinematicController
  - type: Clickable
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.4
        density: 360
        restitution: 0.0
        mask:
        - MobMask
        layer:
        - TableLayer
  - type: Appearance
  - type: ItemSlots
    slots:
      key_slot: #this slot name is important
        name: vehicle-slot-component-slot-name-keys
        whitelist:
          requireAll: true
          tags:
          - VehicleKey
        insertSound:
          path: /Audio/Effects/Vehicle/vehiclestartup.ogg
          params:
            volume: -3
  - type: StaticPrice
    price: 750 # Grand Theft Auto.

- type: entity
  parent: BaseVehicleRideable
  id: VehicleSkeletonMotorcycle
  name: "скелетний мотоцикл"
  description: "Поганий до мозку кісток." # Frontier, using VehicleSkeletonMotorcycleNF instead
  components:
    - type: Vehicle
    - type: Sprite
      sprite: Objects/Vehicles/motorbike.rsi
      layers:
        - state: vehicle
      noRot: true
    - type: Strap
      buckleOffset: "0.1, -0.05"
    - type: MovementSpeedModifier
      acceleration: 2
      friction: 1.5
      baseWalkSpeed: 4.5
      baseSprintSpeed: 7
    - type: UnpoweredFlashlight
    - type: PointLight
      enabled: false
      radius: 3.5
      softness: 2
      mask: /Textures/Effects/LightMasks/cone.png
      autoRot: true
    - type: ItemSlots
      slots:
        key_slot:
          name: vehicle-slot-component-slot-name-keys
          whitelist:
            requireAll: true
            tags:
              - VehicleKey
              - SkeletonMotorcycleKeys
          insertSound:
            path: /Audio/Effects/Vehicle/vehiclestartup.ogg
            params:
              volume: -3

- type: entity
  id: VehicleUnicycle
  parent: [BaseVehicleRideable, BaseFoldable, BaseItem]
  name: "одноколісний велосипед"
  description: "У нього лише одне колесо!"
  components:
  - type: Vehicle
    hornSound:
      path: /Audio/Effects/Vehicle/bicyclebell.ogg
  - type: Sprite
    sprite: Objects/Vehicles/unicycle.rsi
    layers:
    - state: vehicle
    - state: vehicle_folded
      map: ["foldedLayer"]
      visible: false
    noRot: true
  - type: Strap
    buckleOffset: "0.1, -0.05"
  - type: MovementSpeedModifier
    acceleration: 1
    friction: 0.8
    baseWalkSpeed: 3.5
    baseSprintSpeed: 4.3
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 240
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak

- type: entity
  parent: VehicleUnicycle
  id: VehicleUnicycleFolded
  suffix: folded
  components:
  - type: Foldable
    folded: true
