- type: entity
  parent: [ClothingMaskBaseButcherable, BaseFoldable]
  id: ClothingMaskBandanaBase
  abstract: true
  components:
  - type: Appearance
  - type: Foldable
    canFoldInsideContainer: true
  - type: FoldableClothing
    foldedSlots:
    - HEAD
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
    unfoldedSlots:
    - MASK
  - type: Mask
  - type: IngestionBlocker
  - type: IdentityBlocker
    coverage: MOUTH
  - type: Sprite
    layers:
    - state: icon_mask
      map: [ "unfoldedLayer" ]
    - state: icon
      map: ["foldedLayer"]
      visible: false
  - type: Tag
    tags:
    - Bandana
  - type: HideLayerClothing
    slots:
    - Snout
    hideOnToggle: true

- type: entity
  parent: ClothingMaskBandanaBase
  id: ClothingMaskBandWhite
  name: "бандана"
  description: "Бандана, щоб виглядати круто."
  components:
  - type: Sprite
    sprite: Clothing/Head/Bandanas/white.rsi
  - type: Clothing
    sprite: Clothing/Head/Bandanas/white.rsi

- type: entity
  parent: ClothingMaskBandanaBase
  id: ClothingMaskBandBlack
  name: "чорна бандана"
  description: "Чорна бандана, щоб виглядати круто."
  components:
  - type: Sprite
    sprite: Clothing/Head/Bandanas/black.rsi
  - type: Clothing
    sprite: Clothing/Head/Bandanas/black.rsi

- type: entity
  parent: ClothingMaskBandanaBase
  id: ClothingMaskBandBlue
  name: "синя бандана"
  description: "Синя бандана, щоб виглядати круто."
  components:
  - type: Sprite
    sprite: Clothing/Head/Bandanas/blue.rsi
  - type: Clothing
    sprite: Clothing/Head/Bandanas/blue.rsi

- type: entity
  parent: ClothingMaskBandanaBase
  id: ClothingMaskBandBotany
  name: "ботанічна бандана"
  description: "Ботанічна бандана, щоб виглядати круто, виготовлена з натуральних волокон."
  components:
  - type: Sprite
    sprite: Clothing/Head/Bandanas/botany.rsi
  - type: Clothing
    sprite: Clothing/Head/Bandanas/botany.rsi
  - type: Tag
    tags:
    - ClothMade
    - WhitelistChameleon

- type: entity
  parent: ClothingMaskBandanaBase
  id: ClothingMaskBandGold
  name: "золота бандана"
  description: "Золота бандана, щоб виглядати круто."
  components:
  - type: Sprite
    sprite: Clothing/Head/Bandanas/gold.rsi
  - type: Clothing
    sprite: Clothing/Head/Bandanas/gold.rsi

- type: entity
  parent: ClothingMaskBandanaBase
  id: ClothingMaskBandGreen
  name: "зелена бандана"
  description: "Зелена бандана, щоб виглядати круто."
  components:
  - type: Sprite
    sprite: Clothing/Head/Bandanas/green.rsi
  - type: Clothing
    sprite: Clothing/Head/Bandanas/green.rsi

- type: entity
  parent: ClothingMaskBandanaBase
  id: ClothingMaskBandGrey
  name: "сіра бандана"
  description: "Сіра бандана, щоб виглядати круто."
  components:
  - type: Sprite
    sprite: Clothing/Head/Bandanas/grey.rsi
  - type: Clothing
    sprite: Clothing/Head/Bandanas/grey.rsi

- type: entity
  parent: ClothingMaskBandanaBase
  id: ClothingMaskBandRed
  name: "червона бандана"
  description: "Червона бандана, щоб виглядати круто."
  components:
  - type: Sprite
    sprite: Clothing/Head/Bandanas/red.rsi
  - type: Clothing
    sprite: Clothing/Head/Bandanas/red.rsi

- type: entity
  parent: ClothingMaskBandanaBase
  id: ClothingMaskBandSkull
  name: "бандана з черепом"
  description: "Бандана з черепом, щоб виглядати ще крутіше."
  components:
  - type: Sprite
    sprite: Clothing/Head/Bandanas/skull.rsi
  - type: Clothing
    sprite: Clothing/Head/Bandanas/skull.rsi

- type: entity
  parent: ClothingMaskBandanaBase
  id: ClothingMaskBandMerc
  name: "бандана найманця"
  description: "Для захисту голови від сонця, комах та інших небезпек вищого шляху."
  components:
  - type: Sprite
    sprite: Clothing/Head/Bandanas/merc.rsi
  - type: Clothing
    sprite: Clothing/Head/Bandanas/merc.rsi

- type: entity
  parent: ClothingMaskBandanaBase
  id: ClothingMaskBandBrown
  name: "коричнева бандана"
  description: "Коричнева бандана, щоб виглядати круто."
  components:
  - type: Sprite
    sprite: Clothing/Head/Bandanas/brown.rsi
  - type: Clothing
    sprite: Clothing/Head/Bandanas/brown.rsi
