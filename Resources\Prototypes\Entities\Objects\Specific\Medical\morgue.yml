- type: entity
  id: BodyBag
  parent: BaseFoldable
  name: "мішок для трупів"
  description: "Поліетиленовий мішок, призначений для зберігання і транспортування трупів, щоб запобігти розкладанню тіла."
  components:
  - type: Item
    size: Small
  - type: Sprite
    drawdepth: SmallObjects # I guess body bags need appear above a coroner's table?
    sprite: Objects/Specific/Medical/Morgue/bodybags.rsi
    layers:
    - state: bag
      map: ["unfoldedLayer", "enum.StorageVisualLayers.Base"]
    - state: bag_folded
      map: ["foldedLayer"]
      visible: false
    - state: open_overlay
      map: ["enum.StorageVisualLayers.Door"]
    - state: paper
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: Icon
    sprite: Objects/Specific/Medical/Morgue/bodybags.rsi
    state: bag
  - type: Tag
    tags:
    - BodyBag
  - type: Clickable
  - type: InteractionOutline
  - type: MovedByPressure
  - type: Physics
    bodyType: Dynamic
  - type: Transform
    noRot: true
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.4,0.25,0.1"
        density: 20
        mask:
        - Impassable
  - type: EntityStorage
    capacity: 1
    isCollidableWhenOpen: true
    closeSound:
      path: /Audio/Misc/zip.ogg
    openSound:
      path: /Audio/Misc/zip.ogg
  - type: EntityStorageLayingDownOverride
  - type: PaperLabel
    labelSlot:
      insertVerbText: Attach Label
      ejectVerbText: Remove Label
      whitelist:
        components:
        - Paper
  - type: Appearance
  - type: EntityStorageVisuals
    stateDoorOpen: open_overlay
  - type: GenericVisualizer
    visuals:
      enum.PaperLabelVisuals.HasLabel:
        enum.PaperLabelVisuals.Layer:
          True: {visible: true}
          False: {visible: false}
      enum.PaperLabelVisuals.LabelType:
        enum.PaperLabelVisuals.Layer:
          Paper: { state: paper }
          Bounty: { state: bounty }
          CaptainsPaper: { state: captains_paper }
          Invoice: { state: invoice }
      enum.FoldedVisuals.State:
        foldedLayer:
          True: {visible: true}
          False: {visible: false}
        unfoldedLayer:
          True: {visible: false}
          False: {visible: true}
  - type: Pullable
  - type: AntiRottingContainer
  - type: ItemSlots
  - type: ContainerContainer
    containers:
      entity_storage: !type:Container
      paper_label: !type:ContainerSlot
  - type: StaticPrice
    price: 50

- type: entity
  id: BodyBagFolded
  name: "мішок для трупів"
  description: "Поліетиленовий мішок, призначений для зберігання і транспортування трупів, щоб запобігти розкладанню тіла."
  parent: BodyBag
  suffix: folded
  components:
  - type: Foldable
    folded: true
#  - type: BodyBagItem #TODO: we need some kind of generic placable, like thus:
# - type: Placeable
#   prototype: someId
#   snap: Center

- type: entity
  parent: BaseItem
  id: Ash
  name: "попіл"
  description: "Раніше це було чимось, але зараз це не так."
  components:
  - type: Item
    size: Tiny
  - type: Sprite
    sprite: Objects/Materials/materials.rsi
    state: ash
  - type: Tag
    tags:
    - Trash
    - Ash # Frontier
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50
        reagents:
        - ReagentId: Ash
          Quantity: 10
  - type: SolutionSpiker
    sourceSolution: food
    ignoreEmpty: true
  - type: ScoopableSolution
    solution: food
  - type: Extractable
    grindableSolutionName: food
  - type: SupermatterImmune

- type: entity
  parent: Ash
  id: Ectoplasm
  name: "ектоплазма"
  description: "Набагато менш смертельна в такому вигляді."
  components:
    - type: Sprite
      sprite: DeltaV/Mobs/Ghosts/revenant.rsi # DeltaV - Custom revenant sprite
      state: ectoplasm
    - type: Tag
      tags:
        - Trash
        - Ectoplasm
    - type: SolutionContainerManager
      solutions:
        food:
          maxVol: 50
          reagents:
            - ReagentId: Ash
              Quantity: 5
            - ReagentId: Necrosol
              Quantity: 5
            - ReagentId: Ectoplasm  # DeltaV - for normality crystals
              Quantity: 30          # DeltaV
    - type: GuideHelp
      guides:
      - MinorAntagonists
