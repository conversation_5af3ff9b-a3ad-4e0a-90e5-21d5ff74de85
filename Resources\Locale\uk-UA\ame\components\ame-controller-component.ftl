ame-controller-component-interact-no-hands-text = У вас немає рук.
ame-controller-component-interact-using-no-hands-text = У вас немає рук.
ame-controller-component-interact-using-already-has-jar = В контролер вже завантажено банку.
ame-controller-component-interact-using-success = Ви вставляєте банку у паливний слот.
ame-controller-component-interact-using-fail = Ви не можете вставити це у контролер...

ame-controller-component-fuel-slot-fail-whitelist = Ви не можете покласти це в контролер ...
ame-controller-component-fuel-slot-fail-locked = Перед тим, як маніпулювати паливом, вимкніть ГАМ.
ame-controller-component-fuel-slot-success-insert = Ви вставляєте банку в паливний отвір.

## ІНТЕРФЕЙС

ame-window-title = Блок Керування Антиматерією
ame-window-engine-status-label = Статус Двигунів:
ame-window-engine-injection-status-not-injecting-label = Не Вприскується
ame-window-engine-injection-status-injecting-label = Вприсування...
ame-window-toggle-injection-button = Перемкнути Вприскування
ame-window-fuel-status-label = Стан палива:
ame-window-fuel-not-inserted-text = Паливо не надано
ame-window-injection-amount-label = Обʼєм вприскування:
ame-window-refresh-parts-button = Оновити Деталі
ame-window-core-count-label = Кількість ядер:
ame-window-power-currentsupply-label = Поточна потужність:
ame-window-power-targetsupply-label = Цільова потужність:
ame-window-eject-button = Витягти
ame-window-increase-fuel-button = Збільшити
ame-window-decrease-fuel-button = Зменшити
