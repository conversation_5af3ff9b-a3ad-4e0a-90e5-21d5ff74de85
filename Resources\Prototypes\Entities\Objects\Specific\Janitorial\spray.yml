- type: entity
  name: "пляшка з розпилювачем"
  id: SprayBottle
  parent: BaseItem
  suffix: Empty
  description: "Флакон з розпилювачем, що відкручується."
  components:
  - type: Drink
    solution: spray
    ignoreEmpty: true
    useSound:
      path: /Audio/Effects/spray3.ogg
    transferAmount: 10
  - type: Tag
    tags:
    - Spray
    - Trash
  - type: Sprite
    sprite: Objects/Specific/Janitorial/janitorial.rsi
    state: cleaner
  - type: Item
    sprite: Objects/Specific/Janitorial/janitorial.rsi
  - type: SolutionContainerManager
    solutions:
      spray:
        maxVol: 100
  - type: RefillableSolution
    solution: spray
  - type: DrainableSolution
    solution: spray
  - type: SolutionTransfer
    canChangeTransferAmount: true
  - type: SolutionItemStatus
    solution: spray
  - type: UseDelay
  - type: Spray
    transferAmount: 10
    sprayVelocity: 2
    spraySound:
      path: /Audio/Effects/spray2.ogg
  - type: TrashOnSolutionEmpty
    solution: spray
  - type: ExaminableSolution
    solution: spray

- type: entity
  name: "мега пляшка з розпилювачем"
  id: MegaSprayBottle
  parent: SprayBottle
  suffix: Empty
  description: "Величезна пляшка-розпилювач, здатна на неперевершену прибиральну силу."
  components:
  - type: Sprite
    sprite: Objects/Specific/Janitorial/janitorial.rsi
    state: cleaner_large
  - type: SolutionContainerManager
    solutions:
      spray:
        maxVol: 250
  - type: Spray
    transferAmount: 15
    sprayedPrototype: BigVapor
    sprayVelocity: 3
    sprayDistance: 4.5
    spraySound:
      path: /Audio/Effects/spray2.ogg

- type: entity
  name: "пляшка з розпилювачем"
  id: SprayBottleWater
  suffix: Filled
  parent: SprayBottle
  components:
  - type: Tag
    tags:
    - Spray
  - type: SolutionContainerManager
    solutions:
      spray:
        maxVol: 100
        reagents:
        - ReagentId: Water
          Quantity: 100

- type: entity
  name: "космічний очищувач"
  description: "BLAM!-брендовий засіб для прибирання, що не піниться!"
  id: SprayBottleSpaceCleaner
  parent: SprayBottle
  suffix: ""
  components:
  - type: SolutionContainerManager
    solutions:
      spray:
        maxVol: 100
        reagents:
        - ReagentId: SpaceCleaner
          Quantity: 100
  - type: Tag
    tags:
      - Spray
# Vapor

- type: entity
  id: Vapor
  name: "пара"
  categories: [ HideSpawnMenu ]
  components:
  - type: SolutionContainerManager
    solutions:
      vapor:
        maxVol: 50
  - type: Vapor
  - type: AnimationPlayer
  - type: Sprite
    sprite: Effects/chempuff.rsi
    rotation: 180
    layers:
    - state: chempuff
      map: ["enum.VaporVisualLayers.Base"]
  - type: Physics
    bodyType: Dynamic
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        hard: false
        mask:
        - FullTileMask
        - Opaque
  - type: Appearance
  - type: VaporVisuals

- type: entity
  id: BigVapor
  parent: Vapor
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Effects/chempuff.rsi
    rotation: 180
    layers:
    - state: chempuff
      scale: 2, 2
      map: ["enum.VaporVisualLayers.Base"]
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.6,-0.6,0.6,0.6"
        hard: false
        mask:
        - FullTileMask
        - Opaque
