# When adding new food also add to random spawner located in Resources\Prototypes\Entities\Markers\Spawners\Random\Food_Drinks\food_meal.yml
# A bunch of different meals. This stuff doesn't come off their plates because
# it's assembled on them. Or they just don't have plates.

# Base

- type: entity
  parent: FoodInjectableBase
  id: FoodMealBase
  abstract: true
  description: "Смачна їжа, приготована з любов'ю."
  components:
  - type: Item
    storedRotation: -90
  - type: Food
  - type: Sprite
    sprite: Objects/Consumable/Food/meals.rsi
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 26
        reagents:
        - ReagentId: Nutriment
          Quantity: 20

# Meals

- type: entity
  name: "завантажена запечена картопля"
  parent: FoodMealBase
  id: FoodMealPotatoLoaded
  description: "Повністю запечена."
  components:
  - type: FlavorProfile
    flavors:
      - cheesy
      - potatoes
  - type: Sprite
    state: loadedbakedpotato
# Tastes like potato.

- type: entity
  name: "картопля фрі"
  parent: FoodMealBase
  id: FoodMealFries
  description: "Також відома як картопля фрі, картопля фрі, картопля фрі тощо."
  components:
  - type: FlavorProfile
    flavors:
      - potatoes
      - salty
  - type: Sprite
    state: fries
# Tastes like fries, salt.

- type: entity
  name: "сирна картопля фрі"
  parent: FoodMealBase
  id: FoodMealFriesCheesy
  description: "Картопля фрі. Покрита сиром. Ага."
  components:
  - type: FlavorProfile
    flavors:
      - potatoes
      - salty
      - cheesy
  - type: Sprite
    state: fries-cheesy
# Tastes like fries, cheese.

- type: entity
  name: "морквяна картопля фрі"
  parent: FoodMealBase
  id: FoodMealFriesCarrot
  description: "Смачна картопля фрі зі свіжої моркви."
  components:
  - type: FlavorProfile
    flavors:
      - carrots
      - salty
  - type: Sprite
    state: fries-carrot
  - type: Tag
    tags:
    - CarrotFries
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 26
        reagents:
        - ReagentId: JuiceCarrot
          Quantity: 20
# Tastes like carrots, salt.

- type: entity
  name: "начос"
  parent: FoodMealBase
  id: FoodMealNachos
  description: "Чіпси з Космічної Мексики."
  components:
  - type: FlavorProfile
    flavors:
      - nachos
  - type: Sprite
    state: nachos
# Tastes like nachos.

- type: entity
  name: "сирні начос"
  parent: FoodMealBase
  id: FoodMealNachosCheesy
  description: "Смачне поєднання начос і сиру, що плавиться."
  components:
  - type: FlavorProfile
    flavors:
      - nachos
      - cheesy
  - type: Sprite
    state: nachos-cheesy
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 3
        - ReagentId: TableSalt
          Quantity: 1
# Tastes like nachos, cheese.

- type: entity
  name: "Кубинські начос"
  parent: FoodMealBase
  id: FoodMealNachosCuban
  description: "Це небезпечно гострі начос."
  components:
  - type: FlavorProfile
    flavors:
      - nachos
      - cheesy
      - spicy
  - type: Sprite
    state: nachos-cuban
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 7
        - ReagentId: CapsaicinOil
          Quantity: 8
        - ReagentId: Vitamin
          Quantity: 4
# Tastes like nachos, hot pepper.

- type: entity
  name: "м'ята"
  parent: FoodMealBase
  id: FoodMealMint
  description: "Він тонкий, як вафля."
  components:
  - type: FlavorProfile
    flavors:
      - minty
  - type: Sprite
    state: mint
  - type: SolutionContainerManager
    solutions:
      food:
        # Note that this acts as the limiter against injecting anything.
        maxVol: 1
        reagents:
        - ReagentId: Nutriment
          Quantity: 1
# Tastes like parsnips, salt.

- type: entity
  name: "баклажани парміджана"
  parent: FoodMealBase
  id: FoodMealEggplantParm
  description: "Єдиний хороший рецепт для баклажанів."
  components:
  - type: FlavorProfile
    flavors:
      - eggplant
      - cheesy
  - type: Sprite
    state: eggplantparm
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 2
  - type: Tag
    tags:
    - Fruit
# Tastes like eggplant, cheese.

- type: entity
  name: "яки імо"
  parent: FoodMealBase
  id: FoodMealPotatoYaki
  description: "Приготований зі смаженою солодкою картоплею!"
  components:
  - type: FlavorProfile
    flavors:
      - sweet
      - potatoes
  - type: Sprite
    state: yakiimo
# Tastes like sweet potato.

- type: entity
  name: "Кубинський короп"
  parent: FoodMealBase
  id: FoodMealCubancarp
  description: "Бутерброд, який обпікає язик, а потім залишає його онімілим!"
  components:
  - type: FlavorProfile
    flavors:
      - fishy
      - batter
      - spicy
  - type: Sprite
    state: cubancarp
# Delta-V: Removed the toxin and changed the description.
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 6
        - ReagentId: Protein
          Quantity: 3
  - type: Tag
    tags:
    - CubanCarp
    - Meat
# Tastes like fish, batter, hot peppers.

- type: entity
  name: "солонина з капустою"
  parent: FoodMealBase
  id: FoodMealCornedbeef
  description: "Тепер ви можете відчути себе справжнім туристом, який відпочиває в Ірландії."
  components:
  - type: FlavorProfile
    flavors:
      - meaty
      - cabbage
  - type: Sprite
    state: cornedbeef
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 4
  - type: Tag
    tags:
    - Meat
# Tastes like meat, cabbage.

- type: entity
  name: "філе мігрант"
  parent: FoodMealBase
  id: FoodMealBearsteak
  description: "Тому що з'їсти ведмедя було недостатньо мужньо."
  components:
  - type: FlavorProfile
    flavors:
      - meaty
      - fishy
  - type: Sprite
    state: bearsteak
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 2
        - ReagentId: Vitamin
          Quantity: 5
  - type: Tag
    tags:
    - Meat
# Tastes like meat, salmon.

- type: entity
  name: "свиня в ковдрі"
  parent: FoodMealBase
  id: FoodMealPigblanket
  description: "Крихітна ковбаска, загорнута в хрусткий, змащений маслом рулет. Звільніть цю свинку з ковдрової в'язниці, з'ївши її."
  components:
  - type: FlavorProfile
    flavors:
      - meaty
      - butter
  - type: Sprite
    state: pigblanket
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 1
  - type: Tag
    tags:
    - Meat
# Tastes like meat, butter.

- type: entity
  name: "реберця барбекю"
  parent: FoodMealBase
  id: FoodMealRibs
  description: "Реберця барбекю, вкриті корисним соусом барбекю. Найменш веганська річ, яка коли-небудь існувала."
  components:
  - type: Food
    trash: 
    - FoodKebabSkewer
  - type: FlavorProfile
    flavors:
      - meaty
      - smokey
  - type: Sprite
    state: ribs
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 25
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
        - ReagentId: Protein
          Quantity: 10
        - ReagentId: BbqSauce
          Quantity: 10
  - type: Tag
    tags:
    - Meat
# Tastes like meat, smokey sauce.

- type: entity
  name: "яйця бенедикт"
  parent: FoodMealBase
  id: FoodMealEggsbenedict
  description: "Тут лише одне яйце, як грубо."
  components:
  - type: FlavorProfile
    flavors:
      - egg
      - bacon
      - bun
  - type: Sprite
    state: benedict
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 4
  - type: Tag
    tags:
    - Meat
# Tastes like eggs, bacon, bun.

- type: entity
  name: "сирний омлет"
  parent: FoodMealBase
  id: FoodMealOmelette
  description: "Сирниий."
  components:
  - type: FlavorProfile
    flavors:
      - egg
      - cheesy
  - type: Sprite
    state: omelette
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 9
  - type: Tag
    tags:
    - Meat
# Tastes like egg, cheese.

- type: entity
  name: "яєчня"
  parent: FoodMealBase
  id: FoodMealFriedegg
  description: "Смажене яйце, злегка посолена і поперчена."
  components:
  - type: FlavorProfile
    flavors:
      - egg
      - salty
      - peppery
  - type: Sprite
    state: friedegg
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 2
        - ReagentId: TableSalt
          Quantity: 1
        - ReagentId: Blackpepper
          Quantity: 1
  - type: Tag
    tags:
    - Meat
# Tastes like egg, salt, pepper.

- type: entity
  name: "молочна мавпа"
  parent: FoodMealBase
  id: FoodMealMilkape
  description: "Король густих джунглів."
  components:
  - type: FlavorProfile
    flavors:
      - milk
      - chocolate
  - type: Sprite
    state: milkape
# Tastes like milk, chocolate, the jungle.

- type: entity
  name: "цибуля-порей пам'яті"
  parent: FoodMealBase
  id: FoodMealMemoryleek
  description: "Це має освіжити вашу пам'ять."
  components:
  - type: FlavorProfile
    flavors:
      - memoryleek
  - type: Sprite
    state: memoryLeek
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Allicin
          Quantity: 5
# Tastes like pain.

- type: entity
  name: "солоно-солодкий місо-кола-суп"
  parent: FoodMealBase
  id: DisgustingSweptSoup
  description: "Господи Боже."
  components:
  - type: Sprite
    state: saltysweet
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 25
        reagents:
        - ReagentId: Nutriment
          Quantity: 9
        - ReagentId: Water
          Quantity: 10
        - ReagentId: Blackpepper
          Quantity: 3
# Tastes awesome.

- type: entity
  name: "кесо"
  parent: FoodMealBase
  id: FoodMealQueso
  description: "Класичний соус для занурення, з яким неможливо помилитися."
  components:
    - type: FlavorProfile
      flavors:
        - cheesy
    - type: Sprite
      state: queso
# Its queso! Everyone loves queso... Right?.

- type: entity
  name: "сашимі"
  parent: FoodMealBase
  id: FoodMealSashimi
  description: "Його смак можна описати лише як \"екзотичний\"."
  components:
  - type: FlavorProfile
    flavors:
      - fishy
  - type: Sprite
    state: sashimi
# Delta-V: Removed the toxin and changed the description.
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 18
        reagents:
        - ReagentId: Nutriment
          Quantity: 6
        - ReagentId: Protein
          Quantity: 6
  - type: Tag
    tags:
    - Meat
# tastes exotic

- type: entity
  name: "енхиладас"
  parent: FoodMealBase
  id: FoodMealEnchiladas
  description: "Віва Ля Мехіко!"
  components:
    - type: FlavorProfile
      flavors:
        - meaty
    - type: Sprite
      state: enchiladas
    - type: SolutionContainerManager
      solutions:
        food:
          maxVol: 10
          reagents:
          - ReagentId: Nutriment
            Quantity: 8
          - ReagentId: CapsaicinOil
            Quantity: 6
    - type: Tag
      tags:
      - Meat
# What do Europeans eat instead of enchiladas? 25.4 millimeter-iladas.

- type: entity
  name: "динна фруктова миска"
  parent: FoodMealBase
  id: FoodSaladWatermelonFruitBowl
  description: "Єдиний салат, де можна з'їсти миску."
  components:
  - type: FlavorProfile
    flavors:
      - fruity
      - sour
  - type: Sprite
    state: melonfruitbowl
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 65
        reagents:
        - ReagentId: Nutriment
          Quantity: 30
        - ReagentId: Vitamin
          Quantity: 15
        - ReagentId: Water
          Quantity: 5
        - ReagentId: Bicaridine
          Quantity: 5
        - ReagentId: Kelotane
          Quantity: 5
  - type: Tag
    tags:
    - Fruit

- type: entity
  name: "кукурудза в маслі"
  parent: FoodMealBase
  id: FoodMealCornInButter
  description: "Масляниста."
  components:
  - type: Food
    trash: 
    - FoodPlate
  - type: FlavorProfile
    flavors:
      - corn
      - butter
  - type: Sprite
    state: corn-in-butter
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 4
        - ReagentId: Vitamin
          Quantity: 4

- type: entity
  name: "крила гарпії"
  parent: FoodMealBase
  id: FoodHarpyWings
  description: "Воно трохи в'язке."
  components:
  - type: FlavorProfile
    flavors:
      - chicken
  - type: Sprite
    state: harpywings
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 25
        reagents:
        - ReagentId: Nutriment
          Quantity: 13
        - ReagentId: Protein
          Quantity: 7
