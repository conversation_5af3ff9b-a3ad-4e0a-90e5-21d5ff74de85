# When adding new food also add to random spawner located in Resources\Prototypes\Entities\Markers\Spawners\Random\Food_Drinks\food_baked_whole.yml & food_baked_single.yml
# Base

- type: entity
  parent: FoodInjectableBase
  id: FoodBreadBase
  abstract: true
  components:
  - type: FlavorProfile
    flavors:
      - bread
  - type: Food
  - type: Sprite
    sprite: Objects/Consumable/Food/Baked/bread.rsi
  - type: Tag
    tags:
    - Bread
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 26
        reagents:
        - ReagentId: Nutriment
          Quantity: 20

- type: entity
  parent: FoodBreadBase
  id: FoodBreadSliceBase
  abstract: true
  components:
  - type: Item
    size: Tiny
  - type: FlavorProfile
    flavors:
      - bread
  - type: Tag
    tags:
    - Bread
    - Slice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 8
        reagents:
        - ReagentId: Nutriment
          Quantity: 4

# Custom Bread Example

- type: entity
  name: "вулканічний батон"
  parent: FoodBreadBase
  id: FoodBreadVolcanic
  description: "Темний буханець. Нагадує пемзу."
  components:
  - type: Food
  - type: Sprite
    layers:
    - state: alpha
      color: "#281415"
    - state: alpha-filling
      color: "#FF613F"
  - type: SliceableFood
    slice: FoodBreadVolcanicSlice

- type: entity
  name: "вулканічний зріз"
  parent: FoodBreadSliceBase
  id: FoodBreadVolcanicSlice
  description: "Шматок темного батона. Нагадує пемзу."
  components:
  - type: Food
  - type: Sprite
    layers:
    - state: alpha-slice
      color: "#281415"
    - state: alpha-slice-filling
      color: "#FF613F"

# Bread

- type: entity
  name: "банановий хліб"
  parent: FoodBreadBase
  id: FoodBreadBanana
  description: "Небесне і ситне частування."
  components:
  - type: FlavorProfile
    flavors:
      - banana
      - bread
  - type: Food
  - type: Sprite
    layers:
    - state: banana
  - type: SliceableFood
    slice: FoodBreadBananaSlice
  - type: Tag
    tags:
    - Fruit
    - Bread
# Tastes like bread, banana, nut.

- type: entity
  name: "шматочок бананового хліба"
  parent: FoodBreadSliceBase
  id: FoodBreadBananaSlice
  description: "Шматочок смачного бананового хліба."
  components:
  - type: FlavorProfile
    flavors:
      - banana
      - bread
  - type: Food
  - type: Sprite
    layers:
    - state: banana-slice
  - type: Tag
    tags:
    - Fruit
    - Bread
    - Slice

- type: entity
  name: "кукурудзяний хліб"
  parent: FoodBreadBase
  id: FoodBreadCorn
  description: "Трохи хорошого кантрі-стилю, з корінням, стріляниною з револьверів, кукурудзяним хлібом \"тато-гум\"."
  components:
  - type: FlavorProfile
    flavors:
      - bread
      - corn
  - type: Food
  - type: Sprite
    layers:
    - state: cornbread
  - type: SliceableFood
    slice: FoodBreadCornSlice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Nutriment
          Quantity: 15
# Tastes like bread, banana, nut.

- type: entity
  name: "шматочок кукурудзяного хліба"
  parent: FoodBreadSliceBase
  id: FoodBreadCornSlice
  description: "Шматочок кукурудзяного хліба."
  components:
  - type: FlavorProfile
    flavors:
      - bread
      - corn
  - type: Food
  - type: Sprite
    layers:
    - state: cornbread-slice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 5
        reagents:
        - ReagentId: Nutriment
          Quantity: 3

- type: entity
  name: "хліб з вершковим сиром"
  parent: FoodBreadBase
  id: FoodBreadCreamcheese
  description: "Ням-ням-ням!"
  components:
  - type: FlavorProfile
    flavors:
      - cheesy
      - creamy
      - bread
  - type: Food
  - type: Sprite
    layers:
    - state: creamcheese
  - type: SliceableFood
    slice: FoodBreadCreamcheeseSlice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 35
        reagents:
        - ReagentId: Nutriment
          Quantity: 20
        - ReagentId: Vitamin
          Quantity: 5
# Tastes like bread, cheese.

- type: entity
  name: "скибочка хліба з вершковим сиром"
  parent: FoodBreadSliceBase
  id: FoodBreadCreamcheeseSlice
  description: "Шматочок смакоти!"
  components:
  - type: FlavorProfile
    flavors:
      - cheesy
      - creamy
      - bread
  - type: Food
  - type: Sprite
    layers:
    - state: creamcheese-slice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 4
        - ReagentId: Vitamin
          Quantity: 1.2

- type: entity
  name: "м'ясний хліб"
  parent: FoodBreadBase
  id: FoodBreadMeat
  description: "Кулінарна база кожного поважаючого себе красномовця/талановитого/джентльмена."
  components:
  - type: FlavorProfile
    flavors:
      - meaty
      - bread
  - type: Food
  - type: Sprite
    layers:
    - state: meat
  - type: SliceableFood
    slice: FoodBreadMeatSlice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 45
        reagents:
        - ReagentId: Nutriment
          Quantity: 30
        - ReagentId: Vitamin
          Quantity: 5
  - type: Tag
    tags:
    - Meat
    - Bread
# Tastes like bread, meat.

- type: entity
  name: "м'ясна скибочка хліба"
  parent: FoodBreadSliceBase
  id: FoodBreadMeatSlice
  description: "Шматочок смачного м'ясного хліба."
  components:
  - type: FlavorProfile
    flavors:
      - meaty
      - bread
  - type: Food
  - type: Sprite
    layers:
    - state: meat-slice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 1.2
  - type: Tag
    tags:
    - Meat
    - Bread
    - Slice

- type: entity
  name: "хліб мімана"
  parent: FoodBreadBase
  id: FoodBreadMimana
  description: "Найкраще їсти в тиші."
  components:
  - type: FlavorProfile
    flavors:
      - nothing
      - bread
  - type: Food
  - type: Sprite
    layers:
    - state: mimana
  - type: SliceableFood
    slice: FoodBreadMimanaSlice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 40
        reagents:
        - ReagentId: Nutriment
          Quantity: 15
        - ReagentId: Vitamin
          Quantity: 5
        - ReagentId: Nothing
          Quantity: 5
        - ReagentId: MuteToxin
          Quantity: 5
# Tastes like bread, cheese.

- type: entity
  name: "скибочка хліба мімана"
  parent: FoodBreadSliceBase
  id: FoodBreadMimanaSlice
  description: "Шматочок тиші!"
  components:
  - type: FlavorProfile
    flavors:
      - nothing
      - bread
  - type: Food
  - type: Sprite
    layers:
    - state: mimana-slice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
        - ReagentId: Vitamin
          Quantity: 1
        - ReagentId: Nothing
          Quantity: 1
        - ReagentId: MuteToxin
          Quantity: 1

- type: entity
  name: "хліб"
  parent: FoodBreadBase
  id: FoodBreadPlain
  description: "Простого старого земляного хліба."
  components:
  - type: Food
  - type: Sprite
    layers:
    - state: plain
  - type: SliceableFood
    slice: FoodBreadPlainSlice

- type: entity
  name: "скибочка хліба"
  parent: FoodBreadSliceBase
  id: FoodBreadPlainSlice
  description: "Шматочок дому."
  components:
  - type: Food
  - type: Sprite
    layers:
    - state: plain-slice

- type: entity
  name: "ковбасний хліб"
  parent: FoodBreadBase
  id: FoodBreadSausage
  description: "Не думайте про це занадто багато."
  components:
  - type: FlavorProfile
    flavors:
      - meaty
      - bread
  - type: Food
  - type: Sprite
    layers:
    - state: sausage
  - type: SliceableFood
    slice: FoodBreadSausageSlice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 45
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
        - ReagentId: Vitamin
          Quantity: 5
        - ReagentId: Protein
          Quantity: 5
  - type: Tag
    tags:
    - Meat
    - Bread

- type: entity
  name: "скибочка хліба з ковбасою"
  parent: FoodBreadSliceBase
  id: FoodBreadSausageSlice
  description: "Не думайте про це занадто багато."
  components:
  - type: FlavorProfile
    flavors:
      - meaty
      - bread
  - type: Food
  - type: Sprite
    layers:
    - state: sausage-slice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 2
        - ReagentId: Vitamin
          Quantity: 1
        - ReagentId: Protein
          Quantity: 1
  - type: Tag
    tags:
    - Meat
    - Bread
    - Slice

- type: entity
  name: "хліб з павукового м'яса"
  parent: FoodBreadBase
  id: FoodBreadMeatSpider
  description: "Заспокійливо-зелений м'ясний рулет з м'яса павука."
  components:
  - type: FlavorProfile
    flavors:
      - cobwebs
      - bread
  - type: Food
  - type: Sprite
    layers:
    - state: spidermeat
  - type: SliceableFood
    slice: FoodBreadMeatSpiderSlice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 45
        reagents:
        - ReagentId: Nutriment
          Quantity: 30
        - ReagentId: Vitamin
          Quantity: 5
  - type: Tag
    tags:
    - Meat
    - Bread
# Tastes like bread, cobwebs.

- type: entity
  name: "хлібна скибочка з м'ясом павука"
  parent: FoodBreadSliceBase
  id: FoodBreadMeatSpiderSlice
  description: "Шматок м'ясного рулету, зроблений з тварини, яка, швидше за все, все ще хоче вашої смерті."
  components:
  - type: FlavorProfile
    flavors:
      - cobwebs
      - bread
  - type: Food
  - type: Sprite
    layers:
    - state: spidermeat-slice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 12
        reagents:
        - ReagentId: Nutriment
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 1.2
  - type: Tag
    tags:
    - Meat
    - Bread
    - Slice

- type: entity
  name: "хліб з тофу"
  parent: FoodBreadBase
  id: FoodBreadTofu
  description: "Як м'ясний хліб, але для вегетаріанців. Похваліться своїм товаришам по команді, наскільки він кращий."
  components:
  - type: FlavorProfile
    flavors:
      - tofu
      - bread
  - type: Food
  - type: Sprite
    layers:
    - state: tofu
  - type: SliceableFood
    slice: FoodBreadTofuSlice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 48
        reagents:
        - ReagentId: Nutriment
          Quantity: 30
        - ReagentId: Protein
          Quantity: 12
# Tastes like bread, tofu.

- type: entity
  name: "скибочка хліба з тофу"
  parent: FoodBreadSliceBase
  id: FoodBreadTofuSlice
  description: "Шматочок смачного хліба з тофу."
  components:
  - type: FlavorProfile
    flavors:
      - tofu
      - bread
  - type: Food
  - type: Sprite
    layers:
    - state: tofu-slice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 2.4

- type: entity
  name: "ксеном'ясний хліб"
  parent: FoodBreadBase
  id: FoodBreadMeatXeno
  description: "Пристосування та заповнення, кінець ксеношкідництву."
  components:
  - type: FlavorProfile
    flavors:
      - acid
      - bread
  - type: Food
  - type: Sprite
    layers:
    - state: xenomeat
  - type: SliceableFood
    slice: FoodBreadMeatXenoSlice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 45
        reagents:
        - ReagentId: Nutriment
          Quantity: 30
        - ReagentId: Vitamin
          Quantity: 5
  - type: Tag
    tags:
    - Meat
    - Bread
# Tastes like bread, acid.

- type: entity
  name: "хлібна скибочка з ксеном'яса"
  parent: FoodBreadSliceBase
  id: FoodBreadMeatXenoSlice
  description: "Шматок ксеношмаття."
  components:
  - type: FlavorProfile
    flavors:
      - acid
      - bread
  - type: Food
  - type: Sprite
    layers:
    - state: xenomeat-slice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 1.2
  - type: Tag
    tags:
    - Meat
    - Bread
    - Slice

# Other than bread/slices

- type: entity
  name: "багет"
  parent: FoodBreadBase
  id: FoodBreadBaguette
  description: "Смачного!"
  components:
  - type: Sprite
    state: baguette
  - type: SliceableFood
    count: 12
    slice: FoodBreadBaguetteSlice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 1
        - ReagentId: TableSalt
          Quantity: 1
        - ReagentId: Blackpepper
          Quantity: 1
  - type: Clothing
    slots: [ BELT ]
    equippedPrefix: baguette
    quickEquip: false
  - type: Item
    inhandVisuals:
      left:
      - state: baguette-inhand-left
      right:
      - state: baguette-inhand-right
  - type: MeleeWeapon
    wideAnimationRotation: -120
    damage:
      types:
        Blunt: 1 # bonk
# Tastes like France.

- type: entity
  name: "кростіні"
  parent: FoodBreadSliceBase
  id: FoodBreadBaguetteSlice
  description: "Бон аппетит!"
  components:
  - type: Sprite
    state: crostini
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 2
        reagents:
        - ReagentId: Nutriment
          Quantity: 0.5
        - ReagentId: Vitamin
          Quantity: 0.1
        - ReagentId: TableSalt
          Quantity: 0.1
        - ReagentId: Blackpepper
          Quantity: 0.1

- type: entity
  name: "тост з маслом"
  parent: FoodBreadSliceBase
  id: FoodBreadButteredToast
  description: "Хрусткий."
  components:
  - type: FlavorProfile
    flavors:
      - butter
      - bread
  - type: Sprite
    state: buttered-toast
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 5
        - ReagentId: Vitamin
          Quantity: 1
# Tastes like bread, butter.

- type: entity
  name: "французький тост"
  parent: FoodBreadSliceBase
  id: FoodBreadFrenchToast
  description: "Скибочку хліба вмочити у збиту яєчну суміш."
  components:
  - type: FlavorProfile
    flavors:
      - syrupy
      - bread
  - type: Sprite
    state: french-toast
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 4
        - ReagentId: Vitamin
          Quantity: 2
# Tastes like bread, butter.

- type: entity
  name: "часниковий хліб"
  parent: FoodBreadSliceBase
  id: FoodBreadGarlicSlice
  description: "На жаль, вона обмежена."
  components:
  - type: FlavorProfile
    flavors:
      - bread
      - garlic
  - type: Sprite
    layers:
    - state: garlic-slice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 6
        - ReagentId: Vitamin
          Quantity: 5
# Tastes like garlic, Italy.

- type: entity
  name: "желейні тости"
  parent: FoodBreadSliceBase
  id: FoodBreadJellySlice
  description: "Ніби епістемологи збираються віддати свій слиз на тост!" # DeltaV - Epistemics Department replacing Science
  components:
  - type: FlavorProfile
    flavors:
      - bread
      - slimy
  - type: Sprite
    layers:
    - state: jelly-toast
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 4
        - ReagentId: Vitamin
          Quantity: 4
# Tastes like garlic, Italy.

- type: entity
  name: "запліснявіла скибка хліба"
  parent: FoodBreadSliceBase
  id: FoodBreadMoldySlice
  description: "Цілі станції були розірвані на шматки, сперечаючись, чи це все ще можна їсти."
  components:
  - type: Sprite
    layers:
    - state: moldy-slice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 4
        - ReagentId: Mold
          Quantity: 7
# Tastes like decaying fungus.

- type: entity
  name: "два шматочка"
  parent: FoodBreadSliceBase
  id: FoodBreadTwoSlice
  description: "Шикарно."
  components:
  - type: Sprite
    layers:
    - state: two-slice
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
        - ReagentId: Vitamin
          Quantity: 2
        - ReagentId: Wine
          Quantity: 5
# Tastes like decaying fungus.

- type: entity
  name: "хлібна собака"
  id: MobBreadDog
  parent: FoodBreadSausage
  description: "Це хліб. Це собака. Це... хлібний пес?"
  components:
  - type: Sprite
    noRot: true
    drawdepth: Mobs
    sprite: Mobs/Pets/breaddog.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: base
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 50
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: RotationVisuals
    defaultRotation: 0
    horizontalRotation: 0
  - type: Item
    size: Ginormous
  - type: GhostRole
    prob: 1
    name: ghost-role-information-BreadDog-name
    allowMovement: true
    description: ghost-role-information-BreadDog-description
    rules: ghost-role-information-BreadDog-rules
    raffle:
      settings: short
  - type: GhostTakeoverAvailable
  - type: BarkAccent
  - type: Speech
    speechSounds: Dog
    speechVerb: SmallMob
  - type: MovementSpeedModifier
    baseWalkSpeed : 5
    baseSprintSpeed : 3
  - type: Tag
    tags:
    - VimPilot
    - DoorBumpOpener
    - Bread
  - type: CanEscapeInventory
    baseResistTime: 2
  - type: Puller
    needsHands: false
  - type: Examiner
  - type: DoAfter
  - type: CombatMode
  - type: MeleeWeapon
    soundHit:
      path: /Audio/Animals/dog_bark1.ogg
    hidden: true
    damage:
      groups:
        Brute: 1
