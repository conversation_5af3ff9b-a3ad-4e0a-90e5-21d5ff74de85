- type: entity
  id: ClosetToolFilled
  parent: ClosetTool
  suffix: Filled
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterVestHazard
        prob: 0.4
      - id: FlashlightLantern
        prob: 0.7
      - id: Screwdriver
        prob: 0.7
      - id: Wrench
        prob: 0.7
      - id: Welder
        prob: 0.7
      - id: Crowbar
        prob: 0.7
      - id: Wirecutter
        prob: 0.7
      - id: Multitool
        prob: 0.2
      - id: trayScanner
        prob: 0.7
      - id: GasAnalyzer
        prob: 0.7
      - id: ClothingBeltUtility
        prob: 0.2
      - id: ClothingHandsGlovesColorYellow
        prob: 0.05
      - id: ClothingOuterJacketInsulated
        prob: 0.05
      - id: ClothingHeadHatHardhatRed
        prob: 0.4
      - id: CableApcStack
        prob: 0.3
      - id: CableApcStack
        prob: 0.3
      - id: CableApcStack
        prob: 0.3
      - id: SprayPainter
        prob: 0.7

- type: entity
  id: LockerElectricalSuppliesFilled
  suffix: Filled
  parent: LockerElectricalSupplies
  components:
  - type: StorageFill
    contents:
      - id: ToolboxElectricalFilled
        prob: 0.7
      - id: FirelockElectronics
        prob: 0.05
      - id: APCElectronics
        prob: 0.1
      - id: CableMVStack
        prob: 0.2
      - id: CableApcStack
        prob: 0.3

- type: entity
  id: LockerWeldingSuppliesFilled
  suffix: Filled
  parent: LockerWeldingSupplies
  components:
  - type: StorageFill
    contents:
      - id: ClothingHeadHatWelding
      - id: ClothingHeadHatWelding
      - id: ClothingHeadHatWelding
        prob: 0.5
      - id: Welder
      - id: Welder
      - id: WelderMini
        orGroup: thirdWelder
      - id: Welder
        prob: 0.33
        orGroup: thirdWelder
      - id: WelderIndustrial
        prob: 0.33
        orGroup: thirdWelder

- type: entity
  id: LockerAtmosphericsFilledHardsuit
  suffix: Filled, Hardsuit
  parent: LockerAtmospherics
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterHardsuitAtmos
      - id: ClothingMaskGasAtmos
      - id: ClothingOuterSuitAtmosFire
      - id: ClothingHeadHelmetAtmosFire
      - id: GasAnalyzer
      - id: MedkitOxygenFilled
      - id: HolofanProjector
      - id: RPD #funkystation
      - id: DoorRemoteFirefight
      - id: RCD
      - id: RCDAmmo
      - id: RPD
      - id: RCDAmmo # Extra Compressed matter for the RPD
      - id: LunchboxEngineeringFilledRandom # Delta-V Lunchboxes!
        prob: 0.3
      - id: AccessConfigurator

- type: entity
  id: LockerAtmosphericsFilled
  suffix: Filled
  parent: LockerAtmospherics
  components:
  - type: StorageFill
    contents:
      - id: ClothingMaskGasAtmos
      - id: ClothingOuterSuitAtmosFire
      - id: ClothingHeadHelmetAtmosFire
      - id: GasAnalyzer
      - id: MedkitOxygenFilled
      - id: HolofanProjector
      - id: DoorRemoteFirefight
      - id: RPD #funkystation
      - id: RCD
      - id: RCDAmmo
      - id: RPD
      - id: RCDAmmo # Extra Compressed matter for the RPD
      - id: LunchboxEngineeringFilledRandom # Delta-V Lunchboxes!
        prob: 0.3
      - id: AccessConfigurator

- type: entity
  id: LockerEngineerFilledHardsuit
  suffix: Filled, Hardsuit
  parent: LockerEngineer
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterHardsuitEngineering
      - id: ClothingHandsGlovesColorYellow
      - id: ClothingMaskGas
      - id: ClothingShoesBootsMagEng
      - id: RCD
      - id: RCDAmmo
      - id: LunchboxEngineeringFilledRandom # Delta-V Lunchboxes!
        prob: 0.3
      - id: AccessConfigurator

- type: entity
  id: LockerEngineerFilled
  suffix: Filled
  parent: LockerEngineer
  components:
  - type: StorageFill
    contents:
      - id: ClothingHandsGlovesColorYellow
      - id: ClothingOuterJacketInsulated
      - id: ClothingMaskGas
      - id: trayScanner
      - id: RCD
      - id: RCDAmmo
      - id: LunchboxEngineeringFilledRandom # Delta-V Lunchboxes!
        prob: 0.3
      - id: AccessConfigurator

- type: entity
  id: ClosetRadiationSuitFilled
  parent: ClosetRadiationSuit
  suffix: Filled
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterSuitRad
        amount: 2
      - id: GeigerCounter
        amount: 2
