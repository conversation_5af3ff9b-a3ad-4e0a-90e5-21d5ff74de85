- type: entity
  parent: SeatBase
  id: FitnessWeightsBench1
  name: "гирьова лавка"
  components:
  - type: Transform
    anchored: true
  - type: Physics
    bodyType: Static
  - type: Anchorable
  - type: Rotatable
  - type: Sprite
    sprite: Structures/Specific/fitness.rsi
    state: fitnessweight

- type: entity
  parent: FitnessWeightsBench1
  id: FitnessWeightLifter
  name: "штанга"
  components:
  - type: Sprite
    sprite: Structures/Specific/fitness.rsi
    state: fitnesslifter


# Bags
# TODO: Need a way to change to animation state on trigger in world (click) or use damage state visualiser and instant healing to change state on attack.
#soundOnTrigger ?
#TriggerOnActivate ?
#TriggerOnCollide ?

- type: entity
  id: FitnessPunchingBagBopClown
  parent: BaseStructure
  name: "клоунська боп-сумка"
  description: "Сумка для фітнес-тренувань із зображенням клоуна."
  components:
  - type: Sprite
    sprite: Structures/Specific/fitness.rsi
    state: bopbag

- type: entity
  id: FitnessPunchingBag
  parent: BaseStructure
  name: "боксерська груша"
  description: "Фітнес-сумка для тренувань."
  components:
  - type: Sprite
    sprite: Structures/Specific/fitness.rsi
    state: punchingbag

- type: entity
  id: FitnessPunchingBagCaptain
  parent: FitnessPunchingBag
  suffix: captain
  description: "Сумка для фітнес-тренувань з надрукованим на ній капітаном."
  components:
  - type: Sprite
    sprite: Structures/Specific/fitness.rsi
    state: punchingbagcaptain

- type: entity
  id: FitnessPunchingBagSyndicate
  parent: FitnessPunchingBag
  suffix: syndicate
  description: "Сумка для фітнес-тренувань, на якій надруковано агента синдикату."
  components:
  - type: Sprite
    sprite: Structures/Specific/fitness.rsi
    state: punchingbagsyndie

- type: entity
  id: FitnessPunchingBagWizard
  parent: FitnessPunchingBag
  suffix: wizard
  description: "Сумка для фітнес-тренувань з надрукованим на ній майстром."
  components:
  - type: Sprite
    sprite: Structures/Specific/fitness.rsi
    state: punchingbagwizard
