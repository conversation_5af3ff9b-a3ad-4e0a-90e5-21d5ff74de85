# This is a basic dictionary that maps old entity prototype ids to new ids. This only works for entity prototypes, and
# is intended to allow maps to load without having to manually edit them. An empty or "null" string results in the
# entity getting deleted.

# e.g., you can swap all walls with windows and delete all tables by adding lines like:
#
# Window: WallSolid
# WallSolid: Window
# Table: null

# 2023-07-03
ClothingHeadHelmetHelmet: ClothingHeadHelmetBasic
ClothingHeadHelmetHelmetOld: ClothingHeadHelmetBasic

# 2023-07-04
# Bulletproof armor is almost statistically identical to kevlar, however, before this kevlar armor was the closest thing there was to "basic" armor. It makes the most sense to replace it with this.
ClothingOuterVestKevlar: ClothingOuterArmorBasic

# 2023-07-10
Intercom: IntercomCommon

# 2023-07-12
ToyAssistant: ToyFigurinePassenger

# 2023-07-20
ForensicGloves: ClothingHandsGlovesForensic

# 2023-07-24
ClothingEyesGlassesBeer: ClothingEyesHudBeer

# 2023-08-01
lantern: Lantern
lanternextrabright: LanternFlash

# 2023-08-04
BoxMagazineLightRifleHighVelocity: BoxMagazineLightRifle
BoxMagazineMagnumSubMachineGunHighVelocity: BoxMagazineMagnumSubMachineGun
BoxMagazinePistolCaselessRifleHighVelocity: BoxMagazinePistolCaselessRifle
BoxMagazinePistolHighCapacityHighVelocity: BoxMagazinePistolHighCapacity
BoxMagazinePistolHighVelocity: BoxMagazinePistol
BoxMagazinePistolSubMachineGunHighVelocity: BoxMagazinePistolSubMachineGun
BoxMagazineRifleHighVelocity: BoxMagazineRifle
BulletCaselessRifleHighVelocity: BulletCaselessRifle
BulletLightRifleHighVelocity: BulletLightRifle
BulletMagnumHighVelocity: BulletMagnum
BulletPistolHighVelocity: BulletPistol
BulletRifleHighVelocity: BulletRifle
CartridgeCaselessRifleHighVelocity: CartridgeCaselessRifle
CartridgeLightRifleHighVelocity: CartridgeLightRifle
CartridgeMagnumHighVelocity: CartridgeMagnum
CartridgePistolHighVelocity: CartridgePistol
CartridgeRifleHighVelocity: CartridgeRifle
MagazineBoxCaselessRifleHighVelocity: MagazineBoxCaselessRifle
MagazineBoxLightRifleHighVelocity: MagazineBoxLightRifle
MagazineBoxMagnumHighVelocity: MagazineBoxMagnum
MagazineBoxPistolHighVelocity: MagazineBoxPistol
MagazineBoxRifleHighVelocity: MagazineBoxRifle
MagazineCaselessRifleHighVelocity: MagazineCaselessRifle
MagazineCaselessRifleShortHighVelocity: MagazineCaselessRifleShort
MagazineLightRifleHighVelocity: MagazineLightRifle
MagazineMagnumSubMachineGunHighVelocity: MagazineMagnumSubMachineGun
MagazinePistolCaselessRifleHighVelocity: MagazinePistolCaselessRifle
MagazinePistolHighCapacityHighVelocity: MagazinePistolHighCapacity
MagazinePistolHighVelocity: MagazinePistol
MagazinePistolSubMachineGunHighVelocity: MagazinePistolSubMachineGun
MagazineRifleHighVelocity: MagazineRifle
SpeedLoaderMagnumHighVelocity: SpeedLoaderMagnum
SpeedLoaderPistolHighVelocity: SpeedLoaderPistol
#
# 2023-08-07
#If the name is anything to go off of, these are presumably just CEV-Eris versions of the snow rock (which we already have.)
#They are practically never used in this way however, so they're migrated to the basic rock type.
#MountainRock: AsteroidRock #DeltaV
#MountainRockMining: AsteroidRockMining #DeltaV

# 2023-08-08
#WindowTintedDirectional: WindowFrostedDirectional # DeltaV

# 2023-08-10
SyringeSpaceacillin: null

# 2023-08-13
AirlockPainter: SprayPainter

# 2023-08-19
GeneratorPlasma: PortableGeneratorPacman
GeneratorUranium: PortableGeneratorSuperPacman
GeneratorPlasmaMachineCircuitboard: PortableGeneratorPacmanMachineCircuitboard
GeneratorUraniumMachineCircuitboard: PortableGeneratorSuperPacmanMachineCircuitboard

# 2023-12-12
#No this is not the CMO hardsuit, their prototype IDs were just confusingly similar
ClothingOuterHardsuitMedic: ClothingOuterHardsuitSyndieMedic

# 2023-12-13
VendingMachineSmartFridge: SmartFridge

# 2023-12-18
ReagentContainerMilk: DrinkMilkCarton
ReagentContainerMilkSoy: DrinkSoyMilkCarton
ReagentContainerMilkOat: DrinkOatMilkCarton

# 2023-12-20
MiasmaCanister: AmmoniaCanister

# 2023-12-28
WarpPointBeaconBar: null
WarpPointBeaconCargo: null
WarpPointBeaconCommand: null
WarpPointBeaconEngineering: null
WarpPointBeaconMedical: null
WarpPointBeaconNeutral: null
WarpPointBeaconScience: null
WarpPointBeaconSecurity: null
WarpPointBeaconService: null

# 2023-12-30
ClothingEyesGlassesCosmeticSunglasses: ClothingEyesGlassesSunglasses
SpawnPointAssistant: SpawnPointPassenger

# 2024-01-05
DrinkGoldschlagerBottleFull: DrinkGildlagerBottleFull
DrinkGoldschlagerGlass: DrinkGildlagerGlass

# 2024-01-07
ClosetBase: ClosetSteelBase
MonkeyCubeBox: VariantCubeBox

# 2024-01-10
ClothingHeadHatHoodRad: null

# 2024-01-18
ClothingHeadHelmetVoidParamed: null

# 2024-01-19
DefaultStationBeaconTeslaEngine: null

# 2024-01-28
FoodBoxDonkpocketGondola: FoodBoxDonkpocketPizza

# 2024-01-31
#SpawnVehicleWheelchair: null Goobstation - Re-add vehicles
#SpawnVehicleWheelchairFolded: null Goobstation - Re-add vehicles
#VehicleWheelchair: null Goobstation - Re-add vehicles
#VehicleWheelchairFolded: null Goobstation - Re-add vehicles
VehicleSecwayStealObjective: null
#VehicleKeyJanicart: null Goobstation - Re-add vehicles
#VehicleKeySecway: null Goobstation - Re-add vehicles
#VehicleKeyATV: null Goobstation - Re-add vehicles
VehicleKeySkeleton: null
#VehicleKeySyndicateSegway: null Goobstation - Re-add vehicles
VehicleKeySkeletonMotorcycle: null
#VehicleSecway: null Goobstation - Re-add vehicles
#VehicleATV: null Goobstation - Re-add vehicles
#VehicleSyndicateSegway: null Goobstation - Re-add vehicles
VehicleSkeletonMotorcycle: null
VehicleUnicycle: null
VehicleUnicycleFolded: null
ActionVehicleHorn: null
#CrateFunATV: null Goobstation - Re-add vehicles
#CrateFunSyndicateSegway: null Goobstation - Re-add vehicles
MobTaxiBot: null
# MobSupplyBot: null
SpawnVehicleMotobike: null
SpawnVehicleATV: null
#SpawnVehicleSecway: null Goobstation - Re-add vehicles
#SpawnVehicleJanicart: null Goobstation - Re-add vehicles
#VehicleJanicart: null Goobstation - Re-add vehicles
VehicleJanicartDestroyed: null

# 2024-02-01
YellowOxygenTank: OxygenTank
YellowOxygenTankFilled: OxygenTankFilled

# 2024-02-19
Onestar: null # I dont think this is even mapped, but just in case

# 2024-02-22
SolarAssemblyPart: SolarAssemblyFlatpack
AmePart: AmePartFlatpack
AmePartStealObjective: AmePartFlatpackStealObjective

# 2024-02-26
CrateBaseWeldable: CrateGenericSteel

# 2024-03-05
BookBotanicalTextbook: BookRandomStory
BookEscalation: BookRandomStory
BookEscalationSecurity: BookRandomStory
BookDemonomiconRandom: BookRandomStory
BookDemonomicon1: BookRandomStory
BookDemonomicon2: BookRandomStory
BookDemonomicon3: BookRandomStory
BookChemistryInsane: BookRandomStory
BookGnominomicon: BookRandomStory
BookFishing: BookRandomStory
BookDetective: BookRandomStory

# 2024-03-07
AirlockExternalEasyPry: AirlockExternal
AirlockExternalGlassEasyPry: AirlockExternalGlass
AirlockGlassShuttleEasyPry: AirlockGlassShuttle
AirlockShuttleEasyPry: AirlockShuttle
AirlockExternalEasyPryLocked: AirlockExternalLocked
AirlockExternalGlassEasyPryLocked: AirlockExternalGlassLocked
AirlockGlassShuttleEasyPryLocked: AirlockExternalGlassShuttleLocked
AirlockShuttleEasyPryLocked: AirlockExternalShuttleLocked

# 2024-03-10
ClothingBackpackFilledDetective: ClothingBackpackSecurityFilledDetective
ClothingBackpackDuffelFilledDetective: ClothingBackpackDuffelSecurityFilledDetective
ClothingBackpackSatchelFilledDetective: ClothingBackpackSatchelSecurityFilledDetective
FoodChili: FoodChiliPepper
FoodChilly: FoodChillyPepper

# 2024-03-16
ClothingHeadHatHairflower: FoodPoppy

# 2024-03-21
RPED: null

# 2024-03-30
TraversalDistorterMachineCircuitboard: null
MachineTraversalDistorter: null
# These are technically not equivalent, but it probably makes more sense to replace any existing SCAF stuff with SOME kind of armor, instead of just deleting it outright.
ClothingHeadHelmetScaf: ClothingHeadHelmetBasic
ClothingOuterArmorScaf: ClothingOuterArmorBasic

# 2024-03-31
ClothingNeckFlowerWreath: ClothingHeadHatFlowerWreath
ClothingHeadHatFlowerCrown: ClothingHeadHatFlowerWreath
BriefcaseSyndieBase: null

# 2024-04-08
BodyBag_Container: BodyBag
BodyBag_Folded: BodyBagFolded

# 2024-04-26
GlassBoxLaserBroken: GlassBoxBroken
ReinforcementRadioSyndicateMonkey: ReinforcementRadioSyndicateAncestor
ReinforcementRadioSyndicateMonkeyNukeops: ReinforcementRadioSyndicateAncestorNukeops

# 2024-05-01
DrinkBottleGoldschlager: DrinkBottleGildlager

# 2024-05-14
soda_dispenser: SodaDispenser
chem_master: ChemMaster

# 2024-05-21
CrateJanitorExplosive: ClosetJanitorBombFilled

# 2024-05-27
DoorRemoteFirefight: null

# 2024-06-03
AirlockServiceCaptainLocked: AirlockCaptainLocked

# 2024-06-15
ClothingOuterCoatInspector: ClothingOuterCoatDetectiveLoadout

# 2024-06-23
FloorTileItemReinforced: PartRodMetal1


#2024-06-25
BookChefGaming: BookHowToCookForFortySpaceman

#2024-06-29
IntercomAssesmbly: IntercomAssembly

# 2024-07-7
SignScience1: SignScience
SignScience2: SignScience
SignXenobio2: SignXenobio
SignXenolab: SignXenobio
SignToxins2: SignToxins
SignMinerDock: SignShipDock
SignChemistry1: SignChem
SignChemistry2: SignChem
SignCourt: SignLawyer
SignAtmosMinsky: SignAtmos
SignDrones: SignMaterials
SignShield: null # what was this even for?
SignHydro2: SignHydro1
SignHydro3: SignHydro1

# 2024-07-27
LogicGate: LogicGateOr

# 2024-08-11
FoodTacoBeef: FoodTacoShell
FoodTacoChicken: FoodTacoShell
FoodTacoFish: FoodTacoShell
FoodTacoBeefSupreme: FoodTacoShell
FoodTacoChickenSupreme: FoodTacoShell
FoodTacoRat: FoodTacoShell

FoodMeatHumanKebab: FoodKebabSkewer
FoodMeatLizardtailKebab: FoodKebabSkewer
FoodMeatRatKebab: FoodKebabSkewer
FoodMeatRatdoubleKebab: FoodKebabSkewer
FoodMeatSnakeKebab: FoodKebabSkewer
FoodMeatHawaiianKebab: FoodKebabSkewer
FoodMeatKebab: FoodKebabSkewer
FoodMeatFiestaKebab: FoodKebabSkewer

# 2024-10-09
# Removal of separate borg chassis parts, replace them with generic borg parts.
LeftArmBorgEngineer: LeftArmBorg
RightArmBorgEngineer: RightArmBorg
LeftLegBorgEngineer: LeftLegBorg
RightLegBorgEngineer: RightLegBorg
HeadBorgEngineer: LightHeadBorg
TorsoBorgEngineer: TorsoBorg

LeftArmBorgMedical: LeftArmBorg
RightArmBorgMedical: RightArmBorg
LeftLegBorgMedical: LeftLegBorg
RightLegBorgMedical: RightLegBorg
HeadBorgMedical: LightHeadBorg
TorsoBorgMedical: TorsoBorg

LeftArmBorgMining: LeftArmBorg
RightArmBorgMining: RightArmBorg
LeftLegBorgMining: LeftLegBorg
RightLegBorgMining: RightLegBorg
HeadBorgMining: LightHeadBorg
TorsoBorgMining: TorsoBorg

LeftArmBorgService: LeftArmBorg
RightArmBorgService: RightArmBorg
LeftLegBorgService: LeftLegBorg
RightLegBorgService: RightLegBorg
HeadBorgService: LightHeadBorg
TorsoBorgService: TorsoBorg

LeftLegBorgJanitor: LeftLegBorg
RightLegBorgJanitor: RightLegBorg
HeadBorgJanitor: LightHeadBorg
TorsoBorgJanitor: TorsoBorg

# 2024-03-11
ImprovisedExplosive: FireBomb
ImprovisedExplosiveEmpty: FireBombEmpty
ImprovisedExplosiveFuel: FireBombFuel

# 2025-03-02
WeaponSubMachineGunVectorRubber: null
WeaponSubMachineGunVector: null

# 2025-03-05
PosterLegitEnlist: null
PosterLegitNoErp: null
PosterContrabandEnlistGorlex: null

# 2025-04-26
WeaponTetherGun: null
WeaponForceGun: null
WeaponGrapplingGun: null
BorgModuleGrapplingGun: null
WeaponForceGunAdmin: null
WeaponTetherGunAdmin: null

# Lavaland EE stuff
TreasureDatadiskEncrypted: null
VendingMachineBoozeSyndicate: VendingMachineBooze
ClothingOuterHardsuitSyndicateDurathread: ClothingOuterHardsuitSyndicate
PoweredDimSmallLight: PoweredSmallLight
PosterLegitGoobFaded: null
ClothingOuterWinterColorBlue: ClothingOuterWinterMed
ClothingOuterWinterColorBrown: ClothingOuterWinterHydro
MineralScanner: null
AdvancedMineralScanner: null
FoamThrongler: PlushieThrongler
GlueGrenade: null
LubeGrenade: CleanerGrenade
PlushieBlackburn: null # Waiting for port
PlushieBlackburnMatryoskya: null
PlushieDesislavaBlackburn: null
PlushieDurk: null
PlushieLouie: null
PlushieRainbowLizard: null
SoapHomemadeBanana: Soap

BoxAgrichem: null
portableChemicalMixer: null

WallRockBasaltBSCrystal: WallRockBasaltBluespace
AirlockHydroponics: AirlockHydroponicsLocked
Biogenerator: null
FloraGreyStalagmite: null
FelinidCube: TajaranCube


