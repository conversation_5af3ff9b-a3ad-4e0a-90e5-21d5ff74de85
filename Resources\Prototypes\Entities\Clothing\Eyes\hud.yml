- type: entity
  id: ShowSecurityIcons
  abstract: true
  categories: [ HideSpawnMenu ]
  components:
  - type: ShowJobIcons
  - type: ShowMindShieldIcons
  - type: ShowCriminalRecordIcons

- type: entity
  id: ShowMedicalIcons
  abstract: true
  categories: [ HideSpawnMenu ]
  components:
  - type: ShowHealthBars
  - type: ShowHealthIcons

- type: entity
  id: ShowPsionicsIcons
  abstract: true
  categories: [ HideSpawnMenu ]
  components:
  - type: ShowPsionicsRecordIcons

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesHudDiagnostic
  name: "діагностичний hud"
  description: "Головний дисплей, здатний аналізувати цілісність і стан робототехніки та екзокостюмів. Виготовлений із сі-борг-іуму."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Hud/diag.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Hud/diag.rsi
  - type: ShowHealthBars
    damageContainers:
    - Inorganic
    - Silicon

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesHudMedical
  name: "медичний hud"
  description: "Головний дисплей, який сканує гуманоїдів, що знаходяться в полі зору, і надає точні дані про стан їхнього здоров'я."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Hud/med.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Hud/med.rsi
  - type: ShowHealthBars
    damageContainers:
    - Biological
  - type: ShowHealthIcons
    damageContainers:
    - Biological
  - type: Tag
    tags:
    - HudMedical

- type: entity
  parent: [ClothingEyesBase, ShowSecurityIcons]
  id: ClothingEyesHudSecurity
  name: "охоронний hud"
  description: "Головний дисплей, який сканує гуманоїдів у полі зору і надає точні дані про їхній ідентифікаційний статус і записи про безпеку."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Hud/sec.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Hud/sec.rsi
  - type: Tag
    tags:
    - HudSecurity

- type: entity
  parent: [ClothingEyesBase, ShowPsionicsIcons]
  id: ClothingEyesHudEpistemics
  name: "худ епі-псіоніки"
  description: "Головний дисплей, який сканує гуманоїдів у полі зору і надає точні дані про їхні псионічні показники."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Hud/epi.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Hud/epi.rsi

- type: entity
  parent: [ClothingEyesBase, BallisticGlasses] # ODJ requested this for reasons he did not clarify.
  id: ClothingEyesHudBeer
  name: "пивні окуляри"
  description: "Пара sunHud, оснащена апаратурою для сканування реагентів, а також забезпечує вроджене розуміння в'язкості рідини під час руху."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Hud/beergoggles.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Hud/beergoggles.rsi
  - type: ShowThirstIcons
  - type: StealTarget
    stealGroup: ClothingEyesHudBeer
  - type: SolutionScanner

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesHudFriedOnion
  name: "окуляри зі смаженої цибулі"
  description: "Наповнювач"
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Hud/friedonion.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Hud/friedonion.rsi
  - type: ShowHungerIcons
  - type: Food
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 3
        reagents:
        - ReagentId: Nutriment
          Quantity: 3
  - type: FlavorProfile
    flavors:
      - onion
      - greasey

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesHudOnionBeer
  name: "захисні окуляри"
  description: "Наповнювач"
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Hud/onionbeer.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Hud/onionbeer.rsi
  - type: ShowHungerIcons
  - type: ShowThirstIcons

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesHudMedOnion
  name: "медичний смажено-цибульний hud"
  description: "Наповнювач"
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Hud/medonion.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Hud/medonion.rsi
  - type: ShowHealthBars
    damageContainers:
    - Biological
  - type: ShowHealthIcons
    damageContainers:
    - Biological
  - type: ShowHungerIcons

- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesHudMedOnionBeer
  name: "medthungerst hud"
  description: "Наповнювач"
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Hud/medonionbeer.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Hud/medonionbeer.rsi
  - type: ShowHealthBars
    damageContainers:
    - Biological
  - type: ShowHealthIcons
    damageContainers:
    - Biological
  - type: ShowHungerIcons
  - type: ShowThirstIcons

- type: entity
  parent: [ClothingEyesBase, ShowSecurityIcons]
  id: ClothingEyesHudMedSec
  name: "медсек худ"
  description: "Дві хати, з'єднані руками"
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Hud/medsec.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Hud/medsec.rsi
  - type: Construction
    graph: HudMedSec
    node: medsecHud
  - type: ShowHealthBars
    damageContainers:
    - Biological
  - type: ShowHealthIcons
    damageContainers:
    - Biological
  - type: Tag
    tags:
    - HudMedicalSecurity

- type: entity
  parent: [ClothingEyesBase, ShowSecurityIcons]
  id: ClothingEyesHudMultiversal
  name: "універсальний hud"
  description: "Наповнювач"
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Hud/medsecengi.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Hud/medsecengi.rsi
  - type: ShowHealthBars
    damageContainers:
    - Biological
    - Inorganic
  - type: ShowHealthIcons
    damageContainers:
    - Biological
  - type: ShowSyndicateIcons

- type: entity
  parent: [ClothingEyesBase, ShowSecurityIcons]
  id: ClothingEyesHudOmni
  name: "омні hud"
  description: "Наповнювач"
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Hud/omni.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Hud/omni.rsi
  - type: ShowHealthBars
    damageContainers:
    - Biological
    - Inorganic
  - type: ShowHealthIcons
    damageContainers:
    - Biological
  - type: ShowHungerIcons
  - type: ShowThirstIcons
  - type: ShowSyndicateIcons

- type: entity
  parent: [ClothingEyesBase, ShowSecurityIcons, BallisticGlasses]
  id: ClothingEyesHudSyndicate
  name: "синдикатський hud"
  description: "Професійний дисплей синдикату, призначений для кращого виявлення гуманоїдів та їх подальшої ліквідації."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Hud/synd.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Hud/synd.rsi
  - type: ShowSyndicateIcons

- type: entity
  parent: [ClothingEyesBase, ShowSecurityIcons, BallisticGlasses]
  id: ClothingEyesHudSyndicateAgent
  name: "візор агента синдикату"
  description: "Професійний дисплей Агента Синдикату, призначений для швидкої діагностики стану його команди."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Hud/syndagent.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Hud/syndagent.rsi
  - type: ShowSyndicateIcons
  - type: ShowHealthBars
    damageContainers:
    - Biological

- type: entity
  parent: [ClothingEyesGlassesSunglasses, ShowSecurityIcons]
  id: ClothingEyesGlassesHiddenSecurity
  suffix: Syndicate

- type: entity
  parent: [ClothingEyesHudMedical, FlippableEyesBase]
  id: ClothingEyesEyepatchHudMedical
  name: "медичний візор-пов'язка на очі"
  description: "Дисплей, який сканує гуманоїдів, що знаходяться в полі зору, і надає точні дані про стан їхнього здоров'я. Для справжніх патріотів."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Hud/medpatch.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Hud/medpatch.rsi

- type: entity
  parent: [ClothingEyesHudSecurity, FlippableEyesBase]
  id: ClothingEyesEyepatchHudSecurity
  name: "охоронний візор-пов'язка на очі"
  description: "Дисплей, який сканує гуманоїдів у полі зору та надає точні дані про їхній ідентифікаційний статус і записи про безпеку. Для справжніх патріотів."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Hud/secpatch.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Hud/secpatch.rsi 

- type: entity
  parent: [ClothingEyesHudSecurity, FlippableEyesBase]
  id: ClothingEyesEyepatchHudTintedSecurity
  name: "тонована пов'язка на око служби безпеки"
  description: "Крутіший на вигляд родич HUD-окулярів."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Hud/secpatchtint.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Hud/secpatchtint.rsi
  - type: FlashImmunity
  - type: EyeProtection
    protectionTime: 5

- type: entity
  parent: [ClothingEyesHudBeer, FlippableEyesBase]
  id: ClothingEyesEyepatchHudBeer
  name: "пивний візор-пов'язка на очі"
  description: "Пара sunHud, оснащена апаратурою для сканування реагентів, а також вродженим розумінням в'язкості рідини під час руху. Для справжніх патріотів."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Hud/beerpatch.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Hud/beerpatch.rsi

- type: entity
  parent: [ClothingEyesBase, FlippableEyesBase]
  id: ClothingEyesEyepatchHudDiag
  name: "діагностичний візор-пов'язка на очі"
  description: "Головний дисплей, здатний аналізувати цілісність і стан робототехніки та екзокостюмів. Виготовлений із сі-борг-іуму."
  components:
  - type: Sprite
    sprite: Clothing/Eyes/Hud/diagpatch.rsi
  - type: Clothing
    sprite: Clothing/Eyes/Hud/diagpatch.rsi
  - type: ShowHealthBars
    damageContainers:
    - Inorganic
    - Silicon
