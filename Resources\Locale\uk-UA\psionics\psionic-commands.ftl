command-glimmershow-description = Показати поточний рівень Мерехтіння.
command-glimmershow-help = Немає аргументів.

command-glimmerset-description = Встановити показник Мерехтіння.
command-glimmerset-help = glimmerset (ціле число)

command-lspsionic-description = Список псіоніків.
command-lspsionic-help = Немає аргументів.

command-addpsionicpower-description = Ініціалізувати сутність як Psionic із заданим PowerPrototype
command-addpsionicpower-help = Аргумент 1 має бути EntityUid, а аргумент 2 має бути рядком, що відповідає PrototypeId PsionicPower.
addpsionicpower-args-one-error = Аргумент 1 має бути EntityUid
addpsionicpower-args-two-error = Аргумент 2 повинен збігатися з PrototypeId PsionicPower
command-addrandompsionicpower-description = Ініціалізуйте сутність як Psionic з випадковим прототипом PowerPrototype, який доступний для кидка для цієї сутності.
command-addrandompsionicpower-help = Аргумент 1 повинен бути EntityUid.
addrandompsionicpower-args-one-error = Аргумент 1 має бути EntityUid
command-removepsionicpower-description = Видалити псионічну силу з сутності.
command-removepsionicpower-help = Аргумент 1 має бути EntityUid, а аргумент 2 має бути рядком, що відповідає PrototypeId PsionicPower.
removepsionicpower-args-one-error = Аргумент 1 має бути EntityUid
removepsionicpower-args-two-error = Аргумент 2 повинен збігатися з PrototypeId PsionicPower.
removepsionicpower-not-psionic-error = Цільова організація не є Psionic.
removepsionicpower-not-contains-error = Цільова організація не має цієї PsionicPower.
command-removeallpsionicpowers-description = Видалити всі псионічні здібності з сутності.
command-removeallpsionicpowers-help = Аргумент 1 повинен бути EntityUid.
removeallpsionicpowers-args-one-error = Аргумент 1 повинен бути EntityUid.
removeallpsionicpowers-not-psionic-error = Цільова організація не є Psionic.