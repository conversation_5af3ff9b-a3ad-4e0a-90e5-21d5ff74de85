- type: entity
  parent: BaseItem
  id: AnomalyScanner
  name: "сканер аномалій"
  description: "Ручний сканер, створений для збору інформації про різні аномальні об'єкти."
  components:
  - type: Sprite
    sprite: Objects/Specific/Research/anomalyscanner.rsi
    state: icon
  - type: ActivatableUI
    key: enum.AnomalyScannerUiKey.Key
    requireActiveHand: false
    inHandsOnly: true
  - type: UserInterface
    interfaces:
      enum.AnomalyScannerUiKey.Key:
        type: AnomalyScannerBoundUserInterface
  - type: AnomalyScanner
  - type: GuideHelp
    guides:
    - ScannersAndVessels
  - type: Item
    storedRotation: -90
  - type: ReverseEngineering # Nyano
    difficulty: 2
    recipes:
      - AnomalyScanner

- type: entity
  id: AnomalyLocatorUnpowered
  parent: BaseItem
  name: "локатор аномалій"
  description: "Прилад, призначений для пошуку аномалій. Ви перевірили газовидобувників?"
  suffix: Unpowered
  components:
  - type: Sprite
    sprite: Objects/Specific/Research/anomalylocator.rsi
    layers:
      - state: icon
      - state: screen
        shader: unshaded
        visible: false
        map: ["enum.ToggleVisuals.Layer"]
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ToggleVisuals.Toggled:
        enum.ToggleVisuals.Layer:
          True: { visible: true }
          False: { visible: false }
  - type: ItemToggle
  - type: ProximityBeeper
  - type: ProximityDetector
    range: 20
    criteria:
      components:
      - Anomaly
  - type: Beeper
    minBeepInterval: 0.15
    maxBeepInterval: 1.00
    beepSound:
      path: "/Audio/Items/locator_beep.ogg"
      params:
        maxDistance: 1
        volume: -8

- type: entity
  id: AnomalyLocator
  parent: [ AnomalyLocatorUnpowered, PowerCellSlotSmallItem ]
  suffix: Powered
  components:
  - type: PowerCellDraw
    drawRate: 1
    useRate: 0
  - type: ToggleCellDraw

- type: entity
  id: AnomalyLocatorEmpty
  parent: AnomalyLocator
  suffix: Empty
  components:
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default

- type: entity
  id: AnomalyLocatorWideUnpowered
  parent: AnomalyLocatorUnpowered
  name: "локатор аномалій широкого спектру"
  description: "Пристрій, який шукає аномалії з великої відстані, але не має можливості визначити відстань до них."
  suffix: Unpowered
  components:
  - type: Sprite
    sprite: Objects/Specific/Research/anomalylocatorwide.rsi
  - type: Beeper
    minBeepInterval: 0.5
    maxBeepInterval: 0.5
  - type: ProximityDetector
    range: 40
- type: entity
  id: AnomalyLocatorWide
  parent: [ AnomalyLocatorWideUnpowered, PowerCellSlotSmallItem ]
  suffix: Powered
  components:
  - type: PowerCellDraw
    drawRate: 1
    useRate: 0
  - type: ToggleCellDraw

- type: entity
  id: AnomalyLocatorWideEmpty
  parent: AnomalyLocatorWide
  suffix: Empty
  components:
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default

- type: entity
  name: "Рукавичка G.O.R.I.L.L.A"
  parent: BaseItem
  id: WeaponGauntletGorilla
  description: "Надійне дослідницьке обладнання. За допомогою аномального ядра один удар може запустити аномальні об'єкти в повітря."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Melee/gorilla.rsi
    state: icon
  - type: Item
    size: Large
  - type: MeleeWeapon
    attackRate: 2
    angle: 0
    animation: WeaponArcFist
    wideAnimationRotation: -135
    damage:
      types:
        Blunt: 20
  - type: CorePoweredThrower
  - type: MeleeThrowOnHit
    unanchorOnHit: true
    enabled: false
  - type: ItemSlots
    slots:
      core_slot:
        name: anomaly-gorilla-core-slot-name
        insertSound: /Audio/Weapons/Guns/MagIn/batrifle_magin.ogg
        ejectSound: /Audio/Weapons/Guns/MagOut/pistol_magout.ogg
        whitelist:
          components:
          - AnomalyCore
  - type: ContainerContainer
    containers:
      core_slot: !type:ContainerSlot
