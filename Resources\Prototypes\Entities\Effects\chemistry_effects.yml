- type: entity
  id: BaseFoam
  abstract: true
  components:
  - type: Sprite
    drawdepth: Effects
  - type: Appearance
  - type: AnimationPlayer
  - type: SmokeVisuals
  - type: Transform
    anchored: true
  - type: Clickable
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        hard: false
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.4,0.4,0.4"
        mask:
        - ItemMask
        layer:
        - SlipLayer
  - type: Smoke
  - type: ActiveEdgeSpreader
  - type: EdgeSpreader
    id: Smoke
  - type: SolutionContainerManager
    solutions:
      solutionArea:
        maxVol: 600
        canReact: false

- type: entity
  parent: BaseFoam
  id: Smoke
  name: "дим"
  categories: [ HideSpawnMenu ]
  components:
  - type: Occluder
  - type: Sprite
    sprite: Effects/chemsmoke.rsi
    state: chemsmoke
  - type: TimedDespawn
    lifetime: 10
  - type: Tag
    tags:
    - HideContextMenu

- type: entity
  parent: BaseFoam
  id: Foam
  name: "піна"
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    color: "#ffffffcc"
    sprite: Effects/foam.rsi
    layers:
    - state: foam
      map: ["enum.FoamVisualLayers.Base"]
    - map: [ "enum.EdgeLayer.South" ]
      state: foam-south
    - map: [ "enum.EdgeLayer.East" ]
      state: foam-east
    - map: [ "enum.EdgeLayer.North" ]
      state: foam-north
    - map: [ "enum.EdgeLayer.West" ]
      state: foam-west
  - type: SmoothEdge
  - type: IconSmooth
    key: walls
    mode: NoSprite
  - type: FoamVisuals
    animationTime: 0.6
    animationState: foam-dissolve
  - type: Slippery
  - type: StepTrigger
    triggerGroups:
      types:
      - SlipTile
  # disabled until foam reagent duplication is fixed
  #- type: ScoopableSolution
  #  solution: solutionArea

- type: entity
  id: MetalFoam
  name: "металева піна"
  categories: [ HideSpawnMenu ]
  parent: Foam
  components:
  - type: Sprite
    color: "#ffffffcc"
    sprite: Effects/foam.rsi
    layers:
    - state: m_foam
      map: ["enum.FoamVisualLayers.Base"]
    - map: [ "enum.EdgeLayer.South" ]
      state: m_foam-south
    - map: [ "enum.EdgeLayer.East" ]
      state: m_foam-east
    - map: [ "enum.EdgeLayer.North" ]
      state: m_foam-north
    - map: [ "enum.EdgeLayer.West" ]
      state: m_foam-west
  - type: FoamVisuals
    animationTime: 0.6
    animationState: m_foam-dissolve

- type: entity
  id: IronMetalFoam
  name: "залізна піна"
  categories: [ HideSpawnMenu ]
  parent: MetalFoam
  components:
  - type: SpawnOnDespawn
    prototype: FoamedIronMetal

- type: entity
  id: AluminiumMetalFoam
  name: "алюмінієва піна"
  categories: [ HideSpawnMenu ]
  parent: MetalFoam
  components:
  - type: SpawnOnDespawn
    prototype: FoamedAluminiumMetal

- type: entity
  id: BaseFoamedMetal
  name: "основа зі спіненого металу"
  abstract: true
  placement:
    mode: SnapgridCenter
    snap:
    - Wall
  components:
  - type: RCDDeconstructable
    cost: 2
    delay: 2
    fx: EffectRCDDeconstruct2
  - type: Clickable
  - type: InteractionOutline
  - type: Sprite
    drawdepth: Walls
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb {}
        layer:
        - WallLayer
        mask:
        - WallLayer
  - type: Occluder
  - type: Appearance
  - type: SmoothEdge
  - type: IconSmooth
    key: walls
    mode: NoSprite
  - type: Transform
    anchored: true
  - type: Airtight
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]

- type: entity
  id: FoamedIronMetal
  name: "метал з спіненого заліза"
  description: "Для герметизації пробоїн в корпусі."
  parent: BaseFoamedMetal
  components:
  - type: Sprite
    drawdepth: Effects
    color: "#ffffffcc"
    sprite: Effects/foam.rsi
    layers:
    - state: iron_foam
      map: ["enum.FoamVisualLayers.Base"]
    - map: [ "enum.EdgeLayer.South" ]
      state: iron_foam-south
    - map: [ "enum.EdgeLayer.East" ]
      state: iron_foam-east
    - map: [ "enum.EdgeLayer.North" ]
      state: iron_foam-north
    - map: [ "enum.EdgeLayer.West" ]
      state: iron_foam-west

- type: entity
  id: FoamedAluminiumMetal
  name: "метал з спіненого алюмінія"
  description: "Для герметизації пробоїн в корпусі."
  parent: BaseFoamedMetal
  components:
  - type: Sprite
    drawdepth: Effects
    color: "#ffffffcc"
    sprite: Effects/foam.rsi
    layers:
    - state: metal_foam
      map: ["enum.FoamVisualLayers.Base"]
    - map: [ "enum.EdgeLayer.South" ]
      state: metal_foam-south
    - map: [ "enum.EdgeLayer.East" ]
      state: metal_foam-east
    - map: [ "enum.EdgeLayer.North" ]
      state: metal_foam-north
    - map: [ "enum.EdgeLayer.West" ]
      state: metal_foam-west
