using System.Numerics;
using Content.Shared.Shuttles.BUIStates;
using Robust.Client.AutoGenerated;
using Robust.Client.Graphics;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Map;
using Robust.Shared.Physics.Components;

namespace Content.Client.Shuttles.UI;

[GenerateTypedNameReferences]
public sealed partial class NavScreen : BoxContainer
{
    [Dependency] private readonly IEntityManager _entManager = default!;
    private SharedTransformSystem _xformSystem;

    private EntityUid? _shuttleEntity;

    public NavScreen()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
        _xformSystem = _entManager.System<SharedTransformSystem>();

        IFFToggle.OnToggled += OnIFFTogglePressed;
        IFFToggle.Pressed = NavRadar.ShowIFF;

        DockToggle.OnToggled += OnDockTogglePressed;
        DockToggle.Pressed = NavRadar.ShowDocks;

        NfInitialize(); // Frontier Initialization for the NavScreen
    }

    public void SetShuttle(EntityUid? shuttle)
    {
        _shuttleEntity = shuttle;
    }

    private void OnIFFTogglePressed(BaseButton.ButtonEventArgs args)
    {
        NavRadar.ShowIFF ^= true;
        args.Button.Pressed = NavRadar.ShowIFF;
    }

    private void OnDockTogglePressed(BaseButton.ButtonEventArgs args)
    {
        NavRadar.ShowDocks ^= true;
        args.Button.Pressed = NavRadar.ShowDocks;
    }

    public void UpdateState(NavInterfaceState scc)
    {
        NavRadar.UpdateState(scc);
        NfUpdateState(); // Frontier Update State
    }

    public void SetMatrix(EntityCoordinates? coordinates, Angle? angle)
    {
        _shuttleEntity = coordinates?.EntityId;
        NavRadar.SetMatrix(coordinates, angle);
    }

    protected override void Draw(DrawingHandleScreen handle)
    {
        base.Draw(handle);

        if (!_entManager.TryGetComponent(_shuttleEntity, out TransformComponent? gridXform) ||
            !_entManager.TryGetComponent(_shuttleEntity, out PhysicsComponent? gridBody))
        {
            return;
        }

        var (_, worldRot, worldMatrix) = _xformSystem.GetWorldPositionRotationMatrix(gridXform);
        var worldPos = Vector2.Transform(gridBody.LocalCenter, worldMatrix);

        // Get the positive reduced angle.
        var displayRot = -worldRot.Reduced();

        GridPosition.Text = $"{worldPos.X:0.0}, {worldPos.Y:0.0}";
        GridOrientation.Text = $"{displayRot.Degrees:0.0}";

        var gridVelocity = gridBody.LinearVelocity;
        gridVelocity = displayRot.RotateVec(gridVelocity);
        // Get linear velocity relative to the console entity
        GridLinearVelocity.Text = $"{gridVelocity.X + 10f * float.Epsilon:0.0}, {gridVelocity.Y + 10f * float.Epsilon:0.0}";
        GridAngularVelocity.Text = $"{-gridBody.AngularVelocity + 10f * float.Epsilon:0.0}";
    }
}
