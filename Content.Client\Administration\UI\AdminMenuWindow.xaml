<DefaultWindow
    xmlns="https://spacestation14.io"
    xmlns:adminTab="clr-namespace:Content.Client.Administration.UI.Tabs.AdminTab"
    xmlns:adminbusTab="clr-namespace:Content.Client.Administration.UI.Tabs.AdminbusTab"
    xmlns:atmosTab="clr-namespace:Content.Client.Administration.UI.Tabs.AtmosTab"
    xmlns:tabs="clr-namespace:Content.Client.Administration.UI.Tabs"
    xmlns:playerTab="clr-namespace:Content.Client.Administration.UI.Tabs.PlayerTab"
    xmlns:objectsTab="clr-namespace:Content.Client.Administration.UI.Tabs.ObjectsTab"
    xmlns:panic="clr-namespace:Content.Client.Administration.UI.Tabs.PanicBunkerTab"
    xmlns:baby="clr-namespace:Content.Client.Administration.UI.Tabs.BabyJailTab">
    <TabContainer Name="MasterTabContainer">
        <adminTab:AdminTab />
        <adminbusTab:AdminbusTab />
        <atmosTab:AtmosTab />
        <tabs:RoundTab />
        <tabs:ServerTab />
        <panic:PanicBunkerTab Name="PanicBunkerControl" Access="Public" />
        <baby:BabyJailTab Name="BabyJailControl" Access="Public" />
        <playerTab:PlayerTab Name="PlayerTabControl" Access="Public" />
        <objectsTab:ObjectsTab Name="ObjectsTabControl" Access="Public" />
    </TabContainer>
</DefaultWindow>
