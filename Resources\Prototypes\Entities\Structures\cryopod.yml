- type: entity
  parent: BaseStructure
  id: CryogenicSleepUnit
  name: "кріогенний блок для сну"
  description: "Суперохолоджений контейнер, який забезпечує безпеку членів екіпажу під час космічної подорожі."
  components:
  - type: Sprite
    noRot: true
    sprite: _Pirate/Structures/cryostorage.rsi # Pirate retexture
    layers:
    - state: sleeper_0
      map: ["base"]
    - state: sleeper_0-overlay # Pirate retexture
      shader: unshaded
      map: ["overlay"]
  - type: UserInterface
    interfaces:
      enum.CryostorageUIKey.Key:
        type: CryostorageBoundUserInterface
  - type: ActivatableUI
    key: enum.CryostorageUIKey.Key
  - type: AccessReader
    breakOnEmag: false
    access: [["Cryogenics"]]
  - type: InteractionOutline
  - type: Cryostorage
  - type: Physics
    canCollide: false
  - type: DragInsertContainer
    containerId: storage
  - type: ExitContainerOnMove
    containerId: storage
  - type: PointLight
    color: Lime
    radius: 1.5
    energy: 0.5
    castShadows: false
  - type: ContainerContainer
    containers:
      storage: !type:ContainerSlot
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.CryostorageVisuals.Full:
        base:
          True: { state: sleeper_1 }
          False: { state: sleeper_0 }
        overlay: # Pirate retexture
          True: { state: sleeper_1-overlay }
          False: { state: sleeper_0-overlay }

# This one handles all spawns, latejoin and roundstart.
- type: entity
  parent: CryogenicSleepUnit
  id: CryogenicSleepUnitSpawner
  suffix: Spawner, Roundstart AllJobs
  components:
  - type: ContainerSpawnPoint
    containerId: storage

# This one only handles latejoin spawns.
- type: entity
  parent: CryogenicSleepUnit
  id: CryogenicSleepUnitSpawnerLateJoin
  suffix: Spawner, LateJoin
  components:
    - type: ContainerSpawnPoint
      containerId: storage
      spawnType: LateJoin
