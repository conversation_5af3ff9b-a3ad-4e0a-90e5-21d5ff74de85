# species
- type: emoteSounds
  id: MaleHuman
  params:
    variation: 0.125
  sounds:
    Scream:
      collection: MaleScreams
    Laugh:
      collection: <PERSON><PERSON><PERSON>
    Sneeze:
      collection: MaleSneezes
    Cough:
      collection: MaleCoughs
    CatMeow:
      collection: CatMeows
    CatHisses:
      collection: CatHisses
    MonkeyScreeches:
      collection: MonkeyScreeches
    RobotBeep:
      collection: RobotBeeps
    Yawn:
      collection: MaleYawn
    Snore:
      collection: Snores
    <PERSON>k:
      collection: BikeHorn
    Sigh:
      collection: MaleSigh
    Crying:
      collection: MaleCry
    Whistle:
      collection: Whistles
    Weh:
      collection: Weh

- type: emoteSounds
  id: FemaleHuman
  params:
    variation: 0.125
  sounds:
    Scream:
      collection: FemaleScreams
    Laugh:
      collection: Female<PERSON><PERSON>
    Sneeze:
      collection: FemaleSneezes
    Cough:
      collection: FemaleCoughs
    CatMeow:
      collection: CatMeows
    CatHisses:
      collection: CatHisses
    MonkeyScreeches:
      collection: MonkeyScreeches
    RobotBeep:
      collection: RobotBeeps
    Yawn:
      collection: FemaleYawn
    Snore:
      collection: Snores
    Honk:
      collection: CluwneHorn
    Sigh:
      collection: FemaleSigh
    Crying:
      collection: FemaleCry
    Whistle:
      collection: Whistles
    Weh:
      collection: Weh

- type: emoteSounds
  id: MaleReptilian
  params:
    variation: 0.125
  sounds:
    Scream:
      path: /Audio/Voice/Reptilian/reptilian_scream.ogg
    Laugh:
      path: /Audio/Animals/lizard_happy.ogg
    Honk:
      collection: BikeHorn
    Whistle:
      collection: Whistles
    Crying:
      collection: MaleCry
    Weh:
      collection: Weh
    Sneeze:
      collection: MaleSneezes
    Cough:
      collection: MaleCoughs
    CatMeow:
      collection: CatMeows
    CatHisses:
      collection: CatHisses
    MonkeyScreeches:
      collection: MonkeyScreeches
    RobotBeep:
      collection: RobotBeeps
    Yawn:
      collection: MaleYawn
    Snore:
      collection: Snores
    Sigh:
      collection: MaleSigh

- type: emoteSounds
  id: FemaleReptilian
  params:
    variation: 0.125
  sounds:
    Scream:
      path: /Audio/Voice/Reptilian/reptilian_scream.ogg
    Laugh:
      path: /Audio/Animals/lizard_happy.ogg
    Honk:
      collection: BikeHorn
    Whistle:
      collection: Whistles
    Crying:
      collection: FemaleCry
    Weh:
      collection: Weh
    Sneeze:
      collection: FemaleSneezes
    Cough:
      collection: FemaleCoughs
    CatMeow:
      collection: CatMeows
    CatHisses:
      collection: CatHisses
    MonkeyScreeches:
      collection: MonkeyScreeches
    RobotBeep:
      collection: RobotBeeps
    Yawn:
      collection: FemaleYawn
    Snore:
      collection: Snores
    Sigh:
      collection: FemaleSigh

- type: emoteSounds
  id: MaleSlime
  params:
    variation: 0.125
  sounds:
    Squish:
      collection: Squishes
    Scream:
      collection: MaleScreams
    Laugh:
      collection: MaleLaugh
    Sneeze:
      collection: MaleSneezes
    Cough:
      collection: MaleCoughs
    CatMeow:
      collection: CatMeows
    CatHisses:
      collection: CatHisses
    MonkeyScreeches:
      collection: MonkeyScreeches
    RobotBeep:
      collection: RobotBeeps
    Yawn:
      collection: MaleYawn
    Snore:
      collection: Snores
    Honk:
      collection: BikeHorn
    Sigh:
      collection: MaleSigh
    Crying:
      collection: MaleCry
    Whistle:
      collection: Whistles
    Weh:
      collection: Weh

- type: emoteSounds
  id: FemaleSlime
  params:
    variation: 0.125
  sounds:
    Squish:
      collection: Squishes
    Scream:
      collection: FemaleScreams
    Laugh:
      collection: FemaleLaugh
    Sneeze:
      collection: FemaleSneezes
    Cough:
      collection: FemaleCoughs
    CatMeow:
      collection: CatMeows
    CatHisses:
      collection: CatHisses
    MonkeyScreeches:
      collection: MonkeyScreeches
    RobotBeep:
      collection: RobotBeeps
    Yawn:
      collection: FemaleYawn
    Snore:
      collection: Snores
    Honk:
      collection: CluwneHorn
    Sigh:
      collection: FemaleSigh
    Crying:
      collection: FemaleCry
    Whistle:
      collection: Whistles
    Weh:
      collection: Weh

- type: emoteSounds
  id: MaleShadowkin
  params:
    variation: 0.125
  sounds:
    Scream:
      collection: SlimeMaleScreams
    Laugh:
      collection: MaleLaugh
    Sneeze:
      collection: MaleSneezes
    Cough:
      collection: MaleCoughs
    Yawn:
      collection: MaleYawn
    Snore:
      collection: Snores
    Honk:
      collection: BikeHorn
    Sigh:
      collection: MaleSigh
    Crying:
      collection: MaleCry
    Whistle:
      collection: Whistles
    Weh:
      collection: Weh
    Hiss:
      collection: FelinidHisses
    Meow:
      collection: FelinidMeows
    Mew:
      collection: FelinidMews
    Growl:
      collection: FelinidGrowls
    Purr:
      collection: FelinidPurrs
    Mars:
      collection: Mars
    Wurble:
      collection: Wurble

- type: emoteSounds
  id: FemaleShadowkin
  params:
    variation: 0.125
  sounds:
    Scream:
      collection: SlimeFemaleScreams
    Laugh:
      collection: MaleLaugh
    Sneeze:
      collection: FemaleSneezes
    Cough:
      collection: FemaleCoughs
    Yawn:
      collection: FemaleYawn
    Snore:
      collection: Snores
    Honk:
      collection: BikeHorn
    Sigh:
      collection: FemaleSigh
    Crying:
      collection: FemaleCry
    Whistle:
      collection: Whistles
    Weh:
      collection: Weh
    Hiss:
      collection: FelinidHisses
    Meow:
      collection: FelinidMeows
    Mew:
      collection: FelinidMews
    Growl:
      collection: FelinidGrowls
    Purr:
      collection: FelinidPurrs
    Mars:
      collection: Mars
    Wurble:
      collection: Wurble

- type: emoteSounds
  id: UnisexVox
  params:
    variation: 0.125
  sounds:
    Scream:
      path: /Audio/Voice/Vox/shriek1.ogg
    Laugh:
      path: /Audio/Voice/Vox/vox_laugh.ogg
    Sneeze:
      collection: MaleSneezes
    Cough:
      collection: MaleCoughs
    CatMeow:
      collection: CatMeows
    CatHisses:
      collection: CatHisses
    MonkeyScreeches:
      collection: MonkeyScreeches
    RobotBeep:
      collection: RobotBeeps
    Yawn:
      collection: MaleYawn
    Snore:
      collection: Snores
    Honk:
      collection: BikeHorn
    Sigh:
      collection: MaleSigh
    Crying:
      collection: MaleCry
    Whistle:
      collection: Whistles
    Weh:
      collection: Weh

- type: emoteSounds
  id: UnisexDiona
  params:
    variation: 0.125
  sounds:
    Scream:
      path: /Audio/Voice/Diona/diona_scream.ogg
    Laugh:
      collection: DionaLaugh
    Honk:
      collection: BikeHorn
    Weh:
      collection: Weh
    Sneeze:
      collection: MaleSneezes
    Cough:
      collection: MaleCoughs
    CatMeow:
      collection: CatMeows
    CatHisses:
      collection: CatHisses
    MonkeyScreeches:
      collection: MonkeyScreeches
    RobotBeep:
      collection: RobotBeeps
    Yawn:
      collection: MaleYawn
    Snore:
      collection: Snores
    Sigh:
      collection: MaleSigh
    Crying:
      collection: MaleCry
    Whistle:
      collection: Whistles

- type: emoteSounds
  id: UnisexArachnid
  params:
    variation: 0.125
  sounds:
    Scream:
      path: /Audio/Voice/Arachnid/arachnid_scream.ogg
    Laugh:
      path: /Audio/Voice/Arachnid/arachnid_laugh.ogg
    Chitter:
      path: /Audio/Voice/Arachnid/arachnid_chitter.ogg
    Click:
      path: /Audio/Voice/Arachnid/arachnid_click.ogg
    Hiss:
      path: /Audio/Animals/snake_hiss.ogg #DeltaV
    Weh:
      collection: Weh
    Sneeze:
      collection: MaleSneezes
    Cough:
      collection: MaleCoughs
    CatMeow:
      collection: CatMeows
    CatHisses:
      collection: CatHisses
    MonkeyScreeches:
      collection: MonkeyScreeches
    RobotBeep:
      collection: RobotBeeps
    Yawn:
      collection: MaleYawn
    Snore:
      collection: Snores
    Honk:
      collection: BikeHorn
    Sigh:
      collection: MaleSigh
    Crying:
      collection: MaleCry
    Whistle:
      collection: Whistles

- type: emoteSounds
  id: UnisexDwarf
  params:
    variation: 0.125
    pitch: 0.75
  sounds:
    Scream:
      collection: MaleScreams
    Laugh:
      collection: MaleLaugh
    Sneeze:
      collection: MaleSneezes
    Cough:
      collection: MaleCoughs
    CatMeow:
      collection: CatMeows
    CatHisses:
      collection: CatHisses
    MonkeyScreeches:
      collection: MonkeyScreeches
    RobotBeep:
      collection: RobotBeeps
    Yawn:
      collection: MaleYawn
    Snore:
      collection: Snores
    Sigh:
      collection: MaleSigh
    Honk:
      collection: BikeHorn
    Crying:
      collection: MaleCry
    Whistle:
      collection: Whistles
    Weh:
      collection: Weh

- type: emoteSounds
  id: FemaleDwarf
  params:
    variation: 0.125
    pitch: 0.75
  sounds:
    Scream:
      collection: FemaleScreams
    Laugh:
      collection: FemaleLaugh
    Sneeze:
      collection: FemaleSneezes
    Cough:
      collection: FemaleCoughs
    CatMeow:
      collection: CatMeows
    CatHisses:
      collection: CatHisses
    MonkeyScreeches:
      collection: MonkeyScreeches
    RobotBeep:
      collection: RobotBeeps
    Yawn:
      collection: FemaleYawn
    Snore:
      collection: Snores
    Sigh:
      collection: FemaleSigh
    Honk:
      collection: BikeHorn
    Crying:
      collection: FemaleCry
    Whistle:
      collection: Whistles
    Weh:
      collection: Weh

- type: emoteSounds
  id: UnisexMoth
  params:
    variation: 0.125
  sounds:
    Buzz:
      path: /Audio/Voice/Moth/moth_scream.ogg
    Scream:
      path: /Audio/Voice/Moth/moth_scream.ogg
    Laugh:
      path: /Audio/Voice/Moth/moth_laugh.ogg
    Chitter:
      path: /Audio/Voice/Moth/moth_chitter.ogg
    Squeak:
      path: /Audio/Voice/Moth/moth_squeak.ogg
    Weh:
      collection: Weh
    Sneeze:
      collection: MaleSneezes
    Cough:
      collection: MaleCoughs
    CatMeow:
      collection: CatMeows
    CatHisses:
      collection: CatHisses
    MonkeyScreeches:
      collection: MonkeyScreeches
    RobotBeep:
      collection: RobotBeeps
    Yawn:
      collection: MaleYawn
    Snore:
      collection: Snores
    Honk:
      collection: BikeHorn
    Sigh:
      collection: MaleSigh
    Crying:
      collection: MaleCry
    Whistle:
      collection: Whistles

- type: emoteSounds
  id: UnisexIPC
  params:
    variation: 0
  sounds:
    Scream:
      path: /Audio/Voice/IPC/robot-scream.ogg
      params:
        variation: 0.125
    Laugh:
      path: /Audio/Voice/IPC/robot-laugh_3.ogg
      params:
        variation: 0.125
    Sigh:
      path: /Audio/Voice/Talk/pai.ogg
      params:
        variation: 0.125
    Crying:
      path: /Audio/Voice/IPC/cry_robot_1.ogg
      params:
        variation: 0.125
    Whistle:
      path: /Audio/Voice/IPC/pai_whistle.ogg
      params:
        variation: 0.125
    CatMeow:
      collection: CatMeows
      params:
        variation: 0.125
    CatHisses:
      collection: CatHisses
      params:
        variation: 0.125
    MonkeyScreeches:
      collection: MonkeyScreeches
      params:
        variation: 0.125
    RobotBeep: # disease
      path: /Audio/Effects/tesla_consume.ogg
      params:
        variation: 0.125
    Beep:      # normal
      path: /Audio/Voice/IPC/beep_2000.ogg
    Boop:
      path: /Audio/Voice/IPC/beep_500.ogg
    Buzz:
      path: /Audio/Machines/buzz-sigh.ogg
    Honk:
      path: /Audio/Items/bikehorn.ogg
      params:
        variation: 0.125
    Chime:
      path: /Audio/Effects/Cargo/ping.ogg
    Buzz-Two:
      path: /Audio/Machines/buzz-two.ogg
    Ping:
      path: /Audio/Effects/beep1.ogg
      params: # to prevent it from being definitively read by players as "OH SHIT A GRENADE"
        variation: 0.125
    Whirr:
      collection: IPCWhirrs
      params:
        variation: 0

- type: emoteSounds
  id: UnisexSilicon
  params:
    variation: 0.05
  sounds:
    Scream:
      path: /Audio/Voice/IPC/robot-scream.ogg
      params:
        variation: 0.125
    Laugh:
      path: /Audio/Voice/IPC/robot-laugh_3.ogg
      params:
        variation: 0.125
    Sigh:
      path: /Audio/Voice/Talk/pai.ogg
      params:
        variation: 0.125
    Crying:
      path: /Audio/Voice/IPC/cry_robot_1.ogg
      params:
        variation: 0.125
    Whistle:
      path: /Audio/Voice/IPC/pai_whistle.ogg
      params:
        variation: 0.125
    Beep:
      path: /Audio/Voice/IPC/beep_2000.ogg
    Boop:
      path: /Audio/Voice/IPC/beep_500.ogg
    Whirr:
      collection: IPCWhirrs
      params:
        variation: 0
    Chime:
      path: /Audio/Machines/chime.ogg
    Buzz:
      path: /Audio/Machines/buzz-sigh.ogg
    Buzz-Two:
      path: /Audio/Machines/buzz-two.ogg
    Honk:
      path: /Audio/Items/bikehorn.ogg
    Ping:
      path: /Audio/Effects/Cargo/ping.ogg

- type: emoteSounds
  id: UnisexSiliconSyndicate
  params:
    variation: 0.05
  sounds:
    Laugh:
      path: /Audio/Voice/Silicon/syndieborg_laugh.ogg
    Scream:
      path: /Audio/Voice/IPC/robot-scream.ogg
      params:
        variation: 0.125
    Sigh:
      path: /Audio/Voice/Talk/pai.ogg
      params:
        variation: 0.125
    Crying:
      path: /Audio/Voice/IPC/cry_robot_1.ogg
      params:
        variation: 0.125
    Whistle:
      path: /Audio/Voice/IPC/pai_whistle.ogg
      params:
        variation: 0.125
    Beep:
      path: /Audio/Voice/IPC/beep_2000.ogg
    Boop:
      path: /Audio/Voice/IPC/beep_500.ogg
    Whirr:
      collection: IPCWhirrs
      params:
        variation: 0
    Chime:
      path: /Audio/Machines/chime.ogg
    Buzz:
      path: /Audio/Machines/buzz-sigh.ogg
    Buzz-Two:
      path: /Audio/Machines/buzz-two.ogg
    Honk:
      path: /Audio/Items/bikehorn.ogg
    Ping:
      path: /Audio/Effects/Cargo/ping.ogg

# body emotes
- type: emoteSounds
  id: GeneralBodyEmotes
  sounds:
    Clap:
      collection: Claps
    Snap:
      collection: Snaps
      params:
        volume: -6
    Salute:
      collection: Salutes

- type: emoteSounds
  id: DionaBodyEmotes
  sounds:
    Clap:
      path: /Audio/Voice/Diona/diona_clap.ogg
    Snap:
      path: /Audio/Voice/Diona/diona_snap.ogg
      params:
        volume: -10
    Salute:
      path: /Audio/Voice/Diona/diona_salute.ogg
      params:
       volume: -5

# mobs
- type: emoteSounds
  id: Zombie
  sound:
    collection: ZombieScreams
    params:
      variation: 0.125

- type: emoteSounds
  id: Skeleton
  sound:
    path: /Audio/Voice/Skeleton/skeleton_scream.ogg
    params:
      variation: 0.125

- type: emoteSounds
  id: Mouse
  sound:
    path: /Audio/Animals/mouse_squeak.ogg
    params:
      variation: 0.125

- type: emoteSounds
  id: Cluwne
  sound:
    collection: CluwneScreams
    params:
      variation: 0.125

- type: emoteSounds
  id: Bear
  sound:
    path: /Audio/Animals/bear.ogg
    params:
      variation: 0.125

- type: emoteSounds
  id: Cerberus
  sound:
    path: /Audio/Animals/cerberus.ogg
    params:
      variation: 0.125

- type: emoteSounds
  id: Kangaroo
  sound:
    path: /Audio/Animals/kangaroo_grunt.ogg
    params:
      variation: 0.125

- type: emoteSounds
  id: Nymph
  sounds:
    Chirp:
      path: /Audio/Animals/nymph_chirp.ogg

- type: emoteSounds
  id: Parrot
  sound:
    path: /Audio/Animals/parrot_raught.ogg
    params:
      variation: 0.125

- type: emoteSounds
  id: UnisexPlasmaman
  params:
    variation: 0.125
  sounds:
    Scream:
      collection: PlasmamanUnisexScreams
  sound:
    path: /Audio/Voice/Skeleton/skeleton_scream.ogg
