﻿- type: entity
  parent: FireAxeCabinet
  id: ShotGunCabinet
  name: "шафа для рушниць"
  description: "На невеликій етикетці написано \"Тільки для екстреного використання\", а також вказано, як безпечно користуватися рушницею. Начебто."
  components:
  - type: Sprite
    sprite: DeltaV/Structures/Wallmounts/shotgun_cabinet.rsi # Delta-V
    layers:
    - state: cabinet
    - state: shotgun
      map: ["enum.ItemCabinetVisualLayers.ContainsItem"]
      visible: true
    - state: glass
      map: ["enum.ItemCabinetVisualLayers.Door"]
  - type: ItemCabinet
    cabinetSlot:
      ejectOnInteract: true
      whitelist:
        tags:
        - WeaponShotgunKammerer
  - type: AccessReader
    access: [["Security"], ["Command"]]

- type: entity
  id: ShotGunCabinetOpen
  parent: [ShotGunCabinet, FireAxeCabinetOpen]
  suffix: Open

- type: entity
  id: ShotGunCabinetFilled
  parent: [ShotGunCabinet,FireAxeCabinetFilled]
  suffix: Filled
  components:
  - type: ItemCabinet
    cabinetSlot:
      startingItem: WeaponShotgunKammerer
      ejectOnInteract: true
      whitelist:
        tags:
        - WeaponShotgunKammerer

- type: entity
  id: ShotGunCabinetFilledOpen
  parent: [ShotGunCabinetFilled,FireAxeCabinetFilledOpen]
  suffix: Filled, Open

