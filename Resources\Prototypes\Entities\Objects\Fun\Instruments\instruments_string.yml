- type: entity
  parent: BaseHandheldInstrument
  id: ElectricGuitarInstrument
  name: "електрогітара"
  description: "Тепер ви відчуєте себе рок-зіркою!"
  components:
  - type: Instrument
    program: 27
  - type: SwappableInstrument
    instrumentList:
      "Clean": {27: 0}
      "Jazz": {25: 0}
      "Muted": {28: 0}
  - type: Sprite
    sprite: Objects/Fun/Instruments/eguitar.rsi
    state: icon
  - type: MeleeWeapon
    soundHit:
      path: /Audio/Nyanotrasen/Weapons/electricguitarhit.ogg
    range: 1.85
    damage:
      types:
        Blunt: 6
        Shock: 2
    bluntStaminaDamageFactor: 1.25
    heavyRateModifier: 1.4
    heavyDamageBaseModifier: 1.5
    heavyStaminaCost: 2.5
    angle: 105
  - type: DamageOtherOnHit
    staminaCost: 8
  - type: Item
    size: Normal
    sprite: Objects/Fun/Instruments/eguitar.rsi
  - type: Clothing
    quickEquip: false
    slots:
    - back
    sprite: Objects/Fun/Instruments/eguitar.rsi
  - type: Tag
    tags:
    - StringInstrument

- type: entity
  parent: BaseHandheldInstrument
  id: BassGuitarInstrument
  name: "бас-гітара"
  description: "Ти відчуваєш себе справді круто, тримаючи це в руках. Шкода, що ти єдиний, хто так думає."
  components:
  - type: Instrument
    program: 33
  - type: SwappableInstrument
    instrumentList:
      "Fingered": {33: 0}
      "Pick": {34: 0}
      "Slap": {36: 0}
      "Slap (XTra Funky)": {37: 0}
  - type: Sprite
    sprite: Objects/Fun/Instruments/bassguitar.rsi
    state: icon
  - type: MeleeWeapon
    soundHit:
      path: /Audio/Nyanotrasen/Weapons/electricguitarhit.ogg
    range: 1.85
    damage:
      types:
        Blunt: 6
        Shock: 2
    bluntStaminaDamageFactor: 1.25
    heavyRateModifier: 1.4
    heavyDamageBaseModifier: 1.5
    heavyStaminaCost: 2.5
    angle: 105
  - type: DamageOtherOnHit
    staminaCost: 8
  - type: Item
    size: Normal
    sprite: Objects/Fun/Instruments/bassguitar.rsi
  - type: Clothing
    quickEquip: false
    slots:
    - back
    sprite: Objects/Fun/Instruments/bassguitar.rsi
  - type: Tag
    tags:
    - StringInstrument

- type: entity
  parent: BaseHandheldInstrument
  id: RockGuitarInstrument
  name: "рок-гітара"
  description: "Яка сокира!"
  components:
  - type: Instrument
    program: 29
  - type: SwappableInstrument
    instrumentList:
      "Overdrive": {29: 0}
      "Distortion": {30: 0}
      "Harmonics": {31: 0}
  - type: Sprite
    sprite: Objects/Fun/Instruments/rockguitar.rsi
    state: icon
  - type: MeleeWeapon
    soundHit:
      path: /Audio/Nyanotrasen/Weapons/electricguitarhit.ogg
    range: 1.85
    attackRate: .8
    wideAnimationRotation: 45
    damage:
      types:
        Blunt: 6
        Shock: 2
    bluntStaminaDamageFactor: 1.25
    heavyRateModifier: 1.4
    heavyDamageBaseModifier: 1.5
    heavyStaminaCost: 2.5
    angle: 160
  - type: DamageOtherOnHit
    staminaCost: 8
  - type: Wieldable
  - type: IncreaseDamageOnWield
    damage:
      types:
        Blunt: 2
        Shock: 1
  - type: Item
    size: Normal
    sprite: Objects/Fun/Instruments/rockguitar.rsi
  - type: Clothing
    quickEquip: false
    slots:
    - back
    sprite: Objects/Fun/Instruments/rockguitar.rsi
  - type: Tag
    tags:
    - StringInstrument

- type: entity
  parent: BaseHandheldInstrument
  id: AcousticGuitarInstrument
  name: "акустична гітара"
  description: "Так чи інакше, ось Чудо-стіна."
  components:
  - type: Instrument
    program: 24
  - type: SwappableInstrument
    instrumentList:
      "Nylon": {24: 0}
      "Steel": {25: 0}
  - type: Sprite
    sprite: Objects/Fun/Instruments/guitar.rsi
    state: icon
  - type: Tag
    tags:
    - StringInstrument
  - type: Item
    sprite: Objects/Fun/Instruments/guitar.rsi
    size: Normal
  - type: Clothing
    quickEquip: false
    slots:
    - back
    sprite: Objects/Fun/Instruments/guitar.rsi
  - type: Wieldable
  - type: Damageable # Smash it! Does 20 damage a hit, but breaks after 1 hit.
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 20
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          path: /Audio/Weapons/guitarsmash.ogg
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank:
            min: 2
            max: 4
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: DamageOnHit # This makes it destroy after one hit.
    damage:
      types:
        Blunt: 20
  - type: DamageOnLand
    damage:
      types:
        Blunt: 20
  - type: MeleeWeapon
    range: 1.5
    wideAnimationRotation: 45
    damage:
      types:
        Blunt: 9
    bluntStaminaDamageFactor: 1.5
    heavyRateModifier: 1.75
    heavyDamageBaseModifier: 1.5
    heavyStaminaCost: 5
    angle: 105
  - type: DamageOtherOnHit
    staminaCost: 7
  - type: IncreaseDamageOnWield
    damage:
      types:
        Blunt: 3

- type: entity
  parent: BaseHandheldInstrument
  id: GuitarlessFretsInstrument
  name: "безгітарні лади"
  description: "кому взагалі потрібне тіло?"
  suffix: Admeme #the sound is awful
  components:
  - type: Instrument
    program: 120
  - type: Sprite
    sprite: Objects/Fun/Instruments/guitarlessfrets.rsi
    state: icon

- type: entity
  parent: BaseHandheldInstrument
  id: BanjoInstrument
  name: "банджо"
  components:
  - type: Instrument
    program: 105
  - type: Sprite
    sprite: Objects/Fun/Instruments/banjo.rsi
    state: icon
  - type: Item
    size: Normal
    sprite: Objects/Fun/Instruments/banjo.rsi
  - type: Tag
    tags:
    - StringInstrument
  - type: MeleeWeapon
    soundHit:
      path: /Audio/SimpleStation14/Weapons/Melee/banjohit.ogg
    range: 1.5
    damage:
      types:
        Blunt: 8
    bluntStaminaDamageFactor: 1.5
    heavyRateModifier: 1.75
    heavyDamageBaseModifier: 1.5
    heavyStaminaCost: 2.5
    angle: 105
  - type: DamageOtherOnHit
    staminaCost: 8

- type: entity
  parent: BaseHandheldInstrument
  id: ViolinInstrument
  name: "скрипка"
  description: "Улюбленець музичних віртуозів і блюграсс-бендів."
  components:
  - type: Instrument
    program: 40
  - type: SwappableInstrument
    instrumentList:
      "Classical": {40: 0}
      "Bluegrass": {110: 0}
  - type: Sprite
    sprite: Objects/Fun/Instruments/violin.rsi
    state: icon
  - type: Item
    size: Normal
    sprite: Objects/Fun/Instruments/violin.rsi
  - type: Tag
    tags:
    - StringInstrument

- type: entity
  parent: BaseHandheldInstrument
  id: ViolaInstrument
  name: "альт"
  description: "Як скрипка, тільки гірше."
  components:
  - type: Instrument
    program: 41
  - type: Sprite
    sprite: Objects/Fun/Instruments/viola.rsi
    state: icon
  - type: Item
    size: Normal
    sprite: Objects/Fun/Instruments/viola.rsi
  - type: Tag
    tags:
    - StringInstrument

- type: entity
  parent: BaseHandheldInstrument
  id: CelloInstrument
  name: "віолончель"
  description: "Ботаніки називають їх віолончелями."
  components:
  - type: Instrument
    program: 42
  - type: Sprite
    sprite: Objects/Fun/Instruments/cello.rsi
    state: icon
  - type: Item
    size: Large
    shape:
    - 0,0,1,3
    sprite: Objects/Fun/Instruments/cello.rsi
  - type: Tag
    tags:
    - StringInstrument
