- type: entity
  abstract: true
  parent: ReagentContainerBase
  id: ReagentTinBase
  components:
  - type: Openable
    openableByHand: false
    sound:
      path: /Audio/Items/can_open3.ogg
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.OpenableVisuals.Opened:
        enum.OpenableVisuals.Layer:
          True: {state: "icon-open"}
          False: {state: "icon"}
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 6
      behaviors:
      - !type:OpenBehavior
  - type: Item
    sprite: Objects/Consumable/Food/snacks.rsi
    heldPrefix: packet
    size: Normal
  - type: DamageOnLand
    damage:
      types:
        Blunt: 3
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 3
  - type: Damageable
    damageContainer: Inorganic
  - type: StaticPrice
    price: 50

- type: entity
  parent: ReagentTinBase
  id: ReagentTinPowderedJuiceOrange
  name: "банка порошкового апельсинового соку"
  description: "Контейнер з порошкоподібним апельсиновим соком виробництва HydroCo. Згідно з інструкцією, перемішування з водою у співвідношенні 1:1 відновить сік."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Consumable/Drinks/powdered_drinks.rsi
    layers:
    - state: icon
      map: ["enum.OpenableVisuals.Layer"]
    - state: overlay-container
      color: "#FFC420"
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50
        reagents:
        - ReagentId: PowderedJuiceOrange
          Quantity: 50

- type: entity
  parent: ReagentTinBase
  id: ReagentTinPowderedMilk
  name: "банка сухого молока"
  description: "Контейнер із сухим молоком виробництва компанії HydroCo. Згідно з інструкцією, перемішування з водою у співвідношенні 1:1 відновить молоко."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Consumable/Drinks/powdered_drinks.rsi
    layers:
    - state: icon
      map: ["enum.OpenableVisuals.Layer"]
    - state: overlay-container
      color: "#FAFAFA"
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50
        reagents:
        - ReagentId: PowderedMilk
          Quantity: 50

- type: entity
  parent: ReagentTinBase
  id: ReagentTinPowderedMilkSoy
  name: "банка сухого соєвого молока"
  description: "Контейнер із сухим соєвим молоком виробництва HydroCo. Згідно з інструкцією, перемішування з водою у співвідношенні 1:1 відновить молоко."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Consumable/Drinks/powdered_drinks.rsi
    layers:
    - state: icon
      map: ["enum.OpenableVisuals.Layer"]
    - state: overlay-container
      color: "#FFFCE3"
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50
        reagents:
        - ReagentId: PowderedMilkSoy
          Quantity: 50

- type: entity
  parent: ReagentTinBase
  id: ReagentTinPowderedJuiceLime
  name: "банка порошкоподібного лаймового соку"
  description: "Контейнер з порошкоподібним соком лайма виробництва HydroCo. Згідно з інструкцією, перемішування з водою у співвідношенні 1:1 відновить сік."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Consumable/Drinks/powdered_drinks.rsi
    layers:
    - state: icon
      map: ["enum.OpenableVisuals.Layer"]
    - state: overlay-container
      color: "#B4FF64"
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50
        reagents:
        - ReagentId: PowderedJuiceLime
          Quantity: 50

- type: entity
  parent: ReagentTinBase
  id: ReagentTinPowderedJuiceLemon
  name: "банка лимонного соку в порошку"
  description: "Контейнер з порошкоподібним лимонним соком виробництва HydroCo. Згідно з інструкцією, перемішування з водою у співвідношенні 1:1 відновить сік."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Consumable/Drinks/powdered_drinks.rsi
    layers:
    - state: icon
      map: ["enum.OpenableVisuals.Layer"]
    - state: overlay-container
      color: "#FFFC69"
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50
        reagents:
        - ReagentId: PowderedJuiceLemon
          Quantity: 50

- type: entity
  parent: ReagentTinBase
  id: ReagentTinPowderedJuicePineapple
  name: "банка порошкового ананасового соку"
  description: "Контейнер з порошкоподібним ананасовим соком виробництва HydroCo. Згідно з інструкцією, перемішування з водою у співвідношенні 1:1 відновить сік."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Consumable/Drinks/powdered_drinks.rsi
    layers:
    - state: icon
      map: ["enum.OpenableVisuals.Layer"]
    - state: overlay-container
      color: "#FFD644"
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50
        reagents:
        - ReagentId: PowderedJuicePineapple
          Quantity: 50

- type: entity
  parent: ReagentTinBase
  id: ReagentTinPowderedJuiceBanana
  name: "банка порошкового бананового соку"
  description: "Контейнер з порошкоподібним банановим соком виробництва HydroCo. Згідно з інструкцією, перемішування з водою у співвідношенні 1:1 відновить сік."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Consumable/Drinks/powdered_drinks.rsi
    layers:
    - state: icon
      map: ["enum.OpenableVisuals.Layer"]
    - state: overlay-container
      color: "#FFFEBF"
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50
        reagents:
        - ReagentId: PowderedJuiceBanana
          Quantity: 50

- type: entity
  parent: ReagentTinBase
  id: ReagentTinPowderedJuiceBerry
  name: "банка порошкоподібного ягідного соку"
  description: "Контейнер з порошкоподібним ягідним соком виробництва HydroCo. Згідно з інструкцією, перемішування з водою у співвідношенні 1:1 відновить сік."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Consumable/Drinks/powdered_drinks.rsi
    layers:
    - state: icon
      map: ["enum.OpenableVisuals.Layer"]
    - state: overlay-container
      color: "#D366FF"
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50
        reagents:
        - ReagentId: PowderedJuiceBerry
          Quantity: 50

- type: entity
  parent: ReagentTinBase
  id: ReagentTinPowderedJuiceWatermelon
  name: "банка порошкового динного соку"
  description: "Контейнер з порошкоподібним кавуновим соком виробництва HydroCo. ПРИМІТКА - тільки диня; вода не входить до комплекту, продається окремо. Згідно з інструкцією, перемішування з водою у співвідношенні 1:1 відновить сік."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Consumable/Drinks/powdered_drinks.rsi
    layers:
    - state: icon
      map: ["enum.OpenableVisuals.Layer"]
    - state: overlay-container
      color: "#FFBCDB"
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50
        reagents:
        - ReagentId: PowderedJuiceWatermelon
          Quantity: 50

- type: entity
  parent: ReagentTinBase
  id: ReagentTinPowderedJuiceGrape
  name: "банка сушеного виноградного соку"
  description: "Контейнер з порошкоподібним виноградним соком виробництва HydroCo. Згідно з інструкцією, перемішування з водою у співвідношенні 1:1 відновить сік."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Consumable/Drinks/powdered_drinks.rsi
    layers:
    - state: icon
      map: ["enum.OpenableVisuals.Layer"]
    - state: overlay-container
      color: "#E57DFF"
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50
        reagents:
        - ReagentId: PowderedJuiceGrape
          Quantity: 50

- type: entity
  parent: ReagentTinBase
  id: ReagentTinPowderedJuiceApple
  name: "банка сухого яблучного соку"
  description: "Контейнер з порошкоподібним яблучним соком виробництва HydroCo. Згідно з інструкцією, перемішування з водою у співвідношенні 1:1 відновить сік."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Consumable/Drinks/powdered_drinks.rsi
    layers:
    - state: icon
      map: ["enum.OpenableVisuals.Layer"]
    - state: overlay-container
      color: "#FFC67E"
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50
        reagents:
        - ReagentId: PowderedJuiceApple
          Quantity: 50

- type: entity
  parent: ReagentTinBase
  id: ReagentTinPowderedJuiceCherry
  name: "банка порошкового вишневого соку"
  description: "Контейнер з порошкоподібним вишневим соком виробництва HydroCo. Згідно з інструкцією, перемішування з водою у співвідношенні 1:1 відновить сік."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Consumable/Drinks/powdered_drinks.rsi
    layers:
    - state: icon
      map: ["enum.OpenableVisuals.Layer"]
    - state: overlay-container
      color: "#A9536C"
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50
        reagents:
        - ReagentId: PowderedJuiceCherry
          Quantity: 50

- type: entity
  parent: ReagentTinBase
  id: ReagentTinPowderedJuiceCarrot
  name: "банка порошкового морквяного соку"
  description: "Контейнер з порошкоподібним морквяним соком виробництва HydroCo. Згідно з інструкцією, перемішування з водою у співвідношенні 1:1 відновить сік."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Consumable/Drinks/powdered_drinks.rsi
    layers:
    - state: icon
      map: ["enum.OpenableVisuals.Layer"]
    - state: overlay-container
      color: "#FFA06B"
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50
        reagents:
        - ReagentId: PowderedJuiceCarrot
          Quantity: 50

- type: entity
  parent: ReagentTinBase
  id: ReagentTinPowderedJuiceTomato
  name: "банка порошкового томатного соку"
  description: "Контейнер з порошкоподібним томатним соком виробництва HydroCo. Згідно з інструкцією, перемішування з водою у співвідношенні 1:1 відновить сік."
  components:
  - type: Sprite
    sprite: DeltaV/Objects/Consumable/Drinks/powdered_drinks.rsi
    layers:
    - state: icon
      map: ["enum.OpenableVisuals.Layer"]
    - state: overlay-container
      color: "#D85553"
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 50
        reagents:
        - ReagentId: PowderedJuiceTomato
          Quantity: 50
