- type: entity
  abstract: true
  parent: BaseItem
  id: BaseLogicItem
  components:
  - type: Sprite
    sprite: Objects/Devices/gates.rsi
  - type: Anchorable
  - type: Rotatable
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: BasicDevice
  - type: WirelessNetworkConnection
    range: 200

- type: entity
  parent: BaseLogicItem
  id: LogicGateOr
  name: "логічний елемент"
  description: "Логічні ворота з двома входами та одним виходом. Техніки можуть змінювати режим його роботи за допомогою викрутки."
  suffix: Or
  components:
  - type: Sprite
    layers:
    - state: base
    - state: or
      map: [ "enum.LogicGateLayers.Gate" ]
  - type: LogicGate
  - type: UseDelay
    delay: 0.5
  - type: DeviceLinkSink
    ports:
    - InputA
    - InputB
  - type: DeviceLinkSource
    ports:
    - Output
    lastSignals:
      Output: false
  - type: Construction
    graph: LogicGate
    node: logic_gate
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.LogicGateVisuals.Gate:
        enum.LogicGateLayers.Gate:
          Or: { state: or }
          And: { state: and }
          Xor: { state: xor }
          Nor: { state: nor }
          Nand: { state: nand }
          Xnor: { state: xnor }

- type: entity
  parent: LogicGateOr
  id: LogicGateAnd
  suffix: And
  components:
  - type: Sprite
    layers:
    - state: base
    - state: and
      map: [ "enum.LogicGateLayers.Gate" ]
  - type: LogicGate
    gate: And

- type: entity
  parent: LogicGateOr
  id: LogicGateXor
  suffix: Xor
  components:
  - type: Sprite
    layers:
    - state: base
    - state: xor
      map: [ "enum.LogicGateLayers.Gate" ]
  - type: LogicGate
    gate: Xor

- type: entity
  parent: LogicGateOr
  id: LogicGateNor
  suffix: Nor
  components:
  - type: Sprite
    layers:
    - state: base
    - state: nor
      map: [ "enum.LogicGateLayers.Gate" ]
  - type: LogicGate
    gate: Nor

- type: entity
  parent: LogicGateOr
  id: LogicGateNand
  suffix: Nand
  components:
  - type: Sprite
    layers:
    - state: base
    - state: nand
      map: [ "enum.LogicGateLayers.Gate" ]
  - type: LogicGate
    gate: Nand

- type: entity
  parent: LogicGateOr
  id: LogicGateXnor
  suffix: Xnor
  components:
  - type: Sprite
    layers:
    - state: base
    - state: xnor
      map: [ "enum.LogicGateLayers.Gate" ]
  - type: LogicGate
    gate: Xnor

- type: entity
  parent: BaseLogicItem
  id: EdgeDetector
  name: "детектор країв"
  description: "Фронтальний детектор сигналів."
  components:
  - type: Sprite
    state: edge_detector
  - type: EdgeDetector
  - type: DeviceLinkSink
    ports:
    - Input
  - type: DeviceLinkSource
    ports:
    - OutputHigh
    - OutputLow
    lastSignals:
      OutputHigh: false
      OutputLow: false
  - type: Construction
    graph: LogicGate
    node: edge_detector

- type: entity
  parent: BaseLogicItem
  id: PowerSensor
  name: "датчик потужності"
  description: "Пристрій для перевірки сигналів в електромережі."
  components:
  - type: Sprite
    state: power_sensor
  - type: PowerSensor
  - type: PowerSwitchable
    examineText: power-sensor-voltage-examine
    switchText: power-sensor-voltage-switch
    cables:
    - voltage: HV
      node: hv
    - voltage: MV
      node: mv
    - voltage: LV
      node: lv
  - type: UseDelay
    delay: 1
  - type: NodeContainer
    examinable: true
    nodes:
      hv:
        !type:CableDeviceNode
        nodeGroupID: HVPower
      mv:
        !type:CableDeviceNode
        nodeGroupID: MVPower
        enabled: false
      lv:
        !type:CableDeviceNode
        nodeGroupID: Apc
        enabled: false
  - type: DeviceLinkSource
    ports:
    - PowerCharging
    - PowerDischarging
    lastSignals:
      PowerCharging: false
      PowerDischarging: false
  - type: Construction
    graph: LogicGate
    node: power_sensor
