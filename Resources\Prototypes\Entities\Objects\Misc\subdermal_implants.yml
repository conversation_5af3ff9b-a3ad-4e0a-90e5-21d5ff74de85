- type: entity
  id: BaseSubdermalImplant
  name: "імплант"
  description: "Мікроскопічний чіп, який вводиться під шкіру."
  abstract: true
  components:
  - type: SubdermalImplant
  - type: Tag
    tags:
    - SubdermalImplant
    - HideContextMenu

#Fun implants

- type: entity
  parent: BaseSubdermalImplant
  id: SadTromboneImplant
  name: "імплант сумного тромбона"
  description: "Цей імплант грає сумну мелодію, коли користувач помирає."
  categories: [ HideSpawnMenu ]
  components:
    - type: SubdermalImplant
      whitelist:
        components:
        - MobState # admeme implanting a chair with trombone implant needs to give the chair mobstate so it can die first
    - type: TriggerOnMobstateChange
      mobState:
      - Dead
    - type: EmitSoundOnTrigger
      sound:
        collection: SadTrombone
        params:
          variation: 0.125

- type: entity
  parent: BaseSubdermalImplant
  id: LightImplant
  name: "імплант світла"
  description: "Цей імплант випромінює світло від шкіри користувача при активації."
  categories: [ HideSpawnMenu ]
  components:
    - type: SubdermalImplant
      implantAction: ActionToggleLight
    - type: PointLight
      enabled: false
      radius: 2.5
      softness: 5
      mask: /Textures/Effects/LightMasks/cone.png
      autoRot: true
    - type: Tag
      tags:
        - SubdermalImplant
        - HideContextMenu
        - Flashlight
    - type: UnpoweredFlashlight

- type: entity
  parent: BaseSubdermalImplant
  id: BikeHornImplant
  name: "імплант велосипедного клаксона"
  description: "Цей імплант дозволяє користувачеві хонкати будь-де і будь-коли."
  categories: [ HideSpawnMenu ]
  components:
    - type: SubdermalImplant
      implantAction: ActionActivateHonkImplant
    - type: TriggerImplantAction
    - type: EmitSoundOnTrigger
      sound:
        collection: BikeHorn
        params:
          variation: 0.125
    - type: Tag
      tags:
      - BikeHorn

#Security implants

- type: entity
  parent: BaseSubdermalImplant
  id: TrackingImplant
  name: "імплант відстеження"
  description: "Цей імплант має пристрій стеження, підключений до сенсорної мережі костюма, а також монітор стану радіоканалу безпеки."
  categories: [ HideSpawnMenu ]
  components:
    - type: SubdermalImplant
      whitelist:
        components:
        - MobState # admeme implanting a chair with tracking implant needs to give the chair mobstate so it can die first
    - type: SuitSensor
      randomMode: false
      controlsLocked: true
      mode: SensorCords
      activationContainer: "implant"
    - type: DeviceNetwork
      deviceNetId: Wireless
      transmitFrequencyId: SuitSensor
    - type: StationLimitedNetwork
    - type: WirelessNetworkConnection
      range: 500
    - type: MansusGraspBlockTrigger # Goobstation
    - type: TriggerOnMobstateChange
      mobState:
      - Critical
    - type: Rattle
      radioChannel: "Security"

#Traitor implants

- type: entity
  parent: BaseSubdermalImplant
  id: StorageImplant
  name: "імплант зберігання"
  description: "Цей імплант забезпечує приховане зберігання даних у тілі людини за допомогою технології bluespace."
  categories: [ HideSpawnMenu ]
  components:
    - type: SubdermalImplant
      implantAction: ActionOpenStorageImplant
      whitelist:
        components:
        - Hands # no use giving a mouse a storage implant, but a monkey is another story...
    - type: Storage
      grid:
      - 0,0,2,2
    - type: ContainerContainer
      containers:
        storagebase: !type:Container
          ents: [ ]
    - type: UserInterface
      interfaces:
        enum.StorageUiKey.Key:
          type: StorageBoundUserInterface

- type: entity
  parent: BaseSubdermalImplant
  id: FreedomImplant
  name: "імплант свободи"
  description: "Цей імплант дозволяє користувачеві до трьох разів звільнитися від наручників, перш ніж він перестане функціонувати."
  categories: [ HideSpawnMenu ]
  components:
    - type: SubdermalImplant
      implantAction: ActionActivateFreedomImplant
      whitelist:
        components:
        - Cuffable # useless if you cant be cuffed

- type: entity
  parent: [ BaseSubdermalImplant, StorePresetUplink ]
  id: UplinkImplant
  name: "імплант аплінку"
  description: "Цей імплант дозволяє користувачеві отримати доступ до прихованого каналу зв'язку Синдикату за власним бажанням."
  categories: [ HideSpawnMenu ]
  components:
  - type: SubdermalImplant
    implantAction: ActionOpenUplinkImplant
    whitelist:
      components:
      - Hands # prevent mouse buying grenade penguin since its not telepathic
  - type: Store
    balance:
      Telecrystal: 0
  - type: UserInterface
    interfaces:
      enum.StoreUiKey.Key:
        type: StoreBoundUserInterface

- type: entity
  parent: BaseSubdermalImplant
  id: EmpImplant
  name: "імплант ЕМІ"
  description: "Цей імплант створює електромагнітний імпульс при активації."
  categories: [ HideSpawnMenu ]
  components:
    - type: SubdermalImplant
      implantAction: ActionActivateEmpImplant
    - type: TriggerImplantAction
    - type: EmpOnTrigger
      range: 1.75
      energyConsumption: 50000
      disableDuration: 10

- type: entity
  parent: BaseSubdermalImplant
  id: ScramImplant
  name: "імплант телепортера"
  description: "При активації цей імплант випадковим чином телепортує користувача у великому радіусі."
  categories: [ HideSpawnMenu ]
  components:
    - type: SubdermalImplant
      implantAction: ActionActivateScramImplant
    - type: TriggerImplantAction
    - type: ScramImplant

- type: entity
  parent: BaseSubdermalImplant
  id: DnaScramblerImplant
  name: "Імплант скремблера ДНК"
  description: "Цей імплант дозволяє користувачеві один раз випадковим чином змінити свою зовнішність та ім'я."
  categories: [ HideSpawnMenu ]
  components:
    - type: SubdermalImplant
      implantAction: ActionActivateDnaScramblerImplant
      whitelist:
        components:
        - HumanoidAppearance # syndies cant turn hamlet into a human

#Nuclear Operative/Special Exclusive implants

- type: entity
  parent: BaseSubdermalImplant
  id: MicroBombImplant
  name: "імплант мікробомби"
  description: "Цей імплант підриває користувача при активації або після смерті."
  categories: [ HideSpawnMenu ]
  components:
    - type: SubdermalImplant
      permanent: true
      implantAction: ActionActivateMicroBomb
    - type: TriggerOnMobstateChange
      mobState:
      - Dead
    - type: TriggerImplantAction
    - type: ExplodeOnTrigger
    - type: GibOnTrigger
      deleteItems: true
    - type: Explosive
      explosionType: MicroBomb
      totalIntensity: 120
      intensitySlope: 5
      maxIntensity: 30
      canCreateVacuum: false
    - type: Tag
      tags:
        - SubdermalImplant
        - HideContextMenu
        - MicroBomb


- type: entity
  parent: BaseSubdermalImplant
  id: MacroBombImplant
  name: "імплант макробомби"
  description: "Цей імплант створює великий вибух при смерті після запрограмованого відліку часу."
  categories: [ HideSpawnMenu ]
  components:
    - type: SubdermalImplant
      permanent: true
    - type: TriggerOnMobstateChange #Chains with OnUseTimerTrigger
      mobState:
      - Dead
      preventSuicide: true
    - type: OnUseTimerTrigger
      delay: 7
      initialBeepDelay: 0
      beepSound:
        path: /Audio/Machines/Nuke/general_beep.ogg
        params:
          volume: -2
    - type: ExplodeOnTrigger
    - type: GibOnTrigger
      deleteItems: true
    - type: Explosive
      explosionType: Default
      totalIntensity: 3500
      intensitySlope: 15
      maxIntensity: 70
      canCreateVacuum: true
    - type: Tag
      tags:
        - SubdermalImplant
        - HideContextMenu
        - MacroBomb

- type: entity
  parent: BaseSubdermalImplant
  id: DeathAcidifierImplant
  name: "імплант смертельного розплавлення"
  description: "Цей імплант розплавляє користувача та його обладнання після смерті."
  categories: [ HideSpawnMenu ]
  components:
  - type: SubdermalImplant
    permanent: true
    implantAction: ActionActivateDeathAcidifier
  - type: TriggerOnMobstateChange
    mobState:
    - Dead
  - type: TriggerImplantAction
  - type: GibOnTrigger
    deleteItems: true
  - type: SpawnOnTrigger
    proto: Acidifier
  - type: Tag
    tags:
    - SubdermalImplant
    - HideContextMenu
    - DeathAcidifier

- type: entity
  parent: BaseSubdermalImplant
  id: DeathRattleImplant
  name: "імплант брязкальця смерті"
  description: "Цей імплант повідомить радіоканал Синдикату, якщо користувач потрапить у критичний стан або помре."
  categories: [ HideSpawnMenu ]
  components:
    - type: SubdermalImplant
      permanent: true
      whitelist:
        components:
        - MobState # admeme implanting a chair with rattle implant needs to give the chair mobstate so it can die first
    - type: MansusGraspBlockTrigger # Goobstation
    - type: TriggerOnMobstateChange
      mobState:
      - Critical
      - Dead
    - type: Rattle

# Sec and Command implants

- type: entity
  parent: BaseSubdermalImplant
  id: MindShieldImplant
  name: "імплант розумового щита"
  description: "Цей імплант забезпечить лояльність до \"НаноТрейзену\" і захистить від пристроїв контролю свідомості."
  categories: [ HideSpawnMenu ]
  components:
   - type: SubdermalImplant
     permanent: true
   - type: Tag
     tags:
       - MindShield

# Example component adding implant
- type: entity
  parent: BaseSubdermalImplant
  id: ComponentImplant
  name: "Додати компонент DEBUG"
  description: "Приклад компонента, що додає імплант"
  categories: [ HideSpawnMenu ]
  components:
  - type: SubdermalImplant
  - type: AddComponentsImplant
    componentsToAdd:
    - type: Dispellable