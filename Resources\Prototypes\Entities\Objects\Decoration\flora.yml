- type: entity
  id: BaseRock
  name: "валун"
  description: "Важ<PERSON>ий, як дуже важка річ."
  abstract: true
  components:
  - type: Clickable
  - type: Sprite
    noRot: true
    sprite: Objects/Decoration/Flora/flora_rockssolid.rsi
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.4
        density: 1000
        layer:
        - HalfWallLayer
        - Opaque
  - type: Climbable
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 200
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]

- type: entity
  id: BaseTree
  description: "Так, це дерево."
  abstract: true
  components:
  - type: SpriteFade
  - type: Clickable
  - type: Sprite
    noRot: true
    sprite: Objects/Decoration/Flora/flora_trees.rsi
    drawdepth: Overdoors
    offset: 0,0.9
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.15,-0.2,0.15,0.2" # DeltaV: make it smaller
        density: 1000
        layer:
        - WallLayer
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Wood
  - type: MeleeSound
    soundGroups:
      Brute:
        path: /Audio/Effects/chop.ogg
        params:
          variation: 0.05
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 400
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          path: /Audio/Effects/tree_fell.ogg
          params:
            volume: 5
            variation: 0.05
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:SpawnEntitiesBehavior
        spawn:
          Log:
            min: 2
            max: 8

- type: entity
  parent: BaseTree
  id: BaseTreeSnow
  abstract: true
  components:
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_treessnow.rsi
    offset: 0,0.7
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.1,-0.2,0.1,0.2" # DeltaV: make it smaller
        density: 4000
        layer:
        - WallLayer

- type: entity
  parent: BaseTree
  id: BaseTreeLarge
  abstract: true
  components:
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_treeslarge.rsi
    offset: 0,1.55
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.18,-0.2,0.18,0.2" # DeltaV: make it smaller
        density: 2000
        layer:
        - WallLayer

- type: entity
  parent: BaseTree
  id: BaseTreeConifer
  abstract: true
  components:
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_treesconifer.rsi
    offset: 0,1.15
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.1,-0.2,0.1,0.2" # DeltaV: make it smaller
        density: 3500
        layer:
        - WallLayer

- type: entity
  parent: BaseRock
  id: FloraRockSolid01
  components:
  - type: Sprite
    state: rocksolid01

- type: entity
  parent: BaseRock
  id: FloraRockSolid02
  components:
  - type: Sprite
    state: rocksolid02

- type: entity
  parent: BaseRock
  id: FloraRockSolid03
  components:
  - type: Sprite
    state: rocksolid03

- type: entity
  parent: BaseRock
  id: FloraRockSolid
  components:
  - type: Sprite
    layers:
    - state: rocksolid01
      map: ["random"]
  - type: RandomSprite
    available:
    - random:
        rocksolid01: ""
        rocksolid02: ""
        rocksolid03: ""

- type: entity
  name: "сталагміт"
  description: "Шипи з натурального каменю."
  parent: BaseRock
  id: FloraStalagmite1
  components:
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 5
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
          - !type:PlaySoundBehavior
            sound:
              collection: GlassBreak
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_stalagmite.rsi
    state: stalagmite1

- type: entity
  parent: FloraStalagmite1
  id: FloraStalagmite2
  components:
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_stalagmite.rsi
    state: stalagmite2

- type: entity
  parent: FloraStalagmite1
  id: FloraStalagmite3
  components:
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_stalagmite.rsi
    state: stalagmite3

- type: entity
  parent: FloraStalagmite1
  id: FloraStalagmite4
  components:
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_stalagmite.rsi
    state: stalagmite4

- type: entity
  parent: FloraStalagmite1
  id: FloraStalagmite5
  components:
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_stalagmite.rsi
    state: stalagmite5

- type: entity
  parent: FloraStalagmite1
  id: FloraStalagmite6
  components:
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_stalagmite.rsi
    state: stalagmite6

- type: entity
  parent: FloraStalagmite1
  id: FloraGreyStalagmite1
  components:
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_stalagmite.rsi
    state: grey_stalagmite1

- type: entity
  parent: FloraGreyStalagmite1
  id: FloraGreyStalagmite2
  components:
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_stalagmite.rsi
    state: grey_stalagmite2

- type: entity
  parent: FloraGreyStalagmite1
  id: FloraGreyStalagmite3
  components:
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_stalagmite.rsi
    state: grey_stalagmite3

- type: entity
  parent: FloraGreyStalagmite1
  id: FloraGreyStalagmite4
  components:
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_stalagmite.rsi
    state: grey_stalagmite4

- type: entity
  parent: FloraGreyStalagmite1
  id: FloraGreyStalagmite5
  components:
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_stalagmite.rsi
    state: grey_stalagmite5

- type: entity
  parent: FloraGreyStalagmite1
  id: FloraGreyStalagmite6
  components:
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_stalagmite.rsi
    state: grey_stalagmite6


- type: entity
  parent: BaseTree
  id: FloraTree
  name: "дерево"
  components:
  - type: Sprite
    layers:
    - state: tree01
      map: ["random"]
  - type: RandomSprite
    available:
    - random:
        tree01: ""
        tree02: ""
        tree03: ""
        tree04: ""
        tree05: ""
        tree06: ""
        
- type: entity
  parent: BaseTreeSnow
  id: FloraTreeSnow
  name: "засніжене дерево"
  components:
  - type: Sprite
    layers:
    - state: treesnow01
      map: ["random"]
  - type: RandomSprite
    available:
    - random:
        treesnow01: ""
        treesnow02: ""
        treesnow03: ""
        treesnow04: ""
        treesnow05: ""
        treesnow06: ""

- type: entity
  parent: BaseTreeConifer
  id: FloraTreeConifer
  name: "засніжене хвойне дерево"
  components:
  - type: Sprite
    layers:
    - state: treeconifer01
      map: ["random"]
  - type: RandomSprite
    available:
    - random:
        treeconifer01: ""
        treeconifer02: ""
        treeconifer03: ""

- type: entity
  parent: BaseTreeLarge
  id: FloraTreeLarge
  name: "велике дерево"
  components:
  - type: Sprite
    layers:
    - state: treelarge01
      map: ["random"]
  - type: RandomSprite
    available:
    - random:
        treelarge01: ""
        treelarge02: ""
        treelarge03: ""
        treelarge04: ""
        treelarge05: ""
        treelarge06: ""

- type: entity
  parent: BaseTree
  id: FloraTree01
  name: "дерево"
  components:
  - type: Sprite
    state: tree01

- type: entity
  parent: BaseTree
  id: FloraTree02
  name: "дерево"
  components:
  - type: Sprite
    state: tree02

- type: entity
  parent: BaseTree
  id: FloraTree03
  name: "дерево"
  components:
  - type: Sprite
    state: tree03

- type: entity
  parent: BaseTree
  id: FloraTree04
  name: "дерево"
  components:
  - type: Sprite
    state: tree04

- type: entity
  parent: BaseTree
  id: FloraTree05
  name: "дерево"
  components:
  - type: Sprite
    state: tree05

- type: entity
  parent: BaseTree
  id: FloraTree06
  name: "дерево"
  components:
  - type: Sprite
    state: tree06

- type: entity
  parent: BaseTreeSnow
  id: FloraTreeSnow01
  name: "засніжене дерево"
  components:
  - type: Sprite
    state: treesnow01

- type: entity
  parent: BaseTreeSnow
  id: FloraTreeSnow02
  name: "засніжене дерево"
  components:
  - type: Sprite
    state: treesnow02

- type: entity
  parent: BaseTreeSnow
  id: FloraTreeSnow03
  name: "засніжене дерево"
  components:
  - type: Sprite
    state: treesnow03

- type: entity
  parent: BaseTreeSnow
  id: FloraTreeSnow04
  name: "засніжене дерево"
  components:
  - type: Sprite
    state: treesnow04

- type: entity
  parent: BaseTreeSnow
  id: FloraTreeSnow05
  name: "засніжене дерево"
  components:
  - type: Sprite
    state: treesnow05

- type: entity
  parent: BaseTreeSnow
  id: FloraTreeSnow06
  name: "засніжене дерево"
  components:
  - type: Sprite
    state: treesnow06

- type: entity
  parent: BaseTreeSnow
  id: FloraTreeStump
  name: "пеньок"
  components:
  - type: Sprite
    state: treestump

- type: entity
  parent: BaseTreeLarge
  id: FloraTreeLarge01
  name: "велике дерево"
  components:
  - type: Sprite
    state: treelarge01

- type: entity
  parent: BaseTreeLarge
  id: FloraTreeLarge02
  name: "велике дерево"
  components:
  - type: Sprite
    state: treelarge02

- type: entity
  parent: BaseTreeLarge
  id: FloraTreeLarge03
  name: "велике дерево"
  components:
  - type: Sprite
    state: treelarge03

- type: entity
  parent: BaseTreeLarge
  id: FloraTreeLarge04
  name: "велике дерево"
  components:
  - type: Sprite
    state: treelarge04

- type: entity
  parent: BaseTreeLarge
  id: FloraTreeLarge05
  name: "велике дерево"
  components:
  - type: Sprite
    state: treelarge05

- type: entity
  parent: BaseTreeLarge
  id: FloraTreeLarge06
  name: "велике дерево"
  components:
  - type: Sprite
    state: treelarge06

- type: entity
  parent: BaseTreeConifer
  id: FloraTreeConifer01
  name: "засніжене хвойне дерево"
  components:
  - type: Sprite
    state: treeconifer01

- type: entity
  parent: BaseTreeConifer
  id: FloraTreeConifer02
  name: "засніжене хвойне дерево"
  components:
  - type: Sprite
    state: treeconifer02

- type: entity
  parent: BaseTreeConifer
  id: FloraTreeConifer03
  name: "засніжене хвойне дерево"
  components:
  - type: Sprite
    state: treeconifer03

- type: entity
  parent: BaseTreeConifer
  id: FloraTreeChristmas01
  name: "ялинка"
  components:
  - type: Sprite
    state: treechristmas01

- type: entity
  parent: BaseTreeConifer
  id: FloraTreeChristmas02
  suffix: PresentsGiver
  name: "ялинка"
  components:
  - type: Sprite
    state: treechristmas02
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.5,0,0.5,0.75"
        density: 4500
        layer:
        - WallLayer
  - type: LimitedItemGiver
    spawnEntries:
      - id: PresentRandom
        orGroup: present
      - id: PresentRandomCash
        prob: 0.20
        orGroup: present
      - id: PresentRandomAsh
        prob: 0.05
        orGroup: present
    receivedPopup: christmas-tree-got-gift
    deniedPopup: christmas-tree-no-gift
    requiredHoliday: FestiveSeason

- type: entity
  parent: BaseTreeConifer
  id: FloraTreeStumpConifer
  name: "пеньок"
  components:
  - type: Sprite
    state: treestumpconifer

- type: entity
  parent: FloraTree01
  id: ShadowTree01
  name: "темне дерево"
  description: "Листя шепоче про тебе."
  components:
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_shadow_trees.rsi
    state: tree01

- type: entity
  parent: ShadowTree01
  id: ShadowTree02
  components:
  - type: Sprite
    state: tree02

- type: entity
  parent: ShadowTree01
  id: ShadowTree03
  components:
  - type: Sprite
    state: tree03

- type: entity
  parent: ShadowTree01
  id: ShadowTree04
  components:
  - type: Sprite
    state: tree04

- type: entity
  parent: ShadowTree01
  id: ShadowTree05
  components:
  - type: Sprite
    state: tree05

- type: entity
  parent: ShadowTree01
  id: ShadowTree06
  components:
  - type: Sprite
    state: tree06

- type: entity
  parent: BaseTree
  id: LightTree01
  name: "сяюче дерево"
  description: "дивовижне дерево, наповнене дивною енергією."
  components:
  - type: PointLight
    radius: 2.0
    energy: 4.5
    color: "#6270bb"
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_treeslight.rsi
    state: tree01
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 400
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 25
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          path: /Audio/Effects/tree_fell.ogg
          params:
            volume: 5
            variation: 0.05
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:SpawnEntitiesBehavior
        spawn:
          Log:
            min: 0
            max: 1
      - !type:SpawnEntitiesBehavior
        spawn:
          MobLuminousObject:
            min: 0
            max: 1

- type: entity
  parent: LightTree01
  id: LightTree02
  components:
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_treeslight.rsi
    state: tree02

- type: entity
  parent: LightTree01
  id: LightTree03
  components:
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_treeslight.rsi
    state: tree03

- type: entity
  parent: LightTree01
  id: LightTree04
  components:
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_treeslight.rsi
    state: tree04

- type: entity
  parent: LightTree01
  id: LightTree05
  components:
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_treeslight.rsi
    state: tree05

- type: entity
  parent: LightTree01
  id: LightTree06
  components:
  - type: Sprite
    sprite: Objects/Decoration/Flora/flora_treeslight.rsi
    state: tree06
