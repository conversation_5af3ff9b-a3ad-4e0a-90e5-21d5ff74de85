- type: construction
  name: "балка"
  id: Girder
  graph: Girder
  startNode: start
  targetNode: girder
  category: construction-category-structures
  description: "Великий конструктивний вузол з металу."
  icon:
    sprite: /Textures/Structures/Walls/solid.rsi
    state: wall_girder
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "посилена балка"
  id: ReinforcedGirder
  graph: Girder
  startNode: start
  targetNode: reinforcedGirder
  category: construction-category-structures
  description: "Великий конструктивний вузол, виготовлений з металу та сталі."
  icon:
    sprite: /Textures/Structures/Walls/solid.rsi
    state: reinforced_wall_girder
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "настінне обладнання"
  id: ClockworkGirder
  graph: ClockworkGirder
  startNode: start
  targetNode: clockGirder
  category: construction-category-structures
  description: "Велика шестерня з монтажними кронштейнами для додаткового покриття."
  icon:
    sprite: /Textures/Structures/Walls/clock.rsi
    state: wall_gear
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "стіна"
  id: Wall
  graph: Girder
  startNode: start
  targetNode: wall
  category: construction-category-structures
  description: "Утримує повітря всередині та грейтайдів зовні."
  icon:
    sprite: Structures/Walls/solid.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "армована стіна"
  id: ReinforcedWall
  graph: Girder
  startNode: start
  targetNode: reinforcedWall
  category: construction-category-structures
  description: "Утримує повітря всередині та грейтайдів зовні."
  icon:
    sprite: Structures/Walls/solid.rsi
    state: rgeneric
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "стіна з годинником"
  id: WallClock
  graph: ClockworkGirder
  startNode: start
  targetNode: clockworkWall
  category: construction-category-structures
  description: "Утримує повітря всередині та грейтайдів зовні."
  icon:
    sprite: Structures/Walls/clock.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked
# here
- type: construction
  name: "дерев'яна стіна"
  id: WoodWall
  graph: Girder
  startNode: start
  targetNode: woodWall
  category: construction-category-structures
  description: "Утримує повітря всередині та грейтайдів зовні."
  icon:
    sprite: Structures/Walls/wood.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "уранова стіна"
  id: UraniumWall
  graph: Girder
  startNode: start
  targetNode: uraniumWall
  category: construction-category-structures
  description: "Утримує повітря всередині та грейтайдів зовні."
  icon:
    sprite: Structures/Walls/uranium.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "срібна стіна"
  id: SilverWall
  graph: Girder
  startNode: start
  targetNode: silverWall
  category: construction-category-structures
  description: "Утримує повітря всередині та грейтайдів зовні."
  icon:
    sprite: Structures/Walls/silver.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "пластикова стіна"
  id: PlasticWall
  graph: Girder
  startNode: start
  targetNode: plasticWall
  category: construction-category-structures
  description: "Утримує повітря всередині та грейтайдів зовні."
  icon:
    sprite: Structures/Walls/plastic.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "плазмова стіна"
  id: PlasmaWall
  graph: Girder
  startNode: start
  targetNode: plasmaWall
  category: construction-category-structures
  description: "Утримує повітря всередині та грейтайдів зовні."
  icon:
    sprite: Structures/Walls/plasma.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "золота стіна"
  id: GoldWall
  graph: Girder
  startNode: start
  targetNode: goldWall
  category: construction-category-structures
  description: "Утримує повітря всередині та грейтайдів зовні."
  icon:
    sprite: Structures/Walls/gold.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "стіна клоуна"
  id: ClownWall
  graph: Girder
  startNode: start
  targetNode: bananiumWall
  category: construction-category-structures
  description: "Утримує повітря всередині та грейтайдів зовні."
  icon:
    sprite: Structures/Walls/clown.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "м'ясна стіна"
  id: MeatWall
  graph: Girder
  startNode: start
  targetNode: meatWall
  category: construction-category-structures
  description: "Липка."
  icon:
    sprite: Structures/Walls/meat.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

- type: construction
  name: "решітка"
  id: Grille
  graph: Grille
  startNode: start
  targetNode: grille
  category: construction-category-structures
  description: "Хиткий каркас із залізних стрижнів."
  conditions:
    - !type:TileNotBlocked
      failIfSpace: false
  icon:
    sprite: Structures/Walls/grille.rsi
    state: grille
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false

- type: construction
  name: "годинникова решітка"
  id: ClockGrille
  graph: ClockGrille
  startNode: start
  targetNode: clockGrille
  category: construction-category-structures
  description: "Тендітний каркас із залізних стрижнів, зібраний у традиційній ратварській манері."
  conditions:
    - !type:TileNotBlocked
      failIfSpace: false
  icon:
    sprite: Structures/Walls/clockwork_grille.rsi
    state: ratvargrille
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false

- type: construction
  name: "діагональна решітка"
  id: GrilleDiagonal
  graph: GrilleDiagonal
  startNode: start
  targetNode: grilleDiagonal
  category: construction-category-structures
  description: "Хиткий каркас із залізних стрижнів."
  conditions:
    - !type:TileNotBlocked
      failIfSpace: false
  icon:
    sprite: Structures/Walls/grille.rsi
    state: grille_diagonal
  objectType: Structure
  placementMode: SnapgridCenter

- type: construction
  name: "діагональна решітка з годинниковим механізмом"
  id: ClockworkGrilleDiagonal
  graph: GrilleDiagonal
  startNode: start
  targetNode: clockworkGrilleDiagonal
  category: construction-category-structures
  description: "Тендітний каркас із залізних стрижнів, зібраний у традиційній ратварській манері."
  conditions:
    - !type:TileNotBlocked
      failIfSpace: false
  icon:
    sprite: Structures/Walls/clockwork_grille.rsi
    state: ratvargrille_diagonal
  objectType: Structure
  placementMode: SnapgridCenter

- type: construction
  name: "вікно"
  id: Window
  graph: Window
  startNode: start
  targetNode: window
  category: construction-category-structures
  description: "Чисто."
  canBuildInImpassable: true
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/window.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false

- type: construction
  name: "діагональне вікно"
  id: WindowDiagonal
  graph: WindowDiagonal
  startNode: start
  targetNode: windowDiagonal
  category: construction-category-structures
  description: "Чисто."
  canBuildInImpassable: true
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/window_diagonal.rsi
    state: state1
  objectType: Structure
  placementMode: SnapgridCenter

- type: construction
  name: "армоване вікно"
  id: ReinforcedWindow
  graph: Window
  startNode: start
  targetNode: reinforcedWindow
  category: construction-category-structures
  description: "Чітко, але жорстко."
  canBuildInImpassable: true
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/reinforced_window.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false

- type: construction
  name: "діагональне армоване вікно"
  id: ReinforcedWindowDiagonal
  graph: WindowDiagonal
  startNode: start
  targetNode: reinforcedWindowDiagonal
  category: construction-category-structures
  description: "Чітко, але жорстко."
  canBuildInImpassable: true
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/reinforced_window_diagonal.rsi
    state: state1
  objectType: Structure
  placementMode: SnapgridCenter

- type: construction
  name: "тоноване скло"
  id: TintedWindow
  graph: Window
  startNode: start
  targetNode: tintedWindow
  category: construction-category-structures
  description: "Не зрозуміло, але жорстко."
  canBuildInImpassable: true
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/tinted_window.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false

- type: construction
  name: "вікно з годинниковим механізмом"
  id: ClockworkWindow
  graph: Window
  startNode: start
  targetNode: clockworkWindow
  category: construction-category-structures
  description: "Прозорий і міцний, із золотистим відтінком."
  canBuildInImpassable: true
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/clockwork_window.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false

- type: construction
  name: "діагональне годинникове вікно"
  id: ClockworkWindowDiagonal
  graph: WindowDiagonal
  startNode: start
  targetNode: clockworkWindowDiagonal
  category: construction-category-structures
  description: "Прозорий і міцний, із золотистим відтінком."
  canBuildInImpassable: true
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/clockwork_diagonal.rsi
    state: state0
  objectType: Structure
  placementMode: SnapgridCenter

- type: construction
  name: "плазмове вікно"
  id: PlasmaWindow
  graph: Window
  startNode: start
  targetNode: plasmaWindow
  category: construction-category-structures
  canBuildInImpassable: true
  description: "Вибухостійке та ще міцніше, з фіолетовим відтінком."
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/plasma_window.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false

- type: construction
  name: "армоване плазмове вікно"
  id: ReinforcedPlasmaWindow
  graph: Window
  startNode: start
  targetNode: reinforcedPlasmaWindow
  category: construction-category-structures
  canBuildInImpassable: true
  description: "Дуже вибухостійке та ще міцніше, з фіолетовим відтінком."
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/reinforced_plasma_window.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false

- type: construction
  name: "діагональне плазмове вікно"
  id: PlasmaWindowDiagonal
  graph: WindowDiagonal
  startNode: start
  targetNode: plasmaWindowDiagonal
  category: construction-category-structures
  canBuildInImpassable: true
  description: "Вибухостійке та ще міцніше, з фіолетовим відтінком."
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/plasma_diagonal.rsi
    state: state1
  objectType: Structure
  placementMode: SnapgridCenter

- type: construction
  name: "діагональне армоване плазмове вікно"
  id: ReinforcedPlasmaWindowDiagonal
  graph: WindowDiagonal
  startNode: start
  targetNode: reinforcedPlasmaWindowDiagonal
  category: construction-category-structures
  canBuildInImpassable: true
  description: "Дуже вибухостійке та ще міцніше, з фіолетовим відтінком."
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/reinforced_plasma_diagonal.rsi
    state: state1
  objectType: Structure
  placementMode: SnapgridCenter

- type: construction
  name: "спрямоване вікно"
  id: WindowDirectional
  graph: WindowDirectional
  startNode: start
  targetNode: windowDirectional
  category: construction-category-structures
  description: "Чисто."
  canBuildInImpassable: true
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/directional.rsi
    state: window
  objectType: Structure
  placementMode: SnapgridCenter

- type: construction
  name: "спрямоване армоване вікно"
  id: WindowReinforcedDirectional
  graph: WindowDirectional
  startNode: start
  targetNode: windowReinforcedDirectional
  category: construction-category-structures
  description: "Чітко, але жорстко."
  canBuildInImpassable: true
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/directional.rsi
    state: reinforced_window
  objectType: Structure
  placementMode: SnapgridCenter

- type: construction
  name: "вікно з годинниковим механізмом"
  id: WindowClockworkDirectional
  graph: WindowDirectional
  startNode: start
  targetNode: windowClockworkDirectional
  category: construction-category-structures
  description: "Прозорий і міцний, із золотистим відтінком."
  canBuildInImpassable: true
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/directional.rsi
    state: clock_window
  objectType: Structure
  placementMode: SnapgridCenter

- type: construction
  name: "направлене плазмове вікно"
  id: PlasmaWindowDirectional
  graph: WindowDirectional
  startNode: start
  targetNode: plasmaWindowDirectional
  category: construction-category-structures
  canBuildInImpassable: true
  description: "Вибухостійке та ще міцніше, з фіолетовим відтінком."
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/directional.rsi
    state: plasma_window
  objectType: Structure
  placementMode: SnapgridCenter

- type: construction
  name: "направлене армоване плазмове вікно"
  id: PlasmaReinforcedWindowDirectional
  graph: WindowDirectional
  startNode: start
  targetNode: plasmaReinforcedWindowDirectional
  category: construction-category-structures
  canBuildInImpassable: true
  description: "Дуже вибухостійке та ще міцніше, з фіолетовим відтінком."
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/directional.rsi
    state: plasma_reinforced_window
  objectType: Structure
  placementMode: SnapgridCenter

- type: construction
  name: "уранове вікно"
  id: UraniumWindow
  graph: Window
  startNode: start
  targetNode: uraniumWindow
  category: construction-category-structures
  canBuildInImpassable: true
  description: "Прозоре і набагато міцніше за звичайне скло, з додаванням RadAbsorb для захисту від смертельної радіації."
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/uranium_window.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false

- type: construction
  name: "армоване уранове вікно"
  id: ReinforcedUraniumWindow
  graph: Window
  startNode: start
  targetNode: reinforcedUraniumWindow
  category: construction-category-structures
  canBuildInImpassable: true
  description: "Прозоре і набагато міцніше за звичайне скло, з додаванням RadAbsorb для захисту від смертельної радіації."
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/reinforced_uranium_window.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false

- type: construction
  name: "діагональне уранове вікно"
  id: UraniumWindowDiagonal
  graph: WindowDiagonal
  startNode: start
  targetNode: uraniumWindowDiagonal
  category: construction-category-structures
  canBuildInImpassable: true
  description: "Прозоре і набагато міцніше за звичайне скло, з додаванням RadAbsorb для захисту від смертельної радіації."
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/uranium_window_diagonal.rsi
    state: state1
  objectType: Structure
  placementMode: SnapgridCenter

- type: construction
  name: "діагональне армоване уранове вікно"
  id: ReinforcedUraniumWindowDiagonal
  graph: WindowDiagonal
  startNode: start
  targetNode: reinforcedUraniumWindowDiagonal
  category: construction-category-structures
  canBuildInImpassable: true
  description: "Прозоре і набагато міцніше за звичайне скло, з додаванням RadAbsorb для захисту від смертельної радіації."
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/reinforced_uranium_diagonal.rsi
    state: state1
  objectType: Structure
  placementMode: SnapgridCenter

- type: construction
  name: "спрямоване уранове вікно"
  id: UraniumWindowDirectional
  graph: WindowDirectional
  startNode: start
  targetNode: uraniumWindowDirectional
  category: construction-category-structures
  canBuildInImpassable: true
  description: "Прозоре і міцніше за звичайне скло, з додаванням RadAbsorb для захисту від смертельної радіації."
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/directional.rsi
    state: uranium_window
  objectType: Structure
  placementMode: SnapgridCenter

- type: construction
  name: "спрямоване армоване уранове вікно"
  id: UraniumReinforcedWindowDirectional
  graph: WindowDirectional
  startNode: start
  targetNode: uraniumReinforcedWindowDirectional
  category: construction-category-structures
  canBuildInImpassable: true
  description: "Прозоре і набагато міцніше за звичайне скло, з додаванням RadAbsorb для захисту від смертельної радіації."
  conditions:
    - !type:EmptyOrWindowValidInTile
    - !type:NoWindowsInTile
  icon:
    sprite: Structures/Windows/directional.rsi
    state: uranium_reinforced_window
  objectType: Structure
  placementMode: SnapgridCenter

- type: construction
  name: "пожежний шлюз"
  id: Firelock
  graph: Firelock
  startNode: start
  targetNode: Firelock
  category: construction-category-structures
  description: "Це вогнестійкий шлюз - він блокує зону, коли в зоні спрацьовує пожежна сигналізація. Не дайте себе розчавити!"
  icon:
    sprite: Structures/Doors/Airlocks/Standard/firelock.rsi
    state: closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "скляний замок"
  id: FirelockGlass
  graph: Firelock
  startNode: start
  targetNode: FirelockGlass
  category: construction-category-structures
  description: "Це вогнестійкий шлюз - він блокує зону, коли в зоні спрацьовує пожежна сигналізація. Не дайте себе розчавити!"
  icon:
    sprite: Structures/Doors/Airlocks/Glass/firelock.rsi
    state: closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "тонкий вогневий шлюз"
  id: FirelockEdge
  graph: Firelock
  startNode: start
  targetNode: FirelockEdge
  category: construction-category-structures
  description: "Це вогнестійкий шлюз - він блокує зону, коли в зоні спрацьовує пожежна сигналізація. Не дайте себе розчавити!"
  icon:
    sprite: Structures/Doors/edge_door_hazard.rsi
    state: closed
  placementMode: SnapgridCenter
  objectType: Structure
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "жалюзі"
  id: Shutters
  graph: Shutters
  startNode: start
  targetNode: Shutters
  category: construction-category-structures
  description: "Це жалюзі - підключіть їх до кнопки, щоб відкривати і закривати."
  icon:
    sprite: Structures/Doors/Shutters/shutters.rsi
    state: closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true

- type: construction
  name: "скляні жалюзі"
  id: ShuttersWindow
  graph: Shutters
  startNode: start
  targetNode: ShuttersWindow
  category: construction-category-structures
  description: "Це жалюзі - підключіть їх до кнопки, щоб відкривати і закривати."
  icon:
    sprite: Structures/Doors/Shutters/shutters_window.rsi
    state: closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true

- type: construction
  name: "радіаційний затвор"
  id: ShuttersRadiation
  graph: Shutters
  startNode: start
  targetNode: ShuttersRadiation
  category: construction-category-structures
  description: "Це жалюзі - підключіть їх до кнопки, щоб відкривати і закривати."
  icon:
    sprite: Structures/Doors/Shutters/shutters_radiation.rsi
    state: closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true

- type: construction
  name: "противибухові двері"
  id: BlastDoor
  graph: BlastDoor
  startNode: start
  targetNode: blastdoor
  category: construction-category-structures
  description: "На цьому написано \"Вибуховий пристрій\"."
  icon:
    sprite: Structures/Doors/Shutters/blastdoor.rsi
    state: closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true

- type: construction
  name: "подіум"
  id: Catwalk
  graph: Catwalk
  startNode: start
  targetNode: Catwalk
  category: construction-category-structures
  description: "Як решітка. Тільки виглядає краще."
  conditions:
    - !type:TileNotBlocked
      failIfSpace: false
# Lavaland Change: add ability to build catwalks on any tiles
#    - !type:TileType
#      targets:
#        - Lattice
#        - Plating
  icon:
    sprite: Structures/catwalk.rsi
    state: catwalk_preview
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false

- type: construction
  name: "бананієва підлога"
  id: FloorBananium
  graph: FloorBananium
  startNode: start
  targetNode: BananiumFloor
  category: construction-category-structures
  description: "Слизька підлога з яскраво-жовтого бананію."
  conditions:
    - !type:TileNotBlocked
      failIfSpace: false
    - !type:TileType
      targets:
        - Plating
  icon:
    sprite: Tiles/Misc/bananium.rsi
    state: bananium
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false

- type: construction
  name: "дерев'яна барикада"
  id: Barricade
  graph: Barricade
  startNode: start
  targetNode: barricadefull
  category: construction-category-structures
  description: "Імпровізована барикада, зроблена з дерев'яних дощок."
  icon:
    sprite: Structures/barricades.rsi
    state: barricade_full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "дерев'яна барикада"
  id: BarricadeDirectional
  graph: BarricadeDirectional
  startNode: start
  targetNode: barricadefull
  category: construction-category-structures
  description: "Імпровізована барикада, зроблена з дерев'яних дощок."
  icon:
    sprite: Structures/barricades.rsi
    state: barricade_directional
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "перила"
  id: Railing
  graph: Railing
  startNode: start
  targetNode: railing
  category: construction-category-structures
  description: "Перила, призначені для захисту таких ідіотів, як ти, від падіння."
  icon:
    sprite: Structures/Walls/railing.rsi
    state: side
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

- type: construction
  name: "куточок перил"
  id: RailingCorner
  graph: Railing
  startNode: start
  targetNode: railingCorner
  category: construction-category-structures
  description: "Перила, призначені для захисту таких ідіотів, як ти, від падіння."
  icon:
    sprite: Structures/Walls/railing.rsi
    state: corner
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

- type: construction
  name: "перильний куточок малий"
  id: RailingCornerSmall
  graph: Railing
  startNode: start
  targetNode: railingCornerSmall
  category: construction-category-structures
  description: "Перила, призначені для захисту таких ідіотів, як ти, від падіння."
  icon:
    sprite: Structures/Walls/railing.rsi
    state: corner_small
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

- type: construction
  name: "перила круглі"
  id: RailingRound
  graph: Railing
  startNode: start
  targetNode: railingRound
  category: construction-category-structures
  description: "Перила, призначені для захисту таких ідіотів, як ти, від падіння."
  icon:
    sprite: Structures/Walls/railing.rsi
    state: round
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

# Chain link fencing
- type: construction
  name: "ланцюгова огорожа"
  id: FenceMetal
  graph: FenceMetal
  startNode: start
  targetNode: straight
  category: construction-category-structures
  description: "Частина ланцюгової огорожі, призначеної для огородження територій."
  icon:
    sprite: Structures/Walls/fence.rsi
    state: straight
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

- type: construction
  name: "кутова ланцюгова огорожа"
  id: FenceMetalCorner
  graph: FenceMetal
  startNode: start
  targetNode: corner
  category: construction-category-structures
  description: "Частина ланцюгової огорожі, призначеної для огородження територій."
  icon:
    sprite: Structures/Walls/fence.rsi
    state: corner
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

- type: construction
  name: "кінцева частина ланцюгової огорожі"
  id: FenceMetalEnd
  graph: FenceMetal
  startNode: start
  targetNode: end
  category: construction-category-structures
  description: "Частина ланцюгової огорожі, призначеної для огородження територій."
  icon:
    sprite: Structures/Walls/fence.rsi
    state: end
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

- type: construction
  name: "ворота для паркану з ланцюгової огорожі"
  id: FenceMetalGate
  graph: FenceMetal
  startNode: start
  targetNode: gate
  category: construction-category-structures
  description: "Простий спосіб пробратися через паркан із сітки рабиці."
  icon:
    sprite: Structures/Walls/fence.rsi
    state: door_closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

#Wooden fence high
- type: construction
  name: "дерев'яна висока огорожа"
  id: FenceWood
  graph: FenceWood
  startNode: start
  targetNode: straight
  category: construction-category-structures
  description: "Частина дерев'яної огорожі, призначеної для огородження територій."
  icon:
    sprite: Structures/Walls/wooden_fence.rsi
    state: straight
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

- type: construction
  name: "дерев'яна висока огорожа (кінець)"
  id: FenceWoodEnd
  graph: FenceWood
  startNode: start
  targetNode: end
  category: construction-category-structures
  description: "Частина дерев'яної огорожі, призначеної для огородження територій."
  icon:
    sprite: Structures/Walls/wooden_fence.rsi
    state: end
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

- type: construction
  name: "дерев'яна висока огорожа (кутова)"
  id: FenceWoodCorner
  graph: FenceWood
  startNode: start
  targetNode: corner
  category: construction-category-structures
  description: "Частина дерев'яної огорожі, призначеної для огородження територій."
  icon:
    sprite: Structures/Walls/wooden_fence.rsi
    state: corner
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

- type: construction
  name: "дерев'яна висока огорожа (Т-подібна)"
  id: FenceWoodTJunction
  graph: FenceWood
  startNode: start
  targetNode: tjunction
  category: construction-category-structures
  description: "Частина дерев'яної огорожі, призначеної для огородження територій."
  icon:
    sprite: Structures/Walls/wooden_fence.rsi
    state: tjunction
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

- type: construction
  name: "дерев'яна висока огорожа (ворота)"
  id: FenceWoodGate
  graph: FenceWood
  startNode: start
  targetNode: gate
  category: construction-category-structures
  description: "Частина дерев'яної огорожі, призначеної для огородження територій."
  icon:
    sprite: Structures/Walls/wooden_fence.rsi
    state: door_closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

#Wooden fence small
- type: construction
  name: "дерев'яна низька огорожа"
  id: FenceWoodSmall
  graph: FenceWood
  startNode: start
  targetNode: straight_small
  category: construction-category-structures
  description: "Частина дерев'яної огорожі, призначеної для огородження територій."
  icon:
    sprite: Structures/Walls/wooden_fence.rsi
    state: straight_small
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

- type: construction
  name: "дерев'яна низька огорожа (кінець)"
  id: FenceWoodEndSmall
  graph: FenceWood
  startNode: start
  targetNode: end_small
  category: construction-category-structures
  description: "Частина дерев'яної огорожі, призначеної для огородження територій."
  icon:
    sprite: Structures/Walls/wooden_fence.rsi
    state: end_small
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

- type: construction
  name: "дерев'яна низька огорожа (кутова)"
  id: FenceWoodCornerSmall
  graph: FenceWood
  startNode: start
  targetNode: corner_small
  category: construction-category-structures
  description: "Частина дерев'яної огорожі, призначеної для огородження територій."
  icon:
    sprite: Structures/Walls/wooden_fence.rsi
    state: corner_small
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

- type: construction
  name: "дерев'яна низька огорожа (Т-подібна)"
  id: FenceWoodTJunctionSmall
  graph: FenceWood
  startNode: start
  targetNode: tjunction_small
  category: construction-category-structures
  description: "Частина дерев'яної огорожі, призначеної для огородження територій."
  icon:
    sprite: Structures/Walls/wooden_fence.rsi
    state: tjunction_small
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

- type: construction
  name: "дерев'яна низька огорожа (ворота)"
  id: FenceWoodGateSmall
  graph: FenceWood
  startNode: start
  targetNode: gate_small
  category: construction-category-structures
  description: "Частина дерев'яної огорожі, призначеної для огородження територій."
  icon:
    sprite: Structures/Walls/wooden_fence.rsi
    state: door_closed_small
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

#Airlocks
- type: construction
  name: "повітряний шлюз"
  id: Airlock
  graph: Airlock
  startNode: start
  targetNode: airlock
  category: construction-category-structures
  description: "Він відкривається, закривається і, можливо, розчавить вас."
  icon:
    sprite: Structures/Doors/Airlocks/Standard/basic.rsi
    state: assembly
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "скляний шлюз"
  id: AirlockGlass
  graph: Airlock
  startNode: start
  targetNode: glassAirlock
  category: construction-category-structures
  description: "Він відкривається, закривається і, можливо, розчавить вас."
  icon:
    sprite: Structures/Doors/Airlocks/Glass/glass.rsi
    state: assembly
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "шестерний шлюз"
  id: PinionAirlock
  graph: PinionAirlock
  startNode: start
  targetNode: airlock
  category: construction-category-structures
  description: "Він відкривається, закривається і, можливо, розчавить вас."
  icon:
    sprite: Structures/Doors/Airlocks/Standard/clockwork_pinion.rsi
    state: assembly
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "скляний шестерний шлюз"
  id: PinionAirlockGlass
  graph: PinionAirlock
  startNode: start
  targetNode: glassAirlock
  category: construction-category-structures
  description: "Він відкривається, закривається і, можливо, розчавить вас."
  icon:
    sprite: Structures/Doors/Airlocks/Glass/clockwork_pinion.rsi
    state: assembly
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "шаттл-шлюз"
  id: AirlockShuttle
  graph: AirlockShuttle
  startNode: start
  targetNode: airlock
  category: construction-category-structures
  description: "Він відкривається, закривається і, можливо, розчавить вас. Необхідний для з'єднання двох космічних кораблів між собою."
  icon:
    sprite: Structures/Doors/Airlocks/Glass/shuttle.rsi
    state: closed
  #  state: assembly
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

- type: construction
  name: "скляний шатл-шлюз"
  id: AirlockGlassShuttle
  graph: AirlockShuttle
  startNode: start
  targetNode: airlockGlass
  category: construction-category-structures
  description: "Він відкривається, закривається і, можливо, розчавить вас. Необхідний для з'єднання двох космічних кораблів між собою. У цьому є вікно."
  icon:
    sprite: Structures/Doors/Airlocks/Glass/shuttle.rsi
    state: closed
  #  state: assembly
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "вікно"
  id: Windoor
  graph: Windoor
  startNode: start
  targetNode: windoor
  category: construction-category-structures
  description: "Він відкривається, закривається, і ви можете бачити крізь нього! І воно може бути зроблене з плазми, урану або звичайного скла!"
  icon:
    sprite: Structures/Doors/Windoors/windoor.rsi
    state: closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "надійні вікнодвері"
  id: SecureWindoor
  graph: Windoor
  startNode: start
  targetNode: windoorSecure
  category: construction-category-structures
  description: "Вони міцні, це двері, і ви можете бачити крізь них! І вони можуть бути зроблені з плазми, урану або звичайного скла!"
  icon:
    sprite: Structures/Doors/Windoors/windoor.rsi
    state: closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "годинникові вікнодвері"
  id: ClockworkWindoor
  graph: Windoor
  startNode: start
  targetNode: windoorClockwork
  category: construction-category-structures
  description: "Вони відкриваються, закриваються, і ви можете бачити крізь них! Ці виглядають міцними."
  icon:
    sprite: Structures/Doors/Windoors/clockwork_windoor.rsi
    state: closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

#lighting
- type: construction
  name: "настінний світильник"
  id: LightTubeFixture
  graph: LightFixture
  startNode: start
  targetNode: tubeLight
  category: construction-category-structures
  description: "Настінний світильник. Використовуйте лампи."
  icon:
    sprite: Structures/Wallmounts/Lighting/light_tube.rsi
    state: base
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: true
  canBuildInImpassable: false
  conditions:
  # Need an *additional* condition here that forces there to be a wall in the opposite direction to the one used for placement.
  # Also see below. Didn't add it b/c construction ECS work going on. Cheers, - 20kdc
    - !type:TileNotBlocked

- type: construction
  name: "малий настінний світильник"
  id: LightSmallFixture
  graph: LightFixture
  startNode: start
  targetNode: bulbLight
  category: construction-category-structures
  description: "Настінний світильник. Використовуйте лампочки."
  icon:
    sprite: Structures/Wallmounts/Lighting/light_small.rsi
    state: base
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: true
  canBuildInImpassable: false
  conditions:
  # Same here. - 20kdc
    - !type:TileNotBlocked

- type: construction
  name: "наземний ліхтарний стовп"
  id: LightGroundFixture
  graph: LightFixture
  startNode: start
  targetNode: groundLight
  category: construction-category-structures
  description: "Наземний світильник. Використовуйте лампочки."
  icon:
    sprite: Structures/Lighting/LightPosts/small_light_post.rsi
    state: base
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "стробоскоп"
  id: LightStrobeFixture
  graph: LightFixture
  startNode: start
  targetNode: strobeLight
  category: construction-category-structures
  description: "Настінний світильник. Використовуйте лампочки."
  icon:
    sprite: Structures/Wallmounts/Lighting/strobe_light.rsi
    state: base
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: true
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

#conveyor
- type: construction
  name: "конвеєрна стрічка"
  id: ConveyorBelt
  graph: ConveyorGraph
  startNode: start
  targetNode: entity
  category: construction-category-structures
  description: "Конвеєрна стрічка, яку зазвичай використовують для швидкого транспортування великої кількості предметів в інше місце."
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/conveyor.rsi
    state: conveyor_stopped_cw
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "металеві двері"
  id: MetalDoor
  graph: DoorGraph
  startNode: start
  targetNode: metalDoor
  category: construction-category-structures
  description: "Примітивні двері з ручним керуванням, як у печерних людей."
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Doors/MineralDoors/metal_door.rsi
    state: closed
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "дерев'яні двері"
  id: WoodDoor
  graph: DoorGraph
  startNode: start
  targetNode: woodDoor
  category: construction-category-structures
  description: "Примітивні двері з ручним керуванням, як у печерних людей."
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Doors/MineralDoors/wood_door.rsi
    state: closed
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "плазмові двері"
  id: PlasmaDoor
  graph: DoorGraph
  startNode: start
  targetNode: plasmaDoor
  category: construction-category-structures
  description: "Примітивні двері з ручним керуванням, як у печерних людей."
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Doors/MineralDoors/plasma_door.rsi
    state: closed
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "золоті двері"
  id: GoldDoor
  graph: DoorGraph
  startNode: start
  targetNode: goldDoor
  category: construction-category-structures
  description: "Примітивні двері з ручним керуванням, як у печерних людей."
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Doors/MineralDoors/gold_door.rsi
    state: closed
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "срібні двері"
  id: SilverDoor
  graph: DoorGraph
  startNode: start
  targetNode: silverDoor
  category: construction-category-structures
  description: "Примітивні двері з ручним керуванням, як у печерних людей."
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Doors/MineralDoors/silver_door.rsi
    state: closed
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "паперові двері"
  id: PaperDoor
  graph: DoorGraph
  startNode: start
  targetNode: paperDoor
  category: construction-category-structures
  description: "Примітивні двері з ручним керуванням, як у печерних людей."
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Doors/MineralDoors/paper_door.rsi
    state: closed

- type: construction
  name: "пластикові клапани"
  id: PlasticFlapsClear
  graph: PlasticFlapsGraph
  startNode: start
  targetNode: plasticFlaps
  category: construction-category-structures
  placementMode: SnapgridCenter
  description: "Пластиковий клапан, щоб пропускати предмети і не пускати людей."
  objectType: Structure
  canBuildInImpassable: false
  icon:
    sprite: Structures/plastic_flaps.rsi
    state: plasticflaps
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "непрозорі пластикові стулки"
  id: PlasticFlapsOpaque
  graph: PlasticFlapsGraph
  startNode: start
  targetNode: opaqueFlaps
  category: construction-category-structures
  placementMode: SnapgridCenter
  description: "Непрозорий пластиковий клапан, щоб пропускати предмети і не пускати людей."
  objectType: Structure
  canBuildInImpassable: false
  icon:
    sprite: Structures/plastic_flaps.rsi
    state: plasticflaps
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "бананієва статуя клоуна"
  id: BananiumClownStatue
  graph: BananiumStatueClown
  startNode: start
  targetNode: bananiumStatue
  category: construction-category-structures
  placementMode: SnapgridCenter
  description: "Статуя клоуна, зроблена з бананію."
  objectType: Structure
  canBuildInImpassable: false
  icon:
    sprite: Structures/Decoration/statues.rsi
    state: bananium_clown
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "бананіумні двері"
  id: BananiumDoor
  graph: DoorGraph
  startNode: start
  targetNode: bananiumDoor
  category: construction-category-structures
  description: "Примітивні двері з бананіуму, вони гудуть."
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Doors/MineralDoors/bananium_door.rsi
    state: closed
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "бананієвий вівтар"
  id: BananiumAltar
  graph: BananiumAltarGraph
  startNode: start
  targetNode: bananiumAltar
  category: construction-category-structures
  description: "Вівтар для поклоніння хонкматері."
  icon:
    sprite: Structures/Furniture/Altars/Cults/bananium.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: false
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "суцільні потайні двері"
  id: SolidSecretDoor
  graph: SecretDoor
  startNode: start
  targetNode: solidSecretDoor
  category: construction-category-structures
  description: "Потаємні двері в стіні."
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  icon:
    sprite: Structures/Doors/secret_door.rsi
    state: closed
  conditions:
    - !type:TileNotBlocked
