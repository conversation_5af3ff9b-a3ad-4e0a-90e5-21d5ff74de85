- type: entity
  parent: GasPipeBase
  abstract: true
  id: GasTrinaryBase
  placement:
    mode: SnapgridCenter
  components:
    - type: AtmosDevice
    - type: Tag
      tags:
        - Unstackable
    - type: SubFloorHide
      blockInteractions: false
      blockAmbience: false
    - type: NodeContainer
      nodes:
        inlet:
          !type:PipeNode
          nodeGroupID: Pipe
          pipeDirection: North
        filter:
          !type:PipeNode
          nodeGroupID: Pipe
          pipeDirection: West
        outlet:
          !type:PipeNode
          nodeGroupID: Pipe
          pipeDirection: South

- type: entity
  parent: GasTrinaryBase
  id: GasFilter
  name: "газовий фільтр"
  description: "Дуже корисний для фільтрації газів."
  placement:
    mode: SnapgridCenter
  components:
    - type: Sprite
      sprite: Structures/Piping/Atmospherics/gasfilter.rsi
      layers:
        - sprite: Structures/Piping/Atmospherics/pipe.rsi
          state: pipeTrinaryConnectors
          map: [ "enum.PipeVisualLayers.Pipe" ]
        - state: gasFilter
          map: [ "enum.SubfloorLayers.FirstLayer", "enabled" ]
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.FilterVisuals.Enabled:
          enabled:
            True: { state: gasFilterOn }
            False: { state: gasFilter }
    - type: PipeColorVisuals
    - type: UserInterface
      interfaces:
        enum.GasFilterUiKey.Key:
          type: GasFilterBoundUserInterface
    - type: GasFilter
      enabled: false
      transferRate: 1000
      maxTransferRate: 1000
    - type: Flippable
      mirrorEntity: GasFilterFlipped
    - type: Construction
      graph: GasTrinary
      node: filter
    - type: AmbientSound
      enabled: false
      volume: -9
      range: 5
      sound:
        path: /Audio/Ambience/Objects/gas_hiss.ogg
    - type: AtmosMonitoringConsoleDevice
      navMapBlip: GasFlowRegulator
    - type: StationAiWhitelist
    - type: VentCrawlerTube # Goobstation - ventcrawl
      containerId: VentCrawlerTube
    - type: VentCrawlerJunction # Goobstation - ventcrawl
      degrees:
        - 0
        - -90
        - 180
    - type: ContainerContainer # Goobstation - ventcrawl
      containers:
        VentCrawlerTube: !type:Container

- type: entity
  parent: GasFilter
  id: GasFilterFlipped
  name: "газовий фільтр"
  suffix: Flipped
  placement:
    mode: SnapgridCenter
  components:
  - type: Sprite
    sprite: Structures/Piping/Atmospherics/gasfilter.rsi
    layers:
    - sprite: Structures/Piping/Atmospherics/pipe.rsi
      state: pipeTrinaryConnectors
      map: [ "enum.PipeVisualLayers.Pipe" ]
    - state: gasFilterF
      map: [ "enum.SubfloorLayers.FirstLayer", "enabled" ]
  - type: Flippable
    mirrorEntity: GasFilter
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.FilterVisuals.Enabled:
        enabled:
          True: { state: gasFilterFOn }
          False: { state: gasFilterF }
  - type: Construction
    node: filterflipped

  - type: PipeColorVisuals
  - type: NodeContainer
    nodes:
      inlet:
        !type:PipeNode
        nodeGroupID: Pipe
        pipeDirection: South
      filter:
        !type:PipeNode
        nodeGroupID: Pipe
        pipeDirection: West
      outlet:
        !type:PipeNode
        nodeGroupID: Pipe
        pipeDirection: North
  - type: VentCrawlerTube # Goobstation - ventcrawl
    containerId: VentCrawlerTube
  - type: VentCrawlerJunction # Goobstation - ventcrawl
    degrees:
      - 0
      - -90
      - 180
  - type: ContainerContainer # Goobstation - ventcrawl
    containers:
      VentCrawlerTube: !type:Container

- type: entity
  parent: GasTrinaryBase
  id: GasMixer
  name: "газозмішувач"
  description: "Дуже корисний для змішування газів."
  placement:
    mode: SnapgridCenter
  components:
    - type: Sprite
      sprite: Structures/Piping/Atmospherics/gasmixer.rsi
      layers:
        - sprite: Structures/Piping/Atmospherics/pipe.rsi
          state: pipeTrinaryConnectors
          map: [ "enum.PipeVisualLayers.Pipe" ]
        - state: gasMixer
          map: [ "enum.SubfloorLayers.FirstLayer", "enabled" ]
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.FilterVisuals.Enabled:
          enabled:
            True: { state: gasMixerOn }
            False: { state: gasMixer }
    - type: PipeColorVisuals
    - type: UserInterface
      interfaces:
        enum.GasMixerUiKey.Key:
          type: GasMixerBoundUserInterface
    - type: GasMixer
      enabled: false
      inletOne: inlet
      inletTwo: filter
    - type: Flippable
      mirrorEntity: GasMixerFlipped
    - type: Construction
      graph: GasTrinary
      node: mixer
    - type: AmbientSound
      enabled: false
      volume: -9
      range: 5
      sound:
        path: /Audio/Ambience/Objects/gas_hiss.ogg
    - type: AtmosMonitoringConsoleDevice
      navMapBlip: GasFlowRegulator
    - type: StationAiWhitelist
    - type: VentCrawlerTube # Goobstation - ventcrawl
      containerId: VentCrawlerTube
    - type: VentCrawlerJunction # Goobstation - ventcrawl
      degrees:
        - 0
        - -90
        - 180
    - type: ContainerContainer # Goobstation - ventcrawl
      containers:
        VentCrawlerTube: !type:Container

- type: entity
  parent: GasMixer
  id: GasMixerFlipped
  name: "газозмішувач"
  suffix: Flipped
  placement:
    mode: SnapgridCenter
  components:
  - type: Sprite
    sprite: Structures/Piping/Atmospherics/gasmixer.rsi
    layers:
    - sprite: Structures/Piping/Atmospherics/pipe.rsi
      state: pipeTrinaryConnectors
      map: [ "enum.PipeVisualLayers.Pipe" ]
    - state: gasMixerF
      map: [ "enum.SubfloorLayers.FirstLayer", "enabled" ]
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.FilterVisuals.Enabled:
        enabled:
          True: { state: gasMixerFOn }
          False: { state: gasMixerF }
  - type: PipeColorVisuals
  - type: Flippable
    mirrorEntity: GasMixer
  - type: NodeContainer
    nodes:
      inlet:
        !type:PipeNode
        nodeGroupID: Pipe
        pipeDirection: South
      filter:
        !type:PipeNode
        nodeGroupID: Pipe
        pipeDirection: West
      outlet:
        !type:PipeNode
        nodeGroupID: Pipe
        pipeDirection: North
  - type: Construction
    node: mixerflipped
  - type: VentCrawlerTube # Goobstation - ventcrawl
    containerId: VentCrawlerTube
  - type: VentCrawlerJunction # Goobstation - ventcrawl
    degrees:
      - 0
      - -90
      - 180
  - type: ContainerContainer # Goobstation - ventcrawl
    containers:
      VentCrawlerTube: !type:Container

- type: entity
  parent: GasPipeBase
  id: PressureControlledValve
  name: "пневматичний клапан"
  description: "Клапан, керований тиском."
  placement:
    mode: SnapgridCenter
  components:
    - type: AtmosDevice
    - type: Tag
      tags:
        - Unstackable
    - type: SubFloorHide
      blockInteractions: false
      blockAmbience: false
    - type: NodeContainer
      nodes:
        inlet:
          !type:PipeNode
          nodeGroupID: Pipe
          pipeDirection: North
        control:
          !type:PipeNode
          nodeGroupID: Pipe
          pipeDirection: West
        outlet:
          !type:PipeNode
          nodeGroupID: Pipe
          pipeDirection: South
    - type: Sprite
      sprite: Structures/Piping/Atmospherics/pneumaticvalve.rsi
      layers:
        - sprite: Structures/Piping/Atmospherics/pipe.rsi
          state: pipeTrinaryConnectors
          map: [ "enum.PipeVisualLayers.Pipe" ]
        - state: off
          map: [ "enum.SubfloorLayers.FirstLayer", "enabled" ]
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.FilterVisuals.Enabled:
          enabled:
            True: { state: on }
            False: { state: off }
    - type: PipeColorVisuals
    - type: PressureControlledValve
    - type: AmbientSound
      enabled: false
      volume: -9
      range: 5
      sound:
        path: /Audio/Ambience/Objects/gas_hiss.ogg
    - type: Construction
      graph: GasTrinary
      node: pneumaticvalve
    - type: AtmosMonitoringConsoleDevice
      navMapBlip: GasFlowRegulator
    - type: StationAiWhitelist
    - type: VentCrawlerTube # Goobstation - ventcrawl
      containerId: VentCrawlerTube
    - type: VentCrawlerJunction # Goobstation - ventcrawl
      degrees:
        - 0
        - -90
        - 180
    - type: ContainerContainer # Goobstation - ventcrawl
      containers:
        VentCrawlerTube: !type:Container
