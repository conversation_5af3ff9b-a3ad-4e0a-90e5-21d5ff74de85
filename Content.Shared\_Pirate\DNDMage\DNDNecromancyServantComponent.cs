using Robust.Shared.GameStates;
using Robust.Shared.Serialization;

namespace Content.Shared._Pirate.DNDMage;

/// <summary>
/// Компонент для слуг некроманта, які були воскрешені за допомогою некромантського заклинання
/// </summary>
[RegisterComponent, NetworkedComponent, AutoGenerateComponentState]
public sealed partial class DNDNecromancyServantComponent : Component
{
    /// <summary>
    /// EntityUid мага-некроманта, якому служить цей слуга.
    /// Не серіалізується через DataField, оскільки EntityUid не підтримує серіалізацію.
    /// </summary>
    [AutoNetworkedField]
    public EntityUid Master { get; set; } = EntityUid.Invalid;

    /// <summary>
    /// Час, коли слуга був воскрешений
    /// </summary>
    [DataField, AutoNetworkedField]
    public TimeSpan ResurrectionTime { get; set; }

    /// <summary>
    /// Тривалість служіння (в секундах). Після цього часу слуга помре знову
    /// </summary>
    [DataField]
    public float ServantDuration = 300f; // 5 хвилин

    /// <summary>
    /// Чи повинен слуга слідувати за майстром
    /// </summary>
    [DataField]
    public bool ShouldFollowMaster = true;

    /// <summary>
    /// Максимальна відстань від майстра, на якій слуга може знаходитися
    /// </summary>
    [DataField]
    public float MaxDistanceFromMaster = 15f;

    /// <summary>
    /// Чи може слуга атакувати ворогів майстра
    /// </summary>
    [DataField]
    public bool CanAttackEnemies = true;

    /// <summary>
    /// Радіус, в якому слуга буде атакувати ворогів майстра
    /// </summary>
    [DataField]
    public float AttackRadius = 5f;

    /// <summary>
    /// Чи повинен слуга захищати майстра
    /// </summary>
    [DataField]
    public bool ShouldProtectMaster = true;

    /// <summary>
    /// Відсоток пошкодження, яке слуга ділить з майстром (як у Guardian системі)
    /// </summary>
    [DataField]
    public float DamageShare = 0.25f;

    /// <summary>
    /// Чи слуга активний (не в контейнері майстра)
    /// </summary>
    [DataField]
    public bool IsActive = true;

    /// <summary>
    /// Тип слуги залежно від оригінального типу трупа
    /// </summary>
    [DataField]
    public ServantType Type = ServantType.Basic;

    /// <summary>
    /// Рівень лояльності слуги (0-100)
    /// </summary>
    [DataField]
    public float Loyalty = 100f;

    /// <summary>
    /// Максимальна лояльність слуги
    /// </summary>
    [DataField]
    public float MaxLoyalty = 100f;

    /// <summary>
    /// Швидкість втрати лояльності за секунду
    /// </summary>
    [DataField]
    public float LoyaltyDecayRate = 0.5f;

    /// <summary>
    /// Поточна команда від майстра
    /// </summary>
    [DataField]
    public ServantCommand CurrentCommand = ServantCommand.Follow;
}

/// <summary>
/// Типи слуг некроманта
/// </summary>
public enum ServantType
{
    Basic,      // Звичайний слуга
    Warrior,    // Воїн (з зброєю)
    Guardian,   // Захисник (міцніший)
    Scout,      // Розвідник (швидший)
    Mage        // Магічний слуга (рідкісний)
}

/// <summary>
/// Команди для слуг некроманта
/// </summary>
public enum ServantCommand
{
    Follow,     // Слідувати за майстром
    Guard,      // Охороняти позицію
    Attack,     // Атакувати ціль
    Patrol,     // Патрулювати область
    Stay        // Залишатися на місці
}
