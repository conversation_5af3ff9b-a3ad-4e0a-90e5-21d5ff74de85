- type: entity
  id: WeaponWandBase
  abstract: true
  parent: [BaseItem] # Goob edit
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Basic/wands.rsi
  - type: Item
    heldPrefix: wand
    size: Normal
  - type: Gun
    soundEmpty: null # Goobstation
    soundMode: null # Goobstation
    clumsyProof: true # Goobstation
    fireRate: 0.5
    selectedMode: SemiAuto
    availableModes:
    - SemiAuto
  - type: AmmoCounter
  # These will only do anything if the effect layer is actually set.
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.AmmoVisuals.HasAmmo:
        effect:
          True: { visible: True }
          False: { visible: False }
  - type: Tag
    tags:
    - WizardWand
