defusable-popup-begun = {CAPITALIZE(THE($name))} beeps to life; its light is on!
defusable-popup-defuse = {CAPITALIZE(THE($name))} beeps one last time, as the light shuts off forever.
defusable-popup-boom = {CAPITALIZE(THE($name))} roars as the internal bomb explodes!
defusable-popup-fried = {CAPITALIZE(THE($name))} sparks, but fails to begin the countdown.
defusable-popup-cant-anchor = {CAPITALIZE(THE($name))} appears to be bolted to the ground!

defusable-popup-wire-bolt-pulse = The bolts spin in place for a moment.
defusable-popup-wire-proceed-pulse = {CAPITALIZE(THE($name))} buzzes ominously!
defusable-popup-wire-proceed-cut = The digital display on {THE($name)} deactivates.
defusable-popup-wire-chirp = {CAPITALIZE(THE($name))} chirps.
