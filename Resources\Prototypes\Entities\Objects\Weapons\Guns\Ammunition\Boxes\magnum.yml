- type: entity
  abstract: true
  parent: BaseMagazineBox
  id: BaseMagazineBoxMagnum
  components:
  - type: BallisticAmmoProvider
    mayTransfer: true
    whitelist:
      tags:
        - CartridgeMagnum
    proto: CartridgeMagnum
    capacity: 12
  - type: Item
    size: Small
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Boxes/magnum.rsi
  - type: MagazineVisuals
    magState: mag
    steps: 2
    zeroVisible: false
  - type: Appearance

# Boxes
- type: entity
  parent: BaseMagazineBoxMagnum
  id: MagazineBoxMagnum
  name: "коробочка з набоями (.45 magnum)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnum
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  parent: BaseMagazineBoxMagnum
  id: MagazineBoxMagnumPractice
  name: "коробочка з набоями (.45 magnum practice)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumPractice
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: practice

- type: entity
  parent: BaseMagazineBoxMagnum
  id: MagazineBoxMagnumRubber
  name: "коробка боєприпасів (.45 магнум гумовий)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumRubber
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: rubber

- type: entity
  id: MagazineBoxMagnumIncendiary
  parent: BaseMagazineBoxMagnum
  name: "коробка боєприпасів (.45 магнум запальний)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumIncendiary
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: incendiary

- type: entity
  id: MagazineBoxMagnumUranium
  parent: BaseMagazineBoxMagnum
  name: "коробочка з набоями (.45 магнум уран)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumUranium
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: uranium

- type: entity
  id: MagazineBoxMagnumAP
  parent: BaseMagazineBoxMagnum
  name: "коробочка з набоями (.45 магнум бронебійний)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumAP
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
    - state: piercing

- type: entity
  parent: BaseMagazineBoxMagnum
  id: MagazineBoxMagnumShrapnel
  name: "коробка боєприпасів (.45 магнум, шрапнельний)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeMagnumShrapnel
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
