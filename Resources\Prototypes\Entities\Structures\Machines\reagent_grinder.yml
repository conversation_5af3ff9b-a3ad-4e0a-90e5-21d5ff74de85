- type: entity
  id: KitchenReagentGrinder
  parent: [ BaseMachinePowered, SmallConstructibleMachine ]
  name: "подрібнювач реактивів"
  description: "Від BlenderTech. Чи змішається? Давайте дізнаємося!"
  suffix: grinder/juicer
  components:
  - type: Transform
    anchored: true
  - type: ReagentGrinder
  - type: ActivatableUI
    key: enum.ReagentGrinderUiKey.Key
  - type: UserInterface
    interfaces:
      enum.ReagentGrinderUiKey.Key:
        type: ReagentGrinderBoundUserInterface
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ReagentGrinderVisualState.BeakerAttached:
        grinder:
          True: {state: "juicer1"}
          False: {state: "juicer0"}
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.17,0,0.20,0.4"
        mask:
        - TabletopMachineMask
        layer:
        - TabletopMachineLayer
  - type: Sprite
    sprite: Structures/Machines/juicer.rsi
    drawdepth: SmallObjects
    snapCardinals: true
    offset: "0.0,0.4"
    layers:
    - map: [ "grinder" ]
      state: "juicer0"
  - type: ApcPowerReceiver
    powerLoad: 300
  - type: ItemSlots
    slots:
      beakerSlot:
        whitelist:
          components:
          - FitsInDispenser
  - type: Machine
    board: ReagentGrinderMachineCircuitboard
  - type: ContainerContainer
    containers:
      beakerSlot: !type:ContainerSlot
      inputContainer: !type:Container
      machine_board: !type:Container
      machine_parts: !type:Container

- type: entity
  parent: Recycler #too different so different parent
  id: ReagentGrinderIndustrial
  name: "промисловий подрібнювач реактивів"
  description: "Промисловий подрібнювач реактивів."
  components:
  - type: SolutionContainerManager
    solutions:
      output:
        maxVol: 400 #*slaps roof of machine* This baby can fit so much omnizine in it
  - type: MaterialReclaimer
    whitelist:
      components:
      - Extractable #same as reagent grinder
    blacklist:
      tags:
      - HighRiskItem #ian meat
    efficiency: 0.9
  - type: Sprite
    sprite: Structures/Machines/recycling.rsi
    layers:
      - state: grinder-b0
  - type: Machine
    board: ReagentGrinderIndustrialMachineCircuitboard
  - type: GenericVisualizer
    visuals:
      enum.ConveyorVisuals.State:
        enum.RecyclerVisualLayers.Main:
          Forward: { state: grinder-b1 }
          Reverse: { state: grinder-b1 }
          Off: { state: grinder-b0 }
  - type: ContainerContainer
    containers:
      machine_board: !type:Container
      machine_parts: !type:Container
  - type: Construction
    graph: Machine
    node: machine
    containers:
    - machine_parts
    - machine_board
  - type: DrainableSolution
    solution: output
  - type: ExaminableSolution
    solution: output
