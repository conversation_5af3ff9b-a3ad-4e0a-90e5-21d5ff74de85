- type: entity
  id: FloorLiquidPlasmaEntity
  name: "рідка плазма"
  description: "Солодкий, дорогий нектар. Не споживайте."
  placement:
    mode: SnapgridCenter
    snap:
    - Wall
  components:
  - type: TileEmission
    color: "#974988"
  - type: StepTrigger
    requiredTriggeredSpeed: 0
    intersectRatio: 0.1
    blacklist:
      tags:
      - Catwalk
    triggerGroups:
      types:
      - Lava
  - type: TileEntityEffect
    effects:
    - !type:FlammableReaction
      multiplier: 3.75
      multiplierOnExisting: 0.75
    - !type:Ignite
  # Goobstation - fishing
  - type: FishingSpot
    fishList: !type:NestedSelector
      tableId: PlasmaFishingLootTable
    fishDefaultTimer: 25.0
    fishTimerVariety: 15.0
  # Goobstation - fishing
  - type: Transform
    anchored: true
  - type: SyncSprite
  - type: Clickable
  - type: Sprite
    sprite: Tiles/Planet/liquid_plasma.rsi
    drawdepth: BelowFloor
    layers:
    - state: plasma
      shader: unshaded
  - type: Icon
    sprite: Tiles/Planet/liquid_plasma.rsi
    state: full
  - type: IconSmooth
    key: floor
    base: plasma
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.5,-0.5,0.5,0.5"
        layer:
        - SlipLayer
        mask:
        - ItemMask
        density: 1000
        hard: false
      # Goobstation - fishing
      fishing:
        shape:
          !type:PhysShapeCircle
          radius: 0.4
        layer:
        - ItemMask
        mask:
        - HighImpassable
        density: 1000
        hard: false
    # Goobstation - fishing
  - type: Tag
    tags:
    - HideContextMenu
