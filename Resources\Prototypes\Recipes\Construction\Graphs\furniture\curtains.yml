- type: constructionGraph
  id: Curtains
  start: start
  graph:
    - node: start
      actions:
        - !type:DestroyEntity {}
      edges:
        - to: Curtains
          completed:
            - !type:SnapToGrid { }
          steps:
            - material: Cloth
              amount: 2
              doAfter: 1
        - to: CurtainsBlack
          completed:
            - !type:SnapToGrid { }
          steps:
            - material: FloorCarpetBlack
              amount: 1
              doAfter: 1
        - to: CurtainsBlue
          completed:
            - !type:SnapToGrid { }
          steps:
            - material: FloorCarpetBlue
              amount: 1
              doAfter: 1
        - to: CurtainsCyan
          completed:
            - !type:SnapToGrid { }
          steps:
            - material: FloorCarpetCyan
              amount: 1
              doAfter: 1
        - to: CurtainsGreen
          completed:
            - !type:SnapToGrid { }
          steps:
            - material: FloorCarpetGreen
              amount: 1
              doAfter: 1
        - to: CurtainsOrange
          completed:
            - !type:SnapToGrid { }
          steps:
            - material: FloorCarpetOrange
              amount: 1
              doAfter: 1
        - to: CurtainsPink
          completed:
            - !type:SnapToGrid { }
          steps:
            - material: FloorCarpetPink
              amount: 1
              doAfter: 1
        - to: CurtainsPurple
          completed:
            - !type:SnapToGrid { }
          steps:
            - material: FloorCarpetPurple
              amount: 1
              doAfter: 1
        - to: CurtainsRed
          completed:
            - !type:SnapToGrid { }
          steps:
            - material: FloorCarpetRed
              amount: 1
              doAfter: 1
        - to: CurtainsWhite
          completed:
            - !type:SnapToGrid { }
          steps:
            - material: FloorCarpetWhite
              amount: 1
              doAfter: 1
              
    - node: Curtains
      entity: HospitalCurtains
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: MaterialCloth1
              amount: 2
          steps:
            - tool: Cutting
              doAfter: 1
    - node: CurtainsOpen
      entity: HospitalCurtainsOpen
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: MaterialCloth1
              amount: 2
          steps:
            - tool: Cutting
              doAfter: 1
    
    - node: CurtainsBlack
      entity: CurtainsBlack
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemBlack
              amount: 1
          steps:
            - tool: Cutting
              doAfter: 1
    - node: CurtainsBlackOpen
      entity: CurtainsBlackOpen
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemBlack
              amount: 1
          steps:
            - tool: Cutting
              doAfter: 1    
              
    - node: CurtainsBlue
      entity: CurtainsBlue
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemBlue
              amount: 1
          steps:
            - tool: Cutting
              doAfter: 1
    - node: CurtainsBlueOpen
      entity: CurtainsBlueOpen
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemBlue
              amount: 1
          steps:
            - tool: Cutting
              doAfter: 1
              
    - node: CurtainsCyan
      entity: CurtainsCyan
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemCyan
              amount: 1
          steps:
            - tool: Cutting
              doAfter: 1
    - node: CurtainsCyanOpen
      entity: CurtainsCyanOpen
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemCyan
              amount: 1
          steps:
            - tool: Cutting
              doAfter: 1
              
    - node: CurtainsGreen
      entity: CurtainsGreen
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemGreen
              amount: 1
          steps:
            - tool: Cutting
              doAfter: 1
    - node: CurtainsGreenOpen
      entity: CurtainsGreenOpen
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemGreen
              amount: 1
          steps:
            - tool: Cutting
              doAfter: 1
              
    - node: CurtainsOrange
      entity: CurtainsOrange
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemOrange
              amount: 1
          steps:
            - tool: Cutting
              doAfter: 1
    - node: CurtainsOrangeOpen
      entity: CurtainsOrangeOpen
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemOrange
              amount: 1
          steps:
            - tool: Cutting
              doAfter: 1
              
    - node: CurtainsPink
      entity: CurtainsPink
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemPink
              amount: 1
          steps:
            - tool: Cutting
              doAfter: 1
    - node: CurtainsPinkOpen
      entity: CurtainsPinkOpen
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemPink
              amount: 1
          steps:
            - tool: Cutting
              doAfter: 1
              
    - node: CurtainsPurple
      entity: CurtainsPurple
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemPurple
              amount: 1
          steps:
            - tool: Cutting
              doAfter: 1
    - node: CurtainsPurpleOpen
      entity: CurtainsPurpleOpen
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemPurple
              amount: 1
          steps:
            - tool: Cutting
              doAfter: 1
              
    - node: CurtainsRed
      entity: CurtainsRed
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemRed
              amount: 1
          steps:
            - tool: Cutting
              doAfter: 1
    - node: CurtainsRedOpen
      entity: CurtainsRedOpen
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemRed
              amount: 1
          steps:
            - tool: Cutting
              doAfter: 1
              
    - node: CurtainsWhite
      entity: CurtainsWhite
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemWhite
              amount: 1
          steps:
            - tool: Cutting
              doAfter: 1
    - node: CurtainsWhiteOpen
      entity: CurtainsWhiteOpen
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: FloorCarpetItemWhite
              amount: 1
          steps:
            - tool: Cutting
              doAfter: 1
