- type: entity
  name: "коробка для виживання"
  parent: BoxCardboardSmall
  id: BoxSurvival
  description: "Це коробка з базовим респіратором та аварійним кисневим балоном всередині."
  components:
  - type: StorageFill
    contents:
      - id: ClothingMaskBreath
      - id: EmergencyOxygenTankFilled
      - id: SpaceMedipen
      - id: EmergencyMedipen
      - id: Flare
      - id: FoodPSB # Nyanotrasen - Replace Tinned Meat with PSB
      - id: DrinkWaterBottleFull
  - type: Sprite
    layers:
      - state: internals
      - state: emergencytank

- type: entity
  name: "коробка для подовженого виживання"
  parent: BoxCardboardSmall
  id: BoxSurvivalEngineering
  description: "Це коробка з базовим респіратором та кисневим балоном збільшеної місткості всередині."
  components:
  - type: StorageFill
    contents:
      - id: ClothingMaskBreath
      - id: ExtendedEmergencyOxygenTankFilled
      - id: SpaceMedipen
      - id: EmergencyMedipen
      - id: Flare
      - id: FoodPSB # Nyanotrasen - Replace Tinned Meat with PSB
      - id: DrinkWaterBottleFull
  - type: Sprite
    layers:
      - state: internals
      - state: extendedtank

- type: entity
  name: "коробка для виживання"
  parent: BoxCardboardSmall
  id: BoxSurvivalSecurity
  description: "Це коробка з базовим респіратором та аварійним кисневим балоном всередині."
  suffix: Security
  components:
  - type: StorageFill
    contents:
      - id: ClothingMaskGasSecurity
      - id: EmergencyOxygenTankFilled
      - id: SpaceMedipen
      - id: EmergencyMedipen
      - id: Flare
      - id: FoodPSB # Nyanotrasen - Replace Tinned Meat with PSB
      - id: DrinkWaterBottleFull
  - type: Sprite
    layers:
      - state: internals
      - state: emergencytank

- type: entity
  name: "коробка для виживання"
  parent: BoxCardboardSmall
  id: BoxSurvivalBrigmedic
  description: "Це коробка з базовим респіратором та аварійним кисневим балоном всередині."
  suffix: MedSec
  components:
  - type: StorageFill
    contents:
      - id: ClothingMaskBreathMedicalSecurity
      - id: EmergencyOxygenTankFilled
      - id: SpaceMedipen
      - id: EmergencyMedipen
      - id: EmergencyMedipen
      - id: FoodPSB # Nyanotrasen - Replace Tinned Meat with PSB
      - id: DrinkWaterBottleFull
  - type: Sprite
    layers:
      - state: internals
      - state: emergencytank

- type: entity
  name: "коробка для виживання"
  parent: BoxCardboardSmall
  id: BoxSurvivalMedical
  description: "Це коробка з базовим респіратором та аварійним кисневим балоном всередині."
  suffix: Medical
  components:
  - type: StorageFill
    contents:
      - id: ClothingMaskBreathMedical
      - id: EmergencyOxygenTankFilled
      - id: SpaceMedipen
      - id: EmergencyMedipen
      - id: Flare
      - id: FoodPSB # Nyanotrasen - Replace Tinned Meat with PSB
      - id: DrinkWaterBottleFull
  - type: Sprite
    layers:
      - state: internals
      - state: emergencytank

- type: entity
  name: "коробка обіймів"
  parent: BoxCardboardSmall
  id: BoxHug
  description: "Спеціальна коробка для чутливих людей."
  suffix: Emergency
  components:
  - type: Sprite
    layers:
      - state: box_hug
      - state: heart
  - type: Item
    heldPrefix: hug
  - type: StorageFill
    contents:
      - id: ClothingMaskBreath
      - id: EmergencyFunnyOxygenTankFilled
      - id: SpaceMedipen
      - id: EmergencyMedipen
      - id: Flare
      - id: FoodPSB # Nyanotrasen - Replace Tinned Meat with PSB
      - id: DrinkWaterBottleFull
  - type: Tag
    tags:
      - BoxHug

- type: entity
  parent: BoxHug
  id: BoxHugNitrogen
  suffix: Emergency N2
  components:
  - type: StorageFill
    contents:
    - id: ClothingMaskBreath
    - id: EmergencyNitrogenTankFilled
    - id: EmergencyMedipen
    - id: Flare
    - id: FoodSnackNutribrick
    - id: DrinkWaterBottleFull
  - type: Label
    currentLabel: reagent-name-nitrogen

- type: entity
  parent: BoxSurvival
  id: BoxMime
  name: "коробка для виживання"
  description: "Це коробка з базовим респіратором та аварійним кисневим балоном всередині."
  suffix: Emergency
  components:
  - type: StorageFill
    contents:
    - id: ClothingMaskBreath
    - id: EmergencyOxygenTankFilled
    - id: EmergencyMedipen
    - id: Flare
    - id: FoodBreadBaguette
    - id: DrinkWaterBottleFull

- type: entity
  parent: BoxSurvival
  id: BoxMimeNitrogen
  suffix: Emergency N2
  components:
  - type: StorageFill
    contents:
    - id: ClothingMaskBreath
    - id: EmergencyNitrogenTankFilled
    - id: EmergencyMedipen
    - id: Flare
    - id: FoodBreadBaguette
    - id: DrinkWaterBottleFull
  - type: Label
    currentLabel: reagent-name-nitrogen

- type: entity
  parent: BoxCardboardSmall
  id: BoxSurvivalSyndicate
  description: "Це коробка з базовим респіратором та кисневим балоном збільшеної місткості всередині."
  components:
  - type: StorageFill
    contents:
      - id: ClothingMaskGasSyndicate
      - id: ExtendedEmergencyOxygenTankFilled
      - id: EmergencyMedipen
      - id: Flare
      - id: FoodPSB # Nyanotrasen - Replace Tinned Meat with PSB
      - id: DrinkWaterBottleFull
  - type: Sprite
    layers:
      - state: internals
      - state: extendedtank
