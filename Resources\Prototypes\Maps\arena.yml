- type: gameMap
  id: Arena
  mapName: Арена
  mapPath: /Maps/arena.yml
  minPlayers: 15
  maxPlayers: 80
  stations:
    Arena:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: '{0} Arena Station {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: 'DV'
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/DeltaV/NTES_UCLB.yml
        - type: StationJobs
          overflowJobs:
            - Passenger
          availableJobs:
          #civilian
            Passenger: [ -1, -1 ]
            Librarian: [ 1, 1 ]
          #command
            Captain: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1]
            NanotrasenRepresentative: [ 1, 1 ]
            # Magistrate: [ 1, 1 ] # EE: Disabled
            # AdministrativeAssistant: [ 1, 1 ] # EE: Disabled
          #engineering
            AtmosphericTechnician: [ 1, 2 ]
            ChiefEngineer: [ 1, 1 ]
            StationEngineer: [ 2, 3 ]
            TechnicalAssistant: [ 2, 3 ]
          #medical
            Chemist: [ 2, 2 ]
            ChiefMedicalOfficer: [ 1, 1 ]
            MedicalDoctor: [ 3, 5 ]
            MedicalIntern: [ 2, 3 ]
            Paramedic: [ 1, 2 ]
            Psychologist: [ 1, 1 ]
          #security
            Brigmedic: [ 1, 1 ]
            Detective: [ 1, 1 ]
            # Gladiator: [ 0, 2 ] # EE: Disabled
            HeadOfSecurity: [ 1, 1 ]
            Prisoner: [ 1, 2 ]
            # PrisonGuard: [ 1, 1 ] # EE: Disabled
            SecurityOfficer: [ 5, 7 ]
            SecurityCadet: [ 1, 2 ]
            Warden: [ 1, 1 ]
          #service
            Bartender: [ 1, 2 ]
            Botanist: [ 2, 2 ]
            Chef: [ 2, 2 ]
            Clown: [ 1, 1 ]
            HeadOfPersonnel: [ 1, 1 ]
            Janitor: [ 2, 2 ]
            Lawyer: [ 2, 2 ]
            Mime: [ 1, 1 ]
            Musician: [ 1, 1 ]
            Reporter: [ 2, 2 ]
            ServiceWorker: [ 1, 3 ]
          #science
            ResearchDirector: [ 1, 1 ]
            Scientist: [ 3, 5 ]
            ForensicMantis: [ 1, 1 ]
            ResearchAssistant: [ 2, 4 ]
            Chaplain: [ 1, 1 ]
          #supply
            Quartermaster: [ 1, 1 ]
            CargoTechnician: [ 2, 3 ]
            SalvageSpecialist: [ 2, 3 ]
            MailCarrier: [ 1, 2 ]
          #silicon
            StationAi: [ 1, 1 ]
            Borg: [ 2, 2 ]
            MedicalBorg: [ 1, 1 ]
        # blob-config-start SMALL+
        - type: StationBlobConfig
          stageBegin: 25
          stageCritical: 350
          stageTheEnd: 700
        # blob-config-end
