- type: entity
  parent: BaseItem
  id: DoorRemoteDefault
  name: "пульт від дверей"
  description: "Гаджет, який може дистанційно відчиняти та зачиняти двері."
  abstract: true
  components:
  - type: Sprite
    sprite: Objects/Devices/door_remote.rsi
  - type: Item
    storedRotation: -90
  - type: Access #No access, useless
  - type: DoorRemote
  - type: StealTarget
    stealGroup: DoorRemote

- type: entity
  parent: DoorRemoteDefault
  id: DoorRemoteCommand
  name: "пульт від командних дверей"
  components:
  - type: Sprite
    layers:
    - state: door_remotebase
    - state: door_remotelightscolour
      color: "#e6e600"
    - state: door_remotescreencolour
      color: "#9f9f00"
  - type: Access
    groups:
    - AllAccess #Cap must be able to control the station

- type: entity
  parent: DoorRemoteDefault
  id: DoorRemoteSecurity
  name: "пульт від захисних дверей"
  components:
  - type: Sprite
    layers:
    - state: door_remotebase
    - state: door_remotelightscolour
      color: "#cb0000"
    - state: door_remotescreencolour
      color: "#830000"
  - type: Access
    groups:
    - Security

- type: entity
  parent: DoorRemoteDefault
  id: DoorRemoteArmory
  name: "пульт від дверей арсеналу"
  components:
  - type: Sprite
    layers:
    - state: door_remotebase
    - state: door_remotelightscolour
      color: "#e80000"
    - state: door_remotescreencolour
      color: "#a80000"
  - type: Access
    groups:
    - Armory

- type: entity
  parent: DoorRemoteDefault
  id: DoorRemoteService
  name: "пульт від службових дверей"
  components:
  - type: Sprite
    layers:
    - state: door_remotebase
    - state: door_remotelightscolour
      color: "#58c800"
    - state: door_remotescreencolour
      color: "#3a7231"
  - type: Access
    groups:
    - Service

- type: entity
  parent: DoorRemoteDefault
  id: DoorRemoteResearch
  name: "пульт від дослідницьких дверей"
  components:
  - type: Sprite
    layers:
    - state: door_remotebase
    - state: door_remotelightscolour
      color: "#a53aaa"
    - state: door_remotescreencolour
      color: "#652368"
  - type: Access
    groups:
    - Research

- type: entity
  parent: DoorRemoteDefault
  id: DoorRemoteCargo
  name: "логістичний пульт для дверей" # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Sprite
    layers:
    - state: door_remotebase
    - state: door_remotelightscolour
      color: "#b18644"
    - state: door_remotescreencolour
      color: "#5b4523"
  - type: Access
    groups:
    - Cargo

- type: entity
  parent: DoorRemoteDefault
  id: DoorRemoteMedical
  name: "пульт для медичних дверей"
  components:
  - type: Sprite
    layers:
    - state: door_remotebase
    - state: door_remotelightscolour
      color: "#68aed6"
    - state: door_remotescreencolour
      color: "#325f7a"
  - type: Access
    groups:
    - Medical

- type: entity
  parent: DoorRemoteDefault
  id: DoorRemoteEngineering
  name: "пульт для інженерних дверей"
  components:
  - type: Sprite
    layers:
    - state: door_remotebase
    - state: door_remotelightscolour
      color: "#ffa62b"
    - state: door_remotescreencolour
      color: "#bc5b00"
  - type: Access
    groups:
    - Engineering

- type: entity
  parent: DoorRemoteDefault
  id: DoorRemoteFirefight
  name: "пульт для протипожежних дверей"
  description: "Гаджет, який може дистанційно відчиняти та зачиняти FireDoors."
  components:
    - type: Sprite
      layers:
        - state: door_remotebase
        - state: door_remotelightscolour
          color: "#ff9900"
        - state: door_remotescreencolour
          color: "#e02020"

    - type: Access
      groups:
        - FireFight

- type: entity
  parent: DoorRemoteDefault
  id: DoorRemoteAll
  name: "пульт дистанційного керування для супер-дверей"
  suffix: Admeme
  components:
  - type: Sprite
    layers:
    - state: door_remotebase
    - state: door_remotelightscolour
      color: "#2eba22"
    - state: door_remotescreencolour
      color: "#22871a"
  - type: Access
    groups:
    - AllAccess
    tags:
    - CentralCommand
