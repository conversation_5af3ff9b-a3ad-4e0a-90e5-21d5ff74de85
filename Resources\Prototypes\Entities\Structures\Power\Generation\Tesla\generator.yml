- type: entity
  id: TeslaGenerator
  name: "генератор Тесли"
  parent: BaseStructureDynamic
  description: "Дивний пристрій, який при налаштуванні виробляє потужну кулю Тесла."
  components:
  - type: Sprite
    noRot: true
    sprite: Structures/Power/Generation/Tesla/generator.rsi
    state: icon 
  - type: SingularityGenerator #To do: rename the generator
    spawnId: TeslaEnergyBall
  - type: InteractionOutline
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          # Using a circle here makes it a lot easier to pull it all the way from Cargo
          !type:PhysShapeCircle
            radius: 0.45
        density: 190
        # Keep an eye on ParticlesProjectile when adjusting these
        mask:
        - MachineMask
        layer:
        - Opaque
  - type: Anchorable
  #- type: GuideHelp #   To Do - add Tesla Guide

