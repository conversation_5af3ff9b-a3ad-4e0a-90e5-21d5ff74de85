<controls:FancyWindow xmlns="https://spacestation14.io"
                      xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                      xmlns:ui="clr-namespace:Content.Client.Pinpointer.UI"
                      Title="{Loc 'station-map-window-title'}"
                      Resizable="False"
                      SetSize="868 748"
                      MinSize="868 748">
    <BoxContainer Orientation="Vertical">
        <!-- Station name -->
        <controls:StripeBack>
            <PanelContainer>
                <Label Name="StationName" Text="Unknown station" StyleClasses="LabelBig" Align="Center"/>
            </PanelContainer>
        </controls:StripeBack>

        <BoxContainer Orientation="Horizontal" HorizontalExpand="True" VerticalAlignment="Top">
            <ui:NavMapControl Name="NavMapScreen"/>

            <BoxContainer Orientation="Vertical" SetWidth="200">
                <!-- Search bar -->
                <LineEdit Name="FilterBar" PlaceHolder="{Loc 'station-map-filter-placeholder'}" Margin="0 0 10 10" HorizontalExpand="True"/>

                <ScrollContainer HorizontalExpand="True" VerticalExpand="True">
                    <!-- Beacon Buttons (filled by code) -->
                    <BoxContainer Name="BeaconButtons" Orientation="Vertical" HorizontalExpand="True" />
                </ScrollContainer>
            </BoxContainer>
        </BoxContainer>

        <!-- Footer -->
        <BoxContainer Orientation="Vertical">
            <PanelContainer StyleClasses="LowDivider" />
            <BoxContainer Orientation="Horizontal" Margin="10 0 5 0" VerticalAlignment="Bottom">
                <Label Text="{Loc 'station-map-user-interface-flavor-left'}" StyleClasses="WindowFooterText" />
                <Label Text="{Loc 'station-map-user-interface-flavor-right'}" StyleClasses="WindowFooterText"
                        HorizontalAlignment="Right" HorizontalExpand="True"  Margin="0 0 2 0" />
                <TextureRect StyleClasses="NTLogoDark" Stretch="KeepAspectCentered"
                        VerticalAlignment="Center" HorizontalAlignment="Right" SetSize="19 19"/>
            </BoxContainer>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
