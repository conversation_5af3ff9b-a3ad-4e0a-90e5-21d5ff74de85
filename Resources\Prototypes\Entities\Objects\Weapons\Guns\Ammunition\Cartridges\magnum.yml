- type: entity
  id: BaseCartridgeMagnum
  name: "патрон (.45 magnum)"
  parent: BaseCartridge
  abstract: true
  components:
  - type: Tag
    tags:
      - Cartridge
      - CartridgeMagnum
  - type: CartridgeAmmo
    proto: BulletMagnum
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Casings/ammo_casing.rsi
    layers:
    - state: base
      map: ["enum.AmmoVisualLayers.Base"]
  - type: Appearance
  - type: SpentAmmoVisuals

- type: entity
  id: CartridgeMagnum
  name: "патрон (.45 magnum)"
  parent: BaseCartridgeMagnum
  components:
  - type: CartridgeAmmo
    proto: BulletMagnum

- type: entity
  id: CartridgeMagnumPractice
  name: "патрон (.45 magnum practice)"
  parent: BaseCartridgeMagnum
  components:
  - type: CartridgeAmmo
    proto: BulletMagnumPractice
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#dbdbdb"

- type: entity
  id: CartridgeMagnumRubber
  name: "наб<PERSON>й (.45 магнум гумовий)"
  parent: BaseCartridgeMagnum
  components:
  - type: CartridgeAmmo
    proto: BulletMagnumRubber
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#43c4f7"

- type: entity
  id: CartridgeMagnumIncendiary
  name: "набій (.45 магнум запальний)"
  parent: BaseCartridgeMagnum
  components:
  - type: CartridgeAmmo
    proto: BulletMagnumIncendiary
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#ff6e52"

- type: entity
  id: CartridgeMagnumAP
  name: "патрон (.45 magnum бронебійний)"
  parent: BaseCartridgeMagnum
  components:
  - type: CartridgeAmmo
    proto: BulletMagnumAP
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#0a0a0a"

- type: entity
  id: CartridgeMagnumUranium
  name: "патрон (.45 магнум уран)"
  parent: BaseCartridgeMagnum
  components:
  - type: CartridgeAmmo
    proto: BulletMagnumUranium
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#65fe08"

- type: entity
  id: CartridgeMagnumShrapnel
  name: "патрон (.45 магнум, шрапнельний)"
  parent: BaseCartridgeMagnum
  components:
  - type: CartridgeAmmo
    proto: BulletMagnumShrapnelSpread
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
      - state: tip
        map: [ "enum.AmmoVisualLayers.Tip" ]
        color: "#FF00FF"
