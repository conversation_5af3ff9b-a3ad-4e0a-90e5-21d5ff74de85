- type: entity
  parent: BaseItem
  id: BikeHorn
  name: "велосипедний гудок"
  description: "Клаксон від велосипеда."
  components:
  - type: Sprite
    sprite: Objects/Fun/bikehorn.rsi
    state: icon
  - type: Item
    sprite: Objects/Fun/bikehorn.rsi
    size: Tiny
  - type: Clothing
    sprite: Objects/Fun/bikehorn.rsi
    slots: [Belt]
    quickEquip: false
  - type: EmitSoundOnUse
    sound:
      collection: BikeHorn
      params:
        variation: 0.125
  - type: UseDelay
    delay: 0.5
  - type: EmitSoundOnTrigger
    sound:
      collection: BikeHorn
      params:
        variation: 0.125
  - type: Tag
    tags:
    - Payload # yes, you can make re-usable prank grenades
    - BikeHorn
  - type: MeleeWeapon
    wideAnimationRotation: 135
    soundHit:
      collection: BikeHorn
      params:
        variation: 0.125
    damage:
      types:
        Blunt: 0
  - type: DamageOtherOnHit
  - type: Tool
    qualities:
    - Honking
    useSound:
      collection: BikeHorn

- type: entity
  parent: BaseItem
  id: CluwneHorn
  name: "зламаний велосипедний клаксон"
  description: "Зламаний ріг від велосипеда."
  components:
  - type: UseDelay
  - type: Sprite
    sprite: Objects/Fun/cluwnehorn.rsi
    state: icon
  - type: Item
    sprite: Objects/Fun/cluwnehorn.rsi
    size: Tiny
  - type: Clothing
    sprite: Objects/Fun/cluwnehorn.rsi
    slots: [Belt]
    quickEquip: false
  - type: Tag
    tags:
    - Payload # yes, you can make re-usable prank grenades
    - CluwneHorn
  - type: EmitSoundOnTrigger
    sound:
      collection: CluwneHorn
      params:
        variation: 0.125
  - type: EmitSoundOnUse
    sound:
      collection: CluwneHorn
      params:
        variation: 0.246
  - type: MeleeWeapon
    wideAnimationRotation: 135
    soundHit:
      collection: CluwneHorn
      params:
        variation: 0.125
    damage:
      types:
        Blunt: 5


- type: entity
  parent: BikeHorn
  id: GoldenBikeHorn
  name: "золотий гудок"
  suffix: No mapping
  description: "Щасливий приз, моліть богів про винагороду."
  components:
  - type: Sprite
    sprite: Objects/Fun/goldbikehorn.rsi
    state: icon
  - type: Item
    sprite: Objects/Fun/goldbikehorn.rsi
    size: Tiny
  - type: Clothing
    sprite: Objects/Fun/goldbikehorn.rsi
    slots: [Belt]
  - type: Prayable
  - type: StaticPrice
    price: 1000
  - type: PointLight
    radius: 1.2
    energy: 1
    castShadows: false
    color: "#eef066"

- type: entity
  parent: BikeHorn
  id: BananiumHorn
  name: "банановий ріжок"
  description: "Повітряний ріжок з бананіуму."
  components:
  - type: Sprite
    sprite: Objects/Fun/bananiumhorn.rsi
    state: icon
  - type: Item
    sprite: Objects/Fun/bananiumhorn.rsi
    size: Small
  - type: Clothing
    sprite: Objects/Fun/bananiumhorn.rsi
    slots: [Belt]
    quickEquip: false
  - type: EmitSoundOnUse
    sound:
      collection: BananiumHorn
      params:
        variation: 0.246
  - type: EmitSoundOnTrigger
    sound:
      collection: BananiumHorn
      params:
        variation: 0.246
  - type: Construction
    graph: BananiumHorn
    node: bananiumHorn
