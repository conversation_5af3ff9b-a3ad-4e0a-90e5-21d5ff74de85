- type: entity
  id: BannerBase
  parent: BaseStructureDynamic
  name: "базовий банер"
  description: "Це концепція банера, ви не повинні цього бачити."
  abstract: true
  components:
  - type: Sprite
    sprite: Structures/Decoration/banner.rsi
    state: banner
    noRot: true
  - type: Transform
    noRot: true
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.2
        density: 190
        mask:
        - MachineMask
        layer:
        - Opaque
        - MidImpassable
        - HighImpassable
        - BulletImpassable
  - type: InteractionOutline

- type: entity
  id: BannerNanotrasen
  parent: BannerBase
  name: "банер нанотрейзен"
  description: "Банер з логотипом Nanotrasen. Виглядає досить дешево."
  components:
  - type: Sprite
    sprite: Structures/Decoration/banner.rsi
    state: banner

- type: entity
  id: BannerCargo
  parent: BannerBase
  name: "ба<PERSON><PERSON><PERSON> карго"
  description: "Бане<PERSON> у кольорах відділу логістики. Ні. Каргонія." # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Sprite
    sprite: Structures/Decoration/banner.rsi
    state: banner_cargo

- type: entity
  id: BannerEngineering
  parent: BannerBase
  name: "банер інженерії"
  description: "Банер, що відображає кольори інженерного відділу. Скрунгуларті."
  components:
  - type: Sprite
    sprite: Structures/Decoration/banner.rsi
    state: banner_engineering

- type: entity
  id: BannerMedical
  parent: BannerBase
  name: "банер медицини"
  description: "Банер, що відображає кольори медичного відділення. Як стерильно."
  components:
  - type: Sprite
    sprite: Structures/Decoration/banner.rsi
    state: banner_medical

- type: entity
  id: BannerRevolution
  parent: BannerBase
  name: "банер революції"
  description: "Банер із зображенням революції. Віва!"
  components:
  - type: Sprite
    sprite: Structures/Decoration/banner.rsi
    state: banner_revolution

- type: entity
  id: BannerSyndicate
  parent: BannerBase
  name: "банер синдикату"
  description: "Прапор, від якого, на думку синдикату, ви повинні відчувати ненависть до НТ."
  components:
  - type: Sprite
    sprite: Structures/Decoration/banner.rsi
    state: banner_syndicate

- type: entity
  id: BannerScience
  parent: BannerBase
  name: "банер про епістемологію" # DeltaV - Epistemics Department replacing Science
  description: "Банер у кольорах кафедри епістеміки. Де доведено, що дурість більша за всесвіт." # DeltaV - Epistemics Department replacing Science
  components:
  - type: Sprite
    sprite: Structures/Decoration/banner.rsi
    state: banner_science

- type: entity
  id: BannerSecurity
  parent: BannerBase
  name: "банер служби безпеки"
  description: "Банер у кольорах департаменту гівнобезпеки. Охорона, винен."
  components:
  - type: Sprite
    sprite: Structures/Decoration/banner.rsi
    state: banner_security

- type: entity
  id: BannerBlue
  parent: BannerBase
  name: "синій прапор"
  description: "Банер із зображенням синього кольору. Дабудідабудай."
  components:
  - type: Sprite
    sprite: Structures/Decoration/banner.rsi
    state: banner-blue

- type: entity
  id: BannerRed
  parent: BannerBase
  name: "червоний прапор"
  description: "Банер з червоним кольором. Гострий банер."
  components:
  - type: Sprite
    sprite: Structures/Decoration/banner.rsi
    state: banner-red

- type: entity
  id: BannerYellow
  parent: BannerBase
  name: "жовтий прапор"
  description: "Банер, що відображає жовтий колір. Нагадує качок та лимонні кіоски."
  components:
  - type: Sprite
    sprite: Structures/Decoration/banner.rsi
    state: banner-yellow

- type: entity
  id: BannerGreen
  parent: BannerBase
  name: "зелений прапор"
  description: "Банер із зображенням зеленого кольору. Трава, листя, гуакамоле."
  components:
  - type: Sprite
    sprite: Structures/Decoration/banner.rsi
    state: banner-green


