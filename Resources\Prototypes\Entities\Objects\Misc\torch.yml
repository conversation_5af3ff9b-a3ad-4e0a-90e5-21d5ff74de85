- type: entity
  name: "факел"
  parent: BaseItem
  id: Torch
  description: "Смолоскип, зроблений з дерева."
  components:
    - type: ExpendableLight
      spentName: expendable-light-burnt-torch-name
      spentDesc: expendable-light-burnt-torch-desc
      glowDuration: 100
      fadeOutDuration: 4
      iconStateSpent: torch_spent
      turnOnBehaviourID: turn_on
      fadeOutBehaviourID: fade_out
      # Sounds legit nuff
      litSound:
        path: /Audio/Items/Flare/flare_on.ogg
    - type: Sprite
      sprite: Objects/Misc/torch.rsi
      layers:
        - map: [ enum.ExpendableLightVisualLayers.Base ]
          state: torch_unlit
        - map: [ enum.ExpendableLightVisualLayers.Glow ]
          state: lit_overlay
          color: "#FFFFFF"
          visible: false
          shader: unshaded
        - map: [ enum.ExpendableLightVisualLayers.Overlay ]
          state: torch_nocloth
    - type: Icon
      sprite: Objects/Misc/torch.rsi
      state: icon
    - type: Item
      sprite: Objects/Misc/torch.rsi
      heldPrefix: unlit
    - type: Construction
      graph: LightTorch
      node: torch
    - type: Appearance
    - type: PointLight
      enabled: false
      color: "#E25822"
      radius: 1.0
      energy: 5.0
      netsync: false
    - type: IgnitionSource
      temperature: 400
      ignited: false
    - type: LightBehaviour
      behaviours:
        - !type:RandomizeBehaviour # immediately make it bright and flickery
          id: turn_on
          interpolate: Nearest
          minDuration: 0.02
          maxDuration: 0.06
          startValue: 6.0
          endValue: 9.0
          property: Energy
          isLooped: true
        - !type:FadeBehaviour # have the radius start small and get larger as it starts to burn
          id: turn_on
          maxDuration: 8.0
          startValue: 1.0
          endValue: 6.0
        - !type:RandomizeBehaviour # weaker flicker as it fades out
          id: fade_out
          interpolate: Nearest
          minDuration: 0.02
          maxDuration: 0.06
          startValue: 4.0
          endValue: 8.0
          property: Energy
          isLooped: true
        - !type:FadeBehaviour # fade out radius as it burns out
          id: fade_out
          maxDuration: 4.0
          startValue: 6.0
          endValue: 1.0
    - type: Tag
      tags:
      - Torch
