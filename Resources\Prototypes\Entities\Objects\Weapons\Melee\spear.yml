- type: entity
  name: "грубий спис"
  parent: BaseItem
  id: Spear
  description: "Грубий спис для того, щоб продірявити когось."
  components:
  - type: EmbeddableProjectile
    offset: 0.15,0.15
  - type: EmbedPassiveDamage
  - type: ThrowingAngle
    angle: 225
  - type: LandAtCursor
  - type: Tag
    tags:
    - Spear
  - type: Fixtures
    fixtures:
      fix1:
        shape: !type:PolygonShape
          vertices:
            - -0.40,-0.30
            - -0.30,-0.40
            - 0.40,0.30
            - 0.30,0.40
        density: 20
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2
  - type: Sharp
  - type: Sprite
    sprite: Objects/Weapons/Melee/spear.rsi
    layers:
    - state: spear
    - state: spear1
      map: ["enum.SolutionContainerLayers.Fill"]
      visible: false
  - type: MeleeWeapon
    wideAnimationRotation: -135
    range: 1.75
    damage:
      types:
        Piercing: 7
        Slash: 1
    heavyRateModifier: 1.3
    heavyRangeModifier: 1.35
    heavyDamageBaseModifier: 1.0
    heavyStaminaCost: 2.5
    maxTargets: 3
    angle: 20
    animation: WeaponArcThrust
    soundHit:
      path: /Audio/Weapons/bladeslice.ogg
  - type: DamageOtherOnHit
    damage:
      types:
        Piercing: 10
    staminaCost: 6
  - type: Item
    size: Ginormous
  - type: Clothing
    quickEquip: false
    slots:
    - back
    - suitStorage
  - type: Construction
    graph: Spear
    node: spear
  - type: SolutionContainerManager
    solutions:
      melee:
        maxVol: 2
  - type: MeleeChemicalInjector
    solution: melee
  - type: RefillableSolution
    solution: melee
  - type: InjectableSolution
    solution: melee
  - type: SolutionInjectOnEmbed
    transferAmount: 2
    solution: melee
  - type: SolutionTransfer
    maxTransferAmount: 2
  - type: Wieldable
  - type: IncreaseDamageOnWield
    damage:
      types:
        Piercing: 3
        Slash: 3
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 30 #excess damage avoids cost of spawning entities.
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 20
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
          params:
            volume: -4
      - !type:SpawnEntitiesBehavior
        spawn:
          PartRodMetal1:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: UseDelay
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 1
    fillBaseName: spear

- type: entity
  name: "грубий армований спис"
  parent: Spear
  id: SpearReinforced
  description: "Грубий армований спис для тих випадків, коли потрібно наробити в комусь дірок."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Melee/reinforced_spear.rsi
  - type: MeleeWeapon
    wideAnimationRotation: -135
    damage:
      types:
        Piercing: 8.5
        Slash: 1
  - type: DamageOtherOnHit
    damage:
      types:
        Piercing: 12
  - type: Construction
    graph: SpearReinforced

- type: entity
  name: "плазмовий спис"
  parent: Spear
  id: SpearPlasma
  description: "Грубий плазмовий спис для тих випадків, коли потрібно наробити в комусь дірок."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Melee/plasma_spear.rsi
  - type: MeleeWeapon
    wideAnimationRotation: -135
    damage:
      types:
        Piercing: 9.5
        Slash: 1.5
  - type: DamageOtherOnHit
    damage:
      types:
        Piercing: 14
  - type: Construction
    graph: SpearPlasma

- type: entity
  name: "урановий спис"
  parent: Spear
  id: SpearUranium
  description: "Спис з необробленого урану для тих випадків, коли потрібно наробити в комусь дірок."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Melee/uranium_spear.rsi
  - type: MeleeWeapon
    wideAnimationRotation: -135
    damage:
      types:
        Piercing: 8
        Radiation: 8
  - type: DamageOtherOnHit
    damage:
      types:
        Piercing: 8
        Radiation: 8
  - type: Construction
    graph: SpearUranium

- type: entity
  name: "кістяний спис"
  parent: Spear
  id: SpearBone
  description: "Кістки та шовк у поєднанні."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Melee/bone_spear.rsi
  - type: Construction
    graph: SpearBone

- type: entity
  name: "рятувальний гарпун"
  parent: BaseItem
  id: SalvageHarpoon
  description: "Спис, виготовлений з рафінованої сталі, призначений для рятувальних та мисливських цілей. Мисливці полюбляють його, коли йдеться про полювання на різні види коропів."
  components:
  - type: EmbeddableProjectile
    offset: 0.15,0.15
  - type: EmbedPassiveDamage
  - type: ThrowingAngle
    angle: 225
  - type: LandAtCursor
  - type: Tag
    tags:
    - Spear
  - type: Fixtures
    fixtures:
      fix1:
        shape: !type:PolygonShape
          vertices:
            - -0.40,-0.30
            - -0.30,-0.40
            - 0.40,0.30
            - 0.30,0.40
        density: 20
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2
  - type: Sharp
  - type: Sprite
    sprite: _DEN/Objects/Weapons/Melee/harpoon.rsi
    layers:
    - state: spear
    - state: spear1
      map: ["enum.SolutionContainerLayers.Fill"]
      visible: false
  - type: MeleeWeapon
    wideAnimationRotation: -135
    range: 1.75
    damage:
      types:
        Piercing: 10
        Slash: 2
    heavyRateModifier: 1.3
    heavyRangeModifier: 1.35
    heavyDamageBaseModifier: 1.0
    heavyStaminaCost: 2.5
    maxTargets: 3
    angle: 20
    animation: WeaponArcThrust
    soundHit:
      path: /Audio/Weapons/bladeslice.ogg
  - type: DamageOtherOnHit
    damage:
      types:
        Piercing: 16
  - type: Item
    size: Ginormous
  - type: Clothing
    quickEquip: false
    slots:
    - back
    - suitStorage
  - type: SolutionContainerManager
    solutions:
      melee:
        maxVol: 1
  - type: MeleeChemicalInjector
    solution: melee
  - type: RefillableSolution
    solution: melee
  - type: InjectableSolution
    solution: melee
  - type: SolutionInjectOnEmbed
    transferAmount: 1
    solution: melee
  - type: SolutionTransfer
    maxTransferAmount: 1
  - type: Wieldable
  - type: IncreaseDamageOnWield
    damage:
      types:
        Piercing: 3
        Slash: 3
  - type: DamageOnLand
    damage:
      types:
        Blunt: 5
  - type: UseDelay
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 1
    fillBaseName: spear
