- type: entity
  id: WeaponProtoKineticAcceleratorBase
  abstract: true
  parent: BaseItem
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Basic/kinetic_accelerator.rsi
  - type: Item
    sprite: Objects/Weapons/Guns/Basic/kinetic_accelerator.rsi
    size: Large # Lavaland Change
    shape:
    - 0,0,2,1
  # Lavaland Change start
  - type: WeaponAttachment
    lightActionPrototype: ActionTogglePKALight
  - type: PressureDamageChange
  - type: Multishot
  - type: UpgradeableGun
    whitelist:
      tags:
      - PKAUpgrade
  #- type: GunWieldBonus
  #  minAngle: -43
  #  maxAngle: -43
  #- type: Wieldable
  # Lavaland Change end
  - type: Gun
    fireRate: 0.7 #goobstation
    selectedMode: SemiAuto
    #angleDecay: 45 # Lavaland Change
    minAngle: 3 # Lavaland Change
    maxAngle: 5 # Lavaland Change
    availableModes:
    - SemiAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/kinetic_accel.ogg
  - type: AmmoCounter
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.AmmoVisuals.HasAmmo:
        empty-icon:
          True: { visible: False }
          False: { visible: True }
        #todo: add other 'empty' animations
  - type: RechargeBasicEntityAmmo
    rechargeCooldown: 0.65 #Goobstation
    rechargeSound:
      path: /Audio/Weapons/Guns/MagIn/kinetic_reload.ogg
  - type: BasicEntityAmmoProvider
    proto: BulletKinetic
    capacity: 1
    count: 1
  - type: Clothing
    sprite: Objects/Weapons/Guns/Basic/kinetic_accelerator.rsi
    quickEquip: false
    slots:
    - suitStorage
    - Belt
  - type: UseDelay
    delay: 1
  - type: PhysicalComposition #Goobstation - Recycle update
    materialComposition:
      Steel: 250
      Glass: 125
      Silver: 25
  - type: MeleeWeapon
    wideAnimationRotation: 270
    attackRate: 1
    angle: 0
    range: 1.1
    damage:
      types:
        Blunt: 5
    swapKeys: true
    disableHeavy: true
    animation: WeaponArcThrust
    soundHit:
      collection: MetalThud
  - type: IncreaseDamageOnWield
    damage:
      types:
        Blunt: 2.5
  - type: DamageOtherOnHit
    staminaCost: 8
  # PIRATE START
  - type: CanTakeAim
  # PIRATE END
