- type: entity
  id: BaseDionaOrganUnGibbable
  parent: BaseItem
  abstract: true
  components:
  - type: Sprite
    sprite: Mobs/Species/Diona/organs.rsi
  - type: Organ
  - type: Food
  - type: Extractable
    grindableSolutionName: organ
  - type: SolutionContainerManager
    solutions:
      organ:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
      food:
        maxVol: 5
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 5
  - type: FlavorProfile
    flavors:
      - people
  - type: Tag # goob edit
    tags:
    - Meat
    - Organ

- type: entity
  id: BaseDionaOrgan
  parent: BaseDionaOrganUnGibbable
  abstract: true
  components:
  - type: Gibbable

- type: entity
  id: OrganDionaBrain
  parent: BaseDionaOrganUnGibbable
  name: "мозок"
  description: "Центральний осередок псевдоневрологічної активності діони, її коренеподібні вусики шукають своє колишнє тіло."
  components:
  - type: Sprite
    state: brain
  - type: Organ # Shitmed
    slotId: brain
  - type: Food # Shitmed Change
    delay: 5
    forceFeedDelay: 6
    popupOnEat: true
  - type: Brain # Shitmed
  - type: SolutionContainerManager
    solutions:
      organ:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
      Lung:
        maxVol: 100
        canReact: False
      food:
        maxVol: 5
        reagents:
        - ReagentId: GreyMatter
          Quantity: 5
  - type: Tag # goob edit
    tags:
    - Meat
    - Organ
    - Brain

- type: entity
  id: OrganDionaEyes
  parent: BaseDionaOrgan
  name: "очі"
  description: "Я тебе бачу!"
  components:
  - type: Sprite
    layers:
      - state: eyeball-l
      - state: eyeball-r
  - type: Organ # Shitmed
    slotId: eyes
  - type: Eyes
  - type: Tag # goob edit HERETICS
    tags:
    - Meat
    - Organ
    - Eyes

- type: entity
  id: OrganDionaStomach
  parent: BaseDionaOrgan
  name: "шлунок"
  description: "Еквівалент шлунку діони, пахне спаржею та оцтом."
  components:
  - type: Sprite
    state: stomach
  - type: SolutionContainerManager
    solutions:
      stomach:
        maxVol: 50
      food:
        maxVol: 5
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 5
  - type: Organ # Shitmed
    slotId: stomach
  - type: Stomach
  - type: Metabolizer
    maxReagents: 6
    metabolizerTypes: [ Plant ]
    removeEmpty: true
    groups:
      - id: Food
      - id: Drink
      - id: Medicine
      - id: Poison
      - id: Narcotic
      - id: Alcohol
        rateModifier: 0.1
  - type: Tag # goob edit
    tags:
    - Meat
    - Organ
    - Stomach

- type: entity
  id: OrganDionaLungs
  parent: BaseDionaOrgan
  name: "легені"
  description: "Губчастий безлад слизових, схожих на листя структур. Здатні дихати як вуглекислим газом, так і киснем."
  components:
  - type: Sprite
    sprite: Mobs/Species/Human/organs.rsi
    layers:
      - state: lung-l
      - state: lung-r
  - type: Lung # Shitmed
  - type: Organ # Shitmed
    slotId: lungs
  - type: Metabolizer
    removeEmpty: true
    solutionOnBody: false
    solution: "Lung"
    metabolizerTypes: [ Plant ]
    groups:
    - id: Gas
      rateModifier: 100.0
  - type: SolutionContainerManager
    solutions:
      organ:
        maxVol: 10
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
      Lung:
        maxVol: 100
        canReact: False
  - type: Tag # goob edit
    tags:
    - Meat
    - Organ
    - Lungs

# Organs that turn into nymphs on removal
- type: entity
  id: OrganDionaBrainNymph
  parent: OrganDionaBrain
  categories: [ HideSpawnMenu ]
  name: "мозок"
  description: "Джерело неймовірного, нескінченного інтелекту. Хонк."
  components:
  - type: Brain
  - type: Nymph # This will make the organs turn into a nymph when they're removed.
    entityPrototype: OrganDionaNymphBrain
    transferMind: true
  - type: Tag # goob edit
    tags:
    - Meat
    - Organ
    - Brain

- type: entity
  id: OrganDionaStomachNymph
  parent: OrganDionaStomach
  categories: [ HideSpawnMenu ]
  name: "шлунок"
  description: "Гидота. Це складно переварити."
  components:
  - type: Nymph
    entityPrototype: OrganDionaNymphStomach
  - type: Tag # goob edit
    tags:
    - Meat
    - Organ
    - Stomach

- type: entity
  id: OrganDionaLungsNymph
  parent: OrganDionaLungs
  categories: [ HideSpawnMenu ]
  name: "легені"
  description: "Фільтрують кисень з атмосфери, який потім потрапляє в кровотік і використовується як переносник електронів."
  components:
  - type: Nymph
    entityPrototype: OrganDionaNymphLungs
  - type: Tag # goob edit
    tags:
    - Meat
    - Organ
    - Lungs

# Nymphs that the organs will turn into
- type: entity
  id: OrganDionaNymphBrain
  parent: MobDionaNymph
  categories: [ HideSpawnMenu ]
  name: "німфа діони"
  suffix: Brain
  description: "Містить мозок колись повністю сформованої Діони. Вбивство цього вб'є Діону назавжди. Ти монстр."
  components:
  - type: IsDeadIC
  - type: Body
    prototype: AnimalNymphBrain
  - type: VentCrawler # goob
  - type: SurgeryTarget
  - type: UserInterface
    interfaces:
      enum.SurgeryUIKey.Key:
        type: SurgeryBui

- type: entity
  id: OrganDionaNymphStomach
  parent: MobDionaNymphAccent
  categories: [ HideSpawnMenu ]
  name: "німфа діони"
  suffix: Stomach
  description: "Містить шлунок колись повністю сформованої Діони. Від цього він не стає смачнішим."
  components:
  - type: IsDeadIC
  - type: Body
    prototype: AnimalNymphStomach
  - type: VentCrawler # goob
  - type: SurgeryTarget
  - type: UserInterface
    interfaces:
      enum.SurgeryUIKey.Key:
        type: SurgeryBui

- type: entity
  id: OrganDionaNymphLungs
  parent: MobDionaNymphAccent
  categories: [ HideSpawnMenu ]
  name: "німфа діони"
  suffix: Lungs
  description: "Містить легені колись повністю сформованої Діони. Захоплює дух."
  components:
  - type: IsDeadIC
  - type: Body
    prototype: AnimalNymphLungs
  - type: VentCrawler # goob
  - type: SurgeryTarget
  - type: UserInterface
    interfaces:
      enum.SurgeryUIKey.Key:
        type: SurgeryBui
