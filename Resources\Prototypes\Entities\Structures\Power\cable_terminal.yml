- type: entity
  id: CableTerminal
  name: "кабельна клема"
  description: "Вхід для пристроїв типу SMES. Червоні кабелі повинні бути спрямовані до пристрою"
  placement:
    mode: SnapgridCenter
  components:
    - type: Sprite
      sprite: Structures/Power/cable_terminal.rsi
      state: term
      drawdepth: BelowFloor
    - type: Clickable
    - type: InteractionOutline
    - type: CollideOnAnchor
    - type: Transform
      anchored: true
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: Metallic
    - type: RCDDeconstructable
      cost: 2
      delay: 0
      fx: EffectRCDConstruct0  
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 100
          behaviors:
            - !type:DoActsBehavior
              acts: ["Destruction"]
    - type: SubFloorHide
      blockAmbience: false
      blockInteractions: false
    - type: Construction
      graph: CableTerminal
      node: cable_terminal
    - type: NodeContainer
      nodes:
        # Just define nodes for both MV and HV. One will end up unused in 99% of cases
        # but it means one cable terminal type.
        powerHV:
          !type:CableTerminalNode
          nodeGroupID: HVPower
        powerMV:
          !type:CableTerminalNode
          nodeGroupID: MVPower

- type: entity
  id: CableTerminalUncuttable
  parent: CableTerminal
  suffix: uncuttable
  components:
  - type: Cable
    cuttingQuality: null