- type: entity
  id: ReinforcedWindow
  name: "армоване вікно"
  parent: Window
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Windows/reinforced_window.rsi
  - type: Icon
    sprite: Structures/Windows/reinforced_window.rsi
  - type: Repairable
    fuelCost: 10
    doAfterDelay: 2
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: RGlass
  - type: RCDDeconstructable
    cost: 6
    delay: 6
    fx: EffectRCDDeconstruct6
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 150 #excess damage (nuke?). avoid computational cost of spawning entities.
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
    - trigger:
        !type:DamageTrigger
        damage: 75
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassReinforced:
            min: 1
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: IconSmooth
    base: rwindow
  - type: Construction
    graph: Window
    node: reinforcedWindow
  - type: Appearance
  - type: DamageVisuals
    thresholds: [4, 8, 12]
    damageDivisor: 4
    trackAllDamage: true
    damageOverlay:
      sprite: Structures/Windows/cracks.rsi

- type: entity
  parent: ReinforcedWindow
  id: TintedWindow
  name: "тоноване скло"
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Windows/tinted_window.rsi
  - type: Icon
    sprite: Structures/Windows/tinted_window.rsi
  - type: IconSmooth
    base: twindow
  - type: Construction
    graph: Window
    node: tintedWindow
  - type: Occluder
  - type: StaticPrice
    price: 44

- type: entity
  id: WindowReinforcedDirectional
  parent: WindowDirectional
  name: "спрямоване армоване вікно"
  description: "Чітко, але жорстко."
  placement:
    mode: SnapgridCenter
    snap:
    - Window
  components:
  - type: Sprite
    sprite: Structures/Windows/directional.rsi
    state: reinforced_window
  - type: Icon
    sprite: Structures/Windows/directional.rsi
    state: reinforced_window
  - type: Construction
    graph: WindowDirectional
    node: windowReinforcedDirectional
  - type: Appearance
  - type: DamageVisuals
    thresholds: [4, 8, 12]
    damageDivisor: 10
    trackAllDamage: true
    damageOverlay:
      sprite: Structures/Windows/cracks_directional.rsi
  - type: Damageable
    damageModifierSet: RGlass
  - type: RCDDeconstructable
    cost: 4
    delay: 4
    fx: EffectRCDDeconstruct4
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 150 #excess damage (nuke?). avoid computational cost of spawning entities.
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassReinforced:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: StaticPrice
    price: 22

- type: entity
  parent: ReinforcedWindow
  id: ReinforcedWindowDiagonal
  suffix: diagonal
  placement:
    mode: SnapgridCenter
    snap:
    - Window
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Windows/reinforced_window_diagonal.rsi
    state: state0
  - type: IconSmooth
    mode: Diagonal
    key: walls
    base: state
  - type: Icon
    sprite: Structures/Windows/reinforced_window_diagonal.rsi
    state: state0
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PolygonShape
            vertices:
            - "-0.5,-0.5"
            - "0.5,0.5"
            - "0.5,-0.5"
        mask:
        - FullTileMask
        layer:
        - GlassLayer
  - type: Airtight
    noAirWhenFullyAirBlocked: false
    airBlockedDirection:
    - South
    - East
  - type: DamageVisuals
    damageOverlay:
      sprite: Structures/Windows/cracks_diagonal.rsi
  - type: Construction
    graph: WindowDiagonal
    node: reinforcedWindowDiagonal
