- type: construction
  name: "сірий гострий кашкет"
  id: BladedFlatcapGrey
  graph: BladedFlatcapGrey
  startNode: start
  targetNode: icon
  category: construction-category-weapons
  description: "Непримітний капелюх із зашитими в поля осколками скла."
  icon: { sprite: Clothing/Head/Hats/greyflatcap.rsi, state: icon }
  objectType: Item

- type: construction
  name: "коричневий гострий кашкет"
  id: BladedFlatcapBrown
  graph: BladedFlatcapBrown
  startNode: start
  targetNode: icon
  category: construction-category-weapons
  description: "Непримітний капелюх із зашитими в поля осколками скла."
  icon: { sprite: Clothing/Head/Hats/brownflatcap.rsi, state: icon }
  objectType: Item

- type: construction
  name: "скляний ніж"
  id: Shiv
  graph: Shiv
  startNode: start
  targetNode: icon
  category: construction-category-weapons
  description: "Уламок скла з обмотаним навколо нього шматком тканини."
  icon: { sprite: Objects/Weapons/Melee/shiv.rsi, state: icon }
  objectType: Item

- type: construction
  name: "посилене лезо"
  id: ReinforcedShiv
  graph: ReinforcedShiv
  startNode: start
  targetNode: icon
  category: construction-category-weapons
  description: "Уламок армованого скла з обмотаним навколо нього шматком тканини."
  icon: { sprite: Objects/Weapons/Melee/reinforced_shiv.rsi, state: icon }
  objectType: Item

- type: construction
  name: "плазмовий ніж"
  id: PlasmaShiv
  graph: PlasmaShiv
  startNode: start
  targetNode: icon
  category: construction-category-weapons
  description: "Уламок плазмового скла з обмотаним навколо нього шматком тканини."
  icon: { sprite: Objects/Weapons/Melee/plasma_shiv.rsi, state: icon }
  objectType: Item

- type: construction
  name: "урановий ніж"
  id: UraniumShiv
  graph: UraniumShiv
  startNode: start
  targetNode: icon
  category: construction-category-weapons
  description: "Уламок уранового скла з обмотаним навколо нього шматком тканини."
  icon: { sprite: Objects/Weapons/Melee/uranium_shiv.rsi, state: icon }
  objectType: Item

- type: construction
  name: "грубий спис"
  id: Spear
  graph: Spear
  startNode: start
  targetNode: spear
  category: construction-category-weapons
  description: "Грубий спис для того, щоб продірявити когось."
  icon: { sprite: Objects/Weapons/Melee/spear.rsi, state: spear }
  objectType: Item

- type: construction
  name: "грубий армований спис"
  id: SpearReinforced
  graph: SpearReinforced
  startNode: start
  targetNode: spear
  category: construction-category-weapons
  description: "Грубий армований спис для тих випадків, коли потрібно наробити в комусь дірок."
  icon: { sprite: Objects/Weapons/Melee/reinforced_spear.rsi, state: spear }
  objectType: Item

- type: construction
  name: "сирий плазмовий спис"
  id: SpearPlasma
  graph: SpearPlasma
  startNode: start
  targetNode: spear
  category: construction-category-weapons
  description: "Грубий плазмовий спис для тих випадків, коли потрібно наробити в комусь дірок."
  icon: { sprite: Objects/Weapons/Melee/plasma_spear.rsi, state: spear }
  objectType: Item

- type: construction
  name: "сирий урановий спис"
  id: SpearUranium
  graph: SpearUranium
  startNode: start
  targetNode: spear
  category: construction-category-weapons
  description: "Спис з необробленого урану для тих випадків, коли потрібно наробити в комусь дірок."
  icon: { sprite: Objects/Weapons/Melee/uranium_spear.rsi, state: spear }
  objectType: Item

- type: construction
  name: "імпровізована бола"
  id: Bola
  graph: Bola
  startNode: start
  targetNode: bola
  category: construction-category-weapons
  description: "Проста зброя для того, щоб підставити комусь підніжку на відстані."
  icon: { sprite: Objects/Weapons/Throwable/bola.rsi, state: icon }
  objectType: Item

- type: construction
  name: "дерев'яний баклер"
  id: WoodenBuckler
  graph: WoodenBuckler
  startNode: start
  targetNode: woodenBuckler
  category: construction-category-weapons
  description: "Гарно вирізьблений дерев'яний щит!"
  icon: { sprite: Objects/Weapons/Melee/shields.rsi, state: buckler-icon }
  objectType: Item

- type: construction
  name: "імпровізований щит"
  id: MakeshiftShield
  graph: MakeshiftShield
  startNode: start
  targetNode: makeshiftShield
  category: construction-category-weapons
  description: "Сирий і розвалюється. Навіщо ви це робите?"
  icon: { sprite: Objects/Weapons/Melee/shields.rsi, state: makeshift-icon }
  objectType: Item

- type: construction
  name: "імпровізована стріла"
  id: ImprovisedArrow
  graph: ImprovisedArrow
  startNode: start
  targetNode: ImprovisedArrow
  category: construction-category-weapons
  description: "Стріла з наконечником зі шматків скла, для використання з луком."
  icon: { sprite: Objects/Weapons/Guns/Bow/bow.rsi, state: wielded-arrow }
  objectType: Item

- type: construction
  name: "імпровізований лук"
  id: ImprovisedBow
  graph: ImprovisedBow
  startNode: start
  targetNode: ImprovisedBow
  category: construction-category-weapons
  description: "Недбало сконструйований лук, зроблений з дерева та тканини. Це небагато, але він виконував свою роботу протягом тисячоліть."
  icon: { sprite: Objects/Weapons/Guns/Bow/bow.rsi, state: unwielded }
  objectType: Item

- type: construction
  name: "кістяний спис"
  id: SpearBone
  graph: SpearBone
  startNode: start
  targetNode: spear
  category: construction-category-weapons
  description: "Кістки та шовк у поєднанні."
  icon: { sprite: Objects/Weapons/Melee/bone_spear.rsi, state: spear }
  objectType: Item
