
### UI

# Shown when an RCD is examined in details range
rcd-component-examine-detail = Поточний режим {$mode}.

### Interaction Messages

# Shown when changing RCD Mode
rcd-component-change-mode = Режим RCD виставлено на {$mode}.

rcd-component-no-ammo-message = В RCD закінчились заряди!
rcd-component-tile-obstructed-message = Ви не можете деконструювати тайли, коли на них щось є!
rcd-component-tile-indestructible-message = Цю плитку неможливо знищити!
rcd-component-deconstruct-target-not-on-whitelist-message = Ви не можете деконструювати це!
rcd-component-cannot-build-floor-tile-not-empty-message = Ви можете побудувати підлогу тільки в космосі!
rcd-component-cannot-build-wall-tile-not-empty-message = Ви не можете побудувати стіну в космосі!
rcd-component-cannot-build-airlock-tile-not-empty-message = Ви не можете побудувати шлюз в космосі!

rcd-component-examine-mode-details = Наразі встановлено режим '{$mode}'.
rcd-component-examine-build-details = Наразі встановлено збірку {MAKEPLURAL($name)}.


### Повідомлення про взаємодію

# Зміна режиму
rcd-component-change-build-mode = Тепер RCD налаштовано на збірку {$name}.

# Кількість набоїв
rcd-component-insufficient-ammo-message = У RCD не вистачає зарядів!

# Деконструкція
rcd-component-nothing-to-deconstruct-message = Тут немає чого деконструювати!
rcd-component-no-valid-grid = Ви занадто далеко у відкритому просторі, щоб будувати тут!
rcd-component-must-build-on-empty-tile-message = Фундамент тут вже є!
rcd-component-cannot-build-on-empty-tile-message = Ви не можете побудувати це без фундаменту!
rcd-component-must-build-on-subfloor-message = Ви можете встановити його тільки на відкриту підлогу!
rcd-component-cannot-build-on-subfloor-message = Ви не можете побудувати це на відкритій підлозі!
rcd-component-cannot-build-on-occupied-tile-message = Тут не можна будувати, місце вже зайняте!
rcd-component-cannot-build-identical-tile = Ця плитка там вже є!


### Назви категорій
rcd-component-walls-and-flooring = Стіни та підлога
rcd-component-windows-and-grilles = Вікна та решітки
rcd-component-airlocks = Шлюзи
rcd-component-electrical = Електричні
rcd-component-lighting = Освітлення


### Назви прототипів (примітка: елементи, що конструюються, будуть пуралізовані)
rcd-component-deconstruct = деконструювати
rcd-component-floor-steel = сталева черепиця
rcd-component-plating = пластина корпусу
rcd-component-piping = Трубопровід
rcd-component-atmosphericutility = Атмосферні утиліти
rcd-component-pumps = Насоси та клапани
rcd-component-vents = Вентиляційні отвори