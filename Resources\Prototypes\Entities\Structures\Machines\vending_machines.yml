- type: entity
  id: VendingMachine
  parent: BaseMachinePowered
  name: "торговий автомат"
  description: "Просто додайте капіталізм!"
  abstract: true
  components:
  - type: StationAiWhitelist
  - type: AmbientOnPowered
  - type: AmbientSound
    volume: -9
    range: 3
    enabled: false
    sound:
      path: /Audio/Ambience/Objects/vending_machine_hum.ogg
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/empty.rsi
    snapCardinals: false
  - type: Physics
    bodyType: Static
  - type: Transform
    noRot: true
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.45,0.25,0.45"
        mask:
        - MachineMask
        layer:
        - MachineLayer
        density: 1000
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: ["Breakage"]
      - !type:EjectVendorItems
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
  - type: Repairable
    doAfterDelay: 8
  - type: ActivatableUI
    key: enum.VendingMachineUiKey.Key
  - type: ActivatableUIRequiresPower
  - type: UserInterface
    interfaces:
      enum.VendingMachineUiKey.Key:
        type: VendingMachineBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: WiresPanel
  - type: Wires
    boardName: wires-board-name-vendingmachine
    layoutId: Vending
  - type: Anchorable
  - type: TypingIndicator
    proto: robot
  - type: Speech
    speechVerb: Robotic
    speechSounds: Vending
  - type: IntrinsicRadioReceiver
  - type: IntrinsicRadioTransmitter
    channels:
    - Binary
  - type: ActiveRadio
    channels:
    - Binary
    - Common
  - type: DoAfter
  - type: Electrified
    enabled: false
    usesApcPower: true
  - type: EtherealLight
  - type: PointLight
    enabled: false
    castShadows: false
    radius: 2
    energy: 1.5
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: LitOnPowered
  - type: ApcPowerReceiver
    powerLoad: 200
  - type: Actions
  - type: SentienceTarget
    flavorKind: station-event-random-sentience-flavor-mechanical
    weight: 0.025 # fuck you in particular (it now needs 40 vending machines to be as likely as 1 interesting animal)
  - type: StaticPrice
    price: 100
  - type: Appearance
  - type: WiresVisuals

- type: entity
  parent: VendingMachine
  id: VendingMachineCondiments
  name: "приправна станція"
  description: "Намащуйте ці густі липкі речовини на їжу для отримання повного смакового ефекту."
  components:
  - type: VendingMachine
    pack: CondimentInventory
    offState: off
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/condiments.rsi
    drawdepth: SmallObjects
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.3,-0.16,0.3,0.40"
        mask:
        - MachineMask
        layer:
        - MachineLayer
        density: 190
  - type: Advertise
    pack: CondimentVendAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Speech
  - type: Transform
    noRot: false

- type: entity
  parent: VendingMachine
  id: VendingMachineAmmo
  name: "визвольна станція"
  description: "Переважна кількість стародавнього патріотизму охоплює вас, коли ви просто дивитеся на машину."
  components:
  - type: VendingMachine
    pack: AmmoVendInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Advertise
    pack: AmmoVendAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Speech
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/ammo.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]

- type: entity
  parent: VendingMachine
  id: VendingMachineBooze
  name: "Booze-O-Mat"
  description: "Технологічне диво, яке нібито здатне змішати саме ту суміш, яку ви хочете випити, щойно ви попросите."
  components:
  - type: VendingMachine
    pack: BoozeOMatInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    denyState: deny-unshaded
    loopDeny: false
  - type: Advertise
    pack: BoozeOMatAds
  - type: SpeakOnUIClosed
    pack: BoozeOMatGoodbyes
  - type: Speech
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/boozeomat.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Bar"]]
  - type: GuideHelp
    guides:
    - Bartender

- type: entity
  parent: VendingMachine
  id: VendingMachineCart
  name: "PTech"
  description: "Вендинг PTech! Широкий вибір КПК, картриджів та всього іншого, що може знадобитися нудному штовхачу паперу!"
  components:
  - type: VendingMachine
    pack: PTechInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/VendingMachines/laptop.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.3
    color: "#ffb0b0"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: AccessReader
    access: [["HeadOfPersonnel"]]
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true

- type: entity
  parent: VendingMachine
  id: VendingMachineChefvend
  name: "ChefVend"
  description: "Постачальник інгредієнтів для всіх ваших потреб у шафах."
  components:
  - type: VendingMachine
    pack: ChefvendInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
  - type: Advertise
    pack: ChefvendAds
  - type: SpeakOnUIClosed
    pack: ChefvendGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/chefvend.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Service"]]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#4b93ad"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

- type: entity
  parent: VendingMachine
  id: VendingMachineCigs
  name: "ShadyCigs Deluxe"
  description: "Якщо ви хочете захворіти на рак, зробіть це стильно."
  components:
  - type: VendingMachine
    pack: CigaretteMachineInventory
    dispenseOnHitChance: 0.25
    dispenseOnHitThreshold: 2
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
  - type: Advertise
    pack: CigaretteMachineAds
  - type: SpeakOnUIClosed
    pack: CigaretteMachineGoodbyes
  - type: Speech
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/VendingMachines/cigs.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true

- type: entity
  parent: VendingMachine
  id: VendingMachineClothing
  name: "ClothesMate"
  description: "Торговий автомат для одягу."
  components:
  - type: VendingMachine
    pack: ClothesMateInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    denyState: deny-unshaded
  - type: Advertise
    pack: ClothesMateAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Speech
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/clothing.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#3db83b"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

- type: entity
  parent: VendingMachine
  id: VendingMachineWinter
  name: "WinterDrobe"
  description: "Найкраще місце, щоб насолодитися холодом!"
  components:
  - type: VendingMachine
    pack: WinterDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    denyState: deny-unshaded
  - type: Advertise
    pack: ClothesMateAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Speech
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/winterdrobe.rsi
    layers:
    - state: "off"
      map: [ "enum.VendingMachineVisualLayers.Base" ]
    - state: "off"
      map: [ "enum.VendingMachineVisualLayers.BaseUnshaded" ]
      shader: unshaded
    - state: panel
      map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#3db83b"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

- type: entity
  parent: VendingMachine
  id: VendingMachineCoffee
  name: "Найкращі гарячі напої від Solar"
  description: "Подається киплячим, щоб залишався гарячим протягом усієї зміни!"
  components:
  - type: VendingMachine
    pack: HotDrinksMachineInventory
    dispenseOnHitChance: 0.25
    dispenseOnHitThreshold: 2
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
    screenState: screen
    ejectDelay: 5
    soundVend: /Audio/Machines/machine_vend_hot_drink.ogg
    initialStockQuality: 0.33
  - type: Advertise
    pack: HotDrinksMachineAds
  - type: SpeakOnUIClosed
    pack: HotDrinksMachineGoodbyes
  - type: Speech
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/VendingMachines/coffee.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: "screen"
      map: ["enum.VendingMachineVisualLayers.Screen"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.3
    color: "#ad7c4b"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true

- type: entity
  parent: VendingMachine
  id: VendingMachineCola
  name: "Міцні безалкогольні напої"
  description: "Постачальник безалкогольних напоїв наданий компанією Robust Industries, LLC."
  components:
  - type: VendingMachine
    pack: RobustSoftdrinksInventory
    dispenseOnHitChance: 0.25
    dispenseOnHitThreshold: 2
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
    ejectDelay: 1.9
    initialStockQuality: 0.33
  - type: Advertise
    pack: RobustSoftdrinksAds
  - type: SpeakOnUIClosed
    pack: RobustSoftdrinksGoodbyes
  - type: Speech
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/VendingMachines/cola.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#3c5eb5"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true

- type: entity
  parent: VendingMachineCola
  id: VendingMachineColaBlack
  suffix: Black
  components:
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/soda2.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#423438"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

- type: entity
  parent: VendingMachineCola
  id: VendingMachineColaRed
  name: "Космічний продавець кока-коли"
  description: "Він продає колу в космосі."
  components:
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/cola.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#A50824"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

- type: entity
  parent: VendingMachine
  id: VendingMachineSpaceUp
  name: "Space-Up! Продавець"
  description: "Побалуйте себе вибухом смаку."
  components:
  - type: VendingMachine
    pack: SpaceUpInventory
    offState: off
    normalState: normal-unshaded
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/spaceup.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#44964A"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

- type: entity
  parent: VendingMachineCola
  id: VendingMachineSoda
  suffix: Soda
  components:
  - type: VendingMachine
    pack: SodaInventory
    offState: off
    normalState: normal-unshaded
    denyState: deny-unshaded
    ejectState: eject-unshaded
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/VendingMachines/soda.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#CBC6BE"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true

- type: entity
  parent: VendingMachine
  id: VendingMachineStarkist
  name: "Постачальник Star-kist"
  description: "Смак зірки в рідкому вигляді."
  components:
  - type: VendingMachine
    pack: StarkistInventory
    offState: off
    normalState: normal-unshaded
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/starkist.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#D3A44D"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: Transform
    noRot: true

- type: entity
  parent: VendingMachine
  id: VendingMachineShamblersJuice
  name: "Продавець соку Shambler's"
  description: "~Налий мені соку Шамблер!"
  components:
  - type: VendingMachine
    pack: ShamblersJuiceInventory
    dispenseOnHitChance: 0.25
    dispenseOnHitThreshold: 2
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
    ejectDelay: 1.9
    initialStockQuality: 0.33
  - type: Advertise
    pack: RobustSoftdrinksAds
  - type: Speech
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/shamblersjuice.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#66538F"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

- type: entity
  parent: VendingMachine
  id: VendingMachinePwrGame
  name: "Постачальник ігор Pwr"
  description: "Ви хочете, ми маємо це. Зроблено у партнерстві з \"Салати Влада\"."
  components:
  - type: VendingMachine
    pack: PwrGameInventory
    dispenseOnHitChance: 0.25
    dispenseOnHitThreshold: 2
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
    ejectDelay: 1.9
    initialStockQuality: 0.33
  - type: Advertise
    pack: RobustSoftdrinksAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Speech
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/pwrgame.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#6927C5"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

- type: entity
  parent: VendingMachine
  id: VendingMachineDrGibb
  name: "Доктор Гібб Вендор"
  description: "Консерви - вибух різноманітних смаків саме у цього постачальника!"
  components:
  - type: VendingMachine
    pack: DrGibbInventory
    dispenseOnHitChance: 0.25
    dispenseOnHitThreshold: 2
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
    ejectDelay: 1.9
    initialStockQuality: 0.33
  - type: Advertise
    pack: RobustSoftdrinksAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Speech
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/gib.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#D82929"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

- type: entity
  parent: VendingMachine
  id: VendingMachineDinnerware
  name: "Постачальник кухонного посуду Plasteel"
  description: "Постачальник кухонного та ресторанного обладнання."
  components:
  - type: VendingMachine
    pack: DinnerwareInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
  - type: Advertise
    pack: DinnerwareAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/VendingMachines/dinnerware.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Service"]]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#4b93ad"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true

- type: entity
  parent: VendingMachine
  id: VendingMachineMagivend
  name: "MagiVend"
  description: "Чарівний торговий автомат."
  components:
  - type: VendingMachine
    pack: MagiVendInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    denyState: deny-unshaded
    ejectState: eject-unshaded
  - type: Advertise
    pack: MagiVendAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/VendingMachines/magivend.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#9a18d6"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true

- type: entity
  parent: VendingMachine
  id: VendingMachineDiscount
  name: "Знижка від Дена"
  description: "Торговий автомат зі зниженими закусками від сумнозвісної франшизи \"Discount Dan\"."
  components:
  - type: VendingMachine
    pack: DiscountDansInventory
    dispenseOnHitChance: 0.25
    dispenseOnHitThreshold: 2
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    initialStockQuality: 0.33
  - type: Advertise
    pack: DiscountDansAds
  - type: SpeakOnUIClosed
    pack: DiscountDansGoodbyes
  - type: Speech
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/discount.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - texture: Structures/Machines/VendingMachines/maintenance_panel.png
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#6148c7"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

- type: entity
  parent: VendingMachine
  id: VendingMachineEngivend
  name: "Engi-Vend"
  description: "Торгівля запасними інструментами. Що? Ви очікували дотепного опису?"
  components:
  - type: VendingMachine
    pack: EngiVendInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/VendingMachines/engivend.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Engineering"]]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#b89e2a"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true

- type: entity
  parent: VendingMachine
  id: VendingMachineMedical
  name: "НаноМед Плюс"
  description: "Це дозатор медичних препаратів. Тільки натуральні хімікати! (І інвалідні візки, чому б і ні)" # Goobstation
  components:
  - type: VendingMachine
    pack: NanoMedPlusInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
    ejectDelay: 0.6
  - type: Advertise
    pack: NanoMedAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/medivend.rsi
    noRot: true
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Medical"]]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#9dc5c9"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: GuideHelp
    guides:
    - Medical Doctor
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true

- type: entity
  parent: VendingMachine
  id: VendingMachineNutri
  name: "NutriMax"
  description: "Торговий автомат з поживними речовинами для рослин та ботанічними інструментами."
  components:
  - type: VendingMachine
    pack: NutriMaxInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
  - type: Advertise
    pack: NutriMaxAds
  - type: SpeakOnUIClosed
    pack: NutriMaxGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/nutri_green.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Hydroponics"]]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#326e3f"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

- type: entity
  parent: VendingMachine
  id: VendingMachineSec
  name: "SecTech"
  description: "Торговий автомат, що містить охоронне обладнання. На етикетці написано \"ТІЛЬКИ ДЛЯ ПЕРСОНАЛУ СЛУЖБИ БЕЗПЕКИ\"."
  components:
  - type: VendingMachine
    pack: SecTechInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
  - type: Advertise
    pack: SecTechAds
  - type: SpeakOnUIClosed
    pack: SecTechGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/security.rsi
    noRot: true
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Security"]]
  - type: PointLight
    radius: 2
    energy: 1.8
    color: "#78645c"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: GuideHelp
    guides:
    - Security
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true

- type: entity
  parent: VendingMachine
  id: VendingMachineSeedsUnlocked
  name: "MegaSeed Servitor"
  description: "Для тих, кому швидко потрібне насіння. Безумовно, найкращий вибір насіння на станції!"
  suffix: Unlocked
  components:
  - type: VendingMachine
    pack: MegaSeedServitorInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
  - type: Advertise
    pack: MegaSeedAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/seeds_green.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#326e3f"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

- type: entity
  parent: VendingMachineSeedsUnlocked
  id: VendingMachineSeeds
  suffix: Hydroponics
  components:
  - type: AccessReader
    access: [["Hydroponics"]]

- type: entity
  parent: VendingMachine
  id: VendingMachineSnack
  name: "Getmore Chocolate Corp"
  description: "Автомат із закусками, люб'язно наданий шоколадною корпорацією Getmore, що базується на Марсі."
  components:
  - type: VendingMachine
    pack: GetmoreChocolateCorpInventory
    dispenseOnHitChance: 0.25
    dispenseOnHitThreshold: 2
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
    initialStockQuality: 0.33
  - type: Advertise
    pack: GetmoreChocolateCorpAds
  - type: SpeakOnUIClosed
    pack: GetmoreChocolateCorpGoodbyes
  - type: Speech
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/VendingMachines/snack.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#c73434"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true

- type: entity
  parent: VendingMachineSnack
  id: VendingMachineSustenance
  name: "Постачальник послуг з життєзабезпечення"
  description: "Торговий автомат, який продає їжу, як того вимагає розділ 47-C Угоди про етичне поводження з ув'язненими NT."
  components:
  - type: VendingMachine
    pack: SustenanceInventory
    dispenseOnHitChance: 0.25
    dispenseOnHitThreshold: 2
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/sustenance.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#737785"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

- type: entity
  parent: VendingMachineSnack
  id: VendingMachineSnackBlue
  suffix: Blue
  components:
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/snack-blue.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#3c5eb5"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

- type: entity
  parent: VendingMachineSnack
  id: VendingMachineSnackOrange
  suffix: Orange
  components:
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/snack-orange.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#CE3401"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

- type: entity
  parent: VendingMachineSnack
  id: VendingMachineSnackGreen
  suffix: Green
  components:
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/snack-green.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#5F6A1C"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

- type: entity
  parent: VendingMachineSnack
  id: VendingMachineSnackTeal
  suffix: Teal
  components:
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/snack-teal.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#207E79"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

- type: entity
  parent: VendingMachine
  id: VendingMachineSovietSoda
  name: "БОДА"
  description: "Старий автомат із солодкою водою."
  components:
  - type: VendingMachine
    pack: BodaInventory
    dispenseOnHitChance: 0.25
    dispenseOnHitThreshold: 2
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
    initialStockQuality: 0.33
  - type: Advertise
    pack: BodaAds
  - type: SpeakOnUIClosed
    pack: BodaGoodbyes
  - type: Speech
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/sodasoviet.rsi
    noRot: true
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#B80F0A"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true

- type: entity
  parent: VendingMachine
  id: VendingMachineTheater
  name: "AutoDrobe"
  description: "Торговий автомат з костюмами."
  components:
  - type: VendingMachine
    pack: AutoDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
    screenState: screen
  - type: Advertise
    pack: AutoDrobeAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Speech
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/theater.rsi
    noRot: true
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
    - state: "screen"
      map: ["enum.VendingMachineVisualLayers.Screen"]
      shader: unshaded
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#c73434"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true

- type: entity
  parent: VendingMachine
  id: VendingMachineVendomat
  name: "Vendomat"
  description: "Тільки найкраще надійне обладнання в космосі!"
  components:
  - type: VendingMachine
    pack: VendomatInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
  - type: Advertise
    pack: VendomatAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Speech
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/VendingMachines/vendomat.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#9dc5c9"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true

- type: entity
  parent: VendingMachine
  id: VendingMachineRobotics
  name: "Robotech Deluxe"
  description: "Всі інструменти, необхідні для створення власної армії роботів."
  components:
  - type: VendingMachine
    pack: RoboticsInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
  - type: Advertise
    pack: VendomatAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Speech
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/VendingMachines/robotics.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Robotics"]]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#B0ADA9"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true
  - type: GuideHelp
    guides:
    - Robotics

- type: entity
  parent: VendingMachine
  id: VendingMachineYouTool
  name: "YouTool"
  description: "Торговий автомат зі стандартними інструментами. Напис на етикетці: Інструменти для інструментів."
  components:
  - type: VendingMachine
    pack: YouToolInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/VendingMachines/youtool.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#d4ab33"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true

- type: entity
  parent: VendingMachine
  id: VendingMachineGames
  name: "Хороша чиста забава"
  description: "Продає речі, які, ймовірно, не сподобаються капітану та начальнику відділу кадрів, якщо ви будете возитися з ними замість того, щоб виконувати свою роботу..."
  components:
  - type: VendingMachine
    pack: GoodCleanFunInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    ejectDelay: 1.8
  - type: Advertise
    pack: GoodCleanFunAds
  - type: SpeakOnUIClosed
    pack: GoodCleanFunGoodbyes
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/VendingMachines/games.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#326e3f"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true

- type: entity
  parent: VendingMachine
  id: VendingMachineChang
  name: "Пане Чанг"
  description: "Автомат для приготування китайської їжі з самообслуговуванням, для всіх ваших потреб в китайській їжі."
  components:
  - type: VendingMachine
    pack: ChangInventory
    dispenseOnHitChance: 0.25
    dispenseOnHitThreshold: 2
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    denyState: deny-unshaded
    ejectState: eject-unshaded
    initialStockQuality: 0.33
  - type: Advertise
    pack: ChangAds
  - type: SpeakOnUIClosed
    pack: ChangGoodbyes
  - type: Speech
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/VendingMachines/snix.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#ffe599"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true

- type: entity
  parent: VendingMachine
  id: VendingMachineSalvage
  name: "Постачальник металобрухту"
  description: "Найкращий друг гнома!"
  components:
  #- type: VendingMachine # DeltaV: Use mining points instead of limited stock
  #  pack: SalvageEquipmentInventory
  #  offState: off
  #  brokenState: broken
  #  normalState: normal-unshaded
  #  denyState: deny-unshaded
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/mining.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#9dc5c9"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: ShopVendor # DeltaV
    pack: SalvageVendorInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    denyState: deny-unshaded
  - type: PointsVendor # DeltaV
  - type: UserInterface # DeltaV: Replace vending machine BUI with shop vendor
    interfaces:
      enum.VendingMachineUiKey.Key:
        type: ShopVendorBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
      enum.MiningVoucherUiKey.Key:
        type: MiningVoucherBoundUserInterface
  - type: Wires # DeltaV: Use shop vendor wires layout
    layoutId: ShopVendor
  - type: AccessReader
    access: [["Salvage"]]
  - type: GuideHelp
    guides:
    - Salvage
  - type: MiningVendor # Lavaland Change
    kits:
    - MiningCrusher
    - MiningExtraction
    #- MiningResonator
    #- MiningSurvival
    #- MiningDrone
    - MiningConscription

- type: entity
  parent: VendingMachine
  id: VendingMachineDonut
  name: "Пончики Monkin' Donuts"
  description: "Постачальник пончиків наданий компанією Robust Industries, LLC."
  components:
  - type: VendingMachine
    pack: DonutInventory
    dispenseOnHitChance: 0.25
    dispenseOnHitThreshold: 2
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    initialStockQuality: 0.33
  - type: Advertise
    pack: DonutAds
  - type: SpeakOnUIClosed
    pack: DonutGoodbyes
  - type: Speech
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/donut.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#d4ab33"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

# wallmounted machines

- type: entity
  id: VendingMachineWallmount
  parent: VendingMachine
  name: "торговий автомат"
  abstract: true
  placement:
    mode: SnapgridCenter
    snap:
    - Wallmount
  components:
  - type: Sprite
    drawdepth: WallMountedItems
    snapCardinals: false
  - type: WallMount
    arc: 175
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true

- type: entity
  parent: VendingMachineWallmount
  id: VendingMachineWallMedical
  name: "NanoMed"
  description: "Це настінний диспенсер для медичного обладнання. Тільки натуральні хімікати!"
  components:
  - type: VendingMachine
    pack: NanoMedInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    denyState: deny-unshaded
    ejectState: eject-unshaded
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/VendingMachines/wallmed.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - texture: Structures/Machines/VendingMachines/maintenance_panel.png
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Medical"]]
  - type: GuideHelp
    guides:
    - Medical
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#9dc5c9"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

# job clothing

- type: entity
  parent: VendingMachine
  id: VendingMachineHydrobe
  name: "HyDrobe"
  description: "Автомат з промовистою назвою. Він видає одяг та спорядження, пов'язані з ботанікою."
  components:
  - type: VendingMachine
    pack: HyDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Advertise
    pack: HyDrobeAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/hydrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Hydroponics"]]

- type: entity
  parent: VendingMachine
  id: VendingMachineLawDrobe
  name: "LawDrobe"
  description: "Протестую! Цей гардероб видає верховенство права... і адвокатський одяг..."
  components:
  - type: VendingMachine
    pack: LawDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Advertise
    pack: LawDrobeAds
  - type: SpeakOnUIClosed
    pack: LawDrobeGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/lawdrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Justice"]]

- type: entity
  parent: VendingMachine
  id: VendingMachineSecDrobe
  name: "SecDrobe"
  description: "Торговий автомат для продажу одягу, пов'язаного з охороною та безпекою!"
  components:
  - type: VendingMachine
    pack: SecDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Advertise
    pack: SecDrobeAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/secdrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Security"]]

- type: entity
  parent: VendingMachine
  id: VendingBarDrobe
  name: "BarDrobe"
  description: "Стильний продавець для продажу найстильнішого одягу для бару!"
  components:
  - type: VendingMachine
    pack: BarDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Advertise
    pack: BarDrobeAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/bardrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Bar"]]

- type: entity
  parent: VendingMachine
  id: VendingMachineChapel
  name: "PietyVend"
  components:
  - type: VendingMachine
    pack: PietyVendInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    denyState: deny-unshaded
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/chapdrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Chapel"]]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#CCCCCC" #The holy C
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

- type: entity
  parent: VendingMachine
  id: VendingMachineCargoDrobe
  name: "LogiDrobe" # DeltaV - Logistics Department replacing Cargo
  description: "Високотехнологічний торговий автомат для безкоштовного придбання одягу, пов'язаного з логістикою." # DeltaV - Logistics Department replacing Cargo
  components:
  - type: VendingMachine
    pack: CargoDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Advertise
    pack: CargoDrobeAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/cargodrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Cargo"]]

- type: entity
  parent: VendingMachine
  id: VendingMachineMediDrobe
  name: "MediDrobe"
  description: "Торговий автомат, який, за чутками, може видавати одяг для медичного персоналу."
  components:
  - type: VendingMachine
    pack: MediDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Advertise
    pack: MediDrobeAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/medidrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Medical"]]

- type: entity
  parent: VendingMachine
  id: VendingMachineChemDrobe
  name: "ChemDrobe"
  description: "Торговий автомат для видачі одягу, пов'язаного з хімією."
  components:
  - type: VendingMachine
    pack: ChemDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Advertise
    pack: ChemDrobeAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/chemdrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Chemistry"]]

- type: entity
  parent: VendingMachine
  id: VendingMachineCuraDrobe
  name: "CuraDrobe"
  description: "Постачальник з невеликим асортиментом, здатний продавати одяг лише для кураторів та бібліотекарів."
  components:
  - type: VendingMachine
    pack: CuraDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Advertise
    pack: CuraDrobeAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/curadrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Library"]]

- type: entity
  parent: VendingMachine
  id: VendingMachineAtmosDrobe
  name: "AtmosDrobe"
  description: "Цей відносно невідомий торговий автомат видає одяг для техніків-атмосферистів, не менш невідомої професії."
  components:
  - type: VendingMachine
    pack: AtmosDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Advertise
    pack: AtmosDrobeAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/atmosdrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Engineering"]]

- type: entity
  parent: VendingMachine
  id: VendingMachineEngiDrobe
  name: "EngiDrobe"
  description: "Торговий автомат, відомий тим, що продає одяг промислового класу."
  components:
  - type: VendingMachine
    pack: EngiDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Advertise
    pack: EngiDrobeAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/engidrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Engineering"]]

- type: entity
  parent: VendingMachine
  id: VendingMachineChefDrobe
  name: "ChefDrobe"
  description: "Цей торговий автомат, можливо, не видає м'ясо, але він точно видає одяг для кухарів."
  components:
  - type: VendingMachine
    pack: ChefDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Advertise
    pack: ChefDrobeAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/chefdrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Kitchen"]]

- type: entity
  parent: VendingMachine
  id: VendingMachineDetDrobe
  name: "DetDrobe"
  description: "Машина для всіх ваших детективних потреб, доки вам потрібен одяг."
  components:
  - type: VendingMachine
    pack: DetDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Advertise
    pack: DetDrobeAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/detdrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Detective"]]

- type: entity
  parent: VendingMachine
  id: VendingMachineJaniDrobe
  name: "JaniDrobe"
  description: "Самоочисний торговий автомат, здатний видавати одяг для прибиральників."
  components:
  - type: VendingMachine
    pack: JaniDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    denyState: deny-unshaded
    ejectState: eject-unshaded
  - type: Advertise
    pack: JaniDrobeAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/lavatory.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Janitor"]]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#9dc5c9"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

- type: entity
  parent: VendingMachine
  id: VendingMachineSciDrobe
  name: "SciDrobe"
  description: "Простий торговий автомат, придатний для видачі добре пошитого наукового одягу. Схвалений космічними кубинцями."
  components:
  - type: VendingMachine
    pack: SciDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Advertise
    pack: SciDrobeAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/scidrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Research"]]

- type: entity
  parent: VendingMachine
  id: VendingMachineSyndieDrobe
  name: "SyndieDrobe"
  description: "Гардеробна машина, закодована синдикатом, містить елітне вбрання для різних операцій."
  components:
  - type: VendingMachine
    pack: SyndieDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Advertise
    pack: SyndieDrobeAds
  - type: SpeakOnUIClosed
    pack: SyndieDrobeGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/syndiedrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["NuclearOperative"], ["SyndicateAgent"]]

- type: entity
  parent: VendingMachine
  id: VendingMachineRoboDrobe
  name: "RoboDrobe"
  description: "Торговий автомат, призначений для видачі одягу, відомий лише робототехнікам."
  components:
  - type: VendingMachine
    pack: RoboDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Advertise
    pack: RoboDrobeAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/robodrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Robotics"]]

- type: entity
  parent: VendingMachine
  id: VendingMachineGeneDrobe
  name: "GeneDrobe"
  description: "Машина для видачі одягу, пов'язаного з генетикою."
  components:
  - type: VendingMachine
    pack: GeneDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Advertise
    pack: GeneDrobeAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/genedrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Medical"]]

- type: entity
  parent: VendingMachine
  id: VendingMachineViroDrobe
  name: "ViroDrobe"
  description: "Нестерилізована машина для видачі одягу, пов'язаного з вірусологією."
  components:
  - type: VendingMachine
    pack: ViroDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Advertise
    pack: ViroDrobeAds
  - type: SpeakOnUIClosed
    pack: GenericVendGoodbyes
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/virodrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Medical"]]

- type: entity
  parent: VendingMachine
  id: VendingMachineCentDrobe
  name: "CentDrobe"
  description: "Єдиний у своєму роді торговий автомат для всіх ваших естетичних потреб!"
  components:
  - type: VendingMachine
    pack: CentDrobeInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/centdrobe.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#48CF48"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: AccessReader
    access: [["CentralCommand"]]

- type: entity
  parent: VendingMachine
  id: VendingMachineHappyHonk
  name: "Happy Honk Dispenser"
  description: "Щасливий диспенсер для коробочок з їжею honk! від honk! co."
  components:
  - type: VendingMachine
    pack: HappyHonkDispenserInventory
    dispenseOnHitChance: 0.25
    dispenseOnHitThreshold: 2
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    denyState: deny-unshaded
    ejectDelay: 1.9
    soundVend: /Audio/Items/bikehorn.ogg
    initialStockQuality: 1.0 # Nobody knows how Honk does it, but their vending machines always seem well-stocked...
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/happyhonk.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#3c5eb5"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: Advertise
    pack: HappyHonkAds
  - type: SpeakOnUIClosed
    pack: HappyHonkGoodbyes
  - type: AccessReader
    access: [["Kitchen"], ["Theatre"]]

# Gas Tank Dispenser

- type: entity
  parent: VendingMachine
  id: VendingMachineTankDispenserEVA
  suffix: EVA [O2, N2]
  name: "стійка з газовими балонами"
  description: "Постачальник газових балонів для роздачі."
  components:
  - type: VendingMachine
    pack: TankDispenserEVAInventory
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/tankdispenser.rsi #TODO add visualiser for remaining tanks as layers
    state: dispenser

- type: entity
  parent: VendingMachine
  id: VendingMachineTankDispenserEngineering
  suffix: ENG [O2, Plasma]
  name: "стійка з газовими балонами"
  description: "Постачальник газових балонів. Цей має ліврею інженера."
  components:
  - type: VendingMachine
    pack: TankDispenserEngineeringInventory
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/tankdispenser.rsi #TODO add visualiser for remaining tanks as layers
    layers:
    - state: dispenser
    - state: dispensereng

- type: entity
  parent: VendingMachine
  id: VendingMachineChemicals
  name: "ChemVend"
  description: "Мабуть, це не кавоварка."
  components:
  - type: VendingMachine
    pack: ChemVendInventory
    offState: off
    brokenState: broken
    normalState: normal
    denyState: deny
    ejectDelay: 2
  - type: Sprite
    sprite: Structures/Machines/VendingMachines/chemvend.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: AccessReader
    access: [["Chemistry"]]
  - type: GuideHelp
    guides:
    - Chemicals

- type: entity
  parent: VendingMachineChemicals
  id: VendingMachineChemicalsSyndicate
  name: "SyndieJuice"
  description: "Сподіваюся, не зі свіжовичавлених синдиків."
  components:
  - type: VendingMachine
    pack: ChemVendInventorySyndicate
    offState: off
    brokenState: broken
    normalState: normal
    denyState: deny
    ejectDelay: 2
  - type: AccessReader
    access: [["SyndicateAgent"]]

- type: entity
  id: VendingMachineFitness
  parent: VendingMachine
  name: "SweatMAX"
  description: "Продавець тренажерів і харчових добавок, який наживається на вашій неадекватності."
  components:
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/VendingMachines/fitness.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: "screen"
      map: ["enum.VendingMachineVisualLayers.Screen"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: VendingMachine
    pack: FitnessVendInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    denyState: deny-unshaded
    ejectState: eject-unshaded
    screenState: screen
  - type: Advertise
    pack: FitnessVendAds
  - type: SpeakOnUIClosed
    pack: FitnessVendGoodbyes
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#9dc5c9"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true

- type: entity
  id: VendingMachineHotfood
  parent: VendingMachine
  name: "гарячі страви"
  description: "Старий торговий автомат, що обіцяє \"гарячу їжу\". Ви сумніваєтеся, що його вміст ще їстівний."
  components:
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/VendingMachines/hotfood.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
    - state: heater
  - type: VendingMachine
    pack: HotfoodInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    denyState: deny-unshaded
    ejectState: eject-unshaded
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true
  - type: Advertise
    pack: HotfoodAds
  - type: SpeakOnUIClosed
    pack: HotfoodGoodbyes
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#9dc5c9"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: LanguageKnowledge
    speaks:
    - SolCommon
    understands:
    - SolCommon

- type: entity
  id: VendingMachineSolsnack
  parent: VendingMachine
  name: "Марс Март"
  description: "Торговий автомат SolCentric видає ласощі з дому."
  components:
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/VendingMachines/solsnack.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: VendingMachine
    pack: SolsnackInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    ejectState: eject-unshaded
    denyState: deny-unshaded
  - type: Advertise
    pack: SolsnackAds
  - type: SpeakOnUIClosed
    pack: SolsnackGoodbyes
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#9dc5c9"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: LanguageKnowledge
    speaks:
    - SolCommon
    understands:
    - SolCommon

- type: entity
  id: VendingMachineWeeb
  parent: VendingMachine
  name: "Ніппон-тан"
  description: "Сумнозвісний етнічний торговий автомат, наповнений низькокалорійними снеками з високим вмістом сахарози, за браком кращого слова, через брак слів."
  components:
  - type: Sprite
    noRot: true
    sprite: Structures/Machines/VendingMachines/weeb.rsi
    layers:
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.Base"]
    - state: "off"
      map: ["enum.VendingMachineVisualLayers.BaseUnshaded"]
      shader: unshaded
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
    - state: fan
  - type: VendingMachine
    pack: WeebVendInventory
    offState: off
    brokenState: broken
    normalState: normal-unshaded
    denyState: deny-unshaded
    ejectState: eject-unshaded
  - type: Advertise
    pack: WeebVendAds
  - type: SpeakOnUIClosed
    pack: WeebVendGoodbyes
  - type: Transform
    noRot: false
  - type: Rotatable
    rotateWhileAnchored: false
    rotateWhilePulling: true
  - type: PointLight
    radius: 2
    energy: 1.6
    color: "#9dc5c9"
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
  - type: LanguageKnowledge
    speaks:
    - SolCommon
    understands:
    - SolCommon
