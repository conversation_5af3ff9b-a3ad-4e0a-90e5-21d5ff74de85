- type: latheRecipe
  id: PrizeBall
  result: PrizeBall
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 30

- type: latheRecipe
  id: PlushieShadowkin
  result: PlushieShadowkin
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieMothRandom
  result: PlushieMothRandom
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieMothMusician
  result: PlushieMothMusician
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieMothBartender
  result: PlushieMothBartender
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieBee
  result: PlushieBee
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieHampter
  result: PlushieHampter
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieRouny
  result: PlushieRouny
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieLamp
  result: PlushieLamp
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieArachind
  result: PlushieArachind
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieLizard
  result: PlushieLizard
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieSpaceLizard
  result: PlushieSpaceLizard
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieSharkBlue
  result: PlushieSharkBlue
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieSharkPink
  result: PlushieSharkPink
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieSharkGrey
  result: PlushieSharkGrey
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieCarp
  result: PlushieCarp
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieMagicarp
  result: PlushieMagicarp
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieHolocarp
  result: PlushieHolocarp
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieSlime
  result: PlushieSlime
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieSnake
  result: PlushieSnake
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: ToyMouse
  result: ToyMouse
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: ToyRubberDuck
  result: ToyRubberDuck
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieVox
  result: PlushieVox
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieAtmosian
  result: PlushieAtmosian
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushiePenguin
  result: PlushiePenguin
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieHuman
  result: PlushieHuman
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieArachne
  result: PlushieArachne
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieGnome
  result: PlushieGnome
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieLoveable
  result: PlushieLoveable
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieDeer
  result: PlushieDeer
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieIpc
  result: PlushieIpc
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieGrey
  result: PlushieGrey
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieRedFox
  result: PlushieRedFox
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushiePurpleFox
  result: PlushiePurpleFox
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushiePinkFox
  result: PlushiePinkFox
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieOrangeFox
  result: PlushieOrangeFox
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieMarbleFox
  result: PlushieMarbleFox
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieCrimsonFox
  result: PlushieCrimsonFox
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieCoffeeFox
  result: PlushieCoffeeFox
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieBlueFox
  result: PlushieBlueFox
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieBlackFox
  result: PlushieBlackFox
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieVulp
  result: PlushieVulp
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieCorgi
  result: PlushieCorgi
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieGirlyCorgi
  result: PlushieGirlyCorgi
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieRobotCorgi
  result: PlushieRobotCorgi
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieCatBlack
  result: PlushieCatBlack
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieCatGrey
  result: PlushieCatGrey
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieCatOrange
  result: PlushieCatOrange
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieCatSiames
  result: PlushieCatSiames
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieCatTabby
  result: PlushieCatTabby
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieCatTuxedo
  result: PlushieCatTuxedo
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieCatWhite
  result: PlushieCatWhite
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: ToyAi
  result: ToyAi
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 30

- type: latheRecipe
  id: ToyIan
  result: ToyIan
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 30

- type: latheRecipe
  id: BalloonNT
  result: BalloonNT
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 30

- type: latheRecipe
  id: BalloonCorgi
  result: BalloonCorgi
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 30

- type: latheRecipe
  id: CrayonBox
  result: CrayonBox
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 20

- type: latheRecipe
  id: PetRockCarrier
  result: PetRockCarrier
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieXeno
  result: PlushieXeno
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 80

- type: latheRecipe
  id: FoamCrossbow
  result: FoamCrossbow
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 100

- type: latheRecipe
  id: RevolverCapGun
  result: RevolverCapGun
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 100

- type: latheRecipe
  id: PonderingOrb
  result: PonderingOrb
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 80

- type: latheRecipe
  id: ToyAmongPequeno
  result: ToyAmongPequeno
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 80

- type: latheRecipe
  id: FoamCutlass
  result: FoamCutlass
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 100

- type: latheRecipe
  id: ToyHammer
  result: ToyHammer
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 100

- type: latheRecipe
  id: WhoopieCushion
  result: WhoopieCushion
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 100

- type: latheRecipe
  id: PlasticBanana
  result: PlasticBanana
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 80

- type: latheRecipe
  id: WeaponWaterPistol
  result: WeaponWaterPistol
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 100

- type: latheRecipe
  id: WeaponWaterBlaster
  result: WeaponWaterBlaster
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 100

- type: latheRecipe
  id: NewtonCradle
  result: NewtonCradle
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 80

- type: latheRecipe
  id: SnapPopBox
  result: SnapPopBox
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 80

- type: latheRecipe
  id: MrDips
  result: MrDips
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 80

- type: latheRecipe
  id: MrChips
  result: MrChips
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 80

- type: latheRecipe
  id: CrazyGlue
  result: CrazyGlue
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 100

- type: latheRecipe
  id: PlushieRatvar
  result: PlushieRatvar
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 80

- type: latheRecipe
  id: PlushieNar
  result: PlushieNar
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 80

# EMAG

- type: latheRecipe
  id: PlushieGhost
  result: PlushieGhost
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 100

- type: latheRecipe
  id: PlushieRGBee
  result: PlushieRGBee
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 100

- type: latheRecipe
  id: PlushieRainbowCarp
  result: PlushieRainbowCarp
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 100

- type: latheRecipe
  id: PlushieJester
  result: PlushieJester
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 100

- type: latheRecipe
  id: PlushieSlips
  result: PlushieSlips
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 100

- type: latheRecipe
  id: PlushieTrystan
  result: PlushieTrystan
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 100

- type: latheRecipe
  id: PlushieAbductor
  result: PlushieAbductor
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 100

- type: latheRecipe
  id: PlushieAbductorAgent
  result: PlushieAbductorAgent
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 100

- type: latheRecipe
  id: PlushieNuke
  result: PlushieNuke
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 100

- type: latheRecipe
  id: ToyNuke
  result: ToyNuke
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 100

- type: latheRecipe
  id: FoamBlade
  result: FoamBlade
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 150

- type: latheRecipe
  id: BalloonSyn
  result: BalloonSyn
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 100

- type: latheRecipe
  id: SingularityToy
  result: SingularityToy
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 150

- type: latheRecipe
  id: TeslaToy
  result: TeslaToy
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 150

- type: latheRecipe
  id: ToySword
  result: ToySword
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 200

- type: latheRecipe
  id: BwoinkHammer
  result: BwoinkHammer
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 200

- type: latheRecipe
  id: ThronglerToy
  result: ThronglerToy
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 500


## PIRATE
- type: latheRecipe
  id: NFPlushieBotanist
  result: NFPlushieBotanist
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: NFPlushieCmo
  result: NFPlushieCmo
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: NFPlushieConstruction
  result: NFPlushieConstruction
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: NFPlushieEngineer
  result: NFPlushieEngineer
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieGray
  result: PlushieGray
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieUrsaMinor
  result: PlushieUrsaMinor
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieBug
  result: PlushieBug
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50


- type: latheRecipe
  id: PlushieEvilBug
  result: PlushieEvilBug
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieSnail
  result: PlushieSnail
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieGoblin
  result: PlushieGoblin
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieGollylad
  result: PlushieGollylad
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieImp
  result: PlushieImp
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieCaptain
  result: PlushieCaptain
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50

- type: latheRecipe
  id: PlushieGiantBear
  result: PlushieGiantBear
  applyMaterialDiscount: false
  completetime: 0.1
  materials:
    PrizeTicket: 50
