- type: entity
  name: "BaseWeaponSniper"
  parent: BaseItem
  id: BaseWeaponSniper
  description: "Корінцевий зубастий і пагонистий."
  abstract: true
  components:
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
  - type: Item
    size: Huge
  - type: Clothing
    sprite: Objects/Weapons/Guns/Snipers/bolt_gun_wood.rsi
    quickEquip: false
    slots:
    - Back
    - suitStorage
  - type: AmmoCounter
  - type: Wieldable
  - type: GunRequiresWield
  - type: Gun
    fireRate: 0.75
    selectedMode: SemiAuto
    availableModes:
    - SemiAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/sniper.ogg
  - type: BallisticAmmoProvider
    capacity: 10
    proto: CartridgeLightRifle
    whitelist:
      tags:
      - CartridgeLightRifle
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
        ents: []
  - type: StaticPrice
    price: 500
  - type: Execution
  - type: MeleeWeapon
    attackRate: 1.3333
    damage:
      types:
        Blunt: 8.0
    bluntStaminaDamageFactor: 1.25
    swapKeys: true
    disableHeavy: true
    wideAnimationRotation: 135
    animation: WeaponArcThrust
    soundHit:
      collection: MetalThud
  - type: IncreaseDamageOnWield
    damage:
      types:
        Blunt: 2.5
  - type: DamageOtherOnHit
    staminaCost: 7.5
  # PIRATE START
  - type: CanHoldAccessories
    whiteListTags:
      - CombatKnife
  - type: CanTakeAim
  # PIRATE END

- type: entity
  name: "Кардашев-Мосін"
  parent: [BaseWeaponSniper, BaseGunWieldable]
  id: WeaponSniperMosin
  description: "Зброя для полювання або нескінченної окопної війни, з багнетом, прикріпленим до ствола. Використовує гвинтівкові набої калібру .30."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Snipers/bolt_gun_wood.rsi
  - type: Gun
    fireRate: 0.75 
    damageModifier: 1.5 
    selectedMode: SemiAuto
    availableModes:
    - SemiAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/sniper.ogg
    fireOnDropChance: 1
  - type: MeleeWeapon
    range: 1.75
    damage:
      types:
        Piercing: 7
        Slash: 3.5
    wideAnimationRotation: -135
    soundHit:
      path: /Audio/Weapons/bladeslice.ogg
  - type: IncreaseDamageOnWield
    damage:
      types:
        Piercing: 4
        Slash: 2
  - type: DamageOtherOnHit
    damage:
      types:
        Piercing: 8
        Slash: 3
  - type: EmbeddableProjectile
    removalTime: 3.5
  - type: EmbedPassiveDamage
  - type: ThrowingAngle
    angle: 225
  - type: BallisticAmmoProvider
    capacity: 5
    autoCycle: false
    proto: CartridgeLightRifle
    whitelist:
      tags:
      - CartridgeLightRifle

- type: entity
  name: "Кардашев-Мосін"
  parent: WeaponSniperMosin
  id: WeaponSniperMosinRubber
  description: "Зброя для полювання або нескінченної траншейної війни. Використовує гумові кулі калібру .30."
  suffix: Non-Lethal
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifleRubber

- type: entity
  name: "Кардашев-Мосін"
  parent: WeaponSniperMosin
  id: WeaponSniperMosinEmpty
  description: "Зброя для полювання або нескінченної окопної війни. Використовує гвинтівкові патрони калібру .30."
  suffix: Empty
  components:
  - type: BallisticAmmoProvider
    proto: null
  - type: ExtendDescription
    descriptionList:
      - description: "gun-legality-salvage"
        fontSize: 12
        color: "#ff4f00"
        requireDetailRange: false

- type: entity
  name: "Христов"
  parent: [BaseWeaponSniper, BaseGunWieldable]
  id: WeaponSniperHristov
  description: "Портативна протитанкова гвинтівка. Стріляє бронебійними 14,5 мм снарядами. Використовує боєприпаси калібру .60."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Snipers/heavy_sniper.rsi
  - type: Clothing
    sprite: Objects/Weapons/Guns/Snipers/heavy_sniper.rsi
  - type: BallisticAmmoProvider
    whitelist:
      tags:
      - CartridgeAntiMateriel
    capacity: 5
    proto: CartridgeAntiMateriel
  - type: Telescope
  - type: GunRequiresWield # DeltaV - Firing an antimateriel rifle is.. incredibly unweildy with one hand.
  - type: Gun # DeltaV
    fireRate: 0.5

- type: entity
  name: "мушкет"
  parent: BaseWeaponSniper
  id: Musket
  description: "Це мало бути в музеї задовго до твого народження. Використовує мушкетні гільзи." # Delta V - swapping to our crafted musket ammo
  components:
  - type: Sharp
  - type: Item
    size: Large
  - type: Sprite
    sprite: Objects/Weapons/Guns/Snipers/musket.rsi
    state: base
  - type: Clothing
    sprite: Objects/Weapons/Guns/Snipers/musket.rsi
  - type: Gun
    selectedMode: SemiAuto
    availableModes:
    - SemiAuto
    fireOnDropChance: 1
  - type: UseDelayOnShoot
  - type: UseDelay
    delay: 8 #it's a musket
  - type: BallisticAmmoProvider
    whitelist:
      tags:
      - CartridgeMusket # DeltaV - musket instead of anti materiel ammo
    capacity: 1
    proto: CartridgeMusket # DeltaV
  - type: MeleeWeapon
    range: 1.75
    damage:
      types:
        Piercing: 5
        Slash: 3.5
    wideAnimationRotation: -135
    soundHit:
      path: /Audio/Weapons/bladeslice.ogg
  - type: IncreaseDamageOnWield
    damage:
      types:
        Piercing: 4
        Slash: 2
  - type: DamageOtherOnHit
    damage:
      types:
        Piercing: 8
        Slash: 3
  - type: EmbeddableProjectile
    removalTime: 3.5
  - type: EmbedPassiveDamage
  - type: ThrowingAngle
    angle: 225

- type: entity
  name: "кремінний пістолет"
  parent: BaseWeaponSniper
  id: WeaponPistolFlintlock
  description: "Супутниця пірата. Яррр! Використовує мушкетні патрони." # Delta V - Ditto the above
  components:
  - type: Gun
    minAngle: 0
    maxAngle: 30 #miss him entirely because the barrel is smoothbore
    fireOnDropChance: 1
  - type: Item
    size: Small
    storedRotation: 90
  - type: Sprite
    sprite: Objects/Weapons/Guns/Snipers/flintlock.rsi
  - type: Clothing
    sprite: Objects/Weapons/Guns/Snipers/flintlock.rsi
  - type: UseDelayOnShoot
  - type: UseDelay
    delay: 8 #it's a flintlock
  - type: BallisticAmmoProvider
    whitelist:
      tags:
      - CartridgeMusket # DeltaV - musket instead of anti materiel ammo
    capacity: 1
    proto: CartridgeMusket # DeltaV
  - type: StaticPrice
    price: 0
  - type: MeleeWeapon
    attackRate: 1.2
    damage:
      types:
        Blunt: 7.5
    bluntStaminaDamageFactor: 1.0
    wideAnimationRotation: 135
  - type: DamageOtherOnHit
    staminaCost: 5
