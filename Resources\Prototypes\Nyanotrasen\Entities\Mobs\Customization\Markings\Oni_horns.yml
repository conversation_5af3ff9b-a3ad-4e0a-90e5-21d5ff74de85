- type: marking
  id: OniHornSingleCurved
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Nyanotrasen/Mobs/Customization/oni_horns.rsi
    state: single_curved

- type: marking
  id: OniHornSingleLeftCurved
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Nyanotrasen/Mobs/Customization/oni_horns.rsi
    state: single_left_curved

- type: marking
  id: OniHornSingleRightCurved
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Nyanotrasen/Mobs/Customization/oni_horns.rsi
    state: single_right_curved

- type: marking
  id: OniHornDoubleCurvedOutwards
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Nyanotrasen/Mobs/Customization/oni_horns.rsi
    state: double_curved_outwards

- type: marking
  id: OniHornDoubleCurved
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Nyanotrasen/Mobs/Customization/oni_horns.rsi
    state: double_curved

- type: marking
  id: OniHornDoubleLeftBrokeCurved
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Nyanotrasen/Mobs/Customization/oni_horns.rsi
    state: double_left_broke_curved

- type: marking
  id: OniHornDoubleRightBrokeCurved
  bodyPart: HeadTop
  markingCategory: HeadTop
  forcedColoring: false
  speciesRestriction: [Oni]
  sprites:
  - sprite: Nyanotrasen/Mobs/Customization/oni_horns.rsi
    state: double_right_broke_curved
