- type: entity
  id: LockerBooze
  parent: LockerBase
  name: "зберігання випивки"
  description: "Тут бармен зберігає випивку."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: cabinet
    stateDoorOpen: cabinet_open
    stateDoorClosed: cabinet_door

  - type: AccessReader
    access: [["Bar"]]
  - type: EntityStorage
    closeSound:
      path: /Audio/Effects/woodenclosetclose.ogg
    openSound:
      path: /Audio/Effects/woodenclosetopen.ogg

# Bartender
- type: entity
  id: LockerBartender
  parent: LockerBase
  name: "шафка бармена"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: bartender
    stateDoorOpen: bartender_open
    stateDoorClosed: bartender_door
  - type: AccessReader
    access: [ [ "Bar" ] ]

# Basic
- type: entity
  id: LockerSteel
  parent: LockerBaseSecure
  name: "шафа із замком"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: secure
    stateDoorOpen: secure_open
    stateDoorClosed: secure_door
  - type: AccessReader
    access: [ [ "Engineering" ], [ "Command" ], ["Security"] ]
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 15
    staminaCost: 50

# Cargo
- type: entity
  id: LockerQuarterMaster
  parent: LockerBaseSecure
  name: "шафка голови карго" # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: qm
    stateDoorOpen: qm_open
    stateDoorClosed: qm_door
  - type: AccessReader
    access: [["Quartermaster"]]

- type: entity
  id: LockerSalvageSpecialist
  parent: LockerBase
  name: "екіпірування фахівця утилізації"
  description: "Забудьте про кирку."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: mining
    stateDoorOpen: mining_open
    stateDoorClosed: mining_door
  - type: AccessReader
    access: [["Salvage"]]

# Command
- type: entity
  id: LockerCaptain
  parent: LockerBaseSecure
  name: "шафка капітана"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: cap
    stateDoorOpen: cap_open
    stateDoorClosed: cap_door
  - type: AccessReader
    access: [["Captain"]]

- type: entity
  id: LockerHeadOfPersonnel
  parent: LockerBaseSecure
  name: "шафка голови персоналу"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: hop
    stateDoorOpen: hop_open
    stateDoorClosed: hop_door
  - type: AccessReader
    access: [["HeadOfPersonnel"]]

# CE
- type: entity
  id: LockerChiefEngineer
  parent: LockerBaseSecure
  name: "шафка головного інженера"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: ce
    stateDoorOpen: ce_open
    stateDoorClosed: ce_door
  - type: AccessReader
    access: [ [ "ChiefEngineer" ] ]

# Electrical supplies
- type: entity
  id: LockerElectricalSupplies
  parent: LockerBase
  name: "шафа для електроприладів"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: eng
    stateDoorOpen: eng_open
    stateDoorClosed: eng_elec_door
  - type: AccessReader
    access: [ [ "Engineering" ] ]

# Welding supplies
- type: entity
  id: LockerWeldingSupplies
  parent: LockerBase
  name: "шафа для зварювальних матеріалів"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: eng
    stateDoorOpen: eng_open
    stateDoorClosed: eng_weld_door
  - type: AccessReader
    access: [ [ "Engineering" ] ]

# Atmos tech
- type: entity
  id: LockerAtmospherics
  parent: LockerBase
  name: "шафка атмосферного техніка"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: atmos
    stateDoorOpen: atmos_open
    stateDoorClosed: atmos_door
  - type: AccessReader
    access: [ [ "Atmospherics" ] ]

# Engineer
- type: entity
  id: LockerEngineer
  parent: LockerBase
  name: "шафка інженера"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: eng_secure
    stateDoorOpen: eng_secure_open
    stateDoorClosed: eng_secure_door
  - type: AccessReader
    access: [ [ "Engineering" ] ]

# Freezer
- type: entity
  id: LockerFreezerBase
  parent: LockerBase
  name: "морозильна камера"
  suffix: No Access
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: freezer
    stateDoorOpen: freezer_open
    stateDoorClosed: freezer_door
  - type: ExplosionResistance
    damageCoefficient: 0.50
  - type: AntiRottingContainer

- type: entity
  id: LockerFreezer
  parent: LockerFreezerBase
  name: "морозильна камера"
  suffix: Kitchen, Locked
  components:
  - type: AccessReader
    access: [ [ "Kitchen" ] ]

# Botanist
- type: entity
  id: LockerBotanist
  parent: LockerBase
  name: "шафка ботаніка"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: hydro
    stateDoorOpen: hydro_open
    stateDoorClosed: hydro_door
  - type: AccessReader
    access: [ [ "Hydroponics" ] ]

# Janitor
- type: entity
  id: LockerJanitor
  parent: LockerBase
  name: "шафа прибиральника"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: jan
    stateDoorOpen: jan_open
    stateDoorClosed: jan_door
  - type: AccessReader
    access: [ [ "Janitor" ] ]

# Medicine
- type: entity
  id: LockerMedicine
  parent: LockerBase
  name: "шафка з ліками"
  description: "Вщерть заповнений медичним мотлохом."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: med
    stateDoorOpen: med_open
    stateDoorClosed: med_door
  - type: AccessReader
    access: [ [ "Medical" ] ]

# Medical doctor
- type: entity
  id: LockerMedical
  parent: LockerBase
  name: "шафка лікаря"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: med_secure
    stateDoorOpen: med_secure_open
    stateDoorClosed: med_secure_door
  - type: AccessReader
    access: [ [ "Medical" ] ]

# Paramedic
- type: entity
  id: LockerParamedic
  parent: LockerBase
  name: "шафка парамедика"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: paramed
    stateDoorOpen: paramed_open
    stateDoorClosed: paramed_door
  - type: AccessReader
    access: [ [ "Paramedic" ] ] # DeltaV - Change Medical access to Paramedic


# Chemical
- type: entity
  id: LockerChemistry
  parent: LockerBase
  name: "шафа хімика"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: med
    stateDoorOpen: med_open
    stateDoorClosed: chemical_door
  - type: AccessReader
    access: [ [ "Chemistry" ] ]

# CMO
- type: entity
  id: LockerChiefMedicalOfficer
  parent: LockerBaseSecure
  name: "шафка головного лікаря"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: cmo
    stateDoorOpen: cmo_open
    stateDoorClosed: cmo_door
  - type: AccessReader
    access: [ [ "ChiefMedicalOfficer" ] ]

# Science
- type: entity
  id: LockerResearchDirector
  parent: LockerBase
  name: "шафка директора НВ" # DeltaV - Epistemics Department replacing Science
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: rd
    stateDoorOpen: rd_open
    stateDoorClosed: rd_door
  - type: AccessReader
    access: [ [ "ResearchDirector" ] ]

- type: entity
  id: LockerScientist
  parent: LockerBase
  name: "шафка вченого"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: science
    stateDoorOpen: science_open
    stateDoorClosed: science_door
  - type: AccessReader
    access: [ [ "Research" ] ]

# HoS
- type: entity
  id: LockerHeadOfSecurity
  parent: LockerBaseSecure
  name: "шафка начальника служби безпеки"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: hos
    stateDoorOpen: hos_open
    stateDoorClosed: hos_door
  - type: AccessReader
    access: [["HeadOfSecurity"]]

# Warden
- type: entity
  id: LockerWarden
  parent: LockerBaseSecure
  name: "шафка наглядача"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: warden
    stateDoorOpen: warden_open
    stateDoorClosed: warden_door
  - type: AccessReader
    access: [["Armory"]]

# Brigmedic
- type: entity
  id: LockerBrigmedic
  parent: LockerBaseSecure
  name: "роздягальня бріг-медика" # DeltaV - rename brigmedic to corpsman
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: brigmedic
    stateDoorOpen: armory_open
    stateDoorClosed: brigmedic_door
  - type: AccessReader
    access: [["Corpsman"]] # DeltaV - add corpsman access

# Security Officer
- type: entity
  id: LockerSecurity
  parent: LockerBaseSecure
  name: "шафка офіцера"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: sec
    stateDoorOpen: sec_open
    stateDoorClosed: sec_door
  - type: AccessReader
    access: [["Security"]]

- type: entity
  id: GunSafe
  parent: LockerBaseSecure
  name: "сейф для зброї"
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: shotguncase
    stateDoorOpen: shotguncase_open
    stateDoorClosed: shotguncase_door
  - type: AccessReader
    access: [["Armory"]]

# Detective
- type: entity
  id: LockerDetective
  parent: LockerBooze
  name: "кабінет детектива"
  description: "Зазвичай холодна і порожня... як твоє серце."
  components:
  - type: AccessReader
    access: [["Detective"]]

- type: entity
  id: LockerEvidence
  parent: LockerSecurity
  name: "сховище доказів"
  description: "Для зберігання мішків з гільзами та речей затриманих."
  components:
  - type: AccessReader
    access: [["Security"]]

# Syndicate
- type: entity
  id: LockerSyndicatePersonal
  name: "збройова шафа"
  parent: LockerBaseSecure
  description: "Це персональне сховище для оперативного спорядження."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: syndicate
    stateDoorOpen: syndicate_open
    stateDoorClosed: syndicate_door

- type: entity
  id: LockerSyndicateAgentLocker
  name: "шафа агента синдикату"
  parent: LockerBase
  description: "Це персональне сховище для агентурного спорядження."
  components:
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: syndicate1
    stateDoorOpen: syndicate1_open
    stateDoorClosed: syndicate1_door
  - type: AccessReader
    access: [ [ "SyndicateAgent" ] ]

# Bluespace
- type: entity
  id: LockerBluespaceStation
  name: "шафка блюспейс"
  suffix: once to station
  parent: LockerSyndicatePersonal
  description: "Передова технологія шафки."
  components:
    - type: BluespaceLocker
      minBluespaceLinks: 1
      behaviorProperties:
        delay: 1
        actOnOpen: false
        bluespaceEffectOnInit: true
        bluespaceEffectOnTeleportSource: true
        bluespaceEffectOnTeleportTarget: true
        destroyAfterUses: 1
        destroyAfterUsesMinItemsToCountUse: 1
        destroyType: Delete
      autoLinksUseProperties: true
      autoLinkProperties:
        actOnOpen: false
        actOnClose: false
        destroyAfterUses: 2
        destroyType: DeleteComponent

# Clown
- type: entity
  id: LockerClown
  name: "шафка клоуна"
  parent: LockerBaseSecure
  components:
  - type: Appearance
  - type: AccessReader
    access: [["Theatre"]]
  - type: EntityStorageVisuals
    stateBaseClosed: clown
    stateDoorOpen: clown_open
    stateDoorClosed: clown_door

# Mime
- type: entity
  id: LockerMime
  name: "шафка міма"
  parent: LockerBaseSecure
  components:
  - type: Appearance
  - type: AccessReader
    access: [["Theatre"]]
  - type: EntityStorageVisuals
    stateBaseClosed: mime
    stateDoorOpen: mime_open
    stateDoorClosed: mime_door

# Representative
- type: entity
  id: LockerRepresentative
  name: "шафка представника"
  parent: LockerBaseSecure
  components:
  - type: Appearance
  - type: AccessReader
    access: [["Command"]]
  - type: EntityStorageVisuals
    stateBaseClosed: hop
    stateDoorOpen: hop_open
    stateDoorClosed: representative_door
#PIRATE START UNTIL END OF FILE
- type: entity
  id: LockerRobotics
  parent: LockerBase
  name: "шафка робототехніка"
  components:
  - type: Sprite
    sprite: _Pirate/Structures/Storage/closet.rsi
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: robotics
    stateDoorOpen: robotics_open
    stateDoorClosed: robotics_door
  - type: AccessReader
    access: [ [ "Robotics" ] ]
