- type: entity
  id: UraniumWindow
  name: "уранове вікно"
  parent: WindowRCDResistant
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Windows/uranium_window.rsi
  - type: Icon
    sprite: Structures/Windows/uranium_window.rsi
    state: full
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: RGlass
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 60
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassUranium:
            min: 1
            max: 2
      - !type:DoA<PERSON>Behavior
        acts: [ "Destruction" ]
  - type: IconSmooth
    base: uwindow
  - type: Construction
    graph: Window
    node: uraniumWindow
  - type: Appearance
  - type: DamageVisuals
    thresholds: [4, 8, 12]
    damageDivisor: 3.333
    trackAllDamage: true
    damageOverlay:
      sprite: Structures/Windows/cracks.rsi
  - type: StaticPrice
    price: 80
  - type: RadiationBlocker
    resistance: 5

- type: entity
  id: UraniumWindowDirectional
  parent: WindowDirectional
  name: "спрямоване уранове вікно"
  description: "Не заляпайте скло внизу."
  placement:
    mode: SnapgridCenter
    snap:
    - Window
  components:
  - type: Sprite
    sprite: Structures/Windows/directional.rsi
    state: uranium_window
  - type: Icon
    sprite: Structures/Windows/directional.rsi
    state: uranium_window
  - type: Construction
    graph: WindowDirectional
    node: uraniumWindowDirectional
  - type: Appearance
  - type: DamageVisuals
    thresholds: [4, 8, 12]
    damageDivisor: 3.333
    trackAllDamage: true
    damageOverlay:
      sprite: Structures/Windows/cracks_directional.rsi
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassUranium:
            min: 1
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: StaticPrice
    price: 40

- type: entity
  parent: UraniumWindow
  id: UraniumWindowDiagonal
  suffix: diagonal
  placement:
    mode: SnapgridCenter
    snap:
    - Window
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Windows/uranium_window_diagonal.rsi
    state: state0
  - type: IconSmooth
    mode: Diagonal
    key: windows
    base: state
  - type: Icon
    sprite: Structures/Windows/uranium_window_diagonal.rsi
    state: state0
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PolygonShape
            vertices:
            - "-0.5,-0.5"
            - "0.5,0.5"
            - "0.5,-0.5"
        mask:
        - FullTileMask
        layer:
        - GlassLayer
  - type: Airtight
    noAirWhenFullyAirBlocked: false
    airBlockedDirection:
    - South
    - East
  - type: DamageVisuals
    damageOverlay:
      sprite: Structures/Windows/cracks_diagonal.rsi
  - type: Construction
    graph: WindowDiagonal
    node: uraniumWindowDiagonal
