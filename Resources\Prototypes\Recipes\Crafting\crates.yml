- type: construction
  name: "ящик для худоби"
  id: CrateLivestock
  graph: CrateLivestock
  startNode: start
  targetNode: cratelivestock
  category: construction-category-storage
  description: "Дерев'яний ящик для утримання худоби."
  icon: { sprite: Structures/Storage/Crates/livestock.rsi, state: base }
  objectType: Structure

- type: construction
  name: "сталевий ящик"
  id: CrateGenericSteel
  graph: CrateGenericSteel
  startNode: start
  targetNode: crategenericsteel
  category: construction-category-storage
  description: "Металевий ящик для зберігання речей."
  icon: { sprite: Structures/Storage/Crates/generic.rsi, state: icon }
  objectType: Structure

- type: construction
  name: "безпечний ящик"
  id: CrateSecure
  graph: CrateSecure
  startNode: start
  targetNode: cratesecure
  category: construction-category-storage
  description: "Безпечний металевий ящик для зберігання речей. Не потребує спеціального доступу, можна налаштувати за допомогою Конфігуратора доступу."
  icon: { sprite: Structures/Storage/Crates/secure.rsi, state: icon }
  objectType: Structure

- type: construction
  name: "пластиковий ящик"
  id: CratePlastic
  graph: CratePlastic
  startNode: start
  targetNode: crateplastic
  category: construction-category-storage
  description: "Пластиковий ящик для зберігання речей."
  icon: { sprite: Structures/Storage/Crates/plastic.rsi, state: icon }
  objectType: Structure

- type: construction
  name: "картонна коробка"
  id: BigBox
  graph: BaseBigBox
  startNode: start
  targetNode: basebigbox
  category: construction-category-storage
  description: "Велика коробка для зберігання речей або ховання."
  icon: { sprite: Structures/Storage/closet.rsi, state: cardboard }
  objectType: Structure

- type: construction
  name: "картонна коробка"
  id: BoxCardboard
  graph: BoxCardboard
  startNode: start
  targetNode: boxcardboard
  category: construction-category-storage
  description: "Невеличка коробка для зберігання речей."
  icon: { sprite: Objects/Storage/boxes.rsi, state: box }
  objectType: Item

- type: construction
  name: "труна"
  id: CrateCoffin
  graph: CrateCoffin
  startNode: start
  targetNode: cratecoffin
  category: construction-category-storage
  description: "Труна для зберігання трупів."
  icon: { sprite: Structures/Storage/Crates/coffin.rsi, state: base }
  objectType: Structure
