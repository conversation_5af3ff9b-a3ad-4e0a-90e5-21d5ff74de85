<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>$(TargetFramework)</TargetFramework>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="YamlDotNet" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\RobustToolbox\Robust.Shared\Robust.Shared.csproj" />
    </ItemGroup>

  <Import Project="..\RobustToolbox\MSBuild\Robust.Properties.targets" />
</Project>
