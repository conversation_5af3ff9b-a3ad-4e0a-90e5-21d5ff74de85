- type: entity
  name: "спавнер випадкового трупу людини"
  id: SalvageHumanCorpseSpawner
  parent: MarkerBase
  components:
  - type: Sprite
    sprite: Markers/jobs.rsi
    layers:
      - state: green
  - type: ConditionalSpawner
    prototypes:
      - SalvageHumanCorpse

- type: entity
  name: "спавнер випадкового трупу сервісу"
  id: RandomServiceCorpseSpawner
  parent: SalvageHumanCorpseSpawner
  components:
  - type: Sprite
    layers:
      - state: botanist
      - state: green
  - type: ConditionalSpawner
    prototypes:
      - MobRandomServiceCorpse

- type: entity
  name: "спавнер випадкового трупу інженерів"
  id: RandomEngineerCorpseSpawner
  parent: SalvageHumanCorpseSpawner
  components:
  - type: Sprite
    layers:
      - state: engineer
      - state: green
  - type: ConditionalSpawner
    prototypes:
      - MobRandomEngineerCorpse

- type: entity
  name: "спавнер випадкового трупу логістики" # DeltaV - Logistics Department replacing Cargo
  id: RandomCargoCorpseSpawner
  parent: SalvageHumanCorpseSpawner
  components:
  - type: Sprite
    layers:
      - state: cargo_tech
      - state: green
  - type: ConditionalSpawner
    prototypes:
      - MobRandomCargoCorpse

- type: entity
  name: "спавнер випадкового трупу медиків"
  id: RandomMedicCorpseSpawner
  parent: SalvageHumanCorpseSpawner
  components:
  - type: Sprite
    layers:
      - state: doctor
      - state: green
  - type: ConditionalSpawner
    prototypes:
      - MobRandomMedicCorpse

- type: entity
  name: "спавнер випадкового трупу епістемикм" # DeltaV - Epistemics Department replacing Science
  id: RandomScienceCorpseSpawner
  parent: SalvageHumanCorpseSpawner
  components:
  - type: Sprite
    layers:
      - state: scientist
      - state: green
  - type: ConditionalSpawner
    prototypes:
      - MobRandomScienceCorpse

- type: entity
  name: "спавнер випадкового трупу служби безпеки"
  id: RandomSecurityCorpseSpawner
  parent: SalvageHumanCorpseSpawner
  categories: [ HideSpawnMenu ] # DeltaV - Prevent security corpses from being mapped in
  components:
  - type: Sprite
    layers:
      - state: security_officer
      - state: green
  - type: ConditionalSpawner
    prototypes:
      - MobRandomServiceCorpse # DeltaV - Prevent Security corpses from spawning and instead spawn Service corpses

- type: entity
  name: "спавнер випадкового трупу командування"
  id: RandomCommandCorpseSpawner
  parent: SalvageHumanCorpseSpawner
  components:
  - type: Sprite
    layers:
      - state: captain
      - state: green
  - type: ConditionalSpawner
    prototypes:
      - MobRandomCommandCorpse
