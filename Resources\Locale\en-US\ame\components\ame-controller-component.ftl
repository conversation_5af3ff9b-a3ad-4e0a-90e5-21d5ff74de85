ame-controller-component-fuel-slot-fail-whitelist = You can't put that in the controller...
ame-controller-component-fuel-slot-fail-locked = Power down the AME before manipulating its fuel.
ame-controller-component-fuel-slot-success-insert = You insert the jar into the fuel slot.

## UI

ame-window-title = Antimatter Control Unit
ame-window-engine-status-label = Engine Status:
ame-window-engine-injection-status-not-injecting-label = Not Injecting
ame-window-engine-injection-status-injecting-label = Injecting...
ame-window-toggle-injection-button = Toggle Injection
ame-window-fuel-status-label = Fuel Status:
ame-window-fuel-not-inserted-text = No fuel inserted
ame-window-injection-amount-label = Injection amount:
ame-window-refresh-parts-button = Refresh Parts
ame-window-core-count-label = Core count:
ame-window-power-currentsupply-label = Current power supply:
ame-window-power-targetsupply-label = Targeted power supply:
ame-window-eject-button = Eject
ame-window-increase-fuel-button = Increase
ame-window-decrease-fuel-button = Decrease
