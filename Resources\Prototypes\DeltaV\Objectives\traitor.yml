- type: entity # Logistics Officer steal objective.
  categories: [ HideSpawnMenu ]
  parent: BaseTraitorStealObjective
  id: LOLuckyBillStealObjective
  components:
  - type: NotJobRequirement
    job: Quartermaster
  - type: StealCondition
    stealGroup: SpaceCashLuckyBill
    verifyMapExistence: true
    # owner: job-name-qm

- type: entity # Head of Personnel steal objective.
  categories: [ HideSpawnMenu ]
  parent: BaseTraitorStealObjective
  id: HoPBookIanDossierStealObjective
  components:
  - type: NotJobRequirement
    job: HeadOfPersonnel
  - type: StealCondition
    stealGroup: BookIanDossier
    verifyMapExistence: true
    # owner: job-name-hop

- type: entity # Clerk steal objective.
  categories: [ HideSpawnMenu ]
  parent: BaseTraitorStealObjective
  id: ClerkNotaryStealObjective
  components:
  - type: NotJobRequirement
    job: Clerk
  - type: StealCondition
    stealGroup: RubberStampNotary
    verifyMapExistence: true
    owner: job-name-clerk
