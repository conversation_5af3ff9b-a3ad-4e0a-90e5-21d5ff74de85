- type: entity
  name: "пляшка"
  parent: BaseItem
  id: BaseChemistryEmptyBottle
  abstract: true
  description: "Маленька пляшечка."
  components:
  - type: Tag
    tags:
    - Bottle
    - Trash
  - type: PhysicalComposition
    materialComposition:
      Glass: 25
  - type: SpaceGarbage
  - type: Sprite
    sprite: Objects/Specific/Chemistry/bottle.rsi
    layers:
      - state: bottle-1
      - state: bottle-1-1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 6
    fillBaseName: bottle-1-
  - type: Drink
  - type: SolutionContainerManager
    solutions:
      drink: # This solution name and target volume is hard-coded in ChemMasterComponent
        maxVol: 30
  - type: MixableSolution
    solution: drink
  - type: RefillableSolution
    solution: drink
  - type: DrainableSolution
    solution: drink
  - type: ExaminableSolution
    solution: drink
  - type: DrawableSolution
    solution: drink
  - type: SolutionTransfer
    maxTransferAmount: 30
    canChangeTransferAmount: true
  - type: SolutionItemStatus
    solution: drink
  - type: UserInterface
    interfaces:
      enum.TransferAmountUiKey.Key:
        type: TransferAmountBoundUserInterface
  - type: Item
    size: Tiny
    sprite: Objects/Specific/Chemistry/beaker.rsi
  - type: Spillable
    solution: drink
  - type: MeleeWeapon
    soundNoDamage:
      path: "/Audio/Effects/Fluids/splat.ogg"
    damage:
      types:
        Blunt: 0
  - type: TrashOnSolutionEmpty
    solution: drink
  - type: StaticPrice
    price: 0
  - type: DamageOnLand
    damage:
      types:
        Blunt: 5
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 4
  - type: Damageable
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 5
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
          params:
            volume: -4
      - !type:SpillBehavior { }
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlass:
            min: 0
            max: 1
        transferForensics: true
      - !type:DoActsBehavior
        acts: [ "Destruction" ]

- type: entity
  parent: BaseChemistryEmptyBottle
  id: ChemistryEmptyBottle01

- type: entity
  parent: BaseChemistryEmptyBottle
  id: ChemistryEmptyBottle02
  components:
  - type: Sprite
    layers:
      - state: bottle-2
      - state: bottle-2-1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
  - type: SolutionContainerVisuals
    fillBaseName: bottle-2-

- type: entity
  parent: BaseChemistryEmptyBottle
  id: ChemistryEmptyBottle03
  components:
  - type: Sprite
    layers:
      - state: bottle-3
      - state: bottle-3-1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
  - type: SolutionContainerVisuals
    fillBaseName: bottle-3-

- type: entity
  parent: BaseChemistryEmptyBottle
  id: ChemistryEmptyBottle04
  components:
  - type: Sprite
    layers:
      - state: bottle-4
      - state: bottle-4-1
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: false
  - type: SolutionContainerVisuals
    fillBaseName: bottle-4-

- type: entity
  parent: BaseChemistryEmptyBottle
  id: BaseChemistryBottleFilled
  abstract: true
  components:
  - type: Tag
    tags:
    - Bottle

- type: entity
  id: EpinephrineChemistryBottle
  suffix: epinephrine
  parent: BaseChemistryBottleFilled
  components:
  - type: Label
    currentLabel: reagent-name-epinephrine
  - type: SolutionContainerManager
    solutions:
      drink: # This solution name and target volume is hard-coded in ChemMasterComponent
        maxVol: 30
        reagents:
        - ReagentId: Epinephrine
          Quantity: 30

- type: entity
  id: RobustHarvestChemistryBottle
  suffix: robust harvest
  parent: BaseChemistryBottleFilled
  components:
  - type: Label
    currentLabel: reagent-name-robust-harvest
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: RobustHarvest
          Quantity: 30

- type: entity
  id: EZNutrientChemistryBottle
  suffix: ez nutrient
  parent: BaseChemistryBottleFilled
  components:
  - type: Label
    currentLabel: reagent-name-e-z-nutrient
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: EZNutrient
          Quantity: 30

- type: entity
  id: Left4ZedChemistryBottle
  suffix: left-4-zed
  parent: BaseChemistryBottleFilled
  components:
  - type: Label
    currentLabel: reagent-name-left4-zed
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: Left4Zed
          Quantity: 30

- type: entity
  id: UnstableMutagenChemistryBottle
  suffix: unstable mutagen
  parent: BaseChemistryBottleFilled
  components:
  - type: Label
    currentLabel: reagent-name-unstable-mutagen
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: UnstableMutagen
          Quantity: 30

- type: entity
  id: PotassiumChemistryBottle
  suffix: potassium
  parent: BaseChemistryBottleFilled
  components:
  - type: Label
    currentLabel: reagent-name-potassium
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: Potassium
          Quantity: 30

- type: entity
  id: NitrogenChemistryBottle
  suffix: nitrogen
  parent: BaseChemistryBottleFilled
  components:
  - type: Label
    currentLabel: reagent-name-nitrogen
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: Nitrogen
          Quantity: 30

- type: entity
  id: PhosphorusChemistryBottle
  suffix: phosphorus
  parent: BaseChemistryBottleFilled
  components:
  - type: Label
    currentLabel: reagent-name-phosphorus
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: Phosphorus
          Quantity: 30

- type: entity
  id: HydrogenChemistryBottle
  suffix: hydrogen
  parent: BaseChemistryBottleFilled
  components:
  - type: Label
    currentLabel: reagent-name-hydrogen
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: Hydrogen
          Quantity: 30

- type: entity
  id: EthanolChemistryBottle
  suffix: ethanol
  parent: BaseChemistryBottleFilled
  components:
  - type: Label
    currentLabel: reagent-name-ethanol
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: Ethanol
          Quantity: 30

- type: entity
  id: NocturineChemistryBottle
  suffix: nocturine
  parent: BaseChemistryBottleFilled
  components:
  - type: Label
    currentLabel: reagent-name-nocturine
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: Nocturine
          Quantity: 30

- type: entity
  id: EphedrineChemistryBottle
  suffix: ephedrine
  parent: BaseChemistryBottleFilled
  components:
  - type: Label
    currentLabel: reagent-name-ephedrine
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: Ephedrine
          Quantity: 30

- type: entity
  id: OmnizineChemistryBottle
  suffix: omnizine
  parent: BaseChemistryBottleFilled
  components:
  - type: Label
    currentLabel: reagent-name-omnizine
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: Omnizine
          Quantity: 30

- type: entity
  parent: BaseChemistryBottleFilled
  id: CognizineChemistryBottle
  suffix: cognizine
  components:
  - type: Label
    currentLabel: reagent-name-cognizine
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: Cognizine
          Quantity: 30

- type: entity
  id: PaxChemistryBottle
  suffix: pax
  parent: BaseChemistryBottleFilled
  components:
  - type: Label
    currentLabel: reagent-name-pax
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
          - ReagentId: Pax
            Quantity: 30

- type: entity
  id: MuteToxinChemistryBottle
  suffix: mute toxin
  parent: BaseChemistryBottleFilled
  components:
  - type: Label
    currentLabel: reagent-name-mute-toxin
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
          - ReagentId: MuteToxin
            Quantity: 30

- type: entity
  id: LeadChemistryBottle
  suffix: lead
  parent: BaseChemistryBottleFilled
  components:
  - type: Label
    currentLabel: reagent-name-lead
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
          - ReagentId: Lead
            Quantity: 30

- type: entity
  id: ToxinChemistryBottle
  suffix: toxin
  parent: BaseChemistryBottleFilled
  components:
  - type: Label
    currentLabel: reagent-name-toxin
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
          - ReagentId: Toxin
            Quantity: 30
