- type: entity
  abstract: true
  parent: Clothing
  id: ClothingHeadBase
  components:
  - type: Clothing
    slots:
    - HEAD
  - type: Sprite
    state: icon
  - type: Item
    size: Small
    storedRotation: -90
  - type: Food
    requiresSpecialDigestion: true
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Fiber
          Quantity: 10
  - type: Tag
    tags:
      - ClothMade
      - WhitelistChameleon

- type: entity
  abstract: true
  parent: ClothingHeadBase
  id: ClothingHeadBaseButcherable
  components:
  - type: Clothing #PIRATE
    slots: #PIRATE
    - HEAD #PIRATE
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
  - type: Butcherable
    butcheringType: Knife
    spawned:
    - id: MaterialCloth1
      amount: 1
  - type: Tag
    tags:
      - ClothMade
      - WhitelistChameleon

- type: entity
  abstract: true
  parent: Clothing
  id: HatBase
  components:
  - type: Clothing
    slots:
    - HEAD
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
  - type: Sprite
    state: icon
  - type: Tag
    tags:
      - WhitelistChameleon

- type: entity
  abstract: true
  parent: ClothingHeadBase
  id: ClothingHeadLightBase
  name: "базовий шолом з підсвічуванням"
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    layers:
    - state: icon
    - state: icon-flash
      visible: false
      map: [ "light" ]
  - type: Clothing
    equippedPrefix: off
  - type: Item
    heldPrefix: off
    size: Normal
  - type: ToggleableLightVisuals
  - type: PointLight
    enabled: false
    radius: 3
    energy: 1
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
    netsync: false
  - type: Appearance
  - type: HandheldLight
    addPrefix: true
    blinkingBehaviourId: blinking
    radiatingBehaviourId: radiating
  - type: LightBehaviour
    behaviours:
      - !type:FadeBehaviour
        id: radiating
        interpolate: Linear
        maxDuration: 2.0
        startValue: 3.0
        endValue: 2.0
        isLooped: true
        reverseWhenFinished: true
      - !type:PulseBehaviour
        id: blinking
        interpolate: Nearest
        maxDuration: 1.0
        minValue: 0.1
        maxValue: 2.0
        isLooped: true
  - type: PowerCellSlot
    cellSlotId: cell_slot
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellMedium
  - type: ContainerContainer
    containers:
      cell_slot: !type:ContainerSlot {}

- type: entity
  abstract: true
  parent: ClothingHeadBase
  id: ClothingHeadEVAHelmetBase
  name: "базовий космічний шолом"
  components:
  - type: Item
    size: Normal
  - type: PressureProtection
    highPressureMultiplier: 0.6
    lowPressureMultiplier: 1000
  - type: TemperatureProtection
    coefficient: 0.2
  - type: IgniteFromGasImmunity
    parts:
    - Head
  - type: IngestionBlocker
  - type: Clothing
    #Copies ClothingHeadHardsuitBase behavior
    equipSound: /Audio/Mecha/mechmove03.ogg
    unequipSound: /Audio/Mecha/mechmove03.ogg
  - type: Tag
    tags:
    - WhitelistChameleon
    - HelmetEVA
    - PlasmamanSafe
  - type: IdentityBlocker
  - type: HideLayerClothing
    slots:
    - Hair
    - Snout

- type: entity
  abstract: true
  parent: ClothingHeadBase
  id: ClothingHeadEVAHelmetNoIngestionBlocker
  name: "базовий космічний шолом"
  components:
  - type: Item
    size: Normal
  - type: PressureProtection
    highPressureMultiplier: 0.6
    lowPressureMultiplier: 1000
  - type: TemperatureProtection
    coefficient: 0.2
  - type: IgniteFromGasImmunity
    parts:
    - Head
  - type: Clothing
    #Copies ClothingHeadHardsuitBase behavior
    equipSound: /Audio/Mecha/mechmove03.ogg
    unequipSound: /Audio/Mecha/mechmove03.ogg
  - type: Tag
    tags:
    - WhitelistChameleon
    - HelmetEVA
    - PlasmamanSafe
  - type: IdentityBlocker
  - type: HideLayerClothing
    slots:
    - Hair
    - Snout

- type: entity
  abstract: true
  # No parent since we aren't actually an item.
  id: ClothingHeadHardsuitBase
  name: "базовий шолом скафандру"
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    state: icon # default state used by most inheritors
  - type: Clickable
  - type: InteractionOutline
  - type: Clothing
    #Apparently the hardsuit helmet equip sound is from a walking mech?
    equipSound: /Audio/Mecha/mechmove03.ogg
    unequipSound: /Audio/Mecha/mechmove03.ogg
    quickEquip: false
    slots: [ HEAD ]
  - type: PressureProtection
    highPressureMultiplier: 0.3
    lowPressureMultiplier: 1000
  - type: TemperatureProtection
    coefficient: 0.1
  - type: FireProtection
    reduction: 0.2
  - type: IgniteFromGasImmunity
    parts:
    - Head
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.90
        Slash: 0.90
        Piercing: 0.95
        Heat: 0.90
        Radiation: 0.25
  - type: GroupExamine
  - type: IngestionBlocker
  - type: Tag
    tags:
    - WhitelistChameleon
    - PlasmamanSafe
  - type: IdentityBlocker
  - type: HideLayerClothing
    slots:
    - Hair
    - Snout
  - type: Reflect
    spread: 270
    reflectProb: 0.01
    innate: true
    reflects:
      - Energy
      - NonEnergy

- type: entity
  abstract: true
  parent: ClothingHeadBase
  id: ClothingHeadEnvirohelmBase
  name: "базовий шолом екокостюму"
  components:
  - type: Sprite
    layers:
    - state: icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    size: Normal
    heldPrefix: off
  - type: Clothing
    equipDelay: 0.4
    unequipDelay: 0.6 # Slightly higher delay to protect against accidental unequips
    equippedPrefix: off
    clothingVisuals:
      head:
      - state: equipped-HELMET
      - state: on-equipped-HELMET
  - type: IgniteFromGasImmunity
    parts:
    - Head
  - type: Armor
    modifiers:
      coefficients:
        Caustic: 0.90
  - type: EyeProtection
  - type: BreathMask
  - type: PressureProtection # Same as EVA helmet
    highPressureMultiplier: 0.6
    lowPressureMultiplier: 1000
  - type: TemperatureProtection
    coefficient: 0.2
  - type: IngestionBlocker
  - type: Tag
    tags:
    - WhitelistChameleon
    - Envirohelm
    - PlasmamanSafe
  - type: HideLayerClothing
    slots:
    - Hair
    - Snout
    clothingSlots:
    - mask
  - type: ToggleableLightVisuals
    clothingVisuals:
      head:
      - state: on-equipped-HELMET
        shader: unshaded
  - type: PointLight
    enabled: false
    color: "#ffa1ff"
    radius: 4
    energy: 3
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
    netsync: false
  - type: Appearance
  - type: HandheldLight
    addPrefix: true
    blinkingBehaviourId: blinking
    radiatingBehaviourId: radiating
  - type: LightBehaviour
    behaviours:
      - !type:FadeBehaviour
        id: radiating
        interpolate: Linear
        maxDuration: 2.0
        startValue: 3.0
        endValue: 2.0
        isLooped: true
        reverseWhenFinished: true
      - !type:PulseBehaviour
        id: blinking
        interpolate: Nearest
        maxDuration: 1.0
        minValue: 0.1
        maxValue: 2.0
        isLooped: true
  - type: PowerCellSlot
    cellSlotId: cell_slot
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellMedium
  - type: ContainerContainer
    containers:
      cell_slot: !type:ContainerSlot {}
  - type: GuideHelp # While the playerbase is getting introduced to Plasmamen, add their guidebook here
    guides: [ Plasmaman ]

# When making new custom envirohelms, inherit from this entity and
# replace the color fields on the layers
- type: entity
  abstract: true
  parent: ClothingHeadEnvirohelmBase
  id: ClothingHeadEnvirohelmCustomBase
  name: "базовий кастомний шолом для екокостюму"
  components:
  - type: ToggleableLightVisuals
    inhandVisuals:
      left:
      - state: on-inhand-left
        shader: unshaded
      right:
      - state: on-inhand-right
        shader: unshaded
    clothingVisuals:
      head:
      - state: on-equipped-HELMET
        shader: unshaded
  - type: Sprite
    sprite: Clothing/Head/Envirohelms/custom.rsi
    layers:
    - state: icon
      color: "#FFFFFF"
    - state: accent-icon
      color: "#FF0000"
    # - state: midaccent-icon
    #   color: "#0000FF"
    - state: visor-icon
    - state: icon-flash
      visible: false
      shader: unshaded
      map: [ "light" ]
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#FFFFFF"
      - state: accent-inhand-left
        color: "#FF0000"
      # - state: midaccent-inhand-left
      #   color: "#0000FF"
      - state: visor-inhand-left
      right:
      - state: inhand-right
        color: "#FFFFFF"
      - state: accent-inhand-right
        color: "#FF0000"
      # - state: midaccent-inhand-right
      #   color: "#0000FF"
      - state: visor-inhand-right
  - type: Clothing
    sprite: Clothing/Head/Envirohelms/custom.rsi
    clothingVisuals:
      head:
      - state: equipped-HELMET
        color: "#FFFFFF"
      - state: accent-equipped-HELMET
        color: "#FF0000"
      # - state: midaccent-equipped-HELMET
      #   color: "#0000FF"
      - state: visor-equipped-HELMET

- type: entity
  abstract: true
  parent: ClothingHeadHardsuitBase
  id: ClothingHeadHardsuitWithLightBase
  name: "базовий шолом скафандру з підсвічуванням"
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    layers:
    - state: icon
    - state: icon-flash
      visible: false
      map: [ "light" ]
  - type: Clothing
    equippedPrefix: off
  - type: ToggleableLightVisuals
  - type: PointLight
    enabled: false
    radius: 3
    energy: 2
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
    netsync: false
  - type: Appearance
  - type: HandheldLight
    addPrefix: true
    blinkingBehaviourId: blinking
    radiatingBehaviourId: radiating
  - type: LightBehaviour
    behaviours:
      - !type:FadeBehaviour
        id: radiating
        interpolate: Linear
        maxDuration: 2.0
        startValue: 3.0
        endValue: 2.0
        isLooped: true
        reverseWhenFinished: true
      - !type:PulseBehaviour
        id: blinking
        interpolate: Nearest
        maxDuration: 1.0
        minValue: 0.1
        maxValue: 2.0
        isLooped: true
  - type: Battery
    maxCharge: 600 #lights drain 3/s but recharge of 2 makes this 1/s. Therefore 600 is 10 minutes of light.
    startingCharge: 600
  - type: BatterySelfRecharger
    autoRecharge: true
    autoRechargeRate: 2 #recharge of 2 makes total drain 1w / s so max charge is 1:1 with time. Time to fully charge should be 5 minutes. Having recharge gives light an extended flicker period which gives you some warning to return to light area.

- type: entity
  abstract: true
  id: ClothingHeadHatHoodWinterBase
  name: "капюшон базового зимового пальто"
  description: "Капюшон, створений для того, щоб тримати голову в теплі."
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    state: icon
  - type: Clickable
  - type: Clothing
    equipSound: /Audio/Effects/rustle1.ogg
    unequipSound: /Audio/Effects/rustle2.ogg
    quickEquip: false
    slots: [ HEAD ]
  - type: TemperatureProtection
    coefficient: 0.7
  - type: GroupExamine
  - type: HideLayerClothing
    slots:
    - Hair
