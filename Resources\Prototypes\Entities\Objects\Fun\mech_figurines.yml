#TODO give these the new base sprite used in the regular action figures.
#No, not like a template. Just the base of the toy.
- type: entity
  parent: BaseItem
  id: BaseFigurineMech
  name: "статуетка"
  description: "Маленька мініатюра."
  abstract: true
  components:
  - type: Sprite
    sprite: Objects/Fun/mech_figurines.rsi
  - type: PhysicalComposition
    materialComposition:
      Plastic: 100
  - type: StaticPrice
    price: 75
  - type: Tag
    tags:
    - Figurine
    
#Ripley APLU
- type: entity
  parent: BaseFigurineMech
  id: ToyRipley
  name: "іграшковий ріплі"
  description: "Міні-фігурка Мехи! На спині написано \"Mecha № 1/12\"."
  components:
  - type: Sprite
    state: ripley

#Fire Ripley
- type: entity
  parent: BaseFigurineMech
  id: ToyFireRipley
  name: "вогняний ріплі"
  description: "Міні-фігурка Мехи! На спині написано \"Mecha No. 2/12\"."
  components:
  - type: Sprite
    state: fireripley

#Death Ripley
- type: entity
  parent: BaseFigurineMech
  id: ToyDeathRipley
  name: "іграшковий смерторіплі"
  description: "Міні-фігурка Мехи! На спині написано \"Mecha № 3/12\"."
  components:
  - type: Sprite
    state: deathripley

#Ggygax
- type: entity
  parent: BaseFigurineMech
  id: ToyGygax
  name: "іграшковий гайгакс"
  description: "Міні-фігурка Мехи! На спині написано \"Mecha No. 4/12\"."
  components:
  - type: Sprite
    state: gygax

#Durand
- type: entity
  parent: BaseFigurineMech
  id: ToyDurand
  name: "іграшковий дюранд"
  description: "Міні-фігурка Мехи! На спині написано \"Mecha No. 5/12\"."
  components:
  - type: Sprite
    state: durand

#H.O.N.K
- type: entity
  parent: BaseFigurineMech
  id: ToyHonk
  name: "іграшковий H.O.N.K"
  description: "Міні-фігурка Мехи! На спині написано \"Mecha No. 6/12\"."
  components:
  - type: Sprite
    state: honk

#Marauder
- type: entity
  parent: BaseFigurineMech
  id: ToyMarauder
  name: "іграшковий мародер"
  description: "Міні-фігурка Мехи! На спині написано \"Mecha No. 7/12\"."
  components:
  - type: Sprite
    state: marauder

#Seraph
- type: entity
  parent: BaseFigurineMech
  id: ToySeraph
  name: "іграшковий серафим"
  description: "Міні-фігурка Мехи! На спині написано \"Mecha No. 8/12\"."
  components:
  - type: Sprite
    state: seraph

#Mauler
- type: entity
  parent: BaseFigurineMech
  id: ToyMauler
  name: "іграшковий душогуб"
  description: "Міні-фігурка Мехи! На спині написано \"Mecha No. 9/12\"."
  components:
  - type: Sprite
    state: mauler

#Odysseus
- type: entity
  parent: BaseFigurineMech
  id: ToyOdysseus
  name: "іграшковий одісеус"
  description: "Міні-фігурка Мехи! На спині написано \"Mecha №10/12\"."
  components:
  - type: Sprite
    state: odysseus

#Phazon
- type: entity
  parent: BaseFigurineMech
  id: ToyPhazon
  name: "іграіграшковий фазон"
  description: "Міні-фігурка Мехи! На спині написано \"Mecha №11/12\"."
  components:
  - type: Sprite
    state: phazon

#Reticence
- type: entity
  parent: BaseFigurineMech
  id: ToyReticence
  name: "іграшковий ретісенс"
  description: "Міні-фігурка Мехи! На спині написано \"Mecha No.12/12\"."
  components:
  - type: Sprite
    state: reticence
