- type: decal
  id: Crayons
  abstract: true

- type: decal
  id: Numbers
  parent: Crayons
  abstract: true

- type: decal
  id: Alphabet
  parent: Crayons
  abstract: true

- type: decal
  id: Graffiti
  parent: Crayons
  abstract: true

- type: decal
  id: Symbols
  parent: Crayons
  abstract: true

- type: decal
  id: Info
  parent: Crayons
  abstract: true

- type: decal
  id: Brushes
  parent: Crayons
  abstract: true

- type: decal
  id: 0
  parent: Numbers
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: 0

- type: decal
  id: 1
  parent: Numbers
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: 1

- type: decal
  id: 2
  parent: Numbers
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: 2

- type: decal
  id: 3
  parent: Numbers
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: 3

- type: decal
  id: 4
  parent: Numbers
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: 4

- type: decal
  id: 5
  parent: Numbers
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: 5

- type: decal
  id: 6
  parent: Numbers
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: 6

- type: decal
  id: 7
  parent: Numbers
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: 7

- type: decal
  id: 8
  parent: Numbers
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: 8

- type: decal
  id: 9
  parent: Numbers
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: 9

- type: decal
  id: Blasto
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: Blasto

- type: decal
  id: Clandestine
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: Clandestine

- type: decal
  id: Cyber
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: Cyber

- type: decal
  id: Diablo
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: Diablo

- type: decal
  id: Donk
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: Donk

- type: decal
  id: Gene
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: Gene

- type: decal
  id: Gib
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: Gib

- type: decal
  id: Max
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: Max

- type: decal
  id: Newton
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: Newton

- type: decal
  id: North
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: North

- type: decal
  id: Omni
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: Omni

- type: decal
  id: Osiron
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: Osiron

- type: decal
  id: Prima
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: Prima

- type: decal
  id: Psyke
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: Psyke

- type: decal
  id: Sirius
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: Sirius

- type: decal
  id: Tunnel
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: Tunnel

- type: decal
  id: Waffle
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: Waffle

- type: decal
  id: ampersand
  parent: Symbols
  tags: ["crayon", "crayon-3-symbols"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: ampersand

- type: decal
  id: amyjon
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: amyjon

- type: decal
  id: arrow
  parent: Symbols
  tags: ["crayon", "crayon-3-symbols"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: arrow

- type: decal
  id: beepsky
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: beepsky

- type: decal
  id: biohazard
  parent: Info
  tags: ["crayon", "crayon-4-info"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: biohazard

- type: decal
  id: blueprint
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: blueprint

- type: decal
  id: body
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: body

- type: decal
  id: bottle
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: bottle

- type: decal
  id: brush
  parent: Brushes
  tags: ["crayon", "crayon-1-brushes"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: brush

- type: decal
  id: carp
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: carp

- type: decal
  id: cat
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: cat

- type: decal
  id: chevron
  parent: Symbols
  tags: ["crayon", "crayon-3-symbols"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: chevron

- type: decal
  id: clawprint
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: clawprint

- type: decal
  id: clown
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: clown

- type: decal
  id: comma
  parent: Symbols
  tags: ["crayon", "crayon-3-symbols"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: comma

- type: decal
  id: corgi
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: corgi

- type: decal
  id: credit
  parent: Symbols
  tags: ["crayon", "crayon-3-symbols"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: credit

- type: decal
  id: cyka
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: cyka

- type: decal
  id: danger
  parent: Info
  tags: ["crayon", "crayon-4-info"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: danger

- type: decal
  id: disk
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: disk

- type: decal
  id: dot
  parent: Symbols
  tags: ["crayon", "crayon-3-symbols"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: dot

- type: decal
  id: dwarf
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: dwarf

- type: decal
  id: electricdanger
  parent: Info
  tags: ["crayon", "crayon-4-info"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: electricdanger

- type: decal
  id: end
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: end

- type: decal
  id: engie
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: engie

- type: decal
  id: equals
  parent: Symbols
  tags: ["crayon", "crayon-3-symbols"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: equals

- type: decal
  id: evac
  parent: Info
  tags: ["crayon", "crayon-4-info"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: evac

- type: decal
  id: exclamationmark
  parent: Symbols
  tags: ["crayon", "crayon-3-symbols"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: exclamationmark

- type: decal
  id: face
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: face

- type: decal
  id: fireaxe
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: fireaxe

- type: decal
  id: firedanger
  parent: Info
  tags: ["crayon", "crayon-4-info"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: firedanger

- type: decal
  id: food
  parent: Info
  tags: ["crayon", "crayon-4-info"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: food

- type: decal
  id: footprint
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: footprint

- type: decal
  id: ghost
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: ghost

- type: decal
  id: guy
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: guy

- type: decal
  id: heart
  parent: Symbols
  tags: ["crayon", "crayon-3-symbols"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: heart

- type: decal
  id: largebrush
  parent: Brushes
  tags: ["crayon", "crayon-1-brushes"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: largebrush

- type: decal
  id: like
  parent: Symbols
  tags: ["crayon", "crayon-3-symbols"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: like

- type: decal
  id: line
  parent: Brushes
  tags: ["crayon", "crayon-1-brushes"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: line

- type: decal
  id: matt
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: matt

- type: decal
  id: med
  parent: Info
  tags: ["crayon", "crayon-4-info"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: med

- type: decal
  id: minus
  parent: Symbols
  tags: ["crayon", "crayon-3-symbols"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: minus

- type: decal
  id: nay
  parent: Info
  tags: ["crayon", "crayon-4-info"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: nay

- type: decal
  id: pawprint
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: pawprint

- type: decal
  id: peace
  parent: Info
  tags: ["crayon", "crayon-4-info"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: peace

- type: decal
  id: percent
  parent: Symbols
  tags: ["crayon", "crayon-3-symbols"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: percent

- type: decal
  id: plus
  parent: Symbols
  tags: ["crayon", "crayon-3-symbols"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: plus

- type: decal
  id: pound
  parent: Symbols
  tags: ["crayon", "crayon-3-symbols"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: pound

- type: decal
  id: prolizard
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: prolizard

- type: decal
  id: questionmark
  parent: Symbols
  tags: ["crayon", "crayon-3-symbols"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: questionmark

- type: decal
  id: radiation
  parent: Info
  tags: ["crayon", "crayon-4-info"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: radiation

- type: decal
  id: revolution
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: revolution

- type: decal
  id: rune1
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: rune1

- type: decal
  id: rune2
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: rune2

- type: decal
  id: rune3
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: rune3

- type: decal
  id: rune4
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: rune4

- type: decal
  id: rune5
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: rune5

- type: decal
  id: rune6
  parent: Graffiti
  tags: ["crayon", "crayon-5-graffiti"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: rune6

- type: decal
  id: safe
  parent: Info
  tags: ["crayon", "crayon-4-info"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: safe

- type: decal
  id: scroll
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: scroll

- type: decal
  id: shop
  parent: Info
  tags: ["crayon", "crayon-4-info"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: shop

- type: decal
  id: shortline
  parent: Brushes
  tags: ["crayon", "crayon-1-brushes"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: shortline

- type: decal
  id: shotgun
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: shotgun

- type: decal
  id: skull
  parent: Info
  tags: ["crayon", "crayon-4-info"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: skull

- type: decal
  id: slash
  parent: Symbols
  tags: ["crayon", "crayon-3-symbols"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: slash

- type: decal
  id: smallbrush
  parent: Brushes
  tags: ["crayon", "crayon-1-brushes"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: smallbrush

- type: decal
  id: snake
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: snake

- type: decal
  id: space
  parent: Info
  tags: ["crayon", "crayon-4-info"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: space

- type: decal
  id: splatter
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: splatter

- type: decal
  id: star
  parent: Symbols
  tags: ["crayon", "crayon-3-symbols"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: star

- type: decal
  id: stickman
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: stickman

- type: decal
  id: taser
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: taser

- type: decal
  id: thinline
  parent: Brushes
  tags: ["crayon", "crayon-1-brushes"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: thinline

- type: decal
  id: toilet
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: toilet

- type: decal
  id: toolbox
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: toolbox

- type: decal
  id: trade
  parent: Info
  tags: ["crayon", "crayon-4-info"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: trade

- type: decal
  id: uboa
  parent: Crayons
  tags: ["crayon"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: uboa

- type: decal
  id: а
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: а

- type: decal
  id: б
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: б

- type: decal
  id: в
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: в

- type: decal
  id: г
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: г

- type: decal
  id: д
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: д

- type: decal
  id: е
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: е

- type: decal
  id: ж
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: ж

- type: decal
  id: з
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: з

- type: decal
  id: и
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: и

- type: decal
  id: й
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: й

- type: decal
  id: к
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: к

- type: decal
  id: л
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: л

- type: decal
  id: м
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: м

- type: decal
  id: н
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: н

- type: decal
  id: о
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: о

- type: decal
  id: п
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: п

- type: decal
  id: р
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: р

- type: decal
  id: с
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: с

- type: decal
  id: т
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: т

- type: decal
  id: у
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: у

- type: decal
  id: ф
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: ф

- type: decal
  id: х
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: х

- type: decal
  id: ц
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: ц

- type: decal
  id: ч
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: ч

- type: decal
  id: ш
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: ш

- type: decal
  id: щ
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: щ

- type: decal
  id: ь
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: ь

- type: decal
  id: ю
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: ю

- type: decal
  id: я
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: я

- type: decal
  id: є
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: є

- type: decal
  id: і
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: і

- type: decal
  id: ї
  parent: Alphabet
  tags: ["crayon", "crayon-2-alphanum"]
  defaultCleanable: true
  defaultCustomColor: true
  defaultSnap: false
  sprite:
    sprite: Effects/crayondecals.rsi
    state: ї
