- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitDeathSquad
  name: "форма ескадрону смерті"
  description: "Удосконалений броньований комбінезон, що використовується спецпідрозділами під час спецоперацій."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/deathsquad.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/deathsquad.rsi

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitAncient
  name: "старовинний комбінезон"
  description: "Страшенно пошарпаний і обшарпаний сірий комбінезон. Виглядає так, ніби його не прали більше десяти років."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/ancient.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/ancient.rsi

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitBartender
  name: "форма бармена"
  description: "Гарна і охайна форма. Шкода, що немає бару."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/bartender.rsi

- type: entity
  parent: ClothingUniformBaseFlippable
  id: ClothingUniformJumpsuitBartenderNt
  name: "форма бармена Нанотрейзен"
  description: "Гарна і охайна форма. Шкода, що немає бару."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/bartender_nt.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/bartender_nt.rsi

- type: entity
  parent: [ ClothingUniformBaseFlipped, ClothingUniformJumpsuitBartenderNt ]
  id: ClothingUniformJumpsuitBartenderNtFlipped
  name: "форма бармена Нанотрейзен"

- type: entity
  parent: ClothingUniformBaseFlippable
  id: ClothingUniformJumpsuitBartenderIdris
  name: "форма бармена Idris Incorporated"
  description: "Гарна і охайна форма. Шкода, що немає бару."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/bartender_idris.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/bartender_idris.rsi

- type: entity
  parent: [ ClothingUniformBaseFlipped, ClothingUniformJumpsuitBartenderIdris ]
  id: ClothingUniformJumpsuitBartenderIdrisFlipped
  name: "форма бармена Idris Incorporated"

- type: entity
  parent: ClothingUniformBaseFlippable
  id: ClothingUniformJumpsuitBartenderOrion
  name: "форма бармена Orion Express"
  description: "Гарна і охайна форма. Шкода, що немає бару."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/bartender_orion.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/bartender_orion.rsi

- type: entity
  parent: [ ClothingUniformBaseFlipped, ClothingUniformJumpsuitBartenderOrion ]
  id: ClothingUniformJumpsuitBartenderOrionFlipped
  name: "форма бармена Orion Express"

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitJacketMonkey
  name: "мавпа на куртці бармена"
  description: "Пристойна куртка, для пристойної мавпи."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/punpun.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/punpun.rsi
  - type: SuitSensor
    controlsLocked: false
    randomMode: false
    mode: SensorCords

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitCaptain
  name: "капітанський комбінезон"
  description: "Це синій комбінезон із золотими знаками, що позначають звання \"капітан\"."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/captain.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/captain.rsi

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitCargo
  name: "вантажницький комбінезон"
  description: "Міцний комбінезон, виданий співробітникам відділу логістики." # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/cargotech.rsi

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitSalvageSpecialist
  name: "комбінезон утилізатора"
  description: "Це короткий комбінезон з набором захисних елементів. Він дуже брудний."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/salvage.rsi

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitChiefEngineer
  name: "комбінезон головного інженера"
  description: "Це комбінезон підвищеної видимості, який видається тим інженерам, які достатньо божевільні, щоб досягти звання головного інженера."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/ce.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitChiefEngineerTurtle
  name: "водолазка Головного інженера"
  description: "Жовта водолазка, розроблена спеціально для роботи в умовах інженерного відділу."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/ce_turtle.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/ce_turtle.rsi

- type: entity
  parent: [ClothingUniformBase,ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitChaplain
  name: "комбінезон капелана"
  description: "Це чорний комбінезон, який часто носять релігійні люди."
  components:
    - type: Sprite
      sprite: Clothing/Uniforms/Jumpsuit/chaplain.rsi
    - type: Clothing
      sprite: Clothing/Uniforms/Jumpsuit/chaplain.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitCentcomAgent
  name: "Комбінезон агенції ЦентКом"
  description: "Костюм, який носить юридична команда ЦентКом. Пахне горілою кавою."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/centcom_agent.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/centcom_agent.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitCentcomOfficial
  name: "Комбінезон чиновника CentCom"
  description: "Це комбінезон, який носять співробітники CentCom."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/centcom_official.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/centcom_official.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitCentcomOfficer
  name: "Комбінезон офіцерства ЦентКом"
  description: "Це комбінезон, який носять офіцери Центру."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/centcom_officer.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/centcom_officer.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitChef
  name: "кухарський комбінезон"
  description: "Не можу готувати без цього."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/chef.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/chef.rsi

- type: entity
  parent: ClothingUniformBaseFlippable
  id: ClothingUniformJumpsuitChefNt
  name: "форма шеф-кухаря Нанотрейзен"
  description: "Не можу готувати без цього."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/chef_nt.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/chef_nt.rsi

- type: entity
  parent: [ ClothingUniformBaseFlipped, ClothingUniformJumpsuitChefNt ]
  id: ClothingUniformChefNtFlipped
  name: "форма шеф-кухаря Нанотрейзен"

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitChefIdris
  name: "форма шеф-кухаря Idris Incorporated"
  description: "Не можу готувати без цього."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/chef_idris.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/chef_idris.rsi

- type: entity
  parent: [ ClothingUniformBaseFlipped, ClothingUniformJumpsuitChefIdris ]
  id: ClothingUniformChefIdrisFlipped
  name: "форма шеф-кухаря Idris Incorporated"

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitChefOrion
  name: "форма шеф-кухаря Orion Express"
  description: "Не можу готувати без цього."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/chef_orion.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/chef_orion.rsi

- type: entity
  parent: [ ClothingUniformBaseFlipped, ClothingUniformJumpsuitChefOrion ]
  id: ClothingUniformChefOrionFlipped
  name: "форма шеф-кухаря Orion Express"


- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitChemistry
  name: "хімічний комбінезон"
  description: "На цьому комбінезоні якісь дивні плями. Хм."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/chemistry.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitVirology
  name: "вірусологічний комбінезон"
  description: "Він виготовлений зі спеціального волокна, яке забезпечує особливий захист від біонебезпеки. На ньому є нашивка зі званням вірусолога."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/virology.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/virology.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitGenetics
  name: "генетичний комбінезон"
  description: "Він виготовлений зі спеціального волокна, яке забезпечує особливий захист від біологічних загроз. На ньому є нашивка зі званням генетика."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/genetics.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/genetics.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitClown
  name: "костюм клоуна"
  description: "ГУДОК!"
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/clown.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/clown.rsi
    femaleMask: UniformTop
  - type: Tag
    tags:
    - ClownSuit

- type: entity
  parent: ClothingUniformJumpsuitClown
  id: ClothingUniformJumpsuitClownBanana
  name: "банановий костюм клоуна"
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/clown_banana.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/clown_banana.rsi
    femaleMask: UniformTop
  - type: Construction
    graph: BananaClownJumpsuit
    node: jumpsuit
  - type: Armor
    modifiers:
      coefficients:
        Radiation: 0.8

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitJester
  name: "костюм блазня"
  description: "Весела сукня, добре підходить для того, щоб розважити свого господаря, дядька."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/jester.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/jester.rsi

- type: entity
  parent: ClothingUniformJumpsuitJester
  id: ClothingUniformJumpsuitJesterAlt
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/jester2.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/jester2.rsi

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitCMO
  name: "комбінезон головного лікаря"
  description: "Це комбінезон, який носять ті, хто має досвід роботи на посаді головного лікаря. Він забезпечує незначний біологічний захист."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/cmo.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitCMOTurtle
  name: "комбінезон з водолазкою головного лікаря"
  description: "Це водолазка, яку носять ті, хто має досвід роботи на посаді головного лікаря. Вона забезпечує незначний біологічний захист."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/cmo_turtle.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/cmo_turtle.rsi

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitDetective
  name: "міцний костюм"
  description: "Той, хто носить це, має на увазі бізнес."
  components:
    - type: Sprite
      sprite: Clothing/Uniforms/Jumpsuit/detective.rsi
    - type: Clothing
      sprite: Clothing/Uniforms/Jumpsuit/detective.rsi

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitDetectiveGrey
  name: "чорний костюм"
  description: "Сірий костюм приватного детектива, укомплектований затискачем для краватки."
  components:
    - type: Sprite
      sprite: Clothing/Uniforms/Jumpsuit/detective_grey.rsi
    - type: Clothing
      sprite: Clothing/Uniforms/Jumpsuit/detective_grey.rsi

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitEngineering
  name: "інженерний комбінезон"
  description: "Якби цей костюм був непровідним, можливо, інженери справді робили б свою чортову роботу."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/engineering.rsi

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitEngineeringHazard
  name: "захисний інженерний комбінезон"
  description: "Витканий у похмурому, теплому помаранчевому кольорі. Дає зрозуміти оточуючим, що ви справді серйозно ставитеся до роботи."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/engineering_hazard.rsi

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitHoP
  name: "комбінезон Голови персоналу"
  description: "Досить м'який і ненав'язливий. Ідеально підходить для того, щоб зникнути з лиця всесвіту."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/hop.rsi

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitHoS
  name: "комбінезон Голови СБ"
  description: "Стандартна уніформа охорони станції, прикрашена золотими емблемами та командирськими погонами." # DeltaV - improved description
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Uniforms/Jumpsuit/hos.rsi # DeltaV - resprite
  - type: Clothing
    sprite: DeltaV/Clothing/Uniforms/Jumpsuit/hos.rsi # DeltaV - resprite

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitHoSAlt
  name: "водолазка начальника служби безпеки"
  description: "Це водолазка, яку носять ті, хто достатньо сильний і дисциплінований, щоб досягти посади керівника служби безпеки."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/hos_alt.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/hos_alt.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitHoSBlue
  name: "синій комбінезон Голови СБ"
  description: "Блакитна сорочка служби безпеки станції з вугільними штанами. Із золотими емблемами та командирськими погонами." # DeltaV - improved description
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Uniforms/Jumpsuit/hos_blue.rsi # DeltaV - resprite
  - type: Clothing
    sprite: DeltaV/Clothing/Uniforms/Jumpsuit/hos_blue.rsi # DeltaV - resprite

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitHoSGrey
  name: "сірий комбінезон Голови СБ"
  description: "Сіра сорочка охорони станції з вугільними штанами. Виділяється червоними лампасами, золотими емблемами та командирськими погонами." # DeltaV - improved description
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Uniforms/Jumpsuit/hos_grey.rsi # DeltaV - resprite
  - type: Clothing
    sprite: DeltaV/Clothing/Uniforms/Jumpsuit/hos_grey.rsi # DeltaV - resprite

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitHoSParadeMale
  name: "урочиста форма Голови СБ"
  description: "Розкішний одяг начальника служби безпеки для особливих випадків."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/hos_parade.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/hos_parade.rsi

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitHydroponics
  name: "комбінезон гідропоніки"
  description: "Має сильний земляний запах. Сподіваємося, що це просто бруд, а не забруднення."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/hydro.rsi

- type: entity
  parent: ClothingUniformBaseFlippable
  id: ClothingUniformJumpsuitHydroponicsNt
  name: "комбінезон гідропоніки Нанотрейзен"
  description: "Має сильний земляний запах. Сподіваємося, що це просто бруд, а не забруднення."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/hydro_nt.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/hydro_nt.rsi

- type: entity
  parent: [ ClothingUniformBaseFlipped, ClothingUniformJumpsuitHydroponicsNt ]
  id: ClothingUniformJumpsuitHydroponicsNtFlipped
  name: "комбінезон гідропоніки Нанотрейзен"

- type: entity
  parent: ClothingUniformBaseFlippable
  id: ClothingUniformJumpsuitHydroponicsIdris
  name: "комбінезон гідропоніки Idris Incorporated"
  description: "Має сильний земляний запах. Сподіваємося, що це просто бруд, а не забруднення."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/hydro_idris.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/hydro_idris.rsi

- type: entity
  parent: [ ClothingUniformBaseFlipped, ClothingUniformJumpsuitHydroponicsIdris ]
  id: ClothingUniformJumpsuitHydroponicsIdrisFlipped
  name: "комбінезон гідропоніки Idris Incorporated"

- type: entity
  parent: ClothingUniformBaseFlippable
  id: ClothingUniformJumpsuitHydroponicsOrion
  name: "комбінезон гідропоніки Orion Express"
  description: "Має сильний земляний запах. Сподіваємося, що це просто бруд, а не забруднення."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/hydro_orion.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/hydro_orion.rsi

- type: entity
  parent: [ ClothingUniformBaseFlipped, ClothingUniformJumpsuitHydroponicsOrion ]
  id: ClothingUniformJumpsuitHydroponicsOrionFlipped
  name: "комбінезон гідропоніки Orion Express"

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitJanitor
  name: "прибиральницький комбінезон"
  description: "Комбінезон бідного персоналу зі шваброю."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/janitor.rsi

- type: entity
  parent: ClothingUniformBaseFlippable
  id: ClothingUniformJumpsuitJanitorNt
  name: "комбінезон прибиральника Нанотрейзен"
  description: "Комбінезон бідного персоналу зі шваброю."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/janitor_nt.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/janitor_nt.rsi

- type: entity
  parent: [ ClothingUniformBaseFlipped, ClothingUniformJumpsuitJanitorNt ]
  id: ClothingUniformJumpsuitJanitorNtFlipped
  name: "комбінезон прибиральника Нанотрейзен"

- type: entity
  parent: ClothingUniformBaseFlippable
  id: ClothingUniformJumpsuitJanitorIdris
  name: "комбінезон прибиральника Idris Incorporated"
  description: "Комбінезон бідного персоналу зі шваброю."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/janitor_idris.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/janitor_idris.rsi

- type: entity
  parent: [ ClothingUniformBaseFlipped, ClothingUniformJumpsuitJanitorIdris ]
  id: ClothingUniformJumpsuitJanitorIdrisFlipped
  name: "комбінезон прибиральника Idris Incorporated"

- type: entity
  parent: ClothingUniformBaseFlippable
  id: ClothingUniformJumpsuitJanitorOrion
  name: "комбінезон прибиральника Orion Express"
  description: "Комбінезон бідного персоналу зі шваброю."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/janitor_orion.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/janitor_orion.rsi

- type: entity
  parent: [ ClothingUniformBaseFlipped, ClothingUniformJumpsuitJanitorOrion ]
  id: ClothingUniformJumpsuitJanitorOrionFlipped
  name: "комбінезон прибиральника Orion Express"

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitMedicalDoctor
  name: "лікарський комбінезон"
  description: "Він виготовлений зі спеціального волокна, яке забезпечує незначний захист від біологічних загроз. На грудях є хрест, який означає, що власник є підготовленим медичним персоналом."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/medical.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitMime
  name: "костюм міма"
  description: "..."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/mime.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/mime.rsi

- type: entity
  parent: [ ClothingUniformBase, ClothingUniformFoldableBase ]
  id: ClothingUniformJumpsuitParamedic
  name: "парамедичний комбінезон"
  description: "Це плюс, це ж добре, чи не так?"
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/paramedic.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/paramedic.rsi

- type: entity
  parent: [ ClothingUniformBase, ClothingUniformFoldableBase ]
  id: ClothingUniformJumpsuitBrigmedic
  name: "комбінезон брігмедика" # DeltaV - rename brigmedic to corpsman
  description: "Ця форма видається кваліфікованому персоналу, який пройшов навчання. Нікого не хвилює, що навчання тривало півдня."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Uniforms/Jumpsuit/brigmedic.rsi # DeltaV - resprite
  - type: Clothing
    sprite: DeltaV/Clothing/Uniforms/Jumpsuit/brigmedic.rsi # DeltaV - resprite

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitPrisoner
  name: "комбінезон ув'язненого"
  description: "Попався."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    layers:
    - state: icon
      color: "#ff8300"
    - state: prisoner-icon
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#ff8300"
      - state: prisoner-inhand-left
      right:
      - state: inhand-right
        color: "#ff8300"
      - state: prisoner-inhand-right
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/color.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#ff8300"
      - state: prisoner-equipped-INNERCLOTHING
  - type: SuitSensor
    controlsLocked: true
    randomMode: false
    mode: SensorCords
  - type: Tag
    tags:
    - ClothMade
    - PrisonUniform
    - WhitelistChameleon

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitQM
  name: "комбінезон Голови Карго" # DeltaV - Logistics Department replacing Cargo
  description: "Що коричневий може зробити для вас?"
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/qm.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/qm.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitQMTurtleneck
  name: "водолазка офіцера логістики" # DeltaV - Logistics Department replacing Cargo
  description: "Гостра водолазка, створена для суворого робочого середовища постачання."
  components:
    - type: Sprite
      sprite: Clothing/Uniforms/Jumpsuit/qmturtle.rsi
    - type: Clothing
      sprite: Clothing/Uniforms/Jumpsuit/qmturtle.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitQMFormal
  # DeltaV - Logistics Department - Replace Quartermaster with Logistics Officer
  name: "урочистий костюм Голови Карго"
  description: "Натхненний військовими офіцерами логістики минулого, ідеальне вбрання для офіційних заходів."
  # End of modified code
  components:
    - type: Sprite
      sprite: Clothing/Uniforms/Jumpsuit/qmformal.rsi
    - type: Clothing
      sprite: Clothing/Uniforms/Jumpsuit/qmformal.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitResearchDirector
  name: "водолазка містифікатора" # DeltaV - Epistemics Department replacing Science
  description: "Це водолазка, яку носять ті, хто володіє ноу-хау, щоб досягти позиції Містагога. Її тканина забезпечує незначний захист від біологічних забруднень." # DeltaV - Epistemics Department replacing Science
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/rnd.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/rnd.rsi

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitScientist
  name: "комбінезон вченого"
  description: "Він виготовлений зі спеціального волокна, яке збільшує сприйняття інтелекту і зменшує особисту етику. Має маркування, що позначає власника як науковця."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/scientist.rsi

- type: entity
  parent: [ ClothingUniformBase, ClothingUniformFoldableBase ]
  id: ClothingUniformJumpsuitScientistFormal
  name: "формальний комбінезон вчених"
  description: "Уніформа для витончених науковців, яку найкраще носити з краваткою в тон."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/scientist_formal.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/scientist_formal.rsi

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitRoboticist
  name: "комбінезон робототехніки"
  description: "Це тонкий чорний колір з посиленими швами; чудово підходить для промислової роботи."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/roboticist.rsi

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitSec
  name: "офіцерський комбінезон"
  description: "Вугільні штани та тьмяна червона сорочка - стандартна уніформа сумнозвісного відділу служби безпеки." # DeltaV - improved description
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/security.rsi
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#C12D30"
        sprite: Clothing/Uniforms/Jumpsuit/color.rsi
      right:
      - state: inhand-right
        color: "#C12D30"
        sprite: Clothing/Uniforms/Jumpsuit/color.rsi

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitSecBlue
  name: "синій офіцерський комбінезон" # DeltaV - renamed to mirror other security uniforms
  description: "Прохолодна синя сорочка поверх вугільних штанів - для спокійного і зібраного офіцерства." # DeltaV - improved description
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Uniforms/Jumpsuit/security_blue.rsi # DeltaV - resprite
  - type: Clothing
    sprite: DeltaV/Clothing/Uniforms/Jumpsuit/security_blue.rsi # DeltaV - resprite

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitSecGrey
  name: "сірий офіцерський комбінезон"
  description: "Світло-сіра сорочка з яскраво-червоними вкрапленнями для відданного і чуйного офіцерства." # DeltaV - improved description
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Uniforms/Jumpsuit/security_grey.rsi # DeltaV - resprite
  - type: Clothing
    sprite: DeltaV/Clothing/Uniforms/Jumpsuit/security_grey.rsi # DeltaV - resprite

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitWarden
  name: "наглядацький комбінезон" # DeltaV - renamed to mirror other security uniforms
  description: "Червона сорочка на вугільних штанях, з білими знаками та погонами, що позначають звання наглядача." # DeltaV - improved description
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Uniforms/Jumpsuit/warden.rsi # DeltaV - resprite
  - type: Clothing
    sprite: DeltaV/Clothing/Uniforms/Jumpsuit/warden.rsi # DeltaV - resprite

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformOveralls
  name: "комбінезон"
  description: "Чудово підходить для роботи на свіжому повітрі."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/overalls.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/overalls.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitLibrarian
  name: "комбінезон бібліотекаря"
  description: "Затишний зелений джемпер для куратора книжок."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/librarian.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/librarian.rsi

- type: entity
  parent: ClothingUniformBaseFlippable
  id: ClothingUniformJumpsuitLibrarianNt
  name: "комбінезон бібліотекаря Нанотрейзен"
  description: "Затишний блакитний джемпер для куратора книжок."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/librarian_nt.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/librarian_nt.rsi

- type: entity
  parent: [ ClothingUniformBaseFlipped, ClothingUniformJumpsuitLibrarianNt ]
  id: ClothingUniformJumpsuitLibrarianFlipped
  name: "комбінезон бібліотекаря Нанотрейзен"

- type: entity
  parent: ClothingUniformBaseFlippable
  id: ClothingUniformJumpsuitLibrarianIdris
  name: "комбінезон бібліотекаря Idris Incorporated"
  description: "Затишний джемпер бірюзового кольору підійде для куратора книжок."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/librarian_idris.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/librarian_idris.rsi

- type: entity
  parent: [ ClothingUniformBaseFlipped, ClothingUniformJumpsuitLibrarianIdris ]
  id: ClothingUniformJumpsuitLibrarianIdrisFlipped
  name: "комбінезон бібліотекаря Idris Incorporated"

- type: entity
  parent: ClothingUniformBaseFlippable
  id: ClothingUniformJumpsuitLibrarianOrion
  name: "комбінезон бібліотекаря Orion Express"
  description: "Затишний коричневий джемпер для куратора книжок."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/librarian_orion.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/librarian_orion.rsi

- type: entity
  parent: [ ClothingUniformBaseFlipped, ClothingUniformJumpsuitLibrarianOrion ]
  id: ClothingUniformJumpsuitLibrarianOrionFlipped
  name: "комбінезон бібліотекаря Orion Express"

- type: entity
  parent: ClothingUniformBaseFlippable
  id: ClothingUniformJumpsuitLibrarianHeph
  name: "комбінезон бібліотекаря hephaestus industries"
  description: "Затишний зелений джемпер для куратора книжок."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/librarian_heph.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/librarian_heph.rsi

- type: entity
  parent: [ ClothingUniformBaseFlipped, ClothingUniformJumpsuitLibrarianHeph ]
  id: ClothingUniformJumpsuitLibrarianHephFlipped
  name: "комбінезон бібліотекаря hephaestus industries"

- type: entity
  parent: ClothingUniformBaseFlippable
  id: ClothingUniformJumpsuitLibrarianPMCG
  name: "приватна військова контрактна група комбінезон бібліотекаря"
  description: "Затишний білий джемпер для куратора книжок."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/librarian_pmcg.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/librarian_pmcg.rsi

- type: entity
  parent: [ ClothingUniformBaseFlipped, ClothingUniformJumpsuitLibrarianPMCG ]
  id: ClothingUniformJumpsuitLibrarianPMCGFlipped
  name: "приватна військова контрактна група комбінезон бібліотекаря"

- type: entity
  parent: ClothingUniformBaseFlippable
  id: ClothingUniformJumpsuitLibrarianZav
  name: "комбінезон міжзоряного бібліотекаря \"Заводський"
  description: "Криваво-коричневий джемпер, що пасує куратору книжок."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/librarian_zav.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/librarian_zav.rsi

- type: entity
  parent: [ ClothingUniformBaseFlipped, ClothingUniformJumpsuitLibrarianZav ]
  id: ClothingUniformJumpsuitLibrarianZavFlipped
  name: "комбінезон міжзоряного бібліотекаря \"Заводський"

- type: entity
  parent: ClothingUniformBaseFlippable
  id: ClothingUniformJumpsuitLibrarianZeng
  name: "комбінезон бібліотекаря zeng-hu pharmaceuticals"
  description: "Криваво-коричневий джемпер, що пасує куратору книжок."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/librarian_zeng.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/librarian_zeng.rsi

- type: entity
  parent: [ ClothingUniformBaseFlipped, ClothingUniformJumpsuitLibrarianZeng ]
  id: ClothingUniformJumpsuitLibrarianZengFlipped
  name: "комбінезон бібліотекаря zeng-hu pharmaceuticals"

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitCurator
  name: "розумний костюм"
  description: "Це розумно. Занадто розумно..."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/curator.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/curator.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitLawyerRed
  name: "червоний костюм адвоката"
  description: "Кричущий червоний костюм, який носять адвокати і показушники."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/lawyerred.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/lawyerred.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitLawyerBlue
  name: "синій адвокатський костюм"
  description: "Кричущий синій костюм, який носять адвокати і показушники."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/lawyerblue.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/lawyerblue.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitLawyerBlack
  name: "костюм чорного адвоката"
  description: "Витончений чорний костюм, який носять адвокати та гангстери."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/lawyerblack.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/lawyerblack.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitLawyerPurple
  name: "фіолетовий адвокатський костюм"
  description: "Стильна фіолетова річ, яку носять юристи та люди шоу-бізнесу."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/lawyerpurple.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/lawyerpurple.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitLawyerGood
  name: "костюм хорошого адвоката"
  description: "Липкий костюм ідеально підходить для КРИМІНАЛЬНОГО адвоката!"
  components:
    - type: Sprite
      sprite: Clothing/Uniforms/Jumpsuit/lawyergood.rsi
    - type: Clothing
      sprite: Clothing/Uniforms/Jumpsuit/lawyergood.rsi

- type: entity
  parent: UnsensoredClothingUniformBase
  id: ClothingUniformJumpsuitPyjamaSyndicateBlack
  name: "чорна піжама Синдикату"
  description: "Для довгих ночей у пермі."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/pyjamasyndicateblack.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/pyjamasyndicateblack.rsi

- type: entity
  parent: UnsensoredClothingUniformBase
  id: ClothingUniformJumpsuitPyjamaSyndicatePink
  name: "рожева піжама Синдикату"
  description: "Для довгих ночей у пермі."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/pyjamasyndicatepink.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/pyjamasyndicatepink.rsi

- type: entity
  parent: UnsensoredClothingUniformBase
  id: ClothingUniformJumpsuitPyjamaSyndicateRed
  name: "червона піжама Синдикату"
  description: "Для довгих ночей у пермі."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/pyjamasyndicatered.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/pyjamasyndicatered.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitNanotrasen
  name: "комбінезон Нанотрейзену"
  description: "Величний синій комбінезон для представлення NanoTrasen."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/nanotrasen.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/nanotrasen.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitCapFormal
  name: "урочистий костюм капітана"
  description: "Костюм для особливих випадків."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/capformal.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/capformal.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitCentcomFormal
  name: "урочистий костюм центрального командування"
  description: "Костюм для особливих випадків."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/centcomformal.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/centcomformal.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitHosFormal
  name: "урочистий костюм Голови персоналу"
  description: "Костюм для особливих випадків."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/hosformal.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/hosformal.rsi

- type: entity
  parent: UnsensoredClothingUniformBase
  id: ClothingUniformJumpsuitOperative
  name: "оперативницький комбінезон"
  description: "Уніформа для елітних оперативників Синдикату, які виконують тактичні операції у відкритому космосі."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/operative.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/operative.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitTacticool
  name: "комбінезон tacticool"
  description: "Уніформа для низькокваліфікованих оперативників LARP, які виконують тактичні крадіжки в ізольованих рукавичках у відкритому космосі."
  components:
    - type: Sprite
      sprite: Clothing/Uniforms/Jumpsuit/tacticool.rsi
    - type: Clothing
      sprite: Clothing/Uniforms/Jumpsuit/tacticool.rsi
    # Too cool for sensors to be on
    - type: SuitSensor
      randomMode: false
      mode: SensorOff

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitMercenary
  name: "комбінезон найманця"
  description: "Одяг для справжніх найманців, які пройшли крізь вогонь, воду та джунглі планет, що кишать небезпечними монстрами або цілями, за які призначена винагорода."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/mercenary.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/mercenary.rsi

- type: entity
  parent: [ UnsensoredClothingUniformBase, ClothingUniformFoldableBase ]
  id: ClothingUniformJumpsuitNinja
  name: "комбінезон ніндзя"
  description: "Зручний костюм ніндзя, для зручності під час відпочинку та тренувань."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/ninja.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/ninja.rsi
  - type: Unremoveable

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitAtmos
  name: "комбінезон атмосферного техніка"
  description: "Я на роботі. Я не можу покинути роботу. Робота - це дихання. Я перевіряю якість повітря."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/atmos.rsi

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitAtmosCasual
  name: "повсякденний комбінезон атмосферного техніка"
  description: "З такою ж легкістю можна розслабитися, маючи таку просту роботу, як у вас."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/atmos_casual.rsi

- type: entity
  parent: [ ClothingUniformBase, ClothingUniformFoldableBase ]
  id: ClothingUniformJumpsuitPsychologist
  name: "костюм психолога"
  description: "Я не гублю речі. Я кладу речі в місця, які потім вислизають від мене."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/psychologist.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/psychologist.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitReporter
  name: "репортерський костюм"
  description: "Хороший репортер залишається скептиком все життя."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/reporter.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/reporter.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitSafari
  name: "костюм сафарі"
  description: "Ідеально підходить для екскурсії в джунглі."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/safari.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/safari.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitJournalist
  name: "журналістський костюм"
  description: "Якщо журналістика хороша, вона суперечлива за своєю природою."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/journalist.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/journalist.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitMusician
  name: "смокінг музиканта"
  description: "Модний смокінг для музично налаштованих людей.  Ідеально підходить для будь-якого лаунж-виступу!"
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/musician.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/musician.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitERTChaplain
  name: "Форма капелана ERT"
  description: "Спеціальний костюм, пошитий для елітного капеланського корпусу Центрального командування."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/ert_chaplain.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/ert_chaplain.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitERTEngineer
  name: "Інженерна форма ГШР"
  description: "Спеціальний костюм, виготовлений для елітних інженерів компанії CentCom."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/ert_engineer.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/ert_engineer.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitERTJanitor
  name: "Прибиральницька форма ГШР"
  description: "Спеціальний костюм, пошитий для елітних прибиральників CentCom."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/ert_janitor.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/ert_janitor.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitERTLeader
  name: "уніформа лідерства ГШР"
  description: "Спеціальний костюм, виготовлений для найкращих з еліти компанії CentCom."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/ert_leader.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/ert_leader.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitERTMedic
  name: "медична уніформа ГШР"
  description: "Спеціальний костюм, виготовлений для елітних медиків CentCom."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/ert_medic.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/ert_medic.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitERTSecurity
  name: "уніформа служби безпеки ГШР"
  description: "Спеціальний костюм, виготовлений для елітної служби безпеки компанії ЦентКом."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/ert_security.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/ert_security.rsi

- type: entity
  parent: ClothingUniformJumpsuitClown
  id: ClothingUniformJumpsuitCluwne
  name: "костюм клувні"
  suffix: Unremoveable
  description: "Клятий костюм клувні."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/cluwne.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/cluwne.rsi
  - type: Unremoveable

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitDameDane
  name: "костюм якудзи"
  description: "Бака мітай..."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/damedaneoutfit.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/damedaneoutfit.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitPirate
  name: "піратські відходи"
  description: "Піратська варіація комбінезона космічного моряка."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/pirate.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/pirate.rsi
  - type: AddAccentClothing
    accent: PirateAccent

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitCossack
  name: "козацький костюм"
  description: "старі добрі шаровари і бригантина."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/cossack.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/cossack.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitHawaiBlack
  name: "чорна гавайська сорочка"
  description: "Чорний, як зоряна ніч."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/hawaiblack.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/hawaiblack.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitHawaiBlue
  name: "блакитна гавайська сорочка"
  description: "Синій, як величезний океан."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/hawaiblue.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/hawaiblue.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitHawaiRed
  name: "червона гавайська сорочка"
# Fixed typo, "Red as a juicy watermelons."
  description: "Червоні, як соковиті кавуни."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/hawaired.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/hawaired.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitHawaiYellow
  name: "жовта гавайська сорочка"
  description: "Жовтий, як яскраве сонце."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/hawaiyellow.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/hawaiyellow.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitSyndieFormal
  name: "урочиста уніформа Синдикату"
  description: "Уніформа синдикату виконана в елегантному стилі, в ній навіть шкода робити брудні трюки."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/syndieformal.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/syndieformal.rsi

- type: entity
  parent: [ ClothingUniformBase, ClothingUniformFoldableBase ]
  id: ClothingUniformJumpsuitFlannel
  name: "фланелевий комбінезон"
# Fixed a typo, there should be an apostrophe before the s in 'someone's."
  description: "Пахне так, ніби хтось смажив на грилі."
  components:
    - type: Sprite
      sprite: Clothing/Uniforms/Jumpsuit/flannel.rsi
    - type: Clothing
      sprite: Clothing/Uniforms/Jumpsuit/flannel.rsi

- type: entity
  parent: [ClothingUniformBase,ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitSeniorEngineer
  name: "комбінезон старшої інженерії"
  description: "Ознака майстерності та престижу в інженерному відділі."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/senior_engineer.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/senior_engineer.rsi

- type: entity
  parent: [ClothingUniformBase,ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitSeniorResearcher
  name: "комбінезон старших вчених"
  description: "Ознака майстерності та престижу у науковому відділі." # DeltaV - Epistemics Department replacing Science
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/senior_researcher.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/senior_researcher.rsi

- type: entity
  parent: [ClothingUniformBase,ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitSeniorPhysician
  name: "комбінезон старшого лікаря"
  description: "Ознака майстерності та престижу в медичному відділенні."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/senior_physician.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/senior_physician.rsi

- type: entity
  parent: [ClothingUniformBase, ClothingUniformFoldableBase]
  id: ClothingUniformJumpsuitSeniorOfficer
  name: "комбінезон старшого офіцерства"
  description: "Ознака майстерності та престижу у відділі служби безпеки."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/senior_officer.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/senior_officer.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitWeb
  name: "павутинний комбінезон"
  description: "Принаймні, це вже щось."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/web.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/web.rsi
  - type: Butcherable
    butcheringType: Knife
    spawned:
    - id: MaterialWebSilk1
      amount: 8
  - type: FlavorProfile
    flavors:
      - cobwebs
    ignoreReagents:
      - Fiber
  - type: SolutionContainerManager
    solutions: # 24 (3 (fiber count of web) * 8 (to craft)) + 6 (magical crafting bonus)
      food:
        maxVol: 30
        reagents:
        - ReagentId: Fiber
          Quantity: 30
  - type: Construction
    graph: WebObjects
    node: jumpsuit

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitLoungewear
  name: "літній одяг"
  description: "Довгий відрізок тканини, який обертається навколо тіла для комфорту."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/loungewear.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/loungewear.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitCasualBlue
  name: "повсякденний синій комбінезон"
  description: "Вільна поношена синя сорочка з сірими штанами - ідеальний варіант для тих, хто хоче розслабитися."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/casual.rsi
    layers:
    - state: icon-jumpsuit
    - state: icon-shirt
      color: "#134fc1"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left-jumpsuit
      - state: inhand-left-shirt
        color: "#134fc1"
      right:
      - state: inhand-right-jumpsuit
      - state: inhand-right-shirt
        color: "#134fc1"
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/casual.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING-jumpsuit
      - state: equipped-INNERCLOTHING-shirt
        color: "#134fc1"

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitCasualPurple
  name: "повсякденний фіолетовий комбінезон"
  description: "Вільна поношена фіолетова сорочка з сірими штанами - ідеальний варіант для тих, хто хоче розслабитися."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/casual.rsi
    layers:
    - state: icon-jumpsuit
    - state: icon-shirt
      color: "#9c0dff"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left-jumpsuit
      - state: inhand-left-shirt
        color: "#9c0dff"
      right:
      - state: inhand-right-jumpsuit
      - state: inhand-right-shirt
        color: "#9c0dff"
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/casual.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING-jumpsuit
      - state: equipped-INNERCLOTHING-shirt
        color: "#9c0dff"

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitCasualRed
  name: "повсякденний червоний комбінезон"
  description: "Вільна поношена червона сорочка з сірими штанами - ідеальний варіант для тих, хто хоче розслабитися."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/casual.rsi
    layers:
    - state: icon-jumpsuit
    - state: icon-shirt
      color: "#b30000"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left-jumpsuit
      - state: inhand-left-shirt
        color: "#b30000"
      right:
      - state: inhand-right-jumpsuit
      - state: inhand-right-shirt
        color: "#b30000"
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/casual.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING-jumpsuit
      - state: equipped-INNERCLOTHING-shirt
        color: "#b30000"

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitFamilyGuy
  name: "знайомий одяг"
  description: "Змушує згадати час, коли ви зробили щось смішне."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/familiar_garbs.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/familiar_garbs.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitSuitAmish
  name: "костюм амішів"
  description: "Це дуже схоже на костюм амішів."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/amish_suit.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/amish_suit.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitSuitAristocratic
  name: "аристократичний костюм"
  description: "Елегантний костюм з синіми акцентами, в якому ви відчуєте себе аристократом."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/aristocratic_suit.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/aristocratic_suit.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitSuitAristocraticTuxedo
  name: "аристократичний смокінг"
  description: "Можна майже відчути запах спесо, з якого вона була пошита."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/aristocratic_tuxedo.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/aristocratic_tuxedo.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitAscetic
  name: "простий аскетичний одяг"
  description: "Досить простенька на вигляд форма."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/ascetic.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/ascetic.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitBarber
  name: "комбінезон перукаря"
  description: "Хочеться постригтися."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/barber.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/barber.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitSuitBlueBlazer
  name: "синій піджак"
  description: "Сміливий, але консервативний образ. Червоні вельветові штани, темно-синій піджак і краватка."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/blue_blazer.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/blue_blazer.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitDisheveled
  name: "розпатланий костюм"
  description: "Те, що може виглядати як доглянуте офіційне вбрання, якщо ви сліпі."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/disheveled.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/disheveled.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitSuitRegal
  name: "королівський костюм"
  description: "Елегантний червоний костюм з жовтими акцентами."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/regal_suit.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/regal_suit.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitSuitRegalTuxedo
  name: "королівський смокінг"
  description: "Елегантний ошатний костюм з червоним пальто."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/regal_tuxedo.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/regal_tuxedo.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitRippedPunk
  name: "рвані панківські джинси"
  description: "Чорні рвані джинси і топ в сіточку. Як панк."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/rippedpunk.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/rippedpunk.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitSailor
  name: "матроський костюм"
  description: "Популярна на станції \"Підводний човен\"."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/sailor.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/sailor.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitSuitStriped
  name: "смугастий костюм"
  description: "Ансамбль у червоно-білу смужку з темними підтяжками."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/striped_suit.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/striped_suit.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitTrackpants
  name: "спортивні штани"
  description: "Однотонна футболка та спортивні штани."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/trackpants.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/trackpants.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitTurtleneckGrey
  name: "сіра водолазка"
  description: "Теплий светр і темно-сині штани."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/turtleneck_grey.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/turtleneck_grey.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitSuitVictorianRedBlack
  name: "червоно-чорний вікторіанський костюм"
  description: "Костюм у вікторіанському стилі, шикарно!"
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/victorian_red_black.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/victorian_red_black.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitSuitVictorianRedVest
  name: "червоний вікторіанський костюм"
  description: "Костюм у вікторіанському стилі, шикарно!"
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/victorian_red_vest.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/victorian_red_vest.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitSuitVictorianVest
  name: "вікторіанський костюм"
  description: "Костюм у вікторіанському стилі, шикарно!"
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/victorian_vest.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/victorian_vest.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitWaiter
  name: "одяг офіціанта"
  description: "Офіціант! Офіціанте! Принесіть ще страв до цього вбрання, будь ласка!"
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/waiter.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/waiter.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitYogaGymBra
  name: "бюстгальтер для йоги"
  description: "Спортивний бюстгальтер у поєднанні зі штанами для йоги ідеально підходить для тренувань і водночас для гарного вигляду."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/yoga_gym_bra.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/yoga_gym_bra.rsi

# Nuclear 14 outfits
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitSuitRolledSleeves
  name: "костюм з закатаними рукавами"
  description: "Елегантний костюм з жилетом і червоною краваткою, рукави засукані, щоб показати передпліччя."
  components:
  - type: Sprite
    sprite: _Nuclear14/Clothing/Uniforms/FalloutSuits/detectivealt.rsi
  - type: Clothing
    sprite: _Nuclear14/Clothing/Uniforms/FalloutSuits/detectivealt.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitBartenderGrey
  name: "сіра форма бармена"
  description: "Гарна й охайна сіра уніформа. Шкода, що немає бару."
  components:
  - type: Sprite
    sprite: _Nuclear14/Clothing/Uniforms/FalloutSuits/bartenderalt.rsi
  - type: Clothing
    sprite: _Nuclear14/Clothing/Uniforms/FalloutSuits/bartenderalt.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitCheckered
  name: "картатий костюм"
  description: "Гра була сфальсифікована з самого початку."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/checkered.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/checkered.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitFlannelOveralls
  name: "фланелеві комбінезони"
  description: "О, я йду по цій дорозі з поганим настроєм... Господи, я йду по цій дорозі і відчуваю себе погано..."
  components:
  - type: Sprite
    sprite: _Nuclear14/Clothing/Uniforms/FalloutSuits/caravaneer.rsi
  - type: Clothing
    sprite: _Nuclear14/Clothing/Uniforms/FalloutSuits/caravaneer.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitCowboyBrown
  name: "коричневий ковбойський костюм"
  description: "Ура, напарнику!"
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/cowboybrown.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/cowboybrown.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitCowboyGrey
  name: "сірий ковбойський костюм"
  description: "Ура, напарнику!"
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/cowboygrey.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/cowboygrey.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitCamouflageShirt
  name: "камуфляжна сорочка"
  description: "Камуфляжна сорочка з довгими рукавами в поєднанні з класичними джинсами."
  components:
  - type: Sprite
    sprite: _Nuclear14/Clothing/Uniforms/FalloutSuits/fudd.rsi
  - type: Clothing
    sprite: _Nuclear14/Clothing/Uniforms/FalloutSuits/fudd.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitOliveSweater
  name: "оливковий светр"
  description: "Затишний светр, який щільно прилягає до сірих робочих штанів."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/olive_sweater.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/olive_sweater.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitDesertUniform
  name: "пустельна форма"
  description: "Репліка вінтажної бойової форми Solarian, для ЛАРПінгу."
  components:
  - type: Sprite
    sprite: _Nuclear14/Clothing/Uniforms/FalloutSuits/desertbdu.rsi
  - type: Clothing
    sprite: _Nuclear14/Clothing/Uniforms/FalloutSuits/desertbdu.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitLumberjack
  name: "спецодяг лісоруба"
  description: "Комбінезон, який робить вас схожим на лісоруба."
  components:
  - type: Sprite
    sprite: _Nuclear14/Clothing/Uniforms/FalloutSuits/lumberjack.rsi
  - type: Clothing
    sprite: _Nuclear14/Clothing/Uniforms/FalloutSuits/lumberjack.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitLightBlueShirt
  name: "світло-блакитна сорочка"
  description: "Світло-блакитна сорочка з блідим хакі, легка і зручна."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/lightblue_shirt.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/lightblue_shirt.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitGreyShirt
  name: "сіра сорочка та оливкові штани"
  description: "Іноді все, що вам потрібно, - це перевірене часом поєднання сірого та оливкового."
  components:
  - type: Sprite
    sprite: _Nuclear14/Clothing/Uniforms/FalloutSuits/rangerv3.rsi
  - type: Clothing
    sprite: _Nuclear14/Clothing/Uniforms/FalloutSuits/rangerv3.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitFlannelJeans
  name: "фланелеву сорочку та джинси"
  description: "Червона фланелева сорочка в поєднанні з ніжно-блакитними джинсами. Як оригінально."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/flannel_jeans.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/flannel_jeans.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitFlannelBlackJeans
  name: "чорна фланелева сорочка та джинси"
  description: "Не просто фланель, а фланелеву сорочку чорного кольору! Сміливі сьогодні, чи не так?"
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/black_flannel_jeans.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/black_flannel_jeans.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitFlannelKhakis
  name: "фланелеву сорочку та хакі"
  description: "Червона фланелева сорочка зі штанами кольору хакі - елегантна пара."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/flannel_khakis.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/flannel_khakis.rsi

- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitFlannelBlackKhakis
  name: "чорна фланелева сорочка та хакі"
  description: "Чорна фланелева сорочка з брюками кольору хакі - елегантна пара."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/black_flannel_khakis.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/black_flannel_khakis.rsi
#PIRATE
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformJumpsuitBlueshield
  name: "комбінезон синього кольору"
  description: "Сер, це дуже технологічний комбенізон, він зшитий найдосвідчинішими клоунами!."
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Jumpsuit/blueshield.rsi
  - type: Clothing
    sprite: Clothing/Uniforms/Jumpsuit/blueshield.rsi
