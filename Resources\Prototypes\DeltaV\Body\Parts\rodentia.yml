- type: entity
  id: PartRodentia
  parent: BaseItem
  name: "частина тіла роденції"
  abstract: true
  components:
  - type: Sprite
    sprite: DeltaV/Mobs/Species/Rodentia/parts.rsi
  - type: Icon
    sprite: DeltaV/Mobs/Species/Rodentia/parts.rsi
  - type: Damageable
    damageContainer: Biological
  - type: BodyPart
  - type: ContainerContainer
    containers:
      bodypart: !type:Container
        ents: []
  - type: StaticPrice #DynamicPrice
    price: 100
  - type: Tag
    tags:
      - Trash

- type: entity
  id: TorsoRodentia
  name: "тулуб роденції"
  parent: PartRodentia
  components:
  - type: Sprite
    state: "torso_m"
  - type: Icon
    state: "torso_m"
  - type: BodyPart
    partType: Torso

- type: entity
  id: HeadRodentia
  name: "голова роденції"
  parent: PartRodentia
  components:
  - type: Sprite
    state: "head_m"
  - type: Icon
    state: "head_m"
  - type: BodyPart
    partType: Head
    vital: true
  - type: Input
    context: "ghost"
  - type: InputMover
  - type: GhostOnMove
  - type: Tag
    tags:
      - Head

- type: entity
  id: LeftArmRodentia
  name: "ліва рука роденції"
  parent: PartRodentia
  components:
  - type: Sprite
    state: "l_arm"
  - type: Icon
    state: "l_arm"
  - type: BodyPart
    partType: Arm
    symmetry: Left

- type: entity
  id: RightArmRodentia
  name: "права рука роденції"
  parent: PartRodentia
  components:
  - type: Sprite
    state: "r_arm"
  - type: Icon
    state: "r_arm"
  - type: BodyPart
    partType: Arm
    symmetry: Right

- type: entity
  id: LeftHandRodentia
  name: "ліва долоня роденції"
  parent: PartRodentia
  components:
  - type: Sprite
    state: "l_hand"
  - type: Icon
    state: "l_hand"
  - type: BodyPart
    partType: Hand
    symmetry: Left

- type: entity
  id: RightHandRodentia
  name: "права долоня роденції"
  parent: PartRodentia
  components:
  - type: Sprite
    state: "r_hand"
  - type: Icon
    state: "r_hand"
  - type: BodyPart
    partType: Hand
    symmetry: Right

- type: entity
  id: LeftLegRodentia
  name: "ліва нога роденції"
  parent: PartRodentia
  components:
  - type: Sprite
    state: "l_leg"
  - type: Icon
    state: "l_leg"
  - type: BodyPart
    partType: Leg
    symmetry: Left
  - type: MovementBodyPart

- type: entity
  id: RightLegRodentia
  name: "права нога роденції"
  parent: PartRodentia
  components:
  - type: Sprite
    state: "r_leg"
  - type: Icon
    state: "r_leg"
  - type: BodyPart
    partType: Leg
    symmetry: Right
  - type: MovementBodyPart

- type: entity
  id: LeftFootRodentia
  name: "ліва ступня роденції"
  parent: PartRodentia
  components:
  - type: Sprite
    state: "l_foot"
  - type: Icon
    state: "l_foot"
  - type: BodyPart
    partType: Foot
    symmetry: Left

- type: entity
  id: RightFootRodentia
  name: "права ступня роденції"
  parent: PartRodentia
  components:
  - type: Sprite
    state: "r_foot"
  - type: Icon
    state: "r_foot"
  - type: BodyPart
    partType: Foot
    symmetry: Right
