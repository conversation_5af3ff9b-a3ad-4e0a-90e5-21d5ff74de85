### UI

# Shown when examining the window. Each entry represents the window's health condition
comp-window-damaged-1 = It looks fully intact.
comp-window-damaged-2 = It has a few scratches
comp-window-damaged-3 = It has a few small cracks.
comp-window-damaged-4 = It has several big cracks running along its surface.
comp-window-damaged-5 = It has deep cracks across multiple layers.
comp-window-damaged-6 = It's extremely cracked and on the verge of shattering.

### Interaction Messages

# Shown when knocking on a window
comp-window-knock = *knock knock*
