- type: entity
  id: BasePowerCell
  abstract: true
  parent: BaseItem
  components:
  - type: Item
    storedRotation: -90
  - type: Battery
    pricePerJoule: 0.15
  - type: PowerCell
  - type: Explosive
    explosionType: Default
    maxIntensity: 200
    intensitySlope: 1.5
  - type: Sprite
    sprite: Objects/Power/power_cells.rsi
  - type: SolutionContainerManager
    solutions:
      battery:
        maxVol: 5
  - type: InjectableSolution
    solution: battery
  - type: DrawableSolution
    solution: battery
  - type: Extractable
    juiceSolution:
      reagents:
        - ReagentId: Zinc
          Quantity: 5
  - type: Tag
    tags:
      - PowerCell
  - type: Appearance
  - type: PowerCellVisuals
  - type: Riggable
  - type: HitscanBatteryAmmoProvider
    proto: RedLightLaser
    fireCost: 50

- type: entity
  name: "картопляна батарея"
  description: "Справді геніальне джерело енергії."
  id: PowerCellPotato
  parent: BasePowerCell
  components:
  - type: Sprite
    layers:
    - state: potato
  - type: Battery
    maxCharge: 70
    startingCharge: 70
  - type: Tag
    tags:
      - PotatoBattery
  - type: Construction
    graph: PowerCellPotato
    node: potatobattery

- type: entity
  name: "батарейка малої ємності"
  description: "Елемент живлення, що перезаряджається. Це найдешевший тип, який ви можете знайти."
  id: PowerCellSmall
  suffix: Full
  parent: BasePowerCell
  components:
  - type: Sprite
    layers:
    - map: [ "enum.PowerCellVisualLayers.Base" ]
      state: small
    - map: [ "enum.PowerCellVisualLayers.Unshaded" ]
      state: o2
      shader: unshaded
  - type: Battery
    maxCharge: 360
    startingCharge: 360
  - type: MachinePart
    part: PowerCell
    rating: 1
  - type: Tag
    tags:
      - PowerCellSmall

- type: entity
  id: PowerCellSmallPrinted
  suffix: Empty
  parent: PowerCellSmall
  components:
  - type: Sprite
    layers:
      - map: [ "enum.PowerCellVisualLayers.Base" ]
        state: small
      - map: [ "enum.PowerCellVisualLayers.Unshaded" ]
        state: o2
        shader: unshaded
        visible: false
  - type: Battery
    maxCharge: 360
    startingCharge: 0

- type: entity
  name: "батарейка середньої ємності"
  description: "Акумуляторна батарея. Це популярний і надійний варіант."
  id: PowerCellMedium
  suffix: Full
  parent: BasePowerCell
  components:
  - type: Sprite
    layers:
    - map: [ "enum.PowerCellVisualLayers.Base" ]
      state: medium
    - map: [ "enum.PowerCellVisualLayers.Unshaded" ]
      state: o2
      shader: unshaded
  - type: Battery
    maxCharge: 720
    startingCharge: 720
  - type: MachinePart
    part: PowerCell
    rating: 2

- type: entity
  id: PowerCellMediumPrinted
  suffix: Empty
  parent: PowerCellMedium
  components:
  - type: Sprite
    layers:
      - map: [ "enum.PowerCellVisualLayers.Base" ]
        state: medium
      - map: [ "enum.PowerCellVisualLayers.Unshaded" ]
        state: o2
        shader: unshaded
        visible: false
  - type: Battery
    maxCharge: 720
    startingCharge: 0

- type: entity
  name: "батарейка високої ємності"
  description: "Перезарядний стандартизований елемент живлення. Цей преміальний бренд зберігає до 50% більше енергії, ніж конкуренти."
  id: PowerCellHigh
  suffix: Full
  parent: BasePowerCell
  components:
  - type: Sprite
    layers:
    - map: [ "enum.PowerCellVisualLayers.Base" ]
      state: high
    - map: [ "enum.PowerCellVisualLayers.Unshaded" ]
      state: o2
      shader: unshaded
  - type: Battery
    maxCharge: 1080
    startingCharge: 1080
  - type: MachinePart
    part: PowerCell
    rating: 3

- type: entity
  id: PowerCellHighPrinted
  suffix: Empty
  parent: PowerCellHigh
  components:
  - type: Sprite
    layers:
      - map: [ "enum.PowerCellVisualLayers.Base" ]
        state: high
      - map: [ "enum.PowerCellVisualLayers.Unshaded" ]
        state: o2
        shader: unshaded
        visible: false
  - type: Battery
    maxCharge: 1080
    startingCharge: 0

- type: entity
  name: "батарейка надвисокої ємності"
  description: "Перезаряджається стандартизований елемент живлення. Цей виглядає як рідкісний і потужний прототип."
  id: PowerCellHyper
  suffix: Full
  parent: BasePowerCell
  components:
  - type: Sprite
    layers:
    - map: [ "enum.PowerCellVisualLayers.Base" ]
      state: hyper
    - map: [ "enum.PowerCellVisualLayers.Unshaded" ]
      state: o2
      shader: unshaded
  - type: Battery
    maxCharge: 1800
    startingCharge: 1800
  - type: MachinePart
    part: PowerCell
    rating: 4

- type: entity
  id: PowerCellHyperPrinted
  suffix: Empty
  parent: PowerCellHyper
  components:
  - type: Sprite
    layers:
      - map: [ "enum.PowerCellVisualLayers.Base" ]
        state: hyper
      - map: [ "enum.PowerCellVisualLayers.Unshaded" ]
        state: o2
        shader: unshaded
        visible: false
  - type: Battery
    maxCharge: 1800
    startingCharge: 0

- type: entity
  parent: BasePowerCell
  id: PowerCellMicrortg
  name: "мікрорадіоізотопний термоелектричний генератор"
  description: "Прототип перезаряджаємої мікро-rtg комірки. Має меншу ємність, але повільно перезаряджається самостійно. Не має достатнього радіаційного захисту."
  suffix: Full
  components:
  - type: Sprite
    layers:
    - map: [ "enum.PowerCellVisualLayers.Base" ]
      state: micrortg
    - map: [ "enum.PowerCellVisualLayers.Unshaded" ]
      state: o2
      shader: unshaded
  - type: Battery
    maxCharge: 690
    startingCharge: 690
  - type: RadiationSource
    intensity: 0.15
    slope: 0.03
  - type: BatterySelfRecharger
    autoRecharge: true
    autoRechargeRate: 7 # takes 1 minute and 30 seconds to charge itself back to full

- type: entity
  parent: PowerCellMicrortg
  id: PowerCellMicrortgPrinted
  suffix: Empty
  components:
  - type: Sprite
    layers:
    - map: [ "enum.PowerCellVisualLayers.Base" ]
      state: micrortg
    - map: [ "enum.PowerCellVisualLayers.Unshaded" ]
      state: o2
      shader: unshaded
      visible: false
  - type: Battery
    startingCharge: 0

- type: entity
  parent: BasePowerCell
  id: PowerCellMicroreactor
  name: "мікрореакторна батарейка"
  description: "Перезаряджається стандартизованим мікрореакторним осередком. Має меншу ємність, але повільно перезаряджається самостійно."
  suffix: Full
  components:
  - type: Sprite
    layers:
    - map: [ "enum.PowerCellVisualLayers.Base" ]
      state: microreactor
    - map: [ "enum.PowerCellVisualLayers.Unshaded" ]
      state: o2
      shader: unshaded
  - type: Battery
    maxCharge: 720
    startingCharge: 720
  - type: BatterySelfRecharger
    autoRecharge: true
    autoRechargeRate: 12 # takes 1 minute to charge itself back to full

- type: entity
  parent: PowerCellMicroreactor
  id: PowerCellMicroreactorPrinted
  suffix: Empty
  components:
  - type: Sprite
    layers:
    - map: [ "enum.PowerCellVisualLayers.Base" ]
      state: microreactor
    - map: [ "enum.PowerCellVisualLayers.Unshaded" ]
      state: o2
      shader: unshaded
      visible: false
  - type: Battery
    startingCharge: 0

- type: entity
  name: "прототип антикварної батарейки"
  description: "Невелика батарейка, що самозаряджається. Використовується в дослідженнях старої лазерної зброї."
  id: PowerCellAntiqueProto
  parent: BasePowerCell
  components:
    - type: Sprite
      layers:
      - map: [ "enum.PowerCellVisualLayers.Base" ]
        state: antique
      - map: [ "enum.PowerCellVisualLayers.Unshaded" ]
        state: o2
        shader: unshaded
    - type: Battery
      maxCharge: 1200
      startingCharge: 1200
    - type: BatterySelfRecharger
      autoRecharge: true
      autoRechargeRate: 40

# Power cage (big heavy power cell for big devices)

- type: entity
  id: BasePowerCage
  abstract: true
  parent: BasePowerCell
  components:
  - type: Item
    size: Ginormous
  - type: MultiHandedItem
  - type: SolutionContainerManager
    solutions:
      battery:
        maxVol: 15
  - type: Extractable
    juiceSolution:
      reagents:
        - ReagentId: Zinc
          Quantity: 15
  - type: Tag
    tags:
      - PowerCage
  - type: HitscanBatteryAmmoProvider
    proto: RedShuttleLaser
    fireCost: 150
  - type: ClothingSpeedModifier
    walkModifier: 0.8
    sprintModifier: 0.8
  - type: HeldSpeedModifier

- type: entity
  id: PowerCageSmall
  parent: BasePowerCage
  name: "акумулятор малої ємності"
  description: "Акумуляторна батарея для великих пристроїв. Це найдешевший тип, який ви можете знайти."
  components:
  - type: Sprite
    sprite: Objects/Power/power_cages.rsi
    layers:
    - map: [ "enum.PowerCellVisualLayers.Base" ]
      state: small
    - map: [ "enum.PowerCellVisualLayers.Unshaded" ]
      state: o2
      shader: unshaded
  - type: Battery
    maxCharge: 1400
    startingCharge: 1400

- type: entity
  id: PowerCageMedium
  parent: BasePowerCage
  name: "акумулятор середньої ємності"
  description: "Акумуляторна батарея для великих пристроїв. Золотий стандарт ємності та вартості."
  components:
  - type: Sprite
    sprite: Objects/Power/power_cages.rsi
    layers:
    - map: [ "enum.PowerCellVisualLayers.Base" ]
      state: medium
    - map: [ "enum.PowerCellVisualLayers.Unshaded" ]
      state: o2
      shader: unshaded
  - type: Battery
    maxCharge: 2700
    startingCharge: 2700

- type: entity
  id: PowerCageHigh
  parent: BasePowerCage
  name: "акумулятор великої ємності"
  description: "Акумуляторна батарея для великих пристроїв. Збільшена ємність для підвищених рівнів потужності."
  components:
  - type: Sprite
    sprite: Objects/Power/power_cages.rsi
    layers:
    - map: [ "enum.PowerCellVisualLayers.Base" ]
      state: high
    - map: [ "enum.PowerCellVisualLayers.Unshaded" ]
      state: o2
      shader: unshaded
  - type: Battery
    maxCharge: 6200
    startingCharge: 6200

- type: entity
  id: PowerCageSmallEmpty
  parent: PowerCageSmall
  suffix: Empty
  components:
  - type: Sprite
    sprite: Objects/Power/power_cages.rsi
    layers:
    - map: [ "enum.PowerCellVisualLayers.Base" ]
      state: small
    - map: [ "enum.PowerCellVisualLayers.Unshaded" ]
      state: o2
      shader: unshaded
      visible: false
  - type: Battery
    maxCharge: 1400
    startingCharge: 0

- type: entity
  id: PowerCageMediumEmpty
  parent: PowerCageMedium
  suffix: Empty
  components:
  - type: Sprite
    sprite: Objects/Power/power_cages.rsi
    layers:
    - map: [ "enum.PowerCellVisualLayers.Base" ]
      state: small
    - map: [ "enum.PowerCellVisualLayers.Unshaded" ]
      state: o2
      shader: unshaded
      visible: false
  - type: Battery
    startingCharge: 0

- type: entity
  id: PowerCageHighEmpty
  parent: PowerCageHigh
  suffix: Empty
  components:
  - type: Sprite
    sprite: Objects/Power/power_cages.rsi
    layers:
    - map: [ "enum.PowerCellVisualLayers.Base" ]
      state: small
    - map: [ "enum.PowerCellVisualLayers.Unshaded" ]
      state: o2
      shader: unshaded
      visible: false
  - type: Battery
    startingCharge: 0
