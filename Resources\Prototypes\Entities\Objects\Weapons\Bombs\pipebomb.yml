- type: entity
  parent: GrenadeBase
  id: PipeBomb
  name: "трубна бомба"
  description: "Саморобна вибухівка з труб та дроту."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Bombs/pipebomb.rsi
    layers:
    - state: base
      map: ["enum.TriggerVisualLayers.Base"]
    - state: wires
  - type: OnUseTimerTrigger # todo: make it activate through welder/lighter/fire instead
    delay: 5
    examinable: false
    initialBeepDelay: 0
    beepSound: /Audio/Effects/lightburn.ogg
  - type: RandomTimerTrigger
    min: 1
    max: 10
  - type: ExplodeOnTrigger
  - type: Explosive # Weak explosion in a very small radius. Doesn't break underplating.
    explosionType: Default
    totalIntensity: 50
    intensitySlope: 5
    maxIntensity: 6
    canCreateVacuum: false
  - type: Appearance
  - type: TimerTriggerVisuals
  - type: Construction
    graph: PipeBomb
    node: pipebomb
  - type: SpawnOnTrigger # Pirate shock wave
    proto: ExplosionEffectGrenadeShockWave

- type: entity
  parent: BaseItem
  id: PipeBombGunpowder
  name: "трубна бомба"
  description: "Саморобна вибухівка, зроблена з труби. Тут немає пороху."
  suffix: Gunpowder
  components:
  - type: Sprite
    sprite: Objects/Weapons/Bombs/pipebomb.rsi
    state: base
  - type: Construction
    graph: PipeBomb
    node: gunpowder
    defaultTarget: pipebomb

- type: entity
  parent: BaseItem
  id: PipeBombCable
  name: "трубна бомба"
  description: "Саморобна вибухівка, зроблена з труби. Ця без кабелю."
  suffix: Cable
  components:
  - type: Sprite
    sprite: Objects/Weapons/Bombs/pipebomb.rsi
    state: base
  - type: Construction
    graph: PipeBomb
    node: cable
    defaultTarget: pipebomb