- type: entity
  id: ClosetBase
  parent: BaseStructureDynamic
  name: "шафа"
  description: "Стандартний контейнер для зберігання від компанії Нанотрейзен."
  abstract: true
  components:
  - type: ResistLocker
  - type: Transform
    noRot: true
  - type: Sprite
    noRot: true
    sprite: Structures/Storage/closet.rsi
    layers:
    - state: generic
      map: ["enum.StorageVisualLayers.Base"]
    - state: generic_door
      map: ["enum.StorageVisualLayers.Door"]
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
  - type: MovedByPressure
  - type: PaperLabel
    labelSlot:
      insertVerbText: Attach Label
      ejectVerbText: Remove Label
      whitelist:
        components:
        - Paper
  - type: ItemSlots
  - type: DamageOnHighSpeedImpact
    damage:
      types:
        Blunt: 5
    soundHit:
      collection: MetalThud
  - type: InteractionOutline
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.48,0.25,0.48"
        density: 150
        mask:
        - MachineMask
        layer:
        - MachineLayer
  - type: EntityStorage
    closeSound:
      path: /Audio/Effects/closet_close.ogg
      params:
        volume: -4
    openSound:
      path: /Audio/Effects/closet_open.ogg
      params:
        volume: -4
  - type: ContainerContainer
    containers:
      entity_storage: !type:Container
      paper_label: !type:ContainerSlot
  - type: Weldable
  - type: PlaceableSurface
    placeCentered: true
    isPlaceable: false
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: StructuralMetallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
          params:
            volume: -6
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 1
  - type: Appearance
  - type: EntityStorageVisuals
    stateBaseClosed: generic
    stateDoorOpen: generic_open
    stateDoorClosed: generic_door
  - type: StaticPrice
    price: 50
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 10
    staminaCost: 35

# steel closet base (that can be constructed/deconstructed)
- type: entity
  id: ClosetSteelBase
  parent: ClosetBase
  components:
  - type: Construction
    graph: ClosetSteel
    node: done
    containers:
    - entity_storage
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 15
    staminaCost: 50

#Wall Closet
- type: entity
  id: BaseWallCloset
  placement:
    mode: SnapgridCenter
  abstract: true
  name: "настінна шафа"
  description: "Стандартний контейнер для зберігання від компанії Нанотрейзен, тепер на стінах."
  components:
  - type: InteractionOutline
  - type: Clickable
  - type: ResistLocker
  - type: Weldable
  - type: WallMount
    arc: 175
  - type: StaticPrice
    price: 50
  - type: Transform
    noRot: false
  - type: Sprite
    drawdepth: WallMountedItems
    noRot: false
    sprite: Structures/Storage/wall_locker.rsi
    layers:
    - state: generic
      map: ["enum.StorageVisualLayers.Base"]
    - state: generic_door
      map: ["enum.StorageVisualLayers.Door"]
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
  - type: EntityStorage
    isCollidableWhenOpen: true
    enteringOffset: 0, -0.75
    closeSound:
      path: /Audio/Items/deconstruct.ogg
    openSound:
      path: /Audio/Items/deconstruct.ogg
  - type: ContainerContainer
    containers:
      entity_storage: !type:Container
        ents: []
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
          params:
            volume: -6
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 1
  - type: Construction
    graph: ClosetWall
    node: done
    containers:
    - entity_storage

#Wall locker
- type: entity
  id: BaseWallLocker
  parent: BaseWallCloset
  abstract: true
  components:
  - type: AccessReader
  - type: Lock
  - type: LockVisuals
  - type: Sprite
    sprite: Structures/Storage/wall_locker.rsi
    layers:
    - state: generic
      map: ["enum.StorageVisualLayers.Base"]
    - state: generic_door
      map: ["enum.StorageVisualLayers.Door"]
    - state: locked
      map: ["enum.LockVisualLayers.Lock"]
      shader: unshaded
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]

#Base suit storage unit
#I am terribly sorry for duplicating the closet almost-wholesale, but the game malds at me if I don't so here we are.
- type: entity
  id: SuitStorageBase
  parent: BaseStructure
  name: "шафа для зберігання костюмів"
  description: "Химерна високотехнологічна шафа для зберігання скафандрів."
  components:
  - type: AccessReader
  - type: Lock
  - type: Anchorable
    delay: 2
  - type: StaticPrice
    price: 80
  - type: ResistLocker
  - type: Transform
    noRot: true
  - type: Sprite
    noRot: true
    sprite: Structures/Storage/suit_storage.rsi
    layers:
    - state: base
    - state: door
      map: ["enum.StorageVisualLayers.Door"]
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
    - state: locked
      map: ["enum.LockVisualLayers.Lock"]
      shader: unshaded
  - type: MovedByPressure
  - type: DamageOnHighSpeedImpact
    damage:
      types:
        Blunt: 5
    soundHit:
      collection: MetalThud
  - type: InteractionOutline
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.48,0.25,0.48"
        density: 350
        mask:
        - MachineMask
        layer:
        - MachineLayer
  - type: EntityStorage
  - type: ContainerContainer
    containers:
      entity_storage: !type:Container
  - type: Weldable
  - type: PlaceableSurface
    placeCentered: true
    isPlaceable: false
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 400
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
          params:
            volume: -6
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 1
  - type: Appearance
  - type: EntityStorageVisuals
    stateBase: base
    stateDoorOpen: base
    stateDoorClosed: door
  - type: LockVisuals
