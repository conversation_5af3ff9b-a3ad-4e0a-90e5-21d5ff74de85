# Numbers for armor here largely taken from /tg/.
# NOTE: Half of the kind of armor you're probably thinking of is in vests.yml. These should probably be merged some day.

- type: entity
  parent: [ClothingOuterBaseMedium, AllowSuitStorageClothing]
  id: ClothingOuterArmorBasic
  name: "бронежилет"
  description: "Стандартний бронежилет типу I, що забезпечує гідний захист від більшості видів ушкоджень."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Armor/security.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Armor/security.rsi
  - type: Armor #Based on /tg/ but slightly compensated to fit the fact that armor stacks in SS14.
    modifiers:
      coefficients:
        Blunt: 0.70
        Slash: 0.70
        Piercing: 0.70 #Can save you, but bullets will still hurt. Will take about 10 shots from a Viper before critting, as opposed to 7 while unarmored and 16~ with a bulletproof vest.
        Heat: 0.80
  - type: ExplosionResistance
    damageCoefficient: 0.90
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:<PERSON>ysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 55
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

#Alternate / slim basic armor vest
- type: entity
  parent: ClothingOuterArmorBasic
  id: ClothingOuterArmorBasicSlim
  name: "бронежилет"
  suffix: slim
  description: "Тонкий бронежилет типу I, що забезпечує гідний захист від більшості видів пошкоджень."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Armor/security_slim.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Armor/security_slim.rsi

- type: entity
  parent: [ClothingOuterBaseLarge, AllowSuitStorageClothing]
  id: ClothingOuterArmorRiot
  name: "костюм придушення бунтів"
  description: "Костюм з напівгнучкого полікарбонатного бронежилета з важкою підкладкою для захисту від ближнього бою. Ідеально підходить для боротьби з правопорушниками на вокзалі."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Armor/riot.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Armor/riot.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.4
        Slash: 0.4
        Piercing: 0.7
        Heat: 0.9
        Caustic: 0.9
  - type: ExplosionResistance
    damageCoefficient: 0.9
  - type: GroupExamine

- type: entity
  parent: [ClothingOuterBaseLarge, AllowSuitStorageClothing, ClothingOuterEVASuitBase]
  id: ClothingOuterArmorSwat
  name: "костюм спецназу"
  description: "Складений з напівгнучкого полікарбонату, армованих матеріалів та інтегрованого балістичного покриття, він забезпечує винятковий захист від ближнього бою, куль та загроз навколишнього середовища, гарантуючи, що офіцери залишатимуться боєздатними в найсуворіших умовах."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Armor/swat.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Armor/swat.rsi
  - type: PressureProtection
    highPressureMultiplier: 0.45
    lowPressureMultiplier: 1000
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.6
        Slash: 0.6
        Piercing: 0.6
        Heat: 0.85
        Radiation: 0.75
        Caustic: 0.85
  - type: ExplosionResistance
    damageCoefficient: 0.50
  - type: ClothingSpeedModifier # Burdensome for running, but perfect for speedy gliding, even though running is still probably better
    walkModifier: 0.9
    sprintModifier: 0.7
  - type: GroupExamine

- type: entity
  parent: ClothingOuterArmorBasic
  id: ClothingOuterArmorBulletproof
  name: "важкий бронежилет"
  description: "Важкий бронежилет III типу, що забезпечує чудовий захист від куль, але малоефективний проти будь-чого іншого."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Armor/bulletproof.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Armor/bulletproof.rsi
  - type: Armor
    modifiers:
      flatReductions:
        Piercing: 4
      coefficients:
        Blunt: 0.95
        Slash: 0.95
        Piercing: 0.5
        Heat: 0.95

- type: entity
  parent: [ClothingOuterBaseLarge, AllowSuitStorageClothing ]
  id: ClothingOuterArmorRaid
  name: "рейдерський костюм синдикату"
  description: "Дещо гнучкий і добре броньований костюм з потужним наплічним ліхтарем, виготовлений у фірмовому криваво-червоному кольорі Gorlex Marauder, він не захищає власника від космосу."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Armor/syndie-raid.rsi
    layers:
    - state: icon
    - state: light-overlay
      visible: false
      shader: unshaded
  - type: Clothing
    sprite: Clothing/OuterClothing/Armor/syndie-raid.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.35
        Slash: 0.35
        Piercing: 0.35
        Heat: 0.35
        Caustic: 0.5
  - type: ExplosionResistance
    damageCoefficient: 0.35
  - type: ClothingSpeedModifier
    walkModifier: 0.9
    sprintModifier: 0.9
  #Shoulder mounted flashlight
  - type: ToggleableLightVisuals
    spriteLayer: light
    clothingVisuals:
      outerClothing:
      - state: equipped-OUTERCLOTHING-light
        shader: unshaded
  - type: Appearance
  - type: HandheldLight
    addPrefix: false
    blinkingBehaviourId: blinking
    radiatingBehaviourId: radiating
  - type: PointLight
    enabled: false
    color: "#80ff80"
    radius: 5
    energy: 2
    mask: /Textures/Effects/LightMasks/cone.png
    autoRot: true
    netsync: false
  - type: LightBehaviour
    behaviours:
      - !type:FadeBehaviour
        id: radiating
        interpolate: Linear
        maxDuration: 2.0
        startValue: 3.0
        endValue: 2.0
        isLooped: true
        reverseWhenFinished: true
      - !type:PulseBehaviour
        id: blinking
        interpolate: Nearest
        maxDuration: 1.0
        minValue: 0.1
        maxValue: 2.0
        isLooped: true
  - type: Battery
    maxCharge: 600
    startingCharge: 600
  - type: BatterySelfRecharger
    autoRecharge: true
    autoRechargeRate: 2

- type: entity
  parent: ClothingOuterArmorBasic
  id: ClothingOuterArmorReflective
  name: "відбиваючий жилет"
  description: "Бронежилет із вдосконаленим екрануванням для захисту від енергетичної зброї."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Armor/armor_reflec.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Armor/armor_reflec.rsi
  - type: Armor
    modifiers:
      flatReductions:
        Heat: 3
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.9
        Heat: 0.7
  - type: Reflect
    reflectProb: 0.33
    innate: true # armor grants a passive shield that does not require concentration to maintain
    reflects:
      - Energy

- type: entity
  parent: ClothingOuterBaseLarge
  id: ClothingOuterArmorCult
  name: "обладунки аколіта"
  description: "Зловісний на вигляд культовий обладунок, зроблений з кісток."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Armor/cult_armour.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Armor/cult_armour.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.5
        Slash: 0.5
        Piercing: 0.6
        Heat: 0.5
  - type: GroupExamine

- type: entity
  parent: [ClothingOuterBaseLarge, AllowSuitStorageClothing]
  id: ClothingOuterArmorHeavy
  name: "важкий бронекостюм"
  description: "Сильно броньований костюм, що захищає від надмірних пошкоджень."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Armor/heavy.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Armor/heavy.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.2
        Slash: 0.2
        Piercing: 0.2
        Heat: 0.5
        Radiation: 0
        Caustic: 0.75
  - type: GroupExamine
  - type: ClothingRequiredStepTriggerImmune
    slots: WITHOUT_POCKET
  - type: Tag
    tags:
      - FullBodyOuter

- type: entity
  parent: [ClothingOuterBaseLarge, AllowSuitStorageClothing]
  id: ClothingOuterArmorHosSamurai
  name: "тоусей гусоку даймьо"
  description: "Тоусей гусоку, виготовлений для Голови Служби Безпеки, в першу чергу для церемоніального використання, з відсутніми кишенями та рукавицями для полегшення використання вогнепальної зброї, але все ще забезпечує належний захист для Голови Служби Безпеки. Відмінно поєднується з піхвами для катани або тунчхоном!"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Armor/hosamurai.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Armor/hosamurai.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.65
        Slash: 0.65
        Piercing: 0.65
        Heat: 0.65
        Caustic: 0.75
  - type: GroupExamine
  - type: ClothingRequiredStepTriggerImmune
    slots: WITHOUT_POCKET
  - type: Tag
    tags:
      - FullBodyOuter

- type: entity
  parent: ClothingOuterArmorHeavy
  id: ClothingOuterArmorHeavyGreen
  name: "зелений важкий бронекостюм"
  description: "Важкий броньований костюм із зеленими акцентами, що захищає від надмірних пошкоджень."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Armor/heavygreen.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Armor/heavygreen.rsi

- type: entity
  parent: ClothingOuterArmorHeavy
  id: ClothingOuterArmorHeavyRed
  name: "червоний важкий бронекостюм"
  description: "Сильно броньований костюм з червоними акцентами, що захищає від надмірних пошкоджень."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Armor/heavyred.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Armor/heavyred.rsi

- type: entity
  parent: [ClothingOuterArmorHeavy, ClothingOuterWizardBaseArmor] # Goob edit
  id: ClothingOuterArmorMagusblue
  name: "обладунки магуса (сині)"
  description: "Синій бронекостюм, який добре захищає. Не рахуються, як роба мага" # Goob edit
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Armor/magusblue.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Armor/magusblue.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.5
        Slash: 0.5
        Piercing: 0.5
        Heat: 0.5
        Radiation: 0.1
        Caustic: 0.1
  - type: ClothingSpeedModifier # Goobstation
    walkModifier: 1.0
    sprintModifier: 1.0
  - type: Tag # Goobstation
    tags:
      - WhitelistChameleon
      - SyringeArmor

- type: entity
  parent: ClothingOuterArmorMagusblue # Goob edit
  id: ClothingOuterArmorMagusred
  name: "обладунки магуса (червоні)"
  description: "Червоний бронежилет, який забезпечує хороший захист. Не рахуються, як роба мага" # Goob edit
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Armor/magusred.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Armor/magusred.rsi

- type: entity
  parent: [ClothingOuterBaseLarge, AllowSuitStorageClothing]
  id: ClothingOuterArmorCaptainCarapace
  name: "капітанський панцир"
  description: "Броньований нагрудник, який забезпечує захист, водночас пропонуючи максимальну мобільність та гнучкість. Видається лише найкращим на станції."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Armor/captain_carapace.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Armor/captain_carapace.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.5
        Slash: 0.5
        Piercing: 0.6
        Heat: 0.5
        Caustic: 0.9
  - type: ClothingSpeedModifier
    walkModifier: 1.0
    sprintModifier: 1.0
  - type: HeldSpeedModifier
  - type: ExplosionResistance
    damageCoefficient: 0.65
  - type: GroupExamine

- type: entity
  parent: ClothingOuterBaseLarge
  id: ClothingOuterArmorChangeling
  name: "хітинова броня"
  description: "Накачує тіло мінливого на всепоглинаючу хітинову масу обладунків."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Armor/lingarmor.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Armor/lingarmor.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.5
        Slash: 0.5
        Piercing: 0.5
        Heat: 0.9
        Radiation: 0.8
  - type: ClothingSpeedModifier
    walkModifier: 0.7
    sprintModifier: 0.65
  - type: HeldSpeedModifier
  - type: ExplosionResistance
    damageCoefficient: 0.5
  - type: GroupExamine
  - type: ClothingRequiredStepTriggerImmune
    slots: WITHOUT_POCKET
  - type: Tag
    tags:
      - FullBodyOuter

- type: entity
  parent: ClothingOuterBaseLarge
  id: ClothingOuterArmorBone
  name: "кістяна броня"
  description: "Сидить на вас, як друга шкіра."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Armor/bone_armor.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Armor/bone_armor.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.6
        Slash: 0.8
        Piercing: 0.4
  - type: ClothingSpeedModifier
    walkModifier: 0.8
  - type: HeldSpeedModifier
  - type: ExplosionResistance
    damageCoefficient: 0.4
  - type: GroupExamine
  - type: Construction
    graph: BoneArmor
    node: armor
  - type: ClothingRequiredStepTriggerImmune
    slots: WITHOUT_POCKET

- type: entity
  parent: ClothingOuterBaseLarge
  id: ClothingOuterArmorPodWars
  name: "броня Ironclad II"
  description: "Перероблений костюм бронежилета Ironclad II, реліквія часів капсульних війн."
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Armor/podwars_armor.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Armor/podwars_armor.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.5
        Slash: 0.5
        Piercing: 0.6
        Heat: 0.5
  - type: GroupExamine
  - type: ClothingRequiredStepTriggerImmune
    slots: WITHOUT_POCKET

- type: entity
  parent: [ClothingOuterBaseLarge, AllowSuitStorageClothing]
  id: ClothingOuterArmorCaptainDogi
  name: "броньований догі сьогуна"
  description: "Броньований догі, виготовлений з тонких, але міцних тканин і посилений екзотичним сплавом, щоб зберегти його власника захищеним і стильним у бою та при виконанні обов'язків. Слава Сьогуну!"
  components:
  - type: Sprite
    sprite: Clothing/OuterClothing/Armor/captaindogi.rsi
  - type: Clothing
    sprite: Clothing/OuterClothing/Armor/captaindogi.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.5
        Slash: 0.5
        Piercing: 0.6
        Heat: 0.5
        Caustic: 0.9
  - type: ClothingSpeedModifier
    walkModifier: 1.0
    sprintModifier: 1.0
  - type: HeldSpeedModifier
  - type: ExplosionResistance
    damageCoefficient: 0.65
  - type: GroupExamine
