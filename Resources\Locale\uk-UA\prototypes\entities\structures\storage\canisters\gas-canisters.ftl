ent-GasCanister = Газова каністра
    .desc = Балон, який може містити будь-який тип газу. Може бути приєднаний до з'єднувальних портів за допомогою гайкового ключа.

ent-StorageCanister = Балон для зберігання
    .desc = { ent-GasCanister.desc }

ent-AirCanister = Каністра з повітрям
    .desc = Каністра, який може містити будь-який тип газу. Цей призначений для зберігання повітряної суміші. Може бути приєднаний до з'єднувальних портів за допомогою гайкового ключа.

ent-OxygenCanister = Каністра з киснем
    .desc = Каністра, який може містити будь-який тип газу. Цей призначений для зберігання кисню. Може бути приєднаний до з'єднувальних портів за допомогою гайкового ключа.

ent-LiquidOxygenCanister = Каністра з рідким киснем
    .desc = Каністра, який може містити будь-який тип газу. Цей призначений для зберігання рідкого кисню. Може бути приєднаний до з'єднувальних портів за допомогою гайкового ключа.

ent-NitrogenCanister = Каністра з азотом
    .desc = Каністра, який може містити будь-який тип газу. Цей призначений для зберігання азоту. Може бути приєднаний до з'єднувальних портів за допомогою гайкового ключа.

ent-LiquidNitrogenCanister = Каністра з рідким азотом
    .desc = Каністра, який може містити будь-який тип газу. Цей призначений для зберігання рідкого азоту. Може бути приєднаний до з'єднувальних портів за допомогою гайкового ключа.

ent-CarbonDioxideCanister = Каністра з вуглекислим газом
    .desc = Каністра, який може містити будь-який тип газу. Цей призначений для зберігання вуглекислого газу. Може бути приєднаний до з'єднувальних портів за допомогою гайкового ключа.

ent-LiquidCarbonDioxideCanister = Каністра з рідким вуглекислим газом
    .desc = Каністра, який може містити будь-який тип газу. Цей призначений для зберігання рідкого вуглекислого газу. Може бути приєднаний до з'єднувальних портів за допомогою гайкового ключа.

ent-PlasmaCanister = Каністра з плазмою
    .desc = Каністра, який може містити будь-який тип газу. Цей призначений для зберігання плазми. Може бути приєднаний до з'єднувальних портів за допомогою гайкового ключа.

ent-TritiumCanister = Каністра з тритієм
    .desc = Каністра, який може містити будь-який тип газу. Цей призначений для зберігання тритію. Може бути приєднаний до з'єднувальних портів за допомогою гайкового ключа.

ent-WaterVaporCanister = Каністра з водяною парою
    .desc = Каністра, який може містити будь-який тип газу. Цей призначений для зберігання водяної пари. Може бути приєднаний до з'єднувальних портів за допомогою гайкового ключа.

ent-AmmoniaCanister = Каністра з аміаком
    .desc = Каністра, який може містити будь-який тип газу. Цей призначений для зберігання аміаку. Може бути приєднаний до з'єднувальних портів за допомогою гайкового ключа.

ent-NitrousOxideCanister = Каністра з закисом азоту
    .desc = Каністра, який може містити будь-який тип газу. Цей призначений для зберігання закису азоту. Може бути приєднаний до з'єднувальних портів за допомогою гайкового ключа.

ent-FrezonCanister = Каністра з фрезоном
    .desc = Охолоджувач з легкими галюциногенними властивостями. Дійте обережно.

ent-GasCanisterBrokenBase = Зламаний газова каністра
    .desc = Зламаний газовий балон. Ще не марний, оскільки може бути використаний для отримання високоякісних матеріалів.

ent-StorageCanisterBroken = { ent-GasCanisterBrokenBase }
    .desc = { ent-GasCanisterBrokenBase.desc }

ent-AirCanisterBroken = { ent-GasCanisterBrokenBase }
    .desc = { ent-GasCanisterBrokenBase.desc }

ent-OxygenCanisterBroken = { ent-GasCanisterBrokenBase }
    .desc = { ent-GasCanisterBrokenBase.desc }

ent-NitrogenCanisterBroken = { ent-GasCanisterBrokenBase }
    .desc = { ent-GasCanisterBrokenBase.desc }

ent-CarbonDioxideCanisterBroken = { ent-GasCanisterBrokenBase }
    .desc = { ent-GasCanisterBrokenBase.desc }

ent-PlasmaCanisterBroken = { ent-GasCanisterBrokenBase }
    .desc = { ent-GasCanisterBrokenBase.desc }

ent-TritiumCanisterBroken = { ent-GasCanisterBrokenBase }
    .desc = { ent-GasCanisterBrokenBase.desc }

ent-WaterVaporCanisterBroken = { ent-GasCanisterBrokenBase }
    .desc = { ent-GasCanisterBrokenBase.desc }

ent-AmmoniaCanisterBroken = { ent-GasCanisterBrokenBase }
    .desc = { ent-GasCanisterBrokenBase.desc }

ent-NitrousOxideCanisterBroken = { ent-GasCanisterBrokenBase }
    .desc = { ent-GasCanisterBrokenBase.desc }

ent-FrezonCanisterBroken = { ent-GasCanisterBrokenBase }
    .desc = { ent-GasCanisterBrokenBase.desc }
