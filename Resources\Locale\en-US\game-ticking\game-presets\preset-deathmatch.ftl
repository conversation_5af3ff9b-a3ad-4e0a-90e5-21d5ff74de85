death-match-title = DeathMatch
death-match-description = Kill anything that moves! Normal roleplay need not apply. The first to 31 points wins!

death-match-name-player = [bold]{$name}[/bold] ([italic]{$username}[/italic])
death-match-name-npc = [bold]{$name}[/bold]
death-match-assist = {$primary}, assisted by {$secondary},

death-match-kill-callout-0 = {CAPITALIZE($killer)} murdered {$victim}!
death-match-kill-callout-1 = {CAPITALIZE($killer)} killed {$victim}!
death-match-kill-callout-2 = {CAPITALIZE($killer)} fragged {$victim}!
death-match-kill-callout-3 = {CAPITALIZE($killer)} demolished {$victim}!
death-match-kill-callout-4 = {CAPITALIZE($killer)} turned {$victim} into lunch meat!
death-match-kill-callout-5 = {CAPITALIZE($killer)} blitzed {$victim}!
death-match-kill-callout-6 = {CAPITALIZE($killer)} flipped {$victim} upside down and spun 'em!
death-match-kill-callout-7 = {CAPITALIZE($killer)} messed up {$victim}!
death-match-kill-callout-8 = {CAPITALIZE($killer)} sent {$victim} to hell!
death-match-kill-callout-9 = {CAPITALIZE($killer)} danced on {$victim}'s grave!
death-match-kill-callout-10 = {CAPITALIZE($killer)} obliterated {$victim}!
death-match-kill-callout-11 = {CAPITALIZE($killer)} robusted {$victim}!
death-match-kill-callout-12 = {CAPITALIZE($killer)} combined toolbox and {$victim} to create corpse!
death-match-kill-callout-13 = {CAPITALIZE($killer)} made {$victim} bite the dust!
death-match-kill-callout-14 = {CAPITALIZE($killer)} posted a cringe compilation of {$victim}!
death-match-kill-callout-15 = {CAPITALIZE($killer)} saw {$victim} post their full-body OC!
death-match-kill-callout-16 = {CAPITALIZE($killer)} Doom (1993)'ed {$victim}!
death-match-kill-callout-17 = {CAPITALIZE($killer)} humiliated {$victim}!
death-match-kill-callout-18 = {CAPITALIZE($killer)} removed {$victim}'s flashdrive while it was being used!
death-match-kill-callout-19 = {CAPITALIZE($killer)} deleted System32 from {$victim}'s PC!
death-match-kill-callout-20 = {CAPITALIZE($killer)} bwoinked {$victim}!
death-match-kill-callout-21 = {CAPITALIZE($killer)} voted {$victim} off the island!
death-match-kill-callout-22 = {CAPITALIZE($killer)} voted {$victim} for acting sus!
death-match-kill-callout-23 = {CAPITALIZE($killer)} forced {$victim} to code for SS14!
death-match-kill-callout-24 = {CAPITALIZE($killer)} forced {$victim} to code for OpenDream!
death-match-kill-callout-25 = {CAPITALIZE($killer)} forced {$victim} to code for BYOND!
death-match-kill-callout-26 = {CAPITALIZE($killer)} 1984'ed {$victim}!
death-match-kill-callout-27 = {CAPITALIZE($killer)} express-shipped {$victim} to God!
death-match-kill-callout-28 = {CAPITALIZE($killer)} taunt killed {$victim}!
death-match-kill-callout-29 = {CAPITALIZE($killer)} said {$victim} had a nice cut, G!
death-match-kill-callout-30 = {CAPITALIZE($killer)} shuffled on {$victim}'s grave!
death-match-kill-callout-31 = {CAPITALIZE($killer)} pushed {$victim} down the stairs!
death-match-kill-callout-32 = {CAPITALIZE($killer)} enacted the bite of '87 on {$victim}!
death-match-kill-callout-33 = {CAPITALIZE($killer)} saw {$victim} post on reddit!
death-match-kill-callout-34 = {CAPITALIZE($killer)} threw {$victim} to the admin team!
death-match-kill-callout-35 = {CAPITALIZE($killer)} weh'ed {$victim}!
death-match-kill-callout-36 = {CAPITALIZE($killer)} turned {$victim} into an SS13 remake!
death-match-kill-callout-37 = {CAPITALIZE($killer)} forced {$victim} to play Xonotic!
death-match-kill-callout-38 = {CAPITALIZE($killer)} sent {$victim} to Brazil!
death-match-kill-callout-39 = {CAPITALIZE($killer)} epicly hacked {$victim}!
death-match-kill-callout-40 = {CAPITALIZE($killer)} closed {$victim}'s PR!
death-match-kill-callout-41 = {CAPITALIZE($killer)} saw {$victim} merge cringe on master!
death-match-kill-callout-42 = {CAPITALIZE($killer)} witnessed {$victim} sergalpost on main!
death-match-kill-callout-43 = {CAPITALIZE($killer)} did not gently the {$victim}!
death-match-kill-callout-44 = {CAPITALIZE($killer)} schmoved on {$victim}!
death-match-kill-callout-45 = {CAPITALIZE($killer)} cunked {$victim}!
death-match-kill-callout-46 = {CAPITALIZE($killer)} shook {$victim} before drinking!
death-match-kill-callout-47 = {CAPITALIZE($killer)} drove drunk and hit {$victim}!
death-match-kill-callout-48 = {CAPITALIZE($killer)} turned {$victim} into a marketable plush!
death-match-kill-callout-49 = {CAPITALIZE($killer)} gave {$victim} a reminder of their mortality!
death-match-kill-callout-50 = {CAPITALIZE($killer)} ratio'd {$victim}!
death-match-kill-callout-51 = {CAPITALIZE($killer)} ctrl-alt-delete'd {$victim}!
death-match-kill-callout-52 = {CAPITALIZE($killer)} bonked {$victim}!
death-match-kill-callout-53 = {CAPITALIZE($killer)} landed a random crit on {$victim}!
death-match-kill-callout-54 = {CAPITALIZE($killer)} taught {$victim} a valuable lesson!
death-match-kill-callout-55 = {CAPITALIZE($killer)} hit a home run on {$victim}!
death-match-kill-callout-56 = {CAPITALIZE($killer)} dunked on {$victim}!
death-match-kill-callout-57 = {CAPITALIZE($killer)} styled on {$victim}!
death-match-kill-callout-58 = {CAPITALIZE($killer)} said a rude word to {$victim}!
death-match-kill-callout-59 = {CAPITALIZE($killer)} sent hate-mail to {$victim}!
death-match-kill-callout-60 = {CAPITALIZE($killer)} tripped {$victim} down the stairs!
death-match-kill-callout-env-0 = {CAPITALIZE($victim)} lost a point!
death-match-kill-callout-env-1 = {CAPITALIZE($victim)} got humiliated!
death-match-kill-callout-env-2 = {CAPITALIZE($victim)} just looked like an idiot!
death-match-kill-callout-env-3 = {CAPITALIZE($victim)} suffered a skill issue!
death-match-kill-callout-env-4 = {CAPITALIZE($victim)} looked extremely dumb!
death-match-kill-callout-env-5 = {CAPITALIZE($victim)} put themselves out of their misery!
death-match-kill-callout-env-6 = {CAPITALIZE($victim)} got bored of life!
death-match-kill-callout-env-7 = {CAPITALIZE($victim)} didn't try very hard!
death-match-kill-callout-env-8 = {CAPITALIZE($victim)} took out the trash themselves!
death-match-kill-callout-env-9 = {CAPITALIZE($victim)} looked extremely dumb!
death-match-kill-callout-env-10 = {CAPITALIZE($victim)} clowned around!
