- type: entity
  id: BaseMech
  save: false
  abstract: true
  components:
  - type: MobMover
  - type: Mech
  - type: MechAir
  - type: AirFilter
    # everything except oxygen and nitrogen
    gases:
    - CarbonDioxide
    - Plasma
    - Tritium
    - WaterVapor
    - Ammonia
    - NitrousOxide
    - Frezon
    #- Helium3 TODO: fusion
    # remove everything except oxygen to maintain oxygen ratio
    overflowGases:
    - Nitrogen
    - CarbonDioxide
    - Plasma
    - Tritium
    - WaterVapor
    - Ammonia
    - NitrousOxide
    - Frezon
    #- Helium3 TODO: fusion
  - type: AirIntake
  # for intake and filter to work
  - type: AtmosDevice
    requireAnchored: false
    joinSystem: true
  - type: DoAfter
  - type: Repairable
    fuelCost: 25
    doAfterDelay: 10
  - type: UserInterface
    interfaces:
      enum.MechUiKey.Key:
        type: MechBoundUserInterface
  - type: MeleeWeapon
    canWideSwing: false
    hidden: true
    attackRate: 1.3333
    damage:
      types:
        Blunt: 25 #thwack
        Structural: 20
    soundHit:
      collection: MetalThud
  - type: Puller
    needsHands: false
  - type: InputMover
  - type: InteractionOutline
  - type: MovementSpeedModifier
    baseWalkSpeed: 1
    baseSprintSpeed: 2
  - type: Tag
    tags:
    - DoorBumpOpener
    - FootstepSound
  - type: Pullable
  - type: Physics
    bodyType: KinematicController
  - type: Clickable
  - type: WiresPanel
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.45
        density: 21390 # 15 Tonnes
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Appearance
  - type: ContainerContainer
    containers:
      mech-pilot-slot: !type:ContainerSlot
      mech-equipment-container: !type:Container
      mech-battery-slot: !type:ContainerSlot
      paper_label: !type:ContainerSlot # Goobstation
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: LightArmor # Goobstation
  - type: FootstepModifier
    footstepSoundCollection:
      path: /Audio/Mecha/mechmove03.ogg
  - type: GuideHelp
    guides:
    - Robotics
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 1000 # Goobstation
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:ChangeConstructionNodeBehavior
        node: start
      - !type:DoActsBehavior
        acts: ["Destruction"]
  # Goobstation Change Start
  - type: UnpoweredFlashlight # Goobstation mech lights for all mechs its unpowered to match hardsuits
  - type: PointLight
    enabled: false
    softness: 5
    autoRot: true
    mask: /Textures/Effects/LightMasks/cone.png
    radius: 7
    energy: 3
    netsync: false
  - type: GenericVisualizer
    visuals:
      enum.PaperLabelVisuals.HasLabel:
        enum.PaperLabelVisuals.Layer:
          True: { visible: true }
          False: { visible: false }
      enum.PaperLabelVisuals.LabelType:
        enum.PaperLabelVisuals.Layer:
          Paper: { state: paper }
          Bounty: { state: bounty }
          CaptainsPaper: { state: captains_paper }
          Invoice: { state: invoice }
  - type: PaperLabel
    labelSlot:
      insertVerbText: Attach Label
      ejectVerbText: Remove Label
      whitelist:
        components:
        - Paper
      blacklist:
        tags:
        - Book
  - type: StaticPrice
    price: 450
  # Goobstation Change End

- type: entity
  id: MechRipley
  parent: [ BaseMech, IndustrialMech ] # Goobstation
  name: "Ripley APLU"
  description: "Універсальний і легкоброньований, Ripley стане в нагоді практично для будь-якого важкого сценарію роботи. \"APLU\" розшифровується як Автономний зарядний пристрій."
  components:
  - type: Sprite
    drawdepth: Mobs
    noRot: true
    sprite: Objects/Specific/Mech/mecha.rsi
    layers:
    - map: [ "enum.MechVisualLayers.Base" ]
      state: ripley
  - type: FootstepModifier
    footstepSoundCollection:
      path: /Audio/Mecha/sound_mecha_powerloader_step.ogg
  - type: Mech
    baseState: ripley
    openState: ripley-open
    brokenState: ripley-broken
    mechToPilotDamageMultiplier: 0.75
    pilotWhitelist:
      components:
        - HumanoidAppearance
  - type: MeleeWeapon
    hidden: true
    attackRate: 1
    damage:
      types:
        Blunt: 14 #intentionally shit so people realize that going into combat with the ripley is usually a bad idea.
  - type: MovementSpeedModifier
    baseWalkSpeed: 2.25
    baseSprintSpeed: 3.6

- type: entity
  id: MechRipleyBattery
  parent: MechRipley
  suffix: Battery
  components:
  - type: ContainerFill
    containers:
      mech-battery-slot:
      - PowerCellHigh

- type: entity
  parent: [ BaseMech, SpecialMech ] # Goobstation
  id: MechHonker
  name: "H.O.N.K."
  description: "Цей екзокостюм, виготовлений компанією \"Tyranny of Honk, INC\", спроектований як важка клоунська опора. Використовується для поширення веселощів і радості життя. ХОНК!"
  components:
  - type: Sprite
    drawdepth: Mobs
    noRot: true
    sprite: Objects/Specific/Mech/mecha.rsi
    layers:
    - map: [ "enum.MechVisualLayers.Base" ]
      state: honker
  - type: FootstepModifier
    footstepSoundCollection:
      collection: FootstepClown
  - type: Mech
    baseState: honker
    openState: honker-open
    brokenState: honker-broken
    mechToPilotDamageMultiplier: 0.5
    airtight: true # Goobstation - Space Honk is real.
    pilotWhitelist:
      components:
      - HumanoidAppearance

- type: entity
  parent: MechHonker
  id: MechHonkerBattery
  suffix: Battery
  components:
  - type: ContainerFill
    containers:
      mech-battery-slot:
      - PowerCellHigh

- type: entity
  parent: [ BaseMech, SmallMech ] # Goobstation
  id: MechHamtr
  name: "HAMTR"
  description: "Експериментальний мех, який використовує інтерфейс \"мозок-комп'ютер\" для прямого підключення до мозку хом'яків."
  components:
  - type: Sprite
    drawdepth: Mobs
    noRot: true
    sprite: Objects/Specific/Mech/mecha.rsi
    layers:
    - map: [ "enum.MechVisualLayers.Base" ]
      state: hamtr
  - type: FootstepModifier
    footstepSoundCollection:
      path: /Audio/Mecha/sound_mecha_powerloader_step.ogg
  - type: Mech
    baseState: hamtr
    openState: hamtr-open
    brokenState: hamtr-broken
    mechToPilotDamageMultiplier: 0.2
    maxEquipmentAmount: 2
    airtight: true
    pilotWhitelist:
      tags:
      - Hamster
  - type: MeleeWeapon
    hidden: true
    attackRate: 1.25
    damage:
      types:
        Blunt: 10 #thwack
        Structural: 2
  - type: MovementSpeedModifier
    baseWalkSpeed: 2.4
    baseSprintSpeed: 3.7

- type: entity
  parent: MechHamtr
  id: MechHamtrBattery
  suffix: Battery
  components:
  - type: ContainerFill
    containers:
      mech-battery-slot:
      - PowerCellHigh

# Vim!!!!!!!

- type: entity
  parent: BaseMech
  id: MechVim
  name: "Вім"
  description: "Мініатюрний екзокостюм від Nanotrasen, розроблений, щоб незамінні домашні улюбленці станції могли прожити трохи довше."
  components:
  - type: Sprite
    drawdepth: Mobs
    noRot: true
    sprite: Objects/Specific/Mech/mecha.rsi
    layers:
    - map: [ "enum.MechVisualLayers.Base" ]
      state: vim
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.15
        density: 80
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: FootstepModifier
    footstepSoundCollection:
      path: /Audio/Mecha/mechmove03.ogg
      params:
        volume: -10
  - type: Mech
    baseState: vim
    openState: vim-open
    brokenState: vim-broken
    maxEquipmentAmount: 0
    # keep mouse safe
    mechToPilotDamageMultiplier: 0.1
    airtight: true
    pilotWhitelist:
      tags:
      - VimPilot
  - type: MeleeWeapon
    hidden: true
    attackRate: 1.25
    damage:
      types:
        Blunt: 10 #thwack
        Structural: 2
  - type: MovementSpeedModifier
    baseWalkSpeed: 2.25
    baseSprintSpeed: 3.6
  # TOOD: buzz / chime actions
  # TODO: builtin flashlight

- type: entity
  parent: MechVim
  id: MechVimBattery
  suffix: Battery
  components:
  - type: ContainerFill
    containers:
      mech-battery-slot:
      - PowerCellHigh
