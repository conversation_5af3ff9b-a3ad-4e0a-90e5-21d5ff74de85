#These are startingGear definitions for the currently yet=to-be-added Ship VS. Ship gamemode.
#Refer to Nanotrasen.yml for additional comments.

#CREW
#Recruit
- type: startingGear
  id: RecruitSyndieGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitRecruitSyndie
    back: ClothingBackpackFilled
    shoes: ClothingShoesColorBlack
    gloves: ClothingHandsGlovesColorBlack
    id: PassengerPDA
    ears: ClothingHeadsetGrey
  innerClothingSkirt: ClothingUniformJumpsuitRecruitSyndie #Wearing a jumpskirt into combat is a little unfitting and silly, so there is no jumpskirt counterpart for any of the Ship VS. Ship suits.
  satchel: ClothingBackpackSatchelFilled
  duffelbag: ClothingBackpackDuffelFilled

#Repairman
- type: startingGear
  id: RepairmanSyndieGear
  equipment:
    head: ClothingHeadHatHardhatYellow
    jumpsuit: ClothingUniformJumpsuitRepairmanSyndie
    back: ClothingBackpackFilled #The regular industrial backpack looks really weird here, so I've opted for this instead for now. If a new one is never made, then make sure to make a prototype that has this with extended internals!
    shoes: ClothingShoesBootsWork
    gloves: ClothingHandsGlovesColorYellow #Should maybe still be in lockers - this is just so people know that they're there and a part of the outfit.
    id: EngineerPDA
    eyes: ClothingEyesGlassesMeson
    belt: ClothingBeltUtilityEngineering
    ears: ClothingHeadsetAltCommand #Should use the "alt" engineering headset sprite.
  innerClothingSkirt: ClothingUniformJumpsuitRepairmanSyndie
  satchel: ClothingBackpackSatchelFilled
  duffelbag: ClothingBackpackDuffelFilled

#Paramedic
- type: startingGear
  id: ParamedicSyndieGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitParamedicSyndie
    back: ClothingBackpackFilled #The default job backpack again looks way worse. Same case as the NT Paramedc and Syndicate repairman.
    shoes: ClothingShoesColorRed
    id: MedicalPDA
    ears: ClothingHeadsetMedical
    eyes: ClothingEyesHudMedical
    gloves: ClothingHandsGlovesLatex
    belt: ClothingBeltMedicalFilled
  innerClothingSkirt: ClothingUniformJumpsuitParamedicSyndie
  satchel: ClothingBackpackSatchelFilled
  duffelbag: ClothingBackpackDuffelFilled

#HEADS OF STAFF
#Chief Engineer
- type: startingGear
  id: ChiefEngineerSyndieGear
  equipment:
    head: ClothingHeadHatHardhatArmored
    jumpsuit: ClothingUniformJumpsuitChiefEngineerSyndie
    back: ClothingBackpackFilled #In a running theme, the default station job backpack still continues to look strange in comparison to the regular one. It's not as bad as on the syndicate engineer here, though.
    shoes: ClothingShoesBootsJack
    gloves: ClothingHandsGlovesCombat
    id: CEPDA
    eyes: ClothingEyesGlassesMeson
    ears: ClothingHeadsetAltCommand
    belt: ClothingBeltUtilityEngineering
  innerClothingSkirt: ClothingUniformJumpsuitChiefEngineerSyndie
  satchel: ClothingBackpackSatchelFilled
  duffelbag: ClothingBackpackDuffelFilled
