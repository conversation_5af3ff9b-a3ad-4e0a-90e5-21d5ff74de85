- type: entity
  id: RadiationCollector
  name: "колектор радіац<PERSON><PERSON>"
  suffix: Empty tank
  description: "Ма<PERSON><PERSON><PERSON>, яка збирає випромінювання і перетворює його на енергію. Для роботи потрібен плазмовий газ."
  placement:
    mode: SnapgridCenter
  components:
  - type: Clickable
  - type: InteractionOutline
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.45"
        density: 190
        mask:
        - MachineMask
        layer:
        - MachineLayer
  - type: Transform
    anchored: true
    noRot: true
  - type: Sprite
    sprite: Structures/Power/Generation/Singularity/collector.rsi
    snapCardinals: true
    layers:
      - state: ca_off
        map: ["enum.RadiationCollectorVisualLayers.Main"]
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.RadiationCollectorVisuals.TankInserted:
        tankInserted:
          False: { state: ca-tank, visible: false }
          True: { state: ca-tank, visible: true }
      enum.RadiationCollectorVisuals.PressureState:
        pressureLight:
          0: { state: ca-o0, shader: "unshaded" }
          1: { state: ca-o1, shader: "unshaded" }
          2: { state: ca-o2, shader: "unshaded" }
          3: { state: ca-o3, shader: "unshaded" }
  - type: AnimationPlayer
  - type: NodeContainer
    examinable: true
    nodes:
      input:
        !type:CableDeviceNode
        nodeGroupID: HVPower
  - type: PowerMonitoringDevice
    group: Generator
    loadNode: input
    collectionName: radiationCollector
    sprite: Structures/Power/Generation/Singularity/collector.rsi
    state: static
  - type: RadiationCollector
    chargeModifier: 7500
    radiationReactiveGases:
      - reactantPrototype: Plasma
        powerGenerationEfficiency: 1
        reactantBreakdownRate: 0.0001
  - type: RadiationReceiver
  - type: PowerSupplier
  - type: Anchorable
  - type: Rotatable
  - type: Pullable
  - type: GuideHelp
    guides: [ Singularity, Power ]
  - type: ContainerContainer
    containers:
      gas_tank: !type:ContainerSlot {}
  - type: ItemSlots
    slots:
      gas_tank:
        startingItem: PlasmaTank
        whitelist:
          components:
          - GasTank
  - type: UseDelay
    delay: 1

- type: entity
  id: RadiationCollectorNoTank
  suffix: No tank
  parent: RadiationCollector
  components:
  - type: ItemSlots
    slots:
      gas_tank:
        whitelist:
          components:
          - GasTank

- type: entity
  id: RadiationCollectorFullTank
  suffix: Filled tank
  parent: RadiationCollector
  components:
  - type: ItemSlots
    slots:
      gas_tank:
        startingItem: PlasmaTankFilled
        whitelist:
          components:
          - GasTank
