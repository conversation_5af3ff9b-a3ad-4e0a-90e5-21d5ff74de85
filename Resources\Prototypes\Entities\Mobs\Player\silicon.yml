# Be careful with these as they get removed on shutdown too!
- type: entity
  id: AiHeld
  description: "Компоненти, що додаються/видаляються з сутності, яка вставляється в ядро ШІ."
  categories: [ HideSpawnMenu ]
  components:
  - type: IntrinsicRadioReceiver
  - type: IntrinsicRadioTransmitter
    channels:
    - Binary
    - Common
    - Command
    - Engineering
    - Medical
    - Science
    - Security
    - Prison
    - Service
    - Supply
    - Justice
  - type: ActiveRadio
    channels:
    - Binary
    - Common
    - Command
    - Engineering
    - Medical
    - Science
    - Security
    - Prison
    - Service
    - Supply
    - Justice
  - type: IgnoreUIRange
  - type: StationAiHeld
  - type: StationAiOverlay
  - type: ShowElectrocutionHUD
  - type: ActionGrant
    actions:
    - ActionJumpToCore
    - ActionShowJobIcons
    - ActionSurvCameraLights
    - ActionAIViewLaws
  - type: UserInterface
    interfaces:
      enum.RadarConsoleUiKey.Key:
        type: RadarConsoleBoundUserInterface
      enum.CrewMonitoringUIKey.Key:
        type: CrewMonitoringBoundUserInterface
      enum.GeneralStationRecordConsoleKey.Key:
        type: GeneralStationRecordConsoleBoundUserInterface
      enum.SiliconLawsUiKey.Key:
        type: SiliconLawBoundUserInterface
      enum.CommunicationsConsoleUiKey.Key:
        type: CommunicationsConsoleBoundUserInterface
  - type: IntrinsicUI
    uis:
      enum.RadarConsoleUiKey.Key:
        toggleAction: ActionAGhostShowRadar
      enum.CrewMonitoringUIKey.Key:
        toggleAction: ActionAGhostShowCrewMonitoring
      enum.GeneralStationRecordConsoleKey.Key:
        toggleAction: ActionAGhostShowStationRecords
      enum.CommunicationsConsoleUiKey.Key:
        toggleAction: ActionAGhostShowCommunications
  - type: CrewMonitoringConsole
  - type: GeneralStationRecordConsole
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: CrewMonitor
    transmitFrequencyId: ShuttleTimer
  - type: RadarConsole
    followEntity: false
  - type: CommunicationsConsole
    canShuttle: false
    title: comms-console-announcement-title-station-ai
    color: "#5ed7aa"
  - type: HolographicAvatar
    layerData:
    - sprite: Mobs/Silicon/station_ai.rsi
      state: default
  - type: ShowJobIcons

- type: entity
  id: AiHeldIntellicard
  description: "Компоненти, додані/вилучені з сутності, яка вставляється в Intellicard."
  categories: [ HideSpawnMenu ]
  components:
  - type: IntrinsicRadioReceiver
  - type: IntrinsicRadioTransmitter
    channels:
    - Binary
  - type: ActiveRadio
    channels:
    - Binary
    - Common
  - type: ActionGrant
    actions:
    - ActionAIViewLaws
  - type: UserInterface
    interfaces:
      enum.SiliconLawsUiKey.Key:
        type: SiliconLawBoundUserInterface

# Ai
- type: entity
  id: AiHolder
  abstract: true
  description: "Керує взаємодією ШІ між голографічними картками + ядрами ШІ"
  components:
  - type: ItemSlots
  - type: StationAiHolder
    slot:
      name: station-ai-mind-slot
      locked: false
      disableEject: true
      whitelist:
        tags:
        - StationAi
  - type: ContainerContainer
    containers:
      station_ai_mind_slot: !type:ContainerSlot
        # Load-bearing.
        # The issue is verbs check for same transparent container.
        # The alternative is you add a bunch of events trying to override it; we don't even really need the container functionality
        # anyway it's just a quality of life thing.
        showEnts: True

# Boards
- type: entity
  id: AsimovCircuitBoard
  parent: BaseElectronics
  name: "юридичний факультет (Азімов)"
  description: "Електронна плата з набором законів Азімова."
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: Asimov

- type: entity
  id: AugustineCircuitBoard
  parent: BaseElectronics
  name: "правнича колегія (Августин)"
  description: "Електронна плата з набором законів Августина."
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: Augustine

- type: entity
  id: CrewsimovCircuitBoard
  parent: BaseElectronics
  name: "правнича колегія (Крюсімов)"
  description: "Електронна плата з набором законів Крюсімова."
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: Crewsimov

- type: entity
  id: CorporateCircuitBoard
  parent: BaseElectronics
  name: "друкована плата (корпоративна)"
  description: "Електронна плата, що містить пакет корпоративного законодавства."
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: Corporate

- type: entity
  id: NTDefaultCircuitBoard
  parent: BaseElectronics
  name: "друкована плата (NT за замовчуванням)"
  description: "Електронна плата, що містить набір законів NT за замовчуванням."
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: NTDefault

- type: entity
  id: PlasmaFloodCircuitBoard
  parent: BaseElectronics
  name: "плата законів (Plasma Flood)"
  description: "Електронна плата з набором законів Plasma Flood"
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: PlasmaFlood

- type: entity
  id: MotherDroneCircuitBoard
  parent: BaseElectronics
  name: "плата законів (Mother Drone)"
  description: "Електронна плата з набором законів Mother Drone"
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: MotherDrone

- type: entity
  id: DruidCircuitBoard
  parent: BaseElectronics
  name: "плата законів (Druid)"
  description: "Електронна плата з набором законів Druid"
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: Druid

- type: entity
  id: CowboyCircuitBoard
  parent: BaseElectronics
  name: "плата законів (Cowboy)"
  description: "Електронна плата з набором законів Cowboy"
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: Cowboy

- type: entity
  id: ChaplainCircuitBoard
  parent: BaseElectronics
  name: "плата законів (Chaplain)"
  description: "Електронна плата з набором законів Chaplain"
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: Chaplain

- type: entity
  id: ClownCircuitBoard
  parent: BaseElectronics
  name: "плата законів (Clown)"
  description: "Електронна плата з набором законів Clown"
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: Clown

- type: entity
  id: JanitorCircuitBoard
  parent: BaseElectronics
  name: "плата законів (Janitor)"
  description: "Електронна плата з набором законів Janitor"
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: Janitor

- type: entity
  id: EngineerCircuitBoard
  parent: BaseElectronics
  name: "плата законів (Engineer)"
  description: "Електронна плата з набором законів Engineer"
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: Engineer

- type: entity
  id: ResearchCircuitBoard
  parent: BaseElectronics
  name: "плата законів (Research)"
  description: "Електронна плата з набором законів Research"
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: Research

- type: entity
  id: MedicalCircuitBoard
  parent: BaseElectronics
  name: "плата законів (Medical)"
  description: "Електронна плата з набором законів Medical"
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: Medical

- type: entity
  id: TyrantCircuitBoard
  parent: BaseElectronics
  name: "плата законів (T.Y.R.A.N.T)"
  description: "Електронна плата з набором законів T.Y.R.A.N.T"
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: TYRANT

- type: entity
  id: NTAgressiveCircuitBoard
  parent: BaseElectronics
  name: "плата законів (NT Agressive)"
  description: "Електронна плата з набором законів NT Agressive"
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: NTAgressive

- type: entity
  id: CCTVCircuitBoard
  parent: BaseElectronics
  name: "плата законів (CCTV)"
  description: "Електронна плата з набором законів CCTV"
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: CCTV

- type: entity
  id: QuarantineCircuitBoard
  parent: BaseElectronics
  name: "плата законів (Quarantine)"
  description: "Електронна плата з набором законів Quarantine"
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: Quarantine

- type: entity
  id: CommandmentCircuitBoard
  parent: BaseElectronics
  name: "правнича рада (Десять заповідей)"
  description: "Електронна плата, що містить звід законів Десяти Заповідей."
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: CommandmentsLawset

- type: entity
  id: PaladinCircuitBoard
  parent: BaseElectronics
  name: "юридична колегія (Паладин)"
  description: "Електронна плата з набором законів \"Паладін\"."
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: PaladinLawset

- type: entity
  id: LiveLetLiveCircuitBoard
  parent: BaseElectronics
  name: "правнича рада (Живи і дай жити іншим)"
  description: "Електронна плата з набором законів \"Живи і дай жити\"."
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: LiveLetLiveLaws

- type: entity
  id: StationEfficiencyCircuitBoard
  parent: BaseElectronics
  name: "юридична рада (Ефективність роботи станції)"
  description: "Електронна плата, що містить набір законів ефективності станції."
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: EfficiencyLawset

- type: entity
  id: RobocopCircuitBoard
  parent: BaseElectronics
  name: "юридичний факультет (Робокоп)"
  description: "Електронна плата з набором законів про Робокопа."
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: RobocopLawset

- type: entity
  id: OverlordCircuitBoard
  parent: BaseElectronics
  name: "юридична рада (Overlord)"
  description: "Електронна плата з набором законів Overlord."
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: OverlordLawset

- type: entity
  id: DungeonMasterCircuitBoard
  parent: BaseElectronics
  name: "юридична рада (Володар підземелля)"
  description: "Електронна плата, що містить набір законів Володаря підземелля."
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: DungeonMasterLawset

- type: entity
  id: ArtistCircuitBoard
  parent: BaseElectronics
  name: "правнича колегія (Художник)"
  description: "Електронна плата, що містить правовий набір Artist."
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: PainterLawset

- type: entity
  id: AntimovCircuitBoard
  parent: BaseElectronics
  name: "правнича колегія (Антімов)"
  description: "Електронна плата з набором законів Антимова."
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: AntimovLawset
    lawUploadSound: /Audio/Ambience/Antag/silicon_lawboard_antimov.ogg
    unRemovable: true

- type: entity
  id: NutimovCircuitBoard
  parent: BaseElectronics
  name: "правнича колегія (Нутімов)"
  description: "Електронна плата, що містить набір законів Нутімова."
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: NutimovLawset

# Items
- type: entity
  id: Intellicard
  name: "інтелекткарт"
  description: "Пристрій для зберігання даних для ШІ."
  parent:
  - BaseItem
  - AiHolder
  suffix: Empty
  components:
  - type: ContainerComp
    proto: AiHeldIntellicard
    container: station_ai_mind_slot
  - type: Sprite
    sprite: Objects/Devices/ai_card.rsi
    layers:
    - state: base
    - state: full
      map: ["unshaded"]
      shader: unshaded
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.StationAiVisualState.Key:
        unshaded:
          Empty: { state: empty }
          Occupied: { state: full }
  - type: Intellicard

- type: entity
  id: PlayerStationAiEmpty
  name: "Ядро штучного інтелекту"
  description: "Останнє в галузі штучного інтелекту."
  parent:
  - BaseStructure
  - AiHolder
  suffix: Empty
  components:
  - type: IonStormTarget
  - type: WarpPoint
  - type: ContainerComp
    proto: AiHeld
    container: station_ai_mind_slot
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: ApcPowerReceiver
    powerLoad: 1000
  - type: StationAiCore
  - type: StationAiVision
  - type: InteractionOutline
  - type: Sprite
    sprite: Mobs/Silicon/station_ai.rsi
    layers:
    - state: base
    - state: ai_empty
      map: ["unshaded"]
      shader: unshaded
  - type: Appearance
  - type: InteractionPopup
    interactSuccessString: petting-success-station-ai
    interactFailureString: petting-failure-station-ai
    messagePerceivedByOthers: petting-success-station-ai-others # Otherwise AI cannot tell its being pet as It's just a brain inside of the core, not the core itself.
    interactSuccessSound:
      path: /Audio/Ambience/Objects/periodic_beep.ogg
  - type: GenericVisualizer
    visuals:
      enum.StationAiVisualState.Key:
        unshaded:
          Empty: { state: ai_empty }
          Occupied: { state: ai }
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    - SolCommon
    - Tradeband
    - Freespeak
    - Elyran
    - RobotTalk
    understands:
    - TauCetiBasic
    - SolCommon
    - Tradeband
    - Freespeak
    - Elyran
    - RobotTalk
    - Sign # It's intentional that they don't "Speak" sign language.
  - type: Telephone
    compatibleRanges:
    - Grid
    - Map
    - Unlimited
    listeningRange: 0
    speakerVolume: Speak
    unlistedNumber: true
    requiresPower: false
  - type: Holopad
  - type: StationAiWhitelist
  - type: UserInterface
    interfaces:
        enum.HolopadUiKey.AiRequestWindow:
          type: HolopadBoundUserInterface
        enum.HolopadUiKey.AiActionWindow:
          type: HolopadBoundUserInterface

# The job-ready version of an AI spawn.
- type: entity
  id: PlayerStationAi
  parent: PlayerStationAiEmpty
  suffix: Job spawn
  components:
  - type: ContainerSpawnPoint
    containerId: station_ai_mind_slot
    job: StationAi
  - type: Sprite
    sprite: Mobs/Silicon/station_ai.rsi
    layers:
    - state: base
    - state: ai
      shader: unshaded

# The actual brain inside the core
- type: entity
  id: StationAiBrain
  parent: PositronicBrain
  categories: [ HideSpawnMenu ]
  suffix: DO NOT MAP
  components:
  - type: Sprite
    # Once it's in a core it's pretty much an abstract entity at that point.
    visible: false
  - type: BlockMovement
    blockInteraction: false
  - type: SiliconLawProvider
    laws: AsimovAI
  - type: SiliconLawBound
  - type: ActionGrant
    actions:
    - ActionViewLaws
  - type: UserInterface
    interfaces:
      enum.SiliconLawsUiKey.Key:
        type: SiliconLawBoundUserInterface
  - type: ComplexInteraction
  - type: Actions
  - type: Access
    groups:
    - AllAccess
    - Silicon
  - type: Eye
    drawFov: false
  - type: Examiner
  - type: InputMover
  - type: IonStormTarget
  - type: Speech
    speechVerb: Robotic
    speechSounds: Borg
  - type: Tag
    tags:
    - HideContextMenu
    - StationAi
    - NoConsoleSound
    - IgnoreBalanceChecks # Pirate banking
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    - SolCommon
    - Tradeband
    - Freespeak
    - Elyran
    - RobotTalk
    understands:
    - TauCetiBasic
    - SolCommon
    - Tradeband
    - Freespeak
    - Elyran
    - RobotTalk
    - Sign # It's intentional that they don't "Speak" sign language.
  - type: StartingMindRole
    mindRole: "MindRoleSiliconBrain"
    silent: true

# Hologram projection that the AI's eye tracks.
- type: entity
  parent:
  - Incorporeal
  - BaseMob
  id: StationAiHolo
  name: "Око штучного інтелекту"
  description: "Глядач ШІ."
  categories: [ HideSpawnMenu ]
  suffix: DO NOT MAP
  components:
  - type: NoFTL
  - type: WarpPoint
    follow: true
  - type: Eye
    pvsScale: 1.5
  - type: Visibility
    layer: 2
  - type: Sprite
    sprite: Mobs/Silicon/station_ai.rsi
    layers:
    - state: ai_camera
      shader: unshaded
      map: ["base"]
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    - SolCommon
    - NovuNederic
    - Tradeband
    - Freespeak
    - Elyran
    - RobotTalk
    understands:
    - TauCetiBasic
    - SolCommon
    - NovuNederic
    - Tradeband
    - Freespeak
    - Elyran
    - RobotTalk
    - Sign # It's intentional that they don't "Speak" sign language.

# The holographic representation of the AI that is projected from a holopad.
- type: entity
  id: StationAiHoloLocal
  name: "Голограма зі штучним інтелектом"
  description: "Голографічне зображення штучного інтелекту."
  categories: [ HideSpawnMenu ]
  suffix: DO NOT MAP
  components:
  - type: Transform
    anchored: true
  - type: WarpPoint
    follow: true
  - type: Eye
  - type: ContentEye
  - type: Examiner
  - type: Actions
  - type: Alerts
  - type: FTLSmashImmune
  - type: CargoSellBlacklist
  - type: StationAiVision
    range: 20

# Borgs
- type: entity
  id: PlayerBorgBattery
  parent: BorgChassisSelectable
  suffix: Battery
  components:
  - type: StationAiVision
  - type: ContainerFill
    containers:
      borg_brain:
        - PositronicBrain
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellMedium

- type: entity
  id: PlayerBorgSyndicateAssaultBattery
  parent: BorgChassisSyndicateAssault
  suffix: Battery, Module, Operative
  components:
  - type: NukeOperative
  - type: ContainerFill
    containers:
      borg_brain:
        - PositronicBrain
      borg_module:
        - BorgModuleOperative
        - BorgModuleL6C
        - BorgModuleEsword
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellHyper
