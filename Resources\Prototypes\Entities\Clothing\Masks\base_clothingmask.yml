- type: entity
  abstract: true
  parent: Clothing
  id: ClothingMaskBase
  components:
  - type: Sprite
    state: icon
  - type: Item
    size: Small
  - type: Clothing
    slots: [mask]
  - type: Tag # goob edit
    tags:
    - Mask

- type: entity
  abstract: true
  parent: ClothingMaskBase
  id: ClothingMaskPullableBase
  components:
  - type: Mask

- type: entity
  id: ActionToggleMask
  name: "Перемкнути Маску"
  description: "Зручно, але заважає вставляти пиріг у ваш отвір для пирогів."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: { sprite: Clothing/Mask/gas.rsi, state: icon }
    iconOn: Interface/Default/blocked.png
    event: !type:ToggleMaskEvent

- type: entity
  id: ClothingMaskBaseButcherable
  parent: ClothingMaskBase
  abstract: true
  components:
  - type: Butcherable
    butcheringType: Knife
    spawned:
    - id: MaterialCloth1
      amount: 1
  - type: Food
    requiresSpecialDigestion: true
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Fiber
          Quantity: 10
  - type: Tag
    tags:
      - ClothMade
