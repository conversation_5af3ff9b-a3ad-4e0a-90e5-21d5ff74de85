- type: constructionGraph
  id: <PERSON><PERSON><PERSON>
  start: start
  graph:
    - node: start
      actions:
        - !type:DestroyEntity {}
      edges:
        - to: dresser
          completed:
            - !type:SnapToGrid { }
          steps:
            - material: WoodPlank
              amount: 3
              doAfter: 2
    - node: dresser
      entity: Dresser
      edges:
        - to: start
          completed:
            - !type:SpawnPrototype
              prototype: MaterialWoodPlank
              amount: 3
          steps:
            - tool: Prying
              doAfter: 3
