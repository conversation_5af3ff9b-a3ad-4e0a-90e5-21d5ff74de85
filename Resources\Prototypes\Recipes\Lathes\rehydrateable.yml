# recipes should generally cost 1.5x to 2x of the biomass output of their mob

- type: latheRecipe
  id: MonkeyCube
  result: MonkeyCube
  completetime: 30
  materials:
    Biomass: 16

- type: latheRecipe
  id: KoboldCube
  result: KoboldCube
  completetime: 30
  materials:
    Biomass: 16

- type: latheRecipe
  id: CowCube
  result: CowCube
  completetime: 30
  materials:
    Biomass: 120

- type: latheRecipe
  id: GoatCube
  result: GoatCube
  completetime: 30
  materials:
    Biomass: 35

- type: latheRecipe
  id: MothroachCube
  result: MothroachCube
  completetime: 45 # prevent biblical floods
  materials:
    Biomass: 20 # a lot of materials wasted due to complex genetics

- type: latheRecipe
  id: MouseCube
  result: MouseCube
  completetime: 15
  materials:
    Biomass: 12

- type: latheRecipe
  id: CockroachCube
  result: CockroachCube
  completetime: 15
  materials:
    Biomass: 16

- type: latheRecipe
  id: SpaceCarpCube
  result: SpaceCarpCube
  completetime: 30
  materials:
    Biomass: 24
    Plasma: 600

- type: latheRecipe
  id: SpaceTickCube
  result: SpaceTickCube
  completetime: 15
  materials:
    Biomass: 8
    Plasma: 300 # less biomass but more plasma

- type: latheRecipe
  id: AbominationCube
  result: AbominationCube
  completetime: 30
  materials: # abominations are slow and essentially worse than even carp
    Biomass: 28
    Plasma: 500 # more biomass but less plasma
