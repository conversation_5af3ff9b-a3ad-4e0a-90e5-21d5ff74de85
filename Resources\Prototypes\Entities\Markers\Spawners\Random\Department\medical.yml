# Minor medical loot spawner
# Use case: Common areas that aren't medical
- type: entity
  name: "точка появи медичного луту"
  id: LootSpawnerMedicalMinor
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Objects/Specific/Medical/medical.rsi
          state: brutepack
    - type: RandomSpawner
      rarePrototypes:
        - MedkitFilled
      rareChance: 0.1
      prototypes:
        - Syringe #you win some you lose some
        - Brutepack
        - Ointment
        - Gauze
        - EmergencyMedipen
        - PillCanisterIron
      chance: 0.9
      offset: 0.0

# Proper medical loot spawner, contains things like beakers and better meds.
- type: entity
  name: "точка появи класного медичного луту"
  id: LootSpawnerMedicalClassy
  parent: MarkerBase
  components:
    - type: Sprite
      layers:
        - state: red
        - sprite: Objects/Specific/Medical/firstaidkits.rsi
          state: firstaid
    - type: RandomSpawner
      rarePrototypes:
        - PillCanisterDermaline
        - PillCanisterDylovene
        - PillCanisterBicaridine
        - PillCanisterKelotane
        - PillCanisterDexalin
        - HandheldHealthAnalyzer
        - ClothingHandsGlovesLatex
      rareChance: 0.1
      prototypes:
        - Syringe
        - Dropper
        - Beaker
        - LargeBeaker
        - Brutepack
        - Ointment
        - Gauze
        - EmergencyMedipen
        - EpinephrineChemistryBottle
        - PillCanisterTricordrazine
        - PillCanisterCharcoal
        - PillCanisterHyronalin
        - PillCanisterIron
        - Bloodpack
        - MedkitFilled
      chance: 0.9
      offset: 0.0
