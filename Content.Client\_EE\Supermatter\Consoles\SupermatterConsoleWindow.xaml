<controls:FancyWindow xmlns="https://spacestation14.io"
               xmlns:ui="clr-namespace:Content.Client.Pinpointer.UI"
               xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
               Title="{Loc 'supermatter-console-window-title'}"
               Resizable="True"
               SetSize="700 400"
               MinSize="700 400">
    <BoxContainer Orientation="Vertical">
        <!-- Main display -->
        <BoxContainer Orientation="Horizontal" VerticalExpand="True" HorizontalExpand="True">
            <!-- Supermatter list -->
            <BoxContainer Orientation="Vertical" VerticalExpand="True" HorizontalExpand="True" Margin="10 0 10 10">
                <!-- Supermatters (entries added by C# code) -->
                <BoxContainer VerticalExpand="True" HorizontalExpand="True" Margin="0 10 0 0">
                    <ScrollContainer HorizontalExpand="True">
                        <BoxContainer Name="SupermattersTable" Orientation="Vertical" VerticalExpand="True" HorizontalExpand="True" Margin="0 0 0 10" />
                    </ScrollContainer>
                </BoxContainer>
            </BoxContainer>
        </BoxContainer>

        <!-- Footer -->
        <BoxContainer Orientation="Vertical">
            <PanelContainer StyleClasses="LowDivider" />
            <BoxContainer Orientation="Horizontal" Margin="10 2 5 0" VerticalAlignment="Bottom">
                <Label Text="{Loc 'supermatter-console-window-flavor-left'}" StyleClasses="WindowFooterText" />
                <Label Text="{Loc 'supermatter-console-window-flavor-right'}" StyleClasses="WindowFooterText"
                        HorizontalAlignment="Right" HorizontalExpand="True"  Margin="0 0 5 0" />
                <TextureRect StyleClasses="NTLogoDark" Stretch="KeepAspectCentered"
                        VerticalAlignment="Center" HorizontalAlignment="Right" SetSize="19 19"/>
            </BoxContainer>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
