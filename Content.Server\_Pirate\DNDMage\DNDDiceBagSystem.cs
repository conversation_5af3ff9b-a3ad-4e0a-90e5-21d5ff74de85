using Content.Server.Storage.EntitySystems;
using Content.Shared._Pirate.DNDMage;
using Content.Shared.Containers.ItemSlots;
using Robust.Shared.Containers;

namespace Content.Server._Pirate.DNDMage;

/// <summary>
/// Система для обробки компонента DNDDiceBagComponent.
/// </summary>
public sealed class DNDDiceBagSystem : EntitySystem
{
    [Dependency] private readonly SharedContainerSystem _containerSystem = default!;

    public override void Initialize()
    {
        base.Initialize();

        // Підписуємось на подію ініціалізації компонента
        SubscribeLocalEvent<DNDDiceBagComponent, ComponentInit>(OnComponentInit);

        // Підписуємось на подію додавання предмета в контейнер
        SubscribeLocalEvent<DNDDiceBagComponent, EntInsertedIntoContainerMessage>(OnItemInserted);
    }

    /// <summary>
    /// Обробляє подію ініціалізації компонента DNDDiceBagComponent.
    /// </summary>
    private void OnComponentInit(EntityUid uid, DNDDiceBagComponent component, ComponentInit args)
    {
        // Перевіряємо, чи потрібно додавати компонент DNDBagDiceComponent до кісток
        if (!component.AddComponentOnInit)
            return;

        // Отримуємо контейнер для зберігання кісток
        if (!_containerSystem.TryGetContainer(uid, component.ContainerId, out var container))
            return;

        // Додаємо компонент DNDBagDiceComponent до всіх кісток в контейнері
        foreach (var item in container.ContainedEntities)
        {
            // Перевіряємо, чи це кістка з компонентом DNDDiceComponent
            if (HasComp<DNDDiceComponent>(item) && !HasComp<DNDBagDiceComponent>(item))
            {
                // Додаємо компонент DNDBagDiceComponent
                var bagComponent = EnsureComp<DNDBagDiceComponent>(item);
                bagComponent.DiceBagUid = uid;
                bagComponent.ContainerId = component.ContainerId;
                bagComponent.IsBound = true;
                Dirty(item, bagComponent);
            }
        }
    }

    /// <summary>
    /// Обробляє подію додавання предмета в контейнер.
    /// </summary>
    private void OnItemInserted(EntityUid uid, DNDDiceBagComponent component, EntInsertedIntoContainerMessage args)
    {
        // Перевіряємо, чи це потрібний контейнер
        if (args.Container.ID != component.ContainerId)
            return;

        // Перевіряємо, чи це кістка з компонентом DNDDiceComponent
        if (HasComp<DNDDiceComponent>(args.Entity))
        {
            // Додаємо або оновлюємо компонент DNDBagDiceComponent
            var bagComponent = EnsureComp<DNDBagDiceComponent>(args.Entity);
            bagComponent.DiceBagUid = uid;
            bagComponent.ContainerId = component.ContainerId;
            bagComponent.IsBound = true;
            Dirty(args.Entity, bagComponent);
        }
    }

    /// <summary>
    /// Прив'язує кістку до сумки для кісток.
    /// </summary>
    public void BindDiceToBag(EntityUid diceUid, EntityUid bagUid, string containerId = "storage-default")
    {
        if (!HasComp<DNDDiceComponent>(diceUid) || !HasComp<DNDDiceBagComponent>(bagUid))
            return;

        var bagComponent = EnsureComp<DNDBagDiceComponent>(diceUid);
        bagComponent.DiceBagUid = bagUid;
        bagComponent.ContainerId = containerId;
        bagComponent.IsBound = true;
        Dirty(diceUid, bagComponent);
    }
}
