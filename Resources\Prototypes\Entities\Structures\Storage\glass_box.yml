﻿- type: entity
  id: BaseGlassBox
  parent: BaseStructureDynamic
  abstract: true
  placement:
    mode: SnapgridCenter
  components:
  - type: Transform
    anchored: true
  - type: Physics
    bodyType: Static
  - type: Clickable
  - type: InteractionOutline
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.45"
        density: 1000
        mask:
        - MachineMask
        layer:
        - MidImpassable
        - LowImpassable
  - type: ItemSlots
  - type: ContainerContainer
    containers:
      ItemCabinet: !type:ContainerSlot
  - type: Anchorable
    delay: 4
  - type: Appearance

- type: entity
  id: GlassBox
  name: "скляна коробка"
  description: "Міцна вітрина для дорогого експоната."
  parent: BaseGlassBox
  abstract: true # TODO: Temporarily abstract. Remove it after item scaling in cabinets is implemented.
  components:
  - type: Sprite
    noRot: true
    sprite: Structures/Storage/glassbox.rsi
    layers:
    - state: base
    - state: caplaser # TODO: Remove it after item scaling in cabinets is implemented.
      map: ["enum.ItemCabinetVisualLayers.ContainsItem"]
      visible: true
    - state: glass
      map: ["enum.ItemCabinetVisualLayers.Door"]
    - state: locked
      shader: unshaded
      map: ["enum.LockVisualLayers.Lock"]
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.45"
        density: 1000
        mask:
        - MachineMask
        layer:
        - LowImpassable
        - MidImpassable
        - BulletImpassable
  - type: AccessReader
  - type: Repairable
    fuelCost: 15
    doAfterDelay: 5
  - type: Lock
  - type: LockVisuals
  - type: DamageVisuals
    thresholds: [4, 8, 12] # TODO: Fix damage visuals on open state.
    damageDivisor: 7.555
    trackAllDamage: true
    damageOverlay:
      sprite: Structures/Storage/glassbox.rsi
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Glass
  - type: MeleeSound
    soundGroups:
      Brute:
        collection: GlassSmash
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:EmptyAllContainersBehaviour
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
      - !type:PlaySoundBehavior
        sound:
          path: /Audio/Machines/warning_buzzer.ogg
          params:
            volume: 10
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassReinforced:
            min: 1
            max: 2
      - !type:ChangeConstructionNodeBehavior
        node: brokenGlassBox
      - !type:DoActsBehavior
        acts: [ "Destruction" ]

- type: entity
  id: GlassBoxLaser
  parent: GlassBox
  suffix: AntiqueLaser
  components:
  - type: AccessReader
    access: [["Captain"]]
  - type: Construction
    graph: GlassBox
    node: glassBox
  - type: ItemCabinet
    cabinetSlot:
      ejectOnInteract: true
      whitelist:
        tags:
        - WeaponAntiqueLaser
    doorSound:
      path: /Audio/Machines/machine_switch.ogg
    openState: glass-up
    closedState: glass

- type: entity
  id: GlassBoxLaserOpen
  parent: GlassBoxLaser
  suffix: AntiqueLaser, Open
  components:
  - type: Lock
    locked: false
  - type: ItemCabinet
    opened: true

- type: entity
  id: GlassBoxLaserFilled
  parent: GlassBoxLaser
  suffix: AntiqueLaser, Filled
  components:
  - type: ItemCabinet
    cabinetSlot:
      startingItem: WeaponAntiqueLaser
      ejectOnInteract: true
      whitelist:
        tags:
        - WeaponAntiqueLaser

- type: entity
  id: GlassBoxLaserFilledOpen
  parent: GlassBoxLaserFilled
  suffix: AntiqueLaser, Filled, Open
  components:
  - type: Lock
    locked: false
  - type: ItemCabinet
    opened: true

- type: entity
  id: GlassBoxFrame
  name: "рама скляної коробки"
  description: "Міцна вітрина без скла для дорогого експоната."
  parent: BaseGlassBox
  suffix: Frame
  components:
  - type: Sprite
    noRot: true
    sprite: Structures/Storage/glassbox.rsi
    layers:
    - state: base
  - type: Construction
    graph: GlassBox
    node: boxMissingWires
  - type: Climbable
  - type: Damageable
    damageModifierSet: Wood
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank1:
            min: 2
            max: 5
      - !type:DoActsBehavior
        acts: ["Destruction"]

- type: entity
  id: GlassBoxBroken
  name: "розбита скляна коробка"
  description: "Розбита вітрина для викраденого дорогого експоната."
  parent: GlassBoxFrame
  suffix: Broken
  components:
  - type: Sprite
    sprite: Structures/Storage/glassbox.rsi
    layers:
    - state: base
    - state: glass-broken
  - type: Construction
    graph: GlassBox
    node: brokenGlassBox
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassReinforced:
            min: 1
            max: 1
          MaterialWoodPlank1:
            min: 2
            max: 5
      - !type:DoActsBehavior
        acts: ["Destruction"]
