### Interaction Popup component

## Petting animals

petting-success-generic = You pet {THE($target)} on {POSS-ADJ($target)} head.
petting-success-soft-floofy = You pet {THE($target)} on {POSS-ADJ($target)} soft floofy head.

petting-success-bingus = You pet {THE($target)} on {POSS-ADJ($target)} wrinkly little head.
petting-success-bird = You pet {THE($target)} on {POSS-ADJ($target)} cute feathery head.
petting-success-carp = You pet {THE($target)} on {POSS-ADJ($target)} fishy little head.
petting-success-cat = You pet {THE($target)} on {POSS-ADJ($target)} fuzzy little head.
petting-success-corrupted-corgi = In an act of hubris, you pet {THE($target)} on {POSS-ADJ($target)} cursed little head.
petting-success-crab = You pet {THE($target)} on {POSS-ADJ($target)} smooth little head.
petting-success-dehydrated-carp = You pet {THE($target)} on {POSS-ADJ($target)} dry little head. {CAPITALIZE(OBJECT($target))} seems to like you now!
petting-success-dog = You pet {THE($target)} on {POSS-ADJ($target)} soft floofy head.
petting-success-frog = You pet {THE($target)} on {POSS-ADJ($target)} slippery little head.
petting-success-goat = You pet {THE($target)} on {POSS-ADJ($target)} horned floofy head.
petting-success-goose = Against all odds, you manage to pet {THE($target)} on {POSS-ADJ($target)} horrible little head.
petting-success-kangaroo = You pet {THE($target)} on {POSS-ADJ($target)} Strayan head.
petting-success-possum = You pet {THE($target)} on {POSS-ADJ($target)} horrible little head.
petting-success-pig = You pet {THE($target)} on {POSS-ADJ($target)} hairy head.
petting-success-raccoon = You pet {THE($target)} on {POSS-ADJ($target)} trash eating little head.
petting-success-reptile = You pet {THE($target)} on {POSS-ADJ($target)} scaly little head.
petting-success-sloth = You pet {THE($target)} on {POSS-ADJ($target)} slow moving head.
petting-success-space-cat = You pet {THE($target)} on {POSS-ADJ($target)} glass domed head.
petting-success-tarantula = You pet {THE($target)} on {POSS-ADJ($target)} hairy little head.
petting-success-holo = You pet {THE($target)} on {POSS-ADJ($target)} metallic spiky head.
petting-success-dragon = Dodging teeth, claws, and flames, you pet {THE($target)} on {POSS-ADJ($target)} massive scaled head.
petting-success-hamster = You pet {THE($target)} on {POSS-ADJ($target)} fluffy little head.
petting-success-bear = You reluctantly pet {THE($target)} on {POSS-ADJ($target)} mystical head.
petting-success-slimes = You pet {THE($target)} on {POSS-ADJ($target)} mucous surface.
petting-success-snake = You pet {THE($target)} on {POSS-ADJ($target)} scaly large head.
petting-success-monkey = You pet {THE($target)} on {POSS-ADJ($target)} mischevious little head.
petting-success-nymph = You pet {THE($target)} on {POSS-ADJ($target)} wooden little head.

petting-failure-generic = You reach out to pet {THE($target)}, but {SUBJECT($target)} {CONJUGATE-BE($target)} aloof towards you.

petting-failure-bat = You reach out to pet {THE($target)}, but {SUBJECT($target)} {CONJUGATE-BE($target)} too hard to catch!
petting-failure-carp = You reach out to pet {THE($target)}, but {POSS-ADJ($target)} sharp teeth make you think twice.
petting-failure-corrupted-corgi = You reach out to pet {THE($target)}, but think better of it.
petting-failure-crab = You reach out to pet {THE($target)}, but {SUBJECT($target)} snaps {POSS-ADJ($target)} claws in your general direction!
petting-failure-dehydrated-carp = You pet {THE($target)} on {POSS-ADJ($target)} dry little head.
petting-failure-goat = You reach out to pet {THE($target)}, but {SUBJECT($target)} stubbornly refuses!
petting-failure-goose = You reach out to pet {THE($target)}, but {SUBJECT($target)} {CONJUGATE-BE($target)} too horrible!
petting-failure-possum = You reach out to pet {THE($target)}, but are met with hisses and snarls!
petting-failure-pig = You reach out to pet {THE($target)}, but are met with irritated oinks and squeals!
petting-failure-raccoon = You reach out to pet {THE($target)}, but {THE($target)} is busy raccooning around.
petting-failure-sloth = You reach out to pet {THE($target)}, but {SUBJECT($target)} somehow dodge with ludicrous speed!
petting-failure-holo = You reach out to pet {THE($target)}, but {POSS-ADJ($target)} spikes almost impale your hand!
petting-failure-dragon = You raise your hand, but as {THE($target)} roars, you decide you'd rather not be toasty carp food.
petting-failure-hamster = You reach out to pet {THE($target)}, but {SUBJECT($target)} attempts to bite your finger and only your quick reflexes save you from an almost fatal injury.
petting-failure-pibble = You reach out to pet {THE($target)}, and {SUBJECT($target)} lunges at you!
petting-failure-bear = You reach out to pet {THE($target)}, but {SUBJECT($target)} growls, making you think twice.
petting-failure-monkey = You reach out to pet {THE($target)}, but {SUBJECT($target)} almost bites your fingers!
petting-failure-nymph = You reach out to pet {THE($target)}, but {POSS-ADJ($target)} moves their branches away.
petting-failure-shadow = You're trying to pet {THE($target)}, but your hand passes through the cold darkness of his body.

## Petting silicons

petting-success-honkbot = You pet {THE($target)} on {POSS-ADJ($target)} slippery metal head.
petting-success-mimebot = You pet {THE($target)} on {POSS-ADJ($target)} cold metal head.
petting-success-cleanbot = You pet {THE($target)} on {POSS-ADJ($target)} damp metal head.
petting-success-plantbot = You pet {THE($target)} on {POSS-ADJ($target)} muddy metal head.
petting-success-medibot = You pet {THE($target)} on {POSS-ADJ($target)} sterile metal head.
petting-success-weldbot = You pet {THE($target)} on {POSS-ADJ($target)} stained metal head.
petting-success-firebot = You pet {THE($target)} on {POSS-ADJ($target)} warm metal head.
petting-success-generic-cyborg = You pet {THE($target)} on {POSS-ADJ($target)} metal head.
petting-success-salvage-cyborg = You pet {THE($target)} on {POSS-ADJ($target)} dirty metal head.
petting-success-engineer-cyborg = You pet {THE($target)} on {POSS-ADJ($target)} reflective metal head.
petting-success-janitor-cyborg = You pet {THE($target)} on {POSS-ADJ($target)} damp metal head.
petting-success-medical-cyborg = You pet {THE($target)} on {POSS-ADJ($target)} sterile metal head.
petting-success-service-cyborg = You pet {THE($target)} on {POSS-ADJ($target)} dapper looking metal head.
petting-success-syndicate-cyborg = You pet {THE($target)} on {POSS-ADJ($target)} menacing metal head.
petting-success-derelict-cyborg = You pet {THE($target)} on {POSS-ADJ($target)} rusty metal head.
petting-success-recycler = You pet {THE($target)} on {POSS-ADJ($target)} mildly threatening steel exterior.
petting-success-station-ai = You pet {THE($target)} on {POSS-ADJ($target)} cold, square screen.
petting-success-gladiabot = You pet {THE($target)} on {POSS-ADJ($target)} vicious cardboard head.
petting-success-batonbot = You pet {THE($target)} on {POSS-ADJ($target)} protective metal head.
petting-success-disablerbot = You pet {THE($target)} on {POSS-ADJ($target)} protective metal head.
petting-success-minebot = You pet {THE($target)} on {POSS-ADJ($target)} hardened metal head.
petting-success-fillbot = You pet {THE($target)} on {POSS-ADJ($target)} diligent metal head.

petting-failure-honkbot = You reach out to pet {THE($target)}, but {SUBJECT($target)} honks in refusal!
petting-failure-cleanbot = You reach out to pet {THE($target)}, but {SUBJECT($target)} {CONJUGATE-BE($target)} busy mopping!
petting-failure-plantbot = You reach out to pet {THE($target)}, but {POSS-ADJ($target)} clippers nearly snip your fingers off!
petting-failure-mimebot = You reach out to pet {THE($target)}, but {SUBJECT($target)} {CONJUGATE-BE($target)} busy miming!
petting-failure-medibot = You reach out to pet {THE($target)}, but {POSS-ADJ($target)} syringe nearly stabs your hand!
petting-failure-weldbot = You reach out to pet {THE($target)}, but {POSS-ADJ($target)} welder nearly burns your hand!
petting-failure-firebot = You reach out to pet {THE($target)}, but {SUBJECT($target)} sprays you in the face before you can get close!
petting-failure-generic-cyborg = You reach out to pet {THE($target)}, but {SUBJECT($target)} {CONJUGATE-BE($target)} busy stating laws!
petting-failure-salvage-cyborg = You reach out to pet {THE($target)}, but {SUBJECT($target)} {CONJUGATE-BE($target)} busy drilling!
petting-failure-engineer-cyborg = You reach out to pet {THE($target)}, but {SUBJECT($target)} {CONJUGATE-BE($target)} busy repairing!
petting-failure-janitor-cyborg = You reach out to pet {THE($target)}, but {SUBJECT($target)} {CONJUGATE-BE($target)} busy cleaning!
petting-failure-medical-cyborg = You reach out to pet {THE($target)}, but {SUBJECT($target)} {CONJUGATE-BE($target)} busy saving lives!
petting-failure-service-cyborg = You reach out to pet {THE($target)}, but {SUBJECT($target)} {CONJUGATE-BE($target)} busy serving others!
petting-failure-syndicate-cyborg = You reach out to pet {THE($target)}, but {POSS-ADJ($target)} treacherous affiliation makes you reconsider.
petting-failure-derelict-cyborg = You reach out to pet {THE($target)}, but {POSS-ADJ($target)} rusty and jagged exterior makes you reconsider.
petting-failure-station-ai = You reach out to pet {THE($target)}, but {SUBJECT($target)} {CONJUGATE-BASIC($target, "zap", "zaps")} your hand away.
petting-failure-gladiabot = You reach out to pet {THE($target)}, but {SUBJECT($target)} {CONJUGATE-BE($target)} only wants to fight!
petting-failure-batonbot = You reach out to pet {THE($target)}, but {SUBJECT($target)} nearly prods you with its baton!
petting-failure-disablerbot = You reach out to pet {THE($target)}, but {SUBJECT($target)} angrily waves its weapon at you!
petting-failure-minebot = You reach out to pet {THE($target)}, but {SUBJECT($target)} nearly swings its pickaxe at you!
petting-failure-fillbot = You reach out to pet {THE($target)}, but {SUBJECT($target)} pushes your hand away!

petting-success-station-ai-others = { CAPITALIZE(THE($user)) } pets {THE($target)} on {POSS-ADJ($target)} cold, square screen.

## Rattling fences

fence-rattle-success = *rattle*

## Hugging players

hugging-success-generic = You hug {THE($target)}.
hugging-success-generic-others = { CAPITALIZE(THE($user)) } hugs {THE($target)}.
hugging-success-generic-target = { CAPITALIZE(THE($user)) } hugs you.

## Other

petting-success-tesla = You pet {THE($target)}, violating the laws of nature and physics.

petting-failure-tesla = You reach out towards {THE($target)}, but it zaps your hand away.

## Lavaland

petting-success-magmahand = Somehow, miraculously, you pet {THE($target)} on {POSS-ADJ($target)} red-hot surface.
petting-failure-magmahand = You reach out to pet {THE($target)}, but {POSS-ADJ($target)} blistering hot surface makes you reconsider.
