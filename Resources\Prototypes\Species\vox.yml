- type: species
  id: Vox
  name: species-name-vox
  roundStart: true # sad...
  prototype: MobVox
  sprites: MobVoxSprites
  markingLimits: MobVoxMarkingLimits
  dollPrototype: MobVoxDummy
  skinColoration: VoxFeathers
  defaultSkinTone: "#6c741d"
  maleFirstNames: names_vox
  femaleFirstNames: names_vox
  naming: First
  sexes:
  - Unsexed

- type: speciesBaseSprites
  id: MobVoxSprites
  sprites:
    Head: MobVoxHead
    Snout: MobHumanoidAnyMarking
    Hair: MobHumanoidAnyMarking
    FacialHair: MobHumanoidAnyMarking
    Chest: MobVoxTorso
    Eyes: MobVoxEyes
    LArm: MobVoxLArm
    RArm: MobVoxRArm
    LHand: MobVoxLHand
    RHand: MobVoxRHand
    LLeg: MobVoxLLeg
    RLeg: MobVoxRLeg
    LFoot: MobVoxLFoot
    RFoot: MobVoxRFoot
    Tail: MobHumanoidAnyMarking

- type: markingPoints
  id: MobVoxMarkingLimits
  onlyWhitelisted: true
  points:
    Hair:
      points: 1
      required: false
    FacialHair:
      points: 1
      required: false
    Head:
      points: 1
      required: true
    Snout:
      points: 1
      required: true
      defaultMarkings: [ VoxBeak ]
    RightArm:
      points: 1
      required: true
      defaultMarkings: [ VoxRArmScales ]
    RightHand:
      points: 1
      required: true
      defaultMarkings: [ VoxRHandScales ]
    LeftArm:
      points: 1
      required: true
      defaultMarkings: [ VoxLArmScales ]
    LeftHand:
      points: 1
      required: true
      defaultMarkings: [ VoxLHandScales ]
    RightLeg:
      points: 1
      required: true
      defaultMarkings: [ VoxRLegScales ]
    LeftLeg:
      points: 1
      required: true
      defaultMarkings: [ VoxLLegScales ]
    RightFoot:
      points: 1
      required: true
      defaultMarkings: [ VoxRFootScales ]
    LeftFoot:
      points: 1
      required: true
      defaultMarkings: [ VoxLFootScales ]
    Chest:
      points: 1
      required: false
    Tail:
      points: 1
      required: true
      defaultMarkings: [ VoxTail ]

- type: humanoidBaseSprite
  id: MobVoxEyes
  baseSprite:
    sprite: Mobs/Species/Vox/parts.rsi
    state: eyes

- type: humanoidBaseSprite
  id: MobVoxHead
  baseSprite:
    sprite: Mobs/Species/Vox/parts.rsi
    state: head

- type: humanoidBaseSprite
  id: MobVoxHeadMale
  baseSprite:
    sprite: Mobs/Species/Vox/parts.rsi
    state: head

- type: humanoidBaseSprite
  id: MobVoxHeadFemale
  baseSprite:
    sprite: Mobs/Species/Vox/parts.rsi
    state: head

- type: humanoidBaseSprite
  id: MobVoxTorso
  baseSprite:
    sprite: Mobs/Species/Vox/parts.rsi
    state: torso

- type: humanoidBaseSprite
  id: MobVoxTorsoMale
  baseSprite:
    sprite: Mobs/Species/Vox/parts.rsi
    state: torso

- type: humanoidBaseSprite
  id: MobVoxTorsoFemale
  baseSprite:
    sprite: Mobs/Species/Vox/parts.rsi
    state: torso

- type: humanoidBaseSprite
  id: MobVoxLLeg
  baseSprite:
    sprite: Mobs/Species/Vox/parts.rsi
    state: l_leg

- type: humanoidBaseSprite
  id: MobVoxLArm
  baseSprite:
    sprite: Mobs/Species/Vox/parts.rsi
    state: l_arm

- type: humanoidBaseSprite
  id: MobVoxLHand
  baseSprite:
    sprite: Mobs/Species/Vox/parts.rsi
    state: l_hand

- type: humanoidBaseSprite
  id: MobVoxLFoot
  baseSprite:
    sprite: Mobs/Species/Vox/parts.rsi
    state: l_foot

- type: humanoidBaseSprite
  id: MobVoxRLeg
  baseSprite:
    sprite: Mobs/Species/Vox/parts.rsi
    state: r_leg

- type: humanoidBaseSprite
  id: MobVoxRArm
  baseSprite:
    sprite: Mobs/Species/Vox/parts.rsi
    state: r_arm

- type: humanoidBaseSprite
  id: MobVoxRHand
  baseSprite:
    sprite: Mobs/Species/Vox/parts.rsi
    state: r_hand

- type: humanoidBaseSprite
  id: MobVoxRFoot
  baseSprite:
    sprite: Mobs/Species/Vox/parts.rsi
    state: r_foot
