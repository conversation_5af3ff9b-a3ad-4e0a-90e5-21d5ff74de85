- type: entity
  abstract: true
  parent: BaseItem
  id: SheetGlassBase
  name: "скло"
  description: "Лист скла, який часто використовується на станції в різних сферах."
  components:
  - type: Sprite
    sprite: Objects/Materials/Sheets/glass.rsi
  - type: Item
    sprite: Objects/Materials/Sheets/glass.rsi
    size: Normal
  - type: StaticPrice
    price: 0
  - type: Tag
    tags:
    - Sheet
    - NoPaint
  - type: Material
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Glass
  - type: Appearance
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
          params:
            volume: -4
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlass:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: SolutionContainerManager
    solutions:
      glass:
        canReact: false
  # Estacao Pirata - IPC Healing
  - type: BlindHealing
    damageContainers:
      - Silicon
  - type: StackPrice
    price: 2

- type: entity
  parent: SheetGlassBase
  id: SheetGlass
  suffix: Full
  components:
  - type: PhysicalComposition
    materialComposition:
      Glass: 100
  - type: Stack
    stackType: Glass
    baseLayer: base
    layerStates:
    - glass
    - glass_2
    - glass_3
  - type: Sprite
    state: glass_3
    layers:
    - state: glass_3
      map: ["base"]
  - type: Item
    heldPrefix: glass
  - type: FloorTile
    outputs:
    - FloorGlass
  - type: Construction
    graph: Glass
    node: SheetGlass
  - type: Extractable
    grindableSolutionName: glass
  - type: SolutionContainerManager
    solutions:
      glass:
        reagents:
        - ReagentId: Silicon
          Quantity: 10
  - type: UserInterface
    interfaces:
      enum.RadialSelectorUiKey.Key:
        type: RadialSelectorMenuBUI
  - type: ActivatableUI
    key: enum.RadialSelectorUiKey.Key
    inHandsOnly: true
    requireActiveHand: false
  - type: ShortConstruction
    entries:
    - prototype: Window
    - prototype: WindowDirectional
    - prototype: WindowDiagonal
    - prototype: SheetRGlass
    - prototype: SheetPGlass
    - prototype: SheetUGlass

- type: entity
  parent: SheetGlass
  id: SheetGlass10
  suffix: 10
  components:
  - type: Sprite
    state: glass
  - type: Stack
    stackType: Glass
    count: 10

- type: entity
  parent: SheetGlass
  id: SheetGlass1
  suffix: Single
  components:
  - type: Sprite
    state: glass
  - type: Stack
    stackType: Glass
    count: 1

- type: entity
  parent: SheetGlass
  id: SheetGlassLingering0
  suffix: Lingering, 0
  components:
  - type: Stack
    lingering: true
    count: 0

- type: entity
  parent: SheetGlassBase
  id: SheetRGlass
  name: "армоване скло"
  description: "Армований лист скла."
  suffix: Full
  components:
  - type: PhysicalComposition
    materialComposition:
      ReinforcedGlass: 100
  - type: Stack
    stackType: ReinforcedGlass
    baseLayer: base
    layerStates:
    - rglass
    - rglass_2
    - rglass_3
  - type: Sprite
    state: rglass_3
    layers:
    - state: rglass_3
      map: ["base"]
  - type: Item
    heldPrefix: rglass
  - type: FloorTile
    outputs:
    - FloorRGlass
  - type: Construction
    graph: Glass
    node: SheetRGlass
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
          params:
            volume: -4
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassReinforced:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: UserInterface
    interfaces:
      enum.RadialSelectorUiKey.Key:
        type: RadialSelectorMenuBUI
  - type: ActivatableUI
    key: enum.RadialSelectorUiKey.Key
    inHandsOnly: true
    requireActiveHand: false
  - type: ShortConstruction
    entries:
    - prototype: SheetRPGlass1
    - prototype: SheetRUGlass1
    - prototype: ReinforcedWindow
    - prototype: ReinforcedWindowDiagonal
    - prototype: WindowReinforcedDirectional

- type: entity
  parent: SheetRGlass
  id: SheetRGlass1
  name: "армоване скло"
  suffix: Single
  components:
  - type: Sprite
    state: rglass
  - type: Stack
    stackType: ReinforcedGlass
    count: 1
  - type: Extractable
    grindableSolutionName: rglass
  - type: SolutionContainerManager
    solutions:
      rglass:
        reagents:
        - ReagentId: Silicon
          Quantity: 10
        - ReagentId: Iron
          Quantity: 4.5
        - ReagentId: Carbon
          Quantity: 0.5
        canReact: false

- type: entity
  parent: SheetRGlass
  id: SheetRGlass10
  name: "армоване скло"
  suffix: Single
  components:
  - type: Sprite
    state: rglass
  - type: Stack
    stackType: ReinforcedGlass
    count: 10

- type: entity
  parent: SheetGlassBase
  id: SheetPGlass
  name: "плазмове скло"
  description: "Лист напівпрозорої плазми."
  suffix: Full
  components:
  - type: PhysicalComposition
    materialComposition:
      PlasmaGlass: 100
  - type: Stack
    stackType: PlasmaGlass
    baseLayer: base
    layerStates:
    - pglass
    - pglass_2
    - pglass_3
  - type: Sprite
    state: pglass_3
    layers:
    - state: pglass_3
      map: ["base"]
  - type: Item
    heldPrefix: pglass
  - type: Construction
    graph: Glass
    node: SheetPGlass
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
          params:
            volume: -4
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassPlasma:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: UserInterface
    interfaces:
      enum.RadialSelectorUiKey.Key:
        type: RadialSelectorMenuBUI
  - type: ActivatableUI
    key: enum.RadialSelectorUiKey.Key
    inHandsOnly: true
    requireActiveHand: false
  - type: ShortConstruction
    entries:
    - prototype: SheetRPGlass0
    - prototype: PlasmaWindow
    - prototype: PlasmaWindowDiagonal
    - prototype: PlasmaWindowDirectional

- type: entity
  parent: SheetPGlass
  id: SheetPGlass1
  name: "плазмове скло"
  suffix: Single
  components:
  - type: Sprite
    state: pglass
  - type: Stack
    stackType: PlasmaGlass
    count: 1
  - type: Extractable
    grindableSolutionName: pglass
  - type: SolutionContainerManager
    solutions:
      pglass:
        reagents:
        - ReagentId: Silicon
          Quantity: 10
        - ReagentId: Plasma
          Quantity: 10
        canReact: false

- type: entity
  parent: SheetPGlass
  id: SheetPGlass10
  name: "плазмове скло"
  suffix: 10
  components:
  - type: Sprite
    state: pglass
  - type: Stack
    stackType: PlasmaGlass
    count: 10

- type: entity
  parent: SheetPGlass
  id: SheetRPGlass
  name: "армоване плазмове скло"
  description: "Армований лист напівпрозорої плазми."
  suffix: Full
  components:
  - type: PhysicalComposition
    materialComposition:
      ReinforcedPlasmaGlass: 100
  - type: Stack
    stackType: ReinforcedPlasmaGlass
    baseLayer: base
    layerStates:
    - rpglass
    - rpglass_2
    - rpglass_3
  - type: Sprite
    state: rpglass_3
    layers:
    - state: rpglass_3
      map: ["base"]
  - type: Item
    heldPrefix: rpglass
  - type: Construction
    graph: Glass
    node: SheetRPGlass
  - type: Extractable
    grindableSolutionName: rpglass
  - type: SolutionContainerManager
    solutions:
      rpglass:
        reagents:
        - ReagentId: Silicon
          Quantity: 10
        - ReagentId: Plasma
          Quantity: 10
        - ReagentId: Iron
          Quantity: 4.5
        - ReagentId: Carbon
          Quantity: 0.5
        canReact: false
  - type: UserInterface
    interfaces:
      enum.RadialSelectorUiKey.Key:
        type: RadialSelectorMenuBUI
  - type: ActivatableUI
    key: enum.RadialSelectorUiKey.Key
    inHandsOnly: true
    requireActiveHand: false
  - type: ShortConstruction
    entries:
    - prototype: ReinforcedPlasmaWindow
    - prototype: ReinforcedPlasmaWindowDiagonal
    - prototype: PlasmaReinforcedWindowDirectional

- type: entity
  parent: SheetRPGlass
  id: SheetRPGlass1
  name: "армоване плазмове скло"
  suffix: Single
  components:
  - type: Sprite
    state: rpglass
  - type: Stack
    stackType: ReinforcedPlasmaGlass
    count: 1

- type: entity
  parent: SheetRPGlass
  id: SheetRPGlass10
  name: "армоване плазмове скло"
  suffix: Single
  components:
  - type: Sprite
    state: rpglass
  - type: Stack
    stackType: ReinforcedPlasmaGlass
    count: 10

- type: entity
  parent: SheetGlassBase
  id: SheetUGlass
  name: "уранове скло"
  description: "Лист уранового скла."
  suffix: Full
  components:
  - type: PhysicalComposition
    materialComposition:
      UraniumGlass: 100
  - type: Stack
    stackType: UraniumGlass
    baseLayer: base
    layerStates:
    - uglass
    - uglass_2
    - uglass_3
  - type: Sprite
    state: uglass_3
    layers:
    - state: uglass_3
      map: ["base"]
  - type: Item
    heldPrefix: uglass
  - type: Construction
    graph: Glass
    node: SheetUGlass
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
          params:
            volume: -4
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassUranium:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Extractable
    grindableSolutionName: uglass
  - type: SolutionContainerManager
    solutions:
      uglass:
        reagents:
        - ReagentId: Silicon
          Quantity: 10
        - ReagentId: Uranium
          Quantity: 10
        canReact: false
  - type: UserInterface
    interfaces:
      enum.RadialSelectorUiKey.Key:
        type: RadialSelectorMenuBUI
  - type: ActivatableUI
    key: enum.RadialSelectorUiKey.Key
    inHandsOnly: true
    requireActiveHand: false
  - type: ShortConstruction
    entries:
    - prototype: SheetRUGlass0
    - prototype: UraniumWindow
    - prototype: UraniumWindowDiagonal
    - prototype: UraniumWindowDirectional

- type: entity
  parent: SheetUGlass
  id: SheetUGlass1
  name: "уранове скло"
  suffix: Single
  components:
  - type: Sprite
    state: uglass
  - type: Stack
    stackType: UraniumGlass
    count: 1

- type: entity
  parent: SheetUGlass
  id: SheetUGlass10
  name: "уранове скло"
  suffix: 10
  components:
  - type: Sprite
    state: uglass
  - type: Stack
    stackType: UraniumGlass
    count: 10

- type: entity
  parent: SheetUGlass
  id: SheetRUGlass
  name: "армоване уранове скло"
  description: "Армований лист уранового скла."
  components:
  - type: PhysicalComposition
    materialComposition:
      ReinforcedUraniumGlass: 100
  - type: Stack
    stackType: ReinforcedUraniumGlass
    baseLayer: base
    layerStates:
    - ruglass
    - ruglass_2
    - ruglass_3
  - type: Sprite
    state: ruglass_3
    layers:
    - state: ruglass_3
      map: ["base"]
  - type: Item
    heldPrefix: ruglass
  - type: Construction
    graph: Glass
    node: SheetRUGlass
  - type: Extractable
    grindableSolutionName: ruglass
  - type: SolutionContainerManager
    solutions:
      ruglass:
        reagents:
        - ReagentId: Silicon
          Quantity: 10
        - ReagentId: Uranium
          Quantity: 10
        - ReagentId: Iron
          Quantity: 4.5
        - ReagentId: Carbon
          Quantity: 0.5
        canReact: false
  - type: UserInterface
    interfaces:
      enum.RadialSelectorUiKey.Key:
        type: RadialSelectorMenuBUI
  - type: ActivatableUI
    key: enum.RadialSelectorUiKey.Key
    inHandsOnly: true
    requireActiveHand: false
  - type: ShortConstruction
    entries:
    - prototype: ReinforcedUraniumWindow
    - prototype: ReinforcedUraniumWindowDiagonal
    - prototype: UraniumReinforcedWindowDirectional

- type: entity
  parent: SheetRUGlass
  id: SheetRUGlass1
  name: "армоване уранове скло"
  suffix: Single
  components:
  - type: Sprite
    state: ruglass
  - type: Stack
    stackType: ReinforcedUraniumGlass
    count: 1

- type: entity
  parent: SheetRUGlass
  id: SheetRUGlass10
  name: "армоване уранове скло"
  suffix: 10
  components:
  - type: Sprite
    state: ruglass
  - type: Stack
    stackType: ReinforcedUraniumGlass
    count: 10

- type: entity
  parent: SheetGlassBase
  id: SheetClockworkGlass
  name: "годинникове скло"
  description: "Армований латунню лист скла."
  suffix: Full
  components:
  - type: PhysicalComposition
    materialComposition:
      BrassGlass: 100
  - type: Stack
    stackType: ClockworkGlass
    baseLayer: base
    layerStates:
    - cglass
    - cglass_2
    - cglass_3
  - type: Sprite
    state: cglass_3
    layers:
    - state: cglass_3
      map: ["base"]
  - type: Item
    heldPrefix: cglass
  - type: Construction
    graph: Glass
    node: SheetClockworkGlass
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
          params:
            volume: -4
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassClockwork:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Extractable
    grindableSolutionName: brassglass
  - type: SolutionContainerManager
    solutions:
      cglass:
        reagents:
        - ReagentId: Silicon
          Quantity: 10
        - ReagentId: Zinc
          Quantity: 3.3
        - ReagentId: Copper
          Quantity: 6.7
        canReact: false

- type: entity
  parent: SheetClockworkGlass
  id: SheetClockworkGlass1
  name: "годинникове скло"
  suffix: Single
  components:
  - type: Sprite
    state: cglass
  - type: Stack
    stackType: ClockworkGlass
    count: 1

- type: entity
  parent: SheetClockworkGlass
  id: SheetClockworkGlass10
  name: "годинникове скло"
  suffix: 10
  components:
  - type: Sprite
    state: cglass
  - type: Stack
    stackType: ClockworkGlass
    count: 10
