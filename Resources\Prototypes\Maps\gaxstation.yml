- type: gameMap
  id: Gax
  mapName: 'Гакс'
  mapPath: /Maps/gax.yml
  minPlayers: 5
  fallback: true
  stations:
    NCS Gax:
      stationProto: StandardNanotrasenStationCargoOnly
      components:
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/DeltaV/NTES_Delta.yml
        - type: StationNameSetup
          mapNameTemplate: '{0} NVS Гакс {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: 'YG'
        - type: StationJobs
          availableJobs:
            #service
            Captain: [ 1, 1 ]
            NanotrasenRepresentative: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1 ]
            # Magistrate: [ 1, 1 ] # EE: Disabled
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 1, 1 ]
            Botanist: [3 , 3 ]
            Chef: [ 2, 2 ]
            Janitor: [ 2, 2 ]
            ServiceWorker: [ 2, 2 ]
            #Engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 4, 4 ]
            StationEngineer: [ 4, 4 ]
            TechnicalAssistant: [ 2, 2 ]
            #Medical
            ChiefMedicalOfficer: [ 1, 1 ]
            Chemist: [ 2, 2 ]
            MedicalDoctor: [ 3, 3 ]
            MedicalIntern: [ 3, 3 ]
            Paramedic: [ 2, 2 ]
            #Science
            ResearchDirector: [ 1, 1 ]
            ForensicMantis: [ 1, 1 ]
            Scientist: [ 4, 4 ]
            Roboticist: [ 1, 2 ]
            Librarian: [ 1, 1 ]
            Chaplain: [ 1, 1 ]
            ResearchAssistant: [ 2, 2 ]
            #Security
            HeadOfSecurity: [ 1, 1 ]
            Warden: [ 1, 1 ]
            SecurityOfficer: [ 4, 4 ]
            SecurityCadet: [ 4, 4 ]
            Detective: [ 1, 1 ]
            Prisoner: [ 1, 2 ]
            # PrisonGuard: [ 1, 2 ] # EE: Disabled
            Brigmedic: [ 1, 1 ]
            Lawyer: [ 1, 1 ]
            #Supply
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ 1, 3 ]
            CargoTechnician: [ 2, 2 ]
            MailCarrier: [ 1, 2 ]
            #Civilian
            Passenger: [ -1, -1 ]
            Clown: [ 1, 1 ]
            Mime: [ 1, 1 ]
            Musician: [ 1, 1 ]
            #Silicon
            StationAi: [ 1, 1 ]
            Borg: [ 2, 3 ]
            MedicalBorg: [ 1, 2 ]
        # blob-config-start MEDIUM
        - type: StationBlobConfig
          stageBegin: 30
          stageCritical: 375
          stageTheEnd: 750
        # blob-config-end
