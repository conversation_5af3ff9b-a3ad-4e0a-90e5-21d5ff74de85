- type: mappingTemplate
  id: Common
  rootType: Entity
  children:
    - !type:MappingTemplatePrototype
      id: RandomPosterContraband
    - !type:MappingTemplatePrototype
      id: RandomPosterLegit
    - !type:MappingTemplatePrototype
      id: RandomPosterAny
    - !type:MappingTemplatePrototype
      id: FireAlarm
    - !type:MappingTemplatePrototype
      id: Firelock
    - !type:MappingTemplatePrototype
      id: AirAlarm
    - !type:MappingTemplatePrototype
      id: GasVentScrubber
    - !type:MappingTemplatePrototype
      id: AirSensor
    - !type:MappingTemplatePrototype
      id: APCBasic
    - !type:MappingTemplatePrototype
      id: CableApcExtension
    - !type:MappingTemplatePrototype
      id: CableMV
    - !type:MappingTemplatePrototype
      id: CableHV
    - !type:MappingTemplatePrototype
      id: WarpPointBombing
    - !type:MappingTemplatePrototype
      id: DisposalUnit
    - !type:MappingTemplatePrototype
      id: RandomVending
    - !type:MappingTemplatePrototype
      id: RandomVendingDrinks
    - !type:MappingTemplatePrototype
      id: RandomVendingSnacks
    - !type:MappingTemplatePrototype
      id: VendingMachineCigs
    - !type:MappingTemplatePrototype
      id: PottedPlantRandom
    - !type:MappingTemplatePrototype
      id: PottedPlantRandomPlastic
    - !type:MappingTemplatePrototype
      id: ClosetEmergencyFilledRandom
    - !type:MappingTemplatePrototype
      id: ClosetEmergencyN2FilledRandom
    - !type:MappingTemplatePrototype
      id: WallmountTelevision
    - !type:MappingTemplatePrototype
      id: Screen
    - !type:MappingTemplatePrototype
      id: Poweredlight
    - !type:MappingTemplatePrototype
      id: EmergencyLight
    - !type:MappingTemplatePrototype
      id: JanitorServiceLight
    - !type:MappingTemplatePrototype
      id: SpawnMobMouse
    - !type:MappingTemplatePrototype
      id: MouseTimedSpawner
    - !type:MappingTemplatePrototype
      id: SpawnMobCockroach
    - !type:MappingTemplatePrototype
      id: DefibrillatorCabinetFilled
    - !type:MappingTemplatePrototype
      id: StationMap
    - !type:MappingTemplatePrototype
      id: SurveillanceCameraGeneral

- type: mappingTemplate
  id: Service
  rootType: Entity
  children:
    - !type:MappingTemplatePrototype
      id: Bar
      children:
        - !type:MappingTemplatePrototype
          id: BoozeDispenser
        - !type:MappingTemplatePrototype
          id: SodaDispenser
        - !type:MappingTemplatePrototype
          id: VendingMachineBooze
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconBar
        - !type:MappingTemplatePrototype
          id: SurveillanceCameraService
        - !type:MappingTemplatePrototype
          id: Sink
        - !type:MappingTemplatePrototype
          id: Jukebox
        - !type:MappingTemplatePrototype
          id: KitchenReagentGrinder
        - !type:MappingTemplatePrototype
          id: RandomDrinkBottle
        - !type:MappingTemplatePrototype
          id: RandomDrinkGlass
        - !type:MappingTemplatePrototype
          id: SpawnMobMonkeyPunpun
        - !type:MappingTemplatePrototype
          id: VendingBarDrobe
        - !type:MappingTemplatePrototype
          id: LockerBoozeFilled
        - !type:MappingTemplatePrototype
          id: SpawnPointBartender
        - !type:MappingTemplatePrototype
          id: NuclearBombKeg
    - !type:MappingTemplatePrototype
      id: Kitchen
      children:
        - !type:MappingTemplatePrototype
          id: KitchenMicrowave
        - !type:MappingTemplatePrototype
          id: VendingMachineDinnerware
        - !type:MappingTemplatePrototype
          id: Sink
        - !type:MappingTemplatePrototype
          id: KitchenReagentGrinder
        - !type:MappingTemplatePrototype
          id: VendingMachineChefDrobe
        - !type:MappingTemplatePrototype
          id: ClosetChefFilled
        - !type:MappingTemplatePrototype
          id: VendingMachineCondiments
        - !type:MappingTemplatePrototype
          id: SpawnMobAlexander
        - !type:MappingTemplatePrototype
          id: RandomFoodMeal
        - !type:MappingTemplatePrototype
          id: RandomFoodSingle
    - !type:MappingTemplatePrototype
      id: Fridge
      children:
        - !type:MappingTemplatePrototype
          id: AtmosFixFreezerMarker
        - !type:MappingTemplatePrototype
          id: FloorDrain
        - !type:MappingTemplatePrototype
          id: KitchenSpike
        - !type:MappingTemplatePrototype
          id: CrateNPCCow
        - !type:MappingTemplatePrototype
          id: FoodCartCold
        - !type:MappingTemplatePrototype
          id: FoodCartHot
    - !type:MappingTemplatePrototype
      id: Hydroponics
      children:
        - !type:MappingTemplatePrototype
          id: SpawnPointBotanist
        - !type:MappingTemplatePrototype
          id: hydroponicsTray
        - !type:MappingTemplatePrototype
          id: SeedExtractor
        - !type:MappingTemplatePrototype
          id: VendingMachineSeeds
        - !type:MappingTemplatePrototype
          id: VendingMachineNutri
        - !type:MappingTemplatePrototype
          id: WaterTankHighCapacity
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconBotany
        - !type:MappingTemplatePrototype
          id: VendingMachineHydrobe
        - !type:MappingTemplatePrototype
          id: WardrobeBotanistFilled
        - !type:MappingTemplatePrototype
          id: SpawnMobBandito
    - !type:MappingTemplatePrototype
      id: JanitorsCloset
      children:
        - !type:MappingTemplatePrototype
          id: SpawnPointJanitor
        - !type:MappingTemplatePrototype
          id: VendingMachineJaniDrobe
        - !type:MappingTemplatePrototype
          id: JanitorialTrolley
        - !type:MappingTemplatePrototype
          id: ClosetJanitorFilled
        - !type:MappingTemplatePrototype
          id: CleanerDispenser
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconJanitorsCloset
        - !type:MappingTemplatePrototype
          id: ClosetL3JanitorFilled
        - !type:MappingTemplatePrototype
          id: SpawnMobRaccoonMorticia
    - !type:MappingTemplatePrototype
      id: Theater
      children:
        - !type:MappingTemplatePrototype
          id: VendingMachineTheater
        - !type:MappingTemplatePrototype
          id: RandomInstruments
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconTheater
        - !type:MappingTemplatePrototype
          id: VendingMachineHappyHonk
        - !type:MappingTemplatePrototype
          id: SpawnPointClown
        - !type:MappingTemplatePrototype
          id: FoodPieBananaCream
        - !type:MappingTemplatePrototype
          id: BedsheetClown
        - !type:MappingTemplatePrototype
          id: LauncherCreamPie
        - !type:MappingTemplatePrototype
          id: SpawnPointMime
        - !type:MappingTemplatePrototype
          id: LockerMime
        - !type:MappingTemplatePrototype
          id: DrinkBottleOfNothingFull
        - !type:MappingTemplatePrototype
          id: BedsheetMime
        - !type:MappingTemplatePrototype
          id: FoodTartMime
        - !type:MappingTemplatePrototype
          id: SpawnPointMusician
    - !type:MappingTemplatePrototype
      id: ReportersOffice
      children:
        - !type:MappingTemplatePrototype
          id: ComputerSurveillanceWirelessCameraMonitor
        - !type:MappingTemplatePrototype
          id: ComputerMassMedia
        - !type:MappingTemplatePrototype
          id: SurveillanceWirelessCameraMovableConstructed
        - !type:MappingTemplatePrototype
          id: SurveillanceWirelessCameraAnchoredConstructed
        - !type:MappingTemplatePrototype
          id: SpawnPointReporter
    - !type:MappingTemplatePrototype
      id: Church
      children:
        - !type:MappingTemplatePrototype
          id: VendingMachineChapel
        - !type:MappingTemplatePrototype
          id: SpawnPointChaplain
        - !type:MappingTemplatePrototype
          id: WardrobeChapelFilled
        - !type:MappingTemplatePrototype
          id: Crematorium
        - !type:MappingTemplatePrototype
          id: CrateCoffin
        - !type:MappingTemplatePrototype
          id: AltarSpawner
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconChapel
        - !type:MappingTemplatePrototype
          id: CarpetChapel

- type: mappingTemplate
  id: Cargo
  rootType: Entity
  children:
    - !type:MappingTemplatePrototype
      id: ComputerCargoOrders
    - !type:MappingTemplatePrototype
      id: Autolathe
    - !type:MappingTemplatePrototype
      id: IntercomSupply
    - !type:MappingTemplatePrototype
      id: VendingMachineChefDrobe
    - !type:MappingTemplatePrototype
      id: ComputerShuttleCargo
    - !type:MappingTemplatePrototype
      id: ComputerCargoBounty
    - !type:MappingTemplatePrototype
      id: SpawnPointCargoTechnician
    - !type:MappingTemplatePrototype
      id: DefaultStationBeaconSupply
    - !type:MappingTemplatePrototype
      id: SurveillanceCameraSupply
    - !type:MappingTemplatePrototype
      id: ConveyorBelt
    - !type:MappingTemplatePrototype
      id: MailingUnit
    - !type:MappingTemplatePrototype
      id: QMRoom
      children:
        - !type:MappingTemplatePrototype
          id: SpawnPointQuartermaster
        - !type:MappingTemplatePrototype
          id: DresserQuarterMasterFilled
        - !type:MappingTemplatePrototype
          id: LockerQuarterMasterFilled
        - !type:MappingTemplatePrototype
          id: BedsheetQM
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconQMRoom
    - !type:MappingTemplatePrototype
      id: Salvage
      children:
        - !type:MappingTemplatePrototype
          id: LockerSalvageSpecialistFilled
        - !type:MappingTemplatePrototype
          id: SuitStorageSalv
        - !type:MappingTemplatePrototype
          id: VendingMachineSalvage
        - !type:MappingTemplatePrototype
          id: OreProcessor
        - !type:MappingTemplatePrototype
          id: SpawnPointSalvageSpecialist
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconSalvage
        - !type:MappingTemplatePrototype
          id: WardrobeSalvageFilled
        - !type:MappingTemplatePrototype
          id: ComputerRadar

- type: mappingTemplate
  id: Command
  rootType: Entity
  children:
    - !type:MappingTemplatePrototype
      id: Bridge
      children:
        - !type:MappingTemplatePrototype
          id: ComputerComms
        - !type:MappingTemplatePrototype
          id: ComputerId
        - !type:MappingTemplatePrototype
          id: ComputerStationRecords
        - !type:MappingTemplatePrototype
          id: ComputerCriminalRecords
        - !type:MappingTemplatePrototype
          id: ComputerPowerMonitoring
        - !type:MappingTemplatePrototype
          id: ComputerCrewMonitoring
        - !type:MappingTemplatePrototype
          id: ComputerCargoOrders
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconBridge
        - !type:MappingTemplatePrototype
          id: SurveillanceCameraCommand
        - !type:MappingTemplatePrototype
          id: IntercomCommand
    - !type:MappingTemplatePrototype
      id: CaptainsQuarters
      children:
        - !type:MappingTemplatePrototype
          id: SpawnPointCaptain
        - !type:MappingTemplatePrototype
          id: LockerCaptainFilled
        - !type:MappingTemplatePrototype
          id: LockerCaptainFilledNoLaser
        - !type:MappingTemplatePrototype
          id: DresserCaptainFilled
        - !type:MappingTemplatePrototype
          id: CaptainIDCard
        - !type:MappingTemplatePrototype
          id: ToiletGoldenEmpty
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconCaptainsQuarters
        - !type:MappingTemplatePrototype
          id: PinpointerNuclear
        - !type:MappingTemplatePrototype
          id: SpawnMobFoxRenault
        - !type:MappingTemplatePrototype
          id: DrinkFlask
    - !type:MappingTemplatePrototype
      id: HOPOffice
      children:
        - !type:MappingTemplatePrototype
          id: LockerHeadOfPersonnelFilled
        - !type:MappingTemplatePrototype
          id: DresserHeadOfPersonnelFilled
        - !type:MappingTemplatePrototype
          id: SpawnMobCorgi
        - !type:MappingTemplatePrototype
          id: VendingMachineCart
        - !type:MappingTemplatePrototype
          id: CrateServicePersonnel
        - !type:MappingTemplatePrototype
          id: SpawnPointHeadOfPersonnel
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconHOPOffice
        - !type:MappingTemplatePrototype
          id: RubberStampApproved
        - !type:MappingTemplatePrototype
          id: RubberStampDenied
        - !type:MappingTemplatePrototype
          id: MaterialCloth
        - !type:MappingTemplatePrototype
          id: MaterialDurathread
        - !type:MappingTemplatePrototype
          id: BoxID
        - !type:MappingTemplatePrototype
          id: BoxPDA

- type: mappingTemplate
  id: Medical
  rootType: Entity
  children:
    - !type:MappingTemplatePrototype
      id: Chemistry
      children:
        - !type:MappingTemplatePrototype
          id: ChemMaster
        - !type:MappingTemplatePrototype
          id: ChemDispenser
        - !type:MappingTemplatePrototype
          id: LockerChemistryFilled
        - !type:MappingTemplatePrototype
          id: VendingMachineChemicals
        - !type:MappingTemplatePrototype
          id: FloorDrain
        - !type:MappingTemplatePrototype
          id: KitchenReagentGrinder
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconChemistry
        - !type:MappingTemplatePrototype
          id: VendingMachineChemDrobe
        - !type:MappingTemplatePrototype
          id: ChemistryHotplate
        - !type:MappingTemplatePrototype
          id: BoxBeaker
        - !type:MappingTemplatePrototype
          id: LargeBeaker
        - !type:MappingTemplatePrototype
          id: Beaker
        - !type:MappingTemplatePrototype
          id: MachineElectrolysisUnit
        - !type:MappingTemplatePrototype
          id: MachineCentrifuge
        - !type:MappingTemplatePrototype
          id: BaseGasCondenser
        - !type:MappingTemplatePrototype
          id: FuelDispenser
        - !type:MappingTemplatePrototype
          id: SmartFridge
        - !type:MappingTemplatePrototype
          id: HandLabeler
        - !type:MappingTemplatePrototype
          id: SpawnMobWalter
    - !type:MappingTemplatePrototype
      id: Medbay
      children:
        - !type:MappingTemplatePrototype
          id: MedicalTechFab
        - !type:MappingTemplatePrototype
          id: DefibrillatorCabinetFilled
        - !type:MappingTemplatePrototype
          id: VendingMachineMediDrobe
        - !type:MappingTemplatePrototype
          id: LockerMedicineFilled
        - !type:MappingTemplatePrototype
          id: LockerMedicalFilled
        - !type:MappingTemplatePrototype
          id: LockerParamedicFilled
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconMedbay
        - !type:MappingTemplatePrototype
          id: SurveillanceCameraMedical
        - !type:MappingTemplatePrototype
          id: IntercomMedical
        - !type:MappingTemplatePrototype
          id: VendingMachineWallMedical
        - !type:MappingTemplatePrototype
          id: VendingMachineMedical
        - !type:MappingTemplatePrototype
          id: EmergencyRollerBed
        - !type:MappingTemplatePrototype
          id: CheapRollerBedSpawnFolded
        - !type:MappingTemplatePrototype
          id: CheapRollerBed
        - !type:MappingTemplatePrototype
          id: RollerBedSpawnFolded
        - !type:MappingTemplatePrototype
          id: RollerBed
        - !type:MappingTemplatePrototype
          id: Brutepack
        - !type:MappingTemplatePrototype
          id: Ointment
        - !type:MappingTemplatePrototype
          id: Gauze
        - !type:MappingTemplatePrototype
          id: Bloodpack
        - !type:MappingTemplatePrototype
          id: MedkitFilled
        - !type:MappingTemplatePrototype
          id: MedkitOxygenFilled
        - !type:MappingTemplatePrototype
          id: MedkitBruteFilled
        - !type:MappingTemplatePrototype
          id: MedkitToxinFilled
        - !type:MappingTemplatePrototype
          id: MedkitBurnFilled
        - !type:MappingTemplatePrototype
          id: ClothingHandsGlovesColorWhite
        - !type:MappingTemplatePrototype
          id: BoxPillCanister
        - !type:MappingTemplatePrototype
          id: PillCanister
        - !type:MappingTemplatePrototype
          id: PillCanisterRandom
        - !type:MappingTemplatePrototype
          id: WardrobeMedicalDoctorFilled
        - !type:MappingTemplatePrototype
          id: CrateMedical
        - !type:MappingTemplatePrototype
          id: BannerMedical
        - !type:MappingTemplatePrototype
          id: MedicalBed
        - !type:MappingTemplatePrototype
          id: StasisBed
        - !type:MappingTemplatePrototype
          id: MedkitFilled
        - !type:MappingTemplatePrototype
          id: MedkitOxygenFilled
        - !type:MappingTemplatePrototype
          id: MedkitBruteFilled
        - !type:MappingTemplatePrototype
          id: MedkitToxinFilled
        - !type:MappingTemplatePrototype
          id: MedkitBurnFilled
        - !type:MappingTemplatePrototype
          id: HospitalCurtains
        - !type:MappingTemplatePrototype
          id: HospitalCurtainsOpen
    - !type:MappingTemplatePrototype
      id: MorgueRoom
      children:
        - !type:MappingTemplatePrototype
          id: Morgue
        - !type:MappingTemplatePrototype
          id: FloorDrain
        - !type:MappingTemplatePrototype
          id: BiomassReclaimer
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconMorgue
        - !type:MappingTemplatePrototype
          id: BoxBodyBag
        - !type:MappingTemplatePrototype
          id: SmartFridge
        - !type:MappingTemplatePrototype
          id: OperatingTable
    - !type:MappingTemplatePrototype
      id: CMORoom
      children:
        - !type:MappingTemplatePrototype
          id: LockerChiefMedicalOfficerFilled
        - !type:MappingTemplatePrototype
          id: DresserChiefMedicalOfficerFilled
        - !type:MappingTemplatePrototype
          id: SuitStorageCMO
        - !type:MappingTemplatePrototype
          id: SpawnPointChiefMedicalOfficer
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconCMORoom
        - !type:MappingTemplatePrototype
          id: ComputerCrewMonitoring
        - !type:MappingTemplatePrototype
          id: BedsheetCMO
        - !type:MappingTemplatePrototype
          id: SpawnMobCatSpace
        - !type:MappingTemplatePrototype
          id: SpawnMobCat
        - !type:MappingTemplatePrototype
          id: SpawnMobCatBingus
        - !type:MappingTemplatePrototype
          id: SpawnMobCatRuntime
        - !type:MappingTemplatePrototype
          id: SpawnMobCatException
        - !type:MappingTemplatePrototype
          id: SpawnMobCatGeneric
        - !type:MappingTemplatePrototype
          id: SpawnMobCatKitten
    - !type:MappingTemplatePrototype
      id: Cryonics
      children:
        - !type:MappingTemplatePrototype
          id: CryoPod
        - !type:MappingTemplatePrototype
          id: GasThermoMachineFreezer
        - !type:MappingTemplatePrototype
          id: GasThermoMachineFreezerEnabled
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconCryonics
        - !type:MappingTemplatePrototype
          id: CryoxadoneBeakerSmall
    - !type:MappingTemplatePrototype
      id: OperatingRoom
      children:
        - !type:MappingTemplatePrototype
          id: OperatingTable
        - !type:MappingTemplatePrototype
          id: Drill
        - !type:MappingTemplatePrototype
          id: Hemostat
        - !type:MappingTemplatePrototype
          id: Scalpel
        - !type:MappingTemplatePrototype
          id: Saw
        - !type:MappingTemplatePrototype
          id: Cautery
        - !type:MappingTemplatePrototype
          id: Retractor
        - !type:MappingTemplatePrototype
          id: computerBodyScanner
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconSurgery
        - !type:MappingTemplatePrototype
          id: ClothingBackpackDuffelSurgeryFilled
        - !type:MappingTemplatePrototype
          id: CrateFreezer
        - !type:MappingTemplatePrototype
          id: CrateMedicalSurgery
        - !type:MappingTemplatePrototype
          id: CrateSurgery
        - !type:MappingTemplatePrototype
          id: ClothingHeadHatSurgcapBlue
        - !type:MappingTemplatePrototype
          id: ClothingHeadHatSurgcapPurple
        - !type:MappingTemplatePrototype
          id: ClothingHeadHatSurgcapGreen
    - !type:MappingTemplatePrototype
      id: Virology
      children:
        - !type:MappingTemplatePrototype
          id: DiseaseDiagnoser
        - !type:MappingTemplatePrototype
          id: Vaccinator
        - !type:MappingTemplatePrototype
          id: VendingMachineViroDrobe
        - !type:MappingTemplatePrototype
          id: BoxMouthSwab
        - !type:MappingTemplatePrototype
          id: ClothingHandsGlovesNitrile
        - !type:MappingTemplatePrototype
          id: WardrobeVirologyFilled

- type: mappingTemplate
  id: Science
  rootType: Entity
  children:
    - !type:MappingTemplatePrototype
      id: VendingMachineSciDrobe
    - !type:MappingTemplatePrototype
      id: LockerScienceFilled
    - !type:MappingTemplatePrototype
      id: SpawnPointScientist
    - !type:MappingTemplatePrototype
      id: SpawnPointResearchAssistant
    - !type:MappingTemplatePrototype
      id: SurveillanceCameraScience
    - !type:MappingTemplatePrototype
      id: IntercomScience
    - !type:MappingTemplatePrototype
      id: WardrobeScienceFilled
    - !type:MappingTemplatePrototype
      id: BannerScience
    - !type:MappingTemplatePrototype
      id: ResearchAndDevelopmentServer
    - !type:MappingTemplatePrototype
      id: DefaultStationBeaconServerRoom
    - !type:MappingTemplatePrototype
      id: CrewMonitoringServer
    - !type:MappingTemplatePrototype
      id: AnomalyGenerator
      children:
        - !type:MappingTemplatePrototype
          id: MachineAnomalyGenerator
        - !type:MappingTemplatePrototype
          id: MachineAnomalyVessel
        - !type:MappingTemplatePrototype
          id: MachineAPE
        - !type:MappingTemplatePrototype
          id: AnomalyScanner
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconAnomalyGenerator
        - !type:MappingTemplatePrototype
          id: SheetPlasma
    - !type:MappingTemplatePrototype
      id: Robotics
      children:
        - !type:MappingTemplatePrototype
          id: VendingMachineRoboDrobe
        - !type:MappingTemplatePrototype
          id: VendingMachineRobotics
        - !type:MappingTemplatePrototype
          id: BorgCharger
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconRobotics
        - !type:MappingTemplatePrototype
          id: ExosuitFabricator
    - !type:MappingTemplatePrototype
      id: ArtifactLab
      children:
        - !type:MappingTemplatePrototype
          id: MachineArtifactAnalyzer
        - !type:MappingTemplatePrototype
          id: ComputerAnalysisConsole
        - !type:MappingTemplatePrototype
          id: CrateArtifactContainer
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconArtifactLab
        - !type:MappingTemplatePrototype
          id: RandomArtifactSpawner
        - !type:MappingTemplatePrototype
          id: ClosetRadiationSuitFilled
        - !type:MappingTemplatePrototype
          id: ClosetBombFilled
        - !type:MappingTemplatePrototype
          id: ClosetL3ScienceFilled
    - !type:MappingTemplatePrototype
      id: RDRoom
      children:
        - !type:MappingTemplatePrototype
          id: SpawnPointResearchDirector
        - !type:MappingTemplatePrototype
          id: DresserResearchDirectorFilled
        - !type:MappingTemplatePrototype
          id: SuitStorageRD
        - !type:MappingTemplatePrototype
          id: LockerResearchDirectorFilled
        - !type:MappingTemplatePrototype
          id: ComputerRoboticsControl
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconRDRoom
        - !type:MappingTemplatePrototype
          id: BedsheetRD
        - !type:MappingTemplatePrototype
          id: SpawnMobSmile
        - !type:MappingTemplatePrototype
          id: PottedPlantRD
    - !type:MappingTemplatePrototype
      id: RnD
      children:
        - !type:MappingTemplatePrototype
          id: ComputerResearchAndDevelopment
        - !type:MappingTemplatePrototype
          id: Protolathe
        - !type:MappingTemplatePrototype
          id: Autolathe
        - !type:MappingTemplatePrototype
          id: CircuitImprinter
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconRND
        - !type:MappingTemplatePrototype
          id: CapacitorStockPart
        - !type:MappingTemplatePrototype
          id: MicroManipulatorStockPart
        - !type:MappingTemplatePrototype
          id: MatterBinStockPart

- type: mappingTemplate
  id: Engineering
  rootType: Entity
  children:
    - !type:MappingTemplatePrototype
      id: CERoom
      children:
        - !type:MappingTemplatePrototype
          id: DresserChiefEngineerFilled
        - !type:MappingTemplatePrototype
          id: SuitStorageCE
        - !type:MappingTemplatePrototype
          id: LockerChiefEngineerFilled
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconCERoom
        - !type:MappingTemplatePrototype
          id: BedsheetCE
    - !type:MappingTemplatePrototype
      id: Atmos
      children:
        - !type:MappingTemplatePrototype
          id: GasThermoMachineFreezer
        - !type:MappingTemplatePrototype
          id: GasThermoMachineHeater
        - !type:MappingTemplatePrototype
          id: PortableScrubber
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconAtmospherics
        - !type:MappingTemplatePrototype
          id: TegCirculator
        - !type:MappingTemplatePrototype
          id: TegCenter
        - !type:MappingTemplatePrototype
          id: HeatExchanger
        - !type:MappingTemplatePrototype
          id: LockerAtmosphericsFilled
        - !type:MappingTemplatePrototype
          id: SuitStorageAtmos
        - !type:MappingTemplatePrototype
          id: VendingMachineAtmosDrobe
        - !type:MappingTemplatePrototype
          id: WardrobeAtmosphericsFilled
        - !type:MappingTemplatePrototype
          id: SpawnMobCrabAtmos
        - !type:MappingTemplatePrototype
          id: NitrogenCanister
        - !type:MappingTemplatePrototype
          id: OxygenCanister
        - !type:MappingTemplatePrototype
          id: CarbonDioxideCanister
        - !type:MappingTemplatePrototype
          id: WaterVaporCanister
        - !type:MappingTemplatePrototype
          id: StorageCanister
        - !type:MappingTemplatePrototype
          id: PlasmaCanister
        - !type:MappingTemplatePrototype
          id: NitrousOxideCanister
    - !type:MappingTemplatePrototype
      id: Common
      children:
        - !type:MappingTemplatePrototype
          id: SpawnPointBorg
        - !type:MappingTemplatePrototype
          id: GravityGenerator
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconAnomalyGenerator
        - !type:MappingTemplatePrototype
          id: SMESBasic
        - !type:MappingTemplatePrototype
          id: ComputerPowerMonitoring
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconPowerBank
        - !type:MappingTemplatePrototype
          id: Materials
          children:
            - !type:MappingTemplatePrototype
              id: CableMVStack
            - !type:MappingTemplatePrototype
              id: CableApcStack
            - !type:MappingTemplatePrototype
              id: CableApcStack
            - !type:MappingTemplatePrototype
              id: SheetSteel
            - !type:MappingTemplatePrototype
              id: SheetGlass
            - !type:MappingTemplatePrototype
              id: SheetPlastic
            - !type:MappingTemplatePrototype
              id: PartRodMetal
            - !type:MappingTemplatePrototype
              id: SheetRPGlass
            - !type:MappingTemplatePrototype
              id: SheetRGlass
        - !type:MappingTemplatePrototype
          id: ClothingHandsGlovesColorYellow
        - !type:MappingTemplatePrototype
          id: ClothingEyesGlassesMeson
        - !type:MappingTemplatePrototype
          id: WeldingFuelTankFull
        - !type:MappingTemplatePrototype
          id: VendingMachineEngiDrobe
        - !type:MappingTemplatePrototype
          id: VendingMachineEngivend
        - !type:MappingTemplatePrototype
          id: LockerEngineerFilled
        - !type:MappingTemplatePrototype
          id: VendingMachineYouTool
        - !type:MappingTemplatePrototype
          id: CutterMachine
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconEngineering
        - !type:MappingTemplatePrototype
          id: SurveillanceCameraEngineering
        - !type:MappingTemplatePrototype
          id: IntercomEngineering
        - !type:MappingTemplatePrototype
          id: BoxMesonScanners
        - !type:MappingTemplatePrototype
          id: VendingMachineTankDispenserEngineering
        - !type:MappingTemplatePrototype
          id: ToolboxEmergencyFilled
        - !type:MappingTemplatePrototype
          id: ToolboxMechanicalFilled
        - !type:MappingTemplatePrototype
          id: ToolboxElectricalFilled
        - !type:MappingTemplatePrototype
          id: ClothingBeltUtilityEngineering
        - !type:MappingTemplatePrototype
          id: BoxLightbulb
        - !type:MappingTemplatePrototype
          id: BoxLighttube
        - !type:MappingTemplatePrototype
          id: BoxLightMixed
        - !type:MappingTemplatePrototype
          id: LightReplacer
        - !type:MappingTemplatePrototype
          id: Autolathe
        - !type:MappingTemplatePrototype
          id: WardrobeEngineeringFilled
        - !type:MappingTemplatePrototype
          id: BannerEngineering
        - !type:MappingTemplatePrototype
          id: ClothingEyesHudDiagnostic
        - !type:MappingTemplatePrototype
          id: ClothingHeadHatBeretEngineering
    - !type:MappingTemplatePrototype
      id: Telecoms
      children:
        - !type:MappingTemplatePrototype
          id: TelecomServerFilledEngineering
        - !type:MappingTemplatePrototype
          id: TelecomServerFilledCommand
        - !type:MappingTemplatePrototype
          id: TelecomServerFilledMedical
        - !type:MappingTemplatePrototype
          id: TelecomServerFilledScience
        - !type:MappingTemplatePrototype
          id: TelecomServerFilledCommon
        - !type:MappingTemplatePrototype
          id: TelecomServerFilledService
        - !type:MappingTemplatePrototype
          id: TelecomServerFilledSecurity
        - !type:MappingTemplatePrototype
          id: TelecomServerFilledCargo
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconTelecoms
        - !type:MappingTemplatePrototype
          id: SurveillanceCameraRouterEngineering
        - !type:MappingTemplatePrototype
          id: SurveillanceCameraRouterSupply
        - !type:MappingTemplatePrototype
          id: SurveillanceCameraRouterCommand
        - !type:MappingTemplatePrototype
          id: SurveillanceCameraRouterMedical
        - !type:MappingTemplatePrototype
          id: SurveillanceCameraRouterService
        - !type:MappingTemplatePrototype
          id: SurveillanceCameraRouterSecurity
        - !type:MappingTemplatePrototype
          id: SurveillanceCameraRouterGeneral
        - !type:MappingTemplatePrototype
          id: SurveillanceCameraRouterScience
        - !type:MappingTemplatePrototype
          id: SurveillanceCameraWirelessRouterEntertainment
    - !type:MappingTemplatePrototype
      id: SolarPanels
      children:
        - !type:MappingTemplatePrototype
          id: SolarAssembly
        - !type:MappingTemplatePrototype
          id: SolarPanel
        - !type:MappingTemplatePrototype
          id: SolarPanelBroken
        - !type:MappingTemplatePrototype
          id: SolarTracker
        - !type:MappingTemplatePrototype
          id: ComputerSolarControl
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconSolars
    - !type:MappingTemplatePrototype
      id: AME
      children:
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconAME
        - !type:MappingTemplatePrototype
          id: CrateEngineeringAMEShielding
        - !type:MappingTemplatePrototype
          id: CrateEngineeringAMEJar
        - !type:MappingTemplatePrototype
          id: CrateEngineeringAMEControl
        - !type:MappingTemplatePrototype
          id: AmeController
    - !type:MappingTemplatePrototype
      id: PARoom
      children:
        - !type:MappingTemplatePrototype
          id: ParticleAcceleratorEmitterPortUnfinished
        - !type:MappingTemplatePrototype
          id: ParticleAcceleratorEmitterPort
        - !type:MappingTemplatePrototype
          id: ParticleAcceleratorEmitterStarboard
        - !type:MappingTemplatePrototype
          id: ParticleAcceleratorEmitterStarboardUnfinished
        - !type:MappingTemplatePrototype
          id: ParticleAcceleratorPowerBox
        - !type:MappingTemplatePrototype
          id: ParticleAcceleratorPowerBoxUnfinished
        - !type:MappingTemplatePrototype
          id: ParticleAcceleratorFuelChamber
        - !type:MappingTemplatePrototype
          id: ParticleAcceleratorFuelChamberUnfinished
        - !type:MappingTemplatePrototype
          id: ParticleAcceleratorEndCapUnfinished
        - !type:MappingTemplatePrototype
          id: ParticleAcceleratorEndCap
        - !type:MappingTemplatePrototype
          id: ParticleAcceleratorEmitterForeUnfinished
        - !type:MappingTemplatePrototype
          id: ParticleAcceleratorEmitterFore
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconSingularity

- type: mappingTemplate
  id: Security
  rootType: Entity
  children:
    - !type:MappingTemplatePrototype
      id: HOSRoom
      children:
        - !type:MappingTemplatePrototype
          id: SpawnPointHeadOfSecurity
        - !type:MappingTemplatePrototype
          id: LockerHeadOfSecurityFilled
        - !type:MappingTemplatePrototype
          id: SuitStorageHOS
        - !type:MappingTemplatePrototype
          id: DresserHeadOfSecurityFilled
        - !type:MappingTemplatePrototype
          id: BedsheetHOS
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconHOSRoom
        - !type:MappingTemplatePrototype
          id: SpawnMobShiva
    - !type:MappingTemplatePrototype
      id: Brig
      children:
        - !type:MappingTemplatePrototype
          id: VendingMachineSecDrobe
        - !type:MappingTemplatePrototype
          id: VendingMachineSec
        - !type:MappingTemplatePrototype
          id: LockerSecurityFilled
        - !type:MappingTemplatePrototype
          id: Stunbaton
        - !type:MappingTemplatePrototype
          id: LampInterrogator
        - !type:MappingTemplatePrototype
          id: BoxMRE
        - !type:MappingTemplatePrototype
          id: Handcuffs
        - !type:MappingTemplatePrototype
          id: ComputerCriminalRecords
        - !type:MappingTemplatePrototype
          id: ComputerSurveillanceCameraMonitor
        - !type:MappingTemplatePrototype
          id: SpawnPointSecurityCadet
        - !type:MappingTemplatePrototype
          id: SpawnPointSecurityOfficer
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconBrig
        - !type:MappingTemplatePrototype
          id: SurveillanceCameraSecurity
        - !type:MappingTemplatePrototype
          id: IntercomSecurity
        - !type:MappingTemplatePrototype
          id: LockerEvidence
        - !type:MappingTemplatePrototype
          id: BannerSecurity
        - !type:MappingTemplatePrototype
          id: WardrobePrisonFilled
        - !type:MappingTemplatePrototype
          id: BrigTimer
    - !type:MappingTemplatePrototype
      id: WardensOffice
      children:
        - !type:MappingTemplatePrototype
          id: DresserWardenFilled
        - !type:MappingTemplatePrototype
          id: SuitStorageWarden
        - !type:MappingTemplatePrototype
          id: LockerWardenFilled
        - !type:MappingTemplatePrototype
          id: SpawnPointWarden
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconWardensOffice
        - !type:MappingTemplatePrototype
          id: ComputerCriminalRecords
    - !type:MappingTemplatePrototype
      id: Armory
      children:
        - !type:MappingTemplatePrototype
          id: SecurityTechFab
        - !type:MappingTemplatePrototype
          id: SuitStorageSec
        - !type:MappingTemplatePrototype
          id: WeaponSubMachineGunDrozd
        - !type:MappingTemplatePrototype
          id: WeaponShotgunKammerer
        - !type:MappingTemplatePrototype
          id: WeaponShotgunEnforcer
        - !type:MappingTemplatePrototype
          id: WeaponShotgunEnforcerRubber
        - !type:MappingTemplatePrototype
          id: WeaponShotgunDoubleBarreledRubber
        - !type:MappingTemplatePrototype
          id: WeaponShotgunDoubleBarreled
        - !type:MappingTemplatePrototype
          id: WeaponPistolMk58
        - !type:MappingTemplatePrototype
          id: WeaponSubMachineGunWt550
        - !type:MappingTemplatePrototype
          id: WeaponRifleLecter
        - !type:MappingTemplatePrototype
          id: WeaponLaserCannon
        - !type:MappingTemplatePrototype
          id: WeaponLaserCarbine
        - !type:MappingTemplatePrototype
          id: WeaponLaserGun
        - !type:MappingTemplatePrototype
          id: WeaponDisabler
        - !type:MappingTemplatePrototype
          id: WeaponCapacitorRecharger
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconArmory
        - !type:MappingTemplatePrototype
          id: PortableFlasher
        - !type:MappingTemplatePrototype
          id: DeployableBarrier
        - !type:MappingTemplatePrototype
          id: ClothingOuterArmorRiot
        - !type:MappingTemplatePrototype
          id: ClothingOuterArmorBulletproof
        - !type:MappingTemplatePrototype
          id: RiotLaserShield
        - !type:MappingTemplatePrototype
          id: RiotBulletShield
        - !type:MappingTemplatePrototype
          id: RiotShield
        - !type:MappingTemplatePrototype
          id: LootSpawnerArmoryArmorOnly
        - !type:MappingTemplatePrototype
          id: LootSpawnerArmoryGunsOnly
        - !type:MappingTemplatePrototype
          id: LootSpawnerArmory
    - !type:MappingTemplatePrototype
      id: PermaBrig
      children:
        - !type:MappingTemplatePrototype
          id: VendingMachineSustenance
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconPermaBrig
        - !type:MappingTemplatePrototype
          id: SuitStorageEVAPrisoner
    - !type:MappingTemplatePrototype
      id: DetectiveRoom
      children:
        - !type:MappingTemplatePrototype
          id: VendingMachineDetDrobe
        - !type:MappingTemplatePrototype
          id: SpawnPointDetective
        - !type:MappingTemplatePrototype
          id: LockerDetectiveFilled
        - !type:MappingTemplatePrototype
          id: DefaultStationBeaconDetectiveRoom
        - !type:MappingTemplatePrototype
          id: BoxForensicPad
    - !type:MappingTemplatePrototype
      id: Gunnery
      children:
        - !type:MappingTemplatePrototype
          id: TargetClown
        - !type:MappingTemplatePrototype
          id: TargetSyndicate
        - !type:MappingTemplatePrototype
          id: TargetHuman
        - !type:MappingTemplatePrototype
          id: TargetStrange
        - !type:MappingTemplatePrototype
          id: WeaponLaserCarbinePractice
        - !type:MappingTemplatePrototype
          id: WeaponDisablerPractice

