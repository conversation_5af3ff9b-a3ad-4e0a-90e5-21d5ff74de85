- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterVestFlak
  name: "бронежилет"
  description: "Запилений і обвітрений бронежилет."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/OuterClothing/Vests/flak.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/OuterClothing/Vests/flak.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.75
        Heat: 0.9
  - type: ExplosionResistance
    damageCoefficient: 0.9

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterVestFlakPress
  name: "бронежилет (прес)"
  description: "Бронежилет для репортерів, які працюють у зонах, уражених бородавками."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/OuterClothing/Vests/flakpress.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/OuterClothing/Vests/flakpress.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.9
        Slash: 0.9
        Piercing: 0.75
        Heat: 0.9
  - type: ExplosionResistance
    damageCoefficient: 0.9

- type: entity
  parent: ClothingOuterBase
  id: ClothingOuterClerkVest
  name: "жилет клерка"
  description: "шовковий пурпурний жилет з кишенькою для нотаріальної печатки."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/OuterClothing/Vests/clerkvest.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/OuterClothing/Vests/clerkvest.rsi

