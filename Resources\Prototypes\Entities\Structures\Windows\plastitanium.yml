- type: entity
  id: PlastitaniumWindow
  name: "пластанітове вікно"
  parent: Window
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Windows/plastitanium_window.rsi
  - type: Icon
    sprite: Structures/Windows/plastitanium_window.rsi
  - type: Repairable
    fuelCost: 15
    doAfterDelay: 3
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: RGlass
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 1000
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 500
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassReinforced:
            min: 1
            max: 2
          PartRodMetal:
            min: 1
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: IconSmooth
    base: ptwindow
  - type: Appearance
  - type: DamageVisuals
    thresholds: [4, 8, 12]
    damageDivisor: 28
    trackAllDamage: true
    damageOverlay:
      sprite: Structures/Windows/cracks.rsi
  - type: StaticPrice
    price: 100

- type: entity
  parent: ShuttleWindow
  id: PlastitaniumWindowDiagonal
  suffix: diagonal
  placement:
    mode: SnapgridCenter
    snap:
    - Window
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Windows/plastitanium_window_diagonal.rsi
    state: state0
  - type: IconSmooth
    mode: Diagonal
    key: walls
    base: state
  - type: Icon
    sprite: Structures/Windows/plastitanium_window_diagonal.rsi
    state: state0
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PolygonShape
            vertices:
            - "-0.5,-0.5"
            - "0.5,0.5"
            - "0.5,-0.5"
        mask:
        - FullTileMask
        layer:
        - GlassLayer
  - type: Airtight
    noAirWhenFullyAirBlocked: false
    airBlockedDirection:
    - South
    - East
  - type: DamageVisuals
    damageOverlay:
      sprite: Structures/Windows/cracks_diagonal.rsi
