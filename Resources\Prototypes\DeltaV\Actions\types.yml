- type: entity
  id: ActionChitzite
  name: "Відкашлятися від читциту"
  description: "Очистіть свій організм від надлишкової радіації, виводячи її як масу токсичного матеріалу."
  components:
  - type: InstantAction
    charges: 0
    enabled: false
    maxCharges: 1
    icon: { sprite: DeltaV/Objects/Specific/Species/chitinid.rsi, state: chitzite }
    useDelay: 300
    event: !type:ChitziteActionEvent

#PIRATE
- type: entity
  id: ActionOpenRadioImplant
  name: "Відкрити радіоімплант"
  description: "Відкриває відсік для ключів bluespace радіоімплантату, вмонтованого у ваш череп."
  components:
  - type: InstantAction
    itemIconStyle: BigAction
    priority: -20
    icon:
      sprite: Clothing/Ears/Headsets/base.rsi
      state: icon
    event: !type:OpenStorageImplantEvent

- type: entity
  id: ActionOpenMouthStorage
  name: "Відкрити сховище в щічках"
  description: "Дозволяє зберігати предмети в щоках."
  components:
  - type: InstantAction
    itemIconStyle: BigAction
    priority: -10
    icon: DeltaV/Interface/Actions/mouthStorageOpen.png
    event: !type:OpenStorageImplantEvent

- type: entity
  id: ActionToggleSneakMode
  name: "Увімкнути прихований режим"
  description: "Проникайте під столи."
  components:
  - type: InstantAction
    itemIconStyle: BigAction
    useDelay: 3
    icon:
      sprite: Structures/Furniture/Tables/generic.rsi
      state: full
    event: !type:ToggleCrawlingStateEvent
