using Content.Client.UserInterface.Controls;
using Content.Shared._DV.Salvage.Components;
using Robust.Client.AutoGenerated;
using Robust.Client.GameObjects;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;
using System.Numerics;

namespace Content.Client._DV.Salvage.UI;

[GenerateTypedNameReferences]
public sealed partial class MiningVoucherMenu : RadialMenu
{
    [Dependency] private readonly IEntityManager _entMan = default!;
    [Dependency] private readonly IPrototypeManager _proto = default!;

    private readonly SpriteSystem _sprite;

    public event Action<int>? OnSelected;

    public MiningVoucherMenu()
    {
        IoCManager.InjectDependencies(this);
        RobustXamlLoader.Load(this);

        _sprite = _entMan.System<SpriteSystem>();
    }

    public void SetEntity(EntityUid owner)
    {
        if (!_entMan.TryGetComponent<MiningVendorComponent>(owner, out var comp))
            return;

        for (int i = 0; i < comp.Kits.Count; i++)
        {
            var index = i; // copy so the closure doesn't borrow it
            var kit = _proto.Index(comp.Kits[i]);
            var button = new RadialMenuTextureButton()
            {
                StyleClasses = { "RadialMenuTextureButton" },
                SetSize = new Vector2(64f, 64f),
                ToolTip = Loc.GetString(kit.Description)
            };
            button.AddChild(new TextureRect()
            {
                VerticalAlignment = VAlignment.Center,
                HorizontalAlignment = HAlignment.Center,
                Texture = _sprite.Frame0(kit.Sprite),
                TextureScale = new Vector2(2f, 2f)
            });

            button.OnPressed += _ => OnSelected?.Invoke(index);

            Main.AddChild(button);
        }
    }
}
