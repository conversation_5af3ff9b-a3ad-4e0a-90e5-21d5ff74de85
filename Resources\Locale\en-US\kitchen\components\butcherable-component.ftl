﻿butcherable-different-tool = You need a different tool to butcher { THE($target) }.
butcherable-knife-butchered-success = You butcher { THE($target) } with { THE($knife) }.
butcherable-need-knife = Use a sharp object to butcher { THE($target) }.
butcherable-not-in-container = { CAPITALIZE(THE($target)) } can't be in a container.
butcherable-mob-isnt-dead = Needs to be dead.
butcherable-verb-name = Butcher
