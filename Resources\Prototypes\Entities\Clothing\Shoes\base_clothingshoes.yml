- type: entity
  abstract: true
  parent: Clothing
  id: ClothingShoesBase
  components:
  - type: Clothing
    slots:
    - FEET
  - type: Sprite
    state: icon
  - type: Item
    size: Normal
  - type: Food
    requiresSpecialDigestion: true
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Fiber
          Quantity: 10
  - type: Tag
    tags:
    - ClothMade
    - WhitelistChameleon
  - type: ClothingRequiredStepTriggerImmune
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 5
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  abstract: true
  parent: ClothingShoesBase
  id: ClothingShoesBaseButcherable
  components:
  - type: Butcherable
    butcheringType: Knife
    spawned:
    - id: MaterialCloth1
      amount: 1

# stuff common to all military boots
- type: entity
  abstract: true
  parent: [ClothingShoesBase, ClothingSlotBase]
  id: ClothingShoesMilitaryBase
  components:
  - type: Matchbox
  - type: ItemSlots
    slots:
      item:
        name: clothing-boots-sidearm
        whitelist:
          tags:
          - Knife
          - Sidearm

- type: entity
  abstract: true
  parent: ClothingShoesBaseButcherable
  id: ClothingShoesBaseWinterBoots
  description: "Пухнасті чоботи допоможуть пережити навіть найхолоднішу зиму."
  components:
  - type: TemperatureProtection
    coefficient: 0.8
