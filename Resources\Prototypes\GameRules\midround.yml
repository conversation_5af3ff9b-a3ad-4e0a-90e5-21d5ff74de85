- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseGameRule
  id: Thief
  components:
  - type: ThiefRule
  - type: AntagObjectives
    objectives:
    - EscapeThiefShuttleObjective
  - type: AntagRandomObjectives
    sets:
    - groups: ThiefBigObjectiveGroups
      prob: 0.7
      maxPicks: 1
    - groups: ThiefObjectiveGroups
      maxPicks: 10
    maxDifficulty: 2.5
  - type: AntagSelection
    agentName: thief-round-end-agent-name
    definitions:
    - prefRoles: [ Thief ]
      max: 3
      playerRatio: 15
      lateJoinAdditional: true
      allowNonHumans: true
      multiAntagSetting: All
      startingGear: ThiefGear
      components:
      - type: Pacified
      mindRoles:
      - MindRoleThief
      briefing:
        sound: "/Audio/Misc/thief_greeting.ogg"

#PIRATE START UNTIL END OF FILE
# stores configuration for mage
#- type: entity
#  noSpawn: true
#  parent: BaseGameRule
#  id: Mage
#  components:
#  - type: GenericAntagRule
#    agentName: mage-round-end-agent-name
#    objectives:
#    - MageEscapeObjective
#- type: entity
#  categories: [ HideSpawnMenu ]
#  parent: BaseGameRule
#  id: Exterminator
#  components:
#  - type: GenericAntagRule
#    agentName: terminator-round-end-agent-name
#    objectives:
#    - TerminateObjective
#    - ShutDownObjective

- type: entity
  parent: BaseGameRule
  id: Vampire
  components:
  - type: VampireRule
  - type: AntagSelection
    agentName: vampire-roundend-name
    definitions:
    - prefRoles: [ Vampire ]
      max: 1
      playerRatio: 5
      lateJoinAdditional: true
      mindRoles:
      - MindRoleVampire
