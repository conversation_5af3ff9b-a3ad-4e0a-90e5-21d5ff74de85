﻿- type: decal
  id: BrickTile
  abstract: true

- type: decal
  id: BrickTileDark
  parent: BrickTile
  abstract: true

- type: decal
  id: BrickTileDarkBox
  parent: BrickTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: dark_box

- type: decal
  id: BrickTileDarkCornerNe
  parent: BrickTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: dark_corner_ne

- type: decal
  id: BrickTileDarkCornerSe
  parent: BrickTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: dark_corner_se

- type: decal
  id: BrickTileDarkCornerNw
  parent: BrickTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: dark_corner_nw

- type: decal
  id: BrickTileDarkCornerSw
  parent: BrickTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: dark_corner_sw

- type: decal
  id: BrickTileDarkInnerNe
  parent: BrickTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: dark_inner_ne

- type: decal
  id: BrickTileDarkInnerSe
  parent: BrickTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: dark_inner_se

- type: decal
  id: BrickTileDarkInnerNw
  parent: BrickTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: dark_inner_nw

- type: decal
  id: BrickTileDarkInnerSw
  parent: BrickTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: dark_inner_sw

- type: decal
  id: BrickTileDarkEndN
  parent: BrickTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: dark_end_n

- type: decal
  id: BrickTileDarkEndE
  parent: BrickTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: dark_end_e

- type: decal
  id: BrickTileDarkEndS
  parent: BrickTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: dark_end_s

- type: decal
  id: BrickTileDarkEndW
  parent: BrickTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: dark_end_w

- type: decal
  id: BrickTileDarkLineN
  parent: BrickTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: dark_line_n

- type: decal
  id: BrickTileDarkLineE
  parent: BrickTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: dark_line_e

- type: decal
  id: BrickTileDarkLineS
  parent: BrickTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: dark_line_s

- type: decal
  id: BrickTileDarkLineW
  parent: BrickTileDark
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: dark_line_w

