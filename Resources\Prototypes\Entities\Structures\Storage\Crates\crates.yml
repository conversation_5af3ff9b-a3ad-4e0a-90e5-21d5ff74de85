- type: entity
  parent: CrateBaseWeldable
  id: CrateGenericSteel
  name: "сталевий ящик"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/generic.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/generic.rsi
  - type: Reflect
    reflects:
    - Energy
    reflectProb: 0.2
    spread: 90
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 16
    staminaCost: 45

- type: entity
  parent: CrateBaseWeldable
  id: CratePlastic
  name: "пластиковий ящик"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/plastic.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/plastic.rsi
  - type: Construction
    graph: CratePlastic
    node: crateplastic
    containers:
    - entity_storage
  - type: StaticPrice
    price: 80
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 10
    staminaCost: 30

- type: entity
  parent: CratePlastic
  id: CrateFreezer
  name: "морозильна камера"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/freezer.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/freezer.rsi
  - type: AntiRottingContainer
  - type: ExplosionResistance
    damageCoefficient: 0.50

- type: entity
  parent: CratePlastic
  id: CrateHydroponics
  name: "ящик для гідропоніки"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/hydroponics.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/hydroponics.rsi

- type: entity
  parent: CratePlastic
  id: CrateMedical
  name: "медичний ящик"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/medical.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/medical.rsi

- type: entity
  parent: CrateGenericSteel
  id: CrateRadiation
  name: "ящик для радіаційного обладнання"
  description: "Насправді не має свинцевого покриття. Не зберігайте в ньому плутоній."
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/radiation.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/radiation.rsi

- type: entity
  parent: CratePlastic
  id: CrateInternals
  name: "кисневий ящик"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/o2.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/o2.rsi

- type: entity
  parent: CrateGenericSteel
  id: CrateElectrical
  name: "ящик електрика"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/electrical.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/electrical.rsi

- type: entity
  parent: CrateGenericSteel
  id: CrateEngineering
  name: "інженерний ящик"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/engineering.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/engineering.rsi

- type: entity
  parent: CrateGenericSteel
  id: CrateScience
  name: "ящик епістеміки" # DeltaV - Epistemics Department replacing Science
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/science.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/science.rsi

- type: entity
  parent: CrateGenericSteel
  id: CrateSurgery
  name: "операційний ящик"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/surgery.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/surgery.rsi

- type: entity
  parent: CrateGeneric
  id: CrateWeb
  name: "павутинний ящик"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/web.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/web.rsi
    layers:
    - state: base
      map: ["enum.StorageVisualLayers.Base"]
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: Construction
    graph: WebStructures
    node: crate
  - type: Damageable
    damageModifierSet: Web
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWebSilk:
            min: 3
            max: 5
  - type: EntityStorage
    closeSound:
      path: /Audio/Effects/rustle1.ogg
    openSound:
      path: /Audio/Effects/rustle2.ogg

# Secure Crates

- type: entity
  parent: CrateBaseSecure
  id: CrateSecgear
  name: "ящик secgear"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/sec_gear.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/sec_gear.rsi
  - type: AccessReader
    access: [["Security"]]

- type: entity
  parent: CrateBaseSecure
  id: CrateEngineeringSecure
  name: "надійний інженерний каркас"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/engicrate_secure.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/engicrate_secure.rsi
  - type: AccessReader
    access: [["Engineering"]]

- type: entity
  parent: CrateBaseSecure
  id: CrateMedicalSecure
  name: "захищений медичний ящик"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/medicalcrate_secure.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/medicalcrate_secure.rsi
  - type: AccessReader
    access: [["Medical"]]

- type: entity
  parent: CrateBaseSecure
  id: CrateChemistrySecure
  name: "захищений ящик для хімії"
  components:
    - type: Icon
      sprite: Structures/Storage/Crates/chemcrate_secure.rsi
    - type: Sprite
      sprite: Structures/Storage/Crates/chemcrate_secure.rsi
    - type: AccessReader
      access: [["Chemistry"]]

- type: entity
  parent: CrateBaseSecure
  id: CratePrivateSecure
  name: "приватний ящик"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/privatecrate_secure.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/privatecrate_secure.rsi

- type: entity
  parent: CrateBaseSecure
  id: CrateScienceSecure
  name: "захищений ящик епістеми" # DeltaV - Epistemics Department replacing Science
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/scicrate_secure.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/scicrate_secure.rsi
  - type: AccessReader
    access: [["Research"]]

- type: entity
  parent: CrateBaseSecure
  id: CratePlasma
  name: "плазмовий ящик"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/plasma.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/plasma.rsi
  - type: AccessReader
    access: [["Engineering"], ["Research"], ["Chemistry"]]

- type: entity
  parent: CrateBaseSecure
  id: CrateUranium
  name: "урановий ящик"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/uranium.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/uranium.rsi
  - type: AccessReader
    access: [["Engineering"], ["Research"]]

- type: entity
  parent: CrateBaseSecure
  id: CrateSecure
  name: "безпечний ящик"
  components:
    - type: Icon
      sprite: Structures/Storage/Crates/secure.rsi
    - type: Sprite
      sprite: Structures/Storage/Crates/secure.rsi

- type: entity
  parent: CrateBaseSecure
  id: CrateHydroSecure
  name: "захищений ящик гідропоніки"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/hydro_secure.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/hydro_secure.rsi
  - type: AccessReader
    access: [["Hydroponics"]]

- type: entity
  parent: CrateBaseSecureReinforced
  id: CrateWeaponSecure
  name: "захищений ящик для зброї"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/weapon.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/weapon.rsi
  - type: AccessReader
    access: [["Armory"]]

- type: entity
  parent: CrateBaseSecureReinforced
  suffix: Armory, Secure, Reinforced
  id: CrateContrabandStorageSecure
  name: "ящик для зберігання контрабанди"
  description: "Ящик для зберігання контрабанди, конфіскованої у підозрюваних або ув'язнених, що замикається на ключ."
  components:
  - type: Sprite
    sprite: Structures/Storage/Crates/sec_gear.rsi
  - type: AccessReader
    access: [["Armory"]]

- type: entity
  parent: CrateBaseSecure
  id: CrateCommandSecure
  name: "ящик командування"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/command.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/command.rsi
  - type: AccessReader
    access: [["Command"]]

- type: entity
  parent: CrateGeneric
  id: CrateLivestock
  name: "ящик для худоби"
  components:
  - type: EntityStorage
    airtight: false
  - type: Sprite
    sprite: Structures/Storage/Crates/livestock.rsi
    layers:
    - state: base
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      offset: "-0.25,0.625"
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: Icon
    sprite: Structures/Storage/Crates/livestock.rsi
    state: base
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 15
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroyHeavy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank1:
            min: 1
            max: 4
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Physics
    bodyType: Dynamic
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.4,0.4,0.29"
        density: 135
        mask:
        - CrateMask #this is so they can go under plastic flaps
        layer:
        - LargeMobLayer
  - type: Construction
    graph: CrateLivestock
    node: cratelivestock
    containers:
    - entity_storage
  - type: StaticPrice
    price: 125

- type: entity
  parent: CrateGeneric
  id: CrateRodentCage
  name: "клітка для хом'яка"
  components:
  - type: Sprite
    sprite: Structures/Storage/Crates/cage.rsi
    layers:
    - state: base
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      offset: "0.0,0.125"
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: Icon
    sprite: Structures/Storage/Crates/cage.rsi
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 15
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetPlastic:
            min: 1
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Physics
    bodyType: Dynamic
  - type: EntityStorage
    capacity: 1
    airtight: false
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.4,0.4,0.29"
        density: 80
        mask:
        - CrateMask
        layer:
        - LargeMobLayer
  - type: StaticPrice
    price: 60

- type: entity
  parent: CrateGeneric
  id: CratePirate
  name: "піратська скриня"
  description: "Космічна піратська скриня, не для станційних морських свиней."
  components:
  - type: Sprite
    sprite: Structures/Storage/Crates/piratechest.rsi
    layers:
    - state: crate
      map: ["enum.StorageVisualLayers.Base"]
    - state: crate_door
      map: ["enum.StorageVisualLayers.Door"]
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      offset: "0.0,-0.09375"
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: Icon
    sprite: Structures/Storage/Crates/piratechest.rsi
    state: crate_icon
  - type: Appearance
  - type: EntityStorageVisuals
    stateDoorOpen: crate_open
    stateDoorClosed: crate_door

- type: entity
  parent: CratePirate
  id: CrateToyBox
  name: "ящик для іграшок"
  suffix: Empty
  description: "Коробка, переповнена веселощами."
  components:
  - type: Sprite
    sprite: Structures/Storage/Crates/toybox.rsi
    layers:
    - state: crate
      map: ["enum.StorageVisualLayers.Base"]
    - state: crate_door
      map: ["enum.StorageVisualLayers.Door"]
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
    - state: paper
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: Icon
    sprite: Structures/Storage/Crates/toybox.rsi
    state: crate_icon

- type: entity
  parent: CrateGeneric
  id: CrateCoffin
  name: "труна"
  description: "Труна для зберігання трупів."
  components:
  - type: Sprite
    sprite: Structures/Storage/Crates/coffin.rsi
    layers:
    - state: base
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: paper
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: Icon
    sprite: Structures/Storage/Crates/coffin.rsi
    state: base
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 15
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank1:
            min: 1
            max: 4
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Coffin
  - type: Construction
    graph: CrateCoffin
    node: cratecoffin
    containers:
    - entity_storage

- type: entity
  parent: CrateGeneric
  id: CrateWoodenGrave
  name: "могила"
  suffix: wooden
  description: "Тут хтось помер..."
  components:
  - type: Sprite
    sprite: Structures/Storage/Crates/wooden_grave.rsi
    layers:
    - state: base
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      offset: "-0.28125,0.625"
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: Icon
    sprite: Structures/Storage/Crates/wooden_grave.rsi
    state: base
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200 # discourage just beating the grave to break it open
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank1:
            min: 1
            max: 4
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Physics
    bodyType: Static
  - type: Grave
  - type: ProRottingContainer
  - type: EntityStorage
    airtight: true
    isCollidableWhenOpen: false
    closeSound:
      path: /Audio/Items/shovel_dig.ogg
    openSound:
      path: /Audio/Items/shovel_dig.ogg

- type: entity
  parent: CrateWoodenGrave
  id: CrateStoneGrave
  name: "могила"
  suffix: stone
  description: "Тут хтось помер..."
  components:
  - type: Sprite
    sprite: Structures/Storage/Crates/stone_grave.rsi
    layers:
    - state: base
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      offset: "-0.3125,0.5625"
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: Icon
    sprite: Structures/Storage/Crates/stone_grave.rsi
    state: base

- type: entity
  parent: CrateGenericSteel
  id: CrateSyndicate
  name: "Синдикатський ящик"
  description: "Темний сталевий ящик з червоними смугами та вибитою літерою S спереду."
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/syndicate.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/syndicate.rsi

- type: entity
  parent: [StructureWheeled, CrateBaseWeldable]
  id: CrateTrashCart
  name: "візок для сміття"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/trashcart.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/trashcart.rsi
    layers:
    - state: base
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      offset: "0.0,0.03125"
      map: ["enum.PaperLabelVisuals.Layer"]

- type: entity
  parent: CrateBaseSecure
  id: CrateTrashCartJani
  name: "сміттєвий візок прибиральника"
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/trashcart_jani.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/trashcart_jani.rsi
    layers:
    - state: base
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
    - state: locked
      map: ["enum.LockVisualLayers.Lock"]
      shader: unshaded
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      offset: "0.0,0.03125"
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: AccessReader
    access: [["Janitor"]]

- type: entity
  parent: CrateBaseWeldable
  id: InvisibleCrate
  suffix: Stealth
  components:
  - type: Stealth
    hadOutline: true
  - type: StealthOnMove
    passiveVisibilityRate: -0.10
    movementVisibilityRate: 0.10

#PIRATE START UNTIL END OF FILE
- type: entity
  parent: CrateBaseSecure
  id: CrateRoboticsSecure
  name: "безпечний ящик для робототехніки"
  components:
  - type: Icon
    sprite: _Pirate/Structures/Storage/Crates/robocrate_secure.rsi
  - type: Sprite
    sprite: _Pirate/Structures/Storage/Crates/robocrate_secure.rsi
  - type: AccessReader
    access: [["Robotics"]]
