- type: entity
  parent: BaseItem
  id: DeviceQuantumSpinInverter
  name: "квантовий спіновий інвертор"
  description: "Експериментальний пристрій, здатний міняти місцями два об'єкти, змінюючи значення спінів їхніх частинок. Для роботи має бути пов'язаний з іншим пристроєм."
  components:
  - type: Sprite
    sprite: Objects/Devices/swapper.rsi
    layers:
    - state: icon
      map: ["base"]
  - type: Item
    size: Small
  - type: Appearance
  - type: SwapTeleporter
    teleporterWhitelist:
      tags:
      - QuantumSpinInverter
  - type: GenericVisualizer
    visuals:
      enum.SwapTeleporterVisuals.Linked:
        base:
          True: { state: linked }
          False: { state: icon }
  - type: Tag
    tags:
    - QuantumSpinInverter
  - type: ReverseEngineering # Delta
    difficulty: 4
    recipes:
      - DeviceQuantumSpinInverter
