- type: entity
  parent: BaseItem
  id: Bucket
  name: "відро"
  description: "Це нудне старе відро."
  components:
  - type: Drink
    solution: bucket
    ignoreEmpty: true
  - type: Clickable
  - type: Sprite
    sprite: Objects/Tools/bucket.rsi
    layers:
    - state: icon
    - map: ["enum.SolutionContainerLayers.Fill"]
      state: fill-1
      visible: false
  - type: Item
    size: Normal
  - type: Clothing
    sprite: Objects/Tools/bucket.rsi
    slots:
    - HEAD
    - HEAD1 #PIRATE
    - HEAD2 #PIRATE
    quickEquip: false
  - type: SolutionContainerManager
    solutions:
      bucket:
        maxVol: 250
  - type: MixableSolution
    solution: bucket
  - type: SolutionTransfer
    transferAmount: 100
    maxTransferAmount: 100
    minTransferAmount: 10
    canChangeTransferAmount: true
  - type: UserInterface
    interfaces:
      enum.TransferAmountUiKey.Key:
        type: TransferAmountBoundUserInterface
  - type: MeleeWeapon
    soundNoDamage:
      path: "/Audio/Effects/Fluids/splat.ogg"
    damage:
      types:
        Blunt: 0
  - type: Spillable
    solution: bucket
  - type: DrawableSolution
    solution: bucket
  - type: RefillableSolution
    solution: bucket
  - type: DrainableSolution
    solution: bucket
  - type: SolutionItemStatus
    solution: bucket
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 3
    fillBaseName: fill-
  - type: ExaminableSolution
    solution: bucket
  - type: Tag
    tags:
      - Bucket
  - type: PhysicalComposition
    materialComposition:
      Plastic: 50
