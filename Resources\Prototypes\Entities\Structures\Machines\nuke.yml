- type: entity
  parent: [BaseStructure, StructureWheeled]
  id: NuclearBomb
  name: "вибухівка ядерного розщеплення"
  description: "Можливо, вам не варто залишатися тут, щоб перевірити, чи вона увімкнена."
  components:
  - type: Transform
    anchored: true
  - type: Sprite
    sprite: Objects/Devices/nuke.rsi
    noRot: true
    layers:
    - state: nuclearbomb_base
      map: ["enum.NukeVisualLayers.Base"]
    - state: nuclearbomb_deployed_unlit
      map: ["enum.NukeVisualLayers.Unlit"]
      shader: unshaded
      visible: false
    - state: nukefestive
      map: ["christmas"]
      visible: false
  - type: Appearance
  - type: HolidayVisuals
    holidays:
      festive:
      - FestiveSeason
  - type: GenericVisualizer
    visuals:
      enum.HolidayVisuals.Holiday:
        christmas:
          festive: { visible: true }
      enum.NukeVisuals.Deployed:
        enum.NukeVisualLayers.Base:
          False: { state: nuclearbomb_base }
          True:  { state: nuclearbomb_deployed }
        enum.NukeVisualLayers.Unlit:
          True:  { visible: true }
          False: { visible: false }
      enum.NukeVisuals.State:
        enum.NukeVisualLayers.Unlit:
          Idle:        { state: nuclearbomb_deployed_unlit }
          Armed:       { state: nuclearbomb_timing }
          YoureFucked: { state: nuclearbomb_exploding }
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.45
        density: 255 # Has "bluespace technology" to make it lighter, whatever that means. Don't mind the fact that this is lighter then a high capacity fuel tank.
        mask:
        - MachineMask
        layer:
        - HalfWallLayer
  - type: PointLight
    enabled: false
    radius: 4
    energy: 2.0
    color: "#FF4020"
    mask: /Textures/Effects/LightMasks/double_cone.png
  - type: RotatingLight
    speed: 120
  - type: NavMapBeacon
    color: "#ff0000"
    text: nuclear fission explosive
    enabled: false
  - type: NukeLabel
  - type: Nuke
    explosionType: Default
    maxIntensity: 100
    intensitySlope: 5
    totalIntensity: 5000000
    diskSlot:
      name: nuke-slot-component-slot-name-disk
      insertSound:
        path: /Audio/Machines/terminal_insert_disc.ogg
      ejectSound:
        path: /Audio/Machines/terminal_insert_disc.ogg
      whitelist:
        components:
        - NukeDisk
    alertLevelOnActivate: delta
    alertLevelOnDeactivate: green
  - type: InteractionOutline
  - type: ActivatableUI
    key: enum.NukeUiKey.Key
  - type: UserInterface
    interfaces:
      enum.NukeUiKey.Key:
        type: NukeBoundUserInterface
  - type: StaticPrice
    price: 50000 # YOU STOLE A NUCLEAR FISSION EXPLOSIVE?!
  - type: CargoSellBlacklist
  - type: ArrivalsBlacklist
  - type: ItemSlots
  - type: ContainerContainer
    containers:
      Nuke: !type:ContainerSlot
  - type: StealTarget
    stealGroup: NuclearBomb
  - type: WarpPoint
    follow: true
    location: nuclear bomb
  - type: FTLSmashImmune

- type: entity
  parent: NuclearBomb
  id: NuclearBombUnanchored
  suffix: unanchored
  components:
  - type: Transform
    anchored: false
  - type: Physics
    bodyType: Dynamic

- type: entity
  parent: StorageTank
  id: NuclearBombKeg
  name: "вибухівка ядерного розщеплення"
  suffix: keg
  description: "Вам, мабуть, не варто залишатися тут, щоб перевірити, чи він озброєний. Тут є кран збоку."
  components:
  - type: NukeLabel
  - type: Sprite
    sprite: Objects/Devices/nuke.rsi
    layers:
    - state: nuclearbomb_base
    - state: nukefestive
      map: ["christmas"]
      visible: false
  - type: Appearance
  - type: HolidayVisuals
    holidays:
      festive:
      - FestiveSeason
  - type: GenericVisualizer
    visuals:
      enum.HolidayVisuals.Holiday:
        christmas:
          festive: { visible: true }
  - type: Physics
    bodyType: Dynamic
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.45
        density: 80
        mask:
        - TabletopMachineMask
        layer:
        - TabletopMachineLayer
  - type: SolutionContainerManager
    solutions:
      tank:
        reagents:
        - ReagentId: NuclearCola
          Quantity: 3000
  - type: ReagentTank
    transferAmount: 100
  - type: StaticPrice
    price: 5000 # That's a pretty fancy keg you got there.
