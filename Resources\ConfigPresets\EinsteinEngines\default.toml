[admin]
see_own_notes   = true
deadmin_on_join = true

[discord]
rich_main_icon_id = "einstein-engines"

[events]
ramping_average_end_time = 180.0
ramping_average_chaos    = 4.5

[game]
hostname           = "Einstein Engines (change this)"
desc               = "An alternative upstream that favors content intended for RP servers. If you are reading this, please change it."
soft_max_players   = 60
lobbyenabled       = true
lobbyduration      = 240

[hub]
tags = "lang:en-US,region:am_n_e,rp:med"
hub_urls = "https://hub.spacestation14.com/, https://web.networkgamez.com/, https://hub.singularity14.co.uk/"

[ic]
flavor_text = true

[infolinks]
discord    = "https://discord.gg/X4QEXxUrsJ"
github     = "https://github.com/Simple-Station/Einstein-Engines"
website    = "https://simplestation.org/"
bug_report = "https://github.com/Simple-Station/Einstein-Engines/issues/new/choose"

[net]
max_connections = 1024
tickrate = 30

[netres]
limit = 10.0

[build]
#! PLEASE set this for your fork
fork_id = "EinsteinEngines"

[server]
rules_file   = "DefaultRuleset"

[ooc]
enable_during_round = true

[vote]
restart_required_ratio = 0.7
preset_enabled = true
map_enabled = true

#[worldgen]
#enabled = true
