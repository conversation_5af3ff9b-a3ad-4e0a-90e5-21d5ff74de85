- type: gameMap
  id: Asterisk
  mapName: Астеріск
  mapPath: /Maps/asterisk.yml
  minPlayers: 5
  stations:
    Asterisk:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationRandomTransform
          enableStationRotation: false
          maxStationOffset: null
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/DeltaV/NTES_Kaeri.yml
        - type: StationNameSetup
          mapNameTemplate: '{0} Asterisk Station {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: 'DV'
        - type: StationJobs
          overflowJobs:
            - Passenger
          availableJobs:
          #command
            Captain: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1]
            NanotrasenRepresentative: [ 1, 1 ]
            # Magistrate: [ 1, 1 ] # EE: Disabled
            # AdministrativeAssistant: [ 1, 1 ] # EE: Disabled
          #service
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 1, 2 ]
            Botanist: [ 2, 3 ]
            Chef: [ 1, 2 ]
            Clown: [ 1, 1 ]
            Reporter: [ 1, 1 ]
            Musician: [ 1, 1 ]
            Janitor: [ 1, 2 ]
            Mime: [ 1, 1 ]
          #engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 2, 2 ] # increased to 2 slots
            SeniorEngineer: [ 1, 1 ] # adding senior roles :)
            StationEngineer: [ 3, 3 ]
            TechnicalAssistant: [ 2, 4 ]
          #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            Chemist: [ 1, 2 ]
            Paramedic: [ 1, 2 ]
            SeniorPhysician: [ 1, 1 ]
            MedicalDoctor: [ 2, 3 ]
            MedicalIntern: [ 2, 3 ]
          #science
            ResearchDirector: [ 1, 1 ]
            Roboticist: [ 1, 1 ] #PIRATE
            Chaplain: [ 1, 1 ]
            Librarian: [ 1, 1 ]
            ForensicMantis: [ 1, 1 ]
            SeniorResearcher: [ 1, 1 ]
            Scientist: [ 2, 3 ]
            ResearchAssistant: [ 2, 3 ]
          #security
            HeadOfSecurity: [ 1, 1 ]
            Warden: [ 1, 1 ]
            Detective: [ 1, 1 ]
            Brigmedic: [ 1, 1 ]
            SeniorOfficer: [ 1, 1 ]
            SecurityOfficer: [ 2, 3 ]
            SecurityCadet: [ 1, 3 ]
            # PrisonGuard: [ 1, 2 ] # EE: Disabled
            Prisoner: [ 1, 3 ] # :^)
          #supply
            Quartermaster: [ 1, 1 ]
            MailCarrier: [ 1, 2 ]
            SalvageSpecialist: [ 2, 3 ]
            CargoTechnician: [ 2, 4 ]
          #justice
            Lawyer: [ 1, 1 ]
            # Prosecutor: [ 1, 1 ] # Finally figured it out. Feel so dumb. :( # EE: Disabled
          #civilian
            Passenger: [ -1, -1 ]
          #silicons
            Borg: [ 1, 2 ]
            MedicalBorg: [ 1, 1 ]
            StationAi: [ 1, 1 ]
        # blob-config-start SMALL
        - type: StationBlobConfig
          stageBegin: 20
          stageCritical: 300
          stageTheEnd: 600
        # blob-config-end
