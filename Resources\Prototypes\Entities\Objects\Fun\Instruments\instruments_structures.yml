﻿- type: entity
  name: "фортепіано"
  parent: BasePlaceableInstrumentRotatable
  id: PianoInstrument
  description: "Грайте на піаніно \"Голки\" зараз."
  components:
  - type: Instrument
    program: 1
  - type: Sprite
    state: piano
  - type: Climbable

- type: entity
  name: "піаніно"
  parent: BasePlaceableInstrumentRotatable
  id: UprightPianoInstrument
  description: "Я сказав П'яні!"
  components:
  - type: Instrument
    program: 3
  - type: Sprite
    state: piano-upright
  - type: Climbable

- type: entity
  name: "вібрафон"
  id: VibraphoneInstrument
  parent: BasePlaceableInstrumentRotatable
  description: "Хороші вібрації навколо."
  components:
  - type: Instrument
    program: 11
  - type: Sprite
    state: vibraphone

- type: entity
  name: "маримба"
  id: MarimbaInstrument
  parent: BasePlaceableInstrumentRotatable
  components:
  - type: Instrument
    program: 12
  - type: Sprite
    state: marimba

- type: entity
  name: "церковний орган"
  parent: BasePlaceableInstrumentRotatable
  id: ChurchOrganInstrument
  description: "Ця штука дійсно крута!"
  components:
  - type: Instrument
    program: 19
  - type: Sprite
    state: church-organ

- type: entity
  name: "туба"
  parent: BasePlaceableInstrument
  id: TubaInstrument
  description: "Великий тато сімейства духових. Стоячи поруч з його величністю, відчуваєш себе невпевнено."
  components:
  - type: Instrument
    program: 58
  - type: Sprite
    state: tuba

- type: entity
  id: HarpInstrument
  parent: BasePlaceableInstrument
  name: "арфа"
  description: "Якщо щипати за струни, можна порізати пальці, але, принаймні, музика гарна."
  components:
  - type: Instrument
    program: 46
  - type: Sprite
    state: harp

- type: entity
  id: TimpaniInstrument
  parent: BasePlaceableInstrumentRotatable
  name: "литаври"
  description: "Це БУМ-БУМ-БУМ-БУМ!"
  components:
  - type: Instrument
    program: 47
  - type: Sprite
    state: timpani

- type: entity
  id: TaikoInstrument
  parent: BasePlaceableInstrumentRotatable
  name: "тайко"
  description: "Великий барабан. Дивлячись на нього, хочеться вдарити по ньому."
  components:
  - type: Instrument
    program: 116
  - type: Sprite
    state: taiko

- type: entity
  id: ContrabassInstrument
  parent: BasePlaceableInstrument
  name: "контрабас"
  description: "Ідеально підходить для створення приємного джазового ритму."
  components:
  - type: Instrument
    program: 43
  - type: Sprite
    state: contrabass
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.30
        density: 180
        mask:
        - Impassable
        - HighImpassable
        - MidImpassable
        layer:
        - Opaque
        - HighImpassable
        - MidImpassable
        - BulletImpassable

- type: entity
  name: "мінімуг"
  parent: BasePlaceableInstrumentRotatable
  id: MinimoogInstrument
  description: "Це мінімуг, схожий на космічне піаніно, але більш космічний!"
  components:
  - type: Instrument
    program: 81
  - type: Sprite
    state: minimoog

- type: entity
  id: TomDrumsInstrument
  parent: BasePlaceableInstrumentRotatable
  name: "томні барабани"
  description: "Куди поділася решта набору?"
  components:
  - type: Instrument
    program: 117
  - type: Sprite
    state: toms

- type: entity
  parent: [ BasePlaceableInstrument, ConstructibleMachine]
  id: DawInstrument
  name: "цифрова аудіо робоча станція"
  description: "Найсучасніші музичні технології, прямо з 90-х років."
  components:
  - type: Sprite
    layers:
    - state: daw-base
    - state: daw-display
      shader: unshaded
    - state: daw-panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
  - type: Appearance
  - type: WiresVisuals
  - type: WiresPanel
  - type: Machine
    board: DawInstrumentMachineCircuitboard
  - type: Instrument
    allowPercussion: true
    allowProgramChange: true
