- type: constructionGraph
  id: Cyborg
  start: start
  graph:
  - node: start
    entity: CyborgEndoskeleton
    edges:

    # empty the parts via prying
    - to: start
      conditions:
      - !type:ContainerNotEmpty
        container: part-container
      steps:
      - tool: Prying
        doAfter: 0.5
        completed:
          - !type:EmptyAllContainers

    - to: cyborg
      steps:
      - assemblyId: generic
        guideString: borg-construction-guide-string

      - material: Cable
        amount: 1
        doAfter: 1
        store: part-container

      - component: Flash
        name: flash
        store: part-container
        icon:
          sprite: Objects/Weapons/Melee/flash.rsi
          state: flash

      - component: Flash
        name: second flash
        store: part-container
        icon:
          sprite: Objects/Weapons/Melee/flash.rsi
          state: flash

      - tool: Screwing
        doAfter: 0.5

    - to: engineer
      steps:
      - assemblyId: engineer
        guideString: borg-construction-guide-string

      - material: Cable
        amount: 1
        doAfter: 1
        store: part-container

      - component: Flash
        name: flash
        store: part-container
        icon:
          sprite: Objects/Weapons/Melee/flash.rsi
          state: flash

      - component: Flash
        name: second flash
        store: part-container
        icon:
          sprite: Objects/Weapons/Melee/flash.rsi
          state: flash

      - tool: Screwing
        doAfter: 0.5

    - to: janitor
      steps:
      - assemblyId: janitor
        guideString: borg-construction-guide-string

      - material: Cable
        amount: 1
        doAfter: 1
        store: part-container

      - component: Flash
        name: flash
        store: part-container
        icon:
          sprite: Objects/Weapons/Melee/flash.rsi
          state: flash

      - component: Flash
        name: second flash
        store: part-container
        icon:
          sprite: Objects/Weapons/Melee/flash.rsi
          state: flash

      - tool: Screwing
        doAfter: 0.5

    - to: medical
      steps:
      - assemblyId: medical
        guideString: borg-construction-guide-string

      - material: Cable
        amount: 1
        doAfter: 1
        store: part-container

      - component: Flash
        name: flash
        store: part-container
        icon:
          sprite: Objects/Weapons/Melee/flash.rsi
          state: flash

      - component: Flash
        name: second flash
        store: part-container
        icon:
          sprite: Objects/Weapons/Melee/flash.rsi
          state: flash

      - tool: Screwing
        doAfter: 0.5

    - to: mining
      steps:
      - assemblyId: mining
        guideString: borg-construction-guide-string

      - material: Cable
        amount: 1
        doAfter: 1
        store: part-container

      - component: Flash
        name: flash
        store: part-container
        icon:
          sprite: Objects/Weapons/Melee/flash.rsi
          state: flash

      - component: Flash
        name: second flash
        store: part-container
        icon:
          sprite: Objects/Weapons/Melee/flash.rsi
          state: flash

      - tool: Screwing
        doAfter: 0.5

    - to: service
      steps:
      - assemblyId: service
        guideString: borg-construction-guide-string

      - material: Cable
        amount: 1
        doAfter: 1
        store: part-container

      - component: Flash
        name: flash
        store: part-container
        icon:
          sprite: Objects/Weapons/Melee/flash.rsi
          state: flash

      - component: Flash
        name: second flash
        store: part-container
        icon:
          sprite: Objects/Weapons/Melee/flash.rsi
          state: flash

      - tool: Screwing
        doAfter: 0.5

  - node: cyborg
    entity: BorgChassisGenericBare

  - node: engineer
    entity: BorgChassisEngineerBare

  - node: janitor
    entity: BorgChassisJanitorBare

  - node: mining
    entity: BorgChassisMiningBare

  - node: medical
    entity: BorgChassisMedicalBare

  - node: service
    entity: BorgChassisServiceBare

  - node: syndicateassault
    entity: BorgChassisSyndicateAssault

  - node: syndicatemedical
    entity: BorgChassisSyndicateMedical

  - node: syndicatesaboteur
    entity: BorgChassisSyndicateSaboteur
