- type: stack
  id: Ointment
  name: "мазь"
  icon: { sprite: "/Textures/Objects/Specific/Medical/medical.rsi", state: ointment }
  spawn: Ointment
  maxCount: 10

- type: stack
  id: AloeCream
  name: "крем з алое"
  icon: { sprite: "/Textures/Objects/Specific/Hydroponics/aloe.rsi", state: cream }
  spawn: AloeCream
  maxCount: 15 #Changed to 15 due to shitmed changes

- type: stack
  id: Gauze
  name: "марля"
  icon: { sprite: "/Textures/Objects/Specific/Medical/medical.rsi", state: gauze }
  spawn: Gauze
  maxCount: 15 #Changed to 15 due to shitmed changes

- type: stack
  id: Brutepack
  name: "брутпак"
  icon: { sprite: "/Textures/Objects/Specific/Medical/medical.rsi", state: gauze }
  spawn: Brutepack
  maxCount: 15 #Changed to 15 due to shitmed changes

- type: stack
  id: Bloodpack
  name: "пакет крові"
  icon: { sprite: "/Textures/Objects/Specific/Medical/medical.rsi", state: bloodpack }
  spawn: Bloodpack
  maxCount: 15 #Changed to 15 due to shitmed changes

- type: stack
  id: MedicatedSuture
  name: "медикаментозний кетгут"
  icon: {sprite: "/Textures/Objects/Specific/Medical/medical.rsi", state: medicated-suture }
  spawn: MedicatedSuture
  maxCount: 15 #Changed to 15 due to shitmed changes

- type: stack
  id: RegenerativeMesh
  name: "регенеративна сітка"
  icon: {sprite: "/Textures/Objects/Specific/Medical/medical.rsi", state: regenerative-mesh}
  spawn: RegenerativeMesh
  maxCount: 15 #Changed to 15 due to shitmed changes


