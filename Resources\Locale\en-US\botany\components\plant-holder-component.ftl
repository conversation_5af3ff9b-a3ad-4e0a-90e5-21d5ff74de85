## Entity

plant-holder-component-plant-success-message = You plant the {$seedName} {$seedNoun}.
plant-holder-component-already-seeded-message = The {$name} already has seeds in it!
plant-holder-component-remove-weeds-message = You remove the weeds from the {$name}.
plant-holder-component-remove-weeds-others-message = {$otherName} starts uprooting the weeds.
plant-holder-component-no-weeds-message = This plot is devoid of weeds! It doesn't need uprooting.
plant-holder-component-remove-plant-message = You remove the plant from the {$name}.
plant-holder-component-remove-plant-others-message = {$name} removes the plant.
plant-holder-component-no-plant-message = There is no plant to remove.
plant-holder-component-empty-message = {$owner} is empty!
plant-holder-component-spray-message = You spray {$owner}.
plant-holder-component-transfer-message = You transfer {$amount}u to {$owner}.
plant-holder-component-nothing-to-sample-message = There is nothing to take a sample of!
plant-holder-component-already-sampled-message = This plant has already been sampled.
plant-holder-component-dead-plant-message = This plant is dead.
plant-holder-component-take-sample-message = You take a sample from the {$seedName}.
plant-holder-component-compost-message = You compost {$usingItem} into {$owner}.
plant-holder-component-compost-others-message = {$user} composts {$usingItem} into {$owner}.
plant-holder-component-nothing-planted-message = It has nothing planted in it..
plant-holder-component-something-already-growing-message = [color=green]{$seedName}[/color] {$toBeForm} growing here.
plant-holder-component-something-already-growing-low-health-message = The plant looks [color=red]{$healthState}[/color].
plant-holder-component-plant-old-adjective = old and wilting
plant-holder-component-plant-unhealthy-adjective = unhealthy
plant-holder-component-dead-plant-matter-message = It's full of [color=red]dead plant matter[/color].
plant-holder-component-weed-high-level-message = It's filled with [color=green]weeds[/color]!
plant-holder-component-pest-high-level-message = It's filled with [color=gray]tiny worms[/color]!
plant-holder-component-water-level-message = Water:     [color=cyan]{$waterLevel}[/color].
plant-holder-component-nutrient-level-message = Nutrient: [color=orange]{$nutritionLevel}[/color].
plant-holder-component-toxins-high-warning = The [color=red]toxicity level alert[/color] is flashing red.
plant-holder-component-light-improper-warning = The [color=yellow]improper light level alert[/color] is blinking.
plant-holder-component-heat-improper-warning = The [color=orange]improper temperature level alert[/color] is blinking.
plant-holder-component-pressure-improper-warning = The [color=lightblue]improper environment pressure alert[/color] is blinking.
plant-holder-component-gas-missing-warning = The [color=cyan]improper gas environment alert[/color] is blinking.
plant-holder-component-early-sample-message = The plant hasn't grown enough to take a sample yet.
