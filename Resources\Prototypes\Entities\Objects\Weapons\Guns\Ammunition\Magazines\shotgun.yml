- type: entity
  id: BaseMagazineShotgun
  name: "барабан для боєприпасів (набої калібру .50)"
  parent: BaseItem
  abstract: true
  components:
  - type: Tag
    tags:
    - MagazineShotgun
  - type: BallisticAmmoProvider
    mayTransfer: true
    whitelist:
      tags:
        - ShellShotgun
    capacity: 8
    soundRack:
      path: /Audio/Weapons/Guns/Cock/smg_cock.ogg
  - type: Item
    size: Small
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Magazine/Shotgun/m12.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 2
    zeroVisible: false
  - type: Appearance

- type: entity
  id: MagazineShotgunEmpty
  name: "барабан для набоїв (.50 будь-який)"
  suffix: empty
  parent: BaseMagazineShotgun
  components:
  - type: BallisticAmmoProvider
    proto: null

- type: entity
  id: MagazineShotgun
  name: "барабан для боєприпасів (калібр .50)"
  parent: BaseMagazineShotgun
  components:
  - type: BallisticAmmoProvider
    proto: ShellShotgun
  - type: Sprite
    layers:
    - state: pellets
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineShotgunBeanbag
  name: "боєприпасний барабан (бобові мішки калібру .50)"
  parent: BaseMagazineShotgun
  components:
  - type: BallisticAmmoProvider
    proto: ShellShotgunBeanbag
  - type: Sprite
    layers:
    - state: beanbag
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineShotgunSlug
  name: "барабан для набоїв (куля .50)"
  parent: BaseMagazineShotgun
  components:
  - type: BallisticAmmoProvider
    proto: ShellShotgunSlug
  - type: Sprite
    layers:
    - state: slug
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineShotgunIncendiary
  name: "барабанний магазин (.50 запальний)"
  parent: BaseMagazineShotgun
  components:
  - type: BallisticAmmoProvider
    proto: ShellShotgunIncendiary
  - type: Sprite
    layers:
    - state: slug
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineShotgunUranium
  name: "барабан з боєприпасами (.50 урановий слаг)"
  parent: BaseMagazineShotgun
  components:
  - type: BallisticAmmoProvider
    proto: ShellShotgunUranium
  - type: Sprite
    layers:
    - state: slug
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineShotgunBirdshot
  name: "барабан з боєприпасами (.50 дріб)"
  parent: BaseMagazineShotgun
  components:
  - type: BallisticAmmoProvider
    proto: ShellShotgunBirdshot
  - type: Sprite
    layers:
    - state: pellets
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineShotgun00Buckshot
  name: "барабан з боєприпасами (.50 00-Картеч)"
  parent: BaseMagazineShotgun
  components:
  - type: BallisticAmmoProvider
    proto: ShellShotgun00Buckshot
  - type: Sprite
    layers:
    - state: pellets
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineShotgun0000Buckshot
  name: "барабан з боєприпасами (.50 0000-Картеч)"
  parent: BaseMagazineShotgun
  components:
  - type: BallisticAmmoProvider
    proto: ShellShotgun0000Buckshot
  - type: Sprite
    layers:
    - state: pellets
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
