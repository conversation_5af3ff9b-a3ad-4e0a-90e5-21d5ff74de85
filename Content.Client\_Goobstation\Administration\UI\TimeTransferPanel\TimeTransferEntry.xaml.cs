using Content.Shared.Roles;
using Content.Shared.StatusIcon;
using Robust.Client.AutoGenerated;
using Robust.Client.GameObjects;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;

namespace Content.Client._Goobstation.Administration.UI.TimeTransferPanel;

[GenerateTypedNameReferences]
public sealed partial class TimeTransferEntry : BoxContainer
{
    public string PlaytimeTracker;
    public string JobName;

    public TimeTransferEntry(JobPrototype jobProto, SpriteSystem spriteSystem, IPrototypeManager prototypeManager)
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        PlaytimeTracker = jobProto.PlayTimeTracker;
        JobLabel.Text = jobProto.LocalizedName;
        JobName = jobProto.LocalizedName;

        if (prototypeManager.TryIndex<JobIconPrototype>(jobProto.Icon, out var jobIcon))
            JobIcon.Texture = spriteSystem.Frame0(jobIcon.Icon);
    }

    public void UpdateGroupVisibility(bool inGrouped)
    {
        TimeLabel.Visible = !inGrouped;
        TimeEdit.Visible = !inGrouped;
        GroupCheckbox.Visible = inGrouped;
    }

    public string GetJobTimeString()
    {
        return TimeEdit.Text != null ? TimeEdit.Text : "";
    }

    public bool InGroup()
    {
        return GroupCheckbox.Pressed;
    }
}
