# TODO: Removes the icon components from all these when that gets fixed.

- type: entity
  parent: BaseItem
  id: BaseFoodCondiment
  abstract: true
  components:
  - type: DrawableSolution
    solution: food
  - type: DrainableSolution
    solution: food
  - type: Sprite
    sprite: Objects/Consumable/Food/condiments.rsi
  - type: Icon
    sprite: Objects/Consumable/Food/condiments.rsi
  - type: TrashOnSolutionEmpty
    solution: food
  - type: SpaceGarbage
  - type: StaticPrice
    price: 0

# Packets

- type: entity
  parent: BaseFoodCondiment
  id: BaseFoodCondimentPacket
  name: "пакет приправ"
  abstract: true
  description: "Невелика пластикова упаковка з приправами, щоб посипати їжу."
  components:
  - type: Item
    size: Tiny
  - type: Drink
    solution: food
  - type: Openable
    sound:
      collection: packetOpenSounds
  # Since this one is closed, the only way to insert liquid is with a syringe.
  - type: InjectableSolution
    solution: food
  # Note NOT refillable.
  # It be a shame if it turned out ALL drinks were ALWAYS refillable.... ffs.
  # Well its fixed now, but I want to share my pain.
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
  - type: SolutionTransfer
    maxTransferAmount: 10
  - type: Sprite
    layers:
      - state: packet-trans-2
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: true
      - state: packet-layer
        map: ["enum.OpenableVisuals.Layer"]
      - state: packet-label
  - type: GenericVisualizer
    visuals:
      enum.OpenableVisuals.Opened:
        enum.OpenableVisuals.Layer:
          True: {state: "packet-layer-open"}
          False: {state: "packet-layer"}
  - type: Icon
    state: packet-empty
  - type: PhysicalComposition
    materialComposition:
      Plastic: 50
  - type: Tag
    tags:
      - Packet

- type: entity
  parent: BaseFoodCondimentPacket
  id: FoodCondimentPacketAstrotame
  name: "Астротам"
  description: "Солодкість тисячі цукрів, але жодної калорії."
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Astrotame
          Quantity: 10
  - type: Sprite
    layers:
      - state: packet-trans-2
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: true
      - state: packet-layer
        map: ["enum.OpenableVisuals.Layer"]
      - state: packet-label-a
  - type: Icon
    state: packet-astrotame
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 2
    fillBaseName: packet-trans-

- type: entity
  parent: BaseFoodCondimentPacket
  id: FoodCondimentPacketBbq
  name: "Соус барбекю"
  description: "Серветки для рук не входять до комплекту."
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: BbqSauce
          Quantity: 10
  - type: Icon
    state: packet-bbq
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 2
    fillBaseName: packet-trans-

- type: entity
  parent: BaseFoodCondimentPacket
  id: FoodCondimentPacketCornoil
  name: "кукурудзяна олія"
  description: "Кукурудзяна олія. Смачна олія, яку використовують у кулінарії. Виготовляється з кукурудзи."
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Cornoil
          Quantity: 10
  - type: Icon
    state: packet-cornoil
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 2
    fillBaseName: packet-trans-

- type: entity
  parent: BaseFoodCondimentPacket
  id: FoodCondimentPacketFrostoil
  name: "холодний соус"
  description: "Холодний соус. Залишає язик онімілим при проходженні."
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Frostoil
          Quantity: 10
  - type: Icon
    state: packet-frostoil
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 2
    fillBaseName: packet-trans-

- type: entity
  parent: BaseFoodCondimentPacket
  id: FoodCondimentPacketHorseradish
  name: "соус з хрону"
  description: "Пачка смердючого соусу з хроном."
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: HorseradishSauce
          Quantity: 10
  - type: Icon
    state: packet-greygoo
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 2
    fillBaseName: packet-solid-

- type: entity
  parent: BaseFoodCondimentPacket
  id: FoodCondimentPacketHotsauce
  name: "гострий соус"
  description: "Тепер ви можете майже СМАКУВАТИ виразку шлунка!"
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 10
        reagents:
        - ReagentId: Hotsauce
          Quantity: 10
  - type: Icon
    state: packet-hotsauce
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 2
    fillBaseName: packet-trans-

- type: entity
  parent: BaseFoodCondimentPacket
  id: FoodCondimentPacketKetchup
  name: "кетчуп"
  description: "Ти вже відчуваєш себе більш американцем."
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Ketchup
          Quantity: 10
  - type: Sprite
  - type: Icon
    state: packet-ketchup
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 2
    fillBaseName: packet-solid-

- type: entity
  parent: BaseFoodCondimentPacket
  id: FoodCondimentPacketMustard
  name: "гірчиця"
  description: "Приправа, виготовлена з розтертого насіння гірчиці."
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Mustard
          Quantity: 10
  - type: Icon
    state: packet-mustard
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 2
    fillBaseName: packet-solid-

- type: entity
  parent: BaseFoodCondimentPacket
  id: FoodCondimentPacketPepper
  name: "чорний перець"
  description: "Часто використовується для ароматизації їжі або для того, щоб змусити людей чхати."
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Blackpepper
          Quantity: 10
  - type: Icon
    state: packet-pepper
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 2
    fillBaseName: packet-solid-

- type: entity
  parent: BaseFoodCondimentPacket
  id: FoodCondimentPacketSalt
  name: "сіль"
  description: "Сіль. З космічних океанів, імовірно."
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: TableSalt
          Quantity: 10
  - type: Sprite
    layers:
      - state: packet-solid-2
        map: ["enum.SolutionContainerLayers.Fill"]
        visible: true
      - state: packet-layer
        map: ["enum.OpenableVisuals.Layer"]
      - state: packet-label
        color: black
  - type: Icon
    state: packet-salt
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 2
    fillBaseName: packet-solid-

- type: entity
  parent: BaseFoodCondimentPacket
  id: FoodCondimentPacketSoy
  name: "соєвий соус"
  description: "Солоний ароматизатор на основі сої."
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Soysauce
          Quantity: 10
  - type: Icon
    state: packet-soysauce
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 2
    fillBaseName: packet-solid-

- type: entity
  parent: BaseFoodCondimentPacket
  id: FoodCondimentPacketSugar
  name: "цукор"
  description: "Смачний космічний цукор!"
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Sugar
          Quantity: 10
  - type: Icon
    state: packet-sugar
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 2
    fillBaseName: packet-solid-

# Bottles

- type: entity
  parent: BaseFoodCondiment
  id: BaseFoodCondimentBottle
  abstract: true
  name: "пляшка для приправ"
  description: "Тонка скляна пляшка для зберігання приправ."
  components:
  - type: Drink
    solution: food
  - type: Openable
    sound:
      collection: pop
    closeable: true
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 30
  - type: RefillableSolution
    solution: food
  - type: Spillable
    solution: food
  - type: SolutionTransfer
    canChangeTransferAmount: true
    minTransferAmount: 5
    maxTransferAmount: 30
  - type: Sprite
    state: bottle-empty
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 6
    fillBaseName: bottle-alpha-
  - type: GenericVisualizer
    visuals:
      enum.OpenableVisuals.Opened:
        enum.OpenableVisuals.Layer:
          True: {state: "bottle-open"}
          False: {state: "bottle-empty"}
  - type: PhysicalComposition
    materialComposition:
      Glass: 50

- type: entity
  parent: BaseFoodCondimentBottle
  id: FoodCondimentBottleColdsauce
  name: "пляшка для холодного соусу"
  description: "Залишає язик онімілим при проходженні."
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Frostoil
          Quantity: 30
  - type: Sprite
    layers:
    - state: bottle-empty
      map: ["enum.OpenableVisuals.Layer"]
    - state: label-c
    - state: bottle-alpha-6
      map: ["enum.SolutionContainerLayers.Fill"]
      visible: true
  - type: Icon
    state: bottle-coldsauce
  - type: Tag
    tags:
      - Coldsauce

- type: entity
  parent: BaseFoodCondimentBottle
  id: FoodCondimentBottleEnzyme
  name: "універсальний фермент"
  description: "Використовується в приготуванні різних страв."
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Enzyme
          Quantity: 30
  - type: Sprite
    layers:
    - state: bottle-empty
      map: ["enum.OpenableVisuals.Layer"]
    - state: label-e
    - state: bottle-alpha-6
      map: ["enum.SolutionContainerLayers.Fill"]
      visible: true
  - type: Icon
    state: bottle-enzyme
  - type: Tag
    tags:
      - Enzyme

- type: entity
  parent: BaseFoodCondimentBottle
  id: FoodCondimentBottleVinegar
  name: "пляшка оцту"
  description: "Використовується в кулінарії для підсилення смаку."
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Vinegar
          Quantity: 30
  - type: Sprite
    layers:
    - state: bottle-empty
      map: ["enum.OpenableVisuals.Layer"]
    - state: label-v
    - state: bottle-alpha-6
      map: ["enum.SolutionContainerLayers.Fill"]
      visible: true
  - type: Icon
    state: bottle-vinegar

- type: entity
  parent: BaseFoodCondimentBottle
  id: FoodCondimentBottleHotsauce
  name: "пляшка з гострим соусом"
  description: "Тепер ви можете майже СМАКУВАТИ виразку шлунка!"
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Hotsauce
          Quantity: 30
  - type: Sprite
    layers:
    - state: bottle-empty
      map: ["enum.OpenableVisuals.Layer"]
    - state: label-h
    - state: bottle-alpha-6
      map: ["enum.SolutionContainerLayers.Fill"]
      visible: true
  - type: Icon
    state: bottle-hotsauce
  - type: Tag
    tags:
      - Hotsauce

- type: entity
  parent: BaseFoodCondimentBottle
  id: FoodCondimentBottleKetchup
  name: "пляшка з-під кетчупу"
  description: "Ти вже відчуваєш себе більш американцем."
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Ketchup
          Quantity: 30
  - type: Sprite
    layers:
    - state: bottle-empty
      map: ["enum.OpenableVisuals.Layer"]
    - state: label-k
    - state: bottle-alpha-6
      map: ["enum.SolutionContainerLayers.Fill"]
      visible: true
  - type: Icon
    state: bottle-ketchup
  - type: Tag
    tags:
      - Ketchup

- type: entity
  parent: BaseFoodCondimentBottle
  id: FoodCondimentBottleBBQ
  name: "Пляшка з соусом для барбекю"
  description: "Серветки для рук не входять до комплекту."
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: BbqSauce
          Quantity: 30
  - type: Sprite
    layers:
    - state: bottle-empty
      map: ["enum.OpenableVisuals.Layer"]
    - state: label-b
    - state: bottle-alpha-6
      map: ["enum.SolutionContainerLayers.Fill"]
      visible: true
  # So there's no error.
  - type: Icon
    state: bottle-ketchup
  - type: Tag
    tags:
      - BBQsauce

# Shakers

- type: entity
  parent: BaseFoodCondiment
  id: BaseFoodShaker
  abstract: true
  name: "порожній шейкер"
  description: "Шейкер для зберігання та дозування спецій."
  components:
  - type: Item
    size: Tiny
  - type: Drink
    solution: food
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
  - type: SolutionTransfer
    canChangeTransferAmount: true
    minTransferAmount: 5
    maxTransferAmount: 20
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 5
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: GlassBreak
      - !type:SpillBehavior { }
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlass:
            min: 1
            max: 1
        transferForensics: true
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Sprite
    state: shaker-empty
  - type: RefillableSolution
    solution: food

- type: entity
  parent: BaseFoodShaker
  id: FoodShakerSalt
  name: "сільничка"
  description: "Сіль. З космічних океанів, імовірно."
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: TableSalt
          Quantity: 20
  - type: Sprite
    state: shaker-salt
  - type: Icon
    state: shaker-salt
  - type: Tag
    tags:
      - SaltShaker

- type: entity
  parent: BaseFoodShaker
  id: FoodShakerPepper
  name: "шейкер для перцю"
  description: "Часто використовується для ароматизації їжі або для того, щоб змусити людей чхати."
  components:
  - type: SolutionContainerManager
    solutions:
      food:
        reagents:
        - ReagentId: Blackpepper
          Quantity: 20
  - type: Sprite
    state: shaker-pepper
  - type: Icon
    state: shaker-pepper
  - type: Tag
    tags:
      - PepperShaker
