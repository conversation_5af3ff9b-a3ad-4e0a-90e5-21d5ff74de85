using System.Numerics;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Animations;

namespace Content.Client.UserInterface.Systems.Radial.Controls;

[GenerateTypedNameReferences]
public sealed partial class RadialItem : Control
{
    /// <summary>
    /// Item local position in parent control.
    /// </summary>
    [Animatable]
    public Vector2 Offset { get; set; }

    /// <summary>
    /// Item text content, what showed by focusing on item
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// Set item background sprite/texture.
    /// </summary>
    public string Texture
    {
        set => Controller.TexturePath = value;
    }

    /// <summary>
    /// Set item background transparency in bytes (0 - 255)
    /// </summary>
    public byte Opacity
    {
        get => Controller.Modulate.AByte;
        set => Controller.Modulate = new Color(255, 255, 255, value);
    }

    /// <summary>
    /// Tooltip, works like Button.Tooltip
    /// </summary>
    public string? Tooltip
    {
        set => Controller.ToolTip = value;
        get => Controller.ToolTip;
    }

    /// <summary>
    /// TooltipDelay, works like Button.TooltipDelay
    /// </summary>
    public float? TooltipDelay
    {
        set => Controller.TooltipDelay = value;
        get => Controller.TooltipDelay;
    }

    [Animatable]
    public Vector2 ButtonSize
    {
        get => this.Size;
        set => this.SetSize = value;
    }

    public RadialItem()
    {
        RobustXamlLoader.Load(this);
        Offset = Vector2.Zero;
    }
}
