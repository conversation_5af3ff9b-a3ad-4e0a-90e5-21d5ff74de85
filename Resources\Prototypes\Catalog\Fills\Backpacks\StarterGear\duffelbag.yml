- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelClown
  id: ClothingBackpackDuffelClownFilled
  components:
  - type: StorageFill
    contents:
      - id: RubberStampClown

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelBrigmedic
  id: ClothingBackpackDuffelBrigmedicFilled
  components:
  - type: StorageFill
    contents:
      - id: EmergencyRollerBedSpawnFolded
      - id: BodyBagFolded
      - id: Portafib
    #  - id: Flash
      - id: BruteAutoInjector
        amount: 2
      - id: BurnAutoInjector
        amount: 2

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelSecurity
  id: ClothingBackpackDuffelSecurityFilled
  components:
  - type: StorageFill
    contents:
      - id: MagazinePistol

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelSecurity
  id: ClothingBackpackDuffelSecurityFilledDetective
  components:
  - type: StorageFill
    contents:
      - id: ForensicPad
      - id: ForensicScanner

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelMedical
  id: ClothingBackpackDuffelMedicalFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelMedical
  id: ClothingBackpackDuffelParamedicFilled
  components:
  - type: StorageFill
    contents:
      - id: EmergencyRollerBedSpawnFolded

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelCaptain
  id: ClothingBackpackDuffelCaptainFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      #- name: StationCharter
      #- name: TelescopicBaton

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelEngineering
  id: ClothingBackpackDuffelChiefEngineerFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      #- id: TelescopicBaton

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelScience
  id: ClothingBackpackDuffelResearchDirectorFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      #- id: TelescopicBaton

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelHOPFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      #- id: TelescopicBaton

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelMedical
  id: ClothingBackpackDuffelCMOFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      #- id: TelescopicBaton

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelCargo
  id: ClothingBackpackDuffelQuartermasterFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      #- id: TelescopicBaton

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelSecurity
  id: ClothingBackpackDuffelHOSFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      - id: MagazinePistol

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelEngineering
  id: ClothingBackpackDuffelEngineeringFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelAtmospherics
  id: ClothingBackpackDuffelAtmosphericsFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelScience
  id: ClothingBackpackDuffelScienceFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelRobotics
  id: ClothingBackpackDuffelRoboticsFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelHydroponics
  id: ClothingBackpackDuffelHydroponicsFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelMime
  id: ClothingBackpackDuffelMimeFilled
  components:
  - type: StorageFill
    contents:
      - id: RubberStampMime

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelChemistry
  id: ClothingBackpackDuffelChemistryFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelChaplainFilled
  components:
  - type: StorageFill
    contents:
      - id: Bible
      - id: RubberStampChaplain

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelMusicianFilled
  components:
    - type: StorageFill
      contents:
        - id: AcousticGuitarInstrument
        - id: SaxophoneInstrument

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelLibrarianFilled
  components:
    - type: StorageFill
      contents:
        - id: BookRandom

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffel
  id: ClothingBackpackDuffelDetectiveFilled
  components:
    - type: StorageFill
      contents:
      - id: Lighter
      - id: CigPackBlack
      - id: BoxForensicPad
      - id: HandLabeler

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelCargo
  id: ClothingBackpackDuffelCargoFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackDuffelSalvage
  id: ClothingBackpackDuffelSalvageFilled
