- type: entity
  parent: Clothing
  id: IDCardStandard
  name: "посвідчення особи"
  description: "Картка, необхідна для доступу до різних зон на борту станції."
  abstract: true
  components:
  - type: Sprite
    sprite: Objects/Misc/id_cards.rsi
  - type: Clothing
    slots:
    - idcard
    sprite: Objects/Misc/id_cards.rsi
  - type: Item
    size: Small
    heldPrefix: default
    storedRotation: -90
  - type: Access
  - type: IdCard
  - type: StationRecordKeyStorage
  - type: Tag
    tags:
    - DoorBumpOpener
    - WhitelistChameleon
  - type: StealTarget
    stealGroup: IDCard
  - type: NanoChatCard # DeltaV
  - type: MiningPoints # DeltaV

#IDs with layers

- type: entity
  parent: IDCardStandard
  id: PassengerIDCard
  name: "iD-карта пасажира"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#878787"
    - state: subdepartment
      color: "#878787"
    - state: passenger
  - type: PresetIdCard
    job: Passenger

- type: entity
  parent: PassengerIDCard
  id: TechnicalAssistantIDCard
  name: "iD-карта технічного асистента"
  components:
  - type: PresetIdCard
    job: TechnicalAssistant
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#F39F27"
    - state: subdepartment
      color: "#F39F27"
    - state: assistant

- type: entity
  parent: PassengerIDCard
  id: MedicalInternIDCard
  name: "iD-карта інтерна"
  components:
  - type: PresetIdCard
    job: MedicalIntern
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#5B97BC"
    - state: subdepartment
      color: "#5B97BC"
    - state: assistant

- type: entity
  parent: PassengerIDCard
  id: ResearchAssistantIDCard
  name: "iD-карта каукового асистента"
  components:
  - type: PresetIdCard
    job: ResearchAssistant
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#C96DBF"
    - state: subdepartment
      color: "#C96DBF"
    - state: assistant

- type: entity
  parent: PassengerIDCard
  id: SecurityCadetIDCard
  name: "iD-карта кадета служби безпеки"
  components:
  - type: PresetIdCard
    job: SecurityCadet
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#CB0000"
    - state: subdepartment
      color: "#CB0000"
    - state: assistant

- type: entity
  parent: PassengerIDCard
  id: ServiceWorkerIDCard
  name: "iD-карта сервісного працівника"
  components:
  - type: PresetIdCard
    job: ServiceWorker
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#58C800"
    - state: subdepartment
      color: "#58C800"
    - state: assistant

- type: entity
  parent: IDCardStandard
  id: CaptainIDCard
  name: "посвідчення капітана"
  components:
  - type: Sprite
    layers:
    - state: gold
    - state: departmenthead
      color: "#1B67A5"
    - state: subdepartment
      color: "#1B67A5"
    - state: captain
  - type: Item
    heldPrefix: gold
  - type: PresetIdCard
    job: Captain
  - type: Tag
    tags:
    - DoorBumpOpener
    - WhitelistChameleon
    - HighRiskItem
  - type: StealTarget
    stealGroup: CaptainIDCard

- type: entity
  parent: IDCardStandard
  id: SecurityIDCard
  name: "iD-карта служби безпеки"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#CB0000"
    - state: subdepartment
      color: "#CB0000"
    - state: securityofficer
  - type: PresetIdCard
    job: SecurityOfficer

- type: entity
  parent: IDCardStandard
  id: WardenIDCard
  name: "iD-карта наглядача"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#CB0000"
    - state: subdepartment
      color: "#CB0000"
    - state: warden
  - type: PresetIdCard
    job: Warden

- type: entity
  parent: IDCardStandard
  id: EngineeringIDCard
  name: "iD-карта інженера"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#F39F27"
    - state: subdepartment
      color: "#F39F27"
    - state: stationengineer
  - type: PresetIdCard
    job: StationEngineer

- type: entity
  parent: IDCardStandard
  id: MedicalIDCard
  name: "iD-карта медика"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#5B97BC"
    - state: subdepartment
      color: "#5B97BC"
    - state: medicaldoctor
  - type: PresetIdCard
    job: MedicalDoctor

- type: entity
  parent: IDCardStandard
  id: ParamedicIDCard
  name: "iD-карта парамедика"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#5B97BC"
    - state: subdepartment
      color: "#5B97BC"
    - state: paramedic
  - type: PresetIdCard
    job: Paramedic

- type: entity
  parent: IDCardStandard
  id: ChemistIDCard
  name: "iD-карта хіміка"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#5B97BC"
    - state: subdepartment
      color: "#5B97BC"
    - state: chemist
  - type: PresetIdCard
    job: Chemist

- type: entity
  parent: IDCardStandard
  id: CargoIDCard
  name: "iD-карта логістики" # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#B18644"
    - state: subdepartment
      color: "#B18644"
    - state: cargotechnician
  - type: PresetIdCard
    job: CargoTechnician

- type: entity
  parent: IDCardStandard
  id: SalvageIDCard
  name: "iD-карта утилізатора"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#B18644"
    - state: subdepartment
      color: "#C96DBF"
    - state: shaftminer
  - type: PresetIdCard
    job: SalvageSpecialist

- type: entity
  parent: IDCardStandard
  id: QuartermasterIDCard
  name: "iD-карта логіста" # DeltaV - Logistics Department replacing Cargo
  components:
  - type: Sprite
    layers:
      - state: silver
      - state: departmenthead
        color: "#1B67A5"
      - state: subdepartment
        color: "#B18644"
      - state: cargotechnician
  - type: Item
    heldPrefix: silver
  - type: PresetIdCard
    job: Quartermaster

- type: entity
  parent: IDCardStandard
  id: ResearchIDCard
  name: "iD-карта вченого"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#C96DBF"
    - state: subdepartment
      color: "#C96DBF"
    - state: scientist
  - type: PresetIdCard
    job: Scientist

- type: entity
  parent: IDCardStandard
  id: RoboticsIDCard
  name: robotics ID card
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#C96DBF"
    - state: subdepartment
      color: "#952004"
    - state: scientist
  - type: PresetIdCard
    job: Roboticist

- type: entity
  parent: IDCardStandard
  id: ClownIDCard
  name: "iD-карта клоуна"
  components:
  - type: Sprite
    layers:
    - state: rainbow
    - state: department
      color: "#FF00FF"
    - state: subdepartment
      color: "#58C800"
    - state: clown
  - type: PresetIdCard
    job: Clown

- type: entity
  parent: IDCardStandard
  id: MimeIDCard
  name: "iD-карта міма"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#878787"
    - state: subdepartment
    - state: mime
  - type: PresetIdCard
    job: Mime

- type: entity
  parent: IDCardStandard
  id: ChaplainIDCard
  name: "iD-карта капелана"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#C96DBF"
    - state: subdepartment
      color: "#58C800"
    - state: chaplain
    - type: PresetIdCard
      job: Chaplain

- type: entity
  parent: IDCardStandard
  id: JanitorIDCard
  name: "iD-карта прибиральника"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#58C800"
    - state: subdepartment
      color: "#58C800"
    - state: janitor
  - type: PresetIdCard
    job: Janitor

- type: entity
  parent: IDCardStandard
  id: BartenderIDCard
  name: "iD-карта бармена"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#58C800"
    - state: subdepartment
      color: "#58C800"
    - state: bartender
  - type: PresetIdCard
    job: Bartender

- type: entity
  parent: BartenderIDCard
  id: PunPunIDCard
  name: "iD-карта Пун Пуна"
  components:
  - type: PresetIdCard
    job: Bartender
    name: Pun Pun

- type: entity
  parent: IDCardStandard
  id: ChefIDCard
  name: "iD-карта шеф-кухаря"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#58C800"
    - state: subdepartment
      color: "#58C800"
    - state: cook
  - type: PresetIdCard
    job: Chef

- type: entity
  parent: IDCardStandard
  id: BotanistIDCard
  name: "iD-карта ботаніка"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#58C800"
    - state: subdepartment
      color: "#58C800"
    - state: botanist
  - type: PresetIdCard
    job: Botanist

- type: entity
  parent: IDCardStandard
  id: LibrarianIDCard
  name: "iD-карта бібліотекаря"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#58C800"
    - state: subdepartment
      color: "#58C800"
    - state: curator
  - type: PresetIdCard
    job: Librarian

- type: entity
  parent: IDCardStandard
  id: LawyerIDCard
  name: "iD-карта адвоката" # DeltaV - Changed Lawyer to Attorney
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#878787"
    - state: subdepartment
      color: "#CB0000"
    - state: lawyer
  - type: PresetIdCard
    job: Lawyer

- type: entity
  parent: IDCardStandard
  id: ChiefJusticeIDCard
  name: "посвідчення голови суду"
  components:
    - type: PresetIdCard
      job: ChiefJustice
    - type: Sprite
      layers:
      - state: default
      - state: department
        color: "#878787"
      - state: subdepartment
        color: "#CB0000"
      - state: lawyer


- type: entity
  parent: IDCardStandard
  id: ClerkIDCard
  name: "посвідчення працівника"
  components:
    - type: PresetIdCard
      job: Clerk
    - type: Sprite
      layers:
      - state: default
      - state: department
        color: "#878787"
      - state: subdepartment
        color: "#CB0000"
      - state: lawyer

- type: entity
  parent: IDCardStandard
  id: ProsecutorIDCard
  name: "посвідчення працівника прокуратури"
  components:
    - type: PresetIdCard
      job: Prosecutor
    - type: Sprite
      layers:
      - state: default
      - state: department
        color: "#878787"
      - state: subdepartment
        color: "#CB0000"
      - state: lawyer

- type: entity
  parent: IDCardStandard
  id: HoPIDCard
  name: "iD-карта голови персоналу"
  components:
  - type: Sprite
    layers:
    - state: silver
    - state: departmenthead
      color: "#1B67A5"
    - state: subdepartment
      color: "#58C800"
    - state: headofpersonnel
  - type: Item
    heldPrefix: silver
  - type: PresetIdCard
    job: HeadOfPersonnel

- type: entity
  parent: IDCardStandard
  id: CEIDCard
  name: "iD-карта головного інженера"
  components:
  - type: Sprite
    layers:
    - state: silver
    - state: departmenthead
      color: "#1B67A5"
    - state: subdepartment
      color: "#F39F27"
    - state: stationengineer
  - type: Item
    heldPrefix: silver
  - type: PresetIdCard
    job: ChiefEngineer

- type: entity
  parent: IDCardStandard
  id: CMOIDCard
  name: "iD-карта головного лікаря"
  components:
  - type: Sprite
    layers:
    - state: silver
    - state: departmenthead
      color: "#1B67A5"
    - state: subdepartment
      color: "#5B97BC"
    - state: medicaldoctor
  - type: Item
    heldPrefix: silver
  - type: PresetIdCard
    job: ChiefMedicalOfficer

- type: entity
  parent: IDCardStandard
  id: RDIDCard
  name: "iD-карта містагога" # DeltaV - Epistemics Department replacing Science
  components:
  - type: Sprite
    layers:
    - state: silver
    - state: departmenthead
      color: "#1B67A5"
    - state: subdepartment
      color: "#C96DBF"
    - state: scientist
  - type: Item
    heldPrefix: silver
  - type: PresetIdCard
    job: ResearchDirector

- type: entity
  parent: IDCardStandard
  id: HoSIDCard
  name: "iD-карта керівника служби безпеки"
  components:
  - type: Sprite
    layers:
    - state: silver
    - state: departmenthead
      color: "#1B67A5"
    - state: subdepartment
      color: "#CB0000"
    - state: securityofficer
  - type: Item
    heldPrefix: silver
  - type: PresetIdCard
    job: HeadOfSecurity

- type: entity
  parent: IDCardStandard
  id: BrigmedicIDCard
  name: "iD-карта бріг-медика" # DeltaV - rename brigmedic to corpsman
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#CB0000"
    - state: subdepartment
      color: "#5B97BC"
    - state: medicaldoctor
  - type: PresetIdCard # Delta V - Brigmedic, see Prototypes/DeltaV/Roles/Jobs/Security/brigmedic.yml
    job: Brigmedic

- type: entity
  parent: IDCardStandard
  id: CentcomIDCard
  name: "iD-карта особи командного складу"
  components:
  - type: Sprite
    layers:
    - state: centcom
  - type: Item
    heldPrefix: blue
  - type: PresetIdCard
    job: CentralCommandOfficial
  - type: NanoChatCard # DeltaV
    listNumber: false

- type: entity
  parent: IDCardStandard
  id: ERTLeaderIDCard
  name: "ID-картакерівника ГШР"
  components:
  - type: Sprite
    layers:
    - state: centcom
    - state: departmenthead
      color: "#1B67A5"
    - state: subdepartment
      color: "#1B67A5"
    - state: captain
  - type: PresetIdCard
    job: ERTLeader
  - type: Item
    heldPrefix: gold
  - type: NanoChatCard # DeltaV
    listNumber: false

- type: entity
  parent: IDCardStandard
  id: ERTChaplainIDCard
  name: "ID-карта капелана ГШР"
  components:
  - type: Sprite
    layers:
    - state: centcom
    - state: departmenthead
      color: "#1B67A5"
    - state: subdepartment
      color: "#1B67A5"
    - state: chaplain
  - type: PresetIdCard
    job: ERTChaplain
  - type: Item
    heldPrefix: blue
  - type: NanoChatCard # DeltaV
    listNumber: false

- type: entity
  parent: IDCardStandard
  id: ERTEngineerIDCard
  name: "ID-карта інженера ГШР"
  components:
  - type: Sprite
    layers:
    - state: centcom
    - state: departmenthead
      color: "#1B67A5"
    - state: subdepartment
      color: "#F39F27"
    - state: stationengineer
  - type: PresetIdCard
    job: ERTEngineer
  - type: NanoChatCard # DeltaV
    listNumber: false

- type: entity
  parent: IDCardStandard
  id: ERTJanitorIDCard
  name: "ID-карта прибиральника ГШР"
  components:
  - type: Sprite
    layers:
    - state: centcom
    - state: departmenthead
      color: "#1B67A5"
    - state: subdepartment
      color: "#58C800"
    - state: janitor
  - type: PresetIdCard
    job: ERTJanitor
  - type: NanoChatCard # DeltaV
    listNumber: false

- type: entity
  parent: IDCardStandard
  id: ERTMedicIDCard
  name: "ID-карта медика ГШР"
  components:
  - type: Sprite
    layers:
    - state: centcom
    - state: departmenthead
      color: "#1B67A5"
    - state: subdepartment
      color: "#5B97BC"
    - state: medicaldoctor
  - type: PresetIdCard
    job: ERTMedical
  - type: NanoChatCard # DeltaV
    listNumber: false

- type: entity
  parent: IDCardStandard
  id: ERTSecurityIDCard
  name: "ID-карта служби безпеки ГШР"
  components:
  - type: Sprite
    layers:
    - state: centcom
    - state: departmenthead
      color: "#1B67A5"
    - state: subdepartment
      color: "#1B67A5"
    - state: cc
  - type: PresetIdCard
    job: ERTSecurity
  - type: NanoChatCard # DeltaV
    listNumber: false

- type: entity
  parent: IDCardStandard
  id: CentcomIDCardSyndie
  name: "iD-карта особи командного складу"
  suffix: Fake
  components:
  - type: Sprite
    layers:
    - state: centcom
    - state: departmenthead
      color: "#1B67A5"
    - state: subdepartment
      color: "#1B67A5"
    - state: cc
  - type: Item
    heldPrefix: blue
  - type: PresetIdCard
    job: CentralCommandOfficial
  - type: Access
    tags:
    - Maintenance

- type: entity
  parent: IDCardStandard
  id: MusicianIDCard
  name: "iD-карта музиканта"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#878787"
    - state: subdepartment
      color: "#878787"
    - state: musician
  - type: PresetIdCard
    job: Musician

- type: entity
  parent: CentcomIDCard
  id: CentcomIDCardDeathsquad
  name: "iD-карта члена ескадрону смерті"
  components:
  - type: Sprite
    layers:
    - state: centcom
    - state: departmenthead
      color: "#CB0000"
    - state: subdepartment
      color: "#CB0000"
    - state: death
  - type: Item
    heldPrefix: blue
  - type: PresetIdCard
    job: DeathSquad
  - type: NanoChatCard # DeltaV
    listNumber: false

- type: entity
  name: "iD-карта пасажира"
  parent: IDCardStandard
  id: AgentIDCard
  suffix: Agent
  components:
  # Goob edit
  #- type: PresetIdCard
  #  job: Passenger
  - type: Access
    tags:
    - Maintenance
    - SyndicateAgent
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#878787"
    - state: subdepartment
      color: "#878787"
    - state: passenger
  - type: AgentIDCard
  - type: ActivatableUI
    key: enum.AgentIDCardUiKey.Key
    inHandsOnly: true
  - type: Tag
    tags:
    - DoorBumpOpener
  - type: ChameleonClothing
    slot: [idcard]
    default: PassengerIDCard
  - type: UserInterface
    interfaces:
      enum.AgentIDCardUiKey.Key:
        type: AgentIDCardBoundUserInterface
      enum.ChameleonUiKey.Key:
        type: ChameleonBoundUserInterface
  - type: ClothingAddFaction
    faction: Syndicate
  - type: NanoChatCard # DeltaV
    listNumber: false

- type: entity
  name: "iD-карта пасажира"
  parent: AgentIDCard
  id: NukieAgentIDCard
  suffix: Nukie
  components:
  - type: Access
    tags:
    - Maintenance
    - SyndicateAgent
    - NuclearOperative
  - type: ClothingAddFaction
    faction: Syndicate

- type: entity
  parent: IDCardStandard
  id: AtmosIDCard
  name: "iD-карта атмосферного техніка"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#F39F27"
    - state: subdepartment
      color: "#F39F27"
    - state: atmospherictechnician
  - type: PresetIdCard
    job: AtmosphericTechnician

- type: entity
  parent: IDCardStandard
  id: SyndicateIDCard
  name: "iD-карта синдикату"
  components:
  - type: Sprite
    layers:
    - state: black
    - state: department
      color: "#CB0000"
    - state: subdepartment
      color: "#CB0000"
    - state: syndicate
  - type: Access
    tags:
    - NuclearOperative
    - SyndicateAgent
  - type: ClothingAddFaction
    faction: Syndicate
  - type: NanoChatCard # DeltaV
    listNumber: false

- type: entity
  parent: IDCardStandard
  id: PirateIDCard
  name: "iD-карта пірата"
  components:
  - type: Sprite
    layers:
    - state: black
    - state: department
      color: "#878787"
    - state: subdepartment
      color: "#878787"
    - state: death
  - type: Access
    tags:
    - NuclearOperative
    - SyndicateAgent
  - type: ClothingAddFaction
    faction: Syndicate
  - type: NanoChatCard # DeltaV
    listNumber: false

- type: entity
  parent: IDCardStandard
  id: PsychologistIDCard
  name: "iD-карта психолога"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#5B97BC"
    - state: subdepartment
      color: "#5B97BC"
    - state: psychologist
  - type: PresetIdCard
    job: Psychologist

- type: entity
  parent: IDCardStandard
  id: ReporterIDCard
  name: "iD-карта журналіста"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#58C800"
    - state: subdepartment
      color: "#58C800"
    - state: headofpersonnel
  - type: PresetIdCard
    job: Reporter

- type: entity
  parent: IDCardStandard
  id: BoxerIDCard
  name: "iD-карта боксера"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#878787"
    - state: subdepartment
      color: "#878787"
    - state: boxer
  - type: PresetIdCard
    job: Boxer

- type: entity
  parent: IDCardStandard
  id: ZookeeperIDCard
  name: "iD-карта зоолога"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#58C800"
    - state: subdepartment
      color: "#58C800"
    - state: zookeeper
  - type: PresetIdCard
    job: Zookeeper

- type: entity
  parent: IDCardStandard
  id: DetectiveIDCard
  name: "iD-карта детектива"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#CB0000"
    - state: subdepartment
      color: "#CB0000"
    - state: detective
  - type: PresetIdCard
    job: Detective

- type: entity
  parent: CentcomIDCard
  id: CBURNIDcard
  name: "ID-карта CBURN"
  suffix: CBURN
  components:
  - type: Sprite
    layers:
    - state: centcom
    - state: departmenthead
      color: "#CB0000"
    - state: subdepartment
      color: "#CB0000"
    - state: cc
  - type: Item
    heldPrefix: blue
  - type: PresetIdCard
    job: CBURN
  - type: NanoChatCard # DeltaV
    listNumber: false

- type: entity
  parent: IDCardStandard
  id: CluwneIDCard
  name: "iD-карта клувні"
  suffix: Unremoveable
  components:
  - type: Sprite
    layers:
    - state: rainbow
    - state: department
      color: "#5DC574"
    - state: subdepartment
      color: "#5DC574"
    - state: clown
      color: "#7C0A0A"
  - type: IdCard
    jobTitle: job-title-cluwne
  - type: Unremoveable
  - type: NanoChatCard # DeltaV
    listNumber: false

- type: entity
  parent: IDCardStandard
  id: SeniorEngineerIDCard
  name: "iD-карта старшого інженера"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#F39F27"
    - state: subdepartment
      color: "#F39F27"
    - state: senior
  - type: PresetIdCard
    job: SeniorEngineer

- type: entity
  parent: IDCardStandard
  id: SeniorResearcherIDCard
  name: "iD-карта старшого наукового співробітника"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#C96DBF"
    - state: subdepartment
      color: "#C96DBF"
    - state: senior
  - type: PresetIdCard
    job: SeniorResearcher

- type: entity
  parent: IDCardStandard
  id: SeniorPhysicianIDCard
  name: "iD-карта старшого лікаря"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#5B97BC"
    - state: subdepartment
      color: "#5B97BC"
    - state: senior
  - type: PresetIdCard
    job: SeniorPhysician

- type: entity
  parent: IDCardStandard
  id: SeniorOfficerIDCard
  name: "iD-карта старшого офіцера"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#CB0000"
    - state: subdepartment
      color: "#CB0000"
    - state: senior
  - type: PresetIdCard
    job: SeniorOfficer

- type: entity
  parent: IDCardStandard
  id: AdminAssistantIDCard
  name: "посвідчення адміністративного асистента"
  components:
  - type: Sprite
    layers:
    - state: silver
    - sprite: DeltaV/Objects/Misc/id_cards.rsi
      state: idadminassistant
  - type: Item
    heldPrefix: silver
  - type: PresetIdCard
    job: AdministrativeAssistant
  - type: NanoChatCard # DeltaV
    notificationsMuted: true
    listNumber: false

#PIRATE START UNTIL END OF FILE
- type: entity
  parent: IDCardStandard
  id: RoboticistIDCard
  name: "ID-карта робототехніка"
  components:
    - type: Sprite
      layers:
        - state: special
        - state: roboticist
    - type: Item
      heldPrefix: special
    - type: PresetIdCard
      job: Roboticist
