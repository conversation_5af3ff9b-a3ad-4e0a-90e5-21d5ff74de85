- type: guideEntry
  id: Chemicals
  name: guide-entry-chemicals
  text: "/ServerInfo/Guidebook/Chemicals.xml"
  children:
  - Elements
  - Medicine
  - Narcotics
  - Pyrotechnic
  - Toxins
  - Foods
  - Botanical
  - Biological
  - Others
  filterEnabled: True

- type: guideEntry
  id: Elements
  name: guide-entry-elements
  text: "/ServerInfo/Guidebook/ChemicalTabs/Elements.xml"
  filterEnabled: True

- type: guideEntry
  id: Narcotics
  name: guide-entry-narcotics
  text: "/ServerInfo/Guidebook/ChemicalTabs/Narcotics.xml"
  filterEnabled: True

- type: guideEntry
  id: Pyrotechnic
  name: guide-entry-pyrotechnics
  text: "/ServerInfo/Guidebook/ChemicalTabs/Pyrotechnic.xml"
  filterEnabled: True

- type: guideEntry
  id: Toxins
  name: guide-entry-toxins
  text: "/ServerInfo/Guidebook/ChemicalTabs/Toxins.xml"
  filterEnabled: True

- type: guideEntry
  id: Foods
  name: guide-entry-foods
  text: "/ServerInfo/Guidebook/ChemicalTabs/Foods.xml"
  filterEnabled: True

- type: guideEntry
  id: Botanical
  name: guide-entry-botanical
  text: "/ServerInfo/Guidebook/ChemicalTabs/Botany.xml"
  filterEnabled: True

- type: guideEntry
  id: Biological
  name: guide-entry-biological
  text: "/ServerInfo/Guidebook/ChemicalTabs/Biological.xml"
  filterEnabled: True

- type: guideEntry
  id: Others
  name: guide-entry-others
  text: "/ServerInfo/Guidebook/ChemicalTabs/Other.xml"
  filterEnabled: True
