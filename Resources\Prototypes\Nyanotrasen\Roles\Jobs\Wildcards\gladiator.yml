- type: job
  id: Gladiator
  name: job-name-gladiator
  description: job-description-gladiator
  playTimeTracker: JobGladiator
  startingGear: NyanoGladiatorGear
  alwaysUseSpawner: true
  canBeAntag: false
  icon: "JobIconGladiator"
  supervisors: job-supervisors-security
  setPreference: true
#  whitelistRequired: true
  requirements:
    - !type:CharacterDepartmentTimeRequirement
      department: Security
      min: 21600
    - !type:CharacterWhitelistRequirement # PIRATE - Whitelist requirement
  special:
  - !type:AddTraitSpecial
    traits:
    - MartialArtist
  afterLoadoutSpecial:
  - !type:ModifyEnvirosuitSpecial
    charges: 3
  - !type:ModifyEnvirohelmSpecial
    powerCell: PowerCellPotato

- type: startingGear
  id: NyanoGladiatorGear
  subGear:
  - GladiatorPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitGladiator
    outerClothing: ClothingOuterArmorGladiator
    id: GladiatorPDA
    ears: ClothingHeadsetGrey
  innerClothingSkirt: UniformShortsRedWithTop
  #any other possessions, spawn in cell

- type: startingGear
  id: GladiatorPlasmamanGear
  parent: BasePlasmamanGear
  equipment:
    jumpsuit: ClothingUniformEnvirosuitGladiator
    head: ClothingHeadEnvirohelmGladiator
    gloves: ClothingHandsGlovesEnviroglovesGladiator
