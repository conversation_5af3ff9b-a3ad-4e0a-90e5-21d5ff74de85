﻿- type: decal
  id: BrickTileSteel
  parent: BrickTile
  abstract: true

- type: decal
  id: BrickTileSteelBox
  parent: BrickTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: steel_box

- type: decal
  id: BrickTileSteelCornerNe
  parent: BrickTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: steel_corner_ne

- type: decal
  id: BrickTileSteelCornerSe
  parent: BrickTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: steel_corner_se

- type: decal
  id: BrickTileSteelCornerNw
  parent: BrickTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: steel_corner_nw

- type: decal
  id: BrickTileSteelCornerSw
  parent: BrickTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: steel_corner_sw

- type: decal
  id: BrickTileSteelInnerNe
  parent: BrickTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: steel_inner_ne

- type: decal
  id: BrickTileSteelInnerSe
  parent: BrickTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: steel_inner_se

- type: decal
  id: BrickTileSteelInnerNw
  parent: BrickTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: steel_inner_nw

- type: decal
  id: BrickTileSteelInnerSw
  parent: BrickTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: steel_inner_sw

- type: decal
  id: BrickTileSteelEndN
  parent: BrickTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: steel_end_n

- type: decal
  id: BrickTileSteelEndE
  parent: BrickTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: steel_end_e

- type: decal
  id: BrickTileSteelEndS
  parent: BrickTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: steel_end_s

- type: decal
  id: BrickTileSteelEndW
  parent: BrickTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: steel_end_w

- type: decal
  id: BrickTileSteelLineN
  parent: BrickTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: steel_line_n

- type: decal
  id: BrickTileSteelLineE
  parent: BrickTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: steel_line_e

- type: decal
  id: BrickTileSteelLineS
  parent: BrickTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: steel_line_s

- type: decal
  id: BrickTileSteelLineW
  parent: BrickTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/bricktile.rsi
    state: steel_line_w

