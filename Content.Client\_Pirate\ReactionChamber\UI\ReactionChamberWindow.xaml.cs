using Content.Client.Stylesheets;
using Content.Shared._Pirate.ReactionChamber.Components;
using Content.Shared.Chemistry.Reagent;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;


namespace Content.Client._Pirate.ReactionChamber.UI;
[GenerateTypedNameReferences]
public sealed partial class ReactionChamberWindow : DefaultWindow
{
    // [Dependency] private readonly IPrototypeManager _prototypeManager = default!;
    public bool Active;
    public float Temp;
    public ReactionChamberWindow()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
        SetActive(false);
    }
    public void UpdateState(ReactionChamberBoundUIState state)
    {
        ContainerInfo.RemoveAllChildren();
        ReagentsContainer.RemoveAllChildren();
        Sprite.SetEntity(null);
        if (state.Beaker is not null && state.BeakerInfo is not null)
        {

            if (state.BeakerInfo.SpinBoxTemp is not null)
                TempField.Value = state.BeakerInfo.SpinBoxTemp.Value.Float();
            Sprite.SetEntity(state.Beaker.Value);

            var beakerVolLabel = new Label();
            var beakerNameLabel = new Label();
            beakerNameLabel.Text = $"{state.BeakerInfo.Name}";
            beakerNameLabel.HorizontalAlignment = HAlignment.Center;
            beakerVolLabel.Text = $"{state.BeakerInfo.Volume}/{state.BeakerInfo.MaxVolume}";
            beakerVolLabel.HorizontalAlignment = HAlignment.Center;

            ContainerInfo.AddChild(beakerNameLabel);
            ContainerInfo.AddChild(beakerVolLabel);

            if (state.BeakerInfo.Reagents is not null)
            {
                foreach (var (reagent, quantity) in state.BeakerInfo.Reagents)
                {
                    var reagentLabel = new Label();
                    var quantityLabel = new Label
                    {
                        Text = Loc.GetString("reagent-dispenser-window-quantity-label-text", ("quantity", quantity)),
                        StyleClasses = { StyleNano.StyleClassLabelSecondaryColor },
                    };
                    reagentLabel.Text = reagent.Prototype + ": ";

                    // if (_prototypeManager.TryIndex(reagetnt.Prototype, out ReagentPrototype? p))
                    //     name = p.LocalizedName;
                    ReagentsContainer.Children.Add(new BoxContainer
                    {
                        Orientation = BoxContainer.LayoutOrientation.Horizontal,
                        Children =
                    {
                        reagentLabel,
                        quantityLabel,
                    }
                    });
                }
                TemperatureLabel.Text = $"{state.BeakerInfo.Temp}";
            }
        }

    }
    public void SetTemp(float temp)
    {
        Temp = temp;
    }
    public void SetActive(bool active)
    {
        Active = active;
        ActiveButton.Pressed = active;

        if (active)
        {
            ActiveButton.Text = "Вимкнути";
        }
        else
        {
            ActiveButton.Text = "Увімкнути";
        }
    }

}
