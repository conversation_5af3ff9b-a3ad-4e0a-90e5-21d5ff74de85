using Content.Server.GameTicking.Rules;
using Content.Server.GameTicking.Rules.Components;
using Content.Server.StationEvents.Events;
using Content.Shared._Pirate.DNDMage;
using Content.Shared.GameTicking.Components;

namespace Content.Server._Pirate.DNDMage;

public sealed class DNDMageRuleSystem : GameRuleSystem<DNDMageRuleComponent>
{
    public override void Initialize()
    {
        base.Initialize();

        // Не підписуємося на GameRuleAddedEvent тут, оскільки базовий клас вже це робить
    }

    // Замість підписки на подію, перевизначаємо метод Added з базового класу
    protected override void Added(EntityUid uid, DNDMageRuleComponent component, GameRuleComponent gameRule, GameRuleAddedEvent args)
    {
        base.Added(uid, component, gameRule, args);

        // Логіка запуску події ДНД мага
        // Тут можна додати код, який раніше був у методі OnRuleAdded
    }

    // Також можна перевизначити інші методи життєвого циклу правила гри
    protected override void Started(EntityUid uid, DNDMageRuleComponent component, GameRuleComponent gameRule, GameRuleStartedEvent args)
    {
        base.Started(uid, component, gameRule, args);

        // Логіка, яка виконується при старті правила
    }

    protected override void Ended(EntityUid uid, DNDMageRuleComponent component, GameRuleComponent gameRule, GameRuleEndedEvent args)
    {
        base.Ended(uid, component, gameRule, args);

        // Логіка, яка виконується при закінченні правила
    }
}


