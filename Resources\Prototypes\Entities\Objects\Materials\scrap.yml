- type: entity
  parent: BaseItem
  id: BaseScrap
  abstract: true
  name: "брухт"
  description: "Нікчемний мотлох. Однак ви, ймовірно, могли б отримати з нього деякі матеріали."
  suffix: Scrap
  components:
  - type: Sprite
    sprite: Objects/Materials/Scrap/generic.rsi
  - type: Item
    sprite: Objects/Materials/Scrap/generic.rsi
    size: Normal
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: FlimsyMetallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Tag
    tags:
    - Recyclable

- type: entity
  parent: BaseStructure
  id: BaseScrapLarge
  abstract: true
  name: "брухт"
  description: "Нікчемний мотлох. Однак ви, ймовірно, могли б отримати з нього деякі матеріали."
  suffix: Scrap
  components:
  - type: InteractionOutline
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: FlimsyMetallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Tag
    tags:
    - Recyclable
  - type: Transform
    anchored: False
    noRot: true
  - type: Physics
    bodyType: Dynamic
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.40,-0.40,0.40,0.40"
        density: 200
        mask:
          - MachineMask
        layer:
          - MachineLayer

- type: entity
  parent: BaseScrap
  id: ScrapSteel
  name: "купа сталі"
  description: "Купа сталі, зварена разом при екстремальній температурі. Вона пристойного розміру; ви, ймовірно, могли б отримати з неї чимало листів."
  components:
  - type: Sprite
    layers:
      - state: metal-1
        map: [ "base" ]
  - type: Item
    size: Large
    shape:
    - 0,0,4,4 # 5x5
    heldPrefix: metal
  - type: MultiHandedItem
  - type: RandomSprite
    available:
    - base:
        metal-1: ""
        metal-2: ""
        metal-3: ""
  - type: PhysicalComposition
    materialComposition:
      Steel: 3000 # 30 sheets

- type: entity
  parent: BaseScrap
  id: ScrapGlass
  name: "брухт скла"
  description: "Величезна купа різних схем, дивним чином сплавлених разом. Ймовірно, ви могли б витягти з цього деякі матеріали."
  components:
  - type: Sprite
    layers:
    - state: glass-1
      map: [ "base" ]
  - type: Item
    heldPrefix: "glass"
    size: Large
    shape:
    - 0,0,2,2 # 3x3
  - type: RandomSprite
    available:
    - base:
        glass-1: ""
        glass-2: ""
        glass-3: ""
        glass-4: ""
        glass-5: ""
        glass-6: ""
  - type: PhysicalComposition
    materialComposition:
      Glass: 1500 # 15 sheets
      Steel: 500 # 5 sheets

- type: entity
  parent: BaseScrap
  id: ScrapAirlock1
  name: "двері шлюзу"
  description: "Раніше це утримувало повітря. Тепер, здається, воно взагалі нічого не робить."
  components:
  - type: Sprite
    layers:
    - state: junk-airlock-1
      map: [ "base" ]
  - type: RandomSprite
    available:
    - base:
        junk-airlock-1: ""
        junk-airlock-2: ""
  - type: Item
    size: Large
    shape:
    - 0,0,4,4 # 5x5
    heldPrefix: "airlock"
  - type: MultiHandedItem
  - type: PhysicalComposition
    materialComposition:
      Steel: 1500 # 15 sheets

- type: entity
  parent: BaseScrap
  id: ScrapAirlock2
  name: "підсвічування шлюзу"
  description: "Так йому і треба за те, що весь час блимало червоним."
  components:
  - type: Sprite
    layers:
    - state: junk-airlock-3
  - type: Item
    size: Normal
    shape:
    - 0,0,3,0
  - type: PhysicalComposition
    materialComposition:
      Steel: 200 # 2 sheets
      Glass: 500 # 5 sheets

- type: entity
  parent: BaseScrap
  id: ScrapBucket
  name: "розбите відро"
  description: "Боже мій..."
  components:
  - type: Sprite
    layers:
    - state: junk-bucket
  - type: PhysicalComposition
    materialComposition:
      Plastic: 300 # 3 sheets

- type: entity
  parent: BaseScrap
  id: ScrapCamera
  name: "розбита камера"
  description: "Вона все ще може спостерігати."
  components:
  - type: Sprite
    layers:
    - state: junk-camera
  - type: PhysicalComposition
    materialComposition:
      Steel: 500 # 5 sheets
      Glass: 100 # 1 sheet

- type: entity
  parent: BaseScrap
  id: ScrapCanister1
  name: "ручка каністри"
  description: "Корисна для утримання каністри. Тепер вам потрібна лише сама частина каністри."
  components:
  - type: Sprite
    layers:
    - state: junk-canister-1
  - type: PhysicalComposition
    materialComposition:
      Steel: 300 # 3 sheets

- type: entity
  parent: BaseScrap
  id: ScrapCanister2
  name: "клапан каністри"
  description: "Клапан від газової каністри."
  components:
  - type: Sprite
    layers:
    - state: junk-canister-2
  - type: Item
    size: Small
  - type: PhysicalComposition
    materialComposition:
      Steel: 200 # 2 sheets

- type: entity
  parent: BaseScrap
  id: ScrapCloset
  name: "двері шафи"
  description: "Зірвані двері з шафи для технічного обслуговування. Виглядає як гарний елемент декору стін."
  components:
  - type: Sprite
    layers:
    - state: junk-closet
  - type: MultiHandedItem
  - type: Item
    heldPrefix: closet
    size: Huge
    shape:
    - 0,0,5,2
  - type: PhysicalComposition
    materialComposition:
      Steel: 1500 # 15 sheets

- type: entity
  parent: BaseScrap
  id: ScrapFaxMachine
  name: "факс"
  description: "Прикрий результат надсилання занадто великої кількості ASCII-графіки до Центрального командування."
  components:
  - type: Sprite
    layers:
    - state: junk-fax
  - type: Item
    size: Huge
  - type: PhysicalComposition
    materialComposition:
      Plastic: 800 # 8 sheets
      Steel: 200 # 2 sheets

- type: entity
  parent: BaseScrap
  id: ScrapFireExtinguisher
  name: "розколотий вогнегасник"
  description: "Велика тріщина в центрі змушує вас хвилюватися за попереднього користувача."
  components:
  - type: Sprite
    layers:
    - state: junk-fireextinguisher
  - type: PhysicalComposition
    materialComposition:
      Steel: 600 # 6 sheets

- type: entity
  parent: BaseScrap
  id: ScrapFirelock1
  name: "двері протипожежного замка"
  description: "Принаймні ви все ще знаєте, для чого він призначений."
  components:
  - type: Sprite
    layers:
    - state: junk-firelock-1
  - type: Item
    size: Large
    shape:
    - 0,0,4,4 # 5x5
    heldPrefix: firelock-1
  - type: MultiHandedItem
  - type: PhysicalComposition
    materialComposition:
      Steel: 2000 # 20 sheets

- type: entity
  parent: BaseScrap
  id: ScrapFirelock2
  name: "двері протипожежного замка"
  description: "О! Ось куди поділася інша половина!"
  components:
  - type: Sprite
    layers:
    - state: junk-firelock-2
  - type: Item
    size: Large
    shape:
    - 0,0,4,4 # 5x5
    heldPrefix: firelock-2
  - type: MultiHandedItem
  - type: PhysicalComposition
    materialComposition:
      Steel: 2000 # 20 sheets

- type: entity
  parent: BaseScrap
  id: ScrapFirelock3
  name: "рама протипожежного замка"
  components:
  - type: Sprite
    layers:
    - state: junk-firelock-3
  - type: Item
    size: Large
  - type: PhysicalComposition
    materialComposition:
      Steel: 700 # 7 sheets

- type: entity
  parent: BaseScrap
  id: ScrapIntercom
  name: "брухт переговорного пристрою"
  description: "Шкода, що він не постачається з ключами шифрування."
  components:
  - type: Sprite
    layers:
    - state: junk-intercom-1
      map: [ "base" ]
  - type: RandomSprite
    available:
    - base:
        junk-intercom-1: ""
        junk-intercom-2: ""
        junk-intercom-3: ""
  - type: Item
  - type: PhysicalComposition
    materialComposition:
      Steel: 400 # 4 sheets
      Plastic: 300 # 3 sheets

- type: entity
  parent: BaseScrap
  id: ScrapJetpack
  name: "розбитий реактивний ранець"
  description: "Схоже, він більше не літатиме."
  components:
  - type: Sprite
    layers:
    - state: junk-jetpack
  - type: Item
    size: Huge
    heldPrefix: jetpack
  - type: PhysicalComposition
    materialComposition:
      Steel: 1000 # 10 sheets
      Plastic: 200 # 2 sheets

- type: entity
  parent: BaseScrap
  id: ScrapMedkit
  name: "розбита аптечка"
  description: "Сподіваюся, вони нею скористалися."
  components:
  - type: Sprite
    layers:
    - state: junk-medkit-1
      map: [ "base" ]
  - type: RandomSprite
    available:
    - base:
        junk-medkit-1: ""
        junk-medkit-2: ""
  - type: Item
    size: Large
  - type: PhysicalComposition
    materialComposition:
      Plastic: 500 # 5 sheets

- type: entity
  parent: BaseScrap
  id: ScrapMopBucket
  name: "половина відра для швабри"
  description: "Прибиральники вмирають. Двірники виживають."
  components:
  - type: Sprite
    layers:
    - state: junk-mop-bucket
  - type: Item
    size: Huge
  - type: PhysicalComposition
    materialComposition:
      Plastic: 1500 # 15 sheets

- type: entity
  parent: BaseScrap
  id: ScrapPAI
  name: "персональний пристрій ШІ"
  description: "Друже! Ти ще там? Алло? Друже???"
  components:
  - type: Sprite
    layers:
    - state: junk-pai
  - type: Item
    size: Small
  - type: PhysicalComposition
    materialComposition:
      Steel: 300 # 3 sheets
      Plastic: 200 # 2 sheets

- type: entity
  parent: BaseScrap
  id: ScrapPAIGold
  name: "позолочений персональний пристрій ШІ"
  description: "Вау! Яка знахідка - одна на мільйон! Це коштувало б цілий статок... якби було в хорошому стані."
  components:
  - type: Sprite
    layers:
    - state: junk-pai-gold
  - type: Item
    size: Small
  - type: PhysicalComposition
    materialComposition:
      Gold: 100 # 1 sheet
      Plastic: 400 # 4 sheets

- type: entity
  parent: BaseScrap
  id: ScrapTube
  name: "розбита пробірка для зразків"
  description: "Скляна пробірка, що містить якийсь рідкісний зразок. Або, принаймні, раніше містила. Зараз там мало що залишилося."
  components:
  - type: Sprite
    layers:
    - state: tube
      map: [ "base" ]
  - type: RandomSprite
    available:
    - base:
        tube: ""
        tube-bl: ""
        tube-bk: ""
        tube-g: ""
        tube-r: ""
        tube-y: ""
  - type: Item
    size: Small
  - type: PhysicalComposition
    materialComposition:
      Glass: 500 # 5 sheets
      Plastic: 100 # 1 sheet

- type: entity
  parent: BaseScrapLarge
  id: ScrapGeneratorPlasmaLeaking
  name: "генератор P.A.C.M.A.N. з витоком плазми"
  description: "Старий на вигляд генератор P.A.C.M.A.N., у якого почали протікати плазмові баки."
  components:
  - type: Sprite
    sprite: Objects/Materials/Scrap/generator.rsi
    snapCardinals: true
    layers:
      - state: old_generator_plasma
      - state: old_generator_plasma_fuel_leak
  - type: PhysicalComposition
    materialComposition:
      Steel: 4000 # 40 sheets
      Plasma: 2500 # 25 sheets
      Plastic: 500 # 5 sheets

- type: entity
  parent: BaseScrapLarge
  id: ScrapGeneratorPlasma
  name: "старий генератор P.A.C.M.A.N"
  description: "Старий на вигляд генератор P.A.C.M.A.N. Він у дуже поганому стані та не працює."
  components:
  - type: Sprite
    sprite: Objects/Materials/Scrap/generator.rsi
    layers:
      - state: old_generator_plasma
  - type: PhysicalComposition
    materialComposition:
      Steel: 4000 # 40 sheets
      Plasma: 1000 # 10 sheets
      Plastic: 500 # 5 sheets

- type: entity
  parent: BaseScrapLarge
  id: ScrapGeneratorUraniumLeaking
  name: "генератор S.U.P.E.R.P.A.C.M.A.N. з витоком урану"
  description: "Генератор S.U.P.E.R.P.A.C.M.A.N., який, схоже, зазнав якоїсь катастрофічної аварії. З нього витікає уран."
  components:
  - type: Sprite
    sprite: Objects/Materials/Scrap/generator.rsi
    layers:
      - state: uranium_generator
      - map: [ "extra" ]
      - state: rad_dripping
        shader: unshaded
      - state: rad_outline
        shader: unshaded
  - type: RandomSprite
    available:
    - extra:
        red_x: ""
        nothing: ""
  - type: MultiHandedItem
  - type: PhysicalComposition
    materialComposition:
      Steel: 4000 # 40 sheets
      Uranium: 2500 # 25 sheets
      Plastic: 500 # 5 sheets
  - type: RadiationSource
    intensity: 1.0

- type: entity
  parent: BaseScrapLarge
  id: ScrapGeneratorUranium
  name: "зруйнований генератор S.U.P.E.R.P.A.C.M.A.N"
  description: "Генератор S.U.P.E.R.P.A.C.M.A.N., який, схоже, зазнав якоїсь катастрофічної аварії."
  components:
  - type: Sprite
    sprite: Objects/Materials/Scrap/generator.rsi
    layers:
      - state: uranium_generator
      - map: [ "extra" ]
  - type: RandomSprite
    available:
    - extra:
        red_x: ""
        nothing: ""
  - type: PhysicalComposition
    materialComposition:
      Steel: 4000 # 40 sheets
      Uranium: 1000 # 10 sheets
      Plastic: 500 # 5 sheets
  - type: RadiationSource
    intensity: 0.5

- type: entity
  parent: BaseScrapLarge
  id: ScrapGeneratorFrame
  name: "рама генератора"
  description: "Рама генератора типу P.A.C.M.A.N. або S.U.P.E.R.P.A.C.M.A.N. Де решта?"
  components:
  - type: Sprite
    sprite: Objects/Materials/Scrap/generator.rsi
    layers:
      - state: generator_frame
  - type: PhysicalComposition
    materialComposition:
      Steel: 1500 # 15 sheets

- type: entity
  parent: BaseScrap
  id: ScrapGeneratorFuelTank
  name: "паливний бак"
  description: "Паливний бак від генератора типу S.U.P.E.R.P.A.C.M.A.N. Датчик показує, що в ньому залишилося трохи палива."
  components:
  - type: Sprite
    sprite: Objects/Materials/Scrap/generator.rsi
    layers:
      - state: uranium_generator_fuel_tank
  - type: PhysicalComposition
    materialComposition:
      Steel: 200 # 2 sheets
      Uranium: 500 # 5 sheets
