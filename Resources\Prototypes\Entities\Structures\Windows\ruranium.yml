- type: entity
  id: ReinforcedUraniumWindow
  name: "армоване уранове вікно"
  parent: WindowRCDResistant
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Windows/reinforced_uranium_window.rsi
  - type: Icon
    sprite: Structures/Windows/reinforced_uranium_window.rsi
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: RGlass
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassUranium:
            min: 1
            max: 2
          PartRodMetal:
            min: 1
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: IconSmooth
    base: ruwindow
  - type: Construction
    graph: Window
    node: reinforcedUraniumWindow
  - type: Appearance
  - type: DamageVisuals
    thresholds: [4, 8, 12]
    damageDivisor: 6
    trackAllDamage: true
    damageOverlay:
      sprite: Structures/Windows/cracks.rsi
  - type: StaticPrice
    price: 140
  - type: RadiationBlocker
    resistance: 10

- type: entity
  id: UraniumReinforcedWindowDirectional
  parent: WindowDirectional
  name: "спрямоване армоване уранове вікно"
  description: "Не заляпайте скло внизу."
  placement:
    mode: SnapgridCenter
    snap:
    - Window
  components:
  - type: Sprite
    sprite: Structures/Windows/directional.rsi
    state: uranium_reinforced_window
  - type: Icon
    sprite: Structures/Windows/directional.rsi
    state: uranium_reinforced_window
  - type: Construction
    graph: WindowDirectional
    node: uraniumReinforcedWindowDirectional
  - type: Appearance
  - type: DamageVisuals
    thresholds: [4, 8, 12]
    damageDivisor: 3.333
    trackAllDamage: true
    damageOverlay:
      sprite: Structures/Windows/cracks_directional.rsi
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlassUranium:
            min: 1
            max: 2
          PartRodMetal1:
            min: 1
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: StaticPrice
    price: 70

- type: entity
  parent: ReinforcedUraniumWindow
  id: ReinforcedUraniumWindowDiagonal
  suffix: diagonal
  placement:
    mode: SnapgridCenter
    snap:
    - Window
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Windows/reinforced_uranium_diagonal.rsi
    state: state0
  - type: IconSmooth
    mode: Diagonal
    key: walls
    base: state
  - type: Icon
    sprite: Structures/Windows/reinforced_uranium_diagonal.rsi
    state: state0
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PolygonShape
            vertices:
            - "-0.5,-0.5"
            - "0.5,0.5"
            - "0.5,-0.5"
        mask:
        - FullTileMask
        layer:
        - GlassLayer
  - type: Airtight
    noAirWhenFullyAirBlocked: false
    airBlockedDirection:
    - South
    - East
  - type: DamageVisuals
    damageOverlay:
      sprite: Structures/Windows/cracks_diagonal.rsi
  - type: Construction
    graph: WindowDiagonal
    node: reinforcedUraniumWindowDiagonal
