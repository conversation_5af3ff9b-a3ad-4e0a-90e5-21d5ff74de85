- type: entity
  name: "песець"
  parent: SimpleMobBase
  id: MobArcticFox
  description: "Носить королівську шубу сніжної елегантності, що підкреслює її холодну і винахідливу натуру."
  components:
  - type: Sprite
    drawdepth: Mobs
    sprite: DeltaV/Mobs/Pets/arcticfox.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: arcticfox
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 50 #They actually are pretty light, I looked it up
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: Appearance
  - type: Inventory
    speciesId: arcticfox
    templateId: pet
  - type: InventorySlots
  - type: Strippable
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
  - type: DamageStateVisuals
    states:
      Alive:
        Base: arcticfox
      Critical:
        Base: arcticfox_crit
      Dead:
        Base: arcticfox_dead
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 3
  - type: InteractionPopup
    successChance: 0.5
    interactSuccessString: petting-success-soft-floofy
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/Animals/fox_squeak.ogg
  - type: Temperature
    coldDamageThreshold: 210
    coldDamage:
      types:
        Cold: 0.1
  - type: Grammar
    attributes:
      gender: epicene
  - type: Bloodstream
    bloodMaxVolume: 100
  - type: MobPrice
    price: 400
  - type: Tag
    tags:
    - VimPilot
  - type: ScentTracker

- type: entity
  name: "охоронний собака"
  parent: MobCorgi # There isn't a base dog mob, surprisingly.
  id: MobSecDog
  description: "Як не дивно, ця свиня насправді собака."
  components:
  - type: GhostRole
    makeSentient: true
    allowSpeech: true
    allowMovement: true
    name: "sentient security dog"
    description: "Help out the security team by biting the baddies and barking at everything that moves."
  - type: GhostTakeoverAvailable
  - type: CombatMode
  - type: ScentTracker
  - type: MovementSpeedModifier
    baseWalkSpeed : 3.5
    baseSprintSpeed : 5.0
  - type: NpcFactionMember
    factions:
      - PetsNT
  - type: Sprite
    drawdepth: Mobs
    sprite: DeltaV/Mobs/Pets/secdog.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base"]
      state: secdog
  - type: HTN
    rootTask:
      task: SimpleHostileCompound
  - type: Speech
    allowedEmotes: ['Bark', 'Snarl', 'Whine', 'Howl', 'Awoo']
    speechVerb: Canine
    speechSounds: Vulpkanin
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 120 # BIG DOG :3
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: MobThresholds
    thresholds: # BIG DOG. STRONG DOG
      0: Alive
      100: Critical
      200: Dead
  - type: StaminaDamageOnHit
    damage: 22
  - type: MeleeWeapon
    hidden: true
    altDisarm: false
    soundHit:
        path: /Audio/Effects/bite.ogg
    angle: 0
    attackRate: 1
    damage:
      types:
        Slash: 10
  - type: SlowOnDamage
    speedModifierThresholds:
      40: 0.70
      80: 0.30
  - type: Appearance
  - type: Inventory
    speciesId: secdog
    templateId: secdog
  - type: InventorySlots
  - type: Strippable
  - type: UserInterface
    interfaces:
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
  - type: DamageStateVisuals
    states:
      Alive:
        Base: secdog
      Critical:
        Base: secdog_crit
      Dead:
        Base: secdog_dead
  - type: Butcherable
    spawned:
    - id: FoodMeat
      amount: 2
  - type: InteractionPopup
    successChance: 0.5
    interactSuccessString: petting-success-dog
    interactFailureString: petting-failure-generic
    interactSuccessSpawn: EffectHearts
    interactSuccessSound:
      path: /Audio/DeltaV/Voice/Vulpkanin/dog_bark2.ogg
  - type: Grammar
    attributes:
      gender: epicene
  - type: Bloodstream
    bloodMaxVolume: 300 # BIG DOG. MUCH BLOOD :3
  - type: Puller
    needsHands: false
  - type: MobPrice
    price: 400
  - type: Tag
    tags:
    - DoorBumpOpener
    - VimPilot
  - type: Vocal
    sounds:
      Unsexed: MobDog
  - type: LanguageKnowledge
    speaks:
    - Dog
    understands:
    - Dog
    - TauCetiBasic
  - type: Access
    tags:
    - Security
    - Brig

