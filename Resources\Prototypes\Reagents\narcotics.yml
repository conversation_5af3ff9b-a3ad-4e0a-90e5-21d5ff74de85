- type: reagent
  id: Desoxyephedrine
  name: reagent-name-desoxyephedrine
  group: Narcotics
  desc: reagent-desc-desoxyephedrine
  physicalDesc: reagent-physical-desc-translucent
  flavor: bitter
  color: "#FAFAFA"
  boilingPoint: 212.0  # Dexosyephedrine vape when?
  meltingPoint: 170.0
  metabolisms:
    Poison:
      effects:
      - !type:HealthChange
        damage:
          types:
            Poison: 0.75
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 30
        damage:
          types:
            Poison: 2 # this is added to the base damage of the meth.
            Asphyxiation: 2
    Narcotic:
      effects:
      - !type:MovespeedModifier
        walkSpeedModifier: 1.35
        sprintSpeedModifier: 1.35
      - !type:GenericStatusEffect
        key: Stutter
        component: StutteringAccent
      - !type:Jitter
      - !type:GenericStatusEffect
        key: Stun
        time: 3
        type: Remove
      - !type:GenericStatusEffect
        key: KnockedDown
        time: 3
        type: Remove
      - !type:GenericStatusEffect
        conditions:
        - !type:ReagentThreshold
          reagent: Haloperidol
          max: 0.01
        key: Drowsiness
        time: 10
        type: Remove
      - !type:GenericStatusEffect # goob edit
        key: Adrenaline
        component: IgnoreSlowOnDamage
        time: 5
      - !type:ChemAddMoodlet
        moodPrototype: StrongStimulant
    Medicine:
      effects:
      - !type:ResetNarcolepsy
        conditions:
        - !type:ReagentThreshold
          min: 20

- type: reagent
  id: Ephedrine
  name: reagent-name-ephedrine
  group: Narcotics
  desc: reagent-desc-ephedrine
  physicalDesc: reagent-physical-desc-powdery
  flavor: bitter
  color: "#D2FFFA"
  boilingPoint: 255.0
  meltingPoint: 36.0
  metabolisms:
    Narcotic:
      effects:
      - !type:MovespeedModifier
        walkSpeedModifier: 1.25
        sprintSpeedModifier: 1.25
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 20
        damage:
          types:
            Poison: 2 # this is added to the base damage of the meth.
            Asphyxiation: 2
      - !type:Jitter
      - !type:GenericStatusEffect
        key: Stun
        time: 1
        type: Remove
      - !type:GenericStatusEffect
        key: KnockedDown
        time: 1
        type: Remove
      - !type:GenericStatusEffect
        conditions:
        - !type:ReagentThreshold
          reagent: Haloperidol
          max: 0.01
        key: Drowsiness
        time: 10
        type: Remove
      - !type:PopupMessage
        visualType: Medium
        messages: ["ephedrine-effect-tight-pain", "ephedrine-effect-heart-pounds"]
        type: Local
        probability: 0.05
      - !type:GenericStatusEffect # goob edit
        key: Adrenaline
        component: IgnoreSlowOnDamage
        time: 5
      - !type:ChemAddMoodlet
        moodPrototype: StrongStimulant
    Medicine:
      effects:
      - !type:ResetNarcolepsy
        conditions:
        - !type:ReagentThreshold
          min: 30

- type: reagent
  id: Stimulants
  name: reagent-name-stimulants
  group: Narcotics
  desc: reagent-desc-stimulants
  physicalDesc: reagent-physical-desc-energizing
  flavor: sharp
  color: "#9A040E"
  boilingPoint: 212.0
  meltingPoint: 170.0
  metabolisms:
    Narcotic:
      metabolismRate: 1.0
      effects:
      - !type:MovespeedModifier
        walkSpeedModifier: 1.3
        sprintSpeedModifier: 1.3
      - !type:HealthChange
        conditions:
          - !type:ReagentThreshold
            min: 80 #please wait 3 minutes before using another stimpack
        damage:
          types:
            Poison: 1
      - !type:AdjustReagent
        conditions:
        - !type:ReagentThreshold
          reagent: ChloralHydrate
          min: 1
        reagent: ChloralHydrate
        amount: -10
      - !type:GenericStatusEffect
        key: Stun
        time: 3
        type: Remove
      - !type:GenericStatusEffect
        key: KnockedDown
        time: 3
        type: Remove
      - !type:GenericStatusEffect
        key: StaminaModifier
        component: StaminaModifier
        time: 3
        type: Add
      - !type:GenericStatusEffect
        key: ForcedSleep
        time: 3
        type: Remove
      - !type:GenericStatusEffect
        conditions:
        - !type:ReagentThreshold
          reagent: Haloperidol
          max: 0.01
        key: Drowsiness
        time: 10
        type: Remove
      - !type:GenericStatusEffect # goob edit
        key: Adrenaline
        component: IgnoreSlowOnDamage
        time: 5
      - !type:ChemAddMoodlet
        moodPrototype: StrongStimulant
    Medicine:
      metabolismRate: 1.0
      effects:
        - !type:ResetNarcolepsy
        - !type:SatiateHunger
          factor: 1
        - !type:SatiateThirst
          factor: 1
        - !type:HealthChange
          conditions:
          - !type:TotalDamage
            min: 70
            max: 120 # you've got a chance to get out of crit
          damage: # heals at the same rate as tricordrazine, doesn't heal poison because if you OD'd I'm not giving you a safety net
            groups:
              Burn: -1
              Brute: -1

- type: reagent
  id: THC
  name: reagent-name-thc
  group: Narcotics
  desc: reagent-desc-thc
  flavor: bitter
  flavorMinimum: 0.05
  color: "#808080"
  physicalDesc: reagent-physical-desc-crystalline
  plantMetabolism:
  - !type:PlantAdjustNutrition
    amount: -5
  - !type:PlantAdjustHealth
    amount: -1
  metabolisms:
    Narcotic:
      effects:
      - !type:GenericStatusEffect
        key: SeeingRainbows
        component: SeeingRainbows
        type: Add
        time: 16
        refresh: false

- type: reagent
  id: Nicotine
  name: reagent-name-nicotine
  group: Narcotics
  desc: reagent-desc-nicotine
  flavor: bitter
  color: "#C0C0C0"
  physicalDesc: reagent-physical-desc-strong-smelling
  plantMetabolism:
  - !type:PlantAdjustHealth
    amount: -5
  metabolisms:
    Narcotic:
      effects:
      - !type:ChemAddMoodlet
        moodPrototype: NicotineBenefit

# TODO: Replace these nonstandardized effects with generic brain damage
- type: reagent
  id: Impedrezene
  name: reagent-name-impedrezene
  group: Narcotics
  desc: reagent-desc-impedrezene
  physicalDesc: reagent-physical-desc-acrid
  flavor: bitter
  color: "#215263"
  metabolisms:
    Narcotic:
      effects:
      - !type:MovespeedModifier
        walkSpeedModifier: 0.65
        sprintSpeedModifier: 0.65
      - !type:HealthChange
        damage:
          types:
            Poison: 2
      - !type:GenericStatusEffect
        key: SeeingRainbows
        component: SeeingRainbows
        type: Add
        time: 10
        refresh: false
      - !type:ChemVomit # Vomiting is a symptom of brain damage
        probability: 0.05
      - !type:Drunk # Headaches and slurring are major symptoms of brain damage, this is close enough
        boozePower: 5

- type: reagent
  id: SpaceDrugs
  name: reagent-name-space-drugs
  group: Narcotics
  desc: reagent-desc-space-drugs
  physicalDesc: reagent-physical-desc-syrupy
  flavor: bitter
  color: "#63806e"
  metabolisms:
    Narcotic:
      effects:
      - !type:GenericStatusEffect
        key: SeeingRainbows
        component: SeeingRainbows
        type: Add
        time: 5
        refresh: false
      - !type:ChemRerollPsionic #Nyano - Summary: lets the imbiber become psionic.
        conditions:
        - !type:ReagentThreshold
          reagent: SpaceDrugs
          min: 15
      - !type:ChemAddMoodlet
        moodPrototype: SpaceDrugsBenefit

- type: reagent
  id: Bananadine
  name: reagent-name-bananadine
  group: Narcotics
  desc: reagent-desc-bananadine
  physicalDesc: reagent-physical-desc-powdery
  flavor: bitter
  color: "#ffff00"
  metabolisms:
    Narcotic:
      effects:
      - !type:GenericStatusEffect
        key: SeeingRainbows
        component: SeeingRainbows
        type: Add
        time: 5
        refresh: false

# Probably replace this one with sleeping chem when putting someone in a comatose state is easier
- type: reagent
  id: Nocturine
  name: reagent-name-nocturine
  group: Narcotics
  desc: reagent-desc-nocturine
  physicalDesc: reagent-physical-desc-powdery
  color: "#128e80"
  boilingPoint: 444.0
  meltingPoint: 128.0
  metabolisms:
    Narcotic:
      effects:
      - !type:GenericStatusEffect
        conditions:
        - !type:ReagentThreshold
          reagent: Nocturine
          min: 8
        key: ForcedSleep
        component: ForcedSleeping
        refresh: false
        type: Add

- type: reagent
  id: MuteToxin
  name: reagent-name-mute-toxin
  group: Narcotics
  desc: reagent-desc-mute-toxin
  physicalDesc: reagent-physical-desc-syrupy
  color: "#000000"
  boilingPoint: 255.0
  meltingPoint: 36.0
  metabolisms:
    Narcotic:
      effects:
      - !type:GenericStatusEffect
        key: Muted
        component: Muted

- type: reagent
  id: NorepinephricAcid
  name: reagent-name-norepinephric-acid
  group: Narcotics
  desc: reagent-desc-norepinephric-acid
  physicalDesc: reagent-physical-desc-milky
  flavor: salty
  color: "#96a8b5"
  boilingPoint: 255.0
  meltingPoint: 36.0
  metabolisms:
    Narcotic:
      effects:
      - !type:PopupMessage
        type: Local
        messages:
        - generic-reagent-effect-burning-eyes
        - generic-reagent-effect-burning-eyes-a-bit
        - generic-reagent-effect-tearing-up
        - norepinephricacid-effect-eyelids
        - norepinephricacid-effect-eyes-itch
        - norepinephricacid-effect-vision-fade
        - norepinephricacid-effect-vision-fail
        probability: 0.08
      - !type:PopupMessage
        visualType: MediumCaution
        type: Local
        messages:
        - norepinephricacid-effect-eye-disconnect
        - norepinephricacid-effect-eye-pain
        - norepinephricacid-effect-darkness
        - norepinephricacid-effect-blindness
        conditions:
        - !type:ReagentThreshold
          min: 20
        probability: 0.03
        #If anyone wants to add a light dimming or grayscale effect when under 20u, be my guest
      - !type:GenericStatusEffect
        key: TemporaryBlindness
        component: TemporaryBlindness
        conditions:
        - !type:ReagentThreshold
          min: 20

- type: reagent
  id: TearGas
  name: reagent-name-tear-gas
  group: Narcotics
  desc: reagent-desc-tear-gas
  physicalDesc: reagent-physical-desc-milky
  flavor: salty
  color: "#96a8b5"
  boilingPoint: 255.0
  meltingPoint: 36.0
  metabolisms:
    Narcotic:
      effects:
      - !type:PopupMessage
        type: Local
        probability: 0.08
        messages:
        - generic-reagent-effect-burning-eyes
        - generic-reagent-effect-burning-eyes-a-bit
        - generic-reagent-effect-tearing-up
        - norepinephricacid-effect-eyelids
        - norepinephricacid-effect-eyes-itch
        - norepinephricacid-effect-vision-fade
        - norepinephricacid-effect-vision-fail
      - !type:PopupMessage
        type: Local
        visualType: MediumCaution
        probability: 0.03
        messages:
        - norepinephricacid-effect-eye-disconnect
        - norepinephricacid-effect-eye-pain
        - norepinephricacid-effect-darkness
        - norepinephricacid-effect-blindness
        conditions:
        - !type:ReagentThreshold
          min: 5
      - !type:Emote
        emote: Scream
        probability: 0.08
      - !type:GenericStatusEffect
        key: TemporaryBlindness
        component: TemporaryBlindness
        conditions:
        - !type:ReagentThreshold
          min: 5
      - !type:ChemAddMoodlet
        moodPrototype: TearGasEffect

- type: reagent
  id: Happiness
  name: reagent-name-happiness
  group: Narcotics
  desc: reagent-desc-happiness
  physicalDesc: reagent-physical-desc-soothing
  flavor: paintthinner
  color: "#EE35FF"
  metabolisms:
    Narcotic:
      effects:
      - !type:Emote
        emote: Laugh
        showInChat: true
        probability: 0.1
        conditions:
        - !type:ReagentThreshold
          max: 20
      - !type:Emote
        emote: Whistle
        showInChat: true
        probability: 0.1
        conditions:
        - !type:ReagentThreshold
          max: 20
      - !type:Emote
        emote: Crying
        showInChat: true
        probability: 0.1
        conditions:
        - !type:ReagentThreshold
          min: 20
      - !type:ChemAddMoodlet
        moodPrototype: PsicodineEffect
      - !type:GenericStatusEffect
        key: SeeingRainbows
        component: SeeingRainbows
        type: Add
        time: 5
        refresh: false

- type: reagent
  id: Cryanobalamin
  name: reagent-name-cryanobalamin
  group: Narcotics
  desc: reagent-desc-cryanobalamin
  flavor: sweet
  color: "#46a6f5"
  physicalDesc: reagent-physical-desc-sugary
  metabolisms:
    Narcotic:
      effects:
      - !type:StaminaChange
        amount: -3
      - !type:SatiateThirst
        factor: -1
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 30
        damage:
          types:
            Cellular: 0.2

- type: reagent
  id: LiftLoversSpecial
  name: reagent-name-liftloversspecial
  group: Narcotics
  desc: reagent-desc-liftloversspecial
  flavor: sweet
  color: "#f73715"
  physicalDesc: reagent-physical-desc-unhealthy
  metabolisms:
    Narcotic:
      effects:
      - !type:StaminaChange
        amount: -6
      - !type:SatiateThirst
        factor: -2
      - !type:Jitter
        conditions:
        - !type:ReagentThreshold
          min: 7
      - !type:HealthChange
        conditions:
        - !type:ReagentThreshold
          min: 20
        damage:
          types:
            Cellular: 0.3

