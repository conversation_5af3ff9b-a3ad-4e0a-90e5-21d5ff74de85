﻿- type: entity
  parent: [BaseStructure, ConstructibleMachine] # Not a BaseMachinePowered since we don't want the anchorable component
  id: CryoPod
  name: "кріо-капсула"
  description: "Спеціальна машина, призначена для створення безпечного середовища для використання хімічних речовин, які реагують у холодному середовищі."
  components:
  - type: Sprite
    sprite: Structures/Machines/cryogenics.rsi
    drawdepth: Mobs
    noRot: true
    offset: 0, 0.5
    layers:
      - sprite: Structures/Piping/Atmospherics/pipe.rsi
        state: pipeHalf
        offset: 0, -0.5
        map: [ "enum.PipeVisualLayers.Pipe" ]
      - state: pod-open
        map: [ "enum.CryoPodVisualLayers.Base" ]
      - state: cover-on
        map: [ "enum.CryoPodVisualLayers.Cover" ]
        visible: false
      - state: pod-panel
        map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
        visible: false
  - type: InteractionOutline
  - type: Transform
    noRot: true
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.5,-0.5,0.5,0.90"
        density: 200
        mask:
          - MachineMask
        layer:
          - MachineLayer
  - type: ContainerContainer
    containers:
      scanner-body:
        !type:ContainerSlot
        showEnts: true
      beakerSlot: !type:ContainerSlot {}
      machine_board: !type:Container
      machine_parts: !type:Container
  - type: AtmosDevice
  - type: Appearance
  - type: Machine
    board: CryoPodMachineCircuitboard
  - type: WiresVisuals
  - type: WiresPanel
  - type: Wires
    boardName: wires-board-name-cryopod
    layoutId: CryoPod
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
        - !type:EmptyAllContainersBehaviour
        - !type:PlaySoundBehavior
          sound:
            collection: WindowShatter
        - !type:ChangeConstructionNodeBehavior
          node: machineFrame
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
  - type: ApcPowerReceiver
    powerLoad: 3000
  - type: ExtensionCableReceiver
  - type: NodeContainer
    nodes:
      port:
        !type:PortablePipeNode
        nodeGroupID: Pipe
        pipeDirection: South
  - type: AtmosMonitoringConsoleDevice
    navMapBlip: Thermoregulator
  - type: ItemSlots
    slots:
      beakerSlot:
        whitelist:
          components:
            - FitsInDispenser
  - type: ItemToggle
  - type: HealthAnalyzer
    scanDelay: 0
  - type: UserInterface
    interfaces:
      enum.HealthAnalyzerUiKey.Key:
        type: HealthAnalyzerBoundUserInterface
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: ActivatableUI
    key: enum.HealthAnalyzerUiKey.Key
    requiresComplex: false
  - type: ActivatableUIRequiresPower
  - type: PointLight
    color: "#3a807f"
    radius: 2
    energy: 10
    enabled: false
  - type: EmptyOnMachineDeconstruct
    containers:
      - scanner-body
  - type: CryoPod
  - type: CryoPodAir
  - type: ContainerTemperatureDamageThresholds
    coldDamageThreshold: 10
  - type: GuideHelp
    guides:
    - Cryogenics
