- type: entity
  parent: MobHuman
  id: MobDebugCounter
  name: "лічильник налагодження"
  description: "Він вміє рахувати"
  suffix: AI, DEBUG
  components:
    - type: HTN
      rootTask:
        task: DebugCounterCompound
      blackboard:
        MinimumIdleTime: !type:Single
          0.5
        MaximumIdleTime: !type:Single
          0.5
        Count: !type:Single
          0

- type: entity
  parent: MobHuman
  id: MobDebugRandomCounter
  name: "налагодити лічильник випадкових чисел"
  description: "Він може рандомізувати"
  suffix: AI, DEBUG
  components:
    - type: HTN
      rootTask:
        task: DebugRandomCounterCompound
      blackboard:
        MinimumIdleTime: !type:Single
          1
        MaximumIdleTime: !type:Single
          1
        Count: !type:Single
          0

- type: entity
  parent: MobHuman
  id: MobDebugRandomLess
  name: "debug random less"
  description: "Він вміє співати"
  suffix: AI, DEBUG
  components:
    - type: HTN
      rootTask:
        task: DebugRandomLessCompound
      blackboard:
        MinimumIdleTime: !type:Single
          1
        MaximumIdleTime: !type:Single
          1
        Count: !type:Single
          0
