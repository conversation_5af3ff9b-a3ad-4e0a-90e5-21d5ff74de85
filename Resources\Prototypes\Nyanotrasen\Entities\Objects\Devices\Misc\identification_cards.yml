- type: entity
  parent: IDCardStandard
  id: PrisonerIDCard
  name: "посвідчення особи в'язня"
  components:
  - type: Sprite
    layers:
    - state: prisoner
    - state: department
      color: "#292929"
    - state: subdepartment
      color: "#A54900"
    - state: warden
  - type: PresetIdCard
    job: Prisoner

- type: entity
  parent: IDCardStandard
  id: GladiatorIDCard
  name: "посвідчення гладіатора"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#878787"
    - state: subdepartment
      color: "#878787"
    - state: gladiator
  - type: PresetIdCard
    job: Gladiator

- type: entity
  parent: IDCardStandard
  id: PrisonGuardIDCard
  name: "посвідчення охоронця в'язниці"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#CB0000"
    - state: subdepartment
      color: "#CB0000"
    - state: prisonguard
  - type: PresetIdCard
    job: PrisonGuard

- type: entity
  parent: IDCardStandard
  id: MailCarrierIDCard
  name: "посвідчення кур'єра" # DeltaV - Mail Carrier to Courier replacement
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#B18644"
    - state: subdepartment
      color: "#B18644"
    - state: mailcarrier
  - type: PresetIdCard
    job: MailCarrier


- type: entity
  parent: IDCardStandard
  id: MartialArtistIDCard
  name: "посвідчення майстра бойових мистецтв"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#878787"
    - state: subdepartment
      color: "#878787"
    - state: martialartist
  - type: PresetIdCard
    job: MartialArtist

- type: entity
  parent: IDCardStandard
  id: ForensicMantisIDCard
  name: "ідентифікаційна картка мантіса"
  components:
  - type: Sprite
    layers:
    - state: default
    - state: department
      color: "#C96DBF"
    - state: subdepartment
      color: "#C96DBF"
    - state: detective
  - type: PresetIdCard
    job: ForensicMantis
