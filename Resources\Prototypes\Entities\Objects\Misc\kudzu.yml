- type: entity
  id: <PERSON>Kudzu
  abstract: true
  placement:
    mode: SnapgridCenter
    snap:
      - Wall
  components:
    - type: Appearance
    - type: Transform
      anchored: true
    - type: Physics
    - type: Kudzu
    - type: GrowingKudzu
    - type: ActiveEdgeSpreader
    - type: EdgeSpreader
      id: Kudzu

- type: entity
  id: Kudzu
  name: "кудзу"
  parent: BaseKudzu
  description: "Швидко зростаюча, небезпечна рослина. ЧОМУ ВИ ЗУПИНИЛИСЯ, ЩОБ ПОДИВИТИСЯ НА НЕЇ?!"
  components:
    - type: MeleeSound
      soundGroups:
        Brute:
          path: "/Audio/Weapons/slash.ogg"
    - type: Sprite
      sprite: Objects/Misc/kudzu.rsi
      state: kudzu_11
      drawdepth: Overdoors
    - type: KudzuVisuals
    - type: Clickable
    - type: Fixtures
      fixtures:
        fix1:
          hard: false
          density: 7
          shape: !type:PhysShapeAabb
            bounds: "-0.5,-0.5,0.5,0.5"
          layer:
            - MidImpassable
    - type: Damageable
      damageModifierSet: Wood
    - type: Destructible
      thresholds:
        - trigger: !type:DamageTrigger
            damage: 10
          behaviors:
            - !type:DoA<PERSON><PERSON>ehavior
              acts: ["Destruction"]
    - type: Temperature
      heatDamage:
        types:
          Heat: 5
      coldDamage: {}
      coldDamageThreshold: 0
    - type: Flammable
      fireSpread: true #If you walk into incredibly dense, flaming vines, you can expect to burn.
      damage:
        types:
          Heat: 3
    - type: Reactive
      groups:
        Flammable: [Touch]
        Extinguish: [Touch]
      reactions:
        - reagents: [WeedKiller, PlantBGone]
          methods: [Touch]
          effects:
            - !type:HealthChange
              scaleByQuantity: true
              damage:
                types:
                  Heat: 10
    - type: AtmosExposed
    - type: Kudzu
      growthTickChance: 0.3
      spreadChance: 0.4
    - type: SpeedModifierContacts
      walkSpeedModifier: 0.2
      sprintSpeedModifier: 0.2
      ignoreWhitelist:
        components:
          - IgnoreKudzu
    - type: Food
      requiredStomachs: 2 # ruminants have 4 stomachs but i dont care to give them literally 4 stomachs. 2 is good
      delay: 0.5
    - type: FlavorProfile
      flavors:
        - fiber
    - type: SolutionContainerManager
      solutions:
        food:
          reagents:
            - ReagentId: Nutriment
              Quantity: 2

- type: entity
  id: WeakKudzu
  parent: Kudzu
  suffix: Weak
  components:
    - type: Kudzu
      spreadChance: 0.3

- type: entity
  id: KudzuFlowerFriendly
  name: "квітковий килим"
  suffix: Friendly, Floral Anomaly
  description: "Барвистий килим з квітів, що розкинувся на всі боки. Ви не знаєте, зняти його чи залишити."
  parent: Kudzu
  components:
    - type: Sprite
      drawdepth: HighFloorObjects
      sprite: Objects/Misc/kudzuflower.rsi
      state: kudzu_11
    - type: Kudzu
      spriteVariants: 5
      spreadChance: 0.01
    - type: SpeedModifierContacts
      walkSpeedModifier: 0.8
      sprintSpeedModifier: 0.8
      ignoreWhitelist:
        components:
          - IgnoreKudzu
    - type: RandomSpawner
      deleteSpawnerAfterSpawn: false
      rareChance: 0.15
      offset: 0.2
      chance: 0.05
      prototypes:
        - LightTree01
        - LightTree02
        - LightTree03
        - LightTree04
        - LightTree05
        - LightTree06
        - CrystalCyan
      rarePrototypes:
        - AnomalyFloraBulb

- type: entity
  id: KudzuFlowerAngry
  suffix: Angry, Floral Anomaly
  parent: KudzuFlowerFriendly
  components:
    - type: Kudzu
      spreadChance: 0.2
    - type: RandomSpawner
      chance: 0.05
      rarePrototypes:
        - AnomalyFloraBulb
        - AnomalyFloraBulb
        - MobLuminousEntity
        - MobLuminousObject
        - MobLuminousPerson

- type: entity
  id: FleshKudzu
  name: "сухожилля"
  parent: BaseKudzu
  description: "Швидко зростаюче скупчення м'ясистих сухожиль. ЧОМУ ВИ ЗУПИНИЛИСЯ, ЩОБ ПОДИВИТИСЯ НА ЦЕ?!"
  placement:
    mode: SnapgridCenter
    snap:
      - Wall
  components:
    - type: MeleeSound
      soundGroups:
        Brute:
          path: "/Audio/Weapons/slash.ogg"
    - type: Sprite
      sprite: Objects/Misc/fleshkudzu.rsi
      state: kudzu_11
      drawdepth: Overdoors
    - type: KudzuVisuals
    - type: Clickable
    - type: Fixtures
      fixtures:
        fix1:
          hard: false
          density: 7
          shape: !type:PhysShapeAabb
            bounds: "-0.5,-0.5,0.5,0.5"
          layer:
            - MidImpassable
    - type: Damageable
    - type: Destructible
      thresholds:
        - trigger: !type:DamageTrigger
            damage: 40
          behaviors:
            - !type:DoActsBehavior
              acts: ["Destruction"]
    - type: DamageContacts
      damage:
        types:
          Slash: 1.5
          Piercing: 1.5
      ignoreWhitelist:
        tags:
          - Flesh
    - type: Kudzu
      growthTickChance: 0.1
      spreadChance: 0.4
      # Heals each time it manages to do a growth tick:
      damageRecovery:
        types:
          Slash: -0.5
          Heat: -1.0
          Cold: -1.0
          Blunt: -0.5 # Needs to be balanced (approx 3x) with vacuum damage to stall but not kill Kudzu
    - type: Temperature
      heatDamage:
        types:
          Heat: 10
      coldDamage:
        types:
          Cold: 5 #per second, scales with temperature & other constants
    - type: Barotrauma
      damage:
        types:
          Blunt: 0.10 #per second, scales with pressure and other constants.
    - type: Flammable
      fireSpread: true
      damage:
        types:
          Heat: 3
    - type: AtmosExposed
    - type: SpeedModifierContacts
      walkSpeedModifier: 0.3
      sprintSpeedModifier: 0.3
      ignoreWhitelist:
        tags:
          - Flesh
    - type: Food # delightfully devilish !
      delay: 0.5
    - type: SolutionContainerManager
      solutions:
        food:
          reagents:
            - ReagentId: Protein
              Quantity: 2
    - type: Respirator
      damage:
        types:
          Asphyxiation: 0.25
      damageRecovery:
        types:
          Asphyxiation: -0.25

- type: entity
  name: "темний туман"
  id: ShadowKudzu
  parent: [BaseKudzu, BaseShadow]
  components:
    - type: Physics
      canCollide: false
    - type: Occluder
    - type: Sprite
      drawdepth: Effects
      sprite: Effects/spookysmoke.rsi
      layers:
        - state: spookysmoke
          color: "#793a80dd"
          map: [base]
    - type: Kudzu
      growthTickChance: 0.2
      spreadChance: 0.99
    - type: RandomSpawner
      deleteSpawnerAfterSpawn: false
      rareChance: 0.05
      offset: 0.2
      chance: 0.45
      prototypes:
        - ShadowBasaltRandom
      rarePrototypes:
        - ShadowPortal
        - ShadowKudzuLootSpawner
    - type: Tag
      tags:
        - HideContextMenu
        - SpookyFog
    - type: OptionsVisualizer
      visuals:
        base:
          - options: Default
            data: { state: spookysmoke }
          - options: ReducedMotion
            data: { state: spookysmoke_static }

- type: entity
  name: "Хейз"
  id: ShadowKudzuWeak
  parent: ShadowKudzu
  components:
    - type: Kudzu
      spreadChance: 0 #appears during pulsation. It shouldnt spreading.

- type: entity
  name: "Shadowkin Haze"
  id: ShadowkinShadow
  parent: ShadowKudzuWeak
  components:
    - type: RandomSpawner
      deleteSpawnerAfterSpawn: false
      rareChance: 0
      offset: 0.2
      chance: 0.45
      prototypes:
        - ShadowBasaltRandom
    - type: TimedDespawn
      lifetime: 5

- type: entity
  name: "згасаючий серпанок"
  id: ShadowKudzuTemp
  parent: ShadowKudzuWeak
  components:
    - type: RandomSpawner
      deleteSpawnerAfterSpawn: false
      rareChance: 0.2
      offset: 0.2
      chance: 0.45
      prototypes:
        - ShadowBasaltRandom
      rarePrototypes:
        - ShadowKudzuLootSpawnerTemp
    - type: TimedDespawn
      lifetime: 12
