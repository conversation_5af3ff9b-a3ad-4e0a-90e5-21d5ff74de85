- type: entity
  id: NoticeBoard2
  name: "дошка оголошень"
  description: "Щось важливе для публікації?"
  placement:
    mode: SnapgridCenter
  components:
  - type: WallMount
  - type: Sprite
    sprite: Structures/Wallmounts/noticeboard.rsi
    layers:
      - state: noticeboard
      - state: notice-0
      - map: ["enum.StorageFillLayers.Fill"]
  - type: StorageFillVisualizer
    maxFillLevels: 6
    fillBaseName: notice
  - type: Appearance
  - type: InteractionOutline
  - type: Clickable
  - type: Transform
    anchored: true
  - type: Damageable
    damageModifierSet: Wood
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 30
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank:
            min: 1
            max: 2
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Storage
    grid:
    - 0,0,4,3
    maxItemSize: Small
    whitelist:
      tags:
        - Folder
        - Document
        - Write
  - type: UserInterface
    interfaces:
      enum.StorageUiKey.Key:
        type: StorageBoundUserInterface
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
  - type: Tag
    tags:
    - Wooden
  - type: Construction
    graph: NoticeBoard
    node: noticeBoard
