- type: entity
  id: MailTeleporter
  parent: [ BaseMachinePowered, ConstructibleMachine ]
  name: "поштовий телепорт"
  description: "Телепортує пошту, адресовану екіпажу цієї станції."
  components:
  - type: MailTeleporter
  - type: InteractionOutline
  - type: Physics
    bodyType: Static
  - type: Transform
    anchored: true
    noRot: true
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
            bounds: "-0.45,-0.45,0.45,0.00"
        density: 120
        mask:
        - HighImpassable
  - type: Sprite
    sprite: Nyanotrasen/Structures/mailbox.rsi
    scale: 0.5, 0.5
    layers:
    - state: icon
    - state: unlit
      shader: unshaded
      map: ["enum.PowerDeviceVisualLayers.Powered"]
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 75
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
      - !type:ChangeConstructionNodeBehavior
        node: machineFrame
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: ApcPowerReceiver
    powerLoad: 1000 # TODO if we keep this make it spike power draw when teleporting
    powerDisabled: false
  - type: ExtensionCableReceiver
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.PowerDeviceVisuals.Powered:
        enum.PowerDeviceVisualLayers.Powered:
          True: {visible: true}
          False: {visible: false}
  - type: PowerSwitch
  - type: Machine
    board: MailTeleporterMachineCircuitboard
