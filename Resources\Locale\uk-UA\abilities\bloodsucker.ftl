action-name-suck-blood = Смоктати кров
action-description-suck-blood = Висмокчіть кров жертви в руку.

bloodsucker-fail-helmet = Вам потрібно видалити {THE($helmet)}.
bloodsucker-fail-mask = Вам потрібно буде зняти маску!

bloodsucker-fail-not-blood = { CAPITALIZE(SUBJECT($target)) } не має смачної, поживної смертної крові.
bloodsucker-fail-no-blood = { CAPITALIZE(SUBJECT($target)) } не має крові в { POSS-ADJ($target) } тілі.
bloodsucker-fail-no-blood-bloodsucked = { CAPITALIZE(SUBJECT($target)) } висмоктали досуха.

bloodsucker-blood-sucked = Ви висмоктуєте трохи крові з {$target}.
bloodsucker-doafter-start = Ви намагаєтесь висмоктати кров з {$target}.

bloodsucker-doafter-start-victim = {CAPITALIZE(THE($sucker))} намагається вкусити тебе за шию!
bloodsucker-blood-sucked-victim = {CAPITALIZE(THE($sucker))} висмокче трохи вашої крові!

bloodsucked-health-examine = [color=red]{ CAPITALIZE(SUBJECT($target)) } { CONJUGATE-HAVE($target) } сліди укусів на { POSS-ADJ($target) } шиї.[/color]

bloodsucker-glands-throb = Залози за іклами трохи болять.

bloodsucker-not-blood = {$target} не має смачної, поживної крові.