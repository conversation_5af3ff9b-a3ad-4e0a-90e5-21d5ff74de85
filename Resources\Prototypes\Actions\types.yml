# base actions

- type: entity
  id: BaseSuicideAction
  abstract: true
  components:
  - type: ConfirmableAction
    popup: suicide-action-popup

# actions

- type: entity
  id: ActionScream
  name: "Кричи!"
  description: "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    useDelay: 10
    icon: Interface/Actions/scream.png
    event: !type:ScreamActionEvent
    checkCanInteract: false

- type: entity
  id: ActionTurnUndead
  name: "Перетворитися на нежить"
  description: "Піддайтеся своїй інфекції і станьте зомбі."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    checkCanInteract: false
    checkConsciousness: false
    icon: Interface/Actions/zombie-turn.png
    event: !type:ZombifySelfActionEvent

- type: entity
  id: ActionToggleLight
  name: "Індикатор перемикання"
  description: "Увімкніть і вимкніть світло."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: { sprite: Objects/Tools/flashlight.rsi, state: flashlight }
    iconOn: { sprite: Objects/Tools/flashlight.rsi, state: flashlight-on }
    event: !type:ToggleActionEvent

- type: entity
  id: ActionToggleDome
  name: "Перемикання енергетичного купола"
  description: "Увімкніть або вимкніть енергетичний бар'єр."
  components:
  - type: InstantAction
    icon: { sprite: Objects/Weapons/Melee/e_shield.rsi, state: eshield-on }
    event: !type:ToggleActionEvent

- type: entity
  id: ActionOpenStorageImplant
  name: "Імплантат відкритого зберігання"
  description: "Відкриває імплант для зберігання, вмонтований під шкіру"
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    itemIconStyle: BigAction
    priority: -20
    icon:
      sprite: Clothing/Back/Backpacks/backpack.rsi
      state: icon
    event: !type:OpenStorageImplantEvent

- type: entity
  parent: BaseSuicideAction
  id: ActionActivateMicroBomb
  name: "Активувати Мікробому"
  description: "Активує вашу внутрішню мікробому, повністю знищуючи вас і ваше обладнання"
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    checkCanInteract: false
    checkConsciousness: false
    itemIconStyle: BigAction
    priority: -20
    icon:
      sprite: Actions/Implants/implants.rsi
      state: explosive
    event: !type:ActivateImplantEvent

- type: entity
  parent: BaseSuicideAction
  id: ActionActivateDeathAcidifier
  name: "Активувати підсилювач смерті"
  description: "Активує ваш підкислювач смерті, повністю розплавляючи вас і ваше спорядження"
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    checkCanInteract: false
    checkConsciousness: false
    itemIconStyle: BigAction
    priority: -20
    icon:
      sprite: Objects/Magic/magicactions.rsi
      state: gib
    event: !type:ActivateImplantEvent

- type: entity
  id: ActionActivateFreedomImplant
  name: "Вирвися на волю"
  description: "Активація імплантату свободи звільнить вас від будь-яких обмежень для рук"
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    charges: 3
    checkCanInteract: false
    itemIconStyle: BigAction
    priority: -20
    icon:
      sprite: Actions/Implants/implants.rsi
      state: freedom
    event: !type:UseFreedomImplantEvent

- type: entity
  id: ActionOpenUplinkImplant
  name: "Open Uplink"
  description: "Відкриває синдикатський канал зв'язку, вбудований у вашу шкіру"
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    itemIconStyle: BigAction
    priority: -20
    icon:
      sprite: Objects/Devices/communication.rsi
      state: radio
    event: !type:OpenUplinkImplantEvent

- type: entity
  id: ActionActivateEmpImplant
  name: "Активувати EMP"
  description: "Запускає невеликий ЕМІ-імпульс навколо вас"
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    checkCanInteract: false
    charges: 3
    useDelay: 5
    itemIconStyle: BigAction
    priority: -20
    icon:
      sprite: Objects/Weapons/Grenades/empgrenade.rsi
      state: icon
    event: !type:ActivateImplantEvent

- type: entity
  id: ActionActivateScramImplant
  name: "КРИК!"
  description: "Випадково телепортує вас на велику відстань."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    checkCanInteract: false
    charges: 2
    useDelay: 5
    itemIconStyle: BigAction
    priority: -20
    icon:
      sprite: Structures/Specific/anomaly.rsi
      state: anom4
    event: !type:UseScramImplantEvent

- type: entity
  id: ActionActivateDnaScramblerImplant
  name: "Скремблювання ДНК"
  description: "Випадковим чином змінює ваше ім'я та зовнішність."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    charges: 1
    itemIconStyle: BigAction
    priority: -20
    icon:
      sprite: Clothing/OuterClothing/Hardsuits/lingspacesuit.rsi
      state: icon
    event: !type:UseDnaScramblerImplantEvent

- type: entity
  id: ActionMorphGeras
  name: "Перетворитися на Гераса"
  description: "Перетворює вас на Гераса - мініатюрну версію вас, яка дозволяє швидко пересуватися за рахунок вашого інвентарю."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    itemIconStyle: BigAction
    useDelay: 10 # prevent spam
    priority: -20
    icon:
      sprite: Mobs/Aliens/slimes.rsi
      state: blue_adult_slime
    event: !type:MorphIntoGeras

- type: entity
  id: ActionToggleSuitPiece
  name: "Перемикач костюмів"
  description: "Не забудьте спорядити важливі елементи свого костюма перед тим, як вирушати в бій."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    itemIconStyle: BigItem
    useDelay: 1 # equip noise spam.
    event: !type:ToggleClothingEvent

- type: entity
  id: ActionCombatModeToggle
  name: "[color=red]Бойовий режим[/color]"
  description: "Увійдіть в бойовий режим"
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    checkCanInteract: false
    checkConsciousness: false
    icon: Interface/Actions/harmOff.png
    iconOn: Interface/Actions/harm.png
    event: !type:ToggleCombatActionEvent
    priority: -100

- type: entity
  id: ActionCombatModeToggleOff
  parent: ActionCombatModeToggle
  name: "[color=red]Бойовий режим[/color]"
  description: "Увійдіть в бойовий режим"
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    enabled: false
    autoPopulate: false
    priority: -100

- type: entity
  id: ActionChangeVoiceMask
  name: "Назва набору"
  description: "Змініть ім'я, яке чують інші, на інше."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: { sprite: Interface/Actions/voice-mask.rsi, state: icon }
    event: !type:VoiceMaskSetNameEvent

- type: entity
  id: ActionVendingThrow
  name: "Пункт видачі"
  description: "Випадково роздайте предмет зі свого запасу."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    useDelay: 30
    event: !type:VendingMachineSelfDispenseEvent

- type: entity
  id: ActionArtifactActivate
  name: "Активувати артефакт"
  description: "Негайно активує ваш поточний вузол артефактів."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon:
      sprite: Objects/Specific/Xenoarchaeology/xeno_artifacts.rsi
      state: ano01
    useDelay: 60
    event: !type:ArtifactSelfActivateEvent

- type: entity
  id: ActionToggleBlock
  name: "Блок"
  description: "Підніміть або опустіть щит."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: { sprite: Objects/Weapons/Melee/shields.rsi, state: teleriot-icon }
    iconOn: Objects/Weapons/Melee/shields.rsi/teleriot-on.png
    event: !type:ToggleActionEvent

- type: entity
  id: ActionClearNetworkLinkOverlays
  name: "Очистити накладання мережевих з'єднань"
  description: "Чіткі накладання мережевих посилань."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    clientExclusive: true
    checkCanInteract: false
    checkConsciousness: false
    temporary: true
    icon: { sprite: Objects/Tools/multitool.rsi, state: icon }
    event: !type:ClearAllOverlaysEvent

- type: entity
  id: ActionAnimalLayEgg
  name: "Несіть яйце"
  description: "Використовує голод, щоб відкласти яйце."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: { sprite: Objects/Consumable/Food/egg.rsi, state: icon }
    useDelay: 60
    event: !type:EggLayInstantActionEvent

- type: entity
  id: ActionSleep
  name: "Спати"
  description: "Лягай спати."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    checkCanInteract: false
    checkConsciousness: false
    icon: { sprite: Clothing/Head/Hats/pyjamasyndicatered.rsi, state: icon }
    event: !type:SleepActionEvent

- type: entity
  id: ShadowkinActionSleep
  name: action-name-shadowkin-rest
  description: action-description-shadowkin-rest
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    checkCanInteract: false
    checkConsciousness: false
    icon: { sprite: Interface/Actions/shadowkin_icons.rsi, state: rest }
    event: !type:SleepActionEvent

- type: entity
  id: ActionWake
  name: "Прокидайся"
  description: "Досить спати."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: { sprite: Clothing/Head/Hats/pyjamasyndicatered.rsi, state: icon }
    checkCanInteract: false
    checkConsciousness: false
    event: !type:WakeActionEvent
    startDelay: true
    useDelay: 2

- type: entity
  id: ActionActivateHonkImplant
  name: "Гудок"
  description: "Активує ваш імплант, який видаватиме фірмовий звук клоуна."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: { sprite: Objects/Fun/bikehorn.rsi, state: icon }
    event: !type:ActivateImplantEvent
    useDelay: 1

- type: entity
  id: ActionFireStarter
  name: "Підпалюй"
  description: "Підпалює ворогів у радіусі навколо вас."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    priority: -1
    useDelay: 30
    icon: Interface/Actions/firestarter.png
    event: !type:FireStarterActionEvent

- type: entity
  id: ActionToggleEyes
  name: "Відкрити/закрити очі"
  description: "Заплющте очі, щоб захистити очі, або розплющте, щоб насолодитися гарними вогнями."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: Interface/Actions/eyeopen.png
    iconOn: Interface/Actions/eyeclose.png
    event: !type:ToggleEyesActionEvent
    useDelay: 1 # so u cant give yourself and observers eyestrain by rapidly spamming the action
    checkCanInteract: false
    checkConsciousness: false

- type: entity
  id: ActionToggleWagging
  name: action-name-toggle-wagging
  description: action-description-toggle-wagging
  categories: [ HideSpawnMenu ]
  components:
    - type: InstantAction
      icon: { sprite: Interface/Actions/wagging.rsi, state: icon }
      iconOn: { sprite: Interface/Actions/wagging.rsi, state: icon-on }
      itemIconStyle: NoItem
      useDelay: 1 # emote spam
      event: !type:ToggleActionEvent

- type: entity
  id: ActionFabricateLollipop
  name: action-name-fabricate-lollipop
  description: action-description-fabricate-lollipop
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: { sprite: Nyanotrasen/Objects/Consumable/Food/candy.rsi, state: lollipop }
    useDelay: 120
    event:
      !type:FabricateActionEvent
      fabrication: FoodLollipop

- type: entity
  id: ActionFabricateGumball
  name: action-name-fabricate-gumball
  description: action-description-fabricate-gumball
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: { sprite: Nyanotrasen/Objects/Consumable/Food/candy.rsi, state: gumball }
    useDelay: 40
    event:
      !type:FabricateActionEvent
      fabrication: FoodGumball

- type: entity
  id: ToggleNightVision
  name: "Увімкнути нічне бачення"
  description: "Вмикає нічне бачення."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    itemIconStyle: BigAction
    priority: -20
    icon:
      sprite: Clothing/Eyes/Goggles/nightvision.rsi
      state: icon
    event: !type:ToggleNightVisionEvent

- type: entity
  id: ToggleThermalVision
  name: "Перемикач Тепловізійне бачення"
  description: "Перемикає тепловізійне бачення."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    itemIconStyle: BigAction
    priority: -20
    icon:
      sprite: Clothing/Eyes/Goggles/thermal.rsi
      state: icon
    event: !type:ToggleThermalVisionEvent

- type: entity
  id: PulseThermalVision
  parent: ToggleThermalVision
  name: "Імпульсне тепловізійне бачення"
  description: "Тимчасово увімкнути тепловий зір."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    useDelay: 4


- type: entity
  id: ActionSelfExtinguish
  name: "Самозатухання"
  description: "Гасить вас у разі пожежі та ізолює від самозаймання."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    itemIconStyle: BigItem
    icon:
      sprite: Objects/Misc/fire_extinguisher.rsi
      state: fire_extinguisher_open
    iconCooldown:
      sprite: Objects/Misc/fire_extinguisher.rsi
      state: fire_extinguisher_closed
    iconDisabled: Interface/Default/blocked.png
    event: !type:SelfExtinguishEvent
#PIRATES
- type: entity
  id: OpenEmotes
  name: action-name-emotes
  description: action-description-emotes
  components:
  - type: InstantAction
    useDelay: 2
    icon: Interface/Actions/emotes.png
    event: !type:OpenEmotesActionEvent
    checkCanInteract: false
