- type: entity
  parent: DrinkGlass
  id: DrinkHealthViolationGlass
  suffix: health code violation
  description: "Нібито це коктейль. Мотив попереджувального конуса здається доречним."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: HealthViolation
          Quantity: 30
  - type: Icon
    sprite: DeltaV/Objects/Consumable/Drinks/healthcodeviolation.rsi
    state: icon

- type: entity
  parent: DrinkGlass
  id: DrinkGunmetalGlass
  suffix: gunmetal
  description: "Каламутна суміш рому, вершків і... зварювального пального? Мабуть, смачно."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: Gunmetal
          Quantity: 30
  - type: Icon
    sprite: DeltaV/Objects/Consumable/Drinks/gunmetal.rsi
    state: icon

- type: entity
  parent: DrinkGlass
  id: DrinkLemonDropGlass
  suffix: lemon drop
  description: "Келих для мартіні, наповнений напівпрозорою сумішшю освіжаючого лимонного смаку."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: LemonDrop
          Quantity: 30
  - type: Icon
    sprite: DeltaV/Objects/Consumable/Drinks/lemondrop.rsi
    state: icon

- type: entity
  parent: DrinkGlass
  id: DrinkGreenGrassGlass
  suffix: green grass
  description: "Дивний зелений коктейль, увінчаний апельсинами, льодом і пластиковою соломинкою. Цікаво."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: GreenGrass
          Quantity: 30
  - type: Icon
    sprite: DeltaV/Objects/Consumable/Drinks/greengrass.rsi
    state: icon

- type: entity
  parent: DrinkGlass
  id: DrinkDaiquiriGlass
  suffix: classic daiquiri
  description: "Ром, лайм і простий сироп. Такий клас, така вишуканість."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: Daiquiri
          Quantity: 30
  - type: Icon
    sprite: DeltaV/Objects/Consumable/Drinks/daiquiri.rsi
    state: icon

- type: entity
  parent: DrinkGlass
  id: DrinkArsonistsBrewGlass
  suffix: arsonist's brew
  description: "Напевно, це не стосується того, що він ледь-ледь світиться. Чи булькає. Чи димить. Ні, зовсім ні."
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: ArsonistsBrew
          Quantity: 30
  - type: Icon
    sprite: DeltaV/Objects/Consumable/Drinks/arsonist.rsi
    state: icon

- type: entity
  parent: DrinkGlass
  id: DrinkKvassGlass
  suffix: kvass
  description: "Прохолодний освіжаючий напій зі смаком соціалізму."
  components:
    - type: SolutionContainerManager
      solutions:
        drink:
          maxVol: 30
          reagents:
            - ReagentId: Kvass
              Quantity: 30
    - type: Icon
      sprite: DeltaV/Objects/Consumable/Drinks/kvass.rsi
      state: icon

- type: entity
  parent: DrinkGlass
  id: DrinkMothamphetamineGlass
  suffix: mothamphetamine
  description: "Дивно... Розмитий напій. У нього хаотична аура."
  components:
    - type: SolutionContainerManager
      solutions:
        drink:
          maxVol: 30
          reagents:
            - ReagentId: Mothamphetamine
              Quantity: 30
    - type: Icon
      sprite: DeltaV/Objects/Consumable/Drinks/mothamphetamine.rsi
      state: icon

- type: entity
  parent: DrinkGlass
  id: DoubleIceCreamGlass
  suffix: double ice cream
  components:
  - type: SolutionContainerManager
    solutions:
      drink:
        maxVol: 30
        reagents:
        - ReagentId: DoubleIceCream
          Quantity: 30
  - type: Icon
    sprite: DeltaV/Objects/Consumable/Drinks/doubleicecreamglass.rsi
    state: icon
