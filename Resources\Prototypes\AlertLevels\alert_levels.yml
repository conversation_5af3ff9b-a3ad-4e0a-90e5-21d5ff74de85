- type: alertLevels
  id: stationAlerts
  defaultLevel: green
  levels:
    green:
      announcement: alert-level-green-announcement
      color: Green
      emergencyLightColor: LawnGreen
      sound: /Audio/Announcements/Alerts/code_green.ogg
      shuttleTime: 600
    blue:
      announcement: alert-level-blue-announcement
      sound: /Audio/Announcements/Alerts/code_blue.ogg
      color: DodgerBlue
      forceEnableEmergencyLights: true
      emergencyLightColor: DodgerBlue
      shuttleTime: 600
    violet:
      announcement: alert-level-violet-announcement
      sound: /Audio/Announcements/Alerts/code_violet.ogg
      color: Violet
      emergencyLightColor: Violet
      forceEnableEmergencyLights: true
      shuttleTime: 600
    white:
      announcement: alert-level-white-announcement
      sound: /Audio/Announcements/Alerts/code_white.ogg
      emergencyLightColor: "#F6CCF6"
      color: White
      shuttleTime: 600
    yellow:
      announcement: alert-level-yellow-announcement
      sound: /Audio/Announcements/Alerts/code_yellow.ogg
      color: Yellow
      emergencyLightColor: Goldenrod
      forceEnableEmergencyLights: true
      shuttleTime: 600
    red:
      announcement: alert-level-red-announcement
      sound: /Audio/Announcements/Alerts/code_red.ogg
      color: Red
      emergencyLightColor: Red
      forceEnableEmergencyLights: true
      shuttleTime: 600
    gamma:
      announcement: alert-level-gamma-announcement
      selectable: false
      sound: /Audio/Announcements/Alerts/code_gamma.ogg
      disableSelection: true
      color: PaleVioletRed
      emergencyLightColor: PaleVioletRed
      forceEnableEmergencyLights: true
      shuttleTime: 600
    delta:
      announcement: alert-level-delta-announcement
      selectable: false
      sound: /Audio/Announcements/Alerts/code_delta.ogg
      disableSelection: true
      color: DarkRed
      emergencyLightColor: Orange
      forceEnableEmergencyLights: true
      shuttleTime: 1200
    epsilon:
      announcement: alert-level-epsilon-announcement
      selectable: false
      sound: /Audio/Announcements/Alerts/code_epsilon.ogg
      disableSelection: true
      color: DarkViolet
      emergencyLightColor: DarkViolet
      forceEnableEmergencyLights: true
      shuttleTime: 1200
    octarine: #imp station cosmic cult
      announcement: alert-level-octarine-announcement
      selectable: false
      sound: /Audio/Announcements/Alerts/code_epsilon.ogg # i think this is deprecated bc of random announcers
      disableSelection: true
      color: CadetBlue
      emergencyLightColor: CadetBlue
      forceEnableEmergencyLights: true
      shuttleTime: 1200
