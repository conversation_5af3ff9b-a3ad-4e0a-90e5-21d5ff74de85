- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackSatchel
  name: "рюкзак"
  description: "Модний на вигляд рюкзак."
  components:
  - type: Sprite
    sprite: Clothing/Back/Satchels/satchel.rsi
  - type: Storage
    grid:
    - 0,0,1,3
    - 3,0,6,3
    - 8,0,9,3

- type: entity
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelLeather
  name: "шкіряна сумка"
  description: "Трендовий рюкзак з минулої епохи."
  components:
  - type: Sprite
    sprite: Clothing/Back/Satchels/leather.rsi

- type: entity
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelEngineering
  name: "інженерний ранець"
  description: "Міцний рюкзак з додатковими кишенями."
  components:
  - type: Sprite
    sprite: Clothing/Back/Satchels/engineering.rsi

- type: entity
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelAtmospherics
  name: "атмосферний рюкзак"
  description: "Міцний рюкзак з вогнетривких волокон. Пахне плазмою."
  components:
  - type: Sprite
    sprite: Clothing/Back/Satchels/atmospherics.rsi

- type: entity
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelClown
  name: "клоунська сумка"
  description: "Для швидкої втечі від охорони."
  components:
  - type: Sprite
    sprite: Clothing/Back/Satchels/clown.rsi
  - type: Storage
    storageOpenSound:
      collection: BikeHorn

- type: entity
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelMime
  name: "пантоміма"
  description: "Ранець, створений для мовчазного та виразного мистецтва міміки."
  components:
  - type: Sprite
    sprite: Clothing/Back/Satchels/mime.rsi

- type: entity
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelMedical
  name: "медична сумка"
  description: "Стерильна сумка, яку використовують у медичних відділеннях."
  components:
  - type: Sprite
    sprite: Clothing/Back/Satchels/medical.rsi

- type: entity
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelChemistry
  name: "ранець з хімії"
  description: "Стерильний саквояж з аптечними фарбами."
  components:
  - type: Sprite
    sprite: Clothing/Back/Satchels/chemistry.rsi

- type: entity
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelVirology
  name: "вірусологічна сумка"
  description: "Сумка з гіпоалергенних волокон. Створена, щоб допомогти запобігти поширенню хвороб. Пахне мавпочкою."
  components:
  - type: Sprite
    sprite: Clothing/Back/Satchels/virology.rsi

- type: entity
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelGenetics
  name: "генетичний ранець"
  description: "Стерильна сумка з кольорами генетика."
  components:
  - type: Sprite
    sprite: Clothing/Back/Satchels/genetics.rsi

- type: entity
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelScience
  name: "сумка з епістемології" # DeltaV - Epistemics Department replacing Science
  description: "Зручний для зберігання дослідницьких матеріалів."
  components:
  - type: Sprite
    sprite: Clothing/Back/Satchels/science.rsi

- type: entity
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelRobotics
  name: "ранець з робототехніки"
  description: "Зручний для зберігання дослідницьких матеріалів."
  components:
  - type: Sprite
    sprite: Clothing/Back/Satchels/robotics.rsi

- type: entity
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelSecurity
  name: "сумка служби безпеки"
  description: "Міцний рюкзак для потреб, пов'язаних з безпекою."
  components:
  - type: Sprite
    sprite: Clothing/Back/Satchels/security.rsi

- type: entity
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelBrigmedic
  name: "сумка бріг-медика" # DeltaV - rename brigmedic to corpsman
  description: "Стерильна сумка для медичних потреб."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Back/Satchels/brigmedic.rsi # DeltaV - resprite

- type: entity
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelCaptain
  name: "ранець капітана"
  description: "Ексклюзивний ранець для офіцерів \"Нанотрейзену\"."
  components:
  - type: Sprite
    sprite: Clothing/Back/Satchels/captain.rsi

- type: entity
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelHydroponics
  name: "сумка для гідропоніки"
  description: "Сумка виготовлена з натуральних волокон."
  components:
  - type: Sprite
    sprite: Clothing/Back/Satchels/hydroponics.rsi

- type: entity
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelCargo
  name: "логістична сумка" # DeltaV - Logistics Department replacing Cargo
  description: "Міцний рюкзак для крадіжок з логістики." # DeltaV - Logistics Department replacing Cargo
  components:
    - type: Sprite
      sprite: Clothing/Back/Satchels/cargo.rsi

- type: entity
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelSalvage
  name: "сумка утилізтора"
  description: "Міцний рюкзак для зберігання здобичі."
  components:
    - type: Sprite
      sprite: Clothing/Back/Satchels/salvage.rsi

- type: entity
  parent: ClothingBackpackSatchel
  id: ClothingBackpackSatchelHolding
  name: "сумка для утримання"
  description: "Сумка, що відкривається в локальну кишеню блюзового простору."
  components:
  - type: Sprite
    sprite: Clothing/Back/Satchels/holding.rsi
    state: icon
    layers:
    - state: icon
    - state: icon-unlit
      shader: unshaded
  - type: Item
    size: Ginormous
  - type: Storage
    maxItemSize: Huge
    grid:
    - 0,0,19,9

- type: entity
  parent: [ClothingBackpackSatchel, BaseFoldable]
  id: ClothingBackpackSatchelPurse
  name: "гаманець"
  description: "Невелика модна сумка, яку зазвичай носять через плече."
  components:
  - type: Sprite
    sprite: Clothing/Back/Satchels/purse.rsi
  - type: Foldable
    canFoldInsideContainer: true
    unfoldVerbText: fold-flip-verb
    foldVerbText: fold-flip-verb
  - type: FoldableClothing
    foldedEquippedPrefix: flipped

- type: entity
  parent: ClothingBackpackSatchelPurse
  id: ClothingBackpackSatchelPurseFlipped
  suffix: flipped
  components:
  - type: Foldable
    folded: true
  - type: Clothing
    equippedPrefix: flipped
