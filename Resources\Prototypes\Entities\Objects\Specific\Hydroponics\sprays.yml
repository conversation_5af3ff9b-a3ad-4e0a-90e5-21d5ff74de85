- type: entity
  name: "Plant-B-Gone"
  id: PlantBGoneSpray
  parent: SprayBottle
  description: "Вбиває ці настирливі бур'яни!"
  suffix: "Filled"
  components:
  - type: Tag
    tags:
    - PlantBGone
  - type: Sprite
    sprite: Objects/Tools/Hydroponics/sprays.rsi
    state: plantbgone
  - type: SolutionContainerManager
    solutions:
      spray:
        maxVol: 100
        reagents:
        - ReagentId: PlantBGone
          Quantity: 100
  - type: DrainableSolution
    solution: spray
  - type: Item
    sprite: Objects/Tools/Hydroponics/sprays.rsi
    heldPrefix: plantbgone

- type: entity
  name: "спрей від бур'янів"
  id: WeedSpray
  parent: SprayBottle
  description: "Це токсична суміш у формі спрею для знищення дрібних бур'янів."
  suffix: "Filled"
  components:
  - type: Sprite
    sprite: Objects/Tools/Hydroponics/sprays.rsi
    state: weedspray
  - type: SolutionContainerManager
    solutions:
      spray:
        maxVol: 50
        reagents:
        - ReagentId: WeedKiller
          Quantity: 50
  - type: DrainableSolution
    solution: spray
  - type: Spillable
    solution: spray
  - type: Tag
    tags:
    - Spray
  - type: Spray
    transferAmount: 1
  - type: Item
    sprite: Objects/Tools/Hydroponics/sprays.rsi

- type: entity
  name: "спрей від шкідників"
  id: PestSpray
  parent: WeedSpray
  description: "Це спрей для знищення шкідників! Не вдихайте!"
  suffix: "Filled"
  components:
  - type: Sprite
    state: pestspray
  - type: SolutionContainerManager
    solutions:
      spray:
        maxVol: 50
        reagents:
        - ReagentId: PestKiller
          Quantity: 50
  - type: DrainableSolution
    solution: spray
  - type: Item
    sprite: Objects/Tools/Hydroponics/sprays.rsi
