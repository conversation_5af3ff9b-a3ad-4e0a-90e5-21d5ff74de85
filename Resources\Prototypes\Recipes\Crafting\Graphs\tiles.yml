- type: constructionGraph
  id: TileSteel
  start: start
  graph:
    - node: start
      edges:
        - to: steeltile
          completed:
            - !type:SetStackCount
              amount: 4
          steps:
            - material: Steel
              amount: 1
    - node: steeltile
      entity: FloorTileItemSteel

- type: constructionGraph
  id: TileWood
  start: start
  graph:
    - node: start
      edges:
        - to: woodtile
          completed:
            - !type:SetStackCount
              amount: 4
          steps:
            # Needs StackType ID
            - material: WoodPlank
              amount: 1
    - node: woodtile
      entity: FloorTileItemWood

- type: constructionGraph
  id: TilesBrass
  start: start
  graph:
    - node: start
      edges:
        - to: filledPlate
          completed:
            - !type:SetStackCount
              amount: 4
          steps:
            - material: Brass
              amount: 1
        - to: reebe
          completed:
            - !type:SetStackCount
              amount: 4
          steps:
            - material: Brass
              amount: 1
    
    - node: filledPlate
      entity: FloorTileItemBrassFilled
      
    - node: reebe
      entity: FloorTileItemBrassReebe

- type: constructionGraph
  id: TileWhite
  start: start
  graph:
    - node: start
      edges:
        - to: whitetile
          completed:
            - !type:SetStackCount
              amount: 4
          steps:
            - material: Steel
              amount: 1
    - node: whitetile
      entity: FloorTileItemWhite

- type: constructionGraph
  id: TileDark
  start: start
  graph:
    - node: start
      edges:
        - to: darktile
          completed:
            - !type:SetStackCount
              amount: 4
          steps:
            - material: Steel
              amount: 1
    - node: darktile
      entity: FloorTileItemDark

- type: constructionGraph
  id: TileFlesh
  start: start
  graph:
    - node: start
      edges:
        - to: fleshTile
          completed:
            - !type:SetStackCount
              amount: 4
          steps:
            - material: MeatSheets
              amount: 1
    - node: fleshTile
      entity: FloorTileItemFlesh

- type: constructionGraph
  id: TileWoodLarge
  start: start
  graph:
    - node: start
      edges:
        - to: woodtilelarge
          completed:
            - !type:SetStackCount
              amount: 4
          steps:
            - material: WoodPlank
              amount: 2
    - node: woodtilelarge
      entity: FloorTileItemWoodLarge