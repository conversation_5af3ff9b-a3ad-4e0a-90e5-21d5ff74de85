
- type: entity
  parent: ClothingUniformBase
  id: ClothingUniformRandom
  abstract: true
  description: "Генерується нейронними мережами на основі останніх модних тенденцій."
  suffix: Random visual
  components:
  - type: SuitSensor
  - type: Sprite
    sprite: Clothing/Uniforms/procedural.rsi
    layers:
    - state: base_torso_standard
      map: [ "torso" ]
    - state: base_leg_standard
      map: [ "leg" ]
    - state: mask_null
      map: [ "decor" ]
    - state: mask_null
      map: [ "overlay" ]
  - type: Clothing
    femaleMask: UniformTop
    maleMask: UniformTop
    sprite: Clothing/Uniforms/procedural.rsi
    clothingVisuals:
      jumpsuit:
      - state: base_torso_standard
        map: [ "torso" ]
      - state: base_leg_standard
        map: [ "leg" ]
      - state: mask_null
        map: [ "decor" ]
      - state: mask_null
        map: [ "overlay" ]

- type: entity
  parent: ClothingUniformRandom
  id: ClothingRandomSpawner
  name: "довільний барвистий костюм"
  components:
  - type: RandomSpawner
    offset: 0
    prototypes:
    - ClothingUniformRandomArmless
    - ClothingUniformRandomStandard
    - ClothingUniformRandomBra
    - ClothingUniformRandomShorts
    - ClothingUniformRandomShirt

- type: entity
  parent: ClothingUniformRandom
  id: ClothingUniformRandomArmless
  name: "барвистий безрукавий костюм"
  components:
  - type: RandomSprite
    available:
      - torso:
          base_torso_armless: Sixteen
        leg:
          base_leg_standard: Sixteen
          base_leg_short: Sixteen
          base_leg_skirt: Sixteen
          base_leg_skirt_long: Sixteen
        decor:
          decor_torso_armless1: Sixteen
          decor_torso_armless2: Sixteen
          decor_torso_armless3: Sixteen
          decor_torso_armless4: Sixteen
          decor_torso_armless5: Sixteen
          decor_torso_armless6: Sixteen
          decor_torso_armless7: Sixteen
          decor_torso_armless8: Sixteen
          decor_torso_armless9: Sixteen
          decor_torso_armless10: Sixteen
          mask_null: ""

- type: entity
  parent: ClothingUniformRandom
  id: ClothingUniformRandomStandard
  name: "барвистий костюм"
  components:
  - type: RandomSprite
    available:
      - torso:
          base_torso_standard: Sixteen
          base_torso_standard2: Sixteen
        leg:
          base_leg_standard: Sixteen
          base_leg_short: Sixteen
          base_leg_skirt: Sixteen
          base_leg_skirt_long: Sixteen
        decor:
          decor_torso_armless1: Sixteen
          decor_torso_armless2: Sixteen
          decor_torso_armless3: Sixteen
          decor_torso_armless4: Sixteen
          decor_torso_armless5: Sixteen
          decor_torso_armless6: Sixteen
          decor_torso_armless7: Sixteen
          decor_torso_armless8: Sixteen
          decor_torso_armless9: Sixteen
          decor_torso_armless10: Sixteen
          decor_torso_standard1: Sixteen
          decor_torso_standard2: Sixteen
          decor_torso_standard3: Sixteen
          decor_torso_standard4: Sixteen
          decor_torso_standard5: Sixteen
          decor_torso_standard6: Sixteen
          decor_torso_standard7: Sixteen
          decor_torso_standard8: Sixteen
          decor_torso_standard9: Sixteen
          mask_null: ""

- type: entity
  parent: ClothingUniformRandom
  id: ClothingUniformRandomBra
  name: "барвистий топ"
  components:
  - type: RandomSprite
    available:
      - torso:
          base_torso_bra: Sixteen
        leg:
          base_leg_standard: Sixteen
          base_leg_short: Sixteen
          base_leg_skirt: Sixteen
          base_leg_skirt_long: Sixteen
        decor:
          decor_torso_bra1: Sixteen
          decor_torso_bra2: Sixteen
          decor_torso_bra3: Sixteen
          decor_torso_bra4: Sixteen
          decor_torso_bra5: Sixteen
          mask_null: ""

- type: entity
  parent: ClothingUniformRandom
  id: ClothingUniformRandomShorts
  name: "барвисті штани"
  components:
  - type: RandomSprite
    available:
      - torso:
          mask_null: ""
        leg:
          base_leg_standard: Sixteen
          base_leg_short: Sixteen
          base_leg_skirt: Sixteen
          base_leg_skirt_long: Sixteen

- type: entity
  parent: ClothingUniformRandom
  id: ClothingUniformRandomShirt
  name: "барвистий костюм"
  components:
  - type: RandomSprite
    available:
      - torso:
          base_torso_armless: Sixteen
          mask_null: ""
        leg:
          base_leg_standard: Sixteen
          base_leg_short: Sixteen
        decor:
          base_torso_shirt: Sixteen
        overlay:
          decor_torso_shirt1: Sixteen
          decor_torso_shirt2: Sixteen
          decor_torso_shirt3: Sixteen
