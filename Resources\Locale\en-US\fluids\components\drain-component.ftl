﻿drain-component-empty-verb-using-is-empty-message = { CAPITALIZE(THE($object)) } is empty!
drain-component-empty-verb-target-is-full-message = { CAPITALIZE(THE($object)) } is full!
drain-component-empty-verb-inhand = Empty {$object}
drain-component-examine-hint-full = [color="blue"]It is filled to the brim. Maybe a plunger can help?[/color]
drain-component-examine-volume = [color="blue"]Remaining space - {$volume}u.[/color]
drain-component-unclog-fail = { CAPITALIZE(THE($object)) } is still full.
drain-component-unclog-success = { CAPITALIZE(THE($object)) } unclogs.
drain-component-unclog-notapplicable = { CAPITALIZE(THE($object)) } isn't clogged.
