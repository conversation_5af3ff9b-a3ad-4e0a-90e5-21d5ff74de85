﻿<controls:FancyWindow xmlns="https://spacestation14.io"
                      xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
                      xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                      xmlns:customControls="clr-namespace:Content.Client.Administration.UI.CustomControls"
                      Title="{Loc 'borg-ui-menu-title'}"
                      MinSize="600 350"
                      SetSize="650 450">
    <BoxContainer Orientation="Horizontal"
                  HorizontalExpand="True"
                  VerticalExpand="True">
        <BoxContainer Orientation="Vertical" VerticalExpand="True" HorizontalExpand="True" Margin="15 10 15 15">
            <BoxContainer Orientation="Horizontal">
                <ProgressBar Name="ChargeBar" Access="Public" HorizontalExpand="True" MinValue="0" Value="1" MaxValue="1">
                    <Label Name="ChargeLabel" HorizontalAlignment="Left" VerticalAlignment="Center" Margin="5 0 0 0"/>
                </ProgressBar>
                <Control MinWidth="5"/>
                <Button Name="EjectBatteryButton" Text="{Loc 'borg-ui-remove-battery'}" StyleClasses="OpenLeft"/>
            </BoxContainer>
            <customControls:HSeparator Margin="0 10 0 10"/>
            <PanelContainer VerticalExpand="True">
                <PanelContainer.PanelOverride>
                    <gfx:StyleBoxFlat BackgroundColor="#1B1B1E"/>
                </PanelContainer.PanelOverride>
                <BoxContainer HorizontalAlignment="Center" VerticalAlignment="Center">
                    <SpriteView Name="BorgSprite" Scale="5 5"/>
                </BoxContainer>
            </PanelContainer>
            <customControls:HSeparator Margin="0 10 0 10"/>
            <BoxContainer Orientation="Horizontal">
                <LineEdit Name="NameLineEdit" HorizontalExpand="True"/>
                <Label Name="NameIdentifierLabel" Margin="10 0 0 0"/>
            </BoxContainer>
            <Control MinHeight="10"/>
            <Button HorizontalExpand="True" Name="BrainButton" ToggleMode="False" Margin="0" MinHeight="40" StyleClasses="OpenLeft">
                <SpriteView Name="BrainView" HorizontalAlignment="Left" VerticalAlignment="Center"/>
            </Button>
        </BoxContainer>
        <customControls:VSeparator/>
        <BoxContainer Orientation="Vertical" VerticalExpand="True" HorizontalExpand="True" Margin="15 10 15 15">
            <Label Text="{Loc 'borg-ui-modules-label'}" Margin="0 0 0 5"/>
            <PanelContainer VerticalExpand="True">
                <PanelContainer.PanelOverride>
                    <gfx:StyleBoxFlat BackgroundColor="#1B1B1E"/>
                </PanelContainer.PanelOverride>
                <BoxContainer VerticalExpand="True" Orientation="Vertical">
                    <ScrollContainer HScrollEnabled="False" HorizontalExpand="True" VerticalExpand="True">
                        <BoxContainer Orientation="Vertical" HorizontalExpand="True" VerticalExpand="True" Name="ModuleContainer" RectClipContent="True"/>
                    </ScrollContainer>
                    <Label Name="ModuleCounter"
                           HorizontalAlignment="Right"
                           VerticalAlignment="Bottom"
                           StyleClasses="WindowFooterText"
                           Margin="5"/>
                </BoxContainer>
            </PanelContainer>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
