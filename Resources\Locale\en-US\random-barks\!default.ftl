# Default barks. This file serves as a template for future bark types and as a fallback for mobs who do not have unique barks for one reason or another.

# Recommendations:
# - Try to keep barks simple and reasonably short. 8 words is the usual limit, but most barks should be at msot 3 words long.
# - Each bark should be one, or at most two sentences long.
# - Keep in mind that those will usually not be understood by players.
# - Do not add punctuation at the end of the message, except for question marks, so that the random bark system will add random punctuation.
bark-default-1 = Bark
bark-default-2 = Boof
bark-default-3 = Woofums
bark-default-4 = Rawrl
bark-default-5 = Eeeeeee
bark-default-6 = Barkums
bark-default-7 = Awooooooooooooooooooo awooo awoooo
bark-default-8 = Grrrrrrrrrrrrrrrrrr
bark-default-9 = Rarrwrarrwr
bark-default-10 = Goddamn I love gold fish crackers
bark-default-11 = Bork bork boof boof bork bork boof boof bork
bark-default-12 = Bark
bark-default-13 = Boof
bark-default-14 = Woofums
bark-default-15 = <PERSON>rl
bark-default-16 = <PERSON><PERSON><PERSON><PERSON>
bark-default-17 = Barkum

# This should always come last so that it's easy to keep track of.
# Bark counts are locale-specific so they are typically defined in FTL files instead of YML, to make localization easier.
bark-default-count = 17
