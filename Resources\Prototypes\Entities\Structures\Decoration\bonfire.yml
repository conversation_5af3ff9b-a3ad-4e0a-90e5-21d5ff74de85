- type: entity
  id: Bonfire
  parent: BaseStructure
  name: "вогнище"
  description: "Що може бути краще, ніж пізній вечір під небом з гітарою та друзями."
  components:
  - type: Sprite
    noRot: true
    sprite: Structures/Decoration/bonfire.rsi
    layers:
      - state: bonfire
      - state: burning
  - type: PointLight
    radius: 5
    energy: 3
    color: "#FFC90C"
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Wood
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 50
        behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
  - type: AmbientSound
    volume: -5
    range: 5
    sound:
      path: /Audio/Ambience/Objects/fireplace.ogg
  - type: AlwaysHot
  - type: Construction # Frontier
    graph: FiresGraph # Frontier
    node: BonfireNode # Frontier
  - type: Tag
    tags:
      - NoPaint

- type: entity
  id: LegionnaireBonfire
  parent: Bonfire
  name: "вогнище легіонерів"
  description: "Там, у країні лави і попелу, можна приготувати зефір і картоплю."
  components:
  - type: Sprite
    layers:
      - state: legionnaire_bonfire
  - type: PointLight
    color: "#FF5601"
