using Content.Shared.Roles;
using Robust.Shared.Prototypes;
using Robust.Shared.Serialization.TypeSerializers.Implementations.Custom.Prototype;

namespace Content.Shared._Pirate.DNDMage;

[RegisterComponent]
public sealed partial class DNDMageRoleComponent : Component
{
    [DataField("antagonistRole", customTypeSerializer: typeof(PrototypeIdSerializer<AntagPrototype>))]
    public string AntagRole = "DNDMage";
}
