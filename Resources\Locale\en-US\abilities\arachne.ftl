action-name-spin-web = Spin Web
action-desc-spin-web = Use your spinnerets to make a spider web in the current tile. Makes you hungrier and thirstier.
action-name-spin-web-space = You can't spin a web in space!
action-name-spin-web-blocked = There's no room for a web here.
spin-web-action-hungry = You're too hungry to spin a web!
spin-web-action-thirsty = You're too thirsty to spin a web!
spin-web-start-second-person = You start spinning a web.
spin-web-start-third-person = {CAPITALIZE(THE($spider))} starts spinning a web!
cocoon-start-second-person = You start cocooning {THE($target)}.
cocoon-start-third-person = {CAPITALIZE(THE($spider))} starts cocooning {THE($target)}.
uncocoon-start-second-person = You start releasing {THE($target)}.
uncocoon-start-third-person = {CAPITALIZE(THE($spider))} starts releasing {THE($target)}.
spun-web-second-person = You spin up a web.
spun-web-third-person = {CAPITALIZE(THE($spider))} spins up a web!
cocoon = Cocoon
uncocoon = Uncocoon
