- type: entity
  parent: BaseGameRule
  id: DNDMageRule
  components:
  - type: DNDMageRule
  - type: StationEvent
    weight: 10
    duration: 1
    earliestStart: 30
    minimumPlayers: 10
  - type: GameRule
    minPlayers: 10
  - type: AntagObjectives
    objectives:
    - DNDMageSurviveObjective
  - type: AntagSelection
    useCharacterNames: true
    agentName: dnd-mage-roundend-name
    definitions:
    - prefRoles: [ DNDMage ]
      playerRatio: 15
      min: 1
      max: 1
      mindRoles:
      - MindRoleDNDMage
      startingGear: DNDMageGear
      unequipOldGear: true
      components:
      - type: DNDMage
      - type: DNDMageSavingThrow
        savingThrowEnabled: true
      - type: RandomMetadata
        nameSegments:
        - names_wizard_first
        - names_wizard_last
      - type: NpcFactionMember
        factions:
        - Wizard
  - type: MidRoundAntagRule
    spawner: SpawnPointGhostDNDMage

