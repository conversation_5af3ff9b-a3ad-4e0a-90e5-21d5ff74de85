# Moles calculation
#
#   moles = 1000 / (8.31446261 * 293.15 / volume)
#
#   More practically:
#
#   moles = volume * 0.41027581
#
#   Yes, you do need all those digits or your pressure wont end up exactly 1000
#
#
# The number 1000 comes from the default MaxReleasePressure for a GasCanister,
# which is 10 * Atmospherics.OneAtmosphere, rounded down.
#
# 8.31446261 is the universal gas constant
#
# 293.15 is our default atmospheric temperature
#

# If you change the mole amounts, you can calculate the new gas supply times with:
# minutes = (moles / outputPressure) * 325

- type: entity
  id: OxygenTankFilled
  parent: OxygenTank
  suffix: Filled
  components:
  - type: GasTank
    outputPressure: 21.3
    air:
      # 31 minutes
      volume: 5
      moles:
        - 2.051379050 # oxygen
      temperature: 293.15

- type: entity
  id: EmergencyOxygenTankFilled
  parent: EmergencyOxygenTank
  suffix: Filled
  components:
  - type: GasTank
    outputPressure: 21.3
    air:
      # 4 minutes
      volume: 0.66
      moles:
        - 0.270782035 # oxygen
      temperature: 293.15

- type: entity
  id: EmergencyNitrogenTankFilled
  parent: EmergencyNitrogenTank
  suffix: Filled
  components:
  - type: GasTank
    outputPressure: 21.3
    air:
      # 4 minutes
      volume: 0.66
      moles:
        - 0 # oxygen
        - 0.270782035 # nitrogen
      temperature: 293.15

- type: entity
  id: EmergencyPlasmaTankFilled
  parent: EmergencyPlasmaTank
  suffix: Filled
  components:
  - type: GasTank
    outputPressure: 5.325
    air:
      # 16 minutes with Plasmaman lungs
      volume: 0.66
      moles:
        - 0           # oxygen
        - 0           # nitrogen
        - 0           # CO2
        - 0.270782035 # nitrogen
      temperature: 293.15

- type: entity
  id: ExtendedEmergencyOxygenTankFilled
  parent: ExtendedEmergencyOxygenTank
  suffix: Filled
  components:
  - type: GasTank
    outputPressure: 21.3
    air:
      # 9 minutes
      volume: 1.5
      moles:
        - 0.615413715 # oxygen
      temperature: 293.15

- type: entity
  id: ExtendedEmergencyNitrogenTankFilled
  parent: ExtendedEmergencyNitrogenTank
  suffix: Filled
  components:
  - type: GasTank
    outputPressure: 21.3
    air:
      # 9 minutes
      volume: 1.5
      moles:
        - 0 # oxygen
        - 0.615413715 # nitrogen
      temperature: 293.15

- type: entity
  id: ExtendedEmergencyPlasmaTankFilled
  parent: ExtendedEmergencyPlasmaTank
  suffix: Filled
  components:
  - type: GasTank
    outputPressure: 5.325
    air:
      # 37 minutes with Plasmaman lungs
      volume: 1.5
      moles:
        - 0           # oxygen
        - 0           # nitrogen
        - 0           # CO2
        - 0.615413715 # plasma
      temperature: 293.15

- type: entity
  id: DoubleEmergencyOxygenTankFilled
  parent: DoubleEmergencyOxygenTank
  suffix: Filled
  components:
  - type: GasTank
    outputPressure: 21.3
    air:
      # 15 minutes
      volume: 2.5
      moles:
        - 1.025689525 # oxygen
      temperature: 293.15

- type: entity
  id: DoubleEmergencyNitrogenTankFilled
  parent: DoubleEmergencyNitrogenTank
  suffix: Filled
  components:
  - type: GasTank
    outputPressure: 21.3
    air:
      # 15 minutes
      volume: 2.5
      moles:
        - 0 # oxygen
        - 1.025689525 # nitrogen
      temperature: 293.15

- type: entity
  id: DoubleEmergencyPlasmaTankFilled
  parent: DoubleEmergencyPlasmaTank
  suffix: Filled
  components:
  - type: GasTank
    outputPressure: 5.325
    air:
      # 62 minutes with Plasmaman lungs
      volume: 1.5
      moles:
        - 0           # oxygen
        - 0           # nitrogen
        - 0           # CO2
        - 1.025689525 # plasma
      temperature: 293.15

- type: entity
  id: EmergencyFunnyOxygenTankFilled
  parent: EmergencyFunnyOxygenTank
  suffix: Filled
  components:
  - type: GasTank
    outputPressure: 22.4
    air:
      # 4 minutes
      volume: 0.66
      moles:
        - 0.270782035 # 95% oxygen
        - 0 # nitrogen
        - 0 # CO2
        - 0 # plasma
        - 0 # tritium
        - 0 # water vapor
        - 0 # ammonia
        - 0.014251686 # 5% N2O
        # 0.285033721       total
      temperature: 293.15

- type: entity
  id: AirTankFilled
  parent: AirTank
  suffix: Filled
  components:
  - type: GasTank
    outputPressure: 101.3
    air:
      # 6 minutes due to output pressure
      volume: 5
      moles:
        - 0.451303391 # 22% oxygen
        - 1.600075659 # 78% nitrogen
        # 2.051379050       total
      temperature: 293.15

- type: entity
  id: NitrogenTankFilled
  parent: NitrogenTank
  suffix: Filled
  name: "азотний бак"
  components:
  - type: GasTank
    air:
      # 31 minutes
      volume: 5
      moles:
        - 0           # oxygen not included
        - 2.051379050 # nitrogen
      temperature: 293.15

- type: entity
  id: NitrousOxideTankFilled
  parent: NitrousOxideTank
  suffix: Filled
  name: "бак закису азоту"
  components:
  - type: GasTank
    #      0.21  | % oxygen in normal atmosphere
    #  /   0.7   | % oxygen in this mixture
    #  * 101.325 | one atmosphere
    # __________
    #    30.3975   optimal output pressure
    outputPressure: 30.4
    air:
      # only 22 minutes due to pressure
      volume: 5
      moles:
        - 1.435965335 # 70% oxygen
        - 0 # nitrogen
        - 0 # CO2
        - 0 # plasma
        - 0 # tritium
        - 0 # water vapor
        - 0 # ammonia
        - 0.615413715 # 30% N2O
        # 2.051379050       total
      temperature: 293.15

- type: entity
  id: PlasmaTankFilled
  parent: PlasmaTank
  name: "плазмовий бак"
  suffix: Filled
  components:
  - type: GasTank
    outputPressure: 5.325
    air:
      # 125.2 minutes with Plasmaman lungs
      volume: 5
      moles:
        - 0           # oxygen
        - 0           # nitrogen
        - 0           # CO2
        - 2.051379050 # plasma
      temperature: 293.15
