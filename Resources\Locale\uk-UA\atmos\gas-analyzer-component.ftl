## Entity

gas-analyzer-component-player-cannot-reach-message = Ви не можете туди дотягнутися.
gas-analyzer-shutoff = Газоаналізатор вимкнено.

## UI

gas-analyzer-window-name = Газовий аналізатор
gas-analyzer-window-environment-tab-label = Навколишнє середовище
gas-analyzer-window-tab-title-capitalized = {CAPITALIZE($title)}
gas-analyzer-window-refresh-button = Оновити
gas-analyzer-window-no-data = Дані Відсутні
gas-analyzer-window-no-gas-text = Гази Відсутні
gas-analyzer-window-error-text = Помилка: {$errorText}
gas-analyzer-window-pressure-text = Тиск:
gas-analyzer-window-pressure-val-text = {$pressure} кПа
gas-analyzer-window-temperature-text = Температура:
gas-analyzer-window-temperature-val-text = {$tempK}K ({$tempC}°Ц)
gas-analyzer-window-gas-column-name = Газ
gas-analyzer-window-molarity-column-name = моль
gas-analyzer-window-percentage-column-name = %
gas-analyzer-window-molarity-text = {$mol} моль
gas-analyzer-window-percentage-text = {$percentage}
gas-analyzer-window-molarity-percentage-text = {$gasName}: {$amount} моль ({$percentage}%)

# Used for GasEntry.ToString()
gas-entry-info = {$gasName}: {$gasAmount} моль

# overrides for trinary devices to have saner names
gas-analyzer-window-text-inlet = Вхідний отвір
gas-analyzer-window-text-outlet = Вихідний отвір
gas-analyzer-window-text-filter = Фільтр

gas-analyzer-window-volume-text = Обсяг:
gas-analyzer-window-volume-val-text = {$volume} L