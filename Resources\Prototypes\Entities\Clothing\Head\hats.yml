- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeaverHat
  name: "боброва шапка"
  description: "Панове?"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/beaver_hat.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/beaver_hat.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeret
  name: "берет"
  description: "Берет - улюблений головний убір художників."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/beret.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/beret.rsi
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: ClothingHeadHatBeret
  id: ClothingHeadHatBeretWhite
  name: "берет"
  description: "Берет - улюблений головний убір художників."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/beretwhite.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/beretwhite.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeretFrench
  name: "французький берет"
  description: "Французький берет, \"vive la France\"."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/beret_french.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/beret_french.rsi
  - type: AddAccentClothing
    accent: FrenchAccent
  - type: Tag
    tags:
    - ClothMade
    - WhitelistChameleon

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeretSecurity
  name: "берет служби безпеки"
  description: "Стильний варіант одягу для співробітників служби безпеки."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/beret_security.rsi # DeltaV - resprite
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/beret_security.rsi # DeltaV - resprite
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeretArtist
  name: "берет художника"
  description: "Берет для художника."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/beret_artist.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/beret_artist.rsi
  - type: Tag
    tags:
    - ClothMade

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeretPeaked
  name: "гостроверхий берет"
  description: "Гостроверхий берет."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/beret_peaked.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/beret_peaked.rsi
  - type: Tag
    tags:
    - ClothMade

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatCasa
  name: "азійсьий бриль"
  description: "Конусоподібний капелюх"
  components:
    - type: Sprite
      sprite: Clothing/Head/Hats/casa.rsi
    - type: Clothing
      sprite: Clothing/Head/Hats/casa.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeretRND
  name: "берет наукового відділу"
  description: "Берет для справжніх науковців."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/beret_rnd.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/beret_rnd.rsi
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeretEngineering
  name: "берет інженерного відділу"
  description: "Берет з інженерною символікою. Для інженерів, які більше схильні до стилю, ніж до безпеки."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/beret_engineering.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/beret_engineering.rsi
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeretQM
  # DeltaV - Logistics Department - Replace Quartermaster with Logistics Officer
  name: "берет офіцера логістики"
  description: "Берет з емблемою відділу логістики. Для логістів, які більше схильні до стилю."
  # End of modified code
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/beret_qm.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/beret_qm.rsi
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeretHoS
  name: "берет голови безпеки"
  description: "Чорний берет з емблемою командирського звання. Для офіцерів, які більше схильні до стилю, ніж до безпеки."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/beret_hos.rsi # DeltaV - resprite
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/beret_hos.rsi # DeltaV - resprite
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeretWarden
  name: "берет вардена"
  description: "Чорний берет з емблемою звання наглядача. Для офіцерів, які більше схильні до стилю, ніж до безпеки." # DeltaV - changed description to match sprite
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/beret_warden.rsi # DeltaV - resprite
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/beret_warden.rsi # DeltaV - resprite

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeretSeniorPhysician
  name: "берет лікаря"
  description: "Медики, одягнені в кольори медицини та хімії, є гордістю цього відділення!"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/beret_physician.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/beret_physician.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeretBrigmedic
  name: "берет брігмедика"
  description: "Білий берет, схожий на кремовий пиріг на голові."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/beret_brigmedic.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/beret_brigmedic.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeretMerc
  name: "берет найманця"
  description: "Оливковий берет, на значку зображений шакал на скелі."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/beret_merc.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/beret_merc.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBowlerHat
  name: "капелюх-котелок"
  description: "Стильна шапка-котелок."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/bowler_hat.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/bowler_hat.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatCaptain
  name: "каска капітана"
  description: "Добре бути королем."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/captain.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/captain.rsi
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatCardborg
  name: "шолом кардборга"
  description: "Капелюх з коробки."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/cardborg_h.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/cardborg_h.rsi
  - type: TypingIndicatorClothing
    proto: robot
  - type: IdentityBlocker
  - type: IngestionBlocker

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatCentcom
  name: "фірмова шапка ЦентКому"
  description: "Добре бути імператором."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/centcom.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/centcom.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatChef
  name: "кухарський ковпак"
  description: "Це шапочка, яку використовують кухарі, щоб волосся не потрапляло на їжу. Судячи з того, що залишилося на столі, вони не працюють."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/chefhat.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/chefhat.rsi
  - type: Storage
    grid:
    - 0,0,0,0
  - type: UserInterface
    interfaces:
      enum.StorageUiKey.Key:
        type: StorageBoundUserInterface
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
  - type: PilotedClothing
    pilotWhitelist:
      tags:
        - ChefPilot
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: ClothingHeadHatChef
  id: ClothingHeadHatChefNt
  name: "ковпак шеф-кухаря Нанотрейзен"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/chefhat_nt.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/chefhat_nt.rsi

- type: entity
  parent: ClothingHeadHatChef
  id: ClothingHeadHatChefIdris
  name: "фірмовий кухарський ковпак idris"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/chefhat_idris.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/chefhat_idris.rsi

- type: entity
  parent: ClothingHeadHatChef
  id: ClothingHeadHatChefOrion
  name: "шеф-кухарський ковпак \"Оріон Експрес"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/chefhat_orion.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/chefhat_orion.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatFedoraBrown
  name: "коричнева федора"
  description: "Коричневий капелюх."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/brownfedora.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/brownfedora.rsi
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatFedoraGrey
  name: "сіра федора"
  description: "Сірий капелюх."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/greyfedora.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/greyfedora.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatFez
  name: "феска"
  description: "Червона феска."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/fez.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/fez.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatHopcap
  name: "капелюх голови персоналу"
  description: "Велична, стильна кепка для керівника персоналу."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/hopcap.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/hopcap.rsi
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatHoshat
  name: "капелюх голови безпеки"
  description: "У відділку новий шериф."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/hoshat.rsi # DeltaV - resprite
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/hoshat.rsi # DeltaV - resprite
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatOutlawHat
  name: "капелюх злочинця"
  description: "Капелюх, в якому ви виглядаєте так, ніби носите пістолет з насічкою, під номером один і ще дев'ятнадцять."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/outlawhat.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/outlawhat.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatWitch1
  name: "капелюх відьми"
  description: "Відьомський капелюх." # Goob edit
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/witchhat.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/witchhat.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatPaper
  name: "паперовий капелюх"
  description: "Капелюх з паперу."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/paper.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/paper.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatPirate
  name: "піратський капелюх"
  description: "Йо-хо-хо і пляшку рому!"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/pirate.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/pirate.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatPlaguedoctor
  name: "капелюх чумного лікаря"
  description: "Колись їх використовували чумні лікарі."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/plaguedoctor.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/plaguedoctor.rsi
  - type: Tag
    tags:
    - WhitelistChameleon
    - ClothMade
  - type: HideLayerClothing
    slots:
    - Hair
    - Snout

- type: entity
  parent: ClothingHeadBase # Goob edit
  id: ClothingHeadHatRedwizard
  name: "червоний капелюх чарівника"
  description: "Дивно виглядає червоний капелюх, який, безсумнівно, належить справжньому магічному користувачеві."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/redwizard.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/redwizard.rsi
  - type: PressureProtection # DeltaV - Make real wizard clothes space proof
    highPressureMultiplier: 0.6
    lowPressureMultiplier: 1000
  - type: TemperatureProtection
    coefficient: 0.2

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatSantahat
  name: "капелюх Санти"
  description: "Святковий капелюх, який носить Санта-Клаус"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/santahat.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/santahat.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatSombrero
  name: "сомбреро"
  description: "Ідеально для Космічної Мексики,¿si?"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/sombrero.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/sombrero.rsi
  - type: AddAccentClothing
    accent: SpanishAccent

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatSurgcapBlue
  name: "хірургічна шапочка"
  description: "Синя шапочка, яку хірурги одягають під час операцій. Не дає їхньому волоссю лоскотати ваші внутрішні органи."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/surgcap_blue.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/surgcap_blue.rsi
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatSurgcapGreen
  name: "хірургічна шапочка"
  description: "Зелена шапочка, яку носять хірурги під час операцій. Не дає їхньому волоссю лоскотати внутрішні органи."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/surgcap_green.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/surgcap_green.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatSurgcapPurple
  name: "хірургічна шапочка"
  description: "Фіолетова шапочка, яку хірурги одягають під час операцій. Не дає їхньому волоссю лоскотати ваші внутрішні органи."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/surgcap_purple.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/surgcap_purple.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatTophat
  name: "капелюх-циліндр"
  description: "Стильний чорний капелюх. У ньому виглядатимеш елігантно."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/tophat.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/tophat.rsi
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: [ClothingHeadBase, BaseFoldable]
  id: ClothingHeadHatUshanka
  name: "ушанка"
  description: "Ідеально підходить для зими в Сибіру, так? Видно що робили це без смаку на моду.."
  components:
  - type: Clothing
    sprite: Clothing/Head/Hats/ushanka.rsi
  - type: Appearance
  - type: Foldable
    canFoldInsideContainer: true
  - type: FoldableClothing
    foldedEquippedPrefix: up
    foldedHeldPrefix: up
  - type: Sprite
    sprite: Clothing/Head/Hats/ushanka.rsi
    layers:
    - state: icon
      map: [ "unfoldedLayer" ]
    - state: icon-up
      map: ["foldedLayer"]
      visible: false


- type: entity
  parent: ClothingHeadBase # Goob edit
  id: ClothingHeadHatVioletwizard
  name: "фіолетовий капелюх чарівника"
  description: "Дивний на вигляд фіолетовий капелюх, який, безсумнівно, належить справжньому магічному користувачеві."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/violetwizard.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/violetwizard.rsi
  - type: PressureProtection # DeltaV - Make real wizard clothes space proof
    highPressureMultiplier: 0.6
    lowPressureMultiplier: 1000
  - type: TemperatureProtection
    coefficient: 0.2

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatWarden
  name: "кашкет наглядача"
  description: "Відповідальність важка, як колюча корона." # DeltaV - changed description to remove THE LAW
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Head/Hats/warden.rsi # DeltaV - resprite
  - type: Clothing
    sprite: DeltaV/Clothing/Head/Hats/warden.rsi # DeltaV - resprite
  - type: StealTarget
    stealGroup: ClothingHeadHatWarden

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatWitch
  name: "капелюх відьми"
  description: "Відьомський капелюх." # Goob edit
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/witch.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/witch.rsi
  - type: Tag
    tags:
    - WhitelistChameleon
    - ClothMade
  - type: HideLayerClothing
    slots:
    - Hair

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatWizardFake
  name: "фальшивий капелюх чарівника"
  description: "На ньому блискітками написано ЧАРРІВНИК. Поставляється з крутою бородою."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/wizard_fake.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/wizard_fake.rsi

- type: entity
  abstract: true
  parent: [ClothingHeadBase]
  id: ClothingHeadHatWizardBase
  suffix: Wizard Clothes # Goobstation
  components:
  - type: WizardClothes
  - type: Tag # Goobstation
    tags:
    - WizardHat
    - ClothMade
    - WhitelistChameleon

- type: entity
  parent: ClothingHeadBase # Goob edit
  id: ClothingHeadHatWizard
  name: "капелюх чарівника"
  description: "Дивний на вигляд синій капелюх, який, безсумнівно, належить могутньому магічному користувачеві."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/wizardhat.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/wizardhat.rsi
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon
  - type: PressureProtection # DeltaV - Make real wizard clothes space proof
    highPressureMultiplier: 0.6
    lowPressureMultiplier: 1000
  - type: TemperatureProtection
    coefficient: 0.2

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatXmasCrown
  name: "різдвяна корона"
  description: "Щасливого Різдва!"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/xmascrown.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/xmascrown.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatTrucker
  name: "капелюх далекобійника"
  description: "У колишньому Чакса, тепер цей капелюх твій."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/truckershat.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/truckershat.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadPyjamaSyndicateBlack
  name: "синдикатська чорна піжамна шапка"
  description: "Для того, щоб зігріти твою зрадницьку голову."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/pyjamasyndicateblack.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/pyjamasyndicateblack.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadPyjamaSyndicatePink
  name: "синдикатська рожева піжамна шапка"
  description: "Для того, щоб зігріти твою зрадницьку голову."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/pyjamasyndicatepink.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/pyjamasyndicatepink.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadPyjamaSyndicateRed
  name: "синдикатська червона піжамна шапка"
  description: "Для того, щоб зігріти твою зрадницьку голову."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/pyjamasyndicatered.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/pyjamasyndicatered.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadPaperSack
  name: "паперова маска"
  description: "Паперовий мішок з грубими отворами для очей. Корисний для приховування своєї ідентичності.. або потворності."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/papersack.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/papersack.rsi
  - type: IdentityBlocker

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadPaperSackSmile
  name: "усміхнена паперова маска"
  description: "Паперовий мішок з грубими отворами для очей і намальованою посмішкою спереду. Зовсім не моторошно."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/papersacksmile.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/papersacksmile.rsi
  - type: IdentityBlocker

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadFishCap
  name: "рибальська шапка"
  description: "Жінки бояться мене. Риби бояться мене. Чоловіки відвертають від мене очі. Коли я йду, жоден звір не наважується вимовити ані звуку в моїй присутності. Я самотній на цій безплідній землі."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/fishcap.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/fishcap.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadNurseHat
  name: "шапочка медсестри"
  description: "Чомусь я відчуваю, що не повинен покидати це місце."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/nursehat.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/nursehat.rsi
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadRastaHat
  name: "раста капелюх"
  description: "Прямо біля пляжу, хлопче."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/rasta.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/rasta.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadSafari
  name: "капелюх-сафарі"
  description: "Захищає очі від сонця. Робить вас мішенню для місцевих."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/safarihat.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/safarihat.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatJester
  name: "блазнівський капелюх"
  description: "Капелюх з дзвіночками, щоб додати костюму веселості."
  components:
    - type: Sprite
      sprite: Clothing/Head/Hats/jester.rsi
    - type: Clothing
      sprite: Clothing/Head/Hats/jester.rsi

- type: entity
  parent: ClothingHeadHatJester
  id: ClothingHeadHatJesterAlt
  components:
    - type: Sprite
      sprite: Clothing/Head/Hats/jester2.rsi
    - type: Clothing
      sprite: Clothing/Head/Hats/jester2.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeretCmo
  name: "берет головного лікаря"
  description: "Бірюзовий берет з хрестом спереду. Його вигляд заспокоює і дає зрозуміти, що ти вилікуєшся."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/beret_cmo.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/beret_cmo.rsi
  - type: Tag
    tags:
    - ClothMade
    - HamsterWearable
    - WhitelistChameleon

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatPirateTricorn
  name: "піратський капелюх"
  description: "Йо-хо-хо і пляшку рому!"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/piratetricord.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/piratetricord.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatWatermelon
  name: "кавуновий шолом"
  description: "Недбало розрізану половинку кавуна, випотрошену зсередини, носити як шолом. Це може пом'якшити удар по голові."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/melon.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/melon.rsi
  - type: Armor
    modifiers:
      coefficients:
        Blunt: 0.95

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatHolyWatermelon
  name: "кавуновий ореол"
  description: "Святі небеса."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/holyhatmelon.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/holyhatmelon.rsi
  - type: Armor
    modifiers:
      coefficients:
        Caustic: 0.95

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatSyndie
  name: "капелюх синдикату"
  description: "Сувенірний капелюх від \"Syndieland\", їх виробництво вже закрито."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/syndiecap.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/syndiecap.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatSyndieMAA
  name: "капелюх майстра зброї"
  description: "Капелюх майстра зброї, виглядає страхітливо, сумніваюся, що вам сподобається спілкуватися з його власником..."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/syndiecap_maa.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/syndiecap_maa.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatTacticalMaidHeadband
  name: "тактична пов'язка для покоївки"
  description: "Червона пов'язка на голові - не уявляйте себе Рембо і не беріть до рук кілька кулеметів."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/tacticalmaidheadband.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/tacticalmaidheadband.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatHetmanHat
  name: "гетьманська шапка"
  description: "Із Запорозької Січі з любов'ю."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/hetman_hat.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/hetman_hat.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatMagician
  name: "капелюх фокусника"
  description: "Капелюх фокусника."
  components:
  - type: Icon
    sprite: Clothing/Head/Hats/magician.rsi
    state: "icon"
  - type: Sprite
    state: "icon"
    sprite: Clothing/Head/Hats/magician.rsi
  - type: Clothing
    clothingVisuals:
      head:
      - state: equipped-HELMET
        offset: "0, 0.12"
    sprite: Clothing/Head/Hats/magician.rsi
  - type: Item
    size: Small
    sprite: Clothing/Head/Hats/magician.rsi
    storedRotation: 0
  - type: Storage
    grid:
    - 0,0,0,0
  - type: UserInterface
    interfaces:
        enum.StorageUiKey.Key:
          type: StorageBoundUserInterface
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
  - type: Tag

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatCapcap
  name: "кеп-кеп"
  description: "Велика, стильна капітанська кепка."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/capcap.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/capcap.rsi
  - type: Tag
    tags:
    - ClothMade
    - WhitelistChameleon
    - HamsterWearable
  - type: SentienceTarget
    flavorKind: station-event-random-sentience-flavor-inanimate
    weight: 0.0002 # 5,000 times less likely than 1 regular animal
  - type: BlockMovement

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatCentcomcap
  name: "Кепка ЦентКома"
  description: "Екстравагантний, вигадливий кашкет Центрального Командувача."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/comcap.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/comcap.rsi
  - type: Tag
    tags:
    - ClothMade
    - WhitelistChameleon
    - HamsterWearable

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatGladiator
  name: "шолом гладіатора"
  description: "Захищає голову від суворого попелястого вітру та іграшкових списів."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/gladiator.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/gladiator.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatPartyRed
  name: "червоний капелюх для вечірок"
  description: "Поділіться радістю!"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/party_red.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/party_red.rsi
  - type: Tag
    tags:
      - WhitelistChameleon
      - HamsterWearable

- type: entity
  parent: ClothingHeadHatPartyRed
  id: ClothingHeadHatPartyYellow
  name: "жовтий капелюх для вечірок"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/party_yellow.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/party_yellow.rsi

- type: entity
  parent: ClothingHeadHatPartyRed
  id: ClothingHeadHatPartyGreen
  name: "зелений капелюх для вечірок"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/party_green.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/party_green.rsi

- type: entity
  parent: ClothingHeadHatPartyRed
  id: ClothingHeadHatPartyBlue
  name: "синій капелюх для вечірок"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/party_blue.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/party_blue.rsi

- type: entity
  parent: ClothingHeadHatPartyRed
  id: ClothingHeadHatPartyWaterCup
  name: "капелюх для вечірки з чашкою для води"
  description: "Не так повноцінно, як ви сподівалися..."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/party_water_cup.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/party_water_cup.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatGreyFlatcap
  name: "сірий кашкет"
  description: "Модний як для робітничого класу, так і для старого Дженкінса."
  components:
  - type: Tag
    tags:
    - BrimFlatcapGrey
  - type: Sprite
    sprite: Clothing/Head/Hats/greyflatcap.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/greyflatcap.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBrownFlatcap
  name: "коричневий кашкет"
  description: "Дурний клоун! Ти виставив мене в поганому світлі!"
  components:
  - type: Tag
    tags:
    - BrimFlatcapBrown
  - type: Sprite
    sprite: Clothing/Head/Hats/brownflatcap.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/brownflatcap.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatCowboyBrown
  name: "коричневий ковбойський капелюх"
  description: "Цей капелюх замалий для нас обох."
  components:
    - type: Sprite
      sprite: Clothing/Head/Hats/cowboyhatbrown.rsi
    - type: Clothing
      sprite: Clothing/Head/Hats/cowboyhatbrown.rsi
    - type: AddAccentClothing
      accent: ReplacementAccent
      replacement: cowboy

- type: entity
  parent: ClothingHeadHatCowboyBrown
  id: ClothingHeadHatCowboyBlack
  name: "чорний ковбойський капелюх"
  components:
    - type: Sprite
      sprite: Clothing/Head/Hats/cowboyhatblack.rsi
    - type: Clothing
      sprite: Clothing/Head/Hats/cowboyhatblack.rsi

- type: entity
  parent: ClothingHeadHatCowboyBrown
  id: ClothingHeadHatCowboyGrey
  name: "сірий ковбойський капелюх"
  components:
    - type: Sprite
      sprite: Clothing/Head/Hats/cowboyhatgrey.rsi
    - type: Clothing
      sprite: Clothing/Head/Hats/cowboyhatgrey.rsi

- type: entity
  parent: ClothingHeadHatCowboyBrown
  id: ClothingHeadHatCowboyRed
  name: "червоний ковбойський капелюх"
  components:
    - type: Sprite
      sprite: Clothing/Head/Hats/cowboyhatred.rsi
    - type: Clothing
      sprite: Clothing/Head/Hats/cowboyhatred.rsi

- type: entity
  parent: ClothingHeadHatCowboyBrown
  id: ClothingHeadHatCowboyWhite
  name: "білий ковбойський капелюх"
  components:
    - type: Sprite
      sprite: Clothing/Head/Hats/cowboyhatwhite.rsi
    - type: Clothing
      sprite: Clothing/Head/Hats/cowboyhatwhite.rsi

- type: entity
  parent: ClothingHeadHatCowboyBrown
  id: ClothingHeadHatCowboyBountyHunter
  name: "ковбойський капелюх мисливця за головами"
  components:
    - type: Sprite
      sprite: Clothing/Head/Hats/cowboyhatbountyhunter.rsi
    - type: Clothing
      sprite: Clothing/Head/Hats/cowboyhatbountyhunter.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatStrawHat
  name: "солом'яний капелюх"
  description: "Модний капелюх для спекотних днів! Не рекомендується носити біля вогню."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/straw_hat.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/straw_hat.rsi
  - type: Construction
    graph: StrawHat
    node: strawhat
  - type: Tag
    tags:
    - HamsterWearable
  - type: Flammable
    fireSpread: true
    canResistFire: false
    alwaysCombustible: true
    canExtinguish: true
    damage:
      types:
        Heat: 1
  - type: Damageable
    damageModifierSet: Wood
  - type: Appearance
  - type: FireVisuals
    sprite: Effects/fire.rsi
    normalState: fire
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 15
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
            Ash:
              min: 1
              max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeretMedic
  name: "берет лікаря"
  description: "Білий берет, який заохочує бути чистим."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/beret_medic.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/beret_medic.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatFlatcapBartenderNanotrasen
  name: "кашкет бармена Нанотрейзен"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/flatcap_nt.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/flatcap_nt.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatFlatcapBartenderIdris
  name: "кашкет бармена Індріс інкорпорейтед"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/flatcap_idris.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/flatcap_idris.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatFlatcapBartenderOrion
  name: "кашкет бармена Оріон Експрес"
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/flatcap_orion.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/flatcap_orion.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeanie
  name: "шапочка"
  description: "Зимова шапка без козирка, що обіймає голову. Ця затісна."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/beanie.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/beanie.rsi
  - type: TemperatureProtection
    coefficient: 0.5

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeanieWinter
  name: "зимова шапка"
  description: "Зимова шапка без козирка, що обіймає голову. Ця шапка має клапани, які закривають вуха."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/beanie_winter.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/beanie_winter.rsi
  - type: TemperatureProtection
    coefficient: 0.5

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeanieSubmariner
  name: "шапка підводника"
  description: "Дизайн шапки, що щільно облягає голову, популярний серед працівників підводних станцій."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/beanie_submariner.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/beanie_submariner.rsi
  - type: TemperatureProtection
    coefficient: 0.5

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeanieTight
  name: "вузьку шапку"
  description: "Досить щільна шапка, яка приховує волосся."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/beanie_tight.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/beanie_tight.rsi
  - type: TemperatureProtection
    coefficient: 0.5
  - type: HideLayerClothing
    slots:
    - Hair

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatSlouch
  name: "крислатий капелюх"
  description: "Широкий крислатий капелюх, складений так, щоб утворилася сутулість."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/slouch.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/slouch.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatSunVisor
  name: "сонцезахисний козирок"
  description: "Стильний козирок, призначений для захисту від різких сонячних променів."
  components:
  - type: Sprite
    sprite: Clothing/Head/Hats/sun_visor.rsi
  - type: Clothing
    sprite: Clothing/Head/Hats/sun_visor.rsi

# Nuclear 14 hats
- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatBeanieGreen
  name: "зелена шапочка"
  description: "Темно-зелена шапка."
  components:
  - type: Sprite
    sprite: _Nuclear14/Clothing/Head/FalloutHats/beanie.rsi
  - type: Clothing
    sprite: _Nuclear14/Clothing/Head/FalloutHats/beanie.rsi

- type: entity
  parent: ClothingHeadBase
  id: ClothingHeadHatMountie
  name: "капелюх"
  description: "Плаский капелюх, виготовлений на 100% з хутряного фетру."
  components:
  - type: Sprite
    sprite: _Nuclear14/Clothing/Head/FalloutHats/mountiehat.rsi
  - type: Clothing
    sprite: _Nuclear14/Clothing/Head/FalloutHats/mountiehat.rsi
