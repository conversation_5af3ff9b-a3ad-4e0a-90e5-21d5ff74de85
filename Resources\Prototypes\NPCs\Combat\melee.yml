# -- Melee --
# Selects a target in melee and tries to attack it.
- type: htnCompound
  id: MeleeCombatCompound
  branches:
    - tasks:
        - !type:HTNPrimitiveTask
          operator: !type:UtilityOperator
            proto: NearbyMeleeTargets
        - !type:HTNCompoundTask
          task: BeforeMeleeAttackTargetCompound

- type: htnCompound
  id: BeforeMeleeAttackTargetCompound
  branches:
    - preconditions:
        - !type:BuckledPrecondition
          isBuckled: true
      tasks:
        - !type:HTNPrimitiveTask
          operator: !type:UnbuckleOperator
            shutdownState: TaskFinished

    - preconditions:
        - !type:PulledPrecondition
          isPulled: true
      tasks:
        - !type:HTNPrimitiveTask
          operator: !type:UnPullOperator
            shutdownState: TaskFinished

    - preconditions:
        - !type:InContainerPrecondition
          isInContainer: true
      tasks:
        - !type:HTNCompoundTask
          task: EscapeCompound

    # Pickup weapon if we don't have one.
    - preconditions:
        - !type:ActiveHandComponentPrecondition
            components:
              # Just serializer things
              - type: MeleeWeapon
                damage:
                  types:
                    Blunt: 0
            invert: true
      tasks:
        - !type:HTNCompoundTask
          task: PickupMeleeCompound

    # Goobstation - wield an item if it's wieldable
    - preconditions:
        - !type:ActiveHandComponentPrecondition
          components:
          - type: Wieldable
      tasks:
        - !type:HTNPrimitiveTask
          preconditions:
          - !type:UnwieldedEntityInHandsPrecondition
          operator: !type:WieldOperator

    # Melee combat (unarmed or otherwise)
    - tasks:
        - !type:HTNCompoundTask
          task: MeleeAttackTargetCompound

- type: htnCompound
  id: RatServantCombatCompound
  branches:
    - preconditions:
        - !type:ActiveHandComponentPrecondition
          components:
            - type: MeleeWeapon
              damage:
                types:
                  Blunt: 0
          invert: true
      tasks:
        - !type:HTNCompoundTask
          task: PickupMeleeCompound

    - tasks:
        - !type:HTNPrimitiveTask
          operator: !type:UtilityOperator
            proto: OrderedTargets
        - !type:HTNCompoundTask
          task: MeleeAttackOrderedTargetCompound

- type: htnCompound
  id: PickupMeleeCompound
  branches:
    - tasks:
        - !type:HTNPrimitiveTask
          operator: !type:UtilityOperator
            proto: NearbyMeleeWeapons

        - !type:HTNPrimitiveTask
          operator: !type:MoveToOperator

        - !type:HTNPrimitiveTask
          preconditions:
            - !type:ActiveHandFreePrecondition
            - !type:TargetInRangePrecondition
              targetKey: Target
              rangeKey: InteractRange
          operator: !type:InteractWithOperator
            targetKey: Target

# Tries to melee attack our target.
- type: htnCompound
  id: MeleeAttackTargetCompound
  branches:
    - preconditions:
      - !type:KeyExistsPrecondition
        key: Target
      tasks:
        - !type:HTNPrimitiveTask
          operator: !type:MoveToOperator
            shutdownState: PlanFinished
            pathfindInPlanning: true
            removeKeyOnFinish: false
            targetKey: TargetCoordinates
            pathfindKey: TargetPathfind
            rangeKey: MeleeRange
        - !type:HTNPrimitiveTask
          operator: !type:JukeOperator
            jukeType: Away
        - !type:HTNPrimitiveTask
          operator: !type:MeleeOperator
            targetKey: Target
          preconditions:
            - !type:KeyExistsPrecondition
              key: Target
            - !type:TargetInRangePrecondition
              targetKey: Target
              rangeKey: MeleeRange
          services:
            - !type:UtilityService
              id: MeleeService
              proto: NearbyMeleeTargets
              key: Target

- type: htnCompound
  id: EscapeCompound
  branches:
  - tasks:
    - !type:HTNPrimitiveTask
      operator: !type:ContainerOperator
        targetKey: Target
        shutdownState: TaskFinished
    - !type:HTNPrimitiveTask
      operator: !type:EscapeOperator
        targetKey: Target
      preconditions:
      - !type:InContainerPrecondition
        isInContainer: true

- type: htnCompound
  id: MeleeAttackOrderedTargetCompound
  branches:
    - preconditions:
      - !type:KeyExistsPrecondition
        key: Target
      tasks:
        - !type:HTNPrimitiveTask
          operator: !type:MoveToOperator
            shutdownState: PlanFinished
            pathfindInPlanning: true
            removeKeyOnFinish: false
            targetKey: TargetCoordinates
            pathfindKey: TargetPathfind
            rangeKey: MeleeRange
        - !type:HTNPrimitiveTask
          operator: !type:JukeOperator
            jukeType: Away
        - !type:HTNPrimitiveTask
          operator: !type:MeleeOperator
            targetKey: Target
          preconditions:
            - !type:KeyExistsPrecondition
              key: Target
            - !type:TargetInRangePrecondition
              targetKey: Target
              rangeKey: MeleeRange
          services:
            - !type:UtilityService
              id: MeleeService
              proto: OrderedTargets
              key: Target
