- type: entity
  parent: MobObserver
  id: AdminObserver
  name: "адміністратор-спостерігач"
  categories: [ HideSpawnMenu ]
  components:
  - type: Eye
    visMask:
      - TelegnosticProjection
      - PsionicInvisibility
      - Ghost
      - Normal
      - Ethereal
  - type: ContentEye
    maxZoom: 8.916104, 8.916104
  - type: Tag
    tags:
    - InstantDoAfters
    - CanPilot
    - BypassInteractionRangeChecks
    - BypassDropChecks
    - IgnoreBalanceChecks # Pirate banking
    - NoConsoleSound
  - type: Input
    context: "aghost"
  - type: Ghost
    canInteract: true
  - type: GhostHearing
  - type: Hands
  - type: ComplexInteraction
  - type: Puller
    needsHands: false # Tail Drag
    pushAcceleration: 1000000 # Will still be capped in max speed
    maxPushRange: 20
  - type: CombatMode
  - type: Physics
    ignorePaused: true
    bodyType: Kinematic
  - type: Body
    prototype: Aghost
    thermalVisibility: false
  - type: Access
    groups:
    - AllAccess
    - Silicon
    tags:
    - NuclearOperative
    - SyndicateAgent
    - CentralCommand
  - type: UserInterface
    interfaces:
      enum.SolarControlConsoleUiKey.Key:
        type: SolarControlConsoleBoundUserInterface
      enum.CommunicationsConsoleUiKey.Key:
        type: CommunicationsConsoleBoundUserInterface
      enum.RadarConsoleUiKey.Key:
        type: RadarConsoleBoundUserInterface
      enum.CargoConsoleUiKey.Orders:
        type: CargoOrderConsoleBoundUserInterface
      enum.CrewMonitoringUIKey.Key:
        type: CrewMonitoringBoundUserInterface
      enum.GeneralStationRecordConsoleKey.Key:
      # who the fuck named this bruh
        type: GeneralStationRecordConsoleBoundUserInterface
  - type: IntrinsicUI
    uis:
      enum.SolarControlConsoleUiKey.Key:
        toggleAction: ActionAGhostShowSolar
      enum.CommunicationsConsoleUiKey.Key:
        toggleAction: ActionAGhostShowCommunications
      enum.RadarConsoleUiKey.Key:
        toggleAction: ActionAGhostShowRadar
      enum.CargoConsoleUiKey.Orders:
        toggleAction: ActionAGhostShowCargo
      enum.CrewMonitoringUIKey.Key:
        toggleAction: ActionAGhostShowCrewMonitoring
      enum.GeneralStationRecordConsoleKey.Key:
        toggleAction: ActionAGhostShowStationRecords
  - type: SolarControlConsole # look ma i AM the computer!
  - type: CommunicationsConsole
    title: comms-console-announcement-title-centcom
    color: "#228b22"
  - type: RadarConsole
    followEntity: true
  - type: CargoOrderConsole
  - type: BankClient
  - type: CrewMonitoringConsole
  - type: GeneralStationRecordConsole
    canDeleteEntries: true
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: CrewMonitor
    transmitFrequencyId: ShuttleTimer
  - type: WirelessNetworkConnection
    range: 500
  - type: StationLimitedNetwork
  - type: Thieving
    stripTimeMultiplier: 0
    ignoreStripHidden: true
  - type: Stripping
  - type: SolutionScanner
  - type: IgnoreUIRange
  - type: ShowAntagIcons
  - type: Inventory
    templateId: aghost
  - type: InventorySlots
  - type: Loadout
    prototypes: [ MobAghostGear ]
  - type: SupermatterImmune
  - type: BypassInteractionChecks
  - type: PsionicInsulation

- type: entity
  id: ActionAGhostShowSolar
  name: "Інтерфейс керування сонячною батареєю"
  description: "Перегляд інтерфейсу керування сонячною батареєю."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: { sprite: Structures/Machines/parts.rsi, state: box_0 }
    iconOn: Structures/Machines/parts.rsi/box_2.png
    keywords: [ "AI", "console", "interface" ]
    priority: -10
    event: !type:ToggleIntrinsicUIEvent { key: enum.SolarControlConsoleUiKey.Key }

- type: entity
  id: ActionAGhostShowCommunications
  name: "Інтерфейс зв'язку"
  description: "Перегляд інтерфейсу зв'язку."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: { sprite: Interface/Actions/actions_ai.rsi, state: comms_console }
    iconOn: Interface/Actions/actions_ai.rsi/comms_console.png
    keywords: [ "AI", "console", "interface" ]
    priority: -4
    event: !type:ToggleIntrinsicUIEvent { key: enum.CommunicationsConsoleUiKey.Key }

- type: entity
  id: ActionAGhostShowRadar
  name: "Інтерфейс масового сканера"
  description: "Перегляд інтерфейсу масового сканера."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: { sprite: Interface/Actions/actions_ai.rsi, state: mass_scanner }
    iconOn: Interface/Actions/actions_ai.rsi/mass_scanner.png
    keywords: [ "AI", "console", "interface" ]
    priority: -7
    event: !type:ToggleIntrinsicUIEvent { key: enum.RadarConsoleUiKey.Key }

- type: entity
  id: ActionAGhostShowCargo
  name: "Інтерфейс замовлення вантажу"
  description: "Переглянути інтерфейс замовлення вантажу."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: { sprite: Structures/Machines/parts.rsi, state: box_0 }
    iconOn: Structures/Machines/parts.rsi/box_2.png
    keywords: [ "AI", "console", "interface" ]
    priority: -10
    event: !type:ToggleIntrinsicUIEvent { key: enum.CargoConsoleUiKey.Orders }

- type: entity
  id: ActionAGhostShowCrewMonitoring
  name: "Інтерфейс моніторингу екіпажу"
  description: "Перегляд інтерфейсу моніторингу екіпажу."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: { sprite: Interface/Actions/actions_ai.rsi, state: crew_monitor }
    iconOn: Interface/Actions/actions_ai.rsi/crew_monitor.png
    keywords: [ "AI", "console", "interface" ]
    priority: -9
    event: !type:ToggleIntrinsicUIEvent { key: enum.CrewMonitoringUIKey.Key }

- type: entity
  id: ActionAGhostShowStationRecords
  name: "Інтерфейс записів станції"
  description: "Перегляд записів станції Інтерфейс"
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon: { sprite: Interface/Actions/actions_ai.rsi, state: station_records }
    iconOn: Interface/Actions/actions_ai.rsi/station_records.png
    keywords: [ "AI", "console", "interface" ]
    priority: -8
    event: !type:ToggleIntrinsicUIEvent { key: enum.GeneralStationRecordConsoleKey.Key }
