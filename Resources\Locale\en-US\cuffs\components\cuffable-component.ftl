cuffable-component-cannot-interact-message = You can't do that!
cuffable-component-cannot-remove-cuffs-too-far-message = You are too far away to remove the restraints.

cuffable-component-start-uncuffing-self = You start to painfully wriggle out of your restraints.
cuffable-component-start-uncuffing-observer = {$user} starts unrestraining {$target}!
cuffable-component-start-uncuffing-target-message = You start unrestraining {$targetName}.
cuffable-component-start-uncuffing-by-other-message = {$otherName} starts unrestraining you!

cuffable-component-remove-cuffs-success-message = You successfully remove the restraints.
cuffable-component-remove-cuffs-by-other-success-message = {$otherName} unrestrains your hands.
cuffable-component-remove-cuffs-to-other-partial-success-message = You successfully remove the restraints. {$cuffedHandCount} of {$otherName}'s hands remain restrained.
cuffable-component-remove-cuffs-by-other-partial-success-message = {$otherName} removes your restraints. {$cuffedHandCount} of your hands remain restrained.
cuffable-component-remove-cuffs-partial-success-message = You successfully remove the restraints. {$cuffedHandCount} of your hands remain restrained.
cuffable-component-remove-cuffs-fail-message = You fail to remove the restraints.

# UnrestrainVerb
uncuff-verb-get-data-text = Unrestrain
