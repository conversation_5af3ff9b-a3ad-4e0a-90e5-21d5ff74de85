# Barrels
# Base
- type: entity
  parent: BaseStructureDynamic
  id: BaseBarrel
  name: "бочка"
  description: "Ця бочка виглядає так, ніби в ній щось може бути."
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Structures/Storage/barrels.rsi
    state: blue-closed
    netsync: false
    noRot: true
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
            bounds: "-0.2,-0.2,0.2,0.45"
        density: 50
        mask:
        - MachineMask
        layer:
        - WallLayer
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTypeTrigger
        damageType: Heat
        damage: 5
      behaviors:
      - !type:SolutionExplosionBehavior
        solution: barrel
    - trigger:
        !type:DamageTypeTrigger
        damageType: Piercing
        damage: 5
      behaviors:
      - !type:SolutionExplosionBehavior
        solution: barrel
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:SpillBehavior
        solution: barrel
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:DoActsBehavior
        acts: ["Destruction"]

# Base Open
- type: entity
  parent: BaseBarrel
  id: BaseBarrelOpen
  suffix: open
  categories: [ HideSpawnMenu ]
  components:
  - type: SolutionContainerManager
    solutions:
      barrel:
        maxVol: 500
  - type: DrainableSolution
    solution: barrel
  - type: ReagentTank

# Closed
- type: entity
  parent: BaseBarrel
  id: BlackBarrel
  name: "чорна бочка"
  description: "Потерта чорна бочка. Етикетка відірвана."
  components:
  - type: Sprite
    sprite: Structures/Storage/barrels.rsi
    state: black-closed

- type: entity
  parent: BaseBarrel
  id: BlueBarrel
  name: "синя бочка"
  description: "Синя бочка з попереджувальним знаком. Може, в ній вода?"
  components:
  - type: Sprite
    sprite: Structures/Storage/barrels.rsi
    state: blue-closed

- type: entity
  parent: BaseBarrel
  id: RedBarrel
  name: "червона бочка"
  description: "Червона бочка з попереджувальним знаком про вибухонебезпечність. Будьте обережні."
  components:
  - type: Sprite
    sprite: Structures/Storage/barrels.rsi
    state: red-closed
  - type: Explosive
    explosionType: Default
    totalIntensity: 120 # ~ 5 tile radius

- type: entity
  parent: BaseBarrel
  id: YellowBarrel
  name: "жовта бочка"
  description: "Жовта бочка з попереджувальним знаком радіації. Будьте обережні."
  components:
  - type: Sprite
    sprite: Structures/Storage/barrels.rsi
    state: yellow-closed
  - type: RadiationSource
    intensity: 2
    slope: 1

# Open
- type: entity
  parent: BaseBarrelOpen
  id: BlackBarrelOpen
  suffix: open
  name: "чорна бочка"
  description: "Потерта чорна бочка. Етикетка відірвана. Кришка знята і можна зазирнути всередину."
  components:
  - type: Sprite
    sprite: Structures/Storage/barrels.rsi
    state: black-open

- type: entity
  parent: BaseBarrelOpen
  id: BlueBarrelOpen
  suffix: open
  name: "синя бочка"
  description: "Синя бочка з попереджувальним знаком. Кришка знята, і можна зазирнути всередину."
  components:
  - type: Sprite
    sprite: Structures/Storage/barrels.rsi
    state: blue-open

- type: entity
  parent: BaseBarrelOpen
  id: RedBarrelOpen
  suffix: open
  name: "червона бочка"
  description: "Червона бочка з попереджувальним знаком про вибухонебезпечність. Кришка знята, і можна зазирнути всередину."
  components:
  - type: Sprite
    sprite: Structures/Storage/barrels.rsi
    state: red-open

- type: entity
  parent: BaseBarrelOpen
  id: YellowBarrelOpen
  suffix: open
  name: "жовта бочка"
  description: "Жовта бочка з попереджувальним знаком радіаційної небезпеки. Кришка знята, і ви можете зазирнути всередину, але це все одно змушує вас смикатися."
  components:
  - type: Sprite
    sprite: Structures/Storage/barrels.rsi
    state: yellow-open
  - type: RadiationSource
    intensity: 1
    slope: 1

# Full barrels
- type: entity
  parent: BlackBarrelOpen
  id: BlackBarrelFull
  suffix: full
  description: "Пошарпана чорна бочка. Здається, вона повна якоїсь темної рідини."
  components:
  - type: Sprite
    sprite: Structures/Storage/barrels.rsi
    state: black-full
    # TODO - fill with some sort of waste product? Maybe just dirty water.

- type: entity
  parent: RedBarrelOpen
  id: RedBarrelFull
  suffix: full
  description: "Червона бочка з попереджувальним знаком про вибухонебезпечність. Усередині золотиста рідина."
  components:
  - type: Sprite
    sprite: Structures/Storage/barrels.rsi
    state: red-full
  - type: SolutionContainerManager
    solutions:
      barrel:
        reagents:
        - ReagentId: WeldingFuel
          Quantity: 500
  - type: DamageOnToolInteract
    tools: Welding
    weldingDamage:
      types:
        Heat: 10
  - type: Explosive
    explosionType: Default
    totalIntensity: 120 # ~ 5 tile radius

- type: entity
  parent: YellowBarrelOpen
  id: YellowBarrelFull
  suffix: full
  description: "Жовта бочка з попереджувальним знаком радіаційної небезпеки. Там видно слиз, що світиться."
  components:
  - type: Sprite
    sprite: Structures/Storage/barrels.rsi
    state: yellow-full
  - type: RadiationSource
    intensity: 3
    slope: 1
  - type: PointLight
    netsync: false
    radius: 1.5
    energy: 1.6
    color: "#3db83b"
    castShadows: false
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 1000
      behaviors:
      - !type:SpillBehavior
        solution: barrel
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:DoActsBehavior
        acts: ["Destruction"]
    # TODO - fill with some sort of radioactive waste reagent.

# Burning Barrels
- type: entity
  parent: BaseStructureDynamic
  id: BurningBarrel
  name: "обгоріла бочка"
  description: "Ця бочка виглядає так, ніби в ній колись була пожежа."
  components:
  - type: Sprite
    sprite: Structures/Storage/burningbarrel.rsi
    state: burnbarrel
    netsync: false
    noRot: true
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
            radius: 0.2
        density: 50
        mask:
        - MachineMask
        layer:
        - MidImpassable
        - LowImpassable

- type: entity
  parent: BurningBarrel
  id: BurningBarrelLit
  name: "палаюча бочка"
  description: "В бочці щось тліє. Хрустить"
  components:
  - type: Sprite
    sprite: Structures/Storage/burningbarrel.rsi
    state: burnbarrel_lit
    netsync: false
  - type: PointLight
    color: "#E25822"
    radius: 1.0
    energy: 5.0
    netsync: false
  - type: LightBehaviour
    behaviours:
      - !type:RandomizeBehaviour # immediately make it bright and flickery
        id: burnbarrel_lit
        interpolate: Nearest
        minDuration: 0.02
        maxDuration: 0.06
        startValue: 6.0
        endValue: 9.0
        property: Energy
        isLooped: true
