<Control xmlns="https://spacestation14.io"
         xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
         xmlns:graphics="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client">
    <!-- Primary container -->
    <BoxContainer Orientation="Vertical" HorizontalExpand="True">
        <!-- Markings -->
        <BoxContainer Orientation="Vertical" HorizontalExpand="True">
            <BoxContainer HorizontalExpand="True">
                <OptionButton Name="CMarkingCategoryButton" StyleClasses="OpenBoth" />
                <Label Name="CMarkingPoints" Text="uwu" Margin="8 0" />
                <LineEdit Name="CMarkingSearch" PlaceHolder="{Loc 'markings-search'}" HorizontalExpand="True" />
                <Button Name="ResetButton" Text="{Loc 'ui-options-bind-reset'}" StyleClasses="OpenLeft" />
            </BoxContainer>

            <PanelContainer>
                <PanelContainer.PanelOverride>
                    <graphics:StyleBoxFlat BackgroundColor="#1B1B1E" />
                </PanelContainer.PanelOverride>

                <ScrollContainer HorizontalExpand="True" MinSize="300 300">
                    <BoxContainer Name="Markings" Orientation="Vertical" HorizontalExpand="True" MinSize="300 300" />
                </ScrollContainer>
            </PanelContainer>
        </BoxContainer>

        <!-- Colors -->
        <BoxContainer Name="CMarkingColors" Margin="0 4 0 0" Orientation="Vertical" Visible="False" />
    </BoxContainer>
</Control>
