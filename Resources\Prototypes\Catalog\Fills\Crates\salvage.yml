- type: entity
  id: CrateSalvageEquipment
  name: "ящик утилізаційного обладнання"
  description: "Для сміливців."
  suffix: Filled
  parent: CrateGenericSteel
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterHardsuitSalvage
      - id: ClothingMaskBreath
      - id: OxygenTankFilled
      - id: FireExtinguisher
      - id: ClothingShoesBootsMag
      - id: HandHeldMassScanner
      - id: Pickaxe
      - id: Welder
      - id: Wrench
      - id: Screwdriver
      - id: Crowbar
      - id: Wirecutter
      - id: ClothingBeltUtility
      - id: OreBag
      - id: ClothingBeltSalvageWebbing
      - id: ClothingNeckSalvager # DeltaV - salvage cloak
        prob: 0.8

- type: entity
  id: CrateSalvageAssortedGoodies
  suffix: Filled, Salvage Random
  categories: [ HideSpawnMenu ] # You should use SalvageMaterialCrateSpawner instead
  parent: CrateGenericSteel
  components:
  - type: StorageFill
    contents:
        # Normal (10%)
      - id: OxygenTankFilled
        prob: 0.1
      - id: SheetPlasma
        prob: 0.1
      - id: IngotGold
        prob: 0.1
      - id: IngotSilver
        prob: 0.1
      - id: SheetPlasma
        prob: 0.1
      - id: WelderIndustrialAdvanced
        prob: 0.1
      - id: ResearchDisk
        prob: 0.1
      - id: SheetUranium
        prob: 0.1
        #  - Service
      - id: CrayonBox
        prob: 0.1
        #  - Medical
      - id: MedkitFilled
        prob: 0.1
      - id: BoxSyringe
        prob: 0.1
      - id: BoxBeaker
        prob: 0.1
        #  - Heh
      - id: SalvageHumanCorpse
        prob: 0.1
        # Interesting (1%)
        #  - Ammo
      - id: MagazineBoxMagnum
        prob: 0.01
      - id: ResearchDisk10000
        prob: 0.01
        # Just no (0.1%)
        #  - Working guns
      - id: WeaponRevolverDeckard
        prob: 0.001
      - id: WeaponRevolverInspector
        prob: 0.001
      # DeltaV - .38 special revolver - Adds the .38 Special Revolver 'Lucky 37' to the salvage potential loot
      - id: WeaponRevolverLucky
        prob: 0.001
      # End of modified code
      - id: ClothingShoesBootsMagBlinding
        prob: 0.001
        #  - Skub
      - id: Skub
        prob: 0.001
      - id: ClothingHeadHatCatEars
        prob: 0.01
      - id: ClothingHeadHatDogEars
        prob: 0.01
        # TRAITOR EQUIPMENT (0.01%)
      - id: Telecrystal10
        prob: 0.0001
      - id: WeaponRevolverPython
        prob: 0.0001
      - id: WeaponRevolverMateba
        prob: 0.0001

- type: entity
  parent: CrateGenericSteel
  id: CratePartsT3
  name: "ящик з деталями 3-го рівня"
  description: "Містить 5 випадкових деталей 3-го рівня для модернізації машин."
  # TODO add contents.
  #components:
  #- type: StorageFill
  #  contents:
  #   - id: SalvagePartsT3Spawner
  #    amount: 5

- type: entity
  parent: CrateGenericSteel
  id: CratePartsT3T4
  name: "ящик з деталями 3-го/4-го рівня"
  description: "Містить 5 випадкових деталей 3 або 4 рівня для модернізації машин."
  # TODO add contents.
  #components:
  # type: StorageFill
  #  contents:
  #  - id: SalvagePartsT3T4Spawner
  #     amount: 5

- type: entity
  parent: CrateGenericSteel
  id: CratePartsT4
  name: "ящик з деталями 4-го рівня"
  description: "Містить 5 випадкових деталей 4-го рівня для модернізації машин."
  # TODO add contents.
  #components:
  #- type: StorageFill
  #  contents:
  #  - id: SalvagePartsT4Spawner
  #    amount: 5
