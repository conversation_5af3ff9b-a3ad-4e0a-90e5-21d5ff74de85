using Content.Shared._Impstation.Thaven;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Utility;

namespace Content.Client._Impstation.Thaven.Eui;

[GenerateTypedNameReferences]
public sealed partial class MoodContainer : BoxContainer
{
    public MoodContainer(ThavenMood? mood = null)
    {
        RobustXamlLoader.Load(this);

        if (mood != null)
        {
            ThavenMoodTitle.Text = mood.GetLocName();
            ThavenMoodContent.TextRope = new Rope.Leaf(mood.GetLocDesc());
        }
    }
}
