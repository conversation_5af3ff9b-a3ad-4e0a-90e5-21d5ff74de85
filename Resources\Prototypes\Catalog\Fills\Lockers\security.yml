- type: entity
  id: LockerWardenFilledHardsuit
  suffix: Filled, Hardsuit
  parent: LockerWarden
  components:
  - type: StorageFill
    contents:
      - id: FlashlightSeclite
      - id: WeaponDisabler
        prob: 0.3
      - id: ClothingBeltSecurityFilled
      - id: ClothingHandsGlovesKravMaga # Goobstation - Martial Arts
      - id: Flash
      - id: ClothingEyesGlassesSunglasses
      - id: ClothingHeadsetAltSecurity
      - id: ClothingHandsGlovesCombat
      - id: ClothingShoesBootsJack
      - id: ClothingOuterCoatWarden
      - id: ClothingOuterWinterWarden
      - id: RubberStampWarden
      - id: DoorRemoteArmory
      - id: ClothingOuterHardsuitCombatWarden # DeltaV - ClothingOuterHardsuitWarden replaced in favour of warden's riot combat hardsuit.
      - id: HoloprojectorSecurity
      - id: ClothingEyesHudSecurity
      - id: BoxPDAPrisoner # Delta-V
      - id: ClothingShoesBootsWinterWarden #Delta V: Add departmental winter boots
      - id: BoxEncryptionKeyPrisoner #Delta-V
      - id: BoxPrisonerHeadset
      - id: LunchboxSecurityFilledRandom # Delta-v Lunchboxes!
        prob: 0.3
      - id: HoverbikeNfsdFlatpack #PIRATE

- type: entity
  id: LockerWardenFilled
  suffix: Filled
  parent: LockerWarden
  components:
  - type: StorageFill
    contents:
      - id: FlashlightSeclite
      - id: WeaponDisabler
        prob: 0.3
      - id: ClothingBeltSecurityFilled
      - id: ClothingHandsGlovesKravMaga # Goobstation - Martial Arts
      - id: Flash
      - id: ClothingEyesGlassesSunglasses
      - id: ClothingHeadsetAltSecurity
      - id: ClothingHandsGlovesCombat
      - id: ClothingShoesBootsJack
      - id: ClothingOuterCoatWarden
      - id: ClothingOuterWinterWarden
      - id: RubberStampWarden
      - id: DoorRemoteArmory
      - id: HoloprojectorSecurity
      - id: ClothingEyesHudSecurity
      - id: BoxPDAPrisoner # Delta-V
      - id: ClothingShoesBootsWinterWarden #Delta V: Add departmental winter boots
      - id: BoxEncryptionKeyPrisoner #Delta-V
      - id: BoxPrisonerHeadset
      - id: LunchboxSecurityFilledRandom # Delta-v Lunchboxes!
        prob: 0.3
      - id: HoverbikeNfsdFlatpack #PIRATE

- type: entity
  id: LockerSecurityFilled
  suffix: Filled
  parent: LockerSecurity
  components:
  - type: StorageFill
    contents:
      - id: FlashlightSeclite
        prob: 0.8
      - id: ClothingUniformJumpsuitSecGrey
        prob: 0.3
      - id: ClothingUniformJumpsuitSecBlue # DeltaV - consistency with other sec uniforms
        prob: 0.3
      - id: ClothingUniformJumpsuitSec # DeltaV - consistency with other sec uniforms
        prob: 0.3
      - id: ClothingUniformJumpskirtSecGrey # DeltaV - consistency with other sec uniforms
        prob: 0.3
      - id: ClothingUniformJumpskirtSecBlue # DeltaV - consistency with other sec uniforms
        prob: 0.3
      - id: ClothingUniformJumpskirtSec # DeltaV - consistency with other sec uniforms
        prob: 0.3
      - id: ClothingHeadHelmetBasic
      - id: ClothingHeadHelmetInsulated # Nyanotrasen - Insulative headgear
        prob: 0.4
      - id: ClothingOuterArmorPlateCarrier # DeltaV - ClothingOuterArmorBasic replaced in favour of plate carrier and stabproof vest
      - id: ClothingOuterArmorDuraVest
      - id: ClothingOuterStasecSweater # DeltaV - in case the officers get cold :3
        prob: 0.75
      - id: ClothingUniformJumpsuitSecFormal # DeltaV - emergency parade time, Central's coming to visit
        prob: 0.25
      - id: ClothingBeltSecurityFilled
      - id: ClothingBeltCorporateJudo # Goobstation - Martial Arts
      - id: Flash
        prob: 0.5
      - id: ClothingEyesGlassesSunglasses
      - id: ClothingHeadsetAltSecurityRegular # Goobstation
      - id: ClothingHandsGlovesColorBlack
      - id: ClothingShoesBootsJack
      - id: WeaponMeleeNeedle
        prob: 0.1
      - id: ClothingEyesHudSecurity
      - id: HoloprojectorSecurity
        prob: 0.6
      - id: LunchboxSecurityFilledRandom # Delta-v Lunchboxes!
        prob: 0.3
      - id: Bodycam #PIRATE

- type: entity
  id: LockerBrigmedicFilled
  suffix: Corpsman, Filled # DeltaV - rename brigmedic to corpsman
  parent: LockerBrigmedic
  components:
  - type: StorageFill
    contents:
      - id: ClothingEyesGlassesCorpsman # DeltaV - Corpsman Glasses
      - id: WeaponDisabler
      - id: TrackingImplanter
        amount: 2
##      - id: ClothingOuterHardsuitCombatCorpsman # DeltaV - ClothingOuterHardsuitBrigmedic replaced in favour of corpsman's combat hardsuit; removing from standard filled locker to place in hardsuit filled locker.
      - id: BoxSterileMask
      - id: ClothingHeadHatBeretCorpsman # DeltaV - ClothingHeadHatBeretBrigmedic replaced in favour of corpsman beret.
##      - id: ClothingOuterCoatAMG # DeltaV - removed until I can resprite it or replace it.
      - id: ClothingUniformJumpsuitBrigmedic
      - id: ClothingUniformJumpskirtBrigmedic
##      - id: ClothingUniformJumpskirtOfLife # DeltaV - nah
##        prob: 0.1
      - id: HandheldGPSBasic # Delta V - added it for tracking the implant tracker pop up.
      - id: MedkitFilled
      - id: MedkitCombatFilled
        prob: 0.6
      - id: MedkitAdvancedFilled
        prob: 0.4
      - id: MedkitOxygenFilled
        prob: 0.3
      - id: MedkitBruteFilled
        prob: 0.3
      - id: MedkitToxinFilled
        prob: 0.3
      - id: MedkitBurnFilled
        prob: 0.7
##      - id: ClothingNeckCloakMoth #bzzz Moth-pocalypse # DeltaV - why
##        prob: 0.15
      - id: LunchboxSecurityFilledRandom # Delta-v Lunchboxes!
        prob: 0.3
        #PIRATE start
      - id: ClothingBackpackDuffelSurgeryFilled
        #PIRATE end

# DeltaV - adding corpsman locker w/ hardsuit

- type: entity
  id: LockerBrigmedicFilledHardsuit
  suffix: Corpsman, Filled, Hardsuit
  parent: LockerBrigmedic
  components:
  - type: StorageFill
    contents:
      - id: ClothingEyesGlassesCorpsman # DeltaV - Corpsman Glasses
      - id: WeaponDisabler
      - id: TrackingImplanter
        amount: 2
      - id: ClothingOuterHardsuitCombatCorpsman
      - id: ClothingUniformJumpsuitBrigmedic
      - id: ClothingUniformJumpskirtBrigmedic
      - id: HandheldGPSBasic # Added GPS because I just think it should be there tbh.
      - id: MedkitFilled
      - id: MedkitCombatFilled
        prob: 0.6
      - id: MedkitAdvancedFilled
        prob: 0.4
      - id: MedkitOxygenFilled
        prob: 0.3
      - id: MedkitBruteFilled
        prob: 0.3
      - id: MedkitToxinFilled
        prob: 0.3
      - id: MedkitBurnFilled
        prob: 0.7
      - id: LunchboxSecurityFilledRandom # Delta-v Lunchboxes!
        prob: 0.3
        #PIRATE start
      - id: ClothingBackpackDuffelSurgeryFilled
        #PIRATE end

# DeltaV - end addition

- type: entity
  id: LockerDetectiveFilled
  suffix: Filled
  parent: LockerDetective
  components:
  - type: StorageFill
    contents:
      - id: ClothingEyesHudSecurity
        prob: 0.3
      - id: ClothingHeadHatFedoraBrown
      - id: ClothingNeckTieDet
      - id: ClothingOuterVestDetective
      - id: ClothingOuterCoatDetective
      - id: FlashlightSeclite
      - id: ForensicScanner
      - id: LogProbeCartridge
      - id: BoxForensicPad
      - id: DrinkDetFlask
      - id: ClothingHandsGlovesForensic
      - id: RubberStampDetective
      - id: HoloprojectorSecurity
      - id: BoxEvidenceMarkers
      - id: LunchboxSecurityFilledRandom # Delta-v Lunchboxes!
        prob: 0.3
      - id: BoxTapeRecorder # DeltaV

- type: entity
  id: ClosetBombFilled
  parent: ClosetBomb
  suffix: Filled
  components:
  - type: StorageFill
    contents:
      - id: ClothingHeadHelmetBombSuit
      - id: ClothingOuterSuitBomb
      # NT is cheap, what can you do...
      - id: Wirecutter
        prob: 0.9
      - id: Screwdriver
        prob: 0.9
      - id: Multitool
        prob: 0.5

- type: entity
  parent: GunSafe
  id: GunSafeDisabler
  name: "сейф-знешкоджувач"
  components:
  - type: StorageFill
    contents:
    - id: WeaponDisabler
      amount: 5

- type: entity
  parent: GunSafe
  id: GunSafePistolMk58
  name: "сейф для MK58"
  components:
  - type: StorageFill
    contents:
    - id: WeaponPistolMk58
      amount: 4
    - id: MagazinePistol
      amount: 8

- type: entity
  parent: GunSafe
  id: GunSafeRifleLecter
  name: "сейф для Лектора"
  components:
  - type: StorageFill
    contents:
    - id: WeaponRifleLecter
      amount: 2
    - id: MagazineRifle
      amount: 4

- type: entity
  parent: GunSafe
  id: GunSafeSubMachineGunDrozd
  name: "сейф для Дрозда"
  components:
  - type: StorageFill
    contents:
    - id: WeaponSubMachineGunDrozd
      amount: 2
    - id: MagazinePistolSubMachineGun
      amount: 4

- type: entity
  parent: GunSafe
  id: GunSafeShotgunEnforcer
  name: "сейф для Enforcer"
  components:
  - type: StorageFill
    contents:
    - id: WeaponShotgunEnforcer
      amount: 2
    - id: MagazineShotgun
      amount: 4

- type: entity
  parent: GunSafe
  id: GunSafeShotgunKammerer
  name: "сейф для Каммерера"
  components:
  - type: StorageFill
    contents:
    - id: WeaponShotgunKammerer
      amount: 2
    - id: MagazineShotgun
      amount: 4

- type: entity
  id: GunSafeSubMachineGunWt550
  suffix: Wt550
  parent: GunSafe
  name: "сейф для WT550 "
  components:
  - type: StorageFill
    contents:
    - id: WeaponSubMachineGunWt550
      amount: 2
    - id: MagazinePistolSubMachineGunTopMounted
      amount: 4

- type: entity
  parent: GunSafe
  id: GunSafeLaserCarbine
  name: "сейф для лазерних карабінів"
  components:
  - type: StorageFill
    contents:
    - id: WeaponLaserCarbine
      amount: 3

- type: entity
  id: GunSafeRifleBRDIR25
  suffix: BRDI-R25
  parent: GunSafe
  name: "Сейф для гвинтівки BRDI-R25"
  components:
  - type: StorageFill
    contents:
    - id: WeaponSubMachineGunBRDIR25
      amount: 2
    - id: MagazineCaselessRifle
      amount: 4
