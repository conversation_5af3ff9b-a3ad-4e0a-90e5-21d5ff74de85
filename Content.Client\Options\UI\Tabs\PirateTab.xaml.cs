using System;
using Content.Client._Pirate.PingName;
using Content.Shared._Pirate.CCVar;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Configuration;
using Robust.Shared.IoC;
using Robust.Shared.Maths;

namespace Content.Client.Options.UI.Tabs;

[GenerateTypedNameReferences]
public sealed partial class PirateTab : Control
{
    [Dependency] private readonly IConfigurationManager _cfg = default!;

    private PingNameSystem? _pingNameSystem;

    public PirateTab()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        _pingNameSystem = IoCManager.Resolve<IEntitySystemManager>().GetEntitySystem<PingNameSystem>();

        PingNameEnabledCheckBox.OnToggled += OnPingNameEnabledToggled;
        PingNameSoundsCheckBox.OnToggled += OnPingNameSoundsToggled;
        PingNameSelfSoundsCheckBox.OnToggled += OnPingNameSelfSoundsToggled;

        // Sound controls
        TestSoundButton.OnPressed += OnTestSoundButtonPressed;
        PingSoundSelector.OnItemSelected += OnPingSoundSelected;

        // Custom words
        CustomWordsLineEdit.OnTextChanged += OnCustomWordsChanged;

        // Sound cooldown
        SoundCooldownSpinBox.OnValueChanged += OnSoundCooldownChanged;

        // Color controls
        ColorSliders.OnColorChanged += OnColorSlidersChanged;
        PingNameColorReset.OnPressed += OnPingNameColorReset;

        // Apply/Reset buttons
        ApplyButton.OnPressed += OnApplyButtonPressed;
        ResetButton.OnPressed += OnResetButtonPressed;

        SetupSoundSelector();
        UpdateValues();
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            PingNameEnabledCheckBox.OnToggled -= OnPingNameEnabledToggled;
            PingNameSoundsCheckBox.OnToggled -= OnPingNameSoundsToggled;

            ColorSliders.OnColorChanged -= OnColorSlidersChanged;
            PingNameColorReset.OnPressed -= OnPingNameColorReset;

            ApplyButton.OnPressed -= OnApplyButtonPressed;
            ResetButton.OnPressed -= OnResetButtonPressed;
        }

        base.Dispose(disposing);
    }

    private void SetupSoundSelector()
    {
        if (_pingNameSystem == null)
            return;

        var availableSounds = _pingNameSystem.GetAvailablePingSounds();

        foreach (var (soundId, soundName) in availableSounds)
        {
            PingSoundSelector.AddItem(soundName, soundId);
        }
    }

    private void UpdateValues()
    {
        PingNameEnabledCheckBox.Pressed = _cfg.GetCVar(PirateCVars.PingNameEnabled);
        PingNameSoundsCheckBox.Pressed = _cfg.GetCVar(PirateCVars.PingNameSoundsEnabled);
        PingNameSelfSoundsCheckBox.Pressed = _cfg.GetCVar(PirateCVars.PingNameSelfSoundsEnabled);

        // Update sound selector
        var currentSoundId = _cfg.GetCVar(PirateCVars.PingNameSoundId);
        for (var i = 0; i < PingSoundSelector.ItemCount; i++)
        {
            if (PingSoundSelector.GetItemMetadata(i)?.ToString() == currentSoundId)
            {
                PingSoundSelector.SelectId(i);
                break;
            }
        }

        // Update custom words
        CustomWordsLineEdit.Text = _cfg.GetCVar(PirateCVars.PingNameCustomWords);

        // Update sound cooldown
        SoundCooldownSpinBox.Value = _cfg.GetCVar(PirateCVars.PingNameSoundCooldown);

        var colorHex = _cfg.GetCVar(PirateCVars.PingNameColor);
        var color = Color.TryFromHex(colorHex) ?? Color.Yellow;
        ColorSliders.Color = color;

        UpdateColorPreview();
        UpdateChanges();
    }

    private void UpdateColorPreview()
    {
        var currentColor = ColorSliders.Color;
        PingNameColorPreview.Modulate = currentColor;
    }

    private void OnPingNameEnabledToggled(BaseButton.ButtonToggledEventArgs args)
    {
        UpdateChanges();
    }

    private void OnPingNameSoundsToggled(BaseButton.ButtonToggledEventArgs args)
    {
        UpdateChanges();
    }

    private void OnPingNameSelfSoundsToggled(BaseButton.ButtonToggledEventArgs args)
    {
        UpdateChanges();
    }

    private void OnPingSoundSelected(OptionButton.ItemSelectedEventArgs args)
    {
        UpdateChanges();
    }

    private void OnTestSoundButtonPressed(BaseButton.ButtonEventArgs args)
    {
        if (_pingNameSystem == null || PingSoundSelector.SelectedMetadata == null)
            return;

        var soundId = PingSoundSelector.SelectedMetadata.ToString();
        if (!string.IsNullOrEmpty(soundId))
        {
            _pingNameSystem.PlayTestPingSound(soundId);
        }
    }

    private void OnCustomWordsChanged(LineEdit.LineEditEventArgs args)
    {
        UpdateChanges();
    }

    private void OnSoundCooldownChanged(Action<ValueChangedEventArgs>? args)
    {
        UpdateChanges();
    }

    private void OnColorSlidersChanged(Color color)
    {
        UpdateColorPreview();
        UpdateChanges();
    }

    private void OnPingNameColorReset(BaseButton.ButtonEventArgs args)
    {
        var defaultColor = "#FFFF00"; // Yellow

        // Update sliders to show the default color
        var color = Color.TryFromHex(defaultColor) ?? Color.Yellow;
        ColorSliders.Color = color;

        UpdateColorPreview();
        UpdateChanges();
    }

    private void OnApplyButtonPressed(BaseButton.ButtonEventArgs args)
    {
        _cfg.SetCVar(PirateCVars.PingNameEnabled, PingNameEnabledCheckBox.Pressed);
        _cfg.SetCVar(PirateCVars.PingNameSoundsEnabled, PingNameSoundsCheckBox.Pressed);
        _cfg.SetCVar(PirateCVars.PingNameSelfSoundsEnabled, PingNameSelfSoundsCheckBox.Pressed);

        var selectedSoundId = PingSoundSelector.SelectedMetadata?.ToString() ?? "ping1";
        _cfg.SetCVar(PirateCVars.PingNameSoundId, selectedSoundId);

        _cfg.SetCVar(PirateCVars.PingNameCustomWords, CustomWordsLineEdit.Text);
        _cfg.SetCVar(PirateCVars.PingNameSoundCooldown, (float)SoundCooldownSpinBox.Value);
        _cfg.SetCVar(PirateCVars.PingNameColor, ColorSliders.Color.ToHex());

        _cfg.SaveToFile();
        UpdateChanges();
    }

    private void OnResetButtonPressed(BaseButton.ButtonEventArgs args)
    {
        Reset();
    }

    private void Reset()
    {
        PingNameEnabledCheckBox.Pressed = PirateCVars.PingNameEnabled.DefaultValue;
        PingNameSoundsCheckBox.Pressed = PirateCVars.PingNameSoundsEnabled.DefaultValue;
        PingNameSelfSoundsCheckBox.Pressed = PirateCVars.PingNameSelfSoundsEnabled.DefaultValue;

        // Reset sound selector to default
        var defaultSoundId = PirateCVars.PingNameSoundId.DefaultValue;
        for (var i = 0; i < PingSoundSelector.ItemCount; i++)
        {
            if (PingSoundSelector.GetItemMetadata(i)?.ToString() == defaultSoundId)
            {
                PingSoundSelector.SelectId(i);
                break;
            }
        }

        // Reset custom words
        CustomWordsLineEdit.Text = PirateCVars.PingNameCustomWords.DefaultValue;

        // Reset sound cooldown
        SoundCooldownSpinBox.Value = PirateCVars.PingNameSoundCooldown.DefaultValue;

        var defaultColor = Color.TryFromHex(PirateCVars.PingNameColor.DefaultValue) ?? Color.Yellow;
        ColorSliders.Color = defaultColor;

        UpdateColorPreview();
        UpdateChanges();
    }

    private void UpdateChanges()
    {
        var currentSoundId = PingSoundSelector.SelectedMetadata?.ToString() ?? "ping1";

        var isPingNameEnabledSame = PingNameEnabledCheckBox.Pressed == _cfg.GetCVar(PirateCVars.PingNameEnabled);
        var isPingNameSoundsSame = PingNameSoundsCheckBox.Pressed == _cfg.GetCVar(PirateCVars.PingNameSoundsEnabled);
        var isPingNameSelfSoundsSame = PingNameSelfSoundsCheckBox.Pressed == _cfg.GetCVar(PirateCVars.PingNameSelfSoundsEnabled);
        var isSoundIdSame = currentSoundId == _cfg.GetCVar(PirateCVars.PingNameSoundId);
        var isCustomWordsSame = CustomWordsLineEdit.Text == _cfg.GetCVar(PirateCVars.PingNameCustomWords);
        var isSoundCooldownSame = Math.Abs(SoundCooldownSpinBox.Value - _cfg.GetCVar(PirateCVars.PingNameSoundCooldown)) < 0.01f;
        var isColorSame = ColorSliders.Color.ToHex() == _cfg.GetCVar(PirateCVars.PingNameColor);

        var isEverythingSame = isPingNameEnabledSame && isPingNameSoundsSame && isPingNameSelfSoundsSame &&
                              isSoundIdSame && isCustomWordsSame && isSoundCooldownSame && isColorSame;

        ApplyButton.Disabled = isEverythingSame;
        ResetButton.Disabled = isEverythingSame;
    }
}












