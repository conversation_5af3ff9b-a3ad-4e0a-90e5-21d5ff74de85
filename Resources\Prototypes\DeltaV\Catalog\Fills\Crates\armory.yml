- type: entity
  id: CrateArmoryGrand
  parent: CrateWeaponSecure
  components:
  - type: StorageFill
    contents:
      - id: WeaponSniperGrand
        amount: 2
      - id: MagazineLightRifleMarkOne
        amount: 4

- type: entity
  id: CrateArmoryUniversal
  parent: CrateWeaponSecure
  components:
  - type: StorageFill
    contents:
      - id: WeaponPistolUniversal
        amount: 2
      - id: MagazineUniversalMagnum
        amount: 4

- type: entity
  id: CrateArmoryAdjutant
  parent: CrateWeaponSecure
  components:
  - type: StorageFill
    contents:
    - id: WeaponShotgunAdjutant
      amount: 2
    - id: BoxLethalshot
      amount: 3

- type: entity
  id: CrateArmoryEnergyGun
  parent: CrateWeaponSecure
  components:
  - type: StorageFill
    contents: 
      - id: WeaponEnergyGun
        amount: 3
  
- type: entity
  id: CrateArmoryEnergyGunMini
  parent: CrateWeaponSecure
  components:
  - type: StorageFill
    contents: 
      - id: WeaponEnergyGunMini
        amount: 3
