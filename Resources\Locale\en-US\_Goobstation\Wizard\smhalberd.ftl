supermatter-halberd-execution-start-user = You raise {THE($used)} high, ready to bring it down on {$target}!
supermatter-halberd-execution-start-other = {CAPITALIZE($user)} raises {THE($used)} high, ready to bring it down on {$target}!
supermatter-halberd-execution-cancel = You lower {THE($used)}. There'll be time to obliterate them later...
supermatter-halberd-execution-end-user = You bring down {THE($used)}, obliterating {$target} with a heavy blow!
supermatter-halberd-execution-end-other = {CAPITALIZE($user)} brings down {THE($used)}, obliterating {$target} with a heavy blow!
