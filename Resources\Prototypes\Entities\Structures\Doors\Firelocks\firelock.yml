- type: entity
  id: BaseFirelock
  abstract: true
  parent: BaseStructure
  name: "вогневий шлюз"
  description: "Застосуй лом."
  components:
    - type: AtmosAlarmable
      syncWith:
        - FireAlarm
        - AirAlarm
    - type: ApcPowerReceiver
    - type: ExtensionCableReceiver
    - type: DeviceNetwork
      deviceNetId: AtmosDevices
      receiveFrequencyId: AtmosMonitor
    - type: DeviceNetworkRequiresPower
    - type: InteractionOutline
    - type: Damageable
      damageContainer: Inorganic
      damageModifierSet: Metallic
    - type: RCDDeconstructable
      cost: 4
      delay: 6
      fx: EffectRCDDeconstruct6
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 500
        behaviors:
        - !type:DoActsBehavior
          acts: ["Destruction"]
    - type: Sprite
      sprite: Structures/Doors/Airlocks/Standard/firelock.rsi
      snapCardinals: true
      layers:
        - state: closed
          map: ["enum.DoorVisualLayers.Base"]
        - state: closed_unlit
          shader: unshaded
          map: ["enum.DoorVisualLayers.BaseUnlit"]
          visible: false
        - state: welded
          map: ["enum.WeldableLayers.BaseWelded"]
        - state: bolted_unlit
          shader: unshaded
          map: ["enum.DoorVisualLayers.BaseBolted"]
          visible: false
        - state: panel_open
          map: ["enum.WiresVisualLayers.MaintenancePanel"]
    - type: AnimationPlayer
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.49,-0.49,0.49,0.49"  # don't want this colliding with walls or they won't close
          density: 100
          mask:
          - FullTileMask
          layer:
          - AirlockLayer
    - type: Door
      openDrawDepth: WallTops
      closeTimeOne: 0.1
      closeTimeTwo: 0.6
      openTimeOne: 0.1
      openTimeTwo: 0.6
      state: Open
      bumpOpen: false
      clickOpen: true
      crushDamage:
        types:
          Blunt: 15
      openSound:
        path: /Audio/Machines/airlock_open.ogg
      closeSound:
        path: /Audio/Machines/airlock_close.ogg
      denySound:
        path: /Audio/Machines/airlock_deny.ogg
      openingAnimationTime: 0.6
      closingAnimationTime: 0.6
    - type: Weldable
      fuel: 5
      time: 3
    - type: Firelock
    - type: Appearance
    - type: WiresVisuals
    - type: WiresPanel
    - type: UserInterface
      interfaces:
        enum.WiresUiKey.Key:
          type: WiresBoundUserInterface
    - type: Physics
      canCollide: false
    - type: Airtight
      airBlocked: false
      noAirWhenFullyAirBlocked: true
    - type: RadiationBlocker
      enabled: false
      resistance: 1
    - type: Occluder
      enabled: false
    - type: WallMount
      arc: 360
    - type: StaticPrice
      price: 150
    - type: DoorBolt
    - type: AccessReader
      access: [ [ "Engineering" ] ]
    - type: NavMapDoor

- type: entity
  id: Firelock
  parent: BaseFirelock
  components:
  - type: StationAiWhitelist
  - type: ContainerContainer
    containers:
      board: !type:Container
  - type: Construction
    graph: Firelock
    node: Firelock
    containers:
    - board
  - type: ContainerFill
    containers:
      board: [ FirelockElectronics ]

- type: entity
  id: FirelockGlass
  parent: Firelock
  name: "вогневий шлюз з вікном"
  components:
    - type: StationAiWhitelist
    - type: Sprite
      sprite: Structures/Doors/Airlocks/Glass/firelock.rsi
    - type: Occluder
      enabled: false
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.49,-0.49,0.49,0.49"  # don't want this colliding with walls or they won't close
          mask:
          - Impassable
          - HighImpassable
          layer:
          - GlassAirlockLayer
    - type: Door
      occludes: false
    - type: Construction
      node: FirelockGlass

- type: entity
  id: FirelockEdge
  parent: Firelock
  name: "вогневий шлюз"
  components:
    - type: StationAiWhitelist
    - type: Sprite
      sprite: Structures/Doors/edge_door_hazard.rsi
      snapCardinals: false
    - type: Airtight
      airBlocked: false
      noAirWhenFullyAirBlocked: false
      airBlockedDirection:
        - South
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.49,-0.49,0.49,-0.2" # don't want this colliding with walls or they won't close
          mask:
          - Impassable
          - HighImpassable
          layer:
          - GlassAirlockLayer
    - type: Occluder
      enabled: false
    - type: Door
      occludes: false
    - type: Physics
      canCollide: false
    - type: StaticPrice
      price: 100
    - type: Construction
      graph: Firelock
      node: FirelockEdge
