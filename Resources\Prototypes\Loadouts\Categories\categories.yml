# Alphabetically ordered, except for Uncategorized since it is always first
# AUncategorized is always first in subcategories to stay consistent with the root Uncategorized
# AUncategorized is not a spelling mistake, and might have more As added as needed

- type: loadoutCategory
  id: Uncategorized
  root: true

- type: loadoutCategory
  id: Backpacks
  root: true

- type: loadoutCategory
  id: Eyes
  root: true

- type: loadoutCategory
  id: Hands
  root: true

- type: loadoutCategory
  id: Head
  root: true

- type: loadoutCategory
  id: Items
  root: true

- type: loadoutCategory
  id: Jobs
  root: true
  subCategories:
    - JobsAUncategorized
    - JobsCommand
    - JobsEngineering
    - JobsEpistemics
    - JobsLogistics
    - JobsMedical
    - JobsSecurity
    - JobsService
    - JobsZMisc

- type: loadoutCategory
  id: JobsAUncategorized

# Command
# Only Captain and HoP, Department Specific roles are under their respective Departments.
# If we ever added Centcomm or Blueshield, that would go here.
- type: loadoutCategory
  id: JobsCommand
  subCategories:
    - JobsCommandAUncategorized
    - JobsCommandCaptain
    - JobsCommandHeadOfPersonnel
    - JobsCommandAdminAssistant
    - JobsCommandBlueshieldOfficer
    - JobsCommandNanorep
    - JobsCommandMagistrate

- type: loadoutCategory
  id: JobsCommandAUncategorized

- type: loadoutCategory
  id: JobsCommandCaptain

- type: loadoutCategory
  id: JobsCommandHeadOfPersonnel

- type: loadoutCategory
  id: JobsCommandAdminAssistant

- type: loadoutCategory
  id: JobsCommandBlueshieldOfficer

- type: loadoutCategory
  id: JobsCommandNanorep

- type: loadoutCategory
  id: JobsCommandMagistrate

# Engineering
- type: loadoutCategory
  id: JobsEngineering
  subCategories:
    - JobsEngineeringAAUncategorized
    - JobsEngineeringAtmosphericTechnician
    - JobsEngineeringChiefEngineer
    - JobsEngineeringSeniorEngineer
    - JobsEngineeringStationEngineer
    - JobsEngineeringTechnicalAssistant

- type: loadoutCategory
  id: JobsEngineeringAAUncategorized

- type: loadoutCategory
  id: JobsEngineeringAtmosphericTechnician

- type: loadoutCategory
  id: JobsEngineeringChiefEngineer

- type: loadoutCategory
  id: JobsEngineeringSeniorEngineer

- type: loadoutCategory
  id: JobsEngineeringStationEngineer

- type: loadoutCategory
  id: JobsEngineeringTechnicalAssistant

# Epistemics
- type: loadoutCategory
  id: JobsEpistemics
  subCategories:
    - JobsEpistemicsAAUncategorized
    - JobsEpistemicsAcolyte
    - JobsEpistemicsCataloger
    - JobsEpistemicsChaplain
    - JobsEpistemicsGolemancer
    - JobsEpistemicsMystagogue
    - JobsEpistemicsMystic
    - JobsEpistemicsNoviciate
    - JobsEpistemicsPsionicMantis

- type: loadoutCategory
  id: JobsEpistemicsAAUncategorized

- type: loadoutCategory
  id: JobsEpistemicsAcolyte

- type: loadoutCategory
  id: JobsEpistemicsCataloger

- type: loadoutCategory
  id: JobsEpistemicsChaplain

- type: loadoutCategory
  id: JobsEpistemicsGolemancer

- type: loadoutCategory
  id: JobsEpistemicsMystagogue

- type: loadoutCategory
  id: JobsEpistemicsMystic

- type: loadoutCategory
  id: JobsEpistemicsNoviciate

- type: loadoutCategory
  id: JobsEpistemicsPsionicMantis

# Logistics
- type: loadoutCategory
  id: JobsLogistics
  subCategories:
    - JobsLogisticsAUncategorized
    - JobsLogisticsCargoTechnician
    - JobsLogisticsCourier
    - JobsLogisticsLogisticsOfficer
    - JobsLogisticsSalvageSpecialist

- type: loadoutCategory
  id: JobsLogisticsAUncategorized

- type: loadoutCategory
  id: JobsLogisticsCargoTechnician

- type: loadoutCategory
  id: JobsLogisticsCourier

- type: loadoutCategory
  id: JobsLogisticsLogisticsOfficer

- type: loadoutCategory
  id: JobsLogisticsSalvageSpecialist

# Medical
- type: loadoutCategory
  id: JobsMedical
  subCategories:
    - JobsMedicalAUncategorized
    - JobsMedicalChemist
    - JobsMedicalChiefMedicalOfficer
    - JobsMedicalMedicalDoctor
    - JobsMedicalMedicalIntern
    - JobsMedicalParamedic
    - JobsMedicalPsychologist
    - JobsMedicalSeniorPhysician

- type: loadoutCategory
  id: JobsMedicalAUncategorized

- type: loadoutCategory
  id: JobsMedicalChemist

- type: loadoutCategory
  id: JobsMedicalChiefMedicalOfficer

- type: loadoutCategory
  id: JobsMedicalMedicalDoctor

- type: loadoutCategory
  id: JobsMedicalMedicalIntern

- type: loadoutCategory
  id: JobsMedicalParamedic

- type: loadoutCategory
  id: JobsMedicalPsychologist

- type: loadoutCategory
  id: JobsMedicalSeniorPhysician

# Security
- type: loadoutCategory
  id: JobsSecurity
  subCategories:
    - JobsSecurityAUncategorized
    - JobsSecurityWeapons
    - JobsSecurityCadet
    - JobsSecurityCorpsman
    - JobsSecurityDetective
    - JobsSecurityHeadOfSecurity
    - JobsSecuritySecurityOfficer
    - JobsSecuritySeniorOfficer
    - JobsSecurityWarden

- type: loadoutCategory
  id: JobsSecurityAUncategorized

- type: loadoutCategory
  id: JobsSecurityWeapons

- type: loadoutCategory
  id: JobsSecurityCadet

- type: loadoutCategory
  id: JobsSecurityCorpsman

- type: loadoutCategory
  id: JobsSecurityDetective

- type: loadoutCategory
  id: JobsSecurityHeadOfSecurity

- type: loadoutCategory
  id: JobsSecuritySecurityOfficer

- type: loadoutCategory
  id: JobsSecuritySeniorOfficer

- type: loadoutCategory
  id: JobsSecurityWarden

# Service
- type: loadoutCategory
  id: JobsService
  subCategories:
    - JobsServiceAUncategorized
    - JobsServiceBartender
    - JobsServiceBotanist
    - JobsServiceChef
    - JobsServiceClown
    - JobsServiceJanitor
    - JobsServiceLawyer
    - JobsServiceMime
    - JobsServiceMusician
    - JobsServiceReporter

- type: loadoutCategory
  id: JobsServiceAUncategorized

- type: loadoutCategory
  id: JobsServiceBartender

- type: loadoutCategory
  id: JobsServiceBotanist

- type: loadoutCategory
  id: JobsServiceChef

- type: loadoutCategory
  id: JobsServiceClown

- type: loadoutCategory
  id: JobsServiceJanitor

- type: loadoutCategory
  id: JobsServiceLawyer

- type: loadoutCategory
  id: JobsServiceMime

- type: loadoutCategory
  id: JobsServiceMusician

- type: loadoutCategory
  id: JobsServiceReporter

  # Misc
- type: loadoutCategory
  id: JobsZMisc # this breaks convention a bit but the oddballs should be after everything else
  subCategories: # this can also be for unique/station-specific jobs like boxer/gladiator
    - JobsZMiscAUncategorized
    - JobsZMiscPrisoner

- type: loadoutCategory
  id: JobsZMiscAUncategorized

- type: loadoutCategory
  id: JobsZMiscPrisoner

# Now Leaving: Jobs Category

- type: loadoutCategory
  id: Mask
  root: true

- type: loadoutCategory
  id: Neck
  root: true

- type: loadoutCategory
  id: Outer
  root: true

- type: loadoutCategory
  id: Belt
  root: true

- type: loadoutCategory
  id: Shoes
  root: true

- type: loadoutCategory
  id: Species
  root: true

- type: loadoutCategory
  id: Uniform
  root: true
