- type: job
  id: Mime
  name: job-name-mime
  description: job-description-mime
  playTimeTracker: JobMime
  startingGear: MimeGear
  icon: "JobIconMime"
  supervisors: job-supervisors-hop
  access:
  - Theatre
  - Maintenance
  - Mime # DeltaV - Add Mime access
  special:
  - !type:AddComponentSpecial
    components:
    - type: MimePowers
    - type: FrenchAccent
  requirements:
    - !type:CharacterOverallTimeRequirement
      min: 7200 # DeltaV - 2 hours
    - !type:CharacterWhitelistRequirement # PIRATE - Whitelist requirement

- type: startingGear
  id: MimeGear
  subGear:
  - MimePlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitMime
    back: ClothingBackpackMimeFilled
    head: ClothingHeadHatBeret
    belt: ClothingBeltSuspenders
    gloves: ClothingHandsGlovesLatex
    shoes: ClothingShoesColorWhite
    pocket1: CrayonMime
    pocket2: Paper
    mask: ClothingMaskMime
    id: MimePDA
    ears: ClothingHeadsetService
  innerClothingSkirt: ClothingUniformJumpskirtMime
  satchel: ClothingBackpackSatchelMimeFilled
  duffelbag: ClothingBackpackDuffelMimeFilled

- type: startingGear
  id: MimePlasmamanGear
  parent: BasePlasmamanGear
  equipment:
    jumpsuit: ClothingUniformEnvirosuitMime
    head: ClothingHeadEnvirohelmMime
    gloves: ClothingHandsGlovesEnviroglovesWhite
    mask: ClothingMaskMime # Parent sets mask to breath mask so set it again here

- type: entity
  id: ActionMimeInvisibleWall
  name: "Створити невидиму стіну"
  description: "Створити невидиму стіну перед собою, якщо це можливо."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    priority: -1
    useDelay: 30
    icon:
      sprite: Structures/Walls/solid.rsi
      state: full
    event: !type:InvisibleWallActionEvent
