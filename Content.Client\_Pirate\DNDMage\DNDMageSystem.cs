using Content.Shared._Pirate.DNDMage;
using Robust.Client.GameObjects;

namespace Content.Client._Pirate.DNDMage;

/// <summary>
/// Client-side system for handling DNDMageComponent.
/// </summary>
public sealed class DNDMageSystem : EntitySystem
{
    public override void Initialize()
    {
        base.Initialize();
        
        SubscribeLocalEvent<DNDMageComponent, ComponentInit>(OnComponentInit);
    }

    private void OnComponentInit(EntityUid uid, DNDMageComponent component, ComponentInit args)
    {
        // Client-side initialization logic if needed
    }
}
