- type: decal
  id: SyndicateLogo
  abstract: true

- type: decal
  id: syndlogo1
  parent: SyndicateLogo
  tags: ["syndicate"]
  sprite:
    sprite: Decals/syndlogo.rsi
    state: syndlogo1

- type: decal
  id: syndlogo2
  parent: SyndicateLogo
  tags: ["syndicate"]
  sprite:
    sprite: Decals/syndlogo.rsi
    state: syndlogo2

- type: decal
  id: syndlogo3
  parent: SyndicateLogo
  tags: ["syndicate"]
  sprite:
    sprite: Decals/syndlogo.rsi
    state: syndlogo3

- type: decal
  id: syndlogo4
  parent: SyndicateLogo
  tags: ["syndicate"]
  sprite:
    sprite: Decals/syndlogo.rsi
    state: syndlogo4

- type: decal
  id: syndlogo5
  parent: SyndicateLogo
  tags: ["syndicate"]
  sprite:
    sprite: Decals/syndlogo.rsi
    state: syndlogo5

- type: decal
  id: syndlogo6
  parent: SyndicateLogo
  tags: ["syndicate"]
  sprite:
    sprite: Decals/syndlogo.rsi
    state: syndlogo6

- type: decal
  id: syndlogo7
  parent: SyndicateLogo
  tags: ["syndicate"]
  sprite:
    sprite: Decals/syndlogo.rsi
    state: syndlogo7

- type: decal
  id: syndlogo8
  parent: SyndicateLogo
  tags: ["syndicate"]
  sprite:
    sprite: Decals/syndlogo.rsi
    state: syndlogo8

- type: decal
  id: syndlogo9
  parent: SyndicateLogo
  tags: ["syndicate"]
  sprite:
    sprite: Decals/syndlogo.rsi
    state: syndlogo9

- type: decal
  id: syndlogo10
  parent: SyndicateLogo
  tags: ["syndicate"]
  sprite:
    sprite: Decals/syndlogo.rsi
    state: syndlogo10

- type: decal
  id: syndlogo11
  parent: SyndicateLogo
  tags: ["syndicate"]
  sprite:
    sprite: Decals/syndlogo.rsi
    state: syndlogo11

- type: decal
  id: syndlogo12
  parent: SyndicateLogo
  tags: ["syndicate"]
  sprite:
    sprite: Decals/syndlogo.rsi
    state: syndlogo12

- type: decal
  id: syndlogo13
  parent: SyndicateLogo
  tags: ["syndicate"]
  sprite:
    sprite: Decals/syndlogo.rsi
    state: syndlogo13

- type: decal
  id: syndlogo14
  parent: SyndicateLogo
  tags: ["syndicate"]
  sprite:
    sprite: Decals/syndlogo.rsi
    state: syndlogo14

- type: decal
  id: syndlogo15
  parent: SyndicateLogo
  tags: ["syndicate"]
  sprite:
    sprite: Decals/syndlogo.rsi
    state: syndlogo15

- type: decal
  id: syndlogo16
  parent: SyndicateLogo
  tags: ["syndicate"]
  sprite:
    sprite: Decals/syndlogo.rsi
    state: syndlogo16

- type: decal
  id: syndlogo17
  parent: SyndicateLogo
  tags: ["syndicate"]
  sprite:
    sprite: Decals/syndlogo.rsi
    state: syndlogo17

- type: decal
  id: syndlogo18
  parent: SyndicateLogo
  tags: ["syndicate"]
  sprite:
    sprite: Decals/syndlogo.rsi
    state: syndlogo18

- type: decal
  id: syndlogo19
  parent: SyndicateLogo
  tags: ["syndicate"]
  sprite:
    sprite: Decals/syndlogo.rsi
    state: syndlogo19

