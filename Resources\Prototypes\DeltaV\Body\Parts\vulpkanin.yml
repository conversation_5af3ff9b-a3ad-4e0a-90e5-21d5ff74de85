# TODO: Add descriptions (many)
# TODO BODY: Part damage
- type: entity
  id: PartVulpkanin
  parent: BaseItem
  name: "частина тіла вульпи"
  abstract: true
  components:
  - type: Damageable
    damageContainer: OrganicPart # Shitmed Change
  - type: BodyPart
  - type: ContainerContainer
    containers:
      bodypart: !type:Container
        ents: []
  - type: StaticPrice #DynamicPrice
    price: 100
  - type: Tag
    tags:
      - Trash
  # Shitmed Change Start
  - type: Gibbable
  - type: SurgeryTool
    startSound:
      path: /Audio/_Shitmed/Medical/Surgery/organ1.ogg
    endSound:
      path: /Audio/_Shitmed/Medical/Surgery/organ2.ogg
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTypeTrigger
        damageType: Blunt
        damage: 110
      behaviors:
      - !type:GibPartBehavior { }
    - trigger:
        !type:DamageTypeTrigger
        damageType: Slash
        damage: 150
      behaviors:
      - !type:GibPartBehavior { }
    - trigger:
        !type:DamageTypeTrigger
        damageType: Heat
        damage: 200
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawnInContainer: true
        spawn:
          Ash:
            min: 1
            max: 1
      - !type:<PERSON><PERSON>ody<PERSON>ehavi<PERSON> { }
      - !type:PlaySoundBehavior
        sound:
          collection: MeatLaserImpact
    # Shitmed Change End

- type: entity
  id: TorsoVulpkanin
  name: "торс вульпи"
  parent: PartVulpkanin
  components:
  - type: Sprite
    netsync: false
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: "torso_m"
  - type: Icon
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: "torso_m"
  # Shitmed Change Start
  - type: BodyPart
    partType: Torso
    toolName: "a torso"
    containerName: "torso_slot"
  - type: ContainerContainer
    containers:
      torso_slot: !type:ContainerSlot {}
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTypeTrigger
        damageType: Blunt
        damage: 400
      behaviors:
      - !type:GibPartBehavior { }
    - trigger:
        !type:DamageTypeTrigger
        damageType: Slash
        damage: 400
      behaviors:
      - !type:GibPartBehavior { }
    - trigger:
        !type:DamageTypeTrigger
        damageType: Heat
        damage: 400
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawnInContainer: true
        spawn:
          Ash:
            min: 1
            max: 1
      - !type:BurnBodyBehavior { }
      - !type:PlaySoundBehavior
        sound:
          collection: MeatLaserImpact
  # Shitmed Change End

- type: entity
  id: HeadVulpkanin
  name: "голова вульпи"
  parent: PartVulpkanin
  components:
  - type: Sprite
    netsync: false
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: "head_m"
  - type: Icon
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: "head_m"
  - type: BodyPart
    partType: Head
    vital: true
    toolName: "a head" # Shitmed Change
  - type: Input
    context: "ghost"
  - type: InputMover
  - type: GhostOnMove
  - type: Tag
    tags:
      - Head

- type: entity
  id: LeftArmVulpkanin
  name: "ліва рука вульпи"
  parent: PartVulpkanin
  components:
  - type: Sprite
    netsync: false
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: "l_arm"
  - type: Icon
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: "l_arm"
  - type: BodyPart
    partType: Arm
    symmetry: Left
    toolName: "a left arm" # Shitmed Change

- type: entity
  id: RightArmVulpkanin
  name: "права рука вульпи"
  parent: PartVulpkanin
  components:
  - type: Sprite
    netsync: false
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: "r_arm"
  - type: Icon
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: "r_arm"
  - type: BodyPart
    partType: Arm
    symmetry: Right
    toolName: "a right arm" # Shitmed Change

- type: entity
  id: LeftHandVulpkanin
  name: "ліва долоня вульпи"
  parent: PartVulpkanin
  components:
  - type: Sprite
    netsync: false
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: "l_hand"
  - type: Icon
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: "l_hand"
  - type: BodyPart
    partType: Hand
    symmetry: Left
    toolName: "a left hand" # Shitmed Change

- type: entity
  id: RightHandVulpkanin
  name: "права долоня вульпи"
  parent: PartVulpkanin
  components:
  - type: Sprite
    netsync: false
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: "r_hand"
  - type: Icon
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: "r_hand"
  - type: BodyPart
    partType: Hand
    symmetry: Right
    toolName: "a right hand" # Shitmed Change

- type: entity
  id: LeftLegVulpkanin
  name: "ліва нога вульпи"
  parent: PartVulpkanin
  components:
  - type: Sprite
    netsync: false
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: "l_leg"
  - type: Icon
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: "l_leg"
  - type: BodyPart
    partType: Leg
    symmetry: Left
    toolName: "a left leg" # Shitmed Change
  - type: MovementBodyPart

- type: entity
  id: RightLegVulpkanin
  name: "права нога вульпи"
  parent: PartVulpkanin
  components:
  - type: Sprite
    netsync: false
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: "r_leg"
  - type: Icon
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: "r_leg"
  - type: BodyPart
    partType: Leg
    symmetry: Right
    toolName: "a right leg" # Shitmed Change
  - type: MovementBodyPart

- type: entity
  id: LeftFootVulpkanin
  name: "ліва стопа вульпи"
  parent: PartVulpkanin
  components:
  - type: Sprite
    netsync: false
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: "l_foot"
  - type: Icon
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: "l_foot"
  - type: BodyPart
    partType: Foot
    symmetry: Left
    toolName: "a left foot" # Shitmed Change

- type: entity
  id: RightFootVulpkanin
  name: "права стопа вульпи"
  parent: PartVulpkanin
  components:
  - type: Sprite
    netsync: false
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: "r_foot"
  - type: Icon
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: "r_foot"
  - type: BodyPart
    partType: Foot
    symmetry: Right
    toolName: "a right foot" # Shitmed Change