﻿- type: constructionGraph
  id: BananiumAltarGraph
  start: start
  graph:
    - node: start
      edges:
        - to: bananiumAltar
          completed:
            - !type:SnapToGrid
              southRotation: true
          steps:
            - material: Bananium
              amount: 6
              doAfter: 5

    - node: bananiumAltar
      entity: AltarBananium
      edges:
        - to: start
          completed:
          - !type:SpawnPrototype
                prototype: MaterialBananium1
                amount: 6
          - !type:DeleteEntity {}
          steps:
            - tool: Welding
              doAfter: 5
