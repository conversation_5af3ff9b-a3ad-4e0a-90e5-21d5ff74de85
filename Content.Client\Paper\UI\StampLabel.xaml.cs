using System.Numerics;
using Robust.Client.AutoGenerated;
using Robust.Client.Graphics;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;

namespace Content.Client.Paper.UI;

[GenerateTypedNameReferences]
public sealed partial class StampLabel : Label
{
    /// A scale that's applied to the text to ensure it
    /// fits in the allowed space.
    private Vector2 _textScaling = Vector2.One;

    /// Shader used to draw the stamps
    private ShaderInstance? _stampShader;

    /// Allows an additional orientation to be applied to
    /// this control.
    public float Orientation = 0.0f;

    public StampLabel()
    {
        RobustXamlLoader.Load(this);

        var prototypes = IoCManager.Resolve<IPrototypeManager>();
        _stampShader = prototypes.Index<ShaderPrototype>("PaperStamp").InstanceUnique();
    }

    protected override Vector2 MeasureOverride(Vector2 availableSize)
    {
        var desiredTextSize = base.MeasureOverride(availableSize);
        var clampedScale = Vector2.Min(availableSize / desiredTextSize, Vector2.One);
        var keepAspectRatio = MathF.Min(clampedScale.X, clampedScale.Y);
        const float shimmerReduction = 0.1f;
        _textScaling = Vector2.One * MathF.Round(keepAspectRatio / shimmerReduction) * shimmerReduction;
        return desiredTextSize;
    }

    protected override void Draw(DrawingHandleScreen handle)
    {
        var offset = new Vector2(PixelPosition.X * MathF.Cos(Orientation) - PixelPosition.Y * MathF.Sin(Orientation),
                PixelPosition.Y * MathF.Cos(Orientation) + PixelPosition.X * MathF.Sin(Orientation));

        _stampShader?.SetParameter("objCoord", GlobalPosition * UIScale * new Vector2(1, -1));
        handle.UseShader(_stampShader);
        handle.SetTransform(GlobalPixelPosition - PixelPosition + offset, Orientation, _textScaling);
        base.Draw(handle);

        // Restore a sane transform+shader
        handle.SetTransform(Matrix3x2.Identity);
        handle.UseShader(null);
    }
}
