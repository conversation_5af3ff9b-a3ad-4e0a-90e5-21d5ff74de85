- type: entity
  name: "саморобна електрична дубинка"
  parent: BaseItem
  id: Stunprod
  description: "Електродубинка для незаконного позбавлення дієздатності."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Melee/stunprod.rsi
    layers:
    - state: stunprod_off
      map: [ "enum.ToggleVisuals.Layer" ]
  - type: ItemToggle
    soundActivate:
      collection: sparks
      params:
        variation: 0.250
    soundDeactivate:
      collection: sparks
      params:
        variation: 0.250
    soundFailToActivate:
      path: /Audio/Machines/button.ogg
      params:
        variation: 0.250
  - type: ItemToggleMeleeWeapon
    activatedDamage:
      types:
        Shock: 5
  - type: ItemToggleDamageOtherOnHit
  - type: Stunbaton
    energyPerUse: 120
  - type: MeleeWeapon
    wideAnimationRotation: -135
    attackRate: 1.1
    range: 1.6
    damage:
      types:
        Blunt: 7.5
    bluntStaminaDamageFactor: 2.0
    heavyRateModifier: 1.25
    heavyDamageBaseModifier: 1.2
    animation: WeaponArcThrust
  - type: DamageOtherOnHit
  - type: StaminaDamageOnHit
    damage: 22
    sound: /Audio/Weapons/egloves.ogg
  - type: StaminaDamageOnCollide
    damage: 22
    sound: /Audio/Weapons/egloves.ogg
  - type: Battery
    maxCharge: 360
    startingCharge: 360
  - type: UseDelay
  - type: Item
    heldPrefix: off
    size: Normal
  - type: Clothing
    sprite: Objects/Weapons/Melee/stunprod.rsi
    quickEquip: false
    slots:
    - back
  - type: DisarmMalus
    malus: 0.225
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ToggleVisuals.Toggled:
        enum.ToggleVisuals.Layer:
          True: {state: stunprod_on}
          False: {state: stunprod_off}
  - type: StaticPrice
    price: 100
  - type: Construction
    graph: makeshiftstunprod
    node: msstunprod
