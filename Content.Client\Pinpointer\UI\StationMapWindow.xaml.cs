using System.Numerics;
using Content.Client.UserInterface.Controls;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Map;
using Content.Shared.Pinpointer;

namespace Content.Client.Pinpointer.UI;

[GenerateTypedNameReferences]
public sealed partial class StationMapWindow : FancyWindow
{
    [Dependency] private readonly IEntityManager _entMan = default!;

    private readonly List<StationMapBeaconControl> _buttons = new();

    public StationMapWindow()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        FilterBar.OnTextChanged += (bar) => OnFilterChanged(bar.Text);
    }

    public void Set(string stationName, EntityUid? mapUid, EntityUid? trackedEntity)
    {
        NavMapScreen.MapUid = mapUid;

        if (trackedEntity != null)
            NavMapScreen.TrackedCoordinates.Add(new EntityCoordinates(trackedEntity.Value, Vector2.Zero), (true, Color.Cyan));

        if (!string.IsNullOrEmpty(stationName))
        {
            StationName.Text = stationName;
        }

        NavMapScreen.ForceNavMapUpdate();
        UpdateBeaconList(mapUid);
    }

    public void OnFilterChanged(string newFilter)
    {
        foreach (var button in _buttons)
        {
            button.Visible = string.IsNullOrEmpty(newFilter) || (
                !string.IsNullOrEmpty(button.Label) &&
                button.Label.Contains(newFilter, StringComparison.OrdinalIgnoreCase)
            );
        };
    }

    public void UpdateBeaconList(EntityUid? mapUid)
    {
        BeaconButtons.Children.Clear();
        _buttons.Clear();

        if (!mapUid.HasValue)
            return;

        if (!_entMan.TryGetComponent<NavMapComponent>(mapUid, out var navMap))
            return;

        foreach (var beacon in navMap.Beacons.Values)
        {
            var button = new StationMapBeaconControl(mapUid.Value, beacon);

            button.OnPressed += NavMapScreen.CenterToCoordinates;

            _buttons.Add(button);
        }

        _buttons.Sort();

        foreach (var button in _buttons)
            BeaconButtons.AddChild(button);
    }
}