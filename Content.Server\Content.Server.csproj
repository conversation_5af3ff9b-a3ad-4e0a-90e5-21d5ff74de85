<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <!-- Work around https://github.com/dotnet/project-system/issues/4314 -->
    <TargetFramework>$(TargetFramework)</TargetFramework>
    <LangVersion>12</LangVersion>
    <IsPackable>false</IsPackable>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <OutputPath>..\bin\Content.Server\</OutputPath>
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
    <OutputType Condition="'$(FullRelease)' != 'True'">Exe</OutputType>
    <NoWarn>1998</NoWarn>
    <WarningsAsErrors>nullable</WarningsAsErrors>
    <Nullable>enable</Nullable>
    <ServerGarbageCollection>true</ServerGarbageCollection>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="JetBrains.Annotations" PrivateAssets="All" />
    <PackageReference Include="MathNet.Numerics" PrivateAssets="All" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Content.Packaging\Content.Packaging.csproj" />
    <ProjectReference Include="..\Content.Server.Database\Content.Server.Database.csproj" />
    <ProjectReference Include="..\Content.Shared.Database\Content.Shared.Database.csproj" />
    <ProjectReference Include="..\RobustToolbox\Lidgren.Network\Lidgren.Network.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.Shared.Maths\Robust.Shared.Maths.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.Shared\Robust.Shared.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.Server\Robust.Server.csproj" />
    <ProjectReference Include="..\Content.Shared\Content.Shared.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Objectives\Interfaces\" />
    <Folder Include="_Goobstation\Administration\Systems\" />
    <Folder Include="_EE\Shadowling\Systems\Objectives\" />
  </ItemGroup>
  <Import Project="..\RobustToolbox\MSBuild\Robust.Properties.targets" />
</Project>
