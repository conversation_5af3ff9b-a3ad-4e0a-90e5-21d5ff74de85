
- type: entity
  id: SpawnPointMedicalBorg
  parent: SpawnPointJobBase
  name: "медичний кіборг"
  components:
  - type: SpawnPoint
    job_id: MedicalBorg
  - type: Sprite
    layers:
    - state: green
    - sprite: Mobs/Silicon/chassis.rsi
      state: medical
    - sprite: Mobs/Silicon/chassis.rsi
      state: medical_e

- type: entity
  id: SpawnPointChiefJustice
  parent: SpawnPointJobBase
  name: "головний суддя"
  components:
  - type: SpawnPoint
    job_id: ChiefJustice
  - type: Sprite
    layers:
    - state: green
    - sprite: DeltaV/Markers/jobs.rsi
      state: chiefjustice

- type: entity
  id: SpawnPointClerk
  parent: SpawnPointJobBase
  name: "клерк"
  components:
  - type: SpawnPoint
    job_id: Clerk
  - type: Sprite
    layers:
    - state: green
    - sprite: DeltaV/Markers/jobs.rsi
      state: clerk

- type: entity
  id: SpawnPointProsecutor
  parent: SpawnPointJobBase
  name: "прокурор"
  components:
  - type: SpawnPoint
    job_id: Prosecutor
  - type: Sprite
    layers:
    - state: green
    - sprite: DeltaV/Markers/jobs.rsi
      state: prosecutor

- type: entity
  id: SpawnPointCourier
  parent: SpawnPointJobBase
  name: "кур'єр"
  components:
  - type: SpawnPoint
    job_id: Courier
  - type: Sprite
    layers:
    - state: green
    - sprite: DeltaV/Markers/jobs.rsi
      state: courier

- type: entity
  id: SpawnPointAdminAssistant
  parent: SpawnPointJobBase
  name: "адміністративний асистент"
  components:
  - type: SpawnPoint
    job_id: AdministrativeAssistant
  - type: Sprite
    layers:
      - state: green
      - sprite: DeltaV/Markers/jobs.rsi
        state: adminassistant