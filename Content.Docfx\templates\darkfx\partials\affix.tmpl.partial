{{!Copyright (c) Microsoft. All rights reserved. Licensed under the MIT license. See LICENSE file in the project root for full license information.}}

<div class="hidden-sm col-md-2" role="complementary">
  <div class="sideaffix">
    {{^_disableContribution}}
    <div class="contribution">
      <ul class="nav">
        {{#docurl}}
        <li>
          <a href="{{docurl}}" class="contribution-link">{{__global.improveThisDoc}}</a>
        </li>
        {{/docurl}}
        {{#sourceurl}}
        <li>
          <a href="{{sourceurl}}" class="contribution-link">{{__global.viewSource}}</a>
        </li>
        {{/sourceurl}}
      </ul>
    </div>
    {{/_disableContribution}}
    <div class="toggle-mode">
      <div class="icon">
        <i aria-hidden="true">☀</i>
      </div>
      <label class="switch">
        <input type="checkbox" id="switch-style">
        <span class="slider round"></span>
      </label>
      <div class="icon">
        <i aria-hidden="true">☾</i>
      </div>
    </div>

    <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
    <h5>{{__global.inThisArticle}}</h5>
    <div></div>
    <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
    </nav>
  </div>
</div>
