﻿- type: entity
  id: BaseStockPart
  name: "стокова частина"
  parent: BaseItem
  description: "Що?"
  abstract: true
  components:
    - type: Sprite
      sprite: Objects/Misc/stock_parts.rsi
      scale: 0.8, 0.8
    - type: Item
      size: Tiny
    - type: Stack
      count: 1
    - type: PhysicalComposition #Goobstation - Recycle update
      materialComposition:
        Steel: 12
        Plastic: 12
    - type: StockPart # Goobstation

- type: entity
  id: CapacitorStockPart
  name: "конденсатор"
  parent: BaseStockPart
  description: "Базовий конденсатор, який використовується в побудові різноманітних пристроїв."
  suffix: Rating 1
  components:
    - type: Sprite
      state: capacitor
    - type: MachinePart
      part: Capacitor
      rating: 1
    - type: ReverseEngineering # Nyano
      recipes:
        - CapacitorStockPart
    - type: Tag
      tags:
        - CapacitorStockPart
    - type: Stack
      stackType: Capacitor

- type: entity
  id: MicroManipulatorStockPart
  name: "маніпулятор"
  parent: BaseStockPart
  description: "Базовий маніпулятор, що використовується при конструюванні різноманітних пристроїв."
  suffix: Rating 1
  components:
    - type: Sprite
      state: micro_mani
    - type: MachinePart
      part: Manipulator
      rating: 1
    - type: Stack
      stackType: MicroManipulator
    - type: ReverseEngineering # Nyano
      recipes:
        - MicroManipulatorStockPart

- type: entity
  id: MatterBinStockPart
  name: "контейнер матерії"
  parent: BaseStockPart
  description: "Базовий контейнер для речовин, який використовується в конструкції різноманітних пристроїв."
  suffix: Rating 1
  components:
    - type: Sprite
      state: matter_bin
    - type: MachinePart
      part: MatterBin
      rating: 1
    - type: Stack
      stackType: MatterBin
    - type: ReverseEngineering # Nyano
      recipes:
        - MatterBinStockPart

# Rating 2

- type: entity
  id: AdvancedCapacitorStockPart
  name: "вдосконалений конденсатор"
  parent: CapacitorStockPart
  description: "Вдосконалений конденсатор, який використовується в конструкції різноманітних пристроїв."
  suffix: Rating 2
  components:
    - type: Sprite
      state: adv_capacitor
    - type: MachinePart
      rating: 2
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - AdvancedCapacitorStockPart

- type: entity
  id: NanoManipulatorStockPart
  name: "просунутий маніпулятор"
  parent: MicroManipulatorStockPart
  description: "Вдосконалений маніпулятор, який використовується в конструюванні різноманітних пристроїв."
  suffix: Rating 2
  components:
    - type: Sprite
      state: nano_mani
    - type: MachinePart
      rating: 2
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - NanoManipulatorStockPart

- type: entity
  id: AdvancedMatterBinStockPart
  name: "просунутий контейнер матерії"
  parent: MatterBinStockPart
  description: "Удосконалений контейнер для речовин, що використовується в конструкції різноманітних пристроїв."
  suffix: Rating 2
  components:
    - type: Sprite
      state: advanced_matter_bin
    - type: MachinePart
      rating: 2
    - type: ReverseEngineering # Nyano
      difficulty: 2
      recipes:
        - AdvancedMatterBinStockPart

# Rating 3

- type: entity
  id: SuperCapacitorStockPart
  name: "суперконденсатор"
  parent: CapacitorStockPart
  description: "Суперконденсатор, який використовується в конструкції різноманітних пристроїв."
  suffix: Rating 3
  components:
    - type: Sprite
      state: super_capacitor
    - type: MachinePart
      rating: 3
    - type: ReverseEngineering # Nyano
      difficulty: 3
      recipes:
        - SuperCapacitorStockPart

- type: entity
  id: PicoManipulatorStockPart
  name: "супер маніпулятор"
  parent: MicroManipulatorStockPart
  description: "Суперманіпулятор, який використовується в конструюванні різноманітних пристроїв."
  suffix: Rating 3
  components:
    - type: Sprite
      state: pico_mani
    - type: MachinePart
      rating: 3
    - type: ReverseEngineering # Nyano
      difficulty: 3
      recipes:
        - PicoManipulatorStockPart

- type: entity
  id: SuperMatterBinStockPart
  name: "контейнер для надматерії"
  parent: MatterBinStockPart
  description: "Контейнер для надматерії, що використовується в будівництві різноманітних пристроїв."
  suffix: Rating 3
  components:
    - type: Sprite
      state: super_matter_bin
    - type: MachinePart
      rating: 3
    - type: ReverseEngineering # Nyano
      difficulty: 3
      recipes:
        - SuperMatterBinStockPart

# Rating 4

- type: entity
  id: BluespaceCapacitorStockPart
  name: "блюспейс конденсатор"
  parent: CapacitorStockPart
  description: "Конденсатор для блюзового простору, що використовується в конструкції різноманітних пристроїв."
  suffix: Rating 4
  components:
    - type: Sprite
      state: quadratic_capacitor
    - type: MachinePart
      rating: 4
    - type: ReverseEngineering # Nyano
      difficulty: 4
      recipes:
        - BluespaceCapacitorStockPart

- type: entity
  id: BluespaceManipulatorStockPart
  name: "блюспейс маніпулятор"
  parent: MicroManipulatorStockPart
  description: "Маніпулятор, що використовується при конструюванні різноманітних пристроїв."
  suffix: Rating 4
  components:
    - type: Sprite
      state: femto_mani
    - type: MachinePart
      rating: 4
    - type: ReverseEngineering # Nyano
      difficulty: 4
      recipes:
        - BluespaceManipulatorStockPart

- type: entity
  id: BluespaceMatterBinStockPart
  name: "блюспейс контейнер матерії"
  parent: MatterBinStockPart
  description: "Бункер для сміття з блакитного простору, що використовується в конструкції різноманітних пристроїв."
  suffix: Rating 4
  components:
    - type: Sprite
      state: bluespace_matter_bin
    - type: MachinePart
      rating: 4
    - type: ReverseEngineering # Nyano
      difficulty: 4
      recipes:
        -  BluespaceMatterBinStockPart
