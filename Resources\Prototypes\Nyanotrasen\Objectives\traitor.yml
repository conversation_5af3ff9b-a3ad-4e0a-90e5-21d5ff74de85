#- type: entity
#  id: BecomeGolemObjective
#  parent: BaseTraitorObjective
#  name: objective-condition-become-golem-title
#  description: objective-condition-become-golem-description.
#  categories: [ HideSpawnMenu ]
#  components:
#  - type: NotJobRequirement
#    job: Chaplain
#  - type: Objective
#    difficulty: 3.5
#    #unique: false
#    icon:
#      sprite: Nyanotrasen/Mobs/Species/Golem/cult.rsi
#      state: full
#  - type: ObjectiveBlacklistRequirement
#    blacklist:
#      components:
#      - BecomePsionicCondition
#  - type: BecomeGolemCondition

- type: entity
  id: RaiseGlimmerObjective
  parent: BaseTraitorObjective
  categories: [ HideSpawnMenu ]
  name: "Підніміть Гліммер."
  description: "Отримайте мерехтіння вище вказаної кількості."
  components:
  - type: Objective
    difficulty: 2.5
    #unique: false
    icon:
      sprite: Nyanotrasen/Icons/psi.rsi
      state: psi
  - type: RaiseGlimmerCondition
    target: 500
