- type: entity
  id: BaseRoofMarker
  abstract: true
  placement:
    mode: SnapgridCenter
  components:
  - type: Transform
    anchored: true
  - type: Sprite
    drawdepth: Overdoors
    sprite: Markers/cross.rsi

- type: entity
  id: RoofMarker
  name: "Дах"
  suffix: Enabled
  parent: BaseRoofMarker
  components:
  - type: SetRoof
    value: true
  - type: Sprite
    layers:
    - state: green
      shader: unshaded

- type: entity
  id: NoRoofMarker
  name: "Дах"
  suffix: Disabled
  parent: BaseRoofMarker
  components:
  - type: SetRoof
    value: false
  - type: Sprite
    layers:
    - state: red
      shader: unshaded
