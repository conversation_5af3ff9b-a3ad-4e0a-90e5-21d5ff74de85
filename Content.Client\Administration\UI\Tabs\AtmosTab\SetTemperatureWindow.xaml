﻿<DefaultWindow
    xmlns="https://spacestation14.io" Title="{Loc Set Temperature}">
    <BoxContainer Orientation="Vertical">
        <BoxContainer Orientation="Horizontal">
            <Label Text="{Loc Grid}" MinSize="100 0" />
            <Control MinSize="50 0" />
            <OptionButton Name="GridOptions" MinSize="100 0" HorizontalExpand="True" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal">
            <Label Text="{Loc TileX}" MinSize="100 0" />
            <Control MinSize="50 0" />
            <SpinBox Name="TileXSpin" MinSize="100 0" HorizontalExpand="True" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal">
            <Label Text="{Loc TileY}" MinSize="100 0" />
            <Control MinSize="50 0" />
            <SpinBox Name="TileYSpin" MinSize="100 0" HorizontalExpand="True" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal">
            <Label Text="{Loc Temperature}" MinSize="100 0" />
            <Control MinSize="50 0" />
            <SpinBox Name="TemperatureSpin" MinSize="100 0" HorizontalExpand="True" />
        </BoxContainer>
        <Button Name="SubmitButton" Text="{Loc Set Temperature}" />
    </BoxContainer>
</DefaultWindow>
