- type: entity
  id: SpawnPointGladiator
  parent: SpawnPointJobBase
  name: "гладіатор"
  components:
  - type: SpawnPoint
    job_id: Gladiator
  - type: Sprite
    layers:
      - state: green
      - sprite: DeltaV/Markers/jobs.rsi
        state: nyanogladiator

- type: entity
  id: SpawnPointPrisoner
  parent: SpawnPointJobBase
  name: "в'язень"
  components:
  - type: SpawnPoint
    job_id: Prisoner
  - type: Sprite
    layers:
      - state: green
      - state: prisoner

- type: entity
  id: SpawnPointPrisonGuard
  parent: SpawnPointJobBase
  name: "тюремний охоронець"
  components:
  - type: SpawnPoint
    job_id: PrisonGuard
  - type: Sprite
    layers:
      - state: green
      - sprite: DeltaV/Markers/jobs.rsi
        state: nyanoprisonguard

- type: entity
  id: SpawnPointMailCarrier
  parent: SpawnPointJobBase
  name: "листоноша" # Delta V - Mail Carrier to Courier replacement
  components:
  - type: SpawnPoint
    job_id: MailCarrier
  - type: Sprite
    layers:
      - state: green
      - sprite: DeltaV/Markers/jobs.rsi
        state: courier # Delta V - Mail Carrier to Courier replacement

- type: entity
  id: SpawnPointMartialArtist
  parent: SpawnPointJobBase
  name: "майстер бойових мистецтв"
  components:
  - type: SpawnPoint
    job_id: MartialArtist
  - type: Sprite
    layers:
      - state: green
      - sprite: DeltaV/Markers/jobs.rsi
        state: nyanomartialartist

- type: entity
  id: SpawnPointForensicMantis
  parent: SpawnPointJobBase
  name: "мантіс"
  components:
  - type: SpawnPoint
    job_id: ForensicMantis
  - type: Sprite
    layers:
      - state: green
      - sprite: DeltaV/Markers/jobs.rsi
        state: nyanomantis
