﻿using Content.Server.Administration;
using Content.Server.Roles.Jobs;
using Content.Shared.Administration;
using Content.Shared.Players;
using Content.Shared.Roles;
using Robust.Server.Player;
using Robust.Shared.Console;
using Robust.Shared.Prototypes;

namespace Content.Server.Roles
{
    [AdminCommand(AdminFlags.Admin)]
    public sealed class AddRoleCommand : IConsoleCommand
    {
        [Dependency] private readonly EntityManager _entityManager = default!;

        public string Command => "addrole";

        public string Description => "Додає роль до розуму гравця";

        public string Help => "addrole <session ID> <role>";

        public void Execute(IConsoleShell shell, string argStr, string[] args)
        {
            if (args.Length != 2)
            {
                shell.WriteLine("Expected exactly 2 arguments.");
                return;
            }

            var mgr = IoCManager.Resolve<IPlayerManager>();
            if (!mgr.TryGetPlayerDataByUsername(args[0], out var data))
            {
                shell.WriteLine("Can't find that mind");
                return;
            }

            var mind = data.ContentData()?.Mind;
            if (mind == null)
            {
                shell.WriteLine("Can't find that mind");
                return;
            }

            var prototypeManager = IoCManager.Resolve<IPrototypeManager>();
            if (!prototypeManager.TryIndex<JobPrototype>(args[1], out var jobPrototype))
            {
                shell.WriteLine("Can't find that role");
                return;
            }

            var jobs = _entityManager.System<JobSystem>();
            if (jobs.MindHasJobWithId(mind, jobPrototype.Name))
            {
                shell.WriteLine("Mind already has that role");
                return;
            }

            jobs.MindAddJob(mind.Value, args[1]);
        }
    }
}
