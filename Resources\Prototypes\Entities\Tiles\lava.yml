- type: entity
  id: FloorLavaEntity
  name: "лава"
  description: "Не застрибуй. Воно того не варте, хоч би яким смішним воно не було."
  placement:
    mode: SnapgridCenter
    snap:
    - Wall
  components:
  - type: TileEmission
    color: "#FF4500"
  - type: StepTrigger
    requiredTriggeredSpeed: 0
    intersectRatio: 0.1
    blacklist:
      tags:
        - Catwalk
    triggerGroups:
      types:
        - Lava
  - type: TileEntityEffect
    effects:
    - !type:FlammableReaction
      multiplier: 2.5 # Lavaland Change
      multiplierOnExisting: 0.75
    - !type:Ignite
  # Lavaland Change start
    - !type:AdjustTemperature
      amount: 10000 # thermal energy, not temperature!
    - !type:MovespeedModifier
      walkSpeedModifier: 0.6
      sprintSpeedModifier: 0.6
  # Lavaland Change end
  # Goobstation - fishing
  - type: FishingSpot
    fishList: !type:NestedSelector
      tableId: LavaFishingLootTable
    fishDefaultTimer: 25.0
    fishTimerVariety: 15.0
  # Goobstation - fishing
  - type: Transform
    anchored: true
  - type: SyncSprite
  - type: Clickable
  - type: Sprite
    sprite: Tiles/Planet/lava.rsi
    drawdepth: BelowFloor
    layers:
      - state: lava
        shader: unshaded
  - type: Icon
    sprite: Tiles/Planet/lava.rsi
    state: full
  - type: IconSmooth
    key: floor
    base: lava
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.5,-0.5,0.5,0.5"
        layer:
          - SlipLayer
        mask:
          - ItemMask
        density: 1000
        hard: false
    # Goobstation - fishing
      fishing:
        shape:
          !type:PhysShapeCircle
          radius: 0.4
        layer:
        - ItemMask
        mask:
        - HighImpassable
        density: 1000
        hard: false
    # Goobstation - fishing
  - type: Tag
    tags:
      - HideContextMenu
