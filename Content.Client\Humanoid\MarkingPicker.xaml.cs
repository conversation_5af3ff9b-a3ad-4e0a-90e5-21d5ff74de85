using System.Linq;
using System.Numerics;
using Content.Client.Stylesheets;
using Content.Shared.Humanoid;
using Content.Shared.Humanoid.Markings;
using Content.Shared.Humanoid.Prototypes;
using Robust.Client.AutoGenerated;
using Robust.Client.Graphics;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Client.Utility;
using Robust.Shared.Prototypes;
using Robust.Shared.Utility;
using static Robust.Client.UserInterface.Controls.BoxContainer;

namespace Content.Client.Humanoid;

[GenerateTypedNameReferences]
public sealed partial class MarkingPicker : Control
{
    [Dependency] private readonly MarkingManager _markingManager = default!;
    [Dependency] private readonly IPrototypeManager _prototypeManager = default!;

    public Action<MarkingSet>? OnMarkingAdded;
    public Action<MarkingSet>? OnMarkingRemoved;
    public Action<MarkingSet>? OnMarkingColorChange;
    public Action<MarkingSet>? OnMarkingRankChange;

    private List<Color> _currentMarkingColors = new();

    private Button? _selectedMarking;
    private MarkingCategories _selectedMarkingCategory = MarkingCategories.Chest;

    private MarkingSet _currentMarkings = new();

    private List<MarkingCategories> _markingCategories = Enum.GetValues<MarkingCategories>().ToList();

    private string _currentSpecies = SharedHumanoidAppearanceSystem.DefaultSpecies;
    private Sex _currentSex = Sex.Unsexed;
    public Color CurrentSkinColor = Color.White;
    public Color CurrentEyeColor = Color.Black;
    public Marking? HairMarking;
    public Marking? FacialHairMarking;

    private readonly HashSet<MarkingCategories> _ignoreCategories = new();

    public string IgnoreCategories
    {
        get => string.Join(',',  _ignoreCategories);
        set
        {
            _ignoreCategories.Clear();
            var split = value.Split(',');
            foreach (var category in split)
            {
                if (!Enum.TryParse(category, out MarkingCategories categoryParse))
                    continue;

                _ignoreCategories.Add(categoryParse);
            }

            SetupCategoryButtons();
        }
    }

    public bool Forced { get; set; }

    private bool _ignoreSpecies;

    public bool IgnoreSpecies
    {
        get => _ignoreSpecies;
        set
        {
            _ignoreSpecies = value;
            Populate(CMarkingSearch.Text);
        }
    }

    public void SetData(List<Marking> newMarkings, string species, Sex sex, Color skinColor, Color eyeColor)
    {
        var pointsProto = _prototypeManager
            .Index<SpeciesPrototype>(species).MarkingPoints;
        _currentMarkings = new(newMarkings, pointsProto, _markingManager);

        if (!IgnoreSpecies)
            _currentMarkings.EnsureSpecies(species, skinColor, _markingManager); // should be validated server-side but it can't hurt

        _currentSpecies = species;
        _currentSex = sex;
        CurrentSkinColor = skinColor;
        CurrentEyeColor = eyeColor;

        Populate(CMarkingSearch.Text);
    }

    public void SetData(MarkingSet set, string species, Sex sex, Color skinColor, Color eyeColor)
    {
        _currentMarkings = set;

        if (!IgnoreSpecies)
            _currentMarkings.EnsureSpecies(species, skinColor, _markingManager); // should be validated server-side but it can't hurt

        _currentSpecies = species;
        _currentSex = sex;
        CurrentSkinColor = skinColor;
        CurrentEyeColor = eyeColor;

        Populate(CMarkingSearch.Text);
    }

    public MarkingPicker()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        CMarkingCategoryButton.OnItemSelected += OnCategoryChange;
        CMarkingSearch.OnTextChanged += args => Populate(args.Text);
        ResetButton.OnPressed += _ =>
        {
            CMarkingSearch.Text = string.Empty;
            Populate(string.Empty);
        };

        Populate(CMarkingSearch.Text);
    }

    private void SetupCategoryButtons()
    {
        CMarkingCategoryButton.Clear();

        var validCategories = new List<MarkingCategories>();
        for (var i = 0; i < _markingCategories.Count; i++)
        {
            var category = _markingCategories[i];
            var markings = GetMarkings(category);
            if (_ignoreCategories.Contains(category) ||
                markings.Count == 0)
                continue;

            validCategories.Add(category);
            CMarkingCategoryButton.AddItem(Loc.GetString($"markings-category-{category.ToString()}"), i);
        }

        if (validCategories.Contains(_selectedMarkingCategory))
            CMarkingCategoryButton.SelectId(_markingCategories.IndexOf(_selectedMarkingCategory));
        else if (validCategories.Count > 0)
            _selectedMarkingCategory = validCategories[0];
        else
            _selectedMarkingCategory = MarkingCategories.Chest;
    }

    private string GetMarkingName(MarkingPrototype marking) => Loc.GetString($"marking-{marking.ID}");

    private List<string> GetMarkingStateNames(MarkingPrototype marking)
    {
        List<string> result = new();
        foreach (var markingState in marking.Sprites)
        {
            switch (markingState)
            {
                case SpriteSpecifier.Rsi rsi:
                    result.Add(Loc.GetString($"marking-{marking.ID}-{rsi.RsiState}"));
                    break;
                case SpriteSpecifier.Texture texture:
                    result.Add(Loc.GetString($"marking-{marking.ID}-{texture.TexturePath.Filename}"));
                    break;
            }
        }

        return result;
    }

    private IReadOnlyDictionary<string, MarkingPrototype> GetMarkings(MarkingCategories category)
    {
        return IgnoreSpecies
            ? _markingManager.MarkingsByCategoryAndSex(category, _currentSex)
            : _markingManager.MarkingsByCategoryAndSpeciesAndSex(category, _currentSpecies, _currentSex);
    }

    public void Populate(string filter)
    {
        SetupCategoryButtons();

        Markings.DisposeAllChildren();

        var sortedMarkings = GetMarkings(_selectedMarkingCategory).Values.Where(m =>
            m.ID.ToLower().Contains(filter.ToLower()) ||
            GetMarkingName(m).ToLower().Contains(filter.ToLower())
        ).OrderBy(p => Loc.GetString(GetMarkingName(p)));

        foreach (var marking in sortedMarkings)
        {
            var cont = new BoxContainer
            {
                Orientation = LayoutOrientation.Horizontal,
                HorizontalExpand = true,
            };

            //TODO: Show animations and the preview on the player
            var preview = new PanelContainer
            {
                PanelOverride = new StyleBoxFlat { BackgroundColor = Color.FromHex("#2f2f2f"), },
                Children =
                {
                    new TextureRect
                    {
                        Texture = marking.Sprites[0].DirFrame0().TextureFor(
                            Enum.TryParse<Direction>(marking.PreviewDirection, out var dir) ? dir : Direction.South),
                    },
                },
            };

            var item = new Button
            {
                Name = marking.ID,
                Text = Loc.GetString($"marking-{marking.ID}"),
                StyleClasses = { StyleBase.ButtonOpenBoth, },
                ToggleMode = true,
                Pressed = _currentMarkings.TryGetMarking(_selectedMarkingCategory, marking.ID, out _),
            };
            var customize = new Button
            {
                Text = "Customize",
                StyleClasses = { StyleBase.ButtonOpenLeft, },
                Disabled = !item.Pressed,
            };

            item.OnToggled += _ =>
            {
                // Add the marking if they have points for it
                item.Pressed = item.Pressed && !Forced ? _currentMarkings.PointsLeft(_selectedMarkingCategory) > 0 : item.Pressed;
                if (item.Pressed)
                    MarkingAdd(_prototypeManager.Index<MarkingPrototype>(item.Name));
                else
                    MarkingRemove(_prototypeManager.Index<MarkingPrototype>(item.Name));
                customize.Disabled = !item.Pressed;
            };
            customize.OnPressed += _ =>
            {
                _selectedMarking = item;
                OnColorChangePressed(_prototypeManager.Index<MarkingPrototype>(item.Name));
            };

            cont.AddChild(preview);
            cont.AddChild(item);
            cont.AddChild(customize);
            Markings.AddChild(cont);
        }

        CMarkingPoints.Visible = _currentMarkings.PointsLeft(_selectedMarkingCategory) != -1;
        UpdatePoints();
    }


    // repopulate in case markings are restricted,
    // and also filter out any markings that are now invalid
    // attempt to preserve any existing markings as well:
    // it would be frustrating to otherwise have all markings
    // cleared, imo
    public void SetSpecies(string species)
    {
        _currentSpecies = species;
        var markingList = _currentMarkings.GetForwardEnumerator().ToList();

        var speciesPrototype = _prototypeManager.Index<SpeciesPrototype>(species);

        _currentMarkings = new(markingList, speciesPrototype.MarkingPoints, _markingManager, _prototypeManager);
        _currentMarkings.EnsureSpecies(species, null, _markingManager);
        _currentMarkings.EnsureSexes(_currentSex, _markingManager);

        Populate(CMarkingSearch.Text);
    }

    public void SetSex(Sex sex)
    {
        _currentSex = sex;
        var markingList = _currentMarkings.GetForwardEnumerator().ToList();

        var speciesPrototype = _prototypeManager.Index<SpeciesPrototype>(_currentSpecies);

        _currentMarkings = new(markingList, speciesPrototype.MarkingPoints, _markingManager, _prototypeManager);
        _currentMarkings.EnsureSpecies(_currentSpecies, null, _markingManager);
        _currentMarkings.EnsureSexes(_currentSex, _markingManager);

        Populate(CMarkingSearch.Text);
    }

    private void UpdatePoints()
    {
        var count = _currentMarkings.PointsLeft(_selectedMarkingCategory);
        if (count > -1)
            CMarkingPoints.Text = Loc.GetString("marking-points-remaining", ("points", count));
    }

    private void OnCategoryChange(OptionButton.ItemSelectedEventArgs category)
    {
        CMarkingCategoryButton.SelectId(category.Id);
        _selectedMarkingCategory = _markingCategories[category.Id];
        Populate(CMarkingSearch.Text);
        UpdatePoints();
    }

    private void OnColorChangePressed(MarkingPrototype prototype)
    {
        if (prototype.ForcedColoring)
        {
            CMarkingColors.Visible = false;

            return;
        }

        var stateNames = GetMarkingStateNames(prototype);
        _currentMarkingColors.Clear();
        CMarkingColors.DisposeAllChildren();
        for (var i = 0; i < prototype.Sprites.Count; i++)
        {
            var colorContainer = new BoxContainer
            {
                Orientation = LayoutOrientation.Vertical,
            };

            CMarkingColors.AddChild(colorContainer);

            var colorSelector = new ColorSelectorSliders();

            colorContainer.AddChild(new Label { Text = $"{stateNames[i]} color:" });
            colorContainer.AddChild(colorSelector);

            var listing = _currentMarkings.Markings[_selectedMarkingCategory];

            var color = listing[^1].MarkingColors[i];
            var currentColor = new Color(
                color.RByte,
                color.GByte,
                color.BByte
            );
            colorSelector.Color = currentColor;
            _currentMarkingColors.Add(currentColor);
            var colorIndex = _currentMarkingColors.Count - 1;

            colorSelector.OnColorChanged += Changed;
            continue;

            void Changed(Color _)
            {
                _currentMarkingColors[colorIndex] = colorSelector.Color;

                ColorChanged(prototype, colorIndex);
            }
        }

        CMarkingColors.Visible = true;
    }

    private void ColorChanged(MarkingPrototype markingPrototype, int colorIndex)
    {
        if (_selectedMarking is null)
            return;
        var markingIndex = _currentMarkings.FindIndexOf(_selectedMarkingCategory, markingPrototype.ID);

        if (markingIndex < 0)
            return;

        var marking = new Marking(_currentMarkings.Markings[_selectedMarkingCategory][markingIndex]);
        marking.SetColor(colorIndex, _currentMarkingColors[colorIndex]);
        _currentMarkings.Replace(_selectedMarkingCategory, markingIndex, marking);

        OnMarkingColorChange?.Invoke(_currentMarkings);
    }

    private void MarkingAdd(MarkingPrototype marking)
    {
        if (_currentMarkings.PointsLeft(_selectedMarkingCategory) == 0 && !Forced)
            return;

        var markingObject = marking.AsMarking();

        // We need add hair markings in cloned set manually because _currentMarkings doesn't have it
        var markingSet = new MarkingSet(_currentMarkings);
        if (HairMarking != null)
            markingSet.AddBack(MarkingCategories.Hair, HairMarking);
        if (FacialHairMarking != null)
            markingSet.AddBack(MarkingCategories.FacialHair, FacialHairMarking);

        if (!_markingManager.MustMatchSkin(_currentSpecies, marking.BodyPart, out var _, _prototypeManager))
        {
            // Do default coloring
            var colors = MarkingColoring.GetMarkingLayerColors(
                marking,
                CurrentSkinColor,
                CurrentEyeColor,
                markingSet
            );
            for (var i = 0; i < colors.Count; i++)
                markingObject.SetColor(i, colors[i]);
        }
        else
            // Color everything in skin color
            for (var i = 0; i < marking.Sprites.Count; i++)
                markingObject.SetColor(i, CurrentSkinColor);

        markingObject.Forced = Forced;

        _currentMarkings.AddBack(_selectedMarkingCategory, markingObject);

        UpdatePoints();
        OnMarkingAdded?.Invoke(_currentMarkings);
    }

    private void MarkingRemove(MarkingPrototype marking)
    {
        _currentMarkings.Remove(_selectedMarkingCategory, marking.ID);

        UpdatePoints();
        CMarkingColors.Visible = _selectedMarking?.Name != marking.ID;
        OnMarkingRemoved?.Invoke(_currentMarkings);
    }
}
