- type: entity
  id: Window
  parent: BaseStructure
  name: "вікно"
  description: "Чисто."
  placement:
    mode: SnapgridCenter
    snap:
    - Window
  components:
  - type: MeleeSound
    soundGroups:
      Brute:
        collection: GlassSmack
  - type: WallMount
    arc: 360 # interact despite grilles
  - type: Tag
    tags:
      - ForceFixRotations
      - Window
      - WeldbotFixableStructure
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Windows/window.rsi
  - type: Icon
    sprite: Structures/Windows/window.rsi
    state: full
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb {}
        mask:
        - FullTileMask
        layer:
        - GlassLayer
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: Glass
  - type: ExaminableDamage
    messages: WindowMessages
  - type: Repairable
  - type: RCDDeconstructable
    cost: 6
    delay: 4
    fx: EffectRCDDeconstruct4
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 150 #excess damage (nuke?). avoid computational cost of spawning entities.
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlass:
            min: 1
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Airtight
  - type: IconSmooth
    key: walls
    base: window
  - type: Construction
    graph: Window
    node: window
  - type: Appearance
  - type: DamageVisuals
    thresholds: [4, 8, 12]
    damageDivisor: 3.333
    trackAllDamage: true
    damageOverlay:
      sprite: Structures/Windows/cracks.rsi
  - type: StaticPrice
    price: 100
  - type: BlockWeather
  - type: InteractionVerbs
    allowedVerbs:
    - KnockOn

- type: entity
  id: WindowRCDResistant
  parent: Window
  abstract: true
  components:
  - type: RCDDeconstructable
    deconstructable: false

- type: entity
  id: WindowDirectional
  parent: BaseStructure
  name: "спрямоване вікно"
  description: "Чисто."
  placement:
    mode: SnapgridCenter
    snap:
    - Window
  components:
  - type: WallMount
    arc: 360 # interact despite grilles
  - type: Tag
    tags:
      - Window
      - Directional #Delta V - Summary: Allows the tilewindow mapping command to work.
      - WeldbotFixableStructure
  - type: MeleeSound
    soundGroups:
      Brute:
        collection: GlassSmack
  - type: Sprite
    drawdepth: Mobs
    sprite: Structures/Windows/directional.rsi
    state: window
  - type: Icon
    sprite: Structures/Windows/directional.rsi
    state: window
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.49,-0.49,0.49,-0.36"
        density: 1500
        mask:
        - FullTileMask
        layer:
        - GlassLayer
  - type: Repairable
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Glass
  - type: ExaminableDamage
    messages: WindowMessages
  - type: RCDDeconstructable
    cost: 4
    delay: 2
    fx: EffectRCDDeconstruct2
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 150 #excess damage (nuke?). avoid computational cost of spawning entities.
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
    - trigger:
        !type:DamageTrigger
        damage: 25
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WindowShatter
      - !type:SpawnEntitiesBehavior
        spawn:
          ShardGlass:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Airtight
    noAirWhenFullyAirBlocked: false
    airBlockedDirection:
    - South
  - type: Construction
    graph: WindowDirectional
    node: windowDirectional
  - type: Appearance
  - type: DamageVisuals
    thresholds: [4, 8, 12]
    damageDivisor: 3.333
    trackAllDamage: true
    damageOverlay:
      sprite: Structures/Windows/cracks_directional.rsi
  - type: StaticPrice
    price: 10
  - type: InteractionVerbs
    allowedVerbs:
    - KnockOn

- type: entity
  id: WindowDirectionalRCDResistant
  parent: WindowDirectional
  abstract: true
  components:
  - type: RCDDeconstructable
    deconstructable: false

- type: entity
  id: WindowFrostedDirectional
  parent: WindowDirectional
  name: "спрямоване матове вікно"
  description: "Чисто."
  placement:
    mode: SnapgridCenter
    snap:
    - Window
  components:
  - type: Sprite
    sprite: Structures/Windows/directional.rsi
    state: frosted_window
  - type: Icon
    sprite: Structures/Windows/directional.rsi
    state: frosted_window

- type: entity
  parent: Window
  id: WindowDiagonal
  suffix: diagonal
  placement:
    mode: SnapgridCenter
    snap:
    - Window
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Windows/window_diagonal.rsi
    state: state0
  - type: IconSmooth
    mode: Diagonal
    key: walls
    base: state
  - type: Icon
    sprite: Structures/Windows/window_diagonal.rsi
    state: state0
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PolygonShape
            vertices:
            - "-0.5,-0.5"
            - "0.5,0.5"
            - "0.5,-0.5"
        mask:
        - FullTileMask
        layer:
        - GlassLayer
  - type: Airtight
    noAirWhenFullyAirBlocked: false
    airBlockedDirection:
    - South
    - East
  - type: DamageVisuals
    damageOverlay:
      sprite: Structures/Windows/cracks_diagonal.rsi
  - type: Construction
    graph: WindowDiagonal
    node: windowDiagonal
