- type: entity
  abstract: true
  parent: Clothing
  id: UnsensoredClothingUniformBase
  components:
  - type: Sprite
    state: icon
  - type: Clothing
    slots: [innerclothing]
    equipSound:
      path: /Audio/Items/jumpsuit_equip.ogg
  - type: EmitSoundOnPickup # Not sure why this wasn't set to the base; as all clothes should have clothing sounds.
    sound: /Audio/SimpleStation14/Items/Handling/cloth_pickup.ogg
  - type: EmitSoundOnDrop
    sound: /Audio/SimpleStation14/Items/Handling/cloth_drop.ogg
  - type: EmitSoundOnLand
    sound: /Audio/SimpleStation14/Items/Handling/cloth_drop.ogg
  - type: Butcherable
    butcheringType: Knife
    spawned:
    - id: MaterialCloth1
      amount: 3
  - type: Food
    requiresSpecialDigestion: true
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 30
        reagents:
        - ReagentId: Fiber
          Quantity: 30
  - type: Tag
    tags:
    - ClothMade
    - WhitelistChameleon
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 35
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  abstract: true
  parent: UnsensoredClothingUniformBase
  id: UnsensoredClothingUniformSkirtBase
  components:
  - type: Clothing
    slots: [innerclothing]
    femaleMask: UniformTop
  - type: Tag # Needed for species with nonhuman legs/can only wear skirts
    tags:
    - Skirt
    - ClothMade

- type: entity
  abstract: true
  parent: UnsensoredClothingUniformBase
  id: ClothingUniformBase
  components:
  - type: SuitSensor
  - type: DeviceNetwork
    deviceNetId: Wireless
    transmitFrequencyId: SuitSensor
    savableAddress: false
  - type: WirelessNetworkConnection
    range: 1200
  - type: StationLimitedNetwork

- type: entity
  abstract: true
  parent: ClothingUniformBase
  id: ClothingUniformSkirtBase
  components:
  - type: Clothing
    slots: [innerclothing]
    femaleMask: UniformTop
  - type: Tag # Needed for species with nonhuman legs/can only wear skirts
              # And for moths to be able to eat this.
    tags:
    - Skirt
    - ClothMade

- type: entity # For clothes that need suit sensors.
  parent: [UnsensoredClothingUniformBase, BaseFoldable]
  id: ClothingUniformBaseUnsensoredFlippable
  abstract: true
  components:
  - type: Appearance
  - type: Foldable
    canFoldInsideContainer: true
    unfoldVerbText: fold-unrollsleeves-verb
    foldVerbText: fold-rollsleeves-verb
  - type: FoldableClothing
    foldedEquippedPrefix: flipped
    foldedHeldPrefix: flipped
  - type: Sprite
    layers:
    - state: icon
      map: [ "unfoldedLayer" ]
    - state: icon_flipped
      map: ["foldedLayer"]
      visible: false

- type: entity # For clothes that need suit sensors.
  parent: ClothingHeadHeadHatBaseFlippable
  id: ClothingUniformBaseUnsensoredFlipped
  suffix: flipped
  abstract: true
  components:
  - type: Foldable
    folded: true
  - type: Clothing
    equippedPrefix: flipped
  - type: Item
    heldPrefix: flipped
  - type: Sprite
    layers:
    - state: icon
      map: [ "unfoldedLayer" ]
      visible: false
    - state: icon_flipped
      map: ["foldedLayer"]
      visible: true

- type: entity # For clothes that don't need suit sensors.
  parent: ClothingUniformBaseUnsensoredFlippable
  id: ClothingUniformBaseFlippable
  abstract: true
  components:
  - type: SuitSensor
  - type: DeviceNetwork
    deviceNetId: Wireless
    transmitFrequencyId: SuitSensor
  - type: WirelessNetworkConnection
    range: 1200
  - type: StationLimitedNetwork

- type: entity # For clothes that don't need suit sensors.
  parent: ClothingUniformBaseFlippable
  id: ClothingUniformBaseFlipped
  suffix: flipped
  abstract: true
  components: # I *think* I can just disregard the components;
              # and inherit from ClothingUniformBaseFlippable
              # and ClothingUniformBaseUnsensoredFlipped but I don't want to
              # test that right now.
  - type: Foldable
    folded: true
  - type: Clothing
    equippedPrefix: flipped
  - type: Item
    heldPrefix: flipped
  - type: Sprite
    layers:
    - state: icon
      map: [ "unfoldedLayer" ]
      visible: false
    - state: icon_flipped
      map: ["foldedLayer"]
      visible: true

- type: entity
  abstract: true
  parent: UnsensoredClothingUniformBase
  id: UnsensoredClothingUniformEnvirosuitBase
  components:
  - type: IgniteFromGasImmunity
    parts:
    - Torso
    - Groin
    - LeftArm
    - LeftHand
    - RightArm
    - RightHand
    - LeftLeg
    - LeftFoot
    - RightLeg
    - RightFoot
  - type: Clothing
    equipDelay: 0.4
    unequipDelay: 0.6 # Slightly higher delay to protect against accidental unequips
    femaleMask: NoMask
  - type: SelfExtinguisher
    cooldown: 100
    requiresIgniteFromGasImmune: true
    sound:
      path: /Audio/Effects/extinguish.ogg
  - type: LimitedCharges
    maxCharges: 4
    charges: 4
  - type: Armor
    modifiers:
      coefficients:
        Caustic: 0.85
  - type: Tag
    tags:
    - WhitelistChameleon
    - PlasmamanSafe
  - type: ClothingRequiredStepTriggerImmune
    slots: WITHOUT_POCKET
  - type: GuideHelp # While the playerbase is getting introduced to Plasmamen, add their guidebook here
    guides: [ Plasmaman ]

- type: entity
  abstract: true
  parent: UnsensoredClothingUniformEnvirosuitBase
  id: ClothingUniformEnvirosuitBase
  components:
  - type: SuitSensor
  - type: DeviceNetwork
    deviceNetId: Wireless
    transmitFrequencyId: SuitSensor
  - type: WirelessNetworkConnection
    range: 1200
  - type: StationLimitedNetwork

- type: entity
  abstract: true
  parent: UnsensoredClothingUniformEnvirosuitBase
  id: UnsensoredClothingUniformEnvirosuitCustomBase
  components:
  - type: Sprite
    sprite: Clothing/Uniforms/Envirosuits/custom.rsi
    layers:
    - state: icon
      color: "#FFFFFF"
    - state: accent-icon
      color: "#FF0000"
  - type: Item
    inhandVisuals:
      left:
      - state: inhand-left
        color: "#FFFFFF"
      - state: accent-inhand-left
        color: "#FF0000"
      right:
      - state: inhand-right
        color: "#FFFFFF"
      - state: accent-inhand-right
        color: "#FF0000"
  - type: Clothing
    sprite: Clothing/Uniforms/Envirosuits/custom.rsi
    clothingVisuals:
      jumpsuit:
      - state: equipped-INNERCLOTHING
        color: "#FFFFFF"
      - state: accent-equipped-INNERCLOTHING
        color: "#FF0000"
      - state: loweraccent-equipped-INNERCLOTHING
        color: "#FF0000"
      - state: soles-equipped-INNERCLOTHING
        color: "#bababa" # Recommended default soles color

- type: entity
  abstract: true
  parent: UnsensoredClothingUniformEnvirosuitCustomBase
  id: ClothingUniformEnvirosuitCustomBase
  components:
  - type: SuitSensor
  - type: DeviceNetwork
    deviceNetId: Wireless
    transmitFrequencyId: SuitSensor
  - type: WirelessNetworkConnection
    range: 1200
  - type: StationLimitedNetwork

- type: entity
  abstract: true
  id: ClothingUniformFoldableBase
  components:
  - type: Foldable
    canFoldInsideContainer: true
    unfoldVerbText: unfold-verb-clothing-jacket
    foldVerbText: fold-verb-clothing-jacket
  - type: FoldableClothing
    foldedEquippedPrefix: folded
