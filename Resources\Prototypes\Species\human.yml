- type: species
  id: Human
  name: species-name-human
  roundStart: true
  prototype: MobHuman
  sprites: MobHumanSprites
  defaultSkinTone: "#c0967f"
  markingLimits: MobHumanMarkingLimits
  dollPrototype: MobHumanDummy
  skinColoration: HumanToned
  bonusTraitPoints: 2

# The lack of a layer means that
# this person cannot have round-start anything
# applied to that layer. It has to instead
# be defined as a 'custom base layer'
# in either the mob's starting marking prototype,
# or it has to be added in C#.
- type: speciesBaseSprites
  id: MobHumanSprites
  sprites:
    Head: MobHumanHead
    Face: MobHumanoidAnyMarking
    Hair: MobHumanoidAnyMarking
    FacialHair: MobHumanoidAnyMarking
    Snout: MobHumanoidAnyMarking
    Underwear: MobHumanoidAnyMarking
    Undershirt: MobHumanoidAnyMarking
    Chest: MobHumanTorso
    Eyes: MobHumanoidEyes
    LArm: MobHumanLArm
    RArm: MobHumanRArm
    LHand: MobHumanLHand
    RHand: MobHumanRHand
    LLeg: MobHumanLLeg
    RLeg: MobHumanRLeg
    LFoot: MobHumanLFoot
    RFoot: MobHumanRFoot
    Tail: MobHumanoidAnyMarking # Nyanotrasen - Felinid
    HeadTop: MobHumanoidAnyMarking # Nyanotrasen - Felinid & Oni
    HeadSide: MobHumanoidAnyMarking

- type: markingPoints
  id: MobHumanMarkingLimits
  points:
    Head:
      points: 2
      required: false
    Hair:
      points: 1
      required: false
    FacialHair:
      points: 1
      required: false
    Snout:
      points: 1
      required: false
    Tail:
      points: 1
      required: false
    HeadTop:
      points: 1
      required: false
    HeadSide:
      points: 4
      required: false
    Chest:
      points: 6
      required: false
    Underwear:
      points: 1
      required: false
    Undershirt:
      points: 1
      required: false
    RightLeg:
      points: 6
      required: false
    RightFoot:
      points: 6
      required: false
    LeftLeg:
      points: 6
      required: false
    LeftFoot:
      points: 6
      required: false
    RightArm:
      points: 6
      required: false
    RightHand:
      points: 6
      required: false
    LeftArm:
      points: 6
      required: false
    LeftHand:
      points: 6
      required: false

- type: humanoidBaseSprite
  id: MobHumanoidEyes
  baseSprite:
    sprite: Mobs/Customization/eyes.rsi
    state: eyes

- type: humanoidBaseSprite
  id: MobHumanoidAnyMarking

- type: humanoidBaseSprite
  id: MobHumanoidMarkingMatchSkin
  markingsMatchSkin: true

- type: humanoidBaseSprite
  id: MobHumanHead
  baseSprite:
    sprite: Mobs/Species/Human/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobHumanHeadMale
  baseSprite:
    sprite: Mobs/Species/Human/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobHumanHeadFemale
  baseSprite:
    sprite: Mobs/Species/Human/parts.rsi
    state: head_f

- type: humanoidBaseSprite
  id: MobHumanTorso
  baseSprite:
    sprite: Mobs/Species/Human/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobHumanTorsoMale
  baseSprite:
    sprite: Mobs/Species/Human/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobHumanTorsoFemale
  baseSprite:
    sprite: Mobs/Species/Human/parts.rsi
    state: torso_f

- type: humanoidBaseSprite
  id: MobHumanLLeg
  baseSprite:
    sprite: Mobs/Species/Human/parts.rsi
    state: l_leg

- type: humanoidBaseSprite
  id: MobHumanLArm
  baseSprite:
    sprite: Mobs/Species/Human/parts.rsi
    state: l_arm

- type: humanoidBaseSprite
  id: MobHumanLHand
  baseSprite:
    sprite: Mobs/Species/Human/parts.rsi
    state: l_hand

- type: humanoidBaseSprite
  id: MobHumanLFoot
  baseSprite:
    sprite: Mobs/Species/Human/parts.rsi
    state: l_foot

- type: humanoidBaseSprite
  id: MobHumanRLeg
  baseSprite:
    sprite: Mobs/Species/Human/parts.rsi
    state: r_leg

- type: humanoidBaseSprite
  id: MobHumanRArm
  baseSprite:
    sprite: Mobs/Species/Human/parts.rsi
    state: r_arm

- type: humanoidBaseSprite
  id: MobHumanRHand
  baseSprite:
    sprite: Mobs/Species/Human/parts.rsi
    state: r_hand

- type: humanoidBaseSprite
  id: MobHumanRFoot
  baseSprite:
    sprite: Mobs/Species/Human/parts.rsi
    state: r_foot
