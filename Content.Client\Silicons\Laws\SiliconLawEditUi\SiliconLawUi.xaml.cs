﻿using System.Linq;
using Content.Client.UserInterface.Controls;
using Content.Shared.FixedPoint;
using Content.Shared.Silicons.Laws;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Silicons.Laws.SiliconLawEditUi;

[GenerateTypedNameReferences]
public sealed partial class SiliconLawUi : FancyWindow
{
    private List<SiliconLaw> _laws = new();

    public SiliconLawUi()
    {
        RobustXamlLoader.Load(this);
        NewLawButton.OnPressed += _ => AddNewLaw();
    }

    private void AddNewLaw()
    {
        var newLaw = new SiliconLaw();
        newLaw.Order = FixedPoint2.New(_laws.Count + 1);
        _laws.Add(newLaw);
        SetLaws(_laws);
    }

    public void SetLaws(List<SiliconLaw> sLaws)
    {
        _laws = sLaws;
        LawContainer.RemoveAllChildren();
        foreach (var law in sLaws.OrderBy(l => l.Order))
        {
            var lawControl = new SiliconLawContainer();
            lawControl.SetLaw(law);
            lawControl.MoveLawDown += MoveLawDown;
            lawControl.MoveLawUp += MoveLawUp;
            lawControl.DeleteAction += DeleteLaw;

            LawContainer.AddChild(lawControl);
        }
    }

    public void DeleteLaw(SiliconLaw law)
    {
        _laws.Remove(law);
        SetLaws(_laws);
    }

    public void MoveLawDown(SiliconLaw law)
    {
        if (_laws.Count == 0)
        {
            return;
        }

        var index = _laws.IndexOf(law);
        if (index == -1)
        {
            return;
        }

        _laws[index].Order += FixedPoint2.New(1);
        SetLaws(_laws);
    }

    public void MoveLawUp(SiliconLaw law)
    {
        if (_laws.Count == 0)
        {
            return;
        }

        var index = _laws.IndexOf(law);
        if (index == -1)
        {
            return;
        }

        _laws[index].Order += FixedPoint2.New(-1);
        SetLaws(_laws);
    }

    public List<SiliconLaw> GetLaws()
    {
        return _laws;
    }
}
