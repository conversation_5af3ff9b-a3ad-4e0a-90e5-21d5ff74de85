- type: entity
  id: Windoor
  parent: BaseWindoor
  name: "вікно"
  description: "Він відкривається, закривається, і ви можете бачити крізь нього! І воно може бути зроблене з плазми, урану або звичайного скла!"

- type: entity
  id: WindoorSecure
  parent: BaseSecureWindoor
  name: "Захисне Вікно"
  description: "Це міцне вікно і розсувні двері. Ого!"

- type: entity
  id: WindoorClockwork
  parent: BaseClockworkWindoor
  name: "годинникове вікно-двері"
  description: "Це міцне вікно і розсувні двері. Ого!"

- type: entity
  id: WindoorPlasma
  parent: BasePlasmaWindoor
  name: "Плазмове Вікно-двері"
  description: "Це рожеве вікно і розсувні двері. Дивовижно!"

- type: entity
  id: WindoorSecurePlasma
  parent: BaseSecurePlasmaWindoor
  name: "Захищене Плазмове Вікно-двері"
  description: "Це міцне фіолетове вікно і розсувні двері. Приголомшливо!"

- type: entity
  id: WindoorUranium
  parent: BaseUraniumWindoor
  name: "Уранове Вікно-двері"
  description: "Це вікно і розсувні двері. А? О, і вони зелені!"

- type: entity
  id: WindoorSecureUranium
  parent: BaseSecureUraniumWindoor
  name: "Захищене Уранове Вікно-двері"
  description: "Це міцне вікно та розсувні двері. Він такий неоново-зелений, що на смак може навіть нагадувати лайм!"

# TODO remove these with parameterized prototypes/whatever we end up doing
# Windoors (alphabetical)

- type: entity
  parent: Windoor
  id: WindoorBarLocked
  suffix: Bar, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsBar ]

- type: entity
  parent: Windoor
  id: WindoorBarKitchenLocked
  suffix: Bar&Kitchen, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsBarKitchen ]

- type: entity
  parent: Windoor
  id: WindoorCargoLocked
  suffix: Logistics, Locked # DeltaV - Logistics Department replacing Cargo
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsCargo ]

- type: entity
  parent: Windoor
  id: WindoorChapelLocked
  suffix: Chapel, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsChapel ]

- type: entity
  parent: Windoor
  id: WindoorHydroponicsLocked
  suffix: Hydroponics, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsHydroponics ]

- type: entity
  parent: Windoor
  id: WindoorJanitorLocked
  suffix: Janitor, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsJanitor ]

- type: entity
  parent: WindoorPlasma
  id: PlasmaWindoorJanitorLocked
  suffix: Janitor, Locked, Plasma
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsJanitor ]

- type: entity
  parent: Windoor
  id: WindoorKitchenLocked
  suffix: Kitchen, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsKitchen ]

- type: entity
  parent: Windoor
  id: WindoorKitchenHydroponicsLocked
  suffix: Kitchen&Hydroponics, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsKitchenHydroponics ]

- type: entity
  parent: Windoor
  id: WindoorServiceLocked
  suffix: Service, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsService ]

- type: entity
  parent: Windoor
  id: WindoorTheatreLocked
  suffix: Theatre, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsTheatre ]

# Secure

- type: entity
  parent: WindoorSecureSecurityLocked
  id: WindoorSecureArmoryLocked
  suffix: Armory, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsArmory ]

- type: entity
  parent: WindoorSecurePlasma
  id: PlasmaWindoorSecureArmoryLocked
  suffix: Armory, Locked, Plasma
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsArmory ]

- type: entity
  parent: WindoorSecure
  id: WindoorSecureAtmosphericsLocked
  suffix: Atmospherics, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsAtmospherics ]

- type: entity
  parent: WindoorSecurePlasma
  id: PlasmaWindoorSecureAtmosphericsLocked
  suffix: Atmospherics, Locked, Plasma
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsAtmospherics ]

- type: entity
  parent: WindoorSecure
  id: WindoorSecureBarLocked
  suffix: Bar, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsBar ]

- type: entity
  parent: WindoorSecureSecurityLocked
  id: WindoorSecureBrigLocked
  suffix: Brig, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsBrig ]

- type: entity
  parent: WindoorSecure
  id: WindoorSecureCargoLocked
  suffix: Logistics, Locked # DeltaV - Logistics Department replacing Cargo
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsCargo ]

- type: entity
  parent: WindoorSecure
  id: WindoorSecureChapelLocked
  suffix: Chapel, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsChapel ]

- type: entity
  parent: WindoorSecure
  id: WindoorSecureChemistryLocked
  suffix: Chemistry, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsChemistry ]

- type: entity
  parent: WindoorSecurePlasma
  id: PlasmaWindoorSecureChemistryLocked
  suffix: Chemistry, Locked, Plasma
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsChemistry ]

- type: entity
  parent: WindoorSecure
  id: WindoorSecureCentralCommandLocked
  suffix: Central Command, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsCentralCommand ]

- type: entity
  parent: WindoorSecurePlasma
  id: PlasmaWindoorSecureCentralCommandLocked
  suffix: Central Command, Locked, Plasma
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsCentralCommand ]

- type: entity
  parent: WindoorSecureUranium
  id: UraniumWindoorSecureCentralCommandLocked
  suffix: Central Command, Locked, Uranium
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsCentralCommand ]

- type: entity
  parent: WindoorSecure
  id: WindoorSecureCommandLocked
  suffix: Command, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsCommand ]

- type: entity
  parent: WindoorSecurePlasma
  id: PlasmaWindoorSecureCommandLocked
  suffix: Command, Locked, Plasma
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsCommand ]

- type: entity
  parent: WindoorSecure
  id: WindoorSecureDetectiveLocked
  suffix: Detective, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsDetective ]

- type: entity
  parent: WindoorSecure
  id: WindoorSecureEngineeringLocked
  suffix: Engineering, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsEngineering ]

- type: entity
  parent: WindoorSecurePlasma
  id: PlasmaWindoorSecureEngineeringLocked
  suffix: Engineering, Locked, Plasma
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsEngineering ]

- type: entity
  parent: WindoorSecureUranium
  id: UraniumWindoorSecureEngineeringLocked
  suffix: Engineering, Locked, Uranium
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsEngineering ]

- type: entity
  parent: WindoorSecure
  id: WindoorSecureExternalLocked
  suffix: External, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsExternal ]

- type: entity
  parent: WindoorSecure
  id: WindoorSecureJanitorLocked
  suffix: Janitor, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsJanitor ]

- type: entity
  parent: WindoorSecurePlasma
  id: PlasmaWindoorSecureJanitorLocked
  suffix: Janitor, Locked, Plasma
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsJanitor ]

- type: entity
  parent: WindoorSecure
  id: WindoorSecureKitchenLocked
  suffix: Kitchen, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsKitchen ]

- type: entity
  parent: WindoorSecureSecurityLocked
  id: WindoorSecureSecurityLawyerLocked
  suffix: Security/Lawyer, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsSecurityLawyer ]

- type: entity
  parent: WindoorSecure
  id: WindoorSecureMedicalLocked
  suffix: Medical, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsMedical ]

- type: entity
  parent: WindoorSecure
  id: WindoorSecureSalvageLocked
  suffix: Salvage, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsSalvage ]

- type: entity
  parent: WindoorSecure
  id: WindoorSecureSecurityLocked
  suffix: Security, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsSecurity ]

- type: entity
  parent: WindoorSecurePlasma
  id: PlasmaWindoorSecureSecurityLocked
  suffix: Security, Locked, Plasma
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsSecurity ]

- type: entity
  parent: WindoorSecure
  id: WindoorSecureScienceLocked
  suffix: Epistemics, Locked # DeltaV - Epistemics Department replacing Science
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsResearch ]

- type: entity
  parent: WindoorSecurePlasma
  id: PlasmaWindoorSecureScienceLocked
  suffix: Science, Locked, Plasma
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsResearch ]

- type: entity
  parent: WindoorSecure
  id: WindoorSecureServiceLocked
  suffix: Service, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsService ]

- type: entity
  parent: WindoorSecure
  id: WindoorSecureHeadOfPersonnelLocked
  suffix: HeadOfPersonnel, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsHeadOfPersonnel ]

# Syndicate

- type: entity
  parent: Windoor
  id: WindoorSyndicateLocked
  suffix: Syndicate, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsSyndicateAgent ]

- type: entity
  parent: WindoorSecure
  id: WindoorSecureSyndicateLocked
  suffix: Syndicate, Locked
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsSyndicateAgent ]

- type: entity
  parent: WindoorPlasma
  id: PlasmaWindoorSyndicateLocked
  suffix: Syndicate, Locked, Plasma
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsSyndicateAgent ]

- type: entity
  parent: WindoorSecurePlasma
  id: PlasmaWindoorSecureSyndicateLocked
  suffix: Syndicate, Locked, Plasma
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsSyndicateAgent ]

- type: entity
  parent: WindoorUranium
  id: UraniumWindoorSyndicateLocked
  suffix: Syndicate, Locked, Uranium
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsSyndicateAgent ]

- type: entity
  parent: WindoorSecureUranium
  id: UraniumWindoorSecureSyndicateLocked
  suffix: Syndicate, Locked, Uranium
  components:
  - type: ContainerFill
    containers:
      board: [ DoorElectronicsSyndicateAgent ]
