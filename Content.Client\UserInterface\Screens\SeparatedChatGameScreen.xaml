<screens:SeparatedChatGameScreen
    xmlns="https://spacestation14.io"
    xmlns:screens="clr-namespace:Content.Client.UserInterface.Screens"
    xmlns:menuBar="clr-namespace:Content.Client.UserInterface.Systems.MenuBar.Widgets"
    xmlns:actions="clr-namespace:Content.Client.UserInterface.Systems.Actions.Widgets"
    xmlns:chat="clr-namespace:Content.Client.UserInterface.Systems.Chat.Widgets"
    xmlns:alerts="clr-namespace:Content.Client.UserInterface.Systems.Alerts.Widgets"
    xmlns:hotbar="clr-namespace:Content.Client.UserInterface.Systems.Hotbar.Widgets"
    xmlns:widgets="clr-namespace:Content.Client.UserInterface.Systems.Ghost.Widgets"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
    xmlns:graphics="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
    xmlns:inventory="clr-namespace:Content.Client.UserInterface.Systems.Inventory.Widgets"
    xmlns:targeting="clr-namespace:Content.Client._Shitmed.UserInterface.Systems.Targeting.Widgets"
    Name="SeparatedChatHud"
    VerticalExpand="False"
    VerticalAlignment="Bottom"
    HorizontalAlignment="Center">
    <controls:RecordedSplitContainer Name="ScreenContainer" HorizontalExpand="True" VerticalExpand="True" SplitWidth="0" StretchDirection="TopLeft">
        <LayoutContainer Name="ViewportContainer" HorizontalExpand="True" VerticalExpand="True">
            <controls:MainViewport Name="MainViewport"/>
            <widgets:GhostGui Name="Ghost" Access="Protected" />
            <inventory:InventoryGui Name="Inventory" Access="Protected"/>
            <hotbar:HotbarGui Name="Hotbar" Access="Protected"/>
            <targeting:TargetingControl Name="Targeting" Access="Protected"/> <!-- Shitmed Change -->
            <BoxContainer Name="TopLeftContainer" Orientation="Vertical">
                <actions:ActionsBar Name="Actions" Access="Protected" />
                <BoxContainer Name="VoteMenu" Access="Public" Orientation="Vertical"/>
            </BoxContainer>
            <alerts:AlertsUI Name="Alerts" Access="Protected" />
        </LayoutContainer>
        <PanelContainer HorizontalExpand="True" MinWidth="300">
            <PanelContainer.PanelOverride>
                <graphics:StyleBoxFlat BackgroundColor="#2B2C3B" />
            </PanelContainer.PanelOverride>

            <BoxContainer Orientation="Vertical" HorizontalExpand="True" SeparationOverride="10" Margin="10">
                <menuBar:GameTopMenuBar Name="TopBar" HorizontalExpand="True" Access="Protected" />
                <chat:ChatBox VerticalExpand="True" HorizontalExpand="True" Name="Chat" Access="Protected" MinSize="0 0"/>
            </BoxContainer>
        </PanelContainer>
    </controls:RecordedSplitContainer>
</screens:SeparatedChatGameScreen>
