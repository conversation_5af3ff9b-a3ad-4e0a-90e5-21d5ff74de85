- type: entity
  id: DionaGibAction
  name: "Подай себе!"
  description: "Розділіть на 3 німфи."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon:
      sprite: Mobs/Species/Diona/organs.rsi
      state: brain
    event: !type:GibActionEvent {}
    checkCanInteract: false
    checkConsciousness: false

- type: entity
  id: DionaReformAction
  name: "Реформа"
  description: "Реформувати назад у цілу Діону."
  categories: [ HideSpawnMenu ]
  components:
  - type: InstantAction
    icon:
      sprite: Mobs/Species/Diona/parts.rsi
      state: full
    event: !type:ReformEvent {}
    useDelay: 600 # Once every 10 minutes. Keep them dead for a fair bit before reforming
