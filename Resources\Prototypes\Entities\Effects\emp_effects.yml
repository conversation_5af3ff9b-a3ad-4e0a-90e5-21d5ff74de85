- type: entity
  id: EffectEmpPulseNoSound
  categories: [ HideSpawnMenu ]
  components:
  - type: TimedDespawn
    lifetime: 0.8
  - type: Sprite
    drawdepth: Effects
    noRot: true
    layers:
    - shader: unshaded
      map: [ "enum.EffectLayers.Unshaded" ]
      sprite: Effects/emp.rsi
      state: emp_pulse
  - type: EffectVisuals
  - type: Tag
    tags:
    - HideContextMenu
  - type: AnimationPlayer

- type: entity
  parent: EffectEmpPulseNoSound
  id: EffectEmpPulse
  categories: [ HideSpawnMenu ]
  components:
  - type: EmitSoundOnSpawn
    sound:
      path: /Audio/Effects/Lightning/lightningbolt.ogg

- type: entity
  id: EffectEmpDisabled
  categories: [ HideSpawnMenu ]
  components:
  - type: TimedDespawn
    lifetime: 0.4
  - type: Sprite
    drawdepth: Effects
    noRot: true
    layers:
    - shader: unshaded
      map: [ "enum.EffectLayers.Unshaded" ]
      sprite: Effects/emp.rsi
      state: emp_disable
  - type: EffectVisuals
  - type: Tag
    tags:
    - HideContextMenu
  - type: AnimationPlayer
