- type: entity
  id: BaseHumanOrganUnGibbable
  parent: BaseItem
  abstract: true
  components:
  - type: Sprite
    sprite: Mobs/Species/Human/organs.rsi
  - type: Organ
  - type: Food
  - type: Extractable
    grindableSolutionName: organ
  - type: SolutionContainerManager
    solutions:
      organ:
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
      food:
        maxVol: 5
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 5
  - type: FlavorProfile
    flavors:
      - people
  - type: Tag
    tags:
      - Meat
      - Organ


- type: entity
  id: BaseHumanOrgan
  parent: BaseHumanOrganUnGibbable
  abstract: true
  components:
  - type: Gibbable

- type: entity
  id: OrganHumanBrain
  parent: BaseHumanOrganUnGibbable
  name: "мозок"
  description: "Джерело неймовірного, нескінченного інтелекту. Хонк."
  components:
  - type: Sprite
    state: brain
  - type: Organ
    slotId: brain # Shitmed Change
  - type: Food # Shitmed Change
    delay: 5
    forceFeedDelay: 6
    popupOnEat: true
  - type: Input
    context: "ghost"
  - type: Brain
  - type: InputMover
  - type: Examiner
  - type: BlockMovement
  - type: BadFood
  - type: Tag
    tags:
      - Meat
      - Organ
      - Brain
  - type: SolutionContainerManager
    solutions:
      organ:
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
      food:
        maxVol: 5
        reagents:
        - ReagentId: GreyMatter
          Quantity: 5
  - type: FlavorProfile
    flavors:
      - people
  - type: FoodSequenceElement
    entries:
      Burger: Brain
      Taco: Brain

- type: entity
  id: OrganHumanEyes
  parent: BaseHumanOrgan
  name: "очі"
  description: "Я тебе бачу!"
  components:
  - type: Organ # Shitmed Change
    slotId: eyes # Shitmed Change
  - type: Eyes # Shitmed Change
  - type: Sprite
    layers:
      - state: eyeball-l
      - state: eyeball-r
  - type: Tag # goob edit
    tags:
    - Meat
    - Organ
    - Eyes

- type: entity
  id: OrganHumanTongue
  parent: BaseHumanOrgan
  name: "язик"
  description: "М'яз із плоті, який здебільшого використовується для брехні."
  components:
  - type: Sprite
    state: tongue
  - type: Tag # goob edit
    tags:
    - Meat
    - Organ

- type: entity
  id: OrganHumanAppendix
  parent: BaseHumanOrgan
  name: "аппендикс"
  components:
  - type: Sprite
    layers:
    - state: appendix
    - state: appendix-inflamed
      visible: false

- type: entity
  id: OrganHumanEars
  parent: BaseHumanOrgan
  name: "вуха"
  description: "Вухо складається з трьох частин. Внутрішнє, середнє і зовнішнє. Зазвичай тільки одне з цих частин повинне бути видимою."
  components:
  - type: Sprite
    state: ears
  - type: Ears # Shitmed Change

- type: entity
  id: OrganHumanLungs
  parent: BaseHumanOrgan
  name: "легені"
  description: "Фільтрують кисень з атмосфери, який потім потрапляє в кровотік і використовується як переносник електронів."
  components:
  - type: Organ # Shitmed Change
    slotId: lungs # Shitmed Change
  - type: Sprite
    layers:
      - state: lung-l
      - state: lung-r
  - type: Lung
  - type: Metabolizer
    removeEmpty: true
    solutionOnBody: false
    solution: "Lung"
    metabolizerTypes: [ Human ]
    groups:
    - id: Gas
      rateModifier: 100.0
  - type: SolutionContainerManager
    solutions:
      organ:
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
      Lung:
        maxVol: 100.0
        canReact: false
      food:
        maxVol: 5
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 5
  - type: Tag # goob edit
    tags:
    - Meat
    - Organ
    - Lungs

- type: entity
  id: OrganHumanHeart
  parent: BaseHumanOrgan
  name: "серце"
  description: "Мені шкода безсердечного виродка, який його втратив."
  components:
  - type: Heart # Shitmed Change
  - type: Organ # Shitmed Change
    slotId: heart # Shitmed Change
  - type: Sprite
    state: heart-on
  # The heart 'metabolizes' medicines and poisons that aren't filtered out by other organs.
  # This is done because these chemicals need to have some effect even if they aren't being filtered out of your body.
  # You're technically 'immune to poison' without a heart, but.. uhh, you'll have bigger problems on your hands.
  - type: Metabolizer
    maxReagents: 2
    metabolizerTypes: [Human]
    groups:
    - id: Medicine
    - id: Poison
    - id: Narcotic
  - type: Tag # goob edit
    tags:
    - Meat
    - Organ
    - Heart

- type: entity
  id: OrganHumanStomach
  parent: BaseHumanOrgan
  name: "шлунок"
  description: "Гидота. Це складно переварити."
  components:
  - type: Organ # Shitmed Change
    slotId: stomach # Shitmed Change
  - type: Sprite
    state: stomach
  - type: SolutionContainerManager
    solutions:
      stomach:
        maxVol: 50
      food:
        maxVol: 5
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 5
  - type: Stomach
  # The stomach metabolizes stuff like foods and drinks.
  # TODO: Have it work off of the ent's solution container, and move this
  # to intestines instead.
  - type: Metabolizer
    # mm yummy
    maxReagents: 3
    metabolizerTypes: [Human]
    groups:
    - id: Food
    - id: Drink
  - type: Tag # goob edit
    tags:
    - Meat
    - Organ
    - Stomach

- type: entity
  id: OrganHumanLiver
  parent: BaseHumanOrgan
  name: "печінка"
  description: "Поєднання: к'янті та квасоля фава."
  components:
  - type: Liver # Shitmed Change
  - type: Organ # Shitmed Change
    slotId: liver # Shitmed Change
  - type: Sprite
    state: liver
  - type: Metabolizer # The liver metabolizes certain chemicals only, like alcohol.
    maxReagents: 1
    metabolizerTypes: [Human]
    groups:
    - id: Alcohol
      rateModifier: 0.1 # removes alcohol very slowly along with the stomach removing it as a drink
  - type: Tag # goob edit
    tags:
    - Meat
    - Organ
    - Liver

- type: entity
  id: OrganHumanKidneys
  parent: BaseHumanOrgan
  name: "нирки"
  description: "Фільтрує токсини з кровотоку."
  components:
  - type: Organ # Shitmed
    slotId: kidneys
  - type: Sprite
    layers:
      - state: kidney-l
      - state: kidney-r
  # The kidneys just remove anything that doesn't currently have any metabolisms, as a stopgap.
  - type: Metabolizer
    maxReagents: 5
    metabolizerTypes: [Human]
    removeEmpty: true
  - type: Tag # goob edit
    tags:
    - Meat
    - Organ
    - Kidneys
