<ui:TurretControllerWindow xmlns="https://spacestation14.io"
                        xmlns:ui="clr-namespace:Content.Client.TurretController"
                        xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
                        xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                        SetWidth="550"
                        Resizable="False"
                        MouseFilter="Stop">

    <PanelContainer Name="Background" StyleClasses="PdaBackgroundRect" ModulateSelfOverride="#4a5466"/>
    <PanelContainer Name="Border" StyleClasses="PdaBorderRect" />

    <BoxContainer Orientation="Vertical" HorizontalExpand="True">

        <!--Header-->
        <BoxContainer SetHeight="26" Margin="4 2 8 0" Orientation="Horizontal">
            <Control HorizontalExpand="True"/>
            <TextureButton Name="CloseButton" StyleClasses="windowCloseButton" Modulate="#646464" VerticalAlignment="Center" Margin="0 4 4 0"/>
        </BoxContainer>

        <!--Content-->
        <Control Margin="18 0" RectClipContent="True" VerticalExpand="true"
                 HorizontalExpand="True">
            <PanelContainer Name="ContentBorder" StyleClasses="PdaBackground"/>
            <Control Name="ContentsContainer" Margin="3 3" Modulate="#FFFFFF">

                <!-- Screen Background -->
                <PanelContainer Name="ContentBackground" StyleClasses="PdaContentBackground"/>

                <!-- Screen foreground -->
                <BoxContainer Orientation="Vertical">

                    <Label Text="{Loc 'turret-controls-window-title'}" StyleClasses="ConsoleHeading"
                           HorizontalAlignment="Center" Margin="0 5 0 0" />

                    <!-- Linked devices -->
                    <PanelContainer Margin="10 5 10 5">
                        <PanelContainer.PanelOverride>
                            <gfx:StyleBoxFlat BorderColor="#FFFFFF" BorderThickness="2" />
                        </PanelContainer.PanelOverride>

                        <BoxContainer Orientation="Vertical" MinHeight="195" Margin="5 5 5 5">
                            <Label Name="TurretStatusHeader" Text="{Loc 'turret-controls-window-turret-status-label'}" StyleClasses="ConsoleSubHeading"
                                   HorizontalAlignment="Center" />

                            <PanelContainer StyleClasses="LowDivider" HorizontalExpand="True" Margin="-5 5 -5 5" SetHeight="2">
                                <PanelContainer.PanelOverride>
                                    <gfx:StyleBoxFlat BackgroundColor="#FFFFFF" />
                                </PanelContainer.PanelOverride>
                            </PanelContainer>                           

                            <BoxContainer Orientation="Vertical" VerticalExpand="True" HorizontalExpand="True">
                                <Label Name="NoLinkedTurretsText" Text="{Loc 'turret-controls-window-no-turrets'}" StyleClasses="ConsoleText"
                                       HorizontalAlignment="Center" ReservesSpace="False"/>

                                <ScrollContainer VerticalExpand="True" HorizontalExpand="True">
                                    <BoxContainer Name="LinkedTurretsContainer" Orientation="Vertical" Visible="False" ReservesSpace="False">
                                        <!-- Populated with C# code -->
                                    </BoxContainer>  
                                </ScrollContainer>
                            </BoxContainer>
                        </BoxContainer>
                    </PanelContainer>

                    <!-- Armament controls -->
                    <PanelContainer Margin="10 0 10 5">
                        <PanelContainer.PanelOverride>
                            <gfx:StyleBoxFlat BorderColor="#FFFFFF" BorderThickness="2" />
                        </PanelContainer.PanelOverride>

                        <BoxContainer Orientation="Vertical">
                            <Label Text="{Loc 'turret-controls-window-armament-controls-label'}" StyleClasses="ConsoleSubHeading"
                                   HorizontalAlignment="Center" Margin="0 5 0 5" />

                            <PanelContainer StyleClasses="LowDivider" HorizontalExpand="True" SetHeight="2">
                                <PanelContainer.PanelOverride>
                                    <gfx:StyleBoxFlat BackgroundColor="#FFFFFF" />
                                </PanelContainer.PanelOverride>
                            </PanelContainer>

                            <BoxContainer Orientation="Horizontal" Margin="10 10 10 10">
                                <controls:MonotoneButton Name="SafeButton" Text="{Loc 'turret-controls-window-safe'}"
                                                   Shape="OpenRight" Pressed="False" ToggleMode="True" HorizontalExpand="True"/>
                                <controls:MonotoneButton Name="StunButton" Text="{Loc 'turret-controls-window-stun'}"
                                                   Shape="OpenBoth" Pressed="False" ToggleMode="True" HorizontalExpand="True"/>
                                <controls:MonotoneButton Name="LethalButton" Text="{Loc 'turret-controls-window-lethal'}"
                                                   Shape="OpenLeft" Pressed="False" ToggleMode="True" HorizontalExpand="True"/>
                            </BoxContainer>

                        </BoxContainer>
                    </PanelContainer>

                    <!-- Targeting controls -->
                    <PanelContainer Name="TargetingControlsPanel" Margin="10 0 10 10" VerticalExpand="True" HorizontalExpand="True">
                        <PanelContainer.PanelOverride>
                            <gfx:StyleBoxFlat BorderColor="#FFFFFF" BorderThickness="2" />
                        </PanelContainer.PanelOverride>

                        <BoxContainer Orientation="Vertical" VerticalExpand="True" HorizontalExpand="True">
                            <Label Text="{Loc 'turret-controls-window-targeting-controls-label'}" StyleClasses="ConsoleSubHeading"
                                   HorizontalAlignment="Center" Margin="0 5 0 5" />

                            <PanelContainer StyleClasses="LowDivider" HorizontalExpand="True" SetHeight="2">
                                <PanelContainer.PanelOverride>
                                    <gfx:StyleBoxFlat BackgroundColor="#FFFFFF" />
                                </PanelContainer.PanelOverride>
                            </PanelContainer>

                            <!-- Access configuration -->
                            <BoxContainer Orientation="Horizontal" Margin="10 10 10 10" VerticalExpand="True" HorizontalExpand="True" MinHeight="70">

                                <!-- Access groups -->
                                <BoxContainer Name="AccessGroupList" Orientation="Vertical" HorizontalExpand="True" SizeFlagsStretchRatio="0.5" Margin="0 0 10 0">
                                    <!-- Populated with C# code -->
                                </BoxContainer>

                                <PanelContainer StyleClasses="LowDivider" VerticalExpand="True" Margin="0 0 0 0" SetWidth="2">
                                    <PanelContainer.PanelOverride>
                                        <gfx:StyleBoxFlat BackgroundColor="#FFFFFF" />
                                    </PanelContainer.PanelOverride>
                                </PanelContainer>

                                <!-- Access levels -->
                                <ScrollContainer HorizontalExpand="True" VerticalExpand="True" Margin="10 0 0 0">
                                    <BoxContainer Name="AccessLevelGrid" Orientation="Vertical" HorizontalAlignment="Left">
                                        <!-- Populated with C# code -->
                                    </BoxContainer>
                                </ScrollContainer>
                            </BoxContainer>

                        </BoxContainer>
                    </PanelContainer>
                </BoxContainer>
            </Control>
        </Control>

        <!--Footer-->
        <BoxContainer Orientation="Horizontal" SetHeight="28">
            <Label Text="⚠" Margin="0 0 4 4" HorizontalExpand="True" HorizontalAlignment="Right"/>
            <Label Name="Footer" Text="{Loc 'turret-controls-window-footer'}"
                   HorizontalAlignment="Center" Margin="0 0 0 4"/>
            <Label Text="⚠" Margin="4 0 0 4" HorizontalExpand="True" HorizontalAlignment="Left"/>
        </BoxContainer>
    </BoxContainer>
</ui:TurretControllerWindow>
