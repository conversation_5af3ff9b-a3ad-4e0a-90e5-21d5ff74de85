# Loc strings for various entity state & client-side PVS related commands

cmd-reset-ent-help = Використання: resetent <UID сутності>
cmd-reset-ent-desc = Скинути сутність до останнього отриманого стану сервера. Це також призведе до скидання сутностей, які було від'єднано до нульового простору. 

cmd-reset-all-ents-help = Використання: перезавантажувачі
cmd-reset-all-ents-desc = Скидає всі сутності до останнього отриманого стану сервера. Це впливає лише на сутності, які не було від'єднано до нульового простору. 

cmd-detach-ent-help = Використання: detachent <UID сутності>
cmd-detach-ent-desc = Відокремити сутність у нульовий простір, так, ніби вона вийшла з діапазону PVS.

cmd-local-delete-help = Використання: localdelete <UID сутності>
cmd-local-delete-desc = Видаляє сутність. На відміну від звичайної команди видалення, ця команда виконується на стороні клієнта. Якщо сутність не є сутністю на стороні клієнта, це може призвести до помилок.

cmd-full-state-reset-help = Використання: fullstatereset
cmd-full-state-reset-desc = Відкидає будь-яку інформацію про стан сутності і запитує повний стан у сервера.
