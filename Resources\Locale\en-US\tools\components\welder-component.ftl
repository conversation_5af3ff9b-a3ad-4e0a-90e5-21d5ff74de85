welder-component-welder-not-lit-message = The welder is turned off!
welder-component-cannot-weld-message = The welder does not have enough fuel for that!
welder-component-no-fuel-message = The welder has no fuel left!
welder-component-no-fuel-in-tank = The {$owner} is empty.
welder-component-on-examine-welder-lit-message = [color=orange]Lit[/color]
welder-component-on-examine-welder-not-lit-message = Not lit
welder-component-on-examine-detailed-message = Fuel: [color={$colorName}]{$fuelLeft}/{$fuelCapacity}[/color]
    {$status}
welder-component-suicide-lit-others-message = {$victim} welds their every orifice closed! It looks like they are trying to commit suicide!
welder-component-suicide-lit-message = You weld your every orifice closed!
welder-component-suicide-unlit-others-message = {$victim} bashes themselves with the unlit welding torch!
welder-component-suicide-unlit-message = You bash yourself with the unlit welding torch!
welder-component-after-interact-refueled-message = Refueled!
welder-component-already-full = The welder is already full.
