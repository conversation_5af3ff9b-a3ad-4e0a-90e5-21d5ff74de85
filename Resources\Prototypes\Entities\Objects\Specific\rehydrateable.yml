- type: entity
  parent: BaseItem
  id: MonkeyCube
  name: "мавпячий куб"
  description: "Просто додайте води!"
  components:
  - type: Item
    size: Tiny
  - type: SolutionContainerManager
    solutions:
      cube:
        maxVol: 11 # needs room for water
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
  - type: Food
    solution: cube
  - type: RefillableSolution
    solution: cube
  - type: Sprite
    sprite: Objects/Misc/monkeycube.rsi
    state: cube
  - type: Reactive
    reactions:
    - reagents: [Water]
      methods: [Touch, Ingestion, Injection]
      effects:
      - !type:AddToSolutionReaction
        solution: cube
  - type: Rehydratable
    possibleSpawns:
    - MobMonkey
  - type: CollisionWake
    enabled: false
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.3,-0.3,0.3,0.3"
        density: 5
        mask:
        - ItemMask
      rehydrate:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.3,-0.3,0.3,0.3"
        hard: false
        layer:
        - LowImpassable

- type: entity
  parent: MonkeyCube
  id: KoboldCube
  name: "кобольдський куб"
  components:
  - type: Rehydratable
    possibleSpawns:
    - MobKobold

- type: entity
  parent: MonkeyCube
  id: CowCube
  name: "коров'ячий куб"
  components:
  - type: Rehydratable
    possibleSpawns:
    - MobCow

- type: entity
  parent: MonkeyCube
  id: GoatCube
  name: "козячий кубик"
  components:
  - type: Rehydratable
    possibleSpawns:
    - MobGoat

- type: entity
  parent: MonkeyCube
  id: MothroachCube
  name: "кубик тарганомолі"
  components:
  - type: Rehydratable
    possibleSpawns:
    - MobMothroach

- type: entity
  parent: MonkeyCube
  id: MouseCube
  name: "мишачий куб"
  components:
  - type: Rehydratable
    possibleSpawns:
    - MobMouse

- type: entity
  parent: MonkeyCube
  id: CockroachCube
  name: "кубик для тарганів"
  description: "Просто додайте ва- О, БОЖЕ!"
  components:
  - type: Rehydratable
    possibleSpawns:
    - MobCockroach

- type: entity
  parent: MonkeyCube
  id: SpaceCarpCube
  name: "короповий кубик"
  description: "Просто додай води! На свій страх і ризик."
  components:
  - type: Rehydratable
    possibleSpawns:
    - MobCarp

- type: entity
  parent: MonkeyCube
  id: SpaceTickCube
  name: "кубик для галочок"
  description: "Просто додай води! На свій страх і ризик."
  components:
  - type: Rehydratable
    possibleSpawns:
    - MobTick

- type: entity
  parent: MonkeyCube
  id: AbominationCube
  name: "куб мерзоти"
  description: "Просто додай крові!"
  components:
  - type: SolutionContainerManager
    solutions:
      cube:
        maxVol: 10 # needs room for more blood
        reagents:
        - ReagentId: Blood
          Quantity: 9
  - type: Reactive
    reactions:
    - reagents: [Blood]
      methods: [Touch, Ingestion, Injection]
      effects:
      - !type:AddToSolutionReaction
        solution: cube
  - type: Rehydratable
    catalyst: Blood # blood is fuel
    catalystMinimum: 10
    possibleSpawns:
    - MobAbomination

- type: entity
  parent: PlushieCarp
  id: DehydratedSpaceCarp
  name: "зневоднений космічний короп"
  description: "Виглядає як плюшевий іграшковий короп, але просто додайте води, і він стане справжнім космічним коропом!"
  components:
  - type: SolutionContainerManager
    solutions:
      plushie:
        maxVol: 11 # needs room for water
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
  - type: RefillableSolution
    solution: plushie
  - type: Reactive
    reactions:
    - reagents: [Water]
      methods: [Touch, Ingestion, Injection]
      effects:
      - !type:AddToSolutionReaction
        solution: plushie
  - type: Rehydratable
    possibleSpawns:
    - MobCarp
  - type: CollisionWake
    enabled: false
  - type: Physics
    bodyType: KinematicController
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.3,-0.3,0.3,0.3"
        density: 15
        mask:
        - ItemMask
      rehydrate:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.3,-0.3,0.3,0.3"
        hard: false
        layer:
        - LowImpassable
  # pet fish before rehydrating and he will be nice to you
  - type: FactionException
  - type: PettableFriend
    successString: petting-success-dehydrated-carp
    failureString: petting-failure-dehydrated-carp
  - type: EmitSoundOnUse
    handle: false
    sound:
      path: /Audio/Effects/bite.ogg
  - type: Tag
    tags: [] # DeltaV - remove PlushieCarp tag to prevent wasting

- type: entity
  parent: BaseItem
  id: SyndicateSponge
  name: "мавпячий куб"
  suffix: Syndicate
  description: "Просто додайте води!"
  components:
  - type: Item
    size: Tiny
  - type: SolutionContainerManager
    solutions:
      cube:
        maxVol: 11 # needs room for water
        reagents:
        - ReagentId: Nutriment
          Quantity: 10
  - type: Food
    solution: cube
  - type: RefillableSolution
    solution: cube
  - type: Sprite
    sprite: Objects/Misc/monkeycube.rsi
    state: cube
  - type: Reactive
    reactions:
    - reagents: [Water]
      methods: [Touch, Ingestion, Injection]
      effects:
      - !type:AddToSolutionReaction
        solution: cube
  - type: Rehydratable
    possibleSpawns:
    - MobCarpHolo
    - MobXenoRavager
    - MobAngryBee
    - MobAdultSlimesYellowAngry
    - MobSpiderSpace
    - MobBearSpace
    - MobPurpleSnake
    - MobKangarooSpace
    - MobTick
  - type: CollisionWake
    enabled: false
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.3,-0.3,0.3,0.3"
        density: 5
        mask:
        - ItemMask
      rehydrate:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.3,-0.3,0.3,0.3"
        hard: false
        layer:
        - LowImpassable
