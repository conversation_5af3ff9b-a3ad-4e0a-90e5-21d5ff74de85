- type: construction
  id: GlimmerProber
  name: "глімерний зонд"
  description: "Збільшує кількість мерехтінь і точок дослідження."
  graph: GlimmerDevices
  startNode: start
  targetNode: glimmerProber
  category: construction-category-machines
  icon:
    sprite: Nyanotrasen/Structures/Machines/glimmer_machines.rsi
    state: base
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  id: GlimmerDrain
  name: "зливник глімеру"
  description: "Зменшує мерехтіння."
  graph: GlimmerDevices
  startNode: start
  targetNode: glimmerDrain
  category: construction-category-machines
  icon:
    sprite: Nyanotrasen/Structures/Machines/glimmer_machines.rsi
    state: base
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

# - type: construction
#   id: Golem
#   name: golem
#   description: Your very own personal servant! Requires a soul crystal to activate.
#   graph: Golem
#   startNode: start
#   targetNode: golem
#   category: construction-category-machines
#   icon:
#       sprite: Nyanotrasen/Mobs/Species/Golem/cult.rsi
#       state: full
#   objectType: Structure
#   placementMode: SnapgridCenter
#   canBuildInImpassable: False
#   conditions:
#     - !type:TileNotBlocked

# - type: construction
#   id: GolemWood
#   name: wood golem
#   description: Your very own personal servant! This one can hold plants. Requires a soul crystal to activate.
#   graph: GolemWood
#   startNode: start
#   targetNode: golem
#   category: construction-category-machines
#   icon:
#       sprite: Mobs/Species/Diona/parts.rsi
#       state: full
#   objectType: Structure
#   placementMode: SnapgridCenter
#   canBuildInImpassable: False
#   conditions:
#     - !type:TileNotBlocked

# - type: construction
#   id: GolemSilver
#   name: silver golem
#   description: Your very own personal servant! Requires a soul crystal to activate.
#   graph: GolemSilver
#   startNode: start
#   targetNode: golem
#   category: construction-category-machines
#   icon:
#       sprite: Nyanotrasen/Mobs/Species/Golem/silver.rsi
#       state: full
#   objectType: Structure
#   placementMode: SnapgridCenter
#   canBuildInImpassable: False
#   conditions:
#     - !type:TileNotBlocked
