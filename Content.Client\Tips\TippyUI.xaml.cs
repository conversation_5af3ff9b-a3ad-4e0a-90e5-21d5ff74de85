using Content.Client.Paper;
using Robust.Client.AutoGenerated;
using Robust.Client.Graphics;
using Robust.Client.ResourceManagement;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Tips;

[GenerateTypedNameReferences]
public sealed partial class TippyUI : UIWidget
{
    public TippyState State = TippyState.Hidden;
    public bool ModifyLayers = true;

    public TippyUI()
    {
        RobustXamlLoader.Load(this);
    }

    public void InitLabel(PaperVisualsComponent? visuals, IResourceCache resCache)
    {
        if (visuals == null)
            return;

        Label.ModulateSelfOverride = visuals.FontAccentColor;

        if (visuals.BackgroundImagePath == null)
            return;

        LabelPanel.ModulateSelfOverride = visuals.BackgroundModulate;
        var backgroundImage = resCache.GetResource<TextureResource>(visuals.BackgroundImagePath);
        var backgroundImageMode = visuals.BackgroundImageTile ? StyleBoxTexture.StretchMode.Tile : StyleBoxTexture.StretchMode.Stretch;
        var backgroundPatchMargin = visuals.BackgroundPatchMargin;
        LabelPanel.PanelOverride = new StyleBoxTexture
        {
            Texture = backgroundImage,
            TextureScale = visuals.BackgroundScale,
            Mode = backgroundImageMode,
            PatchMarginLeft = backgroundPatchMargin.Left,
            PatchMarginBottom = backgroundPatchMargin.Bottom,
            PatchMarginRight = backgroundPatchMargin.Right,
            PatchMarginTop = backgroundPatchMargin.Top
        };
    }

    public enum TippyState : byte
    {
        Hidden,
        Revealing,
        Speaking,
        Hiding,
    }
}
