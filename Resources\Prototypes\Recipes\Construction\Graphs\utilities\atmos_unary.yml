- type: constructionGraph
  id: GasUnary
  start: start
  graph:
  - node: start
    edges:
    - to: ventpump
      steps:
      - material: Steel
        amount: 2
        doAfter: 1

    - to: passivevent
      steps:
      - material: Steel
        amount: 2
        doAfter: 1

    - to: ventscrubber
      steps:
      - material: Steel
        amount: 2
        doAfter: 1

    - to: outletinjector
      steps:
      - material: Steel
        amount: 2
        doAfter: 1

  - node: ventpump
    entity: GasVentPump
    edges:
    - to: start
      conditions:
      - !type:EntityAnchored
        anchored: false
      completed:
      - !type:SpawnPrototype
        prototype: SheetSteel1
        amount: 2
      - !type:DeleteEntity
      steps:
      - tool: Screwing
        doAfter: 1
      - tool: Welding
        doAfter: 1

  - node: passivevent
    entity: GasPassiveVent
    edges:
    - to: start
      conditions:
      - !type:EntityAnchored
        anchored: false
      completed:
      - !type:SpawnPrototype
        prototype: SheetSteel1
        amount: 2
      - !type:DeleteEntity
      steps:
      - tool: Screwing
        doAfter: 1
      - tool: Welding
        doAfter: 1

  - node: ventscrubber
    entity: GasVentScrubber
    edges:
    - to: start
      conditions:
      - !type:EntityAnchored
        anchored: false
      completed:
      - !type:SpawnPrototype
        prototype: SheetSteel1
        amount: 2
      - !type:DeleteEntity
      steps:
      - tool: Screwing
        doAfter: 1
      - tool: Welding
        doAfter: 1

  - node: outletinjector
    entity: GasOutletInjector
    edges:
    - to: start
      conditions:
      - !type:EntityAnchored
        anchored: false
      completed:
      - !type:SpawnPrototype
        prototype: SheetSteel1
        amount: 2
      - !type:DeleteEntity
      steps:
      - tool: Welding
        doAfter: 1
