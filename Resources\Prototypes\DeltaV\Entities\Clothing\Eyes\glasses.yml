- type: entity
  parent: ClothingEyesBase
  id: ClothingEyesGlassesChemist
  name: "захисні окуляри"
  description: "Окуляри для хіміків та інших працівників, що мають справу з їдкими реагентами."
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Eyes/Glasses/safetyglasses.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Eyes/Glasses/safetyglasses.rsi
  - type: Armor
    modifiers:
      coefficients:
        Caustic: 0.85

- type: entity
  parent: ClothingEyesGlassesGar
  id: ClothingEyesGlassesGarMeson
  name: "гар-мезони"
  description: "Робити неможливе, бачити невидиме!"
  components:
  - type: EyeProtection

- type: entity
  parent: [ClothingEyesBase, ShowSecurityIcons, ShowMedicalIcons, ClothingEyesHudMedical]
  id: ClothingEyesGlassesCorpsman
  name: "окуляри санітара"
  description: "Захисні окуляри, розроблені для санітарів для медичних та охоронних потреб. Тепер з прохолодним синім відтінком!"
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Eyes/Glasses/corpsglasses.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Eyes/Glasses/corpsglasses.rsi
  - type: FlashImmunity
  - type: EyeProtection
    protectionTime: 5
  - type: Construction
    graph: GlassesCorpsHUD
    node: glassesCorps
  - type: Tag
    tags:
    - HamsterWearable
    - WhitelistChameleon
    - SecDogWearable
  - type: GuideHelp
    guides:
    - Security
    - Antagonists
    - Medical
  - type: IdentityBlocker
    coverage: EYES
  - type: ShowHealthBars
    damageContainers:
    - Biological
  - type: ShowHealthIcons
    damageContainers:
    - Biological
