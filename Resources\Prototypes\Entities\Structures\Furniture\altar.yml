- type: entity
  id: AltarBase
  parent: BaseStructure
  name: "вівтар"
  description: "Вівтар богів."
  abstract: true
  components:
  - type: Anchorable
  - type: Transform
  - type: Prayable
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: PlaceableSurface
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.45"
        density: 55
        mask: #mouse can pass under, but I think this is can understandable by logic
        - TableMask
        layer:
        - TableLayer
  - type: Sprite
    snapCardinals: true
  - type: Clickable
  - type: SacrificialAltar
  - type: Strap
    position: Down
    rotation: -90
  - type: GuideHelp
    guides:
    - AltarsGolemancy

- type: entity
  id: AltarNanotrasen
  parent: AltarBase
  name: "вівтар Нанотрейзен"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    state: nanotrasen
  - type: Icon
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    state: nanotrasen
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 125
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 25
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 5
          MaterialCloth1:
            min: 1
            max: 3
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction #Nyano
    graph: Altar
    node: nanotrasen
  - type: StealTarget
    stealGroup: AltarNanotrasen

- type: entity
  id: AltarChaos
  parent: AltarNanotrasen
  name: "вівтар хаосу"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    state: chaos
  - type: Icon
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    state: chaos
  - type: Construction #Nyano
    graph: Altar
    node: chaos

- type: entity
  id: AltarDruid
  parent: AltarNanotrasen
  name: "друїдський вівтар"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    state: druid
  - type: Icon
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    state: druid
  - type: Construction #Nyano
    graph: Altar
    node: druid

- type: entity
  id: AltarToolbox
  parent: AltarNanotrasen
  name: "вівтар ящику для інструментів"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    state: toolbox
  - type: Icon
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    state: toolbox
  - type: Construction #Nyano
    graph: Altar
    node: toolbox

- type: entity
  id: AltarSpaceChristian
  parent: AltarNanotrasen
  name: "космічно-християнський вівтар"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    layers:
    - state: space-christian
    - state: space-christian_lightning
      shader: unshaded
  - type: Icon
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    state: space-christian
  - type: PointLight
    radius: 1.5
    energy: 1
    color: "#fbceb1"
  - type: Construction #Nyano
    graph: Altar
    node: spacechristian

- type: entity
  id: AltarSatana
  parent: AltarNanotrasen
  name: "сатанинський вівтар"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    layers:
    - state: satana
    - state: satana_lightning
      shader: unshaded
  - type: Icon
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    state: satana
  - type: PointLight
    radius: 1.5
    energy: 1
    color: "#ff6347"
  - type: Construction #Nyano
    graph: Altar
    node: satana

- type: entity
  id: AltarTechnology
  parent: AltarNanotrasen
  name: "вівтар технологій"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    layers:
    - state: technology
    - state: technology_lightning
      shader: unshaded
  - type: Icon
    sprite: Structures/Furniture/Altars/Gods/nanotrasen.rsi
    state: technology
  - type: PointLight
    radius: 1.5
    energy: 1
    color: "#e0ffff"
  - type: Construction #Nyano
    graph: Altar
    node: technology

- type: entity
  id: AltarConvertFestival
  parent: AltarBase
  name: "вівтар фестивалю"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    state: festival
  - type: Icon
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    state: festival
  - type: Damageable
    damageModifierSet: Wood
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 15
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroyHeavy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank:
            min: 1
            max: 5
          MaterialCloth1:
            min: 1
            max: 3
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Tag
    tags:
    - Wooden
  - type: Construction #Nyano
    graph: Altar
    node: festival

- type: entity
  id: AltarConvertMaint
  parent: AltarConvertFestival
  name: "вівтар технічних тунелей"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    layers:
    - state: maint
    - state: lightning
      shader: unshaded
  - type: Icon
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    state: maint
  - type: PointLight
    radius: 1.5
    energy: 1
    color: "#fbceb1"
  - type: Construction #Nyano
    graph: Altar
    node: maint

- type: entity
  id: AltarConvertBlue
  parent: AltarConvertFestival
  name: "блакитний вівтар"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    layers:
    - state: blue
    - state: lightning
      shader: unshaded
  - type: Icon
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    state: blue
  - type: PointLight
    radius: 1.5
    energy: 1
    color: "#fbceb1"
  - type: Construction #Nyano
    graph: Altar
    node: blue

- type: entity
  id: AltarConvertBurden
  parent: AltarConvertFestival
  name: "вівтар тягару"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    layers:
    - state: convertaltar
    - state: lightning
      shader: unshaded
  - type: Icon
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    state: convertaltar
  - type: PointLight
    radius: 1.5
    energy: 1
    color: "#fbceb1"
  - type: Construction #Nyano
    graph: Altar
    node: burden

- type: entity
  id: AltarConvert
  parent: AltarConvertFestival
  name: "перетворюючий вівтар"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    layers:
    - state: convertaltar
    - state: lightning
      shader: unshaded
  - type: Icon
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    state: convertaltar
  - type: PointLight
    radius: 1.5
    energy: 1
    color: "#fbceb1"
  - type: Construction #Nyano
    graph: Altar
    node: convert

- type: entity
  id: AltarConvertOrange
  parent: AltarConvertFestival
  name: "помаранчевий вівтар"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    layers:
    - state: orange
    - state: lightning
      shader: unshaded
  - type: Icon
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    state: orange
  - type: PointLight
    radius: 1.5
    energy: 1
    color: "#fbceb1"
  - type: Construction #Nyano
    graph: Altar
    node: orange

- type: entity
  id: AltarConvertRed
  parent: AltarConvertFestival
  name: "червоний вівтар"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    layers:
    - state: red
    - state: lightning
      shader: unshaded
  - type: Icon
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    state: red
  - type: PointLight
    radius: 1.5
    energy: 1
    color: "#fbceb1"
  - type: Construction #Nyano
    graph: Altar
    node: red

- type: entity
  id: AltarConvertWhite
  parent: AltarConvertFestival
  name: "білий вівтар"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    layers:
    - state: white
    - state: lightning
      shader: unshaded
  - type: Icon
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    state: white
  - type: PointLight
    radius: 1.5
    energy: 1
    color: "#fbceb1"
  - type: Construction #Nyano
    graph: Altar
    node: white

- type: entity
  id: AltarConvertYellow
  parent: AltarConvertFestival
  name: "жовтий вівтар"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    layers:
    - state: yellow
    - state: lightning
      shader: unshaded
  - type: Icon
    sprite: Structures/Furniture/Altars/Gods/convertaltar.rsi
    state: yellow
  - type: PointLight
    radius: 1.5
    energy: 1
    color: "#fbceb1"
  - type: Construction #Nyano
    graph: Altar
    node: yellow

- type: entity
  id: AltarHeaven
  parent: AltarBase
  name: "небесний вівтар"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Altars/Cults/heaven.rsi
    layers:
    - state: full
    - state: blood
      shader: unshaded
  - type: Icon
    sprite: Structures/Furniture/Altars/Cults/heaven.rsi
    state: full
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 125
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 25
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 5
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: PointLight
    radius: 1.5
    energy: 1
    color: "#f08080"
  - type: Construction #Nyano
    graph: Altar
    node: heaven

- type: entity
  id: AltarFangs
  parent: AltarHeaven
  name: "ікластий вівтар"
  components:
  - type: Sprite
    sprite: Structures/Furniture/Altars/Cults/fangs.rsi
  - type: Icon
    sprite: Structures/Furniture/Altars/Cults/fangs.rsi
  - type: Construction #Nyano
    graph: Altar
    node: fanged


- type: entity
  id: AltarBananium
  parent: AltarBase
  name: "вівтар Хонкоматері"
  description: "Банановий вівтар, присвячений матері-берегині."
  components:
  - type: Sprite
    sprite: Structures/Furniture/Altars/Cults/bananium.rsi
    state: full
  - type: Construction
    graph: BananiumAltarGraph
    node: bananiumAltar
