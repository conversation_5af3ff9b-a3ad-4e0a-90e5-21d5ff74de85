- type: entity
  id: BaseShutter
  parent: BaseStructure
  name: "затвор"
  abstract: true
  description: "Здригаєшся, коли думаєш про те, що може бути за цими віконницями."
  placement:
    mode: SnapgridCenter
  components:
  - type: StationAiWhitelist
  - type: Sprite
    sprite: Structures/Doors/Shutters/shutters.rsi
    drawdepth: BlastDoors
    layers:
    - state: closed
      map: ["enum.DoorVisualLayers.Base"]
  - type: AnimationPlayer
  - type: Physics
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.49,-0.49,0.49,0.49" # don't want this colliding with walls or they won't close
        density: 100
        mask:
        - FullTileMask
        layer:
        - AirlockLayer
  - type: ContainerFill
    containers:
      board: [ DoorElectronics ]
  - type: ContainerContainer
    containers:
      board: !type:Container
  - type: NavMapDoor
  - type: Door
    openDrawDepth: BlastDoors
    closedDrawDepth: BlastDoors
    bumpOpen: false
    clickOpen: false
    closeTimeOne: 0.2
    closeTimeTwo: 1.2
    openTimeOne: 1.2
    openTimeTwo: 0.2
    openingAnimationTime: 1.4
    closingAnimationTime: 1.4
    crushDamage:
      types:
        Blunt: 5 # getting shutters closed on you probably doesn't hurt that much
    openSound:
      path: /Audio/Machines/blastdoor.ogg
    closeSound:
      path: /Audio/Machines/blastdoor.ogg
  - type: Weldable
    time: 3
  - type: Appearance
  - type: UserInterface
    interfaces:
      enum.WiresUiKey.Key:
        type: WiresBoundUserInterface
  - type: Airtight
  - type: RadiationBlocker
    resistance: 1
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: StrongMetallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 500
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: DoorSignalControl
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: BasicDevice
  - type: WirelessNetworkConnection
    range: 200
  - type: DeviceLinkSink
    ports:
      - Open
      - Close
      - Toggle
  - type: DeviceLinkSource
    ports:
    - DoorStatus
    lastSignals:
      DoorStatus: false
  - type: BlockWeather
  - type: InteractionVerbs
    allowedVerbs:
    - KnockOn


- type: entity
  id: ShuttersNormal
  parent: BaseShutter
  components:
  - type: Occluder
  - type: Construction
    graph: Shutters
    node: Shutters
    containers:
    - board

- type: entity
  id: ShuttersNormalOpen
  parent: ShuttersNormal
  suffix: Open
  components:
  - type: Door
    state: Open
  - type: Physics
    canCollide: false
  - type: Occluder
    enabled: false
  - type: Airtight
    airBlocked: false
  - type: RadiationBlocker
    enabled: false

- type: entity
  id: ShuttersRadiation
  parent: BaseShutter
  name: "радіаційні жалюзі"
  description: "Відносно крихкий набір жалюзі, виготовлений зі свинцевих пластин, що блокують випромінювання."
  components:
  - type: Sprite
    sprite: Structures/Doors/Shutters/shutters_radiation.rsi
    layers:
    - state: closed
      map: ["enum.DoorVisualLayers.Base"]
  - type: Occluder
  - type: Construction
    graph: Shutters
    node: ShuttersRadiation
    containers:
    - board
  - type: RadiationBlocker
    resistance: 10
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]

- type: entity
  id: ShuttersRadiationOpen
  parent: ShuttersRadiation
  suffix: Open
  components:
  - type: Door
    state: Open
  - type: Occluder
    enabled: false
  - type: Physics
    canCollide: false
  - type: Airtight
    airBlocked: false
  - type: RadiationBlocker
    enabled: false

- type: entity
  id: ShuttersWindow
  parent: BaseShutter
  name: "скляні жалюзі"
  description: "Це затвор - з'єднайте його з кнопкою, щоб відкривати і закривати"
  components:
  - type: Sprite
    sprite: Structures/Doors/Shutters/shutters_window.rsi
    layers:
    - state: closed
      map: ["enum.DoorVisualLayers.Base"]
  - type: Construction
    graph: Shutters
    node: ShuttersWindow
    containers:
    - board
  - type: RadiationBlocker
    resistance: 1

- type: entity
  id: ShuttersWindowOpen
  parent: ShuttersWindow
  suffix: Open
  components:
  - type: Door
    state: Open
  - type: Physics
    canCollide: false
  - type: Airtight
    airBlocked: false
  - type: RadiationBlocker
    enabled: false

# Frame for construction
- type: entity
  id: ShuttersFrame
  parent: BaseStructureDynamic
  name: "рама затвора"
  description: "Рама для побудови віконниці."
  components:
  - type: Sprite
    sprite: Structures/Doors/Shutters/shutters.rsi
    state: frame
  - type: Construction
    graph: Shutters
    node: frame1
    containers:
    - board
  - type: InteractionOutline
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 500
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Physics
    bodyType: Dynamic
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.45,-0.45,0.45,0.45"
        density: 55
        mask:
        - Impassable
        - HighImpassable
        layer:
        - HighImpassable
  - type: Transform
    noRot: true
