# To be implemented: see #9072

- type: entity
  name: "посох зцілення"
  parent: WeaponStaffBase
  id: WeaponStaffHealing
  description: "Ви не передбачаєте, що вам доведеться використовувати його в пошуках кривавої бійні занадто часто." # Goobstation
  components:
  - type: Sprite
    sprite: _Goobstation/Wizard/Objects/Weapons/Staves/healing.rsi # Goobstation
    layers:
    - state: healing
  - type: Item
    heldPrefix: healing
  - type: Gun
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/Magic/staff_healing.ogg
  - type: BasicEntityAmmoProvider
    proto: ProjectileHealingBolt
    capacity: 6 # Goob edit
    count: 6 # Goob edit
  - type: RechargeBasicEntityAmmo # Goobstation
    rechargeCooldown: 8

- type: entity
  name: "персонал на вході"
  parent: WeaponStaffBase
  id: WeaponStaffPolymorphDoor
  description: "Для тих випадків, коли вам потрібен маршрут для втечі." # Goob station
  components:
  - type: Sprite
    sprite: _Goobstation/Wizard/Objects/Weapons/Staves/door.rsi # Goobstation
    layers:
    - state: door
  - type: Item
    heldPrefix: door
  - type: Gun
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/Magic/staff_door.ogg
  - type: BasicEntityAmmoProvider
    proto: ProjectilePolyboltDoor
    capacity: 10
    count: 10
  - type: RechargeBasicEntityAmmo # Goobstation
    rechargeCooldown: 2
