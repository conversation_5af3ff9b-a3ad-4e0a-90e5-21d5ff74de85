﻿loadcharacter-command-description = Applies your currently selected character to an entity
loadcharacter-command-help = Usage: loadcharacter | loadcharacter <entityUid> | loadcharacter <entityUid> <characterName>
loadcharacter-command-mismatch = Species mismatch detected between character and selected entity, this may have unexpected results.
loadcharacter-command-complete = Character loaded.
loadcharacter-command-fetching = Fetching character data for {$name}...
loadcharacter-command-fetching-failed = Failed to fetch character data!
loadcharacter-command-failed-fetching = Profile fetching failed???
loadcharacter-command-hint-select = Select character

spawncharacter-command-description = Spawns your currently selected/specified character
spawncharacter-command-help = Usage: spawncharacter | spawncharacter <characterName>
spawncharacter-command-complete = Character spawned.
