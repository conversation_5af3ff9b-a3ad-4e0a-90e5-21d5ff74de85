# TODO:RESET:TIMEDSTORAGEFILL

# Metal Closets
- type: entity
  parent: ClosetBase
  id: ClosetBase2
  abstract: true
  components:
  - type: Sprite
    sprite: Structures/Storage/Closets/closet.rsi
    layers:
    - state: closet
      map: ["enum.StorageVisualLayers.Base"]
    - state: closet_door
      map: ["enum.StorageVisualLayers.Door"]
  - type: EntityStorageVisuals
    stateBaseClosed: closet
    stateDoorOpen: closet_open
    stateDoorClosed: closet_door
  - type: Transform
    anchored: true
    noRot: true
  - type: Physics
    bodyType: Static
  - type: Anchorable # Makes the anchoring near impossible due to high time requirement
    delay: 3600

- type: entity
  parent: ClosetBase2
  id: ClosetBaseW
  name: "шафа"
  description: "Базова шафа для зберігання речей."
  components:
  - type: Weldable
  - type: Sprite
    noRot: true
    netsync: false
    sprite: Structures/Storage/Closets/closet.rsi
    layers:
    - state: closet
      map: ["enum.StorageVisualLayers.Base"]
    - state: closet_door
      map: ["enum.StorageVisualLayers.Door"]
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
  - type: EntityStorageVisuals
    stateBaseClosed: closet
    stateDoorOpen: closet_open
    stateDoorClosed: closet_door


- type: entity
  parent: ClosetBaseW
  id: ClosetGrey1
  components:
  - type: Sprite
    sprite: Structures/Storage/Closets/closetgrey.rsi
  - type: Weldable

- type: entity
  id: ClosetGrey2
  parent: ClosetBaseW
  components:
  - type: Sprite
    sprite: Structures/Storage/Closets/closetgrey2.rsi

- type: entity
  parent: ClosetBaseW
  id: ClosetRusty
  name: "іржава шафа"
  description: "Стара іржава шафа для зберігання речей."
  components:
  - type: Sprite
    sprite: Structures/Storage/Closets/closetold.rsi

- type: entity
  parent: ClosetBaseW
  id: ClosetGunCabinet
  name: "шафа для зброї"
  description: "Безпечна шафа для зберігання зброї."
  components:
  - type: Sprite
    sprite: Structures/Storage/Closets/guncabinet.rsi

- type: entity
  parent: ClosetBaseW
  id: ClosetFridgeDirty
  name: "холодильник"
  description: "Брудний старий холодильник для зберігання свіжих продуктів"
  components:
  - type: Sprite
    sprite: Structures/Storage/Closets/fridgedirty.rsi
  - type: ExplosionResistance
    damageCoefficient: 0.90
  - type: AntiRottingContainer

- type: entity
  parent: ClosetBaseW
  id: ClosetFridgeWideDirty
  name: "холодильник"
  description: "Брудний старий холодильник для зберігання свіжих продуктів"
  components:
  - type: Sprite
    sprite: Structures/Storage/Closets/fridgewidedirty.rsi
  - type: ExplosionResistance
    damageCoefficient: 0.90
  - type: AntiRottingContainer

- type: entity
  parent: ClosetBaseW
  id: ClosetDouble
  name: "подвійна шафа"
  description: "Подвійна шафа для зберігання вдвічі більшої кількості речей."
  components:
  - type: Sprite
    sprite: Structures/Storage/Closets/doublecloset.rsi
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
            bounds: "-0.15,-0.45,0.45,0.45"
        density: 145
        mask:
        - MachineMask
        layer:
        - MachineLayer

# Wooden Closets

- type: entity
  parent: ClosetBase2
  id: ClosetCabinetWood
  name: "шафа"
  description: "Стара довоєнна дерев'яна шафа."
  components:
  - type: Sprite
    sprite: Structures/Storage/Closets/cabinet.rsi
  - type: Damageable
    damageModifierSet: Wood
    damageContainer: Inorganic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 30
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank:
            min: 0
            max: 1
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Tag
    tags:
    - Wooden

- type: entity
  parent: ClosetBaseW
  id: ClosetGeneric
  suffix: generic roller
  components:
  - type: Sprite
    sprite: Structures/Storage/Closets/closetgeneric.rsi

# Wallmounted Closets
- type: entity
  id: ClosetWallMedicabinet
  placement:
    mode: SnapgridCenter
  name: "медична шафа"
  description: "Медична шафа, закріплена на стіні."
  components:
  - type: InteractionOutline
  - type: Clickable
  - type: ResistLocker
  - type: Weldable
  - type: WallMount
    arc: 180
  - type: Transform
    noRot: false
  - type: Sprite
    drawdepth: WallMountedItems
    netsync: false
    noRot: false
    sprite: Structures/Storage/Closets/medicabinet.rsi
    layers:
    - state: closet
    - state: closet_door
      map: ["enum.StorageVisualLayers.Door"]
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
  - type: EntityStorage
    isCollidableWhenOpen: true
    enteringOffset: 0, -0.75
    closeSound:
      path: /Audio/Items/deconstruct.ogg
    openSound:
      path: /Audio/Items/deconstruct.ogg
  - type: ContainerContainer
    containers:
      entity_storage: !type:Container
        ents: []
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 1
