﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <!-- Work around https://github.com/dotnet/project-system/issues/4314 -->
    <TargetFramework>$(TargetFramework)</TargetFramework>
    <LangVersion>12</LangVersion>
    <IsPackable>false</IsPackable>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <WarningsAsErrors>nullable</WarningsAsErrors>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="JetBrains.Annotations" PrivateAssets="All" />
    <PackageReference Include="MathNet.Numerics" PrivateAssets="All" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Content.Shared.Database\Content.Shared.Database.csproj" />
    <ProjectReference Include="..\RobustToolbox\Lidgren.Network\Lidgren.Network.csproj">
      <Private>false</Private>
    </ProjectReference>
    <ProjectReference Include="..\RobustToolbox\Robust.Shared.Maths\Robust.Shared.Maths.csproj">
      <Private>false</Private>
    </ProjectReference>
    <ProjectReference Include="..\RobustToolbox\Robust.Shared\Robust.Shared.csproj">
      <Private>false</Private>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="_EE\Shadowling\Components\Objectives\" />
  </ItemGroup>
  <Import Project="..\RobustToolbox\MSBuild\Robust.Properties.targets" />
  <Import Project="..\RobustToolbox\MSBuild\Robust.CompNetworkGenerator.targets" />
</Project>
