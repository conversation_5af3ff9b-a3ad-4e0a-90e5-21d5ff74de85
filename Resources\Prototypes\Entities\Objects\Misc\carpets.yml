# TODO once tiles can be smoothed and carpets ported over to that, add them to the FloorTile outputs
- type: entity
  parent: FloorTileItemBase
  id: FloorCarpetItemRed
  name: "червоний килим"
  suffix: Item
  components:
  - type: Sprite
    state: carpet-red
  - type: Item
    heldPrefix: carpet-red
  - type: FloorTile
    outputs:
      - Plating
  - type: Stack
    stackType: FloorCarpetRed
  - type: Tag
    tags:
    - CarpetRed
  - type: SpawnAfterInteract #Nuke after convert to FloorTile
    prototype: Carpet
    doAfter: 0.5
    removeOnInteract: true
  - type: Material

- type: entity
  parent: FloorCarpetItemRed
  id: FloorCarpetItemBlack
  name: "чорний килим"
  suffix: Item
  components:
  - type: Sprite
    state: carpet-black
  - type: Item
    heldPrefix: carpet-black
  - type: Stack
    stackType: FloorCarpetBlack
  - type: Tag
    tags:
    - CarpetBlack
  - type: SpawnAfterInteract #Nuke after convert to FloorTile
    prototype: CarpetBlack
    doAfter: 0.5
    removeOnInteract: true
  - type: Material

- type: entity
  parent: FloorCarpetItemRed
  id: FloorCarpetItemBlue
  name: "синій килим"
  suffix: Item
  components:
  - type: Sprite
    state: carpet-blue
  - type: Item
    heldPrefix: carpet-blue
  - type: Stack
    stackType: FloorCarpetBlue
  - type: Tag
    tags:
    - CarpetBlue
  - type: SpawnAfterInteract #Nuke after convert to FloorTile
    prototype: CarpetBlue
    doAfter: 0.5
    removeOnInteract: true
  - type: Material

- type: entity
  parent: FloorCarpetItemRed
  id: FloorCarpetItemGreen
  name: "зелений килим"
  suffix: Item
  components:
  - type: Sprite
    state: carpet-green
  - type: Item
    heldPrefix: carpet-green
  - type: Stack
    stackType: FloorCarpetGreen
  - type: Tag
    tags:
    - CarpetGreen
  - type: SpawnAfterInteract #Nuke after convert to FloorTile
    prototype: CarpetGreen
    doAfter: 0.5
    removeOnInteract: true
  - type: Material

- type: entity
  parent: FloorCarpetItemRed
  id: FloorCarpetItemOrange
  name: "помаранчевий килим"
  suffix: Item
  components:
  - type: Sprite
    state: carpet-orange
  - type: Item
    heldPrefix: carpet-orange
  - type: Stack
    stackType: FloorCarpetOrange
  - type: Tag
    tags:
    - CarpetOrange
  - type: SpawnAfterInteract #Nuke after convert to FloorTile
    prototype: CarpetOrange
    doAfter: 0.5
    removeOnInteract: true
  - type: Material

- type: entity
  parent: FloorCarpetItemRed
  id: FloorCarpetItemSkyBlue
  name: "блакитний килим"
  suffix: Item
  components:
  - type: Sprite
    state: carpet-skyblue
  - type: Item
    heldPrefix: carpet-skyblue
  - type: Stack
    stackType: FloorCarpetSkyBlue
  - type: Tag
    tags:
    - CarpetSBlue
  - type: SpawnAfterInteract #Nuke after convert to FloorTile
    prototype: CarpetSBlue
    doAfter: 0.5
    removeOnInteract: true
  - type: Material

- type: entity
  parent: FloorCarpetItemRed
  id: FloorCarpetItemPurple
  name: "фіолетовий килим"
  suffix: Item
  components:
  - type: Sprite
    state: carpet-purple
  - type: Item
    heldPrefix: carpet-purple
  - type: Stack
    stackType: FloorCarpetPurple
  - type: Tag
    tags:
    - CarpetPurple
  - type: SpawnAfterInteract #Nuke after convert to FloorTile
    prototype: CarpetPurple
    doAfter: 0.5
    removeOnInteract: true
  - type: Material

- type: entity
  parent: FloorCarpetItemRed
  id: FloorCarpetItemPink
  name: "рожевий килим"
  suffix: Item
  components:
  - type: Sprite
    state: carpet-pink
  - type: Item
    heldPrefix: carpet-pink
  - type: Stack
    stackType: FloorCarpetPink
  - type: Tag
    tags:
    - CarpetPink
  - type: SpawnAfterInteract #Nuke after convert to FloorTile
    prototype: CarpetPink
    doAfter: 0.5
    removeOnInteract: true
  - type: Material

- type: entity
  parent: FloorCarpetItemRed
  id: FloorCarpetItemCyan
  name: "ціановий килим"
  suffix: Item
  components:
  - type: Sprite
    state: carpet-cyan
  - type: Item
    heldPrefix: carpet-cyan
  - type: Stack
    stackType: FloorCarpetCyan
  - type: Tag
    tags:
    - CarpetCyan
  - type: SpawnAfterInteract #Nuke after convert to FloorTile
    prototype: CarpetCyan
    doAfter: 0.5
    removeOnInteract: true
  - type: Material

- type: entity
  parent: FloorCarpetItemRed
  id: FloorCarpetItemWhite
  name: "білий килим"
  suffix: Item
  components:
  - type: Sprite
    state: carpet-white
  - type: Item
    heldPrefix: carpet-white
  - type: Stack
    stackType: FloorCarpetWhite
  - type: Tag
    tags:
    - CarpetWhite
  - type: SpawnAfterInteract #Nuke after convert to FloorTile
    prototype: CarpetWhite
    doAfter: 0.5
    removeOnInteract: true
  - type: Material
