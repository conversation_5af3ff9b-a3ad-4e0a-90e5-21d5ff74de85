- type: entity
  parent: BaseItem
  id: BaseHealingItem
  abstract: true
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/medical.rsi
  - type: Item
    size: Small
    sprite: Objects/Specific/Medical/medical.rsi
    heldPrefix: ointment
  # Inherited
  - type: StaticPrice
    price: 0

- type: entity
  name: "мазь"
  description: "Використовується для лікування неприємних опіків. Менш ефективний при опіках їдкими речовинами."
  parent: BaseHealingItem
  id: Ointment
  suffix: Full
  components:
  - type: Tag
    tags:
    - Ointment
  - type: Sprite
    state: ointment
  - type: Item
    heldPrefix: ointment
  - type: Healing
    damageContainers:
      - Biological
    damage:
      types:
        Heat: -10
        Cold: -10
        Shock: -10
        Caustic: -5 #Was 5 per type & 1.5 caustic, Buffed due to limb damage changes
    healingBeginSound:
      path: "/Audio/Items/Medical/ointment_begin.ogg"
    healingEndSound:
      path: "/Audio/Items/Medical/ointment_end.ogg"
  - type: Stack
    stackType: Ointment
    count: 15 #Was 10, Buffed due to limb damage changes
  - type: StackPrice
    price: 10 #Was 5, Buffed due to surgery changes

- type: entity
  id: Ointment1
  parent: Ointment
  suffix: Single
  components:
  - type: Stack
    stackType: Ointment
    count: 1

- type: entity
  id: Ointment10Lingering
  parent: Ointment
  suffix: 10, Lingering
  components:
  - type: Stack
    lingering: true
    count: 10

- type: entity
  name: "регенеративна сітка"
  description: "Використовується для лікування навіть найнеприємніших опіків. Також ефективний при опіках їдкими речовинами."
  parent: BaseHealingItem
  id: RegenerativeMesh
  suffix: Full
  components:
  - type: Tag
    tags:
    - Ointment
  - type: Sprite
    state: regenerative-mesh
  - type: Item
    heldPrefix: regenerative-mesh
  - type: Healing
    damageContainers:
      - Biological
    damage:
      types:
        Heat: -15
        Cold: -15
        Shock: -15
        Caustic: -15 #Was 10 per type, Buffed due to limb damage changes
    healingBeginSound:
      path: "/Audio/Items/Medical/ointment_begin.ogg"
    healingEndSound:
      path: "/Audio/Items/Medical/ointment_end.ogg"
  - type: Stack
    stackType: RegenerativeMesh
    count: 15 #Was 10, Buffed due to limb damage changes
  - type: StackPrice
    price: 40 #Was 20, Buffed due to limb damage changes

- type: entity
  id: OintmentAdvanced1
  parent: RegenerativeMesh
  suffix: Single
  components:
  - type: Stack
    stackType: RegenerativeMesh
    count: 1

- type: entity
  name: "пакет від синців"
  description: "Лікувальний гель-пакет та бинти, призначені для лікування травм від ударів тупими предметами."
  parent: BaseHealingItem
  id: Brutepack
  suffix: Full
  components:
  - type: Tag
    tags:
    - Brutepack
  - type: Sprite
    state: brutepack
  - type: Healing
    damageContainers:
      - Biological
    damage:
      groups:
        Brute: -30 # was 5 (-15 Brute) for each, Buffed due to limb damage changes
    healingBeginSound:
      path: "/Audio/Items/Medical/brutepack_begin.ogg"
    healingEndSound:
      path: "/Audio/Items/Medical/brutepack_end.ogg"
  - type: Stack
    stackType: Brutepack
    count: 15 #Was 10, Buffed due to limb damage changes
  - type: StackPrice
    price: 10 #Was 5, Buffed due to limb damage changes

- type: entity
  id: Brutepack1
  parent: Brutepack
  suffix: Single
  components:
  - type: Stack
    stackType: Brutepack
    count: 1

- type: entity
  id: Brutepack10Lingering
  parent: Brutepack
  suffix: 10, Lingering
  components:
  - type: Stack
    lingering: true
    count: 10

- type: entity
  name: "медикаментозний шов"
  description: "Шов, просочений ліками, ефективно лікує травми від ударів тупими предметами та закриває рани."
  parent: BaseHealingItem
  id: MedicatedSuture
  suffix: Full
  components:
  - type: Tag
    tags:
    - Brutepack
  - type: Sprite
    state: medicated-suture
  - type: Item
    heldPrefix: medicated-suture
    storedRotation: -90
  - type: Healing
    damageContainers:
      - Biological
    damage:
      groups:
        Brute: -45 # was 10 for each, now 20 for each type in the group, Buffed due to Limb Damage Changes
    bloodlossModifier: -10 # a suture should stop ongoing bleeding
    healingBeginSound:
      path: "/Audio/Items/Medical/brutepack_begin.ogg"
    healingEndSound:
      path: "/Audio/Items/Medical/brutepack_end.ogg"
  - type: Stack
    stackType: MedicatedSuture
    count: 15 #Was 10, Buffed due to surgery changes
  - type: StackPrice
    price: 40 #Was 20, Buffed due to limb damage changes

- type: entity
  id: BrutepackAdvanced1
  parent: MedicatedSuture
  suffix: Single
  components:
  - type: Stack
    stackType: MedicatedSuture
    count: 1

- type: entity
  name: "пакет крові"
  description: "Містить революційний універсальний кровозамінник, створений передовою медичною наукою Nanotrasen."
  parent: BaseHealingItem
  id: Bloodpack
  suffix: Full
  components:
  - type: Tag
    tags:
    - Bloodpack
  - type: Sprite
    state: bloodpack
  - type: Healing
    damageContainers:
      - Biological
    damage:
      types:
        Bloodloss: -2.5 #lowers bloodloss damage, was .5, buffed due to Limb Damage Changes
    ModifyBloodLevel: 30 #used to restore 5% blood per use, now restores about 10% blood per use on standard Buffed due to Limb Damage Changes
    healingBeginSound:
      path: "/Audio/Items/Medical/brutepack_begin.ogg"
    healingEndSound:
      path: "/Audio/Items/Medical/brutepack_end.ogg"
  - type: Stack
    stackType: Bloodpack
    count: 15 #Was 10, Buffed due to limb damage changes
  - type: StackPrice
    price: 20 #Was 10, Buffed due to limb damage changes

- type: entity
  parent: Bloodpack
  id: Bloodpack10Lingering
  suffix: 10, Lingering
  components:
  - type: Stack
    lingering: true

- type: entity
  parent: BaseHealingItem
  id: Tourniquet
  name: "джгут"
  description: "Зупиняє кровотечу! Сподіваюся."
  components:
    - type: Tag
      tags:
        - Tourniquet #Goobstation - ParamedHypo
        - SecBeltEquip
    - type: Sprite
      state: tourniquet
    - type: Healing
      damageContainers:
        - Biological
      damage:
        groups:
          Brute: 5 # Tourniquets HURT!
        types:
          Asphyxiation: 5 # Essentially Stopping all blood reaching a part of your body
      bloodlossModifier: -10 # Tourniquets stop bleeding
      delay: 0.5
      healingBeginSound:
        path: "/Audio/Items/Medical/brutepack_begin.ogg"
      healingEndSound:
        path: "/Audio/Items/Medical/brutepack_end.ogg"

- type: entity
  name: "рулон марлі"
  description: "Стерильну марлю, щоб обмотати закривавлені обрубки."
  parent: BaseHealingItem
  id: Gauze
  suffix: Full
  components:
  - type: Tag
    tags:
    - Gauze
  - type: Sprite
    state: gauze
  - type: Construction
    graph: Gauze
    node: gauze
  - type: Healing
    damageContainers:
      - Biological
    damage:
      types:
        Slash: -10 # Was 5
        Piercing: -15 # Was 10, Buffed due to limb damage changes
    bloodlossModifier: -10
    healingBeginSound:
      path: "/Audio/Items/Medical/brutepack_begin.ogg"
    healingEndSound:
      path: "/Audio/Items/Medical/brutepack_end.ogg"
  - type: Stack
    stackType: Gauze
    count: 15 #Was 10, Buffed due to limb damage changes
  - type: StackPrice
    price: 20 #Was 10, Buffed due to limb damage changes

- type: entity
  id: Gauze1
  parent: Gauze
  suffix: Single
  components:
  - type: Stack
    count: 1

- type: entity
  id: Gauze10Lingering
  parent: Gauze
  suffix: 10, Lingering
  components:
  - type: Stack
    lingering: true
    count: 1

- type: entity
  name: "крем з алое"
  description: "Місцевий крем від опіків."
  parent: Ointment ##Yes it's a direct reskin
  id: AloeCream
  components:
  - type: Sprite
    sprite: Objects/Specific/Hydroponics/aloe.rsi
    state: cream
  - type: Stack
    stackType: AloeCream
    count: 15 #Was 10, Buffed due to limb damage changes

- type: entity
  parent: BaseHealingItem
  id: HealingToolbox
  name: "інструментарій зцілення"
  description: "Потужний набір інструментів, просякнутий сильною енергією. Він може зцілити ваші рани і наповнити вас вбивчим наміром."
  suffix: DO NOT MAP
  components:
  - type: Sprite
    sprite: Objects/Specific/Medical/healing_toolbox.rsi
    state: icon
  - type: Healing
    damageContainers:
    - Biological
    damage:
      groups: # these are all split across multiple types
        Brute: -150
        Burn: -150
        Toxin: -150
    bloodlossModifier: -20
    delay: 1
    selfHealPenaltyMultiplier: 0
    healingBeginSound:
      path: "/Audio/Items/Medical/ointment_begin.ogg"
    healingEndSound:
      path: "/Audio/Items/Medical/ointment_end.ogg"

# Pills
- type: entity
  suffix: Dexalin 10u
  parent: Pill
  id: PillDexalin
  components:
  - type: Pill
    pillType: 15
  - type: Sprite
    state: pill16
  - type: Label
    currentLabel: dexalin 10u
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Dexalin
          Quantity: 10


- type: entity
  parent: PillCanister
  id: PillCanisterDexalin
  suffix: Dexalin, 7
  components:
  - type: Label
    currentLabel: dexalin 10u
  - type: StorageFill
    contents:
    - id: PillDexalin
      amount: 7

- type: entity
  suffix: Dylovene 10u
  parent: Pill
  id: PillDylovene
  components:
  - type: Pill
    pillType: 9
  - type: Sprite
    state: pill10
  - type: Label
    currentLabel: dylovene 10u
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Dylovene
          Quantity: 10

- type: entity
  parent: PillCanister
  id: PillCanisterDylovene
  suffix: Dylovene, 5
  components:
  - type: Label
    currentLabel: dylovene 10u
  - type: StorageFill
    contents:
    - id: PillDylovene
      amount: 5

- type: entity
  suffix: Hyronalin 10u
  parent: Pill
  id: PillHyronalin
  components:
  - type: Pill
    pillType: 16
  - type: Sprite
    state: pill17
  - type: Label
    currentLabel: hyronalin 10u
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Hyronalin
          Quantity: 10

- type: entity
  parent: PillCanister
  id: PillCanisterHyronalin
  suffix: Hyronalin, 5
  components:
  - type: Label
    currentLabel: hyronalin 10u
  - type: StorageFill
    contents:
    - id: PillHyronalin
      amount: 5

- type: entity
  suffix: Potassium iodide 10u
  parent: Pill
  id: PillPotassiumIodide
  components:
  - type: Pill
    pillType: 8
  - type: Sprite
    state: pill9
  - type: Label
    currentLabel: potassium iodide 10u
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: PotassiumIodide
          Quantity: 10

- type: entity
  parent: PillCanister
  id: PillCanisterPotassiumIodide
  suffix: Potassium iodide, 5
  components:
  - type: Label
    currentLabel: potassium iodide 10u
  - type: StorageFill
    contents:
    - id: PillPotassiumIodide
      amount: 5

- type: entity
  suffix: Iron 10u
  parent: Pill
  id: PillIron
  components:
  - type: Pill
    pillType: 13
  - type: Sprite
    state: pill14
  - type: Label
    currentLabel: iron 10u
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Iron
          Quantity: 10

- type: entity
  suffix: Copper 10u
  parent: Pill
  id: PillCopper
  components:
  - type: Pill
    pillType: 12
  - type: Sprite
    state: pill13
  - type: Label
    currentLabel: copper 10u
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Copper
          Quantity: 10

- type: entity
  parent: PillCanister
  id: PillCanisterIron
  suffix: Iron, 5
  components:
  - type: Label
    currentLabel: iron 10u
  - type: StorageFill
    contents:
    - id: PillIron
      amount: 5

- type: entity
  parent: PillCanister
  id: PillCanisterCopper
  suffix: Copper, 5
  components:
  - type: Label
    currentLabel: copper 10u
  - type: StorageFill
    contents:
    - id: PillCopper
      amount: 5

- type: entity
  suffix: Kelotane 10u
  parent: Pill
  id: PillKelotane
  components:
  - type: Pill
    pillType: 3
  - type: Sprite
    state: pill4
  - type: Label
    currentLabel: kelotane 10u
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Kelotane
          Quantity: 10

- type: entity
  parent: PillCanister
  id: PillCanisterKelotane
  suffix: Kelotane, 5
  components:
  - type: Label
    currentLabel: kelotane 10u
  - type: StorageFill
    contents:
    - id: PillKelotane
      amount: 5

- type: entity
  suffix: Dermaline 10u
  parent: Pill
  id: PillDermaline
  components:
  - type: Pill
    pillType: 7
  - type: Sprite
    state: pill8
  - type: Label
    currentLabel: dermaline 10u
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Dermaline
          Quantity: 10

- type: entity
  parent: PillCanister
  id: PillCanisterDermaline
  suffix: Dermaline, 5
  components:
  - type: Label
    currentLabel: dermaline 10u
  - type: StorageFill
    contents:
    - id: PillDermaline
      amount: 5

- type: entity
  suffix: Space drugs 15u
  parent: Pill
  id: PillSpaceDrugs
  components:
  - type: Label
    currentLabel: space drugs 15u
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: SpaceDrugs
          Quantity: 15
  - type: GuideHelp
    guides: [ Psionics ]

- type: entity
  parent: PillCanister
  id: PillCanisterSpaceDrugs
  suffix: Space Drugs, 5
  components:
  - type: Label
    currentLabel: LSD 15u
  - type: StorageFill
    contents:
    - id: PillSpaceDrugs
      amount: 5
  - type: GuideHelp
    guides: [ Psionics ]

- type: entity
  suffix: Pax 10u
  parent: Pill
  id: PillPax
  components:
  - type: Label
    currentLabel: pax 10u
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Pax
          Quantity: 10

- type: entity
  parent: PillCanister
  id: PillCanisterPax
  suffix: Pax, 5
  components:
  - type: Label
    currentLabel: Pax 10u
  - type: StorageFill
    contents:
    - id: PillPax
      amount: 5


- type: entity
  suffix: Tricordrazine 10u
  parent: Pill
  id: PillTricordrazine
  components:
  - type: Pill
    pillType: 2
  - type: Sprite
    state: pill3
  - type: Label
    currentLabel: tricordrazine 10u
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Tricordrazine
          Quantity: 10

- type: entity
  parent: PillCanister
  id: PillCanisterTricordrazine
  suffix: Tricordrazine, 5
  components:
  - type: Label
    currentLabel: tricordrazine 10u
  - type: StorageFill
    contents:
    - id: PillTricordrazine
      amount: 5

- type: entity
  suffix: Bicaridine 10u
  parent: Pill
  id: PillBicaridine
  components:
  - type: Pill
    pillType: 4
  - type: Sprite
    state: pill5
  - type: Label
    currentLabel: bicaridine 10u
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Bicaridine
          Quantity: 10

- type: entity
  parent: PillCanister
  id: PillCanisterBicaridine
  suffix: Bicaridine, 5
  components:
  - type: Label
    currentLabel: bicaridine 10u
  - type: StorageFill
    contents:
    - id: PillBicaridine
      amount: 5

- type: entity
  suffix: Charcoal 10u
  parent: Pill
  id: PillCharcoal
  components:
  - type: Pill
    pillType: 20
  - type: Sprite
    state: pill21
  - type: Label
    currentLabel: charcoal 10u
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Charcoal
          Quantity: 10

- type: entity
  parent: PillCanister
  id: PillCanisterCharcoal
  suffix: Charcoal, 3
  components:
  - type: Label
    currentLabel: charcoal 10u
  - type: StorageFill
    contents:
    - id: PillCharcoal
      amount: 3

- type: entity
  suffix: Romerol 10u
  parent: Pill
  id: PillRomerol
  components:
  - type: Label
    currentLabel: romerol 10u
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 20
        reagents:
        - ReagentId: Romerol
          Quantity: 10

- type: entity
  suffix: Ambuzol 10u
  parent: Pill
  id: PillAmbuzol
  components:
    - type: Label
      currentLabel: ambuzol 10u
    - type: SolutionContainerManager
      solutions:
        food:
          maxVol: 20
          reagents:
            - ReagentId: Ambuzol
              Quantity: 10

- type: entity
  suffix: Ambuzol plus 5u
  parent: Pill
  id: PillAmbuzolPlus
  components:
    - type: Pill
      pillType: 2
    - type: Sprite
      state: pill3
    - type: Label
      currentLabel: ambuzol plus 5u
    - type: SolutionContainerManager
      solutions:
        food:
          maxVol: 20
          reagents:
            - ReagentId: AmbuzolPlus
              Quantity: 5

- type: entity
  parent: PillCanister
  id: PillCanisterRandom
  suffix: Random
  components:
  - type: StorageFill
    contents:
    - id: PillDexalin
      prob: 0.10
      maxAmount: 7
      orGroup: RandomPill
    - id: PillDylovene
      prob: 0.10
      maxAmount: 7
      orGroup: RandomPill
    - id: PillHyronalin
      prob: 0.10
      maxAmount: 7
      orGroup: RandomPill
    - id: PillPotassiumIodide
      prob: 0.10
      maxAmount: 7
      orGroup: RandomPill
    - id: PillIron
      prob: 0.10
      maxAmount: 7
      orGroup: RandomPill
    - id: PillCopper
      prob: 0.10
      maxAmount: 7
      orGroup: RandomPill
    - id: PillKelotane
      prob: 0.10
      maxAmount: 7
      orGroup: RandomPill
    - id: PillDermaline
      prob: 0.10
      maxAmount: 7
      orGroup: RandomPill
    - id: PillTricordrazine
      prob: 0.10
      maxAmount: 7
      orGroup: RandomPill
    - id: PillBicaridine
      prob: 0.10
      maxAmount: 7
      orGroup: RandomPill
    - id: PillCharcoal
      prob: 0.10
      maxAmount: 7
      orGroup: RandomPill
    - id: PillAmbuzol
      prob: 0.075
      maxAmount: 7
      orGroup: RandomPill
    - id: PillAmbuzolPlus
      prob: 0.075
      maxAmount: 7
      orGroup: RandomPill
    - id: PillSpaceDrugs
      prob: 0.075
      maxAmount: 7
      orGroup: RandomPill
    - id: StrangePill
      prob: 0.075
      maxAmount: 7
      orGroup: RandomPill

# Syringes
- type: entity
  name: "шприц з ефедрином"
  parent: PrefilledSyringe
  id: SyringeEphedrine
  components:
  - type: SolutionContainerManager
    solutions:
      injector:
        maxVol: 15
        reagents:
        - ReagentId: Ephedrine
          Quantity: 15

- type: entity
  name: "шприц з інапроваліном"
  parent: PrefilledSyringe
  id: SyringeInaprovaline
  components:
  - type: SolutionContainerManager
    solutions:
      injector:
        maxVol: 15
        reagents:
        - ReagentId: Inaprovaline
          Quantity: 15

- type: entity
  name: "шприц з транексамовою кислотою"
  parent: PrefilledSyringe
  id: SyringeTranexamicAcid
  components:
  - type: SolutionContainerManager
    solutions:
      injector:
        maxVol: 15
        reagents:
        - ReagentId: TranexamicAcid
          Quantity: 15

- type: entity
  name: "шприц з бікаридином"
  parent: PrefilledSyringe
  id: SyringeBicaridine
  components:
  - type: SolutionContainerManager
    solutions:
      injector:
        maxVol: 15
        reagents:
        - ReagentId: Bicaridine
          Quantity: 15

- type: entity
  name: "шприц для дерматину"
  parent: PrefilledSyringe
  id: SyringeDermaline
  components:
  - type: SolutionContainerManager
    solutions:
      injector:
        maxVol: 15
        reagents:
        - ReagentId: Dermaline
          Quantity: 15

- type: entity
  name: "гіроналіновий шприц"
  parent: PrefilledSyringe
  id: SyringeHyronalin
  components:
  - type: SolutionContainerManager
    solutions:
      injector:
        maxVol: 15
        reagents:
        - ReagentId: Hyronalin
          Quantity: 15

- type: entity
  name: "шприц для іпекаку"
  parent: PrefilledSyringe
  id: SyringeIpecac
  components:
  - type: SolutionContainerManager
    solutions:
      injector:
        maxVol: 15
        reagents:
        - ReagentId: Ipecac
          Quantity: 15

- type: entity
  name: "шприц з амбузолом"
  parent: PrefilledSyringe
  id: SyringeAmbuzol
  components:
    - type: SolutionContainerManager
      solutions:
        injector:
          maxVol: 15
          reagents:
            - ReagentId: Ambuzol
              Quantity: 15

- type: entity
  name: "шприц із сигінатом"
  parent: PrefilledSyringe
  id: SyringeSigynate
  components:
    - type: SolutionContainerManager
      solutions:
        injector:
          maxVol: 15
          reagents:
            - ReagentId: Sigynate
              Quantity: 15

- type: entity
  name: "шприц з етилредоксазином"
  parent: PrefilledSyringe
  id: SyringeEthylredoxrazine
  components:
    - type: SolutionContainerManager
      solutions:
        injector:
          maxVol: 15
          reagents:
            - ReagentId: Ethylredoxrazine
              Quantity: 15

- type: entity
  name: "шприц з фалангініном"
  parent: PrefilledSyringe
  id: SyringePhalanximine
  components:
    - type: SolutionContainerManager
      solutions:
        injector:
          maxVol: 15
          reagents:
            - ReagentId: Phalanximine
              Quantity: 15

- type: entity
  name: "шприц з фізіологічним розчином"
  parent: PrefilledSyringe
  id: SyringeSaline
  components:
    - type: SolutionContainerManager
      solutions:
        injector:
          maxVol: 15
          reagents:
            - ReagentId: Saline
              Quantity: 15

#this is where all the syringes are so i didn't know where to put it
- type: entity
  name: "шприц з ромеролом"
  parent: PrefilledSyringe
  id: SyringeRomerol
  components:
  - type: SolutionContainerManager
    solutions:
      injector:
        maxVol: 15
        reagents:
        - ReagentId: Romerol
          Quantity: 15

- type: entity
  name: "шприц зі стимуляторами"
  parent: PrefilledSyringe
  id: SyringeStimulants
  components:
  - type: SolutionContainerManager
    solutions:
      injector:
        maxVol: 15
        reagents:
        - ReagentId: Stimulants
          Quantity: 15
