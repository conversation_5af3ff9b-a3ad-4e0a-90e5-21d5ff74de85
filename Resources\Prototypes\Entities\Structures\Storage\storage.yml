- type: entity
  parent: BaseStructure
  id: Rack
  name: "стійка"
  description: "Стійка для зберігання речей."
  components:
  - type: MeleeSound
    soundGroups:
      Brute:
        path:
          "/Audio/Weapons/dodgeball.ogg"
  - type: Construction
    graph: Rack
    node: Rack
  - type: Anchorable
  - type: InteractionOutline
  - type: PlaceableSurface
  - type: Sprite
    sprite: Structures/Furniture/furniture.rsi
    state: rack
    snapCardinals: true
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.3,-0.3,0.3,0.3"
        density: 140
        mask:
        - TableMask
        layer:
        - TableLayer
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 60
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 30
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
          params:
            volume: -6
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Climbable
  - type: StaticPrice
    price: 22
