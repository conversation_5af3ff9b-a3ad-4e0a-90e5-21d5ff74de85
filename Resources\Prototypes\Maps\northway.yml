- type: gameMap
  id: Northway
  mapName: 'Північ'
  mapPath: /Maps/northway.yml
  minPlayers: 0
  maxPlayers: 13
  stations:
    Northway:
      stationProto: StandardNanotrasenStationNoShuttles
      components:
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/emergency.yml
        - type: StationNameSetup
          mapNameTemplate: '{0} Northway Spaceship {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: '14'
        - type: StationJobs
          overflowJobs:
            - Passenger
          availableJobs:
          #service
            Captain: [ 1, 1 ]
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 1, 1 ]
            Botanist: [ 1, 1 ]
            Chef: [ 1 , 1 ]
            Clown: [ 1, 1 ]
            Musician: [ 1, 1 ]
            Janitor: [ 1, 1 ]
            Mime: [ 1, 1 ]
            Reporter: [ 1, 1 ]
            ServiceWorker: [ 1, 1 ]
          #engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 1, 2]
            StationEngineer: [ 2, 2 ]
            TechnicalAssistant: [ 2, 2 ]
          #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            Chemist: [ 1, 1 ]
            MedicalDoctor: [ 2, 2 ]
            MedicalIntern: [ 2, 2 ]
            Paramedic: [ 1, 1 ]
          #science
            ResearchDirector: [ 1, 1 ]
            Chaplain: [ 1, 1 ]
            Scientist: [ 2, 2 ]
            ResearchAssistant: [ 2, 2 ]
          #security
            HeadOfSecurity: [ 1, 1 ]
            Warden: [ 1, 1 ]
            Detective: [ 1, 1 ]
            SecurityOfficer: [ 2, 2 ]
            SecurityCadet: [ 1, 2 ]
            Prisoner: [ 1, 2 ]
          #supply
            Quartermaster: [ 1, 1 ]
            MailCarrier: [ 1, 1 ]
            SalvageSpecialist: [ 2, 3 ]
            CargoTechnician: [ 2, 2 ]
          #civilian
            Passenger: [ 1, 5 ]
          # Silicon
            StationAi: [ 1, 1 ]
        # blob-config-start SMALL+
        - type: StationBlobConfig
          stageBegin: 25
          stageCritical: 350
          stageTheEnd: 700
        # blob-config-end
