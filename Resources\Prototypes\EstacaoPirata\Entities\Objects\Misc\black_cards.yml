- type: entity
  parent: [ BoxCardboard, BaseBagOpenClose ]
  id: CardBoxBase
  name: "палубний ящик"
  categories: [ HideSpawnMenu ]
  components:
  - type: Item
    size: Small
    shape:
    - 0,0,1,1
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    layers:
    - state: black_box
    - state: black_box_open
      map: [ "openLayer" ]
      visible: false
  - type: Storage
    maxItemSize: Normal
    grid:
    - 0,0,1,1
    whitelist:
      components:
      - CardDeck
  - type: OpenTriggeredStorageFill
    contents:
    - id: CardDeckBase
      amount: 1
  - type: Appearance

# Frontier: base stack for card stack component
- type: entity
  parent: [BaseItem]
  id: CardStackBase
  name: "стос карт"
  abstract: true
  components:
  - type: Item
    size: Small
  - type: CardStack
  - type: StripMenuHidden
  - type: ContainerContainer # Frontier
    containers: # Frontier
      cardstack-container: !type:Container # Frontier
# End Frontier

- type: entity
  parent: CardStackBase
  id: CardHandBase
  categories: [ HideSpawnMenu ]
  name: "картярська рука"
  components:
  - type: CardHand
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: singlecard_down_black
  - type: UserInterface
    interfaces:
      enum.CardUiKey.Key:
        type: CardHandMenuBoundUserInterface
  # - type: ActivatableUI # Frontier
  #   key: enum.CardUiKey.Key # Frontier

- type: entity
  parent: CardStackBase
  id: CardDeckBase
  categories: [ HideSpawnMenu ]
  name: "колода карт"
  components:
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: deck_black_full
  - type: Item
    size: Normal
  - type: CardDeck


- type: entity
  parent: CardBoxBase
  id: CardBoxBlack
  name: "чорна колода"
  components:
  - type: Item
    size: Small
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    layers:
    - state: black_box
    - state: black_box_open
      map: [ "openLayer" ]
      visible: false
  - type: OpenTriggeredStorageFill
    contents:
    - id: CardDeckBlack
      amount: 1

- type: entity
  parent: CardDeckBase
  id: CardDeckBlack
  name: "колода карт"
  components:
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: deck_black_full
  - type: CardStack
    initialContent:
    # Clubs
    - CardScAceOfClubsBlack
    - CardSc2OfClubsBlack
    - CardSc3OfClubsBlack
    - CardSc4OfClubsBlack
    - CardSc5OfClubsBlack
    - CardSc6OfClubsBlack
    - CardSc7OfClubsBlack
    - CardSc8OfClubsBlack
    - CardSc9OfClubsBlack
    - CardSc10OfClubsBlack
    - CardScJackOfClubsBlack
    - CardScQueenOfClubsBlack
    - CardScKingOfClubsBlack
    # Diamonds
    - CardScAceOfDiamondsBlack
    - CardSc2OfDiamondsBlack
    - CardSc3OfDiamondsBlack
    - CardSc4OfDiamondsBlack
    - CardSc5OfDiamondsBlack
    - CardSc6OfDiamondsBlack
    - CardSc7OfDiamondsBlack
    - CardSc8OfDiamondsBlack
    - CardSc9OfDiamondsBlack
    - CardSc10OfDiamondsBlack
    - CardScJackOfDiamondsBlack
    - CardScQueenOfDiamondsBlack
    - CardScKingOfDiamondsBlack
    # Hearts
    - CardScAceOfHeartsBlack
    - CardSc2OfHeartsBlack
    - CardSc3OfHeartsBlack
    - CardSc4OfHeartsBlack
    - CardSc5OfHeartsBlack
    - CardSc6OfHeartsBlack
    - CardSc7OfHeartsBlack
    - CardSc8OfHeartsBlack
    - CardSc9OfHeartsBlack
    - CardSc10OfHeartsBlack
    - CardScJackOfHeartsBlack
    - CardScQueenOfHeartsBlack
    - CardScKingOfHeartsBlack
    # Spades
    - CardScAceOfSpadesBlack
    - CardSc2OfSpadesBlack
    - CardSc3OfSpadesBlack
    - CardSc4OfSpadesBlack
    - CardSc5OfSpadesBlack
    - CardSc6OfSpadesBlack
    - CardSc7OfSpadesBlack
    - CardSc8OfSpadesBlack
    - CardSc9OfSpadesBlack
    - CardSc10OfSpadesBlack
    - CardScJackOfSpadesBlack
    - CardScQueenOfSpadesBlack
    - CardScKingOfSpadesBlack
    # Joker
    - CardScJokerBlack

- type: entity
  parent: BaseItem
  id: CardBase
  name: "картка"
  categories: [ HideSpawnMenu ]
  components:
  - type: EmitSoundOnLand
    sound:
      collection: cardShove
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: singlecard_down_black
  - type: Rotatable
  - type: Item
    size: Small
  - type: UseDelay
    delay: 0.5
  - type: Card
    backSprite:
    - sprite: EstacaoPirata/Objects/Misc/cards.rsi
      state: singlecard_down_black
    flipped: true
  - type: StripMenuHidden

# region Black Cards

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc2OfClubsBlack
  components:
  - type: Card
    name: card-sc-2-clubs
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_2_of_Clubs_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc3OfClubsBlack
  components:
  - type: Card
    name: card-sc-3-clubs
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_3_of_Clubs_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc4OfClubsBlack
  components:
  - type: Card
    name: card-sc-4-clubs
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_4_of_Clubs_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc5OfClubsBlack
  components:
  - type: Card
    name: card-sc-5-clubs
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_5_of_Clubs_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc6OfClubsBlack
  components:
  - type: Card
    name: card-sc-6-clubs
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_6_of_Clubs_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc7OfClubsBlack
  components:
  - type: Card
    name: card-sc-7-clubs
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_7_of_Clubs_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc8OfClubsBlack
  components:
  - type: Card
    name: card-sc-8-clubs
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_8_of_Clubs_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc9OfClubsBlack
  components:
  - type: Card
    name: card-sc-9-clubs
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_9_of_Clubs_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc10OfClubsBlack
  components:
  - type: Card
    name: card-sc-10-clubs
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_10_of_Clubs_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardScAceOfClubsBlack
  components:
  - type: Card
    name: card-sc-ace-clubs
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_Ace_of_Clubs_black


- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardScJackOfClubsBlack
  components:
  - type: Card
    name: card-sc-jack-clubs
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_Jack_of_Clubs_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardScKingOfClubsBlack
  components:
  - type: Card
    name: card-sc-king-clubs
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_King_of_Clubs_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardScQueenOfClubsBlack
  components:
  - type: Card
    name: card-sc-queen-clubs
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_Queen_of_Clubs_black


- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardScJackOfDiamondsBlack
  components:
  - type: Card
    name: card-sc-jack-diamonds
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_Jack_of_Diamonds_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardScQueenOfDiamondsBlack
  components:
  - type: Card
    name: card-sc-queen-diamonds
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_Queen_of_Diamonds_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardScKingOfDiamondsBlack
  components:
  - type: Card
    name: card-sc-king-diamonds
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_King_of_Diamonds_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardScAceOfDiamondsBlack
  components:
  - type: Card
    name: card-sc-ace-diamonds
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_Ace_of_Diamonds_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc2OfDiamondsBlack
  components:
  - type: Card
    name: card-sc-2-diamonds
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_2_of_Diamonds_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc3OfDiamondsBlack
  components:
  - type: Card
    name: card-sc-3-diamonds
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_3_of_Diamonds_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc4OfDiamondsBlack
  components:
  - type: Card
    name: card-sc-4-diamonds
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_4_of_Diamonds_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc5OfDiamondsBlack
  components:
  - type: Card
    name: card-sc-5-diamonds
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_5_of_Diamonds_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc6OfDiamondsBlack
  components:
  - type: Card
    name: card-sc-6-diamonds
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_6_of_Diamonds_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc7OfDiamondsBlack
  components:
  - type: Card
    name: card-sc-7-diamonds
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_7_of_Diamonds_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc8OfDiamondsBlack
  components:
  - type: Card
    name: card-sc-8-diamonds
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_8_of_Diamonds_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc9OfDiamondsBlack
  components:
  - type: Card
    name: card-sc-9-diamonds
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_9_of_Diamonds_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc10OfDiamondsBlack
  components:
  - type: Card
    name: card-sc-10-diamonds
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_10_of_Diamonds_black


- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc2OfHeartsBlack
  components:
  - type: Card
    name: card-sc-2-hearts
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_2_of_Hearts_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc3OfHeartsBlack
  components:
  - type: Card
    name: card-sc-3-hearts
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_3_of_Hearts_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc4OfHeartsBlack
  components:
  - type: Card
    name: card-sc-4-hearts
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_4_of_Hearts_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc5OfHeartsBlack
  components:
  - type: Card
    name: card-sc-5-hearts
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_5_of_Hearts_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc6OfHeartsBlack
  components:
  - type: Card
    name: card-sc-6-hearts
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_6_of_Hearts_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc7OfHeartsBlack
  components:
  - type: Card
    name: card-sc-7-hearts
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_7_of_Hearts_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc8OfHeartsBlack
  components:
  - type: Card
    name: card-sc-8-hearts
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_8_of_Hearts_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc9OfHeartsBlack
  components:
  - type: Card
    name: card-sc-9-hearts
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_9_of_Hearts_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc10OfHeartsBlack
  components:
  - type: Card
    name: card-sc-10-hearts
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_10_of_Hearts_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardScKingOfHeartsBlack
  components:
  - type: Card
    name: card-sc-king-hearts
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_King_of_Hearts_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardScQueenOfHeartsBlack
  components:
  - type: Card
    name: card-sc-queen-hearts
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_Queen_of_Hearts_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardScJackOfHeartsBlack
  components:
  - type: Card
    name: card-sc-jack-hearts
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_Jack_of_Hearts_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardScAceOfHeartsBlack
  components:
  - type: Card
    name: card-sc-ace-hearts
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_Ace_of_Hearts_black


- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc2OfSpadesBlack
  components:
  - type: Card
    name: card-sc-2-spades
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_2_of_Spades_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc3OfSpadesBlack
  components:
  - type: Card
    name: card-sc-3-spades
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_3_of_Spades_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc4OfSpadesBlack
  components:
  - type: Card
    name: card-sc-4-spades
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_4_of_Spades_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc5OfSpadesBlack
  components:
  - type: Card
    name: card-sc-5-spades
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_5_of_Spades_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc6OfSpadesBlack
  components:
  - type: Card
    name: card-sc-6-spades
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_6_of_Spades_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc7OfSpadesBlack
  components:
  - type: Card
    name: card-sc-7-spades
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_7_of_Spades_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc8OfSpadesBlack
  components:
  - type: Card
    name: card-sc-8-spades
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_8_of_Spades_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc9OfSpadesBlack
  components:
  - type: Card
    name: card-sc-9-spades
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_9_of_Spades_black


- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardSc10OfSpadesBlack
  components:
  - type: Card
    name: card-sc-10-spades
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_10_of_Spades_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardScKingOfSpadesBlack
  components:
  - type: Card
    name: card-sc-king-spades
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_King_of_Spades_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardScQueenOfSpadesBlack
  components:
  - type: Card
    name: card-sc-queen-spades
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_Queen_of_Spades_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardScJackOfSpadesBlack
  components:
  - type: Card
    name: card-sc-jack-spades
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_Jack_of_Spades_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardScAceOfSpadesBlack
  components:
  - type: Card
    name: card-sc-ace-spades
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: sc_Ace_of_Spades_black

- type: entity
  parent: CardBase
  categories: [ HideSpawnMenu ]
  id: CardScJokerBlack
  components:
  - type: Card
    name: card-sc-joker
  - type: Sprite
    sprite: EstacaoPirata/Objects/Misc/cards.rsi
    state: black_joker

# endregion Black Cards
