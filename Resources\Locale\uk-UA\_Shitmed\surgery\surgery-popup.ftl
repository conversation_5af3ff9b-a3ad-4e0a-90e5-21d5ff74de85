surgery-popup-step-SurgeryStepOpenIncisionScalpel = {$user} робить розріз на {$part} у {$target}.
surgery-popup-step-SurgeryStepClampBleeders = {$user} затискає судини на {$part} у {$target}.
surgery-popup-step-SurgeryStepRetractSkin = {$user} відтягує шкіру на {$part} у {$target}.
surgery-popup-step-SurgeryStepSawBones = {$user} розпилює кістки на {$part} у {$target}.
surgery-popup-step-SurgeryStepPriseOpenBones = {$user} розкриває кістки на {$part} у {$target}.
surgery-popup-step-SurgeryStepCloseBones = {$user} закриває кістки на {$part} у {$target}.
surgery-popup-step-SurgeryStepMendRibcage = {$user} відновлює грудну клітку на {$part} у {$target}.
surgery-popup-step-SurgeryStepCloseIncision = {$user} закриває розріз на {$part} у {$target}.

surgery-popup-step-SurgeryStepInsertFeature = {$user} вставляє щось на {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachHead-step-SurgeryStepInsertFeature = {$user} приєднує голову до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachLeftArm-step-SurgeryStepInsertFeature = {$user} приєднує ліву руку до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachRightArm-step-SurgeryStepInsertFeature = {$user} приєднує праву руку до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachLeftLeg-step-SurgeryStepInsertFeature = {$user} приєднує ліву ногу до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachRightLeg-step-SurgeryStepInsertFeature = {$user} приєднує праву ногу до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachLeftHand-step-SurgeryStepInsertFeature = {$user} приєднує ліву долоню до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachRightHand-step-SurgeryStepInsertFeature = {$user} приєднує праву долоню до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachLeftFoot-step-SurgeryStepInsertFeature = {$user} приєднує ліву стопу до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachRightFoot-step-SurgeryStepInsertFeature = {$user} приєднує праву стопу до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachLegs-step-SurgeryStepInsertFeature = {$user} приєднує ноги до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachHands-step-SurgeryStepInsertFeature = {$user} приєднує руки до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachFeet-step-SurgeryStepInsertFeature = {$user} приєднує стопи до {$part} у {$target}!

surgery-popup-step-SurgeryStepSealWounds = {$user} зашиває рани на {$part} у {$target}.
surgery-popup-step-SurgeryStepSawFeature = {$user} розпилює кістки на {$part} у {$target}.
surgery-popup-step-SurgeryStepClampInternalBleeders = {$user} затискає внутрішні судини на {$part} у {$target}.
surgery-popup-step-SurgeryStepRemoveFeature = {$user} ампутує {$part} у {$target}!
surgery-popup-step-SurgeryStepCarefulIncisionScalpel = {$user} обережно робить розріз на {$part} у {$target}.
surgery-popup-step-SurgeryStepRepairBruteTissue = {$user} відновлює пошкоджені тканини на {$part} у {$target}!
surgery-popup-step-SurgeryStepRepairBurnTissue = {$user} відновлює обпалені тканини на {$part} у {$target}!
surgery-popup-step-SurgeryStepSealTendWound = {$user} зашиває рани на {$part} у {$target}.
surgery-popup-step-SurgeryStepInsertItem = {$user} вставляє щось у {$part} у {$target}!
surgery-popup-step-SurgeryStepRemoveItem = {$user} витягує щось із {$part} у {$target}!

surgery-popup-step-SurgeryStepRemoveOrgan = {$user} видаляє орган із {$part} у {$target}!
surgery-popup-step-SurgeryStepInsertOrgan = {$user} вставляє орган у {$part} у {$target}!

surgery-popup-procedure-SurgeryRemoveBrain-step-SurgeryStepRemoveOrgan = {$user} видаляє мозок із {$part} у {$target}!
surgery-popup-procedure-SurgeryRemoveHeart-step-SurgeryStepRemoveOrgan = {$user} видаляє серце із {$part} у {$target}!
surgery-popup-procedure-SurgeryRemoveLiver-step-SurgeryStepRemoveOrgan = {$user} видаляє печінку із {$part} у {$target}!
surgery-popup-procedure-SurgeryRemoveLungs-step-SurgeryStepRemoveOrgan = {$user} видаляє легені із {$part} у {$target}!
surgery-popup-procedure-SurgeryRemoveEyes-step-SurgeryStepRemoveOrgan = {$user} видаляє очі із {$part} у {$target}!
surgery-popup-procedure-SurgeryRemoveStomach-step-SurgeryStepRemoveOrgan = {$user} видаляє шлунок із {$part} у {$target}!

surgery-popup-procedure-SurgeryInsertBrain-step-SurgeryStepInsertOrgan = {$user} вставляє мозок у {$part} у {$target}!
surgery-popup-step-SurgeryStepInsertLungs = {$user} вставляє легені у {$part} у {$target}!
surgery-popup-step-SurgeryStepInsertLiver = {$user} вставляє печінку у {$part} у {$target}!
surgery-popup-step-SurgeryStepInsertEyes = {$user} вставляє очі у {$part} у {$target}!
surgery-popup-step-SurgeryStepInsertHeart = {$user} вставляє серце у {$part} у {$target}!
surgery-popup-step-SurgeryStepInsertStomach = {$user} вставляє шлунок у {$part} у {$target}!

surgery-popup-step-SurgeryStepSealOrganWound = {$user} зашиває рани на {$part} у {$target}.
surgery-popup-step-SurgeryStepLobotomize = {$user} проводить лоботомію у {$target}!
surgery-popup-step-SurgeryStepMendBrainTissue = {$user} відновлює тканини мозку на {$part} у {$target}.
surgery-popup-step-SurgeryStepTreatTumor = {$user} лікує пухлину на {$part} у {$target}.