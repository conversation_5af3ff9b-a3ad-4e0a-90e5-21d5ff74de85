# head markings

## patterns

marking-RodentiaHeadBlaze = Head - Blaze
marking-RodentiaHeadBlaze-head_m = Head
marking-RodentiaHeadBlaze-blaze = Pattern

marking-RodentiaHeadRound = Head - Face Color
marking-RodentiaHeadRound-head_m = Head
marking-RodentiaHeadRound-round = Pattern

## ears

marking-RodentiaHeadTopEarBat = Ears - Bat
marking-RodentiaHeadTopEarBat-bat = Outer ear

marking-RodentiaHeadTopEarBatLarge = Ears - Bat (Large)
marking-RodentiaHeadTopEarBatLarge-bat_large = Outer ear

marking-RodentiaHeadTopEarHamster = Ears - Hamster
marking-RodentiaHeadTopEarHamster-hamster = Outer ear
marking-RodentiaHeadTopEarHamster-hamster_overlay = Inner ear

marking-RodentiaHeadTopEarLong = Ears - Long
marking-RodentiaHeadTopEarLong-long = Outer ear
marking-RodentiaHeadTopEarLong-long_overlay = Inner ear

marking-RodentiaHeadTopEarMouse = Ears - Mouse
marking-RodentiaHeadTopEarMouse-mouse = Outer ear
marking-RodentiaHeadTopEarMouse-mouse_overlay = Inner ear

marking-RodentiaHeadTopEarMouseLarge = Ears - Mouse (Large)
marking-RodentiaHeadTopEarMouseLarge-mouse_large = Outer 
marking-RodentiaHeadTopEarMouseLarge-mouse_large_overlay = Inner ear

marking-RodentiaHeadTopEarNone = Ears - Hidden
marking-RodentiaHeadTopEarNone-none = None

marking-RodentiaHeadTopEarPointy = Ears - Pointy
marking-RodentiaHeadTopEarPointy-pointy = Outer ear

marking-RodentiaHeadTopEarRabbit = Ears - Rabbit
marking-RodentiaHeadTopEarRabbit-rabbit = Outer ear
marking-RodentiaHeadTopEarRabbit-rabbit_overlay = Inner ear

marking-RodentiaHeadTopEarSmall = Ears - Small
marking-RodentiaHeadTopEarSmall-small = Outer Ear

## snout

marking-RodentiaSnoutBat = Snout - Bat
marking-RodentiaSnoutBat-bat = Snout
marking-RodentiaSnoutBat-bat_nose = Nose

marking-RodentiaSnoutBatCounter = Snout - Bat, Two-tone
marking-RodentiaSnoutBatCounter-bat = Snout
marking-RodentiaSnoutBatCounter-bat_nose = Nose
marking-RodentiaSnoutBatCounter-bat_overlay = Countershade

marking-RodentiaSnoutFlat = Snout - Flat
marking-RodentiaSnoutFlat-flat = Snout
marking-RodentiaSnoutFlat-flat_nose = Nose

marking-RodentiaSnoutFlatCounter = Snout - Flat, Two-tone
marking-RodentiaSnoutFlatCounter-flat = Snout
marking-RodentiaSnoutFlatCounter-flat_nose = Nose
marking-RodentiaSnoutFlatCounter-flat_overlay = Countershade

marking-RodentiaSnoutRound = Snout - Round
marking-RodentiaSnoutRound-round = Snout
marking-RodentiaSnoutRound-round_nose = Nose

marking-RodentiaSnoutRoundCounter = Snout - Round, Two-tone
marking-RodentiaSnoutRoundCounter-round = Snout
marking-RodentiaSnoutRoundCounter-round_nose = Nose
marking-RodentiaSnoutRoundCounter-round_overlay = Countershade

## cheeks

marking-RodentiaCheeksRound = Cheeks - Round
marking-RodentiaCheeksRound-cheeks = Cheek

marking-RodentiaCheeksRoundCounter = Cheeks - Round, Two-tone
marking-RodentiaCheeksRoundCounter-cheeks = Cheek
marking-RodentiaCheeksRoundCounter-cheeks_overlay = Countershade

marking-RodentiaCheeksFluff = Cheeks - Fluff
marking-RodentiaCheeksFluff-fluff = Cheek fluff

marking-RodentiaCheeksFluffCounter = Cheeks - Fluff, Two-tone
marking-RodentiaCheeksFluffCounter-fluff = Cheek fluff
marking-RodentiaCheeksFluffCounter-fluff_overlay = Countershade

marking-RodentiaCheeksFluffAlt = Cheeks - Fluff, Alt
marking-RodentiaCheeksFluffAlt-fluff_alt = Cheek fluff

marking-RodentiaCheeksFluffAltCounter = Cheeks - Fluff, Alt, Two-tone
marking-RodentiaCheeksFluffAltCounter-fluff_alt = Cheek fluff
marking-RodentiaCheeksFluffAltCounter-fluff_alt_overlay = Countershade

marking-RodentiaCheeksWhiskers = Whiskers
marking-RodentiaCheeksWhiskers-whiskers = Whiskers

# body markings

## tail

marking-RodentiaTailBeaver = Tail - Beaver
marking-RodentiaTailBeaver-beaver = Tail

marking-RodentiaTailHamster = Tail - Hamster
marking-RodentiaTailHamster-hamster = Tail

marking-RodentiaTailLong = Tail - Long
marking-RodentiaTailLong-long = Tail

marking-RodentiaTailLongCounter = Tail - Long, Two-tone
marking-RodentiaTailLongCounter-long = Tail
marking-RodentiaTailLongCounter-long_overlay = Countershade

marking-RodentiaTailLongCounterTip = Tail - Long, Three-tone
marking-RodentiaTailLongCounterTip-long = Tail
marking-RodentiaTailLongCounterTip-long_overlay = Countershade
marking-RodentiaTailLongCounterTip-long_tip = Tip

marking-RodentiaTailMouse = Tail - Mouse
marking-RodentiaTailMouse-mouse = Tail

marking-RodentiaTailRabbit = Tail - Rabbit
marking-RodentiaTailRabbit-rabbit = Tail

marking-RodentiaTailRabbitCounter = Tail - Rabbit, Two-tone
marking-RodentiaTailRabbitCounter-rabbit = Tail
marking-RodentiaTailRabbitCounter-rabbit_overlay = Countershade

marking-RodentiaTailShort = Tail - Short
marking-RodentiaTailShort-short = Tail

marking-RodentiaTailSquirrel = Tail - Squirrel
marking-RodentiaTailSquirrel-squirrel = Tail

marking-RodentiaTailSquirrelBicolor = Tail - Squirrel, Two-tone
marking-RodentiaTailSquirrelBicolor-squirrel = Tail
marking-RodentiaTailSquirrelBicolor-squirrel_overlay = Secondary

## patterns

marking-RodentiaChestCountershade = Chest - Countershade
marking-RodentiaChestCountershade-countershade = Countershade

marking-RodentiaChestCountershadeF = Chest - Countershade
marking-RodentiaChestCountershadeF-countershade_f = Countershade

marking-RodentiaLegLeftCountershade = Left Leg - Countershade
marking-RodentiaLegLeftCountershade-l_leg = Leg
marking-RodentiaLegLeftCountershade-countershade_lleg = Countershade

marking-RodentiaLegRightCountershade = Right Leg - Countershade
marking-RodentiaLegRightCountershade-r_leg = Leg
marking-RodentiaLegRightCountershade-countershade_rleg = Countershade

marking-RodentiaChestFawn = Chest - Fawn
marking-RodentiaChestFawn-fawn = Pattern

marking-RodentiaChestHooded = Chest - Hooded
marking-RodentiaChestHooded-hooded = Pattern

marking-RodentiaChestHoodedF = Chest - Hooded
marking-RodentiaChestHoodedF-hooded_f = Pattern

# base parts

marking-RodentiaHeadBasic = Head - Basic
marking-RodentiaHeadBasic-head_m = Head

marking-RodentiaArmLeftBasic = Left Arm - Basic
marking-RodentiaArmLeftBasic-l_arm = Arm

marking-RodentiaArmRightBasic = Right Arm - Basic
marking-RodentiaArmRightBasic-r_arm = Arm

marking-RodentiaLegLeftBasic = Left Leg - Basic
marking-RodentiaLegLeftBasic-l_leg = Leg

marking-RodentiaLegRightBasic = Right Leg - Basic
marking-RodentiaLegRightBasic-r_leg = Leg

marking-RodentiaHandLeftBasic = Left Hand - Basic
marking-RodentiaHandLeftBasic-l_hand = Hand

marking-RodentiaHandRightBasic = Right Hand - Basic
marking-RodentiaHandRightBasic-r_hand = Hand

marking-RodentiaFootLeftBasic = Left Foot - Basic
marking-RodentiaFootLeftBasic-l_foot = Foot

marking-RodentiaFootRightBasic = Right Foot - Basic
marking-RodentiaFootRightBasic-r_foot = Foot
