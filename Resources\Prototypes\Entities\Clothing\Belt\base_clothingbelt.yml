- type: entity
  abstract: true
  parent: Clothing
  id: ClothingBeltBase
  components:
  - type: Sprite
    state: icon
  - type: Item
    size: Normal
  - type: Clothing
    slots: [belt]
    quickEquip: false
    equipSound:
      path: /Audio/SimpleStation14/Items/Handling/toolbelt_pickup.ogg
      params:
        volume: -10
  - type: PhysicalComposition
    materialComposition:
      Cloth: 50
  - type: StaticPrice
    price: 20
  - type: EmitSoundOnPickup
    sound:
      path: /Audio/SimpleStation14/Items/Handling/toolbelt_pickup.ogg
      params:
        volume: -10
  - type: EmitSoundOnDrop
    sound:
      path: /Audio/SimpleStation14/Items/Handling/toolbelt_pickup.ogg
      params:
        volume: -10
  - type: EmitSoundOnLand
    sound:
      path: /Audio/SimpleStation14/Items/Handling/toolbelt_pickup.ogg
      params:
        volume: -10

- type: entity
  abstract: true
  parent: ClothingBeltBase
  id: ClothingBeltStorageBase
  components:
  - type: Storage
    maxItemSize: Normal
    defaultStorageOrientation: Vertical
    grid:
    - 0,0,7,1
  - type: Item
    size: Large
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
        ents: []
  - type: UserInterface
    interfaces:
      enum.StorageUiKey.Key:
        type: StorageBoundUserInterface

- type: entity
  abstract: true
  parent: ClothingBeltBase
  id: ClothingBeltAmmoProviderBase
  components:
  - type: BallisticAmmoProvider
    mayTransfer: true
  - type: Item
    size: Large
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
