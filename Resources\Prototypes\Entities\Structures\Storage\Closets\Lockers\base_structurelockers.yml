- type: entity
  id: LockerBase
  parent: ClosetBase
  abstract: true
  components:
  - type: AccessReader
  - type: Lock
  - type: LockVisuals
  - type: Sprite
    sprite: Structures/Storage/closet.rsi
    noRot: true
    layers:
    - state: generic
      map: ["enum.StorageVisualLayers.Base"]
    - state: generic_door
      map: ["enum.StorageVisualLayers.Door"]
    - state: locked
      map: ["enum.LockVisualLayers.Lock"]
      shader: unshaded
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
          params:
            volume: -6
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetSteel1:
            min: 1
            max: 2
  - type: Construction
    graph: ClosetSteelSecure
    node: done
    containers:
    - entity_storage
  - type: DamageOtherOnHit
    damage:
      types:
        Blunt: 10
    staminaCost: 35

- type: entity
  id: LockerBaseSecure
  parent: LockerBase
  abstract: true
  components:
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: StructuralMetallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 75
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
