atmos-alerts-window-title = Atmospheric Alerts Computer
atmos-alerts-window-station-name = [color=white][font size=14]{$stationName}[/font][/color]
atmos-alerts-window-unknown-location = Unknown location

atmos-alerts-window-tab-no-alerts = Alerts
atmos-alerts-window-tab-alerts = Alerts ({$value})
atmos-alerts-window-tab-air-alarms = Air alarms
atmos-alerts-window-tab-fire-alarms = Fire alarms

atmos-alerts-window-alarm-label = {CAPITALIZE($name)} ({$address})
atmos-alerts-window-temperature-label = Temperature
atmos-alerts-window-temperature-value = {$valueInC} °C ({$valueInK} K)
atmos-alerts-window-invalid-value = N/A
atmos-alerts-window-total-mol-label = Total moles
atmos-alerts-window-total-mol-value = {$value} mol
atmos-alerts-window-pressure-label = Pressure
atmos-alerts-window-pressure-value = {$value} kPa
atmos-alerts-window-oxygenation-label = Oxygenation
atmos-alerts-window-oxygenation-value = {$value}% 
atmos-alerts-window-other-gases-label = Other present gases
atmos-alerts-window-other-gases-value = {$shorthand} ({$value}%) 
atmos-alerts-window-other-gases-value-nil = None
atmos-alerts-window-silence-alerts = Silence alerts from this alarm

atmos-alerts-window-label-alert-types = Alert levels:
atmos-alerts-window-normal-state = Normal
atmos-alerts-window-warning-state = Warning
atmos-alerts-window-danger-state = Danger!
atmos-alerts-window-invalid-state = Inactive

atmos-alerts-window-no-active-alerts = [font size=16][color=white]No active alerts -[/color] [color={$color}]Situation normal[/color][/font]
atmos-alerts-window-no-data-available = No data available
atmos-alerts-window-alerts-being-silenced = Silencing alerts...

atmos-alerts-window-toggle-overlays = Toggle alarm display

atmos-alerts-window-flavor-left = Contact an atmospheric technician for assistance
atmos-alerts-window-flavor-right = v1.8