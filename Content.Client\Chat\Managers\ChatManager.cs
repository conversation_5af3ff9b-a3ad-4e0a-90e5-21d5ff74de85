using Content.Client.Administration.Managers;
using Content.Client.Ghost;
using Content.Shared.Administration;
using Content.Shared.Chat;
using Robust.Client.Console;
using Robust.Shared.Network;
using Robust.Shared.Utility;

namespace Content.Client.Chat.Managers
{
    internal sealed class ChatManager : IChatManager
    {
        [Dependency] private readonly IClientConsoleHost _consoleHost = default!;
        [Dependency] private readonly IClientAdminManager _adminMgr = default!;
        [Dependency] private readonly IEntitySystemManager _systems = default!;

        private ISawmill _sawmill = default!;
        public event Action? PermissionsUpdated; //Nyano - Summary: need to be able to update perms for new psionics.
        public void Initialize()
        {
            _sawmill = Logger.GetSawmill("chat");
            _sawmill.Level = LogLevel.Info;
        }

    public void SendAdminAlert(string message)
    {
        // See server-side manager. This just exists for shared code.
    }

    public void SendAdminAlert(EntityUid player, string message)
    {
        // See server-side manager. This just exists for shared code.
    }

    public void ChatMessageToAll(
        ChatChannel channel,
        string message,
        string wrappedMessage,
        EntityUid source,
        bool hideChat,
        bool recordReplay,
        Color? colorOverride = null,
        string? audioPath = null,
        float audioVolume = 0,
        NetUserId? author = null,
        bool ignoreChatStack = false
    )
    {
        // See server-side code. This method only exists for shared.
    }

    public void SendMessage(string text, ChatSelectChannel channel)
        {
            var str = text.ToString();
            switch (channel)
            {
                case ChatSelectChannel.Console:
                    // run locally
                    _consoleHost.ExecuteCommand(text);
                    break;

                case ChatSelectChannel.LOOC:
                    _consoleHost.ExecuteCommand($"looc \"{CommandParsing.Escape(str)}\"");
                    break;

                case ChatSelectChannel.OOC:
                    _consoleHost.ExecuteCommand($"ooc \"{CommandParsing.Escape(str)}\"");
                    break;

                case ChatSelectChannel.Admin:
                    _consoleHost.ExecuteCommand($"asay \"{CommandParsing.Escape(str)}\"");
                    break;

                case ChatSelectChannel.Emotes:
                    _consoleHost.ExecuteCommand($"me \"{CommandParsing.Escape(str)}\"");
                    break;

                case ChatSelectChannel.Dead:
                    if (_systems.GetEntitySystemOrNull<GhostSystem>() is {IsGhost: true})
                        goto case ChatSelectChannel.Local;

                    if (_adminMgr.HasFlag(AdminFlags.Admin))
                        _consoleHost.ExecuteCommand($"dsay \"{CommandParsing.Escape(str)}\"");
                    else
                        _sawmill.Warning("Tried to speak on deadchat without being ghost or admin.");
                    break;

                // TODO sepearate radio and say into separate commands.
                case ChatSelectChannel.Radio:
                case ChatSelectChannel.Local:
                    _consoleHost.ExecuteCommand($"say \"{CommandParsing.Escape(str)}\"");
                    break;

                case ChatSelectChannel.Whisper:
                    _consoleHost.ExecuteCommand($"whisper \"{CommandParsing.Escape(str)}\"");
                    break;

                //Nyano - Summary: sends the command for telepath communication.
                case ChatSelectChannel.Telepathic:
                    _consoleHost.ExecuteCommand($"tsay \"{CommandParsing.Escape(str)}\"");
                    break;

                case ChatSelectChannel.XenoHivemind:
                    _consoleHost.ExecuteCommand($"aliensay \"{CommandParsing.Escape(str)}\"");
                    break;

                default:
                    throw new ArgumentOutOfRangeException(nameof(channel), channel, null);
            }
        }
        //Nyano - Summary: fires off the update permissions script.
        public void UpdatePermissions()
        {
            PermissionsUpdated?.Invoke();
        }
    }
}
