- type: Tag
  id: Cigar

- type: entity
  id: Cigar
  parent: BaseCigar
  name: "сигара"
  description: "Коричневий згорток тютюну і... ну, ви не зовсім впевнені."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigars/cigar.rsi
    state: unlit-icon
  - type: Tag
    tags:
      - Cigar
      - Trash
  - type: SolutionContainerManager
    solutions:
      smokable:
        maxVol: 50
        reagents:
          - ReagentId: Nicotine
            Quantity: 25
  - type: Clothing
    sprite: Objects/Consumable/Smokeables/Cigars/cigar.rsi
    slots: [ mask ]
    equippedPrefix: unlit
  - type: Item
    size: Tiny

- type: entity
  id: CigarSpent
  parent: Cigar
  suffix: spent
  components:
  - type: Sprite
    state: burnt-icon
  - type: Smokable
    state: Burnt
  - type: SolutionContainerManager
    solutions:
      smokable:
        maxVol: 50
  - type: Tag
    tags:
      - Cigar
      - Trash
      - Burnt

- type: entity
  id: CigarGold
  parent: Cigar
  name: "преміальна гаванська сигара"
  description: "Сигара, гідна лише найкращих з найкращих."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigars/cigar-gold.rsi
    state: unlit-icon
  - type: Clothing
    sprite: Objects/Consumable/Smokeables/Cigars/cigar-gold.rsi
    slots: [ mask ]
    equippedPrefix: unlit
  - type: Item
    size: Tiny
  - type: SolutionContainerManager
    solutions:
      smokable:
        maxVol: 50
        reagents:
          - ReagentId: Nicotine
            Quantity: 25
          - ReagentId: Gold
            Quantity: 5

- type: entity
  id: CigarGoldSpent
  parent: CigarGold
  suffix: spent
  components:
  - type: Sprite
    state: burnt-icon
  - type: Smokable
    state: Burnt
  - type: SolutionContainerManager
    solutions:
      smokable:
        maxVol: 50

- type: entity
  id: CigarRobust
  parent: Cigar
  name: "Сигара Cohiba Robusto"
  description: "Мало що ще можна бажати від сигари."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigars/cigar-gold.rsi
    state: unlit-icon
  - type: Clothing
    sprite: Objects/Consumable/Smokeables/Cigars/cigar-gold.rsi
    slots: [ mask ]
    equippedPrefix: unlit
  - type: Item
    size: Tiny
  - type: SolutionContainerManager
    solutions:
      smokable:
        maxVol: 50
        reagents:
          - ReagentId: Nicotine
            Quantity: 40 # infinite cancer works

- type: entity
  id: CigarRobustSpent
  parent: CigarRobust
  suffix: spent
  components:
  - type: Sprite
    state: burnt-icon
  - type: Smokable
    state: Burnt
  - type: SolutionContainerManager
    solutions:
      smokable:
        maxVol: 50