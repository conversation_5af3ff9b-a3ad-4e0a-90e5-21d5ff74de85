- type: job
  id: ServiceWorker
  name: job-name-serviceworker
  description: job-description-serviceworker
  playTimeTracker: JobServiceWorker
  startingGear: ServiceWorkerGear
  icon: "JobIconServiceWorker"
  supervisors: job-supervisors-service
  canBeAntag: true # DeltaV - Can be antagonist
  access:
  - Service
  - Maintenance
  extendedAccess:
  - Hydroponics
  - Bar # DeltaV - moved to extended access
  - Kitchen # DeltaV - moved to extended access
  requirements:
    - !type:CharacterOverallTimeRequirement
      min: 7200 # DeltaV - 2 hours
    - !type:CharacterEmployerRequirement
      inverted: true
      employers:
      - Unemployed

- type: startingGear
  id: ServiceWorkerGear
  subGear:
  - ServiceWorkerPlasmamanGear
  equipment:
    jumpsuit: ClothingUniformJumpsuitColorDarkGreen # DeltaV
    back: ClothingBackpackFilled
    shoes: ClothingShoesColorBlack
    id: ServiceWorkerPDA
    ears: ClothingHeadsetService
  innerClothingSkirt: ClothingUniformJumpskirtColorDarkGreen # DeltaV
  satchel: ClothingBackpackSatchelFilled
  duffelbag: ClothingBackpackDuffelFilled

- type: startingGear
  id: ServiceWorkerPlasmamanGear
  parent: BasePlasmamanGear
  equipment:
    jumpsuit: ClothingUniformEnvirosuitColorDarkGreen
    head: ClothingHeadEnvirohelmColorDarkGreen
    gloves: ClothingHandsGlovesEnviroglovesColorDarkGreen
