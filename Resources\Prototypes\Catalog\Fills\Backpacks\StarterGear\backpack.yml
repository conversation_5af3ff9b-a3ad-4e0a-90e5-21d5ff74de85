- type: entity
  parent: ClothingBackpack
  id: ClothingBackpackFilled
  categories: [ HideSpawnMenu ]

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackClown
  id: ClothingBackpackClownFilled
  components:
  - type: StorageFill
    contents:
      - id: BoxHug
      - id: RubberStampClown
      - id: CrayonRainbow

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSecurity
  id: ClothingBackpackSecurityFilled
  components:
  - type: StorageFill
    contents:
      - id: MagazinePistol

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackBrigmedic
  id: ClothingBackpackBrigmedicFilled
  components:
  - type: StorageFill
    contents:
      - id: EmergencyRollerBedSpawnFolded
      - id: BodyBagFolded
      - id: Portafib
      - id: BruteAutoInjector
        amount: 2
      - id: BurnAutoInjector
        amount: 2
      - id: MagazinePistol

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSecurity
  id: ClothingBackpackSecurityFilledDetective
  components:
  - type: StorageFill
    contents:
      - id: ForensicPad
      - id: ForensicScanner



- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackMedical
  id: ClothingBackpackMedicalFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackMedical
  id: ClothingBackpackParamedicFilled
  components:
  - type: StorageFill
    contents:
      - id: EmergencyRollerBedSpawnFolded

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackCaptain
  id: ClothingBackpackCaptainFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      #- name: StationCharter
      #- name: TelescopicBaton
- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackEngineering
  id: ClothingBackpackChiefEngineerFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      #- id: TelescopicBaton

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackScience
  id: ClothingBackpackResearchDirectorFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      #- id: TelescopicBaton

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpack
  id: ClothingBackpackHOPFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      #- id: TelescopicBaton

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackMedical
  id: ClothingBackpackCMOFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      #- id: TelescopicBaton

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackCargo
  id: ClothingBackpackQuartermasterFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      #- id: TelescopicBaton

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSecurity
  id: ClothingBackpackHOSFilled
  components:
  - type: StorageFill
    contents:
      - id: Flash
      - id: MagazinePistol

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackEngineering
  id: ClothingBackpackEngineeringFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackAtmospherics
  id: ClothingBackpackAtmosphericsFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackScience
  id: ClothingBackpackScienceFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackRobotics
  id: ClothingBackpackRoboticsFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackHydroponics
  id: ClothingBackpackHydroponicsFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackMime
  id: ClothingBackpackMimeFilled
  components:
  - type: StorageFill
    contents:
      - id: RubberStampMime

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackChemistry
  id: ClothingBackpackChemistryFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpack
  id: ClothingBackpackChaplainFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpack
  id: ClothingBackpackMusicianFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpack
  id: ClothingBackpackLibrarianFilled
  components:
    - type: StorageFill
      contents:
        - id: BookRandom

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpack
  id: ClothingBackpackDetectiveFilled
  components:
    - type: StorageFill
      contents:
      - id: Lighter
      - id: CigPackBlack
      - id: HandLabeler
      - id: BoxForensicPad

# ERT

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackERTLeader
  id: ClothingBackpackERTLeaderFilled
  components:
    - type: StorageFill
      contents:
        - id: BoxSurvivalEngineering
        - id: WeaponDisabler
        - id: MedicatedSuture
        - id: RegenerativeMesh
        - id: BoxZiptie
        - id: CrowbarRed
        - id: MagazineMagnum
      # PIRATE START
        - id: EnergySwordHoS
        - id: WeaponPistolN1984
      # PIRATE END

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackERTSecurity
  id: ClothingBackpackERTSecurityFilled
  components:
    - type: StorageFill
      contents:
        - id: BoxSurvivalEngineering
        - id: WeaponDisabler
        - id: MedicatedSuture
        - id: RegenerativeMesh
        - id: BoxZiptie
        - id: CrowbarRed
      # PIRATE START
        - id: MagazineMagnum
        - id: EnergySwordHoS
        - id: WeaponPistolN1984
      # PIRATE END

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackERTMedical
  id: ClothingBackpackERTMedicalFilled
  components:
    - type: StorageFill
      contents:
        - id: BoxSurvivalMedical
        - id: Hypospray
        - id: MedkitAdvancedFilled
        - id: CrowbarRed
        - id: OmnizineChemistryBottle
        - id: EpinephrineChemistryBottle
        - id: EpinephrineChemistryBottle
        - id: WeaponEnergyGunPistol
        - id: EnergySwordHoS # PIRATE
      

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackERTEngineer
  id: ClothingBackpackERTEngineerFilled
  components:
    - type: StorageFill
      contents:
        - id: BoxSurvivalEngineering
        - id: trayScanner
        - id: RCD
        - id: RCDAmmo
          amount: 2
        - id: CableMVStack
        - id: CableHVStack
        - id: CableApcStack
        - id: SheetPlasteel
        - id: SheetSteel
        - id: SheetGlass
        - id: WeaponEnergyGunPistol
        - id: EnergySwordHoS # PIRATE

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackERTJanitor
  id: ClothingBackpackERTJanitorFilled
  components:
    - type: StorageFill
      contents:
        - id: BoxSurvivalEngineering
        - id: LightReplacer
        - id: BoxLightMixed
        - id: BoxLightMixed
        - id: Soap
        - id: CrowbarRed
        - id: AdvMopItem

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackERTChaplain
  id: ClothingBackpackERTChaplainFilled
  components:
    - type: StorageFill
      contents:
        - id: BoxSurvivalEngineering
        - id: BoxCandle
        - id: BoxBodyBag
        - id: DrinkWaterMelonJuiceJug
        - id: Lantern
        - id: Lantern
        - id: Bible
        - id: CrowbarRed
        - id: FoodBakedBunHotX
        - id: FoodBakedBunHotX
        - id: FoodBakedBunHotX
        - id: FoodBakedBunHotX
        - id: Lighter

# Death Squad

- type: entity
  parent: ClothingBackpackERTSecurity
  id: ClothingBackpackDeathSquadFilled
  name: "рюкзак ескадрону смерті"
  description: "Містить набір найстрашніших агентів ЦК."
  components:
    - type: Storage
      grid:
      - 0,0,7,6
    - type: StorageFill
      contents:
        - id: BoxSurvivalEngineering
        - id: WeaponPulseRifle
        - id: WeaponPulsePistol
        - id: WeaponRevolverMateba
        - id: SpeedLoaderMagnumAP
        - id: SpeedLoaderMagnumAP
        - id: BoxFlashbang
        - id: ToolDebug # spanish army knife
        - id: WelderExperimental
        - id: Hypospray
        - id: DeathAcidifierImplanter # crew will try to steal their amazing hardsuits
        - id: FreedomImplanter

# Cargo

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackCargo
  id: ClothingBackpackCargoFilled

- type: entity
  categories: [ HideSpawnMenu ]
  parent: ClothingBackpackSalvage
  id: ClothingBackpackSalvageFilled

# Pirate

- type: entity
  parent: ClothingBackpackSatchelLeather
  id: ClothingBackpackPirateFilled
  suffix: Filled, Pirate
  components:
    - type: StorageFill
      contents:
        - id: BoxSurvival
        - id: Cutlass
        - id: WeaponRevolverPirate
        - id: ClothingEyesEyepatch
