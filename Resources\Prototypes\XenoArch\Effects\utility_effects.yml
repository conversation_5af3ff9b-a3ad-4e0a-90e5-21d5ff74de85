# Utility effects permanently modify the entity in some way when triggered, and they generally make it 'useful' for some purpose,
# like turning the artifact into a tool, or gun, or whatever.
- type: artifactEffect
  id: EffectIntercom
  targetDepth: 2
  effectHint: artifact-effect-hint-communication
  permanentComponents:
  - type: RadioMicrophone
    powerRequired: false
    toggleOnInteract: false
    listenRange: 3
  - type: Speech
  - type: RadioSpeaker
    toggleOnInteract: false
  - type: ActivatableUI
    key: enum.IntercomUiKey.Key
  - type: Intercom
    requiresPower: false
    supportedChannels:
    - Common
    - CentCom
    - Command
    - Engineering
    - Medical
    - Science
    - Security
    - Service
    - Supply

- type: artifactEffect
  id: EffectRandomInstrument
  targetDepth: 2
  effectHint: artifact-effect-hint-mental
  permanentComponents:
  - type: Instrument
  - type: ActivatableUI
    singleUser: true
    verbText: verb-instrument-openui
    key: enum.InstrumentUiKey.Key
  - type: RandomInstrumentArtifact

- type: artifactEffect
  id: EffectStorage
  targetDepth: 2
  effectHint: artifact-effect-hint-storage
  whitelist:
    components:
    - Item # it doesnt necessarily have to be restricted from structures, but i think it'll be better that way
  permanentComponents:
  - type: Item
    size: Huge
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
        ents: [ ]
  - type: Storage
    maxItemSize: Huge
    grid:
    - 0,0,10,5

- type: artifactEffect
  id: EffectPhasing
  targetDepth: 2
  effectHint: artifact-effect-hint-phasing
  permanentComponents:
  - type: PhasingArtifact

- type: artifactEffect
  id: EffectWandering
  targetDepth: 2
  effectHint: artifact-effect-hint-displacement
  blacklist:
    components:
    - Item # item artifacts can't be anchored, so wanderers can't really be scanned properly
  permanentComponents:
  - type: RandomWalk
    minSpeed: 12
    maxSpeed: 20
    minStepCooldown: 1
    maxStepCooldown: 3

- type: artifactEffect
  id: EffectSolutionStorage
  targetDepth: 2
  effectHint: artifact-effect-hint-storage
  whitelist:
    components:
    - Item
  permanentComponents:
  - type: SolutionContainerManager
    solutions:
      beaker:
        maxVol: 150
  - type: FitsInDispenser
    solution: beaker
  - type: RefillableSolution
    solution: beaker
  - type: DrainableSolution
    solution: beaker
  - type: ExaminableSolution
    solution: beaker
  - type: DrawableSolution
    solution: beaker
  - type: InjectableSolution
    solution: beaker
  - type: SolutionTransfer
    canChangeTransferAmount: true
  - type: Drink
    solution: beaker

- type: artifactEffect
  id: EffectSpeedUp
  targetDepth: 2
  effectHint: artifact-effect-hint-displacement
  whitelist:
    components:
    - Item
  permanentComponents:
  - type: HeldSpeedModifier
    walkModifier: 1.2
    sprintModifier: 1.3

- type: artifactEffect
  id: EffectDrill
  targetDepth: 3
  effectHint: artifact-effect-hint-drill
  whitelist:
    components:
    - Item
  permanentComponents:
  - type: UseDelay
  - type: MeleeWeapon
    damage:
      types:
        Piercing: 18
        Blunt: 4
    soundHit:
      path: /Audio/Weapons/bladeslice.ogg
  - type: Sharp

- type: artifactEffect
  id: EffectPowerGen20K
  targetDepth: 3
  effectHint: artifact-effect-hint-release
  blacklist:
    components:
    - Item
  permanentComponents:
  - type: PowerSupplier
    supplyRate: 20000
  - type: NodeContainer
    examinable: true
    nodes:
      output_hv:
        !type:CableDeviceNode
        nodeGroupID: HVPower

- type: artifactEffect
  id: EffectBigIron
  targetDepth: 3
  effectHint: artifact-effect-hint-gun
  whitelist:
    components:
    - Item
  permanentComponents:
  - type: ContainerContainer
    containers:
      revolver-ammo: !type:Container
  - type: RevolverAmmoProvider
    whitelist:
      tags:
      - CartridgeMagnum
      - SpeedLoaderMagnum
    proto: CartridgeMagnum
    capacity: 7
    chambers: [ True, True, True, True, True, True, True ]
    ammoSlots: [ null, null, null, null, null, null, null ]
    soundEject:
      path: /Audio/Weapons/Guns/MagOut/revolver_magout.ogg
    soundInsert:
      path: /Audio/Weapons/Guns/MagIn/revolver_magin.ogg
  - type: Gun
    selectedMode: SemiAuto
    fireRate: 2
    availableModes:
    - SemiAuto
    - FullAuto # no alien revolver in buildings
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/revolver.ogg

- type: artifactEffect
  id: EffectSentience
  targetDepth: 3
  effectHint: artifact-effect-hint-sentience
  permanentComponents:
  - type: GhostRole
    allowMovement: true
    allowSpeech: true
    makeSentient: true
    name: ghost-role-information-artifact-name
    description: ghost-role-information-artifact-description
    rules: ghost-role-information-freeagent-rules
    mindRoles:
    - MindRoleGhostRoleFreeAgent
    raffle:
      settings: default
  - type: GhostTakeoverAvailable
  - type: MovementSpeedModifier
    baseWalkSpeed: 0.25
    baseSprintSpeed: 0.5

- type: artifactEffect
  id: EffectMultitool
  targetDepth: 3
  effectHint: artifact-effect-hint-multitool
  whitelist:
    components:
    - Item
  permanentComponents:
  - type: UserInterface
    interfaces:
        enum.SignalLinkerUiKey.Key:
          type: SignalPortSelectorBoundUserInterface
  - type: ToolTileCompatible
  - type: Tool
    qualities:
    - Screwing
    speedModifier: 2 # Very powerful multitool to balance out the desire to sell or scrap for points
    useSound: /Audio/Items/drill_use.ogg
  - type: Tag
    tags:
      - Multitool
  - type: MultipleTool
    statusShowBehavior: true
    entries:
    - behavior: Screwing
      useSound:
        path: /Audio/Items/drill_use.ogg
      changeSound:
        path: /Audio/Items/change_drill.ogg
    - behavior: Prying
      useSound:
        path: /Audio/Items/jaws_pry.ogg
      changeSound:
        path: /Audio/Items/change_drill.ogg
    - behavior: Anchoring
      useSound:
        path: /Audio/Items/ratchet.ogg
      changeSound:
        path: /Audio/Items/change_drill.ogg
    - behavior: Cutting
      useSound:
        path: /Audio/Items/jaws_cut.ogg
      changeSound:
        path: /Audio/Items/change_drill.ogg
    - behavior: Pulsing
      changeSound:
        path: /Audio/Items/change_drill.ogg
