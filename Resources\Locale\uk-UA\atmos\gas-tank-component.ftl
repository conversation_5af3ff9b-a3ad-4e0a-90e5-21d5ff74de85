### GasTankComponent stuff.

# Examine text showing pressure in tank.
comp-gas-tank-examine = Тиск: [color=orange]{PRESSURE($pressure)}[/color].

# Examine text when internals are active.
comp-gas-tank-connected = Він підключений до зовнішнього компоненту.

# Examine text when valve is open or closed.
comp-gas-tank-examine-open-valve = Газовідвідний клапан [color=red]відкритий[/color].
comp-gas-tank-examine-closed-valve = Газовідвідний клапан [color=green]закритий[/color].

## Дієслово керування
control-verb-open-control-panel-text = Відкрити Панель Керування

## UI
gas-tank-window-label = Газовий Балон
gas-tank-window-internals-toggle-button = Перемкнути
gas-tank-window-output-pressure-label = Вихідний тиск
gas-tank-window-tank-pressure-text = Тиск: {$tankPressure} кПа
gas-tank-window-internal-text = Внутрішні справи: {$status}
gas-tank-window-internal-connected = [color=green]Підключенно[/color]
gas-tank-window-internal-disconnected = [color=red]Відключенно[/color]

## Valve
comp-gas-tank-open-valve = Відкрити Клапан
comp-gas-tank-close-valve = Закрити Клапан
