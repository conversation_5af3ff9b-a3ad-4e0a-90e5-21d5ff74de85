- type: entity
  parent: BaseStructure
  id: Railing
  name: "перила"
  description: "Елементарні перила, призначені для захисту таких ідіотів, як ти, від падіння."
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Walls/railing.rsi
    state: side
  - type: Icon
    sprite: Structures/Walls/railing.rsi
    state: side
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.49,-0.49,0.49,-0.25"
        density: 1000
        mask:
        - TableMask
        layer:
        - TableLayer
  - type: InteractionOutline
  - type: Repairable
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: FlimsyMetallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 20
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
          params:
            volume: -6
      - !type:SpawnEntitiesBehavior
        spawn:
          PartRodMetal1:
            min: 0
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Climbable
  - type: Construction
    graph: Railing
    node: railing
  - type: RCDDeconstructable
    cost: 2
    delay: 2
    fx: EffectRCDDeconstruct2  

- type: entity
  parent: BaseStructure
  id: RailingCorner
  name: "перила"
  description: "Елементарні перила, призначені для захисту таких ідіотів, як ти, від падіння."
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Walls/railing.rsi
    state: corner
  - type: Icon
    sprite: Structures/Walls/railing.rsi
    state: corner
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.49,-0.49,0.49,-0.25"
        density: 1000
        mask:
        - TableMask
        layer:
        - TableLayer
      fix2:
        shape:
          !type:PhysShapeAabb
          bounds: "0.49,0.49,0.25,-0.49"
        density: 1000
        mask:
        - TableMask
        layer:
        - TableLayer
  - type: InteractionOutline
  - type: Repairable
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: FlimsyMetallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 20
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
          params:
            volume: -6
      - !type:SpawnEntitiesBehavior
        spawn:
          PartRodMetal1:
            min: 0
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Climbable
  - type: Construction
    graph: Railing
    node: railingCorner
  - type: RCDDeconstructable
    cost: 2
    delay: 2
    fx: EffectRCDDeconstruct2  

- type: entity
  parent: BaseStructure
  id: RailingCornerSmall
  name: "перила"
  description: "Елементарні перила, призначені для захисту таких ідіотів, як ти, від падіння."
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Walls/railing.rsi
    state: corner_small
  - type: Icon
    sprite: Structures/Walls/railing.rsi
    state: corner_small
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.49,0.49,-0.25,0.25"
        density: 1000
        mask:
        - TableMask
        layer:
        - TableLayer
  - type: InteractionOutline
  - type: Repairable
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: FlimsyMetallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 20
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
          params:
            volume: -6
      - !type:SpawnEntitiesBehavior
        spawn:
          PartRodMetal1:
            min: 0
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Climbable
  - type: Construction
    graph: Railing
    node: railingCornerSmall
  - type: RCDDeconstructable
    cost: 2
    delay: 2
    fx: EffectRCDDeconstruct2  
    
- type: entity
  parent: BaseStructure
  id: RailingRound
  name: "перила"
  description: "Елементарні перила, призначені для захисту таких ідіотів, як ти, від падіння."
  components:
  - type: Sprite
    drawdepth: WallTops
    sprite: Structures/Walls/railing.rsi
    state: round
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.49,-0.49,0.49,-0.25"
        density: 1000
        mask:
          - TableMask
        layer:
          - TableLayer
      fix2:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.49,-0.49,-0.25,0.49"
        density: 1000
        mask:
          - TableMask
        layer:
          - TableLayer
      fix3:
        shape:
          !type:PhysShapeAabb
          bounds: "0.25,-0.49,0.49,0.49"
        density: 1000
        mask:
          - TableMask
        layer:
          - TableLayer
  - type: InteractionOutline
  - type: Repairable
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: FlimsyMetallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 20
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
          params:
            volume: -6
      - !type:SpawnEntitiesBehavior
        spawn:
          PartRodMetal1:
            min: 0
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Climbable
  - type: Construction
    graph: Railing
    node: railingRound
  - type: RCDDeconstructable
    cost: 2
    delay: 2
    fx: EffectRCDDeconstruct2  
