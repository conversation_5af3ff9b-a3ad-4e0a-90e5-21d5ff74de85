- type: entity
  id: LockerSyndicatePersonalFilled
  suffix: Filled
  parent: LockerSyndicatePersonal
  components:
  - type: EntityTableContainerFill
    containers:
      entity_storage: !type:AllSelector
        children:
        - id: ClothingBeltMilitaryWebbing
        - id: ClothingHandsGlovesCombat
        - id: JetpackBlackFilled
        - id: ClothingUniformJumpsuitOperative
        - id: ClothingUniformJumpskirtOperative
        - id: ClothingHeadsetAltSyndicate
        - id: ClothingEyesHudSyndicate

- type: entityTable
  id: FillLockerEmergencyStandard
  table: !type:AllSelector
    children:
    - id: ClothingMaskBreath
    - id: ClothingOuterSuitEmergency
    - !type:GroupSelector
      children:
      - id: EmergencyOxygenTankFilled
      - id: OxygenTankFilled
    - id: ToolboxEmergencyFilled
      prob: 0.5
    - id: MedkitOxygenFilled
      prob: 0.3
    - id: WeaponFlareGun
      prob: 0.05
    - id: BoxMRE
      prob: 0.1

- type: entity
  id: ClosetEmergencyFilledRandom
  parent: ClosetEmergency
  suffix: Filled, Random
  components:
  - type: EntityTableContainerFill
    containers:
      entity_storage: !type:NestedSelector
        tableId: FillLockerEmergencyStandard

- type: entity
  id: ClosetWallEmergencyFilledRandom
  parent: ClosetWallEmergency
  suffix: Filled, Random
  components:
  - type: EntityTableContainerFill
    containers:
      entity_storage: !type:NestedSelector
        tableId: FillLockerEmergencyStandard

- type: entity
  id: ClosetEmergencyN2FilledRandom
  parent: ClosetEmergencyN2
  suffix: Filled, Random
  components:
  - type: EntityTableContainerFill
    containers:
      entity_storage: !type:AllSelector
        children:
        - id: ClothingMaskBreath
        - id: ClothingOuterSuitEmergency
        - !type:GroupSelector
          children:
          - id: EmergencyNitrogenTankFilled
          - id: NitrogenTankFilled

- type: entityTable
  id: FillLockerFireStandard
  table: !type:AllSelector
    children:
    - id: ClothingOuterSuitFire
    - id: ClothingHeadHelmetFire
    - id: ClothingMaskGas
    - !type:GroupSelector
      children:
      - id: EmergencyOxygenTankFilled
      - id: OxygenTankFilled
    - id: CrowbarRed
    - !type:GroupSelector
      children:
      - id: FireExtinguisher
        weight: 98
      - id: SprayBottleWater #It's just budget cut after budget cut man
        weight: 2

- type: entity
  id: ClosetFireFilled
  parent: ClosetFire
  suffix: Filled
  components:
  - type: EntityTableContainerFill
    containers:
      entity_storage: !type:NestedSelector
        tableId: FillLockerFireStandard

- type: entity
  id: ClosetWallFireFilledRandom
  parent: ClosetWallFire
  suffix: Filled
  components:
  - type: EntityTableContainerFill
    containers:
      entity_storage: !type:NestedSelector
        tableId: FillLockerFireStandard

- type: entityTable
  id: SyndieMaintLoot
  table: !type:GroupSelector
    children:
    - !type:GroupSelector
      children:
      - id: ClothingUniformJumpsuitOperative
      - id: ClothingUniformJumpskirtOperative
    - id: ClothingBackpackDuffelSyndicate
    - id: CyberPen
    - id: CigPackSyndicate
    - id: ClothingBackpackDuffelSyndicatePyjamaBundle
    - id: ClothingBeltMilitaryWebbing
    - id: ClothingShoesBootsCombatFilled
    - id: ToolboxSyndicateFilled
    - id: BalloonSyn
    - id: WeaponSniperMosin
      weight: 2
    - !type:EntSelector
      id: ClothingEyesNightVisionGogglesSyndie
    - !type:EntSelector
      id: ClothingEyesThermalVisionGogglesSyndie

- type: entityTable
  id: MaintenanceLockerLoot
  table: !type:AllSelector
    children:
    - id: StrangePill
      prob: 0.20
    - id: CrayonMagic # Delta V - Just another way to get it
      prob: .01
    - id: Wristwatch
      prob: 0.05
    # Tools
    - !type:NestedSelector
      tableId: MaintToolsTable
      rolls: !type:RangeNumberSelector
        range: 1, 5
    # Fluff
    - !type:NestedSelector
      tableId: MaintFluffTable
      prob: 0.33
      rolls: !type:RangeNumberSelector
        range: 0, 2
    # Plushies
    - !type:NestedSelector
      tableId: AllPlushiesTable
      prob: 0.10
      rolls: !type:RangeNumberSelector
        range: 1, 2
    # Weapons
    - !type:NestedSelector
      tableId: MaintWeaponTable
      prob: 0.075
    # Syndie Loot
    - !type:NestedSelector
      tableId: SyndieMaintLoot
      prob: 0.05

- type: entity
  id: ClosetMaintenanceFilledRandom
  suffix: Filled, Random
  parent: ClosetMaintenance
  components:
  - type: EntityTableContainerFill
    containers:
      entity_storage: !type:NestedSelector
        tableId: MaintenanceLockerLoot

- type: entity
  id: ClosetWallMaintenanceFilledRandom
  parent: ClosetWall
  suffix: Filled, Random
  components:
  - type: EntityTableContainerFill
    containers:
      entity_storage: !type:NestedSelector
        tableId: MaintenanceLockerLoot

- type: entity
  id: ClosetWallRadiationFilled
  suffix: Filled
  parent: ClosetWallRadiation
  components:
  - type: EntityTableContainerFill
    containers:
      entity_storage: !type:AllSelector
        children:
        - !type:EntSelector
          id: ClothingOuterSuitRad
          amount: !type:ConstantNumberSelector
            value: 2
        - !type:EntSelector
          id: GeigerCounter
          amount: !type:ConstantNumberSelector
            value: 2
        - id: MedkitRadiationFilled
