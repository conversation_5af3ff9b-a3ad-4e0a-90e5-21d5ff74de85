using Content.Client.Stylesheets;
using Content.Client.UserInterface.Controls;
using Content.Shared.NameIdentifier;
using Content.Shared.Preferences;
using Content.Shared.Silicons.Borgs;
using Content.Shared.Silicons.Borgs.Components;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Timing;

namespace Content.Client.Silicons.Borgs;

[GenerateTypedNameReferences]
public sealed partial class BorgMenu : FancyWindow
{
    [Dependency] private readonly IEntityManager _entity = default!;

    public Action? BrainButtonPressed;
    public Action? EjectBatteryButtonPressed;
    public Action<string>? NameChanged;
    public Action<EntityUid>? RemoveModuleButtonPressed;

    public float AccumulatedTime;
    private string _lastValidName;
    private List<EntityUid> _modules = new();

    public EntityUid Entity;

    public BorgMenu()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        _lastValidName = NameLineEdit.Text;

        EjectBatteryButton.OnPressed += _ => EjectBatteryButtonPressed?.Invoke();
        BrainButton.OnPressed += _ => BrainButtonPressed?.Invoke();

        NameLineEdit.OnTextChanged += OnNameChanged;
        NameLineEdit.OnTextEntered += OnNameEntered;
        NameLineEdit.OnFocusExit += OnNameFocusExit;

        UpdateBrainButton();
    }

    public void SetEntity(EntityUid entity)
    {
        Entity = entity;
        BorgSprite.SetEntity(entity);

        if (_entity.TryGetComponent<NameIdentifierComponent>(Entity, out var nameIdentifierComponent))
        {
            NameIdentifierLabel.Visible = true;
            NameIdentifierLabel.Text = nameIdentifierComponent.FullIdentifier;

            var fullName = _entity.GetComponent<MetaDataComponent>(Entity).EntityName;
            var name = fullName.Substring(0, fullName.Length - nameIdentifierComponent.FullIdentifier.Length - 1);
            NameLineEdit.Text = name;
        }
        else
        {
            NameIdentifierLabel.Visible = false;
            NameLineEdit.Text = _entity.GetComponent<MetaDataComponent>(Entity).EntityName;
        }
    }

    protected override void FrameUpdate(FrameEventArgs args)
    {
        base.FrameUpdate(args);

        AccumulatedTime += args.DeltaSeconds;
        BorgSprite.OverrideDirection = (Direction) ((int) AccumulatedTime % 4 * 2);
    }

    public void UpdateState(BorgBuiState state)
    {
        EjectBatteryButton.Disabled = !state.HasBattery;
        ChargeBar.Value = state.ChargePercent;
        ChargeLabel.Text = Loc.GetString("borg-ui-charge-label",
            ("charge", (int) MathF.Round(state.ChargePercent * 100)));

        UpdateBrainButton();
        UpdateModulePanel();
    }

    private void UpdateBrainButton()
    {
        if (_entity.TryGetComponent(Entity, out BorgChassisComponent? chassis) && chassis.BrainEntity is { } brain)
        {
            BrainButton.Text = _entity.GetComponent<MetaDataComponent>(brain).EntityName;
            BrainView.Visible = true;
            BrainView.SetEntity(brain);
            BrainButton.Disabled = false;
            BrainButton.AddStyleClass(StyleBase.ButtonOpenLeft);
        }
        else
        {
            BrainButton.Text = Loc.GetString("borg-ui-no-brain");
            BrainButton.Disabled = true;
            BrainView.Visible = false;
            BrainButton.RemoveStyleClass(StyleBase.ButtonOpenLeft);
        }
    }

    private void UpdateModulePanel()
    {
        if (!_entity.TryGetComponent(Entity, out BorgChassisComponent? chassis))
            return;

        ModuleCounter.Text = Loc.GetString("borg-ui-module-counter",
            ("actual", chassis.ModuleCount),
            ("max", chassis.MaxModules));

        if (chassis.ModuleContainer.Count == _modules.Count)
        {
            var isSame = true;
            foreach (var module in chassis.ModuleContainer.ContainedEntities)
            {
                if (_modules.Contains(module))
                    continue;
                isSame = false;
                break;
            }

            if (isSame)
                return;
        }

        ModuleContainer.Children.Clear();
        _modules.Clear();
        foreach (var module in chassis.ModuleContainer.ContainedEntities)
        {
            var moduleComponent = _entity.GetComponent<BorgModuleComponent>(module);
            var control = new BorgModuleControl(module, _entity, !moduleComponent.DefaultModule);
            control.RemoveButtonPressed += () =>
            {
                RemoveModuleButtonPressed?.Invoke(module);
            };
            ModuleContainer.AddChild(control);
        }
    }

    private void OnNameChanged(LineEdit.LineEditEventArgs obj)
    {
        if (obj.Text.Length == 0 ||
            string.IsNullOrWhiteSpace(obj.Text) ||
            string.IsNullOrEmpty(obj.Text))
        {
            return;
        }

        if (obj.Text.Length > HumanoidCharacterProfile.MaxNameLength)
        {
            obj.Control.Text = obj.Text.Substring(0, HumanoidCharacterProfile.MaxNameLength);
        }

        _lastValidName = obj.Control.Text;
        obj.Control.Text = _lastValidName;
    }

    private void OnNameEntered(LineEdit.LineEditEventArgs obj)
    {
        NameChanged?.Invoke(_lastValidName);
    }

    private void OnNameFocusExit(LineEdit.LineEditEventArgs obj)
    {
        if (obj.Text.Length > HumanoidCharacterProfile.MaxNameLength ||
            obj.Text.Length == 0 ||
            string.IsNullOrWhiteSpace(obj.Text) ||
            string.IsNullOrEmpty(obj.Text))
        {
            obj.Control.Text = _lastValidName.Trim();
        }

        NameChanged?.Invoke(_lastValidName);
    }
}

