- type: entity
  parent: ClothingMaskBase
  id: ClothingMaskGasChameleon
  name: "протигаз"
  description: "Маска, що закриває обличчя, яку можна під'єднати до системи подачі повітря."
  suffix: Chameleon
  components:
    - type: Tag
      tags: [] # ignore "WhitelistChameleon" tag
    - type: Sprite
      sprite: Clothing/Mask/gas.rsi
    - type: Clothing
      sprite: Clothing/Mask/gas.rsi
    - type: ChameleonClothing
      slot: [mask]
      default: ClothingMaskGas
    - type: BreathMask
    - type: IdentityBlocker # need that for default ClothingMaskGas
    - type: UserInterface
      interfaces:
        enum.ChameleonUiKey.Key:
          type: ChameleonBoundUserInterface
    - type: HideLayerClothing
      slots:
      - Snout

- type: entity
  parent: ClothingMaskGasChameleon
  id: ClothingMaskGasVoiceChameleon
  suffix: Voice Mask, Chameleon
  components:
    - type: VoiceMask
    - type: HideLayerClothing
      slots:
      - Snout
    - type: UserInterface
      interfaces:
        enum.ChameleonUiKey.Key:
          type: ChameleonBoundUserInterface
        enum.VoiceMaskUIKey.Key:
          type: VoiceMaskBoundUserInterface

- type: entity
  parent: ClothingMaskBase
  id: ClothingMaskWeldingGas
  name: "зварювальна маска-протигаз"
  description: "Протигаз із вбудованими зварювальними окулярами та захисною маскою. Виглядає як череп, явно розроблений ботаніком."
  components:
  - type: Sprite
    sprite: Clothing/Mask/welding-gas.rsi
    state: icon
  - type: Clothing
    sprite: Clothing/Mask/welding-gas.rsi
  - type: BreathMask
  - type: IngestionBlocker
  - type: IdentityBlocker
  - type: FlashImmunity
  - type: EyeProtection
  - type: PhysicalComposition
    materialComposition:
      Steel: 200
      Glass: 100
  - type: StaticPrice
    price: 100
  - type: Tag
    tags:
    - WhitelistChameleon
  - type: HideLayerClothing
    slots:
    - Snout

- type: entity
  parent: ClothingMaskBase
  id: ClothingMaskGoldenCursed
  name: "золота маска"
  description: "Раніше використовувані в дивних пантомімах, після того, як один з акторів збожеволів на сцені, ці маски перестали використовуватися. Ви можете заприсягтися, що його обличчя спотворюється, коли ви не дивитеся."
  components:
  - type: Sprite
    sprite: Clothing/Mask/goldenmask.rsi
    layers:
    - state: icon
      map: [ "mask" ]
  - type: Clothing
    sprite: Clothing/Mask/goldenmask.rsi
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.CursedMaskVisuals.State:
        mask:
          Neutral: { state: icon }
          Despair: { state: icon-despair }
          Joy: { state: icon-joy }
          Anger: { state: icon-anger }
  - type: Tag
    tags: [] # ignore "WhitelistChameleon" tag
  - type: SelfEquipOnly
  - type: CursedMask
    despairDamageModifier:
      coefficients:
        Blunt: 0.6
        Slash: 0.6
        Piercing: 0.4
  - type: HideLayerClothing
    slots:
    - Snout
  - type: IngestionBlocker
  - type: StaticPrice
    price: 5000
