- type: constructionGraph
  id: AirAlarm
  start: start
  graph:
  - node: start
    edges:
    - to: assembly
      steps:
      - material: Steel
        amount: 2
        doAfter: 2.0

  - node: assembly
    entity: AirAlarmAssembly
    actions:
    - !type:AppearanceChange
    edges:
    - to: wired
      steps:
      - material: Cable
        amount: 2
        doAfter: 1
    - to: start
      completed:
      - !type:GivePrototype
        prototype: SheetSteel1
        amount: 2
      - !type:DeleteEntity {}
      steps:
      - tool: Welding
        doAfter: 2

  - node: wired
    entity: AirAlarmAssembly
    edges:
    - to: electronics
      steps:
      - tag: AirAlarmElectronics
        store: board
        name: "air alarm electronics"
        icon:
          sprite: "Objects/Misc/module.rsi"
          state: "door_electronics" # /tg/ uses the same sprite, right?
        doAfter: 1
    - to: assembly
      completed:
      - !type:GivePrototype
        prototype: CableApcStack1
        amount: 2
      steps:
      - tool: Cutting
        doAfter: 1

  - node: electronics
    actions:
    - !type:AppearanceChange
    edges:
    - to: air_alarm
      steps:
      - tool: Screwing
        doAfter: 2

  - node: air_alarm
    entity: AirAlarm
    edges:
    - to: wired
      conditions:
      - !type:AllWiresCut {}
      - !type:WirePanel {}
      - !type:ContainerNotEmpty
        container: board
      completed:
      - !type:EmptyAllContainers
        pickup: true
        emptyAtUser: true
      steps:
      - tool: Prying
        doAfter: 1

- type: constructionGraph
  id: FireAlarm
  start: start
  graph:
  - node: start
    edges:
    - to: assembly
      steps:
      - material: Steel
        amount: 2
        doAfter: 2.0

  - node: assembly
    entity: FireAlarmAssembly
    actions:
    - !type:AppearanceChange
    edges:
    - to: wired
      steps:
      - material: Cable
        amount: 2
        doAfter: 1
    - to: start
      completed:
      - !type:GivePrototype
        prototype: SheetSteel1
        amount: 2
      - !type:DeleteEntity {}
      steps:
      - tool: Welding
        doAfter: 2

  - node: wired
    entity: FireAlarmAssembly
    edges:
    - to: electronics
      steps:
      - tag: FireAlarmElectronics
        store: board
        name: "fire alarm electronics"
        icon:
          sprite: "Objects/Misc/module.rsi"
          state: "door_electronics" # /tg/ uses the same sprite, right?
        doAfter: 1
    - to: assembly
      completed:
      - !type:GivePrototype
        prototype: CableApcStack1
        amount: 2
      steps:
      - tool: Cutting
        doAfter: 1

  - node: electronics
    actions:
    - !type:AppearanceChange
    edges:
    - to: fire_alarm
      steps:
      - tool: Screwing
        doAfter: 2

  - node: fire_alarm
    entity: FireAlarm
    edges:
    - to: wired
      conditions:
      - !type:AllWiresCut {}
      - !type:WirePanel {}
      - !type:ContainerNotEmpty
        container: board
      completed:
      - !type:EmptyAllContainers
        pickup: true
        emptyAtUser: true
      steps:
      - tool: Prying
        doAfter: 1
