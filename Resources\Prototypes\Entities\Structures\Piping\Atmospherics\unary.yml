- type: entity
  parent: GasPipeBase
  abstract: true
  id: GasUnaryBase
  placement:
    mode: SnapgridCenter
  components:
    - type: AtmosDevice
    - type: Tag
      tags:
        - Unstackable
    - type: SubFloorHide
      blockInteractions: false
      blockAmbience: false
    - type: NodeContainer
      nodes:
        pipe:
          !type:PipeNode
          nodeGroupID: Pipe
          pipeDirection: South
    - type: CollideOnAnchor

- type: entity
  parent: [GasUnaryBase, AirSensorBase]
  id: GasVentPump
  name: "вентиляційний отвір"
  description: "Накачує газ у кімнату."
  placement:
    mode: SnapgridCenter
  components:
    - type: VentCrawlerTube  # Goobstation - ventcrawl
      containerId: VentCrawlerTube
    - type: VentCrawlerEntry
    - type: ContainerContainer # Goobstation - ventcrawl
      containers:
        VentCrawlerTube: !type:Container
    - type: ApcPowerReceiver
    - type: ExtensionCableReceiver
    - type: DeviceNetwork
      prefix: device-address-prefix-vent
    - type: Tag
      tags:
        - GasVent
        - Unstackable
    - type: Sprite
      drawdepth: FloorObjects
      sprite: Structures/Piping/Atmospherics/vent.rsi
      noRot: true
      layers:
        - sprite: Structures/Piping/Atmospherics/pipe.rsi
          state: pipeHalf
          map: [ "enum.PipeVisualLayers.Pipe" ]
        - state: vent_off
          map: [ "enabled", "enum.SubfloorLayers.FirstLayer" ]
    - type: Appearance
    - type: PipeColorVisuals
    - type: GenericVisualizer
      visuals:
        enum.VentPumpVisuals.State:
          enabled:
            Off: { state: vent_off }
            In: { state: vent_in }
            Out: { state: vent_out }
            Welded: { state: vent_welded }
    - type: GasVentPump
    - type: Construction
      graph: GasUnary
      node: ventpump
    - type: VentCritterSpawnLocation
    - type: AmbientSound
      enabled: true
      volume: -12
      range: 5
      sound:
        path: /Audio/Ambience/Objects/gas_vent.ogg
    - type: Weldable
    - type: AtmosMonitoringConsoleDevice
      navMapBlip: GasVentOpening

- type: entity
  parent: GasUnaryBase
  id: GasPassiveVent
  name: "пасивна вентиляція"
  description: "Непривідний вентилятор, який вирівнює гази з обох боків."
  placement:
    mode: SnapgridCenter
  components:
    # TODO ATMOS: Find sprite for this.
    - type: Sprite
      drawdepth: FloorObjects
      sprite: Structures/Piping/Atmospherics/vent.rsi
      noRot: true
      layers:
        - sprite: Structures/Piping/Atmospherics/pipe.rsi
          state: pipeHalf
          map: [ "enum.PipeVisualLayers.Pipe" ]
        - state: vent_off
          map: [ "enum.SubfloorLayers.FirstLayer" ]
    - type: Appearance
    - type: PipeColorVisuals
    - type: GasPassiveVent
    - type: Construction
      graph: GasUnary
      node: passivevent
    - type: AtmosMonitoringConsoleDevice
      navMapBlip: GasVentOpening
    - type: VentCrawlerEntry # Goobstation - ventcrawl
    - type: VentCrawlerTube # Goobstation - ventcrawl
      containerId: VentCrawlerTube
    - type: ContainerContainer # Goobstation - ventcrawl
      containers:
        VentCrawlerTube: !type:Container
    - type: Weldable  # Goobstation - ventcrawl
      time: 3

- type: entity
  parent: [GasUnaryBase, AirSensorBase]
  id: GasVentScrubber
  name: "очищувач повітря"
  description: "Всмоктує газ у під'єднані труби."
  placement:
    mode: SnapgridCenter
  components:
    - type: VentCrawlerTube # Goobstation - ventcrawl
      containerId: VentCrawlerTube
    - type: VentCrawlerEntry  # Goobstation - ventcrawl
    - type: ContainerContainer # Goobstation - ventcrawl
      containers:
        VentCrawlerTube: !type:Container
    - type: ApcPowerReceiver
    - type: ExtensionCableReceiver
    - type: DeviceNetwork
      prefix: device-address-prefix-scrubber
    - type: Tag
      tags:
        - GasScrubber
        - Unstackable
    - type: Sprite
      drawdepth: FloorObjects
      sprite: Structures/Piping/Atmospherics/scrubber.rsi
      noRot: true
      layers:
        - sprite: Structures/Piping/Atmospherics/pipe.rsi
          state: pipeHalf
          map: [ "enum.PipeVisualLayers.Pipe" ]
        - state: scrub_off
          map: [ "enabled", "enum.SubfloorLayers.FirstLayer" ]
    - type: Appearance
    - type: PipeColorVisuals
    - type: GenericVisualizer
      visuals:
        enum.ScrubberVisuals.State:
          enabled:
            Off: { state: scrub_off }
            Scrub: { state: scrub_on }
            Siphon: { state: scrub_purge }
            WideScrub: { state: scrub_wide }
            Welded: { state: scrub_welded }
    - type: AtmosDevice
    - type: GasVentScrubber
    - type: Construction
      graph: GasUnary
      node: ventscrubber
    - type: AmbientSound
      enabled: true
      volume: -12
      range: 5
      sound:
        path: /Audio/Ambience/Objects/gas_vent.ogg
    - type: Weldable
    - type: AtmosMonitoringConsoleDevice
      navMapBlip: GasVentScrubber

- type: entity
  parent: GasUnaryBase
  id: GasOutletInjector
  name: "інжектор повітря"
  description: "Всмоктує газ у під'єднані труби."
  placement:
    mode: SnapgridCenter
  components:
    - type: Sprite
      drawdepth: FloorObjects
      sprite: Structures/Piping/Atmospherics/outletinjector.rsi
      layers:
        - state: pipeHalf
          sprite: Structures/Piping/Atmospherics/pipe.rsi
          map: [ "enum.PipeVisualLayers.Pipe" ]
        - state: injector
          map: [ "enum.SubfloorLayers.FirstLayer" ]
        - state: injector-unshaded
          shader: unshaded
          map: [ "enum.LightLayers.Unshaded" ]
          color: "#990000"
    - type: GenericVisualizer
      visuals:
       # toggle color of the unshaded light:
       enum.OutletInjectorVisuals.Enabled:
         enum.LightLayers.Unshaded:
           True: { color: "#5eff5e" }
           False: { color: "#990000" }
    - type: Appearance
    - type: PipeColorVisuals
    - type: GasOutletInjector
    - type: Construction
      graph: GasUnary
      node: outletinjector
    - type: SubFloorHide
      visibleLayers:
      - enum.SubfloorLayers.FirstLayer
      - enum.LightLayers.Unshaded
    - type: AtmosMonitoringConsoleDevice
      navMapBlip: GasVentOpening
    - type: VentCrawlerTube # Goobstation - ventcrawl
      containerId: VentCrawlerTube
    - type: VentCrawlerJunction # Goobstation - ventcrawl
      degrees:
        - 0
    - type: ContainerContainer # Goobstation - ventcrawl
      containers:
        VentCrawlerTube: !type:Container

- type: entity
  parent: [ BaseMachinePowered, ConstructibleMachine ]
  id: BaseGasThermoMachine
  name: "термомашина"
  abstract: true
  placement:
    mode: SnapgridCenter
  components:
    - type: Sprite
      sprite: _Pirate/Structures/Piping/Atmospherics/thermomachine.rsi # Pirate retexture
      snapCardinals: true
    - type: Appearance
    - type: PipeColorVisuals
    - type: Rotatable
    - type: GasThermoMachine
    - type: AtmosPipeColor
    - type: AtmosDevice
    - type: UserInterface
      interfaces:
        enum.ThermomachineUiKey.Key:
          type: GasThermomachineBoundUserInterface
    - type: ActivatableUI
      inHandsOnly: false
      key: enum.ThermomachineUiKey.Key
    - type: WiresPanel
    - type: WiresVisuals
    #- type: PipeRestrictOverlap  Allow for pipe stacking
    - type: NodeContainer
      nodes:
        pipe:
          !type:PipeNode
          nodeGroupID: Pipe
          pipeDirection: South
    - type: Transform
      noRot: false
    - type: DeviceNetwork
      deviceNetId: AtmosDevices
      receiveFrequencyId: AtmosMonitor
      transmitFrequencyId: AtmosMonitor
      sendBroadcastAttemptEvent: true
      examinableAddress: true
    - type: WiredNetworkConnection
    - type: PowerSwitch
    - type: AtmosMonitoringConsoleDevice
      navMapBlip: Thermoregulator

- type: entity
  parent: BaseGasThermoMachine
  id: GasThermoMachineFreezer
  name: "охолоджувач"
  description: "Охолоджує газ у підключених трубах."
  placement:
    mode: SnapgridCenter
  components:
    - type: StationAiWhitelist
    - type: Sprite
      granularLayersRendering: true
      layers:
        - state: freezerOff
        - state: freezerOn # Pirate retexture
          map: ["enum.PowerDeviceVisualLayers.Powered"]
        - state: freezer-Unshaded
          shader: unshaded
          map: ["freezing"]
        - state: freezerPanelOpen
          map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
        - state: pipe
          map: [ "enum.PipeVisualLayers.Pipe" ]
          renderingStrategy: Default
    - type: GenericVisualizer
      visuals:
        enum.PowerDeviceVisuals.Powered:
          enum.PowerDeviceVisualLayers.Powered:
            True: { visible: true } # Pirate retexture
            False: { visible: false }
          freezing:
            True: { visible: true }
            False: { visible: false }
    - type: GasThermoMachine
      coefficientOfPerformance: -3.9
    - type: ApcPowerReceiver
      powerDisabled: true #starts off
    - type: Machine
      board: ThermomachineFreezerMachineCircuitBoard
    - type: DeviceNetwork
      prefix: device-address-prefix-freezer
    - type: StealTarget
      stealGroup: FreezerHeater
    - type: LitOnPowered
    - type: PointLight
      radius: 1.5
      energy: 1.5
      color: "#47bfff"

- type: entity
  parent: GasThermoMachineFreezer
  id: GasThermoMachineFreezerEnabled
  suffix: Enabled
  components:
  - type: GasThermoMachine
  - type: ApcPowerReceiver
    powerDisabled: false

- type: entity
  parent: BaseGasThermoMachine
  id: GasThermoMachineHeater
  name: "обігрівач"
  description: "Нагріває газ у з'єднаних трубах."
  placement:
    mode: SnapgridCenter
  components:
    - type: StationAiWhitelist
    - type: Sprite
      granularLayersRendering : true
      layers:
        - state: heaterOff
          map: [ "enum.PowerDeviceVisualLayers.Powered" ]
        - state: heaterPanelOpen
          map: ["enum.WiresVisualLayers.MaintenancePanel"]
        - state: pipe
          map: [ "enum.PipeVisualLayers.Pipe" ]
          renderingStrategy: Default
    - type: GenericVisualizer
      visuals:
        enum.PowerDeviceVisuals.Powered:
          enum.PowerDeviceVisualLayers.Powered:
            True: { state: heaterOn }
            False: { state: heaterOff }
    - type: GasThermoMachine
      coefficientOfPerformance: 0.95
    - type: ApcPowerReceiver
      powerDisabled: true #starts off
    - type: Machine
      board: ThermomachineHeaterMachineCircuitBoard
    - type: DeviceNetwork
      prefix: device-address-prefix-heater
    - type: StealTarget
      stealGroup: FreezerHeater
    - type: LitOnPowered
    - type: PointLight
      radius: 1.5
      energy: 1.5
      color: "#ffc747"

- type: entity
  parent: GasThermoMachineHeater
  id: GasThermoMachineHeaterEnabled
  suffix: Enabled
  components:
  - type: GasThermoMachine
  - type: ApcPowerReceiver
    powerDisabled: false

- type: entity
  parent: GasThermoMachineFreezer
  id: GasThermoMachineHellfireFreezer
  name: "морозильна камера hellfire"
  description: "Удосконалена машина, яка охолоджує газ у з'єднаних трубах. Має побічний ефект охолодження навколишнього середовища. Холодно, як у пеклі!"
  components:
  - type: Sprite
    sprite: _Pirate/Structures/Piping/Atmospherics/hellfirethermomachine.rsi # Pirate retexture
    granularLayersRendering : true
    layers:
      - state: freezerOff
      - state: freezerOn
        map: ["enum.PowerDeviceVisualLayers.Powered"]
      - state: freezer-Unshaded
        shader: unshaded
        map: ["hellfreezing"]
      - state: freezerPanelOpen
        map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      - state: pipe
        map: [ "enum.PipeVisualLayers.Pipe" ]
        renderingStrategy: Default
  - type: GenericVisualizer
    visuals:
      enum.PowerDeviceVisuals.Powered:
        enum.PowerDeviceVisualLayers.Powered:
          True: { visible: true }
          False: { visible: false }
        hellfreezing:
          True: { visible: true }
          False: { visible: false }
  - type: GasThermoMachine
    minTemperature: 23.15
    heatCapacity: 40000
    energyLeakPercentage: 0.15
  - type: Machine
    board: HellfireFreezerMachineCircuitBoard

- type: entity
  parent: GasThermoMachineHeater
  id: GasThermoMachineHellfireHeater
  name: "пекельний обігрівач"
  description: "Удосконалена машина, яка нагріває газ у під'єднаних трубах. Має побічний ефект витоку тепла в навколишнє середовище. Гаряче, як у пеклі!"
  components:
  - type: Sprite
    sprite: _Pirate/Structures/Piping/Atmospherics/hellfirethermomachine.rsi # Pirate retexture
    granularLayersRendering : true
    layers:
      - state: heaterOff
      - state: heaterOn
        map: ["enum.PowerDeviceVisualLayers.Powered"]
      - state: heater-Unshaded
        shader: unshaded
        map: ["hellheating"]
      - state: heaterPanelOpen
        map: [ "enum.WiresVisualLayers.MaintenancePanel" ]
      - state: pipe
        map: [ "enum.PipeVisualLayers.Pipe" ]
        renderingStrategy: Default
  - type: GenericVisualizer
    visuals:
      enum.PowerDeviceVisuals.Powered:
        enum.PowerDeviceVisualLayers.Powered:
          True: { visible: true }
          False: { visible: false }
        hellheating:
          True: { visible: true }
          False: { visible: false }
  - type: GasThermoMachine
    maxTemperature: 1193.15
    heatCapacity: 40000
    energyLeakPercentage: 0.15
  - type: Machine
    board: HellfireHeaterMachineCircuitBoard

- type: entity
  parent: [ BaseMachinePowered, SmallConstructibleMachine ]
  id: BaseGasCondenser
  name: "конденсатор"
  description: "Конденсує гази в рідину. Тепер нам потрібен водопровід."
  placement:
    mode: SnapgridCenter
  components:
  - type: Sprite
    sprite: Structures/Piping/Atmospherics/condenser.rsi
    snapCardinals: true
    granularLayersRendering: true
    layers:
    - state: off
      map: [ "enum.PowerDeviceVisualLayers.Powered" ]
    - state: panel
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
    - state: pipe
      map: [ "enum.PipeVisualLayers.Pipe" ]
      renderingStrategy: Default
    - state: fill-1
      map: ["enum.SolutionContainerLayers.Fill"]
      visible: false
    - state: trans
  - type: GenericVisualizer
    visuals:
      enum.PowerDeviceVisuals.Powered:
        enum.PowerDeviceVisualLayers.Powered:
          True: { state: on }
          False: { state: off }
  - type: SolutionContainerVisuals
    maxFillLevels: 7
    fillBaseName: fill-
  - type: Appearance
  - type: PipeColorVisuals
  - type: Rotatable
  - type: GasCondenser
  - type: AtmosPipeColor
  - type: AtmosDevice
  #- type: PipeRestrictOverlap # Allow pipe stacking on device.
  - type: ApcPowerReceiver
    powerLoad: 10000
  - type: Machine
    board: CondenserMachineCircuitBoard
  - type: WiresPanel
  - type: WiresVisuals
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:SpillBehavior
        solution: tank
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
  - type: NodeContainer
    nodes:
      pipe:
        !type:PipeNode
        nodeGroupID: Pipe
        pipeDirection: South
  - type: Transform
    noRot: false
  - type: SolutionContainerManager
    solutions:
      tank:
        maxVol: 400
  - type: MixableSolution
    solution: tank
  - type: DrainableSolution
    solution: tank
  - type: ExaminableSolution
    solution: tank
  - type: PowerSwitch
  - type: AtmosMonitoringConsoleDevice
    navMapBlip: Thermoregulator
