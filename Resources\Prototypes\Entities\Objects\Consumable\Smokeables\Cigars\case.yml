- type: entity
  id: CigarCase
  parent: [ BaseStorageItem, BaseBagOpenClose ]
  name: "футляр для сигар"
  description: "Футляр для зберігання сигар, коли ви їх не курите."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigars/case.rsi
    layers:
    - state: closed
    - state: open
      map: ["openLayer"]
    - state: cigar1
      map: ["cigar1"]
      visible: false
    - state: cigar2
      map: ["cigar2"]
      visible: false
    - state: cigar3
      map: ["cigar3"]
      visible: false
    - state: cigar4
      map: ["cigar4"]
      visible: false
    - state: cigar5
      map: ["cigar5"]
      visible: false
    - state: cigar6
      map: ["cigar6"]
      visible: false
    - state: cigar7
      map: ["cigar7"]
      visible: false
    - state: cigar8
      map: ["cigar8"]
      visible: false
  - type: Storage
    grid:
    - 0,0,3,1
  - type: Item
    sprite: Objects/Consumable/Smokeables/Cigars/case.rsi
    size: Normal
    shape:
    - 0,0,2,1
    storedRotation: 90
  - type: StorageFill
    contents:
    - id: Cigar
      amount: 8
  - type: ItemCounter
    count:
      tags: [Cigar]
    composite: true
    layerStates:
    - cigar1
    - cigar2
    - cigar3
    - cigar4
    - cigar5
    - cigar6
    - cigar7
    - cigar8
  - type: Appearance

- type: entity
  id: CigarGoldCase
  parent: CigarCase
  name: "преміальний футляр для сигар"
  description: "Футляр першокласних гаванських сигар. З ними ви побачите тільки голови."
  components:
  - type: StorageFill
    contents:
    - id: CigarGold
      amount: 8

- type: entity 
  id: CigarRobustCase
  parent: CigarCase
  name: "Портсигар Cohiba Robusto"
  description: "Ящик імпортних сигар Cohiba, відомих своїм сильним ароматом."
  components:
  - type: Sprite
    sprite: Objects/Consumable/Smokeables/Cigars/case_robust.rsi
    layers:
    - state: closed
    - state: open
      map: ["openLayer"]
    - state: cigar1
      map: ["cigar1"]
      visible: false
    - state: cigar2
      map: ["cigar2"]
      visible: false
    - state: cigar3
      map: ["cigar3"]
      visible: false
    - state: cigar4
      map: ["cigar4"]
      visible: false
    - state: cigar5
      map: ["cigar5"]
      visible: false
  - type: Storage
    grid:
    - 0,1,4,1
  - type: Item
    sprite: Objects/Consumable/Smokeables/Cigars/case_robust.rsi
    size: Normal
    shape:
    - 0,0,2,1
    storedRotation: 90
  - type: StorageFill
    contents:
    - id: CigarRobust
      amount: 5
  - type: ItemCounter
    count:
      tags: [Cigar]
    composite: true
    layerStates:
    - cigar1
    - cigar2
    - cigar3
    - cigar4
    - cigar5