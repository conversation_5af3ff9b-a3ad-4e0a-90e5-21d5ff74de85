- type: entity
  abstract: true
  parent: BaseStructure
  id: BaseWall
  name: "цоколь"
  description: "Утримує повітря всередині та грейтайдів зовні."
  placement:
    mode: SnapgridCenter
    snap:
    - Wall
  components:
  - type: RangedDamageSound
    soundGroups:
      Brute:
        collection:
          MetalBulletImpact
    soundTypes:
      Heat:
        collection:
          MetalLaserImpact
  - type: Tag
    tags:
    - Wall
  - type: Sprite
    drawdepth: Walls
  - type: Icon
    state: full
  - type: PlacementReplacement
    key: walls
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: StructuralMetallic
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.5,-0.5,0.5,0.5"
        mask:
        - FullTileMask
        layer:
        - WallLayer
        density: 1000
  - type: Occluder
  - type: Airtight
  - type: StaticPrice #was DynamicPrice
    price: 75
  - type: RadiationBlocker
    resistance: 1
  - type: BlockWeather

- type: entity
  parent: BaseWall
  id: WallBrick
  name: "цегляна стіна"
  components:
  - type: Tag
    tags:
      - Wall
  - type: Sprite
    sprite: Structures/Walls/brick.rsi
  - type: Icon
    sprite: Structures/Walls/brick.rsi
  - type: RCDDeconstructable
    cost: 6
    delay: 8
    fx: EffectRCDDeconstruct8
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          Girder:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]

  - type: IconSmooth
    key: bricks
    base: brick
  - type: RustRequiresPathStage # Goobstation
    pathStage: 4 # Mark of rust

- type: entity
  parent: BaseWall
  id: WallClock
  name: "стіна з годинником"
  components:
  - type: Tag
    tags:
      - Wall
  - type: Sprite
    sprite: Structures/Walls/clock.rsi
  - type: Icon
    sprite: Structures/Walls/clock.rsi
  - type: Construction
    graph: ClockworkGirder
    node: clockworkWall
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          ClockworkGirder:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: IconSmooth
    key: walls
    base: clock
  - type: RustRequiresPathStage # Goobstation
    pathStage: 4 # Mark of rust

- type: entity
  parent: BaseWall
  id: WallClown
  name: "бананіумна стіна"
  components:
  - type: Tag
    tags:
      - Wall
  - type: Sprite
    sprite: Structures/Walls/clown.rsi
  - type: Icon
    sprite: Structures/Walls/clown.rsi
  - type: Construction
    graph: Girder
    node: bananiumWall
  - type: RCDDeconstructable
    cost: 6
    delay: 8
    fx: EffectRCDDeconstruct8
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          Girder:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: IconSmooth
    key: walls
    base: clown
  - type: RustRequiresPathStage # Goobstation
    pathStage: 4 # Mark of rust

- type: entity
  parent: BaseWall
  id: WallMeat
  name: "м'ясна стіна"
  description: "Липкий."
  components:
  - type: Tag
    tags:
      - Wall
      - Structure
  - type: Sprite
    sprite: Structures/Walls/meat.rsi
  - type: Icon
    sprite: Structures/Walls/meat.rsi
  - type: Construction
    graph: Girder
    node: meatWall
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100 # weak
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          Girder:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: IconSmooth
    key: walls
    base: meat

- type: entity
  parent: BaseWall
  id: WallCult
  name: "культова стіна"
  description: "Тримає культ всередині, а екіпаж зовні."
  components:
  - type: Tag
    tags:
      - Wall
  - type: Sprite
    sprite: Structures/Walls/cult.rsi
  - type: Icon
    sprite: Structures/Walls/cult.rsi
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          Girder:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: IconSmooth
    key: walls
    base: cult
  - type: Construction
    graph: CultGirder
    node: wall
  - type: RustRequiresPathStage # Goobstation
    pathStage: 4 # Mark of rust

- type: entity
  parent: BaseWall
  id: WallDebug
  name: "стіна налагодження"
  suffix: DEBUG
  components:
  - type: Tag
    tags:
      - Wall
      - Debug
  - type: Sprite
    sprite: Structures/Walls/debug.rsi
  - type: Icon
    sprite: Structures/Walls/debug.rsi
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          Girder:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: IconSmooth
    key: walls
    base: debug
  - type: RustRequiresPathStage # Goobstation
    pathStage: 4 # Mark of rust

- type: entity
  parent: BaseWall
  id: WallDiamond
  name: "діамантова стіна"
  components:
  - type: Tag
    tags:
      - Wall
  - type: Sprite
    sprite: Structures/Walls/diamond.rsi
  - type: Icon
    sprite: Structures/Walls/diamond.rsi
  - type: RCDDeconstructable
    cost: 6
    delay: 8
    fx: EffectRCDDeconstruct8
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          Girder:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: IconSmooth
    key: walls
    base: diamond
  - type: RustRequiresPathStage # Goobstation
    pathStage: 4 # Mark of rust

- type: entity
  parent: BaseWall
  id: WallGold
  name: "золота стіна"
  components:
  - type: Tag
    tags:
      - Wall
  - type: Sprite
    sprite: Structures/Walls/gold.rsi
  - type: Icon
    sprite: Structures/Walls/gold.rsi
  - type: Construction
    graph: Girder
    node: goldWall
  - type: RCDDeconstructable
    cost: 6
    delay: 8
    fx: EffectRCDDeconstruct8
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalSlam
      - !type:DoActsBehavior
        acts: ["Destruction"]
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalSlam
      - !type:ChangeConstructionNodeBehavior
        node: girder
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: IconSmooth
    key: walls
    base: gold

- type: entity
  parent: BaseWall
  id: WallIce
  name: "крижана стіна"
  components:
  - type: Tag
    tags:
      - Wall
  - type: Sprite
    sprite: Structures/Walls/ice.rsi
  - type: Icon
    sprite: Structures/Walls/ice.rsi
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          Girder:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: IconSmooth
    key: walls
    base: ice
  - type: RustRequiresPathStage # Goobstation
    pathStage: 4 # Mark of rust

- type: entity
  parent: BaseWall
  id: WallPlasma
  name: "плазмова стінка"
  components:
  - type: Tag
    tags:
      - Wall
  - type: Sprite
    sprite: Structures/Walls/plasma.rsi
  - type: Icon
    sprite: Structures/Walls/plasma.rsi
  - type: Construction
    graph: Girder
    node: plasmaWall
  - type: RCDDeconstructable
    cost: 6
    delay: 8
    fx: EffectRCDDeconstruct8
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalSlam
      - !type:DoActsBehavior
        acts: ["Destruction"]
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalSlam
      - !type:ChangeConstructionNodeBehavior
        node: girder
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: IconSmooth
    key: walls
    base: plasma
  - type: RadiationBlocker
    resistance: 7

- type: entity
  parent: BaseWall
  id: WallPlastic
  name: "пластикова стіна"
  components:
  - type: Tag
    tags:
      - Wall
  - type: Sprite
    sprite: Structures/Walls/plastic.rsi
  - type: Icon
    sprite: Structures/Walls/plastic.rsi
  - type: Construction
    graph: Girder
    node: plasticWall
  - type: RCDDeconstructable
    cost: 6
    delay: 8
    fx: EffectRCDDeconstruct8
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalSlam
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalSlam
      - !type:ChangeConstructionNodeBehavior
        node: girder
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: IconSmooth
    key: walls
    base: plastic

- type: entity
  parent: BaseWall
  id: WallPlastitaniumIndestructible
  name: "пластикова стіна"
  suffix: indestructible
  components:
    - type: Tag
      tags:
        - Wall
    - type: Sprite
      sprite: Structures/Walls/plastitanium.rsi
    - type: Icon
      sprite: Structures/Walls/plastitanium.rsi
    - type: IconSmooth
      key: walls
      base: plastitanium
    - type: RustRequiresPathStage # Goobstation
      pathStage: 11 # Can't rust

- type: entity
  parent: WallPlastitaniumIndestructible
  id: WallPlastitanium
  name: "пластикова стіна"
  suffix: ""
  components:
    - type: Tag
      tags:
        - Wall
    - type: Destructible
      thresholds:
        - trigger:
            !type:DamageTrigger
            damage: 1000
          behaviors:
            - !type:SpawnEntitiesBehavior
              spawn:
                Girder:
                  min: 1
                  max: 1
            - !type:DoActsBehavior
              acts: [ "Destruction" ]
    - type: RustRequiresPathStage # Goobstation
      pathStage: 10 # Ascension

- type: entity
  parent: WallShuttleDiagonal
  id: WallPlastitaniumDiagonal
  name: "пластикова стіна"
  suffix: diagonal
  placement:
    mode: SnapgridCenter
    snap:
    - Wall
  components:
  - type: Sprite
    drawdepth: Walls
    sprite: Structures/Walls/plastitanium_diagonal.rsi
    state: state0
  - type: IconSmooth
    mode: Diagonal
    key: walls
    base: state
  - type: Icon
    sprite: Structures/Walls/plastitanium_diagonal.rsi
    state: state0
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 2000
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
        - !type:PlaySoundBehavior
          sound:
            collection: MetalSlam
    - trigger:
        !type:DamageTrigger
        damage: 1000
      behaviors:
      - !type:ChangeConstructionNodeBehavior
        node: girder
      - !type:PlaySoundBehavior
        sound:
          collection: MetalSlam
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: RustRequiresPathStage # Goobstation
    pathStage: 10 # Ascension

- type: entity
  id: WallPlastitaniumDiagonalIndestructible
  name: "пластикова стіна"
  suffix: diagonal, indestructible
  description: "Утримує повітря всередині та грейтайдів зовні."
  placement:
    mode: SnapgridCenter
    snap:
    - Wall
  components:
  - type: Transform
    anchored: true
  - type: Clickable
  - type: Tag
    tags:
    - Wall
  - type: Sprite
    drawdepth: Walls
    sprite: Structures/Walls/plastitanium_diagonal.rsi
    state: state0
  - type: IconSmooth
    mode: Diagonal
    key: walls
    base: state
  - type: Icon
    sprite: Structures/Walls/plastitanium_diagonal.rsi
    state: state0
  - type: Physics
    bodyType: Static
  - type: Airtight
    noAirWhenFullyAirBlocked: false
    airBlockedDirection:
    - South
    - East
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PolygonShape
            vertices:
            - "-0.5,-0.5"
            - "0.5,0.5"
            - "0.5,-0.5"
        mask:
        - FullTileMask
        layer:
        - WallLayer
  - type: RustRequiresPathStage # Goobstation
    pathStage: 11 # Can't rust

- type: entity
  parent: BaseWall
  id: WallReinforced
  name: "посилена стіна"
  components:
  - type: Sprite
    sprite: Structures/Walls/solid.rsi
  - type: Icon
    sprite: Structures/Walls/solid.rsi
    state: rgeneric
  - type: Construction
    graph: Girder
    node: reinforcedWall
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: StructuralMetallicStrong
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 600
      behaviors:
      - !type:PlaySoundBehavior #Nyano
        sound:
          collection: MetalBreak
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalSlam
    - trigger:
        !type:DamageTrigger
        damage: 400
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalSlam
      - !type:ChangeConstructionNodeBehavior
        node: girder
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: IconSmooth
    key: walls
    base: reinf_over
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ReinforcedWallVisuals.DeconstructionStage:
        ReinforcedWallVisualLayers.Deconstruction:
          -1: { visible: false }
          0: { state: reinf_construct-0, visible: true}
          1: { state: reinf_construct-1, visible: true}
          2: { state: reinf_construct-2, visible: true}
          3: { state: reinf_construct-3, visible: true}
          4: { state: reinf_construct-4, visible: true}
          5: { state: reinf_construct-5, visible: true}
  - type: ReinforcedWallReplacementMarker
  - type: StaticPrice
    price: 150
  - type: RadiationBlocker
    resistance: 8
  - type: RustRequiresPathStage # Goobstation
    pathStage: 7 # Toxic blade

- type: entity
  parent: WallReinforced
  id: WallReinforcedRust
  name: "посилена стіна"
  suffix: rusted
  components:
  - type: Sprite
    sprite: Structures/Walls/solid_rust.rsi
  - type: Icon
    sprite: Structures/Walls/solid_rust.rsi
    state: rgeneric
  - type: Construction
    graph: Girder
    node: reinforcedWallRust
  - type: IconSmooth
    key: walls
    base: reinf_over
  - type: RustedWall # Goobstation
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 500
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalSlam
    - trigger:
        !type:DamageTrigger
        damage: 350
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalSlam
      - !type:ChangeConstructionNodeBehavior
        node: girder
      - !type:DoActsBehavior
        acts: ["Destruction"]


- type: entity
  parent: WallShuttleDiagonal
  id: WallReinforcedDiagonal
  name: "посилена стіна"
  suffix: diagonal
  placement:
    mode: SnapgridCenter
    snap:
    - Wall
  components:
  - type: Tag
    tags:
    - Wall # Goobstation
  - type: Sprite
    drawdepth: Walls
    sprite: Structures/Walls/reinforced_diagonal.rsi
    state: state0
  - type: IconSmooth
    mode: Diagonal
    key: walls
    base: state
  - type: Icon
    sprite: Structures/Walls/reinforced_diagonal.rsi
    state: state0
  - type: RustRequiresPathStage # Goobstation
    pathStage: 7 # Toxic blade

# Riveting
- type: entity
  parent: BaseWall
  id: WallRiveted
  name: "клепана стінка"
  components:
  - type: Tag
    tags:
      - Wall
  - type: Sprite
    sprite: Structures/Walls/riveted.rsi
  - type: Icon
    sprite: Structures/Walls/riveted.rsi
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: StructuralMetallicStrong
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 1000
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          Girder:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: IconSmooth
    key: walls
    base: riveted
  - type: StaticPrice #DynamicPrice
    price: 150
  - type: RustRequiresPathStage # Goobstation
    pathStage: 10 # Ascension

- type: entity
  parent: BaseWall
  id: WallSandstone
  name: "стіна з пісковику"
  components:
  - type: Tag
    tags:
      - Wall
  - type: Sprite
    sprite: Structures/Walls/sandstone.rsi
  - type: Icon
    sprite: Structures/Walls/sandstone.rsi
  - type: RCDDeconstructable
    cost: 6
    delay: 8
    fx: EffectRCDDeconstruct8
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawn:
          Girder:
            min: 1
            max: 1
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: IconSmooth
    key: walls
    base: sandstone

- type: entity
  parent: BaseWall
  id: WallSilver
  name: "срібна стіна"
  components:
  - type: Tag
    tags:
      - Wall
  - type: Sprite
    sprite: Structures/Walls/silver.rsi
  - type: Icon
    sprite: Structures/Walls/silver.rsi
  - type: Construction
    graph: Girder
    node: silverWall
  - type: RCDDeconstructable
    cost: 6
    delay: 8
    fx: EffectRCDDeconstruct8
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalSlam
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalSlam
      - !type:ChangeConstructionNodeBehavior
        node: girder
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: IconSmooth
    key: walls
    base: silver

- type: entity
  id: WallShuttleDiagonal
  name: "стіна шатла"
  suffix: diagonal
  description: "Утримує повітря всередині та грейтайдів зовні."
  placement:
    mode: SnapgridCenter
    snap:
    - Wall
  components:
  - type: Transform
    anchored: true
  - type: Clickable
  - type: Tag
    tags:
    - Wall
  - type: Sprite
    drawdepth: Walls
    sprite: Structures/Walls/shuttle_diagonal.rsi
    state: state0
  - type: IconSmooth
    mode: Diagonal
    key: walls
    base: state
  - type: Icon
    sprite: Structures/Walls/shuttle_diagonal.rsi
    state: state0
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: StructuralMetallic
  - type: Physics
    bodyType: Static
  - type: Pullable
  - type: Airtight
    noAirWhenFullyAirBlocked: false
    airBlockedDirection:
    - South
    - East
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PolygonShape
            vertices:
            - "-0.5,-0.5"
            - "0.5,0.5"
            - "0.5,-0.5"
        mask:
        - FullTileMask
        layer:
        - WallLayer
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 600
      behaviors:
        - !type:DoActsBehavior
          acts: [ "Destruction" ]
        - !type:PlaySoundBehavior
          sound:
            collection: MetalSlam
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalSlam
      - !type:DoActsBehavior
        acts: ["Destruction"]
    - trigger: #this trigger is a Nyano addition
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:ChangeConstructionNodeBehavior
        node: girder
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Construction
    graph: Girder
    node: diagonalshuttleWall
  - type: RustRequiresPathStage # Goobstation
    pathStage: 7 # Toxic blade

- type: entity
  parent: WallReinforced #Nyano, basically Reinforced Wall (shuttle variant)
  id: WallShuttle
  name: "стіна шатла"
  components:
  - type: Tag
    tags:
      - Wall
  - type: Sprite
    sprite: Structures/Walls/shuttle.rsi
  - type: Icon
    sprite: Structures/Walls/shuttle.rsi
    state: full
#  - type: Destructible
#    thresholds:
#    - trigger:
#        !type:DamageTrigger
#        damage: 1000
#      behaviors:
#        - !type:DoActsBehavior
#          acts: [ "Destruction" ]
#    - trigger:
#        !type:DamageTrigger
#        damage: 500
#      behaviors:
#      - !type:PlaySoundBehavior
#        sound:
#          path: /Audio/Effects/metalbreak.ogg
#      - !type:DoActsBehavior
#        acts: ["Destruction"]
#    destroySound:
#      path: /Audio/Effects/metalbreak.ogg
  - type: Construction
    graph: Girder
    node: shuttleWall
  - type: IconSmooth
    key: walls
    base: state
  - type: RustRequiresPathStage # Goobstation
    pathStage: 7 # Toxic blade

- type: entity
  parent: BaseWall
  id: WallSolid
  name: "суцільна стіна"
  components:
  - type: Tag
    tags:
      - Wall
  - type: Sprite
    sprite: Structures/Walls/solid.rsi
  - type: WallReplacementMarker
  - type: Construction
    graph: Girder
    node: wall
  - type: Icon
    sprite: Structures/Walls/solid.rsi
  - type: RCDDeconstructable
    cost: 6
    delay: 8
    fx: EffectRCDDeconstruct8
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 400
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalSlam
    - trigger:
        !type:DamageTrigger
        damage: 200
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalSlam
      - !type:ChangeConstructionNodeBehavior
        node: girder
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: IconSmooth
    key: walls
    base: solid

- type: entity
  parent: WallShuttleDiagonal
  id: WallSolidDiagonal
  name: "суцільна стіна"
  suffix: diagonal
  placement:
    mode: SnapgridCenter
    snap:
    - Wall
  components:
  - type: Sprite
    drawdepth: Walls
    sprite: Structures/Walls/solid_diagonal.rsi
    state: state0
  - type: IconSmooth
    mode: Diagonal
    key: walls
    base: state
  - type: Icon
    sprite: Structures/Walls/solid_diagonal.rsi
    state: state0

- type: entity
  parent: WallSolid
  id: WallSolidRust
  name: "суцільна стіна"
  suffix: rusted
  components:
  - type: Sprite
    sprite: Structures/Walls/solid_rust.rsi
  - type: Icon
    sprite: Structures/Walls/solid_rust.rsi
    state: full
  - type: Construction
    graph: Girder
    node: wallrust
  - type: IconSmooth
    key: walls
    base: solid
  - type: RustedWall # Goobstation

- type: entity
  parent: BaseWall
  id: WallUranium
  name: "уранова стіна"
  components:
  - type: Sprite
    sprite: Structures/Walls/uranium.rsi
  - type: Icon
    sprite: Structures/Walls/uranium.rsi
  - type: Construction
    graph: Girder
    node: uraniumWall
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalSlam
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalSlam
      - !type:ChangeConstructionNodeBehavior
        node: girder
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: IconSmooth
    key: walls
    base: uranium
  - type: RadiationBlocker
    resistance: 10

- type: entity
  parent: BaseWall
  id: WallWood
  name: "дерев'яна стіна"
  components:
  - type: Sprite
    sprite: Structures/Walls/wood.rsi
  - type: Icon
    sprite: Structures/Walls/wood.rsi
  - type: Construction
    graph: Girder
    node: woodWall
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 300
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroyHeavy
    - trigger:
        !type:DamageTrigger
        damage: 150
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroyHeavy
      - !type:ChangeConstructionNodeBehavior
        node: girder
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: IconSmooth
    key: woods
    base: wood

- type: entity
  parent: BaseWall
  id: WallWeb
  name: "павутинна стіна"
  description: "Досить слабка, але шовковисто-гладка стіна."
  components:
  - type: Clickable
  - type: MeleeSound
    soundGroups:
      Brute:
        path:
          "/Audio/Weapons/slash.ogg"
  - type: Damageable
    damageModifierSet: Web
  - type: Tag
    tags:
      - Wall
  - type: Sprite
    sprite: Structures/Walls/web.rsi
  - type: Icon
    sprite: Structures/Walls/web.rsi
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 30
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWebSilk:
            min: 1
            max: 1
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
  - type: IconSmooth
    key: webs
    base: wall
  - type: Construction
    graph: WebStructures
    node: wall


# Lavalend Walls

- type: entity
  parent: BaseWall
  id: WallNecropolis
  name: "кам'яна стіна"
  components:
  - type: Tag
    tags:
      - Wall
  - type: Sprite
    sprite: Structures/Walls/necropolis.rsi
  - type: Icon
    sprite: Structures/Walls/necropolis.rsi
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 1000
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: IconSmooth
    key: walls
    base: necropolis
  - type: RustRequiresPathStage # Goobstation
    pathStage: 10 # Ascension

- type: entity
  parent: BaseWall
  id: WallMining
  name: "стіна"
  components:
  - type: Tag
    tags:
      - Wall
  - type: Sprite
    sprite: Structures/Walls/mining.rsi
  - type: Icon
    sprite: Structures/Walls/mining.rsi
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 700
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: IconSmooth
    key: walls
    base: mining
  - type: RustRequiresPathStage # Goobstation
    pathStage: 7 # Toxic blade

- type: entity
  parent: WallShuttleDiagonal
  id: WallMiningDiagonal
  name: "стіна"
  suffix: diagonal
  placement:
    mode: SnapgridCenter
    snap:
    - Wall
  components:
  - type: Sprite
    drawdepth: Walls
    sprite: Structures/Walls/mining_diagonal.rsi
    state: state0
  - type: IconSmooth
    mode: Diagonal
    key: walls
    base: state
  - type: Icon
    sprite: Structures/Walls/mining_diagonal.rsi
    state: state0


# Vault Walls

- type: entity
  parent: BaseWall
  id: WallVaultAlien
  name: "стіна чужого сховища"
  description: "Таємнича витіювата стіна. Всередині може бути давня небезпека."
  components:
  - type: Sprite
    sprite: Structures/Walls/vault.rsi
    state: alienvault
  - type: Icon
    sprite: Structures/Walls/vault.rsi
    state: alienvault
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 500
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: RustRequiresPathStage # Goobstation
    pathStage: 7 # Toxic blade

- type: entity
  parent: WallVaultAlien
  id: WallVaultRock
  name: "стіна скельного склепіння"
  components:
  - type: Sprite
    sprite: Structures/Walls/vault.rsi
    state: rockvault
  - type: Icon
    sprite: Structures/Walls/vault.rsi
    state: rockvault

- type: entity
  parent: WallVaultAlien
  id: WallVaultSandstone
  name: "стіна склепіння з пісковику"
  components:
  - type: Sprite
    sprite: Structures/Walls/vault.rsi
    state: sandstonevault
  - type: Icon
    sprite: Structures/Walls/vault.rsi
    state: sandstonevault

# Mime

- type: entity
  id: WallInvisible
  name: "Невидима стіна"
  components:
  - type: TimedDespawn
    lifetime: 15
  - type: Tag
    tags:
      - Wall
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
            bounds: "-0.5,-0.5,0.5,0.5"
        mask:
        - FullTileMask
        layer:
        - GlassLayer
  - type: Airtight
  - type: RustRequiresPathStage # Goobstation
    pathStage: 11 # Can't rust

- type: entity
  id: WallForce
  name: "силова стіна"
  components:
    - type: TimedDespawn
      lifetime: 30 # Goob edit
    - type: Tag
      tags:
        - Wall
        - BlockLightning # Goobstation
    - type: Physics
      bodyType: Static
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.5,-0.5,0.5,0.5"
          mask:
            - FullTileMask
          layer: # Goob edit
            - Opaque
            - HighImpassable
            - MidImpassable
            - LowImpassable
            - BulletImpassable
    - type: Airtight
    - type: Sprite
      sprite: Structures/Magic/forcewall.rsi
      layers: #Goob
      - state: forcewall
        map: [ "base" ] #GOOB
    - type: Icon
      sprite: Structures/Magic/forcewall.rsi
      state: forcewall
#    - type: Dispellable
    - type: Clickable #Nyano
    # Goobstation start
    - type: SpawnAnimation
      animationLength: 1
    - type: Appearance
    - type: GenericVisualizer
      visuals:
        enum.SpawnAnimationVisuals.Spawned:
          base:
            True: { state: forcewall }
            False: { state: forcewallspawn }
    - type: RustRequiresPathStage
      pathStage: 11 # Can't rust
    - type: PreventCollide
      whitelist:
        components:
        - Wizard
        - Apprentice
    # Goobstation end

- type: entity
  parent: BaseWall
  id: WallCobblebrick
  name: "бруківка цегляна стіна"
  description: "Камінь за каменем, ідеально підігнані один до одного, щоб сформувати стіну."
  components:
  - type: Tag
    tags:
      - Wall
  - type: Sprite
    sprite: Structures/Walls/cobblebrick.rsi
  - type: Icon
    sprite: Structures/Walls/cobblebrick.rsi
  - type: RCDDeconstructable
    cost: 6
    delay: 8
    fx: EffectRCDDeconstruct8
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: IconSmooth
    key: cobblebricks
    base: cobblebrick

- type: entity
  parent: WallCobblebrick
  id: WallBasaltCobblebrick
  name: "стіна з базальтової цегли"
  components:
  - type: Sprite
    sprite: Structures/Walls/cobblebrick_basalt.rsi
  - type: Icon
    sprite: Structures/Walls/cobblebrick_basalt.rsi
  - type: IconSmooth
    key: cobblebricks
    base: cobblebrick

- type: entity
  parent: WallCobblebrick
  id: WallSnowCobblebrick
  name: "стіна зі снігової цегли"
  description: "Холодна, не така вже й непроникна стіна."
  components:
  - type: Sprite
    sprite: Structures/Walls/cobblebrick_snow.rsi
  - type: Icon
    sprite: Structures/Walls/cobblebrick_snow.rsi
  - type: IconSmooth
    key: cobblebricks
    base: cobblebrick

- type: entity
  parent: WallCobblebrick
  id: WallAsteroidCobblebrick
  name: "астероїд камінь цегляна стіна"
  components:
  - type: Sprite
    sprite: Structures/Walls/cobblebrick_asteroid.rsi
  - type: Icon
    sprite: Structures/Walls/cobblebrick_asteroid.rsi
  - type: IconSmooth
    key: cobblebricks
    base: cobblebrick

- type: entity
  parent: WallCobblebrick
  id: WallSandCobblebrick
  name: "цегляна стіна з пісковику"
  components:
  - type: Sprite
    sprite: Structures/Walls/cobblebrick_sand.rsi
  - type: Icon
    sprite: Structures/Walls/cobblebrick_sand.rsi
  - type: IconSmooth
    key: cobblebricks
    base: cobblebrick

- type: entity
  parent: WallCobblebrick
  id: WallChromiteCobblebrick
  name: "стіна з хромітової цегли"
  components:
  - type: Sprite
    sprite: Structures/Walls/cobblebrick_chromite.rsi
  - type: Icon
    sprite: Structures/Walls/cobblebrick_chromite.rsi
  - type: IconSmooth
    key: cobblebricks
    base: cobblebrick

- type: entity
  parent: WallCobblebrick
  id: WallAndesiteCobblebrick
  name: "стіна з андезитової цегли"
  components:
  - type: Sprite
    sprite: Structures/Walls/cobblebrick_andesite.rsi
  - type: Icon
    sprite: Structures/Walls/cobblebrick_andesite.rsi
  - type: IconSmooth
    key: cobblebricks
    base: cobblebrick

- type: entity
  parent: BaseWall
  id: WallResin
  name: "стіна-смола-назва"
  description: "стіна-смола-декор"
  components:
  - type: MeleeSound
    soundGroups:
      Brute:
        collection: blood
  - type: Tag
    tags:
    - Wall
  - type: Sprite
    sprite: Structures/Walls/resin.rsi
  - type: Icon
    sprite: Structures/Walls/resin.rsi
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
      - !type:PlaySoundBehavior
        sound:
          collection: gib
  - type: IconSmooth
    key: resinwall
    base: resinwall
