docking-component-dock = Dock
docking-component-undock = Undock

cmd-dock-desc = Attempts to dock 2 airlocks together. Doesn't check whether it is valid.
cmd-dock-help = dock <airlock entityuid1> <airlock entityuid2>

cmd-dock-args = Invalid number of args
cmd-dock-invalid = Invalid EntityUid {$entity}
cmd-dock-found = No docking component found on {$airlock}
cmd-dock-success = Successfully docked
cmd-dock-fail = Unable to dock
