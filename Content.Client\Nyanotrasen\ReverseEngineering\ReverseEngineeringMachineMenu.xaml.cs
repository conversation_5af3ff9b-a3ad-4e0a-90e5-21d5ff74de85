using Content.Client.UserInterface.Controls;
using Content.Shared.ReverseEngineering;
using Robust.Client.AutoGenerated;
using Robust.Client.GameObjects;
using Robust.Client.State;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Utility;

namespace Content.Client.Nyanotrasen.ReverseEngineering;

[GenerateTypedNameReferences]
public sealed partial class ReverseEngineeringMachineMenu : FancyWindow
{
    [Dependency] private readonly IEntityManager _ent = default!;
    public event Action<BaseButton.ButtonEventArgs>? OnScanButtonPressed;
    public event Action<BaseButton.ButtonToggledEventArgs>? OnSafetyButtonToggled;
    public event Action<BaseButton.ButtonToggledEventArgs>? OnAutoScanButtonToggled;
    public event Action<BaseButton.ButtonEventArgs>? OnStopButtonPressed;
    public event Action<BaseButton.ButtonEventArgs>? OnEjectButtonPressed;

    public ReverseEngineeringMachineMenu()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        ScanButton.OnPressed += a => OnScanButtonPressed?.Invoke(a);
        SafetyButton.OnToggled += a => OnSafetyButtonToggled?.Invoke(a);
        AutoScanButton.OnToggled += a => OnAutoScanButtonToggled?.Invoke(a);
        StopButton.OnPressed += a => OnStopButtonPressed?.Invoke(a);
        EjectButton.OnPressed += a => OnEjectButtonPressed?.Invoke(a);
    }


    public void SetButtonsDisabled(ReverseEngineeringMachineScanUpdateState state)
    {
        ScanButton.Disabled = !state.CanScan;
        StopButton.Disabled = !state.Scanning;
        SafetyButton.Pressed = state.Safety;
        AutoScanButton.Pressed = state.AutoProbe;
        EjectButton.Disabled = (state.Target == null || state.Scanning);
    }

    private void UpdateArtifactIcon(EntityUid? uid)
    {
        if (uid == null)
        {
            ItemDisplay.Visible = false;
            return;
        }
        ItemDisplay.Visible = true;

        ItemDisplay.SetEntity(uid);
    }

    public void UpdateInformationDisplay(ReverseEngineeringMachineScanUpdateState state)
    {
        var message = new FormattedMessage();
        _ent.TryGetEntity(state.Target, out var entityTarget);

        UpdateArtifactIcon(entityTarget);

        if (state.ScanReport == null)
        {
            if (!state.CanScan) //no item
                message.AddMarkup(Loc.GetString("analysis-console-info-no-artifact"));
            else if (state.Target == null) //ready to go
                message.AddMarkup(Loc.GetString("analysis-console-info-ready"));
        }
        else
        {
            message.AddMessage(state.ScanReport);
        }

        Information.SetMessage(message);
    }

    public void UpdateProbeTickProgressBar(ReverseEngineeringMachineScanUpdateState state)
    {
        ProgressBar.Visible = state.Scanning;
        ProgressLabel.Visible = state.Scanning;

        if (!state.Scanning)
            return;

        if (state.Target != null)
        {
            TotalProgressLabel.Visible = true;
            TotalProgressLabel.Text = Loc.GetString("reverse-engineering-total-progress-label");
            TotalProgressBar.Visible = true;
            TotalProgressBar.Value = (float) state.TotalProgress / 100f;
        } else
        {
            TotalProgressLabel.Visible = false;
            TotalProgressBar.Visible = false;
        }

        ProgressLabel.Text = Loc.GetString("analysis-console-progress-text",
            ("seconds", (int) state.TotalTime.TotalSeconds - (int) state.TimeRemaining.TotalSeconds));
        ProgressBar.Value = (float) state.TimeRemaining.Divide(state.TotalTime);
    }

    public override void Close()
    {
        base.Close();
    }
}

