# Base

- type: entity
  id: DisposalPipeBase
  abstract: true
  placement:
    mode: SnapgridCenter
    snap:
    - Disposal
  components:
  - type: Sprite
    visible: false
  - type: Appearance
  - type: SubFloorHide
  - type: Clickable
  - type: InteractionOutline
  - type: Physics
    bodyType: Static
    canCollide: false
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.5,0.25,0.5"
        mask:
        - SubfloorMask
  - type: Transform
    anchored: true
  - type: Anchorable
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100 #excess damage (nuke?). avoid computational cost of spawning entities.
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
      - !type:DoActsBehavior
        acts: ["Breakage"]
      - !type:ChangeConstructionNodeBehavior
        node: broken
  - type: Rotatable
  - type: Pullable
  - type: CollideOnAnchor
  - type: ContainerContainer
    containers:
      DisposalTube: !type:Container
  - type: Tag
    tags:
      - Disposal
  - type: StaticPrice
    price: 22

- type: entity
  id: DisposalHolder
  categories: [ HideSpawnMenu ]
  name: "утримувач майна"
  components:
  - type: DisposalHolder
  - type: ContainerContainer
    containers:
      DisposalHolderComponent: !type:Container

# Pipes

- type: entity
  id: DisposalPipeBroken
  parent: DisposalPipeBase
  name: "зламана каналізаційна труба"
  description: "BBP (велика розірвана труба)"
  components:
  - type: Sprite
    drawdepth: ThickPipe
    sprite: Structures/Piping/disposal.rsi
    layers:
    - map: [ "pipe" ]
      state: pipe-b
  - type: Construction
    graph: DisposalPipe
    node: broken

- type: entity
  id: DisposalPipe
  parent: DisposalPipeBase
  name: "каналізаційна труба"
  description: "Величезний сегмент труби, що використовується для побудови систем утилізації."
  components:
  - type: Sprite
    drawdepth: ThickPipe
    sprite: Structures/Piping/disposal.rsi
    layers:
    - map: [ "pipe" ]
      state: conpipe-s
  - type: DisposalTube
    containerId: DisposalTransit
  - type: DisposalTransit
  - type: ContainerContainer
    containers:
      DisposalTransit: !type:Container
  - type: GenericVisualizer
    visuals:
      enum.DisposalTubeVisuals.VisualState:
        pipe:
          Free: { state: conpipe-s }
          Anchored: { state: pipe-s }
  - type: Construction
    graph: DisposalPipe
    node: pipe

- type: entity
  id: DisposalTagger
  parent: DisposalPipeBase
  name: "маркувальник утилізації"
  description: "Труба, яка тегує сутності для маршрутизації."
  components:
  - type: Sprite
    drawdepth: ThickPipe
    sprite: Structures/Piping/disposal.rsi
    layers:
    - map: [ "pipe" ]
      state: conpipe-tagger
  - type: DisposalTube
    containerId: DisposalTagger
  - type: DisposalTagger
  - type: ContainerContainer
    containers:
      DisposalTagger: !type:Container
  - type: GenericVisualizer
    visuals:
      enum.DisposalTubeVisuals.VisualState:
        pipe:
          Free: { state: conpipe-tagger }
          Anchored: { state: pipe-tagger }
  - type: ActivatableUI
    key: enum.DisposalTaggerUiKey.Key
  - type: UserInterface
    interfaces:
      enum.DisposalTaggerUiKey.Key:
        type: DisposalTaggerBoundUserInterface
  - type: Construction
    graph: DisposalPipe
    node: tagger

- type: entity
  id: DisposalTrunk
  parent: DisposalPipeBase
  name: "сміттєвий бак"
  description: "Трубна магістраль, що використовується як точка входу для систем утилізації."
  components:
  - type: Sprite
    drawdepth: ThickPipe
    sprite: Structures/Piping/disposal.rsi
    layers:
    - map: [ "pipe" ]
      state: conpipe-t
  - type: DisposalTube
    containerId: DisposalEntry
  - type: DisposalEntry
  - type: ContainerContainer
    containers:
      DisposalEntry: !type:Container
  - type: GenericVisualizer
    visuals:
      enum.DisposalTubeVisuals.VisualState:
        pipe:
          Free: { state: conpipe-t }
          Anchored: { state: pipe-t }
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.5,0.25,0.4"
        mask:
        - SubfloorMask
  - type: Construction
    graph: DisposalPipe
    node: trunk

- type: entity
  id: DisposalRouter
  parent: DisposalPipeBase
  name: "утилізаційний маршрутизатор"
  description: "Тристоронній маршрутизатор. Сутності з відповідними тегами перенаправляються в сторону."
  components:
  - type: Sprite
    drawdepth: ThickPipe
    sprite: Structures/Piping/disposal.rsi
    layers:
    - map: [ "pipe" ]
      state: conpipe-j1s
  - type: DisposalTube
    containerId: DisposalRouter
  - type: DisposalRouter
    degrees:
    - 0
    - -90
    - 180
  - type: ContainerContainer
    containers:
      DisposalRouter: !type:Container
  - type: GenericVisualizer
    visuals:
      enum.DisposalTubeVisuals.VisualState:
        pipe:
          Free: { state: conpipe-j1s }
          Anchored: { state: pipe-j1s }
  - type: Flippable
    mirrorEntity: DisposalRouterFlipped
  - type: ActivatableUI
    key: enum.DisposalRouterUiKey.Key
  - type: UserInterface
    interfaces:
      enum.DisposalRouterUiKey.Key:
        type: DisposalRouterBoundUserInterface
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.5,-0.5,0.25,0.5"
        mask:
        - SubfloorMask
  - type: Construction
    graph: DisposalPipe
    node: router

- type: entity
  id: DisposalRouterFlipped
  description: "Тристоронній маршрутизатор. Сутності з відповідними тегами перенаправляються в сторону."
  parent: DisposalRouter
  suffix: flipped
  components:
  - type: Sprite
    drawdepth: ThickPipe
    sprite: Structures/Piping/disposal.rsi
    layers:
    - map: [ "pipe" ]
      state: conpipe-j2s
  - type: DisposalTube
    containerId: DisposalRouter
  - type: DisposalRouter
    degrees:
    - 0
    - 90
    - 180
  - type: ContainerContainer
    containers:
      DisposalRouter: !type:Container
  - type: GenericVisualizer
    visuals:
      enum.DisposalTubeVisuals.VisualState:
        pipe:
          Free: { state: conpipe-j2s }
          Anchored: { state: pipe-j2s }
  - type: Flippable
    mirrorEntity: DisposalRouter
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.5,0.5,0.5"
        mask:
        - SubfloorMask
  - type: Construction
    node: routerflipped

- type: entity
  id: DisposalJunction
  parent: DisposalPipeBase
  name: "утилізаційний вузол"
  description: "Тристоронній перехрестя. Стрілка вказує, куди виходять елементи."
  components:
  - type: Sprite
    drawdepth: ThickPipe
    sprite: Structures/Piping/disposal.rsi
    layers:
    - map: [ "pipe" ]
      state: conpipe-j1
    visible: true
  - type: DisposalTube
    containerId: DisposalJunction
  - type: DisposalJunction
    degrees:
    - 0
    - -90
    - 180
  - type: ContainerContainer
    containers:
      DisposalJunction: !type:Container
  - type: GenericVisualizer
    visuals:
      enum.DisposalTubeVisuals.VisualState:
        pipe:
          Free: { state: conpipe-j1 }
          Anchored: { state: pipe-j1 }
  - type: Flippable
    mirrorEntity: DisposalJunctionFlipped
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.5,-0.5,0.25,0.5"
        mask:
        - SubfloorMask
  - type: Construction
    graph: DisposalPipe
    node: junction

- type: entity
  id: DisposalJunctionFlipped
  description: "Тристоронній перехрестя. Стрілка вказує, куди виходять елементи."
  parent: DisposalJunction
  suffix: flipped
  components:
  - type: Sprite
    drawdepth: ThickPipe
    sprite: Structures/Piping/disposal.rsi
    layers:
    - map: [ "pipe" ]
      state: conpipe-j2
  - type: DisposalTube
    containerId: DisposalJunction
  - type: DisposalJunction
    degrees:
    - 0
    - 90
    - 180
  - type: ContainerContainer
    containers:
      DisposalJunction: !type:Container
  - type: GenericVisualizer
    visuals:
      enum.DisposalTubeVisuals.VisualState:
        pipe:
          Free: { state: conpipe-j2 }
          Anchored: { state: pipe-j2 }
  - type: Flippable
    mirrorEntity: DisposalJunction
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.5,0.5,0.5"
        mask:
        - SubfloorMask
  - type: Construction
    node: junctionflipped

- type: entity
  id: DisposalYJunction
  parent: DisposalPipeBase
  name: "утилізація Y-образного переходу"
  description: "Тристороння розв'язка з ще однією точкою виходу."
  components:
  - type: Sprite
    drawdepth: ThickPipe
    sprite: Structures/Piping/disposal.rsi
    layers:
    - map: [ "pipe" ]
      state: conpipe-y
  - type: DisposalTube
    containerId: DisposalJunction
  - type: DisposalJunction
    degrees:
    - 0
    - 90
    - -90
  - type: ContainerContainer
    containers:
      DisposalJunction: !type:Container
  - type: GenericVisualizer
    visuals:
      enum.DisposalTubeVisuals.VisualState:
        pipe:
          Free: { state: conpipe-y }
          Anchored: { state: pipe-y }
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.5,-0.5,0.5,0.25"
        mask:
        - SubfloorMask
  - type: Construction
    graph: DisposalPipe
    node: yJunction

- type: entity
  id: DisposalBend
  parent: DisposalPipeBase
  name: "утилізаційний вигин"
  description: "Трубка, зігнута під кутом 90 градусів."
  components:
  - type: Sprite
    drawdepth: ThickPipe
    sprite: Structures/Piping/disposal.rsi
    layers:
    - map: [ "pipe" ]
      state: conpipe-c
  - type: DisposalTube
    containerId: DisposalBend
  - type: DisposalBend
  - type: ContainerContainer
    containers:
      DisposalBend: !type:Container
  - type: GenericVisualizer
    visuals:
      enum.DisposalTubeVisuals.VisualState:
        pipe:
          Free: { state: conpipe-c }
          Anchored: { state: pipe-c }
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.5,-0.5,0.25,0.25"
        mask:
        - SubfloorMask
  - type: Construction
    graph: DisposalPipe
    node: bend

- type: entity
  parent: DisposalJunction
  id: DisposalSignalRouter
  name: "маршрутизатор сигналу утилізації"
  description: "Тристоронній маршрутизатор, керований сигналом."
  components:
  - type: Sprite
    drawdepth: ThickPipe
    layers:
    - map: [ "pipe" ]
      state: signal-router-free
  - type: DisposalTube
    containerId: DisposalSignalRouter
  - type: DisposalSignalRouter
  - type: DeviceLinkSink
    ports:
    - On
    - Off
    - Toggle
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: BasicDevice
  - type: WirelessNetworkConnection
    range: 200
  - type: ContainerContainer
    containers:
      DisposalSignalRouter: !type:Container
  - type: GenericVisualizer
    visuals:
      enum.DisposalTubeVisuals.VisualState:
        pipe:
          Free: { state: signal-router-free }
          Anchored: { state: signal-router }
  - type: Flippable
    mirrorEntity: DisposalSignalRouterFlipped
  - type: Construction
    graph: DisposalPipe
    node: signal_router

- type: entity
  parent: DisposalSignalRouter
  id: DisposalSignalRouterFlipped
  suffix: flipped
  components:
  - type: Sprite
    layers:
    - map: [ "pipe" ]
      state: signal-router-flipped-free
  - type: DisposalJunction
    degrees:
    - 0
    - 90
    - 180
  - type: GenericVisualizer
    visuals:
      enum.DisposalTubeVisuals.VisualState:
        pipe:
          Free: { state: signal-router-flipped-free }
          Anchored: { state: signal-router-flipped }
  - type: Construction
    node: signal_router_flipped

