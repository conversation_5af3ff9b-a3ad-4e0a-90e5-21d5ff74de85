using Robust.Shared.GameStates;
using Robust.Shared.Serialization;

namespace Content.Shared._Pirate.DNDMage;

/// <summary>
/// Shared system for handling DNDMageSavingThrowComponent.
/// </summary>
public abstract class SharedDNDMageSavingThrowSystem : EntitySystem
{
    public override void Initialize()
    {
        base.Initialize();
        // Підписки перенесені в серверну систему
    }


}

[Serializable, NetSerializable]
public sealed class DNDMageSavingThrowComponentState : ComponentState
{
    public bool SavingThrowEnabled;
    public TimeSpan LastSavingThrowTime;
    public TimeSpan SavingThrowCooldown;

    public DNDMageSavingThrowComponentState(bool savingThrowEnabled, TimeSpan lastSavingThrowTime, TimeSpan savingThrowCooldown)
    {
        SavingThrowEnabled = savingThrowEnabled;
        LastSavingThrowTime = lastSavingThrowTime;
        SavingThrowCooldown = savingThrowCooldown;
    }
}
