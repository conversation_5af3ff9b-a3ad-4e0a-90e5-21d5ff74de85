- type: species
  id: Diona
  name: species-name-diona
  roundStart: true
  prototype: MobDiona
  sprites: MobDionaSprites
  defaultSkinTone: "#cdb369"
  markingLimits: MobDionaMarkingLimits
  dollPrototype: MobDionaDummy
  skinColoration: <PERSON><PERSON>
  maleFirstNames: <PERSON>a<PERSON><PERSON><PERSON>
  femaleFirstNames: <PERSON>a<PERSON><PERSON><PERSON>
  lastNames: DionaLast
  naming: TheFirstofLast

- type: speciesBaseSprites
  id: MobDionaSprites
  sprites:
    Head: MobDionaHead
    HeadTop: MobHumanoidAnyMarking
    HeadSide: MobHumanoidAnyMarking
    Underwear: MobHumanoidAnyMarking
    Undershirt: MobHumanoidAnyMarking
    Chest: MobDionaTorso
    Eyes: MobDionaEyes
    LArm: MobDionaLArm
    RArm: MobDionaRArm
    LHand: MobDionaLHand
    RHand: MobDionaRHand
    LLeg: MobDionaLLeg
    RLeg: MobDionaRLeg
    LFoot: MobDionaLFoot
    RFoot: MobDionaRFoot

- type: markingPoints
  id: MobDionaMarkingLimits
  onlyWhitelisted: true
  points:
    Head:
      points: 5
      required: false
    HeadTop:
      points: 1
      required: false
    HeadSide:
      points: 6
      required: false
    Chest:
      points: 6
      required: false
    Underwear:
      points: 1
      required: false
    Undershirt:
      points: 1
      required: false
    RightLeg:
      points: 6
      required: false
    RightFoot:
      points: 6
      required: false
    LeftLeg:
      points: 6
      required: false
    LeftFoot:
      points: 6
      required: false
    RightArm:
      points: 6
      required: false
    RightHand:
      points: 6
      required: false
    LeftArm:
      points: 6
      required: false
    LeftHand:
      points: 6
      required: false
    Overlay:
      points: 1
      required: true
      defaultMarkings: [ DionaVineOverlay ]

- type: humanoidBaseSprite
  id: MobDionaEyes
  baseSprite:
    sprite: Mobs/Customization/eyes.rsi
    state: diona

- type: humanoidBaseSprite
  id: MobDionaHead
  baseSprite:
    sprite: Mobs/Species/Diona/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobDionaHeadMale
  baseSprite:
    sprite: Mobs/Species/Diona/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobDionaHeadFemale
  baseSprite:
    sprite: Mobs/Species/Diona/parts.rsi
    state: head_f

- type: humanoidBaseSprite
  id: MobDionaTorso
  baseSprite:
    sprite: Mobs/Species/Diona/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobDionaTorsoMale
  baseSprite:
    sprite: Mobs/Species/Diona/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobDionaTorsoFemale
  baseSprite:
    sprite: Mobs/Species/Diona/parts.rsi
    state: torso_f

- type: humanoidBaseSprite
  id: MobDionaLLeg
  baseSprite:
    sprite: Mobs/Species/Diona/parts.rsi
    state: l_leg

- type: humanoidBaseSprite
  id: MobDionaLHand
  baseSprite:
    sprite: Mobs/Species/Diona/parts.rsi
    state: l_hand

- type: humanoidBaseSprite
  id: MobDionaLArm
  baseSprite:
    sprite: Mobs/Species/Diona/parts.rsi
    state: l_arm

- type: humanoidBaseSprite
  id: MobDionaLFoot
  baseSprite:
    sprite: Mobs/Species/Diona/parts.rsi
    state: l_foot

- type: humanoidBaseSprite
  id: MobDionaRLeg
  baseSprite:
    sprite: Mobs/Species/Diona/parts.rsi
    state: r_leg

- type: humanoidBaseSprite
  id: MobDionaRHand
  baseSprite:
    sprite: Mobs/Species/Diona/parts.rsi
    state: r_hand

- type: humanoidBaseSprite
  id: MobDionaRArm
  baseSprite:
    sprite: Mobs/Species/Diona/parts.rsi
    state: r_arm

- type: humanoidBaseSprite
  id: MobDionaRFoot
  baseSprite:
    sprite: Mobs/Species/Diona/parts.rsi
    state: r_foot
