- type: stack
  id: Glass
  name: "скло"
  icon: { sprite: /Textures/Objects/Materials/Sheets/glass.rsi, state: glass }
  spawn: SheetGlass1
  maxCount: 30

- type: stack
  id: ReinforcedGlass
  name: "армоване скло"
  icon: { sprite: /Textures/Objects/Materials/Sheets/glass.rsi, state: rglass }
  spawn: SheetRGlass1
  maxCount: 30

- type: stack
  id: PlasmaGlass
  name: "плазмове скло"
  icon: { sprite: /Textures/Objects/Materials/Sheets/glass.rsi, state: pglass }
  spawn: SheetPGlass1
  maxCount: 30

- type: stack
  id: ReinforcedPlasmaGlass
  name: "армоване плазмове скло"
  icon: { sprite: /Textures/Objects/Materials/Sheets/glass.rsi, state: rpglass }
  spawn: SheetRPGlass1
  maxCount: 30

- type: stack
  id: UraniumGlass
  name: "уранове скло"
  icon: { sprite: /Textures/Objects/Materials/Sheets/glass.rsi, state: uglass }
  spawn: SheetUGlass1
  maxCount: 30

- type: stack
  id: ReinforcedUraniumGlass
  name: "армоване уранове скло"
  icon: { sprite: /Textures/Objects/Materials/Sheets/glass.rsi, state: ruglass }
  spawn: SheetRUGlass1
  maxCount: 30

- type: stack
  id: ClockworkGlass
  name: "годинникове скло"
  icon: { sprite: /Textures/Objects/Materials/Sheets/glass.rsi, state: cglass }
  spawn: SheetClockworkGlass1
  maxCount: 30
