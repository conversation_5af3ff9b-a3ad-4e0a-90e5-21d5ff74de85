#chairs
- type: construction
  name: "крісло"
  id: Chair
  graph: Seat
  startNode: start
  targetNode: chair
  category: construction-category-furniture
  description: "Ти сидиш у цьому. Чи то з волі, чи то з примусу."
  icon:
    sprite: Structures/Furniture/chairs.rsi
    state: chair
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "табурет"
  id: Stool
  graph: Seat
  startNode: start
  targetNode: stool
  category: construction-category-furniture
  description: "Ти сидиш у цьому. Чи то з волі, чи то з примусу."
  icon:
    sprite: Structures/Furniture/chairs.rsi
    state: stool
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "барний стілець"
  id: StoolBar
  graph: Seat
  startNode: start
  targetNode: stoolBar
  category: construction-category-furniture
  description: "Ти сидиш у цьому. Чи то з волі, чи то з примусу."
  icon:
    sprite: Structures/Furniture/chairs.rsi
    state: bar
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "латунний стілець"
  id: ChairBrass
  graph: Seat
  startNode: start
  targetNode: chairBrass
  category: construction-category-furniture
  description: "Ти сидиш у цьому. Чи то з волі, чи то з примусу."
  icon:
    sprite: Structures/Furniture/chairs.rsi
    state: brass_chair
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "офісне крісло"
  id: ChairOfficeLight
  graph: Seat
  startNode: start
  targetNode: chairOffice
  category: construction-category-furniture
  description: "Ти сидиш у цьому. Чи то з волі, чи то з примусу."
  icon:
    sprite: Structures/Furniture/chairs.rsi
    state: office-white
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "темне офісне крісло"
  id: ChairOfficeDark
  graph: Seat
  startNode: start
  targetNode: chairOfficeDark
  category: construction-category-furniture
  description: "Ти сидиш у цьому. Чи то з волі, чи то з примусу."
  icon:
    sprite: Structures/Furniture/chairs.rsi
    state: office-dark
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "зручне крісло"
  id: ChairComfy
  graph: Seat
  startNode: start
  targetNode: chairComfy
  category: construction-category-furniture
  description: "Виглядає справді зручно."
  icon:
    sprite: Structures/Furniture/chairs.rsi
    state: comfy
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "крісло пілота"
  id: chairPilotSeat
  graph: Seat
  startNode: start
  targetNode: chairPilotSeat
  category: construction-category-furniture
  description: "Підходить для капітана."
  icon:
    sprite: Structures/Furniture/chairs.rsi
    state: shuttle
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "дерев'яний стілець"
  id: ChairWood
  graph: Seat
  startNode: start
  targetNode: chairWood
  category: construction-category-furniture
  description: "Ти сидиш у цьому. Чи то з волі, чи то з примусу."
  icon:
    sprite: Structures/Furniture/chairs.rsi
    state: wooden
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "м'ясний стілець"
  id: ChairMeat
  graph: Seat
  startNode: start
  targetNode: chairMeat
  category: construction-category-furniture
  description: "Неприємно спітніла."
  icon:
    sprite: Structures/Furniture/chairs.rsi
    state: meat
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked

- type: construction
  name: "ритуальний стілець"
  id: ChairRitual
  graph: RitualSeat
  startNode: start
  targetNode: chairRitual
  category: construction-category-furniture
  description: "Дивно вирізьблений стілець."
  icon:
    sprite: Structures/Furniture/chairs.rsi
    state: ritual
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "розкладний стілець"
  id: ChairFolding
  graph: Seat
  startNode: start
  targetNode: chairFolding
  category: construction-category-furniture
  description: "Стілець, який легко переносити."
  icon:
    sprite: Structures/Furniture/folding_chair.rsi
    state: folding
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "сталева лавка"
  id: ChairSteelBench
  graph: Seat
  startNode: start
  targetNode: chairSteelBench
  category: construction-category-furniture
  description: "Довгий стілець, зроблений для метро. Дійсно стандартний дизайн."
  icon:
    sprite: Structures/Furniture/chairs.rsi
    state: steel-bench
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "дерев'яна лавка"
  id: ChairWoodBench
  graph: Seat
  startNode: start
  targetNode: chairWoodBench
  category: construction-category-furniture
  description: "Загнали скалку? Ну, принаймні це екологічно."
  icon:
    sprite: Structures/Furniture/chairs.rsi
    state: wooden-bench
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "зручна червона лавка"
  id: RedComfBench
  graph: Seat
  startNode: start
  targetNode: redComfBench
  category: construction-category-furniture
  description: "Лавка з надзвичайно зручною спинкою."
  icon:
    sprite: Structures/Furniture/Bench/comf_bench.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "зручна синя лавка"
  id: BlueComfBench
  graph: Seat
  startNode: start
  targetNode: blueComfBench
  category: construction-category-furniture
  description: "Лавка з надзвичайно зручною спинкою."
  icon:
    sprite: Structures/Furniture/Bench/comf_bench.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

#tables
- type: construction
  name: "сталевий стіл"
  id: Table
  graph: Table
  startNode: start
  targetNode: Table
  category: construction-category-furniture
  description: "Квадратний шматок металу, що стоїть на чотирьох металевих ніжках."
  icon:
    sprite: Structures/Furniture/Tables/generic.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "посилений сталевий стіл"
  id: TableReinforced
  graph: Table
  startNode: start
  targetNode: TableReinforced
  category: construction-category-furniture
  description: "Квадратний шматок металу, що стоїть на чотирьох металевих ніжках. Дуже міцний."
  icon:
    sprite: Structures/Furniture/Tables/reinforced.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "скляний стіл"
  id: TableGlass
  graph: Table
  startNode: start
  targetNode: TableGlass
  category: construction-category-furniture
  description: "Квадратний шматок скла, що стоїть на чотирьох металевих ніжках."
  icon:
    sprite: Structures/Furniture/Tables/glass.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "стіл з армованого скла"
  id: TableReinforcedGlass
  graph: Table
  startNode: start
  targetNode: TableReinforcedGlass
  category: construction-category-furniture
  description: "Квадратний шматок скла, що стоїть на чотирьох металевих ніжках. Дуже міцний."
  icon:
    sprite: Structures/Furniture/Tables/r_glass.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "стіл із плазмового скла"
  id: TablePlasmaGlass
  graph: Table
  startNode: start
  targetNode: TablePlasmaGlass
  category: construction-category-furniture
  description: "Квадратний шматок плазмового скла, що стоїть на чотирьох металевих ніжках. Гарно!"
  icon:
    sprite: Structures/Furniture/Tables/plasma.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "латунний стіл"
  id: TableBrass
  graph: Table
  startNode: start
  targetNode: TableBrass
  category: construction-category-furniture
  description: "Блискучий, стійкий до корозії латунний стіл. Стимпанк!"
  icon:
    sprite: Structures/Furniture/Tables/brass.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "дерев'яний стіл"
  id: TableWood
  graph: Table
  startNode: start
  targetNode: TableWood
  category: construction-category-furniture
  description: "Не підносьте до нього вогонь. Подейкують, що він легко горить."
  icon:
    sprite: Structures/Furniture/Tables/wood.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "покерний стіл"
  id: TableCarpet
  graph: Table
  startNode: start
  targetNode: TableCarpet
  category: construction-category-furniture
  description: "Квадратний шматок дерева, що стоїть на чотирьох ніжках, вкритий тканиною. (Чого ви очікували?)"
  icon:
    sprite: Structures/Furniture/Tables/carpet.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "вишуканий чорний стіл"
  id: TableFancyBlack
  graph: Table
  startNode: start
  targetNode: TableFancyBlack
  category: construction-category-furniture
  description: "Стіл, покритий красивою тканиною."
  icon:
    sprite: Structures/Furniture/Tables/Fancy/black.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "вишуканий синій стіл"
  id: TableFancyBlue
  graph: Table
  startNode: start
  targetNode: TableFancyBlue
  category: construction-category-furniture
  description: "Стіл, покритий красивою тканиною."
  icon:
    sprite: Structures/Furniture/Tables/Fancy/blue.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "вишуканий блакитний стіл"
  id: TableFancyCyan
  graph: Table
  startNode: start
  targetNode: TableFancyCyan
  category: construction-category-furniture
  description: "Стіл, покритий красивою тканиною."
  icon:
    sprite: Structures/Furniture/Tables/Fancy/cyan.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "вишуканий зелений стіл"
  id: TableFancyGreen
  graph: Table
  startNode: start
  targetNode: TableFancyGreen
  category: construction-category-furniture
  description: "Стіл, покритий красивою тканиною."
  icon:
    sprite: Structures/Furniture/Tables/Fancy/green.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "вишуканий помаранчевий стіл"
  id: TableFancyOrange
  graph: Table
  startNode: start
  targetNode: TableFancyOrange
  category: construction-category-furniture
  description: "Стіл, покритий красивою тканиною."
  icon:
    sprite: Structures/Furniture/Tables/Fancy/orange.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "вишуканий фіолетовий стіл"
  id: TableFancyPurple
  graph: Table
  startNode: start
  targetNode: TableFancyPurple
  category: construction-category-furniture
  description: "Стіл, покритий красивою тканиною."
  icon:
    sprite: Structures/Furniture/Tables/Fancy/purple.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "вишуканий рожевий стіл"
  id: TableFancyPink
  graph: Table
  startNode: start
  targetNode: TableFancyPink
  category: construction-category-furniture
  description: "Стіл, покритий красивою тканиною."
  icon:
    sprite: Structures/Furniture/Tables/Fancy/pink.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "вишуканий червоний стіл"
  id: TableFancyRed
  graph: Table
  startNode: start
  targetNode: TableFancyRed
  category: construction-category-furniture
  description: "Стіл, покритий красивою тканиною."
  icon:
    sprite: Structures/Furniture/Tables/Fancy/red.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "вишуканий білий стіл"
  id: TableFancyWhite
  graph: Table
  startNode: start
  targetNode: TableFancyWhite
  category: construction-category-furniture
  description: "Стіл, покритий красивою тканиною."
  icon:
    sprite: Structures/Furniture/Tables/Fancy/white.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "металевий прилавок"
  id: TableCounterMetal
  graph: Table
  startNode: start
  targetNode: CounterMetal
  category: construction-category-furniture
  description: "Виглядає як гарне місце, щоб поставити випивку."
  icon:
    sprite: Structures/Furniture/Tables/countermetal.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "дерев'яний прилавок"
  id: TableCounterWood
  graph: Table
  startNode: start
  targetNode: CounterWood
  category: construction-category-furniture
  description: "Не підносьте до нього вогонь. Подейкують, що він легко горить."
  icon:
    sprite: Structures/Furniture/Tables/counterwood.rsi
    state: full
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

#bathroom
- type: construction
  name: "душ"
  id: Shower
  graph: Shower
  startNode: start
  targetNode: shower
  category: construction-category-furniture
  description: "Душ, укомплектований водою з блакитного простору."
  icon:
    sprite: Structures/Furniture/shower.rsi
    state: shower
    objectType: Structure
  placementMode: SnapgridCenter
  canRotate: true
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  name: "туалет"
  id: ToiletEmpty
  graph: Toilet
  startNode: start
  targetNode: toilet
  category: construction-category-furniture
  description: "Пристрій для змивання людських екскрементів."
  icon:
    sprite: Structures/Furniture/toilet.rsi
    state: disposal
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

#medical
- type: construction
  id: MedicalBed
  name: "медичне ліжко"
  description: "Лікарняне ліжко для одужання пацієнтів. Відпочинок тут забезпечує досить повільне одужання."
  graph: bed
  startNode: start
  targetNode: medicalbed
  category: construction-category-furniture
  icon:
    sprite: Structures/Furniture/furniture.rsi
    state: bed-MED
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  id: OperatingTable
  name: "операційний стіл"
  description: "Використовується для складних медичних процедур."
  graph: bed
  startNode: start
  targetNode: operatingtable
  category: construction-category-furniture
  icon:
    sprite: Structures/Furniture/Tables/optable.rsi
    state: operating_table
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

#bedroom
- type: construction
  id: Bed
  name: "ліжко"
  description: "На ньому можна лежати, спати або прив'язуватися. Відпочинок тут забезпечує надзвичайно повільне загоєння."
  graph: bed
  startNode: start
  targetNode: bed
  category: construction-category-furniture
  icon:
    sprite: Structures/Furniture/furniture.rsi
    state: bed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  id: DogBed
  name: "собача лежанка"
  description: "Зручний на вигляд лежак для собак. Ви навіть можете прив'язати свого улюбленця на випадок, якщо гравітація вимкнеться."
  graph: bed
  startNode: start
  targetNode: dogbed
  category: construction-category-furniture
  icon:
    sprite: Structures/Furniture/furniture.rsi
    state: dogbed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  id: Dresser
  name: "комод"
  description: "Дерев'яний комод, може зберігати речі всередині себе."
  graph: Dresser
  startNode: start
  targetNode: dresser
  category: construction-category-furniture
  icon:
    sprite: Structures/Furniture/furniture.rsi
    state: dresser
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

#racks
- type: construction
  id: Rack
  name: "стійка"
  description: "Стійка для зберігання речей."
  graph: Rack
  startNode: start
  targetNode: Rack
  category: construction-category-furniture
  icon:
    sprite: Structures/Furniture/furniture.rsi
    state: rack
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

#misc
- type: construction
  id: MeatSpike
  name: "м'ясний шип"
  description: "Шип, який можна знайти на кухнях, де обробляють тварин."
  graph: MeatSpike
  startNode: start
  targetNode: MeatSpike
  category: construction-category-furniture
  icon:
    sprite: Structures/meat_spike.rsi
    state: spike
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  id: Curtains
  name: "штори"
  description: "Містить менше 1% ртуті."
  graph: Curtains
  startNode: start
  targetNode: Curtains
  category: construction-category-furniture
  icon:
    sprite: Structures/Decoration/Curtains/hospital.rsi
    state: closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true

- type: construction
  id: CurtainsBlack
  name: "чорні штори"
  description: "Приховує те, що інші не повинні бачити."
  graph: Curtains
  startNode: start
  targetNode: CurtainsBlack
  category: construction-category-furniture
  icon:
    sprite: Structures/Decoration/Curtains/black.rsi
    state: closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true

- type: construction
  id: CurtainsBlue
  name: "сині штори"
  description: "Приховує те, що інші не повинні бачити."
  graph: Curtains
  startNode: start
  targetNode: CurtainsBlue
  category: construction-category-furniture
  icon:
    sprite: Structures/Decoration/Curtains/blue.rsi
    state: closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true

- type: construction
  id: CurtainsCyan
  name: "блакитні штори"
  description: "Приховує те, що інші не повинні бачити."
  graph: Curtains
  startNode: start
  targetNode: CurtainsCyan
  category: construction-category-furniture
  icon:
    sprite: Structures/Decoration/Curtains/cyan.rsi
    state: closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true

- type: construction
  id: CurtainsGreen
  name: "зелені штори"
  description: "Приховує те, що інші не повинні бачити."
  graph: Curtains
  startNode: start
  targetNode: CurtainsGreen
  category: construction-category-furniture
  icon:
    sprite: Structures/Decoration/Curtains/green.rsi
    state: closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true

- type: construction
  id: CurtainsOrange
  name: "помаранчеві штори"
  description: "Приховує те, що інші не повинні бачити."
  graph: Curtains
  startNode: start
  targetNode: CurtainsOrange
  category: construction-category-furniture
  icon:
    sprite: Structures/Decoration/Curtains/orange.rsi
    state: closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true

- type: construction
  id: CurtainsPink
  name: "рожеві штори"
  description: "Приховує те, що інші не повинні бачити."
  graph: Curtains
  startNode: start
  targetNode: CurtainsPink
  category: construction-category-furniture
  icon:
    sprite: Structures/Decoration/Curtains/pink.rsi
    state: closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true

- type: construction
  id: CurtainsPurple
  name: "фіолетові штори"
  description: "Приховує те, що інші не повинні бачити."
  graph: Curtains
  startNode: start
  targetNode: CurtainsPurple
  category: construction-category-furniture
  icon:
    sprite: Structures/Decoration/Curtains/purple.rsi
    state: closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true

- type: construction
  id: CurtainsRed
  name: "червоні штори"
  description: "Приховує те, що інші не повинні бачити."
  graph: Curtains
  startNode: start
  targetNode: CurtainsRed
  category: construction-category-furniture
  icon:
    sprite: Structures/Decoration/Curtains/red.rsi
    state: closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true

- type: construction
  id: CurtainsWhite
  name: "білі штори"
  description: "Приховує те, що інші не повинні бачити."
  graph: Curtains
  startNode: start
  targetNode: CurtainsWhite
  category: construction-category-furniture
  icon:
    sprite: Structures/Decoration/Curtains/white.rsi
    state: closed
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: true

- type: construction
  id: Bookshelf
  name: "книжкова полиця"
  description: "Здебільшого заповнена книжками."
  graph: Bookshelf
  startNode: start
  targetNode: bookshelf
  category: construction-category-furniture
  icon:
    sprite: Structures/Furniture/bookshelf.rsi
    state: base
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked

- type: construction
  id: NoticeBoard
  name: "дошка оголошень"
  description: "Дерев'яна дошка для оголошень, може зберігати папір всередині себе."
  graph: NoticeBoard
  startNode: start
  targetNode: noticeBoard
  category: construction-category-furniture
  icon:
    sprite: Structures/Wallmounts/noticeboard.rsi
    state: noticeboard
  objectType: Structure
  placementMode: SnapgridCenter
  canRotate: true
  canBuildInImpassable: false
  conditions:
    - !type:TileNotBlocked
