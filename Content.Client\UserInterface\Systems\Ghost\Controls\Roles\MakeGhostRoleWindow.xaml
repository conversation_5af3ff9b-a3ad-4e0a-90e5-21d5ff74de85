﻿<DefaultWindow Title="{Loc Make Ghost Role}"
            xmlns="https://spacestation14.io">

    <BoxContainer Orientation="Vertical">
        <BoxContainer Orientation="Horizontal">
            <Label Name="RoleEntityLabel" Text="Entity" />
            <Label Name="RoleEntity" Text="" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal">
            <Label Name="RoleNameLabel" Text="Role Name" />
            <LineEdit Name="RoleName" HorizontalExpand="True" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal">
            <Label Name="RoleDescriptionLabel" Text="Role Description" />
            <LineEdit Name="RoleDescription" HorizontalExpand="True" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal">
            <Label Name="RoleRulesLabel" Text="Role Rules" />
            <LineEdit Name="RoleRules" HorizontalExpand="True" Text="{Loc ghost-role-component-default-rules}" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal">
            <Label Name="MakeSentientLabel" Text="Make Sentient" />
            <CheckBox Name="MakeSentientCheckbox" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal">
            <Label Name="RaffleLabel" Text="Raffle Role?" />
            <OptionButton Name="RaffleButton" />
        </BoxContainer>
        <BoxContainer Name="RaffleCustomSettingsContainer" Orientation="Vertical" Visible="False">
            <BoxContainer Orientation="Horizontal">
                <Label Name="RaffleInitialDurationLabel" Text="Initial Duration (s)" />
                <SpinBox Name="RaffleInitialDuration" HorizontalExpand="True" />
            </BoxContainer>
            <BoxContainer Orientation="Horizontal">
                <Label Name="RaffleJoinExtendsDurationByLabel" Text="Joins Extend By (s)" />
                <SpinBox Name="RaffleJoinExtendsDurationBy" HorizontalExpand="True" />
            </BoxContainer>
            <BoxContainer Orientation="Horizontal">
                <Label Name="RaffleMaxDurationLabel" Text="Max Duration (s)" />
                <SpinBox Name="RaffleMaxDuration" HorizontalExpand="True" />
            </BoxContainer>
        </BoxContainer>
        <BoxContainer Orientation="Horizontal">
            <Button Name="MakeButton" Text="Make" />
        </BoxContainer>
    </BoxContainer>

</DefaultWindow>
