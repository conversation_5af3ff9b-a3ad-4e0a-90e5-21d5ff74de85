- type: entity
  abstract: true
  parent: BaseItem
  id: BaseMagazineBoxAntiMateriel
  name: "коробочка з набоями (протипіхотний калібр .60)"
  components:
  - type: BallisticAmmoProvider
    mayTransfer: true
    whitelist:
      tags:
        - CartridgeAntiMateriel
    proto: CartridgeAntiMateriel
    capacity: 10
  - type: Item
    size: Small
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Boxes/anti_materiel.rsi
  - type: MagazineVisuals
    magState: mag
    steps: 2
    zeroVisible: false
  - type: Appearance

# Boxes
- type: entity
  parent: BaseMagazineBoxAntiMateriel
  id: MagazineBoxAntiMaterielBig
  name: "коробочка з набоями (протипіхотний калібр .60)"
  components:
  - type: BallisticAmmoProvider
    capacity: 30
    proto: CartridgeAntiMateriel
  - type: Sprite
    layers:
    - state: base-b
      map: ["enum.GunVisualLayers.Base"]
    - state: magb-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: magb
    steps: 2
    zeroVisible: false
  - type: Appearance

- type: entity
  parent: BaseMagazineBoxAntiMateriel
  id: MagazineBoxAntiMateriel
  name: "коробочка з набоями (протипіхотний калібр .60)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeAntiMateriel
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  parent: MagazineBoxAntiMateriel
  id: MagazineBoxAntiMaterielShrapnel
  name: "коробка боєприпасів (.60 протипіхотний, шрапнельний)"
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeAntiMaterielShrapnel
