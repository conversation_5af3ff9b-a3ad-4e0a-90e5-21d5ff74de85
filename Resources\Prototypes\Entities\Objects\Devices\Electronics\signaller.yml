#Remote Signaller Device

- type: entity
  parent: BaseItem
  id: RemoteSignaller
  name: "дистанційний сигналізатор"
  description: "Портативний пристрій, що використовується для дистанційного надсилання сигналів об'єктам у невеликому радіусі близько 15 метрів."
  components:
  - type: Sprite
    sprite: Objects/Devices/signaller.rsi
    state: signaller
  - type: Signaller
  - type: UseDelay
  - type: StaticPrice
    price: 40
  - type: DeviceLinkSource
    ports:
      - Pressed
  - type: Tag
    tags:
    - Payload
  - type: DeviceNetwork
    deviceNetId: Wireless
    receiveFrequencyId: BasicDevice
  - type: WirelessNetworkConnection
    range: 15

- type: entity
  parent: RemoteSignaller
  id: RemoteSignallerAdvanced
  name: "вдосконалений дистанційний сигналізатор"
  description: "Портативний пристрій, що використовується для дистанційного надсилання сигналів об'єктам у невеликому радіусі близько 50 метрів."
  components:
  - type: Sprite
    state: signaller2
  - type: WirelessNetworkConnection
    range: 50
  - type: StaticPrice
    price: 30