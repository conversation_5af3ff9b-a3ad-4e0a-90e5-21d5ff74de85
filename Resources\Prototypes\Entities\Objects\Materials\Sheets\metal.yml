- type: entity
  abstract: true
  parent: BaseItem
  id: SheetMetalBase
  description: "Лист металу, який часто використовується на станції в різних сферах."
  components:
  - type: Sprite
    sprite: Objects/Materials/Sheets/metal.rsi
  - type: Item
    sprite: Objects/Materials/Sheets/metal.rsi
    size: Normal
  - type: StaticPrice
    price: 0
  - type: Tag
    tags:
    - Sheet
    - Metal
    - NoPaint
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: SolutionContainerManager
    solutions:
      steel:
        canReact: false

- type: entity
  parent: SheetMetalBase
  id: SheetSteel
  name: "сталь"
  suffix: Full
  components:
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      Steel: 100
  - type: Stack
    stackType: Steel
    baseLayer: base
    layerStates:
    - steel
    - steel_2
    - steel_3
  - type: Sprite
    state: steel_3
    layers:
    - state: steel_3
      map: ["base"]
  - type: Item
    heldPrefix: steel
  - type: Appearance
  - type: FloorTile
    outputs:
    - Plating
  - type: Extractable
    grindableSolutionName: steel
  - type: SolutionContainerManager
    solutions:
      steel:
        reagents:
        - ReagentId: Iron
          Quantity: 9
        - ReagentId: Carbon
          Quantity: 1
  - type: UserInterface
    interfaces:
      enum.RadialSelectorUiKey.Key:
        type: RadialSelectorMenuBUI
  - type: ActivatableUI
    key: enum.RadialSelectorUiKey.Key
    inHandsOnly: true
    requireActiveHand: false
  - type: ShortConstruction
    entries:
    - prototype: Girder
    - prototype: MetalRod
    - category:
        name: Tiles
        icon:
          sprite: Objects/Tiles/tile.rsi
          state: steel
        entries:
        - prototype: TileSteel
        - prototype: TileWhite
        - prototype: TileDark
    - category:
        name: Electronics
        icon:
          sprite: Structures/Machines/computers.rsi
          state: computer-datatheory
        entries:
        - prototype: MachineFrame
        - prototype: Computer
        - prototype: Windoor
        - prototype: LightTubeFixture
        - prototype: APC
        - prototype: AirAlarmFixture
        - prototype: AirSensor
        - prototype: FireAlarm
    - category:
        name: Atmospherics
        icon:
          sprite: Structures/Piping/Atmospherics/vent.rsi
          state: vent_out
        entries:
        - prototype: GasPort
        - prototype: GasOutletInjector
        - prototype: GasVentScrubber
        - prototype: GasPassiveVent
        - prototype: GasVentPump
        - prototype: GasMixer
        - prototype: GasFilter
        - prototype: GasVolumePump
        - prototype: GasPressurePump
        - prototype: GasValve
    - category:
        name: Piping
        icon:
          sprite: Structures/Piping/Atmospherics/pipe.rsi
          state: pipeFourway
        entries:
        - prototype: HeatExchanger
        - prototype: GasPipeBend
        - prototype: GasPipeFourway
        - prototype: GasPipeHalf
        - prototype: GasPipeStraight
        - prototype: GasPipeTJunction
    - category:
        name: Disposals
        icon:
          sprite: Structures/Piping/disposal.rsi
          state: disposal
        entries:
        - prototype: DisposalPipe
        - prototype: DisposalBend
        - prototype: DisposalJunction
        - prototype: DisposalYJunction
        - prototype: DisposalRouter
        - prototype: DisposalTagger
        - prototype: DisposalTrunk
        - prototype: DisposalUnit
    - prototype: CrateGenericSteel
  - type: Tag # Goobstation
    tags:
    - Sheet
    - Metal
    - Steel

- type: entity
  parent: SheetSteel
  id: SheetSteel10
  name: "сталь"
  suffix: 10
  components:
  - type: Sprite
    state: steel
  - type: Stack
    stackType: Steel
    count: 10

- type: entity
  parent: SheetSteel
  id: SheetSteel1
  name: "сталь"
  suffix: Single
  components:
  - type: Sprite
    state: steel
  - type: Stack
    stackType: Steel
    count: 1

- type: entity
  parent: SheetSteel
  id: SheetSteelLingering0
  suffix: Lingering, 0
  components:
  - type: Stack
    lingering: true
    count: 0

- type: entity
  parent: SheetMetalBase
  id: SheetBrass
  name: "латунь"
  description: "Лист латуні, що використовується переважно любителями годинникових механізмів та залишків ратваріанських культів."
  suffix: Full
  components:
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      Brass: 100
  - type: Stack
    stackType: Brass
    baseLayer: base
    layerStates:
    - brass
    - brass_2
    - brass_3
  - type: Sprite
    state: brass_3
    layers:
    - state: brass_3
      map: ["base"]
  - type: Item
    heldPrefix: brass
  - type: Appearance
  - type: FloorTile
    outputs:
    - PlatingBrass
  - type: Extractable
    grindableSolutionName: brass
  - type: SolutionContainerManager
    solutions:
      brass:
        reagents:
        - ReagentId: Zinc
          Quantity: 3.3
        - ReagentId: Copper
          Quantity: 6.7

- type: entity
  parent: SheetBrass
  id: SheetBrass10
  name: "латунь"
  suffix: 10
  components:
  - type: Sprite
    state: brass
  - type: Stack
    stackType: Brass
    count: 10

- type: entity
  parent: SheetBrass
  id: SheetBrass1
  name: "латунь"
  suffix: Single
  components:
  - type: Sprite
    state: brass
  - type: Stack
    stackType: Brass
    count: 1

- type: entity
  parent: SheetMetalBase
  id: SheetPlasteel
  name: "пласталь"
  suffix: Full
  components:
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      Plasteel: 100
  - type: Stack
    stackType: Plasteel
    baseLayer: base
    layerStates:
    - plasteel
    - plasteel_2
    - plasteel_3
  - type: Sprite
    state: plasteel_3
    layers:
    - state: plasteel_3
      map: ["base"]
  - type: Item
    heldPrefix: plasteel
  - type: Appearance
  - type: Extractable
    grindableSolutionName: plasteel
  - type: SolutionContainerManager
    solutions:
      plasteel:
        reagents:
        - ReagentId: Plasma
          Quantity: 10
        - ReagentId: Iron
          Quantity: 9
        - ReagentId: Carbon
          Quantity: 1
        canReact: false
  - type: UserInterface
    interfaces:
      enum.RadialSelectorUiKey.Key:
        type: RadialSelectorMenuBUI
  - type: ActivatableUI
    key: enum.RadialSelectorUiKey.Key
    inHandsOnly: true
    requireActiveHand: false
  - type: ShortConstruction
    entries:
    - prototype: SecureWindoor
  - type: TwistedConstructionTarget
    replacementProto: RunedMetal1

- type: entity
  parent: SheetPlasteel
  id: SheetPlasteel10
  name: "пласталь"
  suffix: 10
  components:
  - type: Sprite
    state: plasteel
  - type: Stack
    stackType: Plasteel
    count: 10

- type: entity
  parent: SheetPlasteel
  id: SheetPlasteel1
  name: "пласталь"
  suffix: Single
  components:
  - type: Sprite
    state: plasteel
  - type: Stack
    stackType: Plasteel
    count: 1
