- type: entity
  id: BaseWeaponBattery
  parent: BaseItem
  abstract: true
  components:
  - type: Sprite
  - type: Item
    size: Huge
  - type: Clothing
    sprite: Objects/Weapons/Guns/Battery/laser_retro.rsi
    quickEquip: false
    slots:
    - Back
    - suitStorage
  - type: AmmoCounter
  - type: Gun
    fireRate: 2
    selectedMode: SemiAuto
    availableModes:
    - SemiAuto
    fireOnDropChance: 0.15
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/laser.ogg
  - type: Battery
    maxCharge: 1000
    startingCharge: 1000
  - type: MagazineVisuals
    magState: mag
    steps: 5
    zeroVisible: false
  - type: Appearance
  - type: StaticPrice
    price: 500
  - type: MeleeWeapon
    attackRate: 1.3333
    damage:
      types:
        Blunt: 8.5
    bluntStaminaDamageFactor: 1.25
    swapKeys: true
    disableHeavy: true
    animation: WeaponArcThrust
    wideAnimationRotation: 180
    soundHit:
      collection: MetalThud
  - type: IncreaseDamageOnWield
    damage:
      types:
        Blunt: 2.5
  - type: DamageOtherOnHit
    staminaCost: 7
  # Shitmed Change
  - type: Cautery
    speed: 0.9
  - type: SurgeryTool
    endSound:
      path: /Audio/Weapons/Guns/Gunshots/laser.ogg

- type: entity
  id: BaseWeaponPowerCell
  parent: BaseItem
  abstract: true
  components:
  - type: Sprite
  - type: Item
    size: Huge
  - type: AmmoCounter
  - type: Gun
    fireRate: 2
    selectedMode: SemiAuto
    availableModes:
    - SemiAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/laser.ogg
  - type: MagazineAmmoProvider
  - type: ItemSlots
    slots:
      gun_magazine:
        name: Magazine
        startingItem: PowerCellSmall
        insertSound: /Audio/Weapons/Guns/MagIn/batrifle_magin.ogg
        ejectSound: /Audio/Weapons/Guns/MagOut/batrifle_magout.ogg
        whitelist:
          tags:
            - PowerCell
            - PowerCellSmall
  - type: Appearance
  - type: StaticPrice
    price: 500
  - type: ContainerContainer
    containers:
      gun_magazine: !type:ContainerSlot
  - type: MeleeWeapon
    attackRate: 1.3333
    damage:
      types:
        Blunt: 9.0
    bluntStaminaDamageFactor: 1.25
    swapKeys: true
    disableHeavy: true
    animation: WeaponArcThrust
    wideAnimationRotation: 180
    soundHit:
      collection: MetalThud
  - type: IncreaseDamageOnWield
    damage:
      types:
        Blunt: 2.5
  - type: DamageOtherOnHit
    staminaCost: 7
  # Shitmed Change
  - type: Cautery
    speed: 0.9
  - type: SurgeryTool
    endSound:
      path: /Audio/Weapons/Guns/Gunshots/laser.ogg

- type: entity
  id: BaseWeaponBatterySmall
  parent: BaseWeaponBattery
  abstract: true
  components:
  - type: Item
    size: Small
    shape:
    - 0,0,1,0
    - 0,1,0,1
  - type: Tag
    tags:
    - Sidearm
  - type: Clothing
    sprite: Objects/Weapons/Guns/Battery/taser.rsi
    quickEquip: false
    slots:
    - Belt
    - suitStorage
  - type: MeleeWeapon
    attackRate: 1.2
    damage:
      types:
        Blunt: 7.5
    bluntStaminaDamageFactor: 1.0
    wideAnimationRotation: 135
  - type: DamageOtherOnHit
    staminaCost: 5

- type: entity
  id: BaseWeaponPowerCellSmall
  parent: BaseWeaponPowerCell
  abstract: true
  components:
  - type: Item
    size: Small
  - type: Tag
    tags:
    - Sidearm
  - type: Clothing
    sprite: Objects/Weapons/Guns/Battery/taser.rsi
    quickEquip: false
    slots:
    - Belt
  - type: MeleeWeapon
    attackRate: 1.2
    damage:
      types:
        Blunt: 7.5
    bluntStaminaDamageFactor: 1.0
    wideAnimationRotation: 135
  - type: DamageOtherOnHit
    staminaCost: 5

- type: entity
  name: "лазерний пістолет svalinn"
  parent: BaseWeaponPowerCellSmall
  id: WeaponLaserSvalinn
  description: "Дешевий і широко використовуваний лазерний пістолет."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Battery/svalinn.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-unshaded-4
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: Item
    sprite: Objects/Weapons/Guns/Battery/svalinn.rsi
  - type: MagazineVisuals
    magState: mag
    steps: 5
    zeroVisible: true

- type: entity
  name: "ретро лазерний бластер"
  parent: BaseWeaponBatterySmall
  id: WeaponLaserGun
  description: "Зброя, що використовує світло, підсилене стимульованим випромінюванням."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Battery/laser_retro.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-unshaded-4
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: HitscanBatteryAmmoProvider
    proto: RedMediumLaser
    fireCost: 62.5
  - type: MagazineVisuals
    magState: mag
    steps: 5
    zeroVisible: true
  - type: Appearance

- type: entity
  name: "саморобний лазерний пістолет"
  parent: BaseWeaponBatterySmall
  id: WeaponMakeshiftLaser
  description: "Краще моліться, щоб він не обпік вам руки."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Battery/makeshift.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-unshaded-4
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: Clothing
    sprite: Objects/Weapons/Guns/Battery/makeshift.rsi
  - type: HitscanBatteryAmmoProvider
    proto: RedLaser
    fireCost: 62.5
  - type: Battery
    maxCharge: 500
    startingCharge: 500

- type: entity
  name: "тесла-рушниця"
  parent: BaseWeaponBattery
  id: WeaponTeslaGun
  description: "Сила первісної стихії блискавки у ваших руках."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Battery/tesla_gun.rsi
    layers:
      - state: base
        map: ["enum.GunVisualLayers.Base"]
      - state: mag-unshaded-4
        map: ["enum.GunVisualLayers.MagUnshaded"]
        shader: unshaded
  - type: Gun
    projectileSpeed: 10
    soundGunshot:
      path: /Audio/Effects/Lightning/lightningshock.ogg
      params:
        variation: 0.2
  - type: ProjectileBatteryAmmoProvider
    proto: TeslaGunBullet
    fireCost: 300
  - type: MagazineVisuals
    magState: mag
    steps: 5
    zeroVisible: true
  - type: Appearance

- type: entity
  name: "лазерна гвинтівка"
  parent: [BaseWeaponBattery, BaseGunWieldable]
  id: WeaponLaserCarbine
  description: "Компанія Nanotrasen Security надає перевагу Nanotrasen Security за дешевизну та простоту використання."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Battery/laser_gun.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-unshaded-4
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: Clothing
    sprite: Objects/Weapons/Guns/Battery/laser_gun.rsi
  - type: StaticPrice
    price: 420
  - type: Gun
    selectedMode: SemiAuto
    availableModes:
    - SemiAuto
  - type: HitscanBatteryAmmoProvider
    proto: RedLaser
    fireCost: 62.5

- type: entity
  name: "тренувальна лазерна гвинтівка"
  parent: WeaponLaserCarbine
  id: WeaponLaserCarbinePractice
  description: "Ця модифікована лазерна гвинтівка стріляє майже нешкідливими променями у 40-ватному діапазоні для тренувань на мішенях."
  components:
  - type: HitscanBatteryAmmoProvider
    proto: RedLaserPractice
    fireCost: 62.5
  - type: StaticPrice
    price: 300
  - type: MeleeWeapon
    damage:
      types:
        Blunt: 4

- type: entity
  name: "імпульсний пістолет"
  parent: BaseWeaponBatterySmall
  id: WeaponPulsePistol
  description: "Надсучасний енергетичний пістолет, якому віддають перевагу як зброї оперативники НТ."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Battery/pulse_pistol.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-unshaded-4
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: Clothing
    sprite: Objects/Weapons/Guns/Battery/pulse_pistol.rsi
  - type: Gun
    selectedMode: SemiAuto
    availableModes:
    - SemiAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/laser_cannon.ogg
  - type: HitscanBatteryAmmoProvider
    proto: Pulse
    fireCost: 200
  - type: Battery
    maxCharge: 2000
    startingCharge: 2000

- type: entity
  name: "антикварний імпульсний пістолет"
  parent: WeaponPulsePistol
  id: WeaponPulsePistolHoS
  description: "Одна з багатьох одиниць зброї з приватної колекції голови безпеки. На цьому пістолеті викарбувано слова: \"Прости нас, Мати Сол\""
  components:
  - type: StealTarget
    stealGroup: HoSAntiqueWeapon

- type: entity
  name: "імпульсний пістолет капітана"
  parent: WeaponPulsePistol
  id: WeaponPulsePistolCaptain
  description: "Рідкісний та екзотичний пістолет, подарований капітану станції. На руків'ї зі слонової кістки викарбувано слова: \"Слава Компанії, слава Матері Сол. Форон зробить нас усіх багатими\""
  components:
  - type: StealTarget
    stealGroup: WeaponCaptain

- type: entity
  name: "імпульсний карабін"
  parent: [BaseWeaponBattery, BaseGunWieldable]
  id: WeaponPulseCarbine
  description: "Високотехнологічний енергетичний карабін, який полюбляють оперативники NT-ERT."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Battery/pulse_carbine.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-unshaded-4
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: Clothing
    sprite: Objects/Weapons/Guns/Battery/pulse_carbine.rsi
  - type: Gun
    selectedMode: SemiAuto
    fireRate: 3
    availableModes:
      - SemiAuto
      - FullAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/laser_cannon.ogg
  - type: HitscanBatteryAmmoProvider
    proto: Pulse
    fireCost: 200
  - type: Battery
    maxCharge: 5000
    startingCharge: 5000

- type: entity
  name: "імпульсна гвинтівка"
  parent: [BaseWeaponBattery, BaseGunWieldable]
  id: WeaponPulseRifle
  description: "Зброя, яка є майже такою ж сумнозвісною, як і її користувачі."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Battery/pulse_rifle.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-unshaded-4
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: Clothing
    sprite: Objects/Weapons/Guns/Battery/pulse_rifle.rsi
  - type: Gun
    fireRate: 1.5
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/laser3.ogg
  - type: HitscanBatteryAmmoProvider
    proto: Pulse
    fireCost: 100
  - type: Battery
    maxCharge: 40000
    startingCharge: 40000

- type: entity
  name: "лазерна гармата"
  parent: [BaseWeaponBattery, BaseGunWieldable]
  id: WeaponLaserCannon
  description: "Надпотужна, потужна лазерна зброя."
  components:
  - type: Tag
    tags:
    - TurretCompatibleWeapon
  - type: Sprite
    sprite: Objects/Weapons/Guns/Battery/laser_cannon.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-unshaded-4
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: Clothing
    sprite: Objects/Weapons/Guns/Battery/laser_cannon.rsi
  - type: Gun
    fireRate: 1.5
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/laser_cannon.ogg
  - type: HitscanBatteryAmmoProvider
    proto: RedHeavyLaser
    fireCost: 100
  - type: MeleeWeapon
    attackRate: 1.4
    damage:
      types:
        Blunt: 10
    bluntStaminaDamageFactor: 1.3333
  - type: DamageOtherOnHit
    staminaCost: 9.5


- type: entity
  name: "портативний сповільнювач частинок"
  parent: BaseWeaponBattery
  id: WeaponParticleDecelerator
  description: "Портативний сповільнювач частинок, здатний розкласти теслу або сингулярність."
  components:
    - type: Item
      size: Ginormous
    - type: MultiHandedItem
    - type: ClothingSpeedModifier
      walkModifier: 0.6
      sprintModifier: 0.6
    - type: HeldSpeedModifier
    - type: Sprite
      sprite: Objects/Weapons/Guns/Battery/particle_decelerator.rsi
      layers:
        - state: base
          map: ["enum.GunVisualLayers.Base"]
    - type: Gun
      fireRate: 0.5
      soundGunshot:
        path: /Audio/Weapons/emitter.ogg
        params:
          pitch: 2
    - type: ProjectileBatteryAmmoProvider
      proto: AntiParticlesProjectile
      fireCost: 500
    - type: Battery
      maxCharge: 10000
      startingCharge: 10000
    - type: MeleeWeapon
      attackRate: 1.6
      damage: # This is super expensive, low attack rate, slows down the user and high stam cost so it can be high
        types:
          Blunt: 34
          Structural: 10
      swapKeys: false
      disableHeavy: false
      disableClick: true
      bluntStaminaDamageFactor: 1.5
      heavyRateModifier: 1.0
      heavyDamageBaseModifier: 1.0
      heavyStaminaCost: 21
      wideAnimationRotation: 270
    - type: DamageOtherOnHit
      staminaCost: 48

- type: entity
  name: "рентгенівська гармата"
  parent: [BaseWeaponBattery, BaseGunWieldable]
  id: WeaponXrayCannon
  description: "Експериментальна зброя, яка використовує концентровану рентгенівську енергію проти своєї цілі."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Battery/xray.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-unshaded-0
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: Clothing
    sprite: Objects/Weapons/Guns/Battery/xray.rsi
  - type: Gun
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/laser3.ogg
  - type: HitscanBatteryAmmoProvider
    proto: XrayLaser
    fireCost: 100
  - type: MagazineVisuals
    magState: mag
    steps: 5
    zeroVisible: true
  - type: Appearance

- type: entity
  name: "цивільний дизейблер"
  parent: BaseWeaponBatterySmall
  id: WeaponCivilianDisabler
  description: "Оригінальні прототипи дизейблерів, переобладнані для цивільної самооборони і поміщені в блискучу нову оболонку. Він має набагато меншу вихідну потужність, ніж сучасний дизейблер, який використовується Службою безпеки, і тому не вимагає ліцензії на носіння."
  components:
    - type: Tag
      tags:
        - Taser
        - Sidearm
    - type: Item
      size: Small
      shape:
      - 0,0,1,1
    - type: Sprite
      sprite: Objects/Weapons/Guns/Battery/civilian-disabler.rsi
      layers:
        - state: base
          map: ["enum.GunVisualLayers.Base"]
        - state: mag-unshaded-0
          map: ["enum.GunVisualLayers.MagUnshaded"]
          shader: unshaded
    - type: Clothing
      sprite: Objects/Weapons/Guns/Battery/civilian-disabler.rsi
      quickEquip: false
      slots:
        - suitStorage
        - Belt
    - type: Gun
      fireRate: 2
      soundGunshot:
        path: /Audio/Weapons/Guns/Gunshots/taser2.ogg
    - type: ProjectileBatteryAmmoProvider
      proto: BulletCivilianDisabler
      fireCost: 100
    - type: MagazineVisuals
      magState: mag
      steps: 5
      zeroVisible: true
    - type: Appearance
    - type: ExtendDescription
      descriptionList:
      - description: "civiliandisabler-extenddescription-security"
        fontSize: 12
        color: "#ff0000"
        requireDetailRange: true
        requirements:
        - !type:CharacterDepartmentRequirement
          departments:
            - Security
    - type: MeleeWeapon
      damage:
        types:
          Blunt: 3.0
      bluntStaminaDamageFactor: 2
      wideAnimationRotation: 90

- type: entity
  name: "дізейблер"
  parent: BaseWeaponBatterySmall
  id: WeaponDisabler
  description: "Зброя самооборони, яка виснажує органічні цілі, послаблюючи їх аж до руйнування."
  components:
    - type: Tag
      tags:
        - Taser
        - Sidearm
        - WeaponDisabler
    - type: Sprite
      sprite: Objects/Weapons/Guns/Battery/disabler.rsi
      layers:
        - state: base
          map: ["enum.GunVisualLayers.Base"]
        - state: mag-unshaded-0
          map: ["enum.GunVisualLayers.MagUnshaded"]
          shader: unshaded
    - type: Clothing
      sprite: Objects/Weapons/Guns/Battery/disabler.rsi
      quickEquip: false
      slots:
        - suitStorage
        - Belt
    - type: Gun
      fireRate: 2
      soundGunshot:
        path: /Audio/Weapons/Guns/Gunshots/taser2.ogg
    - type: ProjectileBatteryAmmoProvider
      proto: BulletDisabler
      fireCost: 100
    - type: MagazineVisuals
      magState: mag
      steps: 5
      zeroVisible: true
    - type: Appearance
    - type: GuideHelp
      guides:
      - Security
    - type: MeleeWeapon
      damage:
        types:
          Blunt: 5.0
      bluntStaminaDamageFactor: 2.5
      wideAnimationRotation: 135

- type: entity
  name: "Пристрій виведення з ладу Центкому"
  parent: WeaponDisabler
  id: WeaponCCDisabler
  description: "Вдосконалена зброя самооборони, яка виснажує органічні цілі, послаблюючи їх, поки вони не впадуть. Тільки для найкращих офіцерів, яким ми не довіряємо летальну зброю."
  components:
  - type: BatterySelfRecharger
    autoRecharge: true
    autoRechargeRate: 20
  - type: StealTarget
    stealGroup: NTRepWeapon
  - type: Gun
    fireRate: 3
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/taser2.ogg
  - type: Sprite
    sprite: Objects/Weapons/Guns/Battery/ccdisabler.rsi
    layers:
      - state: base
        map: ["enum.GunVisualLayers.Base"]
      - state: mag-unshaded-0
        map: ["enum.GunVisualLayers.MagUnshaded"]
        shader: unshaded
  - type: Tag
    tags:
    - HighRiskItem
    - Taser
    - Sidearm

- type: entity
  name: "sMG-знешкоджувач"
  parent: BaseWeaponBattery
  id: WeaponDisablerSMG
  description: "Удосконалена зброя, яка виснажує органічні цілі, послаблюючи їх, поки вони не впадуть."
  components:
  - type: Item
    size: Large
  - type: Tag
    tags:
      - Taser
      - Sidearm
  - type: Sprite
    sprite: Objects/Weapons/Guns/Battery/disabler_smg.rsi
    layers:
      - state: base
        map: ["enum.GunVisualLayers.Base"]
      - state: mag-unshaded-0
        map: ["enum.GunVisualLayers.MagUnshaded"]
        shader: unshaded
  - type: Gun
    selectedMode: FullAuto
    fireRate: 4
    availableModes:
      - SemiAuto
      - FullAuto
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/taser2.ogg
  - type: ProjectileBatteryAmmoProvider
    proto: BulletDisablerSmg
    fireCost: 33
  - type: MagazineVisuals
    magState: mag
    steps: 5
    zeroVisible: true
  - type: StaticPrice
    price: 260
  - type: MeleeWeapon
    damage:
      types:
        Blunt: 6.5
    bluntStaminaDamageFactor: 2.5
    wideAnimationRotation: 180

- type: entity
  name: "тренувальний дізейблер"
  parent: WeaponDisabler
  id: WeaponDisablerPractice
  description: "Зброя самооборони, яка виснажує органічні цілі, послаблюючи їх до повного руйнування. Ця зброя була пристосована для кадетів, що робить її майже нешкідливою."
  components:
    - type: Sprite
      sprite: Objects/Weapons/Guns/Battery/practice_disabler.rsi
      layers:
        - state: base
          map: ["enum.GunVisualLayers.Base"]
        - state: mag-unshaded-0
          map: ["enum.GunVisualLayers.MagUnshaded"]
          shader: unshaded
    - type: Clothing
      sprite: Objects/Weapons/Guns/Battery/practice_disabler.rsi
      quickEquip: false
      slots:
        - Belt
    - type: StaticPrice
      price: 100
    - type: ProjectileBatteryAmmoProvider
      proto: BulletDisablerPractice
      fireCost: 100
    - type: MeleeWeapon
      damage:
        types:
          Blunt: 3
      bluntStaminaDamageFactor: 1.0

- type: entity
  name: "електрошокер"
  parent: BaseWeaponBatterySmall
  id: WeaponTaser
  description: "Енергетичний електрошокер малої потужності, що використовується командами безпеки для знешкодження цілей на відстані."
  components:
  - type: Tag
    tags:
    - Taser
    - Sidearm
  - type: Sprite
    sprite: Objects/Weapons/Guns/Battery/taser.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-unshaded-0
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: Item
    heldPrefix: taser4
  - type: Clothing
    quickEquip: false
    slots:
    - Belt
  - type: Gun
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/taser.ogg
  - type: ProjectileBatteryAmmoProvider
    proto: BulletTaser
    fireCost: 200
  - type: MagazineVisuals
    magState: mag
    steps: 5
    zeroVisible: true
  - type: Appearance

- type: entity
  name: "антикварний лазерний пістолет"
  parent: BaseWeaponBatterySmall
  id: WeaponAntiqueLaser
  description: "Це антикварний лазерний пістолет. Вся робота виконана на найвищому рівні. Він оздоблений шкірою та хромом. Предмет погрожує сплесками енергії."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Battery/antiquelasergun.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-unshaded-4
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: Clothing
    sprite: Objects/Weapons/Guns/Battery/antiquelasergun.rsi
  - type: Gun
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/laser_cannon.ogg
  - type: HitscanBatteryAmmoProvider
    proto: RedMediumLaser
    fireCost: 100
  - type: BatterySelfRecharger
    autoRecharge: true
    autoRechargeRate: 40
  - type: MagazineVisuals
    magState: mag
    steps: 5
    zeroVisible: true
  - type: Appearance
  - type: Tag
    tags:
    - HighRiskItem
    - Sidearm
    - WeaponAntiqueLaser
  - type: StaticPrice
    price: 750
  - type: StealTarget
    stealGroup: WeaponAntiqueLaser
  - type: MeleeWeapon
    attackRate: 1.3333
    damage:
      types:
        Blunt: 9
    bluntStaminaDamageFactor: 1.25
    wideAnimationRotation: 135
  - type: DamageOtherOnHit
    staminaCost: 5
  - type: SentienceTarget # I hope this is only the captain's gun
    flavorKind: station-event-random-sentience-flavor-inanimate
    weight: 0.0002 # 5,000 times less likely than 1 regular animal
    # not putting a BlockMovement component here cause that's funny.

- type: entity
  name: "вдосконалений лазерний пістолет"
  parent: BaseWeaponBatterySmall
  id: WeaponAdvancedLaser
  description: "Експериментальний високоенергетичний лазерний пістолет з ядерною батареєю, що самозаряджається."
  components:
  - type: Item
    size: Normal  # Intentionally larger than other pistols
  - type: Sprite
    sprite: Objects/Weapons/Guns/Battery/advancedlasergun.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-unshaded-4
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: Clothing
    sprite: Objects/Weapons/Guns/Battery/advancedlasergun.rsi
  - type: Gun
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/laser_cannon.ogg
  - type: HitscanBatteryAmmoProvider
    proto: RedMediumLaser
    fireCost: 100
  - type: BatterySelfRecharger
    autoRecharge: true
    autoRechargeRate: 30
  - type: MagazineVisuals
    magState: mag
    steps: 5
    zeroVisible: true
  - type: Appearance
  - type: StaticPrice
    price: 63
  - type: MeleeWeapon
    damage:
      types:
        Blunt: 8
  - type: DamageOtherOnHit
    staminaCost: 6

- type: entity
  name: "ручна гармата C.H.I.M.P"
  parent: BaseWeaponBatterySmall
  id: WeaponPistolCHIMP
  description: "Те, що це невеликий C.H.I.M.P., не означає, що він не може бити, як A.P.E."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Revolvers/chimp.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-unshaded-1
      visible: false
      map: ["enum.GunVisualLayers.MagUnshaded"]
      shader: unshaded
  - type: Appearance
  - type: MagazineVisuals
    magState: mag
    steps: 3
    zeroVisible: false
  - type: Clothing
    sprite: Objects/Weapons/Guns/Revolvers/chimp.rsi
  - type: Gun
    projectileSpeed: 4
    fireRate: 1.5
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/taser2.ogg
  - type: ProjectileBatteryAmmoProvider
    proto: AnomalousParticleDeltaStrong
    fireCost: 100
  - type: BatteryWeaponFireModes
    fireModes:
    - proto: AnomalousParticleDeltaStrong
      fireCost: 100
    - proto: AnomalousParticleEpsilonStrong
      fireCost: 100
    - proto: AnomalousParticleZetaStrong
      fireCost: 100
    - proto: AnomalousParticleSigmaStrong
      fireCost: 100
  - type: Construction
    graph: UpgradeWeaponPistolCHIMP
    node: start
  - type: StaticPrice
    price: 100

- type: entity
  name: "експериментальна ручна гармата C.H.I.M.P"
  parent: WeaponPistolCHIMP
  id: WeaponPistolCHIMPUpgraded
  description: "Цей C.H.I.M.P., здається, має більший удар, ніж зазвичай..."
  components:
  - type: BatteryWeaponFireModes
    fireModes:
    - proto: AnomalousParticleDeltaStrong
      fireCost: 100
    - proto: AnomalousParticleEpsilonStrong
      fireCost: 100
    - proto: AnomalousParticleOmegaStrong
      fireCost: 100
    - proto: AnomalousParticleZetaStrong
      fireCost: 100
    - proto: AnomalousParticleSigmaStrong
      fireCost: 100

- type: entity
  name: "око збожеволілого"
  parent: BaseWeaponBatterySmall
  id: WeaponBehonkerLaser
  description: "Око дивака, воно вистрілює лазером, коли його стискають."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Battery/behonker_eye.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
  - type: Gun
    fireRate: 1
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/laser_clown.ogg
  - type: HitscanBatteryAmmoProvider
    proto: RedMediumLaser
    fireCost: 100
  - type: BatterySelfRecharger
    autoRecharge: true
    autoRechargeRate: 40
  - type: MagazineVisuals
    magState: mag
    steps: 5
    zeroVisible: true
  - type: Appearance
  - type: StaticPrice
    price: 750

- type: entity
  name: "енергетичний дробовик"
  parent: [BaseWeaponBattery, BaseGunWieldable]
  id: WeaponEnergyShotgun
  description: "Єдиний у своєму роді прототип енергетичної зброї, що використовує різні конфігурації дробовиків. Він пропонує можливість як летальних, так і нелетальних пострілів, що робить його універсальною зброєю."
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Battery/energy_shotgun.rsi
    layers:
      - state: base
        map: ["enum.GunVisualLayers.Base"]
      - state: mag-unshaded-4
        map: ["enum.GunVisualLayers.MagUnshaded"]
        shader: unshaded
  - type: Clothing
    sprite: Objects/Weapons/Guns/Battery/energy_shotgun.rsi
  - type: Gun
    fireRate: 2
    soundGunshot:
      path: /Audio/Weapons/Guns/Gunshots/laser_cannon.ogg
    uniformSpread: true
  - type: ProjectileBatteryAmmoProvider
    proto: BulletLaserSpread
    fireCost: 150
  - type: BatteryWeaponFireModes
    fireModes:
    - proto: BulletLaserSpread
      fireCost: 150
    - proto: BulletLaserSpreadSlug
      fireCost: 300
    - proto: BulletDisablerSmgSpread
      fireCost: 120
  - type: Item
    size: Large
    shape:
    - 0,0,3,1
    sprite: Objects/Weapons/Guns/Battery/inhands_64x.rsi
    heldPrefix: energy
  - type: Tag
    tags:
    - HighRiskItem
  - type: GunRequiresWield #remove when inaccuracy on spreads is fixed
  - type: Battery
    maxCharge: 1210
    startingCharge: 1210
  - type: BatterySelfRecharger
    autoRecharge: true
    autoRechargeRate: 48
    autoRechargePause: true
    autoRechargePauseTime: 5

