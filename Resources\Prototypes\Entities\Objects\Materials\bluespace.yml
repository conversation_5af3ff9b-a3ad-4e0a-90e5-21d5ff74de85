- type: entity
  parent: MaterialBase
  id: MaterialBluespace
  suffix: Full
  name: "блюспейс кристал"
  components:
  - type: Sprite
    sprite: Objects/Materials/materials.rsi
    layers:
    - state: bluespace_3
      map: ["base"]
  - type: Appearance
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      Bluespace: 100
  - type: EmitSoundOnUse
    sound:
      collection: RadiationPulse
  - type: EtherealStunItem
  - type: Tag
    tags:
    - BluespaceCrystal
    - RawMaterial
  - type: Stack
    stackType: Bluespace
    baseLayer: base
    layerStates:
    - bluespace
    - bluespace_2
    - bluespace_3
    count: 5
  - type: Item
    size: Small

- type: entity
  parent: MaterialBluespace
  id: MaterialBluespace1
  suffix: 1
  components:
  - type: Sprite
    state: bluespace
  - type: Stack
    count: 1

- type: entity
  parent: MaterialBluespace1
  id: MaterialBluespace3
  suffix: 3
  components:
  - type: Stack
    count: 3

- type: entity
  parent: MaterialBluespace1
  id: MaterialBluespace5
  suffix: 5
  components:
  - type: Stack
    count: 5

- type: entity
  parent: MaterialBase
  id: MaterialNormality
  suffix: Full
  name: "нормальний кристал"
  components:
  - type: Sprite
    sprite: Objects/Materials/materials.rsi
    layers:
    - state: normality_3
      map: ["base"]
  - type: Appearance
  - type: Material
  - type: PhysicalComposition
    materialComposition:
      Normality: 100
  - type: Tag
    tags:
    - NormalityCrystal
    - RawMaterial
  - type: Stack
    stackType: Normality
    baseLayer: base
    layerStates:
    - normality
    - normality_2
    - normality_3
    count: 5
  - type: Item
    size: Small

- type: entity
  parent: MaterialNormality
  id: MaterialNormality1
  suffix: 1
  components:
  - type: Sprite
    state: normality
  - type: Stack
    count: 1

- type: entity
  parent: MaterialNormality1
  id: MaterialNormality3
  suffix: 3
  components:
  - type: Stack
    count: 3

- type: entity
  parent: MaterialNormality1
  id: MaterialNormality5
  suffix: 5
  components:
  - type: Stack
    count: 5
