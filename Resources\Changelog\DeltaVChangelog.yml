Entries:
- author: Debug<PERSON><PERSON>
  changes:
    - message: Add automatic changelogs
      type: Add
  id: 1
  time: '2023-09-13T08:00:00.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Add
      message: Added most of the DeltaV and Nyano food and drink, along with recipes.
  id: 2
  time: '2023-09-13T16:40:16.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added 15 new moth plushes.
  id: 3
  time: '2023-09-13T16:43:47.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added Vulpkanin.
  id: 4
  time: '2023-09-13T18:24:12.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added mothroaches.
  id: 5
  time: '2023-09-13T19:19:25.0000000+00:00'
- author: TadJohnson00
  changes:
    - type: Add
      message: Added arctic foxes to the game. Who knows, maybe you'll see one around!
  id: 6
  time: '2023-09-13T20:15:57.0000000+00:00'
- author: Dorragon
  changes:
    - type: Add
      message: Added Pride-O-Mat
    - type: Add
      message: Added restock box for prideomat that is available in cargo
  id: 7
  time: '2023-09-16T06:26:04.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added various posters, signs and paintings.
  id: 8
  time: '2023-09-18T13:49:03.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Martial Artists, Prison Guards, Prisoners and Gladiators are back.
  id: 9
  time: '2023-09-19T14:22:56.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Felinids have been reintegrated to the crew.
  id: 10
  time: '2023-09-20T23:28:21.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Tweak
      message: >-
        Paramedics now spawn with their respective equipment for retrieving
        crewmates more effectively.
  id: 11
  time: '2023-09-21T04:01:02.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added the Courier, the Mail Carrier's cousin.
    - type: Tweak
      message: Cargo Department is now the Logistics Department
  id: 12
  time: '2023-09-21T04:37:30.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Remove
      message: Clown Spiders no longer can squeeze through the vents.
  id: 13
  time: '2023-09-21T20:45:11.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added the PSB emergency snack.
  id: 14
  time: '2023-09-26T02:57:54.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Tweak
      message: Changed chat messages to not have the names bold.
  id: 15
  time: '2023-09-26T16:47:58.0000000+00:00'
- author: toasterpm87
  changes:
    - type: Add
      message: 'toy A6m2 '
  id: 16
  time: '2023-09-27T15:22:55.0000000+00:00'
- author: DebugOk
  changes:
    - type: Add
      message: Merged latest changes from Wizden
  id: 17
  time: '2023-09-27T19:59:13.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Remove
      message: Removed Dwarf from roundstart choices.
  id: 18
  time: '2023-09-27T21:09:01.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Tweak
      message: >-
        Cadets no longer gets a gun and no more spare mags for Security
        Officers.
  id: 19
  time: '2023-09-27T22:51:15.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Tweak
      message: Give back all the jobs their proper time requirements.
  id: 20
  time: '2023-09-28T14:49:00.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Remove
      message: The Senior Roles are no longer available at round start.
  id: 21
  time: '2023-09-28T16:55:32.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Remove
      message: Remove Ian as a traitor objective.
  id: 22
  time: '2023-09-28T17:01:47.0000000+00:00'
- author: leonardo-dabepis
  changes:
    - type: Tweak
      message: Bees can now be put in inventory and ground up as a ingredient.
  id: 23
  time: '2023-09-28T17:52:22.0000000+00:00'
- author: JJ
  changes:
    - type: Add
      message: Added Grilled Cheese.
  id: 24
  time: '2023-09-28T17:53:04.0000000+00:00'
- author: DebugOk
  changes:
    - type: Tweak
      message: Approving cargo orders now requires the Orders access level
  id: 25
  time: '2023-09-28T18:16:02.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Tweak
      message: Science is now Epistemics.
  id: 26
  time: '2023-09-28T22:32:46.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added a new steal objective for the Head of Logistics.
  id: 27
  time: '2023-09-29T13:28:43.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added fake wizard robes and sandals.
  id: 28
  time: '2023-09-29T13:55:29.0000000+00:00'
- author: PHCodes
  changes:
    - type: Add
      message: Added Mr. Butlertron, the Roboisseur
  id: 29
  time: '2023-09-30T15:51:58.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added the rickenbacker, killer tomato and useful spawners for mappers.
  id: 30
  time: '2023-09-30T19:44:56.0000000+00:00'
- author: DebugOk
  changes:
    - type: Add
      message: Mail has been reimplemented
  id: 31
  time: '2023-10-01T16:25:32.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added the vector safe.
  id: 32
  time: '2023-10-01T20:10:24.0000000+00:00'
- author: VMSolidus
  changes:
    - type: Tweak
      message: >-
        Rebalanced Vulpakin, they are no longer HUMAN+, and are now currently a
        sidegrade to humans.
  id: 33
  time: '2023-10-03T16:49:35.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Remove
      message: Removed the vape.
  id: 34
  time: '2023-10-03T16:51:54.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added the Psionic Mantis.
  id: 35
  time: '2023-10-03T17:47:54.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Add
      message: >-
        Added PDA boxes to department heads' lockers. Bug your manager for a
        promotion now!
  id: 36
  time: '2023-10-03T18:06:07.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added the Psionic Countermeasures research.
  id: 37
  time: '2023-10-03T19:36:47.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added more guns. If that don't work, we'll add more guns.
  id: 38
  time: '2023-10-03T20:01:48.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added tips about glimmer, rat kings and wisps.
  id: 39
  time: '2023-10-03T20:23:18.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added an old epistemics book.
  id: 40
  time: '2023-10-03T21:41:37.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: >-
        Added the ability for Oni Syndicate agents to buy the Kanabou and
        Tousei-Gusoku set.
  id: 41
  time: '2023-10-03T21:45:06.0000000+00:00'
- author: DebugOk
  changes:
    - type: Add
      message: Nanotrasen R&D has cooked up some new hardlight technology
  id: 42
  time: '2023-10-03T22:23:09.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Tweak
      message: Martial artist finally gets a new coat of paint for their PDA.
  id: 43
  time: '2023-10-04T00:20:48.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Tweak
      message: Changed the glass texture to be more visible.
  id: 44
  time: '2023-10-05T19:22:26.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Remove
      message: >-
        The Organic Resources Department now uses better filing system, no more
        bureaucracy errors.
  id: 45
  time: '2023-10-05T19:22:35.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added dogvision colorblindness.
  id: 46
  time: '2023-10-05T19:26:42.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added a pistol in .38 for the Mantis.
  id: 47
  time: '2023-10-05T19:28:00.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added Lollipop and Gumball candies.
  id: 48
  time: '2023-10-05T19:29:04.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Tweak
      message: Mantis gets their own access.
  id: 49
  time: '2023-10-05T20:55:12.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added mediborgs as round start job.
  id: 50
  time: '2023-10-06T15:30:42.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Add
      message: Added Edge Station.
  id: 51
  time: '2023-10-07T05:12:58.0000000+00:00'
- author: VMSolidus
  changes:
    - type: Remove
      message: Scrubs can no longer be obtained from the MediDrobe
    - type: Add
      message: NT may have forgotten to check their MediDrobes for contraband
    - type: Add
      message: >-
        Eight different colors of scrubs and surgical caps can now be found in
        medical scrub crates!
    - type: Add
      message: >-
        Scrubs can now spawn in one of 8 exciting new colors. Please report
        sightings of a mysterious 9th color to management.(this needs to be
        mapped in)
  id: 52
  time: '2023-10-08T22:09:33.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added six new lobby art.
  id: 53
  time: '2023-10-09T17:35:35.0000000+00:00'
- author: ps3moira
  changes:
    - type: Add
      message: Added two new paintings.
  id: 54
  time: '2023-10-10T03:42:19.0000000+00:00'
- author: leonardo_dabepis
  changes:
    - type: Tweak
      message: Changed how the revenant looks.
  id: 55
  time: '2023-10-10T03:48:16.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Holy water and bibles now deal Holy damage to unholy creatures.
  id: 56
  time: '2023-10-10T03:55:34.0000000+00:00'
- author: ps3moira
  changes:
    - type: Tweak
      message: Skull Size
  id: 57
  time: '2023-10-10T04:12:19.0000000+00:00'
- author: TJohnson
  changes:
    - type: Add
      message: >-
        Added new vests to play to replace most of the Type I vests in organic
        play.
    - type: Remove
      message: >-
        Removed most of the Type I vests in organic play. Some might still crop
        up, but don't count on it!
  id: 58
  time: '2023-10-10T04:43:44.0000000+00:00'
- author: ps3moira
  changes:
    - type: Add
      message: Station Holograph
  id: 59
  time: '2023-10-11T00:00:46.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added shock collar, Mystagogue's bible and the fish labeler.
  id: 60
  time: '2023-10-11T03:26:21.0000000+00:00'
- author: Leonardo DaBepis
  changes:
    - type: Tweak
      message: Gave the Courier a new uniform and cap.
  id: 61
  time: '2023-10-12T17:31:11.0000000+00:00'
- author: rosieposieeee
  changes:
    - type: Add
      message: Added Paramedic airlocks.
  id: 62
  time: '2023-10-13T16:40:09.0000000+00:00'
- author: VMSolidus
  changes:
    - type: Add
      message: 'Harpies have been added to the game as a new playable species! '
  id: 63
  time: '2023-10-13T18:25:13.0000000+00:00'
- author: Vordenburg
  changes:
    - type: Add
      message: Enhanced intrusion defenses have been deployed in electrical systems.
  id: 64
  time: '2023-10-15T14:07:21.0000000+00:00'
- author: leonardo-dabepis
  changes:
    - type: Add
      message: added mothamphetamine drink
    - type: Tweak
      message: changed "dulleavene" to "health violation"
  id: 65
  time: '2023-10-16T23:25:27.0000000+00:00'
- author: DebugOk
  changes:
    - type: Tweak
      message: >-
        Moth coloration has been changed, existing characters might look
        slightly weird
  id: 66
  time: '2023-10-17T20:09:27.0000000+00:00'
- author: Tex
  changes:
    - type: Add
      message: Added scrambled eggs.
  id: 67
  time: '2023-10-18T21:07:50.0000000+00:00'
- author: DebugOk
  changes:
    - type: Add
      message: >-
        Certain roles require whitelist now. These being the captain, head of
        security and nukie commander.
  id: 68
  time: '2023-10-18T22:23:18.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Remove
      message: Removed mediborgs as round start job.
  id: 69
  time: '2023-10-20T20:12:46.0000000+00:00'
- author: leonardo-dabepis
  changes:
    - type: Tweak
      message: >-
        Phil Dullea won his slander lawsuit and was able to change "dulleavene"
        to "health code violation"
  id: 70
  time: '2023-10-21T23:38:00.0000000+00:00'
- author: leonardo-dabepis
  changes:
    - type: Add
      message: added some missing inhand sprites for the epi clothes
    - type: Tweak
      message: mystagogue has a better hat now
  id: 71
  time: '2023-10-21T23:42:51.0000000+00:00'
- author: rosieposieeee
  changes:
    - type: Add
      message: >-
        NT has ordered extra-comfortable seating for the crew as a reward in
        lieu of a raise.
  id: 72
  time: '2023-10-22T23:30:40.0000000+00:00'
- author: TJohnson
  changes:
    - type: Add
      message: >-
        Added Laika and her crew, security working dogs that are aligned with
        the station. Best watch your step, citizens, lest your heels be nipped!
  id: 73
  time: '2023-10-23T16:25:53.0000000+00:00'
- author: JJ
  changes:
    - type: Fix
      message: Fixed 'Go back' option for Telegnostic Projection.
  id: 74
  time: '2023-10-23T16:51:48.0000000+00:00'
- author: PHCodes
  changes:
    - type: Add
      message: 'Readded psionics systems and powers. '
  id: 75
  time: '2023-10-08T18:07:53.0000000+00:00'
- author: JJ
  changes:
    - type: Add
      message: Reverse Engineering.
  id: 76
  time: '2023-10-03T22:54:24.0000000+00:00'
- author: JJ
  changes:
    - type: Fix
      message: Fixed 'Go back' option for Telegnostic Projection.
  id: 77
  time: '2023-10-23T16:51:48.0000000+00:00'
- author: PHCodes
  changes:
    - type: Add
      message: 'Added the Deep Fryer and related features. '
  id: 78
  time: '2023-10-18T21:57:11.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added Soju and Sake bottle in the Booze-O-Mat.
  id: 79
  time: '2023-10-23T19:04:14.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Remove
      message: Removed the random sentience event.
  id: 80
  time: '2023-10-23T19:17:23.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Fix
      message: Fixed Tortuga station's critical lack of hardsuits
  id: 81
  time: '2023-10-23T21:40:33.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Remove
      message: >-
        Removed prisoners from job selection, since they were spawning in
        arrivals. Warden no longer requires prisoner playtime in the meantime.
  id: 82
  time: '2023-10-23T23:12:41.0000000+00:00'
- author: VMSolidus
  changes:
    - type: Fix
      message: Harpies can now be stripped, and can randomly be psychic
  id: 83
  time: '2023-10-23T23:32:00.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Fix
      message: Fixed Mantis not having their Metapsionic Pulse.
  id: 84
  time: '2023-10-23T23:40:59.0000000+00:00'
- author: JJ
  changes:
    - type: Tweak
      message: Tweaked round weighting and lowered instances of Revs
  id: 85
  time: '2023-10-24T00:05:22.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Fix
      message: Fixed Felinids not being able to eat certain food.
  id: 86
  time: '2023-10-24T00:36:33.0000000+00:00'
- author: DebugOk
  changes:
    - type: Add
      message: Merged Wizden, hopefully fixing some bugs
  id: 87
  time: '2023-10-24T00:46:22.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Fix
      message: >-
        Fixed the Logistics department not being able to communicate on their
        radio channel.
  id: 88
  time: '2023-10-24T03:33:03.0000000+00:00'
- author: DebugOk
  changes:
    - type: Fix
      message: >-
        Merged wizden (again). Hopefully you won't visit the backrooms when
        using a medical scanner anymore.
  id: 89
  time: '2023-10-24T16:35:53.0000000+00:00'
- author: DebugOk
  changes:
    - type: Fix
      message: Whitelist finally works properly
  id: 90
  time: '2023-10-24T17:10:28.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Fix
      message: Fixed mantis stamp being an error.
  id: 91
  time: '2023-10-24T19:02:28.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Tweak
      message: The moth plushie screams and squeaks once again.
  id: 92
  time: '2023-10-24T19:03:43.0000000+00:00'
- author: JJ
  changes:
    - type: Fix
      message: 'Fixed Mindswap return action between players. '
  id: 93
  time: '2023-10-25T14:21:35.0000000+00:00'
- author: JJ
  changes:
    - type: Tweak
      message: Updated Butlertron's sprite.
  id: 94
  time: '2023-10-25T14:15:45.0000000+00:00'
- author: VMSolidus
  changes:
    - type: Fix
      message: Harpies and Vulpkanin can now be Psychic
  id: 95
  time: '2023-10-25T14:16:21.0000000+00:00'
- author: JJ
  changes:
    - type: Fix
      message: 'Mimes are now psionic. '
  id: 96
  time: '2023-10-25T14:20:40.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Tweak
      message: >-
        Head of Security no longer requires detective playtime for the time
        being.
  id: 97
  time: '2023-10-25T14:26:45.0000000+00:00'
- author: Adrian16199
  changes:
    - type: Add
      message: Added eye colouring on felinid, oni and harpy.
    - type: Add
      message: Added additional tattoes to harpys.
    - type: Fix
      message: Tattoes on felinid and oni are once again usable.
  id: 98
  time: '2023-10-25T14:28:12.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Fix
      message: Fixed missing drink locales.
  id: 99
  time: '2023-10-25T22:23:38.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Add
      message: Reimplemented Brigmedics. They're not on any maps (yet).
  id: 100
  time: '2023-10-25T22:52:49.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Fix
      message: Fixed Mystagogue not having their dispel power.
  id: 101
  time: '2023-10-26T14:55:52.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Tweak
      message: Tweaked the price of the uplink cat ears down to 4TC from 26TC.
  id: 102
  time: '2023-10-27T17:25:08.0000000+00:00'
- author: luringens
  changes:
    - type: Fix
      message: >-
        Harpies can now sing while cuffed, but not while muzzled, mute,
        zombified, or otherwise incapable of talking.
    - type: Add
      message: >-
        You can now make a harpy stop singing by slipping, stunning, or hurting
        them.
  id: 103
  time: '2023-10-27T18:13:03.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Remove
      message: Removed the traitor objective of killing Ian for his meat.
  id: 104
  time: '2023-10-27T18:18:43.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Tweak
      message: Tweaked the Moth related food to be edible by Moths.
  id: 105
  time: '2023-10-27T18:32:16.0000000+00:00'
- author: DebugOk
  changes:
    - type: Tweak
      message: Invalid job preferences are now reset to never upon loading in.
  id: 106
  time: '2023-10-28T01:58:58.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added Mothroaches in the vents.
  id: 107
  time: '2023-10-28T02:00:22.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Tweak
      message: Adjusted event weights so rounds are not as exciting.
  id: 108
  time: '2023-10-28T03:14:19.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added the classic Moth markings back, still can't be coloured.
  id: 109
  time: '2023-10-28T19:05:29.0000000+00:00'
- author: JJ
  changes:
    - type: Add
      message: Add Psionic Antag Objectives
  id: 110
  time: '2023-10-28T19:06:51.0000000+00:00'
- author: DebugOk
  changes:
    - type: Tweak
      message: The 10 reserved whitelist slots have been removed until further notice.
  id: 111
  time: '2023-10-29T03:10:27.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Tweak
      message: >-
        Altered the rules. Read the latest changes to them at the top of the
        rules list!
  id: 112
  time: '2023-10-29T17:36:46.0000000+00:00'
- author: VMSolidus
  changes:
    - type: Add
      message: Cargo can now order medical scrub crates
  id: 113
  time: '2023-10-29T17:38:19.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Remove
      message: Removed the clown hardsuit from being craftable.
  id: 114
  time: '2023-10-30T13:17:07.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Add
      message: Asterisk station has been added to the game.
  id: 115
  time: '2023-11-01T16:32:48.0000000+00:00'
- author: leonardo-dabepis
  changes:
    - type: Tweak
      message: Switched logos around
  id: 116
  time: '2023-11-01T22:45:55.0000000+00:00'
- author: TJohnson
  changes:
    - type: Tweak
      message: Security has received new models of hardsuit!
  id: 117
  time: '2023-11-02T00:31:53.0000000+00:00'
- author: ps3moira
  changes:
    - type: Add
      message: Added Two New Round-End Sounds
    - type: Remove
      message: Lobby Round-End Sounds
  id: 118
  time: '2023-11-02T01:00:16.0000000+00:00'
- author: leonardo-dabepis
  changes:
    - type: Tweak
      message: Technical Assistant now has a 4 hour playtime requirement.
  id: 119
  time: '2023-11-02T16:15:06.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Tweak
      message: >-
        Updated Asterisk Station with various fixes, a slot for an atmos
        technician, and a familiar parallax...
  id: 120
  time: '2023-11-02T20:38:51.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Remove
      message: Removed starvation!
  id: 121
  time: '2023-11-05T19:24:51.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Tweak
      message: Cat Ears have been moved from the Syndicate Uplink into the AutoDrobe.
  id: 122
  time: '2023-11-06T14:39:49.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Remove
      message: Removed the carp suit bundle from the syndicate uplink.
  id: 123
  time: '2023-11-06T14:54:09.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Fix
      message: >-
        Fixed a few issues with Asterisk, including getting stuck on Epi's park
        tree and evac docking at cargo.
  id: 124
  time: '2023-11-06T15:25:25.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: >-
        Cyborgs using the Medical model can now print candies with medicine once
        again.
  id: 125
  time: '2023-11-06T15:46:56.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Add
      message: >-
        Salvage Expedition computer boards can now be researched by Epistemics
        and produced in circuit imprinters.
  id: 126
  time: '2023-11-07T13:36:49.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Fix
      message: >-
        Fixed the Reverse Engineering Machine incorrectly decreasing scan power
        with upgrades.
  id: 127
  time: '2023-11-07T19:31:02.0000000+00:00'
- author: Velcroboy
  changes:
    - type: Tweak
      message: >-
        Rebased Arena Station. This is a major overhaul. Please report any
        issues or oddities in the mapping channel on Discord.
  id: 128
  time: '2023-11-07T19:31:54.0000000+00:00'
- author: deltanedas
  changes:
    - type: Tweak
      message: Carp suit has been moved into the AutoDrobe.
  id: 129
  time: '2023-11-07T19:32:52.0000000+00:00'
- author: VMSolidus
  changes:
    - type: Tweak
      message: >-
        Centcomm Geneticists have modified the Harpies so that they can no
        longer imitate flash bangs. Your eardrums are safe now.
  id: 130
  time: '2023-11-08T22:30:39.0000000+00:00'
- author: leonardo-dabepis
  changes:
    - type: Tweak
      message: Reskinned the Glimmer Prober and Glimmer Drain.
  id: 131
  time: '2023-11-08T22:44:38.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Fix
      message: Brought back the custom round end sounds. Collect them all!
  id: 132
  time: '2023-11-09T04:22:23.0000000+00:00'
- author: VMSolidus
  changes:
    - type: Tweak
      message: >-
        Laika has discovered how to press airlock buttons, now no evildoers are
        safe.
  id: 133
  time: '2023-11-09T19:01:30.0000000+00:00'
- author: Samsterious
  changes:
    - type: Tweak
      message: Character name restrictions are more permissive.
  id: 134
  time: '2023-11-10T15:08:02.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Tweak
      message: Revolutionaries are now fewer in number.
  id: 135
  time: '2023-11-10T15:08:26.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Add
      message: >-
        Head lockers now contain their respective head's spare ID card where
        they didn't already.
  id: 136
  time: '2023-11-11T01:25:33.0000000+00:00'
- author: Velcroboy
  changes:
    - type: Add
      message: The Hive is Alive!
  id: 137
  time: '2023-11-11T16:37:11.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Tweak
      message: Glimmer Prober and Drainer are craftable once again.
    - type: Fix
      message: Fix Probers not making sounds depending on the glimmer levels.
    - type: Add
      message: Adds back the Psionic guidebook.
  id: 138
  time: '2023-11-11T17:39:37.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Fix
      message: Fixed The Hive's AI room losing power.
  id: 139
  time: '2023-11-13T00:42:17.0000000+00:00'
- author: luringens
  changes:
    - type: Add
      message: >-
        Syndicate harpies can now purchase bionic syrinx implants to further
        enchance their mimicry skills. It's like a voice mask for your throat!
  id: 140
  time: '2023-11-13T22:42:09.0000000+00:00'
- author: Adrian16199
  changes:
    - type: Add
      message: Added Shoukou from Nyanotrasen and hopefully improved it.
  id: 141
  time: '2023-11-15T01:00:27.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added the X-01 MultiPhase Energy Gun for the HoS.
  id: 142
  time: '2023-11-15T18:41:01.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Tweak
      message: Slightly adjusted gamemode weights.
  id: 143
  time: '2023-11-16T09:49:24.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Fix
      message: Head of Personnel now has the access that lawyers have.
  id: 144
  time: '2023-11-17T23:56:56.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Tweak
      message: Adjusted Asterisk and Shoukou's min/max population to 0/60.
  id: 145
  time: '2023-11-19T06:10:32.0000000+00:00'
- author: IamVelcroboy
  changes:
    - type: Add
      message: >-
        Salvage has been rebased. New but strangely familiar space debris will
        be pulled in by the salvage magnet. Caution is recommended.
  id: 146
  time: '2023-11-22T16:13:12.0000000+00:00'
- author: tryded
  changes:
    - type: Add
      message: Added double bladed energy swords to Nuclear Operative Uplinks
  id: 147
  time: '2023-11-25T14:38:20.0000000+00:00'
- author: TJohnson
  changes:
    - type: Tweak
      message: Seclites now robust as advertised in item description.
  id: 148
  time: '2023-11-26T00:36:54.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Tweak
      message: >-
        Asterisk Station is now protected by additional meteor netting and is
        also more resistant to movement. Please relay thanks to your local
        engineers!
  id: 149
  time: '2023-11-26T00:38:30.0000000+00:00'
- author: Tryded
  changes:
    - type: Tweak
      message: >-
        Tweaked recipes of bluespace items to require bluespace crystals in
        exchange of needing less materials
  id: 150
  time: '2023-11-26T00:51:27.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Tweak
      message: >-
        Corpsmen start with a few autoinjectors in their packs, and gain
        chemistry access on low-pop rounds.
  id: 151
  time: '2023-11-26T01:21:47.0000000+00:00'
- author: Tryded
  changes:
    - type: Add
      message: >-
        Nanotrasen has recently introduced their miniature energy pistols and
        energy guns to the market and developed experimental research into the
        IK-60 laser carbine and the PDW-9 Energy Pistol. They can be researched
        by science.
  id: 152
  time: '2023-11-27T22:42:27.0000000+00:00'
- author: DebugOk
  changes:
    - type: Tweak
      message: >-
        Mindshields are now made with significantly cheaper components, and they
        dont fuse properly anymore. Meaning they can be removed with a little
        effort.
  id: 153
  time: '2023-12-03T00:48:41.0000000+00:00'
- author: Velcroboy
  changes:
    - type: Fix
      message: Fixed air chamber in The Hive's atmos not having a vacuum.
  id: 154
  time: '2023-12-05T19:16:18.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Tweak
      message: Vulpkanin now have a decent amount of special helmet and mask sprites.
  id: 155
  time: '2023-12-06T08:16:15.0000000+00:00'
- author: luringens
  changes:
    - type: Add
      message: Prisoners are back!
    - type: Fix
      message: >-
        Gladiators, prisoners and prison guards start the shift at their
        predesignated locations.
  id: 156
  time: '2023-12-06T15:54:37.0000000+00:00'
- author: Tryded
  changes:
    - type: Add
      message: Added energy guns to dynamic protolathe emagable recipes.
  id: 157
  time: '2023-12-06T15:57:01.0000000+00:00'
- author: Ygg01
  changes:
    - type: Add
      message: Digging dirt.
  id: 158
  time: '2023-12-06T15:58:00.0000000+00:00'
- author: Velcroboy
  changes:
    - type: Tweak
      message: Buffed Food Cart storage. Get out there and slang some burgers, chefs!
  id: 159
  time: '2023-12-06T15:58:43.0000000+00:00'
- author: ps3moira
  changes:
    - type: Add
      message: >-
        Added mouse operative reinforcements, which can be bought from the
        Syndicate uplink
  id: 160
  time: '2023-12-06T16:03:07.0000000+00:00'
- author: evilexecutive
  changes:
    - type: Add
      message: Harpies now have a visual indication when they're playing a Midi.
    - type: Tweak
      message: >-
        Harpies have been re-balanced so that they now actually have a numerical
        positive.
  id: 161
  time: '2023-12-06T20:10:49.0000000+00:00'
- author: DebugOk
  changes:
    - type: Tweak
      message: Due to abuse, prisoners now require whitelist to play.
  id: 162
  time: '2023-12-07T13:22:03.0000000+00:00'
- author: ps3moira
  changes:
    - type: Remove
      message: Removed bionic syrinx implanter from surplus crate.
  id: 163
  time: '2023-12-11T23:11:41.0000000+00:00'
- author: ps3moira
  changes:
    - type: Remove
      message: Removed Carpotoxin from Sashimi, now they are sushi-grade!
  id: 164
  time: '2023-12-13T20:12:46.0000000+00:00'
- author: Adrian16199
  changes:
    - type: Add
      message: Crew has learned how to make a straw hat out of wheat bushels!
  id: 165
  time: '2023-12-13T20:13:45.0000000+00:00'
- author: IamVelcroboy
  changes:
    - type: Add
      message: Added Yule! Happy Holidays!
  id: 166
  time: '2023-12-13T20:21:34.0000000+00:00'
- author: ps3moira
  changes:
    - type: Add
      message: Added Fish n' Chips. Thank the British!
  id: 167
  time: '2023-12-13T20:32:51.0000000+00:00'
- author: Adrian16199
  changes:
    - type: Tweak
      message: Felinids now meow out their words. They can also sigh now.
  id: 168
  time: '2023-12-13T21:28:39.0000000+00:00'
- author: VMSolidus
  changes:
    - type: Fix
      message: Harpies no longer choke on completely breathable air
  id: 169
  time: '2023-12-14T05:13:43.0000000+00:00'
- author: UnicornOnLSD
  changes:
    - type: Tweak
      message: Changed bags of holding cost to be more consistent.
  id: 170
  time: '2023-12-14T09:17:35.0000000+00:00'
- author: Adrian16199
  changes:
    - type: Fix
      message: Fixed non-wizden species not having markings.
  id: 171
  time: '2023-12-17T21:59:15.0000000+00:00'
- author: Adrian16199
  changes:
    - type: Add
      message: Vulpkanins can sigh now.
  id: 172
  time: '2023-12-17T22:00:12.0000000+00:00'
- author: luringens
  changes:
    - type: Tweak
      message: >-
        Chaplain now gets a glimmer monitor app on their PDA like the rest of
        epistemics.
  id: 173
  time: '2023-12-19T16:19:34.0000000+00:00'
- author: leonardo-dabepis
  changes:
    - type: Remove
      message: >-
        the clown has received medical treatment for his chronic snoring. No
        more HONK mimimimimi
  id: 174
  time: '2023-12-20T03:01:27.0000000+00:00'
- author: deltanedas
  changes:
    - type: Add
      message: >-
        The fishops advanced user manual is now available in all finer
        bookstores near you.
  id: 175
  time: '2023-12-21T02:08:37.0000000+00:00'
- author: Velcroboy and Kilk
  changes:
    - type: Add
      message: Added departmental winter boots!
  id: 176
  time: '2023-12-21T02:12:04.0000000+00:00'
- author: Loonessia
  changes:
    - type: Fix
      message: Deep fried burned food items should now have a description
  id: 177
  time: '2023-12-21T02:15:30.0000000+00:00'
- author: Adrian16199
  changes:
    - type: Add
      message: Head of personel has gotten back their armoured coat.
  id: 178
  time: '2023-12-21T02:17:25.0000000+00:00'
- author: DebugOk
  changes:
    - type: Add
      message: Merged wizden. The upstream changelog may contain incorrect entries.
    - type: Fix
      message: The random client freezes/crashes should be mostly gone.
  id: 179
  time: '2023-12-22T23:46:32.0000000+00:00'
- author: ps3moira
  changes:
    - type: Tweak
      message: Reverted in-hand E-Sword sprites.
  id: 180
  time: '2023-12-24T02:56:01.0000000+00:00'
- author: deltanedas
  changes:
    - type: Tweak
      message: Ore Bags of Holding now use bluespace crystals instead of uranium.
  id: 181
  time: '2023-12-24T19:58:23.0000000+00:00'
- author: Adrian16199
  changes:
    - type: Fix
      message: Fixes cyborgs spawning with nearsighted trait.
  id: 182
  time: '2023-12-27T17:55:07.0000000+00:00'
- author: Adrian16199
  changes:
    - type: Add
      message: 'Added 3 classic hairstyles: Long hair, Long hair 2, Long hair 3.'
  id: 183
  time: '2023-12-27T18:23:39.0000000+00:00'
- author: BramvanZijp
  changes:
    - type: Tweak
      message: >-
        Security dogs now can wear an id card and radio, are able to drag
        things, and have received additional combat training.
  id: 184
  time: '2023-12-28T09:32:10.0000000+00:00'
- author: Adrian16199
  changes:
    - type: Fix
      message: Gives shoukou an oxygen miner until air actualy works.
  id: 185
  time: '2023-12-28T21:38:57.0000000+00:00'
- author: Master2112
  changes:
    - type: Add
      message: >-
        Crime Assist program to Security and Lawyer PDA's, to help identify a
        crime with a simple yes/no question tree.
  id: 186
  time: '2023-12-29T19:34:56.0000000+00:00'
- author: ps3moira
  changes:
    - type: Add
      message: Added Scottish Trait.
    - type: Add
      message: Added Kilt to Theatervend.
  id: 187
  time: '2023-12-29T19:44:44.0000000+00:00'
- author: deltanedas
  changes:
    - type: Tweak
      message: Mail no longer has throwing damage.
  id: 188
  time: '2023-12-29T19:53:10.0000000+00:00'
- author: Velcroboy
  changes:
    - type: Remove
      message: Space winter is officially over!
  id: 189
  time: '2023-12-30T14:15:42.0000000+00:00'
- author: Master2112
  changes:
    - type: Fix
      message: Fixed incorrect reference to ftl in crimeassist yaml
  id: 190
  time: '2023-12-30T22:03:53.0000000+00:00'
- author: Adrian16199
  changes:
    - type: Add
      message: Added a fluffy tail for the felinids.
  id: 191
  time: '2023-12-30T22:05:52.0000000+00:00'
- author: DarkenedSynergy
  changes:
    - type: Fix
      message: Fixed logistics airlocks looking like engineering airlocks when opened.
  id: 192
  time: '2023-12-31T16:23:10.0000000+00:00'
- author: DangerRevolution and VMSolidus
  changes:
    - type: Add
      message: >-
        Rumours say that Syndicate Agents have cracked encoded communications!
        Added the Syndicate Listening Post to the Syndicate's arsenal of
        espionage!
  id: 193
  time: '2023-12-31T16:48:27.0000000+00:00'
- author: VMSolidus and DangerRevolution
  changes:
    - type: Tweak
      message: >-
        The Listening Outpost has received several tweaks and balance changes.
        It should spawn less often now.
  id: 194
  time: '2024-01-01T21:33:58.0000000+00:00'
- author: ps3moira
  changes:
    - type: Tweak
      message: Reverted Shotgun sprites
  id: 195
  time: '2024-01-01T21:48:08.0000000+00:00'
- author: Velcroboy
  changes:
    - type: Add
      message: Added BB guns! Don't shoot your eye out.
  id: 196
  time: '2024-01-02T22:03:49.0000000+00:00'
- author: deltanedas
  changes:
    - type: Tweak
      message: On very high pop revolution rounds, there can be 2 head revolutionaries.
  id: 197
  time: '2024-01-02T22:06:22.0000000+00:00'
- author: VMSolidus
  changes:
    - type: Fix
      message: There should only ever be one Listening Outpost spawning at a time
  id: 198
  time: '2024-01-02T23:05:25.0000000+00:00'
- author: Adrian16199
  changes:
    - type: Add
      message: 'Added maiden outfits into janidrobe. '
  id: 199
  time: '2024-01-07T14:57:38.0000000+00:00'
- author: Adrian16199
  changes:
    - type: Fix
      message: >-
        Prison guards are now mindshielded and have a security playtime
        requirement.
  id: 200
  time: '2024-01-07T18:20:45.0000000+00:00'
- author: DebugOk
  changes:
    - type: Fix
      message: Atmos hopefully shouldn't break anymore
  id: 201
  time: '2024-01-07T22:10:36.0000000+00:00'
- author: Bribrooo
  changes:
    - type: Add
      message: 'added a ChemVend inside of Chemistry on Pebble Station '
  id: 202
  time: '2024-01-11T06:14:27.0000000+00:00'
- author: Adrian
  changes:
    - type: Tweak
      message: Shoukou has maintenance now in logistics and in security
  id: 203
  time: '2024-01-12T22:08:47.0000000+00:00'
- author: DebugOk
  changes:
    - type: Add
      message: Added the spare ID safe
    - type: Remove
      message: The captain's locker no longer starts with a spare ID card
  id: 204
  time: '2024-01-14T23:48:30.0000000+00:00'
- author: Velcroboy
  changes:
    - type: Add
      message: Added Hammurabi Prison Station. Crime must be punished!
  id: 205
  time: '2024-01-17T03:53:36.0000000+00:00'
- author: Gregg
  changes:
    - type: Tweak
      message: Adjusted the rules in regards to PDA confiscation and borg memory.
  id: 206
  time: '2024-01-19T10:30:34.0000000+00:00'
- author: DebugOk
  changes:
    - type: Remove
      message: Zombies can no longer be psionic
  id: 207
  time: '2024-01-19T20:53:05.0000000+00:00'
- author: Adrian16199
  changes:
    - type: Fix
      message: Fixes mantis and them not be able to spawn with duffelbags and satchels.
  id: 208
  time: '2024-01-19T20:54:29.0000000+00:00'
- author: Adrian16199
  changes:
    - type: Fix
      message: Laika no longer takes 200 damage before dying from critical.
  id: 209
  time: '2024-01-19T20:57:11.0000000+00:00'
- author: lleftTheDragon
  changes: []
  id: 210
  time: '2024-01-19T21:06:32.0000000+00:00'
- author: DebugOk
  changes:
    - type: Tweak
      message: Enabled gamerule voting on periapsis
    - type: Tweak
      message: Enabled map voting on periapsis
  id: 211
  time: '2024-01-19T22:33:21.0000000+00:00'
- author: DebugOk
  changes:
    - type: Add
      message: Added gridinv, sorry.
  id: 212
  time: '2024-01-21T18:58:28.0000000+00:00'
- author: TadJohnson00
  changes:
    - type: Tweak
      message: >-
        Pride vend has received an adjusted inventory. Make sure to check out
        the new cloaks available!
  id: 213
  time: '2024-01-30T14:22:13.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Tweak
      message: Updated Asterisk Station.
  id: 214
  time: '2024-01-30T21:09:41.0000000+00:00'
- author: DebugOk
  changes:
    - type: Tweak
      message: >-
        You should be able to exit cryo even after leaving now, meaning it just
        keeps your body safe?
  id: 215
  time: '2024-01-31T07:41:58.0000000+00:00'
- author: DebugOk
  changes:
    - type: Fix
      message: Prisoners will no longer spawn in cryopods instead of in the brig.
  id: 216
  time: '2024-01-31T09:42:57.0000000+00:00'
- author: leonardo-dabepis
  changes:
    - type: Tweak
      message: Made HCV cocktail less immediately lethal (damnation!!!)
  id: 217
  time: '2024-01-31T10:34:16.0000000+00:00'
- author: Adrian16199
  changes:
    - type: Tweak
      message: Shoukou has received the cryopods.
  id: 218
  time: '2024-01-31T15:34:32.0000000+00:00'
- author: DebugOk
  changes:
    - type: Tweak
      message: >-
        Prisoners can now spawn from a special cryopod that can be put in
        security, instead of magically appearing in a cell.
  id: 219
  time: '2024-01-31T22:02:05.0000000+00:00'
- author: DangerRevolution
  changes:
    - type: Add
      message: Added sprites for Syndicate Listener's Headsets
  id: 220
  time: '2024-02-01T18:29:24.0000000+00:00'
- author: leonardo-dabepis
  changes:
    - type: Add
      message: a new pest from beyond the noosphere!
  id: 221
  time: '2024-02-01T18:32:28.0000000+00:00'
- author: Adrian16199
  changes:
    - type: Add
      message: An alternative wagging tail for felinids.
  id: 222
  time: '2024-02-01T18:32:54.0000000+00:00'
- author: ODJ
  changes:
    - type: Tweak
      message: Moth's now take less heat dmg.
    - type: Tweak
      message: You now move a little faster when hurt than previously
  id: 223
  time: '2024-02-01T18:34:18.0000000+00:00'
- author: Rose and Unicorn On LSD
  changes:
    - type: Add
      message: >-
        Added Submarine Station. A new map with a heavy emphasis on the medical
        dept. Features also include a full zoo, park with an ice rink, and some
        sort of experiment-gone-bad deep within the epistemics labs.
  id: 224
  time: '2024-02-01T19:30:23.0000000+00:00'
- author: UnicornOnLSD
  changes:
    - type: Tweak
      message: Submarine station has received adjustments after feedback from HR
  id: 225
  time: '2024-02-03T06:50:49.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Tweak
      message: Updated Edge Station.
  id: 226
  time: '2024-02-04T02:30:44.0000000+00:00'
- author: UnicornOnLSD
  changes:
    - type: Tweak
      message: Submarine has been made better, stronger, and not faster.
  id: 227
  time: '2024-02-04T02:31:24.0000000+00:00'
- author: Velcroboy
  changes:
    - type: Tweak
      message: 'Tweaked the space ruins to be slightly more dangerous. '
  id: 228
  time: '2024-02-05T21:11:50.0000000+00:00'
- author: Colin-Tel
  changes:
    - type: Tweak
      message: Asterisk's TEG has been adjusted after receiving player feedback.
  id: 229
  time: '2024-02-05T21:22:18.0000000+00:00'
- author: UnicornOnLSD
  changes:
    - type: Tweak
      message: Submarine got better telecomms, a revamped psych. ward and more !
  id: 230
  time: '2024-02-07T02:32:33.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Fix
      message: Fixed PSB not being edible by Reptilian
  id: 231
  time: '2024-02-08T15:38:11.0000000+00:00'
- author: Velcroboy
  changes:
    - type: Fix
      message: Fixed martial artist access
  id: 232
  time: '2024-02-08T15:39:24.0000000+00:00'
- author: IamVelcroboy
  changes:
    - type: Tweak
      message: >-
        More improvements all around to Submarine Station as well as the
        addition of medical cyborgs.
  id: 233
  time: '2024-02-08T19:21:16.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Fix
      message: Fixed missing Mantis stamp sprite.
  id: 234
  time: '2024-02-09T10:47:35.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Tweak
      message: .38 Special is now in the ammo techfab.
    - type: Fix
      message: Fixed .38 Special Mindbreaker not breaking minds.
  id: 235
  time: '2024-02-10T23:17:28.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Fix
      message: Fixed glimmer mite being extremely loud for some.
  id: 236
  time: '2024-02-10T23:21:56.0000000+00:00'
- author: Adrian16199
  changes:
    - type: Add
      message: Added a speech bubble to felinids. Mraow!
  id: 237
  time: '2024-02-12T20:51:35.0000000+00:00'
- author: DebugOk
  changes:
    - type: Add
      message: Felinids now fit in bags again!
  id: 238
  time: '2024-02-12T20:52:49.0000000+00:00'
- author: Guess-My-Name
  changes:
    - type: Add
      message: >-
        Some budget cuts were made in HR department, expect more bureaucratic
        errors in near future.
  id: 239
  time: '2024-02-12T21:01:44.0000000+00:00'
- author: Adrian16199
  changes:
    - type: Tweak
      message: Felinid's thieving gloves have been removed for Soft paws mechanic.
  id: 240
  time: '2024-02-12T21:10:44.0000000+00:00'
- author: Adrian16199
  changes:
    - type: Add
      message: Felinids now scream in agony from water.
  id: 241
  time: '2024-02-12T21:11:44.0000000+00:00'
- author: VMSolidus
  changes:
    - type: Add
      message: >-
        Harpies have received a full visual rework. Featuring clothing sprites,
        new markings, a complete overhaul of their layering, as well as visual
        systems.
    - type: Add
      message: Harpies now have bird related speech verbs
    - type: Add
      message: >-
        Two new traits have been added related to birds. Harpies now start by
        default with the new Ultraviolet Vision trait, letting them see a whole
        new spectrum of colors. This trait can be taken by other curious people.
        Harpies that wish to see in RGB colors can instead take the new Normal
        Vision trait.
  id: 242
  time: '2024-02-12T22:25:02.0000000+00:00'
- author: DebugOk
  changes:
    - type: Tweak
      message: >-
        Glimmer probers now react violently to being hit by gorilla gauntlets if
        glimmer is above 500
  id: 243
  time: '2024-02-13T00:59:01.0000000+00:00'
- author: VMSolidus
  changes:
    - type: Fix
      message: Removed the ability of Harpies to destroy people's ears.
  id: 244
  time: '2024-02-13T13:29:37.0000000+00:00'
- author: deltanedas
  changes:
    - type: Add
      message: >-
        Brought back paradox anomalies. Beware anyone that looks *just* like
        you!
  id: 245
  time: '2024-02-13T15:55:35.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Add
      message: Added Deuteranopia trait.
    - type: Tweak
      message: >-
        Vulpkanin now start with Deuteranopia by default which can be removed
        using the Normal Vision trait.
  id: 246
  time: '2024-02-15T17:27:24.0000000+00:00'
- author: FluffiestFloof
  changes:
    - type: Tweak
      message: Glimmer Drain now requires 5 Normality Crystals to craft.
  id: 247
  time: '2024-02-17T03:38:14.0000000+00:00'
- author: VMSolidus
  changes:
    - type: Add
      message: 'Metempsychosis machines have been added to the game. '
  id: 248
  time: '2024-02-18T02:36:16.0000000+00:00'
- author: DebugOk
  changes:
    - type: Add
      message: Merged upstream
    - type: Fix
      message: Fixed the upstream changelog not rendering
  id: 249
  time: '2024-02-18T23:05:48.0000000+00:00'
- author: DebugOk
  changes:
    - type: Tweak
      message: Whitelist to join now works based on slots instead of player count
    - type: Tweak
      message: Periapsis now has only 10 non-whitelisted slots
  id: 250
  time: '2024-02-20T19:46:17.0000000+00:00'
- author: rosieposieeee
  changes:
    - type: Remove
      message: >-
        Submarine Station has been temporarily removed from rotation for routine
        maintenance.
  id: 251
  time: '2024-02-21T01:15:08.0000000+00:00'
