command-description-visualize = Отримує вхідний список сутностей і поміщає їх у вікно інтерфейсу для зручного перегляду.
command-description-runverbas = Виконує дієслово над вхідними сутностями з заданим користувачем.
command-description-acmd-perms = Повертає права адміністратора даної команди, якщо такі є.
command-description-acmd-caninvoke = Перевірте, чи може даний гравець викликати задану команду.
command-description-jobs-jobs = Повертає всі роботи на станції.
command-description-jobs-job = Повертає задану роботу на станції.
command-description-jobs-isinfinite = Повертає true, якщо дана робота нескінченна, інакше false.
command-description-jobs-adjust = Регулює кількість слотів для даного завдання.
command-description-jobs-set = Задає кількість слотів для даної роботи.
command-description-jobs-amount = Повертає кількість слотів для заданої роботи.
command-description-laws-list = Повертає список всіх створінь, які мають закони.
command-description-laws-get = Повертає всі закони для заданої сутності.
command-description-stations-list = Повертає список усіх станцій.
command-description-stations-get = Отримує активну станцію, якщо і тільки якщо вона є тільки одна.
command-description-stations-getowningstation = Отримує станцію, якій "належить" даний об'єкт (в межах якої він знаходиться)
command-description-stations-grids = Повертає всі сітки, пов'язані з вхідною станцією.
command-description-stations-config = Повертає конфігурацію, пов'язану з вхідною станцією, якщо така є.
command-description-stations-addgrid = Додає сітку до заданої станції.
command-description-stations-rmgrid = Видаляє сітку з даної станції.
command-description-stations-rename = Перейменовує задану станцію.
command-description-stations-largestgrid = Повертає найбільшу сітку, яку має дана станція, якщо така є.
command-description-stations-rerollBounties = Очищає всі поточні винагороди для станції і отримує новий вибір.
command-description-stationevent-lsprob = Перераховує ймовірність виникнення різних подій на станціях з усього пулу.
command-description-stationevent-lsprobtime = Виводить ймовірність настання різних подій на станції на основі заданої довжини раунду.
command-description-stationevent-prob = Повертає ймовірність того, що відбудеться подія для однієї станції з усього пулу.
command-description-admins-active = Повертає список активних адміністраторів.
command-description-admins-all = Повертає список ВСІХ адміністраторів, в тому числі і ті, які зараз у деадміні.
command-description-marked = Повертає значення $marked як List<EntityUid>.
command-description-rejuvenate = Омолоджує дані сутності, повертаючи їм повне здоров'я, очищаючи статусні ефекти тощо.
command-description-tag-list = Виводить список тегів для заданих сутностей.
command-description-tag-add = Додає тег до заданих сутностей.
command-description-tag-rm = Видаляє тег із заданих сутностей.
command-description-tag-addmany = Додає список тегів до заданих сутностей.
command-description-tag-rmmany = Видаляє список тегів із заданих сутностей.
command-description-polymorph = Поліморфує вхідний об'єкт за заданим прототипом.
command-description-unpolymorph = Повертає поліморф.
command-description-solution-get = Отримує заданий розв'язок від заданої сутності.
command-description-solution-adjreagent = Налаштовує заданий реагент на заданий розчин.
command-description-mind-get = Забирає розум у сутності, якщо такий є.
command-description-mind-control = Перебирає контроль над сутністю з даним гравцем.
command-description-addaccesslog = Додає журнал доступу до цієї сутності. Зауважте, що це оминає ліміт журналу за замовчуванням і перевірку на призупинення.

command-description-stationevent-simulate = Моделює N кількість раундів, у яких відбудуться події, і виводить значення кожної події після кожного раунду.