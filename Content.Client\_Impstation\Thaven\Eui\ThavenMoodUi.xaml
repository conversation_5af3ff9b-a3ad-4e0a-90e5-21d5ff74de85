<controls:FancyWindow
    xmlns="https://spacestation14.io"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
    Title="{Loc thaven-moods-admin-ui-title}"
    MinSize="560 400"
>
    <!-->
    this shit does not layout properly unless I put the horizontal boxcontainer inside of a vertical one
    ????
    <!-->
    <BoxContainer Orientation="Vertical">
        <BoxContainer Orientation="Horizontal" Align="End">
            <Button Name="NewMoodButton" Text="{Loc thaven-moods-admin-ui-new-mood}" MaxSize="256 64" StyleClasses="OpenRight"></Button>
            <Button Name="SaveButton" Text="{Loc thaven-moods-admin-ui-save}" MaxSize="256 64" Access="Public" StyleClasses="OpenLeft"></Button>
        </BoxContainer>
    </BoxContainer>
    <BoxContainer Orientation="Vertical" Margin="4 60 0 0">
        <ScrollContainer VerticalExpand="True" HorizontalExpand="True" HScrollEnabled="False">
            <BoxContainer Orientation="Vertical" Name="MoodContainer" Access="Public" VerticalExpand="True" />
        </ScrollContainer>
    </BoxContainer>
</controls:FancyWindow>
