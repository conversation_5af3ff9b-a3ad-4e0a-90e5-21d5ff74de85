using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client._CorvaxNext.CrewMedal.UI;

[GenerateTypedNameReferences]
public sealed partial class CrewMedalWindow : DefaultWindow
{
    /// <summary>
    /// Event triggered when the "Save" button is pressed, 
    /// provided the user has changed the reason text.
    /// </summary>
    public event Action<string>? OnReasonChanged;

    private bool _isFocused;
    private string _reason = string.Empty;
    private bool _awarded;
    private int _maxCharacters = 50;

    public CrewMedalWindow()
    {
        RobustXamlLoader.Load(this);

        ReasonLineEdit.OnTextChanged += _ =>
        {
            // Check character limit and award status
            SaveButton.Disabled = _awarded || ReasonLineEdit.Text.Length > _maxCharacters;
            CharacterLabel.Text = Loc.GetString(
                "crew-medal-ui-character-limit", 
                ("number", ReasonLineEdit.Text.Length), 
                ("max", _maxCharacters));
        };

        ReasonLineEdit.OnFocusEnter += _ => _isFocused = true;
        ReasonLineEdit.OnFocusExit += _ => _isFocused = false;

        SaveButton.OnPressed += _ =>
        {
            OnReasonChanged?.Invoke(ReasonLineEdit.Text);
            SaveButton.Disabled = true;
        };

        // Initialize the character counter display
        CharacterLabel.Text = Loc.GetString(
            "crew-medal-ui-character-limit", 
            ("number", ReasonLineEdit.Text.Length), 
            ("max", _maxCharacters));
    }

    /// <summary>
    /// Sets the current reason and synchronizes it with the input field 
    /// if the user is not currently editing the field.
    /// </summary>
    public void SetCurrentReason(string reason)
    {
        if (_reason == reason)
            return;

        _reason = reason;

        // Synchronize text if the input field is not focused
        if (!_isFocused)
            ReasonLineEdit.Text = _reason;
    }

    /// <summary>
    /// Updates the "is medal awarded" status 
    /// and disables editing if the medal is already awarded.
    /// </summary>
    public void SetAwarded(bool awarded)
    {
        _awarded = awarded;
        ReasonLineEdit.Editable = !_awarded;
        SaveButton.Disabled = _awarded;
    }

    /// <summary>
    /// Updates the maximum character limit for the reason.
    /// If the current text exceeds the limit, it will be truncated.
    /// </summary>
    public void SetMaxCharacters(int number)
    {
        _maxCharacters = number;
        if (ReasonLineEdit.Text.Length > _maxCharacters)
            ReasonLineEdit.Text = ReasonLineEdit.Text[.._maxCharacters];
    }
}
