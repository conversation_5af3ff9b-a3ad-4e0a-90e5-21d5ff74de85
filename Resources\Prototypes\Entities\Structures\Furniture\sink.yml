- type: entity
  name: "раковина"
  id: SinkEmpty
  suffix: Empty
  description: "Крани були затягнуті з максимально можливим моментом, але вони все одно капають."
  placement:
    mode: SnapgridCenter
  components:
  - type: Clickable
  - type: InteractionOutline
  - type: Sprite
    sprite: Structures/Furniture/sink.rsi
    layers:
      - state: sink_stem
      - map: [ "enum.SolutionContainerLayers.Fill" ]
        state: sink-fill-1
        visible: false
  - type: Appearance
  - type: SolutionContainerVisuals
    maxFillLevels: 1
    fillBaseName: sink-fill-
    solutionName: drainBuffer
  - type: SolutionContainerManager
    solutions:
      drainBuffer:
        maxVol: 100
      tank:
        maxVol: 500
  - type: SolutionRegeneration
    solution: tank
    generated:
      reagents:
      - ReagentId: Water
        Quantity: 1
  - type: DrainableSolution
    solution: tank
  - type: ReagentTank
  - type: Drain
    autoDrain: false
  - type: DumpableSolution
    solution: drainBuffer
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 80
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
      - trigger:
          !type:DamageTrigger
          damage: 40
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]
          - !type:PlaySoundBehavior
            sound:
              collection: MetalBreak
  - type: AmbientSound
    enabled: false
    volume: -8
    range: 8
    sound:
      path: /Audio/Ambience/Objects/drain.ogg


- type: entity
  name: "раковина"
  id: Sink
  parent: SinkEmpty
  suffix: Water
  components:
  - type: SolutionContainerManager
    solutions:
      drainBuffer:
        maxVol: 200
      tank:
        reagents:
        - ReagentId: Water
          Quantity: 500

- type: entity
  name: "широка раковина"
  id: SinkWide
  parent: Sink
  components:
    - type: Sprite
      sprite: Structures/Furniture/sink.rsi
      layers:
        - state: sink_wide
        - map: [ "enum.SolutionContainerLayers.Fill" ]
          state: sink_wide-fill-1
          visible: false
    - type: SolutionContainerVisuals
      maxFillLevels: 1
      fillBaseName: sink_wide-fill-
      solutionName: drainBuffer

#Stemless Sink

- type: entity
  name: "раковина"
  id: SinkStemless
  parent: SinkEmpty
  components:
  - type: Sprite
    sprite: Structures/Furniture/sink.rsi
    layers:
      - state: sink
      - map: [ "enum.SolutionContainerLayers.Fill" ]
        state: sink-fill-1
        visible: false

- type: entity
  name: "раковина"
  id: SinkStemlessWater
  parent: SinkStemless
  suffix: Water
  components:
  - type: SolutionContainerManager
    solutions:
      drainBuffer:
        maxVol: 100
      tank:
        reagents:
        - ReagentId: Water
          Quantity: 500
