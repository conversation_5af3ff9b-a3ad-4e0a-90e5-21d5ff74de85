# If you're looking at the rsi for this file, you'll probably be confused why
# I didn't just use an alpha for most of this stuff. Well icons don't have the
# ability to have applied colors yet in GUIs. And also inhands... -Swept

- type: entity
  id: CableStack
  abstract: true
  parent: BaseItem
  name: "кабельний стек"
  suffix: Full
  components:
  - type: Tag
    tags:
    - CableCoil
  - type: Stack
    stackType: Cable
  - type: Sprite
    sprite: Objects/Tools/cable-coils.rsi
  - type: Item
    sprite: Objects/Tools/cable-coils.rsi
    size: Small
    storedRotation: -90
  - type: CablePlacer
  - type: Clickable
  - type: StaticPrice
    price: 0
  - type: StackPrice
    price: 1
  - type: PhysicalComposition
    materialComposition:
      Steel: 15

- type: entity
  id: CableHVStack
  parent: CableStack
  name: "Котушка кабелю HV"
  suffix: Full
  description: "Високовольтні кабелі для підключення двигунів до важкої техніки, малих і середніх підприємств та підстанцій."
  components:
  - type: Stack
    stackType: CableHV
    baseLayer: base
    layerStates:
    - coilhv-10
    - coilhv-20
    - coilhv-30
  - type: Sprite
    state: coilhv-30
    layers:
    - state: coilhv-30
      map: ["base"]
  - type: Item
    heldPrefix: coilhv
  - type: CablePlacer
    cablePrototypeID: CableHV
    blockingWireType: HighVoltage
  - type: Appearance
  - type: Extractable
    grindableSolutionName: hvcable
  - type: SolutionContainerManager
    solutions:
      hvcable:
        reagents:
        - ReagentId: Iron
          Quantity: 3
        - ReagentId: Copper
          Quantity: 2
        - ReagentId: Carbon #steel-reinforced
          Quantity: 1
  - type: Healing # HV Cables are drastically slower to use, but provide very good healing (-240 per stack as opposed to -150 for ointment or -255 for mesh.)
    delay: 1.8
    damageContainers:
    - Silicon
    damage:
      types: # these are all split across multiple types. IPCs can still take cold damage, they just heavily resist it.
        Heat: -8
        Shock: -8
        Cold: -2
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 100
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  parent: CableHVStack
  id: CableHVStack10
  suffix: 10
  components:
  - type: Sprite
    state: coilhv-10
  - type: Stack
    count: 10

- type: entity
  parent: CableHVStack10
  id: CableHVStackLingering10
  suffix: Lingering, 10
  components:
  - type: Stack
    lingering: true
    count: 10

- type: entity
  parent: CableHVStack
  id: CableHVStack1
  suffix: 1
  components:
  - type: Sprite
    state: coilhv-10
  - type: Stack
    count: 1

- type: entity
  parent: CableStack
  id: CableMVStack
  name: "Котушка кабелю MV"
  suffix: Full
  description: "Кабелі високої напруги для з'єднання підстанцій з АПЦ, а також для живлення окремих пристроїв, наприклад, випромінювачів."
  components:
  - type: Stack
    stackType: CableMV
    baseLayer: base
    layerStates:
    - coilmv-10
    - coilmv-20
    - coilmv-30
  - type: Sprite
    state: coilmv-30
    layers:
    - state: coilmv-30
      map: ["base"]
  - type: Item
    heldPrefix: coilmv
  - type: CablePlacer
    cablePrototypeID: CableMV
    blockingWireType: MediumVoltage
  - type: Appearance
  - type: Extractable
    grindableSolutionName: mvcable
  - type: SolutionContainerManager
    solutions:
      mvcable:
        reagents:
        - ReagentId: Iron
          Quantity: 3
        - ReagentId: Copper
          Quantity: 2
  - type: Healing # MV Cables are slower to use, but provide better healing (-120 per stack as opposed to -150 for ointment or -255 for mesh.)
    delay: 1.2
    damageContainers:
    - Silicon
    damage:
      types: # these are all split across multiple types. IPCs can still take cold damage, they just heavily resist it.
        Heat: -4
        Shock: -4
        Cold: -1
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 85
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  parent: CableMVStack
  id: CableMVStack10
  suffix: 10
  components:
  - type: Sprite
    state: coilmv-10
  - type: Stack
    count: 10

- type: entity
  parent: CableMVStack10
  id: CableMVStackLingering10
  suffix: Lingering, 10
  components:
  - type: Stack
    lingering: true
    count: 10

- type: entity
  parent: CableMVStack
  id: CableMVStack1
  suffix: 1
  components:
  - type: Sprite
    state: coilmv-10
  - type: Stack
    count: 1

- type: entity
  parent: CableStack
  id: CableApcStack
  name: "Котушка кабелю LV"
  description: "Низьковольтний пучок проводів для підключення АПЦ до машин та інших цілей."
  suffix: Full
  components:
  - type: Sprite
    state: coillv-30
    layers:
    - state: coillv-30
      map: ["base"]
  - type: Item
    heldPrefix: coillv
  - type: Stack
    baseLayer: base
    layerStates:
    - coillv-10
    - coillv-20
    - coillv-30
  - type: CablePlacer
    cablePrototypeID: CableApcExtension
    blockingWireType: Apc
  - type: Appearance
  - type: Extractable
    grindableSolutionName: lvcable
  - type: SolutionContainerManager
    solutions:
      lvcable:
        reagents:
        - ReagentId: Iron
          Quantity: 3
        - ReagentId: Copper
          Quantity: 2
  - type: Healing # LV Cables are quick to use one or two in combat, but provide mediocre healing (-60 per stack as opposed to -150 for ointment or -255 for mesh.)
    delay: 0.6
    damageContainers:
    - Silicon
    damage:
      types: # these are all split across multiple types. IPCs can still take cold damage, they just heavily resist it.
        Heat: -2
        Shock: -2
        Cold: -0.5
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.25,0.25,0.25"
        density: 50
        mask:
        - ItemMask
        restitution: 0.3
        friction: 0.2

- type: entity
  parent: CableApcStack
  id: CableApcStack10
  suffix: 10
  components:
    - type: Sprite
      state: coillv-10
    - type: Stack
      count: 10

- type: entity
  parent: CableApcStack10
  id: CableApcStackLingering10
  suffix: Lingering, 10
  components:
  - type: Stack
    lingering: true
    count: 10

- type: entity
  parent: CableApcStack
  id: CableApcStack1
  suffix: 1
  components:
  - type: Sprite
    state: coillv-10
  - type: Stack
    count: 1
