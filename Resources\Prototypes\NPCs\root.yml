# Specific Root compound tasks being used for NPCs.

# Tries to shoot a target in LOS in range.
- type: htnCompound
  id: TurretCompound
  branches:
    - tasks:
        - !type:HTNPrimitiveTask
          operator: !type:UtilityOperator
            proto: NearbyGunTargets

        - !type:HTNPrimitiveTask
          preconditions:
            - !type:KeyExistsPrecondition
              key: Target
            - !type:TargetInRangePrecondition
              targetKey: Target
              # TODO: Non-scuffed
              rangeKey: RangedRange
            - !type:TargetInLOSPrecondition
              targetKey: Target
              rangeKey: RangedRange
          operator: !type:GunOperator
            targetKey: Target
            requireLOS: true
          services:
            - !type:UtilityService
              id: RangedService
              proto: NearbyGunTargets
              key: Target

    - tasks:
        - !type:HTNCompoundTask
          task: IdleSpinCompound

- type: htnCompound
  id: EnergyTurretCompound
  branches:
    - tasks:
        - !type:HTNPrimitiveTask
          operator: !type:UtilityOperator
            proto: NearbyGunTargets

        - !type:HTNPrimitiveTask
          preconditions:
            - !type:KeyExistsPrecondition
              key: Target
            - !type:TargetInRangePrecondition
              targetKey: Target
              # TODO: Non-scuffed
              rangeKey: RangedRange
            - !type:TargetInLOSPrecondition
              targetKey: Target
              rangeKey: RangedRange
              opaqueKey: true
          operator: !type:GunOperator
            targetKey: Target
            opaqueKey: true
          services:
            - !type:UtilityService
              id: RangedService
              proto: NearbyGunTargets
              key: Target

    - tasks:
        - !type:HTNCompoundTask
          task: IdleSpinCompound

- type: htnCompound
  id: SimpleRangedHostileCompound
  branches:
    - tasks:
        - !type:HTNCompoundTask
          task: InnateRangedCombatCompound
    - tasks:
        - !type:HTNCompoundTask
          task: MeleeCombatCompound
    - tasks:
        - !type:HTNCompoundTask
          task: IdleCompound

- type: htnCompound
  id: SimpleHumanoidHostileCompound
  branches:
    - tasks:
        - !type:HTNCompoundTask
          task: RangedCombatCompound
    - tasks:
        - !type:HTNCompoundTask
          task: MeleeCombatCompound
    - tasks:
        - !type:HTNCompoundTask
          task: IdleCompound
