# Empty mags
- type: entity
  id: BaseMagazineLightRifle
  name: "магазин (гвинтівка калібру .30)"
  parent: BaseItem
  abstract: true
  components:
  - type: Tag
    tags:
      - MagazineLightRifle
  - type: BallisticAmmoProvider
    mayTransfer: true
    whitelist:
      tags:
        - CartridgeLightRifle
    proto: CartridgeLightRifle
    capacity: 30
  - type: Item
    size: Small
  - type: ContainerContainer
    containers:
      ballistic-ammo: !type:Container
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Magazine/LightRifle/light_rifle_mag.rsi
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]
  - type: MagazineVisuals
    magState: mag
    steps: 2
    zeroVisible: false
  - type: Appearance

# Magazines
- type: entity
  id: MagazineLightRifleBox
  name: "коробчатий магазин (.30 гвинтівковий)"
  parent: BaseMagazineLightRifle
  components:
  - type: Tag
    tags:
      - MagazineLightRifleBox
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifle
    capacity: 100
  - type: Item
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Magazine/LightRifle/light_rifle_box.rsi
  - type: MagazineVisuals
    magState: mag
    steps: 8
    zeroVisible: false
  - type: Appearance

- type: entity
  id: MagazineLightRifleBoxPractice
  name: "коробчатий магазин (.30 гвинтівковий, тренувальний)"
  parent: MagazineLightRifleBox
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRiflePractice

- type: entity
  id: MagazineLightRifleBoxRubber
  name: "коробчатий магазин (.30 гвинтівковий гумовий)"
  parent: MagazineLightRifleBox
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifleRubber

- type: entity
  id: MagazineLightRifleBoxIncendiary
  name: "коробчатий магазин (.30 гвинтівковий запальний)"
  parent: MagazineLightRifleBox
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifleIncendiary

- type: entity
  id: MagazineLightRifleBoxUranium
  name: "коробчатий магазин (.30 гвинтівковий, урановий)"
  parent: MagazineLightRifleBox
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifleUranium

- type: entity
  id: MagazineLightRifleBoxShrapnel
  name: "коробка магазинів L6 SAW (.30 гвинтівковий, шрапнельний)"
  parent: MagazineLightRifleBox
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifleShrapnel

- type: entity
  id: MagazineLightRifle
  name: "магазин (гвинтівка калібру .30)"
  parent: BaseMagazineLightRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifle
  - type: Sprite
    layers:
    - state: red
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineLightRifleEmpty
  name: "магазин (.30 гвинтівковий будь-який)"
  suffix: empty
  parent: MagazineLightRifle
  components:
  - type: BallisticAmmoProvider
    proto: null
  - type: Sprite
    layers:
    - state: base
      map: ["enum.GunVisualLayers.Base"]

- type: entity
  id: MagazineLightRiflePractice
  name: "магазин (практична стрільба з гвинтівки калібру .30)"
  parent: BaseMagazineLightRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRiflePractice
  - type: Sprite
    layers:
    - state: practice
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineLightRifleRubber
  name: "магазин (.30 гвинтівковий гумовий)"
  parent: BaseMagazineLightRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifleRubber
  - type: Sprite
    layers:
    - state: rubber
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineLightRifleUranium
  name: "магазин (.30 гвинтівочний уран)"
  parent: BaseMagazineLightRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifleUranium
  - type: Sprite
    layers:
    - state: uranium
      map: ["enum.GunVisualLayers.Base"]
    - state: mag-1
      map: ["enum.GunVisualLayers.Mag"]

- type: entity
  id: MagazineLightRifleIncendiary
  name: "магазин (.30 гвинтівковий запальний)"
  parent: MagazineLightRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifleIncendiary

- type: entity
  id: MagazineLightRifleShrapnel
  name: "магазин (.30 гвинтівковий, шрапнельний)"
  parent: BaseMagazineLightRifle
  components:
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifleShrapnel

- type: entity
  id: MagazineLightRifleMaxim
  name: "магазин під набій (гвинтівка калібру .30)"
  parent: BaseMagazineLightRifle
  components:
  - type: Tag
    tags:
      - MagazineLightRiflePan
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifle
    capacity: 96
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Magazine/LightRifle/maxim.rsi

- type: entity
  id: MagazineLightRiflePkBox
  name: "коробочка з набоями PK (гвинтівка калібру .30)"
  parent: BaseMagazineLightRifle
  components:
  - type: Tag
    tags:
      - MagazineLightRifleBox
  - type: BallisticAmmoProvider
    proto: CartridgeLightRifle
    capacity: 80
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Magazine/LightRifle/pk_box.rsi
  - type: MagazineVisuals
    magState: mag
    steps: 7
    zeroVisible: false
  - type: Appearance
