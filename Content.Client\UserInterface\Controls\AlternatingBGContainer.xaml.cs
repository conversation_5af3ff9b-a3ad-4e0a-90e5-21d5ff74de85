using System.Linq;
using System.Numerics;
using Robust.Client.AutoGenerated;
using Robust.Client.Graphics;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Timing;


namespace Content.Client.UserInterface.Controls;

/// <summary>
///     A box container that gives every child an alternating background color
/// </summary>
[GenerateTypedNameReferences]
public sealed partial class AlternatingBGContainer : BoxContainer
{
    /// If the <see cref="ContentContainer"/>'s horizontal scroll is enabled
    public bool HScrollEnabled => ContentScrollContainer.HScrollEnabled;
    /// If the <see cref="ContentContainer"/>'s vertical scroll is enabled
    public bool VScrollEnabled => ContentScrollContainer.VScrollEnabled;

    public new bool HorizontalExpand
    {
        get => base.HorizontalExpand;
        set
        {
            base.HorizontalExpand = value;
            Container.HorizontalExpand = value;
            ContentScrollContainer.HorizontalExpand = value;
            ContentContainer.HorizontalExpand = value;
        }
    }
    public new bool VerticalExpand
    {
        get => base.VerticalExpand;
        set
        {
            base.VerticalExpand = value;
            Container.VerticalExpand = value;
            ContentScrollContainer.VerticalExpand = value;
            ContentContainer.VerticalExpand = value;
        }
    }

    public Thickness ItemMargins = new(4);

    /// The list of colors to alternate between
    public List<Color> Colors = new()
    {
        Color.FromHex("#25252A"),
        // Color.FromHex("#202025"),
        Color.FromHex("#1B1B1E"),
    };


    /// <inheritdoc cref="AlternatingBGContainer"/>
    public AlternatingBGContainer()
    {
        RobustXamlLoader.Load(this);
    }

    private bool _initialized;
    /// Stupid hack, when will I get an initialized event?
    /// This makes anything added via AddControl before first frame show up before children
    protected override void FrameUpdate(FrameEventArgs args)
    {
        base.FrameUpdate(args);

        if (_initialized)
            return;

        foreach (var child in Children.ToArray())
            if (child != Container)
                AddControl(child);
        UpdateColors(); // Just in case
        _initialized = true;
    }

    /// Move a control to the container and set their background color
    public void AddControl(Control control)
    {
        var panel = new PanelContainer
        {
            PanelOverride = new StyleBoxFlat { BackgroundColor = Colors[ContentContainer.Children.Count(c => c.Visible) % Colors.Count], },
            HorizontalExpand = true,
            VerticalExpand = true,
            Visible = control.Visible,
        };

        // Please let me add Thicknesses
        control.Margin = new(control.Margin.Left + ItemMargins.Left, control.Margin.Top + ItemMargins.Top, control.Margin.Right + ItemMargins.Right, control.Margin.Bottom + ItemMargins.Bottom);
        control.Orphan();
        panel.AddChild(control);
        ContentContainer.AddChild(panel);
    }

    /// Updates the colors of every child
    public void UpdateColors()
    {
        var list = ContentContainer.Children.Where(c => c.Visible).ToList();
        foreach (var child in list)
        {
            if (child is PanelContainer panel)
                ((StyleBoxFlat) panel.PanelOverride!).BackgroundColor = Colors[list.IndexOf(panel) % Colors.Count];
            child.Visible = child.Children.First().Visible;
        }
    }
}
