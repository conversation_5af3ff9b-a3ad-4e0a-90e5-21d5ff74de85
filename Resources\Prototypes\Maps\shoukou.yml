- type: gameMap
  id: Shoukou
  mapName: 'Шōкō'
  mapPath: /Maps/shoukou.yml
  minPlayers: 0
  stations:
    Shoukou:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: '{0} Shōkō "Little Port" {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: 'DV'
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/DeltaV/NTES_Delta.yml
        - type: StationJobs
          overflowJobs:
            - Passenger
          availableJobs:
          #Service
            Passenger: [ -1, -1 ]
            ServiceWorker: [ 1, 2]
            Reporter: [ 0, 1 ]
            Lawyer: [ 1, 1 ]
            Bartender: [ 1, 2 ]
            Botanist: [ 1, 2 ]
            # MartialArtist: [ 2, 3 ] # EE: Disabled
            Chef: [ 1, 2 ]
            Clown: [ 1, 1 ]
            Janitor: [ 1, 2 ]
            Musician: [ 1, 1]
            Mime: [ 1, 1 ]
          #Command
            Captain: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1]
            NanotrasenRepresentative: [ 1, 1 ]
            # Magistrate: [ 1, 1 ] # EE: Disabled
            # AdministrativeAssistant: [ 1, 1 ] # EE: Disabled
            HeadOfPersonnel: [ 1, 1 ]
          #Engineering
            ChiefEngineer: [ 1, 1 ]
            StationEngineer: [ 2, 5 ]
            TechnicalAssistant: [ 2, 3 ]
            AtmosphericTechnician: [ 1, 2 ]
          #Medical
            ChiefMedicalOfficer: [ 1, 1 ]
            Paramedic: [ 1, 2 ]
            MedicalDoctor: [ 3, 3 ]
            Chemist: [ 1, 2 ]
            MedicalIntern: [ 2, 3 ]
            Psychologist: [ 1, 1 ]
          #Security
            HeadOfSecurity: [ 1, 1 ]
            SecurityOfficer: [ 2, 4 ]
            Detective: [ 1, 1 ]
            Warden: [ 1, 1 ]
            SecurityCadet: [ 1, 3 ]
            Brigmedic: [ 1, 1 ]
          #Science
            ResearchDirector: [ 1, 1 ]
            Roboticist: [ 1, 1 ] #PIRATE
            Scientist: [ 2, 4 ]
            ResearchAssistant: [ 2, 3 ]
            Chaplain: [ 1, 1 ]
            ForensicMantis: [ 1, 1 ]
            Borg: [ 2, 2 ]
          #Logistics
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ 2, 4 ]
            MailCarrier: [ 1, 2 ]
            CargoTechnician: [ 2, 3 ]
          # Silicon
            StationAi: [ 1, 1 ]
        # blob-config-start SMALL+
        - type: StationBlobConfig
          stageBegin: 25
          stageCritical: 350
          stageTheEnd: 700
        # blob-config-end
