#
# Base component for consumable food
#
- type: entity
  parent: BaseItem
  id: FoodBase
  abstract: true
  components:
  - type: FlavorProfile
    flavors:
      - food
  - type: Food
  - type: SpaceGarbage
  - type: Sprite
  - type: StaticPrice
    price: 0

# This base type is used to cover all of the "obvious" things that should be doable to open-package food.
# Practically this means injection.
# But it might in future also mean drawing with a syringe, so this is a base prototype just in case.

- type: entity
  parent: FoodBase
  id: FoodInjectableBase
  abstract: true
  components:
  - type: InjectableSolution
    solution: food
  - type: RefillableSolution
    solution: food

# usable by any food that can be opened
# handles appearance with states "icon" and "icon-open"
- type: entity
  id: FoodOpenableBase
  abstract: true
  components:
  - type: Appearance
  - type: Sprite
    layers:
    - state: icon
      map: ["icon"]
  - type: GenericVisualizer
    visuals:
      enum.OpenableVisuals.Opened:
        icon:
          True: {state: "icon-open"}
          False: {state: "icon"}
  - type: Openable
