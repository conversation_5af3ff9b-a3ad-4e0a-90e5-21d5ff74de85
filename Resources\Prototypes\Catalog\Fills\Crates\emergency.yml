- type: entity
  id: CrateEmergencyExplosive
  parent: CrateSecgear
  name: "ящик з бомбозахисним костюмом"
  description: "Науковий відділ збожеволів? Щось біпає за шлюзом? Купуйте зараз і станьте героєм, який був потрібний станції... Тобто, треба зараз! (час не включено)"
  components:
  - type: StorageFill
    contents:
      - id: ClothingHeadHelmetBombSuit
      - id: ClothingMaskGas
      - id: Screwdriver
      - id: Wirecutter
      - id: Multitool
      - id: ClothingOuterSuitBomb

- type: entity
  id: CrateEmergencyFire
  parent: CrateGenericSteel
  name: "ящик пожежника"
  description: "Тільки ви можете запобігти пожежі на станції. Візьміть з собою два костюми пожежника, протигази, ліхтарики, великі кисневі балони, вогнегасники та каски!"
  components:
  - type: StorageFill
    contents:
      - id: FlashlightLantern
        amount: 2
      - id: FireExtinguisher
        amount: 2
      - id: ClothingHeadHatHardhatRed
        amount: 2
      - id: ClothingMaskGas
        amount: 2
      - id: ClothingOuterSuitFire
        amount: 2
      - id: OxygenTankFilled
        amount: 2

- type: entity
  id: CrateEmergencyInternals
  parent: CrateInternals
  name: "ящик для безповітряного простору"
  description: "Контролюйте свою життєву енергію та дихання за допомогою трьох дихальних масок, трьох аварійних кисневих балонів та трьох великих повітряних балонів."
  components:
  - type: StorageFill
    contents:
      - id: ClothingMaskGas
        amount: 3
      - id: ClothingMaskBreath
        amount: 3
      - id: OxygenTankFilled
        amount: 3
      - id: NitrogenTankFilled
        amount: 3
      - id: ClothingOuterSuitEmergency
        amount: 3

- type: entity
  id: CrateEmergencyInternalsLarge
  parent: CrateInternals
  name: "ящик для безповітряного простору (великий)"
  description: "Контролюйте свою життєву енергію та дихання за допомогою шести дихальних масок, шести аварійних кисневих балонів та шести великих повітряних балонів."
  components:
    - type: StorageFill
      contents:
        - id: ClothingMaskGas
          amount: 6
        - id: ClothingMaskBreath
          amount: 6
        - id: OxygenTankFilled
          amount: 6
        - id: NitrogenTankFilled
          amount: 6
        - id: ClothingOuterSuitEmergency
          amount: 6

- type: entity
  id: CrateSlimepersonLifeSupport
  parent: CrateInternals
  name: "ящик життєзабезпечення слаймолюдей"
  description: "Містить чотири дихальні маски та чотири великі балони з азотом."
  components:
  - type: StorageFill
    contents:
      - id: ClothingMaskGas
        amount: 2
      - id: ClothingMaskBreath
        amount: 2
      - id: NitrogenTankFilled
        amount: 4

- type: entity
  id: CratePlasmaInternals
  parent: CrateInternals
  name: "внутрішній ящик (плазма)"
  description: "Містить чотири дихальні маски та чотири резервуари для внутрішньої плазми. Призначений для Плазмаменів."
  components:
  - type: StorageFill
    contents:
      - id: ClothingMaskGas
        amount: 2
      - id: ClothingMaskBreath
        amount: 2
      - id: DoubleEmergencyPlasmaTankFilled
        amount: 4

- type: entity
  id: CratePlasmamanEnvirosuit
  parent: CrateInternals
  name: "ящик плазмового екокостюму"
  description: "Містить два повних комплекти аерокостюмів, дві респіраторні маски та два резервуари для плазми. Призначений для плазмаменів."
  components:
  - type: StorageFill
    contents:
      - id: ClothingUniformEnvirosuit
        amount: 2
      - id: ClothingHeadEnvirohelmEmpty
        amount: 2
      - id: ClothingHandsGlovesEnvirogloves
        amount: 2
      - id: ClothingMaskBreath
        amount: 2
      - id: DoubleEmergencyPlasmaTankFilled
        amount: 2

- type: entity
  id: CrateEmergencyRadiation
  parent: CrateRadiation
  name: "ящик з захистом від радіації"
  description: "Переживіть ядерний апокаліпсис і коллапс надматерії з двома комплектами костюмів захисту від радіації. Кожен комплект містить шолом, костюм та лічильник Гейгера. Ми навіть докинемо пляшку горілки та кілька склянок, враховуючи тривалість життя людей, які замовляють це."
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterSuitRad
        amount: 2
      - id: GeigerCounter
        amount: 2
      - id: DrinkVodkaBottleFull
      - id: DrinkShotGlass
        amount: 2

- type: entity
  id: CrateEmergencyInflatablewall
  parent: CratePlastic
  name: "ящик надувних стін"
  description: "Три стоси надувних стін на випадок, якщо металеві стіни станції більше не хочуть утримувати атмосферу."
  components:
  - type: StorageFill
    contents:
      - id: BoxInflatable

- type: entity
  id: CrateGenericBiosuit
  parent: CratePlastic
  name: "ящик з аварійним біокостюмом"
  description: "Містить 2 костюми біозахисту, щоб гарантувати, що жодна хвороба не відволікатиме вас від того, що ви там робите."
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterBioGeneral
        amount: 2
      - id: ClothingHeadHatHoodBioGeneral
        amount: 2
      - id: ClothingMaskGas
        amount: 2
