﻿- type: entity
  id: PowerSink
  parent: BaseMachine
  name: "викачувач енергії"
  description: "Викачує величезну кількість електроенергії з мережі."
  components:
    - type: Item
      size: Huge
    - type: NodeContainer
      examinable: true
      nodes:
        input:
          !type:CableDeviceNode
          nodeGroupID: HVPower
    - type: Transform
      anchored: true
    - type: Physics
    - type: Fixtures
      fixtures:
        fix1:
          shape:
            !type:PhysShapeAabb
            bounds: "-0.40,-0.40,0.40,0.40"
          density: 90
          mask:
          - MachineMask
          layer:
          - MachineLayer
    - type: Destructible
      thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 25
        behaviors:
        - !type:DoActs<PERSON>ehavior
          acts: [ "Destruction" ]
    - type: PowerSink
    - type: Battery
      maxCharge: 7500000
      pricePerJoule: 0.0003
    - type: ExaminableBattery
    - type: PowerConsumer
      voltage: High
      drawRate: 1000000
    - type: Sprite
      sprite: Objects/Power/powersink.rsi
      state: powersink
    - type: WarpPoint
      location: powersink
