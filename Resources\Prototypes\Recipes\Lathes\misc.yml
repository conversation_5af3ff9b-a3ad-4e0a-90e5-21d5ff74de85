- type: latheRecipe
  id: LightTube
  result: LightTube
  category: Lights
  completetime: 2
  materials:
    Steel: 50
    Glass: 50

- type: latheRecipe
  id: LedLightTube
  result: LedLightTube
  category: Lights
  completetime: 2
  materials:
    Steel: 50
    Glass: 50

- type: latheRecipe
  id: SodiumLightTube
  result: SodiumLightTube
  category: Lights
  completetime: 2
  materials:
    Steel: 50
    Glass: 50

- type: latheRecipe
  id: ExteriorLightTube
  result: ExteriorLightTube
  category: Lights
  completetime: 2
  materials:
    Steel: 50
    Glass: 50

- type: latheRecipe
  id: LightBulb
  result: LightBulb
  category: Lights
  completetime: 2
  materials:
    Steel: 50
    Glass: 50

- type: latheRecipe
  id: LedLightBulb
  result: LedLightBulb
  category: Lights
  completetime: 2
  materials:
    Steel: 50
    Glass: 50

- type: latheRecipe
  id: GlowstickRed
  result: GlowstickRed
  category: Lights
  completetime: 2
  materials:
    Plastic: 50

- type: latheRecipe
  id: Flare
  result: Flare
  category: Lights
  completetime: 2
  materials:
    Plastic: 50

- type: latheRecipe
  id: FlashlightLantern
  result: EmptyFlashlightLantern
  category: Lights
  completetime: 2
  materials:
    Steel: 100
    Glass: 100
    Plastic: 100

- type: latheRecipe
  id: FireExtinguisher
  result: FireExtinguisher
  category: Tools
  completetime: 2
  materials:
    Steel: 200

- type: latheRecipe
  id: Matchbox
  result: Matchbox
  completetime: 1
  materials:
    Wood: 100

- type: latheRecipe
  id: SynthesizerInstrument
  result: SynthesizerInstrument
  completetime: 4
  materials:
    Steel: 300
    Plastic: 300
    Glass: 100

- type: latheRecipe
  id: NodeScanner
  result: NodeScanner
  category: Tools
  completetime: 2
  materials:
    Steel: 100
    Plastic: 50

- type: latheRecipe
  id: AirTank
  result: AirTank
  completetime: 4
  materials:
    Steel: 300

- type: latheRecipe
  id: ClothingShoesBootsMag
  result: ClothingShoesBootsMag
  completetime: 10
  materials:
    Steel: 1000
    Plastic: 500

- type: latheRecipe
  id: ClothingShoesBootsMoon
  result: ClothingShoesBootsMoon
  completetime: 2
  materials:
    Steel: 600

- type: latheRecipe
  id: ClothingShoesBootsSpeed
  result: ClothingShoesBootsSpeed
  completetime: 2
  materials:
    Steel: 1500
    Plastic: 1000
    Silver: 500

- type: latheRecipe
  id: ModularReceiver
  result: ModularReceiver
  completetime: 12
  materials:
    Steel: 750
    Plastic: 100

- type: latheRecipe
  id: FauxTileAstroGrass
  result: FloorTileItemAstroGrass
  completetime: 1
  materials:
    Plastic: 100

- type: latheRecipe
  id: FauxTileMowedAstroGrass
  result: FloorTileItemMowedAstroGrass
  completetime: 1
  materials:
    Plastic: 100

- type: latheRecipe
  id: FauxTileJungleAstroGrass
  result: FloorTileItemJungleAstroGrass
  completetime: 1
  materials:
    Plastic: 100

- type: latheRecipe
  id: FauxTileAstroIce
  result: FloorTileItemAstroIce
  completetime: 1
  materials:
    Plastic: 100

- type: latheRecipe
  id: FauxTileAstroSnow
  result: FloorTileItemAstroSnow
  completetime: 1
  materials:
    Plastic: 100

- type: latheRecipe
  id: FloorGreenCircuit
  result: FloorTileItemGCircuit4
  completetime: 2
  materials:
    Steel: 100

- type: latheRecipe
  id: FloorBlueCircuit
  result: FloorTileItemBCircuit4
  completetime: 2
  materials:
    Steel: 100

- type: latheRecipe
  id: HandheldStationMap
  result: HandheldStationMapEmpty
  completetime: 2
  materials:
    Steel: 300
    Plastic: 100

- type: latheRecipe
  id: ClothingHeadHatWelding
  result: ClothingHeadHatWelding
  completetime: 2
  materials:
    Steel: 400
    Glass: 200

- type: latheRecipe
  id: ClothingShoesBootsMagAdv
  result: ClothingShoesBootsMagAdv
  completetime: 12
  materials:
    Silver: 1000
    Gold: 500

- type: latheRecipe
  id: MailCapsule
  result: MailCapsulePrimed
  completetime: 1
  materials:
    Glass: 100
    Plastic: 100
