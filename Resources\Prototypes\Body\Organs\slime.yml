- type: entity
  id: SentientSlimeCore
  parent: [BaseItem, OrganHumanBrain]
  name: "розумне ядро слизу"
  description: "Джерело неймовірної, нескінченної липкості."
  components:
    - type: Sprite
      sprite: Mobs/Species/Slime/organs.rsi
      state: brain-slime
    - type: Stomach
    - type: Organ
      slotId: core
    - type: Metabolizer
      maxReagents: 6
      metabolizerTypes: [ Slime ]
      removeEmpty: true
      groups:
        - id: Food
        - id: Drink
        - id: Medicine
        - id: Poison
        - id: Narcotic
        - id: Alcohol
          rateModifier: 0.25
    - type: SolutionContainerManager
      solutions:
        stomach:
          maxVol: 50.0
        food:
          maxVol: 5
          reagents:
          - ReagentId: GreyMatter
            Quantity: 5
        organ:
          reagents:
          - ReagentId: Slime
            Quantity: 10


- type: entity
  id: OrganSlimeLungs
  parent: BaseHumanOrgan
  name: "слаймові газові мішечки"
  description: "Збирають азот, який клітини слизу використовують для підтримки життєдіяльності."
  components:
  - type: Sprite
    sprite: Mobs/Species/Slime/organs.rsi
    layers:
      - state: lung-l-slime
      - state: lung-r-slime
  - type: Organ # Shitmed
    slotId: lungs
  - type: Lung
    alert: LowNitrogen
  - type: Metabolizer
    removeEmpty: true
    solutionOnBody: false
    solution: "Lung"
    metabolizerTypes: [ Slime ]
    groups:
    - id: Gas
      rateModifier: 100.0
  - type: SolutionContainerManager
    solutions:
      organ:
        reagents:
        - ReagentId: Slime
          Quantity: 10
      Lung:
        maxVol: 100.0
        canReact: false
      food:
        maxVol: 5
        reagents:
        - ReagentId: UncookedAnimalProteins
          Quantity: 5