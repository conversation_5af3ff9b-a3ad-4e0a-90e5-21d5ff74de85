- type: reagent
  id: JuiceApple
  name: reagent-name-juice-apple
  parent: BaseJuice
  desc: reagent-desc-juice-apple
  physicalDesc: reagent-physical-desc-crisp
  flavor: apple
  color: "#FDAD01"
  recognizable: true

- type: reagent
  id: JuiceBanana
  name: reagent-name-juice-banana
  parent: BaseJuice
  desc: reagent-desc-juice-banana
  physicalDesc: reagent-physical-desc-crisp
  flavor: banana
  color: "#FFE777"

- type: reagent
  id: JuiceBerry
  name: reagent-name-juice-berry
  parent: BaseJuice
  desc: reagent-desc-juice-berry
  physicalDesc: reagent-physical-desc-sweet
  flavor: berry
  color: "#660099"

- type: reagent
  id: JuiceCarrot
  name: reagent-name-juice-carrot
  parent: BaseJuice
  desc: reagent-desc-juice-carrot
  physicalDesc: reagent-physical-desc-crisp
  flavor: carrot
  color: "#FF8820"
  metabolisms:
    Drink:
      effects:
      - !type:SatiateThirst
        factor: 2
      - !type:AdjustReagent
        reagent: Oculine
        amount: 0.5
      - !type:AdjustReagent
        reagent: Nutriment
        amount: 0.5

- type: reagent
  id: JuiceGrape
  name: reagent-name-juice-grape
  parent: BaseJuice
  desc: reagent-desc-juice-grape
  physicalDesc: reagent-physical-desc-crisp
  flavor: juice
  color: "#512284"

- type: reagent
  id: JuiceLemon
  name: reagent-name-juice-lemon
  parent: BaseJuice
  desc: reagent-desc-juice-lemon
  physicalDesc: reagent-physical-desc-citric
  flavor: sour
  color: "#fff690"
  metamorphicSprite:
    sprite: Objects/Consumable/Drinks/lemonjuiceglass.rsi
    state: icon_empty
  metamorphicMaxFillLevels: 5
  metamorphicFillBaseName: fill-
  metamorphicChangeColor: false

- type: reagent
  id: JuiceLime
  name: reagent-name-juice-lime
  parent: BaseJuice
  desc: reagent-desc-juice-lime
  physicalDesc: reagent-physical-desc-citric
  flavor: sour
  color: "#99bb43"

# /datum/reagent/drink/orangejuice/on_mob_life(var/mob/living/M)

# if(..())
# return 1

# if(M.getToxLoss() && prob(20))
# M.adjustToxLoss(-REM)

- type: reagent
  id: JuiceOrange
  name: reagent-name-juice-orange
  parent: BaseJuice
  desc: reagent-desc-juice-orange
  physicalDesc: reagent-physical-desc-citric
  flavor: orange
  color: "#E78108"
  metamorphicSprite:
    sprite: Objects/Consumable/Drinks/orangejuiceglass.rsi
    state: icon_empty
  metamorphicMaxFillLevels: 5
  metamorphicFillBaseName: fill-
  metamorphicChangeColor: false

- type: reagent
  id: JuicePineapple
  name: reagent-name-juice-pineapple
  parent: BaseJuice
  desc: reagent-desc-juice-pineapple
  physicalDesc: reagent-physical-desc-tropical
  flavor: pineapple
  color: yellow

- type: reagent
  id: JuicePotato
  name: reagent-name-juice-potato
  parent: BaseJuice
  desc: reagent-desc-juice-potato
  physicalDesc: reagent-physical-desc-starchy
  flavor: potatoes
  color: "#302000"

- type: reagent
  id: JuiceTomato
  name: reagent-name-juice-tomato
  parent: BaseJuice
  desc: reagent-desc-juice-tomato
  physicalDesc: reagent-physical-desc-saucey
  flavor: tomato
  color: "#731008"

- type: reagent
  id: JuiceWatermelon
  name: reagent-name-juice-watermelon
  parent: BaseJuice
  desc: reagent-desc-juice-watermelon
  physicalDesc: reagent-physical-desc-sweet
  flavor: watermelon
  color: "#EF3520"
  metamorphicSprite:
    sprite: Objects/Consumable/Drinks/watermelonglass.rsi
    state: icon_empty
  metamorphicMaxFillLevels: 4
  metamorphicFillBaseName: fill-
  metamorphicChangeColor: false

- type: reagent
  id: JuiceCherry
  name: reagent-name-juice-cherry
  parent: BaseJuice
  desc: reagent-desc-juice-cherry
  physicalDesc: reagent-physical-desc-sweet
  flavor: cherry
  color: "#84031a"
