- type: entity
  parent: ClothingNeckBase
  id: ClothingNeckMedalBase
  abstract: true
  name: "медаль"
  description: "нікому не дається"
  components:
  - type: Tag
    tags:
    - WhitelistChameleon

- type: entity
  parent: ClothingNeckMedalBase
  id: ClothingNeckBronzeheart
  name: "медаль \"Бронзове серце"
  description: "Надається членам екіпажу за зразкову хоробрість перед обличчям небезпеки."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Medals/bronzeheart.rsi
  - type: Clothing
    sprite: Clothing/Neck/Medals/bronzeheart.rsi
  - type: Tag
    tags:
      - Medal
    # Corvax-Next-CrewMedals-Start
  - type: CrewMedal
  - type: ActivatableUI
    key: enum.CrewMedalUiKey.Key
    inHandsOnly: true
  - type: UserInterface
    interfaces:
      enum.CrewMedalUiKey.Key:
        type: CrewMedalBoundUserInterface
    # Corvax-Next-CrewMedals-End

- type: entity
  parent: ClothingNeckMedalBase
  id: ClothingNeckGoldmedal
  name: "золота медаль за командну роботу"
  description: "Надається членам екіпажу, які демонструють відмінну роботу в команді."
  components:
  - type: Sprite
    sprite: Clothing/Neck/Medals/gold.rsi
  - type: Clothing
    sprite: Clothing/Neck/Medals/gold.rsi
  - type: StealTarget
    stealGroup: ClothingNeckGoldmedal
  - type: Tag
    tags:
      - Medal
    # Corvax-Next-CrewMedals-Start
  - type: CrewMedal
  - type: ActivatableUI
    key: enum.CrewMedalUiKey.Key
    inHandsOnly: true
  - type: UserInterface
    interfaces:
      enum.CrewMedalUiKey.Key:
        type: CrewMedalBoundUserInterface
    # Corvax-Next-CrewMedals-End

- type: entity
  parent: ClothingNeckMedalBase
  id: ClothingNeckCargomedal
  name: "медаль за логістику" # DeltaV - Logistics Department replacing Cargo
  description: "Чи то за відмінну бухгалтерію, мужню рятувальну роботу, чи просто за доброзичливість - ця медаль призначається лише за найкращу роботу у відділі матеріально-технічного забезпечення." # DeltaV - Logistics Department replacing Cargo. Updated description for flavour
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Neck/Medals/cargomedal.rsi # DeltaV - resprite
  - type: Clothing
    sprite: DeltaV/Clothing/Neck/Medals/cargomedal.rsi
  - type: Tag
    tags:
      - Medal
    # Corvax-Next-CrewMedals-Start
  - type: CrewMedal
  - type: ActivatableUI
    key: enum.CrewMedalUiKey.Key
    inHandsOnly: true
  - type: UserInterface
    interfaces:
      enum.CrewMedalUiKey.Key:
        type: CrewMedalBoundUserInterface
    # Corvax-Next-CrewMedals-End

- type: entity
  parent: ClothingNeckMedalBase
  id: ClothingNeckEngineermedal
  name: "медаль інженера"
  description: "Чи то завдяки підтримці дихання екіпажу, ретельному переобладнанню та модернізації, чи просто наявності великої кількості матеріалів під рукою - ця медаль присуджується лише за найкращу роботу в інженерному відділі." # DeltaV - Updated description for flavour
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Neck/Medals/engineermedal.rsi # DeltaV - resprite
  - type: Clothing
    sprite: DeltaV/Clothing/Neck/Medals/engineermedal.rsi
  - type: Tag
    tags:
      - Medal
    # Corvax-Next-CrewMedals-Start
  - type: CrewMedal
  - type: ActivatableUI
    key: enum.CrewMedalUiKey.Key
    inHandsOnly: true
  - type: UserInterface
    interfaces:
      enum.CrewMedalUiKey.Key:
        type: CrewMedalBoundUserInterface
    # Corvax-Next-CrewMedals-End

- type: entity
  parent: ClothingNeckMedalBase
  id: ClothingNeckMedicalmedal
  name: "медична медаль"
  description: "Чи то перше прибуття на місце нещасного випадку, чи то найдбайливіший догляд за хворим, чи то просто турбота про те, щоб пацієнти не з'їхали з глузду - ця медаль призначається лише за найкращу роботу в медичному відділенні." # DeltaV - Updated description for flavour
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Neck/Medals/medicalmedal.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Neck/Medals/medicalmedal.rsi
  - type: Tag
    tags:
      - Medal
    # Corvax-Next-CrewMedals-Start
  - type: CrewMedal
  - type: ActivatableUI
    key: enum.CrewMedalUiKey.Key
    inHandsOnly: true
  - type: UserInterface
    interfaces:
      enum.CrewMedalUiKey.Key:
        type: CrewMedalBoundUserInterface
    # Corvax-Next-CrewMedals-End

- type: entity
  parent: ClothingNeckMedalBase
  id: ClothingNeckSciencemedal
  name: "медаль за епістемологію" # DeltaV - Epistemics Department replacing Science
  description: "Незалежно від того, чи це просування на межі сучасної науки і технологій, чи вилазка на безкраї простори ноосфери, чи просто принесення кави - ця медаль призначається лише за найкращу роботу в галузі епістеміки." # DeltaV - Epistemics Department replacing Science. Updated description for flavour
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Neck/Medals/sciencemedal.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Neck/Medals/sciencemedal.rsi
  - type: Tag
    tags:
      - Medal
    # Corvax-Next-CrewMedals-Start
  - type: CrewMedal
  - type: ActivatableUI
    key: enum.CrewMedalUiKey.Key
    inHandsOnly: true
  - type: UserInterface
    interfaces:
      enum.CrewMedalUiKey.Key:
        type: CrewMedalBoundUserInterface
    # Corvax-Next-CrewMedals-End

- type: entity
  parent: ClothingNeckMedalBase
  id: ClothingNeckSecuritymedal
  name: "медаль за безпеку"
  description: "Затримання озброєних терористів, охорона екіпажу від інопланетних загроз чи просто розмова в коридорі - ця медаль призначається лише за найкращу роботу у відділі безпеки." # DeltaV - Updated description for flavour
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Neck/Medals/securitymedal.rsi
  - type: Clothing
    sprite: DeltaV/Clothing/Neck/Medals/securitymedal.rsi
  - type: Tag
    tags:
      - Medal
    # Corvax-Next-CrewMedals-Start
  - type: CrewMedal
  - type: ActivatableUI
    key: enum.CrewMedalUiKey.Key
    inHandsOnly: true
  - type: UserInterface
    interfaces:
      enum.CrewMedalUiKey.Key:
        type: CrewMedalBoundUserInterface
    # Corvax-Next-CrewMedals-End

- type: entity
  parent: ClothingNeckMedalBase
  id: ClothingNeckClownmedal
  name: "медаль за хоробрість" # DeltaV - Updated title so it's not just a joke medal.
  description: "Нагорода вручається членам екіпажу, які сміються в обличчя смерті, піднімають настрій своїм товаришам і ведуть себе та своїх друзів до успіху." # DeltaV - Updated description for flavour and to reflect title.
  components:
  - type: Sprite
    sprite: DeltaV/Clothing/Neck/Medals/clownmedal.rsi # DeltaV - resprite
  - type: Clothing
    sprite: DeltaV/Clothing/Neck/Medals/clownmedal.rsi # DeltaV - resprite
  - type: StealTarget
    stealGroup: ClothingNeckClownmedal
  - type: Tag
    tags:
      - Medal
    # Corvax-Next-CrewMedals-Start
  - type: CrewMedal
  - type: ActivatableUI
    key: enum.CrewMedalUiKey.Key
    inHandsOnly: true
  - type: UserInterface
    interfaces:
      enum.CrewMedalUiKey.Key:
        type: CrewMedalBoundUserInterface
    # Corvax-Next-CrewMedals-End
