#Entities found at entities/objects/specific/medical
- type: entity
  id: MedkitFilled
  suffix: Filled
  parent: Medkit
  components:
  - type: StorageFill
    contents:
      - id: Brutepack
      - id: Ointment
      - id: Gauze
      - id: PillCanisterTricordrazine
      # see https://github.com/tgstation/blob/master/code/game/objects/items/storage/firstaid.dm for example contents

- type: entity
  id: MedkitBurnFilled
  suffix: Filled
  parent: MedkitBurn
  components:
  - type: StorageFill
    contents:
      - id: Ointment
        amount: 2
      - id: PillCanisterKelotane
      - id: PillCanisterDermaline

- type: entity
  id: MedkitBruteFilled
  suffix: Filled
  parent: MedkitBrute
  components:
  - type: StorageFill
    contents:
      - id: Brutepack
      - id: Gauze
      - id: PillCanisterIron
      - id: PillCanisterCopper

- type: entity
  id: MedkitToxinFilled
  suffix: Filled
  parent: MedkitToxin
  components:
  - type: StorageFill
    contents:
      - id: SyringeIpecac
      - id: SyringeEthylredoxrazine
      - id: AntiPoisonMedipen
      - id: PillCanisterDylovene
      - id: PillCanisterCharcoal

- type: entity
  id: MedkitOxygenFilled
  suffix: Filled
  parent: MedkitO2
  components:
  - type: StorageFill
    contents:
      - id: ClothingMaskBreathMedical
      - id: EmergencyOxygenTankFilled
      - id: EmergencyMedipen
      - id: SyringeInaprovaline
      - id: PillCanisterDexalin

- type: entity
  id: MedkitRadiationFilled
  suffix: Filled
  parent: MedkitRadiation
  components:
  - type: StorageFill
    contents:
      - id: SyringePhalanximine
      - id: RadAutoInjector
      - id: PillCanisterPotassiumIodide
      - id: PillCanisterHyronalin

- type: entity
  id: MedkitAdvancedFilled
  suffix: Filled
  parent: MedkitAdvanced
  components:
  - type: StorageFill
    contents:
      - id: MedicatedSuture
      - id: RegenerativeMesh
      - id: Bloodpack
        amount: 2

- type: entity
  id: MedkitCombatFilled
  suffix: Filled
  parent: MedkitCombat
  components:
  - type: StorageFill
    contents:
      - id: MedicatedSuture
      - id: RegenerativeMesh
      - id: SyringeEphedrine
      - id: BruteAutoInjector
      - id: BurnAutoInjector
      - id: EmergencyMedipen
      - id: PainMedipen # Morphine

- type: entity
  id: StimkitFilled
  suffix: Stimkit, Filled
  parent: Medkit
  components:
  - type: StorageFill
    contents:
      - id: StimpackMini
        amount: 6

