- type: gameMap
  id: Barratry
  mapName: Баратрі
  mapPath: /Maps/barratry.yml
  minPlayers: 10
  stations:
    Barratry:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: '{0} Barratry {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: '14'
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/emergency.yml
        - type: StationJobs
          overflowJobs:
            - Passenger
          availableJobs:
            #service
            Captain: [ 1, 1 ]
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 2, 2 ]
            Botanist: [ 2, 2 ]
            Chef: [ 2, 2 ]
            Janitor: [ 2, 2 ]
            Chaplain: [ 1, 1 ]
            Li<PERSON>rian: [ 1, 1 ]
            Lawyer: [ 2, 2 ]
            ServiceWorker: [ 1, 2 ]
            #engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 3, 3 ]
            SeniorEngineer: [ 1, 1 ]
            StationEngineer: [ 4, 4 ]
            TechnicalAssistant: [ 2, 4 ]
            #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            SeniorPhysician: [ 1, 1 ]
            Chemist: [ 2, 2 ]
            MedicalDoctor: [ 4, 4 ]
            MedicalIntern: [ 2, 4 ]
            Paramedic: [ 1, 1 ]
            #science
            ResearchDirector: [ 1, 1 ]
            SeniorResearcher: [ 1, 1 ]
            Scientist: [ 4, 4 ]
            ResearchAssistant: [2, 4]
            #security
            HeadOfSecurity: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1 ]
            Warden: [ 1, 1 ]
            SeniorOfficer: [ 1, 1 ]
            Detective: [ 1, 1 ]
            SecurityOfficer: [ 5, 5 ]
            SecurityCadet: [ 2, 4 ]
            #supply
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ 3, 3 ]
            CargoTechnician: [ 2, 2 ]
            #civillian
            Passenger: [ -1, -1 ]
            Clown: [ 1, 1 ]
            Mime: [ 1, 1 ]
            Musician: [ 1, 1 ]

