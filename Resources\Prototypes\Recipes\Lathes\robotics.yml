# Base prototypes

- type: latheRecipe
  abstract: true
  id: BaseRoboticsRecipe
  category: Robotics
  completetime: 2

- type: latheRecipe
  abstract: true
  parent: BaseRoboticsRecipe
  id: BaseBorgLimbRecipe
  materials:
    Steel: 250
    Glass: 100

- type: latheRecipe
  abstract: true
  parent: BaseRoboticsRecipe
  id: BaseBorgModuleRecipe
  completetime: 3
  materials:
    Steel: 250
    Glass: 250
    Plastic: 250

- type: latheRecipe
  abstract: true
  parent: BaseBorgModuleRecipe
  id: BaseGoldBorgModuleRecipe
  materials:
    Steel: 500
    Glass: 500
    Plastic: 250
    Gold: 50

# Recipes

- type: latheRecipe
  parent: BaseRoboticsRecipe
  id: ProximitySensor
  result: ProximitySensor
  materials:
    Steel: 200
    Glass: 300

- type: latheRecipe
  parent: BaseRoboticsRecipe
  id: SciFlash
  result: SciFlash
  materials:
    Glass: 100
    Plastic: 200
    Steel: 100

- type: latheRecipe
  parent: BaseRoboticsRecipe
  id: CyborgEndoskeleton
  result: CyborgEndoskeleton
  completetime: 3
  materials:
    Steel: 1500

# limbs

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: LeftArmBorg
  result: LeftArmBorg

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: RightArmBorg
  result: RightArmBorg

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: LeftLegBorg
  result: LeftLegBorg

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: RightLegBorg
  result: RightLegBorg

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: LightHeadBorg
  result: LightHeadBorg

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: TorsoBorg
  result: TorsoBorg

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: RightArmBorgEngineer
  result: RightArmBorgEngineer

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: LeftArmBorgEngineer
  result: LeftArmBorgEngineer

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: LeftLegBorgEngineer
  result: LeftLegBorgEngineer

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: RightLegBorgEngineer
  result: RightLegBorgEngineer

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: HeadBorgEngineer
  result: HeadBorgEngineer

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: TorsoBorgEngineer
  result: TorsoBorgEngineer

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: LeftArmBorgMedical
  result: LeftArmBorgMedical

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: RightArmBorgMedical
  result: RightArmBorgMedical


- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: LeftLegBorgMedical
  result: LeftLegBorgMedical


- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: RightLegBorgMedical
  result: RightLegBorgMedical


- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: HeadBorgMedical
  result: HeadBorgMedical


- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: TorsoBorgMedical
  result: TorsoBorgMedical


- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: LeftArmBorgMining
  result: LeftArmBorgMining


- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: RightArmBorgMining
  result: RightArmBorgMining
  category: Robotics


- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: LeftLegBorgMining
  result: LeftLegBorgMining
  category: Robotics


- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: RightLegBorgMining
  result: RightLegBorgMining
  category: Robotics

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: HeadBorgMining
  result: HeadBorgMining
  category: Robotics

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: TorsoBorgMining
  result: TorsoBorgMining
  category: Robotics

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: LeftArmBorgService
  result: LeftArmBorgService

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: RightArmBorgService
  result: RightArmBorgService

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: LeftLegBorgService
  result: LeftLegBorgService

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: RightLegBorgService
  result: RightLegBorgService

- type: latheRecipe
  parent: BaseBorgLimbRecipe #Pirate fix crash
  id: HeadBorgService
  result: HeadBorgService

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: TorsoBorgService
  result: TorsoBorgService

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: LeftLegBorgJanitor
  result: LeftLegBorgJanitor

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: RightLegBorgJanitor
  result: RightLegBorgJanitor

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: HeadBorgJanitor
  result: HeadBorgJanitor

- type: latheRecipe
  parent: BaseBorgLimbRecipe
  id: TorsoBorgJanitor
  result: TorsoBorgJanitor

# Parts

- type: latheRecipe
  parent: BaseRoboticsRecipe
  id: MMI
  result: MMI
  completetime: 3
  icon:
    sprite: Objects/Specific/Robotics/mmi.rsi
    state: mmi_off
  materials:
    Steel: 1000
    Glass: 200
    Plastic: 500
    Gold: 200

- type: latheRecipe
  parent: BaseRoboticsRecipe
  id: PositronicBrain
  result: PositronicBrain
  completetime: 3
  materials:
    Steel: 500
    Plastic: 500
    Gold: 100
    Silver: 100
    Plasma: 1000

- type: latheRecipe
  parent: BaseRoboticsRecipe
  id: ReverseMMI
  result: ReverseMMI
  completetime: 3
  materials:
    Steel: 1000
    Gold: 200
    Plasma: 500
    Normality: 100 # to limit the number of these you can make, we can justify this as something to make the posibrain's soul stick or something

# Modules

- type: latheRecipe
  parent: BaseBorgModuleRecipe
  id: BorgModuleCable
  result: BorgModuleCable

- type: latheRecipe
  parent: BaseBorgModuleRecipe
  id: BorgModuleFireExtinguisher
  result: BorgModuleFireExtinguisher

- type: latheRecipe
  parent: BaseBorgModuleRecipe
  id: BorgModuleGPS
  result: BorgModuleGPS

- type: latheRecipe
  parent: BaseBorgModuleRecipe
  id: BorgModuleRadiationDetection
  result: BorgModuleRadiationDetection

- type: latheRecipe
  parent: BaseBorgModuleRecipe
  id: BorgModuleTool
  result: BorgModuleTool

# Engineering Modules

- type: latheRecipe
  parent: BaseGoldBorgModuleRecipe
  id: BorgModuleAdvancedTool
  result: BorgModuleAdvancedTool

- type: latheRecipe
  parent: BaseBorgModuleRecipe
  id: BorgModuleConstruction
  result: BorgModuleConstruction

- type: latheRecipe
  parent: BaseGoldBorgModuleRecipe
  id: BorgModuleRCD
  result: BorgModuleRCD

# Janitor Modules

- type: latheRecipe
  parent: BaseBorgModuleRecipe
  id: BorgModuleLightReplacer
  result: BorgModuleLightReplacer

- type: latheRecipe
  parent: BaseBorgModuleRecipe
  id: BorgModuleCleaning
  result: BorgModuleCleaning

- type: latheRecipe
  parent: BaseGoldBorgModuleRecipe
  id: BorgModuleAdvancedCleaning
  result: BorgModuleAdvancedCleaning

# Medical Modules

- type: latheRecipe
  parent: BaseBorgModuleRecipe
  id: BorgModuleDiagnosis
  result: BorgModuleDiagnosis

- type: latheRecipe
  parent: BaseBorgModuleRecipe
  id: BorgModuleTreatment
  result: BorgModuleTreatment

- type: latheRecipe
  parent: BaseGoldBorgModuleRecipe
  id: BorgModuleAdvancedTreatment
  result: BorgModuleAdvancedTreatment

- type: latheRecipe
  parent: BaseGoldBorgModuleRecipe
  id: BorgModuleDefibrillator
  result: BorgModuleDefibrillator

# Science Modules

- type: latheRecipe
  parent: BaseBorgModuleRecipe
  id: BorgModuleArtifact
  result: BorgModuleArtifact

- type: latheRecipe
  parent: BaseBorgModuleRecipe
  id: BorgModuleAnomaly
  result: BorgModuleAnomaly

# Service Modules

- type: latheRecipe
  parent: BaseBorgModuleRecipe
  id: BorgModuleService
  result: BorgModuleService

- type: latheRecipe
  parent: BaseBorgModuleRecipe
  id: BorgModuleGardening
  result: BorgModuleGardening

- type: latheRecipe
  parent: BaseBorgModuleRecipe
  id: BorgModuleHarvesting
  result: BorgModuleHarvesting

- type: latheRecipe
  parent: BaseBorgModuleRecipe
  id: BorgModuleClowning
  result: BorgModuleClowning

- type: latheRecipe
  parent: BaseBorgModuleRecipe
  id: BorgModuleMusique
  result: BorgModuleMusique

# Logistics/Cargo Modules

- type: latheRecipe
  parent: BaseBorgModuleRecipe
  id: BorgModulePka
  result: BorgModulePka

- type: latheRecipe
  parent: BaseGoldBorgModuleRecipe
  id: BorgModuleJetpack
  result: BorgModuleJetpack

- type: latheRecipe
  parent: BaseBorgModuleRecipe
  id: BorgModuleAppraisal
  result: BorgModuleAppraisal

- type: latheRecipe
  parent: BaseBorgModuleRecipe
  id: BorgModuleMining
  result: BorgModuleMining

# - type: latheRecipe
#  parent: BaseGoldBorgModuleRecipe
#  id: BorgModuleGrapplingGun
#  result: BorgModuleGrapplingGun
