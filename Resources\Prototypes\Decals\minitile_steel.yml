﻿- type: decal
  id: MiniTileSteel
  parent: MiniTile
  abstract: true

- type: decal
  id: MiniTileSteelBox
  parent: MiniTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: steel_box

- type: decal
  id: MiniTileSteelCornerNe
  parent: MiniTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: steel_corner_ne

- type: decal
  id: MiniTileSteelCornerSe
  parent: MiniTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: steel_corner_se

- type: decal
  id: MiniTileSteelCornerNw
  parent: MiniTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: steel_corner_nw

- type: decal
  id: MiniTileSteelCornerSw
  parent: MiniTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: steel_corner_sw

- type: decal
  id: MiniTileSteelInnerNe
  parent: MiniTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: steel_inner_ne

- type: decal
  id: MiniTileSteelInnerSe
  parent: MiniTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: steel_inner_se

- type: decal
  id: MiniTileSteelInnerNw
  parent: MiniTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: steel_inner_nw

- type: decal
  id: MiniTileSteelInnerSw
  parent: MiniTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: steel_inner_sw

- type: decal
  id: MiniTileSteelEndN
  parent: MiniTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: steel_end_n

- type: decal
  id: MiniTileSteelEndE
  parent: MiniTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: steel_end_e

- type: decal
  id: MiniTileSteelEndS
  parent: MiniTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: steel_end_s

- type: decal
  id: MiniTileSteelEndW
  parent: MiniTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: steel_end_w

- type: decal
  id: MiniTileSteelLineN
  parent: MiniTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: steel_line_n

- type: decal
  id: MiniTileSteelLineE
  parent: MiniTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: steel_line_e

- type: decal
  id: MiniTileSteelLineS
  parent: MiniTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: steel_line_s

- type: decal
  id: MiniTileSteelLineW
  parent: MiniTileSteel
  tags: ["station", "markings"]
  sprite:
    sprite: Decals/minitile.rsi
    state: steel_line_w

