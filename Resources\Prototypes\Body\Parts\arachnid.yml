# TODO: Add descriptions (many)
# TODO BODY: Part damage
- type: entity
  id: PartArachnid
  parent: [BaseItem, BasePart]
  name: "частина тіла арахніда"
  abstract: true
  components:
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 3
      - ReagentId: CopperBlood
        Quantity: 10

- type: entity
  id: TorsoArachnid
  name: "тулуб арахніда"
  parent: [PartArachnid, BaseTorso]
  components:
  - type: Sprite
    sprite: Mobs/Species/Arachnid/parts.rsi
    state: "torso_m"
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 10
      - ReagentId: CopperBlood
        Quantity: 20

- type: entity
  id: HeadArachnid
  name: "голова арахніда"
  parent: [PartArachnid, BaseHead]
  components:
  - type: Sprite
    sprite: Mobs/Species/Arachnid/parts.rsi
    state: "head_m"
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 5
      - ReagentId: CopperBlood
        Quantity: 10

- type: entity
  id: LeftArmArachnid
  name: "ліва рука арахніда"
  parent: [PartArachnid, BaseLeftArm]
  components:
  - type: Sprite
    sprite: Mobs/Species/Arachnid/parts.rsi
    state: "l_arm"

- type: entity
  id: RightArmArachnid
  name: "права рука арахніда"
  parent: [PartArachnid, BaseRightArm]
  components:
  - type: Sprite
    sprite: Mobs/Species/Arachnid/parts.rsi
    state: "r_arm"

- type: entity
  id: LeftHandArachnid
  name: "ліва долоня арахніда"
  parent: [PartArachnid, BaseLeftHand]
  components:
  - type: Sprite
    sprite: Mobs/Species/Arachnid/parts.rsi
    state: "l_hand"

- type: entity
  id: RightHandArachnid
  name: "права долоня арахніда"
  parent: [PartArachnid, BaseRightHand]
  components:
  - type: Sprite
    sprite: Mobs/Species/Arachnid/parts.rsi
    state: "r_hand"

- type: entity
  id: LeftLegArachnid
  name: "ліва нога арахніда"
  parent: [PartArachnid, BaseLeftLeg]
  components:
  - type: Sprite
    sprite: Mobs/Species/Arachnid/parts.rsi
    state: "l_leg"
  - type: MovementBodyPart

- type: entity
  id: RightLegArachnid
  name: "права нога арахніда"
  parent: [PartArachnid, BaseRightLeg]
  components:
  - type: Sprite
    sprite: Mobs/Species/Arachnid/parts.rsi
    state: "r_leg"
  - type: MovementBodyPart

- type: entity
  id: LeftFootArachnid
  name: "ліва стопа арахніда"
  parent: [PartArachnid, BaseLeftFoot]
  components:
  - type: Sprite
    sprite: Mobs/Species/Arachnid/parts.rsi
    state: "l_foot"

- type: entity
  id: RightFootArachnid
  name: "права стопа арахніда"
  parent: [PartArachnid, BaseRightFoot]
  components:
  - type: Sprite
    sprite: Mobs/Species/Arachnid/parts.rsi
    state: "r_foot"
