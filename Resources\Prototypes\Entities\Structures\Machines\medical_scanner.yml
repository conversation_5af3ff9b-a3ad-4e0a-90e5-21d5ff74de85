﻿- type: entity
  id: MedicalScanner
  parent: BaseMachinePowered
  name: "медичний сканер"
  description: "Громіздкий медичний сканер."
  components:
  - type: MedicalScanner
  - type: DeviceNetwork
    deviceNetId: Wired
    receiveFrequencyId: BasicDevice
  - type: DeviceList
  - type: DeviceLinkSink
    ports:
      - MedicalScannerReceiver
  - type: Sprite
    sprite: Structures/Machines/scanner.rsi
    snapCardinals: true
    layers:
    - state: open
      map: [machineLayer]
    - state: idle_unlit
      map: [terminalLayer]
  - type: GenericVisualizer
    visuals:
      enum.MedicalScannerVisuals.Status:
        machineLayer:
          Off: {state: "closed"}
          Open: {state: "open"}
          Red: {state: "occupied"}
          Death: {state: "occupied"}
          Green: {state: "occupied"}
          Yellow: {state: "occupied"}
        terminalLayer:
          Off: {state: "off_unlit"}
          Open: {state: "idle_unlit"}
          Red: {state: "red_unlit"}
          Death: {state: "off_unlit"}
          Green: {state: "idle_unlit"}
          Yellow: {state: "maint_unlit"}
  - type: Physics
    bodyType: Static
  - type: Construction
    graph: Machine
    node: machine
    containers:
    - machine_parts
    - machine_board
  - type: ContainerContainer
    containers:
      scanner-bodyContainer: !type:ContainerSlot
      machine_board: !type:Container
      machine_parts: !type:Container
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: StrongMetallic
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
      - !type:ChangeConstructionNodeBehavior
        node: machineFrame
      - !type:DoActsBehavior
        acts: ["Destruction"]
  - type: Machine
    board: MedicalScannerMachineCircuitboard
  - type: WiresPanel
  - type: Appearance
  - type: Climbable
  - type: ApcPowerReceiver
    powerLoad: 200 #Receives most of its power from the console
  - type: EmptyOnMachineDeconstruct
    containers:
    - scanner-bodyContainer
  - type: GuideHelp
    guides:
    - Cloning
