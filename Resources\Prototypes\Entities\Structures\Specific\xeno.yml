- type: entity
  id: XenoWardingTower
  name: "Охоронна вежа \"Ксено"
  placement:
    mode: SnapgridCenter
    snap:
    - Wall
  components:
  - type: RangedDamageSound
    soundGroups:
      Brute:
        collection:
          MeatBulletImpact
    soundTypes:
      Heat:
        collection:
          MeatLaserImpact
  - type: Transform
    anchored: true
  - type: Clickable
  - type: InteractionOutline
  - type: Sprite
    sprite: Structures/Specific/xeno_building.rsi
    layers:
      - state: wardingtower
      - state: wardingtower-unshaded
        shader: unshaded
  - type: Damageable
    damageContainer: Inorganic
    damageModifierSet: Metallic
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.5,-0.5,0.5,0.5"
        mask:
          - FullTileMask
        layer:
          - WallLayer
        density: 1000
  - type: Destructible
    thresholds:
      - trigger:
          !type:DamageTrigger
          damage: 50
        behaviors:
          - !type:DoActsBehavior
            acts: [ "Destruction" ]

- type: entity
  parent: XenoWardingTower
  id: CarpStatue
  name: "статуя коропа"
  description: "Статуя одного з відважних коропів, які привели нас туди, де ми є сьогодні. Зроблено зі справжніми зубами!"
  components:
  - type: Sprite
    sprite: Structures/Specific/carp_statue.rsi
    layers:
    - state: statue
    - state: unshaded
      shader: unshaded
