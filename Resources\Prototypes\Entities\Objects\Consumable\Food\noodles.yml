# When adding new food also add to random spawner located in Resources\Prototypes\Entities\Markers\Spawners\Random\Food_Drinks\food_meal.yml
# Base

- type: entity
  parent: FoodInjectableBase
  id: FoodNoodlesBase
  abstract: true
  description: "Ось це гарна паста!"
  components:
  - type: Item
    storedRotation: -90
  - type: Sprite
    sprite: Objects/Consumable/Food/noodles.rsi
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 25
        reagents:
        - ReagentId: Nutriment
          Quantity: 20

# Noodles

- type: entity
  name: "відварені спагетті"
  parent: FoodNoodlesBase
  id: FoodNoodlesBoiled
  description: "Для звичайної страви з локшини потрібно більше інгредієнтів."
  components:
  - type: FlavorProfile
    flavors:
      - pasta
  - type: Sprite
    state: boiled
# Tastes like pasta.

- type: entity
  name: "спагеті"
  parent: FoodNoodlesBase
  id: FoodNoodles
  description: "Спагеті з помідорами. Так само, як готував твій жорстокий батько!"
  components:
  - type: FlavorProfile
    flavors:
      - pasta
      - tomato
  - type: Sprite
    state: tomato
  - type: Tag
    tags:
    - Fruit
# Tastes like pasta, tomato.

- type: entity
  name: "копіпаста"
  parent: FoodNoodlesBase
  id: FoodNoodlesCopy
  description: "Вам, мабуть, не варто пробувати, ви завжди чуєте, як люди говорять про те, як це погано..."
  components:
  - type: FlavorProfile
    flavors:
      - copypasta
  - type: Sprite
    state: copypasta
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 25
        reagents:
        - ReagentId: Nutriment
          Quantity: 12
        - ReagentId: Vitamin
          Quantity: 8
# Tastes like pasta, bad humor.

- type: entity
  name: "спагетті з фрикадельками"
  parent: FoodNoodlesBase
  id: FoodNoodlesMeatball
  description: "Ось це - фрикаделька!"
  components:
  - type: FlavorProfile
    flavors:
      - pasta
      - meaty
  - type: Sprite
    state: meatball
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 8
        - ReagentId: Vitamin
          Quantity: 4
  - type: Tag
    tags:
    - Meat
# Tastes like pasta, meat.

- type: entity
  name: "спешеллоу"
  parent: FoodNoodlesBase
  id: FoodNoodlesSpesslaw
  description: "Улюбленець юристів."
  components:
  - type: FlavorProfile
    flavors:
      - pasta
      - meaty
  - type: Sprite
    state: spesslaw
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 8
        - ReagentId: Vitamin
          Quantity: 6
# Tastes like pasta, meat.

- type: entity
  name: "чау-мейн"
  parent: FoodNoodlesBase
  id: FoodNoodlesChowmein
  description: "Приємне поєднання локшини та смажених овочів."
  components:
  - type: FlavorProfile
    flavors:
      - pasta
      - oily
      - eggplant
      - carrot
  - type: Sprite
    state: chowmein
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 7
        - ReagentId: Vitamin
          Quantity: 6
# Tastes like pasta, tomato.

- type: entity
  name: "масляна локшина"
  parent: FoodNoodlesBase
  id: FoodNoodlesButter
  description: "Локшина, вкрита пікантним вершковим маслом. Просто і слизько, але дуже смачно."
  components:
  - type: FlavorProfile
    flavors:
      - pasta
      - butter
  - type: Sprite
    state: butter
  - type: SolutionContainerManager
    solutions:
      food:
        maxVol: 15
        reagents:
        - ReagentId: Nutriment
          Quantity: 8
        - ReagentId: Vitamin
          Quantity: 1
# Tastes like pasta, butter.
