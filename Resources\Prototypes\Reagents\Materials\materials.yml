- type: material
  id: Biomass
  stackEntity: MaterialBiomass1
  name: materials-biomass
  unit: materials-unit-piece
  icon: { sprite: /Textures/Objects/Misc/monkeycube.rsi, state: cube }
  color: "#8A9A5B"
  price: 0.1

- type: material
  id: Cardboard
  stackEntity: MaterialCardboard1
  name: materials-cardboard
  icon: { sprite: /Textures/Objects/Materials/materials.rsi, state: cardboard }
  color: "#70736c"
  price: 0.005

- type: material
  id: Cloth
  stackEntity: MaterialCloth1
  name: materials-cloth
  unit: materials-unit-roll
  icon: { sprite: /Textures/Objects/Materials/materials.rsi, state: cloth }
  color: "#e7e7de"
  price: 0.05

- type: material
  id: Durathread
  stackEntity: MaterialDurathread1
  name: materials-durathread
  # not exactly a sheet but its sprite suggests it cant be rolled like cloth
  unit: materials-unit-sheet
  icon: { sprite: /Textures/Objects/Materials/materials.rsi, state: durathread }
  color: "#8291a1"
  price: 0.15 # 1-1 mix of plastic and cloth.

- type: material
  id: Paper
  stackEntity: SheetPaper1
  name: materials-paper
  icon: { sprite: Objects/Materials/Sheets/other.rsi, state: paper }
  color: "#d9d9d9"
  price: 0.05 #same as wood

- type: material
  id: Plasma
  stackEntity: SheetPlasma1
  name: materials-plasma
  icon: { sprite: Objects/Materials/Sheets/other.rsi, state: plasma }
  color: "#7e009e"
  price: 0.2

- type: material
  id: Plastic
  stackEntity: SheetPlastic1
  name: materials-plastic
  icon: { sprite: Objects/Materials/Sheets/other.rsi, state: plastic }
  color: "#d9d9d9"
  price: 0.1

- type: material
  id: Wood
  stackEntity: MaterialWoodPlank1
  name: materials-wood
  unit: materials-unit-plank
  icon: { sprite: Objects/Materials/materials.rsi, state: wood }
  color: "#966F33"
  price: 0.05

- type: material
  id: Uranium
  stackEntity: SheetUranium1
  name: materials-uranium
  icon: { sprite: Objects/Materials/Sheets/other.rsi, state: uranium }
  color: "#32a852"
  price: 0.2

- type: material
  id: Bananium
  stackEntity: MaterialBananium1
  name: materials-bananium
  unit: materials-unit-bunch
  icon: { sprite: Objects/Materials/materials.rsi, state: bananium }
  color: "#32a852"
  price: 0.2

- type: material
  id: Meaterial # you can't take this pun from me
  name: materials-meat
  unit: materials-unit-slab
  icon: { sprite: Objects/Materials/Sheets/meaterial.rsi, state: meat }
  color: "#c53648"
  price: 0.05

- type: material
  id: WebSilk
  name: materials-web
  unit: materials-unit-web
  icon: { sprite: Objects/Materials/silk.rsi, state: icon }
  color: "#eeeeee" #eeeeeeeeeeeeeeeeeeeeeeeeeeeeeee
  price: 0 # Maybe better for it to be priceless, knowing how greedy cargo is.

- type: material
  id: Bones
  name: materials-bones
  unit: materials-unit-piece
  icon: { sprite: Objects/Materials/materials.rsi, state: bones }
  color: "#896f5e"
  price: 0

- type: material
  id: Coal
  stackEntity: Coal1
  name: materials-coal
  unit: materials-unit-piece
  icon: { sprite: Objects/Materials/ore.rsi, state: coal }
  color: "#404040"
  price: 0

- type: material
  id: Bluespace
  stackEntity: MaterialBluespace1
  name: materials-bluespace
  unit: materials-unit-piece
  icon: { sprite: Objects/Materials/materials.rsi, state: bluespace }
  color: "#53a9ff"
  price: 7.5

- type: material
  id: Normality
  stackEntity: MaterialNormality1
  name: materials-normality
  unit: materials-unit-piece
  icon: { sprite: Objects/Materials/materials.rsi, state: normality }
  color: "#53a9ff"
  price: 7.5

- type: material
  id: Gunpowder
  name: materials-gunpowder
  unit: materials-unit-piece
  icon: { sprite: Objects/Misc/reagent_fillings.rsi, state: powderpile }
  color: "#A9A9A9"
  price: 0

- type: material
  id: Diamond
  name: materials-diamond
  unit: materials-unit-piece
  icon: { sprite: Objects/Materials/materials.rsi, state: diamond }
  color: "#80ffff"
  price: 20 # big diamond gaslit us so hard diamonds actually became extremely rare
