- type: entity
  abstract: true
  parent: [BaseMachinePowered, ConstructibleMachine]
  id: BaseRecharger
  placement:
    mode: SnapgridCenter
  components:
  - type: Transform
    anchored: true
    noRot: false
  - type: Sprite
    snapCardinals: true
  - type: Appearance
  - type: Charger
    slotId: charger_slot
  - type: Anchorable
    delay: 1
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 80
      behaviors:
      - !type:<PERSON><PERSON><PERSON><PERSON>ehavior
        acts: ["Destruction"]
    - trigger:
        !type:DamageTrigger
        damage: 40
      behaviors:
      - !type:EmptyAllContainersBehaviour
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
  - type: StaticPrice
    price: 25

- type: entity
  abstract: true
  parent: BaseRecharger
  id: BaseItemRecharger
  components:
  - type: Sprite
    drawdepth: SmallObjects
    layers:
    - map: ["enum.PowerChargerVisualLayers.Base"]
      state: "empty"
    - map: ["enum.PowerChargerVisualLayers.Light"]
      state: "light-off"
      shader: "unshaded"
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.10,-0.10,0.10,0.10"
        density: 500
        mask:
        - TabletopMachineMask
        layer:
        - BulletImpassable
  - type: PowerChargerVisuals
  - type: ContainerContainer
    containers:
      charger_slot: !type:ContainerSlot
      machine_board: !type:Container
      machine_parts: !type:Container
  - type: RequireProjectileTarget

- type: entity
  parent: BaseItemRecharger
  id: PowerCellRecharger
  name: "зарядний пристрій для батарейок"
  components:
  - type: Sprite
    sprite: Structures/Power/cell_recharger.rsi
    layers:
    - map: ["enum.PowerChargerVisualLayers.Base"]
      state: "empty"
    - map: ["enum.PowerChargerVisualLayers.Light"]
      state: "light-off"
      shader: "unshaded"
    - state: open
      map: ["enum.WiresVisualLayers.MaintenancePanel"]
      visible: false
  - type: Machine
    board: CellRechargerCircuitboard
  - type: WiresPanel
  - type: GenericVisualizer
    visuals:
      enum.WiresVisuals.MaintenancePanelState:
        enum.WiresVisualLayers.MaintenancePanel:
          True: { visible: true }
          False: { visible: false }
  - type: PowerCellSlot
    cellSlotId: charger_slot
    # fitsInCharger is true i dont think this will ever affect anything negatively but it lets it function
  - type: ItemSlots
    slots:
      charger_slot:
        ejectOnInteract: true
        name: power-cell-slot-component-slot-name-default
        whitelist:
          tags:
          - PowerCell
          - PowerCellSmall

- type: entity
  parent: [ BaseItemRecharger, ConstructibleMachine ]
  id: PowerCageRecharger
  name: "зарядний пристрій для клітки"
  components:
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.25,-0.35,0.25,0.35"
        density: 190
        mask:
        - MachineMask
        layer:
        - MachineLayer
  - type: LitOnPowered
  - type: PointLight
    radius: 1.5
    color: "#03fc4e"
    energy: 0.7
  - type: Charger
    chargeRate: 50
  - type: Sprite
    sprite: Structures/Power/cage_recharger.rsi
  - type: PowerCellSlot
    cellSlotId: charger_slot
  - type: ItemSlots
    slots:
      charger_slot:
        ejectOnInteract: true
        name: Power cage
        whitelist:
          tags:
          - PowerCage
  - type: Machine
    board: PowerCageRechargerCircuitboard
  - type: StaticPrice
    price: 500

- type: entity
  parent: BaseItemRecharger
  id: WeaponCapacitorRecharger
  name: "зарядний пристрій"
  components:
  - type: Sprite
    sprite: Structures/Power/recharger.rsi
  - type: Machine
    board: WeaponCapacitorRechargerCircuitboard
  # no powercellslot since stun baton etc arent powercells
  - type: ItemSlots
    slots:
      charger_slot:
        ejectOnInteract: true
        whitelist:
          components:
          - HitscanBatteryAmmoProvider
          - ProjectileBatteryAmmoProvider
          - Stunbaton
        blacklist:  # Goobstation
          tags:
          - WizardWand
          components:
          - MaterialEnergy

- type: entity
  parent: BaseItemRecharger
  id: TurboItemRecharger
  name: "турбозарядний пристрій"
  description: "Розгінний зарядний пристрій, адаптований до глобального порту."
  components:
  - type: Sprite
    sprite: Structures/Power/turbo_recharger.rsi
  - type: Machine
    board: TurboItemRechargerCircuitboard
  - type: Charger
    chargeRate: 100
  - type: ItemSlots
    slots:
      charger_slot:
        ejectOnInteract: true
        whitelist:
          components:
          - HitscanBatteryAmmoProvider
          - ProjectileBatteryAmmoProvider
          - Stunbaton
          - PowerCell
        blacklist:
          tags:
          - PotatoBattery
          - WizardWand # Goobstation
          components:  # Goobstation
          - MaterialEnergy

- type: entity
  parent: BaseItemRecharger
  id: WallWeaponCapacitorRecharger
  name: "настінний зарядний пристрій"
  components:
  - type: Sprite
    sprite: Structures/Power/wall_recharger.rsi
    layers:
    - map: ["enum.PowerChargerVisualLayers.Base"]
      state: "empty"
    - map: ["enum.PowerChargerVisualLayers.Light"]
      state: "light-off"
      shader: "unshaded"
  - type: WallMount
  - type: Charger
    chargeRate: 25
  - type: ItemSlots
    slots:
      charger_slot:
        ejectOnInteract: true
        whitelist:
          components:
          - HitscanBatteryAmmoProvider
          - ProjectileBatteryAmmoProvider
          - Stunbaton
        blacklist:  # Goobstation
          tags:
          - WizardWand

- type: entity
  parent: BaseRecharger
  id: BorgCharger
  name: "станція підзарядки кіборгів"
  description: "Стаціонарний зарядний пристрій для різноманітних роботів та кіборгів. Напрочуд місткий."
  components:
  - type: Sprite
    sprite: Structures/Power/borg_charger.rsi
    layers:
      - state: borgcharger-u1
        map: ["base"]
      - state: borgcharger0
        map: ["enum.PowerDeviceVisualLayers.Powered"]
        shader: "unshaded"
      - state: borgcharger1
        map: ["charged"]
        shader: "unshaded"
        visible: false
      - state: borgdecon1
        map: ["enum.WiresVisualLayers.MaintenancePanel"]
        visible: false
  - type: Charger
    chargeRate: 30
    slotId: entity_storage
    whitelist:
      components:
      - BorgChassis
      - Silicon # Parkstation IPCs
      - Inventory # Goobstation - Modsuits
  - type: Construction
    containers:
    - machine_parts
    - machine_board
    - entity_storage
  - type: WiresPanel
  - type: WiresVisuals
  - type: Machine
    board: BorgChargerCircuitboard
  - type: GenericVisualizer
    visuals:
      enum.StorageVisuals.Open:
        base:
          True: { state: borgcharger-u0 }
          False: { state: borgcharger-u1 }
        enum.PowerDeviceVisualLayers.Powered:
          True: { state: borgcharger0 }
          False: { state: borgcharger2 }
        charged:
          True: { visible: false }
          False: { visible: true }
      enum.PowerDeviceVisuals.Powered:
        enum.PowerDeviceVisualLayers.Powered:
          True: { visible: true }
          False: { visible: false }
        charged:
          True: { visible: true }
          False: { visible: false }
      enum.CellVisual.Light:
        charged:
          Off: { visible: false }
          Empty: { visible: false }
          Charging:
            visible: true
            state: borgcharger3
          Charged:
            visible: true
            state: borgcharger1
  - type: EntityStorage
    capacity: 1
    whitelist:
      components:
      - BorgChassis
  - type: ContainerContainer
    containers:
      entity_storage: !type:Container
      machine_board: !type:Container
      machine_parts: !type:Container
