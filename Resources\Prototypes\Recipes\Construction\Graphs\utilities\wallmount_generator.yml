﻿- type: constructionGraph
  id: WallmountGenerator
  start: start
  graph:
    - node: start
      edges:
      - to: frame
        steps:
        - material: Steel
          amount: 5
          doAfter: 2

    - node: frame
      entity: BaseGeneratorWallmountFrame
      edges:
      - to: start
        steps:
        - tool: Welding
          doAfter: 4
        completed:
        - !type:GivePrototype
          prototype: SheetSteel1
          amount: 5
        - !type:DeleteEntity {}
      - to: parts
        steps:
        - material: Cable
          amount: 5
          doAfter: 2
        - tool: Screwing
          doAfter: 2

    - node: parts
      entity: BaseGeneratorWallmountFrame
      edges:
      - to: electronics
        steps:
        - tag: WallmountGeneratorElectronics
          store: board
          name: "wallmount generator circuit board"
          icon:
            sprite: "Objects/Misc/module.rsi"
            state: "charger_APC"
          doAfter: 1
      - to: electronicsAPU
        steps:
        - tag: WallmountGeneratorAPUElectronics
          store: board
          name: "wallmount APU circuit board"
          icon:
            sprite: "Objects/Misc/module.rsi"
            state: "charger_APC"
          doAfter: 1
      - to: frame
        completed:
        - !type:GivePrototype
          prototype: CableApcStack1
          amount: 5
        steps:
        - tool: Cutting
          doAfter: 1


    - node: electronics
      edges:
      - to: generator
        steps:
        - tool: Screwing
          doAfter: 2

    - node: electronicsAPU
      edges:
      - to: APU
        steps:
        - tool: Screwing
          doAfter: 2

    - node: generator
      entity: GeneratorWallmountBasic
      edges:
      - to: parts
        conditions:
        - !type:WirePanel {}
        - !type:ContainerNotEmpty
          container: board
        completed:
        - !type:EmptyAllContainers
          pickup: true
          emptyAtUser: true
        steps:
        - tool: Prying
          doAfter: 1

    - node: APU
      entity: GeneratorWallmountAPU
      edges:
      - to: parts
        conditions:
        - !type:WirePanel {}
        - !type:ContainerNotEmpty
          container: board
        completed:
        - !type:EmptyAllContainers
          pickup: true
          emptyAtUser: true
        steps:
        - tool: Prying
          doAfter: 1
