﻿- type: entity
  id: BaseShellShotgun
  name: "набій (.50)"
  parent: BaseCartridge
  abstract: true
  components:
  - type: Tag
    tags:
    - Cartridge
    - ShellShotgun
  - type: CartridgeAmmo
    soundEject:
      collection: ShellEject
  - type: Sprite
    sprite: Objects/Weapons/Guns/Ammunition/Casings/shotgun_shell.rsi
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
  - type: Appearance
  - type: SpentAmmoVisuals
  - type: EmitSoundOnLand
    sound:
      collection: ShellLand
      params:
        volume: -5
  - type: EmitSoundOnCollide
    sound:
      collection: ShellLand
      params:
        volume: -5

- type: entity
  id: ShellShotgunBeanbag
  name: "набій (.50 beanbag)"
  parent: BaseShellShotgun
  components:
  - type: Tag
    tags:
    - Cartridge
    - ShellShotgun
    - ShellShotgunLight
  - type: Sprite
    layers:
      - state: beanbag
        map: [ "enum.AmmoVisualLayers.Base" ]
  - type: CartridgeAmmo
    proto: PelletShotgunBeanbag
  - type: SpentAmmoVisuals
    state: "beanbag"

- type: entity
  id: ShellShotgunSlug
  name: "наб<PERSON>й (куля калібру .50)"
  parent: BaseShellShotgun
  components:
  - type: Sprite
    layers:
      - state: slug
        map: [ "enum.AmmoVisualLayers.Base" ]
  - type: CartridgeAmmo
    proto: PelletShotgunSlug
  - type: SpentAmmoVisuals
    state: "slug"

- type: entity
  id: ShellShotgunLumen
  name: "патрон (.50 Люменбласт)"
  parent: BaseShellShotgun
  components:
  - type: Tag
    tags:
    - Cartridge
    - ShellShotgun
  - type: Sprite
    layers:
      - state: lumen
        map: [ "enum.AmmoVisualLayers.Base" ]
  - type: CartridgeAmmo
    proto: PelletShotgunLumen
  - type: SpentAmmoVisuals
    state: "lumen"

- type: entity
  id: ShellShotgun
  name: "набій (.50)"
  parent: BaseShellShotgun
  components:
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
  - type: CartridgeAmmo
    proto: PelletShotgunSpread

- type: entity
  id: ShellShotgunIncendiary
  name: "гільза (.50 запальний)"
  parent: BaseShellShotgun
  components:
  - type: Sprite
    layers:
      - state: incendiary
        map: [ "enum.AmmoVisualLayers.Base" ]
  - type: CartridgeAmmo
    proto: PelletShotgunIncendiarySpread
  - type: SpentAmmoVisuals
    state: "incendiary"

- type: entity
  id: ShellShotgunPractice
  name: "набій (.50 практична)"
  parent: BaseShellShotgun
  components:
  - type: Sprite
    layers:
      - state: practice
        map: [ "enum.AmmoVisualLayers.Base" ]
  - type: CartridgeAmmo
    proto: PelletShotgunPracticeSpread
  - type: SpentAmmoVisuals
    state: "practice"

- type: entity
  id: ShellTranquilizer
  name: "набій (.50 транквілізатор)"
  parent: BaseShellShotgun
  components:
  - type: Tag
    tags:
    - Cartridge
    - ShellShotgun
    - ShellShotgunLight
  - type: Sprite
    layers:
      - state: practice
        map: [ "enum.AmmoVisualLayers.Base" ]
  - type: CartridgeAmmo
    proto: PelletShotgunTranquilizer
  - type: ChemicalAmmo
  - type: SolutionContainerManager
    solutions:
      ammo:
        reagents:
        - ReagentId: ChloralHydrate
          Quantity: 7
  - type: SolutionTransfer
    maxTransferAmount: 7
  - type: SpentAmmoVisuals
    state: "practice"

- type: entity
  id: ShellShotgunImprovised
  name: "імпровізований дробовий снаряд"
  description: "Саморобний дробовий снаряд, що стріляє болючими скляними осколками. Розліт настільки широкий, що він не міг влучити в широку сторону сараю"
  parent: BaseShellShotgun
  components:
  - type: Tag
    tags:
    - Cartridge
    - ShellShotgun
    - ShellShotgunLight
  - type: Sprite
    layers:
      - state: improvised
        map: [ "enum.AmmoVisualLayers.Base" ]
  - type: Construction
    graph: ImprovisedShotgunShellGraph
    node: shell
  - type: CartridgeAmmo
    proto: PelletShotgunImprovisedSpread
  - type: SpentAmmoVisuals
    state: "improvised"

- type: entity
  id: ShellShotgunUranium
  name: "урановий дробовий снаряд"
  parent: BaseShellShotgun
  components:
    - type: Sprite
      layers:
        - state: depleted-uranium
          map: [ "enum.AmmoVisualLayers.Base" ]
    - type: CartridgeAmmo
      proto: PelletShotgunUraniumSpread
    - type: SpentAmmoVisuals
      state: "depleted-uranium"

- type: entity
  id: ShellShotgunBirdshot
  name: "гільза (.50 дріб)"
  parent: BaseShellShotgun
  components:
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
  - type: CartridgeAmmo
    proto: PelletShotgunSpreadBirdshot

- type: entity
  id: ShellShotgun00Buckshot
  name: "гільза (.50 00-Картеч)"
  parent: BaseShellShotgun
  components:
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
  - type: CartridgeAmmo
    proto: PelletShotgunSpread00Buckshot

- type: entity
  id: ShellShotgun0000Buckshot
  name: "гільза (.50 0000-Картеч)"
  parent: BaseShellShotgun
  components:
  - type: Sprite
    layers:
      - state: base
        map: [ "enum.AmmoVisualLayers.Base" ]
  - type: CartridgeAmmo
    proto: PelletShotgunSpread0000Buckshot

- type: entity
  id: ShellShotgunFlare
  name: "снаряд (сигнальна ракета калібру .50)"
  parent: BaseShellShotgun
  components:
  - type: Tag
    tags:
    - Cartridge
    - ShellShotgun
    - ShellShotgunLight
  - type: Sprite
    layers:
      - state: flare
        map: [ "enum.AmmoVisualLayers.Base" ]
  - type: CartridgeAmmo
    proto: PelletShotgunFlare
  - type: SpentAmmoVisuals
    state: "flare"
