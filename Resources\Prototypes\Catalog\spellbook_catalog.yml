﻿# Offensive
- type: listing
  id: SpellbookFireball
  name: spellbook-fireball-name
  description: spellbook-fireball-desc
  productAction: ActionFireball
  productUpgradeId: SpellbookFireballUpgrade
  cost:
    WizCoin: 2
  categories:
  - SpellbookOffensive
  conditions:
  - !type:ListingLimitedStockCondition
    stock: 1
  - !type:BuyBeforeCondition # Goobstation
    blacklist:
    - SpellbookFullRandom
    - SpellbookSemiRandom

- type: listing
  id: SpellbookRodForm
  productUpgradeId: SpellbookRodFormUpgrade # Goobstation
  name: spellbook-polymorph-rod-name
  description: spellbook-polymorph-rod-desc
  productAction: ActionPolymorphWizardRod
  cost:
    WizCoin: 2 # Goob edit
  categories:
  - SpellbookOffensive
  conditions:
  - !type:ListingLimitedStockCondition
    stock: 1
  - !type:BuyBeforeCondition # Goobstation
    blacklist:
    - SpellbookFullRandom
    - SpellbookSemiRandom

# Defensive
- type: listing
  id: SpellbookForceWall
  productUpgradeId: SpellbookForceWallUpgrade # Goobstation
  name: spellbook-force-wall-name
  description: spellbook-force-wall-desc
  productAction: ActionForceWall
  cost:
    WizCoin: 1 # Goob edit
  categories:
  - SpellbookDefensive
  conditions: # Goobstation
  - !type:ListingLimitedStockCondition
    stock: 1
  - !type:BuyBeforeCondition # Goobstation
    blacklist:
    - SpellbookFullRandom
    - SpellbookSemiRandom

# Utility
# Goob edit
#- type: listing
#  id: SpellbookPolymorphSpider
#  name: spellbook-polymoprh-spider-name
#  description: spellbook-polymorph-spider-desc
#  productAction: ActionPolymorphWizardSpider
#  cost:
#    WizCoin: 2
#  categories:
#  - SpellbookUtility
#  conditions:
#  - !type:ListingLimitedStockCondition
#    stock: 1

- type: listing
  id: SpellbookBlink
  productUpgradeId: SpellbookBlinkUpgrade # Goobstation
  name: spellbook-blink-name
  description: spellbook-blink-desc
  productAction: ActionBlinkSpell # Goob edit
  cost:
    WizCoin: 1
  categories:
  - SpellbookUtility
  conditions:
  - !type:ListingLimitedStockCondition
    stock: 1
  - !type:BuyBeforeCondition # Goobstation
    blacklist:
    - SpellbookFullRandom
    - SpellbookSemiRandom

- type: listing
  id: SpellbookCharge
  productUpgradeId: SpellbookChargeUpgrade # Goobstation
  name: spellbook-charge-name
  description: spellbook-charge-desc
  productAction: ActionChargeSpell
  cost:
    WizCoin: 1
  categories:
  - SpellbookUtility
  conditions:
  - !type:ListingLimitedStockCondition
    stock: 1
  - !type:BuyBeforeCondition # Goobstation
    blacklist:
    - SpellbookFullRandom
    - SpellbookSemiRandom

- type: listing
  id: SpellbookJaunt
  name: spellbook-ethereal-jaunt-name
  description: spellbook-ethereal-jaunt-description
  productAction: ActionPolymorphJaunt
  productUpgradeId: SpellbookJauntUpgrade
  cost:
    WizCoin: 2
  categories:
  - SpellbookUtility
  conditions:
  - !type:ListingLimitedStockCondition
    stock: 1
  - !type:BuyBeforeCondition # Goobstation
    blacklist:
    - SpellbookFullRandom
    - SpellbookSemiRandom

#- type: listing
#  id: SpellbookMindSwap
#  productUpgradeId: SpellbookMindSwapUpgrade # Goobstation
#  name: spellbook-mind-swap-name
#  description: spellbook-mind-swap-description
#  productAction: ActionMindSwapSpell
#  cost:
#    WizCoin: 2
#  categories:
#  - SpellbookUtility
#  conditions:
#  - !type:ListingLimitedStockCondition
#    stock: 1
#  - !type:BuyBeforeCondition # Goobstation
#    blacklist:
#    - SpellbookFullRandom
#    - SpellbookSemiRandom

# Equipment
- type: listing
  id: SpellbookWandDoor
  name: spellbook-wand-polymorph-door-name
  description: spellbook-wand-polymorph-door-description
  productEntity: WeaponStaffPolymorphDoor # Goob edit
  cost:
    WizCoin: 1 # Goob edit
  categories:
  - SpellbookEquipment

# Goob edit
#- type: listing
#  id: SpellbookWandPolymorphCarp
#  name: spellbook-wand-polymorph-carp-name
#  description: spellbook-wand-polymorph-carp-description
#  productEntity: WeaponWandPolymorphCarp
#  cost:
#    WizCoin: 3
#  categories:
#  - SpellbookEquipment
#  conditions:
#  - !type:ListingLimitedStockCondition
#    stock: 1

- type: listing
  id: SpellbookWandLocker
  name: spellbook-wand-locker-name
  description: spellbook-wand-locker-description
  productEntity: WeaponWandLocker
  cost:
    WizCoin: 2 # Goob edit
  categories:
  - SpellbookEquipment

# Event
- type: listing
  id: SpellbookEventSummonGhosts
  name: spellbook-event-summon-ghosts-name
  description: spellbook-event-summon-ghosts-description
  productEvent: !type:SummonGhostsEvent # Goobstation
  icon: # Goobstation
    sprite: Mobs/Ghosts/ghost_human.rsi
    state: icon
  # productAction: ActionSummonGhosts # Goobstation
  cost:
    WizCoin: 0
  categories:
  - SpellbookEvents
  conditions:
  - !type:ListingLimitedStockCondition
    stock: 1

# Goob edit - disable events pending revork
#- type: listing
#  id: SpellbookEventSummonGuns
#  name: spellbook-event-summon-guns-name
#  description: spellbook-event-summon-guns-description
#  productAction: ActionSummonGuns
#  cost:
#    WizCoin: 2
#  categories:
#  - SpellbookEvents
#  conditions:
#  - !type:ListingLimitedStockCondition
#    stock: 1
#  disableRefund: true
#
#- type: listing
#  id: SpellbookEventSummonMagic
#  name: spellbook-event-summon-magic-name
#  description: spellbook-event-summon-magic-description
#  productAction: ActionSummonMagic
#  cost:
#    WizCoin: 2
#  categories:
#  - SpellbookEvents
#  conditions:
#  - !type:ListingLimitedStockCondition
#    stock: 1
#  disableRefund: true

# Upgrades
- type: listing
  id: SpellbookFireballUpgrade
  productUpgradeId: SpellbookFireballUpgrade
  name: spellbook-upgrade-fireball-name
  description: spellbook-upgrade-fireball-description
  icon:
    sprite: _Goobstation/Wizard/actions.rsi # Goob edit
    state: fireball # Goob edit
  cost:
    WizCoin: 2
  categories:
  - SpellbookOffensive
  conditions:
  - !type:BuyBeforeCondition
    whitelist:
      - SpellbookFireball
    blacklist: # Goobstation
    - SpellbookFullRandom
    - SpellbookSemiRandom
  # manual for now
  - !type:ListingLimitedStockCondition
    stock: 2

- type: listing
  id: SpellbookJauntUpgrade
  productUpgradeId: SpellbookJauntUpgrade
  name: spellbook-upgrade-jaunt-name
  description: spellbook-upgrade-jaunt-description
  icon:
    sprite: _Goobstation/Wizard/actions.rsi # Goob edit
    state: jaunt
  cost:
    WizCoin: 2
  categories:
  - SpellbookUtility
  conditions:
  - !type:BuyBeforeCondition
    whitelist:
    - SpellbookJaunt
    blacklist: # Goobstation
    - SpellbookFullRandom
    - SpellbookSemiRandom
  - !type:ListingLimitedStockCondition
    stock: 2
