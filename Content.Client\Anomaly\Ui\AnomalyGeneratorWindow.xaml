﻿<controls:FancyWindow xmlns="https://spacestation14.io"
                xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                Title="{Loc 'anomaly-generator-ui-title'}"

                Resizable="False">
    <BoxContainer Margin="0 0"
                  Orientation="Vertical"
                  HorizontalExpand="True"
                  VerticalExpand="True">
        <BoxContainer Margin="10 0 10 0" Orientation="Horizontal">
            <BoxContainer Orientation="Vertical" HorizontalExpand="True">
                <BoxContainer Orientation="Horizontal"
                              HorizontalExpand="True"
                              VerticalExpand="True"
                              Margin="0 0 0 0"
                              VerticalAlignment="Center">
                    <Label Text="{Loc 'anomaly-generator-fuel-display'}" StyleClasses="StatusFieldTitle" />
                    <ProgressBar Name="FuelBar"
                                 HorizontalExpand="True"
                                 MaxValue="1"
                                 MinValue="0"
                                 SetHeight="25"
                                 Margin="10 0 10 0"
                                 VerticalAlignment="Center">
                        <Label Name="FuelText"
                               Margin="4 0"
                               Text="0 %" />
                    </ProgressBar>
                </BoxContainer>
                <RichTextLabel Name="CooldownLabel" StyleClasses="StatusFieldTitle" />
                <RichTextLabel Name="ReadyLabel" StyleClasses="StatusFieldTitle" />
            </BoxContainer>
            <!--Sprite View-->
            <PanelContainer Margin="12 0 0 0"
                            StyleClasses="Inset"
                            VerticalAlignment="Center">
                <SpriteView Name="EntityView"
                            SetSize="96 96"
                            OverrideDirection="South" />
            </PanelContainer>
        </BoxContainer>
        <BoxContainer VerticalExpand="True"
                      HorizontalAlignment="Center"
                      VerticalAlignment="Center"
                      Margin="0 10">
            <Button Name="GenerateButton"
                    Text="{Loc 'anomaly-generator-generate'}"></Button>
        </BoxContainer>

         <!-- Footer -->
        <BoxContainer Orientation="Vertical">
            <PanelContainer StyleClasses="LowDivider" />
            <BoxContainer Orientation="Horizontal" Margin="10 2 5 0" VerticalAlignment="Bottom">
                <Label Text="{Loc 'anomaly-generator-flavor-left'}" StyleClasses="WindowFooterText" />
                <Label Text="{Loc 'anomaly-generator-flavor-right'}" StyleClasses="WindowFooterText"
                        HorizontalAlignment="Right" HorizontalExpand="True"  Margin="0 0 5 0" />
                <TextureRect StyleClasses="NTLogoDark" Stretch="KeepAspectCentered"
                        VerticalAlignment="Center" HorizontalAlignment="Right" SetSize="19 19"/>
            </BoxContainer>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
