- type: entity
  id: CrateSecurityArmor
  parent: CrateSecgear
  name: "ящик з бронежилетами"
  description: "Три бронежилети, що добре захищають від ударів. Для відкриття потрібен доступ служби безпеки."
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterArmorPlateCarrier # DeltaV - ClothingOuterArmorBulletproof replaced in favour of plate carrier
        amount: 3

- type: entity
  id: CrateSecurityHelmet
  parent: CrateSecgear
  name: "ящик з шоломами"
  description: "Містить три стандартних відер для захисту мозку. Для відкриття потрібен доступ служби безпеки."
  components:
  - type: StorageFill
    contents:
      - id: ClothingHeadHelmetBasic
        amount: 3

- type: entity
  id: CrateSecurityNonlethal
  parent: CrateSecgear
  name: "ящик з нелеталом"
  description: "Дизейблери. Для відкриття потрібен доступ служби безпеки."
  components:
  - type: StorageFill
    contents:
      - id: WeaponDisabler
        amount: 3
      - id: Stunbaton
        amount: 3
      - id: BoxFlashbang
      - id: Flash
        amount: 3
#      - Pepperspray
#      - GrenadeTeargas

- type: entity
  id: CrateSecurityRiot
  parent: CrateSecgear
  name: "ящик з озброєнням для подавлення бунтів"
  description: "Містить два комплекти бронежилетів, шоломи, щити та два Enforcer з гумовими набоями. Додаткові боєприпаси включені. Для відкриття потрібен доступ служби безпеки."
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterArmorARC # DeltaV - ClothingOuterArmorRiot replaced in favour of ARC suit
        amount: 2
      - id: ClothingHeadHelmetRiot
        amount: 2
      - id: WeaponShotgunEnforcerRubber
        amount: 2
      - id: BoxBeanbag
        amount: 2
      - id: RiotShield
        amount: 2
      - id: ClothingMaskGasSecurity
        amount: 2

- type: entity
  id: CrateSecurityHeavyBallisticArmor
  parent: CrateSecgear
  name: "ящик з балістичною бронею"
  description: "Містить два комплекти важкої балістичної броні, шоломів, щитів та протигазів. Потрібен доступ служби безпеки для відкриття."
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterArmorBallisticHeavy
        amount: 2
      - id: ClothingHeadHelmetHeavyBallistic
        amount: 2
      - id: RiotBulletShield
        amount: 2
      - id: ClothingMaskGasSecurity
        amount: 2

- type: entity
  id: CrateSecurityHeavyAblativeArmor
  parent: CrateSecgear
  name: "ящик з лазерно-абляційною бронею"
  description: "Містить два комплекти важкої лазерно-абляційної броні, шоломів, щитів та протигазів. Потрібен доступ служби безпеки для відкриття."
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterArmorAblativeHeavy
        amount: 2
      - id: ClothingHeadHelmetHeavyAblative
        amount: 2
      - id: RiotLaserShield
        amount: 2
      - id: ClothingMaskGasSecurity
        amount: 2

- type: entity
  id: CrateSecurityHeavySecurityArmor
  parent: CrateSecgear
  name: "ящик з важкою бронею служби безпеки"
  description: "Містить два комплекти важкої броні служби безпеки, шоломів та протигазів. Потрібен доступ служби безпеки для відкриття."
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterArmorStandardHeavy
        amount: 2
      - id: ClothingHeadHelmetHeavyStandard
        amount: 2
      - id: ClothingMaskGasSecurity
        amount: 2

- type: entity
  id: CrateSecuritySwat
  parent: CrateSecgear
  name: "ящик для валків"
  description: "Містить два комплекти всеохоплюючих спецкостюмів. Для відкриття потрібен доступ до служби безпеки."
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterArmorSwat
        amount: 2
      - id: ClothingHeadHelmetSwat
        amount: 2
      - id: ClothingMaskGasSwat
        amount: 2
      - id: ClothingHandsGlovesCombat
        amount: 2
      - id: ClothingShoesSwat
        amount: 2

- type: entity
  id: CrateSecuritySupplies
  parent: CrateSecgear
  name: "ящик з припасами безпеки"
  description: "Містить різні припаси для служби безпеки станції. Для відкриття потрібен доступ служби безпеки."
  components:
  - type: StorageFill
    contents:
      - id: BoxHandcuff
      - id: BoxSechud
      - id: ClothingBeltSecurityFilled
      - id: ClothingMaskGasSecurity
      - id: BookSecurity # replace with lawbook at some point

- type: entity
  id: CrateRestraints
  parent: CrateSecgear
  name: "ящик з наручниками та стяжками"
  description: "Містить по дві коробки з наручниками та стяжками. Для відкриття потрібен доступ служби безпеки."
  components:
  - type: StorageFill
    contents:
      - id: BoxHandcuff
        amount: 2
      - id: BoxZiptie
        amount: 2

- type: entity
  id: CrateSecurityBiosuit
  parent: CrateSecgear
  name: "ящик з біокостюмами"
  description: "Містить 2 костюми біозахисту, щоб жодна хвороба не відволікала вас від виконання ваших обов'язків. Для відкриття потрібен доступ служби безпеки."
  components:
  - type: StorageFill
    contents:
      - id: ClothingOuterBioSecurity
        amount: 2
      - id: ClothingHeadHatHoodBioSecurity
        amount: 2
      - id: ClothingMaskSterile
        amount: 2

- type: entity
  id: CrateSecurityTrackingMindshieldImplants
  name: "ящик з імплантерами"
  description: "Містить 4 імплантати MindShield та 4 імплантати для відстеження. Для відкриття потрібен доступ служби безпеки."
  parent: CrateSecgear
  components:
  - type: StorageFill
    contents:
      - id: MindShieldImplanter
        amount: 4
      - id: TrackingImplanter
        amount: 4

# Cosmetic Crates
