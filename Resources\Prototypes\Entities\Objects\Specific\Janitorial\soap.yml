- type: entity
  name: "мило"
  id: Soap
  parent: BaseItem
  description: "Дешевий шматок мила. Не пахне."
  components:
  - type: Tag
    tags:
    - Soap
  - type: Sprite
    sprite: Objects/Specific/Janitorial/soap.rsi
    layers:
    - state: soap-4
      map: ["enum.SolutionContainerLayers.Fill"]
  - type: SolutionContainerVisuals
    maxFillLevels: 4
    fillBaseName: soap-
    changeColor: false
  - type: Appearance
  - type: Item
    sprite: Objects/Specific/Janitorial/soap.rsi
    storedRotation: -90
  - type: Slippery
    paralyzeTime: 2
    launchForwardsMultiplier: 1.5
  - type: StepTrigger
    triggerGroups:
      types:
      - SlipEntity
    intersectRatio: 0.2
  - type: CollisionWake
    enabled: false
  - type: Physics
    bodyType: Dynamic
  - type: Fixtures
    fixtures:
      slips:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.3,0.4,0.3"
        layer:
        - SlipLayer
        hard: false
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.3,0.4,0.3"
        density: 10
        mask:
        - ItemMask
  - type: SolutionContainerManager
    solutions:
      soap:
        maxVol: 50
        reagents:
        - ReagentId: SoapReagent
          Quantity: 50
  - type: SolutionTransfer
    transferAmount: 10
    minTransferAmount: 1
    maxTransferAmount: 25
    canReceive: false
    canChangeTransferAmount: true
  - type: DrainableSolution
    solution: soap
  - type: DeleteOnSolutionEmpty
    solution: soap
  - type: PaintRemover
  - type: FlavorProfile
    flavors:
      - clean
  - type: Food
    solution: soap
  - type: BadFood
  - type: CleansForensics
  - type: Residue
    residueAdjective: residue-slippery
    residueColor: residue-green

- type: entity
  name: "мило"
  id: SoapNT
  parent: Soap
  description: "Шматок мила марки \"НаноТрейзен\". Тхне плазмою."
  components:
  - type: Sprite
    layers:
    - state: nt-4
      map: ["enum.SolutionContainerLayers.Fill"]
  - type: SolutionContainerVisuals
    fillBaseName: nt-
  - type: Item
    heldPrefix: nt
  - type: SolutionContainerManager
    solutions:
      soap:
        maxVol: 100
        reagents:
        - ReagentId: SoapReagent
          Quantity: 100
  - type: Residue
    residueAdjective: residue-slippery
    residueColor: residue-grey

- type: entity
  name: "мило"
  id: SoapDeluxe
  parent: Soap
  description: "Розкішний шматок мила марки Waffle Co. Пахне полуницею."
  components:
  - type: Sprite
    layers:
    - state: deluxe-4
      map: ["enum.SolutionContainerLayers.Fill"]
  - type: SolutionContainerVisuals
    fillBaseName: deluxe-
  - type: Item
    heldPrefix: deluxe
  - type: Residue
    residueAdjective: residue-slippery
    residueColor: residue-brown

- type: entity
  name: "мило"
  id: SoapSyndie
  parent: Soap
  description: "Ненадійний шматок мила. Пахне страхом."
  components:
  - type: Sprite
    layers:
    - state: syndie-4
      map: ["enum.SolutionContainerLayers.Fill"]
  - type: SolutionContainerVisuals
    fillBaseName: syndie-
  - type: Slippery
    paralyzeTime: 5
    launchForwardsMultiplier: 2.5
  - type: Item
    heldPrefix: syndie
  - type: FlavorProfile
    flavors:
      - clean
      - punishment
  - type: CleansForensics
    cleanDelay: 8.0
  - type: Residue
    residueAdjective: residue-slippery
    residueColor: residue-red

- type: entity
  name: "мило"
  id: SoapletSyndie
  categories: [ HideSpawnMenu ]
  parent: Soap
  description: "Крихітний шматочок синдикатського мила."
  components:
  - type: Sprite
    layers:
    - state: syndie-soaplet
  - type: Slippery
    paralyzeTime: 5
    launchForwardsMultiplier: 2.5
  - type: StepTrigger
    triggerGroups:
      types:
      - SlipEntity
    intersectRatio: 0.04
  - type: Item
    heldPrefix: syndie
  - type: Fixtures
    fixtures:
      slips:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.08,-0.06,0.08,0.06"
        layer:
        - SlipLayer
        hard: false
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.08,-0.06,0.08,0.06"
        density: 1
        mask:
        - ItemMask
  - type: DeleteOnTrigger
  - type: EmitSoundOnTrigger
    sound:
      path: "/Audio/Effects/Fluids/splat.ogg"
      params:
        volume: -20

- type: entity
  name: "мило"
  id: SoapHomemade
  parent: Soap
  description: "Саморобний шматок мила. Пахне... добре...."
  components:
  - type: Sprite
    layers:
    - state: gibs-4
      map: ["enum.SolutionContainerLayers.Fill"]
  - type: SolutionContainerVisuals
    fillBaseName: gibs-
  - type: Slippery
    paralyzeTime: 2
  - type: StepTrigger
    triggerGroups:
      types:
      - SlipEntity
  - type: Item
    heldPrefix: gibs
  - type: FlavorProfile
    flavors:
      - clean
      - meaty
  - type: Residue
    residueAdjective: residue-slippery
    residueColor: residue-red

- type: entity
  name: "омега-мило"
  id: SoapOmega
  parent: Soap
  description: "Найдосконаліше мило, відоме людству. Пахне блакитним простором."
  components:
  - type: Sprite
    layers:
    - state: omega-4
      map: ["enum.SolutionContainerLayers.Fill"]
  - type: SolutionContainerVisuals
    fillBaseName: omega-
  - type: Slippery
    paralyzeTime: 7
    launchForwardsMultiplier: 3
  - type: Item
    heldPrefix: omega
  - type: SolutionContainerManager
    solutions:
      soap:
        maxVol: 240 #In the Greek alphabet, Omega is the 24th letter
        reagents:
        - ReagentId: SoapReagent
          Quantity: 240
  - type: Residue
    residueAdjective: residue-slippery
    residueColor: residue-blue
